# Linux

## Performance tweaks

### Increase processing power

#### Disable mitigations

Mitigations can be turned off with the `mitigations=off` kernel parameter.  
For certainty, a lot are mentioned explicitly after `mitigations=off`.
Add the following to the `GRUB_CMDLINE_LINUX_DEFAULT` variable in `/etc/default/grub`

	mitigations=off  

Run `update_grub` afterward.  
See the [Linux administrator guide on kernel parameters](https://www.kernel.org/doc/html/latest/admin-guide/kernel-parameters.html?highlight=kernel%20parameters) for more information.

### Increase disk performance of EXT4

*WARNING*: these tweaks can lead to data loss on power failure or OS crashes.  
Make sure you have a backup of your data before applying these tweaks.
Add the following options to the options part of the mount point in the `/etc/fstab` file.  
Also these tweaks add a marginal increase in performance.

- Increase commit delay on EXT4 to ten minutes, add `commit=600`.
- Disable barriers by adding `nobarrier` or `barrier=0`.
- Use a `relatime` to only write the access time once a day.
- Use `noatime` and `nodiratime` to disable writing the access time at all.

Enable fast_commit

```bash
tune2fs -O fast_commit [EXT4_MOUNT_POINT]
```

### Check EXT4 partition

To get a progress indicator, use `-C 0` parameter

## Tops

View top 20 apps consuming CPU time. Output of ps sorted on accumulated cpu time:

```bash
ps -Ao user,pid,pcpu,time,command --sort=-time | head -n 20
```
## Errors

### Running /scripts/local-block

Do you see lots of these during boot?:

	Begin: Running /scripts/local-block … done
	Begin: Running /scripts/local-block … done
	Begin: Running /scripts/local-block … done
	Begin: Running /scripts/local-block … done
	Begin: Running /scripts/local-block … done

This is caused by a swap partition that cannot not found.  
Get the block device ID of the swap partition:

```bash
blkid | grep swap | cut -d "\\"" -f2
```

And update the `/etc/initramfs-tools/conf.d/resume file` with the new swap block device ID.  
Run the following command to update the ramdisk with new resume partition block device ID:

```bash
update-initramfs -u
```
Above information was found on [Notes on Linux blog](https://mike632t.wordpress.com/2021/03/21/error-message-running-scripts-local-block-on-boot/).

### update-grub

If `update-grub` gives you a lot of `Couldn't find physical volume '(null)'`, you could try running `grub-mkdevicemap -n`

## Zswap

View all zswap parameters:

```bash	
grep -R . /sys/module/zswap/parameters
```

View zswap statistics:

```bash
grep -R . /sys/kernel/debug/zswap
```

Calculate the compression ratio:

```bash
perl -E "say $(cat /sys/kernel/debug/zswap/stored_pages) * 4096 / $(cat /sys/kernel/debug/zswap/pool_total_size)"
```
## Kernel tracing

Echo 1 or 0 to '/proc/sys/kernel/ftrace_enabled' to turn kernel tracing on
or off respectively. 
After that use 'perf record' and 'perf report' to show what functions are
taking up CPU time. Press '+' tonfold the call graph.
To get call graphs, add the '-g' parameter.

'''bash
echo 1 > /proc/sys/kernel/ftrace_enabled

perf record --all-cpus --verbose -g
'''

Press CTRL-C after a while, then use:

'''bash
perf report --all-cpus --verbose -g graph
'''

To show which functions take the most time.

## Magic SysReq

See <https://docs.kernel.org/admin-guide/sysrq.html>

## SMART info

Use `-x` instead of `-a` to get a lot more information. Add `-l farm` to het
extra information fron Seagate drives thet supoort FARM.

'''bash
smartctl -x /dev/sda
smartctl -x -l farm /dev/sda
'''

# systemd

## Boot logs

For an overview of available boot logs:

```bash
journalctl --list-boots
```

To view the boot log of the previous boot:

```bash
journalctl --boot -1
```

To view the boot log of any boot, use the hexadecimal boot ID that can be found with the `--list-boots` option:

```bash
journalctl --boot [BOOT_ID]
```

Problems with starting or stopping services? 
Alwys check `daemon.log`, which may contain information on why a service
cannot start. 
You can enable `systemd` debuging, but that only shows message regarding
`systemd` and not the service you want to start. Try running the service from
the console as the right user and maybe receive nmore information. 
Still want to enable `systemd` loggin? Try this:

```bash
SYSTEMD_LOG_LEVEL=debug systemctl restart [SERVICE]
```

## Reset cpufreq statistics

To reset the statistics (after a GOVERNOR change for example) do:

```bash
echo 1 | sudo tee /sys/devices/system/cpu/cpu*/cpufreq/stats/reset
```

# Networking

Get the supported max MTU value:

```bash
ip -d link list
```

Manualla setting the MTU of an adapter:

```bash
ip link set dev <interface> mtu <mtu>
```

Setting the MTU on bootup can be done by adding a `mtu <MTU>` line to the
right interface in the `/etc/network/interfaces` file.
