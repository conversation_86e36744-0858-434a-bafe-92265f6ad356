# Set system-wide setting 

### Statuses

These are the four statuses that can be set:

	- offline
	- readonly
	- nomail
	= loaded

The setting is actually done by creating above files at the root and
optionally include content with information for visitors.
Only `offline` and `readonly` support an addition comment. The other
statuses just ignore the extra comment.

Mark <PERSON>flock offline on all servers:

	maenaction fork verbose all offline:A database has crashed, will take some time to get it synchronized again
	maenaction fork verbose all readonly:Database is synchronized, this can only by done safely when <PERSON><PERSON>lock is read-only
	maenaction fork verbose all undo offline

The `nomail` status makes it impossible to send ticket reponses and new
users cannot register because the validation mail cannot sent.
The 'loaded` status temporarily (until is is undone) activates special
measures to try to get the load down. Currently that means denying any
(recognized) bot, feed them empty pages with http code 429
