# Networking

## Adding and removing IP-addresses

The following replacements are used:

`<INTERFACE>`
: The name of the network interface, e.g. `eth0`.

`<IP-ADDRESS>`
: The IP-address, e.g. `***********` or `2001:1890:110e:1111::a245`.

`<NETMASK>`
: The netmask, to indicate what part identifies the network, for example `********`.

`<PREFIX_LENGTH>`
: The prefix length, to indicate what part identifies the network, for example `16` combined with IP-address *********, for the same network part as in explanation of the netmask.

## IP Addresses

Show details of all interfaces, for either IPv4 `-4`, IPv6 `-6` or both by leaving out the `-4` or `-6` altogether.

```bash
ip -d -[4|6] addr show
```
Add or remove an IP-address:

```bash
ip -[4|6] addr [add|del] <IP-ADDRESS>/<NETMASK|PREFIX_LENGTH> dev <INTERFACE>

ifconfig <INTERFACE> [inet|inet6] [add|del] <IP-ADDRESS>/<PREFIX_LENGTH>
```

The following example adds the IPv6 `2001:1890:110e:1111::a245` address to interface `eth0`. It is not necessary to specify `-6`, as the IP-address is IPv6.

```bash
ip addr add 2001:1890:110e:1111::a245/64 dev eth0
```

## Gateways

Add or delete a default gateway with `IP-ADDRESS`, reachable via `INTERFACE`:

```bash
ip route [add|del] default via <IP-ADDRESS> dev <INTERFACE>
```

## Routes

View the routing table.
Using `ip` on Linux:

```bash
ip -4 route
ip -6 route
```
Or using `netstat` on macOS for example. The `-n` option prevents the command from resolving IP-addresses to hostnames:

```bash
netstat -rn
```
## Neighbor Discovery

List the neighbor cache, permanent and noarp neighbor entries consecutively, all of them sorted for easy reading.

```bash
ip neigh | sort
ip neigh show nud permanent | sort
ip neigh show nud noarp | sort
```
## Adapter settings

Set the MTU to 9000 for interface `eth0`:

```bash
ip link set dev eth0 mtu 9000

ifconfig enp0s3 mtu 9000 up
```

## Test network speed

Run `iperf3` on client and server to determine (max) network speed.
Run the server:

```bash
iperf3 -s
```

Run the client:

```bash
iperf3 -c [SERVER]
```
