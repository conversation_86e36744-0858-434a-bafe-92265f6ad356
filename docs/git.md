# GIT

### Merge two repositories

This operation should combine the repositories without losing history:

```bash
#!/bin/sh
set -e -v
git clone https://github.com/destination
git clone https://github.com/to_be_moved
cd to_be_moved
git filter-repo --to-subdirectory-filter to_be_moved
cd destination
git remote add to_be_moved ../to_be_moved
git fetch to_be_moved --no-tags
git merge --allow-unrelated-histories to_be_moved/master
git remote remove to_be_moved
```

### Verbose mode

Run ssh in verbose mode for debugging purposses

```bash
GIT_SSH_COMMAND="ssh -vvv" git <command>
```

### Unstaging

Added, moved or removed something you want to revert and have not ccommitted? Use the following:

```bash
git restore --staged FILE
```

### Finding non merged commits

To check what commits from feature-branch or stash has not yet been merged
into master, use this:

```bash
git log -p [feature-branch|stash] ^master --no-merges
```

### Garbage collection

To try to reduce size of the repository, run garbage collection:

```bash
git gc --aggressive
```
