# Synology Disk Station

### Unsupported HDDs & SSDs

Add your SATA or SAS HDDs and SSDs plus SATA and NVMe M.2 drives to your Synology's compatible drive databases, including your Synology M.2 PCIe card and Expansion Unit databases.

Source: <https://github.com/007revad/Synology_HDD_db>

If has been discontinued, use and old version which might still hang around in our Downloads folder archive, named like `Synology_HDD_db-3.4.86.tar.gz`


## Enable de-duplication

Enable data deduplication with non-Synology SSDs and unsupported NAS models.

Download and run the script provided on GitHub:

Source: <https://github.com/007revad/Synology_enable_Deduplication>

If it has been discontinued, use an old version which might still hang around in our Downloads folder archive, named like `Synology_enable_Deduplication-1.3.25.tar.gz`.

## Allow NFS 4.2

Modify the script at `/usr/syno/lib/systemd/scripts/nfsd.sh` to allow
`-V 4.2` and make sure no negative `-N 4.2` is added to invocation of
nfsd.  
Then, of course, `systemctl restart nfs-server`

## Smart status

To view Smart status of the HDDs, use the smartmontools `smartctl` tool and add
`-d sat`:

```bash
smartctl -a -d sat /dev/sata{x}
```
