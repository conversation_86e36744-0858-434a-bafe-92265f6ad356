# Mac and macOS specific

### Remove unwanted files.

Remove all those `._*` files, macOS even has a specific command for it:

	dot_clean .

Remove `.DS_Store` files:

	find . -name '.DS_Store' -type f -delete

## Hidden files

Want to see all hidden files in a Finder window or file popup?
Press COMMAND + SHIFT +.

## Homebrew

Upgrade all outdated packages:

	brew upgrade --cask --greedy

## Red chromium

When chromium browsers glitch and show only red, it has to eo with
hardware acceleration. Read the following link for nor information
and resolution (in part).

<https://github.com/dortania/OpenCore-Legacy-Patcher/issues/1145#issuecomment-2282958031>

## Miscellaneous

When no alert sounds can be heard but the screen flashes instead, kill
`coreaudiod`:

	sudo killall coreaudiod

## Startup

For the boot menu, press and hold the `Option` or `Alt` key. For the recovery mode, press and hold `Command + R` or `Windows + R`.
- Boot menu: `Option` or `Alt`  
- Recovery mode: `Command + R` or `Windows + R`

If you want to force the boot menu to show on every boot, run the following command
(works only on non Silicon Macs):

```bash
sudo nvram manufacturing-enter-picker=true
```

Boot menu: `Option` or `Alt`  
Recovery mode: `Command + R` or `Windows + R`

## Immutible flag

To make a file immutable in macOS, you would use:
```bash
chflags uchg filename  # User immutable flag
# or
sudo chflags schg filename  # System immutable flag
```
To remove the protection:
```bash
chflags nouchg filename  # Remove user immutable flag
# or
sudo chflags noschg filename  # Remove system immutable flag
```
You can verify the flags with:
```bash
ls -lO filename
```

## Writing USB boot disks

Creating a boot disk using an ISO file, can be done in the same way as in
Linux. For example, writing debian cinnamon boot iso to disk 20, do the
following:

```bash
sudo diskutil unmountDisk /dev/disk20
sudo dd if=debian-live-12.9.0-amd64-cinnamon.iso of=/dev/rdisk20 status=progress bs=1048576
```

The `diskutil unmountDisk` makes sure nobody is using the disk anymore.

# Printers

### Enable or disable CUPS web interface

CUPS is the Common Unix Printing System.
On macOS, enter the following commands in the terminal to enable or disable CUPS web interface:

	cupsctl WebInterface=yes # Enable  cups web interface
	cupsctl WebInterface=no  # Disable cups web interface

### Change printer settings

After enabling the CUPS web interface, open a web browser and go to `http://localhost:631/`.  
Click the `Printers` tab and select the printer to configure.

Enable or disable this and that.
