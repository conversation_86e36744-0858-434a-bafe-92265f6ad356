# APT

## Search for a package

```bash
apt-cache search <keyword>
```

## Purge 'rc' residual packages

```bash
sudo apt-get remove --purge $(dpkg -l | awk '/^rc/{print $2}')
```
Or purge on all hosts:

```bash
maenaction fork verbose all execute apt-get remove -y --purge \$\(dpkg -l \| awk \'/^rc/{print \$2}\'\)
```

## List non install-installed (ii) packages

```bash
maenaction fork verbose all execute  dpkg -l \| grep \"^\\w\\w \" \| grep -v ii
```

First letter → desired package state ("selection state"):

	u ... unknown
	i ... install
	r ... remove/deinstall
	p ... purge (remove including config files)
	h ... hold

Second letter → current package state:

	n ... not-installed
	i ... installed
	c ... config-files (only the config files are installed)
	U ... unpacked
	F ... half-configured (configuration failed for some reason)
	h ... half-installed (installation failed for some reason)
	W ... triggers-awaited (package is waiting for a trigger from another package)
	t ... triggers-pending (package has been triggered)

## Hold a package

Sometimes a newer version of a package is released, but is broken or changes too much to make the upgrade time consuming.  
You can hold a package to prevent it from being upgraded.

```bash
PACKAGE=linux-image-6.12.9+bpo-amd64
apt-mark hold $PACKAGE
```

## Allow removal of essential packages

Sometimes, essential packages are superseeded by another solution, like
initscripts by systemd. Even when systemd is responsible for the boot
process, apt still complains initscripts are essential. 
Allow them to be deleted by using the `--allow-remove-essential` parameter:

```bash
apt remove --allow-remove-essential sysvinit initscripts
```
