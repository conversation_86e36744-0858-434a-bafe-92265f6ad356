# Disk performance

`BLOCK_DEVICE` is used as a placeholder for the block device containing the ext4 file system, for example `/dev/sdc2` or `/dev/md2`

### Convert 512 bytes per sector to 4Kn

Convert Western Digital 512e to 4Kn using Hugo.  
Search for the file: `hugo-7.4.5.tar`

```bash
hugo format \
	--danger-zone \
	--fastformat \
	--simple-progress \
	-b 4096 \
	-g BLOCK_DEVICE
```

The `hugo` application is difficult to get a hold of. OpenSeaChest has the
tool `openSeaChest_Format` to accomplish the same thing:

```bash
openSeaChest_Format -d /dev/sd{x} --setSectorSize 4096 --poll
```

To build openSeaChest tools, clone the repository and follow the BUILD.md
instructions:

```bash
git clone https://github.com/Seagate/openSeaChest
```

For a guide if above command is not clear enough, see <https://www.frederickding.com/posts/2022/12/reformatting-wd-red-pro-20tb-wd201kfgx-from-512e-to-4kn-sector-size-0410455/>.

### Disable ext4 journal

After the journal is disabled, it can be re-enabled with `tune2fs -j BLOCK_DEVICE`.  
The `tune2fs` command in the next section can also be run to set the default ext4 features in addition to (re-)adding a journal.

```bash
# Disable journal:
tune2fs -O ^has_journal BLOCK_DEVICE
# Enable journal:
tune2fs -j -O has_journal BLOCK_DEVICE
```

### Maximum mount count and interval

Proper maximum mount count is about 20—30, and the interval should be 6—12 months.  
To set the maximum mount count to 20 and the interval to 6 months:

```bash
tune2fs -c 20 -i 6m BLOCK_DEVICE
```

### Converting ext3 to ext4

```bash
tune2fs -O fast_commit,extents,uninit_bg,dir_index,has_journal BLOCK_DEVICE
```

### Default ext4 enabled features

These are all default features for new installations of ext4.  
Use them to upgrade old ext4 file systems to the latest features.  

```bash
tune2fs -O \
64bit,\
dir_index,\
dir_nlink,\
extent,\
extra_isize,\
fast_commit,\
filetype,\
flex_bg,\
has_journal,\
huge_file,\
large_dir,\
large_file,\
metadata_csum,\
sparse_super \
BLOCK_DEVICE
```

### Nice ext4 mount options

These are the recommended mount options for ext4 file systems:

- acl
- data=writeback
- delalloc **(does not work with data=journal)**
- errors=remount-ro
- journal_async_commit
- journal_checksum
- max_batch_time=50000
- relatime
- user_xattr

Add them to your fstab file, for example:

```
UUID=UUID /mnt ext4 acl,data=writeback,delalloc,errors=remount-ro,journal_async_commit,journal_checksum,max_batch_time=50000,relatime,user_xattr 0 2
```

Using journal writeback can increase the risk of data loss in case of a power failure.  
If you use it on your root partition, make sure to add it to the default mount options using tune2fs, because the root partition is mounted before the fstab file is read.

```bash
# Enable:
tune2fs -o journal_data_writeback BLOCK_DEVICE
# Disable:
tune2fs -o ^journal_data_writeback BLOCK_DEVICE
```
### Nice ext4 mount options when creating file-system

These are the recommended mount options for ext4 file systems when creating them:

- bigalloc
- ext_attr
- inline_data
- resize_inode

```bash
mkfs.ext4 -b 4096 -E bigalloc,ext_attr,inline_data,resize_inode BLOCK_DEVICE
```
