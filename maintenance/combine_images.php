#!/usr/bin/php
<?php

define('CURRENTSTAMP',time());
define('ERASE_LINE',!isset($_SERVER['CRON']) ? "\x1B[K" : null);
define('PREVIOUS_LINE',!isset($_SERVER['CRON']) ? "\x1B[F" : null);
define('DEBUG',!isset($_SERVER['CRON']));
define('DRYRUN',true);

# NOTE: these are mostly PNG images which are not immediately optimized, but optimized once in a while by a cron job

require_once '/home/<USER>/public_html/_exit_if_readonly.inc';
require_once '/home/<USER>/public_html/_db.inc';
require_once '/home/<USER>/public_html/_error.inc';
require_once '/home/<USER>/public_html/_album.inc';
require_once '/home/<USER>/public_html/_runonce.inc';

if (already_running()) {
	exit;
}

ob_implicit_flush(true);
$rc = main();
exit(show_messages_cli() || !$rc ? 1 : 0);

function main() {
	combine_uploadimages();
#	combine_albumimages();
#	combine_userimages();
}
function combine_albumimages() {
	$images = db_simple_hash('albumimagedatameta','
		SELECT GROUP_CONCAT(DATAID),GROUP_CONCAT(STOREID)
		FROM albumimagedatameta
		GROUP BY CRC,LEN
		HAVING COUNT(DISTINCT DATAID)>1
		ORDER BY DATAID', // THMBID?
		DB_FORCE_SERVER,
		'dbbm'
	);
	if ($images === false) return false;
	$combined = 0;
	foreach ($images as $dataidstr => $storeidstr) {
		if (DEBUG) {
		 	echo ERASE_LINE,"processing $dataidstr\n",PREVIOUS_LINE;
		}
		$dataids = explode(',',$dataidstr);
		$storeids = explode(',',$storeidstr);
		$sames = array();
		foreach ($dataids as $ndx => $id) {
			$storeid = $storeids[$ndx];
			$data = dbdata_single('albums',$storeid,'SELECT SQL_NO_CACHE DATA FROM albums.u'.$storeid.' WHERE DATAID='.$id,DB_USE_MASTER);
			if ($data === false) return false;
			$sames[$data][$id] = $storeid;
		}
		foreach ($sames as $infos) {
			if (count($infos) <= 1) continue;
			ksort($infos);
			list($keep,$keepstoreid) = keyval($infos);
			unset($infos[$keep]);
			$idstr = implodekeys(',',$infos);
			if (DEBUG) {
				echo ERASE_LINE,"albumimage:$idstr => $keep\n";
			}
			if (!db_update('albumelement','
				UPDATE albumelement SET DATAID='.$keep.'
				WHERE DATAID IN ('.$idstr.')')
			||	!db_update('albumelement','
				UPDATE albumelement SET THMBID='.$keep.'
				WHERE THMBID IN ('.$idstr.')')
			||	!db_update('albumelement_tobedeleted','
				UPDATE albumelement_tobedeleted SET DATAID='.$keep.'
				WHERE DATAID IN ('.$idstr.')')
			||	!db_update('albumelement_tobedeleted','
				UPDATE albumelement_tobedeleted SET THMBID='.$keep.'
				WHERE THMBID IN ('.$idstr.')')
			||	!db_delete('albumimagedatameta','
				DELETE FROM albumimagedatameta
				WHERE DATAID IN ('.$idstr.')')
			) {
				return false;
			}
			foreach ($infos as $id => $storeid) {
				flush_albumelement($id);
				flush_albumelement($storeid);
				if (!db_delete('albums','DELETE FROM albums.u'.$storeid.' WHERE DATAID='.$id)) {
					return false;
				}
			}
			++$combined;
		}
	}
	if (DEBUG) {
		echo ERASE_LINE,"albumimages: done, $combined\n";
	}
	return true;
}
function combine_uploadimages() {
	$images = db_simpler_array('uploadimagemeta','
		SELECT GROUP_CONCAT(DISTINCT UPIMGID)
		FROM uploadimagemeta
		GROUP BY CRC,DATASIZE
		HAVING COUNT(DISTINCT UPIMGID)>1
		ORDER BY UPIMGID'
	);
	if ($images === false) return false;
	$done = null;
	$combined = 0;
	foreach ($images as $idstr) {
		if (DEBUG) {
		 	echo ERASE_LINE,'uploadimages: processing '.$idstr."\n",PREVIOUS_LINE;
		}
		if (isset($done[$idstr])) continue;
		$done[$idstr] = true;
		$ids = explode(',',$idstr);
		$alldatas = $alldims = $sizs = null;
		foreach ($ids as $id) {
			$dims = db_simple_hash('uploadimagemeta','SELECT SIZE,WIDTH*HEIGHT FROM uploadimagemeta WHERE UPIMGID='.$id);
			if ($dims === false) return false;
			$sizs[$id] = db_single('uploadimagemeta','SELECT SUM(DATASIZE) FROM uploadimagemeta WHERE UPIMGID='.$id);
			if ($sizs[$id] === false) return false;
			$tmp = db_simple_hash('uploadimage_data','
				SELECT SQL_NO_CACHE SIZE,DATA
				FROM data_db.uploadimage_data
				WHERE UPIMGID='.$id
			);
			if ($tmp === false) return false;
			foreach ($tmp as $size => $data) {
				$alldatas[$size][$id] = $data;
			}
			$alldims[$id] = $dims;
		}
		if (!$alldatas) {
			error_log('uploadimage: no datas for '.$idstr,0);
			continue;
		}
		$supersame = 0;
		$sames = array();
		foreach ($alldatas as $size => $datas) {
			$same = 0;
			$prevdata = null;
			foreach ($datas as $id => $data) {
				if (isset($allcnts[$id]))
					++$allcnts[$id];
				else	$allcnts[$id] = 1;

				if (!$prevdata || $prevdata == $data) {
					if (isset($sames[$size]))
						++$sames[$size];
					else	$sames[$size] = 1;
					++$same;
				}
				$prevdata = $data;
			}
			if ($same == count($datas)) ++$supersame;
		}
		if (!$supersame) continue;
		$keep = false;
		if ($supersame == count($alldatas)) {
			$keep = array_shift($ids);
		} else {
			$maxdim = 0;
			$maxcnt = 0;
			$smallest = 0xFFFFFFFF;
			$bestid = 0;
			foreach ($ids as $id) {
				$curcnt = $allcnts[$id];
				$curdim = $alldims[$id];
				if ($curcnt >= $maxcnt
				&&	$curdim > $maxdim
				&&	(	$curcnt > $maxcnt
					||	$curdim > $maxdim
					||	$sizs[$id] < $smallest
					)
				) {
					$maxdim = $curdim;
					$maxcnt = $curcnt;
					$bestid = $id;
					$smallest = $sizs[$id];
				}
			}
			$rmids = array();
			foreach ($ids as $id) {
				if ($id != $bestid) {
					$rmids[] = $id;
				}
			}
			$ids = $rmids;
			$keep = $bestid;
		}
		if (!$keep) continue;
		$idstr = implode(',',$ids);
		if (DEBUG) {
			echo 'uploadimage:',$idstr,' => ',$keep,"\n";
		}
		if (!db_update('uploadimage_link','
			UPDATE uploadimage_link SET UPIMGID='.$keep.'
			WHERE UPIMGID IN ('.$idstr.')')
		||	!db_update('uploadimage_link_log','
			UPDATE uploadimage_link_log SET UPIMGID='.$keep.'
			WHERE UPIMGID IN ('.$idstr.')')
		||	!db_delete('uploadimagemeta','
			DELETE FROM uploadimagemeta
			WHERE UPIMGID IN ('.$idstr.')')
		||	!db_delete('uploadimage_generated','
			DELETE FROM uploadimage_generated
			WHERE UPIMGID IN ('.$idstr.')')
		||	!db_delete('uploadimage_generated_log','
			DELETE FROM uploadimage_generated_log
			WHERE UPIMGID IN ('.$idstr.')')
		||	!db_delete('uploadimage_crop','
			DELETE FROM uploadimage_crop
			WHERE UPIMGID IN ('.$idstr.')')
		||	!db_delete('uploadimage_crop_log','
			DELETE FROM uploadimage_crop_log
			WHERE UPIMGID IN ('.$idstr.')')
		||	!db_delete('uploadimage','
			DELETE FROM uploadimage
			WHERE UPIMGID IN ('.$idstr.')')
		) {
			return false;
		}
		foreach ($ids as $id) {
			if (!db_delete('uploadimage_data','
				DELETE FROM data_db.uploadimage_data
				WHERE UPIMGID='.$id)
			) {
				return false;
			}
		}
		++$combined;
	}
	if (DEBUG) {
		echo ERASE_LINE,"uploadimages: done, $combined\n";
	}
	return true;
}
function combine_userimages() {
	$images = db_simpler_array('userimagemeta','
		SELECT GROUP_CONCAT(DISTINCT DATAID)
		FROM userimagedatameta
		GROUP BY CRC,LEN
		HAVING COUNT(DISTINCT DATAID)>1
		ORDER BY DATAID'
	);
	if ($images === false) return false;
	$done = null;
	$combined = 0;
	foreach ($images as $idstr) {
		if (DEBUG) {
		 	echo ERASE_LINE,"processing $idstr\n",PREVIOUS_LINE;
		}
		$ids = explode(',',$idstr);
		$sames = array();
		foreach ($ids as $ndx => $id) {
			$data = db_single('userimagedata', 'SELECT SQL_NO_CACHE DATA FROM data_db.userimagedata WHERE DATAID='.$id, DB_USE_MASTER);
			if ($data === false) {
				return false;
			}
			$sames[$data][] = $id;
		}
		foreach ($sames as $infos) {
			if (count($infos) <= 1) continue;
			sort($infos);
			$keep = array_shift($infos);
			$idstr = implode(',',$infos);
			if (DEBUG) {
				echo ERASE_LINE,"userimage: $idstr => $keep\n";
			}
			if (!db_update('userimage','
				UPDATE userimage SET DATAID='.$keep.'
				WHERE DATAID IN ('.$idstr.')')
			||	!db_delete('userimagedatameta','
				DELETE FROM userimagedatameta
				WHERE DATAID IN ('.$idstr.')')
			) {
				return false;
			}
			foreach ($infos as $id) {
				if (!db_delete('userimagedata','DELETE FROM data_db.userimagedata WHERE DATAID='.$id)) {
					return false;
				}
			}
			++$combined;
		}
	}
	if (DEBUG) {
		echo ERASE_LINE,"userimages: done, $combined\n";
	}
	return true;
}
