#!/usr/bin/php
<?php

define('CURRENTSTAMP',time());
define('DEBUG',!isset($_SERVER['CRON']));
#define('DRYRUN',true);

require_once '/home/<USER>/public_html/_exit_if_readonly.inc';
require_once '/home/<USER>/public_html/_db.inc';
require_once '/home/<USER>/public_html/_require.inc';
require_once '/home/<USER>/public_html/_notify.inc';

ob_implicit_flush(true);
$rc = main();
exit(show_messages_cli() || !$rc ? 1 : 0);

function main() {
	$galleries = db_rowuse_hash(['gallery','gallerynotifydone'],'
		SELECT GALLERYID,PSTAMP,PARTYID
		FROM gallery
		LEFT JOIN gallerynotifydone USING (GALLERYID)
		WHERE GALLERYID >= 5543
		  AND VISIBLE = "yes"
		  AND PSTAMP < '.(CURRENTSTAMP - 30*60).'
		  AND ISNULL(gallerynotifydone.GALLERYID)',
		DB_NON_ASSOC
	);
	if (!$galleries) {
		return $galleries !== false;
	}
	$donelist = [];
	$rc = true;
	foreach ($galleries as $galleryid => $info) {
		list(,$pstamp,$partyid) = $info;
		if (!$partyid) {
			$donelist[] = $galleryid;
			continue;
		}
		$goers = db_simpler_array('going','
			SELECT USERID
			FROM going
			WHERE PARTYID='.$partyid
		);
		if ($goers === false) {
			$rc = false;
			break;
		}
		if (!$goers) {
			$donelist[] = $galleryid;
			continue;
		}
		foreach ($goers as $userid) {
			if (!notify_register_have(NOTIFY_GALLERY,$pstamp,$userid)) {
				$rc = false;
				break;
			}
		}
		$donelist[] = $galleryid;
	}
	if ($donelist) {
		db_insert('gallerynotifydone','INSERT INTO gallerynotifydone (GALLERYID) VALUES ('.implode('),(',$donelist).')');
	} 
	return $rc;
}
