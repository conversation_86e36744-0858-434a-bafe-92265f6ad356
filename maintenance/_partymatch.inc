<?php

function check_party(
	array	&$upds,
	string	$type,
	string	$title,
	?int 	$stamp		= null,
	?int	$stamp_tzi	= null,
	?string	$location 	= null,
	?string	$city 		= null,
): array|false|null {
	$debug = defined('DEBUG') && DEBUG && !isset($GLOBALS['dont_debug_partymatch']);
#	$debug = false.;
/*	if ($debug) {
		print_rr([
			'type'		=> $type,
			'title'		=> $title,
			'stamp'		=> $stamp,
			'stamp_tzi'	=> $stamp_tzi,
			'location'	=> $location,
			'city'		=> $city,
		]);
	}*/

	if ($type === 'ticketscript') {
		# decode chars before converting utf8 to wind1252, really really bad stream. e.g. <city>K&#195;&#182;ln</city>
		$title = str_replace('&#226;&#128;&#153;', "'", $title);
		if ($location) {
			$location = str_replace('&#226;&#128;&#153;', "'", $location);
		}
		if ($city) {
			$city = str_replace('&#226;&#128;&#153;', "'", $city);
		}

		$title = html_entity_decode($title, ENT_QUOTES);
		if ($location) {
			$location	= html_entity_decode($location, ENT_QUOTES);
		}
#		$city		= html_entity_decode($city,ENT_QUOTES,'UTF-8');

		$title		= preg_replace('~&#([0-9]+);~eu', 'chr("\\1")', $title);
		if ($location) {
			$location	= preg_replace('~&#([0-9]+);~eu', 'chr("\\1")', $location);
		}
	}
	$title = html_entity_decode(utf8_mytrim($title), ENT_QUOTES, 'UTF-8');
	if ($location) {
		$location = html_entity_decode(utf8_mytrim($location), ENT_QUOTES, 'UTF-8');
	}
	if ($city) {
		$city = html_entity_decode(utf8_mytrim($city), ENT_QUOTES, 'UTF-8');
	}

	if (!$title
	||	!$city
	) {
		return null;
	}

	$exact_location = memcached_simple_hash(['location', 'city'], '
		SELECT LOCATIONID,c.NAME="'.addslashes($city).'"
		FROM location AS l
		LEFT JOIN city AS c USING (CITYID)
		WHERE "'.addslashes($location).'" IN (l.NAME, l.TITLE, l.ORDERNAME)
		ORDER BY c.NAME="'.addslashes($city).'" DESC',
		TEN_MINUTES
	);
	if ($exact_location) {
		[$exact_locationid, $exact_city] = keyval($exact_location);
	} else {
		$exact_locationid = $exact_city = false;
	}

	$usestamp = $stamp ?: $stamp_tzi;
	if ($debug) {
		echo 'TITLE: ',$title,', DATE: ';
		_datetime_display($usestamp);
		if ($stamp_tzi) {
			?> (TZI)<?
		}
	}
	if ($debug) {
		echo ', LOCATION: ',$location;
		if ($exact_locationid) {
			echo ', (',$exact_locationid,':',($exact_city ? 'city ok' : 'city NOK'),')';
		}
	}
	if ($debug) {
		echo ', CITY: ',$city;
	}
	if (preg_match($prefix_regex = '"^\s*(?:the|het|a|de|een|restaurant|club|bar|caf[eé])\s+(.*)$"iu', $location, $matches)) {
		$location = $matches[1];
	}

	$lower_title	= mb_strtolower($title);
	$lower_location	= mb_strtolower($location);
	$lower_city		= mb_strtolower($city);

	if ($lower_city === 'arnhem' && $lower_location === 'grote zaal') {
		$lower_location = 'luxor live';
	}

	$stitle =	'"%'.addslashes(preg_replace('"(?:[^\w\d]+|&.*;)"u', '%', preg_replace('"\(.*?\)"u', '', $title))).'%"';
	$slocation =	'"%'.addslashes(preg_replace('"(?:[^\w\d]+|&.*;)"u', '%', preg_replace('"\(.*?\)"u', '', $location))).'%"';
	$scity = 	'"%'.addslashes(preg_replace('"(?:[^\w\d]+|&.*;)"u', '%', preg_replace('"\(.*?\)"u', '', $city))).'%"';

	$trysuccessor = memcached_single_assoc(['location','city'], '
		SELECT LOCATIONID,FOLLOWUPID,location.NAME
		FROM location
		JOIN city USING (CITYID)
		WHERE FOLLOWUPID
		  AND location.NAME LIKE '.$slocation.'
		  AND city.NAME LIKE '.$scity.'
		ORDER BY FOLLOWUPID ASC',
		TEN_MINUTES
	);
	if ($trysuccessor) {
		$successor = null;
		while (	$trysuccessor['FOLLOWUPID']
		&&	($trysuccessor = memcached_single_assoc('location', '
			SELECT LOCATIONID,FOLLOWUPID,NAME
			FROM location
			WHERE LOCATIONID='.$trysuccessor['FOLLOWUPID'].'
			  AND NAME NOT LIKE '.$slocation,
			TEN_MINUTES
			))
		) {
			$successor = $trysuccessor;
		}
		if ($successor) {
			$location = $successor['NAME'];
			$lower_location = mb_strtolower($location);
		}
	}

	$range = $type === 'ticketswap' ? HALF_DAY : 3 * ONE_HOUR;

	$separator = chr(0x1F);
	[$y, $m, $d] = _getdate($usestamp);
	$stampname = $stamp ? 'STAMP' : 'STAMP_TZI';
	if (!($parties = db_rowuse_hash(['party', 'location', 'presaleinfo'],'
		SELECT	party.PARTYID,party.NAME,party.STAMP,STAMP_TZI,SUBTITLE,NOTIME,PRESALE_STAMP,
			(SELECT PRICE FROM partyprice AS pp WHERE pp.PARTYID=party.PARTYID AND WHAT="entrance" AND MEMBERSHIP IS NULL AND TYPE="door"	AND ACCESS="everyone" AND TIME_HOUR=-1 AND EARLY_BIRD = 0 AND NOT GROUP_AMOUNT AND CONSUMPTIONS IS NULL AND PASSEPARTOUT = 0 AND VIP = 0 AND CURRENCY="EUR" ORDER BY PRICE ASC LIMIT 1) AS COST_DOOR,
			(SELECT PRICE FROM partyprice AS pp WHERE pp.PARTYID=party.PARTYID AND WHAT="entrance" AND MEMBERSHIP IS NULL AND TYPE="presale"	AND ACCESS="everyone" AND TIME_HOUR=-1 AND EARLY_BIRD = 0 AND NOT GROUP_AMOUNT AND CONSUMPTIONS IS NULL AND PASSEPARTOUT = 0 AND VIP = 0 AND CURRENCY="EUR" ORDER BY PRICE ASC LIMIT 1) AS COST_PRESALE,
			(SELECT PRICE FROM partyprice AS pp WHERE pp.PARTYID=party.PARTYID AND WHAT="entrance" AND MEMBERSHIP IS NULL AND TYPE="presale"	AND ACCESS="everyone" AND TIME_HOUR=-1 AND     EARLY_BIRD AND NOT GROUP_AMOUNT AND CONSUMPTIONS IS NULL AND PASSEPARTOUT = 0 AND VIP = 0 AND CURRENCY="EUR" ORDER BY PRICE ASC LIMIT 1) AS COST_EARLYBIRD,
			(SELECT PRICE FROM partyprice AS pp WHERE pp.PARTYID=party.PARTYID AND WHAT="entrance" AND MEMBERSHIP IS NULL AND TYPE="presale"	AND ACCESS="everyone" AND TIME_HOUR=-1 AND EARLY_BIRD = 0 AND NOT GROUP_AMOUNT AND CONSUMPTIONS IS NULL AND PASSEPARTOUT = 0 AND     VIP AND CURRENCY="EUR" ORDER BY PRICE ASC LIMIT 1) AS COST_VIP,
			
			PAYLOGIC,PAYLOGICV2,PAYLOGICPOS,SEEDANCE,TIMOCOID,TIMOCOSHOPID,EASYTICKET,TICKETSCRIPT,TICKETSCRIPTV2,RAVEPAS,EVENTTICKETS,WEBSITE,EVENTTRAVEL,XTASIA,TICKETMASTER,YOURTICKETPROVIDER,IKBENAANWEZIG,EVENTIM,EVENTIMSERIESID,TICKETSWAP,
			LOCATIONID,IF(location.NAME="",location.TITLE,location.NAME) AS LOCATION_NAME,
			
				party.NAME LIKE '.$stitle.'
			OR	REPLACE(party.NAME," ","") LIKE '.$stitle.'
			OR	CONCAT(party.NAME," ",SUBTITLE) LIKE '.$stitle.'
			OR	CONCAT(REPLACE(party.NAME," ","")," ",SUBTITLE) LIKE '.$stitle.'
				'.(
				strlen($title) > 3
			?	'	/* substring */
				OR	LENGTH(party.NAME)>3
				AND	"'.addslashes($title).'" LIKE CONCAT("%",REPLACE(party.NAME," ","%"),"%")'
			:	null).'
			
			AS NAME_OK,
			city.NAME AS CITY_NAME,
		(	SELECT GROUP_CONCAT(NAME SEPARATOR "'.$separator.'")
			FROM connect
			JOIN organization ON ASSOCID=ORGANIZATIONID
			WHERE MAINTYPE="party"
			  AND MAINID=party.PARTYID
			  AND ASSOCTYPE="organization") AS ORGANIZATION_NAMES,
		(	SELECT GROUP_CONCAT(ASSOCID)
			FROM connect
			WHERE MAINTYPE="party"
			  AND MAINID=party.PARTYID
			  AND ASSOCTYPE="organization") AS ORGIDS
		FROM party
		LEFT JOIN location USING (LOCATIONID)
		LEFT JOIN boarding USING (BOARDINGID)
		LEFT JOIN city ON city.CITYID=IF(BOARDINGID,boarding.CITYID,IF(location.CITYID,location.CITYID,party.CITYID))
		LEFT JOIN presaleinfo ON presaleinfo.PARTYID=party.PARTYID
		WHERE			party.'.$stampname.' BETWEEN '.($usestamp - $range).' AND '.($usestamp + $range).'
		OR	NOTIME AND	party.STAMP_TZI = '.mktime(12, 0, 0, $m, $d, $y)
	))) {
		if ($debug) {
			echo "\n";
		}
		return $parties === false ? false : null;
	}

	$new_lower_city = preg_replace('"(\s*\(.*?\)\s*)"u','',$lower_city);
	if ($new_lower_city) {
		$lower_city = $new_lower_city;
	}
	$lower_title = preg_replace('"\b\d+\s*(januar[iy]|februar[iy]|ma(?:art|rch)|april|m(?:ei|ay)|jun[ie]|jul[iy]|august(?:us)?|september|o[kc]tober|november|december)\s*\d{4}\b"i','',$lower_title);

	$lower_title	= preg_replace('"([^\d\w]+|\s+)"u', '', $lower_title);
	$lower_location	= preg_replace('"([^\d\w]+|\s+)"u', '', $lower_location);
	$lower_city		= preg_replace('"([^\d\w]+|\s+)"u', '', $lower_city);

	$lower_title	= iconv('UTF-8', 'ASCII//TRANSLIT', $lower_title);
	$lower_location	= iconv('UTF-8', 'ASCII//TRANSLIT', $lower_location);
	$lower_city		= iconv('UTF-8', 'ASCII//TRANSLIT', $lower_city);

	if (!$lower_city
	||	!$lower_title
	) {
		if ($debug) {
			echo "missing t|c|l\n";
		}
		return null;
	}

	$lower_title = str_replace('amsterdamdanceevent','',$lower_title);

	$generic_title = check_for_generic_name($lower_title);

	if ($debug
	&&	$generic_title
	) {
		echo ', GENERIC TITLE';
	}
	$lower_title = preg_replace('"(busreizen|busreis|busvervoer|busservice)"','',$lower_title);
	if ($debug) {
		echo ", OPTIONS:\n";
	}
	foreach ($parties as $partyid => &$party) {
		$cmp_title		= $party['NAME'];
		$cmp_location	= $party['LOCATION_NAME'] ?? '';
		$cmp_city		= $party['CITY_NAME'];

		if (preg_match($prefix_regex,$cmp_location,$matches)) {
			$cmp_location = $matches[1];
		}

		$cmp_title		= mb_strtolower(preg_replace('"([^\d\w]+|\s+)"u', '', $cmp_title));
		$cmp_location	= mb_strtolower(preg_replace('"([^\d\w]+|\s+)"u', '', $cmp_location));
		$cmp_city		= mb_strtolower(preg_replace('"([^\d\w]+|\s+)"u', '', $cmp_city));

		if ($cmp_city
		&&	false !== mb_strpos($lower_location, $lower_city)
		&&	false === mb_strpos($cmp_location, $cmp_city)
		) {
			$cmp_location .= ' '.$cmp_city;
		}

		$cmp_title2 =
			$party['SUBTITLE']
		?	mb_strtolower($cmp_title.preg_replace('"([^\d\w]+|\s+)"u', '', $party['SUBTITLE']), 'utf-8')
		:	null;

		$cmp_title	= iconv('UTF-8', 'ASCII//TRANSLIT', $cmp_title);
		$cmp_location	= iconv('UTF-8', 'ASCII//TRANSLIT', $cmp_location);
		$cmp_city	= iconv('UTF-8', 'ASCII//TRANSLIT', $cmp_city);
		if ($cmp_title2) {
  			$cmp_title2	= iconv('UTF-8', 'ASCII//TRANSLIT', $cmp_title2);
		}
		$generic = check_for_generic_name($cmp_title);

		if ($party['NAME_OK']
		&&	(	$generic_title
			||	$generic
			)
		) {
			$party['NAME_OK'] = false;
		}

		if ($party['NAME_OK']) {
			# don't award extra points for generic names:
		}
		similar_text($lower_title,	$cmp_title,	$title_pct);
		similar_text($lower_location,	$cmp_location,	$location_pct);
		if ($lower_city === 'amsterdam' && $cmp_city === 'rotterdam'
		||	$lower_city === 'rotterdam' && $cmp_city === 'amsterdam'
		) {
			$city_pct = 0;
		} else {
			similar_text($lower_city,	$cmp_city,	$city_pct);
		}
		if ($cmp_title2) {
			similar_text($lower_title,	$cmp_title2,	$subtitled_pct);
			if ($subtitled_pct > $title_pct) {
				$title_pct = $subtitled_pct;
			}
		}
		$is_city = memcached_single('city','SELECT 1 FROM city WHERE NAME IN ("'.addslashes($lower_location).'","'.addslashes($cmp_location).'")');

		$time_diff = $party['NOTIME'] ? 0 : (abs($usestamp - $party[$stamp ? 'STAMP' : 'STAMP_TZI']));
		$party['TITLE_SIMILARITY'] = $title_pct + ($party['NAME_OK'] ? 50 : 0);
		$party['LOCATION_SIMILARITY'] = $location_pct + ($is_city ? -20 : 0);
		$gen_mult = 1;
		if ($party['LOCATION_SIMILARITY'] != 100) {
			if ($generic && $generic_title) {
				$party['LOCATION_SIMILARITY'] *= .5;
				$gen_mult = .5;
			}
		}
		$party['CITY_SIMILARITY'] = $city_pct;
		$location_mismatch = $exact_locationid ? ($exact_locationid !== $party['LOCATIONID'] ? ($exact_city ? -100 : -50) : +50) : 0;
		$party['SIMILARITY'] = $title_pct + ($party['NAME_OK'] ? 50 : 0) + ($location_pct + ($is_city ? -20 : 0))*$gen_mult + $city_pct - ($time_diff * 20 / 3600) - ($party['NOTIME'] ? 20 : 0) + $location_mismatch;
		$party['SIMISTR'] =
			'title: '.round($title_pct).
			($party['NAME_OK'] ? ' (substr: +50)' : null).
			($generic ? ' (generic)' : null).
			', location: '.round($location_pct).($is_city ? ' (is_city: -20)' : null).($gen_mult != 1 ? ' (generic: x '.$gen_mult.')' : null).
			', city: '.round($city_pct).//($city_substr ? ' (substr: '.$city_substr.')' : null).
			($location_mismatch ? ', location mismatch: '.$location_mismatch : null).
			($time_diff ? ', time -'.round($time_diff * 20 / 3600) : null).
			($party['NOTIME'] ? ', notime -20' : null);
	}
	unset($party);
	number_reverse_asort($parties,'SIMILARITY');

	$chosen = false;
	static $__done;
	foreach ($parties as $partyid => $party) {
		if ($party['TITLE_SIMILARITY'] <= 50
		||	$party['SIMILARITY'] < 220
		) {
			if ($debug) {
				echo 'X ';
			}
		} elseif (!$chosen) {
			if (isset($__done[$type][$partyid])) {
				if ($debug) {
					echo '= ';
				}
			} else {
				if ($debug) {
					echo '- ';
				}
			}
			$__done[$type][$partyid] = true;
			$chosen = $party;
			if ($debug) {
				if ($party['CITY_SIMILARITY'] < 100) {
					echo "LOW CITY CHOSEN: ";
				}
			}
		}
		if ($debug) {
			change_timezone('UTC');
			echo $partyid,': ', round($party['SIMILARITY']),
				', ', $party['NAME'],
				', ', _datetime_get($party['STAMP_TZI']),
				', ', ($party['LOCATION_NAME'] ?? ''),
				' (', $party['LOCATIONID'],')',
				', ', $party['CITY_NAME'],
				', SIMILARITY: ', $party['SIMISTR'];
			echo "\n";
			change_timezone();
		}
	}
	if (!$chosen) {
		return null;
	}
	$chosenid = $chosen['PARTYID'];

	if ($upds !== false
	&&	!empty($chosen['WEBSITE'])
	&&	false !== stripos($chosen['WEBSITE'], $findtype = $type === 'ticketscriptv2' ? 'ticketscript' : $type)
	) {
		$sites = [];
		foreach (preg_split("'[\n\s]+'s",$chosen['WEBSITE']) as $site) {
			$site = mytrim($site);
			if ($site
			&&	false === stripos($site, $findtype)
			) {
				$sites[] = $site;
			}
		}
		$upds[$chosenid]['WEBSITE'] = implode("\n", $sites);
		if ($debug) {
			echo "CLEAR WEBSITE: ",$chosen['WEBSITE'],"\n";
		}
	}
	return $chosen;
}

function check_for_generic_name(string $title): bool {
	$rc = false;
	$stripped = $title;
	$opts = '(?:dag|nacht|day|night|land|festival|party|event|feest|evenement|eve)';
	foreach ([
		'koning(?:innen?|s)'.$opts.'(?:20\d\d)?',
		'newyears?'.$opts.'?(?:20\d\d)?',
		'oudnieuw',
		'nye(?:20\d\d)?',
		'residentnight',
		'(?:queen|king)\'?s?'.$opts.'(?:20\d\d)?',
	] as $nopoints) {
		if (preg_match('"^'.$nopoints.'$"ui', $title)) {
			$rc = true;
		}
		$stripped = preg_replace('"'.$nopoints.'"ui', '', $stripped);
	}
	return $rc || preg_match('"^'.$opts.'$"ui', $stripped);
}
