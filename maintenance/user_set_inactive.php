#!/usr/bin/php
<?php

declare(strict_types=1);

define('CURRENTSTAMP', time());

require_once __DIR__.'/../public_html/_exit_if_readonly.inc';
require_once __DIR__.'/../public_html/_db.inc';
require_once __DIR__.'/../public_html/_error.inc';
require_once __DIR__.'/../public_html/_memcache.inc';
require_once __DIR__.'/../public_html/_date.inc';
require_once __DIR__.'/../public_html/_helper.inc';
require_once __DIR__.'/../public_html/_run_and_exit.inc';

run_and_exit(syslog: true);

function main(): bool {
	if (!($actives = db_simpler_array(['user', 'user_account'],'
		SELECT USERID
		FROM user_account
		JOIN user USING (USERID)
		WHERE STATUS = "active"
		  AND CSTAMP <'.(CURRENTSTAMP - ONE_YEAR)
	))) {
		if ($actives === false) {
			return false;
		}
		syslog_message(LOG_WARNING, 'no active users to deactive');
		return true;
	}
	if (false === ($lastused = db_simple_hash('user_data','
		SELECT SQL_NO_CACHE USERID,LAST_USED
		FROM user_data
		WHERE USERID IN ('.implode(',',$actives).')
		  AND LAST_USED < UNIX_TIMESTAMP() - '.ONE_YEAR
	))) {
		return $lastused !== false;
	}
	$userlist = db_rowuse_hash(['user', 'user_account'],'
		SELECT SQL_NO_CACHE
			user_account.USERID,user_account.NICK,
			EMAIL, NAME, REALNAME
		FROM user_account
		JOIN user USING (USERID)
		WHERE USERID IN ('.implode(',',array_keys($lastused)).')'
	);
	if (!$userlist) {
		return $userlist !== false;
	}
	foreach ($userlist as $userid => $user) {
		extract($user);
		if ($EMAIL) {
			if (false === ($messages = db_simpler_array('directmessage', "
				SELECT DISTINCT FROM_USERID
				FROM directmessage
				WHERE READM = 0
				  AND TO_DELETED = 0
				  AND TO_USERID = $userid"
			))) {
				return false;
			}
			$message_count = count($messages);
			if (false === ($seen =  db_single('buddy_request_seen',"SELECT STAMP FROM buddy_request_seen WHERE USERID = $userid"))) {
				return false;
			}
			$seen += 0;
			if (false === ($buddies = db_simpler_array('buddy_request', "
				SELECT USERID_INI, CSTAMP
				FROM buddy_request
				WHERE USERID_ACC = $userid
				  AND CSTAMP > $seen"
			))) {
				return false;
			}
			$buddycnt = count($buddies);
			$msg =	"Beste $NICK ($USERID),\r\n\r\n".
				"Je bent voor het laatst op "._dateday_get($lastused[$USERID])." langsgeweest op Partyflock en inmiddels automatisch op inactief gezet, maar misschien ben je toch benieuwd hoe het nu op Partyflock is, ".
				"of misschien is het leuk om oude bekenden op te zoeken!\r\n".
				"Als je toch weer een keer inlogt word je automatisch weer op actief gezet!\r\n\r\n";

			if ($messages || $buddies) {
				if ($messages) {
					if ($message_count === 1) {
						$msg .= 'Je hebt nog een ongelezen bericht van '.memcached_nick($messages[0]);
					} else {
						$msg .= 'Je hebt nog ongelezen berichten van ';
						foreach ($messages as $fuserid) {
							$msg .= memcached_nick($fuserid);
							switch (--$message_count) {
							default: $msg .= ', '; break;
							case 1: $msg .= ' en '; break;
							case 0: break;
							}
						}
					}
					$msg .= "\r\n";
				}
				if ($buddies) {
					if ($buddycnt === 1) {
						$msg .= 'Je hebt nog een nieuw vriendschapsverzoek van '.memcached_nick($buddies[0]);
					} else {
						$msg .= 'Je hebt nog nieuwe vriendschapsverzoeken van ';
						foreach ($buddies as $fuserid) {
							$msg .= memcached_nick($fuserid);
							switch (--$buddycnt) {
							default: $msg .= ', '; break;
							case 1: $msg .= ' en '; break;
							case 0: break;
							}
						}
					}
					$msg .= "\r\n";
				}
				$msg .= "\r\n";
			}
			$msg .=	"Je nick is: $NICK\r\n".
					"Je USERID is: $USERID\r\n\r\n".
					"Je kunt hier inloggen: https://partyflock.nl/user/login\r\n\r\n".
					"Ben je je wachtwoord vergeten? Volg dan deze link:\r\n\r\n".
					"https://partyflock.nl/user/$USERID/sendpasswdtoken\r\n\r\n".
					'Hopelijk tot ziens!';

			if (!mail(
				$EMAIL,
				'Deactivatie Partyflock profiel',
				quoted_printable_encode($msg),
				[
					'From'						=> 'Partyflock <<EMAIL>>',
					'Auto-Submitted'			=> 'auto-generated',
					'Message-ID'				=> "<user_{$userid}_time_".CURRENTSTAMP.'@partyflock.inactivation',
					'Content-Type'				=> 'text/plain; charset=windows-1252',
					'Content-Transfer-Encoding'	=> 'quoted-printable',
				],
				'-<EMAIL>')
			) {
				_error('failed to send mail to '.$EMAIL);
				return false;
			}
		}
		if (!db_insert('userstatuschange', "
			INSERT INTO userstatuschange SET
				STATUS	= 'inactive',
				STAMP	= ".CURRENTSTAMP.",
				USERID	= $userid")
		||	!db_update('user_account','
			UPDATE user_account SET
				STATUS	="inactive"
			WHERE USERID='.$userid)
		) {
			return false;
		}
		syslog(LOG_INFO, "inactivated user $userid with email $EMAIL");
	}
	return true;
}
