#!/usr/bin/php
<?php

const GLUSTER_EXE		= '/usr/sbin/gluster';
const INACTIVE_VOLUMES	= ['talentuploads'];

$_SERVER['REQUEST_URI'] = 'check_gluster_health';

define('CURRENTSTAMP',	time());
define('DEBUG',			!isset($_SERVER['CRON']));

require_once __DIR__.'/../public_html/_run_and_exit.inc';
require_once __DIR__.'/../public_html/_execute.inc';

if (file_exists('/offline')
||	file_exists('/readonly')
) {
	# Site intentionally offline or in maintenance.
	exit;
}

run_and_exit(syslog: true);

function main(): bool {
	[$rc, $stdout, $stderr] = execute($exe = GLUSTER_EXE.' volume list');
	if ($rc) {
		syslog_message(LOG_ERR, $exe.': failed'.($stderr ? ': '.$stderr : ''));
		return false;
	}
	if (!($stdout = trim($stdout))) {
		syslog_error(LOG_ERR, 'no gluster volumes found');
		return false;
	}
	$ok = true;
	foreach (explode("\n", $stdout) as $volume) {
		if (in_array($volume, INACTIVE_VOLUMES)) {
			# volumes not activeley read or written to
			continue;
		}
		[$rc, $stdout, $stderr] = execute($exe = GLUSTER_EXE.' volume info '.$volume);
		if ($rc) {
			syslog_error(LOG_ERR, $exe.': failed'.($stderr ? ': '.$stderr : ''));
			$ok = false;
			continue;
		}
		$stdout = trim($stdout);
		$need_bricks = 0;
		$bricks = [];
		foreach (explode("\n", $stdout) as $line) {
			if (preg_match('"^Number of Bricks: .*? = (\d+)$"',$line,$match)) {
				$need_bricks = (int)$match[1];
				continue;
			}
			if (preg_match('"^Brick(\d+): (.*)$"',$line,$match)) {
				$bricks[] = $match[2];
			}
		}
		if (!$need_bricks) {
			syslog_error(LOG_ERR, $volume.': no bricks found');
			$ok = false;
			continue;
		}
		$total_bricks = count($bricks);
		if ($need_bricks !== $total_bricks) {
			syslog_error(LOG_ERR, $volume.': in volume info, expected '.$need_bricks.', found '.$total_bricks);
			$ok = false;
			continue;
		}

		[$rc, $stdout, $stderr] = execute($exe = GLUSTER_EXE.' volume status '.$volume);
		if ($rc) {
			syslog_error(LOG_ERR, $exe.': failed'.($stderr ? ': '.$stderr : ''));
			$ok = false;
			continue;
		}
		$stdout = trim($stdout);
		
		if (!preg_match_all('"^Brick ([^ ]+)\s+\d+\s+\d+\s+(.*?)\s+"ms', $stdout, $matches)) {
			syslog_error(LOG_ERR, $volume.': could not find bricks in status');
			$ok = false;
			continue;
		}
		if ($need_bricks !== ($total_bricks = count($matches[0]))) {
			syslog_error(LOG_ERR, $volume.': in volume status, expected '.$need_bricks.', found '.$total_bricks);
			$ok = false;
			continue;
		}
		foreach ($matches[1] as $i => $host) {
			if (!isset($matches[2][$i])) {
				mail_log('missing index 2 in matches', item: get_defined_vars());
			}
			$host = str_replace("\n", '', $host);
			$online = $matches[2][$i];
			if ($online !== 'Y') {
				syslog_error(LOG_ERR, $volume.': brick '.$host.' is not online');
				$ok = false;
				continue 2;
			}
			# somehow, in_array($host, $bricks) does not work:
			$found = false;
			foreach ($bricks as $i => $b) {
				if ($b == $host) {
					$found = $i;
					break;
				}
			}
			if ($found === false) {
				syslog_error(LOG_ERR, $volume.': brick '.$host.' is not in expected list of bricks');
				$ok = false;
				continue;
			}
			unset($bricks[$found]);
		}
		if ($bricks) {
#			foreach ($bricks as $i => $brick) {
#				if (str_starts_with($brick, 'newdata3')) {
#					unset($bricks[$i]);
#				}
#			}
			if ($bricks) {
				syslog_error(LOG_ERR, $volume.': missing bricks: '.implode(', ',$bricks));
				$ok = false;
			}
		}
	}
	return $ok;
}
