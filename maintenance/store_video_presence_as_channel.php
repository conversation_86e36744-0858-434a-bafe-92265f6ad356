#!/usr/bin/php
<?php

declare(strict_types=1);

require_once __DIR__.'/../public_html/_exit_if_readonly.inc';
require_once __DIR__.'/../public_html/_db.inc';
require_once __DIR__.'/../public_html/_error.inc';
require_once __DIR__.'/../public_html/_require.inc';
require_once __DIR__.'/../public_html/_run_and_exit.inc';
require_once __DIR__.'/../public_html/defines/youtube.inc';

define('CURRENTSTAMP',time());
#define('DRYRUN',true);

run_and_exit(run_once: true, syslog: true);

# add video presences of top 300 items as an official videochannel so videos can be processed by admins

function main(): bool {
	if (false === ($existings = db_rowuse_array('videochannel', 'SELECT TYPE, NAME, USER, HANDLE, ID, CHANNELID FROM videochannel'))) {
		return false;
	}
	foreach ($existings as $existing) {
		/** @noinspection PhpRedundantOptionalArgumentInspection */
		extract($existing, \EXTR_OVERWRITE);
		$existing_user  [$TYPE][$USER]   = $CHANNELID;
		$existing_handle[$TYPE][$HANDLE] = $CHANNELID;
		$existing_id    [$TYPE][$ID]     = $CHANNELID;
		$existing_name  [$TYPE][$NAME]   = $CHANNELID;
	}
	if (false === ($done_presencestr = db_single_string('videochannel_presence_done', 'SELECT GROUP_CONCAT(PRESENCEID) FROM videochannel_presence_done'))) {
		return false;
	}
	$done_presencestr = $done_presencestr ?: '0';

	foreach (['artist', 'location', 'organization'] as $ELEMENT) {
		syslog(LOG_INFO, "start processing of presences of element $ELEMENT");

		$presences = null;

		switch ($ELEMENT) {
		case 'organization':
			# try to add the video presence of the last 100 added organizations as a videochannel
			if (false === ($skip_organization_idstr = db_single_string('hidden_orgs', 'SELECT GROUP_CONCAT(ID) FROM hidden_orgs'))) {
				return false;
			}
			if (!$skip_organization_idstr) {
				$skip_organization_idstr = '0';
			}
			$presences = db_rowuse_array('presence', "
				SELECT DISTINCT PRESENCEID, ID, TYPE, SITE
				FROM presence
				WHERE TYPE IN ('youtube', 'vimeo')
				  AND SUPPORTED
				  AND ELEMENT = 'organization'
				  AND ID NOT IN ($skip_organization_idstr)
				  AND PRESENCEID NOT IN ($done_presencestr)
				ORDER BY ID DESC"
			);
			break;

		case 'location':
			# try to add the video presence of the last 100 added locations as a videochannel

			$presences = db_rowuse_array('presence', "
				SELECT DISTINCT PRESENCEID, ID, TYPE, SITE
				FROM presence
				WHERE TYPE IN ('youtube', 'vimeo')
				  AND SUPPORTED
				  AND ELEMENT = 'location'
				  AND PRESENCEID NOT IN ($done_presencestr)
				ORDER BY ID DESC"
			);
			break;

		case 'artist':
			# try to add the video presence of the top 100 artists of the past 90 days as a video channel

			$counter_table = $ELEMENT.'_counter';
			if (!($all_domain_set = get_full_set_from_enum_spec($counter_table, 'DOMAIN'))) {
				return false;
			}
			# get stats from past 90 days.
			if (!($ids = db_simpler_array($counter_table, "
				SELECT {$ELEMENT}ID AS ID
				FROM `$counter_table`
				WHERE DOMAIN IN $all_domain_set
				  AND DAYNUM >= TO_DAYS(NOW()) - 90
				GROUP BY ID
				ORDER BY COUNT(*) DESC
				LIMIT 200"))
			) {
				return $ids !== false;
			}
			$presences = db_rowuse_array('presence', "
				SELECT PRESENCEID, ID, TYPE, SITE
				FROM presence
				WHERE ELEMENT = '$ELEMENT'
				  AND TYPE IN ('youtube', 'vimeo')
				  AND ELEMENT = 'artist'
				  AND PRESENCEID NOT IN ($done_presencestr)
				  AND SUPPORTED
				  AND ID IN (".implode(',', $ids).')'
			);
			break;
		}
		if (!$presences) {
			syslog(LOG_INFO, "all presences for element $ELEMENT already processed, nothing to be done");
			if ($presences === false) {
				return false;
			}
			continue;
		}

		foreach ($presences as $presence) {
			/** @noinspection PhpRedundantOptionalArgumentInspection */
			extract($presence, \EXTR_OVERWRITE);
			syslog(LOG_DEBUG, "checking presence $PRESENCEID: $SITE");

			if ((preg_match('"youtube\.\w{2,3}/(user|channel)/([a-z\d_-]+)"i',	$SITE, $match) && $nm = 'user_or_channel')
			||	(preg_match('"youtube\.\w{2,3}/c/(?P<name>[a-z\d_-]+)"i', 		$SITE, $match)	&& $nm = 'custom')
			||	(preg_match('"youtube\.\w{2,3}/@(?P<handle>[a-z\d_-]+)"i', 		$SITE, $match)	&& $nm = 'handle')
			||	(preg_match('"youtube\.\w{2,3}/(?P<name>[a-z\d_-]+)$"i',	 	$SITE, $match)	&& $nm = 'user')
			||	(preg_match('"vimeo\.com/(?P<name>[a-z\d_-]+)"i', 				$SITE, $match)	&& $nm = 'vimeo')
			) {
				$youtube_type =
				$handle =
				$name =
				$user =
				$id = '';

				switch ($nm) {
				case 'user_or_channel':
					$type = 'youtube';
					[, $channel_type, $arg] = $match;
					$youtube_type = ($channel_type === 'channel' ? 'channel' : 'user');
					if ($channel_type === 'channel') {
						$id = $arg;
						$log_info = 'id '.$id;
					} else { // channel_type === 'user'
						$user = $id = $arg;
						$log_info = 'user '.$user;
					}
					break;

				case 'custom':
					$type = 'youtube';
					$youtube_type = 'custom';
					$name = $match['name'];
					if (!($data = file_get_contents($SITE))
					&&	preg_match('!itemprop="channelId" content="(?<id>[^"]+)"!', $data, $match)
					) {
						$id = $match['id'];
						$log_info = 'id '.$id;
					} else {
						if (!create_videochannel_presence_done('not_understood', $PRESENCEID)) {
							return false;
						}
						syslog_warning(LOG_WARNING, "could not find channel ID for presence $PRESENCEID");
						continue 2;
					}
					break;

				case 'handle':
					$type = 'youtube';
					if (!($channel_url = get_youtube_channel_url_from_any_url($SITE))) {
						if (!create_videochannel_presence_done('not_supported', $PRESENCEID)) {
							return false;
						}
						$handle = $match['handle'];
						// $youtube_type = 'handle';
						syslog_warning(LOG_WARNING, "ignore youtube handle @$handle in presence $PRESENCEID");
						continue 2;
					}
					if (!db_insert('presence_log', '
						INSERT INTO presence_log
						SELECT *, '.CURRENTSTAMP.', 0 FROM presence
						WHERE PRESENCEID = '.$PRESENCEID)
					||	!db_update('presence', '
						UPDATE presence SET
							CUSERID	= 0,
							CSTAMP	= '.CURRENTSTAMP.',
							SITE	= "'.addslashes($channel_url).'"
						WHERE PRESENCEID = '.$PRESENCEID)
					) {
						return false;
					}
					$youtube_type = 'channel';
					$id = get_channel_from_url($channel_url);
					$log_info = 'id '.$id;
					break;

				case 'user':
					$type = 'youtube';
					$name = $id = $user = $match['name'];
					$youtube_type = 'user';
					$log_info = 'user '.$user;
					break;

				case 'vimeo':
					$name = $id = $match['name'];
					$type = 'vimeo';
					if ($name === 'channels') {
						# vimeo.com/channels is not supported
						if (!create_videochannel_presence_done('not_supported', $PRESENCEID)) {
							return false;
						}
						continue 2;
					}
					$log_info = 'id '.$id;
					break;
				}
				if ($id
				&&	($existing_CHANNELID = $existing_id[$type][$id] ?? null)
				||	$user
				&&	($existing_CHANNELID = $existing_user[$type][$user] ?? null)
				||	$handle
				&&	($existing_CHANNELID = $existing_handle[$type][$handle] ?? null)
				||	$name
				&&	($existing_CHANNELID = $existing_name[$type][$name] ?? null)
				) {
					syslog(LOG_INFO, "already got videochannel type $type, youtube_type $youtube_type, info: $log_info for presence $PRESENCEID");
					if (!create_videochannel_presence_done('copy', $PRESENCEID, $existing_CHANNELID)) {
						return false;
					}
					continue;
				}
				syslog(LOG_INFO, "adding videochannel type $type, youtube_type $youtube_type, info: $log_info for presence $PRESENCEID");
				if (!db_insert('videochannel','
					INSERT IGNORE INTO videochannel SET
						USER		 = "'.addslashes($user).'",
						HANDLE		 = "'.addslashes($handle).'",
						TYPE		 = "'.addslashes($type).'",
						NAME		 = "'.addslashes($name).'",
						ID			 = "'.addslashes($id).'",
						YOUTUBE_TYPE = '.($youtube_type ? '"'.$youtube_type.'"' : 'NULL').',
						CUSERID		 = 0,
						CSTAMP		 = '.CURRENTSTAMP)
				) {
					return false;
				}
				if (!($channelid = db_insert_id())) {
					syslog_error(LOG_ERR, "videochannel successfully added, but no ID for presence $PRESENCEID");
					return false;
				}
				if (!$id) {
					syslog_warning(LOG_WARNING, "could not store ID for new channel with channelid $channelid");
				}
				if (!db_insert('connect', '
					INSERT INTO connect (CSTAMP, CUSERID, MAINTYPE, MAINID, ASSOCTYPE, ASSOCID)
					VALUES	('.CURRENTSTAMP.", 0, '$ELEMENT', $ID, 'videochannel', $channelid),
							(".CURRENTSTAMP.", 0, 'videochannel', $channelid, '$ELEMENT', $ID)")
				||	!create_videochannel_presence_done('initial', $PRESENCEID, $channelid)
				) {
					return false;
				}
				if ($id) {
					$existing_id[$TYPE][$id] = $channelid;
				}
				if ($user) {
					$existing_user[$TYPE][$user] = $channelid;
				}
				if ($handle) {
					$existing_handle[$TYPE][$handle] = $channelid;
				}
				if ($name) {
					$existing_name[$TYPE][$name] = $channelid;
				}
			} else {
				syslog(LOG_INFO, "video channel URL of presence $PRESENCEID not understood: $SITE");
				if (!create_videochannel_presence_done('not_understood', $PRESENCEID)) {
					return false;
				}
			}
		}
	}
	return true;
}

function create_videochannel_presence_done(string $type, int $presenceid, int $channelid = 0): bool {
	return db_insert('videochannel_presence_done', "
		INSERT IGNORE INTO videochannel_presence_done SET
		STAMP		= UNIX_TIMESTAMP(),
		TYPE		= '$type',
		CHANNELID	= $channelid,
		PRESENCEID	= $presenceid");
}
