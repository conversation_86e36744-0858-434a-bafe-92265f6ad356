#!/usr/bin/php
<?php

require_once '/home/<USER>/public_html/_exit_if_readonly.inc';
require_once '/home/<USER>/public_html/_db.inc';
require_once '/home/<USER>/public_html/_require.inc';
require_once '/home/<USER>/public_html/_url.inc';

define('CURRENTSTAMP',	time());
define('DEBUG',		!isset($_SERVER['CRON']));
define('DRYRUN',	false);

ob_implicit_flush(true);
$rc = main();
exit(show_messages_cli() || !$rc ? 1 : 0);

function main(): bool {
	$fixthese = db_rowuse_array('facebook_link', 'SELECT * FROM facebook_link WHERE LINK LIKE "%?%"');
	if (!$fixthese) {
		return $fixthese !== false;
	}
	foreach ($fixthese as $fix) {
		extract($fix);
		$new_LINK = cleanup_url($LINK);
		$new_LINK = preg_replace('"\?(.*)$"u', '', $new_LINK);
		$new_LINK = utf8_mytrim($new_LINK, '/');

		error_log($LINK.' => '.$new_LINK);

		if ($new_LINK === 'profile.php') {
			if (!db_delete('facebook_link', '
				DELETE FROM facebook_link
				WHERE FBID = '.$FBID.'
				  AND STAMP = '.$STAMP.'
				  AND LINK = "'.addslashes($LINK).'"
				  AND TYPE = "'.$TYPE.'"')
			) {
				return false;
		 	}
		} else {
			if (!db_update('facebook_link', '
				UPDATE facebook_link SET
					LINK = "'.addslashes($new_LINK).'"
				WHERE FBID = '.$FBID.'
				  AND STAMP = '.$STAMP.'
				  AND LINK = "'.addslashes($LINK).'"
				  AND TYPE = "'.$TYPE.'"')
			) {
				return false;
			}
		}
	}
	return true;
}
