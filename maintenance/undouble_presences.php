#!/usr/bin/php
<?php

require_once '/home/<USER>/public_html/_exit_if_readonly.inc';
require_once '/home/<USER>/public_html/_db.inc';

define('CURRENTSTAMP',	time());
define('DEBUG',		!isset($_SERVER['CRON']));
#define('DRYRUN',	true);

ob_implicit_flush(true);
$rc = main();
exit(show_messages_cli() || !$rc ? 1 : 0);

function main() {
	$multies = db_simple_hash('presence','
		SELECT ELEMENT,ID,GROUP_CONCAT(PRESENCEID)
		FROM presence
		WHERE TYPE IN ("facebook","facebookpage")
		GROUP BY ELEMENT,ID
		HAVING COUNT(*)>1'
	);
	if (!$multies) {
		return $multies === false ? false : true;
	}
	$strips = [];
	foreach ($multies as $element => $ids) {
		foreach ($ids as $id => $presenceidstr) {
			$sites = db_simple_hash('presence','
				SELECT PRESENCEID,SITE,CSTAMP
				FROM presence
				WHERE PRESENCEID IN ('.$presenceidstr.')'
			);
			$identicals = [];
			foreach ($sites as $presenceid => $info) {
				list($site,$cstamp) = keyval($info);
				if (preg_match('"^(?:https?://)?(?:www\.)?(.*?)/?\??$"i',$site,$match)) {
					$site = $match[1];
				}
				$site = strtolower($site);
				$identicals[$site][$presenceid] = $cstamp;
			}
			foreach ($identicals as $site => $presences) {
				if (count($presences) > 1) {
					arsort($presences);
					list($stripid) = keyval($presences);
					$strips[$stripid] = $stripid;
				}
			}
		}
	}
	if ($strips) {
		$idstr = implode(',',$strips);
		if (!db_insert('presence_log','
			INSERT INTO presence_log
			SELECT presence.*,'.CURRENTSTAMP.',0  FROM presence
			WHERE PRESENCEID IN ('.$idstr.')')
		||	!db_delete('presence','
			DELETE FROM presence
			WHERE PRESENCEID IN ('.$idstr.')')
		) {
			return false;
		}
		if (DEBUG) {
			echo db_affected(),' double presences removed',"\n";
		}
	}
	return true;
}
