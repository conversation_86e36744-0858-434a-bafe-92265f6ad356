#!/usr/bin/php
<?php

$_SERVER['REQUEST_URI'] = $GLOBALS['argv'][0];
$_SERVER['HTTP_HOST'] = 'cli';

define('CURRENTSTAMP',	time());
# define('DEBUG',	!isset($_SERVER['CRON']));
define('DEBUG',		true);

require_once '/home/<USER>/public_html/_stable_slug.inc';
require_once '/home/<USER>/public_html/_db.inc';
require_once '/home/<USER>/public_html/_date.inc';
require_once '/home/<USER>/public_html/_pagechanged.inc';

$dryrun = false;

ob_implicit_flush(true);

if (!openlog(
	'check_stabe_slugs'.(defined('DRYRUN') && DRYRUN ? ' dryrun' : ''),
	(defined('DRYRUN') && DRYRUN ? 0 : LOG_PID) | (isset($_SERVER['CRON']) ? 0 : LOG_PERROR),
	LOG_USER
)) {
	exit(1);
}

foreach ($argv as $arg) {
	if ($arg === 'dryrun') {
		$dryrun = true;
	}
}

define('DRYRUN', $dryrun);

syslog(LOG_INFO, 'started'.(defined('DRYRUN') && DRYRUN ? ' dry run' : ''));

$ok = main();

syslog(LOG_INFO, 'flushing slugs');

flush_slugs();

syslog(LOG_INFO, 'finished');

exit(show_messages_cli() || !$ok ? 1 : 0);

function main(): bool {
	return process_slugs();
}

function process_slugs(): bool {
	if (!($max_end_stamp = db_single('party', '
		SELECT MAX(STAMP + DURATION_SECS)
		FROM party'
	))) {
		return $max_end_stamp !== false;
	}

	syslog(LOG_INFO, 'max end stamp: '._datetime_get($max_end_stamp));

	if (!($existing_names = db_simple_hash('stable_slug', '
		SELECT NAME, PARTYID
		FROM stable_slug
		ORDER BY NAME'
	))) {
		return false;
	}
	$current_names = db_rowuse_hash('party', '
		SELECT	party.SLUG,
			GROUP_CONCAT(PARTYID	ORDER BY 
		 				IF(	UNIX_TIMESTAMP() < party.STAMP + party.DURATION_SECS,
							GREATEST(0, CAST(party.STAMP AS SIGNED) - UNIX_TIMESTAMP()),
							'.$max_end_stamp.' + UNIX_TIMESTAMP() - party.STAMP - party.DURATION_SECS
		 				) ASC
			) AS PARTYIDSTR,
			GROUP_CONCAT(
				IF(UNIX_TIMESTAMP() < party.STAMP + party.DURATION_SECS, "future", "past")
		 				ORDER BY 
		 				IF(	UNIX_TIMESTAMP() < party.STAMP + party.DURATION_SECS,
							GREATEST(0, CAST(party.STAMP AS SIGNED) - UNIX_TIMESTAMP()),
							'.$max_end_stamp.' + UNIX_TIMESTAMP() - party.STAMP - party.DURATION_SECS
		 				) ASC

				) AS `WHENSTR`,
			GROUP_CONCAT( GREATEST(0, CAST(party.STAMP AS SIGNED) - UNIX_TIMESTAMP()) ) AS FUTURE_DIFFSTR,
			GROUP_CONCAT( '.$max_end_stamp.' + UNIX_TIMESTAMP() - party.STAMP - party.DURATION_SECS ) AS PAST_DIFFSTR, 
			GROUP_CONCAT( IF(    UNIX_TIMESTAMP() < party.STAMP + party.DURATION_SECS,
                                                        GREATEST(0, CAST(party.STAMP AS SIGNED) - UNIX_TIMESTAMP()),
                                                	'.$max_end_stamp.' + UNIX_TIMESTAMP() - party.STAMP - party.DURATION_SECS)
							 				ORDER BY 
		 				IF(	UNIX_TIMESTAMP() < party.STAMP + party.DURATION_SECS,
							GREATEST(0, CAST(party.STAMP AS SIGNED) - UNIX_TIMESTAMP()),
							'.$max_end_stamp.' + UNIX_TIMESTAMP() - party.STAMP - party.DURATION_SECS
		 				) ASC
					) AS WORTHSTR,
			CAST(
				REGEXP_SUBSTR(
		 			GROUP_CONCAT(
		 				party.PARTYID
		 				ORDER BY 
		 				IF(	UNIX_TIMESTAMP() < party.STAMP + party.DURATION_SECS,
							GREATEST(0, CAST(party.STAMP AS SIGNED) - UNIX_TIMESTAMP()),
							'.$max_end_stamp.' + UNIX_TIMESTAMP() - party.STAMP - party.DURATION_SECS
		 				) ASC,
		 				PARTYID DESC
					),
					"^\\\\d+"
				)
				AS INTEGER
			)
			AS NEAREST_PARTYID
		FROM party
		JOIN (  SELECT DISTINCT SLUG
			FROM party
			WHERE SLUG != ""
			  AND STAMP > '.slugs_gather_since_stamp().'
			  AND FESTIVAL = 1
			  AND ACCEPTED = 1
			  AND CANCELLED = 0
			  AND POSTPONED = 0
			  AND MOVEDID = 0
/*			  AND SLUG = "under-the-bridge-festival-nl"*/
		) AS gathered_slugs USING (SLUG)
		WHERE party.FESTIVAL = 1
		  AND party.ACCEPTED = 1
		  AND CANCELLED = 0
		  AND POSTPONED = 0
		  AND MOVEDID = 0
		GROUP BY SLUG'
	);

	# syslog(LOG_DEBUG, 'current_names: '.var_get($current_names));

	if (!$current_names) {
		if ($current_names !== false) {
			syslog(LOG_INFO, 'inserting no new slugs');
		}
		return $current_names !== false;
	}

	foreach ($current_names as $slug_name => &$info) {
		extract($info);

		$country_code = substr($slug_name, -2);
		$full_slug_name_festival = $slug_name.'-festival-'.$country_code;

		$worths = explode(',', $WORTHSTR);
		$worth_groups = [];
		foreach ($worths as $worth) {
			$worth = (int)$worth;
			$worth_groups[$worth][] = $worth;
		}

		$most_worthy = array_key_first($worth_groups);

		$most_worthies = $worth_groups[$most_worthy];

		if (($worthy_count = count($most_worthies)) > 1) {
			$partyids = explode(',', $PARTYIDSTR);

			# choose event met most views
			syslog(LOG_INFO, 'have multiple worthy parties, choosing one with most views in '.$PARTYIDSTR);

			$views = memcached_simple_hash('party_view', '
				SELECT PARTYID, SUM(HITS) AS TOTAL_VIEWS
				FROM party_view
				WHERE PARTYID IN ('.$PARTYIDSTR.')
				GROUP BY PARTYID
				ORDER BY TOTAL_VIEWS DESC, PARTYID DESC',
				ONE_DAY
			);
			if ($views === false) {
				return false;
			}
			if ($worthy_count !== count($views)) {
				foreach ($partyids as $partyid) {
					$partyid = (int)$partyid;
					if (!isset($views[$partyid])) {
						$views[$partyid] = 0;
					}
				}
				# sort partyids, so if there are no views, we pick the same lowest partyid
				asort($views);
			}

			$info['NEAREST_PARTYID'] =
			$NEAREST_PARTYID = array_key_first($views);

			syslog(LOG_INFO, 'have multiple worthy parties, chooseing one with most view: '.$info['NEAREST_PARTYID']);
		}

		if (isset($current_names[$full_slug_name_festival])) {
			syslog(LOG_INFO, 'full slug name festival exists: '.$full_slug_name_festival.', party: '.$NEAREST_PARTYID);
			$full_slug_name_non_festival = $slug_name.'-'.$country_code;

			if (isset($current_names[$full_slug_name_non_festival])) {
				syslog(LOG_INFO, 'unsetting existing slug name non festival: '.$full_slug_name_non_festival);
				unset($current_names[$full_slug_name_non_festival]);
			}
		}
	}
	unset($info);

	# new slugs:
	$newlist = [];
	# updated partyid slugs:
	$updlist = [];
	# updated name slugs:
	$namlist = [];
	# updated events
	$eventlist = [];

	foreach ($current_names as $slug_name => $info) {
		extract($info);

		$current_partyid = $existing_names[$slug_name] ?? null;

		$slug_slashed = addslashes($slug_name);

		if (!$current_partyid) {
			$existing_slugid = db_single('stable_slug', '
				SELECT SLUGID
				FROM stable_slug
				WHERE PARTYID = '.$NEAREST_PARTYID
			);
			if ($existing_slugid === false) {
				return false;
			}
			if ($existing_slugid) {
				$namlist[$existing_slugid] = '('.$existing_slugid.', "'.$slug_slashed.'", '.CURRENTSTAMP.', '.$NEAREST_PARTYID.')';
				continue;
			}

			$uselist = 'newlist';

		} elseif ($current_partyid !== $NEAREST_PARTYID) {
			$uselist = 'updlist';

			# old event has new url, so it has changed:
			$eventlist[$current_partyid] = $current_partyid;

		} else {
			continue;
		}

		$$uselist['"'.$slug_slashed.'"'] = '("'.$slug_slashed.'", '.CURRENTSTAMP.', '.$NEAREST_PARTYID.')';

		# new event has new url, so has changed:
		$eventlist[$NEAREST_PARTYID] = $NEAREST_PARTYID;
	}

	if (DEBUG) {
		foreach ([
			'newlist'	=> 'inserting %COUNT% new slugs',
			'namlist'	=> 'updating %COUNT% new names for slugs',
			'updlist'	=> 'updating %COUNT% updated slugs',
			'eventlist'	=> 'updating %COUNT% updated events',
		] as	$use_list	=> $desc) {

			$count = count($$use_list);
			syslog(LOG_INFO, str_replace('%COUNT%', $count, $desc));
			if (!$count) {
				continue;
			}
			if (DEBUG) {
				syslog(LOG_DEBUG, $use_list.': '.var_get($$use_list));
			}
		}
	}

	if (DRYRUN) {
		return true;
	}

	if ($newlist) {
		if (!db_insert('stable_slug', '
			INSERT INTO stable_slug (NAME, MSTAMP, PARTYID)
			VALUES '.implode(', ', $newlist))
		) {
			return false;
		}
	}
	if ($namlist) {
		if (!db_insert('stable_slug_log', '
			INSERT INTO stable_slug_log (SLUGID, NAME, MSTAMP, PARTYID, DSTAMP)
			SELECT SLUGID, NAME, MSTAMP, PARTYID, '.CURRENTSTAMP.'
			FROM stable_slug
			WHERE SLUGID IN ('.implodekeys(', ', $namlist).')')
		||	!db_replace('stable_slug', '
			REPLACE INTO stable_slug (SLUGID, NAME, MSTAMP, PARTYID)
			VALUES '.implode(', ', $namlist))
		) {
			return false;
		}
	}
	if ($updlist) {
		if (!db_insert('stable_slug_log', '
			INSERT INTO stable_slug_log (SLUGID, NAME, MSTAMP, PARTYID, DSTAMP)
			SELECT SLUGID, NAME, MSTAMP, PARTYID, '.CURRENTSTAMP.'
			FROM stable_slug
			WHERE NAME IN ('.implodekeys(', ', $updlist).')')
		||	!db_replace('stable_slug', '
			REPLACE INTO stable_slug (NAME, MSTAMP, PARTYID)
			VALUES '.implode(', ', $updlist))
		) {
			return false;
		}
	}
	if ($eventlist) {
		page_changed('party', $eventlist);
	}
	return true;
}
