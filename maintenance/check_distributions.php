#!/usr/bin/php
<?php

define('CURRENTSTAMP',time());
define('DEBUG',!isset($_SERVER['CRON']));

require_once '/home/<USER>/public_html/_exit_if_readonly.inc';
require_once '/home/<USER>/public_html/_db.inc';
require_once '/home/<USER>/public_html/defines/ad.inc';
require_once '/home/<USER>/public_html/_runonce.inc';

ob_implicit_flush(true);
$rc = main();
exit(show_messages_cli() || !$rc ? 1 : 0);

function main() {
	$rc = true;
	foreach (['ad','newsad'] as $element) {
		$base = $element == 'ad' ? null : 'NEWS';
		$bads = db_simpler_array([$element,'distribution_'.$element],'
			SELECT '.$base.'ADID
			FROM '.$element.' AS base
			LEFT JOIN distribution_'.$element.' AS d USING ('.$base.'ADID)
			WHERE ISNULL(d.'.$base.'ADID)
			  AND !KEEPTOP
			  AND !REMOVED
			  AND IMPRESSIONSDONE!=base.IMPRESSIONS
			  AND ACTIVE
			  AND STOPSTAMP>'.CURRENTSTAMP.
			($element == 'ad' ? ' AND POSITION!='.ADPOS_PARTYLIST : null)
		);
		if (!$bads) {
			if ($bads === false) {
				return false;
			}
			continue;
		}
		$rc = false;
		foreach ($bads as $id) {
			error_log('bad distribution for '.$element.': https://vip.partyflock.nl/'.$element.'/'.$id);
		}
	}
	return $rc;
}
