#!/usr/bin/php
<?php

define('CURRENTSTAMP',		time());
define('TODAYSTAMP',		mktime(0,0,0));
define('DEBUG',				!isset($_SERVER['CRON']) && empty($_SERVER['LOCAL_PART']));

const CURRENTUSERID		= 0;
const CURRENTLANGUAGE	= 'nl';        # for shoplink
const DRYRUN			= false;

ini_set('default_socket_timeout', 300);

# want:		title, start, location, city
# optional:	flyer, prices, country

chdir(__DIR__);
require_once __DIR__.'/../public_html/_exit_if_readonly.inc';
require_once __DIR__.'/../config/phpclasses/warn.php';
require_once __DIR__.'/../public_html/_db.inc';
require_once __DIR__.'/../public_html/_helper.inc';
require_once __DIR__.'/../public_html/_date.inc';
require_once __DIR__.'/../public_html/_sort.inc';
require_once __DIR__.'/../public_html/_presale.inc';
require_once __DIR__.'/../public_html/_uploadimage.inc';
require_once __DIR__.'/../public_html/_run_and_exit.inc';
require_once __DIR__.'/../public_html/defines/appic.inc';
require_once __DIR__.'/_partymatch.inc';

const TICKET_PROVIDERS = [
	# 2023-05-27 feed gone bad, does not return any City field anymore
	# 'yourticketprovider'	=> null,
	'ikbenaanwezig'		=> null,
	'appic'				=> null,
	'ticketswap'		=> null,
];

const LIBXML_FLAGS = LIBXML_NOBLANKS | LIBXML_NOCDATA | LIBXML_COMPACT;

run_and_exit(run_once: true, syslog: true);

function main(): bool {
	$upds = [];
	$bads = 0;

	global $argv;
	$single = $argv[1] ?? null;

	$result = [];

	if ($single) {
		if (!array_key_exists($single, TICKET_PROVIDERS)) {
			syslog_error(LOG_ERR, "unknown ticket provider: $single");
			return false;
		}
		$func = 'lookup_'.$single;
		if (!($result[$single] = $func($upds, $single))) {
			syslog_error(LOG_ERR, "something went wrong $func");
			++$bads;
		}
	} else {
		# do appic only when explicitly called
		foreach (array_diff_key(
			TICKET_PROVIDERS, [
				'appic'			=> null,
				'ticketswap'	=> null
		]) as $type => $ignored) {
			$func = 'lookup_'.$type;
			if (!($result[$type] = $func($upds, $type))) {
				syslog_error(LOG_ERR, "something went wrong $func");
				++$bads;
			}
		}
	}

	if ($upds && !process_updates($upds)) {
		++$bads;
	}

	check_for_removes($single);

	if ($result) {
		foreach ($result as $type => $cnts) {
			if (!$cnts) {
				++$bads;
				continue;
			}
			[$match_count, $update_count] = $cnts;

			syslog(LOG_INFO, "$type: done with $match_count matches, $update_count updates");
		}
	}
	return !$bads;
}

function check_for_removes($single = null): bool {
	global $__done_party, $__have_id;
	if ($single) {
		if (!($keep = getifset($__done_party,$single))) {
			return false;
		}
		$__done_party = [$single => $keep];
	}

	if (!$__done_party) {
		return false;
	}

	# don't check ticketswap. URLs are trustworthy and when event is done,
	# there is no use for them anyway, no need to check then either.
	unset($__done_party['ticketswap']);

	require_once __DIR__.'/../public_html/_shoplink.inc';
	foreach ($__done_party as $type => $ids) {
		switch ($type) {
		case 'ticketswap':
		case 'ikbenaanwezig':
			$attrib = strtoupper(str_replace('-','',$type));
			$where = ' (NOT ISNULL('.$attrib.') AND '.$attrib.'!="") ';
			break;
		default:
			$attrib = strtoupper(str_replace('-','',$type));
			$where = $attrib;
			break;
		}

		$removes = db_simple_hash(['presaleinfo','party'],"
			SELECT PARTYID, '.$attrib.'
			FROM presaleinfo
			JOIN party USING (PARTYID)
			WHERE $where
			  AND CANCELLED = 0
			  AND MOVEDID = 0
			  AND NOT SOLD_OUT
			  AND PARTYID NOT IN (".implodekeys(',',$ids).'
			  AND party.STAMP > UNIX_TIMESTAMP()'
		);

		syslog(LOG_DEBUG, 'remove these maybe: '.var_get($removes));

		if (!$removes) {
			continue;
		}

		$ch = curl_init();
		lookup_tickets_setopts_ch($ch);

		$do_HEAD = $is_ticketswap = ($type === 'ticketswap');

		curl_setopt($ch, CURLOPT_NOBODY, $do_HEAD);
		curl_setopt($ch, CURLOPT_HTTPGET, !$do_HEAD);

		$last_done = 0;

		foreach ($removes as $partyid => $val) {
			$link = get_shop_url($type, $partyid, add_utm: false);

			if ($is_ticketswap) {
				$diff = microtime(true) - $last_done;
				$wait = $diff > 2 ? 0 : (2 - $diff);

				if ($wait) {
					usleep($wait * 1000000);
				}
			}

			curl_setopt($ch, CURLOPT_URL, $link);

			$data = curl_exec($ch);

			if ($is_ticketswap) {
				$last_done = microtime(true);
			}

			$info = curl_getinfo($ch);

			$real_bad = false;
			$ignore_bad = false;

			# real_bad precedes ignore_bad

			switch ($type) {
			case 'ticketmaster':
				$real_bad =
					str_starts_with($info['url'], 'http://www.ticketmaster.nl/index.php'); # redirected to index (no sale)

				$ignore_bad = !$real_bad;		# assume still valid if not really bad

				break;

			case 'ikbenaanwezig':
				$real_bad = $info['url'] === 'https://shop.ikbenaanwezig.nl/tickets';	# redirect to main
				$ignore_bad =
					str_contains($data, 'Dit event is priv')
					#	page exists at url ===link:
					||	$info['http_code'] === 200
					&&	$info['url'] 	   === $link;
				break;

			default:
				$real_bad =
					$info['http_code'] !== 200
				&&	$info['http_code'] !== 504;	# gateway timeout
				break;
			}
			if ($real_bad) {
				$real_removes[$partyid][$link] = $attrib.'=NULL';
			} elseif (
				$type !== 'timoco'
			&&	$type !== 'ticketswap'
			&&	!$ignore_bad
			&&	!isset($__have_id[$type][$val])
			) {
				# presale was stored, but not found in current feed, and seems ok
				$keep_these[$type][$partyid] = ['https://vip.partyflock.nl/party/'.$partyid, $link];
			}
		}
		curl_close($ch);
	}
	if (isset($real_removes)) {
		if (!db_insert('presaleinfo_log', '
			INSERT INTO presaleinfo_log
			SELECT * FROM presaleinfo
			WHERE PARTYID IN ('.implodekeys(',',$real_removes).')')
		) {
			return false;
		}
		foreach ($real_removes as $partyid => $attribs) {
			if (!db_update('presaleinfo','
				UPDATE presaleinfo SET
					MUSERID	= 0,
					MSTAMP	= UNIX_TIMESTAMP(),
					'.implode(',',$attribs).'
				WHERE PARTYID='.$partyid)
			) {
				return false;
			}
		}
	}
	if (isset($keep_these)) {
		print_rr($keep_these, 'keep_these');
	}
	if (isset($real_removes)) {
		print_rr($real_removes, 'real_removes');
	}
	return true;
}

function process_updates(array &$upds): bool {
		$stored_image = db_simple_hash(['uploadimage_link','uploadimagemeta'],"
		SELECT DISTINCT ID, COUNT(DISTINCT TYPE),
				(	SELECT WIDTH * HEIGHT
					FROM uploadimagemeta AS m
					WHERE m.UPIMGID = l.UPIMGID
					ORDER BY WIDTH DESC
					LIMIT 1
				)
		FROM uploadimage_link AS l
		WHERE TYPE IN ('party', 'party2')
		  AND ID IN (".implodekeys(',',$upds).')
		GROUP BY ID'
	);
	if ($stored_image === false) {
		return false;
	}
	foreach ($upds as $partyid => $list) {
		$setlist = $party_setlist = $party_price_setlist = [];
		foreach ($list as $name => $val) {
			switch ($name) {
			case 'PAYLOGIC':
			case 'PAYLOGICV2':
				$setlist[$name] = $name.'='.($val === null ? 'NULL' : '"'.addslashes($val).'"');
				$setlist['PAYLOGICPOS'] = 'PAYLOGICPOS=0';
				break;

			case 'APPIC_ACTIVE':
			case 'APPIC_SOLD_OUT':
				$setlist[$name] = $name.'='.($val ? "b'1'" : "b'0'");
				break;

			case 'APPIC':
				# UUID cannot be inserted with binary charset, which Partyflock currently sets after connecting to database
				$setlist[$name] = $name.' = '.($val === null ? 'NULL' : '_ascii "'.addslashes($val).'"');
				break;

			default:
				$setlist[$name] = $name.'='.($val === null ? 'NULL' : '"'.addslashes($val).'"');
				break;
			}
		}
		if ($setlist) {
			if (!db_insert('presaleinfo_log','
				INSERT INTO presaleinfo_log
				SELECT * FROM presaleinfo
				WHERE PARTYID='.$partyid)
			||	!db_insupd('presaleinfo',"
				INSERT INTO presaleinfo SET
					PARTYID	= $partyid,
					ADDR	= '',
					MUSERID	= 0,
					MSTAMP	= UNIX_TIMESTAMP(),".
					($setstr = implode(', ', $setlist)).
					(!isset($setlist['WEBSITE']) ? ', WEBSITE = ""' : '').'
				ON DUPLICATE KEY UPDATE
					MUSERID	= 0,
					MSTAMP	= VALUES(MSTAMP),'.
					$setstr)
			) {
				return false;
			}
			memcached_delete('presaleinfo_'.$partyid);
		}
		if ($party_price_setlist
		&&	!db_insert('partyprice','
			INSERT INTO partyprice (CURRENCY, PARTYID, PRICE, TYPE, VIP, EARLY_BIRD, MSTAMP, MEMBERSHIP)
			VALUES '.implode(', ', $party_price_setlist))
		) {
			return false;
		}
		if ($party_setlist) {
			if (!db_insert('party_log','
				INSERT INTO party_log
				SELECT * FROM party
				WHERE NOT '.binary_equal($party_setlist).'
				  AND PARTYID = '.$partyid)
			||	db_affected()
			&&	!db_update('party','
				UPDATE party SET
					MUSERID	= 0,
					MSTAMP	= UNIX_TIMESTAMP(),'.
					implode(', ' , $party_setlist)."
				WHERE PARTYID = $partyid")
			) {
				return false;
			}
		}
	}
	return true;
}

function lookup_ticketmaster(&$upds) {
	syslog(LOG_DEBUG, 'lookup ticketmaster');

	$totalmatchcnt = $totalupdatecnt = 0;
	$ok = false;
	$rc = lookup_ticketmaster_xml($upds);
	if ($rc) {
		[$matchcnt,$updatecnt] = $rc;
		$totalmatchcnt += $matchcnt;
		$totalupdatecnt += $updatecnt;
		$ok = true;
	}

	return $ok ? [$totalmatchcnt,$totalupdatecnt] : false;
}

/*function lookup_ticketmaster_xml(&$upds) {
	[$y,$m,$d] = _getdate();

#	see also ticket https://vip.partyflock.nl/ticket/1381219 for feed on ticketmaster.nl
#	$feed_url = 'http://www.ticketmaster.nl/feed/eventFeed.php?FROMDATE='.sprintf('%04d-%02d-%02d',$y,$m,$d).'&TODATE=2030-12-31&FORMAT=xml&LANGUAGE=nl-nl&partnerID=35109125C1743396712T';

	$feed_url = 'http://df.zanox.com/download/stream-2000920-1858386.xml?dfid=2000920&asid=1858386&m=xml_tree&itid=3154&token=013A877476DBF2ABAF9A34243C8B3552CDAC20C4A579E692A9A0BD747048890C87BEB2';

	$data = safe_file_get_contents($feed_url,[
#		CURLOPT_USERPWD	=> 'NLEventFeed:NLEventFeed10',
	]);

	if (!$data) {
		syslog_error(LOG_ERR, 'ticketmaster_xml: no xml data');
		return false;
	}

	if (!defined('DRYRUN') || !DRYRUN) {
		db_insert('ticketfeed','INSERT INTO ticketfeed SET TYPE="ticketmaster",BODY="'.addslashes($data).'",STAMP='.time());
	}
	$feed = simplexml_load_string($data,'SimpleXMLElement', LIBXML_FLAGS);

	if (!$feed) {
		syslog_error(LOG_ERR, 'ticketmaster_xml: failed parse xml');
		return false;
	}

	if (empty($feed->data->record)) {
		return null;
	}
	$updatecnt = $matchcnt = 0;
	foreach ($feed->data->record as $event) {
		$eventid = $event->offerid;
		$title = $event->title;
		$stamp_tzi = strtotime($event->date);
		$onsale_tzi = strtotime($event->date_from);
		$venue = $event->destination_name;
		$city = $event->city;

		$GLOBALS['__haveid']['ticketmaster'][(string)$eventid] = true;
		if (DEBUG) {
			echo 'ID: ',$eventid,', ';
		}
		if ($party = check_party($upds,
			'ticketmaster',
			$title,
			0,
			$stamp_tzi,
			$venue,
			$city)
		) {
			$partyid = $party['PARTYID'];
			if ($partyid != 278986) {
				if ($eventid != $party['TICKETMASTER']) {
					$upds[$partyid]['TICKETMASTER'] = $eventid;
					++$updatecnt;
				}
				if (!$party['PRESALE_STAMP']) {
					$upds[$partyid]['PRESALE_STAMP'] = $onsale_tzi;
				}
				++$matchcnt;
			}
		}
	}
	return [$matchcnt,$updatecnt];
}
function lookup_yourticketprovider(array &$upds): array|false {
	syslog(LOG_DEBUG, 'lookup yourticketprovider');

	$content = safe_file_get_contents('https://www.yourticketprovider.nl/feeds/partyflock.aspx',[
		CURLOPT_TIMEOUT	=> FIVE_MINUTES
	]);

	if (!$content) {
		$cnt = memcached_increment('lookup_tickets:yourticketprovider:no_data',1,1,ONE_DAY);
		if ($cnt === 4) {
			syslog_error(LOG_ERR, 'yourticketprovider: no data for the 4th time in 24 hours');
		}
		return false;
	}
	if (false !== strpos($content,'">Oeps!</h1>')) {
		# 500 status
		return false;
	}
	if (!defined('DRYRUN') || !DRYRUN) {
		db_insert('ticketfeed','INSERT INTO ticketfeed SET TYPE="yourticketprovider",BODY="'.addslashes($content).'",STAMP='.time());
	}
	# fix bad entities:
	$content = str_ireplace('&#x3;',null,$content);
	$content = str_ireplace('&#xb;',null,$content);

	$feed = simplexml_load_string($content,'SimpleXMLElement', LIBXML_FLAGS);
	if (!$feed) {
		syslog_error(LOG_ERR, 'yourticketprovider: failed parse xml');
		return false;
	}
	if (!property_exists($feed,'Events')
	||	!property_exists($feed->Events,'Event')
	) {
		return [0,0];
	}
	$updatecnt = $matchcnt = 0;

	foreach ($feed->Events->Event as $event) {
		if (empty($event->Url)) {
			syslog_warning(LOG_WARNING, 'yourticketprovider: no Url found', item: $event);
			continue;
		}
		if (!preg_match('"productId=(?P<productId>[\da-f\-]+)"', $event->Url, $idmatch)) {
			syslog_warning(LOG_WARNING, 'yourticketprovider: event not understood', item: $event);
			continue;
		}
		$id = $idmatch[1];
		if (empty($event->Start)
		||	empty($event->End)
		||	empty($event->Name)
		) {
			syslog_warning(LOG_WARNING, 'yourticketprovider: event date and name not understood', item: $event);
			continue;
		}

		if (empty($event->Location)
#		||	empty($event->Location->Address)
#		||	empty($event->Location->City)
		) {
			syslog_warning(LOG_WARNING, 'yourticketprovider: event location not understood', item: $event);
			continue;
		}

		if (DEBUG) {
			echo 'ID: ',$id,', ';
		}

		$GLOBALS['__haveid']['yourticketprovider'][$id] = true;

		change_timezone('UTC');
		$start = strtotime($event->Start);
		$end = strtotime($event->End);
		change_timezone();

		$tz = db_single('city','
			SELECT TIMEZONE
			FROM city
			JOIN country USING (COUNTRYID)
			WHERE SHORT="'.addslashes($event->Location->Country).'"
			  AND city.NAME="'.addslashes($event->Location->City).'"'
		);
		if (!$tz) {
			$tz = db_single('city','
				SELECT TIMEZONE
				FROM city
				JOIN country USING (COUNTRYID)
				JOIN location USING (CITYID)
				JOIN party USING (LOCATIONID)
				WHERE SHORT="'.addslashes($event->Location->Country).'"
				  AND STAMP>'.(TODAYSTAMP - 6 * ONE_MONTH).'
				GROUP BY TIMEZONE
				ORDER BY COUNT(*) DESC
				LIMIT 1'
			);
			if (!$tz) {
				$tz = 'Europe/Amsterdam';
			}
		}
		change_timezone($tz);
		[$y, $m, $d, $hr, $mn] = _getdate($start);
		change_timezone();

		# $stamp_tzi = strtotime($y.'-'.$m.'-'.$d.' '.$hr.':'.$mn.' GMT');
		# $duration = $end - $start;

		if ($party = check_party($upds,
			'yourticketprovider',
			$event->Name,
			$start,	# stamp
			null,	# stamp_tzi
			$event->Location->Address 	?? null,
			$event->Location->City		?? null
		)) {
			$partyid = $party['PARTYID'];
			if ($id !== $party['YOURTICKETPROVIDER']) {
				$upds[$partyid]['YOURTICKETPROVIDER'] = $id;
				++$updatecnt;
			}
			++$matchcnt;
		}
	}
	return [$matchcnt, $updatecnt];
}

function lookup_ikbenaanwezig(&$upds) {
	syslog(LOG_DEBUG, 'lookup ikbenaanwezig');

	$content = safe_file_get_contents('https://shop.ikbenaanwezig.nl/');
	$empty_key = 'ikbenaanwezig_empty';
	if (!$content) {
		if (memcached_increment($empty_key, 1, 1, ONE_DAY) > 10) {
			syslog_error(LOG_ERR, 'ikbenaanwezig: no data');
		}
		return false;
	}
	memcached_delete($empty_key);

	if (!defined('DRYRUN') || !DRYRUN) {
		db_insert('ticketfeed','INSERT INTO ticketfeed SET TYPE="ikbenaanwezig",BODY="'.addslashes($content).'",STAMP='.time());
	}
#	if (!preg_match_all('"<a href=\"(https://shop\.ikbenaanwezig\.nl/tickets/event/(.*?))\">.*?</a>\s*</h2>\s*<p>(.*?),(.*?)</p>"i',$content,$matches)) {
	if (!preg_match_all('!data-id="(\d+)"\s+data-slug="(https://shop\.ikbenaanwezig\.nl/tickets/event/(.*?))"!is', $content, $matches)) {
		syslog_error(LOG_ERR, 'ibenaanwezig: no matches');
		return false;
	}
	$matchcnt = $updatecnt = 0;
	foreach ($matches[2] as $i => $url) {
		if (false !== stripos($url, 'stand-up-comedy')
		||	false !== stripos($url, '/SPLASH-Nijmegen')			# multi event, not useful
		||	false !== stripos($url, '/healinggarden')			# no datematch (not useful anyway)
		||	false !== stripos($url, '/kiekopdekaaij')			# multiple events (maybe ignore /kiosk/ ? not sure if kiosk never has 1 date)
		||	false !== stripos($url, '/stampuh-15-12-2017')		# sold out
		||	false !== stripos($url, '/patroon-festival')		# no presale here anymore
		||	false !== stripos($url, 'busretour')				# skip travel
		||	false !== stripos($url, 'testival')					# test
		||	false !== stripos($url, 'bingo-mgorganisation-')	# no location
		||	false !== stripos($url, 'test-12-12-2020')			# no location
		||	false !== stripos($url, '/all-in-membership')		# no datematch
		||	false !== stripos($url, '/explore-membership')		# no datematch
		||	false !== stripos($url, '/membership-femmes')		# no datematch
		||	false !== stripos($url, '/dickens-op-het-kasteel')	# no datematch
		) {
			continue;
		}
		$content = safe_file_get_contents($url,null,$info);
		if (!$content) {
			syslog(LOG_WARNING, "ikbenaanwezig: no content at $url");
			continue;
		}
  		if ($info['url'] == 'https://shop.ikbenaanwezig.nl/tickets') {
			# redirected to main, page does not exist
			continue;
		}
		if (false !== strpos($content,'Er zijn geen evenementen')) {
			# no presale yet supposedly
			continue;
		}
//		if (!preg_match('"<h1>(.*?)</h1>\s*<h3>\s*(\d{1,2})\-(\d{1,2})\-(\d{4})"is',$content,$datematch)) {
//			if (false !== strpos($content,'>Tickets<')) {
//				# No 'Tickets' header? Then probably no sale yet
//				$kiosk = false !== strpos($url,'/kiosk/');
//				error_log('ikbenaanwezig'.($kiosk ? ' (kiosk)' : null).': no datematch at '.$url);
//			}
//			# just ignore
//			continue;
//		}
//		list(,,$d,$m,$y) = $datematch;
//		$hour = 22;
//		$mins = 0;

		$kiosk = false;
		if (preg_match('"<span>Start: (\d+)-(\d+)-(\d+) (\d+):(\d+)"is',$content,$datematch)) {
			[,$d,$m,$y,$hour,$mins] = $datematch;
		} elseif (preg_match('"<h3>\s*(\d{1,2})\-(\d{1,2})\-(\d{4})"is',$content,$datematch)) {
			[,$d,$m,$y] = $datematch;
			$hour = 22;
			$mins = 0;
		} elseif (preg_match('"<h3>\s*(\d{1,2})\h+(\w+)\h+(\d{4})\h+(\d+):(\d+)"is',$content,$datematch)) {
			[,$d,$m,$y,$hour,$mins] = $datematch;
			$m = get_month_from_string($m);
		} else {
			syslog(LOG_WARNING,'ikbenaanwezig'.($kiosk ? ' (kiosk)' : null).": no datematch at $url");
			continue;
		}

		if (preg_match('!class="icon-pin-2"></i>(.*?)</p>!is',$content,$locationmatch)) {
			$location = mytrim($locationmatch[1]);
		} else {
			$location = null;
		}
		if (preg_match('!class="icon-pin-1"></i>\s*(?:<a..*?>)?(?:(?:.*?),)?(.*?)</[pa]>!is',$content,$citymatch)) {
			$city = mytrim($citymatch[1]);
		} else {
			$city = null;
		}

		if (preg_match('"facebook\.com/events/(\d+)"',$content,$fbmatch)) {
			$party = db_single_assoc(['party','fbid'],'
				SELECT STAMP,STAMP_TZI,DURATION_SECS
				FROM party
				JOIN fbid ON ID=PARTYID AND ELEMENT="party"
				WHERE FBID='.$fbmatch[1]
			);
			if ($party) {
				change_timezone('UTC');
				[$py,$pm,$pd,$phour,$pmins] = _getdate($party['STAMP_TZI']);
				change_timezone();

				if ($y == $py
				&&	$m == $pm
				&&	$d == $pd
				) {
					$hour = $phour;
					$mins = $pmins;
				}
			}
		}

		if (!preg_match('"<h1><span>(.*?)</span></h1>"is',$content,$titlematch)) {
			syslog(LOG_WARNING, "ikbenaanwezig: no titlematch @ $url");
			continue;
		}
		$name = $titlematch[1];
		$id = $matches[3][$i];

		$GLOBALS['__haveid']['ikbenaanwezig'][$id] = true;
		if (DEBUG) {
			echo 'ID: ',$id,', ';
		}

#		$location = $matches[3][$i];
#		$city = mytrim($matches[4][$i]);

		$stamp_tzi = strtotime($y.'-'.$m.'-'.$d.' '.sprintf('%02d:%02d',$hour,$mins).' GMT');

		$duration = 5 * 3600;

		$pricestr = '';

		$partyid = 0;
		if ($party = check_party($upds,
			'ikbenaanwezig',
			$name,
			null,		# stamp
			$stamp_tzi,	# stamp_tzi
			$location,
			$city,
		)) {
			$partyid = $party['PARTYID'];
			if ($id != $party['IKBENAANWEZIG']) {
				$upds[$partyid]['IKBENAANWEZIG'] = $id;
				++$updatecnt;
			}
			++$matchcnt;
		}
	}
	return [$matchcnt,$updatecnt];
}*/

function lookup_ticketswap(array &$upds): array|false {
	syslog(LOG_DEBUG, 'lookup ticketswap');

	$url = 'https://ticketswap.eu.looker.com/looks/ZcszmKh5sT9g45K9yKQw6fX6NVScZNJf.json';//?apply_formatting=true&apply_vis=true';
	#$url = 'ZcszmKh5sT9g45K9yKQw6fX6NVScZNJf.json';

	syslog(LOG_DEBUG, "fetching $url");

	if (!($content = file_get_contents($url)))  {
		syslog_error(LOG_ERR, "ticketswap: failed to get data from $url");
		return false;
	}

	if ((!defined('DRYRUN') || !DRYRUN)
	&&	!db_insert('ticketfeed', '
		INSERT INTO ticketfeed SET
			TYPE	= "ticketswap",
			BODY	= "'.addslashes($content).'",
			STAMP	='.time())
	) {
		return false;
	}

	$entries = safe_json_decode($content, true);

	$done = [];

	$match_count = $updatecnt = 0;

	foreach ($entries as $entry) {
		# 'dim_events.event_title' => string(39) "Chinastraat NYE - Exit23 - Space Safari"
		# 'dim_events.event_start_date' => string(10) "2023-12-31"
		# 'dim_events.event_country_JA:Fname' => string(7) "Belgium"
		# 'dim_venues.city_name' => string(5) "ghent"
		# 'dim_venues.venue_name' => string(11) "Chinastraat"
		# 'dim_events.event_frontend_url_dynamic_domain' => string(104) "https://www.ticketswap.nl/event/chinastraat-nye-exit23-space-safari/c65fc894-8f38-400a-a3c3-cf13fefdac2c"
		# 'dim_event_types.event_type_available_ticket_count' => int(0)
		# 'dim_event_types.event_type_average_ticket_price' => NULL

		if (empty($entry['dim_events.event_frontend_url_dynamic_domain'])) {
			syslog_warning(LOG_WARNING, 'dim_events.event_frontend_url_dynamic_domain', item: $entry);
			continue;
		}

		$date	  = $entry['dim_events.event_start_date']					?? null;
		$name	  = $entry['dim_events.event_title']						?? null;
		$location = $entry['dim_venues.venue_name']							?? null;
		$city	  = $entry['dim_venues.city_name']							?? null;
		$url	  = $entry['dim_events.event_frontend_url_dynamic_domain']	?? '';

		foreach (['url', 'name', 'date', 'city'] as $required_field) {
			if (!$$required_field) {
				syslog(LOG_DEBUG, "no $required_field, skipping");
				continue 2;
			}
		}

		syslog(LOG_DEBUG, "got url: $url");

		if (!preg_match('"^https://.*?/(?<id>[a-f\d]{8}-[a-f\d]{4}-[a-f\d]{4}-[a-f\d]{4}-[a-f\d]{12})$"i', $url, $match)) {
			syslog_error(LOG_ERR, 'could nog parse id from url');
			continue;
		}

		$id = $match['id'];

		syslog(LOG_DEBUG, "id from url: $id");

		if ($url === 'https://www.ticketswap.nl/event/toppers-in-concert/3c516044-ac5a-4b22-8e41-f2324e97e3e1') {
			syslog(LOG_DEBUG, 'found toppers url');
			$url = '3c516044-ac5a-4b22-8e41-f2324e97e3e1.html';
			if (($data = safe_file_get_contents($url))
			&&	preg_match_all('!<script type="application/ld\+json">(?<schema_json>.*?)</script>!uis', $data, $matches)
			) {
				foreach ($matches['schema_json'] as $json) {
					if (!($schema = json_decode($json, true))) {
						syslog_warning(LOG_WARNING, 'failed to decode ticketswap entry');
						continue;
					}
					if (!isset($schema['@type'])
					||	$schema['@type'] !== 'Event'
					) {
						continue;
					}
					print_rr($schema);
				}
			}
			exit;
		}

		$date .= ' 18:00';
		$name = preg_replace('"(/w .*)$"ui', '', $name);

		change_timezone('UTC');
		$stamp_tzi = strtotime($date);
		change_timezone();

		if ($party = check_party($upds,
			'ticketswap',
			$name,
			null,		# stamp
			$stamp_tzi,	# stamp_tzi
			$location,
			$city
		)) {
			++$match_count;
			$partyid = $party['PARTYID'];
			if (isset($done[$partyid])) {
				continue;
			}
			$done[$partyid] = true;
			if ($party['TICKETSWAP']) {
				syslog(LOG_INFO, "there already is a TicketSwap link registered for event $partyid, skipping this one");
				continue;
			}

			$upds[$partyid]['TICKETSWAP'] = $url;
			++$updatecnt;

			if (false !== ($available = have_idnumber($entry, 'dim_event_types.event_type_available_ticket_count'))) {
				syslog(LOG_INFO, "there are $available ticket(s) left");
				if (!db_insert('ticketswapinfo_log', "
					INSERT INTO ticketswapinfo_log
					SELECT PARTYID, AVAILABLE, MSTAMP
					FROM ticketswapinfo
					WHERE AVAILABLE != $available
					  AND PARTYID    = $partyid")
				||	($affected = db_affected())
				&&	!db_update('ticketswapinfo', "
					INSERT INTO ticketswapinfo SET
						PARTYID		= $partyid,
						CHECKSTAMP	= UNIX_TIMESTAMP(),
						MSTAMP		= UNIX_TIMESTAMP(),
						AVAILABLE	= $available
					ON DUPLICATE KEY UPDATE
						MSTAMP		= IF(AVAILABLE = VALUES(AVAILABLE), MSTAMP, VALUES(MSTAMP)),
						AVAILABLE	= VALUES(AVAILABLE),
						CHECKSTAMP	= UNIX_TIMESTAMP()")
				) {
					return false;
				}
				if ($affected) {
					syslog(LOG_DEBUG, 'updated available tickets');
				}
			}
		}
	}
	if (!$match_count) {
		syslog_error(LOG_ERR, 'ticketswap: not one event matched, is feed empty?');
	}
	return [$match_count, $updatecnt];
}

function lookup_appic(array &$upds): array|false {
	syslog(LOG_DEBUG, 'lookup appic');

	$url = 'https://api.beappic.com/rest-api/v1/shops';

	$objs =
	$contents = [];

	while ($url) {
		syslog(LOG_DEBUG, "fetching $url");

		$content = safe_file_get_contents($url,
			params: [
				CURLOPT_HTTPHEADER => [
					'Authorization: Token 6c36c8b88f63fe8531236994125e025a7ef437a9758e83d99004a1a194df10771ffecf650793c08c0052a83e54941739d9dc0e30a721f82eb73316ef0576827c'
				]
			],
			info: $info
		);
		if ($info['http_code'] !== 200) {
			syslog_error(LOG_CRIT, "appic: got http status {$info['http_code']}");
			return false;
		}
		if (!$content) {
			syslog_error(LOG_CRIT, 'appic: no data');
			return false;
		}

		$contents[] = $content;

		if (!($obj = safe_json_decode($content))) {
			syslog_error(LOG_CRIT, "appic: failed to decode json data: $content");
			return false;
		}

		$objs[] = $obj;

		$url = property_exists($obj,'next') ? $obj->next : null;
	}

	$shops = [];

	foreach ($objs as $obj) {
		if (!property_exists($obj, 'results')) {
			syslog_error(LOG_CRIT, 'appic: no results field');
			return false;
		}
		foreach ($obj->results as $product) {
			$shops[] = $product;
		}
	}

	$save_content = implode('', $contents);

	if (!defined('DRYRUN') || !DRYRUN) {
		db_insert('ticketfeed', 'INSERT INTO ticketfeed SET TYPE="appic", BODY="'.addslashes($save_content).'", STAMP = UNIX_TIMESTAMP()');
	}

	$done_mains = [];
	$match_count = $updatecnt = 0;
	foreach ($shops as $shop) {
			/*class stdClass#9 (12) {
				public $status => string(6) "active"
				public $name => string(32) "Hush. · Rotterdam · Kingsnight"
				public $url_extension => string(25) "hush-rotterdam-kingsnight"
				public $uuid => string(36) "0d03cf76-2364-4cbe-94c1-954c79a4fa8c"
				public $products => array(1) {
				  [0] => class stdClass#10 (15) {
					public $uuid => string(36) "da684e2b-f264-4ce6-b296-8366b45146db"
					public $title => string(15) "Regular Tickets"
					public $event_id => int(211000)
					public $event_name => string(32) "Hush. · Rotterdam · Kingsnight"
					public $photo_url => string(136) "https://app2xpr.s3.eu-central-1.amazonaws.com/media/app-images/thumbnail/016f3e08-f051-47fa-b139-c81ce5cfa8b3.200x200_q85_crop-smart.jpg"
					public $description => NULL
					public $currency => string(3) "EUR"
					public $original_price => double(17)
					public $price => double(17)
					public $service_fee => double(2)
					public $is_sold_out => bool(false)
					public $discounted_price => NULL
					public $loyalty_points_to_gain => NULL
					public $future_prices => array(0) {
					}
					public $available_to => string(19) "2024-04-27 06:00:00"
				  }
				}
				public $event => class stdClass#11 (12) {
				  public $id => int(211000)
				  public $fb_id => string(15) "753724866305480"
				  public $event_feed_id => int(466067)
				  public $name => string(32) "Hush. · Rotterdam · Kingsnight"
				  public $start => string(19) "2024-04-26 23:00:00"
				  public $end => string(19) "2024-04-27 06:00:00"
				  public $street => string(13) "Weena Zuid 33"
				  public $city => string(9) "Rotterdam"
				  public $country => string(2) "NL"
				  public $country_short => string(2) "NL"
				  public $latitude => double(51.************)
				  public $longitude => double(4.************)
				}
				public $ui => class stdClass#12 (5) {
				  public $logo_image => string(136) "https://app2xpr.s3.eu-central-1.amazonaws.com/media/app-images/thumbnail/016f3e08-f051-47fa-b139-c81ce5cfa8b3.200x200_q85_crop-smart.jpg"
				  public $background_image => string(112) "https://app2xpr.s3.eu-central-1.amazonaws.com/media/app-images/original/ff37b57d-4270-4236-91fd-16f2092e4117.jpg"
				  public $background_type => string(5) "event"
				  public $background_color => NULL
				  public $color_primary => string(7) "#ff0059"
				}
				public $server_time => string(19) "2024-03-08 01:48:00"
				public $available_from => string(19) "2024-03-07 17:03:37"
				public $available_to => string(19) "2024-04-27 06:00:00"
				public $description => NULL
				public $shop_url => string(82) "https://appic.shop/redirect/0d03cf76-2364-4cbe-94c1-954c79a4fa8c?source=partyflock"
			  }*/

		if (!property_exists($shop, 'uuid')) {
			syslog_error(LOG_CRIT, 'no uuid found in shop', item: $shop);
			return false;
		}
		switch ($shop->uuid) {
		case 'e66b2277-d37b-478a-a6c9-04aa4246d781':	# bloedmaan
		case '25c8e7fb-458e-4ca5-8297-b77146315bc7':	# old bad event
		case '36682a88-4f54-4a08-a812-fd067106420c':	# old bad event
			#error_log('appic: bad uuid: '.$shop->uuid);
			continue 2;
		}
		if (!property_exists($shop, 'event')
		||	!property_exists($shop, 'status')
		||	!property_exists($shop, 'url_extension')
		||	!property_exists($shop->event, 'event_feed_id')
		||	!$shop->event->event_feed_id
		) {
			syslog_error(LOG_CRIT, 'no event found in shop', item: $shop);
			return false;
		}
		if (property_exists($shop->event, 'end')) {
			$end_stamp = strtotime($shop->event->end);
			if ($end_stamp && $end_stamp < CURRENTSTAMP) {
				syslog(LOG_INFO, 'event is in the past ('.$shop->event->end.'), skipping');
				continue;
			}
		}

		$active = property_exists($shop, 'products') && $shop->status === 'active';

		$not_sold_out = 0;

		if ($active) {
			foreach ($shop->products as $product) {
				if (property_exists($product, 'is_sold_out')
				&&	!$product->is_sold_out
				) {
					++$not_sold_out;
				}
			}

		}

		$sold_out = !$not_sold_out;

		$id = $shop->uuid;

		$GLOBALS['__haveid']['appic'][$shop->uuid] = true;
		if (DEBUG) {
			echo 'ID: ',$id, ' == ',$shop->url_extension,"\n";
		}

		if (!property_exists($shop, 'products')
		||	empty($shop->products)
		) {
			$active = false;
			# ignore shop with no products
			syslog(LOG_WARNING, 'ignored due to no products');
		}

		$partyid = $shop->event->event_feed_id;

		syslog(LOG_DEBUG, "connected event: $partyid: ".memcached_party($partyid));
		syslog(LOG_DEBUG, 'active: '.($active ? 'yes' : 'no'));
		syslog(LOG_DEBUG, 'sold out: '.($sold_out ? 'yes' : 'no'));

		if (false === ($mainid = db_single('party', /** @lang MariaDB */ '
			SELECT '.PARTY_MAIN_ID_INC."
			FROM party
			WHERE PARTYID = $partyid"))
		||	false === ($current_APPIC = db_single('presaleinfo', /** @lang MariaDB */ '
			SELECT APPIC
			FROM presaleinfo
			WHERE PARTYID IN ('.$partyid.', '.($mainid ?: 0).')'))
		) {
			return false;
		}
		if ($current_APPIC !== $id) {
			if (($have_others = db_same_hash('presaleinfo', /** @lang MariaDB */ "
				SELECT DISTINCT APPIC FROM presaleinfo_log
				WHERE PARTYID IN ($partyid, ".($mainid ?: 0).")
				  AND APPIC != ''"))
			&&	(	count($have_others) > 1
				||	!isset($have_others[$id]))
			) {
				$have_others['just_in'] = $id;
				syslog_warning(LOG_WARNING, 'There are already Appic shops active. Please check!',  $have_others);
				continue;
			}
			if (isset($done_mains[$mainid])) {
				syslog(LOG_DEBUG, "already done $mainid: {$done_mains[$mainid]}");

				if (!$active
				||	$sold_out
				) {
					syslog(LOG_DEBUG, 'ignoring this shop due to non-active or sold out');
				} else {
					syslog(LOG_DEBUG, 'overwriting due to active and not sold out');
				}
			}
		}

		$done_mains[$mainid] = $shop->url_extension;

		if ($mainid) {
			if (false === ($partyids = db_simple_hash(['party', 'connect'], /** @lang MariaDB */ '
				SELECT ASSOCID, '.PARTY_MAIN_ID_INC.'
				FROM connect
				JOIN party ON PARTYID=ASSOCID
				WHERE MAINTYPE = "party"
				  AND MAINID = '.$partyid.'
				  AND ASSOCTYPE = "party"
				  AND '.PARTY_MAIN_ID_INC.' = '.$mainid))
			) {
				return false;
			}
			$partyids ??= [];
			$partyids[$partyid] = $mainid;
		} else {
			$partyids = [$partyid => $partyid];
		}
		syslog(LOG_DEBUG, 'series: '.implodekeys(',',$partyids));

		foreach ($partyids as $partyid => /* $mainid */ $ignored) {
			if (false === ($presaleinfo = db_single_assoc('presaleinfo', '
				SELECT	APPIC,
						APPIC_ACTIVE,
						APPIC_SOLD_OUT
				FROM presaleinfo
				WHERE PARTYID = '.$partyid
			))) {
				return false;
			}

			if ($presaleinfo
			&&	(	!$active
				||	$sold_out
				)
			&&	 $id !== $presaleinfo['APPIC']
			&&	 $presaleinfo['APPIC_ACTIVE']
			&&	!$presaleinfo['APPIC_SOLD_OUT']
			) {
				syslog(LOG_DEBUG, 'ignoring updating, because we have other shop with active non sold out products');
			}

			++$match_count;
			if (!$presaleinfo
			||	$id		  !==  $presaleinfo['APPIC']
			||	$active   !== ($presaleinfo['APPIC_ACTIVE']   ? true : false)
			||	$sold_out !== ($presaleinfo['APPIC_SOLD_OUT'] ? true : false)
			) {
				if (in_array($partyid,[362387, 362386, 369588, 369589], true)) {
					#error_log('appic: skippig oops');
					continue;
				}
				$upds[$partyid]['APPIC'] = $id;
				$upds[$partyid]['APPIC_ACTIVE'] = $active;
				$upds[$partyid]['APPIC_SOLD_OUT'] = $sold_out;
				++$updatecnt;
			}
		}
	}
 	return [$match_count, $updatecnt];
}

function lookup_tickets_setopts_ch(CurlHandle $ch): void {
	require_once 'ipresolve.inc';
	curl_setopt_array($ch, [
		CURLOPT_RETURNTRANSFER	=> true,
		CURLOPT_FOLLOWLOCATION	=> true,
		CURLOPT_MAXREDIRS		=> 100,
		CURLOPT_COOKIEFILE		=> '/tmpdisk/lookup_tickets.cookies',
		CURLOPT_COOKIEJAR		=> '/tmpdisk/lookup_tickets.cookies',
		CURLOPT_COOKIESESSION	=> true,
		CURLOPT_HTTPHEADER		=> ['User-Agent: '.USER_AGENT_LINK_CHECK],
		#CURLOPT_VERBOSE		=> true,
	]);
}
