#!/usr/bin/php
<?php

define('CURRENTSTAMP',time());

require_once '/home/<USER>/public_html/_exit_if_readonly.inc';
require_once '/home/<USER>/public_html/_db.inc';
require_once '/home/<USER>/public_html/_error.inc';

ob_implicit_flush(true);

main();
show_messages_cli();

function main() {
	$milestone = 1;
	if (file_exists($milliontmp = '/tmp/__milestone_userid_'.$milestone)) {
		return;
	}
	$millions = $milestone * 1000000;
	#$millions = db_single('user_account','SELECT MAX(USERID) FROM user_account') + 0;
	$millionusers = db_simpler_array('user_account','SELECT SQL_NO_CACHE USERID FROM user_account WHERE USERID BETWEEN '.($millions-100+1).' AND '.$millions.' ORDER BY USERID DESC',DB_USE_MASTER);
	if (!$millionusers) {
		return;
	}
	$size = count($millionusers);
	if ($size) {
		if (file_exists($millionparttmp = '/tmp/__milestone_userid_'.$milestone.'_'.(floor($size / 50) * 50))) {
			return;
		}
		$remaining = 100 - $size;
		if (mail(
				'<EMAIL>',
				__('check_userid:subject:userid_milestone_reached_LINE',DO_UBBFLAT,$arg = array('USERID'=>$millionusers[0],'REMAINING'=>$remaining)),
				__('check_userid:body:userid_milestone_reached_TEXT',DONT_ESCAPE | DO_UBBFLAT,$arg),
				"From: ".basename($_SERVER['SCRIPT_NAME'],'.php')." <<EMAIL>>\r\n".
				"Content-Type: text/plain; charset=windows-1252\r\n".
				"Content-Transfer-Encoding: 8bit",

				'-<EMAIL>'
			)
		) {
			if (!$remaining) {
				touch($milliontmp);
			} else {
				touch($millionparttmp);
			}
		}
	}
}
