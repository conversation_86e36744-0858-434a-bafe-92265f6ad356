#!/usr/bin/php
<?php

define('CURRENTSTAMP',time());

require_once '/home/<USER>/public_html/_exit_if_readonly.inc';
require_once '/home/<USER>/public_html/_db.inc';
require_once '/home/<USER>/public_html/_error.inc';
require_once '/home/<USER>/public_html/_runonce.inc';

if (already_running()) {
	exit;
}

define('DEBUG',!isset($_SERVER['CRON']));

ob_implicit_flush(true);
main();
show_messages_cli();

function main() {
	$stats = db_simple_hash('topic','SELECT FORUMID,COUNT(*) FROM topic GROUP BY FORUMID',DB_USE_MASTER);
	if (!$stats) return;
	foreach ($stats as $forumid => $topix) {
		$updlist[] = 'WHEN '.$forumid.' THEN '.$topix;
	}
	db_update('forum_stats','
	UPDATE forum_stats SET TOPIX=CASE FORUMID '.implode(' ',$updlist).' END
	WHERE FORUMID IN ('.implodekeys(',',$stats).')'
	);
}
