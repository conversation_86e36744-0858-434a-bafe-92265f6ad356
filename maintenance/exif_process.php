#!/usr/bin/php
<?php

declare(strict_types=1);

$_SERVER['REQUEST_URI'] = $GLOBALS['argv'][0];
$_SERVER['HTTP_HOST'] = 'cli';

require_once __DIR__.'/../public_html/_db.inc';
require_once __DIR__.'/../public_html/_date.inc';
require_once __DIR__.'/../public_html/_execute.inc';
require_once __DIR__.'/../public_html/_run_and_exit.inc';

run_and_exit(run_once: true, syslog: true);

function main(): bool {
	$debug = false;
	$dirs = [];
	$output_sql = false;
	global $argv;
	foreach ($argv as $i => $arg) {
		if (!$i) {
			continue;
		}
		/** @noinspection DegradedSwitchInspection */
		switch ($arg) {	# NOSONAR
		case 'debug':
			$debug = true;
			break;
		case 'help':
			echo "usage: $argv[0] [sql] <dir(,dir)*>\n";
			break;
		case 'sql':
			$output_sql = true;
			break;
		default:
			$dirs[] = $arg;
			break;
		}
	}
	define('DEBUG', !$debug || (!$output_sql && !isset($_SERVER['CRON'])));

	if ($dirs) {
		foreach ($dirs as $dir) {
			if (!process_directory($dir, $output_sql)) {
				return false;
			}
		}
	} elseif (!process_galleries($output_sql)) {
		return false;
	}
	return true;
}

function process_galleries($output_sql = false): bool {
	if (!($galleries = db_rowuse_array('gallery','
		SELECT PARTYID, GALLERYID
		FROM party_db.gallery
		WHERE GALLERYID >= 6515
		  AND NOT EXIF_PROCESSED
		GROUP BY GALLERYID'))
	) {
		if ($galleries !== false) {
			syslog(LOG_INFO, 'no galleries to process');
		}
		return $galleries !== false;
	}
	foreach ($galleries as $gallery) {
		$path = "/mnt/gluster/photos_v2/{$gallery['PARTYID']}/unprocessed";

		syslog(LOG_INFO, "processing photos in $path");

		process_directory($path, $output_sql);
		[$rc,, $stderr] = execute('/home/<USER>/maintenance/exif_process.php '.$path);
		if ($stderr) {
			error_log($stderr);
		}
		if ($rc
		||	!db_update('gallery', "
			UPDATE party_db.gallery SET
				EXIF_PROCESSED = UNIX_TIMESTAMP()
			WHERE GALLERYID = {$gallery['GALLERYID']}")
		) {
			return false;
		}
	}
	return true;
}


function process_directory(string $dir, bool $output_sql = false): bool {
	syslog(LOG_INFO, "start processing of $dir");
	if (!is_dir($dir)) {
		syslog_error(LOG_CRIT, "directory to process does not exist: $dir");
		return false;
	}
	if ($dir !== '.' && !chdir($dir)) {
		syslog_last_error(LOG_CRIT, "failed to change working directory to $dir");
		return false;
	}
	if (!($unprocessed_dir = opendir('.'))) {
		syslog_last_error(LOG_CRIT, 'failed to open dir');
		return false;
	}
	while (false !== ($item = readdir($unprocessed_dir))) {
		if ($item[0] === '.') {
			continue;
		}
		if (!preg_match('"^(?<photo_id>\d+)\.(?:avif|jpe?g|png|tiff?|webp)$"i', $item, $match)) {
			syslog(LOG_DEBUG, "item not recognized as image: $item");
			continue;
		}
		syslog(LOG_DEBUG, "item recognized as image: $item");
		$photo_id = (int)$match['photo_id'];

		syslog(LOG_INFO, "checking exif in $item");

		$escaped_item = escape_utf8($item);

		$exe = 'exiftool -t -s "'.$escaped_item.'"';

		syslog(LOG_DEBUG, "executing: $exe");
		[$rc, $stdout, $stderr] = execute($exe);
		if ($rc) {
			syslog(LOG_CRIT, "exiftool failed: $stderr");
			return false;
		}
		$stamp = 0;
		$infos = [];
		foreach (explode("\n", $stdout) as $line) {
			if (!($line = utf8_mytrim($line))) {
				continue;
			}
			$pair = explode("\t", $line);
			if (!isset($pair[1])) {
				continue;
			}
			[$key, $value] = $pair;
			$infos[$key] = $value;
		}
		foreach ([
			'SubSecDateTimeOriginal',
			'SubSecCreateDate',
			'DateTimeCreated',
			'MetadataDate',
		] as $key) {
			if (!isset($infos[$key])) {
				continue;
			}
			if (($value = preg_replace('"^(20\d\d)[\-:](\d\d)[\-:](\d\d)\s"','$1-$2-$3', $infos[$key]))
			&&	($stamp = strtotime($value))
			) {
				syslog(LOG_INFO, "detected date in $key: $infos[$key] = $stamp");
				break;
			}
		}
		if ($output_sql) {
			echo "UPDATE image SET SHOTSTAMP = $stamp WHERE IMGID = $photo_id;\n";
		} else {
			if (!db_update('image', "
				UPDATE image SET SHOTSTAMP = $stamp
				WHERE IMGID = $photo_id")
			) {
				return false;
			}
			syslog(LOG_INFO, "set shot stamp to $stamp = "._datetime_get($stamp));
		}
	}
	closedir($unprocessed_dir);
	return true;
}
