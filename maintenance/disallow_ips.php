#!/usr/bin/php
<?php

define('CURRENTUSERID',0);
define('CURRENTSTAMP',time());
#define('DRYRUN',true);

require_once '/home/<USER>/public_html/_exit_if_readonly.inc';
require_once '/home/<USER>/public_html/_db.inc';
require_once '/home/<USER>/public_html/_error.inc';
require_once '/home/<USER>/public_html/_disallowcommit.inc';
require_once '/home/<USER>/public_html/_runonce.inc';

if (already_running()) {
	exit;
}

ob_implicit_flush(true);
$rc = main();
exit(show_messages_cli() || !$rc ? 1 : 0);

function main() {
	global $argv;
	if (empty($argv[3])
	||	$argv[2] != 'tor' && $argv[2] != 'proxy' && $argv[2] != 'harvest'
	) {
		error_log('need <file> [tor|proxy|harvest] <description>',0);
		return false;
	}
	list(,$file,$type) = $argv;
	if (!file_exists($file)) {
		error_log('file does not exist',0);
		return false;
	}
	foreach (file($file) as $ipstr) {
		if ($ipstr[0] == '#') {
			continue;
		}
		if (preg_match('"\[IPSTR\] => (.*)"',$ipstr,$match)) {
			$ipstr = $match[1];
		} elseif (preg_match('"(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})"',$ipstr,$match)) {
			$ipstr = $match[1];
		}
		$_POST = [
			'DISALLOW'	=> $type,
			'IPSTR'		=> $ipstr,
			'REASON'	=> implode(' ',array_slice($argv,3))
		];
		if (false === disallowuser_commit(DIS_DONT_OVERWRITE)) {
			return false;
		}
	}
	return true;
}
