#!/usr/bin/php
<?php

define('CURRENTSTAMP',time());

require_once '/home/<USER>/public_html/_db.inc';
require_once '/home/<USER>/public_html/_date.inc';
require_once '/home/<USER>/public_html/_urltitle.inc';
require_once '/home/<USER>/public_html/_flockbot.inc';

ob_implicit_flush(true);
$rc = main();
exit(show_messages_cli() || !$rc ? 1 : 0);

function main() {
	$msgs = db_rowuse_array('flockbot_notification','
		SELECT	CHANNEL, MSGID, LINKS, STAMP, ASIS,
			IF(MYSQL_COMPRESSED, UNCOMPRESS(MSG), MSG) AS MSG
		FROM flockbot_notification
		WHERE DONE = 0
		ORDER BY STAMP ASC',
		DB_NO_FLOCKBOT
	);
	if (!$msgs) {
		return $msgs !== false;
	}
	$counts = $uniques = [];

	foreach ($msgs as &$msg) {
		$stripped = preg_replace('/"([^"]*?)(?!\\\\)"/', '', $msg['MSG']);
		$stripped = preg_replace('/(\d+)/' ,'', $stripped);
		$stripped = preg_replace('/,+/', ',', $stripped);
		$stripped = preg_replace('"(\s*SQL_NO_CACHE\s*)"', ' ', $stripped);

		$msg['STRIPPED'] = $stripped;

		if (!$msg['ASIS']) {
			increment_or_set($counts, $stripped, 1);
			$uniques[$stripped] = $msg;
			$ids[$stripped][] = $msg['MSGID'];
		} else {
			increment_or_set($counts, $msg['MSG'], 1);
			$uniques[$msg['MSG']] = $msg;
			$ids[$msg['MSG']][] = $msg['MSGID'];
		}
	}
	unset($msg);

	foreach ($uniques as $msg) {
		extract($msg);

		if (!$ASIS) {
			$dones = $ids[$STRIPPED];
			if ($counts[$STRIPPED] > 1) {
				$MSG .= "\n".'(× '.$counts[$STRIPPED].')';
			}
		} else {
			$dones = $ids[$MSG];
		}

		if (!flockbot_actually_notify($CHANNEL, $MSG, $LINKS, $STAMP)) {
			return false;
		}
		if (!db_update('flockbot_notification','
			UPDATE flockbot_notification SET
				DONE		 = 1,
				MSG		 = IF(MYSQL_COMPRESSED, MSG, COMPRESS(MSG)),
				MYSQL_COMPRESSED = 1
			WHERE MSGID IN ('.implode(', ', $dones).')',
			DB_NO_FLOCKBOT)
		) {
			return false;
		}
	}
	return true;
}
