#!/usr/bin/php
<?php

define('CURRENTSTAMP',time());
define('DEBUG',!isset($_SERVER['CRON']));

require_once '/home/<USER>/public_html/_exit_if_readonly.inc';
require_once '/home/<USER>/public_html/_db.inc';
require_once '/home/<USER>/public_html/_memcache.inc';
require_once '/home/<USER>/public_html/_helper.inc';
require_once '/home/<USER>/public_html/_allstats.inc';

ob_implicit_flush(true);
main();
show_messages_cli();

function main() {
	$allstat_lastdaynum = 0+db_single('allstat','SELECT MAX(DAYNUM) FROM allstat',DB_USE_MASTER);

	$allmax_lastdaynum = 0+db_single('allstat_allmax','SELECT MAX(DAYNUM) FROM allstat_allmax',DB_USE_MASTER);
	$frontmax_lastdaynum = 0+db_single('allstat_frontmax','SELECT MAX(DAYNUM) FROM allstat_frontmax',DB_USE_MASTER);
	$citymax_lastdaynum = 0+db_single('allstat_citymax','SELECT MAX(DAYNUM) FROM allstat_citymax',DB_USE_MASTER);
	$cityfrontmax_lastdaynum = 0+db_single('allstat_cityfrontmax','SELECT MAX(DAYNUM) FROM allstat_cityfrontmax',DB_USE_MASTER);

	$allmax_mobile_lastdaynum = 0+db_single('allstat_allmax_mobile','SELECT MAX(DAYNUM) FROM allstat_allmax_mobile',DB_USE_MASTER);
	$citymax_mobile_lastdaynum = 0+db_single('allstat_citymax_mobile','SELECT MAX(DAYNUM) FROM allstat_citymax_mobile',DB_USE_MASTER);

	if ($allstat_lastdaynum > $allmax_lastdaynum) {
		if (DEBUG) {
			$start = time();
		}
		db_replace(
			'allstat_allmax','
			REPLACE INTO allstat_allmax (DAYNUM,HOUR,DOMAIN,MOBILE,HITS) 
			SELECT DAYNUM,HOUR,DOMAIN,IF(FLAGS&'.ALLSTAT_SMALL_SCREEN.',1,0) AS MOBILE,SUM(HITS)
			FROM allstat
			WHERE DAYNUM>='.$allmax_lastdaynum.'
			  AND !(FLAGS & '.(ALLSTAT_ISADMIN | ALLSTAT_SPIDER).')
			GROUP BY DAYNUM,HOUR,MOBILE'
		);
		if (DEBUG) {
			echo 'allstat_allmax took ',(time() - $start)," seconds to generate..\n";
		}
	}
	if ($allstat_lastdaynum > $frontmax_lastdaynum) {
		if (DEBUG) {
			$start = time();
		}
		db_replace(
			'allstat_frontmax','
			REPLACE INTO allstat_frontmax (DAYNUM,HOUR,DOMAIN,HITS) 
			SELECT DAYNUM,HOUR,DOMAIN,SUM(HITS)
			FROM allstat
			WHERE SECTIONID=30
			  AND DAYNUM>='.$frontmax_lastdaynum.'
			  AND !(FLAGS & '.(ALLSTAT_ISADMIN | ALLSTAT_SPIDER).')
			GROUP BY DAYNUM,HOUR'
		);
		if (DEBUG) {
			echo 'allstat_frontmax took ',(time() - $start)," seconds to generate..\n";
		}
	}
	if ($allstat_lastdaynum > $citymax_lastdaynum) {
		if (DEBUG) {
			$start = time();
		}
		db_replace(
		'allstat_citymax','
			REPLACE INTO allstat_citymax (DAYNUM,HOUR,CITYID,HITS) 
			SELECT DAYNUM,HOUR,CITYID,SUM(HITS)
			FROM allstat
			WHERE DAYNUM>='.$frontmax_lastdaynum.'
			  AND !(FLAGS & '.(ALLSTAT_ISADMIN | ALLSTAT_SPIDER).')
			GROUP BY DAYNUM,HOUR,CITYID'
		);
		if (DEBUG) {
			echo 'allstat_citymax took ',(time() - $start)," seconds to generate..\n";
		}
	}
	if ($allstat_lastdaynum > $cityfrontmax_lastdaynum) {
		if (DEBUG) {
			$start = time();
		}
		db_replace(
			'allstat_cityfrontmax','
			REPLACE INTO allstat_cityfrontmax (DAYNUM,HOUR,CITYID,HITS) 
			SELECT DAYNUM,HOUR,CITYID,SUM(HITS)
			FROM allstat
			WHERE SECTIONID=30
			  AND DAYNUM>='.$frontmax_lastdaynum.'
			  AND !(FLAGS & '.(ALLSTAT_ISADMIN | ALLSTAT_SPIDER).')
			GROUP BY DAYNUM,HOUR,CITYID'
		);
		if (DEBUG) {
			echo 'allstat_cityfrontmax took ',(time() - $start)," seconds to generate..\n";
		}
	}
	if ($allstat_lastdaynum > $allmax_mobile_lastdaynum) {
		if (DEBUG) {
			$start = time();
		}
		db_replace(
			'allstat_allmax_mobile','
			REPLACE INTO allstat_allmax_mobile (DAYNUM,HOUR,HITS) 
			SELECT DAYNUM,HOUR,SUM(HITS)
			FROM allstat
			WHERE DAYNUM>='.$allmax_lastdaynum.'
			  AND !(FLAGS & '.(ALLSTAT_ISADMIN | ALLSTAT_SPIDER).')
			  AND  (FLAGS & '.ALLSTAT_SMALL_SCREEN.')
			GROUP BY DAYNUM,HOUR'
		);
		if (DEBUG) {
			echo 'allstat_allmax_mobile took ',(time() - $start)," seconds to generate..\n";
		}
	}
	if ($allstat_lastdaynum > $citymax_mobile_lastdaynum) {
		if (DEBUG) {
			$start = time();
		}
		db_replace(
			'allstat_citymax_mobile','
			REPLACE INTO allstat_citymax_mobile (DAYNUM,HOUR,CITYID,HITS) 
			SELECT DAYNUM,HOUR,CITYID,SUM(HITS)
			FROM allstat
			WHERE DAYNUM>='.$allmax_lastdaynum.'
			  AND !(FLAGS & '.(ALLSTAT_ISADMIN | ALLSTAT_SPIDER).')
			  AND  (FLAGS & '.ALLSTAT_SMALL_SCREEN.')
			GROUP BY DAYNUM,HOUR,CITYID'
		);
		if (DEBUG) {
			echo 'allstat_citymax_mobile took ',(time() - $start)," seconds to generate..\n";
		}
	}
}
