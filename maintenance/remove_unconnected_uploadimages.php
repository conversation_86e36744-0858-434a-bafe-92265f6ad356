#!/usr/bin/php
<?php

define('CURRENTSTAMP',time());
define('TODAYSTAMP',mktime(0,0,0));
define('ERASE_LINE',!isset($_SERVER['CRON']) ? "\x1B[K" : null);
define('PREVIOUS_LINE',!isset($_SERVER['CRON']) ? "\x1B[F" : null);
define('DEBUG',!isset($_SERVER['CRON']));
#define('DRYRUN',true);

# NOTE: these are mostly PNG images which are not immediately optimized, but optimized once in a while by a cron job

require_once '/home/<USER>/public_html/_exit_if_readonly.inc';
require_once '/home/<USER>/public_html/_db.inc';
require_once '/home/<USER>/public_html/_error.inc';
require_once '/home/<USER>/public_html/_runonce.inc';

run_and_exit(run_once: true);

function main(): bool {
	return	remove_images_with_creation_date_same_as_deletion_date()
	# as there may be unconnected images due to various circumstances (only 1 yet above), we always run it
	&&	remove_unconnected_images();
}

function remove_images_with_creation_date_same_as_deletion_date(): bool {
	# Delete uploadimages with delete stamp set to same value as create stamp.
	# FIXME:
	#	Think of a way to detect why this happens and solve it there, instead of patching this later like this
	if (!db_delete('uploadimage_link_log','
		DELETE FROM uploadimage_link_log
		WHERE CSTAMP=DSTAMP')
	) {
		return false;
	}
	return true;
}

function remove_unconnected_images(): bool {
	if (!($images = db_simpler_array('uploadimage','
		SELECT UPIMGID
		FROM uploadimage
		LEFT JOIN uploadimage_link USING (UPIMGID)
		LEFT JOIN uploadimage_link_log USING (UPIMGID)
		WHERE (		ISNULL(uploadimage_link.UPIMGID)
			AND	ISNULL(uploadimage_link_log.UPIMGID)
		  )
		  AND uploadimage.CSTAMP<'.(TODAYSTAMP - ONE_DAY)
	))) {
		if ($images !== false) {
			echo "no images to remove\n";
		}
		return $images !== false;
	}

	$removed = 0;

	foreach ($images as $upimgid) {
		if (DEBUG) {
		 	echo ERASE_LINE,"processing $upimgid\n",PREVIOUS_LINE;
		}
		if (!db_delete('uploadimage_data','
			DELETE FROM data_db.uploadimage_data
			WHERE UPIMGID='.$upimgid)
		||	!db_delete('uploadimagemeta','
			DELETE FROM uploadimagemeta
			WHERE UPIMGID='.$upimgid)
		||	!db_delete('uploadimagecp','
			DELETE FROM uploadimagecp
			WHERE UPIMGID='.$upimgid)
		||	!db_delete('uploadimagecp_log','
			DELETE FROM uploadimagecp_log
			WHERE UPIMGID='.$upimgid)
		||	!db_delete('uploadimage_facebook_ids','
			DELETE FROM uploadimage_facebook_ids
			WHERE UPIMGID='.$upimgid)
		||	!db_delete('uploadimage_generated','
			DELETE FROM uploadimage_generated
			WHERE UPIMGID='.$upimgid)
		||	!db_delete('uploadimage_generated_log','
			DELETE FROM uploadimage_generated_log
			WHERE UPIMGID='.$upimgid)
		||	!db_delete('uploadimage_crop','
			DELETE FROM uploadimage_crop
			WHERE UPIMGID='.$upimgid)
		||	!db_delete('uploadimage_crop_log','
			DELETE FROM uploadimage_crop_log
			WHERE UPIMGID='.$upimgid)
		||	!db_delete('uploadimagetime','
			DELETE FROM uploadimagetime
			WHERE UPIMGID='.$upimgid)
		||	!db_delete('uploadimageorig','
			DELETE FROM uploadimageorig
			WHERE UPIMGID='.$upimgid)
		||	!db_delete('uploadimage','
			DELETE FROM uploadimage
			WHERE UPIMGID='.$upimgid)
		) {
			return false;
		}
		++$removed;
	}
	if (DEBUG) {
		echo ERASE_LINE,"uploadimages: removed $removed\n";
	}
	return true;
}
