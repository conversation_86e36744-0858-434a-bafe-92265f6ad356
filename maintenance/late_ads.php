#!/usr/bin/php
<?php

declare(strict_types=1);

require_once __DIR__.'/../public_html/_date.inc';

define('CURRENTSTAMP',	time());
define('CURRENTDAYNUM',	to_days());

require_once __DIR__.'/../public_html/_exit_if_readonly.inc';
require_once __DIR__.'/../public_html/_db.inc';
require_once __DIR__.'/../public_html/_error.inc';
require_once __DIR__.'/../public_html/_ad.inc';
require_once __DIR__.'/../public_html/_date.inc';
require_once __DIR__.'/../public_html/_sort.inc';
require_once __DIR__.'/../public_html/_missing.inc';
require_once __DIR__.'/../public_html/_newsad.inc';
require_once __DIR__.'/../public_html/_run_and_exit.inc';

run_and_exit(syslog: true);

function main(): bool {
	return	check('ad')
		&&	check('newsad');
}

function check(string $type): bool {
	[,,, $__hour, $__mins, $__secs] = _getdate();
	switch ($type) {
	case 'ad':
		$running = db_rowuse_array(['ad', 'adinfo', 'banner', 'distribution_ad'],'
			SELECT	"ad" AS ELEMENT,
					ad.ADID, ad.ADID AS ID, STARTSTAMP, STOPSTAMP, ad.IMPRESSIONS, IMPRESSIONSDONE, ACTIVE, ad.POSITION, ad.PARTYID, ad.REMOVED, TAKEOVER,
					distribution_ad.IMPRESSIONS AS D_IMPRESSIONS, THISHOUR,
					banner.NAME AS TITLE,
					(@LATE := '.CURRENTSTAMP.' > STOPSTAMP) AS LATE,
					(@NEED := IF(@LATE, ad.IMPRESSIONS, distribution_ad.IMPRESSIONS + THISHOUR * '.($__mins * ONE_MINUTE + $__secs).' / '.ONE_HOUR.')) AS NEED,
					(@DONE := ad.IMPRESSIONSDONE) AS DONE,
					(@MISSING := @NEED - @DONE) AS MISSING,
					(@PCT := IF(@NEED, 100 * (1 - @DONE / @NEED), 0)) AS PCT,
					(@TIME_LEFT := CAST(STOPSTAMP AS SIGNED) - '.CURRENTSTAMP.') AS TIME_LEFT,
					@PCT * '.ONE_DAY.'  / IF(@TIME_LEFT, IF(@TIME_LEFT > 0, @TIME_LEFT, -1 / @TIME_LEFT), 1) AS CPCT,
					'.profile_filters_for_query('ad').'
			FROM ad
			JOIN adinfo ON adinfo.ADID = ad.ADID
			LEFT JOIN banner USING (BANNERID)
			LEFT JOIN distribution_ad
					ON distribution_ad.ADID = ad.ADID
				   AND DAYNUM = '.CURRENTDAYNUM.'
				   AND HOUR = HOUR(NOW())
			WHERE ad.ACTIVE
			  AND NOT ad.REMOVED
			  AND ad.POSITION != '.ADPOS_PARTYLIST.'
			'./* don't check lateness at start and end of duration, as lateness might approach infinite: */'
			  AND '.CURRENTSTAMP.' NOT BETWEEN STARTSTAMP - '.ONE_HOUR.' AND STARTSTAMP + '.ONE_HOUR.'
			  AND '.CURRENTSTAMP.' NOT BETWEEN  STOPSTAMP - '.ONE_HOUR.' AND  STOPSTAMP + '.ONE_HOUR.'
			  AND STARTSTAMP <= '.CURRENTSTAMP.'
			'./* ads always stop withint ADATE_CONTINUE period, that's the inbuilt hard-stop */'
			  AND (KEEPTOP OR IMPRESSIONSDONE < ad.IMPRESSIONS)
	 		  AND STOPSTAMP > '.CURRENTSTAMP.' + IF(KEEPGOING, '.ADLATE_CONTINUE.', 0)
	 		  AND STARTSTAMP <= '.CURRENTSTAMP.'
			ORDER BY
				LATE DESC,
				CPCT DESC'
		);
		break;

	case 'newsad':
		$running = db_rowuse_array(['newsad', 'relation', 'news', 'distribution_newsad'],'
			SELECT	"newsad" AS ELEMENT,
					newsad.NEWSADID, newsad.NEWSADID AS ID, STARTSTAMP, STOPSTAMP, newsad.IMPRESSIONS, IMPRESSIONSDONE, ACTIVE, KEEPTOP, newsad.REMOVED,
					newsad.RELATIONID, NAME, TITLE,
					distribution_newsad.IMPRESSIONS AS D_IMPRESSIONS, THISHOUR,
					(@LATE := '.CURRENTSTAMP.' > STOPSTAMP) AS LATE,
					(@NEED := IF(@LATE, newsad.IMPRESSIONS, distribution_newsad.IMPRESSIONS + THISHOUR * '.($__mins * ONE_MINUTE + $__secs).' / '.ONE_HOUR.')) AS NEED,
					(@DONE := newsad.IMPRESSIONSDONE) AS DONE,
					(@MISSING := @NEED - @DONE) AS MISSING,
					(@PCT := IF(@NEED, 100 * (1 - @DONE / @NEED), 0)) AS PCT,
					(@TIME_LEFT := CAST(STOPSTAMP AS SIGNED) - '.CURRENTSTAMP.') AS TIME_LEFT,
					@PCT * '.ONE_DAY.'  / IF(@TIME_LEFT, IF(@TIME_LEFT > 0, @TIME_LEFT, -1 / @TIME_LEFT), 1) AS CPCT,
					'.profile_filters_for_query('newsad').'
			FROM newsad
			LEFT JOIN relation USING (RELATIONID)
			JOIN news ON news.NEWSID = newsad.NEWSID
			LEFT JOIN distribution_newsad 
				   ON distribution_newsad.NEWSADID = newsad.NEWSADID
				   AND DAYNUM = '.CURRENTDAYNUM."
				   AND HOUR = $__hour
			WHERE news.ACCEPTED
			  AND newsad.ACTIVE
			  AND NOT newsad.REMOVED
			  AND NOT KEEPTOP
			  AND STOPSTAMP > ".CURRENTSTAMP.'
			  AND (KEEPTOP OR IMPRESSIONSDONE < newsad.IMPRESSIONS)
	 		  AND STOPSTAMP > '.CURRENTSTAMP.' + IF(KEEPGOING AND IMPRESSIONSDONE < newsad.IMPRESSIONS , '.NEWSADLATE_CONTINUE.', 0)
	 		  AND STARTSTAMP <= '.CURRENTSTAMP.'
	 		ORDER BY
	 			LATE DESC,
	 			CPCT DESC'
		);
		break;
	}

	if (!$running) {
		return $running !== false;
	}

	calculate_missing($running);

	$missing = 0;

	foreach ($running as $ad) {
		ob_start();
		echo $ad['ELEMENT'] ?>:<?= $ad['ID'] ?> <?
		?>start:<? _datetime_display_numeric($ad['STARTSTAMP']) ?> <?
		?>stop:<?  _datetime_display_numeric($ad['STOPSTAMP']) ?> <?
		if (!empty($ad['POSITION'])) {
			?>position:<?= $ad['POSITION'] ?> <?
		}
		?>missing:<?= (int)$ad['MISSING'] ?> <?
		?>time_left:<?= $ad['TIME_LEFT'] ?> <?
		?>impressions_left:<?= $ad['IMPRESSIONS_LEFT'] ?> <?
		?>cpct:<?= $ad['__M']['cpct'] ?> <?
		?>max_missing:<? echo (int)$ad['__M']['max_missing'];

		syslog(LOG_DEBUG, ob_get_clean());

		if ($ad['__M']['cpct'] >= 2
		&&	(	!$ad['__M']['max_missing']
			||	$ad['MISSING'] >= $ad['__M']['max_missing']
			)
		) {
			static $__inited = false;
			if (!$__inited) {
				$__inited = true;
				$LITE = true;
				?><style><?
				include __DIR__.'/../public_html/style/_text_colors.inc';
				?></style><?
			}
			$missing_color = ($missing = $ad['MISSING']) > 5_000 ? 'red' : 'orange';
			$percent_class = get_missing_color($ad['__M']['cpct'], $light);
			?><pre><?
			?>slow:    <?= $ad['ELEMENT'] ?>:<?= $ad['ID'], "\n";
			?>title:   <b><?= $ad['TITLE'], "</b>\n";
			?>behind:  <span style="color: <?= $missing_color ?>; font-weight: bold;"><?= round($missing), "</span>\n";
			?>percent: <span class="<? echo $percent_class; if ($light) { ?> light<? } ?>"><?= round($ad['__M']['cpct'], 1), "</span>\n";
			?>link:    https://partyflock.nl/<?= $ad['ELEMENT'] ?>/<?= $ad['ID'], "\n";
			?></pre><?
		}
	}
	return true;
}
