#!/usr/bin/php
<?php

declare(strict_types=1);

define('CURRENTSTAMP',	time());
define('DEBUG',			!isset($_SERVER['CRON']));

require_once __DIR__.'/../public_html/_db.inc';
require_once __DIR__.'/../public_html/_run_and_exit.inc';

run_and_exit(run_once: true, syslog: true);

function main(): bool {
	$user_fbid = 0;
	$user_site = null;

	if (preg_match('"\bfacebook\.com/profile\.php\?id=(?P<fbid>\d+)"', $site, $match)) {
		$user_fbid = (int)$match['fbid'];
		$fbids[$user_fbid] = "('$element', $id, $user_fbid, 'user')";
		$user_site = "https://www.facebook.com/$user_fbid";
		$presence[$user_site] = 'facebook';
	}

	if ($webdriver !== false) {
		require_once '_fb.inc';
		$get_type = null;
		$use_site = $user_site ?? $site;

		$show_infos && $infos[] = "$element:$id store_presence(a) taking ".(microtime(true) - $start_time);

		$fbid = get_fbid_from_link($use_site, $get_type, $webdriver);

		$show_infos && $infos[] = "$element:$id store_presence(b) taking ".(microtime(true) - $start_time);

		if ($fbid
		&&	$fbid !== $user_fbid
		) {
			$fbids[$fbid] = "('$element', $id, $fbid, '".addslashes($get_type ?? '')."')";
			$site = "https://www.facebook.com/$fbid";
			$presence[$site] = $get_type === 'page' ? 'facebookpage' : 'facebook';

			if ($user_fbid) {
				if ($get_type === 'page'
				&&	!db_insert('fbid_identical', '
							INSERT IGNORE INTO fbid_identical SET
								STAMP	= '.CURRENTSTAMP.",
								PAGEID	= $fbid,
								FBID	= $user_fbid")
				) {
					return false;
				}
				if (!empty($user_fbid)) {
					unset($fbids[$user_fbid]);
				}
				if (!empty($user_site)) {
					unset($presence[$user_site]);
				}
			}
		}
}
