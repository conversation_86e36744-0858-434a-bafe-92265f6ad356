#!/usr/bin/php
<?php

declare(strict_types=1);

define('CURRENTSTAMP',	time());
define('DEBUG',			!isset($_SERVER['CRON']));

require_once __DIR__.'/../public_html/_db.inc';
require_once __DIR__.'/../public_html/_run_and_exit.inc';

run_and_exit(run_once: true, syslog: true);

function main(): bool {
	# the orignal queries:
	#
	# select *, min(cstamp) as MIN_CSTAMP, min(PRESENCEID) AS MIN_PRESENCEID from presence group by element, id, site having count(*)>1;
	#
	# select src.* from (select *, min(cstamp) as MIN_CSTAMP, min(PRESENCEID) AS MIN_PRESENCEID from presence group by element, id, site having count(*)>1) as x join presence src on src.presenceid=min_presenceid;
	#
	# create table keep_presence select src.* from (select *, min(cstamp) as MIN_CSTAMP, min(PRESENCEID) AS MIN_PRESENCEID from presence group by element, id, site having count(*)>1) as x join presence src on src.presenceid=x.MIN_PRESENCEID;
	#
	# delete presence from keep_presence join presence using (element, id, site);
	#
	# insert into presence select * from keep_presence;
	#
	# drop table keep_presence;

	if (!db_create('presence', '
		CREATE TEMPORARY  TABLE keep_presence
		SELECT src.* FROM (
			SELECT *, MIN(CSTAMP) AS MIN_CSTAMP, MIN(PRESENCEID) AS MIN_PRESENCEID
			FROM presence
			GROUP BY ELEMENT, ID, SITE 
			HAVING COUNT(*) > 1
		) AS x
		JOIN presence src ON src.presenceid = x.MIN_PRESENCEID',
		flags: DB_USE_SYSLOG)
	) {
		return false;
	}

	if (!($cnt = db_affected())) {
		syslog(LOG_INFO, 'no doubles found');
		return true;
	}
	syslog(LOG_INFO, "found $cnt multiple identical presences");
	if (DEBUG) {
		if (!($duplicates = db_rowuse_array('presence', '
			SELECT *
			FROM keep_presence',
			flags: DB_USE_SYSLOG | DB_FORCE_MASTER
		))) {
			return false;
		}
		$groups = [];
		foreach ($duplicates as $duplicate) {
			#if (isset($counts[$duplicate['ELEMENT']][$duplicate['ID']])) {
				$groups[$duplicate['ELEMENT']][$duplicate['ID']][] = $duplicate['SITE'];
			#}
		}
		print_rr($groups);
	}
	return	db_delete('presence', '
			DELETE presence
			FROM keep_presence
			JOIN presence USING (ELEMENT, ID, SITE)',
			flags: DB_USE_SYSLOG)
		&&	db_insert('presence', '
			INSERT INTO presence
			SELECT *
			FROM keep_presence',
			flags: DB_USE_SYSLOG);
}
