#!/usr/bin/php
<?php

declare(strict_types=1);

require_once __DIR__.'/../public_html/_exit_if_readonly.inc';
require_once __DIR__.'/../public_html/_db.inc';
require_once __DIR__.'/../public_html/_memcache.inc';
require_once __DIR__.'/../public_html/_date.inc';
require_once __DIR__.'/../public_html/_profile.inc';
require_once __DIR__.'/../public_html/_urltitle.inc';
require_once __DIR__.'/../public_html/_run_and_exit.inc';

const NEED_UNIQUE_VIEWERS	= 5;
const VIEWS_DAY_RANGE		= 90;

define('CURRENTSTAMP',	time());
define('TODAYSTAMP',	mktime(0,0,0));
define('CURRENTDAYNUM',	to_days());
define('DEBUG',			!isset($_SERVER['CRON']));
define('DRYRUN',		false);

run_and_exit(run_once: true);

function main(): bool {
	foreach (['be'] as $domain) {
		if (!determine_for($domain)) {
			return false;
		}
	}
	return true;
}

function determine_for(string $domain): bool {
	if (!($countryid = db_single('country', "SELECT COUNTRYID FROM country WHERE SHORT = '$domain'"))) {
		return $countryid !== false;
	}
	if (!db_create('elementforcountry','
		CREATE TEMPORARY TABLE elementforcountry_new
		LIKE elementforcountry')
	) {
		return false;
	}

	$setlist = [];

	# get views for location, organization, artist & party
	foreach (['location', 'organization', 'artist', 'party'] as $element) {
		switch ($element) {
		case 'location':
		case 'organization':
		case 'artist':
			$table = $element.'_counter';
			$viewname = 'VIEWS';
			break;

		case 'party':
			$table = 'party_view';
			$viewname = 'HITS';
			break;
		}

		$spec = explain_table($table);
		if (empty($spec['DOMAIN']->enum)) {
			error_log('empty DOMAIN enum');
			return false;
		}
		$all_domainstr = stringsimplode(',',$spec['DOMAIN']->enum);

		# within domain pick top 25%

		if (!($all_domain_elements = db_simple_hash($table, "
			SELECT DAYNUM, SUM($viewname)
			FROM $table
			WHERE DOMAIN = '$domain'
			  AND DAYNUM > TO_DAYS(NOW()) - ".VIEWS_DAY_RANGE.'
			GROUP BY DAYNUM'))
		) {
			if ($all_domain_elements === false) {
				return false;
			}
			continue;
		}

		sort($all_domain_elements);

		$cnt = count($all_domain_elements);

		# pick top 25%

		$index = 3 * $cnt / 4;

		$from = $all_domain_elements[$index];

		$ok_elements = db_simple_hash($table,'
			SELECT '.$element.'ID, SUM('.$viewname.')
			FROM '.$table.'
			WHERE DOMAIN = "'.$domain.'"
			  AND DAYNUM>=TO_DAYS(NOW()) - '.VIEWS_DAY_RANGE.'
			HAVING SUM('.$viewname.') >= '.$from
		);

		if ($ok_elements === false) {
			return false;
		}
		foreach ($ok_elements as $id => $hits) {
			$setlist[] = '('.$countryid.',"'.$element.'",'.$id.')';
		}

		# all domains, pick top 5%

		if (!($all_elements = db_simple_hash($table, "
			SELECT DAYNUM,SUM($viewname)
			FROM $table
			WHERE DOMAIN IN ($all_domainstr)
			  AND DAYNUM > TO_DAYS(NOW()) - ".VIEWS_DAY_RANGE.'
			GROUP BY DAYNUM'))
		) {
			if ($all_elements === false) {
				return false;
			}
			continue;
		}

		sort($all_elements);

		$cnt = count($all_elements);

		$index = 19 * $cnt / 20;

		$from = $all_elements[$index];

		$ok_elements = db_simple_hash($table,'
			SELECT '.$element.'ID,SUM('.$viewname.')
			FROM '.$table.'
			WHERE DOMAIN IN ('.$all_domainstr.')
			  AND DAYNUM>=TO_DAYS(NOW())-'.VIEWS_DAY_RANGE.'
			HAVING SUM('.$viewname.')>='.$from
		);

		if ($ok_elements === false) {
			return false;
		}
		foreach ($ok_elements as $id => $hits) {
			$setlist[] = '('.$countryid.',"'.$element.'",'.$id.')';
		}
	}

	if (# all events in country
		!db_insert('elementforcountry', '
		INSERT IGNORE INTO elementforcountry_new (COUNTRYID, ELEMENT, ID)
                SELECT DISTINCT '.$countryid.', "party", PARTYID
                FROM party
                JOIN location USING (LOCATIONID)
                JOIN city ON city.CITYID = location.CITYID
		WHERE COUNTRYID = '.$countryid.'
		  AND party.LOCATIONID
		  AND location.CITYID
		UNION
                SELECT DISTINCT '.$countryid.',"party", PARTYID
                FROM party
                JOIN boarding USING (BOARDINGID)
                JOIN city ON city.CITYID=boarding.CITYID
                WHERE COUNTRYID = '.$countryid.'
		  AND party.BOARDINGID
		  AND boarding.CITYID
		UNION
                SELECT DISTINCT '.$countryid.', "party", PARTYID
                FROM party
                JOIN city USING (CITYID)
                WHERE COUNTRYID = '.$countryid.'
		  AND party.CITYID')

		# all locations in country
	||	!db_insert('elementforcountry','
		INSERT IGNORE INTO elementforcountry_new (COUNTRYID, ELEMENT, ID)
		SELECT '.$countryid.', "location", LOCATIONID
		FROM location
		JOIN city USING (CITYID)
		WHERE COUNTRYID = '.$countryid)

		# all organizations in country
	||	!db_insert('elementforcountry','
		INSERT IGNORE INTO elementforcountry_new (COUNTRYID, ELEMENT, ID)
		SELECT '.$countryid.', "organization", ORGANIZATIONID
		FROM organization
		WHERE COUNTRYID = '.$countryid)

		# all artists living in or from country
	||	!db_create('elementforcountry','
		CREATE TEMPORARY TABLE artistforcountry (PRIMARY KEY (ARTISTID))
		SELECT ARTISTID
		FROM artist
		WHERE COUNTRYID = '.$countryid.'
		   OR LIVE_COUNTRYID = '.$countryid)

	||	!db_insert('elementforcountry','
		INSERT IGNORE INTO elementforcountry_new (COUNTRYID, ELEMENT, ID)
		SELECT '.$countryid.', "artist", ARTISTID
		FROM artistforcountry')

		# all foreign events organized by org from country

	||	!db_insert('elementforcountry','
		INSERT IGNORE INTO elementforcountry_new (COUNTRYID, ELEMENT, ID)
		SELECT '.$countryid.', "party", PARTYID
		FROM organization
		JOIN connect ON MAINTYPE = "organization" AND MAINID = ORGANIZATIONID AND ASSOCTYPE = "party"
		JOIN party ON ASSOCID = PARTYID
		LEFT JOIN location USING (LOCATIONID)
		LEFT JOIN boarding USING (BOARDINGID)
		LEFT JOIN city ON city.CITYID = COALESCE(boarding.CITYID, location.CITYID, party.CITYID)
		WHERE city.COUNTRYID != '.$countryid.'
		  AND organization.COUNTRYID = '.$countryid)
	) {
		return false;
	}

	# foreign events to find events with x amount of $countryid artist's

	if (!db_create('elementforcountry', "
		CREATE TEMPORARY TABLE foreign_parties (PRIMARY KEY (PARTYID))
		SELECT DISTINCT PARTYID
		FROM (	SELECT DISTINCT PARTYID
				FROM artistforcountry
				JOIN lineup USING (ARTISTID)
		) AS event_with_artists
		JOIN party USING (PARTYID)
		JOIN location USING (LOCATIONID)
		JOIN city ON city.CITYID = location.CITYID
		WHERE city.COUNTRYID != $countryid
		  AND party.LOCATIONID != 0
		  AND location.CITYID != 0
		UNION
		SELECT DISTINCT PARTYID
		FROM party
		JOIN lineup USING (PARTYID)
		JOIN artistforcountry USING (ARTISTID)
		JOIN boarding USING (BOARDINGID)
		JOIN city ON city.CITYID = boarding.CITYID
		WHERE city.COUNTRYID != $countryid
		  AND party.BOARDINGID != 0
		  AND boarding.CITYID != 0
		UNION
		SELECT DISTINCT PARTYID
		FROM party
		JOIN lineup USING (PARTYID)
		JOIN artistforcountry USING (ARTISTID)
		JOIN city ON city.CITYID = party.CITYID
		WHERE city.COUNTRYID != $countryid
		  AND party.CITYID != 0")
	) {
		return false;
	}
	# at least 5 artist's from country, at least 20% of total
	if (!db_insert('elementforcountry', "
		INSERT IGNORE INTO elementforcountry_new (COUNTRYID, ELEMENT, ID)
		SELECT $countryid, 'party', PARTYID
		FROM (	SELECT PARTYID, COUNT(artistforcountry.ARTISTID) AS PART_CNT, COUNT(*) AS ALL_CNT
				FROM lineup
				JOIN foreign_parties USING (PARTYID)
				LEFT JOIN artistforcountry USING (ARTISTID)
				GROUP BY PARTYID
				HAVING PART_CNT >= 5
				   AND PART_CNT / ALL_CNT >= .2
			) AS okparties")
	) {
		return false;
	}
	# get inhabitants and determine popular items
	if (false === ($userids = db_simpler_array(['user','user_account'], "
		SELECT SQL_NO_CACHE USERID
		FROM user
		WHERE COUNTRYID = $countryid"))
	) {
		return false;
	}
	if (!($stats = db_simple_hash('elementstats_user','
		SELECT ELEMENT, ID, COUNT(DISTINCT USERID)
		FROM elementstats_user
		WHERE USERID IN ('.implode(', ', $userids).')
		  AND ELEMENT IN ("organization", "location", "artist", "party")
		GROUP BY ELEMENT, ID
		HAVING COUNT(DISTINCT USERID) > '.NEED_UNIQUE_VIEWERS))
	) {
		return $stats !== false;
	}
	foreach ($stats as $element => $ids) {
		foreach ($ids as $id => /* $cnt = */ $ignored) {
			$setlist[] = "($countryid, '$element', $id)";
		}
		switch ($element) {
		case 'location':
			# include all events in this location
			if (!db_insert('elementforcountry','
				INSERT IGNORE INTO elementforcountry_new (COUNTRYID, ELEMENT, ID)
				SELECT '.$countryid.', "party", PARTYID
				FROM party
				WHERE LOCATIONID IN ('.implode(', ', $ids).')')
			) {
				return false;
			}
			break;

		case 'organization':
			# include all events by this organization
			if (!db_insert('elementforcountry', '
				INSERT IGNORE INTO elementforcountry_new (COUNTRYID, ELEMENT, ID)
				SELECT '.$countryid.', "party", ASSOCID
				FROM connect
				WHERE MAINTYPE = "organization"
				  AND MAINID IN ('.implode(', ', $ids).')
				  AND ASSOCTYPE = "party"')
			) {
				return false;
			}
			break;

		case 'artist':
			# include all events for this artist
			if (!db_insert('elementforcountry', '
				INSERT IGNORE INTO elementforcountry_new (COUNTRYID, ELEMENT, ID)
				SELECT '.$countryid.', "party", PARTYID
				FROM lineup
				WHERE ARTISTID IN ('.implode(', ', $ids).')')
			) {
				return false;
			}
			break;
		}
	}


	if ($setlist
	&&	!db_insert('elementforcountry', '
		INSERT IGNORE INTO elementforcountry_new (COUNTRYID, ELEMENT, ID)
		VALUES '.implode(', ', $setlist))
	) {
		return false;
	}

	#######


	# big festivals within 500 km

/*	SELECT DISTINCT ID
	FROM fbid
	JOIN facebook_guests USING (FBID)
	WHERE VISITORS>=10000
	  AND ELEMENT="party"'*/

	while (	db_delete('elementforcountry', '
			DELETE FROM elementforcountry
			WHERE COUNTRYID = '.$countryid.'
			LIMIT 100')
		&&	db_affected()
	) {
		if (!waitforslaves('elementforcountry')) {
			return false;
		}
	}
	if (!db_insert('elementforcountry', '
		INSERT INTO elementforcountry
		SELECT *
		FROM elementforcountry_new')
	) {
		return false;
	}
	return true;
}
