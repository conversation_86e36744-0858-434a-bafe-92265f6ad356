#!/usr/bin/php
<?php

require_once '/home/<USER>//public_html/_exit_if_readonly.inc';
require_once '/home/<USER>//public_html/_db.inc';

define('CURRENTSTAMP',	time());
define('DEBUG',		!isset($_SERVER['CRON']));

ob_implicit_flush();
$rc = main();
exit(show_messages_cli() || !$rc);

function main(): bool {
	$need_hosts = ['searchparty','searchpartyrep'];

	$host_count = count($need_hosts);

	$done = db_simple_hash('sph_recreated','
		SELECT ELEMENT,MIN(STAMP)
		FROM sph_recreated
		WHERE HOST IN ('.stringsimplode(',',$need_hosts).')
		GROUP BY ELEMENT
		HAVING COUNT(*)='.$host_count
	);
	if (!$done) {
		return $done !== false;
	}
	foreach ($done as $element => $stamp) {
		$dels[] = 'ELEMENT="'.$element.'" AND MSTAMP<'.$stamp;
	}
	if (!db_delete('sph_update','
		DELETE FROM sph_update
		WHERE '.implode(' OR ',$dels))
	) {
		return false;
	}
	if (DEBUG) {
		echo "flushed ",db_affected()," entries from sph_update\n";
	}
	return true;
}
