#!/usr/bin/php
<?php

declare(strict_types=1);

define('CURRENTSTAMP',	time());
define('DEBUG',			!isset($_SERVER['CRON']));

chdir(__DIR__);
require_once '../public_html/_db.inc';
require_once '../public_html/_run_and_exit.inc';

run_and_exit(syslog: true);

/*

Find single areas with a name:

SELECT NAME, COUNT(DISTINCT PARTYID) AS CNT
FROM (
    SELECT PARTYID, NAME
    FROM partyarea
    GROUP BY PARTYID
    HAVING COUNT(DISTINCT PARTYAREAID) = 1
       AND NAME != ""
) AS single_area
GROUP BY NAME
ORDER BY CNT;

*/

function main(): bool {
	foreach ([
		'1',
		'Area',
		'Area 0',
		'Area 1',
		'Festival',
		'Hardcore',
		'Indoor',
		'Mainstage',
		'Main',
		'Maib Area',
		'Main Floor',
		'Main Room',
		'Main Stage',
		'Outdoor',
		'Room',
		'Room 1',
		'Zaal',
		'Zaal 1',
	] as $area_name) {
		$area_name_s = addslashes($area_name);
	
		syslog(LOG_INFO, "checking for single areas named: $area_name");
	
		if (!db_insert('partyarea_log', '
			INSERT INTO partyarea_log
			SELECT *, UNIX_TIMESTAMP(), 0
			FROM partyarea
			GROUP BY PARTYID
			HAVING COUNT(DISTINCT PARTYAREAID) = 1
			   AND NAME = "'.$area_name_s.'"')
		) {
			return false;
		}
		if ($deleted = db_affected()) {
			if (!db_update('partyarea', '
				UPDATE partyarea
				JOIN (
					SELECT PARTYAREAID, NAME
					FROM partyarea
					GROUP BY PARTYID
					HAVING COUNT(DISTINCT PARTYAREAID) = 1
					   AND NAME = "'.$area_name_s.'"
				) AS single_aree  USING (PARTYAREAID)
				SET	NAME	= "",
					MUSERID	= 0,
					MSTAMP	= UNIX_TIMESTAMP()')
			) {
				return false;
			}
			$deleted = db_affected();
		}
		syslog(LOG_INFO, $deleted ? "deleted $deleted single areas" : "no single areas found");
	}
	return true;
}
