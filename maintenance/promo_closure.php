#!/usr/bin/php
<?php

require_once '/home/<USER>/public_html/_exit_if_readonly.inc';

define('CURRENTSTAMP',time());
define('TODAYSTAMP',mktime(0,0,0));
define('DEBUG',!isset($_SERVER['CRON']));

require_once '/home/<USER>/public_html/_db.inc';
require_once '/home/<USER>/public_html/_memcache.inc';
require_once '/home/<USER>/public_html/_require.inc';
require_once '/home/<USER>/public_html/_promogoing.inc';

ob_implicit_flush(true);
$rc = main();
exit(show_messages_cli() || $rc === false ? 1 : 0);

function main() {
	$promos = db_simpler_array(
		'promo','
		SELECT PROMOID
		FROM promo
		WHERE ISNULL(AFTERGOING)
		  AND ACTIVE=1
		  AND STOPSTAMP<='.TODAYSTAMP
	);
	if ($promos === false) {
		return false;
	}
	if (DEBUG) {
		print_r($promos);
	}
	if (!$promos) {
		return null;
	}
	$rc = true;
	foreach ($promos as $promoid) {
		if (!($cnts = stop_promo($promoid))) {
			$rc = false;
		}
	}
	return $rc;
}
