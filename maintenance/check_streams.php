#!/usr/bin/php
<?php

define('CURRENTSTAMP',	time());
define('DEBUG',		!isset($_SERVER['CRON']));
define('USER_AGENT',	'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_12_5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/60.0.3112.78 Safari/537.36; PartyflockStreamChecker');
define('USER_AGENT_WIN','WinampMPEG/5.24 (compatible; Partyflock stream checker)');

const FSOCK_TIMEOUT	= 10;

require_once '/home/<USER>/public_html/_exit_if_readonly.inc';
require_once '/home/<USER>/public_html/_db.inc';
require_once '/home/<USER>/public_html/_execute.inc';
require_once '/home/<USER>/public_html/_memcache.inc';
require_once '/home/<USER>/public_html/_helper.inc';
require_once '/home/<USER>/public_html/_runonce.inc';

const CHECK_EVERY	= ONE_HOUR;

// 7.html example:
// 0,1,3,32,0,16,Sen<PERSON> 2005 - <PERSON> Live 03-07-2005
// 0 == listeners
// 1 == broadcasting
// 2 == listener peak
// 3 == maxlisteners
// 4 == ?
// 5 == bitrate
// 6 == song playing

if (!openlog('check_streams', LOG_PID | (isset($_SERVER['CRON']) ? 0 : LOG_PERROR), LOG_USER)) {
	exit(1);
}

require_once '/home/<USER>/public_html/_date.inc';
function output(string $str): void {
	$str = trim($str);
#	[$year, $month, $mday, $hour, $mins, $secs] = _getdate(time());
#	error_log(sprintf('%04d-%02d-%02d %02d:%02d.%02d', $year, $month, $mday, $hour, $mins, $secs).' '.$str);
	syslog(LOG_INFO, $str);
}

output("started\n");

$streamid = !empty($GLOBALS['argv'][1]) && ctype_digit($GLOBALS['argv'][1]) ? $GLOBALS['argv'][1] : 0;

if (!$streamid) {
	if (already_running()) {
		exit(0);
	}
}
ob_implicit_flush(true);
main($streamid);
show_messages_cli();

function main($streamid) {
	check_stream($streamid);
}
function check_stream($streamid = 0) {
	global $argv;
	if (!db_getlock('checkstreams:'.$streamid.'.'.(getifset($argv,1)))) {
		return;
	}
	$streams = db_rowuse_array(
		'stream','
		SELECT SQL_NO_CACHE STREAMID,NAME,ADDRESS,TYPE,MAXLISTENERS,LISTENERS,KBITRATE
		FROM stream
		WHERE '.(	$streamid
		  	?	'STREAMID='.$streamid
			:	(	isset($argv[1])
				?	'ACTIVE=0'
				:	'ACTIVE=1 AND CHECKSTAMP<'.CURRENTSTAMP.'-LEAST('.CHECK_EVERY.',FAILED*'.FIVE_MINUTES.')'
				)
		  	)
	);
  	if (!$streams) {
		output("no stream to check\n");
  		return;
	}
	foreach ($streams as $stream) {
		output("checking stream ".$stream['STREAMID']."\n");
		$stream['SONGPLAYING'] = null;
		if ($stream['TYPE'] == 'shoutcast') {
			$stream['MAXLISTENERS'] = 0;
			$stream['LISTENERS'] = 0;
			$stream['KBITRATE'] = 0;
		}
		if (preg_match('"\.as(p?x|f)$"i',$stream['ADDRESS'])) {
			if (check_asx_file($stream)) {
				store_stream($stream);
				continue;
			}
		}
		if (preg_match('"^mmsh?://"i',$stream['ADDRESS'])) {
			if (check_mms_stream($stream,$stream['ADDRESS'])) {
				store_stream($stream);
				continue;
			}
		} else {
			if (preg_match('"^(.*)\.(?:pls|m3u)$i"',$stream['ADDRESS'])) {
				if (check_shoutcast_playlist_manual($stream)) {
					store_stream($stream);
					continue;
				}
			} elseif (preg_match('"^(?:http(s)?://)?([^\/:]*)(?::(\d+))?/?(.*)$"i',$stream['ADDRESS'],$match)) {
				list(,$ssl,$host,$port,$path) = $match;
				if (!$port) {
					$port = $ssl ? 443 : 80;
				}
				if (check_shoutcast_stream($stream,$host,$port,$path,$ssl)) {
					store_stream($stream);
					continue;
				}
				if (!$path) {
					if (check_shoutcast_playlist_direct($stream,$host,$port,$path,$ssl)) {
						store_stream($stream);
						continue;
					}
				}
			}
		}
		$failedstreams[] = $stream['STREAMID'];
	}
	if (isset($failedstreams)) {
		output($stream['NAME'].': failed streams '.implode(',',$failedstreams)."\n");
		if (!db_update(
			'stream','
			UPDATE stream SET
				FAILED		=FAILED+1,
				CHECKSTAMP	='.time().'
			WHERE STREAMID IN ('.implode(',',$failedstreams).')')
		) {
			return;
		}
	}
}
function check_asx_file(&$stream,$asxfile = null) {
	output($stream['NAME'].': check_asx_file: '.($asxfile ? $asxfile : $stream['ADDRESS'])."\n");
	$contents = safe_file_get_contents(
		$asxfile ? $asxfile : $stream['ADDRESS'],
		params: [
			CURLOPT_TIMEOUT	=> 4,
		],
		quiet: true
	);
	if (!$contents) {
		output($stream['NAME'].': check_asx_file, could not get contents of '.$stream['ADDRESS']."\n");
		return false;
	}
	foreach (preg_split('"([\r\n]+)"',$contents) as $line) {
		output($stream['NAME'].': check_asx_file, got line: '.$line."\n");
		if (preg_match('"(?:ref\s+href|ref\d+)\s*=\s*\"?((?:mmsh?|https?)://(?:[^\s\"]+))"i',$line,$match)) {
			output($stream['NAME'].": MATCHED!\n");
			if (0 === strpos($match[1],'mms')) {
				check_mms_stream($stream,$match[1]);
			} elseif (!preg_match('"\.asx$"i',$match[1])) {
				if (check_mms_stream($stream,$match[1])) {
					return true;
				}
				if (preg_match('"^(?:http(s)?://)?([^\/:]*)(?::(\d+))?/?(.*)$"i',$match[1],$match)) {
					list(,$ssl,$host,$port,$path) = $match;
					if (!$port) {
						$port = $ssl ? 443 : 80;
					}
					if (check_shoutcast_stream($stream,$host,$port,$path,$ssl)) {
						return true;
					}
				}
			}
		}
	}
	output($stream['NAME'].": check_asx_file, no stream found\n");
	return false;
}

function check_mms_stream(array &$stream, string $url): bool {
	output($stream['NAME'].": checking mms stream: $url\n");
	if (isset($GLOBALS['__no_mimms'])) {
		return false;
	}
	if (!preg_match('"(mmsh?|http)://([^/:]+)(?::(\d+))?(?:/(.*))?$"i',$url,$match)) {
		output($stream['NAME'].": stream url not understood: ".$url."\n");
		return false;
	}

	$urltype = $match[1];
	$host = $match[2];
	$port = !empty($match[3]) ? $match[3] : ($urltype == 'http' ? 80 : 1755);
	if ($urltype == 'http') {
		$mmsurl = str_replace('http:','mms:',$url);
	} else {
		$mmsurl = $url;
	}
	$fp = @fsockopen($host, $port, $errno, $errstr, FSOCK_TIMEOUT);
	if (!$fp) {
		fsockopenfail($stream, $errno, $errstr);
		return false;
	}
	fclose($fp);
	$stream['ACTUAL'][0] = $url;
	$stream['TYPE'] = 'mms';
	# NOTE: mimms fails for most streams, so just accept connect on right port as success
	return true;
}

function check_shoutcast_stream(&$stream,$host,$port,$path = null,$ssl = false) {
	if (!$path) {
		output($stream['NAME'].": check_shoutcast_stream(,host=$host,port=$port,path=$path,ssl=".($ssl ? 'true' : 'false').")\n");
		$context = stream_context_create([
			'ssl'	=> [
				'verify_peer'	=> false,
			]
		]);
		$fp = @stream_socket_client(($ssl ? 'ssl://'.$host : $host).($port ? ':'.$port : null),$errno,$errstr,FSOCK_TIMEOUT,STREAM_CLIENT_CONNECT,$context);
		if (!$fp) {
			fsockopenfail($stream,$errno,$errstr);
			return false;
		}
		stream_set_timeout($fp,4);
		fputs($fp,"GET /7.html HTTP/1.1\r\nHost: $host\r\nUser-Agent: ".USER_AGENT_WIN."\r\n\r\n");
		$data = '';
		$charset = null;
		while (!feof($fp) && false !== ($line = fgets($fp))) {
			if (!isset($icecast)) {
				output($stream['NAME'].': check_shoutcast_stream, got line: '.trim($line)."\n");
			} else {
				output($stream['NAME'].': check_shoutcast_stream, got '.strlen($line)." bytes\n");
			}
			if (preg_match('"^ICY \d+"i',$line)
			||	false !== stripos($line,'icecast')
			||	false !== stripos($line,'icy-')
			) {
				$icecast = true;
				break;
			}
			if (preg_match('"Content-Type: .*?\bcharset\s*=\s*(?P<charset>.*)"', $line, $match)) {
				$charset = strtoupper($match['charset']);
			}
			if (0 === strncmp($line,'HTTP/1.1 404',12)) {
				output($stream['NAME'].": 404 file not found!\n");
				$notfound = true;
				break;
			}
			if (!isset($icecast)) {
				$data .= trim($line);
			}
		}
		fclose($fp);
		if (!isset($icecast)) {
			if (empty($notfound)) {
				if (preg_match('/<body>(.*)<\/body>/i',$data,$match)) {
					output($stream['NAME'].": found body\n");
					if (preg_match('/^(\d+),(\d+),(\d+),(\d+),(\d+),(\d+),(.*)$/i',$match[1],$newmatches)) {
						if (!$newmatches[2]) {
							// not broadcasting
							output($stream['NAME'].": not broadcasting\n");
							return false;
						}
						static $autoset;
						if (!isset($autoset[$stream['STREAMID']])) {
							$autoset[$stream['STREAMID']] = true;
							$stream['MAXLISTENERS'] = $stream['LISTENERS'] = 0;
						}
						$stream['MAXLISTENERS'] += $newmatches[4];
						$stream['LISTENERS']	+= $newmatches[1];
						$stream['KBITRATE']	= $newmatches[6];

						$title = $newmatches[7];
						$title = str_replace("\xFF",'',$title);

						if ('UTF-8' !== ($detected_charset = $charset ?: mb_detect_encoding($title, 'Windows-1252, UTF-8'))) {
							output($stream['NAME'].": converting stream title from $detected_charset to UTF-8\n");
							$title = mb_convert_encoding($title, 'UTF-8', $detected_charset);
						}

						$stream['SONGPLAYING']	= $title;

						$stream['TYPE'] = 'shoutcast';

						output($stream['NAME'].": found title: $title\n");

						$stream['ACTUAL'][0+$newmatches[6]] = 'http://'.$host.':'.$port;
						return true;
					}
				}
			} elseif (check_shoutcast_playlist_manual($stream)) {
				$stream['TYPE'] = 'shoutcast';
				return true;
			}
		}
	}
	if (false === check_icecast_stream($stream,$host,$port,$path,$ssl)) {
		if (!empty($stream['PLACEHOLDER'])) {
			# placeholder to generic station
			return false;
		}
		return check_shoutcast_playlist_manual($stream);
	}
	return true;
}
function check_icecast_stream(&$stream,$host,$port,$path,$ssl = false) {
	static $__done;
	if (isset($__done[$stream['STREAMID']][$host][$port][$path])) {
		return false;
	}
	$__done[$stream['STREAMID']][$host][$port][$path] = true;
	output($stream['NAME'].": check_icecast_stream(,host=$host,port=$port,path=$path,ssl=".($ssl ? 'true' : 'false').")\n");

	$context = stream_context_create([
		'ssl'	=> [
			'verify_peer'	=> false,
		]
	]);

	$fp = @stream_socket_client(($ssl ? 'ssl://'.$host : $host).($port ? ':'.$port : null),$errno,$errstr,FSOCK_TIMEOUT,STREAM_CLIENT_CONNECT,$context);
	if (!$fp) {
		fsockopenfail($stream,$errno,$errstr);
		return false;
	}
	stream_set_timeout($fp,4);
	if (preg_match('"^/+(.*)$"i',$path,$match)) {
		$path = $match[1];
	}

	if (false !== stripos($host,'partyflock.nl')) {
		return false;
	}

	fputs($fp,$req =
		"GET /$path HTTP/1.1\r\n".
		"Connection: close\r\n".
		"Host: $host\r\n".
		"User-Agent: ".USER_AGENT_WIN."\r\n".
		"ICY-MetaData: 1\r\n".
		"ICY-Br: 1\r\n".
		"\r\n");

	$data = '';
	for ($i = 0; $i < 10; ++$i) {
		$data .= fread($fp,1024);
	}

	if (preg_match('"HTTP/\d+.\d+ 30\d"i',$data)
	&&	preg_match('"Location: (?:http(s)?://)?([^\/:\r\n]*)(?::(\d+))?/?(.*?)[\r\n]"i',$data,$match)
	) {
		list(,$ssl,$host,$port,$path) = $match;
		if (!$port) {
			$port = $ssl ? 443 : 80;
		}

		if ($path === 'JamendoLounge'
		&&	strtolower(str_replace(' ', '', $stream['NAME'])) !== 'jamendolounge'
		) {
			output($stream['NAME'].': placeholder redirect detected');
			$stream['PLACEHOLDER'] = true;
			return false;
		}

		output($stream['NAME'].': redirect using '.$match[0]);
		fclose($fp);
		return check_icecast_stream($stream,$host,$port,$path,$ssl);
	}

	if (preg_match('!Content-Type: ([^\r\n]*)!i',$data,$match)) {
		if ($match[1] == 'audio/x-scpls') {
			fclose($fp);
			return check_shoutcast_playlist_direct($stream,$host,$port,$path);
		}
	}
	if (preg_match('"Content-Type:\s*(audio/mpeg|application/ogg|audio/aac)"i',$data,$match)
	||	false !== stristr($data,'ICY 200 OK')
	) {
		output($stream['NAME'].": ok, found ".($match[1] ?? 'ICY 200 OK')."\n");
		if (preg_match('"icy-br:\s*(\d+)"i',$data,$match)) {
			$stream['KBITRATE'] = $match[1];
			output($stream['NAME'].": bitrate: $match[1]\n");
		}
		if (preg_match('"icy-metaint:\s*(?P<meta_interval>\d+)"i',$data,$match)) {
			output($stream['NAME'].": metaint: ".$match['meta_interval']."\n");
			$total_read_bytes = 0;
			while ($total_read_bytes < 2 * $match['meta_interval'] + 10) {
				$data = fread($fp, 1024 * 1024);
				if (!$data) {
					break;
				}
				$read_bytes = strlen($data);
				if (preg_match('"StreamTitle=\'(?P<stream_title>.*?)\'"i', $data, $mtc)) {
					$title = $mtc['stream_title'];
					$title = str_replace("\xFF", '', $title);

					if ('UTF-8' !== ($detected_charset = mb_detect_encoding($title, 'Windows-1252, UTF-8'))) {
						output($stream['NAME'].": converting stream title from $detected_charset to UTF-8\n");
						$title = mb_convert_encoding($title, 'UTF-8', $detected_charset);
					}

					$stream['SONGPLAYING'] = $title;

					output($stream['NAME'].": found title: $title\n");
				}
				$total_read_bytes += $read_bytes;
			}
			output($stream['NAME'].": read total of $total_read_bytes bytes\n");
		}
		$stream['TYPE'] = 'icecast';
		$stream['ACTUAL'][0+$stream['KBITRATE']] = ($ssl ? 'https' : 'http').'://'.$host.':'.$port.'/'.$path;
		fclose($fp);
		return true;
	}
	output($stream['NAME'].": not ok!\n");
	fclose($fp);
	return stristr($data,'HTTP/1.0 403') ? null : false;
}
function check_shoutcast_playlist(&$stream) {
	output($stream['NAME'].": check_shoutcast_playlist($stream)\n");
	$playlist = safe_file_get_contents($stream['ADDRESS']);
	if (!$playlist) {
		output($stream['NAME'].': could not get contents of '.$stream['ADDRESS']."\n");
		return false;
	}
	$line = strtok($playlist,"\r\n");

	while ($line !== false) {
		$currentok = check_playlist_line($stream,$line);
		if (!isset($ok)
		&&	$currentok
		) {
			$ok = true;
			# maybe we can find more info?
			# not sure this is smart, takes lots of time for many streams
			if ($stream['SONGPLAYING']) {
				return true;
			}
		}
		$line = strtok("\r\n");
	}
	return isset($ok);
}
function check_shoutcast_playlist_manual(&$stream) {
	if (isset($stream['PLAYLISTDONE'])) {
		return false;
	}
	$stream['PLAYLISTDONE'] = true;
	output($stream['NAME'].': check_shoutcast_playlist_manual('.$stream['ADDRESS'].")\n");
	if (!preg_match('"^http(s)?://([^\/:]*)(?::(\d+))?/?(.*)$"i',$stream['ADDRESS'],$match)) {
		output($stream['NAME'].": check_shoutcast_playlist_manual: address not recognized\n");
		return false;
	}
	list(,$ssl,$host,$port,$path) = $match;
	if (!$port) {
		$port = $ssl ? 443 : 80;
	}
	output($stream['NAME'].': found host='.$host.',ssl='.($ssl ? 'true' : 'false').',port='.$port.',path='.$path."\n");
	return check_shoutcast_playlist_direct($stream,$host,$port,$path,$ssl);
}
function check_shoutcast_playlist_direct(&$stream,$host,$port,$path,$ssl = false) {
	output($stream['NAME'].": check_shoutcast_playlist_direct(,host=$host,port=$port,path=$path,ssl=".($ssl ? 'true' : 'false').")\n");
	$context = stream_context_create([
		'ssl'	=> [
			'verify_peer'	=> false,
		]
	]);
	$fp = @stream_socket_client(($ssl ? 'ssl://'.$host : $host).($port ? ':'.$port : null),$errno,$errstr,FSOCK_TIMEOUT,STREAM_CLIENT_CONNECT,$context);
	if (!$fp) {
		fsockopenfail($stream,$errno,$errstr);
		return false;
	}
	output($stream['NAME'].": connected\n");
	stream_set_timeout($fp,4);
	$useragent = USER_AGENT_WIN;

	fputs($fp,$req =
		"GET /$path HTTP/1.1\r\n".
		"Connection: close\r\n".
		"Host: $host\r\n".
		"User-Agent: ".$useragent."\r\n".
		"ICY-MetaData: 1\r\n".
		"ICY-Br: 1\r\n".
		"\r\n");

	$linesleft = 200;
	while (false !== ($line = fgets($fp,1024))) {
		if (!--$linesleft) {
			break;
		}
		output($stream['NAME'].': got line: '.$line);
		if (false !== stristr($line,'ICY 200 OK')
		||	false !== stristr($line,'Server: Icecast')
		) {
			output($stream['NAME'].': detected icecast');
			break;
		}
		if (!isset($gotheader)) {
			if ($line === "\n"
			||	$line === "\r\n"
			) {
				$gotheader = true;
				continue;
			}


			if (isset($redirect)) {
				if (0 == strncasecmp($line,'Location: ',10)) {
					$redirectaddress = trim(substr($line,10));
					output($stream['NAME'].': redirect to address '.$redirectaddress."\n");
					if (preg_match('"\basx\b"i',$redirectaddress)) {
						$currentok = check_asx_file($stream,$redirectaddress);
					} else {
						$currentok = check_playlist_line($stream,$redirectaddress);
					}
					if (!isset($ok)
					&&	$currentok
					) {
						$ok = true;
					}
				}
			}
			if (0 == strncmp($line,'HTTP/1.1 302',12)) {
				output($stream['NAME'].": found redirect!\n");
				$redirect = true;
			}
		} else {
			$currentok = check_playlist_line($stream,$line);
			if (!isset($ok)
			&&	$currentok
			) {
				$ok = true;
			}
		}
	}
	fclose($fp);
	return isset($ok);
}
function check_playlist_line(&$stream,$line) {
	$line = trim($line);
	output($stream['NAME'].': check_playlist_line: '.$line."\n");
	$streamid = $stream['STREAMID'];
	static $doneline;
	static $currentid = 0;
	if ($currentid != $streamid) {
		if ($currentid) {
			unset($doneline[$currentid]);
		}
		$currentid = $streamid;
	}
	if (isset($doneline[$streamid][$line])) {
		return false;
	}
	$doneline[$streamid][$line] = true;
	if (preg_match('"^http(s)?://([^\/:]*)(?::(\d+))?/?(.*)$"i',$line,$match)) {
		list(,$ssl,$host,$port,$path) = $match;
		if (!$port) {
			$port = $ssl ? 443 : 80;
		}
		output($stream['NAME'].": matched linetype 1\n");
		return check_shoutcast_stream($stream,$host,$port,$path,$ssl);
	} elseif (preg_match('"^File(\d+)=(.*)$"i',$line,$match)) {
		list(,$ndx,$address) = $match;
		output($stream['NAME'].": matched linetype 2: ndx=$ndx,address=$address\n");
		if (preg_match('"^http(s)?://([^/:]*?)(?::(\d+))?(/?|/.+)$"i',$address,$newmatch)) {
			list(,$ssl,$host,$port,$path) = $newmatch;
			if (!$port) {
				$port = $ssl ? 443 : 80;
			}
			output($stream['NAME'].": matched address: ".$match[2]."\n");
			return check_shoutcast_stream($stream,$host,$port,$path,$ssl);
		}
	} elseif (preg_match('"(?:ref\s+href|ref\d+)\s*=\s*\"?((?:mmsh?|http)://(?:[^\s\"]+))"i',$line,$match)) {
		output($stream['NAME'].": MATCHED!\n");
		if (!preg_match('"\.asx$"i',$match[1])) {
			if (preg_match('"^http://(.*?)(?::(\d+))?(?:/(.*))?$"i',$match[1],$hostmatches)) {
				if (check_shoutcast_stream($stream,$hostmatches[1],isset($hostmatches[2]) ? $hostmatches[2] : 8000,isset($hostmatches[3]) ? $hostmatches[3] : null)) {
					return true;
				}
			}
			if (check_mms_stream($stream,$match[1])
			||	false !== strpos($match[1],'http:')
			&&	check_mms_stream($stream,str_replace('http:','mms:',$match[1]))
			) {
				return true;
			}
		}
	}
	return false;
}
function store_stream($stream) {
	output($stream['NAME'].": storing stream\n");
	if (!db_update(
		'stream','
		UPDATE stream SET
			TYPE		="'.$stream['TYPE'].'",
			ACTIVE		=1,
			FAILED		=0,
			OKSTAMP		='.time().',
			CHECKSTAMP	='.time().',
			MAXLISTENERS	='.$stream['MAXLISTENERS'].',
			LISTENERS	='.$stream['LISTENERS'].',
			KBITRATE	='.$stream['KBITRATE'].',
			SONGPLAYING	="'.addslashes($stream['SONGPLAYING']).'"
		WHERE STREAMID='.$stream['STREAMID'])
	) {
		return false;
	}
	if ($stream['SONGPLAYING']) {
		db_insert('stream_track_log','
		INSERT INTO stream_track_log SET
			NOWPLAYING	="'.addslashes($stream['SONGPLAYING']).'",
			STAMP		='.time().',
			STREAMID	='.$stream['STREAMID']
		);
	}
	if (!db_delete('streamaddress','
		DELETE FROM streamaddress
		WHERE STREAMID = '.$stream['STREAMID'])
	) {
		return false;
	}
	if (!empty($stream['ACTUAL'])) {
		foreach ($stream['ACTUAL'] as $kbitrate => $url) {
			$vals[] = '('.$stream['STREAMID'].','.time().',"'.addslashes($url).'",'.$kbitrate.')';
		}
		if (!db_insert(
			'streamaddress','
			INSERT INTO streamaddress (STREAMID,CSTAMP,URL,KBITRATE)
			VALUES '.implode(',',$vals))
		) {
			return false;
		}
	}
	return true;
}
function fsockopenfail($stream, $errno, $errstr) {
	output($stream['NAME'].': fsockopen failed ('.$errno.'): '.$errstr."\n");
}
