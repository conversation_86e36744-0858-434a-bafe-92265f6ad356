#!/usr/bin/php
<?php

define('CURRENTSTAMP',time());
define('DEBUG',!isset($_SERVER['CRON']));
#define('DRYRUN',true);

require_once '/home/<USER>/public_html/_exit_if_readonly.inc';
require_once '/home/<USER>/public_html/_db.inc';
require_once '/home/<USER>/public_html/_error.inc';
require_once '/home/<USER>/public_html/_mimeheader.inc';
require_once '/home/<USER>/public_html/_urltitle.inc';
require_once __DIR__.'/../public_html/_run_and_exit.inc';
require_once __DIR__.'/../public_html/__translation.php';

run_and_exit();

function main(): bool {
	if (!openlog('fb_expireds'.(defined('DRYRUN') ? ' dryrun' : null),
			(defined('DRYRUN') ? 0 : LOG_PID)
		|	(isset($_SERVER['CRON']) ? 0 : LOG_PERROR),

		LOG_USER)
	) {
		return false;
	}

	if (!FACEBOOK_ENABLED) {
		syslog(LOG_WARNING, 'no running, facebook disabled');
		return true;
	}

	syslog(LOG_INFO,'started'.(defined('DRYRUN') ? ' dry run' : null));

	$expired = db_simple_hash(['facebook', 'facebook_token', 'user_account', 'fbexpired_mailed'],'
		SELECT facebook.USERID, MAX(EXPIRES) AS MAX_EXPIRES
		FROM facebook
		JOIN facebook_token USING (FACEBOOKID)
		JOIN user_account USING (USERID)
		LEFT JOIN fbexpired_mailed fm ON fm.USERID=user_account.USERID
		WHERE STATUS="active"
		  AND EXPIRES<'.CURRENTSTAMP.'
		GROUP BY facebook.USERID
		HAVING ISNULL(MAX(TOKEN_EXPIRES)) OR MAX(TOKEN_EXPIRES) < MAX_EXPIRES'
	);
	if (!$expired) {
		return $expired !== false;
	}

	$useridstr = implodekeys(',',$expired);

	$was_heres = db_simple_hash('user_data','
		SELECT USERID,LAST_USED
		FROM user_data
		WHERE USERID IN ('.$useridstr.')'
	);

	if (!$was_heres) {
		return $was_heres !== false;
	}

	$ok_heres = [];

	foreach ($was_heres as $userid => $last_used) {
		if ($last_used > $expired[$userid]) {
			$ok_heres[] = $userid;
		}
	}

	if (!$ok_heres) {
		return true;
	}
#	$useridstr = implode(',',$ok_heres);

	static $__currentlangid = null;

	foreach ($ok_heres as $userid) {

		syslog(LOG_INFO,'mailing user '.$userid);

		$user = db_single_assoc('user','
			SELECT	NICK,
				EMAIL,
				IF(REALNAME!="",REALNAME,IF(NAME!="",NAME,NICK)) AS REALNAME
			FROM user
			WHERE USERID='.$userid
		);
		if ($user === false) {
			return false;
		}
		if (!$user) {
			continue;
		}

		if (!db_insert('fbexpired_mailed','
			INSERT INTO fbexpired_mailed SET
				NOT_MAILED	='.($user['EMAIL'] ? 1 : 0).',
				USERID		='.$userid.',
				STAMP		='.CURRENTSTAMP.',
				TOKEN_EXPIRES	='.$expired[$userid])
		) {
			return false;
		}
		if (!db_affected()) {
			return false;
		}

		if (!$user['EMAIL']) {
			continue;
		}

#		$user['EMAIL'] = '<EMAIL>';

		$langid = get_last_langid($userid);
		if ($__currentlangid !== $langid) {
			initialize_locale($langid);
			$__currentlangid = $langid;
		}
		$rc = pf_mail(
		 // email
			$user['EMAIL']
		,// subject
			mime_header_subject(__('mail:expired_facebook:subject',DONT_ESCAPE))
		,// content
			quoted_printable_encode(__('mail:expired_facebook:message_TEXT',DONT_ESCAPE, [
				'NICK'		=> $user['NICK'],
				'USERID'	=> $userid,
				'REALNAME'	=> make_clean_realname($user['REALNAME']),
				'PROFILE'	=> 'https://partyflock.nl/user/'.$userid,
				'SITE'		=> 'https://partyflock.nl/renew.fb'
			]))
		,// headers:
			'From: Partyflock helpdesk <helpdesk@'.MAIL_DOMAIN.">\r\n".
			"Message-ID: <$userid.".CURRENTSTAMP.'.'.uniqid()."@partyflock.expiredfacebook>\r\n".
			"MIME-Version: 1.0\r\n".
			"Content-Type: text/plain; charset=windows-1252\r\n".
			"Content-Transfer-Encoding: quoted-printable"
		,// sendmail params
			'-fhelpdesk@'.MAIL_DOMAIN
		);
		if (!$rc) {
			return false;
		}
		sleep(1);
	}

	return true;
}
