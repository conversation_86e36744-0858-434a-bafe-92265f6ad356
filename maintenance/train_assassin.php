#!/usr/bin/php
<?php

declare(strict_types=1);

require_once '/home/<USER>/public_html/_exit_if_readonly.inc';
require_once '/home/<USER>/public_html/_db.inc';
require_once '/home/<USER>/public_html/_error.inc';
require_once '/home/<USER>/public_html/_execute.inc';
require_once '/home/<USER>/public_html/_run_and_exit.inc';

define('CURRENTSTAMP',	time());
# define('DEBUG',	true);
define('DEBUG',		!isset($_SERVER['CRON']));
# define('DRYRUN',	true);

run_and_exit(run_once: true);

function main(): bool {
	if (!($msgs = db_rowuse_hash(['completemsg', 'contact_ticket_message', 'contact_ticket'], '
		SELECT CTMSGID, TYPE, UNCOMPRESS(completemsg.BODY) AS BODY
		FROM completemsg
		JOIN contact_ticket_message USING (CTMSGID)
		JOIN contact_ticket USING (TICKETID)
		WHERE contact_ticket.STATUS = "closed"
		  AND completemsg.STATUS = "new"
		  AND TYPE IN ("ham", "spam")
		  AND contact_ticket.CSTAMP < '.(CURRENTSTAMP - 2 * ONE_DAY).'
		  AND contact_ticket.MSTAMP < '.(CURRENTSTAMP - 2 * ONE_DAY)))
	) {
		return $msgs !== false;
	}

	$tmpfile = tempnam('/tmpdisk', '__train_mail_');

	if (!($tmpfp = fopen($tmpfile, 'wb'))) {
		return false;
	}

	register_shutdown_function(unlink(...), $tmpfile);

	foreach ($msgs as $ctmsgid => $msg) {
		if (DEBUG) {
			echo "processing $ctmsgid\n";
		}
		if (!train_assassin($msg['TYPE'], $msg['BODY'], $tmpfp, $tmpfile, $learned)
		||	!db_update('completemsg','
			UPDATE completemsg SET STATUS = "'.($learned ? 'learned' : 'not-learned').'"
			WHERE CTMSGID = '.$ctmsgid)
		) {
			return false;
		}
	}
	return true;
}

function train_assassin(string $type, string $body, mixed $tmpfp, string $tmpfile, ?bool &$learned): bool {
	$size = strlen($body);
	if (!ftruncate($tmpfp, $size)
	||	!rewind($tmpfp)
	||	!($written = fwrite($tmpfp, $body))
	) {
		return false;
	}
	[$rc, $stdout, $stderr] = execute("sa-learn --max-size 0 --$type $tmpfile");
	if (DEBUG) {
		echo $type, ': ', $stdout;
	}
	$learned = str_contains($stdout, '(1 message(s) examined)');
	if ($stderr) {
		error_log($stderr);
	}
	if ($rc) {
		error_log('sa-learn failed');
		return false;
	}
	return true;
}
