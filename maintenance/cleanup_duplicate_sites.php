#!/usr/bin/php
<?php

require_once '/home/<USER>/public_html/_exit_if_readonly.inc';
require_once '/home/<USER>/public_html/_db.inc';
require_once '/home/<USER>/public_html/_helper.inc';

define('CURRENTSTAMP',time());
define('DEBUG',!isset($_SERVER['CRON']));
#define('DRYRUN',true);

ob_implicit_flush(true);
$rc = main();
exit(show_messages_cli() || !$rc ? 1 : 0);

function main() {
	$partyids = db_simpler_array(
		['party','organization','connect','location'],'
		SELECT PARTYID
		FROM party
		JOIN connect ON MAINTYPE="party" AND MAINID=PARTYID AND ASSOCTYPE="organization"
		JOIN organization ON ORGANIZATIONID=ASSOCID
		WHERE party.SITE!=""
		  AND organization.SITE!=""
		  AND REPLACE(party.SITE,"www.","")=REPLACE(organization.SITE,"www.","")
		  AND party.MSTAMP<'.(CURRENTSTAMP-30*60).'
		UNION
		SELECT PARTYID
		FROM party
		JOIN location USING (LOCATIONID)
		WHERE party.SITE!=""
		  AND location.SITE!=""
		  AND REPLACE(party.SITE,"www.","")=REPLACE(location.SITE,"www.","")
		  AND party.MSTAMP<'.(CURRENTSTAMP-30*60)
	);
	if (!$partyids) {
		return $partyids !== false;
	}
	$partyidstr = implode(',',$partyids);
	if (!db_insert('party_log','
		INSERT INTO party_log
		SELECT * FROM party
		WHERE PARTYID IN ('.$partyidstr.')')
	||	!db_update('party','
		UPDATE party SET
			MUSERID	=0,
			MSTAMP	='.CURRENTSTAMP.',
			SITE	=""
		WHERE PARTYID IN ('.$partyidstr.')')
	) {
		return true;
	}
	if (DEBUG) {
	        echo 'cleaned ',db_affected()," sites from parties\n";
	}
	return true;
}
