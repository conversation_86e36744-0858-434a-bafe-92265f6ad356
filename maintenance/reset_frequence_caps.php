#!/usr/bin/php
<?php

define('CURRENTSTAMP',time());

require_once '/home/<USER>/public_html/_db.inc';
require_once '/home/<USER>/public_html/_run_and_exit.inc';

run_and_exit(syslog: true, run_once: true);

function main(): bool {
	return reset_frequency_caps();
}

function reset_frequency_caps(): bool {
	return	db_update_in_parts('freqcap', '
		UPDATE freqcap SET
			CNT_TODAY = 0
		WHERE CNT_TODAY != 0',
		part_size: 10)
	&&	log_flush(__FUNCTION__, 'freqcap', 'update');
}

function log_flush(
	string	function,
	string	$table,
	string	$action = 'delete',
	?int	$count  = null,
): true {
	syslog(LOG_INFO, $function.': '.$action.'d '.($count ?? db_affected()).' rows from '.$table);
	return true;
}
