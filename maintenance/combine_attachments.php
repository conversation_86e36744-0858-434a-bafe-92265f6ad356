#!/usr/bin/php
<?php

declare(strict_types=1);

define('CURRENTSTAMP',	time());
define('DEBUG',			!isset($_SERVER['CRON']));

const DRYRUN = false;

require_once __DIR__.'/../public_html/_exit_if_readonly.inc';
require_once __DIR__.'/../public_html/_db.inc';
require_once __DIR__.'/../public_html/_error.inc';
require_once __DIR__.'/../public_html/_runonce.inc';
require_once __DIR__.'/../public_html/_run_and_exit.inc';

run_and_exit(run_once: true, syslog: true);

function main(): bool {
	$ok = combine_attachments($updated, $size_spared);
	syslog(LOG_INFO, $updated ? "$updated attachments combined, $size_spared bytes saved" : 'no attachments combined');
	return $ok;
}

function combine_attachments(
	?int &$combined		 = 0,
	?int &$combined_size = 0,
): bool {
	if (!($leaders = db_simpler_array('contact_ticket_attachment_meta', <<<MARIADB
		SELECT SQL_NO_CACHE GROUP_CONCAT(DATAID ORDER BY DATAID)
		FROM party_db.contact_ticket_attachment_meta
		WHERE SIZE AND CRC
		GROUP BY CRC, SIZE, MIMETYPE, COMPRESSED
		HAVING COUNT(*) > 1
		ORDER BY DATAID
		MARIADB,
		DB_USE_MASTER
	))) {
		return $leaders !== false;
	}
	foreach ($leaders as $data_id_str) {
		syslog(LOG_INFO, "processing: $data_id_str");
		$data = null;
		$keep_data_id = 0;
		$data_ids = explode(',', $data_id_str);
		foreach ($data_ids as &$identical_id) {
			$identical_id = (int)$identical_id;
			$keep_data_id = $identical_id;
			if (false === ($data = db_single('contact_ticket_attachment_data', <<<MARIADB
				SELECT DATA
				FROM data_db.contact_ticket_attachment_data
				WHERE DATAID = $keep_data_id
				MARIADB))
			) {
				return false;
			}
			if ($data) {
				break;
			}
			syslog(LOG_WARNING, "data missing for contact_ticket_attachment_data $keep_data_id");
			$keep_data_id = 0;
		}
		unset($identical_id);
		if (!$keep_data_id) {
			syslog_error(LOG_ERR, "all data gone for data ids $data_id_str");
			return false;
		}
		syslog(LOG_INFO, "keep id: $keep_data_id");

		foreach ($data_ids as $identical_id) {
			if ($identical_id === $keep_data_id) {
				syslog(LOG_INFO, "keeping $identical_id");
				continue;
			}
			if (false === ($identical_data = db_single('contact_ticket_attachment_data', <<<MARIADB
				SELECT SQL_NO_CACHE DATA
				FROM data_db.contact_ticket_attachment_data
				WHERE DATAID = $identical_id
				MARIADB))
			) {
				return false;
			}
			/** @var string $internaldata */
			if ($identical_data
			&&	$identical_data !== $data
			) {
				syslog_error(LOG_ERR, "data not the same: keep $keep_data_id length ".strlen($data)." !== identical $identical_id length ".strlen($identical_data));
				continue;
			}
			syslog(LOG_INFO, "mapping identical $identical_id to keep $keep_data_id");

			if (!DRYRUN
			&&	(	!db_insert('contact_ticket_attachment',  <<<MARIADB
					INSERT INTO contact_ticket_attachment_backup
					SELECT * FROM contact_ticket_attachment
					WHERE DATAID = $identical_id
					MARIADB)
				||	!db_insert('contact_ticket_attachment_meta',  <<<MARIADB
					INSERT INTO contact_ticket_attachment_meta_backup
					SELECT * FROM contact_ticket_attachment_meta
					WHERE DATAID = $identical_id
					MARIADB)
				||	!db_insert('contact_ticket_attachment_data', <<<MARIADB
					INSERT INTO contact_ticket_attachment_data_backup
					SELECT * From contact_ticket_attachment_data
					WHERE DATAID = $identical_id
					MARIADB)
				||	!db_update('contact_ticket_attachment', <<<MARIADB
					UPDATE contact_ticket_attachment SET DATAID = $keep_data_id
					WHERE DATAID = $identical_id
					MARIADB)
				||	!db_delete('contact_ticket_attachment_meta', <<<MARIADB
					DELETE FROM contact_ticket_attachment_meta
					WHERE DATAID = $identical_id
					MARIADB)
				||	!db_delete('contact_ticket_attachment_data', <<<MARIADB
					DELETE FROM data_db.contact_ticket_attachment_data
					WHERE DATAID = $identical_id
					MARIADB)
				)
			) {
				return false;
			}
			++$combined;
			$combined_size += strlen($data);
		}
	}
	return true;
}
