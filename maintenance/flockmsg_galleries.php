#!/usr/bin/php
<?php

define('CURRENTSTAMP', time());

require_once '/home/<USER>/public_html/_db.inc';
require_once '/home/<USER>/public_html/_urltitle.inc';
require_once '/home/<USER>/public_html/_flockbot.inc';
require_once '/home/<USER>/public_html/_run_and_exit.inc';

run_and_exit();

function main(): bool {
	if (!($galleries = db_rowuse_array(['gallery', 'image', 'user'], "
		SELECT	GALLERYID, gallery.PARTYID, ANONYMOUS,
			(SELECT COUNT(*) FROM image WHERE image.GALLERYID = gallery.GALLERYID) AS CNT,
			gallery.USERID, user.REALNAME, user.NAME, NICK
		FROM gallery 
		LEFT JOIN user ON gallery.USERID = user.USERID
		WHERE GALLERYID >= 6412
		  AND NOT SENT_FLOCKMSG
		  AND VISIBLE IN ('yes', 'visitors')"
	))) {
		return $galleries !== false;
	}

	foreach ($galleries as $gallery) {
		extract($gallery);

		$title = get_element_title('party', $PARTYID);
		
		$add_NICK = '';
		
		if ($USERID) {
			if ($REALNAME) {
				$use_NAME = win1252_to_utf8($REALNAME);
				$add_NICK = true;
			} elseif ($NAME) {
				$use_NAME = $NAME;
				$add_NICK = true;
			} else {
				$use_NAME = win1252_to_utf8($NICK);
			}
			if ($add_NICK) {
				$add_NICK = ' ('.escape_utf8(win1252_to_utf8($NICK)).')';
			}
		}
		
		$message = 	"*Nieuwe fotoset: $title!*\n".
				($ANONYMOUS || !$USERID ? '' : "door: $use_NAME$add_NICK\n").
				"$CNT foto's van $title zijn gepubliceerd. Bekijk ze en geef een waardering! (boven de set)\n".
				"→ https://partyflock.nl/gallery/$GALLERYID";

		$escaped_title = escape_utf8($title);
		$escaped_NAME  = $USERID ? escape_utf8($use_NAME) : '';

		$flockml = 	"<flockml>".
				"<b>Nieuwe fotoset: $escaped_title!</b><br />".
				($ANONYMOUS || !$USERID ? '' : "door: <i>$escaped_NAME$add_NICK</i><br />").
				"$CNT foto's van $escaped_title zijn gepubliceerd. Bekijk ze en geef een waardering! (boven de set)<br />".
				"Goede waardering vergroot de uitzendbaarheid van de fotograaf!<br />".
				"→ <b><a href=\"https://partyflock.nl/gallery/$GALLERYID\">$escaped_title foto's</a></b><br />".
				"Help mee met namen van artiesten onder foto's te zetten! Vraag <b>Thomas</b> om info.".
				"</flockml>";

		if (!flockbot_send_message(FLOCK_CHANNEL_PHOTOGRAPHERS, $message, $flockml)) {
			return false;
		}
		if (SERVER_SANDBOX) {
			break;
		}
		if (!db_update('gallery','
			UPDATE gallery SET
				SENT_FLOCKMSG = '.CURRENTSTAMP.'
			WHERE GALLERYID = '.$GALLERYID)
		) {
			return false;
		}
	}
	return true;
}
