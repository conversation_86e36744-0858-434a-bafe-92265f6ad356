#!/usr/bin/php
<?php

define('CURRENTSTAMP',time());

require_once '/home/<USER>/public_html/_exit_if_readonly.inc';
require_once '/home/<USER>/public_html/_db.inc';
require_once '/home/<USER>/public_html/_error.inc';
require_once '/home/<USER>/public_html/_date.inc';

ob_implicit_flush(true);
$rc = main();
exit(show_messages_cli() || !$rc);

function main() {
	list($y,$m,$d) = _getdate();
	$reqs = db_rowuse_array(['camera','party'],'
		SELECT	PARTYID,
			STAMP_TZI,
			NAME,
			STATUS,
			OFFER
		FROM camera
		JOIN party USING (PARTYID)
		WHERE CANCELLED = 0
		  AND STAMP BETWEEN '.(CURRENTSTAMP - ONE_WEEK).' AND '.CURRENTSTAMP.'
		  AND STATUS NOT IN ("accepted","denied","self")'
	);
	if (!$reqs) {
		return $reqs !== false;
	}
	echo "De volgende evenementen hadden afgelopen week een cameraverzoek staan dat wellicht een aanbod was van de organisatie:\n\n";

	foreach ($reqs as $req) {
		extract($req);
		list($y,$m,$d,$hour,$mins) = _getdate($STAMP_TZI,'UTC');
		if ($OFFER) {
			?>AANBOD: <?
		}
		echo "$NAME ($d ",_month_name($m)," $y ",sprintf('%02d:%02d',$hour,$mins),")\n";
		echo "https://vip.partyflock.nl/camera/$PARTYID\n\n";
	}

	echo "\nMogelijk respons:\n";

	echo "
Er had zich helaas geen vrijwillige Partyflockfotograaf aangemeld om foto's te nemen op jullie evenement.
Misschien hebben jullie een eigen goede fotograaf ingeschakeld?

We zouden het leuk vinden als er mooie foto's op Partyflock gepubliceerd kunnen worden!
Mochten jullie professionele foto's hebben die jullie ook op Partyflock zouden willen zien, laat het dan weten.
Indien ze door de keuring komen kunnen ze _gratis_ geplaatst worden.

Dat levert voor jullie als organisatie extra aandacht op en ons als Partyflock een mooie set foto's!­
Alle bezoekers bij ons krijgen een e-mail dat er foto's geplaatst zijn.

Let wel, we keuren streng om de kwaliteit van onze galerij te waarborgen.
Lever het logo apart aan. Wij plaatsen het logo en indien wenselijk de naam van de fotograaf in de standaard onderhoek van de foto's vanaf een bepaalde resolutie.

Je kunt ze het eenvoudigst aan ons doorgeven via Wetransfer.com
Past het niet op WeTransfer? Neem dan even contact op!

=======================

Unfortunately, no voluntary Partyflock photographer wanted to take pictures at Midnight Paradise, they are volunteers after all.
Maybe you enlisted a good photographer yourselves?

If you have quasi-professional pictures that you would like to see on Partyflock, let us know. If they pass the test, we can publish them for _free_.

This will provide extra attention for you, and a nice set of photos for Partyflock.
Please mind we must maintain a high standard for our main galleries.
Make sure there are no logos on the pictures. If there anyway, there is a reasonable change we will not publish them on Partyflock.

Your set of photos can best be sent to us using Wetransfer.com

=========================\n";

	return true;
}
