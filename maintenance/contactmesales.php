#!/usr/bin/php
<?php

declare(strict_types=1);

define('CURRENTSTAMP', time());

require_once __DIR__.'/../public_html/_exit_if_readonly.inc';
require_once __DIR__.'/../public_html/_db.inc';
require_once __DIR__.'/../public_html/_error.inc';
require_once __DIR__.'/../public_html/__translation.php';
require_once __DIR__.'/../public_html/_language.inc';
require_once __DIR__.'/../public_html/_run_and_exit.inc';

dont_set_translation_consts(true);

run_and_exit();

function main(): bool {
	$or_parts = [];
	global $argv;
	array_shift($argv);
	foreach ($argv as $arg) {
		if ($arg === 'unbound') {
			$or_parts[] = ' city.CITYID IS NULL ';
		} elseif (preg_match('"^(?:(?<not>not)_)?country=(?<country_short>[A-Z]{2})$"', $arg, $match)) {
			$or_parts[] = " {$match['not']} SHORT = '{$match['country_short']}' ";
		} else {
			fwrite(STDERR, "unknown argument: $arg\n");
			return false;
		}
	}
	if (!$or_parts) {
		fwrite(STDERR, "need arguments: <country=XX> <unbound>?\n");
		return false;
	}
	return send_to_sales($or_parts);
}

function send_to_sales(array $or_parts): bool {
	if (!($sales = db_rowuse_hash(['salescontactme', 'party', 'location', 'boarding', 'city', 'country', 'user', 'allowinquirymail'], '
		SELECT	PARTYID, COALESCE(salescontactme.LANGID, allowinquirymail.LANGID) AS LANGID,
				party.NAME,
				CONCAT("https://partyflock.nl/party/", PARTYID) AS LINK,
				FROM_unixtime(stamp) AS EVENT_DATE,
				IF(NOT ISNULL(user.USERID) AND user.EMAIL!="", user.EMAIL, allowinquirymail.EMAIL) AS EMAIL,
				salescontactme.USERID, user.NICK, user.NAME AS USER_NAME, user.REALNAME, user.PHONE,
				city.COUNTRYID
		FROM salescontactme
		JOIN party USING (PARTYID)
		LEFT JOIN location USING (LOCATIONID)
		LEFT JOIN boarding USING (BOARDINGID)
		LEFT JOIN city ON city.CITYID = COALESCE(boarding.CITYID, location.CITYID, party.CITYID)
		LEFT JOIN country ON country.COUNTRYID = city.COUNTRYID
		LEFT JOIN user ON salescontactme.USERID = user.USERID
		LEFT JOIN allowinquirymail ON ELEMENT = "party" AND allowinquirymail.ID = PARTYID
		WHERE party.ACCEPTED
		  AND NOT NOTIFIED
		  AND ('.implode(' OR ', $or_parts).')
		ORDER BY salescontactme.CSTAMP DESC'))
	) {
		return $sales !== false;
	}
	$multiple = count($sales) > 1;

	?>The <?= $multiple ? 'people' : 'person' ?> listed below <?= $multiple ? 'have' : 'has' ?> indicated, <?
	?>when submitting <?= $multiple ? 'their' : 'this' ?> event on Partyflock, <?
	?>that they would like to be contacted by our sales department:<?= "\n";

	foreach ($sales as $item) {
		/** @noinspection PhpRedundantOptionalArgumentInspection */
		extract($item, \EXTR_OVERWRITE);
		echo "\n";
		?>Event: <?= $NAME, "\n"
		?>Datum: <?= $EVENT_DATE, "\n"
		?>Event link: https://partyflock.nl/party/<?= $PARTYID, "\n";
		if ($USERID > 1) {
			?>User: <?= win1252_to_utf8($NICK), "\n";
			if ($USER_NAME) {
				?>Name: <?= $USER_NAME, "\n";
			}
			if ($REALNAME) {
				?>Full name: <?= win1252_to_utf8($REALNAME), "\n";
			}
			?>Profile link: https://partyflock.nl/user/<?= $USERID, "\n";
			if ($PHONE) {
				?>Phone: <?= $PHONE, "\n";
			}
		}
		?>E-mail: <? echo $EMAIL, "\n";
		if ($LANGID !== null
		&&	($language = get_language($LANGID))
		) {
			?>Language: <?= __('language:'.$language, DONT_ESCAPE | RETURN_UTF8, langid: LANG_ID_ENGLISH), "\n";
		}
		if (!db_update('salescontactme','
			UPDATE salescontactme SET
				NOTIFIED = 1,
				NSTAMP	 = UNIX_TIMESTAMP()
			WHERE PARTYID IN ('.implodekeys(', ', $sales).')')
		) {
			return false;
		}
	}
	return true;
}
