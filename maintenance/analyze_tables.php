#!/usr/bin/php
<?php

define('CURRENTSTAMP', time());

require_once '/home/<USER>/public_html/_exit_if_readonly.inc';
require_once '/home/<USER>/public_html/_db.inc';
require_once '/home/<USER>/public_html/_run_and_exit.inc';

run_and_exit(syslog: true);

function main(): bool {
	foreach ([
		CURRENT_MASTER_party_db		=> 'party_db',
		CURRENT_MASTER_counter_db	=> 'counter_db',
		CURRENT_MASTER_data_db		=> 'data_db',
	] as $master_server => $database) {
		if (!($tables = db_simpler_array(null, 'SHOW TABLES', DB_FORCE_SERVER, $master_server))) {
			if ($tables === false) {
				return false;
			}
			continue;
		}
		foreach ($tables as $table) {
			syslog(LOG_DEBUG, 'analyzing '.$master_server.':'.$database.'.'.$table);

			$start = time();
			if (!db_replace('analyzing', '
				REPLACE INTO analyzing SET
					SERVER = "'.$master_server.'",
				       `DATABASE` = "'.$database.'",
					TABLENAME = "'.addslashes($table).'"')
			||	!db_update(null, '
				ANALYZE TABLE `'.$table.'`',
				DB_FORCE_SERVER, $master_server)
			||	!db_delete('analyzing','
				DELETE FROM analyzing
				WHERE  SERVER = "'.$master_server.'"
				  AND `DATABASE` = "'.$database.'"
				  AND  TABLENAME = "'.addslashes($table).'"')
			) {
				return false;
			}
			$stop = time();

			$duration = $stop - $start;

			syslog(LOG_INFO, 'analyze of '.$master_server.':'.$database.'.'.$table.' took '.$duration.' seconds');

			if (!waitforslaves()) {
				return false;
			}
		}
	}
	return true;
}
