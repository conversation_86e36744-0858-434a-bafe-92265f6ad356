#!/usr/bin/php
<?php

require_once '/home/<USER>/public_html/_exit_if_readonly.inc';
require_once '/home/<USER>/public_html/_db.inc';
require_once '/home/<USER>/public_html/_require.inc';
require_once '/home/<USER>/public_html/_pagechanged.inc';
require_once '/home/<USER>/public_html/_runonce.inc';

define('CURRENTSTAMP',time());
define('DEBUG',!isset($_SERVER['CRON']));
#define('DRYRUN',true);

ob_implicit_flush(true);
$rc = false;
if (openlog('video_title_fixer'.(defined('DRYRUN') ? ' dryrun' : null),
		(defined('DRYRUN') ? 0 : LOG_PID)
	|	(isset($_SERVER['CRON']) ? 0 : LOG_PERROR),
	LOG_USER)
) {
	$rc = main();
	$rc = !show_messages_cli() && $rc;
	syslog(LOG_INFO,'done '.($rc ? 'ok' : 'failed'));
}
exit($rc ? 0 : 1);

function main() {
	syslog(LOG_INFO,'started'.(defined('DRYRUN') ? ' dry run' : null));
	if (already_running()) {
		syslog(LOG_INFO,'aready running');
		return false;
	}

	if (!fix_episodes()) {
		return false;
	}
	return true;
}
function fix_episodes() {
	$tofixs = db_rowuse_array('video','
		SELECT VIDEOID,TITLE
		FROM video
		WHERE TITLE LIKE "%episode%"'
	);
	if (!$tofixs) {
		return $tofixs === false ? false : true;
	}
	foreach ($tofixs as $tofix) {
		extract($tofix);
		$newtitle = preg_replace('"(?: [\-:\|]+)? \(?episode #?(\d+)\)?"i',' #$1',$TITLE);
		if ($TITLE == $newtitle) {
			$newtitle = preg_replace('"^\(?episode #?(\d+)\)?(?: [\-:\|]+)?(.+)$"i','$2 #$1',$TITLE);
		}
		if ($newtitle
		&&	$TITLE != $newtitle
		) {
			if (!db_insert('video_log','
				INSERT INTO video_log
				SELECT * FROM video
				WHERE VIDEOID='.$VIDEOID)
			||	!db_update('video','
				UPDATE video SET
					MUSERID	=0,
					MSTAMP	='.CURRENTSTAMP.',
					TITLE	="'.addslashes($newtitle).'"
				WHERE VIDEOID='.$VIDEOID)
			) {
				return false;
			}
			page_changed('video',$VIDEOID);
		}
	}
	return true;
}
