#!/usr/bin/php
<?php

define('CURRENTSTAMP',time());
define('TODAYSTAMP',mktime(0,0,0));
define('CURRENTUSERID',0);
define('DEBUG',!isset($_SERVER['CRON']));
define('DEBUG_FACEBOOK',false);

const KEEP_LOGS		= 14 * 24 * 3600;	# 2 weeks
const GRACE_PERIOD	= 1 * 24 * 3600;	# 1 day

$_SERVER['REQUEST_URI'] = 'facebook_background';

require_once '/home/<USER>/public_html/_exit_if_readonly.inc';
require_once '/home/<USER>/public_html/_error.inc';
require_once '/home/<USER>/public_html/_db.inc';
require_once '/home/<USER>/public_html/_date.inc';
require_once '/home/<USER>/public_html/_fbsession.inc';
require_once '/home/<USER>/public_html/_runonce.inc';

ob_implicit_flush(true);

if (already_running()) {
	if (DEBUG) {
		error_log('already running');
	}
	exit(1);
}

#fb_parse_likes(466777);
#exit;

$rc = main();

exit(show_messages_cli() || !$rc ? 1 : 0);

function main() {
	if (!FACEBOOK_ENABLED) {
		return true;
	}
	while (true) {
		if (DEBUG) {
			echo "quering\n";
		}
		$rc = parse_recently_visited();
		if (!$rc) {
			return false;
		}
		if (DEBUG) {
			echo "sleeping 1 second\n";
		}
		sleep(1);
		$date = getdate();
		if ($date['seconds'] >= 58) {
			# return and let cron start us again, so we memory is freed
			if (DEBUG) {
				echo "nearing new minute, quiting to allow restart\n";
			}
			break;
		}
	}
	return true;
}
function parse_recently_visited() {
	$no_auto = false;

	$spec = explain_table('facebook_background');
	if (!$spec) {
		return false;
	}

	# small delay to allow registration process to complete

	while ($todo = db_single_assoc('facebook_background',$q = '
		SELECT USERID,ACTION,CSTAMP,AUTO,
			(	SELECT MAX(DONESTAMP)
				FROM facebook_background AS bg2
				WHERE bg2.USERID=bg1.USERID
				  AND bg2.STATUS="done"
				  AND bg2.ACTION=bg1.ACTION
			) AS LAST_DONE_STAMP,
			COALESCE(SYNC_FRIENDS,1) AS SYNC_FRIENDS,
			COALESCE(SYNC_LIKES,1) AS SYNC_LIKES,
			COALESCE(SYNC_EVENTS,1) AS SYNC_EVENTS,
			`FORCE`,
			EXPIRES
		FROM facebook_background AS bg1
		LEFT JOIN fbsettings USING (USERID)
		LEFT JOIN facebook USING (USERID)
		LEFT JOIN facebook_token USING (FACEBOOKID)
		WHERE STATUS=""
		  AND CSTAMP<'.(CURRENTSTAMP - 60).'
		  AND NOTBEFORE<'.CURRENTSTAMP.
		  ($no_auto ? ' AND NOT AUTO' : null).'
		ORDER BY NOT AUTO DESC,CSTAMP DESC,USERID DESC
		LIMIT 1')
	) {
		extract($todo);

		if ($EXPIRES < CURRENTSTAMP) {
			if (!db_update('facebook_background','
				UPDATE facebook_background SET
					STATUS		="expired",
					DONESTAMP	='.time().'
				WHERE STATUS=""
				  AND USERID='.$USERID)
			) {
				return false;
			}
			continue;
		}
		while (true) {
			$past_hour = db_single('fbgraph_access','
				SELECT COUNT(*)
				FROM fbgraph_access
				WHERE STAMP>'.(time() - ONE_HOUR)
			);
			if ($past_hour === false) {
				return false;
			}
			if (DEBUG) {
				echo 'q'.$past_hour ?>..<?
			}
			if ($past_hour < 1000) {
				break;
			}
			if (DEBUG) {
				echo '.';
			}
			sleep(1);
		}

		if (DEBUG) {
			echo _datetime_get(time()).": doing $ACTION for user $USERID, created ",(CURRENTSTAMP - $CSTAMP)," second ago... ";
		}

		$newstatus = 'done';
		switch ($ACTION) {
		case 'parse_likes':
			if (!$SYNC_LIKES) {
				if (!$FORCE) {
					$newstatus = 'disabled';
					break;
				}
			} else {
				# max once every three days:
				if ($LAST_DONE_STAMP > (time() - GRACE_PERIOD)) {
					$newstatus = 'skipped';
					break;
				}
				if (!fb_have_permission('user_likes',$USERID)) {
					$newstatus = 'nopermission';
					break;
				}
			}
			$rc = fb_parse_likes($USERID);
			if ($rc === false) {
				error_log('parse_likes failed for user '.$USERID);
			}
			if (!$rc) {
				$newstatus = 'failed';
			}
			break;

		case 'parse_friends':
			if (!$SYNC_FRIENDS) {
				if (!$FORCE) {
					$newstatus = 'disabled';
					break;
				}
			} else {
				# max once every three days:
				if ($LAST_DONE_STAMP > (time() - GRACE_PERIOD)) {
					$newstatus = 'skipped';
					break;
				}
				if (!fb_have_permission('user_friends',$USERID)) {
					$newstatus = 'nopermission';
					break;
				}
			}
			if (false === fb_parse_friends($USERID)) {
				error_log('fb_parse_friends for user '.$USERID.' failed');
				$newstatus = 'failed';
				break;
			}
			break;
		case 'sync_fields':
			if ($LAST_DONE_STAMP > (time() - GRACE_PERIOD)) {
				$newstatus = 'skipped';
				break;
			}
			if (false === fb_sync_fields($USERID)) {
				error_log('fb_sync_fields for user '.$USERID.' failed');
				$newstatus = 'failed';
				break;
			}
			break;

		case 'get_pages':
			if ($LAST_DONE_STAMP > (time() - GRACE_PERIOD)) {
				$newstatus = 'skipped';
				break;
			}
			if (false === fb_get_pages($USERID)) {
				error_log('fb_get_pages for user '.$USERID.' failed');
				$newstatus = 'failed';
				break;
			}
			break;

		default:
		        error_log('unsupported action: '.$ACTION);
		        if (DEBUG) {
		        	echo "unsupported action\n";
		        }
			return false;
		}
		if (DEBUG) {
			echo "$newstatus\n";
		}
		if ($newstatus == 'failed') {
			if (!db_update('facebook_background','
				UPDATE facebook_background SET
					STATUS		="'.$newstatus.'",
					DONESTAMP	='.time().'
				WHERE STATUS=""
				  AND CSTAMP='.$CSTAMP.'
				  AND ACTION="'.$ACTION.'"
				  AND USERID='.$USERID)
			) {
				return false;
			}
		}
		if (!db_update('facebook_background','
			UPDATE facebook_background SET
				STATUS		="'.$newstatus.'",
				DONESTAMP	='.time().'
			WHERE STATUS=""
			  AND ACTION="'.$ACTION.'"
			  AND USERID='.$USERID)
		) {
			return false;
		}
	}
	# remove old entries:
	unset($spec['STATUS']->enum['']);
	if (!db_delete('facebook_background','
		DELETE FROM facebook_background
		WHERE STATUS IN ('.stringsimplode(',',$spec['STATUS']->enum).')
		  AND CSTAMP<'.(CURRENTSTAMP - KEEP_LOGS))
	) {
		return false;
	}
	return true;
}
