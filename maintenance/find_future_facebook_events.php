#!/usr/bin/php
<?php

require_once 'vendor/autoload.php';

# use Facebook\WebDriver\Chrome\ChromeOptions;
# use Facebook\WebDriver\Remote\DesiredCapabilities;
use Facebook\WebDriver\Remote\RemoteWebDriver;

define('CURRENTUSERID',		0);
define('CURRENTSTAMP',		time());
define('DEBUG',			!isset($_SERVER['CRON']));
define('DRYRUN',		false);

require_once '/home/<USER>/public_html/_db.inc';
require_once '/home/<USER>/public_html/_fb.inc';
require_once '/home/<USER>/public_html/_urlcheck.inc';
require_once '/home/<USER>/public_html/_runonce.inc';

const NEED_EVENT_PERIOD		= 2 * ONE_YEAR;	# item needs at least one event in his period o be checked
const CHECK_PERIOD		= ONE_WEEK;	# check pages once a week (delay in process lists is two weeks, so we're always earlier)
const MAX_RUN_TIME		= FIVE_MINUTES;

$start_stamp = time();

if (!openlog(
	'find_future_facebook_events',
	(defined('DRYRUN') && DRYRUN ? 0 : LOG_PID) | (isset($_SERVER['CRON']) ? 0 : LOG_PERROR),
	LOG_USER)
) {
	exit(1);
}

if (already_running()) {
	syslog(LOG_INFO, 'already running');
	exit(0);
}

syslog(LOG_INFO, 'started');

ob_implicit_flush();

$ok = main();

syslog(LOG_INFO, 'done');

exit(show_messages_cli() || !$ok ? 1 : 0);

function main(): bool {
	if (have_number($GLOBALS['argv'], 1)) {
		$fbid = $GLOBALS['argv'][1];
		$items = db_rowuse_array('fbid', '
			SELECT DISTINCT FBID
			FROM fbid
			LEFT JOIN fbid_identical USING (FBID)
			WHERE fbid_identical.FBID IS NULL
			  AND FBID = '.$fbid.'
			GROUP BY FBID'
		);
		if (!$items) {
			if ($items === false) {
				return false;
			}
			syslog(LOG_ERR, 'could not find item belonging to fbid '.$fbid);
			return false;
		}
		$fbids = [$fbid];
	} else {
		$location_fbids = db_simpler_array(['fbid', 'fbid_identical', 'future_facebook_events_check', 'party'],  '
			SELECT DISTINCT FBID
			FROM fbid
			JOIN party ON LOCATIONID = ID
			LEFT JOIN fbid_identical USING (FBID)
			LEFT JOIN future_facebook_events_check USING (FBID)
			WHERE fbid_identical.FBID IS NULL
			  AND ELEMENT = "location"
			  AND party.STAMP > '.(CURRENTSTAMP - NEED_EVENT_PERIOD).'
			  AND (    LAST_CHECK IS NULL
			  	OR LAST_CHECK < '.(CURRENTSTAMP - CHECK_PERIOD).'
			      )'
		);
		if ($location_fbids === false) {
		 	return false;
		}
		if (false === ($organization_fbids = db_simpler_array(['fbid', 'fbid_identical', 'future_facebook_events_check', 'connect'], $q = '
			SELECT DISTINCT FBID
			FROM fbid
			LEFT JOIN fbid_identical USING (FBID)
			LEFT JOIN future_facebook_events_check USING (FBID)
			JOIN connect
			  ON MAINTYPE = "organization"
			 AND MAINID = ID
			 AND ASSOCTYPE = "party"
			JOIN party FORCE KEY (STAMP)
			  ON PARTYID = ASSOCID
			WHERE fbid_identical.FBID IS NULL
			  AND ELEMENT = "organization"
			  AND party.STAMP > '.(CURRENTSTAMP - NEED_EVENT_PERIOD).'
			  AND (    LAST_CHECK IS NULL
			  	OR LAST_CHECK < '.(CURRENTSTAMP - CHECK_PERIOD).'
			      )'
		))) {
			return false;
		}
		mail_log('re-evaluate forced key', item: $q);

/*		syslog(LOG_INFO, 'processing '.count($location_fbids).' locations and '.count($organization_fbids).' organizations');

		if ($both_fbids = array_intersect_assoc($location_fbids, $organization_fbids)) {
			syslog(LOG_ERR, 'fbids between locations and organizations interesect');
			print_rr($both_fbids, 'fbids between locations and organizations intersect');
			print_rr($location_fbids, 'location_fbids');
			print_rr($organization_fbids, 'organization_fbids');
			return false;
		}*/

		$fbids = array_merge($location_fbids, $organization_fbids);
	}

	foreach ($fbids as $fbid) {
		$page_id = null;
		$webdriver = null;
		$todo = store_future_facebook_events($webdriver, $fbid, $page_id);

		if (is_int($todo)) {
			foreach (($page_id ? [$page_id, $fbid] : [$fbid]) as $tmp_fbid) {
				if (!db_replace('future_facebook_events_check', '
					REPLACE INTO future_facebook_events_check SET
						LAST_CHECK	= UNIX_TIMESTAMP(),
						FBID		= '.$tmp_fbid.',
						OK		= '.($todo === false ? '0' : '1').',
						RECOGNIZED	= '.($todo === null ? '0' : '1').',
						NO_NEW		= '.($todo === 0 ? '1' : '0'))
				||	$todo === false
				) {
					return false;
				}

				$proclist_done = false;

				if (is_int($todo)) {
					syslog(LOG_INFO, 'marking '.$tmp_fbid.' as 200 for urlcheck');

					if (!update_urlcheck_fbid($tmp_fbid, 200)) {
						return false;
					}

					if ($todo === 0) {
						syslog(LOG_INFO, 'marking proclist done');
					}

					if ($todo === 0
					&&	!($proclistdone = db_replace('proclist_done', '
						REPLACE INTO proclist_done (ELEMENT, ID, STAMP, USERID, FUTURE_CNT)
						SELECT ELEMENT, ID, UNIX_TIMESTAMP(), 0, 0
						FROM fbid
						WHERE ELEMENT IN ("organization", "location", "artist")
						  AND FBID = '.$tmp_fbid))
					) {
						return false;
					}
				}
				if ($proclist_done) {
					syslog(LOG_INFO, 'marked items done for fbid '.$tmp_fbid);
				}
			}
		}
		$current_stamp = time();

		$run_time = $current_stamp - $GLOBALS['start_stamp'];

		if ($run_time >= MAX_RUN_TIME) {
			syslog(LOG_INFO, 'quiting after running for '.$run_time.' seconds');
			break;
		}
		syslog(LOG_INFO, 'sleeping, ran for '.$run_time.' seconds');
		sleep(60);
	}
	if (!empty($webdriver)) {
		syslog(LOG_INFO, 'quiting webdriver with sessionid '.$webdriver->getSessionID());
		$webdriver->quit();
	}
	return true;
}

# int: number of future events to check at least
# null: something failed in recognition, continue with next url
# false: something failed that really shouldn't have

function store_future_facebook_events(?RemoteWebDriver $webdriver, int $fbid, ?int &$page_id = null): int|false|null {
	syslog(LOG_INFO, 'checking '.$fbid.' for events');

	$data = get_facebook_data($webdriver, $path = '/'.$fbid.'/events', syslog: true, force_login: true);
#	file_put_contents('facebook_dump.html', $data);
#	$path = '/'.$fbid.'/events';
#	$data = file_get_contents('facebook_dump.html');

	$presenceids = db_simpler_array('fbid', '
		SELECT DISTINCT ID
		FROM fbid
		WHERE ELEMENT = "presence"
		  AND FBID = '.$fbid
	);
	if ($presenceids === false) {
		return false;
	}
	if (!$presenceids) {
		$presenceids = [0];
	}
	foreach ($presenceids as $presenceid) {
		$get_type = null;
		$page_id = null;
		$fbid = get_fbid_from_facebook_html(
			$path,
			$data,
			get_type: $get_type,
			found_http_status:	$http_status,
			presenceid:				$presenceid,
			page_id:				$page_id
		);
		syslog(LOG_INFO, "got fbid $fbid (role $get_type)".($page_id ? " and pageid $page_id" : '')." from presence $presenceid");

		if ($http_status === 404
		||	$http_status === 419
		) {
			syslog(LOG_INFO, "marking $fbid as $http_status for urlcheck");

			if (!update_urlcheck_fbid($fbid, $http_status)) {
				return false;
			}
		}
	}

	if (!$data) {
		syslog(LOG_ERR, 'got no data from facebook');
		return false;
	}

	$page_id = 0;
	if (preg_match($re = '!{"id":"'.$fbid.'","delegate_page_id":"(?P<fbid>\d+)"!u', $data, $delegate_page_match)) {
		syslog(LOG_INFO, 'found delegate page: '.$delegate_page_match['fbid']);
		$page_id = (int)$delegate_page_match['fbid'];
	}

	if (str_contains($data, 'Geen geplande evenementen')) {
		syslog(LOG_INFO, 'no planned events (1)');
		return 0;
	}

	$doc = new DOMDocument();
	@$doc->loadHTML($data, LIBXML_COMPACT | LIBXML_NOWARNING);

	$xpath = new DOMXpath($doc);

	$h2s = $xpath->query('//h2');
	if (!$h2s->length) {
		syslog(LOG_ERR, 'no h2 found');
		return null;
	}
	$events_header = null;
	foreach ($h2s as $h2) {
		syslog(LOG_DEBUG, 'found h2: '.$h2->textContent);

		if (($box_type = 1) && $h2->textContent === 'Evenementen'
		||	($box_type = 2) && $h2->textContent === 'Geplande evenementen'
		) {
			$events_header = $h2;
		 	break;
		}
	}

	if (!$events_header) {
		syslog(LOG_INFO, 'could not find events_header');
		return null;
	}
	if ($box_type === 1) {
		$event_box = $events_header->parentNode->parentNode->parentNode->parentNode;

		$tab_header = $event_box->nextSibling;

		syslog(LOG_DEBUG, 'tab_header: '.$tab_header->textContent);

		if ($tab_header->textContent === 'AfgelopenMeerAfgelopen') {
			syslog(LOG_INFO, 'no planned events (2)');
			return 0;
		}

		$agenda_box = $event_box->nextSibling->nextSibling;
	} else {
		$event_box = $events_header->parentNode->parentNode->parentNode->parentNode->parentNode->parentNode;

		$agenda_box = $event_box->parentNode;
	}

	# why does this return a dollar sign?
	if ($agenda_box->textContent === '$') {
		$agenda_box = $event_box->parentNode;
	}

	syslog(LOG_DEBUG, 'agenda_box: '.$agenda_box->nodeValue);
	syslog(LOG_DEBUG, 'event_box sibling: '.$event_box->nextSibling->textContent);
	syslog(LOG_DEBUG, 'event_box super: '.$event_box->parentNode->textContent);

	$event_anchors = $xpath->query('.//a', $agenda_box);
	if (!$event_anchors->length) {
		syslog(LOG_ERR, 'no event anchors found');
		return null;
	}

	$events = [];
	foreach ($event_anchors as $event_anchor) {
		$href = $event_anchor->getAttribute('href');
		if (preg_match('~https://www\.facebook\.com/events/(?P<eventid>\d+)/~', $href, $match)) {
			$eventid = (int)$match['eventid'];
			$have = db_single('fbid', 'SELECT 1 FROM fbid WHERE FBID = '.$eventid);
			if ($have === false) {
				return false;
			}
			$ignored = db_single('fbid', 'SELECT 1 FROM facebook_ignore WHERE FBID = '.$eventid);
			if ($ignored === false) {
				return false;
			}

			$box = $event_anchor->parentNode->parentNode->parentNode->parentNode->parentNode;

			syslog(LOG_DEBUG, 'date: '.$box->textContent);

			$date_box = $box->firstChild;

			syslog(LOG_DEBUG, 'date_box: '.substr($date_box->textContent, 0, 100));

			$events[$eventid] = [
				'fbid'		=> $eventid,
				'name'		=> $event_anchor->textContent,
				'date'		=> strtotime($date_box->textContent),
				'have'		=> (bool)$have,
				'ignored'	=> (bool)$ignored,
			];
		}
	}

	if (!$events) {
		syslog(LOG_INFO, 'no events found');
		return 0;
	}

	$todo = 0;
	$setlist = [];
	print_rr($events, 'events');

	foreach ($events as $event) {
		$setlist[$fbid] = '('.$fbid.', '.$event['fbid'].', '.($event['have'] ? '1' : '0').', '.($event['ignored'] ? '1' : '0').')';
		if (!$event['have']
		||	!$event['ignored']
		) {
			++$todo;
		}
	}
	if (!db_insert('future_facebook_events_found', '
		REPLACE INTO future_facebook_events_found (FBID, EVENTID, HAVE, IGNORED)
		VALUES '.implode(', ', $setlist))
	) {
		return false;
	}
	syslog(LOG_INFO, 'found '.$todo.' '.($todo > 1 ? 'events' : 'event'));
	return $todo;
}
