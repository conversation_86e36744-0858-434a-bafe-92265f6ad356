#!/usr/bin/php
<?php

define('CURRENTSTAMP',time());
define('DEBUG',true);
#define('DEBUG',!isset($_SERVER['CRON']));
#define('DRYRUN',true);

define('NEXT_CHECK',	7*24*3600);

require_once '/home/<USER>/public_html/_exit_if_readonly.inc';
require_once '/home/<USER>/public_html/_db.inc';
require_once '/home/<USER>/public_html/_date.inc';
require_once '/home/<USER>/public_html/_pagechanged.inc';
require_once '/home/<USER>/public_html/_require.inc';
require_once '/home/<USER>/public_html/defines/facebook_cookie.inc';
require_once 'ipresolve.inc';

ob_implicit_flush(true);
$rc = main();
exit(show_messages_cli() || !$rc);

function main() {
	$ch = curl_init();
	curl_setopt_array($ch,[
		CURLOPT_ACCEPT_ENCODING	=> '',
		CURLOPT_RETURNTRANSFER	=> true,
		CURLOPT_FOLLOWLOCATION	=> true,
		CURLOPT_MAXREDIRS		=> 10,
		CURLOPT_CONNECTTIMEOUT	=> 20,
		CURLOPT_TIMEOUT			=> 20,
		CURLOPT_SSL_VERIFYPEER	=> false,
		CURLOPT_COOKIESESSION	=> true,

		CURLOPT_COOKIEFILE	=> '/tmpdisk/urlchecker.cookies',
		CURLOPT_COOKIEJAR	=> '/tmpdisk/urlchecker.cookies',
	]);

#	$fbid = 80888740448;
#	list($errno,$data,$info) = get_facebook_data($ch,'/events/'.$fbid);
#	print_rr($data);
#	exit;

	$notime_events = db_rowuse_array(['presence','party','fbid'],'
		SELECT PARTYID,FBID
		FROM party
		JOIN fbid ON ELEMENT="party" and ID=PARTYID
		WHERE NOTIME
		ORDER BY PARTYID DESC'
	);

#	print_rr($notime_events);
#	exit;

	if (!$notime_events) {
		return $notime_events === false ? false : true;
	}
	foreach ($notime_events as $party) {
		extract($party);
		if (DEBUG) {
			echo "getting facebook event name for event $PARTYID, $FBID: ";
		}
		list($errno,$data,$info) = get_facebook_data($ch,'/events/'.$FBID);

		if (false !== strpos($data,'De door jou gevolgde link kan zijn verlopen')) {
			echo "bad event\n";
			sleep(2);
			continue;
		}

		if (preg_match('" van <span>(\d+:\d+)</span> tot <span>(\d+:\d+)</span>"',$data,$match)) {
			$party = memcached_party_and_stamp($PARTYID);

			list(,$start,$stop) = $match;

			list($start_hour,$start_mins) = explode(':',$start);
			list($stop_hour,$stop_mins) = explode(':',$stop);

			change_timezone($party['TIMEZONE']);

			$at2400 = false;

			if ($start_mins == 59) {
				$start_mins = 0;
				if ($start_hour == 23) {
					$start_hour = 0;
					$at2400 = true;
				} else {
					++$start_hour;
				}
			}

			list($start_year,$start_month,$start_day) = _getdate($party['STAMP']);
			$start_stamp = mktime($start_hour,$start_mins,0,$start_month,$start_day,$start_year);

			change_timezone('UTC');
			$start_stamp_tzi = mktime($start_hour,$start_mins,0,$start_month,$start_day,$start_year);
			change_timezone();

			if ($start_hour > $stop_hour) {
				$stop_stamp = strtotime('+1 day '.$stop,$start_stamp);
			} else {
				$stop_stamp = mktime($stop_hour,$stop_mins,0,$start_month,$start_day,$start_year);
			}
			echo "found: ",_datetime_get($start_stamp).' - '._datetime_get($stop_stamp),"\n";

			if (!db_insert('party_log','
				INSERT INTO party_log
				SELECT * FROM party
				WHERE PARTYID='.$PARTYID)
			||	!db_update('party','
				UPDATE party SET
					AT2400		='.($at2400 ? 1 : 0).',
					NOTIME		=0,
					STAMP		='.$start_stamp.',
					STAMP_TZI	='.$start_stamp_tzi.',
					DURATION_SECS	='.($stop_stamp - $start_stamp).',
					MUSERID		=0,
					MSTAMP		='.time().'
				WHERE PARTYID='.$PARTYID)
			) {
				return false;
			}

			change_timezone();

			page_changed('party',$PARTYID);

		} else {
			echo "no time found\n";
		}

		sleep(2);
	}
	return true;
}
function get_facebook_data($ch,$path) {
	static $__fbcookie = 0;
	if ($__fbcookie === 0) {
		$__fbcookie = get_fbcookie();
	}
	curl_setopt_array($ch, [
		CURLOPT_URL			=> 'https://www.facebook.com'.$path,
		CURLOPT_COOKIE		=> $__fbcookie,
		CURLOPT_IPRESOLVE	=> RESOLVE_FB_CURL
	]);

	curl_setopt($ch, CURLOPT_HTTPHEADER, ['User-Agent: '.USER_AGENT_BASE]);
	$data = curl_exec($ch);
	$info = curl_getinfo($ch);
	$ch_errno = $info['http_code'];

	return [$ch_errno,$data,$info];
}
