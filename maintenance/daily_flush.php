#!/usr/bin/php
<?php

declare(strict_types=1);

define('CURRENTSTAMP',	time());

#const DEBUG = true;

require_once __DIR__.'/../public_html/_db.inc';
require_once __DIR__.'/../public_html/_run_and_exit.inc';

run_and_exit(run_once: true, syslog: true);

function main(): bool {
	$ok = true;
	foreach ([
		local_deownerize_tickets(...),
		flush_complete_mail_messages(...),
		flush_directmessage_day_infos(...),
		flush_flock_invitation_day_infos(...),
		flush_expired_locks(...),
		flush_facebook_ignores(...),
		flush_flockbot_notifications(...),
		flush_form_secrets(...),
		flush_identities(...),
		flush_mail_hash(...),
		flush_resolutions(...),
		flush_ticket_feed(...),
		flush_url_checker_facebook_hits(...),
		flush_user_list_cache(...),
		flush_user_sessions(...),
	] as $flusher) {
		# try to keep executing flush functions if one of the flush functions return false
		if (!$flusher()) {
			$ok = false;
		}
		# but quit flush functions if we cannot properly wait
		if (!waitforslaves()) {
			syslog(LOG_ERR, 'wait_for_replicas failed');
			return false;
		}
	}
	return $ok;
}

function flush_url_checker_facebook_hits(): bool {
	/** @noinspection SpellCheckingInspection */
	return log_flush(
		__FUNCTION__,
		'urlchecker_fbhit',
		db_delete('urlchecker_fbhit', '
			DELETE LOW_PRIORITY FROM urlchecker_fbhit
			WHERE STAMP < '.(CURRENTSTAMP - 2 * ONE_DAY)));
}

function flush_ticket_feed(): bool {
	/** @noinspection SpellCheckingInspection */
	return log_flush(
		__FUNCTION__,
		'ticketfeed',
		db_delete_in_parts('ticketfeed', '
			DELETE LOW_PRIORITY FROM ticketfeed
			WHERE STAMP < '.(CURRENTSTAMP - ONE_MONTH),
			part_size: 10));
}

function flush_flockbot_notifications(): bool {
	return	log_flush(
		__FUNCTION__,
		'flockbot_notification',
		db_delete_in_parts('flockbot_notification', '
			DELETE LOW_PRIORITY FROM flockbot_notification
			WHERE DONE = 1
			  AND STAMP < '.(CURRENTSTAMP - 2 * ONE_MONTH),
			part_size: 10));
}

function flush_user_sessions(): bool {
	return log_flush(
		__FUNCTION__,
		'user_session',
		db_delete_in_parts('user_session','
			DELETE LOW_PRIORITY FROM user_session
			WHERE LAST_USED < '.(CURRENTSTAMP - ONE_YEAR)));
}

function flush_resolutions(): bool {
	return log_flush(
		__FUNCTION__,
		'resolution',
		db_delete_in_parts('resolution','
			DELETE LOW_PRIORITY FROM resolution
			WHERE STAMP < '.(CURRENTSTAMP - ONE_YEAR),
			part_size: 10));
}

function flush_expired_locks(): bool {
	require_once __DIR__.'/../public_html/_lock.inc';
	return log_flush(
		__FUNCTION__,
		'locks',
			create_lock_log('expire', CURRENTSTAMP - ONE_MONTH)
		&&	db_delete_in_parts('locks', '
			DELETE LOW_PRIORITY FROM locks
			WHERE LOCK_EXPIRES < '.(CURRENTSTAMP - ONE_MONTH),
			part_size: 10));
}

function flush_form_secrets(): bool {
	/** @noinspection SpellCheckingInspection */
	return log_flush(
		__FUNCTION__,
		'formsecretv2',
		db_delete_in_parts('formsecretv2','
			DELETE LOW_PRIORITY FROM formsecretv2
			WHERE EXPIRES < '.(CURRENTSTAMP - ONE_DAY),
			part_size: 10));
}

function flush_complete_mail_messages(): bool {
	/** @noinspection SpellCheckingInspection */
	if (!($ctmsgids = db_simpler_array(['completemsg', 'contact_ticket_mail', 'contact_ticket_message', 'contact_ticket'], '
		SELECT CTMSGID
		FROM completemsg
		LEFT JOIN contact_ticket_mail AS ctmail USING (CTMSGID)
		LEFT JOIN contact_ticket_message AS ctmsg USING (CTMSGID)
		LEFT JOIN contact_ticket AS ct ON ct.TICKETID = ctmsg.TICKETID
		WHERE ctmail.CTMSGID IS NULL
		   OR ctmsg.CTMSGID IS NULL
		   OR (	(	ct.STATUS = "closed"
		   		OR	ELEMENT LIKE "junk%"
		   		)
			AND ctmsg.CSTAMP < '.(CURRENTSTAMP - 3 * ONE_MONTH).'
			AND	   ct.MSTAMP < '.(CURRENTSTAMP - 3 * ONE_MONTH).'
		      )',
		DB_FORCE_MASTER
	))) {
		return $ctmsgids !== false;
	}
	$ctmsgid_str = implode(', ', $ctmsgids);
	/** @noinspection SpellCheckingInspection */
	return log_flush(
		__FUNCTION__,
		'completemsg',
		db_delete_in_parts('completemsg', "
			DELETE LOW_PRIORITY FROM completemsg
			WHERE CTMSGID IN ($ctmsgid_str)",
			part_size: 10));
}

function flush_user_list_cache(): bool {
	/** @noinspection SpellCheckingInspection */
	return log_flush(
		__FUNCTION__,
		'userlist_cache',
		db_delete_in_parts('userlist_cache', '
			DELETE LOW_PRIORITY FROM userlist_cache
			WHERE LASTUSE < '.(CURRENTSTAMP - ONE_DAY),
			part_size: 10));
}

function flush_mail_hash(): bool {
	/** @noinspection SpellCheckingInspection */
	return log_flush(
		__FUNCTION__,
		'mailhash',
		db_delete_in_parts('mailhash', '
			DELETE LOW_PRIORITY FROM mailhash
			WHERE STAMP < '.(CURRENTSTAMP - ONE_DAY),
			part_size: 10));
}

function flush_identities(): bool {
	/** @noinspection SpellCheckingInspection */
	return log_flush(
		__FUNCTION__,
		'identityv4',
		db_delete_in_parts('identityv4', '
			DELETE LOW_PRIORITY FROM identityv4
			WHERE LAST_HERE < '.(CURRENTSTAMP - 2 * ONE_YEAR),
			part_size: 10));
}

function flush_facebook_ignores(): bool {
	return log_flush(
		__FUNCTION__,
		'facebook_ignore',
		db_delete_in_parts('facebook_ignore', '
			DELETE LOW_PRIORITY FROM facebook_ignore
			WHERE TMPED
			  AND TMPED < '.(CURRENTSTAMP - ONE_WEEK),
			part_size: 10));
}

function flush_directmessage_day_infos(): bool {
	/** @noinspection SpellCheckingInspection */
	return log_flush(
		__FUNCTION__,
		'directmessagedayinfo',
		db_delete_in_parts('directmessagedayinfo', '
			DELETE LOW_PRIORITY FROM directmessagedayinfo
			WHERE STAMP < '.(CURRENTSTAMP - 3 * ONE_DAY),
			part_size: 10));
}

function flush_flock_invitation_day_infos(): bool {
	/** @noinspection SpellCheckingInspection */
	return log_flush(
		__FUNCTION__,
		'flockinvitationdayinfo',
		db_delete_in_parts('flockinvitationdayinfo', '
			DELETE LOW_PRIORITY FROM flockinvitationdayinfo
			WHERE STAMP < '.(CURRENTSTAMP - 3 * ONE_DAY),
			part_size: 10));
}

function local_deownerize_tickets(): bool {
	require_once __DIR__.'/../public_html/_deownerize.inc';
	return log_flush(
		__FUNCTION__,
		'contact_ticket',
		deownerize_tickets(),
		'deownerize');
}

function log_flush(
	string	$function,
	string	$table,
	bool	$ok,
	string	$action = 'delete',
	?int	$count  = null,
): bool {
	if (!$ok) {
		syslog_error(LOG_ERR, $function.':'.$action.' failed!');
	} else {
		syslog(LOG_INFO, $function.': '.$action.'d '.($count ?? db_affected()).' rows from '.$table);
	}
	return $ok;
}
