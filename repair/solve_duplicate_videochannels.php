#!/usr/bin/php
<?php

if (PHP_SAPI !== 'cli') {
	exit;
}

const DEBUG = true;
const DRYRUN = false;

define('CURRENTSTAMP', time());

require_once '/home/<USER>/public_html/_db.inc';
require_once '/home/<USER>/public_html/_presale.inc';
require_once '/home/<USER>/public_html/_run_and_exit.inc';

run_and_exit(syslog: true);

function main(): bool {
	if (!($multiples =  db_rowuse_array('videochannel', '
			SELECT GROUP_CONCAT(NAME SEPARATOR "|") AS NAMES,
				   GROUP_CONCAT(CHANNELID SEPARATOR ",") AS CHANNELIDS,
				   TYPE, ID, COUNT(*) AS COUNT
			FROM videochannel
			WHERE TYPE = "youtube"
			  AND ID != ""
			GROUP BY ID
			HAVING COUNT(*) > 1'))) {
		syslog(LOG_INFO, 'nothing to be done, no doube');
		return $multiples !== false;
	}
	foreach ($multiples as $multiple) {

		extract($multiple);

		syslog(LOG_INFO, "processing type: $TYPE, id: $ID");

		$channelids = explode(',', $CHANNELIDS);
		
		sort($channelids);
		
		syslog(LOG_INFO, "found $COUNT channels: ".implode(', ', $channelids));
		
		$keepid = array_shift($channelids);
	
		syslog(LOG_INFO, 'keeping channel '.$keepid);
		
/*		$set_name = '';
			
		foreach(explode(chr(5), $NAMES) as $name) {
			if ($name) {
				$set_name = $name;
				break;
			}
		}
		
		foreach ($channelids as $channelid) {
			$combined[] = '("videochannel", '.CURRENTSTAMP.', '.$keepid.', '.$channelid.', 2269)';
		}*/
		
		$delete_idstr = implode(', ', $channelids);

		if (
#			!db_insert('videochannel_log', '
#			INSERT INTO videochannel_log
#			SELECT * FROM videochannel
#			WHERE CHANNELID IN ('.$delete_idstr.')')
			!db_insert('combined', '
			INSERT IGNORE  INTO combined (ELEMENT, STAMP, NEWID, OLDID, USERID)
			SELECT "videochannel", UNIX_TIMESTAMP(), '.$keepid.', CHANNELID, 0
			FROM videochannel
            WHERE CHANNELID IN ('.$delete_idstr.')')
#		||	!db_insert('deleted', '
#			INSERT INTO deleted (ELEMENT, ID, DUSERID, DSTAMP)
#			SELECT "videochannel", CHANNELID, 0, '.time().'
#			FROM videochannel
#           WHERE CHANNELID IN ('.$delete_idstr.')')
		||	!db_update('video', '
			UPDATE video SET CHANNELID = '.$keepid.'
			WHERE CHANNELID IN ('.$delete_idstr.')')
		||	!db_update('video_log', '
			UPDATE video_log SET CHANNELID = '.$keepid.'
			WHERE CHANNELID IN ('.$delete_idstr.')')
		||	!db_update('videochannel_log', '
			UPDATE IGNORE videochannel_log SET CHANNELID = '.$keepid.'
			WHERE CHANNELID IN ('.$delete_idstr.')')
		||	!db_update('videochannel_presence_added', '
			UPDATE IGNORE videochannel_presence_added SET CHANNELID = '.$keepid.'
			WHERE CHANNELID IN ('.$delete_idstr.')')
		||	!db_delete('videochannel_presence_added', '
			DELETE FROM videochannel_presence_added
			WHERE CHANNELID IN ('.$delete_idstr.')')
		||	!db_update('videochannelcheck', '
			UPDATE IGNORE videochannelcheck SET CHANNELID = '.$keepid.'
			WHERE CHANNELID IN ('.$delete_idstr.')')
		||	!db_delete('videochannelcheck', '
			DELETE FROM videochannelcheck
			WHERE CHANNELID IN ('.$delete_idstr.')')
		||	!db_delete('videochannel', '
			DELETE FROM videochannel
			WHERE CHANNELID IN ('.$delete_idstr.')')
		||	!db_delete('videochannel_log', '
			DELETE FROM videochannel_log
			WHERE CHANNELID IN ('.$delete_idstr.')')
		||	!db_update('connect', '
			UPDATE IGNORE connect SET MAINID = '.$keepid.'
			WHERE MAINTYPE = "videochannel"
			  AND MAINID IN ('.$delete_idstr.')')
		||	!db_update('connect', '
			UPDATE IGNORE connect SET ASSOCID = '.$keepid.'
			WHERE ASSOCTYPE = "videochannel"
			  AND ASSOCID IN ('.$delete_idstr.')')
		||	!db_delete('connect', '
			DELETE FROM connect
			WHERE MAINTYPE = "videochannel"
			  AND MAINID IN ('.$delete_idstr.')')
		||	!db_delete('connect', '
			DELETE FROM connect
			WHERE ASSOCTYPE = "videochannel"
			  AND ASSOCID IN ('.$delete_idstr.')')
#		||	!db_insert('combined', '
#			INSERT INTO combined (ELEMENT, STAMP, NEWID, OLDID, USERID)
#			VALUES '.implode(', ', $combined))
		) {
			return false;
		}
	}
	return true;
}

	
