#!/usr/bin/php
<?php

require_once '/home/<USER>/public_html/_offline.inc';
require_once '/home/<USER>/public_html/_date.inc';
require_once '/home/<USER>/public_html/_db.inc';
require_once '/home/<USER>/public_html/_memcache.inc';
require_once '/home/<USER>/public_html/_require.inc';
require_once '/home/<USER>/public_html/_topicactions.inc';
require_once '/home/<USER>/public_html/_comment.inc';

#define('DRYRUN',true);

define('IDSTEP',	10000);
define('CURRENTSTAMP',	time());
define('TODAYSTAMP',	mktime(0,0,0));
define('CURRENTUSERID',	0);
define('CURRENTDAYNUM',	to_days());
define('NEXT',		"\x1B[4D, ");
define('NEXTSIZE',	2);
define('ISRESULT',	"\x1B[4D = ");
define('GO',		'... ');
define('GOSIZE',	4);
define('NEWLINE',	"\n");
define('RESETLINE',	"\n\x1B[A\x1B[K");
function BACKSTR($arg) {
	if (!$arg) {
		return;
	}
	BACK(strlen($arg));
}
function BACK($num) {
	if (!$num) {
		return;
	}
	echo "\x1B[",$num,"D\x1B[K";
}

main();

ob_implicit_flush(true);
if (isset($GLOBALS['__error'])) {
	echo "failed:\r\n\r\n";
	foreach ($GLOBALS['__error'] as $errstr) {
		echo $errstr,"\r\n";
	}
}
function main() {
	foreach (commentables() as $parent => $flags) {
		if ($parent != 'albumelement') {
			continue;
		}
		echo 'processing ',$parent,GO;
		$table = $parent.'_comment';
		$offset = 0;
		$backstr = NEXT;
		$updates = 0;
		$inserts = 0;
		while ($ids = db_simpler_array($table,'SELECT DISTINCT ID FROM '.$table.' ORDER BY ID ASC LIMIT '.$offset.','.IDSTEP,DB_FORCE_SERVER,'dbbm')) {
			foreach ($ids as $id) {
				echo $backstr,($checkstr = 'checking id:'.$id.', '.$inserts.' inserted, '.$updates.' updated, getting totals'),GO;
				$totals = db_simple_hash($table,'
					SELECT TO_DAYS(FROM_UNIXTIME(CSTAMP)) AS DAYNUM,COUNT(*)
					FROM '.$table.'
					WHERE CSTAMP<'.TODAYSTAMP.'
					  AND ID='.$id.'
					GROUP BY DAYNUM',
					DB_FORCE_SERVER,
					'dbbm'
				);
				if ($totals === false) {
					return;
				}
				if (!$totals) {
					$backstr = BACK(strlen($checkstr) + GOSIZE);
					continue;
				}
				echo NEXT,($morestr = 'done, get existing'),GO;
				$existing = db_simple_hash('comment_counter','SELECT DAYNUM,CNT FROM comment_counter WHERE ELEMENT="'.$parent.'" AND ID='.$id.' AND DAYNUM<'.CURRENTDAYNUM,DB_USE_MASTER);
				if ($existing === false) {
					return;
				}
				$insert = array();
				$update = array();
				foreach ($totals as $daynum => $cnt) {
					if (!isset($existing[$daynum])) {
						$insert[] = '("'.$parent.'",'.$id.','.$daynum.','.$cnt.')';
					} elseif ($existing[$daynum] != $cnt) {
						$update[$daynum] = 'WHEN '.$daynum.' THEN '.$cnt;
					}
				}
				echo NEXT,($existstr = 'done');
				$insstr = null;
				$updstr = null;
				if ($insert) {
					echo ($insstr = 'inserting '.($cnt = count($insert)).' entries'),GO;
					$inserts += $cnt;
					if (!db_insert('comment_counter','
						INSERT INTO comment_counter (ELEMENT,ID,DAYNUM,CNT)
						VALUES '.implode(',',$insert))
					) {
						return;
					}
					echo NEXT;
				}
				if ($update) {
					echo ($updstr = 'updating '.($cnt = count($update)).',entries'),GO;
					$updates += $cnt;
					if (!db_update('comment_counter','
						UPDATE comment_counter SET CNT=CASE DAYNUM '.implode(' ',$update).' END
						WHERE ELEMENT="'.$parent.'"
						  AND ID='.$id.'
						  AND DAYNUM IN ('.implode(',',array_keys($update)).')')
					) {
						return;
					}
					echo NEXT;
				}
				if ($insert || $update) {
					echo ($donestr = 'done');
					echo "\n";
				} else {
					echo ($donestr = 'no updates required');
				}
				$backstr = BACK(strlen($checkstr) +
						strlen($morestr) +
						strlen($existstr) +
						($updstr ? strlen($updstr) + 2 : 0) +
						($insstr ? strlen($insstr) + 2 : 0) +
						strlen($donestr) + 2*2 /* 4 times a NEXT */ );
			}
			$offset += IDSTEP;
		}
		echo $backstr,
			'done, ',($inserts ? ($inserts == 1 ? '1 insert' : $inserts.' inserts') : 'no inserts'),
			 ' and ',($updates ? ($updates == 1 ? '1 update' : $updates.' updates') : 'no updates'),NEWLINE;

		if ($ids === false) {
			return;
		}
	}
}
