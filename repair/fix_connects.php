#!/usr/bin/php
<?php

require_once '/home/<USER>/public_html/_offline.inc';
require_once '/home/<USER>/public_html/_db.inc';
require_once '/home/<USER>/public_html/_helper.inc';
require_once '/home/<USER>/public_html/_run_and_exit.inc';

define('CURRENTSTAMP',time());
#define('DRYRUN',false);

run_and_exit();

function main(): bool {
	if (!db_insert('connect','
		INSERT IGNORE INTO connect (MAINTYPE,MAINID,ASSOCTYPE,ASSOCID,CSTAMP,CUSERID)
		SELECT ASSOCTYPE,ASSOCID,MAINTYPE,MAINID,CSTAMP,CUSERID
		FROM connect')
	) {
		return false;
	}
	echo 'fixes: ',db_affected(),"\n";

	if (!db_insert('connect','
		INSERT IGNORE INTO connect (ASSOCTYPE,ASSOCID,MAINTYPE,MAINID,CSTAMP,CUSERID)
		SELECT MAINTYPE,<PERSON>INID,ASSOCTYPE,ASSOCID,<PERSON><PERSON>MP,CUSERID
		FROM connect')
	) {
		return false;
	}
	echo 'fixes: ',db_affected(),"\n";

	return true;
}
