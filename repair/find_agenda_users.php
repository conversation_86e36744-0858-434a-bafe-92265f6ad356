#!/usr/bin/php
<?php

define('CURRENTSTAMP',time());

require_once '../public_html/_offline.inc';
require_once '../public_html/_db.inc';
require_once '../public_html/_error.inc';
require_once '../public_html/_date.inc';
require_once '../public_html/_allstats.inc';
require_once '../public_html/_browser.inc';

ob_implicit_flush(true);
$rc = dump();
exit(show_messages_cli() || !$rc ? 1 : 0);

function dump() {
	global $argv;
	if (empty($argv[2])) {
		error_log('need arguments: type date',0);
		return false;
	}
	list(,$type,$date) = $argv;
	if ($date == 'today') {
		list($y,$m,$d) = _getdate();
		$date = sprintf('%d%02d%02d',$y,$m,$d);
	}
	switch ($type) {
	case 'users':
		$fieldname = 'USERID';
		break;
	case 'agents':
		$fieldname = 'BROWSERID';
		break;
	case 'ips':
		$fieldname = 'IPBIN';
		break;
	case 'identities':
		$fieldname = 'IDENTID';
		break;
	default:
		error_log('type '.$type.' not understood',0);
		return false;
	}
	$rows = [];

	$if_agenda = '	IF (	REQUEST LIKE "/city/%"
			OR	REQUEST LIKE "/party/%"
			OR	REQUEST LIKE "/artist/%"
			OR	REQUEST LIKE "/location/%"
			OR	REQUEST LIKE "/organization/%"
			
			OR	REQUEST LIKE "/%_%.ics"
			
			OR	REQUEST LIKE "/feed/agenda/%",1,NULL)';


	foreach (['dbblack','dbbc'] as $server) {
		if ($server == 'dbbc') {
			if (!db_read(null,'USE pagehitstorage',SELECT,DB_FORCE_SERVER,$server)) {
				return false;
			}
		}
		$tables = db_simpler_array(null,'SHOW TABLES LIKE "pagehitdone_%"',DB_FORCE_SERVER,$server);
		if ($tables === false) {
			return false;
		}
		if (!$tables) {
			continue;
		}
		foreach ($tables as $table) {
			if (!preg_match('"^pagehitdone_'.preg_quote($date, '"').'"',$table,$match)
			||	isset($done[$table])
			) {
				continue;
			}
#			error_log('processing '.$server.' : '.$table,0);
			$done[$table] = $server;
			$i = db_rowuse_array(null,$q = '
				SELECT '.$fieldname.',BROWSERID,COUNT(*),COUNT('.$if_agenda.')
				FROM `'.addslashes($table).'`
				GROUP BY '.$fieldname.',BROWSERID',
				DB_FORCE_SERVER | DB_NON_ASSOC,
				$server
			);
			if ($i === false) {
				return false;
			}
			if (!$i) {
				continue;
			}
			foreach ($i as $info) {
				list($field,$browserid,$total,$agenda) = $info;
				if (isset($rows[$field])) {
					$rows[$field][0] += $total;
					$rows[$field][1] += $agenda;
				} else {
					$rows[$field] = [$total,$agenda];
				}
			}
		}
	}
#	error_log('sorting rows',0);
	foreach ($rows as $field => &$info) {
		$info[2] = $info[1] / $info[0];
	}
	unset($info);

	$totalrows = count($rows);
	$i = 0;

	foreach ($rows as $field => $info) {
#		error_log('filtering rows '.floor(100 * $i++ / $totalrows),0);
		[$total, $agenda, $part] = $info;
		if ($total < 100
		||	$part < .5
		) {
			continue;
		}
		$recheck[$field] =
			$fieldname === 'USERID'
		||	$fieldname === 'BROWSERID'
		||	$fieldname === 'IDENTID'
		?	$field
		:	'"'.addslashes($field).'"';
	}
	if (empty($recheck)) {
		error_log('nothing interesting',0);
		return;
	}
	$reqs = [];
	foreach ($done as $table => $server) {
		if (!preg_match('"^pagehitdone_'.preg_quote($date, '"').'"',$table)
		||	!preg_match('"^pagehitdone_(\d{4})(\d{2})(\d{2})"',$table,$match)
		) {
			continue;
		}
		if ($server == 'dbbc') {
			if (!db_read(null,'USE pagehitstorage',SELECT,DB_FORCE_SERVER,$server)) {
				return false;
			}
		}
#		error_log('reprocessing '.$table,0);
		list(,$y,$m,$d) = $match;
		$stamp = mktime(0,0,0,$m,$d,$y) + 24 * 3600;
		$requests = db_rowuse_array(null,'
			SELECT DISTINCT '.$fieldname.',BROWSERID,'.$if_agenda.',REQUEST
			FROM `'.addslashes($table).'`
			WHERE '.$fieldname.' IN ('.implode(',',$recheck).')',
			DB_FORCE_SERVER | DB_NON_ASSOC,
			$server
		);
		if ($requests === false) {
			return false;
		}
		if (!$requests) {
			continue;
		}
		foreach ($requests as $request) {
			list($field,$browserid,$agenda,$uri) = $request;

			if (preg_match('"/party/(\d+)"',$uri,$match)) {
				$party = memcached_party_and_stamp($match[1]);
				if ($party
				&&	$party['STAMP'] < $stamp
				) {
					# skip, in the past
					continue;
				}
			}

			$reqs[$field][$agenda][$uri] = $browserid;
		}
	}
	if (!$reqs) {
		return true;
	}
	$allbrowserids = [];
	$totalreqs = count($reqs);
	$i = 0;
	foreach ($reqs as $field => $req) {
#		error_log('filtering requests '.floor(100 * $i++ / $totalreqs),0);
		$result = [];
		$browserids = [];
		foreach ($req as $agenda => $uris) {
			$result[$agenda] = count($uris);
			foreach ($uris as $uri => $browserid) {
				$browserids[$browserid] = $browserid;
			}
		}
		if (!isset($result[1])) {
			continue;
		}
		if (isset($result[null])) {
			if (($result[1] / ($result[null] + $result[1])) < .5) {
				continue;
			}
		} else {
			$result[null] = 0;
		}
		$allbrowserids += $browserids;
		$results[$field] = [$result,$browserids];
	}
	uasort($results,function($a,$b) {
		return $a[0][1] - $b[0][1];
	});

	if (false === ($browser = db_simple_hash('browsername','
		SELECT ID,NAME
		FROM browsername
		WHERE ID IN ('.implode(',',$allbrowserids).')',
		DB_FORCE_SERVER,
		'dbblack'))
	) {
		return false;
	}
	# Satisfy EA inspection
	$browser = (array)$browser;
	require_once '../public_html/_spider.inc';
	$datafile = '/home/<USER>/data/spiders';
	$spiders = file($datafile,FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
	if ($spiders) {
		array_walk($spiders,function(&$spider){$spider = preg_quote($spider,"\x1F");});
		$regex = '\b('.implode('|',$spiders).')\b';
	} else {
		error_log('could not load spider datafile!',0);
		$regeix = null;
	}

	foreach ($results as $field => $info) {
		list($info,$browserids) = $info;
		$agents = [];
		$allrobot = true;
		foreach ($browserids as $browserid) {
			$agent = !empty($browser[$browserid]) ? $browser[$browserid] : ' ***** NO USER AGENT **** ';

			$robot = preg_match("\x1F".$regex."\x1F",$agent,$matches) && !empty($matches[1]) ? $matches[1] : false;
			$agents[] = $robot ? 'robot: '.$agent : $agent;
			if (!$robot) {
				$allrobot = false;
			}
		}
		$agenda = $info[1];
		$other = $info[null];
		$total = $other + $agenda;
		$part = $agenda / $total;

		printf('%3.1f',$part * 100);
		echo '% ';
		printf('%10d / %10d ',$agenda,$total);

		if ($allrobot) {
			echo '(robot) ';
		}
		switch ($type) {
		case 'users':
			echo 'user ',$field;
			break;
		case 'ips':
			echo 'ip ',inet_ntop($field),' host ',memcached_hostname_from_ipbin($field);
			break;
		case 'agents':
			echo 'browserid ',$field;
			break;
		case 'identities':
			echo 'identity ',$field;
		}
		echo ',  ua: ',implode(', ',$agents),"\n";
	}
	list($y,$m,$d,$hour,$mins) = _getdate();
	echo "\ngrouped by $fieldname, generated at ",sprintf('%04d-%02d-%02d %02d:%02d',$y,$m,$d,$hour,$mins),"\n";
}
