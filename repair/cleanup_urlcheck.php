#!/usr/bin/php
<?php

define('CURRENTSTAMP',time());

require_once '/home/<USER>/public_html/_exit_if_readonly.inc';
require_once '/home/<USER>/public_html/_db.inc';
require_once '/home/<USER>/public_html/_error.inc';

define('DEBUG',!isset($_SERVER['CRON']));

ob_implicit_flush(true);
$rc = main();
exit(show_messages_cli() || !$rc ? 1 : 0);

function main() {
	foreach ([
		'artist'	=> ['artist',		'ARTISTID',		'SITE'],
		'bookartist'	=> ['artist',		'ARTISTID',		'BOOKSITE'],
		'label'		=> ['label',		'LABELID',		'SITE'],
		'stream'	=> ['stream',		'STREAMID',		'SITE'],
		'party'		=> ['party',		'PARTYID',		'SITE'],
		'location'	=> ['location',		'LOCATIONID',		'SITE'],
		'organization'	=> ['organization',	'ORGANIZATIONID',	'SITE'],
	] as $element => $info) {
		list($table,$idname,$sitename) = $info;
	
		if (!db_insert('urlcheck_log','
			INSERT INTO urlcheck_log
			SELECT urlcheck.*
			FROM '.$table.'
			JOIN urlcheck ON ELEMENT="'.$element.'" AND ID='.$idname.'
			WHERE '.$sitename.'=""')
		||	!db_delete('urlcheck','
			DELETE urlcheck
			FROM '.$table.'
			JOIN urlcheck ON ELEMENT="'.$element.'" AND ID='.$idname.'
			WHERE '.$sitename.'=""')
			
		||	!db_insert('urlcheck','
			INSERT INTO urlcheck (ELEMENT,ID)
			SELECT "'.$element.'",'.$idname.'
			FROM '.$table.'
			LEFT JOIN urlcheck ON ELEMENT="'.$element.'" AND ID='.$idname.'
			WHERE '.$sitename.'!=""
			  AND ISNULL(urlcheck.ID)')
		) {
			return false;
		}
	}
	return true;
}
