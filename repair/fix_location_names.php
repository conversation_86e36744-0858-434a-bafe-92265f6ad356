#!/usr/bin/php
<?php

define('CURRENTSTAMP',time());
define('DEBUG',true);
#define('DEBUG',!isset($_SERVER['CRON']));
#define('DRYRUN',true);

const TITLE_PREFIXES = [
	'cafe',
	'café',
	'Feestcafé',
	'Feestcafe',
	'Sportpark',
	'Beachclub',
	'Fletcher Hotel-Restaurant',
	'Restaurant',
	'Hotel',
	'Zaal',
	'Evenementencomplex',
	'Taverne',
	'Dorpscentrum',
	'Danscafé',
	'Dorpshuis',
	'Parkeerplaats',
	'Partycentrum',
	'Grand Café',
	'Grandcafé',
	'Kasteel',
	'Camping',
	'Evenemententerrein',
	'Festivalterrein',
	'Strandpaviljoen',
	'Uitgaanscentrum',
	'Zalencentrum',
	'Ontmoetingscentrum',
	'Bar',
	'Bar Dancing',
	'Bar/Dancing',
];

const NAME_PREFIXES = [
	'het',
	'de',
	'\'t',
	'the',
	'a', 
];

const CURRENTLANGID = 0;

require_once '/home/<USER>/public_html/_exit_if_readonly.inc';
require_once '/home/<USER>/public_html/_db.inc';
require_once '/home/<USER>/public_html/_require.inc';
require_once '/home/<USER>/public_html/_run_and_exit.inc';

run_and_exit();

function main(): bool {
	foreach (TITLE_PREFIXES as $prefix) {
		$names[] = "location.NAME LIKE '$prefix %'";
	}
	foreach (NAME_PREFIXES as $prefix) {
		$prefix = str_replace("'", "\\'", $prefix);
		$names[] = "location.ORDERNAME LIKE '$prefix %'";
	}
	
	if (!($bad_locs = db_rowuse_array('location', "
		SELECT LOCATIONID, TITLE, location.NAME, location.ORDERNAME, city.NAME AS CITY_NAME
		FROM location
		LEFT JOIN city USING (CITYID)
		WHERE ".implode(' OR ', $names)
	))) {
		return $bad_locs !== false;
	}
	foreach ($bad_locs as $bad_loc) {
		extract($bad_loc);
		foreach (TITLE_PREFIXES as $prefix) {
			$prev_ORDERNAME = $ORDERNAME;
			$prev_NAME      = $NAME;
			$NAME	   = preg_replace('"^('.$prefix.'\s+)"ui', '', $NAME);
			$ORDERNAME = preg_replace('"^('.$prefix.'\s+)"ui', '', $ORDERNAME);
			if (!utf8_strcasecmp($ORDERNAME, $CITY_NAME)) {
				$ORDERNAME = $prev_ORDERNAME;
			}
			if (!utf8_strcasecmp($NAME, $CITY_NAME)) {
				$NAME = $prev_NAME;
			}
		}
		foreach (NAME_PREFIXES as $prefix) {
			$prev_ORDERNAME = $ORDERNAME;
			$ORDERNAME = preg_replace($r = '"^('.$prefix.'\s+)"ui', '', $ORDERNAME);
			if (!utf8_strcasecmp($ORDERNAME, $CITY_NAME)) {
				$ORDERNAME = $prev_ORDERNAME;
			}
		}
		echo $LOCATIONID.':'.$bad_loc['NAME'].':'.$bad_loc['ORDERNAME'],"\n";
		echo $LOCATIONID.':'.$NAME.':'.$ORDERNAME,"\n";
		
		if ($bad_loc['NAME']      !== $NAME
		||	$bad_loc['ORDERNAME'] !== $ORDERNAME
		) {
			if (!db_insert('location_log', "
				INSERT INTO location_log
				SELECT * FROM location
				WHERE LOCATIONID = $LOCATIONID")
			||	!db_update('location', "
				UPDATE location SET
					NAME		= '".addslashes($NAME)."',
					ORDERNAME	= '".addslashes($ORDERNAME)."',
					MUSERID		= 0,
					MSTAMP		= ".CURRENTSTAMP."
				WHERE LOCATIONID = $LOCATIONID")
			) {
				return false;
			}
		}
	}
	return true;
}
