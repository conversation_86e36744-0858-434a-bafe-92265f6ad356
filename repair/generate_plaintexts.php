#!/usr/bin/php -d display_errors=on
<?php
ini_set('display_errors','on');

define('CURRENTSTAMP',time());
define('CURRENTLANGUAGE','nl');
#define('DRYRUN',true);

$_SERVER['REMOTE_ADDR'] = '::1';

require_once '../public_html/_offline.inc';
require_once '../public_html/_db.inc';
require_once '../public_html/_error.inc';
require_once '../public_html/_plaintext.inc';
require_once '../public_html/_layout.inc';
require_once '../public_html/_currentuser.inc';

$currentuser = new _currentuser(TINY_USER | SKIP_SETTINGS, 2269);

ob_implicit_flush(true);
$rc = main();
exit(show_messages_cli() || !$rc ? 1 : 0);

function main() {
	foreach ([
		'interview',
		'review',
		'news',
	] as $element) {
		$step = 1000;
		$offset = 0;
		$_REQUEST['sELEMENT'] = $element;
		while ($items = db_simple_hash($element,'SELECT '.$element.'ID,BODY FROM '.$element.' ORDER BY '.$element.'ID ASC LIMIT '.$offset.','.$step)) {
			if (!$items) {
				if ($items === false) {
					return false;
				}
				break;
			}
			$total = count($items);
			$i = 0;
			foreach ($items as $id => $body) {
				$_REQUEST['sID'] = $id;
				error_log($element.':'.$id.' '.(++$i).' / '.$total.' => '.round(100 * $i/$total).'%');
				if (!store_plain_text($element,$id,$body)) {
					return false;
				}
			}
			$offset += $step;
		}
	}
	return true;
}
