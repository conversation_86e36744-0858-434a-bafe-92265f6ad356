#!/usr/bin/php
<?php

require_once '/home/<USER>/public_html/_offline.inc';
require_once '/home/<USER>/public_html/_date.inc';
require_once '/home/<USER>/public_html/_db.inc';
require_once '/home/<USER>/public_html/_memcache.inc';
require_once '/home/<USER>/public_html/_require.inc';
require_once '/home/<USER>/public_html/_topicactions.inc';

#define('DRYRUN',true);

define('IDSTEP',	10000);
define('CURRENTSTAMP',	time());
define('TODAYSTAMP',	mktime(0,0,0));
define('CURRENTUSERID',	0);
define('CURRENTDAYNUM',	to_days());
define('NEXT',		"\x1B[4D, ");
define('ISRESULT',	"\x1B[4D = ");
define('GO',		'... ');
define('NEWLINE',	"\n");
define('RESETLINE',	"\n\x1B[A\x1B[K");
function BACKSTR($arg) {
	if (!$arg) {
		return;
	}
	BACK(strlen($arg));
}
function BACK($num) {
	if (!$num) {
		return;
	}
	echo "\x1B[",$num,"D\x1B[K";
}


ob_implicit_flush(true);

$all = array('directmessage','flockmessage','message');

main($all);
/*
$startdaynum = 0;
$stopdaynum = 0;

if (!isset($argv[1])) {
	main($all,$startdaynum,$stopdaynum);
} else {
	for ($i = 1; $i < $argc; ++ $i) {
		if (is_number($argv[$i])) {
			$startdaynum = $argv[$i];
		} elseif (preg_match('"^today(?:\-(\d+))?$"',$argv[$i],$matches)) {
			if (isset($matches[1])) {
				$daynum = to_days() - $matches[1];
			} else {
				$daynum = to_days();
			}
			if ($startdaynum) {
				$stopdaynum = $daynum;
				if ($stopdaynum <= $startdaynum) {
					echo "stopdaynum is less than startdaynum\n";
					return;
				}
			} else {
				$startdaynum = $daynum;
			}
		} elseif (
			$argv[$i] != 'directmessage'
		&&	$argv[$i] != 'flockmessage'
		&&	$argv[$i] != 'message'
		) {
			echo "invalid argument: ",$argv[$i],"\n";
			echo "need argument: <directmessage | flockmessage | message> <startdaynum>\n";
			return;
		} else {
			$tables[] = $argv[$i];
		}
	}
	main(isset($tables) ? $tables : $all,$startdaynum,$stopdaynum);
}*/
if (isset($GLOBALS['__error'])) {
	echo "failed:\r\n\r\n";
	foreach ($GLOBALS['__error'] as $errstr) {
		echo $errstr,"\r\n";
	}
}
function main($tables) {
	foreach ($tables as $table) {
		echo 'processing ',$table,NEWLINE;

		$totals = db_simple_hash($table,'
			SELECT TO_DAYS(FROM_UNIXTIME(CSTAMP)) AS DAYNUM,COUNT(*)
			FROM '.$table.'
			WHERE CSTAMP<'.TODAYSTAMP.'
			GROUP BY DAYNUM'
		);
		if ($totals === false) {
			return;
		}
		if (!$totals) {
			continue;
		}
		$existing = db_simple_hash($table.'_counter','SELECT DAYNUM,CNT FROM '.$table.'_counter WHERE DAYNUM<'.CURRENTDAYNUM);
		if ($existing === false) {
			return;
		}
		$insert = array();
		$update = array();
		foreach ($totals as $daynum => $cnt) {
			if (!isset($existing[$daynum])) {
				$insert[] = '('.$daynum.','.$cnt.')';
			} elseif ($existing[$daynum] != $cnt) {
				$update[$daynum] = 'WHEN '.$daynum.' THEN '.$cnt;
			}
		}
		if ($insert) {
			echo 'inserting ',count($insert),' entries',GO;
			if (!db_insert($table.'_counter','
				INSERT INTO '.$table.'_counter (DAYNUM,CNT)
				VALUES '.implode(',',$insert))
			) {
				return;
			}
			echo NEXT,'done',NEWLINE;
		}
		if ($update) {
			echo 'updating ',count($update),' entries',GO;
			if (!db_update($table.'_counter','
				UPDATE '.$table.'_counter SET CNT=CASE DAYNUM '.implode(' ',$update).' END
				WHERE DAYNUM IN ('.implode(',',array_keys($update)).')')
			) {
				return;
			}
			echo NEXT,'done',NEWLINE;
		}
	}
}

/*function main($tables,$startdaynum,$stopdaynum) {
	foreach ($tables as $table) {
		$updatecnt = 0;
		$deletecnt = 0;
		$insertcnt = 0;
		$countertable = $table.'_counter';
		echo 'processing ',$table,GO;
		$firstmessage = db_single_assoc($table,'
			SELECT SQL_NO_CACHE CSTAMP,MESSAGEID
			FROM '.$table.'
			WHERE CSTAMP!=0
			ORDER BY MESSAGEID ASC
			LIMIT 1',
			DB_USE_MASTER
		);
		if ($firstmessage === false) {
			return;
		}
		if (!$firstmessage) {
			echo NEXT,'no first message found',NEWLINE;
			return;
		}
		$lastmessage = db_single_assoc($table,'
			SELECT SQL_NO_CACHE CSTAMP,MESSAGEID
			FROM '.$table.'
			WHERE CSTAMP!=0
			ORDER BY MESSAGEID DESC
			LIMIT 1',
			DB_USE_MASTER
		);
		if ($lastmessage === false) {
			return;
		}
		if (!$lastmessage) {
			echo NEXT,'no last message found',NEWLINE;
			continue;
		}
		$first_cstamp = $firstmessage['CSTAMP'];
		$first_messageid = $firstmessage['MESSAGEID'];
		$last_cstamp = $lastmessage['CSTAMP'];
		$last_messageid = $lastmessage['MESSAGEID'];
		$lastdaynum = to_days($last_cstamp)-1;
		$firstdaynum = to_days($first_cstamp);
		if (!$startdaynum) {
			$startdaynum = to_days($first_cstamp);
			$startmessageid = $first_messageid;
			echo NEXT,($str = 'removing all before firstdaynum'.GO);
			if (!db_delete($countertable,'
				DELETE FROM '.$countertable.'
				WHERE DAYNUM<'.$startdaynum)
			) {
				return;
			}
			echo BACK(strlen($str) + 2);
			$deletecnt += db_affected();
		} else {
			if ($startdaynum < $firstdaynum) {
				$startdaynum = $firstdaynum;
			}
			$startmessageid = get_messageid_for_daynum($startdaynum-1,$first_messageid,$last_messageid,$table);
			if (!$startmessageid) {
				return;
			}
		}
		if (!$stopdaynum) {
			$stopmessageid = $last_messageid;
			$stopdaynum = to_days($last_cstamp)-1;
		} else {
			if ($stopdaynum > $lastdaynum) {
				$stopdaynum = $lastdaynum;
			}
			$stopmessageid = get_messageid_for_daynum($stopdaynum+1,$first_messageid,$last_messageid,$table);
			if (!$stopmessageid) {
				return;
			}
		}
		echo NEXT,'from daynum ',$startdaynum,' to ',$stopdaynum,GO;
		$i = 1;
		$groups = ceil(($stopmessageid - $startmessageid) / IDSTEP);
		$backstr = NEXT;
		$cnts = array();
		$hops = array();
		$daynumdoneinpass = array();
		$hopcnt = 0;
		$currentdaynum = 0;
		for ($messageid = $startmessageid; $messageid <= $stopmessageid; $messageid += IDSTEP, ++$i) {
#			echo $backstr,($checkstr = 'hops: '.$hopcnt.', deleted: '.$deletecnt.', inserted: '.$insertcnt.', updated: '.$updatecnt.', now checking '.$i.' of '.$groups.GO);
			$tmpcnts = db_simple_hash($table,'
				SELECT SQL_NO_CACHE TO_DAYS(FROM_UNIXTIME(CSTAMP)) AS DAYNUM,COUNT(*)
				FROM '.$table.'
				WHERE MESSAGEID>='.$messageid.'
				  AND MESSAGEID<'.($messageid+IDSTEP).'
				GROUP BY DAYNUM',
				DB_USE_MASTER
			);
			if ($tmpcnts === false) {
				return;
			}
			if ($tmpcnts) {
				ksort($tmpcnts);
				foreach ($tmpcnts as $daynum => $cnt) {
					if ($daynum > $stopdaynum) {
						$messageid = $stopmessageid;
						break;
					}
					if ($daynum < $startdaynum) {
						continue;
					}
					if (!$currentdaynum) {
						$currentdaynum = $daynum;
					} elseif ($daynum > $currentdaynum) {
						$daynumdoneinpass[$currentdaynum] = $i;
						$currentdaynum = $daynum;
					} elseif ($daynum < $currentdaynum) {
						++$hopcnt;
						$hops[$i] = $daynum;
					}
					if (!isset($cnts[$daynum])) {
						$cnts[$daynum] = $cnt;
					} else {
						$cnts[$daynum] += $cnt;
					}
				}
			}
			if ($cnts) {
				$rc = process_cnts($cnts,$daynumdoneinpass,$countertable,$i);
				if ($rc === false) {
					return;
				}
				if ($rc) {
					list($inserts,$updates,$deletes) = $rc;
					$insertcnt += $inserts;
					$updatecnt += $updates;
					$deletecnt += $deletes;
				}
			}
#			$backstr = BACKSTR($checkstr);
		}
		echo $backstr,'done, hops: ',$hopcnt,', deleted: ',$deletecnt,', inserted: ',$insertcnt,', updated: ',$deletecnt,NEWLINE;
#		?>cnts:<?
#		print_r($cnts);
#		?>daynumdoneinpass:<?
#		print_r($daynumdoneinpass);
		if ($cnts) {
			$rc = process_cnts($cnts,$daynumdoneinpass,$countertable);
			if ($rc === false) {
				return;
			}
			if ($rc) {
				list($inserts,$updates,$deletes) = $rc;
				$insertcnt += $inserts;
				$updatecnt += $updates;
				$deletecnt += $deletes;
			}
		}
	}
}
function get_messageid_for_daynum($finddaynum,$lo,$hi,$table) {
#	echo "get_messageid($finddaynum,$lo,$hi,$table)\n";
	while (1) {
		$pivot = $lo + (integer)(($hi - $lo) / 2);
#		echo "lo = $lo, hi = $hi, pivot = $pivot",GO;
		$pivotmsg = db_single_assoc($table,'
			SELECT SQL_NO_CACHE CSTAMP,MESSAGEID
			FROM '.$table.'
			WHERE MESSAGEID<='.$pivot.'
			ORDER BY MESSAGEID DESC
			LIMIT 1',
			DB_USE_MASTER
		);
		if ($pivotmsg === false) {
			return false;
		}
		if (!$pivotmsg) {
			echo NEXT,'unable to find pivotmsg for lo = ',$lo,', hi = ',$hi,', pivot = ',$pivot,NEWLINE;
			return false;
		}
		$daynum = to_days($pivotmsg['CSTAMP']);
#		echo NEXT,'found daynum: ',$daynum,NEWLINE;
		if ($daynum === $finddaynum) {
			return $pivotmsg['MESSAGEID'];
		} elseif ($daynum < $finddaynum) {
			$newlo = $pivotmsg['MESSAGEID']+1;
			if ($newlo === $lo) {
				echo NEXT,'unable to determine new lo for lo = ',$lo,', hi = ',$hi,', pivot = ',$pivot,NEWLINE;
				return false;
			}
			$lo = $newlo;
		} else {
			$newhi = $pivotmsg['MESSAGEID']-1;
			if ($newhi === $hi) {
				echo NEXT,'unable to determine new hi for lo = ',$lo,', hi = ',$hi,', pivot = ',$pivot,NEWLINE;
				return false;
			}
			$hi = $newhi;
		}
	}
}
function average_between_past_few_passes($done) {
	$pass = 0;
	$spaces = 0;
	$totalspace = 0;
	foreach (array_slice($done,-10) as $inpass) {
		$totalspace += ($inpass - $pass);
		++$spaces;
		$pass = $inpass;
	}
	return array($spaces,$totalspace / $spaces);
}
function process_cnts(&$cnts,$done,$countertable,$pass = 0) {
#	echo "process_cnts(cnts[",count($cnts),"],done[",count($done),"],$pass)\n";
	if (!$done
	||	!$cnts
	) {
		return null;
	}
	$insertsize = 0;
	$updatesize = 0;
	$deletesize = 0;
	if (!$pass) {
		# last pass
		reset($cnts);
		list($mindaynum,$mincnt) = keyval($cnts);
		end($cnts);
		list($maxdaynum,$maxcnt) = keyval($cnts);
		$currentcnts = db_simple_hash($countertable,'
			SELECT SQL_NO_CACHE DAYNUM,CNT
			FROM '.$countertable.'
			WHERE DAYNUM BETWEEN '.$mindaynum.' AND '.$maxdaynum,
			DB_USE_MASTER
		);
		if ($currentcnts === false) {
			return false;
		}
		foreach ($cnts as $daynum => $cnt) {
			if (!$currentcnts
			||	!isset($currentcnts[$daynum])
			) {
				$inserts[] = '('.$daynum.','.$cnt.')';
				++$insertsize;
			} else {
				$updates[$daynum] = 'WHEN '.$daynum.' THEN '.$cnt;
				++$updatesize;
			}
		}
		if ($currentcnts)
		foreach ($currentcnts as $daynum => $cnt) {
			if (!isset($cnts[$daynum])) {
				$deletes[] = $daynum;
				++$deletesize;
			}
		}

	} else {
		list($spaces,$avgbetween) = average_between_past_few_passes($done);
		echo $avgbetween,NEWLINE;
		if ($spaces < 1) {
			return;
		}
		?>cnts:<?
		print_r($cnts);
		?>done:<?
		print_r($done);
		foreach ($cnts as $daynum => $cnt) {
#
#			$doneinpass = isset($done[$daynum]) ? $done[$daynum] :
		}
	}
	if (isset($inserts)) {
		if (!db_insert($countertable,'
			INSERT INTO '.$countertable.' (DAYNUM,CNT)
			VALUES '.implode(',',$inserts))
		) {
			return false;
		}
		$aff = db_affected();
		if ($aff != $insertsize) {
			echo "affected != insertsize ($aff != $insertsize)\n";
			return false;
		}
	}
	if (isset($updates)) {
		if (!db_update($countertable,'
			UPDATE '.$countertable.' SET CNT=CASE DAYNUM '.implode(' ',$updates).' END
			WHERE DAYNUM IN ('.implode(',',array_keys($updates)).')')
		) {
			return false;
		}
		$aff = db_affected();
		if ($aff != $updatesize) {
			echo "affected != updatesize ($aff != $updatesize)\n";
			return false;
		}
	}
	if (isset($deletes)) {
		if (!db_delete($countertable,'
			DELETE FROM '.$countertable.'
			WHERE DAYNUM IN ('.implode(',',$deletes).')')
		) {
			return false;
		}
		$aff = db_affected();
		if ($aff != $deletesize) {
			echo "affected != deletesize ($aff != $deletesize)\n";
			return false;
		}
	}
	return array($insertsize,$updatesize,$deletesize);
}
*/
