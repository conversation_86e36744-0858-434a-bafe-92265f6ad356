#!/usr/bin/php
<?php

define('CURRENTSTAMP', time());

const CURRENTUSERID	= 0;

$_SERVER['REQUEST_URI'] = $GLOBALS['argv'][0];

require_once '../public_html/_exit_if_readonly.inc';
require_once '../public_html/_db.inc';
require_once '../public_html/_flockmod.inc';
require_once '../public_html/_ubb_preprocess.inc';
require_once '../public_html/_comment.inc';
require_once '../public_html/_unicode.inc';
require_once '../public_html/_url.inc';
require_once '../public_html/_run_and_exit.inc';

set_memory_limit(2 * GIGABYTE);

run_and_exit(syslog: true);

function main(): bool {
	$default_params = [
		'body_field'	=> 'BODY',
		'utf8'			=> true,
		'url'			=> false,
		'no_log'		=> false,
		'extra_id'		=> false,
		'extra_field'	=> false,
		'trim'			=> false,
		'compress'		=> false,
		'dryrun'		=> false,
	];

	global $argv;
	$argvcp = $argv;
	unset($argvcp[0]);

	$only_tables = [];
	foreach ($argvcp as $arg) {
		switch ($arg) {
		case 'dryrun':
			$default_params[$arg] = true;
			break;
		default:
			$only_tables[] = $arg;
			break;
		}
	}

	define('DRYRUN', $default_params['dryrun']);

	$ok = true;

	# NOTE: extra_id if used, must be a number currently

	$process_tables = [
		'albumelement'				=> ['idname' => 'ALBUMELEMENTID'],
		'albumelement_tobedeleted'	=> ['idname' => 'ALBUMELEMENTID', 'no_log' => true],
		'albummap'					=> ['idname' => 'MAPID'],
		'albummap_tobedeleted'		=> ['idname' => 'MAPID', 'no_log' => true],
		'announcement'				=> ['idname' => 'ANNOUNCEMENTID'],
		'answeringmachine'			=> ['idname' => 'USERID'],
		'column'					=> ['idname' => 'COLUMNID'],
		'contest'					=> ['idname' => 'CONTESTID'],
		'flock'						=> ['idname' => 'FLOCKID'],
		'flockmeeting'				=> ['idname' => 'MEETINGID'],
		'flockmessage'				=> ['idname' => 'MESSAGEID'],
		'flocktopic'				=> ['idname' => 'TOPICID', 'body_field' => 'SUBJECT'],
		'interview'					=> ['idname' => 'INTERVIEWID'],
		'message'					=> ['idname' => 'MESSAGEID'],
		'topic'						=> ['idname' => 'TOPICID', 'body_field' => 'SUBJECT'],
		'news'						=> ['idname' => 'NEWSID'],
		'personalnote'				=> ['idname' => 'USERID', 'no_log' => true],
		'plaintext'					=> ['idname' => 'ID', 'body_field' => 'PLAIN', 'no_log' => true, 'extra_field' => 'ELEMENT', 'compress' => true],
		'poll'						=> ['idname' => 'POLLID', 'body_field' => 'QUESTION'],
		'poll_answer'				=> ['idname' => 'ANSWERID', 'body_field' => 'ANSWER'],
		'promo'						=> ['idname' => 'PROMOID'],
		'report'					=> ['idname' => 'REPORTID'],
		'review'					=> ['idname' => 'REVIEWID'],
		'stream'					=> ['idname' => 'STREAMID', 'body_field' => 'SONGPLAYING'],
		'stream_track_log'			=> ['idname' => 'STREAMID', 'body_field' => 'NOWPLAYING', 'extra_id' => 'STAMP', 'no_log' => true, 'trim' => true],
		'user_text'					=> ['idname' => 'USERID'],
		'weblog'					=> ['idname' => 'WEBLOGID'],
		'presence'					=> ['idname' => 'PRESENCEID', 'body_field' => 'SITE', 'url' => true],
	];

	foreach (commentables() as $table => $flags) {
		$process_tables[$table.'_comment'] = ['idname' => 'COMMENTID'];
	}

	$process_tables['label_comment'] = ['idname' => 'COMMENTID'];

	$ok = true;

	if ($only_tables) {
		$do_process_tables = [];
		foreach ($only_tables as $only_table) {
			if (!isset($process_tables[$only_table])) {
				syslog(LOG_CRIT, 'not a valid table to process: '.$only_table);
				$ok = false;
			} else {
				$do_process_tables[$only_table] = $process_tables[$only_table];
			}
		}
		$process_tables = $do_process_tables;
	}

	if ($ok) {
		foreach ($process_tables as $table => $params) {
			if (!fix_bad_utf8($table, $params + $default_params)) {
				$ok = false;
				break;
			}
			if (empty($params['no_log'])) {
				if (!fix_bad_utf8($table.'_log', $params + $default_params)) {
					$ok = false;
					break;
				}
			}
		}
	}
	return $ok;
}

function fix_bad_utf8(string $table, array $params): bool {
	syslog(LOG_INFO, 'fixing table: '.$table);

	syslog(LOG_DEBUG, 'parameters: '.implodehash(', ', ' = ', $params));

	extract($params);

	$select_extra_id	= $extra_id    ? ','.$extra_id	  : '';
	$select_extra_field	= $extra_field ? ','.$extra_field : '';

	$orig_body_field = $body_field;

	if ($compress) {
		$body_field = 'UNCOMPRESS('.$body_field.')';
	}

	$items = db_rowuse_array($table, '
		SELECT DISTINCT `'.$idname.'` AS id, '.$body_field.' AS body'.$select_extra_id.$select_extra_field.'
		FROM `'.$table.'`
		WHERE /*ID = 335762 AND*/
		    ( BINARY '.$body_field.' LIKE "%Ã©%"
		   OR BINARY '.$body_field.' LIKE "%Ã¶%"
		   OR BINARY '.$body_field.' LIKE CONCAT("%Ã", CHAR(0xA0), "%")
		/* This is bad UTF8, things got garbled simetimes owht 0xAD 0xA0 */
		   OR BINARY '.$body_field.' LIKE CONCAT("%Â", CHAR(0xAD), "%")
		   OR BINARY '.$body_field.' LIKE CONCAT("%Â", CHAR(0xA0), "%")
		   OR BINARY '.$body_field.' LIKE CONCAT("%", CHAR(0xC3), CHAR(0x82), CHAR(0xC2), CHAR(0xAD), "%")
		   OR BINARY '.$body_field.' LIKE CONCAT("%", CHAR(0xC3), CHAR(0x82), CHAR(0xC2), CHAR(0xA0), "%")
		   OR BINARY '.$body_field.' LIKE CONCAT("%", CHAR(0xC3), CHAR(0x82), CHAR(0xE2), CHAR(0x80), CHAR(0x9A), "%")
		   OR BINARY '.$body_field.' LIKE "%hÃrst%"
		   OR BINARY '.$body_field.' LIKE "%TrÃume%"
		   OR BINARY '.$body_field.' LIKE "%wÃrd%"
		   OR BINARY '.$body_field.' LIKE "%aquÃ%"
		   OR BINARY '.$body_field.' LIKE "%asÃ%"
		   OR BINARY '.$body_field.' LIKE "%finirÃ%"
		   OR BINARY '.$body_field.' LIKE "%mÃ%"
		   OR BINARY '.$body_field.' LIKE "%Ã±%"
		   OR BINARY '.$body_field.' LIKE "%Ã¯%"
		   OR BINARY '.$body_field.' LIKE "%Ã—%"
		   OR BINARY '.$body_field.' LIKE "%Ã´%"
		   OR BINARY '.$body_field.' LIKE "%Ã¡%"
		   OR BINARY '.$body_field.' LIKE "%Ã¹%"
		   OR BINARY '.$body_field.' LIKE "%Ã²%"
		   OR BINARY '.$body_field.' LIKE "%Ã³%"
		   OR BINARY '.$body_field.' LIKE "%Ã¼%"
		   OR BINARY '.$body_field.' LIKE "%Ã«%"
		   OR BINARY '.$body_field.' LIKE "%Â´%"
		   OR BINARY '.$body_field.' LIKE "%Ã¤%"
		   OR BINARY '.$body_field.' LIKE "%Ã¨%"
		   OR BINARY '.$body_field.' LIKE "%Ãº%"
		   OR BINARY '.$body_field.' LIKE "%â€™%"
		   OR BINARY '.$body_field.' LIKE "%â€“%"
		   OR BINARY '.$body_field.' LIKE "%â€™%"
		   OR BINARY '.$body_field.' LIKE "%â€œ%"
		   OR BINARY '.$body_field.' LIKE "%â€¦%"
		   OR BINARY '.$body_field.' LIKE "%â€?%"
		   OR BINARY '.$body_field.' LIKE "%ÃƒÂ©%"
		   OR BINARY '.$body_field.' LIKE "%â‚¬%"
		   OR BINARY '.$body_field.' LIKE "%Ãƒâ€”%"
		   OR BINARY '.$body_field.' LIKE "%Ã˜%"
		   
		   OR BINARY '.$body_field.' LIKE CONCAT("%", CHAR(0x80) ,"%")
		   OR BINARY '.$body_field.' LIKE CONCAT("%", CHAR(0x91) ,"%")
		   OR BINARY '.$body_field.' LIKE CONCAT("%", CHAR(0x92) ,"%")
		   OR BINARY '.$body_field.' LIKE CONCAT("%", CHAR(0x93) ,"%")
		   OR BINARY '.$body_field.' LIKE CONCAT("%", CHAR(0x94) ,"%")
		   OR BINARY '.$body_field.' LIKE CONCAT("%", CHAR(0x95) ,"%")
		   OR BINARY '.$body_field.' LIKE CONCAT("%", CHAR(0x99) ,"%")
		   OR BINARY '.$body_field.' LIKE CONCAT("%", CHAR(0xAD) ,"%")
		   OR BINARY '.$body_field.' LIKE CONCAT("%", CHAR(0xAE) ,"%")
		   OR BINARY '.$body_field.' LIKE CONCAT("%", CHAR(0xB2) ,"%")
		   OR BINARY '.$body_field.' LIKE CONCAT("%", CHAR(0xB3) ,"%")
		   OR BINARY '.$body_field.' LIKE CONCAT("%", CHAR(0xB4) ,"%")
		   OR BINARY '.$body_field.' LIKE CONCAT("%", CHAR(0xC1) ,"%")
		   OR BINARY '.$body_field.' LIKE CONCAT("%", CHAR(0xC2) ,"%")
		   OR BINARY '.$body_field.' LIKE CONCAT("%", CHAR(0xC4) ,"%")
		   OR BINARY '.$body_field.' LIKE CONCAT("%", CHAR(0xC6) ,"%")
		   OR BINARY '.$body_field.' LIKE CONCAT("%", CHAR(0xC9) ,"%")
		   OR BINARY '.$body_field.' LIKE CONCAT("%", CHAR(0xD6) ,"%")
		   OR BINARY '.$body_field.' LIKE CONCAT("%", CHAR(0xD7) ,"%")
		   OR BINARY '.$body_field.' LIKE CONCAT("%", CHAR(0xDB) ,"%")
		   OR BINARY '.$body_field.' LIKE CONCAT("%", CHAR(0xDC) ,"%")
		   OR BINARY '.$body_field.' LIKE CONCAT("%", CHAR(0xDF) ,"%")
		   OR BINARY '.$body_field.' LIKE CONCAT("%", CHAR(0xE0) ,"%")
		   OR BINARY '.$body_field.' LIKE CONCAT("%", CHAR(0xE1) ,"%")
		   OR BINARY '.$body_field.' LIKE CONCAT("%", CHAR(0xE2) ,"%")
		   OR BINARY '.$body_field.' LIKE CONCAT("%", CHAR(0xE4) ,"%")
		   OR BINARY '.$body_field.' LIKE CONCAT("%", CHAR(0xE5) ,"%")
		   OR BINARY '.$body_field.' LIKE CONCAT("%", CHAR(0xE7) ,"%")
		   OR BINARY '.$body_field.' LIKE CONCAT("%", CHAR(0xE8) ,"%")
		   OR BINARY '.$body_field.' LIKE CONCAT("%", CHAR(0xE9) ,"%")
		   OR BINARY '.$body_field.' LIKE CONCAT("%", CHAR(0xEA) ,"%")
		   OR BINARY '.$body_field.' LIKE CONCAT("%", CHAR(0xEB) ,"%")
		   OR BINARY '.$body_field.' LIKE CONCAT("%", CHAR(0xB2) ,"%")
		   OR BINARY '.$body_field.' LIKE CONCAT("%", CHAR(0xB7) ,"%")
		   OR BINARY '.$body_field.' LIKE CONCAT("%", CHAR(0xEF) ,"%")
		   OR BINARY '.$body_field.' LIKE CONCAT("%", CHAR(0xF1) ,"%")
		   OR BINARY '.$body_field.' LIKE CONCAT("%", CHAR(0xF4) ,"%")
		   OR BINARY '.$body_field.' LIKE CONCAT("%", CHAR(0xF6) ,"%")
		   OR BINARY '.$body_field.' LIKE CONCAT("%", CHAR(0xF8) ,"%")
		   OR BINARY '.$body_field.' LIKE CONCAT("%", CHAR(0xF9) ,"%")
		   OR BINARY '.$body_field.' LIKE CONCAT("%", CHAR(0xFA) ,"%")
		   OR BINARY '.$body_field.' LIKE CONCAT("%", CHAR(0xFB) ,"%")
		   OR BINARY '.$body_field.' LIKE CONCAT("%", CHAR(0xFC) ,"%")
		    )',
		DB_FORCE_MASTER
	);

	if (!$items) {
		if ($items !== false) {
			syslog(LOG_INFO, "nothing to be done for $table");
		}
		return $items !== false;
	}

	$total = count($items);
	$current = 0;
	$replacements = 0;
	static $ok_letters = '\d!\?/\+\(\)\[\]&#@a-zA-Z;:=<>\~\-_\"\',\.\s\r\n\x91\x92\x93\x94';

	syslog(LOG_INFO, 'total bad entries: '.$total);

	$process_total = count($items);

	foreach ($items as $item) {
		extract($item);

		$ids[] = $id;

		syslog(LOG_INFO, "checking $table:$id, $process_total left");
		--$process_total;

		$pre_body = $body;

		syslog(LOG_INFO, 'old text '.$pre_body);

		if (isset($body[2])
		&&	$body[0] === "\xEF"
		&&	$body[1] === "\xBB"
		&&	$body[2] === "\xBF"
		) {
			$body = substr($body, 3);
		}
		if (isset($url)) {
			$body = str_replace('â€Ž', '', $body);
		}
		#$body = preg_replace('~['.REPLACEMENT_CHARACTER.']+~i', '', $body);
		$body = preg_replace('~Jos\xE9~',  					'Jos\xC3\xA9', $body);
		$body = preg_replace('~André\xE9~',					'Andr\xC3\xA9', $body);
		$body = str_replace('Ã©', 'é', $body);
		$body = str_replace('Ã¶', 'ö', $body);
		$body = str_replace('Ã´', 'ô', $body);
		$body = str_replace('Ã¯', 'ï', $body);
		$body = str_replace('hÃrst', 'hörst', $body);
		$body = str_replace('TrÃume', 'Träume', $body);
		$body = str_replace('wÃrd', 'würd', $body);
		$body = str_replace('aquÃ', 'aquí', $body);
		$body = str_replace('finirÃ', 'finirà', $body);
		$body = str_replace('VÃƒÂ¤th', 'Väth', $body);
		$body = str_replace('mÃo', 'mío', $body);
		$body = str_replace('mÃ', 'mí', $body);
		$body = str_replace('asÃ', 'así', $body);
		$body = str_replace('Ã˜', 'Ø', $body);
		$body = str_replace('Ã±', 'ñ', $body);
		$body = str_replace('Ã—', '×', $body);
		$body = str_replace('Ã¡', 'á', $body);
		$body = str_replace('ÃƒÂ¡', 'á', $body);
		$body = str_replace('Ã'.chr(0xA0), 'à', $body);
		$body = str_replace('Â'.chr(0xA0), "\u{00A0}", $body);
		$body = str_replace('Â'.chr(0xAD), "\u{00AD}", $body);
		$body = str_replace(chr(0xC3).chr(0x82).chr(0xC2).chr(0xA0), "\u{00A0}", $body);
		$body = str_replace(chr(0xC3).chr(0x82).chr(0xC2).chr(0xAD), "\u{00AD}", $body);
		$body = str_replace('Ã¹', 'ù', $body);
		$body = str_replace('Ã²', 'ò', $body);
		$body = str_replace('Ã³', 'ó', $body);
		$body = str_replace(['Ã¼', 'ÃƒÂ¼'], ['ü', 'ü'], $body);
		$body = str_replace('Â´', '´', $body);
		$body = str_replace('Ã¤', 'ä', $body);
		$body = str_replace('ÃƒÂ©', 'é', $body);
		$body = str_replace(chr(0xC3).chr(0x82).chr(0xE2).chr(0x80).chr(0x9A), 'é', $body);
		$body = str_replace('Ã«', 'ë', $body);
		$body = str_replace('Ã¨', 'è', $body);
		$body = str_replace('Ãº', 'ú', $body);
		$body = str_replace('â‚¬', '€', $body);
		$body = str_replace('ÃƒÂ¢Ã¢€Å¡Ã‚Â¬', '€', $body);
		$body = str_replace('Ãƒâ€”', '—', $body);
		$body = str_replace('â€™', '’', $body);
		$body = str_replace('â€œ', '“', $body);
		$body = str_replace('â€¦', '…', $body);
		$body = str_replace('â€?', '”', $body);
		$body = str_replace('â€“', '–', $body);
		$body = str_replace('â€™', '“', $body);
		$body = str_replace('ÃƒÂ¢','â', $body);
		$body = str_replace('Ã£', 'ã', $body);
		$body = preg_replace('~(?<=['.$ok_letters.'])'.chr(0x80).'(?=['.$ok_letters.']|$)~', '€', $body);
		$body = preg_replace('~(?<=['.$ok_letters.'])'.chr(0x85).'(?=['.$ok_letters.']|$)~', '…', $body);
		$body = preg_replace('~(?<=['.$ok_letters.'])'.chr(0x91).'(?=['.$ok_letters.']|$)~', '‘', $body);
		$body = preg_replace('~(?<=['.$ok_letters.'])'.chr(0x92).'(?=['.$ok_letters.']|$)~', '’', $body);
		$body = preg_replace('~(?<=['.$ok_letters.'])'.chr(0x93).'(?=['.$ok_letters.']|$)~', '“', $body);
		$body = preg_replace('~(?<=['.$ok_letters.'])'.chr(0x94).'(?=['.$ok_letters.']|$)~', '”', $body);
		$body = preg_replace('~(?<=['.$ok_letters.'])'.chr(0x95).'(?=['.$ok_letters.']|$)~', '•', $body);
		$body = preg_replace('~(?<=['.$ok_letters.'])'.chr(0x99).'(?=['.$ok_letters.']|$)~', '™', $body);
		$body = preg_replace('~(?<=['.$ok_letters.'])'.chr(0xAD).'(?=['.$ok_letters.']|$)~', '', $body);
		$body = preg_replace('~(?<=['.$ok_letters.'])'.chr(0xAE).'(?=['.$ok_letters.']|$)~', '®', $body);
		$body = preg_replace('~(?<=['.$ok_letters.'])'.chr(0xB2).'(?=['.$ok_letters.']|$)~', '²', $body);
		$body = preg_replace('~(?<=['.$ok_letters.'])'.chr(0xB3).'(?=['.$ok_letters.']|$)~', '³', $body);
		$body = preg_replace('~(?<=['.$ok_letters.'])'.chr(0xB4).'(?=['.$ok_letters.']|$)~', '´', $body);
		$body = preg_replace('~(?<=['.$ok_letters.'])'.chr(0xBD).'(?=['.$ok_letters.']|$)~', '½', $body);
		$body = preg_replace('~(?<=['.$ok_letters.'])'.chr(0xC1).'(?=['.$ok_letters.']|$)~', 'Á', $body);
		$body = preg_replace('~(?<=['.$ok_letters.'])'.chr(0xC2).'(?=['.$ok_letters.']|$)~', 'Â', $body);
		$pody = preg_replace('~(?<=[\/-c.])'.		   chr(0xC2).'+(?=['.$ok_letters.']|$)~', '', $body);
		$pody = preg_replace('~(?<=[\/-c.])'.		   chr(0xC2).chr(0x73).'+(?=['.$ok_letters.']|$)~', '', $body);
		$body = preg_replace('~(?<=[\/-c.])'.		   chr(0xC3).chr(0x82). '(?=['.$ok_letters.']|$)~', '', $body);
		$body = preg_replace('~(?<=['.$ok_letters.'])'.chr(0xC4).'(?=['.$ok_letters.']|$)~', 'Ä', $body);
		$body = preg_replace('~(?<=['.$ok_letters.'])'.chr(0xC6).'(?=['.$ok_letters.']|$)~', 'Æ', $body);
		$body = preg_replace('~(?<=['.$ok_letters.'])'.chr(0xC9).'(?=['.$ok_letters.']|$)~', 'É', $body);
		$body = preg_replace('~(?<=['.$ok_letters.'])'.chr(0xD6).'(?=['.$ok_letters.']|$)~', 'Ö', $body);
		$body = preg_replace('~(?<=['.$ok_letters.'])'.chr(0xD7).'(?=['.$ok_letters.']|$)~', '×', $body);
		$body = preg_replace('~(?<=['.$ok_letters.'])'.chr(0xDC).'(?=['.$ok_letters.']|$)~', 'Ü', $body);
		$body = preg_replace('~(?<=['.$ok_letters.'])'.chr(0xDF).'(?=['.$ok_letters.']|$)~', 'ß', $body);
		$body = preg_replace('~(?<=['.$ok_letters.'])'.chr(0xE0).'(?=['.$ok_letters.']|$)~', 'à', $body);
		$body = preg_replace('~(?<=['.$ok_letters.'])'.chr(0xE1).'(?=['.$ok_letters.']|$)~', 'á', $body);
		$body = preg_replace('~(?<=['.$ok_letters.'])'.chr(0xE2).'(?=['.$ok_letters.']|$)~', 'â', $body);
		$body = preg_replace('~(?<=['.$ok_letters.'])'.chr(0xC2).chr(0xA3). '(?=['.$ok_letters.']|$)~', 'ã', $body);
		$body = preg_replace('~(?<=['.$ok_letters.'])'.chr(0xE4).'(?=['.$ok_letters.']|$)~', 'ä', $body);
		$body = preg_replace('~(?<=['.$ok_letters.'])'.chr(0xE4).chr(0xE4).'(?=['.$ok_letters.']|$)~', 'ä', $body);
		$body = preg_replace('~(?<=['.$ok_letters.'])'.chr(0xE5).'(?=['.$ok_letters.']|$)~', 'å', $body);
		$body = preg_replace('~(?<=['.$ok_letters.'])'.chr(0xE7).'(?=['.$ok_letters.']|$)~', 'ç', $body);
		$body = preg_replace('~(?<=['.$ok_letters.'])'.chr(0xE8).'(?=['.$ok_letters.']|$)~', 'è', $body);
		$body = preg_replace('~(?<=['.$ok_letters.'])'.chr(0xE9).'(?=['.$ok_letters.']|$)~', 'é', $body);
		$body = preg_replace('~(?<=['.$ok_letters.'])'.chr(0xEA).'(?=['.$ok_letters.']|$)~', 'ê', $body);
		$body = preg_replace('~(?<=['.$ok_letters.'])'.chr(0xEB).'(?=['.$ok_letters.']|$)~', 'ë', $body);
		$body = preg_replace('~(?<=['.$ok_letters.'])'.chr(0xEC).'(?=['.$ok_letters.']|$)~', 'ì', $body);
		$body = preg_replace('~(?<=['.$ok_letters.'])'.chr(0xED).'(?=['.$ok_letters.']|$)~', 'í', $body);
		$body = preg_replace('~(?<=['.$ok_letters.'])'.chr(0xB2).'(?=['.$ok_letters.']|$)~', '²', $body);
		$body = preg_replace('~(?<=['.$ok_letters.'])'.chr(0xB7).'(?=['.$ok_letters.']|$)~', '·', $body);
		$body = preg_replace('~(?<=['.$ok_letters.'])'.chr(0xEF).'(?=['.$ok_letters.']|$)~', 'ï', $body);
		$body = preg_replace('~(?<=['.$ok_letters.'])'.chr(0xF1).'(?=['.$ok_letters.']|$)~', 'ñ', $body);
		$body = preg_replace('~(?<=['.$ok_letters.'])'.chr(0xF2).'(?=['.$ok_letters.']|$)~', 'ò', $body);
		$body = preg_replace('~(?<=['.$ok_letters.'])'.chr(0xF3).'(?=['.$ok_letters.']|$)~', 'ó', $body);
		$body = preg_replace('~(?<=['.$ok_letters.'])'.chr(0xF4).'(?=['.$ok_letters.']|$)~', 'ô', $body);
		$body = preg_replace('~(?<=['.$ok_letters.'])'.chr(0xF5).'(?=['.$ok_letters.']|$)~', 'õ', $body);
		$body = preg_replace('~(?<=['.$ok_letters.'])'.chr(0xF6).'(?=['.$ok_letters.']|$)~', 'ö', $body);
		$body = preg_replace('~(?<=['.$ok_letters.'])'.chr(0xF7).'(?=['.$ok_letters.']|$)~', '÷', $body);
		$body = preg_replace('~(?<=['.$ok_letters.'])'.chr(0xF8).'(?=['.$ok_letters.']|$)~', 'ø', $body);
		$body = preg_replace('~(?<=['.$ok_letters.'])'.chr(0xF9).'(?=['.$ok_letters.']|$)~', 'ù', $body);
		$body = preg_replace('~(?<=['.$ok_letters.'])'.chr(0xFA).'(?=['.$ok_letters.']|$)~', 'ú', $body);
		$body = preg_replace('~(?<=['.$ok_letters.'])'.chr(0xFB).'(?=['.$ok_letters.']|$)~', 'û', $body);
		$body = preg_replace('~(?<=['.$ok_letters.'])'.chr(0xFC).'(?=['.$ok_letters.']|$)~', 'ü', $body);
		$body = preg_replace('~(?<=['.$ok_letters.'])'.chr(0xFD).'(?=['.$ok_letters.']|$)~', 'ý', $body);
		$body = preg_replace('~(?<=['.$ok_letters.'])'.chr(0xFE).'(?=['.$ok_letters.']|$)~', 'þ', $body);
		$body = preg_replace('~(?<=['.$ok_letters.'])'.chr(0xFF).'(?=['.$ok_letters.']|$)~', 'ÿ', $body);

#		if (isset($url)) {
#			$body = str_replace("\xC3\x82", '', $body);
#		}

		if ($trim) {
			$body = preg_replace('"(\s{2,})"u', ' ', $body);
			$body = utf8_mytrim($body);
		}

		if ($body !== $pre_body) {
			syslog(LOG_INFO, 'new text: '.$body);
		} else {
			syslog(LOG_INFO, 'new text is the same');
		}

		$url_body = '';
		if ($url && !str_contains($body, '%')) {
			$url_body = get_ascii_url($body);
			syslog(LOG_INFO, 'urlencoded text: '.$url_body);
			if ($url_body !== $pre_body) {
				$body = $url_body;
			} else {
				syslog(LOG_INFO, 'new urlencoded text is the same');
			}
		}

		if ($pre_body !== $body) {
			syslog(LOG_INFO, 'updating presence');

			$and_extra_id    = $extra_id    ? ' AND '.$extra_id.   ' = '.$$extra_id    		    : '';
			$and_extra_field = $extra_field ? ' AND '.$extra_field.' = "'.addslashes($$extra_field).'"' : '';

			if (!db_update($table, '
				UPDATE `'.$table.'` SET
							 '.$orig_body_field.' = '.($compress ? 'COMPRESS("'.addslashes($body).'")'	   : '"'.addslashes($body).'"').'
				WHERE BINARY '.$orig_body_field.' = '.($compress ? 'COMPRESS("'.addslashes($pre_body).'")' : '"'.addslashes($pre_body).'"').'
				  AND '.$idname.' = '.$id.
				  $and_extra_id.
				  $and_extra_field)
			) {
				return false;
			}
			$replacements += db_affected();
			if (!DRYRUN) {
				sleep(1);
			}
		}
	}
	syslog(LOG_INFO, 'replaced '.$replacements.' entries');
	syslog(LOG_DEBUG, "ids: ".implode(', ', $ids));
	return true;
}
