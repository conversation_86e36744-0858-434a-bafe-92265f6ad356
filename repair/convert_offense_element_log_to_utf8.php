#!/usr/bin/php
<?php

define('CURRENTSTAMP',	time());
define('DEBUG',		!isset($_SERVER['CRON']));
# define('DRYRUN',true);

require_once '../public_html/_exit_if_offline.inc';
require_once '../public_html/_db.inc';

set_memory_limit(GIGABYTE);

ob_implicit_flush(true);
$rc = convert_to_utf8();
exit(show_messages_cli() || !$rc ? 1 : 0);

function convert_to_utf8(): bool {
	global $argv;
	$act = isset($argv[1]) && $argv[1] === 'act';

	$badcnt = 0;

	foreach ([
		# NOTE: todo
		#
		# albummap
		# artist_rating
		# contact_ticket
		# favourite_genre
		# flock
		# flockmessage
		# flocktopic
		# location_rating
		# message
		# music_rating
		# nick
		# organization_rating
		# party_rating
		# poll
		# poll_answer
		# profile
		# profile_image
		# profile_image_caption
		# topic
		# user
		# valentine

		# NOTE: already done
		#
		# albumelement
		# albumelement_comment
		# answeringmachine
		# artist_comment
		# cartoon_comment
		# chat_message		# OFFENSEID < 432438
		# city_comment
		# column
		# column_comment
		# compoentry_comment
		# contest_comment
		# deaduser_comment
		# directmessage
		# interview_comment
		# label_comment
		# location_comment
		# meeting_comment
		# music_comment
		# news_comment
		# organization_comment
		# party_comment
		# photo_comment
		# poll_comment
		# promo_comment
		# report
		# report_comment
		# review_comment
		# stream_comment
		# user_comment
		# user_text
		# video_comment
		# virtualtour_comment
		# weblog
		# weblog_comment
	] as $element) {
		$logs = db_rowuse_array('offense_element_log','
			SELECT * FROM offense_element_log
			WHERE ELEMENT = "'.$element.'"
			ORDER BY OFFENSEID'
		);
		if (!$logs) {
			if ($logs === false) {
				return false;
			}
			continue;
		}
		foreach ($logs as $log) {
			$item = unserialize($log['BODY']);
			if (str_starts_with($item['BODY'], "\xEF\xBB\xBF")) {
				$new_BODY = substr($item['BODY'], 3);
			} else {
				$decomp_BODY = db_single(null, 'SELECT UNCOMPRESS("'.addslashes($item['BODY']).'")');
				if ($decomp_BODY) {
					$item['BODY'] = $decomp_BODY;
				}
				$new_BODY = win1252_to_utf8($item['BODY']);
			}
			if ($new_BODY !== $item['BODY']) {
				error_log('new BODY for element '.$element.' and offense '.$log['OFFENSEID']);

				$new_item = $item;
				rem_utf8_bom($new_BODY);
				$new_item['BODY'] = $new_BODY;
				$item_str = serialize($new_item);

				if (!$act) {
#					print_rr($item, 'OLD item');
#					print_rr($new_item, 'NEW item');
					continue;
				}

				if (!db_update('offense_element_log','
					UPDATE offense_element_log SET
						BODY = "'.addslashes($item_str).'"
					WHERE BODY = "'.addslashes($log['BODY']).'"
					  AND OFFENSEID = '.$log['OFFENSEID'])
				) {
					return false;
				}
			}
		}
	}
	return true;
}
