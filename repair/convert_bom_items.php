#!/usr/bin/php
<?php

define('CURRENTSTAMP',	time());
define('DEBUG',		!isset($_SERVER['CRON']));
# define('DRYRUN',	true);

const RESETLINE	= "\n\x1B[A\x1B[K";

require_once '../public_html/_exit_if_offline.inc';
require_once '../public_html/_db.inc';
require_once '../public_html/_comment.inc';

set_memory_limit(GIGABYTE);

ob_implicit_flush(true);
switch ($argv[1] ?? null) {
default:
	echo $argv[0], " (comments | directmessages)\n";
	exit(1);
case 'comments':
	$rc = convert_bom_comments();
	break;
case 'directmessages':
	$rc = convert_bom_directmessages();
	break;
case 'anonymised_directmessages':
	$rc = convert_bom_anonymised_directmessages();
	break;
}
exit(show_messages_cli() || !$rc ? 1 : 0);

function convert_bom_anonymised_directmessages(): bool {
	$messageids = db_simpler_array('anonymised_directmessage', 'SELECT MESSAGEID FROM anonymised_directmessage');
	if (!$messageids) {
		return $messageids !== false;
	}
	$total = count($messageids);
	foreach ($messageids as $ndx => $messageid) {
		echo RESETLINE, 'processing anonymised_directmessage ', $messageid, ' ', round(100 * $ndx / $total, 1), '%';
		if (!db_update('anonymised_directmessage', ' 
			UPDATE anonymised_directmessage SET
				BODY = CONVERT(CAST(CONVERT(SUBSTRING(BODY,4) USING latin1) AS BINARY) USING utf8mb4)
			WHERE HEX(SUBSTRING(binary BODY, 1, 3)) = "C3AFC2"
			  AND MESSAGEID = '.$messageid)
		||	!waitforslaves('anonymised_directmessage')
		) {
			return false;
		}
	}
	echo "\n done";
	return true;
}

function convert_bom_directmessages(): bool {
	$step = 1000;

	$maxid = db_single('directmessage_data', 'SELECT MAX(MESSAGEID) FROM directmessage_data');
	if (!$maxid) {
		return $maxid !== false;
	}
		
	$startid = 1;
	
	for ($i = $startid; $i <= $maxid; $i += $step) {
		echo RESETLINE, 'processing directmessages till ', ($i + $step), ' ', round(100 * $i / $maxid, 1), '%';
		if (!db_update('directmessage_data', ' 
			UPDATE directmessage_data SET
				BODY = CONVERT(CAST(CONVERT(SUBSTRING(BODY,4) USING latin1) AS BINARY) USING utf8mb4)
			WHERE HEX(SUBSTRING(binary BODY, 1, 3)) = "C3AFC2"
			  AND MESSAGEID BETWEEN '.$i.' AND '.($i + $step - 1))
		||	!waitforslaves('directmessage_data')
		) {
			return false;
		}
	}
	echo "\n done";
	return true;
}

function convert_bom_comments(): bool {
	$step = 1000;
	
	foreach (commentables() as $parent => $flags) {
		$table = $parent.'_comment';
	
		$maxid = db_single($table, 'SELECT MAX(COMMENTID) FROM '.$table);
		if (!$maxid) {
			if ($maxid === false) {
				return false;
			}
			continue;
		}
		
		$startid = 1;

		for ($i = $startid; $i <= $maxid; $i += $step) {
			echo RESETLINE, 'processing ', $table,'s till ', ($i + $step), ' ', round(100 * $i / $maxid, 1), '%';
			if (!db_update($table, ' 
				UPDATE '.$table.' SET
					BODY = CONVERT(CAST(CONVERT(SUBSTRING(BODY,4) USING latin1) AS BINARY) USING utf8mb4)
				WHERE HEX(SUBSTRING(binary BODY, 1, 3)) = "C3AFC2"
				  AND COMMENTID BETWEEN '.$i.' AND '.($i + $step - 1))
			||	!waitforslaves($table)
			) {
				return false;
			}
		}
		echo " done\n";
	}
	return true;
}
