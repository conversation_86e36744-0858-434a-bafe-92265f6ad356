#!/usr/bin/php
<?php

define('CURRENTSTAMP',time());
define('IDSTEP',1000);
define('RESETLINE',	"\n\x1B[A\x1B[K");
#define('DRYRUN',true);

require_once '../public_html/_offline.inc';
require_once '../public_html/_db.inc';
require_once '../public_html/_error.inc';
require_once '../public_html/_comment.inc';
ob_implicit_flush(true);

main();
show_messages_cli();

function main() {
	if ($_SERVER['argc'] >= 3) {
		return cleanup_element($_SERVER['argv'][1]);
	}
	foreach (array('message','flockmessage') as $element) {
		$maxmessageid = db_single($element,'SELECT MAX(MESSAGEID) FROM '.$element,DB_USE_MASTER);
		if (!$maxmessageid) {
			return;
		}
		for ($messageid = 1; $messageid <= $maxmessageid; $messageid += IDSTEP) {
			system($_SERVER['SCRIPT_FILENAME'].' '.$element.' '.$messageid.' '.min($messageid + IDSTEP,$maxmessageid));
 			waitforslave();
		}
	}
}
function cleanup_element($element) {
	$cleaned = array();
	$logslist = db_multirowuse_hash($element.'_log','
		SELECT MESSAGEID,l.* FROM '.$element.'_log AS l
		WHERE MESSAGEID BETWEEN '.$_SERVER['argv'][2].' AND '.$_SERVER['argv'][3].'
		ORDER BY MESSAGEID ASC, MSTAMP ASC',
		DB_USE_MASTER
	);
	if (!$logslist) {
		return;
	}
	foreach ($logslist as $messageid => $logs) {
		echo RESETLINE,"processing $element,$messageid";
		if ($logs === false) {
			return;
		}
		$prevlog = null;
		foreach ($logs as $log) {
			if (!$prevlog) {
				$prevlog = $log;
				continue;
			}
			$cmp = array();
			$differ = false;
			foreach ($log as $key => $val) {
				if ($key == 'MSTAMP'
				||	$key == 'MUSERID'
				) {
					continue;
				}
				if ($val !== $prevlog[$key]) {
					$differ = true;
					break;
				}
			}
			if (!$differ) {
				$checklist = array();
				foreach ($log as $key => $val) {
					if ($val === null) {
						$checklist[] = 'ISNULL('.$key.')';
					} else {
						$checklist[] = $key.'="'.addslashes($val).'"';
					}
				}
				if (!db_delete($element.'_log','
					DELETE FROM '.$element.'_log
					WHERE MESSAGEID='.$messageid.'
					  AND '.implode(' AND ',$checklist).'
					ORDER BY MSTAMP DESC
					LIMIT 1')
				) {
					return;
				}
				$cleaned[$messageid] = true;
				++$cnt;
			} else {
				$prevlog = $log;
			}
		}
		echo ": $cnt                ";
	}
	if ($cleaned) {
		echo ", ids: ",implodekeys(',',$cleaned);
	}
	echo "\n";
}
function waitforslave() {
	while ($behinds = db_slave_behinds(array('dbs1','dbs2','dbs3','dbe','dbultra','dbhyper','dba'),DB_FORCE_SERVER)) {
		$maxbehind = max($behinds);
		if (!$maxbehind || $maxbehind == 1) {
			return true;
		}	
		if (DEBUG) {
			error_log('sleeping for '.$maxbehind.' seconds',0);
		}
		sleep($maxbehind);
	}
	return false;
}
