#!/usr/bin/php
<?php

define('CURRENTSTAMP',time());
define('DRYRUN',true);

require_once '../public_html/_offline.inc';
require_once '../public_html/_db.inc';
require_once '../public_html/_error.inc';
require_once '../public_html/_require.inc';

ob_implicit_flush(true);
main();
show_messages_cli();

function main() {
	$table = 'location_log';
	$idname = 'LOCATIONID';
	$elements = db_rowuse_hash($table,'
		SELECT '.$idname.',ADDRESS,ZIPCODE,POBOX_ZIPCODE,MSTAMP FROM '.$table.'
		WHERE MSTAMP<1286374769-3600 AND (ADDRESS!=""
		   OR ZIPCODE!=""
		   OR POBOX_ZIPCODE!="")'
	);
	foreach ($elements as $id => $element) {
		$nots[] = '('.$idname.'='.$id.' AND MSTAMP='.$element['MSTAMP'].')';
	}
	$elements = db_rowuse_array($table,'
		SELECT '.$idname.',ADDRESS,ZIPCODE,POBOX_ZIPCODE,MSTAMP FROM '.$table.'
		WHERE MSTAMP<1286374769-3600 AND (ADDRESS!=""
		   OR ZIPCODE!=""
		   OR POBOX_ZIPCODE!="")
		   AND NOT ('.implode(' OR ',$nots).')'
	);
	foreach ($elements as $element) {
		$elementid = $element[$idname];
		foreach (array('ADDRESS','ZIPCODE','POBOX_ZIPCODE') as $key) {
			if (!$element[$key]) continue;
			$new = win1252_to_utf8($element[$key]);
			if ($new != $element[$key]) {
				$upds[$elementid][$element['MSTAMP']][] = $key.'="'.addslashes($new).'"';
			}
		}
	}
	foreach ($upds as $elementid => $mstamps) {
		foreach ($mstamps as $mstamp => $setlist) {
			if (!db_update($table,'
				UPDATE '.$table.' SET '.implode(',',$setlist).' WHERE '.$idname.'='.$elementid.' AND MSTAMP='.$mstamp)
			) {
				return;
			}
		}
	}
}
