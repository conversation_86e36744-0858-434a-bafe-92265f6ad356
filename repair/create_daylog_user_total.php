#!/usr/bin/php
<?php

define('CURRENTSTAMP',time());

require_once '/home/<USER>/public_html/_offline.inc';
require_once '/home/<USER>/public_html/_db.inc';
require_once '/home/<USER>/public_html/_error.inc';
require_once '/home/<USER>/public_html/_run_and_exit.inc';

run_and_exit();

function main(): bool {
	if (false === ($min = db_single('daylog','SELECT MIN(DAYNUM) FROM daylog'))
	||	false === ($max = db_single('daylog','SELECT MAX(DAYNUM) FROM daylog'))
	) {
		return false;
	}
	for ($i = $min; $i <= $max; ++$i) {
		if (!db_replace(['daylog', 'daylog_user_total'], '
			REPLACE INTO daylog_user_total (DAYNUM, HITS)
			SELECT DAYNUM, SUM(HITS)
			FROM daylog
			WHERE USERID > 1
			  AND DAYNUM = '.$i.'
			GROUP BY DAYNUM'
		)) {
			return false;
		}
	}
	return true;
}
