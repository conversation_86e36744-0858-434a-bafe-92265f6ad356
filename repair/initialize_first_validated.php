#!/usr/bin/php
<?php

const RESETLINE	= "\n\x1B[A\x1B[K";

define('CURRENTSTAMP',	time());
#define('DRYRUN',	true);

require_once '/home/<USER>/public_html/_offline.inc';
require_once '/home/<USER>/public_html/_db.inc';
require_once '/home/<USER>/public_html/_error.inc';

ob_implicit_flush(true);
$ok = main();
exit(show_messages_cli() || !$ok ? 1 : 0);

function main(): bool {
	$max_partyid = db_single('party', 'SELECT MAX(PARTYID) FROM party');
	if (!$max_partyid) {
		return $max_partyid !== false;
	}
	for ($i = 1; $i <= $max_partyid; ++$i) {
		$have = db_single('party', 'SELECT 1 FROM party WHERE PARTYID = '.$i);
		if (query_failed()) {
			return false;
		} elseif (!$have) {
			continue;
		}
		echo RESETLINE, 'processing party ', $i, ', ', round(100 * $i / $max_partyid, 2), '%... ';

		$histories = db_simple_hash(['party', 'party_log'], '
			(	SELECT IF(MSTAMP, MSTAMP, CSTAMP) AS MSTAMP, ACCEPTED
				FROM party_log
				WHERE PARTYID = '.$i.'
				ORDER BY MSTAMP ASC
			)
			UNION
			(	SELECT MSTAMP, ACCEPTED
				FROM party
				WHERE PARTYID = '.$i.'
			)',
			DB_FORCE_SERVER,
			CURRENT_MASTER_party_db
		);
		if (!$histories) {
			if ($histories === false) {
				return false;
			}
			continue;
		}

		$first_validated =
		 $last_validated = 0;

		foreach ($histories as $mstamp => $accepted) {
			if ($accepted) {
				$first_validated =
					$mstamp
				?:	db_single('party', 'SELECT CSTAMP FROM party WHERE PARTYID = '.$i);
			}
		}

		$reversed_histories = array_reverse($histories, preserve_keys: true);

		foreach ($reversed_histories as $mstamp => $accepted) {
			if ($accepted) {
				$last_validated =
					$mstamp
				?:	db_single('party', 'SELECT CSTAMP FROM party WHERE PARTYID = '.$i);
			}
		}
		if (!db_update('party', '
			UPDATE party SET
				FIRST_VALIDATED = '.$first_validated.',
				 LAST_VALIDATED = '. $last_validated.'
			WHERE PARTYID = '.$i)
		||	!db_update('party_log', '
			UPDATE party_log SET
				FIRST_VALIDATED = '.$first_validated.',
				 LAST_VALIDATED = '. $last_validated.'
			WHERE PARTYID = '.$i.'
			  AND MSTAMP >= '.$first_validated)
		||	!db_update('party_log', '
			UPDATE party_log SET
				LAST_VALIDATED = '.$first_validated.'
			WHERE PARTYID = '.$i.'
			  AND MSTAMP >= '.$last_validated)
		) {
			return false;
		}
	}
	return true;
}
