#!/usr/bin/php
<?php

declare(strict_types=1);

if (PHP_SAPI !== 'cli') {
	exit(1);
}

require_once __DIR__.'/../public_html/_db.inc';

$musicids = db_simpler_array(null, 'SELECT MUSICID FROM music');

foreach ($musicids as $MUSICID) {
	if (!file_exists("/mnt/gluster/music/$MUSICID.mp3")) {
		error_log("$MUSICID.mp3 does not exist");
	}
	extract(db_single_assoc(['music', 'musicproducer', 'musicproduceralias', 'artist', 'user'], "
		SELECT	m.MUSICID AS ID, m.TITLE AS MUSIC_TITLE,
				GROUP_CONCAT(COALESCE(a.NAME, CONVERT(ua.NICK USING utf8mb4), mpa.NAME) SEPARATOR ', ') AS MUSIC_AUTHOR
		FROM music m
		LEFT JOIN musicproducer mp USING (MUSICID)
		LEFT JOIN artist a				 ON mp.TYPE = 'artist'	AND mp.ID = a.ARTISTID
		LEFT JOIN user_account ua		 ON mp.TYPE = 'user'	AND mp.ID = ua.USERID
		LEFT JOIN musicproduceralias mpa ON mp.TYPE = 'alias'	AND mp.ID = mpa.ALIASID 
		WHERE m.MUSICID = $MUSICID"),
		\EXTR_OVERWRITE
	);
	if (!$MUSIC_AUTHOR) {
		error_log("$MUSICID has not author");
	}
	$music_title_s = addslashes($MUSIC_TITLE);
	$music_author_param = $MUSIC_AUTHOR ? " -a '".addslashes($MUSIC_AUTHOR)."' " : '';
	echo "id3v2 -t '$music_title_s' $music_author_param -c 'uploaded to Partyflock' /mnt/gluster/music/$MUSICID.mp3\n";
}
