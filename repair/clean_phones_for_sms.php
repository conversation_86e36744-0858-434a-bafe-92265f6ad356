#!/usr/bin/php
<?php

define('CURRENTSTAMP',time());
define('RESETLINE',	"\n\x1B[A\x1B[K");

require_once '../public_html/_exit_if_offline.inc';
require_once '../public_html/_db.inc';
require_once '../public_html/_phone.inc';

set_memory_limit(2 * GIGABYTE);

ob_implicit_flush(true);
$ok = clean_phones();
exit(show_messages_cli() || !$ok ? 1 : 0);

function clean_phones(): bool {
	foreach (['user', 'user_log'] as $table) {
		$users = db_simple_hash($table, '
			SELECT DISTINCT USERID, PHONE, CLEANPHONE
			FROM '.$table
		);
		if (!$users) {	
			return $users !== false;
		}
		foreach ($users as $userid => $info) {
			echo RESETLINE, "processing $table $userid...";
			
			[$phone, $current_clean_phone] = keyval($info);
			
			$clean_phone = clean_phone_for_sms($phone);
			
			if ($clean_phone !== $current_clean_phone) {
				if (!db_update($table, '
					UPDATE '.$table.' SET CLEANPHONE = '.$clean_phone.'
					WHERE PHONE = "'.addslashes($phone).'"
					  AND USERID = '.$userid)
				) {
					return false;
				}
				if (db_affected()) {
					echo "\nupdated user $userid phone from $phone => $clean_phone\n";
				}
				if (!waitforslaves()) {
					return false;
				}
			}
		}
	}
	echo "\n";
	return true;
}
