#!/usr/bin/php
<?php

define('CURRENTSTAMP',time());
#define('IDSTEP',1000);
#define('RESETLINE',	"\n\x1B[A\x1B[K");
#define('DRYRUN',true);

require_once '../public_html/_offline.inc';
require_once '../public_html/_db.inc';
require_once '../public_html/_error.inc';
require_once '../public_html/_presence.inc';

ob_implicit_flush(true);
$rc = main();
exit(show_messages_cli() || !$rc ? 1 : 0);

function main() {
	$_REQUEST['sELEMENT'] = $_REQUEST['sID'] = null;
	$presences = db_simple_hash('presence','SELECT PRESENCEID,SITE FROM presence WHERE SUPPORTED=1 AND TYPE=""');
	if (!$presences) return $presences === false ? false : true;
	foreach ($presences as $presenceid => $site) {
		$type = supported_presence($site);
		if ($type) {
			if (!db_update('presence','
				UPDATE presence SET TYPE="'.addslashes($type).'"
				WHERE PRESENCEID='.$presenceid)
			) {
				return false;
			}
		}
	}
	return true;
}
