#!/usr/bin/php
<?php

define('CURRENTSTAMP',time());

require_once '../public_html/_exit_if_offline.inc';
require_once '../public_html/_db.inc';
require_once '../public_html/_error.inc';
require_once '../public_html/_date.inc';
require_once '../public_html/_allstats.inc';
require_once '../public_html/_browser.inc';
require_once '../public_html/_run_and_exit.inc';

run_and_exit();

function main(): bool {
	$server_names = ['dbblack'];

	# $server_names = ['dbblack', 'dbbc'];
	$tables = [];

	$db = 'pagehitstorage';

	foreach ($server_names as $server_name) {
		$dbs = ['counter_db'];
		if ($server_name === 'dbc') {
			$dbs[] = 'pagehitstorage';
			if (!db_read(null, 'USE '.$db, SELECT, DB_FORCE_SERVER, $server_name)) {
				return false;
			}
		}
		foreach($dbs as $db) {
			foreach ([
				'pagehitdone_20231023%',
			] as $table_name) {
				echo "checking for tables like $table_name in $server_name:$db\n";

				if (false === ($tmptables = db_simpler_array(null, '
					SHOW TABLES LIKE "'.$table_name.'"',
					DB_FORCE_SERVER,
					$server_name
				))) {
					return false;
				}
				foreach ($tmptables as $tmptable) {
					$tables[$server_name.':'.$db.'.'.$tmptable] = [$server_name, $db, $tmptable];
				}
			}
		}
	}

	ksort($tables);

	if (!$tables) {
		return false;
	}

	$totals =
	$uniques = [];

	foreach ($tables as [$server_name, $db, $table]) {
		echo "checking $server_name:$db.$table\n";

		if (!($i = db_rowuse_array(null,'
			SELECT *, UNCOMPRESS(DATA) AS DATA
			FROM '.$db.'.'.$table.'
			WHERE USERID = 2269',
			DB_FORCE_SERVER,
			$server_name
		))) {
			if ($i === false) {
				return false;
			}
			continue;
		}
		foreach ($i as &$row) {
			if ($row['DATA']) {
			}
		}
		unset($row);
		print_rr($i);
	}
	return true;
}
