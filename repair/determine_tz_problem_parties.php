#!/usr/bin/php
<?php

define('CURRENTIPSTR','cli');
define('CURRENTSTAMP',time());

$path = dirname($_SERVER['SCRIPT_FILENAME']).'/../';

require_once '/home/<USER>/public_html/_date.inc';
require_once '/home/<USER>/public_html/_db.inc';
require_once '/home/<USER>/public_html/_run_and_exit.inc';

run_and_exit();

function main(): bool {
	if (!($tzs = db_simple_hash(null,'
		SELECT TIMEZONE,MIN(STAMP)
		FROM party
		JOIN lineup USING (PARTYID)
		LEFT JOIN boarding USING (BOARDINGID)
		LEFT JOIN location USING (LOCATIONID)
		JOIN city ON city.CITYID=COALESCE(boarding.CITYID,location.CITYID,party.CITYID)
		WHERE NOT ISNULL(TIMEZONE)
		GROUP BY TIMEZONE'
	))) {
		return $tzs !== false;
	}

	$storage = [
		'switch_parties'	=> [],
	];

	foreach ($tzs as $timezone => $min_stamp) {
		try {
			$dtz = new DateTimeZone($timezone);
		} catch (Exception $e) {
			error_log('bad timezone: '.$timezone);
		}
		if (!($switches = $dtz->getTransitions($min_stamp))) {
			continue;
		}
		if (!check_switches($timezone,$switches,$storage)) {
			return false;
		}
	}

#	return	file_put_contents('tz_switch_parties.txt',implode("\n",array_keys($storage['switch_parties'])))
#	&&	file_put_contents('tz_difficult_start.txt',implode("\n",array_keys($storage['difficult_starts'])));
	$inslist = [];
	foreach ($storage['switch_parties'] as $partyid) {
		$inslist[] = '('.$partyid.')';
	}
	return db_insert('switch_parties','
		INSERT IGNORE INTO switch_parties (PARTYID)
		VALUES '.implode(', ', $inslist)
	);
}

function check_switches($timezone,$switches,&$storage) {
	$prev_offset = null;

	foreach ($switches as $switch) {
		extract($switch);
		$extra_parties = db_same_hash(null,'
			SELECT DISTINCT PARTYID
			FROM party
			JOIN lineup USING (PARTYID)
			LEFT JOIN boarding USING (BOARDINGID)
			LEFT JOIN location USING (LOCATIONID)
			JOIN city ON city.CITYID=COALESCE(boarding.CITYID,location.CITYID,party.CITYID)
			WHERE TIMEZONE="'.$timezone.'"
			  AND NOT (		(ISNULL(TIME_START) OR NOT TIME_START)
					AND	(ISNULL(TIME_END) OR NOT TIME_END)
			)
			  AND '.$ts.' BETWEEN STAMP AND (STAMP+IF(DURATION_SECS,DURATION_SECS,6*'.ONE_HOUR.') + '.(2 * ONE_HOUR).')
			  AND STAMP BETWEEN '.($ts - 3 * ONE_DAY).' AND '.$ts
		);
		if (!$extra_parties) {
			if ($extra_parties === false) {
				return false;
			}
			continue;
		}
		$storage['switch_parties'] += $extra_parties;
	}
	return true;
}
