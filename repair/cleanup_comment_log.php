#!/usr/bin/php
<?php

define('CURRENTSTAMP',time());
define('IDSTEP',1000);
define('RESETLINE',	"\n\x1B[A\x1B[K");
#define('DRYRUN',true);

require_once '../public_html/_offline.inc';
require_once '../public_html/_db.inc';
require_once '../public_html/_error.inc';
require_once '../public_html/_comment.inc';
require_once '../public_html/_rating.inc';

ob_implicit_flush(true);
main();
show_messages_cli();

function main() {
	if ($_SERVER['argc'] >= 3) {
		return cleanup_element($_SERVER['argv'][1]);
	}
	foreach (commentables() as $element => $flags) {
		$element .= '_comment';
		$maxcommentid = db_single($element,'SELECT MAX(COMMENTID) FROM '.$element,DB_USE_MASTER);
		if (!$maxcommentid) {
			return;
		}
		for ($commentid = 1; $commentid <= $maxcommentid; $commentid += IDSTEP) {
			if ($element == 'meeting_comment'
			&&	$commentid > 70000
			&&	$commentid < 80000
			) {
				$commentid = 1008782228;
			}
			system($_SERVER['SCRIPT_FILENAME'].' '.$element.' '.$commentid.' '.min($commentid + IDSTEP,$maxcommentid));
 			waitforslave();
		}
	}
	foreach (RATINGABLES as $element => $flags) {
		$element .= '_rating';
		if (!($maxcommentid = db_single($element,'SELECT MAX(RATINGID) FROM '.$element,DB_USE_MASTER))) {
			return;
		}
		for ($commentid = 1; $commentid <= $maxcommentid; $commentid += IDSTEP) {
			system($_SERVER['SCRIPT_FILENAME'].' '.$element.' '.$commentid.' '.min($commentid + IDSTEP,$maxcommentid));
 			waitforslave();
		}
	}
}
function cleanup_element($element) {
	if (!preg_match('"_([a-z]+)$"',$element,$matches)) {
		return;
	}
	switch ($matches[1]) {
	case 'comment':
	case 'rating':
		$type = $matches[1];
		break;
	default:
		error_log('type not understood: '.$element,0);
		return;
	}
	$cleaned = array();
	$logslist = db_multirowuse_hash($element.'_log','
		SELECT '.strtoupper($type).'ID,l.* FROM '.$element.'_log AS l
		WHERE '.$type.'ID BETWEEN '.$_SERVER['argv'][2].' AND '.$_SERVER['argv'][3].'
		ORDER BY '.$type.'ID ASC, MSTAMP ASC',
		DB_USE_MASTER
	);
	if (!$logslist) {
		return;
	}
	foreach ($logslist as $commentid => $logs) {
		echo RESETLINE,"processing $element,$commentid";
		$prevlog = null;
		foreach ($logs as $log) {
			if (!$prevlog) {
				$prevlog = $log;
				continue;
			}
			$cmp = array();
			$differ = false;
			foreach ($log as $key => $val) {
				if ($key == 'MSTAMP'
				||	$key == 'MUSERID'
				) {
					continue;
				}
				if ($val !== $prevlog[$key]) {
					$differ = true;
					break;
				}
			}
			if (!$differ) {
				$checklist = array();
				foreach ($log as $key => $val) {
					if ($val === null) {
						$checklist[] = 'ISNULL('.$key.')';
					} else {
						$checklist[] = $key.'="'.addslashes($val).'"';
					}
				}
				if (!db_delete($element.'_log','
					DELETE FROM '.$element.'_log
					WHERE '.$type.'ID='.$commentid.'
					  AND '.implode(' AND ',$checklist).'
					ORDER BY MSTAMP DESC
					LIMIT 1')
				) {
					return;
				}
				$cleaned[$commentid] = true;
				++$cnt;
			} else {
				$prevlog = $log;
			}
		}
		echo ": $cnt                ";
	}
	if ($cleaned) {
		echo ", ids: ",implodekeys(',',$cleaned);
	}
	echo "\n";
}
function waitforslave() {
	while ($behinds = db_slave_behinds(array('dbs1','dbs2','dbs3','dbe','dbultra','dbhyper','dba'),DB_FORCE_SERVER)) {
		$maxbehind = max($behinds);
		if (!$maxbehind || $maxbehind == 1) {
			return true;
		}
		if (DEBUG) {
			error_log('sleeping for '.$maxbehind.' seconds',0);
		}
		sleep($maxbehind);
	}
	return false;
}
