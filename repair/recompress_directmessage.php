#!/usr/bin/php
<?php

define('CURRENTSTAMP',time());
define('RESETLINE',	"\n\x1B[A\x1B[K");
define('IDSTEP',	1000);
#define('DRYRUN',	true);

require_once '../public_html/_offline.inc';
require_once '../public_html/_db.inc';
require_once '../public_html/_error.inc';
require_once '../public_html/_require.inc';
require_once '../public_html/_site.inc';

ob_implicit_flush(true);
$rc = main();
echo "\n";
exit(show_messages_cli() || !$rc ? 1 : 0);

function main() {
	require_once '../public_html/defines/directmessage.inc';
	require_once '../public_html/_directmessage.inc';

	$maxi = db_single('directmessage','SELECT MAX(MESSAGEID) FROM directmessage');

	$maxi = 10000;

	for ($i = 1; $i <= $maxi; $i += IDSTEP) {
		echo RESETLINE,$i,', '.round(100 * $i / $maxi,2),'% ... ';
		
		$messages = db_simple_hash(['directmessage','directmessage_data'],'
			SELECT MESSAGEID,COMPRESSED,BODY
			FROM directmessage
			JOIN directmessage_data USING (MESSAGEID)
/*			WHERE FROM_USERID=2269 AND READM IN (0,1)*/
			WHERE MESSAGEID BETWEEN '.$i.' AND '.($i + IDSTEP - 1)
		);
		if ($messages === false) {
			return false;
		}
		if (!$messages) {
			continue;
		}
		echo 'got messages, recompressing ...';
		
		ksort($messages);
		
		foreach ($messages as $messageid => $message) {
			list($flags,$data) = keyval($message);
			
#			error_log($messageid.': inputlen: '.strlen($data),0);
			
			$ddata = dmsg_decompress($data,$flags);
			
			$dlen = strlen($ddata);
			
#			error_log($messageid.': outputlen: '.strlen($ddata),0);
			
			if (!$ddata) {
				error_log('decompression of message '.$messageid.' failed or no content',0);
				continue;
			}
			
			$cdata = @gzcompress($ddata,9);
			
			$clen = strlen($cdata);

			if (!$clen) {
				error_log('skipping message '.$messageid.' empty!',0);
				continue;
			}

			if ($clen > 65535) {
				error_log('skipping message '.$messageid.' does not fit',0);
				continue;
			}

			$compress = $clen + 4 < $dlen;
			
			if (!db_update('directmessage','
				UPDATE directmessage SET 
					COMPRESSED=(COMPRESSED&~'.(DMSG_COMPRESSED_ZLIB|DMSG_COMPRESSED_BZIP2|DMSG_COMPRESSED_MYSQL).')|'.($compress ? DMSG_COMPRESSED_MYSQL : 0).'
				WHERE MESSAGEID='.$messageid)
			||	!db_update('directmessage_data','
				UPDATE directmessage_data SET
					BODY="'.addslashes($compress ? pack('V',$dlen).$cdata : $ddata).'"
				WHERE MESSAGEID='.$messageid)
			||	!waitforslaves('directmessage')
			) {
				return false;
			}
		}
	}
	return true;
}
