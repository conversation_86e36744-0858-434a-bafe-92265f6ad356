#!/usr/bin/php
<?php

define('CURRENTSTAMP',time());
#define('DRYRUN',true);

require_once '../public_html/_offline.inc';
require_once '../public_html/_db.inc';
require_once '../public_html/_error.inc';

ob_implicit_flush(true);
main();
show_messages_cli();

function main() {
	$fixthese = db_rowuse_array('presaleinfo','
		SELECT PARTYID,WEBSITE
		FROM presaleinfo
		WHERE WEBSITE LIKE "%eventbrite%"
		ORDER BY PARTYID DESC'
	);
	if (!$fixthese) {
		return;
	}
	$ch = curl_init();
	curl_setopt_array($ch,[
		CURLOPT_ACCEPT_ENCODING	=> '',
		CURLOPT_RETURNTRANSFER	=> true,
		CURLOPT_FOLLOWLOCATION	=> true,
		CURLOPT_MAXREDIRS		=> 10,
		CURLOPT_CONNECTTIMEOUT	=> 20,
		CURLOPT_TIMEOUT			=> 20,
		CURLOPT_SSL_VERIFYPEER	=> false,
		CURLOPT_COOKIESESSION	=> true,
	]);

	foreach ($fixthese as $fixthis) {
		extract($fixthis);
		$sites = explode("\n",$WEBSITE);
		$upd = false;
		foreach ($sites as $i => &$site) {
			if (false === stripos($site,'eventbrite')) {
				continue;
			}
			$newsite = $site;
/*			curl_setopt($ch,CURLOPT_URL,$site);
			curl_exec($ch);
			$info = curl_getinfo($ch);

			if ($info['url'] != $site) {
				$newsite = $info['url'];
			}*/
			if ($newsite != $site) {
				$site = $newsite;
				$upd = true;
			}
		}
		unset($site);
		if ($upd) {
			if (!db_update('presaleinfo','
				UPDATE presaleinfo SET 
					WEBSITE	="'.addslashes(implode("\n",$sites)).'"
				WHERE WEBSITE="'.addslashes($WEBSITE).'"
				  AND PARTYID='.$PARTYID)
			) {
				return false;
			}
		}
	}
}
