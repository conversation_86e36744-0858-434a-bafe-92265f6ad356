#!/usr/bin/php
<?php

define('CURRENTSTAMP', time());
#define('DRYRUN',true);
define('RESETLINE',	"\n\x1B[A\x1B[K");

require_once '/home/<USER>/public_html/_exit_if_readonly.inc';
require_once '/home/<USER>/public_html/_db.inc';

ob_implicit_flush(true);
$ok = test();
exit(show_messages_cli() || !$ok ? 1 : 0);

function test() {
	$max_videoid = db_single('videoinfo', 'SELECT MAX(VIDEOID) FROM videoinfo');
	if (!$max_videoid) {
		return $max_videoid !== false;
	}
	$ok = true;
	$i = 2620;
	for (; $i <= $max_videoid; ++$i) {
		echo RESETLINE, 'videoinfo ', $i, '... ';
		if (!db_update('videoinfo', '
			REPLACE INTO videoinfo_new (TITLE, LOCATION, CHANNEL, CONTENT, PSTAMP, VIDEOID, CHANNEL_TYPE, MSTAMP)
			SELECT	CONVERT(CAST(CONVERT(TITLE USING latin1) AS BINARY) USING utf8mb4),
				CONVERT(CAST(CONVERT(LOCATION USING latin1) AS BINARY) USING utf8mb4),
				CONVERT(CAST(CONVERT(CHANNEL USING latin1) AS BINARY) USING utf8mb4),
				CONVERT(CAST(CONVERT(CONTENT USING latin1) AS BINARY) USING utf8mb4),
				PSTAMP,
				VIDEOID,
				CHANNEL_TYPE,
				MSTAMP
			FROM videoinfo
			WHERE VIDEOID = '.$i)
		) {
			$ok = false;
			break;
		}
	}
	echo "\n";
	return $ok;
}
