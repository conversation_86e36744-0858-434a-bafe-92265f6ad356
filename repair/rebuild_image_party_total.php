#!/usr/bin/php
<?php

define('CURRENTSTAMP',time());

require_once '../public_html/_offline.inc';
require_once '../public_html/_db.inc';

ob_implicit_flush(true);
$rc = main();
exit(show_messages_cli() || !$rc ? 1 : 0);

function main() {
	$galleryids = db_simpler_array('gallery','SELECT DISTINCT GALLERYID FROM gallery');
	if (!$galleryids) {
		return $galleryids !== false;
	}
	foreach ($galleryids as $galleryid) {
		$imgids = db_simpler_array('image','SELECT IMGID FROM image WHERE GALLERYID='.$galleryid);
		if ($imgids === false) {
			return false;
		}
		if (!$imgids) {
			continue;
		}
		if (!db_insert('image_party_total','
			REPLACE INTO image_party_total (VIEWS,GALLERYID,DAYNUM)
			SELECT SUM(COUNTER),'.$galleryid.',DAYNUM
			FROM image_counter
			WHERE IMGID IN ('.implode(',',$imgids).')
			GROUP BY DAYNUM')
		) {
			return false;
		}
	}
}
