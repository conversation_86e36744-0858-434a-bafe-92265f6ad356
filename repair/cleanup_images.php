#!/usr/bin/php
<?php

define('CURRENTSTAMP',time());
define('RESETLINE',	"\n\x1B[A\x1B[K");
define('DEBUG',!isset($_SERVER['CRON']));
#define('DRYRUN',true);

require_once '../public_html/_offline.inc';
require_once '../public_html/_db.inc';

ob_implicit_flush(true);
$rc = main();
exit(show_messages_cli() || !$rc ? 1 : 0);

function main() {
	foreach (array(
#		'albumimage',
		'uploadimage',
#		'userimage',
	) as $element) {
		switch ($element) {
		case 'userimage':
			$maxdataid = db_single('userimagedatameta','SELECT MAX(DATAID) FROM userimagedatameta',DB_USE_MASTER);
			break;
		case 'albumimage':
			$maxdataid = db_single('albumimagedatameta','SELECT MAX(DATAID) FROM albumimagedatameta',DB_USE_MASTER);
			break;
		case 'uploadimage':
			$maxdataid = db_single('uploadimage','SELECT MAX(UPIMGID) FROM uploadimage',DB_USE_MASTER);
			if ($maxdataid === false) {
				return false;
			}
			$max2 = db_single('uploadimagemeta','SELECT MAX(UPIMGID) FROM uploadimagemeta',DB_USE_MASTER);
			if ($max2 === false) {
				return false;
			}
			$maxdataid = max($maxdataid,$max2);
			$max2 = db_single('uploadimage_data_new','SELECT MAX(UPIMGID) FROM uploadimage_data',DB_USE_MASTER);
			if ($max2 === false) {
				return false;
			}
			$maxdataid = max($maxdataid,$max2);
			break;
		}
		if (!$maxdataid) {
			return $maxdataid !== false;
		}
		for ($dataid = 1; $dataid <= $maxdataid; ++$dataid) {
		 	echo RESETLINE,'processing '.$element.':',$dataid,' ',floor(100*$dataid/$maxdataid),'%';
			$have_link = $have_link2 = true;
			switch ($element) {
			case 'userimage':
				$have = db_single('userimage','SELECT USERID FROM userimage WHERE DATAID='.$dataid.' LIMIT 1',DB_USE_MASTER);
				$have_meta = db_single('userimagedatameta','SELECT 1 FROM userimagedatameta WHERE DATAID='.$dataid,DB_USE_MASTER);
				$have_data = db_single('userimagedata_new','SELECT 1 FROM data_db.userimagedata WHERE DATAID='.$dataid,DB_USE_MASTER);
				break;
			case 'albumimage':
				$have = db_single('albumelement','
					SELECT 1 FROM albumelement WHERE DATAID='.$dataid.' LIMIT 1
				UNION	SELECT 1 FROM albumelement WHERE THMBID='.$dataid.' LIMIT 1
				UNION	SELECT 1 FROM albumelement_tobedeleted WHERE DATAID='.$dataid.' LIMIT 1
				UNION	SELECT 1 FROM albumelement_tobedeleted WHERE THMBID='.$dataid.' LIMIT 1
				',
				DB_USE_MASTER
				);
				$have_meta = db_single('albumimagedatameta','SELECT STOREID FROM albumimagedatameta WHERE DATAID='.$dataid.' LIMIT 1',DB_USE_MASTER);
				$have_data = $have_meta ? dbdata_single('albums',$have_meta,'SELECT 1 FROM u'.$have_meta.' WHERE DATAID='.$dataid.' LIMIT 1',DB_USE_MASTER) : null;
				break;
			case 'uploadimage':
				$have =	db_single('uploadimage','SELECT 1 FROM uploadimage WHERE UPIMGID='.$dataid.' LIMIT 1',DB_USE_MASTER);
				$have_meta = db_single('uploadimagemeta','SELECT 1 FROM uploadimagemeta WHERE UPIMGID='.$dataid.' LIMIT 1',DB_USE_MASTER);
				$have_data = db_single('uploadimage_data_new','SELECT 1 FROM data_db.uploadimage_data WHERE UPIMGID='.$dataid.' LIMIT 1',DB_USE_MASTER);
				$have_link = db_single('uploadimage_link','SELECT 1 FROM uploadimage_link WHERE UPIMGID='.$dataid.' LIMIT 1',DB_USE_MASTER);
				$have_link2 = db_single('uploadimage_link_log','SELECT 1 FROM uploadimage_link_log WHERE UPIMGID='.$dataid.' LIMIT 1',DB_USE_MASTER);
				break;
			}
			if ($have === false || $have_meta === false || $have_data === false || $have_link === false || $have_link2 === false) {
				return false;
			}
			if ((!$have && !$have_data && !$have_meta && !$have_link && !$have_link2)
			||	(	$element == 'albumimage'
				?	$have
				:	($have &&  $have_data &&  $have_meta && ($have_link || $have_link2))
				)
			) {
				continue;
			}
			echo $element,':',$dataid,
				', have == '.($have ?: 'no'),
				', have_meta == '.($have_meta ? 'true' : 'false'),
				', have_data == '.($have_data ? 'true' : 'false'),
				', have_link == '.($have_link ? 'true' : 'false'),
				', have_link2 == '.($have_link2 ? 'true' : 'false'),
			"\n";

			if ($element == 'userimage') {
				$data = dbdata_single('userimagedata',$dataid,'SELECT DATA FROM userimagedata WHERE DATAID='.$dataid,DB_USE_MASTER);
				if ($data) {
					static $i = 0;
					++$i;
					file_put_contents('userimage_user_'.$have.'_data_'.$dataid.'_index_'.$i,$data);
				}
			}

			switch ($element) {
			case 'userimage':
				$data = db_single('userimagedata_new','SELECT DATA FROM data_db.userimagedata WHERE DATAID='.$dataid);
				if ($data === false) {
					return false;
				}
				if (!db_insert('userimage_bak','
					INSERT IGNORE INTO userimage_bak
					SELECT * FROM userimage
					WHERE DATAID='.$dataid)
				||	!db_insert('userimagedatameta_bak','
					INSERT IGNORE INTO userimagedatameta_bak
					SELECT * FROM userimagedatameta
					WHERE DATAID='.$dataid)
				||	$data
				&&	!db_insert('userimagedata_bak','
					INSERT IGNORE INTO userimagedata_bak SET
						DATA	="'.addslashes($data).'",
						DATAID	='.$dataid)
				) {
					return false;
				}
				if (!db_delete('userimage','
					DELETE FROM userimage
					WHERE DATAID='.$dataid)
				||	!db_delete('userimagedata_new','
					DELETE FROM data_db.userimagedata
					WHERE DATAID='.$dataid)
				||	!db_delete('userimagedatameta','
					DELETE FROM userimagedatameta
					WHERE DATAID='.$dataid)
				) {
					return false;
				}
				break;
			case 'albumimage':
				if ($have_meta === true) {
					echo "BAD META:$dataid\n";
					break;
				}
				if (!db_delete('albums','
					DELETE FROM u'.$have_meta.'
					WHERE DATAID='.$dataid)
				||	!db_delete('albumimagedatameta','
					DELETE FROM albumimagedatameta
					WHERE DATAID='.$dataid)
				||	!db_insert('albumimagedatafree','
					INSERT IGNORE INTO albumimagedatafree SET DATAID='.$dataid)
				) {
					return false;
				}
				break;
			case 'uploadimage':
				if (!db_create('uploadimage_data_new','
					CREATE TABLE IF NOT EXISTS data_db.BEFORE_DELETE_uploadimage_data
					LIKE data_db.uploadimage_data')
				||	!db_create('uploadimage','
					CREATE TABLE IF NOT EXISTS BEFORE_DELETE_uploadimage
					LIKE uploadimage')
				||	!db_create('uploadimage_link','
					CREATE TABLE IF NOT EXISTS BEFORE_DELETE_uploadimage_link
					LIKE uploadimage_link')
				||	!db_create('uploadimage_link_log','
					CREATE TABLE IF NOT EXISTS BEFORE_DELETE_uploadimage_link_log
					LIKE uploadimage_link_log')
				||	!db_create('uploadimagemeta','
					CREATE TABLE IF NOT EXISTS BEFORE_DELETE_uploadimagemeta
					LIKE uploadimagemeta')

				||	!db_replace('uploadimage_data_new','
					REPLACE INTO data_db.BEFORE_DELETE_uploadimage_data
					SELECT * FROM data_db.uploadimage_data
					WHERE UPIMGID='.$dataid)
				||	!db_replace('uploadimage','
					REPLACE INTO BEFORE_DELETE_uploadimage
					SELECT * FROM uploadimage
					WHERE UPIMGID='.$dataid)
				||	!db_replace('uploadimage_link','
					REPLACE INTO BEFORE_DELETE_uploadimage_link
					SELECT * FROM uploadimage_link
					WHERE UPIMGID='.$dataid)
				||	!db_replace('uploadimage_link_log','
					REPLACE INTO BEFORE_DELETE_uploadimage_link_log
					SELECT * FROM uploadimage_link_log
					WHERE UPIMGID='.$dataid)
				||	!db_replace('uploadimagemeta','
					REPLACE INTO BEFORE_DELETE_uploadimagemeta
					SELECT * FROM uploadimagemeta
					WHERE UPIMGID='.$dataid)
				
				||	!db_delete('uploadimage_data_new','
					DELETE FROM data_db.uploadimage_data
					WHERE UPIMGID='.$dataid)
				||	!db_delete('uploadimage','
					DELETE FROM uploadimage
					WHERE UPIMGID='.$dataid)
				||	!db_delete('uploadimage_link','
					DELETE FROM uploadimage_link
					WHERE UPIMGID='.$dataid)
				||	!db_delete('uploadimage_link_log','
					DELETE FROM uploadimage_link_log
					WHERE UPIMGID='.$dataid)
				||	!db_delete('uploadimagemeta','
					DELETE FROM uploadimagemeta
					WHERE UPIMGID='.$dataid)
				) {
					return false;
				}
				break;
			}
		}
	}
	return true;
}

