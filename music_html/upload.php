<?php

define('CURRENTSTAMP', time());

require_once '../public_html/_exit_if_offline.inc';
require_once '../public_html/_currentuser.inc';
require_once '../public_html/_require.inc';
require_once '../public_html/_error.inc';
require_once '../public_html/_date.inc';
require_once '../public_html/_music.inc';
require_once '../public_html/_execute.inc';
require_once '../public_html/_helper.inc';
require_once '../public_html/_nocache.inc';
require_once '../public_html/_mimetype.inc';
require_once '../public_html/_sourcehost.inc';

function bail(): never {
	mail_log('bad bail()', item: $GLOBALS);
	$msgid = store_messages();
	leave($msgid ?: -1);
}

function ok_bail(): never {
	$msgid = store_messages();
	leave($msgid);
}

function leave(int $msgid = 0): never {
	if ($msgid === -1) {
		$msgid = 'x';
	}
	header('Location: '.get_source_host().'/music/'.($_REQUEST['MUSICID'] ?? 'form').($msgid ? '?msgid='.$msgid : ''));
	exit;
}

send_no_cache_headers();

header('Content-Type: text/html; charset=windows-1252');

$currentuser = new _currentuser(TINY_USER | SKIP_SETTINGS);

if (empty($_POST)) {
	register_error('music:error:no_data_maybe_too_large_LINE');
	bail();
}
if (!require_user()) {
	bail(403);
}
if (!may_upload_music()) {
	register_error('music:error:no_rights_to_upload_LINE');
	bail();
}
if (!require_something_trim($_POST, 'TITLE', null, null, utf8: true)
||	!require_anything_trim($_POST, 'REMIXINFO', utf8: true)
||	!require_anything_trim($_POST, 'ORIGARTIST', utf8: true)
||	false === require_element($_POST, 'CCLICENSE', ['', 'by', 'by-sa', 'by-nd', 'by-nc', 'by-nc-sa', 'by-nc-nd'], strict: true)
||	!optional_file('FILE')
||	isset($_POST['FLAGS'])
&&	!require_number_array($_POST, 'FLAGS')
) {
	bail(400);
}

$flags = 0;

if (isset($_POST['FLAGS'])) {
	foreach ($_POST['FLAGS'] as $flg) {
		$flags |= $flg;
	}
}

if (have_file('FILE')) {
	$tmpname = $_FILES['FILE']['tmp_name'];
	$mimetype = mimetype_binary_file($tmpname);
	if (!$mimetype) {
		register_warning('upload:error:file_type_could_not_be_determined_LINE');
		warning('Bestandstype kon niet bepaald worden!');
		ok_bail();
	}
	if (str_starts_with($mimetype, 'audio/x-wav')) {
		$filetype = 'wav';
	} else {
		[$rc, $stdout, $stderr] = execute('mp3info -x '.$tmpname, EXECUTE_IGNORE_RC | EXECUTE_IGNORE_STDERR);
		if ($stdout
		&&	preg_match('"Media Type:\s+MPEG.+Layer III"i', $stdout)
		) {
			$filetype = 'mp3';
		} else {
			copy($tmpname, $dstname = '/tmpdisk/__no_music_'.uniqid());
			error_log('not a music file: '.$dstname.' ('.$mimetype.')',0);
			register_error('upload:error:file_is_not_a_music_file_LINE', ['MIMETYPE' => $mimetype]);
			bail();
		}
	}
}
$setlist[] = 'INFO = "'.(empty($_POST['INFO']) ? '' : addslashes($_POST['INFO'])).'"';

if (!empty($_POST['ORIGARTIST'])) {
	$_POST['ORIGARTIST'] = preg_replace('"^(?:dj|artist)\s+"iu', '', $_POST['ORIGARTIST']);
}
if (!empty($_POST['REMIXINFO'])
&&	preg_match('"^\s*\(\s*(.*)\s*\)\s*$"u', $_POST['REMIXINFO'], $matches)
) {
	$_POST['REMIXINFO'] = $matches[1];
}
if (($flags & MUSIC_IS_REMIX)
&&	!empty($_POST['REMIXINFO'])
&&	($nick = $GLOBALS['currentuser']->row['NICK'])
) {
	if (preg_match('"^(?:'.preg_quote(win1252_to_utf8($nick), '"').' )?remix$"iu', $_POST['REMIXINFO'])) {
		$_POST['REMIXINFO'] = '';
	}
}

$setlist[] = 'CCLICENSE	="'.($_POST['CCLICENSE'] ? addslashes($_POST['CCLICENSE']) : null).'"';
$setlist[] = 'TITLE	="'.addslashes(utf8_ucfirst($_POST['TITLE'])).'"';
$setlist[] = 'REMIXINFO	="'.addslashes($_POST['REMIXINFO']).'"';
$setlist[] = 'ORIGARTIST="'.addslashes(utf8_ucfirst($_POST['ORIGARTIST'])).'"';
$setlist[] = 'ACTIVE	='.(isset($_POST['ACTIVE']) ? 1 : 0);
$setlist[] = 'GID	='.($gid = have_number($_POST, 'GID') ? $_POST['GID'] : 0);
$setlist[] = 'LABELID	='.(have_number($_POST,'LABELID') ? $_POST['LABELID'] : 0);
$setlist[] = 'FLAGS	='.$flags;

if (have_admin('music')) {
	$setlist[] = 'RIGHTSFREE = '.(isset($_POST['RIGHTSFREE']) ? 1 : 0);
} else {
	$buma = db_single('musicrights', 'SELECT BUMA FROM musicrights WHERE USERID = '.CURRENTUSERID);
	if ($buma === false) {
		bail();
	}
	$setlist[] = 'RIGHTSFREE = '.($buma === null ? 'NULL' : ($buma ? 0 : 1));
}

if ($musicid = have_number($_REQUEST, 'MUSICID')) {
	$music = db_single_assoc('music','SELECT CUSERID FROM music WHERE MUSICID='.$musicid);
	if ($music === false) {
		bail();
	}
	if (!$music) {
		register_error('music:error:nonexistent_LINE', ['ID' => $musicid]);
		bail();
	}
	if (!require_self_or_admin($music['CUSERID'], 'music')) {
		bail();
	}
	if (!db_insert('music_log','
		INSERT INTO music_log
		SELECT * FROM music
		WHERE MUSICID = '.$musicid)
	||	!db_update('music','
		UPDATE music SET
			MUSERID	='.CURRENTUSERID.',
			MSTAMP	='.CURRENTSTAMP.',
			'.implode(', ', $setlist).'
		WHERE MUSICID = '.$musicid)
	) {
		bail();
	}
	register_notice('music:notice:changed_LINE');

} else {
	if (!db_insert('music','
		INSERT INTO music SET
			CUSERID	= '.CURRENTUSERID.',
			CSTAMP	= '.CURRENTSTAMP.',
			'.implode(', ', $setlist))
	) {
		bail();
	}
	$musicid = $_REQUEST['MUSICID'] = db_insert_id();
	register_notice('music:notice:added_LINE');
}
db_replace('music_genre','
REPLACE INTO music_genre SET
	MUSICID	= '.$musicid.',
	GID	= '.$gid
);
if (!db_delete('musicproducer','
	DELETE FROM musicproducer
	WHERE MUSICID = '.$musicid)
) {
	// no action, must continue
}
$setlist = [];
if (have_array($_POST, 'PRODUCER')) {
	foreach ($_POST['PRODUCER'] as $type => $ids) {
		switch ($type) {
		case 'artist':
		case 'user':
		case 'alias':
			foreach ($ids as $id) {
				$setlist[] = '('.$musicid.', '.$id.', "'.$type.'")';
			}
		}
	}
}
if ($setlist) {
	if (!db_insert('musicproducer','
		INSERT INTO musicproducer (MUSICID, ID ,TYPE)
		VALUES '.implode(', ', $setlist))
	) {
	}
}
if (!empty($filetype)) {
	move_uploaded_file($tmpname, $newname = '/mnt/gluster/music/'.$musicid);
	chmod($newname, 0644);
	db_update('music', '
		UPDATE music SET
			STATE = '.MUSICSTATE_UPLOAD_READY.'
		WHERE MUSICID='.$musicid
	);
	shell_exec('/home/<USER>/bin/processmusic.php '.$musicid.' '.$filetype.' </dev/null >/dev/null 2>/dev/null &');
}
ok_bail();
