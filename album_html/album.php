<?php

declare(strict_types=1);

define('CURRENTSTAMP', time());

require_once __DIR__.'/../public_html/_exit_if_offline.inc';
require_once __DIR__.'/../public_html/_argparse.inc';
require_once __DIR__.'/../public_html/_require.inc';
require_once __DIR__.'/../public_html/_visibility.inc';
require_once __DIR__.'/../public_html/_image_bail.inc';
require_once __DIR__.'/../public_html/_helper.inc';
require_once __DIR__.'/../public_html/_modified.inc';
require_once __DIR__.'/../public_html/_spider.inc';
require_once __DIR__.'/../public_html/_hosts.inc';
require_once __DIR__.'/../public_html/_album.inc';
require_once __DIR__.'/../public_html/defines/robots.inc';

deny_robots_if_partyflock_under_load();

if (ROBOT && !ALLOW_ROBOTS_ALBUM_IMAGES) {
	require_once __DIR__.'/../public_html/_getcurrentaddr.inc';
	$hostname = memcached_hostname_from_ipbin(CURRENTIPBIN, CURRENTIPSTR);
	if (false === stripos($hostname, 'google')) {
		image_bail(429);
	}
}

if (!($albumelementid = (int)($_SERVER['eALBUMELEMENTID'] ?? 0))) {
	image_bail(404);
}

$_SERVER['eDATAID'] = isset($_SERVER['eDATAID']) ? (int)$_SERVER['eDATAID'] : 0;

if (isset($_REQUEST['nocache'])) {
	moved_permanently(rtrim(str_replace(['nocache', '&&'], ['', '&'], $_SERVER['REQUEST_URI']), '&?'));
}

require_once __DIR__.'/../public_html/_db.inc';
require_once __DIR__.'/../public_html/_currentuser.inc';

$currentuser = new _currentuser(TINY_USER | SKIP_SETTINGS);

$have_admin = have_admin(['album','albumelement','offense','helpdesk']);
$tables = ['albumelement'];
if ($have_admin) {
	$tables[] = 'albumelement_tobedeleted';
	$tables[] = 'albumelement_log';
}
$first = true;
foreach ($tables as $table) {
	$qstr = "
		SELECT	ALBUMID AS USERID, ACCEPTED, VISIBILITY_ELEMENT, DATAID, THMBID, ORIGNAME,
				albumimagedatabusy.DATAID IS NOT NULL AS BUSY
		FROM $table
		LEFT JOIN albumimagedatabusy USING (DATAID)
		WHERE ALBUMELEMENTID = $albumelementid";
	if ($first) {
		$first = false;
		$albumelement = memcached_single_assoc_if_not_owner(
			'USERID',
			'albumelement',
			$qstr,
			ONE_DAY,
			get_albumelement_table_key($albumelementid, $table)
		);
	} else {
		$albumelement = db_single_assoc($table,$qstr);
	}
	if ($albumelement) {
		break;
	}
	if ($albumelement === false) {
		image_bail(503);
	}
}
if (!$albumelement
||	(	$_SERVER['eTHUMBNAIL']
	&&	!$albumelement['THMBID']
	||	!$albumelement['DATAID']
	)
) {
	image_bail(404);
}
$self = CURRENTUSERID === $albumelement['USERID'];
$self_or_admin = $self || $have_admin;

if (!$self_or_admin
&&	(	!$albumelement['ACCEPTED']
	||	!($visibility = _visibility($albumelement, 'ELEMENT'))
	)
||	!$self_or_admin
&&	invisible_profile($albumelement['USERID'])
) {
	image_bail(403);
}

$dataid = $albumelement[$_SERVER['eTHUMBNAIL'] ? 'THMBID' : 'DATAID'];
$etagid = crc32("$dataid:$albumelementid");

if ($etagid !== $_SERVER['eDATAID']) {
	header('Location: '.ALBUM_HOST."/$albumelementid{$_SERVER['eTHUMBNAIL']}_$etagid.{$_SERVER['eFILETYPE']}",true,301);
	exit;
}
$meta_key = 'album_image:new_meta:'.$dataid;
if ($dataid) {
	do {
		$meta = memcached_single_assoc_if_not_self(
			$albumelement['USERID'],
			'albumimagedatameta',
			"SELECT FILETYPE, LEN, CSTAMP, STOREID FROM albumimagedatameta WHERE DATAID = $dataid",
			HALF_HOUR,
			$meta_key
		);
		if ($meta === false) {
			error_log("no meta for albumelement $albumelementid, dataid $dataid");
			image_bail(500);
		}
		if ($meta) {
			break;
		}
		if ($meta_key === false) {
			break;
		}
		$meta_key = false;
	} while (true);
}
if (empty($meta)) {
	image_bail(404);
}

if ($meta['FILETYPE'] !== $_SERVER['eFILETYPE']) {
	moved_permanently(ALBUM_HOST."/$albumelementid{$_SERVER['eTHUMBNAIL']}_$dataid.{$meta['FILETYPE']}");
}

header('Cache-Control: '.
	($albumelement['VISIBILITY_ELEMENT'] === EVERYBODY ? 'public' : 'private').
	',max-age='.(!empty($albumelement['BUSY']) || CURRENTUSERID === $albumelement['USERID'] ? '0,must-revalidate' : 43200)
);

if (!force_refresh()
&&	not_modified($meta['CSTAMP'], (string)$etagid)
) {
	image_bail(304);
}

header('Content-Type: image/'.($meta['FILETYPE'] === 'jpg' ? 'jpeg' : $meta['FILETYPE']));

$flags = CURRENTSTAMP - $meta['CSTAMP'] < SLAVE_MAX_BEHIND ? DB_USE_MASTER : 0;

if (empty($meta['STOREID'])) {
	image_bail(503);
}
$storeid = $meta['STOREID'];
if (!($data = dbdata_single('albums', $storeid, "
	SELECT DATA
	FROM albums.u$storeid
	WHERE DATAID = $dataid",
	$flags,
	maxbehind: CURRENTSTAMP - $meta['CSTAMP'] - 10))
) {
	image_bail($data === false ?503 : 404);
}
header('Content-Length: '.strlen($data));
echo $data;
