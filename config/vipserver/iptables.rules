# Generated by iptables-save v1.8.9 (nf_tables) on Wed May  8 00:18:06 2024
*filter
:INPUT DROP [258646:14196113]
:FORWARD DROP [0:0]
:OUTPUT ACCEPT [21699479:2991281767]
:f2b-sshd - [0:0]
-A INPUT -p tcp -m multiport --dports 22,722,44422 -j f2b-sshd
-A INPUT -i eth1 -m comment --comment "allow all traffic on local net" -j ACCEPT
-A INPUT -i lo -m comment --comment "allow all trafic on loopback" -j ACCEPT
-A INPUT -p icmp -m comment --comment "allow all icmp traffic" -j ACCEPT
-A INPUT -s *************/32 -i eth0 -j ACCEPT
-A INPUT -i eth0 -p udp -m udp --dport 53 -m comment --comment "accept incoming udp DNS traffic" -j ACCEPT
-A INPUT -i eth0 -p tcp -m tcp --dport 53 -m comment --comment "accept incoming tcp DNS traffic" -j ACCEPT
-A INPUT -i eth0 -p udp -m udp --dport 853 -m comment --comment "accept incoming udp DNS over DTLS traffic" -j ACCEPT
-A INPUT -i eth0 -p tcp -m tcp --dport 853 -m comment --comment "accept incoming tcp DNS over TLS traffic" -j ACCEPT
-A INPUT -i eth0 -p tcp -m tcp --dport 44422 -m comment --comment "accept incoming SSH over tcp on port 44422" -j ACCEPT
-A INPUT -i eth0 -p udp -m udp --dport 44422 -m comment --comment "accept incoming SSH over udp on port 44422" -j ACCEPT
-A INPUT -m conntrack --ctstate RELATED,ESTABLISHED -j ACCEPT
-A f2b-sshd -j RETURN
COMMIT
# Completed on Wed May  8 00:18:06 2024
