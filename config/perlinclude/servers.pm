
use strict;
use warnings;
use Socket;
use Sys::Hostname;
use lib '/home/<USER>/config';

sub getservers {
	my $select = shift;
	my @servers;
	my $external = (hostname() eq 'sandbox');
	local $/ = "\n";
	if (!open SERVERS,'</home/<USER>/config/generic/servers.txt') {
		print STDERR "failed to open /home/<USER>/config/generic/servers.txt for reading: $!\n";
		return undef;
	}
	my $line = 0;
	my %have = (
		'type'	=> 0,
		'host'	=> 0,
		'ip6'	=> 0,
	);
	while (<SERVERS>) {
		++$line;
		if (/^\s*#/) {
			next;
		}
		chomp;
		my @parts = split ';';
		my %server;
		for (@parts) {
			$_ =~ s/^\s*(.*?)\s*$/$1/;
			my @pair = split '=', $_;
			if (!defined $pair[1]) {
				print STDERR "not a pair on line $line of servers.txt\n";
				return undef;
			}
			my ($key,$val) = @pair;

			$key =~ s/^\s*(.*?)\s*$/$1/;
			$val =~ s/^\s*(.*?)\s*$/$1/;
			
			$have{$key} = 1;
			
			if ($key eq 'type'
			||	$key eq 'features'
			) {
				my %hashval;
				for (split ',', $val) {
					$hashval{$_} = 1;
				}
				$server{$key} = \%hashval;
			} else {
				$server{$key} = $val;
			}
		}

		reset(%have);
		foreach my $have_key (keys %have) {
			if (!$have{$have_key}) {
				print STDERR "pair on line $line is missing $have_key\n";
				return undef;
			}
		}

		if ($select
		&&	!defined $server{'type'}{$select}
		&&	$server{'host'} ne $select
		||	!$external
		&&	defined $server{'type'}{'local'}
		) {
			next;
		}
		$server{'id'} = $server{'host'};
		$server{'port'} = 30000;
		if (defined $server{'type'}{'local'}) {
			$server{'ip'} = $server{'host'};
		} else {
	 		if ($external) {
 				$server{'ip'} = $server{'ip6'};
 			} else {
				$server{'ip'} = inet_ntoa(inet_aton($server{'host'}));
			}
		}
		push @servers, \%server;
	}
	close SERVERS;
	return @servers;
}

return 1;
