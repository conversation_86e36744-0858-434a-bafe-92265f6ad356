use strict;
use warnings;
use IPC::Open3;
use lib '/home/<USER>/config';
use perlinclude::secret; 

my $exectimeout = 0;

sub set_max_execution_time {
	my $oldval = $exectimeout;
	$exectimeout = shift;
	return $oldval;
}

sub run {
	# USAGE:
	# my ($rc, $stdout, $stdout) = run
	use IPC::Run3;
	my $ins;
	my $outs;
	my $errs;
	my $ok = run3 @_, \$ins, \$outs, \$errs;
	my $rc = $? ? ($? >> 8) : 0;
	return ($rc, $outs, $errs);
}

sub execute {
	# USAGE:
	# my ($rc, $outs, $errs) = execute("renice 20 $$");
	# if ($rc != 0) {
	#     foreach (@$errs)
	# etc
	use IPC::Open3;
	my $pid = open3(\*INS, \*OUTS, \*ERRS, @_);
	if (!$pid) {
		return (-1, undef, undef, undef);
	}
	close INS;
	my @outs;
	my @errs;
	if ($exectimeout) {
		eval {
			local $SIG{ALRM} = sub { die "alarm\n" };
			alarm $exectimeout;
			@outs = <OUTS>;
			close OUTS;
			@errs = <ERRS>;
			close ERRS;
			waitpid $pid, 0;
		};
		alarm 0;
		if ($@) {
			close OUTS;
			close ERRS;
			die unless $@ eq "alarm\n"; # propagate unexpected errors
			return ($? ? ($? >> 8) : 0, \@outs, \@errs, 1);
		}
	} else {
		@outs = <OUTS>;
		close OUTS;
		@errs = <ERRS>;
		close ERRS;
		waitpid $pid, 0;
		return ($? ? ($? >> 8) : 0, \@outs, \@errs, 1);
	}
	return ($? ? ($? >> 8) : 0, \@outs, \@errs, 0);
}

sub execute_passthrough {
	system(@_);
	return $?
}

sub execute_within_daemon {
	use IPC::Open3;
	my $pid = 0;
	eval {
		$pid = open3(\*INS, \*OUTS, \*ERRS, @_);
	};
	if (!$pid) {
		error('failed to open3: ',$!);
		return -1;
	}
	close INS;
	local $/ = "\n";
	my @outs;
	my @errs;
	eval {
		local $SIG{ALRM} = sub { die "alarm\n" };
		alarm $exectimeout;
		@outs = <OUTS>;
		close OUTS;
		@errs = <ERRS>;
		close ERRS;
		waitpid $pid,0;
	};
	alarm 0;
	if ($@) {
		close OUTS;
		close ERRS;
		die unless $@ eq "alarm\n"; # propagate unexpected errors
		error('timeout executing: ',join(' ',@_));
		return -1;
	}
	for (@outs) {
		s/[\r\n]+/ /;
		s/\s+$//;
		s/^\s+//;
		notice($_);
#		print "\x13",$_,"\r\n";
	}
	my $error = indicatorchar('error');
	for (@errs) {
		s/[\r\n]+/ /;
		s/\s+$//;
		s/^\s+//;
		error($_);
#		print "\x14",$_,"\r\n";
	}
	return $?;
}

return 1;
