sub findapache {
	my $rootpid = 0;
	my $running = 0;
	my $type = '';
	PROCESS: for (`ps aux`) {
		# www-data  3321  0.0  0.4 380240 36248 ?        S    10:36   0:01 /usr/sbin/apache2 -k start
		if (/^(root|www\-data)\s+(\d+).*\/apache2 -k [a-z]+(?: -D ([a-z]+))?/) {
			if (	defined($3)
			&&	!$type
			) {
				$type = $3;
			}
			if (!$running) {
				$running = 1;
			}
			if ($1 eq 'root') {
				$rootpid = $2;
				last PROCESS;
			}
		}
	}
	return $running ? ($running,$rootpid,$type) : (undef,undef,undef);
}

return 1;
