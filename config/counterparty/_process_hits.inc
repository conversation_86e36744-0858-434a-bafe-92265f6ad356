<?php

declare(strict_types=1);

use JetBrains\PhpStorm\ExpectedValues;

function set_table(
	#[ExpectedValues(['pagehitnew', 'pagehitbuffer', 'counthitnew'])]
	string	$table,
): void {
	global $__process_table;
	$__process_table = $table;
}

function reset_table(): void {
	global $__old_id;
	$__old_id = 0;
}

function done_table(int $new_id): ?bool {
	syslog(LOG_INFO, "done_table, new_id: $new_id");
	global $__old_id, $__process_table;
	if ($__old_id > $new_id) {
		syslog(LOG_ERR, "detected __old_id $__old_id > new_id $new_id, should not be");
		return false;
	}
	if ($__old_id
	&&	!db_releaselock("{$__process_table}_$__old_id")
	) {
		return false;
	}
	if ($new_id === DONE_LAST) {
		if (!db_drop($__process_table, "DROP TABLE {$__process_table}_$__old_id")) {
			return false;
		}
		syslog(LOG_DEBUG, "dropped table {$__process_table}_$__old_id");
		return true;
	}
	if ($__old_id) {
		if (!db_alter('pagehitnew', "
			ALTER TABLE {$__process_table}_$__old_id
			  RENAME TO {$__process_table}_$new_id"
		)) {
			return false;
		}
		syslog(LOG_INFO, "{$__process_table}_$__old_id renamed to {$__process_table}_$new_id");
	}
	$__old_id = $new_id;
	return db_getlock("{$__process_table}_$new_id", 60);
}
