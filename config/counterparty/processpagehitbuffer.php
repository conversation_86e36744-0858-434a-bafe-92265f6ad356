#!/usr/bin/php
<?php

require_once '/home/<USER>/public_html/_exit_if_readonly.inc';
require_once '/home/<USER>/public_html/_date.inc';
require_once '/home/<USER>/public_html/_db.inc';
require_once '/home/<USER>/public_html/_error.inc';
require_once '/home/<USER>/public_html/_memcache.inc';
require_once '/home/<USER>/public_html/_helper.inc';
require_once '/home/<USER>/public_html/_allstats.inc';
require_once '/home/<USER>/public_html/_sort.inc';
require_once '/home/<USER>/public_html/_run_and_exit.inc';

set_memory_limit(4* GIGABYTE);

const RESETLINE     	    = "\n\x1B[A\x1B[K";

define('CURRENTSTAMP',		time());
define('DEBUG',				in_array('debug', $argv));

const DONE_NONE				= 0;
const DONE_INITIAL_MOVE		= 1;
const DONE_DAYLOGS			= 2;
const DONE_DAYLOGS_SPIDERS	= 3;
const DONE_IDENTITIESv2		= 4;
const DONE_USERCOUNTER		= 5;
const DONE_IPCITY			= 6;
const DONE_IPLIST			= 7;
const DONE_SPIDERHITS		= 8;
const DONE_ALLSTATS			= 9;
const DONE_BROWSERIDS		= 10;
const DONE_IP6CITY			= 11;
const DONE_IP6LIST	 		= 12;
const DONE_ID_SESSION		= 13;
const DONE_LAST				= 14;

run_and_exit();

function main(): bool {
	if (false === ($tables = db_simpler_array('pagehitbuffer','SHOW TABLES LIKE "pagehitbuffer%"',DB_FORCE_MASTER))) {
		return false;
	}
	if (!$tables) {
		error_log('no pagehitbuffer% tables found',0);
		return false;
	}
	natsort($tables);
	$tables = array_reverse($tables);

	foreach ($tables as $table) {
		if ($table === 'pagehitbuffer') {
			if (!process_table(0)) {
				return false;
			}
		} else {
			$startid = preg_match('"^pagehitbuffer_(\d+)$"',$table,$matches) ? $matches[1] : 0;
			if ($startid) {
				if (!process_table($startid)) {
					break;
				}
			} else {
				error_log('invalid pagehitbuffer table: '.$table);
				return false;
			}
		}
	}
	if (DEBUG) {
		print "\n";
	}
	return true;
}
function process_table(int $startid): bool {
	unset($GLOBALS['__oldid']);
	if (!done_table($startid)) {
		return false;
	}
	if (!$startid) {
		if (!db_getlock($key = 'process_page_hit_buffer:rename_initial', 10)
		||	!db_alter('pagehitbuffer',  'RENAME TABLE pagehitbuffer TO pagehitbuffer_1')
		||	!db_create('pagehitbuffer', 'CREATE TABLE IF NOT EXISTS pagehitbuffer LIKE pagehitbuffer_1')
		||	!db_releaselock($key)
		||	!done_table(DONE_INITIAL_MOVE)
		) {
			return false;
		}
		$startid = DONE_INITIAL_MOVE;
	}
	if (!($pagehits = db_rowuse_array('pagehitbuffer','
		SELECT SQL_NO_CACHE *
		FROM pagehitbuffer_'.$startid.'
		WHERE NOT (FLAGS & '.(ALLSTAT_FEED|ALLSTAT_ICAL).')',
		DB_FORCE_MASTER
	))) {
		return $pagehits === false ? : db_delete('pagehitbuffer','DROP TABLE pagehitbuffer_'.$startid);
	}

	number_sort($pagehits, 'STAMP');

	if (DEBUG) {
		$progress_end = count($pagehits);
		$progress_now = 0;
	}

	// generate useractivity, guestactivity, mobileactivity and spideractivity
	// use stamp and ipnum for these
	$stamp = 0;
	foreach ($pagehits as $page) {
		if (DEBUG) {
			echo RESETLINE, 'making statistics from pagehits, ', round(100 * ++$progress_now / $progress_end, 1), '% done';
		}
		if ($stamp !== $page['STAMP']) {
			$stamp = $page['STAMP'];
			[$year, $month, $day, $hour] = _getdate($stamp);
			$daynum = to_days($year, $month, $day);
		}
		extract($page);

		if ($ipv6 = $IPNUM === null) {
			[$prefix, $iident] = ipbin_to_prefix_and_iident($IPBIN);
		}
		if ($startid < DONE_USERCOUNTER
		&&	$USERID
		) {
			if (isset($usercounter[$USERID])) {
				++$usercounter[$USERID];
			} else {
				$usercounter[$USERID] = 1;
			}
		}
		if ($startid < DONE_DAYLOGS
		&&	!($FLAGS & ALLSTAT_SPIDER)
		) {
			if (isset($hits[$daynum][$USERID])) {
				++$hits[$daynum][$USERID];
			} else {
				$hits[$daynum][$USERID] = 1;
			}
		}
		if ($startid < DONE_DAYLOGS_SPIDERS
		&&	($FLAGS & ALLSTAT_SPIDER)
		) {
			if (isset($spiderhits[$daynum])) {
				++$spiderhits[$daynum];
			} else {
				$spiderhits[$daynum] = 1;
			}
		}
		if ($IDENTID
		&&	$startid < DONE_IDENTITIESv2
		) {
			if (isset($identitycnt[$IDENTID]))
				++$identitycnt[$IDENTID];
			else	  $identitycnt[$IDENTID] = 1;

			$identityinfo[$IDENTID] = $page;
		}
		if ($CITYID
		&&	!($FLAGS & ALLSTAT_CHOSENCITY)
		&&	!($FLAGS & ALLSTAT_SMALL_SCREEN)
		) {
			# store cityid, but hopefully for non mobile devices
			if ($IPNUM
			&&	$startid < DONE_IPCITY
#			&&	ip_for_cmp($IPNUM) == $IPNUM
			) {
				if (isset($ipcity[$daynum][$IPNUM][$CITYID]))
					++$ipcity[$daynum][$IPNUM][$CITYID];
				else	  $ipcity[$daynum][$IPNUM][$CITYID] = 1;
			}
			if ($ipv6
			&&	$startid < DONE_IP6CITY
			) {
				if (isset($ip6city[$daynum][$prefix][$iident][$CITYID]))
					++$ip6city[$daynum][$prefix][$iident][$CITYID];
				else	  $ip6city[$daynum][$prefix][$iident][$CITYID] = 1;
			}
		}
		if ($USERID > 1) {
			if ($ipv6
			&&	$startid < DONE_IP6LIST
			) {
				$ip6last[$USERID][$prefix][$iident] = $stamp;
			}
			if ($IPNUM
			&&	$startid < DONE_IPLIST
			) {
				$iplast[$USERID][$IPNUM] = $stamp;
			}
			if ($startid < DONE_ID_SESSION) {
				$sessionuse[$USERID][$SESSIONID] = $stamp;
			}
		}
		if ($startid < DONE_ALLSTATS) {
			$sectionid = $page['SECTIONID'];
			$domain = $page['DOMAIN'];
			if (isset($allstat[$daynum][$hour][$domain][$CITYID][$sectionid][$FLAGS])) {
				++$allstat[$daynum][$hour][$domain][$CITYID][$sectionid][$FLAGS];
			} else {
				$allstat[$daynum][$hour][$domain][$CITYID][$sectionid][$FLAGS] = 1;
			}
		}
		if ($startid < DONE_SPIDERHITS
		&&	($FLAGS & ALLSTAT_SPIDER)
		) {
			if (isset($spiderhit[$daynum])) {
				++$spiderhit[$daynum];
			} else {
				$spiderhit[$daynum] = 1;
			}
		}
		if ($startid < DONE_BROWSERIDS
		&&	$USERID > 1
		&&	$BROWSERID
		) {
			$browserstat[$USERID][$BROWSERID] = $stamp;
		}
	}

	switch ($startid) {
	case DONE_NONE:
		error_log('DONE_NONE?',0);
		return false;
	case DONE_INITIAL_MOVE:
		if (isset($hits)) {
			foreach ($hits as $daynum => $USERIDs) {
				foreach ($USERIDs as $USERID => $hits) {
					$hitsvals[] = "($daynum,$USERID,$hits)";
				}
			}
			unset($hits);
			if (!do_parts('daylog','INSERT INTO daylog (DAYNUM,USERID,HITS) VALUES ',$hitsvals,' ON DUPLICATE KEY UPDATE HITS=HITS+VALUES(HITS)')) {
				return false;
			}
			unset($hitsvals);
		}
		if (!done_table(DONE_DAYLOGS)) {
			return false;
		}
	case DONE_DAYLOGS:
		if (isset($spiderhits)) {
			foreach ($spiderhits as $daynum => $hits) {
				$spiderhitsvals[] = "($daynum,$hits)";
			}
			unset($spiderhits);
			if (!do_parts('daylog_spiders','INSERT INTO daylog_spiders (DAYNUM,HITS) VALUES ',$spiderhitsvals,' ON DUPLICATE KEY UPDATE HITS=HITS+VALUES(HITS)')) {
				return false;
			}
			unset($spiderhitsvals);
		}
		if (!done_table(DONE_DAYLOGS)) {
			return false;
		}
	case DONE_DAYLOGS_SPIDERS:
		if (isset($identitycnt)) {
			$identitiesv4 = [];
			foreach ($identitycnt as $IDENTID => $cnt) {
				if (!isset($identityinfo[$IDENTID])) {
					_error("no identityinfo found for identid: $IDENTID",0);
					return false;
				}
				$page = $identityinfo[$IDENTID];
				$identitiesv4[] = '('.$IDENTID.','.$page['STAMP'].','.$page['USERID'].',"'.addslashes($page['IPBIN']).'",'.$cnt.','.$page['BROWSERID'].',b\''.($page['FLAGS'] & ALLSTAT_SMALL_SCREEN ? 1 : 0).'\')';
			}
			unset($identitycnt);
			if (!do_parts('identityv4','
				INSERT INTO identityv4 (IDENTID,LAST_HERE,LAST_USERID,LAST_IPBIN,HITS,BROWSERID,SMALL_SCREEN) VALUES ',$identitiesv4,
				' ON DUPLICATE KEY UPDATE
					HITS		=HITS+VALUES(HITS),
					SMALL_SCREEN	=VALUES(SMALL_SCREEN),
					BROWSERID	=VALUES(BROWSERID),
					LAST_HERE	=VALUES(LAST_HERE),
					LAST_USERID	=VALUES(LAST_USERID),
					LAST_IPBIN	=VALUES(LAST_IPBIN)')
			) {
				return false;
			}
			unset($identitiesv4);
		}
		if (!done_table(DONE_IDENTITIESv2)) {
			return false;
		}
	case DONE_IDENTITIESv2:
		if (isset($usercounter)) {
			foreach ($usercounter as $USERID => $hits) {
				$uservals[] = "($USERID,$hits)";
			}
			unset($usercounter);
			if (!do_parts('user_data_counter','INSERT INTO user_data_counter (USERID,COUNTER) VALUES ',$uservals,' ON DUPLICATE KEY UPDATE COUNTER=COUNTER+VALUES(COUNTER)')) {
				return false;
			}
			unset($uservals);
		}
		if (!done_table(DONE_USERCOUNTER)) {
			return false;
		}
	case DONE_USERCOUNTER:
		if (isset($ipcity)) {
			foreach ($ipcity as $daynum => $daynums) {
				foreach ($daynums as $IPNUM => $cities) {
					foreach ($cities as $CITYID => $hits) {
						$cityvals[] = "($daynum,$IPNUM,$CITYID,$hits)";
					}
				}
			}
			unset($ipcity);
			if (!do_parts('ipcity','INSERT INTO ipcity (DAYNUM,IPNUM,CITYID,HITS) VALUES ',$cityvals,' ON DUPLICATE KEY UPDATE HITS=HITS+VALUES(HITS)')) {
				return false;
			}
			unset($cityvals);
		}
		if (!done_table(DONE_IPCITY)) {
			return false;
		}
	case DONE_IPCITY:
		if (isset($iplast)) {
			foreach ($iplast as $USERID => $IPNUMs) {
				foreach ($IPNUMs as $IPNUM => $stamp) {
					$ipvals[] = "($USERID,$IPNUM,$stamp)";
				}
			}
			unset($iplast);
			if (!do_parts('iplist','
				INSERT INTO iplist (LAST_USERID,IPNUM,LAST_USED)
				VALUES ',$ipvals,'
				ON DUPLICATE KEY UPDATE
					LAST_USED	=VALUES(LAST_USED)')
			) {
				return false;
			}
			unset($ipvals);
		}
		if (!done_table(DONE_IPLIST)) {
			return false;
		}
	case DONE_IPLIST:
		if (isset($spiderhit)) {
			foreach ($spiderhit as $daynum => $hits) {
				$spidervals[] = "($daynum,$hits)";
			}
			unset($spiderhit);
			if (!do_parts('spiderhit','INSERT INTO spiderhit (DAYNUM,HITS) VALUES ',$spidervals,' ON DUPLICATE KEY UPDATE HITS=HITS+VALUES(HITS)')) {
				return false;
			}
			unset($spidervals);
		}
		if (!done_table(DONE_SPIDERHITS)) {
			return false;
		}
	case DONE_SPIDERHITS:
		if (isset($allstat)) {
			foreach ($allstat as $daynum => $hours) {
				foreach ($hours as $hour => $domains) {
					foreach ($domains as $domain => $cities) {
						foreach ($cities as $CITYID => $sections) {
							foreach ($sections as $sectionid => $FLAGSs) {
								foreach ($FLAGSs as $FLAGS => $hits) {
									$allvals[] = "($daynum,$hour,'$domain',$CITYID,$sectionid,$FLAGS,$hits)";
								}
							}
						}
					}
				}
			}
			unset($allstat);

			if (!do_parts('allstat','
				INSERT INTO allstat (DAYNUM,HOUR,DOMAIN,CITYID,SECTIONID,FLAGS,HITS)
				VALUES ',$allvals,' ON DUPLICATE KEY UPDATE HITS=HITS+VALUES(HITS)')
			) {
				return false;
			}
			unset($allvals);
		}
		if (!done_table(DONE_ALLSTATS)) {
			return false;
		}
	case DONE_ALLSTATS:
		if (isset($browserstat)) {
			$vals = array();
			foreach ($browserstat as $USERID => $browsers) {
				foreach ($browsers as $BROWSERID => $lstamp) {
					$vals[] = '('.$USERID.','.$BROWSERID.','.$lstamp.')';
				}
			}
			if (!do_parts('browserstat','
				REPLACE INTO browserstat (USERID,ID,LSTAMP)
				VALUES ',$vals)
			) {
				return false;
			}
			unset($vals);
			unset($browserstat);
		}
		if (!done_table(DONE_BROWSERIDS)) {
			return false;
		}
	case DONE_BROWSERIDS:
		if (isset($ip6city)) {
			$cityvals = [];
			foreach ($ip6city as $daynum => $prefixes) {
				foreach ($prefixes as $prefix => $iidents) {
					foreach ($iidents as $iident => $cities) {
						foreach ($cities as $CITYID => $hits) {
							$cityvals[] = "($daynum,0x$prefix,0x$iident,$CITYID,$hits)";
						}
					}
				}
			}
			unset($ip6city);
			if (!do_parts('ip6city','
				INSERT INTO ip6city (DAYNUM,PREFIX,IIDENT,CITYID,HITS)
				VALUES ',$cityvals,'
				ON DUPLICATE KEY UPDATE HITS=HITS+VALUES(HITS)')
			) {
				return false;
			}
			unset($cityvals);
		}
		if (!done_table(DONE_IP6CITY)) {
			return false;
		}
	case DONE_IP6CITY:
		if (isset($ip6last)) {
			$ip6vals = [];
			foreach ($ip6last as $USERID => $prefixes) {
				foreach ($prefixes as $prefix => $iidents) {
					foreach ($iidents as $iident => $stamp) {
						$ip6vals[] = "($USERID,0x$prefix,0x$iident,$stamp)";
					}
				}
			}
			unset($ip6last);
			if (!do_parts('ip6list','
				INSERT INTO ip6list (LAST_USERID,PREFIX,IIDENT,LAST_USED)
				VALUES ',$ip6vals,'
				ON DUPLICATE KEY UPDATE
					LAST_USED	=VALUES(LAST_USED)')
			) {
				return false;
			}
			unset($ip6vals);
		}
		if (!done_table(DONE_IP6LIST)) {
			return false;
		}
	case DONE_IP6LIST:
		if (isset($sessionuse)) {
			$vals = [];
			foreach ($sessionuse as $USERID => $sessions) {
				foreach ($sessions as $SESSIONID => $lstamp) {
					$vals[] = '('.$USERID.','.$SESSIONID.','.$lstamp.')';
				}
			}
			if (!do_parts('user_session','
				INSERT INTO user_session (USERID,SESSIONID,LAST_USED)
				VALUES ',$vals,'
				ON DUPLICATE KEY UPDATE LAST_USED=VALUES(LAST_USED)')
			) {
				return false;
			}
			unset($vals);
			unset($sessionuse);
		}
		if (!done_table(DONE_ID_SESSION)) {
			return false;
		}
	case DONE_ID_SESSION:
		[$year, $month, $day, $hour, $min, $sec] = _getdate(time());
		if (!db_alter('pagehitbuffer','
			ALTER TABLE pagehitbuffer_'.(DONE_LAST - 1).'
			  RENAME AS pagehitdone_'.sprintf('%d%02d%02d%02d%02d%02d',$year,$month,$day,$hour,$min,$sec).'_'.getmypid())
		) {
			return false;
		}
		if (!done_table(DONE_LAST)) {
			return false;
		}
	}
	return true;
}

function do_parts(
	string	$table,
	string	$pre,
	array	$parts,
	?string	$post = null
): bool {
	# don't do parts, as we do not know how to resume of something breaks mid-way
	return db_insert($table, $pre.implode(',', $parts).$post);
/*	if (($size = count($parts)) > 1000) {
		for ($i = 0; $i < $size; $i += 1000) {
			$slice = array_slice($parts, $i, 1000);
			if (!db_insert($table, $pre.implode(', ', $slice).$post)) {
				return false;
			}
		}
	} elseif (!db_insert($table, $pre.implode(',', $parts).$post)) {
		return false;
	}
	return true;*/
}

function done_table(int $newid): ?bool {
	if (DEBUG) {
		echo RESETLINE,"done_table $newid, ",round(100 * $newid / DONE_LAST, 2),"%";
	}
	if ($newid === DONE_LAST) {
		return unlock_tables($newid - 1);
	}
	if (!empty($GLOBALS['__oldid'])) {
		if (!db_alter('pagehitbuffer','
			ALTER TABLE pagehitbuffer_'.$GLOBALS['__oldid'].'
			  RENAME AS pagehitbuffer_'.$newid)
		) {
			return false;
		}
	}
	$GLOBALS['__oldid'] = $newid;
	return lock_tables($newid);
}

function lock_tables(int $id): ?bool {
	return db_getlock('pagehitbuffer_'.$id, 10);
}

function unlock_tables(int $id): bool {
	db_releaselock('pagehitbuffer_'.$id, 'dbblack');
	return true;
}
