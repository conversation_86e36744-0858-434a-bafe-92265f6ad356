diff --recursive --unified joelbutcher_original/facebook-graph-sdk/src/BatchRequest.php joelbutcher/facebook-graph-sdk/src/BatchRequest.php
--- joelbutcher_original/facebook-graph-sdk/src/BatchRequest.php	2025-01-31 12:16:58.000000000 +0100
+++ joelbutcher/facebook-graph-sdk/src/BatchRequest.php	2025-06-07 14:25:54.947961048 +0200
@@ -49,7 +49,7 @@
      * @param null|AccessToken|string $accessToken
      * @param null|string             $graphVersion
      */
-    public function __construct(Application $app = null, array $requests = [], $accessToken = null, $graphVersion = null)
+    public function __construct(?Application $app = null, array $requests = [], $accessToken = null, $graphVersion = null)
     {
         parent::__construct($app, $accessToken, 'POST', '', [], null, $graphVersion);
 
diff --recursive --unified joelbutcher_original/facebook-graph-sdk/src/Client.php joelbutcher/facebook-graph-sdk/src/Client.php
--- joelbutcher_original/facebook-graph-sdk/src/Client.php	2025-01-31 12:16:58.000000000 +0100
+++ joelbutcher/facebook-graph-sdk/src/Client.php	2025-06-07 14:29:21.670202024 +0200
@@ -86,7 +86,7 @@
      * @param null|ClientInterface $httpClient
      * @param bool            $enableBeta
      */
-    public function __construct(ClientInterface $httpClient = null, $enableBeta = false)
+    public function __construct(?ClientInterface $httpClient = null, $enableBeta = false)
     {
         $this->httpClient = $httpClient ?: Psr18ClientDiscovery::find();
         $this->enableBetaMode = $enableBeta;
Only in joelbutcher/facebook-graph-sdk/src: Client.php~
diff --recursive --unified joelbutcher_original/facebook-graph-sdk/src/Exception/ResponseException.php joelbutcher/facebook-graph-sdk/src/Exception/ResponseException.php
--- joelbutcher_original/facebook-graph-sdk/src/Exception/ResponseException.php	2025-01-31 12:16:58.000000000 +0100
+++ joelbutcher/facebook-graph-sdk/src/Exception/ResponseException.php	2025-06-07 14:28:13.562358888 +0200
@@ -43,7 +43,7 @@
      * @param Response     $response          the response that threw the exception
      * @param SDKException $previousException the more detailed exception
      */
-    public function __construct(Response $response, SDKException $previousException = null)
+    public function __construct(Response $response, ?SDKException $previousException = null)
     {
         $this->response = $response;
         $this->responseData = $response->getDecodedBody();
Only in joelbutcher/facebook-graph-sdk/src/Exception: ResponseException.php~
diff --recursive --unified joelbutcher_original/facebook-graph-sdk/src/Helper/RedirectLoginHelper.php joelbutcher/facebook-graph-sdk/src/Helper/RedirectLoginHelper.php
--- joelbutcher_original/facebook-graph-sdk/src/Helper/RedirectLoginHelper.php	2025-01-31 12:16:58.000000000 +0100
+++ joelbutcher/facebook-graph-sdk/src/Helper/RedirectLoginHelper.php	2025-06-07 14:26:58.592150515 +0200
@@ -59,7 +59,7 @@
      * @param null|PersistentDataInterface $persistentDataHandler the persistent data handler
      * @param null|UrlDetectionInterface   $urlHandler            the URL detection handler
      */
-    public function __construct(OAuth2Client $oAuth2Client, PersistentDataInterface $persistentDataHandler = null, UrlDetectionInterface $urlHandler = null)
+    public function __construct(OAuth2Client $oAuth2Client, ?PersistentDataInterface $persistentDataHandler = null, ?UrlDetectionInterface $urlHandler = null)
     {
         $this->oAuth2Client = $oAuth2Client;
         $this->persistentDataHandler = $persistentDataHandler ?: new SessionPersistentDataHandler();
Only in joelbutcher/facebook-graph-sdk/src/Helper: RedirectLoginHelper.php~
diff --recursive --unified joelbutcher_original/facebook-graph-sdk/src/Request.php joelbutcher/facebook-graph-sdk/src/Request.php
--- joelbutcher_original/facebook-graph-sdk/src/Request.php	2025-01-31 12:16:58.000000000 +0100
+++ joelbutcher/facebook-graph-sdk/src/Request.php	2025-06-07 14:27:28.799271373 +0200
@@ -92,7 +92,7 @@
      * @param null|string             $eTag
      * @param null|string             $graphVersion
      */
-    public function __construct(Application $app = null, $accessToken = null, $method = null, $endpoint = null, array $params = [], $eTag = null, $graphVersion = null)
+    public function __construct(?Application $app = null, $accessToken = null, $method = null, $endpoint = null, array $params = [], $eTag = null, $graphVersion = null)
     {
         $this->setApp($app);
         $this->setAccessToken($accessToken);
@@ -167,7 +167,7 @@
      *
      * @param null|Application $app
      */
-    public function setApp(Application $app = null)
+    public function setApp(?xApplication $app = null)
     {
         $this->app = $app;
     }
Only in joelbutcher/facebook-graph-sdk/src: Request.php~
