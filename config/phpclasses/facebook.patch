diff -ruN facebook_orig/graph-sdk/src/Facebook/Authentication/OAuth2Client.php facebook/graph-sdk/src/Facebook/Authentication/OAuth2Client.php
--- facebook_orig/graph-sdk/src/Facebook/Authentication/OAuth2Client.php	2018-12-11 23:56:31.000000000 +0100
+++ facebook/graph-sdk/src/Facebook/Authentication/OAuth2Client.php	2022-03-15 13:38:25.928007752 +0100
@@ -143,7 +143,7 @@
             'scope' => implode(',', $scope)
         ];
 
-        return static::BASE_AUTHORIZATION_URL . '/' . $this->graphVersion . '/dialog/oauth?' . http_build_query($params, null, $separator);
+        return static::BASE_AUTHORIZATION_URL . '/' . $this->graphVersion . '/dialog/oauth?' . http_build_query($params, '', $separator);
     }
 
     /**
diff -ruN facebook_orig/graph-sdk/src/Facebook/FacebookApp.php facebook/graph-sdk/src/Facebook/FacebookApp.php
--- facebook_orig/graph-sdk/src/Facebook/FacebookApp.php	2018-12-11 23:56:31.000000000 +0100
+++ facebook/graph-sdk/src/Facebook/FacebookApp.php	2022-03-13 23:19:47.430129186 +0100
@@ -26,7 +26,7 @@
 use Facebook\Authentication\AccessToken;
 use Facebook\Exceptions\FacebookSDKException;
 
-class FacebookApp implements \Serializable
+class FacebookApp # implements \Serializable
 {
     /**
      * @var string The app ID.
@@ -91,7 +91,7 @@
      *
      * @return string
      */
-    public function serialize()
+    public function __serialize()
     {
         return implode('|', [$this->id, $this->secret]);
     }
@@ -101,7 +101,7 @@
      *
      * @param string $serialized
      */
-    public function unserialize($serialized)
+    public function __unserialize($serialized)
     {
         list($id, $secret) = explode('|', $serialized);
 
diff -ruN facebook_orig/graph-sdk/src/Facebook/GraphNodes/Collection.php facebook/graph-sdk/src/Facebook/GraphNodes/Collection.php
--- facebook_orig/graph-sdk/src/Facebook/GraphNodes/Collection.php	2018-12-11 23:56:31.000000000 +0100
+++ facebook/graph-sdk/src/Facebook/GraphNodes/Collection.php	2022-03-28 16:29:39.546056428 +0200
@@ -35,6 +35,7 @@
 use ArrayIterator;
 use Countable;
 use IteratorAggregate;
+use Traversable;
 
 class Collection implements ArrayAccess, Countable, IteratorAggregate
 {
@@ -162,7 +163,7 @@
      *
      * @return int
      */
-    public function count()
+    public function count(): int
     {
         return count($this->items);
     }
@@ -172,7 +173,7 @@
      *
      * @return ArrayIterator
      */
-    public function getIterator()
+    public function getIterator(): Traversable
     {
         return new ArrayIterator($this->items);
     }
@@ -184,7 +185,7 @@
      *
      * @return bool
      */
-    public function offsetExists($key)
+    public function offsetExists(mixed $key): bool
     {
         return array_key_exists($key, $this->items);
     }
@@ -196,7 +197,7 @@
      *
      * @return mixed
      */
-    public function offsetGet($key)
+    public function offsetGet(mixed $key): mixed
     {
         return $this->items[$key];
     }
@@ -209,7 +210,7 @@
      *
      * @return void
      */
-    public function offsetSet($key, $value)
+    public function offsetSet(mixed $key, mixed $value): void
     {
         if (is_null($key)) {
             $this->items[] = $value;
@@ -225,7 +226,7 @@
      *
      * @return void
      */
-    public function offsetUnset($key)
+    public function offsetUnset(mixed $key): void
     {
         unset($this->items[$key]);
     }
diff -ruN facebook_orig/graph-sdk/src/Facebook/Http/RequestBodyMultipart.php facebook/graph-sdk/src/Facebook/Http/RequestBodyMultipart.php
--- facebook_orig/graph-sdk/src/Facebook/Http/RequestBodyMultipart.php	2018-12-11 23:56:31.000000000 +0100
+++ facebook/graph-sdk/src/Facebook/Http/RequestBodyMultipart.php	2022-03-15 13:38:39.327429089 +0100
@@ -144,7 +144,7 @@
      */
     private function getNestedParams(array $params)
     {
-        $query = http_build_query($params, null, '&');
+        $query = http_build_query($params, '', '&');
         $params = explode('&', $query);
         $result = [];
 
diff -ruN facebook_orig/graph-sdk/src/Facebook/Http/RequestBodyUrlEncoded.php facebook/graph-sdk/src/Facebook/Http/RequestBodyUrlEncoded.php
--- facebook_orig/graph-sdk/src/Facebook/Http/RequestBodyUrlEncoded.php	2018-12-11 23:56:31.000000000 +0100
+++ facebook/graph-sdk/src/Facebook/Http/RequestBodyUrlEncoded.php	2022-03-15 13:38:13.372549964 +0100
@@ -50,6 +50,6 @@
      */
     public function getBody()
     {
-        return http_build_query($this->params, null, '&');
+        return http_build_query($this->params, '', '&');
     }
 }
diff -ruN facebook_orig/graph-sdk/src/Facebook/Url/FacebookUrlManipulator.php facebook/graph-sdk/src/Facebook/Url/FacebookUrlManipulator.php
--- facebook_orig/graph-sdk/src/Facebook/Url/FacebookUrlManipulator.php	2018-12-11 23:56:31.000000000 +0100
+++ facebook/graph-sdk/src/Facebook/Url/FacebookUrlManipulator.php	2022-03-15 13:37:46.857696682 +0100
@@ -53,7 +53,7 @@
             }
 
             if (count($params) > 0) {
-                $query = '?' . http_build_query($params, null, '&');
+                $query = '?' . http_build_query($params, '', '&');
             }
         }
 
@@ -81,7 +81,7 @@
         }
 
         if (strpos($url, '?') === false) {
-            return $url . '?' . http_build_query($newParams, null, '&');
+            return $url . '?' . http_build_query($newParams, '', '&');
         }
 
         list($path, $query) = explode('?', $url, 2);
@@ -94,7 +94,7 @@
         // Sort for a predicable order
         ksort($newParams);
 
-        return $path . '?' . http_build_query($newParams, null, '&');
+        return $path . '?' . http_build_query($newParams, '', '&');
     }
 
     /**
