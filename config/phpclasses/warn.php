<?php

declare(strict_types=1);

const DEFAULT_WARN_AFTER_X_TIMES   = 7;
const DEFAULT_WARN_AFTER_X_SECONDS = 600;

function setup(?string &$is_cli = null): void {
	require_once ($is_cli = PHP_SAPI === 'cli' ? '/home/<USER>/public_html/' : null).'_memcache.inc';
}
function warn(
	string	$message,
	?int	$warn_after_x_times		= null,
	?int	$warn_after_x_seconds	= null,
): bool {
	setup($is_cli);

	$warn_after_x_times   ??= DEFAULT_WARN_AFTER_X_TIMES;
	$warn_after_x_seconds ??= DEFAULT_WARN_AFTER_X_SECONDS;

	if (($fails = memcached_increment($message, 1, 1, $warn_after_x_seconds)) >= $warn_after_x_times) {
		[$message_type, $destination] = $is_cli ? [0, null] : [1, '<EMAIL>'];
		error_log($fails.'× '.$message, $message_type, $destination);
		return true;
	}
	return false;
}
