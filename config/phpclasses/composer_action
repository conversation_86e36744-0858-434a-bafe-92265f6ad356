#!/bin/bash

set -e	# exit immediately after failure
# set -x -v

cd -- "$(dirname -- "$(realpath "${BASH_SOURCE[0]}")")"

DATE=
QUIET=
ACTION=
VERBOSE=
ARGUMENTS=()
BACKUP=

for arg in $@; do
	case "$arg" in
	--quiet)	QUIET=--quiet;;
	--verbose)	VERBOSE=--verbose;;
	install|\
	list|\
	why)		ACTION=$arg;;
	update|\
	remove|\
	require)	ACTION=$arg; BACKUP=1;;
	dryrun|\
	dry-run)	DRYRUN=--dry-run; BACKUP=0;;
	*)			ARGUMENTS+=$arg;;
	esac
done

if [ -z "$ACTION" ]; then
	echo "you need to specify an action: install or update" >&1
	exit 1
fi

mkdir -p vendor

for name in composer.json composer.lock vendor; do
	if [ -f $name ]; then
		chown -R party $name
	fi
done

if [ -n "$BACKUP" ] && [ -z "$DRYRUN"  ]; then
	DATE=$(date +%Y%m%d.%H%M%S)
	cp composer.lock composer.lock_$DATE
fi

su party -c "composer $VERBOSE $QUIET $ACTION $DRYRUN $ARGUMENTS"

if [ "$ACTION" = "update" ]; then
	echo "WARNING: Update the generated composer.lock so facebook/graph-sdk also allows current PHP version"
fi
