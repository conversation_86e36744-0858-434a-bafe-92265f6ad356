
# Uncomment the next line to enable packet forwarding for IPv4
net.ipv4.ip_forward=1

# Uncomment the next line to enable packet forwarding for IPv6
#  Enabling this option disables Stateless Address Autoconfiguration
#  based on Router Advertisements for this host
net.ipv6.conf.all.forwarding=1
net.ipv6.conf.all.proxy_ndp=1
net.ipv6.conf.default.forwarding=1
net.ipv6.conf.default.proxy_ndp=1
net.ipv6.conf.eth0.forwarding=1
net.ipv6.conf.eth0.proxy_ndp=1
net.ipv6.conf.eth1.forwarding=1
net.ipv6.conf.eth1.proxy_ndp=1

net.nf_conntrack_max=1048576
#net.ipv4.vs.expire_quiescent_template=1

net.ipv6.route.max_size = 1048576
