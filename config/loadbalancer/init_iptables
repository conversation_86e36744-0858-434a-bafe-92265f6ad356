#!/bin/sh

set -e

PATH=/usr/local/sbin:/usr/local/bin:/sbin:/bin:/usr/sbin:/usr/bin

default=/etc/default/iptables
if test -f $default; then
  . $default
fi
  
initd="$0"

initd_abort () {
  cmd=$1
  shift
  echo "Aborting iptables $cmd: $@."
  exit 0
}

initd_have_a_cow_man () {
  for i in $@; do
    if ! command -v "$i" >/dev/null 2>&1; then
      initd_abort initd "no $i executable"
      exit 0
    fi
  done
}

initd_clear () {
  rm -f "$autosave"
  echo -n "Clearing ${iptables_command} ruleset: default ACCEPT policy"
  $iptables_save | sed "/-/d;/^#/d;s/DROP/ACCEPT/" | $iptables_restore
  echo "."
}

initd_halt () {
  rm -f $autosave
  echo -n "Clearing ${iptables_command} ruleset: default DROP policy"
  $iptables_save | sed "/-/d;/^#/d;s/ACCEPT/DROP/" | $iptables_restore
  echo "."
}

initd_load () {
  ruleset="$libdir/$@"
  if ! test -f "$ruleset"; then
    initd_abort load "unknown ruleset, \"$@\""
    if  ! test "${ruleset#${libdir}/}" = active -o inactive; then
        usage
    fi
    exit 0
  fi
  if test "${ruleset#${libdir}/}" = inactive; then
    initd_autosave
  fi
  rm -f "$autosave"
  echo -n "Loading ${iptables_command} ruleset: load \"$@\""
  $iptables_restore < "$ruleset"
  echo "."
}

initd_counters () {
  if test "${enable_save_counters:-false}" = true; then
    echo -n " with counters"
    $iptables_save -c > "$ruleset"
  else
    $iptables_save | sed '/^:/s@\[[0-9]\{1,\}:[0-9]\{1,\}\]@[0:0]@g' > "$ruleset"
  fi
}

initd_save () {
  rm -f $autosave
  ruleset="${libdir}/$@"
  echo -n "Saving ${iptables_command} ruleset: save \"$@\""
   initd_counters
  echo "."
}

initd_autosave () {
  if test -f $autosave -a ${enable_autosave-false} = true; then
    ruleset="${libdir}/active"
    echo -n "Autosaving ${iptables_command} ruleset: save \"active\""
    initd_counters
    echo "."
  fi
}

usage () {
#  current="$(ls -m ${libdir} \
#    | sed 's/ \{0,1\}autosave,\{0,1\} \{0,1\}//')"
cat << END
$initd options:
  start|restart|reload|force-reload
     load the "active" ruleset
  save <ruleset>
     save the current ruleset
  load <ruleset>
     load a ruleset
  stop
     load the "inactive" ruleset
  clear
     remove all rules and user-defined chains, set default policy to ACCEPT
  halt
     remove all rules and user-defined chains, set default policy to DROP

Saved ruleset locations: /var/lib/iptables/ and /var/lib/ip6tables/

Please read: $default

END
}

initd_main () {
  case "$1" in
    start|restart|reload|force-reload)
      initd_load active
      if test ${enable_autosave-false} = true; then
        touch $autosave
      fi
      ;;
    stop)
      initd_load inactive
      ;;
    clear)
      initd_clear
      ;;
    halt)
      initd_halt
      ;;
    save)
      shift
      if test -z "$*"; then
        initd_abort save "no ruleset name given"
      else
        initd_save "$*"
      fi
      ;;
    load)
      shift
      if test -z "$*"; then
        initd_abort load "no ruleset name given"
      else
        initd_load "$*"
      fi
      ;;
    save_active) #legacy option
      initd_save active
      ;;
    save_inactive) #legacy option
      initd_save inactive
      ;;
    *)
      if test "$iptables_command" = "iptables"; then
        if test -n "$*"; then
          echo "$initd: unknown command: \"$*\""
        fi
        usage
      fi
      ;;
  esac
}

initd_preload() {
  iptables="/sbin/${iptables_command}"
  iptables_save="${iptables}-save"
  iptables_restore="${iptables}-restore"
  libdir="/var/lib/${iptables_command}"
  autosave="${libdir}/autosave"
  initd_have_a_cow_man "$iptables_save" "$iptables_restore"
  ${iptables_command} -nL >/dev/null
  initd_main $*
}

iptables_command=iptables initd_preload $*
if test "$enable_ipv6" = "true"; then   
  iptables_command=ip6tables initd_preload $*
fi

exit 0
