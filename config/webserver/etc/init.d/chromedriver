#!/bin/bash -e
### BEGIN INIT INFO
# Provides:          chromedriver2
# Required-Start:    $local_fs $remote_fs $network $syslog
# Required-Stop:     $local_fs $remote_fs $network $syslog
# Default-Start:     2 3 4 5
# Default-Stop:      0 1 6
# Short-Description: Start/stop chromedriver
### END INIT INFO


ENV="env -i LANG=C PATH=/usr/local/bin:/usr/bin:/bin"

USER=party
NAME=chromedriver
DAEMON=/usr/local/bin/chromedriver
PARAMS="--headless --readable-timestamp --enable-chrome-logs --append-log --log-level=DEBUG --log-path=/var/log/chromedriver.log --disk-cache-dir=/tmpdisk --port=9515"
# PARAMS="--headless --disable-gpu --readable-timestamp --enable-chrome-logs --append-log --log-level=DEBUG --log-path=/var/log/chromedriver.log --disk-cache-dir=/tmpdisk --port=9515"

. /lib/lsb/init-functions

test -f /etc/default/rcS && . /etc/default/rcS

PIDFILE=/var/run/chromedriver.pid

pidof_chromedriver() {
    # if pidof is null for some reasons the script exits automagically
    # classified as good/unknown feature
    PIDS=$(pidof chromedriver2) || true

    [ -e $PIDFILE ] && PIDS2=$(cat $PIDFILE)
    
    # if there is a pid we need to verify that belongs to chromedriver2
    # for real
    for i in $PIDS; do
    	if [ "$i" = "$PIDS2" ]; then
            # in this case the pid stored in the
            # pidfile matches one of the pidof chromedriver
            # so a simple kill will make it
            echo $i
            return 0
        fi
    done
    return 1
}

chromedriver_stop() {
	PID=$(pidof_chromedriver)

	if [ "${PID}" ]; then
                kill $PID
	fi
}

chromedriver_wait_stop() {
	# running ?
	PIDTMP=$(pidof_chromedriver)
	if kill -0 "${PIDTMP:-}" 2> /dev/null; then
	    PID=$PIDTMP
	fi

	chromedriver_stop

	# wait until really stopped
	if [ -n "${PID:-}" ]; then
		i=0
		while kill -0 "${PID:-}" 2> /dev/null;  do
        		if [ $i = '60' ]; then
        			break;
        	 	else
        			if [ $i = '0' ]; then
                			echo -n " ... waiting "
        			else
                	      		echo -n "."
        		 	fi
        			i=$(($i+1))
        			sleep 1
        	      fi
		 done
	fi
}

wait_for_deaddaemon () {
	pid=$1
	sleep 1
	if test -n "$pid"
	then
		if kill -0 $pid 2>/dev/null
		then
			cnt=0
			while kill -0 $pid 2>/dev/null
			do
				cnt=`expr $cnt + 1`
				if [ $cnt -gt $WAITFORDAEMON ]
				then
					log_action_end_msg 1 "still running"
					exit 1
				fi
				sleep 1
				[ "`expr $cnt % 3`" != 2 ] || log_action_cont_msg ""
			done
		fi
	fi
	log_action_end_msg 0
}


case $1 in
	start)
		log_daemon_msg "Starting" $NAME
		if start-stop-daemon --stop --signal 0 --quiet --pidfile $PIDFILE --exec $DAEMON; then
			log_action_end_msg 0 "already running"
                else
			start-stop-daemon --background --start --chuid $USER --quiet --pidfile $PIDFILE --make-pidfile $PIDFILE --exec $DAEMON -- $PARAMS
			log_action_end_msg $?
			exit $?
                fi
	;;
	stop)
		log_daemon_msg "Stopping" $NAME
		pid=`cat $PIDFILE 2>/dev/null` || true
		if test ! -f $PIDFILE -o -z "$pid"; then
			log_action_end_msg 0 "not running - there is no $PIDFILE"
			exit 0
		fi
		if start-stop-daemon --stop --signal INT --quiet --pidfile $PIDFILE --exec $DAEMON; then
			wait_for_deaddaemon $pid
		elif kill -0 $pid 2>/dev/null; then
			log_action_end_msg 1 "Is $pid not $NAME?  Is $DAEMON a different binary now?"
		else
			log_action_end_msg 1 "$DAEMON died: process $pid not running; or permission denied"
		fi
	;;
	restart)
		$0 stop
		$0 start
	;;
	status)
		if test ! -r $(dirname $PIDFILE); then
			log_failure_msg "cannot read PID file $PIDFILE"
			exit 4
		fi
		pid=`cat $PIDFILE 2>/dev/null` || true
		if test ! -f $PIDFILE -o -z "$pid"; then
			log_failure_msg "$NAME is not running"
			exit 3
		fi
		if start-stop-daemon --pid "$pid" -T ; then
			log_success_msg "$NAME is running"
			exit 0
		else
			log_failure_msg "$NAME is not running"
			exit 1
		fi
	;;
	*)
		log_success_msg "Usage: /etc/init.d/chromedriver {start|stop|restart|status}"
		exit 1
	;;
esac
