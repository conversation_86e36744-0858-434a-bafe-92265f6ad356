#!/usr/bin/php
<?php

require_once '/home/<USER>/public_html/_db.inc';

$rc = check_webs();

exit($rc ? 0 : 1);

function check_webs(): bool {
	$force_appic = false;
	foreach ($GLOBALS['argv'] as $i => $arg) {
		if (!$i) {
			continue;
		}
		switch ($arg) {
		case 'force_appic':
			$force_appic = true;
			break;
		}
	}

	if (str_starts_with(gethostname(), 'web')) {
		if (!apache_running()) {
			error_log('check_webs: apache is not running, web checks not possible.');
			exit(1);
		}
	} else {
		error_log('check_webs: apache is not running, web checks not possible.');
		exit(1);
	}
	return $force_appic ? true : check_appic_v2();
}

function apache_running(): bool {
	return `pgrep --full -- apache2`;
}

function check_appic(): bool {
	if (!($data = file_get_contents('http://localhost/appic/v2/event/359456.json'))) {
		error_log('appic_test: no data');
		return false;
	}

	if (!($obj = safe_json_decode($data, true))) {
		error_log('appic_test: no json');
		return false;
	}
	if (!($stored = file_get_contents('/home/<USER>/config/webserver/tests/appic_event_359456.json'))) {
		error_log('appic_test: missing stored data');
		return false;
	}
	if (!($sobj = safe_json_decode($stored, true))) {
		error_log('appic_test: no stored json');
		return false;
	}
	unset(	$sobj['generated'],		$obj['generated'],
			$sobj['flyer_update'],	$obj['flyer_update']
	);

	file_put_contents('obj_stored',$stored);
	file_put_contents('obj_retrieved',$data);
	if ($sobj !== $obj) {
		file_put_contents('obj_stored',$stored);
		file_put_contents('obj_retrieved',$data);
		error_log('appic_test: stored json does not equate to current');
		return false;
	}
	return true;
}

function check_appic_v2(): bool {
	$max = 10;

	$partyids = db_simple_hash('party','
		SELECT PARTYID,NAME
		FROM party
		WHERE STAMP>UNIX_TIMESTAMP()
		  AND NOT ISNULL((SELECT 1 FROM lineup WHERE lineup.PARTYID=party.PARTYID LIMIT 1))
		  AND NOT ISNULL((SELECT 1 FROM connect WHERE MAINTYPE="party" AND MAINID=PARTYID AND ASSOCTYPE="organization" LIMIT 1))
		  AND ACCEPTED
		ORDER BY RAND()
		LIMIT '.$max
	);
	if ($partyids === false) {
		error_log('appic_test: no future parties');
		return false;
	}
	if (count($partyids) < $max) {
		error_log('appic_test: not enough futurep arties');
		return false;
	}

	$get_context = stream_context_create([
		'ssl'	=> [
			'verify_peer'		=> false,
			'verify_peer_name'	=> false,
		],
	]);
	
	foreach ($partyids as $id => $name) {
		error_log('checking party '.$id.': '.$name);
	
		$old_data = file_get_contents($old_uri = 'https://partyflock.nl/appic/v2/event/'.$id.'.json');
		$new_data = file_get_contents($new_uri = 'https://localhost/appic/v2/event/'.$id.'.json', context: $get_context);
		# $old_data = file_get_contents($old_uri = 'http://partyflock.nl/appic/v2/event/'.$id.'.json');
		# $new_data = file_get_contents($new_uri = 'http://localhost/appic/v2/event/'.$id.'.json');

		$new_data = str_replace('https:\\/\\/localhost\\/', 'https:\\/\\/partyflock.nl\\/', $new_data);

		if (!$new_data) {
			error_log('appic_test: no new data @ '.$new_uri);
			return false;
		}
		if (!$old_data) {
			error_log('appic_test: no old data @ '.$old_uri);
			return false;
		}

		$old_obj = safe_json_decode($old_data, true);
		if (!$old_obj) {
			error_log('appic_test: no old json for data '.$old_data);
			return false;
		}
		$new_obj = safe_json_decode($new_data, true);
		if (!$new_obj) {
			error_log('appic_test: no new json for data '.$new_data);
			return false;
		}

		unset(	$old_obj['generated'],		$new_obj['generated'],
			$old_obj['flyer_update'],	$new_obj['flyer_update']
		);
		if ($old_obj !== $new_obj) {
			error_log('appic_test: stored json does not equate to current for event '.$id.': '.$name);
			
			# var_dump display ... for deep arrays

			ob_start();
			print_r($new_obj);
			$new_print = ob_get_clean();
			
			ob_start();
			print_r($old_obj);
			$old_print = ob_get_clean();
			
			file_put_contents('/tmpdisk/obj_old',$old_print);
			file_put_contents('/tmpdisk/obj_new',$new_print);
		
			error_log('appic_test: check diff using: icdiff /tmpdisk/obj_old /tmpdisk/obj_new | less');
			
			return false;
		}
	}
	return true;
}
