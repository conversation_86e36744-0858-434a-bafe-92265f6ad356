#!/bin/bash

set -e
# set -x -v

cd -- "$(dirname -- "$(realpath "${BASH_SOURCE[0]}")")"

. ../generic/install.include.sh

function install_chrome_driver {
	if [ "$(uname -m)" != "x86_64" ]; then
		echo "PLEASE INSTALL CHROMEDRIVER ON AN x86_64 OTHER MACHINE!"
		echo "Find chrome drivers here: https://googlechromelabs.github.io/chrome-for-testing/"
		exit 1
	fi
	$APT_NONINTERACTIVE install google-chrome-stable wget unzip
	cd /tmp
	wget https://storage.googleapis.com/chrome-for-testing-public/136.0.7103.49/linux64/chromedriver-linux64.zip
	unzip chromedriver-linux64.zip
	mv chromedriver-linux64/chromedriver /usr/local/bin
	rm -rf chromedriver-linux64*

	link_config etc/init.d/chromedriver

	/etc/init.d/chromedriver stop

	update-rc.d chromedriver defaults

	touch /var/log/chromedriver.log
	chown party /var/log/chromedriver.log

	/etc/init.d/chromedriver start
}
