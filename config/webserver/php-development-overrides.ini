# NOTE: Turn this on when enough time is available to fix deprecations
error_reporting = E_ALL
display_error = yes
display_startup_errors = yes
report_memleaks = yes

zend.assertions = 1

xdebug.mode = debug,develop,gcstats,profile
xdebug.show_error_trace = yes
xdebug.show_exception_trace = yes
xdebug.show_local_vars = yes

xdebug.trigger_value = liefheersbeesje
xdebug.client_host = 127.0.0.1
xdebug.client_port = 9003
xdebug.discover_client_host = no
xdebug.start_upon_error = yes
xdebug.start_with_request = no

# log_level  7 = informational
# log_level 10 = debug
xdebug.log_level = 10
xdebug.log = /var/log/php/xdebug.log

# NOTE: JIT does not work with xdebug
# opcache.jit = tracing
# opcache.jit_buffer_size = 128M
