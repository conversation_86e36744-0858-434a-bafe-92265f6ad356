#!/bin/sh

set -e	# exit immediately after error
#set -x	# print input
#set -v	# print shell commands

# give logrotate time to restart chromedriver at 00:00,
GIVE_TIME=230

if [ -f /tmp/rotating_chromedriver ] \
|| [ "$(date +%H%M%S)" -lt $GIVE_TIME ]; then
	exit;
fi

# need /usr/sbin for start-stop-daemon:
PATH=$PATH:/usr/sbin

if [ -z "$(pgrep chromedriver)" ]; then
	echo "chromedriver is not running, starting it" >&2
	/etc/init.d/chromedriver start
fi
