expose_php = Off
# error_reporting = E_ALL & ~E_NOTICE & ~E_DEPRECATED
error_reporting = E_ALL
default_charset = "UTF-8"
variables_order = "EGPCS"
memory_limit = 512M
max_input_time = 600
short_open_tag = On
# 10 MB output buffering:
output_buffering = 10485760
max_execution_time = 120
max_input_vars = 5000
ignore_repeated_errors = On
display_errors = Off
track_errors = Off
html_errors = Off
post_max_size = 2G
include_path = "/home/<USER>/public_html/:/home/<USER>/config/phpclasses:/usr/share/php"
error_log = /var/log/php/error.log
arg_separator.input = ";&"
upload_tmp_dir = /tmpdisk
upload_max_filesize = 2G
max_file_uploads = 500
date.timezone = "Europe/Amsterdam"
session.serialize_handler = igbinary
# 100 KiB error log lines:
log_errors_max_len = 102400

zend.assertions = -1

apc.enabled = 1
apc.shm_size = 4M
;apc.include_once_override = 1
;apc.report_autofilter = 1
;apc.writable = /tmpdisk
apc.serializer = igbinary

xdebug.var_display_max_depth = -1
xdebug.var_display_max_children = -1
#xdebug.var_display_max_data = 1024
xdebug.var_display_max_data = 10240
# show_local_vars only shows the variables of the last user-defined function, which probably is mail_log
xdebug.show_local_vars = 0

# xdebug.mode = develop,profile
xdebug.mode = develop
xdebug.output_dir = /tmpdisk
xdebug.start_with_request = trigger
xdebug.trigger_value = liefheersbeesje

# Probably dangerous, but let's try
imagick.set_single_thread = 0

# See Opcache stats to find out about usage
opcache.memory_consumption = 50
opcache.enable = 1
opcache.enable_cli = 1
opcache.file_cache = /tmp/php-opcode-cache/
opcache.file_cache_only = 0
opcache.file_cache_consistency_checks = 1
opcache.validate_timestamps = 1
opcache.revalidate_freq = 1
opcache.log_verbosity_level = 2
# JIT is incompatible with third party extensions that override zend_execute_ex e.g. Xdebug
# opcache.jit = tracing
opcache.jit_buffer_size = 0
