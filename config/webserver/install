#!/bin/bash

set -e	# exit immediately after error
set -x	# show commands
set -v	#

BASH_SOURCE_SELF=${BASH_SOURCE[0]}
BASH_SOURCE_DIR="${BASH_SOURCE_SELF%/*}"
SCRIPT_DIR=$(realpath "$BASH_SOURCE_DIR")
cd "$SCRIPT_DIR"
source ../generic/install.include.sh
source ./install_chrome_driver.sh

function install_apache_configs {
	local conf mod modules SITE_CONFIG
	# shellcheck disable=SC2043
	for conf in mime; do
		link_config /home/<USER>/config/webserver/mods-available/$conf.conf /etc/apache2/mods-available/$conf.conf
	done
	rm -f /etc/apache2/conf-enabled/*
	for conf in \
		access \
		compression \
		main \
		ssl \
		status
	do
		link_config /home/<USER>/config/webserver/conf.d/$conf /etc/apache2/conf-enabled/$conf
	done
	# shellcheck disable=SC2043
	for mod in alias; do
		rm -f /etc/apache2/mods-enabled/mod.conf
	done
	for mod in \
		auth_basic \
		authz_groupfile \
		autoindex \
		mpm_prefork
	do
		a2dismod -f $mod
	done
	for mod in \
		ext_filter \
		mpm_event \
		headers \
		macro \
		rewrite \
		ssl \
		http2 \
		brotli \
		xsendfile \
		authz_core \
		authz_host \
		authn_file \
		authz_user \
		status \
		info \
		proxy \
		proxy_fcgi
	do
		a2enmod $mod
	done
	if [[ "$HOSTNAME" == sandbox* ]]; then
		modules=(
			auth_basic	# used to limit access to sandbox webserver
		)
		a2enmod "${modules[@]}"
	fi
	if [ "$SITE_CONFIG" = "webstore" ]; then
		SITE_CONFIG=production
	elif [[ "$SITE_CONFIG" == sandbox* ]]; then
		SITE_CONFIG=development
	else
		SITE_CONFIG=$1
	fi
	rm -f	/etc/apache2/sites-enabled/000-default.conf \
			/etc/apache2/sites-enabled/000-default \
			"/etc/apache2/sites-enabled/partyflock_$SITE_CONFIG"

	link_config "/home/<USER>/config/webserver/sites-available/partyflock_$SITE_CONFIG" "/etc/apache2/sites-enabled/partyflock_$SITE_CONFIG"

	if [ -f /etc/apache2/mods-avaiable/php$INSTALL_PHP_VERSION.conf ]; then
		a2dismod php$INSTALL_PHP_VERSION
	fi
	link_config /home/<USER>/config/webserver/apache2.conf /etc/apache2/apache2.conf
	link_config /home/<USER>/config/webserver/etc/init.d/apache2 /etc/init.d/apache2
}

function install_cachefilesd() {
	# shellcheck disable=SC2119
	dont_start_services
	$APT_NONINTERACTIVE install cachefilesd
	echo cachefiles >> /etc/modules
	link_config etc/cachefilesd.conf
	link_config etc/default/cachefilesd
	# shellcheck disable=SC2119
	do_start_services
	systemctl enable --now cachefilesd
	systemctl restart cachefilesd
}

function install_exports() {
	local EXTENSION
	if [ "$HOSTNAME" = webstore ]; then
		EXTENSION=webstore
	else
		EXTENSION=web
	fi
	link_config "/home/<USER>/config/webserver/etc/exports-$EXTENSION" /etc/exports
}

function install_nfs_and_cache {
	# NOTE: currently cache resides on local storage of webstore, as gluster seemed to slow when used last
	cat /home/<USER>/config/webserver/etc/fstab-glusters >> /etc/fstab
	if [[ "$HOSTNAME" != "webstore" ]]; then
		mkdir -p /mnt/webstore/pfcachev2
		mkdir -p /mnt/webstore/imgcache
		mkdir -p /mnt/largepart/cache
		$APT_NONINTERACTIVE install nfs-common
		cat /home/<USER>/config/webserver/etc/fstab-webstore-mount >> /etc/fstab
		install_cachefilesd
		link_config ./etc/exports-web /etc/exports
	else
		# we're on webstore
		mkdir -p /mnt/webstore
		mkdir -p /mnt/largepart/imgcache
		$APT_NONINTERACTIVE install nfs-kernel-server
		chown www-data:www-data /mnt/largepart/imgcache
		chown www-data:www-data /mnt/largepart/pfcachev2
		link_config ./etc/exports-webstore; /etc/exports
	fi
	exportfs -avr
	/etc/init.d/nfs-kernel-server reload
}

function install_php_configs {
	rm -f	/etc/php/$INSTALL_PHP_VERSION/fpm/conf.d/*-php-*overrides.ini \
			/etc/php/$INSTALL_PHP_VERSION/mods-available/*php-*overrides.ini

	if [ -d /etc/php/$INSTALL_PHP_VERSION/fpm/conf.d ]; then
		link_config /home/<USER>/config/webserver/php-overrides.ini /etc/php/$INSTALL_PHP_VERSION/fpm/conf.d/90-php-overrides.ini
	fi

	case "$HOSTNAME" in
	webx | \
	sandbox*) \
		local sapi
		for sapi in cli fpm; do
			link_config /home/<USER>/config/webserver/php-development-overrides.ini /etc/php/$INSTALL_PHP_VERSION/$sapi/conf.d/99-php-development-overrides.ini
		done;;
	esac

	install_php_fpm_config

	mkdir -p /var/log/php
	chown www-data.www-data /var/log/php

	phpenmod php-overrides
	phpenmod php-cli-overrides
	# for update_composer
	phpenmod phar
	# if memcached and apcu are not working:
	phpenmod sysvshm
	# for phpstan
	phpenmod tokenizer

	/home/<USER>/config/generic/install rm-unused-php

	/etc/init.d/php$INSTALL_PHP_VERSION-fpm reload
}

function install_exiftool {
	. ./install_exiftool.sh
	perl -MCPAN -e 'install Image::ExifTool'
}

function install_packages {
	# Required by CPAN:
	# - liblzma-dev

	$APT_NONINTERACTIVE  install \
		advancecomp \
		apache2 \
		cracklib-runtime \
		composer \
		exif \
		exiv2 \
		ffmpeg \
		gifsicle \
		giftrans \
		glusterfs-client \
		gnutls-bin \
		jpeginfo \
		lame \
		libavif-bin \
		libapache2-mod-xsendfile \
		libjpeg62-turbo \
		libjpeg-turbo-progs \
		liblzma-dev \
		libmp3lame0 \
		libtiff-tools \
		libzopfli1 \
		mp3info \
		optipng \
		php$INSTALL_PHP_VERSION-fpm \
		pngcrush \
		pngtools \
		poppler-utils \
		tesseract-ocr \
		unzip \
		wdiff \
		wget \
		whois \
		zip \
		zopfli

	install_gif2apng
	install_exiftool
}

install_php_fpm_config() {
	mkdir -p /etc/php/$INSTALL_PHP_VERSION/fpm/pool.d
	local conf
	for conf in etc/php/$INSTALL_PHP_VERSION/fpm/pool.d/*.conf; do
		link_config $conf
	done
}

function install_gif2apng {
	local PLATFORM
	if [ "$(uname -m)" = "x86_64" ]; then
		PLATFORM=amd64
	else
		PLATFORM=arm64
	fi
	curl http://ftp.nl.debian.org/debian/pool/main/g/gif2apng/gif2apng_1.9+srconly-3+deb11u1_$PLATFORM.deb > gif2apng.deb
	dpkg -i ./gif2apng.deb
	rm gif2apng.deb
}

function install_smartcrop {
	$APT_NONINTERACTIVE install \
		libc6-dev

	rm -rf	/usr/lib/node_modules \
			/usr/local/lib/node_modules

	rm -f	/usr/bin/smartcrop \
			/usr/local/bin/smartcrop

	source ../generic/install_npm_and_node.sh
	install_npm_and_node
	npm install node-opencv

	npm install -g smartcrop
	npm install -g smartcrop-cli
}
function install_icc_profiles {
	$APT_NONINTERACTIVE install \
		icc-profiles \
		icc-profiles-free

	mkdir -p /usr/share/color/icc

	local icc_profile
	for icc_profile in usr/share/color/icc/*; do
		link_config "$icc_profile"
	done
}

function restart_apache {
	/etc/init.d/apache2 restart
}

function reload_php_fpm {
	/etc/init.d/php$INSTALL_PHP_VERSION-fpm reload
}

function install_gluster_mounts {
	cat /home/<USER>/config/webserver/etc/fstab-glusters >> /etc/fstab
	local dir
	for dir in dumps home vt includecache photos_v2 music; do
 		if [ ! -f "/mnt/gluster/$dir" ]; then
			mkdir -p "/mnt/gluster/$dir"
		fi
	done
}

function install_meminfo {
	set -x -v -e
	local TMP_DIR
	TMP_DIR=$(mktemp -d)
	cd "$TMP_DIR"
	git clone https://github.com/BitOne/php-meminfo.git
	cd php-meminfo/extension
	phpize
	./configure --enable-meminfo
	make
	make install
	rm -rf "$TMP_DIR"
}

case "$1" in
development) \
	install_packages
	install_smartcrop
	install_imagemagick_7
	install_icc_profiles
	install_apache_configs "$1"
	install_php_config
	install_chrome_driver
	restart_apache
	reload_php_fpm
	/home/<USER>/config/webserver/test_443 localhost
	;;
vip | \
webstore | \
production | \
sandbox)
	install_gluster_mounts
	install_packages
	install_smartcrop
	install_imagemagick_7
	install_icc_profiles
	install_apache_configs "$1"
	install_php_config
	install_nfs_and_cache
	install_chrome_driver
	restart_apache
	reload_php_fpm
	/home/<USER>/config/webserver/test_443 localhost
	;;

cachefilesd)	install_cachefilesd;;
chrome-driver)	install_chrome_driver;;
gif2apng)		install_gif2apng;;
icc-profiles)	install_icc_profiles;;
imagemagick-7)	install_imagemagick_7;;
meminfo)		install_meminfo;;
nfs-and-cache)	install_nfs_and_cache;;
packages)		install_packages;;
php-config)		install_php_config;;
php-fpm-config)	install_php_fpm_config;;
smartcrop)		install_smartcrop;;
*)				echo "$0: (sandbox | vip | webstore | production)"; exit 1;;
esac

echo "done"
