SERVER NAME=dba;			HOST=alphaparty;						DBNAME=party_db;	STATUS=online
SERVER NAME=dbu;			HOST=ultraparty;	CONNECTION=dbm;		DBNAME=party_db;	STATUS=online
SERVER NAME=dbu_dbc;		HOST=ultraparty;	CONNECTION=dbc;		DBNAME=counter_db;	STATUS=online
SERVER NAME=dbu_ndbd;		HOST=ultraparty;	CONNECTION=ndbd;	DBNAME=data_db;		STATUS=online
SERVER NAME=dbu_albums;		HOST=ultraparty;	CONNECTION=ndbd;	DBNAME=albums;		STATUS=online
SERVER NAME=dbo;			HOST=omegaparty;						DBNAME=party_db;	STATUS=online
SERVER NAME=dbyin;			HOST=yin;			CONNECTION=dbm;		DBNAME=party_db;	STATUS=online
SERVER NAME=dbyin_dbc;		HOST=yin;			CONNECTION=dbc;		DBNAME=counter_db;	STATUS=online
SERVER NAME=dbyin_ndbd;		HOST=yin;			CONNECTION=ndbd;	DBNAME=data_db;		STATUS=online
SERVER NAME=dbyang;			HOST=yang;			CONNECTION=dbm;		DBNAME=party_db;	STATUS=online
SERVER NAME=dbyang_dbc;		HOST=yang;			CONNECTION=dbc;		DBNAME=counter_db;	STATUS=online
SERVER NAME=dbyang_ndbd;	HOST=yang;			CONNECTION=ndbd;	DBNAME=data_db;		STATUS=online
SERVER NAME=dbspecial_dbm;	HOST=special2;		CONNECTION=dbm;		DBNAME=party_db;	STATUS=online
SERVER NAME=dbspecial_dbc;	HOST=special2;		CONNECTION=dbc;		DBNAME=counter_db;	STATUS=online

SERVER NAME=dbsrch;			HOST=searchparty;	CONNECTION=dbm;		DBNAME=party_db;	STATUS=online
SERVER NAME=dbsrch_dbc;		HOST=searchparty;	CONNECTION=dbc;		DBNAME=party_db;	STATUS=online
SERVER NAME=dbsrchrep;		HOST=searchpartyrep;CONNECTION=dbm;		DBNAME=party_db;	STATUS=online
SERVER NAME=dbsrchrep_dbc;	HOST=searchpartyrep;CONNECTION=dbc;		DBNAME=party_db;	STATUS=online

SERVER NAME=dbblack;		HOST=black;								DBNAME=counter_db;	STATUS=online
SERVER NAME=dbwhite;		HOST=white;								DBNAME=counter_db;	STATUS=online

SERVER NAME=ndbd2;			HOST=newdata2;		PORT=3333;			DBNAME=data_db;		STATUS=online
SERVER NAME=ndbd2_albums;	HOST=newdata2;		PORT=3333;			DBNAME=albums;		STATUS=online
SERVER NAME=ndbd3;			HOST=newdata3;		PORT=3333;			DBNAME=data_db;		STATUS=online
SERVER NAME=ndbd3_albums;	HOST=newdata3;		PORT=3333;			DBNAME=albums;		STATUS=online

SERVER NAME=dbbm;			HOST=backupparty;	CONNECTION=dbm;		DBNAME=party_db;	STATUS=online
SERVER NAME=dbbc;			HOST=backupparty;	CONNECTION=dbc;		DBNAME=counter_db;	STATUS=online
SERVER NAME=ndbbd;			HOST=backupparty;	CONNECTION=ndbd;	DBNAME=albums;		STATUS=online
SERVER NAME=dbbds1;			HOST=backupparty;	CONNECTION=ndbd;	DBNAME=data_db;		STATUS=online

# sphinx servers:
SERVER NAME=dbsphinx;		HOST=searchparty;	PORT=9306;	STATUS=online
SERVER NAME=dbsphinxrep;	HOST=searchpartyrep;PORT=9306;	STATUS=online

# NOTE: These PRIMARY rows are currently not processed, but could, in the
# future be used to pick a server if no TABLE specification was found.

PRIMARY: DATABASE = party_db;	CONNECTION = dbm
PRIMARY: DATABASE = counter_db;	CONNECTION = dbc
PRIMARY: DATABASE = data_db;	CONNECTION = ndbd
PRIMARY: DATABASE = albums;		CONNECTION = ndbd

# If no TABLE configuration for a table exists, the following spec is used:

TABLE MASTER=dbu;	SLAVES=dbu,dba,dbo,dbyin,dbyang

# Using table 'sphinx' in db_* functions to force fetching from either
# searchparty server:

TABLE NAME=sphinx;	MASTER=dbsphinx;	SLAVES=dbsphinx,dbsphinxrep

# FIXME: Sort the lines per group, so they are ordered on table name

## party_db @ alphaparty:

TABLE NAME=ad;MASTER=dbu;SLAVES=dba
TABLE NAME=contest;MASTER=dbu;SLAVES=dba
TABLE NAME=contestresponse;MASTER=dbu;SLAVES=dba
TABLE NAME=contestwinmessage;MASTER=dbu;SLAVES=dba
TABLE NAME=notifyinfo;MASTER=dbu;SLAVES=dba
TABLE NAME=promo;MASTER=dbu;SLAVES=dba
TABLE NAME=promo_letter;MASTER=dbu;SLAVES=dba
TABLE NAME=promo_letter_v2;MASTER=dbu;SLAVES=dba
TABLE NAME=promo_letter_concept;MASTER=dbu;SLAVES=dba
TABLE NAME=settings;MASTER=dbu;SLAVES=dba

## party_db @ ultraparty

TABLE NAME=camera;MASTER=dbu;SLAVES=dbu
TABLE NAME=camerarequest;MASTER=dbu;SLAVES=dbu
TABLE NAME=ignorelist;MASTER=dbu;SLAVES=dbu
TABLE NAME=offense;MASTER=dbu;SLAVES=dbu
TABLE NAME=report;MASTER=dbu;SLAVES=dbu
TABLE NAME=rights_forum;MASTER=dbu;SLAVES=dbu
TABLE NAME=validforeigner;MASTER=dbu;SLAVES=dbu

## party_db @ yin

TABLE NAME=buddy;MASTER=dbu;SLAVES=dbyin
TABLE NAME=favourite;MASTER=dbu;SLAVES=dbyin
TABLE NAME=flockinvitation;MASTER=dbu;SLAVES=dbyin
TABLE NAME=flockmessage;MASTER=dbu;SLAVES=dbyin
TABLE NAME=flockrequest;MASTER=dbu;SLAVES=dbyin
TABLE NAME=flocktopic;MASTER=dbu;SLAVES=dbyin
TABLE NAME=profile_for_ip;MASTER=dbu;SLAVES=dbyin
TABLE NAME=profile_for_identity;MASTER=dbu;SLAVES=dbyin
TABLE NAME=profile_for_user;MASTER=dbu;SLAVES=dbyin
TABLE NAME=going;MASTER=dbu;SLAVES=dbyin
TABLE NAME=votenew;MASTER=dbu;SLAVES=dbyin
TABLE NAME=albumelement;MASTER=dbu;SLAVES=dbyin
TABLE NAME=albumelementreference;MASTER=dbu;SLAVES=dbyin
TABLE NAME=albumelement_tobedeleted;MASTER=dbu;SLAVES=dbyin
TABLE NAME=albumimagedatameta;MASTER=dbu;SLAVES=dbyin
TABLE NAME=albummap;MASTER=dbu;SLAVES=dbyin
TABLE NAME=chatinfo;MASTER=dbu;SLAVES=dbyin
TABLE NAME=comment;MASTER=dbu;SLAVES=dbyin
TABLE NAME=contact_ticket_attachment;MASTER=dbu;SLAVES=dbyin
TABLE NAME=contact_ticket_cache;MASTER=dbu;SLAVES=dbyin
TABLE NAME=contact_ticket_mail;MASTER=dbu;SLAVES=dbyin
TABLE NAME=contact_ticket;MASTER=dbu;SLAVES=dbyin
TABLE NAME=contact_ticket_message;MASTER=dbu;SLAVES=dbyin
TABLE NAME=conversation_V2;MASTER=dbu;SLAVES=dbyin
TABLE NAME=disallowuser;MASTER=dbu;SLAVES=dbyin
TABLE NAME=failed_login;MASTER=dbu;SLAVES=dbyin
TABLE NAME=quoted;MASTER=dbu;SLAVES=dbyin
TABLE NAME=rating;MASTER=dbu;SLAVES=dbyin
TABLE NAME=spam_message_crc;MASTER=dbu;SLAVES=dbyin
TABLE NAME=streamaddress;MASTER=dbu;SLAVES=dbyin
TABLE NAME=stream;MASTER=dbu;SLAVES=dbyin
TABLE NAME=user;MASTER=dbu;SLAVES=dbyin
TABLE NAME=user_account;MASTER=dbu;SLAVES=dbyin
TABLE NAME=usergenre;MASTER=dbu;SLAVES=dbyin
TABLE NAME=video;MASTER=dbu;SLAVES=dbyin

## party_db @ omegaparty

TABLE NAME=genre;MASTER=dbu;SLAVES=dbo
TABLE NAME=image;MASTER=dbu;SLAVES=dbo
TABLE NAME=meeting;MASTER=dbu;SLAVES=dbo
TABLE NAME=meet;MASTER=dbu;SLAVES=dbo
TABLE NAME=news;MASTER=dbu;SLAVES=dbo
TABLE NAME=user_image;MASTER=dbu;SLAVES=dbo
TABLE NAME=userimage;MASTER=dbu;SLAVES=dbo
TABLE NAME=user_text;MASTER=dbu;SLAVES=dbo

## party_db @ newdata2, newdata3, yin, yang

TABLE NAME=albums;MASTER=ndbd3_albums;SLAVES=4*ndbd2_albums,ndbd3_albums
TABLE NAME=bannerupload;MASTER=ndbd3;SLAVES=4*ndbd2,ndbd3,dbyin_ndbd,dbyang_ndbd
TABLE NAME=bannerupload_log;MASTER=ndbd3;SLAVES=4*ndbd2,ndbd3,dbyin_ndbd,dbyang_ndbd
TABLE NAME=chat_message;MASTER=ndbd3;SLAVES=4*ndbd2,ndbd3,dbyin_ndbd,dbyang_ndbd
TABLE NAME=contact_ticket_attachment_data;MASTER=ndbd3;SLAVES=4*ndbd2,ndbd3,dbyin_ndbd,dbyang_ndbd
TABLE NAME=debug_log;MASTER=ndbd3;SLAVES=ndbd2,dbyin_ndbd,dbhang_ndbd
TABLE NAME=directmessage_data;MASTER=ndbd3;SLAVES=ndbd3,ndbd3,dbyin_ndbd,dbyang_ndbd
TABLE NAME=directmessage_lookup;MASTER=ndbd3;SLAVES=4*ndbd2,ndbd3,dbyin_ndbd,dbyang_ndbd
TABLE NAME=directmessage_state;MASTER=ndbd3;SLAVES=4*ndbd2,ndbd3,dbyin_ndbd,dbyang_ndbd
TABLE NAME=directmessage;MASTER=ndbd3;SLAVES=4*ndbd2,ndbd3,dbyin_ndbd,dbyang_ndbd
TABLE NAME=dmsgtype;MASTER=ndbd3;SLAVES=SLAVES=4*ndbd2,ndbd3,dbyin_ndbd,dbyang_ndbd
TABLE NAME=dumpdata;MASTER=ndbd3;SLAVES=SLAVES=4*ndbd2,ndbd3,dbyin_ndbd,dbyang_ndbd
TABLE NAME=fbgoingpre;MASTER=ndbd3;SLAVES=SLAVES=4*ndbd2,ndbd3,dbyin_ndbd,dbyang_ndbd
TABLE NAME=fix_conversation;MASTER=ndbd3;SLAVES=4*ndbd2,ndbd3,dbyin_ndbd,dbyang_ndbd
TABLE NAME=includecachedata;MASTER=ndbd3;SLAVES=4*ndbd2,ndbd3,dbyin_ndbd,dbyang_ndbd
TABLE NAME=invoicedata_log;MASTER=ndbd3;SLAVES=4*ndbd2,ndbd3,dbyin_ndbd,dbyang_ndbd
TABLE NAME=invoicedata;MASTER=ndbd3;SLAVES=4*ndbd2,ndbd3,dbyin_ndbd,dbyang_ndbd
TABLE NAME=spamremoved_directmessage;MASTER=ndbd3;SLAVES=4*ndbd2,ndbd3,dbyin_ndbd,dbyang_ndbd
TABLE NAME=specialop;MASTER=ndbd3;SSLAVES=4*ndbd2,ndbd3,dbyin_ndbd,dbyang_ndbd
TABLE NAME=ticketfeed;MASTER=ndbd3;SLAVES=4*ndbd2,ndbd3,dbyin_ndbd,dbyang_ndbd
TABLE NAME=uploadimage_data;MASTER=ndbd3;SLAVES=4*ndbd2,ndbd3,dbyin_ndbd,dbyang_ndbd
TABLE NAME=userimagedata;MASTER=ndbd3;SLAVES=4*ndbd2,ndbd3,dbyin_ndbd,dbyang_ndbd
TABLE NAME=videothumbdata;MASTER=ndbd3;SLAVES=4*ndbd2,ndbd3,dbyin_ndbd,dbyang_ndbd

## party_db @ yang

TABLE NAME=appic_event;MASTER=dbu;SLAVES=dbyang
TABLE NAME=artistalias;MASTER=dbu;SLAVES=dbyang
TABLE NAME=artist;MASTER=dbu;SLAVES=dbyang
TABLE NAME=artist_genre;MASTER=dbu;SLAVES=dbyang
TABLE NAME=lineup;MASTER=dbu;SLAVES=dbyang
TABLE NAME=party_genre;MASTER=dbu;SLAVES=dbyang
TABLE NAME=forumsettings;MASTER=dbu;SLAVES=dbyang
TABLE NAME=forum;MASTER=dbu;SLAVES=dbyang
TABLE NAME=message;MASTER=dbu;SLAVES=dbyang
TABLE NAME=postedinv3;MASTER=dbu;SLAVES=dbyang
TABLE NAME=topic;MASTER=dbu;SLAVES=dbyang
TABLE NAME=uploadimage_link;MASTER=dbu;SLAVES=dbyang
TABLE NAME=uploadimage;MASTER=dbu;SLAVES=dbyang

## party_db @ special2

TABLE NAME=invoice;MASTER=dbu;SLAVES=dbspecial_dbm
TABLE NAME=relation;MASTER=dbu;SLAVES=dbspecial_dbm
TABLE NAME=weatherforecast;MASTER=dbu;SLAVES=dbspecial_dbm
TABLE NAME=commentsinfo;MASTER=dbu;SLAVES=dbspecial_dbm

TABLE NAME=ad_comment;MASTER=dbu;SLAVES=dbyin
TABLE NAME=albumelement_comment;MASTER=dbu;SLAVES=dbyin
TABLE NAME=artist_comment;MASTER=dbu;SLAVES=dbyin
TABLE NAME=cartoon_comment;MASTER=dbu;SLAVES=dbyin
TABLE NAME=city_comment;MASTER=dbu;SLAVES=dbyin
TABLE NAME=column_comment;MASTER=dbu;SLAVES=dbyin
TABLE NAME=compoentry_comment;MASTER=dbu;SLAVES=dbyin
TABLE NAME=contest_comment;MASTER=dbu;SLAVES=dbyin
TABLE NAME=deaduser_comment;MASTER=dbu;SLAVES=dbyin
TABLE NAME=interview_comment;MASTER=dbu;SLAVES=dbyin
TABLE NAME=label_comment;MASTER=dbu;SLAVES=dbyin;
TABLE NAME=location_comment;MASTER=dbu;SLAVES=dbyin
TABLE NAME=meeting_comment;MASTER=dbu;SLAVES=dbyin
TABLE NAME=music_comment;MASTER=dbu;SLAVES=dbyin
TABLE NAME=news_comment;MASTER=dbu;SLAVES=dbyin
TABLE NAME=organization_comment;MASTER=dbu;SLAVES=dbyang
TABLE NAME=party_comment;MASTER=dbu;SLAVES=dbyang
TABLE NAME=photo_comment;MASTER=dbu;SLAVES=dbyang
TABLE NAME=poll_comment;MASTER=dbu;SLAVES=dbyang
TABLE NAME=promo_comment;MASTER=dbu;SLAVES=dbyang
TABLE NAME=report_comment;MASTER=dbu;SLAVES=dbyang
TABLE NAME=review_comment;MASTER=dbu;SLAVES=dbyang
TABLE NAME=stream_comment;MASTER=dbu;SLAVES=dbyang
TABLE NAME=user_comment;MASTER=dbu;SLAVES=dbyang
TABLE NAME=video_comment;MASTER=dbu;SLAVES=dbyang
TABLE NAME=virtualtour_comment;MASTER=dbu;SLAVES=dbyang
TABLE NAME=weblog_comment;MASTER=dbu;SLAVES=dbyang

## counter_db @ white

TABLE NAME=artist_counter;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=adclick_log;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=adclick;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=adimp;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=agenda_log;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=agendafilter;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=agendasearch;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=albumimagedatasize;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=allstat_allmax;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=allstat_allmax_mobile;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=allstat_cityfrontmax;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=allstat_citymax;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=allstat_citymax_mobile;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=allstat_frontmax;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=allstat;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=bgqueryv2;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=browsername;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=browserstat;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=buttonview;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=cityautorename_counter;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=cityinvalid_counter;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=counthit;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=counthitnew;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=counthitparty;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=daylog;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=daylog_spiders;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=daylog_total;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=daylog_user_total;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=directmessagedayinfo;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=dumpuse_total;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=elementstats_identity;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=elementstats_ip;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=emailticketuniq;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=facebooked;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=flockinvitationdayinfo;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=formsecretv2;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=freqcap;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=gmapshitv2;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=guestactivity_mem;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=hidepoll_log;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=hidepoll;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=hit_log;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=identityv4;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=image_counter;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=image_counter_overlay;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=image_counter_thumbnail;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=image_counter_total;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=image_counter_overlay_total;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=image_counter_thumbnail_total;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=image_gallery_year_counter;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=image_gallery_overlay_year_counter;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=image_gallery_thumbnail_year_counter;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=image_party_total;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=image_party_overlay_total;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=image_party_thumbnail_total;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=infohit;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=ip_hits;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=ip6city;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=ip6list;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=ipcity;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=iplist;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=is_robot_ipv4;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=is_robot_ipv6;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=jobclick;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=lastlanguage;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=lastlanguage_log;MASTER=dbblack;SLAVES=dbbwhite
TABLE NAME=last_ip;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=lastseenitem;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=linkclick;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=livestreamhit;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=login_hits;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=login_hits_ok_ip;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=mobileactivity_mem;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=news_counter;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=organization_counter;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=tickethit;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=user_data_counter;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=videoopen_counter;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=videoverview_counter;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=boarding_counter;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=cartoon_counter;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=column_counter;MASTER=dbblack;SLAVES=dbblack
TABLE NAME=comment_counter;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=comment_counter_total;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=directmessage_counter;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=elementstats_user;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=interview_counter;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=location_counter;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=user_session;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=videoview;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=user_data;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=agenda_counter;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=teasercount;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=party_view;MASTER=dbblack;SLAVES=dbwhite

## counter_db @ dbwhite

TABLE NAME=music_genre;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=newlinkclick_log;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=newlinkclick;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=newsadclick_log;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=newsadclick;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=online_log;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=online;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=pagehitbuffer;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=pagehitnew;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=pagehitrememberip;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=pagehitrememberuser;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=pagehitstorehistory;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=partybussenhit;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=photo_size_use;MASTER=dbblack;SLAVES=dbwhite
TABLE MAME=pingdom_hit;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=presencehit;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=promoperiod;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=promoperiodstat;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=promoseen;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=promoseensmall;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=resolution;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=shortlinkstats;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=spideractivity_mem;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=spiderhit;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=stream_play_hit;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=supersearch;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=supersearchpop;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=survey_seen;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=taggerinfo;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=ticketseen;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=tmptoken;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=topwarningseen;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=uniquecode;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=urlclick;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=useractivity_mem;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=user_agent_hits;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=userlist_cache;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=virtualtour_counter;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=warm_hostnames;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=albumelement_counter_log;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=albumelement_counter;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=compoentry_counter;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=feed_counter;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=flockmessage_counter;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=ical_counter;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=flocktopic_counter;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=flocktopic_year_counter;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=job_counter;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=message_counter;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=misccount;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=music_load_counter;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=music_playlist_counter;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=music_view_counter;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=report_counter;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=review_counter;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=stream_play_counter;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=topic_counter;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=topic_year_counter;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=user_counter;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=user_counter_total;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=virtualxml_counter;MASTER=dbblack;SLAVES=dbwhite
TABLE NAME=weblog_counter;MASTER=dbblack;SLAVES=dbwhite
