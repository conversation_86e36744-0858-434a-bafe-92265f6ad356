#!/usr/bin/php
<?php

declare(strict_types=1);

require_once __DIR__.'/../../public_html/defines/cache.inc';

header('Content-Type: text/plain; charset=ascii');

if (empty($argv[1])) {
	$key = CACHE_ALL;
} elseif (!in_array($key = "cache_$argv[1]", [CACHE_ALL, CACHE_HASHES], true)) {
	fwrite(STDERR, "argument $argv[1] not understood\n".basename($argv[0])." [all | hashes]\n");
	exit(1);
}

if (!($ch = curl_init())) {
	fwrite(STDERR, "failed to initialize curl\n");
	exit(1);
}

/** @noinspection CurlSslServerSpoofingInspection */
if (!curl_setopt_array($ch, [
	CURLOPT_RETURNTRANSFER	=> true,
	CURLOPT_FOLLOWLOCATION	=> true,
	# now. connect to https host 127.0.0.1 which is not valid
	CURLOPT_SSL_VERIFYHOST	=> false,
	CURLOPT_CONNECTTIMEOUT	=> 5,
	CURLOPT_URL	 			=> "https://127.0.0.1/clearcache:$key"])
) {
	fwrite(STDERR, "failed to set curl options\n");
	exit(1);
}

$data = curl_exec   ($ch);
$info = curl_getinfo($ch);

if ($info['http_code'] !== 200) {
	$data = trim($data);
	fwrite(STDERR, "cache clear request returned http status {$info['http_code']}: $data\n");
	if ($errno = curl_errno($ch)) {
		$error = curl_error($ch);
		fwrite(STDERR, "further error ($errno): $error\n");
	}
	exit(1);
}
