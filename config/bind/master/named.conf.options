# named.conf.options for master

include "/home/<USER>/config/bind/named.conf.options";

options {
	resolver-query-timeout 5000;

	directory			"/var/cache/bind";

	allow-query			{ any; };
	allow-query-cache	{ corpnets; };
	allow-transfer		{ corpnets; };
	allow-recursion		{ corpnets; };

	recursion yes;

	dnssec-validation auto;

	notify yes;
	also-notify { *********; };
	zone-statistics yes;

# WARNING:	Using forwarders renders the use of some SPAM host checks
#			useless, because the forwarder does wat to many requests and is
#			kind of permanently blocked. So, don't use forwarders.
#
#	forwarders {
#		**************;			# ns1.oxilion.nl
#		2a00:d10::53:1;			# ns1.oxilion.nl
#
#		*************;			# ns2.oxilion.nl
#		2a00:d10:3::53:2;		# ns2.oxilion.nl
#
#		**************;			# ns3.oxilion.net
#		2001:898:2000:1000::5;	# ns3.oxilion.net
#
#		*******;				# Google
#		*******;				# Google
#
#		2001:4860:4860::8888;	# Google
#		2001:4860:4860::8844;	# Google
#	};

	auth-nxdomain no;    # conform to RFC1035
	listen-on    { any; };
	listen-on-v6 { any; };

	listen-on    tls partyflock_cert { any; };
	listen-on-v6 tls partyflock_cert { any; };
};
