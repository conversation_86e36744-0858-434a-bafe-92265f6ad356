#!/bin/bash

set -e

SCRIPT_DIR=$(dirname -- $(realpath "${BASH_SOURCE[0]}"))
cd "$SCRIPT_DIR"
. ../generic/install.include.sh

function install_sphinx {
	dont_start_services
	$APT_NONINTERACTIVE  install sphinxsearch
	link_config /home/<USER>/config/searchparty/sphinx.conf.php /etc/sphinxsearch/sphinx.conf
	# make sure the sphinxsearch crontab is not run:
	if [ -f /etc/cron.d/sphinxsearch ]; then
		mv /etc/cron.d/sphinxsearch /etc/cron.d/sphinxsearch.disabled
	fi
	do_start_services
	/etc/init.d/sphinxsearch start
}

function install_sphinx_default {
	mkdir -p /etc/default
	link_config etc/default/sphinxsearch
}

function install_sphinxread {
	echo "grant select, insert, update, delete, create, lock tables on *.* to 'sphinxread'@'%';" | mariadb mysql
	echo "flush privileges;" | mariadb mysql
}

function install_manticore {
	dont_start_services
	wget https://repo.manticoresearch.com/manticore-repo.noarch.deb
	dpkg -i manticore-repo.noarch.deb
	rm manticore-repo.noarch.deb
	$APT_NONINTERACTIVE update
	$APT_NONINTERACTIVE install manticore manticore-extra
	do_start_services
}

case "$1" in
all|\
sphinx)			install_sphinx
				install_sphinx_default;;
sphinx-default)	install_sphinx_default;;
grant-reading)	install_sphinxread;;
manticore)		install_manticore;;
*)				echo "Usage: $0 (all | sphinx | sphinx-default | manticore)"; exit 1;;
esac
