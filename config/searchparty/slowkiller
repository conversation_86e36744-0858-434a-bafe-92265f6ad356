#!/usr/bin/perl -w

use strict;
require '/home/<USER>/config/perlinclude/daemonize.pm';

&daemonize;

while (1) {
	for (`echo "SHOW FULL PROCESSLIST" | /home/<USER>/config/mysql/runmariadb dbm`) {
		print $_;
		chomp;
		my($id,$user,$host,$db,$command,$time,$state,$info) = split(/\t/,$_);
		if (	$id eq 'Id'
		||	$command ne 'Query'
		||	$info !~ /^\s*SELECT\s*/
		||	$info !~ /\/\* search \*\//
		||	$time !~ /\d+/
		||	$time < 30
		||	$db ne 'party_db'
		||	$user ne 'party'
		) {
			next;
		}
		system('/home/<USER>/config/mysql/runmariadb admin -v processlist | bsd-mailx -e -a "From: searchparty <root>" -s "slowkilled '.$id.'" <EMAIL>');
		system("/home/<USER>/config/mysql/runmariadb admin kill $id");
	}
	sleep(5);
}
