#!/usr/bin/php
<?php

require_once '/home/<USER>/public_html/_exit_if_readonly.inc';
require_once '/home/<USER>/public_html/_db.inc';
require_once '/home/<USER>/public_html/_run_and_exit.inc';

const HOST_TO_DB = [
	'searchparty'		=> 'dbsphinx',
	'searchpartyrep'	=> 'dbsphinxrep',
];

run_and_exit(syslog: true);

function main(): bool {
	$ok = true;

	$argv = $GLOBALS['argv'];
	array_shift($argv);
	if (empty($argv)) {
		syslog_error(LOG_ERR, 'missing host paramaters');
		return false;
	}
	foreach ($argv as $host) {
		if (!preg_match('"^[\w\-\_\.]+$"', $host)) {
			syslog_error(LOG_ERR, 'bad host parameter: '.$host);
			$ok = false;
			continue;
		}
		if (!($db = HOST_TO_DB[$host] ?? null)) {
			syslog_error(LOG_ERR, 'host not understood: '.$host);
			$ok = false;
			continue;
		}
		if (!($dbspec = $GLOBALS['_db_servers'][$db] ?? null)) {
			syslog_error(LOG_ERR, $db.' not found, maybe offline?');
			$ok = false;
			continue;
		}
		if (false === ($sock = @fsockopen($host, 9312, $errno, $errstr, 10))) {
			syslog_error(LOG_CRIT, 'failed to connect to sphinx on '.$host.' ('.$errno.'): '.$errstr);
			$ok = false;
			continue;
		}
		$data = fread($sock, 100);
		$datastr = '';
		$len = strlen($data);

		for ($i = 0; $i < $len; ++$i) {
			$datastr .= ord($data[$i]);
		}

		if (strlen($data) !== 4
		||	$datastr !== '0001'
		) {
			syslog_error(LOG_ERR, 'invalid data from sphinx on '.$host.': '.$data);
			$ok = false;
			continue;
		}
	}
	return $ok;
}
