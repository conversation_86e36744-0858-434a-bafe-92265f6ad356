INSTALL PLUGIN sphinx SONAME 'ha_sphinx.so';

-- MySQL dump 10.13  Distrib 5.6.6-m9, for debian6.0 (x86_64)
--
-- Host: localhost    Database: party_db
-- ------------------------------------------------------
-- Server version	5.5.32-log

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `sphinxsearch`
--

DROP TABLE IF EXISTS `sphinxsearch`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sphinxsearch` (
  `id` bigint(20) unsigned NOT NULL,
  `weight` int(11) NOT NULL,
  `query` varchar(3072) NOT NULL,
  `group_id` int(11) DEFAULT NULL,
  `TYPEID` int(10) unsigned NOT NULL,
  `STAMP` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  KEY `query` (`query`)
) ENGINE=SPHINX DEFAULT CHARSET=latin1 CONNECTION='sphinx://127.0.0.1:9312/*';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sphinxsearch`
--

LOCK TABLES `sphinxsearch` WRITE;
/*!40000 ALTER TABLE `sphinxsearch` DISABLE KEYS */;
/*!40000 ALTER TABLE `sphinxsearch` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sphinxsearchrep`
--

DROP TABLE IF EXISTS `sphinxsearchrep`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sphinxsearchrep` (
  `id` bigint(20) unsigned NOT NULL,
  `weight` int(11) NOT NULL,
  `query` varchar(3072) NOT NULL,
  `group_id` int(11) DEFAULT NULL,
  `TYPEID` int(10) unsigned NOT NULL,
  `STAMP` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  KEY `query` (`query`)
) ENGINE=SPHINX DEFAULT CHARSET=latin1 CONNECTION='sphinx://10.1.1.234:9312/*';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sphinxsearchrep`
--

LOCK TABLES `sphinxsearchrep` WRITE;
/*!40000 ALTER TABLE `sphinxsearchrep` DISABLE KEYS */;
/*!40000 ALTER TABLE `sphinxsearchrep` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2013-07-30 21:37:41

CREATE FUNCTION sphinx_snippets RETURNS STRING SONAME 'ha_sphinx.so';
