#!/usr/bin/php
<?php

define('CURRENTSTAMP',	time());
define('DEBUG',		!isset($_SERVER['CRON']));
#define('DRYRUN',	true);

const MAIL_LOG	= '/var/log/mail.log';
const LAST_LOG	= '/tmp/.__last_flockproc_error';

require_once '/home/<USER>/public_html/_exit_if_readonly.inc';
require_once '/home/<USER>/public_html/_db.inc';
require_once '/home/<USER>/public_html/_date.inc';
require_once '/home/<USER>/public_html/_error.inc';
require_once '/home/<USER>/public_html/_helper.inc';
require_once '/home/<USER>/public_html/_execute.inc';

ob_implicit_flush();
$rc = main();
exit(show_messages_cli() || !$rc ? 1 : 0);

function main(): bool {
	$last_stamp =
		isset($GLOBALS['argv'][1])
	&&	$GLOBALS['argv'][1] === '--force'
	?	0
	:	(integer)@file_get_contents(LAST_LOG);

	$stamp = null;
	
	foreach (file(MAIL_LOG) as $line) {
		if ((($errstr = 'failed')		&& str_contains($line, 'FAILED'))
		||	(($errstr = 'SPAM COMPLAINT')	&& str_contains($line, 'SPAM COMPLAINT'))
		) {
			# found FAILED or SPAM COMPLAINT
		} else {
			# not found
			continue;
		}
		
		if (!preg_match('!^(?P<date>\w+ \d+ \d+:\d+:\d+)!', $line, $match)
			# 2023-03-15T06:12:39.501716+01:00
		&&	!preg_match('!^(?P<date>\d+\-\d+\-\d+T\d+:\d+:\d+\.\d+\+\d+:\d+)!', $line, $match)
		) {
			error_log('could not understand '.$errstr.' line: '.$line);
			continue;
		}
		$stamp = strtotime($match['date']);
		if ($stamp > $last_stamp) {
			$line = trim($line);
			error_log($line);
		}
	}
	if ($stamp) {
		file_put_contents(LAST_LOG, $stamp);
	}
	return true;
}