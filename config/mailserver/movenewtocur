#!/usr/bin/perl -w

use strict;
use Errno;

$| = 1;

my @dodirs = ('','Spam','Virus','Undeliverable');

my @maindirs;
if (defined($ARGV[0])) {
	if ($ARGV[0] =~ /^\//) {
		@maindirs = ($ARGV[0]);
	} else {
		my $pwd = `pwd`;
		chomp($pwd);
		@maindirs = ($pwd.'/'.$ARGV[0]);
	}
} else {
	@maindirs = `ls -d /home/<USER>/*`;
}
USER: foreach my $userpart (@maindirs) {
	chomp($userpart);
	if (!-d $userpart) {
		next;
	}
	if (!-d $userpart.'/new') {
		if (!-d $userpart.'/Maildir/new') {
			print STDERR "no new directory in $userpart or Maildir therein\n";
			next USER;
		}
		$userpart = $userpart.'/Maildir';
	}
	print "moving new to cur for $userpart/";
	foreach my $cdir (@dodirs) {
		print '.',$cdir;
		if (-d $userpart.'/.'.$cdir) {
			system("find $userpart/.$cdir/new -type f -name '*' -exec mv {} $userpart/.$cdir/cur \\;");
		}
		left(length($cdir)+1),cleartoend();
	}
	print ": done\n";
#	newline(),up(),clearline();
}

print "all moved from new to cur cleaned!\n";

sub left {
	my $cols = shift;
	if (!defined($cols)) {
		$cols = '';
	}
	print "\x1B[",$cols,'D';
}
sub up {
	my $lines = shift;
	if (!defined($lines)) {
		$lines = '';
	}
	print "\x1B[",$lines,'A';
}
sub clearline {
	print "\x1B[2K";
}
sub cleartoend {
	print "\x1B[K";
}
sub newline {
	print "\n";
}
