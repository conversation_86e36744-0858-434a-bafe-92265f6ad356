# vim:syntax=apparmor
# Author: <PERSON> <<EMAIL>>
# Last Modified: Sun Aug  3 09:39:03 2008

#include <tunables/global>

/usr/bin/freshclam flags=(attach_disconnected) {
  #include <abstractions/base>
  #include <abstractions/nameservice>
  #include <abstractions/user-tmp>
  #include <abstractions/openssl>

  capability dac_override,
  capability chown,

  capability setgid,
  capability setuid,

  @{PROC}/filesystems r,
  owner @{PROC}/[0-9]*/status r,

  /etc/clamav/clamd.conf r,
  /etc/clamav/freshclam.conf r,
  /etc/clamav/onerrorexecute.d/* mr,
  /etc/clamav/onupdateexecute.d/* mr,
  /etc/clamav/virusevent.d/* mr,

  /home/<USER>/config/generic/etc/hosts r,

  owner @{HOME}/.clamtk/db/ rw,
  owner @{HOME}/.clamtk/db/** rwk,

  owner @{HOME}/.klamav/database/ rw,
  owner @{HOME}/.klamav/database/** rwk,

  /usr/bin/freshclam mr,

  /var/lib/clamav/ r,
  /var/lib/clamav/** krw,

  /var/log/clamav/* krw,
  /{,var/}run/clamav/freshclam.pid w,
  /{,var/}run/clamav/clamd.ctl rw,

  deny /{,var/}run/samba/{gencache,unexpected}.tdb mrwkl,

  # Site-specific additions and overrides. See local/README for details.
  #include <local/usr.bin.freshclam>
}