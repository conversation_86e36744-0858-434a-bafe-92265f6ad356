#!/usr/bin/perl

use strict;

if (!defined $ARGV[0]) {
	print STDERR $0," <database>\n";
	exit(1);
}

my $db = $ARGV[0];

print "SELECT TABLE_NAME FROM information_schema.tables WHERE TABLE_SCHEMA=\"$db\" AND ENGINE=\"MyISAM\" ORDER BY TABLE_NAME ASC\n";

foreach (`echo "SELECT TABLE_NAME FROM information_schema.tables WHERE TABLE_SCHEMA='$db' AND ENGINE='MyISAM' ORDER BY TABLE_NAME ASC" | mysql`) {
	chomp;
	print "ALTER TABLE `$_` ENGINE=Aria TRANSACTIONAL=1;\n";
}
