#!/usr/bin/perl -w

$0 = 'checkslaves';

use strict;
use POSIX;
use DBD::mysql;
use IO::File;
use IO::Socket::INET;
use HTML::Escape qw{escape_html};
use Fcntl ':flock';
use String::CRC32;
use Sys::Hostname;

sub get_error_prefix($$) {
	my ($host, $connection) = @_;
	return "<span style=\"color: red;\">$host</span>". ($connection ? ".<span style=\"color: orange;\">$connection</span>" : '');
}

if ($#ARGV < 0) {
	print STDERR "need slave argument(s): host(,password(,port(,connectionid)))\n";
	exit 1;
}

my $hostname = hostname();
my $log;
my @failures;
my @failslaves;
#my @slowqueries;
my $passwd = '';
my $dbh;

if (-f '/offline'
||	-f '/readonly'
) {
	# Don't check the replicas, Partyflock is intentionally offline or read-only (in maintenance)
	exit;
}

for (@ARGV) {
	my @info = split(/,/,$_);

	my $host = $info[0];
	if ($#info > 0
	&&	$info[1]
	) {
		$passwd = $info[1];
	}
	my $port = 3306;
	if ($#info > 1
	&&	$info[2]
	) {
		$port = $info[2];
	}
	my $connection = '';
	if ($#info > 2
	&&	$info[3]
	) {
		$connection = $info[3];
	}
	if ($host eq 'localhost') {
		# used to store default password
		next;
	}
	my $is_local = ($host =~ /^\//);
	my $axs = $is_local ? 'mysql_socket='.$host : 'host='.$host.';port='.$port;
	if ($is_local) {
		$port = '';
	}
	my $dsn = 'DBI:mysql:mysql_connect_timeout=10;'.$axs;
	if ($dbh) {
		$dbh->disconnect;
	}
	if (!($dbh = DBI->connect($dsn,'cleaner',$passwd, { PrintError => 0 }))) {
		push @failures, get_error_prefix($host, $connection).": failed to connect to slave!\n<span class=\"opacity: .4\">".escape_html(DBI->errstr)."</span>\n\n";
		push @failslaves,$host;
		next;
	}
	my $sth = $dbh->prepare('SHOW SLAVE "'.$connection.'" STATUS');
	if (!$sth) {
		push @failures,	get_error_prefix($host, $connection).": slave prepare failed!\n<span class=\"opacity: .4\">".escape_html($dbh->errstr)."</span>\n\n";
		push @failslaves,$host;
		next;
	}
	if (!$sth->execute) {
		push @failures, get_error_prefix($host, $connection).": slave execute failed\n<span class=\"opacity: .4\">".escape_html($sth->errstr)."</span>\n\n";
		push @failslaves,$host;
		next;
	}
	my $status = $sth->fetchrow_hashref();
	if (!$status) {
		push @failures, get_error_prefix($host, $connection).": could not obtain slave status!\n";
		push @failures, "<span class=\"opacity: .4\">".(defined $sth->errstr ? escape_html($sth->errstr) : "no error available, maybe not initialized as slave")."</span>\n\n";
		push @failslaves,$host;
		$sth->finish;
		next;
	}
	$sth->finish;

	my $backing_up = 0;

=pod
	my $slave_data = "/tmp/__checkslave_${host}_$port";

	# these statuses often return strange 'Seconds_Behind_Master'

	if ($status->{'Slave_IO_Running'} eq 'Checking master version'
	||	$status->{'Slave_IO_Running'} eq 'Preparing'
	||	$status->{'Slave_SQL_Running'} eq 'after apply log event'
	) {
		if (!sysopen(SLAVE_DATA,$slave_data,IO::File::O_RDWR | IO::File::O_CREAT)) {
			push @failures,"failed to open slave data file $slave_data\n";
			next;
		}
		my $lines = @{[<SLAVE_DATA>]};

		if ($lines > 5) {
			push @failures,"$host:$connection, Slave_IO_Running is '".$status->{'Slave_IO_Running'}."', Last_IO_Error: ",$status->{'Last_IO_Error'},"\n\n";
			push @failslaves,$host;
			next;
		}
		seek(SLAVE_DATA,0,SEEK_END);
		syswrite(SLAVE_DATA,"Preparing\n");
		close(SLAVE_DATA);

		next;
	}
	if (-e $slave_data) {
		unlink $slave_data;
	}
=cut
	if ($status->{'Slave_IO_Running'} ne 'Yes') {
		push @failures, get_error_prefix($host, $connection).get_last_db_error($status, 'IO');
		push @failslaves,$host;
		next;
	}
	my $sql_running = $status->{'Slave_SQL_Running'} eq 'Yes';
	if (!$sql_running) {
		if ($host eq 'backupparty') {
			my $sock = IO::Socket::INET->new(
				PeerAddr	=> 'superstore',
				PeerPort	=> 40001
			);
			if ($sock) {
				print $sock "backing_up\r\n";
				while (<$sock>) {
					if (/YES/) {
						$backing_up = 1;
					}
				}
				undef $sock;
			}
		}
		if (!$backing_up) {
			push @failures, get_error_prefix($host, $connection).get_last_db_error($status, 'SQL');
			push @failslaves,$host;
		}
	}
	if (defined $status->{'SQL_Remaining_Delay'}) {
		if ($status->{'Slave_SQL_Running_State'} eq 'Waiting until MASTER_DELAY seconds after master executed event'
		&&	$status->{'SQL_Remaining_Delay'} > 60
		) {
			# We must wait until the SQL_Remaining_Delay reaches zero, otherwise
			# Seconds_Behind_Master is not useful to measure below
			next;
		}
	}

	if (!defined $status->{'Seconds_Behind_Master'}) {
		# I believe, when SQL_Thread is not running, Seconds_Behind_Master is not defined.
		# So when SQL_Running is not 'Yes', a second warning here is not useful.
		# And contuing and checking Seconds_Behind_Master is also not useful,

		if ($sql_running) {
			push @failures, get_error_prefix($host, $connection).": Seconds_Behind_Master is not defined\n\n";
			push @failslaves, $host;
		}
		next;
	}

	# backupparty:
	#
	# backing up?
	# allow delay of 11 for dbc (takes a long time)
	# allow delay of 6 for dbm
	#	^^^ cannnot differentiate, so take 11 hours
	#
	# not backing up?
	# delay of 6 hours

	my $behind = $status->{'Seconds_Behind_Master'};
	my $max = 0;
	if ($host eq 'backupparty'
	?	$behind > ($max = ($backing_up ? 24 : 30) * 3600) # was 16 hours when not backing up
	:	(	$is_local
		?	($behind > ($max = 5 * 24 * 3600))
		:	(	$host =~ /^searchparty/
			?	($behind > ($max = 10 * 60))
			:	($behind > ($max =  2 * 60))
			)
		)
	) {
		push @failures, get_error_prefix($host, $connection).': Seconds_Behind_Master is '.
			  ($behind < 3600 ? sprintf('<span style="color: yellow;">%.1f</span> minutes', $behind / 60) : sprintf('%.1f hours', $behind / 3600)).
			" (".($max < 3600 ? sprintf('%.1f minutes', $max    / 60) : sprintf('%.1f hours', $max    / 3600))." max)\n\n";

		push @failslaves,$host;

#		print "SECONDs BEHIND MASTER TOO LARGE, max is $max\n";
#		use Data::Dumper;
#		print Dumper($status);

=pod	# show queries

		$sth = $dbh->prepare('SHOW FULL PROCESSLIST');
		if (!$sth) {
			push @failures,"$host:$connection, slave prepare failed: ".escape_html($dbh->errstr)."\n";
			push @failslaves,$host;
			next;
		}
		if (!$sth->execute) {
			push @failures,"$host:$connection, slave execute failed: ".escape_html($sth->errstr)."\n";
			push @failslaves,$host;
			next;
		}
		while (my $row = $sth->fetchrow_hashref()) {
			if (defined $row->{'State'}
			&&	$row->{'State'} ne 'Sleep'
			&&	$row->{'State'} ne 'Waiting for master to send event'
			&&	$row->{'Time'} > 10
			) {
				push @slowqueries,"$host:$connection, slow query (".$row->{'Time'}." s) in state ".$row->{'State'}.":  ".$row->{'Info'}."\n";
			}
		}
		$sth->finish;
=cut

	}
}

if ($dbh) {
	$dbh->disconnect;
}

if ($#failslaves == -1) {
	exit 0;
}

my $crcsum = 0;
foreach (@failures) {
	$crcsum += crc32($_);
}

my $show_failures = 1;
my ($sec, $min, $hour, $mday, $mon, $year, $weekday, $yday, $isdst) = localtime(time);

if (sysopen(FILE, '/tmp/__check_slaves_last_mail', IO::File::O_RDWR | IO::File::O_CREAT)) {
	flock(FILE, LOCK_EX);
	my $tens = floor($min / 10);
	my $now_mail = sprintf("%4d%02d%02d%02d%1d%010u", $year + 1900, $mon, $mday, $hour, $tens, $crcsum);
	my $info_length = length $now_mail;
	my $last_mail;
	if ($info_length == sysread(FILE, $last_mail, $info_length)
	&&	$last_mail eq $now_mail
	) {
		$show_failures = 0;
	} else {
		sysseek(FILE, 0, SEEK_SET);
		syswrite(FILE,$now_mail, $info_length, 0);
	}
	close(FILE);
}
if ($show_failures) {
	print STDERR '<html>';
	foreach my $errstr (@failures) {
		$errstr =~ s/\n/<br>\n/g;
		print STDERR $errstr;
	}
	print STDERR '</html>';
#	print STDERR "\n\n";
#	foreach my $qstr (@slowqueries) {
#		print STDERR $qstr,"\n";
#	}
}

exit 1;

sub get_last_db_error {
	my $status = shift;
	my $SQL_or_IO = shift;
	my $error_field = "Last_${SQL_or_IO}_Error";
	my $running_field = "Slave_${SQL_or_IO}_Running";
	my @errors;
	if (!$status->{$error_field}) {
		return '';
	}
	return 	": $running_field is '".$status->{$running_field}."'".
		(	$status->{$error_field}
		?	"\n".'<span class="opacity: .4">Last_Error: '.escape_html($status->{$error_field})."</span>\n\n"
		:	"\n\n");

}
