INSTALL PLUGIN sphinx SONAME 'ha_sphinx.so';
CREATE FUNCTION sphinx_snippets RETURNS STRING SONAME 'ha_sphinx.so';

CREATE TABLE `sphinxsearch` (
  `id` bigint(20) unsigned NOT NULL,
  `weight` int(11) NOT NULL,
  `query` varchar(3072) NOT NULL,
  `group_id` int(11) DEFAULT NULL,
  `TYPEID` int(10) unsigned NOT NULL,
  `STAMP` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  KEY `query` (`query`)
) ENGINE=SPHINX DEFAULT CHARSET=latin1 CONNECTION='sphinx://**********:9312/*';

CREATE TABLE `sphinxsearchrep` (
  `id` bigint(20) unsigned NOT NULL,
  `weight` int(11) NOT NULL,
  `query` varchar(3072) NOT NULL,
  `group_id` int(11) DEFAULT NULL,
  `TYPEID` int(10) unsigned NOT NULL,
  `STAMP` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  KEY `query` (`query`)
) ENGINE=SPHINX DEFAULT CHARSET=latin1 CONNECTION='sphinx://**********:9312/*';

