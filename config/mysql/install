#!/bin/bash

set -e	# exit immediately on certain errors
# set -x	# show commands
# set -v

SCRIPT_DIR=$(dirname -- $(realpath "${BASH_SOURCE[0]}"))
cd "$SCRIPT_DIR"
. ../generic/install.include.sh

function install_mysql_history_path {
	mkdir -p /var/log/mysql_history
}

function install_init_mariadb {
	update-rc.d mysql remove
	rm -f /etc/init.d/mariadb
	link_config /home/<USER>/config/mysql/init_mariadb /etc/init.d/mariadb
	update-rc.d mariadb defaults
}

function install_default_config {
	rm -f	/etc/mysql/my.cnf \
			/db/my.cnf
	mkdir -p /etc/mysql/mariadb.conf.d
	for name in \
		/etc/mysql/debian-start
	do
		link_config /home/<USER>/config/mysql$name $name
	done
	CFG_FILE=/home/<USER>/config/mysql/my.cnf_${HOSTNAME}
	link_config "$CFG_FILE" /etc/my.cnf
	link_config "$CFG_FILE" /etc/mysql/mariadb.conf.d/50-server.cnf
}

function install_packages {
	$APT_NONINTERACTIVE install \
		libjemalloc2 \
		libtcmalloc-minimal4 \
		mariadb-server \
		mysqltuner \
		percona-toolkit

	rm -rf /var/lib/mysql
}

case "$1" in
init_mariadb)
	install_init_mariadb
	;;
default_config)
	install_default_config
	;;
packages)
	install_packages
	;;
all)	$APT_NONINTERACTIVE update
	install_init_mariadb
	install_default_config
	install_packages
	# sandbox on vmware does not support chown on shared map (which contains the database)
	if [ "$HOSTNAME" != 'sandbox' ] \
	&& [ "$HOSTNAME" != 'sandbox-too' ] \
	&& [ -d $dir ]; then
		chown mysql:mysql -R $dir
	fi
	;;
*)	echo "Usage: $0 (init_mariadb | default_config | packages | all)"
	exit 1
esac

exit 0
\
