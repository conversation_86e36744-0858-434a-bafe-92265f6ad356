LOG_MAIL=/home/<USER>/config/generic/log_mail
PATH=/bin/:/usr/bin:/usr/local/bin:/home/<USER>/config/generic:/home/<USER>/config/mysql:/home/<USER>/config/webserver:/home/<USER>/config/bind
NICE=nice -n 20
NOCACHE=nocache
NICE_NOCACHE=nice -n 20 nocache
MAILTO=<EMAIL>
CRON=1

# minute         0-59
# hour           0-23
# day of month   0-31
# month          0-12 (or names, see below)
# day of week    0-7 (0 or 7 is Sun, or use names)

45  */4 * * * $NICE /home/<USER>/config/mysql/flushmaster localhost,dustSUCK alphaparty omegaparty yin,,,dbm yang,,,dbm searchparty,,,dbm searchpartyrep,,,dbm special2,,,dbm backupparty,,,dbm 2>&1 | $LOG_MAIL "flush master" html
*/5 *   * * * $NICE /home/<USER>/config/mysql/checkslaves localhost,dustSUCK alphaparty omegaparty yin,,,dbm yang,,,dbm searchparty,,,dbm searchpartyrep,,,dbm special2,,,dbm backupparty,,,dbm 2>&1 | $LOG_MAIL "check slaves" html
