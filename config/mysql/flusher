#!/usr/bin/perl -w

use strict;
use IPC::Open3;

my $rc = 0;
my $pwdfile = '--defaults-extra-file='.$ENV{'HOME'}.'/.my.cnf';

if (-f $pwdfile) {
	push @ARGV,'--defaults-extra-file='.$pwdfile;
}

push @ARGV,'--unbuffered';
push @ARGV,'--batch';

#if (open MYCNF,$ENV{'HOME'}.'/.my.cnf') {
#	while (<MYCNF>) {
#		if (/password\s*=\s*(.*)/) {
#			push @ARGV,'--password='.$1;
#		}
#	}
#	close MYCNF;
#}

if (!-f '/usr/bin/mariadb') {
	print STDERR "no mariadb client found\n";
}

my $path = $_;

my $pid = open3(\*MYSQLIN, \*MYSQLOUT, \*MYSQLERR, $path, @ARGV);
if (!$pid) {
	print STDERR "failed to open pipe to mysql\n";
	exit 1;
}

print MYS<PERSON>IN "SHOW TABLES; SELECT 'READY';\n";

my @tables;

TABLE: while (<MYSQLOUT>) {
	if (/^Tables_in_/) {
		next TABLE;
	}
	if (/^READY/) {
		last TABLE;
	}
	chomp;
	push @tables, $_;
}

if ($#tables == -1) {
	print STDERR "no tables to be flushed!\n";
	$rc = 1;
} else {
	for (@tables) {
		print MYSQLIN "FLUSH TABLE `$_`;\n";
	}
	print MYSQLIN "SELECT 'READY';\n";

	WAIT: while (<MYSQLOUT>) {
		chomp;
		if ($_ eq 'READY') {
			last WAIT;
		}
		print STDERR "something happend while waiting for flushes: $_\n";
		$rc = 1;
	}
}
close MYSQLIN;
close MYSQLOUT;

ERR: while (<MYSQLERR>) {
	print STDERR $_;
	$rc = 1;
}

close MYSQLERR;

waitpid $pid,0;

exit $rc;
