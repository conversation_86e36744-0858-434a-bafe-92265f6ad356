#!/usr/bin/perl -w

$0 = 'flushmaster';

use strict;
use warnings;
use DBI;
use DBD::mysql;
use Sys::Syslog;

if (-f '/offline'
||	-f '/readonly'
) {
	# Don't flush master when Partyflock is intentionally offline or read-only (in maintenance)
	exit;
}

if ($#ARGV < 0) {
	print STDERR "need server argument(s): host(,password(,port(,connectionid)))\n";
	exit 1;
}

sub get_error_prefix($$) {
	my ($host, $connection) = @_;
	return "<span style=\"color: red;\">$host</span>". ($connection ? ".<span style=\"color: orange;\">$connection</span>" : '');
}

if (!openlog('maendaemon','pid','cron')) {
	exit 1;
}

my $log;
my @failures;
my $error;

#my $masterpasswd = 'dustSUCK';
my $masterpasswd = '';
my $passwd = $masterpasswd;
my $masterargument = 'mysql_socket=/db/mysql.sock';

for (@ARGV) {
	my @info = split(/,/,$_);
	my $host = $info[0];
	if ($host eq 'localhost') {
		$masterpasswd = $passwd = $info[1];
		$masterargument = defined $info[2] ? ($info[2] =~ /^\d+$/ ? 'port='.$info[2] : 'mysql_socket='.$info[2]) : 'mysql_socket=/db/mysql.sock';
		next;
	}
	if ($#info > 0
	&&	$info[1]
	) {
		$passwd = $info[1];
	}
	my $port = 3306;
	if ($#info > 1) {
		$port = $info[2];
	}
	my $prefix = $host;
	my $connection = '';
	if ($#info > 2) {
		$connection = $info[3];
		$prefix .= '.'.$connection;

	}
	my $dsn = 'DBI:mysql:mysql_connect_timeout=10;host='.$host.';port='.$port;
	my $dbh = DBI->connect($dsn,'cleaner',$passwd,{PrintError=>0});
	syslog('info', "checking slave state on $host:$port");
	if (!$dbh) {
		# prolly offline, checkslaves will report about this!
		$error = ": failed to connect to slave: ".DBI->errstr;
		push @failures, get_error_prefix($host, $connection).$error;
		syslog('err', $prefix.$error);
		next;
	}
	my $sth = $dbh->prepare('SHOW SLAVE "'.$connection.'" STATUS');
	if (!$sth) {
		$error = ": slave prepare failed: ".$dbh->errstr;
		push @failures, get_error_prefix($host, $connection).$error;
		syslog('err', $prefix.$error);
		$dbh->disconnect;
		next;
	}
	if (!$sth->execute) {
		$error = ": slave execute failed: ".$sth->errstr;
		push @failures, get_error_prefix($host, $connection).$error;
		syslog('err', $error);
		$dbh->disconnect;
		next;
	}
	my $status = $sth->fetchrow_hashref();
	if (!$status) {
		$error = ": could not obtain slave status: ".($sth->errstr ? $sth->errstr : 'no info');
		push @failures, get_error_prefix($host, $connection).$error;
		syslog('err', $error);
		$dbh->disconnect;
		next;
	}
	if (!defined($log)
	||	$status->{'Master_Log_File'} lt $log
	) {
		$log = $status->{'Master_Log_File'};
	}
	$sth->finish;
	$dbh->disconnect;
}

# FIXME: If something fails below, no HTML formatting is used yet.

my $dsn = 'DBI:mysql:mysql_connect_timeout=10;host=localhost;'.$masterargument;
my $dbh = DBI->connect($dsn,'cleaner',$masterpasswd);

if (!$dbh) {
	$error = "failed to connect to master on localhost";
	push @failures, $error;
	syslog('err', $error);
} else {
	my $qstr;
	my $rv = $dbh->do($qstr = "PURGE MASTER LOGS TO '".$log."'");
	if (!$rv) {
		$error = "query on master failed: $qstr: ".$dbh->errstr;
		push @failures, $error;
		syslog('err', $error);
	}
	syslog('info', "master logs purged to $log");
	$dbh->disconnect;
}

if ($#failures >= 0) {
	print STDERR "<html lang=\"en\">";
	foreach my $errstr (@failures) {
		$errstr =~ s/\n/<br \/>\n/g;
		print STDERR "$errstr\n";
	}
	print STDERR "</html>";
	exit 1;
}
