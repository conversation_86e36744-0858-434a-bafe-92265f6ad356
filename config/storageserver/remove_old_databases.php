#!/usr/bin/php
<?php

/*
require '/home/<USER>/public_html/_date.inc';

define('CURRENTSTAMP',		time());
define('KEEP_WEEKS_DATA',	12);
define('KEEP_WEEKS_MAIN',	156);

ob_implicit_flush();

main();

function main() {
	foreach (array(
		'dbm',
		'dbc',
		'dbtrans',
		'dbds1',
	) as $db) {
		$removes = find_removables($db);
		if (!$removes) {
			continue;
		}
		echo $db; ?>: should remove: <? print_r($removes);
		echo "\n";
		remove_dirs($db,$removes);
	}
}
function remove_dirs($db,$removes) {
	$keep = 0 === strpos($db,'dbd') ? KEEP_WEEKS_DATA : KEEP_WEEKS_MAIN;
	$keepdiff = $keep * 7 * 24 * 3600;
	foreach ($removes as $stamp => $path) {
		$entries = scandir($path);
		foreach ($entries as $entry) {
			if ($entry == '.'
			||	$entry == '..'
			) {
				continue;
			}
			$entrystamp = filemtime($fullpath = $path.'/'.$entry);
			if (!$entrystamp) {
				error_log('zero stamp for '.$fullpath,0);
				continue;
			}
			if (CURRENTSTAMP - $entrystamp < $keepdiff) {
				error_log('not old enough for '.$fullpath,0);
				continue;
			}
			unlink($fullpath);
		}
		rmdir($path);
	}
}
function find_removables($db) {
	$entries = scandir($dbpath = '/archive/database/'.$db);
	$flush = array();
	$keepcnt = 0;
	$keep = 0 === strpos($db,'dbd') ? KEEP_WEEKS_DATA : KEEP_WEEKS_MAIN;
	$keepdiff = $keep * 7 * 24 * 3600;
	foreach ($entries as $entry) {
		# 2010-08-13,12:39.24
		# 1    2  3  4  5  6
		if (!preg_match('"^(\d{4})\-(\d{2})\-(\d{2}),(\d{2}):(\d{2})\.(\d{2})$"',$entry,$matches)) {
			continue;
		}
		$entrystamp = mktime($matches[4],$matches[5],$matches[6],$matches[2],$matches[3],$matches[1]);
#		error_log('diff: '.(CURRENTSTAMP - $entrystamp).' < '.$keepdiff,0);
		if (CURRENTSTAMP - $entrystamp < $keepdiff) {
#			error_log('skipping '.$db.':'.$entry,0);
			++$keepcnt;
			continue;
		}
		$entrystamp = filemtime($fullpath = $dbpath.'/'.$entry);
		if (!$entrystamp) {
#			error_log('skipping2 '.$db.':'.$entry,0);
			++$keepcnt;
			continue;
		}
		if (CURRENTSTAMP - $entrystamp < $keepdiff) {
#			error_log('skipping3 '.$db.':'.$entry,0);
			++$keepcnt;
			continue;
		}
#		error_log('flusing '.$fullpath,0);
		$flush[$entrystamp] = $fullpath;
	}
	if ($keepcnt < $keep) {
		ksort($flush);
		$flush = array_slice($flush,0,count($flush) - $keep + $keepcnt,true);
	}
	return $flush;
}

*/