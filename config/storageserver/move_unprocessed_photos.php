#!/usr/bin/php
<?php

declare(strict_types=1);

# This tools scans all the photo backup directores in search for unprocessed photo directories.

const DRYRUN = false;
const DO_ALL = false;

require_once __DIR__.'/../../public_html/_dir_size.inc';
require_once __DIR__.'/../../public_html/_run_and_exit.inc';

run_and_exit(syslog: true);

function main(): bool {
	$photos_path = '/archive/backups/photos_v2';
	syslog(LOG_DEBUG, "opening path $photos_path");
	if (!($backups = @opendir($photos_path))) {
		syslog_last_error(LOG_CRIT, "opendir of $photos_path failed");
		return false;
	}
	while (false !== ($date_dir_name = readdir($backups))) {
		if ($date_dir_name[0] === '.'
		||	$date_dir_name === 'current'
		) {
			continue;
		}
		$entries[$date_dir_name] = filemtime($date_path = $photos_path.'/'.$date_dir_name);
	}
	closedir($backups);

	arsort($entries);
	
	if (DO_ALL !== true) {
		$entries = [array_key_first($entries) => 0];
	}
	
	foreach ($entries as $date_dir_name => $ignored) {
		syslog(LOG_DEBUG, "found $date_dir_name");
		$date_path = "$photos_path/$date_dir_name";
		if (!is_dir($date_path)) {
			syslog(LOG_WARNING, "not a directory, skipping");
			continue;
		}
		syslog(LOG_DEBUG, "encountered $date_path");
		if (!($date_dir = @opendir($date_path))) {
			syslog_last_error(LOG_CRIT, "opendir of $date_path failed");
			return false;
		}
		while (false !== ($partyid = readdir($date_dir))) {
			if ($partyid[0] === '.'
			||	!ctype_digit($partyid)
			) {
				continue;
			}
			$party_path = "$date_path/$partyid";
			syslog(LOG_DEBUG, "checking party $party_path");

			foreach (['unprocessed', 'uncompressed'] as $move_dir) {
				if (!is_dir($unprocessed_path = "$party_path/$move_dir")) {
					continue;
				}			
				$dst_date_dir = "{$photos_path}_unprocessed/";
				if (is_dir($src_path = "/mnt/gluster/photos_v2/$partyid/$move_dir")) {
					$dst_path = $dst_date_dir.$partyid;
					if (false === ($src_size = dir_size($src_path))) {
						syslog_last_error(LOG_CRIT, "dir_size of $src_path failed");
						return false;
					}
					syslog(	LOG_DEBUG,
							"source path: $src_path\n".
							"source size: $src_size\n".
							"destination path: $dst_path"
					);
					
					if (file_exists($dst_path)) {
						if (false === ($dst_size = dir_size($dst_path))) {
							syslog_last_error(LOG_CRIT, "dir_size of $dst_path failed");
							return false;
						}
						syslog(LOG_DEBUG, "destination size: $dst_size");
						if ($dst_size < $src_size) {
							syslog(	LOG_DEBUG,
									"destination size smaller than source size\n".
									"copy from $unprocessed_path/* to $dst_path"
							);
							$exe = "/bin/cp -lr $unprocessed_path/* $dst_path/";
							if (!DRYRUN) {
								system($exe, $rc);
								if ($rc) {
									syslog_error(LOG_CRIT, "copy from $unprocessed_path/* to $dst_path failed");
									return false;
								}
							}
						}
					} else { 
						syslog( LOG_DEBUG,
								"destination path does not exist\n".
								"copy from $unprocessed_path to $dst_path"
						);
						$exe = "/bin/cp -lr $unprocessed_path $dst_path";
						if (!DRYRUN) {
							system($exe, $rc);
							if ($rc) {
								syslog_error(LOG_CRIT, "copy from $unprocessed_path to $dst_path failed");
								return false;
							}
						}
					}
					clearstatcache();
					if (false === ($dst_size = dir_size($dst_path))) {
						syslog_last_error(LOG_CRIT, "dir_size of $dst_path failed");
						return false;
					}
					syslog(LOG_DEBUG, "new destination path size: $dst_size");
					if ($dst_size !== $src_size) {
						syslog_error(LOG_WARNING, "for party $partyid, source size on gluster ($src_size) !== destination size ($dst_size)");
					} else {
						syslog(LOG_DEBUG, "source size == destination size, removing source path");
						$exe = 'rm -Rf '.$src_path;
						if (!DRYRUN) {
							system($exe, $rc);
							if ($rc) {
								syslog_error(LOG_CRIT, "removal of source path $src_path failed");
								return false;
							}
						}
					}
				}
			}
		}
		closedir($date_dir);
	}
	return true;
}
