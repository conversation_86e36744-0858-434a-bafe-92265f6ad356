#!/usr/bin/php
<?php

declare(strict_types=1);

require_once '/home/<USER>/public_html/_exit_if_readonly.inc';
require_once '/home/<USER>/public_html/_db.inc';
require_once '/home/<USER>/public_html/_helper.inc';
require_once '/home/<USER>/public_html/_run_and_exit.inc';

define('CURRENTSTAMP',	time());
define('KEEPPERIOD', 2 * ONE_WEEK);
define('DEBUG',			!isset($_SERVER['CRON']));

run_and_exit(syslog: true);

function main(): bool {
	foreach (['pagehit', 'counthit'] as $prefix) {
		if (!($tables = db_simpler_array(null, "SHOW TABLES LIKE '{$prefix}done%'", DB_FORCE_SERVER, 'dbbc'))) {
			if ($tables === false) {
				return false;
			}
			continue;
		}
		foreach ($tables as $table) {
			if (!preg_match('"^'.$prefix.'done_(\d+)(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})_(\d+)$"', $table, $match)) {
				syslog_error(LOG_WARNING, "table not recognized: $table");
				continue;
			}
			[, $year, $month, $day, $hour, $min, $sec, $pid] = $match;

			$done_stamp = mktime((int)$hour, (int)$min, (int)$sec, (int)$month, (int)$day, (int)$year);

			if (CURRENTSTAMP - $done_stamp < KEEPPERIOD) {
				continue;
			}

			$checksum_src = db_single_assoc(null, "CHECKSUM TABLE     counter_db.$table", DB_FORCE_SERVER, 'dbbc');
			$checksum_dst = db_single_assoc(null, "CHECKSUM TABLE pagehitstorage.$table", DB_FORCE_SERVER, 'dbbc');

			if (empty($checksum_src)
			||	empty($checksum_dst)
			||	$checksum_src['Checksum'] !== $checksum_dst['Checksum']
			) {
				if (!db_alter(null, "
					RENAME TABLE counter_db.$table
					TO {$prefix}storage.$table",
					DB_FORCE_SERVER,
					'dbbc')
				) {
					return false;
				}
				syslog(LOG_INFO, "renamed table counter_db.$table to {$prefix}storage.$table");
			}
			if (!db_drop(null, "
				DROP TABLE IF EXISTS counter_db.$table",
				DB_FORCE_SERVER | DB_NO_WARNINGS,
				'dbblack')
			) {
				return false;
			}
			syslog(LOG_INFO, "dropped table counter_db.$table");

			if ($warnings = db_warnings()) {
				foreach ($warnings as $warning) {
					if (str_starts_with($warning->message, 'Unknown table')) {
						syslog_error(LOG_WARNING, "could not find table on master: $table");
					} else {
						syslog_error(LOG_WARNING, var_get($warning));
					}
				}
			}

			foreach (['MYD', 'MYI', 'MAD', 'MAI', 'frm'] as $suffix) {
				$file = "/db/{$prefix}storage/$table.$suffix";
				if (!file_exists($file)) {
					continue;
				}
				if (!chmod($file, 0644)) {
					syslog_error(LOG_ERR, "could not chmod $file");
					return false;
				}
			}
			syslog(LOG_NOTICE, "stored $table in {$prefix}storage");
		}
	}
	return true;
}
