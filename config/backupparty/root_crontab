LOG_MAIL=/home/<USER>/config/generic/log_mail
PATH=/bin/:/usr/bin:/usr/local/bin:/home/<USER>/config/generic:/home/<USER>/config/mysql:/home/<USER>/config/webserver:/home/<USER>/config/bind
NICE=nice -n 20
NOCACHE=nocache
NICE_NOCACHE=nice -n 20 nocache
MAILTO=<EMAIL>
CRON=1

# minute         0-59
# hour           0-23
# day of month   0-31
# month          0-12 (or names, see below)
# day of week    0-7 (0 or 7 is Sun, or use names)

15 * * * * $NICE_NOCACHE /home/<USER>/config/backupparty/storehitdone.php 2>&1 | $LOG_MAIL "store hit done"
45 * * * * $NICE_NOCACHE /home/<USER>/config/backupparty/flushhitdone.php 2>&1 | $LOG_MAIL "flush hit done"

30 4 * * * $NICE_NOCACHE /home/<USER>/config/backupparty/cold_store_done_adimp_and_freqcaps.php  2>&1 | $LOG_MAIL "cold store adimp and freqcaps"
