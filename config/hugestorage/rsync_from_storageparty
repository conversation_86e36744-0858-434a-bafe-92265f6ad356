#!/bin/bash

set -e

# NOTE: Using checksums is very slow, so following parameter is not used:
# --checksum=xxh3

BACKUP_PATH="/volume1/Storage/Partyflock"
ARCHIVE_PATH="/volume1/Storage/Partyflock_archive"
RSYNC="rsync"
#RSYNC_BWLIMIT="--bwlimit=1M"
SOURCE_HOST="2001:1540:801:d:1::140"
VERBOSE=
DELETE=
RSYNC_PARAMS=
RSYNC_DRYRUN=
EXCLUDE=
SYMLINKS=
SUBDIR=
COMPRESS=
SOURCES=()
#COMPRESS="--compress --compress-choice=zstd --compress-level=5"

for parameter in "$@"; do
	case $parameter in
	--what=*)
		WHAT="${parameter#*=}"
		case "$WHAT" in
		coldstore)
			SOURCE_PATH=/archive/database/$WHAT
			SOURCES+=("")
			DESTINATION_DIR=.
			SYMLINKS=--links
			;;
		database)
			cd $WHAT
			SOURCE_PATH=/archive/database_latest/
			DESTINATION_DIR=database/
			SYMLINKS=--links
			DELETE=(--delete --delete-before)
			for database in *; do
				if [[ -d "$database" ]]; then
					SOURCES+=("$database/")
				fi
			done
			cd ..
			;;
		database_archive)
			SOURCE_PATH=/archive/database/
			SOURCES+=("")
			DESTINATION_DIR="$ARCHIVE_PATH/database/"
			SYMLINKS=--links
			;;
		gluster)
			cd $WHAT
			SOURCE_PATH=/archive/backups_latest/
			DESTINATION_DIR=gluster/
			SYMLINKS=--links
			DELETE=(--delete --delete-before)
			EXCLUDE=("--exclude=photos_v2_unprocessed")
			for volume in *; do
				if [[ -d "$volume" ]]; then
					SOURCES+=("$volume/")
				fi
			done
			cd ..
			;;
		gluster_archive)
			SOURCE_PATH=/archive/backups/
			SOURCES+=("")
			DESTINATION_DIR="$ARCHIVE_PATH/gluster/"
			SYMLINKS=--links
			;;
		photos_v2_unprocessed)
			SOURCE_PATH=/archive/backups/$WHAT
			SOURCES+=("")
			DESTINATION_DIR=.
			SYMLINKS=--links
			;;
		*)
			echo "what $WHAT not understood, need: coldstore | database | database_archive | gluster| photos_v2_unprocessed" >/dev/stderr
			exit 1
			;;
		esac
		shift
		;;
	--verbose)
		set -x -v
		VERBOSE=--verbose
		echo VERBOSE turned on
		;;
	--debug)
		set -x -v
		echo DEBUG turned on
		RSYNC_PARAMS=(--itemize-changes --info=progress2 --debug=all --progress)
		VERBOSE=--verbose
		shift
		;;
	--dryrun | --dry-run)
		echo going DRY
		RSYNC_DRYRUN=(--dry-run)
		shift
		;;
	*)
		echo "parameter not understood: $parameter" > /dev/stderr
		exit 1
		;;
	esac
done

if [ -z "$WHAT" ]; then
	echo "need what: coldstore | database | gluster | photos_v2_unprocessed" >/dev/stderr
	exit 1
fi

cd "$BACKUP_PATH"

for part in "${SOURCES[@]}"; do
	$RSYNC \
		-e 'ssh -l gethuge -i /volume1/homes/thomas/.ssh/gethuge_ed25519 -p 44422 -oLogLevel=quiet -oUpdateHostKeys=yes -oUserKnownHostsFile=/root/.ssh/known_hosts' \
		$RSYNC_BWLIMIT \
		${RSYNC_PARAMS[@]} \
		${RSYNC_DRYRUN[@]} \
		${DELETE[@]} \
		$SYMLINKS \
		$COMPRESS \
		${EXCLUD[@]} \
		$VERBOSE \
		--times \
		--hard-links \
		--recursive \
		--partial \
		"[$SOURCE_HOST]:$SOURCE_PATH$part" "$DESTINATION_DIR$part"
done
