#!/usr/bin/perl -w

use strict;
use warnings;

use DBI;

# FIXME: We should really fix all these character set problems.
#		 Convert all fields to utf8mb4 and manually by creating new field and copying data with some smart
#		 character set detection so we get both utf8 and latin1 converted to new field all right

#use utf8;
no utf8;
no encoding;
use bytes;
use MIME::Parser;
use MIME::QuotedPrint;
use MIME::Base64;
use HTML::TreeBuilder;
use HTML::FormatText;
use HTML::FormatText::WithLinks;
use HTML::Entities;
use Encode;
use Encode::Encoding;
use Data::Dumper;
use String::Approx;
use String::CRC32;
use Sys::Hostname;
use IPC::Open3;
use Sys::Syslog qw{LOG_DEBUG LOG_INFO LOG_ERR LOG_CRIT LOG_MAIL openlog syslog};
use lib '/home/<USER>/config';
use perlinclude::email;
my $no_utf8 = 0;
my $do_utf8 = 1;

#use Tie::Syslog;
#my $stderr = tie *STDERR, 'Tie::Syslog','mail.warning','flockproc','pid';
#$stderr->ExtendedSTDERR();

my $parser;
my $do_store = ($#ARGV == -1);
my $store_staff = 0;
my $dbh;
my $dbhe;
my $now = time();
my $filehash = 0;
my $filesize = 0;
my $xmailer = '';
my $from = '';
my $from_name = '';
my $from_email = '';
my $notify = '';
my $charset;
my $to;
my $to_name;
my $to_email;
my  $subj;
my  $subj_utf8;
my $lsubj;
my  $replyto;
my $cc;
my $replyto_name;
my $replyto_email;
my $deliveredto;
my $envelope_to;
my  $messageid;
my $references;
my $inreplyto;
my $bodystr;
my $elementid = 0;
my $invalid_from = 0;
my $spamscore = 0;

my $autogen = 0;
my $mlist = '';
my $precedence = 0;
my $ticketid;
my $newticket = 0;
my $ctmsgid;
my $cimid = 0;
my $doneinitial;
my $maybeforward = 0;
my $type = '';
my $forward = '';
my $failure = 0;
my $element;
my $mainhead;		# MIME::Head
my $autoresponse;

sub myend($) {
	my $rc = shift;
	# 0: ok
	# 1: failure
	# 2: silent failure
	if ($rc
	&&	$rc != -1
	) {
		if ($store_staff) {
			if ($do_store) {
#				my $outp;
#				if (open $outp,">/home/<USER>/config/mail/spam_complaints/${now}_$$") {
#					print $outp $mainentity->stringify;
#					close $outp;
#					$rc = 0;
#				}
			}
		}
		if ($rc != 2) {
			syslog(LOG_CRIT, "FAILED!");
		}
	}
	if (defined $parser) {
		$parser->filer->purge;
		my $parser_output_dir = $parser->filer->output_dir;
		if (defined($parser_output_dir)) {
			rmdir($parser_output_dir);
		}
	}
	if ($rc == -1) {
		exit 0;
	}
	if ($do_store) {
		if (!$rc) {
			my $quoted_element = $dbh->quote($element);
			my $quoted_from_email = $dbh->quote($from_email);
			if (!$dbh->do("
				INSERT IGNORE INTO mailhash SET
					HASH	= $filehash,
					LEN		= $filesize,
					ELEMENT	= $quoted_element,
					EMAIL	= $quoted_from_email,
					STAMP	= $now")
			) {
				my $errno = $dbh->{'mysql_errno'};
				if ($errno == 1062) {
					syslog(LOG_INFO,"message ignored, already processed");
				}
			}
		}
		exit ($rc ? 75 : 0);
	}
	exit 75;
}

my $dont_start =
	-e '/offline'
||	-e '/readonly'
||	-e '/nomail';

if (!openlog('flockproc', $do_store ? 'pid' : 'perror', LOG_MAIL)) {
	print STDERR "failed to openlog\n";
	myend(1);
}

# catch STDERR and forward to SYSLOG
open(SYSLOG, "| /usr/bin/logger --skip-empty --stderr --tag flockproc[$$] --priority mail.error") or die("could not open pipe to logger: $!" );

*STDERR = *SYSLOG;

if ($dont_start) {
	syslog(LOG_DEBUG, 'down for maintenance');
	myend(2);
}

if (!$do_store) {
	syslog(LOG_INFO,'not storing');
}

my $localhost = hostname();

$parser = MIME::Parser->new();
$parser->output_under('/tmpdisk');

# FIX ME: use $ENV{'USER'} ?

my $whoami = `whoami`;
chomp($whoami);


if ($localhost eq 'partyflock'
||	$localhost eq 'partyflock.nl'
||	$localhost eq 'preparty'
) {
	my $dsn = 'DBI:mysql:database=party_db;host=ultraparty;mysql_connect_timeout=60';
	$dbh = DBI->connect($dsn,'party', 'FR0Pfrop', {
		PrintError	=> 1,
		PrintWarn	=> 1
	});
	if (!$dbh) {
		syslog(LOG_ERR, 'could not connect to ultraparty.party_db');
		myend(1);
	}
	# NOTE: don't assume anything about the strings we are inserting
	if (!$dbh->do('SET NAMES binary')) {
		syslog(LOG_ERR,"failed to set names to binary");
		myend(1);
	}

#	if (!$dbh->do("SET SESSION wait_timeout=600")) {
#		syslog(LOG_ERR,"could not set wait_timeout for dbh");
#		myend(1);
#	}
	my $dsne = 'DBI:mysql:database=data_db;host=newdata3;mysql_connect_timeout=60;port=3333';
	$dbhe = DBI->connect($dsne,'party','FR0Pfrop',{
		PrintError	=> 1,
		PrintWarn	=> 1
	});
	if (!$dbhe) {
		syslog(LOG_ERR,"could not connect to newdata3.data_db!");
		myend(1);
	}
#	if (!$dbhe->do("SET SESSION wait_timeout=600")) {
#		syslog(LOG_ERR,"could not set wait_timeout for dbhe");
#		myend(1);
#	}
} elsif ($localhost =~ /^sandbox(?:-[a-z]+)?$/) {
	my $dsn = 'DBI:mysql:database=party_db;host=localhost;mysql_connect_timeout=60;mysql_socket=/db/mysql.sock';
	$dbh = DBI->connect($dsn, 'party', 'FR0Pfrop', {
		PrintError	=> 1,
		PrintWarn	=> 1
	});
	if (!$dbh) {
		syslog(LOG_ERR,"could not connect to localhost.party_db");
		myend(1);
	}
	$dbhe = $dbh;
} else {
	syslog(LOG_ERR, "unknown localhost: $localhost");
	myend(1);
}


# MIME::Entity
my $mainentity = $parser->parse(\*STDIN);
if (!$mainentity) {
	syslog(LOG_ERR,"no primary entity!");
	myend(1);
}

process_mail();

sub process_mail {
	$ticketid = 0;
	$ctmsgid = 0;
	$cimid = 0;
	$doneinitial = 0;

	$mainhead = $mainentity->head;
	if (!$mainhead) {
		syslog(LOG_ERR,'no primary head');
		myend(1);
	}

############### mailscanner

	my $mailscanner = $mainhead->get('X-MailScanner-From') || '';
	chomp($mailscanner);
	my $lmailscanner = lc $mailscanner;

############### get References

	$references = $mainhead->get('References') || '';
	chomp($references);

	$inreplyto = $mainhead->get('In-Reply-To') || '';
	chomp($inreplyto);

	if (	$inreplyto  =~ /<?(\d+)\@partyflock\.contest>?/
	||	$references =~ /^<?(\d+)\@partyflock\.contest>?$/

	||	$inreplyto  =~ /(\d+)\@partyflock\.contest/
	||	$references =~ /(\d+)\@partyflock\.contest/
	) {
		$element = 'contest';
		$elementid = $1;
		syslog(LOG_INFO,"connecting to contest:$elementid");
	}

############## get From
	($from_name, $from_email, $from) = get_from($mainhead);
	if ($from) {
		syslog(LOG_INFO,"using from: $from");
	}
	if ($from_email) {
		syslog(LOG_INFO,"using from_email: $from_email");
	}
	if ($from_name) {
		syslog(LOG_INFO,"using from_name: $from_name");
	}

	my $lfrom_email = lc $from_email;

	my $returnpath = $mainhead->get('Return-Path') || '';
	my $lreturnpath = lc $returnpath;
	if (!$from_email) {
		# try return-path
		chomp($returnpath);
		if (	$returnpath
		&&	$returnpath ne '<>'
		) {
			($from_name,$from_email) = parse_from($returnpath, $no_utf8);
		}
		if (!$from_email) {
			# use mailscanner from as last resort
			if (!$mailscanner) {
				syslog(LOG_INFO,"no from line in mail!");
			} else {
				syslog(LOG_INFO,"used x-mailscanner-from as from address: $from");
				($from_name,$from_email) = parse_from($mailscanner, $no_utf8);
			}
		}
	}
	if ($from_name) {
		$from_name =~ s/\\([\(\)])/$1/g;
		syslog(LOG_INFO,"from_name: $from_name");
	}
	if ($from_email) {
		syslog(LOG_INFO,"from_email: $from_email");
	}
	$xmailer = $mainhead->get('X-Mailer') || '';
	chomp($xmailer);

	if (	!$from_email
	||	!is_valid_email($from_email)
	) {
		if ($from_email) {
			syslog(LOG_INFO,"invalid email: $from_email");
		}
		# try X-Sender
		my $xsender = $mainhead->get('X-Sender') || '';
		chomp($xsender);
		if ($xsender) {
			syslog(LOG_INFO,"x-sender: $xsender");
			my ($dummy, $try_email) = parse_from($xsender, $no_utf8);
			if (is_valid_email($try_email)) {
				syslog(LOG_INFO,"using $try_email".($from_email ? " instead of $from_email" : ''));
				$from_email = $try_email;
			}
		}
		if (	!$from_email
		&&	$xmailer
		) {
			syslog(LOG_INFO,"x-mailer: $xmailer");
			my ($dummy,$try_email) = parse_from($xmailer, $no_utf8);
			if (is_valid_email($try_email)) {
				syslog(LOG_INFO,"using $try_email",($from_email ? " instead of $from_email" : ''));
				$from_email = $try_email;
			}
		}
		if (	!$from_email
		||	!is_valid_email($from_email)
		) {
			$invalid_from = 1;
		}
	}

############# get To, determine element

	my $user_part;

	$to = $mainhead->get('To') || '';
	chomp($to);

	syslog(LOG_INFO,"to: $to");
	($to_name,$to_email) = parse_from($to, $no_utf8);
	syslog(LOG_INFO,"to_name: ".($to_name ? $to_name : '(empty)'));
	syslog(LOG_INFO,"to_email: $to_email");

	syslog(LOG_INFO,"iam: $whoami");

	if ($to_email =~ /([\w]+)\@party/) {
		$user_part = $1;
		syslog(LOG_INFO,"userpart of to_email: $user_part");
	}
	$deliveredto = $mainhead->get('Delivered-To') || '';
	chomp($deliveredto);
	syslog(LOG_INFO, $deliveredto ? "delivered-to: $deliveredto" : "no delivered-to header");

	$envelope_to = $mainhead->get('Envelope-to') || '';
	chomp($envelope_to);
	syslog(LOG_INFO, $envelope_to ? "envelope-to: $envelope_to" : "no envelope-to header");

	if (!$element) {
		if ($whoami eq 'root') {
			my $fallback_to;
			if ($envelope_to) {
				syslog(LOG_INFO, "using fallback from envelope-to");
				$fallback_to = $envelope_to;
			} elsif ($deliveredto) {
				syslog(LOG_INFO, "using fallback from develivered-to");
				$fallback_to = $deliveredto;
			}
			if ($fallback_to) {
				if ($fallback_to eq '<EMAIL>'
				||	$fallback_to eq '<EMAIL>'
				) {
					$element = 'office';

				} elsif ($fallback_to =~ /^(.*)\@(.*)$/) {
					$element = getelement($1);
				}
			}
		} else {
			$element = getelement($whoami);
		}
	}
	if (!$element) {
		syslog(LOG_INFO, "chosen default element: helpdesk");
		$element = 'helpdesk';
	}
	syslog(LOG_INFO,"element: $element");

	$precedence = $mainhead->get('Precedence') || '';
	chomp($precedence);
	if ($precedence) {
		syslog(LOG_INFO,"precedence: $precedence");
	}

############# notification

	$notify = $mainhead->get('Disposition-Notification-To') || '';
	chomp($notify);
	if ($notify) {
		syslog(LOG_INFO,"notify: $notify");
	}

#	my $IO;

	my @checkparts = $mainentity->parts;
	if ($#checkparts == -1) {
		$bodystr = $mainentity->bodyhandle->as_string;
	} else {
		$bodystr = '';
		foreach my $part (@checkparts) {
#			print $part->as_string;
			$bodystr .= $part->as_string;

#			$IO = $part->open('r');
#			if ($IO) {
#				$filesize += -s $IO;
#				$filehash += crc32($IO);
#				$IO->close;
#			}
		}
	}
	# use body without urls as hash, because urls might contain unique unsubscribe parameters
	my $nourl_bodystr = $bodystr;
	$nourl_bodystr =~ s!https?://[^\s]+!!gi;
	$filehash = crc32($nourl_bodystr);
	$filesize = length($bodystr);

	syslog(LOG_INFO, "body size: $filesize");
	syslog(LOG_INFO, "body hash: $filehash");

	my $quoted_from_email = $dbh->quote($from_email);
	my $sth = $dbh->prepare("
		SELECT 1
		FROM mailhash
		WHERE HASH	= $filehash
		  AND LEN	= $filesize
		  AND EMAIL = $quoted_from_email"
	);
	if ($sth
	&& 	$sth->execute
	&&	(my $row = $sth->fetchrow_arrayref)
	) {
		syslog(LOG_INFO,"found identical mail already processed!");
		if ($do_store) {
			myend(0);
		} else {
			syslog(LOG_INFO,"processing would normally have stopped here");
		}
	}

############# get Subject

	$subj		= parse_subject($mainhead->get('Subject') || '', $no_utf8);
	$subj_utf8	= parse_subject($mainhead->get('Subject') || '', $do_utf8);
	$subj		=~ s/\n//g;
#	if ($subj =~ /Amelie Lens to host fan Meet & Greet at famed New York  eatery CHLOE & announces new single/) {
#		$subj = 'Amelie Lens to host fan Meet & Greet at famed New York  eatery CHLOE & announces new single \'Falling For You\'';
#		$subj_utf8 = $subj;
#	}
	syslog(LOG_INFO,"subject: $subj");
	if ($subj ne $subj_utf8) {
		syslog(LOG_INFO, "subject_utf8: $subj_utf8");
	}
	$lsubj = lc($subj);

############# get Reply-to

	$replyto = $mainhead->get('Reply-to') || '';
	chomp($replyto);
	if ($replyto) {
		syslog(LOG_INFO, "replyto: $replyto");
		($replyto_name,$replyto_email) = parse_from($replyto, $no_utf8);
		if ($replyto_name) {
			syslog(LOG_INFO, "replyto_name: $replyto_name");
		}
		syslog(LOG_INFO, "replyto_email: $replyto_email");
	} else {
		$replyto_name = '';
		$replyto_email = '';
	}

############### get CC

	$cc = $mainhead->get('CC') || '';
	chomp($cc);
	if ($cc) {
		my $cc_name;
		my $cc_email;
		my @ccs;
		foreach (split /,/,$cc) {
			($cc_name,$cc_email,$cc) = parse_from($_, $do_utf8);
			push @ccs,"$cc_name <$cc_email>";
		}
		$cc = join ',',@ccs;
		syslog(LOG_INFO,"cc: $cc");
	}

############### references to ticket?

	my $is_relation = 0;
	my $is_ok = get_ticketid_from_mail($lsubj, $inreplyto, $references);

############# find email in database, if there, force ok, because it's a contact

	if ((	$from_email
		||	$replyto
		)
	&&	!$is_ok
	&&	!$ticketid
#	&&	$lfrom_email !~ /(?<!thomas)\@partyflock\.nl$/
	) {
		my $sth;
		my $row;
		my $inpart =
			$replyto && $from_email
		?	'IN ('.$dbh->quote($from_email).','.$dbh->quote($replyto).')'
		:	 '='.  $dbh->quote($from_email ? $from_email :  $replyto);

		CONTACTQUERY: foreach my $qstr ((
			"SELECT	'relation',
					GROUP_CONCAT(DISTINCT CONCAT('relation:', RELATIONID) ORDER BY RELATIONID)
			FROM relation
			WHERE CONTACTEMAIL $inpart",

			"SELECT	'relation_and_user',
					GROUP_CONCAT(DISTINCT CONCAT('relation:', relationmember.RELATIONID, ';user:', USERID))
			FROM relationmember
			JOIN user USING (USERID)
			WHERE EMAIL $inpart",

			"SELECT	'user',
					GROUP_CONCAT(DISTINCT CONCAT('user:', USERID))
			FROM user
			WHERE EMAIL $inpart"
		)) {
			if (($sth = $dbh->prepare($qstr))
			&&	$sth->execute
			&&	($row = $sth->fetchrow_arrayref)
			&&	$row->[1]
			) {
				syslog(LOG_INFO, 'sender is known to us: '.$row->[1]);

				if ($row->[0] =~ /^relation/) {
					$is_relation = 1;
				} else {
					$is_ok = 1;
				}
			}
		}
		if (!$is_ok
		&&	!$is_relation
		) {
			syslog(LOG_INFO, 'sender is not known to us');
		}
	}

############# is this autoresponse?

	$autoresponse = parse_subject($mainhead->get('X-Autorespond') || '', $no_utf8);
	if ($autoresponse) {
		syslog(LOG_INFO,"autoresponse: $autoresponse");
	}

############### get Message-ID

	$messageid = $mainhead->get('Message-ID') || '';
	chomp($messageid);
	my $lmessageid = lc $messageid;

	syslog(LOG_INFO, $messageid ? 'messageid: '.$messageid : 'empty messageid');

	if ($precedence eq 'bulk'
	&&	$messageid =~ /^<\d+\.\d+\@partyflock\.ticket>$/
	&&	$from_email =~ /(?<!thomas)\@partyflock\.nl$/
	) {
		syslog(LOG_INFO,'seems to be our own mailing, just skipping');
		myend(0);
	}

############# check for spam complaint

	if (	$store_staff
	=	(	$lfrom_email =~ /^staff\@hotmail\.com/
		&&	$lsubj =~ /^complaint about message from /
		)
	) {
		syslog(LOG_INFO,"prolly spam complaint");
		if (!$mainentity->parts) {
			syslog(LOG_ERR,"could not parse spam complaint");
			myend(1);
		}
		my $attachedmail = ($mainentity->parts)[0];
		if (!$attachedmail) {
			syslog(LOG_ERR,"attachedmail is undef");
			myend(1);
		}
		my $originalmail = ($attachedmail->parts)[0];
		if (!$originalmail) {
			syslog(LOG_ERR,"originalmail is undef");
			myend(1);
		}
		my $original_messageid = $originalmail->head->get('Message-ID');
		chomp($original_messageid);
		my $original_to = $originalmail->head->get('To');
		chomp($original_to);
		my $original_subject = $originalmail->head->get('Subject');
		chomp($original_subject);
		$original_subject = parse_subject($original_subject, $no_utf8);
		if (!$original_messageid) {
			syslog(LOG_ERR,"original Message-ID is empty");
			myend(1);
		}
		if (!$original_to) {
			syslog(LOG_ERR,"original To is empty");
			myend(1);
		}
		syslog(LOG_INFO, "original message-id: $original_messageid");
		syslog(LOG_INFO, "original to: $original_to");
		syslog(LOG_INFO, "original subject: $original_subject");
		syslog(LOG_INFO, "writing complaint mail to complaints folder");
		my $outp;
		if (open $outp, ">/home/<USER>/config/mail/spam_complaints/${now}_$$") {
			print $outp $mainentity->stringify;
			close $outp;
		}

		if ($original_messageid =~ /\@partyflock\.passwordtoken/) {
			# just ignore mail, activation mails are NOT spam
			syslog(LOG_INFO, "ignoring SPAM COMPLAINT for password reset mail");
			myend(0);
		}
		if ($original_messageid =~ /\@partyflock\.activationtoken/) {
			# just ignore mail, activation mails are NOT spam
			syslog(LOG_INFO, "ignoring SPAM COMPLAINT for activation mail");
			myend(0);
		}
		if ($original_messageid =~ /\@partyflock\.expiredfacebook/) {
			syslog(LOG_INFO, "ignoring SPAM COMPLAINT for expided facebook mail");
			myend(0);
		}
		if ($original_messageid =~ /\@partyflock\.contestwin/) {
			syslog(LOG_INFO, "ignoring SPAM COMPLAINT for contest win message");
			myend(0);
		}
		if ($original_messageid =~ /^<?(?<ticketid>\d+)\.(?<ctmsgid>\d+)\@partyflock\.ticket>?$/) {
			syslog(LOG_INFO, "ignoring SPAM COMPLAINT for message in ticket $1 message $2");
			myend(0);
		}
		my $userid;
		# first one below is an old obsolete message-id I believe
		if ($original_messageid =~ 	    /^<?user_(\d+)_(?:.*?)\@partyflock\.(?:notification|(?:in)?activation(?:token)?)>?$/
		||	$original_messageid =~ /^<?user_(\d+)_time_(?:\d+)\@partyflock\.(?:notification|(?:in)?activation(?:token)?)>?$/
		) {
			$userid = $1;
		}
		if (!$userid) {
			syslog(LOG_INFO,"original message id not understood");
			if ($original_subject !~ /Partyflock update/) {
				syslog(LOG_ERR,"cannot work with subject: ".$original_subject);
				myend(1);
			}
			my $body = $originalmail->bodyhandle;
			for ($body->as_lines) {
				if (/(?<link>https?:\/\/partyflock\.nl\/user\/(?<userid>\d+)\b[^\s]+)/) {
					syslog(LOG_INFO, "found link to user profile ".$+{userid}.": ".$+{link});
					$userid = $1;
					last;
				}
			}
			if (!$userid) {
				syslog(LOG_ERR, "not userid found in message");
				myend(1);
			}
		}
		($to_name, $to_email) = parse_from($original_to, $no_utf8);
		my $quoted_to_email = $dbh->quote($to_email);
		my $sth = $dbh->prepare("
			SELECT 1
			FROM notifydays
			JOIN user USING (USERID)
			WHERE USERID = $userid
			  AND EMAIL	 = $quoted_to_email
			  AND NOT (
					ALWAYS	=0
				  AND	ISNULL(`1`)
				  AND	ISNULL(`2`)
				  AND	ISNULL(`4`)
				  AND	ISNULL(`8`)
			)");
		if (!$sth) {
			syslog(LOG_ERR, "notifydays prepare failed");
			myend(1);
		}
		if (!$sth->execute) {
			syslog(LOG_ERR, "notifydays execute failed");
			myend(1);
		}
		my $row = $sth->fetchrow_arrayref;
		if ($row) {
			syslog(LOG_INFO, "turn off notifications for USERID $userid with email $to_email");

			if ($do_store) {
				if (!$dbh->do("
					REPLACE INTO notifydays SET
						USERID	= $userid,
						ALWAYS	= 0,
						`1`		= NULL,
						`2`		= NULL,
						`4`		= NULL,
						`8`		= NULL")
				) {
					syslog(LOG_ERR,"notifydays update query failed: ".$dbh->errstr);
					myend(1);
				}
#				use Mail::Send;
#				my $msg = Mail::Send->new(Subject => 'Partyflock notificaties uitgeschakeld', To => $to_email);
#				$msg->set('Auto-Submitted: auto-generated');
#				$msg->set('From','"Partyflock helpdesk" <<EMAIL>>');
#				my $fh = $msg->open;
#				print $fh
#					"Je zult geen notificaties meer ontvangen op dit email adres omdat je in Windows Live Mail of Hotmail hebt aangeklikt dat deze berichten ongewenst (spam of junk) zijn!\n\n".
#					"Op onderstaande pagina kun je ze indien gewenst weer aanzetten, maar zorg er voor dat je ze dan niet weer als ongewenst bestempeld.\n\n";
#				print $fh "http://partyflock.nl/user/$userid/settings.html";
#				$fh->close;
			}
		} else {
			syslog(LOG_INFO,"already turned off notifications or email address not equal for USERID $userid with email $to_email");
		}
		myend(0);
	}

########### detect autogen

	if ($lsubj =~ /^(?:Out of Office(?: AutoReply)?|Vacation reply|(?:automatisch )?antwoord bij afwezigheid|Ontvangstbevestiging|Niet aanwezig:|.*? automatic reply)/i
	||	$lsubj =~ /(is out of the office\.?)$/i
	||	$lsubj =~ /Afwezig \/ Out of Office/i
	||	$lsubj =~ /^Automatisch antwoord:/i
	||	$lsubj =~ /^AUTO\-REPLY -/i
	) {
		syslog(LOG_INFO,"autogenerated reply according to subject: $subj");
		$autogen = 1;
	} elsif ($autoresponse ne '') {
		syslog(LOG_INFO,"autogenerated reply according to X-Autorepond header: $autoresponse");
		$autogen = 1;
	} else {
		my $autotest = $mainhead->get('Auto-Submitted') || '';
		chomp($autotest);
		if (	$autotest
		&&	$autotest =~ /auto\-(?:replied|generated)/i
		&&	(	$xmailer ne 'Zendesk Mailer'
			||	$bodystr =~ /^Content-Type: .*?\n+\h*##\- .*? \-##\n+Uw aanvraag \(\d+\) is ontvangen/
			)
		) {
			syslog(LOG_INFO,"autogenerated reply according to Auto-Submitted header: $autotest");
			$autogen = 1;
		} else {
			$autotest = $mainhead->get('X-Autogenerated') || '';
			chomp($autotest);
			if (	$autotest
			&&	$autotest =~ /reply/i
			) {
				syslog(LOG_INFO,"autogenerated reply according to X-Autogenerated header: $autotest");
				$autogen = 1;
			} else {
				$autotest = $mainhead->get('X-Autoreply') || '';
				chomp($autotest);
				if (	$autotest
				&&	$autotest =~ /yes/i
				) {
					syslog(LOG_INFO,"autogenerated reply according to X-Autoreply header: $autotest");
					$autogen = 1;
				}
			}
		}
	}

	$mlist = $mainhead->get('List-Unsubscribe') || '';
	if ($mlist) {
		if ($mlist =~ /^<(.*)>$/) {
			$mlist = $1;
		}
		syslog(LOG_INFO,"mailing-list message according to List-Unsubscribe: $mlist");
	}

############# check for spam from known hosts
	my $spamlevel = $mainhead->get('X-Spam-Level') || '';
	my @spamscores = $mainhead->get('X-Spam-Score');
	if ($#spamscores == -1) {
		$spamscore = 0;
	} else {
		for (my $i = $#spamscores; $i >= 0; --$i) {
			$spamscore = $spamscores[$#spamscores];
			if ($spamscore =~ /^\d+$/) {
				last;
			}
		}
		if ($spamscore !~ /^\d+$/) {
			$spamscore = 0;
		}
	}

	if (!$ticketid
	&&	!$is_relation
	) {
		if (!$is_ok) {
			# NOTE: since 2014-01-01:
			#	no nospam entries with score >= 50
			#	no tickets in non junk folders with score >= 60, except this one: 1280741 (TheNewMotion)
			#	no tickets in non junk folders with score >= 50, except this one: 1308895 (Darcher media)
			#	no tickets in non junk folders with score >= 40
			if ($spamscore >= 40) {
				my $spamcheck = $mainhead->get('X-Spam-Status');
				my $score;
				$score= ($spamcheck =~ /(score=\d+.\d+)/) ? $1 : 'score unknown';
				syslog(LOG_INFO,"SPAM detected by scanner ($score)");
				myend(-1);
			}
		}
		my $lsender = $mainhead->get('Sender') || '';
		chomp($lsender);
		$lsender = lc $lsender;
		my $lreplyto = lc $replyto;
		if (	$lmailscanner =~ /(admin\@sweetbabes\.nl|funkybabes@(?:.*)\.funkybabes\.nl|.*\@.*xseno\.nl)/
		||	$lmailscanner =~ /(\@post\.lovehappens\.com|invite\@birthdayalarm\.com|\@(?:mail\.)?whereareyounow\.net|\@noreply\.bebo\.com)/
		||	$lmailscanner =~ /(\@friendster\.com|\@getitfree\.net|\@drugscartel\.nl|\@[a-z]+\.facebox\.com|info\@funkybabes\.nl)/
		||	$lmailscanner =~ /(\@mail-relay-outgoing\.prod\.imvu\.com|invite\@facebook\.com)/
		||	$lmailscanner =~ /bounce\@(taggedmail\.com)/
		||	$lsubj =~ /you have unread messages from|added you as a friend/
		&&	$lfrom_email =~ /info\@whereareyounow\.(?:com|net)/
		&&	$lmessageid =~ /\@(whereareyounow\.(?:com|net))/
		||	$lreplyto =~ /bounce\@(flixter\.com)/
		||	$lfrom_email =~ /noreply\@(zuzu\.nl)/
		||	$lfrom_email =~ /noreply\@(?:[a-z]+\.)?(skype\.com)/
		||	$lfrom_email =~ /geenantwoord\@(hyves)\.nl/
		||	$lsubj =~ /yo+ ladies\/guys|uitnodiging van|meine einladung|let\'s hyve|uitnodiging vervalt bijna|join my hyve|invitation is about to expire|nodigt je uit voor|zit jij al bij hyves|Je bent uitgenodigd/
		&&	(	$lmessageid =~ /\@mail([^\.]*)\.(hyves\.(?:org|net|nl))/
			||	$lreturnpath =~ /\@(hyves\.nl)/
			||	$lfrom_email =~ /\@(hyves\.nl)/
			)
		||	$lsubj =~ /je bent uitgenodigd door/
		&&	$lmessageid =~ /\@\w+\.(pp2g\.lan)/
		||	$lsubj =~ /invitation/
		&&	$lfrom_email =~ /\@invitation\.(sms\.ac)/
		||	$lsubj =~ /ive a new mail address/
		&&	$lfrom_email =~ /(smscity\.nl)/
		||	$lsubj =~ /nodigt je uit/
		&&	$lmessageid =~ /\@www\.(hotsms\.com)/
		||	$lsubj =~ /via wayn/
		&&	$lmessageid =~ /\@(whereareyounow\.com)/
		||	$lsubj =~ /linkedin/
		&&	$lsender =~ /\@[a-z]+\.(linkedin\.com)/
		||	$lfrom_email =~ /invitations\@(linkedin\.com)/
		||	$lfrom_email =~ /noreply\@(lnm.eu)/
#		||	$lsubj =~ / facebook /
#		&&	$lmessageid =~ /@(?:[a-z]+\.)?(facebook\.com)/
		||	$lfrom_email =~ /info\@myflavour\.nl/
		&&	$lsubj =~ /myflavour/
		&&	$lmessageid =~ /\@www\.(myflavour\.nl)/
		||	$lfrom_email =~ /info\@hi5\.com/
		&&	$lsubj =~ /heeft een (hi5)\-vriendenverzoek naar je verzonden/
		||	$lsubj =~ /nodigt je uit met een swingtone/
		&&	$lmessageid =~ /\@(freeringtone4u)/
		||	$lsubj =~ /invites you to join zorpia/
		&&	$lmessageid =~ /\@(zorpia)\.com/
		||	$lsubj =~ /our invitation from .+ is about to expire/
		&&	$lfrom_email =~ /(jaxtr)\@jaxtr\.com/
		||	$lsubj =~ /videoboodschap|video\s*mailen/
		&&	$lmessageid =~ /\@[\w\d]+\.(special\-tones)\.nl/
		||	$lfrom_email =~ /noreply\@(netlog)mail\.com/
		||	$lfrom_email =~ /noreply\@(dg2)\.nl/
		||	$lfrom_email =~ /invite2messenger\@microsoft\.com/
		||	$lfrom_email =~ /(gerallio\@orange)\.nl/
		||	$lsubj =~ /nodigt je uit op MySpace/
		&&	$lfrom_email =~ /\@message\.(myspace\.com)/
		||	$lreplyto =~ /no\-reply\@(uitnodigen\.net)/
		||	$lfrom_email =~ /postcards\@(hallmark\.com)/
		||	$lreplyto =~ /noreply\@mail\.(reunion\.com)/
		||	$lfrom_email =~ /no\-reply\@(blogeiland\.nl)/
		||	$lfrom_email =~ /no\-reply\@(meetyourmessenger)\.com/
		||	$lfrom_email =~ /invite\+.*?\@(facebookmail)\.com/
		||	$lfrom_email =~ /invite\@(youlog)\.com/
		||	$lsubj =~ /nodigt je uit/
		&&	$from_name =~ /(MySpace)/i
		||	$lfrom_email =~ /info\@(hi5)\.com/
		||	$lmessageid =~ /\@([^\.]+)\.(waarbenjij\.nu)/
		||	$lfrom_email =~ /noreply\@(badoo\.com)/
#		||	$lsubj =~ /^Automatisch antwoord bij afwezigheid/
		||	$lsubj =~ /word lid van het netwerk/
		&&	$from_name =~ /(Windows Live)/i
		||	(	$lsubj =~ /invite to (SoundCloud)/
			||	$precedence eq 'bulk'
			&&	$mlist ne ''
			)
		&&	$lfrom_email =~ /notifications\@(soundcloud(?:mail|invite)\.com)/
		||	$mlist =~ /profile.live.com\/options\/notifications/
		&&	$lsubj =~ /wants to be your friend on (Windows Live)/
		||	$lsubj =~ /topo presents/
		&&	$lfrom_email eq '(djt0p0@yahoo\.com)'
		||	$from_email =~ /\@(?:vip\.\d+\.com|factoryoversea\.xicp\.net)$/
		||	$from_email =~ /\@tg\.com$/

		||	$lsubj =~ /meer dan duizend inbraken per dag/
		||	$lsubj =~ /u mist zo geen enkel telefoongesprek/
		||	$lsubj =~ /sla de wachtrij over en/
		||	$lsubj =~ /aanpak van late betalers/
		||	$lsubj =~ /nieuw energiecontract zakelijk/
		||	$lsubj =~ /de perfecte aanvulling op uw verkoop/
		||	$lsubj =~ /maak gebruik van een online marketing ass/
		||	$lsubj =~ /isoleer je spouwmuren/
		||	$lsubj =~ /beveilig uw huis en voorkom/
		||	$lsubj =~ /geef uw relaties een uniek cadeau/
		||	$lsubj =~ /maak je woning energiezuinig/
		||	$lsubj =~ /verras uw klanten met exclusieve/
		||	$lsubj =~ /betaalbare liften voor de trap/
		||	$lsubj =~ /wat dacht u van investeren/
		||	$lsubj =~ /website op maat ontwikkeld/
		||	$lsubj =~ /heb je een website, maar geen of/
		||	$lsubj =~ /profiteer van een frankeringsmachine/
		||	$lsubj =~ /exclusieve wijn met een geperson/
		||	$lsubj =~ /we raden fruitmanden aan/
		||	$lsubj =~ /automatische verwerking van korting/
		||	$lsubj =~ /voorkom vochtproblemen/
		||	$lsubj =~ /geniet van een volledige aangepaste website/
		||	$lsubj =~ /easy to attach clip/
		||	$lsubj =~ /verbeter uw bedrijfsvoering met een op maat/
		||	$lfrom_email eq '<EMAIL>'
		||	$from_name =~ /( TW)$/
		&&	(	$spamscore >= 20
			||	$precedence == 'bulk'
			)
		) {
			syslog(LOG_INFO,"SPAM detected: $1");
			myend(-1);
		}
		# checking exotic headers
		my $lxdsb = $mainhead->get('XDSB-Id') || '';
		chomp($lxdsb);
		$lxdsb = lc $lxdsb;
		if (	$lxdsb
		&&	$lxdsb =~ /plaxoupdaterequest/
		) {
			syslog(LOG_INFO,"SPAM detected, PlaxoUpdateRequest");
			myend(-1);
		}
		if ($lsubj =~ /^hey /) {
			my $loldreturnpath = lc($mainhead->get('Old-Return-Path')) || '';
			chomp($loldreturnpath);
			if (	$loldreturnpath
			&&	$loldreturnpath =~ /info\@hi5\.com/
			) {
				syslog(LOG_INFO,"SPAM detected, hi5.com");
				myend(-1);
			}
		}
	}

################ check for non-delivery

#	my $autogen = lc($mainentity->head->get('X-Autogenerated')) || '';
#	chomp($autogen);
	my $lctype = $mainentity->head->get('Content-Type') || '';
	$lctype = lc($lctype);
	$failure = is_undeliverable($lsubj,$lfrom_email,$lmailscanner,$lctype);
	if ($failure) {# || $autogen) {
		my $failmail = '';
		my $failreason = '';
		my $maybe_reason = '';
#		if (!$autogen) {
			syslog(LOG_INFO,"failure code: $failure");
			# check for secondary subject
			my $bodypart = $mainentity->stringify_body;
			my $newsubj;
			my $newmsgid;
			if ($bodypart =~ /Subject:\s*(.*)/m) {
				$newsubj = parse_subject($1, $no_utf8);
			}
			if ($bodypart =~ /Message-ID:\s*(.*\@partyflock\.ticket)/m) {
				$newmsgid = $1;
			}
			if (	$newsubj
			||	$newmsgid
			) {
				my $haveticketid = get_ticketid_from_mail($newsubj, $newmsgid);
				if ($haveticketid) {
					obtainticket($haveticketid);
					syslog(LOG_INFO,"found failure for TICKETID: $ticketid");
				}
			}


			# PARTS
			if ($mainentity->parts) {
				my $useto = 0;
				my @checkparts = ($mainentity->parts);
				FAILPART: for my $part (@checkparts) {
#					print "-----------------------\n";
#					print "PART: ",$part->stringify,"\n";
#					print "=========================\n";
					$lctype = lc($part->effective_type);
					# continue parsing message/delivery-status even when failmail and failreason found
					if (	$failmail
					&&	$failreason
					&&	$lctype ne 'message/delivery-status'
					) {
						next;
					}

					if (!$failreason) {
						if ($part->stringify =~ /^[\-\<] 550 ([\w\d\h]+)/) {
							$maybe_reason = $1;
						}
					}
					syslog(LOG_INFO,"undeliverable ctype: $lctype");
					if (	$useto
					||	$lctype eq 'text/rfc822-headers'
					) {
						if ($part->stringify =~ m/^\s*To:.*?([^@<\s]+)@([^>\s\t\r\n\f]+)/m) {
							$failmail = $1.'@'.$2;
						}
						last FAILPART;
					}
					my $body = $part->bodyhandle;
					if (!$body) {
						if (!$failmail) {
			 				if ($part->stringify =~ m/^\s*To:.*?([^@<\s]+)@([^>\s\t\r\n\f]+)/m) {
								$failmail = $1.'@'.$2;
							}
						}
						if ($part->parts) {
							push @checkparts,$part->parts;
						}
						next FAILPART;
					}
					$lctype = lc($part->effective_type);

					if (	$failreason
					&&	!$failmail
					) {
						for ($body->as_lines) {
							if (/^\s*To: .*?<?(.*@.*)>?/) {
								$failmail = $1;
								last;
							}
						}
					}
					if (
						$failure == 9
					&&	$lctype eq 'text/plain'
					) {
						syslog(LOG_INFO,"undeliverable newtype 8");
						FAILLINE: for ($body->as_lines) {
							if (!$failmail) {
								if (/RCPT TO:<(.*)>/) {
									$failmail = $1;
								}
							} elsif (/550 <$failmail>: (.*)/) {
								$failreason = $1;
								last FAILLINE;
							}
						}
					} elsif (
						$failure == 3
					&&	$lctype eq 'text/plain'
					) {
						syslog(LOG_INFO,"undeliverable newtype 2");
						my $nextisinfo = 0;
						FAILLINE: for ($body->as_lines) {
							if ($nextisinfo == 1) {
								if (/\s*(.*)@(.*)\s+\((.*)\)/) {
									$failmail = $1.'@'.$2;
									$failreason = $3;
									last FAILLINE;
								}
								if( /\s*(.*)\s+\((.*)\)/) {
									$failmail = lc($1);
									$failreason = $2;
									if ($lfrom_email =~ /\@(?:.*?)([a-z\-]+\.[a-z]{2,3})$/) {
										$failmail .= '@'.$1;
									}
									last FAILLINE;
								}
							} elsif (/The message that you sent was undelive/) {
								$nextisinfo = 1;
							}
						}
					} elsif (
						$lfrom_email =~ /\@safe-mail.net$/
					) {
						syslog(LOG_INFO,"undeliverable type safe-mail");
						FAILLINE: for ($body->as_lines) {
							chomp;
							if (/^Reason: (.*)/) {
								$failreason = $1;
								next;
							}
							if (/sent by you to (.*?\@[^\s\n]*)/) {
								$failmail = $1;
								next;
							}
						}

					} elsif (
						$failure == 7
					||	$failure == 10
					||	$failure == 13
					||	$lctype =~ /message\/(?:x?delivery\-status|disposition\-notification)/
					||	$lsubj =~ /^undelivered mail: user unknown/
					||	$lsubj =~ /^delivery failure \(/
					||	$lsubj =~ /^message delayed \(/
					||	$lsubj =~ /notification_d(.*)tat_de_la_distribution/
					) {
						syslog(LOG_INFO,"undeliverable type 1");
						my $action = '';
						my $status = '';
						my $trynext = 0;
						my $userpart = '';
						my $domainpart = '';
						FAILLINE: for ($body->as_lines) {
							chomp;
							if ($trynext) {
								if (/^(?:[\s\t]+)(.+)/) {
									my $maybereason = $1;
									if ($maybereason !~ /^RCPT TO:</) {
										$failreason .= ' '.$maybereason;
										$trynext = 0;
									}
									next;
								}
								$trynext = 0;
							}
							if (/^Reporting-MTA: dns; (.*)/) {
								$domainpart = $1;
								next;
							}
							if (/^Final\-Recipient: rfc822;\s*<?([^<>\s\t\n\r\f]+)/i) {
								$failmail = $1;
								next;
							}
							if (/^Diagnostic\-Code: (smtp|X-(?:Unix|Notes|Postfix|Local));[ ]?([^\n\r\f]*)/i) {
								if (	lc $1 ne 'x-unix'
								||	$2 !~ /^(\d+)$/
								) {
									$failreason = $2;
								}
								$trynext = 1;
								next;
							}
							if (/^Diagnostic\-Code: (.*?) said: ([^\n\r\f]+)/i) {
								$failreason = $2;
								# do we have failmail?
								if ($failmail eq '') {
									$useto = 1;
								}
								next;
							}
							if (/^Action: (.+)$/i) {
								$action = $1;
								$action =~ s/([\s\t\n\r\f]+)$//;
								next;
							}
							if (/^Status: (.+)$/i) {
								$status = $1;
								$status =~ s/([\s\t\n\r\f]+)$//;
								next;
							}
							if (/^([^@]+@[a-z\.]+) \((.+?)\)/) {
								$failmail = $1;
								$failreason = $2;
								last;
							}
							if (/^\<(.*@.*)\>/) {
								$failmail = $1;
								next;
							}
							if ($failmail eq '') {
								if (/^Original\-Recipient:\s*(?:rfc822;)?<?([^@]+?@[^>\s]+)>?/i) {
									$failmail = $1;
								} elsif (/^Original\-Recipient:\s*([^@]+)$/i) {
									if ($domainpart) {
										$failmail = $1.'@'.$domainpart;
										$domainpart = '';
									} else {
										$userpart = $1;
									}
								}
							}
						}
						if ($userpart && $domainpart) {
							$failmail = $userpart.'@'.$domainpart;
						}
						if ($failreason eq '') {
							if ($action ne '') {
								$failreason .= 'Action: '.$action;
							}
							if ($status ne '') {
								if ($failreason ne '') {
									$failreason .= ', ';
								}
								$failreason .= 'Status: '.$status;
							}
						}
					} elsif (
						$lsubj =~ /^notice: mail delivery status/
					||	$lsubj =~ /^returned mail: see transcript/
					||	$lsubj =~ /^warning: delayed mail/
					) {
						syslog(LOG_INFO,"undeliverable type 2");
						my $secfailmail = '';
						FAILLINE: for ($body->as_lines) {
							if (/^>>> RCPT TO:([\s]*)<(.*?)>/i) {
								$failmail = $2;
							} elsif (/^<<< (.*)/) {
								$failreason = $1;
								if (!$failmail && $secfailmail) {
									$failmail = $secfailmail;
									last FAILPART;
								}
							} elsif (/^<(.*?)>:?/) {
								$secfailmail = $1;
							} elsif (/\(reason: (.*)\)/i) {
								if (!$failmail && $secfailmail) {
									$failmail = $secfailmail;
								}
								$failreason = $1;
							} elsif (/(Quota exceeded)/i) {
								$failreason = $1;
							}
						}
					} elsif (
						$failure == 12
					||	$lsubj =~ /^mail system error - returned mail/
					) {
						syslog(LOG_INFO,"undeliverable type 3");
						my $linecnt = 0;
						FAILLINE: for my $line ($body->as_lines) {
							$line =~ s/[\r\n]+//;
							if ($linecnt) {
								if (!$failreason) {
									syslog(LOG_INFO,"got reason: $line");
									$failreason = $line;
								} elsif ($linecnt >= 2) {
									if ($line =~ /^<(.*?)\@(.*?)>/) {
										$failmail = $1.'@'.$2;
										last FAILPART;
									}
								} elsif ($linecnt >= 4) {
									last FAILPART;
								}
								++$linecnt;
							} elsif ($line =~ /following reason/i) {
								$linecnt = 1;
							}
						}
					} elsif ($lsubj =~ /^undelivered mail returned to sender/i) {
						syslog(LOG_INFO,"undeliverable type 4");
						my $morefail = 0;
						FAILLINE: for ($body->as_lines) {
							if (/^<(.+?)@(.+?)>: (.*?)said: ([^\r\n\f]*)/i) {
								$failmail = $1.'@'.$2;
								$failreason = $4;
								$morefail = 1;
							} elsif ($morefail) {
								if (/^([\s\t\r\n\f]*)$/) {
									last FAILPART;
								} else {
									$_ =~ s/^\s+/ /;
									$_ =~ s/([\s\t\r\n\f]+)$//;
									$failreason .= $_;
								}
							}
						}
					} elsif ($lsubj =~ /^delivery notification: delivery has failed/i) {
						syslog(LOG_INFO,"undeliverable type 5");
						FAILLINE: for ($body->as_lines) {
							if (/(?:Original|Recipient) address: (.*?)@([^\s\t\r\n\f]*)/i) {
								$failmail = $1.'@'.$2;
							} elsif (/Reason: ([^\r\n\f]*)/i) {
								$failreason = $1;
								last FAILPART;
							}
						}
					} elsif (
						$lsubj =~ /^undeliverable:/i
					||	$lsubj =~ /^onbestelbaar:/i
					) {
						syslog(LOG_INFO,"undeliverable type 6");
						FAILLINE: for ($body->as_lines) {
							if (/\s+To:\s+(.*?)@([^\s\t\r\n\f]*)/i) {
								$failmail = $1.'@'.$2;
							} elsif (/MSEXCH:.+?\([\d\w]+\) ([^\r\n\f]*)/i) {
								$failreason = $1;
								last FAILPART;
							}
						}
					} elsif ($subj =~ /^Warning: ([^\s]*) - User unknown/i) {
						syslog(LOG_INFO,"undeliverable type 7");
						$failreason = 'User unknown';
						$failmail = $1;
					} elsif ($lsubj =~ /^undeliverable mail/) {
						syslog(LOG_INFO,"undeliverable type 8");
						FAILLINE: for ($body->as_lines) {
							if (/User mailbox exceeds allowed size: ([^\r\n]+)/) {
								$failreason = 'User mailbox exceeds allowed size';
								$failmail = $1;
								last FAILPART;
							}
							if (/Final error mail: (mail can not be delivered) to the following recipients : <(.*)>/) {
								$failreason = $1;
								$failmail = $2;
								last FAILPART;
							}
						}
					} elsif ($failure == 11) {
						syslog(LOG_INFO,"undeliverable type 10");
						FAILLINE: for ($body->as_lines) {
							if ($failmail eq '') {
								if (/(?:Failed address|To): (.*)/) {
									$failmail = $1;
								}
							} elsif (/550 (.*)(?: <$failmail>)?/) {
								$failreason = $1;
								last FAILPART;
							}
						}
					} elsif ($failure == 14) {
						syslog(LOG_INFO,"undeliverable type 11");
						$lsubj =~ /^returned mail: unreachable recipients:(.+)\@([^\s]+)/;
						$failmail = $1.'@'.$2;
						FAILLINE: for ($body->as_lines) {
							if (/likely reason for failure: (.*)/i) {
								$failreason = $1;
								last FAILLINE;
							}
						}
					} elsif ($failure == 1) {
						my $nextisreason = 0;
						my $scanforTo = 0;
						for ($body->as_lines) {
							if ($nextisreason) {
								$failreason = $_;
								last;
							}
							if (/Recipient: <(.+@.+)>/) {
								$failmail = $1;
							} elsif (/Reason:\s*\d+\s\d\.\d\.\d <.*>... (.*)/) {
								$failreason = $1;
							} elsif (/^\<(.+\@.+)\>:?/) {
								$nextisreason = 1;
								if ($1 !~ /^postmaster@/) {
									$failmail = $1;
								}
							}
						}
					}
				}
			} else {
				my $body = $mainentity->bodyhandle;
				if (!$body) {
					syslog(LOG_INFO,"no body for qmail error message!");
					myend(1);
				}
				if ($failure == 2) {
					syslog(LOG_INFO,"undeliverable newtype 1");
					my $state = 0;
					FAILLINE: for ($body->as_lines) {
						if (!$state) {
							if (/Een bericht dat u/) {
								$state = 1;
							}
						} elsif ($state == 1) {
 							$failmail = $_;
							$state = 2;
						} elsif ($state == 2) {
							if (/De foutmelding was:/) {
								$state = 3;
							}
						} elsif ($state == 3) {
							$failreason = $_;
							last FAILLINE;
						}
					}
				} elsif ($failure == 4) {
					syslog(LOG_INFO,"undeliverable newtype 3");
					my $state = 0;
					FAILLINE: for ($body->as_lines) {
						if (!$state) {
							if (/([^\s]*)\@([^\s]*)/) {
								$failmail = $1.'@'.$2;
								$state = 1;
							}
						} else {
							$failreason = $_;
							$failreason =~ s/^\s+//;
							last FAILLINE;
						}
					}
				} elsif ($failure == 5) {
					syslog(LOG_INFO,"undeliverable newtype 4");
					$failmail = $mainhead->get('X-Loop') || '';
					chomp($failmail);
					$failreason = 'Redirected by email';
				} elsif ($failure == 6) {
					syslog(LOG_INFO,"undeliverable newtype 5");
					FAILLINE: for ($body->as_lines) {
						if (/bij de ontvanger\(s\): (.*)\@(.*)/) {
							$failmail = $1.'@'.$2;
							$failreason = 'veel mogelijke redenen';
						}
					}
				} elsif ($failure == 8) {
					syslog(LOG_INFO,"undeliverable newtype 6");
					my $state = 0;
					FAILLINE: for ($body->as_lines) {
						if ($state == 0) {
							if (/^Een bericht dat je hebt verzonden kon niet afgeleverd worden/) {
								$state = 1;
							}
						} elsif ($state == 1) {
							if (/([^\s]+)\@([^\s]+)/) {
								$failmail = $1.'@'.$2;
								$state = 2
							}
						} elsif ($state == 2) {
							if (/^De foutmelding was/) {
								$state = 3;
							}
						} elsif ($state == 3) {
							if (/^\s*(.*)/) {
								$failreason = $1;
							}
							last FAILLINE;
						}
				}
				} elsif ($lsubj =~ /^failure (?:notice|delivery)/) {
					syslog(LOG_INFO,"undeliverable type A");
					my $nextisreason = 0;
					FAILLINE: for ($body->as_lines) {
						if ($nextisreason == 2) {
							if (/^\/\//) {
								$failreason .= $_;
							}
							last FAILLINE;
						} elsif ($nextisreason == 1) {
							if (/^\d+\.\d+\/\d+\.\d+/) {
								next FAILLINE;
							}
							if ($failreason) {
								$failreason .= $_;
							} else {
								$failreason = $_;
							}
							$nextisreason = 2;
							next FAILLINE;
						} elsif (/^<(.*?)\@(.*?)>:/) {
							$failmail = $1.'@'.$2;
							$nextisreason = 1;
						} elsif (/<((?:.*?)@(?:.*?))> because (.*)/) {
							$failmail = $1;
							$failreason = $2;

						} elsif (/Unable to deliver.*?(\S+\@\S+)/) {
							$failmail = $1;
						} elsif (/Remote host said: (.*)/) {
							$failreason = $1;
							$nextisreason  = 1;
						}
					}
				} elsif ($lsubj =~ /^mail delivery failure/) {
					syslog(LOG_INFO,"undeliverable type B");
					my $nextisreason = 0;
					FAILLINE: for my $line ($body->as_lines) {
						if ($nextisreason) {
							$failreason = $line;
							last;
						} elsif ($line =~ /addressed to \'(.*?)@(.*?)\'/) {
							$failmail = $1.'@'.$2;
							$nextisreason = 1;
						} elsif ($line =~ /550 .* <(.*@.*)> (.*)/) {
							$failmail = $1;
							$failreason = $2;
							last;
						}
					}
				} elsif (
					$lsubj =~ /^mail niet afgeleverd\s*:\s*retour afzender/
				||	$lsubj =~ /mail delivery failed\s*: returning message to sender/
				||	$lsubj =~ /^warning: message [^ ]+ delayed/
				) {
					syslog(LOG_INFO,"undeliverable type D");
					FAILLINE: for ($body->as_lines) {
						if (!$failmail) {
							if (/^\s+(.*@.*)\s*$/) {
								$failmail = $1;
								next;
							}
						} elsif (
							/^\s*\(generated from/i
						||	/^\s*SMTP error from/
						) {
							next;
						} elsif (/^[\s\n\r\f\t]+$/s) {
							last;
						} elsif (
							/^\s*host.*?:\s*5\d\d(?:[\-\s]\d\.\d\.\d\s+)?\s*(.*)$/i
						||	/^\s*(?:5\d\d[\-\s]\d\.\d\.\d\s+)?(.*?)\s*$/
						) {
							my $extraline = $1;
							$extraline =~ s/^\s+//;
							$extraline =~ s/\s+$//;
							if ($failreason) {
								$failreason .= " ".$extraline;
							} else {
								$failreason = $extraline;
							}
						}
					}
				} elsif ($lsubj =~ /^dbmail: delivery failure/) {
					syslog(LOG_INFO,"undeliverable type E");
					FAILLINE: for ($body->as_lines) {
						if (/^\*\*\* ([^\*]+) \*\*\*/) {
							$failreason = $1;
						} elsif (/To: ([^@]+)@([^\s\n\r\t]+)/) {
							$failmail = $1.'@'.$2;
							last FAILLINE;
						}
					}
				} elsif ($lsubj =~ /^delivery failure/) {
					syslog(LOG_INFO,"undeliverable type F");
					FAILLINE: for ($body->as_lines) {
						if (/Mailbox of user (.*) is FULL/i) {
							$failreason = 'Mailbox is vol';
							$failmail = $1;
							last FAILLINE;
						}
						if (/The message you sent to ([a-z\-_\.]+)\/([^\s]*)/) {
							$failmail = $2.'@'.$1;
						}
						if (/(it would exceed the quota for the mailbox)/) {
							$failreason = $1;
							if ($failmail) {
								last FAILLINE;
							}
						}
						if (/failed recipient: (.*@.*)/i) {
							$failmail = $1;
							next;
						}
						if (/reason: (.*)/i) {
							$failreason = $1;
						}
					}
				} elsif ($lsubj =~ /^delivery unsuccessful:/) {
					syslog(LOG_INFO,"undeliverable type G");
					FAILLINE: for ($body->as_lines) {
						if (/^Delivery unsuccessful: (.*)/) {
							$failreason = $1;
						} elsif (/^<(.*)>$/) {
							$failmail = $1;
							last FAILLINE;
						}
					}
				} elsif ($lsubj =~ /^message delivery delay/) {
					syslog(LOG_INFO,"undeliverable type H");
					FAILLINE: for ($body->as_lines) {
						if (/^Reason: (.*)/) {
							$failreason = $1;
						} elsif (/^To: .*?<?([^\s]*)@([^>]*)>?/) {
							$failmail = $1.'@'.$2;
							last FAILLINE;
						}
					}
				} elsif ($lsubj =~ /^message delivery failure/) {
					syslog(LOG_INFO,"undeliverable type I");
					FAILLINE: for ($body->as_lines) {
						if (/\[SMTP:(.*)@(.*)\]: (.*)/) {
							$failmail = $1.'@'.$2;
							$failreason = $3;
							last FAILLINE;
						} elsif (/Error was: (.*)/) {
							$failreason = $1;
						} elsif (/To: (.*)@(.*)/) {
							my $user = $1;
							my $host = $2;
							if ($user !~ /^partyflock\./) {
								$failmail = $user.'@'.$host;
							}
						}
					}
				} elsif ($lsubj =~ /^mail delivery failed/) {
					syslog(LOG_INFO,"undeliverable type J");
					FAILLINE: for ($body->as_lines) {
						if (/<(.*)@(.*)>: (?:.*) <(?:.*)@(?:.*)>... (.*)/) {
							$failmail = $1.'@'.$2;
							$failreason = $3;
							last FAILLINE;
						}
						if (/<(.*)@(.*)>: \d+ \d\.\d\.\d (.*)/) {
							$failmail = $1.'@'.$2;
							$failreason = $3;
						} elsif (/\s*Unrouteable address/) {
							$failreason = 'Unrouteable address';
						}
					}
				} else {
					syslog(LOG_INFO,"just find undeliverable info");
					my $nextisinfo = 0;
					FAILLINE: for ($body->as_lines) {
						if ($nextisinfo) {
							if (/^(.*@.*) \- (.*)/) {
								$failreason = $2;
								$failmail = $1;
								last;
							}
						}
						if (!$failmail) {
							if (/<(.*\@.*)>/) {
								$failmail = $1;
							} elsif (/no message body: (.*@.*)/i) {
								$failmail = $1;
							} elsif (/^\s*(.*\@.*)\s*$/) {
								$failmail = $1;
							}
						}
						if (	/\(reason: (.*)\)/
						||	/(user not found)/
						||	/(Your email looks like an auto.*)/
						) {
							$failreason = $1;
						}
						if (/^To:\-/) {
							$nextisinfo = 1;
							next;
						}
						if (/Remote server replied:.*?\.\.\. (.*)/) {
							$failreason = $1;
						}
						if ($failmail && $failreason) {
							last FAILLINE;
						}
					}
				}
			}
#		} else {
#			syslog(LOG_INFO,"autogenereated: $autogen");
#			$failmail = $from_email;
#			$failreason = 'autogenerated response';
#		}

		# strip trailing STOREDRV.Deliver.Exception
		if ($failreason =~ /^(.*?)STOREDRV\.Deliver\.Exception.*?$/) {
			$failreason = $1;
		}

		chomp($failmail);
		chomp($failreason);

		if (!$failreason) {
			$failreason = $maybe_reason;
		}
		if ($failmail) {
			syslog(LOG_INFO,"failmail: $failmail");
		}
		if ($failreason) {
			syslog(LOG_INFO,"failreason: $failreason");
		}

		if (!$failmail) {
			my $failrecp = $mainentity->head->get('X-Failed-Recipients');
			if ($failrecp
			&&	$failrecp =~ /^([\w\d\-\+\._]+)@([\w\d\-\+\._]+)$/
			) {
				$failmail = $failrecp;
			}
		}

		if ((  !$failreason
			||	$failreason =~ /^Action: failed, Status: \d.\d.\d$/
			)
		&&	$subj =~ /^Returned mail: (.*)/
		) {
			chomp($failreason = $1);
		}

		# strip strange reason for unavailable hotmail accounts
		# : 550 Requested action not taken: mailbox unavailable (-*********:3357:-**********)
		$failreason =~ s/\s*\(\-?\d+:\-?\d+:\-?\d+\)\s*$//;

		if (!$ticketid) {
			$ticketid = get_ticketid_from_mail($lsubj, $inreplyto, $references);
		}
		if (!$failreason && !$failmail && $ticketid) {
			syslog(LOG_INFO,"found ticketid and no failmail and no failreason, processing as normal message");
		} else {

			if ($failmail =~ /\@partyflock\.nl\>?$/) {
				syslog(LOG_INFO,"partyflock failmail ignored");
				#syslog(LOG_DEBUG, "message: ".$bodystr);
				myend(0);
			}

			my $content = $mainentity->as_string;
			if ($content =~ /No action is required on your part/) {
				syslog(LOG_INFO, "no action is required on our part");
				myend(0);
			}

			if ($do_store) {
				my $quoted_fail_mail = $dbh->quote($failmail);
				if (!$dbh->do("
					INSERT INTO realemailcheck SET
						EMAIL		= $quoted_fail_mail,
						STAMP		= $now,
						LAST_FAIL	= $now,
						FAILS		= 1,
						TOTAL_FAILS	= 1
					ON DUPLICATE KEY UPDATE
						STAMP		= $now,
						LAST_FAIL	= $now,
						FAILS		= FAILS + 1,
						TOTAL_FAILS	= TOTAL_FAILS + 1")
				) {
					syslog(LOG_ERR,"query zut failed: ".$dbh->errstr);
					myend(1);
				}
				my $quoted_from = $dbh->quote($from);
				my $quoted_from_name = $dbh->quote($from_name);
				my $quoted_from_email = $dbh->quote($from_email);
				my $quoted_subj = $dbh->quote($subj);
				my $quoted_replyto = $dbh->quote($replyto);
				my $quoted_content = $dbh->quote($content);
				my $quoted_failmail = $dbh->quote($failmail);
				my $quoted_failreason = $dbh->quote($failreason);
				if (!$dbh->do("
					INSERT INTO contact_undeliverable SET
						CSTAMP		= $now,
						FROMLINE	= SUBSTRING($quoted_from, 1, 1024),
						FROM_NAME	= SUBSTRING($quoted_from_name, 1, 1024),
						FROM_EMAIL	= SUBSTRING($quoted_from_email, 1, 1024),
						SUBJECT		= SUBSTRING($quoted_subj, 1, 1024),
						REPLYTO		= SUBSTRING($quoted_replyto, 1, 1024),
						CONTENT		= $quoted_content,
						FAILMAIL	= SUBSTRING($quoted_failmail, 1, 1024),
						FAILREASON	= SUBSTRING($quoted_failreason, 1, 10240)")
				) {
					syslog(LOG_ERR,"query 1 failed: ".$dbh->errstr);
					myend(1);
				}
				if ($content =~ /No action is required on your part/) {
					# if content requires no action, then we don't need to store a message in the ticket section
					myend(0);
				}
				$cimid = $dbh->{'mysql_insertid'};
			}
			if (!$ticketid) {
				myend(0);
			}
		}
	}
	if ($subj =~ /^\[?fwd?:(.*)\]?$/i) {
		$maybeforward = 1;
	}
	mydecode($mainentity);
	myend(0);
}

sub follow_ticketid($) {
	my $ticketid = shift;
	my $sth = $dbh->prepare("SELECT NEWTICKETID FROM ticketaction WHERE ACTION = 'insert' AND TICKETID = $ticketid");
	if ($sth && $sth->execute && (my $row = $sth->fetchrow_arrayref)) {
		syslog(LOG_INFO,"ticket $ticketid was inserted into ".$row->[0]);
		return $row->[0];
	}
	return $ticketid;
}

sub obtainticket {
	if ($ticketid) {
		return;
	}
	my $haveticketid = shift;
	if (defined($haveticketid)) {
		$ticketid = follow_ticketid($haveticketid);

		# update ticket
		if ($do_store) {
			if (	!$dbh->do("
				INSERT INTO contact_ticket_log
				SELECT * FROM contact_ticket
				WHERE TICKETID=$ticketid")
			||	!$dbh->do("
				UPDATE contact_ticket SET ".
					($cimid ? '' : "
					ELEMENT		=IF(ELEMENT LIKE 'junk%',ORG_ELEMENT,ELEMENT),
					WITHEMAIL	=IF(WITHEMAIL=".$dbh->quote($from_email).",WITHEMAIL,''),")."
					LASTDIRECTION	='toadmin',
					MSGCNT		=MSGCNT+1,
					STATUS		='open',
					LSTAMP		=$now,
					MSTAMP		=$now,
					MUSERID		=0,
					READM		=0,
					LASTMAIL	=1,
					LASTFAILED	=$failure
				WHERE TICKETID=$ticketid")
			) {
				syslog(LOG_ERR,"query 2 failed: ".$dbh->errstr);
				myend(1);
			}
#			$qstr =~ s/[\t\n]+/ /g;
#			$qstr =~ s/\s+/ /g;
#			$qstr =~ s/^\s+//g;
#			$qstr =~ s/\s+$//g;
#			syslog(LOG_DEBUG,$qstr);
			$dbh->do('TRUNCATE contact_ticket_cache');
		}
		syslog(LOG_INFO,"update ticket: $ticketid");
	} else {
		# checking subject for ticketid
		# tmp support for non PF prefixed IDs

		my $haveticketid = get_ticketid_from_mail($lsubj, $inreplyto, $references);

		if (	$autoresponse ne ''
		&&	!$haveticketid
		) {
			$haveticketid = get_ticketid_from_mail($autoresponse);
		}
		if ($haveticketid) {
			# this ID might be fake
			# check email and reply-to

			$ticketid = follow_ticketid($haveticketid);

if (0) {
			my $quoted_from_email = $dbh->quote($from_email);
			my $quoted_replyto_email = $dbh->quote($replyto_email);
			my $sth = $dbh->prepare("
				SELECT 1 FROM mailinglist
				WHERE TICKETID=$ticketid AND
				EMAIL IN ($quoted_from_email, $quoted_replyto_email)"
			);
			if (!$sth) {
				syslog(LOG_ERR,"prepare failed");
				myend(1);
			}
			if (!$sth->execute) {
				syslog(LOG_ERR,"execute failed!");
				myend(1);
			}
			my $okmail = $sth->fetchrow_arrayref() ? 1 : 0;
			$sth->finish();
}
my $okmail = 0;
my $sth;
			if (!$okmail) {
				$sth = $dbh->prepare("
					SELECT DISTINCT FROM_EMAIL, TO_EMAIL, REPLYTO
					FROM contact_ticket_mail
					WHERE TICKETID = $ticketid"
				);
				if (!$sth) {
					syslog(LOG_ERR, "prepare failed");
					myend(1);
				}
				if (!$sth->execute) {
					syslog(LOG_ERR, "execute failed!");
					myend(1);
				}
				MAILINFO: while (my $mailinfo = $sth->fetchrow_hashref()) {
					if (	   $from_email
					&&	(	lc($from_email) eq lc($mailinfo->{'FROM_EMAIL'})
						||	lc($from_email) eq lc($mailinfo->{'REPLYTO'})
						||	lc($from_email) eq lc($mailinfo->{'TO_EMAIL'})
						)
					||		   $replyto_email
					&&	(	lc($replyto_email) eq lc($mailinfo->{'FROM_EMAIL'})
						||	lc($replyto_email) eq lc($mailinfo->{'REPLYTO'})
						||	lc($replyto_email) eq lc($mailinfo->{'TO_EMAIL'})
						)
					) {
						$okmail = 1;
						last MAILINFO;
					}
				}
				$sth->finish;
			}
		 	if ($okmail) {
				$ticketid = $haveticketid;
				# add to existing ticket
				if ($do_store) {
					my $quoted_from_email = $dbh->quote($from_email);
					my $set_element	  = $cimid ? 'ELEMENT'	 : "IF(ELEMENT LIKE 'junk%', ORG_ELEMENT, ELEMENT)";
					my $set_withemail = $cimid ? 'WITHEMAIL' : "IF(WITHEMAIL = $quoted_from_email, WITHEMAIL, '')";
					if (!$dbh->do("
						INSERT INTO contact_ticket_log
						SELECT * FROM contact_ticket
						WHERE TICKETID = $ticketid")
					||	!$dbh->do("
						UPDATE contact_ticket SET
							ELEMENT			= $set_element,
							WITHEMAIL		= $set_withemail,
							LASTDIRECTION	= 'toadmin',
							MSGCNT			= MSGCNT + 1,
							STATUS			= 'open',
							LSTAMP			= $now,
							MSTAMP			= $now,
							MUSERID			= 0,
							READM			= 0,
							LASTFAILED		= 0,
							LASTMAIL		= 1
						WHERE TICKETID = $ticketid")
					) {
						syslog(LOG_ERR,"query 4 failed: ".$dbh->errstr);
						myend(1);
					}
#					$qstr =~ s/[\t\n]+/ /g;
#					$qstr =~ s/\s+/ /g;
#					$qstr =~ s/^\s+//g;
#					$qstr =~ s/\s+$//g;
#					syslog(LOG_DEBUG,$qstr);
					$dbh->do('TRUNCATE contact_ticket_cache');
				}
				syslog(LOG_INFO,"update ticket: $ticketid");
			} else {
				syslog(LOG_INFO,"no known email was found for ticketid: $ticketid");
				# create new ticket
				if ($do_store) {
					my $quoted_element = $dbh->quote($element);
					my $quoted_from_email = $dbh->quote($from_email);
					if (!$dbh->do("
						INSERT INTO contact_ticket SET
							EXPIRES			= COALESCE((SELECT EXPIRES FROM party_db.contactreturn WHERE ELEMENT IN ('', $quoted_element) ORDER BY ELEMENT='' ASC LIMIT 1),0),
							WITHEMAIL		= $quoted_from_email,
							LASTDIRECTION	= 'toadmin',
							ISMAIL			= 1,
							LASTMAIL		= 1,
							ELEMENT			= $quoted_element,
							ORG_ELEMENT		= $quoted_element,
							ID				= $elementid,
							CONCERNING		= 0,
							CSTAMP			= $now,
							LSTAMP			= $now,
							MAYBEID			= $ticketid")
					) {
						syslog(LOG_ERR,"query 5 failed: ".$dbh->errstr);
						myend(1);
					}
					$ticketid = $dbh->{'mysql_insertid'};
					$dbh->do('TRUNCATE contact_ticket_cache');
				} else {
					$ticketid = 666;
				}
				$newticket = 1;
				syslog(LOG_INFO,"new ticketid: $ticketid");
				my ($newsubj,$newticketid) = split_subject($subj);
				if ($newsubj ne $subj) {
					$subj = $newsubj;
				}
				syslog(LOG_INFO,"stripped old ticketid, new subject: $subj");
			}
		} else {
			# create ticket
			if ($do_store) {
				my $q;
				my $quoted_element = $dbh->quote($element);
				my $quoted_from_email = $dbh->quote($from_email);
				if (!$dbh->do($q = "
					INSERT INTO contact_ticket SET
						EXPIRES			= COALESCE((SELECT EXPIRES FROM contactreturn WHERE ELEMENT IN ('', $quoted_element) ORDER BY ELEMENT = '' LIMIT 1), 0),
						WITHEMAIL		= $quoted_from_email,
						LASTDIRECTION	= 'toadmin',
						ISMAIL			= 1,
						LASTMAIL		= 1,
						ELEMENT			= $quoted_element,
						ORG_ELEMENT		= $quoted_element,
						ID				= $elementid,
						CONCERNING		= 0,
						LSTAMP			= $now,
						CSTAMP			= $now")
				) {
					syslog(LOG_ERR,"query 6 failed: ".$dbh->errstr);
					syslog(LOG_ERR,"query: ",$q);
					myend(1);
				}
				$ticketid = $dbh->{'mysql_insertid'};
				$dbh->do('TRUNCATE contact_ticket_cache');
			} else {
				$ticketid = 666;
			}
			$newticket = 1;
			syslog(LOG_INFO,"new ticketid: $ticketid");
		}
	}
	if (!$ticketid) {
		syslog(LOG_ERR,"no TICKETID");
		myend(1);
	}
}

sub mycreatemail {
	if (!$do_store) {
		return;
	}
	obtainticket;
	my $quoted_precedence = $dbh->quote($precedence);
	my $quoted_mainhead = $dbh->quote($mainhead->as_string);
	my $quoted_mlist = $dbh->quote($mlist);
	my $quoted_from = $dbh->quote($from);
	my $quoted_from_email = $dbh->quote($from_email);
	my $quoted_from_name = $dbh->quote($from_name);
	my $quoted_to_email = $dbh->quote($to_email);
	my $quoted_to_name = $dbh->quote($to_name);
	my $quoted_subj = $dbh->quote($subj);
	my $quoted_subj_utf8 = $dbh->quote($subj_utf8);
	my $quoted_type = $dbh->quote($type);
	my $quoted_replyto_email = $dbh->quote($replyto_email);
	my $quoted_replyto_name = $dbh->quote($replyto_name);
	my $quoted_cc = $dbh->quote($cc);
	my $quoted_messageid = $dbh->quote($messageid);
	my $quoted_references = $dbh->quote($references);
	my $quoted_forward = $dbh->quote($forward);
	my $q;
	if (!$dbh->do($q = "INSERT INTO contact_ticket_mail SET
			TICKETID	= $ticketid,
			CTMSGID		= $ctmsgid,
			CIMID		= $cimid,
			AUTOGEN		= $autogen,
			SPAMSCORE	= $spamscore,
			PRECEDENCE	= $quoted_precedence,
			HEADERS		= COMPRESS($quoted_mainhead),
			UNSUBSCRIBE	= $quoted_mlist,
			FROMLINE	= $quoted_from,
			FROM_EMAIL	= $quoted_from_email,
			FROM_NAME	= BINARY $quoted_from_name,
			TO_EMAIL	= $quoted_to_email,
			TO_NAME		= $quoted_to_name,
			SUBJECT		= $quoted_subj,
			SUBJECT_UTF8= $quoted_subj_utf8,
			TYPE		= $quoted_type,
			REPLYTO		= $quoted_replyto_email,
			REPLYTONAME	= BINARY $quoted_replyto_name,
			CC			= $quoted_cc,
			MESSAGEID	= $quoted_messageid,
			MREFERENCES	= $quoted_references,
			FORWARD		= $quoted_forward,
			INVALIDFROM	= $invalid_from"
	)) {
		syslog(LOG_ERR,"mycreatemail: could not create contact_ticket_mail: ".$dbh->errstr);
		syslog(LOG_ERR,"mycreatemail: problematic query: ".$q);
		myend(1);
	}
	syslog(LOG_DEBUG, 'created contact_ticket_mail');
	syslog(LOG_DEBUG, 'inreplyto here: '.$inreplyto);
	syslog(LOG_DEBUG, 'references here: '.$references);
	if ($inreplyto) {
		my $quoted_inreplyto = $dbh->quote($inreplyto);
		if (!$dbh->do($q = "
			INSERT INTO contact_ticket_mail_inreplyto SET
				CTMSGID		= $ctmsgid,
				IN_REPLY_TO	= $quoted_inreplyto"
		)) {
			syslog(LOG_ERR,"mycreatemail, inreplyto: could not create: ".$dbh->errstr);
			syslog(LOG_ERR,"mycreatemail, inreplyto: problematic query: ".$q);
		}
	}
	if ($references) {
		foreach my $reference (split / /, $references) {
			my $qouted_reference = $dbh->quote($reference);
			if (!$dbh->do($q = "INSERT INTO contact_ticket_mail_reference SET
					CTMSGID		= $ctmsgid,
					REFERENCE	= $qouted_reference"
			)) {
				syslog(LOG_ERR,"mycreatemail, reference: could not create: ".$dbh->errstr);
				syslog(LOG_ERR,"mycreatemail, reference: problematic query: ".$q);
			}
		}
	}
}

sub mycreatemessage($$$) {
	my ($bodylines, $charset, $onlyhtml) = @_;
	my $bodystring = '';
	my $bodystring_utf8 = '';
	my $prevempty = 0;

#	use Data::Dumper;
#	print Dumper(@$bodylines);

	if (defined $bodylines) {
		BODYLINE: for my $bodyline (@$bodylines) {
			#$bodyline =~ s/^\s+//;
			$bodyline =~ s/[\r\n]+$//;
			if ($bodyline =~ m/^\s*$/) {
				if ($prevempty) {
					next BODYLINE;
				} else {
					$prevempty = 1;
					$bodystring .= "\n";
					if ($charset eq 'utf-8') {
						$bodystring_utf8 .= "\n";
					}
					next BODYLINE;
				}
			} else {
				$prevempty = 0;
			}
			if ($charset eq 'utf-8') {
				#decode_entities($bodyline);

				$bodystring_utf8 .= $bodyline."\n";

				$bodyline = decodeutf8($bodyline);
			} elsif ($bodyline =~ /&(?:[^;]*);/) {
				my $newbodyline = $bodyline;
				#decode_entities($newbodyline);
				if ($newbodyline ne $bodyline) {
					$bodyline = decodeutf8($newbodyline);
				}
			}
			$bodystring .= $bodyline."\n";
		}
	} else {
		$bodystring = 'Alleen HTML beschikbaar, zie bijlage.';
	}
	if ($cimid) {
		# when does this happen?
		$bodystring = '';
	}
	obtainticket;

	if (!$do_store) {
		$ctmsgid = -1;
		return;
	}
	# create initial contact_ticket_message
	if (!$dbh->do("
		INSERT INTO contact_ticket_message SET
			TICKETID	=$ticketid,
			DIRECTION	='toadmin',
			ISMAIL		=1,
			HTMLSRC		=b'".($onlyhtml ? 1 : 0)."',
			BODY		=".$dbh->quote($bodystring).",
			BODYUTF8	=".$dbh->quote($bodystring eq $bodystring_utf8 ? '' : $bodystring_utf8).",
			CSTAMP		=$now")
	) {
		syslog(LOG_ERR,"query 7 failed: ".$dbh->errstr);
		myend(1);
	}
	$ctmsgid = $dbh->{'mysql_insertid'};
	if (!$ctmsgid) {
		syslog(LOG_ERR,"no CTMSGID!");
		myend(1);
	}
	my $quoted_body = $dbh->quote($mainentity->as_string);
	$dbh->do("
	INSERT INTO completemsg SET
		CTMSGID = $ctmsgid,
		BODY	= COMPRESS($quoted_body)"
	);

	my $notify_email = $replyto_email ne '' ? $replyto_email : $from_email;

	if ($notify_email !~ /(?<!thomas)\@partyflock\.nl$/
	&&	(	$notify
		||	$newticket
		&&	$precedence ne 'bulk'
		&&	!$autogen
		&&	!$mlist
		&&	!$failure
		&&	$to_email ne $from_email
		&&	$xmailer !~ /^Mach 5 Mailer/i
		&&	$notify_email !~ /^(?:(?:dono?t|no)[\-_\.]?reply.*|postmaster|mailing|mailer\-daemon|(?:[a-zA-Z\d_+\-]*-)return|undisclosed-recipient:;)\@/i
		&&	$lsubj ne 'afwezigheid'
		)
	) {
		if (!$notify) {
			$notify = $replyto_email ? $replyto : $from;
		}
		syslog(LOG_INFO,"mailing notification to $notify");

		my $go_email =	$to_email !~ /\@partyflock\./
					?	$envelope_to
					:	$to_email;

		use Mail::Send;
		my $new_subject = $subj;
		if ($messageid !~ /\@mail\.gmail\.com/) {
			# don't add anything to subject for gmail, otherwise threading is lost

			my ($newsubj, $newticketid) = split_subject($subj);

			$newsubj .= ' [Partyflock #'.$ticketid.']';

		}
#		my $use_mime = $new_subject !~ /^[\x20-\x7f\t]$/;
		syslog(LOG_INFO,"subject for notification: $new_subject");
#		if ($use_mime) {
#			$new_subject = Encode::encode('MIME-Header',$new_subject);
#			syslog(LOG_INFO, "subject for notification after MIME: $new_subject");
#		}
		my $msg = Mail::Send->new;
#		if ($use_mime) {
#			$msg->set('MIME-Version','1.0');
#		}
		$msg->set('To',$notify);
		$msg->set('References',($references ? $references.' '.$messageid : $messageid));
		$msg->set('In-Reply-To',$messageid);
		$msg->set('Message-ID','<'.$ticketid.'.'.$ctmsgid.'@partyflock.ticket.new>');
		$msg->set('X-Autogenerated','reply');
		$msg->set('X-Auto-Response-Suppress','All');
		$msg->set('Auto-Submitted','auto-replied');
		$msg->set('From', "Partyflock <$go_email>");
		if ($new_subject =~ /^[\x20-\x7F\t]*$/) {
			$msg->set('Subject', $new_subject);
		} else {
			$msg->set('MIME-Version', '1.0');
			$msg->set('Subject', Encode::encode('MIME-Header', $new_subject));
		}
#		if ($cc) {
#			$msg->set('CC',$cc);
#		}
		my $fh = $msg->open;
		print $fh
			"Je bericht met onderwerp \"$subj\" voor ".$to_email.($go_email ne $to_email ? ' ('.$go_email.')' : '').
			" is ontvangen en zal zo spoedig mogelijk behandeld worden.\n\n".
			"Ben je wat vergeten te melden of wil je wat toevoegen? Beantwoord dan dit bericht!\n\n".
			"Ticketnummer: $ticketid\n\n".

			"Is je e-mailadres gekoppeld aan je gebruikersprofiel op Partyflock, dan kun je ook hier je ticket inzien en beantwoorden:\n\n".

			"https://partyflock.nl/ticket/$ticketid\n\n".

			"Voor overige contactmogelijkheden:\n\n".

			"https://partyflock.nl/contact/options\n\n".

			"Bedankt voor je bericht!";

		$fh->close;
	}
}
sub mydecode {
	my $entity = shift;
	my $partcnt = $entity->parts;

	$type = lc $entity->effective_type;
	syslog(LOG_INFO,"effective_type: $type");

	my $head = $entity->head;

	if ($head) {
		if ($partcnt) {
			if ($type eq 'multipart/alternative') {
				# reorder, prefer text/plain
				my @useparts;
				for ($entity->parts) {
					my $ptype = lc $_->effective_type;
					if ($ptype =~ /^text\/plain/) {
						unshift @useparts,$_;
					} else {
						push @useparts,$_;
					}
				}
				for (@useparts) {
					mydecode($_);
				}
			} else {
				for ($entity->parts) {
					mydecode($_);
				}
			}
			return;
		}
		if ($maybeforward == 1) {
			syslog(LOG_INFO,"maybe forward, parsing mail as forwarded mail");
			if (parse_maybe_forward($entity)) {
				# all handled by parse_maybe_forward
				return;
			}
			$maybeforward = 0;
		} elsif ($maybeforward == 2) {
			syslog(LOG_INFO,"finding email in further part");
			if (find_email_in_html($entity)) {
				$maybeforward = 0;
			}
		}
		my $body = $entity->bodyhandle;
		my $disp = $head->get('Content-disposition') || '';
		chomp($disp);

		if ($body) {
			use File::MimeInfo::Magic;
			my $mimetype = '';
			$charset = '';
			if (defined $body->path) {
				my $tmp = $body->path;
				$tmp =~ s/["|\?!\*]/\\$1/g;
				my $cmd = '/home/<USER>/bin/mimetype.php --raw "'.$tmp.'"';
				my $info = qx{$cmd};
				chomp $info;
				my @mimes = split /\x1F/,$info;
				if (defined $mimes[3]) {
					$charset = $mimes[3];
				}
				my $mimetype =
					$#mimes >= 2
				&&	$mimes[1] eq 'OK'
				?	$mimes[2].(defined $mimes[3] && $mimes[3] ne '' ? '; charset='.$mimes[3] : '')
				:	mimetype($body->path);
				if (	$mimetype ne 'application/octet-stream'
				&&	$mimetype !~ /^text\/x\-fortran/
				) {
					syslog(LOG_INFO,"body->path seems of type: $mimetype");
					$type = $mimetype;
				}
			} else {
				syslog(LOG_INFO,"WARNING: no body->path, no mimetype");
			}

			my $completetype = $head->get('Content-type') || '';
			chomp $completetype;
			if ($type =~ /^text\//
			&&	$completetype =~ /^text\/([^;\s]+)/
			) {
				my $subtype = $1;
				$type =~ s/^text\/[^;\s]+/text\/$subtype/;
			}
			$completetype =~ s/[\n\t\s]+/ /g;
			$completetype =~ s/;?\s*name="(.*?)"\s*//;
			chomp $completetype;

			syslog(LOG_INFO,'completetype: '.$completetype);

#			if (	$completetype =~ /^text\/html/
#			&&	$type =~ /^text\//
#			) {
#				$type = $completetype;
#			}
			if ($completetype =~ /\bcharset\s*=\s*[\"\']?([^;\"\']+)/) {
				$charset = lc($1);
				syslog(LOG_INFO, 'charset set to '.$charset);
				if ($type =~ /\bcharset\s*=\s*[\"\']?[^;\"\']+/) {
					$type =~ s/\bcharset\s*=\s*[\"\']?[^;\"\']+[\"\']?/charset=$charset/;
				} else {
					$type .= '; charset='.$charset;
				}
			}
			syslog(LOG_INFO,"using mimetype: $type");

#				if ($charset eq 'iso-2022-jp') {
#					syslog(LOG_INFO,"detected japanese language, ignoring");
#					myend(-1);
#				}
#				$type .= '; charset='.$charset;
#				syslog(LOG_INFO,"characterset: $1, making mimetype: \"$type\"");
#			}


			if (!$doneinitial
			&&	$disp eq 'inline'
			) {
				$doneinitial = 1;
				syslog(LOG_INFO,"type 1 mail");
				mystoremessage($body, $type, $charset);

			} elsif ($disp !~ /^attachment/
				&&	$doneinitial == 0
				&&	(	$type =~ /^text\/plain/
					||	$type =~ /^text\/html/
					)
			) {
				syslog(LOG_INFO,"type 2 mail");
				$doneinitial = 1;
				mystoremessage($body, $type, $charset);

			} elsif (!$cimid) {
				# extra objects, only if cimid=0 (no failure)

				if ($do_store
				&&	!$ctmsgid
				) {
					# create empty message
					syslog(LOG_INFO,"empty message, creating...");

					if ($do_store) {
						obtainticket;
						if (	!$dbh->do("
							INSERT INTO contact_ticket_message SET
								TICKETID	=$ticketid,
								DIRECTION	='toadmin',
								ISMAIL		=1,
								BODY		='',
								CSTAMP		=$now")
						) {
							syslog(LOG_ERR,"query 8 failed: ".$dbh->errstr);
							myend(1);
						}
						$ctmsgid = $dbh->{'mysql_insertid'};
						if (!$ctmsgid) {
							syslog(LOG_ERR,"no CTMSGID!");
							myend(1);
						}
						mycreatemail();
					}
				}
				my $contentid = '';
				my $recommended_filename;
				if (!$head->recommended_filename) {
					if ($type =~ /^text\/html/) {
						$recommended_filename = 'message.html';
					} elsif (
						$type =~ /^(text|image|video|audio)\/(?:x\-(?:ms\-)?)?([a-z]*)$/i
					) {
						if ($1 eq 'text' && $2 eq 'plain') {
							$recommended_filename = 'message.txt';
						} else {
							$recommended_filename = $1.'.'.$2;
						}
					} else {
						$recommended_filename = 'unknown';
					}
				} else {
					$recommended_filename = $head->recommended_filename;
				}
				if ((	$type eq 'multipart/appledouble'
					||	$type eq 'application/applefile'
					)
				&&	$recommended_filename !~ /^\._/
				) {
					$recommended_filename = "._$recommended_filename";
				}
				$contentid = $head->get('Content-ID') || '';
				chomp($contentid);
				if (	$contentid ne ''
				&&	$contentid =~ /^\s*<(.*)>\s*/
				) {
					$contentid = $1;
				}
				if ($contentid) {
					syslog(LOG_INFO,"content-id: $contentid");
				}
				if ($type =~ /^(image\/(?:jpe?g|gif|png)|application\/(?:zip|x\-shockwave\-flash));/) {
					$type = $1;
				}
				$recommended_filename = parse_subject($recommended_filename, $no_utf8);
				syslog(LOG_INFO,"found attachment \"$recommended_filename\" of type \"$type\"".($contentid ? ' and contentid '.$contentid : ''));
				if ($do_store) {
					my $bstring = $body->as_string;

					# FIX ME: find identical here?
					my $compress;
					if ($type =~ /^text\//
					||	$type =~ /^message\//
					) {
						$compress = 1;
					} elsif (
						$type =~ /^application\/applefile/
					||	$type =~ /^application\/msword/
					||	$type =~ /^audio\/wav/
					||	$type =~ /^image\/(?:x\-ms\-)bmp/
					) {
						$compress = 1;
					} else {
						$compress = 0;
					}
					my $crc = crc32($bstring);
					my $len = length($bstring);
#					my $stripped_type = $type;
#					$stripped_type =~ s/;\s*charset=.*//;
					my $quoted_type = $dbh->quote($type);
					my $quoted_charset = $dbh->quote($charset);
					if (!$dbh->do("
						INSERT INTO contact_ticket_attachment_meta SET
							MIMETYPE	= $quoted_type,
/*							CHARSET		= $quoted_charset,	*/
							SIZE    	= $len,
							COMPRESSED	= $compress,
							CRC			= $crc")
					) {
						syslog(LOG_ERR,"failed to store(2) attachment meta: ".$dbh->errstr);
						myend(1);
					}
					my $dataid = $dbh->{'mysql_insertid'};
					my $quoted_bstring = $dbh->quote($bstring);
					my $quoted_data = $compress ? "COMPRESS($quoted_bstring)" : $quoted_bstring;
					if (!$dbhe->do("
						INSERT INTO data_db.contact_ticket_attachment_data SET
							DATAID	= $dataid,
							DATA    = $quoted_data")
					) {
						syslog(LOG_ERR,"failed to store(2) attachment data: ".$dbhe->errstr);
						myend(1);
					}
					$recommended_filename =~ s/\//_/g;
					my $quoted_contentid = $dbh->quote($contentid);
					my $quoted_recommended_filename = $dbh->quote($recommended_filename);
					if (!$dbh->do("
						INSERT INTO contact_ticket_attachment SET
							CTMSGID		= $ctmsgid,
							CONTENTID	= $quoted_contentid,
							FILENAME	= $quoted_recommended_filename,
							DATAID		= $dataid,
							CSTAMP		= $now")
					) {
						syslog(LOG_ERR,"failed to store(2) attachment: ".$dbh->errstr);
						myend(1);
					}
					syslog(LOG_INFO,"stored attachment with dataid $dataid");
				}
			}
		} else {
			syslog(LOG_INFO,"NO BODY!");
		}
	} else {
		syslog(LOG_ERR,"NO HEAD");
		myend(1);
	}
}


sub getelement($) {
	my $name = shift;

	  if ($name eq 'helpdesk') {			return 'helpdesk';
	} if ($name eq 'steden') {				return 'city';
	} if ($name eq 'meetings') {			return 'meeting';
	} if ($name eq 'locaties'
	||	  $name eq 'lokaties') {			return 'location';
	} if ($name eq 'organisaties') {		return 'organization';
	} if ($name eq 'agenda') {				return 'party';
	} if ($name eq 'nieuws') {				return 'news';
	} if ($name eq 'djs'
	||	  $name eq 'artiesten') {			return 'artist';
	} if ($name eq 'forum') {				return 'topic';
	} if ($name eq 'galerij') {				return 'photo';
	} if ($name eq 'klachten') {			return 'abuse';
	} if ($name eq 'pr') {					return 'pr';
	} if ($name eq 'acties') {				return 'contest';
	} if ($name eq 'recensies') {			return 'review';
	} if ($name eq 'interviews') {			return 'interview';
	} if ($name eq 'nieuwsadvertenties') {	return 'newsad';
	} if ($name eq 'info') {				return 'info';
	} if ($name eq 'verslagen') {			return 'report';
	} if ($name eq 'polls') {				return 'poll';
	} if ($name eq 'flockatures'
	||	  $name eq 'vacatures'
	) {										return 'crewjob';
	} if ($name eq 'vacaturebank') {		return 'job';
	} if ($name eq 'newphoto'
	||	  $name eq 'fotografenvacatures'
	||	  $name eq 'aspirantfotografen'
	||	  $name eq 'nieuwefotografen'
	||	  $name eq 'proefset'
	) {										return 'crewjob';
	} if ($name eq 'adverteren') {			return 'b2b';
	} if ($name eq 'banners') {				return 'ad';
	} if ($name eq 'televisie') {			return 'tvad';
	} if ($name eq 'promotie') {			return 'promo';
	} if ($name eq 'camera'
	||	  $name eq 'cameraverzoeken') {		return 'camerarequest';
	} if ($name eq 'labels') {				return 'label';
	} if ($name eq 'streams') {				return 'stream';
	} if ($name eq 'columns') {				return 'column';
	} if ($name eq 'albums') {				return 'album';
	} if ($name eq 'talents') {				return 'talentupload';
	} if ($name eq 'debiteuren') {			return 'invoice';
	} if ($name eq 'videos') {				return 'video';
	} if ($name eq 'muziek') {				return 'music';
	} if ($name eq 'kantoor'
	||	  $name eq 'office') {				return 'office';
	} if ($name eq 'onderzoek') {			return 'research';
	} if ($name eq 'sales'
	||	  $name eq 'marketing') {			return 'sm';
	} if ($name eq 'office') {				return 'office';
	} if ($name eq 'bedrijfsuitje') {		return 'adminmeeting';
	} if ($name eq 'bugs') {				return 'bug';
	} if ($name eq 'development'
	||	  $name eq 'compo'
	||	  $name eq 'virtualtour'
	||	  $name eq 'abuse'
	||	  $name eq 'affiliate'
	||	  $name eq 'pension') {				return $name;
	}
	return undef;
}
sub split_subject($) {
	my $_subj = shift;
	my $_ticketid = 0;
	if ($_subj =~ /^\s*(.*?)\s*\[(?:Partyflock[\s_]*#?(\d+)|PF([a-f0-9]+)|([a-f0-9]+)PF)\]\s*(.*?)\s*$/i) {
		$_subj = $1;
		if ($_subj) {
			$_subj .= ' ';
		}
		$_subj .= $5;
		chomp $_subj;
		$_ticketid = $2 ? $2 : hex ($3 ? $3 : $4);
	}
	return ($_subj,$_ticketid);
}

sub get_ticketid_from_mail($$$) {
	my ($xsubj, $inreplyto, $references) = @_;
	my $where_found;
	if (($where_found = ($inreplyto  && $inreplyto  =~ /<?(\d+)\.(\d+)\@partyflock\.ticket(?:\.new)?>?/i) ? 'In-Reply-To' : '')
	||	($where_found = ($references && $references =~ /<?(\d+)\.(\d+)\@partyflock\.ticket(?:\.new)?>?(?!.*partyflock\.ticket)/i) ? 'References' : '')
	||	($where_found = ($subj && $subj =~ /\[Partyflock #(\d+)\]/i) ? 'Subject' : '')
	) {
		my $ticketid = $1;
		my $ctmsgid = $2;
		syslog(LOG_INFO,"ticketid found in $where_found, force accepted");
		my $sth = $dbh->prepare("SELECT TICKETID FROM contact_ticket_message WHERE CTMSGID = $ctmsgid");
		my $row;
		if (	$sth
		&&	$sth->execute
		&&	($row = $sth->fetchrow_arrayref)
		) {
			my $sameid = $row->[0];
			if ($sameid != $ticketid) {
				syslog(LOG_INFO,"found other ticket $sameid belonging to CTMSGID $ctmsgid");
				$ticketid = $sameid;
			}
		}
		return $ticketid;
	}
	my $quoted_inreplyto = $dbh->quote($inreplyto);
	my $sth = $dbh->prepare("
		SELECT TICKETID
		FROM contact_ticket_mail
		JOIN contact_ticket_mail_inreplyto USING (CTMSGID)
		WHERE IN_REPLY_TO = $quoted_inreplyto"
	);
	my $row;
	if ($sth
	&&	$sth->execute
	&&	($row = $sth->fetchrow_arrayref)
	) {
		my $ticketid = $row->[0];
		syslog(LOG_INFO, "ticketid $ticketid found using In-Reply-To header");
		return $ticketid;
	}
	my ($__subj, $__ticketid) = split_subject($xsubj);
	if ($__ticketid) {
		syslog(LOG_INFO,"ticketid ".$__ticketid." found in subject, force accept");
		return $__ticketid;
	}
	return 0;
}

sub parse_maybe_forward($) {
	my $part = shift;
	my $body = $part->bodyhandle;
	my $head = $part->head;
	if (!$body) {
		return 1;
	}
	my $charset = '';
	if ($head) {
		my $completetype = $head->get('Content-type') || '';
		chomp($completetype);
		if ($completetype =~ /\bcharset\s*=\s*[\"\']?([^;\"\']+)/) {
			$charset = lc($1);
			syslog(LOG_INFO,"characterset: $1");
		}
	}
	$type = lc $part->effective_type;
	my $infomsg = '';
	my $infomsg_utf8 = '';
	my $forwardmsg = '';
	my $forwardmsg_utf8 = '';
	my $state = 0;
	my $nextemail = 0;

	my @lines = $body->as_lines;

	if ($type eq 'text/html') {
		my $f = HTML::FormatText::WithLinks->new();
		my $text = $f->parse($body->as_string);
		if ($text) {
			@lines = split(/\r?\n/,$text);
		}
	}

	LINE: for (@lines) {
		s/[\s\r\n]+$//;

		if ($state == 0) {
			my $mtype;

			if ((/--- (?:Original Message|(?:Originele|Doorgestuurd|Oorspronkelijk) bericht) ---/i && ($mtype = 1))
			||	(/^(?:From|Van): ([a-zA-Z0-9\.\-_]+\@[a-zA-Z0-9\.\-_]+?)(?:Subject)/i && ($mtype = 4))	# borked live mail forward
			||	(/^(?:From|Van): ([a-zA-Z0-9\.\-_]+\@[a-zA-Z0-9\.\-_]+?)(?:To: ([a-zA-Z0-9\.\-_]+\@[a-zA-Z0-9\.\-_]+))(Sub)/i && ($mtype = 2))	# borked live mail forward
			||	(/^(?:From|Van): ([a-zA-Z0-9\.\-_]+\@[a-zA-Z0-9\.\-_]+?)(?:To: ([a-zA-Z0-9\.\-_]+\@[a-zA-Z0-9\.\-_]+))/i && ($mtype = 5))	# borked live mail forward
			||	(/^>? ?((?:From|Van): )/i && ($mtype = 3))
			) {
				if ($mtype == 2 || $mtype == 4 || $mtype == 5) {
					syslog(LOG_INFO,"borked live mail!");
				} else {
					syslog(LOG_INFO,"found $_");
				}
				if ($do_store) {
					$infomsg =~ s/^\s+/ /;
					$infomsg =~ s/([\s\t\r\n\f]+)$//;

					$infomsg_utf8 =~ s/^\s+/ /;
					$infomsg_utf8 =~ s/([\s\t\r\n\f]+)$//;

					obtainticket;
					my $quoted_infomsg = $dbh->quote($infomsg);
					my $quoted_infomsg_utf8 = $infomsg eq $infomsg_utf8 ? "''" : $dbh->quote($infomsg_utf8);
					if (!$dbh->do("
						INSERT INTO contact_ticket_message SET
							TICKETID	= $ticketid,
							DIRECTION	= 'toadmin',
							ISMAIL		= 1,
							BODY		= $quoted_infomsg,
							BODYUTF8	= $quoted_infomsg_utf8,
							CSTAMP		= $now")
					) {
						syslog(LOG_INFO, 'creation of contact_ticket_message failed: '.$dbh->errstr);
						myend(1);
					}
					$ctmsgid = $dbh->{'mysql_insertid'};
					if (!$ctmsgid) {
						syslog(LOG_INFO,"no CTMSGID!");
						myend(1);
					}
					$forward = 'infomsg';
					mycreatemail();
				}
				if ($mtype == 2 || $mtype == 4 || $mtype == 5) {
					$replyto = '';
					$replyto_name = '';
					$replyto_email = '';
					($from_name,$from_email) = parse_from($1, $no_utf8);
					syslog(LOG_INFO,'forwarded from_name: '.($from_name ? $from_name : '(empty)'));
					syslog(LOG_INFO,"forwarded from_email: $from_email");
					if ($mtype == 2 || $mtype == 5) {
						($to_name,$to_email) = parse_from($2, $no_utf8);
						syslog(LOG_INFO,'forwarded to_name: '.($to_name ? $to_name : '(empty)'));
						syslog(LOG_INFO,"forwarded to_email: $to_email");
					}
					$maybeforward = 0;
					$state = 2;
					next LINE;
				} else {
					$replyto = '';
					$replyto_name = '';
					$replyto_email = '';
					$from = '';
					$maybeforward = 2;
					$state = 1;
					if ($mtype == 1) {
						next LINE;
					} else {
						redo LINE;
					}
				}
			}
			if ($_ ne '') {
				if ($charset eq 'utf-8') {
					$infomsg_utf8 .= $_."\n";

					$_ = decodeutf8($_);
				}
				$infomsg .= $_."\n";
			}
		} elsif ($state == 1) {
			if (	/^>?\s*$/
			||	/^\h{2,}$/
			) {
				$state = 2;
				next LINE;
			}
			if (/\s*(?:From|Van): ([^\r\n]*)$/i) {
				$from = $1;
				if ($from !~ /\@/) {
					$nextemail = 1;
					next LINE;
				}
				syslog(LOG_INFO,"forwarded from: $from?");
				($from_name,$from_email) = parse_from($from, $no_utf8);
				if ($from_email =~ /\@/) {
					syslog(LOG_INFO,'forwarded from_name: '.($from_name ? $from_name : '(empty)'));
					syslog(LOG_INFO,"forwarded from_email: $from_email");
					$maybeforward = 0;
					if (!is_valid_email($from_email)) {
						$invalid_from = 1;
					}
				}
				if (!$nextemail) {
					next LINE;
				}
				# we had nextemail set, keep current from
				$_ = '';
			}
			if (/\s*(?:Subject|Onderwerp): (.*)$/i) {
				if (/^(?:.*?)Subject: (.*?)>?\s*Date:/i) {
					# pick $1 from last matched regex, so one line up
					$subj = parse_subject($1, $no_utf8);
				} else {
					# pick $1 from last matched regex, so the one with Subject|Onderwerp, 5 lines up
					$subj = parse_subject($1, $no_utf8);
				}
				$lsubj = lc($subj);
				syslog(LOG_INFO,"forwarded subject: $subj");;
				next LINE;
			}
			if (/\s*Reply-to: (.*)$/i) {
				$replyto = $1;
				if ($replyto) {
					($replyto_name,$replyto_email) = parse_from($replyto, $no_utf8);
					syslog(LOG_INFO,"forwarded reply-to: $replyto");
				} else {
					$replyto_name = '';
					$replyto_email = '';
				}
				next LINE;
			}
			if (/\s*(?:To|Aan): (.*)$/i) {
				my $to = $1;
				syslog(LOG_INFO,"forwarded to: $to");
				($to_name,$to_email) = parse_from($to, $no_utf8);
				syslog(LOG_INFO,'forwarded to_name: '.($to_name ? $to_name : '(empty)'));
				syslog(LOG_INFO,"forwarded to_email: $to_email");
				next LINE;
			}
			if ($nextemail) {
				$nextemail = 0;
				if (/\@/) {
					$from .= ' '.$_;
					syslog(LOG_INFO,"forwarded from: $from?");
					($from_name,$from_email) = parse_from($from, $no_utf8);
					if ($from_email =~ /\@/) {
						syslog(LOG_INFO,'forwarded from_name: '.($from_name ? $from_name : '(empty)'));
						syslog(LOG_INFO,"forwarded from_email: $from_email");
						$maybeforward = 0;
						if (!is_valid_email($from_email)) {
							$invalid_from = 1;
						}
					}
				} else {
					$from_name = $from;
					$from_name =~ s/^[\s"']+//;
					$from_name =~ s/[\s"']+$//;
					$from_email = '';
					syslog(LOG_INFO,'forwarded from_name: '.($from_name ? $from_name : '(empty)'));
					syslog(LOG_INFO,"forwarded from_email: $from_email");
				}
			}
		} elsif ($state == 2) {
			if (/^>?\s*$/) {
				next LINE;
			}
			if ($charset eq 'utf-8') {
				$forwardmsg_utf8 .= $_."\n";
				$_ = decodeutf8($_);
			}
			$forwardmsg .= $_."\n";
			$state = 3;
		} elsif ($state == 3) {
			if ($charset eq 'utf-8') {
				$forwardmsg_utf8 .= $_."\n";
				$_ = decodeutf8($_);
			}
			$forwardmsg .= $_."\n";
		}
	}
	if ($state == 0) {
		syslog(LOG_INFO,"not really forwarded!");
		return 0;
	}
	$doneinitial = 1;

	if ($do_store) {
		$forwardmsg =~ s/^\s+/ /;
		$forwardmsg =~ s/([\s\t\r\n\f]+)$//;

		$forwardmsg_utf8 =~ s/^\s+/ /;
		$forwardmsg_utf8 =~ s/([\s\t\r\n\f]+)$//;
		obtainticket;
		my $quoted_forwardmsg = $dbh->quote($forwardmsg);
		my $quoted_forwardmsg_utf8 = $forwardmsg eq $forwardmsg_utf8 ? "''" : $dbh->quote($forwardmsg_utf8);
		if (!$dbh->do("
			INSERT INTO contact_ticket_message SET
				TICKETID	= $ticketid,
				DIRECTION	= 'forwardinfo',
				ISMAIL		= 1,
				BODY		= $quoted_forwardmsg,
				BODYUTF8	= $quoted_forwardmsg_utf8,
				CSTAMP		= $now")
		) {
			syslog(LOG_INFO, 'creation of contact_ticket_message failed: '.$dbh->errstr);
			myend(1);
		}
		$ctmsgid = $dbh->{'mysql_insertid'};
		if (!$ctmsgid) {
			syslog(LOG_ERR,"no CTMSGID!");
			myend(1);
		}
		$forward = 'forwardmsg';
		mycreatemail();
	}
	return 1;
}

sub find_email_in_html($) {
	my $part = shift;
	my $body = $part->bodyhandle;
	if (!$body) {
		return 0;
	}
	$type = lc $part->effective_type;
	if ($type !~ /^text\/html/) {
		syslog(LOG_INFO,"but successive message is no html but $type");
		return 0;
	}
	my $foundfrom = 0;
	LINE: for ($body->as_lines) {
		if (/From:/) {
			$foundfrom = 1;
		}
		if ($foundfrom) {
			if (/([a-zA-Z0-9\.\-_]+)\@([a-zA-Z0-9\.\-_]+)/) {
				$from_email = $1.'@'.$2;
				$from_name = $1;
				if (!is_valid_email($from_email)) {
					$invalid_from = 1;
				}
				if ($do_store) {
					my $quoted_from_email = $dbh->quote($from_email);
					my $quoted_from_name = $dbh->quote($from_name);
					$dbh->do("
					UPDATE contact_ticket_mail SET
						FROM_EMAIL	= $quoted_from_email,
						FROM_NAME	= $quoted_from_name
					WHERE CTMSGID = $ctmsgid"
					);
				}
				syslog(LOG_INFO,"found forwarded from_email: $from_email");
				syslog(LOG_INFO,"found forwarded from_name: $from_name");
				return 1;
			}
		}
	}
	return 0;
}
#sub decodingproblem($$) {
#	if (!$dostore) {
#		return;
#	}
#	my ($location,$problem) = @_;
#	$dbh->do("INSERT IGNORE INTO decodingproblem SET
#		STAMP	=$now,
#		LOCATION=$location,
#		TICKETID=$ticketid,
#		PROBLEM	=".$dbh->quote($problem)
#	);
#}
sub mycreatehtml($$$) {
	if (!$do_store) {
		return;
	}
	my ($bodystring, $type, $charset) = @_;
	my $quoted_type = $dbh->quote($type);
	my $quoted_charset = $dbh->quote($charset);
	my $size = length($bodystring);
	my $crc32 = crc32($bodystring);

	if (!$dbh->do("
		INSERT INTO contact_ticket_attachment_meta SET
			MIMETYPE	= $quoted_type,
/*			CHARSET		= $quoted_charset, */
			SIZE    	= $size,
			COMPRESSED	= 1,
			CRC			= $crc32"
		)
	) {
		syslog(LOG_ERR,"failed to store attachment meta: ".$dbh->errstr);
		myend(1);
	}
	my $dataid = $dbh->{'mysql_insertid'};
 	my $quoted_bodystring = $dbhe->quote($bodystring);
	if (!$dbhe->do("
		INSERT INTO data_db.contact_ticket_attachment_data SET
			DATAID	= $dataid,
			DATA    = COMPRESS($quoted_bodystring)")
	) {
		syslog(LOG_ERR,"failed to store (4) attachment data: ".$dbhe->errstr);
		myend(1);
	}
	if (!$dbh->do("
		INSERT INTO contact_ticket_attachment SET
			CTMSGID		=$ctmsgid,
			FILENAME	='message.html',
			DATAID		=$dataid,
			CSTAMP		=$now")
	) {
		syslog(LOG_ERR,"failed to store attachment: ".$dbh->errstr);
		myend(1);
	}
	syslog(LOG_INFO,"stored attachment with dataid $dataid");
}
sub mystoremessage($$$) {
	my ($body, $type, $charset) = @_;
	my @plainbodylines;
	my $onlyhtml = $type =~ /text\/html/;
	if ($onlyhtml) {
		syslog(LOG_INFO,"initial has only html");;
		my $str = $body->as_string;
		$str =~ s/<hr.*?>/<br \/?>\-\-<br \/?>/ig;
		my $tree = HTML::TreeBuilder->new->parse($str);
		my $formatter = HTML::FormatText->new('leftmargin' => 0, 'rightmargin' => 100000);
		my $plainbody = $formatter->format($tree);
#		print "\n\n\nGOT: $plainbody\n\n\n";
		@plainbodylines = split /\r?\n/, $plainbody;
	} else {
		@plainbodylines = $body->as_lines;
	}
	mycreatemessage(\@plainbodylines,$charset,$onlyhtml);
	mycreatemail();
	if ($onlyhtml) {
		mycreatehtml($body->as_string,$type,$charset);
	}
}
