#!/usr/bin/php
<?php

declare(strict_types=1);

require_once __DIR__.'/../../public_html/_run_and_exit.inc';
require_once __DIR__.'/../../public_html/_execute.inc';

run_and_exit();

function main(): bool {
	if (empty($argv[1])) {
		fwrite(STDERR, "need domain argument\n");
		return false;
	}
	$domain = $argv[1];

	$dir = opendir('.');
	if (!$dir) {
		fwrite(STDERR, "could not op current directory\n");
		return false;
	}
	$ok = true;
	while (false !== ($source = readdir($dir))) {
		if (preg_match("'^current_(?<file>$domain.*\$'u", $source, $match)) {
			$destination = "previous_{$match['file']}";
			if (file_exists($destination)) {
				if (!@unlink($destination)) {
					$error = error_get_last();
					fwrite("unlink $destination filed".(!empty($error['message']) ? ": {$error['message']}" : '')."\n");
					$ok = false;
					break;
				}
			}

			[$rc, $stdout, $stderr] = execute("git mv $source $destination");
			if ($rc) {
				fwrite(STDERR, "git mv $source → $destination failed".(!empty($stderr) ? ": $stderr" : '')."\n");
				$ok = false;
				break;
			}
		}
	}
	closedir($dir);
	return $ok;
}
