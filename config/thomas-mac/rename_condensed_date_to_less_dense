#!/usr/bin/perl

use strict;
use warnings;
use Data::Dumper;

my $dir;
my $debug = defined $ARGV[0] && ($ARGV[0] eq 'debug' || $ARGV[0] == '--debug') ? 1 : 0;

if (opendir $dir, '.') {
	while (my $file = readdir $dir) {
		if ($file =~ /^\./) {
			next;
		}
		
		if ($file =~ /^(\d{4}-\d{2}-\d{2} \d{2}):(\d{2}.*)$/) {
			print "mv '$file' '$1-$2'\n";
			next;
		}

		if ($file =~ /^(\d{4}-\d{2}-\d{2} \d{2})-(\d{2}.*)$/) {
			print "mv '$file' '$1.$2'\n";
			next;
		}
		
		if ($file =~ /^(?<private>private_)?(?<date>(?<year>19[89]\d|20(?:[012]\d|2[01234]))(?<month>0\d|1[012])(?<day>[012]\d|3[01]))(?:[-_,\s]*(?<time>(?<hour>[01]\d|2[0123])[\.:]?(?<minutes>[012345]\d)(?<seconds>\d\d)?))?(?<version>v\d+)?(?<remainder>[-_,\s]*.*?)?(?<extension>\.\w+)?$/) {
			if ($debug) {
				print STDERR "date or datetime detected in $file\n";
				print STDERR "date: $+{date}, year: $+{year}, month: $+{month}, day: $+{day}, hour: $+{hour}, minutes: $+{minutes}, seconds: $+{seconds}\n";
			}
			my $date = "$+{year}-$+{month}-$+{day}";
			my $time = '';

			if (defined $+{time}) {
				if ($debug) {
					print STDERR "time: $+{time}, hour: $+{hour}, minutes: $+{minutes}\n";
				}
				$time = " $+{hour}.$+{minutes}";
				if (defined $+{seconds}) {
					$time .= ".$+{seconds}";
				}
			}
			my $version = defined $+{version} ? " $+{version}" : '';
			my $remainder = '';
			if (defined $+{remainder}) {
				$remainder = $+{remainder};
				$remainder =~ s/_/ /g;
				$remainder =~ s/^\s+//;
				$remainder =~ s/\s+$//;
				if ($remainder) {
					$remainder = " $remainder";
				}
			}
			my $private = $+{private} ? 'private ' : '';
			my $extension = defined $+{extension} ? $+{extension} : '';
			print "mv \"$file\" \"$private$date$time$version$remainder$extension\"\n";
		} else {
			if ($debug) {
				print STDERR "file has no date or datetime prefix: $file\n";
			}
		}
	}
	closedir $dir ;
}

