# shellcheck disable=SC2034,SC3043,SC3037

DEBIAN_FRONTEND=noninteractive
APT_NONINTERACTIVE="apt --assume-yes"
INSTALL_PHP_VERSION="8.4"

install_apparmor_config() {
	local IS_SERVICE DONT
	if [ "$1" = "dont_start_stop" ]; then
		DONT=1
	elif [ -z "$DONT" ]; then
		IS_SERVICE="-e '/lib/systemd/system/$1.service'"
		if [ "$IS_SERVICE" ]; then
			systemctl stop "$1"
		fi
	fi
	if [ -z "$DONT" ]; then
		systemctl stop apparmor
	fi
	rm -rf "$2"
	link_config "etc/apparmor.d/$2"
	if [ -z "$DONT" ]; then
		systemctl start apparmor
		if [ "$IS_SERVICE" ]; then
			systemctl start "$1"
		fi
	fi
}

link_config() {
	# Don't use realpath on link_target, so everything is housed in /home/<USER>
	# and we could move the entire directory to somewhere else and just symlink to there from /home/<USER>
	local link_target link_name
	if [ -z "$2" ]; then
		local git_root real_target stripped_target
		git_root=$(git rev-parse --show-toplevel)
		real_target=$(realpath "$1")
		stripped_target=${real_target#"$git_root"}
		link_target="/home/<USER>/$stripped_target"
		link_name="/$1"
	else
		link_target="$1"
		link_name="$2"
	fi
	if [ -r "$link_name" ]; then
		if [ -n "$(diff "$link_name" "$link_target")" ]; then
			mv "$link_name" "$link_name-$(date +%Y%m%d.%H%M%S)"
		else
			rm -f "$link_name"
		fi
	else
		mkdir -p "$(dirname "$link_name")"
	fi
	rm -f "$link_name"
	ln -s "$link_target" "$link_name"
}

dont_start_services() {
	echo -e "#!/bin/sh\nexit 101\n" > /usr/sbin/policy-rc.d
	chmod uga+rx /usr/sbin/policy-rc.d
	if [ -n "$1" ]; then
		if [ -x "/etc/init.d/$1" ]; then
			"/etc/init.d/$1" stop
		elif [ -e "/lib/systemd/system/$1.service" ]; then
			systemctl stop "$1"
		fi
	fi
}

do_start_services() {
	rm -f /usr/sbin/policy-rc.d
	if [ -n "$1" ]; then
		if [ -x "/etc/init.d/$1" ]; then
			"/etc/init.d/$1" start
		elif [ -e "/lib/systemd/system/$1.service" ]; then
			systemctl start "$1"
		fi
	fi
}

install_imagemagick_7() {
# NOTE: Still some other stuff depends on these imagick packages.
#		Notably VIPS will be deinstalled too, if all packages below are purged
#
#	$APT_NONINTERACTIVE purge \
#		imagemagick \
#		imagemagick-6-common \
#		imagemagick-6.q16

	$APT_NONINTERACTIVE install \
		php-tokenizer

	phpenmod tokenizer

	$APT_NONINTERACTIVE install \
		libvips-dev \
		libvips-tools

	t=$(mktemp) && \
	wget "https://dist.1-2.dev/imei.sh" -qO "$t" && \
	bash "$t" && \
	rm "$t"

	link_config \
		/home/<USER>/config/generic/etc/php/mods-available/imagick.ini \
		/etc/php/$INSTALL_PHP_VERSION/mods-available/imagick.ini

	mkdir -p /usr/local/etc/ImageMagick-7
	link_config usr/local/etc/ImageMagick-7/policy.xml
	#link_config "$SCRIPT_DIR"/../generic/usr/local/etc/ImageMagick-7/policy.xml /usr/local/etc/ImageMagick-7/policy.xml

    pecl uninstall channel://pecl.php.net/imagick-3.8.0
    pecl install channel://pecl.php.net/imagick-3.8.0
    phpenmod imagick
}

#function install_libvips {
#	$APT_NONINTERACTIVE install meson
#	PWD=$(pwd)
#	BUILD_DIR="/tmpdisk/__make_lipvips_$RANDOM"
#	mkdir -p "$BUILD_DIR"
#	cd "$BUILD_DIR"
#	wget https://github.com/libvips/libvips/releases/download/v8.15.2/vips-8.15.2.tar.xz
#	tar xvJf vips-8.15.2.tar.xz
#	cd vips-8.15.2
#	cd "$PWD"
#}

install_iptables() {
	$APT_NONINTERACTIVE install iptables
	local IPTABLES IPv6TABLES

	IPTABLES="$(realpath iptables.rules)"
	IP6TABLES="$(realpath ip6tables.rules)"

	echo WARNING\! Follow instructions and change manually:
	echo
	echo add to correct interface: pre-up   iptables-restore  \< "$IPTABLES"
	echo add to correct interface: pre-down iptables-save -c  \> "$IPTABLES"
	echo add to correct interface: pre-up   ip6tables-restore \< "$IP6TABLES"
	echo add to correct interface: pre-down ip6tables-save -c \> "$IP6TABLES"
	echo
}
