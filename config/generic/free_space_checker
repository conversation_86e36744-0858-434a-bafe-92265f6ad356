#!/usr/bin/perl -w

use strict;
use IO::File;
use Fcntl ':flock';
use Sys::Hostname;
use Sys::Syslog qw(LOG_DEBUG LOG_INFO LOG_NOTICE LOG_ERR LOG_WARNING LOG_CRIT openlog syslog);

my $debug = !defined $ENV{'CRON'};

if (!openlog('free_space_checker', $debug ? 'pid,perror' : 'pid', 'user')) {
	print STDERR "failed to open syslog: $!\n";
	exit 1;
}

my $hostname = hostname();
my $warned = 0;

foreach ('', '--inodes') {
	my $cnt = 0;
	my $inodes = $_;
	my $command = "timeout --verbose --kill-after=20 10 df --block-size=1 --local $inodes 2>&1";
	syslog(LOG_INFO, "executing: $command");
	DF_LINE: for (qx($command)) {
		if (!$cnt++) {
			# Skip the header.
			next DF_LINE;
		}
		chomp;
		if (/^\//) {
			if (/^(?<device>\/dev\/[a-z]+\d+)\s+(?<total>\d+)\s+(?<used>\d+)\s+(?<free>\d+)\s+(?<percentage_in_use>\d+%)\s+(?<mount_point>\/.*)$/) {
				my $percentage_free = 100 * $+{free} / $+{total};
				my $free = $+{free};
				my $what;
				if ($inodes) {
					$what = 'inodes';
				} else {
					$what = 'MiB';
					$free = int($free / 1048576);
				}
				syslog(LOG_INFO, sprintf("device $+{device}, $free $what free = %.1f%%", $percentage_free));

				if (send_warning($+{device}, $percentage_free, $+{free}, $+{mount_point}, $inodes)) {
					$warned = 1;
				}
			} else {
				my $error;
				syslog(LOG_CRIT, $error = "df entry starts with slash but not recognized:\n$_\n");
				print STDERR $error;
				$warned = 1;
			}
		}
	}
	if ($?) {
		my $error_type;
		my $exit_code = $? >> 8;
		if ($exit_code == 124) {
			$error_type = 'timeout';
		} elsif ($exit_code == 125) {
			$error_type = 'timeout failed';
		} elsif ($exit_code == 126) {
			$error_type = 'command found but cannot be invoked';
		} elsif ($exit_code == 127) {
			$error_type = 'command could not be found';
		} elsif ($exit_code == 137) {
			$error_type = 'command was killed';
		} else {
			$error_type = 'somer error';
		}
		my $error;
		syslog(LOG_CRIT, $error = $error_type.' while executing '.($inodes ? "'df --inodes'" : "'df'").($! ? ": $!" : '')."\n");
		print STDERR $error;
		$warned = 1;
	}
}

exit $warned;

sub send_warning {
	my ($device, $percentage_free, $free, $mount_point, $inodes) = @_;

	my $category;
	my $how_much;
	if ($percentage_free <= 1) {
		$category = 1;
		$how_much = 'critically';
	} elsif ($percentage_free <= 3) {
		$category = 2;
		$how_much = 'extremely';
	} elsif ($percentage_free <= 5) {
		$category = 3;
		$how_much = 'very';
	} else {
		return 0;
	}

	my $what = $inodes ? 'inodes' : 'disk_space';
	my $show_failures = 1;
	my ($seconds, $minuntes, $hour, $month_day, $month, $year, $weekday, $year_day, $is_dst) = localtime(time);

	my $device_for_file = $device;

	$device_for_file =~ s/\//_/g;

	my $status_file_name;
	my $status_file;
	if (sysopen $status_file, $status_file_name = '/tmp/__freespacheck_lastmail_'.$what.$device_for_file, IO::File::O_RDWR | IO::File::O_CREAT) {
		flock($status_file, LOCK_EX);
		my $now_mail = sprintf("%4d%02d%02d%d", $year + 1900, $month, $month_day, $category);
		my $last_mail;
		if (9 == sysread($status_file, $last_mail, 9)
		&&	$last_mail eq $now_mail
		) {
			$show_failures = 0;
		} else {
			sysseek($status_file, 0, SEEK_SET);
			syswrite($status_file, $now_mail, 9, 0);
		}
		close $status_file;
	} else {
		my $error = "status file '$status_file_name' could not be openend: $!";
		print STDERR "$error\n";
		syslog(LOG_ERR, $error);
	}
	if ($show_failures) {
		my $percentage_free = sprintf('%.1f', $percentage_free);
		my $what_is_free;
		if ($inodes) {
			$what_is_free = 'inodes';
		} else {
			$what_is_free = 'MiB';
			$free = int($free / 1048576);
		}
		my $error = "on host $hostname, device $device: free $what $how_much low! only $free $what_is_free, $percentage_free% available!";
		print STDERR "$error\n";
		syslog(LOG_ERR, $error);
	}
	return 1;
}
