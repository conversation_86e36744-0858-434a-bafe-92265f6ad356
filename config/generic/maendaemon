#!/usr/bin/perl

use strict;
use warnings;
use Socket;
use Socket::GetAddrInfo qw(getnameinfo);
use Sys::Hostname;
use Sys::Syslog qw(LOG_DEBUG LOG_INFO LOG_NOTICE LOG_ERR LOG_WARNING LOG_CRIT openlog syslog);
use IO::Socket::INET6 qw(inet_ntoa inet_ntop);
use IO::Socket::SSL;
use Net::SSLeay;
use Time::HiRes qw(usleep);
use IPC::Open3;
use File::Basename;
use Errno;
use lib '/home/<USER>/config';
use perlinclude::execute;
use perlinclude::findapache;
use perlinclude::secret;
use experimental;
#no warnings 'experimental::builtin';
use feature qw{state};
use experimental qw(builtin);
use builtin qw{true false};

my @statuses = ('offline', 'read-only', 'no-mail', 'updating', 'loaded');

$| = 1;
$/ = "\n";

my $read_timeout = 30;
my $exec_timeout = 30;
my $host_name = hostname();
my $now = time();

my $MAX_LOG_OFFSET = 10 * 1024 * 1024;

my $debug = false;
my $have_secret = false;

if (!openlog('maendaemon', 'pid', 'daemon')) {
	print STDERR "failed to open syslog: $!\n";
	exit 1;
}

for (@ARGV) {
	syslog(LOG_INFO, "got argument: $_");
	if ($_ eq 'debug') {
		$debug = true;
		next;
	}

}

my $ssl = false;
my $other = getpeername(STDIN);
if ($other) {
	my $me = getsockname(STDIN);
	my $family = sockaddr_family $other;
	my $herhostname;
	my $heripstring;
	my $iaddr;
	my $port = 0;
	my $err;

	if ($family == AF_INET) {
		(undef, $iaddr) = unpack_sockaddr_in($other);
		($port) = unpack_sockaddr_in($me);
		$herhostname = gethostbyaddr($iaddr, AF_INET);
		$heripstring = inet_ntoa($iaddr);

	} elsif ($family == AF_INET6) {
		(undef, $iaddr) = unpack_sockaddr_in6($other);
		($port) = unpack_sockaddr_in6($me);
		($err, $herhostname) = getnameinfo($iaddr);
		$heripstring = inet_ntop(AF_INET6, $iaddr);

	} else {
		syslog(LOG_ERR, 'unknown family tried to connect: '.$family);
		exit 1;
	}
	if ($herhostname) {
		syslog(LOG_NOTICE, "connect from $herhostname, $heripstring");
	}
	if ($port == 60000) {
		$Net::SSLeay::ssl_version = 10;
		Net::SSLeay::load_error_strings();
		Net::SSLeay::SSLeay_add_ssl_algorithms();

		my $ctx = Net::SSLeay::CTX_new ();

		if (!Net::SSLeay::set_server_cert_and_key($ctx,
				'/home/<USER>/config/certs/current_partyflock.nl.crt',
				'/home/<USER>/config/certs/current_partyflock.nl.key')
		) {
			syslog(LOG_ERR, 'ssl key problem', $!);
			exit 1;
		}

		$ssl = Net::SSLeay::new($ctx);

		Net::SSLeay::set_rfd($ssl, fileno(STDIN));
		Net::SSLeay::set_wfd($ssl, fileno(STDOUT));

		my $rv = Net::SSLeay::accept($ssl);
		if ($rv <= 0) {
			syslog_warning(LOG_WARNING, 'ssl handshake failed');
			exit 1;
		}
		pf_set_ssl($ssl);
	}
} else {
	syslog(LOG_INFO, 'started from console');
}

syslog(LOG_INFO, 'waiting for KEY');
ready($host_name, '@', $now, ':NEEDKEY');

eval {
	local $SIG{ALRM} = sub { die "alarm\n" };
	alarm $read_timeout;
	while ($_ = $ssl ? Net::SSLeay::ssl_read_LF($ssl) : <STDIN>) {
		alarm 0;
		chomp($_);
		if ($_ eq secret()) {
			$have_secret = true;
			last;
		}
	} continue {
		alarm $read_timeout;
	}
	alarm 0;
};
if ($@) {
	die unless $@ eq "alarm\n"; # propagate unexpected errors
	local_error("maendaemon timed out while waiting for secret key");
	bail(1);
}
if (!$have_secret) {
	local_error("no proper key received");
	bail(1);
}
set_max_execution_time(30);
ready($host_name,'@',$now,':READY');

my $auto_close = false;
my $cd = undef;
eval {
	local $SIG{ALRM} = sub { die "alarm\n" };
	alarm $read_timeout;
	COMMAND: while ($_ = $ssl ? Net::SSLeay::ssl_read_LF($ssl) : <STDIN>) {
		alarm 0;
		chomp($_);
		syslog(LOG_DEBUG, "got command: $_");

		if (/^(.*)autoclose\.(.*)$/) {
			$auto_close = 1;
			$_ = $1.$2;
		}
		if (/^(.*)exectimeout=(\d+)\.(.*)$/) {
			set_max_execution_time($2);
			$_ = $1.$3;
		}
		if (/^(.*)cd=([^\s]*)\.(.*)$/) {
			$cd = $2;
			$_ = $1.$3;
		}
		if ($_ eq 'quit') {
			last COMMAND;
		}
		if ($_ eq 'webupdate') {
			my $oldexectimeout = set_max_execution_time(600);
			execute_within_daemon('/home/<USER>/config/generic/updategit', '/home/<USER>');
			set_max_execution_time($oldexectimeout);
			next COMMAND;
		}
		if (/^execute (.*)$/) {
			$ENV{'DEBIAN_FRONTEND'} = 'noninteractive';
			$ENV{'PATH'} = '/bin:/sbin:/usr/sbin:/usr/bin:/usr/local/bin:/usr/local/sbin:/home/<USER>/config/generic:/home/<USER>/config/mysql';
			if (defined $cd) {
				chdir $cd;
			}
			for (split(';', $1)) {
				execute_within_daemon($_);
			}
			next COMMAND;
		}
		if (/^loglines:(?<log_dir>[^:]+):(?<log_name>[^:]+):(?<stamp>\d+):(?<offset>\d+)$/) {
			loglines($+{log_dir}, $+{log_name}, $+{stamp}, $+{offset});
			next COMMAND;
		}
		if ($_ eq 'logs_overview') {
			logs_overview();
			next COMMAND;
		}
		# FIXME: Block below seems unused
		if (0 && /apache/) {
			my $init_script = '/etc/init.d/apache2';
			my $graceful;
			if (!-f $init_script) {
				local_error('apache init script not found: ', $init_script);
				next COMMAND;
			}
			if ($_ eq 'restartapache') {
				my ($running, $apache_PID, $type) = findapache();
				if (!$apache_PID) {
					execute_within_daemon($init_script, 'start');
				} else {
					execute_within_daemon($init_script, 'reload');
					my $check_count = 0;
					APACHE_CHECK: while ($check_count < 10) {
						my ($new_running, $new_apache_PID, $new_type) = findapache();
						if (!$new_running
						||	$new_apache_PID != $apache_PID
						) {
							# NOTE: Not running or same PID before, so restart failed
							next;
						}
						++$check_count;
					}
					if (!$running) {
						execute_within_daemon($init_script, 'start');
					}
				}
				next COMMAND;
			}
			if (/^start[_-]?apache$/) {
				execute_within_daemon($init_script, 'start');
				next COMMAND;
			}
			if (/^(?:apache\s*(?:graceful|reload)|(?:graceful|reload)\s*apache)$/) {
				execute_within_daemon($init_script, 'reload');
				next COMMAND;
			}
		}
		state $regex = '^(?<undo>undo\s*)?(?<status>'.join('|', @statuses).')(?::(?<comment>.+))?$';

		if (m{$regex}) {
			if ($+{undo}) {
				if (!unlink "/$+{status}") {
					local_error("failed to unlink /$+{status}: ", $!);
				}
			} else {
				if (!open STATEFILE, ">/$+{status}") {
					local_error("failed to open /$+{status} for writing: $0");
				} else {
					# print STATEFILE "\x06";
					if ($+{comment}) {
						print STATEFILE $+{comment};
					}
					close STATEFILE;
				}
			}
			next COMMAND;
		}
		if ($_ eq 'online') {
			foreach (@statuses) {
				if (!-f "/$_") {
					next;
				}
				if (!unlink "/$_") {
					local_error("failed to unlink /$_: $!");
					next;
				}
				notice("undone: $_");
			}
			next COMMAND;
		}
		local_error('command not understood: ', $_);
	} continue {
		if ($auto_close) {
			last COMMAND;
		}
		ready($host_name,'@',$now,':READY');
		alarm $read_timeout;
	}
	alarm 0;
};
if ($@) {
	die unless $@ eq "alarm\n";   # propagate unexpected errors
	local_error('maendaemon timeout while waiting for commands');
}

bail(0);

sub logs_overview {
	if (-d '/www') {
		enter_log_dir('/www');
	}
	if (-d '/var/log/apache2') {
		enter_log_dir('/var/log/apache2');
	}
	if (-d '/var/log/php') {
		enter_log_dir('/var/log/php');
	}
	foreach (glob('/var/log/php*fpm.log')) {
		addlogfile($_);
	}
}
sub addlogfile {
	my $file_name = shift;
	my $base_name = basename($file_name);
	my $dir_name = dirname($file_name);
	my @info = stat($file_name);
	notice("LOGSTATUS:$dir_name:$base_name:$info[9]:$info[7]");
}
sub enter_log_dir {
	my $check_dir = shift;
	my $LOG_DIR;
	if (!opendir $LOG_DIR, $check_dir) {
		local_error("failed to open dir $check_dir, for listing: ", $!);
		return;
	}
	DIRENTRY: for (readdir $LOG_DIR) {
		if (/^\./) {
			next DIRENTRY;
		}
		if (/^((?:.*_)?error\.log|php\d+\.\d+-fpm\.log)$/) {
			my @info = stat("$check_dir/$1");
			notice("LOGSTATUS:$check_dir:$1:$info[9]:$info[7]");
		} elsif (-d (my $new_dir = "$check_dir/$_")) {
			enter_log_dir($new_dir);
		}
	}
	closedir $LOG_DIR;
}

sub loglines {
	my ($logdir, $logname, $stamp, $offset) = @_;
	my $LOG_DIR;
	if (!opendir $LOG_DIR, $logdir) {
		local_error("failed to open logdir $logdir for listing", $!);
		return;
	}
	my $new_mode = false;
	for (readdir $LOG_DIR) {
		if (/^\./) {
			next;
		}
		if (/^$logname(?:\.(\d+))?$/) {
			if (!$1) {
				$new_mode = true;
				next;
			}
			if ($1 > $stamp) {
				$stamp = $1;
			}
		}
	}
	closedir $LOG_DIR;
	my $logfile;
	if (!$new_mode) {
		if (!$stamp) {
			notice("LOGSTATUS:$logdir:$logname:$stamp:0");
			return;
		}
		$logfile = "$logdir/$logname.$stamp";
	} else {
		$logfile = "$logdir/$logname";
	}
	my @info = stat($logfile);
	if ($#info == -1) {
		local_error("failed to stat logfile $logfile: ", $!);
		return;
	}
	my $filesize = $info[7];
	if ($filesize - $offset > $MAX_LOG_OFFSET) {
		$offset = $filesize - $MAX_LOG_OFFSET;
		local_error("LOGFILE $logfile GROWING TOO FAST!!");
	}

	if ($filesize > $offset
	||	$new_mode
	&&	$info[9] > $stamp
	) {
		if ($new_mode) {
			$stamp = $info[9];
		}
		if (open LOGFILE, "<$logfile") {
			if ($offset) {
				if (!seek LOGFILE, $offset, 0) {
					local_error("failed to seek file $logfile to offset $offset: ", $!);
					return;
				}
			}
			my $data = '';
			my $moredata;
			while (my $readbytes = read(LOGFILE, $moredata, 1024*1024)) {
				$data   .= $moredata;
				$offset += $readbytes;
			}
			close(LOGFILE);
			notice("LOGSTATUS:$logdir:$logname:$stamp:$offset");
			$data =~ s/\n+$//;
			#$data =~ s/\n/\r\n/g;
			pass_notice($data);
		} else {
			local_error("failed to open $logfile for reading: ", $!);
		}
	} elsif ($filesize < $offset) {
		notice("LOGSTATUS:$logdir:$logname:$stamp:$filesize");
	} else {
		notice('LOGSTATUS:nochange');
	}
}

sub bail {
	my $rv = shift;
	close STDIN;
	close STDOUT;
	close STDERR;
	syslog(LOG_INFO, "exiting with return code $rv");
	exit $rv;
}

sub local_error {
	syslog(LOG_ERR, join('', @_));
	error(@_);
}
