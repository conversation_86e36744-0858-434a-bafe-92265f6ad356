#!/bin/bash

set -e		# exit immediately on failure
set -x -v	# print input & print shell commands

. install.include.sh

function uninstall_imagemagick_6 {
	$APT_NONINTERACTIVE purge \
		imagemagick-6-common \
		libgraphicsmagick-q16-3 \
		libmagickcore-6-arch-config \
		libmagickcore-6-headers \
		libmagickcore-6.q16-6 \
		libmagickcore-6.q16-6-extra \
		libmagickcore-6.q16-dev \
		libmagickcore-dev \
		libmagickwand-6-headers \
		libmagickwand-6.q16-6 \
		libmagickwand-6.q16-dev \
		libmagickwand-dev
}

function uninstall_libvips {
	$APT_NONINTERACTIVE purge \
		libvips \
		libvips-dev
	
	pecl -v uninstall vips
}

case "$1" in
imagick-6)	uninstall_imagemagick_6
		;;
libvips)	uninstall_libvips
		;;
*)		echo "what to uninstall?"
		;;
esac
