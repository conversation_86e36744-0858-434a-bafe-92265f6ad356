#!/bin/bash

set -e
set -x -v

OPCODE_CACHE_PATH=/tmp/php-opcode-cache

# Make the opcode-cache-directory and make sure new files will be readable by the group
mkdir --parents --mode 002 "$OPCODE_CACHE_PATH"

# Let's give ownership to user www-data and group php-opcode-cache
chown www-data:php-opcode-cache "$OPCODE_CACHE_PATH"

# Make the directory readable, writeable and traversable, and set 
# Make sure files and directories created in the cache path belong to the php-opcode-cache group
chmod ug+rwx,g+s "$OPCODE_CACHE_PATH"

# Set default ACL for the cache dir
setfacl -R -m d:g:php-opcode-cache:rwx "$OPCODE_CACHE_PATH"
setfacl -R -m   g:php-opcode-cache:rwx "$OPCODE_CACHE_PATH"
