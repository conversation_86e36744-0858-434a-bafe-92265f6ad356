#!/bin/bash

set -e

# If no match for glob, remove it, instead of passing as-is:
shopt -s nullglob

YEAR=$(date +%Y)
PREVIOUS_YEAR=$(($YEAR-1))

for log_file in \
	/var/log/*-$YEAR???? \
	/var/log/*-$PREVIOUS_YEAR???? \
	/var/log/*/*-$YEAR???? \
	/var/log/*/*-$PREVIOUS_YEAR???? \
	/var/log/*/*/*-$YEAR???? \
	/var/log/*/*/*-$PREVIOUS_YEAR???? \
	/db/*-$YEAR???? \
	/db/*-$PREVIOUS_YEAR????
do
	ionice -c 3 nice -n 19 zstd --ultra -22 --rm -T0 --auto-threads=logical $@ $log_file
done
