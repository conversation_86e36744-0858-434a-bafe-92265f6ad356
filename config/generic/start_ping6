#!/bin/sh

#set -e	# exit immediately on failure
#set -x	# print input
#set -v	# print shell commands

# This starts a daemonize ping6 to our gateway.
# This forces neighor sollicitations from the gateway.

if [ "$1" = "--quiet" ] \
|| [ "$1" = "quiet" ] \
|| [ "$1" = "-q" ]; then
	QUIET=1
	shift
fi

HOSTNAME=$(hostname)

#if [ "$HOSTNAME" = 'preparty' ] \
#|| [ "$HOSTNAME" = 'partyflock.nl' ] \
#|| [ "$HOSTNAME" = 'webx' ] \
#|| [ "$HOSTNAME" = 'vip.partyflock.nl' ] \
#|| [ "$HOSTNAME" = 'cron' ] \
#|| [ "$HOSTNAME" = 'cron.partyflock.nl' ] \
#|| [ "$HOSTNAME" = 'photo-upload' ] \
#|| [ "$HOSTNAME" = 'photo-upload.partyflock.nl' ]; then
#	if [ -z "$QUIET" ]; then
#		echo "This host '$HOSTNAME' has a direct link to the gateway, no need to ping to trigger neighbor solicitations!" >&2
#	fi
#	exit;
#fi

while [ "$#" -gt 0 ]; do
	case "$1" in
	--tell-if-running)		tell_if_running=1;		shift;;
	--tell-if-not-running)	tell_if_not_running=1;	shift;;
	*)						echo "Unknown parameter: $1" >&2; exit 1;;
	esac
done

COMMAND='/usr/bin/ping6 -i 5 2a03:6980:1::61:1'

if [ -z "$(pgrep --full --exact -- "$COMMAND")" ]; then
	daemonize -p /run/ping6er.pid $COMMAND
	if [ $tell_if_not_running ] \
	|| [ -z "$QUIET" ]; then
		echo "ping6 started"
	fi
else
	if [ $tell_if_running ] \
	|| [ -z "$QUIET" ]; then
		echo "ping6 already started" >&2
	fi
fi

