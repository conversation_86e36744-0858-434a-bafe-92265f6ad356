#default word color
#regexp=[\w.,\:\-_/]+
regexp=.+
colours=white
-

#table borders
regexp=[+\-]+[+\-]|[|]
colours=bright_blue
-

#data in ( ) and ' '
regexp=\([\w\d,']+\)
colours=white
-

#numeric
regexp=\s[\d\.]+\s*($|(?=\|))
colours=yellow
-

#date
regexp=\d{4}-\d{2}-\d{2}
colours=cyan
-
#time
regexp=\d{2}:\d{2}:\d{2}
colours=bright_blue
-

#IP
regexp=(\d{1,3}\.){3}\d{1,3}(:\d{1,5})?
colours=cyan
-

#schema
regexp=`\w+`
colours=yellow
-

#email
regexp=[\w\.\-_]+@[\w\.\-_]+
colours=magenta
-

#row delimeter when using \G key
regexp=[*]+.+[*]+
count=stop
colours=white
-

#column names when using \G key
regexp=^\s*\w+:
colours=white
