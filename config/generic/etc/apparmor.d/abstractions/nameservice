# ------------------------------------------------------------------
#
#    Copyright (C) 2002-2009 Novell/SUSE
#    Copyright (C) 2009-2011 Canonical Ltd.
#
#    This program is free software; you can redistribute it and/or
#    modify it under the terms of version 2 of the GNU General Public
#    License published by the Free Software Foundation.
#
# ------------------------------------------------------------------

  abi <abi/3.0>,

  # Many programs wish to perform nameservice-like operations, such as
  # looking up users by name or id, groups by name or id, hosts by name
  # or IP, etc. These operations may be performed through files, dns,
  # NIS, NIS+, LDAP, hesiod, wins, etc. Allow them all here.
  @{etc_ro}/group          r,
  @{etc_ro}/host.conf      r,
  @{etc_ro}/hosts          r,
  /home/<USER>/config/generic/etc/hosts r,
  /mnt/largepart/party/config/generic/etc/hosts r,
  /mnt/largepart/home/<USER>/config/generic/etc/hosts r,
  /archive/git/party_working/config/generic/etc/hosts r,
  /db/home/<USER>/config/generic/etc/hosts r,
  @{etc_ro}/nsswitch.conf  r,
  @{etc_ro}/gai.conf       r,
  @{etc_ro}/passwd         r,
  @{etc_ro}/protocols      r,

  # libtirpc (used for NIS/YP login) needs this
  @{etc_ro}/netconfig r,

  # When using libnss-extrausers, the passwd and group files are merged from
  # an alternate path
  /var/lib/extrausers/group  r,
  /var/lib/extrausers/passwd r,

  # When using sssd, the passwd and group files are stored in an alternate path
  # and the nss plugin also needs to talk to a pipe
  /var/lib/sss/mc/group   r,
  /var/lib/sss/mc/initgroups r,
  /var/lib/sss/mc/passwd  r,
  /var/lib/sss/pipes/nss  rw,

  @{etc_ro}/resolv.conf r,
  # On systems where /etc/resolv.conf is managed programmatically, it is
  # a symlink to @{run}/(whatever program is managing it)/resolv.conf.
  @{run}/{resolvconf,NetworkManager,systemd/resolve,connman,netconfig}/resolv.conf r,
  @{etc_ro}/resolvconf/run/resolv.conf  r,
  @{run}/systemd/resolve/stub-resolv.conf r,
  /mnt/wsl/resolv.conf r,
  /home/<USER>/config/generic/etc/resolv.conf r,
  /mnt/largepart/party/config/generic/etc/resolv.conf r,
  /mnt/largepart/home/<USER>/config/generic/etc/resolv.conf r,
  /archive/git/party_working/config/generic/etc/resolv.conf r,
  /db/home/<USER>/config/generic/etc/resolv.conf r,

  @{etc_ro}/samba/lmhosts  r,
  @{etc_ro}/services       r,
  # db backend
  /var/lib/misc/*.db      r,
  # The Name Service Cache Daemon can cache lookups, sometimes leading
  # to vast speed increases when working with network-based lookups.
  @{run}/.nscd_socket   rw,
  @{run}/nscd/socket    rw,
  /{var/db,var/cache,var/lib,var/run,run}/nscd/{passwd,group,services,hosts}    r,
  # nscd renames and unlinks files in it's operation that clients will
  # have open
  @{run}/nscd/db*  rmix,

  # The nss libraries are sometimes used in addition to PAM; make sure
  # they are available
  /{usr/,}lib{,32,64}/libnss_*.so*      mr,
  /{usr/,}lib/@{multiarch}/libnss_*.so*      mr,
  @{etc_ro}/default/nss               r,

  # avahi-daemon is used for mdns4 resolution
  @{run}/avahi-daemon/socket rw,

  # libnl-3-200 via libnss-gw-name
  @{PROC}/@{pid}/net/psched r,
  @{etc_ro}/libnl-*/classid r,

  # nis
  include <abstractions/nis>

  # ldap
  include <abstractions/ldapclient>

  # winbind
  include <abstractions/winbind>

  # likewise
  include <abstractions/likewise>

  # mdnsd
  include <abstractions/mdns>

  # kerberos
  include <abstractions/kerberosclient>

  #libnss-systemd
  include <abstractions/nss-systemd>

  # Also allow lookups for systemd-exec's DynamicUsers via D-Bus
  #   https://www.freedesktop.org/software/systemd/man/systemd.exec.html
  dbus send
       bus=system
       path="/org/freedesktop/systemd1"
       interface="org.freedesktop.systemd1.Manager"
       member="{GetDynamicUsers,LookupDynamicUserByName,LookupDynamicUserByUID}"
       peer=(name="org.freedesktop.systemd1"),

  # TCP/UDP network access
  network inet  stream,
  network inet6 stream,
  network inet  dgram,
  network inet6 dgram,

  # TODO: adjust when support finer-grained netlink rules
  # Netlink raw needed for nscd
  network netlink raw,

  # interface details
  @{PROC}/@{pid}/net/route r,

  # Include additions to the abstraction
  include if exists <abstractions/nameservice.d>
