# The first element of the path is a directory where the debian-sa1
# script is located
PATH=/usr/lib/sysstat:/usr/sbin:/usr/sbin:/usr/bin:/sbin:/bin

# Activity reports every minute everyday
#5-55/10 * * * * root command -v debian-sa1 > /dev/null && debian-sa1 1 1
# Activity reports every minute everyday
*/1 * * * * root command -v debian-sa1 > /dev/null && debian-sa1 1 1

# Additional run at 23:59 to rotate the statistics file
# 59 23 * * * root command -v debian-sa1 > /dev/null && debian-sa1 60 2
# No additional run, because we already run every minute