# needrestart - <PERSON>art daemons after library updates.
#
# This is the configuration file of needrest<PERSON>. This is perl syntax.
# needrestart uses reasonable default values, you might not need to
# change anything.
#

# Verbosity:
#  0 => quiet
#  1 => normal (default)
#  2 => verbose
#$nrconf{verbosity} = 2;

# Path of the package manager hook scripts.
#$nrconf{hook_d} = '/etc/needrestart/hook.d';

# Path of user notification scripts.
#$nrconf{notify_d} = '/etc/needrestart/notify.d';

# Path of restart scripts.
#$nrconf{restart_d} = '/etc/needrestart/restart.d';

# Disable sending notifications to user sessions running obsolete binaries
# using scripts from $nrconf{notify_d}.
#$nrconf{sendnotify} = 0;

# If needrestart detects systemd it assumes that you use systemd's pam module.
# This allows needrest<PERSON> to easily detect user session. In case you use
# systemd *without* pam_systemd.so you should set has_pam_systemd to false
# to enable legacy session detection!
#$nrconf{has_pam_systemd} = 0;

# Restart mode: (l)ist only, (i)nteractive or (a)utomatically.
#
# ATTENTION: If needrest<PERSON> is configured to run in interactive mode but is run
# non-interactive (i.e. unattended-upgrades) it will fallback to list only mode.
#
#$nrconf{restart} = 'i';
$nrconf{restart} = 'a';

# Use preferred UI package.
#$nrconf{ui} = 'NeedRestart::UI::stdio';

# Change default answer to 'no' in (i)nteractive mode.
#$nrconf{defno} = 1;

# Set UI mode to (e)asy or (a)dvanced.
#$nrconf{ui_mode} = 'e';

# Print a combined `systemctl restart` command line for skipped services.
#$nrconf{systemctl_combine} = 1;

# Blacklist binaries (list of regex).
$nrconf{blacklist} = [
    # ignore sudo (not a daemon)
    qr(^/usr/bin/sudo(\.dpkg-new)?$),

    # ignore DHCP clients
    qr(^/sbin/(dhclient|dhcpcd5|pump|udhcpc)(\.dpkg-new)?$),

    # ignore apt-get (Debian Bug#784237)
    qr(^/usr/bin/apt-get(\.dpkg-new)?$),
];

# Blacklist services (list of regex) - USE WITH CARE.
# You should prefer to put services to $nrconf{override_rc} instead.
# Any service listed in $nrconf{blacklist_rc} will be ignored completely!
#$nrconf{blacklist_rc} = [
#];

# Override service default selection (hash of regex).
#$nrconf{override_rc}
$nrconf{override_rc} = {
    # DBus
    qr(^dbus) => 0,

    # display managers
    qr(^gdm) => 0,
    qr(^kdm) => 0,
    qr(^nodm) => 0,
    qr(^sddm) => 0,
    qr(^wdm) => 0,
    qr(^xdm) => 0,
    qr(^lightdm) => 0,
    qr(^slim) => 0,
    qr(^lxdm) => 0,

    # networking stuff
    qr(^bird) => 0,
    qr(^network) => 0,
    qr(^NetworkManager) => 0,
    qr(^ModemManager) => 0,
    qr(^wpa_supplicant) => 0,
    qr(^openvpn) => 0,
    qr(^quagga) => 0,
    qr(^frr) => 0,
    qr(^tinc) => 0,
    qr(^(open|free|libre|strong)swan) => 0,
    qr(^bluetooth) => 0,

    # gettys
    qr(^getty@.+\.service) => 0,
    qr(^serial-getty@.+\.service) => 0,

    # systemd --user
    qr(^user@\d+\.service) => 0,

    # misc
    qr(^zfs-fuse) => 0,
    qr(^mythtv-backend) => 0,
    qr(^xendomains) => 0,
    qr(^lxcfs) => 0,
    qr(^libvirt) => 0,
    qr(^virtlogd) => 0,
    qr(^virtlockd) => 0,
    qr(^docker) => 0,

    # systemd stuff
    # (see also Debian Bug#784238 & #784437)
    qr(^emergency\.service$) => 0,
    qr(^rescue\.service$) => 0,
    qr(^elogind) => 0,

    # do not restart oneshot services, see also #862840
    qr(^apt-daily\.service$) => 0,
    qr(^apt-daily-upgrade\.service$) => 0,
    qr(^unattended-upgrades\.service$) => 0,
    # do not restart oneshot services from systemd-cron, see also #917073
    qr(^cron-.*\.service$) => 0,

    # ignore rc-local.service, see #852864
    qr(^rc-local\.service$) => 0,

    # don't restart systemd-logind, see #798097
    qr(^systemd-logind) => 0,

    qr(^mysqld) => 0,
    qr(^mariadbd) => 0,
};

# Override container default selection (hash of regex).
$nrconf{override_cont} = {
};

# Disable interpreter scanners.
#$nrconf{interpscan} = 0;

# Ignore script files matching these regexs:
$nrconf{blacklist_interp} = [
    # ignore temporary files
    qr(^/tmp/),
    qr(^/var/),
    qr(^/run/),

];

# Ignore +x mapped files matching one of these regexs:
$nrconf{blacklist_mappings} = [
    # special device paths
    qr(^/(SYSV00000000( \(deleted\))?|drm(\s|$)|dev/)),

    # ignore memfd mappings
    qr(^/memfd:),

    # aio(7) mapping
    qr(^/\[aio\]),

    # Oil Runtime Compiler's JIT files
    qr#/orcexec\.[\w\d]+( \(deleted\))?$#,

    # plasmashell (issue #65)
    qr(/#\d+( \(deleted\))?$),

    # Java Native Access (issues #142 #185)
    qr#/jna\d+\.tmp( \(deleted\))?$#,

    # temporary stuff
    qr#^(/var)?/tmp/#,
    qr#^(/var)?/run/#,
];

# Verify mapped files in filesystem:
# 0 : enabled
# -1: ignore non-existing files, workaround for chroots and broken grsecurity kernels (default)
# 1 : disable check completely, rely on content of maps file only
$nrconf{skip_mapfiles} = -1;

# Enable/disable hints on pending kernel upgrades:
#  1: requires the user to acknowledge pending kernels
#  0: disable kernel checks completely
# -1: print kernel hints to stderr only
#$nrconf{kernelhints} = -1;

# Filter kernel image filenames by regex. This is required on Raspian having
# multiple kernel image variants installed in parallel.
#$nrconf{kernelfilter} = qr(kernel7\.img);

# Enable/disable CPU microcode update hints:
#  1: requires the user to acknowledge pending updates
#  0: disable microcode checks completely
#$nrconf{ucodehints} = 0;

# Nagios Plugin: configure return code use by nagios
# as service status[1].
#
# [1] https://nagios-plugins.org/doc/guidelines.html#AEN78
#
# Default:
#  'nagios-status' => {
#     'sessions' => 1,
#     'services' => 2,
#     'kernel' => 2,
#     'ucode' => 2,
#     'containers' => 1
#  },
#
# Example: to ignore outdated sessions (status OK)
# $nrconf{'nagios-status'}->{sessions} = 0;


# Read additional config snippets.
if(-d q(/etc/needrestart/conf.d)) {
      foreach my $fn (sort </etc/needrestart/conf.d/*.conf>) {
	      print STDERR "$LOGPREF eval $fn\n" if($nrconf{verbosity} > 1);
	      eval do { local(@ARGV, $/) = $fn; <>};
	      die "Error parsing $fn: $@" if($@);
      }
}
