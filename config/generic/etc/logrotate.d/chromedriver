/var/log/chromedriver.log {
	daily
	missingok
	rotate 32
	nocompress
	delaycompress
	notifempty
	create 640 party party
	sharedscripts
	prerotate
		touch /tmp/rotating_chromedriver
		invoke-rc.d chromedriver stop 2>&1 | logger -t chromedriver.logrotate
	endscript
	postrotate
		rm /tmp/rotating_chromedriver
		date +%Y%m%d.%H%M%S >> /var/log/chromedriver_restart_times.log

		# make sure chromedriver opens new log file:
		invoke-rc.d chromedriver start 2>&1 | logger -t chromedriver.logrotate
	endscript
}
