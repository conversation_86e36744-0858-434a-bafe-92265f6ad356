# This file describes the network interfaces available on your system
# and how to activate them. For more information, see interfaces(5).

# The loopback network interface
auto lo
iface lo inet loopback

auto eth0
iface eth0 inet static
	address ***************
	netmask ***************
	network ***************
	broadcast ***************
	gateway ***************
	pre-up    iptables-restore	< /home/<USER>/config/vipserver/iptables.rules
	post-down iptables-save -c	> /home/<USER>/config/vipserver/iptables.rules

iface eth0 inet6 static
	address 2001:1540:801:d::44
	netmask 112
	gateway 2001:1540:801:d::1
	pre-up    ip6tables-restore	< /home/<USER>/config/vipserver/ip6tables.rules
	post-down ip6tables-save -c	> /home/<USER>/config/vipserver/ip6tables.rules

# The primary network interface
auto eth1
iface eth1 inet static
	address *********
	netmask *************
	network ********
	broadcast **********
	#gateway ********
	# dns-* options are implemented by the resolvconf package, if installed
	#dns-nameservers ********
	#dns-search local

iface eth1 inet6 static
	address 2001:1540:801:d:1::44
	netmask 112
