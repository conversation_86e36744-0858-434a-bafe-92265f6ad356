127.0.0.1			localhost
***************		broadcasthost
::1					localhost

# NOTE: first name is the one resolved

********				preparty preparty.local partyflock preparty.ipv4 partygit.ipv4 partyflock.ipv4
2001:1540:801:d:1::1000	preparty preparty.local partyflock preparty.ipv6 partygit.ipv6 partyflock.ipv6

********0				alphaparty alphaparty.ipv4 alphaparty.local
2001:1540:801:d:1::10	alphaparty alphaparty.ipv6 alphaparty.local
********1				omegaparty omegaparty.ipv4 omegaparty.local
2001:1540:801:d:1::11	omegaparty omegaparty.ipv6 omegaparty.local
**********				partymem4 partymem4.ipv4 partymem4.local
2001:1540:801:d:1::238	partymem4 partymem4.ipv6 partymem4.local
**********				partymem5 partymem5.ipv4 partymem5.local
2001:1540:801:d:1::239	partymem5 partymem5.ipv6 partymem5.local


*********				webx vip webx.ipv4 vip.ipv4 webx.local
2001:1540:801:d:1::44	webx vip webx.ipv6 vip.ipv6 webx.local
*********				webstore webstore.ipv4 webstore.local
2001:1540:801:d:1::85	webstore webstore.ipv6 webstore.local

*********				cron nstoo photo-upload cron.ipv4 nstoo.ipv4 photo-upload.ipv4 cron.local nstoo.local photo-upload.local
2001:1540:801:d:1::71	cron nstoo photo-upload cron.ipv6 nstoo.ipv6 photo-upload.ipv6 cron.local nstoo.local photo-upload.local

********99				special2 special2.ipv4 special2.local
2001:1540:801:d:1::199	special2 special2.ipv6 special2.local

*********				yin yin.ipv4 yin.local
2001:1540:801:d:1::80	yin yin.ipv6 yin.local
*********				black black.ipv4 black.local
2001:1540:801:d:1::91	black black.ipv6 black.local
*********				yang yang.ipv4 yang.local
2001:1540:801:d:1::81	yang yang.ipv6 yang.local

*********				white white.ipv4 white.local
2001:1540:801:d:1::90	white white.ipv6 white.local

********01				webone webone.ipv4 webone.local
********02				webtwo webtwo.ipv4 webtwo.local
**********				webfive webfive.ipv4 webfive.local

2001:1540:801:d:1::101	webone webone.ipv6 webone.local
2001:1540:801:d:1::102	webtwo webtwo.ipv6 webtwo.local
2001:1540:801:d:1::105	webfive webfive.ipv6 webfive.local

**********				backupparty backupparty.ipv4 backupparty.local
2001:1540:801:d:1::150	backupparty backupparty.ipv6 backupparty.local

**********				searchparty searchparty.ipv4 searchparty.local
2001:1540:801:d:1::203	searchparty searchparty.ipv6 searchparty.local
**********				searchpartyrep searchpartyrep.ipv4 searchpartyrep.local
2001:1540:801:d:1::234	searchpartyrep searchpartyrep.ipv6 searchpartyrep.local

# *.local for glusters,
# not sure how to change the hosts of current gluster bricks,
# keep old name with local for now
**********				newdata3 newdata3.ipv4 newdata3.local
2001:1540:801:d:1::209	newdata3 newdata3.ipv6 newdata3.local
*********				ultraparty ultraparty.ipv4 ultraparty.local
2001:1540:801:d:1::21	ultraparty ultraparty.ipv6 ultraparty.local
*********2				newdata2 newdata2.ipv4 newdata2.local
2001:1540:801:d:1::212	newdata2 newdata2.ipv6 newdata2.local

**********				superstore superstore.ipv4 superstore.local
2001:1540:801:d:1::140	superstore superstore.ipv6 superstore.local

**********				linkswitch2.ipv4 linkswitch2

# Thomas local:

*************			sandbox
2a10:3781:2354:1::120	sandbox
*************			sandbox-too
2a10:3781:2354:1::121	sandbox-too
************			sandbox-clean
2a10:3781:2354:1::77	sandbox-clean
************			sandbox-vip
2a10:3781:2354:1::88	sandbox-vip

************			hugestorage
2a10:3781:2354:1::59	hugestorage

*************			thomas-mac
*************			thomas-mac-wifi
2a10:3781:2354:1::1000	thomas-mac
2a10:3781:2354:1::1001	thomas-mac-wifi
*************			thomas-ext

# The following lines are desirable for IPv6 capable hosts
# (added automatically by netbase upgrade)

::1     ip6-localhost ip6-loopback
fe00::0 ip6-localnet
ff00::0 ip6-mcastprefix
ff02::1 ip6-allnodes
ff02::2 ip6-allrouters
ff02::3 ip6-allhosts
