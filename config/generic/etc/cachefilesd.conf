###############################################################################
#
# Copyright (C) 2006,2010 Red Hat, Inc. All Rights Reserved.
# Written by <PERSON> (<EMAIL>)
#
# This program is free software; you can redistribute it and/or
# modify it under the terms of the GNU General Public License
# as published by the Free Software Foundation; either version
# 2 of the License, or (at your option) any later version.
#
###############################################################################

dir /mnt/largepart/cache
tag pfcache
culltable 20

# These settings remove old files when the disk reaches 3% free space.
# It will delete files until 20% free space is reached.

brun	20%
bcull	 7%
bstop	 3%
frun	20%
fcull	 7%
fstop	 3%

# Assuming you're using SELinux with the default security policy included in
# this package
# secctx system_u:system_r:cachefiles_kernel_t:s0
