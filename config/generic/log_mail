#!/bin/bash

if [ "$2" = "custom" ]; then
	SUBJECT="$1"
else
	SUBJECT="${1:-partyflock log message}"
fi
shift

if [ "$1" = 'html' ]; then
	CONTENT_TYPE='Content-Type: text/html; charset="utf-8"'
	shift
fi

if [ "$1" = "custom" ]; then
	shift
	if [ -n "$CONTENT_TYPE" ]; then
		qprint -e | mail -E -a "Content-Transfer-Encoding: quoted-printable" -s "$SUBJECT" -a "$CONTENT_TYPE" "$@"
	else
		qprint -e | mail -E -a "Content-Transfer-Encoding: quoted-printable" -s "$SUBJECT" "$@"
	fi
else
	TO_NAME=watcher
	if [ "$1" = 'alarm' ]; then
		TO_NAME=alarmist
		shift
	fi
	FROM="$HOSTNAME: $SUBJECT <<EMAIL>>"
	TO="$TO_NAME <<EMAIL>>"
	if [ -n "$CONTENT_TYPE" ]; then
		qprint -e | mail -a "Content-Transfer-Encoding: quoted-printable" -r "$FROM" -E -s "$SUBJECT" -a "$CONTENT_TYPE" "$TO" "$@"
	else
		qprint -e | mail -a "Content-Transfer-Encoding: quoted-printable" -r "$FROM" -E -s "$SUBJECT" "$TO" "$@"
	fi
fi
