#!/usr/bin/perl -w

use strict;
use warnings;
no if ($] >= 5.018), 'warnings' => 'experimental';
use builtin qw(true false);
use IPC::Open3;
use Sys::Hostname;

$| = 1;

my %new_status;
my %stored_status;

my $stored_last_stamp = 0;
my $total			  = 0;
my $update			  = false;
my $bad_count 		  = 0;
my $stored_bad_count  = 0;
my $now 			  = time();

if (!-x '/bin/bash') {
	print STDERR "bash not found!\n";
	exit(1);
}
my $check_file = '/tmpdisk/__check_all_bash';
if (open CHECKFILE, "<$check_file") {
	while (<CHECKFILE>) {
		if (m/last_check:(?<stamp>\d+):(?<bad_count>\d+)/) {
			$stored_last_stamp = $+{stamp};
			$stored_bad_count  = $+{bad_count};

 		} elsif (m/(?<file>.+):(?<last_modified>\d+):(?<status>bad|ok|ignore)/) {
			$stored_status{$+{file}} = [$+{last_modified}, $+{status}];
		}
	}
	close CHECKFILE;
}

FILE: for (
	`find /home/<USER>/config -name \\*.sh \\! -name .#\\*`,
	`find /home/<USER>/config -perm /+x -type f \\! -name \\*.php \\! -name \\*_new \\! -name \\*~ \\! -name .#\\*`
) {
	chomp;
	if ($_ eq ''
	||	/~$/			# ends with tilde, backup file of joe
	||	/^\.#[^\/]+/	# starts with .#, lock files of joe
	||	/^\.git/		# git files
	) {
		next FILE;
	}
	my $file = $_;
	my $current_last_modified = (stat($file))[9];
	if (defined($stored_status{$file})) {
		my ($stored_last_modified, $stored_last_status) = @{$stored_status{$file}};
		if ($stored_last_status ne 'bad'
		&&	$current_last_modified <= $stored_last_modified
		) {
			if ($stored_last_status eq 'ok') {
				++$total;
			}
			$new_status{$file} = [$current_last_modified, $stored_last_status];
			next FILE;
		}
	}

	my $is_bad = false;
	my $shown_file = false;

	if ($file !~ /\.sh$/) {
		if (!open FILE, $file) {
			print STDERR "failed to open $file: $!\n";
			$is_bad = true;
		} else {
			my $first_line = <FILE>;
			close FILE;
			chomp($first_line);
			
			if ($first_line !~ m/^#!?\s*(\/bin\/(?:ba|z)?sh)\b/) {
				next;
			}
		}
	}
	++$total;
	print "checking $file... ";
	if (!$is_bad) {
		my $pid = open3(undef, undef, \*CHECK_ERR, 'bash', '-n', $file);
		if ($pid == 0) {
			print STDERR "open3 failed: $!\n";
			exit 1;
		}

		CHECK_LINE: while (<CHECK_ERR>) {
			chomp;
			if ($shown_file == false) {
				$shown_file = true;
				print "\n\033[A\033[2K\033[31;1m", "syntax check of $file failed", "\033[0m\n";
				$is_bad = true;
			}
			print STDERR "$_\n";
		}
		waitpid($pid, 0);
	}
	# $? is child result code

	$new_status{$file} = [$current_last_modified, $? || $is_bad ? 'bad' : 'ok'];
	if ($is_bad || $?) {
		++$bad_count;
	}
	if ($update == false) {
		$update = true;
	}
	if ($shown_file == false) {
		print "\n\x1B[A\x1B[K";
	}
}
print "check_all_bash done, ";
if ($bad_count == 0) {
	print "\033[32;1m", "syntax of all ", $total, " bash files is ok\033[0m\n";
	exit 0;
}

print "\033[31;1m$bad_count file", ($bad_count > 1 ? 's' : ''), " failed\033[0m\n";

if ($update != true) {
	exit $bad_count ? 1 : 0;
}
if (open CHECKFILE, '>'.$check_file) {
	print CHECKFILE "last_check:$now:$bad_count\n";
	while (my($file, $info) = each(%new_status)) {
		my ($mtime, $status) = @$info;
		print CHECKFILE "$file:$mtime:$status\n";
	}
	close CHECKFILE;
}
exit $bad_count ? 1 : 0;
