#!/bin/bash

set -e		# exit immediately on failure
set -v -x	# print input & print shell commands

BASH_SOURCE_SELF=${BASH_SOURCE[0]}
BASH_SOURCE_DIR="${BASH_SOURCE_SELF%/*}"
SCRIPT_DIR=$(realpath "$BASH_SOURCE_DIR")
cd "$SCRIPT_DIR"
source ./install.include.sh

function install_sexy_bash_prompt {
	local TMP_DIR
	TMP_DIR="$(mktemp -d --suffix -sexy-bash-prompt)"
	cd "$TMP_DIR"
	git clone --depth 1 --config core.autocrlf=false https://github.com/twolfson/sexy-bash-prompt
	cd sexy-bash-prompt
	make install
	source ~/.bashrc
	cd "$SCRIPT_DIR"
	rm -rf "$TMP_DIR"
}

function install_bash_profile {
	link_config /home/<USER>/config/generic/root/bash_profile ~/.bash_profile
}

function install_tmux {
	rm -f ~/.tmux.conf
	link_config etc/tmux.conf
	$APT_NONINTERACTIVE install tmux
}

function install_party {
	cd party
	git restore-mtime --quiet 2>&1 | grep -v "WARNING: Modified files" | grep -v "To include such"
}

function install_rsyslog {
	dont_start_services
	$APT_NONINTERACTIVE install rsyslog
	link_config etc/rsyslog.conf
	do_start_services
	systemctl restart rsyslog
}

function configure_mdadm_checks {
	link_config etc/default/mdadm
	systemctl restart mdadm
}

function install_grub {
	link_config etc/default/grub
	update-grub
}

function install_initramfs_tools {
	link_config etc/initramfs-tools/modules
	update-initramfs -k all -u
}

function install_updatedb_config {
	link_config etc/updatedb.conf
}

function install_needrestart_config {
	mkdir -p /etc/needrestart
	link_config etc/needrestart/needrestart.conf
}

function install_profile {
	local conf
	for conf in \
		profile.d/pf.sh \
		bash_completion.d/git-prompt
	do
		link_config etc/$conf
	done
}

function install_maendaemon {
	# Non-encrypted for local network:
	update-inetd --multi --verbose --remove '30000\s*'
	# SSL:
	update-inetd --multi --verbose --remove '60000\s*'
	# Obsolete:
	update-inetd --multi --verbose --remove '30001\s*'
	update-inetd --verbose --add "30000	stream	tcp		nowait	root	/home/<USER>/config/generic/maendaemon	maendaemon"
	update-inetd --verbose --add "30000	stream	tcp6	nowait	root	/home/<USER>/config/generic/maendaemon	maendaemon"
	update-inetd --verbose --add "60000	stream	tcp		nowait	root	/home/<USER>/config/generic/maendaemon	maendaemon"
	update-inetd --verbose --add "60000	stream	tcp6	nowait	root	/home/<USER>/config/generic/maendaemon	maendaemon"
	systemctl reload inetd
}

function install_internal_daemon {
	update-inetd --multi --verbose --remove '40001\s*'
	# See note in install_maendaemon why we're not using update-inetd for now
	update-inetd --verbose --add "40001	stream	tcp		nowait	root	/home/<USER>/config/generic/internaldaemon	internaldaemon"
	update-inetd --verbose --add "40001	stream	tcp6	nowait	root	/home/<USER>/config/generic/internaldaemon	internaldaemon"
	systemctl reload inetd
}

function install_locales {
	link_config etc/locale.gen
	locale-gen
}

function install_hosts {
	rm -f /etc/hosts
	link_config etc/hosts
}

function install_cachetool {
	# For more infromation: https://github.com/gordalina/cachetool
	curl -sLO https://github.com/gordalina/cachetool/releases/latest/download/cachetool.phar
	chmod +x cachetool.phar
	mv cachetool.phar /usr/local/bin
}

function install_exim_satellite {
	if [ "$HOSTNAME" != "preparty" ] \
	&& [ "$HOSTNAME" != "partyflock.nl" ]; then
		dont_start_services
		$APT_NONINTERACTIVE install exim4-daemon-light
		rm -f /etc/logrotate.d/exim4-paniclog
		local conf
		for conf in \
			default/exim4 \
			exim4/update-exim4.conf.conf \
			exim4/exim4.conf.template
		do
			link_config etc/$conf
		done
		if [[ "$HOSTNAME" =~ ^sandbox* ]]; then
			echo "$HOSTNAME.partyflock.nl" > /etc/mailname
		fi
		newaliases
		chown root:Debian-exim /etc/exim4/passwd.client
		chmod uga+rw /etc/exim4/passwd.client
		do_start_services
		/etc/init.d/exim4 restart
	fi
}

function install_apt_config {
	mkdir -p /etc/apt/sources.list.d
	local conf
	for conf in \
		sources.list \
		sources.list.d/google-chrome.list \
		sources.list.d/mariadb.sources \
		sources.list.d/sury-php.list
	do
		link_config etc/apt/$conf
	done

	$APT_NONINTERACTIVE install \
		apt-transport-https \
		curl \
		gpg \
		wget

	curl -sSLo /tmp/debsuryorg-archive-keyring.deb https://packages.sury.org/debsuryorg-archive-keyring.deb
	dpkg -i /tmp/debsuryorg-archive-keyring.deb
	rm /tmp/debsuryorg-archive-keyring.deb

	# get key for sury-php.list
	link_config usr/share/keyrings/sury.gpg

	wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | gpg --dearmor - | tee /etc/apt/trusted.gpg.d/google-linux-key.gpg >/dev/null

	mkdir -p /etc/apt/keyrings
	link_config etc/apt/keyrings/mariadb-keyring.pgp

	$APT_NONINTERACTIVE update
}

function dist_upgrade {
	$APT_NONINTERACTIVE update
	$APT_NONINTERACTIVE dist-upgrade
}

function install_joe {
	local conf jcf
	rm -f /etc/joe/editorrc
	$APT_NONINTERACTIVE install joe
	for conf in joerc ftyperc; do
		link_config etc/joe/$conf
	done
	# shellcheck disable=SC2043
	for jcf in my_colors.jcf; do
		link_config usr/share/joe/colors/$jcf
	done
}

function install_ssh {
	$APT_NONINTERACTIVE install ssh
	local side file
	for side in ssh sshd; do
		for file in etc/ssh/"${side}"_config.d/*.conf; do
			link_config "$file"
		done
		if [ -f "etc/ssh/${side}_config" ]; then
			link_config etc/ssh/${side}_config
		fi
	done
	mkdir -p /root/.ssh
	chmod og-rwx /root/.ssh
	/etc/init.d/ssh restart
}

function install_packages_to_pass_checks {
	# NOTE: Needed to lint check flockproc:
	$APT_NONINTERACTIVE install \
		libemail-stuffer-perl \
		libemail-valid-perl \
	 	libfile-mimeinfo-perl \
		libhtml-formattext-withlinks-perl \
		libhtml-tree-perl \
		libstring-approx-perl

	# NOTE: required for bind config check (which might be changed on any server, so required utils on all)
	$APT_NONINTERACTIVE install \
		bind9-utils
	# NOTE: required for checkslaves
	$APT_NONINTERACTIVE install \
		libhtml-escape-perl
}

function install_mdless {
	$APT_NONINTERACTIVE install \
		ruby \
		ruby-dev
	local PWD DATE
	PWD=$(pwd)
	DATE=$(date +%Y%m%d.%H%M%S)
	cd /tmpdisk
	git clone https://github.com/ttscoff/mdless.git "mdless_$DATE"
	cd "mdless_$DATE"
	gem install mdless
	cd "$PWD"
	rm -rf "/tmpdisk/mdless_$DATE"
}

function install_packages {
	if [ "$(uname -m)" = "x86_64" ]; then
		$APT_NONINTERACTIVE install intel-microcode
	fi

	install_packages_to_pass_checks
	install_tmux

	$APT_NONINTERACTIVE install \
		binfmt-support \
		bsd-mailx \
		coreutils \
		debian-archive-keyring \
		debian-goodies \
		deborphan \
		daemonize \
		git \
		git-restore-mtime \
		icdiff \
		libbsd-resource-perl \
		libdbd-mysql-perl \
		libio-socket-ssl-perl \
		libio-socket-inet6-perl \
		libipc-run3-perl \
		libmime-lite-perl \
		libmime-tools-perl \
		libnet-dns-perl \
		libsocket-getaddrinfo-perl \
		libstring-crc32-perl \
		libsys-hostname-long-perl \
		links \
		linux-perf \
		lm-sensors \
		fatrace \
		firmware-linux-nonfree \
		mariadb-client \
		needrestart \
		netcat-openbsd \
		nocache \
		openbsd-inetd \
		qprint \
		rasdaemon \
		rsyslog \
		sudo \
		telnet \
		webp \
		xz-utils \
		zopfli \
		zstd
}

function install_ntp {
	dont_start_services
	$APT_NONINTERACTIVE install ntpsec
	install_apparmor_config ntpsec usr.sbin.ntpd
	do_start_services
	systemctl restart ntpsec
}

function install_apparmor {
	$APT_NONINTERACTIVE install apparmor
	systemctl stop apparmor
	local name
	# shellcheck disable=SC2043
	for name in nameservice; do
		link_config "etc/apparmor.d/abstractions/$name"
	done
	systemctl start apparmor
}

function install_smartmontools {
	dont_start_services
	$APT_NONINTERACTIVE install smartmontools
	link_config etc/default/smartmontools
	do_start_services
	/etc/init.d/smartmontools restart
}

function install_sysstat {
	dont_start_services sysstat
	$APT_NONINTERACTIVE install sysstat
	local conf
	for conf in \
		etc/default/sysstat \
		etc/sysstat/sysstat \
		etc/sysstat/sysstat.ioconf \
		etc/cron.d/sysstat \
		etc/systemd/system/sysstat.service.wants/sysstat-collect.timer \
		lib/systemd/system/sysstat-collect.timer
	do
		link_config "$conf"
	done
	do_start_services sysstat
	systemctl daemon-reload
	systemctl restart cron
}

function install_git_config {
	link_config etc/gitconfig
}

function install_cpufrequtils {
	link_config etc/default/cpufrequtils

	$APT_NONINTERACTIVE install \
		cpufrequtils \
		linux-cpupower

	/etc/init.d/cpufrequtils restart
}

function remove_from_server {
	$APT_NONINTERACTIVE purge \
		dhcp3-client \
		inetutils-inetd \
		isc-dhcp-client \
		isc-dhcp-common \
		unattended-upgrades
}

function install_resolv_conf {
	if [[ "$HOSTNAME" =~ ^sandbox* ]]; then
		link_config etc/resolv-sandbox.conf /etc/resolv.conf
	else
		link_config etc/resolv.conf
	fi
}

function install_php_cli {
	# Remove old PHP:
	$APT_NONINTERACTIVE purge '*php8.3*'
	rm -rf /etc/php/8.3*

	$APT_NONINTERACTIVE install \
		composer \
		libsasl2-2 \
		php-pear \
		php-composer-xdebug-handler \
		php-tokenizer \
		php$INSTALL_PHP_VERSION-zip \
		php$INSTALL_PHP_VERSION-apcu \
		php$INSTALL_PHP_VERSION-cli \
		php$INSTALL_PHP_VERSION-common \
		php$INSTALL_PHP_VERSION-curl \
		php$INSTALL_PHP_VERSION-dev \
		php$INSTALL_PHP_VERSION-gd \
		php$INSTALL_PHP_VERSION-gmp \
		php$INSTALL_PHP_VERSION-igbinary \
		php$INSTALL_PHP_VERSION-imagick \
		php$INSTALL_PHP_VERSION-intl \
		php$INSTALL_PHP_VERSION-mailparse \
		php$INSTALL_PHP_VERSION-mbstring \
		php$INSTALL_PHP_VERSION-memcached \
		php$INSTALL_PHP_VERSION-msgpack \
		php$INSTALL_PHP_VERSION-mysqli \
		php$INSTALL_PHP_VERSION-mysqlnd \
		php$INSTALL_PHP_VERSION-opcache \
		php$INSTALL_PHP_VERSION-sockets \
		php$INSTALL_PHP_VERSION-vips \
		php$INSTALL_PHP_VERSION-xdebug \
		php$INSTALL_PHP_VERSION-xml \
		php$INSTALL_PHP_VERSION-zip \
		poppler-utils

	local module

	pecl channel-update pecl.php.net

	for module in \
		dio \
		xattr
	do
		local install_module
		if [ "$module" = "dio" ]; then
			install_module=$module-0.3.0
		else
			install_module=$module
		fi
		pecl -v uninstall $install_module
		printf "\n" | pecl -v install --ignore-errors $install_module
		link_config \
			/home/<USER>/config/generic/etc/php/mods-available/$module.ini \
			/etc/php/$INSTALL_PHP_VERSION/mods-available/$module.ini
		phpenmod $module
	done

	link_config /home/<USER>/config/webserver/php-overrides.ini 		/etc/php/$INSTALL_PHP_VERSION/cli/conf.d/90-php-overrides.ini
	link_config /home/<USER>/config/webserver/php-cli-overrides.ini 	/etc/php/$INSTALL_PHP_VERSION/cli/conf.d/95-php-cli-overrides.ini

	if [[ "$HOSTNAME" = sandbox* ]]; then
		link_config /home/<USER>/config/webserver/php-development-overrides.ini \
					/etc/php/$INSTALL_PHP_VERSION/cli/conf.d/99-php-development-overrides.ini
	fi

	for module in \
		dio \
		igbinary \
		phar \
		sysvshm \
		sysvsem \
		xattr
	do
		phpenmod $module
	done

	remove_unused_php

	set +e
	addgroup --gid 666 party
	adduser --verbose --system --uid 666 --gid 666 --home /home/<USER>/bin/bash party
	set -e

	/home/<USER>/config/phpclasses/composer_action install

	# update facebook SDK to fix warnings with PHP8
	# generate patch:
	#	<PATH>/facebook_orig contains unmodified checkout
	#	<PATH>/facebook contains new modified version
	#
	#	cd <PATH>
	#	diff -p1 facebook_orig facebook > facebook.patch
	cd /home/<USER>/config/phpclasses/vendor/facebook
	patch -p1 < ../../facebook.patch

	$APT_NONINTERACTIVE purge 'libapache2-mod-php8.*'
}

function remove_unused_php {
	local mod
	for mod in \
		apcu_bc \
		ftp \
		gettext \
		libapache2-mod-php8.3 \
		libapache2-mod-php8.4 \
		memcache \
		pdo \
		pdo_mysql \
		readline \
		redis \
		sockets \
		shmop \
		sysvmsg \
		wddx \
		xmlreader \
		xmlwriter \
		xsl
	do
		phpdismod -v $INSTALL_PHP_VERSION $mod
	done
}

function install_sysctl_config {
	local conf
	for conf in \
		general \
		ipv6 \
		netstuff
	do
		link_config etc/sysctl.d/$conf.conf
	done
	if [ "$HOSTNAME" = web* ]; then
		link_config etc/sysctl.d/swappiness_web.conf
	else
		link_config etc/sysctl.d/swappiness.conf
	fi
	sysctl --system
}

function make_tmpdisk {
	# try to make this if it doesn't exist
	mkdir -p /tmpdisk
	chmod u+w,o+wt /tmpdisk
}

function clear_caches {
	rm -f /var/cache/apt/archives/*.deb
	$APT_NONINTERACTIVE --purge autoremove
}

function install_console_setup {
	link_config etc/default/console-setup
}

function install_nut_client {
	dont_start_services
	$APT_NONINTERACTIVE install nut-client
	local conf
	for conf in nut upsmon; do
		link_config etc/nut/$conf.conf
	done
	do_start_services
	systemctl start nut-monitor.service
}

function install_trixie_kernel {
	$APT_NONINTERACTIVE install linux-image-amd64/trixie
}

function install_root_crontab {
	# NOTE: Would be nice to do this automatically

	echo -e "\e[0;31mWARNING\e[0m Install root_crontab manually!\n"
}

function install_fail2ban {
	if [ "$HOSTNAME" = "partyflock.nl" ] \
	|| [ "$HOSTNAME" = "cron" ] \
	|| [ "$HOSTNAME" = "webx" ]; then
		dont_start_services fail2ban
		$APT_NONINTERACTIVE install fail2ban
		link_config "/home/<USER>/config/generic/etc/fail2ban/jail-$HOSTNAME.local" /etc/fail2ban/jail.local
		link_config etc/fail2ban/fail2ban.local
		link_config etc/fail2ban/filter.d/named-refused.conf
		do_start_services fail2ban
		systemctl start fail2ban
	fi
}

function install_base_named_zones {
	# All servers need this, because they might need to validate the name server config
	mkdir -p /etc/bind
	local zone
	for zone in \
		zones.rfc1918 \
		db.0 \
		db.127 \
		db.255 \
		db.empty \
		db.local \
		db.root
	do
		link_config etc/bind/$zone
	done
}

function install_logrotate_config {
	# Remove some legacy or unused rotation configs that might linger or are in the way
	rm -f	/etc/logrotate.d/mail \
			/etc/logrotate.d/mysql-server.dpkg-bak \
			/etc/logrotate.d/mysql-server.dpkg-new \
			/etc/logrotate.d/php*-fpm \
			/etc/logrotate.d/exim4-paniclog

	link_config etc/logrotate.conf
	local conf
	for conf in etc/logrotate.d/*; do
		link_config "$conf"
		# Remove backups of old configs, not sure if every file is executed,
		# wouldn't want the backups to also start rotating every time
		local YEAR
		YEAR=$(date +%Y)
		rm -f "/etc/logrotate.d/*-$YEAR*"
	done
}


function install_github_cli {
	(type -p wget >/dev/null || (sudo apt update && sudo apt-get install wget -y)) \
	&& sudo mkdir -p -m 755 /etc/apt/keyrings \
	&& wget -qO- https://cli.github.com/packages/githubcli-archive-keyring.gpg | sudo tee /etc/apt/keyrings/githubcli-archive-keyring.gpg > /dev/null \
	&& sudo chmod go+r /etc/apt/keyrings/githubcli-archive-keyring.gpg \
	&& echo "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/githubcli-archive-keyring.gpg] https://cli.github.com/packages stable main" | sudo tee /etc/apt/sources.list.d/github-cli.list > /dev/null \
	&& sudo apt update \
	&& sudo apt install gh -y
}

function install_grc {
	$APT_NONINTERACTIVE install grc
	link_config usr/local/share/grc/config.mariadb
}

function install_compress_logfiles {
	$APT_NONINTERACTIVE install zstd
	link_config etc/cron.d/compress_logfiles
}

function install_pam_sshd_config {
	link_config etc/pam.d/sshd
}

function install_php_opcode_cache {
	# Make a php-opcode-cache with GID one above the ID if user party
	groupadd --force --gid 667 php-opcode-cache
	# Grant www-data, party and root access to the opcode-cache
	usermod --append --groups php-opcode-cache www-data
	usermod --append --groups php-opcode-cache party
	usermod --append --groups php-opcode-cache root
	# For setfacl operations in prepare_php_opcode_cache
	$APT_NONINTERACTIVE install acl
	prepare_php_opcode_cache
}

function install_aicommit2 {
	# For sources and more information: https://github.com/tak-bro/aicommit2
	#
	# prepare-commit-msg hook can be intalled using:
	#
	# ```bash
	# aicommit2 hook install
	# aictommi2 hook uninstall
	# ```
	#
	# To quickly set all keys at once, by running this script with the three ENV vars:
	# - OPENAI_key
	# - GEMINI_key
	# - ANTHROPIC_key
	#
	# Setting a key to 'no' skips the key and it won't be stored.
	# So, setting both OPENAI and ANTHROPIC key but not GEMINI:
	# GEMINI_key=no OPENAI_key=<openai_key> ANTHROPIC_key=<claude_key> ./install aicommit2
	source ./install_npm_and_node.sh
	install_npm_and_node
	cp root/aicommit2 ~/.aicommit2
	npm install -g aicommit2
	local LLM STORE_var
	for LLM in OPENAI GEMINI ANTHROPIC; do
		STORE_var="${LLM}_key"
		if [ -z "${!STORE_var}" ]; then
			read -p -r "aicommit2: $LLM.key=" ${STORE_var?}
		fi
		if [ -n "${!STORE_var}" ] && [ "${!STORE_var}" != "no" ]; then
			aicommit2 config set $LLM.key="${!STORE_var}"
			echo $LLM.key is set
		else
			echo not setting $LLM.key
		fi
	done
	aicommit2 hook install
}

clear_caches

case "$1" in
base)	make_tmpdisk
		install_apt_config
		install_needrestart_config
		dist_upgrade
		install_sysctl_config
		install_base_named_zones
		install_packages
		install_php_cli
		install_php_opcode_cache
		install_cachetool
		install_grc
		install_ntp
		install_apparmor
		install_smartmontools
		install_sysstat
		install_profile
		install_sexy_bash_prompt
		install_bash_profile
		install_hosts
		install_locales
		install_joe
		install_trixie_kernel
		install_initramfs_tools
		install_grub
		install_ssh
		install_maendaemon
		install_internal_daemon
		install_exim_satellite
		install_git_config
		install_github_cli
		install_aicommit2
		install_logrotate_config
		install_compress_logfiles
		install_pam_sshd_config
		install_cpufrequtils
		install_console_setup
		if [[ "$HOSTNAME" =~ ^sandbox* ]]; then
			remove_for_server
			install_resolv_conf
			install_nut_client
		fi
		install_root_crontab
		;;

aicommit2)					install_aicommit2;;
apparmor)					install_apparmor;;
apt-config)					install_apt_config;;
base-named-zones)			install_base_named_zones;;
bash-profile)				install_bash_profile;;
cachetool)					install_cachetool;;
clearcache)					clear_caches;;
compress-logfiles)			install_compress_logfiles;;
console-setup)				install_console_setup;;
cpufrequtils)				install_cpufrequtils;;
dist-upgrade)				dist_upgrade;;
exim-satellite)				install_exim_satellite;;
fail2ban)					install_fail2ban;;
github-cli)					install_github_cli;;
grc)						install_grc;;
grub)						install_grub;;
hosts)						install_hosts;;
imagemagick-7)				install_imagemagick_7;;
initramfs-tools)			install_initramfs_tools;;
joe)						install_joe;;
locales)					install_locales;;
logrotate-config)			install_logrotate_config;;
maendaemon)					install_maendaemon;;
mdadm-checks)				configure_mdadm_checks;;
mdless)						install_mdless;;
needrestart-config)			install_needrestart_config;;
npm-and-node)				source ./install_npm_and_node.sh; install_npm_and_node;;
ntp)						install_ntp;;
nut-client)					install_nut_client;;
packages)					install_packages;;
packages-to-pass-checks)	install_packages_to_pass_checks;;
pam-sshd-config)			install_pam_sshd_config;;
php-cli)					install_php_cli;;
php-opcode-cache)			install_php_opcode_cache;;
profile)					install_profile;;
remove-from-server)			remove_from_server;;
resolv-conf)				install_resolv_conf;;
rm-unused-php)				remove_unused_php;;
root-crontab)				install_root_crontab;;
rsyslog)					install_rsyslog;;
sexy-bash)					install_sexy_bash_prompt;;
smartmontools)				install_smartmontools;;
ssh)						install_ssh;;
sysctl-config)				install_sysctl_config;;
sysstat)					install_sysstat;;
tmux)						install_tmux;;
updatedb-config)			install_updatedb_config;;
*)							echo "not understood!"; exit 1;;
esac

echo "done"
