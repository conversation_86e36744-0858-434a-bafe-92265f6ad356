#!/bin/bash

ASK_PERMISSION=" -y "

for arg in "$@"; do
	if [[ "$arg" == "ask" ]]; then
		ASK_PERMISSION=""
	fi
done

function remove_on_all_servers {
	# there are not used by our servers:

	set +e

	for package in \
		gcc-4.4-base \
		gcc-4.6-base \
		gcc-4.9-base \
		gcc-6-base \
		gcc-7-base \
		gcc-8-base \
		gcc-4.6 \
		gcc-6 \
		gcc-7 \
		gcc-8 \
		libprocps0 \
		libruby1.8 \
		libruby1.9.1
	do
		apt-get $ASK_PERMISSION --purge remove $package
	done
	
	set -e

	apt-get $ASK_PERMISSION --purge remove \
		apt-listchanges \
		gcc-10 \
		gcc-10-base \
		gcc-11 \
		gcc-11-base \
		gir1.2-glib-2.0 \
		krb5-locales \
		libpython2.7-minimal \
		libtinfo5 \
		laptop-detect \
		qml-module-qmltermwidget \
		qml-module-qt-labs-folderlistmodel \
		qml-module-qt-labs-settings \
		qml-module-qtgraphicaleffects \
		qml-module-qtqml \
		qml-module-qtqml-models2 \
		qml-module-qtquick-controls \
		qml-module-qtquick-dialogs \
		qml-module-qtquick-layouts \
		qml-module-qtquick-localstorage \
		qml-module-qtquick-privatewidgets \
		qml-module-qtquick-window2 \
		qml-module-qtquick2 \
		qt5-gtk-platformtheme \
		php-memcache \
		php-memcached \
		ruby \
		ruby-minitest \
		ruby-net-telnet \
		ruby-power-assert \
		ruby-test-unit \
		ruby-xmlrpc \
		rubygems-integration \
		sqlite3 \
		tasksel \
		tasksel-data \
		vim \
		vim-common \
		vim-tiny
}

function remove_x11_stuff {
	apt-get $ASK_PERMISSION --purge remove \
		libxcb-render0 \
		libtiff-tools \
		libtiff5 \
		libx11-data \
		libxcb-shm0 \
		libxcb1 \
	        libwayland-client0 \
        	libwayland-egl1 \
		x11-common
}

function remove_purgatory {
	apt-get $ASK_PERMISSION --purge autoremove
	
	# run a few times, as new orphans get created by deletions
	while [[ ! -z $(deborphan) ]]; do
		deborphan | xargs apt-get $ASK_PERMISSION remove --purge
	done
}

function purge_removed_packages {
	# purge removed packages:

	apt-get $ASK_PERMISSION purge $(dpkg -l | grep '^rc' | awk '{print $2}')
}

function clear_apt_cache {
	apt-get clean
}

function upgrade_all {
	apt-get $ASK_PERMISSION dist-upgrade
}

case "$1" in
backupserver | \
cronserver | \
database | \
loadbalancer | \
memserver | \
storage | \
storageserver | \
webserver)
	apt-get update
	purge_removed_packages
	remove_on_all_servers
	if [[ "$1" == "webserver" ]]; then
		for package in \
			ffmpeg \
			imagemagick \
			node-opencv;
		do
			if [[ -z $(deborphan --list-keep | grep $package) ]]; then
				apt-get -y install $package
				deborphan --add-keep $package
			fi
		done
	fi
	if [[ -z $(deborphan --list-keep | grep ntp) ]]; then
		apt-get -y install ntp
		deborphan --add-keep ntp
	fi
	if [[ "$1" != "cronserver" && "$1" != "webserver" ]]; then
		remove_x11_stuff
	fi	
	clear_apt_cache
	upgrade_all
	remove_purgatory
	;;
*)
	echo "remove_useless_packages (cronserver | backupserver | database | loadbalancer | memserver | storageserver | webserver)"
	exit 1
	;;
esac
