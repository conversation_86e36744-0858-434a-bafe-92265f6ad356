#!/usr/bin/perl -w

use strict;
use warnings;
use IO::Socket;
use IO::Socket::SSL;
use Data::Dumper;
use Sys::Syslog qw(openlog syslog LOG_DEBUG LOG_INFO LOG_NOTICE LOG_WARNING LOG_ERR LOG_CRIT);
use experimental qw(builtin);
use builtin qw{true false};
use lib '/home/<USER>/config';
use perlinclude::servers;
use perlinclude::secret;
use perlinclude::daemonize;

$| = 1;
$/ = "\n";

my $print_too;
my $debug = grep { $_ eq 'debug' } @ARGV;

if (!openlog('watchweblogs', 'pid'.($debug ? ',perror' : ''), 'daemon')) {
	print STDERR "failed to open log\n";
	exit 1;
}

open(SYSLOG, "| /usr/bin/logger --skip-empty --stderr --tag watchweblogs[$$] --priority daemon.error") or die("could not open pipe to logger: $!" );
*STDERR = *SYSLOG;

my @webservers = getservers('web');

if ($#webservers == -1) {
	my $error = 'no webservers found';
	syslog(LOG_ERR, $error);
	print STDERR "$error\n";
	exit 1;
}

my %offsets;
my $interval	= 10;		# Check webserver for log updates every 10 seconds.
my $errno		= 0;
my $fork		= false;
my $daemonize	= false;

for (@ARGV) {
	if ($_ eq 'debug') {
		# detected earlier
		next;
	}
	if ($_ eq 'daemonize') {
		$daemonize = true;
		next;
	}
	my $error = "invalid argument: $_";
	syslog(LOG_ERR, $error);
	print STDERR "$error\n";
	exit 1;
}

my $ready_wait_timeout		= 20;
my $read_overview_timeout	= 30;
my $read_lines_timeout		= 10;

if ($daemonize) {
	syslog(LOG_INFO, $print_too = 'initializing');
	$debug && print "$print_too\n";
}
get_lines(true);
if ((my $offsetcnt = scalar keys(%offsets)) != $#webservers+1) {
	my @webnames;
	for (@webservers) {
		push @webnames,$_->{'id'};
	}
	my $error = 'have '.join(', ',@webnames).' webservers, but offsets for only '.join(', ',keys %offsets);
	syslog(LOG_ERR, $error);
	print STDERR "$error\n";
	exit 1;
}

syslog(LOG_NOTICE, $print_too = 'have offsets for: '.join(', ', keys %offsets));
$debug && print "$print_too\n";

if ($daemonize) {
	syslog(LOG_INFO, $print_too = 'detaching');
	$debug && print "$print_too\n";
	daemonize('/tmpdisk/wwwlog.1', '/tmpdisk/wwwlog.2');
}

while (true) {
	sleep($interval);
	get_lines(false);
}

sub get_lines {
	my $initialize = shift;
	my $start;
	if ($debug) {
		$start = time();
		syslog(LOG_DEBUG, 'get_lines('.($initialize ? 'true' : 'false').')');
	}
	my @children;
	WEBSERVER: for my $server (@webservers) {
		my $uniq	= $$server{'id'};
		my $ip		= $$server{'ip'};
		my $port	= $$server{'port'};
		# my $ip6		= $$server{'ip6'};
		my $head	= '['.$uniq.']';
		if ($fork) {
			my $childpid = fork();
			if (!defined($childpid)) {
				local_error("$head failed to fork child: $!");
				next WEBSERVER;
			}
			if ($childpid) {
				push @children,$childpid;
				next WEBSERVER;
			}
		}
		$debug && syslog(LOG_DEBUG, "$head connecting to $ip port $port");
		my $sock;
		if ($port == 30000) {
			$sock = IO::Socket::INET->new(
				'PeerAddr'	=> $ip,
				'PeerPort'	=> $port,
				'Timeout'	=> $initialize ? 20 : 5
			);
		} else {
			my $tries = 5;
			while ($tries--) {
#				print "connecting to $ip : $port\n";
				$sock = IO::Socket::SSL->new(
	#				'PeerHost'		=> 'partyflock.nl',
	#				'SSL_hostname'		=> 'mail.partyflock.nl',
					'PeerAddr'		=> $ip,
					'PeerPort'		=> $port,
					'Timeout'		=> 5,#$initialize ? 5 : 5,
					'SSL_verify_mode'	=> SSL_VERIFY_NONE,
	#				'SSL_version'		=> 'SSLv3',
					'SSL_version'		=> 'TLSv12:!SSLv2:!SSLv3',
					'SSL_use_cert'		=> 1,
					'SSL_key_file'		=> '/home/<USER>/config/certs/current_partyflock.nl.key',
					'SSL_cert_file'		=> '/home/<USER>/config/certs/current_partyflock.nl.crt',
					'SSL_ca_file'		=> '/home/<USER>/config/certs/current_partyflock.nl.ca-bundle'
				);
				if ($sock) {
					last;
				}
			}
		}

		if ($sock) {
			eval {
				local $SIG{ALRM} = sub { die "alarm\n" };
				alarm $ready_wait_timeout;
				my $readyline = ready_wait($sock);
				alarm 0;
				if (!$readyline) {
					local_error("$head server not ready");
				} elsif ($readyline !~ /:NEEDKEY/) {
					local_error("$head strange ready message: $readyline");
				} else {
					print $sock secret(), $/;
					$debug && syslog(LOG_DEBUG, "$head sent ".secret());
					alarm $ready_wait_timeout;
					my $readyline = ready_wait($sock);
					alarm 0;
					if (!$readyline) {
						local_error("$head server not ready here");
					} elsif ($readyline =~ /:NEEDKEY/) {
						local_error("$head key not accepted");
					} else {
						if ($initialize) {
							print $sock 'logs_overview', $/;
							read_overview($sock,$uniq);
							$debug && syslog(LOG_DEBUG, "$head overview read");
						} else {
							LOGX: while (my($logid,$loginfo) = each(%{$offsets{$uniq}})) {
								my ($currlogdir, $currlogname, $currstamp, $curroffset) = @{$loginfo};
								my $to_send = "loglines:$currlogdir:$currlogname:$currstamp:$curroffset";
								print $sock $to_send, $/;
								$debug && syslog(LOG_DEBUG, "$head sent: $to_send");
								my $newloginfo = read_lines($sock, $uniq, $loginfo);
								if (defined($newloginfo)) {
									$debug && syslog(LOG_DEBUG, "$head offsets changed");
									$offsets{$uniq}{$logid} = $newloginfo;
								}
							}
						}
					}
				}
			};
			if ($@) {
				die unless $@ eq "alarm\n";
				local_error("$head get_lines timeout");
			}
		} else {
			local_error("$head failed to connect to $ip port $port: $!");
			if ($SSL_ERROR) {
				local_error($head,  'ssl failure: ', $SSL_ERROR);
			}
		}
		if ($fork) {
			exit;
		}
	}
	if ($fork) {
		for (@children) {
			$debug && syslog(LOG_DEBUG, "waiting for: $_");
			waitpid($_, 0);
		}
	}
	$debug && syslog(LOG_DEBUG, 'get_lines done in '.(time() - $start).' seconds');
}
sub read_overview {
	my($sock,$uniq) = @_;
	my $head = '['.$uniq.']';
	$debug && syslog(LOG_DEBUG, 'read_overview is waiting for data');
	eval {
		my $webname;
		local $SIG{ALRM} = sub { die "alarm\n" };
		alarm $read_overview_timeout;
		while (my $info = readfromdaemon($sock)) {
			alarm 0;
			my ($line, $type, $pass) = @{$info};
			$debug && syslog(LOG_DEBUG, "received: $line");
			if ($type eq 'ready') {
				last;
			}
			if ($line =~ /^LOGSTATUS:([^:]+):([^:]+):(\d+):(\d+)/) {
				# log_dir:log_name:stamp:offset
				my $logid = "$1:$2";
				$offsets{$uniq}{$logid} = [$1,$ 2, $3, $4];
				$debug && syslog(LOG_DEBUG, "got initial offset log_dir: $1, log_name: $2, stamp: $3, offset: $4");
				next;
			}
			if ($line) {
				output($head, $line, indicatorchar($type), 'output');
			}
		} continue {
			alarm $read_overview_timeout;
		}
		alarm 0;
	};
	if ($@) {
		die unless $@ eq "alarm\n";
		my $error = "$head read_overview timeout";
		syslog(LOG_ERR, $error);
		print STDERR "$error\n";
	}
	alarm 0;
}
sub read_lines {
	my ($sock, $uniq, $loginfo) = @_;
	my $head = "[$uniq]";
	my($currlogdir, $currlogname, $currstamp, $curroffset) = @{$loginfo};
	my $newloginfo;
	$debug && syslog(LOG_DEBUG, "$head read_lines is waiting for data");
	eval {
		local $SIG{ALRM} = sub { die "alarm\n" };
		alarm $read_lines_timeout;
		while (my $info = readfromdaemon($sock)) {
			alarm 0;
			my ($line, $type, $pass) = @{$info};
			$debug && syslog(LOG_DEBUG, "$head received: $line");
			if ($type eq 'ready') {
				last;
			}
			if ($line =~ /^LOGSTATUS:(?<log_dir>[^:]+):(?<log_name>[^:]+):(?<stamp>\d+):(?<offset>\d+)/) {
				$debug && syslog(LOG_DEBUG, "$head got offset log dir: $+{log_dir}, log name: $+{log_name}, stamp: $+{stamp}, offset: $+{offset}");
				if ($currstamp  != $+{stamp}
				||	$curroffset != $+{offset}
				) {
					$newloginfo = [$+{log_dir}, $+{log_name}, $+{stamp}, $+{offset}];
				}
				next;
			}
			if ($line =~ /^LOGSTATUS:nochange/) {
				$debug && syslog(LOG_DEBUG, "$head no changes");
				next;
			}
			if ($line) {
				output($head, $line, indicatorchar($type), 'output');
			}
		} continue {
			alarm $read_lines_timeout;
		}
 	};
 	alarm 0;
	if ($@) {
		die unless $@ eq "alarm\n";
		local_error("$head read_lines timeout ($currlogdir:$currlogname:$currstamp:$curroffset)");
	}
	return $newloginfo;
}

sub local_error {
	my $error = shift;
	syslog(LOG_ERR, $error);
	error($error);
}
