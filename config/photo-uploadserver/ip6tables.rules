# Generated by ip6tables-save v1.8.9 (nf_tables) on Wed Nov  6 19:13:39 2024
*raw
:PREROUTING ACCEPT [1388351:*********]
:OUTPUT ACCEPT [1187333:*********]
-A PREROUTING -p tcp -m tcp --dport 21 -j CT --helper ftp
COMMIT
# Completed on Wed Nov  6 19:13:39 2024
# Generated by ip6tables-save v1.8.9 (nf_tables) on Wed Nov  6 19:13:39 2024
*filter
:INPUT DROP [1924:132383]
:FORWARD DROP [0:0]
:OUTPUT ACCEPT [1187333:*********]
:f2b-named-refused - [0:0]
:f2b-pure-ftpd - [0:0]
:f2b-sshd - [0:0]
-A INPUT -p tcp -m multiport --dports 21,20,990,989 -j f2b-pure-ftpd
-A INPUT -p tcp -m multiport --dports 44422 -j f2b-sshd
-A INPUT -p tcp -m multiport --dports 53,953 -j f2b-named-refused
-A INPUT -p udp -m multiport --dports 53,953 -j f2b-named-refused
-A INPUT -i eth1 -j ACCEPT
-A INPUT -i lo -j ACCEPT
-A INPUT -p ipv6-icmp -j ACCEPT
-A INPUT -s 2a10:3781:2354::/48 -i eth0 -j ACCEPT
-A INPUT -i eth0 -p tcp -m tcp --dport 20 -m comment --comment "accept incoming tcp FTP data traffic" -j ACCEPT
-A INPUT -i eth0 -p udp -m udp --dport 20 -m comment --comment "accept incoming udp FTP data traffic" -j ACCEPT
-A INPUT -i eth0 -p tcp -m tcp --dport 21 -m comment --comment "accept incoming tcp FTP traffic" -j ACCEPT
-A INPUT -i eth0 -p udp -m udp --dport 21 -m comment --comment "accept incoming udp FTP traffic" -j ACCEPT
-A INPUT -i eth0 -p tcp -m tcp --dport 53 -m comment --comment "accept incoming tcp DNS traffic" -j ACCEPT
-A INPUT -i eth0 -p udp -m udp --dport 53 -m comment --comment "accept incoming udp DNS traffic" -j ACCEPT
-A INPUT -i eth0 -p tcp -m tcp --dport 44422 -m comment --comment "accept incoming SSH over tcp on port 44422" -j ACCEPT
-A INPUT -i eth0 -p udp -m udp --dport 44422 -m comment --comment "accept incoming SSH over udp on port 44422" -j ACCEPT
-A INPUT -i eth0 -p udp -m udp --dport 853 -m comment --comment "accept incoming udp DNS over DTLS traffic" -j ACCEPT
-A INPUT -i eth0 -p tcp -m tcp --dport 853 -m comment --comment "accept incoming tcp DNS over TLS traffic" -j ACCEPT
-A INPUT -i eth0 -p tcp -m tcp --dport 989 -m comment --comment "accept incoming tcp FTP-SSL data traffic" -j ACCEPT
-A INPUT -i eth0 -p udp -m udp --dport 989 -m comment --comment "accept incoming udp FTP-SSL data traffic" -j ACCEPT
-A INPUT -i eth0 -p tcp -m tcp --dport 989 -m comment --comment "accept incoming tcp FTP-SSL traffic" -j ACCEPT
-A INPUT -i eth0 -p udp -m udp --dport 989 -m comment --comment "accept incoming udp FTP-SSL traffic" -j ACCEPT
-A INPUT -m conntrack --ctstate RELATED,ESTABLISHED -j ACCEPT
-A f2b-named-refused -j RETURN
-A f2b-pure-ftpd -j RETURN
-A f2b-sshd -j RETURN
COMMIT
# Completed on Wed Nov  6 19:13:39 2024
