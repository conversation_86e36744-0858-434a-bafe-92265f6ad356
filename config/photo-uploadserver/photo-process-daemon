#!/usr/bin/perl -w

use strict;
use warnings;
use DBI;
use IPC::Run3;
use File::stat;
use File::Copy qw(copy);
use Sys::Syslog qw(LOG_DEBUG LOG_INFO LOG_NOTICE LOG_ERR LOG_WARNING LOG_CRIT openlog syslog);
use Fcntl ':mode';
use Encode qw(encode decode);
use Socket::GetAddrInfo qw(getnameinfo);
use IO::Socket::INET;
use IO::Socket::INET6 qw(inet_ntoa inet_ntop);
use Email::Stuffer;
use feature qw{state};
use File::Basename qw(dirname);
use lib '/home/<USER>/config';
use utf8;

#my $process_pid;

# maximumsizes for main and thumbnail photo
my $max_main	= 960 * 640;
my $max_thumb	= 270 * 180;

my $convert 	= '/usr/local/bin/convert';
my $exiftool	= '/usr/local/bin/exiftool';

my $dsn = 'DBI:mysql:database=party_db;host=ultraparty;mysql_connect_timeout=60';
my $haveconn = db_single('SELECT 1');
if (!defined($haveconn)
||	!$haveconn
) {
	exit 1;
}

my $dbh;

$/ = "\r\n";
$| = 1;

my @images;
my @publish;
my @replacements;
my %repimgids;
my $repimgidcnt = 0;
my $userid;
my $user;
my $partyid;
my $party;
my $unlink = 0;
my $anonymous = 0;
my $invisible = 0;
my $okcam = 0;
my $aspiring = 0;

if (!openlog('photo-process-daemon', 'pid', 'daemon')) {
	print STDERR "failed to openlog";
	exit 1;
}

my $other = getpeername(STDIN);
if (!$other) {
	syslog(LOG_INFO, "started from console");
} else {
	my $me = getsockname(STDIN);
	my $family = sockaddr_family $other;
	my $herhostname;
	my $iaddr;
	my $port = 0;
	my $err;
	if ($family == AF_INET) {
		(undef, $iaddr) = unpack_sockaddr_in($other);
		($port) = unpack_sockaddr_in($me);
		$herhostname = gethostbyaddr($iaddr, AF_INET);
	} elsif ($family == AF_INET6) {
		(undef, $iaddr) = unpack_sockaddr_in6($other);
		($port) = unpack_sockaddr_in6($me);
		($err, $herhostname) = getnameinfo($iaddr);
	} else {
		syslog(LOG_ERR, 'unknown family tried to connect: '.$family);
		exit 1;
	}
	if ($herhostname) {
		syslog(LOG_INFO, "connect from $herhostname, ",inet_ntop($iaddr));
	}
}

my $ready = "READY: waiting for command$/";

my ($rc, $outs, $errs);

eval {
	local $SIG{ALRM} = sub { die "alarm\n" };
	LINE: while (1) {
		print $ready;
		if (eof(*STDIN)) {
			last LINE;
		}
		alarm 5;
		while (<STDIN>) {
			alarm 0;
			chomp($_);
			syslog(LOG_INFO,"input: $_");
			if (m/^USERID=(\d+)/) {
				$userid = $1;
				if ($userid) {

					if (!($user = db_single_assoc(
						# language=MariaDB
					  " SELECT	NICK, EMAIL,
								CONCAT(
									UPPER(SUBSTRING(SUBSTRING_INDEX(REALNAME, ' ', 1), 0, 1)),
									SUBSTRING(SUBSTRING_INDEX(REALNAME, ' ', 1), 1)
								) AS FIRST_NAME
						FROM party_db.user
						WHERE USERID = $userid"
					))) {
						print "ERROR:user:error:nonexistent::ID=$userid$/";
						syslog(LOG_ERR,"no user $userid");
						last LINE;
					}
					$okcam =    db_single("SELECT 1 FROM party_db.rights WHERE USERID = $userid AND `PORTION` = 'photographer'");
					$aspiring = db_single("SELECT 1 FROM party_db.rights WHERE USERID = $userid AND `PORTION` = 'aspiring_photographer'");
				}
				syslog(LOG_INFO, "user $userid registered");
				next LINE;
			}
			if (/^ANONYMOUS/) {
				$anonymous = 1;
				syslog(LOG_INFO,'make gallery anonymous');
				next LINE;
			}
			if (/^INVISIBLE/) {
				$invisible = 1;
				syslog(LOG_INFO, 'make gallery invisible');
				next LINE;
			}
			if (m/^UNLINK=(\d+)/) {
				$unlink = $1;
				syslog(LOG_INFO, 'files will '.($unlink ? '' : 'not ').'be removed from home dir');
				next LINE;
			}
			if (m/^PARTYID=(\d+)/) {
				$partyid = $1;
				$party = db_single_assoc('SELECT NAME, ACCEPTED, STAMP > UNIX_TIMESTAMP() AS FUTURE FROM party WHERE PARTYID = '.$partyid);
				if (!defined($party)
				||	!$party
				) {
					print "ERROR:party:error:nonexistent_LINE::PARTYID=$partyid$/";
					syslog(LOG_ERR,"party $partyid found");
					last LINE;
				}
				if (!$party->{'ACCEPTED'}) {
					print "ERROR:party:error:not_yet_accepted_LINE::PARTYID=$partyid;NAME=",$party->{'NAME'},$/;
					syslog(LOG_ERR,"party $partyid has not been validated yet");
					last LINE;
				}
				if ($party->{'FUTURE'}) {
					print "ERROR:event:error:is_in_the_future_LINE::PARTYID=$partyid$/";
					syslog(LOG_ERR,"party $partyid is in the future");
					last LINE;
				}
				if (defined($userid)) {
					# check whether userid made photo's here
				}
				syslog(LOG_INFO, "party $partyid registered");
				next LINE;
			}
			if (m/^GETIMAGES/) {
				if (!$userid) {
					print "ERROR:error:missing_some_data_LINE::PARAM=USERID$/";
					syslog(LOG_ERR, "GETIMAGES needs USERID to be set");
					last LINE;
				}
				undef(@images);

				enterdir();

				#@images = sort {$a->{'NAME'} cmp $b->{'NAME'}} @images;
				my $cnt = 0;
				foreach my $image (@images) {
					print	 "NAME=$image->{'NAME'}",
							"|PATH=$image->{'PATH'}",
							"|SIZE=",$image->{'STAT'}->size,
							"|TYPE=$image->{'TYPE'}",
							"|ERROR=$image->{'ERROR'}",
							"|WIDTH=$image->{'WIDTH'}",
							"|HEIGHT=$image->{'HEIGHT'}",
							"|ORIENTATION=$image->{'ORIENTATION'}",
							(defined $image->{'BAD'} ? '|BAD=1' : ''),
							"\n";
					++$cnt;
				}
				$ready = "READY: $cnt images shown$/";
				if ($#images > -1) {
					syslog(LOG_INFO,"getimages shown $cnt entries");
				} else {
					syslog(LOG_INFO,'no entries to be shown');
				}
				next LINE;
			}
			if (m/^(INCLUDE|REPLACE) (.+)/) {
				my $action = $1;
				if (!$userid) {
					print "ERROR:error:missing_some_data_LINE::PARAM=USERID$/";
					syslog(LOG_ERR, "INCLUDE|REPLACE needs USERID to be set");
					last LINE;
				}
				my $path;
				my $name;
				ARG: foreach my $arg (split(/\|/, $2)) {
					if ($arg =~ m/^NAME=(.+)/) {
						$name = $1;
						next ARG;
					}
					if ($arg =~ m/^PATH=(.*)/) {
						$path = $1;
						next ARG;
					}
					last ARG;
				}
				if (!defined($path)) {
					print "ERROR:error:missing_some_data_LINE::PARAM=PATH$/";
					syslog(LOG_ERR, 'INCLUDE|REPLACE needs PATH to be set');
					last LINE;
				}
				if (!defined($name)) {
					print "ERROR:error:missing_some_data_LINE::PARAM=NAME$/";
					syslog(LOG_ERR, 'INCLUDE|REPLACE needs NAME to be set');
					last LINE;
				}
				my $image = getimageinfo($path, $name);
				if (!defined($image)) {
					print "ERROR:camera:error:failed_to_get_information_about_file_LINE::PATH=$path;NAME=$name$/";
					last LINE;
				}
				if ($action eq 'INCLUDE') {
					syslog(LOG_INFO,'including');
					push(@publish,$image);
				} else {
					syslog(LOG_INFO,'replacing');
					if ($name !~ /^(\d+)(?:\.(?:[\.a-z]+))?$/i) {
						$ready = "ERROR: NAME is not IMGID ($name)\n";
						syslog(LOG_ERR, "invalid name: $name not a photo ID");
						last LINE;
					}
					my $imgid = $1;
					my $photo = db_single_assoc("SELECT image.USERID, party.STAMP FROM party_db.image JOIN party_db.party USING (PARTYID) WHERE IMGID=$imgid");
					if (!defined($photo)
					||	!$photo
					) {
						print "ERROR:photo:error:nonexistent_LINE::ID=$imgid$/";
						syslog(LOG_ERR, "photo $imgid not found");
						last LINE;
					}
					if ($photo->{'USERID'} != $userid) {
						print "ERROR:photo_process:error:photographers_dont_match_LINE::ID=$imgid$/";
						syslog(LOG_ERR, "user $userid is not owner of photo $imgid");
						last LINE;
					}
					if ($photo->{'STAMP'} < time() - 30 * 24 * 3600) {
						print "ERROR:photo_process:error:photo_is_of_event_too_long_ago_LINE$/";
						syslog(LOG_ERR, "photo $imgid belongs to event too long ago");
						last LINE;
					}
					$image->{'IMGID'} = $imgid;
					push @replacements, $image;
					$repimgids{"$imgid"} = 1;
					++$repimgidcnt;
					syslog(LOG_INFO,"image $imgid added to replace list");
				}
				next LINE;
			}
			if (m/^PUBLISH/) {
				if (!defined($partyid)) {
					print "ERROR:error:missing_some_data_LINE::PARAM=PARTYID$/";
					syslog(LOG_ERR, 'PUBLISH needs PARTYID to be set');
					last LINE;
				}
				if ($#publish == -1) {
					print "ERROR:photo_process:error:missing_files_to_process_LINE$/";
					syslog(LOG_ERR, 'PUBLISH needs files to process');
					last LINE;
				}

				# first readlock both image and image_reserved
				if (!db_lock(
					# language=MariaDB
					'LOCK TABLES party_db.image WRITE, party_db.image_reserved WRITE, party_db.camerarequest WRITE')
				) {
					#print "failed to lock image and image_reserved$/";
					print "ERROR:photo_process:error:failed_to_lock_tables_LINE$/";
					syslog(LOG_ERR,'could not lock tables');
					last LINE;
				}

				# Check whether photos where made this PARTYID by USERID.
				# I believe perl database layer does not like bit(1) fields, and hence the 0 + PROCESSING,
				# where PROCESSING is a bit(1) field.
				my $request = db_single_assoc(
					# language=MariaDB
				  " SELECT USERID, 0 + PROCESSING AS PROCESSING, STATUS
					FROM party_db.camerarequest
					WHERE PARTYID = $partyid
					  AND USERID  = $userid"
				);
				if (!$request
				||	!(	$request->{'STATUS'} eq 'accepted'
					||	$request->{'STATUS'} eq 'self'
					||	$request->{'STATUS'} eq 'extern'
					||	$request->{'STATUS'} eq 'take_over'
					||	$request->{'STATUS'} eq 'organization_request'
					)
				) {
					print "ERROR:photo_process:error:invalid_camerarequest_state_LINE$/";
					syslog(LOG_ERR, "invalid camera request, ".(!$request ? " it's missing" : " wrong STATUS: ".$request->{'STATUS'}));
					last LINE;
				}
				if ($request->{'PROCESSING'}) {
					print "ERROR:photo_process:error:photos_are_being_processed_LINE$/";
					syslog(LOG_ERR, 'images are already being processed');
					last LINE;
				}

				# are there images already?
				my $haveimgs = db_single("SELECT 1 FROM party_db.image WHERE PARTYID=$partyid AND USERID=$userid LIMIT 1");
				if (defined($haveimgs) || $haveimgs) {
					if ($#publish > 200) {
						print "ERROR:photo_process:error:too_many_files_to_process_might_be_accident_LINE$/";
						syslog(LOG_ERR, $#publish." files to process might be accidental");;
						next LINE;
					}
				}
				if (!db_insert("
					UPDATE party_db.camerarequest SET
						PROCESSING = 1
					WHERE PARTYID = $partyid
					  AND USERID = $userid"
				)) {
					print "ERROR:photo_process:error:failed_to_initialize_processing_LINE$/";
					syslog(LOG_ERR, 'failed to set PROCESSING to true');
					last LINE;
				}

				print "READY: photos are being processed$/";

				# NOTE: Close STDIN so connection via inetd is closed and flock shows notification to photographer.
				close STDIN;

				# For now, try with normal priority
				#($rc, $outs, $errs) = local_run("renice 20 $$");
				#if (!$rc) {
				#	syslog(LOG_INFO, "reniced to 20");
				#}

				syslog(LOG_DEBUG, 'fetching max image.IMGID');
				# first reserve some images
				my $maximgid = int(db_single('SELECT MAX(IMGID) FROM party_db.image') or 0);
				if (!$maximgid) {
					syslog(LOG_ERR, 'could not fetch maximum IMGID');
					last LINE;
				}
				syslog(LOG_DEBUG, 'fetching max image_reserved.IMGID');
				my $maxreserved = int(db_single('SELECT MAX(IMGID) FROM party_db.image_reserved') or 0);
				if (!$maxreserved) {
					syslog(LOG_ERR, 'could not feth maximum IMGID from image_reserveed');
					last LINE;
				}
				my $use_max = $maximgid > $maxreserved ? $maximgid : $maxreserved;
				++$use_max;

				syslog(LOG_DEBUG, "reserving $#publish IMGIDs starting from $use_max");

				#print "using $use_max as starting point\n";

				my @reserves;
				for (my $cnt = $use_max; $cnt <= $use_max + $#publish; ++$cnt) {
					push @reserves, "($userid, $cnt, UNIX_TIMESTAMP())";
				}
				if (!db_insert("INSERT INTO party_db.image_reserved (USERID,IMGID,STAMP) VALUES ".join(',',@reserves))) {
					print "ERROR:photo_process:error:could_not_reserve_images_LINE$/";
					syslog(LOG_ERR, 'could not reserve images');
					last LINE;
				}
				if (!db_lock('UNLOCK TABLES')) {
					print "ERROR:photo_process:error:failed_to_unlock_tables_LINE$/";
					syslog(LOG_ERR, 'failed to unlock tables');
					last LINE;
				}

				my ($srcdir, $destdir, $xtraset_jpeg, $xtraset_webp, $xtraset_avif) = get_base_info();

				# my (/* undef */ $login, /* undef */ $pass, $uid, $gid) = getpwnam('partydata');

				# create homedir
				CREATEDIR: foreach my $crdir (
					$destdir,
					$destdir.'/main',
					$destdir.'/main2x',
					$destdir.'/thumb',
					$destdir.'/thumb2x',
					$destdir.'/original',
					$destdir.'/unprocessed',
				) {
					if (-d $crdir) {
						syslog(LOG_DEBUG, "directory already exists: $crdir");
						next CREATEDIR;
					}
					if (!mkdir($crdir, 0755)) {
						print "ERROR:photo_process:error:failed_to_create_directory_LINE::DIR=$crdir;REASON=$!$/";
						syslog(LOG_ERR, "failed to create dir $crdir: $!");
						last LINE;
					}
					# chown ($uid, $gid, $crdir);
				}
				my $imgid = $use_max;
				my $ok = 1;
				my @imgids;
				foreach my $image (@publish) {
					if (defined $image->{'BAD'}) {
						next;
					}
					syslog(LOG_DEBUG, "processing next image");
					if (!($ok = process_image(
						$image,
						$imgid,
						$srcdir,
						$destdir,
						$xtraset_jpeg,
						$xtraset_webp,
						$xtraset_avif
						# $uid,
						# $gid
					))) {
						syslog(LOG_ERR, 'process_image failed');
						last;
					}
					push @imgids, $imgid;
					++$imgid;
				}
				if (!$ok) {
					# Remove any images that have been converted ok, doing only part is not useful
					foreach my $rm_imgid (@imgids) {
						foreach (glob("/mnt/gluster/photos_v2/$partyid/*/$rm_imgid*.*")) {
							syslog(LOG_DEBUG, "removing $_");
							if (!unlink "$_") {
								syslog(LOG_ERR, "failed to unlink: $!");
							}
						}
					}
					last LINE;
				}

				# now create all

				$imgid = $use_max;

				my $galleryid = db_single("
					SELECT GALLERYID
					FROM party_db.gallery
					WHERE USERID	= $userid
					  AND PARTYID	= $partyid"
				);
				my $pstamp = time();

				if (!defined($galleryid)
				||	!$galleryid
				) {
					if ($invisible) {
						# force invisible gallery
						$okcam = 0;
					}
					if (db_query("
						INSERT INTO party_db.gallery SET
							ANONYMOUS			= COALESCE($anonymous, (SELECT ANONYMOUS FROM party_db.gallery_default WHERE USERID = $userid)),
							PHOTOGRAPHER		= COALESCE((SELECT PHOTOGRAPHER FROM party_db.gallery_default WHERE USERID = $userid), ''),
							PHOTOGRAPHER_SITE	= COALESCE((SELECT PHOTOGRAPHER_SITE FROM party_db.gallery_default WHERE USERID = $userid), ''),
							TEXTOVERLAY			= COALESCE((SELECT TEXTOVERLAY FROM party_db.gallery_default WHERE USERID = $userid), ''),
							TITLE				= '',
							BODY				= '',
							VISIBLE				= '".($okcam ? 'yes' : 'no')."',
							ASPIRING			= ".($aspiring ? '1' : '0').",
							USERID				= $userid,
							PARTYID				= $partyid,
							EXTERN				= b'".($okcam || $aspiring ? '0': '1')."',
							CSTAMP				= $pstamp,
							PSTAMP				= $pstamp")
					) {
						$galleryid = $dbh->{'mysql_insertid'};
						syslog(LOG_INFO,"new gallery $galleryid created");
					} else {
						syslog(LOG_ERR,'failed to create new gallery');
						last LINE;
					}
				}
				db_lock('LOCK TABLES image WRITE, image_original_filename WRITE');
				my @rmlist;
				my %rmpath;
				foreach my $image (@publish) {
					if (defined $image->{'BAD'}) {
						next;
					}
					if (!db_query("
						INSERT INTO party_db.image SET
							GALLERYID		= $galleryid,
							IMGID			= $imgid,
							CSTAMP			= UNIX_TIMESTAMP(),
							PARTYID			= $partyid,
							USERID			= $userid,
							WIDTH			= $image->{'MAIN_WIDTH'},
							HEIGHT			= $image->{'MAIN_HEIGHT'},
							THUMB_WIDTH		= $image->{'THUMB_WIDTH'},
							THUMB_HEIGHT	= $image->{'THUMB_HEIGHT'},
							DOUBLE_WIDTH	= $image->{'DOUBLE_WIDTH'},
							DOUBLE_HEIGHT	= $image->{'DOUBLE_HEIGHT'},
							ORIGINAL_WIDTH	= $image->{'WIDTH'},
							ORIGINAL_HEIGHT	= $image->{'HEIGHT'}"
					)) {
						syslog(LOG_ERR,'failed to create new image');
					} else {
						syslog(LOG_INFO,"new image $imgid created");
					}

					if (!db_query("
						INSERT INTO party_db.image_original_filename SET
							IMGID		= $imgid,
							FILENAME	= ".$dbh->quote($image->{'NAME'})
					)) {
						syslog(LOG_ERR, 'failed to register original filename "'.$image->{'NAME'}." for image ".$imgid);
					} else {
						syslog(LOG_INFO, 'original filename "'.$image->{'NAME'}.'" registered for image '.$imgid);
					}
					if ($unlink) {
						my $file_name = "/home/<USER>/$userid/$image->{'PATH'}/$image->{'NAME'}";
						syslog(LOG_DEBUG, "adding to file remove list: $file_name");
						push @rmlist, $file_name;
						if ($image->{'PATH'}
						&&	!defined($rmpath{$image->{'PATH'}})
						) {
							syslog(LOG_DEBUG, "added to path remove list: $image->{'PATH'}");
							$rmpath{$image->{'PATH'}} = 1;
						}
					}
					++$imgid;
				}
				db_lock('UNLOCK TABLES');
				if ($#rmlist >= 0) {
					foreach (@rmlist) {
						syslog(LOG_INFO, "unlinking $_");
						if (!unlink $_) {
							syslog(LOG_ERR, "failed to unlink: $!")
						}
					}
				}
				foreach my $path (keys %rmpath) {
					syslog(LOG_INFO, "removing dir $path");
					# This rmdir might fail if there are still images left,
					# but that is no problem, that happens only if some error occurred while processing
					if (!rmdir "/home/<USER>/$userid/$path") {
						syslog(LOG_INFO, 'rmdir failed: '.$!);
					}
				}
				if (!db_insert("
					UPDATE party_db.camerarequest SET
						PROCESSING	= 0
					WHERE PARTYID = $partyid
					  AND USERID = $userid")
				) {
					syslog('warning','failed to reset processing bit');
				}
				if (!db_insert("
					INSERT INTO party_db.notifyinfo (USERID, TYPE, HAVE_IDSTAMP, SEEN_STAMP, UPD)
					SELECT going.USERID, 4, $galleryid, user.CSTAMP, 1
					FROM party_db.going
					JOIN party_db.notifydays USING (USERID)
					JOIN party_db.user_account USING (USERID)
					JOIN party_db.user USING (USERID)
					WHERE PARTYID = $partyid
					  AND STATUS = 'active'
					  AND NOT ISNULL(`4`)
					ON DUPLICATE KEY UPDATE
						UPD				= 1,
						HAVE_IDSTAMP	= GREATEST($pstamp, HAVE_IDSTAMP)"
					)
				) {
					syslog('warning','failed to insupd notifyinfo records');
				}
				syslog(LOG_INFO, 'processing finished');

				my $subject = 'Nieuwe fotoset online: '.$party->{'NAME'};

				my $social_desc = db_single("SELECT SOCIALDESC FROM party_db.gallerysocialdesc WHERE PARTYID = $partyid ORDER BY CSTAMP DESC LIMIT 1");

				my $mail_body =
					"Nieuwe fotoset online: ".$party->{'NAME'}."\r\n\r\n".
					"https://partyflock.nl/gallery/$galleryid\r\n\r\n".
					"Opmerking fotograaf: $social_desc";

				Email::Stuffer
					->to('Karian <<EMAIL>>')
					->cc('Thomas <<EMAIL>>, '.$user->{'FIRST_NAME'}.' <'.$user->{'EMAIL'}.'>')
					->header('Auto-Submitted'			=> 'auto-generated')
					->header('X-Autoreply'				=> 'true')
					->header('X-Autorespond'			=> 'true')
					->header('X-Auto-Response-Suppress' => 'RN,NRN,OOF,AutoReply')
					->header('Precedence'				=> 'auto_reply')
					->header('Content-Type'				=> 'text/plain; charset=UTF-8')
					->from('Partyflock berichtgever <<EMAIL>>')
					->subject($subject)
					->text_body($mail_body)
					->send;

				($rc, $outs, $errs) = local_run("nice -n 19 /home/<USER>/maintenance/determine_photo_crops.php gallery:$galleryid");
				if (!$rc) {
					syslog(LOG_INFO,"determined crops");
				}
				last LINE;
			}
			if (m/^PROCESS/) {
				if (!defined($partyid)) {
					print "ERROR:error:missing_some_data_LINE::PARAM=PARTYID$/";
					syslog(LOG_ERR, 'PROCESS needs PARTYID to be set');
					last LINE;
				}
				if (!defined($userid)) {
					print "ERROR:error:missing_some_data_LINE::PARAM=USERID$/";
					syslog(LOG_ERR, 'PROCESS needs USERID to be set');
					last LINE;
				}
				my $galleryid = db_single("
					SELECT GALLERYID
					FROM party_db.gallery
					WHERE USERID	= $userid
					  AND PARTYID	= $partyid"
				);
				if (!defined($galleryid)
				||	!$galleryid
				) {
					syslog(LOG_ERR, 'PROCESS could not determine GALLERYID');
					print "ERROR:photo_process:error:process_could_not_determine_gallery_LINE$/";
					last LINE;
				}
				if ($#replacements == -1) {
					print "ERROR:photo_process:error:no_replacements_found_to_process$/";
					syslog(LOG_ERR,'no replacements found to process');
					last LINE;
				}
				if ($#replacements != $repimgidcnt-1) {
					print "ERROR:photo_process:error:internal_replacement_counters_not_the_same_LINE$/";
					syslog(LOG_ERR,'internal replacement counters not the same');
					last LINE;
				}
				if (!db_lock('LOCK TABLES image_processing WRITE')) {
					print "ERROR:photo_process::error:failed_to_lock_images_LINE$/";
					syslog(LOG_ERR, 'failed to lock image_processing table');
					last LINE;
				}
				# check whether photo's where made this partyid by userid
				my $busylist = db_rowlist("
					SELECT IMGID
					FROM party_db.image_processing
					WHERE IMGID IN (".join(',',keys(%repimgids)).")
					  AND FINISHED = 0"
				);
				if (!defined($busylist)) {
					print "ERROR:photo_process:error:failed_to_query_busy_photos_LINE$/";
					syslog(LOG_ERR, 'failed to query busylist');
					last LINE;
				}
				my @dont;
				if ($#{$busylist} > -1) {
					foreach my $busy (@{$busylist}) {
						delete($repimgids{$busy->{'IMGID'}});
						push(@dont, $busy->{'IMGID'});
						--$repimgidcnt;
					}
				}
				if ($repimgidcnt) {
					foreach my $imgid (keys(%repimgids)) {
						if (!db_query("
							INSERT INTO party_db.image_processing SET
								IMGID		= $imgid,
								USERID		= $userid,
								STAMP		= UNIX_TIMESTAMP(),
								FINISHED	= 0
							ON DUPLICATE KEY UPDATE
								STAMP		= UNIX_TIMESTAMP(),
								FINISHED	= 0")
						) {
							delete($repimgids{$imgid});
							push(@dont,$imgid);
							--$repimgidcnt;
						}
					}
				}
				if (!db_lock('UNLOCK TABLES')) {
					print "ERROR:photo_process:error:failed_to_unlock_tables_LINE$/";
					syslog(LOG_ERR, 'failed to unlock tables');
					last LINE;
				}
				if (!$repimgidcnt || $#dont >= 0) {
					print "ERROR:photo_process:error:photos_already_processed_or_not_lockable_LINE::IDS=",join(',',@dont),";COUNT=$#dont$/";
					last LINE;
				}

				print "READY: replacing has started$/";
				#READY:photo_process:info:started_replacing_LINE::CNT=$repimgidcnt;IDS=",join(',', keys(%repimgids)),";IGNOREIDS=",join(',', @dont),"$/";

				close STDIN;
				close STDOUT;
				close STDERR;

				my ($srcdir,
					$destdir,
					$xtraset_jpeg,
					$xtraset_webp,
					$xtraset_avif
				) = get_base_info();

				# my ($login, $pass, $uid, $gid) = getpwnam('partydata');

				my @rmlist;
				my %rmpath;
				my $ok = 1;
				REPLACEMENT: foreach my $image (@replacements) {
					if (!defined($repimgids{$image->{'IMGID'}})) {
						next REPLACEMENT;
					}
					if (!($ok = process_image(
						$image,
						$image->{'IMGID'},
						$srcdir,
						$destdir,
						$xtraset_jpeg,
						$xtraset_webp,
						$xtraset_avif
						# $uid,
						# $gid
					))) {
						syslog(LOG_ERR, 'process_image failed!');
						last;
					}
					if (!db_query("
						UPDATE party_db.image SET
							WIDTH			= $image->{'MAIN_WIDTH'},
							HEIGHT			= $image->{'MAIN_HEIGHT'},
							THUMB_WIDTH		= $image->{'THUMB_WIDTH'},
							THUMB_HEIGHT	= $image->{'THUMB_HEIGHT'},
							DOUBLE_WIDTH	= $image->{'DOUBLE_WIDTH'},
							DOUBLE_HEIGHT	= $image->{'DOUBLE_HEIGHT'},
							ORIGINAL_WIDTH	= $image->{'WIDTH'},
							ORIGINAL_HEIGHT	= $image->{'HEIGHT'}
						WHERE IMGID = $image->{'IMGID'}
					")) {
						syslog('warning', 'failed to update database image '.$image->{'IMGID'});
						$ok = 0;
						last;
					}

					foreach (glob("/mnt/webstore/imgcache/$galleryid/$image->{'IMGID'}_*")) {
						syslog(LOG_DEBUG, "added to remove list: $_");
						push @rmlist, $_;
					}

					if ($unlink) {
						syslog(LOG_DEBUG, "added to file remove list: /home/<USER>/$userid/$image->{'PATH'}/$image->{'NAME'}");
						push @rmlist, "/home/<USER>/$userid/$image->{'PATH'}/$image->{'NAME'}";
						if ($image->{'PATH'}
						&&	!defined($rmpath{$image->{'PATH'}})
						) {
							syslog(LOG_DEBUG, "added to path remove list: /home/<USER>/$userid/$image->{'PATH'}");
							$rmpath{$image->{'PATH'}} = 1;
						}
					}
					if (!db_query("
						UPDATE party_db.image_processing SET
							FINISHED = UNIX_TIMESTAMP()
						WHERE IMGID = ".$image->{'IMGID'})
					) {
						syslog('warning', 'failed to reset image_processing');
					}
				}
				if ($#rmlist >= 0) {
					foreach (@rmlist) {
						syslog(LOG_INFO, "unlinking $_");
						if (!unlink "$_") {
							syslog(LOG_ERR, "failed to unlink: $!")
						}
					}
				}
				foreach my $path (keys %rmpath) {
					syslog(LOG_INFO, "removing dir /home/<USER>/$userid/$path");
					# This rmdir might fail if there are still images left,
					# but that is no problem, that happens only if some error occurred while processing
					if (!rmdir "/home/<USER>/$userid/$path") {
						syslog(LOG_ERR, "rmdir failed: $!");
					}
				}
				last LINE;
			}
			if (m/^(QUIT|EXIT)/) {
				print "READY: quiting$/";
				last LINE;
			}
			print "ERROR:photo_process:error:unknown_command_LINE::COMMAND=$_$/";
			last LINE;
		}
	}
};
if ($@) {
	die unless $@ eq "alarm\n";
	print "ERROR: timeout\n";
	syslog(LOG_ERR, 'timout occurred!');
}

sub enterdir {
	my $dirname = shift;
	if (!defined($dirname)) {
		$dirname = '';
	}
	my $CHECKDIR;
	my $path = "/home/<USER>/$userid";
	if ($dirname ne '') {
		$path .= "/$dirname";
	}
	syslog(LOG_DEBUG, "opening dir: $path");
	if (!opendir($CHECKDIR, $path)) {
		print "ERROR: failed to enter $path$/";
		syslog(LOG_ERR, "failed to enter $path");
		return;
	}
	syslog(LOG_DEBUG,"entered $path");

	my @files = sort { $a cmp $b } readdir $CHECKDIR;

	closedir $CHECKDIR;

	ENTRY: foreach my $entry (@files) {
		if ($entry =~ m/^\.{1,2}$/) {
			next ENTRY;
		}
		my $entryname = "$path/$entry";
		syslog(LOG_DEBUG, "encountered $entryname");

		my $stat = stat($entryname);
		if (S_ISREG($stat->mode)) {
			# check type
			my $image = getimageinfo($dirname, $entry, $stat);
			if (defined($image)) {
				push(@images, $image);
			}
			next ENTRY;
		}
		if (S_ISDIR($stat->mode)) {
			enterdir($dirname eq '' ? $entry : $dirname.'/'.$entry);
			next ENTRY;
		}
	}
}

sub getimageinfo {
	my ($path, $name, $stat) = @_;
	my $bad = 0;
	my $full_path = "/home/<USER>/$userid/".($path ? $path.'/' : '')."$name";
	$full_path =~ s/([`"])/\\$1/g; # single quotes are done by qx{}
	if (!defined($stat)) {
		$stat = stat($full_path);
	}
	my $exe = "file \"$full_path\"";

	syslog(LOG_DEBUG, 'execute: '.$exe);
	my $exe_result = qx{$exe};
	syslog(LOG_DEBUG, 'exe_result: '.$exe_result);

	my $width = 0;
	my $height = 0;
	my $error = '';
	my $srctype = 'unknown';
	my $orientation = 1;
	if ($exe_result =~ /PNG image(?: data)?, (\d+) x (\d+),/i) {
		$exe = "pngcheck \"$full_path\" 2>&1";
		syslog(LOG_DEBUG, 'execute: '.$exe);
		$exe_result = qx{$exe};
		if ($exe_result =~ /ERROR/) {
			$bad = 1;
			if ($exe_result =~ /^[^\h]+ (.*)[\r\n\r]+/) {
				$error = $1;
			}
		} else {
			$width = $1;
			$height = $2;
			$srctype = 'png';
		}
	} elsif (
		$exe_result =~ /TIFF image/
	&&	$exe_result !~ /JPEG image data/
	) {
		$exe = "exiv2 \"$full_path\" 2>&1";
		syslog(LOG_DEBUG, 'execute: '.$exe);
		$exe_result = qx{$exe};

		if ($exe_result =~ /Exif\h*Resolution\h*:\h*(\d+)\h*x\h*(\d+)/) {
			$width = $1;
			$height = $2;
			$srctype = 'tiff';

			$exe = "tiffinfo -D \"$full_path\" 2>&1 1>/dev/null";
			syslog(LOG_DEBUG, 'execute: '.$exe);
			$exe_result = qx{$exe};

			foreach my $line (split(/[\r\n]+/,$exe_result)) {
				chomp($line);
				if ($line !~ /: Warning/) {
					syslog(LOG_DEBUG,'got error line: '.$line);
					$error = $line;
					$bad = 1;
					last;
				}
			}
		} else {
			$error = 'failed to find resolution using exiv2';
			chomp($error);
			$bad = 1;
		}
	} else {
		$exe = "identify \"$full_path\"";
		syslog(LOG_INFO, "execute: $exe");
		$exe_result = qx{$exe};

		# Filename[frame #] image-format widthxheight page-widthxpage-height+x-offset+y-offset colorspace user-time elapsed-time
		# Example:
		# 1007967.jpg JPEG 6048x4024 6048x4024+0+0 8-bit sRGB 20.6853MiB 0.010u 0:00.002

		if ($exe_result =~ / (?<type>JPEG|AVIF|WEBP) (?<width>\d+)x(?<height>\d+)/) {
			$width = $+{width};
			$height = $+{height};
			if ($+{type} eq 'JPEG') {
				$srctype = 'jpg';
				if ($orientation = qx{jpegexiforient -n "$full_path"}) {
					$orientation = 0 + $orientation;
				}
			} else {
				$srctype = lc($+{type});
			}
		} else {
			$error = 'unrecognized';
			$bad = 1;
		}
	}
	syslog(LOG_INFO, "image detected as $srctype: ".($bad ? 'bad' : 'ok').", width = $width, height = $height");

	my $result = {
		'NAME'			=> $name,
		'PATH'			=> $path,
		'STAT'			=> $stat,
		'TYPE'			=> $srctype,
		'WIDTH'			=> $width,
		'ERROR'			=> $error,
		'HEIGHT'		=> $height,
		'ORIENTATION'	=> $orientation
	};
	if ($bad) {
		$result->{'BAD'} = 1;
	}
	return $result;
}

sub db_query {
	my $qstr = shift;

	TRYQRY: while (1) {
		if (!$dbh) {
			$dbh = DBI->connect($dsn, 'party', 'FR0Pfrop');

			if (!$dbh) {
				print "ERROR: could not connect to database$/";
				syslog(LOG_ERR,'could not connect to database');
				return 0;
			}
			if (!$dbh->do('SET SESSION query_cache_type=OFF')) {
				print "ERROR: could not turn off query cache$/";
				syslog(LOG_ERR,'could not turn off query cache');
				return 0;
			}
		}
		my $sth = $dbh->prepare($qstr);
		if (!$sth) {
			if ($dbh->{'mysql_errno'} == 2013
			||	$dbh->{'mysql_errno'} == 2006
			) {
				undef $dbh;
				next TRYQRY;
			}
			print "ERROR: could not prepare query$/";
			syslog(LOG_ERR,'could not prepare query ('.$dbh->{'mysql_errno'}.','.$dbh->{'mysql_error'}.'): '.$qstr);
			return 0;
		}
		if (!$sth->execute) {
			if ($dbh->{'mysql_errno'} == 2013
			||	$dbh->{'mysql_errno'} == 2006
			) {
				undef $dbh;
				next TRYQRY;
			}
			$qstr =~ s/[\r\n\t]+/ /g;
			print "ERROR: could not execute query ($qstr)$/";
			syslog(LOG_ERR,"could not execute query (".$dbh->{'mysql_errno'}.",".$dbh->{'mysql_error'}."): $qstr");
			return 0;
		}
		return $sth;
	}
}

sub db_lock {
	my $qstr = shift;
	return db_query($qstr);
}

sub db_insert {
	my $qstr = shift;
	return db_query($qstr);
}

sub db_single {
	my $qstr = shift;

	my $sth = db_query($qstr);

	if (!$sth) {
		return undef;
	}
	my $row = $sth->fetchrow_arrayref;
	if (!$row) {
		return undef;
	}
	return $row->[0];
}

sub db_single_assoc {
	my $qstr = shift;

	my $sth = db_query($qstr);

	if (!$sth) {
		return undef;
	}
	my $row = $sth->fetchrow_hashref;
	if (!$row) {
		return undef;
	}
	return $row;
}

sub db_rowlist {
	my $qstr = shift;
	my $sth = db_query($qstr);
	if (!$sth) {
		return undef;
	}
	my @result;
	while ((my $row = $sth->fetchrow_hashref)) {
		push(@result,$row);
	}
	return \@result;
}

sub get_base_info {
	my $comment =
		'This photo has been made available by the rights holder(s) for publication on Partyflock. '.
		'All rights reserved. For more information, <NAME_EMAIL>';
	return (
		'/home/<USER>/'.$userid,
		'/mnt/gluster/photos_v2/'.$partyid,

		# pnmtojpeg parameters:
		" --quality=93 --comment='$comment' --progressive --optimize --dct=float ",
		# convert to webp parameters:
		" -quality 85 -define webp:thread-level=1 -define webp:partitions=3 -define webp:image-hint=photo -define webp:method=6 -define webp:segment=4 ",
		# convert to avif parameters:
		" -quality 85 -depth 8 -define heic:speed=3 "
	);
}

sub process_image {
	my ($image,
		$imgid,
		$srcdir,
		$destdir,
		$xtraset_jpeg,
		$xtraset_webp,
		$xtraset_avif
		# $uid,
		# $gid
	) = @_;
	my ($rc, $outs, $errs);

	my $source_image = $srcdir.'/'.$image->{'PATH'}.($image->{'PATH'} eq '' ? '' : '/').$image->{'NAME'};

	syslog(LOG_DEBUG, 'source '.$source_image);

	my $unprocessed_destination = "$destdir/unprocessed/$imgid.".$image->{'TYPE'};

	syslog(LOG_DEBUG, "$imgid: copying source to $unprocessed_destination");

	if (!(copy $source_image, $unprocessed_destination)) {
		syslog(LOG_ERR, "failed to copy: $!");
		return 0;
	}
	my $sampling	  = '2x2';
	my $chroma_format = '420';
	my $pamflip = '';

	# exiftool tags: https://exiftool.org/TagNames/
	($rc, $outs, $errs) = local_run("$exiftool -G1 -S -n \"$source_image\"");
	if ($rc) {
		return 0;
	}
	if ($outs =~ /YCbCrSubSampling:\s*(\d+)\s+(\d+)/ims) {
		$sampling = $1.'x'.$2;
		syslog(LOG_DEBUG,"$imgid: detected sampling $sampling");
		if ($sampling eq '1x1') {
			$chroma_format = '444';
		} elsif ($sampling eq '2x1') {
			$chroma_format = '422';
		} else {
			$chroma_format = '420';
		}
	} elsif ($outs =~ /ChromaFormat:\s*(\d+)/ims) {
		if ($1 eq '0') {
			$sampling = '1x1';
			$chroma_format = '444';
		} elsif ($1 eq '2') {
			$sampling = '2x1';
			$chroma_format = '422';
		} elsif ($1 eq '3') {
			$sampling = '2x2';
			$chroma_format = '420';
		}
	}

	my $rotate = 0;
	my $pam_extra = '';
	if ($outs =~ /\[IFD0\]\s*Orientation:\s*(\d+)/ims) {
		syslog(LOG_DEBUG, "$imgid: orientation $1");
		if ($1 == 2) {
			$pam_extra = ' -leftright ';
		} elsif ($1 == 3) {
			$rotate = 180;
		} elsif ($1 == 4) {
			$pam_extra = ' -topbottom';
		} elsif ($1 == 5) {
			$pam_extra = ' -transpose';
		} elsif ($1 == 6) {
			$rotate = 90;
		} elsif ($1 == 7) {
			$pam_extra = ' -xform=transpose,topbottom,leftright ';
		} elsif ($1 == 8) {
			$rotate = 270;
		}
		if ($1 == 5 || $1 == 7 || $1 == 6 || $1 == 8) {
			my $tmp = $image->{'WIDTH'};
			$image->{'WIDTH'} = $image->{'HEIGHT'};
			$image->{'HEIGHT'} = $tmp;
		}
	}
	if ($rotate || $pam_extra) {
		$pamflip = 'pamflip '.$pam_extra;
		if ($rotate == 90) {
			$pamflip .= ' -rotate270 ';
		} elsif ($rotate == 180) {
			$pamflip .= ' -rotate180 ';
		} elsif ($rotate == 270) {
			$pamflip .= ' -rotate90 ';
		}
	}

	# Extra parameters for creation of JPEG using pnmtojpeg:
	$xtraset_jpeg .= " --sample=$sampling ";

	# (unused currently) Extra parameters for JPEG creation using using ImageMagick:
	# $xtraset_jpeg .= " -define jpeg:sampling-factor=$sampling ";

	# Extra parameters for AVIF creation using ImageMagick:
	$xtraset_avif .= " -define heic:chroma=$chroma_format ";

	my $tmp_pnm = "/tmpdisk/__photo_process_$imgid.pnm";

	my $conv_exe =
		$pamflip
	?	"$convert \"$source_image\" -colorspace sRGB pnm:- | $pamflip > $tmp_pnm"
	:	"$convert \"$source_image\" -colorspace sRGB $tmp_pnm";

	($rc, $outs, $errs) = local_run($conv_exe);
	if ($rc) {
		return 0;
	}

	# calculate what the width's and heights should be
	my $have_pixels = $image->{'WIDTH'} * $image->{'HEIGHT'};
	my $main_mult;	 my $double_mult = -1;
	my $main_width;	 my $double_width = 0,
	my $main_height; my $double_height = 0;

	if ($have_pixels <= $max_main) {
		# don't upscale or scale if identical
		$main_mult	 = 0;
		$main_width	 = $image->{'WIDTH'};
		$main_height = $image->{'HEIGHT'};
	} else {
		# scale if picture is not within 1% range
		my $part	 = $have_pixels / $max_main;
		$main_mult	 = ($part >= 0.99 && $part <= 1.01) ? 0 :  (1 / sqrt($have_pixels / $max_main));
	 	$main_width	 = $main_mult ? round($main_mult * $image->{'WIDTH'})  : $image->{'WIDTH'};
	 	$main_height = $main_mult ? round($main_mult * $image->{'HEIGHT'}) : $image->{'HEIGHT'};

	 	if ($image->{'WIDTH'} > $main_width) {
		 	$double_width  = min($main_width  << 1, $image->{'WIDTH'});
		 	$double_height = min($main_height << 1, $image->{'HEIGHT'});
	 		$double_mult   = $double_width / $image->{'WIDTH'};
		}
	}
	my  $thumb_mult   = 1 / sqrt($have_pixels / $max_thumb);
	my  $thumb_width  = round( $thumb_mult * $image->{'WIDTH'});
	my  $thumb_height = round( $thumb_mult * $image->{'HEIGHT'});

	syslog(LOG_INFO, "$imgid: $thumb_width x $thumb_height, $main_width x $main_height, $double_width x $double_height");

	$image->{'DOUBLE_WIDTH'}	= $double_width;
	$image->{'DOUBLE_HEIGHT'}	= $double_height;
	$image->{'MAIN_WIDTH'}		= $main_width;
	$image->{'MAIN_HEIGHT'}		= $main_height;
	$image->{'THUMB_WIDTH'}		= $thumb_width;
	$image->{'THUMB_HEIGHT'}	= $thumb_height;

	my $dst_orig_jpeg = "$destdir/original/$imgid.jpg";
	my $dst_orig_webp = "$destdir/original/$imgid.webp";
	my $dst_orig_avif = "$destdir/original/$imgid.avif";

	my @execs;
	push @execs,"pnmtojpeg -quiet $xtraset_jpeg $tmp_pnm >$dst_orig_jpeg";
 	push @execs,"$convert  -quiet $xtraset_webp $tmp_pnm  $dst_orig_webp";
	push @execs,"$convert  -quiet $xtraset_avif $tmp_pnm  $dst_orig_avif";

	if ($double_mult != -1) {
		push @execs,(
			$double_mult != 1
		?	"$convert -filter lanczos -resize ".$double_width."x".$double_height."! $tmp_pnm - | pnmtojpeg -quiet $xtraset_jpeg >$destdir/main2x/$imgid.jpg"
		:	"pnmtojpeg -quiet $xtraset_jpeg $tmp_pnm >$destdir/main2x/$imgid.jpg"
		);
		push @execs,(
			$double_mult != 1
		?	"$convert -filter lanczos -resize ".$double_width."x".$double_height."! $tmp_pnm $xtraset_webp $destdir/main2x/$imgid.webp"
		:	"$convert $xtraset_webp $tmp_pnm $destdir/main2x/$imgid.webp"
		);
		push @execs,(
			$double_mult != 1
		?	"$convert -filter lanczos -resize ".$double_width."x".$double_height."! $tmp_pnm $xtraset_avif $destdir/main2x/$imgid.avif"
		:	"$convert $xtraset_avif $tmp_pnm $destdir/main2x/$imgid.avif"
		);
	}
	push @execs,(
		$main_mult
	?	"$convert -filter lanczos -resize ".$main_width."x".$main_height."! $tmp_pnm - | pnmtojpeg -quiet $xtraset_jpeg >$destdir/main/$imgid.jpg"
	:	"pnmtojpeg -quiet $xtraset_jpeg $tmp_pnm >$destdir/main/$imgid.jpg"
	);
	push @execs, (
		$main_mult
	?	"$convert -filter lanczos -resize ".$main_width."x".$main_height."! $xtraset_webp $tmp_pnm $destdir/main/$imgid.webp"
	:	"$convert $xtraset_webp $tmp_pnm $destdir/main/$imgid.webp"
	);
	push @execs, (
		$main_mult
	?	"$convert -filter lanczos -resize ".$main_width."x".$main_height."! $xtraset_avif $tmp_pnm $destdir/main/$imgid.avif"
	:	"$convert $xtraset_avif $tmp_pnm $destdir/main/$imgid.avif"
	);
	push @execs,
	"$convert -filter lanczos -resize ".$thumb_width.      "x".$thumb_height."!       $tmp_pnm - | pnmtojpeg -quiet $xtraset_jpeg >$destdir/thumb/$imgid.jpg",
	"$convert -filter lanczos -resize ".($thumb_width * 2)."x".($thumb_height * 2)."! $tmp_pnm - | pnmtojpeg -quiet $xtraset_jpeg >$destdir/thumb2x/$imgid.jpg",
	"$convert -filter lanczos -resize ".$thumb_width.      "x".$thumb_height."!       $xtraset_webp $tmp_pnm $destdir/thumb/$imgid.webp",
	"$convert -filter lanczos -resize ".($thumb_width * 2)."x".($thumb_height * 2)."! $xtraset_webp $tmp_pnm $destdir/thumb2x/$imgid.webp",
	"$convert -filter lanczos -resize ".$thumb_width.      "x".$thumb_height."!       $xtraset_avif $tmp_pnm $destdir/thumb/$imgid.avif",
	"$convert -filter lanczos -resize ".($thumb_width * 2)."x".($thumb_height * 2)."! $xtraset_avif $tmp_pnm $destdir/thumb2x/$imgid.avif";

	# larger than 80 MiB consumes too much memory in parallel
	my $parallel = -s "$tmp_pnm" < 83886080;

	$parallel = 0;

	syslog(LOG_DEBUG, "$imgid: generating image sizes ".($parallel ? 'in parallel' : 'in sequence'));

	my $ok = 1;
	my @childs;
	my $pid;
	EXECS: for (@execs) {
		$pid = 0;
		if ($parallel) {
			$pid = fork();
			if ($pid != 0) {
				push @childs, $pid;
			}
		}
		if ($pid == 0) {
			($rc, $outs, $errs) = local_run($_);
			if ($rc) {
				if ($parallel) {
					exit 1;
				}
			}
			if ($parallel) {
				exit 0;
			}
		}
	}
	if ($parallel) {
		for (@childs) {
			waitpid $_, 0;
			if ($?) {
				syslog(LOG_ERR, "child with PID $_ failed with code ".($? >> 8));
				$ok = 0;
			}
		}
	}
	if ($ok) {
		if ($image->{'TYPE'} eq 'jpg'
		&&	-s $dst_orig_jpeg > -s $source_image
		) {
			if (-e "$dst_orig_jpeg") {
				syslog(LOG_DEBUG, "unlinking: $dst_orig_jpeg");
				if (!unlink "$dst_orig_jpeg") {
					syslog(LOG_ERR, "failed to unlink: $dst_orig_jpeg");
				}
			}
			syslog(LOG_DEBUG, "copying $source_image to $dst_orig_jpeg");
			if (!copy $source_image, $dst_orig_jpeg) {
				syslog(LOG_ERR, "failed to copy $source_image to $dst_orig_jpeg: $!");
			}
		}

		syslog(LOG_DEBUG, 'copying EXIF tags from source image to destination');
#		($rc, $outs, $errs) = local_run("$exiftool -v5 -overwrite_original -tagsFromFile \"$source_image\" $dst_orig_jpeg");
		($rc, $outs, $errs) = local_run("$exiftool -overwrite_original -tagsFromFile \"$source_image\" $dst_orig_jpeg");
		if ($rc) {
			syslog(LOG_ERR, 'failed to copy EXIF tags from source to destination');
			$ok = 0;
		}
#		if (-e "${dst_orig_jpeg}_exiftool_tmp") {
#			syslog(LOG_INFO, 'lingering file found: '.$dst_orig_jpeg.'_exiftool_tmp');
#		}
#		if (-e "${dst_orig_jpeg}_original") {
#			syslog(LOG_INFO, 'lingering file found: '.$dst_orig_jpeg.'_original');
#		}
	}
	# remove temporary files
	syslog(LOG_DEBUG, "unlinking $tmp_pnm");
	if (!unlink	"$tmp_pnm") {
		syslog(LOG_ERR, "failed to unlink: $!");
	}
	if (!$ok) {
		syslog(LOG_DEBUG, 'bad here');
		return 0;
	}
	# change ownership of newly created files
	# chown $uid, $gid,
	# 	"$destdir/original/$imgid.jpg",
	# 	"$destdir/original/$imgid.webp",
	# 	"$destdir/original/$imgid.avif",
	# 	"$destdir/thumb/$imgid.jpg",
	# 	"$destdir/thumb/$imgid.webp",
	# 	"$destdir/thumb/$imgid.avif",
	# 	"$destdir/thumb2x/$imgid.jpg",
	# 	"$destdir/thumb2x/$imgid.webp",
	# 	"$destdir/thumb2x/$imgid.avif",
	# 	"$destdir/main/$imgid.jpg",
	# 	"$destdir/main/$imgid.webp",
	# 	"$destdir/main/$imgid.avif",
	# 	"$destdir/main2x/$imgid.jpg",
	# 	"$destdir/main2x/$imgid.webp",
	# 	"$destdir/main2x/$imgid.avif";

	syslog(LOG_INFO,"processed image $imgid from source $source_image");
	return 1;
}

sub round {
	my $number = shift;
	# rounds only positive numbers
	return int($number + .5);
}

sub min($$) {
	my ($var_a, $var_b) = @_;
	return $var_a < $var_b ? $var_a : $var_b;
}

sub local_run {
	state $run_count = 0;
	syslog(LOG_INFO, "run,$run_count; ".join(' ', @_));
	my $outs;
	my $errs;
	my $ok = run3(@_, \undef, \$outs, \$errs);
	syslog(LOG_INFO, "run,$run_count; ok: ".($ok ? 'true' : 'false'));
	my $rc = $? ? ($? >> 8) : 0;
	if ($rc) {
		local $! = $rc;
		syslog(LOG_INFO, "run,$run_count.rc; $rc".($rc ? ", $!" : ''));
	}
	if ($errs) {
		syslog(LOG_ERR, "run,$run_count.error; $errs");
	}
	++$run_count;
	return ($rc, $outs, $errs);
}

sub local_execute {
	state $execute_count = 0;
	syslog(LOG_INFO, "execute,$execute_count; ".join(' ', @_));
	my ($rc, $outs, $errs) = execute(@_);
	if ($rc) {
		local $! = $rc;
		syslog(LOG_INFO, "execute,$execute_count.rc; $rc".($rc ? ", $!" : ''));
	}
	if ($errs) {
		syslog(LOG_ERR, "execute,$execute_count.error; $errs");
	}
	++$execute_count;
	return ($rc, $outs, $errs);
}
