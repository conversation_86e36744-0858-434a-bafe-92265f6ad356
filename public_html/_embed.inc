<?php

require_once '_browser.inc';
require_once '_hosts.inc';

function runembed(
	string		 $src,
	int|string   $width,
	int|string   $height,
	?array		 $attribs	= null,
	?string		 $id		= null,
	?string		 $class		= null,
	int|float|string|null $max_width = null,
): void {
	$max_width ??= 1;
	if ((	is_float($max_width)
		||	is_int($max_width)
		)
	&&	$width !== '100%'
	) {
		$max_width = ($max_width * 100).'%';
	}
	require_once '_cookie_consent.inc';
	?><iframe<?
	if ($id) {
		?> id="<?= $id ?>"<?
	}
	if ($class) {
		?> class="<?= $class ?>"<?
	}
	?> allowfullscreen<?
	# YouTube includes this 'allow' parameter. Without it, first embed will fail with "Video not found"
	?> allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"<?
	?> referrerpolicy="strict-origin-when-cross-origin"<?
	?> loading="lazy"<?
	?> src="<?= escape_specials($src) ?>"<?
	?> width="<?= $width ?>"<?
	?> height="<?= $height ?>"<?
	?> style="border: 0;<?
		if ($max_width) {
			?> max-width: <?= $max_width ?>;<?
		}
		if (isset($attribs['style'])) {
			echo $attribs['style'] ?>;<?
		}
	?>"<?
	if ($attribs) {
		foreach ($attribs as $key => $val) {
			if (str_starts_with($key, 'data-')) {
				?> <?
				echo $key;
				if ($val !== true) {
					echo '="', escape_specials($val), '"';
				}
			}
		}
	}
	?>></iframe><?
}
