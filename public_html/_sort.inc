<?php

function string_sort(array &$array, string $field, string $sort = 'usort'): void {
	$sort($array, function(array $a, array $b) use ($field): int {
		return strcasecmp($a[$field], $b[$field]);
	});
}

function string_asort(array &$array, string $field): void {
	string_sort($array, $field, 'uasort');
}
function doublestring_sort(array &$array, string $field1, string $field2, string $sort = 'usort'): void {
	$sort($array, function(array $a, array $b) use ($field1, $field2): int {
		return	strcasecmp($a[$field1], $b[$field1])
		?:	strcasecmp($a[$field2], $b[$field2]);
	});
}

function doublestring_asort(array &$array, string $field1, string $field2): void {
	doublestring_sort($array, $field1, $field2, 'uasort');
}

const asc =  1;
const dsc = -1;

function multifield_sort(array &$array, mixed $fields, string $sort = 'usort'): void {
	if (!is_array($fields)) {
		$argcnt = func_num_args();
		for ($i = 1; $i < $argcnt; ++$i) {
			$arg = func_get_arg($i);
			if (!isset($currfield)) {
				$currfield = $arg;
			} elseif ($arg === asc || $arg === dsc) {
				$args[$currfield] = $arg;
				unset($currfield);
			} else {
				$args[$currfield] = asc;
				$currfield = $arg;
			}
			if (isset($currfield)) {
 				$args[$currfield] = asc;
			}
		}
		$fields = $args;
		$sort = 'usort';
	}

	$sort($array, function (array $a, array $b) use ($fields): int {
		foreach ($fields as $field => $order) {
			if ($field[0] === chr(1)) {
				$fields = explode(',', substr($field, 1, -1));
				$aval = $bval = null;
				foreach ($fields as $field) {
					$aval ??= $a[$field];
					$bval ??= $b[$field];
				}
			} else {
				$aval = $a[$field];
				$bval = $b[$field];
			}
			if ($rc = $order * ($aval <=> $bval)) {
				return $rc;
			}
		}
		return 0;
	});
}

function sort_lineup_artists(array &$array, int $stamp = 0, string $sort = 'uasort'): void {
	$star_arrry = $array;

	$maxcnts = 0;
	$maxdst = null;

	$sort($array, function (array $a, array $b) use (&$maxcnts, $stamp, &$maxdst, $array): int {
		static $fields = [
			'ORDER_NAME'	=> [asc, 'utf8_strcasecmp'],
			'PREV_STAMP'	=> [dsc, null],
			'REALNAME'	=> [asc, 'utf8_strcasecmp'],
		];
		if ($maxdst === null) {
			$maxdst = isset($a['DST']) ? 0 : false;
		}
		if ($maxdst !== false) {
			$maxdst = max($maxdst, $a['DST'], $b['DST']);
		}
		foreach ($fields as $field => [$order, $cmp]) {
			$aval = $a[$field] ?? null;
			$bval = $b[$field] ?? null;

			if ($order == dsc) {
				$tmpval = $aval;
				$aval = $bval;
				$bval = $tmpval;
			}

			if ($aval === null) {
				if ($bval === null) {
					return 0;
				}
				return -1;
			} elseif ($bval === null) {
				return 1;
			}
			$rc = $cmp ? $cmp($aval, $bval) : ($aval <=> $bval);
			if ($rc) {
				return $rc;
			}
		}
		$acnt = $a['CNTS'] ?? 0;
		$bcnt = $b['CNTS'] ?? 0;
		$maxcnts = max($maxcnts, $acnt, $bcnt);

		return	(int)($bcnt - $acnt)
			?:	utf8_strcasecmp($a['IDENT'], $b['IDENT']);
	});
	if ($maxdst === false
	||	$maxdst === null
	) {
		return;
	}
	$best = null;
	$bestpts = null;
	$old_bestfit = null;
	if ($stamp) {
		$stamp = CURRENTSTAMP;
	}

	if (!$array) {
		mail_log('array is empty', get_defined_vars());
	}

	foreach ($array as $i => $artist) {
		if (isset($artist['BESTFIT'])) {
			$old_bestfit = $i;
		}

		# only determine lineup entries for artists that have same name DST:
		if (!$artist['DECEASED']
		&&	!$artist['STOPPED']
		&&	$artist['DST'] === $maxdst
		) {
			# 4 events more counts as 1 towards 'more probable'
			# 2 weeks within range counts as 1 towards 'more probable'

			# WAS : SELECT	SUM( '.(365*24*3600).' / POW(ABS( CAST(STAMP AS SIGNED) - '.$stamp.' ),1/10))

			# 1 / (x^2 + 1)
			# x in years, shift 1 year so 0 starts at worth 1
			# multiply by ONE_YEAR to get larger numbers

			$period = ONE_YEAR / 2;

			$pts = memcached_single(['lineup','party'],'
				SELECT SUM( '.$period.' / (POW(ABS( CAST(STAMP AS SIGNED) - '.$stamp.' ) / '.$period.',2) + 1))
				FROM lineup
				JOIN party USING (PARTYID)
				WHERE ARTISTID='.$artist['ARTISTID']
			);

			if ($bestpts === null
			||	$pts > $bestpts
			) {
				$best = $i;
				$bestpts = $pts;
			}
		}
	}
	if ($best !== null) {
		if ($old_bestfit) {
			unset($array[$old_bestfit]['BESTFIT']);
		}
		$array[$best]['BESTFIT'] = true;
	}
}

function number_sort(array &$array, string $field, string $sort = 'usort') {
	$sort($array, function(array $a, array $b) use ($field): int {
		return $a[$field] - $b[$field];
	});
}

function number_asort(array &$array, string $field): void {
	number_sort($array, $field, 'uasort');
}

function float_sort(array &$array, string $field, string $sort = 'usort'): void {
	$sort($array,function(array $a, array  $b) use ($field): int {
		return $a[$field] <=> $b[$field];
	});
}

function float_asort(array &$array, string $field): void {
	float_sort($array, $field, 'uasort');
}

function doublenumber_sort(array &$array, string $field1, string $field2, string $sort = 'usort'): void {
	$sort($array, function(array $a, array $b) use ($field1, $field2): int {
		return	$a[$field1] - $b[$field1]
		?:	$a[$field2] - $b[$field2];
	});
}

function doublenumber_asort(array &$array, string $field1, string $field2): void {
	doublenumber_sort($array, $field1, $field2, 'uasort');
}

function number_reverse_asort(array &$array, string $field): void {
	number_reverse_sort($array, $field, 'uasort');
}

function number_reverse_sort(array &$array, string $field, string $sort = 'usort'): void {
	$sort($array, function(array $a, array $b) use ($field): int {
		return $b[$field] <=> $a[$field];
	});
}

function country_city_sort(array &$array): void {
	$collator = get_collator();

	usort($array, function (array $a, array $b) use ($collator): int {
		switch (	$a['COUNTRYID'] === 1
			?	(	$b['COUNTRYID'] === 1
				?	0	// A == 1, B == 1
				:	-1	// A == 1, B != 1
				)
			:	(	$b['COUNTRYID'] === 1
				?	1	// A != 1, B == 1
				:	2	// A != 1, B != 1
			)
		) {
		case 0:
			# Netherlands
			return	($diff = $collator->compare($a['NAME'], $b['NAME']))
			||	$a['PROVINCEID'] !== $b['PROVINCEID']
			&&	($diff = $collator->compare($a['PROVINCE_NAME'], $b['PROVINCE_NAME']))
			||	$a['MUNICIPALITY_NAME']
			&&	($diff = $collator->compare($a['MUNICIPALITY_NAME'], $b['MUNICIPALITY_NAME']))
			?	$diff
			:	0;

		case -1:
			# a < b
			return -1;

		case 1:
			# b < a
			return 1;

		case 2:
			# differing country, but not Netherlands
			if ($a['COUNTRYID'] !== $b['COUNTRYID']) {
				return $collator->compare(
					get_element_title('country', $a['COUNTRYID']),
					get_element_title('country', $b['COUNTRYID'])
				);
			}
			return $collator->compare($a['NAME'], $b['NAME']);
		}
		return 0;
	});
}

function sort_meetings(array &$meetings, int $stamp): void {
	[,,, $hour, $mins] = _getdate($stamp);
	$host_time = $hour * 100 + $mins;
	usort($meetings, function (array $a, array $b) use ($host_time): int {
		$bx = ($b['HOUR'] * 100 + $b['MINS']);
		if ($bx < $host_time - 6 * 100) {
			$bx += 10000;
		}
		$ax = ($a['HOUR'] * 100 + $a['MINS']);
		if ($ax < $host_time - 6 * 100) {
			$ax += 10000;
		}
		return $ax - $bx;
	});
}

function string_alensort(array &$array): void {
	uasort($array, static function (string $a, string $b): int {
		return	strlen($a) - strlen($b)
		?:	strcasecmp($a, $b);
	});
}

function poll_sort(array &$array, int $modifier = 1): void {
	uasort($array, static function (array $a, array $b) use ($modifier): int {
		[$a] = $a;
		[$b] = $b;
		return $modifier * ($a - $b);
	});
}
