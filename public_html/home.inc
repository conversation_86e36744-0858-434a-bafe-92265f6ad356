<?php

const MAX_FLYERS_MOBILE		= 4;
const MAX_FLYERS_DESKTOP	= 6;
const FORCE_FRONTS_IF_LATE	= 100;	# 100 impressions or more late required for forced ads

function perform_commit(): ?bool {
	return match($_REQUEST['ACTION']) {
		'seen-invites'	=> home_seen_invites(),
		default			=> null,
	};
}
function display_body(): void {
	switch ($_REQUEST['ACTION']) {
	case null:
	case 'seenbuddies':
	case 'seen-invites':
		home_display_overview();
		return;
	default:
		not_found();
	}
}
function home_display_overview(): void {
	layout_section_header();

	include_style('home');

	display_messages();

	if (have_user()) {
		require_once '_flocklist.inc';
		if (false === ($seen = memcached_single('flockinvitation_seen','SELECT STAMP FROM flockinvitation_seen WHERE USERID='.CURRENTUSERID))) {
			return;
		}
		if (flocklist_display_invites($seen)) {
			?><div class="funcs"><?
				?><a href="/home/<USER>"><?= __C('action:mark_seen') ?></a><?
			?></div><?
		}
		if (have_flock_leader()) {
			flocklist_display_requests();
		}
	}
	if (!SERVER_SANDBOX
	&&	have_admin('contest')
	) {
		require_once '_contestlist.inc';
		$contestlist = new _contestlist();
		$contestlist->important_all();
		$contestlist->order_by_close_stamp();
		$contestlist->no_pages = true;
		$contestlist->show_header = true;
		if ($contestlist->query()) {
			$contestlist->display(Eelement_plural_name('contests_to_close'));
		}
	}
	if (have_admin('camerarequest')
	&&	($galleries = db_simple_hash('gallery','
			SELECT GALLERYID,PARTYID
			FROM party_db.gallery
			WHERE NOT THANKED_BY
			  AND GALLERYID > 6568'
	))) {
		if ($thanked_galleryid = have_idnumber($_REQUEST,'GALLERYID')) {
			db_update('gallery','
			UPDATE gallery SET
				THANKED		= '.(empty($_REQUEST['THANKED']) ? '0' : '1').',
				THANKED_BY	= '.CURRENTUSERID.',
				THANKED_AT	= '.CURRENTSTAMP.'
			WHERE GALLERYID = '.$thanked_galleryid
			);
			unset($galleries[$thanked_galleryid]);
		}
		if ($galleries) {
			layout_open_box('white');
			layout_box_header('Organisaties bedanken voor cameratoestemming');
			?><table class="fw default hhla"><?
			foreach ($galleries as $galleryid => $partyid) {
				?><tr><?
				?><td><?= get_element_link('gallery', $galleryid) ?></td><?
				?><td><a target="_blank" href="/camera/<?= $partyid ?>"><?= element_name('camerarequest') ?></a></td><?
				?><td><a class="center seemtext"<?
				?> onmouseover="changerow(this,'addclass','hilited-red')"<?
				?> onmouseout="changerow(this,'remclass','hilited-red')"<?
				?> href="/?THANKED=0;GALLERYID=<?= $galleryid ?>"><?=
					get_special_char('rem',['title' => 'niet gedaan']),' ',__('answer:no')
				?></a></td><td><?
				?><a class="center seemtext"<?
				?> onmouseover="changerow(this,'addclass','hilited-green')"<?
				?> onmouseout="changerow(this,'remclass','hilited-green')"<?
				?> href="/?THANKED=1;GALLERYID=<?= $galleryid ?>"><?=
					get_special_char('add',['title' => 'gedaan']),' ',__('answer:yes')
				?></a></td><?
				?></tr><?
			}
			?></table><?
			layout_close_box();
		}
	}
	if (have_super_admin()
	&&	($fails = memcached_rowuse_array(['contact_undeliverable', 'undelivercheck'], "
		SELECT *, UNCOMPRESS(CONTENT) AS CONTENT
		FROM party_db.contact_undeliverable
		WHERE FAILMAIL = ''
		AND CIMID > (SELECT CIMID FROM party_db.undelivercheck)"
	))) {
		layout_open_box('white');
		print_rr($fails);
		layout_close_box();
	}
	if (!SMALL_SCREEN) {
		?><div id="homec"><?
		?><div id="lcol"><?
	}
	require_once '_homenews.inc';
	$home_news = new homenews();
	$home_news->show_teasers();

	if ($shown_billb
	=	$home_news->double()
	&&	$home_news->get_shown() <= 4
	) {
		echo "\x01BILLB\x02";
	}
	echo "\x01BOXAD\x02";

	home_display_contests();
	home_display_galleries();
	if (!$shown_billb) {
		echo "\x01BILLB\x02";
	}
	show_frontpage_items($home_news->get_shown());
	show_ad_placeholder();
	$home_news->show_lines();
	if (SMALL_SCREEN) {
		home_display_misc();
		return;
	}
	?></div><? # lcol
	?></div><? # homec
}

function home_display_misc(): void {
	$someweekstamp = TODAYSTAMP - ONE_MONTH;
	$choices = [0,1,2,3];

	safe_shuffle($choices);
	foreach ($choices as $which) {
		$element = match($which) {
			0 => 'interview',
			1 => 'review',
			2 => 'column',
			3 => 'report',
		};
		# use DISTINCT to make sure the join_only_for_current_country groups together
		if (!($items = memcached_rowuse_hash(
			$element,'
			SELECT DISTINCT '.($itemid = strtoupper($element).'ID').','.
			(	$element === 'report'
			?	'IF(TITLE != "", TITLE, party.NAME) AS TITLE'
			:	'TITLE'
			).',
				PSTAMP'.($which === 1 ? ',RETRO' : '').'
			FROM `'.$element.'` AS item'.
			(	$element === 'report'
			?	' LEFT JOIN party USING (PARTYID) '
			:	null
			).'
			'.join_only_for_current_country($element,$itemid).'
			WHERE item.PSTAMP > '.$someweekstamp.'
			  AND item.ACCEPTED = 1'.
			  ($element === 'report' ? ' AND OFFICIAL = 1 ' : '').'
			ORDER BY PSTAMP DESC
			LIMIT 20',
			FIVE_MINUTES
		))) {
			if ($items === false) {
				return;
			}
			continue;
		}
		if (false === ($contests = memcached_simple_hash(['connect','contest'],'
			SELECT MAINID, GROUP_CONCAT(ASSOCID)
			FROM connect
			JOIN contest
			WHERE MAINTYPE = "'.$element.'" AND MAINID IN ('.implodekeys(',',$items).')
			  AND ASSOCTYPE = "contest" AND ASSOCID = CONTESTID
			  AND PSTAMP < '.CACHESTAMP.'
			  AND CLOSE_STAMP > '.CACHESTAMP.'
			  AND ACTIVE = 1'
		))) {
			return;
		}
		assert(is_array($contests)); # Satisfy EA inspection
		$header_shown = false;
		foreach ($items as $itemid => $item) {
			if ($item['PSTAMP'] > CURRENTSTAMP) {
				continue;
			}
			if (!$header_shown) {
				$header_shown = true;
				layout_open_box('white');
				?><div class="small center block"><b><?
				?><a href="/<?= $element ?>"><?= Eelement_plural_name($element) ?></a><?
				?></b></div><?
				?><table class="small fw front-items"><?
			}
			$new = $item['PSTAMP'] > CURRENTSTAMP-2*ONE_DAY;
			$href = get_element_href($element,$itemid,$item['TITLE']);
			?><tr class="hh box hpad <? echo $element;
			if ($new) {
				?>-recent<?
			}
			?>" onclick="openLink(event, this, false, true);"><?
			?><td><a onclick="return false" class="seemtext" href="<?= $href ?>"><?= flat_with_entities($item['TITLE'],UBB_UTF8)
			?></a><?
			if (!empty($item['RETRO'])) {
				?> <small>(<?= element_name('retro_review') ?>)</small><?
			}
			if (isset($contests[$itemid])) {
				?> <span class="win"><?= __('partylist:win') ?></span><?
			}
			?></td></tr><?
		}
		if ($header_shown) {
			?></table><?
			layout_close_box();
		}
	}
}

function home_display_galleries(): void {
	require_once '_galleryoverview.inc';
	$galleryoverview = new galleryoverview();
	$galleryoverview->show = 'front';
	$galleryoverview->query();
	$galleryoverview->display();
}

function home_display_contests(): void {
	require_once '_profile.inc';
	# use DISTINCT to make sure the join_only_for_current_country groups together
	if (!($contests = db_rowuse_array(['contest', 'party'],'
		SELECT DISTINCT CONTESTID, PARTYID, contest.NAME AS CONTEST_NAME, party.NAME, EROTIC
		FROM contest
		LEFT JOIN party USING (PARTYID) '.
		join_only_for_current_country('party').'
		WHERE ACTIVE
		  AND CLOSED = 0
		ORDER BY CLOSE_STAMP ASC'))
	) {
		return;
	}
	$keep = [];
	foreach ($contests as &$contest) {
		$name = $contest['NAME'] ?: $contest['CONTEST_NAME'];
		if (isset($keep[$name])) {
			continue;
		}
		$contest['name'] = $name;
		$keep[$name] = $contest;
	}
	unset($contest);
	safe_shuffle($keep);
	$cnt = 0;
	$max = SMALL_SCREEN ? 8 : 9;
	?><div class="winboxes"><?
	foreach ($keep as $contest) {
		/** @noinspection NotOptimalIfConditionsInspection */
		if ($contest['EROTIC']
		&&	(	!($current_profile ??= current_profile() ?: [])
			||	empty($current_profile['erotic']))
		) {
			continue;
		}
		?><a href="<?= get_element_href('contest',$contest['CONTESTID']) ?>" class="seemtext"><?
		?><div class="winbox"><?
		?><span class="win"><?= __('incentive:WIN') ?></span> <?=
			escape_utf8($contest['name'])
		?></div><?
		?></a> <?
		if (++$cnt === $max) {
			break;
		}
	}
	if (($total = count($contests)) > $max) {
		?><a href="/contest"><?
		?><div class="winbox showall win"><?= __('home:show_all_contests',['CNT' => $total]) ?></div><?
		?></a><?
	}
	?></div><?
}

function home_seen_invites(): bool {
	if (!require_user()
	||	!db_replace('flockinvitation_seen','
		REPLACE INTO flockinvitation_seen SET
			USERID	='.CURRENTUSERID.',
			STAMP	='.CURRENTSTAMP)
	) {
		return false;
	}
	memcached_delete('flockinvites:'.CURRENTUSERID);
	return true;
}

/*function show_buddy_birthdays(): void {
	if (!have_user()) {
		return;
	}
	require_once '_buddies.inc';
	$buddies = _buddies_full_cached_list(CURRENTUSERID);
	if (!$buddies) {
		return;
	}
	require_once '_visibility.inc';
	global $__year,$__month,$__day;
	$no29= 	(	$__month == 2 && $__day >= 26 && $__day <= 28
		||	$__month == 3 && $__day == 1
		)
	&&	!checkdate(2,29,$__year);
	$spart = '';
	foreach ([
		[$__year, $__month, $__day],
		_getdate(strtotime('+1 day', CURRENTSTAMP)),
		_getdate(strtotime('+2 day', CURRENTSTAMP)),
	] as $offset => $date) {
		[$year, $month, $day] = $date;
		$where = '(BIRTH_DAY = '.$day.' AND BIRTH_MONTH = '.$month;
		if ($no29) {
			switch ($month) {
			case 2:
				if ($day == 28) {
					$where .= ' OR BIRTH_MONTH = 2 AND BIRTH_DAY = 29 AND LDAY = 28';
				}
				break;
			case 3:
				if ($day == 1) {
					$where .= ' OR BIRTH_MONTH = 2 AND BIRTH_DAY = 29 AND LDAY = 1';
				}
				break;
			}
		}
		$wheres[] = $where.')';
		$spart .= ' WHEN '.$where.') THEN '.$offset;
	}
	$order =
		$no29
	?	'IF(	BIRTH_MONTH = 2 AND BIRTH_DAY = 29,
				IF(LDAY = 1, 3, 2),
				BIRTH_MONTH
		),
		IF(	BIRTH_MONTH = 2 AND BIRTH_DAY = 29,
			LDAY,
			BIRTH_DAY
		),NICK'
	:	'BIRTH_MONTH, BIRTH_DAY, NICK';
	$buddybdays = memcached_rowuse_array(
		array('user','user_account'),$qstr = '
		SELECT USERID,BIRTH_YEAR,BIRTH_MONTH,BIRTH_DAY,LDAY,user_account.NICK,VISIBILITY_ONLINE,
			CASE '.$spart.' END AS OFFSET
		FROM user
		JOIN user_account USING (USERID)
		WHERE STATUS="active"
		  AND USERID IN ('.implode(',',$buddies).')
		  AND ('.implode(' OR ',$wheres).')
		  AND (BIRTH_YEAR>'.($__year-100).' OR BIRTH_YEAR=0)
		  AND VISIBILITY_BIRTHDAY<'.SELF.'
		ORDER BY '.$order,
		FIVE_MINUTES
	);
	if (!$buddybdays) return;
	$today = $tomorrow = $twomorrow = 0;
	$offset = -1;
	$cnt = 0;
	$currmonth = 0;
	$currday = 0;
	ob_start();
	foreach ($buddybdays as $user) {
		extract($user);
		if ($no29
		&&	$BIRTH_MONTH == 2
		&&	$BIRTH_DAY == 29
		) {
			if ($LDAY == 1) {
				$BIRTH_MONTH = 3;
				$BIRTH_DAY = 1;
			} else {
				$BIRTH_DAY = 28;
			}
		}
		if ($currmonth != $BIRTH_MONTH
		||	$currday != $BIRTH_DAY
		) {
			$currmonth = $BIRTH_MONTH;
			$currday = $BIRTH_DAY;
			if ($currmonth) {
				?></tbody><?
			}
			?><tbody<?
			if ($OFFSET) {
				?> class="small"<?
			}
			?>><?
			?><tr class="nowrap nohl"><th><?=
			$date = __(
				!$OFFSET
			?	'date:today'
			:	(	$OFFSET == 1
				?	'date:tomorrow'
				:	'date:dayaftertomorrow'
				)
			); ?></th><td class="right small"><?
			echo $currday,' ',_month_name($currmonth);
			?></td></tr><?
		}
		++$cnt;
		layout_start_rrow(0,null,is_online($user['USERID']) ? 'online' : null);
		echo get_element_link('user',$user['USERID'],$user['NICK']);
		layout_next_cell(class: 'right');
		if ($user['BIRTH_YEAR']) {
			echo compute_age($BIRTH_YEAR, $BIRTH_MONTH, $BIRTH_DAY) + ($OFFSET ? 1 : 0);
		}
		layout_stop_row();
		switch ($OFFSET) {
		case 0:	++$today; break;
		case 1: ++$tomorrow; break;
		case 2: ++$twomorrow; break;
		default: error_log('arghIER',0);
		}
	}
	if ($data = ob_get_clean()) {
		require_once '_heart.inc';
		layout_open_box('red');
		ob_start();
		show_heart(null,'lower hrt');
		$heart = ob_get_clean();
		if ($hidden = true) { //have_admin()) {//$cnt > 3
#			error_log('hidden bdays: '.$cnt,0);
			?><div onclick="swapdisplay(this.nextSibling);toggleclass(this.firstChild,'nomargin')" class="ptr"><?
//			$hidden = false;
#			foreach ($dates as $desc => $tmpcnt) {
#				$parts[] = $tmpcnt.' '.$desc;
#			}
			$parts = array();
			if ($today) {
				$parts[] = $today.' '.__('date:today');
			}
			if ($tomorrow || $twomorrow) {
				if ($parts) {
					$parts[] = ($tomorrow + $twomorrow).' '.__('date:later');
				} else {
					if ($tomorrow) {
						$parts[] = $tomorrow.' '.__('date:tomorrow');
					}
					if ($twomorrow) {
						$parts[] = $twomorrow.' '.__($parts ? 'date:later' : 'date:dayaftertomorrow');
					}
				}
			}
		}
		layout_box_header(
			($hidden ? '<small class="nowrap">' : null).
				Eelement_name('birthday',$cnt).($hidden ? ', <span class="nowrap">'.implode(', ',$parts).'</span></small>' : null),
			'<div class="abs" style="right:.5em">'.$heart.'</div>',
			($hidden ? 'nomargin' : null)
		);
		if ($hidden) {
			?></div><?
		}
		layout_open_table(($hidden ? 'hidden ' : null).'fw default hla');
		echo $data;
		?></tbody><?
		layout_close_table();
		layout_close_box();
	}
}*/

function show_frontpage_items(int $shown_news = 0): void {
	$teaser_week_stamp = TODAYSTAMP - 2 * ONE_WEEK;
	$try_teasers = memcached_rowuse_hash(
		array('review','interview'),'
	(	SELECT CONCAT("interview",INTERVIEWID) AS NDX,"interview" AS ELEMENT,INTERVIEWID AS ID,PSTAMP,0 AS RETRO,
			TITLE, TEASER,
			(SELECT COUNT(*) FROM interview_comment WHERE ID=INTERVIEWID AND ACCEPTED=1) AS CMTS,
			(SELECT CONTESTID FROM contest JOIN connect ON MAINTYPE="contest" AND MAINID=CONTESTID AND ASSOCTYPE="interview" WHERE ASSOCID=INTERVIEWID AND PSTAMP < '.CACHESTAMP.' AND CLOSE_STAMP>'.CACHESTAMP.' AND ACTIVE LIMIT 1) AS CONTESTID
		FROM interview
		WHERE ACCEPTED = 1
		  AND PSTAMP >= '.$teaser_week_stamp.'
		  AND PSTAMP < '.(TODAYSTAMP + ONE_DAY).'
		ORDER BY PSTAMP DESC
	) UNION (
		SELECT CONCAT("review",REVIEWID) AS NDX,"review" AS ELEMENT,REVIEWID AS ID,PSTAMP,RETRO,
			TITLE, TEASER,
			(SELECT COUNT(*) FROM review_comment WHERE ID=REVIEWID AND ACCEPTED = 1) AS CMTS,
			(SELECT CONTESTID FROM contest JOIN connect ON MAINTYPE="contest" AND MAINID=CONTESTID AND ASSOCTYPE="review" WHERE ASSOCID=REVIEWID AND PSTAMP < '.CACHESTAMP.' AND CLOSE_STAMP>'.CACHESTAMP.' AND ACTIVE LIMIT 1) AS CONTESTID
		FROM review
		WHERE ACCEPTED = 1
		  AND PSTAMP >= '.$teaser_week_stamp.'
		  AND PSTAMP < '.(TODAYSTAMP + ONE_DAY).'
		ORDER BY PSTAMP DESC
	) UNION (
		SELECT CONCAT("column",COLUMNID) AS NDX,"column" AS ELEMENT,COLUMNID AS ID,PSTAMP,0 AS RETRO,
			TITLE, TEASER,
			(SELECT COUNT(*) FROM column_comment WHERE ID=COLUMNID AND ACCEPTED = 1) AS CMTS,
			(SELECT CONTESTID FROM contest JOIN connect ON MAINTYPE="contest" AND MAINID=CONTESTID AND ASSOCTYPE="column" WHERE ASSOCID=COLUMNID AND PSTAMP < '.CACHESTAMP.' AND CLOSE_STAMP>'.CACHESTAMP.' AND ACTIVE LIMIT 1) AS CONTESTID
		FROM `column`
		WHERE ACCEPTED = 1
		  AND PSTAMP >= '.$teaser_week_stamp.'
		  AND PSTAMP < '.(TODAYSTAMP + ONE_DAY).'
		ORDER BY PSTAMP DESC
	) UNION (
		SELECT CONCAT("report", REPORTID) AS NDX,"report" AS ELEMENT,REPORTID AS ID,PSTAMP,0 AS RETRO,
			IF(TITLE != "", TITLE, party.NAME) AS TITLE, BODY AS TEASER,
			(SELECT COUNT(*) FROM report_comment WHERE ID=REPORTID AND ACCEPTED = 1) AS CMTS,
			(SELECT CONTESTID FROM contest JOIN connect ON MAINTYPE="contest" AND MAINID=CONTESTID AND ASSOCTYPE="report" WHERE ASSOCID=REPORTID AND PSTAMP < '.CACHESTAMP.' AND CLOSE_STAMP>'.CACHESTAMP.' AND ACTIVE LIMIT 1) AS CONTESTID
		FROM `report`
		LEFT JOIN party USING (PARTYID)
		WHERE report.ACCEPTED = 1
		  AND OFFICIAL=1
		  AND report.PSTAMP >= '.$teaser_week_stamp.'
		  AND report.PSTAMP < '.(TODAYSTAMP + ONE_DAY).'
		ORDER BY report.PSTAMP DESC
	)', TEN_MINUTES
	);
	if (!$try_teasers) {
		return;
	}
	$size = 0;
	foreach ($try_teasers as $teaser) {
		if ($teaser['PSTAMP'] <= CURRENTSTAMP) {
			$teasers[$teaser['NDX']] = $teaser;
			++$size;
		}
	}
	if (empty($teasers)) {
		return;
	}
	foreach ($teasers as $teaser) {
		$where_parts[] = '("'.$teaser['ELEMENT'].'",'.$teaser['ID'].')';
	}
	if (false === ($teaser_count = memcached_rowuse_hash('teasercount', '
		SELECT CONCAT(ELEMENT,ID) AS NDX,HITS
		FROM teasercount
		WHERE (ELEMENT,ID) IN ('.implode(',',$where_parts).')',
		TEN_MINUTES
	))) {
		return;
	}
	foreach ($teasers as $ndx => $teaser) {
		if (!isset($teaser_count[$ndx])) {
			$teaser_count[$ndx] = ['NDX'=>$ndx,'HITS'=>0];
		}
	}
	number_sort($teaser_count,'HITS');
	$chosen_teasers = array_slice($teaser_count,0,$shown_news > (SMALL_SCREEN ? 1 : 2) ? 2 : 4);//$size >= 4 ? 4 : 2);
	require_once '_uploadimage.inc';
	$cnt = 0;
	$vals = [];
	$uploadimage_flags = UPIMG_NOCHANGE | UPIMG_NO_OPENGRAPH;
	?><table class="vtop front-teasers"><?
	foreach ($chosen_teasers as $teaserinfo) {
		$left = !($cnt % 2);
		/** @noinspection PhpRedundantOptionalArgumentInspection */
		extract($teasers[$teaserinfo['NDX']], \EXTR_OVERWRITE);
		$vals[] = "('$ELEMENT', $ID)";
		if ($left) {
			if ($cnt) {
				?></tr><?
			}
			?><tr><?
		}
		if ($ELEMENT === 'report') {
			require_once '_bio.inc';
			$TEASER = get_teaser($TEASER, $same, 500);
		}
		?><td <?
		?> class="<?= LITE ? $ELEMENT : ($ELEMENT === 'review' ? 'grey' : $ELEMENT) ?> hlbox box front-teaser"<?
		?> onclick="openLink(event, '<?= $href = get_element_href($ELEMENT,$ID) ?>', false, true)"><?
			uploadimage_show_with_fallback($ELEMENT, $ID, flags: $uploadimage_flags | UPIMG_R, size: 'tiny');
		?><div><?
			?><b><a onclick="return false" href="<?= $href ?>"><?= flat_with_entities($TITLE,UBB_UTF8) ?></a><?
			if (!empty($RETRO)) {
				?> <small>(<?= element_name('retro_review') ?>)</small><?
			}
			?></b><br /><?
			echo make_all_html($TEASER,UBB_UTF8 | UBB_NO_LINKS);
			if ($CMTS) {
				?><small class="light"> <?= MIDDLE_DOT_ENTITY ?> <a href="/<?= $ELEMENT ?>/<?= $ID ?>/comments"><?= $CMTS ?></a></small><?
			}
			if ($CONTESTID) {
				?> <a class="win lmrgn" href="<?= get_element_href('contest',$CONTESTID) ?>"><?= __('partylist:win') ?></a><?
			}
		?></div><?
		?><div class="clear"></div><?
		?></td><?
		++$cnt;
	}
	?></tr></table><?
	db_insupd('teasercount','
		INSERT INTO teasercount (ELEMENT, ID)
		VALUES '.implode(',', $vals).'
		ON DUPLICATE KEY UPDATE HITS = HITS + 1'
	);
}

function get_flyers(): array {
	if (no_banners()) {
		return [null, null];
	}
	static $__flyers, $__takeover_flyers	;
	if (!isset($__flyers)) {
		$__flyers = [];
		$__takeover_flyers = [];
		$max_flyers = SMALL_SCREEN ? MAX_FLYERS_MOBILE : MAX_FLYERS_DESKTOP;
		for ($i = 0; $i < $max_flyers ; ++$i) {
			if (!($ad = obtain_ad(ADPOS_HOMERECTANGLE))) {
				break;
			}
			if ($ad['TAKEOVER']) {
				$__takeover_flyers[] = $ad;
			}
			$__flyers[] = $ad;
		}
	}
	return [$__flyers, $__takeover_flyers];
}

function fill_home_banners(string &$body): void {
	require_once '_banner.inc';
	$flyer_body = null;
	$billboard_body = null;
	if (ROBOT) {
		goto fill_placeholders;	// NOSONAR
	}
	$show_flyers = [];
	[$flyers, $takeover_flyers] = get_flyers();
	if (SMALL_SCREEN) {
		if ($takeover_flyers) {
			$show_flyers = $takeover_flyers;
			ob_start();
		} elseif ($flyers) {
			$flyer_count = count($flyers);
			safe_shuffle($flyers);
			$pick_flyers = $flyer_count > 3 ? MAX_FLYERS_MOBILE : 2;
			$show_flyers = array_slice($flyers, 0, $pick_flyers);
			ob_start();
		}
		if ($show_flyers) {
			$max_height = 0;
			if (count($flyers) === 1) {
				display_ad($flyers[0], 'block');
			} else {
				$ad_outputs = [];
				foreach ($flyers as $flyer) {
					ob_start();
					ob_start();
					display_ad($flyer, 'block');
					$ad_output = ob_get_clean();
					show_aspect_embed($ad_output, $flyer['WIDTH'], $flyer['HEIGHT']);
					$ad_outputs[] = ob_get_clean();
					$max_height   = max($flyer['HEIGHT'], $max_height);
				}
				?><div class="center"><?
				foreach ($ad_outputs as $ad_output) {
					?><div<?
					?> class="ib vtop block noverflow"<?
					?> style="width:49%; max-height: <?= $max_height ?>px;"><?= $ad_output ?></div> <?
				}
				?></div><?
			}
			$flyer_body = ob_get_clean();
		}
	} else {
		global $subbar;
		if ($subbar) {
			ob_start();
			?><div id="narrowsubbar"><?
			display_ad($subbar, 'block');
			?></div><?
			$billboard_body = ob_get_clean();
		}
		if ($takeover_flyers) {
			$show_flyers = $takeover_flyers;
		} elseif ($flyers) {
			# maybe not enough running flyer to finish very late front ads
			foreach ($flyers as $ndx => $flyer) {
				if (!isset($flyer['MISSING'])) {
					mail_log("flyer is missing a MISSING field", $flyer);
				} elseif ($flyer['MISSING'] >= FORCE_FRONTS_IF_LATE) {
					$show_flyers[] = $flyer;
					unset($flyers[$ndx]);
				}
			}
			if (count($show_flyers) < MAX_FLYERS_DESKTOP) {
				$pick_left = MAX_FLYERS_DESKTOP - count($show_flyers);
				if ($picked_flyers = array_slice($flyers, 0, $pick_left)) {
					$show_flyers = [...$show_flyers, ...$picked_flyers];
				}
			}
		}
		safe_shuffle($show_flyers);
		ob_start();
		foreach ($show_flyers as $show_flyer) {
			display_ad($show_flyer);
		}
		if (!($flyer_body = ob_get_clean())) {
			goto fill_placeholders;	// NOSONAR
		}
		$flyer_body = preg_replace_callback('!<(?:img|video)(.*?)\s*/?>!', static function (array $match): ?string {
			return preg_replace_callback('!'.
				'width="(?<param_width>\d+)" height="(?<param_height>\d+)|'.
				'width:(?<style_width>\d+)px;\s*height:\s*(?<style_height>\d+)px"'.
			'!',static function(array $match): string {
				if (!empty($match['param_width'])
				&&	!empty($match['param_height'])
				) {
					$width  = (int)$match['param_width'];
					$height = (int)$match['param_height'];
				} elseif (
					!empty($match['style_width'])
				&&	!empty($match['style_height'])
				) {
					$width  = (int)$match['style_width'];
					$height = (int)$match['style_height'];
				} else {
					# something is really wrong
					mail_log('missing width and/or height in frontpage image');
					return $match[0];
				}
				$new_width  = $width;
				$new_height = $height;
				scale($new_width, $new_height, FLYER_WIDTH_FRONTPAGE);
				if (empty($match['style_width'])) {
					return "width=\"$new_width\" height=\"$new_height\"";
				}
				return "width:{$new_width}px;height:{$height}px";
			}, $match[0]);
		},$flyer_body); // NOSONAR
		$flyer_body = '<div class="fw center" id="adistri" style="display: flex; justify-content: space-evenly;">'.$flyer_body.'</div>';
	}

	fill_placeholders:

	$body = str_replace([
			"\x01BOXAD\x02",
			"\x01BILLB\x02",
		],[
			$flyer_body,
			$billboard_body,
		],
		$body
	);
}
