<?php

function preamble() {
	if ($_REQUEST['ACTION'] === 'searchresult'
	&&	have_idnumber($_REQUEST,'INVOICENR')
	) {
		if ($promoid = memcached_single('promo','SELECT PROMOID FROM promo WHERE INVOICENR='.$_REQUEST['INVOICENR'])) {
			see_other(get_element_href('promo',$promoid));
		}
		if ($adid = memcached_single('adinfo','SELECT ADID FROM adinfo WHERE INVOICENR='.$_REQUEST['INVOICENR'])) {
			see_other("/ad/$adid".$adid);
		}
		if ($newsadid = memcached_single('newsad','SELECT NEWSADID FROM newsad WHERE INVOICENR='.$_REQUEST['INVOICENR'])) {
			see_other("/newsad/$newsadid");
		}
	}
}
function perform_commit() {
	if (!have_admin('relation')) {
		return false;
	}
	switch ($_REQUEST['ACTION']) {
	case 'commit':		return relation_commit();
	case 'remove':		require_once '_object.inc'; return set_field('REMOVED',"b'1'",'removed');
	case 'restore':		require_once '_object.inc'; return set_field('REMOVED',"b'0'",'restored');
	case 'validate':	return relation_validate();
	case 'combine':		require_once '_combine.inc'; return combine_element();
	}
}
function display_body() {
	switch ($_REQUEST['ACTION']) {
	default:
	case null:
	case 'searchresult':	relation_display_overview(); return;
	case 'single':
	case 'validate':
	case 'commit':
	case 'remove':
	case 'combine':

	case 'tickets':
	case 'invoices':
	case 'ads':
	case 'banners':
	case 'promos':
	case 'newsads':
	case 'jobs':		relation_display_single(); return;

	case 'combinewith':	require_once '_combine.inc'; show_combine_with(); return;
	case 'form':		relation_display_form(); return;
	}
}
function relation_menu() {
	layout_open_menu();
	layout_menuitem(Eelement_name('overview'),'/relation',!$_REQUEST['ACTION']);
	layout_close_menu();
}
function relation_single_menu($relation) {
	layout_open_menu();
	$relationhref = '/relation/'.$relation['RELATIONID'];
	layout_menuitem(__C('action:change'),$relationhref.'/form');
	require_once '_connect.inc';
	layout_open_menuitem();
	connect_menuitem();
	layout_close_menuitem();
	if ($relation['REQUEST']) {
		layout_menuitem(__C('action:validate'),$relationhref.'/validate');
	}
	layout_continue_menu();
	show_element_menuitems($relation);
	layout_close_menu();
}
function relation_validate() {
	if (!require_admin('relation')
	||	!($relationid = require_idnumber($_REQUEST,'sID'))
	||	!db_insert('relation_log','
		INSERT INTO relation_log
		SELECT * FROM relation
		WHERE REQUEST=1
		  AND RELATIONID='.$relationid)
	||	!db_update('relation','
		UPDATE relation SET
			MSTAMP	='.CURRENTSTAMP.',
			MUSERID	='.CURRENTUSERID.',
			REQUEST	=0
		WHERE REQUEST=1
		  AND RELATIONID='.$relationid)
	) {
		return;
	}
	register_notice('relation:notice:validated_LINE');
}
function relation_display_overview() {
	if (!have_admin('relation')) {
		require_once '_relationlist.inc';
		if (!require_relation()) {
			return;
		}
		layout_show_section_header();
		relation_menu();
		$rlist = new relationlist;
		$rlist->query_by_user(CURRENTUSERID);
		if ($rlist->size()) {
			$rlist->display();
		} else {
			?><p>Geen relaties gevonden.</p><?
		}
		return;
	}

	layout_show_section_header();

	relation_menu();

	layout_open_menu();
	layout_menuitem(__C('action:add'),'/relation/form');
	layout_close_menu();

	?><form<?
	?> accept-charset="utf-8"<?
	?> onsubmit="return submitForm(this)"<?
	?> method="get"<?
	?> action="/relation/searchresult"><?

	layout_open_table('fw');
	layout_start_row();
	echo __C('action:search');
	layout_field_value();
	?><input type="search" name="NAME" id="name"<?
	if (!empty($_REQUEST['NAME']) || empty($_REQUEST['INVOICENR'])) {
		?> autofocus="autofocus"<?
	}
	if (!empty($_REQUEST['NAME'])) {
		?> value="<?= escape_utf8($_REQUEST['NAME']) ?>"<?
	}
	?>> <?
	?><input type="submit" value="<?= __('action:search'); ?>" /><?
	layout_restart_row();
	echo Eelement_name('invoice');
	layout_field_value();
	?><input type="number" data-valid="number" class="id" name="INVOICENR"<?
	if ($invoicenr = have_idnumber($_REQUEST, 'INVOICENR')) {
		?> autofocus="autofocus"<?
		?> value="<?= $invoicenr ?>"<?
	}
	?>><?
	layout_stop_row();
	layout_close_table();
	?></form><?

	if ($_REQUEST['ACTION'] === 'searchresult') {
		if (empty($_REQUEST['NAME'])
		&&	empty($_REQUEST['INVOICENR'])
		) {
			?><p>Niets gevonden</p><?
			return;
		}
		if (!empty($_REQUEST['NAME'])) {
			require_once '_relationlist.inc';
			$rlist = new relationlist;
			$rlist->query_by_name($_REQUEST['NAME']);
			if ($rlist->size()) {
				$rlist->display();
			} else {
				?><p>Geen relaties gevonden.</p><?
			}
		} else {
			?><p>Factuurnummer niet gevonden.</p><?
			return;
		}
	}
}

function relation_display_single(): ?bool {
	if (!require_idnumber($_REQUEST,'sID')) {
		return false;
	}
	layout_show_section_header();

	$relationid = $_REQUEST['sID'];
	if (!have_admin()
	&&	!have_relation($relationid)
	&&	!require_admin()
	) {
		return false;
	}
	$relation_admin = have_admin('relation');
	$viewall = $relation_admin || have_relation($_REQUEST['sID']);
	if (!($relation = db_single_assoc('relation','
		SELECT r1.*,
			(SELECT RELATIONID FROM relation AS r2 WHERE r1.DEBNR=r2.DEBNR AND r2.RELATIONID!=r1.RELATIONID LIMIT 1) AS MULTI
		FROM relation AS r1
		WHERE RELATIONID='.$_REQUEST['sID']
	))) {
		if ($relation === false) {
			return false;
		}
		not_found();
		return null;
	}
	if ($relation_admin) {
		relation_menu();
		relation_single_menu($relation);

		if ($relation['BODY']) {
			layout_open_box('white');
			?><div class="block"><span class="warning"><?= __C('header:admin_comments') ?></span><br /><?=
				make_all_html($relation['BODY'])
			?></div><?
			layout_close_box();
		}
	}
	require_once '_ticketlist.inc';
	show_connected_tickets();

	if ($relation_admin) {
		require_once '_connect.inc';
		_connect_display_form('relation',$relation['RELATIONID']);
	}
	if (have_admin('logbook')) {
		require_once '_logbook.inc';
		show_logbook();
	}
	if ($relation['REMOVED']) {
		?><div class="light"><?
	}
	if ($relation['CONTACTEMAIL']) {
		require_once '_mailerrors.inc';
		show_mail_errors($relation['CONTACTEMAIL'],true);
	}
	if ($relation['INVOICEMAIL'] && $relation['INVOICEMAIL'] != $relation['CONTACTEMAIL']) {
		require_once '_mailerrors.inc';
		show_mail_errors($relation['INVOICEMAIL'],true);
	}

	layout_open_box('relation');
	layout_open_box_header($relation['REQUEST'] ? BOX_HEADER_LIGHT : 0);
	echo escape_utf8($relation['NAME']);
	show_dead($relation);
	layout_close_box_header();

	?><div class="vtop ib" style="min-width:50%"><?

	$list = new deflist('deflist vtop');

	if ($viewall) {
		$list->add_row(Eelement_name('relation_number'), $relation['RELATIONID']);
		if ($invoice_name = $relation['INVOICENAME'] ?: $relation['NAME']) {
			$list->add_row(Eelement_name('invoice_name'), escape_utf8($invoice_name));
		}
		if ($invoice_email = $relation['INVOICEMAIL'] ?: $relation['CONTACTEMAIL']) {
			ob_start();
			echo escape_utf8($relation['INVOICECONTACT'] ?: $invoice_name);
			?> &rarr; <?
			print_email_link($invoice_email,$relation['INVOICECONTACT'],PRINT_EMAIL_IMAGE_AFTER | PRINT_EMAIL_TICKET | PRINT_EMAIL_NO_NAME);
			if ($relation['INVOICEMAILONLY']) {
				?> (<?= __('relation:info:only_by_email') ?>)<?
			}
			$list->add_row(Eelement_name('invoice_contact'),ob_get_clean());
		}
		$list->add_row(Eelement_name('payment_deadline'),__('duration:x_days',['DAYS'=>$relation['PAY_WITHIN']]));

		if ($relation['CONTACT']
		||	 $relation['CONTACTEMAIL']
		) {
			ob_start();
			if ($relation['CONTACTEMAIL']) {
				echo escape_utf8($relation['CONTACT']) ?> &rarr; <?
				print_email_link($relation['CONTACTEMAIL'], $relation['CONTACT'], PRINT_EMAIL_IMAGE_BEFORE | PRINT_EMAIL_TICKET | PRINT_EMAIL_NO_NAME);
			} else {
				echo escape_utf8($relation['CONTACT']);
			}
			$list->add_row(Eelement_name('contact_person'),ob_get_clean());
		}
	}
	if (false === ($users = memcached_rowuse_array(['relationmember', 'user_account'], "
		SELECT USERID, EMAIL, NICK, FLAGS, STATUS
		FROM relationmember
		JOIN user_account USING (USERID)
		JOIN user USING (USERID, NICK)
		WHERE relationmember.RELATIONID = $relationid
		ORDER BY NICK"))
	) {
		return false;
	}
	$userids = [];
	$emails  = [];
	if ($relation['CONTACTEMAIL']) {
		$emails[$relation['CONTACTEMAIL']] = true;
	}
	if ($users) {
		$size = count($users);
		ob_start();
		require_once '_relationmember.inc';
		foreach ($users as $user) {
			/** @noinspection PhpRedundantOptionalArgumentInspection */
			extract($user, \EXTR_OVERWRITE);
			$userids[] = $USERID;
			$costindication = ($FLAGS & RMEMB_COST_INDICATION);
			if ($costindication) {
				?><span class="bold-hilited"><?
			}
			echo get_element_link('user',$USERID,$NICK);
			$parts = array();
			if ($STATUS != 'active') {
				$parts[] = '<span class="warning">'.__('status:'.$STATUS).'</span>';
			}
			if ($costindication) {
				$parts[] = 'aanspreekpunt voor kostenindicaties';
			}
			if ($parts) {
				?> (<?= implode(', ',$parts) ?>)<?
			}
			if ($costindication) {
				?></span><?
			}
			if (--$size) {
				?><br /><?
			}
		}
		$list->add_row(Eelement_name('contact_user', $size),ob_get_clean());

		$useridstr = implode(',', $userids);
		if ($relation_admin) {
			if (false === ($also_emails = memcached_boolean_hash('user', "
				SELECT EMAIL
				FROM user
				WHERE EMAIL != ''
				  AND USERID IN ($useridstr)"))
			) {
				return false;
			}
			if ($also_emails) {
				$emails = [...$emails, ...$also_emails];
			}
		}
	}
	if ($relation['SITE']) {
		require_once '_site.inc';
		show_site_row($list,$relation['SITE']);
	}
	if ($viewall) {
		if (!empty($relation['PHONE'])) {
			$list->add_row(Eelement_name('phone'),'<a href="tel:'.($tel = escape_utf8($relation['PHONE'])).'">'.$tel.'</a>');
		}
		if (!empty($relation['MOBILE'])) {
			$list->add_row(Eelement_name('mobile'), escape_utf8($relation['MOBILE']));
		}
		if (!empty($relation['ADDRESS'])
		||	!empty($relation['POBOX'])
		||	!empty($relation['ZIPCODE'])
		||	!empty($relation['CITYID'])
		) {
			ob_start();
			if (!empty($relation['ADDRESS'])) {
				echo escape_utf8($relation['ADDRESS']); ?><br /><?
			}
			if (!empty($relation['POBOX'])) {
				echo Eelement_name('pobox') ?> <? echo $relation['POBOX']; ?><br /><?
			}
			if (!empty($relation['ZIPCODE'])) {
				echo escape_utf8($relation['ZIPCODE']); ?><br /><?
			}
			if (!empty($relation['CITYID'])) {
				echo get_element_link('city',$relation['CITYID']);
				if ($city = memcached_city_info($relation['CITYID'])) {
					?><br /><?
					echo get_element_link('country',$city['COUNTRYID']);
				}
			}
			$list->add_row(Eelement_name('address'),ob_get_clean());
		}
		if ($relation['LOGO_DISCOUNT']) {
			$list->add_row(Eelement_name('logo_discount'),$relation['LOGO_DISCOUNT'].'%');
		}
		if ($relation['DISCOUNT']) {
			$list->add_row(Eelement_name('discount'),$relation['DISCOUNT'].'%');
			$list->add_row(__C('field:discount_stops'),_date_get($relation['DISCOUNT_STOPSTAMP']));
		}
		if ($relation['DEFAULT_COMMISSION']) {
			$list->add_row(Eelement_name('commission'),$relation['DEFAULT_COMMISSION'].'%');
		}
	}
	if ($relation['KVKNUM']) {
		require_once '_kvk.inc';
		show_kvk_row($list,$relation);
	}
	if ($relation['VATNUM']) {
		$list->add_row(Eelement_name('vatnum'), escape_utf8($relation['VATNUM']));
	}
	$list->display();
	?></div><?

	require_once '_connect.inc';
	$connections = get_connections('relation',$relation['RELATIONID']);
	if ($connections) {
/*		$connections = array_intersect_key($connections,[
			'location'	=> true,
			'organization'	=> true,
			'artist'	=> true,
		]);*/
	}

	if (!empty($userids)
	||	!empty($connections)
	) {
		require_once '_employer.inc';
		$groups = [];

		if (!empty($userids)) {
			$employers = get_employers($userids);
			foreach ($employers as $pelement => $employerlist) {
				foreach ($employerlist as $employer) {
					ob_start();
					echo get_element_link($pelement,$employer['ID']);
					show_dead($employer);

					?> <small class="light">(via <? layout_display_list('user',explode(',',$employer['USERIDS'])) ?>)</small><?

					$groups[$pelement][$employer['ID']] = ob_get_clean();
				}
			}
		}

		unset($connections['contact_ticket']);
		if (!empty($connections)) {
			foreach ($connections as $pelement => $ids) {

				foreach ($ids as $id) {
					if (isset($groups[$pelement][$id])) {
						continue;
					}
					ob_start();
					echo get_element_link($pelement,$id);
					show_dead($pelement,$id);

					?> <small class="light">(via <?= element_name('connection') ?>)</small><?

					$groups[$pelement][$id] = ob_get_clean();
				}
			}
		}

		foreach ($groups as $pelement => $items) {
			$showlist = [
				Eelement_name($pelement,count($items)) => $items
			];
			show_column_list($showlist,5);
		}
	}

	layout_display_alteration_note($relation);
	layout_close_box();

	if (!$viewall) {
		return true;
	}

	require_once '_joblist.inc';
	if (false === ($joblist = memcached_rowuse_array(['job', 'invoice'], '
		SELECT '._joblist_columns().', DISCOUNT
		FROM job
		LEFT JOIN invoice USING (INVOICENR)
		WHERE job.RELATIONID = '.$relation['RELATIONID'].'
		ORDER BY STARTSTAMP DESC'
	))) {
		return false;
	}
	require_once '_promolist.inc';
	if (false === ($promolist = memcached_rowuse_array(['promo', 'invoice'], '
		SELECT '._promolist_columns().', DISCOUNT
		FROM promo
		LEFT JOIN invoice USING (INVOICENR)
		WHERE promo.RELATIONID = '.$relation['RELATIONID'].'
		ORDER BY STARTSTAMP DESC'
	))) {
		return false;
	}
	if (false === ($banners = memcached_rowuse_array('banner','
		SELECT BANNERID,NAME,CSTAMP,REMOVED
		FROM banner
		WHERE RELATIONID = '.$relation['RELATIONID'].'
		ORDER BY BANNERID DESC'
	))) {
		return false;
	}
	// ADS
	if (false === ($ads = memcached_rowuse_array(
		['ad','adinfo','banner'],'
		SELECT	ad.ADID,STARTSTAMP,STOPSTAMP,IMPRESSIONS,IMPRESSIONSDONE,ACTIVE,INVOICENR,ad.PARTYID,ad.REMOVED,DISCOUNT,ad.POSITION,
			banner.BANNERID,banner.NAME AS BANNER_NAME,
			GROUP_CONCAT(party.NAME) AS PARTY_NAMES
		FROM ad
		JOIN adinfo USING (ADID)
		LEFT JOIN banner ON banner.BANNERID=ad.BANNERID
		LEFT JOIN bannermultiparty AS bmp ON bmp.BANNERID=ad.BANNERID
		LEFT JOIN party ON party.PARTYID=bmp.PARTYID
		WHERE adinfo.RELATIONID='.$relation['RELATIONID'].'
		GROUP BY ad.ADID
		ORDER BY STARTSTAMP ASC'//IF(STARTSTAMP<UNIX_TIMESTAMP(),-STARTSTAMP,STARTSTAMP) ASC'
	))) {
		return false;
	}
	// NEWSAD
	if (false === ($newsads = memcached_rowuse_array(
		['newsad','news'],'
		SELECT NEWSADID,newsad.INVOICENR,newsad.NEWSID,TITLE,STARTSTAMP,STOPSTAMP,IMPRESSIONS,IMPRESSIONSDONE,ACTIVE,newsad.REMOVED,DISCOUNT
		FROM newsad
		LEFT JOIN news USING (NEWSID)
		WHERE RELATIONID='.$relation['RELATIONID'].'
		ORDER BY NEWSADID DESC'
	))) {
		return false;
	}
	// PIXELS
	if (false === ($pixels = db_rowuse_hash('pixel','
		SELECT	*,
			(SELECT COUNT(*) FROM pixelparty WHERE ACTIVE=1 AND pixelparty.PIXELID=pixel.PIXELID) AS EVENTCNT
		FROM pixel
		WHERE RELATIONID='.$relation['RELATIONID'].'
		ORDER BY STOPSTAMP ASC'
	))) {
		return false;
	}

	// INVOICES
	if ($relation['MULTI']) {
		$invoicenrs = [0];
		foreach ([$promolist,$ads,$newsads,$joblist] as $list) {
			foreach ($list as $info) {
				if ($info['INVOICENR']) {
					$invoicenrs[] = $info['INVOICENR'];
				}
			}
		}
		$and_limit_multi = ' AND INVOICENR IN ('.implode(',',$invoicenrs).')';
	}  else $and_limit_multi = null;

	if (false === ($invoices = memcached_rowuse_array(['invoice','invoice_pdf_processed'],'
		SELECT	INVOICENR,AMOUNT_NEW,SENTSTAMP,STATUS,MIN(PAY_WITHIN) AS PAY_WITHIN,
			invoice_pdf_processed.STAMP AS PDF,
			(SELECT 1 FROM ideal_transaction WHERE ELEMENT="invoice" AND ELEMENTID=INVOICENR AND STATUS="paid" LIMIT 1) AS IDEALED
		FROM invoice
		LEFT JOIN relation ON relation.DEBNR=invoice.DEBNR or relation.RELATIONID=invoice.RELATIONID
		LEFT JOIN invoice_pdf_processed USING (INVOICENR)
		WHERE (invoice.DEBNR='.$relation['DEBNR'].' OR invoice.RELATIONID='.$relation['RELATIONID'].')'.
		 $and_limit_multi.'
		GROUP BY INVOICENR
		ORDER BY SENTSTAMP DESC'
	))) {
		return false;
	}
/*	NOTE: Can be removed in the future. Invoices are done by 2XPR nowadays.
	$nonr_cnt = 0;
	$nonr_fut_cnt = 0;
	$nonr = [];
	$nonr_fut = [];
	foreach ([
		'promo'		=> $promolist,
		'newsad'	=> $newsads,
		'ad'		=> $ads,
		'job'		=> $joblist,
		'pixel'		=> $pixels,
	] as $element => $list) {
		foreach ($list as $item) {
			if ($item['INVOICENR'] === null
#			&&	$item['DISCOUNT'] != 100
			&&	empty($item['REMOVED'])
			) {
				if (CURRENTSTAMP > $item['STOPSTAMP']) {
					$nonr[$element][] = $item;
					++$nonr_cnt;
				} else {
					$nonr_fut[$element][] = $item;
					++$nonr_fut_cnt;
				}
			}
		}
	}
	if ($nonr_cnt) {
		$cnts[] = $nonr_cnt;
	}
	if ($nonr_fut_cnt) {
		$cnts[] = $nonr_fut_cnt;
	}
	if (isset($cnts)) {
		layout_open_box('relation');
		expandable_box_header(Eelement_name('element_without_invoice',$nonr_cnt + $nonr_fut_cnt).' '.MIDDLE_DOT_ENTITY.' '.(implode(' '.MIDDLE_DOT_ENTITY.' ',$cnts)),'nonr');
		?><div id="nonr"><?
		$multi = count($cnts) > 1;
		foreach (['date:past' => $nonr,'date:future' => $nonr_fut] as $desc => $lists) {
			if ($multi) {
				layout_box_header(__($desc),null,'light');
			}
			foreach ($lists as $element => $items) {
				?><fieldset><?
				?><caption><?= element_name($element,count($items)) ?></caption><?
				switch ($element) {
				case 'promo':
					require_once '_promolist.inc';
					layout_open_table('fw default vtop');
					_promolist_display_rows($items,false,false);
					layout_close_table();
					break;
				case 'newsad':
					require_once '_newsadlist.inc';
					layout_open_table('fw default vtop');
					newsadlist_show($items,false);
					layout_close_table();
					break;
				case 'job':
					require_once '_joblist.inc';
					layout_open_table('fw default vtop');
					_joblist_display_rowlist($items,false,false);
					layout_close_table();
					break;
				case 'ad':
					require_once '_adlist.inc';
					layout_open_table('fw default vtop');
					_adlist_display_rowlist($items,false);
					layout_close_table();
					break;
				case 'pixel':
					require_once '_pixel.inc';
					layout_open_table('fw default vtop');
					show_pixel_table($pixels);
					layout_close_table();
				}
				?></fieldset><?
			}
		}
		?></div><?
		layout_close_box();
	}*/

	// TICKETS
	$tickets = [
		'office'		=> [],
		'b2b'			=> [],
		'development'	=> [],
		'bug'			=> []
	];

	$count_only = $_REQUEST['ACTION'] != 'tickets';
	$memcached_query = $count_only ? 'memcached_boolean_hash' : 'memcached_rowuse_hash';

	$t_select = "	t.TICKETID,
					t.CSTAMP,
					t.LSTAMP,
					t.ID,
					t.MSGCNT,
					t.SUBJECT,
					(	SELECT COUNT(*)
						FROM contact_ticket_attachment AS cta
						JOIN contact_ticket_message AS catt USING (CTMSGID)
						WHERE CONTENTID = ''
						  AND FILENAME != 'message.html'
						  AND catt.TICKETID = t.TICKETID
					) AS ATTCNT";

	if (false === ($tickets['promo'] = $memcached_query(['promo', 'contact_ticket'], '
		SELECT '.($count_only ? 't.TICKETID' : $t_select.',REMOVED,promo.TICKETID AS cTICKETID'). "
		FROM contact_ticket AS t
		JOIN promo ON PROMOID = ID
		WHERE ELEMENT = 'promo'
		  AND RELATIONID = $relationid"))
	||	false === ($tickets['ad'] = $memcached_query(['ad', 'adinfo', 'banner', 'contact_ticket'], '
		SELECT '.($count_only ? 't.TICKETID' : $t_select.',SUBJECT,adinfo.TICKETID AS cTICKETID').'
		FROM contact_ticket AS t
		JOIN adinfo ON adinfo.ADID=ID
		JOIN ad USING (ADID)
		JOIN banner USING (BANNERID)
		WHERE ELEMENT="ad"
		  AND adinfo.RELATIONID='.$relationid))
	||	false === ($tickets['banner'] = $memcached_query(['banner', 'contact_ticket'], '
		SELECT '.($count_only ? 't.TICKETID' : $t_select).'
		FROM contact_ticket AS t
		JOIN banner ON BANNERID=ID
		WHERE ELEMENT="banner"
		  AND RELATIONID='.$relationid))
	||	false === ($tickets['newsad'] = $memcached_query(['contact_ticket', 'newsad', 'news'], '
		SELECT '.($count_only ? 't.TICKETID' : $t_select.',TITLE AS NAME,news.TICKETID AS cTICKETID').'
		FROM contact_ticket AS t
		JOIN newsad ON NEWSADID=ID
		JOIN news USING (NEWSID)
		WHERE ELEMENT="newsad"
		  AND RELATIONID='.$relationid))
	||	false === ($tickets['job'] = $memcached_query(['contact_ticket', 'job'], '
		SELECT '.($count_only ? 't.TICKETID' : $t_select.',REMOVED').'
		FROM contact_ticket AS t
		JOIN job ON JOBID=ID
		WHERE ELEMENT="job"
		  AND RELATIONID='.$relationid))
	||	false === ($tickets['invoice'] = $memcached_query(['contact_ticket', 'invoice'], '
		SELECT '.($count_only ? 't.TICKETID' : $t_select).'
		FROM contact_ticket AS t
		JOIN invoice ON INVOICENR=ID
		WHERE ELEMENT="invoice"
		  AND DEBNR='.$relation['DEBNR']))
	) {
		return false;
	}

	# all tickets above are connected to relation, get ids of those tickets for later
	# tickets below are not necessarily connected to something concerning the current relation

	$ticket_count = 0;
	$have_ticketids = [];
	foreach ($tickets as $element => $list) {
		if ($list) {
			$have_ticketids = [...$have_ticketids, ...array_keys($list)];
		}
	}
	if ($have_ticketids) {
		sort($have_ticketids);
	}

	$key = null;
	if (false === ($more = memcached_read(
		ROW_USE_MULTI_HASH,
		['contact_ticket','contact_ticket_mail'],'
		SELECT '.($count_only ? 'DISTINCT ELEMENT AS _ELEMENT,TICKETID AS _TICKETID' : 'DISTINCT ELEMENT AS _ELEMENT,TICKETID AS _TICKETID,'.$t_select).'
		FROM contact_ticket AS t
		JOIN connect ON MAINTYPE="relation" AND ASSOCTYPE="contact_ticket" AND ASSOCID=TICKETID
		WHERE MAINID='.$relationid.'
		  AND ELEMENT IN ('.stringsimplodekeys(', ', $tickets).',"office","b2b","development","bug")'.(
			$have_ticketids
		?	' AND TICKETID NOT IN ('.implode(', ', $have_ticketids).')'
		:	null
		),
		TEN_MINUTES,
		$key,
		DB_STORE_INDEX))
	) {
		return false;
	}
	$ticket_count = 0;
	foreach ($more as $element => $infos) {
		$have_ticketids = [...$have_ticketids, ...array_keys($infos)];
		if (isset($tickets[$element])) {
			$tickets[$element] += $infos;
		} else {
			$tickets[$element] = $infos;
		}
		if (!$count_only) {
			number_asort($tickets[$element],'LSTAMP');
		}
		$ticket_count += count($infos);
	}

	if (!$relation_admin) {
		# only include extra tickets for current user (privacy concerns)
		global $currentuser;
		$userids = [CURRENTUSERID];
		$emails = ($current_email = $currentuser->get_email()) ? [$current_email] : [];
	}
	if ($userids) {
		$unionparts[] = '
			SELECT DISTINCT TICKETID
			FROM contact_ticket
			WHERE WITHUSERID IN ('.implode(', ', $userids).')';
	}
	if ($emails) {
		$unionparts[] = '
			SELECT DISTINCT TICKETID
			FROM contact_ticket_mail
			WHERE FROM_EMAIL IN ('.stringsimplodekeys(', ', $emails).')';
	}
	if (isset($unionparts)) {
		$key = null;
		if (false === ($more = memcached_read(
			ROW_USE_MULTI_HASH,
			['contact_ticket','contact_ticket_mail'], /** @lang MariaDB */ '
			SELECT '.($count_only ? 'DISTINCT ELEMENT AS _ELEMENT,TICKETID AS _TICKETID' : 'DISTINCT ELEMENT AS _ELEMENT,TICKETID AS _TICKETID,'.$t_select).'
			FROM contact_ticket AS t
			JOIN ('.implode(' UNION ',$unionparts).') AS selection USING (TICKETID)
			WHERE ELEMENT IN ('.stringsimplodekeys(',',$tickets).',"office","b2b","development","bug")'.
			($have_ticketids ? ' AND TICKETID NOT IN ('.implode(',',$have_ticketids).')' : '').'
			UNION
			SELECT '.($count_only ? 'DISTINCT ELEMENT AS _ELEMENT,TICKETID AS _TICKETID' : 'DISTINCT ELEMENT AS _ELEMENT,TICKETID AS _TICKETID,'.$t_select).'
			FROM contact_ticket AS t
			JOIN connect
				ON  MAINTYPE="contact_ticket"
				AND MAINID=TICKETID
				AND ASSOCTYPE IN ('.stringsimplodekeys(',',$tickets).',"office","b2b","development","bug")
			JOIN ('.implode(' UNION ',$unionparts).') AS selection USING (TICKETID)'.
			($have_ticketids ? ' WHERE TICKETID NOT IN ('.implode(',',$have_ticketids).')' : ''),
			TEN_MINUTES,
			$key,
			DB_STORE_INDEX))
		) {
			return false;
		}
		$ticket_count = 0;
		foreach ($more as $element => $infos) {
			$have_ticketids = [...$have_ticketids, ...array_keys($infos)];
			add_or_set($tickets, $element, $infos);
			if (isset($tickets[$element])) {
				$tickets[$element] += $infos;
			} else {
				$tickets[$element] = $infos;
			}
			if (!$count_only) {
				number_asort($tickets[$element],'LSTAMP');
			}
			$ticket_count += count($infos);
		}
		unset($list);
		sort($have_ticketids);
	}
	if (!$relation_admin
	&&	$have_ticketids
	) {
		$okusers[CURRENTUSERID] = true;
		$okmails[memcached_single('user', 'SELECT EMAIL FROM user WHERE USERID = '.CURRENTUSERID)] = true;
		if ($relation['CONTACTEMAIL']) {
			$okmails[$relation['CONTACTEMAIL']] = true;
		}
		foreach ($users as $user) {
			$okmails[$user['EMAIL']] = true;
			$okusers[$user['USERID']] = true;
		}
		if (false === ($checks = memcached_rowuse_array(['contact_ticket','contact_ticket_mail'], /** @lang MariaDB */ "
			SELECT ELEMENT, TICKETID, WITHUSERID, (
				SELECT GROUP_CONCAT(DISTINCT IF(DIRECTION = 'toadmin', FROM_EMAIL, IF(DIRECTION = 'touser', TO_EMAIL, NULL)) SEPARATOR '".chr(0x1E)."')
				FROM contact_ticket_mail AS ctm
				LEFT JOIN contact_ticket_message USING (CTMSGID, TICKETID)
				WHERE DIRECTION IN ('toadmin', 'touser')

				) AS EMAILS
			FROM contact_ticket
			WHERE TICKETID IN (".implode(', ',$have_ticketids).')'))
		) {
			return false;
		}
		foreach ($checks as $check) {
			/** @noinspection PhpRedundantOptionalArgumentInspection */
			extract($check, \EXTR_OVERWRITE);
			if ($WITHUSERID && isset($okusers[$WITHUSERID])) {
				continue;
			}
			if ($EMAILS) {
				$EMAILS = explode(chr(0x1E),$EMAILS);
				foreach ($EMAILS as $email) {
					if (isset($okmails[$email])) {
						continue 2;
					}
				}
			}
			unset($tickets[$ELEMENT][$TICKETID]);
			--$ticket_count;
		}
	}

	if (!$joblist
	&&	!$promolist
	&&	!$banners
	&&	!$ads
	&&	!$newsads
	&&	!$invoices
	&&	!$ticket_count
	) {
		return true;
	}
/*	layout_open_menu();
	if ($invoices) {
		layout_menuitem(Eelement_plural_name('invoice'),'#invoices');
	}
	if ($promolist) {
		layout_menuitem(Eelement_plural_name('promo'),'#promos');
	}
	if ($banners) {
		layout_menuitem(Eelement_plural_name('banner'),'#banners');
	}
	if (!empty($ads)) {
		layout_menuitem(Eelement_plural_name('ad'),'#ads');
	}
	if ($newsads) {
		layout_menuitem(Eelement_plural_name('newsad'),'#newsads');
	}
	if ($joblist) {
		layout_menuitem(Eelement_plural_name('job'),'#jobs');
	}
	if ($ticketcnt) {
		layout_menuitem(Eelement_plural_name('contact_ticket'),'#tickets');
	}
	layout_close_menu();*/

	if ($invoices) {
		require_once '_invoicelist.inc';
		$alrights = [];
		$alarms   = [];
/*		foreach ($invoices as $invoice) {
			if (isset($invoice['ALARM'])
			||	$invoice['STATUS'] === 'notpaid'
			||	$invoice['STATUS'] === 'irrevocable'
			) {
				$alarms[] = $invoice;
			}
		}*/
		foreach ([
			'alarm' => $alarms,
			null	=> $invoices
		] as $alarm => $tmpinvoices) {
			if (!$tmpinvoices) {
				continue;
			}
			layout_open_box('relation', 'invoices'.$alarm);
			$cnt = count($tmpinvoices);
			if ($alarm) {
				layout_box_header(Eelement_name('unpaid_invoice',$cnt),$cnt.' '.MIDDLE_DOT_ENTITY.' '.$cnt,'invoicelist'.$alarm);
			} else {
				expandable_box_header(Eelement_name('invoice',$cnt).' '.MIDDLE_DOT_ENTITY.' '.$cnt,'invoicelist');
				?><div class="hidden" id="invoicelist"><?
			}
			layout_open_table('default fw');
			invoicelist_show($tmpinvoices);
			layout_close_table();
			if (!$alarm) {
				?></div><?
			}
			layout_close_box();
		}
	}
	if ($promolist) {
		require_once '_promolist.inc';
		layout_open_box('relation','promos');
		$cnt = count($promolist);
		expandable_box_header(Eelement_name('promo',$cnt).' '.MIDDLE_DOT_ENTITY.' '.$cnt,'promolist');
		?><div class="hidden" id="promolist"><?
		layout_open_table(TABLE_FULL_WIDTH);
		_promolist_display_rows($promolist,false,false);
		layout_close_table();
		?></div><?
		layout_close_box();
	}
	if (!empty($ads)) {
		require_once '_adlist.inc';
		layout_open_box('relation','ads');
		$cnt = count($ads);
		expandable_box_header(Eelement_name('ad',$cnt).' '.MIDDLE_DOT_ENTITY.' '.$cnt,'adlist');
		?><div class="hidden" id="adlist"><?
		layout_open_table('fw default vtop');
		_adlist_display_rowlist($ads);
		layout_close_table();
		?></div><?
		layout_close_box();
	}
	if ($banners) {
		layout_open_box('relation','banners');
		$cnt = count($banners);
		expandable_box_header(Eelement_plural_name('banner').' '.MIDDLE_DOT_ENTITY.' '.$cnt,'bannerlist');
		?><div class="hidden" id="bannerlist"><?
		layout_open_table();
		foreach ($banners as $banner) {
			layout_start_rrow_right(0,$banner['REMOVED'] ? 'unavailable' : null);
			_date_display($banner['CSTAMP']);
			layout_next_cell();
			?>&nbsp;&middot;&nbsp;<a href="/banner/<?= $banner['BANNERID'];
			?>"><?= escape_utf8($banner['NAME']); ?></a><?
			layout_stop_row();
		}
		layout_close_table();
		?></div><?
		layout_close_box();
	}
	if ($newsads) {
		require_once '_newsadlist.inc';
		layout_open_box('relation','newsads');
		$cnt = count($newsads);
		expandable_box_header(Eelement_name('newsad',$cnt).' '.MIDDLE_DOT_ENTITY.' '.$cnt,'newsadlist');
		?><div class="hidden" id="newsadlist"><?
		layout_open_table('fw default vtop');
		newsadlist_show($newsads);
		layout_close_table();
		?></div><?
		layout_close_box();
	}
	if ($joblist) {
		layout_open_box('relation','job');
		$cnt = count($joblist);
		expandable_box_header(Eelement_name('job',$cnt).' '.MIDDLE_DOT_ENTITY.' '.$cnt,'joblist');
		?><div class="hidden" id="joblist"><?
		layout_open_table('fw default vtop');
		_joblist_display_rowlist($joblist,true,false);
		layout_close_table();
		?></div><?
		layout_close_box();
	}
	if (!empty($pixels)) {
		require_once '_pixel.inc';
		layout_open_box('relation','pixels');
		$cnt = count($pixels);
		expandable_box_header(Eelement_name('pixel',$cnt).' '.MIDDLE_DOT_ENTITY.' '.$cnt,'pixellist');
		?><div class="hidden" id="pixellist"><?
		layout_open_table('fw default vtop');
		show_pixel_table($pixels);
		layout_close_table();
		?></div><?
		layout_close_box();
	}
	if (!$count_only) {
		$mailsubjects = $have_ticketids ? memcached_simple_hash('contact_ticket_mail','
				SELECT TICKETID,SUBJECT
				FROM contact_ticket_mail
				JOIN (
					SELECT TICKETID,MAX(CTMSGID) AS CTMSGID
					FROM contact_ticket_mail
					WHERE TICKETID IN ('.implode(',',$have_ticketids).')
					GROUP BY TICKETID
				) AS maxes USING (TICKETID,CTMSGID)') : null;

		require_once '_contactsubject.inc';
		ob_start();
		$tcnt = 0;
		foreach ($tickets as $element => $list) {
			if (!$list) {
				continue;
			}
			layout_start_header_row();
			layout_header_cell();
			layout_header_cell();
			layout_header_cell('<b>'.Eelement_plural_name($element).'</b>');
			layout_stop_header_row();
			number_reverse_sort($list,'LSTAMP');
			foreach ($list as $ticket) {
				layout_start_rrow_right(empty($ticket['REMOVED']) ? 0 : ROW_LIGHT);
				_date_display($ticket['LSTAMP'],'&nbsp;');
				layout_next_cell(class: 'center');
				?><small class="light"><?
				echo $ticket['MSGCNT'];
				?></small><?
				layout_next_cell();
				?><a href="/ticket/<?= $ticket['TICKETID'] ?>"><?
				$title = null;
				$res = null;
				$subject = getifset($ticket,'SUBJECT') ?: getifset($mailsubjects,$ticket['TICKETID']);
				if ($subject) {
					$subject = strip_ticket_subject(isset($title) ? str_ireplace($title,'',$subject) : $subject);
				}
				$element_subject =
					$ticket['ID']
					?	(	$element === 'invoice'
						?	$ticket['ID']
						:	($title = escape_specials($res = get_element_title($element, $ticket['ID']), utf8: isset(USE_UNICODE[$element])))
						)
					:	null;
				if (!$element_subject && !$subject) {
					echo Eelement_name('contact_ticket').' '.$ticket['TICKETID'];
				} elseif (!$element_subject) {
					echo escape_specials($subject);
				} else {
					echo escape_specials($element_subject);
					?> <span class="small light"><?= MIDDLE_DOT_ENTITY ?> <?= escape_specials($subject)  ?></span><?
				}
				?></a><?
				if (!empty($ticket['ATTCNT'])) {
					?><span class="light"> <?= PAPERCLIP_ENTITY ?> <?
					?><small><?= $ticket['ATTCNT'] ?></small></span><?
				}

				if ($res
				&&	is_array($res)
				) {
					error_log_r($res,'AAARGH @ '.$_SERVER['REQUEST_URI']);
				}
				if (!empty($ticket['REMOVED'])) {
					?> (<?= __('status:removed') ?>)<?
				}
				layout_next_cell();
				layout_stop_row();
				++$tcnt;
			}
		}
		if ($content = ob_get_clean()) {
			layout_open_box('relation','tickets');
			layout_box_header(Eelement_name('contact_ticket',$tcnt).' '.MIDDLE_DOT_ENTITY.' '.$tcnt);
			layout_open_table(TABLE_FULL_WIDTH | TABLE_VTOP);
			echo $content;
			layout_close_table();
			layout_close_box();
		}
	} elseif ($ticket_count) {
		layout_open_box('relation','tickets');
		expandable_box_header(Eelement_name('contact_ticket',$ticket_count).' '.MIDDLE_DOT_ENTITY.' '.$ticket_count,false,'/relation/'.$relationid.'/tickets#tickets');
		layout_close_box();
	}
	if ($relation['REMOVED']) {
		?></div><?
	}
	return null;
}

function relation_display_form() {
	if (!require_admin('relation')
	||	!optional_number($_REQUEST,'sID')
	) {
		return;
	}
	require_once '_citylist.inc';
	layout_show_section_header(Eelement_name('relation_form'));

	relation_menu();

	if ($relationid = $_REQUEST['sID']) {
		$relation = db_single_assoc('relation','SELECT * FROM relation WHERE RELATIONID='.$relationid);
		if (!$relation) {
			_error(__('relation:error:nonexistent_LINE',array('ID'=>$relationid)));
			return;
		}
		$users = db_rowuse_array('relationmember','
			SELECT USERID, FLAGS
			FROM relationmember
			WHERE RELATIONID = '.$relationid
		);
		if ($users === false) {
			return;
		}
		if (!$users) {
			$users = [0, 0];
		} else {
			$users[] = 0;
		}
	} else {
		$relation = null;
		$users = [0, 0];
	}
	include_js('js/hiliting');
	?><form<?
		?> accept-charset="utf-8"<?
		?> onsubmit="return submitForm(this);"<?
		?> method="post"<?
		?> action="/relation<?
		if (isset($relation)) {
			?>/<? echo $relation['RELATIONID'];
		}
		?>/commit"><?

	$spec = explain_table('relation');

	layout_open_box('relation');
	layout_open_table('fw');
	layout_start_row();
		echo Eelement_name('name');
		layout_field_value();
		show_input([
			'spec'		=> $spec,
			'type'		=> 'text',
			'name'		=> 'NAME',
			'required'	=> true,
			'value'		=> $relation
		]);
	layout_restart_row();
		layout_next_cell();
		?><label class="<?
		if (empty($relation['DEAD'])) {
			?>not-<?
		}
		?>hilited-red"><?
		show_input([
			'class'		=> 'upLite',
			'type'		=> 'checkbox',
			'name'		=> 'DEAD',
			'checked'	=> !empty($relation['DEAD']) ? true : false
		]);
		?> <label><?=  __('status:ended') ?></label><?
	layout_restart_row();
		echo Eelement_name('debtor_number');
		layout_field_value();
		show_input(array(
			'spec'		=> $spec,
			'type'		=> 'number',
			'name'		=> 'DEBNR',
			'class'		=> 'id',
			'value'		=> $relation
		));
	layout_restart_row();
	echo Eelement_name('payment_deadline');
	layout_field_value();
	show_input([
		'type'	=> 'number',
		'name'	=> 'PAY_WITHIN',
		'class'	=> 'right three_digits',
		'min'	=> 8,
		'max'	=> 300,
		'value'	=> $relation ? $relation['PAY_WITHIN'] : 14
	]);
	?> <?
	echo element_plural_name('day');
	layout_restart_row();
		echo Eelement_name('invoice_name');
		layout_field_value();
		show_input(array(
			'spec'		=> $spec,
			'type'		=> 'text',
			'name'		=> 'INVOICENAME',
			'value'		=> $relation
		));
	layout_restart_row();
		echo Eelement_name('invoice_contact');
		layout_field_value();
		show_input(array(
			'spec'		=> $spec,
			'type'		=> 'text',
			'name'		=> 'INVOICECONTACT',
			'value'		=> $relation
		));
	layout_restart_row();
		echo Eelement_name('invoice_email');
		layout_field_value();
		show_input([
			'spec'		=> $spec,
			'type'		=> 'email',
			'data-valid'	=> 'working-email',
			'name'		=> 'INVOICEMAIL',
			'value'		=> $relation
		]);
	layout_restart_row();
		layout_next_cell();
		if ($relation) {
			?><input type="hidden" name="INVOICEMAILONLY_OLD" value="<?= $relation['INVOICEMAILONLY'] ?>" /><?
		}
		$checked = !$relation || !empty($relation['INVOICEMAILONLY']);
		?><label class="<? if (!$checked) echo 'not-' ?>hilited"><?
		show_input([
			'class'		=> 'upLite',
			'type'		=> 'checkbox',
			'name'		=> 'INVOICEMAILONLY',
			'spec'		=> $spec,
			'checked'	=> $checked
		]);
		?> <?= __('relation:info:only_by_email')
		?></label><?
	layout_restart_row();
		echo Eelement_name('site');
		layout_field_value();
		show_input(array(
			'spec'		=> $spec,
			'type'		=> 'url',
			'name'		=> 'SITE',
			'value'		=> $relation
		));
	layout_restart_row();
		echo Eelement_name('contact_person');
		layout_field_value();
		show_input(array(
			'spec'		=> $spec,
			'type'		=> 'text',
			'name'		=> 'CONTACT',
			'value'		=> $relation
		));
	layout_restart_row();
		echo Eelement_name('contact_email');
		layout_field_value();
		show_input(array(
			'spec'		=> $spec,
			'type'		=> 'email',
			'name'		=> 'CONTACTEMAIL',
			'value'		=> $relation
		));
	$usercnt = 0;
	require_once '_relationmember.inc';
	require_once '_fillelementid.inc';
	foreach ($users as $user) {
		if (is_scalar($user)) {
			$userid = $user;
			$costindication = false;
		} else {
			$userid = $user['USERID'];
			$costindication = ($user['FLAGS'] & RMEMB_COST_INDICATION);
		}
		layout_restart_row(0,null,null,$costindication ? 'light-hilited' : 'not-light-hilited');
		?>Contact USERID <? echo $usercnt + 1;
		layout_field_value();
		show_elementid_input('user', $userid, ['name' => 'CONTACTUSERID[]', 'data-bad-ok' => true]);
		?><label class="r"><?
		?><input class="upLite"<?
		if ($costindication) {
			?> checked="checked"<?
		}
		?> type="radio" name="COSTINDICATION" value="<?= $usercnt; ?>"> verantwoordelijke kostenindicaties<?
		?></label><?
		++$usercnt;
	}
	layout_restart_row();
		echo Eelement_name('phone');
		layout_field_value();
		?><input type="tel" name="PHONE" value="<?= escape_utf8($relation['PHONE'] ?? ''); ?>" /><?

	layout_restart_row();
		echo Eelement_name('mobile');
		layout_field_value();
		?><input type="tel" name="MOBILE" value="<?= escape_utf8($relation['MOBILE'] ?? ''); ?>" /><?

	layout_restart_row();
		echo Eelement_name('address');
		layout_field_value();
		?><input type="text" name="ADDRESS" value="<?= escape_utf8($relation['ADDRESS'] ?? ''); ?>" /><?

	layout_restart_row();
		echo Eelement_name('pobox');
		layout_field_value();
		?><input type="number" data-valid="number" class="id" name="POBOX" value="<?= $relation['POBOX'] ?? ''; ?>" /><?

	layout_restart_row();
		echo Eelement_name('zipcode');
		layout_field_value();
		?><input type="text" class="id" name="ZIPCODE" value="<?= escape_utf8($relation['ZIPCODE'] ?? ''); ?>" /><?

	layout_restart_row();
		echo Eelement_name('city');
		layout_field_value();
		display_dynamic_city_pair(
			cityid: $relation['CITYID'] ?? 0,
		);

	require_once '_kvk.inc';
	show_kvk_form_row($relation);

	layout_restart_row();
		echo Eelement_name('vatnum');
		layout_field_value();
		?><input type="text" class="id" name="VATNUM" value="<?= escape_utf8($relation['VATNUM'] ?? ''); ?>" maxlength="32" /><?

	layout_restart_row();
		echo Eelement_name('logo_discount');
		layout_field_value();
		show_input([
			'spec'		=> $spec,
			'type'		=> 'number',
			'name'		=> 'LOGO_DISCOUNT',
			'min'		=> 0,
			'max'		=> 100,
			'class'		=> 'right rpad three_digits',
			'value'		=> $relation
		]);
		?> %<?

	layout_restart_row();
		echo Eelement_name('discount');
		layout_field_value();
		show_input(array(
			'spec'		=> $spec,
			'type'		=> 'number',
			'name'		=> 'DISCOUNT',
			'min'		=> 0,
			'max'		=> 100,
			'class'		=> 'right rpad three_digits',
			'value'		=> $relation,
			'onkeyup'	=> "setdisplay('discountstoprow',this.value!='0')"
		));
		?> %<?
	layout_restart_row(!empty($relation['DISCOUNT']) ? 0 : ROW_HIDDEN,'discountstoprow');
		echo __C('field:discount_stops');
		layout_field_value();
		_date_display_select_stamp($relation['DISCOUNT_STOPSTAMP'] ?? CURRENTSTAMP,'DISCOUNT_STOP');

	layout_restart_row();
		echo Eelement_name('commission');
		layout_field_value();
		show_input(array(
			'spec'		=> $spec,
			'type'		=> 'number',
			'name'		=> 'DEFAULT_COMMISSION',
			'min'		=> 0,
			'max'		=> 100,
			'class'		=> 'right rpad three_digits',
			'value'		=> $relation
		));
		?> %<?
	layout_restart_row();
		echo __C('header:admin_comments');
		layout_field_value();
		?><textarea cols="50" rows="5" name="BODY"><?= escape_utf8($relation['BODY'] ?? ''); ?></textarea><?
	layout_stop_row();
	layout_close_table();
	layout_close_box();
	?><div class="block"><input type="submit" value="<?= __($relation ? 'action:change' : 'action:add') ?>" /></div><?
	?></form><?
}
function relation_commit(): bool {
	require_once '_phone.inc';

	if (!require_admin('relation')
	||	!require_anything_trim($_POST, 'INVOICENAME', utf8: true)
	||	!require_anything_trim($_POST, 'INVOICECONTACT', utf8: true)
	||	!require_anything_trim($_POST, 'NAME', utf8: true)
	||	!require_anything_trim($_POST, 'PHONE', utf8: true)
	||	!require_anything_trim($_POST, 'MOBILE', utf8: true)
	||	!require_anything_trim($_POST, 'ADDRESS', utf8: true)
	||	!require_anything_trim($_POST, 'ZIPCODE', utf8: true)
	||	!require_anything_trim($_POST, 'CONTACT', utf8: true)
	||	!optional_working_email($_POST, 'CONTACTEMAIL', utf8: true)
	||	!optional_working_email($_POST,'INVOICEMAIL', utf8: true)
	||	!require_anything_trim($_POST, 'BODY', utf8: true)
	||	!require_number_array($_POST, 'CONTACTUSERID', allow_empty: true)
	||	!require_anything_trim($_POST, 'KVKNUM', utf8: true)
	||	!require_anything_trim($_POST, 'VATNUM', utf8: true)
	||	!require_anything_trim($_POST, 'SITE', utf8: true)
	||	false === require_number($_POST, 'CITYID')
	||	false === require_number($_POST, 'DISCOUNT')
	||	false === require_number($_POST, 'LOGO_DISCOUNT')
	||	!require_idnumber($_POST, 'PAY_WITHIN')
	||	!require_date($_POST, 'DISCOUNT_STOP')
	||	false === require_number($_POST, 'DEFAULT_COMMISSION')
	||	false === require_number($_POST, 'POBOX')
	||	false === require_number($_POST, 'DEBNR')
	||	!optional_number($_REQUEST, 'sID')
	||	!optional_number($_POST, 'COSTINDICATION')
	) {
		return false;
	}
	require_once '_relationmember.inc';
	require_once '_site.inc';
	require_once '_namefix.inc';
	$setlist[] = 'DEBNR='.$_POST['DEBNR'];
	$setlist[] = 'PAY_WITHIN='.$_POST['PAY_WITHIN'];
	$setlist[] = 'INVOICENAME="'.addslashes($_POST['INVOICENAME']).'"';
	$setlist[] = 'NAME="'.addslashes(get_fixed_utf8_name($_POST['NAME'])).'"';
	$setlist[] = $_POST['SITE'] ? 'SITE="'.addslashes(make_proper_site_utf8($_POST['SITE'])).'"' : 'SITE=""';
	$setlist[] = 'PHONE="'.addslashes(clean_phone($_POST['PHONE'])).'"';
	$setlist[] = 'MOBILE="'.addslashes($_POST['MOBILE']).'"';
	$setlist[] = 'ADDRESS="'.addslashes($_POST['ADDRESS']).'"';
	$setlist[] = 'DEAD=b\''.(isset($_POST['DEAD']) ? 1 : 0).'\'';
	$setlist[] = 'ZIPCODE="'.addslashes(clean_zip($_POST['ZIPCODE'])).'"';
	$setlist[] = 'CONTACT="'.addslashes($_POST['CONTACT']).'"';
	$setlist[] = 'CONTACTEMAIL="'.addslashes($_POST['CONTACTEMAIL']).'"';
	$setlist[] = 'INVOICEMAIL="'.addslashes($_POST['INVOICEMAIL']).'"';
	$setlist[] = 'INVOICECONTACT="'.addslashes($_POST['INVOICECONTACT']).'"';
	$setlist[] = 'INVOICEMAILONLY='.(
		isset($_POST['INVOICEMAILONLY_OLD'])
	?	(isset($_POST['INVOICEMAILONLY']) ? "b'1'" : (strlen($_POST['INVOICEMAILONLY_OLD']) ? "b'0'" : 'NULL'))
	:	(isset($_POST['INVOICEMAILONLY']) ? "b'1'" : 'NULL')
	);
	$setlist[] = 'CITYID='.$_POST['CITYID'];
	$setlist[] = 'KVKNUM="'.addslashes($_POST['KVKNUM']).'"';
	$setlist[] = 'VATNUM="'.addslashes($_POST['VATNUM']).'"';
	$setlist[] = 'DISCOUNT='.$_POST['DISCOUNT'];
	$setlist[] = 'LOGO_DISCOUNT='.$_POST['LOGO_DISCOUNT'];
	$setlist[] = 'DISCOUNT_STOPSTAMP='.($_POST['DISCOUNT'] ? _date_getstamp($_POST,'DISCOUNT_STOP') : 0);
	$setlist[] = 'DEFAULT_COMMISSION='.$_POST['DEFAULT_COMMISSION'];
	$setlist[] = 'POBOX='.$_POST['POBOX'];
	$setlist[] = 'REQUEST=0';
	$setlist[] = 'BODY="'.addslashes($_POST['BODY']).'"';

	if ($relationid = $_REQUEST['sID']) {
		$setlist[] = 'MSTAMP='.CURRENTSTAMP;
		$setlist[] = 'MUSERID='.CURRENTUSERID;
		if (!db_insert('relation_log','
			INSERT INTO relation_log
			SELECT * FROM relation
			WHERE RELATIONID='.$relationid)
		||	!db_update('relation','
			UPDATE relation SET '.implode(',',$setlist).'
			WHERE RELATIONID='.$relationid)
		) {
			return false;
		}
		_notice(__('relation:notice:changed_LINE'));
	} else {
		$setlist[] = 'CSTAMP='.CURRENTSTAMP;
		$setlist[] = 'CUSERID='.CURRENTUSERID;

		if (!db_insert('relation','INSERT INTO relation SET '.implode(',',$setlist))) {
			return false;
		}
		$_REQUEST['sID'] = $relationid = db_insert_id();
		_notice(__('relation:notice:added_LINE'));
	}
	foreach ($_POST['CONTACTUSERID'] as $userid) {
		if (!$userid) {
			continue;
		}
		$userids[] = $userid;
	}
	$oldmembers = db_simpler_array('relationmember','SELECT USERID FROM relationmember WHERE RELATIONID='.$relationid);
	if ($oldmembers === false) {
		return false;
	}
	$and_where_useridstr = isset($userids) ? ' AND USERID NOT IN ('.implode(',',$userids).')' : null;

	if (!db_insert('relationmember_log','
		INSERT INTO relationmember_log
		SELECT *,'.CURRENTSTAMP.','.CURRENTUSERID.'
		FROM relationmember
		WHERE RELATIONID='.$relationid.
		 $and_where_useridstr)
	||	!db_delete('relationmember','
		DELETE FROM relationmember
		WHERE RELATIONID='.$relationid.
		 $and_where_useridstr)
	) {
		return false;
	}
	require_once '_employees.inc';
	if (isset($userids)) {
		foreach ($userids as $userid) {
			$inslist[] =
				'('.$relationid.
				','.$userid.
				','.(isset($_POST['COSTINDICATION']) && $userid == $_POST['CONTACTUSERID'][$_POST['COSTINDICATION']] ?  RMEMB_COST_INDICATION : 0).
				','.CURRENTSTAMP.
				','.CURRENTUSERID.
				','.CURRENTSTAMP.
				')';
		}
		if (!db_insert('relationmember','
			INSERT INTO relationmember (RELATIONID,USERID,FLAGS,CSTAMP,MUSERID,MSTAMP)
			VALUES '.implode(',',$inslist).'
			ON DUPLICATE KEY UPDATE
				MUSERID	=IF(FLAGS=VALUES(FLAGS),MUSERID,VALUES(MUSERID)),
				MSTAMP	=IF(FLAGS=VALUES(FLAGS),MSTAMP,VALUES(MSTAMP)),
				FLAGS	=IF(FLAGS=VALUES(FLAGS),FLAGS,VALUES(FLAGS))')
		) {
			return false;
		}
		flush_employee($userid);
	}
	foreach ($oldmembers as $userid) {
		memcached_delete('rels:'.$userid);
		flush_employee($userid);
	}
	return true;
}
