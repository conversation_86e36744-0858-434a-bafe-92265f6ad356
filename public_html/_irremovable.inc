<?php

declare(strict_types=1);

function irremovable_reason(string $element, int $id): string|false {
	$reasons = [];
	if (!have_admin($element.'_remove')
	&&	have_admin($element.'_remove_unaccepted')
	) {
		if (false === ($accepted = db_single($element,'SELECT 1 FROM '.$element.' WHERE ACCEPTED=1 AND '.$element.'ID='.$id,DB_USE_MASTER))) {
			return false;
		}
		if (!$accepted) {
			$reasons[] = __('removable:error:is_accepted_LINE');
		} elseif (false === ($was_accepted = db_single($element.'_log','SELECT 1 FROM '.$element.' WHERE ACCEPTED=1 AND '.$element.'ID='.$id.' LIMIT 1',DB_USE_MASTER))) {
			return false;
		} elseif ($was_accepted) {
			$reasons[] = __('removable:error:once_was_accepted_LINE');
		}
	}
	require_once '_employees.inc';
	require_once '_connect.inc';
/*	if (is_employer($element)
	&&	have_employees($element,$id)
	) {
		$reasons[] = __('removable:error:have_employees_LINE');
	}*/
/*	$connections = get_connections($element,$id);
	foreach ($connections as $connected_element => $connected_ids) {
		$reasons[] = __('removable:error:still_connected_LINE',array('ELEMENT'=>$connected_element,'IDS'=>$connected_ids));
	}*/
/*	if ($cnt = db_single('contact_ticket','SELECT COUNT(*) FROM contact_ticket WHERE ELEMENT="'.$element.'" AND ID='.$id,DB_USE_MASTER)) {
		$reasons[] = __('removable:error:still_connected_LINE',array('ELEMENT'=>'contact_ticket','CNT'=>$cnt));
	}*/
	$connections = [];
	switch ($element) {
	case 'contest':
		if (false === ($sms		= db_single('contest_extra_chance', "SELECT 1 FROM contest_extra_chance WHERE CONTESTID = $id LIMIT 1"))
		||	false === ($sms_too	= db_single('targetsms', 		   "SELECT 1 FROM targetsms WHERE SHORTKEY = 'WIN $id' LIMIT 1"))
		) {
			return false;
		}
		if ($sms || $sms_too) {
			$reasons[] = __('removable:error:sms_message_have_been_sent_LINE');
		}
		break;

	case 'artist':
#		if ($cnt = db_single('lineup','SELECT COUNT(*) FROM lineup WHERE ARTISTID='.$id,DB_USE_MASTER)) {
#			$reasons[] = __('removable:error:still_connected_LINE',DO_UBB,array('ELEMENT'=>'party','CNT'=>$cnt));
#		}
		if (false === ($cnt = db_single('musicproducer', 'SELECT COUNT(*) FROM musicproducer WHERE TYPE = "artist" AND ID = '.$id, DB_USE_MASTER))) {
			return false;
		}
		if ($cnt) {
			$reasons[] = __('removable:error:still_connected_LINE', DO_UBB, ['ELEMENT' => 'music','CNT' => $cnt]);
		}
		break;

	case 'city':
		if (false === ($promo_count = db_single('promo_focus','SELECT COUNT(*) FROM promo_focus WHERE TYPE="city" AND ID='.$id,DB_USE_MASTER))
		||	false === ($job_count	= db_single('job_focus',  'SELECT COUNT(*) FROM   job_focus WHERE TYPE="city" AND ID='.$id,DB_USE_MASTER))
		) {
			return false;
		}
		if (($count = $promo_count)
		&&	($test_element = 'promo')
		||	($count = $job_count)
		&&	($test_element = 'job')
		) {
			$reasons[] = __('removable:error:still_connected_LINE',DO_UBB, ['ELEMENT' => $test_element, 'CNT' => $count]);
		}
		$baseidname = 'CITYID';
		$connections = [
			'location'			=> ['location', 'LOCATIONID'],
			'user'				=> 'USERID',
			'party'				=> 'PARTYID',
			'adforcity'			=> ['ad', 'ADID'],
			'adforcityspec'		=> ['ad', 'ADID'],
#			'cityautorename'	=> 'ALIASID',
			'organization'		=> 'ORGANIZATIONID'
		];
		break;

	case 'boarding':
	case 'location':
		$baseidname = $element.'ID';
		$connections = ['party'	=> 'PARTYID'];
		break;

	case 'news':
		$baseidname = 'NEWSID';
		$connections = ['newsad' => 'NEWSADID'];
		break;

	case 'ad':
	case 'newsad':
		$active = db_single($element, 'SELECT ACTIVE AND REMOVED = 0 FROM '.$element.' WHERE '.$element.'ID = '.$id, DB_USE_MASTER);
		if (query_failed()) {
			return false;
		}
		if ($active) {
			$reasons[] = __('removable:error:still_active_LINE');
		}
		break;

	case 'party':
		if (!have_super_admin()) {
			if (false === ($going = db_single(['going',$element], "
				SELECT (SELECT SUM(VISITORS) + SUM(FOLLOWERS)
						FROM appic_event
						JOIN fbid USING (FBID)
						WHERE ELEMENT = 'party'
						  AND ID=$id
				) + (	SELECT COUNT(*)
						FROM going
						WHERE USERID != (SELECT USERID FROM party WHERE PARTYID = $id)
						  AND PARTYID = $id
				)",
				DB_USE_MASTER
			))) {
				return false;
			}
			if ($going) {
				$reasons[] = __('removable:party:has_visitors_LINE');
			}
		}
		$baseidname = 'PARTYID';
		$connections = [
			'contest'		=> 'CONTESTID',
			'meeting'		=> 'MEETINGID',
			'albumelement'	=> 'ALBUMELEMENTID',
			'albummap'		=> 'MAPID',
			'report'		=> 'REPORTID',
			'camera'		=> 'PARTYID',
			'flockmeeting'	=> 'MEETINGID',
			'image'			=> 'IMGID',
			'gallery'		=> 'GALLERYID',
			'pixel'			=> 'PIXELID',
			'salescontactme'=> 'PARTYID',
		];
		break;

	case 'relation':
		$baseidname = 'RELATIONID';
		$connections = [
			'banner'	=> 'RELATIONID',
			'promo'		=> 'PROMOID',
			'newsad'	=> 'NEWSADID',
			'job'		=> 'JOBID',
		];
		break;

	case 'banner':
		$baseidname = 'BANNERID';
		$connections = ['ad' => 'ADID'];
		break;
	}

	foreach ($connections as $connected_element => $info) {
		if (is_array($info)) {
			[$named_element, $idname] = $info;
		} else {
			$named_element = $connected_element;
			$idname = $info;
		}
		if (false === ($ids = db_simpler_array($connected_element,'
			SELECT '.$idname.'
			FROM `'.$connected_element.'`
			WHERE '.$baseidname.' = '.$id.
			(in_array($connected_element, ['ad', 'newsad'], strict: true) ? ' AND REMOVED = 0' : ''),
			DB_USE_MASTER
		))) {
			return false;
		}
		if ($ids) {
			$reasons[] = __('removable:error:still_connected_TEXT',DO_UBB|DO_NL2BR,[
				'ELEMENT' => $named_element,
				'CNT'	  => count($ids),
				'IDS'	  => implode(',',$ids)
			]);
		}
	}
	# connect warns
	foreach ([
		'banner',
		'gallery',
		'interview',
		'invoice',
		'news',
		'newsad',
		'promo',
		'review',
		'video',
	] as $assoctype) {
		if (false === ($ids = db_simpler_array('connect','
			SELECT ASSOCID
			FROM connect
			WHERE MAINTYPE="'.$element.'"
			  AND MAINID='.$id.'
			  AND ASSOCTYPE="'.$assoctype.'"'
		))) {
			return false;
		}
		if ($ids) {
			$reasons[] = __('removable:error:still_connected_TEXT',DO_UBB|DO_NL2BR,['ELEMENT'=>$assoctype,'CNT'=>count($ids),'IDS'=>implode(',',$ids)]);
		}
	}
	if (isset($GLOBALS['__error'])) {
		$reasons = [...$reasons, ...$GLOBALS['__error']];
		unset($GLOBALS['__error']);
	}
	if ($reasons) {
		return '<div class="block">'.implode('</div><div class="block">', $reasons).'</div>';
	}
	return '';
}
