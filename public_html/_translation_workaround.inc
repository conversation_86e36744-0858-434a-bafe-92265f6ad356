<?php

declare(strict_types=1);

function translation_fix_bugged_entries(): int|false {
	if (!waitforslaves('translationtext', ok_replicas: $ok_replicas)
	&&	!$ok_replicas
	) {
		mail_log('could not wait for slaves on table translationtext');
		return false;
	}
	
	# just check all entries, is fast enough

	if (!($key_ids = db_simpler_array('translationtext', "
		SELECT KEYID, LANGID
		FROM party_db.translationtext
		WHERE BINARY BODY LIKE CONCAT('%', CHAR(0xCD), '%')",
		DB_FORCE_MASTER
	))) {
		if ($key_ids === false) {
			return false;
		}
		return 0;
	}
	$goods = null;
	global $_db_servers;
	foreach ($ok_replicas as $server_name) {
		$server = $_db_servers[$server_name];
		#	Only party_db has translations
		if ($server['DBNAME'] !== 'party_db'
		#	Skip the master, the master is broken so we need correct translations from the slaves
		||	CURRENT_MASTER_party_db === $server_name
		) {
			continue;
		}
		if (!($goods = db_rowuse_array('translationtext', '
			SELECT KEYID, LANGID, BODY
			FROM translationtext
			WHERE KEYID IN ('.implode(', ', $key_ids).')
			  AND BINARY BODY NOT LIKE CONCAT("%",CHAR(0xCD),"%")',
			DB_FORCE_SERVER,
			$server_name
		))) {
			continue;
		}
		break;
	}
	if (!$goods) {
		if ($goods !== false) {
			register_error('translation:error:could_not_fix_bugged_entries_LINE');
		}
		return false;
	}
	$fixed = 0;
	foreach ($goods as $good) {
		/** @noinspection PhpRedundantOptionalArgumentInspection */
		extract($good, \EXTR_OVERWRITE);
		$BODY = addslashes($BODY);

		if (!db_update('translationtext', "
			UPDATE translationtext SET
				BODY = '$BODY'
			WHERE LANGID = $LANGID
			  AND KEYID  = $KEYID")
		) {
			return $fixed;
		}
		if (db_affected()) {
			++$fixed;
		}
	}
	return $fixed;
}
