<?php

class _provincelist {
	private $provinces = null;
	private $total = 0;
	private $wheres = null;
	
	public $show_header = false;
	public $show_people = false;
	
	function query() {
		$this->provinces = memcached_rowuse_array_if_not_admin(
			'province',
			'province','
			SELECT PROVINCEID,province.NAME,COUNTRYID
			FROM province '.
			($this->wheres ? ' WHERE '.implode(' AND ',$this->wheres) : ''),
			ONE_HOUR
		);
		if ($this->provinces === false) {
			return false;
		}
		return true;
	}
	function display_province($province) {
		layout_start_rrow();
		echo get_element_link('province',$province['PROVINCEID'],$province['NAME']);
		if ($this->show_people) {
			layout_next_cell(class: 'right light');
			if (isset($this->people[$province['PROVINCEID']])
			&&	($cnt = $this->people[$province['PROVINCEID']])
			) {
				?><a href="/province/<?= $province['PROVINCEID'] ?>/users"><?= $cnt ?></a><?
			}
		}
		layout_stop_row();
	}
	function display() {
		if (!$this->provinces) {
			return;
		}
		string_sort($this->provinces,'NAME');
		layout_open_table(TABLE_FULL_WIDTH);
		if ($this->show_header) {
			$this->display_header();
		}
		$countryid = 1;
		foreach ($this->provinces as $province) {
			$this->display_province($province);
		}
		layout_close_table();
	}
	function in_country($countryid) {
		$this->wheres[] = 'province.COUNTRYID='.$countryid;
	}
}
function provincelist_display_options($selected = 0,$countryid = 1) {
	if (is_array($countryid)) {
		$provlist = db_rowuse_array(
			'province','
			SELECT PROVINCEID,NAME,COUNTRYID
			FROM province
			WHERE COUNTRYID IN ('.implode(',',$countryid).')
			ORDER BY NAME ASC'
		);
		if (!$provlist) {
			return;
		}
		generate_name_doubles($provlist, 'NAME', 'provice');
	} else {
		$provlist = db_rowuse_array(
			'province','
			SELECT PROVINCEID,NAME,COUNTRYID
			FROM province
			WHERE COUNTRYID='.$countryid.'
			ORDER BY NAME ASC'
		);
		if (!$provlist) {
			return;
		}
	}
	foreach ($provlist as $province) {
		?><option value="<?= $province['PROVINCEID'];
		?>"<?
		if ($selected
		&&	$selected == $province['PROVINCEID']
		) {
			?> selected="selected"<?
		}
		?>><? echo escape_utf8($province['NAME']);
		if (isset($province['DOUBLE'])) {
			?> (<?= escape_specials(get_element_title('country',$province['COUNTRYID'])) ?>)<?
		}
		?></option><?
	}
}
