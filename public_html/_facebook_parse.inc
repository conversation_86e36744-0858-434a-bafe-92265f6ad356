<?php

declare(strict_types=1);

function prepare_dom_str(string $dom_str): string {
	if (!str_contains($dom_str, '<?xml encoding="utf-8"?>')) {
		# NOTE: Don't remove this. It makes sure emoji's are processed right
		$dom_str = '<?xml encoding="UTF-8"?>'.$dom_str;
	}
	return	preg_replace(
			# BR tags must be closed in XHTML,
			# SVG tags are not recognized
			'"<br[^>]*>"ui',
			"<br />\n",
			# replace emoji images with their alt text:
			preg_replace_callback('!<img(?<attribs1>.*?)src="(?<src>[^"]*?)"(?<attribs2>[^>]*?)>!uis',
				static function(array $match): string {
				if (!str_contains($match['src'], 'emoji.php')) {
					return $match[0];
				}
				foreach ([$match['attribs1'], $match['attribs2']] as $attribs) {
					if (preg_match('!alt="(?<alt>[^"]*?)"!u', $attribs, $attribs_match)) {
						return $attribs_match['alt'];
					}
				}
				return $match[0];
			},
			$dom_str));
}

function get_elements(string $element, array|int $fbids): array|false {
	if (!is_array($fbids)) {
		$fbids = [$fbids];
	}
	static $__items;
	$result = [];
	foreach ($fbids as $fbid) {
		if (!isset($__items[$element][$fbid])) {
			if (false === ($items = match($element) {
				'location' => db_rowuse_hash(['fbid', 'location', 'city'], "
				SELECT	LOCATIONID AS id, location.NAME AS name, CAST_TO_BIT(DEAD OR FOLLOWUPID) AS dead,
						city.NAME AS city_name
				FROM fbid
				JOIN location ON ID = LOCATIONID
				LEFT JOIN city USING (CITYID)
				WHERE ELEMENT = 'location'
				  AND FBID = $fbid
				ORDER BY DEAD, FOLLOWUPID"
				),
				'organization' => db_rowuse_hash(['fbid', 'organization'], "
				SELECT ORGANIZATIONID AS id, NAME AS name, CAST_TO_BIT(DEADSTAMP OR FOLLOWUPID) AS dead
				FROM fbid
				JOIN organization ON ID = ORGANIZATIONID
				WHERE DEADSTAMP = 0
				  AND FOLLOWUPID = 0
				  AND ELEMENT = 'organization'
				  AND FBID = $fbid
				ORDER BY DEADSTAMP, FOLLOWUPID"
			)})) {
				return false;
			}
			$__items[$element][$fbid] = $items;
		}
		if ($__items[$element][$fbid]) {
			$result = [...$result, ...$__items[$element][$fbid]];
		}
	}
	return $result;
}

function set_libxml_error_handler(): void {
	set_error_handler(static function(
		int $errno,
		string $errstr,
		# ?string $errfile,
		# ?int $errline,
	) : bool {
		#error_log("errno: $errno\nerrstr: $errstr\nerrfile: $errfile\nerrline: $errline");
		return $errno === 2
			   && (str_contains($errstr, 'Opening and ending tag mismatch')
				   || preg_match('"Tag [a-z\d_-]+ invalid in Entity"', $errstr));
	});
}
