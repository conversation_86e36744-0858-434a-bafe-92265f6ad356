<?php

function newsad_commit(?int $newsadid = null, ?int $pstamp = null): bool {
#	define('DRYRUN',true);
	if (!require_admin('newsad')) {
		return false;
	}
	require_once '_distribution.inc';
	if (!$newsadid) {
		$newsadid = $_REQUEST['sID'];
	}
	$oldnewsad = null;
	if ($newsadid
	&&	!($oldnewsad = db_single_assoc(array('newsad','news'),'
		SELECT	NEWSADID, ACTIVE, STARTSTAMP, STOPSTAMP, IMPRESSIONS, IMPRESSIONSDONE, VOUCHERID, NEWSADPRICEID, DOMAIN,
				NEWSID, PSTAMP
		FROM newsad
		LEFT JOIN news USING (NEWSID)
		WHERE NEWSADID = '.$newsadid,
		DB_USE_MASTER))
	) {
		if ($oldnewsad !== false) {
			register_error('newsad:error:nonexistent_LINE', ['ID' => $newsadid]);
		}
		return false;
	}
	$keeptop = isset($_POST['KEEPTOP']) ? 1 : 0;
	if ($calcdistr = $pstamp || $_REQUEST['ACTION'] === 'calcdistr') {
		if (!$oldnewsad) {
			return false;
		}
		if ($oldnewsad['NEWSID']) {
			if ($pstamp === null) {
				$pstamp = $oldnewsad['PSTAMP'];
			}
			if (!$pstamp) {
				register_error('news:error:no_pstamp_LINE', ['NEWSID' => $oldnewsad['NEWSID']]);
				return false;
			}
			if ($pstamp > $oldnewsad['STARTSTAMP']) {
				$setlist['STARTSTAMP'] = $pstamp;
				$oldnewsad['STARTSTAMP'] = $pstamp;
				$start_at_pstamp = true;
			}
		}
		$startstamp = $oldnewsad['STARTSTAMP'];
		$stopstamp = $oldnewsad['STOPSTAMP'];
		$domain = $oldnewsad['DOMAIN'];
		$_POST['IMPRESSIONS'] = $oldnewsad['IMPRESSIONS'];
	} else {
		if (
		#	!require_number_or_empty($_POST,'INVOICENR')
			!require_date($_POST, 'START')
		||	!optional_number($_POST, 'RELATIONID')
		||	false === require_number($_POST, 'NEWSID')
		||	false === require_number($_POST, 'DISCOUNT')
		# NOTE:
		# In _duration.inc there is a check of the date part in process_duration_form_parts()..
		# Time is not explicitly checked for corrected, which resulted in difficult to understand bits.
		# At least, in that same function are the _date_getstamp() calls for both START and STOP times,
		# and _date_getstamp() doesn't do a explicit check if hours are between 0 and 23 and minutes 0 an 59,
		# but just uses 0 as a default value for any wrong value.
		# //	!require_date_and_time($_POST, 'START')
		||	false === require_number($_POST, 'DURATION')
		||	false === require_number($_POST, 'CLICKBOX')
		#||	false === require_number($_POST, 'INEWSID')
		#||	!optional_number($_REQUEST, 'sID')
		||	!require_element($_POST, 'WHAT', ['impressions', 'budget'], strict: true)
		||	false === require_number($_POST, 'WHATVAL')
		||	!optional_number($_POST,'PRICE')
		||	false === ($domain = require_element($_POST, 'DOMAIN', ['', 'nl', 'be'], strict: true, default: ''))
		) {
			return false;
		}
		$startstamp = $stopstamp = 0;
		$newsid = $_POST['NEWSID'];
		$startstamp = _date_getstamp($_POST, 'START');

		require_once '_duration.inc';
		if (!($info = process_duration_form_parts())) {
			return false;
		}
		[$startstamp, $stopstamp] = $info;
		if ($newsid) {
			if (!($pstamp = db_single('news', 'SELECT PSTAMP FROM news WHERE NEWSID = '.$newsid, DB_USE_MASTER))) {
				if ($pstamp !== false) {
					register_error('news:error:no_publication_date_LINE', ['ID' => $newsid]);
				}
				return false;
			}
			if ($pstamp > $startstamp) {
				$startstamp = $pstamp;
				$start_at_pstamp = true;
			}
			if (!$newsadid
			||	(	$oldnewsad['NEWSID']
				&&	$oldnewsad['NEWSID'] !== $newsid
				)
			) {
				if ($pstamp < CURRENTSTAMP - TWO_WEEKS) {
					register_warning('newsad:error:news_too_old_not_connected_max_two_weeks_LINE');
					return false;
				}
			}
		}
		$setlist['OTHER_DOMAIN_FORCED_FILTERS']
								= isset($_POST['OTHER_DOMAIN_FORCED_FILTERS']);
		$setlist['KEEPTOP']		= $keeptop;
		$setlist['STARTSTAMP']	= $startstamp;
		$setlist['STOPSTAMP']	= $stopstamp;
		$setlist['DOMAIN']		= $domain;

		if (!empty($oldnewsad)
		&&	(require_once '_ad.inc')
		&&	$oldnewsad['STOPSTAMP'] !== $stopstamp
		&&	$oldnewsad['STOPSTAMP'] < CURRENTSTAMP - FREQCAP_KEEP_PERIOD + 2 * ONE_DAY
		) {
			return false;
		}

		$setlist['PRICE']		= $_POST['PRICE'] ?: 0;
		$setlist['CLICKBOX']	= (bool)$_POST['CLICKBOX'];
		$setlist['KEEPGOING']	= isset($_POST['HARDSTOP']);

		$setlist['RELATIONID']	= $_POST['RELATIONID'];
		#$setlist['INVOICENR']	= ($_POST['INVOICENR'] === '' ? 'NULL' : $_POST['INVOICENR']);
		$setlist['NEWSID']		= $newsid;
		$setlist['DISCOUNT']	= $_POST['DISCOUNT'];

		if (!($price = db_single_array('newsadprice',
			$newsadid
		?	"SELECT NEWSADPRICEID, COSTPERMILLE FROM newsadprice WHERE NEWSADPRICEID = {$oldnewsad['NEWSADPRICEID']}"
		:	'SELECT NEWSADPRICEID, COSTPERMILLE FROM newsadprice WHERE NEWSADPRICEID <='.CURRENTSTAMP.' ORDER BY NEWSADPRICEID DESC LIMIT 1'))
		) {
			if ($price !== false) {
				register_error('newad:error:could_not_dermine_price_LINE');
			}
			return false;
		}
		[$priceid, $cpm] = $price;

		$impressions = $_POST['WHATVAL'];

		if ($_POST['WHAT'] === 'budget') {
			if (!$_POST['DISCOUNT']
			||	 $_POST['DISCOUNT'] === 100
			) {
				$impressions *= 1000 / $cpm;
			} else {
				$impressions *= 1000 / ($cpm * (100 - $_POST['DISCOUNT']) / 100);
			}
		}
		$_POST['IMPRESSIONS'] = $impressions;
	}
	if (isset($start_at_pstamp)) {
		register_warning('newsad:notice:ad_starts_pstamp_LINE');
	}
	if ($startstamp >= $stopstamp) {
		register_error('newsad:error:startstamp_after_stopstamp_LINE');
		return false;
	}
	if ($calcdistr
	||	(	CURRENTSTAMP < $stopstamp
		&&	(	!$oldnewsad
			||	$oldnewsad['STOPSTAMP']			   				!== $stopstamp
			&&	max($oldnewsad['STOPSTAMP'], $stopstamp)   		> CURRENTSTAMP
			||	$oldnewsad['STARTSTAMP'] 			 			!== $startstamp
			&&	max($oldnewsad['STARTSTAMP'], $startstamp)		> CURRENTSTAMP
			||	$oldnewsad['IMPRESSIONS']	  				  	!== $_POST['IMPRESSIONS']
			||	$oldnewsad['DOMAIN']		  					!== $_POST['DOMAIN']
			)
		)
	) {
		if ($keeptop) {
			if ($newsadid) {
				remove_distribution('newsad',$newsadid);
			}
			register_notice('distribution:notice:unnecessary_LINE');
		} else {
			if (!($distribution = calculate_distribution(
				ADTYPE_NEWS,
				$newsadid,
				$domain,
				$startstamp,
				$stopstamp,
				$_POST['IMPRESSIONS'],
				$oldnewsad['IMPRESSIONSDONE'] ?? 0,
				$maximp))
			) {
				if ($distribution !== false) {
					if ($maximp === false) {
						register_error('distribution:error:something_went_wrong_LINE');
					} else {
						register_error('newsad:error:infeasible_LINE', ['MAXIMPS' => floor($maximp)]);
					}
				}
				return false;
			}
			if ($calcdistr) {
				register_notice('distribution:notice:recalculated_LINE');
			}
			if (!$distribution
			&&	$maximp < $_POST['IMPRESSIONS']
			) {
				register_warning('newsad:error:infeasible_LINE', ['MAXIMPS' => (int)$maximp]);
				$_POST['IMPRESSIONS'] = $maximp;
			}
#			$setlist['IMPRESSIONSTRY'] = floor($_POST['IMPRESSIONSTRY']);
		}
	}
	$setlist['IMPRESSIONS'] = (int)$_POST['IMPRESSIONS'];
	if (isset($_POST['VOUCHER'])) {
		require_once '_voucher.inc';
		if (false === ($voucherid = optional_voucherid($oldnewsad))) {
			return false;
		}
		$setlist['VOUCHERID'] = $voucherid;
	}
	require_once '_profilefilter.inc';
	profilefilter_post_item($setlist);

	[$instr, $valstr, $bincmp] = inserts_values_and_bincmp($setlist);
	if ($newsadid) {
		if (!db_insert('newsad_log', "
			INSERT INTO newsad_log
			SELECT * FROM newsad
			WHERE NOT $bincmp
			  AND NEWSADID = $newsadid")
		) {
			return false;
		}
		if (db_affected()) {
			if (!db_update('newsad','
				UPDATE newsad SET
					MUSERID	= '.CURRENTUSERID.',
					MSTAMP	= '.CURRENTSTAMP.",
					$instr
				WHERE NEWSADID = $newsadid")
			) {
				return false;
			}
		}
		register_notice('newsad:notice:changed_LINE');
	} else {
		if (!db_insert('newsad', "
			INSERT INTO newsad SET 
				NEWSADPRICEID	= $priceid,
				USERID			= ".CURRENTUSERID.',
				CSTAMP			= '.CURRENTSTAMP.",
				$instr")
		) {
			return false;
		}
		$_REQUEST['sID'] = $newsadid = db_insert_id();
		register_notice('newsad:notice:added_LINE');
		ticket_update('newsad', $newsadid);
	}
	db_create('adimp', "
		CREATE TABLE IF NOT EXISTS `adimp_newsad_$newsadid` (
			`IDENTID` int(10) unsigned NOT NULL DEFAULT 0,
			`USERID` mediumint(8) unsigned NOT NULL DEFAULT 0,
			`IPNUM` int(10) unsigned NOT NULL DEFAULT 0,
			`STAMP` int(10) unsigned NOT NULL DEFAULT 0,
			`SERVED` bit(1) NOT NULL DEFAULT b'0',
			`FREE` bit(1) NOT NULL DEFAULT b'0',
			`UNIQ` bigint(20) unsigned NOT NULL DEFAULT 0,
			`TRY` bit(1) NOT NULL DEFAULT b'0',
			`INCID` int(10) unsigned NOT NULL AUTO_INCREMENT,
			`IMMEDIATE` bit(1) NOT NULL DEFAULT b'0',
			`EXPANDED` bit(1) NOT NULL DEFAULT b'0',
			`CLICKED` bit(1) NOT NULL DEFAULT b'0',
			`IPBIN` varbinary(16) NOT NULL DEFAULT '',
			`HARDMATCH` bit(1) NOT NULL DEFAULT b'0',
			PRIMARY KEY (`INCID`),
			KEY `ELEMENT` (`ELEMENT`,`ID`)
		) ENGINE=MyISAM DEFAULT CHARSET=ascii PAGE_CHECKSUM=1");

	if (isset($distribution)) {
		store_distribution($newsadid, $distribution);
	} elseif ($keeptop) {
		remove_distribution('newsad', $newsadid);
	}
	require_once '_profilefilter.inc';
	store_profilefilter();
	return true;
}
