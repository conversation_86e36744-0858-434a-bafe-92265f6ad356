<?php

declare(strict_types=1);

require_once '_syslog.inc';

# by default, warn after ten minutes

function already_running(
	?string	$lock_extra = null,
	int		$warn_after = 600,
	?string	$msg_extra  = null
): bool {
	global $argv;
	$basename = basename($argv[0], '.php');
	$lock_file_name = "/tmp/__$basename".($lock_extra ? "_$lock_extra" : '').'.lock';
	$time_file_name = "{$lock_file_name}_time";

	if (!($lock_file = @fopen($lock_file_name, 'cb'))) {
		syslog_last_error(LOG_ERR, "not enough rights to lock $lock_file_name");
		return true;
	}
	if (flock($lock_file, LOCK_EX | LOCK_NB)) {
		# we need the files to remain open, so store them in static var:
		static $__locks = [];
		$__locks[] = $lock_file;
		if (!@file_put_contents($time_file_name, time())) {
			syslog_last_error(LOG_WARNING, "could not store lock time $time_file_name");
		}
		return false;
	}
	$at = '';
	if (!($force_warn = CLI && defined('DEBUG') && DEBUG)) {
		if (!$warn_after) {
			syslog(LOG_INFO, 'already running');
			return true;
		}
		$at = '';
		if (!($stamp = (int)file_get_contents($time_file_name))) {
			syslog_error(LOG_ERR, "could not read lock time from $time_file_name");
			return true;
		}
		# Warn only if lock is held for more than warn_after seconds.
		$waited = time() - $stamp;
		if ($warn_after !== true
		||	$waited < $warn_after
		) {
			syslog(LOG_INFO, "already running, held for $waited seconds");
			return true;
		}
		$info = localtime($stamp);
		$at = ', locked at '.sprintf('%d-%02d-%02d %02d:%02d',
			$info[5] + 1900,
			$info[4] + 1,
			$info[3],
			$info[2],
			$info[1]
		);
	}
	syslog_error(LOG_WARNING, 'already running, forced warning');
	if ($msg_extra) {
		syslog_error(LOG_WARNING, $msg_extra);
	}
	return true;
}
