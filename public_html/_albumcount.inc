<?php

require_once '_visibility.inc';

function album_element_counts(int $albumid): array {
	static $__albumcounts;
	if (isset($__albumcounts[$albumid])) {
		return $__albumcounts[$albumid];
	}
	if (false === ($counts = memcached_get($cache_key = 'albumcounts:'.$albumid))) {
		if (!($counts = get_album_counts($albumid))) {
			$counts = [false, false, false, false];
			$keep = FIVE_MINUTES;
		}
		memcached_set($cache_key, $counts, $keep ?? ONE_HOUR);
	}
	return $__albumcounts[$albumid] = $counts;
}
function get_album_counts(int $albumid): array|false {
	$counts = false;
	if (false === ($maps = db_multirowuse_hash('albummap','
		SELECT UPMAPID, MAPID, ACCEPTED, VISIBILITY_MAP
		FROM albummap
		WHERE ALBUMID = '.$albumid))
	) {
		return false;
	}
	$mapcounts = null;
	$elemcounts = null;
	$unacceptedmapcounts = null;
	$unacceptedelemcounts = null;
	if ($maps) {
		if (false === ($elems = db_multirowuse_hash('albumelement', '
			SELECT MAPID, ALBUMELEMENTID, ACCEPTED, VISIBILITY_ELEMENT
			FROM albumelement
			WHERE ALBUMID = '.$albumid))
		) {
			return false;
		}
		if (!determine_album_counts(
				$albumid,
				0,
				$maps,
				$elems,
				$mapcounts,
				$elemcounts,
				$unacceptedmapcounts,
				$unacceptedelemcounts)
		) {
			return false;
		}
	}
	return [
		$mapcounts,
		$elemcounts,
		$unacceptedmapcounts,
		$unacceptedelemcounts
	];
}
function determine_album_counts(
	int $albumid,
	int $upmapid,
	$maps,
	$elems,
	&$mapcounts,
	&$elemcounts,
	&$unacceptedmapcounts,
	&$unacceptedelemcounts,
	$startvisibility = 0
): bool {
	if (!isset($maps[$upmapid])) {
		error_log('UPMAPID '.$upmapid.' not found for ALBUMID '.$albumid.' in albumcount',0);
#		error_log_r($maps,'maps');
		return false;
	}
	$ok = true;
	foreach ($maps[$upmapid] as &$map) {
		$currvisibility = $startvisibility;
		$map['USERID'] = $albumid;
		$mapid = $map['MAPID'];
		if ($unaccepted = !$map['ACCEPTED']) {
			$unacceptedmapcounts[$mapid] = 1 + ($unacceptedmapcounts[$mapid] ?? 0);
			$currvisibility = SELF;
		} else {
			$vis = $map['VISIBILITY_MAP'];
			if ($currvisibility <= $vis) {
				$mapcounts[		0][$vis] = 1 + ($mapcounts[		0][$vis] ?? 0);
				$mapcounts[$mapid][$vis] = 1 + ($mapcounts[$mapid][$vis] ?? 0);
				$currvisibility = $vis;
			}
		}
		if (isset($elems[$mapid])) {
			foreach ($elems[$mapid] as $elem) {
				if ($unaccepted
				||	!$elem['ACCEPTED']
				) {
					$unacceptedelemcounts[$mapid] = 1 + ($unacceptedelemcounts[$mapid] ?? 0);
					continue;
				}
				$vis = $currvisibility < $elem['VISIBILITY_ELEMENT'] ? $elem['VISIBILITY_ELEMENT'] : $currvisibility;
				$elemcounts[	 0][$vis] = 1 + ($elemcounts[	  0][$vis] ?? 0);
				$elemcounts[$mapid][$vis] = 1 + ($elemcounts[$mapid][$vis] ?? 0);
			}
		}
		if (isset($maps[$mapid])) {
			if (!determine_album_counts(
				$albumid,
				$mapid,
				$maps,
				$elems,
				$mapcounts,
				$elemcounts,
				$unacceptedmapcounts,
				$unacceptedelemcounts,
				$currvisibility)
			) {
				$ok = false;
			}
		}
	}
	return $ok;
}
function album_total_counts(int $albumid, int $vis): array|false {
	static $__albumtotals;
	if (isset($__albumtotals[$albumid][$vis])) {
		return $__albumtotals[$albumid][$vis];
	}
	if (false === ($info = memcached_get($cache_key = "albumtotals:$albumid:$vis"))) {
		[	$mapcounts,
			$elemcounts,
			$unacceptedmapcounts,
			$unacceptedelemcounts
		] = album_element_counts($albumid);

		if (!$mapcounts
		&&	!$elemcounts
		&&	(	!$unacceptedmapcounts
			&&	!$unacceptedelemcounts
			||	!(require_once '_require.inc')
			||	!(list($mayview) = album_access($albumid))
			||	!$mayview
			)
		) {
			return $__albumtotals[$albumid][$vis] = false;
		}
		$mapcnt  = 0;
		$elemcnt = 0;
		if ($mapcounts
		||	$elemcounts
		) {
			while ($vis >= 0) {
				if ($mapcounts
				&&	isset($mapcounts[0][$vis])
				) {
					$mapcnt += $mapcounts[0][$vis];
				}
				if ($elemcounts
				&&	isset($elemcounts[0][$vis])
				) {
					$elemcnt += $elemcounts[0][$vis];
				}
				--$vis;
			}
		}
		memcached_set(
			$cache_key,
			$info = [
				$mapcnt,
				$elemcnt,
				$unacceptedmapcounts  ? array_sum($unacceptedmapcounts)  : 0,
				$unacceptedelemcounts ? array_sum($unacceptedelemcounts) : 0
			],
			ONE_HOUR
		);
	}
	return $__albumtotals[$albumid][$vis] = $info;
}
function album_map_counts(int $albumid, int $mapid, int $vis): array|false {
	static $__albummapcounts;
	if (isset($__albummapcounts[$mapid][$vis])) {
		return $__albummapcounts[$mapid][$vis];
	}
	if (false === ($info = memcached_get($cache_key = "albummapcounts:$mapid:$vis"))) {
		[	$mapcounts,
			$elemcounts,
			$unacceptedmapcounts,
			$unacceptedelemcounts
		] = album_element_counts($albumid);

		if (!$mapcounts
		&&	!$elemcounts
		&&	(	!$unacceptedmapcounts
			&&	!$unacceptedelemcounts
#			||	any_view_album($albumid)
			)
		) {
			return $__albummapcounts[$mapid] = false;
		}
		$unacceptedelemcnt = 0;
		$unacceptedmapcnt  = 0;
		$subelemcnt        = 0;
		$elemcnt           = 0;
		$tmpvis            = $vis;
		while ($tmpvis >= 0) {
			$elemcnt += $elemcounts[$mapid][$tmpvis] ?? 0;
			$unacceptedmapcnt  += $unacceptedmapcounts[$mapid] ?? 0;
			$unacceptedelemcnt += $unacceptedelemcounts[$mapid] ?? 0;
			--$tmpvis;
		}
		if ($innermaps = get_all_innermaps($albumid,$mapid)) {
			foreach ($innermaps as $tmpmapid) {
				if ($tmpmapid === $mapid
				||	!isset($elemcounts[$tmpmapid])
				) {
					continue;
				}
				$tmpvis = $vis;
				while ($tmpvis >= 0) {
					$subelemcnt += $elemcounts[$tmpmapid][$tmpvis] ?? 0;
					$unacceptedmapcnt  += $unacceptedmapcounts [$tmpmapid] ?? 0;
					$unacceptedelemcnt += $unacceptedelemcounts[$tmpmapid] ?? 0;
					--$tmpvis;
				}
			}
		}
		memcached_set(
			$cache_key,
			$info = [
				$elemcnt,
				$subelemcnt,
				$unacceptedmapcnt,
				$unacceptedelemcnt
			],
			ONE_HOUR
		);
	}
	return $__albummapcounts[$mapid][$vis] = $info;
}
