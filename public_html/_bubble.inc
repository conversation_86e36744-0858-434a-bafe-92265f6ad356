<?php

const BUBBLE_CLEAN			= 1;
const BUBBLE_CURSOR			= 2;
const BUBBLE_BELOW			= 4;
const CLICK_BUBBLE			= 8;
const BUBBLE_CATCHER_NOCAPS	= 16;
const BUBBLE_BLOCK			= 32;
const BUBBLE_30_WIDE		= 64;
const BUBBLE_KEEP_ONCE		= 128;
const CLICK_FOR_QUICK		= 256;
const CATCHER_UNAVAILABLE	= 512;
const CATCHER_LIGHT			= 1024;
const CLICK_KEEP_BUBBLE		= 2048;	# click on catcher, bubble opens and stays open until clicked again
const CATCHER_SMALL			= 4096;
const CONTENT_AT_BOTTOM		= 8192;	# print bubble contents just before html body is closed
const CLICK_OPEN_CENTER		= 16384;
const HELP_BUBBLE			= 32768;
const HELP_CATCHER			= 0x10000;
const LIGHT_SUP				= 0x20000;

const STATE_CATCHER				= 1;
const STATE_TITLE				= 2;
const STATE_CATCHER_AND_TITLE	= 3;
const STATE_CONTENT				= 4;
const STATE_CONTENT_GET			= 5;
const STATE_FINISHED			= 6;
const STATE_KEEP_ONCE			= 7;

class bubble {
	static   array	$bubbles		= [];
	static   bool 	$initialized	= false;

	private	 string	$title;
	private  array	$extra_attribs	= [];
	private  string $span;
	private  string $span_attribs;
	private  int	$bubbleid		= 0;
	private  int	$state			= 0;
	private ?string	$hook;
	private  int	$flags;

	private  string $catcher;
	private  string $content;

	public	 string	$offset;

	function __construct(int $flags = 0, ?string $hook = null) {
		$this->flags = $flags;
		$this->hook = $hook;
	}

	function flags(int $flags = 0): void {
		if ($this->state) {
			$this->end_state();
		}
		$this->flags = $flags;
	}

	function begin_state(int $state): void {
		$this->state = $state;
		ob_start();
	}

	function end_state(): void {
		if ($this->state >= STATE_FINISHED) {
			if ($this->state == STATE_FINISHED) {
				$this->state = 0;
			} else {
				$this->state = STATE_FINISHED;
			}
			return;
		}
		$data = ob_get_clean();
		switch ($this->state) {
		case STATE_CATCHER:				$this->catcher	= $data; break;
		case STATE_TITLE:				$this->title	= $data; break;
		case STATE_CATCHER_AND_TITLE:	$this->catcher	= $this->title; break;
		case STATE_CONTENT_GET:			ob_start();
		case STATE_CONTENT:				$this->content	= $data; $this->bubbleid = 0; break;
		# case STATE_KEEP_ONCE:			break;
		}
		$this->state = 0;
	}

	function catcher(
		?string	$catcher		= '',
		?array	$extra_attribs	= [],
		string	$span 			= '',
		string	$span_attribs	= '',
	): void {
		if ($this->state) {
			$this->end_state();
		}
		if ($extra_attribs) {
			HOME_THOMAS && mail_log('extra_attribs supplied to bubble->catcher', item: get_defined_vars());
			$this->extra_attribs = $extra_attribs;
		}
		if ($span) {
			# Used in user.inc:2805, span = label, span_attribs = for="logout_others"
			$this->span = $span;
		}
		if ($span_attribs) {
			$this->span_attribs = $span_attribs;
		}
		if ($catcher) {
			$this->catcher = $catcher;
		} else {
			$this->begin_state(STATE_CATCHER);
		}
	}

	function title(?string $title = null) {
		if ($this->state) {
			$this->end_state();
		}
		if ($title) {
			$this->title = $title;
		} else {
			$this->begin_state(STATE_TITLE);
		}
	}

	function catcher_and_title(?string $catcher_and_title = null) {
		if ($this->state) {
			$this->end_state();
		}
		if ($catcher_and_title) {
			$this->catcher = $this->title = $catcher_and_title;
		} else {
			$this->begin_state(STATE_CATCHER_AND_TITLE);
		}
	}

	function content(?string $content = null) {
		if ($this->state) {
			$this->end_state();
		}
		if ($content) {
			$this->content = $content;
		} else {
			$this->begin_state(STATE_CONTENT);
		}
	}

	function close() {
		if ($this->state) {
			$this->end_state();
		}
	}

	function display_and_keep_once() {
		$this->display(1);
	}

	function display_hidden_part() {
		$this->display(2);
	}

	function display($action = 0) {
		if ($this->state) {
			$this->end_state();
		}
		require_once '_smallscreen.inc';
		if (SMALL_SCREEN
		&&	!($this->flags & (CLICK_BUBBLE | CLICK_FOR_QUICK))
		) {
			echo $this->catcher;
			return;
		}
		if (!bubble::$initialized
		&&	!isset($_SERVER['AJAX'])
		) {
			include_js('js/bubble');
			$initialized = true;
		}
		$id = $this->bubbleid ? $this->bubbleid : ($this->bubbleid = crc32($this->content));
		if ($action != 2) {
			$this->display_catcher();
		}

		if (!isset(bubble::$bubbles[$id])) {
			if ($this->flags & CONTENT_AT_BOTTOM) {
				ob_start();
			}
			include_style('bubble');
			?><dialog<?
			$styles = null;
			if ($this->flags & BUBBLE_30_WIDE) {
				$styles[] = 'width:30em';
			}
			if ($this->flags & BUBBLE_BELOW) {
				# for valentine hearts
				$styles[] = 'margin-top:20px';
			}
			if ($styles) {
				?> style="<?= implode(';',$styles) ?>"<?
			}
			?> class="bubble"<?
			if (!($this->flags & CLICK_KEEP_BUBBLE)) {
				?> onmouseover="keep_bubble();"<?
				?> onmouseout="dropbubble(<?= $id ?>);"<?
			}
			require_once '_smallscreen.inc';
			if (SMALL_SCREEN
			&&	!($this->flags & CLICK_KEEP_BUBBLE)
			) {
				?> onclick="hidebubbleimmediate(<?= $id ?>)"<?
			}
			?> id="bubble_<?= $id; ?>"><?
			if (!empty($this->title)) {
				?><div class="title"><?= $this->flags & BUBBLE_CATCHER_NOCAPS ? $this->title : ucfirst($this->title); ?></div><?
			}
			if (!empty($this->content)) {
				echo $this->content;
			}
			?></dialog><?
			bubble::$bubbles[$id] = $this->flags & CONTENT_AT_BOTTOM ? ob_get_clean() : true;
		}
		$this->state = ($action == 1 ? STATE_KEEP_ONCE : STATE_FINISHED);
	}

	function get(): string {
		$this->state = STATE_CONTENT_GET;
		$this->display();
		return ob_get_clean();
	}

	function display_catcher() {
		$classes = ['catcher'];
		$flags = $this->flags;
		if (($flags & (CLICK_BUBBLE | CLICK_KEEP_BUBBLE))
		&&	!($flags & BUBBLE_CLEAN)
		) {
			$classes[] = 'unhideanchor';
		} elseif (!($flags & (BUBBLE_CLEAN | CATCHER_UNAVAILABLE | HELP_BUBBLE))) {
			$classes[] = 'dflt';
		}
		if ($flags & (BUBBLE_CURSOR | HELP_BUBBLE)) {
			$classes[] = 'hlp';
		}
		if ($flags & CATCHER_UNAVAILABLE) {
			$classes[] = 'unavailable';
		}
		if ($flags & HELP_CATCHER) {
			$classes[] = 'helpc';
		}
		if ($flags & CATCHER_LIGHT) {
			$classes[] = 'light';
		}
		if ($flags & CATCHER_SMALL) {
			$classes[] = 'small';
		}
		static $__catcher = 0;
		++$__catcher;
		$help = $flags & HELP_BUBBLE;
		?><<?
		echo $span = isset($this->span) ? $this->span : 'span';
		if (isset($this->span_attribs)) {
			?> <? echo $this->span_attribs;
		}
		if ($classes) {
			?> class="<?= implode(' ',$classes) ?>"<?
		}
		$id = $this->bubbleid ?: ($this->bubbleid = crc32($this->content));
		$keep_bubble = $flags & CLICK_KEEP_BUBBLE;
		$clickbubble = $flags & (CLICK_BUBBLE | CLICK_FOR_QUICK);
		$movebubble = (!$clickbubble && !$keep_bubble) || ($flags & CLICK_FOR_QUICK);
		if ($keep_bubble || $clickbubble) {
			?> onclick="if(!showbubblenow)return;<?
			if ($this->hook) {
				echo $this->hook; ?>(this<?
				if (!empty($this->extra_attribs)) {
					?>,<?= $this->extra_attribs;
				}
				?>);<?
			}
			if ($keep_bubble) {
				?>{var i='<?= $id ?>';if(!this.bubbleshown){this.bubbleshown=true;showbubblenow(i,event,<?= $__catcher ?>)}else{this.bubbleshown=false;hidebubbleimmediate(i)}}<?
			} else {
				?>this.blur();showbubblenow(<?= $id ?>,event,<?= $__catcher ?>,<?
				echo $flags & CLICK_OPEN_CENTER ? (empty($this->offset) ? 'true' : $this->offset) : 'false';
				?>)<?
			}
			?>"<?
		}
		if ($movebubble) {
			?> onmouseover="showbubble(<?= $id; ?>,event,<?= $__catcher ?>)"<?
		}
		if (!($flags & CLICK_KEEP_BUBBLE)) {
			?> onmouseout="hidebubble(<?= $id; ?>)"<?
		}
		?>><?
		if (!empty($this->catcher)) {
			echo $this->catcher;
		}
		if ($help) {
			?><sup<?
			if ($flags & LIGHT_SUP) {
				?> class="light"<?
			}
			?>>?</sup><?
		}
		?></<?= $span ?>><?
	}

	function cleanup() {
		$this->state = 0;
		$this->bubbleid = 0;
		unset(	$this->catcher,
				$this->title,
				$this->content);
	}

	function display_and_cleanup() {
		$this->display();
		$this->cleanup();
	}
	static function show_bottom_content() {
		if (!bubble::$bubbles) {
			return;
		}
		foreach (bubble::$bubbles as $id => $content) {
			if ($content === true) {
				continue;
			}
			echo $content;
		}
	}
}

function simple_help($text,$help = null) {
	include_style('bubble');
	if (!$help) {
		?><span class="hlp" title="<?= $text ?>"><sup>?</sup></span><?
	} else {
		?><span class="hlp" title="<?= $help ?>"><?= $text ?><sup>?</sup></span><?
	}
}
