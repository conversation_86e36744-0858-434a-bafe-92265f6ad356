<?php

declare(strict_types=1);

function get_url_from_cacheid(string $dataid, ?string $urlid = null, ?string $crc = null): string|false {
	static $__url;
	if ($urlid === null) {
		if (preg_match('"^([\da-f]{8})([\da-f]{8})([\da-f]{8})(?:([\da-f]{2})([\da-f]{8}))?"', $dataid,$match)) {
			[, $dataid, $urlid, $crc] = $match;
		} else {
			return $__url[$dataid][$urlid][$crc] = false;
		}
	}
	return $__url[$dataid][$urlid][$crc] ??= db_single(['includecache', 'includecacheurl'], "
		SELECT URL
		FROM includecache
		JOIN includecacheurl USING (URLID)
		WHERE DATAID = 0x$dataid
		  AND CRC	 = 0x$crc
		  AND URLID	 = 0x$urlid"
	) ?: false;
}
function is_valid_cacheid(string $url, string $dataid, string $urlid, string $crc): bool {
	return (bool)db_single(['includecache', 'includecacheurl'], "
		SELECT b'1'
		FROM includecache
		JOIN includecacheurl USING (URLID)
		WHERE URL	 = '".addslashes($url)."'
		  AND DATAID = 0x$dataid
		  AND CRC	 = 0x$crc
		  AND URLID	 = 0x$urlid"
	);
}
