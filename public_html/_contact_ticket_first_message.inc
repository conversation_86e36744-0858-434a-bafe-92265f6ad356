<?php

define('PARTY_FROM_MESSAGE',		1);
define('PARTY_FROM_TICKET',		2);
define('PARTY_FROM_TICKET_HISTORY',	3);

function party_source_labels() {
	static $__labels = null;
	if ($__labels) {
		return $__labels;
	}
	return $__labels = array(
		PARTY_FROM_MESSAGE		=> __('party_source:message_LINE'),
		PARTY_FROM_TICKET		=> __('party_source:ticket_LINE'),
		PARTY_FROM_TICKET_HISTORY	=> __('party_source:ticket_history_LINE'),
	);
}
function contact_display_first_ticket_message($ticketid,$color = null) {
	$ticket = db_single_assoc(
		array('contact_ticket','contact_ticket_message','contact_ticket_mail'),'
		SELECT	TICKETID,contact_ticket_message.CSTAMP,BODY,contact_ticket.ISMAIL,contact_ticket_message.USERID_FROM,ELEMENT,ID,ORG_ELEMENT,ORG_ID,
			FROM_EMAIL,FROM_NAME,mail.SUBJECT,
			(SELECT GROUP_CONCAT(CONCAT(ASSOCTYPE,":",ASSOCID)) FROM connect WHERE MAINTYPE="contact_ticket" AND MAINID=TICKETID) AS CONNECTS
		FROM contact_ticket
		LEFT JOIN contact_ticket_message USING (TICKETID)
		LEFT JOIN contact_ticket_mail AS mail USING (TICKETID,CTMSGID)
		WHERE TICKETID='.$ticketid.'
		  AND DIRECTION="toadmin"
		ORDER BY CTMSGID ASC
		LIMIT 1'
	);
	if ($ticket === false) return false;
	if (!$ticket) {
		register_error('contact_ticket:error:nonexistent_LINE',array('ID'=>$ticketid));
		return false;
	}
	$bodies = db_simple_hash('contact_ticket_message','
		SELECT CTMSGID,BODY
		FROM contact_ticket_message
		WHERE TICKETID='.$ticketid.'
		ORDER BY CTMSGID DESC'
	);
	if ($bodies === false) return false;

	$tickets = db_rowuse_array('contact_ticket_log','
		SELECT DISTINCT ELEMENT,ID,ORG_ELEMENT,ORG_ID
		FROM contact_ticket_log
		WHERE TICKETID='.$ticketid.'
		  AND ELEMENT IN ("party","contest","camerarequest")
		  AND ID!=0
		ORDER BY MSTAMP DESC'
	);
	if ($tickets === false) {
		return false;
	}
	$tickets[] = $ticket;
	
	foreach ($tickets as $tmpticket) {
		if ($ticket['ELEMENT']
		&&	$ticket['ID']
		) {
			$elements[$ticket['ELEMENT']][] = $ticket['ID'];
		}
		if ($ticket['ORG_ELEMENT']
		&&	$ticket['ORG_ID']
		) {
			$elements[$ticket['ORG_ELEMENT']][] = $ticket['ORG_ID'];
		}
		if (!empty($ticket['CONNECTS'])) {
			foreach (explode(',',$ticket['CONNECTS']) as $connect) {
				list($element,$id) = explode(':',$connect);
				$elements[$element][] = $id;
			}
		}
	}
	
	layout_open_box($color ?: 'contact');
	?><div class="block"><?
	if (($id = $ticket['ID'])
	&&	($element = $ticket['ELEMENT'])
	||	($id = $ticket['ORG_ID'])
	&&	($element = $ticket['ORG_ELEMENT'])
	) {
		?><div class="r"><?
		if (($element == 'party')
		&&	($party = memcached_party_and_stamp($id))
		) {
			echo get_element_link($element, $id, $party['NAME']);
			?><br /><time class="light" datetime="<?= gmdate(ISO8601Z,$party['STAMP']) ?>"><?
			_datetime_display($party['STAMP']);
			?></time><?
		} elseif (isset(USE_URLTITLE[$element])) {
			echo get_element_link($element,$id);
		}
		?></div><?
	}
	?><b><?
	if ($ticket['USERID_FROM']) {
		?><img class="presence" src="<?= get_favicon() ?>" /> <?
		echo get_element_link('user',$ticket['USERID_FROM']);
	}
	if ($ticket['ISMAIL']) {
		if ($ticket['USERID_FROM']) {
			?><br /><?
		}
		print_email_link($ticket['FROM_EMAIL'],$ticket['FROM_NAME']);
		require_once '_ubb_preprocess.inc';
		$ticket['BODY'] = _ubb_preprocess($ticket['BODY']);
	}
	?></b><br /><?
	?><time class="small light" datetime="<?= gmdate(ISO8601Z,$ticket['CSTAMP']) ?>"><? _datedaytime_display($ticket['CSTAMP']); ?></time><br /><?
	echo make_all_html(mytrim($ticket['BODY']));

	?></div><?
	layout_close_box();
	$parties = null;
	$done = null;
	if (!empty($elements)) {
		foreach ($elements as $element => $ids) {
			foreach ($ids as $id) {
				if ($element == 'contest') {
					$contest = memcached_contest($id);
					if (!($id = getifset($contest,'PARTYID'))) {
						continue;
					}
				}
				if (isset($done[$id])) continue;
				$type = $element == $ticket['ELEMENT'] && $id == $ticket['ID'] ? PARTY_FROM_TICKET : PARTY_FROM_TICKET_HISTORY;
				$parties[$type][$id] = true;
				$done[$id] = true;
			}
		}
	}
	foreach ($bodies as $body) {
		if (preg_match_all('"(?:/party/|party=)(\d+)\b"',$ticket['BODY'],$matcheslist)) {
			foreach ($matcheslist[1] as $partyid) {
				if (isset($done[$partyid])) {
					continue;
				}
				$parties[PARTY_FROM_MESSAGE][$partyid] = true;
				$done[$partyid] = true;
			}
		}
	}
	if ($parties) {
		foreach ($parties as $type => &$partylist) {
			foreach ($partylist as $partyid => &$party) {
				$party = memcached_party_and_stamp($partyid);
				if (!$party) {
					unset($partylist[$partyid]);
					continue;
				}
				$party['PARTYID'] = $partyid;
			}
			unset($party);
		}
		unset($partylist);
	}
	return array($ticket,$parties);
}
