<?php

require_once 'defines/offense.inc';
require_once '_currentuser.inc';

function create_banpoints($offenderid,$offenseid = 0) {
	$status = db_single('user_account','SELECT STATUS FROM user_account WHERE USERID='.$offenderid,DB_USE_MASTER);
	if ($status === false) {
		return false;
	}
	$banpoint = db_single_assoc('banpoint','
		SELECT OFFENSEID,DONEPTS,EXPIRES,BANTIME
		FROM banpoint
		WHERE USERID='.$offenderid.'
		  AND OFFENSEID<'.$offenseid.'
		ORDER BY OFFENSEID DESC
		LIMIT 1',
		DB_USE_MASTER
	);
	if ($banpoint === false) {
		return false;
	}
	$offenses = db_rowuse_array('offense','
		SELECT OFFENSEID,CSTAMP,POINTS,USERID
		FROM offense
		WHERE REGONLY=0
		  AND (POINTS>0 OR ISNULL(POINTS))
		  AND REMOVED=0
		  AND USERID='.$offenderid.'
		  AND OFFENSEID>='.($banpoint ? $banpoint['OFFENSEID'] : 0).'
		ORDER BY OFFENSEID ASC',
		DB_USE_MASTER
	);
	if ($offenses === false) {
		return false;
	}
	if ($offenses) {
		$curroffenseid = 0;
		foreach ($offenses as $offense) {
			extract(get_offense_defines($offense['CSTAMP']));
			if ($offense['POINTS'] === null) {
				$permbanned = true;
				if (!db_insert('banpoint','
					INSERT IGNORE INTO banpoint SET
						CSTAMP		='.$offense['CSTAMP'].',
						USERID		='.$offenderid.',
						OFFENSEID	='.$offense['OFFENSEID'].',
						EXPIRES		='.(CURRENTSTAMP + $OFFENSE_PERMBAN_EXPIRES))
				) {
					return false;
				}
				continue;
			}
			if (!$curroffenseid) {
				$notdonepts = $offense['POINTS'];
				if ($banpoint) {
					$notdonepts -= $banpoint['DONEPTS'];
					$prevbanexpires = $banpoint['EXPIRES'];
					$prevoffenseban = $banpoint['OFFENSEID'];
				} else {
					$prevbanexpires = $prevoffenseban = 0;
				}
			}
			$curroffenseid = $offense['OFFENSEID'];

			$notdonepts += $offense['POINTS'];
			$pts = db_single_assoc('offense','
				SELECT	COALESCE(SUM(POINTS),0) AS TOTAL,
					COALESCE(SUM(IF(CSTAMP>='.($offense['CSTAMP'] - $OFFENSE_ACTIVE_PERIOD).',POINTS,0)),0) AS ACTIVE,
					COALESCE(SUM(IF(CSTAMP>='.($offense['CSTAMP'] - $OFFENSE_ACTIVE_PERIOD).'
						AND OFFENSEID>='.$prevoffenseban.', POINTS-IF(ISNULL(DONEPTS),0,DONEPTS),0)),0) AS NOTDONE
				FROM offense
				LEFT JOIN banpoint USING (OFFENSEID,CSTAMP,USERID)
				WHERE REMOVED=0
				  AND REGONLY=0
				  AND NOT ISNULL(POINTS)
				  AND USERID='.$offenderid.'
				  AND OFFENSEID<='.$curroffenseid,
				DB_USE_MASTER
			);
			if ($pts === false) {
				return false;
			}
	#		echo $pts['NOTDONE'].' == '.$notdonepts,"\n";
			$notdonepts = $pts['NOTDONE'];
	#		print_rr($pts,'pts');
			if ($notdonepts >= $OFFENSE_BAN_EVERY_POINTS) {
				$borders = floor($notdonepts / $OFFENSE_BAN_EVERY_POINTS);
				$donepts = $borders * $OFFENSE_BAN_EVERY_POINTS;
				$bantime = $OFFENSE_POINTS_TO_BANTIME * $OFFENSE_BAN_EVERY_POINTS * floor($pts['ACTIVE'] / $OFFENSE_BAN_EVERY_POINTS);
				$delta = $bantime;

	#			print_rr($borders,'borders');
	#			print_rr($donepts,'donepts');
	#			print_rr($bantime,'bantime');

				if ($prevbanexpires) {
					# if we have active ban, increase bantime of new ban with the remainder of
					# the previous one (e.g., ban of 4 days, 3 done, then new banpoint will get 1 extra day
					$remaining = $prevbanexpires - $offense['CSTAMP'];
	#				print_rr($remaining,'remaining');
					if ($remaining > 0) {
						$bantime += $remaining;
					}
				}

				if (!db_insert('banpoint','
					INSERT INTO banpoint SET
						CSTAMP		='.$offense['CSTAMP'].',
						EXPIRES		='.($offense['CSTAMP'] + $bantime).',
						USERID		='.$offenderid.',
						OFFENSEID	='.$curroffenseid.',
						DELTA		='.$delta.',
						BANTIME		='.$bantime.',
						DONEPTS		='.($offense['POINTS'] - ($notdonepts - $donepts)))
				) {
					return false;
				}
				$prevbanexpires = $offense['CSTAMP'] + $bantime;
				$prevoffenseban = $curroffenseid;
				$notdonepts -= $donepts;
			}
		}
	}
	if (isset($permbanned)) {
		$newstatus = 'permbanned';
	} elseif (!empty($prevbanexpires) && $prevbanexpires > CURRENTSTAMP) {
		$newstatus = 'banned';
	} else {
		# restore old status
		if ($status != 'banned'
		&&	$status != 'permbanned'
		) {
			# if current status is not banned or permbanned, just keep current status
			$newstatus = $status;
		} else {
			# current status is banned or permbanned, but user should not be
			# restore last status in statuschange
			$laststatus = db_single('userstatuschange','
				SELECT STATUS
				FROM userstatuschange
				WHERE STATUS NOT IN ("banned","permbanned")
				  AND USERID='.$offenderid.'
				ORDER BY STAMP DESC
				LIMIT 1',
				DB_USE_MASTER
			);
			if ($laststatus === false) {
				return false;
			}
			$newstatus = $laststatus ? $laststatus : 'inactive';
		}
	}
	if ($newstatus !== $status) {
		if (!change_user_status($offenderid,$newstatus,CURRENTUSERID)) {
			return false;
		}
		register_notice('user:notice:newstatus_'.$newstatus.'_LINE', ['STATUS' => __('status:'.$newstatus), DONT_ESCAPE]);
	}
	return true;
}
function revoke_banpoints(int $offenderid, int $offenseid = 0): bool {
	return	db_insert('banpoint_revoked','
		INSERT INTO banpoint_revoked
		SELECT banpoint.*,'.$offenseid.','.CURRENTSTAMP.'
		FROM banpoint
		WHERE USERID='.$offenderid)
	&&	db_delete('banpoint','
		DELETE FROM banpoint
		WHERE USERID='.$offenderid.'
		  AND OFFENSEID>='.$offenseid);
}
