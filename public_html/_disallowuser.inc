<?php

# move disallowuser_zerohits to disallowuser_log when no real problems arise

const AGENDA_SECTIONS = [
	'agenda'		=> true,
	'artist'		=> true,
	'boarding'		=> true,
	'city'			=> true,
	'date'			=> true,
	'event'			=> true,
	'location'		=> true,
	'organization'	=> true,
	'party'			=> true,
	'provice'		=> true,
];

require_once 'defines/disallow.inc';
require_once '_memcache.inc';
require_once '_getcurrentaddr.inc';
require_once '_server_range_detect.inc';

# FIXME: There are more and more robots on this list.
#		 It's probably faster to find if there is a disallow to check for that specific user,
#		 cache that, and not get the entire list for every user

function disallow_user(string|bool|null $ipbin = null, ?array &$return_info = null): int|false {
	static $__disallows;

	if ($ipbin === true		# refresh cache
	||	!isset($__disallows)
	) {
		if (false === ($__disallows = memcached_simple_hash('disallowuser','
			SELECT	IF(PREFIX = 0xFFFF, 4, 6),
					MASKSIZE,
					IF(	PREFIX = 0xFFFF,
						(0xFFFFFFFF << (32 - MASKSIZE)) & 0xFFFFFFFF,
						HEX(PREFIX)
					),
					IF(	PREFIX = 0xFFFF,
						IIDENT,
						HEX(IIDENT)
					),
					0 + DISALLOW
			FROM disallowuser
			ORDER BY
				PREFIX = 0xFFFF DESC,
				MASKSIZE DESC',
			ONE_DAY,
			DISALLOW_CACHE_KEY,
			DB_NON_ASSOC
		))) {
			return false;
		}
		if ($ipbin === true) {
			# only refreshing cache
			return 0;
		}
	}
	$proto = ipbin_is_ipv4($ipbin) ? 4 : 6;
	if (!isset($__disallows[$proto])) {
		return DISALLOW_NO;
	}
	if ($proto === 4) {
		$ipnum = ipbin_to_ipnum($ipbin);
		foreach ($__disallows[$proto] as $mask_size => $masks) {
			foreach ($masks as $mask => $infos) {
				$masked_ipnum = $ipnum & $mask;
				foreach ($infos as $bad_ipnum => $disallow) {
					if ($masked_ipnum === $bad_ipnum) {
						if (is_array($return_info)) {
							if ($netstr = $mask_size !== 32) {
								$filled = $mask_size / 8;
								foreach (explode('.', long2ip($bad_ipnum)) as $part) {
									if ($filled) {
										$parts[] = $part;
										--$filled;
									} else {
										break;
									}
								}
								$netstr = implode('.', $parts).'.';
							}
							$return_info = [$disallow, CURRENTSTAMP, $mask_size, 'FFFF', strtoupper(dechex($bad_ipnum)), $netstr];
						}
						return $disallow;
					}
				}
			}
		}
	} else {
		[$prefix, $iident] = ipbin_to_prefix_and_iident($ipbin, IP_PAD);
		$total = $prefix.$iident;
		foreach ($__disallows[$proto] as $mask_size => $prefixes) {
			$chars = $mask_size / 4;
			foreach ($prefixes as $dis_prefix => $iidents) {
				foreach ($iidents as $dis_iident => $disallow) {
					$dis_total =
						str_pad($dis_prefix, 16, '0', STR_PAD_LEFT).
						str_pad($dis_iident, 16, '0', STR_PAD_LEFT);

					if (0 === ($rc = strncmp($total, $dis_total, $chars))) {
						if (is_array($return_info)) {
							# disallow:		see defines/disallow.inc
							# CURRENTSTAMP:	stamp when this disallow was determined (so we can say we only want cached items newer than last cache flush
							# mask size,
							# dis_prefix,
							# dis_iddent:	specs of the disallow entry, so we can update the counter using this unfi
							# dis_total:	net/hostname of the ip/mask

							$return_info = [$disallow, CURRENTSTAMP, $mask_size, $dis_prefix, $dis_iident, inet_ntop(hex2bin($dis_total))];
						}
						return $disallow;
					}
				}
			}
		}
	}
	return DISALLOW_NO;
}

function is_disallowed(?string $ipbin = null): false|int {
	# NOTE:	ipbin === null				=> use CURRENTIPBIN and increase last_use
	#		ipbin === non-empty string	=> only getter

	if ($ipbin) {
		static $__disallow;
		return $__disallow[$ipbin] ??= disallow_user($ipbin);
	}
	# This is the primary routine for every page for every user

	static $__current_disallow;
	if (isset($__current_disallow)) {
		return $__current_disallow;
	}
	getcurrentaddr();
	$no_cache = isset($_REQUEST['NOMEMCACHE']);
	$disallow_key = DISALLOW_SINGLE_KEY(CURRENTIPSTR);
	if (($info = $no_cache ? false : memcached_get($disallow_key))
	&&	!memcached_not_found()
	) {
		return $__current_disallow = false;
	}
	if ($info) {
		[$disallow, $stamp] = $info;
		# If no minimum is stored yet, cache is fresh.
		# If there is a minimum, the stamp of the info must be equal or greater.
		if (!($minimum_stamp = $no_cache ? false : memcached_get(DISALLOW_MINIMUM_STAMP_KEY))
		||	$stamp >= $minimum_stamp
		) {
			if ($disallow) {
				[,, $mask_size, $prefix, $iident] = $info;
				increase_disallow_usage($prefix, $iident, $mask_size);
			}
			return $__current_disallow = $disallow;
		}
	}
	$info = [];
	if (false === ($disallow = disallow_user(CURRENTIPBIN, $info))) {
		return $__current_disallow = false;
	}
	memcached_set($disallow_key, $disallow ? $info : [DISALLOW_NO, CURRENTSTAMP], FIVE_MINUTES);

	if ($disallow && $info) {
		# execute only once per page, __current_disallow will be set afterwards and we don't get here
		[,, $mask_size, $prefix, $iident] = $info;
		increase_disallow_usage($prefix, $iident, $mask_size);
	}
	return $__current_disallow = $disallow;
}

function increase_disallow_usage(string $prefix, string $iident, int $masksize): bool {
	return	db_update('disallowuser','
			UPDATE disallowuser SET
				LSTAMP	= '.CURRENTSTAMP.",
				HITS	= HITS + 1
			WHERE PREFIX = 0x$prefix
			  AND IIDENT = 0x$iident
			  AND MASKSIZE = $masksize");
}

function user_creation_disallowed(?string $ipbin = null): bool {
	if (FORCE_ALLOW_USER_CREATION) {
		return false;
	}
	return match (is_disallowed($ipbin)) {
		DISALLOW_TOR,
		DISALLOW_PROXY,
		DISALLOW_LOGIN,
		DISALLOW_CREATION	=> true,
		# DISALLOW_HARVEST,
		default				=> false,
	};
}

function login_disallowed(?string $ipbin = null): bool {
	return is_disallowed($ipbin) === DISALLOW_LOGIN;
}

function require_creation_allowance(): bool {
	if (user_creation_disallowed()) {
		register_error('disallowuser:error:no_creation_LINE');
		register_error('disallowuser:error:is_incorrect_LINE',DO_UBB);
		return false;
	}
	return true;
}

function require_login_allowance(): bool {
	if (is_disallowed() === DISALLOW_LOGIN) {
		register_error('disallowuser:error:no_login_LINE');
		register_error('disallowuser:error:is_incorrect_LINE', DO_UBB);
		return false;
	}
	return true;
}

function flush_disallows(?string $ipstr = null): void {
	memcached_delete(DISALLOW_CACHE_KEY);
	memcached_set(DISALLOW_MINIMUM_STAMP_KEY, CURRENTSTAMP, ONE_WEEK);
	if ($ipstr) {
		memcached_delete(DISALLOW_SINGLE_KEY($ipstr));
	}
	disallow_user(true);
}

/*function get_blockey(int $userid, string $ipstr): string {
	return 'agendav3block:'.$userid.':'.$ipstr;
}

function agenda_disallowed(string $element, int $id, array $args = []): ?array {
	if (DISALLOW_ROBOTS
	||	ALLOW_ROBOTS_AGENDA
	||	!isset(AGENDA_SECTIONS[$element])
	) {
		return null;
	}

	############################ args:
	$cstamp	= CURRENTSTAMP;
	$userid	= CURRENTUSERID;
	$ipstr	= CURRENTIPSTR;
	$identid= defined('CURRENTIDENTID') ? CURRENTIDENTID : 0;

	if (empty($_SERVER['appic'])) {
		require_once '_browser.inc';
		$request_uri	= $_SERVER['REQUEST_URI'];
		$user_agent		= getifset($_SERVER,'HTTP_USER_AGENT');
		$image			= str_starts_with($request_uri,'/images');
		$ipbin			= CURRENTIPBIN;
		$browserid		= get_browserid();
		$platform 		= 'partyflock';
	} else {
		$request_uri	=
		$user_agent 	=
		$image			=
		$ipbin			= null;
		$browserid		= 0;
		$platform		= 'appic';
	}
	extract($args);

	if ($element !== 'agenda' && !$id) {
		return null;
	}

	$ipv6 = str_contains($ipstr, ':');

	$ipnum = $ipv6 ? null : ip2long($ipstr);

	if (is_allowed_robot($ipv6, $ipnum, $ipstr) === true) {
		return null;
	}

	static $__allowed = 0;
	$__allowed ??= memcached_boolean_hash('agenda_allowed','SELECT IPBIN FROM agenda_allowed', TEN_MINUTES, 'agenda_alloweds');

	if (isset($__allowed[$ipbin])) {
		return null;
	}

	if (BLOCK_ONLY_NEW_ITEMS) {
		switch ($element) {
		case 'event':
			# though a MAIN_ID, this is always a valid PARTYID
			$partyid = db_single('party','
				SELECT PARTYID
				FROM appic_event
				WHERE APPICID = '.$id
			);
		case 'party':
			if (!isset($partyid)) {
				$partyid = $id;
			}
			if (($party = memcached_party_and_stamp($partyid))
			&&	(	$party['STAMP'] > $cstamp
				||	$party['STAMP'] + $party['DURATION_SECS'] > $cstamp
				)
			) {
				$log = true;
			}
			break;

		case 'agenda':
			$log = true;
			break;

		case 'date':
			if (!preg_match('"^(\d{4})(\d{2})(\d{2})$"', $id)) {
				return null;
			}
			[$year, $month, $data] = _getdate();
			if ($id >= $year * 10000 + $month * 100 + $day) {
				$log = true;
			}
			break;

		case 'artist':
		case 'boarding':
		case 'city':
		case 'location':
		case 'organization':
		case 'province':
			require_once '_lastparty.inc';
			ob_start();
			$lastparty = show_last_party($element,$id);
			ob_end_clean();
			if ($lastparty
			&&	$lastparty['STAMP_TZI'] > $cstamp - 3 * ONE_HOUR
			) {
				$log = true;
			}
			break;
		}
		if (!isset($log)) {
			return null;
		}
	}

	$disallow = is_disallowed();

	if ($disallow === DISALLOW_TOR
	||	$disallow === DISALLOW_PROXY
	||	$disallow === DISALLOW_HARVEST
	) {
		return [DISALLOW_HARVEST, 'manual-permanent', 0];
	}

	$ipbinstr = addslashes($ipbin);

	$server_range = is_server_ip($ipnum, $ipstr);

	db_insert('agenda_log', "
	INSERT INTO agenda_log SET
		PLATFORM	= '$platform.',
		ELEMENT		= '$element',
		ID			= $id,
		IMAGE		= b'".($image ? '1' : '0')."',
		BLOCKED		= b'".(BLOCK_AGENDA_MISUSE && $server_range ? '1' : '0')."',
		URI			= '".addslashes($request_uri)."',
		BROWSERID	= $browserid,
		IPBIN		= '$ipbinstr',
		IDENTID		= $identid,
		USERID		= $userid,
		STAMP		= $cstamp"
	);

	$blockey = get_blockey($userid, $ipstr);

	$block_info = memcached_get($blockey);

	if (!$block_info) {
		if ($server_range) {
			$block_info = [$cstamp, ONE_DAY];
		} else {
			$block_start = 0;

			if (false === ($reqs = db_simpler_array('agenda_log', "
				SELECT STAMP
				FROM agenda_log
				WHERE STAMP BETWEEN ".($cstamp - ONE_DAY).' AND '.($cstamp - 1)."
				  AND USERID = $userid
				  AND IPBIN = '$ipbinstr'
				GROUP BY URI
				ORDER BY STAMP DESC"
			))) {
				# cannot determine anything, so disallowd = false
				return null;
			}

			$cnt = count($reqs);
			if ($cnt > 1000) {
				$block_start = $cstamp;
				$block_duration = ONE_DAY;
				$block_desc = 'More than 1000 impressions.';
			} elseif ($cnt > 500) {
				$block_start = $cstamp;
				$block_duration = 4 * ONE_HOUR;
				$block_desc = 'More than 500 impressions.';
			} elseif (
				$cnt > 200
			&&	!$identid
			) {
				$block_start = $cstamp;
				$block_duration = ONE_HOUR;
				$block_desc = 'More than 200 impressions without IDENTID.';
			} elseif (
				$too_many = db_single('agenda_log','
					SELECT	COUNT(IF(URI LIKE "%/artist/%" OR ELEMENT="artist",1,NULL)) AS ARTIST_CNT,
							COUNT(IF(URI LIKE "%/location/%" OR ELEMENT="location",1,NULL)) AS LOCATION_CNT,
							COUNT(IF(URI LIKE "%/organization/%" OR ELEMENT="organization",1,NULL)) AS ORGANIZATION_CNT
					FROM agenda_log
					WHERE STAMP BETWEEN '.($cstamp-4*3600).' AND '.($cstamp - 1).'
					  AND USERID='.$userid.'
					  AND IPBIN="'.$ipbinstr.'"
					  AND (	URI LIKE "%/artist/%"
					  OR	URI LIKE "%/location/%"
					  OR	URI LIKE "%/organization/%"
					  OR	ELEMENT IN ("artist","location","organization")
					  )
					HAVING ARTIST_CNT		>=100
						OR LOCATION_CNT		>=100
						OR ORGANIZATION_CNT	>=100'
				)
			) {
				$block_start = $cstamp;
				$block_duration = 4 * ONE_HOUR;
				$block_desc = 'More than 100 imressions on artist, location or organization.';
			} else {
				$imps = 1;
				foreach ($reqs as $stamp) {
					++$imps;

					$have_time = $cstamp - $stamp;

					if ($identid) {
						if ($imps < 30) {
							# first 30 or 10 is ok, for bursts
							continue;
						}
						# MAX_IMPS * SQRT( current_time ) / SQRT( ATTIME )
						$allow_imps = 400 * sqrt($have_time) / sqrt(2 * ONE_HOUR);
					} else {
						if ($imps < 20) {
							# first 20 is ok
							continue;
						}
						$allow_imps = 100 * sqrt($have_time) / sqrt(2 * ONE_HOUR);
					}

					# 100 imps => 10 mins
					# 200 imps => 60 mins
					# 400 imps => 360 mins
					# https://www.google.com/search?hl=en&source=hp&q=google+math&btnG=Google+Search&aq=f&aqi=&oq=#hl=en&q=y%3D10*6%5E(x%2F200)

					if ($allow_imps < $imps) {
						$block_start = $cstamp;
						$block_duration = floor(TEN_MINUTES * pow(6, $imps / 200 ));
						$block_desc = "Too many impressions, impressions served: $imps, impressions allowed: $allow_imps.";
						break;
					}
				}
			}

			if ($block_start) {
				$block_info = [$block_start, $block_duration];

				memcached_set($blockey, $block_info, $block_duration);

				$block_type = $server_range ? 'server-range' : 'much-use';

				db_getlock($lkey = 'lockins_'.$ipstr);
				if (!db_single('agenda_check', "
					SELECT 1
					FROM agenda_check
					WHERE DONE = 0
					  AND USERID = $userid
					  AND IPBIN = '$ipbinstr'")
				) {
					db_insert('agenda_check', "
					INSERT INTO agenda_check SET
						IPBIN		= '$ipbinstr'
						USERID		= $userid,
						IDENTID		= $identid,
						BROWSERID	= $browserid,
						STAMP		= $cstamp"
					);
				}
				db_releaselock($lkey);
			}
		}
	}
	if (BLOCK_AGENDA_MISUSE
	&&	$block_info
	) {
		if ($server_range
			# ^^ block server
		||	$userid == 1
		&&	(	!$user_agent
			||	!is_robot_hostname($user_agent)
			)
			# or non-user with no user-agent or user-agent is not a valid bot hostname
		) {
			list($block_start, $block_duration) = $block_info;

			$after = $block_duration - ($cstamp - $block_start);

			db_update('agenda_log', "
			UPDATE agenda_log SET BLOCKED = 1
			WHERE IPBIN		= '$ipbinstr'
			  AND URI		= '".addslashes($request_uri)."'
			  AND BROWSERID	= $browserid
			  AND IDENTID	= $identid
			  AND USERID	= $userid
			  AND STAMP		= $cstamp"
			);

			$block_type = $server_range ? 'server-range' : 'much-use';

			return [DISALLOW_HARVEST, $block_type, $block_start + $block_duration];
		}
	}
	return null;
}*/
