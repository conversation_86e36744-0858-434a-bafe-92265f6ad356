<?php

declare(strict_types=1);

function _profileimage_get(int|array $user): string {
	require_once '_hosts.inc';
	static $__cached = [];
	static $__show = 0;
	if ($__show === 0) {
		require_once '_settings.inc';
		$__show = setting('SHOW_PROFILE_IMAGES');
	}
	if (!$user || !$__show) {
		return '';
	}
	$userid = is_int($user) ? $user : $user['USERID'];
	if (isset($__cached[$userid])) {
		return $__cached[$userid];
	}
	if (!($user_image = get_user_image($userid))) {
		return $__cached[$userid] = '';
	}
	assert(is_array($user_image)); # Satisfy EA inspection
	if (CURRENTUSERID === $userid
	||	!invisible_profile($userid)
	&&	$user_image['ACCEPTED']
	||	have_admin('helpdesk')
	) {
		$x2 = $user_image['LARGE'] > 1 ? is_high_res() : null;
		$p2 = $x2 ? 2 : 1;
		ob_start();
		?><img<?
		?> class="profile-image middle<?
		if (!$user_image['ACCEPTED']) {
			?> unaccepted-element<?
		}
		?>"<?
		?> style="width: <?= $user_image['WIDTH'] / $p2 ?>px; height: <?= $user_image['HEIGHT'] / $p2 ?>px;"<?
		?> title="<?= escape_specials(memcached_nick($userid)) ?: '???' ?>"<?
		?> src="<?= IMAGES_HOST ?>/images/user/<?= $userid ?>_<?= $user_image['DATAID'] ?>.<?= $user_image['TYPE'] ?>"<?
		?> /><?
		return $__cached[$userid] = ob_get_clean();
	}
	return $__cached[$userid] = '';
}

function get_user_image(int $userid, bool $large = false): array|false|null {
	static $__user_image = [];
	return $__user_image[$userid][$large] ??= memcached_single_assoc_if_not_self($userid, ['userimage', 'userimagedatameta'], /** @lang MariaDB */ "
		SELECT DATAID, LARGE, TYPE, WIDTH, HEIGHT, ACCEPTED
		FROM userimage
		JOIN userimagedatameta USING (DATAID)
		WHERE USERID = $userid
		  AND LARGE IN (".($large ? '1' : '0').(is_high_res() ? ', '.($large + 2) : '').')
		ORDER BY LARGE DESC
		LIMIT 1',
		TEN_MINUTES,
		'userimage:'.($large ? 'large' : 'small').':'.(is_high_res() ? 'hi' : 'lo').':'.$userid);
}

function flush_userimage(int $userid, int $large): void {
	memcached_delete('userimage:'.($large ? 'large' : 'small').':lo:'.$userid);
	memcached_delete('userimage:'.($large ? 'large' : 'small').':hi:'.$userid);
}

function show_user_infos_below_thumb(int $userid, ?string $status = null): void {
	if (ROBOT) {
		return;
	}
	if (in_array($status, ['banned', 'permbanned'], strict: true)) {
		require_once '_status.inc';
		?><div class="small banned">(<?= status_name($status) ?>)</div><?
	}
	require_once '_donators.inc';
	if ($roles = get_donation_roles($userid)) {
		?><div class="small light"><?= implode(', ',$roles) ?></div><?
	}
}
