<?php

declare(strict_types=1);

const NO_ALTER_ATTACHMENT		= false;
const NO_ALTER_ALBUMELEMENT		= false;
const NO_ALTER_UPLOADIMAGE		= false;
const NO_ALTER_USERIMAGE		= false;
const NO_ALTER_DUMP				= false;

# FIXME: We should store master information in database_spec.txt
# WARNING: There are two  other places where master needs to be changed:
#		   - party/config/mail/flockproc
#		   - party/config/photo-uploadserver/photo-process-daemon

const CURRENT_MASTER_party_db	= 'dbu';	// NOSONAR
const CURRENT_MASTER_counter_db	= 'dbblack';// NOSONAR
# For data_Db and albums, master is currently always the same.
const CURRENT_MASTER_data_db	= 'ndbd3';	// NOSONAR
# const CURRENT_MASTER_albums	= 'ndbd3';	// NOSONAR

# define this in your script to start timing:
# DB_TIMING	bool false		don't log anything
#			bool true		log all queries
#		string dbname	log only queries directed at this db
const DB_TIMING					= false;

const WAIT_FOR_SLAVE_TIMEOUT	= 10;	# getting data out of non local alternative is worse than waiting 10 seconds


const DATABASE_SPEC			= __DIR__.'/../../config/webserver/database_spec.txt';
const DB_SLOW_QUERY_FROM	= 2;	# queries are slow starting from this many seconds

# WARNING: https://www.php.net/manual/en/mysqli.persistconns.php
# That.,.. does not work, it does NOT release GET_LOCKs at least, so persistence is futile!
const DB_PERSISTENT_CONNECTIONS		= false;
const DB_MAX_CONNECTION_FAILURES	= 10;	// remove db from active pool after this many failures
const DB_EXPIRE_CONNECTION_FAILURES	= 60;	// retry connecting after this period
const SLAVE_MAX_BEHIND				= 10;	// seconds slave may be behind
# const SLAVE_EXPIRE_BEHIND			= 60;	// time to keep behind status
const SLAVE_MAX_BEHIND_FAILURES		= 20;	// failures after which slave is not tried again
const SLAVE_EXPIRE_BEHIND_FAILURES	= 20;	// time to keep ok/bad status of slave

const SLAVE_OK		= 1;
const SLAVE_BAD		= 2;

# const RETRY_TIME_SLAVE	= 60;

# Find many error codes here: https://mysqlconnector.net/api/mysqlconnector/mysqlerrorcodetype/

# const MYSQL_OK							 	= 0;
const MYSQL_ER_STORAGE_ENGINE					= 1030;
# const MYSQL_ER_NOT_KEYFILE					= 1034;
# const MYSQL_ER_CONST_COUNT_ERROR				= 1040;
const MYSQL_ER_BAD_DB_ERROR						= 1049;
# const MYSQL_ER_TABLE_EXISTS					= 1050;
const MYSQL_ER_BAD_TABLE_ERROR					= 1051;
const MYSQL_ER_SERVER_SHUTDOWN					= 1053;
const MYSQL_ER_DUP_ENTRY						= 1062;
const MYSQL_ER_PARSE_ERROR						= 1064;
const MYSQL_ER_NORMAL_SHUTDOWN					= 1077;
const MYSQL_ER_GOT_SIGNAL						= 1078;
const MYSQL_ER_SHUTDOWN_COMPLETE				= 1079;
const MYSQL_ER_FORCING_CLOSE					= 1080;

const MYSQL_ER_IPSOCK_ERROR						= 1081;
const MYSQL_ER_CRASHED_ON_USAGE					= 1194;
# const MYSQL_ER_LOCK_DEADLOCK					= 1213;
const MYSQL_WARN_DATA_TRUNCATED					= 1265;
const MYSQL_ER_WARN_DEPRECATED_SYNTAX		 	= 1287;
const MYSQL_ER_TRUNCATED_WRONG_VALUE			= 1292;
const MYSQL_ER_QUERY_INTERRUPTED				= 1317;
const MYSQL_ER_TRUNCATED_WRONG_VALUE_FOR_FIELD	= 1366;
# const MYSQL_ER_BINLOG_UNSAFE_STATEMENT		= 1592;
const MYSQL_CR_CONNECTION_KILLED				= 1927;
const MYSQL_CR_UNKNOWN_ERROR					= 2000;
const MYSQL_CR_CONNECTION_ERROR					= 2002;
const MYSQL_CR_CONN_HOST_ERROR					= 2003;  // happens on shutdown
const MYSQL_CR_IPSOCK_ERROR						= 2004;
const MYSQL_CR_UNKNOWN_HOST						= 2005;
const MYSQL_CR_GONE_AWAY						= 2006;
const MYSQL_CR_VERSION_ERROR					= 2007;
const MYSQL_CR_OUT_OF_MEMORY					= 2008;
const MYSQL_CR_WRONG_HOST_INFO					= 2009;
const MYSQL_CR_TCP_CONNECTION					= 2011;
const MYSQL_CR_SERVER_HANDSHAKE_ERR				= 2012;
const MYSQL_CR_SERVER_LOST						= 2013;
const MYSQL_CR_COMMANDS_OUT_OF_SYNC				= 2014;
# const MYSQL_MALFORMED_PKG						= 2027;
const MYSQL_CR_SERVER_LOST_EXTENDED				= 2055;

// WRITE flags
const DB_IGNORE_TRUNCATED	= 0x1;	# Ignore MYSQL_ER_TRUNCATED_WRONG_VALUE_FOR_FIELD (query still fails)
const DB_DONT_WARN_DUP_ENTRY= 0x2;
const DB_KEEP_CLEAN			= 0x4;
# READ
const DB_USE_MASTER			= 0x8;
const DB_FORCE_SERVER		= 0x10;
const DB_STORE_RESULT		= 0x20;
const DB_NON_ASSOC			= 0x40;
const DB_OK_SLAVES_ONLY		= 0x80;
const DB_FRESHEN_MEMCACHE	= 0x100;
const SPHINX_META   		= 0x200;
const DB_UTC				= 0x400;
const DB_FORCE_MASTER		= 0x800;
const DB_PREFER_SERVER		= 0x1000;
const DB_STORE_INDEX		= 0x2000;	# store index prefixed with underscore without the underscore
#							= 0x4000;
const DB_ASSOC_AND_ARRAY	= 0x8000;
const DB_NO_FLOCKBOT		= 0x20000;
# BOTH
const DB_NO_WARNINGS		= 0x40000;
const DB_USE_SYSLOG			= 0x80000;
const DB_DONT_FRESHEN		= 0x100000;	# If set, don't honor any setting that asks to freshen

# The numbers for the database read & write operations can be renamed at will.
# They are not stored in the database or somewhere else, just used 'live' to perform the correct operations.

# Database write operations
const INSERT					= 1;
const UPDATE					= 2;
const REPLACE					= 3;
const DELETE					= 4;
const TRUNCATE					= 5;
const INSUPD					= 6;
const CREATE					= 7;
const DROP						= 8;
const ALTER						= 9;
const DBSET						= 10;

# Database read operations
const SELECT					= 11;
const SINGLE					= 12;
const SINGLE_ASSOC				= 13;
const SIMPLE_ARRAY				= 14;
const BOOLEAN_HASH				= 15;
const ARRAY_USE_HASH			= 16;
const ROW_USE_HASH				= 17;
const ROW_USE_ARRAY		 		= 18;
const MULTI_ROW_USE_HASH		= 19;
const MULTI_ROW_HASH			= 20;
const SIMPLE_HASH				= 21;
const SINGLE_ARRAY				= 22;
const SAME_HASH					= 23;
const MULTI_ROW_USE_INDEX_HASH	= 24;
const ROW_USE_MULTI_HASH		= 25;
const DB_SHOW					= 26;

class attrib_spec {
	public string	    		$type;
	public string   			$key;
	public string|int|bool|null $default;
	public string		 		$extra;

	public int		    		$maxlength;
	public int					$min;
	public int|string			$max;
	public bool		 		    $null;
	public array				$enum;
	public array				$set;
}
