<?php

# https://console.cloud.google.com/apis/dashboard?project=partyflock-378410
# second key has more quote at the moment
#const YOUTUBE_V3_KEY	= 'AIzaSyBD9BqzxgzCZMECVIInb3imhvYYc-pL40Q';
const YOUTUBE_V3_KEY	= 'AIzaSyBrUW7vjJVV4FZnhceVeK-xb1dU4jMphmI';

function youtube_video_non_playable(object $video): array|false {
	if (property_exists($video, 'contentDetails')
	&&	property_exists($video->contentDetails, 'regionRestriction')
	&&	(	property_exists($video->contentDetails->regionRestriction, 'blocked')
		&&	in_array('NL', $video->contentDetails->regionRestriction->blocked)
		||	property_exists($video->contentDetails->regionRestriction, 'allowed')
		&&	!in_array('NL', $video->contentDetails->regionRestriction->allowed)
		)
	) {
		return ['broken', 'region_restricted'];
	}
	if (property_exists($video, 'status')
	&&	property_exists($video->status, 'privacy')
	&&	$video->status->privacy === 'private'
	) {
		return ['hidden', 'private'];
	}
	return false;
}

function youtube_video_non_embeddable(object $video): array|false {
	if (property_exists($video, 'status')
	&&	property_exists($video->status, 'embeddable')
	&&	!$video->status->embeddable
	) {
		return ['broken', 'non_embeddable'];
	}
	return false;
}

function youtube_match_video_url(string $url, ?array &$match = null, bool $utf8 = true): bool {
	# returns youtubeid in the match parameter
	$utf8_mod = $utf8 ? 'u' : '';
	return	preg_match('"^https?://(?:[a-z\d_\-]+\.)?youtube\.[a-z]{2,3}/watch/?\?(?:.*&)?v=(?<youtubeid>[a-z\d_\-]+)$"i'.$utf8_mod, $url, $match)
		||	preg_match('"^https?://youtu\.be/(?P<youtubeid>[a-z\d_\-]+)$"i'.$utf8_mod, $url, $match)
		||	preg_match('"^https?://(?:[a-z\d_\-]+\.)?youtube\.[a-z]{2,3}/embed/videoseries\?list=(?:[a-z\d_\-]+)&v=(?P<youtubeid>[a-z\d_\-]+)$"i'.$utf8_mod, $url, $match)
		||	preg_match('"^https?://(?:[a-z\d_\-]+\.)?youtube\.[a-z]{2,3}/(?:embed|v)/(?P<youtubeid>[a-z\d_\-]+)$"i'.$utf8_mod, $url, $match);
}

function youtube_match_channel_url(string $url, ?array &$match = null, bool $utf8 = true): bool {
	# returns in the match parameter:
	# type:		'channel' | 'c', both mean channel anyway, not useful to even store?
	# channel:	channel ID
	return	preg_match('"^https://(?:[a-z\d_\-]+\.)?youtube\.[a-z]{2,3}/(?<type>channel|c)/(?<channel>[a-z\d_\-]+)$"i'.($utf8 ? 'u' : ''), $url, $match);
}

function get_youtube_channel_url_from_any_url(string $url, bool $utf8 = true): string|false {
	require_once __DIR__.'/../_flockmod.inc';

	# Using a cookie here does more harm than good, most of the time, because YouTube expected some
	# interoperations between us, them and the cookie in between. But we just paste (stale) cookie
	# data and getting no result.

#	if (!($cookie = db_single('cookie', '
#		SELECT DATA
#		FROM cookie
#		WHERE SITE = "youtube"
#		ORDER BY STAMP DESC
#		LIMIT 1,1'))
#	||  !($data = safe_file_get_contents($url, [CURLOPT_COOKIE => $cookie]))
	if (!($data = safe_file_get_contents($url, [
			CURLOPT_COOKIEFILE		=> false,
			CURLOPT_COOKIEJAR		=> false,
			CURLOPT_COOKIESESSION	=> false,
			# Simulatin Wget currently seems to skip the consat page.
			CURLOPT_USERAGENT		=> 'Wget/1.21.3',
			CURLOPT_HTTPHEADER		=> [
				'Accept: */*',
				'Accept-Encoding: identity',
			]
		]))
	||	!preg_match('!property="og:url"\s+content="(?<channel_url>[^"]+)"!i'.($utf8 ? 'u' : ''), $data, $match)
	) {
		error_log("could not get channel for url: $url");
		# error_log($data);
		return false;
	}
	return $match['channel_url'];
}

function get_channel_from_url(string $url, bool $utf8 = true): string|false {
	if (preg_match('"youtube.com/channel/(?<channel>[a-z\d_\-]+)"i'.($utf8 ? 'u' : ''), $url, $match)) {
		return $match['channel'];
	}
	return false;
}
