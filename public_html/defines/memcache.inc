<?php

declare(strict_types=1);

require_once __DIR__.'/timing.inc';

const DEFAULT_EXPIRATION					= 120;
# Temporarily disable mem<PERSON><PERSON> after MAXIMUM_MEMCACHE_FAILURES within REMEMBER_MEMCACHE_FAILURES seconds
const MAXIMUM_MEMCACHE_FAILURES				= 20;
const REMEMBER_MEMCACHE_FAILURES			= 60;

# Set WAIT_FOR_QUERY low, so we are certain time-out occurs early
# If 0, then the logging of slow queries is disabled.
const WAIT_FOR_QUERY						= 0;

const CACHE_PERIOD_PARTYLIST				= DEFAULT_EXPIRATION;
const CACHE_PERIOD_TICKET_LIST_AT_ITEM		= ONE_HOUR;
const MEMCACHE_MAXIMUM_RELATIVE_EXPIRATION	= 30 * ONE_DAY;
