<?php

declare(strict_types=1);

const UBB_HTML					= 0x1;	// generate HTML output
const UBB_HTML_ENTITIES			= 0x2;	// use html entities
const UBB_OBJECTS				= 0x4;
const UBB_SPECIAL				= 0x8;
const UBB_HIDDEN				= 0x10;
const UBB_STATIC_SMILEYS		= 0x20;
const UBB_ANIMATED_SMILEYS		= 0x40;
const UBB_SMILEYS				= UBB_STATIC_SMILEYS | UBB_ANIMATED_SMILEYS;
const UBB_COMBINE_2BR			= 0x80;

const UBB_DONT_ADD_UTM			= 0x100;
const UBB_NO_LINK_ALT			= 0x200;
const UBB_NO_LINKS				= 0x400;
const UBB_NO_WARNINGS			= 0x800;
const UBB_COUNTAD				= 0x1000;

const UBB_UTF8					= 0x2000;
const UBB_STORE_LANGUAGES		= 0x4000;
const UBB_NO_HILITING			= 0x8000;
const UBB_UGC					= 0x10000;
const UBB_NEW_TAB				= 0x20000;
const UBB_WARN_IF_INACCESSIBLE	= 0x40000;

const UBB_SKIP_NL2BR			= 0x80000;	# mainly useful for pre-formated text, <br />s would introduce too much space

const UBB_ALL =
UBB_HTML
|	UBB_HTML_ENTITIES
|	UBB_OBJECTS
|	UBB_SPECIAL
|	UBB_HIDDEN
|	UBB_SMILEYS
|	UBB_COMBINE_2BR;

const UBBT_LONG		= 1;
const UBBT_ELEMENT	= 2;
const UBBT_SHORT	= 3;

const UBB_MAX_TAGS		= 5000;

const UBB_BEGIN	= "\x1E";	// '';	30	0x1E
const UBB_SEP	= "\x1F";	// '';	31	0x1F
const UBB_END	= "\x6";	// '';	 6  0x06
