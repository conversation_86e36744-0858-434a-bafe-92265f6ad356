<?php

use Facebook\Facebook;

spl_autoload_register(static function(string $class): void {
	if (str_starts_with($class, 'Facebook\\')) {
		require_once str_replace('\\','/',$class).'.php';
	}
});

#define('FACEBOOK_ENABLED',		CURRENTSTAMP < 1638478800);	# 2021-12-02 22:00, disabled by Facebook
const FACEBOOK_ENABLED			= false;

const FACEBOOK_STOP_COUNTERS	= 1524002400; # 18 april 2018
const FACEBOOK_SHOW_GUESTS		= true;

# const FACEBOOK_LOG_GRAPH_QUERIES= false;

const FACEBOOK_LIVE_APPID		= '455935437763602';
const FACEBOOK_LIVE_APPSECRET	= '********************************';

const FACEBOOK_TEST_APPID		= '368293807453779';
const FACEBOOK_TEST_APPSECRET	= '********************************';

#const FACEBOOK_APPID			= PHP_SAPI != 'cli' && SERVER_VIP ? FACEBOOK_TEST_APPID : FACEBOOK_LIVE_APPID;
#const FACEBOOK_APPSECRET		= PHP_SAPI != 'cli' && SERVER_VIP ? FACEBOOK_TEST_APPSECRET : FACEBOOK_LIVE_APPSECRET;

const FACEBOOK_APPID			= FACEBOOK_LIVE_APPID;
const FACEBOOK_APPSECRET		= FACEBOOK_LIVE_APPSECRET;

function get_facebook_appsecret(string $appid): ?string {
	static $__appid_to_secret = [
		FACEBOOK_LIVE_APPID	=> FACEBOOK_LIVE_APPSECRET,
		FACEBOOK_TEST_APPID	=> FACEBOOK_TEST_APPSECRET,
	];
	return $__appid_to_secret[$appid] ?? null;
}
function get_fb(float $version = 7.0): object|false {
	static $__fb = new Facebook([
		'app_id'				=> FACEBOOK_APPID,
		'app_secret'			=> FACEBOOK_APPSECRET,
		'default_graph_version'	=> sprintf('v%.1f', $version),
	]);
	return $__fb;
}
function get_fb_app_access_token(): ?string {
	return get_fb()?->getApp()?->getAccessToken();
}
function get_fb_redirect_uri(?string $path = null): string {
	return "https://{$_SERVER['HTTP_HOST']}".($path ?: '/login.fb');
}

/*function get_fbid(string $element, int $id): string|false|null {
	# get 'primary' facebook id, for now this means the one with most visitors
	static $__okelems = [
		'party'	=> true,
	];
	if (!isset($__okelems[$element])) {
		return null;
	}
	return memcached_single(['fbid', 'fbcounters'], "
		SELECT FBID
		FROM fbid
		LEFT JOIN fbcounters USING (FBID)
		WHERE ELEMENT = '$element'
		  AND ID = $id
		ORDER BY ATTENDING DESC, MAYBE DESC")
}*/

function get_facebook_icon(?string $class = null): string {
	ob_start();
	?><img<?
	?> alt="Facebook"<?
	?> title="Facebook"<?
	?> class="presence<? if ($class) { ?> <? echo $class; } ?>"<?
	?> src="<?= STATIC_HOST ?>/presence/facebook<?= is_high_res() ?>.png" /><?
	return ob_get_clean();
}
function appsecret_proof(string $access_token, string $appid): string {
	return hash_hmac('sha256', $access_token, get_facebook_appsecret($appid));
}
function fbgraph_access(int $fbid, string $qstr, string $descr): bool {
	static $safe_per_hour = 5000;

	if (false === ($past_hour = db_single('fbgraph_access','
		SELECT COUNT(*)
		FROM fbgraph_access
		WHERE STAMP > '.(time() - ONE_HOUR)))
	) {
		return false;
	}
	if ($past_hour === $safe_per_hour) {
		require_once '../_flockbot.inc';
		flockbot_notify(FLOCK_CHANNEL_IMPORTANT, 'fbgraph_access is now going past '.$safe_per_hour.' queries!');
	}
	return db_insert('fbgraph_access','
		INSERT INTO fbgraph_access SET
			QSTR	="'.addslashes($qstr).'",
			DESCR	="'.addslashes($descr).'",
			TYPE	="'.addslashes($GLOBALS['__type'] ?? '').'",
			STAMP	='.time().',
			FBID	='.$fbid
	);
}

function get_facebook_ids(
	string	$fbidstr,
	string	$element,
	int		$id,
	array	$fb_ids		 = [],
	bool	$for_processlist = false,
): array {
	if ($fb_ids) {
		if (!$fbidstr) {
			$fbidstr = implode(', ', $fb_ids);
		}
	} elseif ($fbidstr) {
		$fb_ids = int_explode(',', $fbidstr);
	}
	if (!$fb_ids) {
		return [null, null, false];
	}
	if (!$fbidstr) {
		$fbidstr = implode(',', $fb_ids);
	}
	$primary_fbid =
		count($fb_ids) === 1
	?	($fb_ids[0] ?? reset($fb_ids))
	:	(	$fbidstr
		?	get_primary_fb_id($element, $id, $fbidstr, $fb_ids, $for_processlist)
		:	null
		);
	if ($primary_fbid === false) {
		if (function_exists('error')) {
			$error = error(500, 'failed to get primary fb id for '.$element.' '.$id);
		}
		return [false, false, $error ?? null];
	}
	return [$primary_fbid, $fb_ids, null];
}

function get_primary_fb_id(
	string	$element,
	int		$id,
	?string	$fb_idstr		 = null,
	?array	$fb_ids			 = null,
	bool	$for_processlist = false
): int|false|null {
	$best_fbid = 0;

	switch ($element) {
	case 'organization':
	case 'location':
	case 'artist':
		if (false === ($types = db_same_hash('fbid','
			SELECT TYPE, FBID
			FROM fbid
			WHERE FBID IN ('.$fb_idstr.')
			ORDER BY
				TYPE="page" DESC,
				TYPE="user" DESC'))
		||	false === ($likes = db_simple_hash('fblikes','
			SELECT FBID, LIKES
			FROM fblikes
			WHERE FBID IN ('.$fb_idstr.')'))
		) {
			return false;
		}
		assert(is_array($likes)); # Satisfy type checker
		if (!$types) {
			mail_log("no fbid.TYPE for $element $id");
		}
		foreach ($types as /* $type => */ $test_fbids) {
			foreach ($test_fbids as $test_fbid => &$likecnt) {
				$likecnt = $likes[$test_fbid] ?? 0;
			}
			unset($likecnt);
			arsort($test_fbids);
			$best_fbid = array_key_first($test_fbids);
			break;
		}
		break;

	case 'party':
		# first drop all the instances if we have a main
		# but only if instances are not connected to other instance/main

		if (false === ($multi = db_same_hash('feedevent_multiday',"
			SELECT DISTINCT 'main', MAINID
			FROM feedevent_multiday
			WHERE MAINID IN ($fb_idstr)
			UNION
			SELECT DISTINCT 'instance', INSTANCEID
			FROM feedevent_multiday
			WHERE INSTANCEID IN ($fb_idstr)"))
		) {
			return false;
		}
		if ($multi) {
			if ($for_processlist
			&&	isset($multi['main'])
			) {
				# remove mains for processlist, they point to 'generic' encompassing event
				$keep_fb_ids = [];
				foreach ($fb_ids as $fbid) {
					if (!isset($multi['main'][$fbid])) {
						$keep_fb_ids[] = $fbid;
					}
				}
				$fb_ids = $keep_fb_ids;
				if (!isset($fb_ids[1])) {
					return $fb_ids[0] ?? null;
				}
				$fb_idstr = implode(',', $fb_ids);

			} elseif (isset($multi['main'], $multi['instance'])) {
				# are instances days or real editions?
				if (false === ($partyids = db_simple_hash(['fbid', 'connect', 'party'], "
					SELECT fbid.ID, GROUP_CONCAT(mfbid.FBID)
					FROM fbid
					JOIN connect ON connect.MAINID = fbid.ID
					JOIN party iparty ON fbid.ID = iparty.PARTYID
					JOIN fbid mfbid ON mfbid.ID = connect.ASSOCID
					JOIN party mparty ON mfbid.ID = mparty.PARTYID
					WHERE fbid.ELEMENT = 'party'
					  AND mfbid.ELEMENT = 'party'
					  AND fbid.FBID IN (".implode(', ', $multi['instance']).")
					  AND MAINTYPE = 'party'
					  AND ASSOCTYPE = 'party'
					  AND iparty.LOCATIONID = mparty.LOCATIONID /* multi-day only valid for same location */
					GROUP BY fbid.ID"))
				) {
					return false;
				}
				if (!$partyids) {
					# not connected on flock, so certainly not multi-day
					# --> keep instance, drop main
					$keep_fb_ids = [];
					foreach ($fb_ids as $fbid) {
						if (!isset($multi['main'][$fbid])) {
							$keep_fb_ids[] = $fbid;
						}
					}
				} else {
					# connected on flock, keep only main!
					$keep_fb_ids = [];
					foreach ($fb_ids as $fbid) {
						if (!isset($multi['instance'][$fbid])) {
							$keep_fb_ids[] = $fbid;
						}
					}
				}
				$fb_ids = $keep_fb_ids;
				if (!isset($fb_ids[1])) {
					return $fb_ids[0] ?? null;
				}
				$fb_idstr = implode(',', $fb_ids);
			}
		}
		if (false === ($have_visitor_cnts = db_single('fbcounters', "
			SELECT COUNT(DISTINCT FBID)
			FROM fbcounters
			WHERE FBID IN ($fb_idstr)"))
		) {
			return false;
		}
		if (($fbid_cnt = count($fb_ids)) === $have_visitor_cnts) {
			# get most visited
			return db_single('fbcounters', "
				SELECT FBID
				FROM fbcounters
				WHERE FBID IN ($fb_idstr)
				ORDER BY ATTENDING DESC, MAYBE DESC
				LIMIT 1");
		}
		# determine primary using name match
		if (false === ($names = db_simple_hash('facebook_event_name', "
			SELECT FBID, NAME
			FROM facebook_event_name
			WHERE FBID IN ($fb_idstr)
			ORDER BY MULTIDAY = 'day', NAME, FBID"))
		) {
			return false;
		}
		if (!$names
		||	count($names) !== $fbid_cnt
		) {
			# no names yet or not enough names!
			return null;
		}
		if (!($party = memcached_party_and_stamp($id))) {
			return false;
		}
		$party_name = mb_strtolower($party['NAME']);
		$party_name_and_subtitle = $party_name.' '.mb_strtolower($party['SUBTITLE']);

		$best_pct = 0;

		foreach ($names as $fbid => $name) {
			$name = mb_strtolower($name);
			foreach ([
				'inoffiziell',
				'partybus',
				'travel',
				'eventride',
				'unoffic',
				'busje',
			] as $dont_want) {
				if (str_contains($name, $dont_want)) {
					continue 2;
				}
			}
			foreach ([
				/** @lang PhpRegExp */ '"^(?<prefix>.*?)\[[^]]*](?<postfix>.*)$"iu',
				/** @lang PhpRegExp */ '"^(?<prefix>.*?)\([^)]*\)(?<postfix>.*)$"iu',
				/** @lang PhpRegExp */ '"^(?<prefix>.*?)official(?<postfix>.*)$"iu',
				/** @lang PhpRegExp */ '"^(?<prefix>.*?)20\d{2}(?<postfix>.*)$"iu',
			] as $regex) {
				if (preg_match($regex, $name, $match)) {
					$name = $match['prefix'];
					if (!empty($match['postfix'])) {
						$name .= ' '.$match['postfix'];
					}
				}
			}
			$name = preg_replace('"\s{2,}"u', ' ', $name);
			$name = utf8_mytrim($name, '!?.');

			mb_similar_text($party_name, $name, $pct);
			mb_similar_text($party_name_and_subtitle, $name, $pct2);

			$pct = max($pct,$pct2);
			if ($pct > $best_pct) {
				$best_fbid = $fbid;
				$best_pct = $pct;
			}
		}
		break;
	}
	return $best_fbid;
}
