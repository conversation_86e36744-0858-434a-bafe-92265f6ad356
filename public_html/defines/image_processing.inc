<?php

/** @noinspection DuplicatedCode */
/** @noinspection SpellCheckingInspection */

declare(strict_types=1);

const SUPPORTED_INPUT_FILE_TYPES = [
	'.ai'								=> true,
	'application/pdf'					=> true,
	'application/photoshop'				=> true,
	'application/postscript'			=> true,
	'application/psd'					=> true,
	'application/x-photoshop'			=> true,
	'image/avif'						=> true,
	'image/bmp'							=> true,
	'image/gif'							=> true,
	'image/jpeg'						=> true,
	'image/png'							=> true,
	'image/ppm'							=> true,
	'image/psd'							=> true,
	'image/targa'						=> true,
	'image/tif'							=> true,
	'image/tiff'						=> true,
	'image/vnd.wap.wbmp'				=> true,
	'image/image/vnd.adobe.photoshop'	=> true,
	'image/webp'						=> true,
	'image/x-icon'						=> true,
	'image/x-portable-anymap'			=> true,
	'image/x-portable-pixmap'			=> true,
	'image/x-targa'						=> true,
];

function get_image_processing_accepted_mime_types(): string {
	static $__all_supported = implodekeys(',', SUPPORTED_INPUT_FILE_TYPES);
	return $__all_supported;
}
