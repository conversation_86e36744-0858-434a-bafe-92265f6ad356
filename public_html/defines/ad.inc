<?php

declare(strict_types=1);

const ADS_OFFLINE				= false;

const FREQCAP_KEEP_PERIOD		= ONE_MONTH;	# one months

const ADPOS_TOPBOTTOM			=  1;
const ADPOS_LEFTMENU			=  2;
const ADPOS_HOMERECTANGLE		=  3;
const ADPOS_SMALL_SCREEN		=  4;
const ADPOS_MENUMINI			=  5;
const ADPOS_SUBBAR				=  6;
const ADPOS_RIGHT_TOWER			=  7;
const ADPOS_OVERLAY				=  8;
const ADPOS_PARTYLIST			=  9;
const ADPOS_CUSTOM				= 10;
const ADPOS_HPA					= 11;

# Ads in the horizontal list are checked after pageload to not be too near each other
# Too near and they will be hidden.
# So don't include ADPOS_PARTYLIST, those should never be hidden.

const ADPOS_HORIZONTAL = [
	ADPOS_TOPBOTTOM,
	ADPOS_HOMERECTANGLE,
	ADPOS_SMALL_SCREEN,
	ADPOS_SUBBAR
];

const AD_CACHE_TIME				=  10;
const AD_CACHE_TIME_PARTYLIST	= 300;

const ADFLG_ISOLATED			=  1;
const ADFLG_INTRUSIVE			=  2;
const ADFLG_SECTION_LIMIT		=  4;
const ADFLG_NEVER_TO_ADMINS		=  8;
const ADFLG_SMALL_SCREENS		= 16;
const ADFLG_SMALL_SCREENS_ONLY	= 32;
const ADFLG_SUPPORTED			= ADFLG_SECTION_LIMIT;

const ADLATE_CONTINUE			= ONE_WEEK;			# continue for one week to get ad done
const DOUBLE_RIGHT_MISSING		= 100;				# show banners with same URL on the right if this many impressions are missing
const OVERLAY_DEFAULT_DELAY		= TEN_MINUTES;		# 10 minutes default delay between overlays

const AD_END_OF_LARGE_AND_SMALL = 1694755679;	# Fri Sep 15 07:27:59 2023 +0200, end of ADFLG_SMALL_SCREEN*
