<?php

const PROCLIST_DO_PRIORITIES = true;

if (PROCLIST_DO_PRIORITIES) {
	function show_proclist_priority(): void {
		if (!have_admin()) {
			return;
		}

		if (!(db_single('proclist_priority','
			SELECT 1
			FROM proclist_priority
			WHERE ELEMENT = "'.$_REQUEST['sELEMENT'].'"
			  AND ID = '.$_REQUEST['sID']
		))) {
			return;
		}
		?><div class="priority block"><?
		?><a href="/test/process/prioritylist"><?
		echo element_name('priority');
		?></a><?
		?></div><?
	}
}
