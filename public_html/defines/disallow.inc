<?php

declare(strict_types=1);

require_once __DIR__.'/robots.inc';

const BLOCK_AGENDA_MISUSE	= true;		# block if too many agenda items are fetched
const BLOCK_ONLY_NEW_ITEMS	= false;	# block only when future agenda items are repeatedly fetched

const DISALLOW_NO		= 0;
const DISALLOW_CREATION	= 1;
const DISALLOW_LOGIN	= 2;
const DISALLOW_PROXY	= 3;
const DISALLOW_TOR		= 4;
const DISALLOW_HARVEST	= 5;

const MAX_DISALLOW_AGE	= 2629800;	# 1 month

const DISALLOW_TYPE_TO_STRING = [
	DISALLOW_CREATION	=> 'creation',
	DISALLOW_LOGIN		=> 'login',
	DISALLOW_PROXY		=> 'proxy',
	DISALLOW_TOR		=> 'tor',
	DISALLOW_HARVEST	=> 'harvest',
];

function get_disallow_type(?int $id = null): string|array {
	return $id ? DISALLOW_TYPE_TO_STRING[$id] : DISALLOW_TYPE_TO_STRING;
}

const DISALLOW_CACHE_KEY			= 'disallowusers_v19';
const DISALLOW_MINIMUM_STAMP_KEY	= DISALLOW_CACHE_KEY.':minimum_stamp';
function DISALLOW_SINGLE_KEY(string $ipstr) {
	return DISALLOW_CACHE_KEY.':single:'.$ipstr;
}

const FORCE_ALLOW_USER_CREATION		= false;	# always allow creation of new user, not regarding blocks
