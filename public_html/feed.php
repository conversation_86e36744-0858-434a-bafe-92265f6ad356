<?php

require_once '_exit_if_offline.inc';
require_once '_db.inc';
require_once '__translation.php';

define('CURRENTSTAMP',	 time());
define('TODAYSTAMP',	 mktime(0, 0, 0));
define('TODAYSTAMP_TZI', gmmktime(0, 0, 0));
define('CACHESTAMP', 	 CURRENTSTAMP - CURRENTSTAMP % 300);

require_once '_db.inc';
require_once '_memcache.inc';
require_once '_ubb.inc';
require_once '_date.inc';
require_once '_feed.inc';
require_once '_currentuser.inc';
require_once '_style.inc';
require_once '_spider.inc';
require_once '_require.inc';
require_once '_modified.inc';
require_once '_smallscreen.inc';
require_once '_hosts.inc';
require_once '_domain.inc';
require_once 'defines/topic.inc';
require_once 'defines/post.inc';

define('CURRENTDAYNUM', to_days());

function bail(int $errno): never {
	http_response_code($errno === 500 ? 503 : $errno);
	exit;
}

$element = $_REQUEST['ELEMENT'] ?? null;
$type	 = $_REQUEST['TYPE']	?? null;
$id	 = have_number($_REQUEST, 'ID') ? $_REQUEST['ID'] : 0;

if (!$element
||	!($itemid = FEED_ELEMENTS[$element] ?? null)
&&	$type !== 'comments'
) {
	bail(404);
}
if ($itemid === true) {
	$itemid = false;
}
if (!empty($_SERVER['PHP_AUTH_USER'])
&&	 empty($_SERVER['PHP_AUTH_PW'])
) {
	header('WWW-Authenticate: Basic realm="Partyflock"');
	bail(401);
}

$currentuser = new _currentuser(TINY_USER | TRY_BASIC_AUTH);

$stamp = 0;

if ($itemid
&&	!$type
) {
	$stamp = memcached_single($element, '
		SELECT MAX(GREATEST(PSTAMP, MSTAMP))
		FROM `'.$element.'`
		WHERE PSTAMP <= '.CACHESTAMP
	);
} else switch ($element) {
	case 'newsad':
		$stamp = memcached_single(['news', 'newsad'], '
			SELECT MAX(GREATEST(PSTAMP, news.MSTAMP))
			FROM news
			JOIN newsad USING (NEWSID)
			WHERE PSTAMP <= '.CACHESTAMP.'
			  AND ACTIVE'
		);
		break;

	case 'gallery':
		$stamp = memcached_single('gallery', '
			SELECT MAX(PSTAMP)
			FROM gallery
			WHERE VISIBLE = "yes"
			  AND NOVERVIEW = 0
			  AND PSTAMP <= '.CACHESTAMP
		);
		break;

	case 'contest':
		$stamp = memcached_single('contest', '
			SELECT CSTAMP
			FROM party_db.contest
			WHERE NOT ONLYPROMO
			  AND ACTIVE
			ORDER BY CONTESTID DESC
			LIMIT 1'
		);
		break;

	case 'msg':
		if ($type !== 'user') {
			bail(404);
		}
		require_once '_directmessage.inc';
		if (!have_user()
		||	CURRENTUSERID !== $id
		) {
			header('WWW-Authenticate: Basic realm="Partyflock"');
			bail(401);
		}
		if (!($lastdmsgid = db_single('user_cache', '
				SELECT LAST_DMSGID
				FROM user_cache
				WHERE USERID = '.$id))
		||	null === ($stamp = db_single('directmessage', '
				SELECT CSTAMP
				FROM directmessage
				WHERE MESSAGEID = '.$lastdmsgid))
		) {
			$stamp = memcached_single('directmessage', '
				SELECT CSTAMP
				FROM directmessage
				WHERE READM = 0
				  AND TO_DELETED = 0
				  AND TO_USERID = '.$id.'
				ORDER BY MESSAGEID DESC
				LIMIT 1'
			);
		}
		break;

	case 'forum':
	case 'topic':
		if (!($okforumids = memcached_simple_hash('rights_forum', '
			SELECT FORUMID, FORUMID
			FROM rights_forum
			WHERE USERID IN ('.CURRENTUSERID.',1)
			  AND TYPE IN ("normal", "read", "admin")
			ORDER BY USERID',
			TEN_MINUTES
		))) {
			bail($okforumids === false ? 500 : 403);
		}

		asort($okforumids);

		$okforumidstr = '('.implode(', ', $okforumids).')';

		switch ($element) {
		case 'forum':
			if (!have_element($_REQUEST, 'TYPE', ['topics', 'messages'])) {
				bail(404);
			}
			if (have_number($_REQUEST, 'ID')) {
				$forumid = $id;
				if (!isset($okforumids[$forumid])) {
					bail(403);
				}
				$stamp = $type === 'messages'
				?	 memcached_single('forum_stats', '
						SELECT MAX(LAST_POST)
						FROM forum_stats
						WHERE FORUMID = '.$forumid)
				:	 memcached_single('topic', '
						SELECT CSTAMP
						FROM topic
						WHERE FORUMID = '.$forumid.'
						ORDER BY TOPICID DESC
						LIMIT 1');

			} else {
				$stamp = $type === 'messages'
				?	 memcached_single('forum_stats', '
					 SELECT MAX(LAST_POST)
					 FROM forum_stats
					 WHERE FORUMID IN '.$okforumidstr)
				:	 memcached_single('topic', '
					 SELECT CSTAMP
					 FROM topic
					 WHERE FORUMID IN '.$okforumidstr.'
					 ORDER BY TOPICID DESC
					 LIMIT 1');
			}
			break;

		case 'topic':
			if (!have_number($_REQUEST, 'ID')) {
				bail(404);
			}
			$topicid = $id;
			if (!($topic = memcached_single_assoc('topic', '
				SELECT LSTAMP, FORUMID, FLAGS, STATUS
				FROM topic
				WHERE TOPICID = '.$topicid)
			)) {
				bail($topic === false ? 500 : 404);
			}
			if (!isset($okforumids[$topic['FORUMID']])
			||	$topic['STATUS'] === 'hidden'
			) {
				bail(403);
			}
			$stamp = $topic['LSTAMP'];
			break;
		}
		break;

	// FIXME: allow private flocks to be followed too
	case 'flock':
	case 'flocktopic':
		switch ($element) {
		case 'flock':
			if (!have_element($_REQUEST, 'TYPE', ['topics', 'messages'])) {
				bail(404);
			}
			if (have_number($_REQUEST, 'ID')) {
				$flockid = $id;
				if (!(	$stamp
				=	$type === 'messages'
				?	memcached_single(['flocktopic', 'flock'], '
						SELECT LSTAMP
						FROM flocktopic
						JOIN flock USING (FLOCKID)
						WHERE STATUS != "hidden"
						  AND flocktopic.FLOCKID = '.$flockid.'
						  AND NOT (FLAGS & '.TOPIC_IS_ANONYMOUS.')
						  AND flock.ACCEPTED
						  AND REMOVED = 0
						  AND NOT PRIVATE
						ORDER BY LSTAMP DESC
						LIMIT 1')
				:	memcached_single(['flocktopic', 'flock'], '
						SELECT flocktopic.CSTAMP
						FROM flocktopic
						JOIN flock USING (FLOCKID)
						WHERE flocktopic.FLOCKID = '.$flockid.'
						  AND NOT (FLAGS & '.TOPIC_IS_ANONYMOUS.')
						  AND STATUS != "hidden"
						  AND ACCEPTED
						  AND REMOVED = 0
						  AND NOT PRIVATE
						ORDER BY TOPICID DESC
						LIMIT 1')
				)) {
					# no messages yet
					$stamp = memcached_single('flock', '
						SELECT CSTAMP
						FROM flock
						WHERE FLOCKID=  '.$flockid
					);
					$nomsgs = true;
				}

			} else {
				$stamp = memcached_single(['flocktopic', 'flock'],
					$type === 'messages'
				? "	SELECT LSTAMP
					FROM party_db.flocktopic
					JOIN party_db.flock USING (FLOCKID)
					WHERE STATUS != 'hidden'
					  AND NOT (FLAGS & ".TOPIC_IS_ANONYMOUS.')
					  AND flock.ACCEPTED
					  AND REMOVED = 0
					  AND NOT PRIVATE
					ORDER BY LSTAMP DESC
					LIMIT 1'
				: "	SELECT flocktopic.CSTAMP
					FROM party_db.flocktopic
					JOIN party_db.flock USING (FLOCKID)
					WHERE STATUS != 'hidden'
					  AND NOT (FLAGS & ".TOPIC_IS_ANONYMOUS.')
					  AND ACCEPTED
					  AND REMOVED = 0
					  AND NOT PRIVATE
					ORDER BY TOPICID DESC
					LIMIT 1'
				);
			}
			break;

		case 'flocktopic':
			if (!have_number($_REQUEST, 'ID')) {
				bail(404);
			}
			$flocktopicid = $id;
			if (!($flocktopic = memcached_single_assoc(['flock', 'flocktopic'], '
				SELECT LSTAMP, flocktopic.FLOCKID, STATUS
				FROM flocktopic
				JOIN flock USING (FLOCKID)
				WHERE ACCEPTED = 1
				  AND NOT (FLAGS & '.TOPIC_IS_ANONYMOUS.')
				  AND REMOVED = 0
				  AND PRIVATE = 0
				  AND TOPICID = '.$flocktopicid
			))) {
				bail($flocktopic === false ? 500 : 404);
			}
			if ($flocktopic['STATUS'] === 'hidden') {
				bail(403);
			}
			$stamp = $flocktopic['LSTAMP'];
			break;
		}
		break;

	case 'agendall':
	case 'agendarchive':
		if (!ROBOT) {
			http_response_code(401);
			exit;
		}
	case 'agenda':
		if (!have_number($_REQUEST, 'ID')
		||	!have_element($_REQUEST, 'TYPE',['organization', 'location', 'boarding', 'artist', 'user', 'city', 'date', 'province'])
		) {
			bail(404);
		}
/*		require_once '_disallowuser.inc';
		if ($disallow_info = agenda_disallowed($_REQUEST['TYPE'], $_REQUEST['ID'])) {
			[$disallow, $type, $expires] = $disallow_info;
			$after = $expires - CURRENTSTAMP;
			header('Retry-After: '.$after);
			bail(429);
		}*/
		switch ($type) {
		case 'artist':
			$stamp = memcached_single(['party', 'lineup'], '
				SELECT MAX(GREATEST(party.CSTAMP, party.MSTAMP))
				FROM party
				JOIN lineup USING (PARTYID)
				WHERE ACCEPTED = 1
				  AND ARTISTID = '.$id
			);
			break;

		case 'location':
			$stamp = memcached_single('party', '
				SELECT MAX(GREATEST(party.CSTAMP, party.MSTAMP))
				FROM party
				WHERE ACCEPTED = 1
				  AND LOCATIONID = '.$id
			);
			break;

		case 'organization':
			$stamp = memcached_single(['party', 'connect'], '
				SELECT MAX(GREATEST(party.CSTAMP, party.MSTAMP))
				FROM party
				JOIN connect
				  ON MAINTYPE = "party"
				 AND MAINID = PARTYID
				 AND ASSOCTYPE = "organization"
				WHERE ACCEPTED = 1
				  AND ASSOCID = '.$id
			);
			break;

		case 'user':
			require_once '_visibility.inc';
			require_once '_externalsettings.inc';
			if (!($esettings = external_settings_list($id))) {
				bail(500);
			}
			if (!_visibility($esettings, 'AGENDA')) {
				bail(403);
			}
			$stamp = memcached_single(['going', 'party'], '
				SELECT MAX(GREATEST(party.CSTAMP, party.MSTAMP))
				FROM going
				JOIN party USING (PARTYID)
				WHERE ACCEPTED = 1
				  AND going.USERID = '.$id
			);
			break;

		case 'city':
			$stamp = memcached_single(['party', 'location', 'boarding'], <<<SQL
				(	SELECT MAX(GREATEST(CSTAMP, MSTAMP)) AS MSTAMP
					FROM party
					WHERE ACCEPTED
					  AND CITYID = $id
				) UNION (
					SELECT MAX(GREATEST(party.CSTAMP, party.MSTAMP)) AS MSTAMP
					FROM party
					JOIN location USING (LOCATIONID)
					JOIN boarding USING (BOARDINGID)
					WHERE party.ACCEPTED
					  AND boarding.CITYID = $id
				) UNION (
					SELECT MAX(GREATEST(party.CSTAMP, party.MSTAMP)) AS MSTAMP
					FROM party
					JOIN location USING (LOCATIONID)
					LEFT JOIN boarding USING (BOARDINGID)
					WHERE party.ACCEPTED
					  AND boarding.BOARDINGID IS NULL
					  AND location.CITYID = $id
				)
				ORDER BY MSTAMP DESC
				LIMIT 1
				SQL
			);
			break;

		case 'province':
			$stamp = memcached_single(['party', 'location', 'boarding', 'city'], '
				SELECT MAX(GREATEST(COALESCE(pa.CSTAMP, pb.CSTAMP), COALESCE(pa.MSTAMP, pb.MSTAMP)))
				FROM city
				LEFT JOIN boarding USING (CITYID)
				LEFT JOIN location USING (CITYID)
				LEFT JOIN party AS pa ON pa.LOCATIONID = location.LOCATIONID AND pa.ACCEPTED = 1
				LEFT JOIN party AS pb ON pb.BOARDINGID = boarding.BOARDINGID AND pb.ACCEPTED = 1
				WHERE PROVINCEID = '.$id
			);
			break;

		case 'date':
			if (!preg_match('"^(?P<year>\d{4})(?P<month>\d{2})(?P<day>\d{2})$"', $id, $match)) {
				bail(400);
			}
			require_once '_party.inc';
			$date_stamp = gmmktime(0, 0, 0,
				$show_month = (int)$match['month'],
				$show_day   = (int)$match['day'],
				$show_year  = (int)$match['year']
			);
			$start_stamp = $date_stamp + HOUR_DAY_START * ONE_HOUR;

			$stamp = memcached_single('party', '
				SELECT MAX(GREATEST(CSTAMP, MSTAMP)) AS MSTAMP
				FROM party
				WHERE '.($wherep = ' STAMP_TZI BETWEEN '.($start_stamp.' AND '.($start_stamp + ONE_DAY - 1)))
			);
			break;

		default:
			break;
		}
		break;

	case 'guestbook':
		header('Location: https://'.$_SERVER['HTTP_HOST'].link_to_comments('user', $id));
		bail(301);

	case 'headlines':
		[$year, $month, $day] = _getdate();
		$todate = $year * 10000 + $month * 100 + $day;
		$stamp = memcached_single('headlines', '
			SELECT MSTAMP
			FROM headlines
			WHERE DATE <= '.$todate.'
			ORDER BY DATE DESC
			LIMIT 1'
		);
		break;

	default:
		if ($type !== 'comments') {
			break;
		}
		require_once '_comment.inc';
		require_once '_commentsinfo.inc';
		require_once '_externalsettings.inc';
		require_once '_visibility.inc';

		if (!is_commentable($element)) {
			bail(404);
		}
		if ($element === 'user') {
			if (!have_number($_REQUEST, 'ID')
			) {
				bail(404);
			}
			if (external_setting($id, 'VISIBILITY_GUESTBOOK') !== EVERYBODY) {
				bail(403);
			}
		}
		if (!($info = get_commentsinfo($element, $id))) {
			bail(503);
		}
		if ($info[CMTI_HIDEALL]) {
			bail(403);
		}
		$table = $element.'_comment';
		if (!($maxcommentid = memcached_single($table, '
			SELECT MAX(COMMENTID)
			FROM '.$table.'
			WHERE ID = '.$id
		))) {
			break;
		}
		$stamp = memcached_single($table, '
			SELECT CSTAMP
			FROM '.$table.'
			WHERE COMMENTID = '.$maxcommentid
		);
		break;
}

if (!$stamp
&&	$element !== 'msg'
&&	$element !== 'agenda'
&&	$element !== 'agendall'
&&	$element !== 'agendarchive'
&&	$type !== 'comments'
) {
	bail(404);
}

header('Cache-Control: '.(have_user() ? 'private' : 'public'));

if (!HOME_OFFICE) {
	if ($stamp
	&&	not_modified($stamp)
	) {
		bail(304);
	}
}

echo '<?'; ?>xml version="1.0" encoding="<?= $charset = isset($utf8) ? 'utf-8' : 'windows-1252' ?>"<? echo '?>';

$self = 'https://'.$_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI'];

$abs = true;//isset($_REQUEST['ABSPATH']);

$base = $abs ? 'https://'.$_SERVER['HTTP_HOST'] : null;

$rss = isset($_REQUEST['RSS']) && $_REQUEST['RSS'] = '2.0' && $element === 'agenda';

[$current_year] = _getdate();

if (!$rss) {
	header('Content-Type: application/'.(isset($_REQUEST['PUREXML']) ? null : 'atom+').'xml; charset='.$charset);
	?><feed xmlns="http://www.w3.org/2005/Atom" xml:base="https://partyflock.<?= CURRENTDOMAIN ?>/"<?
	if ($element === 'agenda'
	||	$element === 'agendall'
	||	$element === 'agendarchive'
	) {
		?> xmlns:geo="http://www.w3.org/2003/01/geo/wgs84_pos#"<?
		?> xmlns:georss="http://www.georss.org/georss"<?
		?> xmlns:ev="http://purl.org/rss/1.0/modules/event/"<?
	}
	?>><?
	?><!-- Other private feeds are available if you wish to create applications. <NAME_EMAIL> --><?
	?><rights>Copyright <?= $current_year ?> Partyflock, <EMAIL></rights><?
	?><icon><?= $base ?>/favicon.png</icon><?
	?><logo><?= STATIC_HOST ?>/images/<EMAIL></logo><?
	?><id><?= $self; ?></id><?
	?><link rel="self" type="application/atom+xml" href="<?= $self ?>"><?
	if ($stamp) {
		?><updated><?= gmdate(ISO8601Z, $stamp) ?></updated><?
	}
	?><author><name>Partyflock</name></author><?
} else {
	header('Content-Type: application/rss+xml; charset='.$charset);
	$host = $_SERVER['HTTP_HOST'];
	?><rss version="2.0"><?
	?><channel><?
	?><copyright>Copyright <?= $current_year ?> Partyflock, <EMAIL></copyright><?
	?><image><?
		?><url><?= STATIC_HOST; ?>/images/<EMAIL></url><?
		?><title>Partyflock</title><?
		?><link>https://<?= $host ?>/</link><?
	?></image><?
	if ($stamp) {
		?><lastBuildDate><?= gmdate(RFC822Z, $stamp); ?></lastBuildDate><?
	}
}

if (!isset($_REQUEST['START'])) {
	$and_start_stamp = function() {
		return null;
	};
} else {
	$stamp = strtotime($_REQUEST['START']);
	$and_start_stamp = function($field) use ($stamp) {
		return ' AND '.$field.'>='.$stamp.' ';
	};
}

if ($itemid
&&	!$type
||	$element === 'newsad'
) {
	$name = Eelement_plural_name($element);

	?><link type="text/html" href="<?= $base ?>/<?= $element; ?>"><?
	?><title type="html"><?
	ob_start();
	?>Partyflock <? echo $name;
	echo escape_specials(ob_get_clean());
	?></title><?

	$join = $wherep = null;
	switch ($element){
	case 'column':
		$extra = 'ANONYMOUS,BYUSERID AS CUSERID';
		break;
	case 'news':
		$extra = '!SHOWUSER AS ANONYMOUS,USERID AS CUSERID';
		$wherep = ' AND TYPE!="teaserad"';
		break;
	case 'newsad':
		$extra = '!SHOWUSER AS ANONYMOUS,news.USERID AS CUSERID,STARTSTAMP,STOPSTAMP';
		$wherep = ' AND ACTIVE AND TYPE!="teaserad"';// AND STARTSTAMP<'.(TODAYSTAMP+30*3600).' AND STOPSTAMP>'.(TODAYSTAMP-30*3600);
		$join = ' JOIN newsad USING (NEWSID)';
		$itemid = 'NEWSID';
		$element = 'news';
		break;
	default:
		$extra = 'BYUSERID AS CUSERID';
		break;
	}
	if ($stamp
	&&	($items = memcached_rowuse_array(
		$element, $qstr = '
		SELECT	'.$itemid.' AS ID,TITLE,TEASER,PSTAMP,`'.$element.'`.CSTAMP,`'.$element.'`.MSTAMP, '.$extra.'
		FROM `'.$element.'`'.
		$join.'
		WHERE ACCEPTED=1
		  AND PSTAMP<='.$stamp.$wherep.
		 $and_start_stamp('PSTAMP').'
		ORDER BY PSTAMP DESC
		LIMIT 20'))
	) {
		require_once '_uploadimage.inc';
		foreach ($items as $item) {
			?><entry><?
			?><id><?= get_iri($element, $item['ID']) ?></id><?
			?><title type="html"><? ob_start(); echo flat_with_entities($item['TITLE'], UBB_UTF8), escape_specials(ob_get_clean()) ?></title><?
			?><link href="<?= get_element_href($element, $item['ID'], $item['TITLE']) ?>"><?
			?><published><?= gmdate(ISO8601Z, $item['PSTAMP']); ?></published><?

			if ($imgs = uploadimages_get_with_fallback($element, $item['ID'], UPIMG_NOCHANGE, 'regular')) {
				foreach ($imgs as $img) {
					/** @noinspection PhpRedundantOptionalArgumentInspection */
					extract($img, \EXTR_OVERWRITE);
					?><media:thumbnail<?
					?> xmlns:media="https://search.yahoo.com/mrss/"<?
					?> url="<?= $base,IMAGES_HOST,uploadimage_get_url($img) ?>"<?
					?> width="<?= $WIDTH ?>"<?
					?> height="<?= $HEIGHT ?>"<?
					?>><?
				}
			}
			?><updated><?= gmdate(ISO8601Z, max($item['MSTAMP'], $item['PSTAMP'], $item['CSTAMP'])); ?></updated><?
			?><summary type="html"><?
			$data = make_all_html($item['TEASER']);
			$data = str_replace('<i>', '', $data);
			$data = str_replace('</i>', '', $data);
			echo escape_specials($data);
			?></summary><?

			if ($item['CUSERID']
			&&	empty($item['ANONYMOUS'])
			) {
				?><author><?
					?><name><?= escape_specials(memcached_nick($item['CUSERID'])); ?></name><?
					?><uri><?= get_element_href('user', $item['CUSERID']) ?></uri><?
				?></author><?
			}
			?></entry><?
		}
	}
} else switch ($element) {
	case 'gallery':
		?><link type="text/html" href="<?= $base ?>/<?= $element; ?>"><?
		?><title type="html"><?
		ob_start();
		?>Partyflock <? echo Eelement_plural_name('party_photo');
		echo escape_specials(ob_get_clean());
		?></title><?

		if (!($galleries = memcached_rowuse_hash(['gallery', 'image'], '
			SELECT	GALLERYID, gallery.PARTYID, PSTAMP, gallery.CSTAMP, gallery.MSTAMP,
				GROUP_CONCAT(DISTINCT IF(ANONYMOUS, NULL, gallery.USERID)) AS PUSERIDS,
				COUNT(IF(HIDDEN, NULL, 1)) AS CNT
			FROM gallery
			JOIN image USING (GALLERYID)
			WHERE HIDDEN != 1
			  AND NOVERVIEW = 0
			  AND VISIBLE = "yes"
			  AND PSTAMP <= '.CACHESTAMP.
			 $and_start_stamp('PSTAMP').'
			GROUP BY GALLERYID
			ORDER BY PSTAMP DESC
			LIMIT 30'
		))) {
			break;
		}

		change_timezone('UTC');

		foreach ($galleries as $galleryid => $gallery) {
			?><entry><?
			?><id><?= get_iri('gallery', $galleryid) ?></id><?
			?><link href="<?= get_element_href('gallery', $galleryid) ?>"><?

			?><title type="html"><?
			ob_start();
			$party = memcached_party_and_stamp($gallery['PARTYID']);
			echo escape_utf8($party['NAME']);

			if ($party['SUBTITLE']) {
				?> <?= MIDDLE_DOT_ENTITY ?> <?
				echo escape_utf8($party['SUBTITLE']);
			}
			echo escape_specials($title = ob_get_clean());
			?></title><?

			?><published><?= gmdate(ISO8601Z, $gallery['PSTAMP']); ?></published><?
			?><updated><?=   gmdate(ISO8601Z, max($gallery['PSTAMP'], $gallery['CSTAMP'], $gallery['MSTAMP'])); ?></updated><?

			$plist = [];

			foreach (explode(',', $gallery['PUSERIDS']) as $puserid) {
				if (!$puserid) {
					continue;
				}
				?><author><?
				?><name><?= escape_specials(memcached_nick($puserid)) ?></name><?
				?><uri><?= get_element_href('user', $puserid) ?></uri><?
				?></author><?
			}
			?><summary type="html"><?
			ob_start();
			?><a href="<?= get_element_href('gallery', $galleryid) ?>"><?
			echo $gallery['CNT'];
			?> <?
			echo element_name('photo', $gallery['CNT']);
			?></a><?
			echo escape_specials(ob_get_clean());
			?></summary><?
			?></entry><?
		}
		change_timezone();
		break;

	case 'contest':
		?><link type="text/html" href="<?= $base ?>/<?= $element ?>"><?
		?><title type="html"><?
		ob_start();
		?>Partyflock <? echo Eelement_plural_name('contest');
		echo escape_specials(ob_get_clean());
		?></title><?

		if (!($contests = memcached_rowuse_hash(['contest', 'party'], '
			SELECT	CONTESTID, TYPE, contest.MSTAMP, contest.CSTAMP, VISIBLE_GROUPS, AMOUNT, contest.NAME AS CONTEST_NAME,
					PARTYID, party.NAME, party.STAMP_TZI, CANCELLED, MOVEDID
			FROM contest
			LEFT JOIN party USING (PARTYID)
			WHERE ACTIVE '.
			 $and_start_stamp('contest.PSTAMP').'
			ORDER BY CONTESTID DESC
			LIMIT 30'))
		) {
			return;
		}
		require_once '_contest.inc';
		foreach ($contests as $contestid => $contest) {
			/** @noinspection PhpRedundantOptionalArgumentInspection */
			extract($contest, \EXTR_OVERWRITE);

			$year = $city = null;

			if ($PARTYID) {
				require_once '_appic.inc';
				$party = memcached_party_and_stamp($PARTYID);
				if ($party) {
					$party['NAME_FOR_APPIC'] = null;
				}
				[$appic_name, $year, $city] = get_appic_titles($party);
			}

			$args = [
				'NAME'		=> $CONTEST_NAME,
				'PARTYID'	=> $PARTYID ?? 0,
				'YEAR'		=> $year	?? 0,
				'CITY'		=> $city	?? null,
			];

			?><entry><?
			?><id><?= get_iri('contest', $contestid) ?></id><?
			?><link href="<?= get_element_href('contest', $contestid) ?>"><?
			?><published><?= gmdate(ISO8601Z, $CSTAMP) ?></published><?
			?><updated><?=   gmdate(ISO8601Z, max($CSTAMP, $MSTAMP)) ?></updated><?
			?><title type="html"><?
			ob_start();
			switch ($TYPE) {
			case 'guestlist':
			case 'eticket':
			case 'freeticket':
			case 'scancode':
				echo __C('feed:contest:win_free_entrance_TITLE', DO_UBBFLAT, $args);
				break;

			default:
				echo __C('feed:contest:win_something_TITLE', DO_UBBFLAT, $args);
				break;
			}
			echo escape_specials(ob_get_clean());
			?></title><?

			?><summary type="html"><?
			ob_start();
			?><a href="<?= get_element_href('contest', $contestid) ?>"><?
			if ($VISIBLE_GROUPS !== 1) {
				echo $VISIBLE_GROUPS, MULTIPLICATION_SIGN_ENTITY, ' ', $AMOUNT;
			} else {
				echo $AMOUNT, MULTIPLICATION_SIGN_ENTITY, ' ';
			}
			echo ' ', contest_get_type($AMOUNT, $TYPE);
			?></a><?=
				escape_specials(ob_get_clean());
			?></summary><?

			?></entry><?
		}
		break;

/*	OBSOLETE
	case 'headlines':
		$headlines = memcached_single('headlines', '
			SELECT BODY
			FROM headlines
			WHERE DATE<='.$todate.'
			ORDER BY DATE DESC
			LIMIT 1'
		);
		if (!$headlines) return;
		foreach (explode("\n", $headlines) as $headline) {
			?><entry><?
			?><title type="text"><?= flat_with_entities($headline) ?></title><?
			?></entry><?
		}
		break;*/

	case 'msg':
		?><link type="html/text" href="<?= $base ?>/msg"><?
		?><title type="html"><?
		ob_start();
		?>Partyflock <? echo get_feed_title($element, $type, $id);
		echo escape_specials(ob_get_clean());
		?></title><?
		if ($stamp) {
			if ($msgs = memcached_rowuse_array(['directmessage', 'directmessage_data'], /** @lang MariaDB */ '
				SELECT MESSAGEID, FROM_USERID, CSTAMP, BODY, FLAGS
				FROM directmessage
				JOIN directmessage_data USING (MESSAGEID)
				WHERE READM = 0
				  AND TO_DELETED = 0
				  AND TO_USERID = '.$id.
				 $and_start_stamp('CSTAMP').'
				ORDER BY MESSAGEID DESC
				LIMIT 20'
			)) {
				foreach ($msgs as $msg) {
					?><entry><?
					?><id><?= get_iri('directmessage', $msg['MESSAGEID']) ?></id><?
					?><link href="<?= $base ?>/msg/<?= $msg['MESSAGEID'] ?>"><?
					?><title type="html"><?
					ob_start();
					?>Bericht van <?
					echo $author = escape_specials(memcached_nick($msg['FROM_USERID']));
					echo escape_specials(ob_get_clean());
					?></title><?
					?><published><?= gmdate(ISO8601Z, $msg['CSTAMP']); ?></published><?
					?><author><?
						?><name><?= $author; ?></name><?
						?><uri><?= get_element_href('user', $msg['FROM_USERID']) ?></uri><?
					?></author><?
					?><content type="html"><?= escape_specials(make_all_html($msg['BODY'])); ?></content><?
					?></entry><?
				}
			}
		}
		break;

	case 'forum':
		?><link type="html/text" href="<?= $base ?>/forum/<?
		if (isset($forumid)) {
			echo $forumid; ?>/<?
		}
		?>"><?
		?><title type="html"><?
		ob_start();
		?>Partyflock forum<?
		if (isset($forumid)) {
			?>: &quot;<?= escape_utf8(memcached_single('forum', 'SELECT NAME FROM forum WHERE FORUMID='.$forumid)); ?>&quot;<?
		}
		?>, <? echo element_plural_name($type === 'messages' ? 'newest_message' : 'newest_topic');
		echo escape_specials(ob_get_clean());
		?></title><?

		if ($type === 'topics') {
			// TOPICS (+FIRST BODY)
			if ($topics = memcached_rowuse_array(['topic', 'message'], '
				SELECT	topic.TOPICID,
					topic.SUBJECT,
					topic.CSTAMP,
					GREATEST(topic.MSTAMP, message.MSTAMP) AS MSTAMP,
					topic.USERID,
					BODY
				FROM topic
				LEFT JOIN message ON MESSAGEID = FIRST_MESSAGEID
				WHERE STATUS != "hidden"
				'.(isset($forumid) ? 'AND FORUMID = '.$forumid : '').'
				AND message.ACCEPTED = 1
				AND NOT (topic.FLAGS & '.TOPIC_IS_ANONYMOUS.')
				AND FORUMID IN '.$okforumidstr.
				$and_start_stamp('topic.CSTAMP').'
				ORDER BY topic.TOPICID DESC
				LIMIT 50'
			)) {
				foreach ($topics as $topic) {
					?><entry><?
					?><id><?= get_iri('topic', $topic['TOPICID']) ?></id><?
					?><title type="html"><? ob_start(); echo flat_with_entities($topic['SUBJECT']),escape_specials(ob_get_clean()); ?></title><?
					?><link href="<?= get_element_href('topic', $topic['TOPICID'], $topic['SUBJECT']) ?>"><?
					?><published><?= gmdate(ISO8601Z, $topic['CSTAMP']); ?></published><?
					?><updated><?=   gmdate(ISO8601Z, $topic[$topic['MSTAMP'] ? 'MSTAMP' : 'CSTAMP']); ?></updated><?
					?><author><?
						?><name><?= escape_specials(memcached_nick($topic['USERID'])); ?></name><?
						?><uri><?= get_element_href('user', $topic['USERID']) ?></uri><?
					?></author><?
					?><content type="html"><?= escape_specials(make_all_html($topic['BODY'])); ?></content><?
					?></entry><?
				}
			}
		} else {
			// MESSAGES
			if (isset($forumid)) {
				if ($topicids = memcached_simpler_array('topic', '
					SELECT TOPICID
					FROM topic
					WHERE FORUMID = '.$forumid.'
					  AND STATUS != "hidden"
					  AND FORUMID IN '.$okforumidstr.
					 $and_start_stamp('CSTAMP').'
					ORDER BY LSTAMP DESC
					LIMIT 60' // LIMIT of 60, so we allow about 10 messages to be unaccepted
				)) {
					sort($topicids);
					// first get only messageids. this is lots faster
					if ($messageids = memcached_simpler_array('message', '
						SELECT MESSAGEID 
						FROM message
						WHERE TOPICID IN ('.implode(', ', $topicids).')
						  AND NOT (FLAGS & '.POST_IS_ANONYMOUS.')
						ORDER BY MESSAGEID DESC
						LIMIT 60'
					)) {
						sort($messageids);
						$messages = memcached_rowuse_array(['message', 'topic'], '
							SELECT	MESSAGEID, message.CSTAMP, message.MSTAMP, message.USERID, BODY,
									message.TOPICID, SUBJECT
							FROM message
							JOIN topic USING (TOPICID)
							WHERE MESSAGEID IN ('.implode(', ', $messageids).')
							  AND NOT (message.FLAGS & '.POST_IS_ANONYMOUS.')
							  AND ACCEPTED = 1
							ORDER BY MESSAGEID DESC
							LIMIT 50'
						);
					}
				}
			} else {
				$messages = memcached_rowuse_array(['message', 'topic'], '
					SELECT	MESSAGEID, message.CSTAMP, message.MSTAMP, message.USERID, BODY,
							message.TOPICID, SUBJECT
					FROM message FORCE INDEX (message.PRIMARY) /* bug sill in MariaDB 11.1.2 */
					JOIN topic USING (TOPICID)
					WHERE ACCEPTED = 1
					  AND NOT (message.FLAGS & '.POST_IS_ANONYMOUS.')
					  AND STATUS != "hidden"
					  AND FORUMID IN '.$okforumidstr.
					 $and_start_stamp('message.CSTAMP').'
					ORDER BY MESSAGEID DESC
					LIMIT 50'
				);
			}
			if (empty($messages)) {
				break;
			}
			foreach ($messages as $message) {
				?><entry><?
				?><id><?= get_iri('message', $message['MESSAGEID']); ?></id><?
				?><title type="html"><? ob_start(); echo flat_with_entities($message['SUBJECT']),escape_specials(ob_get_clean()); ?></title><?
				# FIXME: point to correct page with PAGE=
				?><link href="<?= get_element_href('topic', $message['TOPICID'], $message['SUBJECT'])?>#m<?= $message['MESSAGEID']; ?>"><?
				?><published><?= gmdate(ISO8601Z, $message['CSTAMP']); ?></published><?
				?><updated><?=   gmdate(ISO8601Z, $message[$message['MSTAMP'] ? 'MSTAMP' : 'CSTAMP']); ?></updated><?
				?><author><?
					?><name><?= escape_specials(memcached_nick($message['USERID'])); ?></name><?
					?><uri><?= get_element_href('user', $message['USERID']) ?></uri><?
				?></author><?
				?><content type="html"><?= escape_specials(make_all_html($message['BODY'])); ?></content><?
				?></entry><?
			}
		}
		break;

	case 'topic':
		$subject = memcached_single('topic', 'SELECT SUBJECT FROM topic WHERE TOPICID='.$topicid);

		?><link type="text/html" href="<?= get_element_href('topic', $topicid) ?>"><?
		?><title type="html"><?
		ob_start();
		?>Partyflock onderwerp: <? echo flat_with_entities($subject, UBB_UTF8);
		echo escape_specials(ob_get_clean());
		?></title><?

		if ($messages = memcached_rowuse_array(['message', 'user_account'], '
			SELECT MESSAGEID, message.USERID, NICK, message.CSTAMP, message.MSTAMP, BODY
			FROM message
			JOIN user_account USING (USERID)
			WHERE TOPICID = '.$topicid.'
			  AND NOT (message.FLAGS & '.POST_IS_ANONYMOUS.')
			  AND ACCEPTED = 1'.
			 $and_start_stamp('message.CSTAMP').'
			ORDER BY MESSAGEID DESC
			LIMIT 50'
		)) {
			foreach ($messages as $message) {
				?><entry><?
				?><id><?= get_iri('message', $message['MESSAGEID']); ?></id><?
				?><title type="html"><?= escape_utf8($subject); ?></title><?
				?><link href="<?= get_element_href('topic', $topicid, $subject) ?>#m<?= $message['MESSAGEID']; ?>"><?
 				?><published><?= gmdate(ISO8601Z, $message['CSTAMP']); ?></published><?
 				?><updated><?=   gmdate(ISO8601Z, $message[$message['MSTAMP'] ? 'MSTAMP' : 'CSTAMP']); ?></updated><?
				?><author><?
					?><name><?= escape_specials($message['NICK']); ?></name><?
					?><uri><?= get_element_href('user', $message['USERID']) ?></uri><?
				?></author><?
				?><content type="html"><?= escape_specials(make_all_html($message['BODY'], UBB_UTF8)); ?></content><?
				?></entry><?
			}
		}
		break;

	case 'flock':
		?><link href="<?= isset($flockid) ? get_element_href('flock', $flockid) : $base.'/flock' ?>"><?
		?><title type="html"><?
		ob_start();
		?>Partyflock flock<?
		if (isset($flockid)) {
			?>: &quot;<?= escape_utf8(memcached_single('flock', 'SELECT NAME FROM flock WHERE FLOCKID='.$flockid)); ?>&quot;<?
		}
		?>, <? echo ($type === 'messages' ? 'nieuwste berichten' : 'nieuwste onderwerpen');
		echo escape_specials(ob_get_clean());
		?></title><?

		if ($type === 'topics') {
			// TOPICS (+FIRST BODY)
			if ($topics = isset($nomsgs) ? null : memcached_rowuse_array(
				'flocktopic', '
				SELECT flocktopic.TOPICID,SUBJECT,flocktopic.CSTAMP,GREATEST(flocktopic.MSTAMP,flockmessage.MSTAMP) AS MSTAMP,flocktopic.USERID,flockmessage.BODY
				FROM flocktopic
				JOIN flock USING (FLOCKID)
				LEFT JOIN flockmessage ON flockmessage.MESSAGEID=flocktopic.FIRST_MESSAGEID
				WHERE STATUS!="hidden"
				'.(	isset($flockid)
				?	'AND FLOCKID='.$flockid
				:	null
				).'
				AND flockmessage.ACCEPTED=1
				AND NOT (flockmessage.FLAGS & '.POST_IS_ANONYMOUS.')
				AND REMOVED=0
				AND flock.ACCEPTED=1'.
				$and_start_stamp('flocktopic.CSTAMP').'
				ORDER BY flocktopic.TOPICID DESC
				LIMIT 50'
			)) {
				foreach ($topics as $topic) {
					?><entry><?
					?><id><?= get_iri('flocktopic', $topic['TOPICID']) ?></id><?
					?><title type="html"><? ob_start(); echo flat_with_entities($topic['SUBJECT'], UBB_UTF8),escape_specials(ob_get_clean()); ?></title><?
					?><link href="<?= get_element_href('flocktopic', $topic['TOPICID'], $topic['SUBJECT']) ?>"><?
					?><published><?= gmdate(ISO8601Z, $topic['CSTAMP']); ?></published><?
					?><updated><?=   gmdate(ISO8601Z, $topic[$topic['MSTAMP'] ? 'MSTAMP' : 'CSTAMP']); ?></updated><?
					?><author><?
						?><name><?= escape_specials(memcached_nick($topic['USERID'])); ?></name><?
						?><uri><?= get_element_href('user', $topic['USERID']) ?></uri><?
					?></author><?
					?><content type="html"><?= escape_specials(make_all_html($topic['BODY'], UBB_UTF8)); ?></content><?
					?></entry><?
				}
			}
		} else {
			// MESSAGES
			if (isset($nomsgs)) {
				$messages = null;
			} elseif (isset($flockid)) {
				$topicids = memcached_simpler_array(
					array('flocktopic', 'flock'), '
					SELECT TOPICID
					FROM flocktopic
					JOIN flock USING (FLOCKID)
					WHERE FLOCKID='.$flockid.'
					  AND NOT (FLAGS & '.TOPIC_IS_ANONYMOUS.')
					  AND REMOVED=0
					  AND ACCEPTED=1
					  AND PRIVATE=0
					  AND STATUS!="hidden"'.
					 $and_start_stamp('flocktopic.CSTAMP').'
					ORDER BY LSTAMP DESC
					LIMIT 60'
				);
				if ($topicids) {
					sort($topicids);
					$messageids = memcached_simpler_array('flockmessage', '
						SELECT MESSAGEID
						FROM flockmessage
						WHERE TOPICID IN ('.implode(', ', $topicids).')'.
						 $and_start_stamp('CSTAMP').'
						ORDER BY MESSAGEID DESC
						LIMIT 60'
					);
					if ($messageids) {
						$messages = memcached_rowuse_array(['flockmessage', 'flocktopic'], '
							SELECT	MESSAGEID, flockmessage.USERID, flockmessage.CSTAMP,
								GREATEST(flocktopic.MSTAMP, flockmessage.MSTAMP) AS MSTAMP,BODY,
								flockmessage.TOPICID, SUBJECT
							FROM flockmessage
							JOIN flocktopic USING (TOPICID)
							WHERE ACCEPTED = 1
							  AND NOT (flockmessage.FLAGS & '.POST_IS_ANONYMOUS.')
							  AND MESSAGEID IN ('.implode(', ', $messageids).')
							ORDER BY MESSAGEID DESC
							LIMIT 50'
						);
					}
				}
			} else {
				$messages = memcached_rowuse_array(['flockmessage', 'flocktopic', 'flock'], '
					SELECT flockmessage.USERID,flockmessage.CSTAMP,GREATEST(flocktopic.MSTAMP,flockmessage.MSTAMP) AS MSTAMP,MESSAGEID,flockmessage.BODY,flockmessage.TOPICID,SUBJECT
					FROM flockmessage FORCE INDEX (PRIMARY) /* bug still in	 MariaDB 11.1.2 */
					JOIN flocktopic USING (TOPICID)
					JOIN flock USING (FLOCKID)
					WHERE flockmessage.ACCEPTED=1 '.
					(isset($flockid) ? 'AND FLOCKID = '.$flockid : '').'
					  AND NOT (flockmessage.FLAGS & '.POST_IS_ANONYMOUS.')
					  AND REMOVED=0
					  AND flock.ACCEPTED=1
					  AND PRIVATE=0
					  AND STATUS!="hidden"'.
					 $and_start_stamp('flockmessage.CSTAMP').'
					ORDER BY MESSAGEID DESC
					LIMIT 50'
				);
			}
			if (!empty($messages)) foreach ($messages as $message) {
				?><entry><?
				?><id><?= get_iri('message', $message['MESSAGEID']) ?></id><?
				?><title type="html"><? ob_start(); echo flat_with_entities($message['SUBJECT'], UBB_UTF8),escape_specials(ob_get_clean()); ?></title><?
				# FIXME: point to correct page
				?><link href="<?= get_element_href('flocktopic', $message['TOPICID'], $message['SUBJECT']) ?>#m<?= $message['MESSAGEID']; ?>"><?
				?><published><?= gmdate(ISO8601Z, $message['CSTAMP']); ?></published><?
				?><updated><?=   gmdate(ISO8601Z, $message[$message['MSTAMP'] ? 'MSTAMP' : 'CSTAMP']); ?></updated><?
				?><author><?
					?><name><?= escape_specials(memcached_nick($message['USERID'])); ?></name><?
					?><uri><?= get_element_href('user', $message['USERID']) ?></uri><?
				?></author><?
				?><content type="html"><?= escape_specials(make_all_html($message['BODY'], UBB_UTF8)); ?></content><?
				?></entry><?
			}

		}
		break;

	case 'flocktopic':
		$subject = flat_with_entities(memcached_single('flocktopic', 'SELECT SUBJECT FROM flocktopic WHERE TOPICID='.$flocktopicid), UBB_UTF8);
		?><link href="<?= $newbase = get_element_href('flocktopic', $flocktopicid) ?>"><?
		?><title type="html"><?
		ob_start();
		?>Partyflock flockonderwerp: <? echo $subject;
		echo escape_specials(ob_get_clean());
		?></title><?
		$messages = memcached_rowuse_array(
			'flockmessage', '
			SELECT MESSAGEID,USERID,CSTAMP,MSTAMP,BODY
			FROM flockmessage
			WHERE TOPICID='.$flocktopicid.'
			  AND NOT (flockmessage.FLAGS & '.POST_IS_ANONYMOUS.')
			  AND ACCEPTED=1'.
			 $and_start_stamp('CSTAMP').'
			ORDER BY MESSAGEID DESC
			LIMIT 50'
		);
		if ($messages) {
			foreach ($messages as $message) {
				?><entry><?
				?><id><?= get_iri('flockmessage', $message['MESSAGEID']) ?></id><?
				?><title type="html"><?= escape_utf8($subject); ?></title><?
				?><link href="<?= $newbase ?>#m<?= $message['MESSAGEID']; ?>"><?
				?><published><?= gmdate(ISO8601Z, $message['CSTAMP']); ?></published><?
				?><updated><?=   gmdate(ISO8601Z, $message[$message['MSTAMP'] ? 'MSTAMP' : 'CSTAMP']); ?></updated><?
				?><author><?
					?><name><?= escape_specials(memcached_nick($message['USERID'])); ?></name><?
					?><uri><?= get_element_href('user', $message['USERID']) ?></uri><?
				?></author><?
				?><content type="html"><?= escape_specials(make_all_html($message['BODY'], UBB_UTF8)); ?></content><?
				?></entry><?
			}
		}
		break;
	case 'agendall':
		$sequence = null;
		$link = false;
	case 'agendarchive':
		if (!isset($link)) {
			$sequence = ' AND STAMP<'.TODAYSTAMP;
			$link = '/'.$type.'/'.$id.'/archive#archive';
		}
	case 'agenda':
		if ($type === 'date') {
			if ($rss) {
 				?><link>https://<?= $host ?>/agenda/day/<?= $show_year; ?>/<?= $show_month; ?>/<?= $show_day; ?>/</link><?
			} else {
 				?><link href="<?= $base ?>/agenda/day/<?= $show_year; ?>/<?= $show_month; ?>/<?= $show_day; ?>"><?
				?><link rel="alternate" type="text/xml" href="<?= $base ?>/date.<? printf('%04d%02d%02d', $show_year, $show_month, $show_day); ?>.xml"><?
			}
		} else {
			if (!isset($link)) {
				$sequence = ' AND STAMP>='.TODAYSTAMP;
				$link = get_element_href($type, $id);
			}
			if ($rss) {
				?><link>https://<?= $host ?>/<?= $link ?></link><?
			} else {
				?><link href="<?= $link; ?>"><?
			}
		}
		?><title type="html"><?
		ob_start();
		?>Partyflock <? echo get_feed_title($element, $type, $id);
		echo $rss ? ob_get_clean() : escape_specials(ob_get_clean());
		?></title><?
		$select = 'party.NAME,STAMP_TZI, SUBTITLE, PARTYID,STAMP,DURATION_SECS,NOTIME,party.CSTAMP,party.MSTAMP,party.LOCATIONID,location.NAME AS LOC_NAME,location.CITYID,boarding.CITYID AS BOARDING_CITYID,MOVEDID,CANCELLED,LINEUP_PSTAMP,party.MUSERID,party.USERID,AT2400,party.CITYID AS PARTY_CITYID,party.BOARDINGID,boarding.ADDRESS AS BRD_NAME,LIVESTREAM';
		switch ($type) {
		case 'artist':
			$parties = memcached_rowuse_array(
				array('party', 'lineup', 'location', 'boarding'), '
				SELECT '.$select.'
				FROM party
				JOIN lineup USING (PARTYID)
				JOIN location ON location.LOCATIONID=party.LOCATIONID
				LEFT JOIN boarding ON boarding.BOARDINGID=party.BOARDINGID
				WHERE party.ACCEPTED=1
				  AND location.ACCEPTED=1
				  AND ARTISTID='.$id.
				  $and_start_stamp('party.STAMP').
				  $sequence
			);
			break;
		case 'location':
			$parties = memcached_rowuse_array(
				array('party', 'location', 'boarding'), '
				SELECT '.$select.'
				FROM party
				JOIN location USING (LOCATIONID)
				LEFT JOIN boarding USING (BOARDINGID)
				WHERE party.ACCEPTED=1
				  AND location.ACCEPTED=1
				  AND location.LOCATIONID='.$id.
				  $and_start_stamp('party.STAMP').
				  $sequence
			);
			break;
		case 'boarding':
			$parties = memcached_rowuse_array(
				['party', 'location', 'boarding'], '
				SELECT '.$select.'
				FROM party
				JOIN location USING (LOCATIONID)
				JOIN boarding USING (BOARDINGID)
				WHERE party.ACCEPTED=1
				  AND location.ACCEPTED=1
				  AND BOARDINGID='.$id.
				  $and_start_stamp('party.STAMP').
				  $sequence
			);
			break;
		case 'organization':
			$parties = memcached_rowuse_array(
				array('party', 'connect', 'location'), '
				SELECT '.$select.'
				FROM party
				JOIN connect ON MAINTYPE="party" AND MAINID=PARTYID AND ASSOCTYPE="organization"
				JOIN location ON location.LOCATIONID=party.LOCATIONID
				LEFT JOIN boarding USING (BOARDINGID)
				WHERE party.ACCEPTED=1
				  AND location.ACCEPTED=1
				  AND ASSOCID='.$id.
				  $and_start_stamp('party.STAMP').
				  $sequence
			);
			break;
		case 'user':
			$parties = memcached_rowuse_array(
				array('party', 'going', 'location'), '
				SELECT '.$select.',MAYBE
				FROM going
				JOIN party USING (PARTYID)
				JOIN location USING (LOCATIONID)
				LEFT JOIN boarding USING (BOARDINGID)
				WHERE party.ACCEPTED=1
				  AND location.ACCEPTED=1
				  AND going.USERID='.$id.
				  $and_start_stamp('party.STAMP').
				  $sequence
			);
			break;
		case 'city':
			$parties = memcached_rowuse_array(
				array('party', 'location'), '
				(	SELECT '.$select.'
					FROM party
					JOIN location USING (LOCATIONID)
					LEFT JOIN boarding USING (BOARDINGID)
					WHERE party.ACCEPTED=1
					  AND location.ACCEPTED=1
					  AND location.CITYID='.$id.
					  $and_start_stamp('party.STAMP').
					  $sequence.'
				) UNION (
					SELECT '.$select.'
					FROM party
					JOIN location USING (LOCATIONID)
					JOIN boarding USING (BOARDINGID)
					WHERE party.ACCEPTED=1
					  AND location.ACCEPTED=1
					  AND boarding.CITYID='.$id.
					  $and_start_stamp('party.STAMP').
					  $sequence.'
				)'
			);
			break;
		case 'province':
			$parties = memcached_rowuse_array(
				array('party', 'location', 'city'), '
				SELECT '.$select.'
				FROM party
				LEFT JOIN location USING (LOCATIONID)
				LEFT JOIN boarding USING (BOARDINGID)
				JOIN city ON city.CITYID=IF(party.BOARDINGID,boarding.CITYID,location.CITYID)
				WHERE party.ACCEPTED=1
				  AND location.ACCEPTED=1
				  AND PROVINCEID='.$id.
				  $and_start_stamp('party.STAMP').
				  $sequence
			);
			break;
		case 'date':
			$parties = memcached_rowuse_array(
				array('party', 'location'), '
				SELECT '.$select.'
				FROM party
				JOIN location USING (LOCATIONID)
				LEFT JOIN boarding USING (BOARDINGID)
				WHERE party.ACCEPTED=1
				  AND location.ACCEPTED=1
				  AND '.$wherep.
				  $and_start_stamp('party.STAMP')
			);
			break;
		}
		if (empty($parties)) {
			break;
		}
		require_once '_sort.inc';
		require_once '_party.inc';
		if ($element === 'agendarchive') {
			number_reverse_sort($parties, 'STAMP');
		} else {
			number_sort($parties, 'STAMP');
		}
		change_timezone('UTC');
		foreach ($parties as $party) {
			$lineup_released = $party['LINEUP_PSTAMP'] <= CURRENTSTAMP;
			if (!$lineup_released
			&&	!have_admin('party')
			&&	$type === 'artist'
			) {
				continue;
			}
			$lineup =
				$lineup_released
			||	have_admin('party')
			?	memcached_simple_hash(['lineup', 'artist'], '
				SELECT ARTISTID,NAME
				FROM lineup
				JOIN artist USING (ARTISTID)
				WHERE PARTYID='.$party['PARTYID'].'
				ORDER BY NAME',
				TEN_MINUTES)
			:	null;
			$link = get_element_href('party', $party['PARTYID'], $party['NAME']);
			if ($rss) {
				?><item><?
				?><guid><?= get_iri('party', $party['PARTYID']) ?></guid><?
				?><link>https://<?= $host, $link ?></link><?
				?><title type="html"><?= escape_utf8($party['NAME']) ?></title><?
				?><pubDate><?= gmdate(RFC822Z, $party['CSTAMP']) ?></pubDate><?
			} else {
				?><entry><?
				?><id><?= get_iri('party', $party['PARTYID']) ?></id><?
				?><link href="<?= $link ?>"><?
				?><title type="html"><?= escape_utf8(escape_utf8($party['NAME'])); ?></title><?
				?><published><?= gmdate(ISO8601Z, $party['CSTAMP']); ?></published><?
				?><updated><?=   gmdate(ISO8601Z, $party[$party['MSTAMP'] ? 'MSTAMP' : 'CSTAMP']); ?></updated><?

				if ($info = memcached_party_and_stamp($party['PARTYID'])) {
					$info['TIMEZONE'] ? change_timezone($info['TIMEZONE']) : null;
				}
				?><ev:startdate><?= date('c', $party['STAMP']) ?></ev:startdate><?
				if ($party['DURATION_SECS']) {
					?><ev:enddate><?= date('c', $party['STAMP'] + $party['DURATION_SECS']) ?></ev:enddate><?
				}
				if ($info) {
					$info['TIMEZONE'] ? change_timezone() : null;
				}
				if (have_admin()) {
					?><author><?
						?><name><?= escape_specials(memcached_nick($party['USERID'])); ?></name><?
						?><uri><?= get_element_href('user', $party['USERID']) ?></uri><?
					?></author><?
				}
				if ($type === 'user') {
					?><partyflock xmlns="https://partyflock.<?= CURRENTDOMAIN ?>/"><?
						?><going partyid="<?= $party['PARTYID'] ?>" userid="<?= $id ?>" maybe="<?= $party['MAYBE'] ?>"><?
					?></partyflock><?
				}
			}
			ob_start();
			?><a href="<?= $link ?>" target="_blank"><?
			echo escape_utf8($party['NAME']);
			if ($party['SUBTITLE']) {
				?> <small>&middot; <?= escape_utf8($party['SUBTITLE']); ?></small><?
			}
			?></a><?
			if ($party['CANCELLED']) {
				?> <span class="error">(<?= __('status:cancelled'); ?>!)</span><?
			}
			if ($party['MOVEDID']) {
				?> <span class="warning">(<?= __('status:moved'); ?>, <?= get_element_link('party', $party['MOVEDID']); ?>)</span><?
			}
			?><br /><?
			$usecityid = $party['BOARDING_CITYID'] ?: $party['CITYID'];
			show_party_time($party,false,memcached_timezone_city($usecityid));
			if ($party['LOCATIONID']) {
				?><br /><?
				echo element_name('location');
				?>: <a href="<?= get_element_href('location', $party['LOCATIONID'], $party['LOC_NAME']); ?>" target="_blank"><?
				echo escape_utf8($party['LOC_NAME']);
				?></a><?
				if (!$party['BOARDINGID'] && $party['CITYID']) {
					?>, <? echo get_element_link('city', $party['CITYID']);
				}
			}
			if ($party['BOARDINGID']) {
				?><br /><?
				echo element_name('boarding');
				?>: <a href="<?= get_element_href('boarding', $party['BOARDINGID'], $party['BRD_NAME']); ?>" target="_blank"><?
				echo escape_utf8($party['BRD_NAME']);
				?></a><?
				if ($party['BOARDING_CITYID']) {
					?>, <? echo get_element_link('city', $party['BOARDING_CITYID']);
				}
			}
			if ($lineup) {
				?><br /><?
				echo element_name('lineup'); ?>: <?
				$size = count($lineup);
				foreach ($lineup as $artistid => $name) {
					echo get_element_link('artist', $artistid, $name);
					if (--$size) {
						?>, <?
					}
				}
			}
			$summary = ob_get_clean();

			$summary = str_replace("\xB7", '&#183;', $summary);

			if ($rss) {
				?><description><?= escape_specials(str_replace('href="/', 'href="'.'https://'.$host.'/', $summary)) ?></description><?
				?></item><?
			} else {
				?><summary type="html"><?
//				echo escape_specials($summary,ENT_COMPAT,null,false);
				echo escape_specials($summary);
				?></summary><?
				if ($geo = memcached_geo_for_party($party['PARTYID'])) {
					?><geo:lat><?=  $lat = round($geo['LAT'], 6) ?></geo:lat><?
					?><geo:long><?= $lon = round($geo['LON'], 6) ?></geo:long><?
					?><georss:point><?= $lat ?> <?= $lon ?></georss:point><?
				}
				?></entry><?
			}
			echo "\n";
		}
		change_timezone();
		break;
	default:
		if ($type !== 'comments') {
			break;
		}
		?><link href="<?= link_to_comments($element, $id) ?>"><?
		if ($element === 'user') {
			?><title type="html"><?
			ob_start();
			?>Partyflock gastenboek van <? echo escape_specials(memcached_nick($id));
			echo escape_specials(ob_get_clean());
			?></title><?
		} else {
			require_once '_feed.inc';
			?><title type="html"><?
			ob_start();
			?>Partyflock <? echo get_feed_title('comments', $element, $id);
			echo escape_specials(ob_get_clean());
			?></title><?
		}
		$comments = memcached_rowuse_array(
			$table, '
			SELECT COMMENTID,CSTAMP,MSTAMP,BODY,USERID,FLAGS,ACCEPTED
			FROM '.$table.'
			WHERE ID = '.$id.'
			  AND NOT (FLAGS & '.POST_IS_ANONYMOUS.') '.
			 $and_start_stamp('CSTAMP').'
			ORDER BY COMMENTID DESC
			LIMIT 50'
		);
		if (!empty($comments)) {
			$is_admin = $element === 'user' ? have_self_or_admin($id, $table) : have_admin($table);
			foreach ($comments as $comment) {
				if (!$comment['ACCEPTED']
				&&	!$is_admin
				) {
					continue;
				}
				?><entry><?
				?><id><?= get_iri($element, $comment['COMMENTID']) ?></id><?
				?><title type="html"><?
				ob_start();
				?>Bericht van <? echo escape_specials(memcached_nick($comment['USERID']));
				echo escape_specials(ob_get_clean());
				?></title><?
				?><published><?= gmdate(ISO8601Z, $comment['CSTAMP']); ?></published><?
				?><updated><?=   gmdate(ISO8601Z, $comment[$comment['MSTAMP'] ? 'MSTAMP' : 'CSTAMP']); ?></updated><?
				?><content type="html"><?= make_all_html($comment['BODY'], UBB_UTF8); ?></content><?
				?></entry><?
			}
		}
		break;
}
if ($rss) {
	?></channel></rss><?
} else {
	?></feed><?
}

require_once '_pagehit.inc';
count_pagehit(ALLSTAT_FEED);

if (!ROBOT) {
	db_insupd('feed_counter', '
		INSERT INTO feed_counter SET
			SECTION	= "'.$element.'",
			DAYNUM	= '.CURRENTDAYNUM.',
			ID	= '.$id.',
			TYPE	= "'.($type ? addslashes($type) : '').'",
			VIEWS	= 1
		ON DUPLICATE KEY UPDATE
			VIEWS	= VIEWS + 1'
	);
}

function get_iri(string $element, int $id): string {
	return 'https://partyflock.'.CURRENTDOMAIN.'/'.$element.':'.$id;
}
