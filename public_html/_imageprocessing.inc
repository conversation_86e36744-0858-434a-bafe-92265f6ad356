<?php

declare(strict_types=1);

require_once '_browser.inc';
require_once '_execute.inc';
require_once '_uploadimage.inc';
require_once 'defines/imagick.inc';

# args:
#	src		= source image name
#	info		= getimagesize

# Use pamscale or pnmscale instead of ImageMagick.
# pamscale is a lot more memory efficient. Where ImageMagick uses gigabytes of memory an storage
# for very large images and runs into limits, pamscale works fine.
const PPM_SCALE = true;

function process_images(array $sizes, array $args, bool $background = false): array|false|null {
	$blobs = [];
	$src     = $args['src'    ] ?? null;
	$to_webp = $args['to_webp'] ?? false;	# NOTE: convert $to_webp to $args['out'] = webp
	if (!$src) {
		if (!($source_data = $args['source_data'] ?? null)) {
			mail_log('no src and no source_data', item: get_defined_vars() + get_execute_logs());
			return false;
		}
		mail_log('process_images with source_data instead of source file', error_log: 'DEBUG');
		$src = tempnam('/tmpdisk', '__process_images_'.getmypid().'_');
		if (!file_put_contents($src, $source_data)) {
			return false;
		}
		unlink_at_end($src);
	}
	if (!($info = $args['info'] ?? uploadimage_file_info($src, get_alpha: true))) {
		$store = uniqid('/tmpdisk/__file_info_null_', true);
		copy($src, $store);
		mail_log('file info is null on '.gethostname().' for file in '.$store, $args, 'process_images fileinfo null');
		return false;
	}
	[$original_width, $original_height] = $info;
	$flags = 0;
	if (false === ($src_size = filesize($src))) {
		mail_log('filesize failed', get_defined_vars());
		return false;
	}
	assert(is_int($src_size)); # Satisfy EA inspection
	if (!$background) {
		if ($original_width	 <= 5000
		&&	$original_height <= 5000
		&&	$src_size < 5 * MEGABYTE
		) {
			# Probably slower than non async, as we're already taxing CPU with 1 process
			# error_log('turning on EXECUTE_ASYNC');
			#if (HOME_THOMAS) {
			#	$flags |= EXECUTE_ASYNC;
			#}
		} elseif (
			(	$original_width  > 10000
			||	$original_height > 10000)
		&&	$src_size > 5 * MEGABYTE
		) {
			register_error('imageprocessing:error:image_too_large_LINE');
			return false;
		}
	}
	if ($quick = !empty($args['quick'])) {
		ignore_user_abort(true);
		set_time_limit(0);
	}
	if (!$quick
	&&	!$background
	) {
		if ($src_size > MEGABYTE
		&&	!db_getlock($single_process_key = "proc_image:$src_size:".hash_file('xxh128', $src), 0)
		) {
			register_error('imageprocessing:error:already_processing');
			return null;
		}
		if ($src_size > 75 * MEGABYTE) {
			mail_log("ERROR imageprocessing file $src too large: $src_size", [
				'max'		=> '75 MiB',
				'_POST'		=> $_POST,
				'_FILES'	=> $_FILES,
			], 'uploaded image too large');
			register_error('imageprocessing:error:file_too_large_LINE', ['MAXMB' => 75]);
			return false;
		}
	}
	# FIXME: Move this to docs/image-processing.md
	#
	# ImageMagick
	#
	# Make sure the `quality` parameter is the first one supplied.
	# This makes sure it won't override any parameter coming after it. Otherwise, it might
	# change them. For example, the `quality` parameter set to 90 or higher, would set
	# the `sampling-factor` to `1x1`. To force the encoder to use `2x2`, supply it
	# later than the `quality` parameter.
	#
	# Options for ImageMagick:
	#
	# -quality (?<quality>\d+)
	#	Number from 0 to 100.
	#	Base quality parameters. But it varies wildly between the encoder, how good or bad
	#	an image looks when using the same low quality value. High (85+) quality values
	#	make the resulting image look identical too each other, no compression  artifacts,
	#
	# -sampling-factor (?<horizontal-factor>\d)x(?<vertical-factor>\d)
	# -sampling-factor (\d):(\d):(\d)
	#	Sampling factors used by JPEG or MPEG-2 encoder and YUV decoder/encoder.
	#	Conversion table for the two different notations. Part of the table:
	#		1x1	=== 4:4:4
	#		2x2 === 4:2:0
	#		2x1 === 4:2:2
	#		1x2 === 4:4:0
	#		4x2 === 4:1:1
	#		4x4 === 4:1:0
	#		JPEG supports: 4:4:4, 4:2:2, 4:2:0, 4:1:1
	#		AVIF supports: 4:4:4, 4:2:2, 4:2:0, 4:0:0
	#		WEBP supports: 4:2:0
	#
	#	1x1 (4:4:4) gives best results
	#
	# Options for generating different filetypes using ImageMagick:
	#
	# -define heic:chroma=xyz (for example 4:4:4 becomes 444)
	#	HEIC container requires specifying chroma/sampling-factor by supplying
	#	the heic:chroma define. -sample-factor does not work.
	#
	# -define jpeg:sampling-factor=
	#	Simply use -sample-factor as in above example.
	#
	# -define jpeg:dct-method=(default | fastest | float | ifast | islow)
	#	Selects the algorithm used for the DCT step.  Choices are:
	#		JDCT_ISLOW: slow but accurate integer algorithm
	#		JDCT_IFAST: faster, less accurate integer method
	#		JDCT_FLOAT: floating-point method
	#		JDCT_DEFAULT: default method (normally JDCT_ISLOW)
	#		JDCT_FASTEST: fastest method (normally JDCT_IFAST)
	#	In libjpeg-turbo, JDCT_IFAST is generally about 5-15% faster than
	#	JDCT_ISLOW when using the x86/x86-64 SIMD extensions (results may vary
	#	with other SIMD implementations, or when using libjpeg-turbo without
	#	SIMD extensions.)  For quality levels of 90 and below, there should be
	#	little or no perceptible difference between the two algorithms.  For
	#	quality levels above 90, however, the difference between JDCT_IFAST and
	#	JDCT_ISLOW becomes more pronounced.  With quality=97, for instance,
	#	JDCT_IFAST incurs generally about a 1-3 dB loss (in PSNR) relative to
	#	JDCT_ISLOW, but this can be larger for some images.  Do not use
	#	JDCT_IFAST with quality levels above 97.  The algorithm often
	#	degenerates at quality=98 and above and can actually produce a more
	#	lossy image than if lower quality levels had been used.  Also, in
	#	libjpeg-turbo, JDCT_IFAST is not fully accelerated for quality levels
	#	above 97, so it will be slower than JDCT_ISLOW.  JDCT_FLOAT is mainly a
	#	legacy feature.  It does not produce significantly more accurate

	$jpeg_options =
		' --progressive '.
		(	$quick
		?	' --quality=5 '.
			' --dct=fast '.
			' - sampling-factor 2x2 '

		:	' --quality=93 '.
			' --optimize '.
			' --dct=float ');

	$webp_options =
		# ImageMagick WEBP defines: https://imagemagick.org/script/webp.php
		' -define webp:thread-level=1 '.
		# ' -define webp:image-hint=photo '.
		(	$quick
		?	' -quality 5 '.
			' -define webp:alpha-quality=0 '.
			' -define webp:method=0 '.
			' -define webp:segment=1 '.
			' -define webp:pass=1 '.
			' -define webp:partitions=0 '

		:	' -quality '.(have_idnumber($args, 'webp_quality') ?: 85).' '.
			' -define webp:method=6 '.
			' -define webp:segment=4 '.
			' -define webp:partitions=3 ');

	$avif_options =
		# ImageMagick AVIF defines: https://imagemagick.org/script/defines.php (see HEIC defines)
		' -depth 8 '.
		(	$quick
		?	' -quality 5 '.
			' -define heic:chroma=420 '.
			' -define heic:speed=9 '

		:	' -quality 85 '.
			' -define heic:speed=3 ');

	if (!$quick && !empty($args['lossless'])) {
		# TODO: Allow setting of lossless for WEBP images by the user uploading.
		$webp_options .= ' -define webp:lossless=true ';
	}

	# We don't want to take much longer, not for quick processing, not for background processing.
	$nice = '';
	# $nice = $quick ? '' : 'ionice -c 3 nice -n 19 ';

	$filter = $quick ? 'box' : 'lanczos';

	$im_convert  = $nice.IM_CONVERT;
	$im_mogrify  = $nice.IM_MOGRIFY;

	$alpha  = $info['alpha']  ?? false;
	$rotate = $args['rotate'] ?? 0;

	# quick jpegtopnm gives different result than non-quick, so need separate pnm
	$intermediate_type = 'pnm';
	$intermediate_file = "{$src}_intermediate.pnm";
	if ($have_intermediary = file_exists($intermediate_file)) {
		error_log('NOTICE already have intermediary file: '.$intermediate_file);
	}
	if (file_exists("$src.pnm")) {
		mail_log("pnm file $src.pnm already exists, why?", get_defined_vars(), error_log: 'ERROR');
		# happens when uploadimage_file_info created tmp for recognition
		return false;
	}

	unlink_at_end($intermediate_file);

	$pam_extra = '';
	if (in_array($info[2], [
		IMAGETYPE_JPEG,
		IMAGETYPE_AVIF,
		IMAGETYPE_TIFF_II,
		IMAGETYPE_TIFF_MM,
	], true)) {
		# NOTE: exiftool tags: https://exiftool.org/TagNames/
		[$rc, $stdout, $stderr] = execute($command = $nice.'exiftool -G1 -S -n '.$src);
		if ($rc || $stderr) {
			process_images_fail($command, $src, $rc, $stderr);
			if ($rc) {
				return false;
			}
		}
		if (!$quick) {
			$sampling = null;
			$chroma = null;
			if (preg_match('"YCbCrSubSampling:\s*(\d+)\s+(\d+)"u', $stdout, $match)) {
				$chroma = match($sampling = $match[1].'x'.$match[2]) {
					'1x1'		=> '444',
					'2x1'		=> '422',
					'2x2'		=> '420',
					default		=> false,
				};
			} elseif (preg_match('"ChromaFormat:\s*(?<chroma_format>\d+)"u', $stdout, $match)) {
				[$sampling, $chroma] = match($match['chroma_format']) {
					'0'		=> ['1x1', '444'],	# 4:4:4
					'2'		=> ['2x1', '422'],	# 4:2:2
					'3'		=> ['2x2', '420'],	# 4:2:0
					default	=> [false, false],
				};
			}
			if ($chroma === false || $sampling === false) {
				mail_log('failed to detect chroma or sampling', get_defined_vars());
			}
			if ($sampling) {
				# JPEG parameters for pnmtojpeg:
				$jpeg_options .= " --sample=$sampling ";
				# JPEG parameters for ImageMagick:
				# $jpeg_options .= " -sampling-factor '$sampling' -define jpeg:sampling-factor=$sampling ";
			}
			if ($chroma) {
				$avif_options .= " -define heic:chroma=$chroma ";
			}
		}
		if (preg_match('"\[IFD0]\s*Orientation:\s*(?<orientation>\d+)"u', $stdout, $match)) {
			switch ((int)$match['orientation']) {
			case 2: $pam_extra = ' -leftright';
					break;
			case 3: $rotate = ($rotate + 180) % 360;
					break; //$pam_extra = ' -rotate180'; break;
			case 4: $pam_extra = ' -topbottom';
					break;
			case 5: $pam_extra = ' -transpose';
					break;
			case 6: $rotate = ($rotate + 90) % 360;
					break;
			case 7: $pam_extra = ' -xform=transpose,topbottom,leftright';
					break;
			case 8: $rotate = ($rotate + 270) % 360;
					break;
			default:
					# no action
			}
			if (in_array($rotate, [90, 270], true)) {
				$original_tmp	 = $original_width;
				$original_width	 = $original_height;
				$original_height = $original_tmp;
			}
		}
		HOME_THOMAS && error_log('avif_options: '.$avif_options);
	}
	switch ($info[2]) {
	case IMAGETYPE_GIF:
		$intermediate_type = 'pam';
		$filetype = 'gif';
		if ($multi_images
		=	($gif_info = shell_exec("gifsicle --no-warnings -I '$src' 2>/dev/null"))
		&&	preg_match('! (?<image_count>\d+) images!', $gif_info, $match)
		&&	$match['image_count'] > 1
		) {
			$to_apng = true;
		}
		break;

	case IMAGETYPE_WEBP:
		# WEBP supports transparency, so convert to pam
		$intermediate_type = 'pam';
		$filetype = 'webp';
		if ($have_intermediary) {
			break;
		}
		[$rc,, $stderr] = execute($command = "$nice dwebp -mt -quiet $src -pam -o $intermediate_file");
		# [$rc,, $stderr] = execute($command = "$im_convert $src $intermediate_type:$intermediate_file");
		if ($rc || $stderr) {
			process_images_fail($command, $src, $rc, $stderr);
			if ($rc && !$stderr) {
				$rc = 0;
			}
			if ($rc) {
				if ($stderr) {
					return false;
				}
				mail_log('rc but no stderr', get_defined_vars());
			}
		}
		break;

	case 'PPM':
		$rc = 0;
		$type_change = true;
		$filetype = 'avif';
		if ($have_intermediary) {
			break;
		}
		$intermediate_file = $info['ppm_file'];
		break;

	case 'AI':
	case 'PDF':
		$filetype = 'avif';
		$type_change = true;
		if ($have_intermediary) {
			break;
		}
		# [$rc,, $stderr] = execute($command = "$nice pdftoppm $src > $intermediate_file");
		[$rc,, $stderr] = execute($command = "$im_convert $src -quality 100 -density 300 $intermediate_type:$intermediate_file");
		if ($rc || $stderr) {
			process_images_fail($command, $src, $rc, $stderr);
			if ($rc) {
				return false;
			}
		}
		break;

	/** @noinspection PhpMissingBreakStatementInspection */
	case IMAGETYPE_PNG:
		$filetype = 'png';
		$type_change = false;
		# fall through
	/** @noinspection PhpMissingBreakStatementInspection */
	case IMAGETYPE_ICO:
		$filetype ??= 'webp';
		# fall through
	case IMAGETYPE_PSD:
	case IMAGETYPE_BMP:
	case IMAGETYPE_WBMP:
	case IMAGETYPE_TIFF_II:
	/** @noinspection PhpMissingBreakStatementInspection */
	case IMAGETYPE_TIFF_MM:
		$type_change ??= true;
		# fall through
	case IMAGETYPE_AVIF:
		$intermediate_type = 'pam';
		$filetype ??= 'avif';
		if ($have_intermediary) {
			break;
		}
		[$rc,, $stderr] = execute($command = "$im_convert $src $intermediate_type:$intermediate_file");
		if ($rc || $stderr) {
			process_images_fail($command, $src, $rc, $stderr);
			if ($rc) {
				return false;
			}
		}
		break;

	case IMAGETYPE_JPEG:
		$filetype = 'jpg';
		if ($have_intermediary) {
			break;
		}
		[$rc,, $stderr] = execute($command = "$im_convert $src -define jpeg:dct-method=islow $intermediate_type:$intermediate_file");
		if ($rc || $stderr) {
			process_images_fail($command, $src, $rc, $stderr);
			if ($rc) {
				return false;
			}
		}
		break;

	default:
		register_error('uploadimage:error:unsupported_filetype_LINE');
		if (isset($single_process_key)) {
			db_releaselock($single_process_key);
		}
		return false;
	}
	if (!empty($rc)) {
		return false;
	}

	$async = [];
	$done = [];
	$orig_filetype = $filetype;

	foreach ($sizes as $size => &$scales) {
		$filetype = $orig_filetype;
		[$width, $height] = $scales;
		$x = null;
		$y = null;
		$crop_width  = null;
		$crop_height = null;
		$box  = false;
		$crop = false;
		if (isset($scales[2])) {
			if ($scales[2] === 'pillar'
			||	$scales[2] === 'letter'
			) {
				$box = $scales[2];
			} else {
				$crop = true;
				[,, $x, $y, $crop_width, $crop_height] = $scales;
			}
		}
		if (in_array($rotate, [90, 270], true)) {
			if ($crop) {
				$scales = [$height, $width, $y, $x, $crop_height, $crop_width];
				[$width, $height, $x, $y, $crop_width, $crop_height] = $scales;
			} else {
				$scales = [$height, $width];
				[$width, $height] = $scales;
			}
		}
		# If we've done this already, get the result from $done.
		# The same width, height, x and y values might happen because the image has a small dimension.
		# For a small dimension, the different sizes contain the same image.
		if (isset($done[$filetype][$width][$height][$x][$y])) {
			$async[$size] = $done[$filetype][$width][$height][$x][$y];
			continue;
		}
		$file = $intermediate_file;
		$box_cmd = '';
		if ($box) {
			if ($alpha) {
				 $first_color =
				$second_color = 'transparent';
			} else {
				if ($box === 'letter') {
					# letterbox
					$hx = floor(.25 * $original_height);
					 $first_cmd = "-crop {$original_width}x$hx+0+0";
					$second_cmd = "-crop {$original_width}x$hx+0+".($original_height - $hx);
				} else {
					# pillarbox
					$wx = floor(.25 * $original_width);
					 $first_cmd = "-crop {$wx}x$original_height+0+0";
					$second_cmd = "-crop {$wx}x$original_height+".($original_width - $wx).'+0';
				}
				foreach ([
					[ 'first_color',  $first_cmd],
					['second_color', $second_cmd],
				] as [$which_color, $command]) {
					[$rc, $stdout, $stderr] = execute($command = "$im_convert $src $command -filter $filter -resize 1x1 txt:-");
					if ($rc || $stderr) {
						process_images_fail($command, $src, $rc, $stderr);
						return false;
					}
					if (preg_match('"#(?<rgb>(?<r>[a-fA-F\d]{2}+)(?<g>[a-fA-F\d]{2}+)(?<b>[a-fA-F\d]{2}+))"u', $stdout, $match)) {
						# NOTE: Make the letterbox and pillarbox colors a bit darker or lighter, hopefully making the
						#		image blend in better.
						$color = hexdec($match['rgb']);
						$r = hexdec($match['r']);
						$b = hexdec($match['b']);
						$g = hexdec($match['g']);
						if ($r >= 20 && $r < 127
						&&	$g >= 20 && $g < 127
						&&	$b >= 20 && $b < 127
						) {
							$r -= 20;
							$g -= 20;
							$b -= 20;
							$match['rgb'] = sprintf('#%02x%02x%02x', $r, $g, $b);
						} elseif (
							$r >= 127 && $r < 235
						&&	$g >= 127 && $g < 235
						&&	$b >= 127 && $b < 235
						) {
							$r += 20;
							$g += 20;
							$b += 20;
							$match['rgb'] = sprintf('#%02x%02x%02x', $r, $g, $b);
						}
					} else {
						$color = 0;
					}
					$$which_color =
						$color
					?	$match['rgb']
					:	'"#000000"';
				}
			}

			$s = max($original_width, $original_height);

			if ($box === 'letter') {
				# letter box
				$blocksize = floor(($s - $original_height) / 2);
				$box_cmd =
					" -gravity south -background  $first_color -extent {$s}x".($original_height + $blocksize).
					" -gravity north -background $second_color -extent {$s}x$s ";
			} else {
				# pillar box
				$blocksize = floor(($s - $original_width) / 2);
				$box_cmd =
					" -gravity east -background  $first_color -extent ".($original_width + $blocksize)."x$s ".
					" -gravity west -background $second_color -extent {$s}x$s ";
			}
		}
		if ($scale
		=	$width  !== $original_width
		||	$height !== $original_height
		) {
			$flags |= EXECUTE_CHECK_PNMSCALE;
		}
		/** @noinspection RedundantSuppression, TypeUnsafeComparisonInspection */
		if ($scale
		&&	$width  == $original_width
		&&	$height == $original_height
		) {
			mail_log(
				'scale but same size? '.
				" $width (".gettype($width).") x $height (".gettype($height).') !='.
				" $original_width (".gettype($original_width).") x $original_height (".gettype($original_height).')'.
				" src: $src",
				error_log: 'WARNING'
			);
		}
		if ($filetype !== 'webp'
		&&	$to_webp
		#	WEBP limit:
		&&	$width  < 16383
		&&	$height < 16383
		) {
			$filetype = 'webp';
			$type_change = true;
		}
		switch ($filetype) {
		case 'gif':
			if ($multi_images) {
				$flags |= EXECUTE_CHECK_GIFSICLE;
				$command = 'gifsicle --no-warnings -O3 --colors 256';
				if ($box) {
					mail_log('letter/pillar box of multi_images NOT supported', get_defined_vars(), error_log: 'WARNING');
				}
				if ($rotate) {
					$command .= " --rotate-$rotate ";
				}
				if ($crop) {
					$command .= " --crop $x,$y+{$crop_width}x$crop_height ";
				}
				if ($scale) {
					$command .= " --resize {$width}x$height ";
				}
				$command .= ' '.$src;
				$file = null;
			} else {
				$command = "$im_mogrify gif:- ";
				if ($rotate) {
					$command .= " -rotate $rotate ";
				}
				if ($box) {
					$command .= $box_cmd;
				}
				if ($crop) {
					$command .= " -crop {$crop_width}x$crop_height+$x+$y ";
				}
				if ($scale) {
					$command .= " -filter $filter -resize {$width}x$height! ";
				}
				$command .= ' -colors 255 gif:- ';
				$file = $src;
			}
			if (isset($to_apng)) {
				$command .= ' | ';
				$command .= __DIR__.'/../bin/mygif2apng.php';
				$filetype = 'png';
			}
			break;

		case 'webp':
		case 'jpg':
		case 'png':
		case 'avif':
			if ($modify = $scale || $box || $crop) {
				$modified[$size] = true;
			}
			$command = '';
			if ($modify
			&&	($box || $crop || $scale)
			) {
				$command .= "$im_mogrify $intermediate_type:- ";
				if ($box) {
					$command .= $box_cmd;
				}
				if ($crop) {
					$command .= " -crop {$crop_width}x$crop_height+$x+$y ";
				}
				if ($scale && !PPM_SCALE) {
					$command .= " -filter $filter -resize {$width}x$height! ";
				}
				$command .= " $intermediate_type:- | ";
				if ($scale && PPM_SCALE) {
					# pnmscale/pamscale is much more memory friendly than Imagemagick
					$command .= ($intermediate_type === 'pam' ? 'pamscale' : 'pnmscale')." -filter $filter -quiet -xsize $width -ysize $height | ";
				}
			}
			switch ($filetype) { # NOSONAR
			case 'png':
				/** @var array<string, string> $__cmd */
				# static $__cmd;
				#if (!isset($__cmd[$src])) {
					# NOTE: to_webp does not get here, because filetype is then set to webp and $png is not true

					/*$header = file_get_contents($int ermediate_file,  length: MEGABYTE);
					$__cmd[$src]
					=   (   $alpha
						&&  preg_match('"MAXVAL\s+(?<max_val>\d+).*?[A-Z]+_ALPHA"s', $header, $match)
						&&  $match['max_val'] > 255
						)
					||  (   preg_match('"DEPTH (?<depth>\d+)"', $header, $match)
						&&  $match['depth'] < 4
						)
					?   $im_convert.' pam:- '.($to_webp ? 'webp' : 'png').':-'
					:   (   $to_webp
						?   "$im_convert $webp_options pam:- webp:- "
						:   (   $alpha
							||  $intermediate_type === 'pam'
							?   'pamrgbatopng'
							:   'pnmtopng'
							)
						).' -quiet';*/
				#}
				$command .= " $im_convert - png:- ";
				#$command .= $__cmd[$src];

				if (!$quick) {
					$command .= ' | /home/<USER>/bin/shrink_png ';
				}
				break;

			case 'webp':
				# NOTE: only ImageMagick mogrify success, both GraphicsMagick and cwebp show: "iTXt: chunk data is too large"
				# $cmd .= 'cwebp '.$webp_options.cwebp_parameters($filetype).' -quiet -o - -- -';
				$command .= "$im_convert $intermediate_type:- $webp_options webp:- ";
				break;

			case 'avif':
				$command .= "$im_convert $intermediate_type:- $avif_options avif:- ";
				break;

			case 'jpg':
				$command .= 'pnmtojpeg -quiet '.$jpeg_options;
				break;

			default:
				error_log("ERROR unsupported filetype '$filetype'");
			}
			break;

		default:
			continue 2;
		}

		if ($file && ($rotate || $pam_extra)) {
			$modified[$size] = true;
			$rotate_str = match($rotate) {
				default	=> '',
				90		=> '-rotate270',
				180		=> '-rotate180',
				270		=> '-rotate90',
			};
			/** @noinspection SpellCheckingInspection */
			$command = "pamflip -memsize=256 -quiet $rotate_str $pam_extra | $command";
		}

		$result = execute($command = $nice.$command, $flags, $file);
		[$rc,, $stderr] = $result;
		if ($rc || $stderr) {
			process_images_fail($command, $src, $rc, $stderr);
			if ($rc) {
				return false;
			}
		}

		$done[$filetype][$width][$height][$x][$y] = $result;
		$async[$size] = $result;
	}
	unset($scales);

	if (empty($async)) {
		if (isset($single_process_key)) {
			db_releaselock($single_process_key);
		}
		return false;
	}

	$identicals = [];
	$async_done = [];
	$unique_size = 0;

	foreach ($async as $size => $id) {
		if ($flags & EXECUTE_ASYNC) {
			[, $blobs[$size]] = $res = finish_execution($id);
		} else {
			[, $blobs[$size]] = $id;
		}
		if (# only if types are the same!
			empty($type_change)
		&&	!isset($modified[$size])
		&&	!empty($src_size)
		&&	($new_size = strlen($blobs[$size])) > $src_size
		) {
			mail_log('image size increased, picking source file', [
				'src_size'	=> $src_size,
				'new_size'	=> $new_size,
				'filetype'	=> $filetype,
			], error_log: 'WARNING');
			$blobs[$size] = file_get_contents($src);
		}
		if ($flags & EXECUTE_ASYNC) {
			assert(is_int($id));	# Satisfy EA inspection
			if (!isset($async_done[$id])) {
				$async_done[$id] = $size;
			}
			if (false !== ($old_id = $res[3] ?? false)) {
				$identicals[$size] = $async_done[$old_id];
			} else {
				$unique_size += strlen($blobs[$size]);
			}
		} else {
			$unique_size += strlen($blobs[$size]);
		}
	}
	if ($flags & EXECUTE_ASYNC) {
		clear_executions($async);
	}
	if (isset($single_process_key)) {
		db_releaselock($single_process_key);
	}
	return [$blobs, $filetype, $unique_size, $identicals, $info, $intermediate_file];
}

function process_images_fail(
	string	$command,
	string	$src,
	int		$rc,
	?string	$stderr
): void {
	$dst	 = uniqid('/tmpdisk/__process_images_failed_', true);
	$log	 = "$dst.log";
	$ok_copy = copy($src, $dst);
	$stderr	 = utf8_mytrim($stderr);
	ob_start();
	echo	'process_images on ', gethostname(), ($rc ? ' failed' : ' force-failed')."\n\n",
			'source file ', ($ok_copy ? '' : 'failed to be ')."copied to $dst\n\n",
			"this log file can also be found at $log\n\n",
			"command: $command\n\n",
			"rc: $rc\n\n",
			"stderr:\n\n",
			$stderr,"\n\n",
			"execute_logs:\n\n";
	print_rr(get_execute_logs(), force: true);
	echo	"\n\nglobals:\n\n";
	print_rr([
		'_SERVER'	=> $_SERVER,
		'_REQUEST'	=> $_REQUEST,
		'_POST'		=> $_POST,
		'_COOKIES'	=> $_COOKIE,
	],	force: true);
	file_put_contents($log, $message = ob_get_clean());

	mail_log($message, subject: 'process_images failed', error_log: 'ERROR');

	static $__warned = false;
	if (!$__warned
	&&	!CLI
	&&	$rc
	) {
		$__warned = true;
		register_error('execute:error:something_went_wrong_LINE', DO_UBB);
	}
}
