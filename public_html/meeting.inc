<?php

function perform_commit(): ?bool {
	switch ($_REQUEST['ACTION']) {
	default:
	case null:	return null;
	case 'join':	return meeting_join();
	case 'leave':	return meeting_leave();
	case 'commit':	return meeting_commit();
	case 'remove':	require_once '_remove.inc';
			return remove_element();
	}
}

function display_body(): void {
	switch ($_REQUEST['ACTION']) {
	default:
	case null:
	case 'archive':		meeting_display_overview();	return;
	case 'single':
	case 'comment':
	case 'comments':
	case 'join':
	case 'leave':
	case 'commit':		meeting_display_single();	return;
	case 'multi':		meeting_display_multi();	return;
	case 'form':		meeting_display_form();		return;
	}
}

function meeting_display_multi(): void {
	if (!($partyid = require_idnumber($_REQUEST, 'subID'))) {
		return;
	}
	$party = memcached_party_and_stamp($partyid);
	if ($party === false) {
		return;
	}
	if (!$party) {
		register_error('party:error:nonexistent_LINE', ['ID' => $partyid]);
		return;
	}
	layout_section_header(Eelement_plural_name('meeting').' @ '.($partylink = get_element_link('party', $partyid)));

	$meetings = memcached_rowuse_array(['meeting', 'meet'],'
		SELECT MEETINGID, HOUR, MINS, meeting.USERID, COUNT(*) AS CNT
		FROM meeting
		LEFT JOIN meet USING (MEETINGID)
		WHERE meeting.PARTYID = '.$partyid.'
		GROUP BY MEETINGID
		ORDER BY HOUR ASC, MINS ASC'
	);
	if (!$meetings) {
		if ($meetings !== false) {
			?><div class="block"><?= __('meeting:info:no_meetings_for_party_LINE', DO_UBB, ['PARTYID' => $partyid]); ?></div><?
		}
		return;
	}

	layout_open_box('meeting');
	layout_box_header($partylink,_datedaytime_get($party['STAMP']));
	layout_open_table(TABLE_FULL_WIDTH);
	layout_start_header_row();
	layout_header_cell_right(Eelement_plural_name('visitor'));
	layout_header_cell_center(Eelement_name('meeting'));
	layout_header_cell(Eelement_name('organizer'));
	layout_stop_header_row();

	foreach ($meetings as $meeting) {
		extract($meeting);
		layout_start_rrow_right(CELL_RIGHT_SPACE);
		echo $CNT;
		layout_next_cell(class: 'center');
		?><a href="/meeting/<?= $MEETINGID ?>"><? printf('%02d:%02d', $HOUR, $MINS); ?></a><?
		layout_next_cell();
		if ($USERID) {
			echo get_element_link('user', $USERID);
		}
		layout_stop_row();
	}
	layout_close_table();
}

function meeting_display_menu(): void {
	layout_open_menu();
	layout_menuitem(Eelement_name('overview'),	'/meeting',		!$_REQUEST['ACTION']);
	layout_menuitem(Eelement_name('archive'),	'/meeting/archive',	$_REQUEST['ACTION'] === 'archive');
	layout_close_menu();

	if (!have_user()) {
		return;
	}
	if (!$_REQUEST['sID']
	&&	$_REQUEST['ACTION'] !== 'form'
	) {
		layout_open_menu();
		layout_menuitem(__C('action:add'), '/meeting/form');
		layout_close_menu();
	}
}

function meeting_display_single_menu($meeting): void {
	if (have_admin('meeting')
	||	(	have_self($meeting['USERID'])
		&&	db_single('party','SELECT STAMP > '.CURRENTSTAMP.' FROM party WHERE PARTYID = '.$meeting['PARTYID'])
		)
	) {
		layout_open_menu();
		layout_menuitem(__C('action:change'), '/meeting/'.$meeting['MEETINGID'].'/form');
		layout_continue_menu();
		show_element_menuitems();
		layout_close_menu();
	}
}

function meeting_display_overview(): void {
	layout_show_section_header();
	
	require_once '_meetinglist.inc';
	$meetinglist = new meetinglist;

	if ($_REQUEST['ACTION'] === 'archive') {
		switch ($_REQUEST['SUBACTION']) {
		default:
			not_found();
			return;

		case 'organization':
			if (!$_REQUEST['ssID']) {
				not_found();
				return;
			}	
			$meetinglist->at_parties_by_organization($_REQUEST['ssID']);
			break;

		case null:
			$meetinglist->select_past();
			break;
		}
	} else {
		$meetinglist->select_future();
	}
	
	$meetinglist->order_reverse_chronologically();
	$meetinglist->show_date = true;
	$meetinglist->show_time = true;
	$meetinglist->show_count = true;
	$meetinglist->show_organization = true;
	$meetinglist->show_header = true;
	$meetinglist->query();

	meeting_display_menu();

	layout_open_box('meeting');
	$meetinglist->display();
	layout_close_box();
}

function meeting_display_single(): void {
	if (!($meetingid = require_idnumber($_REQUEST, 'sID'))) {
		return;
	}
	$meeting = db_single_assoc(['meeting', 'user_account', 'party', 'location', 'city'],'
		SELECT	meeting.*,
			party.NAME, STAMP, party.LOCATIONID, party.CITYID,
			(SELECT COUNT(*) FROM meet WHERE meet.MEETINGID = meeting.MEETINGID) AS VISITOR_CNT
		FROM meeting
		LEFT JOIN party ON party.PARTYID = meeting.PARTYID
		WHERE MEETINGID = '.$meetingid
	);
	if (!$meeting) {
		if ($meeting !== false) {
			register_error('meeting:error:nonexistent_LINE', ['ID' => $meetingid]);
		}
		return;
	}
	$party= memcached_party_and_stamp($meeting['PARTYID']);
	if (!$party) {
		if ($party !== false) {
			register_error('party:error:nonexistent_LINE', ['ID' => $meeting['PARTYID']]);
		}
		return;
	}
	layout_open_section_header();
	echo escape_utf8($party['NAME']);
	?> meeting<?
	if (have_admin('meeting')) {
		?> <small class="light"><?= MIDDLE_DOT_ENTITY ?> <?= $meetingid; ?></small><?
	}
	layout_continue_section_header();
	layout_display_contacts('meeting',$meetingid);
	layout_close_section_header();

	meeting_display_menu();
	meeting_display_single_menu($meeting);

	layout_open_box('meeting');
	layout_open_box_header();
		?>Meeting op <? echo get_element_link('party',$meeting['PARTYID']);
	layout_close_box_header();
	
	layout_open_table(TABLE_VTOP);
	layout_start_row();
		echo Eelement_name('location');
		layout_field_value();
		if ($meeting['LOCATIONID']) {
			echo get_element_link('location', $meeting['LOCATIONID']);
		} elseif ($meeting['CITYID']) {
			echo get_element_link('city', $meeting['CITYID']);
		} else {
			echo __C('status:unknown');
		}
	layout_restart_row();
		echo Eelement_name('organizer');
		layout_field_value();
		if ($meeting['USERID']) {
			echo get_element_link('user', $meeting['USERID']);
		} else {
			echo __('field:unknown');
		}
	layout_restart_row();
		echo __C('nowandsoon:when');
		layout_field_value();
		_dateday_display($party['STAMP']);

	layout_restart_row();
		echo Eelement_name('time');
		layout_field_value();
		printf("%02ld:%02ld", $meeting['HOUR'], $meeting['MINS']);

	layout_restart_row();
		echo __C('nowandsoon:where');
		layout_field_value();
		echo make_all_html($meeting['DESCR'], UBB_UTF8);

	layout_stop_row();
	layout_close_table();
	layout_close_box();

	// going there?
	if (have_user()
	&&	!have_self($meeting['USERID'])
	) {
		// am I on the list?
		$amgoing = db_single('meet','SELECT 1 FROM meet WHERE MEETINGID='.$meetingid.' AND USERID='.CURRENTUSERID);
		
		?><p><?
		if ($amgoing) {
			?><a href="/meeting/<?= $meetingid;
			?>/leave">Ik <?
			if ($meeting['STAMP'] > CURRENTSTAMP) {
				?>ga<?
			} else {
				?>ging<?
			}
			?> toch niet naar deze meeting</a><?
		} else {
			?><a href="/meeting/<?= $meetingid;
			?>/join">Ik <?
			if ($meeting['STAMP'] > CURRENTSTAMP) {
				?>ga<?
			} else {
				?>ging<?
			}
			?> naar deze meeting</a>!<?
		}
		?></p><?
	}

	// any visitors?
	
	if ($meeting['VISITOR_CNT']) {
		require_once '_userlist.inc';
		$userlist = new _userlist;
		$userlist->going_to_meeting($meetingid);
		$userlist->show_heart = true;
		if (!$userlist->query())  {
			return;
		}
		$visible_cnt = $userlist->size();
		
		layout_open_box('meeting');
		layout_box_header($visible_cnt.' / '.$meeting['VISITOR_CNT'].' '.element_name('visitor',$visible_cnt));
		$userlist->hilite_self = true;
		$userlist->display();
		layout_close_box();
	}
	require_once '_commentlist.inc';
	$cmts = new _commentlist;
	$cmts->item($meeting);
	$cmts->display();
}
function meeting_display_form() {
	layout_section_header(Eelement_name('meeting').' '.MIDDLE_DOT_ENTITY.' '.__($_REQUEST['sID'] ? 'action:change' : 'action:add'));

	include_js('js/form/meeting');

	meeting_display_menu();

	if (isset($_REQUEST['MEETINGID'])) {
		if (!require_idnumber($_REQUEST,'MEETINGID')) {
			return;
		}
		if (false === ($meeting = db_single_assoc('meeting','SELECT * FROM meeting WHERE MEETINGID='.$_REQUEST['MEETINGID']))) {
			return;
		}
		if (!$meeting) {
			not_found(); return;
		}
		$party = memcached_party_and_stamp($meeting['PARTYID']);
		if ($party === false) {
			return;
		}
		if (!$party) {
			return not_found('party',$meeting['PARTYID']);
		}
		if (!have_admin('meeting')) {
			if (!have_self($meeting['USERID'])) {
				return;
			}
			// only admins can change old meetings
			if ($party['STAMP'] < CURRENTSTAMP) {
				_error('Meeting is geweest, alleen een medewerker kan nog aanpassingen verrichten.');
				return;
			}
		} elseif (CURRENTUSERID != $meeting['USERID']) {
			$asadmin = true;
		}
		$singleparty = $meeting['PARTYID'];
	} else {
		$meeting = null;
		if ($_REQUEST['subID']) {
			$going = all_going_to_party_askonce($_REQUEST['subID']);
			if ($going != GOING_CERTAIN) {
				if ($going) {
					_error('Je moet wel zeker weten dat je naar dit feest gaat!');
				} else {
					_error('Je staat niet ingeschreven voor dit feest!');
				}
				return;
			}
			// FIXME: check whether subID is on going list
			$party = memcached_party_and_stamp($_REQUEST['subID']);
			if ($party === false) {
				return;
			}
			if (!$party) {
				_error('Feest met ID '.$_REQUEST['subID'].' niet gevonden!');
				return;
			}
			$singleparty = $_REQUEST['subID'];
		}
	}
	if (!isset($meeting)) {
		if (!isset($singleparty)) {
			if (!empty($_SERVER['HTTP_REFERER'])) {
				error_log('futurelist uri: '.$_SERVER['REQUEST_URI'].', referred from: '.$_SERVER['HTTP_REFERER'],0);
			}
			$futurelist = db_rowuse_hash(
				array('going','party'),'
				SELECT going.PARTYID,NAME,STAMP
				FROM going
				JOIN party USING (PARTYID)
				WHERE MAYBE=0
				  AND STAMP>'.CURRENTSTAMP.'
				  AND going.USERID='.CURRENTUSERID
			);
			if ($futurelist === false) {
				return;
			}
			if (!$futurelist) {
				warning('Je kunt alleen een meeting organiseren voor &eacute;&eacute;n van de feesten in je eigen agenda.<br />'.
					'Zoek in de agenda naar het feest waar je een meeting voor wilt organiseren en voeg die toe aan je persoonlijke agenda.'
				);
				return;
			}
		}
		$existing = memcached_rowuse_array(
			array('meet','meeting'),'
			SELECT meeting.USERID,PARTYID,meeting.MEETINGID,HOUR,MINS,DESCR,COUNT(*) AS CNT
			FROM meeting
			JOIN meet USING (MEETINGID)
			WHERE PARTYID IN ('.(isset($singleparty) ? $singleparty : implodekeys(',',$futurelist)).')
			GROUP BY MEETINGID
			ORDER BY PARTYID,HOUR'
		);
		if ($existing) {
			if (isset($singleparty)) {
				sort_meetings($existing,$party['STAMP']);
			}
			foreach ($existing as $option) {
				if (!isset($counts[$option['PARTYID']])) {
					$counts[$option['PARTYID']] = 1;
				} else {
					++$counts[$option['PARTYID']];
				}
			}
			$partyid = 0;
			foreach ($existing as $option) {
				if ($partyid != $option['PARTYID']) {
					if (!isset($party)) {
						$party = $futurelist[$option['PARTYID']];
					}
					if ($partyid) {
						?></ul><?
						layout_close_box();
					}
					$partyid = $option['PARTYID'];
					layout_open_box((isset($singleparty) ? null : 'hidden ').'white','meetingsfor'.$partyid);
					?><p class="warning">Er <?
					if ($counts[$partyid] > 1) {
						?>zijn al <span class="underline"><?= $counts[$partyid];
						?> meetings</span><?
						$join = 'een';
					} else {
						?>is al <span class="underline">een meeting</span><?
						$join = 'de';
					}
					?> voor <i><?= get_element_link('party', $partyid, $party['NAME']);
					?></i>.</p><?
					?><p>Weet je zeker dat je er nog een wilt toevoegen? <?
					?>Je kunt ook deelnemen aan <?= $join; ?> bestaande meeting!</p><?
					?><ul class="meetings"><?
				}
				?><li><b><a href="/meeting/<?= $option['MEETINGID'];
				?>"><?= escape_utf8($party['NAME']);
				?> meeting om <span class="underline"><?= $option['HOUR'];
				?>:<?= $option['MINS'];
				?></span></a></b><br /><?
				?><small class="light"><?= $option['CNT'];
				?> <?
				echo element_name('attendee',$option['CNT']);
				if ($option['USERID']) {
					?>, <?= element_name('organizer') ?>: <? echo get_element_link('user',$option['USERID']);
				}
				?></small><br /><?= make_all_html($option['DESCR'], UBB_UTF8);
				?></li><?
			}
			if ($partyid) {
				?></ul><?
				layout_close_box();
			}
		}
	}
	?><form onsubmit="return submitMeetingForm(this)" method="post" action="/meeting<?
	if ($meeting) {
		?>/<? echo $meeting['MEETINGID'];
	}
	?>/commit"><?
	layout_open_box('meeting');
	layout_open_table(TABLE_VTOP);
	layout_start_row();
		?>Feest<?
		layout_field_value();
		if (isset($singleparty)) {
			?><input type="hidden" name="PARTYID" value="<?= $singleparty; ?>" /><?
			echo get_element_link('party', $singleparty, $party['NAME']);
		} else {
			?><select name="PARTYID" onchange="changeParty(this.value)"><option value="0">kies feest (alleen feesten uit je persoonlijke agenda)</option><?
			foreach ($futurelist as $partyid => $party) {
				?><option<?
				if ($meeting
				&&	$meeting['PARTYID'] === $partyid
				) {
					?> selected="selected"<?
				}
				?> value="<?= $partyid;
				?>"><? _date_display($party['STAMP']);
				?>: <?= escape_utf8($party['NAME']);
				?></option><?
			}
			?></select><?
		}
			
	if ($meeting && have_admin('meeting')) {
		layout_restart_row();
			?>Organisator<?
			layout_field_value();
			?><select name="USERID"><option value="0"></option><?
			$users = db_simple_hash(
				array('meet','user_account'),'
				SELECT meet.USERID,NICK
				FROM meet
				JOIN user_account USING (USERID)
				WHERE MEETINGID='.$meeting['MEETINGID']
			);
			if ($users) {
				natcasesort($users);
				foreach ($users as $userid => $nick) {
					?><option<?
					if ($userid === $meeting['USERID']) {
						?> selected="selected"<?
					}
					?> value="<?= $userid;
					?>"><?= escape_specials($nick);
					?></option><?
				}
			}
			?></select><?
	}
	
	layout_restart_row();
		?>Tijdstip<?
		layout_field_value();
		if ($meeting) {
			_time_display_select(null,$meeting['HOUR'],$meeting['MINS']);
		} else {
			_time_display_select();
		}	
	layout_restart_row();
		require_once '_bubble.inc';
		$bubble = new bubble(BUBBLE_BLOCK);
		$bubble->catcher_and_title('Waar');
		$bubble->content();
		?>Geef hier een duidelijk omschrijving waar men elkaar moet gaan treffen.<br /> <?
		?>Zorg er voor dat dit een herkenbare plaats is.<br /><?
		?>Bijvoorbeeld: &quot;Onder aan de trap&quot; <?
		?>(indien er maar 1 trap aanwezig is in de locatie) of &quot;Bij de ingang&quot;<?
		$bubble->display();
		layout_field_value();
		?><textarea cols="80" rows="15" name="DESCR"><?= escape_utf8($meeting['DESCR']); ?></textarea><?
	layout_stop_row();
	layout_close_table();
	layout_close_box();
	?><p><input type="submit" value="<?= __($meeting ? 'action:change' : 'action:add'); ?>" /></p></form><?
}

function meeting_join(): bool {
	if (!require_user()
	||	!require_idnumber($_REQUEST,'MEETINGID')
	||	!require_referer()
	) {
		return false;
	}
	$partyid = db_single('meeting','SELECT PARTYID FROM meeting WHERE MEETINGID='.$_REQUEST['MEETINGID']);
	if ($partyid === false) {
		return false;
	}
	if (!$partyid) {
		_error('Meeting met ID '.$_REQUEST['MEETINGID'].' is niet aan een correct feest gekoppeld!');
		return false;
	}
	if (!db_insert('meet','
		INSERT IGNORE INTO meet SET
			CSTAMP		='.CURRENTSTAMP.',
			USERID		='.CURRENTUSERID.',
			MEETINGID	='.$_REQUEST['MEETINGID'])
	) {
		return false;
	}
	if (!db_affected()) {
		# meet was there already
		warning('Je staat al ingeschreven voor deze meeting!');
		return true;
	}
	switch (going_to_party_nocache($partyid)) {
	case GOING_CERTAIN:
		# no change needed
		break;
	case GOING_MAYBE:
		if (!db_insert('going_log','
			INSERT INTO going_log
			SELECT *,"upgrade" AS ACTION,'.CURRENTSTAMP.' AS ASTAMP FROM going
			WHERE MAYBE	=0
			  AND USERID	='.CURRENTUSERID.'
			  AND PARTYID	='.$partyid)
		||	!db_insupd('going','
			INSERT INTO going SET
				MAYBE	=0,
				CSTAMP	='.CURRENTSTAMP.',
				USERID  ='.CURRENTUSERID.',
				PARTYID ='.$partyid.'
			ON DUPLICATE KEY UPDATE
				MAYBE	=0,
				CSTAMP	='.CURRENTSTAMP)
		) { 
			return false;
		}
		break;
	case NOT_GOING:
		if (!db_insert('going','
			INSERT IGNORE INTO going SET
				MAYBE	=0,
				CSTAMP	='.CURRENTSTAMP.',
				USERID  ='.CURRENTUSERID.',
				PARTYID ='.$partyid)
		) {
			return false;
		}
		break;
	}
	register_notice('meeting:notice:joined_LINE');
	return true;
}
function meeting_leave(): bool {
	if (!require_user()
	||	!require_idnumber($_REQUEST,'MEETINGID')
	||	!require_referer()
	||	!db_insert('meet_log','
		INSERT INTO meet_log
		SELECT * FROM meet
		WHERE USERID	='.CURRENTUSERID.'
		  AND MEETINGID	='.$_REQUEST['MEETINGID'])
	||	!db_delete('meet','
		DELETE FROM meet
		WHERE USERID	='.CURRENTUSERID.'
		  AND MEETINGID ='.$_REQUEST['MEETINGID'])
	) {
		return false;
	}
	register_notice('meeting:notice:left_LINE');
	return true;
}

function meeting_display_open() {
	layout_open_section_header();
	?>Reeds bestaande meetings<?
	layout_close_section_header();
	
	if (!require_user()
	||	!require_idnumber($_POST,'PARTYID')
	) {
		return;
	}
	$party = memcached_party_and_stamp($_POST['PARTYID']);
	if (!$party) {
		_error('Feest met ID '.$_POST['PARTYID'].' is niet opvraagbaar!');
		return;
	}
	if (!isset($GLOBALS['__open_meetings'])) {
		_error('Geen bestaande meetings gevonden!');
		return;
	}
	$size = sizeof($GLOBALS['__open_meetings']);
	layout_open_box('white');
	?><p>Er <?
	if ($size > 1) {
		?>zijn al <?= $size;
		?> bestaande meetings<?
	} else {
		?>is al een bestaande meeting<?
	}
	?> voor <?= get_element_link('party', $_POST['PARTYID'], $party['NAME']);
	?> op <? _dateday_display($party['STAMP']);
	?>.</p><?
	layout_close_box();
}
function meeting_commit(): bool {
	if (!require_user()
	||	!($meetingid = optional_number($_REQUEST, 'MEETINGID'))
	||	!require_idnumber($_POST, 'PARTYID')
	||	false === require_number($_POST, 'HOUR')
	||	false === require_number($_POST, 'MINS')
	||	!require_something($_POST, 'DESCR', utf8: true)
	) {
		return false;
	}
	if (!($meeting_admin = have_admin('meeting'))) {
		if (!going_to_party_askonce($_REQUEST['PARTYID'])) {
			register_error('meeting:error:only_agenda_meetings_LINE');
			return false;
		}
	}
	$setlist[] = 'PARTYID	='.$_REQUEST['PARTYID'];
	$setlist[] = 'HOUR	='.$_REQUEST['HOUR'];
	$setlist[] = 'MINS	='.$_REQUEST['MINS'];
	$setlist[] = 'DESCR	="'.addslashes(utf8_mytrim($_POST['DESCR'])).'"';

	if ($meeting_admin) {
		// allow admin to change owner
		if (isset($_POST['USERID'])) {
			if (!($userid = require_idnumber($_POST, 'USERID'))) {
				return false;
			}
			$setlist[] = 'USERID = '.$userid;
		}
	}
	if ($meetingid) {
		$setlist[] = 'MSTAMP	='.CURRENTSTAMP;
		$setlist[] = 'MUSERID	='.CURRENTUSERID;
			
		if (!db_insert('meeting_log','
			INSERT INTO meeting_log
			SELECT * FROM meeting
			WHERE MEETINGID = '.$meetingid)
		||	!db_update('meeting','
			UPDATE meeting SET '.implode(',',$setlist).'
			WHERE MEETINGID = '.$meetingid.
			(	$meeting_admin
			?	null
			:	' AND USERID = '.CURRENTUSERID))
		) {
			return false;
		}
		register_notice('meeting:notice:meeting_changed_LINE');

	} else {
		$setlist[] = 'CSTAMP='.CURRENTSTAMP;
		$setlist[] = 'USERID='.CURRENTUSERID;
		
		if (!db_insert('meeting','
			INSERT INTO meeting SET '.implode(',',$setlist))
		) {
			return false;
		}
		
		$meetingid = $_REQUEST['sID'] = db_insert_id();

		// owner of meeting should be on the list of meeters
		db_insert('meet','
			INSERT INTO meet SET
				CSTAMP		='.CURRENTSTAMP.',
				MEETINGID	='.$meetingid.',
				USERID		='.CURRENTUSERID
		);
		register_notice('meeting:notice:meeting_added_LINE');
	}
	return true;
}
