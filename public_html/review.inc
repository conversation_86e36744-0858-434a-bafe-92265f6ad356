<?php

require_once '_genrelist.inc';
require_once '_itemlist.inc';
require_once '_vote.inc';

function display_header() {
	require_once '_feed.inc';
	show_feed('review',FEED_HEADER);
}

function perform_commit() {
	switch ($_REQUEST['ACTION']) {
	default:
	case null:		return;
	case 'commit':		return review_commit();
	case 'accept':
	case 'unaccept':	require_once '_accept.inc'; return item_set_accept($_REQUEST['ACTION']);
	case 'remove':		require_once '_remove.inc'; return remove_element();
	}
}
function display_body() {
	switch ($_REQUEST['ACTION']) {
	default:		not_found(); return;
	case 'remove':
	case null:		return review_display_overview();
	case 'single':		return review_display_single();
	case 'commit':
	case 'comment':
	case 'comments':
	case 'votes':		return review_display_single();
	case 'top':		return review_display_top();
	case 'form':		return review_display_form();
	case 'archive':		return review_display_archive();
	case 'search':
	case 'searchresult':	return review_display_search();
	}
}
function review_increase_counter() {
	if (!have_idnumber($_REQUEST,'sID')) {
		return;
	}
	counthit('review',$_REQUEST['sID']);
}
function review_display_search() {
	require_once '_genrelist.inc';

	layout_open_section_header();
	?>Recensie zoeken<?
	layout_close_section_header();

	_itemlist_display_menu();
	?><form id="simple" onsubmit="return submitForm(this)" method="get" action="/review/searchresult"><?
	?><p><?= __C('search:for_title'); ?>: <?
	?><input onchange="getobj('advancedtitle').value=this.value" type="search" autosave="reviewname" name="NAME" autofocus="autofocus" required="required" /> <?
	?><input type="submit" name="SEARCH" value="<?= __('action:search'); ?>" /><?
	?> &middot; (<span onclick="hide('simple');unhide('advanced');" class="unhideanchor"><?= __('search:advanced'); ?></span>)</p><?
	?></form><?

	?><form accept-charset="utf-8" id="advanced" class="hidden" onsubmit="return submitForm(this)" method="get" action="/review/searchresult"><?
	?><input type="hidden" name="ADVANCED" value="1" /><?
	layout_open_box('white');
	layout_open_table('vtop');
	layout_start_row();
	?><label><input name="CB_NAME" type="checkbox" onclick="setdisplay('advancedtitle',this.checked);" value="1" /><?
	echo Eelement_name('title');
	?></label><?
	layout_field_value();
	?><input id="advancedtitle" class="hidden" type="search" autosave="reviewname" name="NAME" /><?

	layout_restart_row(0,null,'nowrap');
	?><label><input name="CB_GENRES" type="checkbox" onclick="setdisplay('genres',this.checked);" value="1" /><?
	echo Eelement_plural_name('genre');
	?></label><?
	layout_field_value();
	?><div id="genres" class="hidden"><?
	_genrelist_display_checkboxes(have_user() ? user_favourite_genres(CURRENTUSERID) : [], true);
	layout_stop_row();
	layout_close_table();
	layout_close_box();
	?><div class="block"><input type="submit" value="<?= __('action:search'); ?>" /></div><?
	?></form><?

	if (!isset($_REQUEST['NAME'])) return;

	$tables = array('review');

	if (isset($_REQUEST['ADVANCED'])) {
		if (isset($_REQUEST['CB_NAME'])
		&&	require_anything_trim($_REQUEST, 'NAME', utf8: true)
		) {
			$wheres[] = 'TITLE LIKE "%'.addslashes(str_replace(' ','%',$_REQUEST['NAME'])).'%"';
		}
		if (isset($_REQUEST['CB_GENRES'])
		&&	have_number_array($_REQUEST,'GID')
		) {
			$join = ' JOIN review_genre USING (REVIEWID) ';
			$tables[] = 'review_genre';
			$wheres[] = 'GID IN ('.implode(',',$_REQUEST['GID']).')';
		}
	} else {
		if (!require_something_trim($_REQUEST, 'NAME',null,null,utf8:true)) {
			return;
		}
		$wheres[] = 'TITLE LIKE "%'.addslashes(str_replace(' ','%',$_REQUEST['NAME'])).'%"';
	}
	if (!isset($wheres)) {
		warning('Geen zoekcriteria opgegeven!');
		return;
	}
	$reviews = memcached_rowuse_hash(
		$tables,'
		SELECT DISTINCT REVIEWID,TITLE,PSTAMP
		FROM review
		'.(isset($join) ? $join : null).'
		WHERE '.implode(' AND ',$wheres).'
		ORDER BY PSTAMP DESC
		LIMIT '.($offset = have_idnumber($_REQUEST,'OFFSET') ?: 0).',51'
	);
	if ($reviews === false) {
		return;
	}
	if (!$reviews) {
		?><div><?= __('review:info:no_reviews_found_LINE'); ?></div><?
		return;
	}
	if (($back = count($reviews) > 50)
	||	$offset
	) {
		ob_start();
		?><nav><?
		?><div class="block"><?
		if ($back) {
			$uri = $_SERVER['REQUEST_URI'];
			if (str_contains($uri,'OFFSET=')) {
				$uri = preg_replace('"(OFFSET=\d+)"','OFFSET='.($offset+50),$uri);
			} else {
				$uri = $uri.'&OFFSET='.($offset+50);
			}
			?><div class="l"><a href="<?= $uri ?>" rel="prev"><?= __('pagecontrols:previous') ?></a></div><?
		}
		if ($offset) {
			$offset = $offset > 50 ? $offset - 50 : 0;
			$uri = $_SERVER['REQUEST_URI'];
			if (str_contains($uri,'OFFSET=')) {
				$uri = preg_replace('"(OFFSET=\d+)"','OFFSET='.$offset,$uri);
			} else {
				$uri = $uri.'&OFFSET='.$offset;
			}
			?><div class="r"><a href="<?= $uri ?>" rel="next"><?= __('pagecontrols:next') ?></a></div><?
		}
		?><div class="clear"></div><?
		?></div><?
		?></nav><?
		$menu = ob_get_flush();
	}
	layout_open_table(TABLE_REGULAR | TABLE_CLEAN | TABLE_FULL_WIDTH);
	layout_start_header_row();
	layout_header_cell(Eelement_name('title'));
	layout_header_cell_right(Eelement_name('publication'));
	layout_stop_header_row();
	foreach ($reviews as $reviewid => $review) {
		layout_start_rrow();
		echo get_element_link('review',$reviewid,$review['TITLE']);
		layout_next_cell(class: 'right');
		_date_display($review['PSTAMP']);
		layout_stop_row();
	}
	layout_close_table();
	if (isset($menu)) echo $menu;
}
function review_display_form() {
	if (!require_admin(array('review','reviewer'))) {
		return;
	}
	layout_show_section_header();

	if ($reviewid = have_idnumber($_REQUEST,'sID')) {
		if (!require_obtainlock(LOCK_REVIEW,$reviewid)) {
			return;
		}
		$review = db_single_assoc('review','SELECT * FROM review WHERE REVIEWID='.$reviewid,DB_USE_MASTER);
		if ($review === false) {
			return;
		}
		if (!$review) {
			not_found(); return;
		}
		require_once '_element_access.inc';
		if (!may_change_element('review',$reviewid,null,true,$review)) {
			return;
		}
	} else {
		$review = null;
	}
	include_js('js/form/review');
	?><form<?
	?> accept-charset="utf-8"<?
	?> onsubmit="return submitForm(this)"<?
	?> method="post"<?
	?> action="/review<?
	if (isset($review)) {
		?>/<? echo $reviewid;
	}
	?>/commit"><?

	layout_open_box('review');
	layout_open_table('vtop fw');
	layout_start_row();
		echo Eelement_name('title');
		layout_field_value();
		?><input required="required" autofocus="autofocus" maxlength="160" type="text" name="TITLE" value="<? if (isset($review)) echo escape_utf8($review['TITLE']); ?>" /><?

	layout_restart_row();
		?>Spotify<?
		layout_field_value();
		show_input([
			'name'		=> 'SPOTIFY_EMBED',
			'type'		=> 'text',
			'class'		=> 'regular',
			'value_utf8'	=> $review,
		]);

	layout_restart_row();
		?>iTunes<?
		layout_field_value();
		show_input([
			'name'		=> 'ITUNES',
			'type'		=> 'text',
			'class'		=> 'regular',
			'value_utf8'	=> $review && $review['ITUNES_TYPE'] ? $review['ITUNES_TYPE'].'/'.$review['ITUNES_TITLE'].'/id'.$review['ITUNES_ID'] : null,
		]);

	layout_restart_row();
		?><a target="_blank" href="htt://www.bol.com/">Bol.com</a> product ID<?
		layout_field_value();
		show_input([
			'name'		=> 'BOLCOMPRODID',
			'type'		=> 'number',
			'class'		=> 'regular',
			'value'		=> $review && $review['BOLCOMPRODID'] ? $review['BOLCOMPRODID'] : null,
		]);

	layout_restart_row();
		echo Eelement_name('type');
		layout_field_value();
		?><select name="TYPE"><?
			?><option value="cd"><?= __('review:type:cd'); ?></option><?
			?><option<? if (isset($review) && $review['TYPE'] == 'dvd') echo ' selected="selected"'; ?> value="dvd"><?= __('review:type:dvd'); ?></option><?
			?><option<? if (isset($review) && $review['TYPE'] == 'book') echo ' selected="selected"'; ?> value="book"><?= __('review:type:book'); ?></option><?
		?></select><?
	layout_restart_row();
		?><label for="retro">Retro</label><?
		layout_field_value();
		?><input type="checkbox" name="RETRO" id="retro" value="1"<?
		if (!empty($review['RETRO'])) {
			?> checked"<?
		}
		?>><?
	if (have_admin('review')) {
		require_once '_authorlist.inc';
		layout_restart_row();
			?><label for="accepted"><?= __C('attrib:accepted'); ?></label><?
			layout_field_value();
			?><input type="checkbox" value="1" name="ACCEPTED"<? if (isset($review) && $review['ACCEPTED']) echo ' checked';
			?> id="accepted"><?

		layout_restart_row();
			echo Eelement_name('author');
			layout_field_value();
			authorlist_display_select(!empty($review['BYUSERID']) ? $review['BYUSERID'] : 0);
	}
	layout_restart_row();
		?><label for="direct"><?= Eelement_name('publication') ;?></label><?
		layout_field_value();
		show_publication($review);

	layout_restart_row();
		echo Eelement_name('teaser');
		layout_field_value();
		?><textarea class="growToFit" required="required" name="TEASER" cols="40" rows="10"><? if (isset($review)) echo escape_utf8($review['TEASER']);
		?></textarea><?

	layout_restart_row();
		echo Eelement_name('review');
		layout_field_value();
		?><textarea class="growToFit" required="required" name="BODY" cols="80" rows="20"><? if (isset($review)) echo escape_utf8($review['BODY']);
		?></textarea><?

	layout_restart_row();
		echo Eelement_name('score');
		layout_field_value();
		?><input type="number" data-valid="number" min="0" max="100" class="three_digits" name="RATING" value="<? if (isset($review)) echo $review['RATING']; ?>" /><?

	$max_discs = 5;
	layout_restart_row();
		?>Aantal discs<?
		layout_field_value();
		?><select name="DISC_CNT" onchange="changeDiscs(this.value)"><?
		for ($cnt = 1; $cnt <= $max_discs; ++$cnt) {
			?><option<?
			if (isset($review) && $review['DISC_CNT'] == $cnt) {
				?> selected="selected"<?
			}
			?> value="<?= $cnt;
			?>"><?= $cnt;
			?></option><?
		}
		?></select><?
	$disccnt = isset($review) ? $review['DISC_CNT'] : 1;

	layout_restart_row();
		?>Disc titels<?
		layout_field_value();
		for ($cnt = 1; $cnt <= 5; ++$cnt) {
			?><input id="disctitle<?= $cnt; ?>"<?
			if ($disccnt < $cnt) {
				?> class="hidden"<?
			}
			?> placeholder="<?= Eelement_name('title') ?> <?= $cnt ?>"<?
			?> type="text" name="ELEM<?= $cnt;
			?>_TITLE" value="<? if (isset($review)) echo escape_utf8($review['ELEM'.$cnt.'_TITLE']);
			?>"> <?
		}
	layout_restart_row();
		echo Eelement_plural_name('tracklist');
		?><span class="light"> en/of </span><?
		echo Eelement_name('content');
		layout_field_value();
		for ($cnt = 1; $cnt <= $max_discs; ++$cnt) {
			?><textarea<?
			?> id="discbody<?= $cnt; ?>"<?
			?> class="growToFit<?
			if ($disccnt < $cnt) {
				?> hidden<?
			}
			?>" placeholder="<?= Eelement_name('content') ?> <?= $cnt ?>"<?
			?> name="ELEM<?= $cnt;
			?>_BODY" cols="30" rows="20"><? if (isset($review)) echo escape_utf8($review['ELEM'.$cnt.'_BODY']);
			?></textarea> <?
		}
	layout_restart_row();
		echo Eelement_plural_name('genre');
		layout_field_value();
		show_genre_selection(
			$reviewid ? db_boolean_hash('review_genre', 'SELECT GID FROM review_genre WHERE REVIEWID = '.$review['REVIEWID']) : null
		);

	layout_stop_row();
	layout_close_table();
	layout_close_box();
	?><div class="block"><input type="submit"  value="<?= __(isset($review) ? 'action:change' : 'action:add'); ?>" /></div></form><?
}

function review_display_overview() {
	_itemlist_display_overview_header();
	_itemlist_display_menu();
	_itemlist_display_prelist();
	_itemlist_display();
}

function review_display_archive() {
	_itemlist_display_archive_header();
	_itemlist_display_menu();
	_itemlist_display_archive();
}

function review_display_top() {
	layout_open_section_header();
	?>Recensie top 20 afgelopen kwartaal<?
	layout_close_section_header();

	_itemlist_display_menu();

	$tops = memcached_rowuse_hash(
		'review','
		SELECT REVIEWID,RATING,TITLE
		FROM review
		WHERE ACCEPTED=1
		  AND CSTAMP>'.(CURRENTSTAMP-3*30*24*3600).'
		  AND RATING!=0
		ORDER BY RATING DESC
		LIMIT 20'
	);
	if (!$tops) {
		return;
	}
	layout_open_box('review');
	layout_open_box_header();
	?>Top 20 recensies uit het afgelopen kwartaal<?
	layout_close_box_header();
	layout_open_table(TABLE_CLEAN);
	layout_start_header_row();
	layout_header_cell_right('#');
	layout_header_cell(Eelement_name('review'),CELL_ALIGN_LEFT);
	layout_header_cell_right(Eelement_name('score'));
	layout_stop_header_row();
	$position = 0;
	foreach ($tops as $reviewid => $top) {
		layout_start_rrow_right();
		echo ++$position; ?>.<?
		layout_next_cell();
		echo get_element_link('review',$reviewid,$top['TITLE']);
		layout_next_cell(class: 'right');
		echo $top['RATING'];
		layout_stop_row();
	}
	layout_close_table();
	layout_close_box();
}

function review_display_single() {
	require_once '_connect.inc';
	require_once '_uploadimage.inc';
	if (!require_idnumber($_REQUEST,'sID')) {
		return false;
	}
	$reviewid = $_REQUEST['sID'];
	$review = memcached_single_assoc_if_not_admin(
		'review',
		'review','
		SELECT	review.USERID,BOLCOMPRODID,ITUNES_TYPE,ITUNES_TITLE,ITUNES_ID,SPOTIFY_EMBED,
			BODY,REVIEWID,TITLE,review.CSTAMP,review.MSTAMP,review.MUSERID,PSTAMP,ACCEPTED,TYPE,BYUSERID,RATING,TEASER,RETRO,
			ELEM1_TITLE,ELEM1_BODY,ELEM2_TITLE,ELEM2_BODY,ELEM3_TITLE,ELEM3_BODY,ELEM4_BODY,ELEM4_TITLE,ELEM5_TITLE,ELEM5_BODY
		FROM review
		WHERE REVIEWID='.$reviewid
	);
	if ($review === false) {
		return false;
	}
	if (!$review) {
		not_found(); return;
	}
	$hashed = _item_hashed($review);

	$is_admin = itemlist_is_admin($review);
	if (!$is_admin
	&&	!$hashed
	) {
		if ($review['PSTAMP'] > CURRENTSTAMP
		&&	!is_facebook_bot()
		) {
			register_error('review:error:not_yet_publicized_LINE',array('ID'=>$reviewid));
			return false;
		}
		if (!$review['ACCEPTED']) {
			register_error('review:error:not_accepted_LINE',array('ID'=>$reviewid));
			return false;
		}
	}

	layout_show_section_header();

	_itemlist_display_menu($review);

	require_once '_ticketlist.inc';
	show_connected_tickets();

	if ($is_admin) {
		_connect_display_form('review',$review['REVIEWID']);
	}

	if (!SMALL_SCREEN
	&&	(	$is_admin
		||	$hashed
		)
	&&	$review['TEASER']
	) {
		require_once '_itemlist.inc';
		show_item_teaser('review',$reviewid,$review);
	}

	?><article itemscope itemtype="https://schema.org/Review"><?

	require_once '_drag.inc';
	open_drag();

	layout_open_box($review['ACCEPTED'] ? 'review' : 'unaccepted review');
	?><header><?
	layout_open_box_header();
 	?><h1 itemprop="headline"><span itemprop="itemReviewed" itemscope itemtype="https://schema.org/MediaObject"><span itemprop="name"><?
	echo make_all_html($review['TITLE'], UBB_UTF8);
	?></span></span><?
	if ($review['RETRO']) {
		?> <small>(<?= element_name('retro_review') ?>)</small><?
	}
	?></h1><?
	if (get_votes('review',$reviewid)) {
		layout_continue_box_header();
		?><a href="/review/<?= $review['REVIEWID']; ?>/votes#votes"><?= __('ratings:votes'); ?></a><?
	}
	layout_close_box_header();
	?></header><?

	itemlist_show_publish_info($review);

	?><div<?
	if (!SMALL_SCREEN) {
		?> class="right-float"<?
	}
	?>><?
		uploadimage_show('review',$reviewid,UPIMG_SCHEMA | UPIMG_SHOW_HISTORY |(have_admin() ?  UPIMG_LINK_ORIGINAL : 0));
		_connect_display('review',$reviewid);

	ob_start();

	if ($review['ITUNES_TYPE']) {
		require_once '_affiliates.inc';
		?><div class="<?= SMALL_SCREEN ? 'ib' : 'block' ?>"><?
		?><a class="block" target="_blank" href="<?
		echo make_affiliate_url(
			'https://music.apple.com/'.$review['ITUNES_TYPE'].'/'.$review['ITUNES_TITLE'].'/'.$review['ITUNES_ID'],
			'review',
			$reviewid
		);
		?>"><?
		?><img<?
		?> class="zoomover"<?
		?> alt="<?= $title = __('action:view_on_site',['NAME'=>'iTunes']) ?>"<?
		?> title="<?= $title ?>"<?
		?> width="110"<?
		?> height="40"<?
		?> src="<?= STATIC_HOST ?>/images/itunes<?= is_high_res() ?>.png"<?
		?>><?
		?></a><?
		?></div><?
	}
	if ($review['BOLCOMPRODID']) {
		?><div class="<?= SMALL_SCREEN ? 'ib' : 'block' ?>"><?
		?><a target="_blank" href="//partnerprogramma.bol.com/click/click?<?
			?>p=1&amp;<?
			?>s=15892&amp;<?
			?>t=p&amp;<?
			?>pid=<?= $review['BOLCOMPRODID'] ?>&amp;<?
			?>f=PDL&amp;<?
			?>subid=review%3A<?= $reviewid ?>&amp;<?
			?>name=review%3A<?= $reviewid ?>"><?
		?><img<?
		?> class="zoomover"<?
		?> alt="<?= $title = __('action:view_on_site',['NAME'=>'Bol.com']) ?>"<?
		?> title="<?= $title ?>"<?
		?> width="160"<?
		?> height="40"<?
		?> src="<?= STATIC_HOST ?>/images/bolcom<?= (!LITE ? 'd' : null),is_high_res() ?>.png"<?
		?>><?
		?></a><?
		?></div><?
	}

	if ($review['SPOTIFY_EMBED']) {
		require_once '_ubb_preprocess.inc';
		echo make_all_html(_ubb_preprocess('[embed]'.$review['SPOTIFY_EMBED'].'[/embed]'));
	}

	$salestuff = ob_get_clean();

	if ($salestuff && !SMALL_SCREEN) {
		?><div class="center" style="margin-top:1em"><?
		echo $salestuff;
		?></div><?
	}

	?></div><?

	if ($styles = _genrelist_get_item_styles('review', $reviewid)) {
		?><div class="lclr right block"><?= Eelement_name('genre'); ?>: <?= $styles ?></div><?
	}

	include_meta('description', flat_with_entities($review['TEASER'], UBB_UTF8));

	?><div class="hidden" itemprop="description"><?= make_all_html($review['TEASER'], UBB_UTF8) ?></div><?

	?><div class="lclr minp body block" itemprop="reviewBody"><?= make_all_html($review['BODY'], UBB_UTF8, 'review', $reviewid) ?></div><?

	if ($salestuff && SMALL_SCREEN) {
		?><hr class="slim"><?
		?><div class="center tpad"><?
		echo $salestuff;
		?></div><?
	}

	layout_display_alteration_note($review, true);
	layout_close_box();

	close_drag();

	$cnt = 0;
	if (!empty($review['ELEM1_BODY'])) ++$cnt;
	if (!empty($review['ELEM2_BODY'])) ++$cnt;
	if (!empty($review['ELEM3_BODY'])) ++$cnt;
	if (!empty($review['ELEM4_BODY'])) ++$cnt;
	if (!empty($review['ELEM5_BODY'])) ++$cnt;
	if ($cnt) {
		layout_open_box('review');
		layout_box_header(Eelement_name('tracklist',$cnt));

		for ($i = 1; $i <= 5; ++$i) {
			$title = 'ELEM'.$i.'_TITLE';
			$body = 'ELEM'.$i.'_BODY';
			if (empty($review[$body])) {
				continue;
			}
			?><div class="vtop tracklist block"><?

			$parts = [];
			$groups = preg_split('"(?:^|\n+)([^\d\h]+?.*)(?:\n+|$)"u', $review[$body], -1, PREG_SPLIT_DELIM_CAPTURE);

			if (count($groups) === 1) {
				$parts[null] = $review[$body];
			} else {
				$header = null;
				foreach ($groups as $group) {
					$group = utf8_mytrim($group);
					if (!$group) {
						continue;
					}
					if (!$header) {
						$header = $group;
						continue;
					}
					$parts[$header] = $group;
					$header = null;
				}
			}

			$groupcnt = count($parts);
			$have_numbers = false;
			$trackcnt = 0;
			foreach ($parts as $header => &$part) {
				$tracks = [];
				$subparts = preg_split('"(?:^|\n)\h*(?:0*(\d+|\-))[\.\s\)]"u', $part, -1, PREG_SPLIT_DELIM_CAPTURE);

				$num = 0;
				foreach ($subparts as $subpart) {
					$subpart = utf8_mytrim($subpart);
					if (!$subpart) {
						$num = 0;
						continue;
					}
					if (is_number($subpart)) {
						$have_numbers = true;
						$tracks[$num = $subpart] = '';
					} elseif ($subpart == '-') {
						$tracks[++$num] = '';
					} else {
						if (!$num)
							$tracks[$num] = $subpart;
						else	$tracks[$num] .= $subpart;
					}
				}
				$part = $tracks;
				$trackcnt += count($tracks);
			}
			unset($part);
			?><div class="rpad lpad"><?
			if ($review[$title]) {
				?><div class="blackbox"><?= escape_utf8($review[$title]) ?></div><?
			}
			if ($groupcnt == 1 && $trackcnt == 1) {
				[$header, $tracks] = keyval($parts);
				[$num, $data] = keyval($tracks);
				?><div class="block"><?= escape_utf8($data) ?></div><?
			} else {
				?><<?= $type = $have_numbers ? 'o' : 'u' ?>l><?
				$z = 0;
				foreach ($parts as $header => $tracks) {
					if ($header) {
						?><li class="bold" value="" style="list-style:none"><?= make_all_html($header, UBB_UTF8) ?></li><?
					}
					foreach ($tracks as $num => $title) {
						?><li<?
						if (!(++$z % 2)) {
							?> class="zebra"<?
						}
						if ($have_numbers) {
							?> value="<?= $num ?>"<?
						}
						?>><?= make_all_html($title, UBB_UTF8) ?></li><?
					}
				}
				?></<?= $type ?>l><?
			}
			?></div><?
			?></div><?
		}
		layout_close_box();
	}
	if ($_REQUEST['ACTION'] == 'votes') {
		vote_show_box();
	}
	if (have_user()) {
		vote_display_choices($review);
	}

	require_once '_commentlist.inc';
	$cmts = new _commentlist;
	$cmts->item($review);
	$cmts->display();
	?></article><?

	counthit('review',$_REQUEST['sID']);
}

function review_commit() {
	require_once '_namefix.inc';
	require_once '_ubb_preprocess.inc';
	$setlist = [];
	if (!require_admin(['review', 'reviewer'])
	||	!require_anything_trim($_POST, 'SPOTIFY_EMBED', utf8: true)
	||	!require_something_trim($_POST, 'TEASER',null,null,utf8:true)
	||	!require_something_trim($_POST, 'TITLE',null,null,utf8:true)
	||	!require_something_trim($_POST, 'BODY',null,null,utf8:true)
	||	false === require_number($_POST, 'DISC_CNT')
	||	false === require_number($_POST, 'RATING')
	||	!require_element($_POST, 'TYPE', ['cd','dvd','book'])
	||	!parse_publication($setlist)
	|| 	!require_number_or_empty($_POST,'BOLCOMPRODID')
	) {
		return;
	}
	if (!empty($_POST['ITUNES'])) {
		# example link: https://itunes.apple.com/nl/album/state-trance-600-mixed-by/id609879181
		if (!preg_match('"\b(album|movie|podcast)/(.*?)/(?:id)?(\d+)\b"iu', $_POST['ITUNES'], $match)) {
			register_warning('itunes:warning:not_understood_LINE', ['ITUNES' => $_POST['ITUNES']]);
		} else {
			[,$type,$title,$id] = $match;
			$setlist[] = 'ITUNES_TYPE="'.$type.'"';
			$setlist[] = 'ITUNES_ID='.$id;
			$setlist[] = 'ITUNES_TITLE="'.addslashes($title).'"';
		}
	}
	$setlist[] = 'SPOTIFY_EMBED="'.	addslashes($_POST['SPOTIFY_EMBED']).'"';
	$setlist[] = 'BOLCOMPRODID='.	($_POST['BOLCOMPRODID'] ?: 0);
	$setlist[] = 'TEASER="'.	addslashes($teaser = _ubb_preprocess(get_fixed_utf8_name($_POST['TEASER']), utf8: true)).'"';
	$setlist[] = 'TITLE="'.		addslashes(_ubb_preprocess(get_fixed_utf8_name($_POST['TITLE']), utf8: true)).'"';
	$setlist[] = 'BODY="'.		addslashes($body = _ubb_preprocess(get_fixed_utf8_name($_POST['BODY']), utf8: true)).'"';
	$setlist[] = 'DISC_CNT='.	$_POST['DISC_CNT'];
	$setlist[] = 'RATING='.		$_POST['RATING'];
	$setlist[] = 'TYPE="'.		$_POST['TYPE'].'"';
	$setlist[] = 'RETRO='.		(isset($_POST['RETRO']) ? "b'1'" : "b'0'");
	if ($is_admin = have_admin('review')) {
		if (false === require_number($_POST,'BYUSERID')) {
			return;
		}
		$setlist[] = 'ACCEPTED = '.(isset($_POST['ACCEPTED']) ? "b'1'" : "b'0'");
		$setlist[] = 'BYUSERID = '.$_POST['BYUSERID'];
	} else {
		$setlist[] = "ACCEPTED=b'0'";
		$setlist[] = 'BYUSERID='.CURRENTUSERID;
	}
	// non required
	foreach ([
		'ELEM1_BODY',
		'ELEM2_BODY',
		'ELEM3_BODY',
		'ELEM4_BODY',
		'ELEM5_BODY',
		'ELEM1_TITLE',
		'ELEM2_TITLE',
		'ELEM3_TITLE',
		'ELEM4_TITLE',
		'ELEM5_TITLE',
	] as $key) {
		if (!isset($_POST[$key])) {
			$setlist[] = $key.'=""';
		} else {
			$val = utf8_mytrim($_POST[$key]);
			$setlist[] = $key.'="'.addslashes(get_fixed_utf8_name($val)).'"';
		}
	}
	if ($reviewid = $_REQUEST['sID']) {
		require_once '_element_access.inc';
		if (!require_last_lock(LOCK_REVIEW, $reviewid)
		||	!may_change_element('review', $reviewid, null, true)
		||	!db_insert('review_log','
			INSERT INTO review_log
			SELECT * FROM review
			WHERE REVIEWID='.$reviewid)
		) {
			return;
		}
		$setlist[] = 'MUSERID='.CURRENTUSERID;
		$setlist[] = 'MSTAMP='.CURRENTSTAMP;
		if (!db_update('review','
			UPDATE review SET '.implode(',', $setlist).'
			WHERE REVIEWID='.$reviewid)
		) {
			return;
		}
		register_notice('review:notice:changed_LINE');
	} else {
		$setlist[] = 'USERID='.CURRENTUSERID;
		$setlist[] = 'CSTAMP='.CURRENTSTAMP;
		if (!db_insert('review','
			INSERT INTO review SET '.implode(',',$setlist))
		) {
			return false;
		}
		$_REQUEST['sID'] = $reviewid = db_insert_id();
		register_notice('review:notice:added_LINE');
	}
	_genrelist_commit('review', $reviewid);

	require_once '_update.inc';
	update_element('review', $reviewid, $body, $teaser, utf8: true);
}
