<?php

function show_camerarequests($element,$id) {
	$is_camadm = have_admin('camerarequest');
	switch ($element) {
	case 'organization':
		$parties = db_rowuse_hash(array('party','connect'),'
			SELECT ASSOCID,NAME,STAMP
			FROM connect
			LEFT JOIN party ON PARTYID=ASSOCID
			WHERE MAINTYPE="organization"
			  AND ASSOCTYPE="party"
			  AND MAINID='.$id.'
		/*	  AND STAMP>'.(TODAYSTAMP-6*30*24*3600).'*/
			ORDER BY STAMP DESC'
		);
		break;
	case 'location':
		$parties = db_rowuse_hash('party','
			SELECT PARTYID,NAME,STAMP
			FROM party
			WHERE LOCATIONID='.$id.'
		/*	  AND STAMP>'.(TODAYSTAMP-6*30*24*3600).'*/
			ORDER BY STAMP DESC'
		);
		break;
	}
	if (!$parties) {
		return;
	}
	$implodedparties = implodekeys(',',$parties);
	$camreqlist = db_rowuse_hash('camera','
		SELECT PARTYID,STATUS,OFFER
		FROM camera
		WHERE PARTYID IN ('.$implodedparties.')'
	);
	$ticketlist = db_simple_hash('contact_ticket','
		SELECT ID,GROUP_CONCAT(TICKETID)
		FROM contact_ticket
		WHERE ELEMENT="camerarequest"
		  AND ID IN ('.$implodedparties.')
		GROUP BY ID'
	);
	if (!$camreqlist && !$ticketlist) {
		return;
	}
	require_once 'defines/camera.inc';
	layout_open_box($element,'camera');
	layout_box_header(Eelement_plural_name('camerarequest'));
	layout_open_table('fw vtop');
	$done = false;
	foreach ($parties as $partyid => $party) {
		if (!isset($camreqlist[$partyid]) 
		&&	!isset($ticketlist[$partyid])
		) {
			continue;
		}
		if ($done !== null) {
			if ($party['STAMP'] < TODAYSTAMP) {
				if ($done) {
					layout_start_spanned_row(4);
					?><b><?= __C('date:past'); ?></b>:<?
					layout_stop_row();
				}
				$done = null;
			} else {
				if (!$done) {
					layout_start_spanned_row(4);
					?><b><?= __C('date:future'); ?></b>:<?
					layout_stop_row();
				}
				$done = true;
			}
		} 
		layout_start_rrow();
		echo get_element_link('party', $partyid, $party['NAME']);
		layout_next_cell();
		if (isset($camreqlist[$partyid])) {
			ob_start();
			show_camera_status($camreqlist[$partyid]['STATUS']);
			$status = ob_get_clean();
			?><a href="/camera/<?= $partyid ?>"><?= $status ?></a><?
/*			?><a href="/camera/<?= $partyid ?>"><?= element_name(!$status && $camreqlist[$partyid]['OFFER'] ? 'offer' : 'request'); ?></a><?
			if ($status) {
				?>: <? echo $status;
			}*/
		}
		if ($is_camadm) {
			layout_next_cell();
			if (isset($ticketlist[$partyid])) {
				foreach (explode(',',$ticketlist[$partyid]) as $ticketid) {
					?><a href="/ticket/<?= $ticketid ?>"><?= element_name('contact_ticket') ?> #<?= $ticketid;
					?></a><br /><?
				}
			}
		}
		layout_next_cell(class: 'right');
		_dateday_display($party['STAMP'], short: true);
		layout_stop_row();
	}
	layout_close_table();
	layout_close_box();
}
