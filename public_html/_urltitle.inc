<?php

declare(strict_types=1);

require_once '_elementnames.inc';
require_once '_helper.inc';
require_once '_servertype.inc';

# FIXME: URLs not working properly:
#		 https://partyflock.nl/party/473074

const TEST_UTF8_URL_TITLES = false; // HOME_THOMAS;

const USE_URLTITLE = [
	'ad'			=> true,
	'album'			=> true,
	'albumelement'	=> true,
	'albummap'		=> true,
	'announcement'	=> true,
	'artist'		=> true,
	'banner'		=> true,
	'boarding'		=> true,
	'camera'		=> true,
	'cartoon'		=> true,
	'city'			=> true,
	'column'		=> true,
	'compo'			=> true,
	'contest'		=> true,
	'country'		=> true,
	'flock'			=> true,
	'flocktopic'	=> true,
	'forum'			=> true,
	'gallery'		=> true,
	'interview'		=> true,
	'job'			=> true,
	'label'			=> true,
	'lineup'		=> true,
	'location'		=> true,
	'music'			=> true,
	'news'			=> true,
	'organization'	=> true,
	'party'			=> true,
	'photo'			=> true,
	'pixel'			=> true,
	'poll'			=> true,
	'promo'			=> true,
	'province'		=> true,
	'region'		=> true,
	'relation'		=> true,
	'report'		=> true,
	'review'		=> true,
	'stream'		=> true,
	'topic'			=> true,
	'user'			=> true,
	'video'			=> true,
	'videochannel'	=> true,
	'virtualtour'	=> true,
	'weblog'		=> true,
	'eventmap'		=> true,
];

const USE_UNICODE = [
	'ad'			=> true,
	'album'			=> true,
	'albumelement'	=> true,
	'albummap'		=> true,
	'announcement'	=> true,
	'artist'		=> true,
	'banner'		=> true,
	'boarding'		=> true,
	'camera'		=> true,
	'cartoon'		=> true,
	'city'			=> true,
	'column'		=> true,
	'compo'			=> true,
	'contest'		=> true,
	# 'country'		=> true,
	'eventmap'		=> true,
	'flock'			=> true,
	'flocktopic'	=> true,
	'forum'			=> true,
	'gallery'		=> true,
	'interview'		=> true,
	# 'job'			=> true,
	'lineup'		=> true,
	'location'		=> true,
	'music'			=> true,
	'news'			=> true,
	'newsad'		=> true,
	'organization'	=> true,
	'party'			=> true,
	'photo'			=> true,
	'pixel'			=> true,
	'poll'			=> true,
	# 'promo'		=> true,
	'province'		=> true,
	# 'region'		=> true,
	'relation'		=> true,
	'report'		=> true,
	'review'		=> true,
	'stream'		=> true,
	'topic'			=> true,
	# 'user'		=> true,
	'video'			=> true,
	'videochannel'	=> true,
	'virtualtour'	=> true,
	'weblog'		=> true,
];

function get_titles(int|false $which = false, bool $nocache = false): string|array|false {
	static $__titles = [];
	if ($nocache) {
		$was_cache = $_REQUEST['NOMEMCACHE'] ?? null;
		$_REQUEST['NOMEMCACHE'] = true;
		$__titles = [];
	}
	if ($__titles === []) {
		$element = $_REQUEST['sELEMENT'];
		$id		 = $_REQUEST['sID'];
		$action  = $_REQUEST['ACTION'];
		if ($element === 'album') {
			if ($action === 'single'
			&&	$id
			&&	$_REQUEST['subID']
			) {
				$element = 'albummap';
				$id  = $_REQUEST['subID'];
				$action = true;
			}
		} elseif (
			$element === 'video'
		&&	$action === 'channel'
		&&	$_REQUEST['subID']
		) {
			$element = 'videochannel';
			$id = $_REQUEST['subID'];
		}
		if (!$element
		||	!$id
		||	!isset(USE_URLTITLE[$element])
		) {
			$__titles = [false, false];
		} else {
			$__titles = [
				$title = $action ? get_element_title($element, $id) : null,
				$title ? myurlencode($title, isset(USE_UNICODE[$element]), TEST_UTF8_URL_TITLES) : null];
		}
	}
	if ($nocache) {
		$_REQUEST['NOMEMCACHE'] = $was_cache;
	}
	if ($which === false) {
		return $__titles;
	}
	return $__titles[$which];
}

function get_current_url_title(): string|false {
	return get_titles(1);
}

function get_current_title(): string|false {
	return get_titles(0);
}

function check_urltitle(): void {
	$id_name = 'sID';
	$action = $_REQUEST['ACTION'];
	if ('video' === ($element = $_REQUEST['sELEMENT'])
	&&	$_REQUEST['ACTION'] === 'channel'
	&&	empty($_REQUEST['SUBACTION'])
	) {
		$action  = null;
		$element = 'videochannel';
		$id_name = 'subID';
	}
	if (!isset(USE_URLTITLE[$element])
	||	!empty($_SERVER['eDATA'])
	||	!empty($_REQUEST['PAGE'])
	||	!($id = $_REQUEST[$id_name])
	||	!($albummap =
				$_REQUEST['sID']
			&&	$_REQUEST['subID']
			&&	$element === 'album'
			&&	$action === 'single'
		)
	&&	$action
	&&	(	$action !== 'single'
		&&	$action !== 'showtopic'
		||	$_REQUEST['subID']
		||	$_REQUEST['ssID']
		)
	) {
		return;
	}
	[$title, $urltitle] = get_titles();

	if ($title === false) {
		# probably inaccessible
		return;
	}

	$argoffset = strpos($_SERVER['REQUEST_URI'], '?');
	$arg =	$argoffset !== false
		?	substr($_SERVER['REQUEST_URI'], $argoffset)
		:	null;

	if ($element === 'videochannel') {
		$element = 'video/channel';
	}
	if ($urltitle) {
		if (!isset($_SERVER['eURLTITLE'])
		||	fix_urlencode($_SERVER['eURLTITLE']) !== $urltitle
		) {
			# have stored title, but it is not same as url
			$new_url =	'/'.(!empty($albummap) ? "album/{$_REQUEST['sID']}/{$_REQUEST['subID']}" : "$element/$id").
						":$urltitle$arg";
		}
	} elseif (
		!empty($_SERVER['eURLTITLE'])
	||	$_SERVER['REQUEST_URI'][-1] === ':'
	) {
		$new_url =	"/$element/$id$arg";
	}
	if (isset($new_url)) {
		require_once '_http_status.inc';
		header('Cache-Control: max-age=600');
		see_other($new_url);
	}
}

function get_display_element(?string $element): ?string {
	switch ($element) {
	case 'buddyrequest':
		return 'buddy_request';
	case 'ticket':
		return 'contact_ticket';
	case 'stream':
		return 'radio';
	case 'info':
		$GLOBALS['__singular'] = true;
		return 'information_on_advertisting_on_partyflock';
	}
	return $element;
}

function get_element_href(
	string					$element,
	int						$id,
	string|int|false|null	$title = 0,
	bool					$cache = true
): string {
	require_once '_stable_slug.inc';
	if (
		$element === 'party'
		&& ($href = get_slug($id))
	) {
		if (isset($_REQUEST['ABSPATH'])) {
			return "https://{$_SERVER['HTTP_HOST']}.$href";
		}
		return $href;
	}
	if ($cache) {
		static $__encoded = [];
	} else {
		$__encoded = [];
	}
	$__encoded[$element][$id]
	??=	$title
	||	$title === 0
	&&	($title = isset(USE_URLTITLE[$element]) ? get_element_title($element, $id) : '')
	?	myurlencode($title, isset(USE_UNICODE[$element]), TEST_UTF8_URL_TITLES)
	:	false;
	$url_title = $__encoded[$element][$id];

	switch ($element) {
	case 'albummap':
		require_once '_album.inc';
		$href = '/album/'.get_albumid_from_mapid($id);
		break;

	case 'videochannel':
		$href = '/video/channel';
		break;

	default:
		$href = "/$element";
		break;

	}
	$href .= "/$id";
	if ($url_title) {
		$href .= ":$url_title";
	}
	if (isset($_REQUEST['ABSPATH'])) {
		return "https://{$_SERVER['HTTP_HOST']}$href";
	}
	return $href;
}

function get_element_link(
	string				$element,
	int					$id,
	string|false|null	$title		= null,
	?string				$rel		= null,
	?string				$fragment	= null,
	?string				$itemprop   = null,
	?string				$target	 	= null,
	?string				$params	 	= null,
	?string				$class	 	= null,
	string|bool			$title_attr = false,
	?array				$attribs	= [],
	?bool				$utf8		= null,
): ?string {
	if (!$id) {
		mail_log("get_element_link $element called without id");
		return null;
	}
	if ($element === 'user'
	&&	(	!isset($_REQUEST['sELEMENT'])
		||	$_REQUEST['sELEMENT'] !== 'msg'
		)
	) {
		# force visibility in directmessages, all others honor visibility
		require_once '_helper.inc';
		if (invisible_profile($id)) {
			return null;
		}
	}
	if ($rel) {
		$attribs['rel'] = '"'.$rel.'"';
		if ($rel === 'author') {
			$attribs['itemprop'] = '"'.$rel.'"';
		}
	}
	if ($itemprop) {
		$attribs['itemprop'] = '"'.$itemprop.'"';
	}
	if ($target) {
		$attribs['target'] = '"'.$target.'"';
	}
	if ($class) {
		$attribs['class'] = '"'.$class.'"';
	}

	$paramstr = $params ? '?'.escape_specials($params) : '';

	$utf8 ??= isset(USE_UNICODE[$element]);

#	if ($title === false) {
#		mail_log('get_element_link with $title === false, register where that happens');
#	}
	#if ($title === null) {
	# $title could be null, false or empty string
	# In all cases, we want to fetch the actual title, because we cannot show nothing in the anchor at end of this function
	if (!$title) {
		$title = get_element_title($element, $id);
	}

	if (!isset(USE_URLTITLE[$element])) {
		$href = "/$element/$id";
		$title = $title ? escape_specials($title, $utf8) : element_name($element).' #'.$id;
	} elseif (!$title) {
		$href = "/$element/$id";
		$title = element_name($element).' #'.$id;
	} else {
		$href = get_element_href($element, $id, $title);
		$title = escape_specials($title, $utf8);
	}

	if ($title_attr) {
		$attribs['title'] = '"'.($title_attr === true ? $title : escape_specials($title_attr, $utf8)).'"';
	}

	$attribstr = $attribs ? ' '.implodehash(' ', '=', $attribs) : '';

	$no_link = $element === 'user' && $id === 1;

	$tag = $no_link ? 'span' : 'a';

	return	'<'.$tag.$attribstr.($no_link ? ' ' : ' href="'.$href.$paramstr.$fragment.'"').'>'.$title.'</'.$tag.'>';
}

function get_photo_url(int $photoid, string $size, bool|string|null $x2 = null): string|false {
	if ($size === 'original'
	||	str_contains($size, 'x')
	) {
		$x2 = null;
	} elseif ($x2 === null) {
		$x2 = is_high_res();
	} elseif (!is_string($x2)) {
		$x2 = $x2 ? is_high_res() : '';
	}
	if (false === ($url_title = get_urltitle('photo', $photoid))) {
		return false;
	}
	return	PHOTO_HOST."/$photoid/$size/$url_title$x2.".photos_extension();
}

function get_urltitle_key(string $element, int $id): string {
	return "urltitle:$element:$id";
}

function reset_urltitle(string $element, int $id): bool {
	HOME_THOMAS && ($was_url_title = get_element_title($element, $id));
	# getting the url title is complicated, can be multiple queries
	# so turn cache off and determine a fresh one
	set_nomemcache();
	$url_title = get_urltitle($element, $id);
	unset_nomemcache();
	if ($url_title === false) {
		return false;
	}
	/** @noinspection PhpUndefinedVariableInspection */
	HOME_THOMAS && error_log("reset urltitle of $element:$id, was: $was_url_title, now: $url_title");
	return true;
}

function get_urltitle(string $element, ?int $id = null, bool $try_log = false): string|false|null {
	static $__stored;
	if (isset($__stored[$element][$id])) {
		return $__stored[$element][$id];
	}
	$title = null;
	if ($element === 'photo') {
		$key = get_urltitle_key($element, $id);

		# getting update_time using information schema is faster than using SHOW TABLE STATUS LIKE

		static $__appearance_update_time = db_single_string('information_schema',"
			SELECT update_time
			FROM information_schema.TABLES
			WHERE TABLE_SCHEMA = 'party_db'
			  AND TABLE_NAME = 'artist_appearance'");

		if (!$__appearance_update_time) {
			return false;
		}
		$key .= '.'.$__appearance_update_time;

		if (!($title = isset($_REQUEST['NOMEMCACHE']) ? null : memcached_get($key))) {
			if (false === ($min_imgid = memcached_single('image', "
				SELECT MIN(IMGID)
				FROM image
				WHERE GALLERYID = (SELECT GALLERYID FROM image WHERE IMGID = $id)",
				ONE_HOUR))
			|| false === ($appearances = db_simpler_array(['artist', 'artist_appearance'],"
				SELECT NAME
				FROM artist
				JOIN artist_appearance USING (ARTISTID)
				WHERE IMGID = $id
				  AND VISIBILITY_APPEARANCES = 0
				ORDER BY NAME"))
			||	false === ($element_title = get_utf8_element_title($element, $id))
			) {
				return false;
			}
			$title =	$element_title.'-'.
						($id - $min_imgid + 1).
						($appearances ? '-'.implode('-', $appearances) : '');

			memcached_set($key, $title, ONE_DAY);
		}
		$id = 0;
	}
	/** @noinspection NotOptimalIfConditionsInspection */
	if ($id
	&&	(	false === ($title ??= get_utf8_element_title($element, $id))
		||	null  ===  $title)
	&&	$try_log
	&&	false !== ($title = get_logged_title($element, $id))
	&&	null  !==  $title
	&&	!isset(USE_UNICODE[$element])
	) {
		$title = win1252_to_utf8($title);
	}
	return $__stored[$element][$id] = $title ? myurlencode($title, true, TEST_UTF8_URL_TITLES) : '';
}

function get_utf8_element_title(string $element, int $id): string|false|null {
	if (!($title = get_element_title($element, $id))) {
		return $title;
	}
	if (!isset(USE_UNICODE[$element])) {
		$title = win1252_to_utf8($title);
	}
	return $title;
}

function get_element_title(string $element, int $id): string|false|null {
	if ($element === 'orgashost') {
		$element = 'organization';
	}
	if (!$id) {
		require_once '_helper.inc';
		mail_log('id not set in get_element_title for '.$element);
		return false;
	}
	static $__title;
	if (isset($__title[$element])
	&&	array_key_exists($id, $__title[$element])
	) {
		return $__title[$element][$id];
	}

	$utf8 = isset(USE_UNICODE[$element]);

	switch ($element) {
	case 'user':
		require_once '_helper.inc';
		if (invisible_profile($id)) {
			return '';
		}
		break;

	case 'albumelement':
		require_once '_visibility.inc';
		if (!($albumelement = memcached_single_assoc('albumelement', '
			SELECT VISIBILITY_ELEMENT, ALBUMID, ORIGNAME
			FROM albumelement
			WHERE ALBUMELEMENTID = '.$id))
		) {
			return $albumelement;
		}
		if (!_visibility($albumelement, 'ELEMENT')) {
			return '';
		}
		break;

	case 'albummap':
		require_once '_visibility.inc';
		if (!($albummap = memcached_single_assoc('albummap','
			SELECT VISIBILITY_MAP,ALBUMID
			FROM albummap
			WHERE MAPID='.$id
		))) {
			return $albummap;
		}
		if (!_visibility($albummap, 'MAP')) {
			return '';
		}
		break;
	}

	require_once '_memcache.inc';
	switch ($element) {
	default:
		$title_name = 'TITLE';
		break;

	case 'album':
		$title = memcached_single_string('albummap', 'SELECT TITLE FROM albummap WHERE UPMAPID=0 AND ALBUMID = '.$id, TEN_MINUTES);
		break;

	case 'albummap':
		$title = memcached_single_string('albummap', 'SELECT TITLE FROM albummap WHERE MAPID = '.$id, TEN_MINUTES);
		break;

	case 'albumelement':
		require_once 'defines/albumelement.inc';
		require_once '_smiley.inc';
		if (!($albumelement = memcached_albumelement($id))) {
			return false;
		}
		if (!$albumelement['ACCEPTED']
		&&	!have_admin('albumelement')
		) {
			return null;
		}
		if (!$albumelement['TITLE']) {
			$title = preg_replace('"\.(?:avif|bmp|jpe?g|gif|hvec|ico|pdf|psd|a?png|tiff?|webp)$"iu', '', $albumelement['ORIGNAME']);
		} else {
			$title = utf8_mytrim(smiley_remove(_smiley_prepare($albumelement['TITLE'], utf8: true), utf8: true));
		}
		break;

	case 'poll':
		$title_name = 'QUESTION';
		$id_name = 'POLLID';
		break;

	case 'party':
	case 'camera':
	case 'eventmap':
		$title = memcached_party($id);
		break;

	case 'city':
		$title = memcached_city($id);
		break;

	case 'region':
		$title = __('region:'.$id,DONT_ESCAPE);
		break;

	case 'location':
		$title = memcached_location($id);
		break;

	case 'orgaslabel':
	case 'organization':
		$title = memcached_organization($id);
		break;

	case 'newsad':
		$title = db_single_string([$element, 'news'], "
			SELECT TITLE
			FROM newsad
			LEFT JOIN news USING (NEWSID)
			WHERE NEWSADID = $id
			LIMIT 1"
		);
		break;

	case 'banner':
		require_once '_bannerinfo.inc';
		if (!($banner_info = get_bannerinfo($id))) {
			return $banner_info;
		}
		$title = $banner_info['BANNER_NAME'];
		break;

	case 'ad':
		require_once '_bannerinfo.inc';
		if (!($info = get_bannerinfo_using_adid($id))) {
			break;
		}
		if ($info['BANNER_NAME']) {
			$title = $info['BANNER_NAME'];
		} else {
			$title = __translation_get('adpos:'.$info['POSITION'], langid: 0);
			if ($info['PARTYID']) {
				$party = memcached_party_and_stamp($info['PARTYID']);
				if ($party) {
					[$y, $m, $d] = _getdate($party['STAMP_TZI'], 'UTC');

					$title .= ' '.$party['NAME'].sprintf(' %4d-%02d-%02d', $y, $m, $d);
				}
			} elseif ($info['RELATION_NAME']) {
				$title .= ' '.$info['RELATION_NAME'];
			}
		}
		break;

	case 'boarding':
		if (!($info = db_single_array($element,"
			SELECT ADDRESS, NAME
			FROM boarding WHERE
			BOARDINGID = $id"))
		) {
			break;
		}
		[$address, $name] = $info;
		$title = $name ?: '';
		if ($address) {
			$title .= ($title ? ' ' : '').$address;
		}
		break;

	case 'country':
		return __('country:'.$id, DONT_ESCAPE);

	case 'flock':
	case 'stream':
	case 'forum':
	case 'province':
		$title_name = 'NAME';
		$id_name = $element.'ID';
		break;

	case 'videochannel':
		$title = db_single_string($element, "SELECT NAME FROM videochannel WHERE CHANNELID = $id");
		break;

	case 'topic':
		if (!($topic = memcached_topic($id))) {
			break;
		}
		$title = $topic['SUBJECT'];
		break;

	case 'flocktopic':
		$title_name = 'SUBJECT';
		$id_name   = 'TOPICID';
		break;

	case 'user':
	case 'deaduser':
		$title = memcached_nick($id);
		break;

	case 'artist':
		$title = memcached_artist($id);
		break;

	case 'relation':
		$title = memcached_relation($id);
		break;

	case 'report':
		if ($report = memcached_report($id)) {
			$title = $report['TITLE'] ?: $report['NAME'] ?: 'report:'.$id;
		}
		break;

	case 'job':
		$title_name = 'FUNCTION';
		$id_name = 'JOBID';
		break;

	case 'contest':
		if (!($contest = memcached_contest($id))) {
			break;
		}
		if (!in_array($contest['TYPE'], ['guestlist', 'freeticket', 'eticket', 'scancode'], true)) {
			$title = $contest['NAME'];
			if ($contest['PARTY_NAME']) {
				$title .= " ({$contest['PARTY_NAME']})";
			}
		} else {
			$title = $contest['PARTY_NAME'];
			if ($contest['NAME']) {
				$title .= " {$contest['NAME']}";
			}
		}
		$title .= ' '.element_name('contest');
		break;

	case 'compoentry':
		if ($name = memcached_single_string(['compoentry', 'compo'], "
			SELECT TITLE
			FROM compoentry
			JOIN compo USING (COMPOID)
			WHERE COMPOENTRYID = $id",
			TEN_MINUTES
		)) {
			$title = 'inzending voor '.$name;
		}
		break;

	case 'meeting':
		if ($name = memcached_single_string(['meeting', 'party'], '
			SELECT party.NAME
			FROM meeting
			JOIN party USING (PARTYID)
			WHERE MEETINGID='.$id,
			TEN_MINUTES
		)) {
			$title = __('element:meeting(_on_event)', DO_UBBFLAT, ['EVENT' => $name]);
		}
		break;

	case 'photo':
		$title = db_single_string(['party', 'image'], "
			SELECT NAME
			FROM party
			JOIN image USING (PARTYID)
			WHERE IMGID = $id"
		);
		break;

	case 'camerarequest':
		$title = null;
		break;

	case 'gallery':
		if (!($gallery = memcached_gallery($id))) {
			$title = null;
		} else {
			/** @noinspection PhpRedundantOptionalArgumentInspection */
			extract($gallery, \EXTR_OVERWRITE);
			if (!empty($party['NAME'])) {
				$title = $party['NAME'];
				if ($TITLE) {
					$title .= ' '.$TITLE;
				}
			} else {
				$title = $TITLE;
			}
		}
		break;

	case 'video':
		require_once '_videometa.inc';
		$title = (new videometa($id))->get_flat_title($id, true);
		break;

	case 'virtualtour':
		if ($info = memcached_single_array('virtualtour', "
			SELECT ELEMENT, ID
			FROM virtualtour
			WHERE TOURID = $id",
			TEN_MINUTES)
		) {
			[$element, $id] = $info;
			return get_element_title($element,$id);
		}
		break;

	case 'pixel':
		require_once '_pixel.inc';
		$title = db_single(['pixel', 'relation', 'party', 'organization'], /** @lang MariaDB */ '
			SELECT '.pixel_name_select()."
			FROM pixel
			LEFT JOIN relation USING (RELATIONID)
			LEFT JOIN party USING (PARTYID)
			LEFT JOIN organization USING (ORGANIZATIONID)
			WHERE PIXELID = $id"
		).' '.element_name('pixel');
		break;

	case 'fb':
		return strtoupper($element).'#'.$id;

	case 'ticket':
		return Eelement_name('contact_ticket').' #'.$id;

	case 'lineup':
		$title = memcached_single_string(['artist', 'lineup'], "
			SELECT NAME
			FROM artist
			JOIN lineup USING (ARTISTID)
			WHERE LINEUPID = $id",
			TEN_MINUTES
		);
		break;

	case 'label':
		$title_name = 'NAME';
		break;
	}
	if (isset($title_name)) {
		$id_name ??= $element.'ID';
		if (!$element) {
			mail_log('get_element_title encountered an empty $element');
			return 'element:'.$id;
		}
		if (!($title = memcached_single_string($element, "
			SELECT $title_name
			FROM `$element`
			WHERE $id_name = $id",
			TEN_MINUTES))
		) {
			# failure or item does not exists
			return $title;
		}
	}
	if (empty($title)) {
		return $title ?? null;
	}
	if (str_contains($title, '[')) {
		require_once '_ubb.inc';
		$title = plain_text($title, $utf8 ? UBB_UTF8 : 0);
	}
	return $__title[$element][$id] = $title ?? null;
}

function get_logged_title(string $element, int $id): string|false|null {
	static $__logged_title = [];
	if ( isset($__logged_title[$element][$id])) {
		return $__logged_title[$element][$id];
	}
	require_once '_memcache.inc';
	switch ($element) {
	case 'user':
		$title_name = 'NICK';
		break;

	case 'artist':
	case 'boarding':
	case 'city':
	case 'flock':
	case 'label':
	case 'location':
	case 'organization':
	case 'party':
		$title_name = 'NAME';
		break;

	case 'job':
		$title_name = 'FUNCTION';
		break;

	case 'poll':
		$title_name = 'QUESTION';
		break;

	case 'topic':
	case 'flocktopic':
		$title_name = 'SUBJECT';
		break;

	case 'newsad':
		if (!($info =
			memcached_single_array([$element, 'news_log'], "
			SELECT 1, TITLE
			FROM newsad_log
			LEFT JOIN news USING (NEWSID)
			WHERE NEWSID != 0
			 AND NEWSADID = $id
			ORDER BY newsad_log.MSTAMP DESC
			LIMIT 1")
		?:	memcached_single_array([$element.'_log', 'news_log'], "
			SELECT 1, TITLE
			FROM newsad_log
			LEFT JOIN news_log USING (NEWSID)
			WHERE NEWSID != 0
			  AND NEWSADID = $id
			ORDER BY newsad_log.MSTAMP DESC,
			           news_log.MSTAMP DESC
			LIMIT 1"
		) ?: null)) {
			if ($info === false) {
				return false;
			}
			return null;
		}
		[, $title] = $info;
		return $title ?: element_name('newsad').' '.$id;


	case 'albumelement':
		$title_name = 'IF(TITLE = "", ORIGNAME, TITLE) AS TITLE';
		break;

	case 'virtualtour':
	case 'photo':
	case 'contest':
		mail_log("get_logged_title of $element, which is not supported");
		# these have no deleted titles
		$title = false;
		break;

	case 'promo':
	default:
		$title_name = 'TITLE';
		break;
	}
	if (!isset($title)
	&&	 isset($title_name)
	) {
		if ($element === 'lineup') {
			mail_log('get_logged_title of lineup, which is not supported');
			return null;
		}
		$title = memcached_single($element.'_log', "
			SELECT $title_name
			FROM {$element}_log
			WHERE {$element}ID = $id
			ORDER BY MSTAMP DESC
			LIMIT 1"
		);
	}
	if (!empty($title)
	&&	str_contains($title, '[')
	) {
		require_once '_ubb.inc';
		$title = plain_text($title, isset(USE_UNICODE[$element]) ? UBB_UTF8 : 0);
	}
	return $__logged_title[$element][$id] = $title ?? false;
}

function get_element_title_or_log(string $element, int $id, ?bool &$log = null): string|false|null {
	# returns:
	#	string: found either current or log/deleted
	# 	false: error
	#	null: niether current nor log/deleted found

	$log = false;
	if (false === ($title = get_element_title($element, $id))) {
		return false;
	}
	if ($title !== null) {
		return $title;
	}
	if ($title = get_logged_title($element, $id)) {
		$log = true;
		return $title;
	}
	return $log = null;
}

function myurlencode(
	string	$str,
	bool	$utf8	 = false,
	bool	$new	 = false,
): string {
	require_once '_smiley.inc';
	require_once '_ubb.inc';
	require_once '_unicode.inc';

	error_clear_last();

	# only allow regular chars + underscore

	# dashes as separator in url are best:
	# https://www.ecreativeim.com/blog/2011/03/seo-basics-hyphen-or-underscore-for-seo-urls/

	$sep = '-';

	if ($new) {
		if (!$utf8) {
			$str = win1252_to_utf8($str);
			$utf8 = true;
		}
		$str = strip_smileys($str, true);
		$str = plain_text($str, flags: UBB_UTF8);
		# $str = preg_replace('",\s+"', ',', $str);
		# $str = str_replace(['w/', ' · ', ', ', '$', '@'], ['with', ' ',  ',',  'S', 'a'], $str);
		$str = utf8_mytrim(str_replace(['w/', ' · ', ' - ', ', '], ['with', '-', '-', ','], $str));
		$str = urlencode(preg_replace('"(\p{Z}+)"u', '-', $str));
		# $str = fix_urlencode($str);
		$result = $str;
	} else {
		if ($utf8) {
			$str = str_replace([
				LATIN_SMALL_LETTER_TURNED_K_UTF8,
				LATIN_CAPITAL_LETTER_TURNED_K_UTF8,
				LISU_LETTER_KHA_UTF8],
				'K',
				$str
			);
		}
		$result =
			urlencode(
			trim(
				preg_replace(
					'"(\W+)"u',
					$sep,
					str_replace(
						['w/', '$', '@'],
						['with', 'S', 'a'],
						preg_replace(
							[	# convert r&b to rnb
								'"\b(r)&(b)\b"iu',
								# convert & to and
								'"(.{2,}[\s._\-])&([\s._\-].{2,})"u',
								# remove stray apostrophes
								'"\'"u',
							],
							[
								"'\\1n\\2'",
								"'\\1and\\2'",
								'',
							],
							iconv(
								$utf8 ? 'UTF-8' : 'WINDOWS-1252',
								'ASCII//TRANSLIT',
								plain_text(
									strip_smileys(
										# convert h!pster to hipster
										preg_replace(
											'"(h)!(pster)"i'.($utf8 ? 'u' : ''),
											'$1i$2',
											# replace euro sign with E
											str_replace($utf8 ? EURO_SIGN : EURO_SIGN_LATIN1, 'E', $str)
										),
										utf8: $utf8
									),
									flags: $utf8 ? UBB_UTF8 : 0
								)
							)
						)
					)
				),
				$sep
			));
	}
	if ($error = error_get_last())  {
		mail_log("myurlencode('$str', ".($utf8 ? 'utf-8' : 'windows-1252'). ") failed: {$error['message']}", get_defined_vars());
	}
	# hard max is 2048, choose safe lesser amount to allow
	if (strlen($result) > 1024) {
		$result = substr($result, 0, 1024);
	}
	return $result;
}

function fix_urlencode(string $str): string {
	return str_replace(#																				   :	  ?
		['%24', '%2A', '%28', '%29', '%21', '%26', '%27', '%2B', '%2C', '%3B', '%3D', '%5B', '%5D', '%40', '%3A', '%3F'],
		['$',	'*',   '(',   ')',   '!',   '&',   "'",   '+',   ',',   ';',   '=',   '[',   ']',   '@',   '',	  ''],
		urlencode($str)	);
}
