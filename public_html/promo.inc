<?php

require_once '_citylist.inc';
require_once '_colors.inc';
require_once '_connect.inc';
require_once '_contestlist.inc';
require_once '_commentlist.inc';
require_once '_countrylist.inc';
require_once '_buddies.inc';
require_once '_genrelist.inc';
require_once '_locationlist.inc';
require_once '_organizationlist.inc';
require_once '_partylist.inc';
require_once '_promo.inc';
require_once 'defines/promo.inc';
require_once '_promofocus.inc';
require_once '_promoprice.inc';
require_once '_provincelist.inc';
require_once '_ticket.inc';
require_once '_ubb_preprocess.inc';
require_once '_uploadimage.inc';

define('PROMO_MAX_EXEC_TIME',	TEN_MINUTES);
define('PROMO_MEMORY_LIMIT',	GIGABYTE);
define('PROMO_DELETION_AMOUNT',	10);
define('PROMO_INSERTION_AMOUNT',10);

function preamble() {
	require_once '_info.inc';
	info_redirect_legacy();
}
function perform_commit() {
	switch ($_REQUEST['ACTION']) {
	case 'commit':		return promo_commit();
	case 'remfocus':	return promo_remove_focus();
	case 'remfocusgroup':	return promo_remove_focus_group();
	case 'addfocus':	return promo_add_focus();
	case 'send':		return promo_send();
	case 'cancel':		return promo_cancel();
	case 'activate':	return promo_set_active(true);
	case 'deactivate':	return promo_set_active(false);
	case 'remove':		return promo_remove(true);
	case 'restore':		return promo_remove(false);
	case 'calculate':	return promo_calculate_reach();
	case 'processuniques':	return promo_process_uniques();
	}
}
function display_body() {
	switch ($_REQUEST['ACTION']) {
	default:		not_found(); return;
	case 'archive':
	case 'legend':
	case null:		return promo_display_overview();
	case 'comment':
	case 'comments':
	case 'letter':		return promo_display_letter();
	case 'single':
	case 'send':
	case 'cancel':
	case 'commit':
	case 'activate':
	case 'deactivate':
	case 'calculate':
	case 'priceinfo':
	case 'classinfo':
	case 'remove':
	case 'restore':		return promo_display_single();
	case 'form':		return promo_display_form();
	case 'checkprice':	return promo_display_check();
	case 'focus':
	case 'addfocus':
	case 'remfocus':
	case 'remfocusgroup':	return promo_display_focus();
	case 'processuniques':
	case 'uniquesform':	return promo_display_uniques_form();
	case 'pastdeliveries':	return promo_past_deliveries();
	}
}
function promo_process_uniques() {
	if (!require_admin('promo')
	||	!require_file('CODESFILE')
	||	!($promoid = require_idnumber($_REQUEST,'sID'))
	) {
		return;
	}
	$cnt = get_promo_unique_code_count($promoid);
	if ($cnt === false) {
		return;
	}
	$codes = file($_FILES['CODESFILE']['tmp_name']);
	$bad = false;
	foreach ($codes as $code) {
		$code = trim($code);
		if (isset($ucodes[$code])) {
			$bad = true;
			register_error('promo:error:unique_code_appears_more_than_once_LINE',array('UNIQ'=>$code));
		} else {
			$ucodes[$code] = $code;
		}
	}
	if ($bad) {
		return;
	}
	if (!isset($_POST['UNIQUE_ACTION'])) {
		if ($cnt) {
			register_error('promo:error:unique_action_add_but_cnt_nonzero_LINE');
			return;
		}
	}
	if (!($unique_action = require_element($_POST,'UNIQUE_ACTION',['replace','add']))) {
		return;
	}
/*	if ($unique_action != 'add') {
		if (!db_insert('promo_letter_unique_log','
			INSERT INTO promo_letter_unique_log
			SELECT promo_letter_unique.*,'.CURRENTSTAMP.'
			FROM promo_letter_unique
			WHERE PROMOID='.$promoid)
		) {
			return;
		}
	}*/
	switch ($unique_action) {
/*	case 'replace':
		if (!db_delete('promo_letter_unique','
			DELETE FROM promo_letter_unique
			WHERE PROMOID='.$promoid)
		) {
			return;
		}*/
	case 'add':
		$v = get_v($promoid);
		if (!$v) return;
		$userids = db_simpler_array(
			[$table = $v == 1 ? 'promo_letter' : 'promo_letter_v2','promo_letter_unique'],'
			SELECT USERID
			FROM '.$table.'
			LEFT JOIN promo_letter_unique USING (PROMOID,USERID)
			WHERE ISNULL(promo_letter_unique.USERID)
			  AND PROMOID='.$promoid
		);
		if ($userids === false) {
			return;
		}
		if (!$userids) {
			register_warning('promo:warning:there_are_no_letters_LINE');
			return;
		}
		set_memory_limit(GIGABYTE);

		$next = reset($userids);
		foreach ($ucodes as $code) {
			$curr = $next;
			if (!$curr) {
				break;
			}
			$next = next($userids);
			$vals[] = '('.$promoid.','.$curr.',"'.addslashes($code).'")';
		}
		if (!db_insert('promo_letter_unique','
			INSERT INTO promo_letter_unique (PROMOID,USERID,UNIQ)
			VALUES '.implode(',',$vals))
		) {
			return;
		}
		register_notice($unique_action == 'add' ? 'promo:notice:unique_codes_added_LINE' : 'promo:notice:unique_codes_replaced_LINE');
		break;
	}
}
function promo_display_uniques_form() {
	if (!require_admin('promo')
	||	!($promoid = require_idnumber($_REQUEST,'sID'))
	) {
		return;
	}
	layout_show_section_header();

	$cnt = get_promo_unique_code_count($promoid);
	if ($cnt === false) {
		return;
	}
	$v = get_v($promoid);
	if (!$v) return;

	$missingcnt = db_single(
		[$table = $v == 1 ? 'promo_letter' : 'promo_letter_v2','promo_letter_unique'],'
		SELECT COUNT(DISTINCT '.$table.'.USERID)
		FROM '.$table.'
		LEFT JOIN promo_letter_unique USING (PROMOID,USERID)
		WHERE PROMOID='.$promoid.'
		  AND ISNULL(promo_letter_unique.USERID)'
	);
	if ($missingcnt === false) {
		return;
	}
	layout_open_box('promo');
	layout_open_table();
	layout_start_row();
	echo Eelement_plural_name('unique_code');
	layout_field_value();
	echo $cnt;

	layout_restart_row();
	echo Eelement_plural_name('user_without_unique_code');
	layout_field_value();
	echo $missingcnt ? $missingcnt : __('counter:none');
	layout_stop_row();
	layout_close_table();
	layout_close_box();

	?><form method="post" enctype="multipart/form-data" action="/promo/<?= $promoid; ?>/processuniques" onsubmit="return submitForm(this)"><?
	layout_open_box('promo');
	layout_open_table(TABLE_CLEAN);
	layout_start_row();
	echo Eelement_name('file');
	layout_field_value();
	?><input type="file" name="CODESFILE" accept="text/plain" /><?

	layout_restart_row();
	echo Eelement_name('action');
	layout_field_value();
/*	if (!$cnt) {
*/		?><input type="hidden" name="UNIQUE_ACTION" value="add" /><?
		echo __('action:add');
/*	} else {
		?><select name="UNIQUE_ACTION"><?
			?><option value="replace"><?= __('action:replace'); ?></option><?
			?><option value="add"><?= __('action:add'); ?></option><?
		?></select><?
	}*/
	layout_stop_row();
	layout_close_table();
	layout_close_box();
	?><div class="block"><input type="submit" value="<?= __('action:process'); ?>" /><?
	?></form><?
}
function promo_past_deliveries() {
	if (!require_user()) {
		return;
	}
	require_once '_promolist.inc';
	layout_section_header(__C('promolist:header:old_promos'));

	$promoids = get_inactive_promoids();

	if (!$promoids) {
		?><p>Geen oude promoberichten gevonden.</p><?
		return;
	}
	require_once '_pagecontrols.inc';
	$controls = new _pagecontrols;
	$controls->set_per_page(25);
	$controls->include_all(false);
	$controls->show_prev_next();
	$controls->set_total(count($promoids));
	$controls->set_url('/promo/pastdeliveries');
	$controls->set_order('STARTSTAMP');

	$promos = db_rowuse_hash('promo','
		SELECT PROMOID,STOPSTAMP,STARTSTAMP,TITLE,COLOR,TEASER,CLICKBOX,CMTS,promo.FLAGS,TEASER_DESIGN,DISCOUNT
		FROM promo
		WHERE PROMOID IN ('.implode(',',$promoids).')'.
		$controls->order_and_limit()
	);
	if (!$promos) {
		?><p>Geen oude promoberichten gevonden.</p><?
		return;
	}
#	number_reverse_sort($promos,'STARTSTAMP');

	$controls->display_and_store();

	_promolist_display_res($promos,true);

	$controls->display_stored();
}
function promo_display_check() {
	if (!require_admin('promo')) {
		return;
	}
	layout_open_section_header();
	?>Invloed van prijs op opbrengst<?
	layout_close_section_header();

	if (isset($_REQUEST['MINIMUMPRICE'])) {
		if (false === require_number($_REQUEST,'MINIMUMPRICE')
		||	false === require_number($_REQUEST,'DAYDISCOUNT')
		||	false === require_number($_REQUEST,'CLASSDIVIDER')
		||	false === require_number($_REQUEST,'BASE')
		||	false === require_number($_REQUEST,'SEEN')
		||	false === require_number($_REQUEST,'RREAD')
		||	false === require_number($_REQUEST,'DAYS')
		) {
			return;
		}
		$newprice = $_REQUEST;
		$newprice['CRITERIA'] = 0;
	} else {
		$newprice = db_single_assoc('promoprice','SELECT * FROM promoprice ORDER BY PRICEID DESC LIMIT 1');
		if (!$newprice) {
			return;
		}
	}
	?><form method="get" action="/promo/checkprice" onsubmit="return submitForm(this)"><?
	layout_open_box('promo');
	layout_open_table('fw');
	layout_start_row();
	?>Minimumprijs<?
	layout_field_value();
	?><input class="right" type="number" data-valid="number" style="max-width:6.3em" name="MINIMUMPRICE" value="<?= getifset($_REQUEST,'MINIMUMPRICE') ?: $newprice['MINIMUMPRICE']; ?>" /><?

	layout_restart_row();
	?>Dagkorting reservering<?
	layout_field_value();
	?><input class="right" type="number" data-valid="number" min=0 max=100 style="max-width:6.3em" name="DAYDISCOUNT" value="<?= getifset($_REQUEST,'DAYDISCOUNT') ?: $newprice['DAYDISCOUNT']; ?>" />%<?

	layout_restart_row();
	?>Klassendeler<?
	layout_field_value();
	?><input class="right" type="number" data-valid="number" style="max-width:6.3em" name="CLASSDIVIDER" value="<?= getifset($_REQUEST,'CLASSDIVIDER') ?: $newprice['CLASSDIVIDER']; ?>" /><?

	layout_restart_separator_row();
	?>Reserveringskosten<?
	layout_field_value();
	?><input style="max-width:6.3em;text-align:right" type="number" size="5" name="BASE" value="<?= getifset($_REQUEST,'BASE') ?: $newprice['BASE']; ?>" /><?

	layout_restart_row();
	?>Gezien<?
	layout_field_value();
	?><input style="max-width:6.3em;text-align:right" type="number" size="5" name="SEEN" value="<?= getifset($_REQUEST,'SEEN') ?: $newprice['SEEN']; ?>" /><?

	layout_restart_row();
	?>Gelezen<?
	layout_field_value();
	?><input style="max-width:6.3em;text-align:right" type="number" size="5" name="RREAD" value="<?= getifset($_REQUEST,'RREAD') ?: $newprice['RREAD'] ?>" /><?

	layout_restart_separator_row();
	?>Testperiode<?
	layout_field_value();
	?><input style="max-width:6.3em;text-align:right" type="number" size="5" name="DAYS" value="<?= getifset($_REQUEST,'DAYS') ?: 31 ?>" /> dagen<?

	layout_stop_row();
	layout_close_table();
	layout_close_box();
	?><p><input type="submit" value="bepalen" /></p><?
	?></form><?

	if (!isset($_REQUEST['MINIMUMPRICE'])) {
		return;
	}
	$promos = db_rowuse_hash(
		'promo','
		SELECT PROMOID,STARTSTAMP,STOPSTAMP,PRICEID,REACH,TARGETPRICE,VERSION
		FROM promo
		WHERE ACTIVE=1
		  AND REMOVED=0
		  AND STOPSTAMP<='.TODAYSTAMP.'
		  AND STARTSTAMP>='.(TODAYSTAMP-$_REQUEST['DAYS']*24*3600)
	);
	if ($promos === false) {
		return;
	}
	if (!$promos) {
		?><p>Geen promoberichten in opgegeven testperiode.</p><?
		return;
	}
	$sentinfos = [];
	foreach (['promo_letter','promo_letter_v2'] as $table) {
		$tmpsentinfos = memcached_rowuse_hash($table,'
			SELECT	PROMOID,
				COUNT(*) AS TOTAL_CNT,
				COUNT(IF(FLAGS&'.PROMO_MESSAGE_CLOSED.',1,NULL)) AS READ_CNT,
				COUNT(IF(FLAGS&'.PROMO_TEASER_CLOSED.',1,NULL)) AS SEEN_CNT,
				COUNT(IF(FLAGS&'.PROMO_MESSAGE_SHOWN.',1,NULL)) AS VIEW_CNT,
				COUNT(IF(FLAGS&'.PROMO_TEASER_SHOWN.',1,NULL)) AS SCAN_CNT
			FROM '.$table.'
			WHERE PROMOID IN ('.implodekeys(',',$promos).')
			GROUP BY PROMOID'
		);
		if ($tmpsentinfos === false) {
			return;
		}
		if ($tmpsentinfos) {
			$sentinfos += $tmpsentinfos;
		}
	}
	$totalprice = 0;
	$totalnewprice = 0;
	$totalminprice = 0;
	$totalprice_w_min = 0;
	$totalnewprice_w_min = 0;
	layout_open_box('white');
	layout_open_table(TABLE_FULL_WIDTH);
	layout_start_header_row();
	layout_start_header_cell();
	?>Promo<?
	layout_next_header_cell_right();
	layout_next_header_cell_right();
	?>Oude prijs<?
	layout_next_header_cell_right();
	layout_next_header_cell_right();
	?>Nieuwe prijs<?
/*	layout_next_header_cell_right();
	?>Bereikt<?*/
	layout_next_header_cell_right();
	layout_stop_header_cell();
	layout_stop_header_row();
	foreach ($promos as $promoid => $promo) {
		if (!isset($sentinfos[$promoid])) {
			warning('Geen verzendinformatie bekend bij promo met ID '.$promoid);
			continue;
		}
		$sentinfo = $sentinfos[$promoid];
		$price = get_promopriceid($promo['PRICEID']);
		if (!$price) {
			continue;
		}
		if ($price['MINIMUMPRICE'] != $newprice['MINIMUMPRICE']) {
			$minchanged = true;
		}
		[$minprice,$maxprice] = get_promoprice($promo,$sentinfo['TOTAL_CNT'],$price);
		[$newminprice,$newmaxprice] = get_promoprice($promo,$sentinfo['TOTAL_CNT'],$newprice);

		$actualprice =	   $minprice + $sentinfo['SCAN_CNT'] *	$price['SEEN'] / 100 + $sentinfo['VIEW_CNT'] *	$price['RREAD'] / 100;
		$actualnewprice = $newminprice + $sentinfo['SCAN_CNT'] * $newprice['SEEN'] / 100 + $sentinfo['VIEW_CNT'] * $newprice['RREAD'] / 100;

		$prc = 100 * $actualnewprice / $actualprice - 100;
		if ($actualprice != $actualnewprice) {
			layout_start_row(0,$prc > 0 ? 'accept' : 'deny');
		} else {
			layout_start_row();
		}
		?><a href="/promo/<?= $promo['PROMOID'];
		?>"><?= $promo['PROMOID'];
		?></a><?
		layout_field_value_right();
		?><span class="light"><? printf('%.2f',$minprice/100) ?></span><?
		layout_next_cell(class: 'right');
		printf('%.2f',$actualprice/100);
		layout_next_cell(class: 'right');
		?><span class="light"><? printf('%.2f',$newminprice/100) ?></span><?
		layout_next_cell(class: 'right');
		printf('%.2f',$actualnewprice/100);
		#layout_next_cell(class: 'right');
		#printf('%.2f => %.2f',$maxprice,$newmaxprice);
		#layout_next_cell(class: 'right');
		#printf('%.2f',100 * $sentinfo['SCAN_CNT'] / $sentinfo['TOTAL_CNT']);
		layout_next_cell(class: 'right');
		printf('%.2f',$prc); ?>%<?
		layout_stop_row();
		$totalprice += $actualprice;
		$totalnewprice += $actualnewprice;
		if ($newminprice >= $newprice['MINIMUMPRICE']) {
			$totalminprice += $actualnewprice;
		}
		if ($actualprice < $price['MINIMUMPRICE']) {
			$actualprice = $price['MINIMUMPRICE'];
		}
		if ($actualnewprice < $newprice['MINIMUMPRICE']) {
			$actualnewprice = $newprice['MINIMUMPRICE'];
		}
		$totalprice_w_min += $actualprice;
		$totalnewprice_w_min += $actualnewprice;
	}
	$infos = array(
		'totaal (geen minimum)' => array($totalnewprice,$totalprice)
	);
	if (isset($minchanged)) {
		$infos['totaal (niemand gaat voor nieuwe minimum)'] = array($totalminprice,$totalprice_w_min);
		$infos['totaal (iedereen gaat voor nieuwe minimum)']= array($totalnewprice_w_min,$totalprice_w_min);
	} else {
		$infos['totaal'] = array($totalnewprice_w_min,$totalprice_w_min);
	}
	foreach ($infos as $desc => $info) {
		[$newprice,$price] = $info;
		$prc = 100 * $newprice / $price - 100;
		if ($newprice != $price) {
			layout_start_row(0,'selected '.($prc > 0 ? 'accept' : 'deny'));
		} else {
			layout_start_row(0,'selected');
		}
		echo $desc;
		layout_field_value_right();
		printf('%.2f',$price/100);
		layout_next_cell(class: 'right');
		printf('%.2f',$newprice/100);
		layout_next_cell(class: 'right');
		printf('%.2f',$prc); ?>%<?
		layout_stop_row();
	}
	layout_close_table();
	layout_close_box();
}
function remove_letters($promoid,$table) {
	$qstr = 'DELETE FROM '.$table.' WHERE PROMOID='.$promoid.' LIMIT '.PROMO_DELETION_AMOUNT;
	while (($res = db_delete($table,$qstr))) {
		if (db_affected() != PROMO_DELETION_AMOUNT) {
			break;
		}
	}
	return $res;
}

function require_inactive_promo($promoid,$active = null) {
	if ($active === null) {
		$active = db_single('promo', 'SELECT ACTIVE FROM promo WHERE PROMOID = '.$promoid);
		if (querty_failed()) {
			return false;
		}
		if ($active === null) {
			register_error('promo:error:nonexistent_LINE',array('ID'=>$promoid));
			return false;
		}
		if (!$active) {
			return true;
		}
	} elseif (!$active) {
		return true;
	}
	_error('Promo met ID '.$promoid.' mag voor deze operatie niet geactiveerd zijn!');
	return false;
}

function promo_menu(?array $promo = null, bool $is_admin = false, bool $is_relation = false, bool $have_lock = false): void {
	$testpromo = $_REQUEST['sID'] === 9842;
	$v = $promo['VERSION'] ?? 0;
	layout_open_menu();
	if ($is_admin
	||	$is_relation
	) {
		layout_menuitem(Eelement_name('overview'),'/promo',!$_REQUEST['ACTION']);
		layout_menuitem(Eelement_name('archive'),'/promo/archive',$_REQUEST['ACTION'] === 'archive');
		if ($is_admin) {
			layout_menuitem(Eelement_name('test_promo'),'/promo/9842',$_REQUEST['ACTION'] === 'single' && $testpromo);
		}
	}
	layout_continue_menu();
	require_once '_info.inc';
	show_info_menu();
	layout_close_menu();

/*	if (doc_active()
	||	!$promo && !$is_admin
	) {
		return;
	}*/
	if (!$promo) {
		if ($is_admin) {
			layout_open_menu();
			layout_menuitem(__C('action:add'),'/promo/form');
			layout_close_menu();
		}
		return;
	}
	$promoid = $promo['PROMOID'];
	$promohref = '/promo/'.$promoid;
	if (!$have_lock) {
		if ($have_lock === false) {
			?><div class="block"><?
			display_lockinfo(LOCK_PROMO, $promoid);
			?></div><?
		}
		if ($is_relation
		&&	!$promo['REQUEST']
		) {
			layout_open_menu();
			layout_menuitem(__C('action:change_duration_or_budget'),'/ticket/form?ELEMENT=promo;ID='.$promoid);
			layout_close_menu();
		}
		return;
	}
	if (!$is_admin) {
		return;
	}
	layout_open_menu();
	layout_menuitem(__C('action:change'),$promohref.'/form');
	if (!$promo['ACTIVE']) {
		if (!$testpromo && $promo['POLLS']) {
			layout_menuitem(__C('action:add_poll'),'/poll/newform/promo?ELEMENTID='.$promoid);
		}
		layout_menuitem(Eelement_name('focus'),$promohref.'/focus');
	}
	require_once '_connect.inc';
	layout_open_menuitem();
	if ($promo['BEFOREGOING'] !== null) {
		?><span class="unavailable"><?= __C('action:connect') ?></span><?
	} else {
		connect_menuitem("setdisplay('connect-request',true,true);");
	}
	layout_close_menuitem();
	if (!$testpromo) {
		layout_menuitem(
			__C('action:send_message'),
			$promo['TICKETID']
		?	'/ticket/'.$promo['TICKETID']
		:	'/ticket/outgoing-form?USETEMPLATE=generic;ELEMENT=promo;ID='.$promoid
		);
	}
	layout_menuitem(
		__C('action:send_costindication'),
		'/ticket/'.($promo['TICKETID'] ?: 'outgoing-form').
		'?USETEMPLATE=promocost;PROMOID='.$promoid,
		$promo['COSTSENT']
	?	' class="light"'
	:	null
	);
	if ($promo['FLAGS'] & PROMO_UNIQUE_PART) {
		$cnt = get_promo_unique_code_count($promoid);
		layout_menuitem(__C($cnt ? 'action:change_unique_codes' : 'action:add_unique_codes'),$promohref.'/uniquesform');
	}
#	display_paymenu($promo);
	layout_continue_menu();

	require_once '_adlink.inc';
	show_adlink($promo);

	if (!$promo['ACTIVE'] && !$testpromo) {
		$have_promo_letters = db_single(
			$table = $v == 1 ? 'promo_letter' : 'promo_letter_v2','
			SELECT 1
			FROM '.$table.'
			WHERE PROMOID='.$promoid.'
			LIMIT 1',DB_USE_MASTER
		);
		if ($have_promo_letters !== false) {
			if (!$have_promo_letters) {
				layout_menuitem(__C('action:reserve_reach'),$promohref.'/send');
			} else {
				layout_menuitem(__C('action:free_reach'),$promohref.'/cancel');
			}
		}
	}
	if (!$promo['REMOVED']) {
		if (!$promo['ACTIVE']) {
			if ($promo['SENT_STAMP']) {
				$haveconnects = db_single(
					'connect','
					SELECT 1 FROM connect
					WHERE MAINTYPE="promo"
					  AND MAINID='.$promoid.'
					  AND ASSOCTYPE="party"
					LIMIT 1'
				);

				if ($haveconnects
				&&	$promo['BODY']
				&&	$promo['TEASER']
				&&	(	$promo['VERSION'] >= 2
					||	$promo['TARGETPRICE']
					||	$promo['TARGETPRICE'] === null
					)
				) {
					layout_menuitem(__C('action:activate'),$promohref.'/activate');
				} else {
					require_once '_bubble.inc';
					$bubble = new bubble(CLICK_BUBBLE);
					$bubble->catcher(__C('action:activate'));
					$bubble->content();
					if (!$haveconnects) {
						?><div class="block"><b>Geen gekoppelde feesten!</b><br /><?
						?>Er worden dan <span class="underline">geen bezoekersaantallen</span> bijgehouden.<br /><?
						?><i>Ook niet als je na activeren koppelt!</i><br /><?
						?></div><?
					}
					if (!$promo['TEASER']) {
						?><div class="block"><b>Geen teaser!</b><br /><?
						?>Het teaserbericht is nog leeg!</div><?
					}
					if (!$promo['BODY']) {
						?><div class="block"><b>Geen bericht!</b><br /><?
						?>Het bericht bij deze promo is nog leeg!</div><?
					}
					if ($promo['VERSION'] == 1
					&&	$promo['TARGETPRICE'] == '0'
					) {
						?><div class="block"><b>Onmogelijke streefprijs</b><br /><?
						?>Streefprijs van het bericht is lager dan de minimumprijs!</div><?
					}
					?><div class="block"><a href="<?= $promohref; ?>/activate"><?= __C('action:activate_anyway'); ?></a></div><?
					$bubblecontent = $bubble->get();
					layout_open_menuitem();
					echo $bubblecontent;
					layout_close_menuitem();
				}
			}
		} else {
			layout_menuitem(__C('action:deactivate'),$promohref.'/deactivate');
		}
	}
	if (!$promo['ACTIVE'] && !$testpromo) {
		show_element_menuitems($promo);
	}
	layout_close_menu();
}

function promo_set_active(bool $bool): bool {
	if (!require_admin('promo')
	||	!($promoid = $_REQUEST['sID'])
	||	!require_last_lock(LOCK_PROMO, $promoid)
	) {
		return false;
	}
	if ($bool) {
		$v = get_v($promoid);
		if (!$v) {
			return null;
		}
		$have_letters = db_single(
			$table = $v === 1 ? 'promo_letter' : 'promo_letter_v2','
			SELECT b\'1\'
			FROM '.$table.'
			WHERE PROMOID = '.$promoid.'
			LIMIT 1'
		);
		if (query_failed()) {
			return false;
		}
		if (!$have_letters) {
			register_error('promo:error:no_letters_LINE');
			return false;
		}
	}
	$max_stamp = db_single(['connect','party'],'
		SELECT MAX(STAMP + DURATION_SECS + 2 * '.ONE_DAY.')
		FROM connect
		JOIN party ON ASSOCID = PARTYID
		WHERE MAINTYPE = "promo"
		  AND MAINID = '.$promoid.'
		  AND ASSOCTYPE = "party"'
	);
	if ($max_stamp === false) {
		return false;
	}
	if (!$max_stamp) {
		$max_stamp = 0;
	}
	if (!db_insert('promo_log','
		INSERT INTO promo_log
		SELECT * FROM promo
		WHERE ACTIVE != '.($bool ? '1' : '0').'
		  AND PROMOID = '.$promoid)
	||	!db_update('promo','
		UPDATE promo SET
			ACTIVE		= '.($bool ? '1' : '0').',
			MSTAMP		= '.CURRENTSTAMP.',
			MUSERID		= '.CURRENTUSERID.',
			REALSTOPSTAMP	= IF(	'.$max_stamp.' AND '.$max_stamp.' > STARTSTAMP,
						'.$max_stamp.',
						STOPSTAMP - '.ONE_MONTH.'
					  )
		WHERE ACTIVE = '.($bool ? '0' : '1').'
		  AND PROMOID = '.$promoid)
	) {
		return false;
	}
	register_notice($bool ? 'promo:notice:activated_LINE' : 'promo:notice:deactivated_LINE');
	return true;
}

function promo_remove(bool $remove): bool {
	if (!($promoid = require_idnumber($_REQUEST,'sID'))
	||	!require_admin('promo')
	||	!require_post()
	||	!require_inactive_promo($promoid)
	||	!require_last_lock(LOCK_PROMO, $promoid)
	||	!move_promo($promoid, 'promo_letter_v2', 'promo_letter_concept')
	||	!db_insert('promo_log','
		INSERT INTO promo_log
		SELECT * FROM promo
		WHERE REMOVED = '.($remove ? '0' : '1').'
		  AND PROMOID = '.$promoid)
	||	!db_update('promo','
		UPDATE promo SET
			REMOVED		= '.($remove ? '1' : '0').',
			SENT_STAMP	= 0
		WHERE REMOVED = '.($remove ? '0' : '1').'
		  AND PROMOID = '.$promoid)
	) {
		return false;
	}
	register_notice($remove ? 'promo:notice:removed_LINE' : 'promo:notice:restored_LINE');
	if ($remove) {
		register_notice('promo:notice:reach_release_LINE');
		if (!db_insert('contact_ticket_log','
			INSERT INTO contact_ticket_log
			SELECT * FROM contact_ticket
			WHERE STATUS = "pending"
			  AND ELEMENT = "promo"
			  AND ID = '.$promoid)
		) {
			return false;
		}
		if (db_affected()) {
			if (!db_update('contact_ticket','
				UPDATE contact_ticket SET
					MSTAMP		= '.CURRENTSTAMP.',
					MUSERID		= '.CURRENTUSERID.',
					CLOSETYPE	= "auto",
					STATUS		= "closed"
				WHERE STATUS = "pending"
				  AND ELEMENT = "promo"
				  AND ID = '.$promoid)
			) {
				return false;
			}
			register_notice('promo:notice:correspondence_closed_LINE');
		}
	}
	return true;
}

function promo_display_letter() {
	if (!require_idnumber($_REQUEST,'sID')
	||	!require_user()
	) {
		return;
	}
	$promoid = $_REQUEST['sID'];
	$promo = db_single_assoc('promo','
		SELECT COLOR,PROMOID,BODY,MESSAGETITLE,STARTSTAMP,STOPSTAMP,ACTIVE,REMOVED,CMTS,RELATIONID,TARGETPRICE,ABSTARGET,PRICEID,FLAGS,VERSION,REALSTOPSTAMP,DISCOUNT
		FROM promo
		WHERE PROMOID='.$promoid
#		0,
#		'promoletter:'.$promoid
	);
	if ($promo === false) {
		return;
	}
	if (!$promo) {
		register_error('promo:error:nonexistent_LINE',array('ID'=>$promoid));
		return;
	}
	$v = $promo['VERSION'];
	$finished = ($promo['STOPSTAMP'] <= CURRENTSTAMP);
	layout_open_section_header();
	if (have_admin('promo')
	||	have_relation($promo['RELATIONID'])
	) {
		?><a href="<?= get_element_href('promo',$promoid) ?>"><?= Eelement_name('promo') ?></a> <small class="light"><?= MIDDLE_DOT_ENTITY ?> <?= $promoid ?></small><?
	} else {
		echo Eelement_name('promo');
	}
	layout_close_section_header();

	if (!$promo['ACTIVE']) {
		warning('Promo met ID '.$promoid.' is niet actief!');
		return;
	}
	if (!$finished) {
		if (CURRENTSTAMP < $promo['STARTSTAMP']) {
			warning('Promo met ID '.$promoid.' is nog niet gestart!');
			return;
		}
/*		$kept = db_single('promokept','
			SELECT KEPT
			FROM promokept
			WHERE PROMOID='.$promoid.'
			  AND USERID='.CURRENTUSERID
		);*/
	}
	_promo_display($promo,$finished ? false : true);

	if (CURRENTSTAMP < $promo['REALSTOPSTAMP']) {
		?><div class="funcs"><?
		include_js('js/promo');
		?><a href="/msg/markpromoread/<?= $promoid ?>" onclick="return Pf.markPromo(<?= $promoid ?>,'read')"><?= __C('action:mark_read') ?></a><?
		?></div><?
	}
	if ($promo['CMTS']) {
		$cmts = new _commentlist;
		$cmts->item($promo);
		$cmts->display();
	}
	if ($finished) {
		# no accounting for finished promo's
		return;
	}
	db_update($table = $v == 1 ? 'promo_letter' : 'promo_letter_v2','
		UPDATE '.$table.' SET
			FLAGS=FLAGS|'.(PROMO_MESSAGE_SHOWN|PROMO_TEASER_SHOWN).'
		WHERE !(FLAGS&'.PROMO_MESSAGE_SHOWN.')
		  AND PROMOID='.$promoid.'
		  AND USERID='.CURRENTUSERID,
		DB_KEEP_CLEAN
	);
}
function require_unreserved_promo($promoid) {
	$alreadysent = db_single('promo','SELECT SENT_STAMP FROM promo WHERE PROMOID='.$promoid,DB_FORCE_MASTER);
	if ($alreadysent === false) {
		return false;
	}
	if ($alreadysent) {
		warning('Promobericht is reeds gereserveerd!');
		return false;
	}
	return true;
}
function create_concepts($promoid,$vals) {
	$slices = ceil(count($vals) / PROMO_INSERTION_AMOUNT);
	for ($i = 0; $i < $slices; ++$i) {
		$slice = array_slice($vals,$i * PROMO_INSERTION_AMOUNT,PROMO_INSERTION_AMOUNT);
		if (!db_insert('promo_letter_concept','INSERT LOW_PRIORITY INTO promo_letter_concept (PROMOID,USERID) VALUES '.implode(',',$slice))) {
			return;
		}
	}
}
function move_promo($promoid,$from,$to) {
	return actually_move_promo($promoid,$from,$to);
}
function actually_move_promo($promoid,$from,$to) {
	$userids = db_simpler_array($from,'SELECT USERID FROM '.$from.' WHERE PROMOID='.$promoid,DB_FORCE_MASTER);
	if ($userids === false) {
		return false;
	}
	if (!$userids) {
		return true;
	}
	$size = count($userids);
	reset($userids);
	unset($qstr);
	for ($i = 0; $i < $size; ++$i) {
		if (!isset($qstr)) {
			$qstr = 'INSERT LOW_PRIORITY IGNORE INTO '.$to.' (PROMOID,USERID) VALUES ';
		} else {
			$qstr .= ',';
		}
		$qstr .= '('.$promoid.','.current($userids).')';
		if (($i == $size-1)
		||	($i % PROMO_INSERTION_AMOUNT == 0)
		) {
			if (!db_insert($to,$qstr)) {
				return false;
			}
			unset($qstr);
		}
		next($userids);
	}
	if (!remove_letters($promoid,$from)) {
		return false;
	}
	if ($to == 'promo_letter' || $to == 'promo_letter_v2') {
		// force receive for relations
		$forceids = db_simpler_array(['relationmember','promo'],'
			SELECT relationmember.USERID
			FROM relationmember
			JOIN promo USING (RELATIONID)
			WHERE PROMOID='.$promoid
		);
		if ($forceids) {
			db_update($to,'
			UPDATE '.$to.'
			SET FLAGS=FLAGS|'.PROMO_FORCE_RECEIVE.'
			WHERE USERID IN ('.implode(',',$forceids).')
			  AND PROMOID='.$promoid
			);
		}
	}
	return true;
}
function promo_send() {
	if (require_admin('promo')
	&&	($promoid = require_idnumber($_REQUEST,'sID'))
	&&	require_inactive_promo($promoid)
	&&	require_unreserved_promo($promoid)
	&&	require_getlock($lockname = 'promo_send:'.$promoid,1)
	&&	require_last_lock(LOCK_PROMO,$promoid)
	&&	move_promo($promoid,'promo_letter_concept','promo_letter_v2')
	&&	db_update('promo','
		UPDATE promo SET SENT_STAMP='.CURRENTSTAMP.'
		WHERE PROMOID='.$promoid)
	) {
		register_notice('promo:notice:reach_reserved_LINE');
	}
	if (isset($lockname)) {
		db_releaselock($lockname);
	}
}
function promo_cancel() {
	if (require_admin('promo')
	&&	($promoid = require_idnumber($_REQUEST,'sID'))
	&&	require_inactive_promo($promoid)
	&&	require_getlock($lockname = 'promo_send:'.$promoid,1)
	&&	require_last_lock(LOCK_PROMO,$promoid)
	&&	move_promo($promoid,'promo_letter_v2','promo_letter_concept')
	&&	db_update('promo','
		UPDATE promo SET SENT_STAMP=0
		WHERE PROMOID='.$promoid)
	) {
		register_notice('promo:notice:reach_released_LINE');
	}
	if (isset($lockname)) {
		db_releaselock($lockname);
	}
}
function addids(&$ids,$currentgroup,$inclusive,$userids) {
	if ($userids) {
		if ($inclusive) {
			if (isset($ids[$currentgroup][1])) {
				$ids[$currentgroup][1] = array_intersect_key($ids[$currentgroup][1],$userids);
			} else {
				$ids[$currentgroup][1] = $userids;
			}
		} else {
			$ids[$currentgroup][0] += $userids;
		}
	} elseif ($inclusive) {
		$ids[$currentgroup]['DEAD'] = true;
	}
}
function promo_determine_userids($promoid,&$groupcnt,&$foci) {
	// FIXME: use tmp tables on mysqlserver, lots faster! no more hauling of ginormous userids from and to server

	$groupcnt = 0;
	$foci = db_rowuse_array('promo_focus','
		SELECT TYPE,INCLUSIVE,ID,OPTVAL,GROUPID
		FROM promo_focus
		WHERE PROMOID='.$promoid.'
		ORDER BY GROUPID'
	);
	if (!$foci) {
		return null;
	}
	$currentgroup = null;
	foreach ($foci as $focus) {
		$groupid = $focus['GROUPID'];
		if ($currentgroup !== $groupid) {
			$currentgroup = $groupid;
			++$groupcnt;
			$havegroup[$groupid] = true;
		}
	}
/*	if ($groupcnt == 2
	&&	isset($havegroup[0])
	) {
		# two groups, one of which is global. combine these
		$ids[0][0] = array();
		$groupcnt = 1;
	} else {*/
		$currentgroup = $foci[0]['GROUPID'];
		$focuscnt = 0;
		$countries = array();
		$i = 0;
		# if country is not the only focus, we can use country as a filter
		# instead of selector
		# that's quite a bit faster because amount of users in a country is huge and this way we can just use
		# the selected userids and filter out anyone (not) in a country
		#
		# note that there was something similar for genres. but don't know why it was (partially) removed
		#
		# FIXME: shouldn't this be done for provinces too?
		#	  currently, countries and provinces are converted to cities. for countries this now is not
		#	  a problem because they are converted to filters if there are other criteria.
		#	  provinces are borked when implicitely used as filter
		for ($focus = reset($foci); ; $focus = next($foci)) {
			if (!$focus
			||	$currentgroup !== $focus['GROUPID']
			) {
				if ($focuscnt) {
					if ($countries) {
						if (count($countries) !== $focuscnt) {
							foreach ($countries as $ndx) {
								$foci[$ndx]['FILTER'] = true;
							}
						}
					}
				}
				$focuscnt = 0;
				$countries = array();
				$ids[$currentgroup][0] = array();
				if (!$focus) {
					break;
				}
				$currentgroup = $focus['GROUPID'];
			}
			++$focuscnt;
			switch ($focus['TYPE']) {
			case 'country':
				$countries[] = $i;
				break;
			}
			++$i;
		}
/*	}*/
	$currentgroup = 0;
	for ($focus = reset($foci); ; $focus = next($foci)) {
		if (!$focus
		||	$currentgroup != $focus['GROUPID']
		) {
			// CITYRANGES
			if (isset($cityranges)) {
				foreach ($cityranges as $inclusive => $cityrangelist) {
					foreach ($cityrangelist as $cityid => $radius) {
						$city = db_single_assoc('city','SELECT LATITUDE,LONGITUDE FROM city WHERE CITYID='.$cityid);
						if (!$city) {
							return false;
						}
						$rangeparts[] =
						'('.distance($city['LATITUDE'],$city['LONGITUDE'],'LATITUDE','LONGITUDE').' <= '.$radius.')';
					}
					$citylist = memcached_simpler_array('city','
						SELECT DISTINCT CITYID
						FROM city
						WHERE '.implode(' OR ',$rangeparts),
						TEN_MINUTES
					);
					if ($citylist) {
						if (isset($cities[$inclusive])) {
							$cities[$inclusive] = array_merge($cities[$inclusive],$citylist);
						} else {
							$cities[$inclusive] = $citylist;
						}
					} elseif ($inclusive) {
						$ids[$currentgroup]['DEAD'] = true;
					}
					unset($rangeparts);
				}
				unset($cityranges);
			}
			// CITIES
			if (isset($cities)) {
				foreach ($cities as $inclusive => $citylist) {
					sort($citylist);
					$userids = memcached_boolean_hash(
						array('user','user_account'),'
						SELECT USERID
						FROM user
						JOIN user_account USING (USERID)
						WHERE STATUS="active"
						  AND CITYID IN ('.implode(',',$citylist).')',
						TEN_MINUTES
					);
					if ($userids === false) {
						return false;
					}
					addids($ids,$currentgroup,$inclusive,$userids);
				}
				unset($cities);
			}
			// GENRES
			if (isset($genres)) {
				foreach ($genres as $inclusive => $genrelist) {
					$userids = memcached_boolean_hash('genre_fan','
						SELECT DISTINCT USERID
						FROM genre_fan
						JOIN user_account USING (USERID)
						WHERE GID IN ('.implode(',',$genrelist).')
						  AND STATUS="active"',
						TEN_MINUTES
					);
					if ($userids === false) {
						return false;
					}
					addids($ids,$currentgroup,$inclusive,$userids);
				}
				unset($genres);
			}
			if (isset($erotics)) {
				static $__erotic_partyids = 0;
				if ($__erotic_partyids === 0) {
					$__erotic_partyids = memcached_simpler_array('party','SELECT PARTYID FROM party WHERE EROTIC=1',TEN_MINUTES);
				}
				foreach ($erotics as $inclusive => $maybes) {
					foreach ($maybes as $going) {
						foreach ($__erotic_partyids as $partyid) {
							$parties[$inclusive][$going][] = $partyid;
						}
					}
				}
				unset($erotics);
			}
			// PARTIES
			if (isset($parties)) {
				foreach ($parties as $inclusive => $partiespermaybe) {
					$log = false;
					foreach ($partiespermaybe as $going => $partylist) {
						switch ($going) {
 						case 0:	$wherep = 'AND MAYBE=1'; break;
 						case 1: $wherep = 'AND MAYBE=0'; break;
 						case 2: $wherep = null; break;
 						case 3: $wherep = false; break;
 						}
 						$partyidstr = implode(',',$partylist);
 						if ($wherep !== false) {
							$maybeparts[] =	'
							(	SELECT DISTINCT USERID
								FROM going
								JOIN user_account USING (USERID)
								WHERE PARTYID IN ('.$partyidstr.')
								  AND STATUS="active" '.
								 $wherep.'
							)';
						} else {
							$maybeparts[] = '(
								SELECT DISTINCT USERID FROM (
									SELECT DISTINCT USERID
									FROM going_log
									WHERE (ACTION IN ("downgrade","upgrade") OR ACTION IS NULL)
									  AND PARTYID IN ('.$partyidstr.')
									UNION 
									SELECT USERID
									FROM going
									WHERE PARTYID IN ('.$partyidstr.')
								) AS allgoing
								JOIN user_account USING (USERID)
								WHERE STATUS="active"
							)';
							$log = true;
						}
					}
					$userids = memcached_boolean_hash(
						$log ? array('going_log','going','user_account') : array('going','user_account'),
						implode(' UNION ',$maybeparts),
						TEN_MINUTES
					);
					unset($maybeparts);
					if ($userids === false) {
						return false;
					}
					addids($ids,$currentgroup,$inclusive,$userids);
				}
				unset($parties);
			}
			// FLOCKS
			if (isset($flocks)) {
				foreach ($flocks as $inclusive => $flocklist) {
					$userids = memcached_boolean_hash(
						array('flockmember','user_account'),'
						SELECT DISTINCT USERID
						FROM flockmember
						JOIN user_account USING (USERID)
						WHERE FLOCKID IN ('.implode(',',$flocklist).')
						  AND STATUS="active"',
						TEN_MINUTES
					);
					if ($userids === false) {
						return false;
					}
					addids($ids,$currentgroup,$inclusive,$userids);
				}
				unset($flocks);
			}
			// CONTESTS
			if (isset($contests)) {
				foreach ($contests as $inclusive => $contestlist) {
					$userids = memcached_boolean_hash(
						array('contestresponse','user_account'),'
						SELECT DISTINCT USERID
						FROM contestresponse
						JOIN user_account USING (USERID)
						WHERE CONTESTID IN ('.implode(',',$contestlist).')
						  AND STATUS="active"',
						TEN_MINUTES
					);
					if ($userids === false) {
						return false;
					}
					addids($ids,$currentgroup,$inclusive,$userids);
				}
				unset($contests);
			}
			// USERS
			if (isset($users)) {
				foreach ($users as $inclusive => $userids) {
					addids($ids,$currentgroup,$inclusive,$userids);
				}
				unset($users);
			}
			if (isset($favs)) {
				foreach ($favs as $element => $items) {
					foreach ($items as $inclusive => $list) {
						$userids = memcached_boolean_hash(
							array('favourite','user_account'),'
							SELECT DISTINCT USERID
	 						FROM favourite
							JOIN user_account USING (USERID)
							WHERE STATUS="active"
							  AND ELEMENT="'.$element.'"
							  AND ID IN ('.implode(',',$list).')',
							TEN_MINUTES
						);
						if ($userids === false) {
							return false;
						}
						addids($ids,$currentgroup,$inclusive,$userids);
					}
				}
				unset($favs);
			}
			if (isset($promos)) {
				foreach ($promos as $inclusive => $promolist) {
					$orparts = null;
					foreach ($promolist as $usepromoid => $optval) {
						$not = $optval ? null : '!';
						$orparts[] = 'PROMOID='.$usepromoid.' AND '.$not.'(FLAGS&'.PROMO_TEASER_SHOWN.')';
					}
					foreach (['promo_letter','promo_letter_v2'] as $table) {
						$userids = memcached_boolean_hash($table,'
							SELECT USERID
							FROM '.$table.'
							WHERE '.implode(' OR ',$orparts),
							FIVE_MINUTES
						);
						if ($userids === false) {
							return false;
						}
						if ($userids) {
							addids($ids,$currentgroup,$inclusive,$userids);
						}
					}
				}
				unset($promos);
			}
			if (isset($buddies)) {
				if (!function_exists('get_buddies')) {
					function get_buddies($userid) {
						return memcached_boolean_hash(array('buddy','user_account'),'
							SELECT DISTINCT USERID_INI
							FROM buddy
							JOIN user_account ON USERID_INI=USERID
							WHERE USERID_ACC='.$userid.'
							  AND STATUS="active"
							UNION
							SELECT DISTINCT USERID_ACC
							FROM buddy
							JOIN user_account ON USERID_ACC=USERID
							WHERE USERID_INI='.$userid.'
							  AND STATUS="active"',
							FIVE_MINUTES
						);
					}
				}
				foreach ($buddies as $inclusive => $buddylist) {
					$alluserids = array();
					foreach ($buddylist as $userid => $includemore) {
						$userids = get_buddies($userid);
						if ($userids === false) {
							return;
						}
						if ($userids) {
							if ($includemore) {
								foreach ($userids as $userid => $true) {
									$moreuserids = get_buddies($userid);
									if ($moreuserids === false) {
										return;
									}
									if ($moreuserids) {
										$alluserids += $moreuserids;
									}
								}
							}
							$alluserids += $userids;
						}
					}
					addids($ids,$currentgroup,$inclusive,$alluserids);
				}
				unset($buddies);
			}
			if (isset($locations)) {
				foreach ($locations as $inclusive => $locationlist) {
					unset($simples);
					unset($complex);
					foreach ($locationlist as $locationid => $stamp) {
						if (!$stamp) {
							$simples[] = $locationid;
						} else {
							$complex[] = '(LOCATIONID='.$locationid.' AND STAMP>='.$stamp.')';
						}
					}
					$userids = array();
					if (isset($simples)) {
						$userids_simples = memcached_boolean_hash(
							array('going','party','user_account'),'
							SELECT DISTINCT going.USERID
							FROM party
							JOIN going USING (PARTYID)
							JOIN user_account ON user_account.USERID=going.USERID
							WHERE MAYBE=0
							  AND LOCATIONID IN ('.implode(',',$simples).')
							  AND STATUS="active"',
							TEN_MINUTES
						);
						if ($userids_simples === false) {
							return false;
						}
						if ($userids_simples) {
							$userids += $userids_simples;
						}
					}
					if (isset($complex)) {
						$userids_complex = memcached_boolean_hash(
							array('going','party','user_account'),'
							SELECT DISTINCT going.USERID
							FROM party
							JOIN going USING (PARTYID)
							JOIN user_account ON user_account.USERID=going.USERID
							WHERE MAYBE=0
							  AND STATUS="active"
							  AND ('.implode(' OR ',$complex).')',
							TEN_MINUTES
						);
						if ($userids_complex === false) {
							return false;
						}
						if ($userids_complex) {
							$userids += $userids_complex;
						}
					}
					addids($ids,$currentgroup,$inclusive,$userids);
				}
				unset($locations);
			}
			if (isset($organizations)) {
				foreach ($organizations as $inclusive => $organizationlist) {
					unset($simples);
					unset($complex);
					foreach ($organizationlist as $organizationid => $stamp) {
						if (!$stamp) {
							$simples[] = $organizationid;
						} else {
							$complex[] = '(MAINID='.$organizationid.' AND STAMP>='.$stamp.')';
						}
					}
					$userids = array();
					if (isset($simples)) {
						$userids_simples = memcached_boolean_hash(
							array('going','connect','user_account'),'
							SELECT DISTINCT going.USERID
							FROM connect
							JOIN going ON going.PARTYID=ASSOCID
							JOIN user_account ON user_account.USERID=going.USERID
							WHERE MAYBE=0
							  AND MAINTYPE="organization"
							  AND MAINID IN ('.implode(',',$simples).')
							  AND ASSOCTYPE="party"
							  AND STATUS="active"',
							TEN_MINUTES
						);
						if ($userids_simples === false) {
							return false;
						}
						if ($userids_simples) {
							$userids += $userids_simples;
						}
						unset($userids_simples);
					}
					if (isset($complex)) {
						$userids_complex = memcached_boolean_hash(
							array('going','connect','party','user_account'),'
							SELECT DISTINCT going.USERID
							FROM connect
							JOIN party ON party.PARTYID=ASSOCID
							JOIN going ON going.PARTYID=ASSOCID
							JOIN user_account ON user_account.USERID=going.USERID
							WHERE MAYBE=0
							  AND MAINTYPE="organization"
							  AND ASSOCTYPE="party"
							  AND ('.implode(' OR ',$complex).')
							  AND STATUS="active"',
							TEN_MINUTES
						);
						if ($userids_complex === false) {
							return false;
						}
						if ($userids_complex) {
							$userids += $userids_complex;
						}
						unset($userids_complex);
					}
					addids($ids,$currentgroup,$inclusive,$userids);
				}
				unset($organizations);
			}
			// FREEDATES
			if (isset($freedates)) {
				foreach ($freedates as $inclusive => $stamps) {
					$wheres = array();
					foreach ($stamps as $stamp => $optval) {
						$wheres[$optval][] = 'STAMP BETWEEN '.($stamp+6*3600+1).' AND '.($stamp+30*3600-1);
					}
					$wherep = null;
					# OPTVAL=0, require any going
					# OPTVAL=1, require certain
					# OPTVAL=2, require option
					foreach ($wheres as $optval => $stampranges) {
						$stampor = '('.implode(' OR ',$stampranges).')';
						switch ($optval) {
						case 0: $wherep[] = $stampor; break;
						case 1: $wherep[] = '(MAYBE=0 AND '.$stampor.')'; break;
						case 2: $wherep[] = '(MAYBE=1 AND '.$stampor.')'; break;
						}
					}
					$userids = memcached_boolean_hash(
						['going','party','user_account'],'
						SELECT DISTINCT going.USERID
						FROM party
						JOIN going USING (PARTYID)
						JOIN user_account ON user_account.USERID=going.USERID
						WHERE STATUS="active"'.
						(	$wherep
						?	'  AND ('.implode(' OR ',$wherep).')'
						:	null
						),
						TEN_MINUTES
					);
					if ($userids === false) {
						return false;
					}
					addids($ids,$currentgroup,!$inclusive,$userids);
				}
				unset($freedates);
			}
			if (isset($employees)) {
				foreach ($employees as $inclusive => $list) {
					addids($ids,$currentgroup,$inclusive,$list);
				}
				unset($employees);
			}
			if (isset($birthdates)) {
				if (isset($birthdates[1])) {
					$userwheres[1][] = '('.implode(' OR ',$birthdates[1]).')';
				} elseif (isset($birthdates[0])) {
					$userwheres[0][] = implode(' AND ',$birthdates[0]);
				}
				unset($birthdates);
			}
			// USER PARTS
			if (isset($userwheres)) {
				foreach ($userwheres as $inclusive => $wherepart) {
					$userids = memcached_boolean_hash(
						array('user','user_account'),'
						SELECT USERID
						FROM user
						JOIN user_account USING (USERID)
						WHERE STATUS="active" AND '.implode(' AND ',$wherepart),
						TEN_MINUTES
					);
					if ($userids === false) {
						return false;
					}
					addids($ids,$currentgroup,$inclusive,$userids);
				}
				unset($userwheres);
			}
			if (!empty($ids[$currentgroup][0])
			&&	!empty($ids[$currentgroup][1])
			) {
				$ids[$currentgroup][1] = array_diff_key($ids[$currentgroup][1],$ids[$currentgroup][0]);
				unset($ids[$currentgroup][0]);
			}
			if (!$focus) {
				break;
			}
			$currentgroup = $focus['GROUPID'];
		}
		if (isset($focus['FILTER'])) {
			$havefilter = true;
			continue;
		}
		$inclusive = $focus['INCLUSIVE'];
		unset($element);
		switch ($focus['TYPE']) {
			case 'province':
			case 'country':
				$citylist = memcached_simpler_array('city','
					SELECT CITYID
					FROM city
					WHERE '.$focus['TYPE'].'ID='.$focus['ID'],
					TEN_MINUTES
				);
				if ($citylist === false) {
					return false;
				}
				if ($citylist) {
					if (isset($cities[$inclusive])) {
						$cities[$inclusive] = array_merge($cities[$inclusive],$citylist);
					} else {
						$cities[$inclusive] = $citylist;
					}
				} elseif ($inclusive) {
					$ids[$currentgroup]['DEAD'] = true;
				}
				break;
			case 'city':
				if ($focus['OPTVAL']) {
					$cityranges[$inclusive][$focus['ID']] = $focus['OPTVAL'];
				} else {
					$cities[$inclusive][] = $focus['ID'];
				}
				break;
			case 'genre':
				$genres[$inclusive][] = $focus['ID'];
				break;
			case 'erotic':
				$erotics[$inclusive][] = $focus['OPTVAL'];
				break;
			case 'party':
				$parties[$inclusive][$focus['OPTVAL']][] = $focus['ID'];
				break;
			case 'artistarchive':
				$partyids = memcached_simpler_array(
					'lineup',
					$focus['OPTVAL']
				?	'SELECT DISTINCT lineup.PARTYID
					FROM lineup JOIN party USING (PARTYID)
					WHERE MOVEDID=0 AND CANCELLED=0 AND ARTISTID='.$focus['ID']
				:	'SELECT DISTINCT lineup.PARTYID
					FROM lineup JOIN party USING (PARTYID)
					WHERE MOVEDID=0 AND CANCELLED=0 AND STAMP<'.TODAYSTAMP.' AND ARTISTID='.$focus['ID']
				);
				if ($partyids) {
					foreach ($partyids as $partyid) {
						$parties[$inclusive][2][] = $partyid;
					}
				}
				break;
			case 'user':
				$users[$inclusive][$focus['ID']] = true;
				break;
			case 'flock':
				$flocks[$inclusive][] = $focus['ID'];
				break;
			case 'contest':
				$contests[$inclusive][] = $focus['ID'];
				break;
			case 'artist':
				$element = 'artist';
			case 'favlocation':
			case 'favorganization':
				if (!isset($element)) {
					$element = substr($focus['TYPE'],3);
				}
				$favs[$element][$inclusive][] = $focus['ID'];
				break;
			case 'location':
				$locations[$inclusive][$focus['ID']] = $focus['OPTVAL'];
				break;
			case 'organization':
				$organizations[$inclusive][$focus['ID']] = $focus['OPTVAL'];
				break;
			case 'freedate':
				$freedates[$inclusive][$focus['ID']] = $focus['OPTVAL'];
				break;
			case 'gender':
				$part = $focus['ID'] ? 'SEX="M"' : 'SEX="F"';
				if ($inclusive) {
					$userwheres[1][] = $part;
				} else {
					$userwheres[0][] = 'NOT '.$part;
				}
				break;
			case 'relation':
				$userwheres[1][] = 'RELATION="'.($focus['ID'] ? 'yes' : 'no').'"';
				break;
			case 'sexpref':
				if ($focus['ID'] & (1<<0)) {
					$preflist[] = '"hetero"';
				}
				if ($focus['ID'] & (1<<1)) {
					$preflist[] = '"homo"';
				}
				if ($focus['ID'] & (1<<2)) {
					$preflist[] = '"bi"';
				}
				if (isset($preflist)) {
					if ($inclusive) {
						$userwheres[1][] = 'SEXPREF IN ('.implode(',',$preflist).')';
					} else {
						$userwheres[0][] = 'SEXPREF NOT IN ('.implode(',',$preflist).')';
					}
					unset($preflist);
				}
				break;
			case 'age':
				global $__year,$__month,$__day;
				$part = 'BIRTH_YEAR AND BIRTH_YEAR>'.($__year-100);
				if ($focus['OPTVAL']) {
					// MAX AGE
					$minyear = $__year - $focus['OPTVAL'] - 1;
					$part .= '
					AND (		BIRTH_YEAR>'.$minyear.'
						OR	BIRTH_YEAR='.$minyear.'
						AND	(	BIRTH_MONTH>'.$__month.'
							OR	BIRTH_MONTH='.$__month.' AND BIRTH_DAY>'.$__day.'))';
				}
				if ($focus['ID']) {
					// MIN AGE
					$maxyear = $__year - $focus['ID'];
					$part .= '
					AND	(	BIRTH_YEAR<'.$maxyear.'
						OR 	BIRTH_YEAR='.$maxyear.'
						AND	(	BIRTH_MONTH<'.$__month.'
							OR	BIRTH_MONTH='.$__month.' AND BIRTH_DAY<='.$__day.'))';
				}
				if ($inclusive) {
					$userwheres[1][] = $part;
				} else {
					$userwheres[0][] = 'NOT ('.$part.')';
				}
				break;
			case 'birthday':
				if ($inclusive) {
					$birthdates[1][] = 'BIRTH_MONTH='.$focus['OPTVAL'].' AND BIRTH_DAY='.$focus['ID'];
				} else {
					$birthdates[0][] = 'NOT (BIRTH_MONTH='.$focus['OPTVAL'].' AND BIRTH_DAY='.$focus['ID'].')';
				}
				break;
			case 'right':
				switch ($focus['ID']) {
					case ANY_RIGHT:
						$userids = db_boolean_hash('rights','SELECT DISTINCT USERID FROM rights UNION SELECT DISTINCT USERID FROM rights_forum WHERE TYPE="admin"');
						break;
					case ANY_BUT_CAM_RIGHT:
						$userids = db_boolean_hash('rights','SELECT USERID FROM rights GROUP BY USERID HAVING COUNT(IF(`PORTION`!="photographer",1,NULL))>0 UNION SELECT DISTINCT USERID FROM rights_forum WHERE TYPE="admin"');
						break;
					default:
						$userids = array();
						break;
				}
				addids($ids,$currentgroup,$inclusive,$userids);
				break;
			case 'promo':
				$promos[$inclusive][$focus['ID']] = $focus['OPTVAL'];
				break;
			case 'employee':
				switch ($focus['OPTVAL']) {
				case 1:	$element = 'location'; break;
				case 2: $element = 'organization'; break;
				case 3: $element = 'relation'; break;
				}
				$userids = memcached_boolean_hash(
					array($element.'member','user_account'),'
					SELECT DISTINCT USERID
					FROM '.$element.'member
					JOIN user_account USING (USERID)
					WHERE STATUS="active"'
				);
				if ($userids === false) {
					return false;
				}
				increment_or_set($employees,$inclusive,$userids);
				break;
			case 'artistuser':
				$userids = memcached_boolean_hash(
					array('artistmember','user_account'),'
					SELECT DISTINCT USERID
					FROM artistmember
					JOIN user_account USING (USERID)
					WHERE STATUS="active"'
				);
				if ($userids === false) {
					return false;
				}
				addids($ids,$currentgroup,1,$userids);
				break;
			case 'buddies':
				$buddies[$inclusive][$focus['ID']] = $focus['OPTVAL'];
				break;
/*				$userids = memcached_boolean_hash(array('buddy','user_account'),'
					SELECT USERID_INI
					FROM buddy
					JOIN user_account ON USERID_INI=USERID
					WHERE USERID_ACC='.$focus['ID'].'
					  AND STATUS="active"
					UNION
					SELECT USERID_ACC
					FROM buddy
					JOIN user_account ON USERID_ACC=USERID
					WHERE USERID_INI='.$focus['ID'].'
					  AND STATUS="active"',
					TEN_MINUTES
				);
				if ($userids === false) {
					return false;
				}
				if ($focus['OPTVAL']
				&&	$userids
				) {
					// FIXME:
					$more = memcached_boolean_hash(array('buddy','user_account'),'
						SELECT DISTINCT USERID_INI
						FROM buddy
						JOIN user_account ON USERID=USERID_INI
						WHERE USERID_ACC IN ('.implodekeys(',',$userids).')
						  AND STATUS="active"
						UNION
						SELECT DISTINCT USERID_ACC
						FROM buddy
						JOIN user_account ON USERID=USERID_ACC
						WHERE USERID_INI IN ('.implodekeys(',',$userids).')
						  AND STATUS="active"'
					);
					if ($more === false) {
						return false;
					}
					if ($more) {
						$userids += $more;
					}
				}
				addids($ids,$currentgroup,$inclusive,$userids);
				break;*/

			case 'max':
				//$ids['LESSTHANMAX'] = true;
				$ids[$currentgroup]['LIMIT'] = $focus['ID'];
				break;

			case 'maxmoney':
				//$ids['LESSTHANMAX'] = true;
				$ids[$currentgroup]['MAXMONEY'] = $focus['ID'];
				break;

			case 'targetprice':
				$ids[$currentgroup]['TARGETPRICE'] = $focus['ID'];
				break;
		}
	}
	if (isset($havefilter)) {
		$currentgroup = $focus[0]['GROUPID'];
		for ($focus = reset($foci); ; $focus = next($foci)) {
			if (!$focus
			||	$focus['GROUPID'] !== $currentgroup
			) {
				if (isset($gidlist)
				||	isset($countrylist)
				) {
					if (isset($gidlist)) {
						$wherep = '';
						if (isset($gidlist[0])) {
							$wherep .= ' AND GID NOT IN ('.implode(',',$gidlist[0]).')';
						}
						if (isset($gidlist[1])) {
							$wherep .= ' AND GID IN ('.implode(',',$gidlist[1]).')';
						}
						$ids[$currentgroup][1] = memcached_boolean_hash(
							'genre_fan','
							SELECT SQL_NO_CACHE USERID
							FROM genre_fan
							WHERE USERID IN ('.implodekeys(',',$ids[$currentgroup][1]).')'.
							$wherep
						);
						if ($ids[$currentgroup][1] === false) {
							return false;
						}
					}
					if (isset($countrylist)) {
						$wherep = '';
						if (isset($countrylist[0])) {
							$idstr = implode(',',$countrylist[0]);
							$wherep .= ' AND (city.COUNTRYID NOT IN ('.$idstr.') AND user.COUNTRYID NOT IN ('.$idstr.'))';
						}
						if (isset($countrylist[1])) {
							$idstr = implode(',',$countrylist[1]);
							$wherep .= ' AND (city.COUNTRYID IN ('.$idstr.') OR user.COUNTRYID IN ('.$idstr.'))';
						}
						if (!empty($ids[$currentgroup][0])) {
							$wherep .= ' AND user.USERID NOT IN ('.implodekeys(',',$ids[$currentgroup][0]).')';
						}
						if (!empty($ids[$currentgroup][1])) {
							$wherep .= ' AND user.USERID IN ('.implodekeys(',',$ids[$currentgroup][1]).')';
						}
						$ids[$currentgroup][1] = memcached_boolean_hash(
							array('user','city','user_account'),'
							SELECT user.USERID
							FROM user
							JOIN city USING (CITYID)
							JOIN user_account ON user_account.USERID=user.USERID
							WHERE STATUS="active"'.
							$wherep
						);
						if ($ids[$currentgroup][1] === false) {
							return false;
						}
					}
					unset($gidlist);
					unset($countrylist);
				}
				if (!$focus) {
					break;
				}
				$currentgroup = $focus['GROUPID'];
			}
			if (!isset($focus['FILTER'])) {
				continue;
			}
			switch ($focus['TYPE']) {
			case 'genre':
				$gidlist[$focus['INCLUSIVE']][] = $focus['ID'];
				break;
			case 'country':
				$countrylist[$focus['INCLUSIVE']][] = $focus['ID'];
				break;
			default:
				_error($GLOBALS['__promofocustypes'][$focus['TYPE']].' niet ondersteund als filter!');
				return false;
			}
		}
	}
	return $ids;
}
function adduser($userid,$days,&$catsum,&$feas,&$resuserids,&$price) {
	$rc = true;
	if (isset($price['CURRPRICE'])) {
		$currprice = $price['CURRPRICE'];
		for ($day = 0; $day < $days; ++$day) {
			$currprice += ($price['BASE'] - $price['BASE'] * $day * $price['DAYDISCOUNT'] / 100.0) / 100.0;
		}
		$currprice += $price['SEEN'] / 100.0;

		if ($currprice > $price['MAXMONEY']) {
			return false;
		} elseif ($currprice == $price['MAXMONEY']) {
			$rc = false;
		}
		$price['CURRPRICE'] = $currprice;
	}
	for ($day = 0; $day < $days; ++$day) {
		$feas[$userid][$day] = 0;
		if (!isset($catsum[$day][0])) {
			$catsum[$day][0] = 1;
		} else {
			++$catsum[$day][0];
		}
	}
	$resuserids[$userid] = true;
	return $rc;
}
function promo_calculate_reach() {
	if (!require_admin('promo')
	||	!require_idnumber($_REQUEST,'sID')
	||	!require_inactive_promo($promoid = $_REQUEST['sID'])
	||	!require_getlock('promocalculate:'.$promoid,5)
	||	!require_last_lock(LOCK_PROMO,$promoid)
	||	!require_unreserved_promo($promoid)
	) {
		return;
	}
	set_memory_limit(PROMO_MEMORY_LIMIT);
	set_time_limit(PROMO_MAX_EXEC_TIME);

#	$GLOBALS['WAIT_FOR_SLAVE'] = 120;

	// check whether just added promo letters exceed max 2 promo messages at any given time
	$promo = db_single_assoc('promo','
		SELECT PROMOID,STARTSTAMP,STOPSTAMP,ACTIVE,RELATIONID,TARGETPRICE,VERSION,promoprice.*
		FROM promo
		JOIN promoprice USING (PRICEID)
		WHERE PROMOID='.$promoid
	);
	if ($promo === false) {
		return;
	}
	if (!$promo) {
		_error(__('promo:error:nonexistent_LINE',array('ID'=>$promoid)));
		return;
	}
	if (!require_inactive_promo($promoid,$promo['ACTIVE'])) {
		return;
	}
	$v = $promo['VERSION'];
	$table = $v >= 2 ? 'promo_letter_v2' : 'promo_letter';

	if ($v == 1) {
		$overlaps = db_rowuse_hash('promo','
			SELECT PROMOID,STARTSTAMP,STOPSTAMP,SENT_STAMP
			FROM promo
			WHERE SENT_STAMP!=0
			  AND STARTSTAMP<'.$promo['STOPSTAMP'].'
			  AND STOPSTAMP>'.$promo['STARTSTAMP'].'
			  AND PROMOID!='.$promo['PROMOID']
		);
		if ($overlaps === false) {
			return;
		}
		if ($overlaps) {
			number_asort($overlaps,'SENT_STAMP');
			$overlapidstr = implodekeys(',',$overlaps);
			$letters = db_boolean_hash('promo','
				SELECT PROMOID,USERID
				FROM promo_letter
				WHERE PROMOID IN ('.$overlapidstr.')'
			);
			if ($letters === false) {
				return;
			}
		}
	}
	$ids = promo_determine_userids($promoid,$groupcnt,$foci);
	if ($ids === false) {
		return;
	}
	require_once '_donators.inc';

	$nopromo = want_hide_ads(ADS_SHOW_PROMO,$promo['STOPSTAMP']);
	if ($nopromo === false) {
		return;
	}
	$alluserids = array();
	if (!$ids) {
		$currentgroup = 0;
	} else foreach ($ids as $currentgroup => &$group) {
		if (isset($group['DEAD'])) {
			if (!$currentgroup) {
				_error('Globale groep heeft een criterium dat alle leden uitsluit!');
			} else {
				_error('Groep '.$currentgroup.' heeft een criterium dat alle leden binnen die groep uitsluit!');
			}
			continue;
		}
		if (empty($group[0])
		&&	empty($group[1])
		) {
			if ($currentgroup
			||	(	!isset($group['MAXMONEY'])
				&&	!isset($group['TARGETPRICE'])
				&&	!isset($group['LIMIT'])
				)
			) {
				_error('Compleet lege groep ('.($currentgroup ? 'groep '.$currentgroup : 'globale groep').'), waarschuw technische dienst!');
			}
			continue;
		}
		// make sure we honour global info
		if ($currentgroup) {
			$unfiltered = empty($group[1]) ? 0 : count($group[1]);
			if (empty($group[1])) {
				// invert group
				if (!isset($actives)) {
					$actives = memcached_boolean_hash('user_account','SELECT USERID FROM user_account WHERE STATUS="active"');
					if ($actives === false) {
						return;
					}
				}
				$group[1] = array_diff_key($actives,$group[0]);
				unset($group[0]);
			}
			if (!empty($ids[0][1])) {
				$group[1] = array_intersect_key($group[1],$ids[0][1]);
			}
			if (!empty($ids[0][0])) {
				$group[1] = array_diff_key($group[1],$ids[0][0]);
			}
			if (isset($group['LIMIT'])) {
				if (sizeof($group[1]) > $group['LIMIT']) {
					// FIXME:
					$group[1] = db_boolean_hash('user_data','
						SELECT USERID
						FROM user_data
						WHERE USERID IN ('.implodekeys(',',$group[1]).')
						ORDER BY LAST_USED DESC
						LIMIT '.$group['LIMIT']
					);
					if ($group[1] === false) {
						return false;
					}
				}
			}
			$reachvals[] = '('.$promoid.','.$currentgroup.','.$unfiltered.','.count($group[1]).')';
			$alluserids += $group[1];
		} elseif ($groupcnt <= 1) {
			$unfiltered = count($group[1]);
			if (empty($group[1])) {
				// invert group
				if (!isset($actives)) {
					$actives = memcached_boolean_hash('user_account','SELECT USERID FROM user_account WHERE STATUS="active"');
					if ($actives === false) {
						return;
					}
				}
				$group[1] = array_diff_key($actives,$group[0]);
				unset($group[0]);
			}
			if (!empty($nopromo)) {
				$group[1] = array_diff_key($group[1],$nopromo);
			}
			$reachvals[] = '('.$promoid.',0,'.$unfiltered.','.count($group[1]).')';
			$alluserids += $group[1];
		}
	}
	unset($group);
	$maxreach = count($alluserids);
	if (!db_delete('promogroupreach','
		DELETE FROM promogroupreach
		WHERE PROMOID='.$promoid)
	) {
		return;
	}
	if (isset($reachvals)) {
		if (!db_insert('promogroupreach','
			INSERT INTO promogroupreach (PROMOID,GROUPID,REACH,REACH_FILTERED)
			VALUES '.implode(',',$reachvals))
		) {
			return;
		}
		#unset($reachvals);
	}
	$userids = array();
	if (isset($ids[0]['LIMIT'])
	&&	$maxreach > $ids[0]['LIMIT']
	) {
		$globallimit = $ids[0]['LIMIT'];
	}
	if ($ids)
	foreach ($ids as $currentgroup => &$group) {
		if (empty($group[1])) {
			continue;
		}
		if (isset($globallimit)) {
			// FIXME:
			$group[1] = db_boolean_hash('user_data','
				SELECT USERID
				FROM user_data
				WHERE USERID IN ('.implodekeys(',',$group[1]).')
				ORDER BY LAST_USED DESC
				LIMIT '.floor($globallimit * count($group[1]) / $maxreach)
			);
			if ($group[1] === false) {
				return;
			}
		}
		if ($currentgroup) {
			$userids += $group[1];
		} elseif ($groupcnt <= 1) {
			$userids += $group[1];
		}
	}
	unset($group);
	if (empty($userids)) {
		$userids = isset($ids[0][1]) ? $ids[0][1] : null;
	}
	if ($v == 1) {
		if ($overlaps
		&&	$userids
		) {
			foreach ($overlaps as $overlapid => $overlap) {
				if (!isset($letters[$overlapid])) {
					error_log('no letters for promo '.$overlapid,0);
					continue;
				}
				foreach ($userids as $userid => $bool) {
					if (isset($letters[$overlapid][$userid])) {
						$overlapletters[$overlapid][$userid] = $bool;
					}

				}
			}
		}
	}
	$days = round(($promo['STOPSTAMP'] - $promo['STARTSTAMP']) / 24 / 3600);
	$price = &$promo;
	if ($v == 1
	&&	isset($ids[0]['MAXMONEY'])
	) {
		$price['MAXMONEY'] = $ids[0]['MAXMONEY'];
		$price['CURRPRICE'] = 0;
	}
	#unset($ids);
	if (!$userids) {
		_error('Geen selectie mogelijk met opgegeven criteria!');
		return;
	}
	foreach ($userids as $userid => $bool) {
		if ($v >= 2 || !$overlaps) {
			if (!adduser($userid,$days,$catsum,$feas,$resuserids,$price)) {
				break;
			}
			continue;
		}
		$overlappers = array();
		foreach ($overlaps as $overlapid => $overlap) {
			if (isset($overlapletters[$overlapid][$userid])) {
				$overlappers[$overlapid] = $overlap;
			}
		}
		// startstamp is now the startstamp of overlapping promo that starts first
		if (!isset($overlappers)) {
			if (!adduser($userid,$days,$catsum,$feas,$resuserids,$price)) {
				break;
			}
			continue;
		}
		$day = 0;
		for ($stamp = $promo['STARTSTAMP']; $stamp < $promo['STOPSTAMP']; $stamp = strtotime('+1 day',$stamp)) {
			$catcnt = 0;
			foreach ($overlappers as $overlap) {
				// promocnt was ++ if first condition below was true
				if ($overlap['STARTSTAMP'] <= $stamp
				&&	$overlap['STOPSTAMP'] > $stamp
				) {
					++$catcnt;
				}
			}
			// possible promo's at any given time: 2 + (2-currentmaxpromo)
			// current category is amount of ads that might be active
			$feas[$userid][$day] = $catcnt;
			++$day;
		}
		foreach ($feas[$userid] as $day => $cat) {
			if (true) { //$currentmaxpromo - $cat > 0
				if (isset($price['CURRPRICE'])) {
					$currprice = $price['CURRPRICE'];
					$currprice += $cat
					?	($price['BASE'] - $price['BASE'] * $day * $price['DAYDISCOUNT'] / 100) / ($price['CLASSDIVIDER'] * $cat) / 100
					:	($price['BASE'] - $price['BASE'] * $day * $price['DAYDISCOUNT'] / 100) / 100;

					if ($currprice + $price['SEEN'] / 100.0 > $price['MAXMONEY']) {
						$stop = true;
						break;
					}
					$price['CURRPRICE'] = $currprice;
				}
				if (!isset($adduser)) {
					$adduser = true;
				}
				if (!isset($catsum[$day][$cat]))
					  $catsum[$day][$cat] = 1;
				else	++$catsum[$day][$cat];
			} else {
				$cat = -1;
			}
		}
		if (isset($stop)) {
			break;
		}
		if (isset($adduser)) {
			if (isset($price['CURRPRICE'])) {
				$price['CURRPRICE'] += $price['SEEN'] / 100.0;
			}
			$resuserids[$userid] = true;
			unset($adduser);
		}
	}
	if (!empty($catsum)) {
		foreach ($catsum as $day => $cats) {
			foreach ($cats as $clss => $reach) {
				$classes[] = '('.$promoid.','.$day.','.$clss.','.$reach.')';
			}
		}
	}
	if ($promo['RELATIONID']) {
		$reluserids = db_simpler_array(
			'relationmember','
			SELECT USERID
			FROM relationmember
			WHERE RELATIONID='.$promo['RELATIONID']
		);
		if ($reluserids === false) {
			return;
		}
		if ($reluserids) {
			foreach ($reluserids as $reluserid) {
				$resuserids[$reluserid] = true;
			}
		}
	}
	if (!db_delete('promo_class_stats','
		DELETE FROM promo_class_stats
		WHERE PROMOID='.$promoid)
	) {
		return;
	}
	if (!empty($classes)) {
		if (!db_insert('promo_class_stats','
			INSERT IGNORE INTO promo_class_stats (PROMOID,DAY,CLASS,CNT)
			VALUES '.implode(',',$classes))
		) {
			return;
		}
	}
	if (!remove_letters($promoid,'promo_letter_concept')) {
		return;
	}
	foreach ($resuserids as $userid => $bool) {
		$vals[] = '('.$promoid.','.$userid.')';
	}
	$size = count($resuserids);

	create_concepts($promoid,$vals);

	if (!db_update('promo','
		UPDATE promo SET
			ESTIM		='.estimate_seen($promoid,$promo['STARTSTAMP'],$promo['STOPSTAMP'],implodekeys(',',$resuserids)).',
			REACH		='.$size.',
			MAXREACH	='.$maxreach.',
			REQUEST		=0,
			COSTSENT	=0,
			RSTAMP		='.CURRENTSTAMP.',
			BEFOREGOING	=NULL
		WHERE PROMOID='.$promoid)
	// remove goingprepromo
	||	!db_delete('goingprepromo','
		DELETE FROM goingprepromo
		WHERE PROMOID='.$promoid)
	) {
		return;
	}
	$relationid = db_single('promo','SELECT RELATIONID FROM promo JOIN relation USING (RELATIONID) WHERE relation.REQUEST=1 AND PROMOID='.$promoid);
	if ($relationid) {
		if (!db_insert('relation'	,'
			INSERT INTO relation_log
			SELECT * FROM relation
			WHERE RELATIONID='.$relationid)
		||	!db_update('relation','
			UPDATE relation SET
				MSTAMP	='.CURRENTSTAMP.',
				MUSERID	='.CURRENTUSERID.',
				REQUEST	=0
			WHERE RELATIONID='.$relationid)
		) {
		}
	}
	_notice('Bereik vastgesteld.');
}
function promo_remove_focus() {
	require_once '_promoreach.inc';
	if (!require_admin('promo')
	||	!($promoid = require_idnumber($_REQUEST,'sID'))
	||	!require_inactive_promo($promoid)
	||	!require_last_lock(LOCK_PROMO,$promoid)
	||	!($focusid = require_idnumber($_REQUEST,'subID'))
	||	!db_insert('promo_focus_log','
		INSERT INTO promo_focus_log
		SELECT *,'.CURRENTSTAMP.' FROM promo_focus
		WHERE FOCUSID='.$focusid)
	|| 	!db_delete('promo_focus','DELETE FROM promo_focus WHERE FOCUSID='.$focusid)
	||	!reset_reach($promoid)
	) {
		return;
	}
	register_notice('focus:notice:removed_LINE');
}
function promo_remove_focus_group() {
	require_once '_promoreach.inc';
	if (!require_admin('promo')
	||	!($promoid = require_idnumber($_REQUEST,'sID'))
	||	!require_inactive_promo($promoid)
	||	!require_last_lock(LOCK_PROMO,$promoid)
	||	false === require_number($_REQUEST,'subID')
	||	!db_insert('promo_focus_log','
		INSERT INTO promo_focus_log
		SELECT *,'.CURRENTSTAMP.' FROM promo_focus
		WHERE PROMOID='.$promoid.'
		  AND GROUPID='.($groupid = $_REQUEST['subID']))
	|| 	!db_delete('promo_focus','DELETE FROM promo_focus WHERE PROMOID='.$promoid.' AND GROUPID='.$groupid)
	||	!reset_reach($promoid)
	) {
		return;
	}
	register_notice('focus:notice:group_removed_LINE');
}
function promo_add_focus() {
	require_once '_promoreach.inc';
	if (!$_REQUEST['SUBACTION']
	||	!($promoid = require_idnumber($_REQUEST,'sID'))
	||	!require_admin('promo')
	||	false === require_number($_POST,'GROUPID')
	||	!require_inactive_promo($promoid)
	||	!require_last_lock(LOCK_PROMO,$promoid)
	) {
		return false;
	}
	switch ($_REQUEST['SUBACTION']) {
	// COUNTRY
	case 'country':
		if (!require_idnumber($_POST,'COUNTRYID')
		||	false === require_number($_POST,'INCLUSIVE')
		||	!db_insert('promo_focus','
			INSERT INTO promo_focus SET
				TYPE		="country",
				PROMOID		='.$promoid.',
				ID		='.$_POST['COUNTRYID'].',
				INCLUSIVE	='.$_POST['INCLUSIVE'].',
				GROUPID		='.$_POST['GROUPID']
		)) {
			return false;
		}
		break;
	// PROVINCE
	case 'province':
		if (!require_number_array($_POST,'PROVINCEID')
		||	false === require_number($_POST,'INCLUSIVE')
		) {
			return false;
		}
		foreach ($_POST['PROVINCEID'] as $provid) {
			if (!db_insert('promo_focus','
				INSERT INTO promo_focus SET
					TYPE		="province",
					PROMOID		='.$promoid.',
					ID		='.$provid.',
					INCLUSIVE	='.$_POST['INCLUSIVE'].',
					GROUPID		='.$_POST['GROUPID']
			)) {
				return false;
			}
		}
		break;
	// CITY
	case 'city':
		if (!require_idnumber_trim($_POST,'CITYID')
		||	false === require_number($_POST,'INCLUSIVE')
		||		!empty($_POST['OPTVAL'])
			&&	false === require_number($_POST,'OPTVAL')
		||	!db_insert('promo_focus','
			INSERT INTO promo_focus SET
				TYPE		="city",
				PROMOID		='.$promoid.',
				ID		='.$_POST['CITYID'].',
				OPTVAL		="'.(0+$_POST['OPTVAL']).'",
				INCLUSIVE	='.$_POST['INCLUSIVE'].',
				GROUPID		='.$_POST['GROUPID']
		)) {
			return false;
		}
		break;
	// EMPLOYEE
	case 'employee':
		if (!require_idnumber($_POST,'OPTVAL')) {
			return false;
		}
		if (!db_insert('promo_focus','
			INSERT INTO promo_focus SET
				TYPE		="employee",
				PROMOID		='.$promoid.',
				OPTVAL		='.$_POST['OPTVAL'].',
				INCLUSIVE	=1,
				GROUPID		='.$_POST['GROUPID']
		)) {
			return false;
		}
		break;
	// ARTISTUSER
	case 'artistuser':
		if (!db_insert('promo_focus','
			INSERT INTO promo_focus SET
				TYPE		="artistuser",
				PROMOID		='.$promoid.',
				INCLUSIVE	=1,
				GROUPID		='.$_POST['GROUPID']
		)) {
			return false;
		}
		break;
	// GENRE
	case 'genre':
		if (!require_number_array($_POST,'GID')
		||	false === require_number($_POST,'INCLUSIVE')
		) {
			return false;
		}
		foreach ($_POST['GID'] as $gid) {
			$setlist[] = '("genre",'.$promoid.','.$gid.','.$_POST['INCLUSIVE'].','.$_POST['GROUPID'].')';
		}
		if (!isset($setlist)) {
			return false;
		}
		if (!db_insert('promo_focus','
			INSERT INTO promo_focus (TYPE,PROMOID,ID,INCLUSIVE,GROUPID) VALUES '.implode(',',$setlist))
		) {
			return false;
		}
		break;
	// FREEDATE
	case 'freedate':
		if (!require_date($_POST)
		||	!require_anything($_POST,'FREEDATETYPE')
		||	!($stamp = _date_getstamp($_POST))
		) {
			return false;
		}
		switch ($_POST['FREEDATETYPE']) {
		default:
			_error('Incorrecte type ('.escape_specials($_POST['FREEDATETYPE']).') voor vrije dag!');
			return false;
		case 'nothing':			$inclusive = 1; $optval = 0;	break;
		case 'nothing_or_option':	$inclusive = 1;	$optval = 1;	break;
		case 'require_option':		$inclusive = 0;	$optval = 2;	break;
		case 'require_certain':		$inclusive = 0; $optval = 1;	break;
		case 'require_any':		$inclusive = 0; $optval = 0;	break;
		}
		if (!db_insert('promo_focus','
			INSERT INTO promo_focus SET
				TYPE		="freedate",
				ID		='.$stamp.',
				PROMOID		='.$promoid.',
				INCLUSIVE	='.$inclusive.',
				OPTVAL		='.$optval.',
				GROUPID		='.$_POST['GROUPID'])
		) {
			return false;
		}
		break;
	// MAX
	case 'max':
		if (false === require_number($_POST,'MAX')
		||	!db_insert('promo_focus','
			INSERT INTO promo_focus SET
				TYPE		="max",
				ID		='.$_POST['MAX'].',
				PROMOID		='.$promoid.',
				GROUPID		='.$_POST['GROUPID'])
		) {
			return false;
		}
		break;
	// MAXMONEY
	case 'maxmoney':
		if (false === require_number($_POST,'MAXMONEY')
		||	!db_insert('promo_focus','
			INSERT INTO promo_focus SET
				TYPE		="maxmoney",
				ID		='.$_POST['MAXMONEY'].',
				PROMOID		='.$promoid.',
				GROUPID		=0')
		) {
			return false;
		}
		break;
	// TARGETPRICE
	case 'targetprice':
		if (false === require_number($_POST,'TARGETPRICE')
		||	!db_insert('promo_focus','
			INSERT INTO promo_focus SET
				TYPE		="targetprice",
				ID		='.$_POST['TARGETPRICE'].',
				OPTVAL		='.(isset($_POST['ABSOLUTE']) ? 1 : 0).',
				PROMOID		='.$promoid.',
				GROUPID		=0')
		) {
			return false;
		}
		break;
	// PROMO
	case 'promo':
		if (false === require_number($_POST,'USEPROMOID')
		||	false === require_number($_POST,'INCLUSIVE')
		||	false === require_number($_POST,'SEEN')
		||	!db_insert('promo_focus','
			INSERT INTO promo_focus SET
				TYPE		="promo",
				ID		='.$_POST['USEPROMOID'].',
				PROMOID		='.$promoid.',
				GROUPID		='.$_POST['GROUPID'].',
				INCLUSIVE	='.$_POST['INCLUSIVE'].',
				OPTVAL		='.$_POST['SEEN'])
		) {
			return false;
		}
		break;
	// FOCUSCOPY
	case 'focuscopy':
		if (!($copypromoid = require_idnumber($_POST,'COPYPROMOID'))
		) {
			return false;
		}
		$maxgroupid = db_single('promo_focus','SELECT MAX(GROUPID) FROM promo_focus WHERE PROMOID='.$promoid);
		if ($maxgroupid === false) {
			return false;
		}
		++$maxgroupid;
		if (!db_insert('promo_focus','
			INSERT INTO promo_focus (PROMOID,TYPE,ID,OPTVAL,INCLUSIVE,FOCUSID,GROUPID)
			SELECT '.$promoid.',TYPE,ID,OPTVAL,INCLUSIVE,0,IF(GROUPID,GROUPID+'.$maxgroupid.',0)
			FROM promo_focus
			WHERE PROMOID='.$copypromoid)
		) {
			return;
		}
		break;
	// FLOCK
	case 'flock':
		if (!require_idnumber($_POST,'FLOCKID')
		||	!db_insert('promo_focus','
			INSERT INTO promo_focus SET
				TYPE		="flock",
				ID		='.$_POST['FLOCKID'].',
				PROMOID		='.$promoid.',
				GROUPID		='.$_POST['GROUPID'].',
				INCLUSIVE	=1')
		) {
			return false;
		}
		break;
	// CONTEST
	case 'contest':
		if (have_idnumber($_POST,'CONTESTIDD')) {
			$contestid = $_POST['CONTESTIDD'];
		} elseif (!require_idnumber($_POST,'CONTESTID')) {
			return false;
		} else {
			$contestid = $_POST['CONTESTID'];
		}
		if (false === require_number($_POST,'INCLUSIVE')
		||	!db_insert('promo_focus','
			INSERT INTO promo_focus SET
				TYPE		="contest",
				ID		='.$contestid.',
				PROMOID		='.$promoid.',
				GROUPID		='.$_POST['GROUPID'].',
				INCLUSIVE	='.$_POST['INCLUSIVE'])
		) {
			return false;
		}
		break;
	// USER
	case 'user':
		if (!empty($_POST['NICK'])) {
			$_POST['NICK'] = trim($_POST['NICK']);
			$users = db_rowuse_array(
				'user_account','
				SELECT USERID,NICK
				FROM user_account
				WHERE NICK LIKE "%'.str_replace(' ','%',addslashes($_POST['NICK'])).'%"
				'.(have_idnumber($_POST,'NICK') ? ' OR USERID='.$_POST['NICK'] : null).'
				ORDER BY NICK ASC'
			);
			if (!$users) {
				_error('Geen leden gevonden met de nick &quot;'.escape_specials($_POST['NICK']).'&quot;!');
				return true;
			} elseif (!isset($users[1])) {
				$_POST['USERID'] = $users[0]['USERID'];
			} else {
				warning('Meerdere leden mogelijk, kies er een.');
				$GLOBALS['__users'] = $users;
				return true;
			}
		}
		if (!require_idnumber_trim($_POST,'USERID')
		||	false === require_number($_POST,'INCLUSIVE')
		||	!db_insert('promo_focus','
			INSERT INTO promo_focus SET
				TYPE		="user",
				ID		='.$_POST['USERID'].',
				PROMOID		='.$promoid.',
				GROUPID		='.$_POST['GROUPID'].',
				INCLUSIVE	='.$_POST['INCLUSIVE'])
		) {
			return false;
		}
		break;
	// BUDDIES
	case 'buddies':
		if (!require_idnumber_trim($_POST,'USERID')
		||	!db_insert('promo_focus','
			INSERT INTO promo_focus SET
				TYPE		="buddies",
				ID		='.$_POST['USERID'].',
				OPTVAL		='.(isset($_POST['LEVELTWO']) ? '1' : '0').',
				PROMOID		='.$promoid.',
				GROUPID		='.$_POST['GROUPID'].',
				INCLUSIVE	=1')
		) {
			return false;
		}
		break;
	// EROTIC
	case 'erotic':
		if (!db_insert('promo_focus','
			INSERT INTO promo_focus SET
				TYPE		="erotic",
				OPTVAL		='.(isset($_POST['OPTVAL']) ? '1' : '0').',
				PROMOID		='.$promoid.',
				GROUPID		='.$_POST['GROUPID'].',
				INCLUSIVE	='.(empty($_POST['INCLUSIVE']) ? '0' : '1'))
		) {
			return false;
		}
		break;
	// PARTY
	case 'party':
		if (!empty($_POST['NAME'])) {
			$parts = isset($_POST['EXACT'])
			?	'NAME="'.addslashes(trim($_POST['NAME'])).'"'
			:	make_search_regexp('NAME',$_POST['NAME']);

			$parties = db_rowuse_array('party','
				SELECT PARTYID,NAME,STAMP,SUBTITLE
				FROM party
				WHERE ACCEPTED=1
				  AND MOVEDID=0
				  AND CANCELLED=0
				  AND '.(is_array($parts) ? implode(' AND ',$parts) : $parts).'
				  AND STAMP<'.CURRENTSTAMP
			);
			if (!$parties) {
				_error('Geen feesten die voldoen aan de zoekterm &quot;'.escape_utf8($_POST['NAME']).'&quot;!');
				return true;
			} elseif (!isset($parties[1])) {
				$_POST['SELECTED_PARTYID'] = 0;
				$_POST['PARTYID'] = $parties[0]['PARTYID'];
			} else {
				_notice('Meerdere feesten mogelijk, kies er een!');
				$GLOBALS['__parties'] = $parties;
				return true;
			}
		}
		if (isset($_POST['PARTYIDS'])
		&&	require_number_array($_POST,'PARTYIDS')
		&&	require_number_array($_POST,'OPTVALS')
		) {
			$cnt = 0;
			foreach ($_POST['PARTYIDS'] as $partyid) {
				if (!db_insert('promo_focus','
					INSERT INTO promo_focus SET
						TYPE		="party",
						ID		='.$partyid.',
						PROMOID		='.$promoid.',
						INCLUSIVE	='.(empty($_POST['INCLUSIVE']) ? '0' : '1').',
						GROUPID		='.$_POST['GROUPID'].',
						OPTVAL		='.(isset($_POST['OPTVALS'][$cnt]) ? $_POST['OPTVALS'][$cnt] : 0))
				) {
					return false;
				}
				++$cnt;
			}
			_notice($cnt.' feesten toegevoegd.');
		} elseif (
			!require_number_trim($_POST,'SELECTED_PARTYID')
		||	!require_number_trim($_POST,'PARTYID')
		||	false === require_number($_POST,'OPTVAL')
		||	(	!$_POST['SELECTED_PARTYID']
			&&	!$_POST['PARTYID']
			)
		||	!db_insert('promo_focus','
			INSERT INTO promo_focus SET
				TYPE		="party",
				ID		='.($_POST['SELECTED_PARTYID'] ? $_POST['SELECTED_PARTYID'] : $_POST['PARTYID']).',
				PROMOID		='.$promoid.',
				INCLUSIVE	='.(empty($_POST['INCLUSIVE']) ? '0' : '1').',
				GROUPID		='.$_POST['GROUPID'].',
				OPTVAL		='.$_POST['OPTVAL'])
		) {
			return false;
		}
		break;
	# PARTYSELECT
	case 'partyselect':
		if (false === require_number($_POST,'OPTVAL')) {
			return false;
		}
		$countryid = have_idnumber($_POST,'COUNTRYID') ? $_POST['COUNTRYID'] : 0;
		$genres = null;
		if (have_number_array($_POST,'GID')) {
			$genres = $_POST['GID'];
		}
#		print_rr($_POST);
		if (!$countryid || !$genres) {
			_error('Niets gevonden!');
			return;
		}
		$joinstr = '';
		$wheres = [];
		$tables = ['party'];
		if ($countryid) {
			$tables[] = 'location';
			$tables[] = 'boarding';
			$tables[] = 'city';
			$joinstr .= '
				JOIN location USING (LOCATIONID)
				LEFT JOIN boarding USING (BOARDINGID)
				JOIN city ON city.CITYID=COALESCE(boarding.CITYID,location.CITYID,party.CITYID)';
			$wheres[] = 'city.COUNTRYID='.$countryid;
		}
		if ($genres) {
			$tables[] = 'party_genre';
			$joinstr .= '
				JOIN party_genre USING (PARTYID)';
			$wheres[] = 'party_genre.GID IN ('.implode(',',$genres).')';
		}
		if (isset($_POST['WHEN'])) {
			if (isset($_POST['WHEN']['future'])
			&&	isset($_POST['WHEN']['past'])
			) {
				// just do all
			} elseif (isset($_POST['WHEN']['future'])) {
				$wheres[] = 'party.STAMP>'.CURRENTSTAMP;
			} else { // isset($_POST['WHEN']['past'])
				$wheres[] = 'party.STAMP<'.CURRENTSTAMP;
			}
			if (have_number($_POST,'YEAR')) {
				$wheres[] = 'party.STAMP>UNIX_TIMESTAMP("'.$_POST['YEAR'].'-01-01")';
			}
		}
		$partyids = db_simpler_array($tables,'SELECT DISTINCT PARTYID FROM party '.$joinstr.' WHERE party.ACCEPTED=1 AND '.implode(' AND ',$wheres));
#		print_rr($partyids,$q);
#		exit;
		if (!$partyids) {
			_error('Niets gevonden!');
			return;
		}
		$cnt = 0;
		foreach ($partyids as $partyid) {
			if (!db_insert('promo_focus','
				INSERT INTO promo_focus SET
					TYPE		="party",
					ID		='.$partyid.',
					PROMOID		='.$promoid.',
					INCLUSIVE	='.(empty($_POST['INCLUSIVE']) ? '0' : '1').',
					GROUPID		='.$_POST['GROUPID'].',
					OPTVAL		='.$_POST['OPTVAL'])
			) {
				return false;
			}
			++$cnt;
		}
		_notice($cnt.' feesten toegevoegd.');
		break;

	// FAVS OF USER
	case 'favofuser':
		if (!($userid = require_idnumber($_POST,'USERID'))
		||	!require_hash($_POST, 'WHAT', HASH_NUMBER, HASH_STRING, $size, ['artist', 'location', 'organization'])
		) {
			return false;
		}
		$favs = db_multirow_hash('favourite','
			SELECT ELEMENT,ID
			FROM favourite
			WHERE ELEMENT IN ('.stringsimplode(',',$_POST['WHAT']).')
			  AND USERID='.$userid
		);
		if ($favs === false) return false;
		if (!$favs) {
			_error('Geen favorieten gevonden!');
			return false;
		}
		$groupid = db_single('promo_focus','SELECT MAX(GROUPID) FROM promo_focus WHERE PROMOID='.$promoid);
		if ($groupid === false) {
			return false;
		}
		foreach ($favs as $element => $ids) {
			++$groupid;
			switch ($element) {
			case 'artist':		$type = 'artist'; break;
			case 'location':	$type = 'favlocation'; break;
			case 'organization':	$type = 'favorganization'; break;
			}
			foreach ($ids as $id) {
				$setlist[] = '("'.$type.'",'.$id.','.$promoid.',1,'.$groupid.')';
			}
		}
		if (!db_insert('promo_focus','
			INSERT INTO promo_focus (TYPE,ID,PROMOID,INCLUSIVE,GROUPID)
			VALUES '.implode(',',$setlist))
		) {
			return false;
		}
		break;
	// FAVOURITE
	case 'favlocation':
	case 'favorganization':
		if (!($id = have_idnumber_trim($_POST,'IDNUM') ?: require_idnumber($_POST,'ID'))
		||	!db_insert('promo_focus','
			INSERT INTO promo_focus SET
				TYPE		="'.$_REQUEST['SUBACTION'].'",
				ID		='.$id.',
				PROMOID		='.$promoid.',
				INCLUSIVE	=1,
				GROUPID		='.$_POST['GROUPID'])
		) {
			return false;
		}
		break;
	// ARTIST
	case 'artistarchive':
	case 'artist':
		if (!empty($_POST['NAME'])) {
			$startstamp = db_single('promo','SELECT STARTSTAMP FROM promo WHERE PROMOID='.$promoid) ?: CURRENTSTAMP;
			require_once '_artist.inc';
			if (!($artists = get_artist_choices($startstamp, $_POST['NAME'], $size))) {
				if (is_number($_POST['NAME'])) {
					$exists = db_single('artist','SELECT 1 FROM artist WHERE ARTISTID='.$_POST['NAME']);
				}
				if (empty($exists)) {
					_error('Geen artiesten gevonden met de naam &quot;'.escape_utf8($_POST['NAME']).'&quot;!');
					return true;
				}
				$_POST['ARTISTID'] = $_POST['NAME'];
			} elseif ($size == 1) {
				[$artistid,$artist] = keyval($artists);
				$_POST['ARTISTID'] = $artist['ARTISTID'];
			} else {
				warning('Meerdere artiesten mogelijk, kies er een.');
				$GLOBALS['__artists'] = $artists;
				$GLOBALS['__ss'] = $startstamp;
				return true;
			}
		}
		if (!require_idnumber_trim($_POST,'ARTISTID')
		||	!db_insert('promo_focus','
			INSERT INTO promo_focus SET
				TYPE		="'.$_REQUEST['SUBACTION'].'",
				PROMOID		='.$promoid.',
				ID		='.$_POST['ARTISTID'].',
				INCLUSIVE	=1,
				OPTVAL		='.(isset($_POST['FUTURE']) ? '1' : '0').',
				GROUPID		='.$_POST['GROUPID'])
		) {
			return false;
		}
		break;
	// LOCATION
	// ORGANIZATION
	case 'location':
	case 'organization':
		$upper = strtoupper($_REQUEST['SUBACTION']);
		if (!require_idnumber_trim($_POST,$upper.'ID')
		||	false === require_number($_POST,'INCLUSIVE')
		||	!require_date($_POST,'SINCE')
		||	!db_insert('promo_focus','
			INSERT INTO promo_focus SET
				TYPE		="'.$_REQUEST['SUBACTION'].'",
				ID		='.$_POST[$upper.'ID'].',
				OPTVAL		='.(isset($_POST['SINCE']) ? _date_getstamp($_POST,'SINCE') : '0').',
				PROMOID		='.$promoid.',
				INCLUSIVE	='.$_POST['INCLUSIVE'].',
				GROUPID		='.$_POST['GROUPID'])
		) {
			return false;
		}
		break;
	// GENDER
	case 'gender':
		if (!require_something($_POST,'GENDER')
		||	!db_insert('promo_focus','
			INSERT INTO promo_focus SET
				TYPE		="gender",
				ID		='.($_POST['GENDER'] == 'M' ? '1' : '0').',
				PROMOID		='.$promoid.',
				INCLUSIVE	=1,
				GROUPID		='.$_POST['GROUPID']
		)) {
			return false;
		}
		break;
	// RELATION
	case 'relation':
		if (!require_anything($_POST,'RELATION')
		||	!db_insert('promo_focus','
			INSERT INTO promo_focus SET
				TYPE		="relation",
				ID		='.($_POST['RELATION'] ? '1' : '0').',
				PROMOID		='.$promoid.',
				INCLUSIVE	=1,
				GROUPID		='.$_POST['GROUPID']
		)) {
			return false;
		}
		break;
	// SEXPREF
	case 'sexpref':
		if (!require_array($_POST,'SEXPREF')
		||	false === require_number($_POST,'INCLUSIVE')
		) {
			return false;
		}
		$id = 0;
		foreach ($_POST['SEXPREF'] as $sexpref) {
			switch ($sexpref) {
				default:
					_error('Onbekende geaardheid: '.escape_specials($sexpref));
					return false;
				case 'hetero':	$id |= (1<<0); break;
				case 'homo':	$id |= (1<<1); break;
				case 'bi':	$id |= (1<<2); break;
			}
		}
		if (!$id) {
			_error('Geen geaardheid opgegeven!');
			return false;
		}
		if (!db_insert('promo_focus','
			INSERT INTO promo_focus SET
				TYPE		="sexpref",
				ID		='.$id.',
				PROMOID		='.$promoid.',
				INCLUSIVE	='.$_POST['INCLUSIVE'].',
				GROUPID		='.$_POST['GROUPID']
		)) {
			return false;
		}
		break;
	// AGE
	case 'age':
		$minage = have_idnumber($_POST,'MINAGE') ?: 0;
		$maxage = have_idnumber($_POST,'MAXAGE') ?: 0;
		if (!db_insert('promo_focus','
			INSERT INTO promo_focus SET
				TYPE		="age",
				PROMOID		='.$promoid.',
				ID		='.$minage.',
				OPTVAL		='.$maxage.',
				GROUPID		='.$_POST['GROUPID'])
		) {
			return false;
		}
		break;
	// BIRTHDAY
	case 'birthday':
		if (false === require_number($_POST,'BIRTHDAY')
		||	false === require_number($_POST,'BIRTHMONTH')
		||	!db_insert('promo_focus','
			INSERT INTO promo_focus SET
				TYPE	="birthday",
				PROMOID	='.$promoid.',
				ID	='.$_POST['BIRTHDAY'].',
				OPTVAL	='.$_POST['BIRTHMONTH'].',
				GROUPID	='.$_POST['GROUPID'])
		) {
			return false;
		}
		break;
	// RIGHT
	case 'right':
		if (!db_insert('promo_focus','
			INSERT INTO promo_focus SET
				TYPE		="right",
				PROMOID		='.$promoid.',
				ID		='.$_POST['ID'].',
				GROUPID		='.$_POST['GROUPID'])
		) {
			return false;
		}
		break;
	// LASTHERE
	case 'lasthere':
		if (false === require_number($_POST,'DAYS')
		||	!db_insert('promo_focus','
			INSERT INTO promo_focus SET
				TYPE		="lasthere",
				PROMOID		='.$promoid.',
				ID		='.(TODAYSTAMP - $_POST['DAYS']*24*3600).',
				GROUPID		='.$_POST['GROUPID']
		)) {
			return false;
		}
		break;

	case 'idlist':
		$okelems = array('party','location','organization','artist','city','province','user','favlocation','favorganization','user','lineup','buddies');
		if (!require_something_trim($_POST,'BODY')
		||	!require_anything($_POST,'ELEMENT')
		||	$_POST['ELEMENT'] != 'auto'
		&&	!require_element($_POST,'ELEMENT',$okelems)
		) {
			return false;
		}
		$auto = ($element = $_POST['ELEMENT']) == 'auto';
		if ($auto || $element == 'party') {
			if (false === require_number($_POST,'PARTYOPTVAL')) {
				return false;
			}
			$partyoptval = $_POST['PARTYOPTVAL'];
		} else {
			$partyoptval = 0;
		}
		$groupelems = isset($_POST['GROUPELEMS']);
		$lastgroupid = db_single('promo_focus','SELECT MAX(GROUPID) FROM promo_focus WHERE PROMOID='.$promoid);
		if ($lastgroupid === false) {
			return false;
		}
		if (!$groupelems) {
			if (false === ($fixedgroupid = have_number($_POST,'GROUPID')))  {
				$fixedgroupid = $lastgroupid + 1;
			}
		}
		$ins = array();
		$groupid = 0;
		require_once '_namefix.inc';
		$_POST['BODY'] = get_fixed_name($_POST['BODY']);
		$lines = preg_split('"[\r\n]+"',$_POST['BODY']);
		$linecnt = count($lines);
		for ($i = 0; $i < $linecnt; ++$i) {
			$line = trim($lines[$i]);
			if (!$line) {
				continue;
			}
			if (preg_match('"^groep\s+(\d+)"i',$line,$matches)) {
				continue;
			}
			$optval = $foundid = null;
			$inclusive = 1;
			$foundelement = null;
			if ($auto && preg_match('"^\s*(budget|locatie|organisatie|provincie|stad|genre|feest(?: (?:zeker|optie|ooit))?|leeftijd|favoriete\s+artiest|vrienden\s+van):\s*(niet\s+)?(.*)$"i',$line,$matches)) {
				$inclusive = empty($matches[2]) ? 1 : 0;
				$name = mytrim($matches[3]);
#				$name = preg_replace("'https?://(?:[/a-z:_\-\.]+)'",'',trim($matches[3]));
				switch (strtolower($matches[1])) {
				case 'budget':
					$foundelement = 'maxmoney';
					if (preg_match('"\x80\s+(\d+),-"i',$name,$matches)) {
						#print_rr($matches);
						$foundid = $matches[1]*100;
					}
					break;
				case 'locatie':
					$foundelement = 'location';
				case 'organisatie':
					if (!$foundelement) {
						$foundelement = 'organization';
					}
					if (preg_match('"^\s*(.*)\s+vanaf\s+(\d+)\s+([a-z]+)\s+(\d+)\s*$"',$name,$matches)) {
						$name = trim($matches[1]);
						$optval = mktime(0,0,0,get_month_from_string($matches[3]),$matches[2],$matches[4]);
					}
					if (preg_match('"https?://(\w+\.)?partyflock\.nl/'.$foundelement.'/(\d+)"',$name,$match)) {
						$foundid = $match[1];
					} else {
						$foundid = db_single($foundelement,'SELECT '.$foundelement.'ID FROM '.$foundelement.' WHERE NAME="'.addslashes($name).'"');
					}
					break;
				case 'vrienden van':
					$foundelement = 'buddies';
					$foundid = is_number($name) ?: db_single('user_account','SELECT USERID FROM user_account WHERE NICK="'.addslashes($name).'"');
					break;
				case 'organisatie':
					$foundelement = 'organization';
					$foundid = db_single('organization','SELECT ORGANIZATIONID FROM organization WHERE NAME="'.addslashes($name).'"');
					break;
				case 'stad':
					$foundelement = 'city';
					if (preg_match('"(?:binnen|een\s*straal\s*van)\s*(\d+)\s*km\s*van\s*(.*)$"i',$name,$matches)) {
#						print_rr($matches);
						[,$optval,$name] = $matches;
					}
					$foundid = db_single('city','SELECT CITYID FROM city WHERE NAME="'.addslashes($name).'"');
					break;
				case 'genre':
					$foundelement = 'genre';
					$foundid = db_single('genre','SELECT GID FROM genre WHERE NAME="'.addslashes($name).'"');
					break;
				case 'provincie':
					$foundelement = 'province';
					$foundid = db_single('province','SELECT PROVINCEID FROM province WHERE NAME="'.addslashes($name).'"');
					break;
				case 'feest':
					$optval = 3;
				case 'feest optie':
					if ($optval === null) $optval = 0;
				case 'feest zeker':
					if ($optval === null) $optval = 1;
				case 'feest ooit':
					if ($optval === null) $optval = 3;
					$foundelement = 'party';
					if (is_number($name)) {
						$foundid = db_single('party','SELECT PARTYID FROM party WHERE PARTYID='.$name);
					} elseif (preg_match('"^(.*)\b(\d{1,2})\s+([a-z]+)\s+(\d{4})\b(.*)$"',$name,$matches)) {
						$name = mytrim($matches[1].$matches[5]);
						$day = $matches[2];
						$month = $matches[3];
						$year = $matches[4];
						$foundid = db_single('party','SELECT PARTYID FROM party WHERE NAME="'.addslashes($name).'" AND STAMP BETWEEN '.($stamp = mktime(0,0,0,get_month_from_string($month),$day,$year)).' AND '.($stamp + 24*3600));
					} elseif (preg_match('"/party/(\d+)"',$line,$matches)) {
						$foundid = $matches[1];
					}
					break;
				case 'favoriete artiest':
					require_once '_artist.inc';
					$foundelement = 'artist';
					$startstamp = db_single('promo','SELECT STARTSTAMP FROM promo WHERE PROMOID='.$promoid) ?: CURRENTSTAMP;
					if (preg_match('"^(?:http.*/artist/|\[(?:dj|artist)=)(\d+)"',$name,$smatch)) {
						$foundid = $smatch[1];
					} else {
						if (!($artists = get_artist_choices($startstamp,$name,$size))) {
							$foundid = 0;
						} elseif ($size === 1) {
							[$artistid,$artist] = keyval($artists);
							$foundid = $artistid;
						} else {
							foreach ($artists as $artistid => $artist) {
								if (isset($artist['BESTFIT'])) {
									$foundid = $artistid;
									break;
								}
							}
							register_warning('element:warning:multiple_options_one_chosen', DO_UBB, [
								'ELEMENT'	=> 'artist',
								'SIZE'		=> $size,
								'INPUT'		=> $name,
								'CHOSENID'	=> $foundid
							]);
						}
					}
					break;

				case 'leeftijd':
					if (preg_match('"^t/m\s+(\d+)$"',$name,$match)) {
						$foundelement = 'age';
						$foundid = 0;
						$optval = $match[1];
					} elseif (preg_match('"^vanaf\s+(\d+)$"',$name,$match)) {
						$foundelement = 'age';
						$foundid = $match[1];
					} elseif (preg_match('"^(?:van\s+)?(\d+)\s+t/m\s+(\d+)$"',$name,$match)) {
						$foundelement = 'age';
						$foundid = $match[1];
						$optval = $match[2];
					}
					break;
				}
			} elseif (
				preg_match('"/([a-z]+)/(\d+)"',$line,$matches)
			||	preg_match('"\b([a-z]+)=(\d+)\b"',$line,$matches)
			) {
				if ($matches[1] == 'lineup') {
					if (false === ($artists = db_simpler_array('lineup','SELECT ARTISTID FROM lineup WHERE PARTYID='.$matches[2]))) {
						return;
					}
					if (!$artists) {
						warning('Geen artiesten in line-up gevonden!');
					} else {
						foreach ($artists as $artistid) {
							$lines[] = 'artist='.$artistid;
							++$linecnt;
						}
					}
					continue;
				}
				if ($matches[1] === 'dj') {
					$matches[1] = 'artist';
				}
				if ($auto || $matches[1] == $element) {
					$foundelement = $matches[1];
					if ($auto) {
						if (!in_array($foundelement,$okelems)) {
							warning('Automatisch herkenning ondersteunt element '.$foundelement.' niet in regel: '.escape_specials($line));
							continue;
						}
					}
					$foundid = $matches[2];
					_notice(ucfirst(element_name($foundelement)).' met ID '.$foundid.' toegevoegd na herkenning in de regel: '.escape_specials($line));
				} elseif (
					$matches[1] == 'location'
				&&	$element == 'favlocation'
				||	$matches[1] == 'organization'
				&&	$element == 'favorganization'
				||	$matches[1] == 'user'
				&&	(	$element == 'buddies'
					||	$element == 'user'
					)
				) {
					$foundelement = $element;
					$foundid = $matches[2];
				} else {
					warning('Regel is zeer waarschijnlijk voor een ander element bedoeld: '.escape_specials($line));
					continue;
				}
			} elseif ($auto) {
				warning('Automatische herkenning niet mogelijk in regel: '.escape_specials($line));
				continue;
			} elseif (!$auto && preg_match_all('"\d+"',$line,$matches)) {
				if (($optioncnt = count($matches[0])) > 1) {
					warning('Kon ID niet kiezen uit de '.$optioncnt.' getallen in de regel: '.escape_specials($line));
					continue;
				}
				$foundelement = $element;
				$foundid = $matches[0][0];
				$print_element = preg_replace('"^(fav)"','favourite_',$element);
				if ($mult = $print_element == 'buddies') {
					$print_element = 'buddy';
				}
				_notice(Eelement_name($print_element,$mult).' met ID '.$foundid.' toegevoegd gezien het als enige getal voorkomt in de regel: '.escape_specials($line));
			} else {
				warning('Geen ID gevonden in de regel: '.escape_specials($line));
				continue;
			}
			if (!$foundelement) {
				error_log('idlist focus processing failed, no ELEMENT found for promo '.$promoid.' and line: '.$line,0);
				continue;
			}
			if (!$foundid) {
				warning('Geen ID gevonden voor regel: '.escape_specials($line));
				error_log('idlist focus processing failed, no ID found for promo '.$promoid.' and line: '.$line,0);
				continue;
			}
			if ($groupelems
			||	!$groupid
			) {
				$groupid =	$groupelems
					?	(	isset($groupidforelem[$foundelement])
						?	$groupidforelem[$foundelement]
						:	($groupidforelem[$foundelement] = ++$lastgroupid)
						)
					:	$fixedgroupid;
			}
			$ins[] =
				'('.$promoid.
				',"'.$foundelement.
				'",'.$foundid.
				','.(		$foundelement == 'party'
					&&	$partyoptval
					?	$partyoptval
					:	($optval ?: 0)
				).
				','.$groupid.
				','.($element == 'user' && isset($_POST['INVERT']) ? ($inclusive ? 0:1) : $inclusive).
				')';
		}
		if (!$ins) {
			warning('Geen enkel element herkend in de invoer!');
		} elseif (
			!db_insert('promo_focus','
				INSERT INTO promo_focus (PROMOID,TYPE,ID,OPTVAL,GROUPID,INCLUSIVE)
				VALUES '.implode(',',$ins)
			)
		) {
			return false;
		}
		break;
	}
	reset_reach($promoid);
	if (isset($ins)
	?	$ins
	:	db_affected()
	) {
		register_notice('focus:notice:added_LINE');
	}
}
function promo_display_focus() {
	layout_open_section_header();
	?>Promo focus<?
	layout_close_section_header(!($addfocus = ($_REQUEST['ACTION'] == 'addfocus')));

	if (!($promoid = require_idnumber($_REQUEST,'sID'))
	||	!($is_admin = require_admin('promo'))
	||	!($have_lock = require_last_lock_and_freshen(LOCK_PROMO,$promoid))
	) {
		return;
	}
	$promo = db_single_assoc('promo','SELECT ACTIVE, SENT_STAMP, RSTAMP, VERSION FROM promo WHERE PROMOID='.$promoid,DB_FORCE_MASTER);
	if (!$promo) {
		if ($promo === false) {
			return;
		}
		not_found();
		return;
	}

	layout_open_menu();
	layout_menuitem(Eelement_name('promo'),get_element_href('promo',$promoid));
	if ($is_admin
	&&	!$promo['ACTIVE']
	&&	(	$have_lock
		||	$promo['SENT_STAMP']
		)
	) {
		layout_open_menuitem();
		if ($promo['SENT_STAMP']) {
			?><i>Bereik gereserveerd!</i><?
		} else {
			?><a href="/promo/<?= $promoid ?>/calculate">Bereik <?
			if ($promo['RSTAMP']) {
				?>opnieuw <?
			}
			?>bepalen</a><?
		}
		layout_close_menuitem();
	}
	layout_close_menu();

	$show = $_REQUEST['SUBACTION'] ?: null;

	ob_start();
	?><ul><?
	unset($GLOBALS['__promofocustypes']['favlabel']);
	asort($GLOBALS['__promofocustypes']);
	foreach ($GLOBALS['__promofocustypes'] as $type => $name) {
		if ($promo['VERSION'] >= 2) {
			if ($type == 'targetprice'
			||	$type == 'maxmoney'
			) {
				continue;
			}
		}
		if ($type[0] == '_') {
/*			?></ul><ul class="ib"><?*/
			continue;
		}
		?><li><?
		?><a<?
		if ($show
		&&	$show == $type
		) {
			?> class="bold"<?
		}
		?> href="/promo/<?= $promoid ?>/focus/<?= $type ?>#criteria"><?= $name ?></a><?

		?></li><?
	}
	?></ul><?
	$menu = ob_get_clean();

	if (!SMALL_SCREEN) {
		?><div class="l" style="width:20%"><?
		echo $menu;
		?></div><?
		?><div class="r" style="width:80%"><?
	}
	layout_open_box('promo');
	layout_box_header(Eelement_name('focus'));
	$groupids = promo_focus_overview($promoid,SHOW_REMOVE_LINK,'fw');
	layout_close_box();

	if (!$show
	||	!isset($GLOBALS['__promofocustypes'][$show])
	) {
		if (SMALL_SCREEN) {
			echo $menu;
		}
		return;
	}
	if ($addfocus) {
		display_messages();
	}
	?><form onsubmit="return submitForm(this);" method="post" id="focusform" action="/promo/<?= $promoid ?>/addfocus/<?= $show ?>#criteria"><?
	layout_open_box('promo');
	layout_open_box_header();

	$inclusive = have_idnumber($_POST,'INCLUSIVE') ?: false;
	$select_exclusive = $inclusive !== false && !$inclusive ? ' selected="selected"' : null;
	$optval = have_idnumber($_POST,'OPTVAL') ?: false;

	switch ($show) {
		// COUNTRY
		case 'country':
			echo Eelement_name('country');
			layout_close_box_header();
			?><p>Alle mensen <select name="INCLUSIVE"><option value="1">wel</option><option<?= $select_exclusive ?> value="0">niet</option></select> woonachtig in:</p><p><select name="COUNTRYID"><option value="0"></option><?
			_countrylist_display_options();
			?></select></p><?
			break;
		// PROVINCE
		case 'province':
			?>Provincie<?
			layout_close_box_header();
			?><p>Alle mensen <select name="INCLUSIVE"><option value="1">wel</option><option<?= $select_exclusive ?> value="0">niet</option></select> woonachtig in:</p><?
			?><p><select name="PROVINCEID[]" multiple size="13"><?
			provincelist_display_options(0,array(1,6));
			?></select></p><?
			break;
		// CITY
		case 'city':
			echo Eelement_name('city');
			layout_close_box_header();
			layout_open_table();
			layout_start_row();
				?>IN/EX<?
				layout_field_value();
				?><select name="INCLUSIVE"><?
					?><option value="1">inclusief</option><?
					?><option<?= $select_exclusive ?> value="0">exclusief</option><?
				?></select><?
			layout_restart_row();
				echo Eelement_name('city');
				layout_field_value();
				display_dynamic_city_pair();
			layout_restart_row();
				echo Eelement_name('radius');
				layout_field_value();
				?><input type="number" data-valid="number" name="OPTVAL" class="three_digits"<?
				if ($optval) {
					?> value="<?= $optval ?>"<?
				}
				?>> <?
				echo __('abbr:kilometer');
			layout_stop_row();
			layout_close_table();
			break;

		// EMPLOYEE
		case 'employee':
			echo Eelement_plural_name('employee');
			layout_close_box_header();
			?><div class="block">Gekoppeld aan <select required="required" name="OPTVAL"><?
			?><option></option><?
			?><option value="1"><?= element_name('location') ?></option><?
			?><option value="2"><?= element_name('organization') ?></option><?
			?><option value="3"><?= element_name('relation') ?></option><?
			?></select></div><?
			break;
		// ARTISTUSER
		case 'artistuser':
			?>Artiestleden<?
			layout_close_box_header();
			?><p>Alle leden toevoegen die gekoppeld zijn aan een artiestenprofiel.</p><?
			break;
		// ARTIST
		case 'artistarchive':
			$archive = true;
			?>Artiest archief<?
		case 'artist':
			if (!isset($archive)) {
				?>Artiest<?
			}
			layout_close_box_header();
			?><div class="block"><?
			if (!isset($GLOBALS['__artists'])) {
				?><input type="search" autosave="artistname" id="name" name="NAME" autofocus="autofocus" required="required" /><?
			} else {
				generate_name_doubles($GLOBALS['__artists'],'ORDER_NAME','artist',$GLOBALS['__ss']);
				sort_lineup_artists($GLOBALS['__artists'],$GLOBALS['__ss']);
				?>Er zijn meer dan een artiest mogelijk bij de term &quot;<?= escape_utf8($_POST['NAME']);
				?>&quot;, kies er een!</div><div class="block"><select name="ARTISTID"><?
				foreach ($GLOBALS['__artists'] as $artist) {
					show_artist_option($artist,$addempty,$selected,$GLOBALS['__ss']);
				}
				?></select><?
			}
			?></div><?
			if (isset($archive)) {
				?><p><label for="future"><input type="checkbox" name="FUTURE" id="future" value="1"<?
				if (isset($_POST['FUTURE'])) {
					?> checked="checked"<?
				}
				?>> Ook toekomstige optredens meenemen.</label></p><?
			}
			break;
		// GENRE
		case 'genre':
			?>Genre<?
			layout_close_box_header();
			?><p><select name="INCLUSIVE"><option value="1">inclusief</option><option<?= $select_exclusive ?> value="0">exclusief</option></select> de volgende stijlen:</p><?
#			_genrelist_display_checkboxes();
			show_genre_selection();
			break;
		// GOING
		case 'freedate':
			?>Vrije dag<?
			layout_close_box_header();
			?><p>Alleen mensen sturen die <select name="FREEDATETYPE"><?
			?><option value="nothing">helemaal niets</option><?
			?><option value="nothing_or_option" selected="selected">niets of een optie</option><?
			?><option value="require_option">een optie maar geen zeker</option><?
			?><option value="require_certain">zeker iets</option><?
			?><option value="require_any">zeker iets of een optie</option><?
			?></select> in hun agenda hebben staan op:</p><p><?
			_date_display_select();
			?></p><?
			break;
		// MAX
		case 'max':
			?>Gelimiteerd bereik<?
			layout_close_box_header();
			?><p>Maximum aantal mensen bereiken:</p><p><input type="number" data-valid="number" name="MAX" /></p><?
			break;

		// PROMO
		case 'promo':
			?>Promobericht<?
			layout_close_box_header();
			?><p><select name="INCLUSIVE" onchange="document.forms.focusform.SEEN.value=(this.value=='1') ? '0' : '1'"><?
				?><option value="0">Niet</option><?
				?><option<?= $select_exclusive ?> value="1">Wel</option><?
			?></select> de mensen bericht sturen die promobericht met onderstaand ID <?
			?><select name="SEEN"><?
				?><option value="0">niet</option><?
				?><option selected value="1">wel</option><?
			?></select><?
			?> hebben gezien.</p><?
			?><p><input type="number" data-valid="number" name="USEPROMOID" /></p><?
			break;
		// PROMO
		case 'focuscopy':
			?>Promobericht criteria kopi&euml;ren<?
			layout_close_box_header();
			?><input type="hidden" name="GROUPID" value="0" /><?
			?><p>ID: <input type="text" name="COPYPROMOID" /></p><?
			break;
		// FLOCK
		case 'flock':
			echo Eelement_name('flock');
			layout_close_box_header();
			?><p>Alleen mensen die lid zijn van flock met ID <?
			?><input type="number" data-valid="number" name="FLOCKID" /></p><?
			break;
		// CONTEST
		case 'contest':
			echo Eelement_name('contest');
			layout_close_box_header();
			require_once '_contestoptions.inc';
			?><p>Alleen mensen sturen die <?
			?><select name="INCLUSIVE"><option value="1">wel</option><option<?= $select_exclusive ?> value="0">niet</option></select> meedoen aan volgende actie:</p><p><?
			?><select name="CONTESTID"><?
				show_contest_options(0,TODAYSTAMP-7*24*3600);
			?></select> of CONTESTID: <input type="number" data-valid="number" class="id" name="CONTESTID" /></p><?
			break;
		// USER
		case 'user':
			echo Eelement_name('user');
			layout_close_box_header();
			?><p>Onderstaand lid <?
			?><select name="INCLUSIVE"><option value="1">wel</option><option<?= $select_exclusive ?> value="0">niet</option></select> sturen:</p><p><?
			if (!isset($GLOBALS['__users'])) {
				?><input type="search" autosave="nickname" id="nick" name="NICK" autofocus="autofocus" required="required" /><?
			} else {
				?>Er zijn meer leden mogelijk bij de term &quot;<?= escape_specials($_POST['NICK']);
				?>&quot;, kies er een!</p><p><select name="USERID"><?
				foreach ($GLOBALS['__users'] as $user) {
					?><option<?
					if (0 == strcasecmp($user['NICK'],$_POST['NICK'])) {
						?> selected="selected"<?
					}
					?> value="<?= $user['USERID'];
					?>"><?= escape_specials($user['NICK']);
					?></option><?
				}
				?></select><?
			}
			?></p><?
			break;
		// BUDDIES
		case 'buddies':
			?>Vrienden van<?
			layout_close_box_header();
			?><p>Alleen mensen sturen die vriend zijn van: (USERID)</p><p><input type="number" data-valid="number" name="USERID" /></p><?
			?><p><label for="leveltwo"><input type="checkbox" name="LEVELTWO" id="leveltwo" value="1" /> inclusief vrienden van deze vrienden</label></p><?
			break;
		// EROTIC
		case 'erotic':
			echo __C('attrib:erotic');
			layout_close_box_header();
			?><p>Alleen mensen sturen die <?
			?><select name="INCLUSIVE"><option value="1">wel</option><option<?= $select_exclusive ?> value="0">niet</option></select> <?
			?> een erotisch feest in hun agenda <select name="OPTVAL"><?
				?><option<? if (isset($_POST['OPTVAL']) && !$_POST['OPTVAL']) echo ' selected="selected"'; ?> value="0">als optie</option><?
				?><option<? if (isset($_POST['OPTVAL']) && $_POST['OPTVAL'] == 1) echo ' selected="selected"'; ?> value="1">als zeker</option><?
				?><option<? if (isset($_POST['OPTVAL']) && $_POST['OPTVAL'] == 2) echo ' selected="selected"'; ?> value="2">als zeker of optie</option><?
				?><option<? if (!isset($_POST['OPTVAL']) || $_POST['OPTVAL'] == 3) echo ' selected="selected"'; ?> value="3">als zeker of ooit optie</option><?
			?></select> hebben staan</p><?
			break;
		// PARTY
		case 'party':
		case 'partyselect':
			echo Eelement_name('party');
			layout_close_box_header();
			?><p>Alleen mensen sturen die het volgende feest <?
			?><select name="INCLUSIVE"><option value="1">wel</option><option<?= $select_exclusive ?> value="0">niet</option></select> <?
			?>in hun agenda <select name="OPTVAL"><?
				?><option<? if (isset($_POST['OPTVAL']) && !$_POST['OPTVAL']) echo ' selected="selected"'; ?> value="0">als optie</option><?
				?><option<? if (isset($_POST['OPTVAL']) && $_POST['OPTVAL'] == 1) echo ' selected="selected"'; ?> value="1">als zeker</option><?
				?><option<? if (isset($_POST['OPTVAL']) && $_POST['OPTVAL'] == 2) echo ' selected="selected"'; ?> value="2">als zeker of optie</option><?
				?><option<? if (!isset($_POST['OPTVAL']) || $_POST['OPTVAL'] == 3) echo ' selected="selected"'; ?> value="3">als zeker of ooit optie</option><?
			?></select> hebben staan:</p><?
			if (isset($GLOBALS['__parties'])) {
				number_reverse_sort($GLOBALS['__parties'],'STAMP');
				layout_open_table();
				foreach ($GLOBALS['__parties'] as $party) {
					layout_start_rrow_right(0,'bold-hilited');
					?><label for="prty<?= $party['PARTYID'];
					?>"><? _dateday_display($party['STAMP']);
					?></label><?
					layout_next_cell(class: 'hpad center');
					?><input id="prty<?= $party['PARTYID'];
					?>" type="checkbox" name="PARTYIDS[]" value="<?= $party['PARTYID'];
					?>" checked="checked" onclick="cbclick(this,this.parentNode.parentNode)"><?
					layout_next_cell(class: 'hpad center');
					?><select name="OPTVALS[]"><?
						?><option<? if (isset($_POST['OPTVAL']) && !$_POST['OPTVAL']) echo ' selected="selected"'; ?> value="0">als optie</option><?
						?><option<? if (isset($_POST['OPTVAL']) && $_POST['OPTVAL'] == 1) echo ' selected="selected"'; ?> value="1">als zeker</option><?
						?><option<? if (isset($_POST['OPTVAL']) && $_POST['OPTVAL'] == 2) echo ' selected="selected"'; ?> value="2">als zeker of optie</option><?
						?><option<? if (!isset($_POST['OPTVAL']) || $_POST['OPTVAL'] == 3) echo ' selected="selected"'; ?> value="3">als zeker of ooit optie</option><?
					?></select><?
					layout_next_cell();
					?><a href="<?= get_element_href('party',$party['PARTYID']) ?>"><?
					echo escape_utf8($party['NAME']);
					if ($party['SUBTITLE']) {
						?> <small>&middot; <?= escape_utf8($party['SUBTITLE']) ?></small><?
					}
					?></a><?
					layout_stop_row();
				}
				layout_close_table();
			} elseif ($show == 'partyselect') {
				require_once '_genrelist.inc';
				require_once '_countrylist.inc';
				layout_open_table('fw');

				layout_start_row();
				echo Eelement_plural_name('genre');
				layout_field_value();
				show_genre_selection();

				layout_restart_row();
				echo Eelement_name('country');
				layout_field_value();
				?><select name="COUNTRYID"><?
					?><option value="0"></option><?
					_countrylist_display_options();
				?></select><?

				layout_restart_row();
				echo Eelement_name('date');
				layout_field_value();
				?><label class="hilited"><?
				show_input([
					'type'		=> 'checkbox',
					'class'		=> 'upLite',
					'name'		=> 'WHEN[future]',
					'value'		=> 1,
					'checked'	=> true
				]);
				?> <?= __('date:future')
				?></label><br /><?
				?><label class="not-hilited"><?
				show_input([
					'type'		=> 'checkbox',
					'class'		=> 'upLite',
					'name'		=> 'WHEN[past]',
					'value'		=> 1,
					'onclick'	=> 'setdisplay(this.parentNode.nextSibling,this.checked)',
				]);
				?> <?= __('date:past')
				?></label><span class="hidden"> <?
				$minstamp = db_single('party','SELECT MIN(STAMP) FROM party');
				[$y] = _getdate($minstamp);
				?> vanaf <?
				?><select name="YEAR"><?
					global $__year;
					for (; $y < $__year; ++$y) {
						?><option<?
						if ($y == $__year - 5) {
							?> selected="selected"<?
						}
						?> value="<?= $y ?>"><?= $y ?></option><?
					}
				?></select></span><?
				layout_close_table();
			} else {
				require_once '_partyoptions.inc';
				layout_open_table(TABLE_CLEAN);
				layout_start_row();
					echo __C('action:choose');
					layout_field_value();
					?><select name="SELECTED_PARTYID"><?
					?><option value="0"></option><?
					show_party_options();
					?></select><?
				layout_restart_row();
					echo Eelement_name('id');
					layout_field_value();
					?><input type="number" data-valid="number" name="PARTYID" /><?
				layout_restart_row();
					?>Multinaam<?
					layout_field_value();
					?><input type="search" autosave="eventname" name="NAME" /> <label><?
					?><input type="checkbox" name="EXACT" value="1" /> <?= __('search:exact') ?></label><?
				layout_stop_row();
				layout_close_table();
			}
			break;
		// FAV OF USER
		case 'favofuser':
			echo Eelement_plural_name('user_favourites');
			layout_close_box_header();
			?><div class="block"><?
			?><input type="hidden" name="GROUPID" value="0" /><?
			?><select name="WHAT[]" multiple="multiple"><?
				foreach (array('artist','location','organization') as $element) {
					?><option value="<?= $element ?>"><?= Eelement_plural_name('favourite_'.$element) ?></option><?
				}
			?></select><?
			?> van lid met ID <input type="number" min=2 max=<?= db_single('user_account','SELECT MAX(USERID) FROM user_account') ?> data-valid="number" name="USERID" /> opnemen.</div><?
			break;
		// FAV LABEL
		case 'favlocation':
		case 'favorganization':
			$element = substr($show,3);
			require_once '_'.$element.'options.inc';
			echo Eelement_name('favourite_'.$element);
			layout_close_box_header();
			?><div class="block">Alleen mensen sturen die onderstaande <?= element_name($element) ?> als favoriet hebben:</div><?
			?><div class="block"><select id="id" name="ID" required="required"><option></option><?
			$func = 'show_'.$element.'_options';
			$func();
			?></select> of <?
			show_input(array(
				'type'		=> 'number',
				'name'		=> 'IDNUM',
				'min'		=> 0,
				'placeholder'	=> 'ID',
				'onchange'	=> "setattr('id','required',this.value.is_empty())"
			));
			?></div><?
			break;
		// LOCATION
		case 'location':
			require_once '_locationoptions.inc';
			echo Eelement_name('location');
			layout_close_box_header();
			?><p>Alleen mensen sturen die onderstaande locatie <select name="INCLUSIVE"><?
				?><option value="1">wel</option><?
				?><option<?= $select_exclusive ?> value="0">niet</option><?
			?></select>  hebben bezocht:</p><p><select name="LOCATIONID"><option value="0"></option><?
			show_location_options();
			?></select></p><p><?
			?><label for="since"><input onclick="setdisplay('sincedate',this.checked)" type="checkbox" value="1" name="SINCE" id="since" /> Sinds</label><?
			?><span id="sincedate" class="hidden">: <? _date_display_select_stamp( CURRENTSTAMP - ONE_YEAR, 'SINCE', -10, +2); ?></span><?
			?></p><?
			break;
		// ORGANIZATION
		case 'organization':
			require_once '_organizationoptions.inc';
			echo Eelement_name('organization');
			layout_close_box_header();
			?><p>Alleen mensen sturen die <select name="INCLUSIVE"><?
				?><option value="1">wel</option><?
				?><option<?= $select_exclusive ?> value="0">niet</option><?
			?></select> een feest hebben bezocht van onderstaande organisatie:</p><p><select name="ORGANIZATIONID"><option value="0"></option><?
			show_organization_options();
			?></select></p><p><?
			?><label for="since"><input onclick="setdisplay('sincedate',this.checked)" type="checkbox" value="1" name="SINCE" id="since" /> Sinds</label><?
			?><span id="sincedate" class="hidden">: <? _date_display_select_stamp(CURRENTSTAMP - ONE_YEAR, 'SINCE', -10, +2); ?></span><?
			?></p><?
			break;
		// GENDER
		case 'gender':
			echo Eelement_name('gender');
			layout_close_box_header();
			?><p>Alleen naar <select name="GENDER"><option value="F">vrouwen</option><option value="M">mannen</option></select> sturen.</p><?
			break;
		// RELATION
		case 'relation':
			echo Eelement_name('relation');
			layout_close_box_header();
			?><p>Alleen naar leden <select name="RELATION"><?
				?><option value="0">zonder</option><?
				?><option value="1">met</option><?
			?></select> relatie sturen.</p><?
			break;
		// SEXPREF
		case 'sexpref':
			?>Geaardheid<?
			layout_close_box_header();
			?><p>Alleen sturen aan mensen <select name="INCLUSIVE"><option value="1">met</option><option<?= $select_exclusive ?> value="0">niet</option></select> als geaardheid:</p><p>
			<input type="checkbox" value="hetero" name="SEXPREF[]" /> hetero<br />
			<input type="checkbox" value="homo" name="SEXPREF[]" /> homo / lesbisch<br />
			<input type="checkbox" value="bi" name="SEXPREF[]" /> bi</p><?
			break;
		// AGE
		case 'age':
			echo Eelement_name('age');
			layout_close_box_header();
			?><div class="block">van <?
			?><input type="number" data-valid="number" name="MINAGE" class="three_digits center" /> t/m <?
			?><input type="number" data-valid="number" name="MAXAGE" class="three_digits center" /><?
			?></div><?
			break;
		case 'birthday':
			echo Eelement_name('birthday');
			layout_close_box_header();
			?><p>Alle mensen die jarige zijn op:</p><select name="BIRTHDAY"><?
			for ($cnt = 1; $cnt <= 31; ++$cnt) {
				?><option value="<?= $cnt;
				?>"><?= $cnt;
				?></option><?
			}
			?></select> <select name="BIRTHMONTH"><?
			for ($cnt = 1; $cnt <= 12; ++$cnt) {
				?><option value="<?= $cnt;
				?>"><?= _month_name($cnt);
				?></option><?
			}
			?></select></p><?
			break;
		case 'right':
			?>Medewerkers<?
			layout_close_box_header();
			?><p>Stuur een bericht naar <select name="ID">
			<option value="<?= ANY_RIGHT; ?>">alle medewerkers</option>
			<option value="<?= ANY_BUT_CAM_RIGHT; ?>">alle medewerkers, fotografen uitgezonder</option>
			</select></p><?
			break;

		case 'lasthere':
			?>Laatst hier<?
			layout_close_box_header();
			?><p>Actief in de afgelopen <input type="number" data-valid="number" name="DAYS" class="three_digits" /> dagen</p><?
			break;

		case 'idlist':
			echo Eelement_name('list');
			layout_close_box_header();
			?><p>Volgende elementen als <?
			?><select onchange="<?
				?>setdisplay('partyoptval',this.value=='auto'||this.value=='party');<?
				?>setdisplay('invertcb',this.value=='user');<?
				?>" name="ELEMENT"><?
				?><option value="auto"><?= __('attrib:automatic') ?></option><?
				?><optgroup label="<?= __('state:visited') ?>"><?
					?><option value="party"><?= element_name('party') ?></option><?
					?><option value="location"><?= element_name('location') ?></option><?
					?><option value="organization"><?= element_name('organization') ?></option><?
				?></optgroup><?
				?><optgroup label="<?= element_name('favourite') ?>"><?
					?><option value="artist"><?= element_name('artist') ?></option><?
					?><option value="favlocation"><?= element_name('location') ?></option><?
					?><option value="favorganization"><?= element_name('organization') ?></option><?
				?></optgroup><?
				?><optgroup label="<?= __('state:residing') ?>"><?
					?><option value="city"><?= element_name('city') ?></option><?
				?></optgroup><?
 				?><optgroup label="<?= element_name('addressee') ?>"><?
					?><option value="user"><?= element_name('user') ?></option><?
					?><option value="buddies"><?= element_plural_name('buddy_of_user') ?></option><?
				?></optgroup><?
			?></select> toevoegen:</p><?
			?><p><textarea name="BODY" rows="20" cols="100" required="required"></textarea></p><?
			?><p id="partyoptval">Voor herkende feesten geldt: <select name="PARTYOPTVAL"><?
				?><option value="0">alleen twijfelaars</option><?
				?><option value="1">alleen bezoekers</option><?
				?><option value="2">bezoekers en twijfelaars</option><?
				?><option value="3" selected="selected">bezoekers en ooit twijfelaars</option><?
			?></select></p><?
			?><p class="hidden" id="invertcb"><label><input type="checkbox" name="INVERT" value="1" checked="checked" /> <?= __C('status:inverted') ?></label></p><?
			?><p><label><input onclick="setdisplay('groupid',!this.checked)" type="checkbox" checked="checked" value="1" name="GROUPELEMS" /> Elk element in aparte groep</label></p><?
			break;
	}
	layout_close_box();
	?><p><input type="submit" value="<?= __('action:add'); ?>" /><?
	if ($show != 'maxmoney'
	&&	$show != 'targetprice'
	&&	$show != 'focuscopy'
	&&	$show != 'favofuser'
	) {
		?> <select<?
		if (!isset($_POST['GROUPID'])) {
			$maxpromoid = db_single('promo_focus','SELECT MAX(GROUPID) FROM promo_focus WHERE PROMOID='.$promoid);
			if ($show == 'idlist') ++$maxpromoid;
			$_POST['GROUPID'] = $maxpromoid;
		}
		if ($show == 'idlist') {
			?> class="hidden"<?
		}
		?> id="groupid" name="GROUPID"><option value="0">globaal</option><?
		foreach ($groupids as $groupid) {
			?><option<?
			if (isset($_POST['GROUPID'])
			&&	$_POST['GROUPID'] == $groupid
			) {
				?> selected="selected"<?
			}
			?> value="<?= $groupid ?>"><?= $groupid ?></option><?
		}
		$newgroupid = $groupids ? $groupid+1 : 1;
		?><option<?
		if (isset($_POST['GROUPID'])
		&&	$_POST['GROUPID'] == $newgroupid
		) {
			?> selected="selected"<?
		}
		?> value="<?= $groupids ? $groupid+1 : 1 ?>">nieuw</option><?
		?></select><?
	}
	?></p></form></div><?
	if (!SMALL_SCREEN) {
		?></div><?
	} else {
		echo $menu;
	}
}
function promo_display_overview() {
	require_once '_promolist.inc';
	layout_show_section_header();

	if (!have_admin('promo')) {
		if (!require_relation()) {
			return;
		}
		promo_menu(null,false,true);

		if ($_REQUEST['ACTION'] === 'archive') {
			$archive = true;
		}
		$rowlist = db_rowuse_array(['promo', 'relation', 'relationmember'],'
			SELECT '._promolist_columns().'
			FROM promo
			JOIN relation USING (RELATIONID)
			JOIN relationmember USING (RELATIONID)
			LEFT JOIN invoice ON invoice.INVOICENR = promo.INVOICENR
			WHERE relationmember.USERID = '.CURRENTUSERID.'
			  AND promo.REMOVED = 0
			  AND (	   promo.REQUEST = 0
					OR COSTSENT = 1
				OR ACTIVE = 1
				  )'.
			(	isset($archive)
			?	' AND (STOPSTAMP < '.TODAYSTAMP.' OR promo.INVOICENR = 0 OR STATUS = "notpaid")'
			:	' AND STOPSTAMP > '.(TODAYSTAMP - 7*24*3600)
			).'
			ORDER BY STARTSTAMP DESC'
		);
		if ($rowlist === false) {
			return;
		}
		if (!$rowlist) {
			?><p><?= __(isset($archive) ? 'promo:info:none_in_archive_LINE' : 'promo:info:no_recent_LINE'); ?></p><?
			/*Geen promoberichten gevonden.</p><?*/
		} else {
			layout_open_box('promo');
			layout_open_table(TABLE_FULL_WIDTH | TABLE_VTOP);
			_promolist_display_rows($rowlist,false);
			layout_close_table();
			layout_close_box();
		}
		return;
	}

	promo_menu(null,true,false);

	if (!($archive = ($_REQUEST['ACTION'] == 'archive'))) {
		$and_not_locked = null;
		$readylist = db_rowuse_hash(['promo', 'relation', 'invoice'],'
			SELECT '._promolist_columns().', relation.NAME, COSTSENT
			FROM promo
			LEFT JOIN relation USING (RELATIONID)
			LEFT JOIN invoice ON invoice.INVOICENR = promo.INVOICENR
			WHERE promo.REMOVED = 0
			  AND ACTIVE = 0
			  AND PROMOID != 9842
			  AND NOT ISNULL((
				SELECT 1
				FROM contact_ticket
				WHERE ELEMENT = "promo"
				  AND ID = PROMOID
				  AND (STATUS = "open" OR STATUS = "closed" AND TRACKIT = 1)
				LIMIT 1
			  ))'.
			 $and_not_locked.'
			ORDER BY STARTSTAMP ASC'
		);
		$waitinglist = db_rowuse_hash(['promo', 'relation', 'invoice'], '
			SELECT '._promolist_columns().', relation.NAME, COSTSENT
			FROM promo
			LEFT JOIN relation USING (RELATIONID)
			LEFT JOIN invoice ON invoice.INVOICENR = promo.INVOICENR
			WHERE '.($readylist ? 'PROMOID NOT IN ('.implodekeys(',', $readylist).') AND' : '').'
				  promo.REMOVED = 0
			  AND ACTIVE = 0
			  AND PROMOID != 9842'.
			 $and_not_locked.'
			ORDER BY STARTSTAMP ASC'
		);
		if ($readylist || $waitinglist) {
			layout_open_box('promo');
			if ($readylist) {
				layout_box_header('Te verwerken');
				layout_open_table('fw vtop default');
				_promolist_display_rows($readylist,true,true);
				layout_close_table();
			}
			if ($waitinglist) {
				layout_box_header('In afwachting');
				layout_open_table('fw vtop default');
				_promolist_display_rows($waitinglist,true,true);
				layout_close_table();
			}
			layout_close_box();
		}
	}
	$rowlist = db_rowuse_array(array('promo','relation'),'
		SELECT	'._promolist_columns().', relation.NAME
		FROM promo
		LEFT JOIN relation USING (RELATIONID)
		LEFT JOIN invoice ON invoice.INVOICENR=promo.INVOICENR
		WHERE '.($archive ? 'STOPSTAMP<=' : '(ISNULL(TARGETPRICE) OR BUDGET=0 OR TARGETPRICE<BUDGET) AND ACTIVE=1 AND STOPSTAMP>').TODAYSTAMP.'
		ORDER BY STARTSTAMP '.($archive ? 'DESC' : 'ASC').',TITLE'
	);
	if ($rowlist === false) {
		return;
	}
	if ($rowlist) {
		layout_open_box('promo');
		layout_open_table(TABLE_FULL_WIDTH | TABLE_VTOP);
		_promolist_display_rows($rowlist);
		layout_close_table();
		layout_close_box();
	}
	if ($_REQUEST['ACTION']) {
		return;
	}
}
function promo_show_stats($promo,$is_admin = false,$price = null,$have_lock = 0,&$reach = 0) {
	if (is_scalar($promo)) {
		$promoid = $promo;
		$promo = db_single_assoc('promo','
			SELECT RSTAMP,SENT_STAMP,PROMOID,BEFOREGOING,BEFOREGOINGMAYBE,AFTERGOING,AFTERGOINGMAYBE,PRICEID,ACTIVE,STARTSTAMP,VERSION
			FROM promo
			WHERE PROMOID='.$promo
		);
		if (!$promo) {
			return false;
		}
	} else {
		$promoid = $promo['PROMOID'];
	}
	$v = $promo['VERSION'];
	if ($promo['SENT_STAMP']) {
		$sentinfo = memcached_single_assoc(
			[$table = $v == 1 ? 'promo_letter' : 'promo_letter_v2','user'],'
			SELECT	COUNT(*) AS TOTAL_CNT,
				COUNT(IF(FLAGS&'.PROMO_CLOSED.',1,NULL)) AS CLOSED_CNT,
				COUNT(IF(FLAGS&'.PROMO_MESSAGE_CLOSED.',1,NULL)) AS READ_CNT,
				COUNT(IF(FLAGS&'.PROMO_TEASER_CLOSED.',1,NULL)) AS SEEN_CNT,
				COUNT(IF(FLAGS&'.PROMO_MESSAGE_SHOWN.',1,NULL)) AS VIEW_CNT,
				COUNT(IF(FLAGS&'.PROMO_TEASER_SHOWN.',1,NULL)) AS SCAN_CNT,
				COUNT(IF(FLAGS&'.PROMO_COUNTER_SHOWN.',1,NULL)) AS COUNTER_CNT,
				COUNT(IF(SEX="M",1,NULL)) AS MALE_CNT,
				COUNT(IF(SEX="F",1,NULL )) AS FEMALE_CNT
			FROM '.$table.'
			LEFT JOIN user USING (USERID)
			WHERE PROMOID='.$promo['PROMOID'],
			30
		);
	} else {
		$sentinfo = memcached_single_assoc(
			['promo_letter_concept','user'],'
			SELECT	COUNT(*) AS TOTAL_CNT,
				COUNT(IF(SEX="M",1,NULL)) AS MALE_CNT,
				COUNT(IF(SEX="F",1,NULL)) AS FEMALE_CNT
			FROM promo_letter_concept
			LEFT JOIN user USING (USERID)
			WHERE PROMOID='.$promo['PROMOID'],
			30
		);
	}
	if (!$sentinfo) {
		return false;
	}
	if ($promo['SENT_STAMP']
	||	$promo['RSTAMP']
	) {
		layout_open_table(TABLE_FULL_WIDTH);
		if ($promo['SENT_STAMP']) {
			layout_start_row();
				?>Vastgezet op<?
				layout_field_value_right();
				_datedaytime_display($promo['SENT_STAMP']);
			layout_stop_row();
		} elseif ($promo['RSTAMP']) {
			layout_start_row();
				?>Bereik bepaald op<?
				layout_field_value_right();
				_datedaytime_display($promo['RSTAMP']);
			layout_stop_row();
		}
		layout_close_table();
	}
	if ($promo['PRICEID'] >= 1126340776
	&&	$promo['VERSION'] == 1
	) {
		$catstats = memcached_rowuse_array('promo_class_stats','
			SELECT IF(CLASS=0,1,CLASS) AS CLASS,DAY,CNT
			FROM promo_class_stats
			WHERE PROMOID='.$promo['PROMOID'].'
			ORDER BY DAY ASC,CLASS ASC'
		);
		if ($catstats === false) {
			return;
		}
		if ($catstats) {
			$totalreach = 0;
			$totalclass = 0;
			foreach ($catstats as &$catinfo) {
				$totalreach += $catinfo['CNT'];
				$totalclass += $catinfo['CLASS'] * $catinfo['CNT'];
			}
			unset($catinfo);
			$avgclass = round($totalclass / $totalreach,2);
			$GLOBALS['__catstats'] = $catstats;
		}
	}
	layout_open_table(TABLE_FULL_WIDTH);
	if ($sentinfo['TOTAL_CNT']) {
		layout_start_reverse_row();
			echo round(100 * $sentinfo['MALE_CNT'] / $sentinfo['TOTAL_CNT']);
			?> / <?
			echo round(100 * $sentinfo['FEMALE_CNT'] / $sentinfo['TOTAL_CNT']);
			layout_value_field();
			echo element_plural_name('man') ?> / <? echo element_plural_name('woman');
		if (isset($avgclass)) {
			layout_restart_reverse_row();
			echo $avgclass;
			layout_value_field();
			echo element_name('average_class');
		}
		if ($v == 1) {
			if (isset($promo['MAXREACH'])
			&&	$promo['MAXREACH']
			&&	$promo['MAXREACH'] != $promo['REACH']
			) {
				layout_restart_reverse_row();
				echo $promo['MAXREACH'];
				layout_value_field();
				echo element_name('available');
			}
			layout_restart_reverse_row();
				echo $sentinfo['TOTAL_CNT'] ?:  __('answer:none');
				layout_value_field();
				echo element_name('total');
		} else {
			$reach = 0;
			if ($promo['BUDGET']) {
				$max = $promo['BUDGET'] / $price['SEEN'];
				if ($max < $promo['REACH']) {
					$reach = $max;
				}
			}

			layout_restart_reverse_row();
			echo $promo['REACH'];
			layout_value_field();
			echo element_name('available');

			if ($reach) {
				layout_restart_reverse_row();
				echo $reach;
				layout_value_field();
				echo element_name('reachable');
			}
		}
		if ($promo['SENT_STAMP']) {
			require_once '_bubble.inc';
			layout_restart_reverse_row();
				echo $sentinfo['COUNTER_CNT'];
				layout_value_field();
				$bubble = new bubble(BUBBLE_BLOCK | BUBBLE_30_WIDE | HELP_BUBBLE);
				$bubble->catcher_and_title(__('promo:stat:passed'));
				$bubble->content(
					'Onder gepasseerd vallen alle mensen die het tellertje hebben gezien.<br />'.
					'Het kan dus zijn dat niet al deze personen hebben doorgeklikt naar hun berichten.'
				);
				$bubble->display();
			layout_restart_reverse_row();
				echo $sentinfo['SCAN_CNT'];
				layout_value_field();
				echo __('promo:stat:seen');
			layout_restart_reverse_row(0,'notice');
				if ($v < 2) {
					$keptinfo = db_single_assoc(
						'promokept','
						SELECT	COUNT(IF(KEPT=b\'1\',1,NULL)) AS NOW,
							COUNT(*) AS EVER
						FROM promokept
						WHERE PROMOID='.$promo['PROMOID']
					);
					echo $keptinfo['NOW']; ?> / <? echo $keptinfo['EVER'];
					layout_value_field();
					$bubble->cleanup();
					$bubble->catcher_and_title(__('promo:stat:kept'));
					$bubble->content(
						__('promo:info:kept_at_this_moment').' / '.__('promo:info:ever_kept')
					);
					$bubble->display();
				} else {
					echo $sentinfo['SCAN_CNT'] - $sentinfo['CLOSED_CNT'];
					layout_value_field();
					echo __('promo:stat:kept');
				}
			layout_restart_reverse_row();
				echo $sentinfo['SEEN_CNT'];
				layout_value_field();
				echo __('promo:stat:clicked_seen');
			layout_restart_reverse_row();
				echo $sentinfo['VIEW_CNT'];
				layout_value_field();
				echo __('promo:stat:viewed');
			layout_restart_reverse_row();
				echo $sentinfo['READ_CNT'];
				layout_value_field();
				echo __('promo:stat:clicked_viewed');
		}
		layout_stop_row();
	}
	if ($promo['BEFOREGOING'] !== null
	&&	($partyidstr = memcached_single('connect','
			SELECT GROUP_CONCAT(ASSOCID)
			FROM connect
			WHERE MAINTYPE="promo" AND MAINID='.$promoid.'
			  AND ASSOCTYPE="party"'))
	) {
		$tables = [$table = $v == 1 ? 'promo_letter' : 'promo_letter_v2','goingprepromo','goingpostpromo','going'];
		db_read($tables,'SET max_statement_time=600',DBSET,0,0);
		$totals = memcached_simple_hash($tables,'
			SELECT 0+MAYBE,"pre" AS xWHEN,COUNT(*)
			FROM '.$table.'
			JOIN goingprepromo USING (PROMOID,USERID)
			WHERE PROMOID='.$promoid.'
			  AND (FLAGS&'.PROMO_TEASER_SHOWN.')
			GROUP BY MAYBE,xWHEN
			UNION
			SELECT 0+MAYBE,"post" AS xWHEN,COUNT(*)
			FROM '.$table.'
			JOIN goingpostpromo USING (PROMOID,USERID)
			WHERE PROMOID='.$promoid.'
			  AND (FLAGS&'.PROMO_TEASER_SHOWN.')
			GROUP BY MAYBE,xWHEN
			UNION
			SELECT 0,"now" AS xWHEN,(
				SELECT COUNT(*)
				FROM going
				JOIN promo_letter USING (USERID)
				WHERE PROMOID='.$promoid.'
				  AND PARTYID IN ('.$partyidstr.')
				  AND MAYBE=0
				  AND (FLAGS&1)
			)
			UNION 
			SELECT 1,"now" AS xWHEN,(
				SELECT COUNT(*)
				FROM going
				JOIN promo_letter USING (USERID)
				WHERE PROMOID='.$promoid.'
				  AND PARTYID IN ('.$partyidstr.')
				  AND MAYBE=1
				  AND (FLAGS&1)
			)
			',
			0,null,DB_NON_ASSOC
		);
		if ($totals=== false) {
			return;
		}
		$finished = $promo['STOPSTAMP'] <= CURRENTSTAMP;

		$changes = memcached_simple_hash([$table,'goingprepromo','going','goingpostpromo'],'
			SELECT 0+PRE_MAYBE,0+POST_MAYBE,COUNT(*) AS CNT
			FROM '.($finished
			? '(	SELECT pre.USERID,pre.PARTYID,pre.MAYBE AS PRE_MAYBE,post.MAYBE AS POST_MAYBE
				FROM goingprepromo AS pre
				LEFT JOIN goingpostpromo AS post USING (PROMOID,USERID,PARTYID)
				WHERE PROMOID='.$promoid.'
				  AND PARTYID IN ('.$partyidstr.')
				UNION
				SELECT post.USERID,post.PARTYID,pre.MAYBE AS PRE_MAYBE,post.MAYBE AS POST_MAYBE
				FROM goingprepromo AS pre
				RIGHT JOIN goingpostpromo AS post USING (PROMOID,USERID,PARTYID)
				WHERE PROMOID='.$promoid.'
				  AND PARTYID IN ('.$partyidstr.')
			)'
			: '(	SELECT pre.USERID,pre.PARTYID,pre.MAYBE AS PRE_MAYBE,going.MAYBE AS POST_MAYBE
				FROM goingprepromo AS pre
				LEFT JOIN going USING (USERID,PARTYID)
				WHERE PROMOID='.$promoid.'
				  AND PARTYID IN ('.$partyidstr.')
				UNION
				SELECT going.USERID,going.PARTYID,pre.MAYBE AS PRE_MAYBE,going.MAYBE AS POST_MAYBE
				FROM going
				LEFT JOIN goingprepromo AS pre ON PROMOID='.$promoid.' AND pre.USERID=going.USERID AND pre.PARTYID=going.PARTYID
				WHERE going.PARTYID IN ('.$partyidstr.')
			)').' AS combined
			JOIN '.$table.' USING (USERID)
			WHERE PROMOID='.$promoid.'
			   AND (FLAGS&'.PROMO_TEASER_SHOWN.')
			GROUP BY PRE_MAYBE,POST_MAYBE'
		,
		0,null,DB_NON_ASSOC);
#		print_rr($changes,$qstr);
		if ($changes === false) {
			return;
		}
/*		$gained_doubters = 0;
		$gained_visitors = 0;
		$lost_doubters = 0;
		$lost_visitors = 0;
		$visitors_to_doubters = 0;
		$doubters_to_visitors = 0;

		foreach ($changes as $info) {
			list($seen,$post_maybe,$pre_maybe,$cnt) = $info;
			if ($post_maybe === $pre_maybe) {
				# no change
				continue;
			}
			if ($post_maybe  === null) {
				# no post
				if ($pre_maybe === null) {
					# nothing registered, cannot happen
				} elseif ($pre_maybe) {
					# pre doubter, lost doubter
					$lost_doubters += $cnt;
				} else {
					# pre visitor, lost visitor
					$lost_visitors += $cnt;
				}
			} elseif ($post_maybe) {
				# post doubter
				if ($pre_maybe === null) {
					# no pre, gained doubter
					$gained_doubters += $cnt;
				} elseif ($pre_maybe) {
					# pre doutber, no change
				} else {
					# pre visitor, visitor became doubter
					$visitors_to_doubters += $cnt;
					$gained_doubters += $cnt;
				}
			} else {
				# post visitor
				if ($pre_maybe === null) {
					# no pre, gained visitor
					$gained_visitors += $cnt;
				} elseif ($pre_maybe) {
					# pre doutber, doubter became visitor
					$doubters_to_visitors += $cnt;
					$gained_visitors += $cnt;
				} else {
					# pre doubter, no change
				}
			}
		}
		print_rr($gained_visitors,'gained_visitors');
		print_rr($gained_doubters,'gained_doubters');
		print_rr($doubters_to_visitors,'doubters_to_visitors');
		print_rr($visitors_to_doubters,'visitors_to_doubters');
		print_rr($lost_visitors,'lost_visitors');
		print_rr($lost_doubters,'lost_doubters');*/

		$maybes = array(0=>null);

		if ($promo['AFTERGOING'] === null
		||	$promo['BEFOREGOINGMAYBE'] != 0
		||	$promo['AFTERGOINGMAYBE'] != 0
		) {
			$maybes[1] = 'MAYBE';
		}

		$no_pre_post = !db_single('goingprepromo','SELECT 1 FROM goingprepromo WHERE PROMOID='.$promoid);
		//$promo['STARTSTAMP'] < 1141231245;

		layout_start_separator_reverse_row(3);
			layout_value_field(FIELDONLY);
			?><b><?= element_plural_name('number(amount)') ?></b><?
/*			?> <b><?= element_plural_name('visitor') ?> &amp; <?= element_plural_name('doubter') ?></b><?*/
			?> <?
			?><b><?= __($no_pre_post ? 'attrib:within_reach' : 'attrib:within_seen') ?></b><?

		foreach ($maybes as $maybe => $postfix) {
			$what = $maybe ? 'doubter' : 'visitor';
			$nowcnt = getifset($totals,$maybe,'now') ?: 0;
			layout_restart_reverse_row();
				# old promo's have no goingpre/postpromo, so use BEFORE or AFTERGOING instead, but only for reach, not seen
				echo ($beforecnt = $no_pre_post ? $promo['BEFOREGOING'.$postfix] : getifset($totals,$maybe,'pre')) ?: __('answer:none');
				layout_value_field();
				echo element_name($what,$beforecnt) ?> <? echo __('when:before_promo');
			if ($promo['AFTERGOING'.$postfix] !== null) {
				layout_restart_reverse_row();
				echo ($aftercnt = $no_pre_post ? $promo['AFTERGOING'.$postfix] : getifset($totals,$maybe,'post')) ?: __('answer:none');
				layout_value_field();
				echo element_name($what,$aftercnt) ?> <? echo __('when:after_promo');
			} else {
				$aftercnt = $nowcnt;
			}
			$diff[$maybe] = $aftercnt - $beforecnt;
			layout_restart_reverse_row();
			echo $nowcnt ?: __('answer:none');
			layout_value_field();
			echo element_name($what,$nowcnt) ?> <? echo __('date:now');
		}

		if ($changes) {
			$gained_visitors = isset($changes[null][0]) ? $changes[null][0] : 0;
			$gained_doubters = isset($changes[null][1]) ? $changes[null][1] : 0;
			$doubters_to_visitors = isset($changes[1][0]) ? $changes[1][0] : 0;
			$visitors_to_doubters = isset($changes[0][1]) ? $changes[0][1] : 0;
			$lost_visitors = isset($changes[0][null]) ? $changes[0][null] : 0;
			$lost_doubters = isset($changes[1][null]) ? $changes[1][null] : 0;

			$visitors_gained = $gained_visitors + $doubters_to_visitors;
			$visitors_lost = $lost_visitors + $visitors_to_doubters;

#			$doubters_gained = $gained_doubters + $visitors_to_doubters;
#			$doubters_lost = $lost_doubters + $doubters_to_visitors;

			$diff_visitors = $visitors_gained - $visitors_lost;
			$diff_doubters = $gained_doubters - $lost_doubters;

			foreach (array(
				array(true,			null,	null,	element_plural_name('change(external)')),

				array($visitors_gained,		false,	false,	element_name('gained_visitor',$visitors_gained)),
				array($gained_visitors,		false,	false,	element_name('new_visitor',$gained_visitors)),
				array($doubters_to_visitors,	false,	false,	element_name('converted_doubter',$doubters_to_visitors)),

				array(-$visitors_lost,		false,	false,	element_name('lost_visitor',$visitors_lost)),
				array(-$lost_visitors,		false,	false,	element_name('disappeared_visitor',$lost_visitors)),
				array(-$visitors_to_doubters,	false,	false,	element_name('visitor_now_doubting',$visitors_to_doubters)),

#				array(-$visitors_lost,		false,	true,	element_name('visitor',$visitors_lost)),
#				array(-$lost_visitors,		false,	false,	element_name('visitor_gone',$lost_visitors)),
#				array(-$visitors_to_doubters,	false,	false,	element_name('visitor_now_doubting',$visitors_to_doubters)),

				array($gained_doubters,		false,	false,	element_name('new_doubter',$gained_doubters)),
				array(-$lost_doubters,		false,	false,	element_name('disappeared_doubter',$lost_doubters)),

				array(true,			null,	null,	element_name('result')),

				array($diff_visitors,		null,	true,	element_name(($diff_visitors ? ($diff_visitors > 0 ? 'gained_' : 'lost_') : 'unchanged_').'visitor',$diff_visitors)),
				array($diff_doubters,		null,	true,	element_name(($diff_doubters ? ($diff_doubters > 0 ? 'gained_' : 'lost_') : 'unchanged_').'doubter',$diff_doubters)),

			) as $info) {
				[$change,$postfix,$gaindesc,$desc] = $info;
				if ($change === null) {
					continue;
				}
				if ($change === true) {
					layout_restart_separator_reverse_row(3);
					layout_value_field(FIELDONLY);
					?><b><?= $desc ?></b><?
					continue;
				}
				if ($postfix === false) {
					$postfix = '-nb';
				}
				layout_restart_reverse_row(0,!$change ? null : ($change > 0 ? 'notice'.$postfix : 'warning'.$postfix));
				echo abs($change) ?: ($gaindesc === false ? __('answer:none') : null);

				layout_value_field($change ? SEPARATOR_DOT : FIELDONLY);
				echo $desc;
#				if ($gaindesc) {
#					echo ' ',__(!$change ? 'attrib:unchanged' : ($change > 0 ? 'attrib:gained' : 'attrib:lost'));
#				}
			}
		} else {
 			layout_restart_separator_reverse_row(3);
			layout_value_field(FIELDONLY);
			?><b><?= element_name('result') ?></b><?

			foreach ($maybes as $maybe => $postfix) {
				$change = getifset($diff,$maybe);
 				$prefix = $change ? ($change > 0 ? 'gained_' : 'lost_') : 'unchanged_';
				$what = $maybe ? $prefix.'doubter' : $prefix.'visitor';
				layout_restart_reverse_row(0,!$change ? null : ($change > 0 ? 'notice' : 'warning'));
				echo abs($change) ?: null;

				layout_value_field($change ? SEPARATOR_DOT : FIELDONLY);
				echo element_name($what,abs($change)),' ';
#				echo !$change ? 'ongewijzigd' : ($change > 0 ? 'winst' : 'verlies');
				layout_stop_row();
			}
		}
	}
	layout_close_table();

	if ($have_lock === 0) {
		$have_lock = have_lock(LOCK_PROMO,$promo['PROMOID']);
	}

	if ($is_admin
	&&	!$promo['ACTIVE']
	&&	(	$have_lock
		||	$promo['SENT_STAMP']
		)
	) {
		?><div><?
		if ($promo['SENT_STAMP']) {
			?><i>Bereik gereserveerd!</i><?
		} else {
			?><a href="/promo/<?= $promo['PROMOID'];
			?>/calculate">Bereik <?
			if ($promo['RSTAMP']) {
				?>opnieuw <?
			}
			?>bepalen</a><?
		}
		?></div><?
	}
	if ($price
	&&	!empty($catstats)
	) {
		?><div id="class-info"<?
		if ($_REQUEST['ACTION'] != 'classinfo'
		&&	$_REQUEST['ACTION'] != 'calculate'
		) {
			?> class="hidden"<?
		}
		?>><?
		layout_open_box_header();
		echo Eelement_plural_name('class');
		layout_close_box_header();
		promo_show_classes($catstats,$price);
		if (have_admin('promo')) {
			require_once '_price.inc';
			layout_open_table(TABLE_FULL_WIDTH);
			foreach ($GLOBALS['__daystat'] as $day => $info) {
				layout_start_rrow();
				?>korting dag <? echo $day + 1;
				layout_next_cell(class: 'right');
				//echo round(floor(100*(100 - 100 * $info['CLSS'] / $info['BASE']))/100,2);
				echo get_price(floor(($info['BASE'] - $info['CLSS']) / 100));
				layout_stop_row();
			}
			layout_close_table();
		}
		?></div><?
	}
	return $sentinfo;
}
function promo_show_classes($catstats,$price) {
	require_once '_price.inc';
	layout_open_table(TABLE_FULL_WIDTH);
	layout_start_header_row();
	layout_header_cell(Eelement_name('day'));
	layout_header_cell(Eelement_name('discount'));
	layout_header_cell(Eelement_name('class'));
	layout_header_cell(Eelement_name('discount'));
	layout_header_cell_right(Eelement_name('reach'));
	layout_header_cell_right(Eelement_name('price'));
	layout_stop_header_row();
	$totalbaseprice = 0;
	foreach ($catstats as $catinfo) {
		if (!$catinfo['CLASS']) {
			continue;
		}
		// DAY
		layout_start_rrow();
		echo $catinfo['DAY']+1;

		// DAY DISCOUNT
		layout_next_cell();
		$daydiscount = $catinfo['DAY'] * $price['DAYDISCOUNT'];
		if ($daydiscount) {
			echo $daydiscount;
			?>%<?
		}
		// CLASS
		layout_next_cell();
		echo $catinfo['CLASS'];

		// CLASS DISCOUNT
		layout_next_cell();
		if (!$catinfo['CLASS']) {
			$catinfo['CLASS'] = 1;
		}
		$classdiscount = round(100 - 100 / $catinfo['CLASS']);
		if ($classdiscount) {
			echo $classdiscount;
			?>%<?
		}

		// REACH
		layout_next_cell(class: 'right');
		echo $catinfo['CNT'];

		// PRICE
		layout_next_cell(class: 'right');
		if (!$catinfo['CLASS']) {
			$catinfo['CLASS'] = 1;
		}

		$baseprice = $catinfo['CNT'] * (100 - $daydiscount) / 100 * $price['BASE'];
		$classprice = $baseprice / ($price['CLASSDIVIDER'] * $catinfo['CLASS']);

		echo get_price($classprice / 100);

		$totalbaseprice += $classprice;

		layout_stop_row();

		if (isset($daystat[$catinfo['DAY']])) {
			$daystat[$catinfo['DAY']]['BASE'] += $baseprice;
			$daystat[$catinfo['DAY']]['CLSS'] += $classprice;
		} else {
			$daystat[$catinfo['DAY']]['BASE'] = $baseprice;
			$daystat[$catinfo['DAY']]['CLSS'] = $classprice;
		}
	}
	layout_start_spanned_row_right(6);
	?>Totaal:&nbsp;<?
	echo get_price($totalbaseprice / 100);
	layout_stop_row();
	layout_close_table();
	$GLOBALS['__daystat'] = $daystat;
}
function promo_display_single() {
	if (!($promoid = require_idnumber($_REQUEST,'sID'))) {
		return;
	}
	layout_show_section_header();

	$promoid = $_REQUEST['sID'];
	$promo = db_single_assoc(
		array('promo','invoice'),'
		SELECT promo.*,STATUS
		FROM promo
		LEFT JOIN invoice USING (INVOICENR)
		WHERE PROMOID='.$promoid
	);
	if ($promo === false) {
		return;
	}
	if (!$promo) {
		_error(__('promo:error:nonexistent_LINE',array('ID'=>$promoid)));
		return;
	}
	$v = $promo['VERSION'];
	$is_admin = have_admin('promo');
	$is_relation = have_relation($promo['RELATIONID']);

	if (!$is_relation
	&&	!(require_once '_adlink.inc')
	&&	!verify_hash_link($promo)
	&&	!require_admin('promo')
	) {
		return;
	}
	$price = get_promopriceid($promo['PRICEID']);
	if (!$price) {
		_error('Geen prijsinformatie bekend!');
	}
	if ($promo['PROMOID'] == 300) {
		// FIXME: why???
		$promo['STOPSTAMP'] -= 2*24*3600;
	}

	$have_lock = ($is_admin || $is_relation && $promo['REQUEST']) ? obtain_lock(LOCK_PROMO,$promoid) : false;

	promo_menu($promo,$is_admin,$is_relation,$have_lock);

	require_once '_ticketlist.inc';
	show_connected_tickets();

	if ($promo['REMOVED']) {
		?><div class="light"><?
	}

	if ($is_admin) {
		_connect_display_form('promo',$promo['PROMOID']);
	}
	if ($promo['STARTSTAMP'] >= 1193349600
	&&	$promo['STARTSTAMP'] < 1193698800
	) {
		warning('Door een probleem bij de overgang naar het nieuwe systeem met twijfelaars, kunnen de aantallen <i>voor promo</i> niet geheel correct zijn.<br />'.
			'Onze excuses daarvoor.');
	}
	if ($promo['STOPSTAMP'] >= 1193349600
	&&	$promo['STOPSTAMP'] < 1193698800
	) {
		warning('Door een probleem bij de overgang naar het nieuwe systeem met twijfelaars, kunnen de aantallen <i>na promo</i> niet geheel correct zijn.<br />'.
			'Onze excuses daarvoor.');
	}
	if ($promo['ACTIVE']
	&&	$promo['AFTERGOING'] === null
	&&	$promo['STOPSTAMP'] < CURRENTSTAMP
	) {
		require_once '_promogoing.inc';
		error_log('not yet stopped promo '.$promo['PROMOID'],0);
		$cnts = stop_promo($promo['PROMOID']);
		if ($cnts) {
			$promo['AFTERGOING'] = $cnts['CERTAIN_CNT'];
			$promo['AFTERGOINGMAYBE'] = $cnts['MAYBE_CNT'];
		}
	}

	?><article itemscope itemtype="https://schema.org/Article"><?
	layout_open_box('promo');
	layout_box_header(Eelement_name('information'));

	$list = new deflist('deflist vtop fw');

	if ($invoicenr = $promo['INVOICENR']) {
		$list->add_row(Eelement_name('invoice_number'),
			$promo['STATUS'] !== null
		?	'<a href="/invoice/'.$invoicenr.'">'.$invoicenr.'</a>'
		:	$invoicenr
		);
	}
	if ($promo['TITLE'] != $promo['MESSAGETITLE']) {
		$list->add_row(Eelement_name('teaser_title'), escape_specials($promo['TITLE']));
		$list->add_row(Eelement_name('message_title'), escape_specials($promo['MESSAGETITLE']));
	} else {
		$list->add_row(Eelement_name('title'), escape_specials($promo['TITLE']));
	}
	if (($is_admin || $is_relation)
	&&	$promo['RELATIONID']
	) {
		$list->add_row(Eelement_name('relation'), '<b>'.get_element_link('relation',$promo['RELATIONID']).'</b>');
		$relation_members = db_same_hash('relationmember','SELECT USERID FROM relationmember WHERE RELATIONID='.$promo['RELATIONID']);
		$have_letters = db_boolean_hash('promo_letter_v2','SELECT USERID FROM promo_letter_v2 WHERE PROMOID='.$promoid);
		if ($relation_members && $have_letters) {
			foreach ($relation_members as $userid) {
				if (!isset($have_letters[$userid])) {
					$list->add_row(null,'<div class="warning">'.element_name('no_receiver').': '.get_element_link('user',$userid).'</div>');
				}
			}
		}
	}
	$days = round(($promo['STOPSTAMP']-$promo['STARTSTAMP'])/24/3600);
	$list->add_row(Eelement_name('publication'), _dateday_get($promo['STARTSTAMP']));

	if ($days > 1) {
		$list->add_row(Eelement_name('last_day'), _dateday_get($promo['STOPSTAMP']-1));
	}

	$list->add_row(Eelement_name('duration'), $days.' '.element_name('day',$days > 1));
	if ($promo['REALSTOPSTAMP']) {
		$list->add_row(__C('keep_till'), _date_get($promo['REALSTOPSTAMP']));
	}

	if ($v >= 2) {
		# BUDGET
		if ($promo['BUDGET']) {
			require_once '_price.inc';
			$list->add_row(Eelement_name('budget'), get_price($promo['BUDGET']/100));
		}
	}

	if ($promo['FLAGS'] & PROMO_UNIQUE_PART) {
		$list->add_row(Eelement_plural_name('unique_code'), get_promo_unique_code_count($promoid));
	}
	if ($polls = $promo['POLLS']) {
		$list->add_row(Eelement_plural_name('poll'), $polls);
	}

	if (!$promo['CMTS']) {
		$list->add_row(Eelement_plural_name('comment'), __('answer:no'));
	} else {
		$cmtcnt = memcached_single('promo_comment','SELECT COUNT(*) FROM promo_comment WHERE ACCEPTED=1 AND ID='.$promo['PROMOID']);
		if ($cmtcnt) {
			$link = '/promo/'.$promoid.'/letter#cmts';
			ob_start();
			?><a href="<?= $link ?>"><?= Eelement_plural_name('comment',$cmtcnt) ?></a><?
			$field = ob_get_clean();
			ob_start();
			?><a href="<?= $link ?>"><?= $cmtcnt ?></a><?
			$list->add_row($field,ob_get_clean());
		} else {
			$list->add_row(Eelement_plural_name('comment'), __('status:allowed'));
		}
	}

	$list->add_row(Eelement_name('status'), __($promo['ACTIVE'] ? 'status:active' : 'status:inactive'));

	if ($price['CRITERIA']) {
		$list->add_row(Eelement_plural_name('criterium'), db_single('promo_focus','SELECT COUNT(DISTINCT TYPE,GROUPID) FROM promo_focus WHERE PROMOID='.$promo['PROMOID']));
	}
	require_once '_voucher.inc';
	show_voucher_row($list,$promo);



	if ($promo['DISCOUNT']) {
		$list->add_row(Eelement_name('discount'),$promo['DISCOUNT'].'%');
/*		if ($promo['BUDGET']) {
			$list->add_row(Eelement_name('increased_reach'),'+'.(
				$promo['DISCOUNT'] * $promo['BUDGET'] / $price['SEEN'] / 100)
			);
		}*/
	}
	$list->display();

	$connects = promo_get_connects($promoid);

	if (/*	have_admin('promo')
	&&*/	CURRENTSTAMP > $promo['STARTSTAMP']
	&&	$promo['STARTSTAMP'] >	1333118432
		# ^^^ started counting these
	&&	$connects
	&&	$promo['REACH']
	) {
/*		?><fieldset><legend>iedereen</legend><?
		?><img src="/images/diagram/promotoviews/<?= $promoid ?>.png" width="400" height="202" /><?
		?></fieldset><?*/
/*		?><fieldset><legend>ontvangers</legend><?
		?><img src="/images/diagram/promotoviews/<?= $promoid ?>.png?HAVE" width="400" height="202" /><?
		?></fieldset><?*/

		$days = ceil(($promo['STOPSTAMP'] - $promo['STARTSTAMP'])/2);

		?><fieldset class="center"><legend><?= element_plural_name('viewed_event_pages')
			?><br /><?= element_plural_name('receiver') ?> &amp; <?= __('promo:stat:seen') ?></legend><?
		?><img src="/images/diagram/promotoviews/<?= $promoid ?>.png?SEEN;BLOCK=5;WIDTH=<? echo $w = 5 * $days;
		if (isset($_REQUEST['PROMOHASH'])) {
			?>;PROMOHASH=<?= urlencode($_REQUEST['PROMOHASH']);
		}
		?>" width="<?= $w ?>" height="202" style="max-width:100%"><?
		?></fieldset><?
	}

	layout_continue_box();
	layout_open_box_header();
	echo Eelement_plural_name('statistic');
	if ($promo['PRICEID'] >= 1126340776
	&&	$promo['RSTAMP']
	&&	$_REQUEST['ACTION'] != 'classinfo'
	&&	$_REQUEST['ACTION'] != 'calculate'
	&&	$v < 2
	) {
		layout_continue_box_header();
		?><a onclick="swapdisplay('class-info');return false;" href="/promo/<?= $promoid;
		?>/classinfo"><?= element_plural_name('class') ?></a><?
	}
	layout_close_box_header();

	$sentinfo = promo_show_stats($promo, $is_admin, $price, $have_lock);

	if (have_admin('promo')) {
		layout_display_alteration_note($promo, SHOW_ALL_ALTER);
	}
	layout_close_box();

	layout_open_box('promo');
	layout_open_box_header();
	if ($is_admin
	&&	$have_lock
	) {
		?><a href="/promo/<?= $promoid 	?>/focus"><?= Eelement_plural_name('criterium') ?></a><?
	} else {
		echo Eelement_plural_name('criterium');
	}
	layout_close_box_header();
	promo_focus_overview($promo['PROMOID'],0,'fw');
	layout_continue_box();

	if ($price
	&&	!empty($sentinfo['TOTAL_CNT'])
	) {
		layout_open_box_header();
		echo Eelement_name('price');
/*		if (isset($price)
		&&	$_REQUEST['ACTION'] != 'priceinfo'
		) {
			layout_continue_box_header();
			?><a onclick="swapdisplay('priceinfo');location.hash='#priceinfo';return false" href="/promo/<?= $promoid;
			?>/priceinfo#priceinfo"><?= element_name('cost_indication') ?></a><?
		}*/
		layout_close_box_header();

		[$minprice,$maxprice] = get_promoprice($promo,$sentinfo['TOTAL_CNT'],$price);

		if ($promo['ESTIM']) {
			# ESTIM is newer
			$promo['ESTIMSEEN'] = $promo['ESTIM'];
			$promo['ESTIMREAD'] = 0;
		}

		$estimprice = $minprice + ($sentinfo['TOTAL_CNT'] * $price['BASE'] + $promo['ESTIMSEEN'] * $price['SEEN'] + $promo['ESTIMREAD'] * $price['RREAD']) / 100;

		$neverless = $promo['DISCOUNT'] == 100 ? 0 : $price['MINIMUMPRICE'];

		$list = new deflist('deflist vtop fw');

		$list->add_row(Eelement_name('minimum_price'), promoprice_get($minprice,$neverless));

		$add_prices = function($maxprice,$estimprice = null,$discount = 0) use ($list,$promo,$sentinfo,$price,$neverless) {
			if ($have_estim
			=	$estimprice !== null
			&&	(	$promo['VERSION'] == 1
				||	have_admin('promo')
				)
			&&	(	$promo['ESTIM']
				||	$promo['ESTIMSEEN']
				||	$promo['ESTIMREAD']
				)
			) {
				if ($promo['VERSION'] == 2
				&&	!$promo['ESTIM']
				) {
					$list->set_field_class('light');
				}
				if ($discount) {
					$estimprice *= (100 - $discount) / 100;
				} elseif (
					$promo['ESTIM']
				&&	$promo['BUDGET']
				) {
					$list->set_value_class($promo['ESTIM'] ? ($promo['BUDGET'] > $estimprice * 100 ? ' warning' : ' notice') : null);
				}
				$list->add_row(
					Eelement_name('estimated_price'),
					promoprice_get(min($estimprice,$maxprice),$neverless)
				);
			}
			if ($discount) {
				$maxprice *= (100 - $discount) / 100;
			} else {
			$list->set_value_class($maxprice < $promo['BUDGET'] / 100 ? 'warning' : 'notice');
			}
			$list->add_row(
				Eelement_name('maximum_price'),
				promoprice_get($maxprice,$neverless)
			);
		};

		$add_prices($maxprice,$estimprice);

		$currentprice = Eelement_name(
				CURRENTSTAMP >= $promo['STOPSTAMP']
			?	'final_price'
			:	'price_at_this_moment'
		);

		if ($promo['SENT_STAMP']
		&&	CURRENTSTAMP >= $promo['STARTSTAMP']
		) {
			if ($price['SEEN']) {
				$actualprice = $minprice + $sentinfo['SCAN_CNT'] * $price['SEEN'] / 100;

				$list->add_row(
					$currentprice,
					promoprice_get($actualprice,$neverless)
				);
			} else {
				$actualprice = $minprice;
			}
		}
		if ($promo['BUDGET'] && $maxprice > $promo['BUDGET'] / 100) {

			$maxprice = $promo['BUDGET'] / 100;
			if ($estimprice > $maxprice) {
				$estimprice = $maxprice;
			}

			$list->set_field_class('inforow');
			$list->add_row(__C('field:within_budget'));

			if (isset($actualprice)) {
				$list->add_row(
					$currentprice,
					promoprice_get($actualprice,$neverless,$promo['BUDGET'] / 100)
				);
			}

			$list->add_row(
				Eelement_name('maximum_price'),
				promoprice_get($maxprice,$neverless,$promo['BUDGET']/100)
			);
		}
		if ($discount = $promo['DISCOUNT']) {
#			$estimprice *= (100 - $discount) / 100;
			if (isset($actualprice)) {
				$actualprice *= (100 - $discount) / 100;
			}
#			$maxprice *= (100 - $discount) / 100;

			$list->set_field_class('inforow');
			$list->add_row($promo['DISCOUNT'].'% '.element_name('discount'));

			$add_prices($maxprice,$estimprice,$discount);

			if (isset($actualprice)) {
				$list->add_row(
					$currentprice,
					promoprice_get($actualprice,$neverless)
				);
				if ($promoid < 14487
				&&	$is_admin
				) {
					$list->set_row_class('light warning');
					$list->add_row(
						$currentprice,
						promoprice_get($actualprice * (100-$promo['DISCOUNT'])/100,$neverless).
							' (foutief dubbele korting)'
					);
				}
			}
		}
		$list->display();

		if ($promo['ACTIVE']
		&&	$promo['STOPSTAMP'] <= CURRENTSTAMP
		) {
			require_once '_paystatus.inc';
			show_paystatus($promo);
		}
	}
	layout_close_box();

	_promo_display_teaser($promo,$connects,null,$is_admin);
	_promo_display($promo,false,$is_admin);

	if ($promo['REMOVED']) {
		?></div><?
	}
	?></article><?
}
function promo_display_form() {
	require_once '_discounts.inc';
	layout_show_section_header();

	if (!require_admin('promo')) {
		return;
	}
	if ($promoid = $_REQUEST['sID']) {
		$promo = db_single_assoc('promo','SELECT * FROM promo WHERE PROMOID='.$promoid);
		if ($promo === false) {
			return;
		}
		if (!$promo) {
			not_found(); return;
		}
		if (!require_last_lock_and_freshen(LOCK_PROMO,$promoid)) {
			return;
		}
		$v = $promo['VERSION'];
	} else {
		$promo = null;
		$v = 2;
		$lastpromoid = db_single('promo','SELECT MAX(PROMOID) FROM promo',DB_USE_MASTER);
		if ($lastpromoid == 65535) {
			register_error('cannot add more!');
			return;
		}
	}

	$running = !isset($promo)
	?	false
	:	$promo['ACTIVE']
	&&	CURRENTSTAMP-30*60 >= $promo['STARTSTAMP']
	&&	CURRENTSTAMP < $promo['STOPSTAMP'];

	?><form onsubmit="return submitForm(this);" method="post"action="/promo<?
	if (isset($promo)) {
		?>/<? echo $promoid;
	}
	?>/commit"><?

	ticket_passthrough();

	$color = isset($promo) ? $promo['COLOR'] : get_color('random',CLR_INCLUDE_WHITE);
	$flags = isset($promo) ? $promo['FLAGS'] : 0;

	layout_open_box('grey','promobox');
	layout_open_table('fw');
	layout_box_header(Eelement_name('general'));
	layout_start_row();
		echo Eelement_name('relation');
		layout_field_value();
		discount_relation_select(isset($promo) ? $promo['RELATIONID'] : 0);
	layout_stop_row();

		if (!$running
		||	$v >= 2
		) {
			require_once '_duration.inc';
			show_last_day_duration_form_part($promo);
		} else {
			layout_restart_row();
			echo Eelement_name('publication');
			layout_field_value();
			?>Promo loopt, kan niet aangepast worden!<?
			layout_stop_row();
		}

	if ($v >= 2) {
		layout_start_row();
		echo Eelement_name('budget');
		layout_field_value();
		show_input([
			'type'		=> 'number',
#			'min'		=> !empty($promo['TARGETPRICE']) ? ceil($promo['TARGETPRICE'] / 10000) : 0,
			'min'		=> 0,
			'class'		=> 'right three_digits',
			'name'		=> 'BUDGET',
			'value'		=> !empty($promo['BUDGET']) ? $promo['BUDGET'] / 10000 : null,
		]);
		?> euro<?
	}

	layout_restart_row();
		echo Eelement_plural_name('poll');
		layout_field_value();
		show_input([
			'type'	=> 'number',
			'min'	=> 0,
			'max'	=> 100,
			'class'	=> 'right three_digits',
			'name'	=> 'POLLS',
			'value'	=> getifset($promo,'POLLS') ?: null
		]);

	layout_restart_row();
		?><label for="cmts"><?= Eelement_plural_name('comment'); ?></label><?
		layout_field_value();
		?><input<?
		if (isset($promo)
		&&	$promo['CMTS']
		) {
			?> checked="checked"<?
		}
		?> type="checkbox" value="1" id="cmts" name="CMTS"><?
	layout_restart_row();
		echo Eelement_name('color');
		layout_field_value();
		color_display_select($color,CLR_INCLUDE_GREY);

	layout_restart_row();
		echo Eelement_name('clickable');
		layout_field_value();
		require_once '_clickbox.inc';
		show_clickbox_select($promo);

	layout_restart_row(($checked = $flags & PROMO_UNIQUE_PART) ? ROW_BOLD_HILITED : 0);
		?><label for="unique-codes"><?= Eelement_plural_name('unique_code'); ?></label><?
		layout_field_value();
		?><input<?
		if ($checked) {
			?> checked="checked"<?
		}
		?> onclick="cbclickpp(this)" type="checkbox" name="FLAGS[]" value="<?= PROMO_UNIQUE_PART; ?>"><?

	layout_restart_row();
		echo Eelement_name('invoice_number');
		layout_field_value();
		show_input(array(
			'type'	=> 'number',
			'class'	=> 'id right',
			'min'	=> 0,
			'name'	=> 'INVOICENR',
			'value'	=> $promo
		));
	layout_stop_row();

	require_once '_voucher.inc';
	show_voucher_form_row($promo);

	show_discount_form_row($promo);

	layout_close_table();
	layout_close_box();

	#### TEASER

	layout_open_box('grey');
	layout_box_header(Eelement_name('teaser'));
	layout_open_table('fw');
	layout_start_row();
		echo Eelement_name('title');
		layout_field_value('fw');
		?><input required="required" type="text" name="TITLE" value="<? if (isset($promo)) echo escape_specials($promo['TITLE']); ?>" /> <?
	layout_restart_rrow();
		layout_next_cell();
		?><label><input type="checkbox" name="FLAGS[]" value="<?= PROMO_TEASER_CENTER_TITLE ?>"<?
		if ($flags & PROMO_TEASER_CENTER_TITLE) {
			?> checked="checked"<?
		}
		?>> <?= __('action:center') ?></label><?
	layout_restart_row();
		echo Eelement_name('content');
		layout_field_value();
		?><textarea class="growToFit" name="TEASER" rows="10" cols="50"><? if (isset($promo)) echo escape_specials($promo['TEASER']) ?></textarea><?
	layout_restart_row();
		echo Eelement_name('layout');
		layout_field_value();
		show_promo_layout_selection_teaser($promo);
	layout_close_table();
	layout_close_box();

	#### MESSAGE

	layout_open_box('grey');
	layout_box_header(Eelement_name('real_message'));
	layout_open_table('fw');
	layout_start_row();
		echo Eelement_name('title');
		layout_field_value();
		?><input onclick="setdisplay('identical',this.checked);setdisplay('different',!this.checked)" type="checkbox" name="IDEM" value="1"<?
		if ($idem = (!isset($promo) || $promo['TITLE'] == $promo['MESSAGETITLE'])) {
			?> checked="checked"<?
		}
		?>> <span id="identical"<?
		if (!$idem) {
			?> class="hidden"<?
		}
		?>><?= __('attrib:identical') ?></span><?
		?><input<?
		if ($idem) {
			?> class="hidden"<?
		}
		?> id="different" type="text" name="MESSAGETITLE" value="<? if (isset($promo)) echo escape_specials($promo['MESSAGETITLE']); ?>"> <?
		?><label><input type="checkbox" name="FLAGS[]" value="<?= PROMO_MESSAGE_CENTER_TITLE ?>"<?
		if ($flags & PROMO_MESSAGE_CENTER_TITLE) {
			?> checked="checked"<?
		}
		?>> <?= __('action:center') ?></label><?
	layout_restart_row();
		echo Eelement_name('content');
		layout_field_value();
		?><textarea class="growToFit" name="BODY" rows="15" cols="70"><? if (isset($promo)) echo escape_specials($promo['BODY']);
		?></textarea><?
	layout_restart_row();
		echo Eelement_name('layout');
		layout_field_value();
		show_promo_layout_selection_message($promo);
	layout_stop_row();
	layout_close_table();
	layout_close_box();

	?><div class="block"><input type="submit" value="<?= __(isset($promo) ? 'action:change' : 'action:add') ?>" /></div></form><?
}
function promo_commit() {
	require_once '_namefix.inc';
	require_once '_promoreach.inc';
	if(	!require_something_trim($_POST,'TITLE')
	||	!require_anything_trim($_POST,'BODY')
	||	!require_anything_trim($_POST,'TEASER')
	||	!require_anything_trim($_POST,'MESSAGETITLE')
	||	!require_something($_POST,'COLOR')
	||	false === require_number($_POST,'DISCOUNT')
	||	!optional_number($_POST, 'RELATIONID')
	||	!require_number_or_empty($_POST,'INVOICENR')
	||	false === require_number($_POST,'CLICKBOX')
	||	false === require_number($_POST,'POLLS')
	||	!require_admin('promo')
	||	!optional_number($_REQUEST,'BUDGET')
	||	!optional_number($_REQUEST,'TICKETID')
	||	false === require_number($_POST,'TEASER_DESIGN')
	) {
		return;
	}
	$oldpromo = null;
	if ($promoid = have_idnumber($_REQUEST,'sID')) {
		$oldpromo = db_single_assoc('promo','SELECT * FROM promo WHERE PROMOID='.$promoid,DB_USE_MASTER);
		if ($oldpromo === false) {
			return;
		}
		if (!$oldpromo) {
			register_error('promo:error:nonexistent_LINE',array('ID'=>$promoid));
			return;
		}
		$running = $oldpromo['ACTIVE']
			&& CURRENTSTAMP-30*60 >= $oldpromo['STARTSTAMP']
			&& CURRENTSTAMP < $oldpromo['STOPSTAMP'];

		$v = $oldpromo['VERSION'];
	} else {
		$running = false;
		$v = 2;
	}
	if (!$running || $v >= 2) {
		require_once '_duration.inc';
		$info = process_duration_form_parts();
		if (!$info) {
			return;
		}
		[$startstamp,$stopstamp] = $info;
		$setlist[] = 'STARTSTAMP='.$startstamp;
		$setlist[] = 'STOPSTAMP='. $stopstamp;
	}
	$_POST['TITLE'] = get_fixed_name($_POST['TITLE']);
	$_POST['TITLE'] = get_fixed_name($_POST['MESSAGETITLE']);
	if ($v >= 2) {
		$min_price = db_single('promoprice','SELECT MINIMUMPRICE FROM promoprice'.($promoid ? ' WHERE PRICEID='.$oldpromo['PRICEID'] : ' WHERE PRICEID<'.CURRENTSTAMP.' ORDER BY PRICEID DESC LIMIT 1'));
		if ($min_price === false) {
			return;
		}
		$budget = have_idnumber($_POST,'BUDGET') ? $_POST['BUDGET'] * 10000 : 0;
		if ($budget
		&&	$budget < $min_price * 100
		) {
			$budget = $min_price * 100;
		}
		$setlist[] = 'BUDGET='.$budget;
		if (!$promoid) {
			$setlist[] = 'TARGETPRICE=0';
		}
	}
	$setlist[] = 'TITLE="'.		addslashes(_ubb_preprocess($_POST['TITLE'])).'"';
	$setlist[] = 'MESSAGETITLE="'.	addslashes(_ubb_preprocess(isset($_POST['IDEM']) ? $_POST['TITLE'] : $_POST['MESSAGETITLE'])).'"';
	$setlist[] = 'BODY="'.		addslashes($body = _ubb_preprocess(get_fixed_name($_POST['BODY']))).'"';
	$setlist[] = 'TEASER="'.	addslashes($teaser = _ubb_preprocess(get_fixed_name($_POST['TEASER']))).'"';
	$setlist[] = 'COLOR="'.		addslashes($_POST['COLOR']).'"';
	$setlist[] = 'TEASER_DESIGN='.	($_POST['TEASER_DESIGN'] > PROMO_TEASER_LAST ? PROMO_TEASER_DEFAULT : $_POST['TEASER_DESIGN']);
	$setlist[] = 'DISCOUNT='.	$_POST['DISCOUNT'];
	$setlist[] = 'RELATIONID='.	$_POST['RELATIONID'];
	$setlist[] = 'INVOICENR='.	($_POST['INVOICENR'] == '' ? 'NULL' : $_POST['INVOICENR']);
	$setlist[] = 'CLICKBOX='.	$_POST['CLICKBOX'];
#	$setlist[] = 'REQUEST=0';
	$setlist[] = 'CMTS='.		(isset($_POST['CMTS']) ? 1 : 0);
	$setlist[] = 'POLLS='.		$_POST['POLLS'];
	if (isset($_POST['TICKETID'])) {
		$setlist[] = 'TICKETID='.$_POST['TICKETID'];
	}
	if (isset($_POST['VOUCHER'])) {
		require_once '_voucher.inc';
		if (false === ($voucherid = optional_voucherid($oldpromo))) {
			return false;
		}
		$setlist[] = 'VOUCHERID='.$voucherid;
	}
	$flags = 0;
	if (isset($_POST['FLAGS'])) {
		if (!require_number_array($_POST,'FLAGS')) {
			return;
		}
		foreach ($_POST['FLAGS'] as $flag) {
			$flags |= $flag;
		}
	}
	$setlist[] = 'FLAGS='.$flags;
	if ($promoid) {
		if ($v >= 2) {
			if (CURRENTSTAMP < $stopstamp
			&&	$oldpromo['STOPSTAMP'] != $stopstamp
			) {
				$setlist[] = 'AFTERGOING=NULL';
				$setlist[] = 'AFTERGOINGMAYBE=NULL';

				if (!db_delete('goingpostpromo','
					DELETE FROM goingpostpromo
					WHERE PROMOID='.$promoid)
				) {
					return;
				}
			}
			if (($oldpromo['STOPSTAMP'] - $oldpromo['STARTSTAMP'])
			!=	($stopstamp - $startstamp)
			) {
				$setlist[] = 'ESTIM='.estimate_seen($promoid,$startstamp,$stopstamp);
			}
		} else {
			if (!$running
			&&	(	$oldpromo['STARTSTAMP'] != $startstamp
				||	$oldpromo['STOPSTAMP'] != $stopstamp
				)
			) {
				// current letters are invalid
				remove_reach($promoid);
				$setlist[] = 'RSTAMP=0,SENT_STAMP=0,REACH=0';
				_notice('Bereik verwijderd omdat datum of looptijd gewijzigd is.');
			}
		}
		$setlist[] = 'MSTAMP='.CURRENTSTAMP;
		$setlist[] = 'MUSERID='.CURRENTUSERID;
		if (!db_insert('promo_log','
			INSERT INTO promo_log
			SELECT * FROM promo
			WHERE PROMOID='.$promoid)
		||	!db_update('promo','
			UPDATE promo SET '.implode(', ',$setlist).'
			WHERE PROMOID='.$promoid)
		) {
			return;
		}
		register_notice('promo:notice:changed_LINE');
	} else {
		$setlist[] = 'VERSION=2';
		$setlist[] = 'CSTAMP='.CURRENTSTAMP;
		$setlist[] = 'USERID='.CURRENTUSERID;
		$setlist[] = 'PRICEID=(SELECT MAX(PRICEID) FROM promoprice WHERE PRICEID<='.CURRENTSTAMP.')';

		if (!db_insert('promo','INSERT INTO promo SET '.implode(', ',$setlist))) {
			return;
		}
		$_REQUEST['sID'] = $promoid = db_insert_id();
		register_notice('promo:notice:added_LINE');
		ticket_update('promo',$promoid);
	}
	taint_include_cache('promo',$promoid,$body,$teaser);
}
function get_promo_unique_code_count($promoid) {
	static $__count;
	if (isset($__count[$promoid])) {
		return $__count[$promoid];
	}
	return $__count[$promoid] = db_single('promo_letter_unique','SELECT COUNT(DISTINCT USERID) FROM promo_letter_unique WHERE PROMOID='.$promoid);
}
function show_promo_layout_selection_teaser($promo) {
	$selected = ' selected="selected"';
	$design = $promo ? $promo['TEASER_DESIGN'] : PROMO_TEASER_NORMAL_V1;
	?><select name="TEASER_DESIGN"><?
		?><option<? if (!$design) echo $selected ?> value="0"><?= __('promo:layout:normal') ?> v1</option><?
		?><option<? if ($design == 3) echo $selected ?> value="3"><?= __('promo:layout:normal') ?> v2</option><?
		?><option<? if ($design == 1) echo $selected ?> value="1"><?= __('promo:layout:two_columns') ?></option><?
		?><option<? if ($design == 2) echo $selected ?> value="2"><?= __('promo:layout:fade_behind') ?></option><?
	?></select><?
}
function show_promo_layout_selection_message($promo) {
	$selected = ' selected="selected"';
	$flags = $promo['FLAGS'] ?? 0;
	$side = PROMO_MESSAGE_INFOBOX_SIDE;
	$fade = PROMO_MESSAGE_INFOBOX_FADE;
	?><select name="FLAGS[]"><?
		?><option value="0"><?= __('promo:layout:normal') ?></option><?
		?><option<? if ($flags & $side) echo $selected ?> value="<?= $side ?>"><?= __('promo:layout:two_columns') ?></option><?
		?><option<? if ($flags & $fade) echo $selected ?> value="<?= $fade ?>"><?= __('promo:layout:fade_behind') ?></option><?
	?></select><?
}
function estimate_seen($promoid,$startstamp,$stopstamp,$useridstr = null) {

	$period = $stopstamp - $startstamp;

	if (!$useridstr) {
		$useridstr = db_single('promo_letter_v2','
			SELECT GROUP_CONCAT(USERID)
			FROM promo_letter_v2
			WHERE PROMOID='.$promoid,
			DB_FORCE_MASTER
		) ?:	db_single('promo_letter_concept','
			SELECT GROUP_CONCAT(USERID)
			FROM promo_letter_concept
			WHERE PROMOID='.$promoid,
			DB_FORCE_MASTER
		);
		if (!$useridstr) {
			return 0;
		}
	}

	$start_promoid = memcached_single('promo','
		SELECT MIN(PROMOID)
		FROM promo
		WHERE CSTAMP>='.(TODAYSTAMP - ONE_YEAR),
		TEN_MINUTES
	);

	if (!$start_promoid) {
		return 0;
	}

	$avg_conv = memcached_single('promo_letter_v2','
		SELECT AVG(PCT)
		FROM (	SELECT	COUNT(IF(FLAGS & '.PROMO_TEASER_SHOWN.',1,NULL))
			/	COUNT(IF(FLAGS & '.PROMO_COUNTER_SHOWN.',1,NULL))
				AS PCT
			FROM promo_letter_v2
			WHERE PROMOID>='.$start_promoid.'
			GROUP BY PROMOID
		) AS pcts',
		TEN_MINUTES
	);

	return $avg_conv * db_single('user_data','
		SELECT COUNT(*)
		FROM user_data
		WHERE USERID IN ('.$useridstr.')
		  AND LAST_USED>'.(CURRENTSTAMP - $period)
	);
}
