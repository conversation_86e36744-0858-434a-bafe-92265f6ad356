<?php

class homenews {
	private array|false		$newslist;
	private array			$runninglist	= [];
	private int				$showlistsize	= 0;
	private int				$shown			= 0;
	private bool			$double;
	private array			$running		= [];
	private array			$connects;
	private array			$stamps			= [];
	private array			$commentcnt		= [];
	private array			$contests;
	private favouriteinfo	$favinfo;
	private int				$min_pstamp		= CURRENTSTAMP;

	public function __construct() {
		require_once '_newsad.inc';
		$this->double =
			!SMALL_SCREEN
		&&	!probably_small_screen()
		&&	!(	(require_once '_resolution.inc')
			&&	($res = get_resolution())
			&&	$res[1] <= 1024
			);

		[$prefer, $max] =
			$this->double
		?	[PREFER_DOUBLE_NEWSADS, MAX_DOUBLE_NEWSADS]
		:	[PREFER_SINGLE_NEWSADS, MAX_SINGLE_NEWSADS];

		$this->newslist = memcached_rowuse_array_if_not_admin('news', 'news', '
			SELECT DISTINCT NEWSID, TITLE, SUB<PERSON><PERSON><PERSON>, ACCEPTED, PS<PERSON>MP, TYPE
			FROM news
			'.join_only_news_for_current_country().'
			WHERE PSTAMP < '.strtotime('+1 day', TODAYSTAMP).'
			ORDER BY PSTAMP DESC
			LIMIT 50',
			FIVE_MINUTES
		);

		if ($this->newslist === false) {
			return;
		}

		$ids = [];

		foreach ($this->newslist as $news) {
			if ($news['PSTAMP'] < $this->min_pstamp) {
				$this->min_pstamp = $news['PSTAMP'];
			}
			$ids[] = $news['NEWSID'];
		}

		if ($this->running = newsad_obtain($prefer, $max, $this->double)) {
			$this->running = shuffle_assoc($this->running);
			$this->runninglist = $this->fill_teasers($this->running, $ids, false);
		}

		if (empty($ids)) {
			return;
		}
		sort($ids);
		$idstr = implode(',', $ids);

		require_once '_contests.inc';
		$this->contests = get_contests('news',$idstr);

		if (false === ($connects = memcached_multirow_hash('connect', '
			SELECT MAINID, ASSOCTYPE, ASSOCID
			FROM connect
			WHERE MAINTYPE = "news"
			  AND MAINID IN ('.$idstr.')',
			TEN_MINUTES
		))) {
			return;
		}
		$this->connects = $connects;
		$partyids = [];
		foreach ($this->connects as /* $newsid => */ $assoc_types) {
			if (isset($assoc_types['party'])) {
				$partyids = [...$partyids, ...$assoc_types['party']];
			}
		}
		if ($partyids) {
			sort($partyids);
			$partyidstr = implode(',', $partyids);

			if (false !== ($stamps = memcached_simple_hash('party', '
				SELECT PARTYID,STAMP
				FROM party
				WHERE PARTYID IN ('.$partyidstr.')',
				TEN_MINUTES
			))) {
				$this->stamps = $stamps;
			}
			if (have_user()) {
				require_once '_favourite.inc';
				$this->favinfo = new favouriteinfo($partyidstr);
			}
		}
		if (!ROBOT) {
			if (false !== ($commentcnt = memcached_simple_hash('news_comment','
				SELECT ID, COUNT(*)
				FROM news_comment
				WHERE ID IN ('.$idstr.')
				  AND ACCEPTED = 1
				GROUP BY ID',
				FIVE_MINUTES
			))) {
				$this->commentcnt = $commentcnt;
			}
		}
	}
	final public function get_shown(): int {
		return $this->shown;
	}
	final public function double(): bool {
		return $this->double;
	}
	final public function show_teasers(): bool {
		return $this->show_actual_teasers($this->runninglist);
	}
	private function show_actual_teasers(array $list): bool {
		require_once '_ubb.inc';
		$admin = have_admin('news');
		$forceid = have_idnumber($_REQUEST,'NEWSADID') ? $_REQUEST['NEWSADID'] : 0;
		require_once '_uploadimage.inc';
		require_once '_jumper.inc';
		$teasercnt = 0;
		global $subbar;
		$upimg_flags = UPIMG_NOCHANGE | UPIMG_NO_OPENGRAPH;
		$incids = [];
		$only_one = count($list) === 1;

		foreach ($list as $news) {
			$is_interview = !empty($news['INTERVIEWID']);

			if (!$is_interview) {
				$newsid   = $news['NEWSID'];
				$newsadid = $news['NEWSADID'] ?? null;

				if (!$admin
				&&	!$news['ACCEPTED']
				&&	!isset($news['FORCED'])
				) {
					continue;
				}
				++$this->shown;

				if ($newsadid) {
					$try = $news['TRY'];
					$free = ROBOT || have_admin();
					$shown[$newsadid] = $newsadid;
					$hard_match = false;
					if (!empty($this->running[$newsadid]['MUST'])) {
						# count these
						if (!$forceid
						||	 $forceid !== $newsadid
						) {
							if (!empty($news['KEEPTOP'])) {
								$keepids[] = $newsadid;
							} else {
								$updids[$try][] = $newsadid;
							}
							if ($hard_match = ($this->running[$newsadid]['FILTER_MATCH'] ?? null) === 'hard') {
								$hardids[$newsadid] = $newsadid;
							}
						}
					}
					require_once '_adimp.inc';
					if (false === ($incid = create_adimp(
						$newsadid,
						$newsadid,
						free: $free,
						try: $try,
						mark_served: true,
						hard_match: $hard_match))
					) {
						return false;
					}
				} else {
					$incid = null;
				}

				$teaserad = $newsadid && $news['TYPE'] === 'teaserad';
				$ad		  = $newsadid && $news['TYPE'] === 'ad';

				$show_ad_header =
					($ad || $teaserad)
				&&	!$hide_ad_text
				?	($newsid === 34290 ? 'Survey' : _make_illegible(element_name('ad')))
				:	false;
				if ($show_ad_header && !$this->double) {
					?><div class="center blackbox"><?= $show_ad_header ?></div><?
				}
				if (!$newsadid) {
					$jump = get_element_href('news', $newsid);
				} elseif ($forceid && !$teaserad) {
					$jump = get_hashlink($news, 'news');
				} else {
					$jump = jump_to('newsad', $newsadid, incid: $incid);
				}
			} else {
				$show_ad_header = false;
				$jump = get_element_href('interview',$news['INTERVIEWID']);
				$date_str = _date_get($news['PSTAMP']);
			}
			$classstr = null;
			if (!$news['ACCEPTED']) {
				$classstr .= 'unaccepted ';
			}
			if ($clickbox = !$newsadid || $news['CLICKBOX']) {
				$classstr .= 'ptr ';
			}

			$onclick = /** @lang JavaScript */ "addclass(this, 'light'); openLink(event, '$jump', false, true)";

			if ($this->double) {
				if (!($teasercnt & 1)) {
					?><table class="vtop front-teasers nomargin"><tr><?
				}
				if ($only_one) {
					# Hack to force single newsad to the middle
					?><td style="width: 25%;"></td><?
				}

				?><td<?
				?> class="<?= $classstr,($is_interview ? 'interview' : 'news') ?> hlbox box front-teaser"<?
				?> onclick="<?= $onclick ?>"<?
				?>><?
				if ($show_ad_header) {
					?><div class="center udv blackbox"><?= $show_ad_header ?></div><?
				}
			} else {
				box::open($classstr.'hlbox '.($is_interview ? 'interview' : 'news'));
				if ($clickbox) {
					box::onclick($onclick);
				}
			}
			if (!$is_interview) {
				$date_str = null;
				if(isset($this->connects[$newsid])) {
					$date_str = false;
					$current_connects = $this->connects[$newsid];
					if (isset($current_connects['year'])) {
						[$year] = $current_connects['year'][0];
						$date_str = $year;

					} elseif (isset($current_connects['monthyear'])) {
						[$year,$month] = _getdate($current_connects['monthyear'][0]);
						$date_str = _month_name($month).'&nbsp;'.$year;

					} elseif (isset($current_connects['date'])) {
						$stamp = $current_connects['date'][0];
						$date_str = get_relative_day_name($stamp) ?: _date_get($stamp);

					} elseif (isset($current_connects['party'])) {
						$cnt = count($current_connects['party']);
						if ($cnt === 1) {
							$partyid = $current_connects['party'][0];
							if ($stamp = getifset($this->stamps,$partyid)) {
								$date_str = get_relative_day_name($stamp) ?: _date_get($stamp);
							}
							if (($party = memcached_party_and_stamp($partyid))
							&&	($cityid = $party['CITYID'])
							) {
								$date_str .= ', '.escape_utf8(get_element_title('city',$cityid));
							}
						} else {
							$diff_cities = [];
							$diff_months = [];
							if ($cnt <= 3) {
								foreach ($current_connects['party'] as $partyid) {
									if (isset($this->stamps[$partyid])) {
										[$year, $month, $day] = _getdate($this->stamps[$partyid]);
										$diff_months[$year][$month][] = $day;
										if (($party = memcached_party_and_stamp($partyid))
										&&	($cityid = $party['CITYID'])
										) {
											$diff_cities[$cityid] = $cityid;
										}
									}
								}
								if ($diff_months) {
									ksort($diff_months);
									$yearcnt = count($diff_months);
									foreach ($diff_months as $year => $months) {
										ksort($months);
										$monthcnt = count($months);
										foreach ($months as $month => $days) {
											sort($days);
											$daycnt = count($days);
											$daystr = null;
											foreach ($days as $day) {
												$daystr .= $day;
												if (--$daycnt) {
													$daystr .= $daycnt > 1 ? ', ' : ' &amp; ';
												}
											}
											$date_str .= $daystr.' '._month_name($month).' '.$year;
											if (--$monthcnt) {
												$date_str .= $monthcnt > 1 ? ', ' : ' &amp; ';
											}
										}
										if (--$yearcnt) {
											$date_str .= $yearcnt > 1 ? ', ' : ' &amp; ';
										}
									}
								}
							} else {
								foreach ($current_connects['party'] as $partyid) {
									if (isset($this->stamps[$partyid])) {
										[$year,$month] = _getdate($this->stamps[$partyid]);
										$diff_months[$year.':'.$month] = true;
										if (($party = memcached_party_and_stamp($partyid))
										&&	($cityid = $party['CITYID'])
										) {
											$diff_cities[$cityid] = $cityid;
										}
									}
								}
								if ($diff_months
								&&	count($diff_months) === 1
								) {
									$date_str = _month_name($month).' '.$year;
								}
							}
							if ($diff_cities
							&&	count($diff_cities) <= 2
							) {
								foreach ($diff_cities as $cityid) {
									if ($date_str) {
										$date_str .= ', ';
									}
									$date_str .= escape_utf8(get_element_title('city', $cityid));
								}
							}
						}
					}
				}
				if ($testsub = $news['SUBTITLE']) {
					$testdate = $date_str;
					foreach (['testsub', 'testdate'] as $var) {
						$$var = str_replace(
							[' ',  '&amp;',  '&'],
							['',   'en',	'en'],
							$$var
						);
						$$var = preg_replace('"[^a-z]+"u', '', $$var);
					}
					similar_text($testsub, $testdate, $pct);
					if ($pct >= 90) {
						$news['SUBTITLE'] = null;
					}
				}
				if ($forceid === $newsadid) {
					?><img src="<?= STATIC_HOST ?>/images/rarr<?= is_high_res() ?>.png" alt="&rarr;" class="abs rarr" id="nidd<?= $forceid ?>" /><?
				}
				ob_start();
				uploadimage_show_with_fallback('news', $newsid, flags: $upimg_flags, size: 'thumb');
				$img = ob_get_clean();
			} else {
				ob_start();
				uploadimage_show_with_fallback('interview',$news['INTERVIEWID'], flags: $upimg_flags, size: 'thumb');
				$img = ob_get_clean();
			}
			if ($img) {
				if (!str_contains($img, '<a ')) {
					?><a<?
					if (SMALL_SCREEN) {
						?> style="max-width: 50%;"<?
					}
					?> class="right-float"<?
					?> onclick="<?=
							$clickbox
						?	/** @lang JavaScript */ 'return false;'
						:	/** @lang JavaScript */ "addclass(Pf.getParent(this, 'box'), 'seenitem')" ?>"<?
					?> href="<?= $jump ?>"><?= $img ?></a><?
				} else {
					?><div class="right-float"><?= $img ?></div><?
				}
			}
			?><div><?
			?><b><?
			?><a<?
			?> onclick="<?=
					$clickbox
				?	/** @lang JavaScript */ 'return false;'
				:	/** @lang JavaScript */ "addclass(Pf.getParent(this, 'box'),'seenitem');" ?>"<?
			?> href="<?= $jump ?>"><?
			echo	$clickbox
				?	flat_with_entities($news['TITLE'], UBB_UTF8)
				:	make_all_html	  ($news['TITLE'], UBB_UTF8);
			if (!$this->double
			&&	$news['SUBTITLE']
			) {
				?><small> <?= MIDDLE_DOT_ENTITY ?> <?=
					$clickbox
				?	flat_with_entities($news['SUBTITLE'], UBB_UTF8)
				:	make_all_html($news['SUBTITLE'], UBB_UTF8)
				?></small><?
			}
			?></a><?
			?></b><?
			if (!$is_interview) {
				if (isset($this->favinfo,
						  $this->connects[$newsid])
				) {
					$this->favinfo->show_stars($this->connects[$newsid]);
				}
				if (!$news['ACCEPTED']) {
					?> <span class="light">(<?= __('attrib:not_accepted') ?>)</span><?
				}
				if ($this->double
				&&	$news['SUBTITLE']
				) {
					?><br /><small><?=
						$clickbox
					?	flat_with_entities($news['SUBTITLE'], UBB_UTF8)
					:	make_all_html($news['SUBTITLE'], UBB_UTF8)
					?></small><?
				}
			}
			?><br /><?
			if ($date_str) {
				?><small class="light6"><?
				if (!$is_interview) {
					?>&rarr; <?
				}
				echo $date_str;
				?></small><?
			}
			?></div><?
			?><div><?
			echo make_all_html($news['TEASER'], UBB_UTF8 | ($clickbox ? UBB_NO_LINKS : 0));

			if (!$is_interview) {
				show_wins($this->contests, $newsid);

				if (!empty($this->commentcnt[$newsid])) {
					?> <a<?
					?> class="small light"<?
					?> onclick="return openLink(event,this.href, true, true)"<?
					?> href="<?= get_element_href('news',$newsid) ?>#cmts"><?= $this->commentcnt[$newsid] ?></a><?
				}
			}
			?></div><?
			if ($this->double) {
				?></td><?
				if ($only_one) {
					# Hack to force single newsad to the middle
					?><td style="width: 25%;"></td><?
				}
				if ($teasercnt & 1) {
					?></tr></table><?
				}
			} else {
				box::close();
			}
			++$teasercnt;

			if ($subbar) {
				display_ad($subbar, loading: 'eager');
			}
		}
		if ($this->double
		&& ($teasercnt & 1)
		) {
			?><td>&nbsp;</td></tr></table><?
		}
		if (ROBOT) {
			return true;
		}
		$ok = true;
		if (isset($updids)
		&&	!have_admin()
		) {
			foreach ($updids as $try => $ids) {
				$impressions_done = $try ? 'IMPRESSIONSDONETRY' : 'IMPRESSIONSDONE';

				if (!db_update('newsad',"
					UPDATE newsad SET $impressions_done = $impressions_done + 1
					WHERE NEWSADID IN (".implode(', ', $ids).')')
				) {
					$ok = false;
				}
			}
		}
		if (isset($keepids)
		&&	!db_update('newsad','
			UPDATE newsad SET IMPRESSIONSDONE = IMPRESSIONSDONE + 1
			WHERE NEWSADID IN ('.implode(', ', $keepids).')')
		) {
			$ok = false;
		}
		if (isset($hardids)
		&&	!db_update('newsad','
			UPDATE newsad SET IMPRESSIONSDONE_HARD = IMPRESSIONSDONE_HARD + 1
			WHERE NEWSADID IN ('.implode(', ', $hardids).')')
		) {
			$ok = false;
		}
		if (isset($shown)
		&&	!db_update('newsad','
			UPDATE newsad SET ACTUALHITS = ACTUALHITS + 1
			WHERE NEWSADID IN ('.implode(', ', $shown).')')
		) {
			$ok = false;
		}
		return $ok;
	}

	final public function show_lines(): void {
		$hide_subtitles = !empty($this->hide_subtitles);
		$news_admin = have_admin('news');
		$cnt = 0;
		$galleries = memcached_rowuse_hash(['gallery','party'],'
			SELECT	GALLERYID AS ID, PARTYID, PSTAMP, "gallery" AS ITEM_TYPE,
					(SELECT COUNT(*) FROM image WHERE image.GALLERYID = gallery.GALLERYID AND HIDDEN = 0) AS PHOTOS
			FROM gallery
			JOIN party USING (PARTYID)
			WHERE PSTAMP > '.(TODAYSTAMP - 10 * ONE_DAY).'
			  AND NOT NOVERVIEW
			  AND VISIBLE = "yes"
			ORDER BY GALLERYID'
		);
		if ($galleries) {
			foreach ($galleries as $gallery) {
				if ($gallery['PSTAMP'] <= CURRENTSTAMP) {
					$this->newslist[] = $gallery;
				}
			}
			$comments['gallery'] = memcached_simple_hash(['photo_comment','image'],'
				SELECT GALLERYID,COUNT(*)
				FROM photo_comment
				JOIN image ON ID=IMGID
				WHERE ACCEPTED
				  AND GALLERYID IN ('.implodekeys(',',$galleries).')
				GROUP BY GALLERYID'
			);
		}

		if ($interviews = memcached_rowuse_hash('interview','
			SELECT INTERVIEWID AS ID,PSTAMP,"interview" AS ITEM_TYPE
			FROM interview
			WHERE PSTAMP>'.(TODAYSTAMP - 2 * ONE_WEEK).'
			  AND ACCEPTED
			ORDER BY INTERVIEWID'
		)) {
			foreach ($interviews as /* $id => */ $interview) {
				if ($interview['PSTAMP'] <= CURRENTSTAMP) {
					$this->newslist[] = $interview;
				}
			}
			$comments['interview'] = memcached_simple_hash('interview_comment','
				SELECT ID, COUNT(*)
				FROM interview_comment
				WHERE ACCEPTED
				  AND ID IN ('.implodekeys(',', $interviews).')
				GROUP BY ID'
			);
		}

		if ($contests = memcached_rowuse_hash(['contest', 'party'], '
			SELECT DISTINCT CONTESTID, "contest" AS ITEM_TYPE, ACTIVE, CLOSE_STAMP, PARTYID, contest.NAME, PSTAMP
			FROM contest
			LEFT JOIN party USING (PARTYID) '.
			join_only_for_current_country('party').'
			WHERE PSTAMP > '.$this->min_pstamp.'
			  AND ACTIVE')
		) {
			foreach ($contests as $contest) {
				if ($contest['PSTAMP'] <= CURRENTSTAMP) {
					$this->newslist[] = $contest;
				}
			}
			$comments['contest'] = memcached_simple_hash(['contest_comment','image'], '
				SELECT ID,COUNT(*)
				FROM contest_comment
				WHERE ACCEPTED
				  AND ID IN ('.implodekeys(',', $contests).')
				GROUP BY ID'
			);
		}

		if (empty($this->newslist)) {
			return;
		}

		require_once '_layout.inc';

		number_reverse_sort($this->newslist,'PSTAMP');

		$year = $month = $day = null;
		$previous_news = null;

		foreach ($this->newslist as $news) {
			$type = empty($news['ITEM_TYPE']) ? 'news' : $news['ITEM_TYPE'];
			if ($type === 'news') {
				if ($news['PSTAMP'] > CURRENTSTAMP
				||	$news['TYPE'] === 'teaserad'
				||	!$news_admin
				&&	!$news['ACCEPTED']
				) {
					$previous_news = $news;
					continue;
				}
			}
			if ($cnt > 50) {
				break;
			}
			++$cnt;
			$previous_year  = $year;
			$previous_month = $month;
			$previous_day   = $day;

			if (isset($weekday)) {
				$news_per_day[$previous_year][$previous_month][$previous_day][] = [$previous_news, ob_get_clean()];
			}

			[$year, $month, $day, /* $hour */, /* $minutes */, /* $seconds */, $current_weekday] = _getdate($news['PSTAMP']);

			ob_start();
			switch ($type) {
			case 'news':
				$news['NEWS'] = true;
				?><div class="<?
				if (!$news['ACCEPTED']) {
					?>unaccepted <?
				}
				?>item"><?

				$newsid = $news['NEWSID'];
				?><a href="<?= get_element_href('news',$newsid,$news['TITLE']) ?>"><?
				if ($news['TITLE']) {
					echo flat_with_entities($news['TITLE'], UBB_UTF8);
				}
				if (!$hide_subtitles
				&&	$news['SUBTITLE']
				) {
					?><small><?
					if ($news['TITLE']) {
						?> <?= MIDDLE_DOT_ENTITY ?> <?
					}
					echo flat_with_entities($news['SUBTITLE'], UBB_UTF8);
					?></small><?
				}
				?></a><?
				if (isset($this->favinfo,
						  $this->connects[$newsid])
				) {
					$this->favinfo->show_stars($this->connects[$newsid]);
				}

				show_wins($this->contests, $newsid, true);

				if (isset($this->commentcnt[$newsid])) {
					?><small class="light"> <?= MIDDLE_DOT_ENTITY ?> <?
					?><a href="<?= get_element_href('news',$newsid); ?>#cmts"><?= $this->commentcnt[$newsid] ?></a></small><?
				}
				?></div><?
				break;

			case 'interview':
			case 'gallery':
				?><div class="item"><?
				?><a href="<?= get_element_href($type, $news['ID']) ?>"><?
				echo match ($type) {
					'gallery'	=> get_camera_icon(),
					default		=> element_name($type),
				};
				switch ($type) {
				case 'gallery':
					echo __('home:news:photos_published',DO_UBBFLAT,[
						'PHOTOS'	=> $news['PHOTOS'],
						'PARTYID'	=> $news['PARTYID']
					]);
					break;
				case 'interview':
					echo escape_utf8(get_element_title($type,$news['ID']));
					break;
				}
				?></a><?
				if (isset($comments[$news['ID']])) {
					?><small class="light"> <?= MIDDLE_DOT_ENTITY ?> <a href="/gallery/<?= $news['ID'] ?>/comments"><?= $comments[$news['ID']] ?></a></small><?
				}
				?></div><?
				break;

			case 'contest':
				?><div class="item"><?
				$party = $news['PARTYID'] ?  memcached_party_and_stamp($news['PARTYID']) : null;

				$title = '<small>'.Eelement_name('new_contest').':</small> ';
				$title .= $party ? escape_utf8(get_element_title('party',$party['PARTYID'])) : escape_utf8($news['NAME']);

				if ($party) {
					change_timezone('UTC');
					if ($fmt = datefmt_create(
						get_current_locale(),
						IntlDateFormatter::LONG,
						IntlDateFormatter::NONE,
						null,
						IntlDateFormatter::GREGORIAN
					)) {
						$title .= ' <small class="light">'.datefmt_format($fmt, $party['STAMP_TZI']).'</small>';
					}
					change_timezone();
				}
				?><a href="<?= get_element_href('contest', $news['CONTESTID']) ?>"><?= $title
				?> <small class="win"><?= __('partylist:win') ?></small></a><?
				?></div><?
				break;
			}
			$previous_news = $news;

			if (!isset($weekday)
			||	$previous_year  !== $year
			||	$previous_month !== $month
			||	$previous_day   !== $day
			) {
				ob_start();
				?><div class="dayh bold underline"><?
				if (!isset($weekday)) {
					echo element_name('news'); ?> <?
				}
				if ($news['PSTAMP'] >= TODAYSTAMP) {
					echo __('date:today');
				} elseif ($news['PSTAMP'] > TODAYSTAMP - ONE_DAY) {
					echo __('date:yesterday');
				} else {
					echo weekday_name($current_weekday);
				}
				?></div><?

				$header_per_day[$year][$month][$day] = ob_get_clean();

				$weekday = $current_weekday;
			}
		}
		if (isset($weekday, $previous_year, $previous_month, $previous_day)) {
			$news_per_day  [$previous_year][$previous_month][$previous_day][] = [$previous_news, ob_get_clean()];
		}
		foreach ($news_per_day as /* $year => */ &$months) {
			foreach ($months as /* $month => */ &$days) {
				foreach ($days as /* $day => */ &$lines) {
					$per_day_counts[] = count($lines);
					# Group news first, then sort by publication date
					usort($lines,static fn(array $item_a, array $item_b): int =>
							# News first:
							!empty($item_b[0]['NEWS']) <=> !empty($item_a[0]['NEWS'])
							# The reverse publication order:
						?:	$item_b[0]['PSTAMP'] <=> $item_a[0]['PSTAMP']);
				}
				unset($lines);
			}
			unset($days);
		}
		unset($months);

		sort($per_day_counts);
		$median_per_day = $per_day_counts[count($per_day_counts) >> 1];

		$table = $median_per_day < 3;

		layout_open_box('news');
		if ($table) {
			?><table class="fw vtop default itemlist"><?
		}
		foreach ($news_per_day as $year => $months) {
			foreach ($months as $month => $days) {
				foreach ($days as $day => $lines) {
					if (!$table) {
						?><div class="post-day block"><?
						echo $header_per_day[$year][$month][$day];
					}
					$prev_news = null;
					foreach ($lines as [$news,$line]) {
						if (!empty($news['NEWS'])) {
							$prev_news = true;
						} elseif ($prev_news === false) {
							# already done non-news
						} else {
							# add a little space
							if (!$table) {
								?><div style="height:1em">&nbsp;</div><?
							}
							$prev_news = false;
						}
						if ($table) {
							?><tr><?
							?><td class="nowrap light rpad right"><?
							_date_display($news['PSTAMP'], short: true, time_span: true);
							?></td><?
							?><td><?= $line ?></td><?
							?></tr><?
						} else {
							echo $line;
						}
					}
					if (!$table) {
						?></div><?
					}
				}

			}
		}
		if ($table) {
			?></table><?
		}
		layout_close_box();
	}
	function fill_teasers(array $running, array &$newsids, bool $try): array {
		# try seems obsolete. used to indicate optional extra newsads

		# make sure we order in same way as 'running', because it is ordered from most needing impressions to least needing impressions
		$list = memcached_rowuse_array(
			['news', 'newsad'], '
			SELECT DISTINCT NEWSID,TITLE,SUBTITLE,ACCEPTED,PSTAMP,TEASER,KEEPTOP,CLICKBOX,NEWSADID,TYPE,news.CSTAMP,news.USERID
			FROM news
			JOIN newsad USING (NEWSID)
			WHERE NEWSADID IN ('.($idstr = implodekeys(', ', $running)).')
			ORDER BY FIELD(NEWSADID,'.$idstr.')',
			FIVE_MINUTES
		);
		if (!$list) {
			return [];
		}

		$this->showlistsize += count($list);

		foreach ($list as $news) {
			$newsadid = $news['NEWSADID'];
			if (!isset($running[$newsadid]['FORCED'])
			&&	$news['PSTAMP'] > CURRENTSTAMP
			) {
				continue;
			}
			$news['TRY'] = $try;
			$news['FORCED'] = isset($running[$newsadid]['FORCED']) ?: null;
			$newlist[] = $news;
			$newsids[] = $news['NEWSID'];
		}
		if (empty($newlist)) {
			return [];
		}
		return $newlist ?? [];
	}
}
