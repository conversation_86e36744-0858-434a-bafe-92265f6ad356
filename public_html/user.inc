<?php

require_once 'defines/ubb.inc';
require_once '_elementnames.inc';
require_once '_externalsettings.inc';
require_once '_visibility.inc';

const USER_UTF8_FIELDS = [
	'NAME',
	'CITY',
	'OCCUPATION',
];

function preamble(): void {
	if ($userid = $_REQUEST['sID']) {
		if (!user_exists($userid)) {
			not_found();
			return;
		}
		if (invisible_profile($userid)) {
			robot_action('noindex');
			http_response_code(404);
			return;
		}
		switch ($_REQUEST['ACTION']) {
		case 'weblog':
			moved_permanently("/weblog/user/$userid");

		case 'topic':
			moved_permanently("/forum/history/$userid");

		case 'reports':
			moved_permanently("/report/user/$userid");

		case 'flockmessages':
			moved_permanently(str_replace(
				"/user/$userid/flockmessages",
				"/flock/history/$userid",
				$_SERVER['REQUEST_URI'])
			);

		case 'artists':
		case 'locations':
		case 'organizations':
			moved_permanently("/user/$userid/favourites#fav".$_REQUEST['ACTION']);

		case 'photocomments':
			moved_permanently(str_replace(
				"/user/$userid/photocomments",
				"/user/$userid/commentoverview/photo",
				$_SERVER['REQUEST_URI'])
			);

		case 'quoted':
			if (CURRENTUSERID === $_REQUEST['sID']
			&&	$_REQUEST['SUBACTION'] === 'markread'
			&&	have_post()
			&&	have_user()
			&&	($stamp = have_idnumber($_REQUEST, 'STAMP'))
			&&	$stamp < CURRENTSTAMP
			) {
				require_once '_quote.inc';
				db_replace('quoteseen',"REPLACE INTO quoteseen SET USERID = $userid, STAMP = $stamp");
				register_notice('user:notice:all_quotes_read_LINE');
				store_messages_in_cookie();
				flush_new_quotes($userid);
				/** @noinspection UnusedFunctionResultInspection */
				see_other("/user/$userid/quoted");
			}
			break;
		}
	} else {
		switch ($_REQUEST['ACTION']) {
		case 'login':
		case 'form':
		case 'register':
			if (have_element($_REQUEST,'invite',['comment','contest','join'])) {
				setflockcookie('FLOCK_FROMNEW',$_REQUEST['invite'],COOKIE_SET_SESSION);
				require_once '_nocache.inc';
				send_no_cache_headers();
				see_other('/user/'.$_REQUEST['ACTION']);
			}
			break;

		case 'renewfb':
			if (have_user()) {
				require_once '_fbsession.inc';
				fb_renew_redirect();
			}
			break;

		case 'forgotpasswd':
			moved_permanently(str_replace(
				'forgotpasswd',
				'lostpasswd',
				$_SERVER['REQUEST_URI'])
			);
		}
	}
}

function display_header(): void {
	if (!($userid = $_REQUEST['sID'])
	||	!user_exists($userid)
	) {
		return;
	}
	$action = $_REQUEST['ACTION'];
	if (setting('SHOW_USER_GUESTBOOK')
	&&	external_setting($userid, 'VISIBILITY_GUESTBOOK') === EVERYBODY
	) {
		?><link<?
		?> rel="alternate"<?
		?> type="application/atom+xml"<?
		?> title="<?= __('feed:guestbook_LINE',DO_UBBFLAT, ['USERID' => $userid]) ?>"<?
		?> href="/feed/user/<?= $userid ?>/comments.xml" /><?
	}
	if ($action === 'weblog'
	&&	str_contains($_SERVER['REQUEST_URI'], '/user/weblog/USERID/')
	) {
		moved_permanently(str_replace("/user/weblog/USERID/$userid", "/weblog/user/$userid", $_SERVER['REQUEST_URI']));
	}
}

function display_title(): void {
	echo match ($_REQUEST['ACTION']) {
		'subscriptions' => __('section:following'),
		'savedomments'  => element_plural_name('saved_elements'),
		'showonline'	=> element_plural_name('suitable_online_user'),
		default		 	=> null,
	};
}

function perform_commit(): ?bool {
	switch ($_REQUEST['ACTION']) {
	default:
	case null:					return null;
	case 'makebuddy':			return user_make_buddy();
	case 'setstatus':			return user_set_status();
	case 'acceptbuddy':			require_once '_buddy.inc'; return _accept_buddy(require_idnumber($_REQUEST,'sID'));
	case 'buddyremove':			require_once '_buddy.inc'; return _remove_buddy(require_idnumber($_REQUEST,'sID'));
	case 'commitnew':			require_once '_nocache.inc';
								send_no_cache_headers();
	case 'commit':				return user_profile_commit();
	case 'commitsettings':		return user_commit_settings();
	case 'textcommit':			return user_commit_text();
	case 'setrelation':			return user_set_relation();
	case 'commitpersonalnote':	return user_commit_personalnote();
	case 'refreshstats':		return user_refresh_stats();
	case 'ignorecommit':		return user_commit_ignore();
	case 'blockcommit':			return user_commit_block();
	case 'commitdeceased':		return user_commit_deceased_info();
	case 'commitheart':			return user_commit_valentine();
	case 'removeheart':			return user_remove_valentine();
	case 'seenhearts':			return user_seen_hearts();
	case 'mailproblemssolved':	return user_mailproblems_solved();
	case 'mailproblemsseen':	return user_mailproblems_seen();
	case 'disconnectpoll':		return user_disconnect_poll();
	case 'connectpoll':			return user_connect_poll();
	case 'changemail':			return user_change_email();
	case 'disconnectfb':		require_once '_fbsession.inc';		return fb_disconnect();
	case 'eradicate':			require_once '_eradicate_user.inc';	return eradicate_user();
	}
}

function display_body(): void {
	switch ($_REQUEST['ACTION']) {
	default:
		require_once '_search_via_url.inc';
		search_via_url();
		not_found();
		return;

	case 'deceased':
		$_REQUEST['STATUS'] = $_REQUEST['ACTION'];
	case 'search':
	case 'search-result':
	case null:
		user_display_overview();
		return;

	case 'ignorecommit':
	case 'ignoreform':
		user_display_ignore_form();
		return;

	case 'blockcommit':
	case 'blockform':
		user_display_block_form();
		return;

	case 'settings':
	case 'commitsettings':
		user_display_settings();
		return;

	case 'last':
		user_display_last();
		return;

	case 'login':
	case 'impersonate':
	case 'activate':
		user_display_login();
		return;

	case 'logoff':
		user_display_logoff();
		return;

	case 'setuser':
	case 'setfb':
		user_display_loginresult();
		return;

	case 'connectloginfb':
		require_once '_fbsession.inc';
		fb_display_connect_overview();
		return;

	case 'renewfb':
	case 'connectfb':
	case 'updatefb':
	case 'reauthfb':
		require_once '_fbsession.inc';
		fb_display_connect_question();
		return;

	case 'connectedfb':
	case 'updatedfb':
	case 'renewedfb':
	case 'reauthedfb':
		require_once '_fbsession.inc';
		fb_display_connected();
		return;


	case 'textcommit':
	case 'condolences':
	case 'condolence':
	case 'comments':
	case 'comment':
	case 'buddies':
#	case 'buddymap':
#	case 'buddycity':
	case 'archive':
	case 'interestingarchive':
	case 'favourites':
#	case 'artists':
#	case 'locations':
	case 'flocks':
#	case 'organizations':
#	case 'reports':
	case 'columns':
	case 'reviews':
	case 'interviews':
	case 'secrets':
	case 'shoots':
	case 'videoshoots':
	case 'text':
	case 'inactivebuddies':

	case 'commit':
	case 'removebuddy':
	case 'makebuddy':
	case 'acceptbuddy':
	case 'setstatus':
	case 'textremove':
	case 'setrelation':
	case 'refreshstats':
	case 'commitdeceased':
	case 'commitheart':
	case 'removeheart':
	case 'heartform':
	case 'denyheart':
	case 'seenhearts':
	case 'mailproblemsseen':
	case 'disconnectpoll':
	case 'connectpoll':
	case 'acceptemail':
	case 'changemail':
	case 'biography':
	case 'eradicate':
	case 'single':
		user_display_single();
		return;

	case 'mailproblemssolved':
			$_SERVER['REQUEST_METHOD'] === 'POST'
		?	user_display_mail_errors()
		:	user_display_single();
		return;


	case 'deceasedinfo':
		user_display_deceased_info();
		return;
	case 'signup':
	case 'form':
		user_display_profile_form();
		return;
	case 'emailform':
		user_display_email_form();
		return;
	case 'commitnew':
		if (isset($GLOBALS['__usercommitok'])) {
			layout_show_section_header(Eelement_name('registration'));

			layout_open_box('white');
			?><div class="block"><?= __('user:info:activation_message_sent_LINE',DO_UBB | DO_NL2BR) ?></div><?
			layout_close_box();
		} else {
			# make sure password is not stored by password manager:
			header('HTTP/1.1 400',true);

			layout_show_section_header(Eelement_name('registration'));

			layout_open_box('white');
			?><div class="block"><?= __('user:info:registration_failed_LINE') ?></div><?
			layout_close_box();
		}
		return;

	case 'commentoverview':		user_display_all_comments();				return;
	case 'forummessageoverview':user_display_all_messages('forum');			return;
	case 'flockmessageoverview':user_display_all_messages('flock');			return;

	case 'quoted':				user_display_all_quoted();					return;
	case 'ratingoverview':		user_display_all_ratings();					return;
	case 'subscriptions':		user_display_all_subscriptions();			return;

	case 'openedtopics':		user_display_opened_topics();				return;
	case 'flocktopics':			user_display_flocktopics();					return;
	case 'photos':				user_display_photos();						return;
	case 'lostpasswd':			user_display_forgot(true);					return;
	case 'losttoken':			user_display_forgot(false);					return;
	case 'sendpasswdtoken':		user_display_token_form(true);				return;
	case 'sendactivationtoken':
								user_display_token_form(false);				return;
	case 'nonactive':			display_nonactive();						return;
	case 'samemail':			user_display_samemail();					return;
	case 'textform':			user_display_own_info_form();				return;
	case 'chooserelation':		user_choose_relation();						return;
	case 'birthdays':			user_display_birthdays();					return;
	case 'changepasswd':		user_display_password_form(true);			return;
	case 'resetpasswd':			user_display_password_form(false);			return;
	case 'commitpasswd':		user_display_password_change();				return;
	case 'showonline':			user_show_online();							return;
	case 'commitpersonalnote':
	case 'personalnote':		user_show_personalnote();					return;
	case 'delete':				user_delete();								return;
	case 'ignorelist':			user_display_ignorelist();					return;
	case 'blocklist':			user_display_blocklist();					return;
	case 'mailerrors':			user_display_mail_errors();					return;
	case 'mailerror':			user_display_mail_error();					return;
	case 'valentine':			user_display_valentine();					return;
	case 'information':			user_display_information();					return;
	case 'disconnectfb':
	case 'operations':			user_display_operations();					return;
	case 'savedcomments':		user_display_saved_comments();				return;
	case 'bioform':				require_once '_bio.inc';
								show_bio_form();							return;
	}
}
function user_display_all_comments(): void {
	require_once '_comment.inc';
	if (!($userid = require_existing_user($_REQUEST,'sID'))
	||	($element = $_REQUEST['SUBACTION'])
	&&	!is_commentable($element)
	) {
		not_found();
		return;
	}
	$may_all = 	CURRENTUSERID === $userid
			||	have_comment_admin()
			||	have_self_or_admin($userid,'helpdesksuper');

	if ($element === 'photo'
	?	!require_visible_esetting($userid,'STATISTICS',have_admin(['helpdesk', 'photo_comment']))
	:	(!$may_all && !require_self_or_admin($userid,'helpdesksuper'))
	) {
		no_permission();
		return;
	}
	if (CURRENTUSERID === $userid) {
		require_once '_visibility.inc';
		?><div class="small light abs" style="right: 1em;"><?=
			visible_to(
					$element === 'photo'
				?	external_setting($userid,'VISIBILITY_STATISTICS')
				:	SELF
			)
		?></div><?
	}
	require_once '_commentoverview.inc';
	$ov = new commentoverview($userid, $element, $may_all ? 0 : commentoverview::HIDE_SELECTION);
	$ov->display();
}

function user_display_all_messages($base) {
	if (!($userid = require_existing_user($_REQUEST,'sID'))
	||	CURRENTUSERID !== $userid
	&&	!have_admin('helpdesksuper')
	&&	(	$base === 'flock'
		?	!require_admin('flockmessage')
		:	!require_forum_admin()
		)
	) {
		return;
	}
	if ($id = $_REQUEST['subID']) {
		if (!require_existing_element($base,$id)) {
			http_status(404);
			return;
		}
	}
	layout_show_section_header();
	if (CURRENTUSERID === $userid) {
		require_once '_visibility.inc';
		?><div class="small light abs" style="right:1em"><?= visible_to(SELF) ?></div><?
	}
	require_once '_messageoverview.inc';
	$ov = new messageoverview($userid, $base, $id);
	$ov->display();
}

function user_display_all_quoted() {
	if (!($userid = require_existing_user($_REQUEST,'sID'))
	||	(	CURRENTUSERID !== $userid
		&&	!have_super_admin()
		)
	) {
		return;
	}
	require_once '_comment.inc';
	if ($element = $_REQUEST['SUBACTION']) {
		if ($element !== 'new'
		&&	!is_quotable($element)
		&&	$element !== 'flockmessage'
		&&	$element !== 'message'
		&&	$element !== 'directmessage'
		) {
			http_status(404);
			return;
		}
	}
	layout_show_section_header();
	if (CURRENTUSERID === $userid) {
		if ($element === 'new') {
			include_js('js/quoteseen');
			?><div id="nqallseen" class="l block"><?
			?><span<?
			?> data-confirm="<?= __('quotes:confirm:seen_LINE') ?>"<?
			?> data-stamp="<?= CURRENTSTAMP ?>"<?
			?> class="unhideanchor"<?
			?> onclick="Pf.seenQuotes(this)"><?
			?><?= __C('action:mark_all_seen') ?></span><?
			?></div><?
		}

		require_once '_visibility.inc';
		?><div class="light small r block"><?= visible_to(SELF) ?></div><?
		?><div class="clear"></div><?

	}
	require_once '_quoteoverview.inc';
	$ov = new quoteoverview($userid, $element);
	$ov->display();
}
function user_display_saved_comments() {
	if (!($userid = require_existing_user($_REQUEST,'sID'))
	||	(	CURRENTUSERID != $userid
		&&	!have_super_admin()
		)
	) {
		return;
	}
	layout_show_section_header();

	require_once '_commentkeep.inc';
	$so = new savedoverview($userid, $_REQUEST['SUBACTION']);
	$so->display();
}
function user_display_all_ratings() {
	require_once '_rating.inc';
	if (!($userid = require_existing_user($_REQUEST,'sID'))
	||	CURRENTUSERID != $userid
	&&	!have_rating_admin()
	&&	!require_self_or_admin($userid,'helpdesksuper')
	) {
		return;
	}
	require_once '_rating.inc';
	if ($element = $_REQUEST['SUBACTION']) {
		if (!isset(RATINGABLES[$element])) {
			http_status(404);
			return;
		}
	}
	layout_show_section_header();
	if (CURRENTUSERID == $userid) {
		require_once '_visibility.inc';
		?><div class="small light abs" style="right:1em"><?= visible_to(SELF) ?></div><?
	}
	require_once '_ratingoverview.inc';
	$ov = new ratingoverview($userid,$element);
	$ov->display();
}

function user_display_all_subscriptions() {
	$userid = CURRENTUSERID;
	layout_open_section_header();
	echo __C('section:following');
	layout_close_section_header();
	require_once '_subscriptionoverview.inc';
	$ov = new subscriptionoverview($userid);
	$ov->display();
}

function user_disconnect_poll(): bool {
	if (!require_idnumber($_REQUEST,'sID')) {
		return false;
	}
	require_once '_poll.inc';
	return _poll_remove_from_profile(0,$_REQUEST['sID']);
}
function user_connect_poll(): bool {
	if (!require_idnumber($_REQUEST,'sID')
	||	!require_idnumber($_REQUEST,'subID')
	) {
		return false;
	}
	require_once '_poll.inc';
	return _poll_connect_to_profile($_REQUEST['subID']);
}

function show_mailproblems_solved_question(): void {
	?><form<?
	?> onsubmit="return submitForm(this);"<?
	?> method="post"<?
	?> action="/user/<?= $_REQUEST['sID'] ?>/mailproblemssolved"><?
	?><div class="block"><?=
		__('user:question:have_the_problems_been_solved_LINE')
	?> <input type="submit" value="&nbsp;<?= __C('answer:yes') ?>&nbsp;" /><?
	?></div><?
	?></form><?
}

function show_mailproblems_mark_seen(): void {
	?><form<?
	?> onsubmit="return submitForm(this)"<?
	?> method="post"<?
	?> action="/user/<?= $_REQUEST['sID'] ?>/mailproblemsseen"><?
	?><div class="funcs"><?
	?><input type="submit" class="unhideanchor" value="<?= __C('action:mark_seen') ?>" /><?
	?></div><?
	?></form><?
}

function user_mailproblems_solved(): bool {
	if (!require_post()
	||	!($userid = require_idnumber($_REQUEST,'sID'))
	||	!require_self_or_admin($userid,'helpdesk')
	) {
		return false;
	}
	if (false === ($email = db_single('user','SELECT EMAIL FROM user WHERE USERID='.$userid,DB_USE_MASTER))) {
		return false;
	}
	if ($email === null) {
		not_found();
		return false;
	}
	if (!$email) {
		register_error('user:error:no_email_registered_for_user_LINE', DO_UBB, ['USERID' => $userid]);
		return false;
	}
	if (!db_update('realemailcheck',"
		UPDATE realemailcheck SET FAILS = 0
		WHERE EMAIL = '".addslashes($email)."'")
	||	!db_insert('emailcheckseen',"
		INSERT INTO emailcheckseen SET
			USERID		= $userid,
			SEENSTAMP	= ".CURRENTSTAMP."
		ON DUPLICATE KEY UPDATE
			SEENSTAMP	= VALUES(SEENSTAMP)")
	) {
		return false;
	}
	register_notice('user:notice:mailproblemssolved_LINE');
	return true;
}

function user_mailproblems_seen(): bool {
	return	require_post()
	&&	($userid = require_idnumber($_REQUEST, 'sID'))
	&&	require_self_or_admin($userid, 'helpdesk')
	&&	db_insert('emailcheckseen',"
		INSERT INTO emailcheckseen SET
			USERID		= $userid,
			SEENSTAMP	= ".CURRENTSTAMP."
		ON DUPLICATE KEY UPDATE
			SEENSTAMP	= VALUES(SEENSTAMP)"
	);
}

function user_commit_valentine(): bool {
	if (!require_user()
	||	!require_idnumber($_REQUEST,'sID')
	||	!require_referer_regex('/user/'.$_REQUEST['sID'].'[/\.:]')
	) {
		return false;
	}
	if (!($user = memcached_user($_REQUEST['sID']))) {
		return false;
	}
	if ($user['STATUS'] !== 'active') {
		_error('Je kunt alleen een hartje naar een actief lid sturen!');
		return false;
	}
	if (!empty($_POST['BODY'])
	&&	strlen($_POST['BODY']) > 500
	) {
		_error('Je bericht is te lang! Je kunt maximaal 500 karakters gebruiken.');
		return false;
	}
	global $__year,$__month,$__day;
	if ($__month !== 2
	||	$__day !== 14
	&&	$__day !== 13
	&&	$__day !== 12
	) {
		_error('Je kunt vanaf twee dagen voor valentijn hartjes sturen!');
		return false;
	}
	if (!db_insert('valentine_log',"
		INSERT INTO valentine_log
		SELECT * FROM valentine
		WHERE FROM_USERID = ".CURRENTUSERID."
		  AND TO_USERID = {$_REQUEST['sID']}
		  AND YEAR = $__year")
	||	!db_insupd('valentine','
		INSERT INTO valentine SET
			FROM_USERID	='.CURRENTUSERID.',
			TO_USERID	='.$_REQUEST['sID'].',
			ANONYMOUS	='.(isset($_POST['ANONYMOUS']) ? 1 : 0).',
			BODY		="'.(empty($_POST['BODY']) ? null : addslashes(trim($_POST['BODY']))).'",
			MSTAMP		='.CURRENTSTAMP.',
			MUSERID		='.CURRENTUSERID.',
			YEAR		='.$__year.',
			CSTAMP		='.CURRENTSTAMP.'
		ON DUPLICATE KEY UPDATE
			ANONYMOUS	=VALUES(ANONYMOUS),
			BODY		=VALUES(BODY),
			MSTAMP		=VALUES(MSTAMP),
			MUSERID		=VALUES(MUSERID)')
	) {
		return false;
	}
	switch (db_affected()) {
	case 1:	 register_notice ('valentine_heart:notice:sent_LINE');		 break;
	case 2:	 register_notice ('valentine_heart:notice:changed_LINE');	 break;
	default: register_warning('valentine_heart:warning:unchanged_LINE'); break;
	}
	return true;
}
function user_remove_valentine(): bool {
	global $__year,$__month,$__day;
	if ($__month !== 2
	||	$__day !== 14
	&&	$__day !== 13
	&&	$__day !== 12
	) {
		_error('Je kunt een hartje alleen in de twee dagen voor en op valentijnsdag intrekken!');
		return false;
	}
	if (!require_user()
	||	!require_idnumber($_REQUEST,'sID')
	||	!require_referer('/user/'.$_REQUEST['sID'].'/')
	||	!db_insert('valentine_deletion_log',"
		INSERT INTO valentine_deletion_log
		SELECT *, ".CURRENTSTAMP."
		FROM valentine
		WHERE FROM_USERID = ".CURRENTUSERID."
		  AND TO_USERID = {$_REQUEST['sID']}
		  AND YEAR = $__year")
	||	!db_delete('valentine',"
		DELETE FROM valentine
		WHERE FROM_USERID = ".CURRENTUSERID."
		  AND TO_USERID = {$_REQUEST['sID']}
		  AND YEAR = $__year")
	) {
		return false;
	}
	if (db_affected()) {
		_notice('Hartje is verwijderd.');
	}
	return true;
}

function user_seen_hearts(): bool {
	global $__year;
	if (!require_user()
	||	!require_idnumber($_REQUEST,'sID')
	||	!require_self($_REQUEST['sID'])
	||	!require_referer('/user/'.CURRENTUSERID.'\b')
	||	!db_insert('valentineseen',"
		REPLACE INTO valentineseen SET
			USERID	= ".CURRENTUSERID.",
			YEAR	= $__year")
	) {
		return false;
	}
	return true;
}

function user_display_valentine(): void {
	if (!require_idnumber($_REQUEST,'sID')
	||	!require_idnumber($_REQUEST,'subID')
	||  !have_super_admin()
	&&	CURRENTUSERID !== $_REQUEST['sID']
	&&	!require_self($_REQUEST['sID'])
	) {
		return;
	}
	layout_open_section_header();
	?>Valentijnswensen<?
	layout_close_section_header();

	$year = $_REQUEST['subID'];

	global $__year,$__month,$__day;

	if ($__year < $year
	||	(	$__year === $year
		&&	(	$__month < 2
			||	$__month === 2
			&&	$__day < 14
			)
		)
	) {
		_error('Het is nog geen valentijnsdag, je kunt deze hartjes nog niet bekijken!');
		return;
	}
	require_once '_ignores.inc';
	$ignores = ignores(IGNORE_VALENTINE_HEARTS,$_REQUEST['sID']);
	$valentines = memcached_rowuse_array('valentine', "
		SELECT FROM_USERID AS USERID, BODY, ANONYMOUS, ID
		FROM valentine
		WHERE TO_USERID = {$_REQUEST['sID']}
		  AND YEAR = $year
		  AND BLOCKED = 0".(
			$ignores
		?	' AND FROM_USERID NOT IN ('.implodekeys(',',$ignores).')'
		:	null
		).'
		ORDER BY CSTAMP ASC'
	);
	if ($valentines === false) {
		return;
	}
	if (!$valentines) {
		?><p>Helaas, niemand heeft in <?= $_REQUEST['subID'];
		?> jou een hartje gestuurd.</p><?
		return;
	}
	$tcnt = [0, 0, 0, 0];
	$total = 0;
	foreach ($valentines as &$valentine) {
		$type = ($valentine['BODY'] ? 1 : 0) | ($valentine['ANONYMOUS'] ? 2 : 0);
		$valentine['TYPE'] = $type;
		++$tcnt[$type];
		++$total;
	}
	unset($valentine);
	?><p>Op valentijnsdag <?= $_REQUEST['subID'];
	?> heb je <?= $total;
	?> <?
	if ($total > 1) {
		?>hartjes<?
	} else {
		?>hartje<?
	}
	?> gekregen<?
	if ($acnt = $tcnt[2] + $tcnt[3]) {
		?>, waarvan <?= $acnt;
		?> anoniem<?
	}
	?>!</p><?
	$moreinfo = ($tcnt[1] || $tcnt[3] || $tcnt[0]);
	require_once '_heart.inc';
	layout_open_box('white');
	?><div class="block"><?
	foreach ($valentines as &$valentine) {
		$type = $valentine['TYPE'];
		if ($moreinfo) {
			if (!$valentine['ANONYMOUS']) {
				$valentine['NICK'] = memcached_nick($valentine['USERID']);
				$userids[$valentine['USERID']] = true;
				if (!$valentine['BODY']) {
					$nonbody[] = $valentine;
				} else {
					$body[] = $valentine;
				}
			} else {
				if ($valentine['BODY']) {
					$anonbody[] = $valentine;
				}
			}
		}
		switch ($type) {
		case 2: // no body, anonymous
			show_half_heart();
			break;
		case 0: // no body. not anonymous
		case 1: // body, not anonymous
		case 3: // body, anonymous
			show_heart();
			break;
		}
		?> <?
	}
	?></div><?
	unset($valentine);
	layout_close_box();
	if (!$moreinfo) {
		return;
	}
	if (isset($nonbody)) {
		?><h3>Zonder bericht</h3><?
		?><p>Van de volgende leden heb je een hartje zonder bericht ontvangen:</p><?
		layout_open_box('white');
		string_sort($nonbody,'NICK');
		?><div class="block"><?
		foreach ($nonbody as $valentine) {
			echo get_element_link('user', $valentine['USERID'], $valentine['NICK']);
			?><br /><?
		}
		?></div><?
		layout_close_box();
	}
	if (isset($body)) {
		?><h3>Met bericht</h3><?
		?><p>Van de volgende leden heb je een hartje met bericht gekregen:</p><?
		foreach ($body as $valentine) {
			layout_open_box('white');
			$img = _profileimage_get($valentine);
			show_link_to_ticket_form('valentine',$valentine['ID'],'r');
			?><table style="margin: 0;" class="vtop"><tr><td class="vtop" style="width: 110px;"><?= $img ?></td><td><div class="block"><?=
			make_all_html($valentine['BODY']);
			?></div><div class="heartsender block"><?= MIDDLE_DOT_ENTITY ?> <a class="small" href="<?= get_element_href('user',$valentine['USERID'],$valentine['NICK']);
			?>"><?= escape_specials($valentine['NICK']);
			?></a><?
			?></div></td></tr></table><?
			layout_close_box();
		}
	}
	if (isset($anonbody)) {
		?><h3>Anoniem met bericht</h3><?
		?><p>Van de volgende geheime aanbidders heb je een valentijnsbericht gekregen:</p><?
		foreach ($anonbody as $valentine) {
			layout_open_box('white');
			show_link_to_ticket_form('valentine',$valentine['ID'],'r');
			?><div class="block"><?= make_all_html($valentine['BODY']);
			?></div><?
			layout_close_box();
		}
	}
}
function user_commit_deceased_info(): bool {
	require_once '_commentsinfo.inc';
	require_once '_postedin.inc';
	if (!have_post()
	||	!require_idnumber($_REQUEST,'sID')
	||	!require_admin('helpdesk')
	||	(	isset($_POST['HAVEDATE'])
		&&	!require_date($_POST)
		)
	||	(	isset($_POST['MOVEGB_C'])
		&&	!require_date($_POST, 'MOVE_')
		)
	) {
		return false;
	}
	$userid = $_REQUEST['sID'];
	$stamp = $movestamp = 0;
	if (isset($_POST['HAVEDATE'])) {
		$stamp = _date_getstamp($_POST);
		if (!$stamp) {
			_error('Overlijdensdatum kon niet bepaald worden!');
			return false;
		}
	}
	if (isset($_POST['MOVEGB_C'])) {
		$movestamp = _date_getstamp($_POST, 'MOVE_');
		if (!$movestamp) {
			_error('Gastenboekverplaatsingsdatum kon niet bepaald worden!');
			return false;
		}
		$movemsgs = db_rowuse_array('user_comment','
			SELECT * FROM user_comment
			WHERE ID='.$userid.'
			  AND CSTAMP>='.$movestamp.'
			ORDER BY COMMENTID ASC',
			DB_USE_MASTER
		);
		if ($movemsgs === false) {
			return false;
		}
		$movemsglogs = db_rowuse_array('user_comment_log','
			SELECT * FROM user_comment_log
			WHERE ID='.$userid.'
			  AND CSTAMP>='.$movestamp.'
			ORDER BY COMMENTID,MSTAMP',
			DB_USE_MASTER
		);
		if ($movemsglogs === false) {
			return false;
		}
		if (!require_getlock('deaduser_comment:'.$userid,15)) {
			return false;
		}
		$msgcnt = db_single('commentsinfo','SELECT MSGCNT FROM commentsinfo WHERE TYPE="deaduser" AND ID='.$userid,DB_USE_MASTER);
		if ($msgcnt === false) {
			return false;
		}
		if ($msgcnt === null) {
			$msgcnt = 0;
		}
		$oldtonew = array();
		foreach ($movemsgs as $movemsg) {
			$oldid = $movemsg['COMMENTID'];
			$movemsg['COMMENTID'] = 0;
			$setlist = array();
			foreach ($movemsg as $attrib => $val) {
				if ($attrib === 'MSGNO') {
					continue;
				}
				if ($attrib === 'BODY') {
					$reps = 0;
					foreach ($oldtonew as $tmpoldid => $tmpnewid) {
						if (str_contains($val,'[quote element=user_comment id='.$tmpoldid.']')) {
							$val = str_replace('[quote element=user_comment id='.$tmpoldid.']','[quote element=deaduser_comment id='.$tmpnewid.']',$val);
							++$reps;
						}
					}
				}
				$setlist[] = $attrib.'="'.addslashes($val).'"';
			}
			$setlist[] = 'MSGNO='.(++$msgcnt);
			if (!db_insert('deaduser_comment','INSERT INTO deaduser_comment SET '.implode(',',$setlist))) {
				return false;
			}
			$newid = db_insert_id();
			$oldtonew[$oldid] = $newid;
			db_update('quoted','
			UPDATE quoted SET
				QOTELEM	="deaduser_comment",
				QOTID	='.$newid.'
			WHERE QOTELEM="user_comment"
			  AND QOTID='.$oldid.'
			  AND PARENTID='.$userid
			);
			db_update('quoted','
			UPDATE quoted SET
				SRCELEM	="deaduser_comment",
				SRCID	='.$newid.'
			WHERE SRCELEM="user_comment"
			  AND SRCID='.$oldid.'
			  AND PARENTID='.$userid
			);
		}
		foreach ($movemsglogs as $movemsglog) {
			$oldid = $movemsglog['COMMENTID'];
			if (!isset($oldtonew[$oldid])) {
				_error('ARGH, kon nieuw nummer bij oud nummer '.$oldid.' niet vinden!');
				continue;
			}
			$movemsglog['COMMENTID'] = $oldtonew[$oldid];
			$setlist = array();
			foreach ($movemsglog as $attrib => $val) {
				if ($attrib === 'BODY') {
					$reps = 0;
					foreach ($oldtonew as $tmpoldid => $tmpnewid) {
						if (str_contains($val,'[quote element=user_comment id='.$tmpoldid.']')) {
							$val = str_replace('[quote element=user_comment id='.$tmpoldid.']','[quote element=deaduser_comment id='.$tmpnewid.']',$val);
							++$reps;
						}
					}
				}
				$setlist[] = $attrib.'="'.addslashes($val).'"';
			}
			if (!db_insert('deaduser_comment_log','INSERT INTO deaduser_comment_log SET '.implode(',',$setlist))) {
				return false;
			}
		}
		if ($oldtonew) {
			if (!db_delete('user_coment','
				DELETE FROM user_comment
				WHERE COMMENTID IN ('.($idstr = implodekeys(',',$oldtonew)).')')
			||	!db_delete('user_comment_log','
				DELETE FROM user_comment_log
				WHERE COMMENTID IN ('.$idstr.')')
			) {
				return false;
			}
		}
		recreate_postedin('user',$userid);
		recreate_postedin('deaduser',$userid);
		update_commentsinfo('user',$userid,CMTI_FORCE_RESET);
		update_commentsinfo('deaduser',$userid,CMTI_FORCE_RESET);
	}
	if ($stamp) {
		if (!db_insert('going_log','
			INSERT INTO going_log
			SELECT going.*,"deceased" AS ACTION,'.CURRENTSTAMP.' AS ASTAMP FROM going
			JOIN party USING (PARTYID)
			WHERE going.USERID='.$userid.'
			  AND STAMP>'.$stamp)
		||	!db_delete('going','
			DELETE going
			FROM going
			JOIN party USING (PARTYID)
			WHERE going.USERID='.$userid.'
			  AND STAMP>'.$stamp)
		) {
			return false;
		}
		$aff = db_affected();
		if ($aff) {
			_notice($aff > 1 ? $aff.' toekomstige feesten uit agenda verwijderd.' : '1 toekomstig feest uit agenda verwijderd.');
		}
	}
	if (!really_set_user_status($userid,'deceased')
	||	!db_update('externalsettings','
		UPDATE IGNORE externalsettings SET
			VISIBILITY_WRITEGUESTBOOK=3
		WHERE USERID='.$userid)
	||	!db_insert('deceasedinfo_log','
		INSERT INTO deceasedinfo_log
		SELECT * FROM deceasedinfo
		WHERE USERID='.$userid)
	||	!db_replace('deceasedinfo','
		REPLACE INTO deceasedinfo SET
			USERID	='.$userid.',
			DSTAMP	='.$stamp.',
			MOVEGB	='.$movestamp.',
			MSTAMP	='.CURRENTSTAMP.',
			MUSERID	='.CURRENTUSERID.',
			CONDOLENCES='.(isset($_POST['CONDOLENCES']) ? 1 : 0))
	) {
		return false;
	}
	_notice('Lid op overleden gezet.');
	return true;
}
function user_display_deceased_info() {
	if (!($userid = $_REQUEST['sID'])
	||	!require_admin('helpdesk')
	) {
		return;
	}
	layout_show_section_header(Eelement_name('deceased_info'));

	if (!($user = db_single_assoc('user_account', "
		SELECT STATUS, NICK
		FROM user_account
		WHERE USERID = $userid"
	))) {
		if ($user === null) {
			register_error('user:error:nonexistent_LINE');
		}
		return;
	}
	$write_guestbook = external_setting($userid, 'VISIBILITY_WRITEGUESTBOOK');
	$open_guestbook = $write_guestbook !== null && $write_guestbook !== NOBODY;

	if (false === ($deceased_info = db_single_assoc('deceasedinfo',"
		SELECT *
		FROM deceasedinfo
		WHERE USERID = $userid"
	))) {
		return;
	}
	if ($open_guestbook) {
		layout_open_box('white');
		?><div class="block">Gastenboek wordt automatisch gesloten. Kijk dus vanaf wanneer de condoleances in het gastenboek beginnen en geef die datum op, dan <?
		?>kunnen die condoleances naar het offici&euml;le condoleance gedeelte verplaatst worden.</div><?
		layout_close_box();
	}
	?><form<?
	?> method="post"<?
	?> onsubmit="return submitForm(this)" action="/user/<?= $_REQUEST['sID'] ?>/commitdeceased"<?
	?>><?
	layout_open_box('white');
	layout_open_table();
	// DSTAMP
	layout_start_row();
	?><label for="havedate"><?= Eelement_name('date_of_death') ?></label><?
	layout_field_value();
	?><input type="checkbox" value="1" name="HAVEDATE" id="havedate"<?
	if (!empty($deceased_info['DSTAMP'])) {
		?> checked<?
	}
	?> onclick="setdisplay('dstamp',this.checked);"> <span id="dstamp"<?
	if (empty($deceased_info['DSTAMP'])) {
		?> class="hidden"<?
	}
	?>><?
	_date_display_select_stamp(empty($deceased_info['DSTAMP']) ? CURRENTSTAMP : $deceased_info['DSTAMP'],null,-20,0);
	?></span><?
	// CONDOLENCES
	layout_restart_row();
	?><label for="condolences"><?= Eelement_plural_name('condolence') ?></label><?
	layout_field_value();
	?><input type="checkbox" value="1" name="CONDOLENCES" id="condolences"<?
	if (!empty($deceased_info['CONDOLENCES'])
	||	$open_guestbook
	) {
		?> checked="checked"<?
	}
	?>><?

	if ($open_guestbook) {
		layout_restart_row();
		?><label for="movegb-c">Gastenboek verplaatsen</label><?
		layout_field_value();
		?><label for="movegb-c"><input type="checkbox" value="1" name="MOVEGB_C" <?
		?> id="movegb-c" onclick="setdisplay('movegb',this.checked)"></label><span id="movegb" class="hidden"> vanaf <?
		global $__year;
		if ($deceased_info) {
			[$y,$m,$d] = _getdate($deceased_info['DSTAMP']);
			_date_display_select('MOVE_',$y,$m,$d,$y-2,$__year);
		} else {
			_date_display_select('MOVE_',0,0,0,$__year-20,$__year);
		}
		_time_display_select('MOVE_',0,0);
		?></span><?
	}
	layout_stop_row();
	layout_close_table();
	layout_close_box();
	?><div class="block"><input type="submit" value="<?= __('action:change'); ?>" /></div></form><?
}

function user_refresh_stats(): bool {
	if (!require_admin('helpdesk')
	||	!($userid = require_idnumber($_REQUEST, 'sID'))
	||	false === ($dmsg_sent				= db_single_int('directmessage',	'SELECT COUNT(*) FROM directmessage WHERE FROM_USERID='.$userid))
	||	false === ($dmsg_received			= db_single_int('directmessage',	'SELECT COUNT(*) FROM directmessage WHERE   TO_USERID='.$userid))
	||	false === ($forum_messages_posted	= db_single_int('message',			'SELECT COUNT(*) FROM message		WHERE	   USERID='.$userid))
	||	false === ($forum_topics_opened		= db_single_int('topic',			'SELECT COUNT(*) FROM topic			WHERE	   USERID='.$userid))
	||	false === ($last_dmsgid	= db_single_int('directmessage', "
		SELECT MESSAGEID
		FROM directmessage
		WHERE NOT (READM = 0 AND TO_DELETED)
		  AND TO_USERID = $userid
		ORDER BY MESSAGEID DESC
		LIMIT 1"))
	||	!db_insupd('user_cache', "
		INSERT INTO user_cache SET
			USERID					= $userid,
			DMSG_SENT				= $dmsg_sent,
			DMSG_RECEIVED			= $dmsg_received,
			FORUM_MESSAGES_POSTED	= $forum_messages_posted,
			FORUM_TOPICS_OPENED		= $forum_topics_opened,
			LAST_DMSGID				= $last_dmsgid
		ON DUPLICATE KEY UPDATE
			DMSG_SENT				= VALUE(DMSG_SENT),
			DMSG_RECEIVED			= VALUE(DMSG_RECEIVED),
			FORUM_MESSAGES_POSTED	= VALUE(FORUM_MESSAGES_POSTED),
			FORUM_TOPICS_OPENED		= VALUE(FORUM_TOPICS_OPENED),
			LAST_DMSGID				= VALUE(LAST_DMSGID)")
	) {
		return false;
	}
	register_notice('user:notice:statistics_refreshed_LINE');
	return true;
}
function user_set_status(): bool {
	if (!($userid = require_idnumber($_REQUEST,'sID'))
	||	!require_element($_REQUEST, 'STATUS', ['active', 'deceased', 'deleted', 'inactive'])
	||	!require_admin('helpdesk')
	) {
		return false;
	}
	$status = db_single('user_account','SELECT STATUS FROM user_account WHERE USERID='.$userid);
	if ($status === false) 	{
		return false;
	}
	if (!$status) {
		not_found();
		return false;
	}
	if ($status == $_REQUEST['STATUS']) {
		// no need for change
		return true;
	}
	if ($status === 'permbanned'
	||	$status === 'banned'
	) {
		register_error('user:error:cannot_change_status_of_banned_LINE');
		return false;
	}
	if ($status === 'deceased') {
		if (!db_insert('deceasedinfo_log','
			INSERT INTO deceasedinfo_log
			SELECT * FROM deceasedinfo
			WHERE USERID='.$userid)
		||	!db_delete(
			'deceasedinfo','
			DELETE FROM deceasedinfo
			WHERE USERID='.$userid)
		) {
			return false;
		}
	}
	return really_set_user_status($userid,$_REQUEST['STATUS'],$status);
}
function really_set_user_status($userid, $newstatus, $oldstatus = null): bool {
	if (!change_user_status($userid,$newstatus,CURRENTUSERID)) {
		return false;
	}
	if ($newstatus === 'deleted') {
		if (!db_insert('deleteinfo','
			INSERT INTO deleteinfo SET
				TYPE	="'.$newstatus.'",
				USERID	='.CURRENTUSERID.',
				BYUSERID='.CURRENTUSERID.',
				DSTAMP	='.CURRENTSTAMP.',
				IPBIN	="'.addslashes(CURRENTIPBIN).'",
				IDENTID	='.CURRENTIDENTID)
		) {
			return false;
		}
	}
	return true;
}
function user_display_ignorelist() {
	require_once '_ignores.inc';
	if (!require_idnumber($_REQUEST,'sID')
	||	!require_self_or_admin($_REQUEST['sID'],'helpdesk')
	) {
		return;
	}
	$userid = $_REQUEST['sID'];
	layout_open_section_header();
	?>Mensen die <?
	if (CURRENTUSERID == $_REQUEST['sID']) {
		?>je<?
	} else {
		echo get_element_link('user',$userid);
	}
	?> niet wil zien<?
	layout_close_section_header();

	if (!require_user()) {
		return;
	}
	$userlist = db_rowuse_array(
		array('ignorelist','user_account'),'
		SELECT IGNOREID,FLAGS,NICK
		FROM ignorelist
		JOIN user_account ON user_account.USERID=ignorelist.IGNOREID
		WHERE ignorelist.USERID='.$userid.'
		ORDER BY NICK ASC'
	);
	if (!$userlist) {
		return;
	}
	layout_open_box('white');
	layout_open_table();
	layout_start_header_row();
	layout_start_header_cell();
	?>Wie<?
	layout_next_header_cell();
	?>Wat<?
	layout_stop_header_cell();
	layout_stop_header_row();
	foreach ($userlist as $iuser) {
		layout_start_row();
		echo get_element_link('user',$iuser['IGNOREID'],$iuser['NICK']);
		layout_field_value();
		$ilist = get_ignore_names($iuser['FLAGS']);
		if ($ilist) {
			echo implode(', ',$ilist);
		}
		layout_stop_row();
	}
	layout_close_table();
	layout_close_box();
}
function user_display_blocklist() {
	require_once '_blocks.inc';
	if (!require_idnumber($_REQUEST,'sID')
	||	!require_self_or_admin($_REQUEST['sID'],'helpdesk')
	) {
		return;
	}
	$userid = $_REQUEST['sID'];
	layout_open_section_header();
	echo __(CURRENTUSERID === $userid ? 'user:blocklist:header_you' : 'user:blocklist:header',DO_UBB, ['USERID' => $userid]);
	layout_close_section_header();

	if (!require_user()) {
		return;
	}
	if (!($userlist = db_rowuse_array(
		array('blocklist','user_account'),'
		SELECT BLOCKID,FLAGS,NICK
		FROM blocklist
		JOIN user_account ON user_account.USERID=BLOCKID
		WHERE blocklist.USERID='.$_REQUEST['sID'].'
		ORDER BY NICK'))
	) {
		return;
	}
	layout_open_box('white');
	layout_open_table();
	layout_start_header_row();
	layout_start_header_cell();
	echo Eelement_name('who');
	layout_next_header_cell();
	echo Eelement_name('what');
	layout_stop_header_cell();
	layout_stop_header_row();
	foreach ($userlist as $iuser) {
		layout_start_row();
		echo get_element_link('user',$iuser['BLOCKID'],$iuser['NICK']);
		layout_field_value();
		$ilist = get_block_names($iuser['FLAGS']);
		if ($ilist) {
			echo implode(', ',$ilist);
		}
		layout_stop_row();
	}
	layout_close_table();
	layout_close_box();
}
function user_delete() {
	if (!require_user()) {
		return;
	}
	layout_open_section_header();
	echo Eelement_name('user') ?> <?= MIDDLE_DOT_ENTITY ?> <? echo __('action:delete_profile');
	layout_close_section_header();

	$cstamp = db_single('user','SELECT CSTAMP FROM user WHERE USERID='.CURRENTUSERID);
	if ($cstamp === false) {
		return;
	}
	if (!have_post()) {
		?><form<?
		?> method="post"<?
		?> onsubmit="return submitForm(this)"<?
		?> action="/user/delete"<?
		?>><?
		show_form_secret();
		?><div class="block"><?= __('user:info:sure_to_delete_enter_password_TEXT',DO_NL2BR)
		//Weet je zeker dat je jezelf wilt verwijderen?</p><p>Vul dan hieronder je wachtwoord in:
		?></div><?
		?><div class="block"><?
		?><input type="password" name="CHECKPASSWD" required="required" autocomplete="current-password" /><?
		?></div><?
		?><div class="block"><?
		?><input type="submit" name="DELETE" value="<?= __('action:remove_me') ?>" /> <?
		?></div><?
		?><h3>Je kunt een verwijdering niet zelf meer ongedaan maken!</h3><?
		?></form><?
		return;
	}
	if (!require_something_trim($_POST, 'CHECKPASSWD')
	||	!require_valid_origin_or_secret()
	) {
		return;
	}
	global $currentuser;
	if (!$currentuser->verify_password($_POST['CHECKPASSWD'])) {
		return;
	}
	require_once '_status.inc';
	$newstatus = 'deleted';
	if (!db_insert('deleteinfo','
		INSERT INTO deleteinfo SET
			TYPE	="'.$newstatus.'",
			USERID	='.CURRENTUSERID.',
			BYUSERID='.CURRENTUSERID.',
			DSTAMP	='.CURRENTSTAMP.',
			IPBIN	="'.addslashes(CURRENTIPBIN).'",
			IDENTID	='.CURRENTIDENTID)
	||	!change_user_status(CURRENTUSERID,$newstatus)
	) {
		return;
	}
	register_notice('user:notice:profile_'.$newstatus.'_LINE');
}
function user_display_birthdays() {
	layout_show_section_header();

	require_once '_userlist.inc';
	$userlist = new _userlist;
	if (have_idnumber($_REQUEST,'MONTH')
	&&	have_idnumber($_REQUEST,'DAY')
	&&	($month = $_REQUEST['MONTH']) <= 12
	&&	($day = $_REQUEST['DAY']) <= 31
	&&	checkdate($month,$day,2008)
	) {
#		layout_show_section_header('Jarig op '.$day.' '._month_name($month));
		$userlist->born_on_day_and_month($day,$month);
	} else {
#		layout_show_section_header('Vandaag jarig');
		$userlist->birthday_today();
	}
	if (!isset($_REQUEST['NOMENU'])) {
		main_menu();
	}
	if (isset($_REQUEST['ONLYBUDDIES'])) {
		$userlist->only_buddies_as_filter();
	}
	$userlist->only_active();
	$userlist->show_age = true;
	if (have_user()) {
		$userlist->show_heart = true;
	}
	if (!$userlist->query()) {
		return;
	}
	if (!$userlist->size) {
		?><p>Geen verjaardagen.</p><?
		return;
	}
	layout_open_box('white');
	layout_box_header(__C('date:birthday_today'));
	$userlist->display();
	layout_close_box();
}

function user_display_ignore_form() {
	require_once '_ignores.inc';
	if (!require_idnumber($_REQUEST,'sID')
	||	!require_user()
	||	($ignoreid = $_REQUEST['sID']) == CURRENTUSERID
	) {
		return;
	}
	if (!($user = db_single_assoc('user','SELECT NICK,SEX FROM user WHERE USERID='.$ignoreid))) {
		if ($user === false) {
			return;
		}
		not_found();
		return;
	}
	layout_show_section_header(__C('action:ignore').' '.MIDDLE_DOT_ENTITY.' '.get_element_link('user',$ignoreid));

	if (false === ($ignore = db_single('ignorelist','
		SELECT FLAGS
		FROM ignorelist
		WHERE USERID='.CURRENTUSERID.'
		  AND IGNOREID='.$_REQUEST['sID'],
		DB_USE_MASTER))
	) {
		return;
	}
	?><form<?
	?> method="post"<?
	?> onsubmit="return submitForm(this)"<?
	?> action="/user/<?= $ignoreid ?>/ignorecommit"<?
	?>><?
	layout_open_box('white');

	?><table class="fw"><?

	foreach (array(
		IGNORE_FORUM_TOPICS		=> 'forum_topics',
		IGNORE_FORUM_MESSAGES	=> 'forum_messages',
		IGNORE_FLOCK_TOPICS		=> 'flock_topics',
		IGNORE_FLOCK_MESSAGES	=> 'flock_messages',
		IGNORE_COMMENTS			=> 'comments',
		IGNORE_PRIVATE_MESSAGES	=> 'private_messages',
		IGNORE_CHAT_MESSAGES	=> 'chat_messages',
		IGNORE_VALENTINE_HEARTS	=> 'valentine_hearts',
		IGNORE_BUDDY_REQUEST	=> 'friendship_requests',
		IGNORE_COMPLETE			=> 'completely',
	) as $flg => $type) {
		layout_start_rrow(0,($ignore & $flg ? null : 'not-').'bold-hilited','rpad');
		show_input(array(
			'name'		=> 'IGNORE[]',
			'id'		=> 'lbl_'.$type,
			'type'		=> 'checkbox',
			'class'		=> 'upLite',
			'value'		=> $flg,
			'checked'	=> ($ignore & $flg) ? true : false,
		));
		layout_next_cell();
		?><label for="lbl_<?= $type ?>"><b><?= __C('action:ignore_'.$type) ?></b></label><?
		layout_restart_rrow();
		layout_next_cell();
		echo __('ignore:info:'.$type.'_TEXT',DO_UBB,array(
			'IGNOREID'	=> $ignoreid
		));
		layout_stop_row();
	}
	?></table><?
	layout_close_box();
	?><div class="block"><input type="submit" value="<?= __('action:change') ?>" /></div><?
	?></form><?
}
function user_commit_ignore(): bool {
	require_once '_ignores.inc';
	if (!require_user()
	||	!require_idnumber($_REQUEST,'sID')
	||	!optional_hash($_POST, 'IGNORE',HASH_NUMBER,HASH_NUMBER)
	||	!db_insert('ignorelist_log','
		INSERT INTO ignorelist_log
		SELECT * FROM ignorelist
		WHERE USERID='.CURRENTUSERID.'
		  AND IGNOREID='.($ignoreid = $_REQUEST['sID']))
	) {
		return false;
	}
	if (isset($_POST['IGNORE'])) {
		$flags = 0;
		foreach ($_POST['IGNORE'] as $flag) {
			$flags |= $flag;
		}
		if (!db_insupd('ignorelist','
			INSERT INTO ignorelist SET
				USERID	='.CURRENTUSERID.',
				MSTAMP	='.CURRENTSTAMP.',
				IGNOREID='.$ignoreid.',
				FLAGS	='.$flags.'
			ON DUPLICATE KEY UPDATE
				MSTAMP	=VALUES(MSTAMP),
				FLAGS	=VALUES(FLAGS)')
		) {
			return false;
		}
		register_notice('user:ignore:changed_LINE');
		#'Negeerinstellingen aangepast.');
	} else {
		if (!db_delete('ignorelist','
			DELETE FROM ignorelist 
			WHERE USERID='.CURRENTUSERID.'
			  AND IGNOREID='.$ignoreid)
		) {
			return false;
		}
		_notice(__('user:ignore:removed_LINE'));
		#'Negeerinstellingen verwijderd.');
	}
	flush_ignores(CURRENTUSERID);
	return true;
}
function user_display_block_form() {
	require_once '_blocks.inc';
	if (!require_idnumber($_REQUEST,'sID')
	||	!require_user()
	||	$_REQUEST['sID'] == CURRENTUSERID
	) {
		return;
	}
	$userid = $_REQUEST['sID'];
	$gender = db_single('user','SELECT SEX FROM user WHERE USERID='.$userid);
	if (!$gender && $gender !== '') {
		not_found(); return;
	}
	$arg = array('USERID'=>$userid);
	layout_open_section_header();
	echo __('user:block:header',DO_UBB,$arg);
	layout_close_section_header();

	$block = db_single('ignorelist','
		SELECT FLAGS
		FROM blocklist
		WHERE USERID='.CURRENTUSERID.'
		  AND BLOCKID='.$userid,
		DB_USE_MASTER
	);
	if ($block === false) {
		return;
	}
	?><div class="block"><?= __('user:block:info_LINE', DO_UBB, $arg); ?></div><?
	?><form<?
	?> method="post"<?
	?> onsubmit="return submitForm(this)"<?
	?> action="/user/<?= $userid; ?>/blockcommit"<?
	?>><?
	layout_open_box('white');

	?><table class="fw"><?

	foreach ([
		'album'			=> BLOCK_ALBUM,
		'guestbook'		=> BLOCK_GUESTBOOK,
		'weblog'		=> BLOCK_WEBLOG,
		'private_messages'	=> BLOCK_PRIVATE_MESSAGES,
		'valentine_hearts'	=> BLOCK_VALENTINE_HEARTS,
		'buddy_request'		=> BLOCK_BUDDY_REQUESTS,
	] as $what => $flag) {
		?><tr class="<?
		if (!($block & $flag)) {
			?>not-<?
		}
		?>bold-hilited"><td class="center hpad"><?
			?><input<?
			?> class="upLite"<?
			?> id="block-<?= $what ?>"<?
			?> type="checkbox"<?
			?> value="<?= $flag ?>"<?
			?> name="BLOCK[]"<?
			if ($block & $flag) {
				?> checked="checked"<?
			}
			?> onclick="unhide('submitpart')"><?
		layout_next_cell();
			?><label for="block-<?= $what; ?>"><b><?= __C('user:block:'.$what); ?></b></label><?
		layout_restart_rrow();
		layout_next_cell();
			echo __('user:block:'.$what.'_info_TEXT',DO_NL2BR | DO_UBB,$arg);
		layout_stop_row();
	}
	layout_close_table();
	layout_close_box();
	?><div id="submitpart" class="hidden block"><input type="submit" value="<?= __('action:change'); ?>" /></div></form><?
}
function user_commit_block(): bool {
	require_once '_blocks.inc';
	if (!require_user()
	||	!require_idnumber($_REQUEST,'sID')
	||	!optional_hash($_POST, 'BLOCK', HASH_NUMBER, HASH_NUMBER)
	||	!db_insert('blocklist_log','
		INSERT INTO blocklist_log
		SELECT * FROM blocklist
		WHERE USERID='.CURRENTUSERID.'
		  AND BLOCKID='.($blockid = $_REQUEST['sID']))
	) {
		return false;
	}
	if (isset($_POST['BLOCK'])) {
		$flags = 0;
		foreach ($_POST['BLOCK'] as $flag) {
			$flags |= $flag;
		}
		if (!db_insupd('blocklist','
			INSERT INTO blocklist SET
				USERID	='.CURRENTUSERID.',
				MSTAMP	='.CURRENTSTAMP.',
				BLOCKID	='.$blockid.',
				FLAGS	='.$flags.'
			ON DUPLICATE KEY UPDATE
				MSTAMP	=VALUES(MSTAMP),
				FLAGS	=VALUES(FLAGS)')
		) {
			return false;
		}
		register_notice('user:block:notice:changed_LINE');
	} else {
		if (!db_delete('blocklist','
			DELETE FROM blocklist 
			WHERE USERID='.CURRENTUSERID.'
			  AND BLOCKID='.$blockid)
		) {
			return false;
		}
		_notice(__('user:block:notice:removed_LINE'));
	}
	flush_blocks($blockid);
	return true;
}
function user_commit_personalnote(): bool {
	require_once '_ubb_preprocess.inc';

	if (!isset($_POST['STORE'])) {
		if (isset($_POST['BODY'])) {
			$_POST['BODY'] = _ubb_preprocess($_POST['BODY'], utf8: true);
		}
		return true;
	}
	if (!require_user()
	||	!require_anything_trim($_POST, 'BODY', utf8: true)
	) {
		return false;
	}

	$userid =
		have_super_admin()
	&&	have_idnumber($_REQUEST, 'sID')
	?	$_REQUEST['sID']
	:	CURRENTUSERID;

	if ($userid !== CURRENTUSERID
	&&	!require_super_admin()
	) {
		return false;
	}
	require_once '_ubb_preprocess.inc';
	if (empty($_POST['BODY'])) {
		if (!db_insert('personalnote_log', '
			INSERT INTO personalnote_log
			SELECT *, '.CURRENTSTAMP."
			FROM personalnote
			WHERE USERID = $userid")
		||	!db_delete('personalnote', "
			DELETE FROM personalnote
			WHERE USERID = $userid")
		) {
			return false;
		}
		register_notice('user:notice:personalnote_removed_LINE');
	} else {
		$slashed_body = addslashes($body = _ubb_preprocess($_POST['BODY'], utf8: true));

		if (!db_insert('personalnote_log', '
			INSERT INTO personalnote_log
			SELECT *, '.CURRENTSTAMP."
			FROM personalnote
			WHERE NOT (BODY = BINARY '$slashed_body')
			  AND USERID = $userid")
		||	!db_replace('personalnote', "
			REPLACE INTO personalnote SET
				BODY		= '$slashed_body',
				STAMP		= ".CURRENTSTAMP.',
				USERID		= '.$userid)
		) {
			return false;
		}
		register_notice('user:notice:personalnote_changed_LINE');
		taint_include_cache('personalnote', $userid, $_POST['BODY']);
	}
	return true;
}

function user_show_personalnote(): void {
	layout_show_section_header(Eelement_name('personalnote'));

	if (!require_user()) {
		return;
	}

	$userid =
		have_super_admin()
	&&	have_idnumber($_REQUEST,'sID')
	?	$_REQUEST['sID']
	:	CURRENTUSERID;

	if (!isset($_POST['BODY'])) {
		if (false === ($personalnote = db_single_assoc('personalnote', 'SELECT BODY, STAMP FROM personalnote WHERE USERID='.$userid))) {
			return;
		}
		if ($personalnote) {
			?><div class="block"><? __('alteration:lastchange', ['DATETIME' => _datedaytime_get($personalnote['STAMP'])]) ?></div><?
		}
		$body = $personalnote['BODY'] ?? null;
	} else {
		$body = $_POST['BODY'];
	}

	if ($body) {
		layout_open_box('white');
		?><div class="body block"><?= make_all_html($body, UBB_UTF8, 'personalnote', $userid) ?></div><?
		layout_close_box();
	}
	?><form<?
	?> accept-charset="utf-8"<?
	?> method="post"<?
	?> onsubmit="return submitForm(this)"<?
	?> action="/user/<?= $userid ?>/commitpersonalnote"><?
	?><div class="block"><?
	show_textarea([
		'class'		=> 'growToFit',
		'data-max'	=> 20,
		'cols'		=> 80,
		'name'		=> 'BODY',
		'id'		=> 'body',
		'value_utf8'	=> $body
	]);
	?></div><?
	?><div class="block"><?
		?><input type="submit" name="STORE" value="<?= __('action:store') ?>" /> <?
		?><input type="submit" name="PREVIEW" value="<?= __('action:preview') ?>" /><?
	?></div></form><?
}

function user_show_online() {
	$suitable = $_REQUEST['SUBACTION'] === 'suitable';
	layout_show_section_header(Eelement_plural_name($suitable ? 'suitable_online_user' : 'online_user'));

	main_menu();

	if (!$suitable) {
		layout_open_box('white');
		?><div class="body block"><?= __('user:info:show_online_TEXT',DO_NL2BR) ?></div><?
		?><div class="body block"><?= __('user:info:show_suitable_option_TEXT',DO_NL2BR | DO_UBB) ?></div><?
		layout_close_box();
	}

	require_once '_userlist.inc';
	$userlist = new _userlist;
	$userlist->show_camera = true;
	$userlist->show_heart = true;
	if (isset($_REQUEST['ONLYBUDDIES'])) {
		$userlist->only_buddies_as_filter();
		$userlist->only_online_respect_privacy();
	} else {
		$userlist->only_online_respect_privacy();
		#$userlist->only_available();
		$userlist->show_dating = true;
	}
	$userlist->only_online();
	$userlist->only_registered();
	if (!$userlist->query()) {
		return;
	}
	layout_open_box('white');
	layout_box_header(__C('status:now_online'));
	if ($userlist->size) {
		$userlist->display();
	} else {
		?><div class="block"><?= Eelement_name('nobody') ?>.</div><?
	}
	layout_close_box();
}

function user_make_buddy(): bool {
	require_once '_ignores.inc';
	require_once '_notify.inc';
	if (!require_user()
	||	!require_referer()
	||	!($userid = require_idnumber($_REQUEST,'sID'))
	) {
		return false;
	}
	if ($userid === CURRENTUSERID) {
		register_error('buddy_request:error:cannot_be_your_own_buddy_LINE');
		return false;
	}
	if (db_single('buddy_request','
		SELECT 1
		FROM buddy_request
		WHERE USERID_ACC = '.$userid.'
		  AND USERID_INI = '.CURRENTUSERID)
	) {
		register_notice('buddy_request:notice:already_active_LINE', ['USERID' => $userid]);
		return false;
	}
	if (!is_reachable($userid, NOTIFY_BUDDY)) {
		register_error('buddy_request:error:can_only_make_active_users_a_buddy_LINE', ['USERID' => $userid]);
		return false;
	}
	if ($userid > CURRENTUSERID) {
		$maxid = $userid; $minid = CURRENTUSERID;
	} else {
		$minid = $userid; $maxid = CURRENTUSERID;
	}
	if (!require_getlock($lockname = "buddy_{$minid}_$maxid")) {
		return false;
	}
	if (false === ($is_already_buddy = db_single('buddy','
		SELECT 1
		FROM buddy
		WHERE USERID_INI='.CURRENTUSERID.'
		  AND USERID_ACC='.$userid.'
		   OR USERID_INI='.$userid.'
		  AND USERID_ACC='.CURRENTUSERID))
	) {
		return false;
	}
	if ($is_already_buddy) {
		register_warning('buddy_request:warning:already_a_buddy_LINE', ['USERID' => $userid]);
		return true;
	}
	if (false === ($have_buddy_request = db_single('buddy_request','
		SELECT 1
		FROM buddy_request
		WHERE USERID_INI = '.$userid.'
		  AND USERID_ACC = '.CURRENTUSERID))
	) {
		return false;
	}
	if ($have_buddy_request) {
		// ack existing request
		if (!db_insert('buddy', '
			INSERT IGNORE INTO buddy SET
				ASTAMP		='.CURRENTSTAMP.',
				USERID_INI	='.$userid.',
				USERID_ACC	='.CURRENTUSERID)
		||	!db_insert('buddy_request_log','
			INSERT INTO buddy_request_log (USERID_INI, USERID_ACC, CSTAMP, DSTAMP, DUSERID)
			SELECT USERID_INI, USERID_ACC, CSTAMP, '.CURRENTSTAMP.', 0
			FROM buddy_request
			WHERE USERID_INI = '.$userid.'
			  AND USERID_ACC = '.CURRENTUSERID.'
			   OR USERID_INI = '.CURRENTUSERID.'
			  AND USERID_ACC = '.$userid)
		||	!db_delete('buddy_request', '
			DELETE FROM buddy_request
			WHERE USERID_INI = '.$userid.'
			  AND USERID_ACC = '.CURRENTUSERID.'
			   OR USERID_INI = '.CURRENTUSERID.'
			  AND USERID_ACC = '.$userid)
		) {
			return false;
		}
		reigster_notice('buddy_request:notice:now_a_buddy_LINE', ['USERID' => $userid]);
	} else {
		if (false === ($buddy_count = db_single(['buddy_request', 'user_account'], '
			SELECT COUNT(*)
			FROM buddy_request
			JOIN user_account ON USERID = USERID_ACC
			WHERE STATUS = "active"
			  AND USERID_INI = '.CURRENTUSERID))
		) {
			return false;
		}
		if ($buddy_count > MAX_BUDDY_REQUESTS) {
			register_error('user:error:too_many_buddy_requests_LINE', ['MAX' => MAX_BUDDY_REQUESTS]);
		} else {
			if (!db_insert('buddy_request','
				INSERT IGNORE INTO buddy_request SET
					CSTAMP		='.CURRENTSTAMP.',
					USERID_ACC	='.$userid.',
					USERID_INI	='.CURRENTUSERID)
			) {
				return false;
			}
			$ignores = ignores(IGNORE_BUDDY_REQUEST, $userid);
			if (!isset($ignores[CURRENTUSERID])) {
				notify_register_have(NOTIFY_BUDDY, CURRENTSTAMP, $userid);
			}
			register_notice('buddy_request:notice:sent_LINE', ['USERID' => $userid]);
		}
	}
	db_releaselock($lockname);
	return true;
}
/*function user_display_diagram() {
	if (!($userid = require_idnumber($_REQUEST,'sID'))) {
		http_status(404);
		return;
	}
	if ($_SERVER['REQUEST_METHOD'] != 'GET'	# POST method ??
	||	!have_self($_REQUEST['sID'])
	) {
		$nick = memcached_nick($_REQUEST['sID']);
		require_once '_offense_access.inc';
		if (!require_visible_esetting($userid,'DIAGRAM',have_admin('helpdesk') || have_offense_admin())) {
			http_status(403);
			return;
		}
	} else {
		$nick = $GLOBALS['currentuser']->row['NICK'];
	}
	layout_show_section_header(Eelement_plural_name('statistic'));

	?><p>Aantal pagina's bekeken door <?= get_element_link('user',$_REQUEST['sID'],$nick); ?> in de afgelopen dagen.</p><?
	?><p><img src="/images/diagram/user/<?= $_REQUEST['sID'];
	?>.png" width="400" height="202"></p><?
}*/
function user_display_password_change() {
	global $currentuser;
	$change = $currentuser->action == USERACT_CHANGE_PASSWORD;
	layout_show_section_header(__C('action:change_password'));

	if (have_errors()) {
		http_response_code(400);
		return;
	}
}
function user_display_password_form($change) {
	layout_show_section_header(__C('action:change_password'));

	require_once '_token.inc';
	require_once '_user_password.inc';
	$tokeninfo =
		have_something_trim($_REQUEST,'TOKEN')
	?	validate_token($token = $_REQUEST['TOKEN'], 'reset_password')
	:	null;

	?><form<?= $disabled = $tokeninfo || ($tokeninfo === null && have_user()) ? null : ' disabled';
	?> method="post"<?
	?> onsubmit="return submitPasswordForm(this)"<?
	?> action="/user/commitpasswd"><?

	?><input<?= $disabled ?> type="hidden" name="USERACT" value="<?= $useract = empty($tokeninfo) ? USERACT_CHANGE_PASSWORD : USERACT_RESET_PASSWORD ?>" /><?
#	show_form_secret();
	include_js('js/form/checkpasswd');
	layout_open_box('white');

	if (!empty($tokeninfo)) {
		[$userid, $email] = $tokeninfo;
		layout_box_header(get_element_link('user',$userid));
		?><input<?= $disabled ?> type="hidden" name="TOKEN" value="<?= escape_specials($token) ?>" /><?
	}
	layout_open_table(TABLE_CLEAN);

	if (empty($tokeninfo)) {
		layout_start_row();
		echo Eelement_name('current_password');
		layout_field_value();

		?><input<?= $disabled ?> required type="password" name="PASSWD" maxlength="250" autocomplete="current-password" /><?
		layout_stop_row();
	}
	show_password_form(true, $disabled);
	layout_close_table();
	layout_close_box();
	?><div class="block"><input<?= $disabled ?> type="submit" value="<?= __('action:change_password') ?>" /></div><?
	?></form><?
	forgot_something($useract);
}
function user_set_relation(): bool {
	if (!require_user()
	||	!require_referer()
	||	!require_idnumber($_REQUEST,'sID')
	||	false === require_number($_REQUEST,'subID')
	||	!require_self_or_admin($_REQUEST['sID'],'user')
	) {
		return false;
	}
	if ($_REQUEST['subID'] === $_REQUEST['sID']) {
		_error('Je kunt geen relatie beginnen met jezelf!');
		return false;
	}
	if (!db_replace('user_log','
		REPLACE INTO user_log
		SELECT * FROM user
		WHERE USERID='.$_REQUEST['sID'].(
			$_REQUEST['subID']
		?	' AND (RELATIONID!='.$_REQUEST['subID'].' OR RELATION != "yes")'
		:	' AND RELATIONID!=0'
		))
	||	(	$_REQUEST['subID']
		?	!db_update('user','
			UPDATE user SET
				RELATIONID	='.$_REQUEST['subID'].',
				RELATION	="yes",
				MUSERID		='.CURRENTUSERID.',
				MSTAMP		='.CURRENTSTAMP.'
			WHERE USERID='.$_REQUEST['sID'].'
			  AND (RELATIONID!='.$_REQUEST['subID'].' OR RELATION != "yes")')
		:	!db_update('user','
			UPDATE user SET
				RELATIONID	=0,
				MUSERID		='.CURRENTUSERID.',
				MSTAMP		='.CURRENTSTAMP.'
			WHERE USERID='.$_REQUEST['sID'].'
			  AND RELATIONID!=0')
		)
	) {
		return false;
	}
	register_notice('user:notice:relation_set_LINE');
	require_once '_elementchanged.inc';
	element_changed();
	return true;
}
function user_choose_relation() {
	if (!require_user()) {
		return;
	}
	require_once '_status.inc';
	layout_open_section_header();
	?>Kies wederhelft van je relatie<?
	layout_close_section_header();

	$relationid = db_single('user','SELECT RELATIONID FROM user WHERE USERID='.CURRENTUSERID);
	if ($relationid === false) {
		_error('Relatie van gebruiker met ID '.$relationid.' is niet goed opvraagbaar!');
		return;
	}
	$userlist = db_rowuse_hash(array('buddy','user_account'),'
		(	SELECT USERID_ACC AS USERID,NICK,STATUS
			FROM buddy JOIN user_account ON USERID_ACC=USERID
			WHERE USERID_INI='.CURRENTUSERID.'
		) UNION (
			SELECT USERID_INI AS USERID,NICK,STATUS
			FROM buddy JOIN user_account ON USERID_INI=USERID
			WHERE USERID_ACC='.CURRENTUSERID.'
		)
		ORDER BY NICK ASC'
	);
	if ($userlist === false) {
		return;
	}
	$relationlist = db_rowuse_hash(
		array('user','user_account'),'
		SELECT USERID,NICK,STATUS
		FROM user
		JOIN user_account USING (USERID,NICK)
		WHERE RELATIONID='.CURRENTUSERID
	);
	if ($relationlist === false) {
		return;
	}
	if (!$userlist
	&&	!$relationlist
	) {
		?><p>Je kunt alleen een partner kiezen uit de lijst van jouw vrienden en de lijst van mensen die jou reeds als partner hebben aangeduidt.</p><?
		?><p>Op dit moment heb je geen vrienden en niemand heeft jou als partner aangeduidt!.</p><?
		return;
	}
	?><p>Met wie heb je een relatie? (Zorg ervoor dat je hem of haar eerst 'vriendje' hebt gemaakt!)</p><?
	?><form<?
	?> method="post"<?
	?> onsubmit="return submitForm(this)"<?
	?> action="/user/<?= CURRENTUSERID; ?>/setrelation"><?
	?><p><select name="subID"><option value="0"></option><?

	if ($relationlist) {
		?><optgroup label="Menen relatie van jou te zijn"><?
		foreach ($relationlist as $userid => $relation) {
			?><option<?
			if ($relationid
			&&	$relationid === $userid
			) {
				?> selected="selected"<?
			}
			?> value="<?= $userid; ?>"><? echo escape_specials($relation['NICK']);
			if ($relation['STATUS'] !== 'active') {
				?> (<?= status_name($relation['STATUS']); ?>)<?
			}
			?></option><?
		}
		?></optgroup><?
	}
	if ($userlist) {
		if ($relationlist) {
			?><optgroup label="Overige vrienden"><?
		}
		foreach ($userlist as $userid => $user) {
			?><option<?
			if ($relationid
			&&	$relationid === $userid
			) {
				?> selected="selected"<?
			}
			?> value="<?= $userid; ?>"><? echo escape_specials($user['NICK']);
			if ($user['STATUS'] !== 'active') {
				?> (<?= status_name($user['STATUS']); ?>)<?
			}
			?></option><?
		}
		if ($relationlist) {
			?></optgroup><?
		}
	}
	?></select></p><p><input type="submit" value="<?= __('action:send'); ?>" /></p></form><?
}

function user_display_own_info_form(): void {
	if (!require_user()
	||	!($userid = require_idnumber($_REQUEST, 'sID'))
	||	!require_self_or_admin($_REQUEST['sID'], 'user_text')
	) {
		return;
	}
	$usertext = db_single_assoc(['user_text', 'user'],'
		SELECT BODY, VISIBILITY_BODY, COLOR
		FROM user_text
		JOIN user USING (USERID)
		WHERE user_text.USERID = '.$userid
	);
	if ($usertext === false) {
		return;
	}
	if (!$usertext) {
		$usertext = [
			'BODY'			=> '',
			'VISIBILITY_BODY'	=> EVERYBODY,
			'COLOR'			=> 'yellow',
		];
	}
	layout_show_section_header(Eelement_name('user_text'));

	?><p>Hieronder kun je eigen aanvulling op het standaard profiel doen, extra informatie en wetenswaardigheden over jezelf. <?
	?>Denk er aan dat je je aan <i><a href="/policy">het beleid</a></i> houdt!</p><?
	layout_open_box($usertext['COLOR']);

	?><form<?
	?> accept-charset="utf-8"<?
	?> method="post"<?
	?> onsubmit="return submitForm(this)"<?
	?> action="/user/<?= $_REQUEST['sID'] ?>/textcommit#text"><?

	?><div class="block">Zichtbaar voor <? _visibility_display_form_part($usertext, 'BODY') ?></div><?
	?><div class="block"><?
	show_textarea([
		'name'		=> 'BODY',
		'cols'		=> 80,
		'rows'		=> 30,
		'value_utf8'	=> $usertext['BODY'] ?? '',
	]);
	?></div><?
	layout_close_box();
	?><p><?
	?><input style="float:left" type="submit" name="CHANGE" value="<?= __('action:change'); ?>" /><?
	if (!empty($usertext['BODY'])) {
		?><input onclick="return confirm('<?= __('user_text:confirm:removal_LINE') ?>')" style="float:right" type="submit" name="REMOVE" value="<?= __('action:remove'); ?>" /><?
	}
	?></p></form><?
}

function user_commit_text(): bool {
	if (!($userid = require_idnumber($_REQUEST,'sID'))
	||	!require_anything_trim($_POST, 'BODY', utf8: true)
	||	!require_self_or_admin($_REQUEST['sID'], 'user_text')
	) {
		return false;
	}
	if (empty($_POST['BODY'])
	||	isset($_POST['REMOVE'])
	) {
		if (!db_insert('user_text_log','
			INSERT INTO user_text_log
			SELECT * FROM user_text
			WHERE USERID='.$userid)
		||	!db_delete('user_text','
			DELETE FROM user_text
			WHERE USERID='.$userid)
		||	!db_insert('deleted', "
			INSERT INTO deleted SET
				ELEMENT	= 'user_text',
				ID		= $userid.
				DUSERID	= ".CURRENTUSERID.',
				DSTAMP	= '.CURRENTSTAMP)
		) {
			return false;
		}
		register_notice('user_text:notice:removed_LINE');
		return true;
	}
	if (false === require_number($_POST, 'VISIBILITY_BODY')) {
		return false;
	}
	require_once '_offense.inc';
	require_once '_ubb_preprocess.inc';
	if (!db_insupd('user_text','
		INSERT INTO user_text SET
			USERID		='.$_REQUEST['sID'].',
			BODY		="'.addslashes($body = _ubb_preprocess($_POST['BODY'], utf8: true)).'",
			VISIBILITY_BODY	='.$_POST['VISIBILITY_BODY'].',
			MUSERID		='.CURRENTUSERID.',
			MSTAMP		='.CURRENTSTAMP.',
			ACCEPTED	=1
		ON DUPLICATE KEY UPDATE
			BODY		=VALUES(BODY),
			VISIBILITY_BODY	=VALUES(VISIBILITY_BODY),
			MUSERID		=VALUES(MUSERID),
			MSTAMP		=VALUES(MSTAMP),
			ACCEPTED	=VALUES(ACCEPTED)')
	) {
		return false;
	}
	taint_include_cache('user_text', $_REQUEST['sID'], $body);
	register_notice('user_text:notice:changed_LINE');
	$_REQUEST['ACTION'] = 'text';
	return true;
}

function user_display_samemail() {
	if (!require_admin('helpdesksuper')
	||	!require_something_trim($_REQUEST, 'EMAIL')
	) {
		return;
	}
	require_once '_status.inc';
	layout_open_section_header();
	?>Leden met zelfde email-adres<?
	layout_close_section_header();

	# OPTIM: use same routine twice

	layout_open_box('white');
	layout_box_header(_make_illegible($_REQUEST['EMAIL']));
	?><p><em>huidige</em> adres</p><?

	$userlist = db_rowuse_hash('user','
		SELECT USERID,NICK,REALNAME,CITYID,CSTAMP
		FROM user
		WHERE EMAIL="'.addslashes($_REQUEST['EMAIL']).'"'
	);
	if ($userlist) {
		number_reverse_asort($userlist,'USERID');
		layout_open_table(TABLE_FULL_WIDTH);
		?><tr><?
			?><th class="left"><?= Eelement_name('nick'); ?></th><?
			?><th class="left"><?= Eelement_name('status'); ?></th><?
			?><th class="left"><?= Eelement_name('full_name'); ?></th><?
			?><th class="left"><?= Eelement_name('residence'); ?></th><?
			?><th class="right rpad"><?= __C('field:registered'); ?></th><?
		?></tr><?
		foreach ($userlist as $userid => $user) {
			$statuspart = memcached_user($userid);
			?><tr><td><?= get_element_link('user',$userid);
			?></td><td><?= status_name($statuspart['STATUS']);
			?></td><td><?= escape_specials($user['REALNAME']);
			?></td><td><?
			if ($user['CITYID']) {
				echo get_element_link('city',$user['CITYID']);
			}
			?></td><td class="right rpad"><? _datetime_display($user['CSTAMP']);
			?></td></tr><?
		}
		layout_close_table();
	}
	$userlist = db_rowuse_array(['user_log', 'city', 'user_account'],'
		SELECT	DISTINCT
				user_log.USERID,
				user_log.CSTAMP,
				user_account.NICK,
				user_log.REALNAME,
				user_log.CITYID,
				city.NAME AS CITY_NAME,
				user_account.STATUS
		FROM user_log
		LEFT JOIN city ON city.CITYID = user_log.CITYID
		LEFT JOIN user_account ON user_account.USERID = user_log.USERID
		WHERE user_log.EMAIL = "'.addslashes($_REQUEST['EMAIL']).'"'.
		($userlist ? ' AND user_log.USERID NOT IN ('.implodekeys(',',$userlist).')' : '')
	);
	if ($userlist) {
		?><p><em>oud</em> adres</p><?
		number_reverse_sort($userlist,'USERID');
		layout_open_table(TABLE_FULL_WIDTH);
		?><tr><?
			?><th class="left"><?= Eelement_name('nick') ?></th><?
			?><th class="left"><?= Eelement_name('status') ?></th><?
			?><th class="left"><?= Eelement_name('full_name') ?></th><?
			?><th class="left"><?= Eelement_name('residence') ?></th><?
			?><th class="right rpad"><?= __C('field:registered') ?></th><?
		?></tr><?
		foreach ($userlist as $user) {
			?><tr><td><?= get_element_link('user',$user['USERID'],$user['NICK'])
			?></td><td><?= status_name($user['STATUS'])
			?></td><td><?= escape_specials($user['REALNAME'])
			?></td><td><?
			if ($user['CITYID']) {
				echo get_element_link('city',$user['CITYID'],$user['CITY_NAME']);
			}
			?></td><td class="right rpad"><? _datetime_display($user['CSTAMP']);
			?></td></tr><?
		}
		layout_close_table();
	}
	layout_close_box();
}

function display_nonactive(): void {
	layout_show_section_header(Eelement_plural_name('non_active_user'));
	main_menu();
	require_once '_userlist.inc';
	$userlist = new _userlist;
	$userlist->only_nonactive();
	$userlist->show_heart = true;
	$userlist->show_camera = true;
	if (!$userlist->query()
	||	!$userlist->size
	) {
		return;
	}
	layout_open_box('white');
	$userlist->display();
	layout_close_box();
}

function user_display_last(): void {
	if (!require_admin()) {
		return;
	}
	layout_show_section_header();
	main_menu();
	if (false === ($total = db_single('user_accont','SELECT COUNT(*) FROM party_db.user_account'))) {
		return;
	}
	$perpage = have_super_admin() ? 500 : 50;
	require_once '_pagecontrols.inc';
	$controls = new _pagecontrols;
	$controls->set_total($total);
	$controls->set_per_page($perpage);
	$controls->set_url('/user/last');
	$controls->set_order('USERID');

	if (false === ($lastusers = db_rowuse_array(['user', 'user_account'],'
		SELECT USERID, user.NICK, CSTAMP, STATUS, EMAIL, SITE, COUNTRYID
		FROM user
		JOIN user_account USING (USERID)
		JOIN (
			SELECT USERID
			FROM user_account'.
			$controls->order_and_limit().'
		) AS userpage USING (USERID)'.
		(isset($_REQUEST['NONBANNED']) ? ' WHERE STATUS != "permbanned"' : '')
	))) {
		return;
	}
	$controls->display_and_store();

	if ($lastusers) {
		require_once '_status.inc';
		?><form><?
		?><input type="button" onclick="
		var userids = [];
		for (let input of this.form.elements) {
			if (input.checked) {
				userids.push(input.value);
			}
		}
		this.form['USERIDS'].value = userids.join(',');
		" value="GET LIST"><?
		?><input name="USERIDS" type="text" class="fw" /><?

		include_js('js/iptable');
		require_once '_countryflag.inc';

		?><table class="regular fw small" id="iptable"><tr><th colspan="4" class="left"><?= Eelement_plural_name('most_recent_registration') ?></th></tr><?
		foreach ($lastusers as $user) {
			$column_count = 0;

			ob_start();
			[$year, $month, $day, $hour, $mins] = _getdate($user['CSTAMP']);
			?><tr class="<?

			$ipbin = db_single('last_ip','SELECT IPBIN FROM last_ip WHERE USERID='.$user['USERID']);
			if ($ipbin) {
				$ipstr = @inet_ntop($ipbin);
				$hostname = memcached_try_hostname_from_ipbin($ipbin,$ipstr);
			} else {
				$ipstr = $hostname = '';
			}

			ob_start();

			switch ($user['STATUS']) {
			case 'permbanned':
				?>light error-nb <?
				break;
			default:
				?>light <?
			case 'active':
				if (str_contains($user['EMAIL'], 'yahoo')
				||	str_contains($user['EMAIL'], 'protonmail')
				||	str_contains($user['EMAIL'], 'tutanota')
				||	str_contains($user['EMAIL'], 'scryptmail')
				||	str_starts_with($user['EMAIL'], 'peterd')
				||	preg_match('"gmx\.net$"i', $user['EMAIL'])
				||	preg_match('"\.(?:pl|in|ro|biz|cn|id|ru)$"i', $user['EMAIL'])
				||	preg_match('"\.(?:pl|in|ro|biz|cn|id|ru)$"i', $hostname)
				) {
					?>warning-nb <?
				} elseif (
					$hostname
				&&	in_array($hostname, [$ipstr, 'localhost'], strict: true)
				) {
					?>warning-nb lesscolor7 <?
				}
				break;
			}

			$class = ob_get_flush();

			?>"><?
			if (have_admin('user')) {
				?><td><?
				print_email_link($user['EMAIL']);
				?></td><?
				++$column_count;

				?><td><?
				if ($user['COUNTRYID']) {
					echo get_country_flag($user['COUNTRYID']);
				}
				?></td><?
				++$column_count;

				if ($hostname) {
					?><td><?= escape_specials($hostname) ?></td><?
					++$column_count;
				} elseif ($ipstr) {
					?><td data-ipstr="data-ipstr"><?= escape_specials($ipstr) ?></td><?
					++$column_count;
				} else {
					?><td>NO IP</td><?
					++$column_count;
				}
			}
			if (have_admin()) {
				?><td class="nowrap right"><?= $user['USERID'] ?> <?
				?><input type="checkbox" name="USERID[]" value="<?= $user['USERID'] ?>" /><?
				?></td><?
				++$column_count;
			}
			?><td><?= get_element_link('user',$user['USERID'],$user['NICK']) ?></td><?
			?><td><? show_status_name($user['STATUS'],SSNFLG_SHORT) ?></td><?
			?><td class="nowrap"><? printf($year.'-%02d-%02d %02d:%02d',$month,$day,$hour,$mins) ?></td><?
			$column_count += 3;
			?></tr><?

			if (have_super_admin()
			&&	$user['SITE']
			) {
				?><tr class="<?= $class ?>"><?
				?><td colspan="<?= $column_count ?>" style="overflow:hidden"><?
				?><ul><?
				foreach (explode("\n",$user['SITE']) as $site) {
					?><li><a href="<?= escape_specials($site) ?>"><?= escape_specials($site);?></a></li><?
				}
				?></ul><?
				?></td></tr><?
			}
			echo str_replace('<a ','<a target="_blank" ',ob_get_clean());
		}
		?></table><?
		?></form><?
	}
	$controls->display_stored();
}
function user_display_logoff() {
	layout_show_section_header(__C('action:logoff'));

	register_notice('user:notice:logged_off_LINE');

	require_once '_fbsession.inc';
	fb_logout();

	ob_start();
	if ($ad = obtain_ad(ADPOS_HOMERECTANGLE)) {
		display_ad($ad);
	}
	$rect_ad = ob_get_clean();

	if ($rect_ad) {
		?><div class="center"><?
		if ($rect_ad) {
			?> <div class="ib"><?= $rect_ad ?></div><?
		}
		?></div><?
	}
}

function user_display_login(): void {
	require_once '_login.inc';
	if (!require_login_allowance()
	||	!require_identity()
	) {
		return;
	}
	if ($_REQUEST['ACTION'] === 'impersonate') {
		$useract = USERACT_IMPERSONATE;
		$submit = 'action:impersonate';
	} else {
		$useract = USERACT_LOGIN;
		$submit = 'action:login';
	}

	layout_show_section_header(__C($submit));

	if ($useract === USERACT_LOGIN
	&&	have_user()
	) {
		register_notice('user:notice:already_logged_in_LINE');
		return;
	}
	if (!empty($_REQUEST['TOKEN'])) {
		require_once '_token.inc';
			!($token = mytrim($_REQUEST['TOKEN']))
		||	!($tokeninfo = validate_token($token,'activate_account'));
		if (!empty($tokeninfo)) {
			[$userid] = $tokeninfo;
			if (!change_user_status($userid,'active')) {
				return;
			}
			delete_token($userid,'activate_account');
			register_notice('user:notice:activated_LINE');
		}
	}

	if (!require_writable_partyflock()) {
		return;
	}

	# make sure to delete the facebook logout cookie, which is created by FB.logout(), otherwise automatic token refresh after new login fails:
	clearflockcookie('fblo_'.FACEBOOK_APPID);

	include_js('js/form/user');

	ob_start();

	$disabled = false;
	?><form<?
	?> method="post"<?
	?> onsubmit="return submitForm(this);"<?
	?> action="/user/setuser"<?
	?> id="loginform"><?
 	switch ($useract) {
	case USERACT_IMPERSONATE:
 		$disabled = require_super_admin() ? null : ' disabled';

		?><input type="hidden" name="USERACT" value="<?= USERACT_IMPERSONATE ?>" /><?
		layout_open_box('white');
		layout_open_table('fw');

		layout_start_row();
		?><label for="login"><?= Eelement_name('id') ?></label><?
		layout_field_value();
		?><input<?= $disabled
		?> autofocus<?
		?> required<?
		?> type="number"<?
		?> data-valid="number"<?
		?> id="login"<?
		?> name="NEW_FLOCK_USERID"<?
		?> class="credential"<?
		?> autocomplete="username"><?

 		layout_restart_row();
 		?><label for="current-password"><?= Eelement_name('password') ?></label><?
 		layout_field_value();
 		?><input<?= $disabled
 		?> required<?
 		?> type="password"<?
 		?> name="NEW_FLOCK_PASSWD"<?
 		?> id="current-password"<?
 		?> autocomplete="current-password"><?
 		break;

	default:
		[$autologoff, $logout_others, $single_login] = get_login_settings();

		?><input type="hidden" name="USERACT" value="<?= $useract ?>" /><?
		?><input type="hidden" name="FORMSTAMP" value="<?= time() ?>" /><?

		if (!empty($_SERVER['HTTP_REFERER'])
		&&	preg_match('"^https?://(?:(?:[a-z]+\.)?partyflock\.nl|sandbox(?:too)?|10\.10\.10\.102)(/.*)$"i', $_SERVER['HTTP_REFERER'], $match)
		&&	!preg_match('"/user/(?:login|setuser|logoff)\b"', $_SERVER['HTTP_REFERER'])
		) {
			?><input type="hidden" name="RELFERER" value="<?= escape_specials($match[1]) ?>" /><?
		}

		layout_open_box('white');

		layout_box_header('<img src="'.get_favicon().'" class="middle icon" title="Partyflock" /> '.__C('action:login'));

		layout_open_table('fw');
		layout_start_row();
		?><label for="login"><?= Eelement_name('nick_userid_or_email') ?></label><?

		$prefill_login = $_REQUEST['LOGIN'] ?? null;

		layout_field_value();
		?><input<?
		if (!$prefill_login) {
			?> autofocus<?
		}
		?> spellcheck="false"<?
		?> required<?
		?> type="text"<?
		?> id="login"<?
		?> name="NEW_FLOCK_NICK"<?
		?> maxlength="40"<?
		?> class="credential"<?
		?> autocomplete="username"<?
		if ($prefill_login) {
			?> value="<?= escape_specials($prefill_login) ?>"<?
		}
		?>><?

		layout_restart_row();
		?><label for="current-password"><?= Eelement_name('password') ?></label><?
		layout_field_value();
 		?><input<?
 		if ($prefill_login
 		||	!empty($tokeninfo)
 		) {
 			?> autofocus<?
		}
 		?> required<?
 		?> type="password"<?
 		?> name="NEW_FLOCK_PASSWD"<?
 		?> id="current-password"<?
		?> autocomplete="current-password"<?
 		?>><?

		layout_restart_row();
		echo __C('action:auto_logoff');
		layout_field_value();

		?><select name="AUTOLOGOFF_V2"><?
			?><option value="0"><?= __('answer:no') ?></option><?
			?><option<?
			if ($autologoff) {
				?> selected<?
			}
			?> value="1"><?= __('user:login:when_browser_closes') ?></option><?
		?></select><?

		require_once '_bubble.inc';

		layout_restart_row($single_login ? ROW_HIDDEN : 0,'logout_others_row');
		$bubble = new bubble(BUBBLE_30_WIDE | HELP_BUBBLE);
		$bubble->catcher(__C('action:logoff_elsewhere'), span: 'label', span_attribs: 'for="logout_others"');
		$bubble->content(__('user:bubble:logoff_elsewhere_TEXT',DO_NL2BR));
		$bubble->display_and_cleanup();
		layout_field_value();
		?><input<?
		if ($logout_others) {
			?> checked="checked"<?
		}
		?> type="checkbox" name="LOGOUT_OTHERS" value="1" id="logout_others"><?

		layout_restart_row();
		$bubble->cleanup();
		$bubble->catcher(Eelement_name('single_login'),null,'label','for="single_login"');
		$bubble->content(__('user:bubble:single_login_TEXT',DO_NL2BR));
		$bubble->display();
		layout_field_value();
		?><input<?
		if ($single_login) {
			?> checked="checked"<?
		}
		?> onclick="this.form.LOGOUT_OTHERS.checked=this.checked)" type="checkbox" name="SINGLE_LOGIN" value="1" id="single_login"><?
	}
	layout_stop_row();
	layout_close_table();

	layout_close_box();
	if ($disabled) {
		?><div class="block"><input<?= $disabled ?> type="submit" value="<?= __($submit) ?>" /></div><?
	} elseif ($useract === USERACT_IMPERSONATE) {
		?><div class="block"><input type="submit" value="<?= __($submit) ?>" /></div><?
	} else {
		require_once '_recaptcha.inc';
		show_recaptcha_button('set_user', __($submit), 'captchSubmitForm');
	}

	?></form><?

	forgot_something($useract);

	$regular_login = ob_get_clean();

	require_once '_fbsession.inc';

	if ($useract === USERACT_LOGIN) {
		ob_start();
		fb_show_login_part();
		$fb_login = ob_get_clean();

		if ($fb_login) {
			?><div><?= $fb_login ?></div><?
			?><hr class="slim"><?
		}
		?><div><?= $regular_login ?></div><?
	} else {
		echo $regular_login;
	}
}
function forgot_something($useract) {
	if (($forgot = $useract == USERACT_LOGIN)
	||	($forgot = $useract == USERACT_CHANGE_PASSWORD)
	||	$useract == USERACT_RESET_PASSWORD
	) {
		?><div class="block"><?
		echo	$forgot == USERACT_LOGIN
		?	__('user:info:forgot_password_LINE',DO_UBB,['RELURL'=>'/user/lostpasswd'])
		:	__('user:login:lost_or_expired_activation_token_LINE',DO_UBB,['RELURL'=>'/user/losttoken']);
		?></a></div><?
	}
}
function user_display_loginresult() {
	layout_show_section_header(__C('action:login'));

	global $currentuser;
	if (have_user()) {
		if ($currentuser->action == USERACT_IMPERSONATE) {
			if (!have_super_admin()) {
				register_notice('user:notice:impersonating_LINE',DO_UBB,array('USERID'=>CURRENTUSERID));
			}
			return;
		}

		if (is_server_ip()) {
			require_once '_flockbot.inc';
			flockbot_notify(
				FLOCK_CHANNEL_IMPORTANT,
				'user logged on from server space ip '.CURRENTIPSTR."\n".
				FULL_HOST.get_element_href('user', CURRENTUSERID),
				FLOCKBOT_ASIS
			);
		}

		?><div class="notice block"><?= __('user:notice:now_logged_in_LINE',DO_UBB,array('USERID'=>CURRENTUSERID)) ?></div><?
		$weekago = CURRENTSTAMP - ONE_WEEK;
		if (!empty($currentuser->initial_status)) {
			switch ($currentuser->initial_status) {
			case 'banned':
				?><div class="warning block"><?= __('user:warning:ban_lifted_TEXT',DO_NL2BR) ?></div><?
				break;
			case 'unactive':
				?><div class="notice block"><?= __('user:notice:activated_LINE') ?></div><?
				$cstamp = db_single('user','SELECT CSTAMP FROM user WHERE USERID='.CURRENTUSERID);
				if ($cstamp > $weekago) {
					$weekago = $cstamp;
				}
				break;
			case 'inactive':
				?><div class="notice block"><?= __('user:notice:reactivated_LINE') ?></div><?
				break;
			}
			if (!dont_register()) {
				db_insupd(null,'INSERT INTO loginhit SET DAYNUM='.CURRENTDAYNUM.',HITS=1 ON DUPLICATE KEY UPDATE HITS=HITS+1',DB_FORCE_SERVER,'dbblack');
			}
			if ($currentuser->initial_status !== 'unactive'
			&&	($stamp = db_single('user_data','SELECT LAST_USED FROM user_data WHERE USERID='.CURRENTUSERID,DB_USE_MASTER))
			) {
				?><div class="block"><?= __('user:login:here_last_LINE', ['DATEDAYTIME' => _datedaytime_get($stamp)]) ?></div><?
			}
		}
		$failed_logins = db_single_array('failed_login','
			SELECT COUNT(*),MAX(STAMP)
			FROM failed_login
			WHERE STAMP>'.$weekago.'
			  AND NICK IN ("'.CURRENTUSERID.'","'.addslashes($currentuser->row['NICK']).'","'.addslashes($currentuser->row['EMAIL']).'")'
		);
		if ($failed_logins) {
			[$cnt,$last] = $failed_logins;
			if ($cnt) {
				?><div class="<?
				if ($cnt > 10) {
					?>error <?
				} elseif ($cnt > 2) {
					?>warning <?
				}
				?>block"><?= __('user:info:failed_logins_LINE',array(
					'CNT'	=> $cnt,
					'DATE'	=> _datedaytime_get($last)
				)) ?></div><?
			}
		}

		?><p>Word je direct uitgelogd als je nu op een van de menuopties klikt?</p><?
		?><ul><?
		?><li><b>Datum en tijd</b> van je computer moeten <b>correct</b> zijn! Ook de tijdzone en automatische inschakeling van zomer- en wintertijd.</li><?
		?><li>Zorg ervoor dat je <b>cookies</b> accepteert!</li><?
		?><li>Kijk ook bij de veel gestelde vraag &quot;<i><a href="/faq/6">Wanneer ik mijn berichten wil bekijken word ik uitgelogd!</a></i>&quot;</li><?
		?></ul><?
	} else {
		require_once '_fbsession.inc';
		fb_logout();
		layout_open_box('white');
		switch ($currentuser->failed_status) {
		case 'permbanned':
		case 'banned':
			$tmp		= isset($currentuser->banend) ?		$currentuser->banend : 0;
			$offenseid	= isset($currentuser->offenseid) ?	$currentuser->offenseid : 0;
			if ($tmp) {
				?><div class="block"><?= __('user:info:ban_ends_LINE',array('DATETIME'=>_datedaytime_get($tmp))) ?></div><?
			}
			if ($offenseid) {
				?><div class="block"><?= __('user:info:banned_due_to_offense_LINE') ?></div><?
			}
			break;
		default:
			# make sure password is not stored by password manager:
			header('HTTP/1.1 400',true);

			if ($currentuser->failed_status === 'unactive') {
				?><div class="block"><?= __('user:login:unactive_info_LINE') ?></div><?
				forgot_something(USERACT_ACTIVATE);
			} else {
				?><div class="error block"><?= __(
					match ($currentuser->action) {
						USERACT_LOGIN		=> 'user:error:login_failed_LINE',
						USERACT_IMPERSONATE	=> 'user:error:impersonation_failed_LINE',
						default				=> 'user:error:login_failed_LINE',
					})
				?></div><?
				if (isset($currentuser->retry_stamp)) {
					?><div class="block"><?
					[,,,$h,$m] = _getdate($currentuser->retry_stamp);
					$time = sprintf('%02d:%02d',$h,$m);
					echo __('user:info:too_many_tries_(login/activate/impersonate)_LINE',array('TIME'=>$time));
					?></div><?
				}
				if (!empty($currentuser->invalid_credentials)) {
					?><div class="block"><?= __('user:info:setuser_failed_TEXT',DO_NL2BR) ?></div><?
				}
				forgot_something($currentuser->action);
			}
			break;
		}
		?><div class="block"><?= __('user:login:contact_helpdesk_LINE',DO_UBB) ?></div><?
		layout_close_box();
		if (!empty($offenseid)) {
			require_once '_offense.inc';
			show_offense($offenseid,true);
		}
	}
	if (isset($_POST['RELFERER'])) {
		?><div><?= __('user:login:came_from_LINE',DO_UBB,array('RELFERER'=>$_POST['RELFERER'])) ?></div><?
	}
}

function main_menu() {
	layout_open_menu();
	layout_menuitem(Eelement_name('overview'), '/user', !$_REQUEST['ACTION']);
	if (have_admin()) {
		layout_menuitem(__C('pagecontrols:most_recent'), '/user/last', $_REQUEST['ACTION'] === 'last');
	}
	layout_menuitem(__C('status:now_online'), '/user/showonline', $_REQUEST['ACTION'] === 'showonline');
	layout_menuitem(__C('date:birthday_today'), '/user/birthdays', $_REQUEST['ACTION'] === 'birthdays');
	layout_menuitem(__C('menu:commemoration'), '/user/deceased/ordered', isset($_REQUEST['STATUS']) && $_REQUEST['STATUS'] === 'deceased');
	layout_close_menu();
}

function user_display_overview(): void {
	require_once '_status.inc';
	require_once '_userlist.inc';

	if ($_REQUEST['ACTION'] === 'deceased') {
		$userlist = new _userlist();
		$userlist->no_choice = true;
		$userlist->show_camera = true;
		$userlist->show_heart = true;
		$userlist->list_deceased();
		if (!$userlist->query()) {
 			return;
		}
		?><div class="block"><?= __('user:info:in_memoriam_TEXT') ?></div><?
		if ($userlist->size) {
			layout_open_box('white');
			layout_box_header(Eelement_plural_name('user').' '.MIDDLE_DOT_ENTITY.' '.__('status:deceased'));
			$userlist->display();
			layout_close_box();
		}
		return;
	}
	if ($_REQUEST['ACTION'] === 'search') {
		layout_show_section_header(Eelement_plural_name('user').' '.MIDDLE_DOT_ENTITY.' '.element_name('advanced_search'));
		main_menu();
		user_show_advanced_search();
		return;
	}
	if (($search_result = $_REQUEST['ACTION'] === 'search-result')
	||	!$_REQUEST['ACTION']
	) {
		convert_array_to_win1252($_REQUEST, USER_UTF8_FIELDS);
		layout_show_section_header($search_result ? Eelement_plural_name('user').' '.MIDDLE_DOT_ENTITY.' '.element_plural_name('search_result') : null);
		main_menu();

		if (!($advanced = isset($_REQUEST['ADVANCED']))) {
			?><form<?
			?> accept-charset="utf-8"<?
			?> id="normal-search"<?
			?> onsubmit="return submitForm(this)"<?
			?> method="get"<?
			?> action="/user/search-result#results"><?

			?><div class="block"><?= __C('search:for_nick_name_or_email') ?>: <?
			show_input([
				'onchange'	=> /** @lang JavaScript */ "document.forms[1].elements['TERM'].value = this.value;",
				'type'		=> 'search',
				'autosave'	=> 'nick_name_email',
				'name'		=> 'TERM',
				'id'		=> 'nick',
				'autofocus'	=> true,
				'required'	=> true,
				'value'		=> have_something($_REQUEST, 'TERM'),
			]);
			?> <input type="submit" value="<?= __('action:search') ?>" /><?
			?></div><?
			?></form><?
			include_js('js/user_search');
			?><div id="link-to-advanced" class="block"><?
			?><a<?
			?> onclick="return Pf_openAdvancedSearch(this);"<?
			?> href="/user/search"><?= Eelement_name('advanced_search') ?></a><?
			?></div><?
		}

		user_show_advanced_search($advanced);

		if ($search_result) {
			require_once 'defines/user.inc';
			$userlist = new _userlist();
			$userlist->show_camera = true;
			$userlist->show_heart = true;
			if ($nick = have_something($_REQUEST,'NICK')) {
				$userlist->nick_like($nick);
			}
			if ($term = have_something($_REQUEST,'TERM')) {
				$userlist->find_on_nick_name_or_email($term);
			}

			if ($status = have_user_status($_REQUEST, 'STATUS')) {
				// only user admins may see deleted accounts
				if ((	$status === 'all'
					||	$status === 'deleted'
					)
				&&	!require_admin('helpdesk')
				) {
					return;
				}
				if ($status === 'all') {
					$userlist->any_status();
				} else {
					$userlist->with_status($status);
				}
			} elseif (!have_admin()) {
				$userlist->only_active();
			}
			if ($name = have_something($_REQUEST, 'NAME')) {
				$userlist->with_name($name);
			}
			if ($gender = have_element($_REQUEST,'GENDER',['F','M'], true)) {
				$userlist->of_gender($gender);
			}
			if ($sexpref = have_element($_REQUEST,'SEXPREF',['hetero','homo','bi'], true)) {
				$userlist->of_sexpref($sexpref);
			}
			if ($relation = have_element($_REQUEST,'RELATION', ['no', 'yes'], true)) {
				if (isset($_REQUEST['RELNULL'])) {
					$userlist->with_relation_or_null($relation);
				} else {
					$userlist->with_relation($relation);
				}
			}
			$min_age = have_idnumber($_REQUEST,'MINAGE');
			$max_age = have_idnumber($_REQUEST,'MAXAGE');
			if ($min_age && $max_age) {
				if ($min_age !== $max_age) {
					if ($min_age <= $max_age) {
						$userlist->in_age_range($min_age, $max_age);
					} else {
						$userlist->in_age_range($max_age, $min_age);
					}
				} else {
					$userlist->of_age($min_age);
				}
			} elseif ($max_age) {
				$userlist->of_age($max_age);
			}
			if (have_idnumber($_REQUEST,'PARTYID')) {
				$userlist->going_to_party($_REQUEST['PARTYID']);
			}
			if ($city_id = have_idnumber($_REQUEST, 'CITYID')) {
				if ($radius = have_idnumber($_REQUEST, 'RADIUS')) {
					if (!($city = db_single_assoc('city', "SELECT LONGITUDE, LATITUDE FROM city WHERE CITYID = $city_id"))) {
						not_found('city', $city_id);
						return;
					}
					$userlist->within_radius($city['LATITUDE'], $city['LONGITUDE'], $radius);
				} else {
					$userlist->in_city($_REQUEST['CITYID']);
				}
			} elseif (have_idnumber($_REQUEST,'COUNTRYID')) {
				$userlist->in_country($_REQUEST['COUNTRYID']);
			}
			if (have_number_array($_REQUEST,'DATING')) {
				$userlist->available = $_REQUEST['DATING'];
				$flags = 0;
				foreach ($_REQUEST['DATING'] as $flag) {
					$flags |= $flag;
				}
				$userlist->show_dating = $flags;
			}
			if (have_admin('helpdesksuper')) {
				if ($email = have_something($_REQUEST,'EMAIL')) {
					$userlist->show_email = true;
					$userlist->columns = 1;
					$userlist->with_email($email);
				}
				if ($site = have_something($_REQUEST,'SITE')) {
					$userlist->with_site($site);
				}
				if ($text = have_something($_REQUEST,'TEXT')) {
					$userlist->with_usertext($text);
				}
				if (isset($_REQUEST['OFFENSE'])) {
					$userlist->with_offense();
				}
			}
			if (have_admin('user')) {
				if ($realname = have_something($_REQUEST,'REALNAME')) {
					$userlist->with_realname($realname);
				}
			}
			if (isset($_REQUEST['PFIMG'])) {
				$userlist->require_photo();
			}
			if (isset($_REQUEST['ALBUM'])) {
				$userlist->with_album();
			}
			if (isset($_REQUEST['ONLINE'])
			&&	!empty($userlist->available)
			) {
				$userlist->only_online_respect_privacy();
			}

			$userlist->limit(1001);
			$userlist->query();

			layout_open_box('white','results');
			layout_box_header(Eelement_plural_name('search_result'));
			if (!$userlist->size) {
				?><div class="block"><?= __('search:info:nothing_found_LINE') ?></div><?
			} else {
				if ($userlist->size >= 1000) {
					register_warning('search:warning:too_many_results,only_x_shown_LINE', ['X' => 1000]);
				}
				$userlist->display(1000);
			}
			layout_close_box();

		} elseif (have_user()) {
			require_once '_favourite.inc';
			show_favourites();
		}
		return;
	}
	not_found();
}

function user_show_advanced_search(bool $show = true): void {
	?><form<?
	if (!$show) {
		?> class="hidden"<?
	}
	?> accept-charset="utf-8"<?
	?> id="advanced-search"<?
	?> method="get"<?
	?> action="/user/search-result#results"<?
	?> onsubmit="return submitForm(this)"<?
	?>><?

	?><input type="hidden" name="ADVANCED" value="1" /><?

	require_once '_supersearch.inc';
	show_search_header();

	layout_open_box('white');
	layout_open_table('fw');
	layout_start_row();
	echo Eelement_name('nick_name_or_email');
	layout_field_value();
	$term = require_something_trim_clean_or_none($_REQUEST, 'TERM',	utf8: true);
	show_input(['type'=>'search', 'autosave' => 'nick_name_email', 'name'=>'TERM', 'value_utf8' => $term, 'autofocus' => $show]);

	layout_restart_row();
	echo Eelement_name('nick');
	layout_field_value();
	$nick = require_something_trim_clean_or_none($_REQUEST, 'NICK', utf8: true);
	show_input([
		'type'		 => 'search',
		'autosave'	 => 'nick',
		'name'		 => 'NICK',
		'value_utf8' => $nick,
	]);

	layout_restart_row();
	echo Eelement_name('name');
	layout_field_value();
	$name = require_something_trim_clean_or_none($_REQUEST, 'NAME',	utf8: true);
	show_input(['type'=>'search', 'autosave' => 'name', 'name'=>'NAME', 'value_utf8' => $name]);

	$status = require_something_trim_clean_or_none($_REQUEST, 'STATUS', utf8: true);
	if (have_admin('helpdesk')) {
		require_once '_status.inc';
		layout_restart_row();
		echo Eelement_name('status');
		layout_field_value();
		?><select name="STATUS"><?
		?><option value="all"><?= element_name('all') ?></option><?
		status_show_options($status ?: 'active');
		?></select><?
	} else {
		?><input type="hidden" name="STATUS" value="<?
		if ($status) {
			echo escape_utf8($status);
		}
		?>" /><?
	}

	# DATING
	layout_restart_row();
	echo Eelement_name('available');
	layout_field_value();
	require_once '_dating.inc';
	$selected = 0;
	if (have_number_array($_REQUEST, 'DATING')) {
		foreach ($_REQUEST['DATING'] as $flag) {
			$selected |= $flag;
		}
	}
	global $currentuser;
	show_dating_multi_select($selected,null,false);

	$checked = $selected && isset($_REQUEST['ONLINE']);
	layout_restart_row(0,null,null,$checked ? 'hilited' : 'not-hilited');
	?><label for="online"><?= __C('status:now_online') ?></label><?
	layout_field_value();
	?><label><?
	show_input([
		'type'		=> 'checkbox',
		'class'		=> 'upLite',
		'name'		=> 'ONLINE',
		'id'		=> 'online',
		'value'		=> 1,
		'checked'	=> $checked,
		'disabled'	=> !$selected
	]);
	?> (<?= __('user:search:online_only_dating') ?>)<?
	?></label><?

	# GENDER
	layout_restart_row();
	echo Eelement_name('gender');
	layout_field_value();
	$gender = $_REQUEST['GENDER'] ?? null;
	?><select name="GENDER"><?
		?><option></option><?
		?><option<? if ($gender === 'M') echo ' selected' ?> value="M"><?= __('gender:male') ?></option><?
		?><option<? if ($gender === 'F') echo ' selected' ?> value="F"><?= __('gender:female') ?></option><?
	?></select><?

	// SEXPREF
	layout_restart_row();
	echo Eelement_name('(sexual)orientation');
	layout_field_value();
	$sexpref = $_REQUEST['SEXPREF'] ?? null;
	?><select name="SEXPREF"><?
		?><option></option><?
		?><option<? if ($sexpref === 'hetero') echo ' selected' ?> value="hetero"><?= __('sexpref:straight') ?></option><?
		?><option<? if ($sexpref === 'homo'	 ) echo ' selected' ?> value="homo"><?=	  __('sexpref:gay')		 ?></option><?
		?><option<? if ($sexpref === 'bi'	 ) echo ' selected' ?> value="bi"><?=	  __('sexpref:bisexual') ?></option><?
	?></select><?

	// RELATION
	layout_restart_row();
	?><label for="relation"><?= Eelement_name('relation') ?></label><?
	layout_field_value();
	$relation = $_REQUEST['RELATION'] ?? null;
	?><select id="relation" name="RELATION" onchange="setdisplay('unknown-relation-label', this.selectedIndex)"><?
		?><option value=""></option><?
		?><option<? if ($relation === 'no' ) { echo ' selected'; } ?> value="no"><?=  __('field:without') ?></option><?
		?><option<? if ($relation === 'yes') { echo ' selected'; } ?> value="yes"><?= __('field:with') ?></option><?
	?></select><?
	?> <label for="unknown-relation" id="unknown-relation-label" class="<? if (!$relation) { echo 'hidden '; } ?>not-hilited"><?
	?><input id="unknown-relation" class="upLite" type="checkbox" name="RELNULL" value="1" /> <?
	?>ook mensen die hun relatie status niet hebben ingevuld opnemen<?
	?></label><?

	// MINAGE,MAXAGE
	layout_restart_row();
	echo Eelement_name('age');
	layout_field_value();
	?><select name="MINAGE"><option value="0"></option><?
	$min_age = have_idnumber($_REQUEST, 'MINAGE');
	for ($cnt = 10; $cnt <= 80; ++$cnt) {
		?><option<?
		if ($cnt === $min_age) {
			?> selected<?
		}
		?>><?= $cnt ?></option><?
	}
	?></select> &ndash; <?
	?><select name="MAXAGE"><option value="0"></option><?
	$max_age = have_idnumber($_REQUEST, 'MAXAGE');
	for ($cnt = 10; $cnt <= 80; ++$cnt) {
		?><option<?
		if ($cnt === $max_age) {
			?> selected<?
		}
		?>><?= $cnt ?></option><?
	}
	?></select><?

	require_once '_citylist.inc';
	$countryid	= have_idnumber($_REQUEST, 'COUNTRYID');
	$cityid		= have_idnumber($_REQUEST, 'CITYID');
	$radius		= have_idnumber($_REQUEST, 'RADIUS');

	layout_restart_row();
	echo Eelement_name('region');
	layout_field_value();

	display_dynamic_country_options(selected: $countryid, flags: $flags = INCLUDE_EMPTY_COUNTRIES | SHOW_NOTICE_IF_EMPTY | SHOW_RADIUS | DYNLOC_GROUP_INTERESTING);
	layout_restart_row($cityid ? 0 : ROW_HIDDEN, 'dcitypart');
	echo Eelement_name('city');
	layout_field_value();
	display_dynamic_city_options(selected: $cityid, flags: $flags);

	layout_restart_row();
	echo Eelement_name('radius');
	layout_field_value();
	display_dynamic_radius(selected: $radius, flags: $flags);

	$checked = isset($_REQUEST['PFIMG']);
	layout_restart_row(0,null,null,$checked ? 'hilited' : 'not-hilited');
	?><label for="pfimg"><?= str_replace(
		'%PF%',
		'<img alt="partyflock" src="'.get_favicon().'" class="icon" />',
		__C('attrib:with_PF_photo', KEEP_EMPTY_KEYWORDS)
	) ?></label><?
	layout_field_value();
	show_input(['type' => 'checkbox', 'class' => 'upLite', 'name' => 'PFIMG', 'id' => 'pfimg', 'value' => true, 'checked' => $checked]);

	$checked = isset($_REQUEST['ALBUM']);
	layout_restart_row(0,null,null,$checked ? 'hilited' : 'not-hilited');
	?><label for="album"><?= __C('attrib:with_album') ?></label><?
	layout_field_value();
	show_input(array('type'=>'checkbox','class'=>'upLite','name'=>'ALBUM','id'=>'album','value'=>true,'checked'=>$checked));

	if (have_user()
	&&	(require_once '_going.inc')
	&&	have_going()
	) {
		layout_restart_row();
		?><label for="to-party"><?= Eelement_name('party') ?></label><?
		layout_field_value();
		include_js('js/form/user_search');
		?><input type="button" id="to-party" onclick="showPastParties(this)" value="<?= __('action:choose') ?>" /><?
		?><div id="party-options"></div><?
	}
	layout_stop_row();
	if (have_admin('helpdesksuper')) {
		layout_start_spanned_row();
		?><b>Zoekmogelijkheden voor medewerkers</b>: (belastend voor snelheid!)<?

		$email = require_something_trim_clean_or_none($_REQUEST, 'EMAIL', utf8: true);
		layout_restart_row();
		echo Eelement_name('email');
		layout_field_value();
		show_input(['type'=>'search', 'autosave' => 'email', 'name'=>'EMAIL', 'value_utf8' => $email]);

		$site = require_something_trim_clean_or_none($_REQUEST, 'SITE', utf8: true);
		layout_restart_row();
		echo Eelement_name('favourite_site');
		layout_field_value();
		show_input(['type'=>'search', 'autosave' => 'site', 'name'=>'SITE','data-valid'=>'url', 'value_utf8' => $site]);

		$text = require_something_trim_clean_or_none($_REQUEST, 'TEXT', utf8: true);
		layout_restart_row();
		echo Eelement_name('personal_text');
		layout_field_value();
		show_input(['type'=>'search', 'name'=>'TEXT', 'value_utf8' => $text]);

		if (have_admin('user')) {
			$real_name = require_something_trim_clean_or_none($_REQUEST, 'REALNAME', utf8: true);
			layout_restart_row();
			echo Eelement_name('realname');
			layout_field_value();
			show_input(['type'=>'search', 'autosave' => 'realname', 'name'=>'REALNAME', 'value_utf8' => $real_name]);
		}
		$checked = isset($_REQUEST['OFFENSE']);
		layout_restart_row(0,null,null,$checked ? 'hilited' : 'not-hilited');
		?><label for="offense"><?= __('field:with_offense') ?></label><?
		layout_field_value();
		show_input(array('type'=>'checkbox','class'=>'upLite','name'=>'OFFENSE','id'=>'offense','value'=>true,'checked'=>$checked));

		layout_stop_row();
	}
	layout_close_table();
	layout_close_box();
	?><div class="block"><input type="submit" value="<?= __('action:search') ?>" /></div><?
	?></form><?
}

function user_menu($user) {
	if (!have_user()) {
		return;
	}
	$userid = $user['USERID'];
	// only superuser can change default user
	if ($userid == 1 && !have_super_admin()) {
		return;
	}
	$helpdesk_admin = have_admin('helpdesk');
	$user_admin = have_admin('user');
	$userhref = '/user/'.$userid;
	$self = CURRENTUSERID == $userid;
	if ($self || $helpdesk_admin || $user_admin) {
		$showmailproblems =
			$user['EMAIL']
		&&	($failmsgs = memcached_single('contact_undeliverable','SELECT 1 FROM contact_undeliverable WHERE FAILMAIL="'.addslashes($user['EMAIL']).'" LIMIT 1', TEN_MINUTES))
		&&	($fails = memcached_single('realemailcheck','SELECT FAILS FROM realemailcheck WHERE EMAIL="'.addslashes($user['EMAIL']).'"', TEN_MINUTES));

		layout_open_menu();
		if ($self || $user_admin) {
			layout_menuitem(__C('action:change'),$userhref.'/form');
			layout_menuitem(__C('action:change_biography'),$userhref.'/bioform');
		}
		if ($user_admin || have_admin('helpdesk')) {
			layout_menuitem(__C('action:change_email'),$userhref.'/emailform');
		}
		if ($user_admin || $helpdesk_admin) {
			switch ($user['STATUS']) {
			case 'active':
			case 'inactive':
				layout_menuitem(__C('action:reset_password'),$userhref.'/sendpasswdtoken');
				break;
			case 'unactive':
				layout_menuitem(Eelement_name('new_activation_message'),$userhref.'/sendactivationtoken');
				break;
			}
		}

		layout_menuitem(Eelement_plural_name('setting'),$userhref.'/settings');
		if ($self) {
			layout_menuitem(Eelement_plural_name('operation'),'/user/operations');
		}
		layout_menuitem(Eelement_name('information'),$userhref.'/information');
		if ($self) {
			if (!setting_isset(SHOW_PERSONAL_NOTE)) {
				layout_menuitem(Eelement_name('note'),'/user/personalnote');
			}
			layout_menuitem(Eelement_name('poll'),'/poll/personal');
			require_once '_albumcount.inc';
			$info = album_total_counts($_REQUEST['sID'],NOBODY);
			if (!$info || !$info[0]) {
				layout_menuitem(__C('action:create_album'),'/album/'.$userid.'/mapaddform');
			}
		}
		if ($showmailproblems) {
			layout_continue_menu();
			layout_menuitem(Eelement_plural_name('mail_problem'),$userhref.'/mailerrors',' class="warning"');
		}
		layout_close_menu();
	}
	if (!have_admin()) {
		return;
	}
	require_once '_offense_access.inc';
	layout_open_menu();
	if (have_offense_admin()) {
		$cnts = memcached_simple_hash('offense','
			SELECT COUNT(*),COUNT(IF(REMOVED OR REGONLY,1,NULL))
			FROM offense
			WHERE USERID='.$userid
		);
		layout_menuitem(Eelement_plural_name('offense'),'/offense/user/'.$userid);
		if ($cnts) {
			[$total,$removed] = keyval($cnts);
			if ($total) {
				?> <small>(<?
				if ($removed) {
					?><span class="light"><?= $removed ?>,</span><?
				}
				echo $total-$removed;
				?>)</small><?
			}
		}
	}
	if ($helpdesk_admin) {
		layout_menuitem(Eelement_name('information'),$userhref.'/information');
	}
	if (have_admin('rights')) {
		layout_menuitem(Eelement_plural_name('right'),'/rights/user/'.$userid);
	}
	if (have_admin('music')) {
		layout_menuitem(Eelement_plural_name('music_right'),'/music/options/'.$userid);
	}
	if ($helpdesk_admin) {
		if (($self || $helpdesk_admin)
		&&	!empty($failmsgs)
		) {
			layout_menuitem(Eelement_plural_name('mail_problem'),$userhref.'/mailerrors');
		}
/*		if ($helpdesk_admin
		&&	(	$user['STATUS'] == 'active'
			||	$user['STATUS'] == 'inactive'
			||	$user['STATUS'] == 'banned'
			)
		) {
			layout_open_menuitem();
			?><span class="light">Wachtwoord</span> <a href="/user/<?= $userid;
			?>/mailpasswd">toesturen</a> / <a href="/user/<?= $userid;
			?>/newpasswd">vervangen</a><?
			layout_close_menuitem();
		}*/
	}
	if (have_admin('chat')) {
		layout_menuitem(Eelement_name('chat'),'/chat/userlines/'.$userid);
	}
	if (have_admin('contest')
	&&	(require_once '_contestwins.inc')
	&&	($contestcounts = get_contest_counts($userid))
	&&	$contestcounts[0]
	) {
		layout_menuitem(Eelement_plural_name('participated_contest'),'/contest/participations/'.$userid);
	}
	if (memcached_single('contact_ticket','SELECT 1 FROM contact_ticket WHERE WITHUSERID='.$userid.' LIMIT 1')) {
		layout_menuitem(Eelement_plural_name('contact_ticket'),'/ticket/search?SRCH='.$userid.';TYPE=withuser;ALL');
	}
	if (have_admin('helpdesk')) {
		layout_menuitem(Eelement_plural_name('secret'),$userhref.'/secrets#secrets',
			$_REQUEST['ACTION'] === 'secret'
		||	$_REQUEST['ACTION'] === 'single'
		&&	setting('SHOW_USER_SECRETS')
		);
	}
	layout_close_menu();
}
function user_display_operations() {
	layout_show_section_header(Eelement_plural_name('operation'));
	if (!require_user()) {
		return;
	}

	$user = db_single_assoc(['user','fbcreated_user'],'
		SELECT CSTAMP,NOPASSWORD
		FROM user
		LEFT JOIN fbcreated_user USING (USERID)
		WHERE USERID='.CURRENTUSERID
	);
	if (!$user) {
		return;
	}

	layout_open_box('white');

	?><ul class="itempad"><?
	?><li><a href="/user/<?= CURRENTUSERID ?>/form"><?= __C('action:change_profile') ?></a></li><?
	?><li><a href="<?= $user['NOPASSWORD'] ? '/user/'.CURRENTUSERID.'/sendpasswdtoken' : '/user/changepasswd' ?>"><?= __C('action:change_password') ?></a></li><?
	?><li><?
//	if ($user['CSTAMP'] < CURRENTSTAMP-7*24*3600) {
		?><a href="/user/delete"><?= __C('action:remove_account') ?></a><?
/*	} else {
		?><span class="help" title="Je kunt jezelf de eerste week niet verwijderen."><?= __C('action:remove_account') ?></span><?
	}*/
	?></li><?
	require_once '_fbsession.inc';
	fb_show_disconnect_list_item();

	?></ul><?

	layout_close_box();
}
function user_display_information() {
	if (!($userid = require_idnumber($_REQUEST,'sID'))
	||	!require_self_or_admin($userid,'helpdesk')
	) {
		return;
	}
	layout_show_section_header(Eelement_name('information'));

	require_once '_buddies.inc';
	[$icnt,$ocnt] = buddy_request_counts($userid);

	$ignores = memcached_single('ignorelist','SELECT COUNT(*) FROM ignorelist WHERE USERID='.$userid);
	$blocks = memcached_single('blocklist','SELECT COUNT(*) FROM blocklist WHERE USERID='.$userid);

	require_once '_contestwins.inc';
	[$total_contests,$won_contests] = get_contest_counts($userid);

	$self = (CURRENTUSERID == $userid);

	layout_open_box('white');
	?><ul><?
	if ($icnt) {
		?><li><a href="/buddyrequest/<?= $userid ?>/overview/incoming"><?= Eelement_plural_name('incoming_buddy_request') ?></a> <?= MIDDLE_DOT_ENTITY ?> <small><?= $icnt ?></small></li><?
	}
	if ($ocnt) {
		?><li><a href="/buddyrequest/<?= $userid ?>/overview/outgoing"><?= Eelement_plural_name('outgoing_buddy_request') ?></a> <?= MIDDLE_DOT_ENTITY ?> <small><?= $ocnt ?></small></li><?
	}
	if (!$icnt && !$ocnt) {
		?><li><?= Eelement_plural_name('no_buddy_request') ?></li><?
	}
	?><li><?
	if ($ignores) {
		?><a href="/user/<?= $userid;
		?>/ignorelist"><?= Eelement_plural_name('ignored') ?></a> <?= MIDDLE_DOT_ENTITY ?> <small><?= $ignores;
		?></small><?
	} else {
		echo Eelement_plural_name('no_ignored');
	}
	?></li><li><?
	if ($blocks) {
		?><a href="/user/<?= $userid ?>/blocklist"><?= __C('user:block:blocks'); ?></a> <?= MIDDLE_DOT_ENTITY ?> <small><?= $blocks ?></small><?
	} else {
		echo __C('user:block:no_blocks');
	}
	?></li><li><?
	if ($total_contests) {
		?><a href="/contest/participations/<?= $userid ?>"><?= Eelement_name('participated_contest',$total_contests) ?></a> <?= MIDDLE_DOT_ENTITY ?> <small><?= $total_contests ?></small><?
		?></li><?
		if ($won_contests) {
			?><li><a href="/contest/winmsg/<?= $userid ?>/all"><?= Eelement_name('won_contest',$won_contests) ?></a> <?= MIDDLE_DOT_ENTITY ?> <small><?= $won_contests ?></small><?
		}
	} else {
		echo Eelement_plural_name('no_win_contest');
	}
	?></li><?
/*	if ($ocnt = memcached_single('offense','SELECT COUNT(*) FROM offense WHERE USERID='.$userid)) {
		?><li><a href="/offense/user/<?= $userid ?>"><?= Eelement_name('offense_history') ?></a> &middot <small><?= $ocnt ?></small></li><?
	}*/
	?></ul><?
	global $__year,$__month,$__day;
	$valentines = memcached_simple_hash(
		'valentine','
		SELECT YEAR,COUNT(*) AS TOTAL
		FROM valentine
		WHERE TO_USERID='.$userid.'
		  AND YEAR<='.($__month < 2 || ($__month == 2 && $__day < 14) ? $__year-1 : $__year).'
		  AND BLOCKED=0
		GROUP BY YEAR'
	);
	if ($valentines) {
		?><ul><?
		foreach ($valentines as $year => $cnt) {
			?><li><a href="/user/<?= $userid;
			?>/valentine/<?= $year;
			?>"><?= Eelement_name('valentine') ?> <?= $year ?></a> <?= MIDDLE_DOT_ENTITY ?> <small><?= $cnt ?> <?= element_name('(valentine)_heart',$cnt) ?></small></li><?
		}
		?></ul><?
	}
	layout_close_box();
}

function user_display_stats(
	array  $user,
	array  $user_data,
	array &$totals
): void {
	$userid = $user['USERID'];
	$self = CURRENTUSERID === $userid;
	layout_open_table('fw vtop default', max_cols: SMALL_SCREEN ? 1 : 3);

	layout_start_reverse_row();
	if ($user_data['COUNTER']) {
		if (!$self
		&&	!_visibility($user, 'ONLINE', have_admin())
		) {
			?>&plusmn; <?
			echo $user_data['COUNTER'] - $user_data['COUNTER'] % 101;
		} else {
			echo $user_data['COUNTER'];
		}
	} else {
		echo __('counter:none');
	}
	layout_value_field();
	echo __('stats:pages_viewed', ['CNT' => $user_data['COUNTER']]);

	// PROFILE VIEWS
	if ($self
	||	have_admin()
	) {
		layout_restart_reverse_row();
		echo db_single('user_counter_total','SELECT VIEWS FROM user_counter_total WHERE VISITID='.$userid);
		layout_value_field(SEPARATOR_MULTIPLIER);
		echo __('stats:viewed');
	}
	if (!empty($totals['PHOTO_CNT'])) {
		layout_restart_reverse_row();
		$linkopen = '<a href="/user/'.$userid.'/photos">';
		echo $linkopen,$totals['PHOTO_CNT']; ?></a><?
		layout_value_field();
		echo $linkopen,element_name('photo',$totals['PHOTO_CNT']); ?></a><?
	}



	if (!empty($totals['BUDDY_CNT'])) {
		[$linkopen,$linkclose] = get_action_open_close_for_setting('buddies','SHOW_USER_BUDDIES');
		layout_restart_reverse_row();
		echo $linkopen,$totals['BUDDY_CNT'],$linkclose;
		layout_value_field(get_partyflock_icon());
		echo $linkopen,element_name('buddy', $totals['BUDDY_CNT']),$linkclose;
	}

	if (!empty($totals['INACTIVE_BUDDY_CNT'])) {
		[$linkopen,$linkclose] = get_action_open_close('inactivebuddies');
		layout_restart_reverse_row();
		echo $linkopen,$totals['INACTIVE_BUDDY_CNT'],$linkclose;
		layout_value_field(get_partyflock_icon('light6'));
		echo $linkopen,element_name('non_active_buddy',$totals['INACTIVE_BUDDY_CNT']),$linkclose;
	}
/*	if ($fbcnt = memcached_single('fbbuddycounter','SELECT CNT FROM fbbuddycounter WHERE USERID='.$userid)) {
		$linkopen = $linkclose = null;
		layout_restart_reverse_row();
		echo $linkopen,$fbcnt,$linkclose;
		layout_value_field(get_facebook_icon());
		echo $linkopen,element_name('buddy',$fbcnt),$linkclose;
	}*/

	if (!empty($totals['FAVCNT'])) {
		$favcnt = $totals['FAVCNT'];
		[$linkopen,$linkclose] = get_action_open_close_for_setting('favourites','SHOW_USER_FAVOURITES');
		layout_restart_reverse_row();
		echo $linkopen,$favcnt,$linkclose;
		layout_value_field();
		echo $linkopen,element_name('favourite',$favcnt),$linkclose;
	}
	if (!empty($totals['PASTPARTY'])) {
		[$linkopen,$linkclose] = get_action_open_close_for_setting('archive','SHOW_USER_ARCHIVE');
		layout_restart_reverse_row();
		echo $linkopen,$totals['PASTPARTY'],$linkclose;
		layout_value_field();
		echo $linkopen,__('stats:user:parties_visited',array('CNT'=>$totals['PASTPARTY'])),$linkclose;
	}
	if (!empty($totals['PAST_INTERESTED'])) {
		[$linkopen,$linkclose] = get_action_open_close('interestingarchive');
		layout_restart_reverse_row();
		echo $linkopen,$totals['PAST_INTERESTED'],$linkclose;
		layout_value_field();
		echo $linkopen,__('stats:user:parties_interested',['CNT'=>$totals['PAST_INTERESTED']]),$linkclose;
	}
	if ($cnt = memcached_single('contestresponse','SELECT COUNT(*) FROM contestresponse WHERE USERID='.$userid)) {
		layout_restart_reverse_row();
		echo $cnt;
		layout_value_field();
		echo element_name('contestresponse',$cnt);
	}

	if ($totals['SHOOTCNT'] = memcached_single('gallery', '
		SELECT COUNT(*)
		FROM gallery
		WHERE VISIBLE="yes"
		  AND USERID='.$userid,
		TEN_MINUTES
	)) {
		[$linkopen, $linkclose] = get_action_open_close('shoots');
		layout_restart_reverse_row();
		echo $linkopen, $totals['SHOOTCNT'], $linkclose;
		layout_value_field();
		echo $linkopen, element_name('photoshoot', $totals['SHOOTCNT']),$linkclose;
	}
	// VIDEOCNT
	$totals['VIDEOCNT'] = memcached_single(
		array('video','connect'), '
		SELECT COUNT(DISTINCT VIDEOID)
		FROM video
		JOIN connect ON
				MAINTYPE="user"
			AND MAINID='.$userid.' AND
				(ASSOCTYPE="video" AND ASSOCID=VIDEOID
				  OR ASSOCTYPE="videochannel" AND ASSOCID=CHANNELID)'.
		(!have_admin('video') ? ' WHERE STATUS="active"' : null),
		TEN_MINUTES
	);
	if ($totals['VIDEOCNT']) {
		[$linkopen,$linkclose] = get_action_open_close('videoshoots');
		layout_restart_reverse_row();
		echo $linkopen,$totals['VIDEOCNT'],$linkclose;
		layout_value_field();
		echo $linkopen,element_name('videoshoot',$totals['VIDEOCNT']),$linkclose;
	}
	// COLUMNS
	$totals['COLUMNCNT'] = memcached_single('column','SELECT COUNT(*) FROM `column` WHERE ACCEPTED=1 AND ANONYMOUS=0 AND BYUSERID='.$userid, TEN_MINUTES);
	if ($totals['COLUMNCNT']) {
		[$linkopen,$linkclose] = get_action_open_close('columns');
		layout_restart_reverse_row();
		echo $linkopen,$totals['COLUMNCNT'],$linkclose;
		layout_value_field();
		echo $linkopen,element_name('column',$totals['COLUMNCNT']),$linkclose;
	}
	foreach (array('review','interview') as $element) {
		$cnt = $totals[$element.'CNT'] =
			memcached_single(array($element,'connect'), '
			SELECT COUNT(*) FROM (
				SELECT '.$element.'ID FROM '.$element.' WHERE ACCEPTED=b\'1\' AND BYUSERID='.$userid.'
			UNION	SELECT ASSOCID FROM connect
				JOIN '.$element.' ON MAINTYPE="author" AND MAINID='.$userid.' AND ASSOCTYPE="'.$element.'" AND '.$element.'ID=ASSOCID
				WHERE ACCEPTED=b\'1\' AND BYUSERID!='.$userid.'
			) AS elems',
			TEN_MINUTES
		);
		if ($cnt) {
			[$linkopen,$linkclose] = get_action_open_close($element.'s');
			layout_restart_reverse_row();
			echo $linkopen,$cnt,$linkclose;
			layout_value_field();
			echo $linkopen,element_name($element,$cnt),$linkclose;
		}
	}



	if ($totals['REPORTCNT']) {
#		list($linkopen,$linkclose) = get_action_open_close_for_setting('reports','SHOW_USER_REPORTS');
		$linkopen = '<a href="/report/user/'.$userid.'">';
		$linkclose = '</a>';
		layout_restart_reverse_row();
		echo $linkopen,$totals['REPORTCNT'],$linkclose;
		layout_value_field();
		echo $linkopen,element_name('report',$totals['REPORTCNT']),$linkclose;
	}
	if (($flockcnt = memcached_single_assoc(['flockmember', 'flock'],'
			SELECT	COUNT(*) AS TOTAL,
					COUNT(IF(REMOVED=0 AND ACCEPTED=1,1,NULL)) AS VISIBLE
			FROM flockmember
			JOIN flock USING (FLOCKID)
			WHERE USERID='.$userid,
		TEN_MINUTES))
	&&	($totals['FLOCKCNT'] = (have_admin('flock') ? $flockcnt['TOTAL'] : $flockcnt['VISIBLE']))
	) {
		layout_restart_reverse_row();
		$linkopen = '<a href="/user/'.$userid.'/flocks#flocks">';
		echo $linkopen,$totals['FLOCKCNT']; ?></a><?
		layout_value_field();
		echo $linkopen,element_name('flock',$totals['FLOCKCNT']); ?></a><?
	}
	require_once '_pollstatus.inc';
	if (($pollcnt = memcached_single_assoc('poll','
			SELECT	COUNT(*) AS TOTAL,
				COUNT(IF(STATUS='.POLLSTATUS_ACCEPTED.',1,NULL)) AS VISIBLE
			FROM poll
			WHERE USERID='.$userid,
		TEN_MINUTES)
		)
	&&	($pollcnt = have_admin('poll') ? $pollcnt['TOTAL'] : $pollcnt['VISIBLE'])
	) {
		layout_restart_reverse_row();
		$linkopen = '<a href="/poll/user/'.$userid.'">';
		echo $linkopen,$pollcnt; ?></a><?
		layout_value_field();
		echo $linkopen,element_name('poll',$pollcnt); ?></a><?
	}
	if (($musicinfo = memcached_single_assoc(['musicproducer', 'music'],'
			SELECT	COUNT(IF(ACTIVE = 1 AND ACCEPTED = 1, 1, NULL)) AS ACTIVE_TOTAL,
					COUNT(*) AS TOTAL
			FROM musicproducer
			JOIN music USING (MUSICID)
			WHERE TYPE = "user"
			  AND ID = '.$userid,
			ONE_HOUR))
	&&	($usecnt = $self || have_admin('music') ? $musicinfo['TOTAL'] : $musicinfo['ACTIVE_TOTAL'])
	) {
		layout_restart_reverse_row();
		$linkopen = '<a href="/music/user/'.$userid.'">';
		echo $linkopen,$usecnt; ?></a><?
		layout_value_field();
		echo $linkopen,element_name('musicproduction', $usecnt); ?></a><?
	}

	if ($self) {
		require_once '_subscriptions.inc';
		$subs = subscription_total($userid);
		if ($subs) {
			if ($self) {
				$linkopen = '<a href="/user/subscriptions">';
				$linkclose = '</a>';
			} else {
				$linkopen = $linkclose = null;
			}
			layout_restart_reverse_row();
			echo $linkopen,$subs,$linkclose;
			layout_value_field();
			echo $linkopen,element_name('followed_element',$subs),$linkclose;
		}
	}

	$quotedcnt = memcached_single('quoted','SELECT COUNT(*) FROM quoted WHERE QOTUSERID='.$userid, TEN_MINUTES);
	if ($quotedcnt) {
		if ($self) {
			$linkopen = '<a href="/user/'.$userid.'/quoted">';
			$linkclose = '</a>';
		} else {
			$linkopen = $linkclose = null;
		}
		layout_restart_reverse_row();
		echo $linkopen,$quotedcnt,$linkclose;
		layout_value_field(SEPARATOR_MULTIPLIER);
		echo $linkopen,__('field:quoted'),$linkclose;
	}

	// ALL COMMENTS
	require_once '_commentcounters.inc';
	$cnts = get_comment_counters($userid);
	if (!empty($cnts[null])) {
		if ($self
		||	have_admin('helpdesksuper')
		||	(require_once '_comment.inc')
		&&	have_admin(
				array_map(function($parent){
					return $parent.'_comment';
				},
				array_intersect(array_keys($cnts),array_keys(commentables()))
			)
		)) {
			$linkopen = '<a href="/user/'.$userid.'/commentoverview">';
			$linkclose = '</a>';
		} else {
			$linkopen = $linkclose = null;
		}
		layout_restart_reverse_row();
		echo $linkopen,$cnts[null],$linkclose;
		layout_value_field();
		echo $linkopen,element_name('comment',$cnts[null]),$linkclose;
	}
/*	/// FOTO COMMENTS
	$commentcnt = memcached_single('photo_comment','SELECT COUNT(*) FROM photo_comment WHERE USERID='.$userid,TEN_MINUTES);
	if ($commentcnt) {
		if (_visibility($esettings,'STATISTICS',have_admin(array('helpdesk','photo_comment')))) {
			$linkopen = '<a href="/user/'.$userid.'/commentoverview/photo">';
			$linkclose = '</a>';
		} else {
			$linkopen = $linkclose = null;
		}
		layout_restart_reverse_row();
		echo $linkopen,$commentcnt,$linkclose;
		layout_value_field();
		echo $linkopen,element_name('photo_comment',$commentcnt),$linkclose;
	}*/
	// RATINGS
	if ($cnt = memcached_single('rating','SELECT COUNT(*) FROM rating WHERE USERID='.$userid)) {
		require_once '_rating.inc';
		if ($self
		||	have_admin('helpdesksuper')
		||	have_rating_admin()
		) {
			$linkopen = '<a href="/user/'.$userid.'/ratingoverview">';
			$linkclose = '</a>';
		} else {
			$linkopen = $linkclose = null;
		}
		layout_restart_reverse_row();
		echo $linkopen,$cnt,$linkclose;
		layout_value_field();
		echo $linkopen,element_name('rating',$cnt),$linkclose;
	}
	// TOPICS
	if ($topiccnt = memcached_single('topic','SELECT COUNT(*) FROM topic WHERE USERID='.$userid, TEN_MINUTES)) {
		$linkopen = '<a href="/user/'.$userid.'/openedtopics">';
		$linkclose = '</a>';
		layout_restart_reverse_row();
		echo $linkopen,$topiccnt; ?></a><?
		layout_value_field();
		echo $linkopen,element_name('forumtopic',$topiccnt),$linkclose;
	}
	// MESSAGES
	if ($messagecnt = memcached_single('message','SELECT COUNT(*) FROM message WHERE USERID='.$userid, TEN_MINUTES)) {
		if ($self
		||	have_admin('helpdesksuper')
		||	have_forum_admin()
		) {
			$linkopen = '<a href="/user/'.$userid.'/forummessageoverview">';
			$linkclose = '</a>';
		} else {
			$linkopen = $linkclose = null;
		}
		layout_restart_reverse_row();
		echo $linkopen,$messagecnt,$linkclose;
		layout_value_field();
		echo $linkopen,element_name('forummessage',$messagecnt),$linkclose;
		?> <small><small>(<a href="/forum/history/<?= $userid ?>"><?= element_name('topic_list') ?></a>)</small></small><?
	}
	// FLOCKTOPICS
	if ($flocktopics = memcached_single('flocktopic','SELECT COUNT(*) FROM flocktopic WHERE USERID='.$userid, TEN_MINUTES)) {
		$linkopen = '<a href="/user/'.$userid.'/flocktopics">';
		$linkclose = '</a>';
		layout_restart_reverse_row();
		echo $linkopen,$flocktopics,$linkclose;
		layout_value_field();
		echo $linkopen,element_name('flocktopic',$flocktopics),$linkclose;
	}
	// FLOCKMESSAGES
	if ($flockmessages = memcached_single('flockmessage','SELECT COUNT(*) FROM flockmessage WHERE USERID='.$userid, TEN_MINUTES)) {
		if ($self
		||	have_admin(array('helpdesksuper','flockmessage'))
		) {
			$linkopen = '<a href="/user/'.$userid.'/flockmessageoverview">';
			$linkclose = '</a>';
		} else {
			$linkopen = $linkclose = null;
		}
		layout_restart_reverse_row();
		echo $linkopen,$flockmessages,$linkclose;
		layout_value_field();
		echo $linkopen,element_name('flockmessage',$flockmessages),$linkclose;
		?> <small><small>(<a href="/flock/history/<?= $userid ?>"><?= element_name('topic_list') ?></a>)</small></small><?
	}
	require_once '_karma.inc';
	if ((have_user() && use_karma()) || use_karma($userid)) {
		$karmastats = memcached_rowuse_array('karma', '
			SELECT AMOUNT,COUNT(*) AS TOTAL,COUNT(DISTINCT USERID) AS DIFFU
			FROM karma
			WHERE FORUSERID='.$userid.'
			GROUP BY AMOUNT
			ORDER BY AMOUNT DESC',
			TEN_MINUTES
#			null,
#			$self ? DB_FRESHEN_MEMCACHE : 0
		);
		if ($karmastats) {
			$total = 0;
			foreach ($karmastats as $karmastat) {
				extract($karmastat, \EXTR_OVERWRITE);
				$total += $TOTAL;
				layout_restart_reverse_row();
				echo $TOTAL;
				layout_value_field(SEPARATOR_MULTIPLIER);
				echo element_name($AMOUNT > 0 ? 'positive_karma' : 'negative_karma');

				layout_restart_reverse_row();
				echo $DIFFU;
				layout_value_field();
				echo element_name($AMOUNT > 0 ? 'positive_karma_giver' : 'negative_karma_giver',$DIFFU);
			}

if (SERVER_SANDBOX) {

/*			$karma_peruser = memcached_simple_hash('karma','
				SELECT USERID,COUNT
				FROM karma
				WHERE FORUSERID='.$userid.'
				GROUP BY USERID*/

			$karmainfo = memcached_simple_hash('karma', '
				SELECT	(STAMP<=UNIX_TIMESTAMP() - 61*24*3600) AS OLD,
					AMOUNT,

/*					SUM(AMOUNT * 1/(1 + 2 * (UNIX_TIMESTAMP() - STAMP) / 3600/24/365.25)) AS TOTAL*/
/*					SUM(AMOUNT) AS TOTAL*/

/*					COUNT(DISTINCT USERID)/COUNT(*) AS WORTH*/

					COUNT(*)
					
				FROM karma
				WHERE FORUSERID='.$userid.'
				GROUP BY AMOUNT,OLD',
				TEN_MINUTES
			);
			if ($karmainfo) {
				$oldpos = getifset($karmainfo,1,1) ?: 0;
				$newpos = getifset($karmainfo,0,1) ?: 0;

				$oldneg = getifset($karmainfo,1,-1) ?: 1;
				$newneg = getifset($karmainfo,0,-1) ?: 1;

				$old = $oldpos / abs($oldneg);
				$new = ($oldpos + $newpos) / abs($oldneg + $newneg);

				if ($old != $new) {
					layout_restart_reverse_row();
					$pos = $new > $old;
					include_style('votes');
					if ($pos) {
						?><span class="text-<?= $pos ? 'green' : 'orange' ?>"><?
					}
					echo $pos ? '+' : '-';
					if (!$old || !$new) {
						?>&infin;<?
					} else {
						echo round(100 * ($pos ? ($new / $old - 1) : (1 - $new / $old)));
					}
					?><?
					if ($pos) {
						?></span><?
					}
					layout_value_field(SEPARATOR_PERCENTAGE);
					echo element_name('karma_development');
				}
			}
}
		}
	}
	// DIRECTMESSAGE
	$dmsgs = memcached_single_array('user_cache','SELECT DMSG_SENT,DMSG_RECEIVED FROM user_cache WHERE USERID='.$userid, TEN_MINUTES);
	if ($dmsgs) {
		[$sent,$received] = $dmsgs;
	} else {
		$sent = $received = 0;
	}
	if ($sent) {
		layout_restart_reverse_row();
		echo $sent;
		layout_value_field();
		echo __('stats:directmessages_sent',array('CNT'=>$sent));
	}
	if ($received) {
		layout_restart_reverse_row();
		echo $received;
		layout_value_field();
		echo __('stats:directmessages_received',array('CNT'=>$received));
	}
	layout_stop_row();
	layout_close_table();
}
function user_display_mail_errors() {
	require_once '_mailerrors.inc';
	layout_show_section_header(Eelement_plural_name('mail_problem'));

	if (!have_something($_REQUEST,'EMAIL')) {
		if (!require_idnumber($_REQUEST,'sID')
		||	!require_self_or_admin($_REQUEST['sID'],'helpdesk')
		) {
			return;
		}
		$_REQUEST['EMAIL'] = db_single('user','SELECT EMAIL FROM user WHERE USERID='.$_REQUEST['sID']);
	} elseif (!require_super_admin()) {
		return;
	}
	if (!require_something($_REQUEST,'EMAIL')) {
		return;
	}
	if (!($undeliverables = db_rowuse_array('contact_undeliverable', '
		SELECT CIMID, CSTAMP, FAILREASON
		FROM contact_undeliverable
		WHERE FAILMAIL = "'.addslashes($_REQUEST['EMAIL']).'"
		ORDER BY CIMID DESC'))
	) {
		if ($undeliverables !== false) {
			?><p>Geen problemen bekend met dit email-adres</p><?
		}
		return;
	}
	if (!($failcnts = db_single_assoc('realemailcheck','
		SELECT FAILS, LAST_FAIL
		FROM realemailcheck
		WHERE EMAIL="'.addslashes($_REQUEST['EMAIL']).'"'))
	) {
		return;
	}
	if ($failcnts && $failcnts['FAILS']) {
		layout_open_box('white');
		?><div class="block"><?
		$header = Eelement_plural_name('mail_problem');
		if ($failcnts && $failcnts['FAILS'] >= 10) {
			?><span class="error"><?= $header ?></span><?
		} elseif ($failcnts && $failcnts['FAILS'] >= 1) {
			?><span class="warning"><?= $header ?></span><?
		} else {
			?><b><?= $header; ?></b><?
		}
		?><br /><?
		if (!empty($failcnts['FAILS'])) {
			echo __('user:info:email_fails_LINE', ['FAILS' => $failcnts['FAILS']]) ?><br /><?
			if ($failcnts >= 10) {
				echo __('user:info:no_notifications_for_you_LINE');
			}
/*			if ($failcnts['FAILS'] < 10) {
				if ($failcnts['FAILS'] === 1) {
					?>In de afgelopen periode is er 1 bericht onbezorgbaar gebleken.<br /><?
				} else {
					?>In de afgelopen periode zijn er <?= $failcnts['FAILS']; ?> berichten onbezorgbaar gebleken.<br /><?
				}
				?>Als dit aantal groeit tot 10 of meer, dan zul je geen notificaties en andere emails ontvangen!<?
			} else {
				?>In de afgelopen periode zijn er <?= $failcnts['FAILS']; ?> berichten onbezorgbaar gebleken.<br /><?
				?>Je kunt op dit moment <span class="underline">geen</span> notificaties en andere emails ontvangen!<?
			}*/
		}
		?></div><?
		layout_close_box();
		if (!empty($failcnts['FAILS'])) {
			show_mailproblems_solved_question();
		}
	}
	$supervisor = have_admin('helpdesksuper');
	layout_open_box('offense');
	layout_box_header($supervisor ? _make_illegible($_REQUEST['EMAIL']) : __('<EMAIL>'));
	layout_open_table('fw vtop default');
	foreach ($undeliverables as $undeliverable) {
		show_mail_error_row($undeliverable, $supervisor);
	}
	layout_close_table();
	layout_close_box();
}

function user_display_mail_error() {
	layout_show_section_header(Eelement_plural_name('mail_problem'));

	if (!($cimid = require_idnumber($_REQUEST,'subID'))
	||	!require_admin('helpdesksuper')
	||	!($mailerr = db_single_assoc('contact_undeliverable', '
			SELECT CONTENT, CSTAMP, FAILREASON, FAILMAIL
			FROM contact_undeliverable
			WHERE CIMID = '.$cimid))
	) {
		return;
	}
	layout_open_box('offense');
	layout_open_box_header();
	echo $failmail = _make_illegible($mailerr['FAILMAIL']);
	layout_close_box_header();
	layout_open_table(TABLE_BOLD_FIELD | TABLE_VTOP);
	layout_start_row();
		echo Eelement_name('email');
		layout_field_value();
		echo $failmail;
	layout_restart_row();
		echo Eelement_name('date');
		layout_field_value();
		_datedaytime_display($mailerr['CSTAMP']);
	layout_restart_row();
		echo Eelement_name('reason');
		layout_field_value();
		echo escape_specials($mailerr['FAILREASON']);
	layout_restart_row();
		echo Eelement_name('message');
		layout_field_value();
		?><div class="tt"><?=
			nl2br(
				preg_replace_callback(
					'/(?<spaces>\s{2,})/',
					static fn(array $match): string => str_replace(' ', '&nbsp;', $match['spaces']),
					escape_specials($mailerr['CONTENT'])
				)
			)
		?></div><?
	layout_stop_row();
	layout_close_table();
	layout_close_box();
}

function user_display_single() {
	if (!($userid = require_idnumber($_REQUEST,'sID'))) {
		return;
	}
	if (!($user = memcached_single_assoc_if_not_self_nor_any_admin(
		$userid,
		['user','user_account'],'
		SELECT	user_account.NICK, CSTAMP, BIRTH_DAY, BIRTH_MONTH, BIRTH_YEAR, LDAY, EMAIL, DATING,
				NAME, CITY, OCCUPATION, SEX, SITE, SEXPREF, CITYID, user_account.STATUS, ACCEPTED,
				MSTAMP, MUSERID, COUNTRYID, RELATIONID, COLOR, VISIBILITY_ONLINE,
				RELATION, VISIBILITY_NAME, VISIBILITY_OCCUPATION, VISIBILITY_BIRTHDAY, VISIBILITY_AGE, VISIBILITY_CITY,
				VISIBILITY_FAVMUSIC, VISIBILITY_SEXPREF, VISIBILITY_GENDER, VISIBILITY_RELATION, INVALID_CITY,
				VISIBILITY_SITE, VISIBILITY_PRESENCE
		FROM user
		JOIN user_account USING (USERID)
		WHERE USERID='.$userid,
		HALF_HOUR,
		'userprofile:'.$userid))
	) {
		if ($user !== false) {
			not_found();
		}
		return;
	}
	$user['USERID'] = $userid;
	/** @noinspection PhpRedundantOptionalArgumentInspection */
	extract($user, \EXTR_OVERWRITE);
	require_once '_pollstatus.inc';
	$pollid = memcached_single_if_not_self_nor_any_admin(
		$userid,
		['userpoll', 'poll'], '
		SELECT POLLID
		FROM userpoll
		JOIN poll USING (POLLID)
		WHERE STATUS = '.POLLSTATUS_ACCEPTED.'
		  AND userpoll.USERID = '.$userid,
		TEN_MINUTES
	);
	if ($user['STATUS'] === 'banned') {
		$ban = memcached_single_assoc('banpoint','
			SELECT CSTAMP,EXPIRES
			FROM banpoint
			WHERE USERID='.$userid.'
			ORDER BY OFFENSEID DESC
			LIMIT 1'
		);
		$user['BAN_STAMP']   = $ban ? $ban['CSTAMP']  : 0;
		$user['BAN_EXPIRES'] = $ban ? $ban['EXPIRES'] : 0;
	}
	$self = (CURRENTUSERID === $userid);

	$user_data = [
		'COUNTER'	=>	(int)memcached_single_if_not_self_nor_any_admin(
				$userid, 'user_data_counter', 'SELECT COUNTER FROM user_data_counter WHERE USERID = '.$userid, TEN_MINUTES),
		'LAST_USED'	=>	$self
			?	CURRENTSTAMP
			: 	(int)memcached_single_if_not_self_nor_any_admin(
				$userid, 'user_data', 'SELECT LAST_USED FROM user_data WHERE USERID = '.$userid, TEN_MINUTES)
	];
	require_once '_userpart.inc';
	require_once '_presence.inc';
	make_nick_header($user);

	if (!($esettings = external_settings_list($userid))) {
		return;
	}

	layout_open_section_header();
	?><a rel="me" href="<?= get_element_href('user', $userid, $user['NICK']) ?>"><?= Eelement_name('profile') ?></a><?
	?> <small class="light"><?= MIDDLE_DOT_ENTITY, NO_BREAK_SPACE_ENTITY, $userid ?></small><?
	layout_continue_section_header();
	layout_display_offense_link($userid,'user',$userid);
	layout_display_contacts('user',$userid);
	layout_close_section_header();
	if (invisible_profile($userid)) {
		return;
	}
	$helpdesk_admin = have_admin('helpdesk');

	if (have_user()) {
		user_menu($user);
		if (have_admin()) {
			require_once '_ticketlist.inc';
			show_ticketlist_for_user($userid);
		}

		// if registered, show user menu
		if (CURRENTUSERID !== $userid) {
			$userhref = '/user/'.$userid;
			$buddystamp = memcached_single('buddy', '
				SELECT ASTAMP
				FROM buddy
				WHERE USERID_ACC = '.CURRENTUSERID.'
				  AND USERID_INI = '.$userid.'
				   OR USERID_INI = '.CURRENTUSERID.'
				  AND USERID_ACC = '.$userid
			);
			require_once '_notify.inc';
			require_once '_blocks.inc';
			layout_open_menu();
			if ($may_send_message
			=	is_reachable($user,NOTIFY_MSG)
			&&	!blocked(BLOCK_PRIVATE_MESSAGES,$userid)
			) {
				layout_menuitem(__C('action:write_message'),'/msg/form/'.$userid);
			}
			if (!isset($buddystamp)) {
				// there is no row, so no buddy at all!
				// allow current user to make USERID a buddy!
				// or maybe pending request?

				$buddyreq = memcached_single_assoc('buddy_request','
					SELECT USERID_INI,USERID_ACC,CSTAMP
					FROM buddy_request
					WHERE USERID_ACC='.CURRENTUSERID.'
					  AND USERID_INI='.$userid.'
					   OR USERID_INI='.CURRENTUSERID.'
					  AND USERID_ACC='.$userid
				);
				if (!$buddyreq) {
					if (!blocked(BLOCK_BUDDY_REQUESTS,$userid)
					&&	is_reachable($user,NOTIFY_BUDDY)
					) {
						layout_open_menuitem();
						if (memcached_single(
							array('buddy_request','user_account'),'
							SELECT COUNT(*)
							FROM buddy_request
							JOIN user_account ON USERID=USERID_ACC
							WHERE STATUS="active"
							  AND USERID_INI='.CURRENTUSERID
							)
							> MAX_BUDDY_REQUESTS
						) {
							require_once '_bubble.inc';
							$bubble = new bubble(BUBBLE_CLEAN | CATCHER_UNAVAILABLE | BUBBLE_BLOCK | BUBBLE_CURSOR);
							$bubble->catcher(__C('action:request_friendship'));//'Maak '.$otherpart
							$bubble->content(__('user:error:too_many_buddy_requests_LINE',array('MAX'=>MAX_BUDDY_REQUESTS)));
							$bubble->display();
						} else {
							?><a href="<?= $userhref; ?>/makebuddy"><?= __C('action:request_friendship') ?></a><?
						}
						layout_close_menuitem();
					}
				} else {
					layout_open_menuitem();
					if ($buddyreq['USERID_INI'] === CURRENTUSERID) {
						// we are the initiator
						?><a href="<?= $userhref; ?>/buddyremove"><?= __C('action:remove_buddy_request') ?></a><?
					} else {
						if (!blocked(BLOCK_BUDDY_REQUESTS,$userid)) {
							?><a href="<?= $userhref ?>/acceptbuddy"><?= __C('action:accept_buddy') ?></a><?
							layout_next_menuitem();
						}
						?><a href="<?= $userhref ?>/buddyremove"><?= __C('actoin:reject_buddy') ?></a><?
					}
					layout_close_menuitem();
				}
			} else {
				// there is an existing buddy relationship
				// allow the user to cancel it
				layout_open_menuitem();
				with_confirm(
					__C('action:remove_buddy'),
					$userhref.'/buddyremove',
					__C('user:confirm:buddy_removal_LINE')
				);
				layout_close_menuitem();
			}
			layout_open_menuitem();
			?><a href="<?= $userhref; ?>/ignoreform"><?= __C('action:ignore'); ?></a><?
			layout_next_menuitem();
			?><a href="<?= $userhref; ?>/blockform"><?= __C('action:block'); ?></a><?

			$have_conv = memcached_single('conversation_v2','
				SELECT 1 FROM conversation_v2
				WHERE I_USERID='.CURRENTUSERID.'
				  AND WITH_USERID='.$userid
			);
			if ($have_conv) {
				layout_next_menuitem();
				?><a href="/msg/conversationinfo/<?= $userid ?>"><?= Eelement_name('conversation') ?></a><?
			}
			if ($user['STATUS'] === 'active'
			&&	have_flock_leader_invite()
			&&	external_setting($userid,'FLOCKINVITE') !== 'never'
			) {
				layout_next_menuitem();
				?><a href="/flock/inviteform/<?= $userid ?>"><?= __C('action:invite') ?></a><?
			}
			layout_close_menuitem();
			layout_close_menu();
			global $__year,$__month,$__day;
			if ($user['STATUS'] === 'active'
			&&	$__month == 2
			&&	(	$__day == 14
				||	$__day == 13
				||	$__day == 12
				)
			&&	(require_once '_blocks.inc')
			&&	!blocked(BLOCK_VALENTINE_HEARTS,$userid)
			) {
				require_once '_heart.inc';
				layout_open_menu();
				layout_open_menuitem();
				$heart = memcached_single_assoc('valentine','
					SELECT ANONYMOUS,BODY
					FROM valentine
					WHERE FROM_USERID='.CURRENTUSERID.'
					  AND TO_USERID='.$userid.'
					  AND YEAR='.$__year
				);
				if (!$heart) {
					$heart = false;
					?><a onclick="unhide('heartform');return false;" href="/user/<?= $userid;
					?>/heartform"><?= __C('action:send_valentine_heart') ?></a><?
					?> <?
					show_heart();
				} else {
					echo Eelement_name('valentine');
					?> <?
					show_heart();
					?> <a onclick="unhide('heartform');return false;" href="/user/<?= $userid;
					?>/heartform"><?= __('action:change') ?></a> <?= MIDDLE_DOT_ENTITY ?> <a href="/user/<?= $userid;
					?>/removeheart"><?= __('action:remove') ?></a><?
				}
				layout_close_menuitem();
				layout_close_menu();
			}
		}
		if ($user['EMAIL']
		&&	(	CURRENTUSERID == $userid
			||	$helpdesk_admin
			)
		) {
			$seen = max(1189757410,memcached_single('emailcheckseen','SELECT SEENSTAMP FROM emailcheckseen WHERE USERID='.$userid));
			# SELF
			require_once '_mailerrors.inc';
			if ($failures = memcached_rowuse_array('contact_undeliverable', "
				SELECT CIMID, CSTAMP, FAILREASON
				FROM contact_undeliverable
				WHERE CSTAMP > $seen
				  AND FAILMAIL ='".addslashes($user['EMAIL'])."'
				ORDER BY CIMID DESC")
			) {
				$supervisor = have_admin('helpdesksuper');
				layout_open_box('white body');
				?><div class="block"><span class="warning"><?= Eelement_plural_name('mail_problem') ?></span><br /><?
				echo __('user:info:errors_occured_when_sending_email_TEXT', DO_NL2BR | DO_UBB);
				?></div><?
				layout_open_table('vtop');
				foreach ($failures as $failure) {
					show_mail_error_row($failure, $supervisor);
				}
				layout_close_table();
				?><div class="block"><?= __('user:info:do_you_use_an_other_email_TEXT', DO_UBB, ['USERID' => $userid]) ?></div><?
				show_mailproblems_solved_question();
				layout_close_box();
				if (CURRENTUSERID === $userid) {
					show_mailproblems_mark_seen();
				}
			}
		}
	}
	if (isset($heart)) {
		?><div<?
		if ($_REQUEST['ACTION'] !== 'heartform') {
			?> class="hidden"<?
		}
		?> id="heartform"><?

		?><form<?
		?> method="post"<?
		?> action="/user/<?= $userid ?>/commitheart"<?
		?> onsubmit="return submitForm(this);"><?

		layout_open_box('white');
		layout_open_table('vtop fw');
		layout_start_row();
		?><label for="anonymous"><?= __C('field:anonymous') ?></label><?
		layout_field_value();
		?><input type="checkbox" name="ANONYMOUS" id="anonymous" value="1"<?
		if (!empty($heart['ANONYMOUS'])) {
			?> checked="checked"<?
		}
		?>><?
		layout_restart_row();
		?><label for="bodyc"><?= __C('valentine:include_message') ?></label><?
		layout_field_value();
		?><input type="checkbox" name="BODYC" id="bodyc" value="1" onclick="setdisplay('body-row', this.checked);"<?
		if (!empty($heart['BODY'])) {
			?> checked="checked"<?
		}
		?>><?

		layout_restart_row(empty($heart['BODY']) ? ROW_HIDDEN : 0,'body-row');
		echo Eelement_name('real_message');
		layout_field_value();
		show_textarea([
			'name'		=> 'BODY',
			'maxlength'	=> 500,
			'class'		=> 'growToFit',
			'value'		=> $heart
		]);
		layout_stop_row();
		layout_close_table();
		layout_close_box();
		?><div class="block"><input type="submit" value="<?= __($heart ? 'action:change' : 'action:add') ?>" /></div><?
		?></form></div><?
	}

	if ($user['STATUS'] === 'deceased') {
		$deadinfo = memcached_single_assoc('deceasedinfo','SELECT DSTAMP,CONDOLENCES FROM deceasedinfo WHERE USERID='.$userid);
	}
	require_once 'defines/dating.inc';

	$dating_types = $user['DATING'] & DTNG_TYPES;

	$show_dating =
		$dating_types > DTNG_UNSPECIFIED
	&&	($user['DATING'] & DTNG_SHOW_TO_ALL)
	||	(	$GLOBALS['currentuser']->row['DATING']
		&&	!empty($user['DATING'])
		&&	(	$user['STATUS'] === 'active'
			||	CURRENTUSERID === $userid
			||	!empty($may_send_message)
			)
		);

	$is_online =
		CURRENTUSERID !== $userid
	&&	CURRENTSTAMP - $user_data['LAST_USED'] < 300
	&&	_visibility($user, 'ONLINE', have_admin())
	&&	$user['STATUS'] === 'active';

	$test_icon = false;
	$online_div = !$test_icon && $is_online;

	if ($show_dating) {
		require_once '_dating.inc';
		?><div class="dating block"><?
		show_dating_badges($user);
		?></div><?
	}

	?><article itemscope itemtype="https://schema.org/Person"><?
	layout_open_box($user['COLOR'], 'mainprofile');
	?><div class="header vbottom<?
	if ($online_div) {
		?> user-is-online<?
	}
	?>"><?
	if ($online_div) {
		?><div class="online"><?
	}

	?><div class="l uh"><div class="bold middle"><?

	if ($test_icon) {
		?><img src="<?= STATIC_HOST ?>/status/<?= ($is_online ? 'online' : 'offline'),is_high_res() ?>.png" class="icon" /> <?
	}

	if (isset($buddystamp)) {
		require_once '_heart.inc';
		$user['BUDDY'] = true;
		show_heart(null,'hrt middle'); ?> <?
	} else {
		$user['BUDDY'] = false;
	}
	?><h1 itemprop="name" class="nowrap"><?
	?><a rel="me" href="<?= get_element_href('user', $userid) ?>"><?=
		_smiley_replace(escape_specials(_smiley_prepare(get_element_title('user',$userid))))
	?></a><?
	?></h1><?
	if ($user['STATUS'] === 'deceased') {
		show_dead(true);
	}
	layout_display_offense_link($userid, 'nick', $userid);

	$totals = [];
	if (_visibility($esettings,'APPEARANCES')) {
		if (false === ($totals['PHOTO_CNT'] = memcached_single(['image','user_appearance'], '
			SELECT COUNT(*)
			FROM user_appearance
			JOIN image USING (IMGID)
			WHERE user_appearance.USERID = '.$userid.'
			  AND HIDDEN = 0',
			TEN_MINUTES))
		) {
			return;
		}
		if ($totals['PHOTO_CNT']) {
			?> <a href="/user/<?= $userid ?>/photos"><?=
				get_camera_icon(element_name('photo', $totals['PHOTO_CNT']))
			?></a><?
		}
	}

	?></div></div><? # END r uh

	if ($agenda_visible = _visibility($esettings, 'AGENDA')) {
		if (false === ($cnts = memcached_single_assoc_if_not_self(
			$userid, ['going', 'party'], /** @lang MariaDB */ '
			SELECT	COUNT(IF(party.STAMP < '.TODAYSTAMP.' AND MAYBE = 0, 1, NULL)) AS PASTPARTY,
					COUNT(IF(party.STAMP > '.TODAYSTAMP.', 1, NULL)) AS FUTURE_TOTAL,
					COUNT(IF(party.STAMP > '.TODAYSTAMP.' AND MAYBE = 1, 1, NULL)) AS FUTURE_INTERESTED,
					COUNT(IF(party.STAMP < '.TODAYSTAMP." AND MAYBE = 1, 1, NULL)) AS PAST_INTERESTED
			FROM going
			JOIN party USING (PARTYID)
			WHERE going.USERID = $userid
			  AND ACCEPTED
			  AND CANCELLED = 0
			  AND MOVEDID = 0
			  AND POSTPONED = 0",
			HALF_HOUR))
		) {
			return;
		}
		if ($cnts) {
			$totals = [...$totals, ...$cnts];
		} else {
			$totals['PASTPARTY']		 = 0;
			$totals['FUTURE_TOTAL']		 = 0;
			$totals['FUTURE_INTERESTED'] = 0;
			$totals['PAST_INTERESTED']	 = 0;
		}
	}
	$self = CURRENTUSERID === $userid;
	$helpdesk_admin = have_admin('helpdesk');

	$reportcnts = memcached_read_if_not_self_nor_admin(
		$userid, 'report', 'report','
		SELECT COUNT(*), COUNT(IF(NOT ANONYMOUS, 1, NULL))
		FROM report
		WHERE USERID = '.$userid,
		SIMPLE_HASH
	);
	if ($reportcnts === false) {
		return;
	}
	[$total, $visible] = keyval($reportcnts);
	$totals['REPORTCNT'] =
			have_admin('report')
		||	$self
		?	$total
		:	$visible;

	require_once '_offense_access.inc';
	$show_stats = _visibility($esettings, 'STATISTICS', have_offense_admin() || $helpdesk_admin);
	$self_or_helpdesk = $self || $helpdesk_admin;
	if ($self_or_helpdesk) {
		if (false === ($info = memcached_single_assoc(['buddy', 'user_account'], "
			SELECT COUNT(*) AS BUDDY_CNT, COUNT(IF(STATUS = 'active', 1, NULL)) AS INACTIVE_BUDDY_CNT
			FROM buddy
			JOIN user_account
			  ON USERID = IF(USERID_INI = $userid, USERID_ACC, USERID_INI)
			WHERE USERID_INI = $userid
			   OR USERID_ACC = $userid",
			FIVE_MINUTES))
		) {
			return;
		}
		if ($info) {
			$totals = [...$totals, ...$info];
		} else {
			$totals['BUDDY_CNT']		  = 0;
			$totals['INACTIVE_BUDDY_CNT'] = 0;
		}
	} elseif (_visibility($esettings, 'BUDDIES')) {
		$totals['BUDDY_CNT'] = memcached_single(['buddy', 'user_account'], "
			SELECT COUNT(*)
			FROM (	SELECT IF(USERID_INI = $userid, USERID_ACC, USERID_INI) AS USERID
					FROM buddy
					WHERE USERID_INI = $userid
					   OR USERID_ACC = $userid
				 ) AS buddies
			JOIN user_account USING (USERID)
			WHERE STATUS = 'active'",
			FIVE_MINUTES
		) ?: 0;
	}

	$parts = [];
	if (!empty($totals['BUDDY_CNT'])) {
		[$linkopen,$linkclose] = get_action_open_close_for_setting('buddies','SHOW_USER_BUDDIES');
		$parts[] = $linkopen.element_name('buddy', $totals['BUDDY_CNT']).$linkclose.'<small>,'.$totals['BUDDY_CNT'].'</small>';
	}
	require_once '_albumcount.inc';
	require_once '_buddies.inc';
	[$mayview] = album_access($userid);
	$info = album_total_counts($userid,$mayview ? NOBODY : get_current_visi($userid));
	if ($info) {
		[$mapcnt, $elemcnt, $unacceptedmapcnt, $unacceptedelemcnt] = $info;
		if ($mapcnt
		||	$elemcnt
		||	(	(	$unacceptedmapcnt
				||	$unacceptedelemcnt
				)
			&&	[$mayview] = album_access($userid)
			&&	$mayview
			)
		) {
			ob_start();
			?><a<?
			if (!$elemcnt) {
				?> class="light"<?
			}
			?> href="<?= get_element_href('album',$userid) ?>"><?= element_name('album') ?></a><?
			if ($elemcnt || $unacceptedelemcnt) {
				?><small><?
				if ($elemcnt) {
					?>,<? echo $elemcnt;
				}
				if ($unacceptedelemcnt) {
					?><span class="light"><?
					if ($elemcnt) {
						?>+<?
					} else {
						?>,<?
					}
					echo $unacceptedelemcnt; ?></span><?
				}
				?></small><?
			}
			$parts[] = ob_get_clean();
		}
	}
	if (_visibility($esettings,'FAVOURITES',$helpdesk_admin)
	&&	(require_once '_favourite.inc')
	&&	($totals['FAVCNT'] = $favcnt = get_favourite_count($userid))
	) {
		[$linkopen,$linkclose] = get_action_open_close_for_setting('favourites','SHOW_USER_FAVOURITES');
		$parts[] = $linkopen.element_name('favourite',$favcnt).$linkclose.'<small>,'.$favcnt.'</small>';
	}
	if ($show_stats
	&&	$totals['REPORTCNT']
	) {
#		list($linkopen,$linkclose) = get_action_open_close_for_setting('reports','SHOW_USER_REPORTS');
		$linkopen = '<a href="/report/user/'.$userid.'">';
		$linkclose = '</a>';
		$parts[] = $linkopen.element_plural_name('report').$linkclose.'<small>,'.$totals['REPORTCNT'].'</small>';
	}

	if ($esettings['WEBLOG_VISIBLE']) {
		require_once '_visibility.inc';
		$weblogcnt = memcached_single('weblog','SELECT COUNT(*) FROM weblog WHERE USERID='.$userid._where_visible('weblog','WEBLOG',$userid,null,false),600);
		if (/* $show_weblog = */
			$userid === CURRENTUSERID
		||	$weblogcnt
		) {
			ob_start();
			?><a href="/weblog/user/<?= $userid ?>"><?= element_name('weblog') ?></a><?
			if ($weblogcnt) {
				?><small>,<?= $weblogcnt ?></small><?
			}
			$parts[] = ob_get_clean();
		}
	}

	$guestbook_rflags = _visibility($esettings, 'GUESTBOOK', allow_helpdesk: false);

	require_once '_commentsinfo.inc';
	$user_commentsinfo = get_commentsinfo('user',$userid);
	if ($user_commentsinfo) {
		// condolences get precedence over guestbook, but force guestbook if comment action

		$do_condolences =
			!empty($deadinfo['CONDOLENCES'])
		||	$user['STATUS'] === 'deleted'
		&&	db_single('deaduser_comment', "SELECT 1 FROM deaduser_comment WHERE ID = $userid LIMIT 1");

		$show_condolences =
				$do_condolences
			&&	!str_starts_with($_REQUEST['ACTION'], 'comment')
			&&	(	(	$_REQUEST['ACTION'] === 'single'
					&&	(	setting('SHOW_USER_GUESTBOOK')
						||	(require_once '_comment.inc') && (setting('UNCOLLAPSE_COMMENTS') & (1 << CMT_DEADUSER))
						)
					)
				||	str_starts_with($_REQUEST['ACTION'], 'condolence')
			);

		if (have_admin('user_comment')
		&&	($ucnt = $user_commentsinfo[CMTI_MSGCNT])
		||	$guestbook_rflags
		&&	$esettings['VISIBILITY_GUESTBOOK'] !== NOBODY
		&&	!$user_commentsinfo[CMTI_HIDEALL]
		) {
			$show_guestbook =
				!$show_condolences
			&&	(	(	$_REQUEST['ACTION'] === 'single'
					&&	setting('SHOW_USER_GUESTBOOK')
					&&	empty($deadinfo['CONDOLENCES'])
					)
					||	str_starts_with($_REQUEST['ACTION'], 'comment')
				);
			ob_start();
			$classes = layout_build_rowclasses($guestbook_rflags);
			if ($classes) {
				?><span class="<?= implode(' ', $classes) ?>"><?
			}
			?><a<?
			if ($show_guestbook) {
				?> class="selected"<?
			}
			?> href="<?
	#		if (!$show_guestbook) {
				if ($_REQUEST['ACTION'] === 'single'
				&&	setting('SHOW_USER_GUESTBOOK')
				&&	empty($deadinfo['CONDOLENCES'])
				) {
					echo get_element_href('user', $userid, $user['NICK']);
				} else {
					?>/user/<?= $userid ?>/comments<?
				}
	#		}
			?>#<?= setting('COMMENT_PAGE_ORDER') === 'chrono' ? 'bottom' : 'cmts'
			?>"><?= element_name('guestbook') ?></a><?
			if (!isset($ucnt)) {
				$ucnt = $user_commentsinfo[CMTI_OKMSGCNT];
			}
			if ($ucnt) {
				?><small>,<?= $ucnt; ?></small><?
			}
			if ($classes) {
				?></span><?
			}
			$parts[] = ob_get_clean();
		}
		if ($do_condolences
		&&	(require_once '_commentsinfo.inc')
		&&	($deaduser_commentsinfo = get_commentsinfo('deaduser', $userid))
		) {
			ob_start();
			?><a<?
			if ($show_condolences) {
				?> class="selected"<?
			}
			?> href="<?
			if (!$show_condolences) {
				if ($_REQUEST['ACTION'] === 'single'
				&&	setting('SHOW_USER_GUESTBOOK')
				&&	!empty($deadinfo['CONDOLENCES'])
				) {
					echo get_element_href('user', $userid, $user['NICK']);
				} else {
					?>/user/<?= $userid ?>/condolences<?
				}
			}
			?>#<?= setting('COMMENT_PAGE_ORDER') === 'chrono' ? 'bottom' : 'cmts' ?>"><?
			$cnt = $deaduser_commentsinfo[CMTI_OKMSGCNT];
			echo element_name('condolence', $cnt);
			?></a><?
			if ($cnt) {
				?><small>,<?= $cnt ?></small><?
			}
			$parts[] = ob_get_clean();
		}
	}
	if ($parts) {
		?><div class="r uh"><?= implode(' '.MIDDLE_DOT_ENTITY.' ',$parts) ?></div><?
	}
	?><div class="clear"></div><?

	if ($online_div) {
		?></div><?
	}
	?></div><?

	require_once '_donators.inc';
	$donation_roles = get_donation_roles_with_money($userid);

	require_once '_profileimage.inc';
	if (false === ($userimage_small = get_user_image($userid))
	||	false === ($userimage = get_user_image($userid, true))
	||	false === ($caption = memcached_single_assoc_if_not_self_nor_any_admin($userid, 'userimagecaption', '
			SELECT BODY, ACCEPTED
			FROM userimagecaption
			WHERE USERID='.$userid))
	) {
		return;
	}
	$width = 0;

	if ($self_or_helpdesk

	||	$userimage
	&&	(	$userimage['ACCEPTED']
		||	$self_or_helpdesk
		)
	||	$caption
	&&	(	$caption['ACCEPTED']
		||	$self_or_helpdesk
		)
	||	$donation_roles
	||	$userimage_small
	&&	(	$userimage_small['ACCEPTED']
		||	$self_or_helpdesk
		)
	) {
		if ($userimage) {
			$width  = $userimage['WIDTH'];
			$height = $userimage['HEIGHT'];
			if ($userimage['LARGE'] > 1 && is_high_res()) {
				$width  >>= 1;
				$height >>= 1;
			}
		}
		?><div class="<?
		if (!SMALL_SCREEN) {
			?>right-float <?
		}
		?>center relative"<?
		if (!SMALL_SCREEN) {
			?> style="width:<?= max(300, $width) ?>px"<?
		}
		?>><?
		?><figure class="noverflow"><?
		if ($userimage
		&&	(	$self_or_helpdesk
			||	$userimage['ACCEPTED']
			)
		) {
 			if ($helpdesk_admin) {
				?><div class="relative center"><?
				?><div class="abs" style="right: 0; top:0;"><?
				layout_display_offense_link($userid, 'profile_image', $userid);
				?></div><?
			}
			?><img itemprop="image"<?
			if (!$userimage['ACCEPTED']) {
				?> class="unaccepted-element"<?
			}
#			if (SMALL_SCREEN) {
#				scale($width,$height,320/2,480/2);
#			}

			if (aspect_objects()) {
				?> width="<?= $userimage['WIDTH'] ?>"<?
				?> height="<?= $userimage['HEIGHT'] ?>"<?
				?> style="margin-bottom: .4em; max-width: 100%; width: <?= $width ?>px; height:auto;"<?
			} else {
				?> style="margin-bottom: .4em; width:<?= $width ?>px;<?
				if (SMALL_SCREEN) {
					?>max-width:90%;<?
				}
				?>"<?
			}

			?> alt="<?= Eelement_name('profile_image') ?> <?= MIDDLE_DOT_ENTITY ?> <?= escape_specials($user['NICK']) ?>"<?
			?> src="<?= IMAGES_HOST, ($src = "/images/user/{$userid}_{$userimage['DATAID']}.{$userimage['TYPE']}") ?>"><?
			if ($helpdesk_admin) {
				?></div><?
			}
		}
		if (!empty($caption['BODY'])
		&&	(	$self_or_helpdesk
			||	$caption['ACCEPTED']
			)
		) {
			?><figcaption><?
			?><div class="<?
			if (!$caption['ACCEPTED']) {
				?>unaccepted-element <?
			}
			?>block"<?
 			if ($helpdesk_admin) {
				?> style="position: relative;"><div style="position: absolute; right: 0; top: 0;"><?
				layout_display_offense_link($userid, 'profile_image_caption', $userid);
				?></div<?
			}
			?>><?= make_all_html($caption['BODY'], UBB_HTML | UBB_HTML_ENTITIES | UBB_SMILEYS) ?></div><?
			?></figcaption><?
		}
		if ($donation_roles) {
			?><div class="light block nowrap"><?= implode(', ', $donation_roles) ?></div><?
		}
		if ($userimage_small
		&&	(	$self_or_helpdesk
			||	$userimage_small['ACCEPTED']
			)
		) {
			?><div class="<?
			if (!$userimage_small['ACCEPTED']) {
				?>unaccepted-element <?
			}
			?>light block"><?
			?><a href="<?= IMAGES_HOST.($smallsrc = "/images/user/{$userid}_{$userimage_small['DATAID']}.{$userimage_small['TYPE']}") ?>"><?=
				element_name('message_photo')
			?> &rarr;</a><?
			if ($helpdesk_admin
			&&	!$userimage
			) {
				layout_display_offense_link($userid, 'profile_image', $userid);
			}
			?></div><?
		}
		if ($self_or_helpdesk) {
			?><div class="block"><?
			?><a href="/userimage/<?= $userid ?>/form"><?= __('action:change_profile_images') ?></a><?
			?></div><?
		}

		?></figure><?
		?></div><?
		if (($showsrc = !empty($src)	  ? [	  $src, $userimage]		  : null)
		||	($showsrc = !empty($smallsrc) ? [$smallsrc, $userimage_small] : null)
		) {
			[$usesrc, $usrimg] = $showsrc;
			include_og('og:image', FULL_IMAGES_HOST.$usesrc);
			include_og('og:image:width',  $usrimg['WIDTH']);
			include_og('og:image:height', $usrimg['HEIGHT']);
		}
	}
	if (($unactive = $user['STATUS'] === 'unactive')
	||	$user['STATUS'] === 'inactive'
	) {
		?><div class="block"><?
		if ($unactive
		||	!$user_data['LAST_USED']
		) {
			?>Deze gebruiker is nieuw en is sinds zijn aanmelding nog niet weer langsgeweest.<?
		} else {
			?>Deze gebruiker is al geruime tijd niet meer langsgeweest en staat derhalve op non-actief.<?
		}
		?></div><?
	}

	// show only regular user information here

	$roles = [element_name('user')];
	require_once '_donators.inc';
	if ($donation_roles = get_donation_roles($userid)) {
		$roles = [...$roles, ...$donation_roles];
	}
	if (is_admin($userid)) {
		$roles[] = element_name('employee');
	}
	?><span itemscope itemtype="https://schema.org/Role"><?
	foreach ($roles as $role) {
		?><meta itemprop="roleName" content="<?= $role ?>" /><?
	}
	?></span><?

	if ($user['ACCEPTED']
	||	$self_or_helpdesk
	) {
		$list = new deflist(
			(isset($_REQUEST['NODYN']) ? 'nodyn ' : null).
			'deflist vtop forcewrap'.
			($helpdesk_admin ? ' relative' : null).
			($user['ACCEPTED'] ? null : ' unaccepted-element')
		);

		if ($user['NAME']
		&&	($row_class = visibility_class($user, 'NAME')) !== false
		) {
			$list->set_row_class($row_class);
			$list->set_itemprop('givenName');
			$list->add_row(Eelement_name('name'), escape_utf8($user['NAME']));
		}

		$list->start_group('itemprop="address" itemscope itemtype="https://schema.org/PostalAddress"');
		if ($user['CITY']
		&&	(	have_admin(['user', 'city'])
			||	CURRENTUSERID === $userid
			)
		) {
			$list->set_row_class('self');
			ob_start();
			echo escape_utf8($user['CITY']);
			if ($user['COUNTRYID']) {
				?> (<span itemprop="addressCountry"><?= get_element_link('country',$user['COUNTRYID']); ?></span>)<?
			}
			require_once '_cityvalidation.inc';
			if ($user['INVALID_CITY'] === CITY_CHECK) {
				?> (<?= __('attrib:not_validated_yet'); ?>)<?
			} elseif ($user['INVALID_CITY']) {
				?><br /><span class="invalid"><?= __('field:unaccepted'); ?>, <? city_show_invalid_reason($user['INVALID_CITY']); ?></span><?
			}
			$list->add_row(Eelement_name('residence'), ob_get_clean());
		} elseif (
			$user['CITYID']
		&&	($row_class = visibility_class($user, 'CITY')) !== false
		&&	(	!$user['INVALID_CITY']
			||	have_self_or_admin($userid, ['city', 'helpdesk'])
			)
		&&	($city = memcached_single_assoc_if_not_self_nor_admin(
				$userid,
				'city',
				['city','province'],'
				SELECT	city.NAME,city.PROVINCEID,city.COUNTRYID,TIMEZONE,REGIONID,
					province.NAME AS PROVINCE_NAME
				FROM city
				LEFT JOIN province USING (PROVINCEID)
				WHERE CITYID='.$user['CITYID'],
				ONE_HOUR
			))
		) {
			$list->set_row_class($row_class);
			ob_start();
			?><span itemprop="addressLocality"><?=
				get_element_link('city', $user['CITYID'], $city['NAME'])
			?></span><?
			if (($countryid = $city['COUNTRYID']) === 1) {
				if ($city['PROVINCE_NAME']) {
					$have_addr = true;
					?> <span class="light">(<span itemprop="addressRegion"><?= get_element_link('province',$city['PROVINCEID'],$city['PROVINCE_NAME']) ?></span>)</span><?
				}
			} else {
				if ($city['REGIONID']) {
					?>, <span itemprop="addressRegion"><?= get_element_link('region',$city['REGIONID']) ?></span><?
				}
				?> <span class="light">(<?
				if ($city['PROVINCE_NAME']) {
					?><span itemprop="addressRegion"><?= get_element_link('province', $city['PROVINCEID'], $city['PROVINCE_NAME']) ?></span>, <?
				}
				?><span itemprop="addressCountry"><?=
					get_element_link('country',$city['COUNTRYID'])
				?></span><?
				?>)</span><?
			}
			if ($user['INVALID_CITY']) {
				require_once '_cityvalidation.inc';
				if ($user['INVALID_CITY'] === CITY_CHECK) {
					?> (<?= __('attrib:not_validated_yet') ?>)<?
				} else {
					?><br /><span class="invalid"><?= __('field:unaccepted') ?>, <a href="/ticket/form?ELEMENT=helpdesk;ID=<?= $userid
					?>"><? city_show_invalid_reason($user['INVALID_CITY']);
					?></a></span><?
				}
			}
			$list->add_row(Eelement_name('residence'), ob_get_clean());

			if ($countryid) {
				$list->set_row_class($row_class);
				ob_start();
				?><span itemprop="addressCountry"><?=
					get_element_link('country',$countryid)
				?></span><?
				require_once '_countryflag.inc';
				?> <?
				show_country_flag($countryid,'light',true,false);
				$list->add_row(Eelement_name('country'), ob_get_clean());
			}
		} elseif (
			($countryid = $user['COUNTRYID'])
		&&	($row_class = visibility_class($user, 'CITY')) !== false
		&&	(	!$user['INVALID_CITY']
			||	have_self_or_admin($userid, ['city', 'helpdesk'])
			)
		) {
			$list->set_row_class($row_class);
			ob_start();
			?><span itemprop="addressCountry"><?
			echo get_element_link('country',$countryid);
			?></span><?
			require_once '_countryflag.inc';
			?> <?
			show_country_flag($countryid,'light',true,false);
			if ($user['INVALID_CITY']) {
				require_once '_cityvalidation.inc';
				if ($user['INVALID_CITY'] === CITY_CHECK) {
					?> (<?= __('attrib:not_validated_yet'); ?>)<?
				} else {
					?><br /><span class="invalid"><?= __('field:unaccepted'); ?>, <a href="/ticket/form?ELEMENT=helpdesk;ID=<?= $userid;
					?>"><? city_show_invalid_reason($user['INVALID_CITY']);
					?></a></span><?
				}
			}
			$list->add_row(Eelement_name('country'),ob_get_clean());
		}
		$list->end_group();

		if (($occupation = utf8_mytrim($user['OCCUPATION'],'-.'))
		&&	($row_class = visibility_class($user, 'OCCUPATION')) !== false
		) {
			?><meta itemprop="hasOccupation" content="<?= escape_utf8($occupation) ?>" /><?
			$list->set_row_class($row_class);
			$list->add_row(Eelement_name('occupation'), escape_utf8($occupation));
		}
		if ($user['BIRTH_DAY']
		||	$user['BIRTH_MONTH']
		||	$user['BIRTH_YEAR']
		) {
			if (($row_class = visibility_class($user, 'BIRTHDAY' )) !== false) {
				if ($BIRTH_DAY)	{
					$bd[] = $BIRTH_DAY;
				}
				if ($BIRTH_MONTH) {
					$bd[] = _month_name($BIRTH_MONTH);
				}
				if ($BIRTH_YEAR) {
					$bd[] = $BIRTH_YEAR;
				}
				$list->set_row_class($row_class);
				ob_start();
				if ($stamp = count($bd) === 3) {
					?><time itemprop="birthDate" datetime="<? printf('%d-%02d-%02d', $BIRTH_YEAR, $BIRTH_MONTH, $BIRTH_DAY) ?>"><?
				}
				echo implode(' ', $bd);
				if ($stamp) {
					?></time><?
				}
				$list->add_row(Eelement_name('birthdate'), ob_get_clean());

				if ($user['BIRTH_DAY'  ] === 29
				&&	$user['BIRTH_MONTH'] ===  2
				) {
					global $__year, $__month, $__day;
					if ($thisy = checkdate(2,29,$__year)) {
						$day   = 29;
						$month =  2;
					} else {
						$day = $user['LDAY'];
						$month = ($day === 1 ? 3 : 2);
					}
					$year = $__month > $month || $__month === $month && $__day > $day ? $__year + 1 : $__year;
					if ($year === $__year ? !$thisy : !checkdate(2,29,$year)) {
						$list->set_row_class($row_class);
						$list->add_row(Eelement_name('birthday'),
							$month === $__month
							&&	$day === $__day
							?	__('date:today')
							:	$user['LDAY'].' '.
								_month_name($user['LDAY'] === 1 ? 3 : 2).' '.
								(	$__month > $month
								||	$__month === $month && $__day > $day ? $__year + 1 : $__year)
						);
					}
				}
			}
			if ($user['STATUS'] !== 'deceased'
#			&&	$user['BIRTH_DAY']
#			&&	$user['BIRTH_MONTH']
			&&	$user['BIRTH_YEAR']
			&&	($row_class = visibility_class($user,'AGE')) !== false
			) {
				$age = implode(' &ndash; ', _age_range($user['BIRTH_YEAR'], $user['BIRTH_MONTH'], $user['BIRTH_DAY'], CURRENTSTAMP, $user['LDAY']));
				$list->set_row_class($row_class);
				$list->add_row(Eelement_name('age'), $age);
			}
		}

		// DECEASED DATE
		if (!empty($deadinfo['DSTAMP'])) {
			require_once '_status.inc';
			change_timezone(empty($city['TIMEZONE']) ? 'Europe/Amsterdam' : $city['TIMEZONE']);
			[$y,$m,$d] = _getdate($deadinfo['DSTAMP']);
			ob_start();
			?><time itemprop="deathDate" datetime="<? printf('%d-%02d-%02d',$y,$m,$d) ?>"><?
			_date_display($deadinfo['DSTAMP']);
			?></time><?
			$list->add_row(__C('status:deceased'), ob_get_clean());
			change_timezone();

			if ($user['BIRTH_YEAR']
			&&	$user['BIRTH_MONTH']
			&&	$user['BIRTH_DAY']
			&&	($row_class = visibility_class($user, 'AGE')) !== false
			) {
				$list->add_row(
					Eelement_name('age'),
					compute_age($user['BIRTH_YEAR'], $user['BIRTH_MONTH'], $user['BIRTH_DAY'], $deadinfo['DSTAMP'], $user['LDAY'])
				);
			}
		}
		if ($user['SEX']
		&&	($row_class = visibility_class($user,'GENDER')) !== false
		) {
			global $head;
			$head .= '<meta property="profile:gender" content="'.(($male = $user['SEX'] === 'M') ? 'male' : 'female').'" />';

			$list->set_row_class($row_class);
			$list->add_row(Eelement_name('gender'), __($male ? 'gender:male' : 'gender:female'));
			require_once '_gender.inc';
			show_gender_schema($user['SEX']);
		}

		if ($user['SEXPREF']
		&&	($row_class = visibility_class($user,'SEXPREF')) !== false
		) {
			$list->set_row_class($row_class);
			ob_start();
			switch ($user['SEXPREF']) {
			case 'bi':	echo __('sexpref:bisexual'); break;
			case 'hetero':	echo __('sexpref:straight'); break;
			case 'homo':
				switch ($user['SEX']) {
				default:	echo __('sexpref:gay'); break;
				case 'F':	echo __('sexpref:lesbian'); break;
				}
				break;
			}
			$list->add_row(Eelement_name('(sexual)orientation'), ob_get_clean());
		}

		if ($user['RELATION']
		&&	(	($row_class = visibility_class($user,'RELATION')) !== false
			||	 CURRENTUSERID === $user['RELATIONID']
			)
		) {
			$list->set_row_class($row_class === false ? 'self' : $row_class);
			ob_start();
			switch ($user['RELATION']) {
			case 'no':
				echo __('answer:no');
				break;

			case 'yes':
				$otherisme = (CURRENTUSERID === $user['RELATIONID']);
				if ($self) {
					$menuset[] = '<a href="/user/chooserelation">'.__('action:choose_partner').'</a>';
				}
				echo __('answer:yes');

				if ($user['RELATIONID']) {
					$relation = memcached_single_assoc_if_not_self($userid, 'user', '
						SELECT NICK,RELATIONID,RELATION,DATING
						FROM user
						WHERE USERID='.$user['RELATIONID'],
						TEN_MINUTES
					);
					if ($relation) {
						if ($relation['RELATIONID'] === $userid
						&&	$relation['RELATION'] === 'yes'
						) {
							?>, <?= __('field:(relation)_with') ?> <?
							echo get_element_link('user',$user['RELATIONID'],$relation['NICK'],null,'sweetheart');
						} elseif (CURRENTUSERID === $user['RELATIONID']) {
							$menuset[] =
								'<a href="/user/'.CURRENTUSERID.'/setrelation/'.$userid.'">'.
								__('action:confirm').'</a>';
						} elseif (
							(	$self
							||	$otherisme
							||	$helpdesk_admin
							)
						) {
							?>,<span class="light"> met <?=
								get_element_link('user',$user['RELATIONID'],$relation['NICK'],null,'crush')
							?></span><?
							$menuset[] = __('attrib:not_confirmed_yet');
						}
						if ($self) {
							$menuset[] =
								'<a href="/user/'.$userid.'/setrelation/0">'.
								__('action:remove').'</a>';
						}
					}
				}
				if (isset($menuset)) {
					?> <span class="nowrap light">(<?= implode(', ',$menuset); ?>)</span><?
				}
				break;
			}
			$list->add_row(Eelement_name('loverelation'), ob_get_clean());
		}

		if ($all_employees = memcached_rowuse_array(['organizationmember','organization','locationmember','location','artist','artistmember'], /** @lang MariaDB */ "
		(
			SELECT ID, NAME, VISIBILITY_NAME, ACCEPTED, IF(ACCEPTED, 1, 0) AS VISIBLE_TO_ALL, 'artist' AS TYPE
			FROM artistmember 
			JOIN artist ON ARTISTID=ID
			WHERE artistmember.USERID = $userid AND ACTIVE /* AND DECEASED=0*/
		) UNION	(
			SELECT ID, NAME, VISIBILITY_NAME, ACCEPTED, IF(ACCEPTED AND DEADSTAMP = 0 AND FOLLOWUPID = 0, 1, 0) AS VISIBLE_TO_ALL, 'organization' AS TYPE
			FROM organizationmember
			JOIN organization ON ORGANIZATIONID = ID
			WHERE organizationmember.USERID = $userid AND ACTIVE/* AND DEADSTAMP=0*/
		) UNION ( 
			SELECT ID, NAME, VISIBILITY_NAME,ACCEPTED, IF(ACCEPTED AND DEAD = 0 AND FOLLOWUPID = 0, 1,0 ) AS VISIBLE_TO_ALL, 'location' AS TYPE
			FROM locationmember
			JOIN location ON LOCATIONID = ID
			WHERE locationmember.USERID = $userid AND ACTIVE/* AND NOT (DEAD=1 OR FOLLOWUPID!=0)*/
		)
			ORDER BY CASE TYPE WHEN 'artist' THEN 1 WHEN 'organization'  THEN 2 ELSE 3 END, NAME",
			TEN_MINUTES)
		) {
			$is_admin = have_admin();
			$hidden_orgs = get_hidden_orgs();
			$visisize = [];
			$visibilities = [];
			$light = [];
			foreach ($all_employees as $employee) {
				if ($employee['TYPE'] === 'organization'
				&&	isset($hidden_orgs[$employee['ID']])
				) {
					if (!have_admin('organization')) {
						continue;
					}
					$employee['HIDDEN'] = true;
				}
				if (!$employee['ACCEPTED']) {
					if (!have_admin($employee['TYPE'])) {
						continue;
					}
					$employee['HIDDEN'] = true;
				}
				$employee['USERID'] = $userid;
				if (($employee['VISI'] = visibility_class($employee, 'NAME', $is_admin, visibility: $visibility)) !== false) {
					$show_employees[$employee['TYPE']][] = $employee;
					increment_or_set($visisize, $employee['TYPE']);
					$visibilities[$employee['TYPE']][] = $visibility;
					if ($employee['VISI']) {
						$light[$employee['TYPE']] = true;
					}
				}
			}
			if (isset($show_employees)) {
				foreach ($visibilities as $employer_element => $visibilities_list) {
					$invisible_employer[$employer_element] = (array_sum($visibilities_list) !== 0);
				}
				foreach ($show_employees as $employer_element => $show_employees_list) {
					if ($all_invisible_employer = $invisible_employer[$employer_element]) {
						$list->set_row_class('light');
					}
					ob_start();
					layout_display_list(
						$employer_element,
						$show_employees_list,
						$all_invisible_employer
					);
					$list->add_row(Eelement_name($employer_element, $visisize[$employer_element]), ob_get_clean());
				}
			}
		}
		if (CURRENTUSERID === $userid
		||	have_admin()
		) {
			if ($relations = memcached_rowuse_hash(['relation', 'relationmember'], '
				SELECT relationmember.RELATIONID, NAME, DEAD
				FROM relationmember
				JOIN relation USING (RELATIONID)
				WHERE relationmember.USERID = '.$userid.'
				  AND REMOVED = 0
				ORDER BY NAME ASC',
				TEN_MINUTES)
			) {
				$list->set_row_class('light');
				# Contact voor ?
				$list->add_row(Eelement_name('relation'), get_element_list('relation',$relations));
			}
		}
		if (($row_class = visibility_class($user,'FAVMUSIC')) !== false
		&&	(require_once '_genrelist.inc')
		&&	($genres = _genrelist_get_item_styles('user', $userid, $gcnt))
		) {
			$list->set_row_class($row_class);
			ob_start();
			if ($have_favourite_genres = $self && agenda_favourite_genres_count()) {
				?><a href="/agenda/genre/favourites"><?
			}
			echo Eelement_name('favourite_genre', $gcnt);
			if ($have_favourite_genres) {
				?></a><?
			}
			$list->add_row(ob_get_clean(), $genres);
		}
		if (!ROBOT
		&&	$user['SITE']
		&&	($row_class = visibility_class($user, 'SITE')) !== false
		) {
			$sites = preg_split('/[\s\t\r\n]+/u', win1252_to_utf8($user['SITE']));
			$url_cnt = 0;
			require_once '_url.inc';
			require_once '_urlcheck.inc';
			foreach ($sites as $site) {
				$site = cleanup_url($site, true);
				if (!($bad_site = site_is_bad($site))
				&&	is_safe_url($site)
				&&	preg_match('!^(?:(?:http|ftp)s?:/+)?(?:(?:www|ftp|m(?:obile)?|[a-z]{2})\.)?(?<host>[^/]+)(?<path>/.*)?$!iu', $site)
				) {
					add_utm($site, 'user', $userid);
					[$host, $have_path] = get_url_for_display($site);
					$url_list[] = '<a target="_blank" rel="ugc nofollow" href="'.escape_utf8(strip_white_space($site, true)).'">'.
									escape_utf8($host).($have_path ? '&hellip;' : '').
								 '</a>';
					++$url_cnt;
				} elseif ($self || have_admin('user')) {
					$url_list[] =
						'<span class="unavailable forcewrap">'.escape_utf8($site).'</span>'.
						' (<span class="warning">'.element_name($bad_site ? 'bad_site' : 'no_website').'</span>)';
				}
			}
			if (isset($url_list)) {
				$list->set_row_class($row_class);
				$list->add_row(
					Eelement_name('favourite_site', $url_cnt),
					'<div style="max-height: 15em; overflow-y: auto;">'.implode('<br />',$url_list).'</div>'
				);
			}
		}
		if (have_presence()
		&&	($row_class = visibility_class($user, 'PRESENCE')) !== false
		) {
			$list->set_row_class($row_class);
			show_presence_row($list, true);
		}
		if ($list->have_rows()) {
#			layout_open_table('allborders default vtop'.($helpdesk_admin ? ' relative' : null).($user['ACCEPTED'] ? null : ' unaccepted-element'));

			if ($helpdesk_admin) {
				ob_start();
				layout_display_offense_link($userid,'profile',$userid);
				$list->add_caption(ob_get_clean(),'class="abs" style="top:0;right:0"');
			}
			$list->display();
			if (SMALL_SCREEN) {
				?><hr class="slim"><?
			}
		}
	}

	/** @noinspection SuspiciousAssignmentsInspection */
	$list = new deflist('deflist vtop');

	$list->add_row(__C('user:info:member_since'), _datetime_get($user['CSTAMP']));

	if ($helpdesk_admin
	&&	($lstamp = db_single('passwd_mailed','SELECT MAX(STAMP) FROM passwd_mailed WHERE USERID='.$userid))
	) {
		$list->add_row(Eelement_name('password_request'), _date_getnice(PRINTNICE_DATE_TIME,$lstamp));
	}

	if (isset($buddystamp)) {
		$list->add_row(__C('user:info:friendship_since'),$buddystamp ? _date_get($buddystamp) : element_name('long_time'));
	}
	ob_start();
	require_once '_status.inc';
	$statusname = status_name($status = $user['STATUS']);
	switch ($status) {
	default:
		echo $statusname;
		break;

	case 'unactive':
	case 'inactive':
	case 'active':
		echo $statusname;
		if ($helpdesk_admin) {
			?> <span class="light">(<?
				if ($status === 'unactive'
				||	$status === 'inactive'
				) {
					?><a href="/user/<?= $userid ?>/setstatus?STATUS=active"><?= __('action:activate') ?></a>, <?
				}
				if ($status !== 'inactive') {
					?><a href="/user/<?= $userid ?>/setstatus?STATUS=inactive"><?= status_name('inactive') ?></a>, <?
				}
				?><a href="/user/<?= $userid ?>/setstatus?STATUS=deleted"><?= __('action:remove') ?></a>, <?
				?><a href="/user/<?= $userid ?>/deceasedinfo"><?= status_name('deceased') ?></a><?
			?>)</span><?
		}
		break;
	case 'banned':
		?><span class="banned"><?= $statusname; ?></span> <span class="light"><?
		if ($user['BAN_EXPIRES'] < CURRENTSTAMP) {
			?>(<?= __('status:expired') ?>)<?
		} else {
			?>(<?= __('attrib:till_datetime',['DATETIME'=>_datedaytime_get($user['BAN_EXPIRES'])]) ?>)<?
		}
		?></span><?
		break;
	case 'permbanned':
		?><span class="banned"><?= $statusname; ?></span><?
		break;
	case 'deleted':
		?><span class="unavailable"><?= $statusname ?></span><?

		if ($helpdesk_admin) {
			?> (<a href="/user/<?= $userid ?>/setstatus?STATUS=active"><?= __('action:reactivate') ?></a>)<?
		}
		break;
	case 'deceased':
		echo $statusname;
		if ($helpdesk_admin) {
			?> <span class="light">(<?
			?><a href="/user/<?= $userid ?>/deceasedinfo"><?= __('action:change_information') ?></a>, <?
			?><a href="/user/<?= $userid ?>/setstatus?STATUS=active"><?= __('action:reanimate') ?></a>)</span><?
		}
		break;
	}

	$list->add_row(Eelement_name('status'), ob_get_clean());

	if ($user['CSTAMP'] > CURRENTSTAMP - ONE_MONTH
	?	have_admin('helpdesk')
	:	have_super_admin()
	) {
		ob_start();
		?><form method="post" action="/user/<?= $userid ?>/eradicate"><?
		?><input<?
		?> type="submit"<?
		?> onclick="return confirm('Weet je zeker dat je alle uitlatingen van deze persoon wilt vernietigen?')"<?
		?> value="<?= __('action:eradicate') ?>"<?
		?>><?
		?></form><?
		$list->add_row(null,ob_get_clean());
	}

	// LAST_USED
	if ($user['STATUS'] !== 'deceased'
	||	have_admin(['helpdesk', 'user'])
	) {
		ob_start();
		?><b><?
		if ($user_data['LAST_USED']) {
			$thendaynum = to_days($user_data['LAST_USED']);
			switch (CURRENTDAYNUM - $thendaynum) {
			case 0:	echo __('date:today'); break;
			case 1:	echo __('date:yesterday'); break;
			case 2: echo __('date:daybeforeyesterday'); break;
			default:
				_date_display($user_data['LAST_USED']);
				break;
			}
			if (false !== ($visi = visibility_class($user, 'ONLINE', have_admin()))) {
				if ($visi === 'self'
				||	true // !have_super_privacy($userid)
				) {
					$light = $visi === ROW_ADMIN || $visi === ROW_SELF;
					if ($light) {
						?><span class="light"><?
					}
					if ($user_data['LAST_USED'] > TODAYSTAMP - ONE_DAY) {
						?> <?=  __('time:at') ?> <?
					} else {
						?> <?
					}
					_time_display($user_data['LAST_USED']);
					if ($light) {
						?></span><?
					}
				}
			}
		} else {
			?>-<?
		}
		?></b><?
		$list->add_row(__C('field:last_here'), ob_get_clean());
	}
	if ($user['MSTAMP']) {
		ob_start();
		[$luserid, $lstamp] = presence_last_modified();
		if ($lstamp > $user['MSTAMP']) {
			$user['MUSERID'] = $luserid;
			$user['MSTAMP']  = $lstamp;
		}
		_datedaytime_display($user['MSTAMP']);
		if ($user['MUSERID']) {
			if ($user['MUSERID'] !== $userid) {
				?> <?
				echo	have_admin(['user','helpdesk'])
				?	__('alteration:by_user', DO_UBB | DO_STARS, ['USERID' => $user['MUSERID']])
				:	__('alteration:by_an_admin');
			}
		} else {
			?> <?
			echo __('alteration:by_the_system');
		}
		$list->add_row(__C('field:last_modified'), ob_get_clean());
	}

/*	if ($self
	||	have_admin('helpdesk')
	) {
		require_once '_fbsession.inc';
		show_fb_status_rows($list, $userid);
	}*/

	$list->display();

	# show BIOGRAPHY
	require_once '_bio.inc';
	show_bio();

	layout_close_box();
	?></article><?

	if ($show_full_poll
	=	$pollid
	&&	(	$self
		||	setting_isset(SHOW_USER_POLLS)
		&&	(require_once '_pollhide.inc')
		&&	!is_hidden_poll($pollid)
		)
	) {
		require_once '_poll.inc';
		_poll_display_poll('user',$userid,$pollid,true,true);
	}

	global $__year, $__month, $__day, $__hour, $__mins;
	if ($__month === 2
	&&	(	$__day === 14
		||	$__day === 15
		&&	!$self
		||	$self
		&&	$__month === 2
		&&	$__day >= 14
		&&	$__day <= 14 + 14	# show for two weeks
		&&	0 + memcached_single('valentineseen', 'SELECT YEAR FROM valentineseen WHERE USERID = '.CURRENTUSERID, TEN_MINUTES) < $__year
		)
	) {
		require_once '_ignores.inc';
		$ignores = ignores(IGNORE_VALENTINE_HEARTS);
		$valentines = memcached_rowuse_array('valentine', '
			SELECT FROM_USERID, ANONYMOUS, BODY, ID
			FROM valentine
			WHERE YEAR = '.$__year.'
			  AND TO_USERID = '.$userid.'
			  AND BLOCKED = 0'.(
			$ignores ? ' AND FROM_USERID NOT IN ('.implodekeys(', ', $ignores).')' : '').'
			ORDER BY CSTAMP ASC',
			TEN_MINUTES
		);
		if ($valentines) {
			foreach ($valentines as $valentine) {
				if (!$valentine['ANONYMOUS']
				||	 $valentine['BODY']
				) {
					$havecontent = true;
					break;
				}
			}
			layout_open_box('white');
			if ($__day != 14) {
				?><div class="block"><b>Hartjesoogst valentijn <?= $__year; ?></b></div><?
			} elseif (isset($havecontent) && $self) {
				?><div class="block">Beweeg je muis over de duidelijke hartjes om te zien wie ze heeft gestuurd en te kijken of er een berichtje is meegestuurd of <?
				?>bekijk <i><a href="/user/<?= $userid;
				?>/valentine/<?= $__year ?>">het hele overzicht</a></i>!<br /><?
				?>Na valentijn kun je de hartjes automatisch allemaal op gezien zetten waarna ze van je profiel verdwijnen.</div><?
			}
			?><div class="block"><?
			require_once '_heart.inc';
			foreach ($valentines as $valentine) {
				$type = ($valentine['BODY'] ? 1 : 0) | ($valentine['ANONYMOUS'] ? 2 : 0);
				if ($showbubble
				=	$type != 2
				&&	(	have_super_admin()
					||	$self
					)
				) {
					require_once '_bubble.inc';
					$bubble = new bubble(BUBBLE_CLEAN | BUBBLE_BELOW | BUBBLE_CURSOR);
					$bubble->catcher();
				}
				switch ($type) {
				case 2: // no body, anonymous
					show_half_heart();
					break;
				case 0: // no body. not anonymous
				case 1: // body, not anonymous
				case 3: // body, anonymous
					show_heart();
					break;
				}
				if ($showbubble) {
					$bubble->content();
					if ($showbody
					=	$self
					&&	$valentine['BODY']
					) {
						?><table class="vtop" style="padding:0;margin:0"><tr><td><?
					}
					?><div style="margin:0 0 .3em 0"><?
					switch ($type) {
					case 0:
						?><a class="small" href="<?= get_element_href('user',$valentine['FROM_USERID'],$nick = memcached_nick($valentine['FROM_USERID']));
						?>"><?= escape_specials($nick);
						?></a><?
						break;

					case 1:
						echo make_all_html($valentine['BODY']);
						?></div><div class="heartsender"><a class="small" href="<?= get_element_href('user',$valentine['FROM_USERID'],$nick = memcached_nick($valentine['FROM_USERID']));
						?>"><?= escape_specials($nick);
						?></a><?
						break;
					case 3:
						echo make_all_html($valentine['BODY']);
						break;
					}
					?></div><?
					if ($showbody) {
						?></td><?
						?><td><? show_link_to_ticket_form('valentine',$valentine['ID']) ?></td><?
						?></tr></table><?
					}
					$bubble->display();
				}
				?> <?
			}
			?></div><?
			if ($__day != 14
			&&	$self
			) {
			}
			layout_close_box();
			if ($__day != 14
			&&	$self
			) {
				?><div class="funcs"><?
				?><a class="l" href="/user/<?= $userid ?>/valentine/<?= $__year ?>"><?= Eelement_name('overview') ?></a><?
				?><a href="/user/<?= CURRENTUSERID ?>/seenhearts"><?= __C('action:mark_seen') ?></a><?
				?></div><?
			}
		}
	}
	if (have_admin('helpdesk')
	&&	(	$_REQUEST['ACTION'] === 'secrets'
		||	$_REQUEST['ACTION'] === 'single'
		&&	setting('SHOW_USER_SECRETS')
		)
	) {
		show_user_secrets($user);
	}

	$deceased = $user['STATUS'] === 'deceased';
	$show_full_poll = $pollid && ($self || setting_isset(SHOW_USER_POLLS));
	# NOTE NOTE NOTE: remove this setting ^^^^ ?

	$pollid = 0;

	if ($total_shown
	=	($agenda_visible ? 1 : 0)
	+	($pollid ? 1 : 0)
	+	($show_stats ? 1 : 0)
	) {
		layout_open_box($user['COLOR'].' agenda','agendastats');
		if (!SMALL_SCREEN) {
			$show_full_poll = true;
			/*
			switch ($total_shown) {
			case 1:
			case 2:
				$show_full_poll = true;
				break;
			default:
				?><table class="fw vtop dens"><tr><?
				break;
			}*/
		}
		if ($agenda_visible) {
			$maybe_test = true;
			# $show_separate = $totals['FUTURE_TOTAL'] > $totals['FUTURE_INTERESTED'] && $totals['FUTURE_INTERESTED'] > 10;
			$show_separate = false;
			if (!$deceased) {
				require_once '_partylist.inc';
				$partylist = new _partylist;
				$partylist->user_going($userid);
				if ($_REQUEST['ACTION'] === 'single') {
					$partylist->show_vevent = true;
				}
				$partylist->show_lineups = $userid !== CURRENTUSERID;
				$partylist->show_buddy_hearts = true;
				$partylist->hide_buddy = $userid;
				$partylist->show_city =
				$partylist->show_location = true;
				$partylist->show_people = true;
				$partylist->order_chronologically();
				$partylist->select_future(null,true);
				if (CURRENTUSERID === $userid) {
					$partylist->include_all = true;
					if (setting('SHOW_STARS_PERSONAL_AGENDA')) {
						$partylist->show_stars = true;
					}
				} else {
					$partylist->show_stars = true;
				}
				$partylist->hide_separators = true;
				$partylist->show_date = true;
				$partylist->query();
			}
			require_once '_feed.inc';
			layout_open_box_header(NOWRAP);
			?><h2><?
			$future = $deceased ? 0 : $partylist->size();
			echo Eelement_name('agenda');
			?></h2><?

			$parts = [];
			if (!ROBOT) {
				$parts[] = get_ical_feed_link();
				if (SHOW_FEED_ICON) {
					ob_start();
					show_feed('agenda',FEED_ICON);
					$parts[] = ob_get_clean();
				}
			}
			if (!empty($totals['PASTPARTY'])) {
				[$linkopen, $linkclose] = get_action_open_close_for_setting('archive','SHOW_USER_ARCHIVE');
				$parts[] = $linkopen.element_name('archive').$linkclose;
			}
			if ($parts) {
				layout_continue_box_header();
				?><nav class="rmrgn"><?= implode(' '.MIDDLE_DOT_ENTITY.' ',$parts) ?></nav><?
			}
			layout_close_box_header();
			if ($future) {
				if ($maybe_test) {
					if ($show_separate) {
						$partylist->skip = 'maybe';
					}
				}
				$partylist->display();
			} else {
				require_once '_lastparty.inc';
				show_last_party();
			}
			if ($maybe_test) {
				if ($maybe_test) {
					if ($show_separate) {
						$partylist->skip = 'certain';
						?><div class="unhideanchor block" onclick="swapdisplay(this.nextSibling);"><i><?
							__('action:show_x_interesting_events', ['COUNT' => $totals['FUTURE_INTERESTED']])
						?><div class="hidden"><?
						$partylist->display();
						?></div><?
					}
				}
			}
			if ($_REQUEST['ACTION'] !== 'archive'
			&&	$totals['PASTPARTY']
			) {
				[$linkopen, $linkclose] = get_action_open_close('archive',null,element_name('agenda_archive').' '.escape_specials($user['NICK']));
				?><div class="light6 block" style="margin-top: 1em;"><i><?=
					$linkopen, __('action:show_archive'), ', ', $totals['PASTPARTY'], ' ', element_name('event',$totals['PASTPARTY']), $linkclose
				?></i></div><?
			}
		}
		if ($pollid) {
			if (!SMALL_SCREEN && $agenda_visible) {
				layout_continue_box();
			}
#			include_js('js/userpoll');
			layout_box_header(Eelement_name('poll'));
			require_once '_poll.inc';
			_poll_display_poll('user',$userid,$pollid,true);
			if (!isset($show_full_poll)) {
				error_log('no show_full_poll for '.CURRENTUSERID.' at '.$_SERVER['REQUEST_URI'],0);
			}
			if (!$show_full_poll) {
				?><table class="light header center ptr" onclick="hideobj(this.parentNode);unhide('fullpoll')"><tr><td><?
				echo 'P'//Eelement_name('poll(collapsed)')
				?></tr></td></table><?
			}
		}
		if ($show_stats) {
			layout_close_box();
			layout_open_box($user['COLOR']);

			layout_open_box_header();
			echo Eelement_plural_name('statistic');
			if ($helpdesk_admin) {
				layout_continue_box_header();
				?><small class="light">(<a href="/user/<?= $userid; ?>/refreshstats"><?= __('action:refresh'); ?></a>)</small><?
			}
			layout_close_box_header();
			user_display_stats($user, $user_data, $totals);
		}
		layout_close_box();
	}

	if (!empty($totals['BUDDY_CNT'])) {
		if ($_REQUEST['ACTION'] === 'buddies'
		||	$_REQUEST['ACTION'] === 'single'
		&&	setting('SHOW_USER_BUDDIES')
		) {
			show_user_buddies($user);
		}
/*		if ($_REQUEST['ACTION'] == 'buddymap'
		||	$_REQUEST['ACTION'] == 'buddycity'
		) {
			require_once '_usermap.inc';
			show_usermap_overview('user',$userid);
		}*/
	}
	if (!empty($favcnt)) {
		if ($_REQUEST['ACTION'] === 'favourites'
		||	$_REQUEST['ACTION'] === 'single'
		&&	setting('SHOW_USER_FAVOURITES')
		) {
			show_all_favourites($user);
		}
	}

	if ($show_stats) {
		if (!empty($totals['INACTIVE_BUDDY_CNT'])) {
			if ($_REQUEST['ACTION'] === 'inactivebuddies') {
				show_user_inactive_buddies($user);
			}
		}
/*		if (!empty($totals['REPORTCNT'])) {
			if ($_REQUEST['ACTION'] == 'reports'
			||	$_REQUEST['ACTION'] == 'single'
			&&	setting('SHOW_USER_REPORTS')
			) {
				show_user_reports($user);
			}
		}*/
		if (!empty($totals['SHOOTCNT'])) {
			if ($_REQUEST['ACTION'] === 'shoots'
			||	$_REQUEST['ACTION'] === 'single'
			&&	setting('SHOW_USER_SHOOTS')
			) {
				show_user_shoots($user);
			}
		}
		if (!empty($totals['VIDEOCNT'])) {
			if ($_REQUEST['ACTION'] === 'videoshoots') {
				show_user_videos($user);
			}
		}
		if (!empty($totals['COLUMNCNT'])) {
			if ($_REQUEST['ACTION'] === 'columns') {
				show_user_items('column', $user);
			}
		}
		if (!empty($totals['reviewCNT'])) {
			if ($_REQUEST['ACTION'] === 'reviews') {
				show_user_items('review', $user);
			}
		}
		if (!empty($totals['interviewCNT'])) {
			if ($_REQUEST['ACTION'] === 'interviews') {
				show_user_items('interview', $user);
			}
		}
		if (!empty($totals['FLOCKCNT'])) {
			if ($_REQUEST['ACTION'] === 'flocks') {
				show_user_flocks($user);
			}
		}
	}
	if ($agenda_visible) {
		if (!empty($totals['PASTPARTY'])) {
			if ((	$_REQUEST['ACTION'] === 'archive'
				||	$_REQUEST['ACTION'] === 'single'
				&&	setting('SHOW_USER_ARCHIVE')
				)
			) {
				show_user_archive($user);
			}
		}
		if (!empty($totals['PAST_INTERESTED'])) {
			if ((	$_REQUEST['ACTION'] === 'interestingarchive'
				||	$_REQUEST['ACTION'] === 'single'
				&&	setting('SHOW_USER_ARCHIVE')
				)
			) {
				show_user_archive($user, true);
			}
		}
	}
	require_once '_showcase.inc';
	show_presence_showcase(color: $user['COLOR']);

	// USER TEXT
	if ($_REQUEST['ACTION'] === 'single'
	||	$_REQUEST['ACTION'] === 'secrets'
	||	!strncmp($_REQUEST['ACTION'], 'text', 4)
	) {
		show_user_text($user, true, $_REQUEST['ACTION'] === 'secrets');
	}

	if (!empty($show_guestbook)) {
		require_once '_commentlist.inc';
		$cmts = new _commentlist;
		$cmts->item(array_merge($esettings, $user));
		$cmts->visibility = $guestbook_rflags;
		$cmts->force = true;
		if ($user['STATUS'] !== 'active'
		&&	$user['STATUS'] !== 'deceased'
		&&	(	$user['STATUS'] !== 'inactive'
			||	!(require_once '_notify.inc')
			||	!is_reachable($user, NOTIFY_GUESTBOOK)
			)
		) {
			$cmts->readonly();
		}
		$cmts->display();
	} elseif (
		$_REQUEST['ACTION'] === 'comments'
	||	!empty($_REQUEST['PAGE'])
	) {
		header('Location: https://'.$_SERVER['HTTP_HOST'].get_element_href('user',$userid));
		return;
	}

	if (!empty($show_condolences)) {
		require_once '_commentlist.inc';
		$_REQUEST['sELEMENT'] = 'deaduser';
		$cmts = new _commentlist;
		$cmts->force = true;
		$cmts->item($user);
		$cmts->display();
		$_REQUEST['sELEMENT'] = 'user';
	}
	if (CURRENTUSERID > 1
	&&	CURRENTUSERID !== $userid
	) {
		counthit('user',$userid);
	}
}

function user_display_profile_form() {
	require_once '_user_personals.inc';
	require_once '_bubble.inc';
	require_once '_presence.inc';
	require_once '_genrelist.inc';
	require_once '_citylist.inc';

	if (!($userid = $_REQUEST['sID'])) {
		if (!require_mailing()
		||	!require_creation_allowance()
		) {
			return;
		}
		layout_show_section_header(__C('action:register'));

		?><div class="block"><?= __('user:info:new_user_pre_form_LINE',DO_UBB) ?></div><?

	} elseif (!require_idnumber($_REQUEST,'sID')) {
		return;
	} else {
		layout_section_header(Eelement_name('user').' '.MIDDLE_DOT_ENTITY.' '.__($_REQUEST['sID'] ? 'action:change' : 'action:add'));
	}

	if ($userid) {
		if (!require_idnumber($_REQUEST,'sID')
		||		!have_admin('user')				// user can only change himselve except when a user admin
			&&	!have_self($_REQUEST['sID'])
		) {
			return;
		}
		if (false === ($user = db_single_assoc(['user', 'user_account'], '
			SELECT user.*, user_account.STATUS
			FROM user
			JOIN user_account USING (USERID)
			WHERE USERID = '.$userid,
			DB_USE_MASTER
		))) {
			return;
		}
		if (!$user) {
			not_found(); return;
		}
	} else {
		require_once '_fbsession.inc';
		show_fblogin_register();
		$user = null;
	}
	$spec = explain_table('user');
	if (!$spec) {
		return;
	}
	$maxuserid = db_single('user_account','SELECT MAX(USERID) FROM party_db.user_account',DB_USE_MASTER);
	if ($maxuserid === false) {
		return;
	}
	include_js('js/form/user');

	?><form<?
	?> accept-charset="utf-8"<?
	?> id="userform"<?
	?> method="post"<?
	?> onsubmit="return submitForm(this,submitUserForm)"<?

	?> action="/user/<? if ($user) { echo $user['USERID']; ?>/<? } ?>commit<? if (!$user) { ?>new<? } ?>"><?

	# bot honeypot:
	?><input style="display:none" name="SITES" /><?

	?><input type="hidden" name="FORMSTAMP" value="<?= CURRENTSTAMP; ?>" /><?
	if (!$user) {
		?><input type="hidden" name="LAST_USERID" value="<?= 0+$maxuserid; ?>" /><?
	}
	if ($user) {
		show_form_secret();
	}

	layout_open_box($user ? $user['COLOR'] : 'blue','main');

	layout_box_header(Eelement_name('profile_information'));

	#$hide_non_req = $user ? null : ' class="hidden"';
	$hide_non_req = false;

	$chatted = false;
	$limit_nick_changes =
		$userid
	&&	(require_once '_usernick.inc')
	?	limit_nick_changes($userid, $chatted)
	:	false;


	layout_open_table('fw');
	layout_start_row();
		?><label for="nick" class="required"><?= Eelement_name('nick'); ?></label><?
		layout_field_value();
		if ($limit_nick_changes) {
			show_nick_limit_options($limit_nick_changes,$user);
		} else {
			show_input([
				'pattern'	=> '^(?!(?:https?|ftp):/?/?).*',
				'title'		=> __('nick:info_LINE'),
				'spec'		=> $spec,
				'autofocus'	=> true,
				'disabled'	=> $limit_nick_changes === true,
				'required'	=> true,
				'type'		=> 'text',
				'id'		=> 'nick',
				'spellcheck'	=> 'false',
				'name'		=> 'NICK',
				'value'		=> $user,
				'onkeydown'	=> $chatted ? 'setdisplay(\'nicklimit\',true)' : false,
				'data-original'	=> $user ? $user['NICK'] : false,
			]);
		}
		layout_next_cell(class: 'small nowrap middle right');
		?><div class="center<?
		if ($hide_non_req) {
			?> hidden" id="visitxt<?
		}
		?>"><?
		echo element_name('visibility');
		?></div><?
	layout_stop_row();

	if ($limit_nick_changes || $chatted) {
		?><tr id="nicklimit"<?
		if (!$limit_nick_changes) {
			?> class="hidden"<?
		}
		?>><td class="invisible field"></td><td colspan="2"><?
		echo __('user:form:info:limit_nick_TEXT',DO_UBB | DO_CONDITIONAL);
		?></td></tr><?

	}
	if ($hide_non_req) {
		show_comprehensive_checkbox(true);
	}
	?><tbody id="profnon"<?= $hide_non_req ?>><?

	if ($hide_non_req) {
		?><tr><td colspan="3"><?
		?><hr class="slim"><?
		?></td></tr><?
	}

	layout_start_row();
		echo Eelement_name('name');
		layout_field_value();
		show_input([
			'size'		=> 40,
			'type'		=> 'text',
			'name'		=> 'NAME',
			'spellcheck'	=> false,
			'maxlength'	=> $spec['NAME']->maxlength,
			'value_utf8'	=> $user['NAME'] ?? null,
		]);
		layout_next_cell(class: 'right');
		_visibility_display_form_part($user,'NAME');

	layout_restart_row();
		echo Eelement_name('occupation');
		layout_field_value();
		show_input([
			'type'			=> 'text',
			'name'			=> 'OCCUPATION',
			'size'			=> 40,
			'maxlength'		=> min($spec['OCCUPATION']->maxlength, 23),
			'value_utf8'	=> $user['OCCUPATION'] ?? null,
		]);
		layout_next_cell(class: 'right');
		_visibility_display_form_part($user,'OCCUPATION');
	layout_restart_row();
		echo Eelement_name('birthdate');
		layout_field_value();
		?><span id="birthdate"><?
		if ($user) {
			$opts = db_rowuse_array(['user', 'user_log'],'
				(	SELECT DISTINCT BIRTH_YEAR, BIRTH_MONTH, BIRTH_DAY
					FROM user_log
					WHERE USERID = '.$_REQUEST['sID'].'
					  AND MSTAMP >= '.(CURRENTSTAMP - 60 * ONE_DAY).'
				) UNION (
					SELECT BIRTH_YEAR, BIRTH_MONTH, BIRTH_DAY
					FROM user
					WHERE USERID = '.$_REQUEST['sID'].'
				)
				ORDER BY BIRTH_YEAR, BIRTH_MONTH, BIRTH_DAY'
			);
			if ($opts
			&&	count($opts) >= 4
			) {
				$do29 = false;
				foreach ($opts as $opt) {
					if ($opt['BIRTH_DAY']   === 29
					&&	$opt['BIRTH_MONTH'] === 2
					) {
						$do29 = true;
						break;
					}
				}
				$donebd = true;
				?><div><?= __('user:form:info:change_birth_date_max_now_choose_TEXT',DO_NL2BR) ?></div><?
				$cnt = 0;
				foreach ($opts as $opt) {
					?><label<?
					if ($user['BIRTH_DAY']   === $opt['BIRTH_DAY']
					&&	$user['BIRTH_MONTH'] === $opt['BIRTH_MONTH']
					&&	$user['BIRTH_YEAR']  === $opt['BIRTH_YEAR']
					) {
						?> class="bold-hilited"><?
						?><input checked<?
					} else {
						?>><input class="not-bold-hilited"<?
					}
					?> class="upLite"<?
					?> onclick="<?
					if ($do29) {
						?>setdisplay('ldayr',<?= $opt['BIRTH_DAY'] === 29 && $opt['BIRTH_MONTH'] === 2 ? 'true' : 'false' ?>)<?
					}
					?>" type="radio"<?
					?> value="<?= $opt['BIRTH_YEAR'] ?>,<?= $opt['BIRTH_MONTH'] ?>,<?= $opt['BIRTH_DAY'] ?>"<?
					?> name="BIRTHDATE_OPTION"> <?
					if ($opt['BIRTH_DAY']) {
						echo $opt['BIRTH_DAY']; ?> <?
					}
					if ($opt['BIRTH_MONTH']) {
						echo _month_name($opt['BIRTH_MONTH']); ?> <?
					}
					if ($opt['BIRTH_YEAR']) {
						echo $opt['BIRTH_YEAR'];
					}
					?></label><br /><?
					++$cnt;
				}
			}
		}
		if (!isset($donebd)) {
			?><select autocomplete="bday-day" name="BIRTH_DAY" onchange="change_bday(this.form)"><option value="0"></option><?
			//$end = date("t",time());
			for ($cnt = 1; $cnt <= 31; $cnt++) {
				?><option value="<?= $cnt; ?>"<?
				if ($user && $cnt == $user['BIRTH_DAY']) {
					?> selected="selected"<?
				}
				?>><?= $cnt;
				?></option><?
			}
			?></select><select autocomplete="bday-month" name="BIRTH_MONTH" onchange="change_bday(this.form)"><option value="0"></option><?
				for ($cnt = 1; $cnt <= 12; $cnt++) {
				?><option value="<?= $cnt; ?>"<?
				if ($user && $cnt == $user['BIRTH_MONTH']) {
					?> selected="selected"<?
				}
				?>><?= _month_name($cnt); ?></option><?
			}
			global $__year;
			?></select><select autocomplete="bday-year" name="BIRTH_YEAR" onchange="change_bday(this.form)"><option value="0"></option><?
			for ($cnt = 1900; $cnt <= $__year - 5; $cnt++) {
				?><option value="<?= $cnt; ?>"<?
				if ($user && $cnt === $user['BIRTH_YEAR']) {
					?> selected<?
				}
				?>><?= $cnt;
				?></option><?
			}
			?></select><?
		}
		?></span><?
		layout_next_cell(class: 'right');
		_visibility_display_form_part($user,'BIRTHDAY');
	if (!isset($do29) || $do29) {
		layout_restart_row($user && $user['BIRTH_MONTH'] == 2 && $user['BIRTH_DAY'] == 29 ? 0 : ROW_HIDDEN,'ldayr');
			echo Eelement_name('birthday');
			layout_field_value();
			echo __('user:info:in_non_leap_years') ?>: <?
			?><select name="LDAY"><?
				?><option value="28">28 <?= _month_name(2) ?></option><?
				?><option value="1">1 <?= _month_name(3) ?></option><?
			?></select><?
			layout_next_cell();
	}
	layout_restart_row();
		$bubble = new bubble(BUBBLE_BLOCK | HELP_BUBBLE);
		$bubble->catcher_and_title(Eelement_name('age'));
		$bubble->content(__('user:info:age_information_LINE'));
		$bubble->display();
		layout_field_value();
		?><span class="light"><?= __('user:form:age_info_LINE') ?></span><?
		layout_next_cell(class: 'right');
		_visibility_display_form_part($user,'AGE');

	# COUNTRY and CITY
	layout_restart_row();
		echo Eelement_name('country');
		layout_field_value();
		display_dynamic_country_options($user['COUNTRYID'] ?? 0,INCLUDE_OTHER);
		layout_next_cell();

	$hidden_city = !$user || $user['CITYID'] || !$user['CITY'];
	$other_selected = !$user || (!$user['CITYID'] && $user['CITY']) ? OTHER_SELECTED : 0;

	layout_restart_row();
		echo Eelement_name('residence');
		layout_field_value();
		display_dynamic_city_options($user['CITYID'] ?? 0,INCLUDE_OTHER | $other_selected);

		?><span<?
		if ($hidden_city) {
			?> class="hidden"<?
		}
		?> id="cityfield"> <?
		show_input([
			'class'			=> 'regular',
			'size'			=> 40,
			'type'			=> 'text',
			'name'			=> 'CITY',
			'maxlength'		=> $spec['CITY']->maxlength,
			'value_utf8'	=> $user['CITY'] ?? '',
		]);

		?></span><?

	# visiblity CITY/COUNTRY
		layout_next_cell(class: 'right');
		_visibility_display_form_part($user, 'CITY');

	require_once 'defines/dating.inc';
	$need_gender = $user && ($user['DATING'] & DTNG_BODILY);

	layout_restart_row();
		echo Eelement_name('gender');
		layout_field_value();
		?><select<?
		if ($need_gender) {
			?> required<?
		}
		?> name="SEX" id="gender"><?
			?><option value=""></option><?
			?><option value="M"<? if ($user && $user['SEX'] === 'M') { ?> selected<? } ?>><?= __('gender:male') ?></option><?
			?><option value="F"<? if ($user && $user['SEX'] === 'F') { ?> selected<? } ?>><?= __('gender:female') ?></option><?
		?></select><?
		layout_next_cell(class: 'right');
		_visibility_display_form_part($user,'GENDER');
	layout_restart_row();
		echo Eelement_name('sexual_preference');
		layout_field_value();
		?><select<?
		if ($need_gender) {
			?> required<?
		}
		?> name="SEXPREF"><?
			?><option value=""></option><?
			?><option<? if ($user && $user['SEXPREF'] === 'homo') { ?> selected<? } ?> value="homo"><?=
				__((!$user || !$user['SEX'] || $user['SEX'] === 'M') ? 'sexpref:gay' : 'sexpref:lesbian')
			?></option><?
			?><option<? if ($user && $user['SEXPREF'] === 'hetero') { ?> selected<? } ?> value="hetero"><?= __('sexpref:straight') ?></option><?
			?><option<? if ($user && $user['SEXPREF'] === 'bi')	 { ?> selected<? } ?> value="bi"><?= __('sexpref:bisexual') ?></option><?
		?></select><?
		layout_next_cell(class: 'right');
		_visibility_display_form_part($user,'SEXPREF');
	layout_restart_row();
		echo Eelement_name('relation');
		layout_field_value();
		?><select name="RELATION"><?
			?><option value="NULL"></option><?
			?><option<? if ($user && $user['RELATION'] === 'no') { ?> selected<? } ?> value="no"><?= __('answer:no'); ?></option><?
			?><option<? if ($user && $user['RELATION'] === 'yes') { ?> selected<? } ?> value="yes"><?= __('answer:yes'); ?></option><?
		?></select><?
		layout_next_cell(class: 'right');
		_visibility_display_form_part($user,'RELATION');
	layout_restart_row();
		?><label for="dating"><?
		$bubble->cleanup();
		$bubble->catcher_and_title(Eelement_name('available'));
		$bubble->content(__('user:info:dating_information_TEXT',DO_NL2BR | DO_UBB));
		$bubble->display();
		?></label><?

		layout_field_value();
		require_once '_dating.inc';
		$dating = $userid ? $user['DATING'] : DTNG_SHOW_TO_ALL;

		show_dating_multi_select($dating,null,true);

		layout_next_cell();

		show_dating_visibility($dating);
	layout_stop_row();

	layout_start_rrow($need_gender ? 0 : ROW_HIDDEN,null,null,'gendersexprefinfo');
	layout_next_cell();
	echo __('dating:info:need_gender_and_sexpref_LINE');

	layout_stop_row();


	layout_start_row();
		echo Eelement_plural_name('favourite_genre');
		layout_field_value();
		#_genrelist_display_checkboxes($user ? user_favourite_genres($user['USERID']) : []);
		if (!show_user_genre_options($userid)) {
			return;
		}
		layout_next_cell(class: 'right');
		_visibility_display_form_part($user,'FAVMUSIC');

	layout_restart_row();
		$bubble->cleanup();
		$bubble->catcher(Eelement_name('favourite_site','?'));
		$bubble->content('Je kunt hier sites opgeven door ze met een spatie of enter te scheiden.');
		$bubble->display();
		layout_field_value();

		if ($user
		&&	$user['SITE']
		&&	$user['SITE'][strlen($user['SITE']) - 1] !== "\n"
		) {
			$user['SITE'] .= "\n";
		}
		show_textarea([
			'cols'		=> 80,
			'class'		=> 'growToFit',
			'data-max'	=> 10,
			'name'		=> 'SITE',
			'spec'		=> $spec,
			'value'		=> $user
		]);
		layout_next_cell(class: 'right');
		_visibility_display_form_part($user,'SITE');
	if (!show_presence_form_row($user)) {
		return;
	}
	layout_restart_row();
		echo Eelement_name('profile_color');
		layout_field_value();
		?><select onchange="<?
		?>getobj('main').className=this.value+' box';<?
		?>getobj('personal').className=this.value+' box';<?
		?>" name="COLOR"><? color_display_options($user ? $user['COLOR'] : 'blue');
		?></select><?
		layout_next_cell();

	# ORG and LOC too?
	if ($user
	&&	($members = db_read(
			array('artist','artistmember'),'
			SELECT "artist" AS _ELEMENT,ID AS _ID,VISIBILITY_NAME
			FROM artistmember
			WHERE USERID='.$userid.'
			UNION
			SELECT "location" AS _ELEMENT,ID AS _ID,VISIBILITY_NAME
			FROM locationmember
			WHERE USERID='.$userid.'
			UNION
			SELECT "organization" AS _ELEMENT,ID AS _ID,VISIBILITY_NAME
			FROM organizationmember
			WHERE USERID='.$userid,
			ROW_USE_MULTI_HASH
		))
	) {
		foreach ($members as $element => &$list) {
			foreach ($list as $id => &$item) {
				$item['NAME'] = get_element_title($element, $id);
			}
			unset($item);
			string_asort($list, 'NAME');
		}
		unset($list);
		foreach ($members as $element => $list) {
			$is_admin = have_admin($element);
			foreach ($list as $id => $member) {
				extract($member, \EXTR_OVERWRITE);
				if (!$is_admin
				&&	$member['VISIBILITY_NAME'] === NOBODY
				) {
					continue;
				}
				layout_restart_row();
				echo Eelement_name($element);
				layout_field_value();
				echo get_element_link($element, $id, $NAME);
				layout_next_cell(class: 'right');
				_visibility_display_form_part($member, 'NAME','_'.$element.'['.$id.']');
			}
		}
	}

	layout_stop_row();

	?></tbody><?

	layout_close_table();

	layout_close_box();

	show_personal_information_form($spec,$user);

	?><div class="block"><?
	if (!$user) {
		require_once '_recaptcha.inc';
		show_recaptcha_button('new_user', __('action:register'), 'captchSubmitUserForm');
	} else {
		?><input type="submit" value="<?= __($user ? 'action:change' : 'action:register') ?>" /><?
	}
	?></div><?
	?></form><?
}

function user_display_email_form() {
	if (!require_admin(['helpdesk','user'])) {
		return;
	}
	layout_show_section_header(__C('action:change_email'));

	if (!($userid = require_idnumber($_REQUEST,'USERID'))) {
		return;
	}
	if (($helpdesksuper_admin = have_admin('helpdesksuper'))
	&&	!($user = db_single_assoc('user', 'SELECT EMAIL, REALNAME, ADDRESS, ZIPCODE, PHONE FROM user WHERE USERID = '.$userid))
	) {
		if ($user !== false) {
			not_found();
		}
		return;
	}
	$super = have_admin(['helpdesksuper','user']);

	?><form method="post" action="/user/<?= $userid ?>/changemail" onsubmit="return submitForm(this)"><?
	layout_open_box('white');
	layout_box_header(get_element_link('user',$userid));
	layout_open_table(TABLE_CLEAN);
	layout_start_row();
	if ($helpdesksuper_admin) {
		if ($user['REALNAME']) {
			echo Eelement_name('first_and_surname');
			layout_field_value();
			echo escape_specials($user['REALNAME']);
			layout_restart_row();
		}
		if ($user['ADDRESS']) {
			echo Eelement_name('address');
			layout_field_value();
			echo escape_specials($user['ADDRESS']);
			layout_restart_row();
		}
		if ($user['ZIPCODE']) {
			echo Eelement_name('zipcode');
			layout_field_value();
			echo escape_specials($user['ZIPCODE']);
			layout_restart_row();
		}
		if ($super) {
			if ($user['PHONE']) {
				echo Eelement_name('phone');
				layout_field_value();
				echo escape_specials($user['PHONE']);
				layout_restart_row();
			}
		}
		echo Eelement_name('current_email_address');
		layout_field_value();
		echo escape_specials($user['EMAIL']);
		layout_restart_row();
	}
	echo Eelement_name('new_email_address');
	layout_field_value();
	?><input type="email" valid="email" required name="EMAIL" class="credential" autocomplete="email" /><?
	layout_stop_row();
	layout_close_table();

	layout_close_box();
	?><div class="block"><input type="submit" value="<?= __('action:change') ?>" /></div><?

	if ($helpdesksuper_admin) {
		require_once 'include.php';
		include_something([
			'INCLUDE'	=> 'olduserinfo',
			'USERID'	=> $userid,
			'NOHR'		=> true,
		]);
	}
}
function user_change_email(): bool {
	require_once '_mail.inc';
	if (!require_admin(['helpdesk','user'])
	||	!($userid = require_idnumber($_REQUEST,'sID'))
	||	!($email = require_working_email($_POST, 'EMAIL'))
	) {
		return false;
	}
	if (false === ($user = db_single_assoc('user','SELECT EMAIL,NICK FROM user WHERE USERID='.$userid,DB_USE_MASTER))) {
		return false;
	}
	if (!$user) {
		not_found('user', $userid);
		return false;
	}
	if (!db_insert('user_log','
		INSERT INTO user_log
		SELECT * FROM user
		WHERE EMAIL!="'.($smail = addslashes($email)).'"
		  AND USERID='.$userid)
	||	!db_update('user','
		UPDATE user SET	
			MUSERID	='.CURRENTUSERID.',
			MSTAMP	='.CURRENTSTAMP.',
			EMAIL	="'.$smail.'"
		WHERE EMAIL!="'.$smail.'"
		  AND USERID='.$userid)
	) {
		return false;
	}
	if (!db_affected()) {
		register_warning('generic:notice:nothing_changed_LINE');
		return true;
	}
	register_notice('user:notice:email_changed_LINE');
	mail_old_email($user['EMAIL'],$user['NICK'],$userid);
	return true;
}

function user_display_flocktopics() {
	if (!($userid = require_idnumber($_REQUEST, 'sID'))) {
		not_found();
		return;
	}
	if (!require_visible_esetting($userid,'STATISTICS', have_admin(['helpdesk',  'flocktopic']))) {
		no_permission();
		return;
	}
	if (!($nick = memcached_nick($userid))) {
		not_found('user', $userid);
		return;
	}
	layout_open_section_header();
	?>Flockonderwerpen door <?= get_element_link('user', $userid, $nick); ?> geopend<?
	layout_close_section_header();

	require_once '_flocktopiclist.inc';
	$topiclist = new flocktopiclist();
	$topiclist->opened_by($_REQUEST['sID']);
	$topiclist->query();
	$topiclist->display();
}
function user_display_opened_topics() {
	if (!($userid = require_existing_user($_REQUEST,'sID'))) {
		http_status(404);
		return;
	}
	require_once '_offense_access.inc';
	if (!require_visible_esetting($userid,'STATISTICS',have_admin('helpdesk') || have_offense_admin())) {
		http_status(403);
		return;
	}

	layout_show_section_header(__('topic:info:opened_by',DO_UBB,array('USERID'=>$userid)));

	require_once '_topiclist.inc';
	$topiclist = new _topiclist;
	$topiclist->show_diff_self = false;
	$topiclist->show_diff = true;
	$topiclist->show_last = true;
	$topiclist->show_cstamp = true;
	$topiclist->opened_by_user($_REQUEST['sID']);
	$topiclist->order_reverse_lstamp();
	$topiclist->show_author = false;
	$topiclist->show_header = true;
	$topiclist->show_short = true;
	$topiclist->show_groups = false;
	if (!$topiclist->query()) return;


	$topiclist->display();
}
function user_display_photos() {
	if (!require_idnumber($_REQUEST,'sID')) {
		return;
	}
	$userid = $_REQUEST['sID'];
	$user = memcached_user($userid);
	if ($user === false) {
		return;
	}
	if (!$user) {
		not_found(); return;
	}
	layout_open_section_header();
	?>Foto's van <? echo get_element_link('user',$userid,$user['NICK']);
	layout_close_section_header();

	if (invisible_profile($userid,'user_appearance')
	||	($esettings = external_settings_list($userid))
	&&	!_visibility($esettings,'APPEARANCES')
	) {
		return;
	}
	require_once '_photolist.inc';
	$photolist = new _photolist;
	$photolist->of_user($userid);
	$photolist->show_all = true;
	$photolist->show_controls = false;
	$photolist->align_left = true;
	if ($photolist->query()) {
		$photolist->display();
	}
}
function user_display_forgot($forgot) {
	layout_show_section_header($forgot ? __C('action:reset_password') : Eelement_name('new_activation_message'));

	?><form method="post" onsubmit="return submitForm(this)" action="/user/<?= $forgot ? 'sendpasswdtoken' : 'sendactivationtoken' ?>"><?
	layout_open_box('white');
	?><div class="block"><?= __($forgot ? 'user:info:password_reset_TEXT' : 'user:info:resend_token_TEXT',DO_UBB | DO_NL2BR) ?></div><?
	?><div class="block"><?
	?><input required="required" autofocus="autofocus" type="search" autosave="nick_name_email" id="term" name="TERM"<?
	if (!empty($_REQUEST['TERM'])) {
		?> value="<?= escape_specials($_REQUEST['TERM']); ?>"<?
	}
	?>><?
	?></div><?
	layout_close_box();
	?><div class="block"><input type="submit" value="<?= __('action:search') ?>" /></div></form><?
}

function user_display_token_form(bool $passwdtoken): void {
	layout_show_section_header($passwdtoken ? __C('action:reset_password') : Eelement_name('new_activation_message'));

	$delay = QUARTER;
	$userids = [];
	$mail_userids = [];
	if ($userid = $_REQUEST['sID']) {
		if (!($user = memcached_user($userid))) {
			if ($user !== false) {
				not_found('user', $userid);
			}
			return;
		}
		if (in_array($user['STATUS'], $passwdtoken ? ['active', 'unactive', 'inactive', 'banned'] : ['unactive'], true)) {
			$userids = [$userid => $userid];
		}
	} elseif (require_something_trim($_POST, 'TERM')) {
		$filter = $passwdtoken ? 'STATUS IN ("active","unactive","inactive","banned")' : 'STATUS IN ("unactive")';
		$term = $_POST['TERM'];
		$term_slashed = addslashes($term);
		if (false === ($userids = db_same_hash(['user_account','user'], "
			SELECT USERID
			FROM user_account
			JOIN user USING (USERID,NICK)
			WHERE EMAIL != ''
			  AND $filter
			  AND NICK = '$term_slashed'",
			DB_USE_MASTER))
		) {
			return;
		}
		if (is_number($term)) {
			$userid = (int)$term;
			if (false === ($user = memcached_user($userid))) {
				return;
			}
			if ($user && in_array($user['STATUS'],$passwdtoken ? ['active', 'unactive', 'inactive', 'banned'] : ['unactive'], true)) {
				$userids[$userid] = $userid;
			}
		} elseif (
			str_contains($term, '@')
		&&	false === ($mail_userids = db_same_hash(['user','user_account'], "
			SELECT SQL_NO_CACHE USERID
			FROM user_account
			JOIN user USING (USERID)
			WHERE $filter
			  AND EMAIL = '$term_slashed'",
			DB_USE_MASTER))
		) {
			return;
		}
	} else {
		return;
	}
	if (!$userids && !$mail_userids) {
		register_warning(
			$passwdtoken
		?	'user:warning:reset:no_users_with_nick/email/userid_TEXT'
		:	'user:warning:resend:no_users_with_nick/email/userid_TEXT', DO_UBB, ['TERM' => $term]);
		return;
	}
	$alluserids = [...$userids, ...$mail_userids];
	if (false === $lasthere = db_simple_hash('user_data','SELECT USERID,LAST_USED FROM user_data WHERE USERID IN ('.implodekeys(',',$alluserids).')')) {
		return;
	}
	$mailmaxseenstamp = 0;
	$mailmaxcstamp = 0;
	foreach ([
		'userlist'		=> $userids,
		'mailuserlist'	=> $mail_userids
	] as $listname		=> $ids
	) {
		if (!$ids) {
			$$listname = null;
			continue;
		}
		if (!($$listname = db_rowuse_hash(['user', 'user_account', 'passwd_mailed', 'emailcheckseen'],'
			SELECT	ua.USERID, ua.NICK, STATUS,
				user.EMAIL, user.CSTAMP, user.NAME, VISIBILITY_NAME,
				passwd_mailed.STAMP AS MAILED_STAMP,
				emailcheckseen.SEENSTAMP
			FROM user_account AS ua
			JOIN user USING (USERID)
			LEFT JOIN passwd_mailed USING (USERID)
			LEFT JOIN emailcheckseen USING (USERID)
			WHERE EMAIL != ""
			  AND ua.USERID IN ('.implodekeys(', ', $ids).')',
			DB_USE_MASTER))
		) {
			if ($$listname === false) {
				return;
			}
			continue;
		}
		uasort($$listname,static function(array $a, array $b) use ($lasthere): int {
			static $__sw = [
				'unactive'	=> 0,
				'active'	=> 1,
				'inactive'	=> 2,
				'banned'	=> 3,
				'deleted'	=> 4,
			];
			return	$__sw[$a['STATUS']]			   <=> $__sw[$b['STATUS']]
				?:	($lasthere[$b['USERID']] ?? 0) <=> ($lasthere[$a['USERID']] ?? 0);
		});
		if ($listname !== 'mailuserlist') {
			continue;
		}
		$container = null;
		foreach ($mailuserlist as $userid => &$user) {
			$mailmaxseenstamp = max($mailmaxseenstamp,$user['SEENSTAMP']);
			$mailmaxcstamp	  = max($mailmaxcstamp,   $user['CSTAMP']);
			if (!$container) {
				$container = $user;
				$container['CONTAINER'] = $mailuserlist;
			} else {
				$user['SKIP'] = true;
			}
		}
		unset($user);
		$mailuserlist = [$container['USERID'] => $container];
	}

	if ($userlist
	&&	$mailuserlist
	) {
		[$firstuserid/*, $firstuser */] = keyval($userlist);
		[$firstmailid/*, $firstmail */] = keyval($mailuserlist);
		$alluserlist =
			($lasthere[$firstuserid] > $lasthere[$firstmailid])
		?	$userlist	 + $mailuserlist
		:	$mailuserlist + $userlist;
	} else {
		$alluserlist = $userlist ?: $mailuserlist;
	}

	require_once '_profileimage.inc';
	require_once '_visibility.inc';

	if (empty($alluserlist)) {
		mail_log('empty alluserlist');
	}

	require_once '_password.inc';
	$create_request_token = static function(int $userid): string|false {
		return	db_insert('tmptoken','
	 		INSERT INTO tmptoken SET
				TOKENHASH	="'.addslashes($token = create_password(32,null,'#$%&*()=+/.\\[]{}":_-')).'",
				ACTION		="request_for_email",
				USERID		='.$userid.',
				EXPIRES		='.(CURRENTSTAMP + 3600))
		?	db_insert_id().'.'.base64_encode($token)
		:	false;
	};

	unset($user);

	if (!isset($_POST['TOKEN'])) {
		foreach ($alluserlist as $userid => &$user) {
			if (isset($user['SKIP'])) {
				continue;
			}
			/** @noinspection PhpRedundantOptionalArgumentInspection */
			extract($user, \EXTR_OVERWRITE);
			$frommail =
				isset($mail_userids[$userid])
			&&	!isset($userids[$userid]);

			$email = strtolower($EMAIL);
			$seenstamp = $frommail && isset($mailmaxseenstamp) ? max($mailmaxseenstamp,$mailmaxcstamp) : max($SEENSTAMP,$CSTAMP);
			$failures = (
				$allfailures[$email][$seenstamp] ??= db_simple_hash('contact_undeliverable', "
				SELECT CSTAMP,FAILREASON
				FROM contact_undeliverable
				WHERE FAILMAIL = '".addslashes($email)."'
				  AND CSTAMP > $seenstamp
				ORDER BY CIMID DESC")
			);
			$stati = [];
			$lasts = [];
			if (!$frommail) {
				$CONTAINER = [$userid => $user];
			}
			$any_allow = false;
			foreach ($CONTAINER as $tmpuserid => &$tmpuser) {
				if (!isset($stati[$status = $tmpuser['STATUS']])) {
					$stati[$status] = __('status:'.$status);
				}
				if ($last_here = $lasthere[$tmpuserid] ?? null) {
					$thendaynum = to_days($last_here);
					$str = match(CURRENTDAYNUM - $thendaynum) {
						0		=> __('date:today'),
						1		=> __('date:yesterday'),
						2		=> __('date:daybeforeyesterday'),
						default	=> _date_get($last_here),
					};
					$lasts[$last_here] = $tmpuser['LASTHERE'] = $str;
				} else {
					$tmpuser['LASTHERE'] = __('field:unknown');
				}
				$tmpuser['ALLOW'] = $allow = true;//$tmpuser['MAILED_STAMP'] < CURRENTSTAMP - $delay;
				$any_allow = $any_allow || $allow;
			}
			unset($tmpuser);
			if ($frommail) {
				ksort($stati);
				krsort($lasts);
			}
			layout_open_box('white');

			$title =
				$frommail
			?	escape_specials($EMAIL)
			:	get_element_link('user', $userid, $NICK);

			# $title might be null or empty if user is 'invisible' (privacy, banned)

			layout_box_header($title ?? '');

			$img = $frommail ? null : _profileimage_get($user);

			?><div class="center l" style="width:110px;margin-right:1em"><?= $img ?: '<span style="line-height:100px;font-size:100px">?</span>' ?></div><?
			?><div><?
				layout_open_table(TABLE_VTTOP);
				layout_start_row();
				if (!$frommail) {
					echo Eelement_name('id');
					layout_field_value();
					echo $USERID;
					layout_restart_row();
				}
				echo Eelement_name('status');
				layout_field_value();
				echo implode(', ',$stati);

				if (!$frommail
				&&	$user['NAME']
				&&	_visibility($user, 'NAME')
				) {
					layout_restart_row();
					echo Eelement_name('name');
					layout_field_value();
					echo escape_utf8($user['NAME']);
				}

				if ($lasts) {
					layout_restart_row(NOWRAP);
					echo __C('field:last_here');
					layout_field_value();
					echo implode('<br />',$lasts);
				}

				layout_close_table();

			?></div><?
			?><div class="clear<?
			if ($failures) {
				?> hr<?
			}
			?>"></div><?
			if ($failures) {
				?><div class="block"><span class="warning"><?= Eelement_plural_name('mail_problem') ?></span><br /><?
				?>Er zijn in het verleden fouten opgetreden bij het versturen van mail naar het opgegeven adres.<br /><?
				?>Hieronder volgt een overzicht. Zorg er voor dat je deze fouten verhelpt, anders zal het wachtwoord niet aankomen!</div><?

				$repped = false;
				foreach ($failures as $cstamp => &$failure) {
					$failure = preg_replace_callback(
							"'(?<=^|[\s\r\n\t\.\,\;\:\"\'\!\?<>])([a-z0-9]+([_.-]*[a-z0-9]+)*[_]?@([a-z0-9]+([.-][a-z0-9]+)*)+\\.[a-z]{2,4})'i",function($match) use ($frommail,$EMAIL) {
								return $frommail ? (strcasecmp($match[0],$EMAIL) ? '<EMAIL>' : $match[0]) : '<EMAIL>';
							},$failure,-1,$count
					);
					if ($count) {
						$repped = true;
					}
				}
				if (!$frommail && $repped) {
					?><p>Om de privacy te beschermen, is het oorspronkelijk email in de foutmelding vervangen door <span class="underline"><EMAIL></span>.</p><?
				}
				layout_open_table();//TABLE_VTOP);
				foreach ($failures as $cstamp => $failure) {
					layout_start_rrow(0,null,'nowrap rpad right');
					?><b><? _date_printnice(PRINTNICE_DATE_TIME,$cstamp) ?></b><?
					layout_next_cell();
					echo escape_specials($failure);
					layout_stop_row();
				}
				layout_close_table();
			}
			layout_close_box();
			$cnt = count($CONTAINER);
			if ($any_allow && $cnt > 1) {
				?><div class="block"><?= __('user:info:multiple_matches_for_email_LINE',array('CNT'=>$cnt)) ?></div><?
			}
			if (count($stati) === 1 && isset($stati['deleted'])) {
				continue;
			}
			?><div class="block"><?
			if ($any_allow) {
				?><form onsubmit="return <?
				if ($failures) {
					?>confirm('<?= __('user:confirm:mail_problems_you_sure_LINE') ?>')&&<?
				}
				?>submitForm(this)"<?
				?> id="usermailform"<?
				?> method="post"<?
				?> action="/user/<?
				if ($_REQUEST['sID']) {
					echo $_REQUEST['sID'] ?>/<?
				}
				?>send<?= $passwdtoken ? 'passwd' : 'activation' ?>token"><?

				$ok = true;
				if (!$_REQUEST['sID']) {
					?><input type="hidden" name="TERM" value="<?= escape_specials($term) ?>" /><?

					if (!$frommail
					||	$cnt === 1
					) {
						$tokenstr = $create_request_token($userid);
						if (!$tokenstr) {
							$ok = false;
						}
						?><input type="hidden" name="TOKEN" value="<?= $tokenstr ?>" /><?
					} else {
						ob_start();
						foreach ($CONTAINER as $inner_userid => $inner_user) {
							if ($inner_user['ALLOW']) {
								$tokenstr = $create_request_token($inner_userid);
								if (!$tokenstr) {
									$ok = false;
								} else {
									$tokenstrs[] = $tokenstr;
								}
								?><option value="<?= $tokenstr ?>"><?
							} else {
								?><option disabled><?
							}
							echo __('field:last_here') ?>: <?= $inner_user['LASTHERE'] ?>, <?= __('status:'.$inner_user['STATUS']) ?></option><?
						}
						$opts = ob_get_clean();

						?><select name="TOKEN"><?
						?><option value="<?= implode(',',$tokenstrs) ?>"><?=
							element_name('all(profiles)')
						?></option><?=
							$opts;
						?></select> <?
					}
				} else {
					$tokenstr = $create_request_token($userid);
					if (!$tokenstr) {
						$ok = false;
					}
					?><input type="hidden" name="TOKEN" value="<?= $tokenstr ?>" /><?
				}
				if ($ok) {
					require_once '_recaptcha.inc';
					include_js('js/form/usermail');
					show_recaptcha_button(
						'reset_password_mail',
						__($frommail ? 'action:request_for_this_email' : 'action:request_for_this_profile').($cnt === 1 ? ' &uarr;' : ''),
						'submitUserMailForm'
					);
				}
				?></form><?
			} else {
				$delay_mins = $delay / 60;

				$mins = ($delay_mins - (int)((CURRENTSTAMP - $MAILED_STAMP)/$delay_mins)) ?: 0;
				echo __('user:info:only_one_request_per_hour_TEXT',DO_NL2BR,array('MINUTES'=>$mins));
			}
			?></div><?
		}
	}
	unset($user);
	if (!isset($_POST['TOKEN'])) {
		return;
	}
	require_once '_token.inc';
	$mailusers = [];
	foreach (explode(',', $_POST['TOKEN']) as $token) {
		if (!preg_match('"^(\d+)\.(.*)$"',$token,$match)) {
			invalid_token('request_for_email','invalid',$token);
			continue;
		}
		[, $tokenid, $token] = $match;

		$dtoken = base64_decode($token);

		if (false === ($info = db_single_array('tmptoken','
			SELECT EXPIRES,USERID,USTAMP
			FROM tmptoken
			WHERE ACTION = "request_for_email"
			  AND TOKENHASH = "'.addslashes($dtoken).'"
			  AND TOKENID = '.$tokenid,
			DB_USE_MASTER))
		) {
			continue;
		}
		if (!$info) {
			invalid_token('request_for_email','nonexistent',$tokenid);
			continue;
		}
		[$expires, $userid, $ustamp] = $info;
		if ($ustamp) {
			register_warning('user:warning:token_mails_already_sent_LINE');
			continue;
		}
		if (CURRENTSTAMP > $expires) {
			invalid_token('request_for_email', 'expired', $tokenid);
		}
		if (!($user = db_single_assoc(['user', 'user_account'],'
			SELECT	USERID, user.NICK, NAME, REALNAME, EMAIL,
					STATUS
			FROM user
			JOIN user_account USING (USERID)
			WHERE USERID='.$userid))
		) {
			error_log('ERROR request_for_email token user '.$userid.' does not exist: '.$tokenid);
			continue;
		}
		$user['TOKENID'] = $tokenid;
		$mailusers[$userid] = $user;
	}

	require_once '_recaptcha.inc';
	if (!validate_recaptcha('reset_password_mail')) {
		return;
	}
	require_once '_mail.inc';
	require_once '_token.inc';
	require_once '_password.inc';
	require_once 'defines/nomail.inc';
	$totalrc = true;
	$cnt = 0;
	foreach ($mailusers as $userid => $user) {
		$passwdtoken = $user['STATUS'] !== 'unactive' && $passwdtoken;
		$frommail = isset($mail_userids[$userid]) && !isset($userids[$userid]);
		$token = generate_token($userid,$passwdtoken ? 'reset_password' : 'activate_account');
		if (!$token) {
			return;
		}
		$mail = $passwdtoken ? mail_password_token(...) : mail_activation_token(...);
		$rc = no_mail($user['EMAIL']) ? true : $mail($user['EMAIL'], $user['NICK'], $token, $userid);
		if (!$rc) {
			error_log_r($user,'failed to mail');
			$totalrc = false;
			continue;
		}
		++$cnt;
		db_replace('passwd_mailed','
		REPLACE INTO passwd_mailed SET
			USERID	= '.$userid.',
			STAMP	= '.CURRENTSTAMP.',
			IPBIN	= "'.addslashes(CURRENTIPBIN).'",
			IDENTID	= '.CURRENTIDENTID
		);
		db_update('tmptoken','
		UPDATE tmptoken SET USTAMP = '.CURRENTSTAMP.'
		WHERE TOKENID = '.$tokenid
		);
	}
	if (!$totalrc || !$cnt) {
		layout_open_box('red');
		?><div class="block"><?= __('user:info:failed_to_mail_for_profiles_LINE',DO_UBB,['TOTAL' => count($mailusers)]) ?></div><?
		layout_close_box();
	}
	if (!$cnt) {
		return;
	}
	layout_open_box('white');
	if (null === ($user_link = get_element_link('user', $userid, $user['NICK']))) {
		# invisible_profile true, then user_link null
		$user_link = '';
	}
	layout_box_header($cnt > 1 || $frommail ? _make_illegible($term) : $user_link);
	?><div class="block"><?=
		__($passwdtoken ? 'user:info:password_reset_message_sent_LINE' : 'user:info:activation_message_sent_LINE', DO_UBB, ['TOTAL' => count($mailusers)])
	?></div><?
	?><div class="block"><?= __('user:login:contact_helpdesk_LINE', DO_UBB) ?></div><?
	layout_close_box();
}

function user_personal_ok(?array $existing_user = null, ?bool $facebook = false): bool {
	if ($facebook
	&&	empty($_POST['EMAIL'])
	) {
		register_error('require:email:need_email_fb_LINE');
		return false;
	}
	return	(	require_something_trim($_POST, 'EMAIL')
			&&	(	$existing_user
				?	(	!strcasecmp($existing_user['EMAIL'], $_POST['EMAIL'])
					||	require_working_user_email($existing_user['USERID'], $_POST['EMAIL'])
					)
				:	require_working_email($_POST, 'EMAIL'))
			)
			&&	require_anything_trim($_POST, 'REALNAME')
			&&	require_anything_trim($_POST, 'ADDRESS')
			&&	require_anything_trim($_POST, 'ZIPCODE')
			&&	require_phone_or_empty($_POST, 'PHONE');
}

function user_profile_commit($facebook = false): bool {
	convert_array_to_win1252($_POST, keep_utf8: USER_UTF8_FIELDS);

	if (!empty($_POST['SITES'])) {
		# bot honeypot
		return false;
	}

	require_once '_colors.inc';
	require_once '_nick.inc';
	require_once '_presence.inc';
	require_once '_phone.inc';

	if (!$facebook
	&&	!require_post()
	||	!optional_number($_REQUEST, 'sID')
	||	(	!isset($_POST['BIRTHDATE_OPTION'])
		&&	(	false === require_number($_POST, 'BIRTH_YEAR')
			||	false === require_number($_POST, 'BIRTH_MONTH')
			||	false === require_number($_POST, 'BIRTH_DAY')
			)
		)
	||	!require_anything_trim($_POST, 'CITY', true)
	||	!require_anything_trim($_POST, 'NAME', true)
	||	!require_anything_trim($_POST, 'SITE')
	||	!require_anything_trim($_POST, 'OCCUPATION', true)
	||	!require_something_trim($_POST, 'NICK')
	||	!require_color($_POST, 'COLOR')
	||	!optional_number_array($_POST, 'GID')
	||	!optional_number($_POST, 'COUNTRYID')
	||	!optional_number($_POST, 'LDAY')
	) {
		if (!empty($_POST)
		&&	(	empty($_POST['PRESENCE'])
			#	failed form input and more than 30 links in PRESENCE is probably a bot
			||	substr_count($_POST['PRESENCE'], 'http') < 30
			)
		) {
			ob_start();
			echo "user_commit parameter fail.\n\n";
			foreach (['__error', '__warning', '__notice'] as $message_type) {
				global $$message_type;
				if (!empty($$message_type)) {
					echo "$message_type: \n", implode("\n", $$message_type), "\n\n";
				}
			}
			echo '_REQUEST: ';	print_r($_REQUEST);
			echo '_POST: ';		print_r($_POST);
			mail_log(ob_get_clean());
		}
		return false;
	}
	if (!($userid = $_REQUEST['sID'])) {
		if (!require_creation_allowance()) {
			return false;
		}
		if (!$facebook) {
			# check recaptcha
			require_once '_recaptcha.inc';
			if (!validate_recaptcha('new_user', $_POST['EMAIL'] ?? null)) {
				return false;
			}
		}
	}
	if (!empty($_POST['CITY'])
	&&	preg_match('"^[^a-z]+$"iu', $_POST['CITY'])
	) {
		register_warning('user:warning:city_has_only_weird_characters_will_be_ignored_LINE');
		$_POST['CITY'] = '';
	}
	if (isset($_POST['BIRTHDATE_OPTION'])) {
		$parts = explode(',', $_POST['BIRTHDATE_OPTION']);
		if (count($parts) !== 3) {
			_error('Je hebt geen geboortedatum gekozen!');
			return false;
		}
		if (false === require_number($parts,0)
		||	false === require_number($parts,1)
		||	false === require_number($parts,2)
		) {
			return false;
		}
		$_POST['BIRTH_YEAR']  = $parts[0];
		$_POST['BIRTH_MONTH'] = $parts[1];
		$_POST['BIRTH_DAY']	  = $parts[2];
	}
	$user_admin = have_admin('user');

	// FIXME: move to prohib?
	require_once '_banned_emails.inc';
	if (!empty($_POST['EMAIL'])
	&&	_is_banned_email($_POST['EMAIL'])
	) {
		if (!$userid) {
			register_banned_creation('badmail', temp: false);
			return false;
		}
		register_warning('email:warning:banned_LINE', ['EMAIL' => $_POST['EMAIL']]);
		unset($_POST['EMAIL']);
	}

	$setlist = [];

	if ($userid) {
		if (!require_user()
		||	!require_valid_origin_or_secret()
		) {
			return false;
		}
		if (!($existing_user = db_single_assoc('user','
			SELECT USERID, NICK, CITY, CITYID ,SITE, MSTAMP, EMAIL, BIRTH_YEAR, BIRTH_MONTH, BIRTH_DAY
			FROM user
			WHERE USERID='.$userid
		))) {
			if ($existing_user !== false) {
				not_found();
			}
			return false;
		}
		require_once '_usernick.inc';
		check_nick_limit($existing_user);

		$allow_without_password = true; # $user_admin
		# ^^^ currently allow changing of personal information without password

		if (isset($_POST['CHECKPASSWD'])
		||	$allow_without_password
		&&	isset($_POST['EMAIL'])
		) {
			if ($allow_without_password) {
				$updPersonal = true;
			} elseif (have_something_trim($_POST, 'CHECKPASSWD')) {
				global $currentuser;
				if (!$currentuser->verify_password($_POST['CHECKPASSWD'],0,false)) {
					if (isset($_POST['EMAIL'])) {
						register_warning('user:warning:incorrect_passsword_personals_not_altered_LINE');
					} else {
						$warn_missing = true;
					}
				} else {
					$updPersonal = true;
				}
			}
			if (isset($updPersonal)) {
				if (!isset($_POST['EMAIL'])) {
					$warn_missing = true;
					unset($updPersonal);
				} else {
					if (!user_personal_ok($existing_user)) {
						return false;
					}
					require_once '_helper.inc';
					$setlist[] = 'REALNAME="'.	addslashes($_POST['REALNAME']).'"';
					$setlist[] = 'ADDRESS="'.	addslashes($_POST['ADDRESS']).'"';
					$setlist[] = 'ZIPCODE="'.	addslashes(clean_zip($_POST['ZIPCODE'])).'"';
					$setlist[] = 'PHONE="'.		addslashes(clean_phone($_POST['PHONE'])).'"';
					$setlist[] = 'CLEANPHONE='.	clean_phone_for_sms($_POST['PHONE']);

					foreach (['FIND_BY_EMAIL', 'FIND_BY_REALNAME'] as $key) {
						$setlist[] = $key.'='.(
							isset($_POST[$key])
						?	"b'1'"
						:	(	isset($_POST['WASNULL'][$key])
							?	'NULL'
							:	"b'0'"
							)
						);
					}
					if (!strcasecmp($_POST['EMAIL'],$existing_user['EMAIL'])
					||	$user_admin
					) {
						$setlist[] = 'EMAIL="'.addslashes($_POST['EMAIL']).'"';
					} else {
						require_once '_token.inc';
						require_once '_mail.inc';
						$updEmail = true;
					}
				}
			}
			if (isset($warn_missing)) {
				register_warning('user:warning:password_entered_no_personal_info_LINE');
			}
		}
	} else {
		if (false === require_number($_POST, 'FORMSTAMP')
		||	!require_idnumber($_POST, 'LAST_USERID')
		||	!user_personal_ok(facebook: $facebook)
		||	!require_something_trim($_POST, 'NEW_PASSWD')
		||	!require_something_trim($_POST, 'NEW_PASSWD_TWO')
		) {
			return false;
		}
		if ($_POST['NEW_PASSWD'] !== $_POST['NEW_PASSWD_TWO']) {
			register_error('user:error:passwords_not_identical_LINE');
			return false;
		}
		require_once '_passwordstrength.inc';
		if (!password_strong_enough($_POST['NEW_PASSWD'], $msg)) {
			_error(implode('<br />',$msg));
			return false;
		}
		if ($msg) {
			warning(implode('<br />',$msg));
		}
		if (!empty($_POST['EMAIL'])) {
			$existing_userid = db_single('user','
				SELECT USERID
				FROM user
				WHERE USERID>'.$_POST['LAST_USERID'].'
				  AND EMAIL="'.addslashes($_POST['EMAIL']).'"
				LIMIT 1',
				DB_USE_MASTER
			);
			if ($existing_userid === false) {
				return false;
			}
			if ($existing_userid) {
				_notice('Je hebt je zojuist al aangemeld. Wacht op een email met daarin je inlog gegevens!');
				return true;
			}
		}
		$existing_user = null;
	}
	if ($cityid = have_idnumber($_POST, 'CITYID')) {
		if (false === ($countryid = memcached_single('city', 'SELECT COUNTRYID FROM city WHERE CITYID = '.$_POST['CITYID']))) {
			return false;
		}
	} else {
		$countryid = have_idnumber($_POST, 'COUNTRYID');
	}
	require_once '_usercity.inc';
	if (!$cityid
	&&	have_something($_POST, 'CITY')
	) {
		if (!user_check_city($_POST['CITY'], $setlist, $countryid ?: null)) {
			return false;
		}
	} else {
		set_city_parts($setlist, 0, '', $cityid, $countryid);
	}

	if (!($nick = get_stripped_nick($_POST['NICK']))) {
		register_error('user:error:nick_empty_after_stripping_LINE');
		return false;
	}
	if (!$user_admin) {
		require_once '_prohibited.inc';
		if (($type = nick_is_prohibited($nick,$prohibid))
		||	preg_match('"^(?:https?|ftp):/?/?"i',$nick)
		) {
			register_warning('nick:prohibited_LINE', DO_UBB, ['PROHIBID' => $prohibid]);
			if ($existing_user) {
				$nick = $existing_user['NICK'];
			} else {
				return false;
			}
		}
	}

	if (!$existing_user
	||	 $existing_user['NICK'] !== $nick
	) {
		$newnick = $nick;
	}

	// NICK
	$setlist[] = 'NICK="'.addslashes($nick).'"';
	$account_set_list[] = 'NICK="'.addslashes($nick).'"';
	// if photographer
	if (isset($updPersonal)
	&&	$userid
	&&	CURRENTUSERID === $userid
	&&	have_admin('photographer')
	&&	!have_something_trim($_POST, 'REALNAME')
	) {
		register_error('require:error:need_full_name_(camerarequests)_LINE');
		return false;
	}
	if (!isset($_POST['SITE'])) {
		$setlist[] = 'SITE=""';
	} else {
		require_once '_site.inc';
		$sites = [];
		foreach (preg_split('"[\s\t\r\n]+"', $_POST['SITE']) as $ndx => $site) {
			if (!($site = mytrim($site))) {
				continue;
			}
			$sub_array = [($sub_field = 'SITE.'.$ndx) => $site];
			if (require_working_url_or_none($sub_array, $sub_field, utf8: false, severity: 'warning')) {
				$sites[] = $site;
			}
		}
		$setlist[] = 'SITE="'.addslashes(implode("\n", $sites)).'"';

		require_once '_urlcheck.inc';

		foreach ($sites as $site) {
			create_siteid($site);
		}
	}
	foreach ([
		'artist',
		'location',
		'organization'
	] as $element) {
		if (!isset($_POST[$visibility_name = 'VISIBILITY_NAME_'.$element])) {
			continue;
		}
		require_once '_employees.inc';
		foreach ($_POST[$visibility_name] as $id => $visibility) {
			if (!is_number($id)) {
				_error('Incorrecte formulier gegevens (VISIBILITY_NAME_'.$element.'['.escape_specials($id).'])');
				return false;
			}
			$visibility = (int)$visibility;
			if (!require_visibility($visibility)) {
				return false;
			}
			$updvisi[$element][$id] = ['VISIBILITY_NAME' => $visibility];
		}
	}
	$setlist[] = 'NAME="'.			addslashes($_POST['NAME']).'"';
	$setlist[] = 'OCCUPATION="'.	addslashes($_POST['OCCUPATION']).'"';
	$setlist[] = 'COLOR="'.			$_POST['COLOR'].'"';

	if (!$existing_user
	||	$_POST['BIRTH_YEAR']  === $existing_user['BIRTH_YEAR']
	&&	$_POST['BIRTH_MONTH'] === $existing_user['BIRTH_MONTH']
	&&	$_POST['BIRTH_DAY']	  === $existing_user['BIRTH_DAY']
	||	have_admin('user')
	) {
		$change_birthdate = true;
	} else {
		if (false === ($birthdate_options = db_rowuse_array('user_log', "
			SELECT DISTINCT BIRTH_YEAR, BIRTH_MONTH, BIRTH_DAY
			FROM user_log
			WHERE USERID = $userid
			  AND MSTAMP >= ".(CURRENTSTAMP - ONE_MONTH)
		))) {
			return false;
		}
		if (count($birthdate_options) < 4) {
			$change_birthdate = true;
		} else {
			foreach ($birthdate_options as $birthdate_option) {
				if ($birthdate_option['BIRTH_DAY']	=== $_POST['BIRTH_DAY']
				&&	$birthdate_option['BIRTH_MONTH'] === $_POST['BIRTH_MONTH']
				&&	$birthdate_option['BIRTH_YEAR']	=== $_POST['BIRTH_YEAR']
				) {
					$change_birthdate = true;
					break;
				}
			}
		}
	}
	if (isset($change_birthdate)) {
		if ($_POST['BIRTH_YEAR'] < 1900) {
			$_POST['BIRTH_YEAR'] = 0;
		}
		if ($_POST['BIRTH_MONTH'] < 1 || $_POST['BIRTH_MONTH'] > 12) {
			$_POST['BIRTH_MONTH'] = 0;
		}
		if ($_POST['BIRTH_DAY'] < 1 || $_POST['BIRTH_DAY'] > 31) {
			$_POST['BIRTH_DAY'] = 0;
		}
		$setlist[] = 'BIRTH_YEAR='.		$_POST['BIRTH_YEAR'];
		$setlist[] = 'BIRTH_MONTH='.	$_POST['BIRTH_MONTH'];
		$setlist[] = 'BIRTH_DAY='.		$_POST['BIRTH_DAY'];

		if ($_POST['BIRTH_YEAR']
		&&	$_POST['BIRTH_MONTH']
		&&	$_POST['BIRTH_DAY']
		) {
			if (!require_real_date(
				$_POST['BIRTH_YEAR'],
				$_POST['BIRTH_MONTH'],
				$_POST['BIRTH_DAY'])
			) {
				return false;
			}
			$birthodaynum = to_days(
				$_POST['BIRTH_YEAR'],
				$_POST['BIRTH_MONTH'],
				$_POST['BIRTH_DAY']
			);
			$setlist[] = 'BIRTHODAYNUM = '.($birthodaynum >= DAYNUMOFFSET ? $birthodaynum - DAYNUMOFFSET : 0);
		} else {
			$setlist[] = 'BIRTHODAYNUM = 0';
		}
	} else {
		warning('Geboortedatum niet gewijzigd omdat je deze al te vaak hebt aangepast.<br />'.
			'Wil je toch je geboortedatum wijzigen, neem dan contact op met de helpdesk!');
	}
	if ($lday = have_idnumber($_POST, 'LDAY')) {
		$setlist[] = 'LDAY='.$lday;
	}
	// visibility
	foreach ([
		'BIRTHDAY',
		'AGE',
		'OCCUPATION',
		'NAME',
		'CITY',
		'FAVMUSIC',
		'SEXPREF',
		'GENDER',
		'RELATION',
		'SITE',
		'PRESENCE'
	] as $key) {
		if (!isset($_POST['VISIBILITY_'.$key])
		||	!require_visibility($_POST,$key)
		) {
			continue;
		}
		$setlist[] = 'VISIBILITY_'.$key.' = '.$_POST['VISIBILITY_'.$key];
	}

	$setlist[] = 'SEXPREF="'.(have_element($_POST, 'SEXPREF',array('homo','hetero','bi')) ? $_POST['SEXPREF'] : null).'"';
	$setlist[] = 'SEX="'.(have_element($_POST, 'SEX',array('M','F')) ? $_POST['SEX'] : null).'"';

	/////////////////////////////////// RELATION

	if (empty($_POST['RELATION'])
	||	!have_element($_POST, 'RELATION',array('yes','no'))
	) {
		$setlist[] = 'RELATION=""';
	} else {
		$setlist[] = 'RELATION="'.$_POST['RELATION'].'"';
	}

	////////////////////////sedsdf//////////// PASSWD

	if (!$userid) {
		if (isset($_POST['EMAIL'])) {
			if (false === ($listed = db_boolean_hash(['user', 'banpoint'], '
				SELECT DISTINCT USERID
				FROM banpoint
				JOIN user USING (USERID)
				WHERE "'.($email = addslashes($_POST['EMAIL'])).'"=EMAIL
				  AND EXPIRES>'.CURRENTSTAMP
			))) {
				return false;
			}
			if ($listed) {
				register_banned_creation('email');
				return false;
			}
		}
		if (false === ($blocked_userids = db_boolean_hash('banpoint', '
			SELECT DISTINCT USERID
			FROM banpoint
			WHERE EXPIRES>'.CURRENTSTAMP
		))) {
			return false;
		}
		if ($blocked_userids) {
			# perm ban should ban for half a year, so key past year here too:
			if (false === ($lastipbins = IPV6
			?	db_multirow_hash('ip6list','
				SELECT DISTINCT HEX(PREFIX),LAST_USERID
				FROM ip6list
				WHERE LAST_USERID IN ('.implodekeys(',',$blocked_userids).')
				  AND LAST_USED>'.(CURRENTSTAMP - 6 * ONE_MONTH))
			:	db_multirow_hash('iplist','
				SELECT DISTINCT IPNUM,LAST_USERID
				FROM iplist
				WHERE LAST_USERID IN ('.implodekeys(',',$blocked_userids).')
				  AND LAST_USED>'.(CURRENTSTAMP - 6 * ONE_MONTH)
			))) {
				return false;
			}
			if ($lastipbins) {
				if (IPV6) {
					[$prefix] = ipbin_to_prefix_and_iident(CURRENTIPBIN);
					foreach ($lastipbins as $dis_prefix => $userids) {
						if ($dis_prefix == $prefix) {
							register_banned_creation('ipnum', $userids);
							return false;
						}
					}
				} elseif (isset($lastipbins[CURRENTIPNUM])) {
					register_banned_creation('ipnum', $lastipbins[CURRENTIPNUM]);
					return false;
				}
			}
		}
		require_once '_password.inc';
		$hashed_passwd = hash_password($_POST['NEW_PASSWD']);
		$lsetlist[] = 'PASSWD="'.addslashes($hashed_passwd).'"';
	}
	$setlist[] = 'ACCEPTED=b\'1\'';

	$allow_bodily = $_POST['SEX'] || $_POST['SEXPREF'];

	$dating = 0;
	if (have_number_array($_POST, 'DATING')) {
		require_once 'defines/dating.inc';
		foreach ($_POST['DATING'] as $flg) {
			if (($flg & (DTNG_FLIRT | DTNG_ROMANCE | DTNG_SEX))
			&&	!$allow_bodily
			) {
				require_once '_dating.inc';
				register_warning('user:warning:need_gender_and_preference_for_avail_LINE',['TYPE'=>__('dating:'.get_dating_type($flg), DONT_ESCAPE)]);
				continue;
			}
			$dating |= $flg;
		}
		if ($dating) {
			$dating |= DTNG_UNSPECIFIED;
		}
#		if (!($dating & DTNG_UNSPECIFIED)) {
#			$dating = 0;
#		}
	}
	$setlist[] = 'DATING=b\''.decbin($dating).'\'';

	require_once '_genrelist.inc';
	require_once '_mail.inc';
	require_once '_triplets.inc';

	if ($new = !$userid) {
		require_once '_token.inc';
		require_once '_helper.inc';

		$setlist[] = 'EMAIL="'.		addslashes($_POST['EMAIL']).'"';
		$setlist[] = 'REALNAME="'.	addslashes($_POST['REALNAME']).'"';
		$setlist[] = 'ADDRESS="'.	addslashes($_POST['ADDRESS']).'"';
		$setlist[] = 'ZIPCODE="'.	addslashes(clean_zip($_POST['ZIPCODE'])).'"';
		$setlist[] = 'PHONE="'.		addslashes(clean_phone($_POST['PHONE'])).'"';
		$setlist[] = 'CLEANPHONE='.	clean_phone_for_sms($_POST['PHONE']);

		foreach (['FIND_BY_EMAIL', 'FIND_BY_REALNAME'] as $key) {
			$setlist[] = $key.'='.(isset($_POST[$key]) ? "b'1'" : "b'0'");

		}
		$setlist[] = 'CSTAMP='.CURRENTSTAMP;
		$account_set_list[] = 'STATUS="'.($facebook ? 'active' : 'unactive').'"';

		if (!db_insert('user','INSERT INTO user SET '.implode(',',$setlist))) {
			if (db_errno() === MYSQL_ER_DUP_ENTRY) {
				register_error('user:error:account_already_created_LINE');
			}
			return false;
		}
		$userid = db_insert_id();
		$_REQUEST['sID'] = $userid;

		if (!$userid
		||	!$facebook
		&&	!($token = generate_token($userid, 'activate_account'))
		) {
			return false;
		}

		require_once '_browser.inc';

		if (!db_insert('user_creation','
			INSERT INTO user_creation SET
				BROWSERID	='.get_browserid().',
				IDENTID		='.CURRENTIDENTID.',
				IPBIN		="'.addslashes(CURRENTIPBIN).'",
				EMAIL		="'.addslashes($_POST['EMAIL']).'",
				USERID		='.$userid)
		||	!db_insert('user_account','
			INSERT INTO user_account SET '.implode(',',$account_set_list).', USERID = '.$userid)
		||	!db_insert('user_login','
			INSERT INTO user_login SET '.implode(',',$lsetlist).', USERID = '.$userid)
		||	!db_insert('user_data','
			REPLACE INTO user_data SET
				USERID	='.$userid)
		||	!db_insert('last_ip','
			REPLACE INTO last_ip SET
				USERID	='.$userid.',
				IPBIN	="'.addslashes(CURRENTIPBIN).'"'
			)
		||	!db_insert('user_cache','INSERT IGNORE INTO user_cache SET USERID = '.$userid)
		||	!$facebook
		&&	!mail_activation_token(
				$_POST['EMAIL'],
				$nick,
				$token,
				$userid)
		) {
			return false;
		}
		if (IPV6) {
			[$prefix,$iident] = ipbin_to_prefix_and_iident(CURRENTIPBIN);
			if (!db_replace('ip6list', "
				REPLACE INTO ip6list SET
					PREFIX		=0x$prefix,
					IIDENT		=0x$iident,
					LAST_USERID	= $userid,
					LAST_USED	= ".CURRENTSTAMP)
			) {
				return false;
			}
		} elseif (!db_replace('iplist','
			REPLACE INTO iplist SET
				IPNUM		='.CURRENTIPNUM.',
				LAST_USED	='.CURRENTSTAMP.',
				LAST_USERID	='.$userid)
		) {
			return false;
		}
		require_once '_style.inc';
		db_insert('settings','
		INSERT INTO settings SET
			USERID	='.$userid.',
			THEME	="'.(defined('CURRENTTHEME') ? CURRENTTHEME : DEFAULT_SETTINGS['THEME']).'"'
		);
		if (!db_insert('notifydays','INSERT INTO notifydays SET USERID='.$userid)) {
			return false;
		}
		register_notice('user:notice:registration_successful_LINE');
		if (isset($_COOKIE['FLOCK_FROMNEW'])) {
			db_insert('fromnew','
			INSERT INTO fromnew SET
				INVITE	="'.addslashes($_COOKIE['FLOCK_FROMNEW']).'",
				CSTAMP	='.CURRENTSTAMP.',
				USERID	='.$userid
			);
		}
	}

	$changes =
		store_presence(userid: $userid)
	||	_genrelist_commit();

	if (!$new) {
		require_once '_cityvalidation.inc';
		flush_city_stuff_on_user_change();

		if (!have_self_or_admin($userid, 'user')
		||	!db_insert('user_log','
			INSERT INTO user_log
			SELECT * FROM user
			WHERE NOT '.binary_equal($setlist).'
			  AND USERID = '.$userid)
		) {
			return false;
		}
		if (db_affected()) {
			if (!db_update('user','
				UPDATE user SET
					MSTAMP	='.CURRENTSTAMP.',
					MUSERID	='.CURRENTUSERID.',
					'.implode(',',$setlist).'
				WHERE USERID = '.$userid)
			||	!db_update('user_account','
				UPDATE user_account SET '.implode(',',$account_set_list).'
				WHERE USERID='.$userid)
			) {
				return false;
			}
			if (isset($newnick)) {
				delete_old_triplets($userid,$existing_user['NICK']);
			}
			if (isset($updPersonal)) {
				register_notice('user:notice:personal_information_changed_LINE');
			}
			$changes = true;
		}
		if (isset($updEmail)) {
			if (($token = generate_token($userid, 'change_email', $_POST['EMAIL']))
			&&	mail_change_email($_POST['EMAIL'], $nick, $token, $userid)
			) {
				register_notice('user:notice:email_not_changed_yet_LINE');
			} else {
				register_warning('user:warning:email_not_changed_LINE');
			}
		}
		flush_user($userid);
		if (CURRENTUSERID === $userid) {
			global $currentuser;
			$currentuser->row['NICK'] = $nick;
			$currentuser->row['DATING'] = $dating;
		}
		if (isset($updvisi)) {
			foreach ($updvisi as $element => $list) {
				foreach ($list as $id => $info) {
					update_employee($element,$id,$info,$userid);
					if (db_affected()) {
						$changes = true;
					}
				}
			}
		}
		if ($changes) {
			register_notice('user:notice:profile_changed_LINE');
		} else {
			register_notice('user:notice:profile_not_changed_LINE');
		}
	}

	if (isset($newnick)) {
		insert_new_triplets($userid, $newnick);
	}

	require_once '_elementchanged.inc';
	element_changed(userid: $userid);

	$GLOBALS['__usercommitok'] = true;
	return true;
}

function user_display_settings(): void {
	require_once '_bubble.inc';
	require_once '_notify.inc';
	if ($have_user = have_user()) {
		if (!require_idnumber($_REQUEST,'sID')
		||	!require_self_or_admin($_REQUEST['sID'],'user')
		) {
 			return;
 		}
		$userid = $_REQUEST['sID'];
	} else {
		$userid = 1;
	}
	layout_open_section_header();
	echo Eelement_plural_name('setting');
	layout_close_section_header();

	$settings = db_single_assoc(
		'settings','
		SELECT *
		FROM settings
		WHERE USERID IN ('.$userid.',1)
		ORDER BY USERID DESC
		LIMIT 1',
		DB_USE_MASTER
	);
	if (!$settings) {
		_error('Instellingen van lid met ID '.$userid.' zijn niet goed opvraagbaar!');
		return;
	}
	$externalsettings = db_single_assoc(
		'externalsettings','
		SELECT *
		FROM externalsettings
		WHERE USERID IN ('.$userid.',1)
		ORDER BY USERID DESC
		LIMIT 1',
		DB_USE_MASTER
	);
	if ($externalsettings === false) {
		return;
	}
	if (!$externalsettings) {
		_error('Externe instellingen van lid met ID '.$userid.' zijn niet goed opvraagbaar!');
		return;
	}
	$notifydays = db_single_assoc('notifydays','SELECT * FROM notifydays WHERE USERID = '.$userid);
	if ($notifydays === false) {
		return;
	}
	if (!$notifydays) {
		mail_log('no notifydays for user '.CURRENTUSERID);
	}
	if ($have_user) {
		?><form<?
		?> onsubmit="return submitForm(this)"<?
		?> method="post"<?
		?> action="/user/<?= $userid ?>/commitsettings"><?
	} else {
		?><form><fieldset disabled="disabled"><?
	}

	layout_open_box('settings');
	layout_box_header(Eelement_name('general'));
	layout_open_table('dowrap fifty hla default');
	layout_start_row();

	$checked = setting_isset(SHOW_AGENDA_HEARTS,$userid);
		?><label for="show_agenda_hearts"><?= Eelement_plural_name('buddy_hearts_in_agendas') ?></label><?
		layout_field_value();
		show_input([
			'id'		=> 'show_agenda_hearts',
			'type'		=> 'checkbox',
			'name'		=> 'FLAGS[]',
			'value'		=> SHOW_AGENDA_HEARTS,
			'checked'	=> $checked,
#			'class'		=> 'upLite'
		]);

	$checked = setting_isset(SHOW_SHARE_MENU, $userid);
	layout_restart_row();
#	layout_restart_row(0,null,null,$checked ? 'hilited' : 'not-hilited');
		?><label for="show_share_menu"><?= __C('action:show_share_menu') ?></label><?
		layout_field_value();
		show_input([
			'id'		=> 'show_share_menu',
			'type'		=> 'checkbox',
			'name'		=> 'FLAGS[]',
			'value'		=> SHOW_SHARE_MENU,
			'checked'	=> $checked,
#			'class'		=> 'upLite'
		]);


	$checked = setting_isset(SHOW_NEW_QUOTES,$userid);
	layout_restart_row();
#	layout_restart_row(0,null,null,$checked ? 'hilited' : 'not-hilited');
		?><label for="show_new_quotes"><?= Eelement_name('new_quotes_notification') ?></label><?
		layout_field_value();
		show_input([
			'id'		=> 'show_new_quotes',
			'type'		=> 'checkbox',
			'name'		=> 'FLAGS[]',
			'value'		=> SHOW_NEW_QUOTES,
			'checked'	=> $checked,
#			'class'		=> 'upLite'
		]);

	$checked = setting_isset(ALWAYS_MARK_QUOTES,$userid);
	layout_restart_row();
#	layout_restart_row(0,null,null,$checked ? 'hilited' : 'not-hilited');
		$bubble = new bubble(BUBBLE_30_WIDE | HELP_BUBBLE);
		ob_start();
		?><label for="always_mark_qotes"><?= __C('action:always_mark_quotes') ?></label><?
		$bubble->catcher(ob_get_clean());
		$bubble->content(__('user:settings:always_mark_quotes_info_TEXT',DO_NL2BR));
		$bubble->display_and_cleanup();
		layout_field_value();
		show_input([
			'id'		=> 'always_mark_quotes',
			'type'		=> 'checkbox',
			'name'		=> 'FLAGS[]',
			'value'		=> ALWAYS_MARK_QUOTES,
			'checked'	=> $checked,
#			'class'		=> 'upLite'
		]);

	$checked = $settings['FOLLOW_OPENED_TOPICS'];
	layout_restart_row();
		?><label for="follow_opened_topics"><?= __C('user:settings:follow_own_elements') ?></label><?
		layout_field_value();
		show_input([
			'id'		=> 'follow_opened_topics',
			'type'		=> 'checkbox',
			'name'		=> 'FOLLOW_OPENED_TOPICS',
			'value'		=> 1,
			'checked'	=> $checked,
#			'class'		=> 'upLite'
		]);

	$checked = setting_isset(FOLLOW_POSTS,$userid);
	layout_restart_row();
		?><label for="follow_posts"><?= __C('user:settings:follow_own_posts') ?></label><?
		layout_field_value();
		show_input([
			'id'		=> 'follow_posts',
			'type'		=> 'checkbox',
			'name'		=> 'FLAGS[]',
			'value'		=> FOLLOW_POSTS,
			'checked'	=> $checked,
#			'class'		=> 'upLite'
		]);

	layout_restart_row();
		echo Eelement_plural_name('user_list');
		layout_next_cell();
		$remember = $settings['USERLISTS'] & USERLISTS_REMEMBER;
		?><select name="USERLISTS"><?
			?><option value="0"><?= __('user_lists:short') ?></option><?
			?><option<?
			if (!$remember
			&&	$settings['USERLISTS'] & USERLISTS_TILES
			) {
				?> selected="selected"<?
			}
			?> value="<?= USERLISTS_TILES ?>"><?= __('user_lists:comprehensive') ?></option><?
			?><option<?
			if ($remember) {
				?> selected="selected"<?
			}
			?> value="<?= $settings['USERLISTS'] | USERLISTS_REMEMBER ?>"><?= __('action:remember') ?></option><?
		?></select><?

	layout_restart_row();
		?><label for="googlemaps"><?= Eelement_plural_name('map(geo)') ?></label><?
		layout_field_value();
		if (!isset($settings['GMAPS'])) {
			$settings['GMAPS'] = null;
		}
		?><select id="googlemaps" name="GMAPS"><?
			?><option<? if ($settings['GMAPS'] === 'map') echo ' selected'; ?> value="map"><?= __('googlemaps:map') ?></option><?
			?><option<? if ($settings['GMAPS'] === 'satellite') echo ' selected'; ?> value="satellite"><?= __('googlemaps:satellite') ?></option><?
			?><option<? if ($settings['GMAPS'] === 'both') echo ' selected'; ?> value="both"><?= __('googlemaps:hybrid') ?></option><?
		?></select><?

	layout_restart_row();
		?><label for="show_google_calendar"><?= __C('user:settings:show_google_calendar'); ?></label><?
		layout_field_value();
		?><input type="checkbox" id="show_google_calendar" value="<?= SHOW_GOOGLE_CALENDAR;
		?>" name="FLAGS[]"<? if (setting_isset(SHOW_GOOGLE_CALENDAR,$userid)) echo ' checked="checked"'; ?>><?

	layout_stop_row();
	layout_close_table();
	layout_close_box();

	layout_open_box('settings');
	layout_box_header(Eelement_name('your_profile'));
	layout_open_table('fifty hla default');
	layout_start_row();
		echo __C('user:settings:visibility_profile');
		layout_field_value();
		_visibility_display_form_part($externalsettings,'PROFILE');
	layout_restart_row();
		echo __C('user:settings:visibility_online');
		layout_field_value();
		_visibility_display_form_part(memcached_user($userid),'ONLINE');
/*	layout_restart_row();
		echo __C('user:settings:visibility_diagram');
		layout_field_value();
		_visibility_display_form_part($externalsettings,'DIAGRAM');*/
	layout_restart_row();
		echo __C('user:settings:visibility_guestbook');
		layout_field_value();
		?><select name="VISIBILITY_GUESTBOOK"><?
			show_visibility_options($externalsettings['VISIBILITY_GUESTBOOK']);
			?><option<?
			if (NOBODY == $externalsettings['VISIBILITY_GUESTBOOK']) {
				?> selected="selected"<?
			}
			?> value="<?= NOBODY ?>"><?= _visibility_name(NOBODY) ?></option><?
		?></select><?
	layout_restart_row();
		echo __C('user:settings:writability_guestbook');
		layout_field_value();
		_visibility_display_form_part($externalsettings,'WRITEGUESTBOOK',null,null,false,MEMBERS);

	foreach (array('agenda','statistics','buddies','favourites','appearances') as $name) {
		layout_restart_row();
		echo __C('user:settings:visibility_'.$name);
		layout_field_value();
		_visibility_display_form_part($externalsettings,strtoupper($name));
	}
/*	layout_restart_row();
		?><label for="weblog_visible"><?= __C('user:settings:weblog_visible') ?></label><?
		layout_field_value();
		?><input type="checkbox" id="weblog_visible" value="1" name="WEBLOG_VISIBLE"<? if ($externalsettings['WEBLOG_VISIBLE']) echo ' checked="checked"'; ?>><?*/
	layout_restart_row();
		?><label for="show_stars_personal_agenda"><?= __C('user:settings:favourite_stars_in_personal_agenda') ?></label><?
		layout_field_value();
		?><input type="checkbox" id="show_stars_personal_agenda" value="1" name="SHOW_STARS_PERSONAL_AGENDA"<? if ($settings['SHOW_STARS_PERSONAL_AGENDA']) echo ' checked="checked"'; ?>><?
	layout_stop_row();
	layout_close_table();
	layout_close_box();

	layout_open_box('settings','notifications');
	layout_box_header(Eelement_plural_name('email_notification'));
	?><div class="block"><?= __('user:settings:info:email_updates_TEXT',DO_NL2BR) ?></div><?
	layout_open_table('fifty hla default');
	layout_start_row();
	echo Eelement_plural_name('new_private_message');
	layout_field_value();
	show_notify_options('NOTIFY_MSG_DAYS',$notifydays ? $notifydays[NOTIFY_MSG] : null);

	layout_restart_row();
	echo Eelement_plural_name('new_buddy_request');
	layout_field_value();
	show_notify_options('NOTIFY_BUDDY_DAYS',$notifydays ? $notifydays[NOTIFY_BUDDY] : null);

	layout_restart_row();
	echo Eelement_plural_name('new_guestbook_comment');
	layout_field_value();
	show_notify_options('NOTIFY_GUESTBOOK_DAYS',$notifydays ? $notifydays[NOTIFY_GUESTBOOK] : null);

	layout_restart_row();
	echo Eelement_plural_name('new_fotoshoot_of_visitied_party');
	layout_field_value();
	show_notify_options('NOTIFY_GALLERY_DAYS',$notifydays ? $notifydays[NOTIFY_GALLERY] : null);

	layout_restart_row();
	echo Eelement_plural_name('new_contest');
	layout_field_value();
	show_notify_options('NOTIFY_CONTEST_DAYS',$notifydays ? $notifydays[NOTIFY_CONTEST] : null);

	layout_restart_row(!$notifydays ? ROW_HIDDEN : 0,'always-row');
	?><label for="notify_always"><?=  __C('user:settings:receive_updates_how_often') ?></label><?
	layout_field_value();

	?><script>
	var pf_msgs = <?= $notifydays && $notifydays[NOTIFY_MSG] !== null ? 'true' : 'false'; ?>;
	var pf_budd = <?= $notifydays && $notifydays[NOTIFY_BUDDY] !== null ? 'true' : 'false'; ?>;
	var pf_gall = <?= $notifydays && $notifydays[NOTIFY_GALLERY] !== null ? 'true' : 'false'; ?>;
	var pf_gbook = <?= $notifydays && $notifydays[NOTIFY_GUESTBOOK] !== null ? 'true' : 'false'; ?>;
	var pf_cntst = <?= $notifydays && $notifydays[NOTIFY_CONTEST] !== null ? 'true' : 'false'; ?>;
	function changeNotify(type,value) {
		switch (type){
		case 'NOTIFY_MSG_DAYS':
			pf_msgs = value !== 'NULL';
			break;
		case 'NOTIFY_BUDDY_DAYS':
			pf_budd = value !== 'NULL';
			break;
		case 'NOTIFY_GALLERY_DAYS':
			pf_gall = value !== 'NULL';
			break;
		case 'NOTIFY_GUESTBOOK_DAYS':
			pf_gbook = value !== 'NULL';
			break;
		case 'NOTIFY_CONTEST_DAYS':
			pf_cntst = value !== 'NULL';
			break;
		}
		setdisplay('always-row',pf_msgs || pf_budd || pf_gall || pf_cntst);
	}
	</script><?

	?><select id="notifiy_always" name="NOTIFY_ALWAYS"><?
		?><option value="0"><?= __('user:settings:notify:only_when_active') ?></option><?
		?><option<? if ($notifydays && $notifydays['ALWAYS'] == 1) echo ' selected="selected"'; ?> value="1"><?= __('user:settings:notify:after_year_of_inactivity_too') ?></option><?
	?></select><?

	layout_stop_row();

	layout_close_table();
	layout_close_box();


	layout_open_box('settings');
	layout_box_header(Eelement_name('appearance'));
	layout_open_table('fifty hla default');
	layout_start_row();
		?><label for="font_type"><?= Eelement_name('font') ?></label><?
		layout_field_value();
		?><select id="font_type" name="FONT_TYPE"><?
			?><optgroup label="<?= __('font:generic') ?>"><option<? if ($settings['FONT_TYPE'] === 'browser') echo ' selected'; ?> value="browser"><?= __('font:browser_setting') ?></option><?
			?><option<? if ($settings['FONT_TYPE'] === 'serif') echo ' selected'; ?> value="serif">serif</option><?
			?><option<? if ($settings['FONT_TYPE'] === 'sans-serif') echo ' selected'; ?> value="sans-serif">sans-serif</option><?
			?><option<? if ($settings['FONT_TYPE'] === 'cursive') echo ' selected'; ?> value="cursive">cursive</option><?
			?><option<? if ($settings['FONT_TYPE'] === 'fantasy') echo ' selected'; ?> value="fantasy">fantasy</option><?
			?><option<? if ($settings['FONT_TYPE'] === 'monospace') echo ' selected'; ?> value="monospace">monospace</option><?
			?></optgroup><optgroup label="<?= __('font:specific') ?>"><?
			?><option<? if ($settings['FONT_TYPE'] === 'Arial') echo ' selected'; ?> value="Arial">Arial</option><?
			?><option<? if ($settings['FONT_TYPE'] === 'Comic Sans MS') echo ' selected'; ?> value="Comic Sans MS">Comic Sans MS</option><?
			?><option<? if ($settings['FONT_TYPE'] === 'Raleway') echo ' selected'; ?> value="Raleway">Raleway</option><?
			?><option<? if ($settings['FONT_TYPE'] === 'Tahoma') echo ' selected'; ?> value="Tahoma">Tahoma</option><?
			?><option<? if ($settings['FONT_TYPE'] === 'Times New Roman') echo ' selected'; ?> value="Times New Roman">Times New Roman</option><?
			?><option<? if ($settings['FONT_TYPE'] === 'Titillium Web') echo ' selected'; ?> value="Titillium Web">Titillium Web</option><?
			?><option<? if ($settings['FONT_TYPE'] === 'Verdana') echo ' selected'; ?> value="Verdana">Verdana</option><?
			?></optgroup><?
		?></select><?
	layout_restart_row();
		?><label for="font_size"><?= Eelement_name('font_size') ?></label><?
		layout_field_value();
		?><select id="font_size" name="FONT_SIZE"><?
			?><option<? if ($settings['FONT_SIZE'] === 'browser') echo ' selected'; ?> value="browser"><?= __('font:browser_setting') ?></option><?
			?><option<? if ($settings['FONT_SIZE'] === 'xx-small') echo ' selected'; ?> value="xx-small"><?= __('size:super_small') ?></option>
			?><option<? if ($settings['FONT_SIZE'] === 'x-small') echo ' selected'; ?> value="x-small"><?= __('size:extra_small') ?></option><?
			?><option<? if ($settings['FONT_SIZE'] === 'small') echo ' selected="selected"'; ?> value="small"><?= __('size:small') ?></option><?
			?><option<? if ($settings['FONT_SIZE'] === 'medium') echo ' selected'; ?> value="medium"><?= __('size:medium') ?></option><?
			?><option<? if ($settings['FONT_SIZE'] === 'large') echo ' selected'; ?> value="large"><?= __('size:large') ?></option><?
			?><option<? if ($settings['FONT_SIZE'] === 'x-large') echo ' selected'; ?> value="x-large"><?= __('size:extra_large') ?></option><?
			?><option<? if ($settings['FONT_SIZE'] === 'xx-large') echo ' selected'; ?> value="xx-large"><?= __('size:super_large') ?></option><?
		?></select><?
	layout_restart_row();
		?><label for="theme"><?= Eelement_name('theme') ?></label><?
		layout_field_value();
		?><select id="theme" name="THEME"><?
		foreach ([
			''	=> element_name('default'),
			'light' => __('setting:light_site'),
			'dark'	=> __('setting:dark_site'),
		]  as	$theme	=> $name) {
			?><option value="<?= $theme?>"<?
			if ($settings['THEME'] === $theme) {
				?> selected<?
			}
			?>><?= $name ?></option><?
		}
		?></select><?
	layout_restart_row();
		?><label for="theme-contrast"><?= Eelement_name('contrast') ?></label><?
		layout_field_value();
		?><select id="theme-contrast" name="THEME_CONTRAST"><?
		for ($cnt = 100; $cnt >= 10; $cnt -= 5) {
			?><option <?
			if ($cnt == $settings['THEME_CONTRAST']) {
				?>selected <?
			}
			?>value="<?= $cnt; ?>"><?= $cnt; ?>%</option><?
		}
		?></select><?
	layout_restart_row();
		?><label for="outline"><?= Eelement_name('outline') ?></label><?
		layout_field_value();
		?><input id="outline" type="checkbox" value="1" name="THEME_HARD"<? if (!$settings['THEME_SOFT']) echo ' checked="checked"'; ?>><?
/*		layout_field_value();
		?><input type="checkbox" id="theme_soft" value="1" name="THEME_SOFT"<? if ($settings['THEME_SOFT']) echo ' checked'; ?>><?*/
	layout_restart_row();
		?><label for="show_profile_images"><?= __C('user:settings:show_forum_images') ?></label><?
		layout_field_value();
		?><input type="checkbox" id="show_profile_images" value="1" name="SHOW_PROFILE_IMAGES"<? if ($settings['SHOW_PROFILE_IMAGES']) echo ' checked'; ?>><?
	layout_stop_row();
	layout_close_table();
	layout_close_box();

	layout_open_box('settings');
	layout_box_header(Eelement_plural_name('photo'));
	layout_open_table('fifty hla default');
	layout_start_row();
		?><label for="photos_per_page"><?= __C('user:settings:number_of_photos_per_page') ?></label><?
		layout_field_value();
		?><input type="number" data-valid="number" class="three_digits" name="PHOTOS_PER_PAGE" min="5" max="255" value="<?= $settings['PHOTOS_PER_PAGE']; ?>" /><?
	layout_stop_row();
	layout_close_table();
	layout_close_box();

	layout_open_box('settings');
	layout_box_header(__('user:settings:header:forum_and_flocks'));//'Forum &amp; Flocks');
	layout_open_table('fifty hla default');
	layout_start_row();
		?><label for="topics_per_page"><?= __C('user:settings:number_of_topics_per_page') ?></label><?
		layout_field_value();
		?><input type="number" data-valid="number" class="three_digits" id="topics_per_page" name="TOPICS_PER_PAGE" min="5" max="200" value="<?= $settings['TOPICS_PER_PAGE']; ?>" /><?
	layout_restart_row();
		?><label for="messages_per_topic"><?= __C('user:settings:number_of_posts_per_page') ?></label><?
		layout_field_value();
		?><input type="number" data-valid="number" class="three_digits" id="messages_per_topic" name="MESSAGES_PER_TOPIC" min="5" max="1000" value="<?= $settings['MESSAGES_PER_TOPIC']; ?>" /><?
	layout_restart_row();
		?><label for="hide_stars_in_forum"><?= __C('user:settings:no_favourite_stars_after_topics') ?></label><?
		layout_field_value();
		?><input type="checkbox" id="hide_stars_in_forum" value="1" name="HIDE_STARS_IN_FORUM"<? if ($settings['HIDE_STARS_IN_FORUM']) echo ' checked'; ?>><?
	layout_restart_row();
		?><label for="show_topic_hearts"><?= __C('user:settings:buddy_hearts_after_topics') ?></label><?
		//Vrienden harten in onderwerpen
		layout_field_value();
		?><input type="checkbox" id="show_topic_hearts" value="<?= SHOW_TOPIC_HEARTS;
		?>" name="FLAGS[]"<? if (setting_isset(SHOW_TOPIC_HEARTS,$userid)) echo ' checked="checked"'; ?>><?
	layout_restart_row();
		?><label for="always_show_subs"><?= __C('user:settings:always_show_subscriptions') ?></label><?
		layout_field_value();
		?><input type="checkbox" id="always_show_subs" name="FLAGS[]" value="<?= ALWAYS_SHOW_SUBS ?>"<? if (setting_isset(ALWAYS_SHOW_SUBS,$userid)) echo ' checked="checked"'; ?>><?
	layout_stop_row();
	layout_close_table();
	layout_close_box();

	layout_open_box('settings');
	layout_box_header(Eelement_plural_name('flock'));
	layout_open_table('fifty hla default');
	layout_start_row();
		?><label for="flockinvite"><?= __C('user:settings:receive_invitations') ?></label><?
		/*Uitnodigingen ontvangen</label><?*/
		layout_field_value();
		?><select id="flockinvite" name="FLOCKINVITE"><?
			?><option<? if ($externalsettings['FLOCKINVITE'] === 'always') echo ' selected'; ?> value="always"><?= __('answer:always') ?></option><?
			?><option<? if ($externalsettings['FLOCKINVITE'] === 'personalonly') echo ' selected'; ?> value="personalonly"><?= __('answer:personal_only') ?></option><?
			?><option<? if ($externalsettings['FLOCKINVITE'] === 'never') echo ' selected'; ?> value="never"><?= __('answer:never') ?></option><?
		?></select><?
	layout_stop_row();
	layout_close_table();
	layout_close_box();

	layout_open_box('settings');
	layout_box_header(Eelement_plural_name('comment'));
	layout_open_table('fifty hla default');
	layout_start_row();
		?><label for="paged_comments_from"><?= __C('user:settings:paged_comments_starting_from') ?></label><?
		//Opmerkingen gepagineerd vanaf<?
		layout_field_value();
		?><input id="paged_comments_from" class="three_digits" type="number" data-valid="number" name="PAGED_COMMENTS_STARTING_FROM" min="10" max="5000" value="<?= $settings['PAGED_COMMENTS_STARTING_FROM']; ?>" /> <?
		echo element_plural_name('comment');
	layout_restart_row();
		?><label for="comments-per-page"><?= __C('user:settings:number_of_posts_per_page') ?></label><?
		layout_field_value();
		?><input type="number" data-valid="number" class="three_digits" id="comments-per-page" name="COMMENTS_PER_PAGE" min="5" max="100" value="<?= $settings['COMMENTS_PER_PAGE']; ?>" /><?
	layout_restart_row();
		?><label for="comment_page_order"><?= Eelement_name('order') ?></label><?
		layout_field_value();
		?><select id="comment_page_order" name="COMMENT_PAGE_ORDER"><?
			?><option<? if ($settings['COMMENT_PAGE_ORDER'] === 'chrono') echo ' selected'; ?> value="chrono"><?= __('order:chronological'); ?></option><?
			?><option<? if ($settings['COMMENT_PAGE_ORDER'] === 'rchrono') echo ' selected'; ?> value="rchrono"><?= __('order:reverse_chrological') ?></option><?
			?><option<? if ($settings['COMMENT_PAGE_ORDER'] === 'lrchrono') echo ' selected'; ?> value="lrchrono"><?= __('order:only_last_page_reverse_chronological') ?></option><?
		?></select><?
	require_once '_comment.inc';
	foreach (commentables() as $table => $type) {
		if ($table === 'user'
		||	$table === 'ad'
		) {
			continue;
		}
		$commentables[$table] = __('element:'.$table,DONT_ESCAPE);
	}
	asort($commentables);
	foreach ($commentables as $table => $name) {
		layout_restart_row();
		?><label for="uncollapse-<?= $table; ?>"><?= __C('user:settings:show_comments_below',array('ELEMENTNAME'=>$name)) ?></label><?
		layout_field_value();
		$bit = 1<<constant('CMT_'.strtoupper($table));
		?><input type="checkbox"<?
		if ($settings['UNCOLLAPSE_COMMENTS'] & $bit) {
			?> checked="checked"<?
		}
		?> value="<?= $bit; ?>" id="uncollapse-<?= $table; ?>" name="UNCOLLAPSE_COMMENTS[]"><?
	}
	layout_stop_row();
	layout_close_table();
	layout_close_box();

	layout_open_box('settings');
	layout_box_header(Eelement_name('karma'));
	layout_open_table('fifty hla default');
	layout_start_row();
		?><label for="karma_options"><?= Eelement_plural_name('karma_option') ?></label><?
		layout_field_value();
		?><input type="checkbox" id="karma_options" name="FLAGS[]" value="<?= USE_KARMA ?>"<?
		if (setting_isset(USE_KARMA,$userid)) echo ' checked="checked"'; ?>><?
	layout_stop_row();
	layout_close_table();
	layout_close_box();

	layout_open_box('settings');
	layout_box_header(Eelement_plural_name('rating'));
	layout_open_table('fifty hla default');
	layout_start_row();
		?><label for="hide_ratings_from"><?= __C('user:settings:collapse_ratings_starting_from') ?></label><?
		//Waarderingen inklappen vanaf
		layout_field_value();
		?><input id="hide_ratings_from" class="three_digits" type="number" data-valid="number" name="HIDE_RATINGS_STARTING_FROM" min="10" max="2000" value="<?= $settings['HIDE_RATINGS_STARTING_FROM']; ?>" /> <?
		echo element_plural_name('rating');
	require_once '_rating.inc';
	$ratingables = RATINGABLES;
	foreach ($ratingables as $table => &$val) {
		$val = __('element:'.$table,DONT_ESCAPE);
	}
	unset($val);
	asort($ratingables);
	foreach ($ratingables as $table => $name) {
		layout_restart_row();
		?><label for="uncollapse-r-<?= $table; ?>"><?= __C('user:settings:show_ratings_below',array('ELEMENTNAME'=>$name)) ?></label><?
		layout_field_value();
		$bit = 1<<constant('RATING_'.strtoupper($table));
		?><input type="checkbox"<?
		if ($settings['SHOW_RATINGS'] & $bit) {
			?> checked="checked"<?
		}
		?> value="<?= $bit; ?>" id="uncollapse-r-<?= $table; ?>" name="SHOW_RATINGS[]"><?
	}
	layout_stop_row();
	layout_close_table();
	layout_close_box();

	layout_open_box('settings');
	layout_box_header(Eelement_plural_name('directmessage'));
	layout_open_table('fifty hla default');
	layout_start_row();
		?><label for="dmessages_per_page"><?= __C('user:settings:number_of_messages_per_page') ?></label><?
		layout_field_value();
		?><input id="dmessage_per_page" type="number" data-valid="number" class="three_digits" min="5" max="200" name="DMESSAGES_PER_PAGE" value="<?= $settings['DMESSAGES_PER_PAGE']; ?>" /><?
	layout_restart_row();
		?><label for="hide_unread_outgoing"><?= __C('user:settings:hide_unread_outgoing') ?></label><?
		layout_field_value();
		?><input type="checkbox" value="1" id="hide_unread_outgoing" name="HIDE_UNREAD_OUTGOING"<?
		if ($settings['HIDE_UNREAD_OUTGOING']) { ?> checked="checked"<?	}
		?>><?
	layout_stop_row();
	layout_close_table();
	layout_close_box();

	layout_open_box('settings');
	layout_box_header(Eelement_name('personal_menu'));
	layout_open_table('fifty hla default');
	layout_start_row();
/*	if ($userid <= 1231682
	||	!$settings['USER_MENU_TOP']
	) {
		?><label for="user_menu_top"><?= __('user:settings:personal_menu_at_top') ?></label><?
		layout_field_value();
		?><input type="checkbox" id="user_menu_top" value="1" name="USER_MENU_TOP"<? if ($settings['USER_MENU_TOP']) echo ' checked="checked"'; ?>><?
		layout_restart_row();
	} else {
		?><input type="checkbox" value="1" name="USER_MENU_TOP" checked="checked" /><?
	}*/
		?><label for="show_personal_subs"><?= __('user:settings:show_following_in_personal_menu') ?></label><?
		layout_field_value();
		?><input<? if (setting_isset(SHOW_PERSONAL_SUBS,$userid)) echo ' checked="checked"';
		?> type="checkbox" id="show_personal_subs" value="<?= SHOW_PERSONAL_SUBS ?>" name="FLAGS[]"><?
	layout_restart_row();
		?><label for="show_personal_note"><?= __('user:settings:show_note_in_personal_menu') ?></label><?
		layout_field_value();
		?><input<? if (setting_isset(SHOW_PERSONAL_NOTE,$userid)) echo ' checked="checked"';
		?> type="checkbox" id="show_personal_note" value="<?= SHOW_PERSONAL_NOTE ?>" name="FLAGS[]"><?
	layout_stop_row();
	layout_close_table();
	layout_close_box();

	layout_open_box('settings');
	layout_box_header(Eelement_plural_name('profile'));
	layout_open_table('fifty hla default');
	layout_start_row();
		?><label for="show_user_archive"><?= __C('user:settings:always_show',array('WHAT'=>__('element:agenda_archive',DONT_ESCAPE))) ?></label><?
		layout_field_value();
		?><input type="checkbox" id="show_user_archive" value="1" name="SHOW_USER_ARCHIVE"<? if ($settings['SHOW_USER_ARCHIVE']) echo ' checked="checked"'; ?>><?
	layout_restart_row();
		?><label for="show_user_personal"><?= __C('user:settings:always_show',array('WHAT'=>__('element:personal_text',DONT_ESCAPE))) ?></label><?
		layout_field_value();
		?><input type="checkbox" id="show_user_personal" value="1" name="SHOW_USER_PERSONAL"<? if ($settings['SHOW_USER_PERSONAL']) echo ' checked="checked"'; ?>><?
	layout_restart_row();
		?><label for="show_user_guestbook"><?= __C('user:settings:always_show',array('WHAT'=>__('element:guestbook',DONT_ESCAPE))) ?></label><?
		layout_field_value();
		?><input type="checkbox" id="show_user_guestbook" value="1" name="SHOW_USER_GUESTBOOK"<? if ($settings['SHOW_USER_GUESTBOOK']) echo ' checked="checked"'; ?>><?
	if (have_admin('helpdesk')) {
		layout_restart_row();
			?><label for="show_user_secrets"><?= __C('user:settings:always_show',array('WHAT'=>__('elements:secret',DONT_ESCAPE))) ?></label><?
			layout_field_value();
			?><input type="checkbox" id="show_user_secrets" value="1" name="SHOW_USER_SECRETS"<? if ($settings['SHOW_USER_SECRETS']) echo ' checked="checked"'; ?>><?
	}
	layout_restart_row();
		?><label for="show_user_favourites"><?= __C('user:settings:always_show',array('WHAT'=>__('elements:favourite',DONT_ESCAPE))) ?></label><?
		layout_field_value();
		?><input type="checkbox" id="show_user_favourites" value="1" name="SHOW_USER_FAVOURITES"<? if ($settings['SHOW_USER_FAVOURITES']) echo ' checked="checked"'; ?>><?

	layout_restart_row();
		?><label for="show_user_polls"><?= __C('user:settings:always_show',array('WHAT'=>__('element:poll',DONT_ESCAPE))) ?></label><?
		layout_field_value();
		?><input type="checkbox" id="show_user_polls" value="<?= SHOW_USER_POLLS; ?>" name="FLAGS[]"<? if (setting_isset(SHOW_USER_POLLS,$userid)) echo ' checked="checked"'; ?>><?
	layout_restart_row();
		?><label for="show_user_buddies"><?= __C('user:settings:always_show',array('WHAT'=>__('elements:buddy',DONT_ESCAPE))) ?></label><?
		layout_field_value();
		?><input type="checkbox" id="show_user_buddies" value="1" name="SHOW_USER_BUDDIES"<? if ($settings['SHOW_USER_BUDDIES']) echo ' checked="checked"'; ?>><?
/*	layout_restart_row();
		?><label for="show_user_weblog"><?= __C('user:settings:always_show',array('WHAT'=>__('elements:weblog',DONT_ESCAPE))) ?></label><?
		layout_field_value();
		?><input type="checkbox" id="show_user_weblog" value="1" name="SHOW_USER_WEBLOG"<? if ($settings['SHOW_USER_WEBLOG']) echo ' checked="checked"'; ?>><?*/
	layout_restart_row();
		?><label for="uncollapse_hidden_user_text"><?= __C('user:settings:always_unfold_hidden_parts') ?></label><?
		layout_field_value();
		?><input type="checkbox" id="uncollapse_hidden_user_text" value="1" name="UNCOLLAPSE_HIDDEN_USER_TEXT"<? if ($settings['UNCOLLAPSE_HIDDEN_USER_TEXT']) echo ' checked="checked"'; ?>><?
/*	layout_restart_row();
		?><label for="show_maybe_parties"><?= __C('user:settings:show_maybe_parties_of_others') ?></label><?
		layout_field_value();
		?><input type="checkbox" id="show_maybe_parties" value="<?= SHOW_MAYBE_PARTIES ?>" name="FLAGS[]"<? if (setting_isset(SHOW_MAYBE_PARTIES,$userid)) echo ' checked="checked"'; ?>><?*/
	layout_stop_row();
	layout_close_table();
	layout_close_box();

	require_once '_donators.inc';
	$till = $have_user ? may_hide_ads_till($userid) : false;
	layout_open_box('settings','ads');
	layout_box_header(Eelement_name('advertisement(general)'));
	if ($till) {
		?><div class="block"><?=
			__('user:settings:you_may_hide_ads_till_LINE')
			?>  <?
			_datedaytime_display($till);
		?></div><?
	} else {
		?><div class="block"><?=
			__('user:settings:you_may_not_hide_ads_LINE')
		?></div><?
		if (!$have_user) {
			?><div class="warning block"><?= __C('require:user:need_to_be_logged_in_LINE') ?></div><?
			?><div>&rarr; <?
			?><span class="larger smenu ns notice block"><?
			?><a href="/user/signup"><?= __('action:signup') ?></a><?
			?></span><?
			?></div><?
		}
		?><div>&rarr; <?
		?><span class="larger smenu ns notice block"><?
		?><a href="/donation"><?= __('action:donate') ?></a><?
		?></span><?
		?></div><?
	}
	?><hr class="slim"><?
	?><div class="block"><?=
		__('user:settings:info_hide_ads_TEXT', DO_NL2BR)
	?></div><?
	?><hr class="slim"><?
	layout_open_table('fifty hla default');
	if ($till) {
		?><input type="hidden" name="ADS" value="1" /><?
	}
	foreach ([
		ADS_SHOW_PROMO		=> Eelement_plural_name('promo'),
		ADS_SHOW_TOP_BANNER	=> Eelement_plural_name('banner').' '.MIDDLE_DOT_ENTITY.' '.__('position:top').' / '.__('position:bottom'),
		ADS_SHOW_SUB_BAR	=> Eelement_plural_name('banner').' '.MIDDLE_DOT_ENTITY.' '.__('adpos:'.ADPOS_SUBBAR),
		ADS_SHOW_LEFT_TOWER	=> Eelement_plural_name('banner').' '.MIDDLE_DOT_ENTITY.' '.__('position:left'),
		ADS_SHOW_RIGHT_TOWER	=> Eelement_plural_name('banner').' '.MIDDLE_DOT_ENTITY.' '.__('position:right'),
		ADS_SHOW_MENU_MINI	=> Eelement_plural_name('banner').' '.MIDDLE_DOT_ENTITY.' '.__('adpos:'.ADPOS_MENUMINI),
		ADS_SHOW_OVERLAY	=> Eelement_plural_name('banner').' '.MIDDLE_DOT_ENTITY.' '.__('adpos:'.ADPOS_OVERLAY),
		ADS_SHOW_PARTYLIST	=> Eelement_plural_name('other(elements)').' '.MIDDLE_DOT_ENTITY.' '.__('adpos:'.ADPOS_PARTYLIST),
	] as $flag => $desc) {
		layout_row(
			'<label for="FLAG'.$flag.'">'.$desc.'</label>',
			get_input(array(
				'id'		=> 'FLAG'.$flag,
				'type'		=> 'checkbox',
				'name'		=> 'FLAGS[]',
				'value'		=> $flag,
				'disabled'	=> $till ? false : true,
				'checked'	=> setting_isset($flag,$userid)
			))
		);
	}
	layout_close_table();
	layout_close_box();

	?><div class="block"><input type="submit" value="<?= __('action:change') ?>" /><?

	if (!$have_user) {
		?> <span class="warning block"><?= __C('require:user:need_to_be_logged_in_LINE') ?></span><?
	}
	?></div><?
	if (!$have_user) {
		?></fieldset><?
	}
	?></form><?
}

function user_commit_settings(): bool {
	require_once '_notify.inc';
	if (!($userid = require_idnumber($_REQUEST, 'sID'))
	||	!require_self_or_admin($userid, 'user')
	||	!require_element($_POST, 'FLOCKINVITE', ['always', 'never', 'personalonly'], true)
	||	!require_visibility($_POST, 'ONLINE')
	) {
		return false;
	}
	if (isset($_POST['NOTIFY_MSG_DAYS'])) {
		if (false === require_number($_POST, 'NOTIFY_ALWAYS')) {
			return false;
		}
		foreach ([
			'NOTIFY_MSG_DAYS',
			'NOTIFY_BUDDY_DAYS',
			'NOTIFY_GALLERY_DAYS',
			'NOTIFY_GUESTBOOK_DAYS',
			'NOTIFY_CONTEST_DAYS',
		] as $key) {
			if (!require_anything($_POST,$key)
			||	!require_number_trim($_POST,$key.'_EXPLICIT')
			||	(	$_POST[$key] !== 'NULL'
				&&	$_POST[$key] != -1
				&&	false === require_number($_POST,$key)
				)
			) {
				return false;
			}
		}
/*		if ($_POST['NOTIFY_MSG_DAYS'] == 'NULL'
		&&	$_POST['NOTIFY_BUDDY_DAYS'] == 'NULL'
		&&	$_POST['NOTIFY_GALLERY_DAYS'] == 'NULL'
		&&	$_POST['NOTIFY_GUESTBOOK_DAYS'] == 'NULL'
		) {*/
			if (!db_delete('notifydays','
				DELETE FROM notifydays
				WHERE USERID='.$userid)
			) {
				return false;
			}
/*		} else {*/
			if (!(	(	$userid === 1
					||	($email = db_single('user','SELECT EMAIL FROM user WHERE USERID='.$userid))
					&&	!($fails = db_single('realemailcheck','SELECT FAILS FROM realemailcheck WHERE EMAIL="'.addslashes($email).'"'))
					)
				?	true
				:	require_working_user_email($userid, $email)
				)
			||	!db_replace('notifydays','
				REPLACE INTO notifydays SET
					USERID	='.$userid.',
					ALWAYS	='.$_POST['NOTIFY_ALWAYS'].',
					`1`		='.($_POST['NOTIFY_MSG_DAYS'		] === -1 ? $_POST['NOTIFY_MSG_DAYS_EXPLICIT'	  ] : $_POST['NOTIFY_MSG_DAYS']).',
					`2`		='.($_POST['NOTIFY_BUDDY_DAYS'		] === -1 ? $_POST['NOTIFY_BUDDY_DAYS_EXPLICIT'	  ] : $_POST['NOTIFY_BUDDY_DAYS']).',
					`4`		='.($_POST['NOTIFY_GALLERY_DAYS'	] === -1 ? $_POST['NOTIFY_GALLERY_DAYS_EXPLICIT'  ] : $_POST['NOTIFY_GALLERY_DAYS']).',
					`8`		='.($_POST['NOTIFY_GUESTBOOK_DAYS'	] === -1 ? $_POST['NOTIFY_GUESTBOOK_DAYS_EXPLICIT'] : $_POST['NOTIFY_GUESTBOOK_DAYS']).',
					`16`	='.($_POST['NOTIFY_CONTEST_DAYS'	] === -1 ? $_POST['NOTIFY_CONTEST_DAYS_EXPLICIT'  ] : $_POST['NOTIFY_CONTEST_DAYS']))
			||	!db_insert('lastnotify','
				INSERT IGNORE INTO lastnotify SET
					USERID	='.$userid.',
					STAMP	='.CURRENTSTAMP)
			) {
				return false;
			}
			foreach ([
				NOTIFY_MSG		=> 'NOTIFY_MSG_DAYS',
				NOTIFY_BUDDY	=> 'NOTIFY_BUDDY_DAYS',
				NOTIFY_GALLERY	=> 'NOTIFY_GALLERY_DAYS',
				NOTIFY_CONTEST	=> 'NOTIFY_CONTEST_DAYS',
			] as $type => $key) {
				if ($_POST[$key] !== 'NULL'
				?	!db_insupd('notifyinfo', "
					INSERT INTO notifyinfo SET
						USERID			= $userid,
						TYPE			= $type,
						SEEN_IDSTAMP	= ".(0+(
						$type === NOTIFY_MSG
					?	db_single('directmessage','SELECT MAX(MESSAGEID) FROM data_db.directmessage')
					:	(	$type === NOTIFY_BUDDY
						?	max(0+db_single('buddy_request','SELECT MAX(CSTAMP) FROM buddy_request WHERE USERID_ACC='.$userid),
								0+db_single('buddy','SELECT MAX(ASTAMP) FROM buddy WHERE USERID_ACC='.$userid)
							)
						:	(	$type === NOTIFY_GALLERY
							?	db_single('gallery','SELECT MAX(PSTAMP) FROM gallery WHERE VISIBLE="yes" AND PSTAMP<'.CURRENTSTAMP)
							:	(	$type === NOTIFY_CONTEST
								?	db_single('contest','SELECT MAX(PSTAMP) FROM contest WHERE ACTIVE=1 AND PSTAMP<'.CURRENTSTAMP)
								:	0
								)
							)
						)
					)).',
						SEEN_STAMP	='.CURRENTSTAMP.',
						MANUAL_STAMP	='.CURRENTSTAMP.'
					ON DUPLICATE KEY UPDATE
						SEEN_IDSTAMP	=LEAST(SEEN_IDSTAMP,VALUES(SEEN_IDSTAMP)),
						SEEN_STAMP	=LEAST(SEEN_STAMP,VALUES(SEEN_STAMP)),
						MANUAL_STAMP	=LEAST(MANUAL_STAMP,VALUES(MANUAL_STAMP))')
				:	!db_delete('notifyinfo','
					DELETE FROM notifyinfo
					WHERE TYPE='.$type.'
					  AND USERID='.$userid)
				) {
					return false;
				}
			}
/*		}*/
		# force recaching:
		get_notify_days($userid, true);
	}
	// FIXME: showing only part is completely broken!
	// booleans
	if (!isset($_POST['THEME_HARD'])) {
		$_POST['THEME_SOFT'] = true;
	}
	foreach (array(
#		'generic',
#			'USER_MENU_TOP',
#		'profile',
			'SHOW_STARS_PERSONAL_AGENDA',
#		'forum',
			'FOLLOW_OPENED_TOPICS',
			'HIDE_STARS_IN_FORUM',
#		'directmessage',
			'HIDE_UNREAD_OUTGOING',
#		'promo',
#			'HIDE_OLD_PROMOS',
#		'profiles',
			'SHOW_USER_ARCHIVE',
			'SHOW_USER_BUDDIES',
			'SHOW_USER_PERSONAL',
#			'SHOW_USER_REPORTS',
			'SHOW_USER_SECRETS',
			'SHOW_USER_SHOOTS',
#			'SHOW_USER_WEBLOG',
			'SHOW_USER_GUESTBOOK',
			'SHOW_USER_FAVOURITES',
			'UNCOLLAPSE_HIDDEN_USER_TEXT',
#		'appearance',
			'THEME_SOFT',
			'SHOW_PROFILE_IMAGES'
	) as $key) {
/*		if (ctype_lower($key)) {
			$part = $key;
			continue;
		}*/
		$setlist[] = $key.'='.(!empty($_POST[$key]) ? '1' : '0');
	}
	$flags = 0;
	if (isset($_POST['FLAGS'])) {
		if (!require_number_array($_POST, 'FLAGS')) {
			return false;
		}
		foreach ($_POST['FLAGS'] as $flg) {
			if (!is_number($flg)) {
				_error('FLAG waarde '.escape_specials($flg).' is niet numeriek!');
				return false;
			}
			$flags |= $flg;
		}
	}
	// others with their default values
	foreach (array(						// min, max, default
		'COMMENTS_PER_PAGE'				=> array(5,		 100,	 30),
		'DMESSAGES_PER_PAGE'			=> array(5,		 200,	 20),
		'MESSAGES_PER_TOPIC'			=> array(5,		1000,	 50),
		'PHOTOS_PER_PAGE'				=> array(5,		 255,	 30),
		'THEME_CONTRAST'				=> array(10,	 100,	100),
		'TOPICS_PER_PAGE'				=> array(5,		 200,	 30),
		'PAGED_COMMENTS_STARTING_FROM'	=> array(10,	5000,	100),
		'HIDE_RATINGS_STARTING_FROM'	=> array(10,	2000,	500),
		'USERLISTS'						=> [0,3,0],
	) as $key => $info) {
		[$min,$max,$default] = $info;

		if (!isset($_POST[$key])) {
			// don't change if key not set
			continue;
		}
		/** @noinspection NotOptimalIfConditionsInspection */
		if (($val = trim($_POST[$key])) === ''
		||	!ctype_digit($val)
		||	($val = (int)$val) < $min
		||	$val > $max
		) {
			$val = $default;
		}
		$setlist[] = $key.'='.$val;
	}
	foreach (array(
		'FONT_SIZE',
		'FONT_TYPE',
		'THEME',
		'COMMENT_PAGE_ORDER',
		'GMAPS'
	) as $key) {
		if (!isset($_POST[$key])) {
			// don't change if key not set
			continue;
		}
		$setlist[] = $key.'="'.addslashes($_POST[$key]).'"';
	}

	if (!isset($_POST['UNCOLLAPSE_COMMENTS'])) {
		$uncollapse = 0;
	} elseif (!require_number_array($_POST, 'UNCOLLAPSE_COMMENTS')) {
		return false;
	} else {
		$uncollapse = 0;
		foreach ($_POST['UNCOLLAPSE_COMMENTS'] as $bit) {
			$uncollapse |= $bit;
		}
	}
	$setlist[] = 'UNCOLLAPSE_COMMENTS='.$uncollapse;

	if (!isset($_POST['SHOW_RATINGS'])) {
		$uncollapse = 0;
	} elseif (!require_number_array($_POST, 'SHOW_RATINGS')) {
		return false;
	} else {
		$uncollapse = 0;
		foreach ($_POST['SHOW_RATINGS'] as $bit) {
			$uncollapse |= $bit;
		}
	}

	$setlist[] = 'SHOW_RATINGS='.$uncollapse;
  	$setlist[] = 'FLAGS='.($flags ?: 0);
	$setlist[] = 'MSTAMP='.CURRENTSTAMP;

	foreach ([
		'GUESTBOOK'		=> NOBODY,
		'WRITEGUESTBOOK'	=> SELF,
		'AGENDA'		=> SELF,
		'STATISTICS'		=> SELF,
		'BUDDIES'		=> SELF,
		'FAVOURITES'		=> SELF,
		'APPEARANCES'		=> SELF,
#		'DIAGRAM'		=> SELF,
		'PROFILE'		=> SELF,
	] as $name => $maxvisi) {
		if (!require_visibility($_POST,$name,$maxvisi)) {
			return false;
		}
		$esetlist[] = 'VISIBILITY_'.$name.'='.$_POST['VISIBILITY_'.$name];
	}
	# $esetlist[] = 'WEBLOG_VISIBLE='.(isset($_POST['WEBLOG_VISIBLE']) ? 1 : 0);
	$esetlist[] = 'FLOCKINVITE="'.$_POST['FLOCKINVITE'].'"';

	if (!db_insupd('settings','
		INSERT INTO settings SET USERID='.$userid.','.implode(',',$setlist).'
		ON DUPLICATE KEY UPDATE '.implode(',',$setlist))
	) {
		return false;
	}
	if (db_affected()) {
		flush_settings($userid);
	}
	$esetliststr = implode(',',$esetlist);
	if (!db_insupd('externalsettings','
		INSERT INTO externalsettings
		SET USERID='.$userid.','.$esetliststr.'
		ON DUPLICATE KEY UPDATE '.$esetliststr)
	) {
		return false;
	}
	if (db_affected()) {
		flush_external_settings($userid);
	}
	if (!db_update('user_account','
		UPDATE user_account SET VISIBILITY_ONLINE='.$_POST['VISIBILITY_ONLINE'].'
		WHERE USERID='.$userid)
	) {
		return false;
	}
	if (db_affected()) {
		flush_user($userid);
	}
	register_notice('settings:notice:changed_LINE');
	return true;
}

function register_banned_creation(string $type, ?array $userids = null, bool $temp = true) {
	$id = 0;
	if (db_insert('bannedcreation','
		INSERT INTO bannedcreation SET
			STAMP	='.CURRENTSTAMP.',
			TYPE	="'.$type.'",
			NICK	="'.addslashes(win1252_to_utf8($_POST['NICK'])).'",
			EMAIL	="'.addslashes($_POST['EMAIL']).'",
			IPBIN	="'.addslashes(CURRENTIPBIN).'",
			IDENTID	='.CURRENTIDENTID)
	) {
		$id = db_insert_id();
		if ($userids) {
			$vals = [];
			foreach ($userids as $userid) {
				$vals[$userid] = '('.$id.', '.$userid.')';
			}
			db_insert('bannedcreation_ref', '
			INSERT INTO bannedcreation_ref (ID, USERID)
			VALUES '.implode(',', $vals)
			);
		}
	}
	register_error('user:error:'.($temp ? 'temporarily' : 'permanently').'_blacklisted_TEXT', DO_NL2BR, ['ID' => $id]);
}
