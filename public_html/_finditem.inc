<?php

function show_find_item(array $args): bool {
	static $__default_args = [
		'class'			=> null,
		'element'		=> null,	# NOTE: only 'organization' supported now
		'id'			=> null,	# HTML id for the text input field
		'id_name'		=> null,	# Database field name for the ID
		'multi_input'	=> false,
		'onchange'		=> null,
		'placeholder'	=> null,
		'selected'		=> null,
	];
	/** @noinspection PhpRedundantOptionalArgumentInspection */
	extract([...$__default_args, ...$args], \EXTR_OVERWRITE);
	include_js('js/form/finditem');
	include_js('js/form/uplite');
	$store_id = uniqid('find_store_', true);
	if (!$id_name) {
		$id_name = strtoupper($element).'ID';
		if ($multi_input) {
			$id_name .= '[]';
		}
	}
	if ($selected) {
		if (is_array($selected)) {
			$selected = (isset($selected[0]) ? implode(...) : implodekeys(...))(', ', $selected);
		}
		if (false === ($names = db_simple_hash($element, "
			SELECT {$element}ID, REPLACE(NAME, ',', ' ')
			FROM $element
			WHERE {$element}ID IN (".(is_array($selected) ? implode(', ', $selected) : $selected).')'))
		) {
			return false;
		}
		if ($multi_input) {
			$value = implode(', ', $names).', ';
		} else {
			$value = reset($names);
		}
	} else {
		$value = null;
	}
	if (!$onchange) {
		$onchange = 'null';
	}
	$input_args = [
		'class'				=> $class,
		'data-element'		=> $element,
		'data-id-name'		=> $id_name,
		'data-multi-input'	=> !empty($multi_input),
		'data-selector'		=> ($multi_input ? 'checkbox' : 'radio'),
		'data-storeid'		=> $store_id,
		'id'				=> $id ?? null,
		# 'name'			=> $name ?: strtoupper($element).'_NAME',
		'onblur'			=> $multi_input ? /** @lang JavaScript */ "Pf_addSeparator(this, ', ');" : '',
		'onchange'			=> /** @lang JavaScript */ "Pf_findItem(this, $onchange);",
		'onkeyup'			=> /** @lang JavaScript */ 'Pf_delayedFindItem(this);',
		'placeholder'		=> $placeholder,
		'type'				=> 'text',
		'value_utf8'		=> $value,
	];
	foreach ($args as $arg => $val) {
		if (!isset($input_args[$arg])
		&&	str_starts_with($arg, 'data-')
		) {
			$input_args[$arg] = $val;
		}
	}
	show_input($input_args);
	?><div<?
	?> class="findresults"<?
	?> id="<?= $store_id ?>"><?
	if (!empty($names)) {
		foreach ($names as $id => $name) {
			# duplicate of display routine in js/form/finditem
			?><div><?
			?><label<?
			?> class="basehl cbi bold-hilited"<?
			?> onclick="Pf_clickFindItem(event, '<?= $element ?>', <?= $id ?>);"><?
			show_input([
				'checked'	=> true,
				'class'		=> 'upLite',
				'name'		=> $id_name,
				'type'		=> $multi_input ? 'checkbox' : 'radio',
				'value'		=> $id,
			])
			?> <?
			echo escape_utf8($name);
			$cityid = 0;
			$countryid = 0;
			switch ($element) {
			case 'city':
				$cityid = $id;
				$countryid = memcached_single('city', 'SELECT COUNTRYID FROM city WHERE CITYID = '.$cityid);
				break;

			case 'organization':
				if ($organization = memcached_single_array('organization', 'SELECT CITYID, COUNTRYID FROM organization WHERE ORGANIZATIONID = '.$id)) {
					[$cityid, $countryid] = $organization;
				}
				break;
			}
			if ($cityid) {
				?><span class="light6 nb">, <?= escape_utf8(get_element_title('city', $cityid)) ?></span><?
			}
			if ($countryid) {
				?><span class="light6 nb">, <?= __('country:'.$countryid) ?></span><?
			}
			?></label><?
			?></div><?
		}
	} elseif (!$multi_input) {
		?><input type="hidden" name="<?= $id_name ?>" value="0" /><?
	}
	?></div><?

	return true;
}
