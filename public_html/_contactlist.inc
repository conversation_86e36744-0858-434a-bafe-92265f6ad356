<?php

class _contactlist { // NOSONAR
	private array		 $tickets		 = [];
	private array		 $locks			 = [];
	private ?array		 $wheres		 = null;
	private ?string		 $order_by		 = null;
	private bool		 $for_me		 = false;
	private int			 $total			 = 0;
	private array		 $shown			 = [];
	private ?string		 $uniqid		 = null;
	private ?string	 	 $single_element = null;
	private bool		 $above_element	 = false;
	private ?string		 $in_forum		 = null;
	private int|bool	 $only_link		 = false;
	private contactstars $contactstars;
	private bool		 $as_admin		 = false;
	private int			 $with_user		 = 0;
	private array		 $subjects		 = [];
	private string		 $with_body;
	private int			 $column_count;
	// in:
	public bool			 $admin_list	 = false;
	public bool 		 $show_withuser	 = true;
#	public bool			 $show_readm	 = false;
	public bool			 $show_groups	 = false;
	public bool			 $hide_header	 = false;
	public string 		 $box_class		 = 'contact';
	public bool			 $collapse		 = false;

	final public function query(?array $arg = null) : void {
		if ($arg) {
			$this->tickets       = $arg;
			$this->above_element = true;
		} else {
			require_once '_lock.inc';
			$joins    = [];
			$distinct = '';
			$selects  = ["
				TICKETID,
				(	SELECT COUNT(*)
					FROM contact_ticket_attachment AS cta
					JOIN contact_ticket_message AS catt USING (CTMSGID)
					WHERE CONTENTID = ''
					  AND FILENAME != 'message.html'
					  AND catt.TICKETID = contact_ticket.TICKETID
				) AS ATTCNT,
				contact_ticket.MSTAMP,
				contact_ticket.CSTAMP, contact_ticket.OUTGOING,
				contact_ticket.LSTAMP, OWNERID, contact_ticket.SUBJECT AS TSUBJ,
				contact_ticket.STATUS, ELEMENT, contact_ticket.ID, contact_ticket.ISMAIL, LASTMAIL, WITHUSERID, WITHEMAIL, MSGCNT, LASTDIRECTION,
				(	SELECT CONCAT(CTMSGID, '\t', FROM_EMAIL, '\t', FROM_NAME)
					FROM contact_ticket_message AS ctt
					JOIN contact_ticket_mail USING (CTMSGID)
					WHERE ctt.TICKETID = contact_ticket.TICKETID
					  AND DIRECTION = 'forwardinfo'
					ORDER BY CTMSGID DESC
					LIMIT 1
				) AS FORWARD_INFO"];

			if ($this->admin_list) {
				$this->wheres[] = 'WITHUSERID != '.CURRENTUSERID;
				$tmp_wheres     = $this->wheres;
				$tmp_wheres[]   = "contact_ticket.STATUS = 'open'";
				$this->inner_query($selects, $joins, $tmp_wheres, $distinct);
				$tmp_wheres   = $this->wheres;
				$tmp_wheres[] = 'TRACKIT';
				$this->inner_query($selects, $joins, $tmp_wheres, $distinct);

			} elseif ($this->as_admin) {
				$joins['contact_ticket_message'] = 'JOIN contact_ticket_message AS ctm USING (TICKETID)';
				$this->order_by                  = ' LSTAMP DESC ';
				$this->wheres[]                  = "ctm.USERID_FROM = $this->as_admin AND ctm.DIRECTION = 'touser'";
				$this->inner_query($selects, $joins, $this->wheres, 'DISTINCT ', limit: ' LIMIT 1000');

			} elseif ($this->with_body) {
				$joins['contact_ticket_message'] = 'JOIN contact_ticket_message USING (TICKETID)';

				# @ does not work in query string: it selects specifiek field (@body for example), so email addresses cannot be search this way

				$mask = db_single('sphinx_element', "SELECT MASK FROM party_db.sphinx_element WHERE ELEMENT = 'contact_ticket_message'");

				$joins['sphinxsearch'] = 'JOIN sphinxsearch AS ss ON ss.ID & ~'.$mask.' = CTMSGID';
				$this->wheres[]        = 'QUERY=\''.($q = str_replace('\\"', '"', addslashes($this->with_body))).';'.'mode=extended;'.'index=contact_ticket_messages,new_contact_ticket_messages;'.'limit=100;'.'sort=extended:@id desc\'';

				static $sphinx_snippets = static function(string $index) use ($q) {
					return "SPHINX_SNIPPETS(
							BODY,
							'$index',
							'$q',
							1 as query_mode,
							1 as exact_phrase,
							200 as limit_words,
							'[hilite]'  as before_match,
							'[/hilite]' as after_match
					)";
				};

				$selects[] = 'COALESCE('.$sphinx_snippets('new_contact_ticket_messages').','.$sphinx_snippets('contact_ticket_messages').') AS SNIP';

				$this->snip = true;

				$this->inner_query($selects, $joins, $this->wheres, 'DISTINCT ', limit: 'LIMIT 100');

			} elseif (
				!isset($this->with_user)
			&&	!isset($this->with_email)
			) {
				$this->inner_query($selects, $joins, $this->wheres, $distinct);
			} else {
				if (isset($this->with_user)) {
					$tmp_wheres   = $this->wheres;
					$tmp_wheres[] = 'WITHUSERID = '.$this->with_user;
					$this->inner_query($selects, $joins, $tmp_wheres, $distinct);
				}
				if (isset($this->with_email)) {
					$email = $this->with_email;
					$distinct = 'DISTINCT ';
					$joins['contact_ticket_mail'] = 'JOIN contact_ticket_mail USING (TICKETID)';
					$tmp_wheres = $this->wheres;
					$not_an_email = !str_contains($email, '@');
					$prefix       = $not_an_email ? false : preg_match('".+@$"', $email);
					$ticketids = db_simpler_array('mailinglist', '
						SELECT TICKETID
						FROM mailinglist
						WHERE EMAIL'.(
							$prefix
						?	' LIKE "'.addslashes($email).'%"'
						:	(	$email[0] === '@'
							?	' LIKE "%'
							:	' = "'
							).addslashes($email).'"')
					);
					$tmp_wheres[] =
						($ticketids ? '(ICKETID IN ('.implode(',', $ticketids).') OR ' : '').
						(	$not_an_email
						?	'FROM_NAME LIKE "'.addslashes($email).'%"'
						:	(	$prefix
							?	'(FROM_EMAIL LIKE "'.addslashes($email).'%"
								OR  TO_EMAIL LIKE "'.addslashes($email).'%")'
							:	'(FROM_EMAIL'.($email[0] === '@' ? ' LIKE "%' : '="').addslashes($email).'"
								OR	TO_EMAIL'.($email[0] === '@' ? ' LIKE "%' : '="').addslashes($email).'")'
							)
						).
						($ticketids ? ')' : '');

					$this->inner_query($selects, $joins, $tmp_wheres, $distinct);
				}
			}
			if (!$this->tickets
			||	false === ($locks = db_simple_hash('locks', '
				SELECT LOCK_ID, LOCK_LOCKERID
				FROM locks
				WHERE LOCK_TYPE = '.LOCK_TICKET.'
				  AND LOCK_EXPIRES > '.(CURRENTSTAMP - __get_lock_time(LOCK_TICKET))))
			) {
				return;
			}
			$this->locks = $locks;
			if ($this->only_link
			&&	$this->only_link !== true
			) {
				$foundme = false;
			}
			$element_names = [];
			$info = [];
			foreach ($this->tickets as $ticket) {
				$info[$ticket['OWNERID']][$element = $ticket['ELEMENT']][$ticket['ID']][] = $ticket;

				if (!isset($element_names[$element])) {
					$element_names[$element] = $element === 'party' ? Eelement_name('agenda') : Eelement_plural_name($element);
				}
				if (isset($foundme)
				&&	!$foundme
				&&	$ticket['WITHUSERID'] === CURRENTUSERID
				&&	have_user()
				) {
					$foundme = true;
				}
			}
			$this->elements_names = $element_names;
			if (isset($foundme)
			&& 	!$foundme
			) {
				$this->only_link = true;
			}
			$this->tickets = [];
			$reversed      = isset($this->reversed) ? -1 : 1;
			foreach ($info as  /* $ownerid => */ &$more_info) {
				uksort($more_info, static function(string $a, string $b) use ($element_names): int {
					static $__order = ['bug' => -1, 'development' => 1];
					return (($__order[$a] ?? 0) <=> ($__order[$b] ?? 0))
						?:	strcasecmp($element_names[$a], $element_names[$b]);
				});
				foreach ($more_info as /* $element => */ $id_list) {
					$new_id_list = [];
					foreach ($id_list as $id => $tickets) {
						if ($id) {
							$new_id_list[] = count($tickets) > 1 ? $tickets : $tickets[0];
						} else {
							$new_id_list += $tickets;
						}
					}
					usort($new_id_list, static fn(array $a, array $b): int =>
						$reversed * (($a['LSTAMP'] ?? $a[0]['LSTAMP']) - ($b['LSTAMP'] ?? $b[0]['LSTAMP']))
					);
					foreach ($new_id_list as $content) {
						if (isset($content[0])) {
							$this->tickets = [...$this->tickets, ...$content];
							#foreach ($content as $ticket) {
							#	$this->tickets[] = $ticket;
							#}
						} else {
							$this->tickets[] = $content;
						}
					}
				}
			}
			unset($more_info);
			if ($this->admin_list) {
				$sgroups = memcached_boolean_hash('ticketgroup', 'SELECT ELEMENT FROM ticketgroup WHERE USERID = '.CURRENTUSERID);
				if ($sgroups && have_admin(array_keys($sgroups))) {
					// reorder tickets
					$normals  = [];
					$specials = [];
					$owned    = [];
					foreach ($this->tickets as &$ticket) {
						if ($ticket['OWNERID']) {
							if ($ticket['OWNERID'] === CURRENTUSERID) {
								$owned[] = $ticket;
							} else {
								$normals[] = $ticket;
							}
							#	$withuser[$ticket['OWNERID']] = $ticket['WITHUSERID'];
						} elseif (isset($sgroups[$ticket['ELEMENT']])) {
							$ticket['SPECIAL'] = true;
							$specials[]        = $ticket;
						} else {
							$normals[] = $ticket;
						}
					}
					unset($ticket);
					$this->tickets = $owned + $specials + $normals;
				}
			}
			// topics in forum
//			if ($this->in_forum
//			&&	!empty($topicids)
//			) {
//				$this->forumid = memcached_simple_hash('topic', '
//					SELECT TOPICID, FORUMID
//					FROM topic
//					WHERE TOPICID IN ('.implode(', ', $topicids).')');
//			}

		}
#		require_once '_ticketvote.inc';
		$mailtickets = [];
		foreach ($this->tickets as &$ticket) {
			if ($ticket['WITHUSERID']) {
				$withuserids[$ticket['WITHUSERID']] = true;
			}
			if ($ticket['ISMAIL']
			||	$ticket['LASTMAIL']
			) {
				if ($ticket['WITHUSERID']) {
					$ticket['ISUSERS'][$ticket['WITHUSERID']] = true;
				}
				require_once '_contactmailoptions.inc';
				if ($ticket['MAILOPTIONS'] = get_contact_mail_options($ticket['TICKETID'])) {
					foreach ($ticket['MAILOPTIONS'] as $mailoption) {
						if (!$mailoption['EMAIL']
						||	$mailoption['FLOCK_FORWARD']
						) {
							continue;
						}
						if ($users = get_users_with_email($mailoption['EMAIL'])) {
							foreach ($users as $user) {
								$ticket['ISUSERS'][$user['USERID']] = true;
								$withuserids[$user['USERID']]       = true;
							}
						}
					}
				}
				$mailtickets[] = $ticket['TICKETID'];
			}
		}
		unset($ticket);
		if ($mailtickets
		&&	($local_subjects = memcached_simple_hash('contact_ticket_mail', '
				SELECT TICKETID, SUBJECT
				FROM contact_ticket_mail
				JOIN (	SELECT TICKETID, MAX(CTMSGID) AS CTMSGID
						FROM contact_ticket_mail
						WHERE TICKETID IN ('.implode(',', $mailtickets).")
						  AND SUBJECT != ''
						  AND CIMID = 0
						GROUP BY TICKETID
				) AS maxCTMSGID USING (TICKETID, CTMSGID)"))
		) {
			$this->subjects = $local_subjects;
		}
		// withuserids, working for organizations
		if (isset($withuserids)) {
			require_once '_contactstars.inc';
			$this->contactstars = new contactstars($withuserids);
		}
	}

	final public function get_ticketids() : array {
		if (!$this->tickets) {
			return [];
		}
		return array_column($this->tickets, 'TICKETID');
	}

	private function inner_query(
		array $selects,
		array $joins,
		array $wheres,
		string $distinct = '',
		string $limit	 = '',
	) : void {
		if ($joins) {
			$tables   = array_keys($joins);
			$tables[] = 'contact_ticket';
		} else {
			$tables = 'contact_ticket';
		}
		if (!isset($this->allow_junk)) {
			$wheres[] = "(
				   OWNERID != 0
				OR ELEMENT NOT IN (
						'junk_failure',
						'junk_mailinglist',
						'junk_nolearn',
						'junk_recognize',
						'junk_spam')
				)";
		}
		if (!SERVER_SANDBOX
		&&	(	isset($this->with_email)
			|| isset($this->with_body))
		) {
			$flags = DB_FORCE_SERVER;
			$arg   = ['dbsrch', 'dbsrchrep'];
		} else {
			$flags = 0;
			$arg   = null;
		}
		$order_prefix = isset($this->with_body) ? 'ss.ID DESC,' : '';

		if ($local_tickets = db_rowuse_hash($tables, '
			SELECT '.$distinct.implode(', ', $selects).'
			FROM contact_ticket '
			.($joins ? implode(' ', $joins) : '').
			($wheres ? ' WHERE '.implode(' AND ', $wheres) : '').
			(isset($this->with_status) ? ' AND contact_ticket.STATUS="'.$this->with_status.'"' : '').
			(isset($this->order_by) ? ' ORDER BY '.$order_prefix.$this->order_by : (
				$this->show_groups
				? "	ORDER BY {$order_prefix}OWNERID = ".CURRENTUSERID." DESC,
					OWNERID = 0 DESC,
					(SELECT NICK FROM user_account WHERE USERID = OWNERID),
					ELEMENT IN ('junk_failure', 'junk_mailinglist', 'junk_nolearn', 'junk_recognize', 'junk_spam', 'development'),
					ELEMENT = 'bug' DESC,
					ELEMENT,
					ID,
					LSTAMP"
				: " ORDER BY {$order_prefix}ELEMENT, ID, LSTAMP ")).
			$limit,
			$flags,
			$arg)
		) {
			$this->tickets += $local_tickets;
			$this->total   += count($local_tickets);
		}
	}
//	final public function shown_tickets() : bool {
//		return !empty($this->shown);
//	}
	final public function have_tickets() : bool {
		return $this->total > 0;
	}
	final public function get_ticket_total() : int {
		return $this->total;
	}
	final public function clear() : void {
		$this->tickets = [];
		$this->wheres  = [];
	}
	final public function display() : void {
		if (!$this->tickets) {
			return;
		}
		include_js('js/contactticketlist');

		$ownerid		= null;
		$element		= null;
		$interest_group = false;
		$previd			= null;
		$outgoing		= null;
		$table_opened	= false;

		# wide:			small:
		# 1. MSGCNT		1. MSGCNT
		# 2. ISMAIL		2. ISMAIL
		# 3. WITH		3. SUBJECT + WITH
		# 4. SUBJECT	4. STATUS?
		# 5. STATUS?	5. DATE
		# 6. DATE

		$this->column_count = (SMALL_SCREEN ? 4 : 5) + (isset($this->show_status) ? 1 : 0);

		require_once '_ticketlist.inc';
		foreach ($this->tickets as $ndx => $ticket) {
			if ($ndx === 'CNT'
			#	^ ??
			||	(isset($ticket['ACCESSIBLE']) ? !$ticket['ACCESSIBLE'] : !ticket_accessible($ticket, $this->for_me))

			||	isset($this->only_me)
			&&	$ticket['WITHUSERID'] !== CURRENTUSERID
			) {
				continue;
			}
			if (!$this->single_element) {
				if (isset($interest_group)) {
					if (!empty($ticket['SPECIAL'])) {
						$interest_group = true;
					} elseif ($interest_group) {
						if (!$ticket['OWNERID']) {
							$this->close_table();
							layout_close_box();
							layout_open_box($this->box_class);
							layout_box_header(__C('contact_ticket:all_not_owned_tickets'));
							$table_opened = true;
							$this->open_table();
						}
						$interest_group = null;
					}
				}
				if ($ownerid === null) {
					$element = null;
					layout_open_box($this->box_class);
					if (isset($this->header)) {
						if ($this->collapse) {
							ob_start();
							expand_collapse($this->uniqid = uniqid('tickets_', true));
							$this->collapse = ob_get_clean();
							?><div class="ptr" onclick="<?
							expand_collapse_clicker($this->uniqid) ?>"><?
						}
						layout_box_header($this->header, $this->collapse);
						if ($this->collapse) {
							?></div><?
						}
					}
					$table_opened = true;
					$this->open_table();
					// $ownerid = true;
				} elseif ($ownerid !== $ticket['OWNERID']) {
					$this->close_table();
					layout_close_box();

					$element = null;
					layout_open_box($this->box_class);
					if (!$this->hide_header) {
						layout_box_header(
							$ticket['OWNERID']
						?	(	!isset($this->name_inbox)
							&&	$ticket['OWNERID'] === CURRENTUSERID
							?	 __C('contactlist:inbox')
							:	get_element_link('user', $ticket['OWNERID'])
							)
						:	(	!empty($ticket['SPECIAL'])
							?	Eelement_plural_name('interest_group')
							:	__C('contact_ticket:all_not_owned_tickets')
							)
						);

					}
					$table_opened = true;
					$this->open_table();
				}
			}
			if ($this->single_element
			&&	$outgoing !== $ticket['OUTGOING']
			||	$element !== $ticket['ELEMENT']
			) {
				if ($this->single_element
				&&	$element
				) {
					$this->close_table();
					layout_close_box();
				}
				$element  = $ticket['ELEMENT'];
				// $outgoing = $ticket['OUTGOING'];
				$previd   = 0;

				if ($this->single_element) {
					layout_open_box($this->box_class);
					$table_opened = true;
					$this->open_table();
				}
				if (isset($this->snip)) {
					?><tbody class="nohl"><?
				}
				?><tr class="nohl panther"><?
				?><th class="msg-cnt"><?
				$outgoing = $ticket['OUTGOING'];
				if ($this->above_element) {
					show_incoming_outgoing($outgoing);
				}
				?></th><?
				?><th class="msg-type"></th><?
				?><th colspan="<?= $this->column_count - 2 ?>"><?
				if (isset($this->elements_names[$element])) {
					echo $this->elements_names[$element];
				} elseif ($element === 'party') {
					echo Eelement_name('agenda');
				} else {
					echo Eelement_plural_name($element);
				}
				?></th><?
				?></tr><?
				if (isset($this->snip)) {
					?></tbody><?
				}
			}
			$this->shown[] = $ticket['TICKETID'];
			if ($this->display_row($ticket, $previd)) {
				$previd = $ticket['ID'];
			}
			$ownerid = $ticket['OWNERID'];
		}
		if ($table_opened) {
			$this->close_table();
			layout_close_box();
		}
	}
	private function open_table() : void {
		$this->shown = [];
		ob_start();
	}
	private function close_table() : void {
		if (!($data = ob_get_clean())) {
			return;
		}
		$classes = ['default fw vtop'];
		if (SMALL_SCREEN) {
			$classes[] = 'vttop';
		}
		if (isset($this->snip)) {
			$classes[] = 'brdgrp';
		}
		if (isset($this->check_states)) {
			?><div onmouseenter="Pf.checkTicketStates(this);" data-ticketids="<?= implode(',', $this->shown) ?>"><?
		}
		layout_open_table(implode(' ', $classes), $this->uniqid, $this->uniqid ? 'hidden' : null);
		echo $data;
		?></table><?
		if (isset($this->check_states)) {
			?></div><?
		}
	}

	final public function display_row(array $ticket, ?int $previd): bool {
		$class_list = null;
		$ticketid   = $ticket['TICKETID'];
		$withuserid = $ticket['WITHUSERID'];
		$status     = $ticket['STATUS'];
		$showlink   =	   !$this->only_link
						||	$this->only_link !== true
						&&	$withuserid === $this->only_link;
		if (!($withu = !isset($this->not_self) && (CURRENTUSERID === $withuserid))
		&&	!$this->only_link
		) {
			if ($this->in_forum
			&&	(	$ticket['ELEMENT'] !== 'topic'
				||	!$ticket['ID']
				||	empty($this->forumid[$ticket['ID']])
				||	$this->forumid[$ticket['ID']] !== $this->in_forum)
			) {
				return false;
			}
			$lock_userid	   = getifset($this->locks, $ticketid);
			$locked_but_not_me = $lock_userid && $lock_userid !== CURRENTUSERID;
			if ($lock_userid === CURRENTUSERID) {
				$class_list[] = 'ticket-locked-by-me';
			}
			$old_ticket = $status !== 'closed' && $ticket['LSTAMP'] < CURRENTSTAMP - ONE_WEEK;

			if ($locked_but_not_me) {
				$class_list[] = 'locked-item';
			}
			unset($unusable);
			$sameasprev =	$previd
						&&	$previd === $ticket['ID']
						&&	(!$this->above_element
						||	$ticket['ELEMENT'] !== $_REQUEST['sELEMENT']
						||	$ticket['ID'] !== $_REQUEST['sID']);
		}
		$snip = isset($this->snip);
		?><<?
		echo $snip ? 'tbody' : 'tr';
		if ($showlink) {
			$class_list[] = 'ptr hh';
			?> data-ticketid="<?= $ticketid ?>"<?
			?> onclick="openLink(event, '/ticket/<?= $ticketid ?>', true)"<?
		}
		if (!is_aspiring_content_admin()
		&&	is_aspiring_content_admin($ticket['WITHUSERID'])
		) {
			$class_list[] = 'small light';
		}
		if ($class_list) {
			?> class="<?= implode(' ', $class_list) ?>"<?
		}
		?>><?
		if ($snip) {
			?><tr><?
		}
		if ($showlink
		&&	!$withu
		) {
			// MSGCNT
			?><td class="center small light"><?
			if ($ticket['MSGCNT'] > 1) {
				echo $ticket['MSGCNT'];
			}
			?></td><?
		} else {
			?><td></td><?
		}
		// VLOP
		?><td class="hpad relative"><?
		// mailp
		if ($ticket['LASTMAIL']) {
			?><div class="abs at">@</div><?
		}
		?><img<?
		?> alt="ticket <?= $ticket['TICKETID'] ?>"<?
		?> class="mail<?
		if (!$withu
		&&	(	!empty($sameasprev)
			||	!empty($locked_but_not_me))
		) {
			?> light<?
		}
		?>"<?
		?> src="<?= STATIC_HOST ?>/images/<?= !$withu
		&& $ticket['LASTDIRECTION'] === 'toadminfromadmin' ? 'internal_' : null ?>mail<?= is_high_res() ?>.png" /><?
		?></td><?

		// WITH
		if ($status === 'pending'
		||	$status === 'closed'
		) {
			$classes[] = 'ticket-'.$status;
		}
		if (!empty($sameasprev)) {
			$classes[] = 'rpad llpad';
		} else {
			$classes[] = 'rpad';
		}
		?><td class="<?= implode(' ', $classes) ?>"><?

		if (SMALL_SCREEN) {
			ob_start();
		}
		if ($this->show_withuser) {
			if (!empty($sameasprev)) {
				?><small class="light"><?= __('contactlist:about_same_element') ?></small><br /><?
			}
			if ($withu
			&&	!$this->admin_list
			) {
				if (!($otherid = memcached_single('contact_ticket_message', '
					SELECT USERID_FROM
					FROM contact_ticket_message
					WHERE TICKETID='.$ticketid.'
					  AND DIRECTION IN ("touser", "action")
					  AND USERID_FROM!='.$withuserid.'
					ORDER BY CTMSGID DESC
					LIMIT 1'))
				) {
					?>(<?= __('contactlist:not_yet_attended_to') ?>)<?
				} else {
					show_admin_first_name($otherid);
				}
			} elseif (
				$ticket['LASTMAIL']
			||	$ticket['ISMAIL']
			) {
				$with = null;
				if (empty($ticket['MAILOPTIONS'])) {
					mail_log('empty mailoptions for ticket: '.$ticketid);
				}
				if (!($mailoptions = $ticket['MAILOPTIONS'])
				||	(!$ticket['LASTMAIL'] && $withuserid)
				) {
					if ($withuserid) {
						$with = memcached_nick($withuserid);
					}
				} elseif ($mailoptions[0]['NAME']) {
					$with = $mailoptions[0]['NAME'];
					!($with = preg_replace('"(\[[^]]*]|\([^)]*\))"', '', $with) ?: $with);
				} else {
					$with = $mailoptions[0]['EMAIL'];
				}

				if (!$with) {
					echo __('contactlist:with_someone');
				} else {
					require_once '_mimeheader.inc';
					echo str_replace('%NAME%', htmlentities_of_decoded_quoted_printable($with), __('contactlist:with_person', KEEP_EMPTY_KEYWORDS));
				}
				if ($mailoptions
				&&	$withuserid
				) {
					?> <small class="light"><?
					?>(<?=	$ticket['LASTMAIL']
						?	escape_specials(memcached_nick($withuserid))
						:	htmlentities_of_decoded_quoted_printable($mailoptions[0]['NAME'] ?: $mailoptions[0]['EMAIL'])
					?>)</small><?
				}
				if (isset($this->contactstars)
				&&	!empty($ticket['ISUSERS'])
				) {
					foreach ($ticket['ISUSERS'] as $userid => $ignored) {
						$this->contactstars->show_stars($userid);
					}
				}
			} elseif ($withuserid) {
				echo __('contactlist:with_person', ['NAME' => get_element_title('user', $withuserid)]);
				if (isset($this->contactstars)) {
					$this->contactstars->show_stars($withuserid);
				}
			}
		} else {
			echo __('contactlist:with_someone');
		}
		if (SMALL_SCREEN) {
			$withpart = ob_get_clean();
			ob_start();
		} else {
			?></td><td class="hpad"><?
		}
		if ($this->above_element) {
			if ($ticket['TSUBJ']) {
				echo escape_specials($ticket['TSUBJ']);
			} elseif (
				($subject = $this->subjects[$ticketid] ?? null)
			&&	!preg_match('"^https?://"i', $subject)
			) {
				require_once '_contactsubject.inc';
				show_ticket_subject($subject);
			} elseif (
				$ticket['ID']
			&&	$ticket['ID'] !== $_REQUEST['sID']
			&&	$ticket['ELEMENT'] !== $_REQUEST['sELEMENT']
			&&	$showlink
			) {
				require_once '_elementshort.inc';
				show_short_element($ticket['ELEMENT'], $ticket['ID']);
			}
		} else {
			require_once '_elementshort.inc';
			ob_start();
			if ($ticket['ID']) {
				if ($showlink) {
					require_once '_elementshort.inc';
					show_short_element($ticket['ELEMENT'], $ticket['ID']);
				}
			} elseif (
				$showlink
				# show single connect of there is only 1 connect
			&&	($connects = db_rowuse_array('connect', "
				SELECT ASSOCTYPE, ASSOCID
				FROM connect
				WHERE MAINTYPE = 'contact_ticket'
				  AND MAINID = {$ticket['TICKETID']}"))
			&&	count($connects) === 1
			) {
				/** @noinspection PhpRedundantOptionalArgumentInspection */
				extract($connects[0], \EXTR_OVERWRITE);
				show_short_element($ASSOCTYPE, $ASSOCID);
			}
			$elem_subj = ob_get_clean();

			$t_subj = $ticket['TSUBJ'] ? escape_specials($ticket['TSUBJ']) : null;

			$mail_subj = null;

			if ($subject = $this->subjects[$ticketid] ?? null) {
				require_once '_contactsubject.inc';
				ob_start();
				show_ticket_subject($subject, $nolink);
				$mail_subj = ob_get_clean();
				if ($elem_subj
				&&	isset(USE_URLTITLE[$ticket['ELEMENT']])
				&& (	$ticket['ID']
					&&	($title = get_element_title($ticket['ELEMENT'], $ticket['ID']))
					&&	similar_text($nolink, $title, $pct)
					&&	$pct >= 80
					||	similar_text($nolink,
						preg_replace(
							'"<.*>|'.MIDDLE_DOT_ENTITY.'"',
							'',
							$elem_subj
						), $pct)
					&&	$pct >= 80)
				) {
					$mail_subj = null;
				}
			}
			if ($t_subj) {
				echo $t_subj;
			} elseif ($elem_subj) {
				echo $elem_subj;
				if ($mail_subj) {
					?>, <small><?= $mail_subj ?></small><?
				}
			} else {
				echo $mail_subj;
			}
		}
		if (SMALL_SCREEN) {
			$subj = ob_get_clean();
			?><div><em class="small"><?=
				/** @noinspection PhpUndefinedVariableInspection */
				$withpart
			?></em></div><?
			if ($subj) {
				?><div><?
				echo $subj;
				if (!empty($ticket['ATTCNT'])) {
					?><span class="light"> <?= PAPERCLIP_ENTITY ?><?
					?><small><?= $ticket['ATTCNT'] ?></small></span><?
				}
				?></div><?
			}
		} elseif (!empty($ticket['ATTCNT'])) {
			?><span class="light"> <?= PAPERCLIP_ENTITY ?><?
			?><small><?= $ticket['ATTCNT'] ?></small></span><?
		}

		if ($ticket['FORWARD_INFO']) {
			[/* $f_ctmsgid */, $f_email, $f_name] = explode("\t", $ticket['FORWARD_INFO']);

			?><br /><span class="small notice-nb"><?= __('contactlist:forwarded_src') ?> <strong><?= escape_specials($f_name ?: $f_email) ?></strong></span><?
		}
		?></td><?

		// STATUS
		if (isset($this->show_status)) {
			static $__ticketstatus;
			?><td class="ticket-<?= $status ?>"><?= $__ticketstatus[$status] ??= __('contactlist:ticketstatus:'.$status, DO_NBSP) ?></td><?
		}
		?><td class="nowrap small right ticket-<?= $status ?>"><?

		$stamp = $ticket[$this->stamp_field ?? 'LSTAMP'];

		_date_printnice((SMALL_SCREEN ? PRINTNICE_NUMERIC : 0) | PRINTNICE_DATE_TIME | PRINTNICE_SHORT, $stamp);
		// AGE
		if (!empty($old_ticket)
		&&	$ticket['ELEMENT'] !== 'development'
		&&	$ticket['ELEMENT'] !== 'bug'
		) {
			$total_days = floor((CURRENTSTAMP - $ticket['LSTAMP']) / ONE_HOUR) + 1;
			$weeks      = floor($total_days / 7);

			?>: <span class="nowrap ticket-<?
			if ($total_days < 3) {
				?>old<?
			} elseif ($total_days < 6) {
				?>older<?
			} elseif ($total_days < 9) {
				?>oldest<?
			} elseif ($total_days < 12) {
				?>elder<?
			} else {
				?>ancient<?
			}
			?>"><?
			echo $weeks, 'w';
			/*			if ($days) {
							echo '+',$days,'d';
						}*/ ?> <?= __('contactlist:old') ?></span><?
		}
		?></td><?
		?></tr><?
		if ($snip) {
			?><tr><?
			?><td class="msg-cnt"></td><?
			?><td class="msg-type"></td><?
			?><td class="small light6" style="padding: 0 2em;" colspan="<?= $this->column_count - 2 ?>"><?= make_all_html($ticket['SNIP']) ?></td><?
			?></tr><?
		}
		return true;
	}
//	final public function created_by(int $userid): void {
//		$this->wheres[] = 'contact_ticket.USERID = '.$userid;
//	}
	final public function with_user(int $userid) : void {
		$this->with_user = $userid;
	}
	final public function as_admin(int $userid) : void {
		$this->as_admin = $userid;
	}
	final public function with_email(string $email, bool $exact = false) : void {
		$this->with_email  = $email;
		$this->email_exact = $exact;
	}
//	final public function non_closed() : void {
//		$this->wheres[] = 'contact_ticket.STATUS != "closed"';
//	}
//	final public function only_closed() : void {
//		$this->wheres[] = 'contact_ticket.STATUS = "closed"';
//	}
//	final function closed_or_read() : void {
//		$this->wheres[] = '(contact_ticket.STATUS="closed" OR contact_ticket.READM=1)';
//	}
//	final function in_year_and_month(string $STAMP_field, int $year, int $month): void {
//		if ($month === 12) {
//			$endyear = $year + 1;
//			$endmonth = 1;
//		} else {
//			$endyear = $year;
//			$endmonth = $month + 1;
//		}
//		$this->wheres[] =
//			'contact_ticket.'.$STAMP_field.' BETWEEN '.
//			mktime(0, 0, 0, $month, 1, $year).
//			' AND '.
//			(mktime(0, 0, 0, $endmonth, 1, $endyear) - 1);
//
//		$this->order_by = '
//			IF(OWNERID = '.CURRENTUSERID.', 0, 1) ASC,
//			(SELECT NICK FROM user_account WHERE USERID = OWNERID) ASC,
//			ELEMENT,
//			contact_ticket.'.$STAMP_field;
//
//		$this->stamp_field = $STAMP_field;
//	}
	final public function for_me() : void {
		if (!($elems = _contact_construct_element_list())) {
			$this->wheres[] = 'OWNERID = '.CURRENTUSERID;
			return;
		}
		if (!have_admin('contact_ticket_all_inbox')) {
			$this->wheres[] = '(
					(	contact_ticket.ELEMENT IN ('.stringsimplode(',', $elems).')
					OR	contact_ticket.ELEMENT LIKE "junk_%"
					)
				AND OWNERID IN (0, '.CURRENTUSERID.')
			)';
		}
		$this->for_me = true;
	}
	final public function only_mine() : void {
		$this->only_mine = true;
		$this->wheres[]  = 'OWNERID = '.CURRENTUSERID;
	}
//	final public function only_unread_and_open() : void {
//		$this->wheres[] = '(READM = 0 OR contact_ticket.STATUS = "open")';
//	}
	final public function not_closed_and_read() : void {
		$this->wheres[] = '!(READM AND contact_ticket.STATUS = "closed")';
	}
	final public function useful_for_nonadmin() : void {
		$this->as_user = true;
#		$this->wheres[] = '(contact_ticket.ELEMENT!="development" OR contact_ticket.STATUS="pending" OR contact_ticket.READM=0)';
	}
//	final public function only_unread_and_pending() : void {
//		$this->wheres[] = '(READM = 0 OR contact_ticket.STATUS = "pending")';
//	}
	final public function open_and_pending() : void {
		$this->wheres[] = 'contact_ticket.STATUS IN ("open", "pending", "delayed")';
	}
//	final public function only_open() : void {
//		$this->wheres[] = 'contact_ticket.STATUS = "open"';
//	}
	final public function with_status(string $status) : void {
		static $__doneprogress = false;
		switch ($status) {
		case 'open_progress':
			$__doneprogress = true;
			$status         = 'open';
			$this->wheres[] = 'READM = 0';
			break;
		case 'open':
			if ($__doneprogress) {
				$this->wheres[] = 'READM = 1';
			}
			break;
		}
		$this->with_status = $status;
	}
	final public function only_pending() : void {
		$this->wheres[] = 'contact_ticket.STATUS = "pending"';
	}
	final public function single_element(bool $set = true) : void {
		$this->single_element = $set;
	}
	final public function element_and_id(string $element, int $id) : void {
		$this->single_element = true;
		$this->wheres[] = "ELEMENT = '$element' AND ID = $id";
	}
	final public function elements(array|string $arg) : void {
		if (!is_array($arg)) {
			$arg = [$arg];
		}
		foreach (is_array($arg) ? $arg : [$arg] as $element) {
			if (str_starts_with($element, 'junk')) {
				$this->allow_junk = true;
			}
		}
		$this->wheres[] = 'ELEMENT IN ("'.implode('", "', $arg).'")';
	}
	final public function in_forum(int $forumid) : void {
		$this->in_forum = $forumid;
	}
	final public function only_link_with_currentuser() : void {
		$this->only_link = have_user() ? CURRENTUSERID : true;
	}
	final public function all_old() : void {
		$this->wheres[] = '
				contact_ticket.STATUS = "open"
			AND	contact_ticket.ELEMENT NOT IN (
/*					"development", "bug",*/
					"ad", "newsad", "promo", "banner", "office"
				)
			AND	LSTAMP<'.(TODAYSTAMP - 2 * ONE_WEEK);
	}

	final public function with_body(string $term) : void {
		$this->with_body = $term;
	}
}

function show_incoming_outgoing(bool $outgoing, string $class = 'abs tdir'): void {
	?><img<?
	?> class="<?
	echo $class;
	if (!$outgoing) {
		?> light<?
	}
	?>"<?
	?> src="<?= STATIC_HOST ?>/images/<?= CURRENTTHEME ?>/<?= $outgoing ? 'outgoing' : 'incoming' ?>.png"<?
	?> title="<?= $title = element_plural_name($outgoing ? 'outgoing_contact_ticket' : 'incoming_contact_ticket') ?>"<?
	?> alt="<?= $title ?>" /><?
}
