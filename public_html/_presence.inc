<?php

declare(strict_types=1);

const ELEMENTS_WITH_PRESENCES = [
	'artist'		=> 'artist',
	'location'		=> 'location',
	'organization'	=> 'organization',
	'party'			=> 'party',
	'stream'		=> 'stream',
	'user'			=> 'user',
];

const PRFLG_THEMED	= 1;
const PRFLG_2x		= 2;
const PRFLG_OFFLINE	= 4;
const PRFLG_V2		= 8;

function get_presence_flags(string $type): int|false {
	return get_all_presences($type)[0] ?? false;
}

# WARNING: Also update te presence, presence_log and precencehit tables!

function get_all_presences(?string $type = null): ?array {
	/** @noinspection SpellCheckingInspection */
	static $__presence_specs = [
		# numbers
		'101barz'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"(\b101barz\.bnn\.nl/page/profiel/[\da-f]+|[a-z\d_\-]+\.(?<!www\.)101barz\.nl)"'],
		'123video'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\b[a-z\d_\-]+\.(?<!www\.)123video\.nl\b"'],
		'123website'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\b123website\.nl/[a-z\d_.+-]+"'],
		'2day'				=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\b2day\.nl/[a-z\d_.+-]+"'],
		'2night'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\b2night\.nl/[a-z\d_.+-]+"'],
		'3voor12'			=> [PRFLG_OFFLINE|PRFLG_THEMED,	/** @lang PhpRegExp */ '"\b3voor12\.vpro\.nl/artiesten/artiest/\d+"'],
		'4us'				=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\b4us\.vg/member\.php\?u=\d+"'],
		'500px'				=> [PRFLG_THEMED,				/** @lang PhpRegExp */ '"\b500px\.com/[a-z\d_.+-]+"'],

		# A
		'about.me'			=> [0,							/** @lang PhpRegExp */ '"\babout\.me/[a-z\d_.+-]+"'],
		'agendainfo'		=> [0,							/** @lang PhpRegExp */ '"\bagendainfo\.nl/user/(\d+)/"'],
		'almere4you'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\b[a-z\d_\-]+\.(?<!www\.)almere4you\.nl\b"'],
		'alphen'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\balphen\.nu/\?p=profiel&pid=\d+"'],
		'altparty'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\baltparty\.net/profile/[a-z\d_.+-]+"'],
		'andrelon'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bactie\.andrelon\.nl/#/gallerij/\d+"'],
		'apeldoorner'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bapeldoorner\.com/[a-z\d_.+-]+"'],
		'appstore'			=> [PRFLG_2x,					/** @lang PhpRegExp */ '"\bitunes\.apple\.com/app/[a-z\d_.+-]+/id\d+"'],
		'artistdata_sonicbids'
							=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bartistdata\.sonicbids\.com/[a-z\d_.+-]+"'],
		'asn-online'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\basn\-online\.nl/community/profiles/view/\d+"'],
		'atlastfm'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\batlast\.fm/[a-z\d_.+-]+"'],
		'audiogenic'		=> [PRFLG_OFFLINE|PRFLG_THEMED,	/** @lang PhpRegExp */ '"\baudiogenic\.fr/\d+/artist-[a-z\d_.+-]+"'],
		'audiomack'			=> [0,							/** @lang PhpRegExp */ '"\baudiomack.com/artist/.+"'],
		'autoblog'			=> [0,							/** @lang PhpRegExp */ '"\bautoblog\.nl/gebruikers/[a-z\d_.+-]+"'],
		'awdio'				=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bawdio\.com/[a-z\d_.+-]+"'],
		'audiojelly'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\baudiojelly\.com/artists/[a-z\d_.+-]+/\d+"'],

		# B
		'babes-dudes'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bbabes\-dudes\.nl/[a-z\d_.+-;]+"'],
		'babybytes'			=> [0,							/** @lang PhpRegExp */ '"\bbabybytes\.nl/vip/[a-z\d_.+-]+"'],
		'badoo'				=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bbadoo\.com/[a-z\d_.+-]+"'],
		'back2noize'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bback2noize\.com/memberlist\.php\?mode=viewprofile&u=\d+"'],
		'bambuser'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bbambuser\.com/[a-z\d_.+-]+"'],
		'bandcamp'			=> [PRFLG_2x,					/** @lang PhpRegExp */ '"\b(?:[a-z\d_\-]+\.(?<!www\.)bandcamp\.com|bandcamp\.com/[a-z\d_]+)\b"'],
		'baszdrome'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bbaszdrome\.com/info/djdetails\.php\?djid=\d+"'],
		'battlefieldheroes'	=> [0,							/** @lang PhpRegExp */ '"\bbattlefieldheroes\.com/[a-z]{2,3}/player/\d+"'],
		'beatport'			=> [0,							/** @lang PhpRegExp */ '"\bbeatport\.com/[a-z\d_.+-]+"'],
		'beatportal'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bbeatportal\.com/artists/[a-z\d_.+-]+"'],
		'beatsdigital'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bbeatsdigital\.com/label/[a-z\d_.+-]+/\d+"'],
		'beatstars'			=> [PRFLG_2x,					/** @lang PhpRegExp */ '"(?:\bbeatstars\.com/[a-z\d_.+-]+|[a-z\d_\-]+\.(?<!www\.)beatstars.com/?$)"'],
		'bebo'				=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bbebo\.com/[a-z\d_.+-]+"'],
		'behance'			=> [PRFLG_2x,					/** @lang PhpRegExp */ '"\bbehance\.net/[a-z\d_.+-]+"'],
		'betribes'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bbetribes\.com/[a-z\d_.+-]+"'],
		'bimeiden'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bbimeiden\.nl/index\.php\?action=profile;u=\d+"'],
		'blackbunny'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bblackbunny\.nl/[a-z\d_.+-]+"'],
		'blip.fm'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bblip\.fm/[a-z\d_.+-]+"'],
		'blog-video'		=> [PRFLG_OFFLINE | PRFLG_2x,	/** @lang PhpRegExp */ '"\b[a-z\d_\-]+\.(?<!www\.)blog\-video\.tv\b"'],
		'blogg'				=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\b[a-z\d_\-]+\.(?<!www\.)blogg\.se\b"'],
		'blogger'			=> [PRFLG_2x,					/** @lang PhpRegExp */ '"\b(?:[a-z\d_.+-]+\.(?<!www\.)(?:blogspot|blogger)\.|blogger\.com/profile/\d+)"'],
		'blogsport'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\b[a-z\d_\-]+\.(?<!www\.)blogsport\.de\b"'],
		'bokt'				=> [0,							/** @lang PhpRegExp */ '"\bbokt\.nl/forums/memberlist\.php\?mode=viewprofile&u=\d+"'],
		'bol'				=> [0,							/** @lang PhpRegExp */ '"\bbol\.com/a-z\d_{2,3}/2ndhand/2ndhand_seller\.html\?sellerId=\d+|\bbol\.com/.+"'],
		'bolo'				=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bbolo\.nl/artiest/[a-z\d_.+-]+"'],
		'boomr'				=> [PRFLG_OFFLINE|PRFLG_THEMED,	/** @lang PhpRegExp */ '"\bboomr\.nl/[a-z\d_.+-]+"'],
		'bramosia'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bbramosia\.nl/profiel/\d+"'],
		'break'				=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bbreak\.com/user/[a-z\d_.+-]+"'],
		'bricklink'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bbricklink\.com/store\.asp\?p=[a-z\d_.+-]+"'],
		'brightkite'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bbrightkite\.com/people/[a-z\d_.+-]+"'],
		'brommerforum'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bbrommerforum\.nl/user/\d+"'],
		'bulletstar'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bbulletstar\.(?:net|de)/\?p=profile&g=[a-z\d_.+-]+"'],
		'buurmeisje'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bbm2011\.nl/profiel/\d+"'],
		'burn-studios'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bburn\-studios\.com/profile/[a-z\d_.+-]+"'],
		'buzznet'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\b[a-z\d_\-]+\.(?<!www\.)buzznet\.com/user/"'],

		# C
		'cdbaby'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bcdbaby\.com/cd/[a-z\d_.+-]+"'],
		'chatgirl'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\b[a-z\d_\-]+\.(?<!www\.)chatgirl\.nl\b"'],
		'chathoek'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bchathoek\.nl/\d+\-"'],
		'chatnu'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bchatnu\.nl/users/profile/\d+"'],
		'chattijd'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bprofielen\.chattijd\.nl/\?profiel=[a-z\d_.+-]+"'],
		'civicclubholland'	=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bcivicclubholland\.nl/forum/memberlist\.php\?mode=viewprofile&u=\d+"'],
		'clubcharts'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bclubcharts\.nl/memberlist\.php\?mode=viewprofile&u=\d+"'],
		'come2me'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\b[a-z\d_\-]+\.(?<!www\.)come2me\.nl\b"'],
		'corebay'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bcorebay\.com/index\.php\?showuser=\d+"'],
		'coretime'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bcoretime\.fm/member/\d+"'],
		'couchsurfing'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bcouchsurfing\.org/people/[a-z\d_.+-]+"'],
		'cu2'				=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bcu2\.nl/[a-z\d_.+-]+"'],

		# D
		'd2jsp'				=> [0,							/** @lang PhpRegExp */ '"\bd2jsp\.org/user\.php\?i=\d+"'],
		'dailybooth'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bdailybooth\.com/[a-z\d_.+-]+"'],
		'dailymotion'		=> [PRFLG_2x,					/** @lang PhpRegExp */ '"\bdailymotion\.com/[a-z\d_.+-]+"'],
		'dance.nl'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bdance\.nl/members/[a-z\d_.+-]+\.html"'],
		'dance-tunes'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\b(?:dance\-tunes\.com/#?home/(?:artist|label)/\d+|dancetunesradio\.com/tag/[a-z\d_.+-]+)"'],
		'dancegids'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bdancegids\.nl/(?:profiles/my\-profile/\d+|[a-z\d_.+-]+)"'],
		'danceproducer'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bdanceproducer\.nl/profile/[a-z\d_.+-]+"'],
		'darkthrone'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bdarkthrone\.com/viewprofile(?:\.dt\?.*?\bid=|/index/)\d+"'],
		'deezer'			=> [PRFLG_2x,					/** @lang PhpRegExp */ '"\bdeezer\.com/[a-z]{2/(?:album|artist|profile|show)/\d+"'],
		'decksharks'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bdecksharks\.com/[a-z\d_\-_\+\.]+/"'],
		'delicious'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bdelicious\.com/[a-z\d_.+-]+"'],
		'demodrop'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bdemodrop\.a-z\d_{2,3}\b"'],
		'deviantart'		=> [0,							/** @lang PhpRegExp */ '"\b[a-z\d_\-\.\+]+\.(?<!www\.)deviantart\."'],
		'dg2'				=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bdg2\.nl/.+"'],
		'dhost'				=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bdhost\.info/[a-z\d_.+-]+"'],
		'digg'				=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bdigg\.com/[a-z\d_.+-]+"'],
		'digitalracing'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bforum\.digitalracing\.nl/member\.php\?u=\d+"'],
		'discogs'			=> [PRFLG_2x,					/** @lang PhpRegExp */ '"\bdiscogs\.com/[a-z\d_.+-]+"'],
		'discord'			=> [0,							/** @lang PhpRegExp */ '"\bdiscord(?:app)?\.com/invite/.+"'],
		'disqus'			=> [0,							/** @lang PhpRegExp */ '"\bdisqus\.com/[a-z\d_.+-]+"'],
		'dj-mixes'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bdj\-mixes\.com/[a-z\d_.+-]+"'],
		'djbooth'			=> [0,							/** @lang PhpRegExp */ '"\b(?:djbooth\.net/index/artists/info/|djbooth.net/artists/)[a-z\d_.+-]+"'],
		'djdownload'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bdjdownload\.com/(?:labels|artist)/[a-z\d_.+-]+/\d+"'],
		'djfez'				=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bdjfez\.com/djs/[a-z\d_.+-]+"'],
		'djguide'			=> [0,							/** @lang PhpRegExp */ '"\bdjguide\.nl/(?:djinfo\.p\?djid|artistinfo\.p\?id|profielinfo\.p\?iduserprofile|locatiedetail\.p\?idlocation|organiserevents\.p\?id)=\d+"'],
		'djlist'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bthedjlist\.com/(?:djs|members|labels)/[a-z\d_.+-]+"'],
		'djresource'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bdjresource\.eu/Profile/profileid/\d+"'],
		'djscene'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bdjscene\.nl/industry/Artiesten/item\?artisttype=\d+&artistid=\d+"'],
		'djtunes'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bdjtunes\.com/(?:#/)?[a-z\d_.+-]+"'],
		'drumandbass'		=> [0,							/** @lang PhpRegExp */ '"\bdrumandbass\.nl/forum/index\.php\?action=profile[&;]u=\d+"'],
		'dutchdancescene'	=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bdutchdancescene\.com/profile/[a-z\d_.+-]+"'],
		'dutchheaven'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bdutchheaven\.nl/modules\.php\?name=Model_Details&op=userinfo&uname=[a-z\d_.+-]+"'],

		# E
		'ea_fc'				=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bea\.com/nl/voetbal/mgd/player-profile/\d+"'],
		'ebay'				=> [0,							/** @lang PhpRegExp */ '"\b(?:shop\.ebay\.[a-z]{2,4}/[a-z\d_.+-]+/m\.html|myworld\.(?:benl\.)?ebay\.a-z\d_{2,4}/[a-z\d_.+-]+)"'],
		'edmcommunity'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bedmcommunity\.com/memberlist\.php\?mode=viewprofile&u=\d+\b"'],
		'eigenwijsprodukties'
							=> [PRFLG_OFFLINE, 				/** @lang PhpRegExp */ '"\beigenwijsprodukties\.nl/artiesten/[a-z\d_.+-]+/"'],
		'electrobel'		=> [0,							/** @lang PhpRegExp */ '"\belectrobel\.org/profile\.ebel/\d+"'],
		'esl'				=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\besl\.eu/eu/cs/4on4/mr15/ladder/player/\d+/"'],
		'esnips'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\besnips\.com/user/[a-z\d_.+-]+"'],
		'examiner'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bexaminer\.com/[a-z\d_.+-]+"'],

		# F
		'f1rejects'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bf1rejects\.com/forum/memberlist\.php\?mode=viewprofile&u=\d+"'],
		'facebookpage'		=> [PRFLG_2x,					/** @lang PhpRegExp */ '"\bfacebook\.[a-z]{2,3}/(?:pages|pg)/.+"'],
		'facebook'			=> [PRFLG_2x,					/** @lang PhpRegExp */ '"\bfacebook\.[a-z]{2,3}/.+"'],
		'factsonacts'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bfactsonacts\.nl/catalog/profile/fetchone/[^/]+/\?k=\d+"'],
		'fear.fm'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bfear\.fm/(?:user|artist|radio/program)/[^/]+/\d+\b"'],
		'feest.je'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bfeest\.je/[a-z\d_.+-]+"'],
		'festuc'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bfestuc\.com/dj\-[a-z\d_.+-]+"'],
		'fetlife'			=> [0,							/** @lang PhpRegExp */ '"\bfetlife\.com/users/\d+"'],
		'flavors'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bflavors\.me/[a-z\d_.+-]+"'],
		'flickr'			=> [0,							/** @lang PhpRegExp */ '"\bflickr\.com/(?:photos|people|groups)/[a-z\d_.+-]+"'],
		'flowd'				=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bflowd\.com/(?:hot/)?[a-z\d_.+-]+"'],
		'fmylife'			=> [PRFLG_THEMED,				/** @lang PhpRegExp */ '"\bfmylife\.com/user/\d+"'],
		'formspring'		=> [PRFLG_OFFLINE|PRFLG_THEMED,	/** @lang PhpRegExp */ '"\bformspring\.me/[a-z\d_.+-]+"'],
		'formule323club'	=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bformule323club\.nl/forum/memberlist\.php\?mode=viewprofile&u=\d+"'],
		'forumer'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\b[a-z\d_\-]+\.(?<!www\.)forumer\.com/index\.php\?showuser=\d+"'],
		'fotografentop'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bfotografentop\.nl/info/\d+(?::|\.html)"'],
		'fotolog'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bfotolog\.com/[a-z\d_.+-]"'],
		'fourartists'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bfourartists\.com/[a-z\d_]{2,3}/kuenstler/a-z\d_/[a-z\d_.+-]+\.html\b"'],
		'foursquare'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bfoursquare\.com/(?:user/)?[a-z\d_.+-]+"'],
		'fr12'				=> [0,							/** @lang PhpRegExp */ '"\bfr12\.nl/(?:profiel/\d+|user/[a-z\d_\-\+_]_)"'],
		'friend.ly'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bfriend\.ly/profile\?(?:ref=[a-z\d_]+&)?id=\d+"'],
		'friendster'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bprofiles\.friendster\.com/\d+"'],
		'ftd'				=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bforum\.ftd\.nu/member\.php\?u=\d+"'],
		'fundalize'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bfundalize\.com/profiel/[a-z\d_.+-]+"'],
		'funkybabes'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bfunkybabes\.nl/[a-z\d_.+-]+"'],
		'fusionbv'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bfusionbv\.com/community/memberlist\.php\?mode=viewprofile&u=\d+"'],

		# G
		'gabber.fm'			=> [PRFLG_OFFLINE | PRFLG_2x,	/** @lang PhpRegExp */ '"\bgabber\.fm/user\.php\?id\.\d+"'],
		'gabbertube'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bgabbertube\.com/profile/\d+\-[a-z\d_.+-]+"'],
		'gamesmeter'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bgamesmeter\.nl/user/\d+"'],
		'gamespot'			=> [0,							/** @lang PhpRegExp */ '"\bgamespot\.com/profile/[a-z\d_.+-]+/"'],
		'gametrailers'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bgametrailers\.com/users/[a-z\d_.+-]+"'],
		'gay'				=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bgay\.nl/[a-z\d_.+-]+"'],
		'gaydar'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bgaydar\.nl/[a-z\d_.+-]+"'],
		'gertlily'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bgertlily\.co\.cc/home/<USER>"'],
		'getglue'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bgetglue\.com/[a-z\d_.+-]+"'],
		'getsnipper'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bgetsnipper\.com/a-z\d_{2}/channel/[a-z\d_-]+/"'],
		'gigatools'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bgigatools\.com/user/[a-z\d_.+-]+"'],
		'globalhardcore'	=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bglobalhardcore\.net/forum/memberlist\.php\?mode=viewprofile&u=\d+"'],
		'globalhardtrance'	=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bglobalhardtrance\.com/forum/memberlist\.php\?mode=viewprofile&u=\d+"'],
		'goatrance'			=> [0,							/** @lang PhpRegExp */ '"\bgoatrance\.de/goabase/member/profile/[a-z\d_]+"'],
		'goodreads'			=> [0,							/** @lang PhpRegExp */ '"\bgoodreads\.com/user/show/\d+"'],
		'google'			=> [PRFLG_2x,					/** @lang PhpRegExp */ '"\b(?:google\.com/profiles/[a-z\d_.+-]+|maps\.google\.com/maps/place\?cid=\d+|profiles\.google\.a-z\d_{2,4}/[a-z\d_.+-]+)"'],
		'googleplus'		=> [PRFLG_OFFLINE | PRFLG_2x,	/** @lang PhpRegExp */ '"\b(?:plus\.google\.a-z\d_{2,3}/(?:(?:u/0/)?\d{10,}|\+[a-z\d_.+-]+|u/.+)|gplus\.to/[a-z\d_.+-]+)\b"'],
		'googlesites'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bsites\.google\.a-z\d_{2,3}/site/[a-z\d_.+-]+/"'],
		'gowalla'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bgowalla\.com/spots/\d+"'],
		'gravatar'			=> [0,							/** @lang PhpRegExp */ '"\bgravatar\.com/[a-z\d_.+-]+"'],
		'grimepedia'		=> [PRFLG_OFFLINE|PRFLG_THEMED,	/** @lang PhpRegExp */ '"\bgrimepedia\.co\.uk/wiki/[a-z\d_.+-]+"'],
		'guestzone'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bguestzone\.nl/(member_info\.php\?member_id=|party_agenda\.php\?(?:organiser|(?:venuelink=\d+&)?venue)=)\d+"'],

		# H
		'hardcoredates'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bhardcoredates\.de/component/option,com_comprofiler/task,userProfile/user,\d+"'],
		'harderstate'		=> [0,							/** @lang PhpRegExp */ '"\bharderstate\.com/member/[a-z\d_.+-]+"'],
		'hardstyle'			=> [0,							/** @lang PhpRegExp */ '"\bhardstyle\.com/[a-z\d_.+-]+"'],
		'hardtraxx'			=> [0,							/** @lang PhpRegExp */ '"\bhardtraxx\.nl/forum/member/[a-z\d_.+-]+"'],
		'hardware.info'		=> [0,							/** @lang PhpRegExp */ '"\bhardware\.info/profiel/\d+"'],
		'h4h'				=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bh4h\.fm/(?:[a-z\d_.+-]+|user/[a-z\d_.+-\*]+).html$"'],
		'hearthis.at'		=> [PRFLG_THEMED,				/** @lang PhpRegExp */ '"\bhearthis\.at/.+"'],
		'hi5'				=> [0,							/** @lang PhpRegExp */ '"\bhi5\.com/[a-z\d_.+-]+"'],
		'highdeas'			=> [0,							/** @lang PhpRegExp */ '"\bhighdeas\.com/users/[a-z\d_.+-]+"'],
		'hiphopinjesmoel'	=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bhiphopinjesmoel\.com/members/\d+"'],
		'homo.nl'			=> [PRFLG_OFFLINE|PRFLG_2x,		/** @lang PhpRegExp */ '"\b[a-z\d_\-]+\.(?<!www\.)homo\.nl\b"'],
		'hotfrog'			=> [0,							/** @lang PhpRegExp */ '"\bhotfrog\.a-z\d_{2,4}/Companies/[a-z\d_.+-]+"'],
		'house-mixes'		=> [0,							/** @lang PhpRegExp */ '"\bhouse\-mixes\.com/(?:artists|profile)/[a-z\d_.+-]+"'],
		'hyves'				=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"(?:[a-z\d_\-]+\.(?<!www\.)hyves\.(?:nl|net)|www\.hyves\.nl/(?:hyves|spot|agenda)/\d+/.+)"'],

		# I
		'ibiza-voice'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bibiza-voice\.com/music/artist/[a-z\d_.+-]+"'],
		'icq'				=> [0,							/** @lang PhpRegExp */ '"\bicq\.com/people/\d+"'],
		'ididid'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bididid\.eu/groups/\d+"'],
		'idj'				=> [PRFLG_THEMED,				/** @lang PhpRegExp */ '"\bidj\.cz/[a-z\d_.+-]+"'],
		'iens'				=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\biens\.nl/restaurant/\d+"'],
		'ilike'				=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bilike\.com/(?:user|artist)/[a-z\d_.+-]+"'],
		'imageshack'		=> [0,							/** @lang PhpRegExp */ '"\bimageshack\.com/user/.+"'],
		'imdb'				=> [0,							/** @lang PhpRegExp */ '"\bimdb\.com/name/nm\d+"'],
		'imikimi'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bimikimi\.com/[a-z\d_.+-]+"'],
		'imvu'				=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bavatars\.imvu\.com/[a-z\d_.+-]+"'],
		'insidegamer'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\binsidegamer\.nl/members/\d+"'],
		'instagram'			=> [PRFLG_2x|PRFLG_V2,			/** @lang PhpRegExp */ '"\binstagram\.com/(?:p/)?[a-z\d_.+-]+"'],
		'inthemix'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\binthemix\.com\.au/(?:allabout/artist/\d+|people/[a-z\d_.+-]+)"'],
		'is-music'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\b[a-z\d_-]+\.(?<!www\.)is-music\.net\b"'],
		'itsmyurls'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bitsmyurls\.com/[a-z\d_.+-]+"'],
		# FIXME: Create an 'apple_music' type to replace the 'itunes' one. Can use existing icon.
		'itunes'			=> [PRFLG_THEMED|PRFLG_2x,		/** @lang PhpRegExp */ '"\b(?:itunes\.com/[a-z\d_.+-]+|(?:itunes|music)\.apple\.com/(?:(?:a-z\d_{2,4}/)?(?:(?:podcast|artist|curator)/[a-z\d_.+-%]+|profile/id\-\d+)|.*?viewPodcast\?id=\d+))\b"'],
		'iwiw'				=> [PRFLG_OFFLINE|PRFLG_THEMED,	/** @lang PhpRegExp */ '"\biwiw\.hu/pages/user/userdata\.jsp\?userID=\d+"'],

		# J
		'jamendo'			=> [0,							/** @lang PhpRegExp */ '"\bjamendo\.com/(?:a-z\d_{2,4}/)?(?:user|artist)/[a-z\d_.+-]+/"'],
		'jappy'				=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bjappy\.de/user/[a-z\d_.+-]+"'],
		'juno'				=> [0,							/** @lang PhpRegExp */ '"\bjuno\.co\.uk/artists/[a-z\d_.+-]+"'],
		'junodownload'		=> [0,							/** @lang PhpRegExp */ '"\bjunodownload\.com/(?:artists|labels|products)/[a-z\d_.+-]+"'],

		# K
		'kamasutrabeurs'	=> [0,							/** @lang PhpRegExp */ '"\bkamasutrabeurs\.nl/leden/.+/"'],
		'kat.ph'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bkat\.ph/user/[a-z\d_.+-]+"'],
		'kickasstorrents'	=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bkickasstorrents\.com/user/[a-z\d_.+-]+/"'],
		'king'				=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bking\.com/community/profile\.jsp\?username=[a-z\d_.+-]+"'],
		'kvk_hallo'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bhallo\.kvk\.nl/hallo/ondernemers/[a-z]+/default\.aspx\b"'],
		'komi'				=> [PRFLG_2x,					/** @lang PhpRegExp */ '"\b[a-z]+.komi\.io/"'],

		# L
		'labelsbase'		=> [0,							/** @lang PhpRegExp */ '"\blabelsbase\.net/.+"'],
		'labyrinthbookings'	=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\blabyrinthbookings\.com/agency/artists/[a-z\d_-]+"'],
		'lastfm'			=> [0,							/** @lang PhpRegExp */ '"\blast(?:fm\.a-z\d_{2,4}|\.fm)/(?:listen/)?(?:user|music|venue|group|event)/[a-z\d_.+-%]+"'],
		'legaldownload.net'	=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\blegaldownload\.net/[a-z\d_.+-]+"'],
		'lethal-zone'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\blethal\-zone\.eu/memberlist\.php\?mode=viewprofile&u=\d+"'],
		'letsmix'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bletsmix\.com/[a-z\d_.+-]+"'],
		'link2party'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\blink2party\.nl/(?:a-z\d_+/)?[a-z\d_.+-]+"'],
		'linkedin'			=> [0,							/** @lang PhpRegExp */ '"\b(?:linkedin\.com/[a-z\d.+_-]+|linkd\.in/[a-z\d_]+)"'],
		'linkin.bio'		=> [PRFLG_2x,					/** @lang PhpRegExp */ '"\blinkin\.bio/[a-z\d]+"'],
		'linkr.bio'			=> [0,							/** @lang PhpRegExp */ '"\blinkr\.bio/[a-z\d._+-]+"'],
		'linktree'			=> [0,							/** @lang PhpRegExp */ '"\blinktr.ee/[a-z\d_.+-]+"'],
		'listen2myradio'	=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\b[a-z\d_.+-]+\.listen2myradio\.com\b"'],
		'live'				=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\b(?:[a-z\d_.+-]+\.profile\.live\.com|profile\.live\.com/cid-[a-f\d]+)\b"'],
		'livedoor'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bblog\.livedoor\.jp/[a-z\d_.+-]+"'],
		'livejournal'		=> [0,							/** @lang PhpRegExp */ '"\b[a-z\d_.+-]+\.livejournal\.com\b"'],
		'livemocha'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\blivemocha\.com/profiles/view/\d+"'],
		'livenation'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\b(?:livenation\.com/[a-z\d_-]+/venue/\d+|vipnation\.livenation\.com/venue/[a-z\d_-]+)"'],
		'livesets'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\blivesets\.com/forum/user/\d+"'],
		'livestream'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\blivestream\.com/[a-z\d_.+-]+"'],
		'lockerz'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\blockerz\.com/gallery/\d+"'],
		'lololyrics'		=> [0,							/** @lang PhpRegExp */ '"\blololyrics\.com/user/[a-z\d_.+-]+"'],
		'lookbook'			=> [0,							/** @lang PhpRegExp */ '"\blookbook\.nu/[a-z\d_.+-]+"'],
		'lookilike'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\blookilike\.com/models/overview/profile/[a-z\d_.+-]+"'],
		'looptijden'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\blooptijden\.nl/lopers/[a-z\d_.+-]+"'],
		'lovegirlz'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\blovegirlz\.nl/(?:[a-z\d_.+-]|%\d+)+"'],
		'lsdb'				=> [0,							/** @lang PhpRegExp */ '"\blsdb\.a-z\d_{2,4}/(?:user/[a-z\d]+|artists/view/\d+)"'],

		# M
		'marktplaats'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bverkopers\.marktplaats\.nl/\d+"'],
		'maxmodels'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bmaxmodels\.nl/models/fashion\-men/[a-z\d_.+-]+/\d+/\d+"'],
		'meetme'			=> [PRFLG_OFFLINE | PRFLG_THEMED | PRFLG_2x,
															/** @lang PhpRegExp */ '"\bmeetme\.com/member/\d+"'],
		'megavideo'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bmegavideo\.com/[a-z\d_.+-\?\=]+"'],
		'meinvz'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bmeinvz\.net/Profile/[a-z\d_.+-]+"'],
		'memories4you'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bmemories4you\.nl/index\.php\?option=com_comprofiler&task=userProfile&user=\d+"'],
		'messenger'			=> [PRFLG_2x,					/** @lang PhpRegExp */ '"//(?:m\.me/.+|(?:www\.)?messenger\.com/t/[^/&]+/?)$"'],
		'metacafe'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bmetacafe\.com/channels/[a-z\d_.+-]+/"'],
		'metal-archives'	=> [0,							/** @lang PhpRegExp */ '"\bmetal\-archives\.com/band\.php\?id=\d+"'],
		'mijnalbum'			=> [0,							/** @lang PhpRegExp */ '"\b[a-z\d_-]+\.(?<!www\.)mijnalbums?\.nl\b"'],
		'ministryofsound'	=> [PRFLG_OFFLINE|PRFLG_THEMED,	/** @lang PhpRegExp */ '"\bministryofsound\.com(?:\.au)?/music/artist/dj\-[a-z\d_.+-]+"'],
		'minus'				=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\b[a-z\d_-]+\.(?<!www\.)minus\.com\b"'],
		'mix.dj'			=> [PRFLG_OFFLINE | PRFLG_2x | PRFLG_THEMED,
															/** @lang PhpRegExp */ '"\bmix\.dj/[a-z\d_.+-]+"'],
		'mixcrate'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bmixcrate\.com/[a-z\d_+_-]+"'],
		'mixcloud'			=> [PRFLG_2x,					/** @lang PhpRegExp */ '"\bmixcloud\.com/[a-z\d_.%+_-]+"'],
		'mixify'			=> [PRFLG_OFFLINE | PRFLG_2x,	/** @lang PhpRegExp */ '"\bmixify\.com/[a-z\d_.+-]+"'],
		'mixlr'				=> [PRFLG_OFFLINE | PRFLG_2x,	/** @lang PhpRegExp */ '"\bmixlr\.com/[a-z\d_.+-]+"'],
		'modellennet'		=> [0,							/** @lang PhpRegExp */ '"\bmodellennet\.be/profile_det\.php\?type=model\&pid=\d+"'],
		'modelmayhem'		=> [PRFLG_THEMED,				/** @lang PhpRegExp */ '"\bmodelmayhem\.com/[a-z\d_.+-]+"'],
		'motorstek'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\b(?:[a-z\d_-]+\.(?<!www\.)motorstek\.nl|motorstek\.nl/gebruiker/[a-z\d_-]+)\b"'],
		'moviemeter'		=> [0,							/** @lang PhpRegExp */ '"\bmoviemeter\.nl/user/\d+"'],
		'msnsexdate'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bmsnsexdate\.com/profiel/[a-z\d_.+-]+\.html\b"'],
		'multiply'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\b[a-z\d_-]+\.(?<!www\.)multiply\.com\b"'],
		'music2dance'		=> [0,							/** @lang PhpRegExp */ '"\bmusic2dance\.com/index\.php\?m=profiel&id=\d+"'],
		'musicfrom'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bmusicfrom\.nl/artiesten/\d+"'],
		'musicmeter'		=> [0,							/** @lang PhpRegExp */ '"\bmusicmeter\.nl/user/\d+"'],
		'myanimelist'		=> [0,							/** @lang PhpRegExp */ '"\bmyanimelist\.net/profile/[a-z\d_.+-]+"'],
		'mydjspace	'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bmydjspace\.com/[a-z\d_.+-]+"'],
		'myebook'			=> [PRFLG_OFFLINE|PRFLG_THEMED,	/** @lang PhpRegExp */ '"\bmyebook\.com/[a-z\d_.+-]+"'],
		'mygamercard'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bprofile\.mygamercard\.net/[a-z\d_.+-%]+"'],
		'mygb'				=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\b[a-z\d_-]+\.(?<!www\.)mygb\.nl\b"'],
		'myname'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bmyname\.is/[a-z\d_.+-]+"'],
		'mypodcast'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\b[a-z\d_-]+\.(?<!www\.)mypodcast\.com\b"'],
		'myspace'			=> [PRFLG_THEMED| PRFLG_2x,		/** @lang PhpRegExp */ '"\bmyspace\.a-z\d_{2,4}/[a-z\d_.+-]+"'],
		'myvideo'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bmyvideo\.a-z\d_{2,4}/channel/[a-z\d_.+-]+"'],

		# N
		'netlog'			=> [PRFLG_OFFLINE|PRFLG_THEMED,	/** @lang PhpRegExp */ '"\bnetlog\.a-z\d_+/[a-z\d_.+-]+"'],
		'new-bambu'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bnew-bambu\.com/start\.php/profil/[a-z\d_.+-]+"'],
		'newunderground'	=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bnewunderground\.nl/deejay/[a-z\d_.+-]+/\d+"'],
		'nike'				=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bmy\.nike\.com/[a-z\d_\s.+_-]+"'],
		'ning.danceproducer'=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bdanceproducer\.ning\.com/profile/[a-z\d_.+-]+"'],
		'nogeno'			=> [PRFLG_OFFLINE|PRFLG_THEMED,	/** @lang PhpRegExp */ '"\bnogeno\.com/[a-z\d_.+-]+"'],
		'noisetrade'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bnoisetrade\.com/[a-z\d_.+-]+"'],
		'noxa'				=> [0,							/** @lang PhpRegExp */ '"\bnoxa\.net/[a-z\d_.+-]+"'],
		'nujij'				=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bnujij\.nl/[a-z\d_.+-]+\.\d+\.lynkx"'],

		# O
		'obamiconme'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bobamiconme\.pastemagazine\.com/profiles/[a-z\d_.+-]+"'],
		'official.fm'		=> [PRFLG_OFFLINE|PRFLG_THEMED,	/** @lang PhpRegExp */ '"\b[a-z\d_-]+\.(?<!www\.)official\.fm\b"'],
		'officialpokerrankings'
							=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bofficialpokerrankings\.com/pokerstars/[a-z\d_.+-]+"'],
		'oinovosom'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\boinovosom.com.br/[a-z\d_.+-]+"'],
		'oldschoolgabbers'	=> [PRFLG_OFFLINE|PRFLG_THEMED,	/** @lang PhpRegExp */ '"\boldschoolgabbers\.nl/user\.php\?id\.\d+"'],
		'onesheet'			=> [PRFLG_OFFLINE|PRFLG_2x,		/** @lang PhpRegExp */ '"\bonesheet\.com/[a-z\d_.+-]+"'],
		'ongekendtalent'	=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bongekendtalent\.nl/band/[a-z\d_.+-]+"'],
		'onlyfriends'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bonlyfriends\.be/forum/memberlist\.php\?mode=viewprofile&u=\d+"'],
		'orkut'				=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\borkut\.com(?:\.br)?/(?:Main#)?Profile\?uid=\d+"'],
		'ourstage'			=> [0,							/** @lang PhpRegExp */ '"\bourstage\.com/epk/[a-z\d_.+-]+"'],
		'outlar'			=> [PRFLG_OFFLINE|PRFLG_2x,		/** @lang PhpRegExp */ '"\boutlar\.com/artist\.php\?id=\d+"'],

		# P
		'panoramio'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bpanoramio\.com/user/[a-z\d_.+-]+"'],
		'party-babe'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bparty\-babe\.be/a-z\d_+/fotograaf/a-z\d_+"'],
		'partydb'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bpartydb\.nl/user/[a-z\d_.+-]+\.html"'],
		'partyflock'		=> [0,							/** @lang PhpRegExp */ '"\bpartyflock\.nl/(?:artist|dj|user)/\d+"'],
		'partyfreaker'		=> [0,							/** @lang PhpRegExp */ '"\bpartyfreaker\.nl/profile/[a-z\d_.+-]+"'],
		'partygood'			=> [PRFLG_OFFLINE|PRFLG_THEMED,	/** @lang PhpRegExp */ '"\bpartygood\.nl/index\.php/members/mijn\-profiel/userprofile/[a-z\d_.+-]+"'],
		'partyguide.be'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bpartyguide\.be/[a-z]+/members/\d+/"'],
		'partypeeps2000'	=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\b(?:partypeeps2000|pp2g)\.(?:com|nl)/(?:members/[a-z\d_.+-]+(?:\.aspx)?|Artist/\d+)"'],
		'partyscene'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bpartyscene\.nl/(?:pages\.php\?page=1063&uid=\d+|community/profiel\?uid=\d+)"'],
		'patreon'			=> [0,							/** @lang PhpRegExp */ '"\bpatreon\.com/.+"'],
		'pfsquad'			=> [PRFLG_OFFLINE | PRFLG_THEMED | PRFLG_2x,
															/** @lang PhpRegExp */ '"\bpfsquad\.nu/gebruiker_cp\.html\?profile=\d+"'],
		'photobucket'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bphotobucket\.com/(?:home/[a-z\d_.+-]+|albums/[a-z\d_]+/[a-z\d_.+-]+)"'],
		'photoshop'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bphotoshop\.com/users/[\da-z\d_.+-]+/profile"'],
		'picasa'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bpicasa(?:web\.google)?\.a-z\d_{2,4}/[a-z\d_.+-]+"'],
		'picturepush'		=> [0,							/** @lang PhpRegExp */ '"\b[a-z\d_\-\.]+\.(?<!www\.)picturepush\.com\b"'],
		'picturetrail'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bpicturetrail\.com/[a-z\d_.+-]+"'],
		'piczo'				=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\b[a-z\d_\-\.]+\.(?<!www\.)piczo\.com\b"'],
		'pingplace'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bpingplace\.nl/[a-z\d_.+-]+"'],
		'pinterest'			=> [PRFLG_2x,					/** @lang PhpRegExp */ '"\bpinterest\.com/[a-z\d_.+-]+"'],
		'pkr'				=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bpkr\.com/a-z\d_{2,4}/community/players/\d+"'],
		'play.fm'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bplay\.fm/(?:artist|festival)/[a-z\d_.+-]+"'],
		'playfire'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bplayfire\.com/[a-z\d_.+-]+"'],
		'plurlife'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bplurlife\.com/[a-z\d_.+-]+"'],
		'podomatic'			=> [0,							/** @lang PhpRegExp */ '"\b(?:[a-z\d_-]+\.(?<!www\.)podomatic\.com|podomatic\.com/[a-z\d_.+-]+)"'],
		'polyvore'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bpolyvore\.com/cgi/profile\?id=\d+"'],
		'posterous'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\b[a-z\d_-]+\.(?<!www\.)posterous\.com\b"'],
		'promodj'			=> [0,							/** @lang PhpRegExp */ '"\b(?:[a-z\d_-]+\.(?<!www\.)(?:pdj|promodj)\.(?:ru|com)|promodj\.com/[a-z\d_]+)\b"'],
		'purevolume'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bpurevolume\.com/[a-z\d_.+-]+"'],

		# Q
		'q-dance'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bq-dance\.(?:com\.au|a-z\d_{2,4})/q/user_profile/[a-z\d_.+-]+"'],
		'qik'				=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bqik\.com/[a-z\d_.+-]+"'],

		# R
		'race.sk'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\brace\.sk/forum/memberlist\.php\?mode=viewprofile&u=\d+"'],
		'racesimulations'	=>[PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bracesimulations\.com/account/userinfo/\d+\.html"'],
		'rapidshare'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\brapidshare\.com/users/[a-z\d_.+-]+"'],
		'rave.ca'			=> [PRFLG_2x,					/** @lang PhpRegExp */ '"\brave\.ca/[a-z]{2}/profiles_info/\d+"'],
		'rcrotterdam'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\brcrotterdam\.nl/member\.php/\d+-"'],
		'rdio'				=> [PRFLG_OFFLINE|PRFLG_2x,		/** @lang PhpRegExp */ '"\brdio\.com/artist/[a-z\d_.+-]+"'],
		'reislogger'		=> [0,							/** @lang PhpRegExp */ '"\b[a-z\d_-]+\.(?<!www\.)reislogger\.nl\b"'],
		'residentadvisor'	=> [PRFLG_THEMED|PRFLG_2x,		/** @lang PhpRegExp */ '"\bra\.co/(?:dj|profile|clubs?|events?|promoters?|labels?)/[a-z\d_.+-]+"'],
		'reverbnation'		=> [0,							/** @lang PhpRegExp */ '"\breverbnation\.com/[a-z\d_.+-]+"'],
		'robotdj'			=> [PRFLG_OFFLINE|PRFLG_THEMED,	/** @lang PhpRegExp */ '"\brobotdj\.net/?\?dj=.+"'],
		'rolldabeats'		=> [0,							/** @lang PhpRegExp */ '"\brolldabeats\.com/artist/[a-z\d_.+-]+"'],
		'rotterdam4you'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\b[a-z\d_-]+\.(?<!www\.)rotterdam4you\.nl\b"'],
		'runkeeper'			=> [0,							/** @lang PhpRegExp */ '"\brunkeeper\.com/user/[a-z\d_.+-]+"'],

		# S
		'samurai.fm'		=> [PRFLG_OFFLINE|PRFLG_2x,		/** @lang PhpRegExp */ '"\bsamurai\.fm/[a-z\d_.+-]+"'],
		'schuelervz'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bschuelervz\.net/Profile/[a-z\d_.+-]+"'],
		'sdc'				=> [0,							/** @lang PhpRegExp */ '"\bwww\.sdc\.com/react/#/profile\?idUser=\d+"'],
		'seeyoudance'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bseeyoudance\.nl/[a-z\d_.+-]+"'],
		'sevenload'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bsevenload\.com/users/[a-z\d_.+-]+"'],
		# NOTE: Only entry here, no icon and no database update done:
		# 'shopify'			=> [0,							/** @lang PhpRegExp */ '"\b(?:my)?shopify.com"'],
		'silaa7-mocro'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bsilaa7-mocro\.nl/home\.php\?p=profiel/profiel&naam=[a-z\d_.+-]+"'],
		'sitemodel'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bsitemodel\.net/(?:view_profile\.php\?member_id=)?\d+"'],
		'skoften'			=> [0,							/** @lang PhpRegExp */ '"\bskoften\.net/skoft/\d+"'],
		'skyrock'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\b[a-z\d_-]+\.(?<!www\.)(?:skyrock|skyblog)\.com\b"'],
		'slideshare'		=> [0,							/** @lang PhpRegExp */ '"\bslideshare\.net/[a-z\d_.+-]+"'],
		'snapchat'			=> [PRFLG_2x,					/** @lang PhpRegExp */ '"\bsnapchat\.com/add/.+"'],
		'solo.to'			=> [PRFLG_2x,					/** @lang PhpRegExp */ '"\bsolo\.to/.+"'],
		'solomio'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\b(solomio\.nl/p\d+_|[a-z\d_-]+\.(?<!www\.)solomio\.nl)"'],
		'songkick'			=> [PRFLG_2x,					/** @lang PhpRegExp */ '"\bsongkick\.com/(?:users/[a-z\d_.+-]+|artists/\d+)"'],
		'soundarea'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bforum\.soundarea\.org/index\.php\?/user/\d+"'],
		'soundclick'		=> [PRFLG_THEMED,				/** @lang PhpRegExp */ '"\bsoundclick\.com/[a-z\d_.+-]+"'],
		'soundcloud'		=> [PRFLG_2x,					/** @lang PhpRegExp */ '"\bsoundcloud\.com/(?!you/tracks)[a-z\d_.+-]+"'],
		'spaces'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\b[a-z\d_-]+\.(?<!www\.)spaces\.live\."'],
		'speedcore.ca'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bspeedcore\.ca/component/comprofiler/userprofile/[a-z\d_.+-]+"'],
		'spoilertv'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bspoilertv\.co\.uk/forum/memberlist\.php\?mode=viewprofile&u=\d+"'],
		'spotify'			=> [PRFLG_V2 | PRFLG_2x,		/** @lang PhpRegExp */ '"\b(?:open|play)\.spotify\.com/(?:user|artist|playlist)/[a-z\d_.+-]+"'],
		'spreadshirt'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\b[a-z\d_-]+\.(?<!www\.)spreadshirt\.nl"'],
		'starchat'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bstarchat\.nl/members/profile/[a-z\d_.+-]+"'],
		'steamcommunity'	=> [0,							/** @lang PhpRegExp */ '"\bsteamcommunity\.com/(?:id|profiles)/[a-z\d_.+-]+"'],
		'studivz'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bstudivz\.net/Profile/[a-z\d_.+-]+"'],
		'stumbleupon'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\b(?:stumbleupon\.com/stumbler/[a-z\d_.+-]+|[a-z\d_-]+\.(?<!www\.)stumbleupon\.com)"'],
		'sugababes'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bsugababes\.nl/[a-z\d_;*.+-]+"'],
		'superdudes'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bsuperdudes\.nl/[a-z\d_;*.+-]+"'],
		'sxc'				=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bsxc\.hu//?profile/[a-z\d_.+-]+"'],

		# T
		'tagged'			=> [0,							/** @lang PhpRegExp */ '"\btagged\.com/(?!profile.html$)[a-z\d_.+-]+"'],
		'techno4ever.fm'	=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\btechno4ever\.fm/profil/\d+"'],
		'telegram'			=> [PRFLG_2x,					/** @lang PhpRegExp */ '"//(?:t\.me/.+|.+\.t\.me/?)$"'],
		'theharderforum'	=> [PRFLG_OFFLINE|PRFLG_THEMED,	/** @lang PhpRegExp */ '"\btheharderforum\.com/index\.php\?/user/\d+"'],
		'thehardest'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bthehardest\.nl/(?:download\.php\?list\.\d+|user\.php\?id\.\d+)\b"'],
		'themapark'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bthemepark\.nl/ubb/ubbthreads\.php/users/\d+"'],
		'thepiratebay'		=> [PRFLG_THEMED,				/** @lang PhpRegExp */ '"\bthepiratebay\.a-z\d_{2,3}(?:/user/[a-z\d_.+-]+|search\.php\?q=user:.+)"'],
		'threads'			=> [PRFLG_THEMED,				/** @lang PhpRegExp */ '"\bthreads\.net/@[a-z\d_]{2,}"'],
		'tiktok'			=> [0,							/** @lang PhpRegExp */ '"\btiktok.com/\@.+"'],
		'tilllate'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\btilllate\.com/[a-z]{2,4}/(?:member|location|artist|venue)/[a-z\d_.+-]+"'],
		'tmf'				=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\b(?:[a-z\d_\-_]+\.(?<!www\.)tmf\.nl|tmf\.nl/[a-z\d_.+-]+)"'],
		'topdj'				=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\b[a-z\d_\-_]+\.topdj\.ua\b"'],
		'townster'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\btownster\.de/a-z\d_+/ort/[a-z\d_\-]+"'],
		'trackitdown'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\btrackitdown\.net/(?:(?:artist|recordlabel)/\d+|profile/[a-z\d_.+-]+)"'],
		'trance.fm'			=> [PRFLG_OFFLINE|PRFLG_THEMED,	/** @lang PhpRegExp */ '"\btrance\.fm/(?:#/)?people/\d+"'],
		'transmissionfm'	=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\btransmissionfm\.com/profile/[a-z\d_.+-]+/"'],
		'travbuddy'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\btravbuddy\.com/[a-z\d_.+-]+"'],
		'trueachievements'	=> [0,							/** @lang PhpRegExp */ '"\btrueachievements\.com/gamer/[a-z\d_.+]+"'],
		'tsapper'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\btsapper\.nl/profielkijk\.php\?userId=\d+"'],
		'tuenti'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bplaces\.tuenti\.com/[a-z\d_.+-]+"'],
		'tumblr'			=> [0,							/** @lang PhpRegExp */ '"\b[a-z\d_-]+\.(?<!www\.)tumblr\.com\b"'],
		'tunecore'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\btunecore\.com/music/[a-z\d_.+-]+"'],
		'turkishplace'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bturkishplace\.nl/(?:profiel/[a-z\d_.+-]+/|[a-z\d_.+-]+)"'],
		'tweakers'			=> [0,							/** @lang PhpRegExp */ '"\btweakers\.net/gallery/[a-z\d_.+-]+"'],
		'twitch'			=> [0,							/** @lang PhpRegExp */ '"\btwitch.tv/[a-z\d_.+-]+"'],
		'twitpic'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\btwitpic\.com/photos/[a-z\d_.+-]+"'],
		'twitter'			=> [PRFLG_2x,					/** @lang PhpRegExp */ '"\btwitter\.com/[a-z\d_.+-]+"'],
		'twoo'				=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\btwoo\.com/\d+"'],

		# U
		'uplay'				=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\buplay\.a-z\d_{2,4}\.ubi\.com/profile/[a-z\d_.+-]+"'],
		'ustream'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bustream\.tv/(?:channel|user)/[a-z\d_.+-]+"'],

		# V
		'vampirefreaks'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bvampirefreaks\.com/[a-z\d_.+-]+"'],
		'vgjot'				=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bvgjot\.nl/index\.php/[a-z]+-[a-z-]+\.html\b"'],
		'vi.be'				=> [PRFLG_THEMED,				/** @lang PhpRegExp */ '"\bvi\.be/platform/[a-z\d_.+-]+"'],
		'vibedeck'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bvibedeck\.com/[a-z\d_-]+"'],
		'viberate'			=> [0,							/** @lang PhpRegExp */ '"\bviberate\.com/artist/.+"'],
		'vimeo'				=> [0,							/** @lang PhpRegExp */ '"\bvimeo\.com/(?:user\d+|[a-z\d_.+-]+)"'],
		'virtualdj'			=> [0,							/** @lang PhpRegExp */ '"\bvirtualdj\.com/user/[a-z\d_.+-]+"'],
		'virtualnights'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bvirtualnights\.com/[a-z\d_.+-]+"'],
		'vk'				=> [0,							/** @lang PhpRegExp */ '"\bvk\.com/[a-z\d_.+-]+"'],
		'vkontakte'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bvkontakte\.ru/(?:id\d+|[a-z\d_\.]+)"'],
		'voetbalmodel'		=> [PRFLG_OFFLINE|PRFLG_THEMED,	/** @lang PhpRegExp */ '"\bvoetbalmodel\.nl/\d+/\d+/[a-z\d_.+-]+\.html"'],
		'voetbalzone'		=> [0,							/** @lang PhpRegExp */ '"\bvoetbalzone\.nl/profiel\.asp\?uid=\d+"'],
		'vwforum'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bvwforum\.nl/member/[a-z\d_.+-]+"'],

		# W
		'waarbenjij'		=> [0,							/** @lang PhpRegExp */ '"\b[a-z\d_\-_]+\.(?<!www\.)waarbenjij\.nu/profiel"'],
		'warp'				=> [PRFLG_OFFLINE | PRFLG_2x,	/** @lang PhpRegExp */ '"\bwarp\.net/records/[a-z\d_.+-]+"'],
		'web-log'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\b[a-z\d_\-_]+\.(?<!www\.)web\-log\.nl\b"'],
		'webklik'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\b[a-z\d_\-_]+\.(?<!www\.)webklik\.nl\b"'],
		'webnode'			=> [PRFLG_2x,					/** @lang PhpRegExp */ '"\b[a-z\d_\-_]+\.(?<!www\.)webnode\.(?:com|nl)\b"'],
		'weebly'			=> [0,							/** @lang PhpRegExp */ '"\b[a-z\d_\-_]+\.(?<!www\.)weebly\.com\b"'],
		'weheartit'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bweheartit\.com/[a-z\d_.+-]+"'],
		'wer-kennt-wen'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bwer-kennt-wen\.de/neues/[a-z\d_.+-]+"'],
		'wetransfer'		=> [0,							/** @lang PhpRegExp */ '"\b[a-z\d_\-_]+.(?<!www\.)wetransfer\.com"'],
		'whatsapp'			=> [PRFLG_2x,					/** @lang PhpRegExp */ '"\b(?:wa\.me/\d+|(?:chat|www)\.whatsapp\.com/(?:channel/)?[a-zA-Z\d]{22,})"'],
		'wieowie'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bwieowie\.nl/[a-z\d_.+-]+"'],
		'wikipedia'			=> [0,							/** @lang PhpRegExp */ '"\bwikipedia\.org/wiki/[a-z\d_.+-%]+"'],
		'wix'				=> [0,							/** @lang PhpRegExp */ '"\bwix\.com/[a-z\d_.+-]+"'],
		'wordpress'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\b[a-z\d_\-_]+\.(?<!www\.)wordpress\.com\b"'],
		'worldtv'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bworldtv\.com/[a-z\d_.+-]+"'],

		# X
		'x'					=> [PRFLG_2x,					/** @lang PhpRegExp */ '"\bx\.com/[a-z\d\+._\-]+"'],
		'xfire'				=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bxfire\.com/profile/[a-z\d_.+-]+"'],
		'xing'				=> [0,							/** @lang PhpRegExp */ '"\bxing\.com/profile/[a-z\d_.+-]+"'],
		'xseno'				=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bxseno\.nl/[a-z\d_.+-%\*;]+/?"'],
		'xstreamist'		=> [PRFLG_OFFLINE|PRFLG_THEMED,	/** @lang PhpRegExp */ '"\bxstreamist\.com/members/\d+"'],

		# Y
		'yahoo'				=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\b(?:profile\.yahoo\.com/[A-Z\d]{10,}|music\.yahoo\.com/[a-z\d_.+-]+/)"'],
		'yalwa'				=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\byalwa\.nl/ID_\d+/"'],
		'yearbook'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\byearbook\.com/ask/[a-z\d_.+-]+"'],
		'youtube'			=> [PRFLG_2x,					/** @lang PhpRegExp */ '"\b(?<!music\.)youtube\.a-z\d_+/@?(?!v/|watch\?)[a-z\d_.+-]+"'],
		'youtube.music'		=> [0,							/** @lang PhpRegExp */ '"\bmusic\.youtube\.com/channel/[a-zA-Z\d_-]+"'],

		# Z
		'zero-inch'			=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\b(?:[a-z\d_\-_]+\.(?<!www\.)zero\-inch\.com|zero\-inch\.com/artist/[a-z\d_.+-]+)\b"'],
		'zideo'				=> [PRFLG_OFFLINE|PRFLG_2x,		/** @lang PhpRegExp */ '"\bzideo\.nl/partner/[a-z\d_]+/"'],
		'zippyshare'		=> [PRFLG_OFFLINE,				/** @lang PhpRegExp */ '"\bzippyshare\.com/[a-z\d_.+-]+"'],
		'zoom'				=> [0,							/** @lang PhpRegExp */ '"\bforum\.zoom\.nl/profile/\d+\-"'],
	];
	if ($type) {
		return $__presence_specs[$type] ?? null;
	}
	return $__presence_specs;
}

function supported_presence(
	?string	 $site  = null,
	bool	 $utf8  = false
): string|false {
	static $__presence_specs = get_all_presences();
	foreach ($__presence_specs as $type => [$flags, $regex]) {
		if (($flags & PRFLG_OFFLINE)
		||	!preg_match("{$regex}i".($utf8 ? 'u' : ''), $site,)
		) {
			continue;
		}
		return $type;
	}
	return false;
}

function store_presence(
	?array $skip   = null,
	?int   $userid = null,
	bool   $utf8   = false,
): ?bool {
	require_once 'defines/youtube.inc';
	require_once '_site.inc';

	if (!isset($_POST['PRESENCE'])) {
		return false;
	}
	$element = $_REQUEST['sELEMENT'];
	$id		 = $_REQUEST['sID'];

	if (false === ($existing = db_simple_hash('presence', "
		SELECT SITE, PRESENCEID
		FROM presence
		WHERE ELEMENT = '$element'
		  AND ID = $id"))
	) {
		return false;
	}

	$userid ??= CURRENTUSERID;
	$presences = [];

	$start_time = microtime(true);

	foreach (preg_split('"[\r\n]+"'.($utf8? 'u' : ''), $_POST['PRESENCE']) as $line) {
		if (!($line = mytrim($line, utf8: $utf8))) {
			continue;
		}
		/** @noinspection NotOptimalIfConditionsInspection */
		if ((	str_contains($line, ' ')
			||	str_contains($line, "\t")
			)
		&&	preg_match('"(?:\s*(https?\S*)\s+)+"', $line)
		) {
			foreach (preg_split('"\s+"', $line) as $site) {
				if (!($site = mytrim($site, utf8: true))) {
					continue;
				}
				if (preg_match('"[\x00-\x1F\x7F-\xFF]"', $site)
				&&	($new_site = preg_replace_callback(
					'"[\x00-\x1F\x7F-\xFF]"',
					static fn($match): string => '%'.strtoupper(dechex(ord($match[0]))),
					$site))
				) {
					assert(is_string($new_site)); # Satisfy EA inspection
					mail_log("converted presence site $site to $new_site", error_log: 'DEBUG');
					$site = $new_site;
				}
				$presences[$site] = true;
			}
		} else {
			$presences[$line] = true;
		}
	}
	$ok_presences = [];
	foreach ($presences as $site => $ignored) {
		if (preg_match(
				'"^https?://(?:www\.)?facebook\.com/(?:'.
					'events/(?<eventid>\d+)(?:/?\?event_time_id=|/)(\d+)|'.
					'profile\.php\?id=(?<profile_id>\d+)'.
			')"i', $site, $match)
		) {
			if ($match['eventid']) {
				$ok_presences[$extra_site = "https://www.facebook.com/events/{$match['eventid']}/"] = $extra_site;
			} else {
				$site = "https://www.facebook.com/{$match['profile_id']}";
			}
		} elseif (
			preg_match('"^https?://www\.youtube\.com/(?!channel)"i', $site, $match)
		&&	false !== ($new_site = get_youtube_channel_url_from_any_url($site))
		) {
			$site = $new_site;
		}
		$ok_presences[$site] = $site;
	}

	$presences = $ok_presences;
	$webdriver = null;

	$show_infos = true; # SERVER_SANDBOX || SERVER_VIP;

	$show_infos && $infos[] = "$element:$id store_presence(1) taking ".(microtime(true) - $start_time);

	$presence = [];

	foreach ($presences as $site) {
		$show_infos && $infos[] = "$element:$id store_presence(2) taking ".(microtime(true) - $start_time)." starting with site $site";

		if (!($site = cleanup_presence_url($site, utf8: $utf8))) {
			continue;
		}

		if ($skip && isset($skip[base_url($site)])) {
			$presence[$site] = 0;
			continue;
		}

		$show_infos && $infos[] = "$element:$id store_presence(2.1) taking ".(microtime(true) - $start_time);

		$type = supported_presence($site);

		$show_infos && $infos[] = "$element:$id store_presence(2.2) taking ".(microtime(true) - $start_time);

		if ($element === 'party') {
			if ($type === 'facebook'
			&&	preg_match('"/events/(\d+)"', $site, $match)
			) {
				$fbids[$fbid = $match[1]] = "('$element', $id, $fbid, 'event')";
			}
		} elseif (in_array($type, ['facebook', 'facebookpage'])	) {
			if (str_contains($site, '/events/')) {
				register_warning('presence:warning:no_event_presence_on_non_event_LINE');
				continue;
			}
			$user_fbid = 0;
			$user_site = null;

			if (preg_match('"\bfacebook\.com/profile\.php\?id=(?P<fbid>\d+)"', $site, $match)) {
				$user_fbid = (int)$match['fbid'];
				$fbids[$user_fbid] = "('$element', $id, $user_fbid, 'user')";
				$user_site = "https://www.facebook.com/$user_fbid";
				$presence[$user_site] = 'facebook';
			}

			if ($webdriver !== false) {
				require_once '_fb.inc';
				$get_type = null;
				$use_site = $user_site ?? $site;

				$show_infos && $infos[] = "$element:$id store_presence(a) taking ".(microtime(true) - $start_time);

				$fbid = get_fbid_from_link($use_site, $get_type, $webdriver);

				$show_infos && $infos[] = "$element:$id store_presence(b) taking ".(microtime(true) - $start_time);

				if ($fbid
				&&	$fbid !== $user_fbid
				) {
					$fbids[$fbid] = "('$element', $id, $fbid, '".addslashes($get_type ?? '')."')";
					$site = "https://www.facebook.com/$fbid";
					$presence[$site] = $get_type === 'page' ? 'facebookpage' : 'facebook';

					if ($user_fbid) {
						if ($get_type === 'page') {
							if (!db_insert('fbid_identical', '
								INSERT IGNORE INTO fbid_identical SET
									STAMP	= '.CURRENTSTAMP.",
									PAGEID	= $fbid,
									FBID	= $user_fbid")
							) {
								return false;
							}
						}
						if (!empty($user_fbid)) {
							unset($fbids[$user_fbid]);
						}
						if (!empty($user_site)) {
							unset($presence[$user_site]);
						}
					}
				}
			}
		}

		$show_infos && $infos[] = "$element:$id store_presence(2.3) taking ".(microtime(true) - $start_time);

		$presence[$site] = $type;
		$have_presence[$type] = $type;
	}

	$show_infos && $infos[] = "$element:$id store_presence(3) taking ".(microtime(true) - $start_time);

	if (empty($have_presence['facebook'])
	&&	empty($have_presence['facebookpage'])
	) {
		if (!db_insert('fbid_log', '
			INSERT INTO fbid_log
			SELECT *, '.CURRENTSTAMP."
			FROM fbid
			WHERE ELEMENT = '$element'
			  AND ID = $id")
		||	!db_delete('fbid', "
			DELETE FROM fbid
			WHERE ELEMENT = '$element'
			  AND ID = $id")
		) {
			return false;
		}
	} elseif (
		!empty($fbids)
	&&	!db_insert('fbid','
		INSERT INTO fbid (ELEMENT, ID, FBID, TYPE)
		VALUES '.implode(', ', $fbids)."
		ON DUPLICATE KEY UPDATE TYPE = IF(VALUE(TYPE) = '', TYPE, VALUE(TYPE))")
	) {
		return false;
	}
	foreach ($presence as $site => &$supported) {
		if (!$supported) {
			register_warning(
				$supported === 0
			?	'party:warning:site_not_party_specific_LINE'
			:	'presence:warning:not_yet_supported_LINE', ['SITE' => $site]
			);
			if ($supported === 0) {
				unset($presence[$site]);
			}
		}
	}
	unset($supported);

	$show_infos && $infos[] = "$element:$id store_presence(4) taking ".(microtime(true) - $start_time);

	$new = array_diff_key($presence, $existing);
	$rem = array_diff_key($existing, $presence);

	if ($rem) {
		require_once '_site.inc';
		$remfb = [];
		foreach ($rem as $site => $presenceid) {
			if (str_contains($site, 'facebook.')) {
				$remfb[base_url($site)] =
					preg_match('"facebook\.com/events/(\d+)"', $site, $match)
				||	preg_match('"facebook\.com/(\d+)"', $site, $match)
				||	preg_match('"facebook\.com/.*?-(\d+)$"', $site, $match)
				?	[$presenceid, (int)$match[1]]
				:	[$presenceid, null];
			}
		}

		if ($new) {
			foreach ($new as $site => /* $type = */ $ignored) {
				unset($remfb[base_url($site)]);
			}
		}
		if (!empty($remfb)) {
			$remfbids = [];
			foreach ($remfb as /* $site => */ [/* $site, */ $fbid]) {
				if ($fbid) {
					$remfbids[] = $fbid;
				}
			}
			if ($remfbids) {
				$remfbidstr = implode(', ', $remfbids);
				require_once '_favourite.inc';
				if (is_favourable($element)) {
					if (false === ($userids = db_simpler_array(['fbid', 'fblikeids'], "
						SELECT DISTINCT USERID
						FROM fbid
						JOIN fblikeids USING (FBID)
						WHERE FBID IN ($remfbidstr)
						  AND ELEMENT = '$element'
						  AND ID = $id"
					))) {
						return false;
					}
					foreach ($userids as $inner_userid) {
						unmark_favourite($element, $id, $inner_userid, true);
					}
				}
				if (!db_insert('fbid_log', '
					INSERT INTO fbid_log
					SELECT *, '.CURRENTSTAMP. "
					FROM fbid
					WHERE FBID IN ($remfbidstr)
					  AND ELEMENT = '$element'
					  AND ID = $id")
				||	!db_delete('fbid', "
					DELETE FROM fbid
					WHERE FBID IN ($remfbidstr)
					  AND ELEMENT = '$element'
					  AND ID = $id")
				) {
					return false;
				}
			}
		}
		$rem_idstr = implode(', ', $rem);
		if (!db_insert('urlcheck', "
			INSERT INTO urlcheck_log
			SELECT * FROM urlcheck
			WHERE ELEMENT = 'presence'
			  AND ID IN ($rem_idstr)")
		||	!db_delete('urlcheck', "
			DELETE FROM urlcheck
			WHERE ELEMENT = 'presence'
			  AND ID IN ($rem_idstr)")
		||	!db_insert('fbid_log', '
			INSERT INTO fbid_log
			SELECT *, '.CURRENTSTAMP."
			FROM fbid
			WHERE ELEMENT = 'presence'
			  AND ID IN ($rem_idstr)")
		||	!db_delete('fbid', "
			DELETE FROM fbid
			WHERE ELEMENT = 'presence'
			  AND ID IN ($rem_idstr)")
		||	!db_insert('presence_log','
			INSERT INTO presence_log
			SELECT presence.*, '.CURRENTSTAMP.", $userid FROM presence
			WHERE PRESENCEID IN ($rem_idstr)")
		||	!db_delete('presence', "
			DELETE FROM presence
			WHERE PRESENCEID IN ($rem_idstr)")
		||	!db_delete('youtube_info', "
			DELETE FROM youtube_info
			WHERE PRESENCEID IN ($rem_idstr)")
		) {
			return false;
		}
	}

	$show_infos && $infos[] = "$element:$id store_presence(5) taking ".(microtime(true) - $start_time);

	if ($new) {
		$setlist = [];
		foreach ($new as $site => $type) {
			$setlist[] = "('$element', $id, '".addslashes($site)."', $userid, ".CURRENTSTAMP.', '.($type ? '1' : '0').',"'.($type ? addslashes($type) : '').'")';
		}
		foreach ($setlist as $setstr) {
			if (!db_insert('presence', '
				INSERT INTO presence (ELEMENT, ID, SITE, CUSERID, CSTAMP, SUPPORTED, TYPE)
				VALUES '.$setstr)
			||	!db_insert('urlcheck','
				INSERT INTO urlcheck SET
					ELEMENT	= "presence",
					ID		= '.db_insert_id())
			) {
				return false;
			}
		}
	}

	$show_infos && $infos[] = "$element:$id store_presence took ".($taken_time = microtime(true) - $start_time);

	if ($taken_time > 2) {
		error_log('store_presence took '.$taken_time.' seconds');
	}

	$show_infos && $taken_time > 2 && error_log(implode("\n", $infos));

	if ($rem || $new) {
		memcached_delete('presences:'.$element.':'.$id);
		return true;
	}
	return null;
}

function add_presence(string $element, int $id, string $type, string $uri): int|false {
	if (!db_getlock($key = "add_presence $element:$id:$type:$uri")) {
		return false;
	}
	if ($type === 'facebook'
	&&	$element === 'party'
	) {
		if (!preg_match('"/events/(?<event_id>\d+)"', $uri, $match)) {
			error_log('add_presence: facebook event uri not understood: '.$uri);
			return false;
		}
		if (!db_insert('fbid', "
			INSERT IGNORE INTO fbid SET
				ELEMENT	= 'party',
				ID		= $id,
				FBID	= {$match['event_id']},
				TYPE	= 'event'")
		) {
			return false;
		}
		if (!db_affected()) {
			error_log("already have presence for party $id and fbid {$match['event_id']}");
			return true;
		}
	}
	if (!db_insert('presence', "
		INSERT INTO presence SET
			ELEMENT	= '$element',
			ID		= $id,
			CSTAMP	= ".CURRENTSTAMP.',
			CUSERID	= '.CURRENTUSERID.",
			SITE	= '".addslashes($uri)."',
			TYPE	= '$type.'")
	||	!($presenceid = db_insert_id())
	||	!db_insert('urlcheck', "
		INSERT INTO urlcheck SET
			ELEMENT	= 'presence',
			ID		= $presenceid")
	) {
		return false;
	}
	db_releaselock($key);
	memcached_delete("presences:$element:$id");
	return $presenceid;
}

# remove_presence(element, id)
# remove_presence([presenceid, ...])
# remove_presence(presenceid)
function remove_presences(string|array $element, ?int $id = null): bool {
	$wherep =
		$id === null
	?	(	is_array($element)
		?	'	presence.PRESENCEID IN ('.implode(',', $element).')'
		:	'	presence.PRESENCEID='.$element
		)

	:	"	presence.ELEMENT = '$element'
		AND	presence.ID = $id";

	if (!db_insert('urlcheck','
		INSERT INTO urlcheck_log
		SELECT urlcheck.*
		FROM urlcheck
		JOIN presence ON PRESENCEID = urlcheck.ID
		WHERE urlcheck.ELEMENT = "presence"
		  AND '.$wherep)
	||	!db_delete('urlcheck','
		DELETE urlcheck
		FROM urlcheck
		JOIN presence ON PRESENCEID = urlcheck.ID
		WHERE urlcheck.ELEMENT = "presence"
		  AND '.$wherep)
	||	!db_delete('fbid','
		DELETE fbid
		FROM fbid
		JOIN presence ON PRESENCEID=fbid.ID
		WHERE fbid.ELEMENT="presence"
		  AND '.$wherep)
	||	!db_insert('presence_log','
		INSERT INTO presence_log
		SELECT presence.*,'.CURRENTSTAMP.','.CURRENTUSERID.'
		FROM presence
		WHERE '.$wherep)
	||	!db_delete('presence','
		DELETE FROM presence
		WHERE '.$wherep)
	) {
		return false;
	}
	if (!is_array($element)) {
		memcached_delete('presences:'.$element.':'.$id);
	}
	return true;
}

function presence_last_modified(): array {
	static $__lastmodified =
		($presences = have_presence())
		?	(	db_single_array('presence', 'SELECT CUSERID, CSTAMP FROM presence WHERE PRESENCEID = '.$presences['MAXID'])
			?:	[null, null]
			)
		:	[0, 0];
	return $__lastmodified;
}

function show_presence(bool $me = false, bool $lines = false, ?string $element = null, ?int $id = null): int {
	if (!($presences = have_presence($element, $id, $infos))) {
		return 0;
	}

	unset($presences['MAXID']);
	# Keys contain the single word identifier of the presences
	ksort($presences);

	$is_party = $_REQUEST['sELEMENT'] === 'party';

	$cnt = 0;

	if (isset($presences['spotify'])) {
		# showing presecenses, if we have both user/artist and playlist type links, keep only user/artist to display in icons
		static $__chosen_spotify = null;

		if ($__chosen_spotify !== null) {
			$presences['spotify'] = $__chosen_spotify;

		} elseif (($count = count($presences['spotify'])) === 1) {
			$__chosen_spotify = $presences['spotify'];

		} else {
			$spotifies = [];
			foreach ($presences['spotify'] as $site => $icon) {
				if ($type = get_type_from_spotify_link($site)) {
					$spotifies[$type][$site] = $icon;
				}
			}
			unset($spotifies['playlist']);
			if (!--$count) {
				$__chosen_spotify = [$site => $icon];
			} else {
				uksort($spotifies, function (string $type_a, string $type_b): int {
					static $__worths = [
						'artist'   => 0, # most preferred
						'user'	   => 1,
						'playlist' => 2,
						'track'	   => 3, # least preferred
					];
					return $__worths[$type_a] - $__worths[$type_b];
				});

				[$type, $sites_and_icons] = keyval($spotifies);

				$__chosen_spotify = $sites_and_icons;
			}

			$presences['spotify'] = $__chosen_spotify;
		}
	}

	foreach ($presences as $type => $sites) {
		if (is_bool($sites)) {
			mail_log('sites for type '.$type.' is bool', item: get_defined_vars());
			continue;
		}
		foreach ($sites as $site => $icon) {
			if ($lines) {
				?><div><?
			}
			if ($me) {
				$icon = str_replace('rel="', 'rel="me ', $icon);
			}

			if ($is_party
			&&	$type === 'facebook'
			&&	preg_match('"events/(\d+)"', $site, $match)
			) {
				mail_log('where does this strange print happen in show_presence?');

				$icon = str_replace('</a>', ob_get_clean().'</a>', $icon);
			}

/*			if ($multi_spotify
			&& 	($type = get_type_from_spotify_link($site))
			) {
				$short = match($type) {
					'artist'	=> 'a',
					'user'		=> 'u',
					'playlist'	=> 'pl',
					default		=> '',
				};
				$icon = str_replace('<a ', '<a class="seemtext" ', $icon);
				$icon = str_replace('</a>', '<sub class="abs">'.$short.'</sub></a>', $icon);
#				echo $type;
			}*/

			echo $icon;

			if ($lines) {
				?></div><?
			}
			++$cnt;
		}
	}

	if (!ROBOT) {
		foreach ($infos as /* $type => */ $more) {
			foreach ($more as /* $site => */ $info) {
				$views_presenceid[] = $info['PRESENCEID'];
			}
		}

		db_insupd('presence_view', '
		INSERT INTO presence_view (PRESENCEID, LAST_VIEW, LAST_CHECK)
		SELECT ID, UNIX_TIMESTAMP(), LAST_CHECK
		FROM urlcheck
		WHERE urlcheck.ELEMENT = "presence"
		  AND urlcheck.ID IN ('.implode(', ',$views_presenceid).')
		ON DUPLICATE KEY UPDATE LAST_VIEW = VALUES(LAST_VIEW)'
		);
	}

	return $cnt;
}

function get_presences(string $element, int $id): array {
	static $__presences;
	return	$__presences[$element][$id]
	??=		$__presences[$element][$id] = memcached_rowuse_hash('presence', "
			SELECT PRESENCEID, TYPE, SITE
			FROM presence
			WHERE ELEMENT = '$element'
			  AND ID = $id",
			key: 'presences:'.$element.':'.$id,
			flags: DB_NON_ASSOC
	)	?: [];
}

function have_presence(
	?string	$element		= null,
	?int	$id				= null,
	?array &$infos			= null,
	bool	$include_bad	= false,
	bool	$show_bubbles	= true,
): array {
	$element ??= $_REQUEST['sELEMENT'];
	$id		 ??= $_REQUEST['sID'];

	$show_bubbles = $show_bubbles && have_admin($element);

	static $__presence;
	static $__infos;
	if (isset($__presence[$element][$id])) {
		$infos = $__infos[$element][$id] ?? [];
		return $__presence[$element][$id];
	}
	if (!($sites = get_presences($element, $id))) {
		return $__presence[$element][$id] = [];
	}

	$__presence[$element][$id] = [];

	require_once '_urlcheck.inc';
	require_once '_jumper.inc';

	$show_offline =
		$include_bad
	||	(	$element === 'user'
		?	(	CURRENTUSERID === $id
			||	have_admin('helpdesk')
			)
		:	have_admin($element)
		);

	$maxid = 0;
	$done = [];

	foreach ($sites as $presenceid => [, $TYPE, $SITE]) {
		if (isset($done[$SITE])) {
			# sporadically, people enter same address twice
			continue;
		}
		$done[$SITE] = true;

		$status = get_url_status('presence', $presenceid);

		# FIXME: Both YouTube and Instagram cannot be checked for status currently using simple get pages.
		#		 Instagram recognizes us as a scraper and YouTube shows a cookie message.
		#		 Using youtube_info state, youtube can be done but there is no consistent single 'urlchecker'.
		if ($TYPE === 'youtube') {
			$offline = memcached_single('youtube_info','
				SELECT BADCNT >= 1
				FROM youtube_info
				WHERE PRESENCEID = '.$presenceid
			);
		} else {
			$offline = url_is_bad($status);
		}
		if ($offline && !$show_offline) {
			continue;
		}
		$offline_service = false;
		$is_fb = $TYPE === 'facebook' || $TYPE === 'facebookpage';

		foreach (get_all_presences() as $type => $presence_spec) {
			if (!$is_fb
			||	$type !== 'facebook'
			&&	$type !== 'facebookpage'
			) {
				if ($type !== $TYPE) {
					continue;
				}
			}

			if ($type === 'facebook'
			&&	$TYPE === 'facebookpage'
			) {
				$type = 'facebookpage';
			}
			if (!$offline) {
				[$flags] = $presence_spec;
				$offline_service = $offline = $flags & PRFLG_OFFLINE;
			}
			if ($offline
			&&	!$show_offline
			) {
				continue;
			}

			[$flags, $regex] = $presence_spec;
			if (!preg_match('{$regex}i', $SITE)) {
				continue;
			}

			ob_start();
			?><a<?
			if ($offline) {
				?> class="light"<?
			}
			?> target="_blank"<?
			?> title="<?= escape_specials($type) ?>: <?= escape_specials($SITE) ?>"<?
			?> href="<?= jump_to('presence', $presenceid) ?>"<?
			?> rel="nofollow"><?
			if ($offline
			&&	$show_bubbles
			) {
				require_once '_bubble.inc';
				$bubble = new bubble(BUBBLE_CLEAN);
				$bubble->catcher();
			}
			show_presence_icon($type, $presence_spec);
			if ($offline
			&&	$show_bubbles
			) {
				$bubble->content();
				if (empty($offline_service)) {
					if (!$status) {
						error_log('no status for presence '.$presenceid.': '.$SITE);
					} else {
						show_url_bubble($status, $SITE, 'presence', $presenceid);
					}
				} else {
					?><div><?= __('presence:info:service_is_offline_LINE') ?></div><?
				}
				$bubble->display();
			}
			?></a><?
			$__presence[$element][$id][$type][$SITE] = $iconstr = ob_get_clean();
			$infos[$type][$SITE] = [
				'OFFLINE'		=> $offline,
				'PRESENCEID'	=> $presenceid,
				'SITE'			=> $SITE,
				'ICON'			=> $iconstr
			];
			if ($presenceid > $maxid) {
				$maxid = $presenceid;
			}
			break;
		}
	}
	if ($maxid) {
		$__presence[$element][$id]['MAXID'] = $maxid;
		$__infos[$element][$id] = $infos ?? [];
	}
	return $__presence[$element][$id];
}

function show_presence_form_row(
	?array  $info  = null,
	?string $class = null,
	?int	$srcid = null,
): bool {
	$element = $_REQUEST['sELEMENT'];
	if ($id = ($srcid ??= $_REQUEST['sID'])) {
		if (false === ($sites = db_simpler_array('presence', "
			SELECT SITE
			FROM presence
			WHERE ELEMENT = '$element'
			  AND ID = $id
			ORDER BY SITE"
		))) {
			return false;
		}
	}
	layout_restart_row(rowuserdef: $class);
	$event = false;
	switch ($element) {
	case 'user':
		$is_user = true;
		require_once '_bubble.inc';
		$bubble = new bubble(BUBBLE_BLOCK | HELP_BUBBLE);
		$bubble->catcher_and_title(Eelement_plural_name('social_link'));
		$bubble->content(__('presence:info:bubble_TEXT', DO_NL2BR));
		$bubble->display();
		break;

	case 'party':
		$event = true;
		?><div><?=
			Eelement_plural_name('source_link')
		?> <div class="ib colorless"><?=
		get_facebook_icon() ?> <?
		show_presence_icon('instagram'); ?> <?
		show_presence_icon('residentadvisor');
		?></div><?
		?></div><?
		break;

	default:
		echo Eelement_plural_name('social_link');
		break;
	}
	layout_field_value();

	if (!empty($sites)) {
/*		$__bare_hosts = [];
		NOTE: Overly complex and doesn't seem to do what it should, order sites on main domain.
			  Just use regular sorting for now.

		usort($sites, static function (string $url_one, string $url_two) use (&$__bare_hosts): int {
			foreach (['url_one', 'url_two'] as $url_var) {
				if (isset($__bare_hosts[$$url_var])) {
					continue;
				}
				$bare_host = parse_url($$url_var, PHP_URL_HOST);
				if (preg_match('"^(?:(?<subdomain>[a-z\d_\-\.]+)\.)?(?<domain>[^.]+)\.(?<toplevel>[a-z]{2,})$"i', $bare_host, $match)) {
					$bare_host = $match['domain'].'-'.$match['subdomain'].'.'.$match['toplevel'];
				}
				$__bare_hosts[$$url_var] = $bare_host;
			}
			return $__bare_hosts[$url_one] <=> $__bare_hosts[$url_two];
		});*/
		$content = escape_ascii(implode("\n", $sites))."\n";
	} else {
		$content = null;
	}

	$show_check_dups = ($element !== 'user');

	include_js('js/form/presence');

	?><textarea<?
	?> class="growToFit"<?
	?> name="PRESENCE"<?
	?> cols="80"<?
	?> rows="<?= empty($sites) ? '2' : max(2, count($sites) + 1) ?>"<?
	if ($show_check_dups) {
		?> onkeyup="Pf_checkPresenceDups(this, '<?= $element ?>', <?= $id ?>);"<?
	}
	?> ondrop="Pf_dropPresence(event, this);"<?
	if (empty($event)) {
		?> placeholder="<?= __('presence:info:link_to_profile_here') ?>"<?
	}
	?>><?= $content ?></textarea><?

	if ($show_check_dups) {
		?><div id="fbtopfstore"></div><?
	}

	if (isset($is_user)) {
		layout_next_cell(class: 'right');
		_visibility_display_form_part($info, 'PRESENCE');
	}
	return true;
}

function get_presence_icon(string $service, ?string $class = null): string {
	require_once '_presence.inc';
	ob_start();
	show_presence_icon($service, class: $class);
	return ob_get_clean();
}

function show_presence_icon(
	string	$type,
	?array	$presence_spec	= null,
	?string	$class			= null
): void {
	require_once '_layout.inc';
	$flags =
		$presence_spec
	?	$presence_spec[0]
	:	get_presence_flags($type);

	if ($type === 'twitter') {
		$type = 'x';
		$flags = PRFLG_THEMED;
	}

	$x2_url =
		$type === 'partyflock'
	?	get_favicon()
	:	STATIC_HOST.'/presence/'.$type.
		($flags & PRFLG_THEMED ? '_'.CURRENTTHEME : '').
		($flags & PRFLG_V2 ? '_v2' : '').
		($flags & PRFLG_2x ? '@2x' : '').
		(webp_supported() ? '.webp' : '.png');

	$x1_url = str_replace('@2x', '', $x2_url);

	?><img<?
	?> loading="eager"<?
	?> alt="<?= escape_specials($type) ?>"<?
	?> class="<?= $class ? $class.' ' : ''  ?>presence"<?
	?> src="<?= is_high_res() ? $x2_url : $x1_url ?>"<?
	if ($x1_url !== $x2_url) {
		?> srcset="<?= $x1_url ?> 1x, <?= $x2_url ?> 2x"<?
	}
	?> /><?
}

function move_site_to_presence(
	string|array|null	$site_fields	= null,
	bool		  		$utf8			= false
): void {
	foreach (is_array($site_fields) ? $site_fields : ['SITE'] as $site_field) {
		if (empty($_POST[$site_field])
		||	!supported_presence($_POST[$site_field])
		) {
			continue;
		}
		$_POST['PRESENCE'] =
			!empty($_POST['PRESENCE'])
		?	$_POST['PRESENCE']."\n".$_POST[$site_field]
		:	$_POST[$site_field];

		register_warning('presence:warning:site_moved_to_presence_LINE', [$site_field => $_POST[$site_field]]);
		$_POST[$site_field] = '';
	}
}

function show_presence_row(deflist $list, bool $me = false): bool {
	if (!have_presence()) {
		return false;
	}
	ob_start();
	$cnt = show_presence($me);
	$list->set_row_class('presencerow');
	$list->add_row(Eelement_name('link', $cnt), ob_get_clean());
	return true;
}

function link_to_facebook_events(string $element, int $id): void {
	$presences = have_presence($element, $id);

	if (!isset($presences['facebook'])
	&&	!isset($presences['facebookpage'])
	) {
		return;
	}
	$site = key($presences[isset($presences['facebookpage']) ? 'facebookpage' : 'facebook']);

	if (preg_match('"facebook\.com/(?<path>(?:pages|pg)/[^/]*/\d+.*)$"', $site, $match)) {
		mail_log('not sure how to handle this facebook page link: '.$site, error_log: 'WARNING');
		$path = $match['path'];
	} elseif (preg_match('"facebook\.com/.*-(?<fbid>\d+)$"', $site, $match)) {
		$path = $match['fbid'].'?sk=events';
	} elseif (preg_match('"facebook\.com/profile\.php\?.*?id=(?<fbid>\d+)"', $site, $match)) {
		$path = $match['fbid'].'?v=events';
	} elseif (preg_match('"facebook\.com/(.*)(?:\?.*)?$"', $site, $match)) {
		$path = rtrim($match[1],'/').'/events';
	} else {
		return;
	}
	?> <a<?
	?> target="_blank"<?
	?> class="light colorless colorfull-hover"<?
	?> href="https://www.facebook.com/<?= $path ?>"><?
	show_presence_icon('facebook');
	?></a><?
}

function link_to_instagram(string $element, int $id): void {
	$presences = have_presence($element, $id);

	if (!isset($presences['instagram'])) {
		return;
	}
	$site = key($presences['instagram']);

	?> <a target="_blank" class="light colorless colorfull-hover" href="<?= $site ?>"><?
	show_presence_icon('instagram');
	?></a><?
}

function cleanup_presence_url(string $site, bool $utf8 = false): string {
	require_once '_flockmod.inc';
	require_once '_helper.inc';
	require_once '_url.inc';
	require_once '_site.inc';

	$utf8_mod = $utf8 ? 'u' : '';

	# Remove any service name in front of URL:
	if (preg_match('"^[a-z_-]+\s*:\s*(?<site>http.*?)\s*$"'.$utf8_mod, $site, $match)) {
		$site = $match['site'];
	}
	if (false !== stripos($site, 'facebook.com')) {
		# Store only the ID that belongs to the current day of multi-day event
		if (preg_match('"\bfacebook\.com/events/(?<fbid>\d+)(?:/|\b|$)"'.$utf8_mod, $site, $match)) {
			$site = "https://www.facebook.com/{$match['fbid']}";
		}
		if (preg_match('"^(?<prefix>[^/]*facebook[^/]*)/(?:events|timeline|info)[/\-](?<fbid>\d+)?(?<path>.*)$"i'.$utf8_mod, $site, $match)) {
			$site = $match['prefix'].'/'.($match['fbid'] ?: $match['path']);
		}
		if ($new_site = preg_replace('"/(?:info|about|videos|photos)/?$"'.$utf8_mod, '', $site)) {
			$site = $new_site;
		}
	}
	# Recognize some well-known services
	# and entry of the name of the service in front of the account name or URL.
	$match_service = '
		(?<service>'.
			'insta(?:gram)?|'.
			'telegram|'.
			'snap(?:chat)?|'.
			'soundcloud|'.
			'tiktok|'.
			"what'?sapp".
	 	')';
	if (preg_match('"\b'.$match_service.'[\s:;*]+(?<account_or_url>.+?)\s*$"i'.$utf8_mod, $site, $match)
	||	preg_match('"(?<account_or_url>[^\s@]+)\s*@\s*'.$match_service.'"i'.$utf8_mod, $site, $match)
	) {
		if (preg_match('"^https?://"i'.$utf8_mod, $match['account_or_url'])) {
			$site = $match['account_or_url'];
		} else {
			//$account = myrtrim($match['account_or_url'], '/', $utf8);
			switch (strtolower($match['service'])) {
			case 'snap':
			case 'snapchat':
				$site = 'https://www.snapchat.com/add/'.$match['account'];
				break;

			case 'insta':
			case 'instagram':
				$site = 'https://www.instagram.com/'.ltrim($match['account'], '@');
				break;

			case 'soundcloud':
				$site = 'https://soundcloud.com/'.$match['account'];
				break;

			case 'telegram':
				$site = 'https://t.me/'.$match['account'];
				break;

			case 'tiktok':
				$site = 'https://www.tiktok.com/'.($match['account'][0] === '@' ? '' : '@').$match['account'];
				break;

			case 'whatsapp':
				if (!preg_match('"^\h*\+(?<phone>[\s\d\h()-]+)$"', $match['account'], $new_match)) {
					register_error('presence:error:whatsapp_needs_full_phone_with_country_code_LINE');
					return $site;
				}
				$site = 'https://wa.me/'.preg_replace('"\D+"', '', $new_match['phone']);
				break;
			}
		}
	} elseif (preg_match('"partyflock\.nl/(?!user/)(?<path>.*)$"'.$utf8_mod, $site, $match)) {
		require_once '_search_via_url.inc';
		if ($tmp_userid = find_nick($match['path'])) {
			$site = FULL_HOST.get_element_href('user', $tmp_userid);
		}
	}
	return make_proper_site($site);
}

function show_presence_search(?array $item = null): void {
	if (!$item
	||	(	!have_admin($_REQUEST['sELEMENT'])
		&&	!is_aspiring_content_admin()
		)
	) {
		return;
	}

	?><div class="block"><?=
		__C('action:search_on') ?> <?

	if (!isset(USE_UNICODE[$_REQUEST['sELEMENT']])) {
		$item['NAME'] = win1252_to_utf8($item['NAME']);
	}
	$item['NAME'] = $item['NAME'] ?: ($item['TITLE'] ?? '');
	$enc = urlencode($item['NAME']);
	$raw_enc = rawurlencode($item['NAME']);
	//$types = !empty($item['TYPE']) ? explode_to_hash(',',$item['TYPE']) : null;

	$presences = have_presence(infos: $infos);
	# Prefixing 'dj' often yields empty page on instagram

	?> <a<?
	?> class="colorhover lpad"<?
	?> target="_blank"<?
	?> href="https://www.google.com/search?q=&quot;<?= $enc ?>&quot;"<?
	?>><? show_presence_icon('google') ?></a><?

	if (!isset($presences['facebookpage'])
	||	!($site = key($presences['facebookpage']))
	||	$infos['facebookpage'][$site]['OFFLINE']
	) {
		?> <a<?
		?> class="colorhover lpad"<?
		?> target="_blank" href="https://www.facebook.com/search/top/?q=<?= $enc ?>"><?= get_facebook_icon() ?></a><?
	}

	if (!isset($presences['soundcloud'])
	||	!($site = key($presences['soundcloud']))
	||	$infos['soundcloud'][$site]['OFFLINE']
	) {
		?> <a<?
		?> class="colorhover lpad"<?
		?> target="_blank" href="https://soundcloud.com/search/people?q=<?= $enc ?>"><? show_presence_icon('soundcloud') ?></a><?
	}

	if (!isset($presences['instagram'])
	||	!($site = key($presences['instagram']))
	||	$infos['instagram'][$site]['OFFLINE']
	) {
		?> <a<?
		?> class="colorhover lpad"<?
		?> target="_blank"<?
		?> href="https://www.google.com/search?q=&quot;<?= $enc ?>&quot;+site%3Ainstagram.com"><? show_presence_icon('instagram') ?></a><?
	}

	if (!isset($presences['youtube'])
	||	!($site = key($presences['youtube']))
	||	$infos['youtube'][$site]['OFFLINE']
	) {
		?> <a<?
		?> class="colorhover lpad"<?
		?> target="_blank"<?
		?> href="https://www.youtube.com/results?search_query=<?= $enc ?>&amp;sp=EgIQAg%253D%253D"><? show_presence_icon('youtube') ?></a><?
	}

	if (!isset($presences['itunes'])
	||	!($site = key($presences['itunes']))
	||	$infos['itunes'][$site]['OFFLINE']
	) {
		?> <a<?
		?> class="colorhover lpad"<?
		?> target="_blank"<?
		?> href="https://music.apple.com/search?term=<?= $enc ?>"><? show_presence_icon('itunes') ?></a><?
	}

	if (!isset($presences['spotify'])
	||	!($site = key($presences['spotify']))
	||	$infos['spotify'][$site]['OFFLINE']
	) {
		?> <a<?
		?> class="colorhover lpad"<?
		?> target="_blank"<?
		?> href="https://open.spotify.com/search/<?= $raw_enc,  match($_REQUEST['sELEMENT']) {
			'artist'		=> '/artists',
			'organization'	=> '/users',
			default			=> '',
		}
		?>"><? show_presence_icon('spotify') ?></a><?
	}
	if (!isset($presences['itunes'])
	||	!($site = key($presences['itunes']))
	||	$infos['itunes'][$site]['OFFLINE']
	) {
		?> <a<?
		?> class="colorhover lpad"<?
		?> target="_blank"<?
		?> href="https://music.apple.com/search?term=<?= $raw_enc ?>"<?
		?>><? show_presence_icon('itunes') ?></a><?
	}
	if (!isset($presences['tiktok'])
	||	!($site = key($presences['tiktok']))
	||	$infos['tiktok'][$site]['OFFLINE']
	) {
		?> <a<?
		?> class="colorhover lpad"<?
		?> target="_blank"<?
		?> href="https://www.tiktok.com/search/user?q=<?= $raw_enc ?>"><? show_presence_icon('tiktok') ?></a><?
	}

	if (!isset($presences['residentadvisor'])
	||	!($site = key($presences['residentadvisor']))
	# NOTE: resident always resturns 403, we're detect as a bad bot I presume
	# ||	$infos['residentadvisor'][$site]['OFFLINE']
	) {
		?> <a<?
		?> class="colorhover lpad"<?
		?> target="_blank"<?
		?> href="https://www.google.com/search?q=&quot;<?= $enc ?>&quot;+site:ra.co"><? show_presence_icon('residentadvisor') ?></a><?
	}

	if (!isset($presences['discogs'])
	||	!($site = key($presences['discogs']))
	||	$infos['discogs'][$site]['OFFLINE']
	) {
		?> <a<?
		?> class="colorhover lpad"<?
		?> target="_blank"<?
		?> href="https://www.discogs.com/search/?q=&quot;<?= $enc ?>&quot;&type=<?=
			match($_REQUEST['sELEMENT']) {
				'artist'		=> 'artist',
				'organization'	=> 'label',
				default			=> 'all'
			}
		?>"><? show_presence_icon('discogs') ?></a><?
	}
	?></div><?
}

function get_type_from_spotify_link(string $site): ?string {
	if (preg_match('"spotify\.com/(?P<type>a-z\d_+)/"i', $site, $match)) {
		return $match['type'];
	}
	return null;
}
