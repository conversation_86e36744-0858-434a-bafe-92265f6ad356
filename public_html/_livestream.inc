<?php

require_once 'defines/youtube.inc';

function get_embeddable_livestream($address,&$id = null) {
	if (preg_match('"^https?://(?:www\.)?twitch\.(?:tv|net|com)/([\w\d\_]+)"',$address,$match)) {
		return 'https://www.twitch.tv/'.($id = $match[1]);
	}
	if (youtube_match_video_url($address, $match)) {
		return 'https://www.youtube.com/watch?v='.($id = $match['youtubeid']);
	}
	if (preg_match('"^https?://(?:\w+\.)?youtube\.\w{2,3}/embed/videoseries\?list=([\w\d\_\-]+)"',$address,$match)) {
		return 'https://www.youtube.com/embed/videoseries?list='.$match[1];
	}
	if (preg_match('"^https?://(?:\w+\.)*facebook.com/\w+/videos/(\d+)/"',$address,$match)
	) {
		return 'https://www.facebook.com/'.($id = $match[1]);
	}
	if (preg_match('"https?://player.vimeo.com/video/(\d+)"',$address,$match)
	) {
		return 'https://player.vimeo.com/video/'.($id = $match[1]);
	}
	return null;
}
