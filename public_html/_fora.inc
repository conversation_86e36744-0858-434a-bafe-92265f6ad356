<?php

require_once '_forum.inc';
require_once '_memcache.inc';

#define('INCLUDE_CLOSED_FORA',	1);

function get_fora($arg = null,$flags = 0) {
	$wherep = $arg
	?	(	is_array($arg)
		?	' WHERE FORUMID IN ('.(isset($arg[0]) ? implode(',',$arg) : implodekeys(',',$arg)).')'
		:	' WHERE FORUMID IN ('.$arg.')'
		)
	: null;
#	if (!($flags & INCLUDE_CLOSED_FORA)) {
		$wherep = ($wherep ? $wherep.' AND ' : ' WHERE ').'NOT (FLAGS&'.FORUMFLAG_CLOSED.')';
#	}
	return memcached_simple_hash(
		'forum','
		SELECT FORUMID,NAME
		FROM forum'.
		$wherep.'
		ORDER BY NAME ASC',
		HALF_HOUR
	);
}
