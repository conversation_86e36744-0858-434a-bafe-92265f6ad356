<?php

const UCHK_REALNAME		= 0x1;
const UCHK_ADDRESS		= 0x2;
const UCHK_PHONE		= 0x4;
const UCHK_GENDER		= 0x8;
const UCHK_BIRTHDATE	= 0x10;

function missing_personal(?array $user = null, int $flags = 0): bool|null|array|string {
	if (!$user) {
		$user = db_single_assoc(['user', 'city'],'
			SELECT SQL_NO_CACHE
				ADDRESS,
				BIRTH_DAY,
				BIRTH_MONTH,
				BIRTH_YEAR,
				CITY,
				EMAIL,
				FIND_BY_EMAIL,
				FIND_BY_REALNAME,
				PHONE,
				REALNAME,
				SEX,
				ZIPCODE,
				user.CITYID, city.COUNTRYID
			FROM user
			LEFT JOIN city USING (CITYID)
			WHERE user.USERID = '.CURRENTUSERID
		);
		if (!$user) {
			if ($user === null) {
				register_error('user:error:nonexistent_LINE', ['ID' => CURRENTUSERID]);
			}
			return true;
		}
	}
	if (!email_has_mailserver($user['EMAIL'])) {
		return 'email';
	}
	if ($flags & UCHK_REALNAME) {
		if (!$user['REALNAME']) {
			return 'realname';
		}
		if (!preg_match('"^\s*(\w{2,})[\s\-]+(?:.*?)+(\w{2,})\s*$"', $user['REALNAME'])) {
			return ['realname', 'realname_inclusing_surname'];
		}
	}
	if ($flags & UCHK_ADDRESS) {
		if (!$user['ADDRESS']) {
			return 'address';
		}
		if (!$user['CITYID']
		&&	!$user['CITY']
		) {
			return 'cityid';
		}
		if (!$user['ZIPCODE']) {
			return 'zipcode';
		}
		switch ($user['COUNTRYID']) {
		case 1:	# NL
			$valid = preg_match('!^\s*\d{4}\s*[a-zA-Z]{2}(?:[^a-zA-z]+.*)?$!', $user['ZIPCODE']);
			break;
		case 3; # UK
			$valid = preg_match('!^\s*[a-z](?:\d|[\da-z]\d|\d[a-z]|[a-z]\d[\da-z])\s*\d[a-z]{2}\s*$!i', $user['ZIPCODE']);
			#			  1	   2  3		 3	   4
			break;
		case 2:	# DE
		case 5:	# IT
		case 10:# ES
		case 13:# FR
			$valid = preg_match('!^\s*\d{5}\s*$!', $user['ZIPCODE']);
			break;
		case 6:	# BE
		case 26:# LU
		case 29:# DK
			$valid = preg_match('!^\s*\d{4}\s*$!', $user['ZIPCODE']);
			break;
		default:
			$valid = false;
		}
		if (!$valid) {
			return ['zipcode', 'valid_zipcode'];
		}
	}
	if ($flags & UCHK_PHONE) {
		if (!$user['PHONE']) {
			return 'phone';
		}
		$info = count_chars($user['PHONE'], 1);
		if ($info) {
			$totalcnt = 0;
			foreach ($info as $char => $cnt) {
				switch ($char) {
				case ord('0'):
				case ord('1'):
				case ord('2'):
				case ord('3'):
				case ord('4'):
				case ord('5'):
				case ord('6'):
				case ord('7'):
				case ord('8'):
				case ord('9'):
					$totalcnt += $cnt;
					break;
				}
			}
			if ($totalcnt < 10) {
				return ['phone', 'valid_phone'];
			}
		}
		if (!require_phone_or_empty($user, 'PHONE')) {
			return ['phone', 'valid_phone'];
		}
	}
	if ($flags & UCHK_GENDER) {
		if (!$user['SEX']) {
			return 'gender';
		}
	}
	if ($flags & UCHK_BIRTHDATE) {
		if (!$user['BIRTH_YEAR']) {
			return 'birthdate';
		}
		if ($user['BIRTH_MONTH']
		&&	$user['BIRTH_DAY']
		&&	!checkdate(
				$user['BIRTH_MONTH'],
				$user['BIRTH_DAY'],
				$user['BIRTH_YEAR'])
		) {
			return ['birthdate', 'valid_birthdate'];
		}
	}
	return null;
}

function get_missing_personal_message(bool|array|null|string $missing = null, ?int $userid = null): ?string {
	if (!$missing) {
		return null;
	}
	if ($missing === true) {
		return __C('error:generic_LINE');
	}
	$userid ??= CURRENTUSERID;
	if (is_scalar($missing)) {
		$hash = $missing;
	} else {
		$hash = $missing[0];
		$missing = $missing[1];
	}
	return __C('user:missing_personal:'.$missing, DO_UBB,  ['LINK' => '/user/'.$userid.'/form#'.$hash]);
}
