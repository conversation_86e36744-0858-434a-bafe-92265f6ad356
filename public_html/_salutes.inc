<?php

function store_email_salute(int $ticketid, int $ctmsgid, string $to_mail): void {
	if (CLI
	||	empty($_POST['HEADER'])
	||	$_POST['HEADER'] === getifset($_POST, 'ORIG_HEADER')
	) {
		return;
	}
	if (!preg_match('"^\s*(?:H[oa]?i|Ha(?:llo)?|Beste|Dear)\s+(.*)$"iu', $_POST['HEADER'], $match)) {
		$salutes = db_simpler_array('contact_signatures','SELECT DISTINCT HEADER FROM contact_signatures');
		if ($salutes === false) {
			return;
		}
		if ($salutes) {
			foreach ($salutes as $salute) {
				if (str_contains($salute, '#NAAM#')) {
					$salute = preg_quote($salute,'"');
					$salute = str_replace('#NAAM#', '(.*)', $salute);
					if (preg_match('"^'.$salute.'$"iu', $_POST['HEADER'], $match)) {
						$found = true;
						break;
					}
				}
			}
		}
		if (!isset($found)) {
			return;
		}
	}
	if (!($name = utf8_mytrim($match[1], ',.:!?'))
	||	mb_strlen($name) === 1
	) {
		return;
	}
	db_insert('email_salute','
	INSERT INTO email_salute SET
		NAME	= "'.addslashes(utf8_ucfirst(win1252_to_utf8($name))).'",
		EMAIL	= "'.addslashes(win1252_to_utf8($to_mail)).'",
		TICKETID= '.$ticketid.',
		CTMSGID	= '.$ctmsgid.',
		STAMP	= '.CURRENTSTAMP.',
		USERID	= '.CURRENTUSERID
	);
}

function get_email_salute(string $email, ?string $name = '', ?array $template = null, bool $utf8 = false): string {
	# utf8 parameters!
	$stored_name = db_single('email_salute','
		SELECT NAME
		FROM email_salute
		WHERE EMAIL = "'.addslashes($email).'"
		ORDER BY USERID = '.CURRENTUSERID.' DESC, STAMP DESC'
	);

	if ($stored_name || $name) {
		$utf8 = $utf8 || $stored_name;
		$utf8_mod = $utf8 ? 'u' : '';

		$use_name = $stored_name ?: $name;

		[$first_name] = preg_split("'[\s\.@_]'".$utf8_mod, $use_name);

	} else {
		# make utf8 default, although email cannot contain utf8
		$utf8 = true;
		$utf8_mod = 'u';

		require_once '_email.inc';
		$use_name = get_name_from_email($email);

		if (!empty($template['tTYPE'])
		&&	!empty($template['tID'])
		) {
			if ($connected_name = db_single($template['tTYPE'], '
				SELECT NAME
				FROM `'.($slashed_table = addslashes($template['tTYPE'])).'`
				WHERE '.$slashed_table.'ID = '.$template['tID']
			)) {
				if (mb_strtolower(str_replace($connected_name, ' ', ''))
				=== mb_strtolower(str_replace($use_name, ' ', ''))
				) {
					$use_name = $connected_name;
					$from_element = true;
				}
			}
		}
		$first_name = $use_name;
	}

	if ($first_name === null) {
		mail_log('first_name is null', item: get_defined_vars(), subject: 'get_email_salute first_name null');
		return '';
	}
	$first_name = mytrim($first_name, ' ,.!?', $utf8);
	if (!isset($from_element)) {
		$first_name = ($utf8 ? 'mb_strtolower' : 'strtolower')($first_name);
		$first_name = my_ucfirst($first_name, $utf8);
	}

	# always returns win1252 for now

	return $utf8 ? utf8_to_win1252($first_name) : $first_name;
}

function get_all_contact_signatures(): array {
	$signatures = db_rowuse_hash('contact_signatures','
		SELECT ELEMENT,FROMNAME,HEADER,HEADER_LS,FOOTER
		FROM contact_signatures
		WHERE ENABLED=b\'1\'
		  AND USERID='.CURRENTUSERID
	);
	if (!isset($signatures['default'])) {
		$signatures['default'] = generate_contact_signature();
	}
	return $signatures;
}

function get_contact_signature(string $element): array|null|false {
	static $__signatures;
	return	$__signatures[$element][CURRENTUSERID]
	??=	query_contact_signature($element);
}

function query_contact_signature(string $element): array|null|false {
	$signature = db_single_assoc('contact_signatures','
		SELECT FROMNAME, HEADER, HEADER_LS, FOOTER, ELEMENT, ENABLED
		FROM contact_signatures
		WHERE ELEMENT = "'.addslashes($element).'"
		  AND USERID = '.CURRENTUSERID.'
		  AND ENABLED = 1'
	);
	return	$signature === false
	?	false
	:	($signature ?? false);
}

function generate_contact_signature(): array {
	$user = memcached_single_array('user', 'SELECT SEX, REALNAME FROM user WHERE USERID = '.CURRENTUSERID);
	if ($user) {
		[$gender, $name] = $user;
		$name = $name ? win1252_to_utf8($name) : '';
	} else {
		$gender = null;
	}
	if (!isset($name)) {
		global $currentuser;
		$name = win1252_to_utf8($currentuser->row['NICK']);
	}

	[$first_name] = preg_split("'[\s\.@_]'u", $name);
	$first_name = utf8_mytrim($name, " ,.!?\r\n\t\xb");
	$first_name = utf8_ucfirst($first_name);

	return ['GENERATED'	=>	true,
			'FROMNAME'	=>	$first_name,
			'FOOTER'	=>	"Met vriendelijke groet\n$first_name,\nmedewerk".($gender === 'F' ? 'ster' : 'er')." Partyflock",
			'HEADER'	=>	'Hi #NAAM#.',
			'HEADER_LS'	=>	'Hi',
	];
}
