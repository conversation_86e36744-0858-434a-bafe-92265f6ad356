<?php

function get_offense_points($userid,$offenseid = 0,$offensecstamp = CURRENTSTAMP) {
	$beforepart = $offenseid ? ' AND OFFENSEID<='.$offenseid : null;
	require_once 'defines/offense.inc';
	$pts = db_single_assoc('offense','
		SELECT	COALESCE(SUM(POINTS),0) AS TOTAL,
			COALESCE(SUM(IF(CSTAMP>'.($offensecstamp - get_offense_defines($offensecstamp,'OFFENSE_ACTIVE_PERIOD')).',POINTS,0)),0) AS ACTIVE
		FROM offense
		WHERE REMOVED=0
		  AND REGONLY=0
		  AND USERID='.$userid.$beforepart,
		DB_USE_MASTER
	);
	if ($pts === false) {
		return false;
	}
	$lastban = db_single_assoc('banpoint','
		SELECT OFFENSEID,DONEPTS
		FROM banpoint
		WHERE USERID='.$userid.$beforepart.'
		ORDER BY OFFENSEID DESC
		LIMIT 1',
		DB_USE_MASTER
	);
	if ($lastban === false) {
		return false;
	}
	if (!$lastban) {
		$pts['NOTDONEPTS'] = $pts['ACTIVE'];
		return $pts;
	}
	$notdonepts = db_single('offense','
		SELECT SUM(POINTS)
		FROM offense
		WHERE USERID='.$userid.$beforepart.'
		  AND REMOVED=0
		  AND REGONLY=0
		  AND OFFENSEID>='.$lastban['OFFENSEID']
	);
#	print_rr($notdonepts,$qstr);
	if ($notdonepts === false) {
		return;
	}
	$pts['NOTDONEPTS'] = $notdonepts - $lastban['DONEPTS'];
	return $pts;	
}
