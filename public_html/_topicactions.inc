<?php

require_once 'defines/post.inc';
require_once 'defines/topic.inc';
require_once '_pollstatus.inc';

function set_topic_status($topic,$topicid,$status,$isadmin = false,$showmsgs = true): bool {
	require_once '_quote.inc';
	if (!db_getlock($key = $topic.':meta:'.$topicid,60)) {
		return false;
	}
	if (!db_insert($topic.'_log','
		INSERT INTO '.$topic.'_log
		SELECT * FROM '.$topic.'
		WHERE STATUS!="'.$status.'"
		  AND TOPICID='.$topicid)
	) {
		db_releaselock($key);
		return false;
	}
	if (!db_affected()) {
		db_releaselock($key);
		if ($showmsgs) {
			$arg = array('TOPICID'=>$topicid,'ELEMENT'=>$topic);
			switch ($status) {
			case 'open':
			case 'closed':
			case 'hidden':	register_warning('topicactions:warning:already_'.$status.'_LINE', DO_UBB, $arg);
			default:	register_warning('topic:warning:not_changed_LINE', DO_UBB, $arg);
			}
		}
		return false;
	}
	if (!db_update($topic,'
		UPDATE '.$topic.' SET 
			MSTAMP	='.CURRENTSTAMP.',
			MUSERID	='.CURRENTUSERID.',
			STATUS	="'.$status.'"'.
			(($altid = have_idnumber($_REQUEST,'ALTID')) && $isadmin ? ',ALTID='.$altid : null).
			($isadmin ? ',FLAGS=FLAGS|'.TOPIC_CHANGED_BY_ADMIN : null).'
		WHERE TOPICID='.$topicid)
	) {
		db_releaselock($key);
		return false;
	}
	memcached_delete($topic.':'.$topicid);
	require_once '_sphinx.inc';
	sphinx_update_element($topic,$topicid);

	if (($hidden = ($status === 'hidden'))
	||	$status === 'closed'
	) {
		$flags = db_single($topic,'SELECT FLAGS FROM '.$topic.' WHERE TOPICID='.$topicid);
		if ($flags
		&&	($flags & TOPIC_IS_POLL)
		&&	($pollid = db_single('poll','SELECT POLLID FROM poll WHERE OPENSTAMP!=0 AND CLOSESTAMP=0 AND ELEMENT="'.$topic.'" AND ELEMENTID='.$topicid))
		) {
			if (db_insert('poll_log','
				INSERT INTO poll_log
				SELECT * FROM poll
				WHERE POLLID='.$pollid)
			&&	db_update('poll','
				UPDATE poll SET
					STATUS		='.($hidden ? POLLSTATUS_UNACCEPTED : 'STATUS').',
/*					ACCEPTED	='.($hidden ? 0 : 'ACCEPTED').',*/
					MSTAMP		='.CURRENTSTAMP.',
					MUSERID		='.CURRENTUSERID.',
					OPENDURATION	=OPENDURATION+('.CURRENTSTAMP.'-OPENSTAMP),
					CLOSESTAMP	='.CURRENTSTAMP.'
				WHERE POLLID='.$pollid)
			) {
				if ($showmsgs) {
					register_notice($hidden ? 'poll:notice:rejected_LINE' : 'poll:notice:closed_LINE', DO_UBB, ['POLLID' => $pollid]);
				}
			}
		}
	}
	db_releaselock($key);
	if ($showmsgs) {
		$arg = array('TOPICID'=>$topicid,'ELEMENT'=>$topic);
		switch ($status) {
		case 'closed':
			$arg['ALTID'] = $altid;
			register_notice('topicactions:notice:closed_LINE',DO_UBB,$arg);
			break;
		case 'open':
		case 'hidden':	register_notice('topicactions:notice:set_to_'.$status.'_LINE', DO_UBB, $arg); break;
		default:	register_notice('topic:notice:changed_LINE', DO_UBB, $arg); break;
		}
	}
	flush_new_quotes_element($topic, $topicid);
	return true;
}

function set_message_accept(string $message, int $messageid, bool $accepted, int $post_flags = 0): bool {
	$is_flock = $message === 'flockmessage';
	$topic = $is_flock ? 'flocktopic' : 'topic';
	$topicinfo = db_single_assoc([$message, $topic],'
		SELECT SQL_NO_CACHE msg.TOPICID,MSGCNT,BADMSGCNT,LUSERID,LSTAMP,'.($is_flock ? 'FLOCKID' : 'FORUMID').' AS PARENTID,STATUS,msg.FLAGS AS MESSAGE_FLAGS
		FROM '.$message.' AS msg
		JOIN '.$topic.' USING (TOPICID)
		WHERE MESSAGEID='.$messageid,
		DB_USE_MASTER
	);
	if (!$topicinfo) {
		if ($topicinfo !== false) {
			register_error('message:error:no_topic_LINE', ['TYPE' => $message, 'MESSAGEID' => $messageid]);
		}
		return false;
	}
	$topicid = $topicinfo['TOPICID'];
	if (!require_getlock($key = $topic.':meta:'.$topicid,60)) {
		return false;
	}
	$accepted = $accepted ? 1 : 0;
	if (!db_insert($message.'_log','
		INSERT INTO '.$message.'_log
		SELECT * FROM '.$message.'
		WHERE ACCEPTED != '.$accepted.'
		  AND MESSAGEID = '.$messageid)
	) {
		db_releaselock($key);
		return false;
	}
	if (!db_affected()) {
		db_releaselock($key);
		register_warning($accepted ? 'message:warning:already_accepted_LINE' : 'message:warning:already_rejected_LINE');
		return false;
	}
	require_once '_post.inc';
	$flags = get_post_flags($message, $topicinfo['MESSAGE_FLAGS'], $post_flags, $topicinfo['PARENTID']);

	if (!db_update($message,'
		UPDATE '.$message.' AS msg JOIN '.$topic.' AS tpc USING (TOPICID) SET
			msg.MSTAMP	= '.CURRENTSTAMP.',
			msg.MUSERID	= '.CURRENTUSERID.',
			msg.ACCEPTED	= '.$accepted.',
			msg.FLAGS	= '.$flags.',
			BADMSGCNT	= BADMSGCNT + '.($accepted ? -1 : 1).
	($post_flags ? ',msg.FLAGS	= msg.FLAGS | '.$post_flags : '').'
		WHERE MESSAGEID = '.$messageid.'
		  AND msg.ACCEPTED != '.$accepted)
	) {
		db_releaselock($key);
		return false;
	}
	if (!$accepted) {
		if ($topicinfo['STATUS'] !== 'hidden'
		&&	$topicinfo['MSGCNT'] === $topicinfo['BADMSGCNT']+1
		) {
			require_once '_topic.inc';
			set_topic_status(
				$topic,
				$topicid,
				'hidden',
					$message === 'flockmessage'
				?	have_admin('flockmessage')
				:	_topic_adminnable($topicid)
			);
			$hid = true;
		}
	}
	if (!isset($hid)) {
		$linfo = db_single_assoc($message,'
			SELECT USERID,CSTAMP
			FROM '.$message.'
			WHERE ACCEPTED=1
			  AND TOPICID='.$topicid.'
			ORDER BY MESSAGEID DESC
			LIMIT 1'
		);
		if ($linfo
		&&	(	$linfo['USERID'] != $topicinfo['LUSERID']
			||	$linfo['CSTAMP'] != $topicinfo['LSTAMP']
			)
		) {
			db_update($topic,'
			UPDATE '.$topic.' SET
				LUSERID	='.$linfo['USERID'].',
				LSTAMP	='.$linfo['CSTAMP'].'
			WHERE TOPICID='.$topicid
			);
		}
	}
	db_releaselock($key);
	register_notice($accepted ? 'message:notice:accepted_LINE' : 'message:notice:rejected_LINE');
	return true;
}
function update_topicstats($topic,$topicid,$qstr,$flags = 0) {
	$locked = db_getlock($key = $topic.':meta:'.$topicid,60);
	$rc = db_update($topic,$qstr,$flags);
	if ($locked) {
		db_releaselock($key);
	}
	if (db_affected()) {
		memcached_delete('topic:'.$topicid);
	}
	return $rc;
}
function move_topic($topicid,$toforumid,$checkrights = true): bool {
	// admin must have rights to move items from current FORUMID to the new FORUMD
	// this is limited by the originating FORUMID, if the admin has admin rights
	// there, he may move it to any forum.
	if ($checkrights) {
		if (!_topic_adminnable($topicid)) {
			register_error('error:not_enough_rights_for_operation_LINE');
			return false;
		}
	}
	$forumid = require_topic_forumid($topicid);
	if (!$forumid) {
		return false;
	}
	if ($forumid === $toforumid) {
		register_warning('topic:warning:cant_move_to_same_forum_LINE');
		return false;
	}
	$oktoforum = db_single('forum','SELECT 1 FROM forum WHERE FORUMID='.$toforumid);
	if ($oktoforum === false) {
		return false;
	}
	if (!$oktoforum) {
		register_error('forum:error:nonexistent_LINE', ['ID' => $toforumid]);
		return false;
	}

	if (!db_insert('topic_log','
		INSERT INTO topic_log
		SELECT * FROM topic
		WHERE TOPICID='.$topicid.'
		  AND FORUMID!='.$toforumid)
	||	!db_update('topic','
		UPDATE topic SET
			MSTAMP	='.CURRENTSTAMP.',
			MUSERID	='.CURRENTUSERID.',
			FORUMID	='.$toforumid.'
		WHERE TOPICID='.$topicid.'
		  AND FORUMID!='.$toforumid)
	) {
		return false;
	}

	if (db_affected()) {
		memcached_delete('topic:'.$topicid);
		require_once '_sphinx.inc';
		sphinx_update_element('topic',$topicid);
	}
	$fromforum_lastpost = db_single('topic','SELECT MAX(LSTAMP) FROM topic WHERE FORUMID='.$forumid);
	if ($fromforum_lastpost === false) {
		$fromforum_lastpost = 'LAST_POST';
	} elseif (!$fromforum_lastpost) {
		$fromforum_lastpost = 0;
	}
	$toforum_lastpost   = db_single('topic','SELECT MAX(LSTAMP) FROM topic WHERE FORUMID='.$toforumid);
	if ($toforum_lastpost === false) {
		$toforum_lastpost = 'LAST_POST';
	} elseif (!$toforum_lastpost) {
		$toforum_lastpost = 0;
	}
	if (!db_insupd('forum_stats','
		INSERT INTO forum_stats SET
			FORUMID		='.$forumid.',
			TOPIX		=0,
			LAST_POST	='.$fromforum_lastpost.'
		ON DUPLICATE KEY UPDATE
			TOPIX		=TOPIX-1,
			LAST_POST	=GREATEST(VALUES(LAST_POST),LAST_POST)')
	||	!db_insupd('forum_stats','
		INSERT INTO forum_stats SET
			FORUMID		='.$toforumid.',
			TOPIX		=1,
			LAST_POST	='.$toforum_lastpost.'
		ON DUPLICATE KEY UPDATE 
			TOPIX		=TOPIX+1,
			LAST_POST	=GREATEST(VALUES(LAST_POST),LAST_POST)')
	) {
		# partial failure
		return true;
	}
	register_notice('topic:notice:moved_LINE');
	return true;
}
function commit_new_topic(string $element): bool {
	if (!have_post()) {
		return false;
	}
	$result = ($flock = $element === 'flocktopic') ? actual_flocktopic_commit_new() : _message_commit();
	if (false !== ($msgid = store_messages_in_cookie())
	||	$result
	) {
		if ($result) {
			[/* $messageid */, $topicid] = $result;
			see_other(get_element_href($element,$topicid));
		}
		if ($flock) {
			if ($flockid = have_idnumber($_REQUEST,'FLOCKID')) {
				see_other('/flocktopic/newform?FLOCKID='.$flockid);
			}
		} else {
			see_other('/forum/'.($_REQUEST['sID'] ? $_REQUEST['sID'].'/' : null).'form');
		}
	}
	return false;
}
