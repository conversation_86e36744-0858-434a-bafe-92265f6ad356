<?php

require_once '_cookie.inc';
require_once '_memcache.inc';
require_once '_require.inc';
require_once '_spider.inc';
require_once '_getcurrentaddr.inc';
require_once '_globalsettings.inc';

getcurrentaddr();
load_identity();

function load_identity(): int|false {
	if (defined('CURRENTIDENTID')) {
		return CURRENTIDENTID;
	}
	if (ROBOT
	||	CLI
	) {
		define('CURRENTIDENTID', 0);
		return 0;
	}

	$identid = have_idnumber($_COOKIE,'FLOCK_IDENTID');

	if (false === ($cookie_secret = have_number($_COOKIE, 'FLOCK_SECRET'))) {
		if (!$identid) {
			$lock_key = get_lock_secret();
			if (db_getlock($lock_key, 5)) {
				if (!($new_secret = memcached_get($lock_key))) {
					$new_secret = safe_random_int(1, 0xFFFF);
				}
				memcached_set($lock_key,$new_secret, 20);
				setflockcookie('FLOCK_SECRET', $new_secret, COOKIE_SET_LONG_LIVED);
				require_once '_cookie_consent.inc';
				if (!defined('CURRENTSTAMP')) {
					error_log('WARNING CURRENTSTAMP not defined @ '.$_SERVER['REQUEST_URI']);
				}
				db_releaselock($lock_key);
			}
			define('CURRENTIDENTID', 0);
			return 0;
		}
		# no secret, but have identy, just set SECRET again if it's a recente identity
	} elseif (!$identid) {
		define('CURRENTIDENTID', $identid = create_new_identity($cookie_secret));
		return $identid;
	}
	if (false === ($stored_secret = db_single_int('identityv4','SELECT SECRET FROM identityv4 WHERE IDENTID='.$identid))) {
		define('CURRENTIDENTID', 0);
		return 0;
	}
	if ($stored_secret === null) {
		# no identity stored with given secret and identity.
		# create a new one with given secret
		define('CURRENTIDENTID', $new_identid = create_new_identity($cookie_secret));
		return $new_identid;
	}
	if (# very old identity without secret:
		!$stored_secret
		# invalid cookie secret, update to new one
	||	$cookie_secret > 0xFFFF
	) {
		# very old identity, without secret
		$stored_secret = safe_random_int(1, 0xFFFF);
		$update = true;
	} else {
		if ($cookie_secret !== false
		&&	$stored_secret === $cookie_secret
		) {
			define('CURRENTIDENTID', $identid);
			return $identid;
		}
		if (false === ($identity = db_single_array('identityv4', 'SELECT HITS, LAST_HERE FROM identityv4 WHERE IDENTID = '.$identid))) {
			define('CURRENTIDENTID', 0);
			return 0;
		}
		if (!$identity) {
			$update = false;
		} else {
			[$hits, $last_here] = $identity;
			$update =	$hits < 500
					||	$last_here > CURRENTSTAMP - ONE_WEEK;
		}
	}
	if ($update) {
		define('CURRENTIDENTID', $identid);
		if ($cookie_secret === false
		||	$cookie_secret > 0xFFFF
		) {
			setflockcookie('FLOCK_SECRET', $stored_secret, COOKIE_SET_LONG_LIVED);
		} else {
			db_update('identityv4','
			UPDATE identityv4 SET
				SECRET = ' .$cookie_secret.'
			WHERE IDENTID = '.$identid);
		}
		return $identid;
	} else {
		define('CURRENTIDENTID', $new_identid = create_new_identity($cookie_secret === false ? $stored_secret : $cookie_secret));
		return $new_identid;
	}
}

function create_new_identity(int $secret = 0): int {
	if (empty($GLOBALS['__allow_new_identity'])
	&&	(	isset($_SERVER['AJAX'])
		||	!preg_match('"/(?:index|fb|images/ad)\.php$"',$_SERVER['SCRIPT_FILENAME'])
		)
	) {
		# only create new identities for main pages
		# and for ads (need identity for freqcaps)
		unset($_COOKIE['FLOCK_IDENTID']);
		return 0;
	}
	if (!$secret) {
		# no secret (indicates cookies work), don't create new identity
		unset($_COOKIE['FLOCK_IDENTID']);
		clearflockcookie('FLOCK_IDENTID');
		return 0;
	}
	require_once '_browser.inc';
	if (!db_insert('identityv4','
		INSERT INTO identityv4 SET
			BROWSERID	='.get_browserid().',
			SECRET		='.$secret.',
			LAST_IPBIN	="'.addslashes(CURRENTIPBIN).'",
			LAST_HERE	='.CURRENTSTAMP
		)
	||	!($newidentid = db_insert_id())
	) {
		unset($_COOKIE['FLOCK_IDENTID']);
		return 0;
	}
	setflockcookie('FLOCK_IDENTID', $newidentid, COOKIE_SET_LONG_LIVED);
	return $newidentid;
}

function require_identity(): bool {
	if (!CURRENTIDENTID) {
		register_error('identity:error:no_identity_LINE');
		return false;
	}
	return true;
}

function get_lock_secret(): string {
	$keylist = [];
	foreach ([
		'HTTP_USER_AGENT',
#		'HTTP_ACCEPT',
		'HTTP_ACCEPT_DATETIME',
		'HTTP_FROM',
		'HTTP_DNT',
		'HTTP_ACCEPT_ENCODING',
		'HTTP_ACCEPT_LANGUAGE',
		'HTTP_ACCEPT_CHARSET',
		'HTTP_X_FORDWARDED_FOR',
		'HTTP_VIA',
		'REMOTE_ADDR'
	] as $ndx) {
		if (isset($_SERVER[$ndx])) {
			$keylist[$ndx] = $_SERVER[$ndx];
		}
	}
	return 'idscrt:'.hash('xxh128', implode('|', $keylist));
}
