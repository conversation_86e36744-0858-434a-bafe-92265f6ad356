<?php

function get_goings_count(): int|false {
	if (false === ($goings = get_goings())) {
		return false;
	}
	return $goings ? count($goings) : 0;
}
function get_goings(): array|false {
	# 1416830545 is start of this functionality where party visitors get asked for rating after event.
	# Removing the timestamp comparison means a lot of very old events get asked for rating.
	static $__goings = memcached_rowuse_array(
		['going', 'goingasked', 'party', 'votenew', 'vote_tmp', 'party_rating'], '
		SELECT	going.PARTYID, NAME, LOCATIONID, party.STAMP, MAYBE,
				vote_tmp.VOTEID AS TMP_VOTEID, vote_tmp.STAMP AS TMP_VOTESTAMP
		FROM going
		LEFT JOIN goingasked USING (PARTYID, USERID)
		JOIN party ON party.PARTYID = going.PARTYID
		LEFT JOIN votenew ON votenew.TYPE = "party"
						 AND votenew.ID = going.PARTYID
						 AND votenew.USERID = '.CURRENTUSERID.'
		LEFT JOIN party_rating ON party_rating.ID = going.PARTYID
							  AND party_rating.USERID = '.CURRENTUSERID.'
		LEFT JOIN vote_tmp ON vote_tmp.TYPE = "party"
						  AND vote_tmp.ID = going.PARTYID
						  AND vote_tmp.USERID = '.CURRENTUSERID.'
		WHERE party.STAMP < '.(CACHESTAMP - 4 * ONE_HOUR).'
		  AND CANCELLED = 0
		  AND MOVEDID = 0
		  AND POSTPONED = 0
		  AND party.ACCEPTED
		  AND goingasked.USERID IS NULL
		  AND (	MAYBE
			 OR	party.STAMP > '.(1416830545 - (SERVER_SANDBOX ? 2 * ONE_YEAR : 0)).'
			AND	votenew.TYPE IS NULL
			AND	party_rating.ID IS NULL
		  )
		  AND going.USERID = '.CURRENTUSERID.'
		ORDER BY STAMP DESC
		LIMIT 50',
		ONE_DAY,
		'goingasked:v2:'.CURRENTUSERID
	);
	return $__goings;
}
function set_going_asked(int $partyid, ?bool $went = false): bool {
	$rc = db_insert('goingasked','
		INSERT IGNORE INTO goingasked SET
			WENT	='.($went === null ? 'NULL' : ($went ? 1 : 0)).',
			PARTYID	='.$partyid.',
			USERID	='.CURRENTUSERID.',
			STAMP	='.CURRENTSTAMP
	);
	memcached_delete('goingasked:v2:'.CURRENTUSERID);
	return $rc;
}

function asked_going(int $partyid, int $userid = 0): bool {
	return (bool)db_single('goingasked', '
		SELECT 1
		FROM goingasked
		WHERE PARTYID = '.$partyid.'
		  AND USERID = '.($userid ?: CURRENTUSERID)
	);
}

function show_goings(): void {
	if (!($asks = get_goings())) {
		return;
	}

	require_once '_uploadimage.inc';
	require_once '_vote.inc';

	layout_open_box('white');
	layout_box_header(__('going:ask:header:tell_us'));
	?><div class="block"><?= __('going:ask:tell_us_TEXT',DO_NL2BR) ?></div><?
	layout_close_box();

	?><div class="block"><?
	show_input([
		'type'		=> 'button',
		'value'		=> __('action:ignore_all'),
		'data-sure'	=> __('going:confirm:sure_to_ignore_events_LINE'),
		'onclick'	=> 'Pf.ignoreAllRemaining(this)',
	]);
	?></div><?

	include_js('js/goingprocess');

	if (!($spec = explain_table('party_rating'))) {
		return;
	}

	require_once '_urlforimg.inc';

	?><div class="imggroup"><?

	foreach ($asks as $party) {
		extract($party, \EXTR_OVERWRITE);

		?><form<?
		?> accept-charset="utf-8"<?
		?> id="gf<?= $PARTYID ?>"><?

		?><input type="hidden" name="PARTYID" value="<?= $PARTYID ?>" /><?

		layout_open_box('grey hlbox');
		layout_box_header(
			str_replace('<a ','<a target="_blank" ',get_element_link('party',$PARTYID,$NAME)).
			'<small class="light"> &middot; '._dateday_get($STAMP).
			($LOCATIONID ? ', '.get_element_link('location', $LOCATIONID) : '').
			'</small>',
			null,
			$MAYBE ? 'not-hilited-green' : 'hilited-green'
		);

		if ($imgs = uploadimages_get_with_fallback('party', $PARTYID, UPIMG_NOCHANGE,  'regular', ['landscape','square'])) {
			uploadimage_show_from_img($imgs[0]);
		}

		$my_vote = vote_display_choices(
			null,
			$TMP_VOTEID !== null ? [$TMP_VOTEID, $TMP_VOTESTAMP] : null,
			'party',
			$PARTYID,
			true
		);

		?><div class="block"><?
		show_input([
			'name'			=> 'RATING',
			'utf8'			=> true,
			'maxlength'		=> $spec['BODY']->maxlength,
			'placeholder'	=> __('going:ask:give_a_short_optinion_LINE'),
			'type'			=> 'text',
			'onkeyup'		=> 'Pf.updateStoreBut('.$PARTYID.')',

			#'data-max'	=> 5,
			#'data-maxlines'=> 5,
			#'class'	=> 'growToFit',
		]);
		?></div><?

		layout_close_box();

		?><div class="block"><?
		?><input<?
		if (!$my_vote) {
			?> class="light novents"<?
		}
		?> type="button" name="STORE" value="<?= __('action:store') ?>" onclick="return Pf.processGoing(this)"><?
		?><div class="r"><?
		?><input type="button" name="IGNORE" value="<?= __('action:ignore') ?>" onclick="return Pf.processGoing(this,'ignore')" /> <?
		?><input type="button" value="<?= __('action:did_not_go') ?>" onclick="return Pf.processGoing(this,'nogo')" /><?
		?></div><?

		?></div><?

		?></form><?
	}
	?></div><?
}
function process_going(): void {
	require_once '_helper.inc';
	if (!($partyid = require_idnumber($_POST,'PARTYID'))) {
		bail(400);
	}
	global $currentuser;
	$currentuser = new _currentuser(SKIP_SETTINGS);
	if (!have_user()) {
		bail(403);
	}
	if (isset($_POST['IGNORE'])) {
		if ($_POST['IGNORE'] === 'nogo') {
			$maybe = db_single('going','
				SELECT MAYBE
				FROM going
				WHERE MAYBE = 0
				  AND USERID = '.CURRENTUSERID.'
				  AND PARTYID = '.$partyid
			);
			if (db_errno()) {
				bail(500);
			}
			if (!$maybe) {
				require_once '_goingparty.inc';
				if (!join_party($partyid, sure: false, quiet: true)) {
					bail(500);
				}
			}
			if (!set_going_asked($partyid, false)) {
				bail(500);
			}
		} elseif ($_POST['IGNORE'] === 'ignore') {
			if (!set_going_asked($partyid, null)) {
				bail(500);
			}
		} else {
			mail_log('invalid IGNORE value: '.$_POST['IGNORE']);
			bail(400);
		}
		bail(200);
	}
	$maybe = db_single('going','
		SELECT MAYBE
		FROM going
		WHERE USERID = '.CURRENTUSERID.'
		  AND PARTYID = '.$partyid
	);
	if (db_errno() || $maybe === null) {
		bail(500);
	}
	$what = [];
	if ($maybe) {
		require_once '_goingparty.inc';
		join_party($partyid, sure: true, quiet: true);
		$what[] = 'join_sure';
	}

	if (!empty($_POST['RATING'])) {
		require_once '_rating.inc';
		$_REQUEST['sELEMENT'] = 'party';
		$_REQUEST['sID'] = $partyid;
		if (!commit_rating(true)) {
			bail(500);
		}
		$what[] = 'rating';
	}

	if (!db_insert('votenew','
		REPLACE INTO votenew (USERID, ID, TYPE, STAMP, VOTEID)
		SELECT USERID, ID, TYPE, STAMP, VOTEID
		FROM vote_tmp
		WHERE TYPE = "party"
		  AND ID = '.$partyid.'
		  AND USERID = '.CURRENTUSERID)
	) {
		bail(500);
	}
	if (db_affected()) {
		$what[] = 'vote';
	}
	if (!set_going_asked($partyid,true)) {
		bail(500);
	}
	?><div class="block"><?= __('going:ask:opinion_registered_LINE',DO_UBB,['PARTYID'=>$partyid]) ?></div><?
	bail(200);
}

function show_going_process_menu(): int {
	if ($goings_count = get_goings_count()) {
		?><div id="group-count-menu-item" class="notice ns"><a<?
		if ($_REQUEST['sELEMENT'] === 'agenda'
		&&	$_REQUEST['ACTION']   === 'going'
		) {
			?> class="selected"<?
		}
		?> href="/agenda/going"><?= __C('action:tell') ?></a>!<?
		?> <?= MIDDLE_DOT_ENTITY ?> <?
		?><span><?= $goings_count ?></span></div><?
	}
	return $goings_count;
}
