<?php

require_once '_date.inc';
require_once '_layout.inc';
require_once '_ubb.inc';
require_once '_articles.inc';

function get_artist_interview_count($artistid) {
	return memcached_single(
		array('connect','interview'), '
		SELECT COUNT(*)
		FROM connect
		JOIN interview ON INTERVIEWID=ASSOCID
		WHERE MAINTYPE="artist"
		  AND MAINID='.$artistid.'
		  AND ASSOCTYPE="interview"'.
		  and_not_hidden('interview'),
		TEN_MINUTES
	);
}

function get_artist_interviews(int $artistid, int &$size): array|false {
	$interviews = memcached_rowuse_array(
		array('connect','interview'), '
		SELECT INTERVIEWID,TITLE,TEASER,interview.PSTAMP,BYUSERID
		FROM connect
		JOIN interview ON INTERVIEWID=ASSOCID
		WHERE MAINTYPE="artist"
		  AND MAINID='.$artistid.'
		  AND ASSOCTYPE="interview"'.
		  and_not_hidden('interview').'
		  AND PSTAMP > '.(TODAYSTAMP - 4 * ONE_YEAR).'
		ORDER BY PSTAMP DESC'.
		($size ? ' LIMIT '.$size : null),
		TEN_MINUTES
	);
	$size = $interviews ? count($interviews) : 0;
	return $interviews;
}

function show_artist_interviews($artistid,$interviews = false) {
	if ($interviews === false) {
		$size = 0;
		$interviews = get_artist_interviews($artistid,$size);
		if (!$interviews) {
			return;
		}
	} elseif (!$interviews) {
		return;
	}
	layout_open_box('artist','interviews');
	layout_box_header(Eelement_plural_name('interview'));
	layout_open_table(TABLE_FULL_WIDTH);
	foreach ($interviews as $interview) {
		layout_start_rrow();
		echo get_element_link('interview',$interview['INTERVIEWID'],$interview['TITLE']);
		layout_next_cell(class: 'right');
		echo __('attribution:user',DO_UBB,array('USERID'=>$interview['BYUSERID']));
/*		?>door <a href="/user/<?= $interview['BYUSERID'];
		?>"><?= escape_specials($interview['NICK']);
		?></a><?*/
		layout_next_cell(class: 'right');
		_dateday_display($interview['PSTAMP']);
		layout_stop_row();
	}
	layout_close_table();
	layout_close_box();
}
function show_artist_reviews($artistid,$reviews = false) {
	if ($reviews === false) {
		require_once '_articles.inc';
		$size = 0;
		# does this doe query only once for size <= 3
		$reviews = get_articles('review','artist',$artistid,$size);
		if (!$reviews) {
			return;
		}
	} elseif (!$reviews) {
		return;
	}
	layout_open_box($_REQUEST['sELEMENT'],'reviews');
	layout_box_header(Eelement_plural_name('review'));
	layout_open_table('default fw hla');
	foreach ($reviews as $review) {
		layout_start_rrow();
		echo get_element_link('review',$review['ARTICLEID'],$review['TITLE']);
		layout_next_cell(class: 'right');
		if ($review['USERIDS'] || $review['BYUSERID']) {
/*			?>door <?*/
			if ($review['USERIDS']) {
/*				$ids = explode(',',$review['USERIDS']);
				if ($review['BYUSERID']) {
					$ids[] = $review['BYUSERID'];
				}
				$size = count($ids);
				foreach ($ids as $id) {
					echo get_element_link('user',$id);
					if (--$size) {
						?>, <?
					}
				}*/
				echo __('attribution:users',DO_UBB,array('USERIDS'=>$review['USERIDS'].($review['BYUSERID'] ? ','.$review['BYUSERID'] : null)));
			} else {
				echo __('attribution:user',DO_UBB,array('USERID'=>$review['BYUSERID']));
#				echo get_element_link('user',$review['BYUSERID']);
			}
		}
		layout_next_cell(class: 'right');
		_dateday_display($review['PSTAMP']);
		layout_stop_row();
	}
	layout_close_table();
	layout_close_box();
}

function show_artist_archive(array $artist) {
	require_once '_partylist.inc';
	$partylist = new _partylist;
	if (ROBOT) {
		$partylist->show_vevent = true;
	}
	$partylist->show_date =
	$partylist->show_stars =
	$partylist->show_camera =
	$partylist->show_location =
	$partylist->show_city =
	$partylist->show_buddy_hearts = true;
	$partylist->select_past();
	if (!have_admin()) {
		$partylist->not_cancelled_nor_moved();
	}
	$partylist->only_artist($artist['ARTISTID']);
	$partylist->order_reverse_chronologically();
	$partylist->hide_separators = true;
	if ($partylist->query()) {
		layout_open_box('artist', 'archive');
		layout_box_header(__C('artist:header:archive'));
		$partylist->display();
		layout_close_box();
	}
}
