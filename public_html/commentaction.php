<?php

require_once '_date.inc';

define('CURRENTSTAMP',		time());
define('CURRENTDAYNUM',	to_days());
define('TODAYSTAMP',		mktime(0,0,0));
define('TODAYSTAMP_TZI',	gmmktime(0,0,0));

require_once '_exit_if_readonly.inc';
require_once '_helper.inc';
require_once '_error.inc';
require_once '_settings.inc';
require_once '_layout.inc';
require_once '_nocache.inc';
require_once '_require.inc';
require_once '_currentuser.inc';
require_once '_identity.inc';

send_no_cache_headers();

function bail(int $errno): never {
	if ($errno !== 200) {
		http_response_code($errno);
	}
	display_messages_only('error', 'warning');
	exit;
}

require_once '_error_handler.inc';
set_my_error_handler();

$commentid = 0;

if (!($parentelement = require_something($_REQUEST, 'sELEMENT'))
||	!($parentid = require_idnumber($_SERVER, 'eELEMENTID'))
||	!($action = require_element($_SERVER, 'eACTION', ['accept', 'deny', 'remove', 'new', 'change', 'form'], true))
||	$action !== 'new'
&&	!($commentid = require_idnumber($_SERVER, 'eCOMMENTID'))
) {
	bail(400);
}
$currentuser = new _currentuser(TINY_USER);

if (!require_user()) {
	bail(403);
}

switch ($action) {
case 'accept':
case 'deny':
case 'remove':
	if (!require_valid_origin()) {
		bail(403);
	}
	break;
case 'change':
	if (!require_valid_origin_or_secret()) {
		bail(403);
	}
#	$GLOBALS['__topicmayreply'] = true;
	# need to check whether topic is old and may not receive new messages (bumps)
	if (empty($_POST)) {
		require_once '_require.inc';
		if (($tries = have_idnumber($_SERVER, 'HTTP_X_TRIES') ?: 0)
		&&	$tries === 1
		) {
			require_once '_helper.inc';
			error_log('no data @ '.$_SERVER['REQUEST_URI'], 0);
		}
		header('X-NODATA: 1');
		register_error('form:error:no_data_TEXT', DO_NL2BR | DO_UBB);
		bail(400);
	}
	break;
}
switch ($action) {
case 'change':
case 'form':
case 'new':
	header('Content-Type: text/html; charset=windows-1252');
	if (!empty($_POST['BODY'])) {
		require_once '_badcontent.inc';
		require_once '_comment.inc';
		if (fail_bad_content($body = $_POST['BODY'], $parentelement, $parentid, utf8: true)) {
			bail(410);
		}
	}

	break;
}

$commentelement = match($parentelement) {
	'topic'			=> 'message',
	'flocktopic'	=> 'flockmessage',
	default			=> $parentelement.'_comment',
};

$post_flags = 0;
$show_flags = 0;

require_once '_commentobject.inc';

$may_reply = 0;

if ($commentid) {
	require_once 'defines/post.inc';
	if (!($access = may_change_comment($commentelement, $commentid, $action))) {
		bail(403);
	}
	[$maychange, $maystatus, $need_admin, $need_leader] = $access;
	if ($need_admin) {
		$post_flags |= POST_CHANGED_BY_ADMIN;
	}
	if ($need_leader) {
		$post_flags |= POST_CHANGED_BY_LEADER;
	}
	if (!empty($_REQUEST['ALLOW_SEEN'])) {
		$show_flags |= CMTFLG_ALLOW_SEEN;
	}
} elseif (empty($_REQUEST['ALLOW_SEEN'])) {
	$may_reply = CMTFLG_MAY_REPLY;
}
if (!empty($_REQUEST['SHOW_PARENT'])) {
	$show_flags |= CMTFLG_SHOW_PARENT;
}

switch ($parentelement) {
case 'topic':
case 'flocktopic':
	switch ($action) {
	case 'accept':
	case 'remove':
	case 'deny':
		require_once '_topicactions.inc';
		if (!set_message_accept($commentelement, $commentid, $action === 'accept', $post_flags)) {
			display_messages_only();
			bail(412);
		}
		break;
	}
}

switch ($parentelement) {
case 'topic':
	switch ($action) {
	case 'change':
	case 'form':
	case 'new':
		require_once '_topic.inc';
		require_once '_message.inc';
		require_once '_commentobject.inc';
		$topic = new _topic($parentid);
		if ($topic === false) {
			error_log('503 here!',0);
			bail(503);
		}
		if (!$topic || !$topic->exists()) {
			bail(404);
		}
		if (!$topic->may_view) {
			bail(403);
		}
		switch ($action) {
		case 'new':
			$_REQUEST['sID'] = $parentid;
		case 'change':
			if (!($result = _message_commit(post_flags: $post_flags))) {
				display_messages_only();
				bail(412);
			}
			list($commentid) = $result;
			break;
		case 'form':
			$GLOBALS['CHANGEID'] = $commentid;
			break;
		}
		$show_flags |= $may_reply;
		if ($topic->is_admin) {
			$show_flags |= CMTFLG_IS_ADMIN;
		}
		$comment = query_message('message', $commentid, DB_FRESHEN_MEMCACHE | DB_USE_MASTER, true);
		comment_display('message', $topic->topicrow, $comment[0], $show_flags);
		break;
	}
	break;
case 'flocktopic':
	switch ($action) {
	case 'new':
	case 'form':
	case 'change':
		require_once '_flocktopic.inc';
		require_once '_commentobject.inc';
		$topic = get_flocktopic($parentid);
		if ($topic === false) {
			error_log('503 here too!');
			bail(503);
		}
		if (!$topic) {
			bail(404);
		}
		switch ($action) {
		case 'form':
			$GLOBALS['CHANGEID'] = $commentid;
			break;
		case 'change':
			if (!update_flockmessage($parentid, $commentid, $post_flags)) {
				display_messages_only();
				bail(412);
			}
			break;
		case 'new':
			if (!$commentid = add_flockmessage($parentid)) {
				display_messages_only();
				bail(412);
			}
			break;
		}
		if (false === ($comments = query_message('flockmessage', $commentid, DB_FRESHEN_MEMCACHE | DB_USE_MASTER, true))) {
			error_log('503 here also!',0);
			bail(503);
		}
		if (!$comments) {
			bail(404);
		}
		$comment = $comments[0];
		$show_flags |= $may_reply;
		if ($flockadmin = have_admin('flockmessage')) {
			$show_flags |= CMTFLG_IS_ADMIN;
		};
		if (have_flock_leader($topic['FLOCKID'])) {
			$show_flags |= CMTFLG_IS_LEADER;
		}
		comment_display('flockmessage', $topic, $comment, $show_flags);
		break;
	}
	break;

default:
	require_once '_comment.inc';
	require_once '_commentlist.inc';
	if (!is_commentable($parentelement)) {
		bail(400);
	}
	$commentelement = $parentelement.'_comment';
	$is_admin = have_admin($commentelement);
	switch ($action) {
	case 'accept':
	case 'remove':
	case 'deny':
		$_REQUEST['sID']		= $parentid;
		$_REQUEST['SUBACTION']	= $action;
		$_REQUEST['ACTION']		= 'comment';
		$_REQUEST['COMMENTID']	= $commentid;
		$_REQUEST['NOMEMCACHE'] = true;

		$commentlist = new _commentlist(commit_only: true, post_flags: $post_flags);
		if (!$commentlist->commit_result()) {
			display_messages_only();
			bail(412);
		}
#		require_once '_pagechanged.inc';
#		page_changed($parentelement, $parentid);
		break;

	case 'change':
	case 'form':
	case 'new':
		require_once '_commentobject.inc';

		$_REQUEST['sID']		= $parentid;
		$_REQUEST['ACTION']		= 'comment';
		$_REQUEST['COMMENTID']	= $commentid;
		$_REQUEST['NOMEMCACHE'] = true;

		switch ($action) {
		case 'change':
		case 'new':
			$_REQUEST['SUBACTION'] = 'commit';
			break;
		case 'form':
			$GLOBALS['CHANGEID'] = true;
			break;
		}

		$extraid = 0;

		if ($parentelement === 'albumelement') {
			require_once '_album.inc';
			$extraid = clean_get_albumid_from_albumelementid($parentid);
			if ($extraid === false) {
				error_log('503 here also again!');
				bail(503);
			}
			if (!$extraid) {
				register_error('albumelement:error:nonexistent_LINE', ['ID' => $parentid]);
				bail(404);
			}
		}

		$commentlist = new _commentlist($extraid, true, $post_flags);
		switch ($action) {
		case 'change':
		case 'new':
			if (!($commentid = $commentlist->commit_result())) {
				display_messages_only();
				bail(412);
			}
			if ($action === 'new') {
				require_once '_pagechanged.inc';
				page_changed($parentelement, $parentid);
			}
			break;
		}
		if (!($comment = db_single_assoc($parentelement.'_comment',/** @lang MariaDB */ '
			SELECT USERID, MUSERID, BODY, COMMENTID, ACCEPTED, CSTAMP, MSTAMP, FLAGS, MSGNO, ID'.($parentelement === 'albumelement' ? ', EXTRAID AS ALBUMID' : '')."
			FROM {$parentelement}_comment AS comment
			WHERE COMMENTID = $commentid",
			DB_USE_MASTER))
		) {
			if ($comment === false) {
				error_log('503 here again too!');
			}
			bail($comment === false ? 503 : 404);
		}
		if (!$is_admin
		&&	CURRENTUSERID !== $comment['USERID']
		) {
			bail(403);
		}
		$show_flags = $may_reply;
		if ($is_admin) {
			$show_flags |= CMTFLG_IS_ADMIN;
		}
		comment_display($commentelement, $parentid, $comment, $show_flags);
/*		switch ($action) {
		case 'change':
		case 'new':
			$commentlist->display_row($comment, false);
			break;
		case 'form':
			$commentlist->display_form($comment);
			break;
		}*/
		break;
	}
}
switch ($action) {
case 'accept':
case 'deny':
	header('Content-Type: text/html; charset=windows-1252');
	echo __C($action === 'deny' ? 'action:accept' : 'action:deny');
	break;

case 'new':
	require_once '_secret.inc';
	$secret = get_form_secret();
	header('X-SECRET: '.$secret);
	header('X-ID: '.$commentid);
	header('X-CURRENTSTAMP: '.CURRENTSTAMP);
	break;
}

switch ($action) {
case 'accept':
case 'deny':
case 'remove':
	require_once '_quote.inc';
	flush_new_quotes_element($parentelement, $parentid, $commentelement, $commentid);
	break;

case 'new':
case 'change':
	warn_bad_content($_POST['BODY'], $commentelement, $commentid, $parentelement, $parentid, utf8: true);
	break;
}
if ($refreshkey = get_refreshkey()) {
	memcached_set($refreshkey, CURRENTSTAMP);
}

bail(200);
