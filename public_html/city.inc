<?php

function display_header(): void {
	require_once '_feed.inc';
	show_feed('agenda',FEED_HEADER);
}

function preamble(): void {
	if (!($cityid = $_REQUEST['sID'])) {
		return;
	}
	if ($_REQUEST['ACTION'] === 'pastlocations') {
		moved_permanently(str_replace('pastlocations','locations',$_SERVER['REQUEST_URI']));
	}
}
function perform_commit(): ?bool {
	/** @noinspection UsingInclusionOnceReturnValueInspection */
	return match($_REQUEST['ACTION']) {
		'commit'			=> city_commit(),
		'addunknown'		=> city_add_unknown(),
		'userchange'		=> city_change_user(),
		'addandinvalidate',
		'invalidate'		=> city_invalidate(),
		'autoreplace'		=> city_auto_replace(),
		'autorenameadd'		=> city_add_auto_rename(),
		'autorenamerem'		=> city_remove_auto_rename(),
		'remove'			=> (require_once '_remove.inc')  && remove_element(),
		'combine'			=> (require_once '_combine.inc') && combine_element(),
		default				=> null,
	};
}

function display_body(): void {
	switch ($_REQUEST['ACTION']) {
	default:
		require_once '_search_via_url.inc';
		search_via_url();
		not_found();
		return;

	case null:
	case 'searchresult':	city_display_overview(); return;
	case 'archive':
	case 'users':
	case 'locations':
	case 'boardings':
	case 'organizations':
	case 'comment':
	case 'comments':
	case 'combine':
	case 'single':			city_display_single(); return;
	case 'combinewith':		require_once '_combine.inc'; show_combine_with(); return;
	case 'form':
	case 'commit':
	case 'addunknown':		city_display_form(); return;
	case 'showunknown':
	case 'clearcity':
	case 'autoreplace':
	case 'userchange':
	case 'invalidate':
	case 'addandinvalidate':city_display_unknown(); return;
	case 'shownolonlat':	city_display_no_lonlat(); return;
	case 'userform':		city_display_user_form();return;
	case 'autorenames':
	case 'autorenameadd':
	case 'autorenamerem':	city_display_auto_renames(); return;
	case 'invalids':		city_display_invalids(); return;
	case 'remove':			$GLOBALS['commitresult'] ? city_display_overview() : city_display_single(); return;
	case 'emptyprovinces':	city_display_empty_provinces(); return;
	}
}

function city_display_empty_provinces() {
	if (!require_admin('city')) {
		return;
	}
	require_once '_cityvalidation.inc';

	$cities = db_rowuse_array('city','
		SELECT CITYID, COUNTRYID, NAME
		FROM city
		WHERE COUNTRYID IN ('.COUNTRIES_REQUIRING_PROVINCE.')
		  AND PROVINCEID = 0
		ORDER BY
			COUNTRYID ASC,
			NAME ASC'
	);

	if (!$cities) {
		return;
	}

	$province_name = [
		1	=> 'provincie',
		2	=> 'state',
		6	=> 'provincie',
	];

	$current_countryid = null;

	layout_open_box('city');
	foreach ($cities as $city) {
		extract($city, EXTR_OVERWRITE);
		if ($current_countryid !== $COUNTRYID) {
			if ($current_countryid) {
				layout_close_table();
			}
			$current_countryid = $COUNTRYID;
			layout_box_header(get_element_link('country', $current_countryid));
			layout_open_table('default');
		}

		?><tr><?
		?><td><a target="_blank" href="/city/<?= $CITYID ?>/form"><?= escape_utf8($NAME) ?></a></td><?
		?><td><a target="_blank" href="https://www.google.com/search?q=<?= urlencode($NAME.', '.$province_name[$COUNTRYID]) ?>">Google</a></td><?
		?></tr><?
	}
	layout_close_table();
}

function city_display_invalids(): void {
	layout_section_header(Eelement_plural_name('invalid_city'));

	if (!require_admin('city')) {
		return;
	}
	$invalids = db_rowuse_array('cityinvalid','
		SELECT NAME, TYPE
		FROM cityinvalid
		ORDER BY NAME ASC'
	);
	if (!$invalids) {
		return;
	}
	require_once '_cityvalidation.inc';
	layout_open_table(TABLE_FULL_WIDTH);
	foreach ($invalids as $invalid) {
		layout_start_rrow();
		echo escape_utf8($invalid['NAME']);
		layout_next_cell(class: 'right');
		city_show_invalid_reason($invalid['TYPE']);
		layout_stop_row();
	}
	layout_close_table();
}

function city_change_user(): bool {
	require_once '_counthit.inc';
	if (!require_admin('city')
	||	!require_idnumber($_REQUEST,'USERID')
	||	!require_idnumber($_POST, 'COUNTRYID')
	||	!optional_number($_POST, 'CITYID')
	||	!($cityname = db_single('user','SELECT CITY FROM user WHERE USERID='.$_REQUEST['USERID']))
	||	!db_insert('user_log','
		INSERT INTO user_log
		SELECT * FROM user
		WHERE USERID='.$_REQUEST['USERID'])
	||	!db_update('user','
		UPDATE user SET
			INVALID_CITY	= 0,
			MSTAMP			= '.CURRENTSTAMP.',
			MUSERID			= '.CURRENTUSERID.',
			CITYID			= '.(!empty($_POST['CITYID']) ? $_POST['CITYID'] : 0).',
			COUNTRYID		= '.$_POST['COUNTRYID'].',
			CITY			= ""
		WHERE USERID='.$_REQUEST['USERID'])
	) {
		return false;
	}

	_notice('Lid is aangepast.');

	if (have_something_trim($_POST, 'AUTORENAME', utf8: true)) {
		if (!db_insert('cityautorename','
			INSERT INTO cityautorename SET
				ALIAS	= "'.addslashes($_POST['AUTORENAME']).'",
				CITYID	= '.(!empty($_POST['CITYID']) ? $_POST['CITYID'] : $_POST['COUNTRYID']).',
				TYPE	= "'.(!empty($_POST['CITYID']) ? 'city' : 'country').'",
				CSTAMP	= '.CURRENTSTAMP.',
				USERID	= '.CURRENTUSERID)
		) {
			return false;
		}
		$aliasid = db_insert_id();
		counthit('cityinvalid', $aliasid);
		_notice('Auto-rename &quot;'.escape_utf8($_POST['AUTORENAME']).
			'&quot; naar '.(
				!empty($_POST['CITYID'])
			?	get_element_link('city',$_POST['CITYID'])
			:	get_element_link('country',$_POST['COUNTRYID'])
			).' toegevoegd!'
		);
	}
	return true;
}
function city_display_user_form() {
	if (!require_admin('city')
	||	!require_idnumber($_REQUEST,'USERID')
	) {
		return;
	}
	$user = db_single_assoc('user','SELECT NICK, USERID, CITY, COUNTRYID FROM user WHERE USERID = '.$_REQUEST['USERID']);
	if (!$user) {
		not_found();
		return;
	}
	layout_open_section_header();
	?>Stad van gebruiker aanpassen<?
	layout_close_section_header();

	if (!$user['CITY']) {
		register_error('city:error:no_user_supplied_city_found_LINE');
		return;
	}

	require_once '_bubble.inc';
	require_once '_citylist.inc';

	?><form<?
	?> accept-charset="utf-8"<?
	?> onsubmit="return submitForm(this)"<?
	?> method="post" action="/city/userchange"<?
	?>><?
	?><input type="hidden" name="USERID" value="<?= $_REQUEST['USERID']; ?>" /><?

	layout_open_box('city');
	layout_box_header(Eelement_name('city').' '.MIDDLE_DOT_ENTITY.' '.get_element_link('user',$_REQUEST['USERID'],$user['NICK']));

	layout_open_table(TABLE_VTOP | TABLE_CLEAN);
	layout_start_row();
		?>Opgegeven stad<?
		layout_field_value();
		echo escape_utf8($user['CITY']);

	layout_restart_row();
		?>Vervangen door<?
		layout_field_value();
		display_dynamic_city_pair(
			countryid: $user['COUNTRYID'],
			#flags: INCLUDE_EMPTY_COUNTRIES | COUNTRY_REQUIRED,
		);

	layout_restart_row();
		$bubble = new bubble(BUBBLE_BLOCK);
		$bubble->catcher();
		?><label for="autorename">Toevoegen als auto-rename</label><?
		$bubble->content();
		?>Niet als auto-rename toevoegen als de opgegeven stad de naam van een stad is waarvan er meer bestaan!<br /><?
		?>Geef dan op welke stad de voorkeur geniet door een van de steden met die naam aan te passen en als voorkeursstad te markeren!<?
		$bubble->display();
		layout_field_value();
		?><label for="autorename"><?
		?><input type="checkbox" value="<?
		echo $autorename = escape_specials(trim(preg_replace('/[^[:alpha:]]+/','', utf8_to_ascii($user['CITY']))));
		?>" name="AUTORENAME" id="autorename"><?
		?> &quot;<?= $autorename; ?>&quot;<?
		?></label><?
	layout_stop_row();
	layout_close_table();
	layout_close_box();

	?><p><input type="submit" value="<?= __('action:change'); ?>" /></p></form><?
}
function city_auto_replace(): void {
	require_once '_cityvalidation.inc';
	if (!require_admin('city')
	||	!($trylist = db_rowuse_array(['user', 'user_account'], "
		SELECT USERID, CITY
		FROM user FORCE KEY (CITY)
		JOIN user_account FORCE KEY (USERID) USING (USERID)
		WHERE STATUS = 'active'
		  AND CITY IS NOT NULL
		  AND (INVALID_CITY = ".CITY_CHECK.' OR INVALID_CITY = 0 AND CITYID = 0)'))
	) {
		return;
	}
	$replacecnt = 0;
	$invalidcnt = 0;
	$autorenamecnt = 0;
	foreach ($trylist as $user) {
		// FIXME: user.CITY is still latin1
		// FIXME: use user_check_city here?

		$city = utf8_to_ascii($user['CITY']);

		$tryname = '("'.addslashes(trim(preg_replace('"[\d\s[:punct:]]+"', '', $city))).'","'.addslashes(trim($city)).'")';

		if (false === ($options = db_rowuse_array(['city', 'cityautorename'], "
			SELECT CITYID, COUNTRYID
			FROM city
			WHERE CLEANNAME IN $tryname
			UNION
			SELECT CITYID, COUNTRYID
			FROM cityautorename
			JOIN city USING (CITYID)
			WHERE ALIAS IN $tryname
			  AND TYPE = 'city'"))
		) {
			return;
		}
		if ($options
		&&	!isset($options[1])
		) {
			# single result
			$chosen = $options[0];
			if (!db_insert('user_log','
				INSERT INTO user_log
				SELECT * FROM user
				WHERE USERID='.$user['USERID'])
			||	!db_update('user','
				UPDATE user SET
					INVALID_CITY	= 0,
					MSTAMP			= '.CURRENTSTAMP.',
					MUSERID			= '.CURRENTUSERID.",
					CITYID			= {$chosen['CITYID']},
					COUNTRYID		= {$chosen['COUNTRYID']},
					CITY			= NULL
				WHERE USERID = {$user['USERID']}")
			) {
				return;
			}
			++$replacecnt;
			continue;
		}
		if (false === ($options = db_rowuse_array('cityinvalid', '
			SELECT CINVID, TYPE
			FROM cityinvalid
			WHERE NAME IN '.$tryname))
		) {
			return;
		}
		if ($options
		&&	!isset($options[1])
		) {
			$chosen = $options[0];
			if (!db_insert('user_log','
				INSERT INTO user_log
				SELECT * FROM user
				WHERE USERID = '.$user['USERID'])
			||	!db_update('user','
				UPDATE user SET
					INVALID_CITY	= '.$chosen['TYPE'].',
					MSTAMP		= '.CURRENTSTAMP.',
					MUSERID		= '.CURRENTUSERID.'
				WHERE USERID = '.$user['USERID'])
			) {
				return;
			}
			++$invalidcnt;
			continue;
		}
		if (false === ($options = db_rowuse_array('cityautorename', '
			SELECT CITYID AS ID, ALIASID, TYPE
			FROM cityautorename
			WHERE ALIAS IN '.$tryname))
		) {
			return;
		}
		if ($options
		&&	!isset($options[1])
		) {
			$chosen = $options[0];
			if ($chosen['TYPE'] === 'country') {
				$cityid = 0;
				$countryid = $chosen['ID'];
			} else {
				$cityid = $chosen['ID'];
				if (!($countryid = db_single_int('city','SELECT COUNTRYID FROM city WHERE CITYID = '.$cityid))) {
					if ($countryid !== false) {
						register_error('city:error:city_not_connected_to_country_LINE', ['CITYID' => $cityid]);
					}
					return;
				}
			}
			if (!db_insert('user_log','
				INSERT INTO user_log
				SELECT * FROM user
				WHERE USERID = '.$user['USERID'])
			||	!db_update('user','
				UPDATE user SET
					INVALID_CITY	= 0,
					MSTAMP		= '.CURRENTSTAMP.',
					MUSERID		= '.CURRENTUSERID.',
					CITYID		= '.$cityid.',
					COUNTRYID	= '.$countryid.',
					CITY		= ""
				WHERE USERID = '.$user['USERID'])
			) {
				return;
			}
			++$autorenamecnt;
			continue;
		}
	}
	if (!$replacecnt
	&&	!$invalidcnt
	&&	!$autorenamecnt
	) {
		_notice('Geen steden kunnen vervangen.');
	} else {
		if ($replacecnt) {
			_notice($replacecnt > 1 ? $replacecnt.' steden vervangen.' : 'E&eacute;n stad vervangen.');
		}
		if ($invalidcnt) {
			_notice($invalidcnt > 1 ? $invalidcnt.' steden afgekeurd.' : 'E&eacute;n stad afgekeurd.');
		}
		if ($autorenamecnt) {
			_notice($autorenamecnt > 1 ? $autorenamecnt.' steden automatisch hernoemd.' : 'E&ecute;n stad automatisch hernoemd.');
		}
	}
}

function city_add_unknown(): bool {
	if (!$_REQUEST['subID']
	||	!require_admin('city')
	||	false === ($info = db_single_assoc(['user', 'user_account'], "
			SELECT CITY, COUNTRYID, CITYID
			FROM user
			JOIN user_account USING (USERID)
			WHERE USERID = {$_REQUEST['subID']}"))
	) {
		return false;
	}
	if (!$info) {
		not_found();
		return false;
	}
	if (!$info['CITY']) {
		register_error('city:error:no_user_supplied_city_found_LINE');
		_error('Geen onbekende stad in het profiel van lid met ID '.$_REQUEST['subID'].'!');
		return false;
	}
	/** @noinspection PhpArrayWriteIsNotUsedInspection */
	$_POST = [
		'NAME'			=> utf8_ucfirst($info['CITY']),
		'COUNTRYID'		=> $info['COUNTRYID'],
		'TIMEZONE'		=> 'UTC',
		'LATITUDE'		=> '0',
		'LONGITUDE'		=> '0',
		'REGIONID'		=> '0',
		'PROVINCEID'	=> '0',
	];
	return city_commit();
}
function city_display_unknown():void  {
	layout_show_section_header(Eelement_plural_name('unknown_city'));

	if (!require_admin('city')) {
		return;
	}
	if (!obtain_lock(LOCK_INVALIDCITYLIST)) {
		?><div class="body force-link block"><?
		display_lockinfo(LOCK_INVALIDCITYLIST);
		?></div><?
		return;
	}
	require_once '_cityvalidation.inc';
	if (false === ($unknowns = db_rowuse_array(['user', 'user_account'], "
        SELECT USERID, user.NICK, CITY, ZIPCODE, user.COUNTRYID
        FROM user_account
        JOIN user USING (USERID)
        WHERE user_account.STATUS = 'active'
          AND CITY IS NOT NULL
          AND ( INVALID_CITY = ".CITY_CHECK.'
             OR INVALID_CITY = 0
            AND CITYID = 0)
        ORDER BY CITY'))
	) {
		return;
	}
	layout_open_menu();
	layout_open_menuitem();
	?><span class="unhidelink" onclick="unhide('city_invalids')"><?= Eelement_name('information') ?></span><?
	layout_close_menuitem();
	layout_close_menu();

	?><div class="hidden" id="city_invalids"><?
	layout_open_box('white');
	?><p>Je kunt een stad op meerdere manieren afkeuren.<br /><?
	?>Ten eerste is er de mogelijkheid om de stad permanent af te keuren. <?
	?>Elke keer als iemand dezelfde woorden als stad invult, zal deze direct worden afgekeurd.<br /><?
	?>Als tweede mogelijkheid is een eenmalige afkeuring. <?
	?>Dit kan handig zijn als een stad in principe wel bestaat, <?
	?>maar niet klopt aangezien het IP adres naar iets anders wijst, of postcode niet overeenkomt.</p><?
	?><p>De volgende eenmalige afkeurmogelijkheden worden geboden:</p><?
	// ONCE ONLY
	?><dl class="boldterm"><?

	?><dt>IP ander land</dt><?
	?><dd>Het IP nummer duidt een ander land aan dan waar de opgegeven stad in ligt.</dd><?

	?><dt>postcode&lt;&gt;stad</dt><?
	?><dd>Opgegeven postcode komt niet overeen met de stad (hoeft niet gecontroleerd te worden, gaat om stad in buitenland in combinatie met postcode in NL, die vallen hier onder)</dd><?

	?><dt>ongeldige postcode</dt><?
	?><dd>Postcode is niet correct.</dd><?

	?><dt>verzoek om postcode</dt><?
	?><dd>Er is een stad opgegeven waar er velen van zijn. Er is een postcode nodig om met een routeplanner evt een nabij gelegen stad te kunenn vinden.</dd><?

	?><dt>tijdelijk</dt><?
	?><dd>Opgegeven plaats is een tijdelijk onderkomen. Bijvoorbeeld een vakantie.</dd><?

	?><dt>zoek contact</dt><?
	?><dd>Stad ziet er leuk uit, misschien wel ok, maar niet helemaal begrepen, laat gebruiker contact opnemen via contact sectie.</dd><?
	?></dl><?

	// ALWAYS
	?><p>De volgende redenen kunnen opgegeven worden voor permanente afwijzigen:</p><?

	?><dl class="boldterm"><?
	?><dt>onzin</dt><?
	?><dd>Stad is onzin, bijvoorbeeld &quot;in de achtertuin&quot;, &quot;in een kartonnen doos&quot;</dd><?

	?><dt>onbegrepen</dt><?
	?><dd>Stad wordt niet begrepen, bijvoorbeeld &quot;H'bergen&quot; (wat is daar afgekort?)</dd><?

	?><dt>meer dan 1</dt><?
	?><dd>Er is meer dan 1 stad opgegeven, bijvoorbeeld &quot;Amsterdam &amp; Rotterdam&quot;</dd><?

	?><dt>omgeving</dt><?
	?><dd>Stad is niet duidelijk aangegeven, bijvoorbeeld &quot;omgeving Eindhoven&quot;, &quot;rond Amsterdam&quot;</dd><?

	?><dt>is gemeente</dt><?
	?><dd>Opgegeven stad is geen stad maar een gemeente.</dd><?

	?><dt>is provincie</dt><?
	?><dd>Opgegeven stad is geen stad maar een provincie.</dd><?

	?><dt>is land</dt><?
	?><dd>Opgegeven stad is geen stad maar een land.</dd><?

	?></dl><?
	layout_close_box();
	?></div><?

	if (!$unknowns) {
		?><p>Er zijn geen onbekende steden op dit moment.</p><?
		return;
	}
	layout_open_box('city');
	layout_open_table('fw vtop hha default');
	?><tr><?
	?><th><?= Eelement_name('user') ?></th><?
	?><th>Laatste IP</th><?
	?><th><?= Eelement_name('city') ?></th><?
	?><th><?= Eelement_name('zipcode') ?></th><?
	?><th>Eenmalig</th><?
	?><th>Toevoegen</th><?
	?><th></th><?
	?></tr><?
	require_once '_cityvalidation.inc';
	foreach ($unknowns as $unknown) {
		?><tr><?
		?><td class="rpad" id="u<?= $unknown['USERID'] ?>"><?= get_element_link('user', $unknown['USERID'], $unknown['NICK']) ?></td><?
		?><td class="rpad"><?
		if ($lastip = db_single_array(['iplist','ip6list'], /** @lang MariaDB */ "
			(	SELECT 'FFFF', HEX(IPNUM), LAST_USED
				FROM iplist
				WHERE LAST_USERID = {$unknown['USERID']}
				ORDER BY LAST_USED DESC
				LIMIT 1
			) UNION (
				SELECT HEX(PREFIX), HEX(IIDENT), LAST_USED
				FROM ip6list
				WHERE LAST_USERID = {$unknown['USERID']}
				ORDER BY LAST_USED DESC
				LIMIT 1
			)
			ORDER BY LAST_USED DESC
			LIMIT 1")
		) {
			[$prefix,$iident] = $lastip;
			$ipbin = prefix_and_iident_to_ipbin($prefix,$iident);
			$ipstr = inet_ntop($ipbin);
			$host = memcached_hostname_from_ipbin($ipbin);
			?><a target="_blank" href="/ip/<?= $ipstr ?>#whois"><?=
				preg_match('"^(.*?)([^.]+)\.([a-z]+)$"i',$host,$matches)
			?	'<small class="light">'.$matches[1].'</small><br />'.escape_specials($matches[2]).'.'.$matches[3]
			:	escape_specials($host)
			?></a><?
		} else {
			?>?<?
		}
		?></td><td class="rpad"><div class="block"><a href="https://maps.google.com/?q=<?
		echo urlencode($unknown['CITY']);
		if ($unknown['COUNTRYID']) {
			?>,<? echo urlencode(get_utf8_element_title('country',$unknown['COUNTRYID']));
		}
		?>" target="flockcitylookup"><?= escape_utf8($unknown['CITY'])
		?></a><?
		if ($unknown['COUNTRYID']) {
			?><br /><span class="light"><?=
				#escape_specials(get_element_title('country',$unknown['COUNTRYID']))
				__('country:'.$unknown['COUNTRYID'])
			?></span><?
		}
		?></div><?

		?></td><td class="rpad"><?
		if ($unknown['ZIPCODE']) {
			echo escape_specials($unknown['ZIPCODE']);
		}
		?></td><td class="rpad warning-nb"><?
		$action = 'invalidate';
				// IP MISMATCH
				?><a href="/city/<?= $action;
				?>/USERID/<?= $unknown['USERID'];
				?>/CITY_INVALID/<?= CITY_INVALID_IP_MISMATCH
				?>">IP&nbsp;ander&nbsp;land</a><br /><?
				// ZIPCODE MISMATCH
				/*?><a href="/city/<?= $action
				?>/USERID/<?= $unknown['USERID']
				?>/CITY_INVALID/<?= CITY_INVALID_ZIPCODE_MISMATCH;
				?>">postcode&lt;&gt;stad</a><br /><?
				// INVALID ZIPCODE
				?><a href="/city/<?= $action
				?>/USERID/<?= $unknown['USERID']
				?>/CITY_INVALID/<?= CITY_INVALID_ZIPCODE
				?>">ongeldige&nbsp;postcode</a><br /><?
				// FILL ZIPCODE
				?><a href="/city/<?= $action
				?>/USERID/<?= $unknown['USERID']
				?>/CITY_INVALID/<?= CITY_INVALID_FILL_ZIPCODE
				?>">verzoek&nbsp;om&nbsp;postcode</a><br /><?*/
				// TEMPORARY
				?><a href="/city/<?= $action
				?>/USERID/<?= $unknown['USERID']
				?>/CITY_INVALID/<?= CITY_INVALID_TEMPORARY
				?>">tijdelijk</a><br /><?
				// MAKE CONTACT
				?><a href="/city/<?= $action
				?>/USERID/<?= $unknown['USERID']
				?>/CITY_INVALID/<?= CITY_INVALID_MAKE_CONTACT
				?>">zoek&nbsp;contact</a><?
				?><br /><?
				// IS_HOLIDAY
				/*?><a href="/city/<?= $action;
				?>/USERID/<?= $unknown['USERID'];
				?>/CITY_INVALID/<?= CITY_INVALID_IS_HOLIDAY;
				?>">is&nbsp;vakantie&nbsp;adres</a><?
				?><br /><?*/
				// IS_FUTURE
				?><a href="/city/<?= $action
				?>/USERID/<?= $unknown['USERID']
				?>/CITY_INVALID/<?= CITY_INVALID_IS_FUTURE
				?>">is&nbsp;toekomstig</a><?
				?><br /><?
				// NEED_MORE_INFO
				?><a href="/city/<?= $action
				?>/USERID/<?= $unknown['USERID']
				?>/CITY_INVALID/<?= CITY_INVALID_NEED_MORE_INFO
				?>">meer&nbsp;info&nbsp;nodig</a><?

		?></td><?
		$action = 'addandinvalidate';
		?><td class="rpad warning-nb"><?
		// NONSENSE
			?><a href="/city/<?= $action
			?>/USERID/<?= $unknown['USERID']
			?>/CITY_INVALID/<?= CITY_INVALID_NONSENSE
			?>">onzin</a><br /><?
			// DONT UNDERSTAND
			?><a href="/city/<?= $action
			?>/USERID/<?= $unknown['USERID']
			?>/CITY_INVALID/<?= CITY_INVALID_DONT_UNDERSTAND
			?>">onbegrepen</a><br /><?
			// MORE THAN ONE
			?><a href="/city/<?= $action;
			?>/USERID/<?= $unknown['USERID'];
			?>/CITY_INVALID/<?= CITY_INVALID_MORE_THAN_ONE;
			?>">meer&nbsp;dan&nbsp;1</a><br /><?
			// VAGUE
			?><a href="/city/<?= $action
			?>/USERID/<?= $unknown['USERID']
			?>/CITY_INVALID/<?= CITY_INVALID_VAGUE
			?>">omgeving</a><br /><?
			// IS_ADDRESS
			?><a href="/city/<?= $action
			?>/USERID/<?= $unknown['USERID']
			?>/CITY_INVALID/<?= CITY_INVALID_IS_ADDRESS
			?>">is&nbsp;adres</a><br /><?
			// IS_CITYPART
			/*?><a href="/city/<?= $action;
			?>/USERID/<?= $unknown['USERID'];
			?>/CITY_INVALID/<?= CITY_INVALID_IS_CITYPART;
			?>">is&nbsp;wijk</a><br /><?*/
			// IS_MUNICIPALITY
			?><a href="/city/<?= $action
			?>/USERID/<?= $unknown['USERID']
			?>/CITY_INVALID/<?= CITY_INVALID_IS_MUNICIPALITY
			?>">is&nbsp;gemeente</a><br /><?
			// PROVINCE
			?><a href="/city/<?= $action
			?>/USERID/<?= $unknown['USERID']
			?>/CITY_INVALID/<?= CITY_INVALID_IS_PROVINCE
			?>">is&nbsp;provincie</a><br /><?
		?></td><td><?
		?><div class="block notice-nb"><?
			?><a href="/city/userform/USERID/<?= $unknown['USERID'] ?>"><?= __('action:change') ?></a><?
		?></div><?
		?><div class="block notice-nb"><?
			?><a target="_blank" href="/city/addunknown/<?= $unknown['USERID'] ?>"><?= __('action:add') ?></a><?
		?></div><?
		?></td></tr><?
	}
	layout_close_table();
	layout_close_box();
	?><div class="center"><a href="/city/autoreplace"><?= __('action:replace_automatically') ?></a></div><?
}

function city_invalidate(): bool {
	require_once '_cityvalidation.inc';
	require_once '_counthit.inc';
	if (!require_idnumber($_REQUEST,'USERID')
	||	!require_valid_invalid_city_reason($_REQUEST,'CITY_INVALID')
	||	false === ($invalid_city = db_single('user','
		SELECT INVALID_CITY
		FROM user
		WHERE USERID='.$_REQUEST['USERID']
	))) {
		return false;
	}
	if ($invalid_city !== CITY_VALID
	&&	$invalid_city !== CITY_CHECK
	) {
		_error('Lid is reeds aangepast, stad staat al op afgekeurd!');
		return false;
	}
	if (!db_insert('user_log','
		INSERT INTO user_log
		SELECT * FROM user
		WHERE USERID='.$_REQUEST['USERID'])
	||	!db_update('user','
		UPDATE user SET
			MSTAMP		='.CURRENTSTAMP.',
			MUSERID		='.CURRENTUSERID.',
			INVALID_CITY	='.$_REQUEST['CITY_INVALID'].'
		WHERE USERID='.$_REQUEST['USERID'])
	) {
		return false;
	}
	if ($_REQUEST['ACTION'] === 'invalidate') {
		_notice('Stad afgekeurd.');
		return true;
	}

	if (!($city_name = db_single('user','SELECT CITY FROM user WHERE USERID='.$_REQUEST['USERID']))) {
		return true;
	}
	$city_name = win1252_to_ascii($city_name);

	if (!($cityinvalidname = addslashes(trim(preg_replace('/[^[:alpha:]]+/u', '', $city_name))))) {
		error_log("could not produce cityinvalidname for $city_name");
		return false;
	}
	if (db_single('cityinvalid', 'SELECT 1 FROM cityinvalid WHERE NAME="'.addslashes($cityinvalidname).'" LIMIT 1')) {
		// already exists;
		return true;
	}
	if (!db_insert('cityinvalid','
		INSERT INTO cityinvalid SET
			NAME	= "'.addslashes($cityinvalidname).'",
			USERID	= '.CURRENTUSERID.',
			CSTAMP	= '.CURRENTSTAMP.',
			TYPE	= '.$_REQUEST['CITY_INVALID'])
	) {
		return false;
	}
	$cinvid = db_insert_id();
	counthit('cityinvalid',$cinvid);
	_notice('Stad afgekeurd en toegevoegd aan onzin lijst.');
	return true;
}

function city_display_no_lonlat() {
	if (!require_admin('city')) {
		return;
	}
	layout_show_section_header(Eelement_plural_name('city_without_degrees'));

	$cities = db_simple_hash('city','
		SELECT CITYID, NAME
		FROM city
		WHERE LONGITUDE = 0
		   OR LATITUDE = 0
		ORDER BY NAME ASC'
	);
	if ($cities === false) {
		return;
	}
	if (!$cities) {
		?><div class="block"><?= __('city:info:no_cities_without_degrees_LINE') ?></div><?
		return;
	}
	?><ul><?
	foreach ($cities as $cityid => $name) {
		?><li><a href="/city/<?= $cityid; ?>/form"><?= escape_utf8($name); ?></li><?
	}
	?></ul><?
}

function city_menu(): void {

	layout_open_menu();
	layout_menuitem(Eelement_name('overview'),'/city', !$_REQUEST['ACTION']);

	if (!have_admin('city')) {
		layout_close_menu();
	}
	require_once '_cityvalidation.inc';

	if (!($city_attention_counts = get_city_attention_counts())) {
		return;
	}

	extract($city_attention_counts, EXTR_OVERWRITE);

	$autorename_cnt = memcached_single('cityautorename', 'SELECT COUNT(*) FROM cityautorename', TEN_MINUTES);
	   $invalid_cnt = memcached_single('cityinvalid', 'SELECT COUNT(*) FROM cityinvalid', TEN_MINUTES);

	if (!$user_invalid_city_cnt
	&&	!$city_no_lonlat_cnt
	&&	!$city_no_province_cnt
	&&	!$autorename_cnt
	&&	!$invalid_cnt
	) {
		return;
	}

	if ($autorename_cnt) {
		layout_menuitem(Eelement_plural_name('auto-rename'), '/city/autorenames', $_REQUEST['ACTION'] === 'autorenames');
		?> <?= MIDDLE_DOT_ENTITY ?> <?
		echo $autorename_cnt;
	}
	if ($invalid_cnt) {
		layout_menuitem(__C('status:invalid'), '/city/invalids', $_REQUEST['ACTION'] === 'invalids');
		?> <?= MIDDLE_DOT_ENTITY ?> <?
		echo $invalid_cnt;
	}
	layout_close_menu();

	if (!$user_invalid_city_cnt
	&&	!$city_no_lonlat_cnt
	&&	!$city_no_province_cnt
	) {
		return;
	}

	layout_open_menu();
	if ($user_invalid_city_cnt) {
		layout_menuitem(__C('attrib:unknown'), '/city/showunknown', $_REQUEST['ACTION'] === 'showunknown');
		?> <?= MIDDLE_DOT_ENTITY ?> <?
		echo $user_invalid_city_cnt;
	}
	if ($city_no_lonlat_cnt) {
		layout_menuitem(__C('attrib:without_degrees'), '/city/shownolonlat', $_REQUEST['ACTION'] === 'shownolonlat');
		?> <?= MIDDLE_DOT_ENTITY ?> <?
		echo $city_no_lonlat_cnt;
	}
	if ($city_no_province_cnt) {
		layout_menuitem(__C('attrib:without_province'), '/city/emptyprovinces', $_REQUEST['ACTION'] === 'emptyprovinces');
		?> <?= MIDDLE_DOT_ENTITY ?> <?
		echo $city_no_province_cnt;
	}
	layout_close_menu();
}

function city_element_menu(int $cityid) {
	$cityhref = '/city/'.$cityid;

	layout_open_menu();
	if ($_REQUEST['ACTION'] !== 'form'
	&&	$_REQUEST['ACTION'] !== 'commit'
	) {
		layout_menuitem(__C('action:change'), $cityhref.'/form');
	}

	layout_continue_menu();
	show_element_menuitems();
	layout_close_menu();
}

function city_display_auto_renames(): void {
	if (!require_admin('city')) {
		return;
	}
	layout_section_header('Steden autorenames');

	?><p>Als een lid &eacute;&eacute;n van de aliassen invoert zoals hier onder weergegeven, dan wordt zijn stad automatisch vervangen!</p><?

	if (false === ($autorenames = db_rowuse_array(['cityautorename', 'city', 'country'], "
		SELECT	ALIASID, ALIAS, cityautorename.CITYID AS ID, TYPE,
			IF(city.NAME IS NOT NULL, city.NAME, country.NAME) AS NAME
		FROM cityautorename
		LEFT JOIN city ON TYPE = 'city' AND city.CITYID = cityautorename.CITYID
		LEFT JOIN country ON TYPE = 'country' AND country.COUNTRYID = cityautorename.CITYID"
	))) {
		return;
	}
	if (!$autorenames) {
		?><div class="block"><?= __('city:info:no_auto_renames_LINE') ?></div><?
		return;
	}
	doublestring_sort($autorenames, 'NAME', 'ALIAS');

	layout_open_table(TABLE_FULL_WIDTH | TABLE_SORTABLE);
	layout_start_header_row();
	layout_header_cell(Eelement_name('alias'),COLUMN_IS_SORTED_ASC);
	layout_next_header_cell();
	echo Eelement_name('place');
	layout_next_header_cell(COLUMN_DONT_SORT);
	layout_stop_header_cell();
	layout_stop_header_row();
	foreach ($autorenames as $autorename) {
		?><tr><td><?= escape_utf8($autorename['ALIAS']);
		?></td><td><?= get_element_link($autorename['TYPE'], $autorename['ID'], $autorename['NAME']);
		?></td><td class="right"><a href="/city/autorenamerem/ALIASID/<?= $autorename['ALIASID'];
		?>"><?= __('action:remove'); ?></a></td></tr><?
	}
	layout_close_table();
}

function city_remove_auto_rename() {
	if (!require_admin('city')
	||	!require_idnumber($_REQUEST,'ALIASID')
	||	!db_insert('cityautorename_log','
		INSERT INTO cityautorename_log
		SELECT * FROM cityautorename
		WHERE ALIASID='.$_REQUEST['ALIASID'])
	||	!db_delete('cityautorename','
		DELETE FROM cityautorename
		WHERE ALIASID='.$_REQUEST['ALIASID'])
	) {
		return;
	}
	register_notice('city:notice:auto_rename_removed_LINE');
}

function city_add_auto_rename(): void {
	require_once '_counthit.inc';
	if (!require_admin('city')
	||	!require_idnumber($_POST, 'CITYID')
	||	!require_something($_POST, 'ALIAS', utf8: true)
	||	!db_insert('cityautorename','
		INSERT INTO cityautorename SET
			USERID	= '.CURRENTUSERID.',
			CSTAMP	= '.CURRENTSTAMP.',
			CITYID	= '.$_POST['CITYID'].',
			ALIAS	= "'.addslashes(utf8_mytrim($_POST['ALIAS'])).'"')
	) {
		return;
	}
	$aliasid = db_insert_id();
	counthit('cityautorename', $aliasid);
	register_notice('city:notice:auto_rename_added');
}

function city_display_overview() {
	layout_show_section_header();
	city_menu();

	if (have_admin('city')) {
		layout_open_menu();
		layout_menuitem(__C('action:add'), '/city/form');
		layout_close_menu();
	}

	$name = require_something_trim_clean_or_none($_REQUEST, 'NAME', utf8: true);

	?><form accept-charset="utf-8" onsubmit="return submitForm(this)" method="get" action="/city/searchresult"><?
	?><input type="hidden" name="enc" value="&#129392;" /><?
	?><div class="block"><?
	?><input placeholder="<?= __('action:search_for_city') ?>" type="search" autosave="cityname" name="NAME" id="cityname" autofocus="autofocus" required="required"<?
	if ($name) {
		?> value="<?= escape_utf8($name) ?>"<?
	}
	?> /> <?
	?><input type="submit" value="<?= __('action:search') ?>" /></div><?
	?></form><?
	if ($_REQUEST['ACTION'] === 'searchresult') {
		if ($name) {
			?><div class="bold block"><?= Eelement_name('search_term') ?>: &quot;<?= escape_utf8($name) ?>&quot;</div><?

			require_once '_citylist.inc';
			$citylist = new _citylist;
			$citylist->name_like($name);
			if ($citylist->query()) {
				if ($citylist->have_rows()) {
					layout_open_box('city');
					$citylist->display();
					layout_close_box();
				} else {
					?><div class="block"><? __('search:info:nothing_found_LINE') ?></div><?
				}
			}
		}
	} elseif ($_REQUEST['ACTION']) {
		not_found();
		return;
	}
}
function city_display_form() {
	if (!require_admin('city')) {
		return;
	}
	layout_show_section_header();

	city_menu();

	if ($cityid = $_REQUEST['sID']) {
		if (!($city = db_single_assoc('city', "SELECT * FROM city WHERE CITYID = $cityid"))) {
			if ($city !== false) {
				not_found();
			}
			return;
		}
		if (have_post()) {
			foreach ($city as $key => &$val) {
				if (isset($_POST[$key])) {
					$val = $_POST[$key];
				}
			}
			unset($val);
		}
		if ($city['LONGITUDE'] === null
		||	$city['LATITUDE']  === null
		) {
			if (isset($_REQUEST['USERID'])) {
				if (!require_idnumber($_REQUEST,'USERID')) {
					return;
				}
				if (false === ($zipcode = db_single('user','SELECT ZIPCODE FROM user WHERE USERID='.$_REQUEST['USERID']))) {
					_error('Postcode van lid met ID '.$_REQUEST['USERID'].' is niet opvraagbaar!');
					return;
				}
				if ($zipcode) {
					?><p><?= Eelement_name('zipcode') ?>: <?= escape_specials($zipcode) ?></p><?
				}
			}
		}
	} else {
		$city = null;
	}

	require_once '_countrylist.inc';
	require_once '_provincelist.inc';
	require_once '_timezoneoptions.inc';

	?><form accept-charset="utf-8" onsubmit="return submitForm(this)" method="post" id="elementform" action="/city<?
	if (isset($city)) {
		?>/<? echo $city['CITYID'];
	}
	?>/commit"><?
	?><input type="hidden" name="enc" value="&#129392;" /><?

	include_js('js/form/city');

	require_once '_ticket.inc';

	ticket_passthrough();

	$codes = db_simple_hash('country','SELECT SHORT,COUNTRYID FROM country');
	echo '<script>',
		'function getCountryid(code,results){',
			'switch(code){';
			foreach ($codes as $code => $countryid) {
				echo "case '$code':";
				switch($code) {
				case 'CN': # China, Tibet
					echo "return findCountry('Tibet',results)?92:50;";
					break;
				case 'GB': # UK, Schotland
					echo "return findCountry('Schotland',results)?48:3;";
					break;
				default:
					echo "return $countryid;";
				}
			}
	echo	'}}', '</script>';

	if (false === ($regions = db_same_hash('region','SELECT COUNTRYID,REGIONID FROM region'))) {
		return;
	}

	layout_open_box('city');

	require_once '_google_maps.inc';
	if ($_REQUEST['ACTION'] === 'addunknown') {
		?><script>addreadyevent(function(){let mapsq=getobj('mapsq');lookupAddress(mapsq)})</script><?
	}
	$address_parts = [];
	if ($city) {
		$address_parts[] = $city['NAME'];
		if ($city['PROVINCEID']
		&&	($province_name = db_single('province','SELECT NAME FROM province WHERE PROVINCEID = '.$city['PROVINCEID']))
		) {
			$address_parts[] = $province_name;
		}
		if ($city['COUNTRYID']) {
			$address_parts[] = win1252_to_utf8(get_element_title('country',$city['COUNTRYID']));
		}
	}

	layout_open_table('fw','xxx');
	layout_start_row();
		echo Eelement_name('country');
		layout_field_value();
		?><select required onchange="changeCountry(this);lookupCountry(this);update_mapsqf(this.form)" name="COUNTRYID" name="country"><?
			?><option value="0"></option><?
			_countrylist_display_options(isset($city) ? $city['COUNTRYID'] : 0);
		?></select><?
	layout_restart_row();
		echo Eelement_name('province');
		layout_field_value();
		?><div id="provinces"><?
		?><select onchange="update_mapsqf(this.form)" name="PROVINCEID"><?
		?><option value="0"></option><?
		provincelist_display_options(
			isset($city) ? $city['PROVINCEID'] : 0,
			isset($city) ? $city['COUNTRYID'] : 1
		);
		?></select><?
		?></div><?


		ob_start();
		?><select name="REGIONID"><?
		?><option value="0"></option><?
		$visi = false;
		foreach ($regions as $countryid => $regions) {
			foreach ($regions as $regionid => &$name) {
				$name = get_element_title('region',$regionid);
			}
			unset($name);
			asort($regions);
			?><optgroup<?
			if ($hidden = $city && $city['COUNTRYID'] != $countryid) {
				?> class="hidden"<?
			} else {
				$visi = true;
			}
			?> data-forcountry="<?= $countryid ?>"<?
			?> label="<?= escape_specials(get_element_title('country',$countryid)); ?>"><?
			foreach ($regions as $regionid => $name) {
				?><option<?
				if (!$hidden && $city && $regionid == $city['REGIONID']) {
					?> selected<?
				}
				?> value="<?= $regionid ?>"<?
				?>><?= escape_specials($name) ?></option><?
			}
			?></optgroup><?
		}
		?></select><?
		$data = ob_get_clean();

	layout_restart_row($visi ? 0 : ROW_HIDDEN,'regionrow');
		echo Eelement_name('region');
		layout_field_value();
		echo $data;

	layout_restart_row();
		?><label for="name"><?= Eelement_name('name') ?></label><?
		layout_field_value();
		show_input([
			'onchange'		=> /** @lang JavaScript */ 'update_mapsqf(this.form);',
			'type'			=> 'text',
			'id'			=> 'name',
			'name'			=> 'NAME',
			'value_escaped'	=> $city ? escape_utf8($city['NAME']) : null,
			'required'		=> true
		]);

	require_once '_alternate_name.inc';
	show_alternate_name_form('city', $cityid);

	layout_restart_row();
		echo Eelement_name('timezone');
		layout_field_value();
		?><select required="required" name="TIMEZONE" id="timezones"><?
			show_timezone_options(
				$city['COUNTRYID'] ?? 0,
				$city ? ($city['TIMEZONE'] == 'UTC' ? null : $city['TIMEZONE']) : null,
				true
			);
		?></select><?
		require_once '_bubble.inc';
		simple_help(__('timezone:help:automatically_filled_on_latlon_change_LINE'));
	layout_restart_row();
		echo Eelement_name('map');
		layout_field_value();
		require_once '_searchmap.inc';
		show_search_map('city', $cityid, $city, 13, implode(', ', $address_parts), true);

	layout_restart_row();
		echo Eelement_name('latitude');
		layout_field_value();
		show_gps([
			'onchange'	=> 'changeCoords(this)',
			'required'	=> true,
			'name'		=> 'LATITUDE',
			'value'		=> $lat = $city['LATITUDE'] ?? null,
			'data-tz'	=> $lat
		]);
	layout_restart_row();
		echo Eelement_name('longitude');
		layout_field_value();
		show_gps(array(
			'onchange'	=> 'changeCoords(this)',
			'required'	=> true,
			'name'		=> 'LONGITUDE',
			'value'		=> $lon = $city['LONGITUDE'] ?? null,
			'data-tz'	=> $lon
		));
	layout_restart_row();
		echo Eelement_name('extra');
		layout_field_value();
		$municipality_name = !empty($city['MUNICIPALITID']) ? db_single('municipality','SELECT NAME FROM municipality WHERE MUNICIPALITID='.$city['MUNICIPALITID']) : null;
		?><input type="text" name="MUNICIPALITY_NAME" value="<?
		if ($municipality_name) {
			echo escape_utf8($municipality_name);
		}
		?>"><?
		if (!empty($city['MUNICIPALITID'])) {
			?><input type="hidden" name="MUNICIPALITID" value="<?= $city['MUNICIPALITID']; ?>" /><?
		}
	layout_stop_row();
	layout_close_table();
	layout_close_box();
	?><div class="block"><input type="submit" value="<?= __(isset($city) ? 'action:change' : 'action:add'); ?>" /></div></form><?
}
function city_display_single() {
	if (!($cityid = require_idnumber($_REQUEST,'sID'))) {
		return;
	}
	$city = memcached_single_assoc_if_not_admin(
		'city',
		['city','province'],'
		SELECT	CITYID,city.NAME,LATITUDE,LONGITUDE,DELETED,city.MUSERID,city.USERID,city.CSTAMP,city.MSTAMP,city.CLEANNAME,
			city.COUNTRYID,
			city.PROVINCEID,province.NAME AS PROVINCENAME,REGIONID,
			city.MUNICIPALITID,municipality.NAME AS MUNICIPALITY_NAME
		FROM city
		LEFT JOIN province ON province.PROVINCEID = city.PROVINCEID
		LEFT JOIN municipality ON municipality.MUNICIPALITID = city.MUNICIPALITID
		WHERE CITYID='.$cityid
	);
	if ($city === false) {
		return;
	}
	if (!$city) {
		not_found();
		return;
	}

	if ($_REQUEST['ACTION'] === 'single') {
		include_meta('description', __('metad:city:single',DO_UBBFLAT,['CITYID'=>$cityid]));
	}

	layout_show_section_header();

	city_menu();

	if ($city_admin = have_admin('city')) {
		city_element_menu($cityid);
	}

	require_once '_ticketlist.inc';
	show_connected_tickets();

	$cnts = memcached_single_assoc(
		array('party','location','boarding'), '
		SELECT	COUNT(IF(STAMP >= '.TODAYSTAMP.',1,NULL)) AS FUTURE_CNT,
			COUNT(IF(STAMP >= '.TODAYSTAMP.' AND party.ACCEPTED=1,1,NULL)) AS FUTURE_OK_CNT,
			COUNT(*) AS TOTAL_CNT,
			COUNT(IF(party.ACCEPTED=1,1,NULL)) AS TOTAL_OK_CNT
		FROM party
		JOIN (	SELECT PARTYID
			FROM party
			WHERE CITYID='.$cityid.'
			UNION
			SELECT PARTYID
			FROM party
			JOIN location USING (LOCATIONID)
			WHERE BOARDINGID=0
			  AND location.CITYID='.$cityid.'
			UNION
			SELECT PARTYID
			FROM party
			JOIN boarding USING (BOARDINGID)
			WHERE boarding.CITYID='.$cityid.'
		) AS party_city USING (PARTYID)',
		TEN_MINUTES
	);
	if (!$cnts) return;
	if (!have_admin('party')) {
		$cnts['TOTAL_CNT'] = $cnts['TOTAL_OK_CNT'];
		$cnts['FUTURE_CNT'] = $cnts['FUTURE_OK_CNT'];
	}
	$cnts['PAST_CNT'] = $cnts['TOTAL_CNT'] - $cnts['FUTURE_CNT'];
	$cnts['LOCATION'] = memcached_single('location','SELECT COUNT(*) FROM location WHERE CITYID='.$cityid);
	$cnts['BOARDING'] = memcached_single('location','SELECT COUNT(*) FROM boarding WHERE CITYID='.$cityid);
	$cnts['ORGANIZATION'] = memcached_single('organization','SELECT COUNT(*) FROM organization WHERE CITYID='.$cityid);
	$cnts['USER'] = memcached_single('user', 'SELECT COUNT(*) FROM user WHERE CITYID='.$cityid);
/*	FIXME: remove slower query if in future not needed
	$cnts['USER'] = memcached_single(['user','user_account'],'
		SELECT COUNT(*)
		FROM user
		JOIN user_account USING (USERID)
		WHERE user_account.STATUS="active"
		  AND INVALID_CITY=0
		  AND CITYID='.$cityid,
		ONE_DAY
	);*/

	require_once '_background_queries.inc';
	$visitorcnts = get_bgquery(BGQRY_VISITORS,'city',$cityid);
	if ($visitorcnts) {
		[$cnts['VISITOR_CNT'],$cnts['UNIQUE_CNT']] = keyval($visitorcnts);
	}

	?><article itemscope itemtype="https://schema.org/City"><?
	layout_open_box('city');
	layout_open_box_header();
	?><h1 itemprop="name"><?
	echo escape_utf8($city['NAME']);
	if ($city['MUNICIPALITY_NAME']) {
		?> (<?= escape_utf8($city['MUNICIPALITY_NAME']) ?>)<?
	}
	?></h1><?
	layout_close_box_header();

	require_once '_google_maps.inc';
	show_mini_google_map('city',$cityid,$city);

	if ($city['DELETED']) {
		?><p>Deze stad is <span class="underline">verwijderd</span>!<br /><?
		?>Het kan zijn dat het geen stad, maar een wijk, gemeente of adres is.</p><?
	}
	if ($mapping
		=	$city['LONGITUDE'] !== null
		&&	$city['LATITUDE']  !== null
	) {
		?><div class="l"><?
	}

	$list = new deflist('deflist vtop');

	$list->add_row(Eelement_name('country'), get_element_link('country',$city['COUNTRYID']));

	if ($city_admin) {
		$list->set_row_class('light');
		$list->add_row(Eelement_name('base').' '.element_name('recognition'), escape_utf8($city['CLEANNAME']));
	}
	if ($city['REGIONID']) {
		$list->add_row(Eelement_name('region'), get_element_link('region',$city['REGIONID']));
	}
	if ($city['PROVINCENAME']) {
		$list->add_row(Eelement_name('province'), get_element_link('province', $city['PROVINCEID'], $city['PROVINCENAME']));
	}
	if ($city_admin) {
		if (false === ($renames = memcached_simpler_array('cityautorename', "
			SELECT ALIAS
			FROM cityautorename
			WHERE CITYID = $cityid
			  AND TYPE = 'city'
			ORDER BY ALIAS ASC"
		))) {
			return;
		}
		if ($renames) {
			$list->set_row_class('light');
			$list->add_row(
				Eelement_plural_name('auto-rename'),
				nl2br(escape_utf8(implode("\n", $renames)))
/*				'<span onclick="swapdisplay(\'autorenames\')" class="unhideanchor">'.Eelement_plural_name('auto-rename').'</span>',
				'<span onclick="swapdisplay(\'autorenames\')" class="unhideanchor">'.count($renames).'</span>'.
					'<div class="hidden" id="autorenames">'.
						nl2br(escape_utf8(implode("\n", $renames)))
					'</div>'*/
			);
		}
	}

	$list->display();

	if ($mapping) {
		require_once '_maplink.inc';
		?></div><div class="l lmrgn"><?
		show_map_link($city);
		?></div><?
		?><div class="lclr"></div><?
	}

	require_once '_alternate_name.inc';
	show_alternate_names('city', $cityid, $city['NAME']);

	require_once '_affiliates.inc';
	show_booking_com('city',$cityid,$city);

	layout_display_alteration_note($city);
	layout_close_box();

	require_once '_partylist.inc';
	$partylist = new _partylist;

	$partylist->in_city($cityid);
	$partylist->select_future();
	$partylist->show_vevent = true;
	$partylist->show_date_headers = true;
	$partylist->one_table = true;
	$partylist->show_lineups = true;
#	$partylist->show_date = false;
	$partylist->hide_flag = true;
	$partylist->hide_subtitle =
	$partylist->show_location =
#	$partylist->show_buddy_hearts =
	$partylist->show_people =
	$partylist->show_buddies =
	$partylist->show_stars = true;
	$partylist->order_chronologically_but_notime();
	if (!$partylist->query()) {
		return;
	}
	require_once '_feed.inc';
	layout_open_box('city agenda','agenda');

	if (SMALL_SCREEN) {
		city_show_stats($cnts);
		layout_continue_box();
	}

	layout_open_box_header();
	?><h2><?
	$future = $partylist->size();
	if (!$future) {
		?><span class="light"><?
	}
#	echo Eelement_name('agenda');
	echo Eelement_name('party_agenda');
	?> <?
	echo escape_utf8($city['NAME']);
	if (!$future) {
		?></span><?
	}
	?></h2><?
	?><nav style="display:inline"> <?= MIDDLE_DOT_ENTITY ?> <?= __('action:see_also') ?>:<?
	?> <a href="/agenda/now-and-soon/<?= $cityid ?>"><?= __('nowandsoon:in_the_neighbourhood') ?></a></nav><?

	$parts = [];
	if (!ROBOT) {
		$parts[] = get_ical_feed_link();
		if (SHOW_FEED_ICON) {
			$parts[] = get_feed('agenda',FEED_ICON);
		}
	}
	if ($cnts['PAST_CNT']) {
		[$linkopen, $linkclose] = get_action_open_close('archive');
		$parts[] = $linkopen.element_name('archive').$linkclose;
	}
	if ($parts) {
		layout_continue_box_header();
		?><nav><?= implode(' '.MIDDLE_DOT_ENTITY.' ',$parts) ?></nav><?
	}
	layout_close_box_header();
	if ($future) {
		$partylist->display();
	} elseif ($cnts['TOTAL_CNT']) {
		require_once '_lastparty.inc';
		show_last_party();
	}
	unset($partylist);
	if (!SMALL_SCREEN) {
		layout_close_box();
		layout_open_box('city');

		city_show_stats($cnts);
	}

	layout_close_box();

	switch ($_REQUEST['ACTION']) {
	case 'users':
		require_once '_userlist.inc';
		$userlist = new _userlist;
		$userlist->in_city($cityid);
		$userlist->show_heart = true;
		$userlist->show_camera = true;
		$userlist->only_active();
		if (!$userlist->query()) {
			return;
		}
		if ($userlist->size) {
			layout_open_box('city','users');
			layout_box_header(Eelement_plural_name('user'));
			$userlist->display();
			layout_close_box();
		}
		unset($userlist);
		break;
	// LOCATIONS
	case 'locations':
		layout_open_box('city','locations');
		require_once '_locationlist.inc';
		$locationlist = new _locationlist;
		$locationlist->in_city($cityid);
		$locationlist->select_future();
		$locationlist->show_header = true;
		$locationlist->show_prefix = true;
		$locationlist->show_stats = true;
		$locationlist->show_country = false;
		$locationlist->show_address = true;
		$locationlist->query();
		if (!$locationlist->is_empty()) {
			layout_box_header(Eelement_plural_name('location'));
			$locationlist->display();
		}
		$locationlist->clear();
		$locationlist->in_city($cityid);
		$locationlist->select_past();
		$locationlist->query();
		if (!$locationlist->is_empty()) {
			layout_box_header(Eelement_plural_name('inactive_location'));
			$locationlist->display();
		}
		layout_close_box();
		unset($locationlist);
		break;
	// BOARDINGS
	case 'boardings':
		layout_open_box('city','boardings');
		require_once '_boardinglist.inc';
		$boardinglist = new boardinglist;
		$boardinglist->in_city($cityid);
		$boardinglist->show_header = true;
		$boardinglist->show_prefix = true;
		$boardinglist->show_stats = true;
		$boardinglist->show_country = false;
		$boardinglist->show_address = true;
		$boardinglist->query();
		if (!$boardinglist->is_empty()) {
			layout_box_header(Eelement_plural_name('boarding'));
			$boardinglist->display();
		}
		layout_close_box();
		unset($boardinglist);
		break;
	// ORGANIZATIONS
	case 'organizations':
		require_once '_organizationlist.inc';
		$organizationlist = new _organizationlist;
		$organizationlist->in_city($cityid);
		$organizationlist->openbox = false;
		$organizationlist->show_partycnt = true;
		$organizationlist->show_header = false;
		if ($organizationlist->query()) {
			layout_open_box('city','organizations');
			layout_box_header(Eelement_plural_name('organization'));
			$organizationlist->display();
			layout_close_box();
		}
		break;
	// ARCHIVE
	// archive takes too much memory for current list
	case 'archive':
		set_memory_limit(GIGABYTE);
		set_time_limit(TEN_MINUTES);

		require_once '_partylist.inc';
		$partylist = new _partylist;
		$partylist->in_city($cityid);
		$partylist->select_past();
		$partylist->one_table =
		$partylist->show_date_headers =
		$partylist->hide_flag =
		$partylist->show_location = true;
		$partylist->order_reverse_chronologically_but_notime();
		$start = time();
		if ($partylist->query()) {
			layout_open_box('city', 'archive');
			layout_box_header(Eelement_name('archive'));
			$partylist->display();
			layout_close_box();
		}
		unset($partylist);
		break;

/*	case 'visitmap':
		require_once '_usermap.inc';
		show_usermap_overview('city',$cityid,VISITOR_MAP);
		break;*/

	default:
		$showcmts = true;
		break;
	}
	if (isset($showcmts)) {
		require_once '_commentlist.inc';
		$cmts = new _commentlist;
		$cmts->item($city);
		$cmts->display();
	}
	?></article><?
}
function city_show_stats($cnts) {
	ob_start();
	foreach ([
		'USER'		=> ['user',			'users'],
		'LOCATION'	=> ['location',			'locations'],
		'BOARDING'	=> ['boarding',			'boardings'],
		'TOTAL_CNT'	=> ['party',			null],
		'FUTURE_CNT'	=> ['when:in_the_future',	null,		true],
		'PAST_CNT'	=> ['when:in_the_past',		null,		true],

		'VISITOR_CNT'	=> ['visitor',			null],
		'UNIQUE_CNT'	=> ['unique_visitor',		null],
		'ORGANIZATION'	=> ['organization',		'organizations'],
	] as $which => $info) {
		if (empty($cnts[$which])) {
			continue;
		}
		[$elementname,$hash] = $info;
		if (!$hash) {
			$linkopen = $linkclose = null;
		} else {
			[$linkopen,$linkclose] = get_action_open_close($hash);
		}
		layout_start_reverse_row();
		echo $linkopen,$cnts[$which],$linkclose;
		layout_value_field();
		echo $linkopen,(isset($info[2]) ? __($elementname) : element_name($elementname,$cnts[$which])),$linkclose;

/*		if ($which == 'VISITOR_CNT') {
			list($linkopen,$linkclose) = get_action_open_close('visitmap');
			?> <?= MIDDLE_DOT_ENTITY ?> <?
			echo $linkopen,element_name('origin'),$linkclose;
		}*/

		layout_stop_row();

	}
	if ($data = ob_get_clean()) {
		layout_box_header(Eelement_plural_name('statistic'));
		layout_open_table(TABLE_FULL_WIDTH);
		echo $data;
		layout_close_table();
	}
}

function city_commit(): bool {
	if (!require_admin('city')
	||	!require_something_trim($_POST, 'NAME', null, null, utf8: true)
	||	false === require_lng_or_lat($_POST, 'LATITUDE', utf8: true)
	||	false === require_lng_or_lat($_POST, 'LONGITUDE', utf8: true)
	||	false === require_number($_POST, 'PROVINCEID', utf8: true)
	||	false === require_number($_POST, 'COUNTRYID', utf8: true)
	||	false === require_number($_POST, 'REGIONID', utf8: true)
	||	!require_something_trim($_POST, 'TIMEZONE', null, null, utf8: true)
	) {
		return false;
	}
	$setlist[] = 'COUNTRYID		='.$_POST['COUNTRYID'];
	$setlist[] = 'PROVINCEID	='.$_POST['PROVINCEID'];
	$setlist[]=  'REGIONID		='.$_POST['REGIONID'];
	$setlist[] = 'NAME		="'.addslashes($_POST['NAME']).'"';
	$setlist[] = 'LATITUDE		='. round($_POST['LATITUDE'], 12);
	$setlist[] = 'LONGITUDE		='.round($_POST['LONGITUDE'], 12);
	$setlist[] = 'TIMEZONE		="'.addslashes($_POST['TIMEZONE']).'"';
	$setlist[] = 'CLEANNAME		="'.addslashes(preg_replace('/[^[:alpha:]]+/', '', utf8_to_ascii($_POST['NAME']))).'"';

	if (!empty($_POST['MUNICIPALITY_NAME'])) {
		if ($municipalitid = have_idnumber($_POST, 'MUNICIPALITID')) {
			if (!db_update('municipalitiy','
				UPDATE municipality SET
					NAME = "'.addslashes($_POST['MUNICIPALITY_NAME']).'"
				WHERE MUNICIPALITID = '.$municipalitid)
			) {
				return false;
			}
		} else {
			if (!db_insert('municipality','
				INSERT INTO municipality SET
					NAM E ="'.addslashes($_POST['MUNICIPALITY_NAME']).'"')
			) {
				return false;
			}
			$municipalitid = db_insert_id();
		}
		$setlist[] = 'MUNICIPALITID = '.$municipalitid;
	} else {
		if (have_idnumber($_POST, 'MUNICIPALITID')) {
			if (!db_delete('municipality','
				DELETE FROM municipality
				WHERE MUNICIPALITID = '.$_POST['MUNICIPALITID'])
			) {
				return false;
			}
		}
		$municipalitid = 0;
		$setlist[] = 'MUNICIPALITID = 0';
	}

	$cityid = $_REQUEST['sID'];

	$other_cityid = db_single('city', '
		SELECT CITYID
		FROM city
		WHERE NAME = "'.addslashes($_POST['NAME']).'"
		  AND COUNTRYID = '.$_POST['COUNTRYID'].'
		  AND PROVINCEID = '.$_POST['PROVINCEID'].'
		  AND MUNICIPALITID = '.$municipalitid.
		($cityid ? ' AND CITYID != '.$cityid : '')
	);

	if ($other_cityid === false) {
		return false;
	}
	if ($other_cityid) {
		register_error('city:error:identical_exists_LINE', DO_UBB, ['CITYID' => $other_cityid]);
		$_REQUEST['sID'] = $other_cityid;
		return false;
	}

	if ($cityid) {
		if (!db_insert('city_log','
			INSERT INTO city_log
			SELECT * FROM city
			WHERE NOT '.binary_equal($setlist).'
			  AND CITYID = '.$cityid)
		) {
			return false;
		}
		if (db_affected()) {
			if (!db_update('city','
				UPDATE city SET
					MUSERID	= '.CURRENTUSERID.',
					MSTAMP	= '.CURRENTSTAMP.','.
					implode(', ', $setlist).'
				WHERE CITYID = '.$cityid)
			) {
				return false;
			}
		}
		register_notice('city:notice:changed_LINE');

		require_once '_cityvalidation.inc';
		flush_city_stuff_on_city_change();
	} else {
		$setlist[] = 'USERID = '.CURRENTUSERID;
		$setlist[] = 'CSTAMP = '.CURRENTSTAMP;

		if (!db_insert('city', '
			INSERT INTO city SET '.implode(',', $setlist))
		) {
			return false;
		}
		$_REQUEST['sID'] = $cityid = db_insert_id();
		register_notice('city:notice:added_LINE');

		require_once '_ticket.inc';
		ticket_update('city', $cityid);
	}

	require_once '_alternate_name.inc';
	commit_alternate_names('city', $cityid);

	page_changed('city', $cityid);
	return true;
}
