<?php

function get_event_advice(array $party, bool $force = false, bool $nocache = false): array|false|null {
	require_once '_customer.inc';
	$customer_event = is_customer_event($party);

/*	error_log($_SERVER['REMOTE_ADDR'].
		' getting advice for '.$party['PARTYID'].
		', force: '.($force ? 'yes' : 'no').
		', nocache: '.($nocache ? 'yes' : 'no').
		', customer_event: '.($customer_event ? 'yes' : 'no').
		', request: '.$_SERVER['REQUEST_URI'].
		', user_agent: '.($_SERVER['HTTP_USER_AGENT'] ?? '')
	);
	return;*/

	if (!$force
	&&	$customer_event
	) {
		# not showing advice on customer pages
		return null;
	}

	$debug = HOME_THOMAS && isset($_REQUEST['debug']);
	$partyid = $party['PARTYID'];
	$main_id = $party['MAIN_ID'];
	$context = 'party:'.$partyid.', user: '.CURRENTUSERID.', identid: '.CURRENTIDENTID.', ip: '.$_SERVER['REMOTE_ADDR'];

	# NOTE: If events in this picklist change, then the list might not be most appropriate.
	#	It would be best to invalidate the picklists containing and event that has changed.

	if (false === ($party_mstamp = db_single_int('pagechanged','SELECT MSTAMP FROM pagechanged WHERE ELEMENT="party" AND ID='.$partyid))) {
		return false;
	}

	$picklistkey	 = "advice_picklist:v2:$partyid:$party_mstamp";
	$picklistdonekey = "$picklistkey:done";

	$done_stamp = memcached_get($picklistdonekey);

	$picklist = ($nocache || $debug || isset($_REQUEST['NOMEMCACHE'])) ? false : memcached_get($picklistkey);

	if (!$force
	&&	!$picklist
	&&	(	ROBOT
		||	!CURRENTIDENTID)
	) {
		// don't generate advice for prolly robots
		// but do show if humans have visited this party
		return null;
	}

	if ($picklist === false) {
		if (!($party = memcached_party_and_stamp($partyid))
		||	!$party['ACCEPTED']
		) {
			return null;
		}

		$master_type = explode_to_hash(',',$party['LOCATIONTYPE']);

		if (isset($master_type['beach'])) {
			$master_type['outdoor'] = 'outdoor';
			unset($master_type['beach']);
		}

		$master_type_cnt = count($master_type);

		# get all events in same master genre
		if (false === ($masters = memcached_same_hash('event_master_genre', /** @lang MariaDB */ "
			/* advice $context */
			SELECT MGID
			FROM event_master_genre
			WHERE MGID
			  AND PARTYID = $partyid",
			ONE_HOUR))

			# only erotic event advice on erotic event pages
			# only stream advice on stream event pages
		||	false === ($other_events = memcached_simple_hash(['event_master_genre', 'party'], /** @lang MariaDB */ "
			/* advice $context */
			SELECT DISTINCT PARTYID, LOCATIONTYPE
			FROM event_master_genre
			JOIN party USING (PARTYID)
			WHERE MGID
			  AND ACCEPTED
			  AND ".($party['EROTIC'] ? 'EROTIC' : 'EROTIC = 0').'
			  AND LIVESTREAM IN ('.($party['LIVESTREAM'] === 'only' ? '"only"' : '"","+event"').')
			  AND CANCELLED = 0
			  AND POSTPONED = 0
			  AND STAMP > '.TODAYSTAMP,
			ONE_HOUR))
		) {
			return false;
		}
		assert(is_array($other_events)); # Satisfy static analysis
#		print_rr($other_events,'other_events.'.$q);
		unset($other_events[$partyid]);

		if (!$other_events) {
			return null;
		}

		ksort($other_events);

		$other_eventsidstr = implodekeys(',',$other_events);

		if ($masters) {
			if (false === ($event_mgids = memcached_simple_hash('event_master_genre', /** @lang MariaDB */ "
				/* advice $context */
				SELECT PARTYID, GROUP_CONCAT(DISTINCT MGID ORDER BY MGID)
				FROM event_master_genre
				WHERE PARTYID IN ($other_eventsidstr)
				GROUP BY PARTYID",
				ONE_HOUR))
			) {
				return false;
			}

		#	print_rr($event_mgids);
		#	exit;

			$masters_cnt = count($masters);

			foreach ($event_mgids as $tmp_partyid => $mgidstr) {
				$mgids = explode_to_hash(',',$mgidstr);

				# $max_match = max(count($mgids),$masters_cnt);

				$identicals = array_intersect($masters,$mgids);

				$identical_cnt = count($identicals);
		/*
				print_rr([
					'party'		=> get_element_title('party',$tmp_partyid),
					'mgids'		=> $mgids,
					'identicals'	=> $identicals,
					'identical_cnt' => $identical_cnt,
					'max_match'	=> $max_match,
				]);
		*/
				if (!$masters_cnt) {
					mail_log('masters_cnt is zero', get_defined_vars());
					$mgid_match[$tmp_partyid] = 0;
				} else {
					$mgid_match[$tmp_partyid] = $identical_cnt / $masters_cnt; //$max_match;
				}
			}

	#		print_rr($mgid_match);
	#		exit;
		}

	/*	$views = memcached_simple_hash('party_view','
			SELECT PARTYID, CAST(SUM(HITS) AS UNSIGNED INT) AS PARTY_TOTAL
			FROM party_view
			WHERE PARTYID IN ('.$other_eventsidstr.')
			  AND DAYNUM > TO_DAYS(NOW()) - 60
			GROUP BY PARTYID
			ORDER BY PARTY_TOTAL DESC'
		);

		$most_views = reset($views);*/
		if ($party['LAT']) {
			if (false === ($distances = memcached_simple_hash(['party', 'location', 'boarding', 'city'], /** @lang MariaDB */ "
				/* advice $context */
				SELECT PARTYID, ".
					distance(
						$party['LAT'],
						$party['LON'],
						'COALESCE(boarding.LATITUDE,  location.LATITUDE,  city.LATITUDE)',
						'COALESCE(boarding.LONGITUDE, location.LONGITUDE, city.LONGITUDE)'
					)."
				FROM party
				LEFT JOIN location USING (LOCATIONID)
				LEFT JOIN boarding USING (BOARDINGID)
				LEFT JOIN city ON city.CITYID = COALESCE(boarding.CITYID, location.CITYID, party.CITYID)
				WHERE PARTYID IN ($other_eventsidstr)
				  AND COALESCE(boarding.LATITUDE, location.LATITUDE, city.LATITUDE)",
				ONE_HOUR))
			) {
				return false;
			}
		} else {
			$distances = [];
		}
		assert(is_array($distances)); # Satisfy static analysis

		if (false === ($source_artists = memcached_same_hash('lineup', /** @lang MariaDB */ "
			/* advice $context */
			SELECT DISTINCT ARTISTID
			FROM lineup
			WHERE PARTYID = $partyid"))
		||	false === ($other_artists = memcached_simple_hash('lineup', /** @lang MariaDB */ "
			/* advice $context */
			SELECT PARTYID, GROUP_CONCAT(DISTINCT ARTISTID ORDER BY ARTISTID)
			FROM lineup
			WHERE PARTYID IN ($partyid, $other_eventsidstr)
			GROUP BY PARTYID"))
		) {
			return false;
		}

		$source_artist_cnt = count($source_artists);

		foreach ($other_artists as $tmp_partyid => $artistidstr) {
			$artistids = explode_to_hash(',', $artistidstr);

			$max_match = max(count($artistids), $source_artist_cnt);

			$identicals = array_intersect($source_artists, $artistids);

			$identical_cnt = count($identicals);

			if (!$max_match) {
				mail_log('max_match is zero for partyid '.$tmp_partyid, get_defined_vars());
				$artists_match[$tmp_partyid] = 0;
			} else {
				$artists_match[$tmp_partyid] = $identical_cnt / $max_match;
			}
		}

		#####################3

		$source_gids = memcached_simpler_array(['lineup', 'artist_genre'], /** @lang MariaDB */ '
			/* advice '.$context.' */
			SELECT GID
			FROM lineup
			JOIN artist_genre USING (ARTISTID)
			WHERE PARTYID = '.$partyid.'
			ORDER BY GID'
		);

		$other_artist_genres = memcached_simple_hash(['lineup', 'artist_genre'], /** @lang MariaDB */ '
			/* advice '.$context.' */
			SELECT PARTYID, GROUP_CONCAT(GID ORDER BY GID)
			FROM lineup
			JOIN artist_genre USING (ARTISTID)
			WHERE PARTYID IN ('./*$partyid*/'0'.','.$other_eventsidstr.')
			GROUP BY PARTYID
			ORDER BY PARTYID, GID'
		);

	#	print_rr($source_gids,'source_gids');
	#	print_rr($other_artist_genres,'other_artist_genres');

		$source_gids_cnt = count($source_gids);

		foreach ($other_artist_genres as $tmp_partyid => $gidstr) {
			$gids = explode(',', $gidstr);

#			print_rr($source_gids,'source_gids');
#			print_rr($gids,'gids');

			$max_match = max(count($gids), $source_gids_cnt);

#			print_rr($max_match);

			$identicals = [];

	#		$identicals = array_intersect($source_gids,$gids);
			foreach ($source_gids as /* $i => */ $sgid) {
				foreach ($gids as $j => $gid) {
					if ($sgid === $gid) {
						$identicals[] = $gid;
						unset($gids[$j]);
					}
				}
			}

	#		if ($tmp_partyid == 386603) {
	#			print_rr($identicals,'identicals');
	#			exit;
	#		}

			$identical_cnt = count($identicals);

			# max_match might be 0 for no lineup I think
			$gids_match[$tmp_partyid] = $identical_cnt / ($max_match ?: 0);
		}

	#	print_rr($gids_match);
	#	exit;

		if (false === ($daydiffs = memcached_simple_hash('party', /** @lang MariaDB */ "
			/* advice $context */
			SELECT PARTYID, ABS(CAST(STAMP AS SIGNED) - {$party['STAMP']})
			FROM party
			WHERE PARTYID IN ($other_eventsidstr)",
			ONE_HOUR))
		) {
			return false;
		}
		assert(is_array($daydiffs)); # Satisfy static analysis

	#	print_rr($daydiffs);
	#	print_rr($distances);
		$toplists = [];
		foreach ($other_events as $tmp_partyid => $other_type) {
#			print_rr($tmp_partyid,'other event');
			# group views per x:
	#		static $__group_views = 100;
	#		$use_views = floor( ($views[$tmp_partyid] ?? 0) / $__group_views ) * $__group_views;
	#		$views_worth =
	#			$use_views / $most_views;

			# views_worth : 0 - 1, 1 = most views

	/*		$infos['views'][$tmp_partyid] = [
				'views'		=> $views[$tmp_partyid] ?? 0,
				'use_views'	=> $use_views,
				'worth'		=> $views_worth,
			];*/

			# group distances per x km:
			static $__group_distance = 50;

			if (isset($distances[$tmp_partyid])) {
				$use_distance = floor( $distances[$tmp_partyid] / $__group_distance ) * $__group_distance;

				$distances_worth = 1 / ( 1 + $use_distance / 50 );
			} else {
				$distances_worth = 0;
			}

			# distance_worth : 0 - 1, 1 is neareast,

			/*$infos['distances'][$tmp_partyid] = [
				'distance'		=> $distances[$tmp_partyid] ?? null,
				'use_distance'	=> $use_distance,
				'worth'			=> $distances_worth,
			];*/

			$genres_worth = $mgid_match[$tmp_partyid] ?? 0;

			# genres_worth: 0 - 1, 1 means all master genres match

			$artists_worth = $artists_match[$tmp_partyid] ?? 0;

			# artists_worth: 0 - 1, 1 means all artists match

			static $__group_diffdays = ONE_WEEK / 2;
			# group day diffs per week:
			$use_daydiff = floor( ($daydiffs[$tmp_partyid] ?? 0) / $__group_diffdays) * $__group_diffdays;

			$daydiffs_worth = 1 / (1 +  $use_daydiff / $__group_diffdays);

			# daydiff_worth: 0 - 1, 1 means same week, other further away

	/*		$infos['daydiffs'][$tmp_partyid] = [
				'daydiff'	=> $daydiffs[$tmp_partyid] ?? 0,
				'weekdiff'	=> $daydiffs[$tmp_partyid] / ONE_WEEK,
				'use_daydiff'	=> $use_daydiff,
				'worth'		=> $daydiffs_worth,
			];*/

			# multipliers to give certain types more weight:

			# doubles cannot be used as index (converted to int)

	#		$toplists['artists'][(string)$artists_worth][$tmp_partyid] = $tmp_partyid;

			static $__group_artistgenres = 1/4;

			$artistgenres_worth =
				isset($gids_match[$tmp_partyid])
			?	floor($gids_match[$tmp_partyid]  / $__group_artistgenres) * $__group_artistgenres
			:	0;

			$other_type = explode_to_hash(',',$other_type);
			if (isset($other_type['beach'])) {
				$other_type['outdoor'] = 'outdoor';
				unset($other_type['beach']);
			}
			#$other_type meight be empty (livestreams)

			$max_match = max(count($other_type),$master_type_cnt);
			$identicals = array_intersect($other_type,$master_type);
			$identical_cnt = count($identicals);
			$types_worth = $identical_cnt / ($max_match ?: 1);

#			print_rR($master_type,'master');
#			print_rr($other_type,'other');
#			print_rr($types_worth,'worth');

			foreach ([
				'genres'		=> $genres_worth,
				'distances'		=> $distances_worth,
		#		'views'			=> $views_worth,
				'daydiffs'		=> $daydiffs_worth,
				'artists'		=> $artists_worth,
				'artistgenres'	=> $artistgenres_worth,
				'types'			=> $types_worth,
			] as $type => $worth) {
				$toplists[$type][(string)$worth][$tmp_partyid] = $tmp_partyid;
				$topworths[$type][$tmp_partyid] = ${$type.'_worth'};
			}
		}

	#	print_rR($infos);
	#	exit;

		# if we have matching and nonmatching mastergenres, strip all totally not matching ones

		if (count($toplists['genres']) > 1) {
			unset($toplists['genres'][0]);
		}

	#	print_rr($toplists,'toplists');
	#	print_rr($topworths,'topworths');
		$topindices = [];
		foreach ($toplists as $type => &$worths) {
			$topindices[$type] = [];
			krsort($worths);
			$pos = 0;
			foreach ($worths as /* $worth => */ $tmp_partyids) {
				$topindices[$type] += array_fill_keys($tmp_partyids,$pos);
				++$pos;
			}
		}
		unset($worths);

	#	print_rr($topindices);
	#	exit;

		$types = array_keys($topindices);

		# give certain components more weight:
		static $__multiplier = [
			'genres'		=> 2,
			'distances'		=> 2,
			'types'			=> 4,
			'daydiffs'		=> 2,
	#		'artists'		=> 3,
			'artistgenres'	=> 2,
		];

		$picklist = [];

		foreach ($other_events as $tmp_partyid => $ignored /* $null */) {
			$average_positions[$tmp_partyid] = 0;

			$bad = false;

			foreach ($types as $type) {
				if (!isset($topindices[$type][$tmp_partyid])) {
					$bad = true;
					break;
				}
				if (isset($__multiplier[$type])) {
					$topindices[$type][$tmp_partyid] = $__multiplier[$type] ** $topindices[$type][$tmp_partyid];
				}
				$average_positions[$tmp_partyid] += $topindices[$type][$tmp_partyid];
			}
			if ($bad) {
				unset($average_positions[$tmp_partyid]);
				continue;
			}
			$average_positions[$tmp_partyid] /= 4;
			$picklist[(string)$average_positions[$tmp_partyid]][$tmp_partyid] = $tmp_partyid;
		}

	#	print_rr($topindices['distances']);
	#	print_rr($average_positions[386882]);
	#	print_rr($picklist);
	#	exit;

		asort($average_positions);
		ksort($picklist);

		memcached_set($picklistdonekey,CURRENTSTAMP,ONE_DAY);
		memcached_set($picklistkey,$picklist,ONE_DAY);
	}

	$combined_picklist = [];

	foreach ($picklist as /* $avg_pos => */ $parties) {
		$mainids = [];
		foreach ($parties as $tmp_partyid) {
			if (!($tmp_party = memcached_party_and_stamp($tmp_partyid))
			||	 $tmp_party['CANCELLED']
			||	 $tmp_party['POSTPONED']
			||	!$tmp_party['ACCEPTED']
			||	 $tmp_party['MAIN_ID'] === $main_id
			) {
				continue;
			}
			if (!$nocache) {
				if (false === ($tmp_mstamp = db_single('pagechanged','SELECT MSTAMP FROM pagechanged WHERE ELEMENT="party" AND ID='.$tmp_partyid))) {
					return false;
				}
				if ($tmp_mstamp
				&&	$tmp_mstamp > $done_stamp
				) {
					# event on picklist has changed
					# force cache refresh
					return get_event_advice($party,true,true);
				}
			}
			$mainids[$mainid = $tmp_party['MAIN_ID']] = $mainid;
		}
		$combined_picklist += $mainids;
		if (count($combined_picklist) >= 3) {
			break;
		}
	}
	if ($debug) {
		?><div><b>Picklist</b></div><?
		?><ol><?
		foreach ($combined_picklist as $local_partyid) {
			?><li value="<?= $average_positions[$local_partyid] ?>"><?= get_element_link('party', $local_partyid) ?></li><?
		}
		?></ol><?
		?><table class="fw bordered"><?
		?><tr><?
		?><th class="lpad">event</th><?
		?><th>date</th><?
		?><th class="right">pos</th><?
		foreach ($types as $type) {
			?><th class="center" colspan="2"><?= $type ?></th><?
		}
		?></tr><?

		static $__colors = [
			'#F88',
			'#8F8',
			'#88F',
			'#FF0',
			'#0FF',
			'#F0F',
		];

		foreach ($average_positions as $local_partyid => $average_position) {
			if (!($local_party = memcached_party_and_stamp($local_partyid))) {
				if ($local_party === false) {
					return false;
				}
				not_found('party', $local_partyid);
				continue;
			}
			?><tr><?
			?><td class="lpad"><?= get_element_link('party', $local_partyid) ?></td><?
			?><td class="hpad nowrap"><?
				_date_display($local_party['STAMP'], short: true, time_span: true);
			?></td><?
			?><td class="bold right tt"><? printf('%.2f', $average_position) ?></td><?

			foreach ($types as $i => $type) {
				?><td class="right tt" style="max-width: 3em; color: <?= $__colors[$i] ?>;"><?
				printf('%d', $topindices[$type][$local_partyid] ?? 0) ?><?
				?></td><?
				?><td class="right tt" style="max-width: 3em; color:<?= $__colors[$i] ?>;"><?
				printf('%.2f', $topworths[$type][$local_partyid] ?? 0);
				?></td><?
			}
			?></tr><?
		}
		?></table><?
	}
	return $combined_picklist;
}

function show_event_advice(array $party): void {
	require_once '_uploadimage.inc';

	if (!($combined_picklist = get_event_advice($party))) {
		return;
	}
	$partyid = $party['PARTYID'];

	safe_shuffle($combined_picklist);
	$pickcnt = 0;

	?><aside><?
	?><div class="advice block clear light8"><?

	?><div class="advice-header"><?= __C('party:info:other_interesting_events_LINE') ?></div><?

	?><div style="<?
	if (!SMALL_SCREEN) {
		?>display: flex; <?
		?>justify-content: space-between;<?
	}
	?>"><?

	$upimg_flags = UPIMG_NOCHANGE | UPIMG_NO_OPENGRAPH;

	foreach ($combined_picklist as $local_partyid) {
		if ($new_partyid = get_combined_id('party', $local_partyid)) {
			$local_partyid = $new_partyid;
		}
		if (!($local_party = memcached_party_and_stamp($local_partyid))) {
			if ($local_party === false) {
				return;
			}
			# Maybe deleted
			continue;
		}
		?><div class="advice-box hlbox<?
		if (!SMALL_SCREEN) {
			?> ib<?
		}
		?>"<?
		?> onclick="
			do_inline('POST','/adviceclick.act','RETURN', null, 'PARTYID=<?= $partyid ?>&amp;PARTYID_ADVICE=<?= $local_partyid ?>');
			openLink(event,'/party/<?= $local_partyid ?>', true, true);
		"><?

		?><div class="novents"><?
		?><div class="l rmrgn"><?

		uploadimage_show_with_fallback(
			'party',
			$local_partyid,
			flags: $upimg_flags | UPIMG_PREFER_ORIENTATION,
			size: 'thumb',
			orientation_preference: ['square'],
			max_width: 100
		);
		?></div><?

		?><div class="bold"><?= get_element_link('party', $local_partyid) ?></div><?
		?><div><?
		change_timezone('UTC');
		?><small class="fyel"><?
		_datetime_display($local_party['STAMP_TZI']);
		?></small><?
		change_timezone();
		?></div><?

		if ($local_party['CITYID']) {
			?><div><?
			if ($local_party['LOCATIONID']) {
				echo get_element_link('location', $local_party['LOCATIONID']) ?>, <?
			}
			echo get_element_link('city', $local_party['CITYID']);
			?></div><?
		}

		?></div><?
		?></div><?

		if (++$pickcnt >= 3) {
			break;
		}
	}
	?></div><?
	?></div><?
	?></aside><?

}
