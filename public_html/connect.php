<?php

define('CURRENTSTAMP',	time());
define('TODAYSTAMP',	mktime(0, 0, 0));
define('CACHESTAMP',	CURRENTSTAMP - CURRENTSTAMP % 300);

const force_priority = [
	'video'	=> ['artist' => true]
];

require_once '_exit_if_readonly.inc';
require_once '_require.inc';
require_once '_currentuser.inc';
require_once '_connect.inc';
require_once '_layout.inc';
require_once '_style.inc';
require_once '_error.inc';
require_once '_element.inc';
require_once '_spider.inc';
require_once '_hosts.inc';
require_once '_helper.inc';
require_once '_date.inc';

require_once '_error_handler.inc';
set_my_error_handler();

$_REQUEST['SECTION'] = 'connect';
$_REQUEST['ACTION'] ??= null;
$_REQUEST['CHOOSE'] ??= null;

if (!have_valid_origin()) {
	http_response_code(403);
	exit;
}

$currentuser = new _currentuser();

if (!have_admin()) {
	http_response_code(403);
	exit;
}

if (false === require_number($_REQUEST, 'ID')
||  !require_connectable($_REQUEST, 'ELEMENT')
||	!require_element_access($_REQUEST['ELEMENT'], $_REQUEST['ID'])
) {
	http_response_code(404);
	exit;
}

require_once '_browser.inc';

header('Content-Type: text/html; charset=windows-1252');

?><!DOCTYPE html><?
?><html><?
?><head><?
	include_js_immediate('js/main');
	include_js_immediate('js/form/validation');
	include_style('form');
	?><base target="_top" /><?
	?><link rel="stylesheet" media="all" href="<? show_stylesheet('main') ?>" /><?
	load_fonts();
	include_style(true);
	global $commitresult;
	?><script>
	addreadyevent(function() {
		let interval = null;
		let f = function() {
			if(!parent.Pf_connectFrameLoaded) {
				return false;
			}
			if (interval) {
				clearInterval(interval);
			}
			parent.Pf_connectFrameLoaded(
				<?= isset($_REQUEST['INLINE']) ? 'true' : 'false' ?>,
				'<?= $_REQUEST['ELEMENT'] ?>',
				<?= $_REQUEST['ID'] ?>,
				<?= 	$_REQUEST['ACTION'] === 'form'
					||	$_REQUEST['ACTION'] === 'comment'
					&&	!$commitresult
					?	'true' : 'false' ?>
			);
			return true
		};
		if(!f()) {
			interval = setInterval(f, 500);
		}
	});</script><?
?></head><?

?><body class="forcescroll connectboxbody<?
if (isset($_REQUEST['INLINE'])) {
	?> inline-body<?
}
?>"><?
if (!isset($_REQUEST['INLINE'])) {
	echo get_close_char([
		'style'		=> 'position: absolute; right: 5px; top: 5px',
		'onclick'	=> "parent.Pf_loadconnectbox(null, '{$_REQUEST['ELEMENT']}', {$_REQUEST['ID']})",
	]);
}

if ($_REQUEST['ACTION']) {
	$other_ids = [];

	switch ($_REQUEST['ACTION']) {
	case 'remove':
		if ($other_id = connect_remove()) {
			$other_ids[] = $other_id;
		}
		break;

	case 'commit':
		$other_ids = connect_commit();
		break;
	}

	if ($other_ids) {
			  $connectable = $_REQUEST['ELEMENT'];
		$other_connectable = $_REQUEST['OTHERELEMENT'];

		$id = $_REQUEST['ID'];

		if ($update_organizationids
		=	$connectable === 'party'
		&&	in_array($other_connectable, ['organization', 'orgashost'])
		?	$other_ids
		:	(	in_array($connectable, ['organization',  'orgashost'])
			&&	$other_connectable === 'party'
			?	[$id]
			:	0
			)
		) {
			foreach ($update_organizationids as $update_organizationid) {
				require_once '_memcache.inc';
				partylist_needs_refresh('organization', $update_organizationid);

				require_once '_pixel.inc';
				foreach ((in_array($update_organizationid, $other_ids) ? [$id] : $other_ids) as $partyid) {
					auto_extend_pixel($update_organizationid, $partyid, extend: $_REQUEST['ACTION'] !== 'remove');
				}

				require_once '_genrelist.inc';
				clear_future_event_genres('organization', $update_organizationid);

				require_once '_customer.inc';
				flush_customers();
			}
		}

		if ((	   $connectable === 'video' && $update_videoids =	   [$id])
		||	($other_connectable === 'video' && $update_videoids = $other_ids)
		) {
			require_once '_videometa.inc';
			foreach ($update_videoids as $update_videoid) {
				videometa::flush_titles($update_videoid);
			}
		}
	}
}

display_messages();

switch ($_REQUEST['ACTION']) {
default:
case 'remove':
	connect_overview();
	break;

case 'commit':
	$other_ids ? connect_overview() : connect_form();
	break;

case 'form':
	connect_form();
	break;
}

?></body></html><?

function connect_make_url(?string $other_element = null): string {
	return	'/connect'.
			'?ELEMENT='.$_REQUEST['ELEMENT'].
			';ID='.$_REQUEST['ID'].
			';OTHERELEMENT='.($other_element ?: $_REQUEST['OTHERELEMENT']).
			(isset($_REQUEST['INLINE']) ? ';INLINE' : null);
}

function connect_overview(): void {
	if (false === ($connects = db_multirow_hash('connect', "
		SELECT ASSOCTYPE, ASSOCID
		FROM connect
		WHERE MAINTYPE = '{$_REQUEST['ELEMENT']}'
		  AND MAINID = {$_REQUEST['ID']}"))
	||	false === ($counts = memcached_simple_hash('connect_direction', "
		SELECT ASSOCTYPE, COUNT(*)
		FROM connect_direction
		WHERE MAINTYPE = '{$_REQUEST['ELEMENT']}'
		GROUP BY ASSOCTYPE WITH ROLLUP",
		ONE_DAY
	))) {
		return;
	}

	if ($counts) {
		foreach ($counts as $element => $cnt) {
			$pct = round(100 * $cnt / $counts[null]);
			if ($pct) {
				$prioritylist[$element] = $pct;
			}
		}
	}

	layout_open_table('fw vtop default hhla');
	foreach (get_connectables() as $element => $mapping_or_obsolete) {
		if ($mapping_or_obsolete === false
		&&	!isset($connects[$element])
		) {
			# obsolete, only show if we have connection
			continue;
		}

		ob_start();
		$key = 'element:'.$element;
		$elemname = __C($key = 'element:'.$element);

		?><tr><td class="field"><?

		?><a target="_self" href="<?= connect_make_url($element) ?>;ACTION=form"><?
		echo get_special_char('add', ['label' => $elemname]);
		?></a><?
		layout_field_value();
		$real_element = match ($element) {
			'author'		=> 'user',
			'producer',
			'remixer'		=> 'artist',
			'orgashost',
			'orgaslabel'	=> 'organization',
			'locashost',
			'locasorg'		=> 'location',
			default			=> $element,
		};
		if (isset($connects[$element])) {
			unset($elemlist);
			foreach ($connects[$element] as $id) {
				[$link, $content] = _element_display_link($real_element,$id);
				$elemlist[] = $content.
					' <a target="_self" href="'.connect_make_url($element).';OTHERID='.$id.';ACTION=remove">'.get_remove_char().'</a>';
			}
			echo implode('<br />', $elemlist);
		}
		?></td></tr><?

		$rows[
			isset($prioritylist[$element])
		||	isset(force_priority[$_REQUEST['ELEMENT']][$real_element])
		?	1 : 0
		][(isset($connects[$element]) ? 0 : 1).$elemname] = ob_get_clean();
	}
	krsort($rows);
	foreach ($rows as $priority => $list) {
		ksort($list);
		?><tbody><?
		if (!$priority
		&&	isset($rows[1])
		) {
			?><tr class="nohl separator" style="height: .5em"><td colspan="2"><hr class="slim"></td></tr><?
		}
		echo implode('',$list);
		?></tbody><?
	}
	layout_close_table();
}

function connect_form(): void {
	if (!require_connectable($_REQUEST, 'ELEMENT')
	||	!require_connectable($_REQUEST, 'OTHERELEMENT')
	||	false === require_number($_REQUEST, 'ID')
	||	!require_element_access($_REQUEST['ELEMENT'], $_REQUEST['ID'])
	) {
		return;
	}
	?><form<?
	?> accept-charset="utf-8"<?
	?> target="_self"<?
	?> method="post"<?
	?> onsubmit="return submitForm(this)"<?
	?> action="<?= connect_make_url();
	?>;ACTION=commit"><p><b><?= __C('connect:add_connection_to', ['ELEMENT' => __('element:'.$_REQUEST['OTHERELEMENT'])]);
	?></b></p><?

	layout_open_table('fw hha');
	switch ($_REQUEST['OTHERELEMENT']) {
		case 'party':
			connect_form_option_name();
			connect_form_choose_party();
			break;
		case 'organization':
		case 'location':
		case 'artist':
		case 'city':
		case 'stream':
		case 'videochannel':
		case 'locasorg':
		case 'locashost':
		case 'orgaslabel':
		case 'orgashost':
		case 'relation':
			connect_form_option_name();
			break;

		case 'date':
			connect_form_option_date();
			break;

		case 'monthyear':
			connect_form_option_monthyear();
			break;

		case 'year':
			connect_form_option_year();
			break;

		case 'user':
		case 'author':
		case 'remixer':
		case 'producer':
			connect_form_option_name();
			break;

		case 'review':
		case 'interview':
		case 'news':
		case 'contest':
			connect_form_option_name();
			connect_form_choose('last100');
			break;

		case 'contact_ticket':
		case 'report':
		case 'province':
		case 'topic':
		case 'flock':
		default:
			connect_form_option_id();
			break;
	}
	layout_close_table();
	?><div class="block"><input type="submit" value="<?= __('action:connect'); ?>" /></div><?
	?></form><?
}

function connect_form_option_date(): void {
	layout_start_row();
	?><label for="date"><?= Eelement_name('date'); ?></label><?
	layout_field_value();
	_date_display_select();
	layout_stop_row();
}

function connect_form_option_monthyear(): void {
	[$year, $month] = _getdate();
	layout_restart_row();
		?><label for="month"><?= __C('element:month'); ?></label> &amp; <label for="year"><?= __('element:year'); ?></label><?
		layout_field_value();
		?><select name="MONTH" id="month" ondblclick="if (this.selectedIndex) { this.form.submit(); }"><?
		?><option value="0"></option><?
		for ($mcnt = 1; $mcnt <= 12; ++$mcnt) {
			?><option<?
			if ($mcnt === $month) {
				?> selected="selected"<?
			}
			?> value="<?= $mcnt ?>"><? printf('%02d',$mcnt); ?>. <?= _month_name($mcnt) ?></option><?
		}
		?></select> <select name="YEAR" id="year" ondblclick="if (this.selectedIndex !== -1) { this.form.submit(); }"><?
		for ($ycnt = $year-2; $ycnt <= $year+2; ++$ycnt) {
			?><option<?
			if ($ycnt === $year) {
				?> selected="selected"<?
			}
			?> value="<?= $ycnt ?>"><?= $ycnt ?></option><?
		}
		?></select><?
	layout_stop_row();
}

function connect_form_option_year(): void {
	list($year) = _getdate();
	layout_restart_row();
		?><label for="year"><?= __C('element:year'); ?></label><?
		layout_field_value();
		?><select name="YEAR" id="year" ondblclick="if(this.selectedIndex !== -1) { this.form.submit(); }"><?
		for ($ycnt = $year-2; $ycnt <= $year+2; ++$ycnt) {
			?><option<?
			if ($ycnt === $year) {
				?> selected<?
			}
			?> value="<?= $ycnt ?>"><?= $ycnt ?></option><?
		}
		?></select><?
	layout_stop_row();
}

function connect_form_choose(string $choose): void {
	layout_start_row();
	?><label for="otherid"><?= __C('connect:choose_from'); ?></label><?
	layout_field_value();
	if ($_REQUEST['CHOOSE'] == $choose) {
		$otheridname	 = _element_to_idname   ($_REQUEST['OTHERELEMENT']);
		$othertitlename  = _element_to_titlename($_REQUEST['OTHERELEMENT']);

		$selfid = $_REQUEST['ELEMENT'] == $_REQUEST['OTHERELEMENT'] ? $_REQUEST['ID'] : 0;

		switch ($choose) {
			case 'last100';
				?><select name="OTHERID" id="otherid" ondblclick="if (this.selectedIndex) { this.form.submit(); } "><?
				?><option value="0"></option><?
				$otherelements = db_rowuse_array($_REQUEST['OTHERELEMENT'],/** @lang MariaDB */ '
					SELECT	'.$otheridname.' AS OTHERID,
							'.$othertitlename.' AS TITLE,
							CSTAMP
					FROM '.$_REQUEST['OTHERELEMENT'].'
					ORDER BY '.$otheridname.' DESC
					LIMIT 100'
				);
				if ($otherelements) {
					foreach ($otherelements as $otherelement) {
						if ($selfid
						&&	$selfid === $otherelement['OTHERID']
						) {
							continue;
						}
						?><option value="<?= $otherelement['OTHERID']
						?>"><? _date_display_numeric($otherelement['CSTAMP']);
						?>: <?= escape_specials($otherelement['TITLE'])
						?></option><?
					}
				}
				?></select><?
				break;

		}
	} else {
		switch ($choose) {
			case 'last100':
				?><a target="_self" href="<?= connect_make_url() ?>;ACTION=form;CHOOSE=<?= $choose ?>"><?= __('connect:past_cnt', ['CNT' => 100]) ?></a><?
				break;
		}
	}
	layout_stop_row();
}

function connect_form_choose_party(): void {
	layout_start_row();
	?><label for="otherid"><?= __C('connect:choose_from') ?></label><?
	layout_field_value();
	if ($_REQUEST['CHOOSE'] === 'past') {
		require_once '_partyoptions.inc';
		?><select name="OTHERID" id="otherid" ondblclick="if (this.selectedIndex) { this.form.submit(); }"><?
		?><option value="0"></option><?
		show_party_options(0, false);
		?></select><?
	} else {
		?><a target="_self" href="<?= connect_make_url() ?>;ACTION=form;CHOOSE=past"><?= __('date:past') ?></a><?
	}

	layout_restart_row();
	?><label for="otherid"><?= __C('connect:choose_from'); ?></label><?
	layout_field_value();
	if ($_REQUEST['CHOOSE'] === 'future') {
		require_once '_partyoptions.inc';
		?><select name="OTHERID" id="otherid" ondblclick="if (this.selectedIndex) { this.form.submit(); }"><?
		?><option value="0"></option><?
		show_party_options(0, true);
		?></select><?
	} else {
		?><a target="_self" href="<?= connect_make_url();
		?>;ACTION=form;CHOOSE=future"><?= __('date:future'); ?></a><?
	}
	layout_stop_row();
}

function connect_form_option_id(): void {
	layout_start_row();
	?><label for="manualid"><?= Eelement_name('id(s)'); ?></label><?
	layout_field_value();
	?><input autofocus="autofocus" type="text" id="manualid" name="MANUALID" /><?
	layout_stop_row();
}

function connect_form_option_name(): void {
	$connectable = get_connectables($_REQUEST['OTHERELEMENT']);
	$utf8 = isset(USE_UNICODE[is_string($connectable) ? $connectable : $_REQUEST['OTHERELEMENT']]);

	layout_start_row();
	?><label for="name_or_ids"><?= Eelement_name('name_or_id(s)'); ?></label><?
	layout_field_value();
	global $nameoptions;
	if ($nameoptions) {
		require_once '_connect.inc';
		$have = get_connections($_REQUEST['ELEMENT'], $_REQUEST['ID']);
		$same_element = $_REQUEST['ELEMENT'] === $_REQUEST['OTHERELEMENT'];
		?><select<?
		?> autofocus="autofocus"<?
		?> onkeydown="if(event.code === 'Enter' || event.code === 'NumpadEnter') { this.form.submit(); }"<?
		?> multiple="multiple"<?
		?> class="fw<?
		if ($_REQUEST['OTHERELEMENT'] === 'party') {
			?> connectselect<?
		}
		?>"<?
		?> name="OTHERID[]"<?
		?> id="name_or_ids"<?
		?> size="10"<?
		?> ondblclick="if (this.selectedIndex !== -1) { this.form.submit(); }"<?
		?>><?
		if (isset($nameoptions[0]['ARTISTID'])) {
			require_once '_namedoubles.inc';
			generate_name_doubles($GLOBALS['nameoptions'], 'NAME', 'artist');
			sort_lineup_artists($GLOBALS['nameoptions']);
			foreach ($nameoptions as $opt) {
				$opt['ALLOW'] = true;
				$disabled =
					$same_element
				&&	$opt['ARTISTID'] === $_REQUEST['ID']
				||	isset($have[$_REQUEST['OTHERELEMENT']][$opt['ARTISTID']]);
				$selected_artist = null;
				show_artist_option($opt, $addempty, $selected_artist, 0, $disabled);
			}
		} elseif (isset($nameoptions[0])) {
			foreach ($nameoptions as $opt) {
				?><option<?
				if ($same_element
				&&	$opt['ID'] === $_REQUEST['ID']
				||	isset($have[$_REQUEST['OTHERELEMENT']][$opt['ID']])
				) {
					?> disabled<?
				}
				?> value="<?= $opt['ID'] ?>"><?
				echo escape_specials($opt['NAME'], $utf8);
				show_dead_option($opt);
				if (!empty($opt['PARTS'])) {
					?> (<?= escape_utf8($opt['PARTS']) ?>)<?
				}
				if (isset($city_name[$opt['ID']])) {
					?>, <?
					echo escape_utf8($city_name[$opt['ID']]);
				}
				?></option><?
			}
		} else {
			foreach ($nameoptions as $label => $opts) {
				?><optgroup label="<?= escape_specials($label, true) ?>"><?
				foreach ($opts as $opt) {
					?><option<?
					if ($same_element
					&&	$opt['ID'] === $_REQUEST['ID']
					||	isset($have[$_REQUEST['OTHERELEMENT']][$opt['ID']])
					) {
						?> disabled<?
					}
					?> value="<?= $opt['ID'] ?>"><?= escape_specials($opt['NAME'], !empty($opt['UTF8']));
					?></option><?
				}
				?></optgroup><?
			}
		}
		?></select><?
	} else {
		?><input<?
		if (!empty($_POST['NAME'])) {
			?> value="<?= escape_specials($_POST['NAME'], utf8: $utf8) ?>"<?
		}
		?> type="text"<?
		?> id="name_or_ids"<?
		?> name="NAME"<?
		?> autofocus="autofocus"><?
	}
}

function connect_commit(): array|false {
	if (false === require_number($_REQUEST, 'ID')
	||	!require_connectable($_REQUEST, 'ELEMENT')
	||	!require_connectable($_REQUEST, 'OTHERELEMENT')
	||	!require_element_access($_REQUEST['ELEMENT'], $_REQUEST['ID'])
	) {
		return false;
	}
	if ($_REQUEST['ELEMENT'] === 'video'
	&&	$_REQUEST['ELEMENT'] === $_REQUEST['OTHERELEMENT']
	) {
		# this borks due to get_flat_title getting into infinite loop
		return false;
	}
	$connectable = get_connectables($_REQUEST['OTHERELEMENT']);
	$utf8 = isset(USE_UNICODE[is_string($connectable) ? $connectable : $_REQUEST['OTHERELEMENT']]);
	if (!$utf8
	&&	!empty($_POST['NAME'])
	) {
		$_POST['NAME'] = utf8_to_win1252($_POST['NAME']);
	}
	$other = $_REQUEST['OTHERELEMENT'];
	$table = match ($other) {
		'remixer',
		'producer'	=> 'artist',
		'author'	=> 'user',
		default		=> $other,
	};
	if (!empty($_POST['NAME'])) {
		$_POST['NAME'] = utf8_mytrim($_POST['NAME'], ' []()!.,@#$%^&*<>;\'"');

		# strip left-over sepearators, ampersand is already stripp above, we do it superfluous here:

		$match_separators = '(?:x|f(?:ea)?t(?:uring)?\.?|b[23]b|with|w/|v(?:ersu)?s\.?)';

		$_POST['NAME'] = preg_replace('"(^'.$match_separators.'\h+|\h+'.$match_separators.')"iu', '', $_POST['NAME']);

		$otherids = have_number_list($_POST, 'NAME', '[,\s]+') ?: [];

		switch ($table) {
		case 'party':
			$year = 0;
			if (preg_match('"^(?<prefix>.*?)\b(?<year>20\d\d)\b(?<postfix>.*?)$"u', $_POST['NAME'], $match)) {
				$year = (int)$match['year'];
				$_POST['NAME'] = $match['prefix'].$match['postfix'];
			}
			if (false === ($parties = db_rowuse_array(['party', 'location', 'boarding', 'city'], '
				SELECT PARTYID AS ID, party.NAME, STAMP, STAMP_TZI, SUBTITLE, MOVEDID, CANCELLED, location.NAME AS LOCATION_NAME, city.NAME AS CITY_NAME, EDITION, AT2400
				FROM party
				LEFT JOIN location USING (LOCATIONID)
				LEFT JOIN boarding USING (BOARDINGID)
				LEFT JOIN city ON city.CITYID = COALESCE(boarding.CITYID, location.CITYID, party.CITYID)
				WHERE (	party.NAME LIKE "%'.($likepart = addslashes(str_replace(' ','%', $_POST['NAME']))).'%"
				   OR CONCAT(party.NAME," ",IF(party.EDITION,CONCAT("#",party.EDITION," "),""), party.SUBTITLE) LIKE "%'.$likepart.'%"
				 )
				'.($year ? ' AND STAMP BETWEEN UNIX_TIMESTAMP("'.$year.'-01-01") AND UNIX_TIMESTAMP("'.($year + 1).'-01-01")-1' : '').'
				ORDER BY STAMP DESC'))
			) {
				return false;
			}
			if (!$parties) {
				if (!$otherids) {
					register_error('connect:error:element_not_found_LINE', ['ELEMENT' => __('element:party', DONT_ESCAPE)]);
					return false;
				}
				break;
			}
			if (isset($parties[1])) {
				foreach ($parties as $party) {
					$party['NAME'] = _date_get_numeric($party['STAMP'] - ($party['AT2400'] ? 1 : 0)).': '.$party['NAME'];
					if ($party['EDITION']) {
						$party['NAME'] .= ' #'.$party['EDITION'];
					}
					if ($party['SUBTITLE']) {
						$party['NAME'] .= ' '.MIDDLE_DOT.' '.$party['SUBTITLE'];
					}
					$party['NAME'] .= ', '.($party['LOCATION_NAME'] ?? '').', '.$party['CITY_NAME'];
					if ($party['CANCELLED']) {
						$party['NAME'] .= ' ('.win1252_to_utf8(__('status:cancelled',DONT_ESCAPE)).')';
					}
					if ($party['MOVEDID']) {
						$party['NAME'] .= ' ('.win1252_to_utf8(__('status:moved',DONT_ESCAPE)).')';
					}
					# [$y] = _getdate($party['STAMP_TZI'],'UTC');
					$label = win1252_to_utf8(__($party['STAMP'] < CURRENTSTAMP ? 'date:past' : 'date:future',DONT_ESCAPE));//.': '.$y;
					$party['UTF8'] = true;

					$GLOBALS['nameoptions'][$label][] = $party;
				}
				return false;
			}
			$party = $parties[0];
			$otherids[] = $party['ID'];
			break;

		case 'artist':
			require_once '_artist.inc';
			if (false === ($elements = get_artist_choices(0, $_POST['NAME']))) {
				return false;
			}
			if (!$elements) {
				if (!$otherids) {
					register_error('connect:error:element_not_found_LINE', ['ELEMENT' => __("element:$other", DONT_ESCAPE)]);
					return false;
				}
				break;
			}
			if (count($elements) > 1) {
				foreach ($elements as $elem) {
					$GLOBALS['nameoptions'][] = $elem;
				}
				return false;
			}
			[$otherid, $elem] = keyval($elements);
			if ($elem['DST'] < 70) {
				# force select because name match is bad
				$GLOBALS['nameoptions'][] = $elem;
				return false;
			}
			break;
		case 'news':
			$namename = 'TITLE';
		case 'orgashost':
		case 'orgaslabel':
		case 'locasorg':
		case 'locashost':
			switch ($table) {
			case 'orgashost':
			case 'orgaslabel':
				$table = 'organization';
				break;
			case 'locasorg':
			case 'locashost':
				$table = 'location';
				break;
			}
		case 'relation':
		case 'organization':
		case 'city':
		case 'location':
		case 'user':
		case 'contest':
		case 'stream':
			if (empty($namename)) {
				$namename = 'NAME';
			}
			$tables = [$table];
			$order = null;
			switch ($other) {
			case 'location':
			case 'locasorg':
			case 'locashost':
				$selectextra = '
					city.CITYID, city.COUNTRYID,
					(SELECT MAX(STAMP) FROM party WHERE party.LOCATIONID = elem.LOCATIONID) AS LSTAMP,
					FOLLOWUPID,
					DEAD
				';
				$joinextra = 'LEFT JOIN city USING (CITYID)';
				$tables[] = 'city';
				$tables[] = 'party';
				break;
			case
			'orgashost':
			case 'organization':
				$selectextra = '
					city.CITYID, COALESCE(city.COUNTRYID, elem.COUNTRYID) AS COUNTRYID,
					(SELECT MAX(STAMP) FROM connect JOIN party ON ASSOCID=PARTYID WHERE MAINTYPE="organization" AND MAINID=elem.ORGANIZATIONID AND ASSOCTYPE="party") AS LSTAMP,
					FOLLOWUPID,
					DEADSTAMP';
				$joinextra = '
					LEFT JOIN city USING (CITYID)
					LEFT JOIN country ON country.COUNTRYID = COALESCE(city.COUNTRYID, elem.COUNTRYID)
				';
				$tables = [...$tables, 'city', 'country','party','connect'];
				break;

			case 'author':
			case 'user':
				$namename = 'NICK';
				break;
			case 'news':
			case 'contest':
			case 'review':
			case 'interview':
				$selectextra = 'PSTAMP';
				$order = 'PSTAMP DESC';
				break;
			}
			if (!($elements = db_rowuse_array($tables, "
				SELECT	{$table}ID AS ID, elem.$namename AS NAME
						".(isset($selectextra) ? ", $selectextra" : '')."
				FROM $table AS elem
					 ".(isset($joinextra) ? $joinextra : '')."
				WHERE elem.$namename LIKE '%".addslashes(str_replace(' ', '%', $_POST['NAME']))."%'".
				($otherids ? " OR {$table}ID IN (".implode(', ', $otherids).') ' : '')."
				GROUP BY {$table}ID
				ORDER BY elem.".($order ?: $namename)))
			) {
				if ($elements !== false) {
					register_error('connect:error:element_not_found_LINE', ['ELEMENT' => __("element:$other", DONT_ESCAPE)]);
				}
				return false;
			}
			if (isset($elements[1])) {
				foreach ($elements as $elem) {
					$parts = [];
					if ($otherids && in_array($elem['ID'], $otherids, true)) {
						$parts[] = "ID: {$elem['ID']}";
					}
					if (!empty($elem['CITYID'])) {
						$parts[] = get_element_title('city', $elem['CITYID']);
					}
					if (!empty($elem['COUNTRYID'])) {
						$parts[] = win1252_to_utf8(__('country:'.$elem['COUNTRYID']));
					}
					if (!empty($elem['GENRES'])) {
						$parts[] = $elem['GENRES'];
					}
					if (!empty($elem['LSTAMP'])) {
						$parts[] = element_name('last_event').': '._date_get($elem['LSTAMP']);
					}
					if ($parts) {
						$elem['PARTS'] = implode(', ',$parts);
					}
					if (!empty($elem['PSTAMP'])) {
						$elem['NAME'] = _date_get_numeric($elem['PSTAMP']).': '.$elem['NAME'];
					}
					$GLOBALS['nameoptions'][] = $elem;
				}
				return false;
			}
			$elem = $elements[0];
			$otherids = [$elem['ID']];
			break;
		}
	} else {
		switch ($other) {
		case 'date':
			if (!require_date($_POST)) {
				return false;
			}
			$otherid = _date_getstamp($_POST);
			break;
		case 'year':
			if (false === require_number($_POST, 'YEAR')) {
				return false;
			}
			$otherid = $_POST['YEAR'];
			break;
		case 'monthyear':
			if (false === require_number($_POST, 'MONTH')
			||	false === require_number($_POST, 'YEAR')
			) {
				return false;
			}
			$otherid = mktime(0,0,0,$_POST['MONTH'],1,$_POST['YEAR']);
			break;

		default:
			if (empty($_POST['MANUALID'])) {
				if (!have_number($_POST, 'OTHERID')
				&&	!require_number_array($_POST, 'OTHERID')
				) {
					return false;
				}
				$otherids = $_POST['OTHERID'];
			} else {
				if (!($otherids = require_number_list($_POST, 'MANUALID', '[,\s]'))) {
					return false;
				}
			}
			break;
		}
	}
	if (!empty($otherid)) {
		$otherids[] = $otherid;
	}
	if (!$otherids) {
		return false;
	}
	if (!is_array($otherids)) {
		$otherids = [$otherid];
	}

	foreach ($otherids as $id) {
		if ($_REQUEST['ELEMENT'] === $other
		&&	$_REQUEST['ID']		 === $id
		) {
			register_error('connect:error:cannot_connect_to_self_LINE');
			continue;
		}
		if ($other === 'organization'
		||	$other === 'location'
		) {
			switch ($_REQUEST['ELEMENT']) {
			case 'relation':
				# relations should only get concepts
				connect_concepts($_REQUEST['ELEMENT'],$_REQUEST['ID'],$other,$id);
				break;
			default:
				connect_family($_REQUEST['ELEMENT'], $_REQUEST['ID'], $other, $id);
				break;
			}

			if ($_REQUEST['ELEMENT'] === 'party') {
				# ignore succesors if predecessor is connected:

				if (false === ($currents = db_simple_hash($other, "
					SELECT {$other}ID,FOLLOWUPID
					FROM `$other`
					JOIN connect ON {$other}ID = ASSOCID
					WHERE MAINTYPE = '{$_REQUEST['ELEMENT']}'
					  AND MAINID = {$_REQUEST['ID']}
					  AND ASSOCTYPE = '$other'",
					DB_USE_MASTER
				))) {
					return false;
				}
				$discontinue = false;
				foreach ($currents as $tmp_id => $followup_id) {
					if ($followup_id == $id) {
						register_warning('connect:warning:predecessor_already_exists,successor_ignored_LINE',DO_UBB,[
							'ELEMENT	'	=> $other,
							'ID'			=> $tmp_id,
							'FOLLOWUPID'	=> $followup_id
						]);
						$discontinue = true;
					}
				}

				if ($discontinue) {
					continue;
				}

				# ignore predecessors if successor is connected:

				if (false === ($otherinfo = db_single($other, "
					SELECT FOLLOWUPID
					FROM `$other`
					WHERE {$other}ID = $id",
					DB_USE_MASTER
				))) {
					return false;
				}
				if ($otherinfo === null) {
					register_error($other.':error:nonexistent_LINE', ['ID' => $id]);
					return false;
				}
				$followup_id = $otherinfo;
				if (isset($currents[$followup_id])) {
					register_warning('connect:warning:followup_already_connected_LINE', DO_UBB, [
						'ELEMENT'		=> $other,
						'ID'			=> $id,
						'FOLLOWUPID'	=> $followup_id
					]);
					continue;
				}
			}
		}
		if (!create_connect($_REQUEST['ELEMENT'], $_REQUEST['ID'], $other, $id)) {
			return false;
		}
	}
	return $otherids;
}

function connect_remove(
	?string	$connectable		= null,
	?int 	$id					= null,
	?string	$other_connectable	= null,
	?int	$other_id			= null
): int|bool {
	if ($connectable === null) {
		if (false === require_number($_REQUEST, 'ID')
		||	false === require_number($_REQUEST, 'OTHERID')
		||	!($connectable = require_connectable($_REQUEST, 'ELEMENT'))
		||	!($other_connectable = require_connectable($_REQUEST, 'OTHERELEMENT'))
		) {
			return false;
		}
		$id = $_REQUEST['ID'];
		$other_id = $_REQUEST['OTHERID'];
	}

	$element = get_element_from_connectable($connectable);
	$other_element = $other_connectable ? get_element_from_connectable($other_connectable) : null;

	$where = "
		WHERE MAINTYPE	= '$connectable'
		  AND MAINID	=  $id
		  AND ASSOCTYPE	= '$other_connectable'
		  AND ASSOCID	=  $other_id
		  
		   OR ASSOCTYPE	= '$connectable'
		  AND ASSOCID	=  $id
		  AND MAINTYPE	= '$other_connectable'
		  AND MAINID	=  $other_id";

	if (!require_element_access($element, $id)
	||	!db_insert('connect_log','
		INSERT INTO connect_log
		SELECT *,'.CURRENTSTAMP.','.CURRENTUSERID.'
		FROM connect'.
		$where)
	||	!db_delete('connect','
		DELETE FROM connect'.
		$where)
	) {
		return false;
	}
	if (db_affected()) {
		require_once '_pagechanged.inc';
		page_changed($element, $id, $other_element, $other_id);
		require_once '_sphinx.inc';
		sphinx_update_element($element, $id);
		sphinx_update_element($other_element, $other_id);
	}
	return $other_id;
}
