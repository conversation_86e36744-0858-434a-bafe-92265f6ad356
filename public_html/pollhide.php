<?php

define('CURRENTSTAMP',time());
require_once '_exit_if_readonly.inc';
require_once '_nocache.inc';
require_once '_require.inc';
require_once '_identity.inc';
require_once '_pollhide.inc';

send_no_cache_headers();

header('Content-Type: text/html; charset=windows-1252');

function bail($errno) {
	http_response_code($errno);
	display_messages_only();
	exit;
}
if (!require_post()
||	!require_idnumber($_SERVER,'ePOLLID')
) {
	bail(400);
}
if (!require_valid_origin()
||	!require_identity()
) {
	bail(403);
}
bail(hide_poll($_SERVER['ePOLLID']) ? 202 : 500);
