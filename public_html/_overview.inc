<?php

class overview {
	public function display() {
		if (empty($this->comments)) {
			return;
		}
		$active_desc = null;
		switch ($this::class) {
		case 'messageoverview':
			$messages = true;
			foreach ($this->counts as $id => $cnt) {
				$counts[strtolower($desc = $id
				?	__('overview:select:messages_in', DO_UBBFLAT, ['ELEMENT' => $this->parent, 'ID' => $id])
				:	element_plural_name('all_message')
				)] = [$id, $cnt, $desc];

				if ($active_desc === null && (!$this->selected || $this->selected === $id)) {
					$active_desc = $desc;
					$active_count = $cnt;
				}
			}
			break;

		case 'commentoverview':
			$comments = true;
			$allcounts = isset($this->oldcounts) ? $this->oldcounts : $this->counts;
			$active_desc = null;
			foreach ($allcounts as $element => $cnt) {
				$counts[$desc = element_plural_name($element ? $element.'_comment' : 'all_comment')] = array($element,$cnt);
				if ($active_desc === null && (!$this->selected || $this->selected === $element)) {
					$active_desc = $desc;
					$active_count = $cnt;
				}
			}
			$hide_selection = $this->flags & commentoverview::HIDE_SELECTION;
			break;

		case 'quoteoverview':
			$all_name = 'all_quote';
			$quotes = true;

		case 'savedoverview':
			if (!isset($all_name)) {
				$all_name = 'all_saved_element';
			}
			foreach ($this->counts as $element => $cnt) {
				$counts[element_plural_name($element ?: $all_name)] = array($element,$cnt);
			}
			break;
		}
		ksort($counts);

		ob_start();
		if (empty($hide_selection)
		&&	(	count($counts) > 1
			||	isset($this->newquotes)
			)
		) {
			?><select onchange="<?
				?>this.disabled = true;<?
				?>openLink(event, '/user/<?= $this->userid ?>/<?= $_REQUEST['ACTION'] ?>' + (this.value ? '/' + this.value : ''));<?
			?>"><? 
			if (isset($quotes)) {
				[, $seenstamp] = get_new_quotes($this->userid);
				if (isset($this->newquotes)) {
					?><option disabled="disabled"<?
					if ($this->newquotes) {
						?> class="hidden"<?
					}
					?>><?
					echo element_plural_name('no_new_quote');
					?></option><?
					if ($this->newquotes) {
						?><option id="newqotopt" value="new"><?
						echo element_plural_name('new_quote');
						if (!empty($seenstamp)) {
							?> <? echo __('date:since_date',array('DATETIME'=>_datetime_get($seenstamp)));
						}
						?> (<?= $this->newquotes ?>)<?
						?></option><?
					}
				}
			}
			foreach ($counts as $desc => $info) {
				if (isset($comments)) {
					[$element, $cnt] = $info;
					?><option<?
					if ($this->selected
					&&	$this->selected === $element
					) {
						?> selected<?
					}
					if (!isset($this->counts[$element])) {
						?> disabled<?
					}
					?> value="<?= $element ?>"><?= $desc ?> (<?= $cnt ?>)</option><?
				} elseif (isset($messages)) {
					[$id, $cnt, $desc] = $info;
					if ($id
					&&	isset($this->visible)
					&&	!isset($this->visible[$id])
					) {
						continue;
					}
					?><option<?
					if ($this->selected
					&&	$this->selected === $id
					) {
						?> selected<?
					}
					if ($id
					&&	$this->parent === 'forum'
					&&	$this->forums
					&&	!isset($this->forums[$id])
					) {
						?> disabled<?
					}
					?> value="<?= $id ?>"><?= $desc ?> (<?= $cnt ?>)</option><?
				} else {
					[$element, $cnt] = $info;
					?><option<?
					if ($this->selected === $element) {
						?> selected<?
					}
					?> value="<?= $element ?>"><?= $desc ?> (<?= $cnt ?>)</option><?
				}
			}
			?></select><?
		}
		$selector = ob_get_clean();

		$img = get_user_image($this->userid);

		layout_open_box();
		layout_box_header(get_element_link('user',$this->userid));
		if ($img) {
			?><div class="abs" style="top:.5em;right:0"><?= _profileimage_get($this->userid) ?></div><?
			?><div class="r" style="height:<?= $img['HEIGHT'] ?>px;margin-top:-1em"></div><?
		}
		?><div class="block"><?
		echo $selector;
		?></div><?
		if ($img) {
			?><div class="clear"></div><?
		}
		layout_close_box();

		global $__refresh_key;
		?><section><?
		?><div id="<?= $this::class ?>sect" class="section" data-refreshkey="<?= $__refresh_key ?>"><?

		$this->controls->display_and_store();

		?><div class="wthumbs"><?
		$this->display_comments();
		?></div><?

		$this->controls->display_stored();

		?></div><?
		?></section><?
	}
	protected function init_karma(string $element, in|array $id_or_ids): void {
		require_once '_karma.inc';
		karma($id_or_ids, $element);
	}
	protected function display_comments(): void {
		require_once '_commentobject.inc';

		$flags = CMTFLG_SHOW_PARENT | CMTFLG_SHOW_GRANDPARENT;
		$cls = $this::class;

		switch ($cls) {
		case 'quoteoverview':
			if ($_REQUEST['SUBACTION'] === 'new') {
				include_js('js/quoteseen');
				$flags |= CMTFLG_ALLOW_SEEN;
			}
			break;
		case 'savedoverview':
			$flags |= CMTFLG_IN_SAVED_OVERVIEW | CMTFLG_FORCE_SHOW;
			break;
		}

		foreach ($this->comments as $comment) {
			if (isset($comment['NOCOMMENT'])) {
				require_once '_element.inc';
				layout_open_box('white nmsg');
				layout_open_box_header();
				?><div  class="r"><?
				show_commentkeep_links($comment['ELEMENT'],$comment['ID'],true);
				?></div><?
				?><div><?
				echo get_element_link($comment['ELEMENT'],$comment['ID']);
				if ($comment['ELEMENT'] === 'party') {
					if ($party = memcached_party_and_stamp($comment['ID'])) {
						if ($party['SUBTITLE']) {
							?><small> <?= MIDDLE_DOT_ENTITY ?> <?
							echo escape_utf8($party['SUBTITLE']);
							?></small><?
						}
					}
				}
				?></div><?
				?><div class="nb small"><?= element_name($comment['ELEMENT']) ?></div><?
				layout_close_box_header();

				ob_start();
				switch ($comment['ELEMENT']) {
				case 'party':
					if ($party) {
						dateday_display_link_tzi($party['STAMP_TZI']);
						?> <?
						_time_display($party['STAMP']);
						if ($party['LOCATIONID'] || $party['CITYID']) {
							?><br /><?
							if ($party['LOCATIONID']) {
								echo get_element_link('location',$party['LOCATIONID']);
								if ($party['CITYID']) {
									?>, <?
								}
							}
							if ($party['CITYID']) {
								echo get_element_link('city',$party['CITYID']);
							}
						}
					}
					break;
				}
				if ($data = ob_get_clean()) {
					?><div class="block"><?
					echo $data;
					?></div><?
				}
				layout_close_box();
			} else {
				comment_display($comment['ELEMENT'],null,$comment,$flags);
			}
		}
	}
}
