<?php

require_once '_album.inc';

// FIXME: there is one similar in album.inc ...

function albumelement_show_single(
	array	$albumelement,
	?array	$commentsinfo	= null,
	bool	$markstuff		= false,
	bool	$marked			= false,
	?bool	$showsource		= false,
): void {
	$id = $albumelement['ALBUMELEMENTID'];
	if ($showsource) {
		# show in whose album every individual image resides
/*		?><table style="display:inline-block" class="split"><tr><td><?*/
		?><div class="photo-box"><?
	}
	?><div<?
	if ($markstuff
	&&	have_admin('album')
	) {
		?> id="ae<?= $id ?>"<?
	}
	?> class="<?=
		$marked
	?	'hid '
	:	(	empty($albumelement['ACCEPTED'])
		?	'nok '
		:	null
		) ?>photo-box" style="max-width: <?= $albumelement['WIDTH'] + 4 ?>px;"><?

	if (!ROBOT
	&&	$commentsinfo
	&&	!$commentsinfo[CMTI_HIDEALL]
	&&	($cnt = $commentsinfo[CMTI_OKMSGCNT])
	) {
		show_comment_counter($cnt);
	}
	?><a<?
	if ($markstuff
	&&	have_admin('album')
	) {
		?> onclick="return markPhoto(<?= $id ?>)"<?
	}
	?> href="<?= get_element_href('albumelement',$id) ?>"><?
	show_albumthumb($albumelement);
	?></a></div><?
	if ($showsource) {
		?><div class="small light"><?= get_element_link('user',$albumelement['ALBUMID']) ?></div><?
		?></div><?
	}
}

function get_albumelement(int $albumelementid): array|false|null {
	return memcached_single_assoc(['albumelement','albumimagedatameta','albummap'], '
		SELECT	ALBUMELEMENTID,ae.ALBUMID,ae.ALBUMID AS USERID,ae.BODY,ae.SHOWBODY,ae.TITLE,ae.ACCEPTED,ae.ORDERID,ae.DATAID,ORIGNAME,THMBID,ae.CSTAMP,
				ae.VISIBILITY_ELEMENT,ae.VISIBILITY_COMMENTS,ae.VISIBILITY_WRITECOMMENTS, ae.FLAGS,
				ae.COLOR,ae.ALIGN,ae.PARTYID,ae.PARTYLINKTOP,
				WIDTH,HEIGHT,FILETYPE,
				ae.MAPID,UPMAPID,albummap.TITLE AS MAPTITLE,
				NOT ISNULL(albumimagedatabusy.DATAID) AS BUSY
		FROM albumelement AS ae
		LEFT JOIN albumimagedatameta USING (DATAID)
		LEFT JOIN albummap USING (MAPID,ALBUMID)
		LEFT JOIN albumimagedatabusy USING (DATAID)
		WHERE ALBUMELEMENTID = '.$albumelementid,
		HALF_HOUR,
		get_albumelement_key($albumelementid)
	);
}

const ALBUMFLG_LINK		= 1;
const ALBUMFLG_NOCACHE	= 2;

function show_albumthumb(int|array $albumimage, int $flags = 0): void {
	if (is_int($albumimage)) {
		if (!($albumimage = memcached_single_assoc('albumelement','
			SELECT	WIDTH, HEIGHT, FILETYPE, ACCEPTED, CRC, THMBID,
					(busy.DATAID IS NOT NULL) AS BUSY
			FROM albumelement
			JOIN albumimagedatameta AS meta ON meta.DATAID = THMBID
			LEFT JOIN albumimagedatabusy AS busy ON busy.DATAID = THMBID
			WHERE ALBUMELEMENTID = '.($albumelementid = $albumimage),
			TEN_MINUTES,
			get_albumthumb_key($albumelementid)))
		) {
			return;
		}
	} else {
		$albumelementid = $albumimage['ALBUMELEMENTID'];
	}

	?><div class="ib relative"><?

	if ($flags & ALBUMFLG_LINK) {
		?><a href="<?= get_element_href('albumelement', $albumelementid) ?>"><?
	}

	//$busy = show_busy_albumelement($albumimage, 'thumb');

	?><img<?
	?> loading="lazy"<?
	?> style="width: <?= $albumimage['WIDTH'] ?>px;"<?
	?> class="mw100"<?
	?> id="aimg<?= $albumelementid ?>"<?
	if (!empty($albumimage['TITLE'])) {
		?> alt="<?= $title = escape_utf8($albumimage['TITLE']) ?>" <?
		?> title="<?= $title ?>" <?
	}
	if (!$albumimage['ACCEPTED']) {
		?> class="unaccepted-element"<?
	}
	if (!isset($albumimage['THMBID'])) {
		static $__done = false;
		if (!$__done) {
			error_log_r(debug_backtrace(),'no dataid');
			$__done = true;
		}
	}
	?> width="<?= $albumimage['WIDTH'] ?>"<?
	?> src="<?= ALBUM_HOST ?>/<?= $albumelementid ?>t_<?= crc32($albumimage['THMBID'].':'.$albumelementid) ?>.<?= $albumimage['FILETYPE'] ?>" /><?
	if ($flags & ALBUMFLG_LINK) {
		?></a><?
	}
	?></div><?
}

function show_busy_albumelement(array $albumelement, string $type): bool {
	if (empty($albumelement['BUSY'])
	||	empty($albumelement['ALBUMELEMENTID'])
	) {
		return false;
	}
	include_style('spinners');
	include_js('js/refreshalbumimgs');

	?><div<?
	?> class="centerspin refreshaimg"<?
	?> data-type="<?= $type ?>"<?
	?> data-id="<?= $albumelement['ALBUMELEMENTID'] ?>"<?
	?>><?
	?><div class="barcontainer"><?
	?><div class="circleloader"><?
	?><div class="bounce"></div><?
	?></div><?
	?></div><?
	?></div><?
	return true;
}

function albumelement_readies(): int {
	require_once '_currentuser.inc';
	if (!($ids   = have_number_list($_POST, 'IDSTR'))
	||	!($types = have_something  ($_POST, 'TYPESTR') ? explode(',',$_POST['TYPESTR']) : false)
	||	count($ids) !== count($types)
	) {
		return http_status::BAD_REQUEST->value;
	}
	global $currentuser;
	$currentuser = new _currentuser(TINY_USER | SKIP_SETTINGS);
	if (!require_user()) {
		return http_status::UNAUTHORIZED->value;
	}
	if (!($stuffs = db_rowuse_hash(['albumelement', 'albumimagedatabusy', 'albumimagedataupdated'], "
		SELECT	albumelement.ALBUMELEMENTID,
				aodi.ALBUMELEMENTID AS SECONDID,
				DATAID, THMBID,
				IF (albumimagedatabusy.DATAID, b'1', b'0') AS BUSY,
				aodi.NEWID AS DONE
		FROM albumelement
		LEFT JOIN albumimagedatabusy USING (DATAID)
		LEFT JOIN albumimagedataupdated aodi ON aodi.NEWID = albumelement.DATAID
		WHERE ALBUMID = ".CURRENTUSERID."
		  AND albumelement.ALBUMELEMENTID IN ({$_POST['IDSTR']})"))
	) {
		return ($stuffs === false ? http_status::SERVICE_UNAVAILABLE : http_status::NOT_FOUND)->value;
	}
	assert(isset($types) && is_array($types)); # Satisfy EA inspection
	$dataids = [];
	foreach ($ids as $i => $albumelementid) {
		$type = $types[$i];
		if (!($dataid = $stuffs[$albumelementid][$type === 'normal' ? 'DATAID' : 'THMBID'] ?? null)) {
			continue;
		}
		$dataids[] = $dataid;
	}
	if (empty($dataids)) {
		return http_status::NOT_FOUND->value;
	}
	$dataidstr = implode(',', $dataids);
	if (false === ($states = db_simple_hash('albumimagedatabusy', "
		SELECT DATAID, 0 AS DONE
		FROM albumimagedatabusy
		WHERE DATAID IN ($dataidstr)
		UNION
		SELECT NEWID, 1 AS DONE
		FROM albumimagedataupdated
		WHERE NEWID IN ($dataidstr)"))
	) {
		return http_status::SERVICE_UNAVAILABLE->value;
	}
	assert(is_array($states)); # Satisfy EA inspection
	$result = [];
	foreach ($ids as $i => $albumelementid) {
		if (!($dataid = $dataids[$i] ?? null)) {
			mail_log('no dataid found for number '.$i, get_defined_vars());
			continue;
		}
		if (null === ($done_state =  $states[$dataid] ?? null)) {
			# should this (not) happen?
			# mail_log('no state found for dataid '.$dataid, get_defined_vars());
			continue;
		}
		$result[$albumelementid] = !$done_state ? 'busy' : crc32($dataid.':'.$albumelementid);
	}
	header('Content-Type: application/json');
	echo safe_json_encode($result);
	return http_status::OK->value;
}
