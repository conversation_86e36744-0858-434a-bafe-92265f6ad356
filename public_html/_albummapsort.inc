<?php

function albummap_stamp_from_title(string $title): ?int {
	if (!preg_match('"(?:(\d{4,})\-(\d{1,2})\-(\d{1,2})|(\d{1,2})\-(\d{1,2})\-(\d{4,}))"',$title,$matches)) {
		return null;
	}
	if (!empty($matches[1])) {
		list(,$year,$month,$day) = $matches;
	} else {
		list(,,,,$day,$month,$year) = $matches;
	}
	return mktime(0,0,0,$month,$day,$year);
}
function sort_albummaps(array &$maps,array $stamps,bool $reverse = false): void {
	$reverse = $reverse ? -1 : 1;

	foreach ($maps as &$map) {
		if (isset($stamps[$map['PARTYID']])) {
			continue;
		}
		$stamps[$map['PARTYID']] = albummap_stamp_from_title($map['TITLE']) ?? 0;
	}
	unset($map);

	uasort($maps, function($a, $b) use ($stamps, $reverse) {
		$astamp = $stamps[$a['PARTYID']];
		$bstamp = $stamps[$b['PARTYID']];
		if ($astamp) {
			if ($bstamp) {
				return $reverse * ($astamp - $bstamp);
			}
			return 1;
		} elseif ($bstamp) {
			return -1;
		}
		return strcasecmp($a['TITLE'],$b['TITLE']);
	});
}
function reverse_sort_albummaps(&$maps, $stamps) {
	return sort_albummaps($maps, $stamps);
}
