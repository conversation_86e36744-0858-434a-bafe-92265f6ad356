<?php

declare(strict_types=1);

require_once '_allstats.inc';

function count_pagehit(int $flags = 0): void {
	if (dont_register()) {
		return;
	}
	require_once '__translation.php';
	require_once '_spider.inc';
	require_once '_allstats.inc';
	require_once '_browser.inc';
	require_once '_smallscreen.inc';
	require_once '_identity.inc';

	$setlist = [];
	$robot = ROBOT || isset($_REQUEST['ASROBOT']);

	global $__FORUMID;
	if ((	$forumid =
				have_number($_REQUEST,'FORUMID')
			?	$_REQUEST['FORUMID']
			:	($__FORUMID ?? 0)
		)
	&&	memcached_forum($forumid)
	) {
		$setlist[] = 'FORUMID = '.$forumid;
	}
	if (!empty($_REQUEST['sELEMENT'])
	&&	($sectionid = get_sectionid($_REQUEST['sELEMENT']))
	) {
		$setlist[] = 'SECTIONID = '.$sectionid;
	}
	if ($browserid = get_browserid()) {
		$setlist[] = 'BROWSERID = '.$browserid;
	}
	if ($_SERVER['REQUEST_METHOD'] === 'POST') {
		$flags |= ALLSTAT_POST;
	}
	if (SMALL_SCREEN) {
		$flags |= ALLSTAT_SMALL_SCREEN;
	}
	$flags |= ALLSTAT_SSL;
	if (!$robot || CURRENTUSERID > 1) {
		if (have_user()) {
			$flags |= ALLSTAT_USER;
			if (have_admin()) {
				$flags |= ALLSTAT_ISADMIN;
			}
		}
		if ($city = get_current_city()) {
			if (!$city['SURE']) {
				$flags |= ALLSTAT_CHOSENCITY;
			}
			$setlist[] = 'CITYID = '.$city['CITYID'];
		}
		if (CURRENTUSERID > 1) {
			$setlist[] = 'SESSIONID = '.$GLOBALS['currentuser']->sessionid;
			$setlist[] = 'USERID = '.CURRENTUSERID;
		}
	} else {
		$flags |= ALLSTAT_SPIDER;
	}
	if (defined('LITE') && LITE) {
		$flags |= ALLSTAT_LITE;
	}
	if ($flags) {
		$setlist[] = 'FLAGS = '.$flags;
	}
	$data = [];
	if ($_POST) {
		$post_copy = [];
		foreach ($_POST as $field => $value) {
			switch ($field) {
			case 'NEW_FLOCK_PASSWD':
			case 'NEW_PASSWD':
			case 'NEW_PASSWD_TWO':
			case 'CHECKPASSWD':
			case 'TMPPASSWD':
			case 'PASSWORD':
				$post_copy[$field] = 'xxxxxxxx';
				break;
			default:
				$len = strlen(igbinary_serialize($value));

				# store max 1 MB per field
				$post_copy[$field] =
					$len > MEGABYTE
				?	'TOO_MUCH_DATA('.$len.')'
				:	$value;
			}
		}
		$data['_POST'] = $post_copy;
	}
	if (!empty($_FILES)) {
		$data['_FILES'] = $_FILES;
	}
	foreach (['all__error', 'all__warning'] as $notif) {
		if (empty($GLOBALS[$notif])) {
			continue;
		}
		$data[$notif] = $GLOBALS[$notif];
	}
	require_once '_domain.inc';
	db_insert('pagehitnew','
	INSERT INTO pagehitnew SET
		DOMAIN	= "'.CURRENTDOMAIN.'",
		DATA	= '.($data ? 'COMPRESS("'.addslashes(get_r($data)).'")' : '""').',
		REQUEST	= "'.addslashes($_SERVER['REQUEST_URI']).'",
		LANGID	= '.CURRENTLANGID.',
		STAMP	= '.CURRENTSTAMP.',
		IDENTID	= '.(defined('CURRENTIDENTID') ? CURRENTIDENTID : '0').',
		IPBIN	= '.(CURRENTIPBIN ? '"'.addslashes(CURRENTIPBIN).'"' : 'NULL').',
		IPNUM	= '.(CURRENTIPNUM ?: 'NULL').
		($setlist ? ', '.implode(', ', $setlist) : ''),
		DB_KEEP_CLEAN
	);
}
