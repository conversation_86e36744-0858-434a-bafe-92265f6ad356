<?php

declare(strict_types=1);

require_once '_currency.inc';
require_once '__translation.php';

function get_price(
	float	$price_in_cents,
	string	$currency = 'EUR',
	# Show .00 decimals instead of a minus sign
	bool	$decimal_zero = false,
): string {
	$prefix = get_currency_prefix($currency) ?: $currency;
	$rounded_price_in_cents = round($price_in_cents);
	$result = '';
	if ($help_span = ($rounded_price_in_cents !== $price_in_cents)) {
		$result .= '<span class="help" title="'.$prefix.' '.($price_in_cents / 100).'">';
	}
	if ($prefix) {
		$result .= $prefix.' ';
	}
	static $locale_info = get_locale_info();
	$str = number_format(
		$price_in_cents / 100,
		2,
		$locale_info['decimal_point'],
		$locale_info['thousands_sep']
	);
	if ((	$decimal_zero
		||	($rounded_price_in_cents % 100)
		)
	&&	str_ends_with($str, '00')
	) {
		$str = substr($str, 0, -2).'-';
	}
	$result .= $str;
	if (!$prefix && $currency) {
		$result .= ' '.$currency;
	}
	if ($help_span) {
		$result .= '</span>';
	}
	return $result;
}

function get_price_number(float $cent): string {
	$round_cent = round($cent);
	if ($round_cent % 100) {
		return sprintf('%d,%02d', (int)($round_cent / 100), $round_cent % 100);
	}
	return ($round_cent / 100).',-';
}
