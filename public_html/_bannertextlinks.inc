<?php

declare(strict_types=1);

function get_banner_textlinks(int $bannerid, bool $show_warnings = false): array|false|null {
	if (!($body = memcached_single('bannertextlinks','
		SELECT TEXTLINKS
		FROM bannertextlinks
		WHERE BANNERID = '.$bannerid
	))) {
		return $body;
	}
	$text_links = null;
	$text = null;
	foreach (explode("\n", $body) as $link_part) {
		if (!($link_part = trim($link_part))) {
			continue;
		}
		if (preg_match('"^https?://"', $link_part)) {
			if ($text) {
				$text_links[] = [$text, $link_part];
				$text = null;
			} elseif ($show_warnings) {
				register_warning('textlink:warning:missing_text_LINE', ['LINK' => $link_part]);
			}
		} elseif ($text) {
			if ($show_warnings) {
				register_warning('textlink:warning:consecutive_text_ignored_LINE', ['TEXT' => $link_part]);
			}
		} else {
			$text = $link_part;
		}
	}
	return $text_links;
}
