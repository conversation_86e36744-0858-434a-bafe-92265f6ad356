<?php

function pixelitem_process(): void {
	require_once '_currentuser.inc';
	global $currentuser;
	$currentuser = new _currentuser(TINY_USER | SKIP_SETTINGS);
	if (!require_admin('pixel')) {
		bail(403);
	}
	if (false === require_number($_POST, 'ACTIVE')
	||	!($pixelid = require_idnumber($_POST, 'PIXELID'))
	||	!($element = require_element($_POST, 'ELEMENT', ['party', 'ad'], true))
	||	!($item_id = require_idnumber($_POST, 'ID'))
	) {
		bail(400);
	}
	$new_active	= $_POST['ACTIVE'] ? '1' : '0';

	if (!db_insert("pixel{$element}_log", "
		INSERT INTO pixel{$element}_log
		SELECT *, 0, 0
		FROM pixel$element
		WHERE ACTIVE 		!= $new_active
		  AND {$element}ID	 = $item_id
		  AND PIXELID		 = $pixelid")
	) {
		bail(500);
	}
	if (!db_affected()) {
		bail(200);
	}
	if (!db_update("pixel$element", "
		UPDATE pixel$element SET
			MUSERID	= ".CURRENTUSERID.',
			MSTAMP	= '.CURRENTSTAMP.",
			ACTIVE	= $new_active
		WHERE ACTIVE		!= $new_active
		  AND {$element}ID	 = $item_id
		  AND PIXELID 		 = $pixelid")
	) {
		bail(500);
	}
	update_pixel_stopstamp($pixelid);
	flush_active_pixels();
	bail(200);
}

function update_pixel_stopstamp(
	?int $pixelid  = null,
	?int $partyid  = null,
	bool $no_log   = false,
	bool $get_only = false
): int|false {
	# FIXME: also check connected ads and use them stopstamps
	if ($partyid) {
		if (!($pixelids = db_simpler_array('pixelparty', 'SELECT PIXELID FROM pixelparty WHERE PARTYID = '.$partyid))) {
			return false;
		}
		$stopstamps = [];
		foreach ($pixelids as $pixelid) {
			if (!($stopstamp = update_pixel_stopstamp($pixelid))) {
				return false;
			}
			$stopstamps[] = $stopstamp;
		}
		return max($stopstamps);
	}
	if (!$pixelid) {
		mail_log('update_pixel_stopstamp called without pixelid');
		return false;
	}

	if (false === ($pixel = db_single_assoc('pixel', 'SELECT PARTYID, STOPSTAMP FROM pixel WHERE PIXELID = '.$pixelid))) {
		return false;
	}
	
	if ($pixel['PARTYID']) {
		$new_stopstamp = db_single_int('party', "
			SELECT STAMP + DURATION_SECS
			FROM party
			WHERE PARTYID = {$pixel['PARTYID']}",
			DB_FORCE_MASTER
		);
	} else {
		$new_stopstamp = db_single_int(['party', 'pixelparty', 'ad', 'pixelad'], "
			SELECT	GREATEST(
						COALESCE(
							(	SELECT MAX(STAMP + DURATION_SECS)
								FROM party
								JOIN pixelparty USING (PARTYID)
								WHERE ACTIVE
								  AND PIXELID = $pixelid
							),	0
						),
						COALESCE(
							(	SELECT MAX(STOPSTAMP)
								FROM ad
								JOIN pixelad USING (ADID)
								WHERE pixelad.ACTIVE
								  AND PIXELID = $pixelid
							),	0
						)
					)",
			DB_FORCE_MASTER
		);
	}
	if ($new_stopstamp === false) {
		return false;
	}
	$stopstamp = max($pixel['STOPSTAMP'], $new_stopstamp);
	if ($get_only) {
		return $stopstamp;
	}
	if ($stopstamp !== $new_stopstamp) {
		if ($no_log) {
			# Used by pixel form commit, because we just inserted/updated the pixel,
			# we don't want to create an 'update' for fixing the stopstamp afterwards.
			if (!db_update('pixel', "
				UPDATE pixel SET
					STOPSTAMP = $new_stopstamp
				WHERE PIXELID = $pixelid")
			) {
				return false;
			}
		} elseif (
			!db_insert('pixel_log', "
			INSERT INTO pixel_log
			SELECT * FROM pixel
			WHERE STOPSTAMP != $new_stopstamp
			  AND PIXELID = $pixelid")
		||	db_affected()
		&&	!db_update('pixel', '
			UPDATE pixel SET
				MUSERID		= '.CURRENTUSERID.',
				MSTAMP		= '.CURRENTSTAMP.",
				STOPSTAMP	= $new_stopstamp
			WHERE STOPSTAMP != $new_stopstamp
			  AND PIXELID = $pixelid")
		) {
			return false;
		}
	}
	return $stopstamp;
}

const ALL_ACTIVE_PIXELS_CACHE_KEY	= 'activepixels_v7';

function flush_active_pixels(): void {
	memcached_delete(ALL_ACTIVE_PIXELS_CACHE_KEY);
}

function get_all_active_pixels(): array {
	static $__all_active_pixels = 0;
	if (!($force_fresh = isset($_REQUEST['NOMEMCACHE']))) {
		if ($__all_active_pixels !== 0) {
			return $__all_active_pixels;
		}
		if (false !== ($__all_active_pixels = memcached_get(ALL_ACTIVE_PIXELS_CACHE_KEY))) {
			return $__all_active_pixels;
		}
	}
	$pixels = db_rowuse_array(['pixel', 'pixelparty', 'pixelad'], '
		SELECT	pixel.PIXELID, FACEBOOKID, GOOGLEID, TIKTOKID, PINTERESTID, SNAPCHATID, pixel.STOPSTAMP,
				ORGANIZATIONID,
				pixel.PARTYID,
				GROUP_CONCAT(DISTINCT pixelparty.PARTYID) AS PARTYIDS,
				GROUP_CONCAT(DISTINCT pixelad.ADID) AS ADIDS
		FROM pixel
		LEFT JOIN pixelparty
			   ON pixelparty.PIXELID = pixel.PIXELID
			  AND pixelparty.ACTIVE
		LEFT JOIN pixelad
			   ON pixelad.PIXELID = pixel.PIXELID
			  AND pixelad.ACTIVE
		WHERE pixel.ACTIVE
		  AND pixel.STOPSTAMP > '.(TODAYSTAMP + ONE_DAY).'
		GROUP BY pixel.PIXELID'
	);

	$timeout = ONE_DAY;

	$__all_active_pixels = [];

	if (!$pixels) {
		if ($pixels === false) {
			$timeout = 60;
		}
	} else {
		foreach ($pixels as $pixel) {
			extract($pixel);

			if (CURRENTSTAMP >= $STOPSTAMP) {
				continue;
			}

			$store_pixel = [$FACEBOOKID, $GOOGLEID, $TIKTOKID, $PINTERESTID, $SNAPCHATID];
			$connections = [];

			if ($ORGANIZATIONID) {
				$connections[] = ['organization', $ORGANIZATIONID];
			}
			if ($PARTYID) {
				$connections[] = ['party', $PARTYID];
			}
			if ($PARTYIDS) {
				foreach (explode(',', $PARTYIDS) as $partyid) {
					$connections[] = ['party', (int)$partyid];
				}
			}
			if ($ADIDS) {
				foreach (explode(',', $ADIDS) as $adid) {
					$connections[] = ['need_ad', (int)$adid];
				}
			}
			foreach ($connections as [$element, $id]) {
				if (!isset($__all_active_pixels[$element][$id][$PIXELID])) {
					$__all_active_pixels[$element][$id][$PIXELID] = $store_pixel;
				}
			}
		}
	}
	memcached_set(ALL_ACTIVE_PIXELS_CACHE_KEY, $__all_active_pixels, $timeout);
	return $__all_active_pixels;
}

function get_pixels_for_item(?string $element = null, ?int $id = null): array {
	$element ??= $_REQUEST['sELEMENT'];
	$id		 ??= $_REQUEST['sID'];
	return get_all_active_pixels()[$element === 'ad' ? 'need_ad' : $element][$id] ?? [];
}

function get_active_pixels(): array {
	static $__active_pixels;
	if (!($force_fresh = isset($_REQUEST['NOMEMCACHE']))) {
		if (isset($__active_pixels))  {
			return $__active_pixels;
		}
	}

	$all_active_pixels = get_all_active_pixels();

	$__active_pixels = $all_active_pixels[$_REQUEST['sELEMENT']][$_REQUEST['sID']] ?? [];

	if (!empty($all_active_pixels['need_ad'])
	&&	($shown_adids = get_shown_adids())
	) {
		foreach (array_intersect_key($all_active_pixels['need_ad'], $shown_adids) as $adid => $ad_pixels) {
			foreach ($ad_pixels as $pixelid => $store_pixel) {
				$__active_pixels[$pixelid] = $store_pixel;
			}
		}
	}
	return $__active_pixels;
}

function show_pixel_table(array $pixels): void {
	?><table class="small fw default hla"><?
	?><tr><?
	?><th><?= Eelement_name('organization') ?></th><?
	if (!SMALL_SCREEN) {
		?><th><?= Eelement_name('event') ?></th><?
		?><th><?= Eelement_name('relation') ?></th><?
	}
	?><th class="right"><?= Eelement_name('stop') ?></th><?
	?><th></th><?
	?></tr><?
	foreach ($pixels as $pixel) {
		extract($pixel);
		?><tr<?
		?> class="ptr<?
		if ($ACTIVE) {
			?> active notice-nb<?
		}
		?>"<?
		?> onclick="openLink(event,'/pixel/<?= $PIXELID ?>')"<?
		?>><?

		?><td><?
		if ($ORGANIZATIONID) {
			echo escape_utf8(get_element_title('organization', $ORGANIZATIONID));
		} elseif ($RELATIONID) {
			echo escape_utf8(get_element_title('relation', $RELATIONID));
		}
		?></td><?

		if (!SMALL_SCREEN) {
			?><td><?
			if ($PARTYID) {
				echo escape_utf8(get_element_title('party', $PARTYID));
			} elseif ($EVENTCNT) {
				echo $EVENTCNT, MULTIPLICATION_SIGN_ENTITY;
			}
			?></td><?
			?><td><?
			if ($RELATIONID) {
				echo escape_utf8(get_element_title('relation', $RELATIONID));
			}
			?></td><?
		}
		[$y, $m, $d, $hour, $mins] = _getdate($STOPSTAMP);
		?><td class="right"><?
		?><span style="width: 1.5em;" class="right ib"><?= $d ?></span> <?= short_month_name($m) ?> <?= $y
		?></td><td><?
		if ($AUTOEXTEND) {
			?> +<?
		}
		?></td><?
		?></tr><?
	}
	?></table><?
}

function auto_shrink_pixel(int $orgid, int $partyid): bool {
	return auto_extend_pixel($orgid, $partyid, extend: false);
}

function auto_extend_pixel(int $organizationid, int $partyid, bool $extend = true): bool {
	if (!($extendable = db_simple_hash('pixel', "
		SELECT PIXELID, ORGANIZATIONID
		FROM pixel
		WHERE ORGANIZATIONID = '$organizationid'
		  AND AUTOEXTEND
		  AND STOPSTAMP > ".CURRENTSTAMP))
	) {
		return $extendable !== false;
	}
	foreach ($extendable as $PIXELID => $ORGANIZATIONID) {
		if ($extend) {
			if (!db_insert('pixelparty', '
				INSERT IGNORE INTO pixelparty SET
					CUSERID		 = '.CURRENTUSERID.',
					CSTAMP		 = '.CURRENTSTAMP.",
					PIXELID		 = $PIXELID,
					PARTYID		 = $partyid,
					AUTOEXTENDED = 1")
			) {
				return false;
			}
		} else {
			if (!db_insert('pixelparty_log', '
				INSERT INTO pixelparty_log
				SELECT *, '.CURRENTSTAMP.', '.CURRENTUSERID." FROM pixelparty
				WHERE PIXELID = $PIXELID
				  AND PARTYID = $partyid")
			||	!db_delete('pixelparty', "
				DELETE FROM pixelparty
				WHERE PIXELID = $PIXELID
				  AND PARTYID = $partyid")
			) {
				return false;
			}
		}
	}
	flush_active_pixels();
	return true;
}

function pixel_name_select(): string {
	return 'IF(	LENGTH(organization.NAME),
				organization.NAME,
				IF(	LENGTH(party.NAME),
					party.NAME,
					IF (LENGTH(relation.NAME),
						relation.NAME,
						CONCAT("#", PIXELID)
					)
				)
			)';
}

const PIXEL_STATUS_PLACEHOLDER = "\x1PIXEL_STATUS\x4";

function show_pixel_status(bool $get_placeholder = false): bool {
	static $__placeholder = false;
	if ($get_placeholder) {
		return $__placeholder;
	}
	$__placeholder = true;
	echo PIXEL_STATUS_PLACEHOLDER;
	return true;
}

function replace_pixel_status(string &$body): void {
	if (!show_pixel_status(get_placeholder: true)) {
		return;
	}
	if (!($pixels = get_pixels_for_item())) {
		$body = str_replace(PIXEL_STATUS_PLACEHOLDER, '', $body);
		return;
	}
	ob_start();
	$pixelnames = db_simple_hash(['pixel', 'relation', 'party', 'organization'], '
		SELECT PIXELID, '.pixel_name_select().'
		FROM pixel
		LEFT JOIN relation USING (RELATIONID)
		LEFT JOIN party USING (PARTYID)
		LEFT JOIN organization USING (ORGANIZATIONID)
		WHERE PIXELID IN ('.implodekeys(', ', $pixels).')'
	);
	$count = count($pixels);
	?><div class="block fblue"><?
	echo get_facebook_icon()
	?> <?
	if ($count > 1) {
		echo $count; ?> <?
	}
	echo element_name('pixel', $count) ?> <? echo  __('status:active');

	if ($pixelnames) {
		?>: <?
		$shows = [];
		foreach ($pixelnames as $pixelid => $name) {
			$shows[] = '<a class="bold" href="/pixel/'.$pixelid.'">'.escape_utf8($name).'</a>';
		}
		echo implode(', ',$shows);
	}
	?></div><?
	$body = str_replace(PIXEL_STATUS_PLACEHOLDER, ob_get_clean(), $body);
}

function get_active_pixels_code(): string {
	require_once 'defines/google.inc';
	$pixels = get_active_pixels();
	# Partyflock pixels: (check the sites if they still work
	$pixels[0] = $every_page_pixels = [
		null,# 281446635659609,							# Facebook,	 https://business.facebook.com/events_manager2/list/pixel/281446635659609/overview?business_id=236313826732468&global_scope_id=236313826732468&act=10153865914180995&nav_source=flyout_menu
		GOOGLE_ANALYTICS_ID_GA4,						# Google,	 https://analytics.google.com/analytics/web/#/p265670072/reports/intelligenthome
		null,# 'CCCUUMBC77U3SM0RR720',					# TikTok,	 https://ads.tiktok.com/i18n/events_manager/pixel/detail/CCCUUMBC77U3SM0RR720?start_time=2023-04-05&end_time=2023-04-11&from=ALL&aadvid=7141001313097138178
		null,# 2613812181091,							# Pinterest, https://analytics.pinterest.com/audience-insights/?advertiserId=549765082700&tab=partner&audience_1=partner___impression_plus_engagement
		null,# '440ba9f7-90e5-4506-8a75-a4509ef5df8e',	# Snapchat,	 https://ads.snapchat.com/d095557d-164a-48c7-9592-9ad9638ee4f3/events?ref_aid=d095557d-164a-48c7-9592-9ad9638ee4f3
	];
	# $pixels[] = [null, GOOGLE_ANALYTICS_ID_GA4, null, null, null];
	$done = [];
	ob_start();
	foreach (['facebook', 'google', 'tiktok', 'pinterest', 'snapchat'] as $type) {
		ob_start();
		foreach ($pixels as [$facebookid, $googleid, $tiktokid, $pinterestid, $snapchatid]) {
			if (!($id = ${$type.'id'})) {
				continue;
			}
			if (isset($done[$type][$id])) {
				continue;
			}
			$done[$type][$id] = true;

			switch ($type) {
			case 'facebook':
				?>fbq('init','<?= $facebookid ?>'<?
				if (have_user()) {
					?>,{'em':'<?= hash('sha256', $GLOBALS['currentuser']->row['EMAIL']) ?>'}<?
				}
				?>);<?
				break;

			case 'google':
				?>gtag('config','<?= $googleid ?>'<?
				if (have_user()) {
					?>,{'user_id':'<?= CURRENTUSERID ?>'}<?
				}
				?>);<?
				break;

			case 'pinterest':
				?>pintrk('load','<?= $pinterestid ?>');<?
				break;

			case 'snapchat':
				?>snaptr('init','<?= $snapchatid ?>'<?
				if (have_user()) {
					?>,{'user_hashed_email':'<?= hash('sha256', $GLOBALS['currentuser']->row['EMAIL']) ?>'}<?
				}
				?>);<?
				break;

			case 'tiktok':
				?>ttq.load('<?= $tiktokid ?>');<?
				break;
			}
		}
		if (!($pixels_call_str = ob_get_clean())) {
			continue;
		}
		switch ($type) {
		case 'facebook':
			?>!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?<?
			?>n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;<?
			?>n.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;<?
			?>t.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,<?
			?>document,'script','<?= $script = 'https://connect.facebook.net/en_US/fbevents.js' ?>');<?
			break;

		case 'google':
			require_once 'defines/google.inc';
			include_js($script = 'https://www.googletagmanager.com/gtag/js?id='.GOOGLE_ANALYTICS_ID_GA4, JS_EXTERNAL);
			?>window.dataLayer=window.dataLayer||[];<?
			?>function gtag(){dataLayer.push(arguments);}<?
			?>gtag('js',new Date());<?
			break;

		case 'pinterest':
			?>!function(e){if(!window.pintrk){window.pintrk=function(){<?
				?>window.pintrk.queue.push(Array.prototype.slice.call(arguments))};<?
				?>var n=window.pintrk;n.queue=[],n.version='3.0';<?
				?>var t=document.createElement('script');t.async=!0,t.src=e;<?
				?>var r=document.getElementsByTagName('script')[0];<?
				?>r.parentNode.insertBefore(t,r)}}<?
			?>('<?= $script = 'https://s.pinimg.com/ct/core.js' ?>');<?
			break;

		case 'snapchat':
			?>(function(e,t,n){if(e.snaptr)return;var a=e.snaptr=function(){<?
				?>a.handleRequest?a.handleRequest.apply(a,arguments):a.queue.push(arguments)};<?
				?>a.queue=[];var s='script';r=t.createElement(s);r.async=!0;<?
				?>r.src=n;var u=t.getElementsByTagName(s)[0];<?
				?>u.parentNode.insertBefore(r,u);})<?
			?>(window,document,'<?= $script = 'https://sc-static.net/scevent.min.js' ?>');<?
			break;

		case 'tiktok':
			?>
			!function (w, d, t) {
				w.TiktokAnalyticsObject=t;
				var ttq=w[t]=w[t]||[];
				ttq.methods=['page','track','identify','instances','debug','on','off','once','ready','alias','group','enableCookie','disableCookie'],
				ttq.setAndDefer=function(t,e){t[e]=function(){t.push([e].concat(Array.prototype.slice.call(arguments,0)))}};
				for(var i=0;i<?= '<' ?>ttq.methods.length;i++)
			  		ttq.setAndDefer(ttq,ttq.methods[i]);
			  	ttq.instance=function(t){
			  		for(var e=ttq._i[t]||[],n=0;n<?= '<' ?>ttq.methods.length;n++)
			  			ttq.setAndDefer(e,ttq.methods[n]);
					return e
				},
				ttq.load=function(e,n){
					var i='<?= $script = 'https://analytics.tiktok.com/i18n/pixel/events.js'; ?>';
					ttq._i=ttq._i||{},ttq._i[e]=[],ttq._i[e]._u=i,
					ttq._t=ttq._t||{},ttq._t[e]=+new Date,ttq._o=ttq._o||{},
					ttq._o[e]=n||{};
					var o=document.createElement('script');
					o.type='text/javascript',
					o.async=!0,o.src=i+'?sdkid='+e+'<?= '&' ?>lib='+t;
					var a=document.getElementsByTagName('script')[0];
					a.parentNode.insertBefore(o,a)
				};
			<?
			break;
		}

		include_head("<link rel=\"preconnect\" href=\"$script\" />");

		echo $pixels_call_str;

		switch ($type) {
		case 'facebook':
			?>fbq('track','PageView');<?
			break;

		case 'pinterest':
			?>pintrk('page');<?
			break;

		case 'snapchat':
			?>snaptr('track','PAGE_VIEW');<?
			break;

		case 'tiktok':
			?>ttq.page();}<?
			?>(window,document,'ttq');<?
			break;

		}
	}
	return ob_get_clean();
}

function show_non_js_pixels(array $pixels): void {
	foreach ($pixels as [$facebookid, /*$googleid*/, /*$tiktokid*/, $pinterestid, /*$snapchatid*/]) {
		if ($facebookid) {
			?><img<?
			?> height="1"<?
			?> width="1"<?
			?> style="display:none"<?
			?> alt=""<?
			?> src="https://www.facebook.com/tr?id=<?= $facebookid ?>&amp;ev=PageView&amp;noscript=1&amp;pfcb=<?= uniqid() ?>" /><?
		}
		if ($pinterestid) {
			?><img<?
			?> height="1"<?
			?> width="1"<?
			?> style="display:none;"<?
			?> aalt=""R<?
			?> src="https://ct.pinterest.com/v3/?event=init&amp;tid=<?= $pinterestid ?>&amp;<?
			if (have_user()) {
				?>pd[em]=<?= hash('sha256', $GLOBALS['currentuser']->row['EMAIL']) ?>&amp;<?
			}
			?>noscript=1&amp;pfcb=<?= uniqid() ?>" /><?
		}
	}
}

function get_head_pixels(): string {
	static $__done = false;
	if ($__done) {
		mail_log('multiple calls to get_head_pixels');
		return '';
	}
	$__done = true;
	if (!($js_data = get_active_pixels_code())) {
		return '';
	}
	ob_start();
	?><script><?= minify_js($js_data) ?></script><?
	return ob_get_clean();
}

function show_non_head_pixels(): void {
	if (!($pixels = get_active_pixels()))  {
		return;
	}
	?><noscript><? show_non_js_pixels($pixels); ?></noscript><?
}
