<?php

declare(strict_types=1);

require_once '_memcache.inc';
require_once '_require.inc';

#const TEST_SIMPLER = HOME_THOMAS || CLI;
const TEST_SIMPLER = true;

function catch_presales(
	string	$tmp,
	int		$partyid,
	bool	$for_appic = false
): ?string {
	$processed_key = 'caught_presales:v2:'.hash('xxh128', $tmp);
	if (!isset($_REQUEST['NOMEMCACHE'])
	&&	($processed = memcached_get($processed_key))
	) {
		return $processed;
	}
	require_once '_date.inc';
	require_once '_presale.inc';
	require_once '_unicode.inc';

	if (!($party = memcached_party_and_stamp($partyid))) {
		return $tmp;
	}
	if (str_contains($tmp, "\r")) {
		# patterns don't work with carriage returns in them
		$tmp = str_replace("\r", '', $tmp);
	}
	if (!($presale_url = get_generic_presale_link($partyid, $party, $for_appic ? 'appic' : '')[0])) {
		$presale_url = '--';
	}

	if (TEST_SIMPLER) {
		# Match
		# \p{Emoji} Emoji's
		# \pS	   Symbols
		# \pP	   Punctuation
		$symbols =  '\p{Emoji}\pS\pP';
		if (false === ($ticket_recognition = db_single_string('ticketseller', "
			SELECT GROUP_CONCAT(REPLACE(RECOGNITION, '.', '\\\\.')  SEPARATOR '|')
			FROM ticketseller"))
		) {
			return $tmp;
		}
	} else {
		$symbols = get_symbs();
	}

	if ($party['PREREG_URL']) {
		$prereg_url = FULL_HOST.'/jumpto/prereg/'.$partyid.($for_appic ? '?appic' : '');
		$tmp = str_replace($party['PREREG_URL'], $prereg_url, $tmp);
		if (null === ($new_tmp = preg_replace_callback('"'.
				'('.	'(?:pre[-\h]*)?(?:registration|registratie|register|registreer)\s*'.
						'(je)?\s*'.
						'(?:now|nu|hier)?'.
						'[;:\s\n'.$symbols.']*'.
				')'.
				'('.
					'(?:'.	'https?'.
							'|prot\.cl/'.
							'|bit\.ly/'.
							'|www\.'.
							'|tinyurl\.'.
							'|[a-z\d%_.-]+/pre'.
					')[^\s\n]*)'.
			'"ius',
			static fn(array $match): string => $match[1].$prereg_url,
			$tmp))
		) {
			preg_failure($tmp);
		} else {
			assert(is_string($new_tmp)); # Satisfy EA inspection
			$tmp = $new_tmp;
		}
	}

	$ticket_re =
#		/** @lang RegExp */
		'('.
			'(?:'.	 'https?'.
					'|prot\.cl/'.
					'|[a-z\d_.-]+\.[a-z]{2,}/[a-z\d_/%.+-]+'.
					'|bit\.ly/'.
					'|www\.'.
					'|tinyurl\.'.
					'|[a-z\d%_.-]+/tickets'.
					'|[a-z\d%_.-/]{4,}+[#/]tickets'.
			')[^\s\n]*'.
		')';

	/** @noinspection RegExpDuplicateAlternationBranch */
	$regexp_one =
		/** @lang PhpRegExp */
			'"'.
			'(?:scoort?\s*j(?:e|ou))?\s*'.
			'(?:'.	'tix|'.
					'Get Tix Now|'.
					'𝗧𝗶𝗰𝗸𝗲𝘁𝘀|'.
					'𝐓𝐢𝐜𝐤𝐞𝐭𝐬|'.
					'𝐓𝐈𝐂𝐊𝐄𝐓𝐒|'.
					'𝗧𝗜𝗖𝗞𝗘𝗧𝗦|'.
					'𝙏𝙄𝘾𝙆𝙀𝙏𝙎|'.
					'ticketsale|'.
					'𝗣𝗥𝗘-𝗦𝗔𝗟𝗘|'.
					'VVK|'.
					'Koop\h*(?:NU\h*)?je\h*ticket\(?s\)?|'.
					'ticket\(?s?\)?\h*(?:\s*sale|link|shop|verkoop)?|'.
					'(?:voor|kaart)?verkoop|'.
					'Kaartjes\s*koop\s*je|'.
					'(?:pre|𝗣𝗥𝗘)[\s-]*(?:sale|𝗦𝗔𝗟𝗘)|early[\s\-]*bird\s*(?:tickets?)?'.
			')'.
			'(?:\s*[vf]ia\s+)?'.
			'[;:\s\n'.$symbols.'→]*'.
			'(?:\s*\(\d+[AP]M\)\s*)?'.
				'\s*(?:(?:en|&)\s*meer\s*informatie)?'.
					'\(?'.
						'\s*(?:for|voor)?'.
						'\s*(?:only)?'.
						'(?:\s*/?\s*(?:€[.\s]*)?(?:\d+(?:[,.\s]*(?:\d+|-)?)?)?)*'.
					'\)?'.
				'\s*(?:are|will\s*(?:be|sell)|zullen|zijn|worden|is)?'.
				'\s*(?:alleen)?'.
				'\s*(?:now|nu|fast)?'.
				'\s*(?:verkrijgbaar|available|beschikbaar|verkocht|gestart|starts?|for\s*sale|te\s*koop|scoor|on\s*sale)?'.
				'\s(?:je\s*tickets\s*voor\s*[^\n]+?)?'.
				'\s*(?:'.
					'\s*(?:the\s*)?\d+(?:th|st|nd)\s*of\s*\w+'.
						'\s*(?:\d{1,2}:\d{2})?'.
						'\s*(?:PM|AM)?'.
					'|'.
					'\s*(?:'.implode('|', get_day_names()).')?'.
					'\s*\d+'.
					'\s*(?:'.implode('|', get_month_from_string(true)).')'.
					'\s*\d*'.
					'\s*(?:om)?'.
					'\s*(?:\d{1,2}:\d{2})?'.
				')?'.
				'\s*(?:for|voor)?'.
				'\s*(?:only)?'.
				'(?:\s*/?\s*(?:€[.\s]*|\s+)(?:\d+[,.\s]*(?:\d+|-)?)?)*'.
				'\s*(?:zijn)?'.
				'\s*(?:(?:inc|exc?)(?:l(?:lusief)?)?\s*(?:pre\s*sale\s*)?fee)?'.
				'\s*(?:now|nu)?'.
				'\s*(?:van\s*start)?'.
				'\s*(?:op|bij|[vf]ia|at|through)?'.
				'\s*(?:Resident\s*Advisor|Starticket\.ch)?'.
			'[;:\s\n'.$symbols.']*'.
			'\s*(?:op|bij|[vf]ia|at|through|(?:right\s*)?here(?:\s*now)?|(?:nog\s*)?beschikbaar)?'.
			'\s*(?:de\s*website)?'.
			'\s*(?:online)?'.
			'[:\s!\-👉]*'.
			$ticket_re.
		'"ius';

	$regexp_two =
		/** @lang PhpRegExp */
		'"(?<url>(?:'.
			(TEST_SIMPLER ? '(?:https?://)?[a-z\d_.-]*(?:'.$ticket_recognition.')|' : '').
			'(?:https?://)?(?:www\.)?[^\s\n]+?/tickets?|'.
			'(?:https?://)?bit\.ly/[^\s\n]*tickets[^\s\n/]*|'.
			'(?:https?://)?[^\s\n#]{4,}#tickets'.
			(	TEST_SIMPLER
			?	''
			:	'|(?:https?://)?[a-z\d_.-]+\.sollidd\.com|'.
				'(?:https?://)?(?:(?:www|shop)\.)?ikbenaanwezig\.nl/|'.
				'(?:https?://)?eventix\.shop/[a-z\d]{8}|'.
				'https?://(?:(?:www|queue|shop|[^.]+)\.)?(?:'.
					'billetweb|'.
					'eventbrite|'.
					'eventix|'.
					'eventsquare|'.
					'paylogic|'.
					'stager|'.
					'tibbaa|'.
					'ticketmaster|'.
					'yourticketprovider'.
				')\.'
			).
		')[^\s\n]*)"ius';

	$regexp_three =
		/** @lang PhpRegExp */
		'"'.'(?:\n|^)(?:Link|Verkrijgbaar|Online|𝐓𝐈𝐂𝐊𝐄𝐓𝐒|𝗧𝗜𝗖𝗞𝗘𝗧𝗦|Tickets|Kaartverkoop)[\s:]*'.
				'(?:€[.\s]*)?(?:\d+(?:[,.\s]*(?:\d+|-)?)?)?'.
				'\s*(?:[vf]ia)?'.
				'[\s>]*'.
				$ticket_re.
			'"ius';

	$regexp_four =
		/** @lang PhpRegExp */
		'"'.	'Tickets?[^\n]*?['.$symbols.'\s:]*'.
				'(?:['.$symbols.'][^\n]*?\n)*'.
				'['.$symbols.'\s]*'.
				'Via(?:\s*de\s*website)?[:\s]*'.
				$ticket_re.
			'"ius';

	$regexp_five =
		/** @lang PhpRegExp */
		"!TICKETS\h*\($ticket_re\)!ius";

	$regexp_six =
		/** @lang PhpRegExp */
		# Tickets for Sonic Acts Academy > http://bit.ly/2BHP5lf
		'"Tickets for [\w\s]+ >? '.$ticket_re.'"ius';

	$regexp_seven =
		/** @lang PhpRegExp */
		# Tickets available as of Monday 2/12 18h Partybus 20h Regular tickets (minimum 21 years): https://bit.ly/TDGladiator
		'"Tickets available as of [^\n]*?:\s*'.$ticket_re.'"ius';

	$done = [];
	foreach ([
		$regexp_one,
		$regexp_two,
		$regexp_three,
		$regexp_four,
		$regexp_five,
		$regexp_six,
		$regexp_seven,
	] as $regexp) {
		if (!preg_match_all($regexp, $tmp, $matches)) {
			continue;
		}
		foreach ($matches[1] as $url) {
			# trim end of sentence indicators like .!? from the end of the URL
			$url = rtrim($url, ',.:;!?');
			if (isset($done[$url])
			||	str_starts_with($url, 'https://partyflock.nl')
			) {
				continue;
			}
			$done[$url] = true;
			$replace_url_regexp = '!(?<=\b|\s)'.preg_quote($url, '!').'(?=\b|\s)!u';
			if (null === ($new_tmp = preg_replace($replace_url_regexp, $presale_url, $tmp))) {
				preg_failure($tmp, $replace_url_regexp);
			} else {
				assert(is_string($new_tmp));
				$tmp = $new_tmp;
			}
		}
	}
	if (null !== ($new_tmp = preg_replace_callback($regexp = /** @lang PhpRegExp */ '"https?://?(?:[a-z]+\.)?facebook\.com/events/(?<fbid>\d+)"',
		static function(array $match): string {
			require_once 'defines/appic.inc';
			if (!($partyid = memcached_single('fbid', '
				SELECT DISTINCT '.PARTY_MAIN_ID_INC."
				FROM fbid
				JOIN party ON party.PARTYID = fbid.ID
				WHERE FBID = {$match['fbid']}"))
			) {
				return $match[0];
			}
			return FULL_HOST.'/party/'.$partyid;
		}, $tmp))
	) {
		$tmp = $new_tmp;
	} else {
		preg_failure($tmp, $regexp);
	}
	if (null !== ($new_tmp = preg_replace_callback($regexp = '"(?:https?://)?(?:[a-z]+\.)?facebook\.com/(?<!events/)([^/\s]+)"iu',
		static function(array $match): string {
			[$orig, $name] = $match;
			if (preg_match('"-(?<fbid>\d{8,})$"', $name, $match)
			&&	($item = memcached_single_assoc('fbid', <<<MARIADB
					SELECT ELEMENT, ID
					FROM fbid
					WHERE FBID = {$match['fbid']}
					ORDER BY
						ELEMENT = 'presence',
						fbid.TYPE = 'page' DESC,
						fbid.TYPE = 'user' DESC
					LIMIT 1
					MARIADB))
			) {
				return FULL_HOST.'/'.$item['ELEMENT'].'/'.$item['ID'];
			}
			if ($item = memcached_single_assoc('fbid',"
				SELECT ELEMENT, ID
				FROM fbid
				JOIN facebook_link USING (FBID)
				WHERE LINK = '".addslashes($name)."'
				ORDER BY
					ELEMENT = 'presence',
					fbid.TYPE = 'page' DESC,
					fbid.TYPE = 'user' DESC
				LIMIT 1")
			) {
				return FULL_HOST."/{$item['ELEMENT']}/{$item['ID']}";
			}
			return $orig;
		},$tmp))
	) {
		$tmp = $new_tmp;
	} else {
		preg_failure($tmp, $regexp);
	}
	memcached_set($processed_key, $tmp, ONE_DAY);
	return $tmp;
}
