<?php

declare(strict_types=1);

require_once '__translation.php';

const STAR_ELEMENT_TO_COLOR = [
	'artist'		=> 'yellow',
	'location'		=> 'purple',
	'music'			=> 'orange',
	'organization'	=> 'cyan',
	'party'			=> 'blue',
	'stream'		=> 'green',
];

function get_star_name(?string $element = null): string {
	$element ??= $_REQUEST['sELEMENT'];
	return STAR_ELEMENT_TO_COLOR[$element] ?? $element;
}

function get_crown(?string $alt = null): string {
	ob_start();
	?><img<?
	?> src="<?= STATIC_HOST ?>/images/relation<?= is_high_res() ?>.png"<?
	?> class="star"<?
	if ($alt) {
		?> title="<?= $escaped_title = escape_utf8($alt) ?>"<?
		?> alt="<?= $escaped_title ?>"<?
	}
	?> /><?
	return ob_get_clean();
}

function get_star(
	?string $element		= null,
	 string|false|null $alt	= null,
	?string $color			= null,
): string {
	if (!$color) {
		$element ??= $_REQUEST['sELEMENT'];
	}
	ob_start();
	?><img<?
	?> class="star"<?
	?> src="<?= STATIC_HOST ?>/star/<?
	if ($color) {
		echo $color;
	} elseif ($element) {
		echo get_star_name($element);
	} else {
		?>transparent<?
	}
	echo is_high_res() ?>.<?=  webp_supported() ? 'webp' : 'png' ?>"<?
	if ($alt === null) {
		?> alt="<?= __('attrib:favourite') ?>"<?
	} elseif ($alt) { // NOSONAR
		?> alt="<?= escape_specials($alt) ?>"<?
	}
	?> /><?
	return ob_get_clean();
}

function show_star(?string $element = null): void {
	?> <? echo get_star($element);
}

function get_header_star(?string $alt = null): string {
	if (!have_user()) {
		return '';
	}
	if ($alt === null) {
		$alt = __('attrib:favourite');
	} elseif ($alt) { // NOSONAR
		$alt = escape_utf8($alt);
	}
	require_once '_favourite.inc';
	ob_start();
	?><img<?
	?> id="fstar"<?
	if ($alt) {
		?> title="<?= $alt ?>"<?
		?> alt="<?= $alt ?>"<?
	}
	?> class="lmrgn star<? if (!check_favourite()) { ?> hidden<? } ?>"<?
	?> src="<?= STATIC_HOST ?>/star/<?= get_star_name(), is_high_res() ?>.<?= webp_supported() ? 'webp' : 'png' ?>" /><?
	return ob_get_clean();
}
