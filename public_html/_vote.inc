<?php

declare(strict_types=1);

require_once '_voice.inc';
require_once '__translation.php';
require_once 'defines/vote.inc';

const NEED_VOTES = 4;	# need this many vote's to actually display result

const VOTABLES = [
	'artist'		=> 'artist',
	'gallery'		=> 'gallery',
	'location'		=> 'location',
	'music'			=> 'music',
	'organization'	=> 'organization',
	'party'			=> 'party',
	'review'		=> 'review',
	'talentupload'	=> 'talentuplooad',
];

/*const TIME_CORRECTABLE = [
	'artist'		=> 'artist',
	'location'		=> 'location',
	'organization'	=> 'organization',
];*/

const VOTE_BG_COLOR = [
	null,
	[0xffdbdb ,0x2e0000], # ultra-red
	[0xffdfdb, 0x2e0500], # super-red
	[0xffe5db, 0x2e0c00], # red
	[0xffeadb, 0x2e1300], # orange-red
	[0xffeddb, 0x2e1700], # orange
	[0xf7f1db, 0x241c00], # orange-green
	[0xecf7db, 0x152300], # green
	[0xe1fcdb, 0x072a00], # super-green
	[0xdbffdb, 0x002e00], # ultra-green
];

const VOTE_BAR_NAME = [
	null,
	'ultra-red',
	'super-red',
	'red',
	'orange-red',
	'orange',
	'orange-green',
	'green',
	'super-green',
	'ultra-green',
];

function is_votable(string $element): bool {
	return isset(VOTABLES[$element]);
}

function time_correctable(string $element): bool {
	#return isset(TIME_CORRECTABLE[$element]);
	return false;
}

function vote_name(int $vote_id): ?string {
	static $__vote_name;
	return $__vote_name[$vote_id] ??= __('vote:'.$vote_id);
}

function vote_bg_value(int $vote_id): int {
	return VOTE_BG_COLOR[$vote_id][LITE ? 0 : 1];
}

function vote_bar_name(int $vote_id): ?string {
	return VOTE_BAR_NAME[$vote_id];
}

function vote_process(): bool {
	/** @noinspection RedundantSuppression, NotOptimalIfConditionsInspection */
	if (!($id = (int)$_REQUEST['sID'])
	||	!have_user()
	||	!isset($_POST['CASTVOTE'])
	||	!is_number($_POST['CASTVOTE'])
	||	($vote = (int)$_POST['CASTVOTE']) > MAX_VOTEID
	||	!is_votable($sELEMENT = $_REQUEST['sELEMENT'])
	) {
		return false;
	}
	if (isset($_POST['TMP'])) {
		if (!db_replace('vote_tmp',"
			REPLACE INTO vote_tmp SET
				TYPE	= '$sELEMENT',
				ID  	= $id,
				STAMP	= ".CURRENTSTAMP.',
				USERID	= '.CURRENTUSERID.",
				VOTEID	= $vote")
		) {
			return false;
		}
		memcached_delete('goingasked:v2:'.CURRENTUSERID);
		require_once '_background_queries.inc';
		rm_bgquery_votes($sELEMENT, $id);
		return true;
	}
	if (!db_insert('votenew_log','
		INSERT INTO votenew_log	
		SELECT * FROM votenew
		WHERE USERID ='.CURRENTUSERID."
		  AND TYPE	= '$sELEMENT'
		  AND ID	= $id")
	) {
		return false;
	}
	if (!$vote) {
		if (!db_insert('votenew_log','
			INSERT INTO votenew_log SET
				STAMP	= '.CURRENTSTAMP.',
				USERID	= '.CURRENTUSERID.",
				TYPE	= '$sELEMENT',
				ID		= $id,
				VOTEID	= 0")
		||	!db_delete('votenew', '
			DELETE FROM votenew
			WHERE USERID = '.CURRENTUSERID."
			  AND TYPE	 = '$sELEMENT'
			  AND ID	 = $id")
		) {
			return false;
		}
		register_notice('vote:notice:vote_removed_LINE');
		require_once '_background_queries.inc';
		rm_bgquery_votes($sELEMENT, $id);
		return true;
	}
	if (!may_speak()) {
		register_notice('vote:error:may_not_vote_LINE');
		return false;
	}
	if (!db_insupd('votenew','
		INSERT INTO votenew SET
			STAMP	= '.CURRENTSTAMP.',
			USERID	= '.CURRENTUSERID.",
			TYPE	= '$sELEMENT',
			ID		= $id,
			VOTEID	= $vote
		ON DUPLICATE KEY UPDATE
			STAMP	= ".CURRENTSTAMP.",
			VOTEID	= $vote")
	) {
		return false;
	}
	register_notice('vote:notice:vote_registered_LINE');
	require_once '_background_queries.inc';
	rm_bgquery_votes($sELEMENT, $id);
	return true;
}

function get_median(array $votes): ?int {
	if (!$votes) {
		return null;
	}
	# Emmpty string is the WITH ROLLUP total
	$halve = $votes[''] >> 1;
	foreach ($votes as $vote_id => $cnt) {
		if (!is_int($vote_id)) {
			# totals
			continue;
		}
		$halve -= $cnt;
		if ($halve < 0) {
			return $vote_id;
		}
	}
	if (is_int($first_id = array_key_first($votes))) {
		return $first_id;
	}
	return null;
}

function get_votes(string $element, int $id, bool $corrected = false): array|false {
	static $__votes;
	if ( isset($__votes[$element][$id][$corrected])) {
		return $__votes[$element][$id][$corrected];
	}
	# votes are worth less the older they are.
	require_once '_background_queries.inc';
	if (!($result = get_bgquery(BGQRY_VOTES, $element, $id))) {
			return false;
	}
	[$votes, $extra_votes] = $result;

	if (!$votes && isset($extra_votes)) {
		$votes = $extra_votes;
		$extra_votes = null;
	}
	$normal_votes = [];
	$corrected_votes = [];
	if ($votes) {
		# Emmpty string is the WITH ROLLUP total
		foreach ([0, 1, 2, 3, 4, 5, 6, 7, 8, 9, ''] as $vote_id) {
			$count = 0;
			$corrected_count = 0;
			if (isset($extra_votes[$vote_id])) {
				[, $extra_count, $extra_corrected_count] = $extra_votes[$vote_id];
						  $count += $extra_count;
				$corrected_count += $extra_corrected_count;
			}
			if (isset($votes[$vote_id])) {
				[, $extra_count, $extra_corrected_count] = $votes[$vote_id];
						  $count += $extra_count;
				$corrected_count += $extra_corrected_count;
			}
			   $normal_votes[$vote_id] = $count;
			$corrected_votes[$vote_id] = $corrected_count;
		}
	}
	$__votes[$element][$id][false] = $normal_votes;
	$__votes[$element][$id][true]  = $corrected_votes;
	if ($corrected) {
		return $corrected_votes;
	}
	return $normal_votes;
}

function vote_display_choices(
	?array	$item	 = null,
	?array	$my_vote = null,
	?string	$element = null,
	?int	$id		 = null,
	bool	$tmp	 = false,
 ): int|false|null {
	$element ??= $_REQUEST['sELEMENT'];
	$id		 ??= $_REQUEST['sID'];
	# NOTE: Very early votes have stamp = 0
	if (!$my_vote
	&&	false === ($my_vote = db_single_array('votenew', "
		SELECT VOTEID, STAMP
		FROM votenew
		WHERE TYPE ='$element'
		  AND ID = $id
		  AND USERID = ".CURRENTUSERID,
		DB_USE_MASTER))
	) {
		return false;
	}
	if ($my_vote) {
		[$my_vote_id, $stamp] = $my_vote;
		# ints get transformed in strings when used as keys for associative array
		# FIXME: But this $my_vote is no associative array?
		$my_vote_id = (int)$my_vote_id;
	} else {
		$my_vote_id = false;
	}
	include_style('votes');
	if ($show_full = !isset($_SERVER['AJAX'])) {
		include_js('js/vote');
		layout_open_box('white bordered');
		if (!$tmp) {
			ob_start();
			if ($element === 'review') {
				$item_type = $item ? $item['TYPE'] : db_single('review','SELECT TYPE FROM review WHERE REVIEWID='.$id);
				echo __("vote:question:{$item_type}_{$element}_LINE");
			} else {
				echo __("vote:question:{$element}_LINE");
			}
			$question = ob_get_clean();
			layout_box_header($question);
		}
		?><div><?
	}
	?><div<?
	?> class="vote-options"<?
	?> data-element="<?= $element ?>"<?
	?> data-id="<?= $id ?>"<?
	if ($tmp) {
		# used for temporary votes on /agenda/going
		?> data-tmp="data-tmp"<?
		if ($my_vote) {
			?> data-voted="data-voted"<?
		}
		?> id="vote-<?= $element ?>-<?= $id ?>"<?
	}
	?>><?

	for ($vote_id = MAX_VOTEID; $vote_id >= 1; --$vote_id) {
		$bar_name = vote_bar_name($vote_id);
		?><span<?
		?> onclick="Pf_makeVote(this, <?= $vote_id ?>);"<?
		?> class="<?
		if ($my_vote_id === $vote_id) {
			?>bold bar <?= $bar_name ?> <?
		}
		?>vote-choice text-<?= $bar_name ?>"<?
		?>><?= vote_name($vote_id) ?></span><?
		if ($vote_id > 1) {
			?> <?
		}
	}
	if ($my_vote) {
		?> <?
		?><span onclick="Pf_makeVote(this, 0);" class="unhideanchor"><?= vote_name(0) ?></span><?
	}
	?></div><?
	if ($my_vote) {
		include_style('votes');
		# Very early votes have stamp = 0, so we don't show the date
		require_once '_elementnames.inc';
		require_once '_date.inc';
		?><hr class="slim"><?
		?><div><?= __('vote:info:your_vote_was_LINE',[
			'DATE' => $stamp ? _date_get($stamp) : '',
			'TIME' => $stamp ? _time_get($stamp) : ''
		])
		?>: <span class="vote text-<?= vote_bar_name($my_vote_id) ?>"><?= vote_name($my_vote_id) ?></span></div><?
		if ($stamp
		&&	time_correctable($element)
		) {
			?><div class="block"><?= __('vote:info:old_votes_less_weight_TEXT',DO_NL2BR,[
				'WEIGHT' => round(100 * ONE_YEAR / (ONE_YEAR + (TODAYSTAMP + ONE_DAY - $stamp) / 2))
			]) ?></div><?
		}
	}
	if ($show_full) {
		?></div><?
		layout_close_box();

	}
	return $my_vote_id;
}

function vote_show_row(bool $restart = true): void {
	$element = $_REQUEST['sELEMENT'];
	$id		 = $_REQUEST['sID'];

	if ($element === 'party'
	&&	($party = memcached_party_and_stamp($id))
	&&	$party['STAMP'] > TODAYSTAMP + ONE_DAY + ONE_HOUR
	) {
		# no votes yet
		return;
	}

	if (!($votes = get_votes($element, $id, time_correctable($element)))
	||  !($total_votes = $votes[''])
	) {
		return;
	}
	$restart ? layout_restart_reverse_row() : layout_start_reverse_row();

	include_style('votes');
	if ($total_votes >= NEED_VOTES) {
		$median = get_median($votes);
		?><a class="vote text-<?= vote_bar_name($median) ?>" href="/<?= $element ?>/<?= $id ?>/votes#votes"><?= vote_name($median) ?></a><?
	} else {
		echo __('answer:no(vote_result)');
	}
	layout_value_field($total_votes >= NEED_VOTES ? SEPARATOR_DOT : FIELDONLY);
	if ($total_votes >= NEED_VOTES) {
		?><a<?
		if ($_REQUEST['ACTION'] === 'votes') {
			?> class="selected"<?
		}
		?> href="/<?= $element ?>/<?= $id ?>/votes#votes"><?= element_name('vote_result') ?></a><?
	} else {
		echo element_name('vote_result');
	}
	?> <span class="light">(<?= __('vote:vote_count', ['CNT' => $total_votes]) ?>)</span><?

	/** @noinspection PhpExpressionResultUnusedInspection */
	!$restart && layout_stop_row();
}

function vote_show_box(): void {
	include_style('votes');

	$element = $_REQUEST['sELEMENT'];
	$id		 = $_REQUEST['sID'];

	/** @noinspection NotOptimalIfConditionsInspection */
	if (time_correctable($element)
	&&	false === ($corrected = get_votes($element, $id, true))
	||	false === ($votes	  = get_votes($element, $id))
	) {
		return;
	}
	if ($show_full = !isset($_SERVER['AJAX'])) {
		layout_open_box($element, 'votes');
	}
	if (empty($votes)) {
		?><div class="block"><?= __('vote:info:no_votes_registered_LINE') ?></div><?
		if ($show_full) {
			layout_close_box();
		}
		return;
	}
	if (!empty($corrected)) {
		?><div id="corrected-votes"><?
		layout_open_box_header();
		$median = get_median($corrected);
		echo __C('vote:field:vote_result_corrected') ?>: <?
		?>: <span class="vote text-<?= vote_bar_name($median) ?>"><?= vote_name($median) ?></span><?
		layout_continue_box_header();

		?><span<?
		?> onclick="swapdisplay('all-votes', 'corrected-votes');"<?
		?> class="unhideanchor"><?= __('votes:all') ?></span><?

		layout_close_box_header();

		?><table class="regular fw middle votes"><?

		for ($vote_id = MAX_VOTEID; $vote_id >= 1; --$vote_id) {
			?><tr><td><?= vote_name($vote_id) ?></td><?
			?><td class="right"><?
			if (isset($corrected[$vote_id])) {
				echo round($corrected[$vote_id],1);
			}
			?></td><td class="right"><?
			if (isset($corrected[$vote_id])) {
				if (!$corrected['']) {
					mail_log('corrected total index does not exist', get_defined_vars());
				}
				$percentage = 100 * $corrected[$vote_id] / ($corrected[''] ?: 1);
				if ($rounded_percentage = round($percentage)) {
					echo round($rounded_percentage) ?>%<?
				}
			} else {
				$percentage = false;
			}
			?></td><td style="width: 85%;"><?
			if (false !== $percentage) {
				?><div class="<?= vote_bar_name($vote_id) ?> bar" style="width: <?= $percentage ?>%;"></div><?
			}
			?></td></tr><?
		}
		layout_close_table();

		?></div><?
		?><div id="all-votes" class="hidden"><?
	}
	layout_open_box_header(BOX_HEADER_REPLACEMENT);
	$median = get_median($votes);
	echo Eelement_name('vote_result');
	?>: <span class="vote text-<?= vote_bar_name($median) ?>"><?= vote_name($median) ?></span><?
	if (!empty($corrected)) {
		layout_continue_box_header();
		?><span<?
		?> onclick="swapdisplay('all-votes', 'corrected-votes');"<?
		?> class="unhideanchor"><?= __('votes:time_corrected') ?></span><?
	}
	layout_close_box_header();
	?><table class="regular fw middle votes"><?
	for ($vote_id = MAX_VOTEID; $vote_id >= 1; --$vote_id) {
		?><tr><?
		?><td><?= vote_name($vote_id) ?></td><?
		?><td class="right"><?
		if (isset($votes[$vote_id])) {
			echo $votes[$vote_id];
		}
		?></td><td class="right"><?
		if (isset($votes[$vote_id])) {
			$percentage = 100 * $votes[$vote_id] / $votes[''];
			if ($random_pct = round($percentage)) {
				echo round($random_pct) ?>%<?
			}
		} else {
			$percentage = false;
		}
		?></td><td style="width: 85%;"><?
		if ($percentage !== false) {
			?><div class="<?= vote_bar_name($vote_id) ?> bar" style="width: <?= $percentage ?>%;"></div><?
		}
		?></td></tr><?
	}
	layout_close_table();
	if (!empty($corrected)) {
		?></div><?
	}
	if ($show_full) {
		layout_close_box();
	}
}
