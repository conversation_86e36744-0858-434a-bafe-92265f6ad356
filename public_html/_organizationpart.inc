<?php

require_once '_layout.inc';

function show_organization_archive(array $organization): void {
	require_once '_partylist.inc';

	$partylist = new _partylist;
	if (ROBOT) {
		$partylist->show_vevent = true;
	}
	$partylist->show_stars =
	$partylist->show_camera =
	$partylist->show_city =
	$partylist->show_location =
	$partylist->show_people =
	$partylist->show_buddy_hearts =
	$partylist->hide_separators = true;

	if ($organization['ORGANIZATIONID'] === 3776) {
		$partylist->show_date_headers = true;
	} else {
		$partylist->show_date = true;
	}

	$partylist->by_organization($organization['ORGANIZATIONID']);
	if (!have_admin()) {
		$partylist->not_cancelled_nor_moved();
	}
	$partylist->select_past();
	$partylist->order_reverse_chronologically();
	if (!$partylist->query()) {
		return;
	}
	layout_open_box('organization','archive');
	layout_box_header(escape_utf8($organization['NAME']).' '.MIDDLE_DOT_ENTITY.' '.__('header:past_parties'));
	$partylist->display();
	layout_close_box();
}
