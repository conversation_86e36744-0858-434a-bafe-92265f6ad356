<?php

# safest to include all from previous party.inc
require_once '_buddies.inc';
require_once '_buddybubble.inc';
require_once '_citylist.inc';
require_once '_connect.inc';
require_once '_contactstars.inc';
require_once '_countries.inc';
require_once '_favourite.inc';
require_once '_genrelist.inc';
require_once '_lineup.inc';
require_once '_okpartyforphotos.inc';
require_once '_partylist.inc';
require_once '_photographers.inc';
require_once '_ticket.inc';
require_once '_vote.inc';
require_once '_party.inc';
require_once '_reportlist.inc';
require_once '_site.inc';
require_once '_userlist.inc';

function preamble() {
	switch ($_REQUEST['ACTION']) {
	case 'day':
		if ($_REQUEST['subID']
		&&	(	!$_REQUEST['ssID']
			||	!$_REQUEST['sssID'])
		&&	($stamp = db_single(null, 'SELECT UNIX_TIMESTAMP(FROM_DAYS('.$_REQUEST['subID'].'))'))
		) {
			[$year, $month, $day] = _getdate($stamp);
			moved_permanently("/agenda/day/$year/$month/$day");
		}
		break;

	case 'nowandsoon':
		moved_permanently(str_replace('/nowandsoon', '/now-and-soon', $_SERVER['REQUEST_URI']));

	case 'outdoor':
		global $__year;
		see_other("/agenda/{$_REQUEST['ACTION']}/$__year");

	case 'festival':
		moved_permanently(str_replace('/festival', '/festivals', $_SERVER['REQUEST_URI']));

	case 'favourites':
		if (!have_user()) {
			http_response_code(400);
			exit;
		}
		break;

	case 'genre':
		if ($_REQUEST['subID']
		&&	($genre = get_genres($_REQUEST['subID']))
		&&	($genre_for_url = genre_for_url($genre))
		) {
			moved_permanently("/agenda/genre/$genre_for_url");
		}
		if (!($genre_in_url = $_REQUEST['SUBACTION'] ?: ($_SERVER['eDATA'] ?? null))) {
			moved_permanently('/agenda/genres');
		}
		global $current_genre;
		if ($genre_in_url === 'favourites') {
			ob_start();
			echo element_plural_name('favourite_genre');
			?> <?=
				element_plural_name('event')
			?> &amp; <?
			echo element_plural_name('party');

			$current_genre = [user_favourite_genres(CURRENTUSERID), null, ob_get_clean()];
		} elseif (
			(require_once '_genres.inc')
		&&	([$gid, $genre_name, $redirect_to] = get_genre_from_genre_in_url($genre_in_url))
		) {
			if ($redirect_to) {
				see_other("/agenda/genre/$redirect_to");
			}
			ob_start();
			echo escape_utf8($genre_name);
			?> <?=
				element_plural_name('event')
			?> &amp; <?
			echo element_plural_name('party');
			global $current_genre;
			$current_genre = [$gid, $genre_name, ob_get_clean()];
			include_meta('description', __('metad:party:genre_list', ['GENRE' => $genre_name]));
		} else {
			see_other('/agenda/genres');
		}
		break;

	case 'now-and-soon':
		if (($cityid = $_REQUEST['subID'])
		&&	!get_element_title('city', $cityid)
		) {
			see_other('/agenda/now-and-soon');
		}
		break;

	default:
		if (!$_REQUEST['ACTION']
		||	 $_REQUEST['subID']
		) {
			break;
		}
		require_once '_specialday.inc';
		if ($special_day = get_special_days($_REQUEST['ACTION'])) {
			global $__year;
			$use_year = $__year;
			if (($info = get_special_day_start_and_stop($special_day, $use_year))
			&&	CURRENTSTAMP > $info['stop']
			) {
				++$use_year;
			}
			see_other("/agenda/{$special_day}/$use_year");
		}
		if (($genre_in_url = $_REQUEST['ACTION'])
		&&	([$gid /*, $genre_name, $redirect_to */] = get_genre_from_genre_in_url($genre_in_url))
		&&	$gid
		) {
			# Old links on /agenda/<genre> should be redirected to /agenda/genre/<genre>
			moved_permanently(str_replace('/agenda/', '/agenda/genre/', $_SERVER['REQUEST_URI']));
		}

		break;
	}
}

function display_header(): void {
	if ($_REQUEST['ACTION'] === 'day'
	&&	($year = $_REQUEST['subID'])
	&&	($month = $_REQUEST['ssID'])
	&&	($day = $_REQUEST['sssID'])
	) {
		require_once '_feed.inc';
		$date = sprintf('%04d%02d%02d',$year,$month,$day);
		?><link rel="alternate" type="application/atom+xml" title="<?= get_feed_title('agenda','date',$date);
		?>" href="/feed/agenda/date/<?= $date; ?>.xml" /><?
		?><link rel="alternate" type="application/xml" href="/date.<?= $date ?>.xml" /><?
	}
}

function display_title() {
	switch ($_REQUEST['ACTION']) {
	case 'day':
		$day   = $_REQUEST['sssID'];
		$month = $_REQUEST['ssID'];
		$year  = $_REQUEST['subID'];
		if (!$day || !$month || !$year) {
			not_found	();
			return;
		}
		[,,,,,, $weekday] = _getdate(gmmktime(0, 0, 0, $month, $day, $year), 'UTC');

		echo __('element:agenda_of_DATE',[
			'DAY'		=> $day,
			'MONTHNAME'	=> _month_name($month),
			'YEAR'		=> $year,
			'WEEKDAYNAME'	=> weekday_name($weekday),
			'DAYNAME'	=> get_day_name($year, $month, $day)
		]);
		break;

	case 'festivals':
		echo __('title:agenda:festivals');
		?> <?= MIDDLE_DOT_ENTITY ?> <?
		echo element_name('festival_agenda');
		include_meta('description', __('metad:party:festivals'));
		break;

	case 'cancelled':
		echo __('title:agenda:cancelled');
		?> <?= MIDDLE_DOT_ENTITY ?> <?
		echo element_name('festival_agenda');
		include_meta('description', __('metad:party:cancelled'));
		break;

	case 'now-and-soon':
		echo __('section:now_and_soon');
		if (($cityid = $_REQUEST['subID'])
		&&	($name = get_element_title('city', $cityid))
		) {
			?> <?= MIDDLE_DOT_ENTITY ?> <?
			echo escape_utf8($name);
		}
		?> <?= MIDDLE_DOT_ENTITY ?> <?
		echo element_name('party_agenda');
		break;

	case 'archive':
		echo element_name('party_agenda');
		?> <?= MIDDLE_DOT_ENTITY ?> <?
		if ($_REQUEST['subID']) {
			if ($_REQUEST['ssID']) {
				echo _month_name($_REQUEST['ssID']);
				?> <?
			}
			echo $_REQUEST['subID'];
		}  else {
			echo element_name('archive');
		}
		break;

	case 'genre':
		global $current_genre;
		if (empty($current_genre)) {
			mail_log('empty current_genre');
			return;
		}
		[/* $gid */, /* $genre_name */, $genre_title] = $current_genre;
		echo $genre_title;
		break;

	case 'going':
		echo element_name('party_agenda').' '.MIDDLE_DOT_ENTITY.' '.__('action:tell');
		break;

	case 'livestream':
		echo element_name('livestream_agenda');
		break;

	case 'new':
		echo element_plural_name('new_event');
		break;

	case null:
		echo $_REQUEST['sID']
		?	__('element:agenda_of_MONTH', ['MONTHNAME' => _month_name($_REQUEST['subID']), 'YEAR' => $_REQUEST['sID']])
		:	element_name('party_agenda');
		break;

	default:
		require_once '_specialday.inc';
		if (get_special_days($_REQUEST['ACTION'])) {
			global $__agenda_title, $__year;
			echo $__agenda_title = __('day_name:'.($special_day = str_replace('-', '_', $_REQUEST['ACTION'])));
			$year = $_REQUEST['subID'] ?: $__year;
			echo ' ', $year, ' ', element_name('party_agenda');

			global $metadesc;

			include_meta('description',
				$metadesc =
				str_replace(
					'%DURING_DAYNAME%',
					__('date:during:'.$special_day),
					__('metad:party:specialday', KEEP_EMPTY_KEYWORDS, [
						'YEAR' => $year,
					]
				))
			);

			break;
		}
		break;
	}
}

function display_body(): void {
	switch ($_REQUEST['ACTION']) {
	case null:
	case 'genre':
	case 'genres':
	case 'allfuture':
	case 'christmas':
	case 'easter':
	case 'new-years-eve':
	case 'new-years-day':
	case 'kings-night-and-day':
	case 'liberation-day':
	case 'halloween':
	case 'carnival':
	case 'ascension-day':
	case 'pentecost':
	default:				agenda_display_overview(); return;
	case 'favourites':		agenda_display_favourites(); return;
	case 'placeholders':	agenda_display_placeholders(); return;
	case 'photographers':	agenda_display_photographers(); return;
	case 'comments':		agenda_display_all_comments(); return;
	case 'search':			agenda_display_search(); return;
	case 'buddies':			agenda_display_buddies(); return;
	case 'favourites':		agenda_display_favourites(); return;
	case 'photographers':	agenda_display_photographers(); return;
	case 'needupdate':		require_once '_needupdates.inc'; show_agenda_needupdates(); return;
	case 'day':				agenda_display_day(); return;
	case 'going':			agenda_display_going(); return;
	case 'now-and-soon':	agenda_display_now_and_soon(); return;
	case 'festivals':		agenda_display_festivals(); return;
	case 'livestreams':		agenda_display_livestreams(); return;
	case 'cancelled':		agenda_display_cancelled(); return;
	case 'mastergenre':		agenda_display_mastergenre(); return;
	case 'new':				agenda_display_new(); return;
	case 'archive':			if ($_REQUEST['subID']) {
								if ($_REQUEST['ssID']) {
									agenda_display_archived_month();
								}
							} else {
								agenda_display_archive();
							}
							return;
	}
}

function agenda_display_archived_month() {
	if (!($year = $_REQUEST['subID'])
	||	!($month = $_REQUEST['ssID'])
	) {
		not_found();
		return;
	}

	layout_show_section_header();

	$partylist = new _partylist;

	$partylist->in_year_and_month($year,$month);

	$partylist->order_chronologically_but_notime();
	$partylist->show_people = true;
	$partylist->show_city = true;
	$partylist->hide_day_prefix = true;
	$partylist->show_stars = true;
	$partylist->show_location = true;
	$partylist->show_date_headers = true;
	$partylist->show_camera = true;
	$partylist->query();
	main_menu();
	$partylist->display();
}

function agenda_display_new() {
	layout_show_section_header();

	main_menu();

	layout_open_box();
	layout_box_header(Eelement_plural_name('new_event'));

	$partylist = new _partylist;
	$partylist->new();
	$partylist->show_city = true;
	$partylist->hide_day_prefix = true;
	$partylist->show_stars = true;
	$partylist->show_location = true;
	$partylist->hide_ads = true;
	$partylist->query();
	$partylist->display();

	layout_close_box();
}

function agenda_display_archive() {
	layout_show_section_header(Eelement_name('party_agenda').' '.MIDDLE_DOT_ENTITY.' '.element_name('archive'));

	require_once '_itemlist.inc';
	_itemlist_display_archive();
}

function agenda_display_day(): void {
	if (!($year  = require_idnumber($_REQUEST, 'subID'))
	||	!($month = require_idnumber($_REQUEST, 'ssID'))
	||	!($day   = require_idnumber($_REQUEST, 'sssID'))
	||	!require_real_date($year, $month, $day)
	) {
		return;
	}
	$daystamp = gmmktime(0, 0, 0, $month, $day, $year);
	if (!$daystamp) {
		return;
	}
	$daystamp += HOUR_DAY_START * ONE_HOUR;
	$infos = memcached_simpler_array('party','
		(SELECT STAMP_TZI FROM party WHERE STAMP_TZI <  '.$daystamp.'		 ORDER BY STAMP_TZI DESC LIMIT 1)
	UNION	(SELECT STAMP_TZI FROM party WHERE STAMP_TZI >= '.($daystamp + ONE_DAY).' ORDER BY STAMP_TZI  ASC LIMIT 1)
		ORDER BY STAMP_TZI ASC',
		HALF_HOUR
	);

	change_timezone('UTC');
	layout_show_section_header('<h1>'.Eelement_name('party_agenda').' '.MIDDLE_DOT_ENTITY.' '._dateday_get($daystamp).'</h1>');
	change_timezone();

	main_menu();

	show_months();

	show_quickjump($year, $month, $day);

	if ($infos) {
		layout_open_menu();
		change_timezone('UTC');
		foreach ($infos as $i => $stamp) {
			[$tmp_year, $tmp_month, $tmp_day] = _getdate($correctedstamp = offset_party_stamp($stamp));
			if ($stamp > $daystamp) {
				layout_continue_menu();
			}
			layout_open_menuitem();
			?><a class="prty" href="/agenda/day/<?= $tmp_year ?>/<?= $tmp_month ?>/<?= $tmp_day ?>"><?
			if (!$i) {
				?>&larr; <?
			}
			if ($relative_day_name = get_relative_day_name($correctedstamp, TODAYSTAMP_TZI)) {
				echo $relative_day_name; ?>, <?
			}
			_dateday_display($correctedstamp);
			if ($i) {
				?> &rarr;<?
			}
			?></a><?
			layout_close_menuitem();
		}
		change_timezone();
		layout_close_menu();
	}
	$partylist = new _partylist;
#	$partylist->include_all = true;
	$partylist->hide_day_prefix = true;
	$partylist->show_vevent = true;
	$partylist->select_day($year, $month, $day);
	$partylist->only_for_current_country();
	$partylist->show_people = true;
	$partylist->show_city = true;
	$partylist->show_location = true;
	$partylist->show_stars = true;
	$partylist->show_date_headers = true;
	$partylist->show_buddies = true;
	$partylist->show_contests = true;
	$partylist->show_filter = true;
	if ($daystamp < TODAYSTAMP) {
		$partylist->show_camera = true;
	} else {
		$partylist->show_camera_present = true;
	}
	if ($partylist->query()) {
		$partylist->show_lineups_form = true;
		$partylist->display();
	}
}

function agenda_display_going(): void {
	if (!require_user()) {
		return;
	}
	layout_show_section_header();
	require_once '_goingprocess.inc';
	show_goings();
}

function agenda_display_now_and_soon(): void {
	require_once '_nowandsoon.inc';
	show_now_and_soon();
}

function agenda_display_festivals(): void {
	layout_show_section_header(Eelement_name('party_agenda').' '.MIDDLE_DOT_ENTITY.' '.__('title:agenda:festivals').' '.MIDDLE_DOT_ENTITY.' '.$_REQUEST['subID']);

	main_menu();

	if (!($years = memcached_simpler_array('party', "
		SELECT DISTINCT CAST(FROM_UNIXTIME(STAMP_TZI, '%Y') AS int)
		FROM party_db.party
		ORDER BY STAMP_TZI DESC",
		TEN_MINUTES
	))) {
		return;
	}
	if ($_REQUEST['subID']
	&&	!in_array($_REQUEST['subID'], $years, true)
	) {
		not_found();
		return;
	}
	if ($years) {
		?><div class="large block"><label><?= element_name('year') ?>: <?
		?><select name="YEAR" onchange="location.href='/agenda/festivals' + (this.value ? '/' + this.value : '');"><?
		?><option></option><?
		foreach ($years as $year) {
			?><option<?
			if ($_REQUEST['subID'] === $year) {
				?> selected<?
			}
			?>><?= $year ?></option><?
		}
		?></select><?
		?></label><?
		?></div><?
	}

	$partylist = new _partylist();
	$partylist->show_date_headers	= true;
	$partylist->show_people			= true;
	$partylist->show_vevent			= true;
	$partylist->show_city			= true;
	$partylist->show_buddies		= true;
	$partylist->show_location		= true;
	$partylist->show_stars			= true;
	$partylist->show_camera_present = true;
	$partylist->hide_day_prefix		= true;
	$partylist->show_filter			= true;
	$partylist->show_lineups_form	= true;
	$partylist->order_chronologically_but_notime();
	#$partylist->only_for_current_country();
	#$partylist->with_location_of_types(array('outdoor','beach'));
	$partylist->only_festivals();
	if ($_REQUEST['subID']) {
		$partylist->in_year($_REQUEST['subID']);
	} else {
		$partylist->select_future();
	}
	if (!$partylist->query()) {
		return;
	}
	if ($partylist->have_rows()) {
		$partylist->display();
	}
}

function agenda_display_cancelled(): void {
	layout_show_section_header(Eelement_plural_name('cancelled_event'));

	main_menu();

	$partylist = new _partylist();
	$partylist->show_date_headers = true;
	$partylist->show_people = true;
	$partylist->show_vevent = true;
	$partylist->show_city = true;
	$partylist->show_buddies = true;
	$partylist->show_location = true;
	$partylist->show_stars = true;
	$partylist->show_camera_present = true;
	$partylist->hide_day_prefix = true;
	$partylist->order_chronologically_but_notime();
	$partylist->cancelled = true;
	$partylist->select_future();
	$partylist->only_for_current_country();
	if (!$partylist->query()) {
		return;
	}
	$partylist->show_filter = true;
	$partylist->show_lineups_form = true;
	if ($partylist->have_rows()) {
		$partylist->display();
	}
}
function agenda_display_buddies(): void {
	?>Vriendenagenda<?
	layout_close_section_header();
	main_menu();
	if (!have_buddies()) {
		return;
	}
	$partylist = new _partylist;
	$partylist->show_date_headers = true;
	$partylist->show_people = true;
	$partylist->show_city = true;
	$partylist->show_location = true;
	$partylist->show_stars = true;
	$partylist->show_buddies = true;
	$partylist->show_camera_present = true;
	$partylist->order_chronologically_but_notime();
	$partylist->only_buddies();
	$partylist->select_future();
	$partylist->show_filter = true;
	if (!$partylist->query()) {
		return;
	}
	if ($partylist->have_rows()) {
		$partylist->show_lineups_form = true;
		$partylist->display();
	} else {
		?><p>Niets gevonden.</p><?
	}
}

function agenda_display_all_comments(): void {
	layout_show_section_header();

	require_once '_allcomments.inc';
	show_all_comments('party');
}

function agenda_display_photographers(): void {
	layout_show_section_header(Eelement_name('party_agenda').' '.MIDDLE_DOT_ENTITY.' '.element_name('presence_of_photographers'));
	main_menu();

	$partylist = new _partylist;
	$partylist->show_date_headers = true;
	$partylist->show_people = true;
	$partylist->show_city = true;
	$partylist->show_location = true;
	$partylist->show_stars = true;
	$partylist->show_buddies = true;
	$partylist->show_camera_present = true;
	$partylist->order_chronologically_but_notime();
	$partylist->only_photographers();
	$partylist->select_future();
	if (!$partylist->query()) {
		return;
	}
	if ($partylist->have_rows()) {
		$partylist->display();
	} else {
		?><p>Niets gevonden.</p><?
	}
}

function agenda_display_placeholders(): void {
	layout_show_section_header(Eelement_name('party_agenda').' '.MIDDLE_DOT_ENTITY.' '.element_plural_name('placeholder'));
	main_menu();

	$partylist = new _partylist;
	$partylist->show_date_headers = true;
	$partylist->show_people = true;
	$partylist->show_city = true;
	$partylist->show_location = true;
	$partylist->show_stars = true;
	$partylist->show_buddies = true;
	$partylist->show_camera_present = true;
	$partylist->order_chronologically_but_notime();
	$partylist->only_placeholders();
	$partylist->select_future();
	if (!$partylist->query()) {
		return;
	}
	if ($partylist->have_rows()) {
		$partylist->display();
	}
}

function agenda_display_search(): void {
	require_once '_locationtype.inc';
	require_once '_countries.inc';
	require_once '_citylist.inc';

	layout_show_section_header(Eelement_name('party_agenda').' '.MIDDLE_DOT_ENTITY.' '.__C('action:search'));

	$initial_form = !str_contains($_SERVER['REQUEST_URI'], '?');

	$countries = get_nonempty_countries();
	include_js('js/form/partysearch');

	?><form<?
	?> accept-charset="utf-8"<?
	?> onsubmit="return Pf.submitPartySearch(this)"<?
	?> method="get"<?
	?> action="/agenda/search#results"><?

	?><input type="hidden" name="enc" value="&#129392;" /><?

	require_once '_supersearch.inc';
	show_search_header();

	clean_utf8($_REQUEST, 'NAME');

	layout_open_box('white');

	?><dl class="srchdl"><?
	?><dt><?
		$checked = isset($_REQUEST['CB_NAME']);
		?><label class="<? if (!$checked) echo 'not-' ?>hilited"><?
		show_input([
			'class'		=> 'upLite',
			'name'		=> 'CB_NAME',
			'type'		=> 'checkbox',
			'onclick'	=> "setdisplay('namepart',this.checked); this.form['NAME'].value = ''",
			'value'		=> '1',
			'checked'	=> $checked
		]);
		?> <?=
		Eelement_name('name')
	?></dt><?
	?><dd<?
	if (!$checked) {
		?> class="hidden"<?
	}
	?> id="namepart"><?
		$name = have_something_trim($_REQUEST, 'NAME', utf8: true);
		show_input([
			'type'			=> 'search',
			'name'			=> 'NAME',
			'class'			=> 'fw',
			'id'			=> 'partyname',
			'autofocus'		=> true,
			'value_utf8'	=> $name ?: null,
			'placeholder'	=> element_name('event_name'),
		]);
	?></dd><?

	$currentcity = get_current_city();
	if ($countries) {
		?><dt><?
			// COUNTRY
			$checked = isset($_REQUEST['CB_COUNTRY']);
			?><label class="<? if (!$checked) echo 'not-' ?>hilited"><?
			show_input([
				'class'		=> 'upLite',
				'name'		=> 'CB_COUNTRY',
				'type'		=> 'checkbox',
				'onclick'	=> "setdisplay('countrypart',this.checked);Pf.enable(this.form.FILTERCOUNTRYID,this.checked)",
				'value'		=> 1,
				'checked'	=> $checked
			]);
			?> <?
			echo Eelement_name('country') ?></label><?
		?></dt><?
		?><dd><?
			$countryid = have_idnumber($_REQUEST, 'FILTERCOUNTRYID') ?: ($currentcity['COUNTRYID'] ?? 0);
			?><select<?
			if (!$checked) {
				?> class="hidden"<?
				?> disabled="disabled"<?
			}
			?> id="countrypart"<?
			?> name="FILTERCOUNTRYID"<?
			?> onchange="changeFilterCountry(this)"<?
			?>><?
			foreach ($countries as $tmpcountryid => $name) {
				?><option<?
				if ($countryid === $tmpcountryid) {
					?> selected<?
				}
				?> value="<?= $tmpcountryid ?>"><?= escape_specials($name); ?></option><?
			}
			?></select><?
		?></dd><?
	}
	?><dt><?
		// CITY
		$checked = isset($_REQUEST['CB_CITY']);
		?><label class="<? if (!$checked) echo 'not-' ?>hilited"><?
		show_input([
			'class'		=> 'upLite',
			'name'		=> 'CB_CITY',
			'type'		=> 'checkbox',
			'onclick'	=> "setdisplay('citypart',this.checked);".
					   "Pf.enable([this.form.COUNTRYID, this.form.CITYID, this.form.RADIUS], this.checked)",
			'value'		=> 1,
			'checked'	=> $checked
		]);
		?> <?
		echo Eelement_name('region') ?></label><?
	?></dt><?
	?><dd><?
		?><div id="citypart"<?
		if (!$checked) {
			?> class="hidden"<?
		}
		?>><?
		display_dynamic_city_pair(
			cityid:		have_idnumber($_REQUEST,'CITYID') ?: (!have_idnumber($_REQUEST, 'COUNTRYID') && $currentcity ? $currentcity['CITYID'] : 0),
			countryid:	have_idnumber($_REQUEST, 'COUNTRYID'),
			flags:		SHOW_RADIUS | ($checked ? 0 : DYNLOC_DISABLED),
			radius:		have_idnumber($_REQUEST, 'RADIUS') ?: 50
		);
		?></div><?
	?></dd><?
	?><dt><?
		// TYPE
		$checked = isset($_REQUEST['CB_TYPE']);
		?><label class="<? if (!$checked) echo 'not-' ?>hilited"><?
		show_input([
			'class'		=> 'upLite',
			'name'		=> 'CB_TYPE',
			'type'		=> 'checkbox',
			'onclick'	=> "setdisplay('typepart',this.checked)",
			'value'		=> 1,
			'checked'	=> $checked
		]);
		?> <?
		echo Eelement_name('location_type') ?></label><?
	?></dt><?
	?><dd><?
		?><div id="typepart"<?
		if (!$checked) {
			?> class="hidden"<?
		}
		?>><?
		show_locationtype_checkboxes(
			isset($_REQUEST['LOCATIONTYPE'])
		&&	is_array($_REQUEST['LOCATIONTYPE'])
		?	$_REQUEST['LOCATIONTYPE']
		:	[]
		);

		$checked = isset($_REQUEST['EROTIC']);
		?><label class="tmrgn cbi <?
		if (!$checked) {
			?>not-<?
		}
		?>bold-hilited"><?
		show_input([
			'type'		=> 'checkbox',
			'class'		=> 'upLite',
			'name'		=> 'EROTIC',
			'checked'	=> $checked,
			'value'		=> 1
		]);
		?> <?= __('attrib:erotic') ?></label><?
		?></div><?
	?></dd><?
	if (have_admin('party')) {
		?><dt><?
		$checked = isset($_REQUEST['CB_SOLDOUT']);
		?><label class="<? if (!$checked) echo 'not-' ?>hilited"><?
		show_input([
			'class'		=> 'upLite',
			'name'		=> 'CB_SOLDOUT',
			'type'		=> 'checkbox',
			'onclick'	=> "setdisplay('soldout',this.checked);Pf.enable(this.form.SOLDOUT,this.checked)",
			'value'		=> 1,
			'checked'	=> $checked
		]);
		?> <?
		echo __C('status:sold_out') ?></label><?
		?></dt><?
		?><dd><?
		?><div id="soldout"<?
		if (!$checked) {
			?> class="hidden"<?
		}
		?>><select name="SOLDOUT"<?
		if (!$checked) {
			?> disabled="disabled"<?
		}
		?>><?
			?><option value="1"><?= __('answer:yes') ?></option><?
			?><option<?
			if (isset($_REQUEST['SOLDOUT'])
			&&	!$_REQUEST['SOLDOUT']
			) {
				?> selected="selected"<?
			}
			?> value="0"><?= __('answer:no') ?></option><?
		?></select></div><?
		?></dd><?
	}
	?><dt><?
		// PRICE
		$checked = isset($_REQUEST['CB_PRICE']);
		?><label class="<? if (!$checked) echo 'not-' ?>hilited"><?
		show_input([
			'class'		=> 'upLite',
			'name'		=> 'CB_PRICE',
			'type'		=> 'checkbox',
			'onclick'	=> "setdisplay('price',this.checked);Pf.enable([this.form.MAXPRICE,this.form.MINPRICE],this.checked)",
			'value'		=> 1,
			'checked'	=> $checked
		]);
		?> <?
		echo Eelement_name('price') ?></label><?
	?></dt><?
	?><dd><?
		?><div id="price"<?
		if ($disabled = !$checked) {
			?> class="hidden"<?
			$disabled = ' disabled="disabled"';
		}
		?>><?= __('attrib:at_least') ?> <select name="MINPRICE" onchange="changeMinPrice(this)"<?= $disabled ?>><?
		?><option value="0"><?= __('cost:free') ?></option><?
		$selected = have_idnumber($_REQUEST,'MINPRICE');
		for ($i = 10; $i <= 100; $i += 10) {
			?><option<?
			if ($i * 100 === $selected) {
				?> selected="selected"<?
			}
			?> value="<?= $i * 100 ?>"><?= $i ?></option><?
		}
		?></select>, <?= __('attrib:at_most') ?> <select name="MAXPRICE"<?= $disabled ?>><?
		?><option value="0"><?= __('cost:free') ?></option><?
		$selected = have_idnumber($_REQUEST,'MAXPRICE');
		for ($i = 10; $i <= 100; $i += 10) {
			?><option<?
			if ($i * 100 === $selected) {
				?> selected="selected"<?
			}
			?> value="<?= $i * 100 ?>"><?= $i ?></option><?
		}
		?><option value=""><?= element_name('all') ?></option><?
		?></select></div><?
	?></dd><?
	?><dt><?
		// WHEN
		$checked = $when_checked = isset($_REQUEST['CB_WHEN']) || $initial_form;
		?><label class="<? if (!$checked) echo 'not-' ?>hilited"><?
		show_input([
			'class'		=> 'upLite',
			'name'		=> 'CB_WHEN',
			'type'		=> 'checkbox',
			'onclick'	=> "setdisplay('when',this.checked);Pf.enable([this.form.WHEN,this.form.WHENDATE,this.form.WHENTODAY,this.form.WHENYEAR],this.checked)",
			'value'		=> 1,
			'checked'	=> $checked
		]);
		?> <?
		echo __C('nowandsoon:when') ?></label><?
	?></dt><?
	?><dd><?
		?><div id="when"<?
		if (!$checked) {
			?> class="hidden"<?
		}
		?>><?
			global $__year;
			if ($info = db_single_array('party', 'SELECT MIN(STAMP_TZI), MAX(STAMP_TZI) FROM party_db.party')) {
				[$start_year] = _getdate($info[0]);
				[ $last_year] = _getdate($info[1]);
			} else {
				$start_year = $__year - 15;
				 $last_year = $__year +  2;
			}
			$when = getifset($_REQUEST, 'WHEN');
			$checked = (!$when || $when === 'on' || $when === 'all') && !$initial_form;
			?><label class="<? if (!$checked) echo 'not-' ?>hilited"><?
				show_input([
					'type'		=> 'radio',
					'class'		=> 'upLite',
					'name'		=> 'WHEN',
					'value'		=> 'all',
					'checked'	=> $checked,
					'disabled'	=> !$when_checked,
					'onclick'	=> "Pf.clickWhen(this)"
				]);
				?> <?
				echo element_name('all');
			?></label><?
			?><br /><?
			$checked = $when === 'future' || $initial_form;
			?><label class="<? if (!$checked) echo 'not-' ?>hilited"><?
				show_input([
					'type'		=> 'radio',
					'class'		=> 'upLite',
					'name'		=> 'WHEN',
					'value'		=> 'future',
					'checked'	=> $checked,
					'disabled'	=> !$when_checked,
					'onclick'	=> "Pf.clickWhen(this)"
				]);
				?> <?
				echo __('date:future');
			?></label><?
			?><br /><?
 			$checked = $when === 'past';
			?><label class="<? if (!$checked) echo 'not-' ?>hilited"><?
				show_input([
					'type'		=> 'radio',
					'class'		=> 'upLite',
					'name'		=> 'WHEN',
					'value'		=> 'past',
					'checked'	=> $checked,
					'disabled'	=> !$when_checked,
					'onclick'	=> "Pf.clickWhen(this)"
				]);
				?> <?
				?> <? echo __('date:past');
			?></label><?
			?><br /><?
 			$checked = $when === 'today';
			?><label class="<? if (!$checked) echo 'not-' ?>hilited"><?
				show_input([
					'type'		=> 'radio',
					'class'		=> 'upLite',
					'name'		=> 'WHEN',
					'value'		=> 'today',
					'checked'	=> $checked,
					'disabled'	=> !$when_checked,
					'onclick'	=> "Pf.clickWhen(this)"
				]);
				?> <?
				?> <? echo __('date:today');
			?></label><?
			?><br /><?
 			$checked = $when === 'year';
			?><label class="<? if (!$checked) echo 'not-' ?>hilited"><?
				show_input([
					'type'		=> 'radio',
					'class'		=> 'upLite',
					'name'		=> 'WHEN',
					'value'		=> 'year',
					'checked'	=> $checked,
					'disabled'	=> !$when_checked,
					'onclick'	=> "Pf.clickWhen(this)"
				]);
				?> <?
				?> <? echo element_name('year');
			?></label><?
			?><div id="whenyear" class="ib lpad<?
			if (!$checked) {
				?> hidden<?
			}
			?>"><?
			?><select<?
			if (!$checked) {
				?> disabled="disabled"<?
			}
			?> name="YEAR"><?
			$year = have_idnumber($_REQUEST,'YEAR') ?: $__year;
			for (	$year_cnt  = $start_year;
				$year_cnt <= $last_year;
				++$year_cnt
			) {
				?><option<?
				if ($year === $year_cnt) {
					?> selected<?
				}
				?> value="<?= $year_cnt ?>"><?= $year_cnt ?></option><?
			}
			?></select><?
			?></div><?
			?><br /><?
 			$checked = $when === 'date';
			?><label class="<? if (!$checked) echo 'not-' ?>hilited"><?
				show_input([
					'type'		=> 'radio',
					'class'		=> 'upLite',
					'name'		=> 'WHEN',
					'value'		=> 'date',
					'checked'	=> $checked,
					'disabled'	=> !$when_checked,
					'onclick'	=> "Pf.clickWhen(this)"
				]);
				?> <? echo element_plural_name('date');
			?></label><?
			?><div id="whendate" class="ib lpad<?
			if (!$checked) {
				?> hidden<?
			}
			?>"><?
			_date_display_select(
				prefix:		'START_',
				select_year:	have_idnumber($_REQUEST,'START_YEAR')  ?: $__year,
				select_month:	have_idnumber($_REQUEST,'START_MONTH') ?: 0,
				select_day:	have_idnumber($_REQUEST,'START_DAY')	  ?: 0,
				start_year:	$start_year,
				end_year:	 $last_year,
#				empty_value:	null,
#				onchange:	null,
#				required:	false,
#				max_year:	null,
				disabled:	!$checked

			); ?> <?= __('time:till') ?> <?

			_date_display_select(
				prefix:		'STOP_',
				select_year:	have_idnumber($_REQUEST, 'STOP_YEAR')  ?: $__year,
				select_month:	have_idnumber($_REQUEST, 'STOP_MONTH') ?: 0,
				select_day:	have_idnumber($_REQUEST, 'STOP_DAY')   ?: 0,
				start_year:	$start_year,
				end_year:	$last_year,
#				empty_value:	null,
#				onchange:	null,
#				required:	false,
#				max_year:	null,
				disabled:	!$checked
			);
			?></div><?
		?></div><?
	?></dd><?
	?><dt><?
		$checked = isset($_REQUEST['CB_DAYTIME']);
		?><label class="<? if (!$checked) echo 'not-' ?>hilited"><?
		show_input([
			'class'		=> 'upLite',
			'name'		=> 'CB_DAYTIME',
			'type'		=> 'checkbox',
			'value'		=> '1',
			'checked'	=> $checked
		]);
		?> <?
		echo Eelement_name('daytime'); ?></label><?
	?></dt><?
	?><dd><?
	?></dd><?
	?><dt><?
		$checked = isset($_REQUEST['CB_NIGHTTIME']);
		?><label class="<? if (!$checked) echo 'not-' ?>hilited"><?
		show_input([
			'class'		=> 'upLite',
			'name'		=> 'CB_NIGHTTIME',
			'type'		=> 'checkbox',
			'value'		=> '1',
			'checked'	=> $checked
		]);
		?> <?
		echo Eelement_name('nighttime'); ?></label><?
	?></dt><?
	?><dd><?
	?></dd><?
	?><dt><?
		$checked = isset($_REQUEST['CB_MINAGE']);
		?><label class="<? if (!$checked) echo 'not-' ?>hilited"><?
		show_input([
			'class'		=> 'upLite',
			'name'		=> 'CB_MINAGE',
			'type'		=> 'checkbox',
			'onclick'	=> "setdisplay('minage',this.checked);Pf.enable([this.form.MINMAX,this.form.MINAGE],this.checked)",
			'value'		=> 1,
			'checked'	=> $checked
		]);
		?> <?
		echo Eelement_name('minimum_age'); ?></label><?
	?></dt><?
	?><dd><?
		if (have_user()
		&&	($user = db_single_assoc('user','
				SELECT BIRTH_DAY,BIRTH_MONTH,BIRTH_YEAR,LDAY
				FROM user
				WHERE USERID='.CURRENTUSERID)
			)
		&&	$user['BIRTH_YEAR']
		) {
			if (!$user['BIRTH_MONTH']) {
				$user['BIRTH_MONTH'] = 1;
			}
			if (!$user['BIRTH_DAY']) {
				$user['BIRTH_DAY'] = 1;
			}
			$userage = compute_age($user['BIRTH_YEAR'], $user['BIRTH_MONTH'], $user['BIRTH_DAY'], CURRENTSTAMP, $user['LDAY']);
		} else {
			$userage = 0;
		}
		?><div id="minage"<?
		if (!$checked) {
			?> class="hidden"<?
		}
		?>><?

		$minmax = getifset($_REQUEST, 'MINMAX');

		?><select name="MINMAX"<?
		if (!$checked) {
			?> disabled="disabled"<?
		}
		?>><?
		?><option<?
		if ($minmax === 'max') {
			?> selected<?
		}
		?> value="max"><?= __('attrib:up_to') ?></option><?
		?><option value="min"<?
		if ($minmax === 'min'
		||	!$minmax
		&&	$userage >= 21
		) {
			?> selected<?
		}
		?>><?= __('attrib:at_least') ?></option><?
		?></select> <?
		?><select name="MINAGE" onchange="Pf.changeMinAge(this)"<?
		if (!$checked) {
			?> disabled<?
		}
		?>><?
		$ages = memcached_boolean_hash('party','SELECT DISTINCT MIN_AGE FROM party_db.party ORDER BY MIN_AGE',ONE_HOUR);
		if ($ages) {
			if ($userage
			&&	!isset($ages[$userage])
			) {
				$ages[$userage] = true;
				ksort($ages);
			}
			$selected_age =
				getifset($_REQUEST,'MINAGE') !== null
			?	$_REQUEST['MINAGE']
			:	$userage;

			foreach ($ages as $age => $true) {
				?><option<?
				if ($selected_age !== false) {
					if ($selected_age !== null) {
						if ($selected_age <= $age) {
							$selected_age = null;
							?> selected="selected"<?
						}
					}
				} elseif ($userage) {
					if ($userage >= 21
					&&	$age === 21
					) {
						?> selected="selected"<?
					}
				} elseif ($age === 16) {
					?> selected="selected"<?
				}
				?> value="<?= $age; ?>"><?= $age; ?></option><?
			}
		}
		?></select></div><?
	?><dd><?
	?><dt><?
		// GIDS
		$checked = isset($_REQUEST['CB_GIDS']);
		?><label class="<? if (!$checked) echo 'not-' ?>hilited"><?
		show_input([
			'class'		=> 'upLite',
			'name'		=> 'CB_GIDS',
			'type'		=> 'checkbox',
			'onclick'	=> "setdisplay('genres',this.checked);Pf.enable(this.form['GID[]'],this.checked)",
			'value'		=> 1,
			'checked'	=> $checked
		]);
		?> <?
		echo Eelement_plural_name('genre'); ?></label><?
	?></dt><?
	?><dd><?
		?><div id="genres"<?
		if (!$checked) {
			?> class="hidden"<?
		}
		?>><?
		$gid = $_REQUEST['GID'] ?? null;
		if (is_string($gid)) {
			mail_log('GID is a string instead of array', $_REQUEST);
			$gid = null;
		}
		show_genre_selection(
			$gid ?: (have_user() ? user_favourite_genres(CURRENTUSERID) : null),
			disabled: !$checked
		);
		?></div><?
	?></dd><?
	?></dl><?

	layout_close_box();
	?><div class="block"><input type="submit" value="<?= __('action:search') ?>" /></div><?
	?></form><?

	if ($initial_form) {
		return;
	}

	db_insert('agendasearch', '
	INSERT INTO agendasearch SET
		STAMP	= UNIX_TIMESTAMP(),
		USERID	= '.CURRENTUSERID.',
		IPBIN	= "'.addslashes(CURRENTIPBIN).'",
		REQUEST = "'.addslashes(json_encode($_REQUEST)).'"'
	);

	$partylist = new _partylist;
	$partylist->limit = 1000;
	$partylist->show_date_headers = true;
	$partylist->show_people = true;
	$partylist->show_city = true;
	$partylist->show_buddies = true;
	$partylist->show_location = true;
	$partylist->show_stars = true;
	$partylist->show_camera_present = true;
	$partylist->show_camera = true;
	$partylist->hide_day_prefix = true;
	$partylist->none_hidden = true;
	if (isset($_REQUEST['CB_DAYTIME'])) {
		$partylist->daytime();
	}
	if (isset($_REQUEST['CB_NIGHTTIME'])) {
		$partylist->nighttime();
	}
	$partylist->order_chronologically_but_notime();
	if (have_something_trim($_REQUEST, 'NAME', utf8: true)) {
		$partylist->with_name($_REQUEST['NAME']);
	}
	if (isset($_REQUEST['EROTIC'])) {
		$partylist->only_erotic();
	}
	if (isset($_REQUEST['CB_TYPE'])) {
		if (isset($_REQUEST['LOCATIONTYPE'])
		&&	is_array($_REQUEST['LOCATIONTYPE'])
		) {
			$partylist->with_location_of_types($_REQUEST['LOCATIONTYPE']);
		}
	}
	if (isset($_REQUEST['CB_GIDS'])) {
		if (!require_number_array($_REQUEST,'GID')) {
			return;
		}
		$partylist->with_styles($_REQUEST['GID']);
	}
	if (isset($_REQUEST['CB_CITY'])) {
		if (!have_idnumber($_REQUEST,'CITYID')
		||	!have_number($_REQUEST,'RADIUS')
		) {
			return;
		}
		$partylist->within_city_radius($_REQUEST['CITYID'],$_REQUEST['RADIUS']);
	}

	if (isset($_REQUEST['CB_WHEN'])) {
		if (isset($_REQUEST['WHEN'])) {
			switch ($_REQUEST['WHEN']) {
			case 'past':
				$partylist->select_past();
				$partylist->order_reverse_chronologically();
				break;
			case 'future':
				$partylist->select_future(0);
				break;
			case 'today':
				[$y,$m,$d] = _getdate();
				$partylist->select_day($y,$m,$d);
				break;
			case 'year':
				if ($year = have_idnumber($_REQUEST,'YEAR')) {
					$partylist->select_year($year);
				}
				break;
			case 'date':
				if (require_date($_REQUEST, 'START_')
				&&	require_date($_REQUEST, 'STOP_')
				) {
					$partylist->select_range($_REQUEST, 'START_', 'STOP_');
				}
				break;
			default:
				$partylist->order_reverse_chronologically();
				break;
			}
		} else {
			if (!isset($_REQUEST['WHENPAST'])
			||	!isset($_REQUEST['WHENFUTURE'])
			) {
				if (isset($_REQUEST['WHENPAST'])) {
					$partylist->select_past();
					$partylist->order_reverse_chronologically();
				} else {
					$partylist->select_future(0);
				}
			} else {
				$partylist->order_reverse_chronologically();
			}
			if (isset($_REQUEST['WHENDATE'])
			&&	require_date($_REQUEST,'START_')
			&&	require_date($_REQUEST,'STOP_')
			) {
				$partylist->select_range($_REQUEST,'START_','STOP_');
			}
		}
	}
	if (isset($_REQUEST['CB_PRICE'])) {
		if (false === require_number($_REQUEST,'MINPRICE')
		||	!require_number_or_empty($_REQUEST,'MAXPRICE')
		) {
			return;
		}
		$partylist->in_price_range($_REQUEST['MINPRICE'],!strlen($_REQUEST['MAXPRICE']) ? null : $_REQUEST['MAXPRICE']);
	}
	if (isset($_REQUEST['CB_MINAGE'])) {
		if (false === require_number($_REQUEST, 'MINAGE')
		||	!require_element($_REQUEST, 'MINMAX', ['min', 'max'], strict: true)
		) {
			return;
		}
		if ($_REQUEST['MINMAX'] === 'min') {
			$partylist->min_minage($_REQUEST['MINAGE']);
		} else {
			$partylist->max_minage($_REQUEST['MINAGE']);
		}
	}
	if (isset($_REQUEST['CB_COUNTRY'])) {
		if (!require_idnumber($_REQUEST, 'FILTERCOUNTRYID')) {
			return;
		}
		$partylist->in_country($_REQUEST['FILTERCOUNTRYID']);
	}
	if (isset($_REQUEST['CB_SOLDOUT'])) {
		$partylist->sold_out(!empty($_REQUEST['SOLDOUT']));
	}
	if (!$partylist->query()) {
		return;
	}
	$nothing = true;
	if ($partylist->have_rows()) {
		if ($partylist->size() === 1000) {
			register_warning('search:warning:too_many_results,only_x_shown_LINE',['X'=>1000]);
		}
	//	$partylist->display_searchresult();
		ob_start();
		$partylist->display();
		$nothing = !ob_get_length();
		ob_end_flush();
	}
	if ($nothing) {
		?><div class="block"><?= __('search:info:nothing_found_LINE') ?></div><?
	}
}
function get_partydays() {
	static $__cached = 0;
	if ($__cached !== 0) {
		return $__cached;
	}
	if ($cached = memcached_get($key = 'partydayscache.'.get_domain())) {
		return $__cached = $cached;
	}
	$stamps = db_simple_hash('party','
		SELECT DISTINCT STAMP_TZI,ENDSTAMP_TZI,DURATION_SECS
		FROM party '.
		join_only_events_for_current_country().'
		WHERE STAMP_TZI>='.(TODAYSTAMP_TZI - 2*24*3600 + HOUR_DAY_START * 3600)
	);
	if (!$stamps) {
		return $__cached = $stamps;
	}
	# need this complexity for multi-day events
	$add_stamp = function(&$res,$stamp) {
#		$stamp -= HOUR_DAY_START * ONE_HOUR;
		static $__done;
		[$y,$m,$d,$hour] = _getdate($stamp);
		if ($hour < HOUR_DAY_START) {
			[$y,$m,$d] = _getdate($stamp = strtotime('-1 day',$stamp));
		}
		if (isset($__done[$y][$m][$d])) {
			return;
		}
		$__done[$y][$m][$d] = true;
		$week_number = date('W',$stamp);
		$daynum = to_days($y,$m,$d);
		$res[$daynum] = [
			'STAMP_TZI'	=> gmmktime(0,0,0,$m,$d,$y),
			'DAYNUM'	=> $daynum,
			'WEEKNUM'	=> $week_number,
#			'DATE'		=> $y.'-'.$m.'-'.$d,
		];
	};
	change_timezone('UTC');
	foreach ($stamps as $stamp => $info) {
		[$endstamp,$duration] = keyval($info);
		if ($duration < ONE_DAY) {
			$add_stamp($res,$stamp);
		} else {
			do {
				$add_stamp($res,$stamp);
				$stamp += strtotime('+1 day',$stamp);
			}
			while ($stamp < $endstamp);
		}
	}
	change_timezone();
	ksort($res);
	memcached_set($key, $res, TEN_MINUTES);
	return $res;
}
function show_quickjump(int $cyear = 0, int $cmonth = 0, int $cday = 0) {
	if (!($partydays = get_partydays())) {
		return;
	}

	change_timezone('UTC');

	$yesterday = strtotime('yesterday'/*.HOUR_DAY_START.':00'*/, TODAYSTAMP_TZI);	# TODAYSTAMP_TZI - 24*3600 + HOUR_DAY_START * ONE_HOUR;
	$tomorrow  = strtotime('tomorrow' /*.HOUR_DAY_START.':00'*/, TODAYSTAMP_TZI);	# TODAYSTAMP_TZI + 24*3600 + HOUR_DAY_START * ONE_HOUR;

	global $__year, $__month, $__day;

	?><nav><?

	if (!SMALL_SCREEN) {
		?><div class="block"><?= __('action(agenda):go_to_(date)') ?>: <?
	}

	$show_placeholder =
		!$cyear
		# ^ overview page
	||	gmmktime(0, 0, 0, $cmonth, $cday, $cyear) < $yesterday;


	?><select<?
	?> onchange="<?
	if ($show_placeholder) {
		?>if(this.selectedIndex)<?
	}
	?>location.href='/agenda/day/' + this.value" name="MONTH"><?
	if ($show_placeholder) {
		?><option value=""><?
		if (SMALL_SCREEN) {
			echo __('action(agenda):go_to_(date)'),': ';
		}
		echo element_name('a_specific_date');
		?></option><?
	}
	$week = 0;
	foreach ($partydays as $partyday) {
		[$year,$month,$day,,,,$weekday] = _getdate($partyday['STAMP_TZI']);

		if ($week !== $partyday['WEEKNUM']) {
			if ($week) {
				?></optgroup><?
			}
			$week = $partyday['WEEKNUM'];
			?><optgroup label="<?= element_name('week') ?> <?
			echo $week;
			if ($__year !== $year) {
				?> <?= MIDDLE_DOT_ENTITY ?> <? echo $year;
			}
			?>"><?
		}
		?><option<?
		if ($cyear
		&&	$year === $cyear
		&&	$month === $cmonth
		&&	$day === $cday
		) {
			?> selected="selected"<?
		}
		?> value="<?= $year; ?>/<?= $month; ?>/<?= $day; ?>"><?
		echo $day;
		?> <?
		echo _month_name($month);
		?> <?= MIDDLE_DOT_ENTITY ?> <?
		if ($partyday['STAMP_TZI'] < TODAYSTAMP_TZI) {
			echo __($partyday['STAMP_TZI'] < $yesterday
			?	'date:daybeforeyesterday'
			:	'date:yesterday'
			);
			?>, <?
		} elseif ($partyday['STAMP_TZI'] < $tomorrow) {
			echo __('date:today') ?>, <?
		}
		echo weekday_name($weekday);
		if ($day_name = get_day_name($year, $month, $day)) {
			?> <?= MIDDLE_DOT_ENTITY ?> <?
			echo $day_name;
		}
		?></option><?
	}
	if ($week) {
		?></optgroup><?
	}
	?></select><?
	if (!$cyear) {
		?> <form<?
		?> accept-charset="utf-8"<?
		?> class="r nowrap"<?
		?> method="get"<?
		?> action="/agenda/search"><?

		?><input type="hidden" name="enc" value="&#129392;" /><?
		?><input type="hidden" name="CB_NAME" value="1" /><?
		?><input type="hidden" name="CB_WHEN" value="1" /><?
		?><input type="hidden" name="WHEN" value="future" /><?
		?><input type="search" name="NAME" /><?
		?> <input type="submit" value="<?= __('action:search') ?>" /><?

		?></form><?
		?><div class="clear"></div><?
	}
	if (!SMALL_SCREEN) {
		?></div><?
	}
	?></nav><?
	change_timezone();
}

function agenda_display_mastergenre(): void {
	if (!require_admin('party')
	||	!($master_genres = db_simple_hash('mastergenre', "
		SELECT MGID, NAME
		FROM party_db.master_genre
		WHERE NAME != 'house'
		ORDER BY NAME"))
	) {
		return;
	}
	?><select onchange="if(this.value) location.href = '/agenda/mastergenre/' + this.value"><?
	?><option></option><?
	foreach ($master_genres as $mgid => $name) {
		?><option<?
		if ($mgid === $_REQUEST['subID']) {
			?> selected<?
		}
		?> value="<?= $mgid ?>"><?= addslashes($name) ?></option><?
	}
	?></select><?

	if (!$_REQUEST['subID']) {
		return;
	}

	$partylist = new _partylist;
	$partylist->select_future();
	$partylist->master_genre($_REQUEST['subID']);
	$partylist->query();
	$partylist->display();
}

function agenda_display_overview(): void {
	require_once '_specialday.inc';
	$current_genre = getifset($GLOBALS, 'current_genre');
	$in_year = $_REQUEST['sID'];
	$in_month = $_REQUEST['subID'];
	if ($in_year) {
		if ($in_month) {
			if ($in_month < 0
			||	$in_month > 12
			) {
				register_error('date:error:incorrect_month_LINE', ['MONTH' => $in_month]);
				not_found();
				return;
			}
			if (false === ($min_max_stamp = memcached_single_array('party', "
				SELECT	CAST(FROM_UNIXTIME(MIN(STAMP), '%Y') AS int) AS MIN_YEAR,
						CAST(FROM_UNIXTIME(MAX(STAMP), '%Y') AS int) AS MAX_YEAR
				FROM party_db.party",
				FIVE_MINUTES))
			) {
				return;
			}
			[$min_year, $max_year] = $min_max_stamp;
			if ($in_year < $min_year
			||	$in_year > $max_year
			) {
				register_error('party:error:incorrect_year_for_overview_LINE', ['YEAR' => $in_month, 'MIN' => $min_year, 'MAX' => $max_year]);
				not_found();
				return;
			}
		} else {
			not_found();
			return;
		}
	} elseif ($_REQUEST['subID']) {
		if (!($special_day = get_special_days($_REQUEST['ACTION']))) {
			register_error('party:error:missing_year_for_overview_LINE');
			not_found();
			return;
		}
	} elseif (
		$_REQUEST['ACTION'] === 'genres'
	||	$_REQUEST['ACTION'] === 'genre'
	) {
		$genres = true;
	} elseif (get_special_days($_REQUEST['ACTION'])) {
		$special = true;
	} elseif (
		$_REQUEST['ACTION']
	&&	!$current_genre
	) {
		not_found();
		return;
	}
	if (!empty($special_day)) {
		global $__year;
		$year = $_REQUEST['subID'] ?: $__year;
	}

	if (!empty($GLOBALS['__agenda_title'])) {
		if (preg_match('"^(.*)\."', $GLOBALS['metadesc'], $match)) {
			layout_section_header("<h1>$match[1]</h1>");
		} else {
			layout_section_header('<h1>'.$GLOBALS['__agenda_title'].' '.element_name('party_agenda').'</h1>');
		}
	} else {
		layout_section_header(
			'<h1>'.Eelement_name('party_agenda').(
				$current_genre
			||	isset($genres)
			?	'' //' '.MIDDLE_DOT_ENTITY.' '.$current_genre[2]
			:	(	$in_year
				?	' <small class="light">&middot '.($in_month ? _month_name($in_month).' ' : '').$in_year.'</small>'
				:	(empty($GLOBALS['__agenda_title']) ? '' : ' '.MIDDLE_DOT_ENTITY.' '.$GLOBALS['__agenda_title'])
				)
			).(!empty($year) ? ' '.MIDDLE_DOT_ENTITY.' '.$year : '').'</h1>'
		);
	}

	main_menu();

	$partylist = new _partylist();

	$jump_year	= 0;
	$jump_month	= 0;
	$jump_day	= 0;

	if (!empty($special_day)) {
		require_once '_party.inc';
		change_timezone('UTC');
		if (!($info = get_special_day_start_and_stop($special_day, $year, $partylist))) {
			return;
		}
		/** @noinspection PhpRedundantOptionalArgumentInspection */
		extract($info, \EXTR_OVERWRITE);

		if (CURRENTSTAMP < $stop
		&&	(	!$_REQUEST['subID']
			||	$__year === $year
			)
		) {
			$new_start = max($start, strtotime('today +'.HOUR_DAY_START.' hours -24 hours'));
			if ($new_start !== $start) {
				$start = $new_start;
			}
		}
		change_timezone();
		$partylist->select_between($start, $stop);

	} elseif ($in_year && $in_month) {
		$partylist->in_year_and_month($in_year,$in_month);
	} elseif (!$current_genre) {
		$partylist->select_coming_week(2);
	} else {
		$partylist->select_future(0);
	}

	# this filters some non-visited foreign events
	$partylist->only_for_current_country();

	if (!$current_genre
	&&	!isset($genres)
	) {
		ob_start();
		show_months();
		$month_select = ob_get_flush();

		show_quickjump($jump_year, $jump_month, $jump_day);

	} else {
		if (!$current_genre) {
			show_middle_header(Eelement_plural_name('events_and_parties_per_genre'));
			$gid = 0;
		} else {
			[$gid, /* $genre_name */, $genre_title] = $current_genre;
			show_middle_header("<h2>$genre_title</h2>");
		}
		?><div class="block"><?
		show_genre_select(
			'agenda',
			is_array($gid) ? 'favourites' : $gid,
			include_favourites: true,
			include_empty: isset($genres) ? __('action:choose_genre') : ''
		);
		?></div><?

		if (isset($genres)
		&&	!$current_genre
		) {
			return;
		}
		if (is_integer($gid)
		&&	($description_info = memcached_single_array('genre_description', $q = "
			SELECT LANGID, `SHORT`
			FROM genre_description
			WHERE GID = $gid
			  AND LANGID IN (0, ".CURRENTLANGID.')
			ORDER BY LANGID = '.CURRENTLANGID.' DESC
			LIMIT 1'))
		) {
			require_once '_ubb.inc';
			[$langid, $description] = $description_info;
			?><div class="genre-description" lang="<?= LOCALES[$langid][0] ?>"><?= make_all_html($description, UBB_UTF8) ?></div><?
		}
	}
	if (isset($special)) {
		layout_open_box();
		global $metadesc;
		$parts = explode("\n", $metadesc);
		layout_box_header($parts[0]);
		?><div class="block"><?= $parts[1] ?></div><?
		layout_close_box();
	}
	$partylist->hide_day_prefix 	= true;
	$partylist->show_vevent			= true;
	$partylist->show_people			= true;
	$partylist->show_city			= true;
	$partylist->show_location		= true;
	$partylist->show_stars			= true;
	$partylist->show_date_headers	= true;
	$partylist->show_contests		= true;
	$partylist->show_camera_present = true;
	$partylist->show_lineups		= true;
	$partylist->order_chronologically_but_notime();
	if (!isset($genres)) {
		$partylist->show_filter = true;
	} elseif ($current_genre) {
		$partylist->with_styles($current_genre[0]);
	}
	if (have_user()) {
		$partylist->show_buddies = true;
	}
	if ($partylist->query()) {
		$partylist->show_lineups_form = true;
		$partylist->display();
	}
	if (isset($month_select)) {
		?><div class="block">&hellip;</div><?
		?><hr class="slim" style="margin: 1em 0;"><?
		echo $month_select;
	}
}

function show_months(): void {
	$thenstamp = memcached_single('party','
		SELECT STAMP
		FROM party
		WHERE STAMP < '.offset_day_start(TODAYSTAMP).'
		ORDER BY STAMP DESC
		LIMIT 1',
		ONE_HOUR
	);
	if ($thenstamp) {
		$diff = CURRENTDAYNUM - to_days($thenstamp);
		$name = match($diff) {
			1	=> __('date:yesterday'),
			2	=> __('date:daybeforeyesterday'),
			default	=> __('date:days_past', ['DAYS' => $diff]),
		};
		[$year, $month, $day] = _getdate($thenstamp);
		$lst[] = '<a href="/agenda/day/'.$year.'/'.$month.'/'.$day.'">'.$name.'</a>';
	}
	$have_parties = memcached_boolean_hash('party', '
		SELECT DISTINCT 
			 YEAR(DATE_SUB(FROM_UNIXTIME(STAMP_TZI), INTERVAL '.HOUR_DAY_START.' HOUR)) AS YEAR,
			MONTH(DATE_SUB(FROM_UNIXTIME(STAMP_TZI), INTERVAL '.HOUR_DAY_START.' HOUR)) AS MONTH
		FROM party
		WHERE STAMP >= '.offset_day_start(TODAYSTAMP).'
		ORDER BY YEAR, MONTH',
		TEN_MINUTES,
		null,
		DB_UTC
	);
	if (!$have_parties) {
		return;
	}
	$lst = [];
	$current_year = 0;
	if (!$_REQUEST['ACTION']) {
		$in_year = $_REQUEST['sID'];
		$in_month = $_REQUEST['subID'];
	}
	foreach ($have_parties as $year => $months) {
		foreach ($months as $month => $bool) {
			if ($current_year !== $year) {
				$current_year = $year;
			}
			$selected =
				isset($in_year)
			&&	$in_year === $year
			&&	$in_month === $month;

			$lst[$current_year][] =
				'<a'.
				($selected ? ' class="selected underline"' : '').
				' title="'.__('element:agenda_of_month_and_year', ['YEAR' => $year, 'MONTHNAME' => _month_name($month)]).'"'.
				' href="/agenda/'.$current_year.'/'.$month.'">'._month_name($month).'</a>';
		}
	}
	?><nav><?
	?><table class="vtop"><?
	foreach ($lst as $year => $months) {
		?><tr><td><b><?= $year ?></b>:&nbsp;</td><td><?= implode(' '.MIDDLE_DOT_ENTITY.' ',$months) ?></td></tr><?
	}
	?></table><?
	?></nav><?
}

function main_menu(): void {
	$action = $_REQUEST['ACTION'];

	if ($action === 'single') {
		return;
	}

	$cnts = memcached_single_array(['party','camera'],'
		SELECT	COUNT(*),
			COUNT(IF(FIND_IN_SET("outdoor",LOCATIONTYPE) OR FIND_IN_SET("beach",LOCATIONTYPE),1,NULL)),
			COUNT(IF(NOT ISNULL(camera.PARTYID),1,NULL))
		FROM party
		'.join_only_events_for_current_country().'
		LEFT JOIN camera ON camera.PARTYID=party.PARTYID AND camera.STATUS IN ("accepted","self")
		WHERE ACCEPTED=1
		  AND STAMP_TZI>='.get_for_future_stamp_tzi()
	);
	[$allcnt, $outdoorcnt, $photocnt] = $cnts ?: [false, false, false];

	layout_open_menu();
	layout_menuitem(Eelement_plural_name('festival'), '/agenda/festivals', $action === 'festivals');
	layout_menuitem(Eelement_name('outdoor'), '/agenda/outdoor', $action === 'outdoor');
	if ($outdoorcnt) {
		?><small>,<?= $outdoorcnt ?></small><?
	}

	if (have_user()) {
		if ($buddies = _buddies_full_hash(CURRENTUSERID)) {
			ob_start();
			if (!($cnt = memcached_single(['party','going'], '
				SELECT COUNT(DISTINCT PARTYID)
				FROM going
				JOIN party USING (PARTYID)
				WHERE going.USERID IN ('.implode(', ', $buddies).')
				  AND ACCEPTED
				  AND MAYBE IN (0, 1)
				  AND STAMP_TZI >= '.get_for_future_stamp_tzi(),
				ONE_HOUR
			))) {
				?><span class="light"><?
			}
			echo Eelement_plural_name('buddy');
			if (!$cnt) {
				?></span><?
			}
			layout_menuitem(ob_get_clean(), '/agenda/buddies', $action === 'buddies');
			if ($cnt) {
				?><small>,<?= $cnt ?></small><?
			}
		}
		if ($favourites = get_favourites()) {
			$minimum_stamp_tzi = get_for_future_stamp_tzi();
			$tables = ['party'];
			$query_parts = [];
			foreach ($favourites as $element => $ids) {
				$idstr = implode(', ', $ids);
				switch ($element) {
				case 'location':
					$query_parts[] = "
						SELECT DISTINCT PARTYID
						FROM party
						WHERE LOCATIONID IN ($idstr)
						  AND STAMP_TZI >= $minimum_stamp_tzi
						  AND ACCEPTED
						UNION
						SELECT DISTINCT PARTYID
						FROM party
						JOIN connect
						WHERE MAINTYPE IN ('locasorg', 'locashost')
						  AND MAINID IN ($idstr)
						  AND ASSOCTYPE = 'party'
						  AND ASSOCID = PARTYID
						  AND ACCEPTED";
					break;

				case 'artist':
					$tables[] = 'lineup';
					$query_parts[] = "
						SELECT DISTINCT PARTYID
						FROM party
						JOIN lineup USING (PARTYID)
						WHERE ARTISTID IN ($idstr)
						  AND STAMP_TZI >= $minimum_stamp_tzi
						  AND ACCEPTED";
					break;

				case 'organization':
					$tables[] = 'connect';
					$query_parts[] = "
						SELECT DISTINCT PARTYID
						FROM party
						JOIN connect
						WHERE MAINTYPE IN ('organization', 'orgashost')
						  AND MAINID IN ($idstr)
						  AND ASSOCTYPE = 'party'
						  AND ASSOCID = PARTYID
						  AND STAMP_TZI >= $minimum_stamp_tzi
						  AND ACCEPTED
						UNION
						SELECT DISTINCT PARTYID
						FROM party
						JOIN partyarea USING (PARTYID)
						WHERE HOSTEDBYID IN ($idstr)
						  AND STAMP_TZI >= $minimum_stamp_tzi
						  AND ACCEPTED";
					break;

				default:
					continue 2;
				}
			}
			if (!$query_parts) {
				mail_log('no query_parts to obtain favourites', get_defined_vars());

			} elseif ($cnt = memcached_single($tables, '
				SELECT COUNT(*)
				FROM ('.implode(' UNION ', $query_parts).') AS favs')
			) {
				ob_start();
				if (!$cnt) {
					?><span class="light"><?
				}
				echo Eelement_plural_name('favourite');
				if (!$cnt) {
					?></span><?
				}
				layout_menuitem(ob_get_clean(), '/agenda/favourites', $action === 'favourites');
				if ($cnt) {
					?><small>,<?= $cnt ?></small><?
				}
			}
		}
	}

	$favourite_genres_count = agenda_favourite_genres_count();
	layout_menuitem(
		Eelement_plural_name($favourite_genres_count ? 'favourite_genre' : 'genre'),
		$favourite_genres_count ? '/agenda/genre/favourites' : '/agenda/genres',
		$favourite_genres_count ? ($_REQUEST['ACTION'] === 'genre' && $_REQUEST['SUBACTION'] === 'favourites') : ($_REQUEST['ACTION'] === 'genre')
	);
	if ($favourite_genres_count) {
		?><small>,<?= $favourite_genres_count ?></small><?
	}

	ob_start();
	if (!$photocnt) {
		?><span class="light"><?
	}
	echo Eelement_plural_name('photographer');
	if (!$photocnt) {
		?></span><?
	}
	layout_menuitem(ob_get_clean(),'/agenda/photographers',$action === 'photographers');
	if ($photocnt) {
		?>,<small><?= $photocnt ?></small><?
	}

	if (have_admin()) {
		layout_menuitem(__C('status:new'), '/agenda/new', $action === 'new');
	}

	layout_continue_menu();
	layout_menuitem(Eelement_name('archive'),'/agenda/archive',$action === 'archive');
	layout_menuitem(__C('action:advanced_search'),'/agenda/search',$action === 'search');
	layout_close_menu();
}

function agenda_display_favourites() {
	layout_show_section_header(Eelement_name('party_agenda').' '.MIDDLE_DOT_ENTITY.' '.element_plural_name('favourite'));

	main_menu();
	$partylist = new _partylist;
	$partylist->show_date_headers =
	$partylist->show_people =
	$partylist->show_city =
	$partylist->show_location =
	$partylist->show_stars =
	$partylist->show_buddies =
	$partylist->skip_future_lineup =
	$partylist->show_lineups =
	$partylist->show_camera_present = true;
	$partylist->order_chronologically_but_notime();
	$partylist->only_favourites();
	$partylist->select_future();
	if (!$partylist->query()) {
		return;
	}
	if ($partylist->have_rows()) {
	//	$partylist->display_searchresult();
		$partylist->display();
	} else {
		?><p>Niets gevonden.</p><?
	}
}
function agenda_display_livestreams() {
	layout_show_section_header(Eelement_name('livestream_agenda'));

	main_menu();
	$partylist = new _partylist;
	$partylist->show_date_headers =
	$partylist->show_people =
	$partylist->show_stars =
	$partylist->show_buddies =
	$partylist->skip_future_lineup =
	$partylist->show_lineups = true;
	$partylist->order_chronologically_but_notime();
	$partylist->only_livestreams = true;
	$partylist->select_future();
	if (!$partylist->query()) {
		return;
	}
	if ($partylist->have_rows()) {
	//	$partylist->display_searchresult();
		$partylist->display();
	}
}
