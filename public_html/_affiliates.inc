<?php

declare(strict_types=1);

const BOOKING_COM_AID	= 361595;

# OBSOLETE: Remove when nobody complains
# const TRADEDOUBLER_ID	= 1837667;
# const TRADETRACKER_ID	= 72271;
# const DAISYCON_ID		= 94351;

function count_affiliate(string $element, string $type): bool {
	return db_insupd('misccount', "
	INSERT INTO misccount SET
		ELEMENT	= '$element',
		TYPE	= '$type',
		DAYNUM	= ".CURRENTDAYNUM.',
		HITS	= 1
	ON DUPLICATE KEY UPDATE
		HITS	= HITS + 1'
	);
}

function get_booking_com(string $element, int $id, array $item, ?bool &$show_icon = null, ?string &$tkey = null, ?array &$trep = null) {
	static $__href;
	if (isset($__href[$element][$id])) {
		return $__href[$element][$id];
	}
	switch ($element) {
	case 'party':
		if ($item['STAMP'] < CURRENTSTAMP - 8*ONE_HOUR
		||	!($uselocation = $item['boarding'] ?? $item['location'] ?? null)
		) {
			return null;
		}
		$show_icon = true;
		/*		preg_match('"festival|open[\s-]*air|outdoor"iu', $item['NAME'].$item['SUBTITLE'])
			||	$item['VISITORCNT'] >= 100
			||	($item['CERTAIN_CNT'] + $item['MAYBE_CNT']) >= 100
			||	$item['RESTRICTION']
			||	$item['EROTIC']
			||	!empty($item['city'])
			&&	!in_array($item['city']['COUNTRYID'], [1, 6], true)

			||	(require_once '_geo.inc')
			&&	($geo = get_geolocation_cache())
			&&	$geo[0]
			&&	$geo[1]
			&&	null !== $uselocation['LATITUDE']
			&&	null !== $uselocation['LONGITUDE']
			&&	calculate_distance($geo[0], $geo[1], $uselocation['LATITUDE'], $uselocation['LONGITUDE']) >= 100;*/

		[$y, $m, $d] = _getdate($item['STAMP_TZI'] - 3 * ONE_HOUR, 'UTC');
		[$outy, $outm, $outd] = _getdate($item['STAMP_TZI'] + $item['DURATION_SECS'],'UTC');
		if ($outy === $y
		&&	$outm === $m
		&&	$outd === $d
		) {
			change_timezone('UTC');
			[$outy, $outm, $outd] = _getdate(strtotime('+1 day',$item['STAMP_TZI']),'UTC');
			change_timezone();
		}

		$args = [
			'aid='.BOOKING_COM_AID,
			'latitude='. rtrim((string)$uselocation['LATITUDE' ],'0'),
			'longitude='.rtrim((string)$uselocation['LONGITUDE'],'0'),
			'order=distance_from_landmark',
			'dst_landmark=cc',
			'radius=20',
			'selected_currency=EUR',
			'do_availability_check=1',
			'checkin_monthday='.$d,
			'checkin_year_month='.$y.'-'.str_pad((string)$m,2,'0',STR_PAD_LEFT),
			'checkout_monthday='.$outd,
			'checkout_year_month='.$outy.'-'.str_pad((string)$outm,2,'0',STR_PAD_LEFT),
			'label=party_'.$id
		];
		$tkey = 'affiliate:info:book_vicinity_LINE';
		break;

	case 'region':
	case 'city':
	case 'location':
	case 'boarding':
		$show_icon = true;
		$args = [
			'aid='.BOOKING_COM_AID,
			'latitude='. rtrim((string)$item['LATITUDE' ],'0'),
			'longitude='.rtrim((string)$item['LONGITUDE'],'0'),
 			'order=distance_from_landmark',
			'dst_landmark=cc',
			'radius=20',
			'selected_currency=EUR',
//			'do_availability_check=1',
//			'checkin_monthday='.$d,
//			'checkin_year_month='.$y.'-'.str_pad($m,2,'0',STR_PAD_LEFT),
//			'checkout_monthday='.$outd,
//			'checkout_year_month='.$outy.'-'.str_pad($outm,2,'0',STR_PAD_LEFT),
			"label={$element}_$id"
		];
		$tkey = 'affiliate:info:book_in_city/region_LINE';
		$trep = ['REGION' => escape_utf8($item['NAME'])];
		break;

	default:
		mail_log("get_booking_com(): unknown element $element", get_defined_vars(), error_log: 'WARNING');
		return false;
	}

	$href = escape_specials('//www.booking.com/searchresults.'.CURRENTLANGUAGE.'.html?'.implode('&',$args));

	return $__href[$element][$id] = $href;
}

function show_booking_com(string $element, int $id, array $item): ?string {
	if (!($href = get_booking_com($element, $id, $item, $show_icon, $tkey, $trep))) {
		return $href;
	}

	count_affiliate($element, 'bookingcom');

	$title = str_replace('%VIALIST%', 'Booking.com', __($tkey, KEEP_EMPTY_KEYWORDS));
	if (isset($trep)) {
		[$key, $val] = keyval($trep);
		$title = str_replace('%'.$key.'%', $val, $title);
	}

	?><a<?
	?> href="<?= $href ?>" target="_blank"><?
	?><div class="partner-button bookingcom" title="<?= $title ?>"><?
	?><img alt="<?= $title ?>" src="<?= STATIC_HOST ?>/buttons/bookingcom.png" /><?
	?></div><?
	?></a> <?

	return $href;
}

function make_affiliate_url(string $url, ?string  $element = null, ?int $id = null, ?array $args = null, bool $utf8 = false): string {
	$url = mytrim($url, utf8: $utf8);
	global $__ubbcontext;
	if (!$__ubbcontext && !$element) {
		return $url;
	}
	if ($__ubbcontext) {
		[$element, $id] = $__ubbcontext;
	}
	require_once '_spider.inc';
	if (ROBOT) {
		# Don't show affiliate links to robots.
		return $url;
 	}
	require_once '_hosts.inc';
	if (empty($args['DONT_REPLACE_UTM'])) {
		$url = replace_utm($url, $element, $id, $utf8);
	}
	$utf8_mod = $utf8 ? 'u' : '';
/*	OBSOLETE: Remove when nobody complains

	# BOL
	if (str_contains($url,'partnerprogramma.bol.com')) {
		$url = preg_replace('"\bs=\d+"'.$utf8_mod, 's=15892', $url);
		$url = preg_replace('"\bsubid=.*(?:$|&)"'.$utf8_mod, 'subid='.$element.':'.$id, $url, -1, $count);
		if (!$count) {
			$url .= '&subid='.$element.':'.$id;
		}

	} elseif (preg_match('"^(https?:)?//(?:www\.)?bol\.com"i'.$utf8_mod, $url, $match)) {
		$url =
			'//partnerprogramma.bol.com/click/click?p=1&t=url&s=15892&url='.
			urlencode((empty($match[1]) ? 'https:' : '').$url).
			'&f=TXL&subid='.$element.':'.$id.'&name='.$element.':'.$id;*/

	if (false !== mb_stripos($url, 'www.sdc.com')) {
		$sdc = 25204;
		if (str_contains($url, 'ref=')) {
			$url = preg_replace('"\bref=\d+"'.$utf8_mod, "ref=$sdc", $url);
		} else {
			$url .= (str_contains($url, '?') ? '&' : '?'). "ref=$sdc";
		}

/*	OBSOLETE: Remove when body complains

	} elseif (preg_match('"^(https?://|//)?(?:www\.)?('.
			'(bax-shop'.
			'|jbl'.
			(CURRENTSTAMP < 1709161200 ? '|vakantieveilingen' : '').
			'|mooiesneakers'.
		')\.(?:nl|com)'.
		')(/.*)?$"i'.$utf8_mod,$url,$match)
	) {
		$tt_ref = str_replace('_', '', $element).'%3A'.$id;
		switch ($which = strtolower($match[3] ?: $match[2])) {
		case 'bax-shop':
			if (str_contains($match[0],'/tradetracker/')) {
				return $match[0];
			}
			$url = $match[1] ?: 'https://';
			$url .= 'www.bax-shop.nl/tradetracker/?tt=1687_12_'.TRADETRACKER_ID.'_'.$tt_ref.'&r='.urlencode(empty($match[4]) ? '/' : $match[4]);
			break;
		default:
			# only path
			static $__cids = [
				'jbl'				=> 10224,
				'mooiesneakers'		=> 19418,
				'vakantieveilingen'	=> 4179,
			];
			$cid = $__cids[$which];
			$url = 'https://tc.tradetracker.net/?c='.$cid.'&m=12&a='.TRADETRACKER_ID.'&r='.$element.'%3A'.$id.(empty($match[4]) ? null : '&u='.urlencode($match[4]));
			break;
		}*/

	# Booking.com
	} elseif (preg_match('"^(?<scheme>https?://|//)?(?:www\.)?booking\.com(?<path>/.*)?$"i'.$utf8_mod, $url, $match)) {
		$new_aid = 'aid='.BOOKING_COM_AID;
		$url = preg_replace('"\baid=\d+"'.$utf8_mod, $new_aid, $url, -1, $count) ?? $url;
		$have_aid = $count;
		$new_label = "label={$element}_$id";
		$url = preg_replace('"\blabel=[a-z\d_.-]+"'.$utf8_mod, $new_label, $url, -1, $count) ?? $url;
		$have_label = $count;

		if (!$have_label || !$have_aid) {
			$url .=	empty($match['path'])
				||	$match['path'] === '/'
				||	!str_contains($match['path'], '?')
				?	'?'
				:	'&';
			if (!$have_aid) {
				$url .= $new_aid;
			}
			if (!$have_label) {
				if (!$have_aid) {
					$url .= '&';
				}
				$url .= $new_label;
			}
		}
	}
	return $url;
}

/* OBSOLETE: Remove when nobody complains

function link_affiliates(string $text, bool $ubb, ?string $element = null, int $id = 0, bool $utf8 = false): string {
	if (ROBOT) {
		return $text;
	}
	$store = [];
	$utf8_mod = $utf8 ? 'u' : '';
	$ndx = -1;
	if (false !== mb_stripos($text, '[url')
	||	false !== mb_stripos($text, '[img')
	||	false !== mb_stripos($text, '[relurl')
	) {
		$text = preg_replace_callback('"\[((?:rel)?url|img)\b.*?\\1\]"i'.$utf8_mod, static function(array $match) use (&$store, &$ndx) : string {
			$store[++$ndx] = $match[0];
			return "\x15$ndx\x16";
		}, $text);
	}
	$text = preg_replace_callback('"(^|[/\s\x2\b\]\'\"\(])((?:www?\.)?(?:'.
		'sdc(?:.com)?'.
	'))([!,.\-\s\n\/\)?:\x3\[\"\']|$)"ims'.$utf8_mod, static function(array $match) use ($utf8_mod, $ubb): string {
		static $__urls = [
			'sdc'		=> 'https://www.sdc.com/',
			'sdc.com'	=> 'https://www.sdc.com/',
		];
 		$lower = mb_strtolower(preg_replace('"^www?\.|\.\w+$|-|\W+"i'.$utf8_mod, '', $match[2]));
 		$site = $__urls[$lower];
		return  $match[1].(
				!$ubb
				?	   '<a target="_blank" rel="nofollow" href="'.escape_utf8(make_affiliate_url($site, utf8: true)).'">'.$match[2].'</a>'
				:	   '[url url="'.$site.'" affiliate]'.$match[2].'[/url]'
			).$match[3];
	}, $text);

	if ($ndx > -1) {
		$text = preg_replace_callback("'\x15(\d+)\x16'".$utf8_mod, function(array $match) use ($store): string {
			return $store[$match[1]];
		}, $text);
	}
	return $text;
}*/
