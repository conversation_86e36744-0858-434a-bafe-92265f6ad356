<?php

require_once '_bio.inc';
require_once '_unicode.inc';

function get_party_description(
	int  $partyid,
	bool $for_appic = false
): array {
	$fb = db_single_assoc('facebook_info','
		SELECT NAME, DESCRIPTION AS DESCR, 0 AS FBID, CSTAMP
		FROM facebook_info
		WHERE PARTYID = '.$partyid
	);
	if ((!$fb || empty($fb['DESCR']))
	&&	($feed_event = memcached_single_assoc(['fbid','feedevent','fbcounters'], "
		SELECT UNCOMPRESS(DESCRIPTION) AS DESCR, NAME, FBID, MSTAMP
		FROM
		(	SELECT DISTINCT DESCRIPTION, NAME, FBID, ATTENDING, MAYBE, MSTAMP
			FROM fbid
			JOIN feedevent ON FBID = FEEDID
			LEFT JOIN fbcounters USING (FBID)
			WHERE fbid.ELEMENT = 'party'
			  AND fbid.ID = $partyid
			UNION
			SELECT DISTINCT DESCRIPTION, NAME, FBID, ATTENDING, MAYBE, MSTA<PERSON>
			FROM fbid
			JOIN feedevent ON FBID = FEEDID
			LEFT JOIN fbcounters USING (FBID)
			WHERE feedevent.PARTYID = $partyid
		) AS x
		ORDER BY ATTENDING DESC,MSTAMP DESC
		LIMIT 1"))
	&&	(	!$fb
		||	empty($fb['DESCR'])
			# Fallback older than feed event:
		||	$fb['CSTAMP'] < $feed_event['MSTAMP']
		)
	){
		$fb = $feed_event;
	}
	if (!$fb || empty($fb['DESCR'])) {
		return [];
	}
	/** @noinspection PhpCastIsUnnecessaryInspection, UnnecessaryCastingInspection */
	# Workaround for: [EA] '$fb' may not support offset operations (or its type not annotated properly: [bool, array])..
	$fb = (array)$fb;

	require_once '_flockmod.inc';
	$fb['DESCR'] = clean_utf8($fb['DESCR']);

	# strip WOOV stuf
	if (preg_match("'(.*\r?\n).*?\bWOOV\.?\r?\n.*?\r?\n\r?\n(.*)'is",$fb['DESCR'],$match)) {
		$fb['DESCR'] = $match[1].$match[2];
	}

	$fb['DESCR'] = preg_replace("'\n.{0,4}?\h*[\w\h]*App[:\h]*(?:https?://)?woov\..*?(?:\n|$)'is",'',$fb['DESCR']);
	$fb['DESCR'] = str_replace(REPLACEMENT_CHARACTER, '', $fb['DESCR']);

	$party = memcached_party_and_stamp($partyid);

	$description = $fb['DESCR'];
	$original_description = $description;

	require_once '_unicode.inc';
	$symbols = get_symbs();

	# find info
	# generally does not work: (INFO often is not nice description)
/*	if (preg_match('"\n['.$symbols.'\s]*INFO(?:rmation)?['.$symbols.'\s]*\s*(.*)$"uis',$description,$match)) {
		if (!preg_match('"^[^\n]*€"',$match[1])
		#	^ info starting with price is ignored
		) {
			$description = $match[1];
		}
	}*/

	# remove trailing lists with symbols
	# notable user: Disco Dolly
	if (preg_match('"^(?:['.$symbols.']+\h*[^\r\n]*?[\r\n]+)+(.*)$"s',$description,$match)) {
		$description = $match[1];
	}

	$artist_names = [];
	$artist_name_str = '';
	require_once '_partyinfo.inc';
	if ($lineup = get_party_lineup($partyid)) {
		foreach ($lineup as $artist_name) {
			$new_name = '';
			foreach (preg_split('"(\W)"u', $artist_name, -1, PREG_SPLIT_DELIM_CAPTURE) as $artist_name_part) {
				switch ($artist_name_part) {
				case '-':
				case ' ':
					$new_name .= '[\s\-]*';
					break;
				default:
					$new_name .= preg_quote($artist_name_part, '"');
					break;
				}
			}
			$artist_names[] = $new_name;
		}
		$artist_name_str = implode('|', $artist_names);
	}

	$remove_head = static function (&$tmp) use ($artist_name_str, $symbols, $party): bool {
		$replaced = false;
		if (preg_match('"^(?:Ticket(?:s|link)\s*[:@]?\s*http[^\n]+|info:)\n\n(.*)$"uis',$tmp,$match)) {
			# strip leading ticket link
			$tmp = utf8_mytrim($match[1]);
			$replaced = true;
		}
		if ($artist_name_str) {
			# strip leading artist names
			if (preg_match(
				'"^[\s\-'.$symbols.']*'.
					'(?:DJ\s)?'.
					'(?:'.$artist_name_str.'|\w+ residents)'.
					'(?:[\s\-]*live)?'.
					'(?:\s*\([^\n]+\))?'.
					'\n(.*)$"uis', $tmp, $match)
			) {
				$tmp = utf8_mytrim($match[1]);
				$replaced = true;
			}
		}
		if (preg_match('"^\d+\s*(?:januari|februari|maart|april|mei|juni|juli|augustus|september|oktober|november|december)[\w\s]*?\s*\d+:\d+\s*['.$symbols.']*\n(.*)$"uis',$tmp,$match)) {
			# strip generic date header
			$tmp = utf8_mytrim($match[1]);
			$replaced = true;
		}
		if (preg_match('"^Tickets[\s'.$symbols.']*https?://[^\n]*\n(.*)$"uis',$tmp,$match)) {
			$tmp = utf8_mytrim($match[1]);
			$replaced = true;
		}
		if (preg_match('"^(?:['.$symbols.']\s*(?:[a-z\d.,€?!\-]+\s){0,4}[a-z\d.,€?!\-]+\n)+(.*?)$"uis',$tmp,$match)) {
			# strip leading lists (often line-up or generic info)
			$tmp = utf8_mytrim($match[1]);
			$replaced = true;
		}
		if (preg_match('"^line[-\s]?up:\s*(?:[^\n/|,]+[/|,]\s*)*[^\n/|,]+\n\n(.*)$"uis',$tmp,$match)) {
			# strip leading lineup
			$tmp = utf8_mytrim($match[1]);
			$replaced = true;
		}
		if (preg_match('"^['.$symbols.']*\s*(?:'.
			'[^\n]+ proudly presents:?\n'.
			'|op de hoogte blijven[^\n]* like [^\n]* http[^\n]*facebook[^\n]*\n'.
			'|support by (?:'.$artist_name_str.'|\w+ \w+)\n'.
			'|More info soon\.*\n'.
			'|(?:\s*<3\s*)+\n'.
			'|(?:#\w+\s?)+\n'.
			'|\w+['.$symbols.'\s]*presented by \w+'.
			'|(?:new years eve|christmas) special\n'.
			'|'.(	!empty($party['location']['NAME'])
			?	preg_quote($party['location']['NAME'], '"').
				(	!empty($party['city']['NAME'])
				?	'['.$symbols.'\s*]'.preg_quote($party['city']['NAME'], '"')
				:	null
				)
			:	null
			).'\n'.
			'|free entrance!?\n'.
			'|(?:monday|tuesday|wednesday|thursday|friday|saturday|sunday)\s*\d{1,2}\s*\w+\s*20\d{1,2}'.
			'|\d+[.\-]\d+[\-.]20\d\d\s[^\n]{1,40}\n'.
			'|(?:'.	'Wanneer:|'.
					'Aanvang:|'.
					'@ \w+\n|'.
					'\d{2}[:.]\d{2}[\s/\-]+\d{2}[:.]\d{2}\b|'.
					'Toegang:|'.
					'Tijd:|'.
					'Presale:|'.
					'Tickets VVK:|'.
					'Tickets Door:|'.
					'Voorverkoop:|'.
					'Papaya club, Zrce beach, Island of Pag, Croatia|'.
					'Deurverkoop:|'.
					'Iedere laatste \w+ van de maand - \w+\n|'.
					'Binnen\s*kort online!?\n|'.
					'Kaartverkoop via |'.
					'Koop je tickets hier:|'.
					'\w+ presents \w+\.?\n|'.
					'Party (?:location|date|time):|'.
					'Table / Vip service:|'.
					'minimum leeftijd \d+|'.
					# 28 / 11 / 2014 - Showtime Vlaardingen
					'\d{1,2}\s*/\s*\d{1,2}\s*/\s*20\d{2}[\s*\-]*(?:[\w ]{1,30})?\n\n|'.
					'online tickets via link|'.
					'(?:tickets|presale|door)\s*€|'.
					'early[\-\s]*birds? €|'.
					'(?:regular|normale|family) tickets? €|'.
					'RSVP |'.
					'>+\n|'.
					'Tickets Deur:|'.
					'Early bird start|'.
					'Early Bird(?: tickets)?:|'.
					'Door:|'.
					'Sound:|'.
					'Space:|'.
					'Entree:|'.
					'Locatie:|'.
					'Tickets\s*\(\d+\s*[AP]M\)\s*|'.
					'Tickets(?::|\s*\|)|'.
					'Ticketlink:|'.
					'Waar:|'.
					'Datum:|'.
					'Website:|'.
					'Meer info en tickets:|'.
					'(?:www\.)?(?:soundcloud|youtube)\s*bit\.ly/|'.
					'facebook\s*on\.fb\.me/'.
				')[^\n]*'.
			')(.*)$"uis', $tmp, $match)
		) {
			$tmp = utf8_mytrim($match[1]);
			$replaced = true;
		}
		if (preg_match('"^(?:['.$symbols.'] [^\n]+\n){2,}\n(.*)$"uis', $tmp, $match)) {
			# lists, (symbols for 'dot')
			$tmp = utf8_mytrim($match[1]);
			$replaced = true;
		}
		while (	($tmp = preg_replace('"^(?:http\S+|www\.\S+)\n+"ui','',$tmp,-1,$cnt))
		&&	$cnt) {
			# strip leading websites
			$replaced = true;
		}
		if (preg_match('"^['.$symbols.'A-Z\s&\d\\\|l/:.,–\-\'’‘!@\"()]+\n(.*)$"us',str_replace('w/','',$tmp),$match)) {
			$tmp = utf8_mytrim($match[1]);
			$replaced = true;
		}
		# strip title/subtitle
		if (preg_match('"^['.$symbols.'\s]*'.preg_quote($party['NAME'], '"').(
				$party['SUBTITLE']
			?	'(?:\s*['.$symbols.']*\s*'.preg_quote($party['SUBTITLE'], '"').')?'
			:	null
			).'(?:\s*\(.*?\))?'.
			'['.$symbols.':\s]*\n(.*)$"usi',$tmp,$match)
		) {
			$tmp = utf8_mytrim($match[1]);
			$replaced = true;
		}
		if (preg_match('"^[\s\-'.$symbols.']*[A-Z\-&\sx]+[\s\-'.$symbols.']*\n(.*)$"us',$tmp,$match)) {
			$tmp = utf8_mytrim($match[1]);
			$replaced = true;
		}
		return $replaced;
	};

	while ($remove_head($description)) {}

	$lines = explode("\n", $description);
	$ok_lines = [];
	$ok_lines_count = 0;
	$previous_empty = false;

	foreach ($lines as $line) {
		$line = utf8_mytrim($line);
		# trim symbols
		$line = preg_replace(['"\h*DJ$"ui', '"^['.$symbols.'\s]+"ui', '"['.$symbols.'\s]+$"ui'], '', $line);

		if (preg_match('"^(?:'.
				'(?:Saturday|Sunday|Monday|Tuesday|Wednesday|Thursday|Friday) (?:January|February|March|April|May|June|July|August|September|October|November|December),?\s*\d+(?:th)?[\s\-]*20\d{2}'.
		'|'.	'(?:DAY|NIGHT)\s*EVENT[\s:]*\d+[:.]\d+(?:(?:till|[\s-]+)\s*\d+[:.]\d+)?'.
		'|'.	'\d+\s*(?:'.	'January|February|March|April|May|June|July|August|September|October|November|December|'.
								'januari|februari|maart|april|mei|juni|juli|augustus|september|oktober|november|december)\s*20\d\d[\s.]*\d+:\d+[\s.]*\d+:\d+'.
		'|'.	'PRE-REGISTER HERE: [\w.]+'.
		'|'.	'.*?\bpresent(?:eren|s)?:?$'.
		'|'.	'\d+:\d+\h*-\h*\d+:\d+'.
		'|'.	'Tickets'.
		'|'.	'www\.\w+\.\w+'.
		'|'.	'RA RSVP: http\H+'.
		'|'.	'\w+(?:\s+\w+)? b[23]b \w+(?:\s+\w+)?'.
		'|'.	'(?:More|(?:Very\h*)?Special\h*Guest)\h*TBA'.
		'|'.	'https?://\S+'.
		'|'.	'(?:(?:Early|Regul(?:a|ie)r)\s*Bird|Deur|Door)\s*(?:€\d+)?(?:\s*\(?cash only\)?)?'.
		'|'.	preg_quote($party['NAME'], '"').
				($party['SUBTITLE'] ? '(?:\s*['.$symbols.']*\s*'.preg_quote($party['SUBTITLE'], '"').')?' : '').
		'(?:\s*\(.*?\))?'.
		')$"iu',$line)
		) {
			# not interesting line
			continue;
		}

		$is_empty = !$line;

		if ($is_empty
		&&	$previous_empty
		) {
			# don't include more than 1 empty line
			continue;
		}

		++$ok_lines_count;

		$ok_lines[] = $line;

		$previous_empty = $is_empty;
	}
	$description = implode("\n",$ok_lines);

	$tmp = $description;

	while ($remove_head($tmp)) {}

	# strip prices:
	if (preg_match('"^(.*?)(?:(?:^|\n)[\s\-'.$symbols.']*(?:(?:Normal|Late|Early)\s*bird|Regulier):[^\n]*)+\n(.*)$"uis',$tmp,$match)) {
		$tmp = utf8_mytrim($match[1]."\n".$match[2]);
		$remove_head($tmp);
	}
	# strip uninteresting bits:
	if (preg_match('"^(.{20,}?)\n[\s\-'.$symbols.']+(?:MUZIEK|Line[\s\-]?up|WHO\'S THERE|GENERAL INFO|TICKETS\s*/*|TABLE SERVICE|[\-'.$symbols.']+|[A-Z()\s]{10,})"us',$tmp,$match)) {
		$tmp = $match[1];
	}
	if (preg_match('"^(.{20,}?)\n[\s\-'.$symbols.']*'.
		'(?:'.preg_quote($party['NAME'], '"').':|'.
			'datum:|'.
			'\d{1,2}:\d{2} - \d{1,2}:\d{2}:|'.
			'timetable:|'.
			'doors open:|'.
			'date:|'.
			'time[\-\s]*table(?:\s*&\s*info)?:?|'.
			'(?:area|studio) \d+:|'.
			'locatie:|'.
			'info:|'.
			'waar:|'.
			'vroege vogel:|'.
			'\nkaartverkoop via |'.
			'\nkarten via |'.
			'fase 1:|'.
			'𝗣𝗿𝗼𝗴𝗿𝗮𝗺𝗺𝗮|'.
			'meer informatie:|'.
			'ticket release:|'.
			'open:|'.
			'ticketlink:|'.
			'tickets via|'.
			'ARTISTS\n|'.
			'(?:de sexy )?line[\s\-]*up\s*[:!]?|'.
			'dj\'?s:|'.
			'[▬•]{4,}|'.
			'V(?:oor)?V(?:erkoop)?:\s*€[^\n]*?)[^\n]*\n"uis',$tmp,$match)
	) {
		$tmp = $match[1];
	}
	if ($artist_name_str
		# stop at lineup
	&&	preg_match('"^(.{20,}?)\n[x\s\-'.$symbols.']*(?:DJ\s)?('.$artist_name_str.')(?:\s*\(.*?\)|[\s\-]*live)?\n"uis',$tmp,$match)
	/** @noinspection RegExpDuplicateAlternationBranch */
	||	preg_match('"^(.*)\n(?:𝐋𝐈𝐍𝐄|LINE|𝗟𝗜𝗡𝗘)[-\h]*(?:𝐔𝐏|UP|𝗨𝗣)\n.+"uis',$tmp,$match)
	||	preg_match('"^[\s\-'.$symbols.']*line[\s-]?up:?[\s\-'.$symbols.']*\n+(?:[\s\-'.$symbols.']*[\w\s\d]+\n)+?\n(.*)$"usi',$tmp,$match)
	||	preg_match('"^(.?)\n\n(?:(?:\w+ ){1,3}\w+\n)+\n"usi',$tmp,$match)
	||	preg_match('"^(.*?)[^\n]*(?:more info at|exclusive sound by)"uis',$tmp,$match)
	||	preg_match('"^(.*?)[A-Z]+\s+[A-Z]+\s+\d+\n"us',$tmp,$match)
	) {
		$tmp = $match[1];
	}

	$tmp = utf8_mytrim($tmp,'|');

	$teaser = get_teaser($tmp,$newsame);

	require_once '_catch_presales.inc';

	$teaser = catch_presales($teaser, $partyid, $for_appic);

	$original_description = catch_presales($original_description, $partyid, $for_appic);

	return [$teaser,
		$original_description,
		$teaser === $original_description,
		$fb['FBID'],
		$fb['NAME']
	];
}
