<?php

declare(strict_types=1);

require_once 'defines/timing.inc';

const BGQRY_VISITORS			= 1;
const BGQRY_VISITORS_EVER		= 2;
const BGQRY_VOTES				= 3;
const BGQRY_AGES				= 5;
const BGQRY_AGE_MEDIAN			= 6;
const BGQRY_VISITORMAP			= 7;
const BGQRY_VISITORMAP_MAYBE	= 8;
const BGQRY_VIEWS				= 9;
const BGQRY_LAST				= 9;

const BGQRY_CACHE_TIME			= ONE_DAY;

function rm_bgquery_votes(string $element, int|array $id): bool {
	return rm_bgquery($element, $id, BGQRY_VOTES);
}
function rm_bgquery(string $element, int|array $id, int|array|null $queryid = null): bool {
	if (!$queryid) {
		$queryid = implode(', ', range(1, BGQRY_LAST));
	} elseif (is_array($queryid)) {
		$queryid = implode(', ', $queryid);
	}
	return	db_delete('bgqueryv2', "
			DELETE FROM bgqueryv2
			WHERE QUERYID IN ($queryid)
			  AND ELEMENT = '$element'
			  AND ID IN (".(is_array($id) ? implode(',', $id) : $id).')'
	);
}

/*function get_cachetime(int $queryid, string $element): int {
	return match ($queryid) {
	BGQRY_VOTES,
	BGQRY_VISITORS_EVER,
	BGQRY_AGES,
	BGQRY_AGE_MEDIAN		=> ONE_HOUR,
	BGQRY_VISITORS,
	BGQRY_VISITORMAP_MAYBE,
	BGQRY_VISITORMAP		=> $element === 'party' ? ONE_HOUR : ONE_DAY,
	BGQRY_VIEWS				=> ONE_DAY,
	};
}*/

# cache -> false, then refreshing
# EXPIRES 0 => no need to update
# EXPIRES > 0 => update after STAMP==EXPIRES

function do_background_query(
	int		$queryid,
	string	$element,
	int		$id,
	bool	$cache = true
): mixed {
	static $__cache;
	if ($cache
	&&	isset($__cache[$queryid][$element][$id])
	) {
		return $__cache[$queryid][$element][$id];
	}
	if (!$cache) {
		# for slow queries, and queries that don't care about being 3 hours behind
		$flags = DB_FORCE_SERVER;
		$server = ['dbsrch', 'dbsrchrep'];
	} else {
		$flags = 0;
		$server = null;
	}
	// $cache_time = get_cachetime($queryid, $element);
	$cache_time = BGQRY_CACHE_TIME;

	switch ($queryid) {
	case BGQRY_VISITORS:
		$key = null;
		switch ($element) {
		case 'city':
			$result = memcached_simple_hash(['party','going','location','boarding'], "
				SELECT COUNT(*), COUNT(DISTINCT going.USERID)
				FROM party
				JOIN going USING (PARTYID)
				JOIN (	SELECT DISTINCT PARTYID
						FROM party
						WHERE CITYID = $id
						UNION
						SELECT DISTINCT PARTYID
						FROM party
						JOIN location USING (LOCATIONID)
						WHERE location.CITYID = $id
						UNION
						SELECT DISTINCT PARTYID
						FROM party
						JOIN boarding USING (BOARDINGID)
						WHERE boarding.CITYID = $id
				) AS parties_in_city USING (PARTYID)
				WHERE MAYBE = 0",
				$cache_time,
				$key,
				$flags,
				$server
			);
			break;

		case 'location':
			$result = memcached_simple_hash(['party','going'],'
				SELECT COUNT(*), COUNT(DISTINCT going.USERID)
				FROM party
				JOIN going USING (PARTYID)
				WHERE MAYBE = 0
				  AND LOCATIONID = '.$id,
				$cache_time,
				$key,
				$flags,
				$server
			);
			break;

		case 'organization':
			$result = memcached_simple_hash(['connect', 'going'],'
				SELECT COUNT(*), COUNT(DISTINCT going.USERID)
				FROM connect
				JOIN going ON ASSOCID=PARTYID
				WHERE MAYBE = 0
				  AND MAINTYPE = "organization"
				  AND ASSOCTYPE = "party"
				  AND MAINID = '.$id,
				$cache_time,
				$key,
				$flags,
				$server
			);
			break;

		case 'party':
			$result = memcached_single_array(['going','user'], "
				SELECT	COUNT(IF(MAYBE = 0, USERID, NULL)) AS CERTAIN_CNT,
						COUNT(IF(MAYBE = 0 AND SEX = 'F', 1, NULL)) AS FEMALE_CNT,
						COUNT(IF(MAYBE = 0 AND SEX = 'M', 1, NULL)) AS MALE_CNT,
						COUNT(IF(MAYBE, USERID, NULL)) AS MAYBE_CNT,
						COUNT(*) AS TOTAL_CNT
				FROM going
				JOIN user USING (USERID)
				WHERE PARTYID = $id",
				$cache_time
			);
			break;
		}
		break;

	case BGQRY_VISITORS_EVER:
		$result = memcached_single(['going_log', 'going', 'user'],'
			SELECT COUNT(*) FROM (
				SELECT DISTINCT gl.USERID
				FROM going_log AS gl
				LEFT JOIN going AS g
					 ON g.USERID = gl.USERID
					AND g.PARTYID = gl.PARTYID
					AND g.MAYBE = 0
				WHERE ACTION = "downgrade"
				  AND gl.PARTYID = '.$id.'
				  AND g.USERID IS NULL
				  AND g.MAYBE IN (0, 1)
				UNION 
				SELECT USERID
				FROM going
				WHERE MAYBE = 1
				  AND PARTYID = '.$id.'
			) AS all_going',
			$cache_time
		);
		break;

	case BGQRY_VIEWS:
		/** @noinspection PhpSwitchStatementWitSingleBranchInspection */
		switch ($element) {
		case 'party':
			$result = memcached_single_array('party_view','
				SELECT CAST(SUM(HITS) AS UNSIGNED), UNIX_TIMESTAMP(FROM_DAYS(MIN(DAYNUM)))
				FROM party_view
				WHERE PARTYID = '.$id,
				ONE_DAY
			);
			break;
		}
		break;

	case BGQRY_VOTES:
		$time_corrected_count = '
			CAST(
				SUM(  '.ONE_YEAR.' / ('.ONE_YEAR.'
					+ ('.(TODAYSTAMP + 25 * ONE_HOUR).'
					- votenew.STAMP) / 2)
				) AS UNSIGNED
			)';
		$votes = memcached_read(ARRAY_USE_HASH, 'votenew', '
			SELECT VOTEID,COUNT(*), '.$time_corrected_count.'
			FROM votenew
			WHERE TYPE = "'.$element.'"
			  AND ID = '.$id.'
			GROUP BY VOTEID
			WITH ROLLUP',
			$cache_time
		);

		switch ($element) {
		case 'location':
			$extravotes = memcached_read(ARRAY_USE_HASH, ['votenew', 'party'], '
				SELECT VOTEID,COUNT(*), '.$time_corrected_count.'
				FROM votenew
				JOIN party ON ID = PARTYID
				WHERE TYPE = "party"
				  AND LOCATIONID = '.$id.'
				GROUP BY VOTEID
				WITH ROLLUP',
				$cache_time
			);
			break;

		case 'organization':
			$extravotes = memcached_read(ARRAY_USE_HASH, ['votenew', 'connect'], '
				SELECT VOTEID, COUNT(*), '.$time_corrected_count.'
				FROM votenew
				JOIN connect
					 ON MAINTYPE = "party"
					AND MAINID = ID
					AND ASSOCTYPE = "organization"
				WHERE TYPE = "party"
				  AND ASSOCID = '.$id.'
				GROUP BY VOTEID
				WITH ROLLUP',
				$cache_time
			);
			break;

		default:
			$extravotes = null;
			break;
		}
		$result = [$votes, $extravotes];
		break;

	case BGQRY_AGES:
		if (!($party = memcached_party_and_stamp($id))) {
			$result = 0;
			break;
		}
		$elderly_age = 60;
		$ages = memcached_simple_hash(['going','user'],'
			SELECT	FLOOR(
					('.to_days($party['STAMP']).' - TO_DAYS(CONCAT(BIRTH_YEAR, "-", IF(BIRTH_MONTH, BIRTH_MONTH, 7), "-", IF(BIRTH_DAY, BIRTH_DAY, 15))))
					/
					'.ONE_YEAR_DAYS.'
				) AS AGE,
				COUNT(*)
			FROM going
			JOIN user USING (USERID)
			WHERE MAYBE = 0
			  AND BIRTH_YEAR != 0
			  AND going.PARTYID = '.$id.'
			GROUP BY AGE
			HAVING AGE < '.$elderly_age,
			$cache_time
		);
		if (!$ages) {
			$result = 0;
		} else {
			$new_total = 0;
			$new_age_total = 0;
			$age_total = 0;
			$new_ages = [];
			$total = array_sum($ages);
			foreach ($ages as $age => $cnt) {
				$age_total += $age * $cnt;
				$pct = 100 * $cnt / $total;
				if ($pct < 1) {
					continue;
				}
				$new_total += $cnt;
				$new_age_total += $age * $cnt;
				$new_ages[$age] = $cnt;
			}
			# ALL AGES, USEFUL AGES, AVERAGE AGE, AGE_TOTAL, TOTAL_AGE, AGE_COUNT
			$result = [
				$elderly_age,
					$ages,		$age_total / $total,			$age_total,		$total,
				$new_ages,	$new_age_total / $new_total,	$new_age_total,	$new_total
			];
		}
		break;

	case BGQRY_AGE_MEDIAN:
		if (!($party = memcached_party_and_stamp($id))) {
			$result = 0;
			break;
		}
		if (!($ages = get_bgquery(BGQRY_AGES, 'party', $id))) {
			$result = 0;
			break;
		}
		[,,,,, $ages,,, $age_count] = $ages;
		$result = memcached_single(['going','user'], /** @lang MariaDB */ '
			SELECT	('.	to_days($party['STAMP']).'
				-	TO_DAYS(CONCAT(BIRTH_YEAR, "-", IF(BIRTH_MONTH, BIRTH_MONTH, 7), "-", IF(BIRTH_DAY, BIRTH_DAY, 15)))
				)
				/ '.ONE_YEAR_DAYS.'
			FROM going
			JOIN user USING (USERID)
			WHERE MAYBE = 0
			  AND BIRTH_YEAR != 0
			  AND PARTYID = '.$id.'
			  AND FLOOR(
			  	(	'.to_days($party['STAMP']).' 
			  	-	TO_DAYS(CONCAT(BIRTH_YEAR, "-", IF(BIRTH_MONTH, BIRTH_MONTH, 7), "-", IF(BIRTH_DAY, BIRTH_DAY, 15)))
			  	)
			  	/
			  	'.ONE_YEAR_DAYS.') IN ('.implodekeys(',',$ages).')
			ORDER BY BIRTH_YEAR ASC,
				 BIRTH_MONTH ASC,
				 BIRTH_DAY ASC
			LIMIT '.(int)($age_count >> 1).', 1',
			$cache_time
		);
		break;

	case BGQRY_VISITORMAP_MAYBE:
		$maybe = true;
		# fall through
	case BGQRY_VISITORMAP:
		$key = null;
		switch ($element) {
		case 'party':
			$result = memcached_read(ROW_USE_MULTI_HASH, ['going', 'user', 'city'], '
				/* indices:2 */
				SELECT	IF(ISNULL(city.CITYID), user.COUNTRYID, city.COUNTRYID) as _COUNTRYID,
					 user.CITYID AS _CITYID, LATITUDE, LONGITUDE, city.NAME, COUNT(*) AS CNT
				FROM going
				JOIN user USING (USERID)
				LEFT JOIN city USING (CITYID)
				WHERE MAYBE = '.(isset($maybe) ? '1' : '0').'
				  AND PARTYID = '.$id.'
				GROUP BY _COUNTRYID,
						 _CITYID
				ORDER BY CNT DESC,
					 NAME ASC',
				$cache_time,
				$key,
				DB_NON_ASSOC
			);
			break;

		case 'location':
			$result = memcached_read(ROW_USE_MULTI_HASH, ['going', 'user', 'party', 'location', 'city'], '
				/* indices:2 */
				SELECT	IF(ISNULL(city.CITYID), user.COUNTRYID, city.COUNTRYID) as _COUNTRYID,
					user.CITYID AS _CITYID, city.LATITUDE, city.LONGITUDE, city.NAME, COUNT(*) AS CNT
				FROM going
				JOIN party USING (PARTYID)
				JOIN location USING (LOCATIONID)
				JOIN user ON user.USERID = going.USERID
				LEFT JOIN city AS city ON city.CITYID = user.CITYID
				WHERE MAYBE = '.(isset($maybe) ? '1' : '0').'
				  AND LOCATIONID = '.$id.'
				GROUP BY _COUNTRYID,
						 _CITYID
				ORDER BY CNT DESC,
						 NAME ASC',
				$cache_time,
				$key,
				DB_NON_ASSOC | $flags,
				$server
			);
			break;

		case 'city':
			$result = memcached_read(ROW_USE_MULTI_HASH, ['going','user','party','location','city'], '
				/* indices:2 */
				SELECT	IF(ISNULL(city.CITYID), user.COUNTRYID, city.COUNTRYID) as _COUNTRYID,
					user.CITYID AS _CITYID, city.LATITUDE, city.LONGITUDE, city.NAME, COUNT(*) AS CNT
				FROM going AS g
				JOIN (	SELECT DISTINCT PARTYID
						FROM party
						WHERE CITYID = '.$id.'
						UNION
						SELECT DISTINCT PARTYID
						FROM party
						JOIN location USING (LOCATIONID)
						WHERE location.CITYID = '.$id.'
						UNION
						SELECT DISTINCT PARTYID
						FROM party
						JOIN boarding USING (BOARDINGID)
						WHERE boarding.CITYID = '.$id.'
				) AS parties_in_city USING (PARTYID)
				JOIN user ON user.USERID = going.USERID
				LEFT JOIN city ON city.CITYID = user.CITYID
				WHERE MAYBE = '.(isset($maybe) ? '1' : '0').'
				GROUP BY _COUNTRYID,
						 _CITYID
				ORDER BY CNT DESC,
					 NAME ASC',
				$cache_time,
				$key,
				DB_NON_ASSOC | $flags,
				$server
			);
			break;

		case 'organization':
			$result = memcached_read(ROW_USE_MULTI_HASH, ['going', 'user', 'connect', 'city'], '
				/* indices:2 */
				SELECT	IF(ISNULL(city.CITYID), user.COUNTRYID, city.COUNTRYID) as _COUNTRYID,
					user.CITYID AS _CITYID, LATITUDE, LONGITUDE, city.NAME, COUNT(*) AS CNT
				FROM going
				JOIN connect
					 ON MAINTYPE = "party"
					 AND MAINID = PARTYID
					 AND ASSOCTYPE = "organization"
				JOIN user USING (USERID)
				LEFT JOIN city USING (CITYID)
				WHERE MAYBE = '.(isset($maybe) ? '1' : '0').'
				  AND ASSOCID = '.$id.'
				GROUP BY _COUNTRYID,
						 _CITYID
				ORDER BY CNT DESC,
						 NAME ASC',
				$cache_time,
				$key,
				DB_NON_ASSOC | $flags,
				$server
			);
			break;
		}
		break;
	}
	if ($cache) {
		$__cache[$queryid][$element][$id] = $result ?: false;
	}
	return $result;
}

function get_bgquery(
	int	$queryid,
	string $element,
	int	$id
): mixed {

	$wherep = '
			QUERYID = '.$queryid.'
		AND ELEMENT = "'.$element.'"
		AND ID = '.$id;

	$try = 1;

	if (/*	!have_admin($element)
	&&*/	(	!isset($_REQUEST['NOMEMCACHE'])
		||	!str_contains($_SERVER['QUERY_STRING'], 'NOMEMCACHE')
		)
	) {
		// $cache_time = get_cachetime($queryid,$element);
		$cache_time = BGQRY_CACHE_TIME;

		while ($try) {
			$result = db_single('bgqueryv2','
				SELECT RESULT
				FROM bgqueryv2
				WHERE '.$wherep
			);
			if ($result !== null
			&&	$result !== false
			) {
				db_update('bgqueryv2','
				UPDATE bgqueryv2 SET
					EXPIRES = '.(CURRENTSTAMP + $cache_time).'
				WHERE EXPIRES = 0
				  AND '.$wherep
				);
				if ($try === 2) {
					db_releaselock($wherep);
				}
				return igbinary_unserialize($result);
			}
			if ($try === 2) {
				break;
			}
			if (!db_getlock($wherep, 5)) {
				break;
			}
			$lock = true;
			$try = 2;
		}
	}
#	error_log('WARNING could not get cached bgquery for '.$queryid.':'.$element.':'.$id);

	if (false !== ($result = do_background_query($queryid, $element, $id))) {
		set_background_query($queryid, $element, $id, $result);
	}
	if (!empty($lock)) {
		db_releaselock($wherep);
	}
	return $result;
}

function set_background_query(
	int		$queryid,
	string	$element,
	int		$id,
	mixed	$result
): bool {
	$result = addslashes(igbinary_serialize($result));
	return db_replace('bgqueryv2',"
	REPLACE INTO bgqueryv2 SET
		QUERYID		= $queryid,
		ELEMENT		= '$element',
		ID			= $id,
		RESULT		= '$result',
		EXPIRES		= 0"
	);
}
