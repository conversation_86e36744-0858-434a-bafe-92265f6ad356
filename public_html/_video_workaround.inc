<?php

function show_chrome_video_workaround(string $id): void {
	include_js('https://www.youtube.com/iframe_api', JS_EXTERNAL);

	?><script>
	function onYouTubeIframeAPIReady() {
		let done_reload = false;

		player = new YT.Player('<?= $id ?>', {
			events: {
				'onError': function(event) {
					if (!done_reload) {
						console.log('reloading');
						let video = getobj('<?= $id ?>');
						if (video) {
							video.src = video.src;
						}
						done_reload = true;
					}
				}
			}
		});
	}
	</script><?
}
