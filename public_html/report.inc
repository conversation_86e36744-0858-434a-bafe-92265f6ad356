<?php

function preamble() {
	if ($reportid = have_idnumber($_REQUEST, 'sID')) {
		if ($reportid === 13201) {
			moved_permanently(get_element_href('interview', 340));
		}
		if (($report = memcached_report($reportid))
		&&	$report['PARTYID']
		) {
			if (!($party = memcached_party_and_stamp($report['PARTYID']))) {
				mail_log("NO party for PARTYID {$report['PARTYID']} and REPORTID $reportid", error_log: 'ERROR');
			} else {
				$report += $party;
			}
		}
	}
}
function display_title(): void {
	if ($reportid = have_idnumber($_REQUEST, 'sID')) {
		if ($report = memcached_report($reportid)) {
			if ($report['TITLE']) {
				echo escape_utf8($report['TITLE']);
			} else {
				echo $report['PARTYID'] ? __('element:report_of', DO_UBBFLAT, ['PARTYID' => $report['PARTYID']]) : element_name('report');
				if ($report['STAMP']) {
					?> (<? _date_display($report['STAMP']); ?>)<?
				}
				if ($report['USERID']) {
					?> <? echo __('attribution:user', DO_UBBFLAT, ['USERID' => $report['USERID']]);
				}
			}
		} else {
			echo element_name('report');
		}
		# show_current_action($reportid);
	} elseif (
		($userid = have_idnumber($_REQUEST, 'subID'))
	&&	$_REQUEST['ACTION'] === 'user'
	) {
		if (!memcached_nick($userid)) {
			mail_log("user $userid does not exist", error_log: 'ERROR');
			not_found();
			return;
		}
		echo __C('elements:report_by', DO_UBBFLAT, ['USERID' => $userid]);
	} else {
		echo element_plural_name('report');
		# show_current_action();
	}
}
function perform_commit(): ?bool {
	return match ($_REQUEST['ACTION']) {
		default		=> null,
		'commit'	=> report_commit(),
		'accept',
		'unaccept'	=> (require_once '_accept.inc') && item_set_accept($_REQUEST['ACTION']),
		'remove'	=> (require_once '_remove.inc') && remove_element(),
		'anonymize'	=> report_set_anonymous(true),
		'onymize'	=> report_set_anonymous(false),
	};
}

function display_body(): void {
	switch ($_REQUEST['ACTION']) {
	default:			not_found(); return;
	case 'visited':
	case 'doubted':
	case null:			report_display_overview(); return;
	case 'user':		report_display_user(); return;
	case 'single':		report_display_single() ? report_increase_counter() : null; return;
	case 'commit':
	case 'comments':
	case 'comment':
	case 'anonymize':
	case 'onymize':		report_display_single(); return;
	case 'archive':		report_display_archive(); return;
	case 'remove':		$GLOBALS['commitresult'] ? report_display_overview() : report_display_single(); return;
	case 'form':		report_display_form(); return;
	case 'organization':report_display_organization(); return;
	}
}

function report_set_anonymous(bool $new_anonymous): bool {
	$set_anonymous = $new_anonymous ? '1' : '0';
	if (!($reportid = require_idnumber($_REQUEST, 'sID'))
	||	!require_existing_report($reportid)
	||	!($report = memcached_report($reportid))
	||	!require_self_or_admin($report['USERID'], 'report')
	||	!db_insert('report_log', "
		INSERT INTO report_log
		SELECT * FROM report
		WHERE REPORTID = $reportid
		  AND ANONYMOUS != $set_anonymous")
	||	db_affected()
	&&	!db_update('report','
		UPDATE report SET
			MUSERID		= '.CURRENTUSERID.',
			MSTAMP		= '.CURRENTSTAMP.",
			ANONYMOUS	= $set_anonymous
		WHERE REPORTID = $reportid")
	) {
		return false;
	}
	register_notice($new_anonymous ? 'report:notice:anonymized_LINE' : 'report:notice:onymized_LINE');
	return true;
}
function report_menu(?array $report = null): void {
	layout_open_menu();
	layout_menuitem(Eelement_name('overview'),'/report');
	layout_close_menu();
	if (!have_user()) {
		return;
	}
	layout_open_menu();
	if (!$report) {
		layout_menuitem(__C('action:add'),'/report/form');
	} else {
		$reportid = $report['REPORTID'];
		$reporthref = "/report/$reportid";
		$my_report = CURRENTUSERID === $report['USERID'];
		$report_admin = have_admin('report');
		if ($report_admin
		||	$my_report
		&&	CURRENTSTAMP - ($report['MSTAMP'] ?: $report['CSTAMP']) < 2 * ONE_WEEK
		) {
			layout_menuitem(__C('action:change'), $reporthref.'/form');
		}
		if ($report_admin
		||	$my_report
		) {
			if ($report_admin) {
				if ($report['ACCEPTED']) {
					layout_menuitem(__C('action:reject'),$reporthref.'/unaccept');
				} else {
					layout_menuitem(__C('action:accept'),$reporthref.'/accept');
				}
			}
			if ($report['ANONYMOUS']) {
				layout_menuitem(__C('action:onymize'),$reporthref.'/onymize');
			} else {
			 	layout_menuitem(__C('action:anonymize'),$reporthref.'/anonymize');
			}
			if ($report_admin) {
				require_once '_connect.inc';
				layout_open_menuitem();
				connect_menuitem();
				layout_close_menuitem();

				layout_continue_menu();
				show_element_menuitems();
			}
		}
	}
	layout_close_menu();
}

function report_display_organization(): void {
	require_once '_itemlist.inc';
	_itemlist_display_overview_header();
	_itemlist_display_menu();

	if (!($organization_id = require_idnumber($_REQUEST,'subID'))) {
		not_found();
		return;
	}

	layout_open_box('white');
	layout_box_header(Eelement_plural_name('report').' '.MIDDLE_DOT_ENTITY.' '.get_element_link('organization', $organization_id));
	layout_close_box();

	_itemlist_display();
}

function report_display_overview(): void {
	require_once '_itemlist.inc';
	_itemlist_display_overview_header();
	_itemlist_display_menu();

	if (have_user()) {
		require_once '_going.inc';
		  $maybe_going = get_all_going(CURRENTUSERID, GOING_MAYBE);
		$certain_going = get_all_going(CURRENTUSERID, GOING_CERTAIN);
		if (!($cnts = memcached_single_array('report', '
			SELECT	COUNT(*),
					COUNT(IF(PARTYID IN ('.(implodekeys(', ', $maybe_going)   ?: -1).'), 1, NULL)),
					COUNT(IF(PARTYID IN ('.(implodekeys(', ', $certain_going) ?: -1).'), 1, NULL))
			FROM report
			WHERE ACCEPTED'))
		) {
			return;
		}
		[$total_cnt, $maybe_cnt, $certain_cnt] = $cnts;
		if ($maybe_cnt || $certain_cnt) {
			?><form method="get" action="/report" onsubmit="location.href = '/report/' + (this.ACTION.value ? this.ACTION.value : ''); return false;"><?
			?><div class="center block"><?
			?><select onchange="if(!this.form.onsubmit || this.form.onsubmit())this.form.submit()" name="ACTION"><?
				?><option value=""><?= element_plural_name('all_report') ?> <?= MIDDLE_DOT_ENTITY ?> <?= $total_cnt ?></option><?
				if ($certain_cnt) {
					?><option<?
					if ($_REQUEST['ACTION'] === 'visited') {
						?> selected<?
					}
					?> value="visited"><?= element_name('visited_party',$certain_cnt) ?> <?= MIDDLE_DOT_ENTITY ?> <?= $certain_cnt ?></option><?
				}
				if ($maybe_cnt) {
					?><option<?
					if ($_REQUEST['ACTION'] === 'doubted') {
						?> selected<?
					}
					?> value="doubted"><?= element_name('doubted_party',$maybe_cnt) ?> <?= MIDDLE_DOT_ENTITY ?> <?= $maybe_cnt ?></option><?
				}
			?></select><?
			?></div><?
			?></form><?
			switch ($_REQUEST['ACTION']) {
			case 'doubted':
				$goings = $maybe_going;
			case 'visited':
				if (!isset($goings)) {
					$goings = $certain_going;
				}
				if (!$goings) {
					error_log('bad goings for '.CURRENTUSERID.' @ '.$_SERVER['REQUEST_URI'],0);
					break;
				}
				global $__report_goings;
				$__report_goings = $goings;
				break;
			}
		}
	}
	_itemlist_display_prelist();
	_itemlist_display();
}

function report_display_user(): void {
	if (!($userid = require_idnumber($_REQUEST, 'subID'))) {
		not_found();
		return;
	}
	require_once '_itemlist.inc';
	layout_show_section_header('<h1>'.__C('elements:report_by', DO_UBB, ['USERID' => $userid]).'</h1>');
	_itemlist_display(userid: $userid);
}

function report_display_archive(): void {
	require_once '_itemlist.inc';
	_itemlist_display_archive_header();
	_itemlist_display_menu();
	_itemlist_display_archive();
}
function report_display_single(): void {
	if (!($reportid = require_idnumber($_REQUEST, 'sID'))) {
		return;
	}
	if ($reportid === 13221) {
		include_og('og:image','https://photo.partyflock.nl/960461/main/<EMAIL>');
		include_og('og:image:width',  1920);
		include_og('og:image:height', 1280);
	}
	if (false === ($report = memcached_single_assoc_if_not_self_nor_admin(
		memcached_single('report', 'SELECT USERID FROM report WHERE REPORTID='.$reportid, ONE_DAY),
		'report',
		'report','
		SELECT REPORTID, USERID, PARTYID, ACCEPTED, CSTAMP, MUSERID, MSTAMP, BODY, ANONYMOUS, TITLE
		FROM report
		WHERE REPORTID = '.$reportid))
	) {
		return;
	}
	if (!$report) {
		not_found('report', $reportid);
		return;
	}
	$report_admin = have_admin('report');
	$report['REPORTID'] = $reportid;
	$party = memcached_party_and_stamp($report['PARTYID']);
	$report['PNAME'] = !empty($party['NAME']) ? $party['NAME'] : '';
	layout_show_section_header($report);

	report_menu($report);

	if (!have_admin('report')
	&&	!$report['ACCEPTED']
	&&	CURRENTUSERID !== $report['USERID']
	) {
		register_error('report:error:rejected_LINE', ['ID' => $reportid]);
		no_permission();
		return;
	}
	require_once '_ticketlist.inc';
	show_connected_tickets();
	if ($report_admin) {
		_connect_display_form('report', $reportid);
	}
	?><article itemscope itemtype="https://schema.org/CreativeWork"><?
	layout_open_box($report['ACCEPTED'] ? 'report body' : 'unaccepted-element report body');
	?><header><?
	layout_open_box_header();

	$func = $report['TITLE'] ? __(...) : __C(...);

	$deftitle = $report['PARTYID'] ? $func('element:report_of', DO_UBB, ['PARTYID' => $report['PARTYID']]) : element_name('report', 1, !$report['TITLE']);
	$datepart = empty($party['STAMP']) ? '' : ', '._date_get($party['STAMP']);

	if ($report['TITLE']) {
		?><h1 itemprop="headline"><span itemprop="name"><?= escape_utf8($report['TITLE']) ?></span></h1><br /><?
		?><span class="nb small"><?= $deftitle, $datepart ?></span><?
	} else {
		?><h1 itemprop="headline"><?
		?><span itemprop="name"><?= $deftitle, $datepart ?></span><?
		?></h1><?
	}
	layout_close_box_header();
	?></header><?

	require_once '_itemlist.inc';
	itemlist_show_publish_info($report);

	require_once '_uploadimage.inc';
	ob_start();
	$img = uploadimage_show_with_fallback('report', $reportid, flags: UPIMG_SCHEMA | UPIMG_SHOW_HISTORY, size: 'regular');
	$imgdata = ob_get_clean();

	if ($reportid === 13208
	||	$reportid === 13210
	||	$reportid === 13221
	) {
		# hide connection, nicer layout then
	} else {
		?><div class="<?
		if (!SMALL_SCREEN) {
			?>right-float <?
		}
		?>center"><?
		echo $imgdata;
		_connect_display('report', $reportid);
		?></div><?
	}


	?><div class="lclr minp block body" itemprop="articleBody"><?=
		make_all_html(beautify_text($report['BODY']), UBB_UTF8, 'report', $reportid)
	?></div><?
	layout_display_alteration_note($report, true);
	layout_close_box();

	require_once '_commentlist.inc';
	$cmts = new _commentlist();
	$cmts->item($report);
	$cmts->display();

	counthit('report', $reportid);

	?></article><?
}
function require_existing_report(int $reportid): bool {
	if (false === ($exists = db_single('report', 'SELECT 1 FROM report WHERE REPORTID = '.$reportid))) {
		return false;
	}
	if (!$exists) {
		return not_found('report', $reportid);
	}
	return true;
}
function report_display_form(): void {
	layout_show_section_header();
	if (!require_user()) {
		return;
	}
	$report_admin = have_admin('report');
	$reporter	  = have_admin('reporter');

	if ($reportid = $_REQUEST['sID']) {
		if (!($report = db_single_assoc('report', 'SELECT * FROM report WHERE REPORTID = '.$reportid))) {
			if ($report !== false) {
				not_found();
			}
			return;
		}
		// only admin or creating user can change a report

		if (!$report_admin) {
			if (CURRENTUSERID !== $report['USERID']
			||	CURRENTSTAMP - ($report['MSTAMP'] ?: $report['CSTAMP']) >= 2*7*24*3600
			) {
				require_admin('report');
				return;
			}
		}
	} else {
		$report = [];
	}
	if (false === ($parties = db_rowuse_hash(['going', 'party'], '
		SELECT PARTYID, NAME, STAMP
		FROM going
		JOIN party USING (PARTYID)
		WHERE MAYBE = 0
		  AND going.USERID='.($report['USERID'] ?? CURRENTUSERID).'
		  AND STAMP < '.(CURRENTSTAMP - 5 * ONE_HOUR).'
		  AND ACCEPTED
		  AND MOVEDID = 0
		  AND CANCELLED = 0
		  AND POSTPONED = 0
		ORDER BY STAMP DESC'))
	) {
		return;
	}
	if ($report && !isset($parties[$report['PARTYID']])) {
		if (!($parties[$report['PARTYID']] = memcached_party_and_stamp($report['PARTYID']))) {
			return;
		}
	} elseif ($parties === false) {
		return;
	}
	if (!$parties) {
		register_error('report:error:not_going_anywhere_TEXT', DO_NL2BR);
		return;
	}
	layout_open_box('white');
	?><div class="block"><?= __('report:info:above_form_info_TEXT', DO_UBB) ?></div><?
	layout_close_box();
	if (false === ($last_reportid = db_single('report', 'SELECT MAX(REPORTID) FROM report'))) {
		return;
	}
	?><form<?
	?> onsubmit="return submitForm(this);"<?
	?> enctype="multipart/form-data"<?
	?> accept-charset="utf-8"<?
	?> method="post"<?
	?> action="/report<? if (isset($report)) { ?>/<? echo $reportid; } ?>/commit"><?
	?><input type="hidden" name="FORMSTAMP" value="<?= CURRENTSTAMP ?>" /><?
	?><input type="hidden" name="LAST_REPORTID" value="<?= $last_reportid ?>" /><?

	include_js('js/form/report');

	$spec = explain_table('report');

#	require_once '_uploadimage.inc';
#	show_uploadimage_for_element_form('report',$reportid);

	layout_open_box('report');
	layout_open_table('fw');
	layout_start_row();
		echo Eelement_name('party');
		layout_field_value();
		if (!$report
		&&	($partyid = have_idnumber($_REQUEST, 'PARTYID'))
		&&	going_to_party_askonce($partyid)
		) {
			?><input type="hidden" name="PARTYID" value="<?= $partyid ?>" /><?
			?><b><?= get_element_link('party', $partyid) ?></b><?

			$party = memcached_party_and_stamp($partyid);
		} else {
			?><select name="PARTYID" required onchange="Pf.reportChangeEvent(this);"><?
			$currmon = 0;
			foreach ($parties as $partyid => $party) {
				[$year, $month] = _getdate($party['STAMP']);
				if ($currmon !== $month) {
					if ($currmon) {
						?></optgroup><?
					}
					?><optgroup label="<?= _month_name($month); ?> <?= $year; ?>"><?
					$currmon = $month;
				}
				?><option<?
				if (isset($report)
				&&	$report['PARTYID'] === $partyid
				) {
					?> selected<?
				}
				?> data-placeholder="<?= escape_utf8($party['NAME']) ?>"<?
				?> value="<?= $partyid ?>"><? _date_display_numeric($party['STAMP']);
				?>: <?= escape_utf8($party['NAME']);
				?></option><?
			}
			if ($currmon) {
				?></optgroup><?
			}
			?></select><?
			if ($report) {
				$party = memcached_party_and_stamp($report['PARTYID']);
			}
		}
	layout_restart_row();
		echo Eelement_name('title');
		layout_field_value();
		show_input([
			'type'			=> 'text',
			'name'			=> 'TITLE',
			'placeholder'	=> $party['NAME'] ?? null,
			'value_utf8'	=> $report,
			'maxlength'		=> 1024,
		]);

	if (have_admin(['report','reporter'])) {
		layout_restart_row();
			$checked = !empty($report['OFFICIAL']);
			?><label for="official"><?= __C('status:official') ?></label><?
			layout_field_value();
			show_input([
				'name'		=> 'OFFICIAL',
				'checked'	=> $checked ? true : false,
				'value'		=> 1,
				'type'		=> 'checkbox',
			]);
	}

	layout_restart_row();
		echo Eelement_name('report');
		layout_field_value();
		?><textarea<?
		?> class="growToFit"<?
		?> required="required"<?
		?> minlength="1000"<?
		?> name="BODY"<?
		?> cols="75"<?
		?> rows="20"><?
		if (!empty($report['BODY'])) {
			echo escape_utf8($report['BODY']);
		}
		?></textarea><?
	layout_stop_row();
	layout_close_table();
	layout_close_box();
	?><div class="block"><input type="submit" value="<?= __(isset($report) ? 'action:change' : 'action:add'); ?>" /></div><?
	?></form><?
}
function report_commit(): bool {
	if (!require_user()
	||	!require_idnumber($_POST,'PARTYID')
	||	!require_something_trim($_POST, 'BODY')
	||	!optional_number($_REQUEST,'sID')
	) {
		return false;
	}
	require_once '_ubb_preprocess.inc';
	if (empty($_POST['PARTYID'])) {
		_error('Geen feest opgegeven!');
		return false;
	}

	$utf8 = true;

	$_POST['BODY'] = beautify_text($_POST['BODY']);
	$len = strlen($_POST['BODY']);
	if ($len < 1000) {
		_error(
			'Je verslag heeft minder dan 1000 karakters.<br />'.
			'De bedoeling dat je een verslag van redelijk formaat schrijft.<br />'.
			'Wil je slechts een korte opmerking plaatsen, doe dat dan bij het feest. (zie '.get_element_link('party',$_POST['PARTYID']).')'
		);
		return false;
	}
	$body = _ubb_preprocess($_POST['BODY'], utf8: true);
	$title = _ubb_preprocess($_POST['TITLE'], utf8: true);

	if ($reportid = $_REQUEST['sID']) {
		if (!have_admin('report')) {
			if (!($report = db_single_assoc('report', '
				SELECT USERID, MSTAMP, CSTAMP
				FROM report
				WHERE REPORTID = '.$reportid,
				DB_USE_MASTER))
			) {
				if ($report !== false) {
					not_found();
				}
				return false;
			}
			if (CURRENTUSERID !== $report['USERID']
			||	CURRENTSTAMP - ($report['MSTAMP'] ?: $report['CSTAMP']) >= 2 * ONE_DAY
			) {
				require_admin('report');
				return false;
			}
		}
		// create log entry

		if (!db_insert('report_log', '
			INSERT INTO report_log
			SELECT * FROM report
			WHERE REPORTID = '.$reportid)
		||	!db_update('report','
			UPDATE report SET
				MSTAMP	 = '.CURRENTSTAMP.',
				MUSERID	 = '.CURRENTUSERID.',
				PARTYID	 = '.$_POST['PARTYID'].',
				OFFICIAL = '.(have_admin(['reporter','report']) ? (isset($_POST['OFFICIAL']) ? "b'1'" : "b'0'") : 'OFFICIAL').',
				BODY	 = "'.addslashes($body).'",
				TITLE	 = "'.addslashes($title).'",
				ACCEPTED = 1
			WHERE REPORTID = '.$reportid)
		) {
			return false;
		}
		register_notice('report:notice:changed_LINE');
	} else {
		if (!require_idnumber($_POST,'LAST_REPORTID')
		||	false === require_number($_POST,'FORMSTAMP')
		) {
			return false;
		}
		if (false === ($reportid = db_single('report', "
			SELECT REPORTID
			FROM report
			WHERE REPORTID > {$_POST['LAST_REPORTID']}
			  AND FORMSTAMP = {$_POST['FORMSTAMP']}
			  AND USERID = ".CURRENTUSERID))
		) {
			return false;
		}
		if ($reportid) {
			$_REQUEST['sID'] = $reportid;
			register_warning('report:warning:already_added_LINE');
			return true;
		}
		if (!db_insert('report','
			INSERT INTO report SET 
				PSTAMP	  = '.CURRENTSTAMP.',
				CSTAMP	  = '.CURRENTSTAMP.',
				USERID	  = '.CURRENTUSERID.',
				OFFICIAL  = b\''.(have_admin(['report','reporter']) && isset($_POST['OFFICIAL']) ? 1 : 0).'\',
				FORMSTAMP = '.$_POST['FORMSTAMP'].',
				PARTYID	  = '.$_POST['PARTYID'].',
				BODY	  = "'.addslashes($body).'",
				TITLE	  = "'.addslashes($title).'",
				ACCEPTED  = 1')
		) {
			return false;
		}
		$reportid = $_REQUEST['sID'] = db_insert_id();
		register_notice('report:notice:added_LINE');
	}
	require_once '_update.inc';
	update_element('report', $reportid, $body, utf8: true);
	return true;
}
