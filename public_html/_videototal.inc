<?php

declare(strict_types=1);

function show_video_statistics_row(bool $restart = true): void {
	$element = $_REQUEST['sELEMENT'];
	$id		 = $_REQUEST['sID'];

	require_once '_videolist.inc';
	$videolist = new _videolist();
	$videolist->connected_to($element, $id);
	if (!($infos = $videolist->query(-1))) {
		return;
	}
	foreach ($infos as $info) {
		if (!$info['TOTAL']) {
			continue;
		}
		$link =	$info['TOTAL'] === 1
			?	get_element_href('video',$info['VIDEOID'])
			:	"/video/$element/$id".($info['MUSICONLY'] ? '/musiconly' : '');

		if ($restart) {
			layout_restart_reverse_row();
		} else {
			layout_start_reverse_row();
		}
		?><a href="<?= $link ?>"><?= $info['TOTAL'] ?></a><?
		layout_value_field();
		?><a href="<?= $link ?>"><?=
			element_name($info['MUSICONLY'] ? 'video_with_only_music' : 'video',$info['TOTAL'])
		?></a><?

		if (!$restart) {
			/** @noinspection PhpExpressionResultUnusedInspection */
			layout_stop_row();
		}
	}
}
