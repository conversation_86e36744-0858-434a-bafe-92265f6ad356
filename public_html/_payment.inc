<?php

function archive_payment() {
	if (!($idealid = require_idnumber($_POST,'IDEALID'))) {
		bail(400);
	}
	global $currentuser;
	$currentuser = new _currentuser(SKIP_SETTINGS);
	if (!require_admin('invoice')) {
		bail(403);
	}
	if (!db_insert('ideal_seen','
		INSERT IGNORE INTO ideal_seen SET
			CUSERID	='.CURRENTUSERID.',
			CSTAMP	='.CURRENTSTAMP.',
			IDEALID	='.$idealid)
	) {
		bail(500);
	}
	bail(200);
}
