<?php

declare(strict_types=1);

require_once '_language.inc';
require_once '_tagger.inc';
require_once '_namefix.inc';

function display_tagger(): void {
	global $langid;
	$langid = have_number($_POST, 'LANGID') !== false ? $_POST['LANGID'] : CURRENTLANGID;

	if (!empty($_POST['BODY'])
	&&	false === ($barebody = setup_tagger($_POST['BODY'], $langid))
	) {
		return;
	}

	include_js('js/form/tagger');

	?><form<?
	?> accept-charset="utf-8"<?
	?> method="post"<?
	?> action="/test/tagger"<?
	?> onsubmit="return submitForm(this);"><?
	?><div class="block"><?

	?><textarea<?
	?> onkeyup="Pf_detectLanguage(this.value, this.form.LANGID);"<?
	?> cols="80"<?
	?> rows="20"<?
	?> name="BODY"<?
	?> id="mainbody"<?
	?> autofocus><?
	if (!empty($barebody)) {
		global $makedisplay;
		$makedisplay = false;
		echo escape_utf8(preg_replace_callback('"\x12(\d+)\x13"u', 'tagreplace', $barebody));
	}
	?></textarea><?
	?></div><?
	if (!have_post()) {
		?><div class="block"><?
		?><input type="submit" value="<?= __('action:send') ?>" /> <?=
		element_name('language') ?>: <? show_languages_select(0);
		?></div><?
		?><div><?
		?><label><input type="checkbox" value="1" name="INCLUDE_THE_DEAD" /> <?= __('tagger:include_the_dead') ?></label><?
		?></div><?
	}
	?></form><?
}


function setup_tagger(
	string	$body,
	int		$inplangid,
	?array	$do_elems = null,
): string|false {
	global $langid;
	$langid = $inplangid;
	$keeps = [];
	$keepi = -1;
	require_once '_namefix.inc';
	$body = get_fixed_utf8_name(utf8_mytrim($body));

	global	$repid,
			$tagreps,
			$makedisplay;

	# keep existing ubb tags and don't do anything with them
	$body = preg_replace_callback(
			'"'.
			'\[([^\s\]]+)(?:=[^]]*)?].*?\[/$1]|'.
			'\[([^]]*)]|'.
			'[\w.-]+\.\w{2,4}\b'.
			'"u',
		 static function(array $match) use (&$keeps, &$keepi): string  {
			$keeps[++$keepi] = $match;
			return "\x1E$keepi\x1F";
		},$body
	);
	$include_the_dead = !empty($_POST['INCLUDE_THE_DEAD']);
	$barebody = $body;
	$repid = 0;
	$qries = [];
	foreach ($do_elems ?: ['artist', 'location', 'organization', 'party'] as $elem) {
		# NOTE: Using BINARY modifier to make sure NAMEs are not squashed on collation
		switch ($elem) {
		case 'artist':
			$qries['artist'] = /** @lang MariaDB */ "(
				SELECT BINARY LOWER(NAME) AS NAME, 'artist' AS ELEMENT, ARTISTID
				FROM artist
				WHERE LENGTH(NAME) > 2  ".($include_the_dead ? '' : '
				  AND STOPPED = 0
				  AND DECEASED = 0
				  AND FOLLOWUPID = 0')."
				UNION
				SELECT BINARY LOWER(alt.NAME) AS NAME, ELEMENT, ID
				FROM alternate_name AS alt
				JOIN artist ON ID = ARTISTID
				WHERE ELEMENT = 'artist'
				  AND LENGTH(alt.NAME) > 2  ".($include_the_dead ? '' : '
				  AND STOPPED = 0
				  AND DECEASED = 0
				  AND FOLLOWUPID = 0').'
			)';
			break;

		case 'location':
			$qries['location'] = /** @lang MariaDB */ "(
				SELECT BINARY LOWER(NAME) AS NAME, 'location' AS ELEMENT, LOCATIONID
				FROM location
				WHERE LENGTH(NAME) > 2 ".($include_the_dead ? '' : '
				  AND DEAD = 0
				  AND FOLLOWUPID = 0')."
				UNION
				SELECT BINARY LOWER(alt.NAME) AS NAME, ELEMENT, ID
				FROM alternate_name AS alt
				JOIN location ON ID = LOCATIONID
				WHERE ELEMENT = 'location'
				  AND LENGTH(alt.NAME) > 2 ".($include_the_dead ? '' : '
				  AND DEAD = 0
				  AND FOLLOWUPID = 0').'
			)';
			break;

		case 'party':
			$qries['party'] = /** @lang MariaDB */ "(
				SELECT BINARY LOWER(NAME) AS NAME, 'party' AS ELEMENT, PARTYID
				FROM party
				WHERE LENGTH(NAME) > 2
				  AND STAMP >= UNIX_TIMESTAMP()
				UNION
				SELECT BINARY LOWER(alt.NAME) AS NAME, ELEMENT, ID
				FROM alternate_name AS alt
				JOIN party ON ID = PARTYID
				WHERE ELEMENT = 'party'
				  AND LENGTH(alt.NAME) > 2
				  AND STAMP >= UNIX_TIMESTAMP()
			)";
			break;

		case 'organization':
			$qries['organization'] = /** @lang MariaDB */ "(
				SELECT BINARY LOWER(NAME) AS NAME, 'organization' AS ELEMENT, ORGANIZATIONID
				FROM organization
				WHERE LENGTH(NAME) > 2  ".($include_the_dead ? '' : '
				  AND DEADSTAMP = 0
				  AND FOLLOWUPID = 0')."
				UNION
				SELECT BINARY LOWER(alt.NAME) AS NAME, ELEMENT, ID
				FROM alternate_name AS alt
				JOIN organization ON ID = ORGANIZATIONID
				WHERE ELEMENT = 'organization'
				  AND LENGTH(alt.NAME) > 2  ".($include_the_dead ? '' : '
				  AND DEADSTAMP = 0
				  AND FOLLOWUPID = 0').'
			)';
			break;
		}
	}
	if (!($elements = db_same_hash(['artist', 'location', 'party', 'organization', 'alternate_name'],
		implode(' UNION ',$qries).'
		ORDER BY LENGTH(NAME) DESC,
			ELEMENT = "organization" DESC,
			ELEMENT = "location" DESC,
			ELEMENT = "artist" DESC,
			ELEMENT = "party" DESC'))
	) {
		return false;
	}
	$regex_delimiter = chr(1);
	foreach ($elements as $name => $repinfo) {
		$name = (string)$name;
		$quoted_name	   = preg_quote($name, $regex_delimiter);
		$quoted_name_ascii = preg_quote(utf8_to_ascii($name), $regex_delimiter);

		$barebody = preg_replace_callback(
			$regex_delimiter.
				'\b(?<!'."\x12".')(?<name>'.$quoted_name.'|'.$quoted_name_ascii.')'.
					# Positive look-ahead:
					'(?=[ .,;:!?\'\"*#%^&\b\s()\[\]{}\'\"$'.MATCH_EM_DASH.MATCH_EN_DASH.'-])'.
			$regex_delimiter.'iu',

			static function(array $match) use (&$repinfo, &$tagreps): string {
				global $repid;
				$repinfo['_name'] = $match['name'];
				$tagreps[$repid] = $repinfo;
				return "\x12".($repid++)."\x13";
			},
			$barebody
		);
	}

	$fordisplay = $barebody;

	if ($keeps) {
		foreach ($keeps as $i => $match) {
			$barebody = str_replace(
				"\x1E$i\x1F",
				$match[0],
				$barebody
			);
			$fordisplay = str_replace(
				"\x1E$i\x1F",
				"\x12span style=\x11background-color:rgba(".(LITE ? '0,0,0' : '255,255,255').",.1)\x11\x13".$match[0]."\x12/span\x13",
				$fordisplay
			);
		}
	}

	$makedisplay = true;
	$displaybody = preg_replace_callback('"\x12(\d+?)\x13"u', 'tagreplace', $fordisplay);

	?><script>
	function showinfo(obj) {
		const count = obj.getAttribute('data-count');
		const chosen = parseInt(obj.getAttribute('data-chosen'));
		if (count) {
			if (!obj.firstChild.getAttribute
			||	!obj.firstChild.getAttribute('data-counter')
			) {
				obj.innerHTML = '<small data-counter="data-counter" class="light novents">' + count + '<?= MULTIPLICATION_SIGN_ENTITY ?></small> ' + obj.innerHTML;
			}
		}
		if (chosen === -1) {
			return;
		}
		const elems = obj.getAttribute('data-elems').split(',');
		const ids = obj.getAttribute('data-ids').split(',');
		obj.innerHTML += `<small data-spec="data-spec" class="light novents"> = ${elems[chosen]}:${ids[chosen]}</small>`;
	}

	function hideinfo(obj) {
		if (obj.lastChild.getAttribute
		&&	obj.lastChild.getAttribute('data-spec')
		) {
			Pf.removeNode(obj.lastChild);
		}
	}

	/**
	 * @param {PointerEvent} event
	 * @param {HTMLElement} obj
	 * @param {integer} repid
	 */
	function recalc(event, obj, repid) {
		if (event.shiftKey
		||	event.ctrlKey
		||	event.metaKey
		||	event.altKey
		) {
			event.stopPropagation();
			const elems  = obj.getAttribute('data-elems').split(',');
			const ids	 = obj.getAttribute('data-ids'  ).split(',');
			const chosen = parseInt(obj.getAttribute('data-chosen'));
			console.log(`chosen is ${chosen}`);
			window.open(`/${elems[chosen]}/${ids[chosen]}`, '_blank');
			return;
		}
		/** @var {HTMLInputElement|false} textobj */
		const textobj = getobj('barebody', true);
		if (!textobj) {
			return;
		}
		let body = textobj.value;
		for (let i = 0; i < <?= $repid ?>; ++i) {
			let sobj = getobj(`rep-${i}`, true);
			if (!sobj) {
				continue;
			}
			const elems = sobj.getAttribute('data-elems').split(',');
			const chosen = parseInt(sobj.getAttribute('data-chosen'));
			const elemname = sobj.getAttribute('data-elemname');
			const locations = sobj.getAttribute('data-locations').split(',');
			const ids = sobj.getAttribute('data-ids').split(',');
			const accepteds = JSON.parse(sobj.getAttribute('data-accepteds'));
			const newindex = i === repid ? (chosen === elems.length - 1 ? -1 : chosen + 1) : chosen;
			const oldindex = parseInt(sobj.getAttribute('data-chosen'));
			setattr(sobj, 'data-chosen', newindex);
			body = body.replace(
				RegExp('<?= "\x12" ?>' + i +'<?= "\x13" //?>'),
					newindex === -1
				?	elemname
				:	'[' + elems[newindex] + '=' + ids[newindex] + ' name="' + elemname + '"]'
			);
			if (i === repid) {
				taggerclick(
					/* word			*/ elemname,
					/* chosen		*/ newindex === -1 ? '' : elems[newindex],
					/* id			*/ newindex === -1 ?  0 : ids  [newindex],
					/* prevchosen	*/ oldindex === -1 ? '' : elems[oldindex],
					/* previd		*/ oldindex === -1 ?  0 : ids  [oldindex]
				);
				sobj.className = (
					newindex === -1
				?	'bottom-dashed'
				:	'basehl hilited-' + elems[newindex] +
					(accepteds[newindex] ? '' : ' error')
				) + ' ptr';

				let location = null;
				for (location = sobj.firstChild; location; location = location.nextSibling) {
					if (location.getAttribute
					&&	location.getAttribute('data-location')
					) {
						break;
					}
				}
				if (location) {
					location.innerHTML =
						newindex === -1
					?	''
					:	locations[newindex];
				}
			}
		}
		hideinfo(obj);
		showinfo(obj);
		const mainobj = getobj('mainbody', true);
		if (mainobj) {
			mainobj.value = body;
		}
	}

	function taggerclick(word, chosen, id, prevchosen, previd) {
		console.log(`tagger click(${word}, ${chosen}, ${id}, ${prevchosen}, ${previd})`);
		const treq = initrequest();
		if (!treq) {
			return;
		}
		const poststr =
			  'WORD='+encodeURIComponent(word)
			+ '&CHOICE=' + chosen
			+ '&ID=' + id
			+ '&PREVCHOICE=' + prevchosen
			+ '&PREVID=' + previd
			+ '&LANGID=<?= $langid ?>';

		treq.open('POST', '/taggerclick.act', true);
		treq.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
		treq.send(poststr);
	}
	</script><?
	?><input type="hidden" id="barebody" value="<?= escape_utf8($barebody) ?>" /><?
	?><div class="block"><?=
		__('tagger:info:CTRL_click_TEXT')
	?></div><?
	layout_open_box('white');
	?><div class="noselect block"><?=
		nl2br(strtr(flat_with_entities($displaybody, UBB_UTF8), "\x12\x13\x11", '<>"'))
	?></div><?
	layout_close_box();

	return $barebody;
}

function tagreplace(array $match): string {
	global	$tagreps,
			$makedisplay,
			$langid;

	static $taggerinfo;
	if (!isset($taggerinfo)) {
		$taggerinfo = [];
		if ($tagreps) {
			foreach ($tagreps as /* $repid => */ $tagrep) {
				$word = $tagrep['_name'];
				if ($info = db_single_array('taggerinfo', "
					SELECT LOWER(WORD), CHOICE, ID, HITS
					FROM taggerinfo
					WHERE LANGID = $langid
					  AND WORD = '".addslashes($word)."'
					ORDER BY HITS DESC
					LIMIT 1")
				) {
					[$lowerword, $choice, $id, $hits] = $info;
					if ($lowerword
					||	$hits > 5
					) {
						$taggerinfo[$lowerword] = [$choice, $id];
					}
				}
			}
		}
	}

	$repid = (int)$match[1];
	$name = mb_strtolower($tagreps[$repid]['_name']);
	$foundname = $tagreps[$repid]['_name'];
	$elements = [];
	$elementids = [];
	$locations = [];
	$accepteds = [];
	$location_count = 0;
	static $__accepted;

	foreach ($tagreps[$repid] as $element => $ids) {
		if ($element[0] === '_') {
			continue;
		}
		$idcnt = count($ids);
		while ($idcnt--) {
			$elements[] = $element;
			$elementids[] = ($id = array_shift($ids));
			if (!isset($__accepted[$element][$id])) {
				$__accepted[$element][$id] = db_single($element, "
					SELECT ACCEPTED
					FROM `$element`
					WHERE {$element}ID = $id"
				);
			}
			$accepteds[] = (bool)$__accepted[$element][$id];
			$item_info = null;
			switch ($element) {
			case 'artist':
				$item_info = db_single_assoc('artist','
					SELECT COUNTRYID, NULL AS CITYID
					FROM artist
					WHERE ARTISTID = '.$id
				);
				break;

			case 'location':
				$item_info = db_single_assoc('location','
					SELECT city.COUNTRYID, city.CITYID
					FROM location
					JOIN city USING (CITYID)
					WHERE LOCATIONID = '.$id
				);
				break;

			case 'organization':
				$item_info = db_single_assoc('location','
					SELECT IF(organization.COUNTRYID, organization.COUNTRYID, city.COUNTRYID) AS COUNTRYID, city.CITYID
					FROM organization
					LEFT JOIN city USING (CITYID)
					WHERE ORGANIZATIONID = '.$id
				);
				break;
			}
			$shortcode =
				!empty($item_info['COUNTRYID'])
			?	memcached_single('country', 'SELECT SHORT FROM country WHERE COUNTRYID = '.$item_info['COUNTRYID'])
			:	'';

			$locations[] =
				$location =
				!empty($item_info['COUNTRYID'])
			?	$shortcode
			:	'';

			if ($location) {
				++$location_count;
			}
		}
	}

	if (isset($taggerinfo[$name])) {
		[$chosenelement, $id] = $taggerinfo[$name];
		if (!$chosenelement) {
			$chosen = -1;
		} else {
			$chosenelementid = $id;
			$chosen = 0;
			$found = false;
			foreach ($elementids as $id) {
				if ($elements  [$chosen] === $chosenelement
				&&	$elementids[$chosen] === $chosenelementid
				) {
					$found = true;
					break;
				}
				++$chosen;
			}
			if (!$found) {
				$chosen = 0;
				$chosenelement   = $elements  [0];
				$chosenelementid = $elementids[0];
			}
		}
	} else {
		$chosen = 0;
		$chosenelement   = $elements  [0];
		$chosenelementid = $elementids[0];
	}
	$count = count($elements);
	return	$makedisplay
	?	"\x12span ".
			'id'.				"=\x11rep-$repid\x11 ".
			'data-accepteds'.	"=\x11".safe_json_encode($accepteds)."\x11".
			'data-elemname'.	"=\x11$foundname\x11 ".
			'data-elems'.		"=\x11".implode(',', $elements)."\x11 ".
			'data-ids'.			"=\x11".implode(',', $elementids)."\x11 ".
			'data-count'.		"=\x11".$count."\x11 ".
			'data-locations'.	"=\x11".implode(',', $locations)."\x11 ".
			'data-chosen'.		"=\x11$chosen\x11 ".
			'onclick'.			"=\x11"."recalc(event, this, $repid);"."\x11 ".
			'onmouseover'.		"=\x11".'showinfo(this);'."\x11 ".
			'onmouseout'.		"=\x11".'hideinfo(this);'."\x11 ".
			'class'.			"=\x11".(
						$chosen > -1
					?	'basehl hilited-'.$chosenelement.' ptr'.
						(empty($__accepted[$chosenelement][$chosenelementid]) ? ' error' : '')
					:	'bottom-dashed ptr'
					).
					"\x11".
		"\x13".	(	isset($chosenelementid)
			?	(	$count > 1
				?	"\x12small data-counter=\x11data-counter\x11 class=\x11light novents\x11\x13$count".MULTIPLICATION_SIGN."\x12/small\x13 "
				:	''
				).
				"[$chosenelement id=$chosenelementid name=\"$foundname\"]"
			:	$foundname
			).
			(	!isset($chosenelementid)
			||	empty($locations[$chosen === -1 ? $chosenelementid : $chosen])
			?	(	$location_count
				?	" \x12small data-location=\x11data-location\x11 class=\x11light novents\x11\x13\x12/small\x13"
				:	''
				)
			:	" \x12small data-location=\x11data-location\x11 class=\x11light novents\x11\x13".$locations[$chosen === -1 ? $chosenelementid : $chosen]."\x12/small\x13"
			).
		"\x12/span\x13"
	:	(	$chosen < 0
		?	$foundname
		:	"[$chosenelement=$chosenelementid name=\"$foundname\"]"
		);
}
