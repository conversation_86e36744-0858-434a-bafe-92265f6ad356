<?php

function show_last_party(?string $element = null, ?int $id = null): array|null|false {
	$element ??= $_REQUEST['sELEMENT'];
	$id	 ??= $_REQUEST['sID'];

	if (!($lastparty = get_last_party($element, $id))) {
		return $lastparty;
	}
	$cityid = 0;
	if ($locationid = getifset($lastparty,'LOCATIONID') ?: 0) {
		$location = memcached_location_info($locationid);
		$cityid = $location['CITYID'] ?? 0;
	}
	?><div class="block"><?= __(get_last_party_info_key($element), DO_UBB, [
		'PARTYID'	=> $lastparty['PARTYID'],
		'LOCATIONID'	=> $locationid,
		'CITYID'	=> $cityid,
		'DATE'		=> _dateday_get_tzi($lastparty['STAMP_TZI'] - ($lastparty['AT2400'] ? 1 : 0)),
	]);
	?></div><?
	return $lastparty;
}

function get_last_party_info_key(string $element): string {
	return match($element) {
		'artist'	=> 'artist:info:last_performance_LINE',
		'user'		=> 'agenda:info:last_party_visited_LINE',
		default		=> 'agenda:info:last_party_LINE',
	};
}

function get_last_party(?string $element = null, ?int $id = null): array|null|false {
	$element ??= $_REQUEST['sELEMENT'];
	$id	 ??= $_REQUEST['sID'];

	switch ($element) {
	case 'location':
		$tables = 'party';
		$qstr = '
			SELECT PARTYID, AT2400, NAME, STAMP_TZI
			FROM party
			WHERE LOCATIONID = '.$id.'
			  AND MOVEDID = 0
			  AND CANCELLED = 0
			  AND POSTPONED = 0
			  AND ACCEPTED = 1
			ORDER BY STAMP DESC
			LIMIT 1';
		break;

	case 'boarding':
		$tables = 'party';
		$qstr = '
			SELECT PARTYID, AT2400, NAME, STAMP_TZI, STAMP, LOCATIONID
			FROM party
			WHERE BOARDINGID = '.$id.'
			  AND MOVEDID = 0
			  AND CANCELLED = 0
			  AND POSTPONED = 0
			  AND ACCEPTED = 1
			ORDER BY STAMP DESC
			LIMIT 1';
		break;

	case 'locasorg':
		$tables = 'party';
		$qstr ='
			SELECT PARTYID, AT2400, NAME, STAMP_TZI, STAMP, LOCATIONID
			FROM party
			JOIN connect
			  ON ASSOCID = PARTYID
			WHERE MAINTYPE = "locasorg"
			  AND MAINID = '.$id.'
			  AND LOCATIONID != '.$id.'
			  AND MOVEDID = 0
			  AND CANCELLED = 0
			  AND POSTPONED = 0
			  AND ACCEPTED = 1
			ORDER BY STAMP DESC
			LIMIT 1';
		break;

	case 'organization':
		$tables = ['party', 'connect'];
		$qstr ='
			SELECT PARTYID, AT2400, NAME, STAMP_TZI, STAMP, LOCATIONID
			FROM party
			JOIN (
				SELECT PARTYID, 0 AS IS_ORG, 1 AS IS_HOST
				FROM partyarea
				WHERE HOSTEDBYID = '.$id.'
				UNION
				SELECT ASSOCID AS PARTYID, IF(MAINTYPE = "organization", 1, 0) AS IS_ORG, IF(MAINTYPE="orgashost", 1, 0) AS IS_HOST
				FROM connect
				WHERE MAINTYPE IN ("orgashost", "organization")
				  AND MAINID = '.$id.'
				  AND ASSOCTYPE = "party"

			) AS findhostororg USING (PARTYID)
			WHERE MOVEDID = 0
			  AND CANCELLED = 0
			  AND POSTPONED = 0
			  AND ACCEPTED = 1
			ORDER BY STAMP DESC
			LIMIT 1';
		break;

	case 'user':
		$tables = ['going', 'party'];
		$qstr = '
			SELECT PARTYID, AT2400, NAME, STAMP_TZI, STAMP, LOCATIONID
			FROM party
			JOIN going USING (PARTYID)
			WHERE going.USERID = '.$id.'
			  AND MOVEDID = 0
			  AND CANCELLED = 0
			  AND POSTPONED = 0
			  AND ACCEPTED = 1
			  AND MAYBE = 0
			ORDER BY STAMP DESC
			LIMIT 1';
		break;

	case 'city':
		$tables = ['party', 'location', 'boarding'];
		$qstr = '
			SELECT PARTYID, AT2400, STAMP_TZI, STAMP, LOCATIONID
			FROM (
			(	SELECT PARTYID, AT2400, STAMP_TZI, STAMP, LOCATIONID
				FROM party
				JOIN location USING (LOCATIONID)
				WHERE MOVEDID = 0
				  AND CANCELLED = 0
				  AND POSTPONED = 0
				  AND party.ACCEPTED = 1
				  AND location.CITYID = '.$id.'
				ORDER BY STAMP DESC
				LIMIT 1
			) UNION (
				SELECT PARTYID, AT2400, STAMP_TZI, STAMP, LOCATIONID
				FROM party
				JOIN boarding USING (BOARDINGID)
				WHERE MOVEDID = 0
				  AND CANCELLED = 0
				  AND POSTPONED = 0
				  AND party.ACCEPTED = 1
				  AND boarding.CITYID = '.$id.'
				ORDER BY STAMP DESC
				LIMIT 1
			) UNION (
				SELECT PARTYID, AT2400, STAMP_TZI, STAMP, LOCATIONID
				FROM party
				WHERE MOVEDID = 0
				  AND CANCELLED = 0
				  AND POSTPONED = 0
				  AND party.ACCEPTED = 1
				  AND CITYID = '.$id.'
				ORDER BY STAMP DESC
				LIMIT 1)
			) AS opts
			ORDER BY STAMP_TZI DESC
			LIMIT 1';
  		break;

	case 'province':
		$tables = ['party', 'location', 'boarding'];
		$qstr = '
			SELECT PARTYID, AT2400, STAMP_TZI, STAMP, LOCATIONID
			FROM (
			(	SELECT PARTYID, AT2400, STAMP_TZI, STAMP, LOCATIONID
				FROM party
				JOIN location USING (LOCATIONID)
				JOIN city ON location.CITYID = city.CITYID
				WHERE MOVEDID = 0
				  AND CANCELLED = 0
				  AND POSTPONED = 0
				  AND party.ACCEPTED = 1
				  AND PROVINCEID = '.$id.'
				ORDER BY STAMP DESC
				LIMIT 1
			) UNION (
				SELECT PARTYID, AT2400, STAMP_TZI, STAMP, LOCATIONID
				FROM party
				JOIN boarding USING (BOARDINGID)
				JOIN city ON boarding.CITYID = city.CITYID
				WHERE MOVEDID = 0
				  AND CANCELLED = 0
				  AND POSTPONED = 0
				  AND party.ACCEPTED = 1
				  AND PROVINCEID = '.$id.'
				ORDER BY STAMP DESC
				LIMIT 1
			) UNION (
				SELECT PARTYID, AT2400, STAMP_TZI, STAMP, LOCATIONID
				FROM party
				JOIN city USING (CITYID)
				WHERE MOVEDID = 0
				  AND CANCELLED = 0
				  AND POSTPONED = 0
				  AND party.ACCEPTED = 1
				  AND PROVINCEID = '.$id.'
				ORDER BY STAMP DESC
				LIMIT 1)
			) AS opts
			ORDER BY STAMP_TZI DESC
			LIMIT 1';
  		break;

	case 'artist':
		$tables = ['lineup', 'party'];
		$qstr =  '
			SELECT PARTYID, AT2400, NAME, STAMP_TZI, STAMP, LOCATIONID
			FROM party
			JOIN lineup USING (PARTYID)
			WHERE ARTISTID = '.$id.'
			  AND MOVEDID = 0
			  AND CANCELLED = 0
			  AND POSTPONED = 0
			  AND ACCEPTED = 1
			ORDER BY STAMP DESC
			LIMIT 1';

		break;
	}
	$need_refresh = partylist_refresh_needed();
	return memcached_single_assoc(
		tables:  $tables,
		qstr:    $qstr,
		expires: TEN_MINUTES,
		flags:   $need_refresh ? DB_FRESHEN_MEMCACHE : 0
	);
}

function get_lastparty_in_dead_location(int $locationid, int $notpartyid = 0): array|null|false {
	return db_single_assoc(['location', 'party'], '
		SELECT FOLLOWUPID, DEAD, PARTYID, STAMP, STAMP_TZI, party.NAME
		FROM location
		LEFT JOIN party USING (LOCATIONID)
		WHERE (FOLLOWUPID != 0 OR DEAD = 1)
		  AND location.LOCATIONID = '.$locationid.
		($notpartyid ? ' AND PARTYID != '.$notpartyid : '').'
		ORDER BY STAMP DESC
		LIMIT 1'
	);
}
