<?php

require_once '_elementnames.inc';

function get_contact_elements(?string $element = null): array|bool {
	static $__contact_elements;
	if (!isset($__contact_elements)) {
		if (!($explanation = explain_table('contact_ticket'))) {
			$__contact_elements = [];
		} else {
			$__contact_elements = $explanation['ELEMENT']->enum;
			unset($__contact_elements['pr']);
		}
	}
	return	$element !== null
		?	isset($__contact_elements[$element])
		:	$__contact_elements;
}

function is_contact_element(string $element): bool {
	return get_contact_elements($element);
}

function get_contact_element_names(bool $capitalize = false): array {
	static $__contact_element_names;
	if (!isset($__contact_element_names[$capitalize])) {
		foreach (get_contact_elements() as $element => $true) {
			$element_names[$element] = element_name(
				$element === 'party' ? 'agenda' : $element,
				capitalize: $capitalize
			);
		}
		asort($element_names);
		return $__contact_element_names[$capitalize] = $element_names;
	}
	return $__contact_element_names[$capitalize];
}
