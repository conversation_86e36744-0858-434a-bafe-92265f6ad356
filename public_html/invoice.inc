<?php

require_once '_invoice.inc';
require_once '_invoicelist.inc';

function perform_commit(): ?bool {
	switch ($_REQUEST['ACTION']) {
	default:
	case null:				return null;
	case 'markread':		return invoice_markread();
#	case 'commitlist':		return invoice_commit_list();
#	case 'commitpdf':		return invoice_commit_pdf();
	case 'setstatus':		return invoice_set_status();
	case 'commitnumbers':	return invoice_commit_numbers();
	case 'commit':			return invoice_commit();
	}
}
function display_body(): void {
	switch ($_REQUEST['ACTION']) {
	default:
	case null:
	case 'commitlist':
	case 'commitpdf':	invoice_display_overview(); return;
	case 'markread':	$_REQUEST['sID'] ? invoice_display_single() : invoice_display_mine(); return;
	case 'mine':		invoice_display_mine(); return;
	case 'single':
	case 'setstatus':
	case 'commit':		invoice_display_single(); return;
#	case 'listform':	invoice_display_list_form(); return;
#	case 'pdfform':		invoice_display_pdf_form(); return;
#	case 'nrform':		invoice_display_number_form(); return;
	case 'form':		invoice_display_form(); return;
	}
}
function invoice_set_status() {
	if (!require_admin('invoice')
	||	!($status = require_element($_REQUEST, 'STATUS', ['paid', 'notpaid', 'irrecoverable', 'ignore']))
	||	!require_idnumber($_REQUEST,'sID')
	||	!db_insert('invoice_log', "
		INSERT INTO invoice_log
		SELECT *, UNIX_TIMESTAMP()
		FROM invoice
		WHERE INVOICENR = {$_REQUEST['sID']}
		  AND STATUS != '$status'")
	||	!db_update('invoice', "
		UPDATE invoice SET
			STATUS		= '$status',
			MSTAMP		= UNIX_TIMESTAMP(),
			MUSERID		= ".CURRENTUSERID.(
				$status == 'notpaid'
			||	$status == 'irrevocable'
			?	', READM = 0'
			:	'')."
		WHERE INVOICENR = {$_REQUEST['sID']}
		  AND STATUS != '$status'")
	) {
		return;
	}
	switch ($status) {
	case 'ignore':			$tkey = 'invoice:notice:setstatus_ignore_LINE'; break;
	case 'irrecoverable':	$tkey = 'invoice:notice:setstatus_irrecoverable_LINE'; break;
	case 'notpaid':			$tkey = 'invoice:notice:setstatus_notpaid_LINE'; break;
	case 'paid':			$tkey = 'invoice:notice:setstatus_paid_LINE'; break;
	}
	register_notice($tkey);
}
function invoice_year_stats() {
	static $__stats = 0;
	if ($__stats === 0) {
		$__stats = memcached_simple_hash('invoice','
			SELECT FROM_UNIXTIME(SENTSTAMP,"%Y") AS YEAR,COUNT(*)
			FROM invoice
			GROUP BY YEAR'
		);
	}
	return $__stats;
}
function invoice_display_overview() {
	if (!require_admin('invoice')) {
		return;
	}
	layout_show_section_header();

	layout_open_menu();
	global $__year;
	layout_menuitem(Eelement_name('overview'),'/invoice',!$_REQUEST['ACTION']);
	layout_menuitem(Eelement_name('archive'),'/invoice/archive/'.$__year,$_REQUEST['ACTION'] == 'archive' && $_REQUEST['subID'] == $__year);
	layout_menuitem(__C('paystatus:irrecoverable',DO_UBB),'/invoice/irrecoverable',$_REQUEST['ACTION'] == 'irrecoverable');
	layout_menuitem(__C('paystatus:ignore',DO_UBB),'/invoice/ignore',$_REQUEST['ACTION'] == 'ignore');
	layout_close_menu();

	layout_open_menu();
	layout_menuitem(__C('action:add'),'/invoice/form');
#	layout_menuitem(__C('action:process_list'),'/invoice/listform');
#	layout_menuitem(__C('action:process_pdf'),'/invoice/pdfform');
#	layout_menuitem(__C('action:paid_list'),'/invoice/nrform');

/*	$startnr = db_single('invoice_pdf_processed','SELECT MIN(INVOICENR) FROM invoice_pdf_processed');
	if ($startnr === false) {
		return;
	}
	if (($last_stamps = db_single_array(['invoice','invoice_pdf_processed'],'
			SELECT	(SELECT MAX(STAMP) FROM invoice_pdf_processed),
				(SELECT MAX(CSTAMP) FROM invoice),
				(SELECT MAX(SENTSTAMP) FROM invoice)'
		))
	&&	($cnts = db_single_array(['invoice','invoice_pdf_processed','relation'],'
		SELECT	COUNT(*),
			COUNT(	IF(	ISNULL(i.INVOICENR)
				OR	ISNULL(ipp.INVOICENR),
				1,NULL)
			),
			COUNT(	IF(	ISNULL(relation.RELATIONID)
			  	OR	INVOICEMAILONLY IS NULL
			  	OR	INVOICEMAILONLY=0,
		  		1,NULL)
			)
		FROM (
			SELECT INVOICENR FROM invoice_pdf_processed WHERE STAMP='.$last_stamps[0].'
		UNION	SELECT INVOICENR FROM invoice WHERE CSTAMP='.$last_stamps[1].'
		UNION	SELECT INVOICENR FROM invoice WHERE SENTSTAMP='.$last_stamps[2].'
		)
		AS allinvoices
		LEFT JOIN invoice AS i USING (INVOICENR)
		LEFT JOIN invoice_pdf_processed AS ipp USING (INVOICENR)
		LEFT JOIN relation USING (RELATIONID)'))
	) {
		layout_menuitem(__C('action:download'),'/invoices_'.$last_stamps[0].'.obj', DOWNLOAD_LINK);
		list($total,$bads,$print) = $cnts;
		?> <small<?
		if ($bads) {
			?> class="warning"<?
		}
		?>>(<?
		if ($bads) {
			echo $bads,' ',__('status:missing'),', ';
		}
		echo $print ?> / <?= $total ?>, <?
		_datetime_display($last_stamps[0]);
		?>)</small> <?
	}*/
	layout_close_menu();

	switch ($_REQUEST['ACTION']) {
	default:
		$wherep = 'STATUS="notpaid"';
		$show_warn = true;
		break;
	case 'archive':
		global $__year;
		$wherep = 'STATUS="paid"';
		$show_year = $_REQUEST['subID'] ?: $__year;
		if ($show_year) {
			$wherep .= ' AND SENTSTAMP BETWEEN UNIX_TIMESTAMP("'.$show_year.'-01-01") AND UNIX_TIMESTAMP("'.($show_year+1).'-01-01")-1';
		}
		break;
	case 'ignore':
	case 'irrecoverable':
		$wherep = 'STATUS="'.$_REQUEST['ACTION'].'"';
	}

	$invoices = memcached_rowuse_hash(
		['invoice','relation','debitor','invoice_pdf_processed'],$q = '
		SELECT	invoice.INVOICENR,invoice.DEBNR,invoice.SENTSTAMP,AMOUNT_NEW,STATUS,RELATIONID,
			RELATIONID,PAY_WITHIN,
			debitor.NAME AS DEBITOR_NAME,
			invoice_pdf_processed.STAMP AS PDF,
			SENTSTAMP>=1483225200 AND ISNULL(invoice_pdf_processed.INVOICENR) AS ALARM,
			ISNULL(relation.RELATIONID) OR (CONTACTEMAIL="" AND INVOICEMAIL="") AS CANNOT_MAIL,
			(SELECT 1 FROM ideal_transaction WHERE ELEMENT="invoice" AND ELEMENTID=INVOICENR AND STATUS="paid" LIMIT 1) AS IDEALED,
			invoice_mailed.STAMP AS MAILED_AT,invoice_mailed.TICKETID AS MAILED_TICKETID
		FROM invoice
		LEFT JOIN relation USING (RELATIONID)
		LEFT JOIN debitor ON invoice.DEBNR=debitor.DEBNR
		LEFT JOIN invoice_pdf_processed USING (INVOICENR)
		LEFT JOIN invoice_mailed USING (INVOICENR)
		WHERE '.$wherep.'
		GROUP BY INVOICENR
		ORDER BY SENTSTAMP DESC,INVOICENR DESC'
	);
	if ($invoices === false) {
		return;
	}
	if ($_REQUEST['ACTION'] == 'archive') {
		$stats = invoice_year_stats();

		?><div class="block"><?
		?><select onchange="location.href='/invoice/archive/'+this.value;return false;"><?
		for ($y = $__year; $y >= 2005; --$y) {
			?><option<?
			if ($y == $show_year) {
				?> selected="selected"<?
			}
			?> value="<?= $y ?>"><?
			echo $y;

			if (isset($stats[$y])) {
				?> <?= MIDDLE_DOT_ENTITY ?> <?
				echo $stats[$y];
			}
			?></option><?
		}
		?></select><?
		?></div><?
	}


	if (!$invoices) {
		?><div class="block"><?= __('invoice:info:none_found_LINE') ?></div><?
		return;
	}
	foreach ($invoices as $invoice) {
		if ($stamp = $invoice['PDF']) {
			$stamps[$stamp] = $stamp;
		}
	}

	$alarms = !isset($stamps) ? null :
		memcached_rowuse_hash(
			['invoice','relation','debitor','invoice_pdf_processed'],'
			SELECT	INVOICENR,invoice.DEBNR,'.(TODAYSTAMP + ONE_DAY).' AS SENTSTAMP,AMOUNT_NEW,STATUS,RELATIONID,
				RELATIONID,PAY_WITHIN,
				debitor.NAME AS DEBITOR_NAME,
				invoice_pdf_processed.STAMP AS PDF,
				1 AS ALARM,
				ISNULL(relation.RELATIONID) OR (CONTACTEMAIL="" AND INVOICEMAIL="") AS CANNOT_MAIL
			FROM invoice_pdf_processed
			LEFT JOIN invoice USING (INVOICENR)
			LEFT JOIN relation USING (RELATIONID)
			LEFT JOIN debitor ON debitor.DEBNR=invoice.DEBNR
			WHERE (	invoice_pdf_processed.STAMP IN ('.implode(',',$stamps).')
				  AND ISNULL(invoice.INVOICENR)
				  )
				  OR (not isnull(invoice.INVOICENR) AND invoice.DEBNR=0 AND invoice.RELATIONID=0)
			GROUP BY INVOICENR'
		);

	foreach ($invoices as $ndx => $invoice) {
		if ($invoice['ALARM']) {
			$alarms[$ndx] = $invoice;
			unset($invoices[$ndx]);
		}
	}

	foreach ([$alarms,$invoices] as $ok => $tmpinvoices) {
			if (!$tmpinvoices) {
					continue;
				}
				uasort($tmpinvoices,function($a,$b) {
			$rev = $b['SENTSTAMP'] - $a['SENTSTAMP'];
			return $rev ?: ($b['INVOICENR'] - $a['INVOICENR']);
				});
		layout_open_box('white');
		layout_box_header($ok ? Eelement_plural_name('outstanding_invoice') : '<span class="error">'.Eelement_plural_name('incomplete_invoice').'</span>');
		invoicelist_show($tmpinvoices);
		layout_close_box();
	}
}
function invoice_display_mine() {
	if (!$GLOBALS['currentuser']->get_relations()) {
		return;
	}
	layout_show_section_header();

	invoice_menu();

	$invoices = memcached_multirowuse_hash(['invoice','relation','invoice_pdf_processed'],'
		SELECT	IF(READM=0 AND STATUS="notpaid" OR STATUS="irrecoverable",0,1) AS PRIO,
			READM,invoice.DEBNR,INVOICENR,SENTSTAMP,AMOUNT_NEW,STATUS,READM,
			RELATIONID,PAY_WITHIN,
			invoice_pdf_processed.STAMP AS PDF
		FROM invoice
		LEFT JOIN relation USING (RELATIONID)
		LEFT JOIN invoice_pdf_processed USING (INVOICENR)
		WHERE (		invoice.DEBNR IN ('.implode(',',have_relation()).')
			OR	invoice.RELATIONID IN ('.implodekeys(',',have_relation()).')
		)
		/*  AND DEBNR!=0*/
		  AND STATUS!="ignore"
		ORDER BY PRIO ASC,SENTSTAMP DESC'
	);
	foreach ($invoices as $prio => $invoicelist) {
		layout_open_box('yellow');
		layout_box_header(Eelement_plural_name(!$prio ? 'unpaid_invoice' : 'invoice',count($invoicelist)));
		invoicelist_show($invoicelist);
		layout_close_box();
		foreach ($invoicelist as $invoice) {
			if ($have_unread = invoice_may_mark_read($invoice)) {
				break;
			}
		}
		if ($have_unread) {
			?><div class="funcs"><a href="/invoice/markread"><?= __C('action:mark_seen'); ?></a></div><?
		}
	}
}
function invoice_menu() {
	layout_open_menu();
	if (have_admin('invoice')) {
		layout_menuitem(Eelement_name('overview'),'/invoice',!$_REQUEST['ACTION']);
	} else {
		layout_menuitem(Eelement_name('overview'),'/invoice/mine',$_REQUEST['ACTION'] == 'mine' && !$_REQUEST['SUBACTION']);
	}
	layout_close_menu();
}
function invoice_markread() {
	if (!require_relation()) {
		return;
	}
	if ($invoicenr = $_REQUEST['sID']) {
		$invoice = db_single_assoc('invoice','
			SELECT invoice.DEBNR,RELATIONID,SENTSTAMP,READM,STATUS,COALESCE(MIN(PAY_WITHIN),'.INVOICE_DEFAULT_PAY_WITHIN.') AS PAY_WITHIN
			FROM invoice
			LEFT JOIN relation USING (RELATIONID)
			WHERE INVOICENR='.$invoicenr.'
			GROUP BY INVOICENR'
		);
		if (!$invoice) {
			return;
		}
		if (!in_array($invoice['DEBNR'],have_relation())
		&&	!have_relation($invoice['RELATIONID'])
		) {
			register_notice('invoice:error:not_meant_for_you_LINE');
			return;
		}
		$invoice['INVOICENR'] = $invoicenr;
		$invoices = array($invoice);
	} else {
		$invoices = db_rowuse_array('invoice','
			SELECT INVOICENR,READM,SENTSTAMP,STATUS,COALESCE(MIN(PAY_WITHIN),'.INVOICE_DEFAULT_PAY_WITHIN.') AS PAY_WITHIN
			FROM invoice
			LEFT JOIN relation USING (RELATIONID)
			WHERE invoice.DEBNR IN ('.implode(',',have_relation()).')
			   OR invoice.RELATIONID IN ('.implodekeys(',',have_relation()).')
			GROUP BY INVOICENR'
		);
		if (!$invoices) return;
	}
	foreach ($invoices as $invoice) {
		if (invoice_may_mark_read($invoice)) {
			$rvoices[] = $invoice['INVOICENR'];
		}
	}
	if (!isset($rvoices)) return;
	if (!db_update('invoice','
		UPDATE invoice SET
			READM		=1,
			READSTAMP	='.CURRENTSTAMP.'
		WHERE READM=0
		  AND INVOICENR IN ('.implode(',',$rvoices).')')
	) {
		return;
	}
	register_notice(count($rvoices) > 1 ? 'invoice:notice:marked_all_read_LINE' : 'invoice:notice:marked_read_LINE');
}
function invoice_display_single() {
	if (!require_idnumber($_REQUEST,'sID')) {
		return;
	}
	$invoiceid = $_REQUEST['sID'];
	$invoice = db_single_assoc(
		['invoice','relation','debitor','ideal_transaction'],'
		SELECT	INVOICENR,i.DEBNR,AMOUNT,AMOUNT_NEW,READM,SENTSTAMP,DEBTCOLLECTION,STATUS,i.MUSERID,i.MSTAMP,i.USERID,i.CSTAMP,UNIQID,VAT,i.RELATIONID,
			RELATIONID,PAY_WITHIN,VATNUM,
			COUNTRYID,
			debitor.NAME AS DEBITOR_NAME,
			(SELECT PAYDATE FROM ideal_transaction WHERE ELEMENT="invoice" AND ELEMENTID=INVOICENR AND STATUS="paid" LIMIT 1) AS IDEALED
		FROM invoice AS i
		LEFT JOIN relation USING (RELATIONID)
		LEFT JOIN city USING (CITYID)
		LEFT JOIN debitor ON debitor.DEBNR=i.DEBNR
		WHERE INVOICENR='.$invoiceid.'
		GROUP BY INVOICENR'
	);
	if ($invoice === false) {
		return;
	}
	if (!$invoice) {
		register_error('invoice:error:nonexistent_LINE',array('ID'=>$invoiceid));
		return;
	}
	require_once '_adlink.inc';
	$invoice_admin = have_admin('invoice');
	if (!$invoice_admin) {
		$rels_to_debs = $GLOBALS['currentuser']->get_relations() ?: [];
		if (!in_array($invoice['DEBNR'],$rels_to_debs)
		&&	!isset($rels_to_debs[$invoice['RELATIONID']])
		&&	!($need_uniqid = verify_hash_link($invoice))
		) {
			register_error('invoice:error:not_meant_for_you_LINE');
			return;
		}
	}

#	$vat = $invoice['SENTSTAMP'] >= 1349042400 ? 21 : 19;

	require_once '_price.inc';
	layout_show_section_header();

	invoice_menu();

	if ($invoice_admin) {
		layout_open_menu();
		if ($_REQUEST['sID']) {
			layout_menuitem(__C('action:change'),'/invoice/'.$_REQUEST['sID'].'/form');
		}
		if (($status = $invoice['STATUS']) != 'notpaid') {
			layout_menuitem(__C('paystatus:notpaid'),'/invoice/'.$invoiceid.'/setstatus?STATUS=notpaid');
		}
		if ($status != 'paid') {
			layout_menuitem(__C('paystatus:paid'),'/invoice/'.$invoiceid.'/setstatus?STATUS=paid');
		}
		if ($status != 'irrecoverable') {
			layout_menuitem(__C('paystatus:irrecoverable'),'/invoice/'.$invoiceid.'/setstatus?STATUS=irrecoverable');
		}
		if ($status != 'ignore') {
			layout_menuitem(__C('paystatus:ignore'),'/invoice/'.$invoiceid.'/setstatus?STATUS=ignore');
		}
		layout_continue_menu();
		show_adlink($invoice);

		layout_close_menu();
		if ($status == 'notpaid') {
			layout_open_menu();
			layout_menuitem(__C('action:create_reminder'),'/ticket/outgoing-form?ELEMENT=invoice;ID='.$invoiceid.';USETEMPLATE=notpaid');
			if ($ticketid = db_single('contact_ticket','SELECT MAX(TICKETID) FROM contact_ticket WHERE ELEMENT="invoice" AND ID='.$invoiceid)) {
				layout_menuitem(__C('action:create_reminder_existing_ticket'),'/ticket/'.$ticketid.'?ELEMENT=invoice;ID='.$invoiceid.';USETEMPLATE=notpaid');
			}
			layout_close_menu();
		}
	}

	if (have_user()) {
		require_once '_ticketlist.inc';
		show_connected_tickets();
	}

	if ($invoice['COUNTRYID'] > 1) {
		$is_inc_vat = false;
	} else {
		$is_inc_vat = !$invoice['VATNUM'] || $invoice['VATNUM'] && preg_match('"^NL\d"i',$invoice['VATNUM']);
	}

	if (!($is_inc_vat ? $invoice['VAT'] : !$invoice['VAT']) ) {
		error_log('WARNING is_inc_vat ('.($is_inc_vat ? 'true' : 'false').') discrepancy with VAT ('.$invoice['VAT'].') @ '.$_SERVER['REQUEST_URI']);
	}

	?><article itemscope itemtype="https://schema.org/Article"><?

	layout_open_box('yellow');
	if (db_single('invoicedata','SELECT 1 FROM data_db.invoicedata WHERE INVOICENR='.$invoiceid)) {
		ob_start();
		?><a href="/invoice_<?= $invoiceid ?>.obj" target="_blank"><?
		?><img src="<?= STATIC_HOST ?>/images/download_file.png" class="abs z1" style="right:10px;top:10px" /><?
		?></a><?
		$downloadlink = ob_get_clean();
	}
	layout_box_header(Eelement_name('invoice').' '.$_REQUEST['sID'],isset($downloadlink) ? $downloadlink : null);
	layout_open_table();
	layout_start_row();
	echo Eelement_name('date');
	layout_field_value();
	_date_display($invoice['SENTSTAMP']);

	layout_restart_row();
	echo Eelement_name('due_date');
	layout_field_value();
	if ($invoiceid >= 20130959 && $invoiceid <= 20130977) {
		$invoice['PAY_WITHIN'] += 17;
	}

	_date_display($invoice['SENTSTAMP'] + $invoice['PAY_WITHIN'] * ONE_DAY);

	if ($invoice['STATUS'] == 'paid') {
		?> <span class="notice">(<?
		echo __('paystatus:paid');
		if ($invoice['IDEALED']) {
			echo __('paystatus:via_ideal');
		}
		?>)</span><?
		if ($invoice['IDEALED']) {
			layout_restart_row();
			echo __C('paystatus:paid');
			layout_field_value();
			echo _datetime_display($invoice['IDEALED']);
		}
	} elseif ($invoice['STATUS'] == 'ignore') {
		if ($invoice_admin) {
			?> <span class="notice-nb">(<?= __('status:ignored') ?>)</span><?
		}
	} elseif (($irrecoverable = $invoice['STATUS'] == 'irrecoverable')
		||	$invoice['SENTSTAMP'] < CURRENTSTAMP - $invoice['PAY_WITHIN'] * ONE_DAY
	) {
		$still_warn = true;
		// TOO LATE
		$dayslate = ceil((CURRENTSTAMP - $invoice['SENTSTAMP'] - $invoice['PAY_WITHIN'] * ONE_DAY) / ONE_DAY);

		?> <span class="<?= $dayslate >= $invoice['PAY_WITHIN'] * INVOICE_FAIL_MULT ? 'error' : 'warning' ?>">(<?

		echo __('days_late',array('DAYS'=>$dayslate));
		if ($invoice_admin
		&&	$irrecoverable
		) {
				?>, <?= __('paystatus:irrecoverable') ?>!<?
		}
		?>)</span><?
	}
	if ($invoice['DEBTCOLLECTION']) {
		layout_restart_row(0,null,null,isset($still_warn) ? 'error' : null);
		?>&rarr; <? echo Eelement_name('collection_agency');
		layout_field_value();
		_date_display($invoice['DEBTCOLLECTION']);
	}
	if ($invoice['RELATIONID']) {
		layout_restart_row();
		echo Eelement_name('relation');
		layout_field_value();
		echo get_element_link('relation',$invoice['RELATIONID']);
	}
	if ($invoice['DEBITOR_NAME']) {
		layout_restart_row();
		echo Eelement_name('debtor');
		layout_field_value();
		echo escape_utf8($invoice['DEBITOR_NAME']);
		if (!$invoice['RELATIONID']) {
			$maybe = invoice_maybe_relation($_REQUEST['sID']);
			if ($maybe) {
				layout_restart_row();
				echo Eelement_name('relation');
				layout_field_value();
				echo __('answer:maybe') ?>: <?
				foreach ($maybe as $relid => $bool) {
					if (!isset($firsts)) {
						$first = true;
					} else {
						?>, <?
					}
					echo get_element_link('relation',$relid);
				}
			}
		}
	}
	if ($invoice['DEBNR']) {
		layout_restart_row();
		echo Eelement_name('debtor_number');
		layout_field_value();
		echo escape_specials($invoice['DEBNR']);
	}

	layout_restart_row();
	echo Eelement_name($invoice['VAT'] ? 'money_amount' : 'money_amount_wo_vat');
	layout_field_value();
	?><b><?= get_price($invoice['AMOUNT_NEW'] * (100 + $invoice['VAT']) / 100); ?></b><?

	if ($invoice['VAT']) {
		layout_restart_row();
		echo Eelement_name('money_amount_wo_vat');
		layout_field_value();
		echo get_price($invoice['AMOUNT_NEW']);

		if ($invoice['AMOUNT']) {
			$calc = 100 * $invoice['AMOUNT'] / (100 + $invoice['VAT']);
			if ((int)$calc !== (int)$invoice['AMOUNT_NEW']) {
				error_log('WARNING calc wo vat for invoice '.$invoiceid.' '.$calc.' != '.$invoice['AMOUNT_NEW']);
			}
		}
		layout_restart_row();
		echo Eelement_name('vat');
		layout_field_value();
		echo get_price($invoice['AMOUNT_NEW'] * $invoice['VAT'] / 100); ?> (<?= $invoice['VAT'] ?>%)<?
	}
	layout_stop_row();
	layout_close_table();

	layout_continue_box();

	require_once '_bankinformation.inc';
	show_bank_information(false,true,$invoice['SENTSTAMP']);

	if ($invoice_admin) {
		layout_display_alteration_note($invoice);
	}
	layout_close_box();

	if ((SERVER_SANDBOX || !$invoice_admin)
	&&	$invoice['SENTSTAMP'] >= **********	# unix_timestamp('2017-11-01')
	) {

		$may_mark = invoice_may_mark_read($invoice);
		$may_pay = $invoice['STATUS'] != 'paid';

		if ($may_pay
		||	$may_mark
		) {
/*			require_once '_ideal.inc';
			show_ideal_form(
				'invoice',
				$invoiceid,
				$invoice['AMOUNT_NEW'] * (100 + $invoice['VAT']) / 100,
				!empty($need_uniqid) ? [
					'UNIQID'	=> getifset($_REQUEST,'UNIQID')
				] : null
			);*/

			if ($may_mark) {
				?><div class="funcs"><?
				?><a href="/invoice/<?= $invoiceid ?>/markread"><?= __C('action:mark_seen'); ?></a><?
				?></div><?
			}
		}
	}

	if ($invoice['STATUS'] == 'notpaid'
	||	$invoice['STATUS'] == 'irrecoverable'
	) {
		$need_hr = true;
		if ($invoice['SENTSTAMP'] <= CURRENTSTAMP - $invoice['PAY_WITHIN'] * ONE_DAY) {
			?><div class="block">Tot op heden hebben wij nog geen volledige betaling van bovenstaande factuur ontvangen, terwijl de uiterste <?
			?>betaaldatum reeds is gepasseerd. Het kan zijn dat deze factuur aan uw aandacht is ontsnapt. Daarom vraag ik <?
			?>u alsnog het verschuldigde bedrag per ommegaande aan ons over te maken.</div><?
			if ($invoice['SENTSTAMP'] <= CURRENTSTAMP - $invoice['PAY_WITHIN'] * INVOICE_FAIL_MULT * ONE_DAY) {
				?><div class="error" style="font-weight:normal">Indien bovengenoemd bedrag niet uiterlijk binnen 10 dagen op onze rekening staat bijgeschreven, <?
				?>zijn wij genoodzaakt om deze vordering ter incasso uit handen te geven aan een incassobureau.<br /><?
				?>Op grond van onze algemene voorwaarden komen dan alle met de invordering gepaardgaande kosten alsmede <?
				?>de wettelijke/contractuele rente voor uw rekening.</div><?
			}
			?><div class="block">Mocht er een dringende reden zijn waarom u niet tot betaling bent overgegaan, dan vraag ik u direct contact op <?
			?>te nemen met <i><a href="mailto:<EMAIL>?subject=Factuur <?= $_REQUEST['sID'];
			?>"><EMAIL></a></i> of <?
			?><i><a href="/ticket/form?ELEMENT=invoice;ID=<?= $_REQUEST['sID'];
			?>">gebruik te maken van de contact sectie</a></i>.</div><?
		} else {
			?><div class="block">Onlangs is bovenstaande factuur aangemaakt en per e-mail verzonden naar het adres dat bij ons geregistreerd staat.</div><?
		}
		if ($invoice['VAT']) {
			?><div class="block">Het bovenstaande bedrag is in euro's en inclusief <?= $invoice['VAT'] ?>% BTW. U kunt het verschuldigde bedrag overmaken op onderstaande rekening.</div><?
		}

		if (!isset($first)) {
			?><div class="block">Indien dit bericht uw betaling onverhoopt heeft gekruist, dan kunt u dit schrijven als niet verzonden beschouwen</div><?
		}
	}
		require_once '_promolist.inc';

	$promos = db_rowuse_array(
		'promo','
		SELECT '._promolist_columns().',"'.$invoice['STATUS'].'" AS STATUS
		FROM promo
		WHERE INVOICENR='.$_REQUEST['sID']
	);
	$ads = db_rowuse_array(
		array('adinfo','ad','banner'),'
		SELECT adinfo.ADID,ad.BANNERID,NAME,ACTIVE,STARTSTAMP,STOPSTAMP,ad.BANNERID,NAME AS BANNER_NAME,IMPRESSIONS,IMPRESSIONSDONE,"'.$invoice['STATUS'].'" AS STATUS,PARTYID,ad.REMOVED
		FROM adinfo
		JOIN ad USING (ADID)
		LEFT JOIN banner USING (BANNERID)
		WHERE INVOICENR='.$_REQUEST['sID']
	);
	$newsads = db_rowuse_array(
		array('newsad','news'),'
		SELECT newsad.NEWSADID,TITLE,ACTIVE,STARTSTAMP,STOPSTAMP,IMPRESSIONS,IMPRESSIONSDONE,"'.$invoice['STATUS'].'" AS STATUS,REMOVED
		FROM newsad
		JOIN news USING (NEWSID)
		WHERE INVOICENR='.$_REQUEST['sID']
	);

	$pixels = db_rowuse_hash('pixel','
		SELECT	*,
			(SELECT COUNT(*) FROM pixelparty WHERE ACTIVE=1 AND pixelparty.PIXELID=pixel.PIXELID) AS EVENTCNT
		FROM pixel
		WHERE INVOICENR='.$_REQUEST['sID'].'
		ORDER BY STOPSTAMP ASC'
	);

	if (!$promos
	&&	!$ads
	&&	!$newsads
	&&	!$pixels
	) {
		return;
	}
	if (isset($need_hr)) {
		?><hr class="slim"><?
	}
	?><div class="block">Bovenstaande factuur betreft <i>ten minste</i> onderstaande elementen. <?
	?>Het kan zijn dat er specifieke afspraken zijn gemaakt of extra <?
	?> diensten zijn afgenomen die hieronder niet vermeld zijn.</div><?

	if ($promos) {
		layout_open_box('yellow');
		layout_box_header(Eelement_plural_name('promo'));
		layout_open_table(TABLE_FULL_WIDTH);
		_promolist_display_rows($promos,false);
		layout_close_table();
		layout_close_box();
	}
	if ($ads) {
		require_once '_adlist.inc';
		layout_open_box('yellow');
		layout_box_header(Eelement_plural_name('ad'));
		layout_open_table(TABLE_FULL_WIDTH);
		_adlist_display_rowlist($ads);
		layout_close_table();
		layout_close_box();
	}
	if ($newsads) {
		require_once '_newsadlist.inc';
		layout_open_box('yellow');
		layout_box_header(Eelement_plural_name('newsad'));
		layout_open_table(TABLE_FULL_WIDTH);
		newsadlist_show($newsads);
		layout_close_table();
		layout_close_box();
	}
	if ($pixels) {
		require_once '_pixel.inc';
		layout_open_box('yellow');
		layout_box_header(Eelement_plural_name('pixel'));
		layout_open_table(TABLE_FULL_WIDTH);
		show_pixel_table($pixels);
		layout_close_table();
		layout_close_box();
	}
	?></article><?
}
/*function invoice_display_number_form() {
	if (!require_admin('invoice')) {
		return;
	}
	layout_show_section_header();

	?><form method="post" action="/invoice/commitnumbers" onsubmit="return submitForm(this)"><?
	layout_open_box('white');
	layout_box_header(Eelement_plural_name('invoice_number'));
	?><div class="block"><textarea name="INVOICENRS" cols="40" rows="20"></textarea></div><?
	layout_close_box();
	?><div class="block"><input type="submit" value="<?= __('action:process') ?>" /></div><?
	?></form><?
}
function invoice_commit_numbers() {
	if (!require_admin('invoice')
	||	!require_something_trim($_POST,'INVOICENRS')
	) {
		return;
	}
	$_POST['INVOICENRS'] = preg_split("'[\s,]+'",$_POST['INVOICENRS']);
	if (!require_number_array($_POST,'INVOICENRS')) {
		return;
	}
	if (!($invoicenrs = $_POST['INVOICENRS'])) {
		return;
	}
	if (!db_insert('invoice_log','
		INSERT INTO invoice_log
		SELECT *,'.CURRENTSTAMP.' FROM invoice
		WHERE STATUS!="notpaid"
		  AND INVOICENR IN ('.($invoicenrstr = implode(',',$invoicenrs)).')')
	||	!db_update('invoice','
		UPDATE invoice SET
			MUSERID	='.CURRENTUSERID.',
			MSTAMP	='.CURRENTSTAMP.',
			STATUS	="paid"
		WHERE STATUS!="paid"
		  AND INVOICENR IN ('.$invoicenrstr.')')
	) {
		return;
	}
	register_notice('invoice:notice:marked_paid_LINE');
}*/
/*function invoice_display_list_form() {
	if (!require_admin('invoice')) {
		return;
	}
	layout_show_section_header();

	?><form enctype="multipart/form-data" method="post" action="/invoice/commitlist" onsubmit="return submitForm(this)"><?
	layout_open_box('white');
	layout_open_table(TABLE_CLEAN);
	layout_start_row();
	echo Eelement_name('list');
	layout_field_value();
	?><input type="file" name="LIST" accept="text/plain" /><?
	layout_stop_row();
	layout_close_table();
	layout_close_box();
	?><div class="block"><input type="submit" value="<?= __('action:process') ?>" /> als <select name="TYPE"><?
		?><option value="open"><?= element_name('list_of_outstanding_invoices') ?></option><?
		?><option value="never"><?= element_name('list_of_uncollectible_bills') ?></option><?
	?></select></div><?
	?></form><?
}*/
/*function invoice_display_pdf_form() {
	if (!require_admin('invoice')) return;
	layout_show_section_header();

	?><form enctype="multipart/form-data" method="post" action="/invoice/commitpdf" onsubmit="return submitForm(this)"><?
	layout_open_box('white');
	layout_open_table(TABLE_CLEAN);
	layout_start_row();
	echo Eelement_name('pdf');
	layout_field_value();
	?><input type="file" name="PDF" accept="application/pdf" /><?
	layout_stop_row();
	layout_close_table();
	layout_close_box();
	?><div class="block"><input type="submit" value="<?= __('action:process') ?>" /></div><?
	?></form><?
}*/
/*function invoice_commit_list() {
	if (!require_post()
	||	!require_admin('invoice')
	||	!require_file('LIST')
	||	!require_element($_POST,'TYPE',array('open','never'))
	) {
		return;
	}
	$sha1 = sha1_file($_FILES['LIST']['tmp_name']);
	if (!$sha1) {
		_error('SHAONE kon niet bepaald worden!');
		return;
	}
	$filesize = filesize($_FILES['LIST']['tmp_name']);
	$processed = db_single(
		'invoicedone','
		SELECT STAMP
		FROM invoicedone
		WHERE SHA1="'.$sha1.'"
		  AND LEN='.$filesize
	);
	if ($processed === false) {
		return;
	}
	if ($processed) {
		_error('Bestand is al verwerkt op '._dateday_get($processed).'!');
		return;
	}
	$lines = file($_FILES['LIST']['tmp_name']);
	if (!$lines) {
		return;
	}
	$idcnt = 0;
	foreach ($lines as $line) {
		$info = explode("\t",$line);
		if (!is_number($info[0])) {
			$i = 0;
			foreach ($info as $field) {
				switch ($field) {
				case 'Factuurnummer':		$invoicenr_ndx = $i; break;
				case 'Debiteur - Zoekcode':	$debnr_ndx = $i; break;
				case 'Debiteur - Naam':		$name_ndx = $i; break;
				case 'Totaalbedrag':		$value_ndx = $i; break;
				case 'Datum':			$date_ndx = $i; break;
				}
				++$i;
			}
			if (!isset($invoicenr_ndx)
			||	!isset($debnr_ndx)
			||	!isset($name_ndx)
			||	!isset($value_ndx)
			||	!isset($date_ndx)
			) {
				register_error('invoice:error:incorrect_datafile_LINE');
				return;
			}
			continue;
		}
		$debnr = $info[$debnr_ndx];
		$invoicenr = $info[$invoicenr_ndx];
		$name = $info[$name_ndx];
		$value = $info[$value_ndx];
		$date = $info[$date_ndx];

		$len = strlen($value);
		$rval = '';
		for ($i = 0; $i < $len; ++$i) {
			if (ctype_digit($value[$i])) {
				$rval .= $value[$i];
			}
		}
		if (!$rval) {
			continue;
		}
		++$idcnt;
		$ids[] = $invoicenr;
		list($day,$month,$year) = explode('-',$date);
		if (!require_real_date($year,$month,$day)) {
			return;
		}
		if (!$debnr) {
			_error('debitor with id 0');
			return;
		}
		$invoicelist[] = array($debnr,$invoicenr,$rval,mktime(0,0,0,$month,$day,$year));
		$debs[] = '('.$debnr.',"'.addslashes($name).'",'.CURRENTUSERID.','.CURRENTSTAMP.')';
	}
	if (empty($invoicelist)) {
		_error('Geen facturen gevonden in verzonden bestand!');
		return;
	}
	$idstr = implode(',',$ids);
	if ($_POST['TYPE'] == 'open') {
		$oldcnt = db_single('invoice','
			SELECT COUNT(*)
			FROM invoice
			WHERE INVOICENR IN ('.$idstr.')
			  AND CSTAMP<'.(TODAYSTAMP-365.2425*24*3600)
		);
		if ($oldcnt === false) {
			return;
		}
		if ($oldcnt / $idcnt > 5) {
			_error(	'Meer dan 5% van de facturen in het verzonden bestand zijn ouder dan een jaar.<br />'.
				'Weet je zeker dat je de lijst als lijst met openstaande facturen wilt verwerken?'
			);
			return;
		}
		if (!db_insert('invoice_log','
			INSERT INTO invoice_log
			SELECT *,'.CURRENTSTAMP.' FROM invoice
			WHERE INVOICENR IN ('.$idstr.')')
		) {
			return;
		}
		$addcnt = $updcnt = 0;
		foreach ($invoicelist as $invoiceinfo) {
			list($debnr,$invoicenr,$rval,$sentstamp) = $invoiceinfo;
			$status = db_single('invoice','SELECT STATUS FROM invoice WHERE INVOICENR='.$invoicenr);
			if ($status === false) {
				return;
			}
			if (!is_number($debnr)) {
				$debnr = 0;
				register_warning('invoice:warning:invalid_debnr_LINE',array('INVOICENR'=>$invoicenr,'DEBNR'=>$debnr));
			}
			if (!$status
			||	$status == 'notpaid'
			) {
				if (!db_insupd('invoice','
					INSERT INTO invoice SET
						UNIQID		="'.create_password(32,null,false).'",
						CSTAMP		='.CURRENTSTAMP.',
						USERID		='.CURRENTUSERID.',
						DEBNR		='.$debnr.',
						INVOICENR	='.$invoicenr.',
						AMOUNT		='.$rval.',
						SENTSTAMP	='.$sentstamp.'
					ON DUPLICATE KEY UPDATE
						STATUS		="notpaid",
						MSTAMP		='.CURRENTSTAMP.',
						MUSERID		='.CURRENTUSERID)
				) {
					return;
				}
				$aff = db_affected();
				if ($aff > 1) {
					++$updcnt;
				} elseif ($aff == 1) {
					++$addcnt;
				}
			}
		}
		if ($addcnt) {
			_notice($addcnt.' facturen toegevoegd.');
		}
		if ($updcnt) {
			_notice($updcnt.' facturen op niet betaald gezet.');
		}
		db_update('invoice','
			UPDATE invoice SET STATUS="paid"
			WHERE STATUS="notpaid"
			  AND INVOICENR NOT IN ('.$idstr.')'
		);
		$aff = db_affected();
		if ($aff) {
			_notice($aff.' facturen als afgerond gemarkeerd.');
		}
	} else {
		// never paid
		$recentcnt = db_single('invoice','
			SELECT COUNT(*)
			FROM invoice
			WHERE INVOICENR IN ('.$idstr.')
			  AND CSTAMP>'.(TODAYSTAMP - 365.2425*24*3600)
		);
		if ($recentcnt === false) {
			return;
		}
		if ($recentcnt / $idcnt > 1) {
			_error(	'Meer dan 1% van de facturen in het verzonden bestand zijn van het afgelopen jaar.<br />'.
				'Weet je zeker dat je de lijst als oninbaar wilt verwerken?'
			);
			return;
		}
		if (!db_insert('invoice_log','
			INSERT INTO invoice_log
			SELECT *,'.CURRENTSTAMP.' FROM invoice
			WHERE INVOICENR IN ('.$idstr.')')
		) {
			return;
		}
		foreach ($invoicelist as $invoiceinfo) {
			list($debnr,$invoicenr,$rval,$sentstamp) = $invoiceinfo;
			if (!db_insupd(
				'invoice','
				INSERT INTO invoice SET
					UNIQID		="'.create_password(32,null,false).'",
					CSTAMP		='.CURRENTSTAMP.',
					USERID		='.CURRENTUSERID.',
					DEBNR		='.$debnr.',
					INVOICENR	='.$invoicenr.',
					AMOUNT		='.$rval.',
					SENTSTAMP	='.$sentstamp.',
					STATUS		="irrecoverable",
					READM		=0
				ON DUPLICATE KEY UPDATE
					STATUS		=IF(STATUS="ignore",STATUS,"irrecoverable"),
					MSTAMP		='.CURRENTSTAMP.',
					MUSERID		='.CURRENTUSERID)
			) {
				return;
			}
		}
		db_update('invoice','
			UPDATE invoice SET READM=0
			WHERE STATUS="irrecoverable"
			  AND READM=1
			  AND INVOICENR IN ('.$idstr.')'
		);
		_notice($idcnt.' facturen als oninbaar gemarkeerd.');
	}
	db_insert('invoicedone','
	INSERT INTO invoicedone SET
		STAMP	='.CURRENTSTAMP.',
		SHA1	="'.$sha1.'",
		LEN	='.$filesize
	);
	if (isset($debs)) {
		if (!db_replace(
			'debitor','
			REPLACE INTO debitor (DEBNR,NAME,MUSERID,MSTAMP)
			VALUES '.implode(',',$debs))
		) {
			return;
		}
	}
	register_notice('list:notice:processed_LINE');
}*/
/*function invoice_commit_pdf() {
	if (PHP_SAPI != 'cli' && !require_admin('invoice')
	||	!($file = require_file('PDF'))
	) {
		return false;
	}
	set_memory_limit(GIGABYTE);
	set_time_limit(TEN_MINUTES);

	require_once '_execute.inc';
#	print_rr($_POST);
#	print_rr($_FILES);
	# split pdf
	$cwd = getcwd();
	$rc = false;
	if (!chdir('/tmpdisk')) {
		register_error('generic:error:failed_to_chdir_LINE',['DIR'=>'/tmpdisk']);
		goto finish;
	}
	$max_invoicenr = db_single('invoice','SELECT MAX(INVOICENR) FROM invoice');
	if (!$max_invoicenr) {
		register_error('invoice:pdf:error:no_maximum_invoicenr_LINE');
		goto finish;
	}
	$uniq = uniqid();
	list($rc,$stdout,$stderr) = execute('pdftk '.$file['tmp_name'].' burst output ipdf_'.$uniq.'_%010d.pdf');
	if ($rc) {
		_error($stderr);
		goto finish;
	}
	@unlink('/tmpdisk/doc_data.txt');
	if (!($tmpdir = opendir('/tmpdisk'))) {
		register_error('generic:error:failed_to_opendir_LINE',['DIR'=>'/tmpdisk']);
		goto finish;
	}
	$bad = false;
	while (false !== ($file = readdir($tmpdir))) {
		if (preg_match('"^ipdf_'.$uniq.'_\d{10}\.pdf$"',$file)) {
			if (!$bad
			&&	!($datas[$file] = file_get_contents($file))
			) {
				register_error('generic:error:failed_to_file_get_contents_LINE',array('FILE'=>$file));
				$bad = true;
			}
		}
	}
	ksort($datas);
	$invoicenr = null;
	if (!$bad) {
		require_once '_date.inc';
		list($year) = _getdate();
	foreach ($datas as $file => $data) {
		list($rc,$stdout,$stderr) = execute('pdftotext -layout '.$file.' -');
		if ($rc) {
			$bad = true;
			continue;
		}
		if (preg_match('"Factuurnummer:\s*(\d+)"i',$stdout,$match)) {
			$invoicenr = $match[1];
			if ($invoicenr < 2006001
			||	$invoicenr > sprintf('%04d9999',$year)
			) {
				register_error('invoice:pdf:misunderstood_invoicenr_LINE',array('INVOICENR'=>$invoicenr));
				$bad = true;
				continue;
			}
			if (isset($invoices[$invoicenr])) {
				register_error('invoice:pdf:multipage_where_not_expected_LINE');
				$bad = true;
				continue;
			}
			$invoices[$invoicenr][$file] = $data;
			continue;
		}
		if (!$invoicenr) {
			register_error('invoice:pdf:error:misunderstood_first_page_LINE');
			$bad = true;
			continue;
		}
		$havemulti = true;
		$invoices[$invoicenr][$file] = $data;
	}}
	if (!$bad && isset($havemulti)) foreach ($invoices as $invoicenr => &$pages) {
		if (count($pages) > 1) {
#			print_rr(array_keys($pages),'multipage invoice');
			list($rc,$stdout,$stderr) = execute('pdftk '.implodekeys(' ',$pages).' cat output -');
			if ($rc || !$data) {
				$bad = true;
				break;
			}
			$pages = array($stdout);
		}
	}
	unset($pages);
	if (!$bad) {
		foreach ($invoices as $invoicenr => $pages) {
			list(,$data) = keyval($pages);
			if (!db_replace('invoicedata','
				REPLACE INTO data_db.invoicedata SET
					INVOICENR	='.$invoicenr.',
					CSTAMP		='.CURRENTSTAMP.',
					CUSERID		='.CURRENTUSERID.',
					DATA		="'.addslashes($data).'"',
				DB_NOWARN_DUP_ENTRY)
			) {
				if (!is_duplicate()) {
					$bad = true;
					break;
				}
			}
			if (db_affected()) {
				register_notice('invoice:pdf:notice:invoice_added_LINE',array('INVOICENR'=>$invoicenr));
				db_insert('invoice_pdf_processed',' INSERT
				INTO invoice_pdf_processed SET
					INVOICENR	='.$invoicenr.',
					USERID		='.CURRENTUSERID.',
					STAMP		='.CURRENTSTAMP
				);
			} else {
				register_warning('invoice:pdf:warning:invoice_skipped_LINE',array('INVOICENR'=>$invoicenr));
			}
		}
	}
	foreach ($datas as $file => $data) {
		unlink($file);
	}
	$rc = true;
	finish:
	chdir($cwd);
	return $rc;
}*/
function invoice_display_form() {
	if (!require_admin('invoice')) {
		return;
	}
	if ($invoicenr = $_REQUEST['sID']) {
		$invoice = db_single_assoc('invoice','
			SELECT	invoice.*,
				invoice_mailed.USERID AS MAIL_USERID,
				invoice_mailed.STAMP AS MAIL_STAMP,
				invoice_mailed.TICKETID AS MAIL_TICKETID
			FROM invoice
			LEFT JOIN invoice_mailed USING (INVOICENR)
			WHERE INVOICENR='.$invoicenr
		);
		if ($invoice === false) {
			return;
		}
		if (!$invoice) {
			register_error('invoice:error:nonexistent_LINE',array('ID'=>$invoicenr));
			return;
		}
	} else {
		$invoice = null;
	}

	layout_show_section_header();

	?><form<?
	?> enctype="multipart/form-data"<?
	?> onsubmit="return submitForm(this)"<?
	?> method="post"<?
	?> action="/invoice/<?
	if ($invoicenr) {
		echo $invoicenr; ?>/<?
	}
	?>commit"><?
	layout_open_box('yellow');
	layout_open_table('fw');
	layout_start_row();
	echo Eelement_name('invoice_number');
	layout_field_value();
	if ($invoicenr) {
		echo $invoicenr;
	} else {
		?><input class="id" type="number" data-valid="number" min="1" required="required" name="INVOICENR" value="<? if ($invoicenr) echo $invoicenr; ?>" /><?
	}

	?><input type="hidden" name="DEBNR" value="<?= $invoicenr ? $invoice['DEBNR'] : 0 ?>" /><?

/*	layout_restart_row();
	echo Eelement_name('debtor_number');
	layout_field_value();
	?><input class="id" type="number" data-valid="number" min="0" name="DEBNR" value="<? if ($invoicenr) echo $invoice['DEBNR']; ?>" /><?*/

	layout_restart_row();
	echo Eelement_name('relation');
	layout_field_value();
	require_once '_relationlist.inc';
	?><select required="required" name="RELATIONID"><?
	?><option></option><?
	relationlist_show_options($invoice ? $invoice['RELATIONID'] : null);
	?></select><?

	layout_restart_row();
	echo Eelement_name('invoice_date');
	layout_field_value();
	_date_display_select_stamp($invoicenr ? $invoice['SENTSTAMP'] : CURRENTSTAMP, 'SENT');

	layout_restart_row();
	echo Eelement_name('money_amount');
	layout_field_value();
	?><input class="right id" type="number" data-valid="number" min=0 name="AMOUNT_NEW" value="<? if ($invoicenr) echo $invoice['AMOUNT_NEW']; ?>" /> <? echo __('amount_in_eurocents');

	layout_restart_row();
	echo Eelement_name('status');
	layout_field_value();
	?><select name="STATUS"><?
		?><option value="notpaid"><?= __('paystatus:notpaid'); ?></option><?
		?><option<? if ($invoicenr && $invoice['STATUS'] == 'paid') echo ' selected="selected"'; ?> value="paid"><?= __('paystatus:paid'); ?></option><?
		?><option<? if ($invoicenr && $invoice['STATUS'] == 'irrecoverable') echo ' selected="selected"'; ?> value="irrecoverable"><?= __('paystatus:irrecoverable'); ?></option><?
		?><option<? if ($invoicenr && $invoice['STATUS'] == 'ignore') echo ' selected="selected"'; ?> value="ignore"><?= __('paystatus:ignore'); ?></option><?
	?></select><?

	layout_restart_row();
	?><label for="mailed"><?= __C('status:sent') ?></label><?
	layout_field_value();
	?><label><?

	$show_id = !$invoice || $invoice['MAIL_USERID'] === null;

	show_input([
		'type'		=> 'checkbox',
		'id'		=> 'mailed',
		'checked'	=> $checked = $invoice && $invoice['MAIL_STAMP'],
		'disabled'	=> $checked,
		'value'		=> 1,
		'name'		=> 'MAILED',
		'onclick'	=> $show_id ? "setdisplay('tickid',this.checked);/*setattr(this.form.MAIL_TICKETID,'required',this.checked)*/" : null
	]);
	?> (<?= $invoice['MAIL_STAMP'] ? ($invoice['MAIL_USERID'] ? get_element_link('user',$invoice['MAIL_USERID']) : __('attrib:automatic')) : __('attrib:manual') ?>)<?
	?></label><?

	if ($show_id) {
		?> <span<?
		if (!$checked) {
			?> class="hidden"<?
		}
		?> id="tickid"><?
		echo element_name('contact_ticket') ?>: <?
		show_input([
			'name'	=> 'MAIL_TICKETID',
			'style'	=> 'width:10em',
			'type'	=> 'number',
			'value'	=> $invoice['MAIL_TICKETID'] ?: null
		]);
		?></span><?
	}

	layout_restart_row();
	?><label for="debtcoll">&rarr; <?= Eelement_name('collection_agency') ?></label><?
	layout_field_value();
	show_input(array(
		'type'		=> 'checkbox',
		'id'		=> 'debtcoll',
		'checked'	=> $invoice['DEBTCOLLECTION'],
		'name'		=> 'DEBTCOLLECTION',
		'value'		=> $invoice['DEBTCOLLECTION'] ?: CURRENTSTAMP,
	));

	layout_restart_row();
	echo Eelement_name('file');
	layout_field_value();
	show_input([
		'type'		=> 'file',
		'accept'	=> 'application/pdf',
		'name'		=> 'INVOICE',
	]);

	layout_close_table();
	layout_close_box();
	?><div class="block"><input type="submit" value="<?= __($invoicenr ? 'action:change' : 'action:add'); ?>" /></div><?
	?></form><?
}
function invoice_commit() {
	if (($invoicenr = have_idnumber($_POST,'INVOICENR'))
	&&	db_single('invoice','SELECT 1 FROM invoice WHERE INVOICENR='.$invoicenr)
	) {
		warning('Factuur bestaat al!');
		store_messages_in_cookie();
		header('Location: https://'.$_SERVER['HTTP_HOST'].'/invoice/'.$invoicenr, true, 303);
		exit;
	}

	if (!require_admin('invoice')
	||	false === require_number($_POST,'DEBNR')
	||	false === require_number($_POST,'RELATIONID')
	||	!require_date($_POST,'SENT')
	||	false === require_element($_POST, 'STATUS', ['ignore', 'irrecoverable', 'notpaid', 'paid'])
		||	false === require_number($_POST,'AMOUNT_NEW')
	) {
		return;
	}
	if (isset($_POST['MAILED'])
	&&	($ticketid = have_idnumber($_POST,'MAIL_TICKETID'))
	) {
#		if (!db_single('contact_ticket','SELECT 1 FROM contact_ticket WHERE ELEMENT="invoice"'.($_REQUEST['sID'] ? ' AND ID='.$_REQUEST['sID'] : null))) {
		if (!db_single('contact_ticket','SELECT 1 FROM contact_ticket WHERE TICKETID='.$ticketid)) {
			register_error('invoice:error:ticket_not_correct_LINE',DO_UBB,['TICKETID'=>$ticketid]);
			return;
		}
	}
	$setlist = [
		'RELATIONID'	=> $_POST['RELATIONID'],
		'DEBNR'			=> $_POST['DEBNR'],
		'SENTSTAMP'		=> _dateonly_getstamp($_POST,'SENT'),
		'STATUS'		=> $_POST['STATUS'],
		'AMOUNT_NEW'	=> $_POST['AMOUNT_NEW'],
		'DEBTCOLLECTION'=> have_idnumber($_POST, 'DEBTCOLLECTION') ?: 0,
	];

	if ($invoicenr = have_idnumber($_POST,'INVOICENR')) {
		$setlist['INVOICENR'] = $invoicenr;
	}
	[$instr,, $bincmp] = inserts_values_and_bincmp($setlist);
	if (!$invoicenr) {
		if (!($invoicenr = require_idnumber($_REQUEST,'sID'))) {
			return;
		}
		if (!db_insert('invoice_log','
			INSERT INTO invoice_log
			SELECT *,'.CURRENTSTAMP.' FROM invoice
			WHERE INVOICENR = '.$invoicenr.'
			  AND NOT '.$bincmp)
		) {
			return;
		}
		if (!db_affected()) {
			if (!isset($_POST['MAILED'])) {
				$nothing_changed = true;
			}
		} else {
			if (!db_update('invoice','
				UPDATE invoice SET
					MUSERID	='.CURRENTUSERID.',
					MSTAMP	='.CURRENTSTAMP.',
					'.$instr.'
				WHERE INVOICENR='.$invoicenr)
			) {
				return;
			}
			register_notice('invoice:notice:changed_LINE');
		}
	} elseif ($invoicenr) {
		if (!db_insert('invoice','
			INSERT INTO invoice SET
				UNIQID		="'.create_password(32,null,false).'",
				USERID		='.CURRENTUSERID.',
				CSTAMP		='.CURRENTSTAMP.',
				'.$instr)
		) {
			return;
		}
		register_notice('invoice:notice:added_LINE');
		$_REQUEST['sID'] = $invoicenr;
	}
	if (isset($_POST['MAILED'])) {
		db_insert('invoice_mailed','
		INSERT IGNORE INTO invoice_mailed SET
			TICKETID	='.$ticketid.',
			INVOICENR	='.$invoicenr.',
			STAMP		='.CURRENTSTAMP.',
			USERID		='.CURRENTUSERID
		);
		register_notice('invoice:notice:manually_mailed_LINE');
	}
	if (have_file('INVOICE')) {
		$data = file_get_contents($_FILES['INVOICE']['tmp_name']);
		if (!db_insert('invoicedata_log','
			INSERT INTO data_db.invoicedata_log
			SELECT * FROM data_db.invoicedata
			WHERE INVOICENR='.$invoicenr)
		||	!db_replace('invoicedata','
			REPLACE INTO data_db.invoicedata SET
				INVOICENR	='.$invoicenr.',
				CSTAMP		='.CURRENTSTAMP.',
				CUSERID		='.CURRENTUSERID.',
				DATA		="'.addslashes($data).'"')
		||	!db_replace('invoice_pdf_processed','
			REPLACE INTO invoice_pdf_processed SET
				INVOICENR	='.$invoicenr.',
				STAMP		='.CURRENTSTAMP.',
				USERID		='.CURRENTUSERID)
		) {
			return;
		}
	} elseif (isset($nothing_changed)) {
		register_notice('invoice:warning:nothing_changed_LINE');
	}
}
function invoice_may_mark_read($invoice) {
	return	!$invoice['READM']
	&&	$invoice['STATUS'] == 'notpaid';
#	&&	CURRENTSTAMP < $invoice['SENTSTAMP'] + $invoice['PAY_WITHIN'] * ONE_DAY;
#	^^ allow marking anything as read because pay status is delayed now with 2XPR
}
