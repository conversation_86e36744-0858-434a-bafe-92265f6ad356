<?php

define('CURRENTSTAMP',	time());
define('TODAYSTAMP',	mktime(0,0,0));

require_once '_exit_if_readonly.inc';

function bail(int $errno): never {
	display_messages();
	http_response_code($errno);
	exit;
}

require_once '_voice.inc';
require_once '_rating.inc';
require_once '_currentuser.inc';
require_once '_smiley.inc';
require_once '_flockmod.inc';
require_once '_require.inc';

if (!require_valid_origin()) {
	bail(403);
}

$currentuser = new _currentuser(TINY_USER);

if (!require_user()) {
	bail(403);
}
if (!isset(RATINGABLES[$_REQUEST['sELEMENT']])) {
	bail(400);
}
if (!may_speak()) {
	register_error('rating:error:you_may_not_rate_this_element', ['ELEMENT' => $_REQUEST['sELEMENT']]);
	bail(403);
}
if (!commit_rating()) {
	bail(500);
}
header('Content-Type: text/html; charset=utf-8');

if (isset($_POST['RATING'])) {
	echo	replace_shy(
			_smiley_replace(
				escape_utf8(
					_smiley_prepare($_POST['RATING'], utf8: true)
				),
				utf8: true
			),
			utf8: true
		);
}
