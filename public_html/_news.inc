<?php

function get_news_counts(?string $element = null, ?int $id = null) {
	static $__counts;
	if (isset($__counts[$element][$id])) {
		return $__counts[$element][$id];
	}
	$id ??= $_REQUEST['sID'];
	switch ($element ?: $_REQUEST['sELEMENT']) {
	case 'location':
		$newss = memcached_rowuse_hash(['connect', 'party', 'news'], '
			SELECT NEWSID,ACCEPTED,PSTAMP
			FROM (	SELECT ASSOCID AS NEWSID
				FROM connect
				WHERE MAINTYPE="location"
				  AND MAINID='.$id.'
				  AND ASSOCTYPE="news"
			UNION SELECT ASSOCID AS NEWSID
				FROM connect
				JOIN party ON MAINID=PARTYID
				WHERE LOCATIONID='.$id.'
				  AND MAINTYPE="party"
				  AND ASSOCTYPE="news"
			) AS combined
			JOIN news USING (NEWSID)
			WHERE TYPE!="compilation"
			ORDER BY PSTAMP DESC',
			TEN_MINUTES
		);
		$maxage = ONE_YEAR;
		break;
	case 'organization':
		$newss = memcached_rowuse_hash(['connect', 'news'], '
			SELECT NEWSID,ACCEPTED,PSTAMP
			FROM (	SELECT ASSOCID AS NEWSID
				FROM connect
				WHERE MAINTYPE="organization"
				  AND ASSOCTYPE="news"
				  AND MAINID='.$id.'
				UNION
				SELECT c2.ASSOCID AS NEWSID
				FROM connect AS c1
				JOIN connect AS c2 ON c1.MAINTYPE="organization"
				JOIN news ON NEWSID=c2.ASSOCID
				  AND c1.MAINID		='.$id.'
				  AND c1.ASSOCTYPE	="party"
				  AND c1.ASSOCID	= c2.MAINID
				  AND c2.MAINTYPE	="party"
				  AND c2.ASSOCTYPE	="news"
			) AS combined
			JOIN news USING (NEWSID)
			WHERE TYPE!="compilation"
			ORDER BY PSTAMP DESC',
			TEN_MINUTES
		);
		$maxage = ONE_YEAR;
		break;
	case 'artist':
		$newss = memcached_rowuse_hash(['connect','news'], '
			SELECT NEWSID, ACCEPTED, PSTAMP
			FROM (	SELECT ASSOCID AS NEWSID
				FROM connect
				WHERE MAINTYPE = "artist"
				  AND MAINID = '.$id.'
				  AND ASSOCTYPE = "news"
			) AS combined
			JOIN news USING (NEWSID)
			WHERE TYPE != "compilation"
			ORDER BY PSTAMP DESC',
			TEN_MINUTES
		);
		$maxage = ONE_YEAR;
		break;
	case 'party':
		$newss = memcached_rowuse_hash(
			['connect','news'], '
			SELECT NEWSID, ACCEPTED, PSTAMP
			FROM connect
			JOIN news ON NEWSID = ASSOCID
			WHERE MAINTYPE = "party"
			  AND MAINID = '.$id.'
			  AND ASSOCTYPE = "news"
			  AND TYPE != "compilation"
			ORDER BY PSTAMP DESC ',
			TEN_MINUTES
		);
		break;

	default:
		error_log("unsupported element ($element) in get_news_counts()");
		return [0, 0, []];
	}
	if (!$newss) {
		return $__counts[$element][$id] = [$newss, $newss, $newss];
	}
	$news_admin = have_admin('news');
	$total = $teasercnt = 0;
	$teasers = [];
	foreach ($newss as $newsid => $news) {
		if ($news['ACCEPTED']
		&&	CURRENTSTAMP >= $news['PSTAMP']
		||	$news_admin
		) {
			++$total;
			++$teasercnt;
			$teasers[$newsid] = $newsid;
		}
	}
	return $__counts[$element][$id] = [$total, $teasercnt, $teasers];
}
function show_news_teasers(bool $force_all = false): void {
	[$total, $teasercnt, $teasers] = get_news_counts();
	if (!$teasers) {
		return;
	}
	if (!$force_all && $_REQUEST['ACTION'] !== 'news' && $total > 10) {
		$teasers = array_slice($teasers,0,10);
		$teasercnt = 10;
	}
	require_once '_newslist.inc';
	$link = $total > $teasercnt ? '/'.$_REQUEST['sELEMENT'].'/'.$_REQUEST['sID'].'/news#news' : null;
	layout_open_box($_REQUEST['sELEMENT'],'news');
	layout_box_header(Eelement_name('news',$total),
		$link
		? '<a href="'.$link.'">'.__('action:show_all').'</a> <small>('.$total.')</small>'
		: null
	);
	$newslist = new _newslist();
	$newslist->order_by_descending_pstamp();
	$newslist->with_ids($teasers);
	if ($newslist->query()) {
		$newslist->display();
	}
	layout_close_box();
	if ($total > $teasercnt) {
		?><div class="funcs"><?
		?><a href="<?= $link ?>"><?= __('action:show_all_x_news_messages',array('X'=>$total)) ?></a><?
		?></div><?
	}
}
