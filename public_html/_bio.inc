<?php

declare(strict_types=1);

const ELEMENTS_WITH_BIO = [
	'artist'		=> 'biography',
	'location'		=> 'description',
	'organization'	=> 'description',
	'party'			=> 'information',
	'stream'		=> 'description',
	'user'			=> 'biography',
];

function may_have_bio(?string $element = null): string|array|null {
	if ($element) {
		return ELEMENTS_WITH_BIO[$element] ?? null;
	}
	return ELEMENTS_WITH_BIO;
}

function get_bio_base(string $element, int $id): array|false {
	static	$__bio = [];
	return	$__bio[$element][$id] ??= db_single_assoc('bio','
		SELECT BODY, MSTAMP, LANGID, NEED_CHECK
		FROM bio
		WHERE ELEMENT = "'.addslashes($element).'"
		  AND ID = '.$id.'
		ORDER BY LANGID = '.CURRENTLANGID.' DESC, LANGID
		LIMIT 1'
	) ?: false;
}

function get_teaser(
	string  $BODY,
	?bool   &$same		= null,
	?int	$teaser_len = null,
): string {
	$teaser_len ??= 1000;
	if ($same = mb_strlen($BODY) < $teaser_len) {
		$same = true;
		return $BODY;
	}
	# need prefix/postfix of 0/1 to make sure trimming does not strip control chars
	$separator_start	= "0\x05";
	$separator_end		= "\x06".'1';
	$i = 0;
	if (null === ($BODY = preg_replace_callback('"(?<ubb_tag>\[[^]]*])"u', static function (array $match) use (&$reps, &$i, $separator_start, $separator_end): string {
		if ($match['ubb_tag'] === '[hr]') {
			return '';
		}
		$reps[++$i] = $match['ubb_tag'];
		return $separator_start.$i.$separator_end;
	}, $BODY))
	) {
		mail_log('BODY is null in bio.get_teaser', get_defined_vars());
		$BODY = '';
	}

	$TEASER = utf8_mytrim(mb_substr($BODY, 0, $teaser_len));

	if ($pos = mb_strrpos($TEASER, "\n\n")) {
		$TEASER = mb_substr($TEASER, 0, $pos);
	} else {
		$len = mb_strlen($TEASER);
		$ok = false;
		$offset = 0;
		while (!$ok) {
				($pos = mb_strrpos($TEASER, '.', $offset))
			||	($pos = mb_strrpos($TEASER, '!', $offset))
			||	($pos = mb_strrpos($TEASER, '?', $offset));

			if ($pos === false) {
				break;
			}
			$test = mb_substr($TEASER, 0, $pos + 1);
			if (!preg_match('"\sMr\.$"u', $test)) {
				$TEASER = $test;
				$ok = true;
				break;
			}
			$offset = - ($len - $pos) - 1;
		}
		if (!$ok) {
			if (($endpos = mb_strrpos($TEASER, '!'))
			||	($endpos = mb_strrpos($TEASER, '.'))
			||	($endpos = mb_strrpos($TEASER, '?'))
			) {
				$TEASER = mb_substr($TEASER, 0, $endpos);
			} else {
//				error_log_r($TEASER,'WARNING teaser notok @ '.$_SERVER['REQUEST_URI']);
			}
		}
		if (preg_match('"([.?!])+$"u', $TEASER, $match)) {
			$TEASER = utf8_mytrim($TEASER, '.?!').$match[1];
		}
		$TEASER .= '…';
	}
	if ($reps
	&&	str_contains($TEASER, $separator_start)
	&&	null === ($TEASER = preg_replace_callback(
		'"'.$separator_start.'(\d+)'.$separator_end.'"u',
		static fn (array $match): string => $reps[$match[1]],
		$TEASER))
	) {
		mail_log('TEASER is null in bio.get_teaser after preg_replace_callback', get_defined_vars());
		$TEASER = '';
	}
	return utf8_mytrim($TEASER);
}

function show_bio_form(): bool {
	require_once '_language.inc';
	require_once '_countryflag.inc';
	$element = $_REQUEST['sELEMENT'];
	layout_show_section_header(
		null,
		Eelement_name(
			$element === 'artist'
		||	$element === 'user'
		?	'biography'
		 :	'information')
	);
	if (!may_change_bio($element, $id = $_REQUEST['sID'])) {
		return false;
	}
	layout_open_box('white');
	?><div class="block"><?= __('bio:contact_us_TEXT', DO_NL2BR | DO_UBB, $map = ['ELEMENT' => $element, 'ID' => $id]) ?></div><?
	if ($element !== 'user') {
		?><div class="block"><?= __('bio:may_change_TEXT', DO_NL2BR | DO_UBB, $map) ?></div><?
	}
	layout_close_box();
	if (false === ($bios = db_rowuse_hash('bio', "
		SELECT LANGID, MSTAMP, MUSERID, CSTAMP, CUSERID, BODY
		FROM bio
		WHERE ELEMENT = '$element'
		  AND ID = $id"))
	) {
		return false;
	}
	if (!($languages = get_languages())) {
		return $languages !== false;
	}

	include_js('js/form/bio');
	?><form<?
	?> accept-charset="utf-8"<?
	?> id="bioform"<?
	?> onsubmit="return false;"<?
	?> action="#"><?
	?><input type="hidden" name="enc" value="&#129392;" /><?
	?><input type="hidden" name="ELEMENT" value="<?= $element ?>" /><?
	?><input type="hidden" name="ID" value="<?= $id ?>" /><?

	$spec = explain_table('bio');
	$ubb_flags = (UBB_ALL & ~UBB_OBJECTS) | UBB_UTF8;

	foreach ($languages as $langid => $language) {
		$BODY = null;
		if ($have = isset($bios[$langid])) {
			/** @noinspection PhpRedundantOptionalArgumentInspection */
			extract($bios[$langid], \EXTR_OVERWRITE);
		}
		?><div lang="<?= $language['SHORTCODE'] ?>"><?
		layout_open_box('white');
		layout_box_header(get_country_flag($language['FLAGCOUNTRYID']).' '.escape_utf8($language['NAME']));

		$same = true;

		$TEASER = $BODY ? get_teaser($BODY, $same) : null;

		?><input type="hidden" name="SAME[<?= $langid ?>]" value="<?= $same ? '1' : '0' ?>" /><?
		?><fieldset<?
		if (!$TEASER || $same) {
			?> class="hidden"<?
		}
		?> style="border: 1px solid grey; margin-bottom: .4em;"><?
		?><legend><?= element_name('teaser') ?></legend><?
		?><div class="body block" id="teaser<?= $langid ?>"><?
		if ($TEASER) {
			echo make_html($TEASER, $ubb_flags, $element.'_bio_teaser', $id);
		}
		?></div><?
		?></fieldset><?
		show_textarea_utf8([
			'class'		=> 'hidden block noverflow growToFit',
			'maxlength'	=> $spec['BODY']->maxlength,
			'rows'		=> $BODY ? (substr_count($BODY,"\n") + 2) : null,
			'cols'		=> 80,
			'name'		=> "BODY$langid",
			'value'		=> $BODY."\n",
			'lang'		=> $language['SHORTCODE'],
		]);
		?><div class="body block" id="body<?= $langid ?>"><?
		if ($BODY) {
			echo make_html($BODY, $ubb_flags, $element.'_bio_body', $id);
		}
		if ($have) {
			layout_display_alteration_note($bios[$langid]);
		}
		?></div><?
		layout_close_box();
		?></div><?
		?><div class="funcs block"><?
		?><input<?
		?> name="CHANGE<?= $langid ?>"<?
		?> type="button"<?
		?> class="unhideanchor"<?
		?> onclick="return Pf_changeBio(this, <?= $langid ?>);"<?
		?> value="<?= __('action:change') ?>" /> <?

		?><input <?
		?> name="PREVIEW<?= $langid ?>"<?
		?> type="button"<?
		?> class="hidden"<?
		?> onclick="return Pf_previewBio(this, <?= $langid ?>);"<?
		?> value="<?= __('action:preview') ?>" /> <?

		?><input <?
		?> name="EDIT<?= $langid ?>"<?
		?> type="button"<?
		?> class="hidden"<?
		?> onclick="return Pf_editBio(this, <?= $langid ?>);"<?
		?> value="<?= __('action:edit') ?>" /><?
		?></div><?
	}
	layout_close_table();
	?></form><?
	return true;
}

function show_bio_form_part(): bool {
	require_once '_language.inc';
	require_once '_countryflag.inc';
	# party only
	$element = $_REQUEST['sELEMENT'];
	if ($id = $_REQUEST['sID']) {
		if (false === ($bios = db_rowuse_hash('bio', "
			SELECT LANGID, MSTAMP, MUSERID, CSTAMP, CUSERID, BODY
			FROM bio
			WHERE ELEMENT = '$element'
			  AND ID = $id"))
		) {
			return false;
		}
	} else {
		$bios = [];
	}
	assert(is_array($bios)); # Satisfy EA inspection
	if (!($spec = explain_table('bio'))) {
		return false;
	}
	if (!($languages = get_languages())) {
		return $languages !== false;
	}
	foreach ($languages as $langid => $language) {
		$BODY = $bios[$langid]['BODY'] ?? null;
		?><div lang="<?= $language['SHORTCODE'] ?>"><?
		layout_open_box($element);
		layout_box_header(get_country_flag($language['FLAGCOUNTRYID']).' '.escape_utf8($language['NAME']));
		show_textarea_utf8([
			'class'		=> 'block noverflow growToFit',
			'maxlength'	=> $spec['BODY']->maxlength,
			'rows'		=> ($BODY ? substr_count($BODY,"\n") : 0) + 2,
			'cols'		=> 80,
			'name'		=> "BODY$langid",
			'value'		=> $BODY."\n",
			'lang'		=> $language['SHORTCODE'],
		]);
		layout_close_box();
		?></div><?
	}
	return !empty($bios);
}

function show_bio(): bool {
	$element = $_REQUEST['sELEMENT'];
	$id		 = $_REQUEST['sID'];

	if (!($bio = get_bio_base($element, $id))) {
		return false;
	}
	/** @noinspection PhpRedundantOptionalArgumentInspection */
	extract($bio, \EXTR_OVERWRITE);
	if ($NEED_CHECK) {
		register_error('bio:error:needs_check_LINE');
	}
	$teaser = get_teaser($BODY,$same);
	?><article data-partyflock-type="bio" lang="<?= get_language($LANGID) ?>"><?
	?><div class="body block"><?
	?><div><?
	?><h3 class="bold inline"><?= Eelement_name($element === 'artist' || $element === 'user' ? 'biography' : 'information') ?></h3> <?
	if (!ROBOT) {
		?><span class="small light"><?= MIDDLE_DOT_ENTITY ?> <? _date_display($MSTAMP, extra: 'itemprop="dateModified"') ?></span></div><?
	}
	$ubb_flags = (UBB_ALL & ~UBB_OBJECTS) | UBB_UTF8;
	if ($element === 'user') {
		$ubb_flags |= UBB_UGC;
	}
	if (!$same) {
		$elem = may_have_bio($element);
		if ($hide = ($_REQUEST['ACTION'] !== $element)) {
			include_js('js/bio');
			$bio_url = "/$element/$id/$elem";
			?><div id="bioteaser"><?= make_html($teaser, $ubb_flags, $element.'_bio_teaser', $id)
			?><div><?
			?><a<?
			?> data-url="<?= $bio_url ?>"<?
			?> data-title-prefix="<?= element_name($elem) ?>"<?
			?> onclick="return Pf_uncollapseBio(this);"<?
			?> href="<?= $bio_url ?>"><i>&rarr; <?= __('action:read_more') ?></i></a><?
			?></div><?
			?></div><?
		}
		?><div id="biobody"<?
		if ($hide) {
			?> class="hidden"<?
		}
		?>><?= make_html($BODY, $ubb_flags, $element.'_bio_body', $id) ?></div><?
	} else {
		?><div><?= make_html($BODY, $ubb_flags, $element.'_bio', $id) ?></div><?
	}
	?></div><?
	?></article><?
	return true;
}

function may_change_bio(string $element, int $id): bool {
	$input = [
		'ID'		=> $id,
		'ELEMENT'	=> $element
	];
	$user_field = $element === 'stream' ? 'CUSERID' : 'USERID';
	global $currentuser;
	/** @noinspection NotOptimalIfConditionsInspection */
	if (!($id = require_idnumber($input,'ID'))
	||	!require_element($input, 'ELEMENT', array_keys(may_have_bio()), true)
	||	!($item = db_single_assoc($element,"
		SELECT $user_field AS USERID, ACCEPTED
		FROM `$element`
		WHERE {$element}ID = $id"))
	) {
		return false;
	}
	if (!(/* $may_change = */ !$item['ACCEPTED'] && CURRENTUSERID === $item['USERID'])
	&&	(	$element === 'artist'
		&&	!$currentuser->is_artist($id)
		||	$element === 'user'
		&&	$id !== CURRENTUSERID)
	&&	!am_employee($element, $id)
	&&	!require_admin($element)
	) {
		return false;
	}
	return true;
}
function commit_bio(?int $force_by_userid = null): int|array {
	require_once '_language.inc';
	require_once '_ubb.inc';
	require_once '_ubb_preprocess.inc';
	require_once '_nocache.inc';
	require_once '_namefix.inc';
	require_once '_flockmod.inc';
	require_once '_layout.inc';
	require_once '_bio.inc';
	send_no_cache_headers();
	if (!($languages = get_languages())) {
		return 503;
	}
	/** @noinspection NotOptimalIfConditionsInspection */
	if (!($id = require_idnumber($_POST, 'ID'))
	||	!($element = require_element($_POST, 'ELEMENT', array_keys(may_have_bio())))
	||	false === ($langid = require_number($_POST, 'LANGID'))
	||	!assert(is_int($langid)) # Satisfy EA inspection
	||	!isset($languages[$langid])
	||	!require_anything_trim($_POST, 'BODY', true)
	) {
		return 400;
	}
	if ($force_by_userid === null) {
		global $currentuser;
		$currentuser ??= new _currentuser();
		if (!may_change_bio($element, $id)) {
			return  403;
		}
		$userid = CURRENTUSERID;
	} else {
		$userid = $force_by_userid;
	}
	if (false === ($have = db_single_array('bio',"
		SELECT BODY, CSTAMP, CUSERID
		FROM bio
		WHERE LANGID = $langid
		  AND ELEMENT = '$element'
		  AND ID = $id",
		DB_USE_MASTER))
	) {
		return 503;
	}
	[$have_body, $cstamp, $cuserid] = $have;

	$body = get_fixed_utf8_name(str_replace(REPLACEMENT_CHARACTER, '', $_POST['BODY']));
	$body = preg_replace('"\s+([,:.])\s+"u','$1 ',$body) ?? $body;
	$body = _ubb_preprocess($body, true);

	if ($have_body) {
		if (!db_insert('bio_log', '
			INSERT INTO bio_log
			SELECT *, '.CURRENTSTAMP.", $userid
			FROM bio
			WHERE NOT (BODY = BINARY '".addslashes($body)."')
			  AND LANGID = $langid
			  AND ELEMENT = '$element'
			  AND ID = $id")
		) {
			return 503;
		}
		if (!db_affected()) {
			if ($force_by_userid !== null) {
				return 200;
			}
			register_notice('item:notice:no_change_needed_LINE');
			$no_change = true;
		}
	}
	if (!isset($no_change)) {
		if (!$body) {
			if (!db_delete('bio', "
				DELETE FROM bio
				WHERE LANGID = $langid
				  AND ELEMENT = '$element'
				  AND ID = $id")
			) {
				return 503;
			}
		} elseif (!db_insupd('bio', "
			INSERT INTO bio SET
				CUSERID		= $userid,
				MUSERID		= $userid,
				TEASER		= '',
				LANGID		= $langid,
				ELEMENT		= '$element',
				ID			= $id,
				MSTAMP		= ".CURRENTSTAMP.",
				BODY		= '".addslashes($body)."'
			ON DUPLICATE KEY UPDATE
				MSTAMP		= VALUE(MSTAMP),
				MUSERID		= VALUE(MUSERID),
				TEASER		= VALUE(TEASER),
				BODY		= VALUE(BODY),
				NEED_CHECK	= IF(VALUE(BODY) LIKE '%ABOVE IS COMBINED%', 1, 0)")
		) {
			return 503;
		}
		require_once '_pagechanged.inc';
		page_changed($element, $id);
	}
	if ($force_by_userid !== null) {
		return 200;
	}
	header('Content-Type: application/json');
	ob_start();
	layout_display_alteration_note([
		'CUSERID'	=> $cuserid	?: $userid,
		'CSTAMP'	=> $cstamp	?: CURRENTSTAMP,
		'MUSERID'	=> $userid,
		'MSTAMP'	=> CURRENTSTAMP,
	]);
	$ubb_flags = (UBB_ALL & ~UBB_OBJECTS) | UBB_UTF8;
	$teaser = get_teaser($body, $same);
	return [
		'BODY'		=> make_html($body,	$ubb_flags, $element.'_bio_body',   $id),
		'TEASER'	=> make_html($teaser, $ubb_flags, $element.'_bio_teaser', $id),
		'SAME'		=> $same,
		'ALTER'		=> ob_get_clean(),
	];
}

function commit_bios_only(string $element, int $id): bool {
	require_once '_language.inc';
	if (!($languages = get_languages())) {
		return $languages !== false;
	}
	foreach ($languages as $langid => $ignored /* $language */) {
		if (!isset($_POST['BODY'][$langid])) {
			continue;
		}
		if (false === ($have_body = db_single_int('bio', "
			SELECT 1
			FROM bio
			WHERE BODY != ''
			  AND LANGID = $langid
			  AND ELEMENT = '$element'
			  AND ID = $id",
			DB_USE_MASTER))
		) {
			return false;
		}
		$body = get_fixed_utf8_name($_POST['BODY'][$langid]);
		$body = preg_replace('" +([,:.]) +"u','$1 ',$body);
		$body = _ubb_preprocess($body, true);

		if ($have_body) {
			if (!db_insert('bio_log','
				INSERT INTO bio_log
				SELECT *, '.CURRENTSTAMP.', '.CURRENTUSERID."
				FROM bio
				WHERE BODY != BINARY '".addslashes($body)."'
				  AND LANGID = $langid
				  AND ELEMENT = '$element'
				  AND ID = $id")
			) {
				return false;
			}
			if (!db_affected()) {
				continue;
			}
		}
		if (!$body) {
			if (!db_delete('bio', "
				DELETE FROM bio
				WHERE LANGID = $langid
				  AND ELEMENT = '$element'
				  AND ID = $id")
			) {
				return false;
			}
		} elseif (!db_insupd('bio','
			INSERT INTO bio SET
				CUSERID		= '.CURRENTUSERID.',
				CSTAMP		= '.CURRENTSTAMP.',
				MUSERID		= '.CURRENTUSERID.',
				MSTAMP		= '.CURRENTSTAMP.",
				BODY		= '".addslashes($body)."',
				TEASER		= '',
				LANGID		= $langid,
				ELEMENT		= '$element',
				ID			= $id
			ON DUPLICATE KEY UPDATE
				MSTAMP		= VALUE(MSTAMP),
				MUSERID		= VALUE(MUSERID),
				TEASER		= VALUE(TEASER),
				BODY		= VALUE(BODY),
				NEED_CHECK	= IF(VALUE(BODY) LIKE '%ABOVE IS COMBINED%', 1, 0)")
		) {
			return false;
		}
	}
	return true;
}
