<?php

declare(strict_types=1);

require_once '_buddies.inc';

function _accept_buddy(int $userid_ini): bool {
	if ($userid_ini === CURRENTUSERID) {
		register_error('buddy:error:self_request_LINE');
		return false;
	}
#	require_once '_notify.inc';
#	if (!is_reachable($userid_ini,NOTIFY_BUDDY)) {
#		register_error('buddy:error:inactive_user_LINE',DO_UBB,array('USERID'=>$userid_ini));
#		return;
#	}
	if ($userid_ini > CURRENTUSERID) {
		$max_id = $userid_ini; $min_id = CURRENTUSERID;
	} else {
		$min_id = $userid_ini; $max_id = CURRENTUSERID;
	}
	if (!require_user()
	||	!require_getlock($lock_name = "buddy_{$min_id}_$max_id")
	) {
		return false;
	}
	if (!(db_single_int('buddy_request', "
		SELECT CSTAMP
		FROM buddy_request
		WHERE USERID_INI = $userid_ini
		  AND USERID_ACC = ".CURRENTUSERID))
	) {
		register_notice('buddy:error:nonexistent_request_LINE',DO_UBB, ['USERID' => $userid_ini]);
	} elseif (
		db_single('buddy', "
		SELECT 1 FROM buddy
		WHERE USERID_ACC = $userid_ini
		  AND USERID_INI = ".CURRENTUSERID."
		   OR USERID_INI = $userid_ini
		  AND USERID_ACC = ".CURRENTUSERID)
	) {
		register_notice('buddy:notice:friendship_already_accepted_LINE',DO_UBB, ['USERID' => $userid_ini]);
	} elseif (
		db_insert('buddy', '
		INSERT IGNORE INTO buddy SET
			ASTAMP		= '.CURRENTSTAMP.',
			USERID_ACC	= '.CURRENTUSERID.",
			USERID_INI	= $userid_ini")
	&&	db_delete('buddy_request', "
		DELETE FROM buddy_request
		WHERE USERID_INI = $userid_ini
		  AND USERID_ACC = ".CURRENTUSERID.'
		   OR USERID_INI = '.CURRENTUSERID."
		  AND USERID_ACC = $userid_ini")
	) {
		register_notice('buddy:notice:friendship_accepted_LINE',DO_UBB, ['USERID' => $userid_ini]);
		flush_buddies($userid_ini);
		flush_buddies(CURRENTUSERID);
		$ok = true;
	}
	db_releaselock($lock_name);
	return isset($ok);
}

function create_buddies(int $userid_ini, int|array $arg): int|false {
	/** @noinspection SpellCheckingInspection */
	$userid_accs = !is_array($arg) ? [$arg] : $arg;
	$setlist     = [];
	foreach ($userid_accs as $userid_acc) {
		$setlist[] = "(b'1', ".CURRENTSTAMP.", $userid_ini, $userid_acc)";
	}
	if (!db_insert('buddy','
		INSERT IGNORE INTO buddy (VIA_FB,ASTAMP,USERID_INI,USERID_ACC)
		VALUES '.implode(',',$setlist))
	) {
		return false;
	}
	$inserted = db_affected();
	flush_buddies($userid_ini);
	foreach ($userid_accs as $userid_acc) {
		flush_buddies($userid_acc);
	}
	return $inserted;
}

function destroy_buddies(int $userid_ini, int|array $arg): int|false {
	/** @noinspection SpellCheckingInspection */
	$userid_accs = !is_array($arg) ? [$arg] : $arg;

	$user_idstr = implode(',',$userid_accs);

	$where = "	VIA_FB
		  AND	(USERID_INI = $userid_ini
			 AND USERID_ACC IN ($user_idstr)
			  OR USERID_ACC = $userid_ini
			 AND USERID_INI IN ($user_idstr)
			)";

	if (!db_insert('buddy_log', '
		INSERT INTO buddy_log (USERID_INI, USERID_ACC, ASTAMP, VIA_FB, DSTAMP, DUSERID)
		SELECT USERID_INI, USERID_ACC, ASTAMP, VIA_FB, '.CURRENTSTAMP.', '.CURRENTUSERID.'
		FROM buddy
		WHERE '.$where)
	||	!db_delete('buddy','
		DELETE FROM buddy
		WHERE '.$where)
	) {
		return false;
	}
	$deleted = db_affected();
	flush_buddies($userid_ini);
	foreach ($userid_accs as $userid_acc) {
		flush_buddies($userid_acc);
	}
	return $deleted;
}
function _remove_buddy(int $other_id, int $self_id = 0): bool {
	layout_show_section_header();
	if (!$other_id) {
		return false;
	}
	if (!$self_id) {
		$self_id = CURRENTUSERID;
	}
	if ($other_id > $self_id) {
		$max_id = $other_id; $min_id = $self_id;
	} else {
		$min_id = $self_id; $max_id = $other_id;
	}
	if (!require_user()
	||	!require_getlock($lock_name = "buddy_{$min_id}_$max_id")
	) {
		return false;
	}
	if (!db_insert('buddy_log', '
		INSERT INTO buddy_log (USERID_INI, USERID_ACC, ASTAMP, VIA_FB, DSTAMP, DUSERID)
		SELECT USERID_INI, USERID_ACC, ASTAMP, VIA_FB, '.CURRENTSTAMP.', '.CURRENTUSERID."
		FROM buddy
		WHERE USERID_INI = $min_id
		  AND USERID_ACC = $max_id
		   OR USERID_ACC = $min_id
		  AND USERID_INI = $max_id")
	||	!db_delete('buddy', "
		DELETE FROM buddy
		WHERE USERID_INI = $min_id
		  AND USERID_ACC = $max_id
		   OR USERID_ACC = $min_id
		  AND USERID_INI = $max_id")
	) {
		db_releaselock($lock_name);
		return false;
	}
	$deleted = db_affected();

	if (!db_insert('buddy_request_log','
		INSERT INTO buddy_request_log (USERID_INI, USERID_ACC, CSTAMP, DSTAMP, DUSERID)
		SELECT USERID_INI, USERID_ACC, CSTAMP, '.CURRENTSTAMP.', '.CURRENTUSERID."
		FROM buddy_request
		WHERE USERID_INI = $min_id
		  AND USERID_ACC = $max_id
		   OR USERID_ACC = $min_id
		  AND USERID_INI = $max_id")
	||	!db_delete('buddy_request', "
		DELETE FROM buddy_request
		WHERE USERID_INI = $min_id
		  AND USERID_ACC = $max_id
		   OR USERID_ACC = $min_id
		  AND USERID_INI = $max_id")
	) {
		db_releaselock($lock_name);
		return false;
	}
	register_notice($deleted ? 'buddy:notice:friendship_removed_LINE' : 'buddy:notice:friendship_denied_LINE',DO_UBB, ['USERID' => $other_id]);
	flush_buddies($min_id);
	flush_buddies($max_id);
	db_releaselock($lock_name);
	return true;
}
