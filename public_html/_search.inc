<?php

declare(strict_types=1);

require_once 'defines/search.inc';

function flora_show_search(): void {
	if ($forum = in_array($_REQUEST['sELEMENT'], [
		'forum',
		'topic',
	])) {
		$parent = 'forum';
		$topic = 'topic';
		$message = 'message';
		$parentidname = 'FORUMID';
	} else {
		$parent = 'flock';
		$topic = 'flocktopic';
		$message = 'flockmessage';
		$parentidname = 'FLOCKID';
	}

	layout_show_section_header(Eelement_name($parent).' '.MIDDLE_DOT_ENTITY.' '.__('action:search'));

	$topicid = have_idnumber($_REQUEST, 'TOPICID');
	$parentid = have_idnumber($_REQUEST, $parentidname) ?: false;

	if ($topicid) {
		$actual_parentid = memcached_single($topic,'
			SELECT '.$parentidname.'
			FROM '.$topic.'
			WHERE TOPICID = '.$topicid
		);
		if ($actual_parentid === false) {
			return;
		}
		if (!$actual_parentid) {
			not_found($topic, $topicid);
			return;
		}
		if ($parentid !== false
		&&	$actual_parentid !== $parentid
		) {
			error_log('actual HUH @ '.$_SERVER['REQUEST_URI']);
		}
		$parentid = $actual_parentid;

		$GLOBALS['actual_parentid'] = $parentid;
	}

	?><form<?
	?> accept-charset="utf-8"<?
	?> method="get"<?
	?> onsubmit="return submitForm(this);"<?
	?> action="/<?= $topic ?>/searchresult#results"><?

	if ($forum) {
		require_once '_supersearch.inc';
		show_search_header();
	}

	layout_open_box('white');
	layout_box_header(Eelement_plural_name($forum ? 'forum' : 'flock'));

	require_once '_bubble.inc';

	layout_open_table('fw');
	layout_start_row();
	?><label for="terms"><?= __C('action:search_for') ?></label><?
	layout_field_value();

	?><input id="terms" type="search" name="TERMS" id="terms" required<?
	if (!empty($_REQUEST['TERMS'])) {
		?> value="<?= escape_utf8($_REQUEST['TERMS']) ?>"<?
	}
	?>> <?
	$bubble = new bubble(HELP_BUBBLE | BUBBLE_30_WIDE);
	$bubble->catcher();
	$bubble->content(__('search:help:overview_TEXT',DO_NL2BR | DO_UBB));
	$bubble->display();

	layout_restart_row();
	echo __C('attrib:in');
	layout_field_value();
	if ($topicid) {
		?><input type="hidden" name="TYPE" value="<?= $message ?>" /><?
		?><input type="hidden" name="TOPICID" value="<?= $topicid ?>" /><?

		?><strong><?= get_element_link($parent,$parentid) ?></strong>: <?
		echo get_element_link($topic,$topicid);

		$topic_type = false;
	} else {
		include_js('js/form/search');
		$type = $_REQUEST['TYPE'] ?? $message;
		$topic_type = str_contains($type, 'topic');
		foreach ([
			$topic		=> element_plural_name('topic_title'),
			$message	=> element_plural_name($message),
		] as $val => $desc) {
			$checked = $type === $val;
			?><div><label for="type" class="<?
			if (!$checked) {
				?>not-<?
			}
			?>bold-hilited"><?
			show_input([
				'type'			=> 'radio',
				'id'			=> 'type',
				'name'			=> 'TYPE',
				'checked'		=> $checked,
				'class'			=> 'upLite',
				'value'			=> $val,
				'data-topic'	=> str_contains($val,'topic'),
				'onclick'		=> 'Pf.changeSearchElement(this)',
			])
			?> <?= $desc ?></label></div><?
		}

		$parents = $_REQUEST[$forum ? 'FORUMID' : 'FLOCKID'] ?? null;
		$all_parents = $parents && (is_array($parents) ? in_array(0, $parents, true) : $parents === 0);

		if ($forum || $parentid) {
			layout_restart_row();
			?><label for="parent"><?= Eelement_name($parent) ?></label><?
			layout_field_value();
			if ($forum) {
				require_once '_forumlist.inc';
				?><select id="parent" name="FORUMID[]" multiple="multiple" size="10"><?
				?><option<?
				if (!$parentid && !$parents || $all_parents) {
					?> selected="selected"<?
				}
				?> value="0"><?= element_plural_name('all_forum') ?></option><?
				_forumlist_display_accessible($parentid ?: $parents);
			} else {
				?><select id="parent" name="FLOCKID"><?
				?><option<?
				if (!$parentid && !$parents || $all_parents) {
					?> selected="selected"<?
				}
				?> value="0"><?= element_plural_name('all_flock') ?></option><?

				$flocks = member_of_flocks();
				$flocks[$parentid] = $parentid;
				foreach ($flocks as $flockid => &$name) {
					$name = get_element_title('flock',$flockid);
				}
				unset($name);
				asort($flocks);
				foreach ($flocks as $flockid => $name) {
					?><option<?
					if ($flockid === $parentid) {
						?> selected="selected"<?
					}
					?> value="<?= $flockid ?>"><?= escape_specials($name) ?></option><?
				}
			}
			?></select><?
		}
	}

	layout_restart_row();
	?><label for="publication"><?= Eelement_name('publication') ?></label><?
	layout_field_value();
	$period = have_idnumber($_REQUEST,'PERIOD');
	$is_admin = have_admin();
	?><select id="publication" name="PERIOD"><?
		?><option value="0"></option><?
		foreach ([
			2 * ONE_YEAR	=> 'past_two_years',
			ONE_YEAR		=> 'past_year',
			SIX_MONTHS		=> 'past_half_year',
			ONE_MONTH		=> 'past_month',
			ONE_DAY			=> 'past_day',
#			ONE_HOUR		=> 'past_hour',
		] as $local_period => $desc) {
			if ($local_period < 15778800 && !$is_admin) {
				continue;
			}
			?><option<?
			if ($period === $local_period) {
				?> selected<?
			}
			?> value="<?= $local_period ?>"><?= element_name($desc) ?></option><?
		}
	?></select><?

	layout_restart_row();
	?><label for="order"><?= Eelement_name('order') ?></label><?
	layout_field_value();
	?><select id="order" name="ORDER"><?
		$selected = getifset($_REQUEST,'ORDER');
		foreach ([
			'date'			=> !$topic_type,
			'date_last_post'=> $topic_type,
			'date_opened'	=> $topic_type,
			'relevance'		=> true,
		] as $order => $show) {
			?><option value="<?= $order ?>"<?
			if (!$show) {
				?> class="hidden"<?
			}
			if ($order === $selected) {
				?> selected<?
			}
			?>><?= element_name($order) ?></option><?
		}
	?></select><?

	layout_stop_row();
	layout_close_table();
	layout_close_box();

	?><div class="block"><input type="submit" value="<?= __('action:search') ?>" /></div></form><?
}

function flora_show_search_results(): void {
	if ($forum
	=	$_REQUEST['sELEMENT'] === 'forum'
	||	$_REQUEST['sELEMENT'] === 'topic'
	) {
		$parent = 'forum';
		$topic = 'topic';
		$message = 'message';
		$parentidname = 'FORUMID';
	} else {
		$parent = 'flock';
		$topic = 'flocktopic';
		$message = 'flockmessage';
		$parentidname = 'FLOCKID';
	}

	if (!require_something_trim($_REQUEST, 'TERMS')) {
		return;
	}

	layout_show_section_header();

	flora_show_search();

	robot_action('noindex');

	$page = have_idnumber($_REQUEST,'PAGE') ?: 1;

	if (mb_strlen($_REQUEST['TERMS']) < 3) {
		register_error('search:error:term_too_small_LINE');
		return;
	}

	$parentid = false;

	global $currentuser;

	if ($forum) {
		if (isset($_REQUEST[$parentidname])) {
			if (!require_number_array($_REQUEST,$parentidname)) {
				return;
			}
			foreach ($_REQUEST[$parentidname] as $forumid) {
				if (!$forumid) {
					unset($forumids);
					break;
				}
				if ($currentuser->has_forum_read_access($forumid)) {
					$forumids[] = $forumid;
				}
			}
		}
		if ($topicid = have_idnumber($_REQUEST,'TOPICID')) {
			global $actual_parentid;
			if (!$currentuser->has_forum_read_access($actual_parentid)) {
				return;
			}
		}
	} else {
		if (($parentid = have_idnumber($_REQUEST,$parentidname))
		&&	!may_view_element($parent,$parentid)
		) {
			return;
		}
		if (($topicid = have_idnumber($_REQUEST,'TOPICID'))
		&&	!may_view_element($topic,$topicid)
		) {
			return;
		}
	}
	if ($topicid
	&&	!empty($_REQUEST['TYPE'])
	&&	$_REQUEST['TYPE'] !== $message
	) {
		moved_permanently(
			str_replace(
				"TYPE={$_REQUEST['TYPE']}",
				"TYPE=$message",
				$_SERVER['REQUEST_URI']));
	}
	if (!empty($_REQUEST['PUBLICATION'])) {
		$begin_stamp = match ($_REQUEST['PUBLICATION']) {
			'halfyear'	=> TODAYSTAMP - SIX_MONTHS,
			'year'		=> TODAYSTAMP - ONE_YEAR,
			'twoyear'	=> TODAYSTAMP - 2 * ONE_YEAR,
			default		=> null,
		};
	} else {
		$begin_stamp = null;
	}
	if (!$begin_stamp
	&&	($period = have_idnumber($_REQUEST,'PERIOD'))
	) {
		$begin_stamp = TODAYSTAMP - $period;
	}
	$element = $_REQUEST['TYPE'] ?: 'topic';
	$is_message = str_contains($element, 'message');
	$per_page = 40;
	if (('date' === ($order = $_REQUEST['ORDER'] ?? null))
	&&	!$is_message
	) {
		$order = 'date_last_post';
	}
	[$qry, $hilites] = parse_terms($_REQUEST['TERMS'], true);

	$sphinx_servers =
	(	SERVER_VIP
	||	HOME_THOMAS
	||	crc32(igbinary_serialize($_REQUEST)) & 1)
	?	['dbsphinx', 'dbsphinxrep']
	:	['dbsphinxrep', 'dbsphinx'];

	if ($forum) {
		$indices = $is_message ? 'messages,new_messages' : 'topics,new_topics';
	} else {
		$indices = $is_message ? 'flockmessages,new_flockmessages' : 'flocktopics,new_flocktopics';
	}
	$selects = ['*'];
	$wheres = ["MATCH('$qry')"];
	switch ($order) {
	default:
	case 'date':
	case 'date_opened':
		$sphinx_order = 'id';
		break;
	case 'date_last_post':
		$sphinx_order = 'lstamp';
		break;
	case 'relevance':
		$sphinx_order = 'weight()';
		$relevance = true;
		break;
	}

	if ($forum) {
		if (isset($forumids)) {
			$wheres[] = 'forumid /* forumids */ in ('.implode(',',$forumids).')';
		}
		$readids = $currentuser->forum_ids_read_access();
		$wheres[] = 'forumid /* readids */ in ('.implode(',',$readids).')';
	} else {
		if ($parentid) {
			$wheres[] = 'flockid='.$parentid;
		}
		if (!have_admin('flocktopic')) {
			$wheres[] = 'removed=0';
			if ($flocks = member_of_flocks()) {
				$selects[] = 'if(private=0 or in(flockid,'.implode(',',$flocks).'), 1, 0) AS ok';
				$wheres[] = 'ok=1';
			} else {
				$wheres[] = 'private=0';
			}
		}
	}
	if ($topicid) {
		$wheres[] = 'topicid='.$topicid;
	}
	if ($forum ? !have_forum_admin() : !have_admin('flocktopic')) {
		if ($is_message) {
			$wheres[] = 'accepted=1';
		}
		$wheres[] = 'statusno in (1,2)';
	}
	if (isset($begin_stamp)) {
		$wheres[] = 'cstamp>='.$begin_stamp;
	}
	$result = db_rowuse_array('sphinx','
		SELECT '.implode(', ',$selects)."
		FROM $indices
		WHERE ".implode(' AND ',$wheres)."
		ORDER BY $sphinx_order DESC
		LIMIT ".(($page - 1) * $per_page).','.($per_page + 1),
		DB_PREFER_SERVER | SPHINX_META,
		$sphinx_servers
	);
	$size = $result ? count($result) : 0;
	$sphinx_meta = sphinx_meta();
	$total = 0;
	$total_found = 0;
	if (!$size) {
		$infomsg = __('search:info:nothing_found_LINE');
	} else {
		$ids = [];
		foreach ($result as $info) {
			$ids[] = $info['id'] & 0x00FFFFFFFFFFFFFF;
		}
		if ($sphinx_meta) {
			$total		 = $sphinx_meta['total'] ?? 0;
			$total_found = $sphinx_meta['total_found'] ?? 0;
		}
		$idstr = implode(',', $ids);
		$infomsg = __('search:info:total_results_LINE',[
			'TOTAL' 		=> $total,
			'TOTAL_FOUND'	=> $total_found,
		],DO_NL2BR);
	}
	?><hr class="slim light" id="results"><?
	?><div class="block"><?= $infomsg ?></div><?

	$parents = $_REQUEST[$forum ? 'FORUMID' : 'FLOCKID'] ?? null;
	$all_parents = $parents && (is_array($parents) ? in_array(0,$parents, true) : $parents === 0);

	if ($topicid || $parentid || !$is_message || !$all_parents) {
		?><div class="block"><?= __('search:info:not_enough_search_more_LINE') ?></div><?

		if (!$is_message) {
			?><div class="block">&rarr; <?
			?><a href="<?= escape_utf8(str_replace('TYPE='.$element,'TYPE='.($forum ? 'message' : 'flockmessage'),$_SERVER['REQUEST_URI'])) ?>"><?=
				__C('action:search_in')
			?> <strong><?= element_plural_name($message) ?></strong></a>.<?
			?></div><?
		}
		if ($topicid) {
			?><div class="block">&rarr; <?
			?><a href="<?= escape_utf8(str_replace('TOPICID='.$topicid,'FORUMID[]='.$GLOBALS['actual_parentid'],$_SERVER['REQUEST_URI'])) ?>"><?=
				__C('action:search_in')
			?> <strong><?
			?><?= escape_utf8(get_element_title($parent,$GLOBALS['actual_parentid'])) ?><?
			?></strong><?
			?></a>.<?
			?></div><?
		}
		if (!$all_parents
		||	$GLOBALS['actual_parentid']
		) {
			?><div class="block">&rarr; <?
			?><a href="<?= escape_specials(
				preg_replace('"\b(?:TOPICID='.$topicid.'|(?:FLOCK|FORUM)ID\[]=\d+)\b"','',$_SERVER['REQUEST_URI']
			)) ?>&amp;FORUMID[]=0"><?=
				__C('action:search_in')
			?> <strong><?
			?><?= element_plural_name($forum ? 'all_forum' : 'all_flock') ?><?
			?></strong><?
			?></a>.<?
			?></div><?
		}
	}
	if (!$size) {
		return;
	}
	if (isset($relevance)) {
		$order_str = ' CASE %IDNAME% ';
		foreach ($ids as $cnt => $id) {
			$order_str .= " WHEN $id THEN $cnt ";
		}
		$order_str .= ' END ';
	} elseif ($order === 'date_last_post') {
		$order_str = ' LSTAMP DESC ';
	} else {
		$order_str = ' %IDNAME% DESC ';
	}

	?><hr class="slim light"><?
	$controls = new _pagecontrols();
	$controls->set_per_page($per_page);
	$controls->set_total($total);
	$controls->include_last(false);
	$_SERVER['HILITE'] = $hilites;
	robot_action('noindex');
	switch ($_REQUEST['TYPE'] ?? null) {
	default:
	case $topic:
		require_once "_{$topic}list.inc";
		$topiclist = $forum ? new _topiclist() : new flocktopiclist();
		$topiclist->show_header = true;
		$topiclist->search = true;
		$topiclist->show_author = true;
		$topiclist->show_short = true;
		$topiclist->show_last = true;
		$topiclist->wheres[] = " TOPICID IN ($idstr)";
		$topiclist->orders[] = str_replace('%IDNAME%','TOPICID',$order_str);
		if (!$topiclist->query()) {
			return;
		}
		$controls->display_and_store();
		if ($topiclist->have_rows()) {
			$topiclist->display();
		}
		$controls->display_stored();
		break;

	case $message:
		global $currentuser;
		$wheres = [];

		if (!$forum) {
			$flocks = member_of_flocks();
			if (!have_admin('flocktopic')) {
				$wheres[] = "topic.STATUS != 'hidden' AND NOT flock.REMOVED AND flock.ACCEPTED";
				$wheres[] = '(	NOT PRIVATE'.
								($flocks ? ' OR FLOCKID IN ('.implode(',',$flocks).')' : '').')';
			}
		} else {
			$adminids = $currentuser->forum_ids_adminnable();
			$wheres[] = $readids ? 'FORUMID IN ('.implode(',',$readids).')' : 0;
			$wheres[] = '(	topic.STATUS != "hidden"
							AND (m.ACCEPTED'.
								($adminids ? ' OR FORUMID IN ('.implode(',',$adminids).')' : '').'))';
		}
		$wheres[] = "MESSAGEID IN ($idstr)";
		$key = null;
		$messages = memcached_rowuse_array(
			$forum ? ['topic','message'] : ['flocktopic','flockmessage','flock'],/** @lang MariaDB */ "
			SELECT	SUBJECT, TOPICID, $parentidname, $parentidname AS GRANDPARENTID,
					MESSAGEID, m.BODY, m.USERID, m.CSTAMP, m.MSTAMP, m.MUSERID, m.ACCEPTED, m.FLAGS, topic.STATUS, MSGNO, MSGCNT
			FROM $message AS m
			JOIN $topic AS topic USING (TOPICID)".
			(!$forum ? ' JOIN flock USING (FLOCKID) ' : '').'
			WHERE '.implode(' AND ', $wheres).'
			ORDER BY '.str_replace('%IDNAME%', 'MESSAGEID', $order_str),
			key: $key,
			flags: DB_FORCE_SERVER,
			arg: ['dbsrch', 'dbsrchrep']
		);
		if ($messages === false) {
			return;
		}
		if ($messages) {
			require_once '_commentobject.inc';
			require_once '_karma.inc';

			$element = $forum ? 'message' : 'flockmessage';

			karma($ids, $element);

			$controls->display_and_store();
			foreach ($messages as $message) {
				comment_display($element, $message, $message, CMTFLG_SHOW_PARENT | CMTFLG_SHOW_GRANDPARENT);
			}
			$controls->display_stored();
		}
		break;
	}
	unset($_SERVER['HILITE']);
}

function parse_terms_utf8(string $termstr): array {
	return parse_terms($termstr, true);
}

function parse_terms(string $arg_termstr, bool $utf8 = false): array {
	$utf8_mod = $utf8 ? 'u' : '';
	$term_str = preg_replace('"\s{2,}"'.$utf8_mod, ' ', $arg_termstr);
	$term_str = mytrim($term_str, utf8: $utf8);
	$phrases = [];
	$phrasecnt = 0;
	$hilites = [];
	# find phrases
	$term_str = preg_replace_callback('!"(.*)?"!'.$utf8_mod, static function(array $match) use (&$phrasecnt, &$phrases) {
		$term = " \x1F$phrasecnt\x1F ";
		$phrases[] = $match[1];
		++$phrasecnt;
		return $term;
	},$term_str);
	$term_str = ($utf8 ? 'utf8_mytrim' : 'trim')(preg_replace('"\h+"'.$utf8_mod, ' ', $term_str));
	$have_spaces = false !== ($utf8 ? 'mb_strpos' : 'strpos')($term_str, ' ');
	$terms = explode(' ', $term_str);
	$words = [];
	foreach ($terms as $i => &$term) {
		if (!isset($term[0])
		||	in_array($term, ['-', '+', '*', '\\', '~', '|', '&', '^', '/'], true)
		) {
			unset($terms[$i]);
			continue;
		}
		if ($term[0] === "\x1F") {
			$i = ($utf8 ? 'mb_substr' : 'substr')($term, 1, -1);
			$hilite = $phrases[$i];

			# Backslash before quotes does not work.
			# Backslash in other places seem to work.
			# For now, strip the backlashes in front of quotes.

			$hilite = myrtrim($hilite, '\\', $utf8);

			$term = '"'.sphinx_escape($hilite).'"';

			$words[] = $term;
		} else {
			$hilite = preg_replace('"\W+"'.$utf8_mod, '', $term);
			# strip exclamation mark, results in 0 results
			$term = myrtrim($term, '!', $utf8);
			$term = $term ? sphinx_escape($term) : '';

			if (strlen($term) >= 2) {
				# require at least 2 chars for minimum infix length
				$words[] = $term;
				# prefer exact match using ^$: 10 times the weight
				# FIXME: ^$ seems to not work
				$term = '^'.$term.'$^100 | ^'.$term.'*^10 | '.$term.'*';
			}
		}
		if ($hilite) {
			$hilites[] = $hilite;
		}
	}
	unset($term);
	$term_str = implode(' ', $terms);
	if ($have_spaces
	&&	!$phrasecnt
	) {
		$parts = [];

		if ($words) {
			$base_term_str = implode(' ',$words);
			# Backslash before quotes does not work.
			# Backslash in other places seem to work.
			# For now, strip the backlashes in front of quotes.
			$base_term_str = myrtrim($base_term_str, '\\', $utf8);
			$parts[] = '"'.$base_term_str.'"';
		}
		$parts[] = $term_str;
		if ($words) {
			$parts[] = '*'.implode('', $words).'*';
			$parts[] = implode('', $words);
		}
		$term_str = implode(' | ',$parts);
	}
	return [str_replace(chr(0), '', $term_str),
			$hilites];
}

function sphinx_escape(string $input): string {
	# Need to double escape when sending to sphinx searchd
	# Seems do be due to the fact all characters are indexed (seee https://sphinxsearch.com/bugs/view.php?id=708)

	return str_replace(
		['\\',   '(',	 ')',	 '|',	'-',	  '!',	 '@',	 '~',	 '"',	 "'",	'&',	 '/',	 '^',	 '$',	 '=',	 '<'],
		['\\\\', '\\\\(', '\\\\)', '\\\\|', '\\\\-', '\\\\!', '\\\\@', '\\\\~', '\\\\"', '\\\'', '\\\\&', '\\\\/', '\\\\^', '\\\\$', '\\\\=', '\\\\<'],
		$input
	);
}
