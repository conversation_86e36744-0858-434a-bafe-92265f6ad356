<?php

declare(strict_types=1);

#function presale_sites(bool $include_inactives = false): array {
function presale_sites(): array {
	static $__sites = [
	#	'easyticket'			=> ['EASYTICKET',			'easy'],
		'eventtickets'			=> ['EVENTTICKETS',			'eventtickets'],
		'eventim'				=> ['EVENTIM',				'eventim'],
		'fnacspectacles'		=> ['FNAC',					'fnac'],
		'fnac'					=> ['FNAC',					'fnac'],
		'link2ticket'			=> ['LINK2TICKET',			'link2ticket'],
		'paylogic'				=> ['PAYLOGIC',				'paylogic'],
	#	'ravepas'				=> ['RAVEPAS',				'ravepas'],
	#	'knaek'					=> ['KNAEK',				'knaek'],
	#	'seedance'				=> ['SEEDANCE',				'seedance'],
	#	'seetickets'			=> ['SEEDANCE',				'seedance'],
	#	'ticketscript'			=> ['TICKETSCRIPT',			'ticketscript'],
		'ticketmaster'			=> ['TICKETMASTER',			'ticketmaster'],
	#	'ticketservice'			=> ['TICKETSERVICE',		'service'],
	#	'timoco'				=> ['TIMOCOID',				'timoco'],
		'yourticketprovider'	=> ['YOURTICKETPROVIDER',	'yourticketprovider'],
		'nowonlinetickets'		=> ['NOWONLINETICKETS',		'nowonlinetickets'],
		'ikbenaanwezig'			=> ['IKBENAANWEZIG',		'ikbenaanwezig'],
	#	'ibizadiscoticket'		=> ['IBIZADISCOTICKET',		'ibizadiscoticket'],
	];
/*	static $__inactive_sites = [
		'easyticket'			=> ['EASYTICKET',			'easy'],
		'ravepas'				=> ['RAVEPAS',				'ravepas'],
		'knaek'					=> ['KNAEK',				'knaek'],
		'seedance'				=> ['SEEDANCE',				'seedance'],
		'seetickets'			=> ['SEEDANCE',				'seedance'],
		'ticketscript'			=> ['TICKETSCRIPT',			'ticketscript'],
		'ticketservice'			=> ['TICKETSERVICE',		'service'],
		'timoco'				=> ['TIMOCOID',				'timoco'],
		'ibizadiscoticket'		=> ['IBIZADISCOTICKET',		'ibizadiscoticket'],
	];
	return $include_inactives ? [...$__sites, ...$__inactive_sites] : $__sites;*/
	return $__sites;
}

function get_ticket_info(int $partyid): array {
	# THIS ALL SEEMS OLD AND OUTDATED

	static $__ticket_info = null;
	if ($__ticket_info) {
		return $__ticket_info;
	}
	if (!($party = memcached_party_and_stamp($partyid))) {
		return $__ticket_info = [];
	}

	$__ticket_info = [];

	foreach ([
		[		   0, 1694743842,	'TIMOCOID',			'timoco',			'Timoco',				true,	null],
		[		   0, 1693552232,	'RAVEPAS',			'ravepas',			'Ravepas',				true,	CURRENTSTAMP > 1448924400 ? null : [38, 25, null, 'ravepas', true]],
		[		   0, 1262300400,	'SEEDANCE',			'seedance',			'See Dance',			true,	[25, 25, null, 'seetickets']],
		[		   0, 1458427357,	'KNAEK',			'knaek',			'Knaek',				true,	CURRENTSTAMP > 1448924400 ? null : [25, 25, null, 'knaek', true]],
								   # SEEDANCE was ticketonline before 2010-01-01
		[ 1262300400, 1286426321,
									'SEEDANCE',			'seedance',			'Ticketonline',			true,	[25, 25, null, 'ticketonline']],
		[		   0, 1124010000,	'TICKETBOX',		'ticketbox',		'TicketBox',			false,	[25, 25, null, 'ticketbox']],
		[		   0, 1286426321,	'BEEP', 			'beep',				'Beep',					false,	null],
		[		   0, 1674956602,	'EASYTICKET',		'easy',				'Easyticket',			true,	null],
	#	[		   0, 1695671716,	'EVENTTICKETS',		'eventtickets',		'Event-Tickets',		true,	null],
		[		   0, 1692387798,	'TICKETSERVICE',	'service',			'Ticket Service',		true,	null],
		[		   0, 1435136971,	'IBIZADISCOTICKET',	'ibizadiscoticket',	'Ibiza Disco Ticket',	true,   null],
		[		   0, 1332046800,	'TICKETSCRIPT',		'ticketscript',		'Ticketscript',			true,	CURRENTSTAMP > 1448924400 ? null : [25, 25, null, 'ticketscript']],
		[		   0, 1540087200,	'TICKETSCRIPTV2',	'ticketscript',		'Ticketscript',			true,	CURRENTSTAMP > 1448924400 ? null : [25, 25, null, 'ticketscript']],
	] as $info) {
		[$first_event, $last_event, $NAME] = $info;

		if ($party['STAMP'] > $last_event
		||	$first_event
		&&	$party['STAMP'] < $first_event
		) {
			continue;
		}
		$__ticket_info[$NAME] = array_slice($info, 3);
	}

	return $__ticket_info += [
		# NAME						LOWER NAME				Name					LINK	IMAGE LINK
		'APPIC'					=> ['appic',				'Appic',				true,	null],
		'EVENTIM'				=> ['eventim',				'Eventim',				true,	null],
		'EVENTTICKETS'			=> ['eventtickets',			'Event-Tickets',		true,	null],
		'FNAC'					=> ['fnac',					'FNAC',					true,	null],
		'LINK2TICKET'			=> ['link2ticket',			'Link2Ticket',			true,	null],
		'PAYLOGICV2'			=> ['paylogic',				'Paylogic',				true,	null],
		'PAYLOGIC'				=> ['paylogic',				'Paylogic',				true,	null],
		'TICKETMASTER'			=> ['ticketmaster',			'Ticketmaster',			true,	null],
		'YOURTICKETPROVIDER'	=> ['yourticketprovider',	'YourTicketProvider',	true,	null],
		'NOWONLINETICKETS'		=> ['nowonlinetickets',		'NowOnlineTickets',		true,	null],
		'IKBENAANWEZIG'			=> ['ikbenaanwezig',		'IkBenAanwezig',		true,	null],
	];
}

function is_supported_presale_site(string $site, &$get_type = null): string|false {
	/** @noinspection SuspiciousLoopInspection */
	foreach (presale_sites() as $site_part => [$name, $get_type]) {
		if (preg_match('"(?:https?://)?[^/]*?'.preg_quote($site_part,'"').'\b"i', $site)) {
			return $name;
		}
	}
	return false;
}

function is_bad_presale_site(?string $site = null): array|bool {
	static $__bad = [
		'4alltickets.nl',
		'adultdvdclub.nl',
		'beatfreax.com',
		'belnu.nl',
		'besteltickets.com',
		'bobs.nl',
		'budgetticket.nl',
		'clubjudge.com',
		'concertreis.nl',
		'deticketservice.nl',
		'djguide.nl',
		'ecopharma.com',
		'entreeticket.',
		'ewordmarketing.com',
		'feestagenda.com',
		'ferratum.nl',
		'fresh.fm',
		'gabberwear.nl',
		'getmein.com',
		'getmein.nl',
		'globalticketservice.com',
		'go-for-events.com',
		'gsmweb.nl',
		'hardcore-nation.nl',
		'hollandticketservice.com',
		'hooded.nl',
		'hooligan-clothing.nl',
		'hyves.nl',
		'hyves.net',
		'instantlening.com',
		'kaartverkoop.nl',
		'keesisterug.nl',
		'koppelbox.nl',
		'muziek-muziek.com',
		'onlineticketshop.nl',
#		'partynight.nl',
		'partyshirt.com',
		'pepetickets.nl',
		'rang1tickets.nl',
		'reizenpaleis.nl',
		'rige.net',
		'rockitopenair.nl',
		'seatwave.com',
		'seatwave.nl',
		'seeyoudance.nl',
		'serumevents.nl',
		'slimmingdrops.nl',
		'sneakershop.nl',
		'spoedgeld.com',
		'spreadshirt.net',
		'thema-tabel.com',
		'ticket-unlimited.nl',
		'ticket2.nl',
		'ticketbande.com',
		'ticketforsale.nl',
		'ticketloket.com',
		'ticketonline.nl',
		'tickets2buy.nl',
		'tickets4events.nl',
		'tickets4u.eu',
		'tickets4u.nl',
		'ticketsnederland.nl',
		'ticketunlimited.nl',
		'top-video-rock.com',
		'topeventtickets.com',
		'travel2dance.nl',
		'trimgel.be',
		'trimgel.nl',
		'typhone.nl',
		'vakantieveilingen.nl',
		'viagogo.com',
		'viagogo.nl',
		'worldticketscentre.eu',
		'worldwideticketshop.eu',

		// misc:

		'flirtzoeken.nl',
		'huurwoningbemiddeling.nl',
	];
	if (!$site) {
		return $__bad;
	}
	static $__regex =
		"'\b".
		str_replace(
			chr(0x1F),
			'|',
			preg_quote(
				implode(chr(0x1F), $__bad),
				"'"
			)
		)."\b'";
	return (bool)preg_match($__regex, $site);
}

/** @return array{
 *		main_info:	string|null,
 *		main_link:	string|null,
 *		icons:		array<string, string>,
 *		mains:		array<string, string>,
 *		trailer:	array<string>,
 *		no_sale:	bool,
 *		websites:	array<string>
 *	}
 */
function prepare_presale_info(
	array  &$presale_info,
	array	$party,
	bool	$force_sale		= false,
	bool	$freshen_cache	= false
): array {
	static $__presale_info = [];
	require_once '_hosts.inc';
	$partyid = $presale_info ? $presale_info['PARTYID'] : $party['PARTYID'];
	if (!$freshen_cache
	&&	isset($__presale_info[$partyid])
	) {
		return $__presale_info[$partyid];
	}
	require_once '_require.inc';
	$party_admin = have_admin('party');
	$always_show_to_admins = have_admin(['appic', 'party', 'helpdesk']);
	# don't stop sale if party is SOLD_OUT, as we might have marked it erroneously
	$no_sale =
		!$always_show_to_admins
	&&	!$force_sale
	&&	(	CURRENTSTAMP > $party['STAMP'] + ($party['DURATION_SECS'] ?: 6 * ONE_HOUR)
		||	$party['CANCELLED']
		||	$party['MOVEDID']
		);
	[$websites, $site_i, $have_i] = parse_presale_websites($presale_info);
	$mains	 = [];
	$trailer = [];
	$icons	 = [];
	if (!CLI && $_SERVER['SCRIPT_FILENAME'] === '/home/<USER>/public_html/index.php') {
		require_once '_presale_affiliates.inc';
		add_affiliate_button($partyid, $party, $mains, $icons, true);
	}

	foreach (get_ticket_info($partyid) as $type => [$ticket_type, $name,, $image_info]) {
		if (!isset($presale_info[$type])) {
			continue;
		}
		$class = 'middle';
		if ($no_sale) {
			$open_link  = null;
			$close_link = null;
			$alt = __('party:alt:nosale_tickets',['ORG'=>$name]);
		} elseif (
			empty($presale_info[$type])
		&&	(	$type === 'TICKETSCRIPT'
			||	$type === 'TICKETSCRIPTV2'
			)
		&&	!isset($site_i[$type])
		) {
			$open_link  = null;
			$close_link = null;
			$alt = __('party:alt:no_direct_tickets',['ORG'=>$name]);
			$class .= ' light';
		} else {
			$myi = $site_i[$type] ?? false;
			if ($myi !== false) {
				$ticket_type = 'site';
			}
			$link = "/order_ticket/$ticket_type/$partyid";
			$args = [];
			if ($myi !== false) {
				$args[] = 'I='.$myi;
			}
			if ($args) {
				$link .= '?'.implode(';',$args);
			}
			if ($link) {
				$open_link = '<a rel="nofollow" href="'.$link.'" target="_blank"';
				if (!$party['ACCEPTED']
				&&	$party_admin
				&&	$myi !== false
				) {
					$open_link .= ' class="checkp"';
				}
				$open_link .= ' onclick="return Pf.trackLink(this.href, \'presale\');">';
				$close_link = '</a>';
			} else {
				$open_link  = null;
				$close_link = null;
			}
			$alt = __('party:alt:buy_tickets',['ORG'=>$name]);
		}
		$mains[$type] = $name ? ($open_link ? str_replace('rel="nofollow" ','rel="nofollow" class="extra" ',$open_link).$name.$close_link : $name) : null;
		if ($image_info) {
			[$width, $height, $theme, $file] = $image_info;
			$x2 = !empty($image_info[4]) ? is_high_res() : '';
			ob_start();
			echo $open_link;
			?><img<?
			?> alt="<?= $alt ?>"<?
			?> title="<?= $alt ?>"<?
			?> class="<?= $class ?>"<?
			?> width="<?= $width ?>"<?
			?> height="<?= $height ?>"<?
			?> src="<?= STATIC_HOST ?>/images/<?= $theme, $file, $x2 ?>.png" /><?
			echo $close_link;
			$icons[$type] = ob_get_clean();
		}

		if ($type === 'TICKETSCRIPTV2'
		||	$type === 'PAYLOGICV2'
		) {
			$presale_info['TICKETSCRIPT'] = null;
		}
	}
	foreach ([
		'WEBSITE',
		'LOCATION',
		'EMAIL',
		'ADDR'
	] as $type) {
		if (empty($presale_info[$type])) {
			continue;
		}
		$args = ['NOSALE' => $no_sale ? 1 : 0];
		switch ($type) {
		case 'LOCATION':
			if (!$party['LOCATIONID']) {
				break;
			}
			$args['LOCATIONID'] = $party['LOCATIONID'];
			$trailer[] = __('party:presale:at_location',DO_UBB,$args);
			break;

		case 'EMAIL':
			$args['EMAIL'] = $presale_info['EMAIL'];
			$trailer[] =
				str_replace(
					['%OPEN%', '%CLOSE%'],
					[
						!$party_admin && $no_sale ? '' : '<a href="mailto:'.escape_utf8($presale_info['EMAIL']).'">',
					 	!$party_admin && $no_sale ? '' : '</a>'
					],
					__('party:presale:at_email', KEEP_EMPTY_KEYWORDS, $args)
				);
			break;

		case 'WEBSITE':
			$i = 0;
			foreach ($websites as $site) {
				if ($site
				&&	!isset($have_i[$i])
				) {
					if (!preg_match('"^(?:https?://)?(?:(?:w+|iframeshop|iframe|shop)\.)?(?<domain>[^/]+)/?"i',$site,$match)) {
						mail_log("site not understood for party $partyid: $site");
					} else {
						$link = "/order_ticket/site/$partyid?I=$i";
						$mains[] =
							$no_sale
						?	(	$party_admin
							?	'<a rel="nofollow" target="_blank" href="'.$link.'">'.escape_utf8($match['domain']).'</a>'
							:	escape_utf8($match['domain'])
							)
						:	'<a rel="nofollow" target="_blank" href="'.$link.'" onclick="return Pf.trackLink(this.href, \'presale\');">'.escape_utf8($match['domain']).'</a>';
					}
				}
				++$i;
			}
			break;

		case 'ADDR':
			$trailer[] =
				str_replace(
					['%OPEN%', '%CLOSE%'],
					['<span class="extra unhideanchor" onclick="swapdisplay(\'presale-addresses\')">', '</span>'],
					__('party:presale:at_a_few_presale_addresses', KEEP_EMPTY_KEYWORDS)
				);
			break;
		}
	}
	if (!CLI && $_SERVER['SCRIPT_FILENAME'] === '/home/<USER>/public_html/index.php') {
		add_affiliate_button($partyid, $party, $mains, $icons, false);
	}
	if (!$mains) {
		$main_link = null;
		$main_info = null;
	} else {
		[$main_info, $main_link] = keyval($mains);
	}
	return $__presale_info[$partyid] = [
		'main_info'	=> $main_info,
		'main_link'	=> $main_link,
		'icons'		=> $icons,
		'mains'		=> $mains,
		'trailer'	=> $trailer,
		'no_sale'	=> $no_sale,
		'websites'	=> $websites,
	];
}

function get_ubb_presale_link(
	string	$element,
	int		$id,
	array	$attribs,
	int		$flags = 0
): string|false {
	$utf8 = (bool)($flags & UBB_UTF8);
	[$url] = get_generic_presale_link($id);
	if (!$url || !($url = preg_replace('"^(https?://(?:vip\.)?partyflock\.[a-z]+)\b"', '', $url))) {
		return false;
	}
	$alt = __('party:alt:buy_tickets', DO_UBBFLAT | DO_CONDITIONAL);
	$title = empty($attribs['title']) ? $alt : escape_specials($attribs['title'], $utf8);

	if (!($flags & UBB_HTML)) {
		return $title;
	}
	ob_start();
	?><a<?
	?> class="itemlink"<?
	?> rel="nofollow"<?
	?> href="<?= $url ?>"<?
	?> target="_blank"<?
	?>><?= $title ?></a><?
	return ob_get_clean();
}

function get_presale_info(string $element, int $id, bool $freshen_cache = false): array|false {
	static $__presaleinfo = [];
	if (!$freshen_cache
	&&	isset($__presaleinfo[$element][$id])
	) {
		return $__presaleinfo[$element][$id];
	}
	if (!($info = db_single_assoc('presaleinfo', "
		SELECT *
		FROM presaleinfo
		WHERE PARTYID = $id"))
	) {
		if ($info === null) {
			$info = [];
		}
		return $__presaleinfo[$element][$id] = $info;
	}
	if (!empty($info['RAVEPAS'])) {
		$info['KNAEK'] = $info['RAVEPAS'];
	}
	# if APPIC is inactive or sold out, remove it
	if (!empty($info['APPIC'])
	&&	(  !$info['APPIC_ACTIVE']
		||	$info['APPIC_SOLD_OUT']
		)
	#&&	($party = memcached_party_and_stamp($id))
	#&&	$party['STAMP'] > CURRENTSTAMP
	) {
		$info['APPIC_GID'] = $info['APPIC'];
		$info['APPIC'] = null;
		$info['APPIC_SOLD_OUT'] = null;
		$info['APPIC_ACTIVE'] = false;
	}
	return $__presaleinfo[$element][$id] = $info;
}

function parse_presale_websites(array &$presaleinfo): array {
	if (empty($presaleinfo['WEBSITE'])) {
		return [null, null, null];
	}
	$websites = explode("\n", $presaleinfo['WEBSITE']);
	$site_i = [];
	$have_i = [];
	$i = 0;
	foreach ($websites as &$site) {
		if (($site = mytrim($site))
		&&	($name = is_supported_presale_site($site))
		) {
			if ($name === 'PAYLOGIC'
			&& 	$presaleinfo['PAYLOGICV2']
			) {
				$name = 'PAYLOGICV2';
			}
			$presaleinfo[$name] = true;
			$site_i[$name] = $i;
			$have_i[$i] = $i;
			$site = '';
		}
		++$i;
	}
	return [$websites, $site_i, $have_i];
}

function show_no_presale_indication(array $party, bool $text = false): void {
	if (!$party['DOORONLY']
	&&	!$party['FREE_ENTRANCE']
	&&	!$party['SOLD_OUT']
	&&	!$party['CANCELLED']
	&&	!$party['MOVEDID']
	&&	($party['STAMP'] + $party['DURATION_SECS']) > CURRENTSTAMP
	&&	(	empty($party['PRESALE_STAMP'])
		||	$party['PRESALE_STAMP'] < CURRENTSTAMP
		)
	&&	get_presale_link($party['PARTYID'], $party) === [null, null]
	&&	!memcached_single('presaleinfo','
			SELECT IF(ADDR != "" OR LOCATION, 1, 0)
			FROM presaleinfo
			WHERE PARTYID = '.$party['PARTYID'])
	) {
		require_once '_layout.inc';

		?> <div class="relative ib lower<?
		if ($party['STAMP'] > CURRENTSTAMP + 3 * ONE_MONTH) {
			# further than three months away?
			?> light<?
		}
		if ($text) {
			?> error block<?
		}
		?>"><?
		?><div class="abs oglo error presence" style="padding: 1em; font-size: 50%;"><?= CROSS_MARK_ENTITY ?></div><?
		echo get_ticket_icon();

		if ($text) {
			?> <?
			echo __C('party:warning:presale_missing');
		}
		?></div><?
	}
}

function presale_with_parameters(string $uri, array|string|null $parameters = null): string {
	return
		$uri.(
			$parameters
		?	(	!str_contains($uri, '?')
			?	'?'
			:	'&'
			).(	is_string($parameters)
			?	$parameters
			:	implode('&', $parameters)
			)
		:	''
	);
}

function get_generic_presale_link(int $id, ?array $party = null, array|string|null $parameters = null): array {
	[$href, $onclick] = get_presale_link($id, $party);
	if ($href) {
		return [presale_with_parameters(FULL_HOST.'/order_ticket/'.$id, $parameters), $onclick];
	}
	return [null, null];
}

function get_presale_link(int $partyid, ?array $party = null, array|string|null $parameters = null): array {
	static $__presale_link = [];
	if (isset($__presale_link[$partyid])) {
		return $__presale_link[$partyid];
	}
	if (!($presaleinfo = get_presale_info('party', $partyid))) {
		return $__presale_link[$partyid] = [null, null];
	}
	if (!($party ??= memcached_party_and_stamp($partyid))) {
		mail_log('get_presale_link: no party '.$partyid);
		return $__presale_link[$partyid] = [null, null];
	}
	if (!($locals = prepare_presale_info($presaleinfo, $party, true))) {
		mail_log('get_presale_link: failed to prepare presale info for party '.$partyid);
		return $__presale_link[$partyid] = [null, null];
	}
	foreach ($locals['mains'] as $main) {
		if (!str_contains($main, '<a ')) {
			continue;
		}
		if (preg_match('!href="(?<href>.*?)"(?:.*?onclick="(?<onclick>.*?)")?!', $main, $match)) {
			return $__presale_link[$partyid] = [
				presale_with_parameters(FULL_HOST.$match['href'], $parameters),
				$match['onclick'] ?? null
			];
		}
	}
	return $__presale_link[$partyid] = [null, null];
}
