<?php

function _forumlist_display_accessible($selected = 0,$partyid = null) {
	global $currentuser;

	$currentuser->load_forum_rights();

	$allforums = $currentuser->forums;

	string_asort($allforums,'NAME');

	if ($partyid) {
		$party_forums = [48=>true,49=>true,50=>true,51=>true,52=>true,7=>true,46=>true,57=>true,60=>true];
		$groups[element_plural_name('applicable_forum')] = array_intersect_key($allforums,$party_forums);
		$groups[null] = array_diff_key($allforums,$party_forums);
	} else {
		$groups[null] = $allforums;
	}
	static $__swap = [
		4	=> 60,
		60	=> 4
	];

	foreach ($groups as $label => $forums) {
		if ($label) {
			?><optgroup label="<?= $label ?>"><?
		}
		foreach ($forums as $forumid => $forum) {
			if (($swapid = getifset($__swap,$forumid))
			&&	isset($forums[$swapid])
			) {
				$forumid = $swapid;
				$forum = $forums[$swapid];
			}
			?><option<?
			if ($selected
			&&	(	is_scalar($selected)
				?	$selected == $forumid
				:	in_array($forumid,$selected)
				)
			) {
				?> selected<?
			}
			?> value="<?= $forumid ?>"><?= escape_utf8($forum['NAME']);
			?></option><?
		}
		if ($label) {
			?></optgroup><?
			?><option disabled="disabled"></option><?
		}
	}
}
