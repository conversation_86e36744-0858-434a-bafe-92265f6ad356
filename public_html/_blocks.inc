<?php

define('BLOCK_ALBUM',		1);	# 1
define('BLOCK_WEBLOG',		2);	# 2
define('BLOCK_GUESTBOOK',	4);	# 3
define('BLOCK_PRIVATE_MESSAGES',8);	# 4
define('BLOCK_VALENTINE_HEARTS',16);	# 5
define('BLOCK_BUDDY_REQUESTS',	32);	# 6
define('BLOCK_LAST_BIT',	6);

function blocked($which,$userid,$blockid = CURRENTUSERID) {
	static $__blocks;
	if (isset($__blocks[$blockid])) {
		$block = $__blocks[$blockid];
	} elseif (false !== ($block = memcached_get($ikey = 'blocks:'.$blockid))) {
		$__blocks[$blockid] = $block;
	} else {
		$blocklist = db_simple_hash('blocklist','
			SELECT USERID,FLAGS
			FROM blocklist
			WHERE BLOCKID='.$blockid
		);
		if ($blocklist === false) {
			return $__blocks[$blockid] = false;
		}
		if (!$blocklist) {
			return $__blocks[$blockid] = null;
		}
		$block = null;
		foreach ($blocklist as $tmpuserid => $flags) {
			for ($bit = 0; $bit <= BLOCK_LAST_BIT; ++$bit) {
				$mask = 1<<$bit;
				if ($flags & $mask) {
					$block[$mask][$tmpuserid] = $tmpuserid;
				}
			}
		}
		#print_rr($block,'setting '.$ikey);
		memcached_set($ikey,$__blocks[$blockid] = $block, TEN_MINUTES);
	}
	return $which ? (isset($block[$which][$userid]) ? $block[$which][$userid] : null) : $block;
}
function flush_blocks($blockid) {
	#print_rr('flushing blocks:'.$blockid);
	memcached_delete('blocks:'.$blockid);
}
function get_block_names($flags) {
	$ilist = array();
	foreach (array(
		BLOCK_ALBUM		=> element_name('album'),
		BLOCK_GUESTBOOK		=> element_name('guestbook'),
		BLOCK_WEBLOG		=> element_name('weblog'),
		BLOCK_PRIVATE_MESSAGES	=> element_plural_name('directmessage'),
		BLOCK_VALENTINE_HEARTS	=> element_plural_name('valentine_heart'),
	) as $flag => $what) {
		if ($flags & $flag) {
			$ilist[] = $what;
		}
	}
	return $ilist;
}
