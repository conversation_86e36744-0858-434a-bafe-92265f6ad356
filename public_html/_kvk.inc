<?php

declare(strict_types=1);

function show_kvk_row(deflist $list, array $item): void {
	if (!empty($item['COUNTRYID'])
	&&	$item['COUNTRYID'] !== 1
	) {
		# We don't know (yet) how to handle non-Dutch chamber of commerce numbers
		$force_no_link = true;
	}
	ob_start();
	if (!isset($force_no_link)
	&&	preg_match("'^(?<number>\d+)(?: 0+)?$'u", $item['KVKNUM'], $match)
	) {
		?><a target="_blank" href="https://www.kvk.nl/bestellen/#/<? printf('%08d', $match['number']) ?>"><?= $item['KVKNUM'] ?></a><?
	} else {
		echo escape_utf8($item['KVKNUM']);
	}
	$list->add_row(Eelement_name('coc(chamber_of_commerce)'), ob_get_clean());
}

function show_kvk_form_row(?array $item = null, ?string $name = null, ?array $spec = null): void {
	layout_restart_row();
		?><a href="https://www.kvk.nl/" title="<?= Eelement_name('chamber_of_commerce') ?>"><?= Eelement_name('coc(chamber_of_commerce)') ?></a><?
		layout_field_value();
		show_input([
			'class'		=> 'right id',
			'type'		=> 'number',
			'name'		=> $name = $name ?: 'KVKNUM',
			'value'		=> $item[$name] ?? null,
			'spec'		=> $spec['KVKNUM'] ?? $spec
		]);
}
