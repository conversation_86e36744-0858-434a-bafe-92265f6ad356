<?php

function update_postedin(
	string	$element,
	int		$id,
	int		$messageid,
	int		$msgno	   = 1,
	?int	$userid	   = null,
): bool {
	$userid ??= CURRENTUSERID;

	return	db_replace('postedinv3', "
		REPLACE INTO postedinv3 SET
			ELEMENT		= '$element',
			ID			= $id,
			USERID		= $userid,
			MESSAGEID	= $messageid,
			MSGNO		= $msgno,
			LSTAMP		= ".CURRENTSTAMP
	);
}

function recreate_postedin(string $element, int $id): bool {
	if (!db_delete('postedinv3', "
		DELETE FROM postedinv3
		WHERE ELEMENT = '$element'
		  AND ID = $id")
	) {
		return false;
	}

	[$commenttable, $idname, $parentidname] = match($element) {
		'topic'			=> ['message',	   		 'MESSAGEID', 'TOPICID'],
		'flocktopic'	=> ['flockmessage',		 'MESSAGEID', 'TOPICID'],
		default			=> [$element.'_comment', 'COMMENTID', 'ID'],
	};

	if (!($postedins = db_rowuse_array($commenttable, /** @lang MariaDB */"
		SELECT	USERID,
				$parentidname AS ID,
				MAX($idname) AS MESSAGEID,
				MAX(MSGNO) AS MSGNO,
				MAX(CSTAMP) AS CSTAMP
		FROM `$commenttable`
		WHERE $parentidname = $id
		GROUP BY USERID",
		DB_USE_MASTER))
	) {
		return $postedins !== false;
	}

	$setlist = [];
	foreach ($postedins as $postedin) {
		extract($postedin);
		$setlist[] = "('$element', $ID, $USERID, $MESSAGEID, $MSGNO, $CSTAMP)";
	}

	return	db_insert('postedinv3', '
		INSERT INTO postedinv3 (ELEMENT, ID, USERID, MESSAGEID, MSGNO, LSTAMP)
		VALUE '.implode(', ', $setlist).'
		ON DUPLICATE KEY UPDATE
			LSTAMP		= IF(VALUES(LSTAMP) > LSTAMP, VALUES(LSTAMP),	LSTAMP),
			MESSAGEID	= IF(VALUES(LSTAMP) > LSTAMP, VALUES(MESSAGEID), MESSAGEID),
			MSGNO		= IF(VALUES(LSTAMP) > LSTAMP, VALUES(MSGNO),	 MSGNO)'
	);
}

function get_self_posts(string $element, string $idstr, int $userid = 0): array|bool {
	if (!$userid) {
		$userid = CURRENTUSERID;
	}
	return memcached_simple_hash('postedinv3', "
		SELECT ID, MSGNO, MESSAGEID
		FROM postedinv3
		WHERE ELEMENT = '$element'
		  AND USERID = $userid
		  AND ID IN ($idstr)"
	);
}
