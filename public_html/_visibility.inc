<?php

declare(strict_types=1);

# FIXME: Terminology is a bit off. Since 0 is maximum visibility (EVERYBODY),
#		 and 4 is minimum visibility (NOBODY), the terms "min" and "max" are
#		 swapped. We should fix this sometime,

require_once '_buddies.inc';
require_once '_layout.inc';

const EVERYBODY		= 0;
const MEMBERS		= 1;
const BUDDIES		= 2;
const SELF			= 3;
const LAST			= 4;
const NOBODY		= 4;	# workaround

function _visibility_display_form_part(
	string|int|array|null	$item			= null,
	?string					$part			= null,
	?string					$name_extension	= null,
	?string					$id				= null,
	bool					$hidden			= false,
	int						$min_visibility = EVERYBODY,
	int						$max_visibility = SELF,
	?array					$attributes		= null,
): void {
	if ($item && is_array($item)) {
		$selected = $item['VISIBILITY_'.$part];
	} elseif (is_number($item)) {
		$selected = (int)$item;
	} else {
		$selected = EVERYBODY;
	}
	?><select<?
	if ($hidden) {
		?> class="hidden"<?
	}
	if ($id) {
		?> id="<?= $id ?>"<?
	}
	if ($attributes) {
		foreach ($attributes as $key => $value) {
			?> <?= $key ?>="<?= $value ?>"<?
		}
	}
	?> name="VISIBILITY_<?= $part, $name_extension ?>"><?
	show_visibility_options($selected, $min_visibility, $max_visibility);
	?></select><?
}

function show_visibility_options(
	?int $selected = null,
	int	 $min_visibility = EVERYBODY,
	int	 $max_visibility = SELF,
): void {
	/** @var int $i */
	for ($i = $min_visibility; $i <= $max_visibility; ++$i) {
		?><option<?
		if ($selected === $i) {
			?> selected<?
		}
		?> value="<?= $i ?>"><?= _visibility_name($i) ?></option><?
	}
}

function require_visibility(
	array|int	$arg,
	?string		$part = null,
	int			$max_visibility = SELF,
	bool		$utf8 = false
): bool {
	if (!empty($part)
	&&	is_array($arg)
	) {
		$field = 'VISIBILITY_'.$part;
		if (false === require_something($arg, $field, $utf8)
		||	false === require_number($arg, $field, utf8: $utf8)
		) {
			return false;
		}
		if ($arg[$field] > $max_visibility) {
			register_error('require:visibility:error_LINE', [
				'NAME'	=> $field,
				'VALUE'	=> $arg[$field]
			]);
			return false;
		}
	}
	/** @noinspection NotOptimalIfConditionsInspection */
	elseif (
		!is_number($arg)
	||	$arg > $max_visibility
	) {
		register_error('require:visibility:value_LINE', ['VALUE' => $arg]);
		return false;
	}
	return true;
}

function _visibility_name(int $id): string {
	static $__name;
	return $__name[$id] ??= __($key = 'visibility:'.$id) ?: $key;
}

function visible_to(int $id): string {
	static $__name;
	return $__name[$id] ??= __($key = 'visible_to:'.$id) ?: $key;
}

/*function readable_by(int $id): string {
	static $__name;
	return $__name[$id] ??= __($key = 'readable_by:'.$id) ?: $key;
}*/

function writable_by(int $id, bool $caps = false): string {
	static $__name;
	return $__name[$id] ??= __($key = 'writable_by:'.$id, $caps ? DO_CAPFIRST : 0) ?: $key;
}

function _where_visible(
	string		$element_name,
	string		$visibility_name,
	# owner can be a user id or a field name
	int|string	$owner = 0,
	?string		$and = null,
	bool		$allow_helpdesk = true
): string {
	$and = $and ?? ' AND ';
	$right_name = $element_name;
	if (!have_user()) {
		return $and.$element_name.'.VISIBILITY_'.$visibility_name.' = 0 ';
	}
	if (have_admin($allow_helpdesk ? ['helpdesk', $right_name] : $right_name)) {
		return $and.' /* have_admin(helpdesk || '.$right_name.')  */ 1';
	}
	if ($element_name !== 'artist') {
		if ($owner && is_number($owner)) {
			if ($owner === CURRENTUSERID) {
				return $and.' /* owner === CURRENTUSERID */ 1';
			}
			$buddies = _buddies_full_cached_list($owner);
			$am_buddy = isset($buddies[CURRENTUSERID]) ? 1 : 0;
			$when_extra = null;
		} else {
			$buddies = _buddies_full_cached_list(CURRENTUSERID);
			if (!$owner) {
				$owner = 'USERID';
			}
			$buddies[CURRENTUSERID] = CURRENTUSERID;
			$when_extra = ' WHEN '.SELF.' THEN '.$element_name.'.'.$owner.'='.CURRENTUSERID;
		}
	} else {
		$when_extra = null;
	}
	return $and.'CASE '.$element_name.'.VISIBILITY_'.$visibility_name.'
		WHEN '.EVERYBODY.' THEN 1
		WHEN '.MEMBERS.' THEN 1
		WHEN '.BUDDIES.' THEN '.($am_buddy ?? (empty($buddies) ? 0 : $element_name.'.'.$owner.' IN ('.implode(',', $buddies).')')).
		$when_extra.'
		ELSE 0
		END';
}

function filter_invisible(array $list, string|array $arg, string $useridname = 'USERID'): array {
	if (is_array($arg)) {
		foreach ($arg as $field) {
			$list = filter_invisible($list, $field, $useridname);
		}
		return $list;
	}
	$field = $arg;
	$key = 'VISIBILITY_'.$field;
	$have_user = have_user();
	$helpdesk_admin = have_admin('helpdesk');
	$newlist = [];
	foreach ($list as $item) {
		if ($helpdesk_admin) {
			$newlist[] = $item;
			continue;
		}
		if (!in_array($item['STATUS'], ['active', 'inactive', 'deceased'])) {
			continue;
		}

		switch ($item[$key]) {
		default:
		case EVERYBODY:
			$newlist[] = $item;
			break;
		case MEMBERS:
			if ($have_user) {
				$newlist[] = $item;
			}
			break;
		case BUDDIES:
			static $buddies = 0;
			if ($buddies === 0) {
				$buddies =
					defined('CURRENTUSERID')
				?	_buddies_full_hash(CURRENTUSERID)
				:	[];
			}
			if (isset($buddies[$item[$useridname]])) {
				$newlist[] = $item;
			}
			break;
		case SELF:
			if ($have_user
			&&	CURRENTUSERID === $item[$useridname]
			) {
				$newlist[] = $item;
			}
			break;
		}
	}
	return $newlist;
}

# VISIBILITY_ACCESS:
# 0:
# ROW_FULL,  row shown to everybody, viewer is member or viewer is buddy
# ROW_ADMIN, row shown because of admin rights
# ROW_SELF,  row shown because item is ours

const VISIBILITY_ROW_TO_CLASS = [
	ROW_FULL	=> '',
	ROW_ADMIN	=> 'admin',
	ROW_SELF	=> 'self',
	false		=> false,
];

function visibility_class(
	 array	&$item,
	 string	 $field,
	?bool	 $admin			 = null,
	 bool	 $allow_helpdesk = true,
	?int	&$visibility	 = null,
): string|false {

	return VISIBILITY_ROW_TO_CLASS[
		_visibility(
			$item,
			$field,
			$admin,
			$allow_helpdesk,
			$visibility)];
}

function _visibility(
	 array	&$item,
	 string	 $field,
	?bool	 $admin			 = null,
	 bool	 $allow_helpdesk = true,
	?int	&$visibility	 = null,
): int|false {
	if (!$item) {
		mail_log('visibility: item is empty', get_defined_vars());
		return false;
	}
	if (!array_key_exists($visibility_key = "VISIBILITY_$field", $item)) {
		mail_log("$visibility_key does not exist in item", get_defined_vars());
		return 0;
	}
	switch ($visibility = $item[$visibility_key]) {
	case EVERYBODY:
		return ROW_FULL;

	case MEMBERS:
		if (have_user()) {
			return ROW_FULL;
		}
		break;
	case SELF:
	case BUDDIES:
		if (!isset($item['USERID'])) {
			if (isset($item['ALBUMID'])) {
				$item['USERID'] = $item['ALBUMID'];
			} elseif (isset($item['ARTISTID'])) {
				# could get artist buddies
				return 0;
			} else {
				mail_log('USERID not set in _visibility()', $item, 'USERID not set in _visibility()');
				return 0;
			}
		}
		if (defined('CURRENTUSERID')) {
			if ($visibility === BUDDIES) {
				if (!isset($item['BUDDY'])) {
					$buddies = _buddies_full_hash(CURRENTUSERID);
					$item['BUDDY'] = isset($buddies[$item['USERID']]);
				}
				if ($item['BUDDY']) {
					return ROW_FULL;
				}
			}
			if (CURRENTUSERID === $item['USERID']) {
				return ROW_SELF;
			}
		}
		break;
	}
	if ($admin
	|| ($allow_helpdesk
	&& have_admin('helpdesk'))
	) {
		return ROW_ADMIN;
	}
	return false;
}

function get_current_visi(int $userid): int {
	if (CURRENTUSERID === $userid) {
		return SELF;
	}
	require_once '_buddies.inc';
	if (is_buddy($userid)) {
		return BUDDIES;
	}
	if (have_user()) {
		return MEMBERS;
	}
	return EVERYBODY;
}

# check whether item of $userid is visible to CURRENTUSERID
function is_visible(int $userid, int $visibility): int {
	switch ($visibility) {
	case EVERYBODY:
		return ROW_FULL;

	case MEMBERS:
		if (have_user()) {
			return ROW_FULL;
		}
		break;

	case BUDDIES:
		if (!defined('CURRENTUSERID')) {
			# currentuser might not be loaded, just return no access
			return 0;
		}
		$buddies = _buddies_full_hash(CURRENTUSERID);
		if (isset($buddies[$userid])) {
			return ROW_FULL;
		}
		if (CURRENTUSERID === $userid) {
			return ROW_SELF;
		}
		break;
	case SELF:
		if (!defined('CURRENTUSERID')) {
			# currentuser might not be loaded, just return no access
			return 0;
		}
		if (CURRENTUSERID === $userid) {
			return ROW_SELF;
		}
		break;
	}
	return 0;
}
