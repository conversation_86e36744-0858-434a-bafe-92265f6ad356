<?php

function embed_music(string $content, ?array $attribs = null): string {
	$width  = have_number($attribs, 'width')  ? $attribs['width']  : 300;
	$height = have_number($attribs, 'height') ? $attribs['height'] : 24;
	if ($attribs
	&&	have_idnumber($attribs, 'id')
	) {
		$music = memcached_single_assoc('music','
			SELECT DURATION, ACTIVE, ACCEPTED, STATE, CUSERID
			FROM music
			WHERE MUSICID = '.$attribs['id']
		);
		if (!$music) {
			return	$music === false
			?	false
			:	'<span class="error">'.__('music:error:nonexistent_LINE', ['ID' => $attribs['id']]).'</span>';
		}
		require_once '_music.inc';
		if (CURRENTIDENTID
		&&	$music['DURATION']
		&&	$music['STATE'] === MUSICSTATE_PROCESSING_OK
		&&	(	$music['ACTIVE']
			&&	$music['ACCEPTED']
			||	have_self_or_admin($music['CUSERID'], 'music')
			)
		) {
			ob_start();
			show_music_player($attribs['id']);
			$musicpart = ob_get_contents();
			ob_end_clean();
			return $musicpart;
		}
		return '<span class="warning">'.__('music:error:not_listenable_LINE', DO_UBB, ['MUSICID' => $attribs['id']]).'</span>';
	}
	require_once '_embed.inc';
	require_once '_spider.inc';
	ob_start();
	?><audio id="musicplayer" controls src="<?= $content ?>"></audio><?
	$content = ob_get_contents();
	ob_end_clean();
	return $content;
}
