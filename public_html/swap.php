<?php

define('CURRENTSTAMP',time());

require_once '_exit_if_readonly.inc';
require_once '_require.inc';
require_once '_favourite.inc';
require_once '_currentuser.inc';
require_once '_album.inc';

function bail($errno) {
	http_response_code($errno);
	exit;
}

$currentuser = new _currentuser(TINY_USER | SKIP_SETTINGS);

header('Cache-control: no-cache,max-age=0,must-revalidate');

if (!have_valid_origin()) {
	bail(403);
}

if (!have_user()
||	!have_post()
||	!isset($_SERVER['eELEMENT'])
||	!isset($_SERVER['eID1'])
||	!isset($_SERVER['eID2'])
||	!require_getlock('swap:'.($element = $_SERVER['eELEMENT']).':'.($id1 = $_SERVER['eID1']).','.($id2 = $_SERVER['eID2']),15)
) {
	bail(412);
}
$idstr = '('.$id1.','.$id2.')';

switch ($element) {
case 'albumelement':
	$owner = 'ALBUMID AS USERID';
	$albumelement = true;
	break;
default:
	$owner = 'USERID';
}
$idname = strtoupper($element).'ID';
$elements = db_rowuse_array($element, '
	SELECT '.$owner.', ORDERID, '.$idname.' AS ID
	FROM '.$element.'
	WHERE '.$idname.' IN '.$idstr
);
if ($elements === false) {
	bail(500);
}
if (count($elements) !== 2) {
	bail(404);
}
if (!have_admin($element)
&&	(	$elements[0]['USERID'] !== CURRENTUSERID
	||	$elements[1]['USERID'] !== CURRENTUSERID
	)
) {
	bail(403);
}
if (!db_update($element,'
	UPDATE '.$element.' SET ORDERID='.$elements[0]['ORDERID'].'
	WHERE '.$idname.'='.($id = $elements[1]['ID']))
||	!db_update($element,'
	UPDATE '.$element.' SET	ORDERID='.$elements[1]['ORDERID'].'
	WHERE '.$idname.'='.($id = $elements[0]['ID']))
) {
	bail(500);
}
if (isset($albumelement)) {
	flush_albumelement($id1);
	flush_albumelement($id2);
}

