<?php

declare(strict_types=1);

require_once '_servertype.inc';
require_once '_globalsettings.inc';

if ((	SERVER_SANDBOX
	||	HOME_OFFICE
	)
&&	!isset($_ENV['TERM'])
&&	!CLI
) {
	define('LOGQUERIES',	true);

	class query_info {
		public int				 $type;
		public int 				 $action;
		public string|array|null $tables;
		public string			 $qstr;
		public float			 $time_taken;
		public ?string			 $server_name;
		public ?array			 $caller;
		public ?string			 $key;

		final public const int LOG_DB		= 0;
		final public const int LOG_MEM		= 1;
		final public const int LOG_READ		= 0;
		final public const int LOG_WRITE	= 1;

		final public const array TYPE_NAME   = [
			self::LOG_DB  => 'DB',
			self::LOG_MEM => 'MEM',
		];
		final public const array ACTION_NAME = [
			self::LOG_READ  => 'READ',
			self::LOG_WRITE => 'WRITE',
		];
	}
	global $__query_count, $__query_log;
	$__query_count = [
		query_info::LOG_DB	=> [query_info::LOG_READ => 0, query_info::LOG_WRITE => 0],
		query_info::LOG_MEM	=> [query_info::LOG_READ => 0],
	];
	$__query_log = [];

	function get_caller(): ?array {
		$traces = debug_backtrace();
		$stop_i = count($traces);
		$start_i = 2;
		for ($i = $start_i; $i < $stop_i; ++ $i) {
			$trace = $traces[$i];
			if (!str_ends_with($trace['file'], '_db.inc')
			&&	!str_ends_with($trace['file'], '_memcache.inc')
			) {
				return $trace;
			}
		}
		return null;
	}

	function log_query(
		int					$type,
		int					$action,
		array|string|null	$tables,
		string		 		$qstr,
		float				$time_taken  = 0,
		?string				$server_name = null,
		?mysqli		 		$handle		 = null,
		?string				$key		 = null,
	): void {
		global $__query_log, $__query_count;
		$qinfo = new query_info();
		$qinfo->type 		= $type;
		$qinfo->action		= $action;
		$qinfo->tables 		= $tables;
		$qinfo->qstr		= $qstr;
		$qinfo->time_taken  = $time_taken;
		if (!$server_name
		&&	$handle
		) {
			$server_name = get_server_name_from_handle($handle);
		}
		$qinfo->server_name = $server_name;
		$qinfo->key			= $key;
		$qinfo->caller		= get_caller();

		$__query_log[$type][$action][] = $qinfo;
		++$__query_count[$type][$action];
	}

	function display_querylog(): void {
		?><div class="hidden" id="queries"><?
		global $__query_count, $__query_log;
		foreach ($__query_count as $type => $actions) {
			foreach ($actions as $action => $cnt) {
				if (!$cnt) {
					continue;
				}
				?><a href="#queries_<?= $type ?>_<?= $action ?>"><?=
					$cnt, ' ', query_info::TYPE_NAME[$type], ' ', query_info::ACTION_NAME[$action], ($cnt > 1 ? 'S' : '')
				?></a><br /><?
			}
		}
		$super_slow = false;
		$slows = '';

		foreach ($__query_log as $type => $actions) {
			foreach ($actions as $action => $queries) {
				?><h5 id="queries_<?= $type ?>_<?= $action ?>"><?= query_info::TYPE_NAME[$type] ?> <?= query_info::ACTION_NAME[$action] ?></h5><?
				foreach ($queries as $qinfo) {
					ob_start();
					?><div class="<?
					$slow = null;
					if ($qinfo->time_taken >= 500) {
						?>error <?
						$slow = 'REALLY SLOW: ';
						$super_slow = true;
					} elseif ($qinfo->time_taken >= 50) {
						?>warning <?
						$slow = 'SLOW: ';
					}
					?>query"><?
					?><b><?= query_info::TYPE_NAME[$type] ?> <?= query_info::ACTION_NAME[$action] ?></b><?
					?><small class="darken"><?
					if ($qinfo->time_taken) {
						?><span class="r"><?= $slow,round($qinfo->time_taken,2) ?> ms</span><?
					}
					?> <? echo is_array($qinfo->tables) ? implode(', ',$qinfo->tables) : $qinfo->tables;
					if ($qinfo->server_name) {
						?> (<span class="server"><?= $qinfo->server_name ?></span>)<?
					} elseif (!empty($qinfo->key)) {
						?> (<?= $qinfo->key ?>)<?
					}
					if ($qinfo->caller) {
						?> called from <?
						$line = $qinfo->caller['line'];
						$file = $qinfo->caller['file'];

						echo escape_utf8($file), ' line '.$line;
					}
					?></small><?
					?><br /><?
					?><span class="forcewrap"><?= escape_utf8(str_replace("\t", ' ', $qinfo->qstr)) ?></span><?
					?></div><?
					if ($slow) {
						$slows .= ob_get_flush();
					} else {
						ob_end_flush();
					}
				}
			}
		}
		if (!empty($slows)) {
			?><h5 id="queries_slow">SLOW</h5><?
			echo $slows;
		}
		?></div><?
		ob_start();
		?>addreadyevent(function(){
			<? if (!empty($slows)) { ?>
				<? if (!$super_slow) { ?>
					setclass('slow-query-link', true, 'light', true);
				<? } ?>
				setdisplay('slow-query-link', true, true);
			<? } ?>
			if (/^\#queries(?:_slow)?/.test(location.hash)) {
				unhide('queries');
			}
		})<?
		$js_data = ob_get_clean();
		echo '<script>'.minify_js($js_data).'</script>';
	}
} else {
	define('LOGQUERIES',	false);
}
