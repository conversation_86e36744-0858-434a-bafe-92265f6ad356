<?php

class _newslist {
	public ?int $max				= null;
	public bool $show_date			= false;
	public ?string $postlink		= null;			// trailing URL part

	private ?array $newslist	= null;
	private int $size			= 0;
	private ?array $wheres		= null;
	private ?array $joins		= null;
	private ?array $tables		= ['news'];
	private bool $have_more		= false;
	private int	$offset			= 0;
	private string $order		= ' ORDER BY news.NEWSID DESC ';

	function add_where($wpart) {
		$this->wheres[] = $wpart;
	}
	function have_rows() {
		return $this->size;
	}
	function query() {
		$selects = ['news.NEWSID,news.TITLE,news.ACCEPTED,news.USERID,news.PSTAMP,TEASER'];
		$selects[] = '(SELECT COUNT(*) FROM news_comment WHERE ID=NEWSID) AS CNT';
		$selects[] = '(SELECT GROUP_CONCAT(NEWSADID) FROM newsad WHERE newsad.NEWSID=news.NEWSID) AS NEWSADIDS';
		$this->tables[] = 'news_comment';
		$this->tables[] = 'newsad';
		$this->offset = (have_idnumber($_REQUEST,'OFFSET') ? $_REQUEST['OFFSET'] : 0);

		if (!have_super_admin()
		&&	CURRENTLANGUAGE !== 'en'
		) {
			$this->wheres[] = 'NEWSID != 34290';
		}

		$tmplist = memcached_rowuse_array_if_not_admin(
			'news',
			$this->tables, '
			SELECT '.implode(',',$selects).'
			FROM news '.($this->joins ? implode(' ',$this->joins) : null).
			($this->wheres ? ' WHERE '.implode(' AND ',$this->wheres) : null).
			$this->order.
			($this->max ? ' LIMIT '.$this->offset.', '.($this->max + 1) : null),
			TEN_MINUTES
		);
		if ($tmplist === false) {
			return false;
		}
		if (!$tmplist) {
			return true;
		}
		$is_admin = have_admin('news');
		$donecnt = 0;
		foreach ($tmplist as $news) {
			++$donecnt;
			if ($news['ACCEPTED']
			||	(	$is_admin
				||	CURRENTUSERID == $news['USERID']
				)
			) {
				$this->newslist[] = $news;
				++$this->size;
				if ($this->size == $this->max) {
					break;
				}
			}
		}
		if ($donecnt < count($tmplist)) {
			$this->have_more = true;
		}
		return true;
	}
	function display() {
		if (!isset($this->newslist)) {
			return;
		}
		$newsadmin = have_admin('news');
		$okcnt = 0;
		foreach ($this->newslist as $news) {
			if (!$newsadmin
			&&	$news['PSTAMP'] > CURRENTSTAMP
			) {
				continue;
			}
			$oknews[] = $news;
			++$okcnt;
		}
		if (!$okcnt) return;
		$this->display_controls();

		foreach ($oknews as $news) {
			$this->display_news($news);
			if (--$okcnt) {
/*				?><hr class="slim"><?*/
			}
		}
		$this->display_controls();
	}
	function display_news($news) {
		$link =	get_element_href('news',$news['NEWSID'],$news['TITLE']).$this->postlink;

		require_once '_ubb.inc';

		?><div<?
		?> onclick="openLink(event,'<?= $link ?>')" class="hh ptr vtop<?
		if (!$news['ACCEPTED']) {
			?> unaccepted<?
		}
		?>"><?

		?><div class="nws"><?
		?><a onclick="return false" href="<?= $link ?>"><?= flat_with_entities($news['TITLE'], UBB_UTF8) ?></a><?

		static $news_admin = null;
		static $newsad_admin = null;
		if ($news_admin === null) {
			$news_admin = have_admin('news');
			$newsad_admin = have_admin('newsad');
		}

		if (($news_admin || $newsad_admin)
		&&	$news['NEWSADIDS']
		) {
			$newsads = [];
			foreach (explode(',',$news['NEWSADIDS']) as $newsadid) {
				$newsads[] =
					'<span class="notice nb">'.
					(	$newsad_admin
					?	/** @lang HTML */ '<a onclick="event.stopPropagation(); return true;" href="'.get_element_href('newsad',$newsadid).'">'
					:	null
					).
					element_name('newsad').' '.$newsadid.
					(	$newsad_admin
					?	'</span>'
					:	null
					);
			}
			?> <?= MIDDLE_DOT_ENTITY ?> <?= implode(', ',$newsads) ?><?
		}
		?><div class="r pad"><? _date_display($news['PSTAMP']) ?></div><?

		?></div><?

		?><div class="nwsitm"><?
		echo flat_with_entities($news['TEASER'], UBB_UTF8);
		if ($news['CNT']) {
			?> <a class="small light" href="<?= link_to_comments('news',$news['NEWSID']) ?>"><?= $news['CNT'] ?></a><?
		}
		?></div><?

		?></div><?
	}
	function display_controls() {
		if (!$this->have_more && !$this->offset) {
			return;
		}
		layout_open_menu();
		layout_open_menuitem();
		if ($this->have_more) {
			$newoffset = $this->offset + $this->max;
			?><a href="<?= escape_specials(
				isset($_REQUEST['OFFSET'])
			?	preg_replace('"OFFSET=(\d*)"','OFFSET='.$newoffset,$_SERVER['REQUEST_URI'])
			:	$_SERVER['REQUEST_URI'].'&OFFSET='.$newoffset
			)
			?>">&lt; <?= $this->max;
			?></a><?
		} else {
			?><b>&lt;<?= $this->max; ?></b><?
		}
		layout_next_menuitem();
		if ($this->offset) {
			$newoffset = $this->offset > $this->max ? $this->offset - $this->max : 0;
			?><a href="<?= escape_specials(
				isset($_REQUEST['OFFSET'])
			?	preg_replace('"OFFSET=(\d*)"','OFFSET='.$newoffset,$_SERVER['REQUEST_URI'])
			:	$_SERVER['REQUEST_URI'].'&OFFSET='.$newoffset
			)
			?>"><?= $this->max;
			?>&gt;</a><?
		} else {
			?><b><?= $this->max; ?>&gt;</b><?
		}
		layout_close_menuitem();
		layout_close_menu();
	}
	function linked_to_organization_through_party($orgid) {
		$newsids = memcached_simpler_array(
			'connect', '
			SELECT ASSOCID
			FROM connect
			WHERE MAINTYPE="organization"
			  AND ASSOCTYPE="news"
			  AND MAINID='.$orgid.'
			UNION
			SELECT ptn.ASSOCID
			FROM connect AS otp
			JOIN connect AS ptn ON
					  otp.MAINTYPE="organization"
				  AND otp.MAINID='.$orgid.'
				  AND otp.ASSOCTYPE="party"
				  AND otp.ASSOCID=ptn.MAINID
				  AND ptn.MAINTYPE="party"
				  AND ptn.ASSOCTYPE="news"',
			TEN_MINUTES
		);
		if (!$newsids) {
			$this->wheres[] = '0';
			return;
		}
		$this->wheres[] = 'news.NEWSID IN ('.implode(', ',$newsids).')';
	}
	function linked_to_organization($orgid) {
		$this->joins[] = 'JOIN connect ON ASSOCID=news.NEWSID AND ASSOCTYPE="news" AND MAINTYPE="organization" AND MAINID='.$orgid;
		$this->tables[] = 'connect';
	}
	function linked_to_location($locationid) {
		$this->joins[] = '
			JOIN (	SELECT ASSOCID AS NEWSID
				FROM connect
				WHERE MAINTYPE="location"
				  AND MAINID='.$locationid.'
				  AND ASSOCTYPE="news"
				UNION SELECT ASSOCID AS NEWSID
				FROM connect
				JOIN party ON MAINID=PARTYID
				WHERE LOCATIONID='.$locationid.'
				  AND MAINTYPE="party"
				  AND ASSOCTYPE="news"
			) AS combined USING (NEWSID)';
		$this->tables[] = 'connect';
	}
	function linked_to_party($partyid) {
		$this->wheres [] = 'ASSOCTYPE="news" AND MAINTYPE="party" AND MAINID='.$partyid;
		$this->joins[] = 'JOIN connect ON ASSOCID=news.NEWSID';
		$this->tables[] = 'connect';
	}
	function order_by_newsid_ascending() {
		$this->order = ' ORDER BY news.NEWSID ASC ';
	}
	function order_by_descending_pstamp() {
		$this->order = ' ORDER BY news.PSTAMP DESC ';
	}
	function with_ids($ids) {
		$this->wheres[] = 'NEWSID IN ('.implode(',',$ids).')';
	}
}
