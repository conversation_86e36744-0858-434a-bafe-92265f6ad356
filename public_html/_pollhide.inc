<?php

require_once '_db.inc';
require_once '_memcache.inc';

function hide_poll($pollid) {
	if (db_insert('hidepoll','
		INSERT IGNORE INTO hidepoll SET
			IDENTID	='.CURRENTIDENTID.',
			POLLID	='.$pollid,
		DB_DONT_WARN_DUP_ENTRY
		)
	) {
		memcached_delete(get_poll_hide_key($pollid));
		return true;
	}
	return false;
}
function is_hidden_poll($arg) {
	$pollid = is_array($arg) ? $arg['POLLID'] : $arg;
	static $__hiddenpoll;
	return	isset($__hiddenpoll[$pollid])
	?	$__hiddenpoll[$pollid]
	:	$__hiddenpoll[$pollid] = memcached_single(
		'hidepoll',
		'SELECT 1 FROM hidepoll WHERE IDENTID='.CURRENTIDENTID.' AND POLLID='.$pollid,
		FIVE_MINUTES,
		get_poll_hide_key($pollid)
	);
}
function get_poll_hide_key($pollid,$identid = CURRENTIDENTID) {
	return 'hidepoll:'.$identid.':'.$pollid;
}
