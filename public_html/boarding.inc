<?php

function get_boarding_info(): array|false|null {
	static $__boarding = 0;
	if ($__boarding !== 0) {
		return $__boarding;
	}
	if (!($boardingid = $_REQUEST['sID'])) {
		return $__boarding = null;
	}
	return $__boarding = memcached_single_assoc(['boarding', 'city'], '
		SELECT	boarding.NAME, city.NAME AS CITY_NAME, boarding.LATITUDE AS LAT, boarding.LONGITUDE AS LON, COUNTRYID
		FROM boarding
		LEFT JOIN city USING (CITYID)∂z
		WHERE BOARDINGID = '.$boardingid,
		TEN_MINUTES
	);
}
function display_title(): void {
	if ($boarding = get_boarding_info()) {
		echo element_name('boarding') ?>: <?
		echo escape_utf8($boarding['NAME']);
		if ($boarding['CITY_NAME']) {
			?>, <?
			echo escape_utf8($boarding['CITY_NAME']);
		}
		# show_current_action($boardingid);
	} else {
		echo element_plural_name('boarding');
		# show_current_action();
	}
}
function display_header(): void {
	if ($boarding = get_boarding_info()) {
		require_once '_geotag.inc';
		show_geotag($boarding);
	}
	require_once '_feed.inc';
	show_feed('agenda',FEED_HEADER);
}
function perform_commit(): ?bool {
	switch ($_REQUEST['ACTION']) {
	default:
	case null:			return null;
	case 'commit':		return boarding_commit();
	case 'remove':		require_once '_remove.inc';  return remove_element();
	case 'combine':		require_once '_combine.inc'; return combine_element();
	}
}
function display_body(): void {
	switch ($_REQUEST['ACTION']) {
	default:
	case 'searchresult':
	case 'remove':
	case null:			boarding_display_overview(); return;
	case 'form':		boarding_display_form(); return;
	case 'archive':
	case 'commit':
	case 'archive':
	case 'combine':
	case 'remove':
#		robot_action('noindex');
	case 'single':		boarding_display_single(); return;
	case 'combinewith':	require_once '_combine.inc'; show_combine_with(); return;
	}
}
function main_menu(?array $boarding = null) {
	$is_admin = have_admin('boarding');
	layout_open_menu();
	layout_menuitem(Eelement_name('overview'),'/boarding',!$_REQUEST['ACTION']);
	layout_close_menu();

	if (!$is_admin) {
		return;
	}
	layout_open_menu();
	if ($boarding) {
		$boardingid = $boarding['BOARDINGID'];
		$boardinghref = '/boarding/'.$boardingid;
		layout_menuitem(__C('action:change'), $boardinghref.'/form', $_REQUEST['ACTION'] === 'form');
		show_element_menuitems();
	} else {
		layout_menuitem(__C('action:add'),'/boarding/form');
	}
	layout_close_menu();
}
function boarding_display_overview() {
	if ($_REQUEST['ACTION'] === 'searchresult') {
		require_once '_boardinglist.inc';
		layout_show_section_header(Eelement_plural_name('search_result'));
		main_menu();
		$terms = clean_utf8($_REQUEST, 'NAME');
		?><div class="bold block"><?= Eelement_name('search_term') ?>: &quot;<?= escape_utf8($terms) ?>&quot;</div><?
		layout_open_box('boarding');
		layout_box_header(Eelement_plural_name('search_result'));
		$boardinglist = new boardinglist();
		$boardinglist->show_city = true;
		$boardinglist->like($terms);
		if ($boardinglist->query()) {
			if (!$boardinglist->is_empty()) {
				$boardinglist->display();
			} else {
				?><p>Niets gevonden.</p><?
			}
		}
		layout_close_box();
		unset($boardinglist);
	} elseif (!$_REQUEST['ACTION']) {
		layout_show_section_header();
		main_menu();
		?><form accept-charset="utf-8" onsubmit="return submitForm(this);" method="get" action="/boarding/searchresult"><?
		?><div class="block"><?= __C('search:for_name') ?>: <?
		?><input type="search" name="NAME" id="boardingname" required /> <?
		?><input type="submit" value="<?= __('action:search') ?>" /></div></form><?
	} else {
		not_found();
	}
}
function boarding_display_form() {
	if (!require_admin('boarding')) {
		return;
	}
	require_once '_citylist.inc';

	layout_section_header(Eelement_name('boarding').' '.MIDDLE_DOT_ENTITY.' '.__(($boardingid = $_REQUEST['sID']) ? 'action:change' : 'action:add'));

	main_menu();

	if ($boardingid) {
		$boarding = db_single_assoc('boarding','SELECT * FROM boarding WHERE BOARDINGID='.$boardingid);
		if ($boarding === false) {
			return;
		}
		if (!$boarding) {
			register_error('boarding:error:nonexistent_LINE',array('ID'=>$boardingid));
			return;
		}
	} else {
		$boarding = null;
	}
	include_js('js/form/boarding');
	?><form accept-charset="utf-8" id="elementform" onsubmit="return submitBoardingForm(this,<?= $boardingid ?>)" method="post" action="/boarding<?
	if ($boardingid) {
		?>/<? echo $boardingid;
	}
	?>/commit"><?

	$spec = explain_table('boarding');

	layout_open_box('boarding');
	layout_open_table('fw');
	layout_start_row();
		echo Eelement_name('name');
		layout_field_value();
		?><input required="required" type="text" maxlength="<?= $spec['NAME']->maxlength ?>" name="NAME" id="boardingname" value="<? if (isset($boarding)) echo escape_utf8($boarding['NAME']); ?>" autofocus="autofocus" /><?
	layout_restart_row();
		echo Eelement_name('address');
		layout_field_value();
		show_input([
			'onchang'		=> /** @lang JavaScript */ "update_mapsq(this.value + ', ' + this.form.CITYID.options[this.form.CITYID.selectedIndex].text, true);",
			'type'			=> 'text',
			'name'			=> 'ADDRESS',
			'value_utf8'	=> $boarding['ADDRESS'] ?? '',
			'required'		=> true,
		]);
		?><br /><?
		?><label><input type="checkbox" name="OVERLAP" value="1" /> <?= __('action:allow_overlap') ?></label><?

	layout_restart_row();
		?><label for="zipcode"><?= Eelement_name('zipcode') ?></label><?
		layout_field_value();
		?><input<?
		?> type="text"<?
		?> id="zipcode"<?
		?> name="ZIPCODE"<?
		?> maxlength="<?= $spec['ZIPCODE']->maxlength ?>"<?
		?> value="<? if (isset($boarding)) echo escape_utf8($boarding['ZIPCODE']); ?>" /><?

	layout_restart_row();
		echo Eelement_name('city');
		layout_field_value();
		display_dynamic_city_pair(
			$boarding['CITYID'] ?? 0,
			countryid: $boarding['COUNTRYID'] ?? 0,
			flags: UPDATE_MAP,
		);

	layout_restart_row();
		echo Eelement_name('map');
		layout_field_value();
		require_once '_searchmap.inc';
		show_search_map('boarding', $boardingid, $boarding, 13);

	layout_restart_row();
		echo Eelement_name('latitude');
		layout_field_value();
		show_gps(array(
			'required'	=> true,
			'name'		=> 'LATITUDE',
			'value'		=> $boarding['LATITUDE'] ?? null,
		));
	layout_restart_row();
		echo Eelement_name('longitude');
		layout_field_value();
		show_gps(array(
			'required'	=> true,
			'name'		=> 'LONGITUDE',
			'value'		=> $boarding['LONGITUDE'] ?? null,
		));
	layout_stop_row();
	layout_close_table();

	layout_close_box();
	?><div class="block"><input type="submit" value="<?= __(isset($boarding) ? 'action:change' : 'action:add') ?>" /></div></form><?
}
function boarding_display_single() {
	if (!($boardingid = require_idnumber($_REQUEST,'sID'))) {
		return;
	}
	if (!($boarding = memcached_single_assoc_if_not_admin(
		'boarding',
		['boarding','city','country'], '
		SELECT	boarding.*,
				boarding.BOARDINGID AS ID,
				city.NAME AS CITYNAME, REGIONID,
				city.COUNTRYID, country.SHORT
		FROM boarding
		LEFT JOIN city USING (CITYID)
		LEFT JOIN country USING (COUNTRYID)
		WHERE BOARDINGID = '.$boardingid,
		TEN_MINUTES))
	) {
		if ($boarding !== false) {
			register_error('boarding:error:nonexistent_LINE', ['ID' => $boardingid]);
		}
		return;
	}
	$boarding_admin = have_admin('boarding');
	layout_show_section_header();

	main_menu($boarding);

	require_once '_ticketlist.inc';
	show_connected_tickets();

	// PARTYCNT
	if (!($cnts = memcached_single_assoc('party', /** @lang MariaDB */'
		SELECT	COUNT(IF(STAMP >= '.TODAYSTAMP.', 1, NULL)) AS FUTURE_CNT,
				COUNT(IF(STAMP >= '.TODAYSTAMP.' AND ACCEPTED AND CANCELLED = 0 AND POSTPONED = 0 AND MOVEDID = 0, 1, NULL)) AS FUTURE_OK_CNT,
				COUNT(*) AS TOTAL_CNT,
				COUNT(IF(ACCEPTED AND CANCELLED = 0 AND POSTPONED = 0 AND MOVEDID = 0, 1, NULL)) AS TOTAL_OK_CNT
		FROM party
		WHERE BOARDINGID = '.$boardingid,
		TEN_MINUTES))
	) {
		return;
	}
	if (!have_admin('party')) {
		$cnts['TOTAL_CNT'] = $cnts['TOTAL_OK_CNT'];
		$cnts['FUTURE_CNT'] = $cnts['FUTURE_OK_CNT'];
	}
	$cnts['PAST_CNT'] = $cnts['TOTAL_CNT'] - $cnts['FUTURE_CNT'];

	?><article itemscope itemtype="https://schema.org/Place"><?

	layout_open_box('boarding');
	layout_box_header(escape_utf8($boarding['NAME']));

	$mapping = 	!empty($boarding['LONGITUDE'])
			&&	!empty($boarding['LATITUDE'])
			||	$boarding['ZIPCODE']
			||	$boarding['ADDRESS'];
	if ($mapping) {
		?><div class="l"><?
	}
	layout_open_table(TABLE_VTOP);
	// ADDRESS
	if ($boarding['ADDRESS']
	||	$boarding['ZIPCODE']
	||	$boarding['CITYNAME']
	) {
		layout_start_row();
		echo Eelement_name('address');
		layout_field_value();
		if ($boarding['ADDRESS']) {
			echo str_replace(' ', '&nbsp;', escape_utf8($boarding['ADDRESS'])); ?><br /><?
		}
		if ($boarding['ZIPCODE']) {
			echo str_replace(' ', '&nbsp;' ,escape_utf8($boarding['ZIPCODE'])); ?><br /><?
		}
		if ($boarding['CITYID']) {
			echo get_element_link('city', $boarding['CITYID'], $boarding['CITYNAME']);

			if ($boarding['REGIONID']) {
				?><br /><?
				echo get_element_link('region',$boarding['REGIONID']);
			}

			$currentcity = get_current_city();
			if (ROBOT
			||	$currentcity
			&&	$currentcity['COUNTRYID'] != $boarding['COUNTRYID']
			) {
				require_once '_countryflag.inc';
				?><br /><?
				echo get_element_link('country', $boarding['COUNTRYID']);
				show_country_flag($boarding['COUNTRYID'], 'lmrgn light');
			}
		}
		layout_stop_row();
		require_once '_addresscheck.inc';
		if ($same = find_at_same_address('boarding',$boarding['ADDRESS'],$boarding['CITYID'],$boardingid, cache: true)) {
			layout_start_row();
			echo Eelement_name('same_address');
			layout_field_value();
			$names = memcached_simple_hash('boarding','SELECT BOARDINGID,NAME FROM boarding WHERE ADDRESS!=NAME AND BOARDINGID IN ('.implodekeys(',',$same).')');
			foreach ($same as $sameid => $same) {
				?><div><a href="<?= get_element_href('boarding',$sameid) ?>"><?
				echo escape_specials(get_element_title('boarding',$sameid));
				if (isset($names[$sameid])) {
					?>, <? echo escape_utf8($names[$sameid]);
				}
				?></a><?
				?></div><?
			}
			layout_stop_row();
		}
	}
	layout_close_table();
	if ($mapping) {
		require_once '_maplink.inc';
		?></div><div class="l lmrgn"><?
		show_map_link($boarding,16);
		?></div><?
		?><div class="lclr"></div><?
	}

	require_once '_affiliates.inc';
	show_booking_com('boarding',$boardingid,$boarding);

	layout_display_alteration_note($boarding);
	layout_close_box();

	layout_open_box('boarding', 'agenda');
	// AGENDA
	layout_open_box_header();
	?><h2><?
	if ($future = $cnts['FUTURE_CNT']) {
		echo Eelement_name('agenda');
	} else {
		?><span class="light"><?= Eelement_name('agenda') ?></span><?
	}
	?></h2><?

	$parts = [];
	if (!ROBOT) {
		$parts[] = get_ical_feed_link();
		if (SHOW_FEED_ICON) {
			$parts[] = get_feed('agenda',FEED_ICON);
		}
	}
	if ($cnts['PAST_CNT']) {
		[$linkopen,$linkclose] = get_action_open_close('archive');
		$parts[] = $linkopen.element_name('archive').$linkclose;
	}
	if ($parts) {
		layout_continue_box_header();
		?><nav><?= implode(' '.MIDDLE_DOT_ENTITY.' ',$parts) ?></nav><?
	}
	layout_close_box_header();

	if ($future) {
		require_once '_partylist.inc';
		$partylist = new _partylist();
		if ($_REQUEST['ACTION'] === 'single') {
			$partylist->show_vevent = true;
		}
		$partylist->boarding_from($boardingid);
		$partylist->show_location =	true;
		$partylist->show_people = true;
		$partylist->hide_flag = true;
		$partylist->show_date = true;
		$partylist->show_camera = true;
		$partylist->show_stars = true;
		$partylist->show_buddy_hearts = true;
		$partylist->order_chronologically();
		$partylist->select_future();
		if ($partylist->query()) {
			$partylist->display();
		}
		unset($partylist);
	} else {
		require_once '_lastparty.inc';
		show_last_party('boarding',$boardingid);
	}

	layout_continue_box(1/3);

	// STATISTICS
	layout_box_header(Eelement_plural_name('statistic'));
	layout_open_table('fw default');//,null,null,2);
	layout_start_reverse_row(!$cnts['TOTAL_CNT'] ? ROW_LIGHT : 0);
		echo $cnts['TOTAL_CNT'] ?: __('counter:none');
		layout_value_field();
		echo Eelement_name('party', $cnts['TOTAL_CNT']);
	layout_restart_reverse_row(!$future ? ROW_LIGHT : 0);
		echo $future ?: __('counter:none');
		layout_value_field();
		echo __('when:in_the_future');
	if ($cnts['PAST_CNT']) {
		layout_restart_reverse_row();
		// ARCHIVE
		[$linkopen,$linkclose] = get_action_open_close('archive');
		echo $linkopen,$cnts['PAST_CNT'],$linkclose;
		layout_value_field();
		echo $linkopen,__('when:in_the_past'),$linkclose;
	}
	if ($visitorcnts = memcached_simple_hash(['party', 'going'],'
		SELECT COUNT(*), COUNT(DISTINCT going.USERID)
		FROM party
		JOIN going USING (PARTYID)
		WHERE MAYBE=0
		  AND BOARDINGID='.$boardingid,
		HALF_HOUR)
	) {
		[$total, $uniq] = keyval($visitorcnts);
		if ($total) {
			layout_restart_reverse_row();
			echo $total;
			layout_value_field();
			echo element_name('visitor',$total);
		}
		if ($uniq) {
			layout_restart_reverse_row();
			echo $uniq;
			layout_value_field();
			echo element_name('unique_visitor',$uniq);
		}
	}
	layout_stop_row();
	layout_close_table();
	layout_close_box();

	switch ($_REQUEST['ACTION']) {
	case 'archive':
		if (!$cnts['PAST_CNT']) {
			break;
		}
		require_once '_partylist.inc';
		$partylist = new _partylist();
		$partylist->show_location = true;
		$partylist->show_stars = true;
		$partylist->hide_flag = true;
		$partylist->show_date = true;
		$partylist->show_camera = true;
		$partylist->boarding_from($boardingid);
		$partylist->select_past();
		$partylist->order_reverse_chronologically();
		if ($partylist->query()) {
			layout_open_box('boarding','archive');
			layout_box_header(Eelement_name('archive'));
			$partylist->display();
			layout_close_box();
		}
		break;
	}
	?></article><?
	counthit('boarding',$boardingid);
}
function boarding_commit(): bool {
	require_once '_namefix.inc';
	require_once '_addresscheck.inc';
	if (!require_admin('boarding')
	||	!optional_number($_REQUEST, 'BOARDINGID')
	||	!require_something_trim($_POST, 'NAME', null, null, utf8: true)
	||	!require_something_trim($_POST, 'ADDRESS', null, null, utf8: true)
	||	!require_anything_trim($_POST, 'ZIPCODE', utf8: true)
	||	false === require_number($_POST, 'CITYID')
	||	!require_lng_or_lat($_POST, 'LONGITUDE')
	||	!require_lng_or_lat($_POST, 'LATITUDE')
	) {
		return false;
	}
	$boardingid = have_idnumber($_REQUEST,'BOARDINGID') ?: 0;
	if (!isset($_POST['OVERLAP'])) {
		if (($result = address_exists('boarding', $_POST['ADDRESS'], $_POST['CITYID'], $boardingid))
		||	$result === false
		) {
			return false;
		}
	}
	$setlist[] = 'NAME="'.		addslashes(get_fixed_utf8_name($_POST['NAME'])).'"';
	$setlist[] = 'ADDRESS="'.	addslashes($_POST['ADDRESS']).'"';
	$setlist[] = 'ZIPCODE="'.	addslashes($_POST['ZIPCODE']).'"';
	$setlist[] = 'CITYID='.		$_POST['CITYID'];
	$setlist[] = 'LONGITUDE='.	round($_POST['LONGITUDE'],12);
	$setlist[] = 'LATITUDE='.	round($_POST['LATITUDE'],12);

	if ($boardingid) {
		if (!db_insert('boarding_log','
			INSERT INTO boarding_log
			SELECT * FROM boarding
			WHERE NOT '.binary_equal($setlist).'
			  AND BOARDINGID='.$boardingid)
		) {
			return false;
		}
		if (db_affected()
		&&	!db_update('boarding','
				UPDATE boarding SET
					MUSERID	='.CURRENTUSERID.',
					MSTAMP	='.CURRENTSTAMP.',
					'.implode(',',$setlist).'
				WHERE BOARDINGID = '.$boardingid)
		) {
			return false;
		}
		register_notice('boarding:notice:changed_LINE');
	} else {
		if (!db_insert('boarding','
			INSERT INTO boarding SET
				CSTAMP	='.CURRENTSTAMP.',
				CUSERID	='.CURRENTUSERID.',
				'.implode(',',$setlist))
		) {
			return false;
		}
		$_REQUEST['sID'] = db_insert_id();
		register_notice('boarding:notice:added_LINE');
	}
	return true;
}
