#!/usr/bin/php
<?php

declare(strict_types=1);

if (PHP_SAPI !== 'cli') {
    exit(1);
}

function compact_var_dump(string $text): string {
	return $text;
}

function escape_specials(string $text, bool $utf8 = false): string {
	return $text;
}

$test = [
	'test1'	=> 10,
	'test2'	=> 10.5,
	'test3' => [
		'inner1'	=> true,
		'inner2'	=> false,
		'inner3'	=> null,
	],
];

var_dump($test);

var_dump((object)$test);

exit;

print_rr((object)$test);
print_rr(true);

		function get_pretty_value(array|object|int|float|string|bool|null $value, bool $utf8): array|string {
			if (is_array($value)) {
				$result = [];
				foreach ($value as $key => $val) {
					$result[get_pretty_value($key, $utf8)] = get_pretty_value($val, $utf8);
				}
				return $result;
			}
			if (is_object($value)) {
				$result = [];
				foreach ($value as $key => $val) {
					$result[get_pretty_value($key, $utf8)] = get_pretty_value($val, $utf8);
				}
				return $result;
			}
			if (is_int($value)) {
				return '<span style="color: cyan; font-weight:bold;">'.$value.'</span>';
			}
			if (is_float($value)) {
				return '<span style="color: #66F; font-weight:bold;">'.$value.'</span>';
			} 
			if ($value === '') {
				return '<span style="color: gray; font-weight:bold;">&laquo;&raquo;</span>';
			}
			if ($value === null) {
				return '<span style="color: orange; font-weight:bold;">null</span>';
			}
			if ($value === true || $value === "\x1") {
				return '<span style="color: #0F0; font-weight:bold;">true</span>';
			}
			if ($value === false || $value === "\x0") {
				# FIXME: comparison to "\x0" does not work
				return '<span style="color: #F00; font-weight:bold;">false</span>';
			}
			if (is_string($value)) {
				return
					'<span style="color: gray; font-weight: bold;">&laquo;</span>'.
					escape_specials($value, $utf8).
					'<span style="color: gray; font-weight: bold;">&raquo;</span>';
			}
			return '<span style="color: orange; font-weight: bold;">'.escape_specials($value, $utf8).'</span>';
		}


function print_rr(
	mixed   $elem,
	?string $name		   = null,
	bool	$utf8		   = false,
	bool	$force		   = false,
	bool	$hide_location = false,
	?string	$data		   = null,
): void {
	if ($to_error_log
	=	!empty($_SERVER['REQUEST_URI'])
	&&	preg_match('"\.(?:json|js|png|gif|jpe?g|webp)$"i', $_SERVER['REQUEST_URI'])
	) {
		ob_start();
	}

	if (false) {
		if (!$hide_location) {
			echo 'called from ', $file, ($function ? ' => '.$function : ''), ' on line ', $line, "\n";
		}
	 	if ($name) {
			echo $name ?>:<?
			/*<?= gettype($elem) ?> = <?*/
		}
		ob_start();
		var_dump($elem);
		echo compact_var_dump(ob_get_clean());

	} else {
		?><pre><?
		if ($name) {
			echo escape_specials($name, $utf8);
		}
		?>:<?= escape_specials(gettype($elem)) ?> = <?

		$elem = get_pretty_value($elem, $utf8);

/*		if ($processed
		=	is_array ($elem)
		||	is_object($elem)
		) {
			$nullify = static function(mixed $arg) use (&$nullify, $utf8): array {
				$res = [];
				foreach ((is_object($arg) ? get_object_vars($arg) : $arg) as $key => $val) {
					$check_variables = ['key'];
					if (is_array($val)
					||	is_object($val)
					) {
					 	$val = $nullify($val);
					} else {
						$check_variables[] = 'val';
					}
					foreach ($check_variables as $check_variable) {
						$$check_variable = get_pretty_value($$check_variable, $utf8);
					}
					$res[$key] = $val;
				}
				return $res;
			};
			$elem = $nullify($elem);
		} else {
			$elem = get_pretty_value($elem, $utf8 );
		}*/
		if (!$data) {
			ob_start();
			print_r($elem);
			$data = compact_var_dump(ob_get_clean());
		}
		echo $data;
		?></pre><?
	}
	if ($to_error_log) {
		error_log(ob_get_clean());
	}
}

