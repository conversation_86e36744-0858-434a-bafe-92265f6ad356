<?php

function get_smtp_result(&$smtp,&$msg,&$log) {
	while (false !== ($line = fgets($smtp))) {
		$log .= 'recv:'.$line;
		if (preg_match('"^\s*(\d+)\s+(.*)$"is',$line,$matches)) {
			$msg = $matches[2];
			return $matches[1];
		}
	}
	return false;
}
function checkmail(string $mx_host, string $email, ?string &$msg, ?int $timeout = null, ?int &$returncode = null, ?bool &$cached = false): bool|null {
	if (!$timeout) {
		$timeout = 5;
	}
	$log = '';
	$rc = actual_checkmail($mx_host, $email, $msg, $timeout, $returncode, $log);
	log_checkmail($email, $log, $rc);
	return $rc;
}
function actual_checkmail(string $mx_host, string $email, ?string &$msg, ?int $timeout = null, ?int &$returncode = null, string &$log = ''): bool|null {
	if (!isset($GLOBALS['__forcemailcheck'])
	&&	preg_match('"(?:hotmail|live|msn|outlook)\."', $mx_host)	# don't check hotmail/live/msn for the moment, they quickly blacklist
	) {
		return null;
	}
	$smtp = safe_fsockopen($mx_host, 25, $errno, $msg, 5);
	if (!$smtp) {
		$msg = 'could not connect to port 25 on '.$mx_host.': '.$msg.' ('.$errno.')';
		return false;
	}
	if (!stream_set_timeout($smtp,$timeout)) {
		$msg = 'unable to set timeout';
		return null;
	}
	$log = '';
	$result = get_smtp_result($smtp,$msg,$log);
	if (!$result || $result != 220) {
		$msg = trim($msg);
		return null;
	}
	$str = "HELO partyflock.nl\r\n";
	fwrite($smtp,$str);
	$log .= 'send:'.$str;

	$result = get_smtp_result($smtp,$msg,$log);
	if (!$result || $result != 250) {
		$msg = trim($msg);
		return null;
	}
	$str = "MAIL FROM:<<EMAIL>>\r\n";
	fwrite($smtp,$str);
	$log .= 'send:'.$str;
	$result = get_smtp_result($smtp,$msg,$log);
	if (!$result || $result != 250) {
		$msg = trim($msg);
		return null;
	}
	$str = "RCPT TO:<$email>\r\n";
	fwrite($smtp,$str);
	$log .= 'send:'.$str;

	$result = get_smtp_result($smtp,$msg,$log);
	$str = "QUIT\r\n";
	fwrite($smtp,$str);
	$log .= 'send:'.$str;
	get_smtp_result($smtp,$null,$log);
	fclose($smtp);
	$msg = $result ? trim($msg) : 'Timeout';

	$returncode = $result;

	if ($result == 554) {
		mail_log('554 '.$msg);
	}

	return 	$result === false
	?	null
	:	(	($result >= 200 && $result < 300)
		?	true
		:	(	($result >= 500 && $result < 600)
			&&	false === stripos($msg,'authentication required')
			?	false
			:	null
			)
		);
}
function log_checkmail($email,$log,$rc) {
	require_once '_db.inc';
	db_insert('emailcheck_log','
	INSERT INTO emailcheck_log SET
		EMAIL	="'.addslashes($email).'",
		LOG		="'.addslashes($log).'",
		RC		="'.($rc === null ? 'null' : ($rc === true ? 'true' : ($rc === false ? 'false' : ''))).'",
		STAMP	='.CURRENTSTAMP
	);
}
