<?php

declare(strict_types=1);

function cache_video_thumbs(
	int			$videoid,
	string		$type,
	int|string 	$externalid,
	?string		$url		= null,
	bool		$force		= false,
	?array		$thumbnails = null,
): bool {
	return match ($type) {
		'vimeo'		=> cache_vimeo_thumbs  ($videoid, (int)$externalid, $url, $force),
		'youtube'	=> cache_youtube_thumbs($videoid, $externalid, $thumbnails, $force),
		default		=> false,
	};
}

function cache_vimeo_thumbs(
	int		$videoid,
	int		$externalid,
	?string	$url	= null,
	bool	$force	= false
): bool {
	if (!$url
	&&	(!($data = safe_file_get_contents("https://vimeo.com/api/v2/video/$externalid.php", info: $info))
		|| $info['http_code'] !== 200
		|| !($info = unserialize($data, ['allowed_classes' => false]))
		|| !($video = $info[0] ?? null)
		|| !($url = $video['thumbnail_large'] ?? null))
	) {
		return false;
	}
	return store_thumb_from_url($videoid, 0, $url, force: $force);
}
function cache_youtube_thumbs(
	int		$videoid,
	string	$youtubeid,
	?array	$thumbnails	= null,
	bool	$force		= false
): bool {
	$ok = true;
	$thumbnails ??= [];
	foreach ([
		'default',
		'mqdefault',
		'hqdefault',
		'sddefault',
		'maxresdefault',
		'hq720_3',
		'hq720_2',
		'hq720_1',
		'hq720',
	] as $i => $type) {
		if (!isset($thumbnails[$type])) {
			# thumbnail does not exist
			continue;
		}
		if (!store_thumb_from_url(
			$videoid,
			$i,
			"https://i.ytimg.com/vi/$youtubeid/$type.jpg",
			$force)
		) {
			# continue to try to store the other thumbs
			$ok = false;
		}
	}
	return $ok;
}

function store_thumb_from_url(int $videoid, int $i, string $url, bool $force): bool {
	if (!$force
	&&	(	($have = db_single('videothumbmeta', "
			SELECT 1
			FROM videothumbmeta
			WHERE VIDEOID = $videoid
			  AND ID = $i
			  AND AVAILABLE = 1"))
		||	$have === false
		)
	) {
		return (bool)$have;
	}

	static $__unlink   = false;
	static $__tmp_name = null;

	$__tmp_name ??= uniqid("/tmpdisk/video.$videoid.thumb.".getmypid().'.', true);

	if (CLI) {
		syslog(LOG_INFO, 'fetching video thumbnail from '.$url);
	}

	if (!($thumb_data = safe_file_get_contents($url, [CURLOPT_HTTPHEADER => [ACCEPT_HEADER_FOR_IMAGE]], $info))
	||	 $info['http_code'] !== 200
	) {
		if (CLI) {
			syslog(LOG_WARNING, 'no thumbnail found');
		} else {
			register_warning('video:warning:no_thumb_LINE', [
				'VIDEOID'	=> $videoid,
				'I'			=> $i,
				'URL'		=> $url,
				'BASENAME'	=> basename($url, '.jpg'),
			]);
		}

		if (false === db_single('videothumbmeta', "
			SELECT b'1'
			FROM videothumbmeta
			WHERE VIDEOID = $videoid
			  AND ID = $i")
		&&	!query_failed()
		&&	'active' === db_single('video', "
			SELECT STATUS
			FROM video
			WHERE VIDEOID = $videoid"
		)) {
			mail_log("request for video thumb $videoid:$i which does not exist, but video is active!\n\nhttps://partyflock.nl/video/$videoid");
		}
		return false;
	}
	if (!file_put_contents($__tmp_name, $thumb_data)) {
		return false;
	}
	if (!$__unlink) {
		register_shutdown_function('unlink', $__tmp_name);
		$__unlink = true;
	}
	if (!($image_info = getimagesize($__tmp_name))) {
		mail_log(
			"getimagesize failed for video $videoid:$i\n\n".
			'file size: '.filesize($__tmp_name)."\n".
			'thumb data size: '.strlen($thumb_data)."\n".
			"thumb should have been @ $url"
		);
		return false;
	}
	switch ($image_info['mime']) {
	case 'image/jpeg':
		# ok!
		break;

	case 'image/png':
		# happens almost never, but support it using simple gd library conversion
		if (!($img = imagecreatefromstring($thumb_data))) {
			return false;
		}
		ob_start();
		imagejpeg($img, null, 92);
		if (!($thumb_data = ob_get_clean())) {
			error_log('empty image after conversion for video '.$videoid);
			return false;
		}
		break;

	default:
		mail_log("unsupported image format {$image_info['mime']} for url $url and video $videoid");
		return false;
	}

	if (($have_already = db_single('videothumbdata', "
		SELECT 1
		FROM data_db.videothumbdata
		WHERE VIDEOID = $videoid
		  AND LENGTH(DATA) = ".strlen($thumb_data)."
		  AND DATA = '".addslashes($thumb_data)."'"))
	||	$have_already === false
	) {
		return (bool)$have_already;
	}
	$width	= $image_info[0] ?? 0;
	$height	= $image_info[1] ?? 0;
	$aspect = $height ? ($width / $height) : 0;
	# Don't store new aspects if the difference might even be due to floats being not exact
	# $min_aspect = $aspect - .01;
	# $max_aspect = $aspect + .01;
	$size = strlen($thumb_data);

	return db_replace('videothumbmeta', "
		REPLACE INTO videothumbmeta SET
			VIDEOID	  = $videoid,
			ID		  = $i,
			WIDTH	  = $width,
			HEIGHT	  = $height,
			ASPECT	  = $aspect,
			SIZE	  = $size,
			CSTAMP	  = ".CURRENTSTAMP.',
			AVAILABLE = 1')
	&&	db_replace('videothumbdata','
		REPLACE INTO data_db.videothumbdata SET
			DATA	= "'.addslashes($thumb_data)."\",
			VIDEOID	= $videoid,
			ID		= $i");
}
