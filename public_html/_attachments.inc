<?php

const FORCE_NON_INLINE_IF_BIGGER = 100000;

function show_attachment_options(bool $include_table = true, ?array $template = null): void {
	if (NO_ALTER_ATTACHMENT) {
		return;
	}

	include_js('js/form/attachments');
/*	?><input type="hidden" name="MAX_FILE_SIZE" value="<?= 32 * 1024 * 1024 ?>" /><?*/
	if ($include_table) {
		?><table class="nomargin hla"><?
	}

	if ($implicitatt = getifset($template,'ATTACHMENT')) {
		layout_start_row();
		echo Eelement_name('attachment');
		layout_field_value();
		echo escape_specials($implicitatt);
		?><input type="hidden" name="INCLUDE_ATTACHMENT" value="invoice:<?= $template['ID'] ?>" /><?
		?><span class="unhideanchor" style="padding-left:1em" onclick="changerow(this,'remove')"><?= __('action:remove') ?></span><?
		layout_stop_row();
	}

	?><tbody id="baseatt" class="hidden"><?
	layout_start_row(ROW_HIDDEN);
	echo Eelement_name('attachment');
	layout_field_value();
	?><input type="file" class="autow" name="ATTACHMENT[]" onchange="changeAttachment(this)" /><?
	?><span class="unhideanchor" style="padding-left:1em" onclick="removeAttachment(this)"><?= get_remove_char() ?></span><?
	layout_stop_row();
	?></tbody><?
	if ($include_table) {
		?></table><?
	}
}

function verify_attachments() {
	if (NO_ALTER_ATTACHMENT) {
		register_error('attachments:error:not_at_the_moment_LINE');
		return false;
	}
	if (!isset($_POST['ATTACHMENT_C'])) {
		return true;
	}
	$atts = get_attachments();
	if (!$atts) {
		register_error('attachments:error:no_attachments_LINE');
		return false;
	}
	foreach ($atts as $att) {
		if (!require_file($att)) {
			$bad = true;
			$name = empty($att['name']) ? null : $att['name'];
			register_error('attachments:error:bad_attachment_LINE',$name ? array('NAME'=>$name) : null);
		} elseif ($att['size'] > 32*1024*1024) {
			$bad = true;
			register_error('attachments:error:too_large_LINE',array('NAME'=>$att['name'],'MAX'=>32*1024*1024));
		}
	}
	return isset($bad) ? false : true;
}
function get_attachments() {
	static $__atts = 0;
	if ($__atts !== 0 && !isset($_POST['reinitattachments'])) {
		return $__atts;
	}
	unset($_POST['reinitattachments']);
	$__atts = array();
	if (isset($_POST['INCLUDE_ATTACHMENT']) && is_scalar($_POST['INCLUDE_ATTACHMENT'])) {
		list($element,$id) = explode(':',$_POST['INCLUDE_ATTACHMENT']);
		switch ($element) {
		case 'invoice':
			if (!CLI && !have_admin('invoice')) {
				return false;
			}
			$invoicedata = db_single('invoicedata','SELECT DATA FROM data_db.invoicedata WHERE INVOICENR='.$id);
			if (!$invoicedata) {
				return false;
			}
			$__atts[] = array(
				'name'		=> 'partyflock_invoice_'.$id.'.pdf',
				'type'		=> 'application/pdf',
				'tmp_data'	=> $invoicedata ?: false,
				'error'		=> $invoicedata ? 0 : UPLOAD_ERR_EXTENSION,
				'size'		=> strlen($invoicedata)
			);
		}
	}
	if (!isset($_FILES['ATTACHMENT'])
	||	!isset($_POST['ATTACHMENT_C'])
	) {
		return $__atts;
	}
	$atts = $_FILES['ATTACHMENT'];
	foreach ($atts['name'] as $i => $name) {
		if (!$name) continue;
		$__atts[] = array(
			'name'		=> $name,
			'type'		=> $atts['type'][$i],
			'tmp_name'	=> $atts['tmp_name'][$i],
			'error'		=> $atts['error'][$i],
			'size'		=> $atts['size'][$i]
		);
	}
	return $__atts;
}
