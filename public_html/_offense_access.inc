<?php

function is_offense_element($element = null) {
	static $__elements = array(
		'albummap'		=> true,
		'albumelement'		=> true,
		'answeringmachine'	=> true,
		'chat_message'		=> true,
		'column'		=> true,
		'contact_ticket'	=> true,
		'directmessage'		=> true,
		'flock'			=> true,
		'flockmessage'		=> true,
		'flocktopic'		=> true,
		'message'		=> true,
		'poll'			=> true,
		'report'		=> true,
		'topic'			=> true,
		'user'			=> true,
		'user_text'		=> true,
		'valentine'		=> true,
		'weblog'		=> true,

		'nick'			=> true,
		'profile'		=> true,
		'profile_image'		=> true,
		'profile_image_caption'	=> true,
	);
	return	$element
	?	(	isset($__elements[$element])
		||	str_contains($element,'_comment')
		||	str_contains($element,'_rating')
		)
	:	$__elements;
}
function may_create_offense($element,$id) {
	if (!is_offense_element($element)) {
		return false;
	}
	require_once '_element_access.inc';
	return may_change_element($element,$id);
}
function have_offense_admin() {
	static $__offense_admin = null;
	if ($__offense_admin !== null) {
		return $__offense_admin;
	}
	if (!have_admin()) {
		return $__offense_admin = false;
	}
	if (have_admin(array('offense','helpdesk','helpdesksuper'))
	||	have_forum_admin()
	) {
		return $__offense_admin = true;
	}
	if (have_admin(array_keys(is_offense_element()))) {
		return true;
	}
	global $currentuser;
	foreach ($currentuser->rights as $right => $xx) {
		if (!str_contains($right, '_comment')
		||	!str_contains($right, '_rating')
		) {
			return $__offense_admin = true;
		}
	}
	return $__offense_admin = false;
}
function require_offense_admin() {
	if (!have_offense_admin()) {
		register_error('require:offense_admin:need_offense_rights_LINE');
		return false;
	}
	return true;
}
