<?php

function element_changed(
	?string			$element = null,
	 int|array|null $id_arg  = null,
	?int			$userid  = null,
): bool {
	# element_changed also does:
	#	- page_changed
	#	- sphinx_update_element

	$element ??= $_REQUEST['sELEMENT'];

	if (!$id_arg) {
		$ids = [$_REQUEST['sID']];
	} elseif (is_array($id_arg)) {
		$ids = $id_arg;
	} else {
		$ids = [$id_arg];
	}
	if (!$ids) {
		mail_log('element_changed called without ids');
		return false;
	}
	require_once '_pagechanged.inc';
	if (!page_changed($element, $ids)) {
		return false;
	}
	require_once '_sphinx.inc';
	if (!sphinx_update_element($element, $ids)) {
		return false;
	}
	if (!db_insert('elementchanged_log','
		INSERT INTO elementchanged_log
		SELECT * FROM elementchanged
		WHERE ELEMENT = "'.$element.'"
		  AND ID IN ('.implode(', ', $ids).')')
	) {
		return false;
	}
	$userid ??= CURRENTUSERID;
	$replace_list = [];
	foreach ($ids as $id) {
		$replace_list[] = '("'.$element.'", '.$id.', '.CURRENTSTAMP.', '.$userid.')';
	}
	return db_insert('elementchanged','
		REPLACE INTO elementchanged (ELEMENT, ID, MSTAMP, MUSERID)
		VALUES '.implode(', ', $replace_list)
	);
}
