<?php

function show_organization_options($selected = 0,$skip = 0) {
/*	static $__orgstr = null;
	if (isset($__orgstr)) {
		echo $selected ? str_replace('option value="'.$selected.'"','option selected="selected" value="'.$selected.'"',$__orgstr) : $__orgstr;
		return;
	}*/
	static $__orgs = 0;
	if ($__orgs === 0) {
		$__orgs = memcached_rowuse_array_if_not_admin(
			'organization',	// not admin
			'organization',	// tables
			'SELECT ORGANIZATIONID,NAME,DEADSTAMP FROM organization ORDER BY NAME ASC'
		);
		if (!$__orgs) {
			return;
		}
	}
	ob_start();
	foreach ($__orgs as $organization) {
		?><option<?
		if ($skip
		&&	$skip == $organization['ORGANIZATIONID']
		) {
			?> disabled="disabled"<?
		}
		if ($selected
		&&	$selected == $organization['ORGANIZATIONID']
		) {
			?> selected="selected"<?
		}
		?> value="<?= $organization['ORGANIZATIONID']; ?>"<?
		?>><?
		echo escape_utf8($organization['NAME']);
		show_dead_option($organization);
		?></option><?
	}
	$__orgstr = ob_get_clean();
	echo $__orgstr;
	# caching break selected
#	echo $selected ? str_replace('option value="'.$selected.'"','option selected="selected" value="'.$selected.'"',$__orgstr) : $__orgstr;
}
