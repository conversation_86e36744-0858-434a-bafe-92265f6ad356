<?php

function show_offense(array|int $offense, bool $force = false): void {
	if (is_array($offense)) {
		$offenseid = $offense['OFFENSEID'];
	} else {
		$offenseid = $offense;
		$offense = db_single_assoc(['offense', 'banpoint',' offense_reference_element'], '
			SELECT b.*, o.*, ore.ID AS ALTID
			FROM offense AS o
			LEFT JOIN banpoint AS b USING (OFFENSEID)
			LEFT JOIN offense_reference_element AS ore USING (OFFENSEID)
			WHERE OFFENSEID = '.$offenseid
		);
		if (!$offense) {
			if ($offense !== false) {
				register_error('offense:error:nonexistent_LINE', ['ID' => $offenseid]);
			}
			return;
		}
	}
	require_once '_offense_access.inc';
	$is_admin = have_offense_admin();
	if (!$force) {
		if (!$is_admin
		&&	CURRENTUSERID !== $offense['USERID']
		) {
			return;
		}
	}
	$element = $offense['ELEMENT'];
	$warning = !$offense['REGONLY'] && ($offense['POINTS'] === null || $offense['POINTS']);

	layout_open_box($warning ? 'offense' : 'white');
	layout_open_box_header();
	echo Eelement_name($warning ? 'warning' : 'notice');
	layout_continue_box_header();
	layout_display_contacts('offense', $offenseid, $offense['BY_USERID']);
	layout_close_box_header();
	
	if ($offense['REMOVED']) {
		?><div class="block"><?= __('offense:info:this_offense_is_revoked_LINE') ?></div><?
	}

	?><div class="block"><?
	switch ($offense['TYPE']) {
	case 'spam_using_other':
	case 'spam':
		$key = 'offense:info:spam_warning_TEXT';
		break;
	case 'spam_other':
		$key = 'offense:info:'.$offense['TYPE'].'_'.($element === 'directmessage' ? 'private' : 'public').'_warning_TEXT';
		break;
	case 'spam_ad':
		$key = 'offense:info:'.$offense['TYPE'].'_warning_TEXT';
		break;
	default:
		$key = 'offense:info:top_warning_TEXT';
		break;
	}
	echo __($key,DO_NL2BR | DO_UBB | DO_CONDITIONAL);
	?></div><?

	$altid = $offense['ALTID'];

	$list = new deflist('deflist vtop');

	$list->add_row(
		$offense['POINTS'] === null || $offense['POINTS']
		?	Eelement_name('offender')
		:	Eelement_name('user'),
		get_element_link('user',$offense['USERID'])
	);

	if ($offense['REGONLY']) {
		$list->add_row(__C('field:register_only'), __('answer:yes'));
	}
	$list->add_row(Eelement_name('date'), _datedaytime_get($offense['CSTAMP']));
	$list->add_row(Eelement_name('element'), element_name($element));

	if ($is_admin
	||	$element !== 'directmessage'
	&&	$element !== 'valentine'
	) {
		$list->add_row(Eelement_name('id'), $offense['ID']);
	}
	
	ob_start();
	if (isset(USE_URLTITLE[$element])) {
		echo get_element_link($element,$offense['ID']);
	} else switch ($element) {
	default:
		require_once '_element.inc';
		list($link,$content) = _element_display_link($element,$offense['ID']);
		if ($link) {
			$link = escape_specials($link);
			?><a href="<?= $link ?>">https://<?= $_SERVER['HTTP_HOST'],$link ?></a><?
		}
		break;
	case 'valentine':
	case 'chat_message':
	case 'directmessage':
	case 'nick':
	case 'profile':
	case 'profile_image':
	case 'profile_image_caption':
		# no link possible, ignore
		break;
	}
	if ($link = ob_get_clean()) {
		$list->add_row(Eelement_name('link'),'&rarr; '.$link);
	}

	$list->add_row(Eelement_name('type'), __('offense:type:'.$offense['TYPE']));
	
	switch ($offense['TYPE']) {
	case 'copyright_infringement':
	case 'portraitright_infringement':
		$list->add_row(Eelement_name('action'),__('offense:extra:only_visible_to_you'));
		break;
	}

	if ($offense['EXTRA']
	&&	(	!$altid
		||	!preg_match('"^https?://partyflock\.nl/'.$element.'/'.$altid.'(:[^\n]*)?$"i',$offense['EXTRA'])
		)
	) {
		$list->add_row(Eelement_name('explanation'), make_all_html($offense['EXTRA']));
	}
	if ($offense['PROHIBID']) {
		ob_start();
		if (have_admin('prohib')) {
			?><a href="/prohib/<?= $offense['PROHIBID']; ?>/form"><?= $offense['PROHIBID'] ?></a><?
		} else {
			echo $offense['PROHIBID'];
		}
		$list->add_row(Eelement_name('prohib'), ob_get_clean());
	}
	if ( $offense['POINTS']
	&&	!$offense['REMOVED']
	&&	!$offense['REGONLY']
	) {
		$list->add_row(Eelement_plural_name('penalty_point'), $offense['POINTS']);
	}
	if (!$offense['REMOVED']) {
		ob_start();
		if ($offense['POINTS'] === null) {
			echo __('answer:yes');
			require_once '_status.inc';
			?>, <span class="banned"><?= status_name('permbanned'); ?></span><?
		} elseif ($offense['DELTA']) {
			echo __('offense:info:ban_increased',['DAYS' => round($offense['DELTA'] / 24 / 3600,2)]);
		} else {
			echo __('answer:no');
		}
		$list->add_row(Eelement_name('ban'), ob_get_clean());
	}
	if ($element === 'chat_message') {
		$duration = db_single('chatban','SELECT EXPIRES-CSTAMP FROM chatban WHERE OFFENSEID='.$offenseid);
		$list->add_row(Eelement_name('chatban'),
			$duration
		?	__('duration:x_minutes',array('MINUTES'=>$duration/60))
		:	__('answer:no')
		);
	}
	if ($is_admin) {
		$list->set_row_class('clean');
		$list->add_row('&nbsp;');
		
		if ((	str_starts_with($offense['TYPE'], 'spam')
			||	str_starts_with($offense['TYPE'], 'drugs')
			)
		&&	$element === 'directmessage'
		) {
			if ($otherids = db_simple_hash(['offense_reference_offense', 'offense'], '
				SELECT BASE,OFFENSEID,USERID
				FROM	(SELECT 0 AS BASE, OFFENSEID		   FROM offense_reference_offense WHERE BASEID = '.$offenseid.'
				UNION	 SELECT 1 AS BASE, BASEID AS OFFENSEID FROM offense_reference_offense WHERE OFFENSEID = '.$offenseid.') AS infos
				JOIN offense USING (OFFENSEID)')
			) {
				foreach ($otherids as $base => $info) {
					ob_start();
					$first = true;
					foreach ($info as $tmpoffenseid => $tmpuserid) {
						if ($first) {
							$first = false;
						} else {
							?>, <?
						}
						?><a href="/offense/<?= $tmpoffenseid ?>"><?= escape_specials(memcached_nick($tmpuserid)) ?></a><?
					}
					$list->add_row($base ? Eelement_name('base') : Eelement_plural_name('other(elements)'), ob_get_clean());
				}
			}
			if ($reminfo = db_single_assoc('spamremoved', '
				SELECT CNT, MATCHED_CNT, DELAYED_CNT, MATCHED_DELAYED_CNT
				FROM spamremoved
				WHERE OFFENSEID = '.$offenseid)
			) {
				$totalmatched = $reminfo['MATCHED_CNT'] + $reminfo['MATCHED_DELAYED_CNT'];
				$totaldeleted = $reminfo['CNT'] + $reminfo['DELAYED_CNT'];
				if ($totalmatched) {
					$byme = db_single(
						['directmessage', 'directmessage_delayed', 'spamremoved_directmessage', 'spamremoved_directmessage_delayed'], '
						SELECT	(	SELECT COUNT(*)
								FROM spamremoved_directmessage
								JOIN directmessage USING (MESSAGEID)
								WHERE FROM_USERID = '.$offense['USERID'].'
								  AND OFFENSEID = '.$offenseid.')
						+	(	SELECT COUNT(*)
								FROM spamremoved_directmessage_delayed
								JOIN directmessage_delayed USING (DELAYEDID)
								WHERE FROM_USERID = '.$offense['USERID'].'
								  AND OFFENSEID = '.$offenseid.')'
					);
					ob_start();
					if ($byme !== $totalmatched) {
						echo $byme,' / ';
					}
					echo $totalmatched;
					$list->add_row(__C('status:matched'), ob_get_clean());
				}
				if ($totaldeleted) {
					$list->add_row(__C('status:deleted'), $totaldeleted);
				}
			}
		}
		$list->add_row(Eelement_name('officer'),
			$offense['BY_USERID'] === 0
		?	element_name('system')
		:	get_element_link('user', $offense['BY_USERID'])
		);
		if ($offense['MUSERID']
		&&	$offense['MUSERID'] !== $offense['BY_USERID']
		) {
			$list->add_row(Eelement_name('last_change'), _datetime_get($offense['MSTAMP']));
			
			$list->add_row(__C('field:changed_by'), get_element_link('user',$offense['MUSERID']));
		}
	}
	
	if ($altid
	||	$offense['TYPE'] === 'double'
	&&	(	$element === 'topic'
		||	$element === 'flocktopic'
		||	$element === 'flock'
		)
	&&	($altid = db_single($element,'SELECT ALTID FROM '.$element.' WHERE '.$element.'ID='.$offense['ID']))
	) {
		$list->add_row(Eelement_name($element === 'user' ? 'reference' : 'alternative'), get_element_link($element,$altid));
	}
	$list->display();

	ob_start();
	switch ($element) {
	case 'albumelement':
	case 'albummap':
		$imgs = db_rowuse_array(['albumelement_reference', 'image_reference_meta'], '
			SELECT iref.DATAID, WIDTH, HEIGHT, FILETYPE
			FROM albumelement_reference AS aref
			JOIN image_reference_meta AS iref ON aref.THMBID = iref.DATAID
			WHERE aref.OFFENSEID = '.$offenseid
		);
		break;
	case 'profile_image':
		$imgs = db_rowuse_array(['userimage_reference', 'image_reference_meta'],'
			SELECT DATAID, LARGE, WIDTH, HEIGHT, FILETYPE
			FROM userimage_reference
			JOIN image_reference_meta AS iref USING (DATAID)
			WHERE iref.OFFENSEID = '.$offenseid
		);
		break;
	}
	if (!empty($imgs)) foreach ($imgs as $image) {
		?><img src="/images/reference/<?= $image['DATAID'] ?>.<?= $image['FILETYPE'] ?>" width="<?= $image['WIDTH'] ?>" height="<?= $image['HEIGHT'] ?>" /><?
	}
	if (($logs = db_rowuse_array('offense_element_log', 'SELECT * FROM offense_element_log WHERE OFFENSEID='.$offenseid))) {
		require_once '_offense_log_element.inc';
		show_offense_log_elements($logs, $offense);
	}
	if ($data = ob_get_clean()) {
		require_once 'defines/offense.inc';
		layout_box_header(Eelement_name('original_content'));
		?><div class="block"><?= __('offense:info:original_content_TEXT',DO_NL2BR) ?></div><?
		if ($offenseid <= OFFENSE_ORIGINAL_CONTENT_VALID) {
			?><div class="block"><?= __('offense:info:generated_original_content_TEXT',DO_NL2BR) ?></div><?
		}
		?><div class="block"><?= $data ?></div><?
	}
	layout_close_box();
}
