<?php

declare(strict_types=1);

header('Content-Type: text/plain; charset=ascii');

require_once '_servertype.inc';
require_once 'defines/cache.inc';

const ALLOWED_KEYS = [CACHE_HASHES, CACHE_ALL];
const ALLOWED_IPS  = ['127.0.0.1', '::1'];

if (!in_array($_SERVER['REMOTE_ADDR'], ALLOWED_IPS, true)) {
	error('you are not allowed to do this');
	http_response_code(403);
	exit;
}
if (empty($_SERVER['eKEY'])
||	!in_array($_SERVER['eKEY'], ALLOWED_KEYS, true)
) {
	error('missing or invalid KEY to flush');
	http_response_code(400);
	exit;
}
if ($_SERVER['eKEY'] === CACHE_ALL) {
	apcu_clear_cache();
	exit;
}
$full_cache_key = HASHES_VERSION.':'.$_SERVER['eKEY'];
if (!apcu_exists($full_cache_key)) {
	# Currently nothing stored at this key
	exit;
}
if (!apcu_delete($full_cache_key)) {
	error("failed to flush {$_SERVER['eKEY']}");
	http_response_code(503);
	exit;
}

function error(string $message): void {
	echo "$message\n";
	error_log($message);
}
