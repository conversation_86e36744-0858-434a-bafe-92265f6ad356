<?php

require_once '_date.inc';
require_once '_gallery.inc';
require_once '_layout.inc';

function show_location_archive($location,$as_org = false) {
	require_once '_partylist.inc';
	$partylist = new _partylist;
	if (ROBOT) {
		$partylist->show_vevent = true;
	}
	$partylist->show_stars = true;
	$partylist->hide_flag = true;
	$partylist->show_date = true;
	$partylist->show_camera = true;
	$partylist->show_buddy_hearts = true;
	$partylist->hide_separators = true;
	$partylist->show_people = true;

	if ($as_org) {
		$partylist->as_locasorg($location['LOCATIONID']);
		$partylist->show_location = true;
	} else {
		$partylist->on_location_including_livestreams($location['LOCATIONID']);
		$partylist->select_past();
	}
	if (!have_admin()) {
		$partylist->not_cancelled_nor_moved();
	}
	$partylist->order_reverse_chronologically();
	if ($partylist->query()) {
		layout_open_box('location',$as_org ? 'orchive' : 'archive');
		layout_box_header(escape_utf8($location['NAME']).' '.MIDDLE_DOT_ENTITY.' '.($as_org ? __('header:as_organization') : __('header:past_parties',array('NAME'=>$location['NAME']))));
		$partylist->display();
		layout_close_box();
	}
}
