<?php

function preamble(): void {
	if (!have_admin()) {
		return;
	}

	if ($ipnum = have_idnumber($_REQUEST,'sID')) {
		$ipstr = long2ip($ipnum);
		header('Location: https://'.$_SERVER['HTTP_HOST'].'/ip/'.urlencode($ipstr),true,301);
		exit;
	}

	robot_action('noindex');
}

function get_title_parts(): array {
	return [
		element_name('ip'),
		have_something($_REQUEST,'IPSTR') ? escape_specials($_REQUEST['IPSTR']) : ''
	];
}

function perform_commit(): ?bool {
	if (!have_admin()) {
		no_permission();
		return false;
	}
	switch ($_REQUEST['ACTION']) {
	default:
		not_found();
		return null;
	case null:
		if (!empty($_POST)) {
			require_once '_disallowcommit.inc';
			return	isset($_POST['REMOVE'])
			?	disallowuser_remove()
			:	disallowuser_commit();
		}
	}
	return null;
}

function show_info(string $ipbin, string $ipstr, int $masksize, string $netstr): void {
	require_once 'defines/disallow.inc';
	layout_open_box('white');
	layout_open_box_header();
	# NOTE: escaping of names is done in memcached_netname_from_netstr
	if (!$netstr) {
		?><div style="margin-botton: .4em"><?
		?><span class="nb">ip:</span> <a href="/ip/<?= $ipstr ?>"><?= $ipstr ?></a><?
		if (($name = memcached_hostname_from_ipbin($ipbin,$ipstr))
		&&	 $name !== $ipstr
		) {
			?><br /><span class="nb">hostname:</span> <? echo $name;
		}
		?></div><?
		?><div><?
		[$netstr, $masksize] = get_netip_and_masksize($ipstr);
		if (($name = memcached_netname_from_netstr($netstr))
		&&	$name !== $ipstr
		&&	$name !== $netstr
		) {
			echo str_replace(", \n", '<br />', $name);
		}
		?></div><?
	} else {
		?><a href="/ip/<?= $netstr ?>"><?= $netstr ?></a><?
		if ($name = memcached_netname_from_netstr($ipstr)) {
			?><br /><? echo str_replace(", \n", '<br />', $name);
		}
	}
	layout_close_box_header();

	if (have_super_admin()) {
		$checked = db_single('agenda_allowed','SELECT 1 FROM agenda_allowed WHERE IPBIN="'.addslashes($ipbin).'"');
		?><div class="block"><label class="<?
		if (!$checked) {
			?>not-<?
		}
		?>hilited-green"><?
		show_input([
			'type'		=> 'checkbox',
			'class'		=> 'upLite',
			'checked'	=> $checked,
			'onclick'	=> "do_inline(
				'POST',
				'/allowagenda.act',
				'ACT',
				function(req) {
					if(req.status !== 200) {
						this.checked == !this.checked
					}
				},
				(this.checked ? '' : 'REMOVE=1&') + 'IPSTR=".$ipstr."'
			)",
		]);
		?> <?= __('action:always_allow_agenda') ?></div><?

		$checked = db_single('login_hits_ok_ip','SELECT 1 FROM login_hits_ok_ip WHERE IPBIN = "'.addslashes($ipbin).'"');
		?><div class="block"><label class="<?
		if (!$checked) {
			?>not-<?
		}
		?>hilited-green"><?
		show_input([
			'type'		=> 'checkbox',
			'class'		=> 'upLite',
			'checked'	=> $checked,
			'onclick'	=> "do_inline(
				'POST',
				'/loginhitsokip.act',
				'ACT',
				function(req) {
					if(req.status !== 200) {
						this.checked == !this.checked
					}
				},
				(this.checked ? '' : 'REMOVE=1&') + 'IPSTR=".$ipstr."'
			)",
		]);
		?> <?= __('action:login_hits_ok_ip') ?></div><?
	}

	if (is_server_ip(ipstr: $ipstr)) {
		?><div class="warning block">SERVER-SPACE</div><?
	}

	?><pre onclick="this.select()"><?
	?>-A INPUT -s <?= $ipstr ?>/32 -m comment --comment "botnet IP trying logins" -j DROP<?
	?></pre><?

	$superhell = have_admin('helpdesksuper');
	$disallowadmin = have_admin('disallowuser');
	if ($superhell || $disallowadmin) {
		require_once '_disallowuser.inc';
		[$prefix, $iident] = ipbin_to_prefix_and_iident($ipbin);
		$info = [];
		if ($disallow = disallow_user($ipbin, $info)) {
			[,	$stamp,
				$dis_masksize,
				$dis_prefix,
				$dis_iident,
				$dis_netstr
			] = $info;

			$other =
				$dis_masksize !== $masksize
			||	$dis_prefix   !== $prefix
			||	$dis_iident	  !== $iident;

			if (false === ($disallow = db_single_assoc('disallowuser', "
				SELECT *, 0 + DISALLOW AS ID
				FROM disallowuser
				WHERE PREFIX = 0x$dis_prefix
				  AND IIDENT = 0x$dis_iident
				  AND MASKSIZE = $dis_masksize"
			))) {
				return;
			}
			if ($disallow) {
				?><div class="<?
				echo match($disallow['ID']) {
					DISALLOW_LOGIN		=> 'error',
					DISALLOW_PROXY,
					DISALLOW_CREATION	=> 'warning',
					DISALLOW_HARVEST,
					DISALLOW_TOR		=> 'tor',
				};
				?> block"><?
				echo __('ip:info:'.$disallow['DISALLOW'].'_disallowed_LINE');
				if ($other) {
					?><br ><? echo __('ip:info:disallowed_source_LINE',DO_UBB,[
						'NETSTR'	=> $dis_netstr,
						'MASKSIZE'	=> $dis_prefix !== 0xFFFF ? $dis_masksize : 0
					]);
				}
				if ($disallow['USERID']
				||	$disallow['REASON']
				||	$disallow['CSTAMP']
				) {
					?><br /><? echo __('ip:info:disallow_added_by_LINE' ,DO_UBB ,[
							'USERID' => $disallow['USERID'],
							'REASON' => $disallow['REASON'],
							'DATE'	 => _date_get($disallow['CSTAMP']),
							'TIME'	 => _time_get($disallow['CSTAMP'])
						]);
				}
				if ($disallow['LSTAMP']) {
					?><br /><? echo __('ip:info:disallow_use_stats_LINE',DO_UBB,[
							'DATE'	=> _date_get($disallow['LSTAMP']),
							'TIME'	=> _time_get($disallow['LSTAMP']),
							'HITS'	=> $disallow['HITS']
						]);
				}
				?></div><?
			}
		}
		if (empty($other) && $disallowadmin) {
			require_once 'defines/disallow.inc';
			require_once '_disallowcommit.inc';
			?><form method="post" action="/ip/<?= $ipstr ?>" onsubmit="return submitForm(this)"><?
			show_disallow_form($disallow, $ipbin);
			?></form><?
		}

		if ($history = db_rowuse_array('disallowuser_log', "
			SELECT CSTAMP, DSTAMP, REASON, DISALLOW, HITS
			FROM disallowuser_log
			WHERE PREFIX = 0x$prefix
			  AND IIDENT = 0x$iident
			  AND MASKSIZE = $masksize
			ORDER BY CSTAMP DESC"
		)) {
			layout_box_header(Eelement_name('history'));
			layout_open_table('default');
			foreach ($history as $disallow) {
				extract($disallow);
				layout_start_rrow_right(usercelldef: 'rpad');
				echo _date_display($CSTAMP, short: true);
				?>&nbsp;-<?

				layout_next_cell(class: 'rpad right');
				echo _date_display($DSTAMP, short: true)
				?>,<?

				layout_next_cell(class: 'rpad right');
				echo $HITS
				?> <?
				echo element_name('hit', $HITS);
				?>,<?

				layout_next_cell(class: 'rpad');
				echo __('action:disallow') ?> <? echo __('disallowuser:'.$DISALLOW);
				if ($REASON) {
					?>:<?
				}
				layout_next_cell();
				echo escape_specials($REASON);

				layout_stop_row();
			}
			layout_close_table();
		}
	}
	layout_close_box();
}

function show_whois(string $ipstr): void {
	require_once '_execute.inc';
	require_once '_ubb_preprocess.inc';
	layout_open_box('white');
	layout_box_header(__('ip:header:whois_information'));
	$starttime = time();
	[$rc, $stdout, $stderr] = execute('timeout '.(ROBOT ? 1 : 5).' whois '.$ipstr);
	if ($stderr) {
		if (!ROBOT) {
			error_log('whois took '.(time() - $starttime).' seconds on '.$_SERVER['REQUEST_URI'].' for '.$ipstr.' with '.$stderr);
		}
		?><div class="warning block"><?= nl2br(escape_specials($stderr)) ?></div><?
	}
	?><div class="block" id="whois"><?
	echo make_all_html(_ubb_preprocess($stdout), UBB_ALL & ~UBB_SMILEYS);
	?></div><?
	layout_close_box();
}

function display_body(): void {
	if (!require_admin()) {
		no_permission();
		return;
	}
	if (!require_something($_REQUEST, 'IPSTR')) {
		not_found();
		return;
	}
	$ipstr = $_REQUEST['IPSTR'];
	[$ipstr, $masksize, $net] = get_netip_and_masksize($netstr = $ipstr, true);
	if (!($ipbin = inet_pton($ipstr))) {
		not_found();
		return;
	}
	if (isset($_SERVER['eMASKSIZE'])) {
		$masksize = $_SERVER['eMASKSIZE'];
	}
	show_info($ipbin, $ipstr, $masksize, $net ? $netstr : false);

	if (have_admin('helpdesksuper')) {
		$max = 1000;
		if ($ipv4 = ipbin_is_ipv4($ipbin)) {
			$ipnum = ipbin_to_ipnum($ipbin);
			if ($net) {
				$rest = 32 - $masksize;
				$stopnum = $ipnum;
				for ($i = 0; $i < $rest; ++$i) {
					$stopnum |= 1 << $i;
				}
				$wherep = 'IPNUM BETWEEN '.$ipnum.' AND '.$stopnum;
			} else {
				$wherep = 'IPNUM='.$ipnum;
			}
			$iplistlist = db_rowuse_array('iplist','
				SELECT LAST_USERID AS USERID, LAST_USED'. ($net ? ', IPNUM' : '').'
				FROM iplist
				WHERE '.$wherep.'
				ORDER BY LAST_USED DESC
				LIMIT '.$max,
				DB_NON_ASSOC
			);
		} else {
			[$prefix, $iident] = ipbin_to_prefix_and_iident($ipbin, IP_PAD);
			if ($net) {
				if ($masksize === 64) {
					$wherep = 'PREFIX = 0x'.$prefix;
				} else {
					$total = $prefix.$iident;

					if ($masksize < 64) {
						$chars = $masksize / 4;

						$actual_prefix = substr($prefix, 0, $chars);

						$startfix = str_pad($actual_prefix, 16, '0');
						$stopfix  = str_pad($actual_prefix, 16, 'F');

						$wherep = 'PREFIX BETWEEN 0x'.$startfix.' AND 0x'.$stopfix;
					} else {
						$chars = ($masksize - 64) / 4;

						$actual_iident = substr($iident, 0, $chars);

						$startiident = str_pad($actual_iident, 16, '0');
						$stopiident  = str_pad($actual_iident, 16, 'F');

						$wherep = " PREFIX=0x$prefix
								AND IIDENT BETWEEN 0x$startiident AND 0x$stopiident";
					}
				}
			} else {
				$wherep = "
						PREFIX = 0x$prefix
					AND IIDENT = 0x$iident";
			}
			$iplistlist = db_rowuse_array('ip6list', "
				SELECT LAST_USERID AS USERID, LAST_USED".($net ? ', HEX(PREFIX), HEX(IIDENT)' : '')."
				FROM ip6list
				WHERE $wherep
				ORDER BY LAST_USED DESC
				LIMIT $max",
				DB_NON_ASSOC
			);
		}
		if ($iplistlist) {
			layout_open_box('white small');
			layout_box_header(__($net ? 'ip:header:users_with_similar_ip' : 'ip:header:users_with_identical_ip'));
			if (count($iplistlist) === 1000) {
				?><div class="block"><?= __('ip:info:only_last_x_most_recently_used_ips_are_show_LINE',['X'=>$max]) ?></div><?
			}
			layout_open_table('fw vtop default forcewrap hhla');
			layout_start_header_row();
			layout_start_header_cell();
			echo Eelement_name('nick');
			if ($net) {
				layout_next_header_cell(userdef: 'nowrap');
				echo Eelement_name('ip');
			}
			layout_next_header_cell();
			echo Eelement_name('status');
			layout_next_header_cell_right(userdef: 'nowrap');
			echo __C('field:last_used');
			layout_next_header_cell();
			echo Eelement_name('name');
			layout_next_header_cell();
			echo Eelement_name('residence');
			layout_stop_header_cell();
			layout_stop_header_row();

			require_once '_status.inc';
			foreach ($iplistlist as $ipelem) {
				[$userid, $last_used] = $ipelem;
				static $__userinfo;
				if (false === ($info = ($__userinfo[$userid] ??= db_single_assoc('user', "
						SELECT NICK, REALNAME, CITYID
						FROM user
						WHERE USERID = $userid")
					?:	false
				))) {
					continue;
				}
				$user = memcached_user($userid);

				// NICK
				layout_start_rrow();
				echo get_element_link('user',$userid);
				if ($net) {
					layout_next_cell(class: 'nowrap');
					if ($ipv4) {
						[,, $ipnum] = $ipelem;
						$ipstr = long2ip($ipnum);
						?><a href="/ip/<?= $ipstr ?>"><?= $ipstr ?></a><?
					} else {
						[,, $prefix, $iident] = $ipelem;
						$ipstr = prefix_and_iident_to_ipstr($prefix, $iident);
						?><a href="/ip/<?= $ipstr ?>"><?= $ipstr ?></a><?
					}
				}
				// STATUS
				layout_next_cell();
				if ($user['STATUS']) {
					echo status_name($user['STATUS']);
				}
				// LAST_USED
				layout_next_cell(class: 'nowrap right');
				_datetime_display($last_used, space: '&nbsp;', short: true);

				// REALNAME
				layout_next_cell();
				if (!empty($info['REALNAME'])) {
	 				echo escape_specials($info['REALNAME']);
				}
				// CITY
				layout_next_cell();
				if ($info['CITYID']) {
					echo get_element_link('city',$info['CITYID']);
				}
				layout_stop_row();
			}
			layout_close_table();
			layout_close_box();
		}
		show_ipcities($ipbin,$wherep);
	}
	show_whois($ipstr);
}

function show_ipcities(string $ipbin, string $wherep): void {
	if (ipbin_is_ipv4($ipbin)) {
		$ipnum = ipbin_to_ipnum($ipbin);
		if (!($ipcities =db_simple_hash('ipcity', "
			SELECT CITYID,SUM(HITS)
			FROM ipcity
			WHERE $wherep
			GROUP BY CITYID"
		))) {
			return;
		}
	} elseif (!($ipcities = db_simple_hash('ip6city', "
		SELECT CITYID, SUM(HITS)
		FROM ip6city
		WHERE $wherep
		GROUP BY CITYID"
	))) {
		return;
	}
	arsort($ipcities);
	layout_open_box('white');
	layout_box_header(Eelement_plural_name('city'));
	layout_open_table();
	foreach ($ipcities as $cityid => $hits) {
		layout_start_row(CELL_ALIGN_RIGHT);
		echo $hits;

		layout_value_field(SEPARATOR_MULTIPLIER);
		echo get_element_link('city', $cityid);
		layout_stop_row();
	}
	layout_close_table();
	layout_close_box();
}
