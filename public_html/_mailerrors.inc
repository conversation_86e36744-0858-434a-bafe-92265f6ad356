<?php

declare(strict_types=1);

function show_mail_errors(#[SensitiveParameter] string $email, bool $supervisor): ?bool {
	if (!($undeliverables = db_rowuse_array('contact_undeliverable','
		SELECT CIMID, CSTAMP, FAILREASON
		FROM contact_undeliverable
		WHERE FAILMAIL = "'.addslashes($email).'"
		ORDER BY CIMID DESC'))
	) {
		return $undeliverables;
	}
	$cnt = count($undeliverables);
	$uniq = uniqid('mailerrors_', true);
	layout_open_box('white');
	ob_start();
	expand_collapse($uniq);
	$clicker = ob_get_clean();
	$lasterror = end($undeliverables);
	$lasterror = $lasterror['CSTAMP'];
	if ($lasterror > CURRENTSTAMP - 20 * ONE_DAY) {
		if ($lasterror > CURRENTSTAMP - 7 * ONE_DAY) {
			$last_class = 'error';
		} else {
			$last_class = 'warning';
		}
	} else {
		$last_class = null;
	}
	?><div class="ptr" onclick="<? expand_collapse_clicker($uniq) ?>"><?
	layout_box_header(
		($supervisor ? _make_illegible($email).': ' : '').
		$cnt.' '.element_name('mail_problem',$cnt).', '.
		__('field:last').' '.
		($last_class ? '<span class="'.$last_class.'">' : '')._datetime_get($lasterror).
		($last_class ? '</span>' : ''),

		$clicker
	);
	?></div><?
	layout_open_table('hidden fw vtop default',$uniq);
	foreach ($undeliverables as $undeliverable) {
		show_mail_error_row($undeliverable,$supervisor);
	}
	layout_close_table();
	layout_close_box();
	return true;
}

function show_mail_error_row(array $undeliverable, bool $supervisor): void {
	/** @noinspection PhpRedundantOptionalArgumentInspection */
	extract($undeliverable, \EXTR_OVERWRITE);
	layout_start_rrow(0,null,'nowrap rpad right');
	?><b><? _date_printnice(PRINTNICE_DATE_TIME,$CSTAMP) ?></b>:<?
	if ($supervisor) {
		layout_next_cell(class: 'rpad');
		?><a href="/user/mailerror/<?= $CIMID ?>"><?
		?><img<?
		?> width="16" height="12"<?
		?> class="mail"<?
		?> src="<?= STATIC_HOST ?>/images/mail<?= is_high_res() ?>.png"<?
		?> alt="[FAILMAIL#<?= $CIMID ?>]" /></a><?
	}
	layout_next_cell();
	echo escape_specials(
		$supervisor
	?	$FAILREASON
	:	preg_replace(
			'"[a-zA-Z0-9_.-]+@[a-zA-Z0-9_.-]+\.[a-zA-Z]{2,4}"',
			__('<EMAIL>'),
			$FAILREASON
		)
	);
	layout_stop_row();
}
