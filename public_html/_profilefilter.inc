<?php

declare(strict_types=1);

function get_master_genres(): array {
	static $__master_genres = db_simple_hash('master_genre', 'SELECT MGID,NAME FROM master_genre ORDER BY NAME') ?: [];
	return $__master_genres;
}

function profilefilter_show_form(?array $item = null): bool {
	$element = $_REQUEST['sELEMENT'];
	$id		 = $_REQUEST['sID'];
	if (false === ($filters = load_profilefilters($element, $id))) {
		return false;
	}
	?><tr><td colspan="2"><hr class="slim" /></td></tr><?

	?><tbody id="filterheader"><?
	?><tr class="filter-header"><td></td><td class="header"><?

	include_js('js/choosecity');
	include_js('js/form/profilefilter');

 	?><span<?
 	?> data-next-ndx="<?= $filters ? max(array_keys($filters)) + 1 : 0 ?>"<?
 	?> class="unhideanchor"<?
 	?> onclick="Pf_addProfileFilter(this);"><?= Eelement_plural_name('filter') ?> (<?= element_name('profiling') ?>) <?=
 		get_special_char('add',['title'=>__C('action:add_filter')])
 	?></span><?
	?></td></tr><?
	?><tr><td colspan="2"><hr class="slim"></td></tr><?

	?><tr id="filter_enforcement" <?
	if (empty($filters)) {
		?> class="hidden"<?
	}
	?>><?
	?><td class="field"><?= Eelement_name('enforcement') ?></td><?
	?><td class="value"><?
	?><select name="FILTERS_ENFORCE"><?
	?><option value="soft"><?= __('attrib:best_effort') ?></option><?
	?><option<?
	if ($item && $item['FILTERS_ENFORCE'] === 'hard') {
		?> selected<?
	}
	?> value="hard"><?= __('attrib:guaranteed') ?></option><?
	?></select></td><?
	?></tr><?
	?></tbody><?
	foreach ($filters as $grp => $group) {
		show_filter_group($grp, $group);
	}
	return true;
}

function get_postfilter_part(): void {
	if (false === require_number($_POST, 'NDX')) {
		bail(400);
	}
	header('Content-Type: text/html; charset=win1252');
	show_filter_group($_POST['NDX']);
}

function show_filter_group(int $ndx, ?array $preset = null): void {
	require_once 'include.php';
	require_once '_countries.inc';
	require_once '_genrelist.inc';
	if (!($countries = get_nonempty_countries())) {
		bail(500);
	}
	?><tbody data-ndx="<?= $ndx ?>"><?
	?><tr><td colspan="2"><hr class="slim small light"></td></tr><?
	?><tr><?
	?><td class="field"><?= Eelement_plural_name('city') ?></td><?
	?><td class="value relative"><?
	echo get_close_char([
		'class'			=> 'large rtop',
		'style'			=> 'top: 1em; right: 1em;',
		'onmouseover'	=> /** @lang JavaScript */ "hiliteparent(this, 'TBODY', 'hilited-red')",
		'onmouseout'	=> /** @lang JavaScript */ "deliteparent(this, 'TBODY')",
		'onclick'		=> 'Pf.removeProfileFilter(this)',
	]);
	$postfix = "_$ndx";
	?><div id="citieslist<?= $postfix ?>"><?
	if (!empty($preset['city'])) {
		foreach ($preset['city'] as $cityid => $radius) {
			?><div id="city_<?= $cityid, $postfix ?>"><?
			?><input type="hidden" name="GRP[<?= $ndx ?>][CITY][<?= $cityid ?>]" value="<?= $radius ?>" /><?
			echo get_element_link('city', $cityid);
			if ($radius) {
				?> <?= WHITE_CIRCLE_ENTITY ?> <?= $radius ?> <? echo __('abbr:kilometer');
			}
			?> <?= MIDDLE_DOT_ENTITY ?> <?
			?><span class="small unhideanchor" onclick="Pf_filterRemoveCity(<?= $cityid ?>, '<?= $postfix ?>');"><?=
				HEAVY_MULTIPLICATION_X_ENTITY
			?></span><?
			?></div><?
		}
	}
	?></div><?

	?><select onchange="setdisplay('add<?= $postfix ?>', this.selectedIndex); changeCountry(this, 0, '', '<?= $postfix ?>');"><?
	?><option value="0"></option><?
	foreach ($countries as $countryid => $name) {
		?><option value="<?= $countryid ?>"><?= escape_specials($name) ?></option><?
	}
	?></select><br /> <?

	$_REQUEST['INCLUDE'] = 'cities';
	$_REQUEST['COUNTRYID'] = 0;
	$_REQUEST['SELECTED'] = 0;
	$_REQUEST['FLAGS'] = 0;

	include_something([
		'INCLUDE'	=> 'cities',
		'COUNTRYID'	=> 0,
		'SELECTED'	=> 0,
		'FLAGS'		=> 0,
		'POSTFIX'	=> $postfix,
	]);

	?><span id="add<?= $postfix ?>" class="hidden"><?
	?> <input type="number" data-valid="number" min="0" id="radius<?= $postfix ?>" class="right three_digits" /> <?= __('abbr:kilometer')
	?> <input onclick="Pf_filterAddCity('GRP[<?= $ndx ?>][CITY]', '<?= $postfix ?>');" type="button" name="ADD" value="<?= __('action:add') ?>" /><?
	?></span><?

	?></td><?
	?></tr><?

	?><tr><?
	?><td class="field"><?= __C('attrib:erotic') ?></td><?
	?><td class="value"><?
	$checked = isset($preset['erotic']);
	?><label class="basehl cbi <? if (!$checked) { ?>not-<? } ?>bold-hilited-red"><?
	show_input([
		'class'		=> 'upLite',
		'type'		=> 'checkbox',
		'name'		=> "GRP[$ndx][EROTIC]",
		'value'		=> '1',
		'checked'	=> $checked,
	]);
	?> <?= __('attrib:erotic') ?></label><?
	?></td><?
	?></tr><?

	?><tr><?
	?><td class="field"><?= Eelement_plural_name('genre') ?></td><?
	?><td class="value" style="columns: 10em;"><?
	foreach (get_master_genres() as $mgid => $name) {
		$checked = isset($preset['mastergenre'][$mgid]);
		?><label class="basehl cbi <? if (!$checked) { ?>not-<? } ?>bold-hilited"><?
		show_input([
			'class'		=> 'upLite',
			'type'		=> 'checkbox',
			'name'		=> "GRP[$ndx][MGID][]",
			'value'		=> $mgid,
			'checked'	=> $checked,
		]);
		?> <?= escape_utf8($name) ?></label><?
		?><br /><?
	}
	?></td><?
	?></tr><?
	?></tbody><?
}

function show_profilefilter_rows(deflist $list, array $item): void {
	$element = $_REQUEST['sELEMENT'];
	$id		 = $_REQUEST['sID'];
	if (!($filters = load_profilefilters($element, $id))) {
		return;
	}
	$multiple_groups = isset($filters[1]);
	ob_start();
	if ($multiple_groups) {
		?><ol class="taglist"><?
	}
	foreach ($filters as /* $group => */ $types) {
		$parts = [];
		foreach ($types as $type => $values) {
			switch ($type) {
			case 'erotic':
				$parts[] = __('attrib:erotic');
				break;

			case 'mastergenre':
				$master_genres = get_master_genres();
				foreach ($values as $mgid => $ignored) {
					if (!isset($master_genres[$mgid])) {
						register_error('profile_filter:error:master_genre_does_not_exist_LINE', ['MGID' => $mgid]);
						$parts[] = "BAD_MGID($mgid)";
					} else {
						$parts[] = escape_utf8($master_genres[$mgid]);
					}
				}
				break;

			case 'city':
				foreach ($values as $cityid => $radius) {
					ob_start();
					echo get_element_link('city', $cityid);
					if ($radius) {
						?> <?= WHITE_CIRCLE_ENTITY ?> <?= $radius ?> <? echo __('abbr:kilometer');
					}
					$parts[] = ob_get_clean();
				}
				break;
			}
		}
		if ($multiple_groups) {
			?><li><?
		}
		?><span class="tag"><?= implode('</span> &amp; <span class="tag">', $parts) ?></span><?
		if ($multiple_groups) {
			?></li><?
		}
	}
	if ($multiple_groups) {
		?></ol><?
	}
	$data = ob_get_clean();
	$list->add_row(Eelement_name('filter_enforcement'), __($item['FILTERS_ENFORCE'] === 'soft' ? 'attrib:best_effort' : 'attrib:guaranteed'));
	$list->add_row(Eelement_name('filter', $multiple_groups), $data);
}

function load_profilefilters(string $element, int $id): array|false {
	if (false === ($input_filters = db_simple_hash('profilefiltergroup', "
		SELECT GRP, TYPE, VAL, EXTRA
		FROM profilefiltergroup
		WHERE ELEMENT = '$element'
		  AND ID      = $id"))
	) {
		return false;
	}
	$filters = [];
	foreach ($input_filters as $grp => $types) {
		foreach ($types as $type => $vals) {
			foreach ($vals as $val => $extra) {
				switch ($type) {	# NOSONAR
				case 'erotic':
					$filters[$grp][$type] = true;
					break;

				case 'mastergenre':
					$filters[$grp][$type][$val] = $val;
					break;

				case 'city':
					$filters[$grp][$type][$val] = $extra;
					break;
				}
			}
		}
	}
	return $filters;
}

function store_profilefilter(): bool {
	$element = $_REQUEST['sELEMENT'];
	$id		 = $_REQUEST['sID'];
	if (empty($_POST['GRP'])) {
		return db_update('profilefiltergroup_history', '
			UPDATE profilefiltergroup_history SET
				DUSERID	= '.CURRENTUSERID.',
				DSTAMP	= '.CURRENTSTAMP."
			WHERE NOT DUSERID
			  AND NOT DSTAMP
			  AND ELEMENT = '$element'
			  AND ID = $id")
		&&	db_delete('profilefiltergroup', "
			DELETE FROM profilefiltergroup
			WHERE ELEMENT = '$element'
			  AND ID = $id");
	}
	if (!require_array($_POST, 'GRP')) {
		return false;
	}
	$ndx = 0;
	$filters = [];
	foreach ($_POST['GRP'] as $supplied_ndx => $supplied_filters) {
		if (!is_array($supplied_filters)) {
			mail_log("bad filters in GRP $supplied_ndx", ['_POST' => $_POST]);
			return false;
		}
		foreach ($supplied_filters as $type => $data) {
			switch ($type) {
			case 'EROTIC':
				$filters[$ndx]['erotic'] = true;
				break;

			case 'MGID':
				if (!require_number_array($supplied_filters, $type)) {
					return false;
				}
				static $__mgids = get_master_genres();
				foreach ($data as $mgid) {
					if (!isset($__mgids[$mgid])) {
						register_error('profile_filter:error:master_genre_does_not_exist_LINE', ['MGID' => $mgid]);
						mail_log("bad MGID $mgid for GRP $supplied_ndx", ['_POST' => $_POST]);
						continue;
					}
					$filters[$ndx]['mastergenre'][$mgid] = $mgid;
				}
				break;

			case 'CITY':
				if (!require_hash($supplied_filters, $type, HASH_NUMBER, HASH_NUMBER)) { # NOSONAR
					mail_log("bad CITY spec for GRP $supplied_ndx", ['_POST' => $_POST]);
					return false;
				}
				foreach ($data as $cityid => $radius) {
					if (!memcached_city($cityid)) {
						register_nonexistent('city', $cityid);
						mail_log("city $cityid does not exist in GRP $supplied_ndx", ['_POST' => $_POST]);
						continue;
					}
					$filters[$ndx]['city'][$cityid] = $radius;
				}
				break;
			default:
				mail_log("unknown filter type '$type' in GRP $supplied_ndx", ['_POST' => $_POST]);
				return false;
			}
		}
		++$ndx;
	}
	if (false === ($old_filters = load_profilefilters($element, $id))) {
		return false;
	}
	$remove_groups = [];
	if ($old_filters) {
		foreach ($old_filters as $old_group => $old_filtergroup) {
			if (isset($filters[$old_group])
			&&	$filters[$old_group] === $old_filtergroup
			) {
				unset($filters[$old_group]);
			} else {
				# group changed or group completely gone
				$remove_groups[$old_group] = $old_group;
			}
		}
	}
	if ($remove_groups
	&&	(	!db_update('profilefiltergroup_history', '
			UPDATE profilefiltergroup_history SET
				DUSERID	= '.CURRENTUSERID.',
				DSTAMP	= '.CURRENTSTAMP.'
			WHERE NOT DUSERID
			  AND NOT DSTAMP
			  AND GRP IN ('.($grp_str = implode(', ', $remove_groups)).")
			  AND ELEMENT = '$element'
			  AND ID = $id")
		||	!db_delete('profilefiltergroup', "
			DELETE FROM profilefiltergroup
			WHERE GRP IN ($grp_str)
			  AND ELEMENT = '$element'
			  AND ID = $id"))
	) {
		# don't continue
		return false;
	}
	$sets = [];
	foreach ($filters as $grp => $types) {
		foreach ($types as $type => $data) {
			switch ($type) {
			case 'erotic':
				$sets[] = "'$element', $id, $grp, 'erotic', 1, 0";
				break;

			case 'mastergenre':
				foreach ($data as $mgid) {
					$sets[] = "'$element', $id, $grp, 'mastergenre', $mgid, 0";
				}
				break;

			case 'city':
				foreach ($data as $cityid => $radius) {
					$sets[] = "'$element', $id, $grp, 'city', $cityid, $radius";
				}
			}
		}
	}
	if ($sets) {
		$history_extra = CURRENTSTAMP.', '.CURRENTUSERID;
		$current_values_str = '('.implode('),				   (', $sets).')';
		$history_values_str = '('.implode(", $history_extra), (", $sets).", $history_extra)";

		if (!db_insert('profilefiltergroup','
			INSERT INTO profilefiltergroup (ELEMENT, ID, GRP, TYPE, VAL, EXTRA)
			VALUEES '.$current_values_str)
		||	!db_insert('profilefiltergroup_history','
			INSERT INTO profilefiltergroup_history (ELEMENT, ID, GRP, TYPE, VAL, EXTRA, CSTAMP, CUSERID)
			VALUES '.$history_values_str)
		) {
			return false;
		}
	}
	return true;
}

function profile_filters_process_item(array &$item): void {
	if (empty($item['FILTERS'])) {
		$item['FILTERS'] = [];
		return;
	}
	$filters = [];
	foreach (explode(';', $item['FILTERS']) as $infos) {
		[$grp, $type, $val, $extra] = explode(',', $infos);
		$filters[$grp][$type][] = [$val, $extra];
	}
	$item['FILTERS'] = $filters;
}

function profile_filters_for_query(string $element, bool $include_need = false): string {
	global $__mins, $__secs;
	return
	(	$include_need
	?	"	(@NEED := IF(@LATE, $element.IMPRESSIONS, d.IMPRESSIONS + THISHOUR * ".($__mins * ONE_MINUTE + $__secs).' / '.ONE_HOUR.")) AS NEED,
			(@DONE := $element.IMPRESSIONSDONE) AS DONE,
			(@MISSING := @NEED - @DONE) AS MISSING,
			(@TIME_LEFT := CAST(STOPSTAMP AS SIGNED) - ".CURRENTSTAMP.') AS TIME_LEFT, '
	:	''
	).	"	(@IMPRESSIONS_LEFT := CAST($element.IMPRESSIONS AS SIGNED) - CAST($element.IMPRESSIONSDONE AS SIGNED)) AS IMPRESSIONS_LEFT,
			(@SECONDS_PER_IMPRESSION := @TIME_LEFT / @IMPRESSIONS_LEFT) AS SECONDS_PER_IMPRESSION,
			(@NEED_SECONDS := @MISSING * @SECONDS_PER_IMPRESSION) AS NEED_SECONDS,
			(	SELECT GROUP_CONCAT(CONCAT(GRP, ',', TYPE, ',', VAL, ',', EXTRA) SEPARATOR ';')
				FROM profilefiltergroup
				WHERE ELEMENT = '$element'
				  AND ID = $element.{$element}ID
			)
			AS FILTERS,
			FILTERS_ENFORCE";
}

function item_domain_alright(array &$item): bool {
	if (!$item['DOMAIN']
	||	CURRENTDOMAIN === $item['DOMAIN']
	) {
		# ok domain
		return item_match_profile_filters($item);
	}
	if (# Skip this domain:
		!$item['OTHER_DOMAIN_FORCED_FILTERS']
		# No filters specified, bad domain:
	||	empty($item['FILTERS'])
	) {
		return false;
	}
	# allow hard match only
	if (item_match_profile_filters($item)
	&&	$item['FILTER_MATCH'] === 'hard'
	) {
		mail_log("hard match for item for {$item['DOMAIN']}", $item);
		return true;
	}
	return false;
}

function item_match_profile_filters(array &$item): bool {
	if (empty($item['FILTERS'])) {
		# no filters, always match
		return true;
	}
	$enforce = $item['FILTERS_ENFORCE'];
	require_once '_profile.inc';
	if (false === ($profile = current_profile())) {
		return false;
	}
	foreach ($item['FILTERS'] as $grp => $filters) {
		$group_match = true;
		foreach ($filters as $type => $rows) {
			$group_match = false;
			switch ($type) {	# NOSONAR
			case 'mastergenre':
				if (empty($profile['genres'])) {
					break;
				}
				foreach ($rows as [$val/*, $extra */]) {
					if (isset($profile['genres'][$val])) {
						$group_match = true;
						break;
					}
				}
				break;

			case 'city':
				if (empty($profile['positions'])) {
					break;
				}
				foreach ($rows as [$cityid, $extra]) {
					if (!($city = memcached_city_info($cityid))) {
						continue;
					}
					foreach ($profile['positions'] as $pos) {
						[$lat, $lon] = $pos;
						$distance = calculate_distance($lat, $lon, $city['LATITUDE'], $city['LONGITUDE']);
						if ($distance <= ($extra ?: 10)) {
							$group_match = true;
							defined('DEBUG_AD') && DEBUG_AD && debug_ad('hard match for item with id '.($item['ADID'] ?? $item['NEWSADID'] ?? '?').' due to distance below '.($extra ?: 10));
							break 2;
						}
					}
				}
				break;

			case 'erotic':
				if (isset($profile['erotic'])) {
					$group_match = true;
				}
				break;
			}
			if (!$group_match) {
				break;
			}
		}
		if ($group_match) {
			defined('DEBUG_AD') && DEBUG_AD && debug_ad('hard match for item with id '.($item['ADID'] ?? $item['NEWSADID'] ?? '?'));
			$item['FILTER_MATCH'] = 'hard';
			$item['FILTER_GROUP'] = $grp;
			return true;
		}
	}
	if ($enforce === 'soft'
	&&	(	$item['LATE']
		||	false !== ($allow_behind = profile_filter_allow_behind($item))
		&&	assert(is_int($allow_behind)) # Satisfy EA inspection
		&&	$item['NEED_SECONDS'] > $allow_behind)
	) {
		defined('DEBUG_AD') && DEBUG_AD && debug_ad('soft match');
		$item['FILTER_MATCH'] = 'soft';
		return true;
	}
	return $item['FILTER_MATCH'] = false;
}

function profile_filter_allow_behind(array $item): int|false {
	if (empty($item['STARTSTAMP'])
	||	empty($item['STOPSTAMP'])
	) {
		return false;
	}
	# NOTE: Care to explain better?:
	#
	# pct = ax + zpct
	# start at 1%, end at 0%
	# dur = duration
	#
	# y = 1 / duration * x + 1

	$start_part = 1;
	$allow_part = $start_part - $start_part * (CURRENTSTAMP - $item['STARTSTAMP']) / ($item['STOPSTAMP'] - $item['STARTSTAMP']);

	# allow half hour hour of behind at t=0

	$allow_behind = HALF_HOUR * $allow_part;

	return (int)$allow_behind;
}

function profilefilter_post_item(array &$setlist): void {
	$key = 'FILTERS_ENFORCE';
	$val = have_element($_POST, 'FILTERS_ENFORCE', ['hard', 'soft'], true) ?: 'soft';

	if (isset($setlist[0])) {
		$setlist[] = "$key = '$val'";
	} else {
		$setlist[$key] = $val;
	}
}

function show_hard(array $info): void {
	if (null === ($done_hard = $info['IMPRESSIONSDONE_HARD'] ?? null)) {
		return;
	}
	$done = $info['IMPRESSIONSDONE'];

	layout_restart_reverse_row(ROW_LIGHT);
		echo $done_hard ? separate_thousands($done_hard) : __('counter:none');
		layout_value_field();
		echo element_name('impression', $done_hard), ' ('.__('attrib:guaranteed').')';

	$pct_hard = round(100 * $done_hard / $done, 1);

	layout_restart_reverse_row(ROW_LIGHT);
		echo $pct_hard;
		layout_value_field(SEPARATOR_PERCENTAGE);
		echo element_plural_name('impression'), ' ('.__('attrib:guaranteed').')';
}
