<?php

require_once 'defines/videothumb.inc';

function show_video_thumb(
	array|int	$video,
	int			$width,
	?string		$class	 = null,
	bool		$show_bg = true,
): void {
	$want_width = $width * get_dpr();
	if (!($meta = get_videothumb_meta($videoid = is_int($video) ? $video : $video['VIDEOID'], $want_width))) {
		?><div class="videothumb <?= $class ?>"></div><?
		return;
	}
	if (!$meta['WIDTH']) {
		# recommended thumbnails size for YouTube:
		$meta['WIDTH'] = 1280;
		$meta['WiDTH'] =  720;
	}
	if ($meta['WIDTH'] !== VIDEOTHUMB_WIDTH) {
		$meta['HEIGHT'] *= VIDEOTHUMB_WIDTH / $meta['WIDTH'];
	}
	if ($horizontal_offset = ((VIDEOTHUMB_WIDTH * 9 / 16) - $meta['HEIGHT']) / 2) {
		$styles[] = 'background-position: -'.VIDEOTHUMB_X_OFFSET.'px '.($horizontal_offset - VIDEOTHUMB_Y_OFFSET).'px';
	}
	$bg_src = IMAGES_HOST."/images/video/$videoid/{$meta['ID']}.jpg";
	if ($show_bg) {
		$styles[] = "background-image: url($bg_src)";
	}

	?><div<?
	if (!$show_bg) {
		?> data-bg-src="<?= $bg_src ?>"<?
	}
	if ($styles) {
		?> style="<?= implode('; ', $styles) ?>;"<?
	}
	?> class="videothumb middle<?
	if ($class) {
		?> <? echo $class;
	}
	?>"></div><?
}

function get_videothumb_meta(int $videoid, ?int $want_width = null): array|false {
	require_once '_browser.inc';
	return memcached_single_assoc('videothumbmeta',"
		SELECT SIZE, CSTAMP, ID, WIDTH, HEIGHT
		FROM videothumbmeta
		WHERE VIDEOID = $videoid
		  AND AVAILABLE = 1
		ORDER BY ".(
				$want_width !== null
		?	"	WIDTH >= $want_width DESC,
				WIDTH ASC,
				ID DESC"
		:	'	WIDTH DESC,
			 	ID DESC'
		).'
		LIMIT 1'
	)
	?: false;
}

function include_video_og(int $videoid): void {
	if (!($thumbnail = memcached_single_assoc('videothumbmeta', "
		SELECT ID, WIDTH, HEIGHT
		FROM videothumbmeta
		WHERE VIDEOID = $videoid
		ORDER BY WIDTH DESC
		LIMIT 1"
	))) {
		return;
	}
	include_og('og:image',		  FULL_IMAGES_HOST."/images/video/$videoid/{$thumbnail['ID']}.jpg");
	include_og('og:image:width',  $thumbnail['WIDTH']);
	include_og('og:image:height', $thumbnail['HEIGHT']);
}
