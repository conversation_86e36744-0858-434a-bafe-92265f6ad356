<?php

declare(strict_types=1);

const DEFAULT_SETTINGS = [
	'PHOTOS_PER_PAGE'				=> 30,
	'MESSAGES_PER_TOPIC'			=> 50,
	'COMMENTS_UNCOLLAPSE'			=> true,
	'TOPICS_PER_PAGE'				=> 80,
	'SHOW_USER_ARCHIVE'				=> false,
	'SHOW_USER_BUDDIES'				=> false,
	'DMESSAGES_PER_PAGE'			=> 20,
	'AUTO_LOGOFF'					=> false,
	'FONT_SIZE'						=> 'browser',
	'SHOW_PROFILE_IMAGES'			=> true,
	'THEME'							=> 'dark',
	'SHOW_USER_PERSONAL'			=> false,
	'THEME_SOFT'					=> false,
	'THEME_CONTRAST'				=> 100,
	'COMMENTS_PER_PAGE'				=> 30,
	'SHOW_USER_SECRETS'				=> false,
	'USER_MENU_TOP'					=> true,
	'SHOW_USER_SHOOTS'				=> false,
	'SHOW_USER_WEBLOG'				=> false,
	'FONT_TYPE'						=> 'Raleway',
	'UNCOLLAPSE_HIDDEN_USER_TEXT'	=> false,
	'HIDE_OLD_PROMOS'				=> false,
	'HIDE_UNREAD_OUTGOING'			=> false,
	'FOLLOW_OPENED_TOPICS'			=> true,
	'HIDE_STARS_IN_FORUM'			=> false,
	'SHOW_USER_GUESTBOOK'			=> true,
	'SHOW_STARS_PERSONAL_AGENDA'	=> false,
	'PAGED_COMMENTS_STARTING_FROM'	=> 100,
	'SHOW_USER_FAVOURITES'			=> false,
	'UNCOLLAPSE_COMMENTS'			=> 0b111001111111101111111111111100,
	'COMMENT_PAGE_ORDER'			=> 'chrono',
	'HIDE_RATINGS_STARTING_FROM'	=> 500,
	'SHOW_RATINGS'					=> 0b111110,
	'AGENDA_FLAGS'					=> 0,
	'FLOCKSFIRSTPAGE'				=> 'O',
	'GMAPS'							=> 'map',
	'FLAGS'							=> 0b1101111111110001011001011,
	'USERLISTS'						=> 0b10,
];

require_once 'defines/settings.inc';
require_once '_memcache.inc';

function setting_isset(int $flag, ?int $userid = null): bool {
	return (bool)(setting('FLAGS', $userid) & $flag);
}

function setting(
	string|true|null			$key		= null,
	?int						$userid		= null,
	int|string|bool|array|null	$new_value	= null,
): int|string|bool|array|null {
	static $__settings;
	$userid ??= defined('CURRENTUSERID') ? CURRENTUSERID : 1;
	if ($key === true) {
		# flush settings
		unset($__settings[$userid]);
		return null;
	}
	if (!isset($__settings[$userid])) {
		if (!($settings = get_settings($userid))
		&&	!($settings = get_settings(1))
		) {
			$settings = DEFAULT_SETTINGS;
		}
		if ($settings && defined('CURRENTTHEME')) {
			$settings['THEME'] = CURRENTTHEME; # THEME may be overridden by cookie
		}
		$__settings[$userid] = $settings;
	}  else {
		$settings = $__settings[$userid];
	}
	if ($new_value !== null) {
		/** @noinspection PhpUnusedLocalVariableInspection */
		$__settings[$userid][$key] = $new_value;
		return null;
	}
	if (!$key) {
		return $settings;
	}
	if (!$settings) {
		mail_log("no setting $key for user $userid");
		return DEFAULT_SETTINGS[$key];
	}
	if ($userid <= 1
	&&	$key === 'USERLISTS'
	) {
		static $__read = false;
		if (!$__read) {
			$__read = true;
			require_once '_identity.inc';
			if (CURRENTIDENTID
			&&	false !== ($user_lists = memcached_single('settingsi','
				SELECT USERLISTS
				FROM settingsi
				WHERE IDENTID = '.CURRENTIDENTID,
				FIVE_MINUTES,
				'settings_user_lists:'.CURRENTIDENTID))
			&&	null !== $user_lists
			) {
				$__settings[$userid][$key]	= $user_lists;
				$settings[$key]				= $user_lists;
			}
		}
	}
	return $settings[$key] ?? DEFAULT_SETTINGS[$key];
}

function flush_settings(int $userid): void {
	memcached_delete('settings:v2:'.$userid);
	/** @noinspection UnusedFunctionResultInspection */
	setting(true, $userid);
}

function flush_settings_user_lists(int $identid): void {
	memcached_delete('settings_user_lists:'.$identid);
	/** @noinspection UnusedFunctionResultInspection */
	setting(true, 1);
}

function get_settings(int $userid): array|false|null {
	return memcached_single_assoc('settings',"
		SELECT *
		FROM settings
		WHERE USERID = $userid",
		FIVE_MINUTES,
		'settings:v2:'.$userid
	);
}
