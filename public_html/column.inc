<?php

require_once '_genrelist.inc';
require_once '_itemlist.inc';
require_once '_ticket.inc';

function preamble() {
	if ($columnid = $_REQUEST['sID']) {
		global $column;
		$column = memcached_single_assoc('column','SELECT TITLE,TEASER,ACCEPTED,PSTAMP FROM `column` WHERE COLUMNID='.$columnid,600);
	}
}
function display_header() {
	require_once '_feed.inc';
	show_feed('column',FEED_HEADER);
	global $column;
	if (!empty($column)
	&&	(	have_admin('column')
		||	$column['ACCEPTED']
		&&	$column['PSTAMP'] <= CURRENTSTAMP
		)
	) {
		include_meta('description', flat_with_entities($column['TEASER'], UBB_UTF8));
	}
}
function perform_commit() {
	switch ($_REQUEST['ACTION']) {
	default:
	case null:		return;
	case 'commit':		return column_commit();
	case 'accept':
	case 'unaccept':	require_once '_accept.inc'; return item_set_accept($_REQUEST['ACTION']);
	case 'remove':		require_once '_remove.inc'; return remove_element();
	}
}
function display_body() {
	switch ($_REQUEST['ACTION']) {
	default:		not_found(); return;
	case null:
	case 'remove':		return column_display_overview();
	case 'single':		return column_display_single();
	case 'commit':
	case 'comment':
	case 'comments':	return column_display_single();
	case 'form':		return column_display_form();
	case 'archive':		return column_display_archive();
	}
}
function column_display_form() {
	layout_show_section_header();

	if (!require_admin('column')) {
		return;
	}
	if ($columnid = have_idnumber($_REQUEST,'sID')) {
		if (!require_obtainlock(LOCK_COLUMN,$columnid)) {
			return;
		}
		$column = db_single_assoc('column','SELECT * FROM `column` WHERE COLUMNID='.$_REQUEST['COLUMNID']);
		if ($column === false) {
			return;
		}
		if (!$column) {
			not_found();
			return;
		}
		if (!require_self_or_admin($column['USERID'],'column')) {
			return;
		}
	} else {
		$column = null;
	}
	?><form<?
	?> accept-charset="utf-8"<?
	?> enctype="multipart/form-data"<?
	?> onsubmit="return submitForm(this)"<?
	?> method="post"<?
	?> action="/column<?
	if ($columnid) {
		?>/<? echo $columnid;
	}
	?>/commit"><?
	ticket_passthrough();

	require_once '_uploadimage.inc';
	show_uploadimage_for_element_form('column', $columnid);

	layout_open_box('column');
	layout_open_table(TABLE_CLEAN);
	layout_start_row();
		echo Eelement_name('title');
		layout_field_value();
		?><input required="required" maxlength="160" type="text" name="TITLE" value="<? if (isset($column)) echo escape_utf8($column['TITLE']);
		?>"><?
	layout_restart_row();
		echo Eelement_name('author');
		layout_field_value();
		require_once '_fillelementid.inc';
		show_elementid_input('user', $column['BYUSERID'], [
			'name' => 'BYUSERID'
		]);
	layout_restart_row();
		?><label for="anonymous"><?= __C('field:anonymous') ?></label><?
		layout_field_value();
		?><input type="checkbox" value="1" name="ANONYMOUS"<? if (isset($column) && $column['ANONYMOUS']) echo ' checked="checked"';
		?> id="anonymous"><?
	layout_restart_row();
		?><label for="accepted"><?= __C('attrib:accepted') ?></label><?
		layout_field_value();
		?><input type="checkbox" value="1" name="ACCEPTED"<? if (isset($column) && $column['ACCEPTED']) echo ' checked="checked"';
		?> id="accepted"><?
	layout_restart_row();
		?><label for="direct"><?= Eelement_name('publication') ?></label><?
		layout_field_value();
		show_publication($column);
	layout_restart_row();
		echo Eelement_name('teaser');
		layout_field_value();
		?><textarea required="required" name="TEASER" cols="40" rows="10"><? if (isset($column)) echo escape_utf8($column['TEASER']);
		?></textarea><?
	layout_restart_row();
		echo Eelement_name('column');
		layout_field_value();
		?><textarea required="required" name="BODY" cols="80" rows="20"><? if (isset($column)) echo escape_utf8($column['BODY']);
		?></textarea><?
	layout_stop_row();
	layout_close_table();
	layout_close_box();
	?><div class="block"><?
	?><input type="submit" value="<?= __(isset($column) ? 'action:change' : 'action:add') ?>" /></div><?
	?></form><?
}
function column_display_overview() {
	_itemlist_display_overview_header();
	_itemlist_display_menu();
	_itemlist_display_prelist();
	_itemlist_display();
}
function column_display_archive() {
	_itemlist_display_archive_header();
	_itemlist_display_menu();
	_itemlist_display_archive();
}
function column_display_single() {
	require_once '_connect.inc';
	require_once '_uploadimage.inc';
	require_once '_vote.inc';
	if (!($columnid = require_idnumber($_REQUEST,'sID'))) {
		return false;
	}
	$column = db_single_assoc('column','
		SELECT BYUSERID, USERID, BODY, COLUMNID, TITLE, PSTAMP, ACCEPTED, CSTAMP, MSTAMP, MUSERID, ANONYMOUS, TEASER
		FROM `column`
		WHERE COLUMNID='.$_REQUEST['COLUMNID']
	);
	if ($column === false) {
		return false;
	}
	if (!$column) {
		not_found();
		return;
	}

	$is_admin = itemlist_is_admin($column);
	$hashed = _item_hashed($column);
	if (!$is_admin
	&&	!$hashed
	) {
		if ($column['PSTAMP'] > CURRENTSTAMP) {
			_error('Column met ID '.$_REQUEST['COLUMNID'].' is nog niet gepubliceerd!');
			return false;
		}
		if (!$column['ACCEPTED']) {
			_error('Column met ID '.$_REQUEST['COLUMNID'].' is niet goedgekeurd!');
			return false;
		}
	}

	layout_show_section_header($column);

	_itemlist_display_menu($column);

	require_once '_ticketlist.inc';
	show_connected_tickets();

	if ($is_admin) {
		_connect_display_form('column',$column['COLUMNID']);
	}
	if (!SMALL_SCREEN
	&&	(	$is_admin
		||	$column['BYUSERID'] == CURRENTUSERID
		||	$hashed
		)
	&&	$column['TEASER']
	) {
		require_once '_itemlist.inc';
		show_item_teaser('column',$columnid,$column);
	}
	?><article itemscope itemtype="https://schema.org/CreativeWork"><?
	layout_open_box($column['ACCEPTED'] ? 'column' : 'unaccepted column');
	?><header><?
	layout_open_box_header();
	?><h1 itemprop="name"><?= make_all_html($column['TITLE'], UBB_UTF8) ?></h1><?
	layout_close_box_header();
	?></header><?
	itemlist_show_publish_info($column);
	?><div class="<?
	if (!SMALL_SCREEN) {
		?>right-float <?
	}
	?>center"><?
		uploadimage_show('column',$column['COLUMNID'],UPIMG_SCHEMA | UPIMG_SHOW_HISTORY);
		_connect_display('column',$column['COLUMNID']);
	?></div><?
	?><div class="lclr minp block" itemprop="articleBody"><?= make_all_html($column['BODY'],null,'column',$columnid) ?></div><?
	layout_display_alteration_note($column);
	layout_close_box();

	require_once '_commentlist.inc';
	$cmts = new _commentlist;
	$cmts->item($column);
	$cmts->display();
	?></article><?

	counthit('column',$_REQUEST['sID']);
}

function column_commit() {
	require_once '_ubb_preprocess.inc';
	$setlist = array();
	if (!require_admin('column')
	||	!require_something_trim($_POST, 'TEASER', null, null, utf8: true)
	||	!require_something_trim($_POST, 'TITLE', null, null, utf8: true)
	||	!require_something_trim($_POST, 'BODY', null, null, utf8: true)
	||	false === require_number($_POST,'BYUSERID')
	||	!parse_publication($setlist)
	) {
		return;
	}
	$setlist[] = 'TEASER="'.addslashes($teaser = _ubb_preprocess($_POST['TEASER'], utf8: true)).'"';
	$setlist[] = 'TITLE="'.addslashes(_ubb_preprocess($_POST['TITLE'], utf8: true)).'"';
	$setlist[] = 'BODY="'.addslashes($body = _ubb_preprocess($_POST['BODY'], utf8: true)).'"';
	$setlist[] = 'ACCEPTED='.(isset($_POST['ACCEPTED']) ? 1 : 0);
	$setlist[] = 'ANONYMOUS='.(isset($_POST['ANONYMOUS']) ? 1 : 0);
	$setlist[] = 'BYUSERID='.$_POST['BYUSERID'];

	if ($columnid = $_REQUEST['sID']) {
		if (!require_last_lock(LOCK_COLUMN,$columnid)
		||	!db_insert('column_log','
			INSERT INTO column_log
			SELECT * FROM `column`
			WHERE COLUMNID='.$_REQUEST['COLUMNID'])
		) {
			return;
		}
		$setlist[] = 'MUSERID='.CURRENTUSERID;
		$setlist[] = 'MSTAMP='.CURRENTSTAMP;
		if (!db_update('column','
			UPDATE `column` SET '.implode(',',$setlist).'
			WHERE COLUMNID='.$_REQUEST['COLUMNID'])
		) {
			return;
		}
		register_notice('column:notice:changed_LINE');
#		release_lock(LOCK_COLUMN,$columnid);
	} else {
		$setlist[] = 'USERID='.CURRENTUSERID;
		$setlist[] = 'CSTAMP='.CURRENTSTAMP;
		if (!db_insert('column','
			INSERT INTO `column` SET '.implode(',',$setlist))
		) {
			return false;
		}
		$_REQUEST['sID'] =
		$_REQUEST['COLUMNID'] = $columnid = db_insert_id();
		register_notice('column:notice:added_LINE');

		if (have_idnumber($_REQUEST,'TICKETID')) {
			ticket_update('column',$_REQUEST['sID']);
		}
	}
	require_once '_uploadimage.inc';
	uploadimage_actual_receive('column',$columnid);

	require_once '_update.inc';
	update_element('column', $columnid, $body, $teaser, utf8: true);
}
