<?php

define('TODAYSTAMP',	mktime(0,0,0));
define('CURRENTSTAMP',	time());
define('CACHESTAMP',	CURRENTSTAMP - CURRENTSTAMP % 300);

require_once '_exit_if_readonly.inc';
require_once '_nocache.inc';
require_once '__translation.php';

require_once '_layout.inc';
require_once '_currentuser.inc';
require_once '_error.inc';
require_once '_lock.inc';
require_once '_lineup.inc';
require_once '_require.inc';

require_once '_helper.inc';

function bail(?int $errno = null): never {
	if ($errno) {
		http_status($errno === 500 ? 503 : $errno);
	}
	display_messages_only();
	exit;
}

send_no_cache_headers();

if (!have_valid_origin()) {
	bail(403);
}

header('Content-Type: text/html; charset=windows-1252');

$currentuser = new _currentuser(TINY_USER);

if (!isset($_SERVER['ePARTYID'])
||	!isset($_SERVER['eACTION'])
||	!have_element($_SERVER, 'eACTION', ['formparts', 'addartist', 'removeartist', 'process', 'parseform', 'parse', 'removearea'])
) {
	bail(400);
}
$partyid = $_SERVER['ePARTYID'];

if (!($party = db_single_assoc(
	['party','location','boarding','city','lineupdapartyneedudate'],'
	SELECT	PARTYID, party.USERID, STAMP, STAMP_TZI, DURATION_SECS, party.ACCEPTED, LINEUP_PSTAMP, NOTIME, COUNTRYID, TIMEZONE, TIMETABLE_PSTAMP, LIVESTREAM,
			(SELECT CSTAMP FROM lineupneedupdate WHERE lineupneedupdate.PARTYID = party.PARTYID) AS LINEUP_NEEDUPDATE
	FROM party
	LEFT JOIN location USING (LOCATIONID)
	LEFT JOIN boarding USING (BOARDINGID)
	LEFT JOIN city ON city.CITYID = COALESCE(boarding.CITYID, location.CITYID, party.CITYID)
	WHERE PARTYID = '.$partyid
))) {
	if ($party === false) {
		bail(500);
	}
	register_error('party:error:nonexistent_LINE', ['ID' => $partyid]);
	bail(404);
}

if (!require_last_lock_and_freshen(LOCK_PARTY, $partyid)
||	!require_admin_or_my_party($party)
) {
	bail(403);
}

# dirty stuff with 1F as separator

switch ($_SERVER['eACTION']) {
case 'addartist':
	# 1F separates lineup form and notices/errors
	if (200 !== ($errno = lineup_add_artist($party))) {
		bail($errno);
	}
	lineup_display($party, true);
	echo "\x1F";
	bail(200);

case 'formparts':
	# 1F separates single add form and lineup form
	lineup_show_add_form($party);
	echo "\x1F";
	lineup_display($party,true);
	break;

case 'removeartist':
	bail(lineup_remove_artist($partyid) ? 200 : 500);
	break;

case 'process':
	if (!($rc = lineup_process($party, $areamove))) {
		bail($rc === false ? 500 : 400);
	}
	lineup_display($party, true);
	# NOTE: \x1F needed here?
	echo "\x1F";
	bail(200);

case 'parseform':
	lineup_show_parse_form($partyid);
	break;

case 'parse':
	if (!($info = lineup_parse($party))) {
		bail(500);
	}
	ob_start();
	lineup_display($party, $info);
	$data = ob_get_clean();
	http_status(200);
	echo $data;
	echo "\x1F";
	bail();

case 'removearea':
	if (!lineup_remove_area($partyid)) {
		bail(500);
	}
	break;
}
