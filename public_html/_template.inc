<?php

function template_for_late_invoice($id) {
	require_once '_relationmember.inc';
	require_once '_price.inc';
	require_once '_adlink.inc';

	$invoice = db_single_assoc(['invoice','relation'],'
		SELECT	INVOICENR,
			CONTACT,
			CONTACTEMAIL,
			INVOICEMAIL,
			INVOICECONTACT,
			INVOICENAME,
			AMOUNT_NEW,VAT,SENTSTAMP,invoice.RELATIONID,UNIQID
		FROM invoice
		JOIN relation USING (RELATIONID)
		WHERE INVOICENR='.$id
	);
	if (!$invoice) {
		return false;
	}
	extract($invoice);
	if ($RELATIONID) {
		$contactuser = db_single_array(['relationmember','user_account'],'
			SELECT USERID,FLAGS&'.RMEMB_COST_INDICATION.'
			FROM relationmember
			JOIN user_account USING (USERID)
			WHERE RELATIONID='.$RELATIONID.'
			  AND STATUS="active"
			ORDER BY FLAGS&'.RMEMB_COST_INDICATION.' DESC
			LIMIT 1'
		);
		if ($contactuser === false) {
			return false;
		}
	} else {
		$contactuser = null;
	}

	[$y,$m,$d] = _getdate($SENTSTAMP);
	$date = sprintf('%d %s %d',$d,_month_name($m),$y);

	if ($INVOICEMAIL) {
		$to_userid = $contactuser && $contactuser[1] ? $contactuser[0] : 0;
		$to_email = $INVOICEMAIL;
		$to_name = $INVOICECONTACT ?: $INVOICENAME ?: $CONTACT;
		$to_noncost = false;
	} else {
		$to_userid = $contactuser ? $contactuser[0] : 0;
		$to_email = $CONTACTEMAIL;
		$to_name = $CONTACT;
		$to_noncost = $to_userid && !$contactuser[1];
	}

	return [
		'ELEMENT'	=> 'invoice',
		'ID'		=> $id,
		'BODY'		=> __('mail:late_invoice:message_TEXT',DONT_ESCAPE,[
						'SENTSTAMP'	=> $SENTSTAMP,
						'RELATIONID'	=> $RELATIONID,
						'DATE'		=> $date,
						'INVOICENR'	=> $id,
						'PRICE'		=> get_price_number($AMOUNT_NEW * (100 + $VAT) / 100),
						'URL'		=> get_hashlink($invoice,'invoice') ?: FULL_HOST.'/invoice/'.$id,
					]),
		'SUBJECT'	=> __('mail:late_invoice:subject',DONT_ESCAPE,['INVOICENR' =>$id]),
		'TO_USERID'	=> $to_userid,
		'TO_NONCOST'	=> $to_noncost,
		'TO_NAME'	=> $to_name,
		'TO_EMAIL'	=> $to_email,
		'ATTACHMENT'	=> 'partyflock_invoice_'.$id.'.pdf',
		'FORCE_EMAIL'	=> true,
	];
}
function template_for_promo_cost($promoid) {
	require_once '_relationmember.inc';
	require_once '_price.inc';
	require_once '_promofocus.inc';
	require_once '_promoprice.inc';
	require_once '_status.inc';
	$promo = db_single_assoc(
		array('promo','promoprice','relation'),'
		SELECT	PROMOID,TITLE,STARTSTAMP,STOPSTAMP,promo.DISCOUNT,REACH,promo.RELATIONID,ESTIM,TARGETPRICE,POLLS,promo.CSTAMP,BUDGET,VERSION,
			promoprice.*,
			CONTACT,CONTACTEMAIL
		FROM promo
		JOIN promoprice USING (PRICEID)
		LEFT JOIN relation ON relation.RELATIONID=promo.RELATIONID
		WHERE PROMOID='.$promoid
	);
	if (!$promo) {
		return false;
	}
	if (!$promo['REACH']) {
		_error('Promo heeft geen bereik!');
		return false;
	}
	ob_start();
	$groupcnt = promo_focus_overview($promoid,FOCUS_FOR_MAIL);
	$content = ob_get_contents();
	ob_end_clean();
	if (!$content) {
		warning('Zijn er geen criteria?');
	}

	$price = get_promopriceid($promo['PRICEID']);

	[$minprice,$maxprice] = get_promoprice($promo,$promo['REACH'],$promo);

	$neverless = $promo['DISCOUNT'] == 100 ? 0 : $promo['MINIMUMPRICE'];

	$estimprice = $minprice + ($promo['ESTIM'] * $price['SEEN']) / 100;

	$get_prices = function($minprice,$estimprice,$maxprice,$budget,$postfix = null) use ($neverless) {
		$show_budget_price = $budget && 100 * $maxprice > $budget;

		return "Minimale prijs$postfix: ".chr(128).' '.
			get_price_number($minprice < $neverless
			?	$neverless
			:	$minprice
			)."\n".
		(	$estimprice
		?	"Geschatte prijs$postfix: ".chr(128).' '.
				get_price_number($estimprice < $neverless
				?	$neverless
				:	$estimprice
				)."\n"
		:	null
		).
		(	!$show_budget_price
		?	"Maximale prijs$postfix: ".chr(128).' '.
			get_price_number($maxprice < $neverless
			?	$neverless
			:	$maxprice
			)."\n"
		:	null
		).
		(	$show_budget_price
		?	"Maximale prijs$postfix binnen budget: ".chr(128).' '.
				get_price_number($budget / 100)
				."\n"
		:	null
		);
	};

	$prices = $get_prices($minprice,$estimprice,$maxprice,$promo['BUDGET']);

	$reachpart = '';
	if ($promo['BUDGET']) {
		$max = floor($promo['BUDGET'] / $price['SEEN']);
		if ($max < $promo['REACH']) {
			$reachpart = $max.' / ';
		}
	}
	$reachpart .= $promo['REACH'];

	if ($promo['DISCOUNT']) {
		$dminprice = $minprice * (100-$promo['DISCOUNT'])/100;
		$dmaxprice = $maxprice * (100-$promo['DISCOUNT'])/100;
		$destimprice = $estimprice * (100-$promo['DISCOUNT'])/100;
		$dbudget = $promo['BUDGET'] * (100-$promo['DISCOUNT'])/100;
		$discount = 'Korting: '.$promo['DISCOUNT']."%\n".
			$get_prices($dminprice,$destimprice,$dmaxprice,$dbudget,' na korting')/*.(
			$promo['BUDGET']
		?	'Vergroot bereik door korting: +'.($promo['DISCOUNT'] * $promo['BUDGET'] / $promo['SEEN'] / 100)."\n"
		:	null
		)*/;
	} else {
		$discount = null;
	}
	if ($promo['RELATIONID']
	&&	(	$users = db_rowuse_array(
			array('relationmember','user_account'),'
			SELECT relationmember.USERID,STATUS
			FROM relationmember
			JOIN user_account USING (USERID)
			WHERE RELATIONID='.$promo['RELATIONID'])
		)
	) {
		$ok = false;
		foreach ($users as $user) {
			if ($user['STATUS'] == 'active'
			||	$user['STATUS'] == 'inactive'
			) {
				$ok = true;
				break;
			} else {
				$status = $user['STATUS'];
			}
		}
		$moreinfo =
			$ok
		?	'Je kunt op onderstaand adres het promobericht en statistieken inzien:'
		:	'Je zou op onderstaand adres het promobericht en statistieken kunnen inzien, ware het niet, dat het gekoppelde Partyflock '.
			'account '.status_name($status).' is. Als je een ander doorgeeft kan dat gekoppeld worden!'
		;
	} else {
		$moreinfo =
			'Als je een Partyflock account doorgeeft dat gekoppeld kan worden, kun je op onderstaand adres '.
			'het promobericht en statistieken inzien:';
	}


	$userid = cost_indication_userid($promo['RELATIONID']);
	if ($userid === false) {
		return false;
	}
	$userid = 0 +  $userid;

	require_once '_adlink.inc';

	$body = "Hierbij vind je de kostenindicatie voor het door jou aangevraagde promobericht.\n".
#		"Helaas kunnen we door ons nieuwe systeem nog geen geschatte prijs geven, maar je kunt ons je budget opgeven en dan zorgen wij dat je promo niet boven het bedrag zal uitkomen!\n".
		"\n".
		"Geregeld passen wij het bericht enigszins aan, om het beter leesbaar en beter geschikt voor Partyflock te maken. Controleer even of het bericht zo ook naar wens is.\n".
		"\n".
		"Titel: ".$promo['TITLE']."\n".
		"Start: "._date_get($promo['STARTSTAMP'])."\n".
		"Laatste dag: "._date_get($promo['STOPSTAMP']-1)."\n".
		"Loopduur: ".($days = round(($promo['STOPSTAMP']-$promo['STARTSTAMP'])/24/3600))." dag".($days > 1 ? 'en' : null)."\n".
		($promo['BUDGET'] ? "Budget: ".chr(128).' '.get_price_number($promo['BUDGET']/100)."\n" : null).
		"Bereik: $reachpart\n".
		($promo['POLLS'] ? "Polls: ".$promo['POLLS']."\n" : null).
		$prices.
		$discount.
		(	$content
		?	"Criteria:\n".strip_tags($content)
		:	null
		).
		"\n".
		$moreinfo."\n".
		"\n".
		get_hashlink($promo,'promo',$userid)."\n".
		"\n".
		"Graag wil ik je ook wijzen op onze algemene voorwaarden die van toepassing zijn op elke overeenkomst: ".FULL_HOST."/promo/terms\n".
		"\n".
			"Het bereik is gereserveerd voor jouw promobericht, ".
			"laat daarom ook wat van je horen wanneer je niet ge".chr(239)."nteresseerd bent, ".
			"dan kan het bereik weer vrijgegeven worden en wellicht kan er wat aan de criteria gesleuteld worden!\n\n".

		"Als 24 uur na de oorspronkelijke start van de promo nog geen reactie of akkoord is geweest, zal het gereserveerde bereik vrijgegeven worden.\n\n".
		"Mocht je nog vragen hebben, beantwoord dan dit bericht, want middels automatisch koppeling aan het promobericht kunnen we dan direct ".
		"zien waar het over gaat!";

	$email = null;
	if ($promo['CONTACTEMAIL']) {
		$email = $promo['CONTACTEMAIL'];
	} else {
		if ($promo['RELATIONID']) {
			register_warning('template:warning:relation_has_no_contact_email_LINE',['RELATIONID'=>$promo['RELATIONID']]);
			$email = db_single(['relationmember','user_account'],'
				SELECT EMAIL
				FROM relationmember
				JOIN user USING (USERID)
				JOIN user_account USING (USERID)
				WHERE relationmember.RELATIONID='.$promo['RELATIONID'].'
				ORDER BY STATUS="active" DESC,FLAGS&'.RMEMB_COST_INDICATION.' DESC
				LIMIT 1'
			);
		}
	}
	return [
		'ELEMENT'	=> 'promo',
		'ID'		=> $promoid,
		'BODY'		=> $body,
		'SUBJECT'	=> 'Kostenindicatie promobericht '.($promo['TITLE'] ? $promo['TITLE'] : '#'.$promoid),
		'TO_USERID'	=> 0+$userid,
		'TO_NAME'	=> $promo['CONTACT'],
		'TO_EMAIL'	=> $email,
		'DESTINATION'	=> $email ? 'email' : 'userid',
		'tACTION'	=> 'promocost',
		'tPROMOID'	=> $promoid,
		'FORCE_EMAIL'	=> true
	];
}
function template_for_generic($element,$id) {
	require_once '_relationmember.inc';
	switch ($element) {
	case 'promo':
		$info = db_single_assoc(
			array('promo','relation'),'
			SELECT TITLE,RELATIONID,CONTACT,CONTACTEMAIL
			FROM promo
			LEFT JOIN relation USING (RELATIONID)
			WHERE PROMOID='.$id
		);
		if (!$info) {
			return false;
		}
		$subject = 'Betreffende promotiebericht '.($info['TITLE'] ? 'voor '.$info['TITLE'] : $id);
		break;
	case 'job':
		$info = db_single_assoc(
			array('job','relation'),'
			SELECT FUNCTION,RELATIONID,CONTACT,relation.CONTACTEMAIL
			FROM job
			LEFT JOIN relation USING (RELATIONID)
			WHERE JOBID='.$id
		);
		if (!$info) {
			return false;
		}
		$subject = 'Betreffende vacature '.($info['FUNCTION'] ? $info['FUNCTION'] : $id);
		break;
	}
	if (empty($info)) {
		return false;
	}
	$userid = cost_indication_userid($info['RELATIONID']);
	if ($userid === false) {
		return false;
	}
	$emails = array();
	return array(
		'ELEMENT'	=> $element,
		'ID'		=> $id,
		'BODY'		=> '',
		'SUBJECT'	=> $subject,
		'TO_USERID'	=> 0+$userid,
		'TO_NAME'	=> $info['CONTACT'],
		'TO_EMAIL'	=> $info['CONTACTEMAIL'],
		'DESTINATION'	=> $info['CONTACTEMAIL'] ? 'email' : 'userid',
#		'tACTION'	=> 'generic',
#		'tPROMOID'	=> $id,
		'FORCE_EMAIL'	=> true,
	);
}
function template_for_job_cost($jobid) {
	require_once '_relationmember.inc';
	$job = db_single_assoc(
		array('job','relation'),'
		SELECT	JOBID,FUNCTION,STARTSTAMP,STOPSTAMP,job.DISCOUNT,job.RELATIONID,CONTACT,relation.CONTACTEMAIL,
			jobprice.*,
			CONTACT, relation.CONTACTEMAIL
		FROM job
		JOIN jobprice USING (PRICEID)
		LEFT JOIN relation ON relation.RELATIONID=job.RELATIONID
		WHERE JOBID='.$jobid
	);
	if (!$job) {
		return false;
	}
	$prices = db_single_assoc(
		'jobprice', '
		SELECT *
		FROM jobprice
		WHERE PRICEID=' . $job['PRICEID']
	);
	if ($prices) {
		$costperday = $prices['COSTPERDAY'];
	} else {
		$costperday = 0;
	}
	$days = round(($job['STOPSTAMP'] - $job['STARTSTAMP']) / 24 / 3600);
	$totalprice = $days * $costperday;
	$weeks = round($days / 7);

	if ($job['DISCOUNT']) {
		require_once '_price.inc';
		$dprice = $totalprice * (100 - $job['DISCOUNT']) / 100;
		$discount =
			"Korting: " . $job['DISCOUNT'] . "%\n".
			"Prijs na korting: " . (get_price_number($dprice)) . "\n";
	} else {
		$discount = null;
	}
	require_once '_price.inc';
	$body = "Hierbij vind je de kostenindicatie voor de door jou aangevraagde vacature:\n".
		"\n".
		"Functie: " . $job['FUNCTION'] . "\n" .
		"Start: " . _date_get($job['STARTSTAMP']) . "\n" .
		"Loopduur: " . $weeks . ($weeks > 1 ? ' weken' : ' week') . "\n" .
		"Prijs: " . get_price_number($totalprice) . "\n" .
		$discount .
		"\n" .
		FULL_HOST.'/job/' . $jobid . "/priceinfo\n" .
		"\n" .
		"Graag wil ik je ook wijzen op onze algemene voorwaarden die van toepassing zijn op elke overeenkomst: ".FULL_HOST."/job/terms\n" .
		"\n" .
		"We ontvangen graag je akkoord op deze offerte, zodat we de vacature kunnen activeren.\n" .
		"Valt het plaatsen buiten het budget? We kunnen de looptijd ook verkorten!";

	$userid = cost_indication_userid($job['RELATIONID']);
	if ($userid === false) {
		return false;
	}
	return array(
		'ELEMENT'	=> 'job',
		'ID'		=> $jobid,
		'BODY'		=> $body,
		'SUBJECT'	=> 'Kostenindicatie vacature ' . ($job['FUNCTION'] ? $job['FUNCTION'] : '#' . $jobid),
		'TO_NAME'	=> $job['CONTACT'],
		'TO_EMAIL'	=> $job['CONTACTEMAIL'],
		'TO_USERID'	=> 0+$userid,
		'DESTINATION'	=> $job['CONTACTEMAIL'] ? 'email' : 'userid',
		'tACTION'	=> 'jobcost',
		'tJOBID'	=> $jobid,
		'FORCE_EMAIL'	=> true,
	);
}

function get_contact_info(array $contest): array|false {
	$userid = null;
	if (!$contest['TICKETID']) {
		if (is_number($contest['CONTACT'])) {
			$contactname = $email = null;
			$userid = $contest['CONTACT'];
		} else {
			[$contactname, $email, $userid] = contact_get_legacy_mailinfo($contest['CONTACT']);
		}
	} elseif (
		$contest['CONTACT']
	&&	match_email($contest['CONTACT'])
	) {
		$contactname = null;
		$email = $contest['CONTACT'];
	} else {
		$message = db_single_array(['contact_ticket_message','contact_ticket_mail'],'
			SELECT FROM_NAME,FROM_EMAIL
			FROM contact_ticket_message
			JOIN contact_ticket_mail USING (CTMSGID,TICKETID)
			WHERE TICKETID = '.$contest['TICKETID'].'
			  AND DIRECTION = "toadmin"
			ORDER BY CTMSGID DESC
			LIMIT 1'
		);
		if ($message) {
			[$contactname, $email] = $message;
		} else {
			$user = db_single_array(['user','contact_ticket'],'
				SELECT EMAIL,REALNAME,NAME,NICK
				FROM contact_ticket
				JOIN user ON WITHUSERID=user.USERID
				WHERE TICKETID='.$contest['TICKETID']
			);
			if (!$user) {
				return false;
			}
			[$email, $realname, $name, $nick] = $user;
			$contactname = make_clean_realname($realname ?: $name ?: $nick);
		}

	}
	return [$contactname, $email, $userid];
}

function template_for_contest_result($contestid) {
	require_once '_contest.inc';
	$contest = db_single_assoc(['contest','party'],'
		SELECT	IF(NOT ISNULL(party.NAME),party.NAME,contest.NAME) AS NAME,GROUPS,AMOUNT,TYPE,contest.TICKETID,CONTACT,
			STAMP,STAMP_TZI,AT2400,NOTIME,
			LOCATIONID
		FROM contest
		LEFT JOIN party USING (PARTYID)
		WHERE CONTESTID = '.$contestid
	);
	if ($contest === false) {
		return false;
	}
	if (!$contest) {
		register_error('contest',$contestid);
		return false;
	}

	[$contactname, $email, $userid] = get_contact_info($contest);

	$winnerlist = db_rowuse_array(['contestresponse', 'contestcode', 'contestwinmessage', 'user', 'city'], '
		SELECT	user.NICK, REALNAME, ADDRESS, ZIPCODE, user.CITYID, user.CITY, user.EMAIL, user.PHONE, EMAIL, CODE,
			city.NAME, city.COUNTRYID
		FROM contestresponse
		LEFT JOIN contestwinmessage USING (USERID, CONTESTID)
		LEFT JOIN contestcode USING (USERID, CONTESTID)
		LEFT JOIN user USING (USERID)
		LEFT JOIN city USING (CITYID)
		WHERE CONTESTID = '.$contestid.'
		  AND WINSTATUS = "winner"'
	);

	if ($winnerlist === false) {
		_error('Winnaars voor actie met ID '.$contestid.' niet opvraagbaar!');
		return false;
	}

	if (!$winnerlist) {
		_error('Er zijn geen winnaars.');
		return false;
	}

	$size = count($winnerlist);

	$winnerstr = '';
	$winnercnt = count($winnerlist);

	$contest_name = utf8_to_win1252($contest['NAME'] ?: $contest['PARTY_NAME']);

	$part = $contest['GROUPS'] > $winnercnt;

	foreach ($winnerlist as $winner) {
		fix_user_personalia($winner);
		--$size;
		if ($contest['TYPE'] === 'guestlist') {
			$winnerstr .= ' - ';
		}
		$winnerstr .= make_clean_realname($winner['REALNAME']);
		switch ($contest['TYPE']) {
		case 'eticket':
			$winnerstr .= "\n".$winner['EMAIL']."\n";
			if ($size) {
				$winnerstr .= "\n";
			}
			break;
		case 'guestlist':
			if ($winner['CODE']) {
				$have_uniq = true;
				$winnerstr .= ", ".$winner['CODE'];
			}
			$winnerstr .= "\n";
			break;
		default:
			$winnerstr .= "\n".$winner['ADDRESS']."\n".$winner['ZIPCODE'];
			if ($winner['NAME']) {
				$winnerstr .= ' '.utf8_to_win1252($winner['NAME']);
			} elseif ($winner['CITY']) {
				$winnerstr .= ' '.$winner['CITY'];
			}
			if ($winner['COUNTRYID'] != 1) {
				$winnerstr .= "\n".utf8_to_win1252(get_element_title('country',$winner['COUNTRYID']));
			}
			$winnerstr .= "\n";
			if ($size) {
				$winnerstr .= "\n";
			}
			break;
		}
	}
	$winnerstr = myrtrim($winnerstr);

/*	$body = 'Hier '.($part ? $winnercnt : 'de').' winnaars van de '.$contest['GROUPS'].
		'x '.$contest['AMOUNT'].
		' '.contest_get_type($contest['AMOUNT'],$contest['TYPE']).
		' actie voor '.$contest['NAME'].(
			$extrainfo
		=	$contest['STAMP']
		?	' ('._datedaytime_get($contest).(
				$contest['LOC_NAME']
			?	', '.$contest['LOC_NAME']
			:	null
			).')'
		:	null
		).
		" op Partyflock.\n\n".

		"Stuur nog even bevestiging terug dat de namen zijn doorgekomen en verwerkt, dan kunnen wij hen automatisch inlichten.\n".
		"Bij geen respons worden geen winnaars ingelicht en worden er voorlopig geen nieuwe winacties voor je geplaatst.\n\n".

		$winnerstr."\n".

		(	$contest['TYPE'] == 'guestlist'
		?	(	isset($have_uniq)
			?	"Zoals je ziet generen wij ook een unieke code voor elke winnaar ".
				"en adviseren wij de mensen om het bericht waarin staat dat ze gewonnen hebben uit te printen een mee te nemen om zo het controleren bij de entree".
				"nauwkeuriger en sneller te laten verlopen!\n\n"
			:	null
			).
			"Als de gastenlijst op een bepaalde tijd sluit, geef dit dan door, dan kan dit de winnaars duidelijk gemaakt worden.\n\n"
		:	null
		).
		(	$contest['TYPE'] == 'guestlist'
		||	$contest['TYPE'] == 'freeticket'
		||	$contest['TYPE'] == 'eticket'
		?	"Komt er iemand niet opdagen? Geef dit dan ook door! Dan kan deze persoon uitgesloten worden van deelname in de toekomst.\n\n"
		:	null
		).
		"Vergeet niet dit bericht te bevestigen, anders kunnen wij de winnaars niet laten weten dat ze gewonnen hebben!\n\n".

		"Bedankt!";*/

	$body = __('template:contest_winners:body_TEXT', DONT_ESCAPE, [
			'THISCOUNT'	=> count($winnerlist),
		'GROUPS'	=> $contest['GROUPS'],
		'AMOUNT'	=> $contest['AMOUNT'],
		'CONTESTID'	=> $contestid,
		'CONTEST_NAME'	=> $contest_name,
		'CONTEST_TYPE'	=> contest_get_type($contest['AMOUNT'], $contest['TYPE']),
		'WINNERS'	=> $winnerstr,
		'GUESTLIST'	=> $contest['TYPE'] === 'guestlist',
		'ETICKETS'	=> $contest['TYPE'] === 'eticket',
		'OTHER'		=> $contest['TYPE'] !== 'guestlist' && $contest['TYPE'] !== 'eticket',
		'EVENT_DATE'	=> _datedaytime_get($contest),
		'LOCATION_NAME'	=> ($loc_name = $contest['LOCATIONID'] ? utf8_to_win1252(get_element_title('location', $contest['LOCATIONID'])) : null),
	]);

	$subject = __('template:contest_winners:subject_TEXT',DONT_ESCAPE,[
		'CONTEST_NAME'	=> $contest_name,
		'LOCATION_NAME'	=> $loc_name,
	]);

	return ['ELEMENT'		=> 'contest',
			'ID'			=> $contestid,
			'BODY'			=> $body,
			'EXPIRES'		=> 3 * ONE_DAY,
			'SUBJECT'		=> $subject,
			'TO_USERID'		=> $userid ?? null,
			'TO_NAME'		=> $contactname ?? null,
			'TO_EMAIL'		=> $email ?? null,
			'DESTINATION'	=> !empty($email) ? 'email' : (!empty($userid) ? 'userid' : null),
			'FORCE_EMAIL'	=> true
	];
}

function template_for_contest_creation($contestid) {
	$contest = db_single_assoc(['contest','party','contestcode'],'
		SELECT	contest.NAME, GROUPS, AMOUNT, TYPE, CLOSE_STAMP, contest.TICKETID, CONTACT, ADDGOING,
			PARTYID, STAMP, STAMP_TZI, AT2400, NOTIME, party.NAME AS PARTY_NAME, GROUP_CONCAT(CODE) AS CODES,
			LOCATIONID
		FROM contest
		LEFT JOIN party USING (PARTYID)
		LEFT JOIN contestcode
		  ON contestcode.CONTESTID = contest.CONTESTID
		 AND contestcode.USERID = 0
		WHERE contest.CONTESTID = '.$contestid,
		DB_USE_MASTER
	);
	if ($contest === false) {
		return false;
	}
	if (!$contest) {
		register_error('contest', $contestid);
		return false;
	}

	[$contactname, $email, $userid] = get_contact_info($contest);

	$contest_name = utf8_to_win1252($contest['NAME'] ?: $contest['PARTY_NAME']);

	$body = __('template:contest_creation:body_TEXT',DONT_ESCAPE,[
		'GROUPS'	=> $contest['GROUPS'],
		'AMOUNT'	=> $contest['AMOUNT'],
		'CONTESTID'	=> $contestid,
		'CLOSE_DATE'	=> _dateday_get($contest['CLOSE_STAMP']),
		'TICKETS'	=> ($tickets = in_array($contest['TYPE'], ['guestlist', 'freeticket', 'eticket', 'scancode'])) ? 1 : 0,
		'ADD_DOUBT'	=> $contest['ADDGOING'] && $tickets,
		'CONTEST_NAME'	=> $contest_name,
		'GUESTLIST'	=> ($guestlist = $contest['TYPE'] === 'guestlist') ? 1 : 0,
		'SCANCODES'	=> $contest['TYPE'] === 'scancode' ? 1 : 0,
		'ETICKETS'	=> $contest['TYPE'] === 'eticket',
		'CODES'		=> $guestlist && $contest['CODES'] ? '- '.implode("\n- ",explode(',',$contest['CODES'])) : null,
		'EVENT_DATE'	=> _datedaytime_get($contest),
		'LOCATION_NAME'	=> ($loc_name = $contest['LOCATIONID'] ? utf8_to_win1252(get_element_title('location',$contest['LOCATIONID'])) : null),
	]);

	$subject = __('template:contest_creation:subject_LINE',DONT_ESCAPE,[
		'CONTEST_NAME'	=> $contest_name,
		'LOCATION_NAME'	=> $loc_name,
	]);

	return ['ELEMENT'		=> 'contest',
			'ID'			=> $contestid,
			'BODY'			=> $body,
			'SUBJECT'		=> $subject,
			'TO_USERID'		=> $userid ?? null,
			'TO_NAME'		=> $contactname ?? null,
			'TO_EMAIL'		=> $email ?? null,
			'FORCE_EMAIL'	=> true,
	];
}

function template_for_camera_request(int $partyid, array $userids, ?string $element = null, int $id = 0) {
	if (!$party = memcached_party_and_stamp($partyid)) {
		if ($party !== false) {
			register_nonexistent('party',$partyid);
		}
		return false;
	}
	$camera = db_single_assoc('camera','
		SELECT GLISTPLUS,CONSUMPTIONS,TRAVEL_EXPENSES,REWARD_EXTERNAL,OFFER
		FROM camera
		WHERE PARTYID='.$partyid
	);
	if (!$camera) {
		if ($camera !== false) {
			register_nonexistent('camerarequest',$partyid);
		}
		return false;
	}
	require_once '_rights.inc';

	if ($element === 'photographer') {
		$info['CAMREQ_USERID'] = $userids[0];
		$body = null;
	} else {
		if (!($users = db_rowuse_array('user','
			SELECT USERID, EMAIL, SEX, REALNAME
			FROM user
			WHERE USERID IN ('.implode(', ', $userids).')'
		))) {
			_error('Geen fotografen gevonden!');
			return false;
		}
		if ($id) {
			if (!($info = db_single_assoc($element,'
				SELECT CAMREQ_USERID, CAMREQ_NAME, CAMREQ_EMAIL, EMAIL
				FROM '.$element.'
				WHERE '.strtoupper($element).'ID = '.$id
			))) {
				register_nonexistent($element,$id);
				return false;
			}
		} elseif ($userid = have_idnumber($_REQUEST,'tUSERID')) {
			if (!($info = db_single_assoc('user','
				SELECT EMAIL AS CAMREQ_EMAIL, IF(REALNAME, REALNAME, IF(NAME, NAME, NICK)) AS CAMREQ_NAME
				FROM user
				WHERE USERID = '.$userid
			))) {
				$info = [];
			}
			$info['CAMREQ_USERID'] = $userid;
		}
		foreach ($users as $user) {
			$cammers[] = make_clean_realname($user['REALNAME']).' ('.$user['EMAIL'].')';
		}
		$camcnt = $camtotal = count($cammers);
		$camstr = '';
		foreach ($cammers as $cammer) {
			--$camcnt;
			if ($camstr) {
				$camstr .= $camcnt ? ', ' : ' en ';
			}
			$camstr .= $cammer;
		}
		extract($camera);
		if ($OFFER) {
			$specstr = 'Geldt het aanbod nog?';
			if ($GLISTPLUS) {
				$specparts[] = '+'.$GLISTPLUS.' '.element_name('guestlist');
			}
			if ($CONSUMPTIONS !== null) {
				$specparts[] =
					$CONSUMPTIONS
				?	$CONSUMPTIONS.' '.element_name('free_consumption',$CONSUMPTIONS)
				:	element_plural_name('free_consumption');
			}
			if ($TRAVEL_EXPENSES == 1) {
				$specparts[] = element_name('travel_allowance');
			}
			if ($REWARD_EXTERNAL !== null) {
				$specparts[] =
					$REWARD_EXTERNAL
				?	$REWARD_EXTERNAL.' euro '.element_name('compensation')
				:	element_name('compensation');
			}
			if (isset($specparts)) {
				$specstr .= ' ('.implode(', ',$specparts).')';
			}
		} else {
			$specstr = "Is het mogelijk om ".($camtotal == 1 ? ($users[0]['SEX'] == 'F' ? 'haar' : 'hem') : 'hen')." en een +1 op de perslijst te zetten?\n";
		}

		static $__counter = array('geen','een','twee','drie','vier','vijf','zes','zeven');
		$body =ucfirst(getifset($__counter,$camtotal) ?: $camtotal).' van onze fotografen, '.$camstr.
			', '.($camtotal > 1 ? 'hebben' : 'heeft').' aangegeven graag foto\'s te maken op '.
			utf8_to_win1252($party['NAME']).
			($party['LOCATION_NAME'] ? ', '.utf8_to_win1252($party['LOCATION_NAME']) : '').
			($party['CITY_NAME'] ? ', '.utf8_to_win1252($party['CITY_NAME']) : '').
			', '.
			(	$party['DURATION_SECS'] > ONE_DAY
			?	'#!MEERDAAGS_EVENEMENT_VRAAG_EEN_DAG_OF_ALLE_DAGEN_AAN!#'
			:	_datedaytime_get($party)
			).'.'."\n\n".

			"De foto's zullen gepubliceerd worden in Appic en op Partyflock. Ze zullen in onze algemene galerij te zien zijn, uitgelicht op de voorpagina, bij de organisatie, locatie en het evenement zelf.\n".
			"Mooie reclame en aandacht voor jullie evenement.\n\n".

			"$specstr\n".

			"Willen jullie de foto's voor publicatie keuren? Laat het ons weten. Je kunt altijd ook achteraf bepalen dat bepaalde foto's verwijderd moeten worden.\n\n".
			"Als het niet kan of jullie geen behoefte hebben, laat het ook even weten, dan kunnen we terugkoppelen aan de fotograaf en kan deze andere plannen trekken.\n\n".

			"Mocht onstage een mogelijkheid zijn, dan wordt dat zeer op prijs gesteld. Dat geeft potentieel beter beeld van het evenement.\n".
			"Parkeerkaart en wat muntjes zal de fotograaf zeker waarderen.\n\n".

			"Ik verneem graag je reactie!";
	}

	$subject = 'Verzoek toestemming fotograaf Appic & Partyflock voor '.utf8_to_win1252($party['NAME']);

	return ['ELEMENT'		=> 'camerarequest',
			'ID'			=> $partyid,

			'tACTION'		=> 'requestfor',
			'tREQUESTFOR'	=> $userids,
			'tPARTYID'		=> $partyid,
			'tTYPE'			=> $element,
			'tID'			=> $id,

			'BODY'			=> $body,
			'SUBJECT'		=> $subject,
			'TO_USERID'		=> !empty($info['CAMREQ_USERID']) ? $info['CAMREQ_USERID'] : null,
			'TO_NAME'		=> !empty($info['CAMREQ_NAME'])   ? $info['CAMREQ_NAME']   : null,
			'TO_EMAIL'		=> !empty($info['CAMREQ_EMAIL'])  ? $info['CAMREQ_EMAIL']  : (!empty($info['EMAIL']) ? $info['EMAIL'] : null),

			'DESTINATION'	=> !empty($info['CAMREQ_EMAIL']) ? 'email' : (!empty($info['CAMREQ_USERID']) ? 'userid' : null),

			'FORCE_EMAIL'	=> true,
	];
}


function template_for_camera_offer(int $partyid): array|false {
	if (!($name = memcached_party($partyid))) {
		return false;
	}
	return ['ELEMENT'	=> 'camerarequest',
			'ID'		=> $partyid,
			'BODY'		=> __('template:camera_offer:body_TEXT',	DONT_ESCAPE),
			'SUBJECT'	=> __('template:camera_offer:subject_LINE', DONT_ESCAPE, ['NAME' => $name]),
	];
}

function template_for_camera_thanks($partyid) {
	$name = memcached_party($partyid);
	if (!$name) {
		return false;
	}
	$body = "Bedankt dat er een Partyflock fotograaf langs mag komen!\n".
		"\n".
		"Even ter verduidelijk: De fotograaf is niet bij ons in dienst en geeft ons een licentie om de foto's op Partyflock te gebruiken, wij als Partyflock hebben dus geen zeggenschap over ander gebruik van de foto's.\n".
		"Wil je de foto's gebruiken, bijvoorbeeld op eigen site of op flyers? Neem dan altijd eerst contact op met de fotograaf!\n".
		"\n".
		FULL_HOST.get_element_href('party', $partyid);

	return ['ELEMENT'	=> 'camerarequest',
			'ID'		=> $partyid,
			'BODY'		=> $body,
			'SUBJECT'	=> 'Cameraverzoek Partyflock voor '.addslashes($name),
	];
}

function template_for_camera_published(int $partyid): array|false {
	if (!($name = memcached_party($partyid))) {
		return false;
	}

	if (!($galleryids = db_simpler_array('gallery', 'SELECT GALLERYID FROM gallery WHERE PARTYID = '.$partyid))) {
		return false;
	}

	$galleries = [];
	foreach ($galleryids as $galleryid) {
		$galleries[] = FULL_HOST.get_element_href('gallery', $galleryid);
	}

	$body = "We hebben de foto's van $name gepubliceerd:\n".
			"\n".
			implode("\n", $galleries)."\n".
			"\n".
			"Bedankt dat er een Partyflock fotograaf langs mocht komen en we hopen dat we volgende keer weer welkom zijn!\n";

	return ['ELEMENT'	=> 'camerarequest',
			'ID'		=> $partyid,
			'BODY'		=> $body,
			'SUBJECT'	=> "Foto's ".addslashes($name)." op Partyflock gepubliceerd",
	];
}

function template_for_creation($element,$id) {
	$elementname = element_name($element);
	$body = "Bedankt voor het aanmelden van een $elementname. ".ucfirst($elementname)." is toegevoegd!\n\n".
		FULL_HOST.get_element_href($element, $id);

	return ['ELEMENT'	=> $element,
			'ID'		=> $id,
			'BODY'		=> $body,
			'SUBJECT'	=> ucfirst($elementname).' toegevoegd',
	];
}
function template_for_element_creation($element,$id) {
	switch ($element) {
	case 'ad':
/*		$ad = db_single_assoc('ad','
			SELECT PSTAMP,news.CSTAMP,news.USERID,NEWSID,STARTSTAMP,STOPSTAMP,IMPRESSIONS
			FROM news
			JOIN newsad USING (NEWSID)
			WHERE ADID='.$id.'
			GROUP BY NEWSADID'
		);
		if (!$ad) {
			return false;
		}
		list(,,,$hour,$mins) = _getdate($ad['STARTSTAMP']);
		$startdate = !$hour && !$mins ? _dateday_get($ad['STARTSTAMP']) : _datedaytime_get($ad['STARTSTAMP']);
		list(,,,$hour,$mins) = _getdate($ad['STOPSTAMP']);
		$stopdate = !$hour && !$mins ? _dateday_get($ad['STOPSTAMP']-1) : _datedaytime_get($ad['STOPSTAMP']);
		require_once '_adlink.inc';
		$body = __('template:newsad_creation:body_TEXT',[
			'STARTDATE'	=> $startdate,
			'STOPDATE'	=> $stopdate,
			'IMPRESSIONS'	=> $ad['IMPRESSIONS'],
			'PUBDATE'	=> _datedaytime_get($news['PSTAMP']),
			'VIEWADLINK'	=> get_adlink($ad)
		]);
		return [
			'ELEMENT'	=> 'newsad',
			'ID'		=> $id,
			'BODY'		=> $body,
	#		'SUBJECT'	=> ucfirst($elementname).' toegevoegd',
		];*/
		return null;
	case 'newsad':
		$news = db_single_assoc(['newsad','news'],'
			SELECT PSTAMP,news.CSTAMP,news.USERID,NEWSID,STARTSTAMP,STOPSTAMP,IMPRESSIONS
			FROM news
			JOIN newsad USING (NEWSID)
			WHERE NEWSADID='.$id.'
			GROUP BY NEWSADID'
		);
		if (!$news) {
			return false;
		}
		[,,,$hour,$mins] = _getdate($news['STARTSTAMP']);
		$startdate = !$hour && !$mins ? _dateday_get($news['STARTSTAMP']) : _datedaytime_get($news['STARTSTAMP']);
		[,,,$hour,$mins] = _getdate($news['STOPSTAMP']);
		$stopdate = !$hour && !$mins ? _dateday_get($news['STOPSTAMP']-1) : _datedaytime_get($news['STOPSTAMP']);
		require_once '_itemlist.inc';
		$body = __('template:newsad_creation:body_TEXT',DONT_ESCAPE,$x = [
			'STARTDATE'	=> $startdate,
			'STOPDATE'	=> $stopdate,
			'IMPRESSIONS'	=> $news['IMPRESSIONS'],
			'PUBDATE'	=> _datedaytime_get($news['PSTAMP']),
			'VIEWLINK'	=> FULL_HOST.'/news/'.$news['NEWSID'].'?ITEMHASH='._item_hash($news,$news['NEWSID']),
			'VIEWADLINK'	=> FULL_HOST.'/newsad/'.$id
		]);
		return [
			'ELEMENT'	=> 'newsad',
			'ID'		=> $id,
			'BODY'		=> $body,
	#		'SUBJECT'	=> ucfirst($elementname).' toegevoegd',
		];
	}
	return null;
}
function template_for_news_creation($id) {
	$news = db_single_assoc('news','
		SELECT PSTAMP,CSTAMP,USERID,NEWSID,
			(SELECT 1 FROM connect WHERE MAINTYPE="news" AND MAINID='.$id.' AND ASSOCTYPE="party" LIMIT 1) AS HAVE_EVENT
		FROM news
		WHERE NEWSID='.$id.'
		GROUP BY NEWSID'
	);
	if (!$news) {
		return false;
	}
	require_once '_itemlist.inc';
	$body = __('template:news_creation:body_TEXT',DONT_ESCAPE,[
		'FUTURE'	=> $news['PSTAMP'] > CURRENTSTAMP ? 1 : 0,
		'PUBDATE'	=> _datedaytime_get($news['PSTAMP']),
		'VIEWLINK'	=> FULL_HOST.'/news/'.$id.'?ITEMHASH='._item_hash($news,$id),
		'HAVE_EVENT'	=> $news['HAVE_EVENT']
	]);
	return [
		'ELEMENT'	=> 'news',
		'ID'		=> $id,
		'BODY'		=> $body,
#		'SUBJECT'	=> ucfirst($elementname).' toegevoegd',
	];
}

function template_for_advertising_information(): array|false {
	if (!require_admin(['newsad', 'ad', 'promo', 'job'])) {
		return false;
	}
	return [
		'BODY'		=> __('template:advertising_info:body_TEXT', DONT_ESCAPE),
		'SUBJECT'	=> __('template:advertising_info:subject_LINE', DONT_ESCAPE),
	];
}

function template_obtain() {
	if (!isset($_REQUEST['USETEMPLATE'])) {
		return null;
	}
	switch ($_REQUEST['USETEMPLATE']) {
	case 'notpaid':
		if (!($id = require_idnumber($_REQUEST,'ID'))) {
			return false;
		}
		$template = template_for_late_invoice($id);
		break;
	case 'news_creation':
		if (!($id = require_idnumber($_REQUEST,'ID'))) {
			return false;
		}
		$template = template_for_news_creation($id);
		break;
	case 'newsad_creation':
		if (!($id = require_idnumber($_REQUEST,'ID'))) {
			return false;
		}
		$template = template_for_element_creation('newsad',$id);
		break;
	case 'ad_creation':
		if (!($id = require_idnumber($_REQUEST,'ID'))) {
			return false;
		}
		$template = template_for_element_creation('ad',$id);
		break;
	case 'creation':
		if (!require_idnumber($_REQUEST,'ID')
		||	!require_element($_REQUEST, 'ELEMENT', ['location', 'organization', 'artist', 'contest'])
		) {
			return false;
		}
		$template =
			$_REQUEST['ELEMENT'] == 'contest'
		?	template_for_contest_creation($_REQUEST['ID'])
		:	template_for_creation($_REQUEST['ELEMENT'],$_REQUEST['ID']);
		break;
	case 'promocost':
		if (!require_idnumber($_REQUEST,'PROMOID')) {
			return false;
		}
		$template = template_for_promo_cost($_REQUEST['PROMOID']);
		break;
	case 'generic':
		if (!require_element($_REQUEST, 'ELEMENT', ['promo', 'job'])
			||	!require_idnumber($_REQUEST,'ID')
		) {
			return false;
		}
		$template = template_for_generic($_REQUEST['ELEMENT'],$_REQUEST['ID']);
		break;
	case 'jobcost':
		if (!require_idnumber($_REQUEST,'JOBID')) {
			return false;
		}
		$template = template_for_job_cost($_REQUEST['JOBID']);
		break;

	case 'contestresult':
		if (!require_idnumber($_REQUEST,'CONTESTID')) {
			return false;
		}
		$template = template_for_contest_result($_REQUEST['CONTESTID']);
		break;

	case 'camerarequest':
		if (!require_idnumber($_REQUEST,'ID')
		||	!require_number_array($_REQUEST,'REQUESTFOR')
		) {
			return false;
		}
		if (!($tTYPE = require_element($_REQUEST, 'tTYPE', array('organization', 'location', 'photographer', 'other', 'ticket', 'offer')))) {
			return;
		}
		$template = template_for_camera_request($_REQUEST['ID'],$_REQUEST['REQUESTFOR'],$tTYPE,have_idnumber($_REQUEST,'tID'));
		break;

	case 'cameraoffer':
		if (!require_idnumber($_REQUEST,'PARTYID')) {
			return;
		}
		$template = template_for_camera_offer($_REQUEST['PARTYID']);
		break;

	case 'camerathanks':
		if (!require_idnumber($_REQUEST,'PARTYID')) {
			return;
		}
		$template = template_for_camera_thanks($_REQUEST['PARTYID']);
		break;

	case 'camerapublished':
		if (!require_idnumber($_REQUEST,'PARTYID')) {
			return;
		}
		$template = template_for_camera_published($_REQUEST['PARTYID']);
		break;

	default:
		_error('Onbekend template type &quot;'.escape_specials($_REQUEST['USETEMPLATE']).'&quot;');
		return false;
	}
	if (!$template) {
		register_error('tempate:error:could_not_generate_template_LINE');
		return null;
	}
	$template['tTEMPLATED'] = '1';
	return $template;
}
function template_passthrough($template) {
	if ($template === true) {
		$template = $_POST;
	}
	foreach ($template as $key => $val) {
		if ($key[0] != 't') {
			continue;
		}
		if (is_array($val)) {
			foreach ($val as $vval) {
				?><input type="hidden" name="<?= $key ?>[]" value="<?= $vval ?>" /><?
			}
		} else {
			?><input type="hidden" name="<?= $key ?>" value="<?= $val ?>" /><?
		}
	}
	if (isset($_REQUEST['USETEMPLATE'])) {
		?><input type="hidden" name="TEMPLATEUSED" value="<?= escape_specials($_REQUEST['USETEMPLATE']); ?>" /><?
	}
}
/*function template_extra_form_parts($template) {
	if (!isset($template['tACTION'])) {
		return;
	}
	switch ($template['tACTION']) {
	case 'requestfor':
		if (!empty($template['TO_USERID'])
		||	!empty($template['TO_NAME'])
		||	!empty($template['TO_EMAIL'])
		) {
			if ($template['TO_USERID']) {
				layout_start_row();
				echo Eelement_name('contact_user');
				layout_field_value();
				echo get_element_link('user',$template['TO_USERID']);
				layout_stop_row();
			}
			if ($template['TO_EMAIL']) {
				layout_start_row();
				echo Eelement_name('contact_email');
				layout_field_value();
				print_email_link($template['TO_EMAIL'],$template['TO_NAME']);
				layout_stop_row();
			}
			$havecontact = true;
			?></td></tr><tr style="border:1px solid <?= LITE ? 'black' : 'white'; ?>;padding-bottom:.3em"></tr><?
		}
		if (!$template['tID']) {
			break;
		}
		break;
	}
}*/
function template_post_process($ticketid,$ctmsgid = 0) {
	if ($ctmsgid
	&&	!empty($_REQUEST['TEMPLATEUSED'])
	) {
		db_update('contact_ticket_message','UPDATE contact_ticket_message SET TEMPLATE="'.addslashes($_REQUEST['TEMPLATEUSED']).'" WHERE CTMSGID='.$ctmsgid);
	}
	if (!isset($_POST['tACTION'])) {
		return true;
	}
	switch ($_POST['tACTION']) {
	case 'promocost':
		if (!require_idnumber($_POST,'tPROMOID')
		||	!require_admin('promo')
		||	!db_update('promo','
			UPDATE promo SET
				COSTSENT='.CURRENTSTAMP.',
				TICKETID=IF(TICKETID=0,'.$ticketid.',TICKETID)
			WHERE PROMOID='.$_POST['tPROMOID'])
		) {
			return false;
		}
		break;
	case 'jobcost':
		if (!require_idnumber($_POST,'tJOBID')
		||	!require_admin('job')
		||	!db_update('job','
			UPDATE job SET
				COSTSENT='.CURRENTSTAMP.',
				TICKETID=IF(TICKETID=0,'.$ticketid.',TICKETID)
			WHERE JOBID='.$_POST['tJOBID'])
		) {
			return false;
		}
		break;

	case 'requestfor':
		if (!require_number_array($_POST,'tREQUESTFOR')
		||	!require_admin('camerarequest')
		||	!require_idnumber($_POST,'tPARTYID')
		||	!db_insert('camera_log','
			INSERT INTO camera_log
			SELECT * FROM camera
			WHERE PARTYID='.$_POST['tPARTYID'])
		||	!db_update('camera','
			UPDATE camera SET
				WRITTEN		='.CURRENTSTAMP.',
				MSTAMP		='.CURRENTSTAMP.',
				MUSERID		='.CURRENTUSERID.'
			WHERE PARTYID='.$_POST['tPARTYID'])
		||	!db_insert('camerarequest_log','
			INSERT INTO camerarequest_log
			SELECT * FROM camerarequest
			WHERE USERID IN ('.($useridstr = implode(',',$_POST['tREQUESTFOR'])).')
			  AND PARTYID='.$_POST['tPARTYID'])
		||	!db_update('camerarequest','
			UPDATE camerarequest SET 
				REQUESTED	='.CURRENTSTAMP.',
				MUSERID		='.CURRENTUSERID.',
				MSTAMP		='.CURRENTSTAMP.'
			WHERE USERID IN ('.$useridstr.')
			  AND PARTYID = '.$_POST['tPARTYID'])
		) {
			return false;
		}
		foreach ($_POST['tREQUESTFOR'] as $requestfor) {
			if (!db_insert('camerarequest_ticket','
				INSERT IGNORE INTO camerarequest_ticket SET
					TICKETID	='.$ticketid.',
					REQUESTFOR	="'.implode(',',$_POST['tREQUESTFOR']).'",
					ASKTYPE		="'.addslashes($type = getifset($_POST,'tTYPE')).'",
					ASKID		='.($type == 'photographer' ? $_POST['tREQUESTFOR'][0] : have_idnumber($_POST,'tID')))
			) {
				return false;
			}
		}
		break;
	}
	return true;
}
