<?php

const BANNER_SIZE_TOPBOTTOM			= 1;
const BANNER_SIZE_LEADERBOARD		= 2;
const BANNER_SIZE_TOWER				= 3;
const BANNER_SIZE_RECTANGLE			= 4;
const BANNER_SIZE_LARGERECTANGLE	= 5;
const BANNER_SIZE_HUGERECTANGLE		= 6;
const BANNER_SIZE_TEXTLINK			= 7;
const BANNER_SIZE_INLINE			= 8;
const BANNER_SIZE_FRONTFLYER		= 9;
const BANNER_SIZE_MENUMINI			= 10;
const BANNER_SIZE_BILLBOARD			= 11;
const BANNER_SIZE_OVERLAY			= 12;
const BANNER_SIZE_WIDETOWER			= 13;
const BANNER_SIZE_RIGHTFLYER		= 14;
const BANNER_SIZE_SMALL_MOBILE		= 15;
const BANNER_SIZE_LARGE_MOBILE		= 16;
const BANNER_SIZE_HUGE_MOBILE		= 17;
const BANNER_SIZE_LAST				= 17;

const FLYER_WIDTH_FRONTPAGE			= 200;

const DISABLED_BANNER_SIZES = [
	BANNER_SIZE_RECTANGLE,
	BANNER_SIZE_LARGERECTANGLE,
	BANNER_SIZE_TOPBOTTOM,
];

# NOTE if need be, we could combine the params and encrypt them and make a link of it somehow

const ADLINK_HUSSLE = '(?:upplnk|[\w\d]{7}t[\w\d]{8})';

function get_adlink_href(
	int	        	$adid,
	int	        	$incid,
	int		        $uniq,
	int|string|null	$arg	= null,
): string {
	require_once '_password.inc';
	$random = create_password(size: 16, __specials: false);
	$pre = substr($random, 0, 7);
	$post = substr($random, 7, 8);
	return '/'.$pre.'t'.$post.'/'.$adid.'/'.$incid.'/'.$uniq.'/'.($arg ?: '');
	# return '/upplnk/'.$adid.'/'.$incid.'/'.$uniq.'/'.($arg ?: '');
}

const COUNTAD_FULL_RE	= '(?<countad>\d+/\d+/\d+)';
const COUNTAD_RE	    = '(?:(?<adid>\d+)/(?<incid>\d+)/(?<uniq>\d+))';

function get_adlink_href_from_countad(string $countad, int|string|null $arg): ?string {
	if (preg_match('"^'.COUNTAD_RE.'$"', $countad, $match)) {
		return get_adlink_href(
			(int)$match['adid'],
			(int)$match['incid'],
			(int)$match['uniq'],
			$arg
		);
	}
	return null;
}

function have_banner_size(array $arr, string $field): bool {
	if (!have_idnumber($arr, $field)
	||	$arr[$field] <= 0
	||	$arr[$field] > BANNER_SIZE_LAST
	) {
		return false;
	}
	return true;
}
function require_banner_size(array $arr, string $field): bool {
	if (false === require_number($arr, $field)) {
		return false;
	}
	if ($arr[$field] <= 0
	||	$arr[$field] > BANNER_SIZE_LAST
	) {
		register_error('banner:error:invalid_banner_size_LINE', [
			'NAME'	=> $field,
			'VALUE'	=> $arr[$field]
		]);
		return false;
	}
	return true;
}

function banner_size_options(int $selected = 0): void {
	for ($size = 1; $size <= BANNER_SIZE_LAST; ++$size) {
		if ($selected !== $size
		&&	in_array($size, DISABLED_BANNER_SIZES, true)
		) {
			# skip
			continue;
		}
		$size_name[$size] = banner_size_name($size);
	}
	asort($size_name)
	?><option></option><?
	foreach ($size_name as $size => $name) {
		?><option<?
		if ($selected === $size) {
			?> selected<?
		}
		?> value="<?= $size ?>"><?= $name ?></option><?
	}
}

function banner_size_name(int $size): string {
	return match($size) {
		BANNER_SIZE_TOPBOTTOM		=> __('bannersize:top_small'),
		BANNER_SIZE_LEADERBOARD		=> __('bannersize:leaderboard'),
		BANNER_SIZE_TOWER			=> __('bannersize:skyscraper'),
		BANNER_SIZE_RECTANGLE		=> __('bannersize:rectangle'),
		BANNER_SIZE_LARGERECTANGLE	=> __('bannersize:large_rectangle'),
		BANNER_SIZE_HUGERECTANGLE	=> __('bannersize:huge_rectangle'),
		BANNER_SIZE_TEXTLINK		=> __('bannersize:textlink'),
		BANNER_SIZE_INLINE			=> __('bannersize:inline'),
		BANNER_SIZE_FRONTFLYER		=> __('bannersize:flyer_front'),
		BANNER_SIZE_MENUMINI		=> __('bannersize:menu_button'),
		BANNER_SIZE_BILLBOARD		=> __('bannersize:billboard'),
		BANNER_SIZE_OVERLAY			=> __('bannersize:overlay'),
		BANNER_SIZE_WIDETOWER		=> __('bannersize:skyscraper_wide'),
		BANNER_SIZE_RIGHTFLYER		=> __('bannersize:flyer_small'),
		BANNER_SIZE_SMALL_MOBILE	=> __('bannersize:mobile_small'),
		BANNER_SIZE_LARGE_MOBILE	=> __('bannersize:mobile_large'),
		BANNER_SIZE_HUGE_MOBILE		=> __('bannersize:mobile_huge'),
	};
}

const BANNER_SIZE_TO_DIMENSION = [
	BANNER_SIZE_TOPBOTTOM		=> [ 468,  60],
	BANNER_SIZE_LEADERBOARD		=> [ 728,  90],
	BANNER_SIZE_WIDETOWER		=> [ 160, 600],
	BANNER_SIZE_TOWER			=> [ 120, 600],
	BANNER_SIZE_RECTANGLE		=> [ 180, 150],
	BANNER_SIZE_LARGERECTANGLE	=> [ 300, 250],
	BANNER_SIZE_HUGERECTANGLE	=> [ 336, 280],
	BANNER_SIZE_MENUMINI		=> [ 140, 140],
	BANNER_SIZE_BILLBOARD		=> [ 970, 250],
	BANNER_SIZE_OVERLAY			=> [1000, 800],
	BANNER_SIZE_RIGHTFLYER		=> [ 160, 300],
	BANNER_SIZE_SMALL_MOBILE	=> [ 320,  50],
	BANNER_SIZE_LARGE_MOBILE	=> [ 320, 100],
	BANNER_SIZE_HUGE_MOBILE		=> [ 320, 240],
];

function banner_get_width_height(int $size, ?string $data = null): array {
	if (isset(BANNER_SIZE_TO_DIMENSION[$size])) {
		return BANNER_SIZE_TO_DIMENSION[$size];
	}
	if ($size === BANNER_SIZE_FRONTFLYER) {
		$width  = 0;
		$height = 0;
		if ($data) {
			foreach (['width', 'height'] as $dim) {
				if (preg_match("!$dim\s*=\s*\"(\d+)\"|$dim\s*:\s*(\d+)!", $data, $match)) {
					$$dim = $match[2] ?? $match[1];
				}
			}
		}
		if ($width && $height) {
			return [$width, $height];
		}
		return [336, 500];
	}
	return [0, 0];
}

function get_banner_size(array $banner): int {
	$banners = [];
	foreach (BANNER_SIZE_TO_DIMENSION as $size => [$width, $height]) {
		$banners[$width][] = [$height, $size];
	}
	foreach ($banners as $width => $heights) {
		if ($width !== $banner['WIDTH']) {
			continue;
		}
		if (!isset($heights[1])) {
			# Only one size for this width
			return $heights[0][1];
		}
		$distance = [];
		foreach ($heights as [$height, $size]) {
			$distance[$size] = abs($height - $banner['HEIGHT']);
		}
		asort($distance);
		return array_key_first($distance);
	}
	return 0;
}

function display_banner(
	int|array $arg,
	bool	  $free	= false,
	int|bool  $uniq	= false,
	int		  $incid   = 0,
	string	  $loading = 'lazy',
): int|false {
	if (!$arg) {
		return false;
	}
	require_once '_browser.inc';
	require_once '_smallscreen.inc';
	require_once '_adstuff.inc';

	if (is_int($arg)) {
		/** @var array<string,mixed> $banner */
		if (!($banner = memcached_single_assoc('banner', "
			SELECT BANNERID, ADBOX, TYPE, WIDTH, HEIGHT, SIZETYPE, CLICKTAG
			FROM banner
			WHERE BANNERID = $arg"))
		) {
			return 0;
		}
	} else {
		$banner = $arg;
	}
	require_once '_adcount.inc';
	if ($uniq === true) {
		/** @noinspection PhpRedundantOptionalArgumentInspection */
		$uniq = safe_random_int(0, PHP_INT_MAX);
	}
	ob_start();
	if ($partyid = $banner['PARTYID'] ?? null) {
		$banner['TYPE'] = 'partylist';
	} else {
		$bannerid = $banner['BANNERID'] ?? null;
	}

	$flags =
		empty($_REQUEST['sELEMENT'])
	||	$_REQUEST['sELEMENT'] === 'ad'
	||	$_REQUEST['sELEMENT'] === 'banner'
	?	DB_FRESHEN_MEMCACHE
	:	0;

	if ($overlay
	=	$uniq
	&&	(	# party-ad is an example without SIZETYPE
			!empty($banner['SIZETYPE'])
		&&	$banner['SIZETYPE'] === BANNER_SIZE_OVERLAY
		||	!empty($banner['POSITION'])
		&&	$banner['POSITION'] === ADPOS_OVERLAY
		)
	) {
		if (SMALL_SCREEN
		&& 	$banner['BANNERID'] === 4731
		) {
			$banner['WIDTH']  /= 2.2;
			$banner['HEIGHT'] /= 2.2;
		}
		?><div<?
		?> class="hidden bgov"<?
		?> id="verlay"<?
		?> data-width="<?= $banner['WIDTH'] ?>"<?
		?> data-height="<?= $banner['HEIGHT'] ?>"<?
		?> data-content="verlayimg"<?
		?> onclick="this.parentNode.removeChild(this);"><?
		?><table><tr><td><?
		echo get_close_char([
			'class'	=> 'large rtop',
			'style'	=> 'top:1em;right:1em',
		]);
	}

	switch ($banner['TYPE']) {
	case 'partylist':
		require_once '_affiliates.inc';
		require_once '_buybutton.inc';
		require_once '_currency.inc';
		require_once '_favourite.inc';
		require_once '_genres.inc';
		require_once '_party.inc';
		require_once '_partyinfo.inc';
		require_once '_resolution.inc';
		require_once '_star.inc';
		require_once '_uploadimage.inc';

		if ($uniq) {
			$incid = allow_adcount($banner, $free, $uniq, true);
		}

		if (!($party = memcached_party_and_stamp($partyid))) {
			mail_log("party $partyid does not exist for party list banner $bannerid", get_defined_vars());
			break;
		}

		$favs = get_favourites() ?: [];
		$lineup = get_party_lineup($partyid);

		$href = $uniq ? get_adlink_href($banner['ADID'], $incid, $uniq) : get_element_href('party', $partyid);
		$GLOBALS['partyad_link'] = $href;
		$open = '<a rel="nofollow" target="_blank" href="'.$href.'">';

		?><div class="fw relative spotl"><?

		$above = SMALL_SCREEN;
		$opref = $above ? ['landscape','square'] : ['square','portrait'];
		$flags = UPIMG_NOCHANGE | UPIMG_PREFER_ORIENTATION;

		# prefer orientation to generated/crop or not

		if (!($img = uploadimage_get('party', $partyid, 'regular@2x', $flags, $opref))) {
			if (!($img = uploadimage_get('party2', $partyid, 'regular@2x', $flags, $opref))) {
				if (have_admin()) {
					?><div class="warning block">Geen afbeelding bij evenement!</div><?
				}
			}
		}

		$buy_button = true;

		if ($img) {
			if (empty($img['HEIGHT'])) {
				error_log_r($img,'banner has no HEIGHT');
			}
			$res = get_resolution();
			$w = $res ? ($above ? ($res[4] * get_dpr() ?: 600) : ($res[4] <= 1024 ? 200 : 300)) : 0;

/*				SMALL_SCREEN
			&&	(	$img['WIDTH'] > 2 * $img['HEIGHT']
				||	(require_once '_resolution.inc')
				&&	($res = get_resolution())
				&&	$img['WIDTH'] > .4  * $res[1]
				);*/

			if ($above) {
				?><div class="block"><? uploadimage_show_from_img($img, UPIMG_NO_OPENGRAPH, $open, '</a>', 0, $w) ?></div><?
				show_buy_buttons([
					'force_customer'	=> true,
					'class'				=> 'center block',
					'partyid'			=> $partyid,
					'ad'				=> true,
					'hide_ticketswap'	=> true,
				]);
				$buy_button = false;
				?><div style="center block"><?
			} else {
				?><div class="l"><?
				uploadimage_show_from_img($img, UPIMG_NO_OPENGRAPH, $open, '</a>', $w);

				if (empty($img['SHOWN_WIDTH'])) {
					$img['SHOWN_WIDTH'] = 300;
					?><div style="<?
					?>width:100%;<?
					?>border:1px dashed red;<?
					?>max-width:300px;<?
					?>font-size:500%;<?
					?>font-weight:1000;<?
					?>color:red;<?
					?>text-align:center;<?
					?>">?</div><?
				}

				?><div style="margin-top: .5em; width: <?= $img['SHOWN_WIDTH'] ?>px;"><?
				show_buy_buttons([
					'force_customer'	=> true,
					'class'				=> 'center fw',
					'partyid'			=> $partyid,
					'ad'				=> true,
					'hide_ticketswap'	=> true,
				]);
				?></div><?
				$buy_button = false;
				?></div><?

				?><div class="party-desc" style="margin-left: <?= $img['SHOWN_WIDTH'] ?>px;"><?
			}
		}
		?><div class="<?
		if ($above) {
			?>center <?
		}
		?>"><?
		?><div class="ib info"><?
		?><b><?= $open ?><h2><?= escape_utf8($party['NAME']) ?></h2><?
		if ($party['SUBTITLE']) {
			if ($above) {
				?><br /><?
			} else {
				?> <?= MIDDLE_DOT_ENTITY ?> <?
			}
			echo escape_utf8($party['SUBTITLE']);
		}
		?></a></b><?
		if (isset($favs['organization'])
		&&	($orgs = memcached_same_hash('connect',"
					SELECT ASSOCID
					FROM connect
					WHERE MAINTYPE = 'party'
					  AND MAINID = $partyid
					  AND ASSOCTYPE = 'organization'",
					TEN_MINUTES))
		&&	($favorgs = array_intersect_key($orgs,$favs['organization']))
		) {
			$favinfo = new favouriteinfo();
			$favinfo->show_stars(['organization' => $favorgs]);
		}
		?><br /><?
		show_party_time($party,false);
		if ($party['LOCATIONID']) {
			?><br /><?
			echo get_element_link('location',$party['LOCATIONID']);
			if (isset($favs['location'][$party['LOCATIONID']])) {
				?> <? echo get_star('location');
			}
		} elseif (!empty($party['UNKNOWN_LOCATION'])) {
			?><br /><?
			echo element_name($party['BOARDINGID'] ? 'unknown_boat' : 'unknown_location');
		}
		if ($party['BOARDINGID']) {
			?><br /><?
			echo get_element_link('boarding',$party['BOARDINGID']);
		}
		if ($party['CITYID']) {
			?>, <? echo get_element_link('city',$party['CITYID']);
		}
		?></div><?
		?></div><?

		if ($buy_button) {
			show_buy_buttons([
				'force_customer'	=> true,
				'class'				=> 'block',
				'partyid'			=> $partyid,
				'hide_ticketswap'	=> true,
			]);
		}

		if ($lineup) {
			$max_show = 30;
			$showlineup = $checklist = [];
			foreach ($lineup as $artistid => $info) {
				if (isset($favs['artist'][$artistid])) {
					$showlineup[$artistid] = $info;
				} else {
					$checklist[$artistid] = $info;
				}
			}
			if (($lineupcnt = count($lineup)) > $max_show) {
				if (isset($checklist)
				&&	($remaining = $max_show - count($showlineup)) > 0
				) {
					if ($counts = memcached_simple_hash(['favourite','user_account','artist'], '
						SELECT ARTISTID, COUNT(*) AS CNT
						FROM artist
						LEFT JOIN favourite
						  ON ELEMENT = "artist"
						 AND ID = ARTISTID
						LEFT JOIN user_account
						  ON user_account.USERID = favourite.USERID
						 AND user_account.STATUS = "active"
						WHERE ARTISTID IN ('.implodekeys(',', $lineup).')
						GROUP BY ARTISTID
						ORDER BY CNT DESC',
						ONE_WEEK
					)) {
						$slice = array_slice($counts, 0, $remaining, true);
						$use = array_intersect_key($lineup, $slice);
						$showlineup += $use;
					}
				}
			} else {
				$showlineup += $checklist;
			}
			foreach ($showlineup as $artistid => &$info) {
				ob_start();
				$is_fav = isset($favs['artist'][$artistid]);
				if ($is_fav) {
					?><span class="bold"><?
				}
				echo get_element_link('artist',$artistid,$info);
				if ($is_fav) {
					?> <?= get_star('artist') ?></span><?
				}
				$info = ob_get_clean();
			}
			unset($info);

			ob_start();
			show_genres($partyid, $party, true, separator: ' ', show: GENRES_SHOW_EXPLICIT);
			if ($data = ob_get_clean()) {
				?><div class="<?
				if ($above) {
					?>center <?
				}
				?>smenu genrebuttons"><?= $data	?></div><?
			}
			if ($party['FREE_ENTRANCE']) {
				?><div class="free-entrance block"><b><?= Eelement_name('free_entrance') ?></b></div><?
			}
			?><div<?
			if ($above) {
				?> class="center"<?
			}
			?>><?
			echo implode(', ',$showlineup);
			if (isset($remaining) && $remaining > 0) {
				echo __('partyad:info:,_and_many_more_LINE', DO_UBB, ['CNT' => $lineupcnt - $max_show]);
			}
			?></div><?
		}
		?></div><?
		if ($img) {
			?></div><?
			?><div class="clear"></div><?
		}
		break;

	case 'link':
		if (!($info = db_single_array('bannerlink', '
			SELECT URL, OBJTYPE
			FROM bannerlink
			WHERE BANNERID = '.$bannerid,
			DB_USE_MASTER))
		) {
			break;
		}
		[$src, $filetype] = $info;
		$seed = crc32($src);

	case 'upload':
		if (!isset($info)) {
			if (!($info = db_single_array('bannerupload','
				SELECT CRC32, FILETYPE, SIZE2x, SIZE
				FROM bannerupload 
				WHERE BANNERID = '.$bannerid,
				DB_USE_MASTER))
			) {
				if ($info === null) {
					error_log('WARNING no bannerupload for BANNERID '.$bannerid);
				}
				break;
			}
			[$seed, $filetype, $x2, $x1] = $info;
			if (!is_high_res() && $x2) {
				$x2 = false;
			}
			if (in_array($_REQUEST['sELEMENT'], ['ad', 'banner'], true)
			&&	(!$x1 || !$x2)
			) {
				(!$x1 ? register_error(...) : register_warning(...))('banner:error:missing_parts_LINE', ['1x' => $x1, '2x' => $x2]);
			}
		}
		if (!isset($seed)) {
			$seed = 0;
		}
		if ($uniq) {
			# NOTE: count_immediately counts robots too
			$incid = allow_adcount($banner, $free, $uniq, count_immediately: false);
		}

		# LEGACY: no need to actually display, just mention legacy connect on the banner form
/*		if ($fallback
		=	($fallbackid = $banner['FALLBACKID'])
		?	($fallback = db_single_assoc('banner','SELECT BANNERID,ADBOX,WIDTH,HEIGHT,TYPE FROM banner WHERE BANNERID='.$fallbackid, DB_USE_MASTER))
		:	false
		) {
			ob_start();
			$fallback = array_merge($banner, $fallback);
			$fallback['FB'] = true;
			$fallback['FALLBACKID'] = 0;
			display_banner($fallback, $free, $uniq, $incid);
			$fallback = ob_get_clean();
		}*/

		if (!($might_resize = SMALL_SCREEN)
		&&	isset($banner['SIZETYPE'])
		) {
			switch ($banner['SIZETYPE']) {
			case BANNER_SIZE_TOPBOTTOM:
			case BANNER_SIZE_LEADERBOARD:
				# to allow fluid on smaller windows
				$might_resize = true;
				break;
			}
		}
		require_once '_hosts.inc';
		switch ($filetype) {
		case 'swiffy':
			$click_url =
				$uniq
			?	($banner['URL'] ? get_adlink_href($banner['ADID'], $incid, $uniq) : '')
			:	"https://{$_SERVER['HTTP_HOST']}/ping";

			?><div class="ib ptr" onclick="return openLink(event, '<?= $click_url ?>', true, true);"><?

			?><iframe<?
				?> loading="<?= $loading ?>"<?
				?> class="as-block centered novents seamless"<?
				?> sandbox="allow-scripts allow-popups"<?
				?> width="<?= $banner['WIDTH'] ?>"<?
				?> height="<?= $banner['HEIGHT'] ?>"<?
				?> src="/images/<?
				$url = $banner['URL'] ?? '';
				$seed = crc32($url);
				if ($uniq && $url) {
					$incid = allow_adcount($banner, $free, $uniq, count_immediately: true);
					echo create_ad_stuff(STUFF_AD)."/{$banner['ADID']}".($uniq ? "i{$uniq}d$incid" : '');
				} else {
					echo create_ad_stuff(STUFF_BANNER, $seed)."/$bannerid";
				}
				if ($seed) {
					?>_<? echo dechex($seed);
				}
				?>.frm<?
				$clicktag = '%63%6C%69%63%6B%54%61%67'; # This says 'clickTag'
				?>?<?= $clicktag ?>=<?= urlencode($click_url)
			?>"><?
			?></iframe><?
			?></div><?
			break;

		case 'avif':
		case 'gif':
		case 'jpg':
		case 'mp4':
		case 'png':
		case 'webp':
			if ($banner['BG'] !== null) {
				?><div class="center" style="background-color: #<? printf('%06X',$banner['BG']) ?>;"><?
			}
			if ($clickurl = $uniq ? get_adlink_href($banner['ADID'], $incid, $uniq) : 'https://'.$_SERVER['HTTP_HOST'].'/ping') {
				?><a target="_blank"<?
				?> rel="nofollow"<?
				?> href="<?= $clickurl ?>"><?
			}
			$data_src = AD_HOST.'/images/'.(
				$incid && $uniq
			?	create_ad_stuff(STUFF_AD)."/{$banner['ADID']}".($uniq ? "i{$uniq}d$incid" : '')
			:	($seed ? create_ad_stuff(STUFF_BANNER, $seed) : 'banner')."/$bannerid"
			).($seed ? '_'.dechex($seed) : '').(empty($x2) ? '' : '@2x').($filetype ? '.'.$filetype : '');

			if ($filetype === 'mp4') {
				?><video<?
				?> autoplay<?
				?> muted<?
				?> loop<?
				?> playsinline<?
				?> width="<?=  $banner['WIDTH'] ?>"<?
				?> height="<?= $banner['HEIGHT'] ?>"<?
				?> class="middle"<?
				?> style="max-width: 100%; width:<?= $banner['WIDTH'] ?>px; height:auto;"<?
				?>><?
					?><source src="<?= $data_src ?>" type="video/mp4" /><?
				?></video><?
			} else {
				?><img loading="<?= $loading ?>" class="middle" <?
				if (aspect_objects()) {
					# Wwidth and height are not needed for aspect objects.
					# Width as HTML attribute is also in stylesheet, so can be missed.
					# Also, AdGuard Dutch filter blocks images with width 140 and 160.
					?>height="<?= $banner['HEIGHT'] ?>" <?
					?>style="max-width: 100%; width: <?= $banner['WIDTH'] ?>px; height:auto;" <?
				} elseif (
					$might_resize
				||	$banner['WIDTH'] > 720
				||	!empty($banner['LIMIT_WIDTH'])
				) {
					?>style="max-width: <?= $banner['LIMIT_WIDTH'] ?? $banner['WIDTH'] ?>px; width: 100%;" <?
				} else {
					?>width="<?= $banner['WIDTH'] ?>" <?
					if (!SMALL_SCREEN) {
						?>height="<?= $banner['HEIGHT'] ?>" <?
					}
				}
				if ($overlay) {
					?>id="verlayimg" <?
					?>data-<?
				}
				?>src="<?= $data_src ?>"<?
				?> /><?
			}

			if ($clickurl) {
				?></a><?
			}
			if ($banner['BG'] !== null) {
				?></div><?
			}
			break;

		case 'swf':
			require_once '_embed.inc';
			$argstr = null;
			if (!isset($banner['CLICKTAG'])) {
				error_log('WARNING clickTAG not set @ '.$_SERVER['REQUEST_URI']);
			} elseif ($banner['CLICKTAG']) {
				$clicktag = $bannerid === 1383 ? 'clickTAG' : '%63%6C%69%63%6B%54%61%67';//'clickTag';
				$argstr =
					$uniq
				?	($banner['URL'] ? $clicktag.'='.urlencode(get_adlink_href($banner['ADID'], $incid, $uniq)) : '')
				:	$clicktag.'='.'https://'.$_SERVER['HTTP_HOST'].'/ping';
			}
			# NOTE: https://stackoverflow.com/questions/6402262/wmode-what-does-it-mean-in-browsers-rendering-behavior
			runembed(
				src:		AD_HOST.'/images/'.(
								$incid && $uniq
							?	create_ad_stuff(STUFF_AD)."/{$banner['ADID']}".($uniq ? "i{$uniq}d$incid" : '')
							:	create_ad_stuff(STUFF_BANNER, $seed)."/$bannerid"
							).($seed ? '_'.dechex($seed) : '').'.swf?'.$argstr,
				width: 		$banner['WIDTH'],
				height:		$banner['HEIGHT'],
				attribs:	['wmode' => 'transparent'],
				class:		'as-block centered',
			);
			break;

		default:
			error_log('unsupported filetype "'.$filetype.'" for banner with id '.$bannerid.' @ '.$_SERVER['REQUEST_URI']);
			break;
		}
		break;

	case 'iframe':
		$url = db_single('banneriframe', '
			SELECT URL
			FROM banneriframe
			WHERE BANNERID = '.$bannerid
		);
		if (!$url) {
			break;
		}
		if ($uniq) {
			$incid = allow_adcount($banner, $free, $uniq);
		}
		$clickurl = $uniq ? get_adlink_href($banner['ADID'], $incid, $uniq) : 'https://'.$_SERVER['HTTP_HOST'].'/ping';

		?><a target="_blank" href="<?= $clickurl ?>"><?
		?><div class="center" style="<?
			?>vertical-align:middle;<?
			?>display:inline-block;<?
			?>user-select:none;<?
			?>max-width:100%;<?
			?>overflow:hidden;<?
			?>width:<?= $banner['WIDTH'] ?>px;<?
			?>height:<?= $banner['HEIGHT'] ?>px;<?
			?>z-index:10000000;<?
		?>"><?

		?><iframe<?
			?> loading="<?= $loading ?>"<?
			?> sandbox="allow-scripts allow-popups"<?
			?> class="novents ib seamless"<?
			?> width="<?= $banner['WIDTH'] ?>"<?
			?> height="<?= $banner['HEIGHT'] ?>"<?
			?> src="/images/<?
			$seed = crc32($url);
			if ($uniq && $banner['URL']) {
				$incid = allow_adcount($banner, $free, $uniq, count_immediately: true);
				echo create_ad_stuff(STUFF_AD)."/{$banner['ADID']}".($uniq ? "i{$uniq}d$incid" : '');
			} else {
				echo create_ad_stuff(STUFF_BANNER, $seed)."/$bannerid";
			}
			if ($seed) {
				?>_<? echo dechex($seed);
			}
			?>.frm<?
		?>"><?
		?></iframe><?

		?></div><?
		?></a><?

		break;
	case 'script':
		$script = db_single_assoc('bannerscript','
			SELECT SCRIPTURL,FIRSTSCRIPTURL,ANCHORURL,IMAGEURL
			FROM bannerscript
			WHERE BANNERID='.$bannerid
		);
		if (!$script) {
			break;
		}
		?><div style="width: <?= $banner['WIDTH'] ?>px; height: <?= $banner['HEIGHT'] ?>px;" class="centered"><?

		$script['FIRSTSCRIPTURL'] = str_replace('#UNIQUE#',$uniq,$script['FIRSTSCRIPTURL']);
		$script['SCRIPTURL'] = str_replace('#UNIQUE#',$uniq,$script['SCRIPTURL']);
		$script['ANCHORURL'] = str_replace('#UNIQUE#',$uniq,$script['ANCHORURL']);
		$script['IMAGEURL']  = str_replace('#UNIQUE#',$uniq,$script['IMAGEURL']);
		if ($script['FIRSTSCRIPTURL']) {
			?><script src="<?= escape_specials($script['FIRSTSCRIPTURL']) ?>"></script><?
		}
		if ($script['SCRIPTURL']) {
			?><script src="<?= escape_specials($script['SCRIPTURL']) ?>"></script><?
		}
		if ($script['ANCHORURL']
		&&	$script['IMAGEURL']
		) {
			?><noscript><?
			?><a<?
			?> rel="nofollow"<?
			?> href="<?= escape_specials($script['ANCHORURL']) ?>"><?
			?><img <?
				?>loading="lazy"<?
				?>class="as-block centered" <?
				?>src="<?= escape_specials($script['IMAGEURL']) ?>" <?
				?>width="<?= $banner['WIDTH'] ?>" <?
				?>height="<?= $banner['HEIGHT'] ?>" <?
			?></a><?
			?></noscript><?
		}
		?></div><?
		break;

	case 'html':
		# dbg
		$html = memcached_single_array('bannerhtml', '
			SELECT BODY, SCRIPT_URL
			FROM bannerhtml
			WHERE BANNERID = '.$bannerid,
			flags: $flags
		);
		if (!$html) {
			break;
		}
		[$body, $script_url] = $html;

		if ($div = true) {
			# force div to catch click
			$classes = ['centered'];
			?><div<?
			if ($banner['BANNERID'] === 6526) {
				$left_offset = 140/4;
				$top_offset = 80/4;
				$styles[] =
					'transform:scale(.5);'.
					'margin-left:-'.$left_offset.'px;'.
					'margin-top:-'.$top_offset.'px;'.
					'width:160px;'.
					'height:120px;';
			} else {
				if ($banner['WIDTH']) {
					$styles[] = 'width:'.$banner['WIDTH'].'px';
				}
				if ($banner['HEIGHT']) {
					$styles[] = 'height:'.$banner['HEIGHT'].'px';
				}
			}
			if (isset($styles)) {
				?> style="<?= implode(';',$styles) ?>"<?
			}
			?> class="<?= implode(' ',$classes) ?>"<?
			?>><?
		}
		if ($uniq) {
			$incid = allow_adcount($banner, $free, $uniq, count_immediately: true);
		}
		require_once '_hosts.inc';

		$countclick = $uniq ? 'https://'.$_SERVER['HTTP_HOST'].get_adlink_href($banner['ADID'], $incid, $uniq) : '';

		foreach ([
			# name		 # utf8
			'script_url'	=> false,
			'body'		=> true
		] as $var => $utf8) {
			if (!$$var
			||	!str_contains($$var, '#')
			&&	!str_contains($$var, '{')
			) {
				continue;
			}
			$mod = $utf8 ? 'u' : '';
			$$var =
				preg_replace('"{IFTHEMELIGHT}(.*?){ELSE}(.*?){ENDIF}"'.$mod, LITE ? '$1' : '$2',
				preg_replace_callback('"{IF LANGUAGE=(\w{2})}(.*?)(?:{ELSE}(.*?))?{ENDIF}"'.$mod, function(array $match): string {
					return $match[CURRENTLANGUAGE === $match[1] ? 2 : 3];
				},
				preg_replace('"{IF HAVEUSER}(.*?)(?:{ELSE}(.*?))?{ENDIF}"'.$mod,	have_user() ? '$1' : '$2',
				preg_replace('"{IF SECURE}(.*?){ELSE}(.*?){ENDIF}"'.$mod,		'$1',
				str_replace('#CURRENTNICKURLENC#',	urlencode($GLOBALS['currentuser']->row['NICK']),
				str_replace('#CURRENTNICK#',		$GLOBALS['currentuser']->row['NICK'],
				str_replace('#CURRENTTHEME#',		CURRENTTHEME,
				str_replace('#CURRENTLANGUAGE#',	CURRENTLANGUAGE,
				str_replace('#CURRENTURL#',			urlencode($_SERVER['REQUEST_URI']),
				str_replace('#RAWCOUNTCLICK',		$countclick ?: 'https://'.$_SERVER['HTTP_HOST'].'/ping',
				str_replace('#COUNTCLICK#',			urlencode($countclick ?: "https://{$_SERVER['HTTP_HOST']}/ping"),
				str_replace('#2X#',					is_high_res() ? '@2x' : '',
				str_replace('#COUNTCLICKABS#',		urlencode($countclick ?: "https://{$_SERVER['HTTP_HOST']}/ping"),
				str_replace('#UNIQUE#',				$uniq ?: 1,
					$$var
			))))))))))))));
		}

		if ($script_url) {
			$seed = crc32($script_url);
			$script_url = '/images/'.(
					$uniq
				?	create_ad_stuff(STUFF_AD)."/{$banner['ADID']}".($uniq ? "i{$uniq}d$incid" : '')
				:	create_ad_stuff(STUFF_BANNER, $seed)."/{$banner['BANNERID']}"
				).($seed ? '_'.dechex($seed) : '').'.js';

			$body = str_replace('#SCRIPT#', '<script src="'.$script_url.'"></script>', $body);
		}

		echo escape_utf8($body);

		if ($div) {
			?></div><?
		}
		break;
	case 'textlinks':
		require_once '_bannertextlinks.inc';
		$links = get_banner_textlinks($bannerid, !$uniq);
		if (!$links) {
			break;
		}
		?><div style="width:<?= $banner['WIDTH'] ?>px;height:<?= $banner['HEIGHT'] ?>px" class="centered"><?
		if ($uniq) {
			/* $incid = */ allow_adcount($banner, $free, $uniq, count_immediately: true);
			$cnt = count($links);
			$chosen = CURRENTSTAMP % $cnt;
			[$text/* , $link*/] = $links[$chosen];
			?><div class="center"><?
			?><a target="_blank"<?
			?> rel="nofollow"<?
			?> href="<?= get_adlink_href($banner['ADID'], $uniq, $chosen) ?>"><?= escape_ascii($text); ?></a><?
			?></div><?
		} else {
			foreach ($links as /* $id => */ [$text, $link]) {
				?><a target="_blank"<?
				?> rel="nofollow"<?
				?> href="<?= $link ?>"><?= escape_ascii($text) ?></a><br /><?
			}
		}
		?></div><?
		break;
	case 'ubb':
		$body = memcached_single('bannerubb','
			SELECT BODY
			FROM bannerubb
			WHERE BANNERID = '.$bannerid,
			flags: $flags
		);
		if (!$body) {
			break;
		}
		if ($uniq) {
			$incid = allow_adcount($banner, $free, $uniq, true);
		}
		require_once '_ubb.inc';
		$body = str_replace(
			'%COUNTCLICK%',
				isset($banner['ADID']) && $uniq
			?	' countad='.$banner['ADID'].'/'.$incid.'/'.$uniq.' '
			:	'',
			$body
		);
		echo make_all_html($body, UBB_UTF8 | (isset($banner['ADID']) ? UBB_COUNTAD : 0));
		break;

	case 'floormovie':
		require_once '_floormoviebanner.inc';
		$incid = allow_adcount($banner, $free, $uniq, true);
		show_floormovie_banner($banner['BANNERID'], $banner['ADID'] ?? 0, $incid, $uniq);
		$uniq = false;
		break;

	case 'multiparty':
		$q = "	SELECT PARTYID, UPIMGID, WIDTH, HEIGHT, FILETYPE, ul.TYPE
				FROM bannermultiparty
				JOIN party USING (PARTYID)
				JOIN uploadimage_link AS ul ON ul.ID = PARTYID AND
					CASE WHAT
					WHEN 'both'  THEN ul.TYPE IN ('party', 'party2')
					WHEN 'front' THEN ul.TYPE = 'party'
					WHEN 'back'  THEN ul.TYPE = 'party2'
					END
				JOIN uploadimagemeta USING (UPIMGID)
				JOIN uploadimage USING (UPIMGID)
				WHERE BANNERID = $bannerid
				  AND STAMP > %CACHESTAMP%
				  AND ORIENTATION = 'portrait'
				  AND SIZE = 'original'";

		if (false === ($all_flyers = db_rowuse_array(['bannermultiparty', 'uploadimage_link', 'uploadimagemeta', 'uploadimage'],
			str_replace('%CACHESTAMP%', CACHESTAMP, $q)
		))) {
			return 0;
		}
		if (!$all_flyers) {
			# fallback if we're a bit late
			if (false === ($all_flyers = db_rowuse_array(['bannermultiparty', 'uploadimage_link', 'uploadimagemeta', 'uploadimage'],
				str_replace('%CACHESTAMP%', 0, $q)
			))) {
				return 0;
			}
			if (!$all_flyers) {
				break;
			}
		}
		$flyer_cnt = count($all_flyers);
		$right = !empty($banner['POSITION']) && $banner['POSITION'] === ADPOS_RIGHT_TOWER;
		$max_width = $right ? 160 : FLYER_WIDTH_FRONTPAGE;
		$max_height = 0;
		foreach ($all_flyers as $flyer) {
			/** @noinspection PhpRedundantOptionalArgumentInspection */
			extract($flyer, \EXTR_OVERWRITE);
			scale($WIDTH, $HEIGHT, $max_width);
			$heights[] = $HEIGHT;
			$max_height = max($max_height, $HEIGHT);
		}
		sort($heights);

		$median_height = $heights[count($heights) >> 1];

		if ($uniq) {
			$incid = allow_adcount($banner, $free, $uniq, true);
		}
		$fronts = [];
		$backs  = [];
		foreach ($all_flyers as $flyer) {
			$partyid = $flyer['PARTYID'];
			if ($flyer['TYPE'] === 'party2') {
				$backs[$partyid] = $flyer;
			} else {
				$fronts[$partyid] = $flyer;
			}
		}
		if (!$backs) {
			$all_flyers = $fronts;
			safe_shuffle($all_flyers);

		} elseif (!$fronts) {
			$all_flyers = $backs;
			safe_shuffle($all_flyers);

		} else {
			foreach ($fronts as $partyid => &$flyer) {
				if (isset($backs[$partyid])) {
					$flyer['back'] = $backs[$partyid];
					unset($backs[$partyid]);
				}
			}
			unset($flyer);
			$tmpallflyers = array_merge($fronts, $backs);
			safe_shuffle($tmpallflyers);
			$all_flyers = [];
			foreach ($tmpallflyers as $flyer) {
				$all_flyers[] = $flyer;
				if (isset($flyer['back'])) {
					$flyer['back']['front'] = $flyer;
					$all_flyers[] = $flyer['back'];
				}
			}
		}

		ob_start();
		foreach ($all_flyers as $ndx => $flyer) {
			/** @noinspection PhpRedundantOptionalArgumentInspection */
			extract($flyer, \EXTR_OVERWRITE);
			scale($WIDTH, $HEIGHT, $max_width);
			 $width_for_url = $WIDTH;
			$height_for_url = $HEIGHT;
			if (is_high_res()) {
				 $width_for_url <<= 1;
				$height_for_url <<= 1;
			}
			?><span<?
			?> data-delay="<?= !empty($flyer['back']) ? 2500 : 3300 ?>"<?
			?> style="left: 0; max-height:<?= $median_height ?>px;<?
			if ($HEIGHT !== $median_height) {
				?>margin-top: <?= ($median_height - $HEIGHT) >> 1 ?>px;<?
			}
			?>"<?
			?> class="fadable middle abs<?
			if ($ndx) {
				?> faded"<?
				?> data-src="<?= IMAGES_HOST ?>/images/<?= $TYPE ?>/<?= $PARTYID ?>_<?= $width_for_url ?>x<?= $height_for_url ?>_<?= $UPIMGID ?>.<? echo $FILETYPE;
			} else {
				?> z1<?
			}
			?>"><?

			?><a target="_blank"<?
			?> rel="nofollow"<?
			?> href="<?= $uniq ? get_adlink_href($banner['ADID'], $incid, $uniq, $PARTYID) :  get_element_href('party', $PARTYID);
			?>"><?

			if (!$ndx) {
				$seed = $UPIMGID;
				$backside = $TYPE === 'party2' ? 'b' : '';
				$src = AD_HOST.'/images/'.(
					$incid && $uniq
					?	create_ad_stuff(STUFF_AD)."/{$banner['ADID']}".($uniq ? "i{$uniq}d$incid" : '')
					:	create_ad_stuff(STUFF_BANNER, $seed)."/$bannerid"
					).($seed ? '_'.dechex($seed) : '')."_$PARTYID{$backside}_{$width_for_url}x$height_for_url.$FILETYPE";

				?><img src="<?= $src ?>" class="fw" /><?
			}
			?></a><?

			?></span><?
		}
		$flyerstr = ob_get_clean();
		?><div<?
		?> class="relative mltprt ib noverflow"<?
		?> style="<?
			?>max-width: 100%; <?
			?>width: <?= $banner['WIDTH'] = $right ? 160 : FLYER_WIDTH_FRONTPAGE ?>px; <?
			?>height: <?= $banner['HEIGHT'] = $median_height ?>px;<?
		?>"><?= $flyerstr ?></div><?

		if ($flyer_cnt > 1) {
			include_js('js/ablada');
		}
		break;

	default:
		error_log('unknown banner type for banner with id '.$bannerid,0);
		break;
	}

	if ($overlay) {
		?></td></tr></table><?
		?></div><?
		include_js('js/verlay');
	}

	$data = ob_get_clean();
	if (!empty($banner['ADBOX'])) {
		layout_open_box('white');
		$adname = element_name('ad');
		layout_box_header(_make_illegible($adname),null,'small');
		?><div class="small abs" style="margin-left: .5em; margin-top: -1.1em;"><?
		?></div><?
		?><div style="padding: .2em;"><?= $data ?></div><?
		layout_close_box();
	} else {
		echo $data;
	}
	return $uniq;
}

function display_banner_innerbox(array $banner): void {
	?><div<?
	if (!empty($banner['PARTYID'])) {
		?> style="margin-bottom: .5em; padding: 1em;"<?
		?> class="party-spot"<?
	}
	?>><?
	?><div id="bannerbox"><?
	?><input type="hidden" name="HEIGHT" value="<?= $banner ? $banner['HEIGHT'] : 0 ?>" /><?
	if ($banner) {
		?><div id="actualbanner"<?
		if (empty($banner['PARTYID'])) {
			?> class="center"<?
		}
		?>><?
		# show banner to admin, never use fallback
		$banner['FALLBACKID'] = 0;
		display_banner($banner);
		?></div><?
	}
	?></div><?

	if (!empty($banner['BANNERID'])
	&&	have_admin('banner')
	&&	(	empty($_REQUEST['sELEMENT'])
		||	$_REQUEST['sELEMENT'] !== 'banner'
		)
	) {
		?><div class="center"><?
		?><a href="/banner/<?= $banner['BANNERID'] ?>"><?= __('action:change_banner') ?></a><?
		?></div><?
	}
	?></div><?
}
