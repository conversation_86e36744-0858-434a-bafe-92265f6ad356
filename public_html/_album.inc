<?php

const GROUPING_LANDSCAPE_FIRST	= 1;
const GROUPING_PORTRAIT_FIRST	= 2;
const GROUPING_FIRST_FIRST	= 3;

function get_albumtree_elements_key(int $albumid): string {
	return 'albumtree,elems:'.$albumid;
}
function get_albumtree_maps_key(int $albumid): string {
	return 'albumtree,maps:'.$albumid;
}
function get_albumall_key(int $albumid): string {
	return 'albumall:v2:'.$albumid;
}
function get_albumelement_key(int $albumelementid): string {
	return 'albumelement:v4:'.$albumelementid;
}
function get_albumelement_table_key(int $albumelementid, string $table): string {
	return 'albumelem:'.$albumelementid.','.$table;
}
function get_albumthumb_key(int $albumelementid): string {
	return 'albumthumb:'.$albumelementid;
}
function get_albumimages_key(int $mapid): string {
	return 'albumimages:'.$mapid;
}
function get_albumid_from_mapid(int $mapid): int|null|false {
	require_once '_memcache.inc';
	# maps can't move to other albums
	return memcached_single('albummap','
		SELECT ALBUMID
		FROM albummap
		WHERE MAPID='.$mapid,
		ONE_DAY
	);
}
function get_albumid_from_albumelementid($albumelementid): int|false {
	require_once '_memcache.inc';
	# albumelements can't move to other albums
	return memcached_single('albumelement','
		SELECT ALBUMID
		FROM albumelement
		WHERE ALBUMELEMENTID='.$albumelementid,
		ONE_DAY
	);
}

function clean_get_albumid_from_mapid(int $mapid): int|false|null {
	if (false === ($albumid = db_single('albummap', "
		SELECT ALBUMID
		FROM albummap
		WHERE MAPID = $mapid",
		DB_USE_MASTER))
	) {
		return false;
	}
	if (!$albumid) {
		register_error('albummap:error:nonexistent_LINE', ['ID' => $mapid]);
	}
	return $albumid;
}

function clean_get_albumid_from_albumelementid(int $albumelementid): int|null|false {
	$albumid = db_single('albumelement','SELECT ALBUMID FROM albumelement WHERE ALBUMELEMENTID='.$albumelementid,DB_USE_MASTER);
	if ($albumid === false) {
		return false;
	}
	if (!$albumid) {
		register_error('albumelement:error:nonexistent_LINE',['ID'=>$albumelementid]);
	}
	return $albumid;
}
function show_album_party_options(string $prefix, ?array $element = null,int $userid = CURRENTUSERID): void {
	require_once '_going.inc';
	$partyids = get_all_going($userid, GOING_CERTAIN);
	if ($partyids === false) {
		return;
	}
	$mappartyids = memcached_boolean_hash('albummap','
		SELECT DISTINCT PARTYID
		FROM albummap
		WHERE PARTYID!=0
		  AND ALBUMID='.$userid
	);
	if ($mappartyids === false) {
		return;
	}
	$elempartyids = memcached_boolean_hash('albumelement','
		SELECT DISTINCT PARTYID
		FROM albumelement
		WHERE PARTYID!=0
		  AND ALBUMID='.$userid
	);
	if ($elempartyids === false) {
		return;
	}
	if ($mappartyids) {
		$partyids += $mappartyids;
	}
	if ($elempartyids) {
		$partyids += $elempartyids;
	}
	if (!$partyids) {
		?><input type="hidden" name="<?= $prefix ?>PARTYID" value="0" /><?
		?><input type="hidden" name="<?= $prefix ?>PARTYLINKTOP" value="0" /><?
		return;
	}
	ksort($partyids);
	$parties = memcached_rowuse_hash('party', '
		SELECT PARTYID,STAMP,NAME
		FROM party
		WHERE PARTYID IN ('.implodekeys(',',$partyids).')
		  AND STAMP<='.TODAYSTAMP,
		TEN_MINUTES
	);
	if ($parties === false) {
		return;
	}
	if (!$parties) {
		?><input type="hidden" name="<?= $prefix ?>PARTYID" value="0" /><?
		?><input type="hidden" name="<?= $prefix ?>PARTYLINKTOP" value="0" /><?
		return;
	}
	number_reverse_asort($parties, 'STAMP');
	layout_restart_row();
	include_js('js/form/albumpartyoptions');
	echo __C('element:party');
	layout_field_value();
	?><select class="fw" onchange="changeParty(this.value)" name="<?= $prefix ?>PARTYID"><?
	?><option value="0"></option><?
	foreach ($parties as $partyid => $party) {
		?><option<?
		if ($element
		&&	$element['PARTYID'] == $party['PARTYID']
		) {
			?> selected="selected"<?
		}
		?> value="<?= $party['PARTYID'];
		?>"><? _date_display_numeric($party['STAMP'],'&nbsp;');
		?>: <?= escape_utf8($party['NAME']);
		?></option><?
	}
	?></select><?
	if ($element
	&&	$prefix == 'MAP'
	) {
		?> <label><?
		?><input type="checkbox" value="1" name="PARTYCONNECTDESCEND" /> <?
		echo __C('action:(dis)connected_contents_too');
		?></label><?
	}
	layout_restart_row($element && $element['PARTYID'] ? 0 : ROW_HIDDEN, 'partylinktoprow');
	echo Eelement_name('position');
	layout_field_value();
	?><select name="<?= $prefix; ?>PARTYLINKTOP"><?
		?><option value="0"><?= __('position:below') ?></option><?
		?><option<?
		if (!empty($element['PARTYLINKTOP'])) {
			?> selected="selected"<?
		}
		?> value="1"><?= __('position:above') ?></option><?
	?></select><?
}
function show_album_partylink($element) {
	if (!($party = memcached_party_and_stamp($element['PARTYID'], mail_when_nonexistent: true))) {
		return;
	}
	?><div class="<?= $element['ALIGN'] ?> block"><?
	?><small><?= get_element_link('party', $element['PARTYID'], $party['NAME'])
	?> <?= MIDDLE_DOT_ENTITY ?> <? _date_display($party['STAMP']);
	?></small></div><?
}

function album_display_alignment_options($selected = null): void {
	?><option<? if ($selected && $selected == 'left') echo ' selected="selected"'; ?> value="left"><?= __('alignment:left') ?></option><?
	?><option<? if ($selected && $selected == 'right') echo ' selected="selected"'; ?> value="right"><?= __('alignment:right') ?></option><?
	?><option<? if (!$selected || $selected == 'center') echo ' selected="selected"'; ?> value="center"><?= __('alignment:center') ?></option><?
}

const ALBUMELEMENT_ALIGNMENTS = ['left', 'center', 'right'];

function require_alignment(array $arr, string $field): bool {
	return require_element($arr, $field, ALBUMELEMENT_ALIGNMENTS, utf8: true);
}

function get_albumimages(int $albumid, int $mapid): array|null|false {
	return memcached_rowuse_hash_if_not_self(
		$albumid,
		['albumelement', 'albumimagedatameta'],'
		SELECT ALBUMELEMENTID,FILETYPE,WIDTH,HEIGHT,ACCEPTED,TITLE,ORDERID,THMBID,THMBID AS DATAID,
			VISIBILITY_ELEMENT,VISIBILITY_COMMENTS,ALBUMID AS USERID,
			NOT ISNULL(busy.DATAID) AS BUSY
		FROM albumelement
		JOIN albumimagedatameta ON albumimagedatameta.DATAID=THMBID
		LEFT JOIN albumimagedatabusy AS busy ON busy.DATAID=THMBID
		WHERE MAPID='.$mapid,
		ONE_DAY,
		get_albumimages_key($mapid)
	);
}
function flush_albumcache(int $albumid): void {
	memcached_delete(get_albumtree_maps_key($albumid));
	memcached_delete(get_albumtree_elements_key($albumid));
	memcached_delete(get_albumall_key($albumid));
	flush_albumcounts($albumid);
}
function flush_albumcounts(int $albumid): void {
	require_once '_visibility.inc';
	memcached_delete('albumcounts:'.$albumid);
	for ($vis = 0; $vis <= LAST; ++$vis) {
		memcached_delete('albumtotals:'.$albumid.':'.$vis);
	}
	# FIXME: only flush counts for maps that have changed
	$mapids = db_simpler_array('albummap','SELECT MAPID FROM albummap WHERE ALBUMID='.$albumid);
	if (!$mapids) {
		return;
	}
	foreach ($mapids as $mapid) {
		for ($vis = 0; $vis <= LAST; ++$vis) {
			memcached_delete('albummapcounts:'.$mapid.':'.$vis);
		}
	}
}
function require_album_not_in_use(int $albumid): bool {
	if (!db_getlock('albumchange:'.$albumid, 4)) {
		register_error('album:error:album_busy_LINE');
		return false;
	}
	return true;
}
function flush_albumelements(array $ids): void {
	if (isset($ids[0])) {
		foreach ($ids as $id) {
			flush_albumelement($id);
		}
	} else {
		foreach ($ids as $id => $null) {
			flush_albumelement($id);
		}
	}
}
function flush_albumelement(int $albumelementid): bool {
	if ($info = db_single_array('albumelement','SELECT ALBUMID,MAPID FROM albumelement WHERE ALBUMELEMENTID='.$albumelementid)) {
		[$albumid, $mapid] = $info;
		flush_albumcache ($albumid);
		flush_albumcounts($albumid);
		memcached_delete(get_albumimages_key($mapid));
	}
	memcached_delete(get_albumelement_key($albumelementid));
	memcached_delete(get_albumthumb_key  ($albumelementid));
	foreach ([
		'albumelement',
		'albumelement_log',
		'albumelement_tobedeleted'
	] as $table) {
		memcached_delete(get_albumelement_table_key($albumelementid, $table));
	}
	return true;
}
function get_album_where_for_element(string $element, int $id, ?array $onlybuddies = null) {
	require_once '_visibility.inc';
	$where = match ($element) {
		'party'			=> " WHERE PARTYID = $id",
		'location'		=> " JOIN party USING (PARTYID) WHERE LOCATIONID = $id",
		'organization'	=> " JOIN connect ON MAINTYPE = 'organization' AND ASSOCTYPE = 'party' AND ASSOCID = PARTYID AND MAINID = $id",
		default			=> '',
	};
	if (!have_admin('albumelement')) {
		$where .= ' AND albumelement.ACCEPTED=1';
	}
	if ($onlybuddies) {
		asort($onlybuddies);
		$where .= ' AND ALBUMID IN ('.implode(',',$onlybuddies).')';
	}
	return $where._where_visible('albumelement', 'ELEMENT', 'ALBUMID');
}

function get_album_total_for_element($element,$id,$onlybuddies = null) {
	return memcached_single('albumelement','SELECT COUNT(*) FROM albumelement'.get_album_where_for_element($element,$id,$onlybuddies),ONE_DAY);
/*	- memcached_single(
		array('albumelement','albumimagedatameta','albumhiddencrclen'),'
		SELECT COUNT(*)
		FROM albumelement AS ae
		JOIN albumimagedatameta AS meta ON meta.DATAID=ae.THMBID
		JOIN albumhiddencrclen AS ahcl ON ahcl.CRC=meta.CRC AND ahcl.LEN=meta.LEN
		WHERE PARTYID='.$party['PARTYID'],
		ONE_HOUR
	);*/
}
function album_show_stats_row(?string $element = null, ?int $id = null, ?int $total =  null, bool $restart = true) {
 	if ($total === null) {
		$element ??= $_REQUEST['sELEMENT'];
		$id		 ??= $_REQUEST['sID'];
		$total = get_album_total_for_element($element,$id);
	}
	if (!$total) {
		return;
	}
	$link_open = "<a href=\"/album/$element/$id\">";
	$link_close = '</a>';
	$restart ? layout_restart_reverse_row() : layout_start_reverse_row();
	echo $link_open, $total, $link_close;
	layout_value_field();
	echo $link_open,element_name('album_photo',$total),$link_close;
	/** @noinspection PhpExpressionResultUnusedInspection */
	!$restart && layout_stop_row();
}
