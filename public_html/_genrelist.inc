<?php

require_once '_genres.inc';

function _genrelist_get_item_styles(
	 string	$element,
	 int	$id,
	?int   &$cnt		= null,
	?array	$sort_by	= null,
	 bool	$no_masters = false,
	 string	$separator	= ', ',
): ?string {
	$key = null;
	if (!($genres = match($element) {
		'user' => memcached_rowuse_array(['genre_fan','genre','usergenre'], "
			(	SELECT NAME, GID
				FROM genre_fan
				JOIN genre USING (GID)
				WHERE USERID = $id
			) UNION (
				SELECT NAME, 0
				FROM usergenre
				WHERE USERID = $id
			)
			ORDER BY NAME ASC",
			key: $key,
			flags: DB_NON_ASSOC
		),

	default => memcached_rowuse_array([$element.'_genre', 'genre'], "
			SELECT NAME, GID
			FROM {$element}_genre
			JOIN genre USING (GID)
			WHERE {$element}ID = $id
			ORDER BY NAME",
			key: $key,
			flags: DB_NON_ASSOC
		),
	})) {
		$cnt = 0;
		return null;
	}
	if ($sort_by) {
		usort($genres, static fn(array $a, array $b): int => ($sort_by[$b[1]] ?? 0) <=> ($sort_by[$a[1]] ?? 0));
	}

	$cnt = count($genres);
	$result = [];
	$gids = [];
	static $__avail = get_available_genres('party');
	foreach ($genres as [$name, $gid]){
		if ($link = $gid) {
			if (!isset($__avail[$gid])) {
				$link = false;
			}
		}
		$gids[] = $gid;
		ob_start();
		if ($show_count = isset($sort_by[$gid])) {
			?><span class=""><?
		}
		if ($link) {
			?><a class="link" href="/agenda/genre/<?= genre_for_url($name) ?>"><?= escape_utf8($name) ?></a><?
		} else {
			echo escape_utf8($name);
		}
		if ($show_count) {
			?> <small class="light"><?= MULTIPLICATION_SIGN_ENTITY ?> <?= $sort_by[$gid] ?></small><?
			?></span><?
		}
		$result[] = ob_get_clean();
	}
	$str = implode($separator, $result);

	if (!$no_masters
	&&	$gids
	# &&	have_admin($element)
	&&	($master = memcached_single('master_genre','
		SELECT GROUP_CONCAT(DISTINCT NAME ORDER BY NAME SEPARATOR ", ")
		FROM master_genre
		JOIN genre_to_master USING (MGID)
		WHERE GID IN ('.implode(', ', $gids).')'))
	) {
		$str .= "<br /><span class=\"master-genre\">$master</span>";
	}
	return '<span class="force-link">'.$str.'</span>';
}

function get_genre_link($gid): string {
	if (!($genres = get_genres())) {
		return '';
	}
	$name = $genres[$gid];
	return '<a class="link" href="/agenda/genre/'.genre_for_url($name).'">'.escape_utf8($name).'</a>';
}

function show_genre_selection(
	?array	$have			= null,
	bool	$uncollapsed	= false,
	bool	$disabled		= false,
	?array	$make_bold		= null,
): void {
	if (!($genres = get_genres())) {
		return;
	}
	foreach (array_rand($genres, 3) as $gid) {
		$random_genres[] = $genres[$gid];
	}
	sort($random_genres);

	include_js('js/form/genres');

	$haves = [];
	if ($have) {
		if (isset($have[0])) {
			foreach ($have as $gid) {
				if (isset($genres[$gid])) {
					$haves[$gid] = $genres[$gid];
				}
			}
		} else {
			foreach ($have as $gid => $ignored) {
				if (isset($genres[$gid])) {
					$haves[$gid] = $genres[$gid];
				}
			}
		}
		asort($haves);
	}
	ob_start();
	?><input type="hidden" name="GENRESID" disabled value="<?= $uniq = uniqid('', true) ?>" /><?
	?><input<?
	?> name="GENRES"<?
	?> data-ampersands="<?

	if ($ampersands = db_simpler_array('genre', "
		SELECT NAME
		FROM party_db.genre
		WHERE NAME LIKE '%&%'"
	)) {
		$ampersands[] = 'd&b'; # drum & bass
		foreach ($ampersands as &$ampersand) {
			$ampersand = preg_replace('"\s*&\s*"u', '\s*\&\s*', $ampersand);
			$ampersand = str_replace(' ', '\s*', $ampersand);
		}
		unset($ampersand);
		echo escape_utf8('\b('.implode('|', $ampersands).')\b');
	}
	?>"<?
	?> data-convs="<?
	$genre_convs = get_genre_recognitions() ?: [];
	foreach ($genre_convs as /* $src => */ &$dst) {
		$dst = preg_replace('"[\W_]+"', '', $dst);
	}
	unset($dst);
	echo escape_utf8(safe_json_encode($genre_convs));
	?>"<?
	?> onchange="Pf_changeGenres(this, event);"<?
	?> onpaste="Pf_checkGenresClip(event, this);"<?
	?> onkeyup="Pf_changeGenresDelayed(this);"<?
	?> onblur="Pf_addSeparator(this, ', ');"<?
	?> type="text"<?
	?> autocomplete="off"<?
	?> class="genrein"<?
	?> id="genres_<?= $uniq ?>"<?
	?> placeholder="<?= __('field:for_example') ?>: <?= escape_utf8(implode(', ', $random_genres)),', &hellip;' ?>"<?
	if ($have) {
		?> value="<?= escape_utf8(implode(', ', $haves)) ?>, "<?
	}
	?> /><?
	$input = ob_get_clean();

	ob_start();
	?><input name="GENREALL" type="button" onclick="Pf_clickGenreAll(this);"<?
	if (!$uncollapsed) {
		?> data-chosen="data-chosen"<?
	}
	?> <?= $uncollapsed ? 'data-other' : 'value' ?>="<?= BLACK_DOWN_POINTING_TRIANGLE_ENTITY ?>"<?		# down rectangle: click to expand
	?> <?= $uncollapsed ? 'value' : 'data-other' ?>="<?= BLACK_UP_POINTING_TRIANGLE_ENTITY	 ?>" /><?	#   up rectangle: click to collapse
	$button = ob_get_clean();

	# BUG: why a complete table?
	?><table class="nomargin dens fw"><? // NOSONAR
	?><tr><td><?= $button ?></td><td class="fw"><?= $input ?></td></tr><?
	?></table><?
	$lines = [];
	foreach ($genres as $gid => $name) {
		$checked = isset($haves[$gid]);
		ob_start();
		?><li<?
		if (!$checked && !$uncollapsed) {
			?> class="hidden"<?
		}
		?>><?
		?><label class="<?
		if ($make_bold
		&&	isset($make_bold[$gid])
		) {
			?>bold <?
		}
		if (!$checked) {
			?>not-<?
		}
		?>bold-hilited"><?
		show_input([
			'type'		=> 'checkbox',
			'name'		=> 'GID[]',
			'value'		=> $gid,
			'data-name'	=> $name,
			'class'		=> 'upLite',
			'checked'	=> $checked,
			'disabled'	=> $disabled,
			'onclick'	=> 'Pf_clickGenre(this);',
		]);
		?><div class="description"><?= escape_utf8($name) ?></div><?
		?></label><?
		?></li><?
		$lines[] = ob_get_clean();
	}
	if ($lines) {
		?><ul class="nostyle genre-checkboxes nice-boxes"><?= implode('', $lines) ?></ul><?
	}
}

function _genrelist_display_checkboxes(
	array	$gidhash	= [],
	bool	$showclear	= false,
): void {
	if (!($genres = get_genres())) {
		return;
	}
	$lines = [];
	foreach ($genres as $gid => $name) {
		ob_start();
		?><label class="cbi <?
		if (!($checked = isset($gidhash[$gid]))) {
			?>not-<?
		}
		?>bold-hilited"><input<?
		if ($checked) {
			?> checked<?
		}
		?> class="upLite" type="checkbox" name="GID[]" value="<?= $gid ?>"><?
		?> <?= escape_utf8($name)
		?></label><br /><?
		$lines[] = ob_get_clean();
	}
	if ($showclear) {
		ob_start();
		?><input type="button" onclick="(function(form) {
			for (let gid of form['GID[]']) {
				gid.checked = false;
				gid.onclick(gid);
			}
		})(this.form);" value="<?= __('action:clear') ?>"><?
		$lines[] = ob_get_clean();
	}
	if ($lines) {
		show_columns($lines, 4);
	}
}

function show_user_genre_options(int $userid): bool {
	if ($userid
	&&	(	false === ($selected	= db_same_hash('genre_fan',	  'SELECT GID  FROM genre_fan WHERE USERID = '.$userid, DB_USE_MASTER))
		||	false === ($user_genres = db_simpler_array('usergenre','SELECT NAME FROM usergenre WHERE USERID = '.$userid, DB_USE_MASTER)))
	) {
		return false;
	}
	show_genre_selection($selected ?? null);

	require_once '_bubble.inc';
	?><div class="block" style="margin-top: .5em;"><?

	ob_start();
	$bubble = new bubble(BUBBLE_BLOCK | BUBBLE_30_WIDE | HELP_BUBBLE | CONTENT_AT_BOTTOM);
	$bubble->catcher(__('genrelist:propositions'));
	$bubble->content(__('genrelist:proposition_information_TEXT'));
	$bubble->display();
	$description = ob_get_clean();

	show_input_with_label(
		!empty($user_genres),
		'bold-hilited',
		'cbi', [
			'type'		=> 'checkbox',
			'value'		=> '1',
			'name'		=> 'USEROPTS',
			'id'		=> 'useropts',
			'class'		=> 'upLite',
			'onclick'	=> "setdisplay('usergenres', this.checked)"],
		$description
	);
	?><span id="usergenres"<?
	if (empty($user_genres)) {
		?> class="hidden"<?
	} else {
		sort($user_genres);
	}
	?>>: <?
	show_input([
		'type'			=> 'text',
		'name'			=> 'USERGENRES',
		'value_utf8'	=> !empty($user_genres) ? implode(', ', $user_genres) : null,
	]);
	?></span></div><?
	return true;
}

function user_favourite_genres(int $userid): array|false|null {
	return db_same_hash('genre_fan', "
		SELECT GID
		FROM genre_fan
		WHERE USERID = $userid"
	);
}

function _genrelist_commit(?string $element = null, ?int $id = null): int|false {
	$element ??= $_REQUEST['sELEMENT'];
	$id		 ??= $_REQUEST['sID'];
	$changes = 0;
	if ($element === 'user') {
		$table = 'genre_fan';
		if (!empty($_POST['USERGENRES'])) {
			if (false === ($havegenres = db_boolean_hash('usergenre','
				SELECT LOWER(NAME)
				FROM usergenre
				WHERE USERID = '.$id,
				DB_FORCE_MASTER))
			||	false === ($defgenres = memcached_simple_hash(['genre', 'genre_recognize'], "
				SELECT LOWER(REPLACE(REPLACE(NAME, '  ', ' '), '-', '')), GID
				FROM party_db.genre
				UNION
				SELECT LOWER(NAME), GID
				FROM party_db.genre_recognize"))
			) {
				return false;
			}
			foreach (explode(',', $_POST['USERGENRES']) as $genre) {
				if (!($lgenre = mb_strtolower(preg_replace('"\W+"u', '', $genre)))) {
					continue;
				}
				if (isset($defgenres[$lgenre])) {
					$_POST['GID'][] = $defgenres[$lgenre];
					continue;
				}
				$genre = trim($genre);
				$keepgenres[$genre] = true;
				if (!isset($havegenres[$genre])) {
					$inslist[$genre] = '('.$id.', "'.addslashes($genre).'", '.CURRENTSTAMP.')';
				}
			}
		}
		$where_for_usergenre = /** @lang MariaDB */ "WHERE USERID = $id";
		if (!empty($keepgenres)) {
			$where_for_usergenre .= /** @lang MariaDB */ ' AND NAME NOT IN (BINARY '.stringsimplodekeys(',', $keepgenres).')';
		}
		if (!db_insert('usergenre_log', '
			INSERT INTO usergenre_log
			SELECT *, '.CURRENTSTAMP."
			FROM usergenre
			$where_for_usergenre")
		||	!db_delete('usergenre', "
			DELETE FROM usergenre
			$where_for_usergenre")
		) {
			return false;
		}
		$changes += db_affected();
		if (isset($inslist)) {
			if (!db_insert('usergenre', '
				INSERT IGNORE INTO usergenre (USERID, NAME, CSTAMP)
				VALUES '.implode(', ', $inslist)
			)) {
				return false;
			}
			$changes += db_affected();
		}

	} else {
		$table = $element.'_genre';
	}
	if (!isset($_POST['GID'])) {
		if (!db_insert("{$table}_log", "
			INSERT INTO {$table}_log
			SELECT *, ".CURRENTSTAMP."
			FROM $table
			WHERE {$element}ID = $id")
		||	!db_delete($table, "
			DELETE FROM $table
			WHERE {$element}ID = $id")
		) {
			return false;
		}
		$changes += ($affected = db_affected());
		if ($affected) {
			require_once '_profile.inc';
			clear_future_event_genres($element, $id);
			clear_element_profile($element, $id);
		}
		return $changes;
	}
	if (!require_number_array($_POST, 'GID')) {
		return false;
	}
	foreach ($_POST['GID'] as $gid) {
		$vals[$gid] = "($id, $gid, ".CURRENTSTAMP.')';
	}
	$gidstr = isset($vals) ? implodekeys(',',$vals) : '';
	if (!db_insert("{$table}_log", "
		INSERT INTO {$table}_log
		SELECT *, ".CURRENTSTAMP."
		FROM $table
		WHERE {$element}ID = $id".
		($gidstr ? " AND GID NOT IN ($gidstr)" : ''
	))) {
		return false;
	}
	if (!isset($vals)) {
		return $changes;
	}
	if (!db_delete($table, "
		DELETE FROM $table
		WHERE GID NOT IN ($gidstr)
		  AND {$element}ID = $id")
	) {
		return false;
	}
	$changes += db_affected();
	if (!db_insert($table, "
		INSERT IGNORE INTO $table ({$element}ID, GID, CSTAMP)
		VALUES ".implode(', ', $vals))
	) {
		return false;
	}
	$changes += ($affected = db_affected());
	if ($affected) {
		require_once '_profile.inc';
		clear_future_event_genres($element, $id);
		clear_element_profile($element, $id);
	}
	return $changes;
}

function clear_future_event_genres(string $element, int $id): void {
	# allow redetermination of event genre if artist or org changes (only future)
	if ($future_events = match($element) {
		'artist' => db_simpler_array(['party','lineup'], "
			SELECT DISTINCT PARTYID
			FROM party
			JOIN lineup USING (PARTYID)
			WHERE ARTISTID = $id
			  AND STAMP > ".CURRENTSTAMP
		),

		'organization' => db_simpler_array(['party','lineup'], "
			SELECT DISTINCT PARTYID
			FROM party
			JOIN connect ON MAINTYPE = 'party'
					    AND MAINID = PARTYID
					    AND ASSOCTYPE IN ('organization', 'orgashost')
			WHERE ASSOCID = $id
			  AND STAMP > ".CURRENTSTAMP."
			UNION
			SELECT DISTINCT PARTYID
			FROM partyarea
			JOIN party USING (PARTYID)
			WHERE HOSTEDBYID = $id
			  AND STAMP > ".CURRENTSTAMP
		),

		default => [],
	}) {
		require_once '_genres.inc';
		foreach ($future_events as $partyid) {
			clear_determined_genres($partyid);
		}
	}
}

function genrelist_display_options(
	 string|int	$selected	= 0,
	?array		$genres		= null,
	 bool		$gid_values = false,
): void {
	$all_genres = $genres ?: (array)get_genres();
	if (!$all_genres) {
		mail_log('genrelist_display_options: no genres', get_defined_vars());
		return;
	}
	if (is_string($selected)
	&&	$selected !== 'favourites'
	) {
		mail_log('genrelist_display_options got string $selected', get_defined_vars());
		$selected = (int)$selected;
	}
	# If $selected genre is not in the all_genres list because there are no future events,
	# then add it to the list with count 0 so it can be selected.
	if ($selected
	&&	$selected !== 'favourites'
	&&	!isset($all_genres[$selected])
	) {
		# Not sure if count=0 genres got input here, because currently if count=0, it won't get shown.
		# Using false as count to force showing there are 0 events.
		$all_genres[$selected] = [get_genres($selected) => false];
		uasort($all_genres, static function(array $a, array $b): int {
			return key($a) <=> key($b);
		});
	}
	$genres = [];
	if (have_user()
	&&	($favourite_genres = user_favourite_genres(CURRENTUSERID))
	) {
		foreach ($all_genres as $gid => $name_and_count) {
			$genres[isset($favourite_genres[$gid]) ? 1 : 0][$gid] = $name_and_count;
		}
	} else {
		$genres[0] = $all_genres;
	}
	$group_count = count($genres);
	foreach ([1, 0] as $favourite) {
		if (!isset($genres[$favourite])) {
			continue;
		}
		if ($group_count > 1) {
			?><optgroup label="<?= element_plural_name($favourite ? 'favourite_genre' : 'other_genre') ?>"><?
		}
		foreach ($genres[$favourite] as $gid => $name_and_count) {
			if (is_array($name_and_count)) {
				$name = key($name_and_count);
				$count = current($name_and_count);
			} else {
				# music section uses only name and no count
				$name = $name_and_count;
				$count = 0;
			}
			?><option<?
			if ($selected === $gid) {
				?> selected<?
			}
			?> value="<?= $gid_values ? $gid : genre_for_url($name) ?>"><?
			echo escape_utf8($name);
			if ($count
			||	$count === false
			) {
				?> <small>&middot; <?= $count ?: 0 ?></small><?
			}
			?></option><?
		}
		if ($group_count > 1) {
			?></optgroup><?
		}
	}
}

function show_genre_select(
	?string			$element			= null,
	 int|string		$selected			= 0,
	?array			$genres				= null,
	 bool			$include_favourites = false,
	 bool|string	$include_empty		= false,
	 bool			$include_all		= false,
): void {
	if (!$genres
	&&	false === ($genres = get_available_genres($element))
	) {
		return;
	}
	if ($element === 'agenda'
	&&	$genres
	&&	$include_favourites
	) {
		$favourite_count = 0;
		$gid = array_key_first($genres);
		$info = $genres[$gid];
		if (is_array($info)) {
			if (false === ($favourite_genres = user_favourite_genres(CURRENTUSERID))) {
				return;
			}
			foreach (array_intersect_key($genres, $favourite_genres) as /* $gid => */ $names_to_count) {
				$favourite_count += array_sum($names_to_count);
			}
		}
	}
	if ($include_favourites
	&&	(	!have_user()
		||	!user_favourite_genres(CURRENTUSERID)
		)
	) {
		$include_favourites = false;
	}
	$action  = $_REQUEST['ACTION'];
	$top_part = $action === 'top' ? '/top' : '';

	?><select<?
	?> name="GENRE"<?
	if ($include_empty) {
		?> required<?
	}
	?> onchange="
		location.href =
			this.value === ''
		?	'/<?= $element, $top_part ?>/genres'
		:	'/<?= $element, $top_part ?>/genre/' + this.value;
	"><?
	if ($include_empty) {
		?><option<?
		if (!$selected) {
			?> selected<?
		}
		?> value=""<?
		?> disabled><?= $include_empty === true ? '' : $include_empty ?></option><?
	}
	if ($include_all) {
		?><option<?
		?> selected<?
		?> value=""><?= element_name('all') ?></option><?
	}
	if ($include_favourites) {
		?><option<?
		if ($selected === 'favourites') {
			?> selected<?
		}
		?> value="favourites"><?
		echo element_plural_name('all_favourite_genre');
		if (!empty($favourite_count)) {
			?> <?= MIDDLE_DOT_ENTITY ?> <? echo $favourite_count;
		}
		?></option><?
	}
	genrelist_display_options($selected, $genres);
	?></select><?
}

function show_genres_block(array $item): void {
	$element = $_REQUEST['sELEMENT'];
	$id		 = $_REQUEST['sID'];
	if (!($styles = _genrelist_get_item_styles($element, $id))) {
		return;
	}
	?><div class="block"><?
	?><b><?= Eelement_plural_name('genre') ?></b><?
	if (!empty($item['COPY_GENRES'])
	&&	have_admin($element)
	) {
		?> <small class="light">(<?= __('genres:info:copy_to_new_event') ?>)</small><?
	}
	?><br /><?= $styles
	?></div><?
}
