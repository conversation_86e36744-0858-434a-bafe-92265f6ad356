<?php

define('CURRENTSTAMP',time());

require_once '_db.inc';
require_once '_flockmod.inc';
require_once '_memcache.inc';
require_once '_namedoubles.inc';
require_once '_nocache.inc';
require_once '_require.inc';
require_once '__translation.php';
require_once '_elementnames.inc';
require_once '_findorganization.inc';

send_no_cache_headers();

function bail(int $errno = 200, string $type = 'application/json'): never {
	if ($errno === 200) {
		header('Content-Type: '.$type.'; charset=utf-8', true, $errno);
	} else {
		http_response_code($errno);
	}
	exit;
}
if (!isset($_SERVER['eELEMENT'])
||	!have_something_trim($_REQUEST, 'NAME')
) {
	bail(400);
}

$basekey = 'findy:';
if (isset($_REQUEST['NODEF'])) {
	$basekey .= 'nodef:';
}

switch ($_SERVER['eELEMENT']) {
case 'boarding':
	$name = utf8_mytrim($_REQUEST['NAME']);
	$result = memcached_get($key = $basekey."boarding:".hash('xxh128', strtolower($name)));
	if ($result) {
		echo $result;
		bail(200, 'text/html');
	}
	ob_start();
	$elems = db_rowuse_hash(['boarding','city','country'],'
		SELECT BOARDINGID, elem.NAME AS NAME, ADDRESS, elem.CITYID, city.NAME AS CITY_NAME, CURRENCY
		FROM boarding AS elem
		LEFT JOIN city USING (CITYID)
		LEFT JOIN country USING (COUNTRYID)
		WHERE elem.NAME LIKE "%'.($namestr = addslashes(preg_replace('"[\s,\.]+"u', '%', $name))).'%"
		   OR ADDRESS LIKE "%'.$namestr.'%"
		   OR city.NAME LIKE "%'.$namestr.'%"
		ORDER BY elem.NAME ASC, city.NAME ASC'
	);
	if (!$elems) {
		echo __('findelement:no_results_LINE');
	} else {
		$exact = 0;
		$lower_name = mb_strtolower($name);
		foreach ($elems as $boardingid => &$elem) {
			if (mb_strtolower($elem['NAME'])	=== $lower_name
			||	mb_strtolower($elem['ADDRESS'])	=== $lower_name
			) {
				$elem['EXACT'] = true;
				$exactlist[$boardingid] = $elem;
				++$exact;
			}
		}
		unset($elem);
		if (($size = count($elems)) > 20) {
			if (isset($exactlist)) {
				echo __('findelement:many_options_only_exact_LINE') ?><br /><?
				$elems = $exactlist;
			} else {
				echo __('findelement:too_many_options_LINE', ['TOTAL' => $size]);
				$elems = null;
			}
		}
		if ($elems) {
			require_once '_elementoption.inc';
			generate_name_doubles($elems, 'NAME', $_SERVER['eELEMENT']);
			foreach ($elems as $id => $elem) {
				show_element_option(
					$elem,
						$size === 1
					||	$exact === 1
					&&	isset($elem['EXACT'])
				);
				?><br /><?
			}
		}
	}
	memcached_set($key, ob_get_flush(), DEFAULT_EXPIRATION);
	bail(200, 'text/html');
	break;

case 'location':
	$name = utf8_mytrim($_REQUEST['NAME']);
	$key = null;
	$prefix = '
		SELECT LOCATIONID, TITLE, elem.NAME, elem.CITYID, city.NAME AS CITY_NAME, LOCATIONTYPE, DEAD, FOLLOWUPID, CURRENCY, MIN_AGE, ADDRESS
		FROM location AS elem
		LEFT JOIN city USING (CITYID)
		LEFT JOIN country USING (COUNTRYID)
		LEFT JOIN alternate_name ON ELEMENT = "location" AND ID = LOCATIONID';

	ob_start();

	# can't we do this in a nice way without so much duplication?
	$elems = db_rowuse_hash(['location', 'city', 'country'],
		$prefix.'
		WHERE
			  '.where_search_title('CONCAT(		 elem.TITLE, " ", elem.ADDRESS, " ",COALESCE(city.NAME, ""))',	$name, WILD_BOTH | NAME_UTF8).'
		   OR '.where_search_title('CONCAT(alternate_name.NAME, " ", elem.ADDRESS, " ",COALESCE(city.NAME, ""))',	$name, WILD_BOTH | NAME_UTF8).'
		   OR '.where_search_title('CONCAT(		 elem.TITLE, " ",	  		   COALESCE(city.NAME, ""))',	$name, WILD_BOTH | NAME_UTF8).'
		   OR '.where_search_title('CONCAT(alternate_name.NAME, " ",				   COALESCE(city.NAME, ""))',	$name, WILD_BOTH | NAME_UTF8).'
		   OR '.where_search_title('CONCAT(COALESCE(city.NAME, ""), " ", elem.ADDRESS, " ",		  elem.TITLE)',	$name, WILD_BOTH | NAME_UTF8).'
		   OR '.where_search_title('CONCAT(COALESCE(city.NAME, ""), " ", elem.ADDRESS, " ", alternate_name.NAME)',	$name, WILD_BOTH | NAME_UTF8).'
		   OR '.where_search_title('CONCAT(COALESCE(city.NAME, ""), " ",							 elem.TITLE)',	$name, WILD_BOTH | NAME_UTF8).'
		   OR '.where_search_title('CONCAT(COALESCE(city.NAME, ""), " ",					alternate_name.NAME)',	$name, WILD_BOTH | NAME_UTF8).'
		   OR '.where_search_title('elem.TITLE',									$name, WILD_BOTH | NAME_UTF8).'
		   OR '.where_search_title('elem.ADDRESS',									$name, WILD_BOTH | NAME_UTF8).'
		   OR '.where_search_title('alternate_name.NAME',								$name, WILD_BOTH | NAME_UTF8).'
		ORDER BY elem.ORDERNAME ASC, city.NAME ASC'
	);

	if ($elems === false) {
		bail(500);
	}
	$force_id = false;
	if (preg_match('"^(.*~)?(\d+)$"u', $name, $match)
	&&	!isset($elems[$id = $match[2]])
	) {
		$force_id = !empty($match[1]) ? $id : false;
		$elem_by_number = db_single_assoc(['location','city','country'],
			$prefix.'
			WHERE LOCATIONID = '.$id
		);
		if ($elem_by_number) {
			if ($force_id) {
				$elem_by_number['EXACT'] = true;
			}
			$elems[$id] = $elem_by_number;
		}
	}

	if ($elems) {
		$exact = 0;
		$lower_name = mb_strtolower($name);
		foreach ($elems as $locationid => &$elem) {
			if ($force_id == $locationid
			||	mb_strtolower($elem['TITLE'])	=== $lower_name
			||	mb_strtolower($elem['NAME'])	=== $lower_name
			) {
				$elem['EXACT'] = true;
				$exactlist[$locationid] = $elem;
				++$exact;
			}
		}
		unset($elem);
		if (($size = count($elems)) > 20) {
			if (isset($exactlist)) {
				echo __('findelement:many_options_only_exact_LINE') ?><br /><?
				$elems = $exactlist;

			} elseif (!empty($elem_by_number)) {
				echo __('findelement:many_options_only_exact_LINE') ?><br /><?
				$elems = [
					$elem_by_number['LOCATIONID'] => $elem_by_number
				];

			} else {
				echo __('findelement:too_many_options_LINE', ['TOTAL'=>$size]);
				$elems = null;
			}
		}
		if ($elems) {
			require_once '_elementoption.inc';
			generate_name_doubles($elems, 'NAME', $_SERVER['eELEMENT']);
			foreach ($elems as $id => $elem) {
				show_element_option(
					$elem,
						!isset($_REQUEST['NODEF'])
					&&	(	$size === 1
						||	$exact === 1
						&&	isset($elem['EXACT'])
						||	$force_id === $id
					)
				);
				?><br /><?
			}
		}
	} else {
		echo __('findelement:no_results_LINE');
	}
	if ($key) {
		memcached_set($key, ob_get_flush(), DEFAULT_EXPIRATION);
	}
	bail(200, 'text/html');

case 'organizations':
	$gids = [];
	if (!empty($_POST['ORGANIZATIONID'])
	&&	is_array($_POST['ORGANIZATIONID'])
	) {
		foreach ($_POST['ORGANIZATIONID'] as $id => $checked) {
			if (is_number($id)) {
				if ($checked === '2') {
					$checked = FIND_UPDATED;
				} else {
					$checked = $checked === '1';
				}
				$gids += find_organization((int)$id,$checked);
			}
		}
	}
	require_once '_unicode.inc';
	$_REQUEST['NAME'] = utf8_mytrim($_REQUEST['NAME']);
	foreach (preg_split('"(\h+(?:x|'.MULTIPLICATION_SIGN.')\h+|,)"u', $_REQUEST['NAME']) as $name) {
		if (!($name = utf8_mytrim($name))) {
			continue;
		}
		$gids += find_organization($name);
	}
	if ($gids) {
		sort($gids);
		header('X-GIDS: '.implode(',',$gids));
	}
	bail(200, 'text/html');

case 'user':
	# FIXME: is this used?
	require_once '_triplets.inc';
	$nick = $_REQUEST['NAME'];
	[$wherep, $jc] = build_triplets_where($nick);
	$joinstr = '';
	if ($jc && $jc < 50) {
		while ($jc--) {
			$joinstr .= ' JOIN usertriplets AS t'.$jc.' USING (USERID)';
		}
		$wherep .= ' AND ';
		$users = db_simple_hash(['user_account', 'usertriplets'], '
			SELECT user_account.USERID,NICK
			FROM user_account '.$joinstr.'
			WHERE '.$wherep.'NICK LIKE "%'.str_replace(' ','%',addslashes(db_quote_wild($nick))).'%"
			  AND STATUS="active"
			ORDER BY ABS(LENGTH(NICK)-'.strlen($nick).')
			LIMIT 100'
		);
		if ($users === false) {
			bail(500);
		}
		echo '<?'; ?>xml version="1.0" encoding="utf-8"<? echo '?>';
		?><result><?
		foreach ($users as $userid => $nick) {
			?><user id="<?= $userid; ?>" name="<?= win1252_to_utf8(escape_specials($nick)); ?>"><?
		}
		?></result><?
		bail(200, 'xml');
	}
}

bail(400);
