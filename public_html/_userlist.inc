<?php

# NOTE: User lists with more than 1000 users will only be shown as lists.
#	User lists with 100 to 1000 users will be shown as list at first, but can be swapped with tiles.
#	User lists below 100 can be tiles or lists and start as configured by user.

const ONLY_LIST_FROM	= 1000;
const FIRST_LIST_FROM	=  100;

class _userlist {
	private array	$wheres		= [];
	private array	$tables		= [];
	private string	$table		= 'user_account';

	public ?int		$columns		= null;		// null for determine
	public int		$size			= 0;
	public bool		$hide_winners	= false;
	public bool		$hilite_online	= true;
	public bool		$show_camera	= false;
	public bool		$show_heart		= false;

	function __construct() {
		$this->source_element = $_REQUEST['sELEMENT'] ?? null;
	}

	static function show_camera(int $userid): void {
		?><a href="/user/<?= $userid ?>/photos"><?
		?><img<?
		?> loading="lazy"<?
		?> class="lower cam"<?
		?> src="<?= STATIC_HOST; ?>/images/cam<?= is_high_res() ?>.png"<?
		?> alt="<?= element_plural_name('photo') ?>"><?
		?></a><?
	}
	static function show_heart(bool $full): void {
		?><img<?
		?> loading="lazy"<?
		?> alt="&<?= $full ? 'hearts' : '#9825' ?>;"<?
		?> class="<?= !$full ? 'light ' : null ?>lower hrt"<?
		?> src="<?= STATIC_HOST ?>/images/heart<?= is_high_res() ?>.png"<?
		?> alt="<?= element_name('buddy') ?>"><?
	}

	function count(): int {
		return isset($this->userlist) ? count($this->userlist) : 0;
	}

	function only_full_buddies(): void {
		$this->only_full_buddies = true;
	}

	function create_cache() {
		if (isset($this->cacheid)) {
			return $this->cacheid;
		}
		$data = igbinary_serialize($this);
		$this->hash = hash('xxh128', $data, true);

		$prev_images = $this->show_images ?? null;

		unset($this->show_images);

		if (!db_getlock('userlist_cache', 5)) {
			return false;
		}
		if (false !== ($this->cacheid = db_single('userlist_cache','
			SELECT INCID
			FROM userlist_cache
			WHERE HASH	= "'.addslashes($this->hash).'"
			  AND DATA	= COMPRESS("'.addslashes($data).'")'))
		&&	!$this->cacheid
		) {
			$this->cacheid
			=	db_insert('userlist_cache','
				INSERT INTO userlist_cache SET
					HASH	= "'.addslashes($this->hash).'",
					DATA	= COMPRESS("'.addslashes($data).'"),
					STAMP	= '.CURRENTSTAMP.',
					LASTUSE	= '.CURRENTSTAMP.',
					USERID	= '.CURRENTUSERID
				)
			?	db_insert_id()
			:	false;
		}
		db_releaselock('userlist_cache');
		if ($prev_images !== null) {
			$this->show_images = $prev_images;
		}
		return $this->cacheid;
	}

	function query() {
		require_once '_visibility.inc';
		$this->create_cache();

		if (!isset($this->show_images)) {
			$this->show_images =
				isset($_REQUEST['SHOWIMGS'])
			||	(setting('USERLISTS') & USERLISTS_TILES);
		}
		if (isset($this->have_conversation)) {
			$this->tables['conversation_v2'] = 'conversation_v2';
			$this->joins[] = 'JOIN conversation_v2 ON WITH_USERID=XX.USERID';
			$this->wheres[] = 'I_USERID='.CURRENTUSERID;
		}
		if (isset($this->non_permbanned_non_admin)) {
			$this->non_perm_banned();
		}
		if (isset($this->buddies_of_me)) {
			$this->buddies_of_user = CURRENTUSERID;
		}
		if (isset($this->available)) {
			require_once '_dating.inc';
			$need = 0;
			foreach ($this->available as $flag) {
				$need |= $flag;
			}
			$wherep = get_dating_where($need,!empty($this->suitable_only));
			$this->wheres[] =
				isset($this->also_include_gender)
			?	'(('.($wherep ?: '0').') OR XX.SEX="'.$this->also_include_gender.'")'
			:	$wherep;
			$this->table = 'user';
		}
		if (isset($this->find_on_nick_name_or_email)) {
			$str = $this->find_on_nick_name_or_email;
			require_once '_triplets.inc';
			[$wherep, $jc] = build_triplets_where($str);
			if ($jc && $jc < 50) {
				$this->tables['usertriplets'] = 'usertriplets';
				while ($jc--) {
					$tripjoins[] = 'JOIN usertriplets AS t'.$jc.' USING (USERID)';
				}
				$useridparts[] = '(SELECT USERID FROM user_account '.implode(' ',$tripjoins).' WHERE '.$wherep.' AND NICK LIKE "%'.str_replace(' ','%',addslashes(db_quote_wild($str))).'%")';
			} else {
				$useridparts[] = '(SELECT USERID FROM user_account WHERE NICK LIKE "%'.str_replace(' ','%',addslashes(db_quote_wild($str))).'%")';
			}
			$useridparts[] = '(SELECT USERID FROM user WHERE FIND_BY_REALNAME=1 AND REALNAME="'.addslashes($str).'")';
			$useridparts[] = '(SELECT USERID FROM user WHERE NAME="'.addslashes(win1252_to_utf8($str)).'" AND (FIND_BY_REALNAME OR '.(_where_visible('user','NAME',0,'') ?: 1).'))';

			if (match_email($str)) {
				$useridparts[] = '(SELECT USERID FROM user WHERE FIND_BY_EMAIL=1 AND EMAIL="'.addslashes($str).'")';
			}
			$this->joins[] = 'JOIN ('.implode(' UNION ',$useridparts).') AS opts ON opts.USERID = XX.USERID';
			$this->table = 'user';
		}
		if (isset($this->nick_like)) {
			$str = $this->nick_like;
			require_once '_triplets.inc';
			[$wherep,$jc] = build_triplets_where($str);
			if ($jc && $jc < 50) {
				$this->tables['usertriplets'] = 'usertriplets';
				while ($jc--) {
					$this->tripjoins[] = 'JOIN usertriplets AS t'.$jc.' USING (USERID)';
				}
				$this->wheres[] = $wherep;
			}
			$this->wheres[] = 'XX.NICK LIKE "%'.str_replace(' ','%',addslashes(db_quote_wild($str))).'%"';
		}
		if (isset($this->buddies_of_user)) {
			require_once '_buddies.inc';
			$this->wheres[] =
				($buddylist = _buddies_full_cached_list($this->buddies_of_user))
			?	'XX.USERID IN ('.implode(', ', $buddylist).')'
			:	'0';
		}
		if (isset($this->oa)) {
			$this->wheres[] = 'user_account.STATUS="active"';
		}
		if (isset($this->only_online)) {
			$this->hilite_online = false;
			if (!($onlines = online_ids())) {
				$this->wheres[] = '0';
			} else {
				ksort($onlines);
				$this->wheres[] = 'XX.USERID IN ('.implodekeys(', ', $onlines).')';
			}
		}
		if (isset($this->only_online_respect_privacy)) {
			$this->hilite_online = false;
			if (!($onlines = online_ids())) {
				$this->wheres[] = '0';
			} else {
				ksort($onlines);
				$this->wheres[] = 'XX.USERID IN ('.implodekeys(', ', $onlines).')';
				if (!have_admin()) {
					if ($extra = _where_visible('user_account', 'ONLINE', 0, '')) {
						$this->wheres[] = $extra;
					}
				}
			}
		}
		if (isset($this->only_full_buddies)) {
			$this->wheres[] =
				!have_user()
			||	!(require_once '_buddies.inc')
			||	!($userlist = _buddies_full_cached_list(CURRENTUSERID))
			?	'0'
			:	'XX.USERID IN ('.implode(', ', $userlist).')';
		}
		$this->table = 'user';
		$selects = ['XX.USERID, XX.NICK, user_account.STATUS, SEX, VISIBILITY_GENDER'];

		if ($this->show_images) {
			$this->table = 'user';
			$selects[] = 'XX.COUNTRYID, XX.CITYID, VISIBILITY_CITY, SEXPREF, VISIBILITY_SEXPREF, DATING';
		}
		if ($this->show_images) {
			unset($this->show_age);
		}
		if ($this->show_images || isset($this->show_age)) {
			$this->table = 'user';
			$selects[] = 'BIRTH_DAY, BIRTH_MONTH, BIRTH_YEAR, LDAY, VISIBILITY_AGE';
		}
		if (isset($this->deceasedlist)) {
			$selects[] = 'DSTAMP, XX.MSTAMP, DSTAMP AS SORTSTAMP';
			$this->table = 'user';
			$this->tables['deceasedinfo'] = 'deceasedinfo';
			$this->joins[] = 'LEFT JOIN deceasedinfo ON deceasedinfo.USERID = XX.USERID';
			$this->wheres[] = 'user_account.STATUS = "deceased"';
		}
		if ($this->show_heart) {
			if (have_user()) {
				require_once '_buddies.inc';
				$this->full_buddy = _buddies_full_hash(CURRENTUSERID);
				$this->half_buddy = _buddies_half_hash(CURRENTUSERID);
			} else {
				$this->show_heart = false;
			}
		}
		if (isset($this->show_fan_since)) {
			$selects[] = 'favourite.CSTAMP AS FAN_STAMP';
		}
		if (isset($this->only_city)) {
			$selects[] = 'CITYID';
		}
		if (isset($this->show_email)) {
			$selects[] = 'EMAIL';
		}
		if (!have_admin('user')) {
			$this->wheres[] = 'XX.USERID != 1';
		}
		if (isset($this->for_contest)
		&&	empty($this->hide_winners)
		) {
			$selects[] = 'WINSTATUS = "winner" AS IS_WINNER, TOLD';
		}

		if ($this->table === 'user') {
			# need STATUS
			$this->tables['user_account'] = 'user_account';
			$this->joins[] = ' JOIN user_account ON XX.USERID = user_account.USERID ';
		}
		$selects[] = 'esbase.VISIBILITY_AGENDA, esbase.VISIBILITY_PROFILE';
		$this->joins[] = ' LEFT JOIN externalsettings AS esbase ON XX.USERID = esbase.USERID ';
		$this->tables['externalsettings'] = 'externalsettings';

		$this->tables[$this->table] = $this->table;
		$qstr = //str_replace('XX.',$this->table.'.',
			'SELECT '.(isset($this->need_distinct) ? 'DISTINCT ' : '').implode(',',$selects).
			" FROM $this->table AS XX ".
			(!empty($this->tripjoins) ? ' '.implode(' ',$this->tripjoins) : '').
			(!empty($this->joins) ? ' '.implode(' ',$this->joins) : '').
			(!empty($this->wheres) ? ' WHERE '.implode(' AND ', $this->wheres) : '0').
			(!empty($this->limit) ? ' LIMIT '.$this->limit : '');
		#);

		$this->userlist =
			!isset($this->nocache)
		?	memcached_rowuse_array($this->tables, $qstr)
		:	db_rowuse_array($this->tables, $qstr);

		$userlist_size = $this->userlist ? count($this->userlist) : 0;

		if (isset($this->try_userid)) {
			if (false === ($user = memcached_single_assoc(
				$this->tables,
				#str_replace('XX.', $this->table.'.',
				'SELECT '.
					(isset($this->need_distinct) ? 'DISTINCT ' : '').
					implode(', ', $selects).
				' FROM '.$this->table.' AS XX '.
				(!empty($this->joins) ? implode(' ',$this->joins) : '').
				' WHERE XX.USERID='.$this->try_userid))
			) {
				return false;
			}
			if ($user) {
				$this->userlist[] = $user;
				++$userlist_size;
			}
		}

		if (!$this->userlist) {
			return true;
		}

		if (empty($this->show_invisible)) {
			if (!($this->userlist = filter_invisible($this->userlist,$this->source_element === 'party' ? ['PROFILE','AGENDA'] : 'PROFILE'))) {
				return true;
			}
			$userlist_size = count($this->userlist);
		}

		if (!($this->size += $userlist_size)) {
			return true;
		}

		if (!isset($_SERVER['AJAX'])) {
			# only first list for normal page, calls via AJAX means the user pressed a button to get the other type
			if ($this->size >= FIRST_LIST_FROM) {
				$this->show_images = false;
			}
		}

		if (isset($this->onlybuddies)) {
			require_once '_buddies.inc';
			$newuserlistsize = 0;
			foreach ($this->userlist as $user) {
				if (is_buddy($user['USERID'])) {
					$newuserlist[] = $user;
					++$newuserlistsize;
				}
			}
			$this->size = $newuserlistsize;
			$this->userlist = $newuserlistsize ? $newuserlist : null;
			if (!$this->userlist) {
				return true;
			}
		}
		if ($this->show_camera || $this->show_heart) {
			require_once '_hosts.inc';
			$anybuddy = false;
			foreach ($this->userlist as $user) {
				$userid = $user['USERID'];
				if ($this->show_camera) {
					$ids[] = $userid;
				}
				if ($this->show_heart
				&&	!$anybuddy
				&&	(	isset($this->half_buddy[$userid])
					||	isset($this->full_buddy[$userid])
					)
				) {
					$anybuddy = true;
					if (!$this->show_camera) {
						break;
					}
				}
			}
			if (!$anybuddy) {
				$this->show_heart = false;
			}
			if ($this->show_camera) {
				sort($ids);
				$this->hasphoto = memcached_boolean_hash(
					'user_appearance','
					SELECT DISTINCT USERID
					FROM user_appearance
					WHERE USERID IN ('.implode(',',$ids).')',
					ONE_HOUR
				);
				if (empty($this->hasphoto)) {
					$this->show_camera = false;
				}
			}
		}
		require_once '_sort.inc';
		string_sort($this->userlist,'NICK');

		if (!empty($GLOBALS['currentuser']->row['DATING'])
		&&	empty($this->show_dating)
		) {
			$this->show_dating = true;
		}
		return true;
	}

	function size(): int {
		return $this->size;
	}

	function display_cell(array &$row): void {
		/** @noinspection PhpRedundantOptionalArgumentInspection */
		extract($row, \EXTR_OVERWRITE);
		$age = (
			isset($this->show_age)
		||	$this->show_images
		)
		&&	$BIRTH_YEAR !== 0
		&&	(!isset($this->deceasedlist) || $DSTAMP)
		?	compute_age($BIRTH_YEAR, $BIRTH_MONTH, $BIRTH_DAY, isset($this->deceasedlist) ? $DSTAMP : CURRENTSTAMP, $LDAY)
		:	0;

		?><div<?
		require_once '_visibility.inc';
		$class = null;
		$user_classes = null;
		$invisible = empty($this->show_invisible) && invisible_profile($USERID);

		if ($STATUS !== 'active') {
			$user_classes[] = 'status-'.$STATUS;
		}
		if (isset($this->hilite_self) && have_self($USERID)) {
			$class = 'user-hilite-self';
		} elseif (
			empty($this->hide_winners)
		&&	(	!isset($this->won)
			||	$this->won === true
			||	isset($this->won[$USERID])
			)
		&&	!empty($IS_WINNER)
		&&	(	$TOLD
			||	have_admin('contest')
			)
		) {
			$class = 'user-won-contest';
		} elseif (
			$this->hilite_online
		&&	(SERVER_SANDBOX ? !mt_rand(0,10) : is_online($USERID))
		) {
			$class = 'online';
		} elseif (
			isset($this->try_userid)
		&&	$USERID === $this->try_userid
		) {
			$class = 'bold';
		}
		?> class="u-cell<?
		if (isset($this->show_age)
		||	isset($this->show_email)
		) {
			?> relative<?
		}
		if ($class) {
			?> <? echo $class;
		}
		?>"><?

		if ($this->show_images) {
			if (isset($this->these_users)) {
				$info = $this->these_users[$USERID];
				if (!empty($info['SHOOT_CNT'])) {
					?><div class="r small right"><?
					echo $info['SHOOT_CNT'] ?><br /><?
					echo $info['IMAGE_CNT'] ?><br /><?
					_date_display_numeric($info['LAST_STAMP']);
					?></div><?
				}
			}
		}

		$shown_camera = false;
		if (!$this->show_images) {
			if ($this->show_camera) {
				if (!$invisible
				&&	isset($this->hasphoto[$USERID])
				) {
					self::show_camera($USERID);
				} else {
					?><span class="ib cam"></span><?
				}
			}
			$shown_heart = false;
			if ($this->show_heart) {
				$half = isset($this->half_buddy[$USERID]);
				$full = isset($this->full_buddy[$USERID]);
				if ($half || $full) {
					self::show_heart($full);
				} else {
					?><span class="ib hrt"></span><?
				}
			}
			show_gender_symbol($row);
		}
		if (!$invisible
		&&	isset($this->show_age)
		&&	$age
		&&	_visibility($row,'AGE')
		) {
			?><span style="position:absolute;top:0;right:1em"><?= $age ?></span><?
		}
		if (isset($this->show_email)) {
			?><span style="position:absolute;top:0;right:0"><?
			echo escape_specials($EMAIL);
			?></span><?
		}

		$link =	isset($this->specialurl)
		?	$this->specialurl.$USERID
		:	get_element_href('user', $USERID);

		$show_dating =
			$row['STATUS'] === 'active'
		&&	isset($this->show_dating)
		&&	(require_once 'defines/dating.inc')
		&&	($user = memcached_user($row['USERID']))
		?	$user['DATING']
		:	0;

		if ($this->show_images) {
			ob_start();
			if ($this->show_camera
 			&&	$invisible
 			&&	isset($this->hasphoto[$USERID])
 			) {
 				self::show_camera($USERID);
			}
			if ($this->show_heart) {
				$half = isset($this->half_buddy[$USERID]);
				$full = isset($this->full_buddy[$USERID]);
				if ($half || $full) {
					self::show_heart($full);
				}
			}
			$extra = ob_get_clean();

			if (($uimg = $invisible ? null : get_user_image($row['USERID']))
			&&	($img = _profileimage_get($row))
			) {
				$height = $uimg['HEIGHT'];
				if ($uimg['LARGE'] > 1) {
					$height /= 2;
				}
				if ($height > 100) {
					$margin = ($height - 100) / 2;
					$img = str_replace(';height:',';margin-top:-'.$margin.'px;height:',$img);
				}
				$img = str_replace('<img ','<img loading="lazy" data-lazyload="true" ',$img);
			} else {
				$img = null;
			}

			?><div class="pi"><?
			if (!$img) {
				?><div class="unknown"><?
				?><a href="<?= $link ?>"><?
				?><img loading="lazy" src="<?= STATIC_HOST ?>/images/user_<?= CURRENTTHEME, is_high_res() ?>.png" /><?
				?></a><?
				?></div><?
			} else {
				echo $img;
			}
			?></div> <?
			?><div class="ib vtop"><?
			?><div class="bold"><?
		}

		$NICK = $invisible ? get_element_title('user',$USERID) : memcached_nick($USERID);

		?><a<?
		if (isset($this->rel)) {
			?> rel="<?= $this->rel ?>"<?
		}
		if (isset($this->itemprop)) {
			?> itemprop="<?= $this->itemprop ?>"<?
		}
		if ($user_classes) {
			?> class="<?= implode(' ', $user_classes) ?>"<?
		}
		?> href="<?= $link ?>"<?
		?> title="<?= !empty($FAN_STAMP) ? __('attrib:fan_since').' '._date_get($FAN_STAMP) : escape_specials($NICK) ?>"<?
		?>><?=
			$this->show_images
			?	_smiley_replace(escape_specials(_smiley_prepare($NICK)))
			:	escape_specials($NICK)
		?></a><?

		if ($this->show_images) {
			?></div><?
			if ($show_dating || $extra) {
				?><div><?
				echo $extra;
				if ($show_dating) {
					require_once '_dating.inc';
					show_dating_squares($row,is_number($this->show_dating) ? $this->show_dating : null);
				}
				?></div><?
			}
			if (!$invisible) {
				ob_start();
				if ($SEX
				&&	_visibility($row,'GENDER')
				) {
					show_gender($SEX);
				}
				if ($age
				&&	_visibility($row,'AGE')
				) {
					?> <small><?= $age ?></small><?
				}
				if ($data = ob_get_clean()) {
					?><div><?= $data ?></div><?
				}
				if (isset($this->deceasedlist)) {
					?><div class="small"><? echo __('status:deceased');
					if ($DSTAMP) {
						?>: <? _date_display($DSTAMP);
					}
					?></div><?
				}
				if (($CITYID || $COUNTRYID)
				&&	_visibility($row,'CITY')
				) {
					?><div><small><?
					if ($CITYID) {
						echo get_element_link('city',$CITYID);
					}
					static $__currentcountryid = false;
					if ($__currentcountryid === false) {
						$__currentcountryid =
							($currentcity = get_current_city())
						?	$currentcity['COUNTRYID']
						:	1;
					}
					if ($COUNTRYID && (!$CITYID && $COUNTRYID !== $__currentcountryid)) {
						if ($row['CITYID']) {
							?>, <?
						}
						echo get_element_link('country', $COUNTRYID);
					}
					?></small></div><?
				}
			}
			?></div><?
		} elseif ($show_dating) {
			require_once '_dating.inc';
			?> <div class="ib rmrgn"><?
			show_dating_squares($row,is_number($this->show_dating) ? $this->show_dating : null);
			?></div><?
		}

		?></div><?
	}
	function list_deceased() {
		$this->deceasedlist = true;
	}
	function display_deceased(array $userlist): void {
		if (have_user()) {
			require_once '_buddies.inc';
			$buddies = _buddies_full_cached_list(CURRENTUSERID);
			if ($buddies) {
				$havebuddy = false;
				foreach ($userlist as &$user) {
					if (isset($buddies[$user['USERID']])) {
						$user['BUDDY'] = true;
						$havebuddy = true;
					}
				}
				unset($user);
			}
		}
		require_once '_heart.inc';
		layout_open_table(TABLE_FULL_WIDTH | TABLE_SORTABLE);
		layout_start_header_row();
		if (!empty($havebuddy)) {
			layout_header_cell();
		}
		layout_header_cell(Eelement_name('user'));
		layout_header_cell_right(Eelement_name('date_of_death'));
		layout_header_cell_right(__C('field:last_change'));
		layout_stop_header_row();
		foreach ($userlist as $user) {
			layout_start_rrow();
			if (!empty($havebuddy)) {
				if (isset($user['BUDDY'])) {
					show_heart(element_name('buddy'));
				}
				layout_next_cell();
			}
			echo get_element_link('user',$user['USERID'],$user['NICK']);
			set_cell_value($user['DSTAMP']);
			layout_next_cell(class: 'right');
			if ($user['DSTAMP']) {
				_date_display($user['DSTAMP']);
			}
			set_cell_value($user['MSTAMP']);
			layout_next_cell(class: 'right');
			_date_display($user['MSTAMP']);
		}
		layout_close_table();
	}

	function display(int $max = 0): int {
		if (empty($this->userlist)) {
			return 0;
		}
		if (isset($this->only_city)) {
			$size = 0;
			$userlist = [];
			foreach ($this->userlist as $user) {
				if ($user['CITYID'] === $this->only_city) {
					$userlist[] = $user;
					++$size;
				}
			}
		} else {
			$userlist = $this->userlist;
			$size = $this->size;
		}
		if (!$userlist) {
			return 0;
		}
		if ($max !== 0 && $max < $size) {
			// display max entries
		} else {
			$max = $size;
		}

		if (isset($this->only_city)) {
			$this->columns = $size < 10 ? 1 : ($size < 20 ? 2 : $this->columns);
		}
		if ($this->columns === null) {
			require_once '_smallscreen.inc';
			$this->columns = SMALL_SCREEN ? 2 : 3;//min(SMALL_SCREEN ? 2 : 3,ceil($max / 20));
		}
		$percol = $this->columns ? ceil($size / $this->columns) : 0;
		$cnt = 0;

		if ($this->show_images) {
			require_once '_profileimage.inc';
		}

		include_style('userlist');

		if ($allow_tiles_and_comprehensive = $this->size < ONLY_LIST_FROM) {
			if (!empty($this->cacheid)) {
				if (!isset($_SERVER['AJAX'])) {
					$this->show_list_swapp();
				}
				?><div><?
			}
		}
		if ($this->show_images) {
			global $__lazy_load;
			$__lazy_load = true;
			?><div class="imaged lazycontainer"><?
		}
		if (isset($this->deceasedlist)) {
			number_reverse_sort($userlist,'SORTSTAMP');
		}
		if (!$this->show_images
		&&	isset($this->deceasedlist)
		) {
			$this->display_deceased($userlist);
		} else {
			foreach ($userlist as $user) {
				if (0 === $cnt % $percol) {
					if ($cnt) {
						?></div><?
					}
					?><div class="block" style="float:left;width:<?= round(floor(10000 / $this->columns)/100,2) ?>%"><?
				}
				$this->display_cell($user);
				++$cnt;
				if ($cnt === $max) {
					break;
				}
			}
			?></div><?
		}
		if ($this->show_images) {
			?></div><?
		}
		if ($allow_tiles_and_comprehensive) {
			if (!empty($this->cacheid)) {
				?></div><?
				if (!isset($_SERVER['AJAX'])) {
					?></div><?
				}
			}
		}
		return $this->columns;
	}

	function show_list_swapp() {
		require_once '_lazyload.inc';
		include_js('js/userlist');

		ob_start();
		$tiles = $this->size < FIRST_LIST_FROM && !empty($this->show_images);

		?><span<?
		?> class="<?= $tiles ? 'tiled' : 'listed' ?>"<?
		?> data-cacheid="<?= $this->cacheid ?>"<?
		?> data-hash="<?= bin2hex($this->hash) ?>"<?
		if (have_user() || CURRENTIDENTID) {
			?> data-remember="true"<?
		}
		?>><?
		?><span<?
		?> class="tilesbt unhideanchor"<?
		?> onclick="Pf.showUserList(this.parentNode,true)"><?= __('user_lists:comprehensive') ?></span><?
		?> <?= MIDDLE_DOT_ENTITY ?> <?
		?><span<?
		?> class="listbt unhideanchor"<?
		?> onclick="Pf.showUserList(this.parentNode,false)"><?= __('user_lists:short') ?></span><?
		?></span><?
		box::extra_right_header(ob_get_clean());

		?><div id="userlist-<?= $this->cacheid ?>"><?
	}

	function fan_going_to_party(int $PARTYID, $artistids, $maybe) {
		//$this->table = 'going';
		$this->joins[] = 'JOIN going ON XX.USERID=going.USERID';
		# prevously:
		# FORCE KEY (PRIMARY) makes sure the biggest index is used.
		# EXPLAIN says it yields more rows than the non forced key, which implies it's slower, but it of course isn't
		# now:
		# Database seem to select the right execution path
		$this->joins[] = '
			JOIN favourite
			ON  favourite.USERID = going.USERID
			AND ELEMENT = "artist"
			AND favourite.ID IN ('.implode(',',$artistids).')';
		$this->joins[] = 'LEFT JOIN externalsettings AS esf ON esf.USERID=XX.USERID';
		$this->wheres[] = 'MAYBE=b\''.($maybe ? 1 : 0).'\' AND going.PARTYID='.$PARTYID;
		$this->wheres[] = '(ISNULL(esf.USERID)'._where_visible('esf','FAVOURITES',0,' OR ').')';
		$this->need_distinct = true;
		$this->tables['going'] = 'going';
		$this->tables['favourite'] = 'favourite';
		$this->tables['externalsettings'] = 'externalsettings';
	}

	function going_to_party($PARTYID,$maybe = false,$ever = false) {
		if ($ever) {
			# ever implies maybe
			$this->tables['going'] = 'going';
			$this->tables['going_log'] = 'going_log';
			$this->joins[] = 'JOIN (
				SELECT DISTINCT gl.USERID
				FROM going_log AS gl
				LEFT JOIN going AS g ON g.USERID=gl.USERID AND g.PARTYID=gl.PARTYID AND g.MAYBE=0
				WHERE ACTION="downgrade"
				  AND gl.PARTYID='.$PARTYID.'
				  AND g.USERID IS NULL
				UNION
				SELECT USERID
				FROM going
				WHERE MAYBE=1
				  aND PARTYID='.$PARTYID.'
			) AS allgoing ON XX.USERID=allgoing.USERID';
		} else {
			$this->joins[] = 'JOIN going ON XX.USERID=going.USERID';
			$this->wheres[] = ($maybe ? 'MAYBE=1' : 'MAYBE=0').' AND going.PARTYID='.$PARTYID;
			$this->tables['going'] = 'going';
		}
	}
/*	function going_to_location($locationid,$maybe = false) {
		$this->need_distinct = true;
		$this->joins[] = '
			JOIN going ON XX.USERID=going.USERID
			JOIN party ON party.PARTYID=going.PARTYID
			JOIN location ON location.LOCATIONID=party.LOCATIONID';
		$this->wheres[] = ($maybe ? 'MAYBE=1' : 'MAYBE=0').' AND location.LOCATIONID='.$locationid;
		$this->tables['going'] = 'going';
		$this->tables['party'] = 'party';
		$this->tables['location'] = 'location';
	}
	function going_to_city($cityid,$maybe = false) {
		$this->need_distinct = true;
		$this->joins[] = '
			JOIN going ON XX.USERID=going.USERID
			JOIN party ON party.PARTYID=going.PARTYID
			LEFT JOIN location ON location.LOCATIONID=party.LOCATIONID
			LEFT JOIN boarding ON boarding.BOARDINGID=party.BOARDINGID';
		$this->wheres[] = ($maybe ? 'MAYBE=1' : 'MAYBE=0').' AND COALESCE(boarding.CITYID,location.CITYID,party.CITYID)='.$cityid;
		$this->tables['going'] = 'going';
		$this->tables['party'] = 'party';
		$this->tables['location'] = 'location';
		$this->tables['boarding'] = 'boarding';
	}*/
	function maybe_going_to_party($PARTYID) {
		return $this->going_to_party($PARTYID,true);
	}
	function going_to_meeting($meetingid) {
		$this->joins[] = 'JOIN meet ON XX.USERID=meet.USERID';
		$this->wheres[] = 'meet.MEETINGID='.$meetingid;
		$this->tables['meet'] = 'meet';
	}
	function in_flockmeeting($meetingid) {
		$this->joins[] = 'JOIN flockmeet ON XX.USERID=flockmeet.USERID';
		$this->wheres[] = 'flockmeet.MEETINGID='.$meetingid;
		$this->tables['flockmeet'] = 'flockmeet';
	}
	function contesting_in(int $contestid): void {
		$this->wheres[] = 'contestresponse.CONTESTID = '.$contestid;
		$this->joins[] = 'JOIN contestresponse ON contestresponse.USERID = XX.USERID';
		$this->for_contest = true;
		$this->tables['contestresponse'] = 'contestresponse';
	}

	function fans($type,$id) {
		$this->wheres[] = '(favourite.ELEMENT="'.$type.'" AND favourite.ID='.$id.')';
		$this->joins[] = 'JOIN favourite ON favourite.USERID=XX.USERID';
		$this->tables['favourite'] = 'favourite';
	}
	function only_buddies_as_filter() {
		$this->onlybuddies = true;
	}
	function buddies_of_user($userid) {
		if ($userid === CURRENTUSERID) {
			$this->buddies_of_me = true;
		} else {
			$this->buddies_of_user = $userid;
		}
	}
/*	function userid_in($array) {
		sort($array);
		$this->wheres[] = 'XX.USERID IN ('.implode(',',$array).')';
	}*/
	function non_perm_banned() {
		$this->wheres[] = 'user_account.STATUS!="permbanned"';
	}
	function only_active() {
		$this->oa = true;
	}
	function only_nonactive() {
		$this->wheres[] = 'user_account.STATUS!="active"';
	}
	function any_status() {
	}
	function with_status($status) {
		$this->wheres[] = 'user_account.STATUS="'.addslashes($status).'"';
	}
	function birthday_today() {
		global $__year,$__month,$__day;
		$this->table = 'user';
		$this->wheres[] = '
			(	BIRTH_MONTH='.$__month.' AND BIRTH_DAY='.$__day.(
				$__month === 2 && $__day === 28
			?	' OR BIRTH_MONTH=2 AND BIRTH_DAY=29 AND LDAY=28'
			:	(	$__month === 3 && $__day === 1
				?	' OR BIRTH_MONTH=2 AND BIRTH_DAY=28 AND LDAY=1'
				:	null
				)
			).')
			AND (BIRTH_YEAR>'.$__year.' - 100 OR BIRTH_YEAR=0) '.
			_where_visible('user','BIRTHDAY');
	}
	function born_on_day_and_month($day,$month) {
		global $__year;
		$this->table = 'user';
		$this->wheres[] = '
				BIRTH_MONTH='.$month.'
			AND BIRTH_DAY='.$day.'
			AND (BIRTH_YEAR>'.$__year.' - 100 OR BIRTH_YEAR=0) '._where_visible('user','BIRTHDAY');
#			AND VISIBILITY_BIRTHDAY!=3';
	}
	function only_online() {
		$this->only_online = true;
	}
	function only_online_respect_privacy() {
		$this->only_online_respect_privacy = true;
	}
	function only_suitable() {
	}
	function only_available() {
		require_once '_dating.inc';
		$this->available = get_dating_info();
		if (have_user()) {
			if ($_REQUEST['SUBACTION'] !== 'suitable') {
				global $currentuser;
				switch ($gender = $currentuser->row['SEX']) {
				case 'M':
				case 'F':
					switch ($currentuser->row['SEXPREF']) {
					case 'homo':
						# show other gender
						$this->also_include_gender = $gender === 'M' ? 'F' : 'M';
						break;
					case 'bi':
						# hide current GENDER
						break;
					case 'hetero':
						# show current GENDER
						$this->also_include_gender = $gender;
						break;
					}
					break;
				}
			}
		} else {
			$this->wheres[] = 'XX.DATING';
		}
	}
	function only_registered() {
		$this->wheres[] = 'XX.USERID!=1';
	}
	function nick_like($str) {
		$str = mytrim(strip_shy($str));
		if (is_number($str)) {
			$this->try_userid = $str;
		}
		$this->nick_like = $str;
	}
	function find_on_nick_name_or_email($str) {
		$str = mytrim(strip_shy($str));
		if (is_number($str)) {
			$this->try_userid = $str;
		}
		if (!$str) {
			$this->wheres[] = '0';
			return;
		}
		$this->find_on_nick_name_or_email = $str;
	}
	function of_gender($gender) {
		require_once '_visibility.inc';
		$this->table = 'user';
		$this->wheres[] = 'XX.SEX="'.addslashes($gender).'"'._where_visible('user','GENDER');
	}
	function of_sexpref($sexpref) {
		require_once '_visibility.inc';
		$this->table = 'user';
		$this->wheres[] = 'XX.SEXPREF="'.addslashes($sexpref).'"'._where_visible('user','SEXPREF');
	}
	function limit($limit) {
		$this->limit = $limit;
	}
	function with_relation(string $relation): void {
		require_once '_visibility.inc';
		$this->table = 'user';
		$this->wheres[] = 'XX.RELATION="'.addslashes($relation).'"'._where_visible('user','RELATION');
	}
	function with_relation_or_null(string $relation): void {
		require_once '_visibility.inc';
		$this->table = 'user';
		$this->wheres[] = 'XX.RELATION IN ("","'.addslashes($relation).'")'._where_visible('user','RELATION');
	}
	function in_age_range(int $minage, int $maxage): void {
		require_once '_visibility.inc';
		global $__year, $__month, $__day;

		$this->table = 'user';
		$firstyear = $__year - $maxage - 1;
		$lastyear  = $__year - $minage;
		$firstdaynum = to_days($firstyear, $__month, $__day)	 - DAYNUMOFFSET;
		$lastdaynum  = to_days($lastyear,  $__month, $__day) - 1 - DAYNUMOFFSET;

		$this->wheres[] =
			'BIRTHODAYNUM BETWEEN '.$firstdaynum.' AND '.$lastdaynum.' AND ('.
				(_where_visible('user', 'AGE',	  0, '') ?: 1).
			' OR '.	(_where_visible('user', 'BIRTHDAY', 0, '') ?: 1).
			')';
	}
	function of_age($age) {
		require_once '_visibility.inc';
		global $__year,$__month,$__day;
		$this->table = 'user';
		$firstyear = $__year-$age-1;
		$lastyear = $__year-$age;
		$firstdaynum = to_days($firstyear,$__month,$__day) - DAYNUMOFFSET;
		$lastdaynum = to_days($lastyear,$__month,$__day)-1 - DAYNUMOFFSET;
		$this->wheres[] =
			'((BIRTH_YEAR='.$firstyear.' AND BIRTH_MONTH>'.$__month.' OR BIRTH_YEAR='.$lastyear.' AND BIRTH_MONTH<'.$__month.') OR '.
			'BIRTHODAYNUM BETWEEN '.$firstdaynum.' AND '.$lastdaynum.') AND ('.
				(_where_visible('user','AGE',0,'') ?: 1).
			' OR '.	(_where_visible('user','BIRTHDAY',0,'') ?: 1).
			')';
	}
	function of_age_going($age) {
		$this->table = 'user';
		$this->wheres[] = 'BIRTH_YEAR!=0 AND BIRTH_MONTH!=0 AND BIRTH_DAY!=0 AND ('.
				(_where_visible('user','AGE',0,'') ?: 1).
			' OR '.	(_where_visible('user','BIRTHDAY',0,'') ?: 1).
			')';
		$this->wheres[] = 'FLOOR((TO_DAYS(FROM_UNIXTIME(findage.STAMP))-TO_DAYS(CONCAT(BIRTH_YEAR,"-",BIRTH_MONTH,"-",BIRTH_DAY)))/365.2425)='.$age;
		$this->joins[] = 'JOIN party AS findage ON findage.PARTYID=going.PARTYID';
		$this->tables['party'] = 'party';
	}
	function require_photo() {
		require_once '_visibility.inc';
		$this->need_distinct = true;
		$this->tables['user_appearance'] = 'user_appearance';
		$this->tables['externalsettings'] = 'externalsettings';
		$this->joins[] = 'JOIN user_appearance ON user_appearance.USERID=XX.USERID';
		$this->joins[] = 'LEFT JOIN externalsettings AS esapp ON esapp.USERID=XX.USERID';
		//$this->wheres[] = '(SELECT 1 FROM user_appearance WHERE user_appearance.USERID=XX.USERID LIMIT 1)';
		$this->wheres[] = '(ISNULL(esapp.USERID) '._where_visible('esapp','APPEARANCES',0,' OR ').')';
	}
	function with_album() {
		$this->need_distinct = true;
		array_unshift($this->tables,'albumelement','albummap');
		$this->joins[] = 'JOIN albummap ON albummap.ALBUMID=XX.USERID JOIN albumelement ON albumelement.ALBUMID=XX.USERID';
		//$this->wheres[] = '(SELECT 1 FROM albumelement WHERE albumelement.USERID=XX.USERID LIMIT 1)';
	}
	function with_email($email) {
		$this->table = 'user';
		$email = db_quote_wild($email);
		$this->wheres[] = '(EMAIL LIKE "%'.addslashes($email).'%")';
	}
	function with_site($site) {
		$this->table = 'user';
		$site = db_quote_wild($site);
		$this->wheres[] = 'SITE LIKE "%'.addslashes($site).'%"';
	}
	function with_usertext($text) {
		$text = db_quote_wild($text);
		$ids = db_simpler_array('user_text','SELECT USERID FROM user_text WHERE BODY LIKE "%'.addslashes($text).'%" LIMIT 1001');
		if (!$ids) {
			$this->wheres[] = 0;
			return;
		}
		sort($ids);
		$this->wheres[] = 'XX.USERID IN ('.implode(', ',$ids).')';
	}
	function with_realname($realname) {
		$this->table = 'user';
		$realname = db_quote_wild($realname);
		$this->wheres[] = 'REALNAME!="" AND REALNAME LIKE "%'.str_replace(' ','%',trim(addslashes($realname))).'%"';
	}
	function with_name($name) {
		require_once '_visibility.inc';
		$this->table = 'user';
		$name = db_quote_wild($name);
		$this->wheres[] = 'XX.NAME!="" AND XX.NAME LIKE "%'.str_replace(' ','%',trim(addslashes($name))).'%"'._where_visible('user','NAME');
	}
	function in_province($provid) {
		require_once '_visibility.inc';
		$this->nocache = true;
		$this->table = 'user';
		$this->tables['city'] = 'city';
		$this->joins[] = 'JOIN city AS inprovince ON inprovince.CITYID = XX.CITYID';
		$this->wheres[] = 'INVALID_CITY=0 AND inprovince.PROVINCEID = '.$provid._where_visible('user','CITY');
	}
	function in_country($countryid) {
		require_once '_visibility.inc';
		$this->table = 'user';
/*		$this->tables['city'] = 'city';
		$this->joins[] = 'JOIN city AS incountry ON incountry.CITYID = XX.CITYID';
		$this->wheres[] = 'INVALID_CITY=0 AND incountry.COUNTRYID='.$countryid._where_visible('user','CITY');*/
		$this->wheres[] = 'INVALID_CITY=0 AND XX.COUNTRYID='.$countryid._where_visible('user','CITY');
	}
	function in_city($cityid) {
		require_once '_visibility.inc';
		$this->table = 'user';
		$this->wheres[] = 'NOT INVALID_CITY AND XX.CITYID IN ('.(is_array($cityid) ? implode(',',$cityid) : $cityid).')'._where_visible('user','CITY');
		if (is_array($cityid)) {
			$this->only_city = true;
		}
	}
	function within_radius($lat,$lon,$radius) {
		$this->table = 'user';
		$cities = memcached_cities_within_radius($lat,$lon,$radius);
		if (!$cities) {
			$this->wheres[] = '0';
		} else {
			require_once '_visibility.inc';
			sort($cities);
			$this->wheres[] = 'XX.CITYID IN ('.implode(',',$cities).')'._where_visible('user','CITY');
		}
	}
	function with_offense() {
		$this->joins[] = 'JOIN offense ON offense.USERID=XX.USERID';
		$this->need_distinct = true;
		$this->tables['offense'] = 'offense';
		//$this->wheres[] = '(SELECT 1 FROM offense WHERE offense.USERID=XX.USERID LIMIT 1)';
	}

	function have_conversation() {
		$this->have_conversation = true;
	}

	function in_flock($flockid) {
		$this->tables['flockmember'] = 'flockmember';
		$this->joins[] = 'JOIN flockmember ON flockmember.USERID=XX.USERID';
		$this->wheres[] = 'flockmember.FLOCKID='.$flockid;
	}
	function flock_request($flockid) {
		$this->tables['flockrequest'] = 'flockrequest';
		$this->joins[] = 'JOIN flockrequest ON flockrequest.USERID=XX.USERID';
		$this->wheres[] = 'flockrequest.FLOCKID='.$flockid;
	}
	function flock_invitation($flockid) {
		$this->tables['flockinvitation'] = 'flockinvitation';
		$this->joins[] = 'JOIN flockinvitation ON flockinvitation.USERID=XX.USERID';
		$this->wheres[] = 'flockinvitation.FLOCKID='.$flockid;
	}
	function these_users($users) {
		if (isset($users[0])) {
			$useridstr = implode(',',$users);
		} else {
			$useridstr = implodekeys(',',$users);
			$this->these_users = $users;
		}
		$this->wheres[] = 'XX.USERID IN ('.$useridstr.')';
	}
}
