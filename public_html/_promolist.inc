<?php

require_once 'defines/promo.inc';
require_once '_promo.inc';

function _promolist_active_count($set = false) {
	static $__activecnt = false;
	if ($set !== false) {
		return $__activecnt = 0;
	}
	if ($__activecnt !== false) {
		return $__activecnt;
	}
	$__activecnt = 0;
	$promos = db_rowuse_hash('promo','
		SELECT PROMOID,TARGETPRICE,ABSTARGET,PRICEID,BUDGET,VERSION,STOPSTAMP,REALSTOPSTAMP,DISCOUNT
		FROM promo
		WHERE ACTIVE=1
		  AND STARTSTAMP<='.TODAYSTAMP.'
		  AND REALSTOPSTAMP>'.TODAYSTAMP.'
		ORDER BY STOPSTAMP ASC'
	);
	if (!$promos) {
		return $__activecnt;
	}
	$promoids = array_keys($promos);
	sort($promoids);
	$promoidstr = implode(',',$promoids);
	$letterstati = [];

	foreach (['promo_letter_v2'] as $table) {
		$tmp_letterstati = db_simple_hash($table,'
			SELECT PROMOID,FLAGS
			FROM '.$table.'
			WHERE USERID='.CURRENTUSERID.'
			  AND !(FLAGS & '.PROMO_CLOSED.')
			  AND PROMOID IN ('.$promoidstr.')'
		);
		if ($tmp_letterstati) {
			$letterstati += $tmp_letterstati;
		}
	}
	if (!$letterstati) {
		return $__activecnt;
	}
	$promos = array_intersect_key($promos,$letterstati);

#	$promoids = array_keys($promos);
#	sort($promoids);
#	$promoidstr = implode(',',$promoids);
#	need all promo's for active_seens

	global $__active_seens,$__active_promos;

	$__active_seens = db_simple_hash('promoseensmall','
		SELECT PROMOID,STAMP
		FROM promoseensmall
		WHERE PROMOID IN ('.$promoidstr.')
		  AND USERID='.CURRENTUSERID.'
		ORDER BY STAMP DESC
		LIMIT '.MAX_PROMOS_PER_PERIOD
	);

	if ($__active_seens === false) {
		return false;
	}

	foreach ($__active_seens as $promoid => $stamp) {
		$tmp[$promoid] = _datetime_get($stamp);
	}
/*	if (isset($tmp)) {
		print_rr($tmp,'active_seens');
	}*/

	if (!$__active_seens) {
		$pick = MAX_PROMOS_PER_PERIOD;
	} else {
		$havecnt = 0;
		foreach ($__active_seens as $promoid => $stamp) {
			$v = get_v($promoid);
			if ($v == 1) {
				# don't count old promos
				continue;
			}
			if (CURRENTSTAMP - $stamp >= PROMO_PERIOD) {
				break;
			}
			++$havecnt;
			$lstamp = $stamp;
		}
		$pick = $havecnt < MAX_PROMOS_PER_PERIOD ? MAX_PROMOS_PER_PERIOD - $havecnt : 0;
	}

	$__activecnt = 0;

	foreach ($promos as $promoid => $promo) {
		if (!isset($letterstati[$promoid])) {
			continue;
		}
		$flags = $letterstati[$promoid];
		$shown = $flags & PROMO_SHOWN;
		if ($shown
		||	CURRENTSTAMP < $promo['STOPSTAMP']
		&&	$pick
		&&	(	$flags & PROMO_FORCE_RECEIVE
			||	!$promo['BUDGET']
			||	$promo['TARGETPRICE'] < $promo['BUDGET']
			)
		) {
			if (!$shown) {
				--$pick;
				++$__activecnt;
			}
			$actives[$promoid] = [$flags,$promo];
			if (!($flags & PROMO_COUNTER_SHOWN)) {
				$markthese[] = $promoid;
			}
		} else {
			if (!$shown
			&&	CURRENTSTAMP < $promo['STOPSTAMP']
			&&	(	!$promo['BUDGET']
				||	$promo['TARGETPRICE'] < $promo['BUDGET']
				)
			) {
				$ignore[] = $promoid;
			}
		}
	}
	if (isset($actives)) {
		if (isset($markthese)) {
			db_update('promo_letter_v2','
			UPDATE promo_letter_v2 SET
				FLAGS = FLAGS|'.PROMO_COUNTER_SHOWN.'
			WHERE USERID='.CURRENTUSERID.'
			  AND PROMOID IN ('.implode(',',$markthese).')'
			);
		}
		$GLOBALS['__active_promos'] = $actives;
	}
	return $__activecnt;
}
function _promolist_display_active() {
	_promolist_active_count();
	global $__active_promos,$__active_seens;
	if (empty($__active_promos)) {
		return;
	}
	_promolist_active_count(0);
	$promos = [];

	$promoids = array_keys($__active_promos);
	sort($promoids);
	$promoidstr = implode(',',$promoids);

	foreach ([2] as $v) {
		$table = $v >= 2 ? 'promo_letter_v2' : 'promo_letter';
		$subpromos = db_rowuse_hash(
			[$table,'promo'],'
			SELECT	PROMOID,STOPSTAMP,REALSTOPSTAMP,BEFOREGOING,TITLE,SENT_STAMP,COLOR,TEASER,CLICKBOX,CMTS,promo.FLAGS,TEASER_DESIGN,VERSION,BUDGET,PRICEID,DISCOUNT,
				letter.FLAGS AS LETTER_FLAGS
			FROM promo
			JOIN '.$table.' AS letter USING (PROMOID)
			WHERE letter.USERID='.CURRENTUSERID.'
			  AND VERSION='.$v.'
			  AND PROMOID IN ('.$promoidstr.')'
		);
		if ($subpromos) {
			$promos += $subpromos;
		}
	}
	if (!$promos) {
		return;
	}
	$total = count($promos);

	$tmppromos = [];
	# get promos in right order:
	# - first unseen
	# - then ordered by seen
	# - then old ones without seen (can be removed when v2 promos are running a while)
	foreach ($promos as $promoid => $promo) {
		if (!($promo['LETTER_FLAGS'] & PROMO_SHOWN)) {
			$tmppromos[$promoid] = $promo;
		} else {
			# this keeps order of active_seens, and inserts any old promos at the end
			$__active_seens[$promoid] = $promo;
		}
		if ($promo['BEFOREGOING'] === null) {
			# for promo's starting mid day, and haven't been started by the promo_opener script
			require_once '_promogoing.inc';
			start_promo($promoid);
		}
	}
	foreach ($__active_seens as $promoid => $promo) {
		if (!is_array($promo)) {
			continue;
		}
		$tmppromos[$promoid] = $promo;

	}

/*	?><h3 id="promocntheader" data-cnt="<?= $total ?>"><?= Eelement_plural_name('promo') ?></h3><?*/
	/* &middot; <span><?= $total ?></spa></h3><?*/

	_promolist_display_res($tmppromos);

	foreach ($tmppromos as $promoid => $promo) {
		if (!is_array($promo)) {
			continue;
		}
		$flags = $__active_promos[$promoid][0];
		if ($flags & PROMO_TEASER_SHOWN) {
			continue;
		}
		$table = $promo['VERSION'] == 2 ? 'promo_letter_v2' : 'promo_letter';
		if (db_update($table,'
			UPDATE '.$table.' SET
				FLAGS=FLAGS|'.PROMO_TEASER_SHOWN.'
			WHERE !(FLAGS&'.PROMO_TEASER_SHOWN.')
			  AND PROMOID='.$promoid.'
			  AND USERID='.CURRENTUSERID,
			DB_KEEP_CLEAN)
		&&	($aff = db_affected())
		) {
			if (!isset($_active_seens[$promoid])) {
				db_insert('promoseensmall','
				INSERT IGNORE INTO promoseensmall SET
					PROMOID	='.$promoid.',
					USERID	='.CURRENTUSERID.',
					STAMP	='.CURRENTSTAMP
				);
			}
			$promo = $tmppromos[$promoid];
			if ($promo['BUDGET']) {
				require_once '_promoprice.inc';
				if (($price = get_promopriceid($promo['PRICEID']))
				&&	$price['SEEN']
				&&	db_update('promo','
					UPDATE promo SET
						TARGETPRICE=TARGETPRICE+'.$price['SEEN'].'
					WHERE PROMOID='.$promoid)
				) {
				}
				if ($__active_promos[$promoid][1]['TARGETPRICE'] + $price['SEEN'] >= $promo['BUDGET']) {
					db_insert('promobudget','
					INSERT IGNORE INTO promobudget SET
						PROMOID	='.$promoid.',
						BUDGET	='.$promo['BUDGET'].',
						STAMP	='.CURRENTSTAMP
					);
				}
			}
		}
	}
}
function _promolist_display_res($promos,$noactions = false) {
	foreach ($promos as $promoid => $promo) {
		$promoids[] = $promo['PROMOID'];
	}
	$connects = memcached_multirowuse_hash(
		['connect','party'],'
		SELECT MAINID,ASSOCID,STAMP,NAME
		FROM connect
		JOIN party ON PARTYID=ASSOCID
		WHERE MAINTYPE="promo"
		  AND MAINID IN ('.implode(',',$promoids).')
		  AND ASSOCTYPE="party"
		ORDER BY STAMP ASC'
	);
	if ($connects) {
		require_once '_favourite.inc';
		foreach ($connects as $connectgroup) {
			foreach ($connectgroup as $connect) {
				$partyids[] = $connect['ASSOCID'];
			}
		}
		$favinfo = new favouriteinfo($partyids);
	}
	foreach ($promos as $promoid => $promo) {
		?><div id="promoteaser_<?= $promoid ?>"><?
		if (empty($connects[$promo['PROMOID']])) {
			_promo_display_teaser($promo);
		} else	_promo_display_teaser($promo,$connects[$promo['PROMOID']],$favinfo,false,$noactions);

		?><div class="funcs"><?
		if ($promo['CMTS']) {
			?><div class="light l"><?
			?><a href="<?= link_to_comments('promo',$promoid) ?>"><?= Eelement_plural_name('comment'); ?></a> (<?= memcached_single(
				'promo_comment',
				'SELECT COUNT(*) FROM promo_comment WHERE ACCEPTED=1 AND ID='.$promoid
			);
			?>)</div><?
		}
		if (!$promoid) {
			error_log_r($promos,'0 promoid @ '.$_SERVER['REQUEST_URI']);
		}
		?><a href="/promo/<?= $promoid ?>/letter"><?= __C('action:read') ?></a><?

		if (!$noactions
		&&	CURRENTSTAMP < $promo['REALSTOPSTAMP']
		) {
			include_js('js/promo');
			?> <?= MIDDLE_DOT_ENTITY ?> <a href="/msg/markpromoseen/<?= $promoid ?>" onclick="return Pf.markPromo(<?= $promoid ?>,'seen')"><?= __C('action:mark_seen') ?></a><?
		}
		?></div><?
		?></div><?
	}
}
function get_inactive_promoids() {
	$promoids = [];
	foreach (['promo_letter','promo_letter_v2'] as $table) {
		$tmppromoids = memcached_simpler_array(
			[$table,'promo'], '
			SELECT DISTINCT PROMOID
			FROM '.$table.' AS l
			JOIN promo USING (PROMOID)
			WHERE ACTIVE=1
			  AND REMOVED=0
			  AND l.USERID='.CURRENTUSERID.'
			  AND STARTSTAMP < '.TODAYSTAMP.'
/*			  AND STOPSTAMP < '.(TODAYSTAMP + 24*3600).'*/
			  AND (l.FLAGS&'.PROMO_CLOSED.' OR STOPSTAMP < '.TODAYSTAMP.')',
			TEN_MINUTES
		);
		if ($tmppromoids) {
			$promoids = array_merge($promoids,$tmppromoids);
		}
	}
	return $promoids;
}
function have_inactive_promoids() {
	foreach (['promo_letter_v2','promo_letter'] as $table) {
		$have = memcached_simpler_array(
			[$table,'promo'],'
			SELECT 1
			FROM '.$table.' AS l
			JOIN promo USING (PROMOID)
			WHERE ACTIVE=1
			  AND REMOVED=0
			  AND l.USERID='.CURRENTUSERID.'
			  AND STARTSTAMP < '.TODAYSTAMP.'
/*			  AND STOPSTAMP < '.(TODAYSTAMP + 24*3600).'*/
			  AND (l.FLAGS&'.PROMO_CLOSED.' OR STOPSTAMP < '.TODAYSTAMP.')
			LIMIT 1',
			  ONE_DAY
		);
		if ($have) {
			return true;
		}
	}
	return false;
}
function _promolist_display_inactive() {
 	$have = have_inactive_promoids();
	if (!$have) {
		return false;
	}
	layout_menuitem(__C('promolist:header:old_promos'),'/promo/pastdeliveries');
	return true;
}
function _promolist_columns() {
	return 'PROMOID,TITLE,promo.RELATIONID,promo.INVOICENR,STARTSTAMP,STOPSTAMP,ACTIVE,SENT_STAMP,promo.REQUEST,REACH,promo.DISCOUNT,PRICEID,TARGETPRICE,COSTSENT,promo.REMOVED,VERSION,BUDGET';
}
function _promolist_display_rows($rowlist,$relation = true,$printwarns = false) {
	$hideremoved = $_REQUEST['sELEMENT'] != 'relation' || !have_admin('promo');
	require_once '_price.inc';
	require_once '_promoprice.inc';
	if (isset($rowlist[0])) {
		$promo = $rowlist[0];
	} else {
		list($ndx,$promo) = keyval($rowlist);
	}
	$doinvoice = array_key_exists('INVOICENR',$promo);
	$isadmin = have_admin('promo');
	if ($printwarns) {
		foreach ($rowlist as $promo) {
			$promoids[] = $promo['PROMOID'];
		}
		$tickets = db_multirowuse_hash('contact_ticket','
			SELECT ID,TICKETID,STATUS,(
				SELECT MAX(CSTAMP) FROM contact_ticket_message AS ctm
				WHERE ctm.TICKETID=ct.TICKETID
				  AND DIRECTION="touser"
				  ) AS LAST_TOUSER
			FROM contact_ticket AS ct
			WHERE STATUS IN ("pending","open")
			  AND ELEMENT="promo"
			  AND ID IN ('.implode(',',$promoids).')
			ORDER BY TICKETID'
		);
	}
	require_once '_lock.inc';
	if (false === ($locked_ids = locked_ids(LOCK_PROMO))) {
		return false;
	}
	foreach ($rowlist as $promo) {
		extract($promo);
		if ($REMOVED
		&&	$hideremoved
		) {
			continue;
		}
		$flags = $doinvoice ? CELL_ALIGN_RIGHT | CELL_LIGHT : 0;
		$rowclasses = array();
		$testpromo = $PROMOID === 9842;
		if ($ACTIVE) {
			$rowclasses[] = 'active';
		}
		if (!$testpromo
		&&	CURRENTSTAMP >= $STARTSTAMP
		&&	CURRENTSTAMP <  $STOPSTAMP
		) {
			$rowclasses[] = 'running';
		}
		if (!empty($promo['REMOVED'])) {
			$rowclasses[] = 'unavailable';
		}
		if (isset($locked_ids[$PROMOID])) {
			$rowclasses[] = 'light';
		}
		layout_start_rrow($flags,$rowclasses);
		if ($doinvoice) {
			if ($INVOICENR) {
				?><a href="<?= get_element_href('promo',$PROMOID,$TITLE);
				?>"><?= $INVOICENR;
				?></a>&nbsp;&middot;&nbsp;<?
			}
			layout_next_cell(class: 'rpad');
		}
		echo get_element_link('promo', $PROMOID, $TITLE);
		if ($printwarns) {
			list($minprice,$maxprice) = get_promoprice($promo);
			if ($minprice > 25000) {
				include_style('votes');
				?> <small class="selected vote super-green"> (<?= __('attrib:min') ?> <?= get_price(round($minprice)) ?>!)</small><?
			} elseif ($maxprice > 50000) {
				include_style('votes');
				?> <small class="vote super-green"> (<?= __('attrib:max') ?> <?= get_price(round($maxprice)) ?>)</small><?
			}
		}
		if ($relation) {
			layout_next_cell();
			if (!empty($NAME)) {
				echo get_element_link('relation', $RELATIONID, $NAME);
			}
		}
		layout_next_cell(class: 'road right nowrap');
		if (!$testpromo) {
			$startdays = to_days($STARTSTAMP);
			if ($printwarns
			&&	$isadmin
			&&	!$ACTIVE
			&&	$startdays <= CURRENTDAYNUM+2
			) {
				if ($STARTSTAMP <= TODAYSTAMP) {
					?><span class="error"><?
					# FIXME: DST problem
					if ($STARTSTAMP < TODAYSTAMP) {
						echo __('date:days_past',array('DAYS'=>floor((TODAYSTAMP - $STARTSTAMP)/ONE_DAY)));
					} else {
						echo __('date:today');
					}
					?></span><?
				} elseif ($startdays <= CURRENTDAYNUM+1) {
					?><span class="warning"><?= __('date:tomorrow'); ?>!</span><?
				} else {
					?><span class="selected warning-nb"><?= __('date:dayaftertomorrow'); ?></span><?
				}
			} else {
				_date_display($STARTSTAMP, short: true, time_span: true);
			}
		}
		layout_next_cell(class: 'right');
		if ($printwarns) {
			if (isset($tickets[$PROMOID])) {
				$max_last = 0;
				foreach ($tickets[$PROMOID] as $ticket) {
					$max_last = max($max_last,$ticket['LAST_TOUSER']);
				}
				if ($days = abs(floor((TODAYSTAMP-$max_last)/24/3600))) {
					echo '-',$days;
				}
			}
			layout_next_cell(class: 'right nowrap');
			if (isset($tickets[$PROMOID])) foreach ($tickets[$PROMOID] as $ticket) {
				$incoming =
					$ticket['STATUS'] == 'open'
				||	($ticket['STATUS'] == 'closed' && $ticket['TRACKIT']);
				?><a href="/ticket/<?= $ticket['TICKETID'] ?>"><?
				?><img class="<?
				echo $incoming ? 'lower icon' : 'mail';
				?>" src="<?= STATIC_HOST ?>/images/<?
				if ($incoming) {
					?>incoming_<?
				}
				?>mail<?= is_high_res() ?>.png" alt="#<?= $ticket['TICKETID'] ?>"></a> <?
			}
		} else {
			echo round(($STOPSTAMP-$STARTSTAMP)/24/3600);
		}
		layout_next_cell(class: 'right');
		if ($VERSION >= 2
		&&	$BUDGET
		&&	(	$ACTIVE
			||	$TARGETPRICE
			)
		) {
			echo floor(100 * $TARGETPRICE / $BUDGET),'%';
		}
		layout_stop_row();
	}
}
