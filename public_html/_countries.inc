<?php

require_once '_memcache.inc';

function get_nonempty_countries(): array {
	return get_countries(true);
}

const COUNTRIES_WITH_CITIES	= true;
const COUNTRIES_WITH_EVENTS	= 2;

function get_countries(bool $nonempty = false): array {
	static $__countries;
	if (isset($__countries[$nonempty])) {
		return $__countries[$nonempty];
	}
	$countries =
		$nonempty
	?	(	$nonempty === COUNTRIES_WITH_EVENTS
		?	memcached_same_hash(['party', 'location', 'boarding', 'city'],'
				SELECT DISTINCT COUNTRYID
				FROM party
				JOIN location USING (LOCATIONID)
				JOIN city ON city.CITYID = location.CITYID
				WHERE party.LOCATIONID != 0
				  AND location.CITYID != 0
				UNION
				SELECT DISTINCT COUNTRYID
				FROM party
				JOIN boarding USING (BOARDINGID)
				JOIN city ON city.CITYID = boarding.CITYID
				WHERE party.BOARDINGID != 0
				  AND boarding.CITYID != 0
				UNION
				SELECT DISTINCT COUNTRYID
				FROM party
				JOIN city USING (CITYID)
				WHERE party.CITYID != 0',
				ONE_HOUR
			)
		:	memcached_same_hash('city', 'SELECT DISTINCT COUNTRYID FROM city', ONE_HOUR)
		)
	:	memcached_same_hash('country', 'SELECT COUNTRYID FROM country', ONE_HOUR);

	if ($countries) {
		$ok_countries = [];
		foreach ($countries as $countryid => &$name) {
			if (!$countryid) {
				continue;
			}
			$ok_countries[$countryid] = __('country:'.$countryid);
		}
		unset($name);
		$countries = $ok_countries;
		asort($countries);
	}
	return $__countries[$nonempty] = $countries;
}

function country_has_cities(int $countryid = 0): array|bool {
	/** @var array<int,true> $__have_cities */
	static $__have_cities = memcached_boolean_hash('city', '
		SELECT DISTINCT COUNTRYID
		FROM city',
		ONE_HOUR
	);
	if (!$__have_cities) {
		return false;
	}
	if (!$countryid) {
		return $__have_cities;
	}
	return isset($__have_cities[$countryid]);
}
