<?php

function get_discounts($relationid = 0) {
	static $discounts = 0;
	if ($discounts === 0) {
		$discounts = db_simple_hash('relation','
			SELECT RELATIONID,CONCAT(LOGO_DISCOUNT,",",IF(DISCOUNT_STOPSTAMP>'.CURRENTSTAMP.',DISCOUNT,0))
			FROM relation
			WHERE DISCOUNT!=0
			   OR LOGO_DISCOUNT!=0'
		);
	}
	return $relationid ? (isset($discounts[$relationid]) ? $discounts[$relationid] : null) : $discounts;
}
function discount_relation_select($selected = 0) {
	$discounts = get_discounts();
	if ($discounts === false) {
		return;
	}
	require_once '_relationlist.inc';
	include_js('js/form/discount');
	?><select style="width:100%;max-width:30em"<?
	if ($discounts) {
		?> onchange="<?
		?>var dsc=[0,0];<?
		?>switch(this.value){<?
		foreach ($discounts as $relationid => $info) {
			list($logo_discount,$discount) = explode(',',$info);
			?>case '<?= $relationid; ?>':<?
			if ($logo_discount) {
				?>dsc[0]=<?= $logo_discount ?>;<?
			}
			if ($discount) {
				?>dsc[1]=<?= $discount ?>;<?
			}
			?>break;<?
		}
		?>}<?
		?>Pf.setDiscount('discount-relation',dsc[0]+dsc[1]);<?
		?>setdisplay('discountinfo',dsc[0]);<?
		?>if(dsc[0]){<?
			?>var url=getobj('logodscurl',true);<?
			?>if(url){<?
				?>url.href='/relation/'+this.value;<?
			?>}<?
		?>}<?
		?>"<?
	}
	?> name="RELATIONID"><?
	?><option></option><?
	relationlist_show_options($selected);
	?></select><?
}

function show_discount_form_row(?array $item = null): void {
	layout_start_row();
		echo Eelement_name('discount');
		layout_field_value();
		show_input([
			'type'		=> 'number',
			'min'		=> 0,
			'max'		=> 100,
			'class'		=> 'three_digits right',
			'id'		=> 'discount',
			'name'		=> 'DISCOUNT',
			'value'		=> !empty($item['DISCOUNT']) ? $item['DISCOUNT'] : null,
		]);
		?> %<?
	layout_restart_row(!$item || empty($item['RELATIONID']) || !get_discounts($item['RELATIONID']) ? ROW_HIDDEN : 0,'discountinfo');
		layout_next_cell();
		?><span class="notice nb ns"><?
		echo __('attrib:including') ?> <?
		?><a target="_blank" id="logodscurl" href="/relation/<?= getifset($item,'RELATIONID') ?>"><?= element_name('logo_discount') ?></a><?
		?></span><?
	layout_stop_row();
}
