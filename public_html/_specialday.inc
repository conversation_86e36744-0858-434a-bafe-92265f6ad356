<?php

declare(strict_types=1);

const SPECIAL_DAYS = [
	# urlpart				=> active
	'carnival'				=> true,
	'easter'				=> true,
	'ascension-day'			=> true,
	'pentecost'				=> true,
	'christmas'				=> true,
	'halloween'				=> true,
	'kings-night-and-day'	=> true,
	'liberation-day'		=> true,
	'new-years-eve'			=> true,
	'new-years-day'			=> true,
	'queens-night-and-day'	=> 2013,
];

function get_special_days(string|int|null $day_name = 0): string|false|array {
	# ACTION null should not be special day
	if ($day_name === 0) {
		return SPECIAL_DAYS;
	}
	if (isset(SPECIAL_DAYS[$day_name])) {
		return $day_name;
	}
	return false;
}

function get_halloween_words(): array {
	static $parts = [
		'boos ',
		'curse',
		'dark',
		'dead',
		'death',
		'doden',
		'dood',
		'fright',
		'ghost',
		'ghoul',
		'halloqueen',	# gay
		'hallow',
		'haunted',
		'hell ',
		' hell',
		'horror',
		'monster',
		'muerte',
		'muertos',
		'nightmare',
		'pumpkin',
		'spook',
		'thriller',
		'trick or treat',
		'vampire',
		'ween',
		'zombie',
	];
	return $parts;
}

function get_carnival_words(): array {
	static $words = [
		'alaaf',
		'bal',
		'carna',
		'carne',
		'carni',
		'carnorval',
		'c@rn',
		'karna',
		'karne',
		'karni',
		'knaltibal',
		'knaltival',
		'oetel',
		'rosenmontag',
		'rosen montag',
		'rosen-montag',
		'struivenbal',
		'thunderdommel',
		'vasteloave',
	];
	return $words;
}

function get_special_day_start_and_stop(
	string		 $special_day,
	int			 $year,
	?_partylist &$partylist = null
): array|false {
	static $__special_day;
	if ( isset($__special_day[$special_day][$year])) {
		return $__special_day[$special_day][$year];
	}

	$jump_day   = 0;
	$jump_month = 0;
	$jump_year  = 0;

	switch ($special_day) {
	case 'easter':
		[, $start, $stop] = get_easter($year);
		break;

	case 'ascension-day':
		[, $start, $stop] = get_ascension($year);
		break;

	case 'pentecost':
		[, $start, $stop] = get_pentecost($year);
		break;

	case 'christmas':
		$start = gmmktime(HOUR_DAY_START, 0, 0, 12, 24, $year);
		$stop  = gmmktime(HOUR_DAY_START, 0, 0, 12, 27, $year) - 1;
		break;

	case 'liberation-day':
		$start = gmmktime(HOUR_DAY_START, 0, 0, 5, 5, $year);
		$stop  = gmmktime(HOUR_DAY_START, 0, 0, 5, 6, $year) - 1;
		$jump_year  = $year;
		$jump_month = 5;
		$jump_day   = 5;
		break;

	case 'halloween':
		# Halloween 31st of October
		# get weekend before and after exact Halloween

		change_timezone('UTC');
		$halloween = gmmktime(22, 0, 0, 10, 31, $year);
		$start = strtotime('last thursday '.HOUR_DAY_START.' AM', $halloween);
		$stop  = strtotime('next monday   '.HOUR_DAY_START.' AM', $halloween);
		change_timezone();

		if ($partylist) {
			$partylist->only_halloween();
		}
		break;

	case 'new-years-eve':
		global $__month;
		if ($__month !== 1) {
			$start_year = $year;
			$stop_year  = $year + 1;
		} else {
			$start_year = $year - 1;
			$stop_year  = $year;
		}
		$start = gmmktime(HOUR_DAY_START, 0, 0, 12, 31, $start_year);
		$stop  = gmmktime(HOUR_DAY_START, 0, 0, 1, 1, $stop_year) - 1;
		$jump_year = $year;
		$jump_month = 12;
		$jump_day = 31;
		break;

	case 'new-years-day':
		$start = gmmktime(HOUR_DAY_START, 0, 0, 1, 1, $year);
		$stop  = gmmktime(HOUR_DAY_START, 0, 0, 1, 2, $year) - 1;
		break;

	case 'carnival':
		$carnival_start  = get_carnival_start($year);
		$carnival_start -= ONE_WEEK - HOUR_DAY_START;
		$carnival_end    = $carnival_start + (3 + 7) * ONE_DAY + HOUR_DAY_START;
		$start 			 = $carnival_start;
		$stop 			 = $carnival_end;
		if ($partylist) {
			$partylist->only_carnival($year);
		}
		break;

	case 'queens-night-and-day':
		if ($year >= 2014) {
			not_found();
			return false;
		}
		/** @noinspection DuplicatedCode */
		[,,,,,, $weekday] = _getdate(strtotime($year.'-04-30'));
		$corr = $weekday ? 0 : -1;
		$start = gmmktime(HOUR_DAY_START, 0, 0, 4, 30 + $corr - 1, $year);
		$stop  = gmmktime(HOUR_DAY_START, 0, 0, 4, 31 + $corr, $year) - 1;
		if ($partylist) {
			$partylist->in_country(1);
		}
		break;

	case 'kings-night-and-day':
		if ($year < 2014) {
			not_found();
			return false;
		}

		[,,,,,, $weekday] = _getdate(strtotime($year.'-04-27'));
		$corr = $weekday ? 0 : -1;
		$start = gmmktime(HOUR_DAY_START, 0, 0, 4, 27 + $corr - 1, $year);
		$stop  = gmmktime(HOUR_DAY_START, 0, 0, 4, 28 + $corr, $year) - 1;
		if ($partylist) {
			$partylist->in_country(1);
		}
		break;
	}

	if (!isset($start)
	||	!isset($stop)
	) {
		mail_log('start or stop not set', item: get_defined_vars());
		not_found();
		return $__special_day[$special_day][$year] = false;
	}

	return	$__special_day[$special_day][$year] = [
		'start'			=> $start,
		'stop'			=> $stop,
		'jump_year'		=> $jump_year,
		'jump_month'	=> $jump_month,
		'jump_day'		=> $jump_day,
		'start_date'	=> _datetime_get($start),
		'stop_date'		=> _datetime_get($stop),
	];
}
