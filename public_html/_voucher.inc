<?php

function show_voucher_row(deflist &$list, array $item): void {
	if (!$item['VOUCHERID']
	||	!($voucher = db_single_assoc('discountvoucher','
			SELECT DISCOUNT, VOUCHER, EXPIRES, USED
			FROM discountvoucher
			WHERE VOUCHERID = '.$item['VOUCHERID'],
			DB_USE_MASTER))
	) {
		return;
	}
	ob_start();
	?><a<?
	?> class="<?= implode(' ',get_voucher_classes($voucher,$item['CSTAMP'])) ?>"<?
	?> href="/test/checkvoucher?VOUCHER=<?= $vchr = escape_specials($voucher['VOUCHER']) ?>"><?= $vchr ?></a><?
	$list->add_row(Eelement_name('voucher'), ob_get_clean());
}

function show_voucher_form_row(?array $item = null): void {
	layout_start_row();
	echo Eelement_name('voucher');
	layout_field_value();
	show_voucher_form_part($item);
	layout_stop_row();
}

function show_voucher_form_part(?array $item = null): bool {
	show_input([
		'name'	=> 'VSTAMP',
		'type'	=> 'hidden',
		'value'	=> ($vstamp = $item && $item['CSTAMP'] ? $item['CSTAMP'] : CURRENTSTAMP)
	]);
	if (empty($item['VOUCHERID'])) {
		$voucher = null;
	} else {
		$voucher = db_single_assoc('discountvoucher','SELECT DISCOUNT,VOUCHER,EXPIRES,USED FROM discountvoucher WHERE VOUCHERID='.$item['VOUCHERID'],DB_USE_MASTER);
		if (!$voucher) {
			return false;
		}
	}
	include_js('js/form/voucher');
	include_js('js/form/discount');

	show_input([
		'type'		=> 'text',
		'name'		=> 'VOUCHER',
		'class'		=> 'center id',
		'value'		=> $voucher['VOUCHER'] ?? null,
		'onkeyup'	=> 'checkVoucher(this)',
		'onchange'	=> 'checkVoucher(this)',
		'pattern'	=> '^\s*[a-zA-Z0-9]{12}\s*$'
	]);
	?><span><?
	show_voucher_discount($voucher,$vstamp);
	?></span><?
	return true;
}

function show_voucher_discount($voucher = null,$vstamp = 0) {
	if (empty($voucher['DISCOUNT'])) {
		return 0;
	}
	if (!$vstamp) {
		$vstamp = CURRENTSTAMP;
	}
	$classes = get_voucher_classes($voucher,$vstamp ?: CURRENTSTAMP);
	?><a title="<?= element_name('more_information') ?>" href="/test/checkvoucher?VOUCHER=<?= escape_specials($voucher['VOUCHER']) ?>" class="small lmrgn<?
	if ($classes) {
		?> <? echo implode(' ',$classes);
	}
	?>"><?= $voucher['DISCOUNT'] ?>%</a><?
	return $vstamp < $voucher['EXPIRES'] ? $voucher['DISCOUNT'] : '';
}
function get_voucher_classes($voucher,$vstamp) {
	$classes = array($vstamp > $voucher['EXPIRES'] ? 'error' : 'notice');
	if ($voucher['USED'] && isset($_SERVER['AJAX'])) {
		$classes[] = 'unavailable light';
	}
	return $classes;
}
function optional_voucherid($olditem = null,&$voucher = null) {
	if (empty($_POST['VOUCHER'])) {
		return 0;
	}
	if (!require_regex($_POST,'VOUCHER','"^\s*[a-zA-Z0-9]{12}\s*$"')) {
		return false;
	}
	$voucher = db_single_assoc('discountvoucher','SELECT VOUCHERID,DISCOUNT,USED FROM discountvoucher WHERE VOUCHER="'.addslashes($_POST['VOUCHER']).'"',DB_USE_MASTER);
	if ($voucher === false) {
		return false;
	}
	if (!$voucher) {
		register_error('voucher:error:nonexistent_LINE',array('VOUCHER'=>$_POST['VOUCHER']));
		return false;
	}
	if ($voucher['USED']) {
		if (!$olditem
		||	$olditem['VOUCHERID']
		&&	$olditem['VOUCHERID'] != $voucher['VOUCHERID']
		) {
			register_warning('voucher:warning:already_used_LINE');
			if (!have_admin(array('promo','job','newsad','ad'))) {
				return 0;
			}
		}
	} else {
		db_update('discountvoucher','UPDATE discountvoucher SET USED=b\'1\' WHERE VOUCHERID='.$voucher['VOUCHERID']);
	}
	return $voucher['VOUCHERID'];
}
function show_voucher_object() {
	global $currentuser;
	if (!$currentuser) {
		require_once '_currentuser.inc';
		require_once '_identity.inc';
		$currentuser = new _currentuser(TINY_USER | SKIP_SETTINGS);
	}
	if (!have_admin(array('promo','newsad','ad','job'))) {
		usleep(mt_rand(500, 2500));
	}
	if (!have_regex($_POST,'VOUCHER','"^\s*[a-zA-Z0-9]{12}\s*$"')
	||	!($vstamp = have_idnumber($_POST,'VSTAMP'))
	) {
		bail(400);
	}
	$voucher = db_single_assoc('discountvoucher','SELECT * FROM discountvoucher WHERE VOUCHER="'.addslashes($_POST['VOUCHER']).'"',DB_USE_MASTER);
	if ($voucher === false) {
		bail(500);
	}
	header('Content-Type: application/json');
	ob_start();
	if ($voucher) {
		$discount = show_voucher_discount($voucher,$vstamp);
	} else {
		?> ?<?
	}
	echo safe_json_encode([
		'DISCOUNT'	=> $discount ?? 0,
		'HTML'		=> ob_get_clean()
	]);
}
