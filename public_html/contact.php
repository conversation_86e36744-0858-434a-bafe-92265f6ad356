<?php

define('CURRENTSTAMP',time());

require_once '_db.inc';
require_once '_currentuser.inc';
require_once '_require.inc';

function bail($errno) {
	http_response_code($errno);
	exit;
}
if (!isset($_REQUEST['ACTION'])) {
	bail(400);
}
if (!have_valid_origin()) {
	bail(403);
}

$currentuser = new _currentuser(TINY_USER | SKIP_SETTINGS);

switch ($_REQUEST['ACTION']) {
case 'markcontestsseen':
	if (!have_admin('contest')) {
		bail(403);
	}
	if (isset($_POST['USERID'])
	&&	is_array($_POST['USERID'])
	) {
		foreach ($_POST['USERID'] as $userid) {
			if (!db_insert('contestseen_userid_log','
				INSERT INTO contestseen_userid_log
				SELECT * FROM contestseen_userid
				WHERE USERID='.$userid)
			||	!db_insupd('contestseen_userid','
				INSERT INTO contestseen_userid SET
					USERID		='.$userid.',
					ADMINID		='.CURRENTUSERID.',
					SEENSTAMP	='.CURRENTSTAMP.'
				ON DUPLICATE KEY UPDATE
					ADMINID		=VALUES(ADMINID),
					SEENSTAMP	=VALUES(SEENSTAMP)')
			) {
				bail(500);
			}
		}
	}
	if (isset($_POST['EMAIL'])
	&&	is_array($_POST['EMAIL'])
	) {
		foreach ($_POST['EMAIL'] as $email) {
			if (!db_insert('contestseen_email_log','
				INSERT INTO contestseen_email_log
				SELECT * FROM contestseen_email
				WHERE EMAIL="'.($email = addslashes($email)).'"')
			||	!db_insupd('contestseen_email','
				INSERT INTO contestseen_email SET
					EMAIL		="'.$email.'",
					ADMINID		='.CURRENTUSERID.',
					SEENSTAMP	='.CURRENTSTAMP.'
				ON DUPLICATE KEY UPDATE
					ADMINID		=VALUES(ADMINID),
					SEENSTAMP	=VALUES(SEENSTAMP)')
			) {
				bail(500);
			}
		}
	}
}
