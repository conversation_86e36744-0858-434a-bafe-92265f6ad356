<?php

declare(strict_types=1);

require_once '_memcache.inc';

function get_poll(int|string $arg, int $elementid = 0): array|false|null {
	require_once '_pollhide.inc';
	if (is_number($arg)) {
		return memcached_single_assoc('poll', "
			SELECT *
			FROM poll
			WHERE POLLID = $arg",
			FIVE_MINUTES,
			'poll:'.$arg
		);
	}
	if ($arg === 'home') {
		require_once '_pollstatus.inc';
		if (CURRENTIDENTID
		&&	($poll = memcached_single_assoc('poll','
			SELECT *
			FROM poll
			WHERE STATUS = '.POLLSTATUS_ACCEPTED."
			  AND NOT CLOSESTAMP
			  AND OPENSTAMP
			  AND ELEMENT = 'home'
			ORDER BY RAND()
			LIMIT 1",
			FIVE_MINUTES))
		&&	!is_hidden_poll($poll['POLLID'])
		) {
			return $poll;
		}
	} elseif ($elementid) {
		return memcached_single_assoc('poll', "
			SELECT *
			FROM poll
			WHERE ELEMENT = '$arg'
			  AND ELEMENTID = $elementid",
			FIVE_MINUTES
		);
	}
	return false;
}

function get_userpoll_count(?int $userid = null): int|bool|null {
	$userid ??= CURRENTIDENTID;
	static	$__user_poll_count = [];
	return	$__user_poll_count[$userid] ??=	memcached_single('poll', "
			SELECT COUNT(*)
			FROM poll
			WHERE USERID = $userid",
			FIVE_MINUTES,
			"user_poll_countL$userid");
}
