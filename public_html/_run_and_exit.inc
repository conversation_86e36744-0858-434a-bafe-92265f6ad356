<?php

declare(strict_types=1);

require_once __DIR__.'/defines/timing.inc';
require_once __DIR__.'/_error.inc';
require_once __DIR__.'/_syslog.inc';

function run_and_exit(
	?string	    	$main			= null,
	?bool    		$run_once		= null,
	?int			$warn_after		= null,
	?string			$lock_extra		= null,
	?bool	    	$syslog			= null,
	bool			$syslog_quiet	= false,
	mixed			$main_parameter	= null,
): never {
	$run_once   ??= false;
	$warn_after ??= TEN_MINUTES;
	$syslog     ??= false;
	/** @noinspection UsingInclusionReturnValueInspection */
	if ($syslog) {
		startlog($syslog_quiet);
	}
	if ($run_once
	&&	(require_once '_runonce.inc')
	&&	already_running($lock_extra, $warn_after)
	) {
		exit(1);
	}
	ob_implicit_flush();

	$ok = (false !== ($main ?: 'main')($main_parameter));

	# Shown warnings and errors? Then set $ok to false if it was still true.
	$shown = show_messages_cli();
	if ($shown && $ok) {
		$ok = false;
	}

	if ($syslog) {
		stoplog($ok);
	}
	exit($ok ? 0 : 1);
}
