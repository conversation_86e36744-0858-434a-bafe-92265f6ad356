<?php

declare(strict_types=1);

function status_name(string $status): string {
	static	$__status_name;
	return	$__status_name[$status] ??= __('status:'.($status ?: 'unknown'));
}

function status_show_options(?string $selected = null): void {
	require_once 'defines/user.inc';
	foreach (USER_STATUSES as $status) {
		?><option<?
		if ($selected === $status) {
			?> selected<?
		}
		?> value="<?= $status ?>"><?=
			status_name($status)
		?></option><?
	}
}

const SSNFLG_LIGHT		= 1;
const SSNFLG_BRACKETS	= 2;
const SSNFLG_SHORT		= 4;

function show_status_name(string $status, int $flags = 0): void {
	$classes =
		$status === 'banned'
	||	$status === 'permbanned'
	?	[$status]
	:	[];

	if ($flags & SSNFLG_LIGHT) {
		$classes[] = 'light';
	}
	if ($classes) {
		?><span class="<?= implode(' ', $classes) ?>"><?
	}
	if ($flags & SSNFLG_BRACKETS) {
		?>(<?
	}
	echo status_name(
		$flags & SSNFLG_SHORT
	&&	$status === 'permbanned'
	?	'banned'
	:	$status
	);
	if ($flags & SSNFLG_BRACKETS) {
		?>)<?
	}
	if ($classes) {
		?></span><?
	}
}
