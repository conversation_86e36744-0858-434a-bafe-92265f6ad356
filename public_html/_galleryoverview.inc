<?php

declare(strict_types=1);

class galleryoverview {
	public int				$year			= 0;
	public string			$show;

	private	bool			$date_in_front	= false;
	private bool			$front			= false;	# display for frontpage
	private string			$loading		= 'eager';	# galleryoverview thumbs appear high up the page
	private array			$views;
	private string			$gallery_idstr;
	private int				$id;
	private _pagecontrols	$controls;
	private array			$shoots			= [];

	final public function query(): bool {
		$base_query = /* @lang MariaDB */ '
			SELECT	gallery.GALLERYID, gallery.PARTYID, gallery.USERID, EXTERN,
					TITLE,VISIBLE, gallery.PSTAMP,
					TO_DAYS(FROM_UNIXTIME(gallery.PSTAMP)) AS ADDED_DAYNUM,
					TO_DAYS(FROM_UNIXTIME('.TODAYSTAMP.')) AS NOW_DAYNUM,
					party.NAME, party.STAMP, SUBTITLE, LOCATIONID,
					gallery.USERID AS PUSERS,
					NICK AS PNICKS,
					ANONYMOUS, NOVERVIEW/*,
					COUNT (IF (WIDTH >= HEIGHT, 1, NULL)) AS LANDASCAPE_TOTAL,
					COUNT (IF (WIDTH  < HEIGHT, 1, NULL)) AS PORTRAIT_TOTAL */
				FROM gallery
				/*LEFT JOIN image USING (GALLERYID, PARTYID)*/
				LEFT JOIN party USING (PARTYID)
				'.join_only_events_for_current_country().'
				LEFT JOIN user_account ON user_account.USERID = gallery.USERID';

		switch ($this->show) {
		case 'inactive':
			$local_shoots = memcached_rowuse_array(['image','party','user','gallery'], /* @lang MariaDB */
				$base_query.'
				WHERE VISIBLE = "no"
				  AND NOVERVIEW = 0
				  AND gallery.CSTAMP > '.(TODAYSTAMP - (SERVER_SANDBOX ? 500 : 14) * ONE_DAY).'
				ORDER BY gallery.PSTAMP DESC,party.STAMP DESC
				LIMIT 30'
			);
			break;

		case 'front':
			$show_shoots = SMALL_SCREEN ? 6 : 9;

			$local_shoots = memcached_rowuse_array(['image','party','user','gallery'], /* @lang MariaDB */
				$base_query.'
				WHERE VISIBLE = "yes"
				  AND NOVERVIEW != 2
				/* GROUP BY GALLERYID */
				ORDER BY PSTAMP DESC
				LIMIT '.$show_shoots);
			break;

		case 'recent':
			if (!($total = memcached_single('gallery', "
				SELECT COUNT(*)
				FROM gallery
				WHERE VISIBLE = 'yes'
				  AND NOVERVIEW != 2"))
			) {
				return $total !== false;
			}

			require_once '_pagecontrols.inc';
			$local_controls = new _pagecontrols();
			$local_controls->set_total($total);
			$local_controls->set_per_page(SMALL_SCREEN ? 16 : 4 * 7);
			$local_controls->set_url('/gallery');
			$local_controls->clear_hash();
			$local_controls->clear_bottom_hash();
			$local_controls->show_prev_next();
			$local_controls->set_order('gallery.PSTAMP, party.STAMP');

			$local_shoots = memcached_rowuse_array(['image','party','user','gallery'], /* @lang MariaDB */ "
				$base_query
				WHERE VISIBLE = 'yes'
				  AND NOVERVIEW != 2".
				$local_controls->order_and_limit()
			);

			$this->controls = $local_controls;
			break;

		case 'year':
			$this->date_in_front = true;
			$local_shoots = memcached_rowuse_array(['gallery', 'party', 'image', 'user_account'], /* @lang MariaDB */
				$base_query.'
				WHERE gallery.PSTAMP BETWEEN '. mktime(0, 0, 0, 1, 1, $this->year).'
										 AND '.(mktime(0, 0, 0, 1, 1, $this->year + 1) - 1).'
				ORDER BY gallery.PSTAMP ASC',
				HALF_HOUR
			);
			break;

		case 'location':
			$local_shoots = memcached_rowuse_array(['gallery', 'party', 'image', 'user_account'], /* @lang MariaDB */ "
				$base_query
				WHERE VISIBLE IN ('yes', 'visitors')
				  AND LOCATIONID = $this->id
				ORDER BY gallery.PSTAMP DESC"
			);
			break;

		case 'organization':
			$local_shoots = memcached_rowuse_array(['gallery', 'party', 'image', 'user_account'], /* @lang MariaDB */ "
				$base_query
				JOIN connect
					 ON MAINTYPE = 'organization'
					AND MAINID = $this->id
					AND ASSOCTYPE = 'party'
					AND gallery.PARTYID = ASSOCID
				WHERE VISIBLE IN ('yes', 'visitors')
				ORDER BY gallery.PSTAMP DESC"
			);
			break;

		default:
			error_log("WARNING galleryoverview->query: unknown show: $this->show");
			return false;
		}
		if (!$local_shoots) {
			return $local_shoots !== false;
		}

		$gallery_admin = have_admin(['camerarequest', 'gallery']);

		require_once '_gallery.inc';
		foreach ($local_shoots as &$shoot) {
			if (!($counts = memcached_single_array('image','
				SELECT	COUNT(IF(HIDDEN, 1, NULL)),
						COUNT(IF(HIDDEN, NULL, 1))
				FROM image
				WHERE GALLERYID = '.$shoot['GALLERYID']
			))) {
				if ($counts === false) {
					return false;
				}
				continue;
			}

			[$shoot['HIDDEN_CNT'], $shoot['VISIBLE_CNT']] = $counts;

			$gallery_ids[] = $shoot['GALLERYID'];

			if ((	!visible_gallery($shoot)
				||	!$shoot['VISIBLE_CNT']
				)
			&&	(	!have_user()
				||	!$gallery_admin
				&&	(   !($photographers = explode_to_hash(',', $shoot['PUSERS']))
					||   !isset($photographers[CURRENTUSERID])
					)
				)
			) {
				continue;
			}
			$this->shoots[] = $shoot;
		}
		unset($shoot);

		if (empty($this->shoots)) {
			mail_log('galleryoverview->query second stage results in empty shoots', item: get_defined_vars());
			return true;
		}
		sort($gallery_ids);
		$this->gallery_idstr = implode(', ', $gallery_ids);

		if (false === ($local_views = memcached_simple_hash('image_gallery_year_counter', "
			SELECT GALLERYID, SUM(VIEWS)
			FROM image_gallery_year_counter
			WHERE GALLERYID IN ($this->gallery_idstr)
			GROUP BY GALLERYID",
			TEN_MINUTES))
		) {
			return false;
		}
		$this->views = $local_views;
		return true;
	}
	final public function show_thumb(array $thumb, string $orientation): void {
		require_once '_star.inc';
		/** @noinspection PhpRedundantOptionalArgumentInspection */
		extract($thumb, \EXTR_OVERWRITE);
		if (!$GALLERYID
		||	!($image = db_single_assoc('image', "
			SELECT IMGID, PARTYID, WIDTH, HEIGHT, THUMB_WIDTH, THUMB_HEIGHT
			FROM image
			WHERE GALLERYID = $GALLERYID
			  AND USERID = $USERID
			  AND HIDDEN = 0
			ORDER BY $orientation DESC, RAND()
			LIMIT 1"))
		) {
			return;
		}
		# Satisfy EA inspection
		assert(is_array($image));
		$width  = $image['WIDTH'];
		$height = $image['HEIGHT'];
		#$pixels = $w * $h;

		# most use aspect 1.5, 100 / 66.6666666
		$max_pixels = ($image['THUMB_WIDTH'] * $image['THUMB_HEIGHT']) << 2;

		scale_max_pixels($width, $height, $max_pixels);

		$size_type_x2 = ($width << 1).'x'.($height << 1);
		$size_type_x1 = $width.'x'.$height;

		if (HOME_THOMAS) {
			?><div style="text-align: left;"><?
			print_rr([
				'image'					=> $image,
				'THUMB_WIDTH twice'		=> $image['THUMB_WIDTH'] << 1,
				'THUMB_HEIGHT twice'	=> $image['THUMB_HEIGHT'] << 1,
				'270 * 2 * 180 * 2'		=> 270 * 2 * 180 * 2,
				'max_pixels'			=> $max_pixels,
				'scaled width x height'	=> $width.' x '.$height,
			]);
			?></div><?
		}

		?><div class="centered relative labelled ib zoomover"><?
		?><div class="relative" style="max-width: <?= $width ?>px;"><?

		if (!empty($thumb['NEW'])) {
			?><div class="abs z2" style="right: .5em; top: .5em;"><?= get_star('yellow') ?></div><?
		}

		if ($comment_count
		=	!ROBOT
		?	memcached_single(['image', 'photo_comment'], "
				SELECT COUNT(*)
				FROM image
					JOIN photo_comment ON ID = IMGID
				WHERE ACCEPTED = 1
				  AND GALLERYID = $GALLERYID",
				TEN_MINUTES)
		:	false
		) {
			show_comment_counter($comment_count, "/gallery/$GALLERYID/comments");
		}
		?><a href="<?= get_element_href('gallery', $GALLERYID) ?>" class="middle"><?

		$x1_src = get_photo_url($image['IMGID'], $size_type_x1);
		$x2_src = get_photo_url($image['IMGID'], $size_type_x2);

		?><img<?
		?> loading="<?= $this->loading ?>"<?
		?> class="ib"<?
		?> alt="<?
		echo Eelement_plural_name('photo');
		if ($image['PARTYID']) {
			if (!($party = memcached_party_and_stamp($image['PARTYID']))) {
				server_failure();
			} else {
				[$y, $m, $d] = _getdate($party['STAMP_TZI'] - $party['AT2400'],'UTC');
				?> <?= escape_utf8($party['NAME'])
				?>, <?= $d
				?> <?= _month_name($m) ?> <? echo $y;
				if ($party['LOCATION_NAME']) {
					?>, <? echo escape_utf8($party['LOCATION_NAME']);
				}
				if ($party['CITY_NAME']) {
					?>, <? echo escape_utf8($party['CITY_NAME']);
				}
			}
		} else {
			echo escape_utf8($TITLE);
		}
		?>"<?
		if (aspect_objects()) {
			?> width="<?= $width ?>"<?
			?> height="<?= $height ?>"<?
			?> style="max-width: <?= SMALL_SCREEN ? '95' : '100' ?>%; width:<?= $width ?>px; height: auto;"<?
		} else {
			?> style="max-width: <?= $width ?>px; width: <?= SMALL_SCREEN ? '95' : '100' ?>%;"<?
		}
		?> src="<?= is_high_res() ? $x2_src : $x1_src ?>"<?
		?> srcset="<?= $x1_src ?> 1x, <?= $x2_src ?> 2x" /><?
		?></a><?
		?></div><?

		?><div><?
			?><div class="subinfo tworows"><?
			?><b><?
			if ($PARTYID) {
				echo get_element_link('party', $PARTYID, $NAME);
			}
			if ($TITLE) {
				if ($PARTYID) {
					?> <?
				}
				echo escape_specials($TITLE);
			}
			?></b><?
			if ($LOCATIONID) {
				?><br /><span class="small light6"><?= get_element_link('location',$LOCATIONID) ?></span><?
			}
			?></div><?
		?></div><?
		?></div><?
	}
	final public function show_thumbs_row(int $start, int $stop, string $orientation): bool {
		if (!isset($this->shoots[$start - 1])) {
			return false;
		}
		?><tr class="photos"><?

		for ($cnt = $start; $cnt <= $stop; ++$cnt) {
			if (!isset($this->shoots[$cnt - 1])) {
				break;
			}
			/** @noinspection PhpRedundantOptionalArgumentInspection */
			extract($this->shoots[$cnt - 1], \EXTR_OVERWRITE);
			if ($GALLERYID >= 5500
			&&	$GALLERYID <= 5506
			) {
				$orientation = 'WIDTH < HEIGHT';
				break;
			}
		}
		for ($cnt = $start; $cnt <= $stop; ++$cnt) {
			if (!isset($this->shoots[$cnt - 1])) {
				break;
			}
			?><td><?
			$this->show_thumb($this->shoots[$cnt - 1], $orientation);
			?></td><?
		}
		?></tr><?
		return true;
	}
	final public function show_thumbs(): void {
		if (empty($this->shoots)) {
			return;
		}
		if (!$this->front) {
			layout_open_box('gallery');
		}

		?><table class="gallery-preview fw rndbrd"><?

		# NOTE: previously this routine wilt alternate landscape with portrait rows.
		#	 	but sometimes no portraits are available, which didn't look nice
		#		for now, just landscapes


		$size = count($this->shoots);

		$per_row = SMALL_SCREEN ? 2 : 3;

		$rows = floor($size / $per_row);

		$last_row = $rows - 1;

		$rowcnt = 0;

		while ($rowcnt <= $last_row) {
			# $want_landscape = true; //mt_rand(0, 3);
			# $orientation = 'WIDTH '.($want_landscape ? '>=' : '<').' HEIGHT';
			$orientation = 'WIDTH >= HEIGHT';
			if (!$this->show_thumbs_row(
					$per_row * $rowcnt + 1,
					$per_row * ($rowcnt + 1),
					$orientation
			)) {
				break;
			}
			++$rowcnt;
		}
		?></table><?

		if (!$this->front) {
			layout_close_box();
		}
	}
	final public function show_shoots(): void {
		if (empty($this->shoots)
		||	empty($this->gallery_idstr)
		) {
			return;
		}
		require_once '_gallery.inc';
		require_once '_heart.inc';
		require_once '_buddies.inc';
		require_once '_buddy_or_self_appearances.inc';

		if (have_user()
		&&	($buddies = _buddies_full_hash(CURRENTUSERID))
		&&	($visible = get_buddy_or_self_appearances($this->gallery_idstr,$buddies))
		) {
			$visible_str = implodekeys(',',$visible);
			if (false === ($buddy_count = memcached_simple_hash(['image', 'externalsettings', 'user_appearance', 'onphoto'], "
				SELECT GALLERYID,COUNT(DISTINCT IMGID)
				FROM image
				LEFT JOIN user_appearance AS ua USING (IMGID)
				LEFT JOIN onphoto USING (IMGID)
				WHERE HIDDEN = 0
				  AND GALLERYID IN ($this->gallery_idstr)
				  AND (	   ua.USERID IN ($visible_str)
				   OR onphoto.USERID IN ($visible_str))
				GROUP BY GALLERYID"))
			) {
				return;
			}
		} else {
			$buddy_count = [];
		}
		# Satisfy EA inspection
		assert(is_array($buddy_count));

		layout_open_box($_REQUEST['sELEMENT'],'photos');
		if (!empty($this->id)) {
			layout_box_header(Eelement_plural_name('photo'));
		}
		$show_hidden =
			($is_admin = have_admin(['photo', 'camerarequest']))
		||	have_admin('photographer');

		layout_open_table('fw sortable hha defpad');
		layout_start_header_row();
		if ($this->date_in_front) {
			layout_header_cell_right(__C('field:added'), CELL_RIGHT_SPACE | NOWRAP);
		}
		layout_header_cell(Eelement_name('party'));
		if ($this->show !== 'location') {
			layout_header_cell(Eelement_name('location'));
		}
		if (!SMALL_SCREEN
		&&	!ROBOT
		) {
			layout_header_cell_right('<span class="help" title="'.__('average_views_per_photo').'">'.Eelement_plural_name('view').'</span>',CELL_RIGHT_SPACE);
		}
		if (!SMALL_SCREEN) {
			layout_header_cell_right(Eelement_plural_name('photo'),CELL_RIGHT_SPACE);
		}
		if (!$this->date_in_front	) {
			layout_header_cell_right(__C('field:added'));
		}
		layout_stop_header_row();
		$row_count = 0;
		foreach ($this->shoots as $shoot) {
			if ($this->date_in_front) {
				set_cell_value($shoot['PSTAMP']);
			}
			layout_start_rrow(
				($this->date_in_front ? CELL_ALIGN_RIGHT | CELL_RIGHT_SPACE | NOWRAP : 0)
			|	($row_count & 1 ? ZEBRA : 0)
			|	(going_to_party($shoot['PARTYID']) ? ROW_GOING : 0)
			|	(!visible_gallery($shoot) ? ROW_LINE_THROUGH : 0)
			|	(!$shoot['VISIBLE_CNT'] ? ROW_LIGHT : 0)
			);
			++$row_count;
			if ($this->date_in_front) {
				_date_display($shoot['PSTAMP'], short: true);
				layout_next_cell();
			}
			?><a href="<?= get_element_href('gallery',$shoot['GALLERYID']) ?>"><?
			if ($shoot['NAME']) {
				echo escape_utf8($shoot['NAME']);
			}
			if (!SMALL_SCREEN
			&&	$shoot['SUBTITLE']
			) {
				?> <small>&middot; <?= escape_utf8($shoot['SUBTITLE']) ?></small><?
			}
			if ($shoot['TITLE']) {
				?> <?
				echo escape_specials($shoot['TITLE']);
			}
			?></a><?
			if ($cnt = $buddy_count[$galleryid = $shoot['GALLERYID']] ?? null) {
				?><a class="r rmrgn" href="/gallery/<?= $galleryid ?>/buddieself"><?
				?><small><?= $cnt ?></small> <? show_heart(); ?></a><?
			}
			if ($show_hidden
			&&	$shoot['HIDDEN_CNT']
			&&	(	$is_admin
				||	$shoot['USERID'] === CURRENTUSERID)
			) {
				?><small class="light nowrap r rmrgn">(<?
					?><a href="/gallery/<?= $shoot['GALLERYID'] ?>/hidden"><?= $shoot['HIDDEN_CNT'] ?> <?= __('status:rejected')
				?>)</small><?
			}
			if ($this->show !== 'location') {
				layout_next_cell();
				if ($shoot['LOCATIONID']) {
					echo get_element_link('location',$shoot['LOCATIONID']);
				}
			}
			if (!ROBOT
			&&	!SMALL_SCREEN
			) {
				layout_next_cell(class: 'right');
				if ($shoot['VISIBLE_CNT']
				&&	isset($this->views[$galleryid])
				) {
					echo round($this->views[$galleryid] / $shoot['VISIBLE_CNT']);
				}
			}
			?>&nbsp;<?
			if (!SMALL_SCREEN) {
				layout_next_cell(class: 'rpad right');
				echo $shoot['VISIBLE_CNT'];
			}
			if (!$this->date_in_front) {
				set_cell_value($shoot['PSTAMP']);
				layout_next_cell(class: 'right nowrap lpad'.(SMALL_SCREEN ? ' small' : ''));
				switch ($shoot['NOW_DAYNUM'] - $shoot['ADDED_DAYNUM']) {
					case 0:	echo __('date:today'); break;
					case 1: echo __('date:yesterday'); break;
					case 2:
					case 3:
					case 4:
					case 5:
					case 6:
						echo weekday_name($shoot['PSTAMP']);
						break;
					default:
							SMALL_SCREEN
						?	_date_display_numeric($shoot['PSTAMP'])
						:	_date_display($shoot['PSTAMP'], short: true);
						break;
				}
			}
			layout_stop_row();
		}
		layout_close_table();
		layout_close_box();
	}
	final public function display(): void {
		if ($this->front = $this->show === 'front') {
			$this->show_thumbs();
			return;
		}
		if (isset($this->controls)) {
			$this->controls->display();
		}

		$this->show_thumbs();
		$this->show_shoots();

		if (isset($this->controls)) {
			$this->controls->display();
		}
	}
	final public function show_year(int $year): void {
		$this->show = 'year';
		$this->year = $year;
	}
	final public function show_inactive(): void {
		$this->show = 'inactive';
	}
	final public function show_recent(): void {
		$this->show = 'recent';
	}
	final public function show_location(int $locationid): void {
		$this->show = 'location';
		$this->id = $locationid;
	}
	final public function show_organization(int $organizationid): void {
		$this->show = 'organization';
		$this->id = $organizationid;
	}
	final public function get_last_galleryid(): int {
		if (empty($this->shoots)) {
			return 0;
		}
		return $this->shoots[0]['GALLERYID'];
	}
}
