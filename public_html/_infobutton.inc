<?php

function show_info_button(
	string  $message_id,
	string  $button_text,
	string  $message,
	?string $header = null,
): void {
	$message_padding = SMALL_SCREEN ? 1 : 5;

	include_js('js/infomessage');

	?><span<?
	?> class="relative unhideanchor"<?
	?> onclick="<?
		?>Pf_showInfoMessage('<?= $message_id ?>');<?
		?>history.pushState(true, null, '/camera/' + message_id);<?
	?>"><?
	?><span<?
	?> style="<?
		?>position:absolute;<?
		?>font-size:175%;<?
		?>top:-.2em;<?
		?>left:.2em;<?
		?>color:<?= LITE ? '#050' : '#0F0' ?>;<?
	?>">&#8505;</span><?
	?><span style="margin-left:1.4em"><?
	echo $button_text;
	?></span><?
	?></span><?

	?><div<?
	?> class="hidden"<?
	?> id="<?= $message_id ?>"<?
	?> onclick="Pf_hideInfoMessage('<?= $message_id ?>')"<?
	?> style="<?
		?>z-index:10000;<?
		?>background:rgba(0,0,0,.4);<?
		?>position:fixed;<?
		?>top:0;<?
		?>left:0;<?
		?>bottom:0;<?
		?>right:0;<?
	?>"><?
	?><div class="bg forcescroll" style="<?
		?>border:1px solid grey;<?
		?>overflow:scroll;<?
		?>padding:<?= $message_padding ?>em;<?
		?>margin:<?= SMALL_SCREEN ? 2 : 10 ?>%;<?
		?>max-height:80%;<?
	?>"><?
	
	if ($header) {
		?><h2><?= $header ?></h2><?
	}
	
	?><div class="relative body"><?
	
	$x_pos = $message_padding / 2;
	
	echo get_close_char([
		'class'		=> (SMALL_SCREEN ? null : 'large ').'rtop fixed',
		'style'		=> "top: -{$x_pos}em; right: -{$x_pos}em",
		'onclick'	=> "Pf_hideInfoMessage('$message_id', true)",
	]);
	echo $message;
	?></div><?
	
	?></div><?
	?></div><?

	?><script>
	addreadyevent(function(){
		if(location.hash === '#<?= $message_id ?>') {
			Pf_showInfoMessage('<?= $message_id ?>');
		}
	});
	</script><?
}
