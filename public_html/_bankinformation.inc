<?php

function show_bank_information($shownick = null,$showkvk = false,$sentstamp = 0) {
	layout_open_box_header();
	echo __C('donation:bankinformation');
	layout_close_box_header();
	$list = new deflist('deflist vtop');
	$list->add_rows(
		!$sentstamp
	||	$sentstamp >= **********
	?	[
		[__C('bankinformation:field:ownername'),	'2XPR'],
		$showkvk ? [Eelement_name('coc(chamber_of_commerce)'), '********'] : null,
		$showkvk ? [Eelement_name('VAT_number'),	'NL855110004B01'] : null,
		[__C('bankinformation:field:city'),		'Utrecht'],
		[__C('bankinformation:field:bank'),		'<a target="_blank" href="https://www.abnamro.nl/">ABN AMRO</a>'],
		[__C('bankinformation:field:iban_number'),	'******************'],
		[__<PERSON>('bankinformation:field:bic'),		'ABNANL2A'],
	]
	:	[
		[__C('bankinformation:field:ownername'),	'Partyflock'],
		$showkvk ? [Eelement_name('coc(chamber_of_commerce)'), '********'] : null,
		$showkvk ? [Eelement_name('VAT_number'),	'NL817978999B01'] : null,
		[__C('bankinformation:field:city'),		'Enschede'],
		[__C('bankinformation:field:bank'),		'<a target="_blank" href="https://www.knab.nl/">KNAB</a>'],
		[__C('bankinformation:field:iban_number'),	'******************'],
		[__C('bankinformation:field:bic'),		'KNABNL2H'],
	]);
	if ($shownick && have_user()) {
		$list->add_row(Eelement_name('your_userid'), CURRENTUSERID);
		$list->set_value_class('forcewrap');
		$list->add_row(Eelement_name('your_nick'), escape_specials($GLOBALS['currentuser']->row['NICK']));
	}
	$list->display();
}
