<?php

function may_speak(?array $item = null): bool {
	static $__okpost;
	if (isset($__okpost)) {
		return $__okpost;
	}
	if (!have_user()) {
		return $__okpost = false;
	}
	switch ($_REQUEST['sELEMENT']) {
	case 'party':
		if (empty($item)) {
			$item = memcached_party_and_stamp($_REQUEST['sID']);
			if (!$item) 	{
				return $__okpost = false;
			}
		}
//		require_once '_going.inc';
		return	$__okpost
		=	/*going_to_party_askonce($_REQUEST['sID'])
		&&*/	CURRENTSTAMP > ($item['STAMP'] + 2*3600)
		&&	!$item['CANCELLED']
		&&	!$item['MOVEDID'];

	case 'location':
/*		$locs = memcached_boolean_hash(
			array('going','party'),'
			SELECT DISTINCT LOCATIONID
			FROM going
			JOIN party USING (PARTYID)
			WHERE MAYBE=0
			  AND going.USERID='.CURRENTUSERID.'
			  AND STAMP<='.TODAYSTAMP,
			3600
		);
		return $__okpost = isset($locs[$_REQUEST['sID']]);*/
		return $__okpost = true;

	case 'organization':
/*		$orgs = memcached_boolean_hash(
			array('going','party','connect'),'
			SELECT DISTINCT ASSOCID
			FROM going
			JOIN party USING (PARTYID)
			JOIN connect ON MAINID=going.PARTYID AND MAINTYPE="party" AND ASSOCTYPE="organization"
			WHERE MAYBE=0
			  AND STAMP<='.TODAYSTAMP.'
			  AND going.USERID='.CURRENTUSERID,
			3600
		);
		return $__okpost = isset($orgs[$_REQUEST['sID']]);*/
		return $__okpost = true;

	case 'artist':
/*		$seen = memcached_boolean_hash(
			array('going','party','lineup'),'
			SELECT DISTINCT ARTISTID
			FROM lineup
			JOIN going USING (PARTYID)
			JOIN party USING (PARTYID)
			WHERE MAYBE=0
			  AND STAMP<='.TODAYSTAMP.'
			  AND going.USERID='.CURRENTUSERID,
			3600
		);
		return $__okpost = isset($seen[$_REQUEST['sID']]);*/
		return $__okpost = true;

	case 'review':
		return $__okpost = true;

	case 'talentupload':
		if (!have_admin()) {
			return $__okpost = (CURRENTUSERID == 358870);
		}
		$talents = memcached_boolean_hash('talentupload','SELECT USERID FROM talentupload');
		return $__okpost = 
			$talents 
		&&	!isset($talents[CURRENTUSERID]);

	case 'music':
		return $__okpost = true;
	
	case 'gallery':
		# return $__okpost = true;
		$shooterid = memcached_single('gallery', 'SELECT USERID FROM gallery WHERE GALLERYID = '.$_REQUEST['sID']);
		if (CURRENTUSERID === $shooterid) {
			return $__okpost = false;
		}
		return $__okpost = true;
	}
	return $__okpost = false;
}
