<?php

declare(strict_types=1);

require_once '_hashes.inc';
require_once '_require.inc';
require_once '_servertype.inc';
require_once '_settings.inc';
require_once '_spider.inc';
require_once 'defines/styles.inc';

function show_stylesheet(
	string	$which,
	?array	$overrule	= null
): string|true {
	static $__id = false;
	if ($overrule
	||	$__id === false
	) {
		$all_settings = setting() ?: DEFAULT_STYLES;
		foreach (DEFAULT_STYLES  as $key => $ignored) {
			if ($overrule === true
			&&	$key === 'THEME'
			) {
				$settings[$key] = (LITE ? 'dark' : 'light');
			} else {
				$settings[$key] = $overrule[$key] ?? $all_settings[$key];
			}
		}
		if (isset($overrule['FORLITE'])) {
			$settings['FORLITE'] = 'FORLITE=1';
		}
		if (false === ($id = get_id_for_style_settings($settings))) {
			return '';
		}
		if (!$overrule) {
		 	$__id = $id;
		}
	} else {
		$id = $__id;
	}
	ob_start();
	?>/style/<?
	echo $which;
	if ($id) {
		echo '_', $id;
	}
	?>_<?= get_hash("style/$which.css.php")
	?>.css<?
	$style_file = ob_get_flush();
	return true;
}

function get_id_for_style_settings(array $style_settings): int|false {
	$sets = [];
	foreach ($style_settings as $key => $val) {
		if (is_bool($val)) {
			$sets[] = $key.'='.($val ? "b'1'" : "b'0'");
		} elseif (is_int($val)) {
			$sets[] = $key.'='.$val;
		} else {
			$sets[] = $key.'="'.$val.'"';
		}
	}
	while (true) {
		if (($id = db_single('stylespec','
			SELECT ID
			FROM stylespec
			WHERE '.implode(' AND ', $sets)))
		||	$id === false
		) {
			return $id;
		}
		if (!db_insert('stylespec', '
			INSERT IGNORE
			INTO stylespec
			SET '.implode(', ', $sets),
			DB_DONT_WARN_DUP_ENTRY
		)) {
			if (is_duplicate()) {
				mail_log('do we every get a duplicate stylespec like now?');
				continue;
			}
			return false;
		}
		return db_insert_id();
	}
}
