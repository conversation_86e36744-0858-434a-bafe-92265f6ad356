<?php

declare(strict_types=1);

const FIND_UPDATED = 2;

function find_organization(string $name, bool|int|null $arg_checked = null): array|false {
	# returns
	#	- array of gids that should be filled in new event
	#	- false on failure

	$hidden_ids = get_hidden_orgs() ?: 0;

	require_once '_layout.inc';

	$force_id = preg_match('"^(.*(~))?(\d+)$"',$name,$match);
	$via_parser = !empty($match[2]);
	# 1 = name~x
	# 2 = ~ (via parser)
	# 3 = id

	$prefix = "
		SELECT	ORGANIZATIONID, organization.NAME, CITYID, COUNTRYID, DEADSTAMP, FOLLOWUPID, COPY_GENRES,
				(SELECT GROUP_CONCAT(GID ORDER BY GID) FROM organization_genre og WHERE og.ORGANIZATIONID = organization.ORGANIZATIONID) AS GIDS
		FROM organization
		LEFT JOIN alternate_name ON ELEMENT = 'organization' AND ID = ORGANIZATIONID";

	if (false === ($elems = $force_id
	?	[]
	:	memcached_rowuse_hash(
		'organization',
		$prefix.'
		WHERE (		'.where_search_title(  'organization.NAME', $name, WILD_BOTH | NAME_UTF8).'
			   OR	'.where_search_title('alternate_name.NAME', $name, WILD_BOTH | NAME_UTF8).'
		)
		  AND ORGANIZATIONID NOT IN ('.implodekeys(',',$hidden_ids).')
		ORDER BY NAME ASC'))
	) {
		return false;
	}
	$elems = (array)$elems;
	if ($force_id
	&&	!isset($elems[$id = $match[3]])
	) {
		$force_id = !empty($match[1]) ? $id : false;
		if ($elem_by_number = db_single_assoc('organization',
			$prefix.'
			WHERE ORGANIZATIONID = '.$id
		)) {
			if ($force_id) {
				$elem_by_number['EXACT'] = true;
			}
			$elems[$id] = $elem_by_number;
		}
	}
	if (!$elems) {
		echo escape_utf8($name) ?>: <?= __('findelement:no_results_LINE') ?><br /><?
		return [];
	}
	require_once '_sort.inc';
	$exact = 0;
	$exact_list = [];
	foreach ($elems as $id => &$elem) {
		if ($force_id === $id
		||	0 === strcasecmp($elem['NAME'], $name)
		) {
			$elem['EXACT'] = true;
			$exact_list[$id] = $elem;
			++$exact;
		}
	}
	unset($elem);
	if (($size = count($elems)) > 20) {
		if (!empty($exact_list)) {
			echo escape_specials($name) ?>: <? __('findelement:many_options_only_exact_LINE') ?><br /><?
			$elems = $exact_list;

		} elseif (!empty($elem_by_number)) {
			echo __('findelement:many_options_only_exact_LINE') ?><br /><?
			$elems = [$elem_by_number['ORGANIZATIONID'] => $elem_by_number];

		} else {
			echo escape_utf8($name) ?>: <?= __('findelement:too_many_options_LINE', ['TOTAL' => $size]) ?><br /><?
			return [];
		}
	}
	string_asort($elems,'NAME');
	$prefill_gids = [];
	foreach ($elems as $id => $elem) {
		static $__shown;
		if (isset($__shown[$id])) {
			continue;
		}
		$__shown[$id] = true;
		$checked =
			$arg_checked
		??	(	!isset($_REQUEST['NODEF'])
			&&	(	$size  === 1
				||	$exact === 1
				&&	isset($elem['EXACT'])
				||	$force_id === $id
				)
			);

		$classes = [];
		if ($arg_checked === FIND_UPDATED) {
			$classes[] = 'updated';
		}
		$classes[] = $checked ? 'bold-hilited' : 'not-bold-hilited';
		if ($via_parser) {
			$classes[] = 'prefilled';
		}
		if ($elem['DEADSTAMP'] || $elem['FOLLOWUPID']) {
			$classes[] = 'light';
		}
		?><label class="basehl cbi<?
		if ($classes) {
			?> <? echo implode(' ',$classes);
		}
		?>"><?
		$input_args = [
			'type'		=> 'checkbox',
			'name'		=> 'ORGANIZATIONID[]',
			'value'		=> $id,
			'class'		=> 'upLite',
			'checked'	=> $checked,
		];
		if ($elem['COPY_GENRES']
		&&	!empty($elem['GIDS'])
		) {
			$input_args += [
				'onclick'	=> /** @lang JavaScript */ 'if(Pf_fillGenres) { Pf_fillGenres(this); }',
				'data-gids'	=> $elem['GIDS']
			];
			if ($checked) {
				$prefill_gids += explode_to_hash(',',$elem['GIDS']);
			}
		}
		show_input($input_args);
		?> <?
		echo escape_utf8($elem['NAME']);
		show_dead($elem);
		if ($elem['CITYID']) {
			require_once '_urltitle.inc';
			?><span class="light6 nb">, <?=
				escape_utf8(get_element_title('city',$elem['CITYID']))
			?></span><?
		}
		if ($elem['COUNTRYID']) {
			require_once '_urltitle.inc';
			?><span class="light6 nb">, <?= __('country:'.$elem['COUNTRYID']) ?></span><?
		}
		if ($elem['FOLLOWUPID']) {
			if (!($name = memcached_organization($elem['FOLLOWUPID']))) {
				mail_log('organization.name is empty for id '.$elem['FOLLOWUPID'], get_defined_vars());
			} else {
				?> &rarr; <?= escape_utf8($name) ?> (<?= element_name('successor') ?>)<?
			}
		}
		?></label><?
		?><br /><?
	}
	return $prefill_gids;
}
