<?php

require_once '_itemlist.inc';

function display_header() {
	require_once '_feed.inc';
	show_feed('interview', FEED_HEADER);

	if (($interviewid = $_REQUEST['sID'])
	&&	($interview = memcached_single_assoc('interview','
			SELECT TEASER, ACCEPTED, PSTAMP, CSTAMP, USERID, INTERVIEWID
			FROM interview
			WHERE INTERVIEWID = '.$interviewid,
			TEN_MINUTES))
	&&	(	have_admin('interview')
		||	$interview['ACCEPTED']
		&&	$interview['PSTAMP'] < CURRENTSTAMP
		||	_item_hashed($interview, $interviewid, 'interview')
		||	is_facebook_bot()
		)
	) {
		include_meta('description', flat_with_entities($interview['TEASER'], UBB_UTF8));
	}
}

function perform_commit() {
	switch ($_REQUEST['ACTION']) {
	case 'commit':		return interview_commit();
	case 'accept':
	case 'unaccept':	require_once '_accept.inc'; return item_set_accept($_REQUEST['ACTION']);
	case 'remove':		require_once '_remove.inc'; return remove_element();
	}
}

function display_body() {
	switch ($_REQUEST['ACTION']) {
	default:		not_found(); return;
	case null:
	case 'remove':		return interview_display_overview();
	case 'single':		return interview_display_single();
	case 'commit':
	case 'comments':
	case 'comment':		return interview_display_single();
	case 'form':		return interview_display_form();
	case 'archive':		return interview_display_archive();
	}
}
function interview_display_overview() {
	_itemlist_display_overview_header();
	_itemlist_display_menu();
	_itemlist_display_prelist();
	_itemlist_display();
}
function interview_display_archive() {
	_itemlist_display_archive_header();
	_itemlist_display_menu();
	_itemlist_display_archive();
}

function interview_display_form() {
	layout_show_section_header();

	if (!require_admin(array('interview','interviewer'))) {
		return;
	}
	if ($interviewid = have_idnumber($_REQUEST,'sID')) {
		if (!require_obtainlock(LOCK_INTERVIEW,$interviewid)) {
			return;
		}
		$interview = db_single_assoc('interview','SELECT * FROM interview WHERE INTERVIEWID='.$interviewid,DB_USE_MASTER);
		if ($interview === false) {
			return;
		}
		if (!$interview) {
			not_found();
			return;
		}
		require_once '_element_access.inc';
		if (!may_change_element('interview',$interviewid,null,true,$interview)) {
			no_permission();
			return;
		}
	} else {
		$interview = null;
	}
	$spec = explain_table('interview');

	?><form<?
	?> accept-charset="utf-8"<?
	?> onsubmit="return submitForm(this)"<?
	?> method="post"<?
	?> action="/interview<?
	if (isset($interview)) {
		?>/<? echo $_REQUEST['INTERVIEWID'];
	}
	?>/commit"><?
	layout_open_box('interview');
	layout_open_table('fw');
	layout_start_row();
		echo Eelement_name('title');
		layout_field_value();
		show_input([
			'spec'		=> $spec,
			'required'	=> true,
			'name'		=> 'TITLE',
			'type'		=> 'text',
			'autofocus'	=> true,
			'value_utf8'	=> $interview['TITLE'] ?? null,
		]);
	layout_restart_row();
		echo element_name('language');
		layout_field_value();
		require_once '_language.inc';
		show_languages_select($interview ? $interview['LANGID'] : 1);

	if (have_admin('interview')) {
		require_once '_authorlist.inc';
		layout_restart_row();
			?><label for="accepted"><?= __C('attrib:accepted') ?></label><?
			layout_field_value();
			?><input type="checkbox" value="1" name="ACCEPTED"<? if ($interview && $interview['ACCEPTED']) echo ' checked';
			?> id="accepted"><?
		layout_restart_row();
			echo Eelement_name('author');
			layout_field_value();
			authorlist_display_select($interview ? $interview['BYUSERID'] : 0,$interview);
	}
	layout_restart_row();
		?><label for="direct"><?= Eelement_name('interview') ?></label><?
		layout_field_value();
		show_publication($interview);

	layout_restart_row();
		echo Eelement_name('teaser');
		layout_field_value();
		show_textarea([
			'required'	=> true,
			'class'		=> 'growToFit',
			'name'		=> 'TEASER',
			'cols'		=> 40,
			'rows'		=> 10,
			'value_utf8'	=> $interview['TEASER'] ?? '',
		]);

	layout_restart_row();
		echo Eelement_name('interview');
		layout_field_value();
		show_textarea([
			'required'	=> true,
			'class'		=> 'growToFit',
			'name'		=> 'BODY',
			'cols'		=> 80,
			'rows'		=> 20,
			'value_utf8'	=> $interview['BODY'] ?? '',
		]);

	layout_stop_row();
	layout_close_table();
	layout_close_box();
	?><div class="block"><?
	?><input type="submit" value="<?= __($interview ? 'action:change' : 'action:add') ?>" /><?
	?></div><?
	?></form><?
}

function interview_display_single() {
	require_once '_connect.inc';
	require_once '_uploadimage.inc';
	if (!($interviewid = require_idnumber($_REQUEST,'sID'))) {
		return false;
	}
	$interview = db_single_assoc('interview','
		SELECT INTERVIEWID, BODY, interview.CSTAMP, PSTAMP, ACCEPTED, TITLE, MSTAMP, USERID, BYUSERID, TEASER, AUTHOR, LANGID
		FROM interview
		WHERE INTERVIEWID = '.$interviewid
	);
	if (!$interview) {
		if ($interview === false) {
			return false;
		}
		not_found();
		return;
	}
	$is_admin = itemlist_is_admin($interview);
	$hashed = _item_hashed($interview);
	if (!$is_admin
	&&	!$hashed
	) {
		if ($interview['PSTAMP'] > CURRENTSTAMP
		&&	!is_facebook_bot()
		) {
			register_error('interview:error:not_published_LINE');
			return false;
		}
		if (!$interview['ACCEPTED']) {
			register_error('interview:error:not_accepted_LINE');
			return false;
		}
	}
	layout_show_section_header();

	_itemlist_display_menu($interview);

	require_once '_ticketlist.inc';
	show_connected_tickets();

	if ($is_admin) {
		_connect_display_form('interview',$interview['INTERVIEWID']);
	}

	if (!SMALL_SCREEN
	&&	($is_admin || $hashed)
	&&	$interview['TEASER']
	) {
		require_once '_itemlist.inc';
		show_item_teaser('interview', $interviewid, $interview);
	}

	?><article itemscope itemtype="https://schema.org/CreativeWork"><?
	require_once '_drag.inc';
	open_drag();
	layout_open_box($interview['ACCEPTED'] ? 'interview' : 'unaccepted interview');
	?><header><?
	layout_box_header('<h1 itemprop="name">'.flat_with_entities($interview['TITLE'], UBB_UTF8).'</h1>');
	?></header><?

	itemlist_show_publish_info($interview);

	$img = uploadimage_get('interview', $interview['INTERVIEWID']);

	?><div class="<?
	if (!SMALL_SCREEN
	&&	$img
	) {
		?>right-float <?
	}
	?>center"><?
	uploadimage_show_from_img($img,UPIMG_SCHEMA | UPIMG_SHOW_HISTORY);
	_connect_display('interview',$interview['INTERVIEWID']);
	?></div><?

	?><div<?
	?> class="lclr minp body block"<?
	?> itemprop="articleBody"<?
	?> lang="<?= get_language($interview['LANGID']) ?>"><?
	echo make_all_html($interview['BODY'], UBB_UTF8, 'interview', $interview['INTERVIEWID']);
	?></div><?

	layout_display_alteration_note($interview, true);
	layout_close_box();

	close_drag();

	require_once '_commentlist.inc';
	$cmts = new _commentlist;
	$cmts->item($interview);
	$cmts->display();

	?></article><?

	counthit('interview',$_REQUEST['sID']);
}

function interview_commit() {
	require_once '_namefix.inc';
	require_once '_ubb_preprocess.inc';
	$setlist = [];
	if (!require_admin(['interview', 'interviewer'])
	||	!require_something_trim($_POST, 'TITLE', null, null, utf8: true)
	||	!require_something_trim($_POST, 'TEASER', null, null, utf8: true)
	||	!require_something_trim($_POST, 'BODY', null, null, utf8: true)
	||	false === require_number($_POST, 'LANGID')
	||	!parse_publication($setlist)
	) {
		return;
	}

	$interviewid = $_REQUEST['sID'];

	$setlist['LANGID'] = 'LANGID	='.$_POST['LANGID'];
	$setlist['TITLE' ] = 'TITLE	="'.addslashes(_ubb_preprocess(get_fixed_utf8_name($_POST['TITLE']), utf8: true)).'"';
	$setlist['TEASER'] = 'TEASER	="'.addslashes($teaser = _ubb_preprocess(get_fixed_utf8_name($_POST['TEASER']), utf8: true)).'"';
	$setlist['BODY'	 ] = 'BODY	="'.addslashes($body = _ubb_preprocess(get_fixed_utf8_name($_POST['BODY']), utf8: true)).'"';

	if (have_admin('interview')) {
		if (!empty($_POST['AUTHOR'])) {
			$setlist['AUTHOR'  ] = 'AUTHOR	 = "'.addslashes($_POST['AUTHOR']).'"';
			$setlist['BYUSERID'] = 'BYUSERID = 0';
		} else {
			if (false === require_number($_POST, 'BYUSERID')) {
				return;
			}
			$setlist['BYUSERID'] = 'BYUSERID = '.$_POST['BYUSERID'];
		}
		$setlist['ACCEPTED'] = 'ACCEPTED = '.(isset($_POST['ACCEPTED']) ? "b'1'" : "b'0'");
	} else {
		$setlist['BYUSERIDS']	= 'BYUSERID	= '.CURRENTUSERID;
		$setlist['ACCEPTED']	= "ACCEPTED	= b'0'";
	}
	if ($interviewid) {
		require_once '_element_access.inc';
		if (!require_last_lock(LOCK_INTERVIEW, $interviewid)
		||	!may_change_element(element: 'interview', id: $interviewid, warn: true)
		||	!db_insert('interview_log','
			INSERT INTO interview_log
			SELECT * FROM interview
			WHERE NOT '.binary_equal($setlist).'
			  AND INTERVIEWID = '.$interviewid)
		) {
			return;
		}
		if (db_affected()) {
			$setlist['MUSERID'] = 'MUSERID = '.CURRENTUSERID;
			$setlist['MSTAMP' ] = 'MSTAMP  = '.CURRENTSTAMP;

			if (!db_update('interview','
				UPDATE interview SET '.implode(', ', $setlist).'
				WHERE INTERVIEWID = '.$_REQUEST['INTERVIEWID'])
			) {
				return;
			}
		}
		register_notice('interview:notice:changed_LINE');
	} else {
		$setlist['USERID'] = 'USERID = '.CURRENTUSERID;
		$setlist['CSTAMP'] = 'CSTAMP = '.CURRENTSTAMP;

		if (!db_insert('interview','
			INSERT INTO interview SET '.implode(', ', $setlist))
		) {
			return;
		}
		$_REQUEST['sID'] = $interviewid = db_insert_id();
		register_notice('interview:notice:added_LINE');
	}
	require_once '_update.inc';
	update_element('interview', $interviewid, $body, $teaser, utf8: true);
}
