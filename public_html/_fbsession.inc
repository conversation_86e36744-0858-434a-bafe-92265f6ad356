<?php

require_once 'vendor/autoload.php';

use Facebook\FacebookJavaScriptLoginHelper;
use Facebook\GraphUser;

require_once '_servertype.inc';
require_once '_helper.inc';
require_once '_db.inc';

const PFFB_V = 5;

define('FB_DEBUG',	!isset($_SERVER['CRON']));

require_once 'defines/facebook.inc';

function token_need_renew($code,$subcode) {
	return	$code === 190
	&&	(	$subcode === 458 # The user has not authorized app
		||	$subcode === 459	# User Checkpointed
		||	$subcode === 460	# Password Changed
		||	$subcode === 463 # Session has expired on ...
		||	$subcode === 464	# Unconfirmed User
		||	$subcode === 466 # The access token is invalid
		||	$subcode === 490	# The user is enrolled in a blocking, logged-in checkpoint
		||	$subcode === 493 # the user hasn't engaged the app in longer than 90 days.
		);
}
function fb_want_permissions() {
	static $__base = [
		'email',			# allowed
		'public_profile',		# allowed
		'user_location',		# allowed
		'user_birthday',		# allowed again on 2018-10-23
		'user_gender',
		'user_likes',
		'user_friends',
#		'pages_show_list',
	];
	return $__base;
}
function set_facebooked(int $v = PFFB_V): void {
	require_once '_identity.inc';
	if (!db_insert('facebooked','INSERT IGNORE INTO facebooked SET V='.$v.',IDENTID='.CURRENTIDENTID.',CSTAMP='.CURRENTSTAMP,DB_DONT_WARN_DUP_ENTRY)) {
		return;
	}
	memcached_delete('fbed:'.CURRENTIDENTID);
}
function have_facebooked(): int|false|null {
	return memcached_single_int('facebooked','
		SELECT V
		FROM facebooked
		WHERE IDENTID = '.CURRENTIDENTID,
		TEN_MINUTES,
		'fbed:'.CURRENTIDENTID
	);
}
function fb_have_connection(?int $userid = null): int|false {
	$userid ??= CURRENTUSERID;
	static $__have = [];
	return $__have[$userid]
	??=	memcached_single('facebook', "
		SELECT FACEBOOKID
		FROM facebook
		WHERE USERID = $userid",
		FIVE_MINUTES,
		'havefb:'.$userid
	)	?: false;
}
function fb_get_active_token(?int $userid = null): array {
	if ($tokens = fb_get_tokens($userid)) {
		foreach ($tokens as $tokeninfo) {
			[,, $expires] = $tokeninfo;
			if ($expires > CURRENTSTAMP) {
				return $tokeninfo;
			}
		}
	}
	return [null, null, null, null];
}
function fb_get_tokens(?int $userid = null) {
	return fb_get_token($userid, true);
}
function fb_get_token(?int $userid = null, bool $all = false): array {
	$userid ??= CURRENTUSERID > 1 ? CURRENTUSERID : ($currentuser->was_userid ?? 0);
	if ($all) {
		static $__have_all = [];
		return $__have_all[$userid]
		??= db_rowuse_array(['facebook', 'facebook_token'], "
			SELECT ACCESSTOKEN, FACEBOOKID, EXPIRES, MSTAMP
			FROM facebook
			LEFT JOIN facebook_token USING (FACEBOOKID)
			WHERE USERID = $userid
			ORDER BY EXPIRES DESC",
			DB_NON_ASSOC
		) ?: [];
	}
	static $__have;
	if (isset($__have[$userid])) {
		return $__have[$userid];
	}
	# get the most non-expired token for user (user might have connected more than one facebook profile to single userid)
	return $__have[$userid] = db_single_array(['facebook', 'facebook_token'], "
		SELECT ACCESSTOKEN, FACEBOOKID, EXPIRES, MSTAMP
		FROM facebook
		LEFT JOIN facebook_token USING (FACEBOOKID)
		WHERE USERID = $userid
		ORDER BY EXPIRES DESC
		LIMIT 1"
	) ?: [null, null, null, null];
}
function fb_expire_token($arg): bool {
	if (is_string($arg)) {
		$access_token = $arg;
	} else {
		$access_token = $arg->getToken();
	}
	if (false === ($userids = db_simpler_array('facebook_token', "
		SELECT DISTINCT USERID
		FROM facebook
		JOIN facebook_token USING (FACEBOOKID)
		WHERE ACCESSTOKEN = '".addslashes($access_token)."'"))
	) {
		return false;
	}
	if ($userids) {
		foreach ($userids as $userid) {
			# clear facebook state:
			memcached_delete('fbtryrenew:'.$userid);
			# clear done:
			memcached_delete('fbtriedrenew:'.$userid);
		}
	}
	return	db_insert('facebook_token_log','
		INSERT INTO facebook_token_log
		SELECT *, '.CURRENTSTAMP."
		FROM facebook_token
		WHERE EXPIRES
		  AND ACCESSTOKEN = '".addslashes($access_token)."'")
	&&	db_update('facebook_token', '
		UPDATE facebook_token SET
			EXPIRES	= 0,
			MSTAMP	= '.CURRENTSTAMP."
		WHERE EXPIRES
		  AND ACCESSTOKEN = '".addslashes($access_token)."'");
}
function fb_have_permission($permission = null,$userid = null,$set = false) {
	if ($userid === null) {
		$userid = CURRENTUSERID;
	}
	if ($userid <= 1) {
		return false;
	}
	static $__permissions;
	if ($set) {
		$__permissions[$userid] = $set;
		return;
	}
	$permissions =
		isset($__permissions[$userid])
	?	$__permissions[$userid]
	:	($__permissions[$userid] = explode_to_hash(',',db_single(['facebook_permissions','facebook'],'
			SELECT SCOPES
			FROM facebook_permissions
			JOIN facebook USING (FACEBOOKID)
			WHERE USERID='.$userid
		)));

	return $permission ? isset($permissions[$permission]) : $permissions;
}

function fb_clear_permissions(int $fb_user_id) {
	return fb_store_permissions($fb_user_id, '');
}

function fb_store_permissions(int $fb_user_id, string $scopestr, int $expires = 0): bool {
	return	db_insert('facebook_permissions_log','
			INSERT INTO facebook_permissions_log
			SELECT *,'.CURRENTSTAMP.' FROM facebook_permissions
			WHERE FACEBOOKID='.$fb_user_id.
			($expires ? ' AND SCOPES!="'.$scopestr.'"' : ''))
	&&		(	$expires
			?	db_insert('facebook_permissions','
				INSERT INTO facebook_permissions SET
					FACEBOOKID	='.$fb_user_id.',
					SCOPES		="'.$scopestr.'",
					CSTAMP		='.CURRENTSTAMP.'
				ON DUPLICATE KEY UPDATE
					CSTAMP		=IF(SCOPES!=VALUES(SCOPES),VALUES(CSTAMP),CSTAMP),
					SCOPES		=IF(SCOPES!=VALUES(SCOPES),VALUES(SCOPES),SCOPES)')
			:	db_delete('facebook_permissions','
				DELETE FROM facebook_permissions
				WHERE FACEBOOKID='.$fb_user_id)
			);
}

function fb_clear_token(int $fb_user_id) {
	return fb_store_token(null, $fb_user_id);
}

function fb_store_token(?string $access_token = null, $metadata = null): bool {
	if (!$access_token) {
		# clear token
		$fb_user_id = $metadata;
		$scopestr = '';
		$no_expires = false;
		$expires = 0;
	} else {
		$fb_user_id = $metadata->getUserId();
		$expires_at = $access_token->getExpiresAt();
		$no_expires = !$expires_at;
		$expires = $expires_at ? $expires_at->getTimestamp() : CURRENTSTAMP + 90 * ONE_DAY; /* no expires_at: app developer */
		$scopes = $metadata->getScopes();
		sort($scopes);
		$scopestr = addslashes(implode(',',$scopes));
	}
	return
		fb_store_permissions($fb_user_id, $scopestr, $expires)
	&&	db_insert('facebook_token_log', $q1 = '
		INSERT INTO facebook_token_log
		SELECT *,'.CURRENTSTAMP.'
		FROM facebook_token
		WHERE FACEBOOKID='.$fb_user_id.'
		  AND '.($expires ? '(ACCESSTOKEN!="'.addslashes($access_token).'" OR '.$expires.'>EXPIRES)' : '1'))
	&&	(	$expires
		?	db_insert('facebook_token', $q2 = '
			INSERT INTO facebook_token SET
				ACCESSTOKEN	="'.addslashes($access_token).'",
				APPID		='.FACEBOOK_APPID.',
				FACEBOOKID	='.$fb_user_id.',
				EXPIRES		='.$expires.',
				NO_EXPIRES	=b\''.($no_expires ? 1 : 0).'\',
				MSTAMP		='.CURRENTSTAMP.'
			ON DUPLICATE KEY UPDATE
				MSTAMP		=IF(VALUES(ACCESSTOKEN)!=ACCESSTOKEN OR VALUES(EXPIRES)>=EXPIRES,VALUES(MSTAMP),MSTAMP),
				APPID		=IF(VALUES(ACCESSTOKEN)!=ACCESSTOKEN OR VALUES(EXPIRES)>=EXPIRES,VALUES(APPID),APPID),
				EXPIRES		=IF(VALUES(ACCESSTOKEN)!=ACCESSTOKEN OR VALUES(EXPIRES)>=EXPIRES,VALUES(EXPIRES),EXPIRES),
				NO_EXPIRES	=IF(VALUES(ACCESSTOKEN)!=ACCESSTOKEN OR VALUES(EXPIRES)>=EXPIRES,VALUES(NO_EXPIRES),NO_EXPIRES),
				ACCESSTOKEN	=IF(VALUES(ACCESSTOKEN)!=ACCESSTOKEN OR VALUES(EXPIRES)>=EXPIRES,VALUES(ACCESSTOKEN),ACCESSTOKEN)')
		:	db_delete('facebook_token','
			DELETE FROM facebook_token
			WHERE FACEBOOKID='.$fb_user_id)
		);
}
function fb_logout() {
	if (!FACEBOOK_ENABLED) {
		return;
	}
}
function fb_login(&$expires = null) {
	session_start();

#	error_log_r($_SESSION,'_SESSION @ fb_login');

	if (!have_idnumber($_SESSION,'fb_user_id')) {
		return false;
	}
	$user = db_single_assoc('facebook','
		SELECT USERID,EXPIRES
		FROM facebook
		JOIN facebook_token USING (FACEBOOKID)
		JOIN user_account USING (USERID)
		WHERE FACEBOOKID='.$_SESSION['fb_user_id'].'
		ORDER BY
			STATUS="active" DESC,
			STATUS="inactive" DESC,
			STATUS="unactive" DESC,
			USERID='.($_SESSION['USERID'] ?? 0).' DESC
		LIMIT 1',
		DB_USE_MASTER
	);
	if (!$user) {
		return false;
	}
	$expires = $user['EXPIRES'];
	return $user['USERID'];
}
function fb_get_user_profile($fb, string $access_token, int $fb_user_id) {
	$key = 'fbinfo:v2:'.$fb_user_id;
	if ($fb_user_id) {
		if ($user_profile = memcached_get($key)) {
			return $user_profile;
		}
	}

	fbgraph_access($fb_user_id,'/me','user_profile');

	$q = null;
	try {
		$response = $fb->get(
			$q = '/me?fields=id,location{id,name,location},picture.type(large),birthday,name,email,gender',
			$access_token
		);
	} catch (Exception $e) {
		return fb_process_exception([
			'exception'	=> $e,
			'access_token'	=> $access_token,
			'userid'	=> CURRENTUSERID,
			'query'		=> $q,
		]);
	}
	if (!$response) {
		error_log('fb_get_user_profile: no response @ '.strip_uri_args($_SERVER['REQUEST_URI']));
		return false;
	}
	$user_profile = $response->getGraphUser();
	if (!$user_profile) {
		error_log('fb_get_user_profile: no user_profile @ '.strip_uri_args($_SERVER['REQUEST_URI']));
		return false;
	}
	memcached_set($key,$user_profile,60);
	return $user_profile;
}

function fb_register() {
	if (!FACEBOOK_ENABLED) {
		return false;
	}
	if (($key = 'fb_user_id')	   && empty($_SESSION[$key])
	||	($key = 'fb_user_profile') && empty($_SESSION[$key])
	) {
		error_log_r($_SESSION,'WARNING missing '.$key.' in _SESSION @ '.$_SERVER['REQUEST_URI']);
		return false;
	}

	$user_profile = $_SESSION['fb_user_profile'];

	[$userimage_tmp, $userimage_crc, $userimage_len] = fb_get_userimage($user_profile);

	$fields = fb_parse_profile_fields($user_profile);

	if (!$fields) {
		error_log('no fields');
		return false;
	}
	extract($fields);

	require_once '_password.inc';
	require_once '_visibility.inc';

	$_REQUEST['sID'] = 0;

	$visi = SELF;

	$_POST = [
		'NAME'					=> $name,
		'NICK'					=> $name,
		'COLOR'					=> 'white',
		'EMAIL'					=> $email,
		'BIRTH_YEAR'			=> $birth_year,
		'BIRTH_MONTH'			=> $birth_month,
		'BIRTH_DAY'				=> $birth_day,
		'VISIBILITY_AGE'		=> $visi,
		'VISIBILITY_BIRTHDAY'	=> $visi,
		'CITYID'				=> $cityid,
		'CITY'					=> $city_name,
		'VISIBILITY_CITY'		=> $visi,
		'COUNTRYID'				=> $countryid,
		'ADDRESS'				=> '',
		'ZIPCODE'				=> '',
		'REALNAME'				=> $realname,
		'PHONE'					=> '',
		'SEX'					=> $gender,
		'VISIBILITY_GENDER'		=> $visi,
		'RELATION'				=> '',
		'SEXPREF'				=> '',
		'VISIBILITY_SEXPREF'	=> $visi,
		'FIND_BY_EMAIL'			=> true,
		'FIND_BY_REALNAME'		=> true,
		'FORMSTAMP'				=> CURRENTSTAMP,
		'LAST_USERID'			=> db_single('user','SELECT MAX(USERID) FROM user'),
		'PRESENCE'				=> '',
		'SITE'					=> '',
		'OCCUPATION'			=> '',
		'FROTS'					=> '<a href=\'/\'></a>',
		'NEW_PASSWD'			=> ($new_password = create_password()),
		'NEW_PASSWD_TWO'		=> $new_password,
	];

	require_once 'user.inc';

	$_REQUEST['sELEMENT'] = 'user';

	if (!user_profile_commit(facebook: true)
	||	!($userid = $_REQUEST['sID'])
	) {
		error_log('fb_create_profile: user_profile_commit failed @ '.strip_uri_args($_SERVER['REQUEST_URI']));
		error_log_r($GLOBALS['__error']   ?? null, '__error');
		error_log_r($GLOBALS['__warning'] ?? null, '__warning');
		return false;
	}

	fb_store_userimage($userid, $userimage_tmp);

	db_insert('fbcreated_user','
	INSERT INTO fbcreated_user SET
		USERID		='.$userid.',
		IMAGECRC	='.$userimage_crc.',
		IMAGELEN	='.$userimage_len
	);

	if (!empty($user_profile['about'])) {
		require_once '_bio.inc';
		$oldPOST = $_POST;
		$_POST = [
			'ID'		=> $userid,
			'ELEMENT'	=> 'user',
			'LANGID'	=> CURRENTLANGID,
			'BODY'		=> $user_profile['about']
		];
		commit_bio(0);
		$_POST = $oldPOST;
	}
	create_fb_connection($_SESSION['fb_user_id'], $userid);
	fb_background($userid, ['parse_likes', 'parse_friends', 'sync_fields']);
	return $userid;
}

function delete_fb_connection($fb_user_id) {
	return create_fb_connection($fb_user_id,0);
}

function create_fb_connection($fb_user_id,$userid) {
	if (!$userid) {
		$flush_userid = db_single('facebook','SELECT USERID FROM facebook WHERE FACEBOOKID='.$fb_user_id);
		if ($flush_userid === false) {
			return false;
		}
	} else {
		$flush_userid = $userid;
	}
	$rc = db_insert('facebook_log','
		INSERT INTO facebook_log
		SELECT *,'.CURRENTSTAMP.'
		FROM facebook
		WHERE FACEBOOKID='.$fb_user_id.
		($userid ? ' AND USERID='.$userid : null)
	)
	&&	(	$userid
		?	db_replace('facebook','
			REPLACE INTO facebook SET
				USERID		='.$userid.',
				FACEBOOKID	='.$fb_user_id.',
				STAMP		='.CURRENTSTAMP)
		:	db_delete('facebook','
			DELETE FROM facebook
			WHERE FACEBOOKID='.$fb_user_id)
		);
	memcached_delete('havefb:'.$flush_userid);
	return $rc;
}
function fb_parse_likes($userid,$all = true) {
	if (!FACEBOOK_ENABLED) {
		return false;
	}
	if (!$userid) {
		$userid = CURRENTUSERID;
	}
	if (!fb_have_permission('user_likes',$userid)) {
		error_log('no user_likes permission');
		return false;
	}
	$start = time();
	require_once '_favourite.inc';
	$sync = db_single('fbsettings','SELECT SYNC_LIKES FROM fbsettings WHERE USERID='.$userid);
	if (db_errno()) {
	 	return false;
	}
	if ($sync !== null && !$sync) {
		$all_favs = null;
		$rem_favs = db_multirow_hash('favourite','SELECT ELEMENT,ID FROM favourite WHERE VIA_FB=1 AND USERID='.$userid);
		if ($rem_favs === false) {
			return false;
		}
	} else {
		[$access_token,$fb_user_id] = fb_get_active_token($userid);
		if (!$access_token) {
			error_log('WARNING no active fb token for user '.$userid);
			return null;
		}
		$all_favs = db_same_hash('favourite','
			SELECT VIA_FB,ELEMENT,ID
			FROM favourite
			WHERE USERID='.$userid
		);
		if ($all_favs === false) {
			return false;
		}
		$rem_favs = getifset($all_favs,1) ?: [];
		$add_favs = [];

		$fb = get_fb();

		$total = $connected = $removed = 0;
		$q = null;
		$response = null;
		try {
			fbgraph_access($fb_user_id,'/me','user_likes');

			$response = $fb->get(
				$q = '/me/likes?limit=1000',
				$access_token
			);

			for (	$page = $response->getGraphEdge();
				$page;
				$page = $fb->next($page)
			) {
				foreach ($page as $like) {
					$likesetlist[] = '('.$userid.','.$like['id'].','.CURRENTSTAMP.')';

					++$total;
					$items = db_same_hash('fbid','
						SELECT ELEMENT,ID
						FROM fbid
						WHERE ELEMENT IN ("artist","location","organization")
						  AND FBID='.$like['id']
					);
					if (!$items) {
						continue;
					}
					foreach ($items as $element => $ids) {
						foreach ($ids as $id) {
							if (!isset($all_favs[0][$element][$id])
							&&	!isset($all_favs[1][$element][$id])
							&&	!isset($all_favs[null][$element][$id])
							) {
								$add_favs[$element][$id] = $id;
							}
							unset($rem_favs[$element][$id]);
							if (empty($rem_favs[$element])) {
								unset($rem_favs[$element]);
							}
						}
					}
					$connected += count($items);
				}
				if (!$all) {
					break;
				}
			}
		} catch (Exception $e) {
			if (fb_maybe_retry($e,$userid,'parse_likes',$response)) {
				return true;
			}

			return fb_process_exception([
				'exception'	=> $e,
				'access_token'	=> $access_token,
				'userid'	=> $userid,
				'query'		=> $q,
				'response'	=> $response,
			]);
		}
	}
	$inserted = 0;

	if ($add_favs) {
		foreach ($add_favs as $element => $ids) {
			$affected = mark_favourite($element,$ids,$userid,true);
			$inserted += $affected;
		}
	}
	if ($all) {
		# only remove when we get all likes!
		# because if we don't we remove lots of likes we didn't get ;)
		if ($rem_favs) {
			foreach ($rem_favs as $element => $ids) {
				$affected = unmark_favourite($element,$ids,$userid,true);
				$removed += $affected;
			}
		}
		if (!db_insert('fblikeids_log','
			INSERT INTO fblikeids_log
			SELECT * FROM fblikeids
			WHERE USERID='.$userid)
		) {
			return false;
		}
		# split deletion to not hold up the table too long:
		while (	db_delete('fblikeids','
			DELETE FROM fblikeids
			WHERE USERID='.$userid.'
			LIMIT 10')
		&&	db_affected()
		) {
			# keep deleting until no rows left
		}
	}
	if (!empty($likesetlist)) {
		db_insert('fblikeids','
		INSERT IGNORE INTO fblikeids (USERID,FBID,STAMP)
		VALUES '.implode(',',$likesetlist)
		);
	}

	$stop = time();
	$diff = $stop - $start;
	fb_action_log(0,'likes','parsed:'.$total.',connected:'.$connected.',inserted:'.$inserted.',removed:'.$removed.'.time:'.$diff.'s',$userid);
	if ($rem_favs || $add_favs) {
		require_once '_pagechanged.inc';
		page_changed('user',$userid);
	}
	if (!empty($_REQUEST['sELEMENT'])
	&&	$_REQUEST['sELEMENT'] == 'test'
	) {
		?><div class="tt block">likes on Facebook: <?= $total ?></div><?
		?><div class="tt block">likes found on Partyflock: <?= $connected ?></div><?
		?><div class="tt block">inserted on Partyflock: <?= $inserted ?></div><?
		?><div class="tt block">removed from Partyflock: <?= $removed ?></div><?
	}
	return true;
}
function fb_parse_friends($userid) {
	if (!FACEBOOK_ENABLED) {
		return null;
	}
	if (!$userid) {
		$userid = CURRENTUSERID;
	}
	if (!fb_have_permission('user_friends',$userid)) {
		error_log('no user_friends permission');
		return false;
	}
	require_once '_buddy.inc';
	$start = time();
	$total = $connected = $removed = 0;
	$sync = db_single('fbsettings','SELECT SYNC_FRIENDS FROM fbsettings WHERE USERID='.$userid);
	if (db_errno()) {
		return false;
	}
	if ($sync !== null && !$sync) {
		$add_buddies = null;
		$rem_buddies = db_same_hash('buddy','
			SELECT USERID_INI
			FROM buddy
			WHERE VIA_FB=1
			  AND USERID_ACC='.$userid.'
			UNION
			SELECT USERID_ACC
			FROM buddy
			WHERE VIA_FB=1
			  AND USERID_INI='.$userid
		);
		$total_count = 0;
	} else {
		[$access_token,$fb_user_id] = fb_get_active_token($userid);
		if (!$access_token) {
			error_log('WARNING no active fb token for user '.$userid);
			return null;
		}

		$all_buddies = db_same_hash('buddy','
			SELECT VIA_FB,USERID_INI
			FROM buddy
			WHERE USERID_ACC='.$userid.'
			UNION
			SELECT VIA_FB,USERID_ACC
			FROM buddy
			WHERE USERID_INI='.$userid
		);
		if ($all_buddies === false) {
			return false;
		}

		$rem_buddies = getifset($all_buddies,1) ?: [];

		$fb = get_fb();

		$add_buddies = [];
		$q = null;
		$response = null;
		try {
			fbgraph_access($fb_user_id,'/me','user_friends');

			$response = $fb->get(
				$q = '/me/friends',
				$access_token
			);

			$total_count = null;

			for (	$page = $response->getGraphEdge();
				$page;
				$page = $fb->next($page)
			) {
				if ($total_count === null) {
					$total_count = $page->getTotalCount();
				}
				foreach ($page as $friend) {
					++$total;
					$items = db_simpler_array('fbid','
						SELECT USERID
						FROM facebook
						WHERE FACEBOOKID='.$friend['id']
					);
					if (!$items) {
						continue;
					}
					foreach ($items as $tmpuserid) {
						if (!isset($all_buddies[0][$tmpuserid])
						&&	!isset($all_buddies[1][$tmpuserid])
						) {
							$add_buddies[$tmpuserid] = $tmpuserid;
						}
						unset($rem_buddies[$tmpuserid]);
					}
					$connected += count($items);
				}
			}
		} catch (Exception $e) {
			if (fb_maybe_retry($e,$userid,'parse_friends',$response)) {
				return true;
			}
			return fb_process_exception([
				'exception'	=> $e,
				'access_token'	=> $access_token,
				'userid'	=> $userid,
				'query'		=> $q,
				'response'	=> $response,
			]);
		}
	}
	$inserted = $removed = 0;
	if ($add_buddies) {
#		print_rr($add_buddies,'add_buddies');
		$inserted = create_buddies($userid,$add_buddies) ?: 0;
	}
	if ($rem_buddies) {
#		print_rr($rem_buddies,'rem_buddies');
		$removed = destroy_buddies($userid,$rem_buddies) ?: 0;
	}

	db_replace('fbbuddycounter','
	REPLACE INTO fbbuddycounter SET
		USERID	='.$userid.',
		MSTAMP	='.CURRENTSTAMP.',
		CNT	='.($total_count ?: 0)
	);
	$stop = time();
	$diff = $stop - $start;
	fb_action_log(0,'friends','parsed:'.$total.',connected:'.$connected.',inserted:'.$inserted.',removed:'.$removed.',time:'.$diff.'s',$userid);
	if ($rem_buddies || $add_buddies) {
		require_once '_pagechanged.inc';
		page_changed('user',$userid);
	}
	if (!empty($_REQUEST['sELEMENT'])
	&&	$_REQUEST['sELEMENT'] == 'test'
	) {
		?><div class="tt block">friends on Facebook: <?= $total_count ?></div><?
		?><div class="tt block">friends on Facebook also Partyflock: <?= $total ?></div><?
		?><div class="tt block">friend profiles on Partyflock: <?= $connected ?></div><?
		?><div class="tt block">inserted on Partyflock: <?= $inserted ?></div><?
		?><div class="tt block">removed from Partyflock: <?= $removed ?></div><?
	}
	return true;
}
function fb_created_info($userid) {
	return db_single_assoc('fbcreated_user','
		SELECT	(SELECT 1 FROM user_log WHERE USERID='.$userid.' AND MUSERID!=0 LIMIT 1) OR (SELECT 1 FROM user WHERE USERID='.$userid.' AND MUSERID!=0) AS CHANGED_PROFILE,
			(SELECT 1 FROM userimage WHERE USERID='.$userid.' AND MUSERID!=0 LIMIT 1) AS CHANGED_IMAGE,
			IMAGECRC,
			IMAGELEN
		FROM fbcreated_user
		WHERE USERID='.$userid
	);
}
function fb_get_pages($userid) {
	if (!FACEBOOK_ENABLED) {
		return false;
	}
	if (!$userid) {
		$userid = CURRENTUSERID;
	}

	[$access_token,$fb_user_id] = fb_get_active_token($userid);
	if (!$access_token) {
		error_log('WARNING no active fb token for user '.$userid);
		return null;
	}

	$fb = get_fb();

	$pages = [];
	$response = null;

	try {
		fbgraph_access($fb_user_id,'/me','user_accounts');

		$response = $fb->get(
			$q = '/me/accounts',
			$access_token
		);

		for (	$node = $response->getGraphEdge();
			$node;
			$node = $fb->next($node)
		) {
			foreach ($node as $page) {
				if (isset($page['tasks'])) {
					$tasks = $page['tasks']->asArray();
				} else {
					$tasks = [];
				}
				$pages[$page['id']] = '('.$page['id'].','.$fb_user_id.','.CURRENTSTAMP.','.($tasks ? '"'.implode(',',$tasks).'"' : '""').')';
			}
		}
	} catch (Exception $e) {
		if (fb_maybe_retry($e,$userid,'get_pages',$response)) {
			return true;
		}
		return fb_process_exception([
			'exception'	=> $e,
			'access_token'	=> $access_token,
			'userid'	=> $userid,
			'query'		=> $q,
			'response'	=> $response,
		]);
	}

	$wherep = $pages ? ' AND PAGEID NOT IN ('.implodekeys(',',$pages).')' : null;

	if (!db_insert('facebook_pages_log','
		INSERT INTO facebook_pages_log
		SELECT *,'.CURRENTSTAMP.'
		FROM facebook_pages
		WHERE FACEBOOKID='.$fb_user_id.
		$wherep)
	||	!db_delete('facebook_pages','
		DELETE FROM facebook_pages
		WHERE FACEBOOKID='.$fb_user_id.
		$wherep)
	) {
		return false;
	}
	if ($pages) {
		if (!db_replace('facebook_pages','
			REPLACE INTO facebook_pages (PAGEID,FACEBOOKID,STAMP,PERMS)
			VALUES '.implode(',',$pages))
		) {
			return false;
		}
	}
	return true;
}
function fb_sync_fields($user_profile = null, ?int $userid = null): bool {
	if (!FACEBOOK_ENABLED) {
		return false;
	}
	if (!$userid) {
		$userid = CURRENTUSERID;
	}
	$fb_created = fb_created_info($userid);
	if (!$fb_created) {
		return $fb_created === false ? false : true;
	}
	if ($fb_created['CHANGED_PROFILE']
	&&	$fb_created['CHANGED_IMAGE']
	) {
		error_log('profile and image changed, skipping');
		return true;
	}

	$start = time();

	if (!$fb_created['CHANGED_PROFILE']) {
		if (!($fields = fb_parse_profile_fields($user_profile))) {
			error_log('no_fields');
			return false;
		}
		extract($fields);
	}

	$stop = time();
	$diff = $stop - $start;
	$updated = [];

	if (!$fb_created['CHANGED_PROFILE']) {
		$user = db_single_assoc('user','
			SELECT	BIRTH_DAY,BIRTH_MONTH,BIRTH_YEAR,
				CITY,CITYID,COUNTRYID,
				EMAIL,
				NICK,NAME,REALNAME,
				SITE,
				SEX
			FROM user
			WHERE USERID='.$userid
		);
		if (!$user) {
			return false;
		}

		if ($user['BIRTH_DAY']   !== $birth_day
		||	$user['BIRTH_MONTH'] !== $birth_month
		||	$user['BIRTH_YEAR']  !== $birth_year
		) {
			$setlist[] = 'BIRTH_YEAR = '.$birth_year.', BIRTH_MONTH = '.$birth_month.', BIRTH_DAY = '.$birth_day;
			$updated[] = 'birthdate';
		}
		if ($user['REALNAME'] != $realname) {
			$setlist[]   = 'NAME = "'.($realname = addslashes($realname)).'",REALNAME = "'.$realname.'", NICK = "'.$realname.'"';
			$uasetlist[] = 'NICK = "'.$ralname.'"';
			$updated[]   = 'name';
		}
		if ($user['SEX'] !== $gender) {
			$setlist[] = 'SEX = "'.$gender.'"';
			$updated[] = 'gender';
		}
		if ($user['CITYID'] !== $cityid) {
			$setlist[] = 'CITYID = '.$cityid;
			$updated[] = 'cityid';
		}
		if ($user['COUNTRYID'] !== $countryid) {
			$setlist[] = 'COUNTRYID = '.$countryid;
			$updated[] = 'countryid';
		}
		if ($cityid
		?	$user['CITY']
		:	$user['CITY'] !== $city_name
		) {
			$setlist[]  = 'CITY = '.($cityid ? 'NULL' : '"'.addslashes($city_name).'"');
			$updateed[] = 'city';
		}
		if (!empty($setlist)) {
			if (!db_insert('user_log', '
				INSERT INTO user_log
				SELECT * FROM user
				WHERE USERID = '.$userid)
			||	!db_update('user', '
				UPDATE user SET
					MSTAMP	= '.CURRENTSTAMP.',
					MUSERID	= 0,
					'.implode(', ', $setlist).'
				WHERE USERID = '.$userid)
			) {
				return false;
			}
			if (!empty($uasetlist)) {
				require_once '_triplets.inc';
				if (!db_update('user_account', '
					UPDATE user_account SET
						'.implode(', ', $uasetlist).'
					WHERE USERID = '.$userid)
				) {
					return false;
				}
				delete_old_triplets($userid,$name);
			}
			require_once '_elementchanged.inc';
			element_changed('user', $userid, 0);
			flush_user($userid);
		}
	}
	if (!$fb_created['CHANGED_IMAGE']) {
		[$userimage_tmp, $userimage_crc, $userimage_len] = fb_get_userimage($user_profile, $data);

		if ($userimage_crc !== $fb_created['IMAGECRC']
		||	$userimage_len !== $fb_created['IMAGELEN']
		) {
			if (fb_store_userimage($userid, $userimage_tmp)) {
				db_update('fbcreated_user', '
				UPDATE fbcreated_user SET
					IMAGECRC	= '.$userimage_crc.',
					IMAGELEN	= '.$userimage_len.'
				WHERE USERID ='.$userid
				);
 				$updated[] = 'userimage';
			} else {
				error_log('fb_store_userimage failed, data: '.$data);
			}
		}
	}
	fb_action_log(0, 'fields', ($updated ? 'updated:'.implode(',', $updated).';' : '').'time:'.$diff.'s', $userid);
	return true;
}

function fb_show_profile_opts(int $userid = 0): void {
	?><div class="hidden abs z1"><?
	?><table class="fw center nomargin" style="height: 100px;"><tr><td class="middle"><?
	if ($userid) {
		?><a<?
		?> target="_blank"<?
		?> onclick="event.stopPropagation(); return true;"<?
		?> href="<?= get_element_href('user',$userid) ?>"><?=
			__('action:view_profile') ?></a><?
		?><br />&nbsp;<br /><?
	}
	?><a class="ib" onclick="Pf.busy(true, true); return true;" href="/connectcreate.fb?USERID=<?= $userid ?>"><?
	if (!$userid) {
		echo element_name('new_profile');
		?><br /><?
		?><br /><?
	}
	echo __('action:confirm');
	?></a><?
	?></td></tr></table><?
	?></div><?
}

function fb_display_connect_overview() {
	if (!FACEBOOK_ENABLED) {
		return false;
	}
	layout_show_section_header(__C('action:login_with_facebook'));

	session_start();

	require_once '_fbsession.inc';

	if (empty($_SESSION['fb_user_profile'])) {
		$redir = 'https://'.$_SERVER['HTTP_HOST'].(have_user() ? '/user/connectfb' : '/user/login');
		if (!ROBOT) {
			error_log('no fb_user_profile in _SESSION for fb_display_connect_overview');
		}
		header('Location: '.$redir);
		return false;
	}

	$fb_user_profile = $_SESSION['fb_user_profile'];

	$email = $fb_user_profile->getEmail();

	$users = $email ? db_rowuse_hash(['user', 'user_account', 'banpoint'],'
		SELECT	user.USERID, user.NICK, STATUS,
			GROUP_CONCAT(DATAID) AS DATAIDS,
			GROUP_CONCAT(LARGE)  AS LARGES,
			GROUP_CONCAT(WIDTH)  AS WIDTHS,
			GROUP_CONCAT(HEIGHT) AS HEIGHTS,
			GROUP_CONCAT(TYPE)   AS TYPES
		FROM user
		JOIN user_account USING (USERID)
		LEFT JOIN userimage ON userimage.USERID = user.USERID AND LARGE IN (0, 1, 2, 3)
		LEFT JOIN userimagedatameta USING (DATAID)
		WHERE '.fb_where_connectable_users($email).'
		GROUP BY user.USERID
		ORDER BY STATUS = "active" DESC,
			 STATUS = "inactive" DESC,
			 STATUS = "unactive" DESC'
	) : [];

	if ($users === false) {
		return false;
	}

	include_busy_img();

	require_once '_urltitle.inc';
	require_once '_browser.inc';
	require_once '_currentuser.inc';
	require_once '_layout.inc';
	require_once '_smiley.inc';

	include_js('js/fbchoose');

	require_once '_star.inc';

	ob_start();
	?><div class="fbchoice globox hlbox" onclick="Pf.FBchoose(this,0)"><?

	fb_show_profile_opts(0);

	?><div class="imgb ptr"><?

		?><div class="noverflow relative" style="width:100%;height:100px"><?

		if (!$users) {
			?><div class="abs" style="top:0;right:0"><?= get_star('yellow') ?></div><?
		}

		if (($picture = $fb_user_profile->getPicture())
		&&	($img_url = $picture->getUrl())
		) {
			?><table class="fw nomargin" style="height:100%"><tr><td class="middle"><?
			?><img style="width:100px" src="<?= $img_url ?>" /><?
			?></td></tr></table><?
		} else {
			?>+<?
		}
		?></div><?
		?><hr class="slim"><?
		?><div<?
		if (!$users) {
			?> class="notice ns"<?
		}
		?>><?= escape_utf8($fb_user_profile->getName()) ?></div><?
		?><div><?= __('action:create_new_profile') ?></div><?

		?></div><?
	?></div><?
	$new_profile = ob_get_clean();

	$blox = [];

	if ($users) {
		$last_useds = db_simple_hash('user_data','SELECT USERID,LAST_USED FROM user_data WHERE USERID IN ('.implodekeys(',',$users).')');
		foreach ($users as $userid => &$user) {
			$user['LAST_USED'] = getifset($last_useds,$userid) ?: 0;
			foreach (['WIDTHS','HEIGHTS','LARGES','DATAIDS','TYPES'] as $key) {
				$user[$key] = $user[$key] ? explode(',',$user[$key]) : null;
			}
		}
		unset($user);

		$users[0] = [
			'STATUS'	=> 'new',
			'NICK'		=> null,
			'LAST_USED'	=> 0,
		];

		static $__statusorder = [
			'active'	=> 1,
			'new'		=> 2,
			'inactive'	=> 3,
			'unactive'	=> 4,
			'banned'	=> 5,
		];
		uasort($users,function($a,$b) use ($__statusorder) {
			$astat = $__statusorder[$a['STATUS']];
			$bstat = $__statusorder[$b['STATUS']];
			if ($astat != $bstat) {
				return $astat - $bstat;
			}
			$ldiff = $b['LAST_USED'] - $a['LAST_USED'];
			if ($ldiff) {
				return $ldiff;
			}
			return strcasecmp($a['NICK'],$b['NICK']);
		});

		$status = null;
		$first = true;
		foreach ($users as $userid => $user) {
			if ($user['STATUS'] == 'new') {
				$blox[] = $new_profile;
				continue;
			}

			ob_start();
			extract($user);
			?><div<?
			?> class="fbchoice globox hlbox"<?
			?> onclick="Pf.FBchoose(this,<?= $userid ?>)"><?

			fb_show_profile_opts($userid);

			?><div class="imgb ptr"><?
			?><div class="noverflow centered" style="width:100px;height:100px"><?

			if ($first) {
				?><div class="abs" style="top:0;right:0"><?= get_star('yellow') ?></div><?
			}


			if ($LARGES) {
				asort($LARGES);
				foreach ($LARGES as $ndx => $large) {
					if ($ndx < 2
					&&	is_high_res()
					&&	isset($LARGES[$ndx+2])
					) {
						$ndx += 2;
					}
					break;

				}
				$width = $WIDTHS[$ndx];
				$height = $HEIGHTS[$ndx];
				scale($width,$height,100,100,false);
				$dataid = $DATAIDS[$ndx];
				$type = $TYPES[$ndx];

				?><img<?
					?> style="width:<?= $width ?>px<?
					if ($height < 100) {
						?>;margin-top:<?= (100 - $height) / 2 ?>px<?
					}
					?>"<?
					?> src="<?= IMAGES_HOST ?>/images/user/<?= $userid ?>_<?= $dataid ?>.<?= $type ?>"<?
				?>><?
			} else {
				?><img<?
				?> style="width:100%"<?
				?> src="<?= STATIC_HOST ?>/images/user_<?= CURRENTTHEME,is_high_res() ?>.png"><?
			}
			?></div><?

			?><hr class="slim"><?

			?><div<?
			if ($first) {
				?> class="notice ns"<?
			}
			?>><?
				?><a target="_blank" href="<?= get_element_href('user',$userid) ?>"><?
				echo _smiley_replace(_smiley_prepare($NICK));
				?></a><?
			?></div><?
			?><div class="small"><?
			if ($LAST_USED) {
				echo _date_display($LAST_USED);
				?><br /><?
			}
			echo __('status:'.$STATUS);
			?></div><?
			?></div><?

			?></div><?

			$blox[] = ob_get_clean();

			$first = false;
		}
	} else {
		$blox[] = $new_profile;
	}


	layout_open_box('white');

	?><div class="block"><?= __($users ? 'user:info:facebook_login_connect_info_TEXT' : 'user:info:facebook_login_no_existing_TEXT',DO_NL2BR | DO_UBB) ?></div><?

	if (SMALL_SCREEN) {
		?><div class="center"><?
	}

	if (!empty($blox)) {
		echo implode('',$blox);
	}
	if (SMALL_SCREEN) {
		?></div><?
	}

	?><div class="block"><?
	echo __('facebook:info:have_other_profile_LINE') ?><br /><?
	echo __('facebook:info:log_in_regular_LINE',DO_UBB);
	?></div><?

	layout_close_box();

	?><div class="block" style="margin-top:.8em"><?= __('facebook:info:what_we_do_TEXT',DO_NL2BR | DO_UBB) ?></div><?

}

function fb_show_login_part(): void {
	if (!FACEBOOK_ENABLED) {
		layout_open_box('white');
		layout_box_header(get_facebook_icon().' '.__C('action:login_with_facebook'));
		?><div class="block"><?
		echo __('facebook:info:login_disabled_plus_alternative_TEXT', DO_NL2BR | DO_UBB);
		?></div><?
		layout_close_box();
		return;
	}

	?><form method="get" onsubmit="Pf.busy(true);return true" action="/loginredir.fb"><?
	layout_open_box('white');
	layout_box_header(get_facebook_icon().' '.__C('action:login_with_facebook'));

	if (FACEBOOK_ENABLED) {
		if (have_facebooked()) {
			?><div class="block"><?
			?><div><label class="not-hilited"><input class="upLite" type="checkbox" name="FORCECHOICE" /> <?= __('action:choose_profile_to_connect') ?></label></div><?
			?></div><?
		}

		?><div class="block"><?
		?><input class="action-button fb-login-button" type="submit" value="<?= __C('action:continue_with_facebook') ?>" /><?
		?></div><?
	} else {
		?><div class="block"><?
		?>Inloggen met Facebook is voorlopig niet meer mogelijk.<br /><?
		?><a href="/user/lostpasswd">Je kunt een wachtwoord in te stellen door een wachtwoordwijzigingslink te laten sturen via het wachtwoord vergeten formulier met als email het e-mailadres dat je <?
		?>op Facebook gebruikt</a><?
		?></div><?
	}

	layout_close_box();

	?></form><?

	if (FACEBOOK_ENABLED) {
		?><div class="block"><?= __('facebook:info:login_with_pf_first_TEXT',DO_NL2BR | DO_UBB) ?></div><?
	}
}

function fb_process_exception(array $args) {
	# returns true for exception we never like
	# returns false for exception where user has stale token

	$access_token =
	$query =
	$silent =
	$exception = null;

	extract($args, \EXTR_OVERWRITE);

	if (!empty($response)
	&&	method_exists($response,'getHttpStatusCode')
	&&	($status = $response->getHttpStatusCode()) >= 500
	&&	$status < 600
	) {
		error_log('SERVER ERROR');
	}

	if (!method_exists($exception,'getCode')) {
		$code = $exception->code;
		$subcode = getifset($exception,'error_subcode');
		$msg = $exception->message;
	} else {
		$code = $exception->getCode();
		$subcode = method_exists($exception,'getSubErrorCode') ? $exception->getSubErrorCode() : 0;
		$msg = $exception->getMessage();
	}
	$uri = CLI ? null : ' @ '.strip_uri_args($_SERVER['REQUEST_URI']);

	$bad_error = false;

	if ($silent === false
	||	!isset($silent[$code])
	) {
		static $__silent = [
		#	code	subcode
			1	=> [
				99	=> true,	# An unknown error occurred
			],
			2	=> [
				-1	=> true,	# An unexpected error has occurred. Please retry your request later.
			],
			28	=> [
				0	=> true,	# Operation timed out after x milliseconds with x out of x bytes received
			],
			190	=> [
				458	=> true,	# Error validating access token: The user has not authorized application
				460	=> true,	# Error validating access token: The session has been invalidated because the user has changed the password.
				463	=> true,	# Error validating access token: Session has expired on ...
				464	=> true,	# Error validating access token: Sessions for the user  are not allowed because the user is not a confirmed user.
				467	=> true,	# Error validating access token: This may be because the user logged out or may be due to a system error.
				490	=> true,	# Error validating access token: The user is enrolled in a blocking, logged-in checkpoint
			],
			200	=> [
				2069030	=> true,	# This endpoint is not supported in the new Pages experience.
			],
		];
		if (CLI
		&&	defined('DEBUG')
		&&	DEBUG
		||	$silent === false
		||	defined('DEBUG_FACEBOOK')
		&&	DEBUG_FACEBOOK
		||	!isset($__silent[$code][$subcode])
		&&	!(	$code === 100
			&&	$subcode === 33
			&&	(	str_contains($query, '/attending')
				||	str_contains($query, '/maybe')
				||	str_contains($query, '/declined')
				)
			# 	^^^ unsupported get/post request for 'attending' is now expected due to facebook limitations:
			#		https://newsroom.fb.com/news/2018/04/restricting-data-access/
			)
		) {
			error_log(
				'fb_process_exception (user '.$userid.', code '.$code.($subcode ? ', subcode '.$subcode : null).'): '.$msg.$uri
			);
			if ($query) {
				error_log('^^ failed query: '.$query);
				error_log('^^ using access: '.$access_token);
			}
			if (method_exists($exception,'getResponseData')) {
				error_log_r($exception->getResponseData());
			}
			if (!empty($response)) {
				ob_start();
				print_rr($response);
				error_log(ob_get_clean());
				if (method_exists($response,'getBody')) {
					error_log('body: '.$response->getBody());
				}
			}
			$bad_error = true;
		}
	}

	db_insert('facebook_exception','
	INSERT INTO facebook_exception SET
		USERID		='.($userid ?: 0).',
		STAMP		='.CURRENTSTAMP.',
		CODE		='.$code.',
		SUBCODE		='.$subcode.',
		QSTR		="'.addslashes($query).'"'
	);

	# See https://developers.facebook.com/docs/graph-api/using-graph-api

	if ($code == 10
	||	$code >= 200
	&&	$code <= 299
	) {
		error_log('WARNING facebook exception permission problem');
		$bad_error = true;
	}
	if (token_need_renew($code,$subcode)) {
		if (!$access_token) {
			error_log('WARNING cannot expire token, access_token missing');
			$bad_error = true;
		} else {
			fb_expire_token($access_token);
		}
	}
	return !$bad_error;
}
function fb_where_connectable_users(string $email): string {
	return '
		EMAIL = "'.addslashes($email).'"
	  AND (		STATUS IN ("active", "inactive", "unactive")
	  	OR	STATUS = "banned"
	  	AND	(SELECT MAX(EXPIRES) FROM banpoint WHERE banpoint.USERID = user.USERID) < '.CURRENTSTAMP.'
	)';
}

function show_fblogin_register(): void {
	if (!FACEBOOK_ENABLED) {
		return;
	}
	$url = '/loginredir.fb?FORCECHOICE=new';
	layout_open_box('white');
	layout_box_header(get_facebook_icon().' <a href="'.$url.'">'.__C('action:login_with_facebook').'</a>');

	?><div class="block"><?
	?><a href="<?= $url ?>"><?
	?><button class="action-button fb-login-button"><?= __C('action:continue_with_facebook') ?></button><?

	?></a></div><?
	layout_close_box();
}

function show_fb_menu() {
	if (!FACEBOOK_ENABLED
	||	!have_user()
	) {
		return;
	}
	return	show_renew_fb_menu()
	||	show_connect_fb_menu();
}
function clear_fb_ignored() {
	memcached_delete('fbignored:'.CURRENTUSERID);
}
function show_connect_fb_menu() {
	$fb_user_id = fb_have_connection();

	if ($fb_user_id) {
		return;
	}

	if (memcached_single('facebook_ignored','SELECT 1 FROM facebook_ignored WHERE USERID='.CURRENTUSERID,ONE_DAY,'fbignored:'.CURRENTUSERID)) {
		return;
	}

	?><div id="connectfbmenu"><?
	echo get_facebook_icon();
	?> <a<?
	if ($_REQUEST['sELEMENT'] === 'user'
	&&	$_REQUEST['ACTION']   === 'connectfb'
	) {
		?> class="selected"<?
	}
	?> href="/user/connectfb"><?= __('action:connect') ?></a><?
	?> <span<?
	?> onclick="do_inline('POST','/ignore.fb','ACT',function(req){<?
		?>if(req.status==200){<?
			?>Pf.removeNode('connectfbmenu');<?
		?>}else{<?
			?>showerrors(req,ERR_TO_ALERT);<?
		?>}})"<?
	?> class="light8 bold ptr"<?
	?> style="font-size: 150%; line-height: <?= 100/1.5 ?>%; vertical-align: -12.5%;"><?= MULTIPLICATION_SIGN_ENTITY ?></span><?
	?></div><?
	return true;
}
function fb_flush_renew() {
	memcached_delete('fbtryrenew:'.CURRENTUSERID);
	memcached_delete('fbtriedrenew:'.CURRENTUSERID);
}
function show_renew_fb_menu() {
	# force showing of fresh option, for testing purposes
	$force_refresh = false;

	if (!fb_have_connection()) {
		return false;
	}

	$statekey = 'fbtryrenew:'.CURRENTUSERID;
	$donekey = 'fbtriedrenew:'.CURRENTUSERID;

	# main status makes sure only once per day we check whether we want to refresh token
	$status = $force_refresh || isset($_REQUEST['NOMEMCACHE']) ? false : memcached_get($statekey);
	if ($status) {
		return false;
	}
	$donetry = $force_refresh || isset($_REQUEST['NOMEMCACHE']) ? false : memcached_get($donekey);
	if (!$donetry) {
		# fb_background of parsing: once per 'try', which is once per day max

		memcached_set($statekey, 'hadnostatus', ONE_MINUTE);
		$tokeninfo = fb_need_refresh();
		if (!$tokeninfo) {
			if ($tokeninfo === null) {
				fb_background(CURRENTUSERID,['parse_likes','get_pages','parse_friends','sync_fields']);
			}
			memcached_set($statekey,'noneed',ONE_DAY);
			return false;
		}

		memcached_set($statekey, 'trying', 5);
		memcached_set($donekey, igbinary_serialize($tokeninfo), ONE_DAY);

		[$access_token, $fb_user_id, $expires, $mstamp] = $tokeninfo;

		global $currentuser;

		if (!$force_refresh
		&&	$expires > CURRENTSTAMP
		&&	!$currentuser->is_impersonated()
		) {
/*			ob_start(); ?>
			Pf.FBAPI(function(){
				FB.getLoginStatus(function(response){
					if (response.status == 'connected') {
						do_inline(	'POST',
								'/refresh.fb?automatic',
								'ACT',
								null,
								'USERID=<?= CURRENTUSERID ?>\u0026DATA='+Pf.encodeForPost(response.authResponse.accessToken)
						);
					}
				});
			}); <?
			$jsdata = ob_get_clean();

			echo '<script>', minify_js($jsdata), '</script>';
*/
			fb_background(CURRENTUSERID, ['parse_likes', 'parse_friends', 'sync_fields']);

			return true;
		}
	} else {
		[, $fb_user_id, $expires, $mstamp] = igbinary_unserialize($donetry);
	}

	if (!$force_refresh
	&&	CURRENTSTAMP < $expires - 2 * ONE_WEEK
	) {
		# not yet showing menu
		return false;
	}

	$action = 'renew';
	?><div><?
	echo get_facebook_icon('redhue');
	?> <a class="warning-nb<?
	if ($_REQUEST['sELEMENT'] === 'user'
	&&	$_REQUEST['ACTION']   === $action.'fb'
	) {
		?> selected<?
	}
	?>" href="/user/<?= $action ?>fb"><?= __($action == 'renew' ? 'status:expired' : 'action:'.$action) ?>!</a><?
	?></div><?

	fb_try_overlay('expired', $mstamp);

	return true;
}
function fb_try_overlay(string $type, int $val = 0): void {
	static $__done = null;
	if ($__done === 'expired') {
		return;
	}
	$__done = $type;

	if (!memcached_add($overlaykey = 'fb:'.$type.':overlay:'.CURRENTUSERID, true, ONE_HOUR)) {
		# already done
		return;
	}
	switch ($type) {
	case 'updated':
		$field = 'V';
		break;
	case 'expired':
		$field = 'MSTAMP';
		break;
	}
	$done = db_single('fboverlay','SELECT 1 FROM fboverlay WHERE USERID='.CURRENTUSERID.' AND '.$field.'='.$val);
	if ($done === false || $done) {
		return;
	}
	if (!db_insert('fboverlay','
		INSERT INTO fboverlay SET
			USERID		='.CURRENTUSERID.',
			'.$field.'	='.$val.',
			SHOWSTAMP	='.CURRENTSTAMP)
	) {
		return;
	}
	$GLOBALS['__pffboverlay'] = $type;
}
function show_pf_fb_overlay() {
	# if !show overlay
	#	return;

	$action = $GLOBALS['__pffboverlay'] == 'expired' ? 'renew' : 'update';

	?><form<?
		?> method="get"<?
		?> action="/<?= $action ?>redir.fb"<?
		?> onsubmit="Pf.busy(true,true);return true"<?
	?> id="pffboverlay"<?
	?> class="bgov"<?
	?> style="<?
		?>width:100%;<?
		?>height:100%;<?
	?>"<?
	?>><?
	echo get_close_char([
		'class'		=> 'large rtop',
		'style'		=> 'top:1em;right:1em',
		'onclick'	=> "Pf.removeNode('pffboverlay')",
	]);


	?><table class="fw" style="height:100%"><tr><td class="center middle"><?

	?><div><?
	?><img style="width:25%;max-width:300px" src="<?= STATIC_HOST ?>/images/facebook_<?= is_high_res() ? 600 : 300 ?>px.png" /><?
	?> <?
	?><img style="width:25%;max-width:300px;border:1px solid rgba(255,255,255,.3);border-radius:2px" src="<?= STATIC_HOST ?>/images/partyflock_<?= is_high_res() ? 600 : 300 ?>px.png" /><?
	?></div><?

	?><div style="margin:1em 0"><?
	echo __('facebook:info:renew_overlay_'.$GLOBALS['__pffboverlay'].'_TEXT',DO_NL2BR);
	?></div><?


	?><div><?
	?><input type="submit" class="notice" value="<?= __('action:'.$action.'_facebook_connection') ?>" /><?
	if (!SMALL_SCREEN) {
		?><div class="ib" style="width:3em">&nbsp;</div><?
		?><button onclick="Pf.removeNode('pffboverlay');return false"><?= __('action:no_ignore') ?></button><?
	}
	?></div><?

	?></td></tr></table><?

	?></form><?
}
function fb_renew_redirect() {
	[,$fb_user_id] = fb_get_token(CURRENTUSERID);
	if ($fb_user_id) {
		return;
	}
	error_log(
		'WARNING redirect renew to connect for user '.CURRENTUSERID.
		(empty($_SERVER['HTTP_REFERER']) ? null : ', got here from '.$_SERVER['HTTP_REFERER'])
	);
	header('Location: https://'.$_SERVER['HTTP_HOST'].'/user/connectfb');
	exit;
}
function fb_display_renew_new_profile() {
	[$metadata,$long_lived_token,$stored_fb_user_id] = $_SESSION['fb_renewfail'];
	unset($_SESSION['fb_renewfail']);

	if (!require_user()) {
		return;
	}

	$fb = get_fb();

	$fb_user_id = $metadata->getUserId();

	$fb_user_profile = fb_get_user_profile($fb,$long_lived_token,$fb_user_id);

	?><form method="get" onsubmit="Pf.busy(true,true);return true" action="/connectredir.fb"><?

	layout_open_box('white');
	layout_box_header(get_facebook_icon().' Wil je je Partyflock profiel ook aan dit Facebook account koppelen?');

	include_js('js/fbchoose');

	?><div class="fbchoice globox hlbox" onmouseover="console.log('hi');unhide(this.firstChild)" onmouseout="hide(this.firstChild)" onclick="Pf.busy(true,true);Pf.getParentNode(this,'FORM').submit()"><?

	?><div class="hidden abs z1" style="width:200px"><?
	?><table class="ptr fw center nomargin" style="height:100px;width:100%"><tr><td class="middle"><?
	echo __('action:confirm');
	?></td></tr></table><?
	?></div><?


	?><div class="imgb ptr"><?

		?><div class="noverflow relative" style="width:100%;height:100px"><?

			if (($picture = $fb_user_profile->getPicture())
			&&	($img_url = $picture->getUrl())
			) {
				?><table class="fw nomargin" style="height:100%"><tr><td class="middle"><?
				?><img style="width:100px" src="<?= $img_url ?>" /><?
				?></td></tr></table><?
			} else {
				?>+<?
			}
		?></div><?
		?><hr class="slim"><?

		?><div class="notice ns"><?= escape_utf8($fb_user_profile->getName()) ?></div><?

	?></div><?

	?></div><?

	layout_close_box();

	?><div class="block"><?
	?><input type="submit" value="<?= __('action:connect_with_facebook') ?>" /><?
	?></div><?

	?></form> <?
}
function fb_display_connect_question() {
	switch ($_REQUEST['ACTION']) {
	case 'updatefb':
		$section_header = 'action:update_connection';
		$header = 'action:update_facebook_connection';
		$action = 'update';
		$button = 'action:update_connection';
		break;
	case 'connectfb':
		$section_header = 'action:connect';
		$action = 'connect';
		$header = $button = 'action:connect_with_facebook';
		break;
	case 'renewfb':
		session_start();

		if (!empty($_SESSION['fb_renewfail'])) {
			return fb_display_renew_new_profile();
		}

		$section_header = 'action:renew';
		$header = $button = 'action:renew_facebook_connection';
		$action = 'renew';
		break;
	case 'reauthfb':
		$section_header = 'action:reauth';
		$header = $button = 'action:reauth_facebook_connection';
		$action = 'reauth';
		break;
	}

#	error_log('fb_display_connect_question '.$action.' for user '.CURRENTUSERID);

	$update = str_contains($_REQUEST['ACTION'],'update');

	layout_show_section_header(__C($section_header));

	if (!have_user()) {
		return;
	}

	layout_open_box('white');
	layout_box_header(get_facebook_icon().' '.__C($header));
	?><div class="block"><?= __('facebook:info:'.$action.'_existing_TEXT',DO_NL2BR | DO_UBB) ?></div><?
	?><div class="hr"></div><?
	?><div class="block"><?= __('facebook:info:what_we_do_TEXT',DO_NL2BR | DO_UBB) ?></div><?
	layout_close_box();
	?><form class="l ib" method="get" onsubmit="Pf.busy(true,true);return true" action="/<?= $action ?>redir.fb"><?
	?><input type="submit" value="<?= __($button) ?>" /><?
	?></form> <?
}
function fb_display_connected($connect = true) {
	layout_show_section_header(Eelement_name('connection'));

	if ($connect) {
		if (fb_have_connection()) {
			register_notice(!str_contains($_REQUEST['ACTION'],'renew') ? 'facebook:notice:connected_LINE' : 'facebook:notice:renewed_LINE');
		} else {
			register_warning('facebook:notice:not_connected_LINE');
		}
	}
	fb_flush_renew();
}
function fb_action_log($fbid,$action,$data = null,$userid = null) {
	if ($userid === null) {
		$userid = CURRENTUSERID;
	}
	if (!$fbid) {
		$fbid = db_single('facebook','SELECT FACEBOOKID FROM facebook WHERE USERID='.$userid) ?: 0;
	}

	return db_insert('facebook_action_log','
	INSERT INTO facebook_action_log SET
		ACTION		="'.$action.'",
		DATA		="'.($data ? addslashes($data) : '').'",
		FBID		='.$fbid.',
		STAMP		='.time().',
		USERID		='.$userid.',
		BG		='.(php_sapi_name() == 'cli' ? 1 : 0)
	);
}
function fb_parse_signed_request($signed_request,$secret) {
	if (!str_contains($signed_request,'.')) {
		error_log('fp_parse_signed_request: invalid signed request @ '.strip_uri_args($_SERVER['REQUEST_URI']));
		return false;
	}
	[$encoded_sig, $payload] = explode('.', $signed_request, 2);

	$sig = fb_base64_url_decode($encoded_sig);
	$data = safe_json_decode(fb_base64_url_decode($payload), true);

	// confirm the signature
	$expected_sig = hash_hmac('sha256', $payload, $secret, $raw = true);
	if ($sig !== $expected_sig) {
		error_log('fb_parse_signed_request: bad signed JSON signature @ '.strip_uri_args($_SERVER['REQUEST_URI']));
		return null;
	}
	return $data;
}
function fb_base64_url_decode($input) {
	return base64_decode(strtr($input, '-_', '+/'));
}
function get_fb_name($fb_user_id) {
	if ($name = memcached_get($key = 'fbname:'.$fb_user_id)) {
		return $name;
	}

	$fb = get_fb();

	$app_access_token = get_fb_app_access_token();

	$name = null;

	try {
		$response = $fb->get(
			$q = '/'.$fb_user_id,
			$app_access_token
		);

		$fb_user_profile = $response->getGraphUser();

		$name = $fb_user_profile->getName();

	} catch(Exception $e) {
		fb_process_exception([
			'exception'	=> $e,
			'access_token'	=> $app_access_token,
			'userid'	=> CURRENTUSERID,
			'query'		=> $q,
		]);
		return false;
	}
	memcached_set($key,$name,ONE_DAY);
	return $name;
}
function fb_show_disconnect_list_item() {
	if (!FACEBOOK_ENABLED) {
		return false;
	}
	if (fb_have_connection()) {

		$tokens = fb_get_tokens();
		foreach ($tokens as $token) {
			[$access_token, $fb_user_id, $expires, $mstamp] = $token;
			$desc = get_fb_name($fb_user_id) ?: $fb_user_id;
			?><li><?
			echo get_facebook_icon();
			?> <a href="/user/disconnectfb/<?= $fb_user_id ?>"><?= __C('action:disconnect_facebook') ?>: <?= escape_specials($desc,true) ?></a><?
			?> <?= MIDDLE_DOT_ENTITY ?> <?
			if ($expires > CURRENTSTAMP) {
				?><span class="notice-nb"><?
				echo __('status:active');
				?></span><?
			} else {
				?><span class="error-nb"><?
				echo __('status:expired');
				?></span><?
			}
			?></li><?
		}
	} else {
		?><li><?
		echo get_facebook_icon();
		?> <a href="/user/connectfb"><?= __('action:connect_with_facebook') ?></a><?
		?></li><?
	}
}
function fb_disconnect() {
	if (!FACEBOOK_ENABLED) {
		return false;
	}
	$tokens = fb_get_tokens();
	if (!$tokens) {
		return false;
	}

	$del_fb_user_id = $_REQUEST['subID'];

	$fb = get_fb();

	foreach ($tokens as $token) {
		[$access_token,$fb_user_id,$expires,$mstamp] = $token;

		if ($del_fb_user_id
		&&	$del_fb_user_id != $fb_user_id
		) {
			continue;
		}

		$q = null;
		try {
			$response = $fb->delete(
				$q = '/me/permissions',
				[],
				$access_token
			);

		} catch (Exception $e) {
			fb_process_exception([
				'exception'	=> $e,
				'access_token'	=> $access_token,
				'userid'	=> CURRENTUSERID,
				'query'		=> $q,
			]);
			return false;
		}

		delete_fb_connection($fb_user_id);
		fb_clear_token($fb_user_id);
		fb_clear_permissions($fb_user_id);
		fb_action_log($fb_user_id,'deauth');
	}
	fb_flush_renew();
	register_notice('facebook:notice:disconnected_LINE');
}
function fb_background($userid,$arg) {
	$ok = true;
	$auto = false;
	foreach ((is_array($arg) ? $arg : [$arg]) as $action) {
		switch ($action) {
		case 'auto':
			$auto = true;
			continue 2;
		case 'sync_fields':
			$fb_created = fb_created_info($userid);
			if (!$fb_created
			||	(	$fb_created['CHANGED_PROFILE']
				&&	$fb_created['CHANGED_IMAGE']
				)
			) {
				continue 2;
			}
			break;
		}
		$rc = db_insert('facebook_background','
		INSERT INTO facebook_background SET
			AUTO	=b\''.($auto ? 1 : 0).'\',
			USERID	='.$userid.',
			ACTION	="'.$action.'",
			CSTAMP	='.CURRENTSTAMP
		);
		if (!$rc) {
			$ok = false;
		}
	}
	return $ok;
}
function retry_background($userid,$action,$delay = FIVE_MINUTES) {
	# keep tabs on table to check whether endless loops are created

	return db_insert('facebook_background','
	INSERT INTO facebook_background SET
		NOTBEFORE	='.(CURRENTSTAMP + $delay).',
		USERID		='.$userid.',
		ACTION		="'.$action.'",
		CSTAMP		='.CURRENTSTAMP
	);
}
function fb_need_refresh($userid = 0) {
	# false	=> no facebook / permissions
	# null	=> no need refresh
	# true	=> need refresh

	if (!FACEBOOK_ENABLED) {
		return false;
	}
	if (!$userid) {
		$userid = CURRENTUSERID;
	}
	static $__need;
	if (isset($__need[$userid])) {
		return $__need[$userid];
	}
	$__need[$userid] = false;

	$tokens = fb_get_tokens($userid);
	if (!$tokens) {
		return false;
	}
	if (!HOME_THOMAS)
	foreach ($tokens as $tokeninfo) {
		[$access_token, $fb_user_id, $expires, $mstamp] = $tokeninfo;

		if ( 	$access_token
		&&	CURRENTSTAMP - $mstamp > ONE_DAY			# don't update token if it's newer than 1 day
#		&&	CURRENTSTAMP < ($expires - 2 * ONE_WEEK)		# expires further than 2 weeks away
#		&&	$mstamp > CURRENTSTAMP - ONE_DAY			# modified in past day
		) {
			# have valid token
			return $__need[$userid] = null;
		}
	}
	$permissions = fb_have_permission();
	if (!$permissions) {
		error_log('WARNING fb refresh but no existing permissions for '.$userid);
	}
	return $__need[$userid] = $tokens[0];
}
function show_fb_status_rows(deflst &$list, int $userid): void {
	$fb_user_id = fb_have_connection($userid);
	if ($fb_user_id) {
		[,, $expires, $token_mstamp] = fb_get_token($userid);

		$fb_active = $expires > CURRENTSTAMP;

		$self = CURRENTUSERID ==$userid;

		$list->add_row(
			Eelement_name('connection'),
			'<a class="'.($fb_active ? 'green' : 'red').'hue" target="_blank" href="https://www.facebook.com/'.$fb_user_id.'">'.get_facebook_icon().'</a>'.
			(	$fb_active
			?	' <span class="notice-nb">'.__('status:active').'</span>'.
					($self && fb_need_refresh() ? ' &rarr; <a href="/user/renewfb">'.__('action:renew').'</a>' : null)

			:	' <span class="warning-nb">'.__('status:expired').'</span>'.
					($self ? ' &rarr; <a href="/user/renewfb">'.__('action:renew').'</a>' : null)
			)
		);

		if (have_admin('helpdesk')) {
			$list->add_row('&nbsp;&nbsp;&nbsp;'.__C($expires > CURRENTSTAMP ? 'lock:expires(at)' : 'lock:expired(at)'),
					_datedaytime_get($expires ?: $token_mstamp));

			$list->add_row('&nbsp;&nbsp;&nbsp;'.__C($expires > CURRENTSTAMP ? 'lock:expires(at)' : 'lock:expired(at)'),
					!$expires ? __('status:invalid') : ($days = round(($expires - CURRENTSTAMP) / ONE_DAY)).' '.element_name('day', $days));

			$list->add_row('&nbsp;&nbsp;&nbsp;'.__C('field:last_modified'),
					-($days = round((CURRENTSTAMP - $token_mstamp) / ONE_DAY)).' '.element_name('day', $days));
		}

		if (have_super_admin()) {
			$list->add_row('FB Move', '<a target="_blank" href="/test/fbmove/'.$userid.'">MOVEAWAY</a>');
		}
	}
	if (have_super_admin()
	&&	db_single('fbcreated_user','SELECT 1 FROM fbcreated_user WHERE USERID = '.$userid)
	) {
		$list->add_row('Similars','<a target="_blank" href="/test/fbcreated/'.$userid.'">VIEW</a>');
	}
}

function fb_get_userimage($user_profile, &$data = null) {
	$userimage_tmp = null;
	$userimage_crc = $userimage_len = 0;

	if (($picture = $user_profile->getPicture())
	&&	!$picture->isSilhouette() # don't copy default img
	&&	($data = safe_file_get_contents($picture->getUrl()))
	) {
		$tmp = '/tmpdisk/__fbimage_'.uniqid();
		if (file_put_contents($tmp,$data)) {
			register_shutdown_function('unlink',$tmp);
			$userimage_tmp = $tmp;
			$userimage_crc = crc32($data);
			$userimage_len = strlen($data);
		}
	}
	return [$userimage_tmp,$userimage_crc,$userimage_len];
}

function fb_store_userimage(int $userid, ?string $userimage_tmp =  null): bool {
	if (!$userimage_tmp) {
		return true;
	}
	require_once '_userimage.inc';
	$oldPOST = $_POST;
	$_POST = [
		'CAPTION'	=> null,
		'BOTH'		=> true,
	];
	$_FILES = [
		'FILE'	=> [
			'name'		=> [
				1	=> 'fb',
			],
			'tmp_name'	=> [
				1	=> $userimage_tmp,
			],
			'error'		=> [
				1	=> UPLOAD_ERR_OK,
			],
			'size'		=> [
				1	=> filesize($userimage_tmp),
			],
		],
	];
	$rc = commit_userimage($userid, facebook: true);
	$_POST = $oldPOST;
	$_FILES = [];
	return $rc;
}

function fb_parse_profile_fields($user_profile) {
	$cityid = $countryid = 0;
	$city_name = '';

	if (($location = $user_profile->getLocation())
	&&	(	is_a($location,'Facebook\\GraphNodes\\GraphLocation')
		||	property_exists($location,'getLocation')
		&&	($location = $location->getLocation())
		&&	is_a($location,'Facebook\\GraphNodes\\GraphLocation')
		)
	) {
		$cities = db_simple_hash('city','
			SELECT CITYID,NAME
			FROM city
			WHERE (@distance:='.distance($location->getLatitude(),$location->getLongitude(),'LATITUDE','LONGITUDE').') <= 2
			ORDER BY @distance DESC'
		);
		if ($cities) {
			[$cityid,$name] = keyval($cities);
			$city = memcached_city_info($cityid);
			$countryid = $city['COUNTRYID'];
		}
		$city_name = iconv('utf-8','windows-1252//TRANSLIT',$location->getName());
	}

	if ($birthday = $user_profile->getBirthday()) {
		$birth_day = $birthday->format('d');
		$birth_month = $birthday->format('m');
		$birth_year = $birthday->format('Y');
	} else {
		$birth_year = $birth_month = $birth_day = 0;
	}


	$email = $user_profile->getEmail();

	$realname = $name = make_clean_realname($user_profile->getName() ?? '', true);

	$gender = match($user_profile->getGender()) {
		'male'		=> 'M',
		'female'	=> 'F',
		default		=> '',
	};

	return [
		'email'		=> $email,
		'name'		=> $name,
		'realname'	=> $realname,
		'gender'	=> $gender,
		'birth_day'	=> $birth_day,
		'birth_month'	=> $birth_month,
		'birth_year'	=> $birth_year,
		'city_name'	=> $city_name,
		'cityid'	=> $cityid,
		'countryid'	=> $countryid,
	];
}
function fb_maybe_retry($e,$userid,$action,$response) {
	# returns true to skip fb_process_exception
	if (!CLI) {
		# only retry for cli script facebook_background
		return false;
	}
	if (!empty($response)
	&&	method_exists($response,'getHttpStatusCode')
	&&	($status = $response->getHttpStatusCode()) >= 500
	&&	$status < 600
	) {
		# server error
		return true;
	}
	switch ($e->getCode()) {
	case 2: # ??
	case 7:	# Failed to connect to graph.facebook.com port 433: Connection timed out
	case 28:# Operation timed out

		retry_background($userid,$action);
		return true;
	}
	return false;
}
