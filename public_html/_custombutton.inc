<?php

const ACTIVE_CUSTOMBUTTONS_CACHE_KEY = 'active_custombuttons_v2';

function custombutton_count_view(int $buttonid, string $element, int $id): int|false {
	if (!db_insert('buttonview', "
		INSERT INTO buttonview SET 
			BUTTONID = $buttonid.',
			ELEMENT	 = '$element',
			ID		 = $id,
			STAMP	 = ".CURRENTSTAMP.',
			USERID	 = '.CURRENTUSERID.',
			IPBIN	 ="'.addslashes(CURRENTIPBIN).'",
			IDENTID	 ='.CURRENTIDENTID
	)) {
		return false;
	}
	return db_insert_id();
}
function custombutton_count_hit(int $buttonid, int $incid): bool {
	return db_update('buttonview', '
		UPDATE buttonview SET CLICKED = '.CURRENTSTAMP."
		WHERE BUTTONID = $buttonid
		  AND INCID = $incid"
	);
}
function custombutton_jump(int $buttonid, int $incid): void {
	if (!($destination = db_single('custombutton', "
		SELECT DESTINATION
		FROM custombutton
		WHERE BUTTONID = $buttonid"
	))) {
		require_once '_image_bail.inc';
		image_bail($destination === false ? 503 : 404);
	}
	$info = db_single_array('buttonview', "
		SELECT ELEMENT, ID
		FROM buttonview
		WHERE BUTTONID = $buttonid
		  AND INCID = $incid"
	);
	[$element, $id] = $info ?: [null, 0];

	show_url_and_location($destination, $element, $id);

	if (ROBOT) {
		return;
	}

	require_once '_identity.inc';
	require_once '_currentuser.inc';
	global $currentuser;
	$currentuser = new _currentuser(TINY_USER | SKIP_SETTINGS);
	custombutton_count_hit($buttonid, $incid);
}

function get_custombutton_jump_link(int $buttonid, int $incid) {
	return "/button/$buttonid/$incid";
}

function get_custombutton_and_count(?string $element = null, ?int $id = null): ?string {
	$element ??= $_REQUEST['sELEMENT'];
	$id		 ??= $_REQUEST['sID'];

	static $__actives = 0;
	if ($__actives === 0) {
		if (empty($_REQUEST['NOMEMCACHE'])) {
			$__actives = memcached_get(ACTIVE_CUSTOMBUTTONS_CACHE_KEY);
		}
		if (!$__actives) {
			if (false === ($__tmp_actives = db_rowuse_array(['custombutton', 'custombutton_placement'],'
				SELECT ELEMENT, ID, BUTTONID, STOPSTAMP, CLASS, TEXT
				FROM custombutton
				JOIN custombutton_placement USING (BUTTONID)
				WHERE ACTIVE
				  AND STOPSTAMP > '.CURRENTSTAMP))
			) {
				return null;
			}
			$__actives = [];
			foreach ($__tmp_actives as $__tmp_active) {
				/** @noinspection PhpRedundantOptionalArgumentInspection */
				extract($__tmp_active, \EXTR_OVERWRITE);
				$__actives[$ELEMENT][$ID] = [
					$BUTTONID,
					$STOPSTAMP,
					$CLASS,
					$TEXT,
				];
			}
			memcached_set(ACTIVE_CUSTOMBUTTONS_CACHE_KEY, $__actives, ONE_HOUR);
		}
	}
	if (empty($__actives[$element][$id])) {
		# no button for this element
		return null;
	}
	[$buttonid, $stop_stamp, $class, $text] = $__actives[$element][$id];
	if ($stop_stamp < CURRENTSTAMP) {
		# ended, might still be cached, flush cache to regenerate
		memcached_delete(ACTIVE_CUSTOMBUTTONS_CACHE_KEY);
		return null;
	}
	$incid = custombutton_count_view($buttonid, $element, $id);
	ob_start();
	?><a href="<?= get_custombutton_jump_link($buttonid, $incid) ?>" rel="nofollow"><?
	?><div class="action-button generic-button <?= $class ?>"><?= $text ?></div><?
	?></a><?
	return ob_get_clean();
}
