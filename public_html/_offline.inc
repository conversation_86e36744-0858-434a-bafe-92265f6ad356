<?php

require_once '_globalsettings.inc';

function online_check(): void {
	if (!($offline = partyflock_offline())) {
		return;
	}

	header('Retry-After: 600', true, 503);
	header('Cache-control: no-cache', true);
	header('Content-Type: text/html; charset=windows-1252');
	header('X-Robots-Tag: noindex', true);

	define('CURRENTUSERID', 1);

	require_once '_style.inc';
	require_once '__translation.php';

	?><!DOCTYPE html><?
	?><html lang="<?= CURRENTLANGUAGE ?>"><?
		?><head><? # NOSONAR
			show_favicon_link();
			?><title>Partyflock temporary offline</title><?
		?></head><?
	?><body style="padding: 1em; font-family: sans-serif;"><?

	?><h1 style="font-weight: bold; color: #F00;"><?
	if ($str = __('offline:header_LINE', RETURN_FALSE)) {
		   echo $str;
	} else {
		   ?>Partyflock temporary offline<?
	}
	?></h1><?

	if ($ctime = filemtime('/offline')) {
		require_once '_date.inc';
		?><div style="opacity: .4;"><?
		[$year, $month, $day, $hour, $mins] = _getdate();
		printf('%04d-%02d-%02d %02d:%02d', $year, $month, $day, $hour, $mins);
		?></div><?
	}

	if ($str = __('offline:information_TEXT',DO_NL2BR | RETURN_FALSE)) {
		?><div class="block"><?= $str ?></div><?
	} else {
		?><div class="block">Due to maintenance or technical difficulties Partyflock is temporarily offline.<br /><?
		?><div class="block">Our apologies for any inconvenience this may cause, we are trying to get up an running as quickly as possible!</div><?
	}

	if (is_string($offline)) {
		# Additional information
		require_once '_helper.inc';
		require_once '_date.inc';
		?><h2><?= __('offline:additional_info_LINE', RETURN_FALSE) ?: 'Additional information' ?></h2><?
		?><div><?= nlnl2cmbr(escape_utf8($offline)) ?></div><?
	}
	?></body><?
	?></html><?
	exit;
 }
