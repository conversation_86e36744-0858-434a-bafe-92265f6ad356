<?php

function commit_userimage(?int $i_am_userid = null, ?int $muserid = null, ?bool $facebook = false) {
	if (NO_ALTER_USERIMAGE) {
		return false;
	}
	$i_am_userid ??= CURRENTUSERID;
	$muserid ??= $i_am_userid;

	require_once '_execute.inc';
	require_once '_uploadimage.inc';
	require_once '_profileimage.inc';
	if (!$facebook) {
		if (!require_user()
		||	!require_idnumber($_REQUEST,'sID')
		||	!require_self_or_admin($userid = $_REQUEST['sID'],'helpdesk')
		) {
			return false;
		}
	} else {
		$userid = $i_am_userid;
	}
	if ($use_photo = ($action = $_REQUEST['ACTION'] ?? '') === 'usephoto') {
		if (!require_idnumber($_REQUEST, 'subID')
		||	!require_referer_regex('/photo/'.$_REQUEST['subID'].'[\.:/]')
		) {
			return false;
		}
	} elseif (
		!CLI # allow cli to perform commit (facebook updated profile image)
	&&	!require_referer()
	) {
		return false;
	}
	if (isset($_POST['CAPTION'])) {
		$_POST['CAPTION'] = trim($_POST['CAPTION']);
		$caption = db_single('userimagecaption','SELECT BODY FROM userimagecaption WHERE USERID='.$userid,DB_USE_MASTER);
		if ($caption === false) {
			return false;
		}
		if ($caption != $_POST['CAPTION']) {
			if (!db_insert('userimagecaption_log','
				INSERT INTO userimagecaption_log
				SELECT * FROM userimagecaption
				WHERE USERID='.$userid)
			) {
				return false;
			}
			if ($_POST['CAPTION']) {
				if (!db_replace('userimagecaption','
					REPLACE INTO userimagecaption SET
						BODY	="'.addslashes($_POST['CAPTION']).'",
						MSTAMP	='.CURRENTSTAMP.',
						MUSERID	='.$muserid.',
						USERID	='.$userid)
				) {
					return false;
				}
				register_notice('userimage:notice:caption_changed_LINE');
			} else {
				if (!db_insert('userimagecaption_log','
					INSERT INTO userimagecaption_log SET
						USERID	='.$i_am_userid.',
						BODY	="",
						MSTAMP	='.CURRENTSTAMP.',
						MUSERID	='.$muserid)
				||	!db_delete('userimagecaption','
					DELETE FROM userimagecaption
					WHERE USERID='.$i_am_userid)
				) {
					return false;
				}
				register_notice('userimage:notice:caption_removed_LINE');
			}
		}
	}
	$existing = db_simple_hash('userimage','
		SELECT LARGE,DATAID
		FROM userimage
		WHERE DATAID!=0
		  AND USERID='.$userid,
		DB_USE_MASTER
	);
	if ($existing === false) {
		return false;
	}
	if ($existing) {
		$haveothers = db_same_hash('userimage','
			SELECT DATAID
			FROM userimage
			WHERE USERID!='.$userid.'
			  AND DATAID IN ('.implode(',',$existing).')'
		);
		if ($haveothers === false) {
			return false;
		}
	} else {
		$haveothers = null;
	}
	$both = !empty($_POST['BOTH']);
	$whiches =
		$both
	?	[1]
	:	(	isset($_REQUEST['SMALL'])
		?	[0]
		:	(	isset($_REQUEST['LARGE'])
			?	[1]
			:	[0,1]
			)
		);

	if ($both) {
		if (empty($_POST['REMOVE'][1])
		&&	empty($_POST['URL'][1])
		&&	empty($_FILES['FILE']['name'][1])
		) {
			$use_image = db_single_assoc(['uploadimagedatameta','userimage'],'
				SELECT LARGE,DATAID,WIDTH,HEIGHT,CRCORIG,LENORIG
				FROM userimage
				JOIN userimagedatameta USING (DATAID)
				WHERE USERID='.$userid.'
				  AND LARGE IN (1,3,5)
				ORDER BY WIDTH DESC
				LIMIT 1'
			);
			if ($use_image === false) {
				return false;
			}
			if ($use_image) {
				$data = dbdata_single('userimagedata', $use_image['DATAID'],'
					SELECT SQL_NO_CACHE DATA
					FROM data_db.userimagedata
					WHERE DATAID='.$use_image['DATAID']
				);
				if ($data === false) {
					return false;
				}
				if ($data) {
					$tmp = '/tmpdisk/__userimage_input_'.uniqid();
					register_shutdown_function('unlink',$tmp);
					file_put_contents($tmp,$data);
					$_FILES['FILE']['name'][0] = true;
					$_FILES['FILE']['tmp_name'][0] = $tmp;
					$crc_orig[0] = $use_image['CRCORIG'];
					$len_orig[0] = $use_image['LENORIG'];
					$whiches = [0,2,4];
					$both = false;
				}
			}
		}
	}
	foreach ($whiches as $which) {
		# 0: thumb
		# 1: regular
		# 2: thumb @2x
		# 3: regular @2x
		# 4: thumb original
		# 5: regular original

		$sizes = $both ? [0,1,2,3,4,5] : [$which,$which+2,$which+4];

		if (isset($_POST['REMOVE'][$which])) {
			foreach ($sizes as $size) {
				remove_userimage($userid,$size,$existing,$haveothers,true);
			}
			flush_userimage($userid,$which);
			continue;
		}
		$tmp = false;
		if ($use_photo) {
			$imgid = $_REQUEST['subID'];
			$image = db_single_assoc('image','SELECT HIDDEN,WIDTH,HEIGHT,PARTYID FROM image WHERE IMGID='.$imgid);
			if ($image === false) {
				return false;
			}
			if (!$image) {
				register_error('photo:error:nonexistent_LINE', ['ID' => $imgid]);
				return false;
			}
			if ($image['HIDDEN']) {
				register_error('photo:error:hidden_LINE', ['ID' => $imgid]);
				return false;
			}
			$filesize = false;
			$tmp = '/tmpdisk/__userimage_'.uniqid();
			register_shutdown_function('unlink', $tmp);
			foreach (['main2x', 'main'] as $size_type) {
				$filename = '/mnt/gluster/photos_v2/'.$image['PARTYID'].'/'.$size_type.'/'.$imgid.'.jpg';
				if ($filesize = filesize($filename)) {
					if (!copy($filename, $tmp)) {
						register_error('userimage:error:failed_to_copy_LINE');
						return false;
					}
					break;
				}
			}
			if (!$filesize) {
				register_error('userimage:error:no_photo_found_LINE');
				return false;
			}
			$crc_orig[$which] = (int)hexdec(hash_file('crc32', $filename, false));
			$len_orig[$which] = $filesize;

		} elseif (!empty($_FILES['FILE']['name'][$which])) {
			if (!require_files('FILE',false,$which)) {
				return false;
			}
			$tmp = $_FILES['FILE']['tmp_name'][$which];
			if ($tmp
			&&	!isset($crc_orig[$which])
			) {
				$crc_orig[$which] = crc32(file_get_contents($tmp));
				$len_orig[$which] = filesize($tmp);
			}

		} elseif (!empty($_POST['URL'][$which])) {
			$url = $origurl = $_POST['URL'][$which];
			if (preg_match('"//album\.partyflock.*?(\d+)(t)?\.(?:jpg|gif|png)"', $url, $match)
			||	preg_match('"/albumelement/(\d+)[:\.]"', $url, $match)
			||	preg_match('"albumelement=(\d+)"', $url, $match)
			) {
				$idname = !empty($match[2]) ? 'THMBID' : 'DATAID';
				$albumelementid = $match[1];
				$albumelement = db_single_assoc(
					'albumelement','
					SELECT ALBUMID AS USERID,ACCEPTED,VISIBILITY_ELEMENT,meta.DATAID,ORIGNAME,CRCORIG,LENORIG,STOREID
					FROM albumelement AS ae
					JOIN albumimagedatameta AS meta ON meta.DATAID=ae.'.$idname.'
					WHERE ALBUMELEMENTID='.$albumelementid
				);
				if ($albumelement === false) {
					continue;
				}
				if (!$albumelement) {
					_error(__('albumelement:error:nonexistent_LINE',array('ID'=>$albumelementid)));
					continue;
				}
				require_once '_visibility.inc';
				if (!have_self_or_admin($albumelement['USERID'],array('album','albumelement','offsense','helpdesk'))
				&&	(	!$albumelement['ACCEPTED']
					||	!($visi = _visibility($albumelement,'ELEMENT'))
					)
				) {
					_error(__('albumelement:warning:inaccessible_LINE',array('ID'=>$albumelementid)));
					continue;
				}
				$storeid = $albumelement['STOREID'];
				$data = dbdata_single('albums',$storeid,'SELECT SQL_NO_CACHE DATA FROM albums.u'.$storeid.' WHERE DATAID='.$albumelement['DATAID']);
				$crc_orig[$which] = $albumelement['CRCORIG'];
				$len_orig[$which] = $albumelement['LENORIG'];

			} else {
				if (preg_match('"\[img\](.+?)\[/img\]"i', $url, $match)
				||	preg_match('"\[img=(.+?)\]"i', $url, $match)
				) {
					$url = $match[1];
				}
				$data = safe_file_get_contents($url, [CURLOPT_HTTPHEADER => [ACCEPT_HEADER_FOR_IMAGE]]);
				$crc_orig[$which] = crc32($data);
				$len_orig[$which] = strlen($data);
			}
			if (!$data) {
				register_error('albumelement:error:no_data_at_LINE',DO_UBB,['URL'=>$origurl]);
				continue;
			}
			$tmp = '/tmpdisk/__userimage_'.uniqid();
			if (!file_put_contents($tmp,$data)) {
				register_error('userimage:error:failed_to_write_temporarily_LINE');
				continue;
			}
			register_shutdown_function('unlink',$tmp);
		}
		if (!$tmp) {
			continue;
		}
		require_once '_uploadimage.inc';
		if (!($info = uploadimage_file_info($tmp))) {
			register_error('userimage:error:imagesize_information_unobtainable_LINE');
			continue;
		}
		static $__maxs = [
				[100*200,  100, 200],
				[480*340,  640, 480],
				[200*400,  200, 400],
				[960*680, 1280, 960],
		];
		$gensizes = [];
		foreach ($sizes as $ndx => $size) {
			[$width[$size], $height[$size]] = $info;
			if ($size > 3) {
				# store original
				if ($width[$size] > $width[$size-2]) {
					$gensizes[$size] = [$width[$size], $height[$size]];
				} else {
					remove_userimage($userid, $size, $existing, $haveothers, false);
				}
				continue;
			}
			scale_max_pixels(
				$width[$size],
				$height[$size],
				$__maxs[$size][0],
				$__maxs[$size][1],
				$__maxs[$size][2]
			);
			if ($size < 2 || $width[$size] > $width[$size - 2]) {
				$gensizes[$size] = [$width[$size], $height[$size]];
			} else {
				remove_userimage($userid, $size, $existing, $haveothers, false);
				flush_userimage($userid, $which);
			}
		}
		if ($gensizes) {
			$procthese[] = [$gensizes,$tmp,$info];
		}
	}
	if (!isset($procthese)) {
		return false;
	}
	$identicals = $blobs = $allgensizes = [];
	require_once '_imageprocessing.inc';
	foreach ($procthese as $tmpinfo) {
		[$gensizes, $tmp, $info] = $tmpinfo;
		$info = process_images($gensizes, [
			'src'			=> $tmp,
			'source_data'	=> $data ?? null,
			'info'			=> $info,
		]);
		if (!$info) {
			continue;
		}
		[$tmpblobs, $filetype, $unique_size, $tmpidenticals] = $info;
		foreach ($tmpblobs as $size => $null) {
			$filetypes[$size] = $filetype;
		}
		$blobs += $tmpblobs;
		$identicals += $tmpidenticals;
		$allgensizes += $gensizes;
	}
	$allcrclens = [];
	foreach ($crc_orig as $which => $crc) {
		$allcrclens[] = [$crc, $len_orig[$which]];
	}
	foreach ($blobs as $size => $filedata) {
		$len = $lens[$size] = strlen($filedata);
		$crc = $crcs[$size] = crc32($filedata);
		$allcrclens[] = [$crc, $len];

		if (max_file_size_surpassed('userimagedata', 'DATA', $len)) {
			return 0;
		}
	}

	require_once '_prohibited.inc';
	foreach ($allcrclens as $info) {
		[$crc, $len] = $info;
		if ($offensetype = crclen_is_prohibited($crc, $len, $prohibid)) {
			register_error('image:prohibited_LINE', DO_UBB, ['PROHIBID' => $prohibid]);
			return false;
		}
	}
	$forceid = $__done = array();
	foreach ($blobs as $size => $filedata) {
		$len = $lens[$size];
		$crc = $crcs[$size];

		$dataids = db_simpler_array('userimagedatameta','SELECT DATAID FROM userimagedatameta WHERE CRC='.$crc.' AND LEN='.$len);
		if ($dataids === false)	{
			return false;
		}
		if (!$dataids) {
			continue;
		}
		foreach ($dataids as $dataid) {
			$data = db_single('userimagedata', 'SELECT DATA FROM data_db.userimagedata WHERE DATAID='.$dataid);
			if ($data === false) {
				return false;
			}
			if (!$data) {
				continue;
			}
			if ($data == $filedata) {
				$forceid[$size] = $dataid;
				break;
			}
		}
	}
	# $__done may span large and small images
	foreach ($blobs as $size => $filedata) {
		$action = 'set';
		$dataid = 0;
		if ($dataid = $forceid[$size] ?? false) {
			$action = 'setother';
		} elseif ($dataid = $__done[$filedata] ?? false) {
			$action = 'setsame';
		} else {
			$dataid = 0;
			[$width, $height] = $allgensizes[$size];
			$which = $size % 2;
			$crc = $crcs[$size];
			$len = $lens[$size];
			$filetype = $filetypes[$size];

			if (!db_insert('userimagedatameta','
				INSERT INTO userimagedatameta SET
					DATAID	='.$dataid.',
					WIDTH	='.$width.',
					HEIGHT	='.$height.',
					TYPE	="'.$filetype.'",
					CRCORIG	='.($both ? $crc_orig[1] : $crc_orig[$which]).',
					LENORIG	='.($both ? $len_orig[1] : $len_orig[$which]).',
					CRC	='.$crc.',
					LEN	='.$len)
			||	!($dataid = db_insert_id())
			||	!db_replace('userimagedata', '
				INSERT INTO data_db.userimagedata SET
					DATA	="'.addslashes($filedata).'",
					DATAID	='.$dataid)
			) {
				continue;
			}
		}
		if (str_starts_with($action,'set')) {
			if (isset($existing[$size])
			&&	$existing[$size] != $dataid
			) {
				remove_userimage($userid,$size,$existing,$haveothers,false);
			}
		}
		if (!db_insupd('userimage','
			INSERT INTO userimage SET
				USERID	='.$userid.',
				LARGE	='.$size.',
				DATAID	='.$dataid.',
				MSTAMP	='.CURRENTSTAMP.',
				MUSERID	='.$muserid.',
				ACCEPTED=1
			ON DUPLICATE KEY UPDATE
				DATAID	=VALUES(DATAID),
				MSTAMP	=VALUES(MSTAMP),
				MUSERID	=VALUES(MUSERID),
				ACCEPTED=1')
		) {
			continue;
		}
		flush_userimage($userid,$size);
		if ($size < 2) {
			if (!CLI) {
				register_notice('userimage:notice:'.($size ? 'large' : 'small').'_image_changed_LINE');
			}
		}
		$existing[$size] = $dataid;
		$__done[$filedata] = $dataid;
	}
	return true;
}
function remove_userimage($userid,$size,&$existing,$haveothers,$msg = false) {
	if (!isset($existing[$size])) {
		return true;
	}

	$dataid = $existing[$size];

	$deldata
	=	!isset($haveothers[$dataid])
	&&	!have_self_other($existing,$size,$dataid);

	unset($existing[$size]);

	if (!db_insert('userimage_history','
		INSERT INTO userimage_history
		SELECT * FROM userimage
		WHERE USERID='.$userid.'
		  AND DATAID='.$dataid.'
		  AND LARGE='.$size)
	||	!db_delete('userimage','
		DELETE FROM userimage
		WHERE USERID='.$userid.'
		  AND DATAID='.$dataid.'
		  AND LARGE='.$size)
	) {
		return false;
	}
	if ($msg && $size < 2) {
		if (!CLI) {
			register_notice('userimage:notice:'.($size ? 'large' : 'small').'_image_deleted_LINE');
		}
	}
	return true;
}
function have_self_other($existing,$searchsize,$searchdataid) {
	foreach ($existing as $size => $dataid) {
		if ($searchsize != $size
		&&	$searchdataid == $dataid
		) {
			return true;
		}
	}
	return false;
}
