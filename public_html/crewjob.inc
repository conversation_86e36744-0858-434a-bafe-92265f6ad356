<?php

function perform_commit() {
	switch ($_REQUEST['ACTION']) {
	case 'commit':	return crewjob_commit();
	}
}
function display_body() {
	switch ($_REQUEST['ACTION']) {
	default:
	case null:
	case 'commit':	return crewjob_display_overview();
	case 'form':	return crewjob_display_form();
	}
}
function require_crewjob_element(array $array, string $field): string|false {
	require_once '_crewjob.inc';
	return require_element($array, $field, CREW_JOBS);
}

function crewjob_display_overview() {
	require_once '_crewjob.inc';
	layout_show_section_header();

	$crewjobs = memcached_rowuse_hash('crewjob','
		SELECT ELEMENT,ACTIVE,BODY,TITLE
		FROM crewjob'
	);
	if ($crewjobs === false) {
		return;
	}
	if (!$crewjobs) {
		?><p><?= __('crewjob:no_crewjobs_at_the_moment_LINE'); ?></p><?
		return;
	}
	$is_admin = have_admin('crewjobdescription');

	ob_start();

	foreach (CREW_JOBS as $element) {
		if ($element === 'footer') {
			continue;
		}
		$crewjob = isset($crewjobs[$element]) ? $crewjobs[$element] : null;
		if (!$is_admin
		&&	(	!$crewjob
			||	!$crewjob['ACTIVE']
			)
		||	$_REQUEST['ACTION']
		&&	$_REQUEST['ACTION'] !== 'commit'
		&&	$_REQUEST['ACTION'] !== $element
		) {
			continue;
		}
		$active = !empty($crewjob['ACTIVE']);

		if (!$active) {
			?><div class="small"><?
		}
		layout_open_box($active ? 'white' : 'grey',$element);
		layout_open_box_header();

		$type = !$crewjob || $crewjob['ELEMENT'] === 'general' ? null : '-'.$crewjob['ELEMENT'];

		ob_start();
		?><a href="/ticket/form?ELEMENT=crewjob<?= $type ?>"><?
		$contact_link = ob_get_flush();
		if (!empty($crewjob['TITLE'])) {
			echo escape_utf8($crewjob['TITLE']);
		} else {
			echo Eelement_name($element);
		}
		?></a><?
		layout_continue_box_header();
		if ($is_admin) {
			?><a href="/crewjob/form/<?= $element; ?>"><?= __('action:change'); ?></a><?
		}
		layout_close_box_header();
		if ($active
		&&	!empty($crewjob['BODY'])
		) {
			?><div class="block"><?= make_all_html($crewjob['BODY'], UBB_UTF8); ?></div><?
		}
		layout_close_box();
		if (!$active) {
			?></div><?
		} else {
			?><div class="funcs left">&rarr; <?= $contact_link,__C('action:contact_us') ?></a></div><?
		}
	}
	$have = ob_get_length();
	ob_end_flush();

	if (!$have) {
		not_found();
		return;
	}

	if (isset($crewjobs['footer'])
	&&	(	!$_REQUEST['ACTION']
		||	!in_array($_REQUEST['ACTION'],['sales','developer'])
		)
	) {
		if (!empty($crewjobs['footer']['BODY'])) {
			?><p><?= make_all_html($crewjobs['footer']['BODY'], UBB_UTF8); ?></p><?
		}
		if ($is_admin) {
			?><div class="right"><a href="/crewjob/form/footer"><?
			echo __($crewjobs['footer']['BODY'] ? 'action:change_footer' : 'action:change');
			?></a></div<?
		}
	}
}
function crewjob_display_form() {
	if (!require_admin('crewjobdescription')
	||	!require_crewjob_element($_REQUEST,'SUBACTION')
	) {
		return;
	}
	layout_open_section_header();
	echo __('crewjob:pageheader:change_form');
 	layout_close_section_header();

	$crewjob = db_single_assoc('crewjob','SELECT * FROM crewjob WHERE ELEMENT="'.$_REQUEST['SUBACTION'].'"');
	if ($crewjob === false) {
		return;
	}
	$footer = ($_REQUEST['SUBACTION'] === 'footer');

	?><form<?
	?> accept-charset="utf8"<?
	?> onsubmit="return submitForm(this)"<?
	?> method="post"<?
	?> action="/crewjob/commit/<?= $_REQUEST['SUBACTION']; ?>"><?

	if ($footer) {
		?><input type="hidden" name="TITLE" value="" /><?
	}
	layout_open_box('white');
	layout_open_table(TABLE_VTOP | TABLE_CLEAN);
	layout_start_row();
	echo Eelement_name('element');
	layout_field_value();
	echo $_REQUEST['SUBACTION'];
	if (!$footer) {
		layout_restart_row();
		echo Eelement_name('title');
		layout_field_value();
		?><input type="text" required="required" name="TITLE" value="<?= escape_utf8($crewjob['TITLE']); ?>" /><?
	}
	layout_restart_row();
	echo Eelement_name('description');
	layout_field_value();
	?><textarea cols="60" required="required" rows="20" name="BODY"><?= escape_utf8($crewjob['BODY']); ?></textarea><?
	layout_restart_row();
	?><label for="active"><?= __C('status:active'); ?></label><?
	layout_field_value();
	?><input type="checkbox" name="ACTIVE" id="active" value="1"<? if ($crewjob['ACTIVE']) echo ' checked'; ?>><?
	layout_stop_row();
	layout_close_table();
	layout_close_box('white');
	?><div class="block"><input type="submit" value="<?= __('action:change'); ?>" /></div></form><?
}
function crewjob_commit() {
	if (!require_admin('crewjobdescription')
	||	!require_crewjob_element($_REQUEST,'SUBACTION')
	||	!require_something_trim($_POST, 'BODY', null, null, utf8: true)
	||	!require_anything_trim($_POST,'TITLE', utf8: true)
	) {
		return;
	}
 	if (!db_insert('crewjob_log','
		INSERT INTO crewjob_log
		SELECT * FROM crewjob
		WHERE ELEMENT="'.$_REQUEST['SUBACTION'].'"')
	||	!db_update('crewjob','
		INSERT INTO crewjob SET	
			BODY	="'.addslashes($_POST['BODY']).'",
			TITLE	="'.addslashes($_POST['TITLE']).'",
			ACTIVE	='.(isset($_POST['ACTIVE']) ? '1' : '0').',
			MSTAMP	='.CURRENTSTAMP.',
			MUSERID	='.CURRENTUSERID.',
			ELEMENT="'.$_REQUEST['SUBACTION'].'"
		ON DUPLICATE KEY UPDATE
			BODY	=VALUES(BODY),
			TITLE	=VALUES(TITLE),
			ACTIVE	=VALUES(ACTIVE),
			MSTAMP	='.CURRENTSTAMP.',
			MUSERID	='.CURRENTUSERID)
	) {
		return;
	}
	register_notice('crewjob:notice:changed_LINE');
}
