<?php

declare(strict_types=1);

const NEW_ADIMPS = false;

function create_adimp(
	string 	$element,
	int 	$id,
	?int	$uniq			= null,
	bool	$free			= false,
	bool	$try			= false,
	bool	$mark_served	= false,
	bool	$hard_match		= false,
): int|false {
	require_once '_identity.inc';
	require_once '_currentuser.inc';
	global $currentuser;
	$currentuser ??= new _currentuser(TINY_USER | SKIP_SETTINGS);
	if (!NEW_ADIMPS) {
		return true;
	}
	create_adimp_table($element, $id);
	return db_insert('adimp', "
		INSERT INTO adimp_{$element}_$id SET
			IDENTID		= '.CURRENTIDENTID.',
			USERID		= '.CURRENTUSERID.',
			IPBIN		= '".addslashes(CURRENTIPBIN)."',
			IPNUM		= ".CURRENTIPNUM.',
			STAMP		= '.CURRENTSTAMP.',
			FREE		= '.($free		  ? "b'1'" : "b'0'").',
			TRY			= '.($try		  ? "b'1'" : "b'0'").',
			SERVED		= '.($mark_served ? "b'1'" : "b'0'").',
			IMMEDIATE	= '.($mark_served ? "b'1'" : "b'0'").',
			HARDMATCH	= '.($hard_match  ? "b'1'" : "b'0'").',
			UNIQ		= '.($uniq ?? 0)
	);
}

function create_adimp_table(string $element, int $id): bool {
	require_once '_identity.inc';
	require_once '_currentuser.inc';
	global $currentuser;
	$currentuser ??= new _currentuser(TINY_USER | SKIP_SETTINGS);
	if (!NEW_ADIMP) {
		return true;
	}
	switch ($element) {
	case 'ad':
		return db_create('adimp', "
			CREATE TABLE IF NOT EXISTS `adimp_{$element}_$id` (
				`UNIQ`			   bigint unsigned NOT NULL DEFAULT 0,
				`IPBIN`			     varbinary(16) NOT NULL DEFAULT '',
				`IDENTID`			  int unsigned NOT NULL DEFAULT 0,
				`USERID`		mediumint unsigned NOT NULL DEFAULT 0,
				`STAMP`				  int unsigned NOT NULL DEFAULT 0,
				`SERVED`			  bit(1) NOT NULL DEFAULT b'0',
				`FREE`				  bit(1) NOT NULL DEFAULT b'0',
				`TRY`				  bit(1) NOT NULL DEFAULT b'0',
				`IMMEDIATE`			  bit(1) NOT NULL DEFAULT b'0',
				`EXPANDED`			  bit(1) NOT NULL DEFAULT b'0'
	
				`CLICKED`			  bit(1) NOT NULL DEFAULT b'0',
				`HARDMATCH`			  bit(1) NOT NULL DEFAULT b'0',
				PRIMARY KEY (`INCID`)
			)
			ENGINE = MyISAM
			AUTO_INCREMENT = 0
			DEFAULT
			CHARSET = ascii
			/* PAGE_CHECKSUM = 1 */ "
		);`

	case 'newsad':
		return db_create('adimp', "
			CREATE TABLE IF NOT EXISTS `adimp_newsad_$id` (
				`INCID`				 ($ int unsigned NOT NULL AUTO_INCREMENT,
				`IPBIN`		`	     varbinary(16) NOT NULL DEFAULT '',
				`IDENTID`			  int unsigned NOT NULL DEFAULT 0,
				`USERID`		mediumint unsigned NOT NULL DEFAULT 0,
				`STAMP`				  int unsigned NOT NULL DEFAULT 0,
				`FREE`				  bit(1) NOT NULL DEFAULT b'0',
				`TRY`				  bit(1) NOT NULL DEFAULT b'0',
				`CLICKED`			  bit(1) NOT NULL DEFAULT b'0',
				`HARDMATCH`			  bit(1) NOT NULL DEFAULT b'0',
				PRIMARY KEY (`INCID`)
			)
		ENGINE = MyISAM
			AUTO_INCREMENT = 0
			DEFAULT
			CHARSET = ascii
			/* PAGE_CHECKSUM = 1 */ "
		);

	default:
		mail_log("create_adimp_table: unsupported element: $element", get_defined_vars(), error_log: 'ERROR');

	}
	return false;
}
