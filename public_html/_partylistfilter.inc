<?php

function show_partylist_filters_placeholder(array $invisilist): void {
	require_once '_geo.inc';
	[$lat, $lon] = get_geolocation_cache();
	$stored = get_stored_filters();
	layout_open_box('white');
	if ($lat && $lon) {
		// cached lat/lon
		?><meta id="CGEO" content="<?= $lat ?>,<?= $lon ?>" /><?
	}
	if ($invisilist) {
		// initial
		?><meta id="INVISIDS" content="<?= implode(',',$invisilist) ?>" /><?
	}
	?><div class="r"><?= element_name('order') ?>: <?
	?><select name="ORDER" onchange="change_order(this);"><?
		?><option value="NAME"><?= element_name('name') ?></option><?
		?><option<? if ($stored && $stored['SORT'] === 'LOCATION') echo ' selected' ?> value="LOCATION"><?= element_name('location') ?></option><?
		?><option<? if ($stored && $stored['SORT'] === 'CITY')     echo ' selected' ?> value="CITY"><?=     element_name('city') ?></option><?
		?><option<? if ($stored && $stored['SORT'] ==='VISITORS')  echo ' selected' ?> value="VISITORS"><?= element_plural_name('visitor') ?></option><?
	?></select></div><?
	?><form<?
	?> id="filterform"<?
	?> onsubmit="filter_parties(this);lightForm(this,true);return false" data-nopos="<?= __('error:position:not_available_LINE') ?>"><?
	?><div id="filters"><?
	show_partylist_filters_actual($stored);
	?></div><?
	?></form><?
	layout_close_box();
	include_js('js/geo');
	include_js('js/partyfilter');
	include_js('js/form/validation');
}

function show_partylist_filters_actual(?array $stored = null): array|false {
	require_once '_geo.inc';
	require_once '_identity.inc';
	require_once '_currentuser.inc';
	require_once '_countries.inc';

	global $currentuser;
	$currentuser ??= new _currentuser(TINY_USER | SKIP_SETTINGS);

	$currentcity = get_current_city(NO_GEO);

	$current_location = element_name('current_location');

	if ((false !== ($lat = have_float($_REQUEST, 'lat')))
	&&	(false !== ($lon = have_float($_REQUEST, 'lon')))
	) {
		require_once '_geo.inc';
		store_geolocation_cache($lat, $lon);
	}
	$stored ??= get_stored_filters();

	[$lat, $lon] = get_geolocation_cache();

	$have_position = $lat && $lon;

	ob_start();

	$countryid = $stored ? $stored['COUNTRYID'] : ($currentcity ? 0 : 1);	# stored precedes all, then 0 for currentcity, otherwise NL as default

	ob_start();
	?><select<?
	?> name="COUNTRYID"<?
	?> onchange="filter_parties(this.form)"<?
	?>><?
	if ($currentcity) {
		?><optgroup label="<?= element_name('country_of_residence') ?>"><?
			?><option value="0"><?= escape_specials(get_element_title('country',$currentcity['COUNTRYID'])) ?></option><?
		?></optgroup><?
	}
	?><optgroup label="<?= __('elements:other(elements)') ?>"><?
	foreach (get_countries(COUNTRIES_WITH_EVENTS) as $local_countryid => $local_country_name) {
		?><option<?
		?> value="<?= $local_countryid ?>"<?
		if ($local_countryid === $countryid) {
			?> selected="selected"<?
		}
		?>><?= escape_specials($local_country_name) ?></option><?
	}
	?></optgroup><?
	?></select><?
	show_form_part('COUNTRY',$stored,0,ob_get_clean());

	$current_countryid = $currentcity ? $currentcity['COUNTRYID'] : 1;

	require_once '_countryflag.inc';
	show_form_part('COMPATRIOTS',$stored,1,
		str_replace('%X%',
			'<input onchange="
						if (!parseInt(this.value)) {
							this.form.SHOW_COMPATRIOTS.selectedIndex = 0;
						}
						filter_parties(this.form);
					" class="right three_digits inpspc" type="number" data-valid="number" name="COMPATRIOTS" value="'.($stored ? $stored['COMPATRIOTS'] : '10').'" />',
			__('partyfilter:with_at_least_X_compatriots', DO_UBB | KEEP_EMPTY_KEYWORDS).
			' '.get_country_flag($current_countryid,'light')
	));
	show_form_part(
		'VISITORS',
		$stored,
		1,
		str_replace(
			'%X%',
			'<input onchange="
						if (!parseInt(this.value)) {
							this.form.SHOW_VISITORS.selectedIndex = 0;
						}
						filter_parties(this.form);
					" class="right three_digits inpspc" type="number" data-valid="number" name="VISITORS" value="'.($stored ? $stored['VISITORS'] : '100').'" />',
			__('partyfilter:with_at_least_X_visitors', KEEP_EMPTY_KEYWORDS)
	));
	if ($currentcity) {
		show_radius_part(
			'CITY',
			escape_utf8(get_element_title('city',$currentcity['CITYID'])),
			$stored,
			$currentcity['LATITUDE'],
			$currentcity['LONGITUDE']
		);
	}

	show_radius_part('LOCATION',$current_location,$stored,$lat,$lon);

	if (have_user()) {
		require_once '_going.inc';
		if (have_going()) {
			show_form_part('GOING',$stored,1,element_name('your_agenda'));
		}
		if (have_buddies()) {
			show_form_part('BUDDIES',$stored,0,element_plural_name('buddy'));
			show_form_part('BUDDIES_MAYBE',$stored,0,element_plural_name('uncertain_buddy'));
		}
		require_once '_favourite.inc';
		if (get_favourite_count(CURRENTUSERID, 'artist', 'location', 'organization')) {
			show_form_part('FAVOURITES',$stored,1,element_plural_name('favourite'));
		}
		if (memcached_single('genre_fan','SELECT 1 FROM genre_fan WHERE USERID = '.CURRENTUSERID.' LIMIT 1')) {
			show_form_part('GENRES',$stored,0,element_plural_name('favourite_genre'));//,false,__('partyfilter:genres_not_perfect'));
		}
	}
	$data = ob_get_clean();

	# show as radio:

	$part = $stored['PART'] ?? null;

	show_filter_radios($part,$have_position);

	# geo only allowed on https, or if we have long/lat of course

	?><div class="<?
	if ($part !== 'region') {
		?>hidden <?
	}
	?>fpart" id="region_part"><?
	show_radius_part('REGION', $current_location, $stored, $lat, $lon, true);
	?></div><?

	?><div<?
	?> class="<? if ($part !== 'choose') { ?>hidden <? } ?>	fpart">
	?> id="choose_part"><?
	?><?= $data ?></div><?

	return $stored;
}

function show_filter_radios($part, bool $have_position = false) {
	$show_region = $part === 'region';
	$show_interesting = $part === 'choose';
	$show_all = !$show_region && !$show_interesting;

	?><div><label class="basehl cbi <?= $show_all ? null : 'not-' ?>bold-hilited"><?
	?><input class="upLite" onclick="Pf_clickBox(this)" data-upLite="data-upLite" type="radio" name="PART" onchange="change_part(this)" value="all"<?
	if ($show_all) {
		?> checked="checked"<?
	}
	?>> <?= __('partylistfilter:show_entire_agenda') ?></label></div><?

	# geo only possible if have_pos or secure

	?><div><label class="basehl cbi <?= $show_region ? null : 'not-' ?>bold-hilited"><?
	?><input id="onlygeoregion" class="upLite" onclick="Pf_clickBox(this)" data-upLite="data-upLite"  type="radio" name="PART" onchange="change_part(this)" value="region"<?
	if ($show_region) {
		?> checked="checked"<?
	}
	if (!$have_position) {
		?> disabled="disabled"<?
	}
	?>><?
	if (!$have_position) {
		?><script>
		if (navigator.geolocation) {
			let only_geo_region = getobj('only_geo_region');
			if (only_geo_region) {
				only_geo_region.disabled = false;
			}
		}
		</script><?
	}
	?> <?= __('partylistfilter:show_only_current_location') ?></label></div><?

	if (have_user()) {
		?><div><label class="basehl cbi <?= $show_interesting ? null : 'not-' ?>bold-hilited"><?
		?><input class="upLite" onclick="Pf_clickBox(this)" data-upLite="data-upLite"  type="radio" name="PART" onchange="change_part(this)" value="choose"<?
		if ($show_interesting) {
			?> checked="checked"<?
		}
		?>> <?= __('partylistfilter:show_and_choose_interesting') ?></label></div><?
	}
}
function actually_filter_parties() {
	if ($_POST['PART'] === 'all') {
		if (!CURRENTIDENTID) {
			return;
		}
		db_insert('partyfilter_log','
		INSERT INTO partyfilter_log
		SELECT * FROM partyfilter
		WHERE PART!="all"
		  AND IDENTID='.CURRENTIDENTID.'
		  AND USERID='.CURRENTUSERID
		);
		db_insupd('partyfilter','
		INSERT INTO partyfilter SET
			MSTAMP	='.CURRENTSTAMP.',
			USERID	='.CURRENTUSERID.',
			IDENTID	='.CURRENTIDENTID.',
			PART	="all"
		ON DUPLICATE KEY UPDATE
			MSTAMP	=IF(PART="all",MSTAMP,VALUES(MSTAMP)),
			PART	=VALUES(PART)'
		);
		return;
	}
	if (!require_number_list($_POST, 'PARTYIDS', ',', true)) {
		bail(400);
	}
	store_posted_filters();

	$parties = get_partylist_visibilities($_POST['PARTYIDS']);

	header('Content-Type: application/json');
	echo safe_json_encode($parties);

}
function store_posted_filters() {
	if (!CURRENTIDENTID && !have_user()) {
		return;
	}
	if ($_POST['PART'] === 'choose') {
		foreach ([
				'SHOW_COUNTRY',
				'SHOW_FAVOURITES',
				'SHOW_BUDDIES',
				'SHOW_BUDDIES_MAYBE',
				'SHOW_GOING',
				'SHOW_GENRES',
				'SHOW_COMPATRIOTS',
				'SHOW_VISITORS',
			] as $key) {
			$store[] = $key.'=b\''.(($val = have_number($_POST,$key)) ? decbin($val) : 0).'\'';
		}
	}

	$spec = explain_table('partyfilter');

	foreach (($_POST['PART'] === 'choose' ? ['CITY','LOCATION'] : ['REGION']) as $type) {
		if (!($val = have_number($_POST,$key = 'SHOW_'.$type))) {
			$store[] = $key.'=b\'0\'';
			continue;
		}
		$store[] = $key.'=b\''.decbin($val).'\'';
		if ($radius = have_number($_POST,$radius_field = $type.'_RADIUS')) {
			$store[] = $radius_field.'='.min($radius,$spec[$radius_field]->max);
		}
	}
	$store[] = 'VISITORS='.		min(have_idnumber($_POST, 'VISITORS') ?: 100,	$spec['VISITORS']->max);
	$store[] = 'COMPATRIOTS='.	min(have_idnumber($_POST, 'COMPATRIOTS') ?: 20,	$spec['COMPATRIOTS']->max);
	$store[] = 'COUNTRYID='.	(have_idnumber($_POST, 'COUNTRYID') && get_element_title('country',$_POST['COUNTRYID']) ? $_POST['COUNTRYID'] : 0);
	if (empty($store)) {
		return;
	}
	$store[] = 'PART="'.addslashes($_POST['PART']).'"';
	return update_stored_filters($store);
}

function update_stored_filters(array $storelist): bool {
	$stored = get_stored_filters();

	if ($stored
	&&	$stored['IDENTID'] === CURRENTIDENTID
	&&	$stored['USERID'] === CURRENTUSERID
	) {
		if (!db_insert('partyfilter_log', '
			INSERT INTO partyfilter_log
			SELECT * FROM partyfilter
			WHERE NOT '.binary_equal($storelist).'
			  AND IDENTID = '.CURRENTIDENTID.'
			  AND USERID = '.CURRENTUSERID)
		) {
			return false;
		}
		if (!db_affected()) {
			return true;
		}
	}
	return db_insupd('partyfilter', '
		INSERT INTO partyfilter SET
			MSTAMP	='.CURRENTSTAMP.',
			IDENTID	='.CURRENTIDENTID.',
			USERID	='.CURRENTUSERID.','.
			($storestr = implode(',',$storelist)).'
		ON DUPLICATE KEY UPDATE
			MSTAMP	=VALUES(MSTAMP),'.
			$storestr
	);
}

function get_partylist_visibilities(string $partyidstr, bool $usestore = false): array|bool {
	$currentcity = get_current_city();
	if ($usestore) {
		if (!($stored = get_stored_filters())) {
			return true;
		}
		$_POST['PART'] = $stored['PART'];
		if ($currentcity) {
			if ($stored['SHOW_COUNTRY']) {
				if (!($_POST['COUNTRYID'] = $stored['COUNTRYID'])) {
					$_POST['COUNTRYID'] = $currentcity['COUNTRYID'];
				}
			}
			if ($stored['SHOW_CITY']) {
				$_POST['CITY_LATLNG'] = $currentcity['LATITUDE'].','.$currentcity['LONGITUDE'];
			}
		} else {
			$_POST['COUNTRYID'] = $stored['COUNTRYID'];
		}
		require_once '_geo.inc';
		list($lat,$lon) = get_geolocation_cache();
		foreach (array('LOCATION','REGION') as $type) {
			$_POST[$key = 'SHOW_'.$type] = $stored[$key];
			if ($lat
			&&	$lon
			) {
				$_POST[$type.'_LATLNG'] = $lat.','.$lon;
			}
		}
		# just copy
		foreach ([
			'CITY_RADIUS',
			'REGION_RADIUS',
			'LOCATION_RADIUS',
			'SHOW_CITY',
			'SHOW_COUNTRY',
			'SHOW_GOING',
			'SHOW_FAVOURITES',
			'SHOW_BUDDIES',
			'SHOW_BUDDIES_MAYBE',
			'SHOW_GENRES',
			'SHOW_COMPATRIOTS',
			'SHOW_VISITORS',
#			'SHOW_GID',
			'COMPATRIOTS',
			'VISITORS',
		] as $key) {
			$_POST[$key] = $stored[$key];
		}
	}
	$ccountryid = $currentcity ? $currentcity['COUNTRYID'] : 1;
	$filters = [];
	$selectors = [];
#	$havings = array();
	$joins = [];
	$filter = false;
	switch ($_POST['PART']) {
	default:
		$_POST['PART'] = 'all';
	case 'all':
		return true;
	case 'region':
		$distances = ['REGION'];
		break;
	case 'choose':
		$distances = ['CITY','LOCATION'];
#		if (isset($_POST['SHOW_GID'])) {
#			error_log_r($_POST);
#		}
		if (!empty($_POST['SHOW_COUNTRY'])) {
			if (false === ($countryid = require_idnumber($_POST, 'COUNTRYID'))) {
				if ($usestore) {
					return false;
				}
				bail(400);
			}
			if ($_POST['SHOW_COUNTRY'] == 1) { $which = &$selectors; } else { $which = &$filters; }
			$which[] = 'COUNTRYID='.($countryid ?: $ccountryid);
		}
		if (!empty($_POST['SHOW_COMPATRIOTS'])) {
			if (false === ($compatriots = have_number($_POST, 'COMPATRIOTS'))) {
				$compatriots = false;
			}
			if ($compatriots) {
				if ($_POST['SHOW_COMPATRIOTS'] == 1) { $which = &$selectors; } else { $which = &$filters; }

				require_once '_geo.inc';
				$tstpartyids = memcached_simple_hash(['going','user'], '
					SELECT PARTYID,COUNT(*) AS CNT
					FROM going
					JOIN user USING (USERID)
					WHERE PARTYID IN ('.$partyidstr.')
					  AND user.COUNTRYID='.$ccountryid.'
					GROUP BY PARTYID',
													 TEN_MINUTES
				);
				if ($tstpartyids === false) {
					if ($usestore) {
						return false;
					}
					bail(500);
				}
				$okpartyids = array(0);
				asort($tstpartyids);
				foreach ($tstpartyids as $tstpartyid => $tstcnt) {
					if ($tstcnt >= $compatriots) {
						$okpartyids[] = $tstpartyid;
					}
				}
				$which[] = 'party.PARTYID IN ('.implode(',',$okpartyids).')';
			}
		}
		if (!empty($_POST['SHOW_VISITORS'])) {
			if (false === ($visitors = have_number($_POST, 'VISITORS'))) {
				$visitors = false;
			}
			if ($visitors) {
				if ($_POST['SHOW_VISITORS'] == 1)
					$which = &$selectors;
				else	$which = &$filters;

				$tstpartyids = memcached_simple_hash(['going','fbcounters'], '
					SELECT PARTYID,COUNT(*) + COALESCE((
						SELECT MAX(ATTENDING)
						FROM fbcounters
						JOIN fbid USING (FBID)
						WHERE ELEMENT="party"
						  AND ID=PARTYID),0)
					FROM party
					LEFT JOIN going USING (PARTYID)
					WHERE PARTYID IN ('.$partyidstr.')
					GROUP BY PARTYID',
													 TEN_MINUTES
				);
				if ($tstpartyids === false) {
					if ($usestore) {
						return false;
					}
					bail(500);
				}
				$okpartyids = [0];
				asort($tstpartyids);
				foreach ($tstpartyids as $tstpartyid => $tstcnt) {
					if ($tstcnt >= $visitors) {
						$okpartyids[] = $tstpartyid;
					}
				}
				$which[] = 'party.PARTYID IN ('.implode(',',$okpartyids).')';
			}
		}
		global $currentuser;
		if (!$currentuser) {
			require_once '_currentuser.inc';
			$currentuser = new _currentuser(TINY_USER | SKIP_SETTINGS);
		}
		if (have_user()) {
			if (!empty($_POST['SHOW_FAVOURITES'])) {
				if ($_POST['SHOW_FAVOURITES'] == 1) { $which = &$selectors; } else { $which = &$filters; }
				require_once '_favourite.inc';
				$favs = get_favourites();
				if ($favs === false) {
					if ($usestore) {
						return false;
					}
					bail(500);
				}
				$subwhich = array();
				if (isset($favs['location'])) {
					$subwhich[] = 'party.LOCATIONID IN ('.implodekeys(',',$favs['location']).')';
				}
				if (isset($favs['organization'])) {
					$joins[] = 'LEFT JOIN connect ON MAINTYPE="party" AND MAINID=party.PARTYID AND ASSOCTYPE="organization" AND ASSOCID IN ('.implodekeys(',',$favs['organization']).')';
					$subwhich[] = 'NOT ISNULL(ASSOCID)';
				}
				if (isset($favs['artist'])) {
					$joins[] = 'LEFT JOIN lineup ON lineup.PARTYID=party.PARTYID AND lineup.ARTISTID IN ('.implodekeys(',',$favs['artist']).')';
					$subwhich[] = 'NOT ISNULL(lineup.PARTYID)';
				}
				if ($subwhich) {
					$which[] = '('.implode(' OR ',$subwhich).')';
				}
			}
			foreach (array(0,1) as $maybe) {
				$extra = $maybe ? '_MAYBE' : null;
				if (!empty($_POST[$showpart = 'SHOW_BUDDIES'.$extra])
				&&	have_buddies()
				) {
					require_once '_buddies.inc';
					$buddies = _buddies_full_hash(CURRENTUSERID);
					if ($buddies === false) {
						if ($usestore) {
							return false;
						}
						bail(500);
					}
					if ($buddies) {
						$going = 'g'.$maybe;
						$joins[] = 'LEFT JOIN going AS '.$going.' ON '.$going.'.PARTYID=party.PARTYID AND '.$going.'.USERID IN ('.implode(',',$buddies).') AND '.$going.'.MAYBE='.$maybe;
						if ($_POST[$showpart] == 1) { $which = &$selectors; } else { $which = &$filters; }
						$which[] = 'NOT ISNULL('.$going.'.USERID)';

/*						$okpartyidstr = memcached_single('going',$qstr = '
							SELECT GROUP_CONCAT(DISTINCT PARTYID)
							FROM going
							WHERE USERID IN ('.implode(',',$buddies).')
							  AND MAYBE='.$maybe.'
							  AND PARTYID IN ('.$partyidstr.')'
						);
						print_rr($qstr);
						if ($okpartyidstr === false) {
							if ($usestore) { return false; } else bail(500);
						}
						$which[] = 'party.PARTYID IN ('.($okpartyidstr ?: 0).')';*/
					}
				}
			}
			if (!empty($_POST['SHOW_GOING'])
			&&	(require_once '_going.inc')
			) {
				$goings = get_all_going();
				if ($goings === false) {
					if ($usestore) {
						return false;
					}
					bail(500);
				}
				if ($_POST['SHOW_GOING'] == 1) { $which = &$selectors; } else { $which = &$filters; }
				$which[] = $goings ? 'party.PARTYID IN ('.implodekeys(',',$goings).')' : 0;
			}
			if (!empty($_POST['SHOW_GENRES'])
			&&	($gids = db_simpler_array('genre_fan','SELECT GID FROM genre_fan WHERE USERID='.CURRENTUSERID))
			) {
/*				$joins[] = 'LEFT JOIN party_genre ON party_genre.PARTYID=party.PARTYID AND party_genre.GID IN ('.implode(',',$gids).')';
				if ($_POST['SHOW_GENRES'] == 1) { $which = &$selectors; } else { $which = &$filters; }
				$which[] = 'NOT ISNULL(party_genre.PARTYID)';*/

				# if no genre connected to party, use genres of artists

				$gidstr = implode(',',$gids);
				$joins[] = 'LEFT JOIN party_genre ON party_genre.PARTYID=party.PARTYID AND party_genre.GID IN ('.$gidstr.')';

				# NOTE: too slow, find faster solution?
#				$joins[] = 'LEFT JOIN lineup AS find_genre_lineup ON find_genre_lineup.PARTYID=party.PARTYID AND find_genre_lineup.TYPE NOT IN ("mc")';
#				$joins[] = 'LEFT JOIN artist_genre ON artist_genre.ARTISTID=find_genre_lineup.ARTISTID AND artist_genre.GID IN ('.$gidstr.')';
				if ($_POST['SHOW_GENRES'] == 1) { $which = &$selectors; } else { $which = &$filters; }

#				$which[] = '(NOT ISNULL(party_genre.PARTYID) OR (ISNULL((SELECT GID FROM party_genre AS pg WHERE pg.PARTYID=party.PARTYID LIMIT 1)) AND artist_genre.GID))';
				$which[] = 'NOT ISNULL(party_genre.PARTYID)';
			}
		}
		break;
	}
	foreach ($distances as $type) {
		if (!have_number($_POST,$show = 'SHOW_'.$type)) {
			$store[] = $show.'=0';
			continue;
		}
		if (empty($_POST[$show])) {
			continue;
		}
		$matches = null;
		if (!require_something($_POST,$type.'_LATLNG')
		||	!require_regex($_POST,$type.'_LATLNG','"^(\-?\d+(?:\.\d+)?),(\-?\d+(\.\d+)?)$"',$matches)
		||	!($radius = require_idnumber($_POST,$type.'_RADIUS'))
		) {
			if ($usestore) {
				return false;
			}
			mail_log(
				'Something seems to have gone with the partylistfilter', item: get_defined_vars()
			);
			bail(400);
		}
		list(,$lat,$lon) = $matches;

		if ($_POST[$show] == 1) {
			$which = &$selectors;
		} else {
			$which = &$filters;
		}
		ob_start();
		print_rr($matches);
		$original_matches = ob_get_clean();


		$which[] = '(
			NOT ISNULL(
				COALESCE(boarding.BOARDINGID,location.LOCATIONID,city.CITYID)
			)
			AND COALESCE('.distance($lat,$lon,
				'COALESCE(boarding.LATITUDE,location.LATITUDE,city.LATITUDE)',
				'COALESCE(boarding.LONGITUDE,location.LONGITUDE,city.LONGITUDE)',
				$original_matches
			).',-1) BETWEEN 0 AND '.$radius.'
		)';

		$store[] = 'SHOW_'.$type.'='.$_POST[$show].','.$type.'_RADIUS='.$radius;
	}
# 	error_log_r($selectors,'selectors');
#	error_log_r($filters,'filters');
	if (!$selectors && !$filters) {
		return false;
	}
	$visiblepart = '';
#	error_log_r($selectors,'selectors');
	if ($selectors) {
		$visiblepart = '('.implode(' OR ',$selectors).')';
	}
#	error_log_r($filters,'filters');
	if ($filters) {
		if ($visiblepart) {
			$visiblepart .= ' AND ';
		}
		$visiblepart .= implode(' AND ',$filters);
	}
#	error_log($visiblepart,0);
	# NOTE: Unaccepted parties are always included. They just are never shown on the preliminary page, so
	#	hiding or unhiding doesn't do anything
	#
	# FIXME: is GROUP BY needed?
	$postcopy = $_POST;
	unset($postcopy['PARTYIDS']);
	if (false === ($parties = memcached_simple_hash(
		['party','location','boarding','city'],'
		SELECT SQL_NO_CACHE /* '.CURRENTIDENTID.':'.CURRENTUSERID.' */ party.PARTYID,SUM(IF('.$visiblepart.',1,0))
		FROM party
		LEFT JOIN location USING (LOCATIONID)
		LEFT JOIN boarding USING (BOARDINGID)
		LEFT JOIN city ON city.CITYID=COALESCE(boarding.CITYID,location.CITYID,party.CITYID) '.
		($joins ? implode(' ',$joins) :	null).'
		WHERE party.PARTYID IN ('.$partyidstr.')
		GROUP BY party.PARTYID'
	))) {
		if ($usestore) {
			return false;
		}
		bail(500);
	}
	if ($parties) {
		foreach ($parties as $partyid => &$visible) {
			$visible = $visible ? true : false;
		}
		return $parties;
	}
	return false;
}

function show_radius_part($type,$name,$stored,$lat,$lon,$force_limit = false) {
	?><div><?
	?><input type="hidden" name="<?= $type ?>_LATLNG" value="<?
	if ($lat && $lon) {
		echo $lat ?>,<? echo $lon;
	}
	?>"><?
	show_form_part($type,$stored,$force_limit ? 2 : 1,$name,true,null,$force_limit);
	?> <?= __('partyfilter:upto') ?> <?

	?><select class="inpspc" onchange="filter_parties(this.form)" name="<?= $type ?>_RADIUS"><?
	foreach ([10,20,30,40,50,100,150,200,300,400,500] as $radius) {
		?><option<?
		if ($stored
		?	($radius == $stored[$type.'_RADIUS'])
		:	($radius == 100)
		) {
			?> selected="selected"<?
		}
		?> value="<?= $radius ?>"><?= $radius ?></option><?
	}
	?></select><?
	?> <?= __('abbr:kilometer') ?></div><?
}
function show_form_part($type,$stored,$default,$text,$nodiv = false,$swaphide = null,$force_default = false) {
	if (!$nodiv) {
		?><div><?
	}
	$key = 'SHOW_'.$type;
	$selected = $force_default ? $default : ($stored ? $stored[$key] : $default);
	?><select class="sellim" onchange="<?
		?>filter_parties(this.form)<?
		if ($swaphide) {
			$hidekey = 'swaphide_'.uniqid();
			?>;setdisplay('<?= $hidekey ?>',this.selectedIndex)<?
		}
	?>" name="<?= $key ?>"><?
		?><option<?
		if ($force_default && $default) {
			?> disabled="disabled"<?
		}
		?> value="0"></option><?
		?><option<?
		if ($force_default && $default != 1) {
			?> disabled="disabled"<?
		}
		echo $selected == 1 ? ' selected="selected"' : null ?> value="1"><?= __('partylistfilter:extend') ?></option><?

		?><option<?
		if ($force_default && $default != 2) {
			?> disabled="disabled"<?
		}
		echo $selected == 2 ? ' selected="selected"' : null ?> value="2"><?= __('partylistfilter:limit') ?></option><?
	?></select> <?
	echo $text;
	if ($swaphide) {
		?><span id="<?= $hidekey ?>" class="lmrgn<?
		if (!$selected) {
			?> hidden<?
		}
		?>">(<?= $swaphide ?>)</span><?
	}
	if (!$nodiv) {
		?></div><?
	}
}
function get_stored_filters() {
	static $__stored = 0;
	return	$__stored === 0
	?	(	$__stored =
			have_user()
			?	(	db_single_assoc('partyfilter','
					SELECT * FROM partyfilter
					WHERE IDENTID='.CURRENTIDENTID.'
					  AND USERID='.CURRENTUSERID)
				?:	db_single_assoc('partyfilter','
					SELECT * FROM partyfilter
					WHERE USERID='.CURRENTUSERID.'
					ORDER BY MSTAMP DESC
					LIMIT 1')
				?:	db_single_assoc('partyfilter','
					SELECT * FROM partyfilter
					WHERE IDENTID='.CURRENTUSERID.'
					ORDER BY MSTAMP DESC
					LIMIT 1')
			)
			:	(	CURRENTIDENTID
				?	db_single_assoc('partyfilter','
					SELECT * FROM partyfilter
					WHERE IDENTID='.CURRENTIDENTID.'
					ORDER BY MSTAMP DESC
					LIMIT 1')
				:	null
				)
		)
	:	$__stored;
}
