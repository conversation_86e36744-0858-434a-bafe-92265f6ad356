<?php

# ubb uses \x1e<data \x1f separated>k\x06

require_once 'defines/ubb.inc';
require_once '_date.inc';
require_once '_db.inc';
require_once '_helper.inc';
require_once '_memcache.inc';
require_once '_smiley.inc';
require_once '_wrap.inc';
require_once '_url.inc';
require_once '_urltitle.inc';
require_once '_browser.inc';
require_once '_require.inc';
require_once '_settings.inc';
require_once '_hosts.inc';

global $__ubbflags;
$__ubbflags = [UBB_ALL];

function ubbspecialchars(string $input): string {
	return escape_specials($input, end($GLOBALS['__ubbflags']) & UBB_UTF8);
}
function ubb_message(string $priority, string $complete, ?string $message = null): string {
	$flags = end($GLOBALS['__ubbflags']);
	if ( ($flags & UBB_HTML)
	&&	!($flags & UBB_NO_WARNINGS)
	) {
		if ($message) {
			return ubbspecialchars($complete).'<span class="'.$priority.'">{'.$message.'}</span>';
		}
		return '<span class="'.$priority.'">'.ubbspecialchars($complete).'</span>';
	}
	if ($flags & UBB_HTML_ENTITIES) {
		if ($message) {
			return ubbspecialchars("$complete{$message}");
		}
		return ubbspecialchars($complete);
	}
	if ($message) {
		return "$complete{$message}";
	}
	return $complete;
}
function ubb_warning(string $complete, ?string $warning = null): string {
	return ubb_message('warning', $complete, $warning);
}
function ubb_error(string $complete, ?string $message = null): string {
	return ubb_message('error', $complete, $message);
}

function get_ubb_patterns(bool $utf8 = false): string {
	static $__patterns;
	if (!isset($__patterns[$utf8])) {
		$__patterns[$utf8] = /** @lang PhpRegExp */
		"'".
		"\[([a-zA-Z]+)(?:(:?=|\s+)([^]]*))?](.*?)\[/\\1](?(?<=/(?:li|ul|ol)])\r?\n?)".	# UBBT_LONG, 1==TAG,2=SEPARATOR,3=ATTRIBS,4=CONTENT
		'|'.
		'\[([a-zA-Z]+)(:?=|\s)([^]]+)]'.				# UBBT_ELEMENT, 5==TAG,6==SEPARATOR,7=ATTRIBS
		'|'.
		'\[([a-zA-Z]+)]'.								# UBBT_SHORT, 8==TAG
		'|'.
		'(?<=\s|^)\(([a-z]{2,3}|[A-Z]{2,3})\)(?!\w)'.	# 9 === FLAGname
		'|'.
		'&#(\d+);'.										# 10 === entnumber
		"'Ss".($utf8 ? 'u' : '');
	}
	return $__patterns[$utf8];
}

function prepare_ubb(string $input, bool $utf8 = false): ?string {
	if (null !== ($new_input = preg_replace_callback(get_ubb_patterns($utf8), 'preprocess_ubb_tag', $input))) {
		return $new_input;
	}
	preg_failure($input);
	return $input;
}

function preprocess_ubb_tag(array $match): string {
	$attribstr = $separator = $content = '';
	if (isset($match[10])) {
		$type		= UBBT_ELEMENT;
		$tag		= 'ent';
		$attribstr 	= ' id='.$match[10];
	} elseif (isset($match[9])) {
		# countryflag
		if ('dj' === ($lower_short = strtolower($match[9]))) {
			# Not a country
			return "($match[9])";
		}
		if ($lower_short === 'uk') {
			$lower_short = 'gb';
			$match[9] = 'GB';
			$field = 'SHORT';
		} else {
			$field = strlen($lower_short) === 2 ? 'SHORT' : 'SHORTA3';
		}
		if (!memcached_single_int('country', "
			SELECT 1
			FROM country
			WHERE $field = '$lower_short'",
			ONE_DAY)
		) {
			return "($match[9])";
		}
		$alt		= $match[0];
		$type		= UBBT_ELEMENT;
		$tag		= 'flag';
		$separator	= ' ';
		$attribstr	= ' id='.$match[9].' light nowarn alt='.$alt;
	} elseif (isset($match[8])) {
		$type		= UBBT_SHORT;
		$tag		= $match[8];
	} elseif (isset($match[7])) {
		$type		= UBBT_ELEMENT;
		$tag		= $match[5];
		$separator	= $match[6];
		$attribstr	= $match[7];
	} else {
		$type		= UBBT_LONG;
		$tag		= $match[1];
		$separator	= $match[2];
		$attribstr	= $match[3];
		$content	= $match[4];
	}
	return UBB_BEGIN.$match[0].UBB_SEP.$tag.UBB_SEP.$type.UBB_SEP.$separator.UBB_SEP.$attribstr.UBB_SEP.$content.UBB_END;
}

const DUTCH_UBB_TAG = [
	'actie'			=> 'contest',
	'artiest'		=> 'artist',
	'competitie'	=> 'contest',
	'feest'			=> 'party',
	'lid'			=> 'user',
	'land'			=> 'country',
	'locatie'		=> 'location',
	'lokatie'		=> 'location',
	'nieuws'		=> 'news',
	'onderwerp'		=> 'topic',
	'organisatie'	=> 'organization',
	'overtreding'	=> 'offense',
	'recensie'		=> 'review',
	'stad'			=> 'city',
];

function ubb_convert_tag(string $lower_tag): string {
	return	DUTCH_UBB_TAG[$lower_tag]
		??	match ($lower_tag) {
			'org',
			'organisation'		=> 'organization',
			default				=> $lower_tag,
		};
}

function _ubb_parse_attribs(
	string	$tag,
	int		$type,
	string	$attribstr,
	string &$content,
	?string	$sep = null,
): array {
	if (empty($attribstr)) {
		return [];
	}
	global $__ubbflags;
	$flags = end($__ubbflags);
	$utf8 = (bool)($flags & UBB_UTF8);
	$utf8_mod = $utf8 ? 'u' : '';
	$attribs = [];

	switch ($type) {
	case UBBT_LONG:
		switch ($tag) {
		case 'url';
		case 'relurl':
		case 'email':
			$find_attributes = [
				'(newtab|noitalics)'	=> true,
				'(no_utm)'				=> true,
				'(cacheid)'				=> '([\da-f]{34}|[\da-f]{26})'
			];
			if ($flags & UBB_COUNTAD) {
				require_once '_banner.inc';
				$find_attributes['(countad)'] = COUNTAD_FULL_RE;
			}
			foreach ($find_attributes as $attrib => $val) {
				if (preg_match(
					'"^(.*)\s'.$attrib.($val === true ? '' : '='.$val).'(\s.*?|)$"'.$utf8_mod,
					$attribstr,
					$match)
				) {
					$attribs[$match[2]] = $val === true ? true : $match[3];
					$attribstr = mytrim($match[1],utf8: $utf8).' '.mytrim($match[$val === true ? 3 : 4],utf8: $utf8);
				}
			}
			$attribstr = mytrim($attribstr, '"', utf8: $utf8);
			#if (preg_match('!^"(.*?)"$!'.$utf8_mod, $attribstr, $match)) {
			#	$attribstr = $match[1];
			#}
			break;
		}
		switch ($sep) {
		case ':=':
		case '=':
			switch ($tag) {
			case 'language':
				$attribstr = 'lang='.$attribstr;
				break 2;
			case 'hidden':
			case 'info':
			case 'help':
				$attribs['title'] = $attribstr;
				break;
			default:
				$attribs['name'] = $content;
				$content = $attribstr;
			}
			return $attribs;
		default:
			switch ($tag) {
			case 'url':
			case 'relurl':
				# NOTE: Both url and relurl support only the
				#	name attribute. Previously it was supported
				#	to enter a name with spaces but without quotes
				#	so we need to keep supporting that
				if (preg_match('/name=(?:"([^"]*)"|([^"]*))/i'.$utf8_mod, $attribstr, $match)) {
					$attribs['name'] = mytrim(empty($match[2]) ? $match[1] : $match[2],null,$utf8);
					return $attribs;
				}
				break;
			}
		}
		break;
	case UBBT_ELEMENT:
		switch ($sep) {
		case ':=':
		case '=':
			if (!preg_match("'^(\d+)(?:\s+(.*))?$'".$utf8_mod, $attribstr, $match)) {
				$attribs['name'] = $attribstr;
				return $attribs;
			}
			$attribs['id'] = $match[1];
			if (empty($match[2])) {
				return $attribs;
			}
			$attribstr = $match[2];
		}
		break;
	}
	if (preg_match_all('!(?:^|\s+)[a-z_]+(?:=(?:"([^"]+)"|([^\s"]+)))?!'.$utf8_mod, $attribstr, $match)) {
		mail_log('_ubb.inc: do we get here?', get_defined_vars(), error_log: 'DEBUG');
		foreach ($match[1] as $cnt => $key) {
			if (!empty($match[3][$cnt])) {
				$val = $match[3][$cnt];
			} elseif (!empty($match[2][$cnt])) {
				$val = $match[2][$cnt];
			} else {
				$val = true;
			}
			if ($key === 'setinfo') {
				if (isset($attribs[$key])) {
					assert(is_array($attribs[$key])); # Satisfy EA inspection
					/** @noinspection UnsupportedStringOffsetOperationsInspection */
					$attribs[$key][] = $val;
				} else {
					$attribs[$key] = [$val];
				}
			} else {
				$attribs[$key] = $val;
			}
		}
	}
	return $attribs;
}

function process_ubb_tag(array $match): string {
	global $__ubbflags;
	$flags = end($__ubbflags);
	$info = explode(UBB_SEP, $match[1]);
	if (count($info) < 6) {
		if (!isset($_REQUEST['sELEMENT'])
		||	(	$_REQUEST['sELEMENT'] !== 'ticket'
			&&	$_REQUEST['sELEMENT'] !== 'msg'
			)
		) {
			error_log_r($match,'bad ubb match at '.$_SERVER['REQUEST_URI']);
		}
		return ubb_error($match[0]);
	}
	[$complete, $tag, $type, $separator, $attribstr, $content] = explode(UBB_SEP, $match[1]);
	$type = (integer)$type;
	$lowertag = strtolower($tag);
	$attribs = $attribstr ? _ubb_parse_attribs($lowertag, $type, $attribstr, $content, $separator) : [];

	if ($separator === ':=') {
		$attribs['noitalics'] = true;
	}
	$mytrim = $flags & UBB_UTF8 ? 'utf8_mytrim' : 'mytrim';

	$html	 = (bool)($flags & UBB_HTML);
	$htmlent = (bool)($flags & UBB_HTML_ENTITIES);
	$links	 = !($flags & UBB_NO_LINKS);
	$linkalt = (bool)($flags & UBB_NO_LINK_ALT);
	$inline	 = (bool)($flags & UBB_STATIC_SMILEYS);
	$utf8	 = (bool)($flags & UBB_UTF8);
	$utf8_mod= $utf8 ? 'u' : '';

	if ($flags & UBB_OBJECTS) {
		if ($type === UBBT_LONG) {
			switch ($lowertag) {
			case 'img':
				if (!$content) {
					return '';
				}
				$data = preg_match('"^data:image/(.*);base64,(.*)$"'.$utf8_mod, $content, $match);

				if (false !== call_user_func($utf8 ? 'mb_strpos' : 'strpos', $content, 'party.snt.utwente.nl/~partydata/')) {
					$content = str_replace('party.snt.utwente.nl/~partydata/', 'photo.partyflock.nl/', $content);
				}
				if (isset($attribs['unsafe'])
				||	isset($attribs['malware'])
				||	!is_safe_url($content)
				) {
					return ubb_warning($complete, 'dangerous link');
				}
				if (isset($attribs['broken'])) {
					return ubb_warning($complete, 'broken link');
				}
				if (isset($attribs['reqauth'])) {
					return ubb_warning($complete, 'requires authorization');
				}
				if (isset($attribs['needuser'])
				&&	!have_user()
				) {
					return ubb_warning($complete);
				}
				if (isset($attribs['copyrighted'])) {
					return ubb_warning($complete, 'not allowed due to copyright');
				}
				if (str_contains($content, 'partyflock.nl')
				&&	!preg_match('"(partyflock\.nl|10\.10\.10\.102|stage|sandbox(?:too)?).*\.(?:png|jpe?g|gif|bmp|svg|tiff?)$"i'.$utf8_mod, $content)
				) {
					return ubb_warning($complete,'non-image link');
				}
				if (str_contains($content, '%UNIQ%')) {
					$content = str_replace('%UNIQ%', (string)(safe_random_int() << 32 | safe_random_int()), $content);
				}

				$url = $data ? $content : strip_white_space($content, $utf8);
				$src = ubbspecialchars($url);

				require_once '_spider.inc';

				global $__ubbcontext;
				$src_info = null;
				if (isset($attribs['cacheid'])
				&&	preg_match('"^([\da-f]{8})([\da-f]{16}|[\da-f]{24})$"'.$utf8_mod, $attribs['cacheid'], $match)
				) {
					[,$dataid] = $match;
					$src = '/images/cache/'.$attribs['cacheid'];
					$src_info = Eelement_name('source', DONT_ESCAPE).': '.ubbspecialchars($url);

					if (aspect_objects()
					&&	($img = memcached_single_assoc(['includecachemisc'],'
							SELECT WIDTH,HEIGHT
							FROM includecachemeta
							WHERE DATAID=0x'.$dataid
						))
					&&	$img['WIDTH']
					) {
						$attribs['actual_width'] = $img['WIDTH'];
						$attribs['actual_height'] = $img['HEIGHT'];
					}
				} elseif ($__ubbcontext) {
					[$element, $id, $imgs] = $__ubbcontext;
					if (!$imgs) {
						$__ubbcontext[2] = $imgs = memcached_rowuse_hash(['includecache', 'includecacheurl', 'includecachemeta', 'includecachemisc'], "
							SELECT URL,DATAID,URLID,includecacheurl.CRC, 0 + ELEMENT AS ELEMENTID, COPYRIGHT, WIDTH, HEIGHT
							FROM includecache
							JOIN includecacheurl USING (URLID)
							JOIN includecachemeta USING (DATAID)
							LEFT JOIN includecachemisc USING (DATAID, URLID, ELEMENT, ID)
							WHERE DATAID NOT IN (
										  0, /* 0?? */
									 114232, /* flock image 403 */
									1433441, /* flock image 403 new */
									  61730  /* flock image 404 */
								)
							  AND ELEMENT = '$element'
							  AND ID = $id",
							TEN_MINUTES,
							"includecache:$element:$id"
						);
					}

					if (isset($imgs[$url])
					# tmp: (due to cache)
					&&		  $imgs[$url]['ELEMENTID']
					&&	empty($imgs[$url]['COPYRIGHT'])
					) {
						extract($imgs[$url], \EXTR_OVERWRITE);
						$src = '/images/cache/'.($cacheid = sprintf('%08x%08x%08x%02x%08x', $DATAID, $URLID, $CRC, $ELEMENTID, $id));
						if (null !== ($new_complete = preg_replace('"^(\[img.*?)(\].*?\[/img\])$"', '\\1 cacheid='.$cacheid.'\\2', $complete))) {
							$complete = $new_complete;
						} else {
							preg_failure($complete);
							return ubb_warning($complete, 'preg_replace failure');
						}
						$src_info = Eelement_name('source',DONT_ESCAPE).': '.$url;
						if (aspect_objects()
						&&	$imgs[$url]['WIDTH']
						) {
							$attribs['actual_width']  = $imgs[$url]['WIDTH'];
							$attribs['actual_height'] = $imgs[$url]['HEIGHT'];
						}
					}
				}
				if (!ROBOT
				&&	$src_info
				) {
					if (isset($attribs['title'])) {
						$attribs['title'] .= "\n".$src_info;
					} else {
						$attribs['title'] = $src_info;
					}
				}

				fix_partyflock_hosts($url, $html);

				$imgattrs = $classstr = '';

				$styles = new_img_scaler($attribs, $dims);

				if (!$styles) {
					$styles = ['max-width: 100%'];
				}

				if ($attribs) foreach ($attribs as $key => $value) {
					switch ($key) {
					case 'title':
						$imgattrs .= ' title="'.ubbspecialchars($value).'"';
						break;
					case 'vspan':
					case 'hspan':
						if (empty($value) || !is_number($value)) {
							break;
						}
						$imgattrs .= ' '.$key.'="'.ubbspecialchars($value).'"';
						break;
					case 'left':
					case 'right':
					case 'lclr':
					case 'rclr':
					case 'clear':
						$classstr .= ' '.$key;
						break;

					case 'align':
						$value = strtolower($value);
						if ($value !== 'center'
						&&	$value !== 'left'
						&&	$value !== 'right'
						) {
							break;
						}
						$classstr .= ' '.$value;
						break;

					case 'width':
					case 'height':
						if (aspect_objects()) {
							$imgattrs .= ' '.$key.'="'.ubbspecialchars($value).'"';
						}
						break;
					}
				}
				if (!isset($attribs['tracker'])) {
					if (!ROBOT) {
						$imgattrs .= ' alt="'.ubbspecialchars($complete).'"';
					}
				} else {
					$classstr .= ' abs';
				}
				$postfix = null;
				$prefix = null;

				if (!isset($attribs['spoiler'])) {
					return
						$prefix.
						'<img'.
						' class="ubb'.$classstr.'"'.
						$imgattrs.
						($styles ? ' style="'.implode(';',$styles).'"' : null).
						' src="'.$src.'" />'.
						$postfix;
				}

				ob_start();
				$w = $h = 100;
				?><div<?
				?> class="ib spoiler center"<?
				?> onclick="<?
					?>setdisplay(this.nextSibling, true);<?
					?>Pf.removeNode(this)"<?
				?> style="width: <?= $w ?>px; height: <?= $h ?>px; line-height: <?= $h ?>px;">spoiler!</div><?
				echo $prefix;
				?><img<?
				?> loading="lazy"<?
				?> class="hidden ubb<?= $classstr ?>"<?
				echo $imgattrs;
				if ($styles) {
					?> style="<?= implode('; ', $styles) ?>;"<?
				}
				?> src="<?= $src ?>"<?
				?> /><?
				echo $postfix;
				return ob_get_clean();

			case 'music':
				require_once '_embedmusic.inc';
				return embed_music($content, $attribs);

			case 'movie':
			case 'embed':
				$content = replace_shy($content, $utf8);
				require_once '_embedmovie.inc';
				return embed_movie($content, $attribs);
			}
		} elseif ($type === UBBT_ELEMENT) {
			switch ($lowertag) {
			case 'pfimage_small':
			case 'uploadimage_small':
				$size = 'thumb';
			case 'uploadimage':
			case 'pfimage':
				if (!isset($size)) {
					$size = 'regular';
				}
			case 'uploadimage_large':
			case 'pfimage_large':	return ubb_make_uploadimage($complete, $attribs, $size ?? 'original', utf8: $utf8);
			case 'photo':		return ubb_make_photo($complete, $attribs, utf8: $utf8);
			case 'albumelement':
			case 'albumphoto':	return ubb_make_albumelement($complete, $attribs, utf8: $utf8);
#			case 'albummap':	return ubb_make_albummap($complete, $attribs, utf8: $utf8);
			case 'dump':		return ubb_make_dump($complete, $attribs, utf8: $utf8);
			case 'agenda':		return ubb_include_agenda($complete, $attribs, utf8: $utf8);
			case 'timetable':
			case 'lineup':		return ubb_make_lineup($complete, $attribs, $lowertag, utf8: $utf8);
			case 'icon':		return ubb_make_icon($complete, $attribs, utf8: $utf8);
			}
		}
	}
	if ($flags & UBB_SPECIAL) {
		if ($type === UBBT_LONG) {
			static $__hiddenid = 0;
			static $__hiddendone = 0;
			switch ($lowertag) {
			case 'info':
			case 'help':
				if (!isset($attribs['title'])) {
					return ubb_warning($complete,__('ubb:error:missing_title_LINE', $utf8 ? RETURN_UTF8 : 0));
				}
				return '<span class="help" title="'.ubbspecialchars($attribs['title']).'">'.make_html($content, deeper: true).'</span>';
			case 'ignore':
				$portions = isset($attribs['view_admin']) ? explode(',',$attribs['view_admin']) : null;
				if (have_admin($portions)) {
					ob_start();
					require_once '_bubble.inc';
					$bubble = new bubble(HELP_BUBBLE);
					$bubble->catcher('');
					$bubble->content('<div class="block bold">view_admin: '.($attribs['view_admin'] ?? 'any').'</div>'.make_html($content, deeper: true));
					$bubble->display_and_cleanup();
					return ob_get_clean();
				}
				return '';
			case 'hidden':
				if (!($flags & UBB_HIDDEN)) {
					return make_html($content, deeper: true);
				}
				require_once '_layout.inc';
				if (empty($attribs['title'])) {
					if (!($hiddenid = have_number($attribs,'id'))) {
						if ($__hiddendone === $__hiddenid) {
							$hiddenid = ++$__hiddenid;
						} else {
							$hiddenid = $__hiddendone = $__hiddenid;
						}
					}
					$title = null;
				} else {
					$title = $attribs['title'];
					$hiddenid = $__hiddendone = ++$__hiddenid;
				}
				return	(	$title
					?	'<span onclick="'.
							"unhide('hidden_$hiddenid');".
							"hideobj(this)".
						'" class="unhidelink">'.
						make_html($title).
						'</span>'
					:	null
					).	'<span id="hidden_'.$hiddenid.'" class="hidden">'.make_html($content, deeper: true).'</span>';
			case 'visible':
				if (!($flags & UBB_HIDDEN)) {
					return make_html($content, deeper: true);
				}
				if (!($hiddenid = have_number($attribs,'id'))) {
					if ($__hiddendone === $__hiddenid) {
						$hiddenid = ++$__hiddenid;
					} else {
						$hiddenid = $__hiddendone = $__hiddenid;
					}
				}
				return	'<span onclick="'.
						"swapdisplay('hidden_$hiddenid');".(
							!isset($attribs['keep'])
						?	"hideobj(this)"
						:	null).
					'" class="unhidelink">'.
					make_html($content, deeper: true).
					'</span>';
			case 'fieldset':
				ob_start();
				?><fieldset<?
				if (!isset($attribs['fw'])) {
					?> class="ib"<?
				}
				?>><?
				if (isset($attribs['legend'])) {
					?><legend><?= ubbspecialchars($attribs['legend']) ?></legend><?
				}
				if (null === ($new_content = preg_replace('"\[/legend\]\r?\n"i', '[/legend]', $content))) {
					preg_failure($content);
					ob_end_clean();
					return ubb_warning($complete, 'preg_replace failure');
				}
				$content = $new_content;
				echo make_html($mytrim($content), null, null, 0, true);
				?></fieldset><?
				return ob_get_clean();

			case 'legend':
				return '<legend>'.make_html($content, deeper: true).'</legend>';
			}
		}
	}
	switch ($type) {
	case UBBT_ELEMENT:
		switch ($lowertag) {
		case 'ent':
			if ($id = have_idnumber($attribs, 'id')) {
				$htmlpart = '&#'.$id.';';
				$res = $html || $htmlent ? $htmlpart : html_entity_decode($htmlpart, ENT_HTML5, $utf8 ? 'UTF-8' : 'Windows-1252');
				return $res;
			}
			if (isset($attribs['name'])
			&&	preg_match('"^0?x([a-f\d]+)$"i', $attribs['name'], $match)
			) {
				$htmlpart = '&#x'.$match[1].';';
				return $html ? $htmlpart : html_entity_decode($htmlpart, ENT_HTML5, $utf8 ? 'UTF-8' : 'Windows-1252');
			}
			return ubb_warning($complete);

		case 'flag':
			require_once '_require.inc';
			if ($id = have_number($attribs,'id')) {
				if (!($info = memcached_single_array('country','SELECT COUNTRYID, SHORT FROM country WHERE COUNTRYID='.$id))) {
					return	$info === false
						?	ubb_error($complete)
						:	ubb_warning($complete,__('country:error:nonexistent_LINE', $utf8 ? RETURN_UTF8 : 0, ['ID' => $id]));
				}
				[$countryid, $short] = $info;
			} else {
				if (isset($attribs['name'])) {
					$info = explode(' ', $attribs['name'], 2);
					$short = $info[0];
					if (isset($info[1])) {
						$attribs = [$info[1] => true];
					}
				} elseif (isset($attribs['id'])) {
					$short = $attribs['id'];
				} else {
					[$short] = keyval($attribs);
					if (!$short) {
						return ubb_error($complete);
					}
				}
				if (!($countryid = memcached_single('country','SELECT COUNTRYID FROM country WHERE SHORT="'.addslashes($short).'"',HALF_HOUR))) {
					return	isset($attribs['nowarn'])
						?	$complete
						:	ubb_warning($complete,__('country:error:nonexistend_shortcode_LINE', $utf8 ? RETURN_UTF8 : 0, ['SHORT' => $short]));
				}
			}
			require_once '_countryflag.inc';
			if ($html || $inline){
				return get_country_flag(
					$countryid,
					isset($attribs['light']) ? 'light' : null,
					alt: !empty($attribs['alt']) ? escape_specials($attribs['alt']) : null
				);
			}
			return strtoupper($short);

		case 'star':
			if (!$html && !$inline) {
				return '*';
			}
			$type = 'yellow';
			require_once '_star.inc';
			if (isset($attribs['type'])) {
				switch ($attribs['type']) {
				case 'location':		$type = 'purple'; break;
				case 'organisation':
				case 'organization':	$type = 'cyan';	break;
				case 'label':			$type = 'blue'; break;
				case 'stream':			$type = 'green'; break;
				case 'relation':		$type = 'multi'; break;
				case 'music':			$type = 'orange'; break;
				case 'artist':
				case 'simple':			$type = 'yellow'; break;
				}
			} elseif (isset($attribs['color'])) {
				switch ($attribs['color']) {
				case 'purple':
				case 'cyan':
				case 'blue':
				case 'green':
				case 'orange':
				case 'gold':
				case 'white':
				case 'transparent':
				case 'yellow':	$type = $attribs['color']; break;
				}
			}
			return $type === 'multi' ? get_crown() : get_star($type);

		case 'contactstars':
			if (!($userid = have_idnumber($attribs, 'id'))) {
				return ubb_warning($complete, 'invalid id');
			}
			require_once '_contactstars.inc';
			static $__contactstars;
			if (!isset($__contactstars)) {
				$__contactstars = new contactstars($userid);
			} else {
				$__contactstars::load_infos($userid);
			}
			return $__contactstars->get_stars($userid);

		case 'video':
			if (!($videoid = have_idnumber($attribs,'id'))) {
				return ubb_warning($complete,'invalid id');
			}
			if (isset($attribs['embed'])
			||	isset($attribs['movie'])
			||	isset($attribs['show'])
			||	isset($attribs['play'])
			||	isset($attribs['player'])
			) {
				require_once '_embedvideo.inc';
				return embed_video(
					$videoid,
					have_length($attribs,'width') ?: 0,
					have_length($attribs,'height') ?: 0,
					1,
					have_float($attribs,'aspect') ? ['aspect'=>$attribs['aspect']] : null
				);
			}
			break;

		case 'presence':
			if (!$html && !$inline) {
				return '';
			}
			require_once '_presence.inc';
			if (($element = have_element($attribs, 'element', ELEMENTS_WITH_PRESENCES, strict: true))
			||	!($id = have_idnumber($attribs, 'id'))
			) {
				if (!($type = $attribs['type'] ?? null)
				||	false === ($presence_flags = get_presence_flags($type))
				) {
					return ubb_warning($complete, __('ubb:error:presence_misunderstood', $utf8 ? RETURN_UTF8 : 0));
				}
				ob_start();
				show_presence_icon($type, $presence_flags);
				$icon = ob_get_clean();
				if (isset($attribs['includename'])) {
					if (null === ($new_icon = preg_replace('"(<img(?:[^/]*/)?>)"','$1 '.ucfirst($type), $icon))) {
						preg_failure($icon);
						return ubb_warning($complete, 'preg_replace failure');
					}
					$icon = $new_icon;
				}
				return $icon;
			}
			$presences = have_presence($element,$id);
			if ($types = getifset($attribs,'only') ?: getifset($attribs,'type')) {
				$only = array();
				foreach (explode(',',$types) as $type) {
					$only[$type] = true;
					if ($type === 'facebook') {
						$only['facebookpage'] = 'facebookpage';
					}
				}
			} else {
				$only = false;
			}
			$href = get_element_href($element, $id);
			$parts = [];
			if ($only === false
			||	isset($only['partyflock'])
			) {
				$parts[] =
					'<a'.
					' target="_blank"'.
					' title="'.FULL_HOST.$href.'"'.
					' href="'.$href.'">'.
						'<img'.
						' class="presence"'.
						' src="'.get_favicon().'" />'.
						(isset($attribs['includename']) ? ' Partyflock' : null).
					'</a>';
			}
			if ($presences) {
				foreach ($presences as $type => $presence) {
					if (is_array($presence)
					&&	(	$only === false
						||	isset($only[$type])
						)
					) {
						[, $icon] = keyval($presence);
						if (isset($attribs['includename'])) {
							if (null === ($new_icon = preg_replace('"(<img(?:[^/]*/)?>)"','$1 '.ucfirst($type), $icon))) {
								preg_failure($icon);
								return ubb_warning($complete, 'preg_replace failure');
							}
							$icon = $new_icon;
						}
						$parts[] = str_replace(' rel="me"', '', $icon);
					}
				}
			}
			return implode(isset($attribs['lines']) ? '<br />' : ' ',$parts);

		case 'presale':
			# NOTE: this clashes with presale short, make sure to not combine [presale][/presale] with single [presale]
			require_once '_presale.inc';
			if (!($id = have_idnumber($attribs, 'id'))
			||	!($element = have_element($attribs, 'element', ['party']))
			) {
				return ubb_warning($complete);
			}
			return get_ubb_presale_link($element, $id, $attribs, $flags) ?: ubb_warning($complete);

/*		case 'permalink':
			if (!($id = have_idnumber($attribs, 'id'))
			||	!($element = have_something_trim($attribs, 'element') ? $attribs['element'] : null)
			) {
				return ubb_warning($complete);
			}
			require_once '_contactelements.inc';
			require_once '_comment.inc';
			if (!is_comment_table($element)
			&&	!is_contact_element($element)
			) {
				return ubb_warning($complete);
			}
			ob_start();
			$title = element_name($element).' '.$id;
			?><a<?
				?> class="prmlnk"<?
				?> target="_blank"<?
				?> title="<?= $title ?>"<?
				?> rel="bookmark"<?
				?> href="https://partyflock.<?= CURRENTDOMAIN ?>/<?= $element ?>:<?= $id ?>"><?
			echo $title;
			?> <img<?
				?> alt="<?= $title ?>"<?
				?> class="lower icon"<?
				?> src="<?= STATIC_HOST ?>/images/chain<?= is_high_res() ?>.png" /><?
			?></a><?
			return ob_get_clean();*/
		}
		require_once '_require.inc';
		if (!$attribs
		||	!($id = have_idnumber($attribs, 'id'))
		) {
			return $htmlent ? ubbspecialchars($complete) : $complete;
		}
		if (null === ($result = ubb_make_element(ubb_convert_tag($lowertag), $id, $attribs, $complete))) {
			mail_log('ubb_make_element returned null');
		}
		return $result;

	case UBBT_SHORT:
		switch ($lowertag) {
		case 'favicon':			return $html || $inline ? '<img class="presence" data-ubb="'.ubbspecialchars($complete).'" src="'.get_favicon().'" />' : 'Pf';
		case 'star':			require_once '_star.inc';
								return $html || $inline ? get_star('artist') : ($utf8 ? BLACK_STAR : BLACK_STAR_LATIN1);
		case 'clear':			return $html ? '<br style="clear:both">' : "\n";
		case 'clearleft':		return $html ? '<br style="clear:left">' : "\n";
		case 'dot':				return $htmlent ? '&middot;' : ($utf8 ? MIDDLE_DOT : MIDDLE_DOT_LATIN1);
		case 'mail':			return $html ? '<img class="mail" data-ubb="'.ubbspecialchars($complete).'" src="'.STATIC_HOST.'/images/mail'.is_high_res().'.png" />' : 'M';
		case 'shy':				return $htmlent ? SOFT_HYPHEN_ENTITY : ($utf8 ? SOFT_HYPHEN : SOFT_HYPHEN_LATIN1);
		case 'euro':			return $htmlent ? EURO_SIGN_ENTITY : ($utf8 ? EURO_SIGN : EURO_SIGN_LATIN1);
		case 'rarr':			return $htmlent ? '&rarr;' : ($utf8 ? RIGHTWARD_ARROW : '->');
		case 'larr':			return $htmlent ? '&larr;' : ($utf8 ? LEFTWARD_ARROW : '<-');
		case 'newline':			return $htmlent ? '<br />' : "\n";
		case 'theme':			return CURRENTTHEME;
		case 'currentusernick':	require_once '_require.inc';
								if (have_user()) {
									$nick = $GLOBALS['currentuser']->row['NICK'];
									return	$html || $inline
									?	'<span class="currentusernick" title="Iedereen ziet hier zijn of haar eigen naam staan.">'.
										escape_specials($nick).
										'</span>'
									:	(	$htmlent
										?	escape_specials($nick)
										:	(	$utf8
											?	win1252_to_utf8($nick)
											:	$nick
											)
										);
								}
								return element_name('guest');
		case 'hr':				return $html ? '<hr class="ubb slim">' : "-----------------------\n";
		case 'hellip':			return $html ? HORIZONTAL_ELLIPSIS_ENTITY : ($utf8 ? HORIZONTAL_ELLIPSIS : '...');
		case 'partyflocknl':
		case 'partysntutwentenl':
								return 'partyflock.'.CURRENTDOMAIN;
		case 'smileys':			return $html ? smiley_get_overview() : $complete;
		}
		break;
	case UBBT_LONG:
		switch ($lowertag) {
		case 'presale':
			# NOTE: this clashes with presale short, make sure to not combine [presale][/presale] with single [presale]
			if (!($partyid = (int)$content)) {
				return ubb_warning($complete);
			}
			require_once '_presale.inc';
			[$content] = get_generic_presale_link($partyid);
			$lowertag = 'url';

		case 'relurl':
			$relurl = $lowertag === 'relurl';
		case 'url':
			if ($content) {
				$content = str_replace('<wbr>', '', $content);
			}
			if (isset($attribs['url'])) {
				$attribs['name'] = $content;
				$content = $attribs['url'];
			}
			if (!$content) {
				return ubb_error($complete);
			}
			$content = _smiley_reset($content);

			if (isset($attribs['unsafe'])
			||	isset($attribs['malware'])
			||	!is_safe_url($content)
			) {
				return ubb_warning($complete,'dangerous link');
			}
			if (isset($content[2])
			&&	$content[0] === '/'
			) {
				if ($content[1] === '/') {
					$relhost = true;
				} else {
					$relurl = true;
				}
			}
			$classes = [];
			if (empty($attribs['name'])) {
				# don't wrap url most urls, because soft hyphens get copied as spaces or dashes
				fix_no_extensions($content, !empty($relurl), $utf8);
				$content = cleanup_url($content, $utf8);
				$name = escape_specials($content, $utf8);
				$same = true;
				$classes[] = 'forcewrap';
			} else {
				$name = $attribs['name'];
				$same = $content === $name;
				$content = cleanup_url($content, $utf8);
				fix_no_extensions($name, !empty($relurl), $utf8);
				fix_no_extensions($content, !empty($relurl), $utf8);
				$name = make_html($name, $flags, null, 0, true);
			}

			$open = $close = null;
			if (isset($attribs['noitalics'])) {
				$classes[] = 'ns';
			}
			if ($countad = have_something_trim($attribs, 'countad') ? $attribs['countad'] : null) {
				if (!($adlink_href = get_adlink_href_from_countad($countad, $content))) {
					mail_log('could not contruct adlink from countad: '.$countad);
				} else {
					$relurl = 'relurl';
					$attribs['newtab'] = true;
					$content = $adlink_href;
				}
			}
			if (isset($attribs['cacheid'])) {
				require_once '_includecache.inc';
				$url = get_url_from_cacheid($attribs['cacheid']);
				if ($url) {
					$same = false;
					$relurl = 'relurl';
					$content = '/images/cache/'.$attribs['cacheid'];
				} else {
					if (null === ($new_complete = preg_replace('"\s*cacheid=[a-f\d]+"'.$utf8_mod, '', $complete))) {
						preg_failure($complete);
						return ubb_warning($complete, 'preg_replace failure');
					}
					$complete = $new_complete;
				}
			}
			fix_partyflock_hosts($content, $html, $utf8);
			$content = $mytrim($content);

			# always remove utm stuff and cleanup url, but not within ticket section

			if (empty($_REQUEST['sELEMENT'])
			||	(	$_REQUEST['sELEMENT'] !== 'contact'
				&&	$_REQUEST['sELEMENT'] !== 'ticket'
				)
			) {
				if (null === ($new_content = preg_replace('"([\?&])(?:utm_(?:content|source|medium|campaign|referer_info)=[\w\d\-\.]*)"'.$utf8_mod, '$1', $content))) {
					preg_failure($content);
					return ubb_warning($complete, 'preg_replace failure');
				}
				$content = $new_content;
			}

			[$element, $id] = $GLOBALS['__ubbcontext'];

			$dont_add_utm =
					($flags & UBB_DONT_ADD_UTM)
				||	!empty($attribs['no_utm']);

			$site_is_broken = false;

			if ($content
			&&	!contains_partyflock_domain($content, $utf8)
			&&	$content[0] !== '/'	# internal
			&&	$content[0] !== '#'	# internal
			) {
				# only to checked urls for external urls
				require_once '_urlcheck.inc';
				$site_is_broken = site_is_bad($content);

				if (!$dont_add_utm) {
					add_utm($content, $element, $id);
				}
			}
			$classes[]	= $site_is_broken ? 'broken-link' : 'link';
			$target		= $site_is_broken  ? '' : ' target="_blank"';
			$linktag	= $site_is_broken ? 'span' : 'a';

			$classstr = $classes ? ' class="'.implode(' ', $classes).'"' : '';

			require_once '_affiliates.inc';
			require_once '_url.inc';
			return	is_safe_url($content)
			?	(	$html && $links
					?	$open.'<'.$linktag.$classstr.' data-ubb="'.ubbspecialchars(isset($attribs['affiliate']) ? $attribs['name'] : _smiley_reset($complete)).'"'.(
							(!empty($relurl) || 0 === strncasecmp($content,'mms://',6)) && !isset($attribs['newtab']) && !($flags & UBB_NEW_TAB)
						?	''
						:	$target
						).(	empty($relurl) || $same
						?	''
						:	' title="'.ubbspecialchars($content).'" '
						).(	isset($attribs['affiliate'])
						||	$flags & UBB_UGC
						?	' rel="ugc" '
						:	''
						).(	$site_is_broken
						?	''
						:	' href="'.ubbspecialchars(
							make_affiliate_url(
								!empty($relurl) ? $content : strip_white_space($content, $utf8),
								$element,$id,[
									'DONT_REPLACE_UTM' => $dont_add_utm
								]
							)
							).'"'
						).'>'.$name.'</'.$linktag.'>'.$close
					:	(	$same
						?	$content
						:	$name.($linkalt ? ' ('.$content.')' : null)
						)
				)
			:	(	$html && $links
				?	$open.'<span class="help warning" title="'.Eelement_name('possibly_dangerous_link').($same ? null : ': '.ubbspecialchars($content)).'">'.$name.'</span>'.$close
				:	Eelement_name('possibly_dangerous_link').': '.($same ? $name : $name.' ('.$content.')')
				);

		case 'email':
			if ($content) {
				$content = str_replace('<wbr>', '', $content);
			}
			if (!$content) {
				return ubb_error($complete);
			}
			$content = _smiley_reset($content);
			$classes = [];

			if (empty($attribs['name'])) {
				# don't wrap url most urls, because soft hyphens get copied as spaces or dashes
				$name = escape_specials($content, $utf8);
				$same = true;
				$classes[] = 'forcewrap';
			} else {
				$name = $attribs['name'];
				$same = $content === $name;
				$name = make_html($name, $flags, null, 0, true);
			}
			$open = $close = null;
			if (isset($attribs['noitalics'])) {
				$classes[] = 'ns';
			}
			$content = $mytrim($content);

			$classstr = $classes ? ' class="'.implode(' ', $classes).'"' : '';

			return	$html && $links
			?	'<a class="emailink" href="'._make_illegible('mailto:'.$content).'"'.($same ? null : ' title="'.ubbspecialchars($content).'"').'>'.$name.'</a>'
			:	(	$same
				?	($htmlent ? ' &lt;'.$content.'&gt;' : ' <'.$content.'>')
				:	$name.($linkalt ? ($htmlent ? ' &lt;'.$content.'&gt;' : ' <'.$content.'>') : '')
				)
			;

		case 'dj':
			static $__dj_warned = false;
			if (!$__dj_warned
			&&	have_admin()
			) {
				$__dj_warned = true;
				warning('[dj=] is inmiddels vervangen door [artist=]');
			}
			$lowertag = 'artist';
			break;

		case 'header':
			if (!$html) {
				return make_html($content, deeper: true);
			}
			return '<div'.(
				isset($attribs['id'])
			?	' id="'.ubbspecialchars($attribs['id']).'"'
			:	null
			).	' class="'.(
				isset($attribs['right'])
				?	'right '
				:	(	isset($attribs['center'])
					?	'center '
					:	null
					)
				).(
					isset($attribs['inline'])
				?	'ib '
				:	'ngmrgn '
				).
				'header ubbheader">'.make_html($content, deeper: true).'</div>';
		case 'noubb':
			$tmp = str_replace('[',"\x01",$content);
			$tmp = make_html($tmp,null,null,0,true);
			return str_replace("\x01",'[',$tmp);

		case 'tt':
			if (str_contains($content, "\t")) {
				$newcontent = '';
				$tabsize = $attribs ? (have_idnumber($attribs, 'tabsize') ?: 8) : 8;
				foreach (explode("\n", $content) as $line) {
					while (false !== ($tabpos = ($utf8 ? mb_strpos(...) : strpos(...))($line, "\t"))) {
						$start = ($utf8 ? mb_substr(...) : substr(...))($line, 0, $tabpos);
						$tab = str_repeat(' ', $tabsize - $tabpos % $tabsize);
						$end = ($utf8 ? mb_substr(...) : substr(...))($line, $tabpos + 1);
						$line = $start.$tab.$end;
					}
					if ($newcontent) {
						$newcontent .= "\n";
					}
					$newcontent .= $line;
				}
				$content = $newcontent;
			}
			$bordered  = isset($attribs['bordered'])  ? 'bordered '   : '';
			$allselect = isset($attribs['allselect']) ? 'all-select ' : '';
			return	!$html
			?	make_html($content, deeper: true)
			:	'<span class="'.$allselect.$bordered.'tt">'.
					make_html(
						_smiley_reset($content),
						($utf8 ? UBB_UTF8 : 0) | UBB_HTML | UBB_HTML_ENTITIES | UBB_SKIP_NL2BR,
						deeper: true
					).
				'</span>';

		case 'tte':
			return	!$html
			?	make_html($content, deeper: true)
			:	'<span class="tt">'.
					nl2br(
						make_html(
							_smiley_reset($content),
							($utf8 ? UBB_UTF8 : 0) | UBB_HTML | UBB_HTML_ENTITIES | UBB_SKIP_NL2BR,
							deeper: true
						)
					).
				'</span>';

		case 'dlist':
			$dlist = true;
		case 'table':
			$content = $mytrim($content);
			if (!$content) {
				return '';
			}
			if (!$html) {
				return 'TABLE NOT SUPPORTED HERE';
			}
			if (!function_exists('make_row')) {
				function make_row($key,$val,$rightalign) {
					return	$key
					?	'<tr><td class="field '.($rightalign ? 'right' : 'left').' rpad">'.make_html($key,null,null,0,true).
						':</td><td>'.make_html($val,null,null,0,true).
						'</td></tr>'
					:	'<tr><th colspan="2" class="left">'.make_html($val,null,null,0,true).'</th></tr>';
				}
				function make_cells($key,$cells,$rightalign,$skip_first) {
					global $__ubbflags,$__sep;
					$flags = end($__ubbflags);
					$result = '<tr>';
					$nextalign = null;
					$mytrim = $flags & UBB_UTF8 ? 'utf8_mytrim' : 'mytrim';
					foreach ($cells as $ndx => $cell) {
						if (!$ndx && $skip_first) {
							continue;
						}
						$cell = $mytrim($cell);
						if (preg_match('"^'.preg_quote($__sep,'"').'([rc]?)$"',$cell,$match)) {
							switch ($match[1]) {
							case 'r': $nextalign = ' class="right"'; break;
							case 'c': $nextalign = ' class="center"'; break;
							default: $nextalign = null; break;
							}
							continue;
						}
						$result .= '<td'.$nextalign.'>'.make_html($cell,$flags,null,0,true).'</td>';
						$nextalign = null;
					}
					return $result.'</tr>';
				}
				function make_dsc($key,$val,$rightalign) {
					return	'<dt>'.($key ? make_html($key,null,null,0,true) : '').'</dt>'.
						'<dd>'.($val ? make_html($val,null,null,0,true) : '').'</dd>';
				}
			}
			$key = null;
			$val = '';
			$rightalign = isset($attribs['right']);
			$maxkey = 0;
			global $__sep;
			$__sep = getifset($attribs,'separator') ?: null;
			foreach (explode("\n",$content) as $line) {
				$line = trim($line);
				if (!$line) {
					continue;
				}
				if ($__sep) {
					if (false === ($parts = preg_split($s = '"('.preg_quote($__sep,'"').'[rc]?)"',$line,-1,PREG_SPLIT_DELIM_CAPTURE))) {
						preg_failure($line);
						return ubb_warning($complete, 'preg_split failure');
					}
					$pairs[] = [null, $parts];
				} elseif (preg_match('"^\s*'.(isset($dlist) ? '(.*?)(?<!::)' : '([^:]*)').'\s*'.(isset($dlist) ? '::' : ':').'\s*(.*)$"',$line,$match)) {
					if ($match[1]) {
						if ($key) {
							$pairs[] = [$key, $val];
							$maxkey = max($maxkey, strlen($key));
						}
						$key = $mytrim($match[1]);
						$val = $mytrim($match[2]);
					} elseif ($match[2][0] === ':') {
						if ($key) {
							$pairs[] = [$key, $val];
							$maxkey = max($maxkey, strlen($key));
							$val = null;
							$key = null;
						}
						$pairs[] = [null, substr($match[2], 1)];
					} elseif ($match[2]) {
						$val .= "\n".$mytrim($match[2]);
					}
				} else {
					$val .= "\n".$mytrim($line);
				}
			}
			if ($key) {
				$pairs[] = [$key, $val];
				$maxkey = max($maxkey, strlen($key));
			} elseif ($val) {
				$pairs[] = [null, $val];
			}
			$classes = ['ubb' => 'ubb'];
			if (!empty($attribs['class'])) {
				if (false === ($class_parts = preg_split('"[\s,]"',$attribs['class']))) {
					preg_failure($attribs['class']);
					return ubb_warning($complete, 'preg_split failure');
				}
				foreach ($class_parts as $cls) {
					switch ($cls) {
					case 'vtop':
					case 'default':
					case 'fw':
					case 'nocellbrd':
					case 'hpadded':
					case 'between':
					case 'bordered':
					case 'elpad':
					case 'nowrap':
					case 'itempad':
					case 'nowrap':
					case 'hha':
						$classes[$cls] = $cls;
						break;
					}
				}
			}
			if (isset($dlist)) {
				if (isset($attribs['boldterm'])) {
					$classes['boldterm'] = 'boldterm';
				}
				$classses['nomargin'] = 'nomargin';

				$result = '<dl class="'.implode(' ',$classes).'">';
				$make_row = 'make_dsc';
			} elseif ($__sep) {
				$skip_first = true;
				foreach ($pairs as $info) {
					if ($mytrim($info[1][0])) {
						$skip_first = false;
						break;
					}
				}
				$result =
					'<table class="'.implode(' ',$classes).'">';
				$make_row = 'make_cells';
			} else {
				$classes['dens'] = 'dens';
				$classes['vtop'] = 'vtop';
				$classes['nomargin'] = 'nomargin';
				$result = '<table class="'.implode(' ',$classes).'">';
				$make_row = 'make_row';
			}
			if (isset($pairs)) {
				foreach ($pairs as $info) {
					[$key,$val] = $info;
					$result .= $make_row($key,$val,$rightalign,!empty($skip_first));
				}
				return $result.(isset($dlist) ? '</dl>' : '</table>');
			}
			return make_html($mytrim($content),null,null,0,true);
		case 'list':
			if (!$html) {
				return 'LIST NOT SUPPORTED HERE';
			}
			$separators = '-*+';
			if ($utf8) {
				$separators .= EN_DASH.EM_DASH.BULLET.MIDDLE_DOT;
			} else {
				$separators .= MIDDLE_DOT_LATIN1;
			}
			$content = $mytrim($content);
			if (false === ($parts = preg_split('"((?:^|[\r\n]+)\s*(?:['.$separators.']+|\d+[.:x\s]))"ms'.$utf8_mod, $content, -1, PREG_SPLIT_DELIM_CAPTURE))) {
				preg_failure($content);
				return ubb_warning($complete, 'preg_split failure');
			}
			$elem = null;
			$info = [];
			$simple = null;
			$types = [];
			$indent = 1;
			if (isset($attribs['types'])) {
				$types = explode(',',$attribs['types']);
			}
			if (isset($attribs['nobullets'])) {
				$classes['nobullets'] = 'nobullets';
			}
			if (isset($attribs['itempad'])) {
				$classes['itempad'] = 'itempad';
			}
			if (isset($attribs['class'])) {
				if (false === ($class_parts = preg_split('"[\s,]"', $attribs['class']))) {
					preg_failure($attribs['class']);
					return ubb_warning($complete, 'preg_split failure');
				}
				foreach ($class_parts as $part) {
					switch ($part) {
					case 'vtop':
					case 'default':
					case 'fw':
					case 'nocellbrd':
					case 'hpadded':
					case 'between':
					case 'bordered':
					case 'elpad':
					case 'nowrap':
					case 'itempad':
						$classes[$part] = $part;
						break;
					}
				}
			}
			$classstr = isset($classes) ? implode(' ',$classes).' ' : null;
			switch ($attribs['type'] ?? (isset($attribs['numbered']) ? 'numbered' : null)) {
				case 'numbered':
					$elem = 'ol';
				default:
					foreach ($parts as $i => &$part) {
						$part = myltrim($part);
						if (preg_match('"^(?:^|[\r\n]+|)(?:(['.$separators.']+)|(\d+)([\.:x\s]))"ms',$part,$match)) {
							if ($match[1]) {
								$indent = strlen($match[1]);
							}
							if ($simple === null) {
								if ($simple = !isset($match[3])) {
									$info[$i] = $indent;
								} else {
									if (!$elem) {
										$elem = 'ol';
									}
									$info[$i] = $match;
									if ($match[3] !== '.'
									&&	$match[3] !== ' '
									&&	$match[3] !== "\t"
									) {
										$special = true;
									}
								}
							} elseif ($simple) {
								if (!isset($match[3])) {
									$info[$i] = $indent;
								}
							} elseif (isset($match[3])) {
								$info[$i] = $match;
							}
						}
					}
					unset($part);
					if (!$elem) {
						$elem = 'ul';
					}
					break;
			}
			if ($types) {
				$type = array_shift($types);
				$elem = $type === 'numbered' ? 'ol' : 'ul';
			}
			if (isset($special)) {
				$result = '<table class="dens" style="margin:0 0 0 .3em">';
				$elem = 'table';
			} else {
				# inline-list... inline-block makes list full size and 'clears' floats. this workaround is dirty but works for now
#				$attribs['as-block'] = true;
#				$result = '<'.$elem.' class="ubb il'.(isset($attribs['as-block']) ? 'fake' : null).'">';
				$result = '<'.$elem.' class="'.$classstr.'ubb">';
			}
			$currinfo =
			$closeli = null;
			$currdent =
			$indent = 1;
			foreach ($parts as $i => $part) {
				if (isset($info[$i])) {
					$currinfo = $info[$i];
					continue;
				}
				$line = $part;
				if (empty($line)) {
					continue;
				}
				if (!$currinfo || (is_int($currinfo) ? ($indent = $currinfo) : false)) {
					if ($currdent !== $indent) {
						$i = abs($currdent - $indent);
						$close = $currdent > $indent ? '/' : null;
						$open = $close ? null : ' class="ubb"';
						if (!$close) {
							if ($types) {
								$type = array_shift($types);
								$tag = $type === 'numbered' ? 'ol' : 'ul';
							} else {
								$tag = $elem;
							}
						} else {
							$result .= $closeli;
							$closeli = null;
						}
						while ($i--) {
							$result .= '<'.$close.$tag.$open.'>';
						}
						if ($close) {
							$result .= '</li>';
						}
						$currdent = $indent;
					}
					$result .= $closeli.'<li>'.make_html($line,null,null,0,true);
					$closeli = '</li>';
				} elseif (isset($special)) {
					$result .=
						'<tr><td class="right">'.$currinfo[2].
						'</td><td class="left rpad" style="max-width:1em">'.$currinfo[3].
						'</td><td>'.make_html($line,null,null,0,true).
						'</td></tr>';
				} else {
					$result .= '<li value="'.$currinfo[2].'">'.make_html($line,null,null,0,true).'</li>';
				}
			}
			return $result.$closeli.'</'.$elem.'>';
		case 'img':
			# no objects are done, trackers should be skipped
			if (isset($attribs['tracker'])) {
				return '';
			}
			break;
		case 'multilanguage':
			global $languages;

			make_html($mytrim($content),$flags | UBB_STORE_LANGUAGES, deeper: true);
			$result = null;

			$do_titles=  false;
			foreach ($languages as $lang => $info) {
				[$title,$data] = $info;

				$do_titles = $title || $do_titles;
			}

			$nochoice = !$htmlent || (!isset($attribs['choice']) && !isset($attribs['bottom']) && !isset($attribs['top']) && !$do_titles);

			if ($languages) {
				[$lang] = keyval($languages);
				if ($lang !== CURRENTLANGUAGE
				&&	isset($languages[CURRENTLANGUAGE])
				) {
					$tmp = $languages[CURRENTLANGUAGE];
					unset($languages[CURRENTLANGUAGE]);
					$languages = array_merge([CURRENTLANGUAGE => $tmp], $languages);
				}

				$uniq = uniqid('', true);
				$first = true;
				$do_langs = [];
				ob_start();
				foreach ($languages as $lang => [, $data]) {
					$do_langs[] = $lang;

					if (!$html) {
						echo $data;
						break;
					}

					?><div<?
					?> lang="<?= $lang ?>"<?
					?> id="<?= $lang ?>_<?= $uniq ?>"<?
					?> class="inln<?
					if ($first) {
						$first = false;
					} elseif (!$nochoice) {
						?> hidden<?
					}
					?>"><?= $data ?></div><?

					if ($nochoice) {
						break;
					}
				}
				$content = ob_get_clean();

				$bottom = isset($attribs['bottom']);


			if (!$nochoice) {
				ob_start();
				if ($flags = memcached_simple_hash('language','SELECT SHORTCODE,FLAGCOUNTRYID FROM language WHERE SHORTCODE IN ('.stringsimplode(',',$do_langs).')',TEN_MINUTES)) {
					include_js('js/multilang');
					require_once '_countryflag.inc';
					$first = true;
					?><div style="margin-<?= $bottom ? 'top' : 'bottom' ?>:.5em" class="langs"><?
					foreach ($languages as $lang => $info) {
						$countryid = getifset($flags,$lang);
						if (!$countryid) continue;
						[$title] = $info;
						?><div<?
						?> onclick="Pf.unhideLanguage(this,'<?= $lang ?>','<?= $uniq ?>')"<?
						?> class="rmrgn unhideanchor<?
						if (!$do_titles) {
							?> ib<?
						}
						if ($first) {
							?> selected<?
						}
						?>"><?
						show_country_flag($countryid,$first ? null : 'light',false,__('language:'.$lang, $utf8 ? RETURN_UTF8 : 0));
						if ($title) {
							?> <? echo make_html($title,null,null,0,true);
						}
						?></div><?
						$first = false;
					}
					?></div><?
				}
				$selectors = ob_get_clean();
			}

				if ($nochoice) {
					$result = $content;
				} else {
					ob_start();
					?><div class="inln"><?
					if (!$bottom) {
						echo $selectors;
					}
					echo $content;
					if ($bottom) {
						echo $selectors;
					}
					?></div><?
					$result = ob_get_clean();
				}
			}
			$languages = [];
			return $result;
		case 'language':
			if (!($flags & UBB_STORE_LANGUAGES)) {
				return ubb_warning($complete,__('ubb:error:must_be_within_multilanguage_LINE', $utf8 ? RETURN_UTF8 : 0));
			}
			global $languages;
			$found = false;
			$lang = getifset($attribs,'lang');
			foreach (LOCALES as $locale) {
				if ($lang === $locale[0]) {
					$title = getifset($attribs,'title');
					$data = make_html($mytrim($content),null,null,0,true);
					$found = true;
					break;
				}
			}
			if (!$found) {
				$title = null;
				$data = ubb_warning($complete,__('ubb:error:language_not_understood_LINE', $utf8 ? RETURN_UTF8 : 0));
			}
			$languages[$lang] = [$title, $data];
			return '';
		}
		if ($lowertag === 'hilite') {
			$tmphilite = $_SERVER['HILITE'] ?? null;
			unset($_SERVER['HILITE']);
			$content = make_html($mytrim($content), deeper: true);
			if ($tmphilite) {
				$_SERVER['HILITE'] = $tmphilite;
			}
		} else {
			$content = make_html($mytrim($content), deeper: true);
		}

		if (!$html
		&&	(	$lowertag !== 'hilite'
			||	!isset($_SERVER['HILITE'])
			)
		) {
			return $content;
		}

		switch ($lowertag) {
		case 'float':
			ob_start();
			?><div class="ubb <?= isset($attribs['right']) ? 'r' : 'l' ?>"<?
				if ($width = get_css_width_from_attribs($attribs, 'width')) {
					?> style="width:<?= $width ?>"<?
				}
			?>><?
			echo $content;
			?></div><?
			return ob_get_clean();
		case 'over':		return '<span class="overline">'.$content.'</span>';
		case 'strike':
		case 'deleted':
		case 'del':		return '<span class="line-through">'.$content.'</span>';
		case 'lesslight':	return '<span class="light6">'.$content.'</span>';
		case 'light':		return '<span class="light">'.$content.'</span>';
		case 'smallspacer':	return str_replace("<br />\n<br />",'<div class="smallspacer"></div>',$content);
		case 'b':
		case 'v':
		case 'head':		return '<b>'.$content.'</b>';
		case 'warning':
		case 'notice':
		case 'incfee':
		case 'win':
		case 'banned':
		case 'error':		return '<span class="'.$lowertag.'">'.$content.'</span>';
		case 'hilite':		return '<mark class="search-hilite">'.$content.'</mark>';
		case 'buttons':		return '<div class="ubb smenu">'.$content.'</div>';
		case 's':
		case 'i':			return '<i>'.$content.'</i>';
		case 'u':
		case 'o':			return '<span class="underline">'.$content.'</span>';
		case 'sub':			return '<sub>'.$content.'</sub>';
		case 'sup':			return '<sup>'.$content.'</sup>';
		case 'translationkey':
			switch ($content) {
			case 'doc:gallery:info_TEXT':
				return __($content, DO_UBB | DO_NL2BR | DO_CONDITIONAL | ($utf8 ? RETURN_UTF8 : 0));
			}
			return '';

		case 'quote':
		case 'cite':
			if ($attribs
			&&	($id = have_idnumber($attribs,'message'))
			) {
				$element = 'message';
				$attribs['id'] = $attribs['message'];
			} elseif ($id = have_number($attribs,'id')) {
				$element = $attribs['element'] ?? null;
			} else {
				$element = null;
			}
			if (!$element && !$id) {
				switch ($attribs ? have_something($attribs,'type') : null) {
				case 'easy':	$class = 'easy-quote'; break;
				default:		$class = 'quote'; break;
				}
				if (isset($attribs['ib'])) {
					$class .= ' ib';
				}
				return '<div class="'.$class.'">'.$content.'</div>';
			}
			if (!$element) {
				return ubb_warning($complete, __('ubb:error:quote_missing_element_LINE', $utf8 ? RETURN_UTF8 : 0));
			}
			if (!$id) {
				return ubb_warning($complete, __('ubb:error:quote_missing_id_LINE', $utf8 ? RETURN_UTF8 : 0));
			}
			$bad_parent = false;
			$parent_element = null;
			switch ($element) {
				// MESSAGE
				case 'message':
					$parent_element = 'topic';
					$message_info = memcached_single_assoc(['message', 'topic'], '
						SELECT message.CSTAMP,message.USERID,ACCEPTED,FORUMID,MESSAGEID,MSGNO,MSGCNT,message.FLAGS,TOPICID AS PARENTID
						FROM message
						JOIN topic USING (TOPICID)
						WHERE MESSAGEID='.$id,
						3600
					);
					if ($id > 108128779
					&&	!$message_info
					) {
						error_log_r($message_info, 'messagemoved for user '.CURRENTUSERID." and messageid $id @ {$_SERVER['REQUEST_URI']} and input: $content");
/*						$message_info = memcached_single_assoc(
							array('message','topic'),'
							SELECT message.CSTAMP,message.USERID,ACCEPTED,FORUMID,   TOPICID,BODY
							FROM message
							JOIN topic USING (TOPICID)
							JOIN messagemoved ON NEWMESSAGEID=MESSAGEID
							WHERE OLDMESSAGEID='.$id,
							3600
						);
						if ($message_info) {
							error_log_r($message_info,'messagemoved for user '.CURRENTUSERID.' and messageid '.$id.' @ '.$_SERVER['REQUEST_URI'].' and input: '.$input);
						}*/
					}
					break;

				case 'flockmessage':
					$parent_element = 'flocktopic';
					$message_info = memcached_single_assoc(['flockmessage', 'flocktopic', 'flock'], '
						SELECT fm.CSTAMP, fm.USERID, fm.ACCEPTED, flocktopic.FLOCKID, PRIVATE, MESSAGEID, MSGNO, MSGCNT, fm.FLAGS, TOPICID AS PARENTID
						FROM flockmessage AS fm
						JOIN flocktopic USING (TOPICID)
						JOIN flock USING (FLOCKID)
						WHERE MESSAGEID='.$id,
						3600
					);
					break;

				case 'directmessage':
				case 'privatemessage':
					$message_info = memcached_single_assoc('directmessage',"
						SELECT CSTAMP, FROM_USERID AS USERID, TO_USERID
						FROM directmessage
						WHERE MESSAGEID = $id",
						ONE_HOUR
					);
					break;

				case 'contact_ticket_message':
					$parent_element = 'contact_ticket';
					$message_info = memcached_single_assoc('contact_ticket_message',"
						SELECT ctm.CSTAMP, USERID_FROM AS USERID, WITHUSERID, OWNERID, ELEMENT, ID
						FROM contact_ticket_message AS ctm
						JOIN contact_ticket USING (TICKETID)
						WHERE CTMSGID = $id"
					);
					break;

				default:
					require_once '_comment.inc';
					if (is_quotable($element)) {
						$parent_element = is_comment_table($element);
						$finds = [$element => false];
						if ($parent_element === 'albumelement') {
							$finds[$element.'_log'] = true;
						}
						foreach ($finds as $test_element => $bad_parent) {
							if ($message_info = memcached_single_assoc($test_element, "
								SELECT CSTAMP, ACCEPTED, USERID, COMMENTID AS MESSAGEID, MSGNO, MSGCNT, comment.FLAGS, comment.ID AS PARENTID
								FROM $test_element AS comment
								LEFT JOIN commentsinfo ON TYPE = '$parent_element' AND commentsinfo.ID = comment.ID
								WHERE COMMENTID = $id",
								ONE_HOUR
							)) {
								break;
							}
						}
					}
					break;
			}
			require_once '_layout.inc';
			if ($quotelink
			=	!empty($message_info)
			&&	!$bad_parent
			?	link_to_message($element, $parent_element, getifset($message_info, 'PARENTID'), $message_info)
			:	''
			) {
				$quoteopen = '<a class="nb" href="'.$quotelink.'" title="'.__('ubb:quote:go_to_original_message', $utf8 ? RETURN_UTF8 : 0).'">';
				$quoteclose = '</a>';
			} else {
				$quoteopen = $quoteclose = '';
			}
			require_once '_spider.inc';
			$show_arrow = !ROBOT && $quoteopen;
			$r=	empty($message_info)
			?	'<blockquote class="quote">'.
				$content.
				'</blockquote>'
			:
				'<blockquote class="quote'.($message_info['USERID'] === CURRENTUSERID ? ' mine' : '').'"'.($quotelink ? ' cite="'.$quotelink.'"' : '').'>'.
					'<div class="info'.(false && $show_arrow ? ' relative' : '').'">'.

				str_replace(
					['%LINKOPEN%', '%LINKCLOSE%'],
					[$quoteopen, $quoteclose],
					__('ubb:quote:quote_of_LINE', DO_UBB | KEEP_EMPTY_KEYWORDS | ($utf8 ? RETURN_UTF8 : 0), [
						'USERID'	=>	!empty($message_info['FLAGS'])
								&&	(require_once 'defines/post.inc')
								&&	($message_info['FLAGS'] & POST_IS_ANONYMOUS)
								?	0
								:	$message_info['USERID'],
						'DATETIME'	=> _datedaytime_get($message_info['CSTAMP'])
					])
				).
				($show_arrow ?  '<div class="ib nb seemtext r ptr lpad">'.$quoteopen.'&#x25b6;'.$quoteclose.'</div>' : '').
				'</div>'.
				$content.
				'</blockquote>';

			return $r;

		case 'li':
			return "<$lowertag>$content</$lowertag>";

		case 'ul':
		case 'ol':
			return "<$lowertag class=\"ubb\">$content</$lowertag>";

		case 'indent':
			return '<span style="margin: 0 1em;">'.$content.'</span>';

		case 'justify':
		case 'right':
		case 'left':
		case 'centered':
		case 'nowrap':
		case 'center':
			if (isset($attribs['clear'])) {
				$lowertag .= ' clear';
			}
			return "<div class=\"$lowertag\">$content</div>";

		case 'nb':
		case 'colorless':
			return "<span class=\"ib $lowertag\">$content</span>";

		case 'boxrow':
			$classes = array();
			foreach ([
				'center',
				'right',
				'vbottom',
				'vmiddle',
				'vtop',
			] as $class) {
				if (isset($attribs[$class])) {
					$classes[] = $class;
				}
			}
			return '<div class="'.implode(' ',$classes).'">'.$content.'</div>';

		case 'box':
			$styles = [];
			$do_ib = true;
			$classes = [];
			foreach (['centered','r','right-side','center','left','right','vtop','vmiddle'] as $class) {
				if (isset($attribs[$class])) {
					if ($class === 'centered') {
						$do_ib = false;
					}
					$classes[] = $class === 'right-side' ? 'r' : $class;
				}
			}
			if ($do_ib) {
				$classes[] = 'ib';
			}
			if ($width = get_css_width_from_attribs($attribs, 'width')) {
				$styles[] = 'width:'.$width;
			}
			return '<div'.
				($classes ? ' class="'.implode(' ',$classes).'"' : '').
				' style="'.implode(';',$styles).'"><div style="margin:.25em">'.$content.'</div></div>';

		case 'phone':
			return '<a href="tel:'.$content.'">'.(
				isset($attribs['name'])
			?	escape_specials($attribs['name'], $utf8)
			:	$content
			).'</a>';

		case 'large':
			$span = 'span';
			$style = '';
			return '<'.$span.$style.' class="ubb larger">'.$content.'</'.$span.'>';

		case 'small':
			$span = 'span';
#		case 'smallblock':
#			if (!isset($span)) $span = 'div';
			$style =
				!empty($attribs['size']) && preg_match('"^(?:(\d+)(px|%)?|(?:xx?\-)?(?:small|large)|medium|smaller|larger|inherit)$"',$attribs['size'],$match)
			?	' style="font-size:'.(empty($match[1]) || !empty($match[2]) ? $match[0] : ($match[1] > 1 ? $match[1].'px' : $match[1])).'"'
			:	'';
			return '<'.$span.$style.' class="ubb small">'.$content.'</'.$span.'>';

		case 'urlencode':	return urlencode($content);
		default:
			return $complete;
		}
	}
	return ubbspecialchars($complete);
}

function ubb_make_element(
	string	$element,
	int		$id,
	array	$attribs	= [],
	string	$complete	= '',
	bool	$checkonly	= false,
	?string $force_name = null,
	bool	$force_view	= false,
): string|bool|null {
	global $__ubbflags;
	$flags = end($__ubbflags);

	$utf8	 = (bool)($flags & UBB_UTF8);
	$html	 = (bool)($flags & UBB_HTML);
	$htmlent = (bool)($flags & UBB_HTML_ENTITIES);

	require_once '_album.inc';
	require_once '_banner.inc';
	require_once '_combinable.inc';
	require_once '_countryflag.inc';
	require_once '_element_access.inc';
	require_once '_favourite.inc';
	require_once '_itemnames.inc';
	require_once '_poll.inc';
	require_once '_resizy.inc';
	require_once '_star.inc';

	if (false === ($access = $force_view ? true : may_view_element($element, $id))) {
		if (!empty($attribs['name'])) {
			return $attribs['name'];
		}
		return ubb_warning($complete, __('ubb:error:element_inaccessible_LINE', $utf8 ? RETURN_UTF8 : 0));
	}
	if ($access === null) {
		if (isset(COMBINABLE_ELEMENTS[$element])
		&&	$id !== ($newid = get_combined_id($element, $id))
		&&	($result = ubb_make_element($element, $newid, $attribs, $complete))
		) {
			return $result;
		}
		return ubb_warning($complete, __($element.':error:nonexistent_LINE', $utf8 ? RETURN_UTF8 : 0, ['ID' => $id]));
	}

	# FIXME: check accessiblity as is done with relation element (for topics in senate etc)
	if ($flags & UBB_WARN_IF_INACCESSIBLE
	&&	!may_view_element($element, $id, as_unregistered: true)
	) {
		register_warning('ubb:warning:item_in_text_not_generally_accessible_LINE', ($utf8 ? RETURN_UTF8 : 0) | DO_UBB, ['ELEMENT' => $element, 'ID' => $id]);
	}

	$hide_country_flag = 0;
	if ($show_country_flag = isset($attribs['showflag'])) {
		if ($partyid = have_number($attribs,'partyid')) {
			$party = memcached_party_and_stamp($partyid);
			$hide_country_flag = $party['COUNTRYID'];
		}
	}

	if ($supplied_name = $force_name ?: getifset($attribs, 'name')) {
		$name = $supplied_name;
		$name = str_replace("''",'"',$name);
		$name = html_entity_decode($name, ENT_HTML5 | ENT_COMPAT, $utf8 ? 'utf-8' : 'windows-1252');
		if (!$html) {
			return $htmlent ? ubbspecialchars($name) : $name;
		}

	} else switch ($element) {
	case 'lineup':
		return $complete;

	case 'artist':
	case 'organisation':
	case 'organization':
		static $__names;
		if (isset($__names[$element][$id])) {
			$name = $__names[$element][$id];
			break;
		}
		$flags &= ~UBB_SMILEYS;
		global $__ubbcontext;
		[$context_element, $context_id] = $__ubbcontext ?? ['', 0];
		$name = get_name($element, $id, $context_element, $context_id);
		break;

	case 'relation':
		$name = memcached_relation($id);
		break;

	case 'camera':
	case 'camerarequest':
		$name = __C('camerarequest:show_short', ['PARTYID' => $id]);
		break;

	case 'compoentry':
		if (!($info = memcached_single_array('compoentry', '
			SELECT CREATORID, COMPOID
			FROM compoentry
			WHERE COMPOENTRYID='.$id))
		) {
			break;
		}
		[$creator_id, $compo_id] = $info;
		$name = Eelement_name('submission').' #'.$id.
			' ('.element_name('compo')." #$compo_id) ".
			__('alteration:by_user', $utf8 ? RETURN_UTF8 : 0, ['USERID' => $creator_id]);
		break;

	case 'ticket':
		$name = element_name('contact_ticket').' #'.$id;
		break;

	case 'ad':
	case 'newsad':
	case 'invoice':
	case 'offense':
		$name = element_name($element).' #'.$id;
		break;

	case 'guide':
		$name = __C('guide:'.$id.':title_LINE');
		break;

	case 'forum':
		$name = memcached_single($element, 'SELECT NAME FROM '.$element.' WHERE '.strtoupper($element).'ID = '.$id, TEN_MINUTES);
		break;

	case 'banner':
		if ($flags & UBB_NO_LINKS
		||	!isset($attribs['embed'])
		||	!($flags & UBB_HTML)
		) {
			$name = memcached_single($element,'SELECT NAME FROM banner WHERE BANNERID='.$id);
		} else {
			ob_start();
			display_banner($id);
			return ob_get_clean();
		}
		break;
	case 'faq';
		if (db_single('faq','SELECT 1 FROM faq WHERE FAQID='.$id)) {
			$name = __C('faq:'.$id.':question_LINE');
		}
		break;
	case 'photo':
		$name = memcached_single(['image','party'], 'SELECT NAME FROM image JOIN party USING (PARTYID) WHERE IMGID='.$id,TEN_MINUTES);
		if ($name) {
			$name = __('ubb:make_element:photo', $utf8 ? RETURN_UTF8 : 0, [
				'ID'	=> $id,
				'NAME'	=> $name
			]);
		}
		break;

	case 'album':
		$name = __('ubb:make_element:album', $utf8 ? RETURN_UTF8 : 0, [
			'NICK'	=> get_element_title('user', $id)
		]);
		break;

	case 'albumthumb':
	case 'albumsmall':
		$attribs['small'] = true;
	case 'albumelement':
	case 'albumimage':
	case 'albumphoto':
		if ($checkonly) {
			return (bool)db_single_int('albumelement','SELECT 1 FROM albumelement WHERE ALBUMELEMENTID='.$id);
		}
		$attribs['id'] = $id;
		return ubb_make_albumelement($complete, $attribs, $utf8);

	case 'albummap':
		if($checkonly) {
			return	memcached_albummap($id)
				&&	get_albumid_from_mapid($id);
		}
		return ubb_make_albummap($complete, $id, utf8: $utf8);

	case 'poll':
		if (isset($attribs['ask'])) {
			return make_ubbpoll($id,$checkonly);
		}
		$name = get_element_title($element,$id);
		break;

	case 'datetime':
		ob_start();
		_datedaytime_display($id);
		return ob_get_clean();

	case 'date':
		# date string:	  20240529 for 29th of May 2024
		# timestamp:	1716934073 for same date, time optional (use datetime ubb code)

		if ($id > 30000000) {
			ob_start();
			_dateday_display($id);
			return ob_get_clean();
		}

		$year  = (int)($id / 10000);
		$id -= $year * 10000;
		$month = (int)($id / 100);
		$day = $id - $month * 100;

		if (!($stamp = mktime(year: $year, month: $month, day: $day, hour: 0))) {
			break;
		}
		$name = _dateday_get($stamp);
		break;

	case 'virtualtour':
		if (($name = get_element_title($element, $id))
		&&	isset($attribs['embed'])
		) {
			$dw = 640;
			$dh = 400;
			$w = have_idnumber($attribs, 'width');
			$h = have_idnumber($attribs, 'height');
			if (!$w && !$h) {
				$w = $dw;
				$h = $dh;
			} elseif ($w) {
				$h = $w * $dh / $dw;
			} else { // $h
				$w = $h * $dw / $dh;
			}

			if (SMALL_SCREEN) {
				$res = get_resolution();
				if ($res) {
					[, $screen_width, $screen_height] = $res;
					scale($w, $h, $screen_width, $screen_height);
				} else {
					scale($w, $h, 480);
				}
			}

			include_js('js/krpano');

			$uid = uniqid('vt', true);

			ob_start();
			start_resizy($uid, RSZ_DOMREADY);
			$resizy = ob_get_clean();

			ob_start();
			?><div class="block" id="<?= $uid ?>" style="height: <?= $h ?>px; max-width: 800px;"></div><?
			?><script>
				embedpano({
					target:			'<?= $uid ?>',
					width:			'100%',
					height:			'100%',
					allowfullscreen:'true',
					xml:			'/virtualtour/<?= $id ?>.xml<?
						if ($roomid = have_idnumber($attribs, 'room')) {
						 	?>?ROOMID=<? echo $roomid;
						} ?>'
				})
			</script><?
			$base = ob_get_clean();

			return $base.$resizy;
		}
		break;

	case 'user':
		if (invisible_profile($id)) {
			if (false === ($user = memcached_user($id))) {
				return '';
			}
			if (!$user) {
				mail_log("nonexistent user $id", get_defined_vars());
				return '';
			}
			return __('status:'.$user['STATUS'], $utf8 ? RETURN_UTF8 : 1);
		}
		$name = get_element_title($element, $id);
		break;

	case 'party':
		$flags &= ~UBB_SMILEYS;
	default:
		if (isset(USE_URLTITLE[$element])) {
			$name = get_element_title_or_log($element, $id);
		}
		break;
	}
	if (empty($name)
	&&	isset(COMBINABLE_ELEMENTS[$element])
	&&	$id !== ($newid = get_combined_id($element,$id))
	) {
		$result = ubb_make_element($element,$newid,$attribs,$complete);
		if ($result) {
			return $result;
		}
	}
	if ($checkonly) {
		return !empty($name);
	}
	if (!isset($name)) {
		return $htmlent ? ubbspecialchars($complete) : $complete;
	}
	if (!$name) {
		return ubb_error($complete);
	}
	if (!$html) {
		if (isset(USE_UNICODE[$element])) {
			if ($htmlent) {
				return escape_utf8($name);
			}
			if (!($flags & UBB_UTF8)) {
				return utf8_to_win1252($name);
			}
			return $name;
		}
		if ($htmlent) {
			return escape_specials($name);
		}
		if ($flags & UBB_UTF8) {
			return win1252_to_utf8($name);
		}
		return $name;
	}
	if ($supplied_name) {
		$name = ubbspecialchars($name);
	} elseif (isset(USE_UNICODE[$element])) {
		if ($htmlent) {
			$name = escape_utf8($name);
		}
		if (!($flags & UBB_UTF8)) {
			$name = utf8_to_win1252($name);
		}
	} else {
		if ($flags & UBB_UTF8) {
			$name = win1252_to_utf8($name);
		}
		global $__ubbcontext;
		[$context_element,$context_id] = $__ubbcontext ?? ['',0];
		$name = make_html($name,$flags | UBB_NO_WARNINGS,$context_element,$context_id);
	}
	if ($flags & UBB_NO_LINKS) {
		return $name;
	}
	$open = $close = null;
	$classes = ['itemlink'];
	if (isset($attribs['noitalics'])) {
		$classes[] = 'ns';
	}
	$classstr = ' class="'.implode(' ',$classes).'"';

	$country_flag = null;
	if ($show_country_flag) {
		switch ($element) {
		case 'artist':
			$country_flag = '';
			$artist = db_single_assoc('artist','SELECT COUNTRYID,LIVE_COUNTRYID FROM artist WHERE ARTISTID='.$id);
			if (!empty($artist['COUNTRYID'])
			&&	$artist['COUNTRYID'] !== $hide_country_flag
			) {
				$country_flag = ' '.get_country_flag_for_artist($id, $artist['COUNTRYID'], class: 'light');
			}
			if (!empty($artist['LIVE_COUNTRYID'])
			&&	$artist['LIVE_COUNTRYID'] !== $artist['COUNTRYID']
			&&	$artist['LIVE_COUNTRYID'] !== $hide_country_flag
			) {
				$country_flag = ' &rarr; '.get_country_flag_for_artist($id, $artist['LIVE_COUNTRYID'], class: 'light');
			}
			break;
		case 'organization':
		case 'organisation':
			if ($countryid = db_single('organization','SELECT COUNTRYID FROM organization WHERE ORGANIZATIONID='.$id)) {
				$country_flag = ' '.get_country_flag($countryid,'light');
			}
			break;
		}
	}

	if (!have_user()
	||	!($favourites = get_favourites())
	||	!isset($favourites[$element][$id])
	) {
		$show_star = false;
	} else {
		# Only show a favourite star the first time an element is mentioned per context
		static $__starred = [];
		global $__ubbcontext;
		print_rr($__ubbcontext);
		if (!($current_context = $__ubbcontext ?? false)) {
			$show_star = true;
		} else {
			[$context_element, $context_id] = $current_context;
			if ($show_star = !isset($__starred[$context_element][$context_id])) {
				$__starred[$context_element][$context_id] = true;
			}
		}
	}

	$ubb = ' data-ubb="'.ubbspecialchars($complete).'"';

	$flags = end($GLOBALS['__ubbflags']);
	return	$open.(	($flags & UBB_COUNTAD)
		&&	($countad = have_something_trim($attribs, 'countad'))
		?	'<a'.$classstr.$ubb.(ROBOT ? ' rel="sponsored"' : '').' href="'.get_adlink_href_from_countad($attribs['countad'], $element.'='.$id).'">'.$name.'</a>'
		:	(	isset(USE_URLTITLE[$element])
			?	'<a'.$classstr.$ubb.' href="'.get_element_href($element, $id).'">'.$name.'</a>'
			:	'<a'.$classstr.$ubb.' href="/'.$element.'/'.$id.'">'.$name.'</a>'
			)
		).$close.

		($show_star ? '<sup style="transform: scale(.8);"> '.get_star($element).'</sup> ' : '').

		$country_flag;
}

function ubb_make_icon($complete, $attribs, bool $utf8 = false): string {
	$class = 'icon lower';
	$open = null;
	$close = null;
	$extra = null;
	switch (getifset($attribs,'type')) {
	case 'remove':		$class = 'icon wide'; $icon = 'police'; break;
	case 'add':		$icon = 'plus'; break;
	case 'save':
		$class = 'colorless icon lower';
	case 'notify':
	case 'offense':
	case 'website':
	case 'saved':
	case 'karmaup_active':
	case 'karmaup_inactive':
	case 'karmadown_active':
	case 'karmadown_inactive':
				$icon = $attribs['type']; break;
	case 'subupd':		$icon = 'sun_blue'; break;
	case 'golite':
	case 'subnew':		$icon = 'sun'; break;
	case 'godark':		$icon = 'moon'; break;
	case 'share':		$icon = 'earth'; break;

	case 'ticket':		$class = 'mail'; $icon = 'mail'; break;
	case 'ticket_internal':	$class = 'mail'; $icon = 'internal_mail'; break;
	case 'ticket_email':	$class = 'mail'; $icon = 'mail'; $open = '<span class="ib seemtext relative"><div class="abs at">@</div>'; $close = '</span>'; break;
	case 'ticket_outgoing':	$icon = 'outgoing_mail'; break;

	case 'photo':		$icon = 'cam'; break;
	case 'video':		$icon = 'clapboard'; break;

	case 'buddy':		$icon = 'heart'; break;

	case 'insecure':	$class = 'light lower icon';
	case 'secure':		$icon = 'lock'; break;

	case 'permalink':	$icon = 'chain'; break;

	case 'itunes':
		$class = 'zoomover';
		$icon = 'itunes';
		ob_start();
		?> alt="<?= $title = __('action:view_on_site', $utf8 ? RETURN_UTF8 : 0, ['NAME' => 'iTunes']) ?>"<?
		?> title="<?= $title ?>"<?
		?> width="110"<?
		?> height="40"<?
		$extra = ob_get_clean();
		break;

	case 'bol.com':
		$class = 'zoomover';
		$icon = LITE ? 'bolcom' : 'bolcomd';
		ob_start();
		?> alt="<?= $title = __('action:view_on_site', $utf8 ? RETURN_UTF8 : 0, ['NAME' => 'Bol.com']) ?>"<?
		?> title="<?= $title ?>"<?
		?> width="160"<?
		?> height="40"<?
		$extra = ob_get_clean();
		break;

	case 'website':
	case 'site':
	case 'url':
		$icon = 'website';
		$open = '<span class="siteimg">';
		$close = '</span>';
		$class = null;
		break;
	}
	return	isset($icon)
	?	$open.'<img'.($class ? ' class="'.$class.'"' : null).$extra.' data-ubb="'.ubbspecialchars($complete).'" src="'.STATIC_HOST.'/images/'.$icon.is_high_res().'.png" />'.$close
	:	ubb_warning($complete,__('ubb:error:unknown_icon_LINE', $utf8 ? RETURN_UTF8 : 0, ['TYPE' => $attribs['type'] ?? '']));
}

function ubb_make_lineup(string $complete, array $attribs, string $type, bool $utf8 = false): string {
	if (!($id = have_number($attribs,'id'))) {
		return ubb_warning($complete,__('ubb:error:invalid_id_LINE', $utf8 ? RETURN_UTF8 : 0, ['ID' => $id]));
	}
	require_once 'defines/party.inc';
	require_once '_lineup.inc';

	if (isset($attribs['all'])) {
		$single_party = single_lineup_multi_days($id,$cparties);
		if ($single_party) {
			$id = $single_party;
		}
	}
	if (!($party = memcached_single_assoc(['party', 'location', 'boarding', 'city', 'lineupneedupdate'],'
		SELECT	TIMETABLE_PSTAMP, LINEUP_PSTAMP, STAMP, STAMP_TZI, DURATION_SECS, city.COUNTRYID, party.USERID, TIMEZONE, FLAGS,
				(SELECT STAMP FROM lineupneedupdate WHERE lineupneedupdate.PARTYID = party.PARTYID) AS LINEUP_NEEDUPDATE
		FROM party
		LEFT JOIN location USING (LOCATIONID)
		LEFT JOIN boarding USING (BOARDINGID)
		LEFT JOIN city ON city.CITYID = COALESCE(boarding.CITYID, location.CITYID, party.CITYID)
		WHERE PARTYID = '.$id))
	) {
		return $complete;
	}
	$party['PARTYID'] = $id;
	ob_start();
	if ($width = get_css_width_from_attribs($attribs, 'width')) {
		if ($width === '100%') {
			?><div class="ib fw"><?
		} else {
			?><div class="ib<?
			?>" style="<?
			if (!str_ends_with($width, '%')) {
				?>width: 100%; max-<?
			}
			?>width: <?= $width ?>;<?
			?>"><?
		}
	}
	if ($type === 'lineup') {
		$attribs['notimes'] = true;
	}
	lineup_display($party,false,$attribs ?: [],true);
	if ($width) {
		?></div><?
	}
	return ob_get_clean();
}

function ubb_make_uploadimage($complete,$attribs,$format = 'original', bool $utf8 = false) {
	if (!($upimgid = have_number($attribs,'id'))) {
		return ubb_warning($complete,__('ubb:error:invalid_id_LINE', $utf8 ? RETURN_UTF8 : 0, ['ID' => $upimgid]));
	}
	if ($element = have_something($attribs, 'element')) {
		$img = db_single_assoc(['uploadimagemeta','uploadimage','uploadimage_link'],'
			SELECT UPIMGID, WIDTH, HEIGHT, FILETYPE
			FROM uploadimagemeta
			JOIN uploadimage USING (UPIMGID)
			JOIN uploadimage_link USING (UPIMGID)
			WHERE SIZE = "'.addslashes($format).'"
			  AND TYPE  ="'.addslashes($element).'"
			  AND ID = '.$upimgid
		);
	} else {
		$img = db_single_assoc(['uploadimagemeta','uploadimage'],'
			SELECT UPIMGID,WIDTH,HEIGHT,FILETYPE
			FROM uploadimagemeta
			JOIN uploadimage USING (UPIMGID)
			WHERE SIZE="'.addslashes($format).'"
			  AND UPIMGID='.$upimgid
		);
	}
	if ($img === false) {
		return ubb_error($complete);
	}
	if (!$img) {
		return ubb_warning($complete,__('uploadimage:error:nonexistent_LINE', $utf8 ? RETURN_UTF8 : 0, ['ID' => $upimgid]));
	}
	$upimgid = $img['UPIMGID'];

	$fetch_width  = $img['WIDTH'];
	$fetch_height = $img['HEIGHT'];
	if (is_high_res()) {
		$fetch_width  <<= 1;
		$fetch_height <<= 1;
	}
	ob_start();
	?><img <?
	?> loading="lazy"<?
	?> data-ubb="<?= ubbspecialchars($complete) ?>"<?
	?> width="<?= $img['WIDTH'] ?>"<?
/*	?> height="<?= $img['HEIGHT'] ?>"<? */
	if (!ROBOT) {
		?> alt="<?= ubbspecialchars($complete) ?>"<?
	}
	?> src="<?= IMAGES_HOST ?>/images/upload/<?= $upimgid ?>_<?= $fetch_width ?>x<?= $fetch_height ?>.<?= $img['FILETYPE'] ?>" /><?
	return ob_get_clean();
}

function ubb_make_photo($complete, $attribs, bool $utf8 = false) {
	if (!($id = have_number($attribs,'id'))) {
		return ubb_warning($complete,__('ubb:error:invalid_id_LINE', $utf8 ? RETURN_UTF8 : 0, ['ID' => $id]));
	}
	if (!isset($attribs['size'])) {
		$size = 'small';
	} elseif (!have_element($attribs,'size',array('small','thumb','medium','main'))) {
		return ubb_warning($complete,__('ubb:error:bad_photo_size_LINE', $utf8 ? RETURN_UTF8 : 0, ['HAVE' => $attribs['size'], 'NEED' => 'small,thumb,medium,main']));
	} else {
		$size = $attribs['size'];
	}
	if (!($image = db_single_assoc('image','
		SELECT PARTYID,THUMB_WIDTH,THUMB_HEIGHT,HIDDEN,USERID,WIDTH,HEIGHT,DOUBLE_WIDTH,DOUBLE_HEIGHT
		FROM image
		WHERE IMGID='.$id))
	) {
		if ($image === false) {
			return ubb_error($complete);
		}
		return ubb_warning($complete, __('photo:error:nonexistent_LINE', $utf8 ? RETURN_UTF8 : 0, ['ID' => $id]));
	}
	if ($image['HIDDEN'] === 1
	&&	!have_self_or_admin($image['USERID'],'photo')
	) {
		return ubb_warning($complete, __('status:only_for_admins', $utf8 ? RETURN_UTF8 : 0));
	}
	$classes[] = 'ubb';
	if (isset($attribs['left'])) {
		$classes[] = 'left';
	} elseif (isset($attribs['right'])) {
		$classes[] = 'right';
	}
	foreach (['width','height'] as $key) {
		if (!isset($attribs[$key])) {
			$attribs[$key] = $$key;
		}
	}
	$styles = new_img_scaler($attribs,$dims);

	$prefix = $postfix = null;

	if ($copyright = $attribs['copyright'] ?? null) {
		ob_start();
		?><div class="ib relative"><?
		?><div class="small abs ovbg" style="bottom: .2em; right: .2em;">&copy; <?= escape_utf8($copyright) ?></div><?
		$prefix = ob_get_clean();
		$postfix = '</div>';
	}
	if ($description = $attribs['description'] ?? null) {
		ob_start();
		?><div class="ib relative"><?
		?><div class="small abs ovbg" style="bottom: .2em; left: .2em;"><?= escape_utf8($description) ?></div><?
		$prefix = ob_get_clean();
		$postfix = '</div>';
	}
	if (isset($attribs['clear'])) {
		$classes[] = 'clear';
	}
	ob_start();
	echo $prefix;
	?><a href="<?= get_element_href('photo',$attribs['id']) ?>"><?
	?><img <?
	?> loading="lazy"<?
	?> class="<?= implode(' ',$classes) ?>"<?
	?> style="<?= implode(';',$styles) ?>"<?
	?> data-ubb="<?= ubbspecialchars($complete) ?>"<?
	if (!ROBOT) {
		?> alt="<?= ubbspecialchars($complete) ?>"<?
	}
	?> src="<?= get_photo_url($attribs['id'], match ($size) {
		'small',
		'thumb'		=> 'thumb',
		# 'medium',
		# 'main',
		default		=> 'main',
	}) ?>" /></a><?
	echo $postfix;
	if (isset($attribs['clear'])) {
		?><br style="clear: both;" /><?
	}
	return ob_get_clean();
}

function ubb_make_albummap(string $complete, int $mapid, bool $utf8 = false): string {
	if (!($albummap = memcached_albummap($mapid))) {
		return ubb_warning($complete, __('albummap:error:nonexistent_LINE', $utf8 ? RETURN_UTF8 : 0, ['ID' => $mapid]));
	}
	if (!may_view_element('albummap',$mapid)) {
		return ubb_warning($complete, __('ubb:error:element_inaccessible_LINE', $utf8 ? RETURN_UTF8 : 0));
	}
	require_once '_album.inc';
	if (!($albumid = get_albumid_from_mapid($mapid))) {
		return ubb_error($complete);
	}
	global $__ubbflags;
	$flags = end($__ubbflags);
	return	$flags & UBB_HTML
#	?	'<a href="/albummap/'.$albumid.'/'.$mapid.'">'.($albummap['TITLE'] ? $albummap['TITLE'] : __('attrib:without_title', $utf8 ? RETURN_UTF8 : 0)).'</a>'
	?	get_element_link('albummap',$mapid,get_element_title('albummap',$mapid) ?: __('attrib:without_title', $utf8 ? RETURN_UTF8 : 0))
	:	ubbspecialchars($albummap['TITLE']);
}

function ubb_make_albumelement(string $complete, array $attribs, bool $utf8 = false): string {
	if (!($id = have_number($attribs, 'id'))) {
		return ubb_warning($complete, __('ubb:error:invalid_id_LINE', $utf8 ? RETURN_UTF8 : 0, ['ID' => $id]));
	}
	require_once '_element_access.inc';
	if (!may_view_element('albumelement', $id)) {
		return ubb_warning($complete, __('ubb:error:element_inaccessible_LINE', $utf8 ? RETURN_UTF8 : 0));
	}
	$usethumb = isset($attribs['thumbnail']) || isset($attribs['small']);
	$albumelement = memcached_single_assoc(['albumelement', 'albumimage'], '
		SELECT	VISIBILITY_ELEMENT, ACCEPTED, TITLE, ALBUMID,
			WIDTH, HEIGHT, LEN, FILETYPE, THMBID, albumimagedatameta.DATAID
		FROM albumelement
		JOIN albumimagedatameta ON albumimagedatameta.DATAID='.($usethumb ? 'THMBID' : 'albumelement.DATAID').'
		WHERE ALBUMELEMENTID = '.$id,
		TEN_MINUTES
	);
	if ($albumelement === false) {
		return ubb_error($complete);
	}
	if (!$albumelement) {
		return ubb_warning($complete, __('albumelement:error:nonexistent_LINE', $utf8 ? RETURN_UTF8 : 0, ['ID' => $id]));
	}
	if (!$albumelement['ACCEPTED']) {
		$classes[] = 'unaccepted-element';
	}
	global $__ubbflags;
	$flags = end($__ubbflags);
	if (!($flags & UBB_OBJECTS)) {
		return escape_utf8($albumelement['TITLE']);
	}
	if (isset($attribs['width'])) {
		scale($albumelement['WIDTH'],$albumelement['HEIGHT'],$attribs['width'],0);
	}
	$classes[] = 'ubb';
	if (isset($attribs['left'])) {
		$classes[] = 'left';
	} elseif (isset($attribs['right'])) {
		$classes[] = 'right';
	}
	return	(	isset($attribs['clear'])
			?	'<br style="clear:both">'
			:	null
		).
		(isset($attribs['nolink']) ? null : '<a href="'.get_element_href('albumelement',$id).'">').
		'<img'.(	$albumelement['TITLE']
			?	' alt="'.($title = escape_utf8($albumelement['TITLE'])).'" title="'.$title.'"'
			:	''
		).
		' loading="lazy"'.
		' class="'.implode(' ',$classes).'"'.
		' width="100%"'.
		' style="max-width:'.$albumelement['WIDTH'].'px"'.
		' data-ubb="'.ubbspecialchars($complete).'"'.
		(ROBOT ? null : ' alt="'.ubbspecialchars($complete).'"').
		' src="'.ALBUM_HOST.'/'.$id.
		($usethumb ? 't' : null).
		'_'.crc32($albumelement[$usethumb ? 'THMBID' : 'DATAID'].':'.$id).'.'.$albumelement['FILETYPE'].
		'" />'.
		(	isset($attribs['nolink']) ? '' : '</a>').
		(	isset($attribs['clear']) ? '<br style="clear:both">' : '');
}

function ubb_make_dump(
	string $complete,
	array  $attribs,
	bool   $utf8 = false
): string {
	if (!($id = have_idnumber($attribs, 'id'))) {
		return ubb_warning($complete, __('ubb:error:invalid_id_LINE', $utf8 ? RETURN_UTF8 : 0, ['ID' => $id]));
	}
	if (!($dump = db_single_assoc('dumpmeta','
		SELECT FILENAME, WIDTH, HEIGHT, MIMETYPE, ADMINONLY, USERID, DATAID2x
		FROM dumpmeta
		WHERE DUMPID = '.$id))
	) {
		if($dump === false){
			return ubb_error($complete);
		}
		return ubb_warning($complete, __('dump:error:nonexistent_LINE', $utf8 ? RETURN_UTF8 : 0, ['ID' => $id]));
	}

	$x2 = is_high_res() && $dump['DATAID2x'] ? '@2x' : '';

	global $__ubbflags;
	$flags = end($__ubbflags);
	$html = $flags & UBB_HTML;

	if ($dump['ADMINONLY'] && !have_admin()) {
		return	ubb_warning($complete, __('status:only_for_admins', $utf8 ? RETURN_UTF8 : 0));
	}
	if (!empty($attribs['name'])) {
		$name = ubbspecialchars($attribs['name']);
	} elseif (!empty($attribs['title'])) {
		$name = ubbspecialchars($attribs['title']);
	} else {
		$name = escape_utf8($dump['FILENAME']);
		if (!isset($attribs['download'])) {
			if (str_starts_with($dump['MIMETYPE'], 'image/')) {
				if (isset($attribs['left'])) {
					$classes[] = 'left';
				} elseif (isset($attribs['right'])) {
					$classes[] = 'right';
				}
				foreach ($attribs as $key => $val) {
					switch ($key) {
					case 'clear':
					case 'lclr':
					case 'rclr':
						$classes[] = $key;
						break;
					}
				}
				$styles = new_img_scaler($attribs, $dims, $dump);
				$classes[] = 'ubb middle';

				ob_start();
				$copyright   = $attribs['copyright'] ?? null;
				$description = $attribs['description'] ?? null;

				if ($copyright || $description) {
					?><div class="ib relative"<?
					if ($styles) {
						?> style="<?= implode('; ', $styles) ?>;"<?
						$styles = ['max-width: 100%'];
					}
					?>><?
					if ($copyright) {
						?><div class="small abs ovbg" style="bottom: .2em; right: .2em;">&copy; <?= escape_specials($copyright) ?></div><?
					}
					if ($description) {
						?><div class="small abs ovbg" style="bottom: .2em; left: .2em;"><?= escape_specials($description) ?></div><?
					}
				}
				?><img<?
				if ($styles) {
					?> style="<?= implode('; ', $styles) ?>;"<?
				}
				?> loading="lazy"<?
				?> data-ubb="<?= ubbspecialchars($complete) ?>"<?
				?> src="<?= DUMP_HOST ?>/<?= $attribs['id'], $x2 ?>/<?= $name ?>"<?
				if (!ROBOT) {
					?> alt="<?= ubbspecialchars($complete) ?>"<?
				}
				?> class="<?= implode(' ', $classes) ?>" /><?
				if ($copyright || $description) {
					?></div><?
				}
				$image_part = ob_get_clean();
				//$clear_part = isset($attribs['clear']) ? '<br style="clear: both;">' : '';

				if( $html
				&&	CURRENTUSERID === $dump['USERID']) {
					return "<a href=\"/dump/{$attribs['id']}\">$image_part</a>";//$clear_part";
				}
				return $image_part;//.$clear_part;

			} elseif (str_contains($dump['MIMETYPE'], 'application/x-shockwave-flash')) {
				if(ROBOT){
					return '';
				}
				return 'FLASH NOT SUPPORTED';

			} elseif (str_starts_with($dump['MIMETYPE'], 'video/')) {
				$args = [
					'type'	=> 'video',
					'width'	=> have_idnumber($attribs, 'width'),
					'height'=> have_idnumber($attribs, 'height'),
				];
				if ($stillid = have_idnumber($attribs, 'stillid')) {
					# FIXME: seems to not be supported down the source tree anymore

					$still = db_single_assoc('dumpmeta','
						SELECT FILENAME, MIMETYPE, ADMINONLY, USERID, DATAID2x
						FROM dumpmeta
						WHERE DUMPID = '.$stillid
					);
					if ($still === null) {
						register_warning('dump:error:non_existent_LINE', DO_UBB | ($utf8 ? RETURN_UTF8 : 0), ['ID' => $stillid]);
					} elseif ($still) {
						$x2still = $still['DATAID2x'];
						$args['image'] = DUMP_HOST.'/'.$stillid.$x2still.'/'.escape_utf8($still['FILENAME']);
					}
				}
				require_once '_embedmovie.inc';
				return embed_movie(DUMP_HOST."/{$attribs['id']}$x2/$name",$args);
			}
		}
	}
	return	$html
	?	'<a href="'.DUMP_HOST."/{$attribs['id']}$x2/$name?download\">$name</a>"
	:	$name;
}

function ubb_include_agenda(string $complete, array $attribs, bool $utf8 = false): string {
	if (!($element = have_element($attribs, 'element', $ok_elements = ['user', 'location', 'organization', 'artist']))) {
		return ubb_warning($complete, __('ubb:error:invalid_element_LINE', $utf8 ? RETURN_UTF8 : 0, ['HAVE' => $element, 'NEED' => implode(',', $ok_elements)]));
	}
	if (!($id = have_idnumber($attribs,'id'))) {
		return ubb_warning($complete, __('ubb:error:invalid_id_LINE', $utf8 ? RETURN_UTF8 : 0, ['ID' => $id]));
	}
	require_once '_partylist.inc';
	$partylist = new _partylist;
	$startstamp = have_number($attribs, 'startstamp');
	$stopstamp = have_number($attribs, 'stopstamp');
	if ($startstamp
	||	$stopstamp
	) {
		if ($startstamp) {
			$partylist->select_from_startstamp($startstamp);
		}
		if ($stopstamp) {
			$partylist->select_from_stopstamp($stopstamp);
		}
		$partylist->order_chronologically();
	} elseif (isset($attribs['archive'])) {
		$partylist->select_past();
		$partylist->order_reverse_chronologically();
	} else {
		$partylist->select_future();
		$partylist->order_chronologically();
	}
	$partylist->order_chronologically();
	$partylist->show_stars = true;
	$partylist->show_date = true;
	$partylist->show_buddies = true;
	if (!isset($attribs['show_separators'])) {
		$partylist->hide_separators = true;
	}
	if (isset($attribs['show_location'])) {
		$partylist->show_location = true;
	}
	$id = $attribs['id'];
	switch ($attribs['element']) {
	case 'user':		$partylist->user_going($id); break;
	case 'artist':		$partylist->only_artist($id); break;
	case 'organisation':
	case 'organization':	$partylist->by_organization($id); break;
	case 'location':	$partylist->on_location($id); $partylist->hide_flag = true; break;
	}
	if (!$partylist->query()) {
		return ubb_error($complete);
	}
	$width = get_css_width_from_attribs($attribs, 'width') ?: '600px';

	ob_start();
	?><div class="ib" style="text-align:left;<?
	if (!str_ends_with($width, '%')) {
		?>width:100%;max-<?
	}
	?>width:<? echo $width;
	if ($attribs
	&&	have_something($attribs,'height')
	&&	preg_match('"^(\d+)(%)?$"',$attribs['height'],$match)
	) {
		$height = !empty($match[2]) ? $attribs['height'] : $attribs['height'].'px';
		?>;height:<? echo $height;
	}
	?>"><?
	$partylist->nomargin = true;
	$partylist->display();
	?></div><?
	return ob_get_clean();
}

function plain_text(string $input, ?int $flags = null): ?string {
	return $input ? make_html($input, $flags ?? 0) : '';
}

function flat_nolinks(string $input, int $flags = 0): string {
	return $input ? make_html($input, $flags | UBB_HTML_ENTITIES | UBB_NO_LINKS) : '';
}

function flat_with_entities(string $input, ?int $flags = null, ?string $context_element = null, int $context_id = 0): string {
	return $input ? make_html($input, $flags | UBB_HTML_ENTITIES, $context_element, $context_id) : '';
}

function make_all_html(string $input, ?int $flags = null, ?string $context_element = null, int $context_id = 0): string {
	return $input ? make_html($input, $flags | UBB_ALL, $context_element, $context_id) : '';
}

function hilite(string $input, bool $utf8 = false): string {
	if (!isset($_SERVER['HILITE'])) {
		return $input;
	}
	foreach ($_SERVER['HILITE'] as $search) {
		if (null !== ($new_input = preg_replace_callback('"'.$search.'"i'.($utf8 ? 'u' : ''),static fn(array $match): string =>
			preprocess_ubb_tag([
				'[hilite]'.$match[0].'[/hilite]',
				'hilite',
				'',
				'',
				$match[0]
			]),
			$input,
			20))
		) {
			$input = $new_input;
		} else {
			preg_failure($input);
		}
	}
	return $input;
}

function make_html(
	string		$input,
	?int		$argflags	= null,
	?string		$element	= null,
	int			$id			= 0,
	bool		$deeper		= false
): ?string {
	global $__ubbflags, $__ubbcontext;
	if ($argflags !== null) {
		$__ubbflags[] = ($flags = $argflags);
	} else {
		$flags = end($__ubbflags);
	}

	$utf8 = (bool)($flags & UBB_UTF8);
	$utf8_mod = $utf8 ? 'u' : '';

	if ($element) {
	 	$__ubbcontext = [$element, $id, null];
	// 	require_once '_affiliates.inc';
	// 	$input = link_affiliates($input, true, $element, $id, $utf8);
	 }

	if (!$deeper
	&&	($flags & UBB_SMILEYS)
	) {
		# smileys need only one pass, otherwise [quote] will be parsed twice for smileys
		require_once '_smiley.inc';
		$input = _smiley_prepare($input, (bool)($flags & UBB_ANIMATED_SMILEYS), $utf8);
	}

	if ($doubb =
		(	str_contains($input, '[')
		||	str_contains($input, '(')
		||	str_contains($input, '&#'))
	) {
		if (null !== ($new_input =
			 preg_replace_callback('"(?<before>\[(?:/(?<close_tag>d?list|center|header|multilanguage|quote|table|[oud]l)|(?<short_tag>lineup|timetable)[^\]]+)\])[\s\r\n]*"i'.$utf8_mod, fn (array $match): string =>
			 	$match['before'].($match['close_tag'] === 'quote' || $match['close_tag'] === 'multilanguage' ? "\n\n" : "\n"),
			 $input))
		) {
			$input = $new_input;
		} else {
			preg_failure($input);
		}
		if (preg_match($re = "'(?:\r?\n){2,}\.{3,}(?:\r?\n){2,}'s".$utf8_mod, $input)) {
			if (null !== ($new_input = preg_replace($re, "\n[hellip]\n" , $input))) {
				$input = $new_input;
			} else {
				preg_failure($input);
			}
		}
		# replace_permalinks($input, $utf8);

		$input = prepare_ubb($input, $utf8);
	}
	if (isset($_SERVER['HILITE'])
	&&	!($flags & UBB_NO_HILITING)
	) {
		$result = '';
		if (false === ($parts = preg_split(
			'"('.UBB_BEGIN.'.*?'.UBB_END.'|'.SMILEY_START.'.*?'.SMILEY_END.')"Ss'.$utf8_mod,
			$input,
			-1,
			PREG_SPLIT_DELIM_CAPTURE | PREG_SPLIT_NO_EMPTY))
		) {
			preg_failure($input);
		} else {
			foreach ($parts  as $part) {
				# don't hilite inside ubb tags & smileys
				if ($part[0] !== UBB_BEGIN
				&&	!str_starts_with($part, SMILEY_START)
				) {
					$part = hilite($part, $utf8);
					$doubb = true;
				}
				$result .= $part;
			}
		}
		$input = $result;
	}

	$html	 = (bool)($flags & UBB_HTML);
	$htmlent = (bool)($flags & UBB_HTML_ENTITIES);
	$cmbbr	 = (bool)($flags & UBB_COMBINE_2BR);

	if (!($flags & UBB_SKIP_NL2BR)
	&&	($cmbbr || $html)
	&&	str_contains($input, "\n")
	) {
		$do_nl = $cmbbr ? nlnl2cmbr(...) : nl2br(...);
	} else {
		$do_nl = false;
	}
	if ($html || $htmlent) {
		if ($doubb) {
			$result = '';
			if (false === ($parts = preg_split(
				'"('.UBB_BEGIN.'.*?'.UBB_END.')"Ss'.$utf8_mod,
				$input,
				-1,
				PREG_SPLIT_DELIM_CAPTURE | PREG_SPLIT_NO_EMPTY))
			) {
				preg_failure($input);
			} else {
				foreach ($parts as $part) {
					if ($part[0] !== UBB_BEGIN) {
						if ($htmlent) {
							$part = ubbspecialchars($part);
						}
						if ($do_nl) {
							$part = $do_nl($part);
						}
					}
					$result .= $part;
				}
			}
			$input = $result;
		} else {
			if ($htmlent) {
				$input = ubbspecialchars($input);
			}
			if ($do_nl) {
				$input = $do_nl($input);
			}
		}
	}
	if ($doubb) {
		if (null !== ($new_input = preg_replace_callback(
			'"'.UBB_BEGIN.'(.*?)'.UBB_END.'"Ss'.$utf8_mod,
			'process_ubb_tag',
			$input,
			UBB_MAX_TAGS))
		) {
			$input = $new_input;
		} else {
			error_log('new_input === null (2) @ '.$_SERVER['REQUEST_URI'].' and input: '.$input);
			header('Retry-After: '.ONE_HOUR, true, 503);
		}
	}
	# upgrade http calls to partyflock to https
	if (null !== ($new_input = preg_replace(
		'"http://(?<host>(?:[a-zA-Z\d_-]+\.)?partyflock\.nl)"'.$utf8_mod,	// NOSONAR
		'https://$1',
		$input))
	) {
		$input = $new_input;
	} else {
		preg_failure($input);
	}
	if (!$deeper
	&&	($flags & UBB_SMILEYS)
	) {
		$input = _smiley_replace($input, $utf8);
	}
	if ($argflags !== null) {
		array_pop($__ubbflags);
	}
	if ($element) {
		$__ubbcontext = null;
	}
	return $flags & UBB_UTF8 ? $input : replace_shy($input);
}

/*function replace_permalinks(string &$input, bool $utf8 = false): void {
	$utf8_mod = $utf8 ? 'u' : '';
	if (!preg_match('"partyflock\.\w{2}/([a-z_]+):\d+"'.$utf8_mod,$input)) {
		return;
	}
	$input = preg_replace_callback('"https?://(?:vip\.)?partyflock\.\w{2}/(?P<element>[a-z_]+):(?P<id>\d+)\b"sS'.$utf8_mod, function($match) {
		return	isset(USE_URLTITLE[$match['element']])
		?	'[permalink element='.$match['element'].' id='.$match['id'].']'
		:	$match[0];
	}, $input);
}*/

function fix_no_extensions(&$input, $relurl = false, bool $utf8 = false): void {
	$utf8_mod = $utf8 ? 'u' : '';
	if ($relurl
	?	preg_match('"^(.+)(?:/|.html)(\?.+)?$"'.$utf8_mod, $input, $match)
	:	preg_match('"^(https?://(?:[a-z]+\.)?partyflock\.nl/.*)(?:/|.html)(\?.+)?$"'.$utf8_mod, $input, $match)
	) {
		$input = $match[1];
		if (!empty($match[2])) {
			$input .= $match[2];
		}
	}
}
function fix_partyflock_hosts(string &$input, bool $html = true, bool $utf8 = false) : void {
	if (!contains_partyflock_domain($input, $utf8)) {
		return;
	}
	$utf8_mod = $utf8 ? 'u' : '';
	require_once '_servertype.inc';
	if (SERVER_SANDBOX) {
		$search = '"http(s)?:?/+?/(?:(\w+)\.)?partyflock\.\w{2}"i'.$utf8_mod;
		$rep = function ($match) use ($html) {
			$host = null;
			if (!empty($match[2])) {
				switch ($match[2]) {
				case 'album':	$host = ALBUM_HOST; break;
				case 'static':	$host = STATIC_HOST; break;
				case 'dump':	$host = DUMP_HOST; break;
				case 'photo':	$host = PHOTO_HOST; break;
				case 'music':	$host = MUSIC_HOST; break;
				}
			}
			if ($host && $html && $host[0] !== '/') {
				return $host;
			}
			return 'https://'.$_SERVER['HTTP_HOST'].$host;
		};
	} else {
		$search = '"https?(?::?/+?/?)(\w+\.)?(partyflock\.\w{2})(.*)$"i'.$utf8_mod;
		$rep = function ($match) {
			return 'https://'.(SERVER_VIP ? ($match[1] ?: 'vip.') : ($match[1] === 'vip.' ? null : $match[1])).$match[2].$match[3];
		};
	}
	$input = preg_replace_callback($search, $rep, $input);
}

function remove_ubb(string $input, bool $utf8 = false): string {
	if (!$input) {
		return $input;
	}
	return mytrim(preg_replace_callback(get_ubb_patterns($utf8),function($match) use ($utf8) {
		$tag = $match[1];
		$lowertag = strtolower($tag);

		if (isset($match[4])) {
			return mytrim(remove_ubb($match[4], $utf8), null, $utf8);
		} elseif (isset($match[3])) {
			if (($strght = ($match[2] === ':=')) || $match[2] === '=') {
				if (ctype_digit($match[3])) {
					return $match[3];
				}
			} else {
				return mytrim(remove_ubb($match[3], $utf8), null, $utf8);
			}
		}
		return null;
	},
	$input,UBB_MAX_TAGS),
	null,$utf8);
}
function new_img_scaler(&$attribs,&$dims = null,$dump = null) {
	$styles = [];
	if (preg_match('"^(\d+)(%)?$"',$width = mytrim(getifset($attribs,'width') ?? ''),$match)) {
		if (empty($match[2])) {
			$height = have_idnumber($attribs,'height') ?: 0;
			if ($dump) {
				if ($width) {
					if (!$height) {
						$height = round($dump['HEIGHT'] * $width / $dump['WIDTH']);
					}
				} elseif ($height) {
					$width = round($dump['WIDTH'] * $height / $dump['HEIGHT']);
				} else {
					$width = $dump['WIDTH'];
					$height = $dump['HEIGHT'];
				}
			}
		} else {
			$attribs['scale'] = $match[1].'%';
			$width = $attribs['actual_width'] ?? null;
			$height = $attribs['actual_height'] ?? null;
		}
	} elseif (preg_match('"^(\d+)$"',$height = mytrim($attribs['height'] ?? ''),$match)) {
		$width = have_idnumber($attribs,'width') ?: 0;
		if ($dump) {
			if ($height) {
				if (!$width) {
					$width = round($dump['WIDTH'] * $height / $dump['HEIGHT']);
				}
			} elseif ($width) {
				$height = round($dump['HEIGHT'] * $width / $dump['WIDTH']);
			} else {
				$width = $dump['WIDTH'];
				$height = $dump['HEIGHT'];
			}
		}
	} elseif ($dump) {
		$width = $dump['WIDTH'];
		$height = $dump['HEIGHT'];
	}
	if (!aspect_objects()
	&&	(	is_number($width) && $width > 3000
		||	!isset($attribs['width'])
		||	!isset($attribs['scale'])
		)
	) {
		# force scale
		$attribs['scale'] = '100%';
	}
	if ($width && is_number($width) && $height && is_number($height)) {
		$dims = $width.'x'.$height;
	}

	$a_width  = $attribs['actual_width']  ?? null;
	$a_height = $attribs['actual_height'] ?? null;
	require_once '_smallscreen.inc';
	if (isset($attribs['scale'])) {
		if ($width) {
			if (aspect_objects()
			&&	($width  || $a_width)
			&&	($height || $a_height)
			) {
				$styles['height'] = 'height: auto';
				$styles['max-width'] = 'max-width: '.$attribs['scale'];

				$attribs['width']  = $width  ?: $a_width;
				$attribs['height'] = $height ?: $a_height;
			} else {
				$styles['max-width'] = 'max-width: '.$width.'px';
				$width = $attribs['scale'];
			}
		} else {
				$width = $attribs['scale'] === '100%' ? null : $attribs['scale'];
				$styles['max-width'] = 'max-width: 100%';
		}
	} elseif (
		aspect_objects()
	&&	($width  || $a_width)
	&& 	($height || $a_height)
	) {
		$styles['height'] = 'height: auto';
		$styles['max-width'] = 'max-width: 100%';

		$attribs['width']  = $width  ?: $attribs['actual_width'];
		$attribs['height'] = $height ?: $attribs['actual_height'];
	} elseif ($height && !SMALL_SCREEN) {
		$styles['height'] = 'height: '.(is_number($height) ? $height.'px' : $height);
	}
	if ($width) {
		$styles['width'] = 'width: '.(is_number($width) ? $width.'px' : $width);
	}
	return $styles;
}
function get_css_width_from_attribs(?array $attribs, string $field): string {
	if (empty($attribs[$field])
	||	!preg_match('"^(?<number>\d+)(?<width_unit>.+)?$"',$attribs[$field], $width_match)
	) {
		return '';
	}
	if (!empty($width_match['width_unit'])) {
		return $attribs[$field];
	}
	return "$attribs[$field]px";
}
