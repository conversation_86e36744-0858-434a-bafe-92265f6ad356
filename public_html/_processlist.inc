<?php

require_once 'defines/memcache.inc';
require_once 'defines/appic.inc';
require_once 'defines/processlist.inc';
require_once '_customer.inc';
require_once '_flockmod.inc';

set_memory_limit(GIGABYTE);

const SHOW_ALL_ELEMENTS		= SERVER_SANDBOX;	# show even elements that need no update

const LOCK_PERIOD			= HALF_HOUR;
const STATS_PERIOD			= 2 * ONE_YEAR;

const IMPORTANCE_MULTIPLIER = 1;

const DO_FBTOR				= false;	# fb scraping is not working currently
const DO_URLCHECK			= false;	# fb urlchecking is broken currently

function get_importance(
	int $flock_views		= 0,
	int $flock_visitors 	= 0,
	int $appic_views		= 0,
	int $appic_visitors 	= 0,
	int $ticket_hits_appic	= 0,
	int $ticket_hits_flock	= 0,
	int $cstamp				= 0,
	int $stamp				= 0,
	bool $debug			  	= false,
): int {
	if ($debug) {
		error_log_r(get_defined_vars());
	}
	if (isset($_REQUEST['appic'])) {
		if ($debug) {
			error_log('appic calculation');
		}
		$i =	20 * $ticket_hits_appic
			+	10 * $appic_visitors
			+	 1 * $appic_views;

	} elseif (isset($_REQUEST['partyflock'])) {
		if ($debug) {
			error_log('partyflock calculation');
		}
		$i =	20 * $ticket_hits_flock
			+	10 * $flock_visitors
			+	 1 * $flock_views;

	} else {
		if ($debug) {
			error_log('combined calculation');
		}
		$i =	20 * $ticket_hits_flock
			+	20 * $ticket_hits_appic
			+	10 * $appic_visitors
			+	10 * $flock_visitors
			+	 1 * $appic_views
			+	 1 * $flock_views;
	}
	if ($debug) {
		error_log('importance: '.$i);
	}
	return $i;
}

function processlist_main(): void {
	if (!require_admin('party')) {
		no_permission();
		return;
	}

	switch ($_REQUEST['SUBACTION']) {
	default:
		not_found();
		return;

	case null:
		layout_open_box('white');
		layout_box_header(Eelement_plural_name('new_event'));

		foreach ([
			'importance'		=> 'importance',
			# 'total_views'		=> 'total-views',
			# 'total_visitors'	=> 'total-visitors',
		] as $order_by => $url_order_by) {
			?><div class="block"><b><?= element_name("order_on_$order_by") ?></b></div><?

			# List item: DESCRIPTION,  PATH,  LOC/ORG/ALL, ARG

			$lists = [
				[element_plural_name('relation_and_customer'),			'relations','all']
			];
			if (PROCLIST_DO_PRIORITIES) {
				$lists[] =
					[element_plural_name('priority_list'), 				'prios',	'all'];
			}

			$lists = [
				...$lists,
				...[[element_plural_name('contest_organization'),		'contests',	'all'],
					[element_name('everything').' Appic + Partyflock',	'all',		'all'],
				#	[element_name('everything').' Appic',				'all',		'all',		'appic'],
				#	[element_name('everything').' Partyflock',			'all',		'all',		'partyflock'],

				#	['NL + BE Appic',									'all',		'all',		'appic;country=1,6'],
				#	['NL + BE Partyflock',								'all',		'all',		'partyflock;country=1,6'],

				#	[element_name('abroad').' Appic',					'all',		'all',		'appic;notcountry=1,6'],
				#	[element_name('abroad').' Partyflock',				'all',		'all',		'partyflock;notcountry=1,6'],
				]
			];

			?><ul><?
			foreach ($lists as $info) {
				[$desc, $path, $element] = $info;
				$arg = isset($info[3]) ? ";$info[3]" : '';

				?><li><a href="/test/process/<?= $path ?>?order=<?= $url_order_by ?>;type=<?= $element, $arg ?>"><?
				echo $desc;
				if ($element !== 'all') {
					?> <?= MIDDLE_DOT_ENTITY ?> <? echo element_plural_name($element);
				}
				?></a></li><?
			}
			?></ul><?
		}

		layout_close_box();

		layout_open_box('white');
		layout_box_header(__C('action:update_events'));
		?><ul><?
		?><li><a href="/test/process/changed"><?= $date_order_text = ($order_text = element_name('order')).': '.element_name('data') ?></a></li><?
		if (CURRENTUSERID === 2269) {
			?><li><a href="/test/process/changed?skipped"><?= $date_order_text ?> (<?= __('status:skipped') ?>)</a></li><?
		}
		?><li><a href="/test/process/changed?sortby=popular"><?= $order_text ?>: <? element_name('popularity') ?></a></li><?
		?></ul><?
		layout_close_box();

		layout_open_box('white');
		layout_box_header(Eelement_plural_name('more_list'));

/*		if (CURRENTUSERID === 2269) {
			?><li><a href="/test/process/low-resolution-images"><?= element_plural_name('low_resolution_image') ?></a></li><?
			?><li><a href="/test/process/ignored-events"><?= element_plural_name('ignored_event') ?></a></li><?
		}*/
		$menu = [
			[LOCK_PROCLIST_MISSING_PRESALES,	'missing-presales',				element_plural_name('missing_presale_after_sale_start')],
#			[LOCK_PROCLIST_MAYBE_CANCELLED,		'maybe-cancelled',				element_plural_name('maybe_canceled_event')],
			[LOCK_PROCLIST_NO_LOCATION,			'no-location',					element_name('unknown_location')],
			[LOCK_PROCLIST_TOP_ARTISTS,			'top-artists',					element_plural_name('top_artist')],
#			[LOCK_PROCLIST_TOP_ARTISTS,			'top-artists?DOMAIN=be',		element_plural_name('top_artist').' BE'],
			[LOCK_PROCLIST_TOP_ORGANIZATIONS,	'top-organizations',			element_plural_name('top_organization')],
#			[LOCK_PROCLIST_TOP_ORGANIZATIONS,	'top-organizations?DOMAIN=be',	element_plural_name('top_organization').' BE'],
			[LOCK_PROCLIST_TOP_LOCATIONS,		'top-locations',				element_plural_name('top_location')],
#			[LOCK_PROCLIST_TOP_LOCATIONS,		'top-locations?DOMAIN=be',   	element_plural_name('top_location').' BE'],
			[LOCK_PROCLIST_MOVEDS,				'moved',						element_plural_name('moved_event')],
			[LOCK_PROCLIST_POSTPONED,			'postponed',				 	element_plural_name('postponed_event')],
			[LOCK_PROCLIST_BAD_TIMETABLES,		'bad-timetables',				element_plural_name('bad_timetable')],
			[LOCK_PROCLIST_APPIC_CHANGES,		'change-requests',			 	element_plural_name('change_request'),
				db_single('update_from_appic', "SELECT CONCAT(COUNT(DISTINCT APPIC_ID, ACTION), '/', COUNT(*)) FROM update_from_appic WHERE STATUS = 'open'")],
		];
		?><ul><?
		foreach ($menu as $menu_item) {
			[$lock_type, $url_part, $name] = $menu_item;
			show_lockable($lock_type, "/test/process/$url_part", $name, $menu_item[3] ?? null);
		}
		?><li><a href="/test/unaccepted-artists-connected">ongevalideerde artiesten gekoppeld aan gevalideerd evenement</a></li><?
		?><li><a href="/test/lineups-without-type"><?= element_plural_name('lineup_without_type') ?></a></li><?
		?><li><a href="/test/process/no-presale"><?= element_name('no_presale') ?></a></li><?
		?><li><a href="/agenda/placeholders"><?= element_plural_name('placeholder_event') ?></li><?
		?><li><a href="/user/last"><?= element_plural_name('new_user') ?></a></li><?
		?><li><a class="unavailable" href="/agenda/mastergenre"><?= element_plural_name('agenda_genre_lists') ?></a></li><?
		?><li><a href="https://business.facebook.com/latest/inbox/all?asset_id=***********&amp;nav_ref=fb_web_pplus_settings_menu">Facebook &amp; Instagram inbox</a></li><?
		?></ul><?
		layout_close_box();

		layout_open_box();
		layout_box_header(Eelement_plural_name('other_list'));
		?><ul><?
		?><li><a href="/test/process/statistics?days=7"><?= element_plural_name('processing_statistic') ?></a></li><?
		?><li><a href="/agenda/new"><?= element_plural_name('new_event') ?></a></li><?
/*		?><li><a href="/test/process/really-incomplete">line-up incompleet maar wel time-table</a></li><?*/
		?><li><a href="/test/process/priority-list"><?= element_name('priority_list') ?></a></li><?
		?></ul><?
		layout_close_box();
		break;

#	case 'maybe-cancelled':
#		processlist_maybe_cancelled();
#		break;

	case 'no-location':				processlist_no_location(); return;
	case 'postponed':				processlist_postponed(); return;
	case 'moved':					processlist_moved(); return;
	case 'statistics':				processlist_display_statistics(); return;
	case 'priority-list':			processlist_priority_list(); return;
	case 'top-artists':				processlist_top('artist', LOCK_PROCLIST_TOP_ARTISTS); return;
	case 'top-organizations':		processlist_top('organization', LOCK_PROCLIST_TOP_ORGANIZATIONS); return;
	case 'top-locations':			processlist_top('location', LOCK_PROCLIST_TOP_LOCATIONS); return;
	case 'bad-timetables':			processlist_bad_timetables(); return;
	case 'low-resolution-images':	processlist_low_resolution_images(); return;
	case 'missing-presales':		processlist_missing_presales();	return;
	case 'really-incomplete':		processlist_really_incomplete(); return;
//	case 'ignored-events':			processlist_ignored_events(); return;
	case 'no-presale':				processlist_no_presale(); return;
	case 'customer-squares':		processlist_customer_squares(); return;
	case 'change-requests':			require_once '_process_appic_changes.inc'; processlist_change_requests(); return;

	case 'changed':
	case 'relations':
	case 'prios':
	case 'contests':
	case 'all':
		break;
	}

	require_once 'defines/facebook.inc';
	require_once '_complete.inc';

	if (false === ($customer = get_customers())) {
		return;
	}
	assert(is_array($customer)); # Satisfy EA inspection

	$types = [];
	switch ($_REQUEST['SUBACTION']) {
	case 'prios':
		$elems = memcached_same_hash('proclist_priority', '
			SELECT ELEMENT, ID, NULL
			FROM proclist_priority'
		);
		break;

	case 'contests':
		$elems['organization'] = memcached_simple_hash(['contest', 'party', 'connect', 'appic_event'], '
			SELECT DISTINCT ASSOCID, NULL
			FROM contest
			JOIN party USING (PARTYID)
			JOIN connect
			  ON MAINTYPE = "party"
			 AND MAINID = PARTYID
			 AND ASSOCTYPE = "organization"
			WHERE PSTAMP > '.(TODAYSTAMP - ONE_YEAR).'
			  AND ACTIVE
			  AND NOT ISNULL((SELECT 1 FROM contestresponse cr WHERE cr.CONTESTID = contest.CONTESTID AND TOLD = 1 LIMIT 1))
			UNION
			SELECT DISTINCT ASSOCID, NULL
			FROM party
			JOIN appic_event USING (PARTYID)
			JOIN connect
			  ON MAINTYPE = "party"
			 AND MAINID = PARTYID
			 AND ASSOCTYPE = "organization"
			WHERE WINGAME
			  AND STAMP > '.(TODAYSTAMP - ONE_YEAR),
			ONE_DAY
		);
		break;

	case 'relations':
		$parts = [];
		foreach (['appic', 'partyflock'] as $brand) {
			if (isset($customer[$brand]['location'])) {
				$parts[] = /** @lang MariaDB */ '
					SELECT DISTINCT "location", LOCATIONID, "'.$brand.'"
					FROM location
					JOIN fbid
					  ON ELEMENT = "location"
					 AND ID = LOCATIONID
					WHERE LOCATIONID IN ('.implode(', ', $customer[$brand]['location']).')
					  AND FOLLOWUPID = 0
					  AND DEAD = 0';
			}
			if (isset($customer[$brand]['organization'])) {
				$parts[] = /** @lang MariaDB */ '
					SELECT DISTINCT "organization", ORGANIZATIONID, "'.$brand.'"
					FROM organization
					JOIN fbid
					  ON ELEMENT = "organization"
					 AND ID = ORGANIZATIONID
					LEFT JOIN hidden_orgs ON hidden_orgs.ID = ORGANIZATIONID
					WHERE ORGANIZATIONID IN ('.implode(', ', $customer[$brand]['organization']).')
					  AND FOLLOWUPID = 0
					  AND DEADSTAMP = 0
					  AND hidden_orgs.ID IS NULL';
			}
		}
		if (!$parts) {
			return;
		}
		$elems = memcached_same_hash(null, implode(' UNION ', $parts));
		break;

	case 'all':
		$parts = [];
		foreach (['appic','partyflock'] as $brand) {
			if (isset($customer[$brand]['location'])) {
				$parts[] = /** @lang MariaDB */ '
					SELECT DISTINCT "location",LOCATIONID,"'.$brand.'"
					FROM location
					JOIN fbid ON ELEMENT="location" AND ID=LOCATIONID
					WHERE LOCATIONID IN ('.implode(',',$customer[$brand]['location']).')
					  AND FOLLOWUPID = 0
					  AND DEAD = 0';
			}
			if (isset($customer[$brand]['organization'])) {
				$parts[] = /** @lang MariaDB */ "
					SELECT DISTINCT 'organization', ORGANIZATIONID, '$brand'
					FROM organization
					JOIN fbid ON ELEMENT = 'organization' AND ID = ORGANIZATIONID
					LEFT JOIN hidden_orgs ON hidden_orgs.ID = ORGANIZATIONID
					WHERE ORGANIZATIONID IN (".implode(', ',$customer[$brand]['organization']).')
					  AND FOLLOWUPID = 0
					  AND DEADSTAMP = 0
					  AND hidden_orgs.ID IS NULL';
			}
		}

		$elems = memcached_same_hash(null, '
			SELECT DISTINCT "organization", ASSOCID, NULL
			FROM party
			JOIN connect c ON c.MAINTYPE="party" AND c.MAINID=PARTYID AND c.ASSOCTYPE="organization"
			JOIN fbid ON ELEMENT="organization" AND ID=c.ASSOCID
			WHERE STAMP > '.(TODAYSTAMP - STATS_PERIOD).'
			  AND NOT ISNULL((SELECT 1 FROM fbid WHERE ELEMENT="organization" AND ID = ASSOCID LIMIT 1))
			  AND ISNULL((SELECT 1 FROM hidden_orgs WHERE ID = ASSOCID LIMIT 1))

			UNION

			SELECT DISTINCT "location", LOCATIONID, NULL
			FROM party
			JOIN location USING (LOCATIONID)
			JOIN fbid ON ELEMENT="location" AND ID=LOCATIONID
			WHERE LOCATIONID!=0
			  AND FOLLOWUPID = 0
			  AND DEAD = 0
			  AND STAMP > '.(TODAYSTAMP - STATS_PERIOD).'
			  AND NOT ISNULL((SELECT 1 FROM fbid WHERE ELEMENT="location" AND ID=LOCATIONID LIMIT 1)) '.

			($parts ? ' UNION '.implode(' UNION ',$parts) : null),

			TEN_MINUTES
		);
		if ($elems === false) {
			return;
		}
		if (!$elems) {
			break;
		}
		if (false === ($prios = db_same_hash('proclist_priority','
			SELECT ELEMENT, ID
			FROM proclist_priority'))
		) {
			return;
		}
		if ($prios) {
			foreach ($prios as $element => $ids) {
				foreach ($ids as $id) {
					unset($elems[$element][$id]);
				}
			}
		}
		break;
	}

	switch ($_REQUEST['SUBACTION']) {
	case 'relations':
	case 'prios':
	case 'contests':
	case 'all':
		if (empty($elems)
		||	!($element = require_element($_REQUEST, 'type', ['location', 'organization', 'all'], true))
		) {
			return;
		}
		$do = $element === 'all' ? ['location','organization'] : [$element];

		$and_where = null;
		$join = null;

		if ($want_countryid = have_something($_REQUEST,'country')) {
			$want_countryid = explode_to_hash(',',$_REQUEST['country']);

			$and_where .= ' AND city.COUNTRYID IN ('.implode(',',$want_countryid).') ';
		}
		if ($not_countryid = have_something($_REQUEST,'notcountry')) {
			$not_countryid = explode_to_hash(',',$_REQUEST['notcountry']);

			$and_where .= ' AND city.COUNTRYID NOT IN ('.implode(',',$not_countryid).') ';
		}

		if ($want_countryid || $not_countryid) {
			$join = ' LEFT JOIN location USING (LOCATIONID) LEFT JOIN boarding USING (BOARDINGID) LEFT JOIN city ON city.CITYID=COALESCE(boarding.CITYID,location.CITYID,party.CITYID)';
		}

		## UPDATE: only select parties in not_country and want_country!

		$okidlist = [];
		$events = [];
		$filtered_element = [];
		$has_contests = [];
		$new_event_lists = [];
		#$keep = CURRENTUSERID == 2269;
		$keep = false;

		foreach ($do as $element) {
			if (!isset($elems[$element])) {
				continue;
			}
			$idlist = $elems[$element];
			$idlistkeys = array_keys($idlist);

			switch ($element) {
			case 'location':
				foreach ($idlist as $id => $info) {
					$tmp_new_event_lists = memcached_same_hash('party', '
						SELECT	IF(STAMP>'.TODAYSTAMP.',1,0) AS FUTURE, 
							CONCAT("location:",LOCATIONID) AS NDX,
							PARTYID
						FROM party
						'.$join.'
						WHERE LOCATIONID='.$id.'
						'.$and_where.'
						  AND STAMP>'.(TODAYSTAMP - STATS_PERIOD).'
						 HAVING ISNULL((
							SELECT 1
							FROM connect
							JOIN fbid ON ELEMENT = "organization" AND ID = ASSOCID
							WHERE MAINTYPE = "party"
							  AND MAINID = PARTYID
							  AND ASSOCTYPE IN ("organization")
							LIMIT 1
						))',
						ONE_DAY
					);
					if ($tmp_new_event_lists === false) {
						return;
					}
					foreach ($tmp_new_event_lists as $future => $tmp_new_event_list) {
						if (isset($new_event_lists[$future])) {
							$new_event_lists[$future] += $tmp_new_event_list;
						} else {
							$new_event_lists[$future] = $tmp_new_event_list;
						}
					}
				}
				$types[] = element_plural_name('location');
				break;

			case 'organization':
				$has_contests[$element] = memcached_simple_hash(['contest', 'party', 'connect'], '
					SELECT ASSOCID, COUNT(DISTINCT CONTESTID)
					FROM contest
					JOIN party USING (PARTYID)
					JOIN connect
					  ON MAINTYPE = "party"
					 AND MAINID = PARTYID
					 AND ASSOCTYPE = "organization"
					'.$join.'
					WHERE PSTAMP > '.(TODAYSTAMP - ONE_YEAR).'
					'.$and_where.'
					  AND ACTIVE
					  AND NOT ISNULL((SELECT 1 FROM contestresponse cr WHERE cr.CONTESTID = contest.CONTESTID AND TOLD=1 LIMIT 1))
					  AND (CLOSED = 0 OR TOLD = 1)
					GROUP BY ASSOCID',
					ONE_DAY
				);
				foreach ($idlist as $id => $info) {
					$tmp_new_event_lists = memcached_same_hash(['party','connect'],'
						SELECT IF(STAMP > '.TODAYSTAMP.', 1, 0), CONCAT("organization:", MAINID), ASSOCID
						FROM connect
						JOIN party ON PARTYID = ASSOCID
						'.$join.'
						WHERE MAINTYPE IN ("organization")
						  AND MAINID = '.$id.'
						  AND ASSOCTYPE = "party"
						'.$and_where.'
						  AND party.STAMP > '.(TODAYSTAMP - STATS_PERIOD),
						ONE_DAY
					);
					if ($tmp_new_event_lists === false) {
						return;
					}
					if ($tmp_new_event_lists) {
						foreach ($tmp_new_event_lists as $future => $tmp_new_event_list) {
							if (isset($new_event_lists[$future])) {
								$new_event_lists[$future] += $tmp_new_event_list;
							} else {
								$new_event_lists[$future] = $tmp_new_event_list;
							}
						}
					}
				}
				$types[] = element_plural_name('organization');
				break;
			}

			if (empty($new_event_lists)) {
				return;
			}

			foreach ($new_event_lists as $future => $new_events) {
				$events += $new_events;
			}

			$idlistkeystr = implode(', ',$idlistkeys);

			if (DO_URLCHECK) {
				require_once '_urlcheck.inc';
				$bad_fbids= db_boolean_hash(['fbid', 'urlcheck'],'
					SELECT fbid.FBID AS ID
					FROM fbid
					JOIN fbid pfbid ON pfbid.FBID=fbid.FBID AND pfbid.ELEMENT="presence"
					JOIN urlcheck ON urlcheck.ELEMENT="presence" AND urlcheck.ID=pfbid.ID
					WHERE fbid.ELEMENT="'.$element.'"
					  AND fbid.ID IN ('.$idlistkeystr.')
					  AND '.where_url_is_bad()
				);
			} else {
				$bad_fbids = [];
			}
			if (DO_FBTOR) {
				$fbtors = db_simple_hash(['fbtor', 'fbid'], '
					SELECT ID,GROUP_CONCAT(EVENTIDS)
					FROM fbtor
					JOIN fbid USING (FBID)
					WHERE ELEMENT="'.$element.'"
					  AND ID IN ('.$idlistkeystr.')
					  AND NOT IMPOSSIBLE
					  AND CHECKSTAMP>'.(TODAYSTAMP - 3 * ONE_WEEK).'
					GROUP BY ELEMENT,ID'
				);
				$fbchecked = db_simple_hash(['fbtor', 'fbid'],'
					SELECT ID,MIN(CHECKSTAMP)
					FROM fbtor
					JOIN fbid USING (FBID)
					WHERE ELEMENT="'.$element.'"
					  AND ID IN ('.$idlistkeystr.')
					  AND NOT IMPOSSIBLE
					  AND CHECKSTAMP>'.(TODAYSTAMP - 3 * ONE_WEEK).'
					GROUP BY ELEMENT,ID'
				);
			}

			foreach ($idlist as $id => $cust) {
				$key = $element.':'.$id;

				$countryforkey[$key] =
				$countryid = memcached_single('organization',
					$element === 'organization'
					?	'SELECT COUNTRYID FROM organization WHERE ORGANIZATIONID = '.$id
					:	'SELECT COUNTRYID FROM location JOIN city USING (CITYID) WHERE LOCATIONID = '.$id,
					ONE_HOUR
				);

				if ($want_countryid) {
					if (!isset($want_countryid[$countryid])) {
						continue;
					}
				}
				if ($not_countryid) {
					if (isset($not_countryid[$countryid])) {
						continue;
					}
				}

				$args = [
					'future_events'	=> getifset($new_event_lists, 1),
				];
				if (DO_FBTOR) {
					$args['eventidstr'] = getifset($fbtors, $id);
					$args['checkstamp'] = getifset($fbchecked, $id);
				}

				$need_updates[$key] = $need_update = processlist_need_update($element, $id, $args, $filtered, $new_events);
				$have_new[$key] = $new_events;

				if (isset($_REQUEST['includedone'])) {
					$okidlist[$key] = $cust;
					$keep = true;

				} elseif ($need_update) {
					$okidlist[$key] = $cust;

				} elseif (CURRENTUSERID === 2269 && $filtered) {
					$okidlist[$key] = $cust;
					$filtered_element[$element][$id] = $filtered;
				} else {
					unset($events[$key]);
				}
#				static $__i = 0;
#				if (++$__i > 10) {
#					break;
#				}
			}

			$idstr = implodekeys(',', $idlist);

/*			$last_check[$element] = db_simple_hash('proclist_done','
				SELECT ID,STAMP
				FROM proclist_done
				WHERE ELEMENT="'.$element.'"
				  AND ID IN ('.$idstr.')'
			);*/

			if (false === ($locked[$element] = get_locked_items($element))) {
				return;
			}

			$proc_stats[$element] = memcached_rowuse_hash(null,'
				SELECT	ffd.ID,
					COUNT(DISTINCT ffd.EVENTID) AS FOUND,
					COUNT(DISTINCT fbid.FBID) AS ADDED,
					COUNT(DISTINCT fi.FBID) AS IGNORED
				FROM facebook_found_drag AS ffd
				LEFT JOIN fbid
					 ON fbid.FBID = ffd.EVENTID
					AND fbid.ELEMENT = "party"
				LEFT JOIN facebook_ignore AS fi
					 ON fi.FBID = ffd.EVENTID 
					AND (fbid.FBID IS NULL OR fi.FBID != fbid.FBID)
				WHERE ffd.ELEMENT = "'.$element.'"
				  AND ffd.ID IN ('.$idstr.')
				GROUP BY ID'
			);
		}

		if (!$events) {
			# all done
			break;
		}

		$order = getifset($_REQUEST, 'order');
		$order_importance = $order === 'importance';
		$total_importance = [];
		if ($order_importance) {
			$allparties = [];
			$total_events = 0;
			foreach ($events as $key => $parties) {
				$allparties += $parties;
				$total_events = max($total_events, count($parties));
			}
			sort($allparties);
			$allpartyidstr = implode(',',$allparties);

			$appic_views = memcached_simple_hash(['party','appic_event'], '
				SELECT party.PARTYID, UNIQUE_VIEWS
				FROM party
				LEFT JOIN appic_event ON appic_event.PARTYID = '.PARTY_MAIN_ID_INC."
				WHERE UNIQUE_VIEWS IS NOT NULL
				  AND UNIQUE_VIEWS
				  AND party.PARTYID IN ($allpartyidstr)",
				 ONE_DAY
			);
			$appic_views_fb = memcached_simple_hash(['fbid','appic_event'],'
				SELECT ID,UNIQUE_VIEWS
				FROM fbid
				JOIN appic_event USING (FBID)
				WHERE UNIQUE_VIEWS IS NOT NULL
				  AND PARTYID < 376699
				  AND UNIQUE_VIEWS
				  AND ELEMENT="party"'.
				  ($appic_views ? ' AND ID NOT IN ('.implodekeys(',',$appic_views).')' : null).'
				  AND ID IN ('.$allpartyidstr.')',
				ONE_DAY
			);

			# get from db, might be deleted/combined in allpartyidstr, event_created might contain less
			$event_created = db_simple_hash('party',$q = '
				SELECT PARTYID,CSTAMP,STAMP
				FROM party
				WHERE PARTYID IN ('.$allpartyidstr.')',
				ONE_DAY
			);

			$flock_visitors = memcached_simple_hash('going','
				SELECT PARTYID,COUNT(*)
				FROM going
				WHERE MAYBE=0
				  AND PARTYID IN ('.$allpartyidstr.')
				GROUP BY PARTYID',
				ONE_DAY
			);
			$appic_visitors = memcached_simple_hash('appic_event','
				SELECT ID,MAX(VISITORS)
				FROM appic_event
				JOIN fbid USING (FBID)
				WHERE ELEMENT="party"
				  AND ID IN ('.$allpartyidstr.')
				GROUP BY ID',
				ONE_DAY
			);
			$flock_views = memcached_simple_hash('party_view','
				SELECT PARTYID,SUM(HITS)
				FROM party_view
				WHERE PARTYID IN ('.$allpartyidstr.')
				GROUP BY PARTYID',
				ONE_DAY
			);
/*			$ticket_hits = memcached_simple_hash('tickethit','
				SELECT PARTYID,0+APPIC,COUNT(DISTINCT IPBIN,IDENTID,USERID)
				FROM tickethit
				WHERE PARTYID IN ('.$allpartyidstr.')
				  AND APPIC IN (0,1)
				GROUP BY PARTYID,0+APPIC',
				ONE_DAY
			);*/
			foreach ($allparties as $tmp_partyid) {
/*				$flock_views[$tmp_partyid] = memcached_single('party_view','
					SELECT SUM(HITS)
					FROM party_view
					WHERE PARTYID='.$tmp_partyid,
					ONE_DAY
				);
				$appic_views[$tmp_partyid] = memcached_single(['party','appic_event'],$q = '
					SELECT UNIQUE_VIEWS
					FROM party
					LEFT JOIN appic_event ON appic_event.PARTYID='.PARTY_MAIN_ID_INC.'
					WHERE NOT ISNULL(UNIQUE_VIEWS)
					  AND UNIQUE_VIEWS
					  AND party.PARTYID='.$tmp_partyid,
					ONE_DAY
				);
				$appic_views_fb[$tmp_partyid] = memcached_single(['fbid','appic_event'],'
					SELECT UNIQUE_VIEWS
					FROM fbid
					JOIN appic_event USING (FBID)
					WHERE NOT ISNULL(UNIQUE_VIEWS)
					  AND PARTYID < 376699
					  AND UNIQUE_VIEWS
					  AND ELEMENT="party"'.
					  ($appic_views ? ' AND ID NOT IN ('.implodekeys(',',$appic_views).')' : null).'
					  AND ID='.$tmp_partyid,
					ONE_DAY
				);

				# get from db, might be deleted/combined in allpartyidstr, event_created might contain less
				$event_created[$tmp_partyid] = db_simple_hash('party',$q = '
					SELECT CSTAMP,STAMP
					FROM party
					WHERE PARTYID='.$tmp_partyid,
					ONE_DAY
				);

				$flock_visitors[$tmp_partyid] = memcached_single('going','
					SELECT COUNT(*)
					FROM going
					WHERE MAYBE=0
					  AND PARTYID='.$tmp_partyid,
					ONE_DAY
				);
				$appic_visitors[$tmp_partyid] = memcached_single('appic_event','
					SELECT MAX(VISITORS)
					FROM appic_event
					JOIN fbid USING (FBID)
					WHERE ELEMENT="party"
					  AND ID='.$tmp_partyid,
					ONE_DAY
				);*/
				$ticket_hits[$tmp_partyid] = memcached_simple_hash('tickethit','
					SELECT 0+APPIC,COUNT(DISTINCT IPBIN,IDENTID,USERID)
					FROM tickethit
					WHERE PARTYID='.$tmp_partyid.'
					  AND APPIC IN (0,1)
					GROUP BY 0+APPIC',
					ONE_DAY
				);
			}
		}

		$hitsthisitem = [];
		$event_counts = [];
		$okparts = [];
		$debug = HOME_OFFICE && isset($_REQUEST['debug']);

		foreach ($events as $key => $parties) {
			[$element, $id] = explode(':',$key);

			if ($order_importance) {
				if ($debug) {
					set_memory_limit(3 * GIGABYTE);

					ob_start();

					layout_open_box();
					layout_box_header(get_element_link($element, $id));
					echo count($parties) ?> <? echo element_plural_name('event');
					?><table class="bordered hpadded"><?
					?><tr><?
					?><td><?= element_name('event') ?></td><?
					?><td class="right"><?= get_partyflock_icon() ?> <?= element_plural_name('view') ?></td><?
					?><td class="right"><?= element_plural_name('visit') ?></td><?
					?><td class="right"><?= get_appic_icon() ?> <?= element_plural_name('view') ?></td><?
					?><td class="right"><?= element_plural_name('visit') ?></td><?
					?><td class="right"><?= get_ticket_icon() ?></td><?
					?><td class="right"><?= element_name('importance') ?></td><?
					?><td class="right"><?= element_plural_name('day') ?></td><?
					?></tr><?
				}

				# caculate estimated views per year per event

				$importance_total = [];

				foreach ($parties as $partyid) {
					if (!isset($event_created[$partyid])) {
						# prolly deleted/combined away
						continue;
					}
					$created = $event_created[$partyid];
					[$cstamp, $stamp] = keyval($created);

#					$running = min($stamp,CURRENTSTAMP) - $cstamp;
					$running = CURRENTSTAMP - $cstamp;

					if ($running < ONE_WEEK) {
						# this skews stats
						continue;
					}
					$importance = get_importance(
						$flock_views   [$partyid] ?? 0,
						$flock_visitors[$partyid] ?? 0,
						$appic_views   [$partyid] ?? $appic_views_fb[$partyid] ?? 0,
						$appic_visitors[$partyid] ?? 0,
						$ticket_hits_appic = $ticket_hits[$partyid][1] ?? 0,
						$ticket_hits_flock = $ticket_hits[$partyid][0] ?? 0,
						$cstamp,
						$stamp
					);

					increment_or_set(
						$hitsthisitem,
						$key,
						$chosen_ticket_hits =
							isset($_REQUEST['appic'])
						?	$ticket_hits_appic
						:	(	isset($_REQUEST['partyflock'])
							?	$ticket_hits_flock
							:	$ticket_hits_appic + $ticket_hits_flock
							)
					);

					$importance_total[$partyid] = $importance;

					if ($debug) {
#						error_log_r($created,get_element_title('party',$partyid));
						?><tr><?
						?><td><?= get_element_link('party',$partyid) ?></td><?
						?><td class="right tt"><?= getifset($flock_views,$partyid) ?: null ?></td><?
						?><td class="right tt"><?= getifset($flock_visitors,$partyid) ?></td><?
						?><td class="right tt"><?= getifset($appic_views,$partyid) ?: null ?></td><?
						?><td class="right tt"><?= getifset($appic_visitors,$partyid) ?></td><?
						?><td class="right tt"><?= $chosen_ticket_hits ?></td><?
						?><td class="right tt"><?= $importance ?></td><?
						?><td class="right tt"><?= floor($running / ONE_DAY) ?></td><?

						?></tr><?
					}
				}
				if ($debug) {
					?></table><?
				}
				$importance_total_list = array_values($importance_total);

				# add zero zo single event items are lower
#				array_unshift($importance_total_list,0);

/*				if ($debug
				&&	$element === 'organization'
				&&	$id === 4
				) {
					print_rr($importance_total_list, 'importance_total_list');
				}*/

				$listsize = count($importance_total_list);
				sort($importance_total_list);

				if (!$listsize) {
					$average = $median = 0;
				} else {
					$average = array_sum($importance_total_list) / $listsize;

					$median =
						$listsize & 1
					?	$importance_total_list[$listsize >> 1]
					:	($importance_total_list[$listsize >> 1] + $importance_total_list[($listsize >> 1) - 1]) / 2;

					$ndxs = [];
					for ($i = 0; $i < floor($listsize >> 1); ++$i) {
						$ndxs[] = $i;
						$ndxs[] = $listsize - 1 - $i;
					}
					if ($listsize & 1) {
						$ndxs[] = $i;
					}

					rsort($ndxs);

	#				print_rr($total_events,'should be');
	#				print_rr($importance_total_list);

					$ptr = 0;
					$endptr = count($ndxs) - 1;

#					print_rr($ndxs,'ndxs');
#					print_rr($ptr.' -> '.$endptr);

					for ($i = $listsize; $i < $total_events; ++$i) {
						$ndx = $ndxs[$ptr];
						$importance_total_list[] = $importance_total_list[$ndx];
						$ptr = $ptr === $endptr ? 0 : $ptr + 1;
					}

					sort($importance_total_list);

				}
				$median_importance[$key] = $median;
				$average_importance[$key] = $average;

/*				# fill with average or median:
				for ($i = $listsize; $i < $total_events; ++ $i) {
					$importance_total_list[] = $median;
				}*/

				# NOTE: extrapolate:

#				print_rr($importance_total_list);
#				exit;

				$total_importance[$key] = array_sum($importance_total_list);

				if ($debug) {
					print_rr($total_importance[$key], 'total_importance');
					print_rr($median, 'median_importance');
					print_rr($average, 'average_importance');

					if ($listsize) {

						print_rr($listsize & 1
						?	$importance_total_list[$listsize >> 1]
						:	($importance_total_list[$listsize >> 1] + $importance_total_list[($listsize >> 1) - 1]) / 2,
							'median_importance_after_extrapolate'
						);
						print_rr(array_sum($importance_total_list) / count($importance_total_list),'average_importance_after_extrapolate');
						print_rr($importance_total_list,'extrapolated');
					}
					layout_close_box();
					$debugs[$key] = ob_get_clean();
				}

			} else {
				sort($parties);
				$partyidstr = implode(',', $parties);

				$event_counts[$key] = count($parties);

				$flock_views[$key] = memcached_single('party_view','
					SELECT SUM(HITS)
					FROM party_view
					WHERE DAYNUM>'.(CURRENTDAYNUM - (STATS_PERIOD / ONE_DAY)).'
					  AND PARTYID IN ('.$partyidstr.')',
					ONE_DAY
				);
				$event_visitors[$key] =
					($flock_visitors[$key] = memcached_single('going','
					SELECT COUNT(*)
					FROM going
					WHERE MAYBE=0
					  AND PARTYID IN ('.$partyidstr.')',
					ONE_DAY
				)) + ($appic_visitors[$key] = memcached_single('appic_event','
					SELECT SUM(VISITORS)
					FROM appic_event
					JOIN fbid USING (FBID)
					WHERE ELEMENT="party"
					  AND ID IN ('.$partyidstr.')',
					ONE_DAY
				));
			}
		}

		$prios = get_priorities();

		switch ($order) {
		case 'importance':
			uksort($okidlist,
/*				function($a, $b) use ($total_importance, $has_contests): int {
					[$a_element, $a_id] = explode(':', $a);
					[$b_element, $b_id] = explode(':', $b);
					$a_id = (int)$a_id;
					$b_id = (int)$b_id;
					# customers
					if ($diff
					=	(is_customer($b_element, $b_id) ? 1 : 0)
					-	(is_customer($a_element, $a_id) ? 1 : 0)
					) {
						return $diff;
					}
					if ($diff
					=	($b_element === 'organization' && isset($has_contests[$a_element][$b_id]) ? 1 : 0)
					-	($a_element === 'organization' && isset($has_contests[$b_element][$a_id]) ? 1 : 0)
					) {
						return $diff;
					}
					return ($total_importance[$b] ?? 0) - ($total_importance[$a] ?? 0);
				}*/

				static function(string $a, string $b) use($has_contests, $total_importance): int {
					[$a_element, $a_id] = explode(':', $a);
					[$b_element, $b_id] = explode(':', $b);
					$a_id = (int)$a_id;
					$b_id = (int)$b_id;
					return	((int)is_customer($b_element, $b_id) <=> (int)is_customer($a_element, $a_id))
						?:	((int)($has_contests[$b_element][$b_id] ?? 0)	 <=> ((int)($has_contests[$a_element][$a_id] ?? 0)))
						?:	(($total_importance[$b] ?? 0)					 <=> ($total_importance[$a] ?? 0));
				}
			);
			break;

		case 'per-event':
			# per event views
/*			uksort($okidlist,function($a, $b) use ($flock_views, $event_counts): int {

				$a_views  = $flock_views [$a] ?? 0;
				$b_views  = $flock_views [$b] ?? 0;
				$a_events = $event_counts[$a] ?? 0;
				$b_events = $event_counts[$b] ?? 0;

				if ($diff
				=	($b_events ? ($b_views / $b_events) : 0)
				-	($a_events ? ($a_views / $a_events) : 0)
				) {
					return $diff;
				}
				return $b_events - $a_events;
			});*/
			uksort($okidlist, static fn(string $a, string $b): int =>
					(	($event_counts[$b] ?? 0)
					?	(($flock_views[$b] ?? 0) / $event_counts[$b])
					:	0
					)
					<=>
					(	($event_counts[$b] ?? 0)
					?	(($flock_views[$b] ?? 0) / $event_counts[$a])
					:	0
					)
			);
			break;

		case 'total-views':
/*			uksort($okidlist,function($a,$b) use ($flock_views, $event_counts): int {
				if ($diff
				=	($flock_views[$a] ?? 0)
				-	($flock_views[$b] ?? 0)
				) {
					return $diff;
				}
				return	($event_counts[$a] ?? 0)
				-	($event_counts[$b] ?? 0);
			});*/
			uksort($okidlist, fn(string $a, string $b): int =>
				(($flock_views [$a] ?? 0) <=> ($flock_views [$b] ?? 0))
			?:	(($event_counts[$a] ?? 0) <=> ($event_counts[$b] ?? 0)));
			break;

		case 'total-visitors':
/*			uksort($okidlist, function($a, $b) use ($event_counts, $flock_views, $event_visitors) {
				if ($diff
				=	($event_visitors[$a] ?? 0)
				-	($event_visitors[$b] ?? 0)
				) {
					return $diff;
				}
				if ($diff
				=	($flock_views[$a] ?? 0)
				-	($flock_views[$b] ?? 0)
				) {
					return $diff;
				}
				return 	($event_counts[$a] ?? 0)
				-	($event_counts[$b] ?? 0);
			});*/
			uksort($okidlist, fn(string $a, string $b): int =>
				(($event_visitors[$a] ?? 0) <=> ($event_visitors[$b] ?? 0))
			?:	(($flock_views	 [$a] ?? 0) <=> ($flock_views	[$b] ?? 0))
			?:	(($event_counts	 [$a] ?? 0) <=> ($event_counts	[$b] ?? 0)));
			break;
		}

		if (!empty($debugs)) {
			?><div class="hidden abs" style="border:1px solid green;z-index:20;background-color:black;position:fixed;left:10em;top:10em;width:80%;height:800px;overflow-y:scroll" id="debugstore" onclick="hide(this)"></div><?
			foreach ($debugs as $key => $data) {
				?><div class="hidden" id="debug-<?= $key ?>"><?= $data ?></div><?
			}
		}

		layout_open_box('white');
		layout_box_header((
			isset($_REQUEST['appic'])
		?	get_appic_icon()
		:	(	isset($_REQUEST['partyflock'])
			?	get_partyflock_icon()
			:	null
			)
		).' Nieuwe evenementen zoeken');

		$show_pct = true;
		$show_ticket_hits = true;

		include_js('js/sorttable');
		include_js('js/processlist');

		?><table<?
		?> data-type="newlist"<?
		?> class="fw sortable hha proceventlist"<?
		if ($keep) {
			?> data-keep="data-keep"<?
		}
		?> onmouseenter="if (Pf.checkVisibles) { Pf.checkVisibles(this) }"<?
		?>><?
		?><tr><?
		?><th class="center" scope="col"></th><?
		?><th class="center" scope="col"></th><?
		?><th style="width: 50%;" scope="col"><?= Eelement_name('element') ?></th><?

		if ($show_ticket_hits) {
			?><th class="right" scope="col"><?= get_ticket_icon() ?></th><?
		}
		if ($show_pct) {
			?><th class="right" scope="col"></th><?
		}
		?><th class="win" scope="col"><?= __('partylist:win') ?></th><?
		?><th class="right" scope="col">&euro;</th><?

		if ($order_importance) {
			?><th class="right" scope="col"><?= Eelement_name('importance') ?></th><?
		} else {
			?><th class="right" scope="col"><?= Eelement_plural_name('event') ?></th><?
			?><th class="right"<?
			if ($order === 'total-visitors') {
				?> data-sorted="data-sorted"<?
			}
			?> scope="col"><?= element_plural_name('visitor') ?></th><?
			?><th class="right" scope="col"><?= Eelement_plural_name('visitor') ?>/<?= element_name('event') ?></th><?
			?><th class="right"<?
			if ($order === 'total-views') {
				?> data-sorted="data-sorted"<?
			}
			?> scope="col"><?= element_plural_name('view') ?></th><?
			?><th class="right"<?
			if ($order === 'per-event') {
				?> data-sorted="data-sorted"<?
			}
			?> scope="col"><?= element_plural_name('view') ?>/<?= element_name('event') ?></th><?
		}
		?></tr><?

		include_style('font_titillium');

		foreach ($okidlist as $key => $cust) {
			[$element, $id] = explode(':',$key);

			$id = (int)$id;
			$event_cnt = $event_counts[$key] ?? 0;
			$average_impo = $average_importance[$key] ?? 0;

			$countryid = $countryforkey[$key] ?? 0;

			$fbids = db_simpler_array(['fbid', (DO_URLCHECK ? 'urlcheck' : null), (DO_FBTOR ? 'fbtor' : null)],'
				SELECT DISTINCT fbid.FBID
				FROM fbid
				LEFT JOIN fbid pfbid
					 ON pfbid.FBID = fbid.FBID
					AND pfbid.ELEMENT = "presence"
				'.(DO_URLCHECK ? '
					LEFT JOIN urlcheck
						 ON urlcheck.ELEMENT = "presence"
						AND urlcheck.ID = pfbid.ID ' : '').'
				'.(DO_FBTOR ? '
					LEFT JOIN fbtor
						 ON fbtor.FBID = fbid.FBID ' : '').'
				WHERE fbid.TYPE IN ("page", "group")
				  AND fbid.ELEMENT = "'.$element.'"
				  AND fbid.ID = '.$id.
				(DO_FBTOR ? '
				  AND (	fbtor.FBID IS NULL
					OR fbtor.IMPOSSIBLE
					OR fbtor.EVENTIDS != ""
					) ' : null).
				(DO_URLCHECK ? '
				  AND (	   pfbid.FBID IS NULL
				  	OR urlcheck.ELEMENT IS NULL
					OR NOT '.where_url_is_bad().'
					 ) ' : '')
			);
			$ok_fbids = [];
			foreach ($fbids as $fbid) {
				if (empty($bad_fbids[$fbid])) {
					$ok_fbids[$fbid] = $fbid;
				}
			}
			if (!$ok_fbids) {
				continue;
			}

			if (DO_FBTOR) {
				$impossible_info = db_single_assoc('fbtor','
					SELECT	COUNT(IF(IMPOSSIBLE, 1, NULL)) AS IMPOSSIBLES,
							COUNT(IF(HAVEMENU, 1, NULL)) AS HAVEMENUS
					FROM fbtor
					WHERE IMPOSSIBLE
					  AND FBID IN ('.implode(',', $ok_fbids).')'
				);
			}

			ob_start();
			?><td class="center"><?
			if ($countryid) {
				require_once '_countryflag.inc';
				show_country_flag($countryid);
			}
			?></td><?

			?><td class="center"><?= match($element) {
				'organization'	=> '&#128101;',
				'location'	=> '&#128205;',
			}
			?></td><?

			?><td<?
			if ($filtered = $filtered_element[$element][$id] ?? null) {
				?> class="light"<?
			}
			?>><?
			echo escape_utf8(get_element_title($element, $id));

			$inaccessible = false;

			if (DO_FBTOR
			&&	$impossible_info
			&&	$impossible_info['IMPOSSIBLES'] === count($ok_fbids)
			) {
				if (!$impossible_info['HAVEMENUS']) {
					$inaccessible = true;
					?> <small>= <b><?= __('attrib:inaccessible') ?></b></small><?
				} else {
					?> <small>= <b>no events</b></small><?
				}
			}

			if (isset($_REQUEST['showfbids'])) {
				?> <?
				?><span class="mono"><?= implode(', ', $fbids) ?></span><?
			}

			if ($filtered) {
				?> <span class="warning">(<?= $filtered ?>)</span><?
			}


			if (isset($debugs[$key])) {
				?> <span<?
				?> class="unideanchor helpc"<?
				?> onclick="<?
					?>event.stopPropagation();<?
					?>unhide('debugstore');<?
					?>getobj('debugstore').innerHTML = getobj('debug-<?= $key ?>').innerHTML;<?
				?>"<?
				?>>&#8505;</span><?
			}
			?></td><?

/*			?><td><?
			if (!empty($have_new[$key])) {
				echo get_star('yellow');
			}
			?></td><?*/

			if ($show_ticket_hits) {
				?><td class="right" style="color: pink;"><?
				echo getifset($hitsthisitem, $key);
				?></td><?
			}

			if ($show_pct) {
				?><td class="right"><?

				if (!$inaccessible) {
					$okpart = null;
					if (isset($proc_stats[$element][$id])) {
						extract($proc_stats[$element][$id], EXTR_OVERWRITE);
						$okpart = $ADDED / $FOUND;
					}
					if ($okpart !== null) {
						$okpct = round(100 * $okpart);
						?><b class="<?
						if ($okpct > 75) {
							?>notice<?
						} elseif ($okpct > 50) {
							?>warning<?
						} elseif ($okpct > 25) {
							?>warror<?
						} else {
							?>error<?
						}
						?>"><?= $okpct ?>%</b><?
					}
				}
				?></td><?
			}


			?><td class="right nowrap"><?
			$have_cust = false;
			foreach (['partyflock','appic'] as $c) {
				if (isset($cust[$c])
				||	isset($customer[$c][$element][$id])
				) {
					$cust[$c] = call_user_func('get_'.$c.'_icon');
					$have_cust = true;
				} else {
					$cust[$c] = '<div class="ib presence iconw"></div>';
				}
			}
			if ($have_cust) {
				ksort($cust);
				echo implode(' ',$cust);
			}
			?></td><?

			?><td><?
			if (isset($has_contests[$element][$id])) {
				?><span class="win"><?
				if ($has_contests[$element][$id] > 1) {
					echo $has_contests[$element][$id], MULTIPLICATION_SIGN_ENTITY ?> <?
				}
				echo __('partylist:win');
				?></span><?
			}
			?></td><?

			if ($order_importance) {
				$importance = empty($total_importance[$key]) ? null : floor($total_importance[$key]);
				?><td class="right small importance" data-value="<?= $importance ?>"><?
				if ($importance) {
					echo number_format($importance, 0, null, '&thinsp;');
				}
				?></td><?
			} else {
				?><td class="right small"><?= $e_cnt = getifset($event_counts,$key) ?: null ?></td><?
				?><td class="right small"><?= $vis_cnt = getifset($event_visitors,$key) ?: null ?></td><?
				?><td class="right small"><?= $e_cnt ? round($vis_cnt / $e_cnt) : null ?></td><?
				?><td class="right small"><?= $v_cnt = getifset($flock_views,$key) ?: null ?></td><?
				?><td class="right small"><?= $e_cnt ? round($v_cnt / $e_cnt) : null ?></td><?
			}
			$row = ob_get_clean();

			?><tr class="ptr <?
			if (isset($locked[$element][$id])) {
				?> locked-item <?
			}
			if ($have_cust) {
				?> proc-customer<?
			} elseif (isset($prios[$element][$id])) {
				?> proc-prio<?
			}
			if (empty($need_updates[$key])) {
				?> light<?
			}
			?>"<?
			?> data-element="<?= $element ?>"<?
			?> data-id="<?= $id ?>"<?
			if (!isset($_REQUEST['showfbids'])) {
				?> onclick="Pf.openItem(this)"<?
			}
			if ($ok_fbids) {
				?> data-fb="<?= implode(',', array_map(static fn(int $fbid): string => $fbid.'/events', $ok_fbids)) ?>"<?
			}
			?>><?
			echo $row;
			?></tr><?
		}
		?></table><?
		layout_close_box();
		break;

	case 'changed':
		if ($debug = HOME_THOMAS && isset($_REQUEST['debug'])) {
			set_memory_limit(3 * GIGABYTE);
		}

		$today = strtotime('today '.HOUR_DAY_START.':00', CURRENTSTAMP);

		$sortby = have_element($_REQUEST,'sortby',['popular','date']) ?: 'date';

		$last_check = db_simple_hash(['party','proclist_done'],"
			SELECT PARTYID, proclist_done.STAMP
			FROM party
			JOIN proclist_done ON ELEMENT = 'party' AND ID = PARTYID
			WHERE CANCELLED = 0
			  AND party.STAMP > $today"
		);

		$parties = db_simple_hash(['party','proclist_done'],'
			SELECT PARTYID, GREATEST(COALESCE(proclist_done.STAMP, 0), CSTAMP)
			FROM party
			LEFT JOIN proclist_done ON ELEMENT = "party" AND ID = PARTYID
			WHERE CANCELLED = 0
			  AND party.STAMP >= '.$today
		);

		if (!$parties) {
			return;
		}
		ksort($parties);
		$partyidstr = implodekeys(',', $parties);

		$incompletes = memcached_same_hash('lineupneedupdate','
			SELECT PARTYID
			FROM lineupneedupdate
			WHERE PARTYID IN ('.$partyidstr.')'
		);
		if ($incompletes === false) {
			return;
		}

		$have_timetables = memcached_simple_hash('lineup','
			SELECT PARTYID,IF(COUNT(IF(TYPE IN ("mc","vj","lj","show") OR START_STAMP,1,NULL)) / COUNT(*) > .9,1,0)
			FROM lineup
			WHERE PARTYID IN ('.$partyidstr.')
			GROUP BY PARTYID'
		);
		if ($have_timetables === false) {
			return;
		}
		$lineup_counts = memcached_simple_hash('lineup','
			SELECT PARTYID,COUNT(*)
			FROM lineup
			WHERE PARTYID IN ('.$partyidstr.')
			GROUP BY PARTYID'
		);
		if ($lineup_counts === false) {
			return;
		}
		$flock_views = memcached_simple_hash('party_view','
			SELECT PARTYID,SUM(HITS)
			FROM party_view
			WHERE/* DAYNUM>'.(CURRENTDAYNUM - (STATS_PERIOD / ONE_DAY)).'
			  AND*/ PARTYID IN ('.$partyidstr.')
			GROUP BY PARTYID',
			ONE_DAY
		);

		$appic_views = memcached_simple_hash(['party','appic_event'],'
			SELECT party.PARTYID,UNIQUE_VIEWS
			FROM party
			LEFT JOIN appic_event ON appic_event.PARTYID='.PARTY_MAIN_ID_INC.'
			WHERE NOT ISNULL(UNIQUE_VIEWS)
			  AND UNIQUE_VIEWS
			  AND party.PARTYID IN ('.$partyidstr.')',
			ONE_DAY
		);
		$appic_views_fb = memcached_simple_hash(['fbid','appic_event'],'
			SELECT ID,UNIQUE_VIEWS
			FROM fbid
			JOIN appic_event USING (FBID)
			WHERE NOT ISNULL(UNIQUE_VIEWS)
			  AND PARTYID < 376699
			  AND UNIQUE_VIEWS
			  AND ELEMENT="party"
			  AND ID NOT IN ('.implodekeys(',',$appic_views).')
			  AND ID IN ('.$partyidstr.')',
			ONE_DAY
		);

		$flock_visitors = memcached_simple_hash('going','
			SELECT PARTYID,COUNT(*)
			FROM going
			WHERE MAYBE=0
			  AND PARTYID IN ('.$partyidstr.')
			GROUP BY PARTYID',
			ONE_DAY
		);

		$appic_visitors = memcached_simple_hash('appic_event','
			SELECT ID,MAX(VISITORS)
			FROM appic_event
			JOIN fbid USING (FBID)
			WHERE ELEMENT="party"
			  AND ID IN ('.$partyidstr.')
			GROUP BY ID',
			ONE_DAY
		);

		$appic_partner = memcached_simple_hash('appic_event','
			SELECT ID,PARTNER
			FROM appic_event
			JOIN fbid USING (FBID)
			WHERE ELEMENT="party"
			  AND ID IN ('.$partyidstr.')
			GROUP BY ID',
			ONE_DAY
		);

		$tickethits = memcached_simple_hash('tickethit','
			SELECT PARTYID,0+APPIC,COUNT(DISTINCT IPBIN,IDENTID,USERID)
			FROM tickethit
			WHERE PARTYID IN ('.$partyidstr.')
			  AND APPIC IN (0,1)
			GROUP BY PARTYID,0+APPIC',
			ONE_DAY
		);

		$have_maps = memcached_boolean_hash('uploadimage_link','
			SELECT ID
			FROM uploadimage_link
			WHERE TYPE="eventmap"
			  AND ID IN ('.$partyidstr.')'
		);

		$area_counts = memcached_simple_hash('partyarea','
			SELECT PARTYID,COUNT(*)
			FROM partyarea
			WHERE PARTYID IN ('.$partyidstr.')
			GROUP BY PARTYID'
		);

		require_once '_presale.inc';
		require_once '_appic.inc';

		$parties_with_maps = memcached_simpler_array('uploadimage_link',"
			SELECT DISTINCT ID
			FROM uploadimage_link
			WHERE TYPE = 'eventmap'"
		);

		if ($parties_with_maps === false) {
			return;
		}

		$parties_with_maps[] = 0;

		$all_fbids_per_party = db_same_hash('fbid','
			SELECT ID, FBID
			FROM fbid
			WHERE TYPE = "event"
			  AND ELEMENT = "party"
			  AND ID IN ('.$partyidstr.')'
		);
		if ($all_fbids_per_party === false) {
			return;
		}
		if (DO_FBTOR) {
			$last_changes = db_simple_hash(['fbid','fbtor_event','proclist_done'],'
				SELECT fbid.ID,IF(!ISNULL(MAX(proclist_done.STAMP)) AND MAX(LASTCHANGE)<=MAX(proclist_done.STAMP),"SAME",MAX(LASTCHANGE))
				FROM fbid
				JOIN fbtor_event USING (FBID)
				LEFT JOIN proclist_done ON proclist_done.ELEMENT="party" AND proclist_done.ID=fbid.ID
				WHERE TYPE="event"
				  AND fbid.ELEMENT="party"
				  AND fbid.ID IN ('.$partyidstr.')
				GROUP BY fbid.ID'
			);
			if ($last_changes === false) {
				return;
			}
		}

		$okparties = [];
		foreach ($parties as $partyid => $done_stamp) {
			$party = memcached_party_and_stamp($partyid);
			if (!$party) {
				mail_log('party with id '.$partyid.' not found');
				continue;
			}

			[$y, $m, $d, $hour, $mins] = _getdate($party['SHOW_STAMP'] = $party['STAMP_TZI'],'UTC');
			if ($hour < 6) {
				[$y, $m, $d, $hour, $mins] = _getdate($party['SHOW_STAMP'] = $party['STAMP_TZI'] - 10 * ONE_HOUR, 'UTC');
			}
			$date = 10000 * $y + 100 * $m + $d;

			$appic_vws			= $appic_views[$partyid] ?? $appic_views_fb[$partyid] ?? 0;
			$appic_v			= $appic_visitors[$partyid] ?? 0;
			$flock_v			= $flock_visitors[$partyid] ?? 0;
			$flock_vws			= $flock_views[$partyid] ?? 0;
			$ticket_hits_appic	= $tickethits[$partyid][1] ?? 0;
			$ticket_hits_flock	= $tickethits[$partyid][0] ?? 0;
			$area_count			= $area_counts[$partyid] ?? 0;
			$have_map			= isset($have_maps[$partyid]);

			if ($had_map = (!isset($have_maps[$partyid]) && $party['ORGIDS'])) {
#				require_once '_conceptsimilars.inc';
				if (false === ($had_map = memcached_single(['party', 'uploadimage', 'connect'], "
					SELECT 1
					FROM party
					JOIN uploadimage_link ON TYPE = 'eventmap'
										   AND ID = PARTYID
					JOIN connect ON MAINTYPE = 'party'
							    AND MAINID = PARTYID
							    AND ASSOCTYPE = 'organization'
							    AND ASSOCID IN ({$party['ORGIDS']})

					WHERE CANCELLED = 0
					  AND MOVEDID = 0
					  AND NAME IN ('".addslashes($party['NAME'])."')
					  AND PARTYID != $partyid
					  AND PARTYID IN (".implode(',', $parties_with_maps).")
					  AND STAMP < ".TODAYSTAMP."
					  AND STAMP > ".(TODAYSTAMP - 3 * ONE_YEAR)."
					LIMIT 1",
					ONE_DAY
				))) {
					return;
				}
				$had_maps[$partyid] = $had_map;
			}

			$want_map =
				!$have_map
			&&	($had_map || $area_count >= 3)
			&&	getifset($lineup_counts,$partyid) >= 6
			&&	(CURRENTSTAMP > $party['STAMP'] - ONE_MONTH);

			$have_lineup = array_key_exists($partyid,$have_timetables);

			$have_timetable = getifset($have_timetables,$partyid);

			$incomplete = !empty($incompletes[$partyid]);

			$want_timetable =
				$have_lineup
			&&	!$have_timetable
			&&	getifset($lineup_counts,$partyid) > 2
			&&	!$incomplete && (CURRENTSTAMP > $party['STAMP'] - ONE_MONTH);

			$last_change = $last_changes[$partyid] ?? 0;

			$visitors = $appic_v + $flock_v;

			$importance = get_importance(
				$flock_vws,
				$flock_v,
				$appic_vws,
				$appic_v,
				$ticket_hits_appic,
				$ticket_hits_flock,
				$party['CSTAMP'],
				$party['STAMP']
#				$partyid == 401525
			);


			ob_start();
			show_no_presale_indication($party);
			$no_presale = ob_get_clean();

			$want_presale = $no_presale && (!$party['PRESALE_STAMP'] || CURRENTSTAMP > $party['PRESALE_STAMP']);

#			$keep_all = HOME_THOMAS;
			$keep_all = false;

			$args = [
				'done_stamp'		=> $done_stamp,
				'have_timetable'	=> getifset($have_timetables,$partyid),
				'lineup_count'		=> getifset($lineup_counts,$partyid),
				'flock_views'		=> $flock_views ?: 0,
				'appic_views'		=> $appic_views ?: 0,
				'appic_visitors'	=> $appic_v ?: 0,
				'flock_visitors'	=> $flock_v ?: 0,
				'ticket_hits_appic'	=> $ticket_hits_appic ?: 0,
				'ticket_hits_flock'	=> $ticket_hits_flock ?: 0,
				'importance'		=> $importance,
				'incomplete'		=> $incomplete,
				'no_presale'		=> $no_presale,
				'have_map'			=> $have_map,
				'had_map'			=> $had_map,
				'area_count'		=> $area_count,
				'last_change'		=> $last_change,
				'want_presale'		=> $want_presale,
				'want_timetable'	=> $want_timetable,
				'want_map'			=> $want_map,
#				'debug'				=> $partyid == 381606
			];

			if ($done_stamp
			&&	!processlist_need_update('party', $partyid, $args)
			) {
				if (!$keep_all) {
					$party['DONE'] = true;
				}
			}

			if (!empty($party['DONE'])) {
				continue;
			}

			if (!$have_lineup
				# ^^^ don't skip of no line-up at all
			) {
			} elseif (
				!$want_timetable
			&&	!$want_map
			&&	!$want_presale
				# ^^^ dont' want anything new
			) {
				continue;
			}

			$party += [
				'appic_v'			=> $appic_v,
				'flock_v'			=> $flock_v,
				'flock_views'		=> $flock_views,
				'appic_views'		=> $appic_views,
				'importance'		=> $importance,
				'have_lineup'		=> $have_lineup,
				'have_timetable'	=> $have_timetable,
				'never_had_lineup'	=> getifset($args,'never_had_lineup'),
				'had_timetable'		=> getifset($args,'had_timetable'),
				'visitors'			=> $visitors,
				'incomplete'		=> $incomplete,
				'no_presale'		=> $no_presale,
				'have_map'			=> $have_map,
				'had_map'			=> $had_map,
				'area_count'		=> $area_count,
				'args'				=> $args,
			];

			$party['DATE'] = $date;

			$fbids = getifset($all_fbids_per_party, $partyid);
			if ($fbids) {
				require_once '_urlcheck.inc';

				$bad_fbids = [];
				$bad_sites = memcached_simpler_array(['presence', 'urlcheck'], '
					SELECT SITE
					FROM presence
					JOIN urlcheck
						 ON urlcheck.ELEMENT = "presence"
						AND urlcheck.ID = PRESENCEID
					WHERE presence.ELEMENT = "party"
					  AND presence.ID = '.$partyid.'
					  AND presence.TYPE = "facebook"
					  AND '.where_url_is_bad(),
					ONE_DAY
				);
				if ($bad_sites) {
					foreach ($bad_sites as $site) {
						if (preg_match('"/(\d+)$"',$site,$match)) {
							$bad_fbids[] = $match[1];
						}
					}
				}

				$party['BAD_SITES'] = $bad_sites;
				$party['BAD_FBIDS'] = $bad_fbids;

				$party['FBIDS'] = $fbids;

				[$party['FBID']] = get_facebook_ids('', 'party', $partyid, $fbids, for_processlist: true);
			}

			$okparties[$partyid] = $party;
		}

		$parties = $okparties;
		if (!$parties) {
			return;
		}

		switch ($sortby) {
		case 'popular':
			uksort($parties,function($a,$b) use ($flock_views,&$parties) {

				$b_importance = getifset($parties[$b],'importance') ?: 0;
				$a_importance = getifset($parties[$a],'importance') ?: 0;

				return $b_importance - $a_importance;
			});
			break;
		case 'date':
			uksort($parties,function($a,$b) use ($flock_views,$incompletes,$have_timetables,$parties) {
/*				$a_skip = isset($parties[$a]['SKIP']) ? 1 : 0;
				$b_skip = isset($parties[$b]['SKIP']) ? 1 : 0;
				$diff = $a_skip - $b_skip;
				if ($diff) {
					return $diff;
				}
*/
				$date_diff = $parties[$a]['DATE'] - $parties[$b]['DATE'];
				if ($date_diff) {
					return $date_diff;
				}

				$fb_a = isset($parties[$a]['FBID']) ? 1 : 0;
				$fb_b = isset($parties[$b]['FBID']) ? 1 : 0;
				$fb_diff = $fb_a - $fb_b;
				if ($fb_diff) {
					return $fb_diff;
				}

				$lineup_a = array_key_exists($a,$have_timetables) ? 1 : 0;
				$lineup_b = array_key_exists($b,$have_timetables) ? 1 : 0;
				$lineup_diff = $lineup_a - $lineup_b;
				if ($lineup_diff) {
					return $lineup_diff;
				}

				$complete_a = isset($incompletes[$a]) ? 0 : 1;
				$complete_b = isset($incompletes[$b]) ? 0 : 1;
				$complete_diff = $complete_a - $complete_b;
				if ($complete_diff) {
					return $complete_diff;
				}

				return strcasecmp($parties[$a]['NAME'],$parties[$b]['NAME']);
			});

		}

		include_js('js/sorttable');
		include_js('js/processlist');

		$curr_date = null;

		if (false === ($locked = get_locked_items('party'))) {
			return;
		}

		$sortbydate = $sortby === 'date';

		$show = true;

		if (false === ($have_mystery = memcached_same_hash('lineup', '
			SELECT PARTYID FROM lineup
			WHERE ARTISTID = 58686
			 AND PARTYID IN ('.implodekeys(', ', $parties).')')
		)) {
			return;
		}

		foreach ($parties as $partyid => $party) {
			extract($party, EXTR_OVERWRITE);

			$all_bad_fbs = !empty($party['BAD_FBIDS']) && count($party['BAD_FBIDS']) === count($party['FBIDS']);

			if (!$keep_all && isset($_REQUEST['skipped'])) {
				if (!isset($party['SKIP'])) {
					continue;
				}
			} else {
				if (!$all_bad_fbs
				&&	isset($party['SKIP'])
				) {
					continue;
				}
			}

			if ($curr_date !== true
			&&	(	!$curr_date
				||	$curr_date != $DATE
				)
			) {
				if ($curr_date) {
					?></table><?
					layout_close_box();
				}
				layout_open_box('white');
				if ($sortbydate) {
					ob_start();
					change_timezone('UTC');
					_dateday_display($SHOW_STAMP);
					[$y, $m, $d] = _getdate($SHOW_STAMP);
					if ($day_name = get_day_name($y,$m,$d)) {
						?> <?= MIDDLE_DOT_ENTITY ?> <?
						echo $day_name;
					}
					change_timezone();
					$header = ob_get_clean();

					layout_box_header($header);
				}

				?><table<?
				?> data-type="changelist"<?
				?> class="<?
				if (SMALL_SCREEN) {
					?>small <?
				}
				?>fw hha sortable proceventlist"<?
				if ($keep_all) {
					?> data-keep="data-keep"<?
				}
				?> onmouseenter="if (Pf.checkVisibles) { Pf.checkVisibles(this) }"<?
				?>><?
				?><tr><?
				?><th style="width:40%"><?= element_name('event') ?></th><?
				?><th class="small">partner</th><?
				if ($sortby !== 'date') {
					?><th class="small right">date</th><?
				}
				if ($show_last_check = true) { //$keep_all) {
					?><th class="small right">c</th><?
				}
		/*		?><th class="right">platform</th><?*/
				?><th class="hpad colorless"><?= get_ticket_icon() ?></th><?
				?><th class="hpad">L</th><?
				?><th class="hpad"><?= get_clock_icon() ?></th><?
				?><th class="hpad"><? show_map_icon() ?></th><?
/*				?><th class="hpad">&#9634;</th><?*?
/*				?><th class="right"><?= get_appic_icon() ?></th><?
				?><th class="right"><?= get_partyflock_icon() ?></th><?
				?><th class="right">views</th><?*/

				?><th class="right">i</th><?

				?></tr><?

				$curr_date = $sortbydate ? $DATE : true;
			}

			?><tr class="ptr<?
			if (!$all_bad_fbs
			&&	!empty($party['DONE'])
			) {
				?> item-done<?
			}
			if (isset($locked[$partyid])) {
				?> locked-item<?
			}
			?>"<?
			if (isset($party['SKIP'])) {
				?> data-skip="data-skip"<?
			}
			?> data-element="party"<?
			?> data-id="<?= $partyid ?>"<?
			?> onclick="Pf.openItem(this)"<?
			if (!empty($party['FBID'])) {
				?> data-fb="<?= $party['FBID'] ?>"<?
			}
			?>><?

			$lstamp = $last_check[$partyid] ?? false;

			?><td><?
			echo escape_utf8(get_element_title('party', $partyid));

			if ($all_bad_fbs) {
				?> <span class="error">&rarr; <?= __('status:cancelled') ?>?</span><?
			}
			if ($party['POSTPONED']) {
				?> <span class="notice">&rarr; <?= __('status:postponed') ?>?</span><?
			}

			if ($debug) {
				?> <span<?
				?> class="unideanchor helpc"<?
				?> onclick="<?
					?>event.stopPropagation();<?
					?>unhide('debug-<?= $partyid ?>');<?
				?>"<?
				?>>&#8505;</span><?
			}

			?></td><?
			$partner = getifset($appic_partner, $partyid);
			?><td data-value="<?= get_appic_partner_prio($partner) ?>"><?
			if ($partner) {
				?><span class="small light win"><?= escape_utf8(strtolower($appic_partner[$partyid])) ?></span><?
			}
			?></td><?

			if ($sortby !== 'date') {
				?><td class="small right" data-value="<?= $STAMP ?>"><?
				_date_display_tzi($STAMP_TZI,short: true, time_span: true);
				?></td><?
			}
			if ($show_last_check) {
				?><td class="small right" data-value="<?= $lstamp ?>"><?
				if ($lstamp) {
					$days = ceil((CURRENTSTAMP - $lstamp )/ ONE_DAY);
					echo $days,'d';
				}
				?></td><?
			}

			if ($no_presale) {
				?><td class="hpad"><?
				echo $no_presale;
				?></td><?
			} else {
				?><td></td><?
			}

			?><td class="hpad" data-value="<?
			echo	!$have_lineup
			?	3
			:	(	$incomplete
				?	2
				:	(	isset($have_mystery[$partyid])
					?	1
					:	0
					)
				);

			?>"><?

			if (!$have_lineup
			||	$incomplete
			) {
				if ($have_lineup) {
					show_incomplete_icon($show, 'lower light7');
				} elseif ($never_had_lineup) {
					show_incomplete_icon($show, 'lower colorless');
				} else {
					show_incomplete_icon($show, 'lower');
				}
			} elseif (isset($have_mystery[$partyid])) {
				?><span style="<?
					?>filter:hue-rotate(50deg);<?
				?>"><?
				show_incomplete_icon($show, 'lower light3');
				?></span><?
			}
			?></td><?

			?><td class="hpad"><?
			if ($have_timetable) {
#				echo get_clock_icon();
			} elseif ($had_timetable > .5) {
				echo get_clock_icon() ?><span class="notice">!</span><?
			} elseif ($args['want_timetable']) {
				?><span class="light"><?= get_clock_icon() ?>?</span><?
			}
			?></td><?

			?><td class="hpad"><?
			if ($have_map) {
#				show_map_icon();
			} elseif ($had_map) {
				show_map_icon() ?><span class="notice">!</span><?
			} elseif ($args['want_map']) {
				?><span class="light"><? show_map_icon() ?>?</span><?
			}
			?></td><?

/*			?><td class="hpad"><?
			if (!isset($have_square[$partyid])) {
				?>&#9634; ?<?
			}
			?></td><?*/

/*			?><td class="right"><?= $appic_v ?></td><?
			?><td class="right"><?= $flock_v ?></td><?
			?><td class="right"><?= $views ?></td><?*/

			?><td class="right<?
			if ($importance > 5000 * IMPORTANCE_MULTIPLIER) {
				?> notice<?
			} elseif ($importance > 1000 * IMPORTANCE_MULTIPLIER) {
				?> notice-nb<?
			} elseif ($importance > 500 * IMPORTANCE_MULTIPLIER) {
				?> less-notice<?
			} elseif ($importance > 100 * IMPORTANCE_MULTIPLIER) {
				?> light<?
			} else {
				?> light small<?
			}
			?>"><?
			echo round($importance);
			?></td><?

			?></tr><?

			if ($debug) {
				?><tr id="debug-<?= $partyid ?>" class="hidden"><?
				?><td colspan="100"><?
					print_rr([
						'flock_views'	=> $args['flock_views'][$partyid] ?? null,
						'appic_views'	=> $args['appic_views'][$partyid] ?? null,
					]);
				?></td><?
				?></tr><?
			}
		}
		if ($curr_date) {
			echo '</table>';
			layout_close_box();
		}

		break;
	}
}

function processlist_priority_list() {
	if (!($priolist = db_rowuse_array(['proclist_priority', 'location', 'organization', 'city', 'country'], "
		SELECT ELEMENT, ID, DELAY, city.CITYID, country.COUNTRYID
		FROM party_db.proclist_priority
		LEFT JOIN location
		  ON ELEMENT = 'location'
		 AND LOCATIONID = ID
		LEFT JOIN organization
		  ON ELEMENT = 'organization'
		 AND ORGANIZATIONID = ID
		LEFT JOIN city
		  ON city.CITYID = COALESCE(location.CITYID, organization.CITYID)
		LEFT JOIN country
		  ON country.COUNTRYID = COALESCE(city.COUNTRYID, organization.COUNTRYID)
		ORDER BY country.NAME ASC,
			 city.NAME ASC,
			 COALESCE(location.ORDERNAME,organization.NAME) ASC,
			 DELAY ASC"
	))) {
		return $priolist !== false;
	}

	$period = ONE_YEAR;

	$prio = null;

	include_js('js/sorttable');

	layout_show_section_header(Eelement_plural_name('priority'));

	?></tr><?
	?><table class="fw sortable hla"><?
	?><tr><?
	?><th><?= Eelement_name('element') ?></th><?
	if (have_super_admin()) {
		?><td><?= Eelement_name('element') ?></td><?
		?><td><?= Eelement_name('id')?></td><?
	}
	?><th><?= Eelement_name('country'), ', ', Eelement_name('city') ?></th><?
	?><th class="hpad right"><?= get_ticket_icon() ?></th><?
	?><th class="hpad right"><?= Eelement_plural_name('visitor') ?></th><?
	?><th class="hpad right"><?= Eelement_plural_name('view') ?></th><?
	?><th class="hpad right"><?= Eelement_plural_name('visitor') ?></th><?
	?><th class="hpad right"><?= Eelement_plural_name('view') ?></th><?
	?><th></th><?
	?></tr><?

	foreach ($priolist as $item) {
		extract($item, EXTR_OVERWRITE);

		switch ($ELEMENT) {
		case 'organization':
			$partyids = memcached_simpler_array(['party', 'connect'],'
				SELECT PARTYID
				FROM party
				JOIN connect
				  ON MAINTYPE = "party"
				 AND MAINID = PARTYID
				WHERE STAMP > '.(TODAYSTAMP - $period).'
				  AND ASSOCTYPE = "organization"
				  AND ASSOCID = '.$ID
			);
			break;

		case 'location':
			$partyids = memcached_simpler_array('party','
				SELECT PARTYID
				FROM party
				WHERE STAMP > '.(TODAYSTAMP - $period).'
				  AND LOCATIONID = '.$ID
			);
			break;
		}

		$appic_views =
		$flock_views =
		$appic_visitors =
		$flock_visitors =
		$ticket_hits = 0;

		if ($partyids) {
/*			$allpartyidstr = implode(',',$partyids);

			$flock_visitors = memcached_single('going','
				SELECT COUNT(*)
				FROM going
				WHERE MAYBE = 0
				  AND PARTYID IN ('.$allpartyidstr.')',
				ONE_DAY
			);
			$appic_visitors = memcached_single('appic_event','
				SELECT SUM(VISITORS)
				FROM appic_event
				JOIN fbid USING (FBID)
				WHERE ELEMENT = "party"
				  AND ID IN ('.$allpartyidstr.')',
				ONE_DAY
			);
			$flock_views = memcached_single('party_view','
				SELECT SUM(HITS)
				FROM party_view
				WHERE PARTYID IN ('.$allpartyidstr.')',
				ONE_DAY
			);
			$appic_views = memcached_single(['party','appic_event'], '
				SELECT SUM(UNIQUE_VIEWS)
				FROM party
				LEFT JOIN appic_event
				  ON appic_event.PARTYID = '.PARTY_MAIN_ID_INC.'
				WHERE NOT ISNULL(UNIQUE_VIEWS)
				  AND party.PARTYID IN ('.$allpartyidstr.')',
				ONE_DAY
			);

			$ticket_hits = memcached_single('tickethit','
				SELECT COUNT(DISTINCT PARTYID, IPBIN, IDENTID, USERID)
				FROM tickethit
				WHERE PARTYID IN ('.$allpartyidstr.')',
				ONE_DAY
			);*/

			foreach ($partyids as $partyid) {
				$flock_visitors += memcached_single('going','
					SELECT COUNT(*)
					FROM going
					WHERE MAYBE = 0
					  AND PARTYID = '.$partyid,
					ONE_DAY
				);
				$appic_visitors += memcached_single('appic_event','
					SELECT SUM(VISITORS)
					FROM appic_event
					JOIN fbid USING (FBID)
					WHERE ELEMENT = "party"
					  AND ID = '.$partyid,
					ONE_DAY
				);
				$flock_views += memcached_single('party_view','
					SELECT SUM(HITS)
					FROM party_view
					WHERE PARTYID = '.$partyid,
					ONE_DAY
				);
				$appic_views += memcached_single(['party','appic_event'], '
					SELECT SUM(UNIQUE_VIEWS)
					FROM party
					LEFT JOIN appic_event
					  ON appic_event.PARTYID = '.PARTY_MAIN_ID_INC.'
					WHERE NOT ISNULL(UNIQUE_VIEWS)
					  AND party.PARTYID = '.$partyid,
					ONE_DAY
				);
				$ticket_hits += memcached_single('tickethit','
					SELECT COUNT(DISTINCT PARTYID, IPBIN, IDENTID, USERID)
					FROM tickethit
					WHERE PARTYID = '.$partyid,
					ONE_DAY
				);
			}
		}

		?><tr><?

		?><td><?= get_element_link($ELEMENT,$ID) ?></td><?
		if (have_super_admin()) {
			?><td><?= $ELEMENT ?></td><?
			?><td><?= $ID ?></td><?
		}
		?><td><?
		if ($COUNTRYID) {
			echo get_element_link('country',$COUNTRYID);
		}
		if ($CITYID) {
			?>, <?
			echo get_element_link('city',$CITYID);
		}
		?></td><?

		?><td data-value="<?= $ticket_hits ?: 0 ?>" class="right"><?= $ticket_hits ? $ticket_hits.' '.get_ticket_icon() : null ?></td><?

		?><td data-value="<?= $appic_visitors ?: 0 ?>" class="hpad right"><?= $appic_visitors ? $appic_visitors.' '.get_appic_icon() : null ?></td><?
		?><td data-value="<?= $appic_views ?: 0 ?>" class="hpad colorless right"><?= $appic_views ? $appic_views.MULTIPLICATION_SIGN_ENTITY.' '.get_appic_icon() :  null ?></td><?
		?><td data-value="<?= $flock_visitors ?: 0 ?>" class="hpad right"><?= $flock_visitors ? $flock_visitors.' '.get_partyflock_icon() : null ?></td><?
		?><td data-value="<?= $flock_views ?: 0 ?>" class="hpad colorless right"><?= $flock_views ? $flock_views.MULTIPLICATION_SIGN_ENTITY.' '.get_partyflock_icon() : null ?></td><?

		?></tr><?
	}
	?></table><?
	return true;
}

/*
NOTE: facebook links can break thought people can visit them still

function processlist_maybe_cancelled() {
	if (!require_obtainlock(LOCK_PROCLIST_MAYBE_CANCELLED)) {
		return;
	}

	release_lock_on_unload(LOCK_PROCLIST_MAYBE_CANCELLED);

	$events = db_same_hash('fbid','
		SELECT PARTYID,FBID
		FROM fbid
		JOIN party ON PARTYID=ID
		WHERE TYPE="event"
		  AND CANCELLED = 0
		  AND ACCEPTED
		  AND ELEMENT="party"
		  AND STAMP>'.CURRENTSTAMP
	);
	if (!$events) {
		return $events !== false;
	}
	require_once '_urlcheck.inc';
	$bads = [];
	foreach ($events as $partyid => $fbids) {
		$bad_sites = memcached_simpler_array(['presence','urlcheck'],'
			SELECT SITE
			FROM presence
			JOIN urlcheck ON urlcheck.ELEMENT="presence" AND urlcheck.ID=PRESENCEID
			WHERE presence.ELEMENT="party"
			  AND presence.ID='.$partyid.'
			  AND presence.TYPE="facebook"
			  AND (		ISNULL(urlcheck.ELEMENT)
				  OR	urlcheck.FAILS>0
			)',
			TEN_MINUTES
		);
		if (!$bad_sites) {
			continue;
		}
		foreach ($bad_sites as $site) {
			if (preg_match('"/(\d+)$"',$site,$match)) {
				unset($fbids[$match[1]]);
			}
		}
		if (!$fbids) {
			$bads[$partyid] = $partyid;//memcached_simpler_array('presence','SELECT PRESENCEID FROM presence WHERE ELEMENT="party" AND ID='.$partyid);
 		}
	}
	if (!$bads) {
		print_rR('Geen potentieel afgelaste evenementen.');
		return;
	}

	layout_open_box();
	layout_box_header('Potentieel afgelaste evenementen');
	?><div class="block">Zijn er nog tickets te koop? Dan natuurlijk niet zomaar op 'afgelast' zetten!</div><?
	?><ul><?
	foreach ($bads as $partyid) {
		?><li><?= str_replace('<a ','<a target="_blank" ',get_element_link('party',$partyid)) ?></li><?
	}
	?></ul><?
	layout_close_box();
}*/

function processlist_customer_squares() {
	$parties = db_simpler_array('party','
		SELECT DISTINCT party.PARTYID
		FROM appic_event
		JOIN party ON '.PARTY_MAIN_ID_INC.'=appic_event.PARTYID
		WHERE STAMP>'.TODAYSTAMP.'
		  AND PARTNER NOT IN ("","Prospect")'
	);

	require_once '_uploadimage.inc';

	foreach ($parties as $partyid) {
		$img = uploadimage_get('party', $partyid, 'regular@2x', UPIMG_NOCHANGE, ['square']);

		?><div class="ib block" style="width: 500px; height: 500px;"><?
		?><a target="_blank" href="<?= get_element_href('party',$partyid) ?>"><?
		if (!have_uploadimage($img)) {
			?>GEEN SQUARE<?
		} else {
			uploadimage_show_from_img($img);
		}
		?></a><?
		?></div><?
	}
}
function processlist_no_location() {
	$events = db_rowuse_hash(['party','fbid'],'
		SELECT PARTYID,CITYID,
			(SELECT GROUP_CONCAT(FBID) FROM fbid WHERE ELEMENT="party" AND ID=PARTYID) AS FBIDS
		FROM party
		WHERE LOCATIONID=0
		  AND MOVEDID = 0
		  AND CANCELLED = 0
		  AND LIVESTREAM IN ("","+event")
		  AND STAMP>'.CURRENTSTAMP
	);
	if (!$events) {
		return $events !== false;
	}
	$last_check = db_simple_hash('proclist_done','
		SELECT ID,STAMP
		FROM proclist_done
		WHERE ELEMENT="party"
		  AND ID IN ('.implodekeys(',',$events).')'
	);

	layout_open_box();
	layout_box_header('Evementen zonder locatie');
	?>Controleer of er wellicht al een locatie vermeld wordt op Facebook.<br /><?
	?>Klaar met een evenement? Klik op grote min bij eventement. De lijst wordt niet automatisch bijgewerkt, ververs voor huidige staat van de lijst.<?
	layout_close_box();

	include_js('js/processlist');
	layout_open_box();
	?><table class="regular padded"><?
	foreach ($events as $partyid => $info) {
		extract($inf, EXTR_OVERWRITE);

		$party = memcached_party_and_stamp($partyid);

		$last_checked = getifset($last_check,$partyid);

		$check_delay = CURRENTSTAMP - $last_checked;

		$time_left = $party['STAMP'] - CURRENTSTAMP;

		$recently_done =
			$last_checked
		&&	(	$time_left > 3 * ONE_MONTH
			?	($check_delay < ONE_MONTH)
			:	(	$time_left > ONE_MONTH
				?	($check_delay < 2 * ONE_WEEK)
				:	(	$time_left < ONE_WEEK
					?	$check_delay < ONE_DAY
					:	$check_delay < 3.5 * ONE_DAY
					)
				)
			);

		if (/*	!have_super_admin()
		&&*/	$recently_done
		) {
			continue;
		}

		?><tr<?
		if ($recently_done) {
			?> class="light"<?
		}
		?>><?
		?><td class="right"><?
		_date_display($party['STAMP'], time_span: true);
		?></td><?
		?><td><?
			echo str_replace('<a ','<a target="_blank" onclick="return Pf.openFacebookToo(this,\'no-locations\',['.$FBIDS.'])" ',get_element_link('party',$partyid));
		?></td><?
		?><td><?
		if ($CITYID) {
			echo get_element_link('city',$CITYID);
		}
		?></td><?
		?></tr><?
	}
	?></table><?
	layout_close_box();
}
function processlist_no_presale() {
	layout_open_box();
	layout_box_header(Eelement_plural_name('event_without_presale'));
	layout_close_box();

	$events = db_rowuse_hash(['party','fbid', 'fbtor_event', 'presaleinfo'],'
		SELECT fbid.ID,party.STAMP,FBID AS FBIDS,TICKETURI
		FROM fbtor_event
		JOIN fbid USING (FBID)
		JOIN party ON party.PARTYID=ID
		LEFT JOIN presaleinfo ON presaleinfo.PARTYID=ID
		
		WHERE ELEMENT="party"

		  AND STARTSTAMP>'.CURRENTSTAMP.'
		  AND party.STAMP>'.CURRENTSTAMP.'
		  AND TICKETURI!=""
		  
		  AND NOT party.CANCELLED
		  AND NOT FREE_ENTRANCE
		  AND NOT party.SOLD_OUT
		  AND NOT party.MOVEDID
		  AND NOT party.POSTPONED

		  AND LIVESTREAM IN ("","+event")
		  
		  AND (		ISNULL(presaleinfo.PARTYID)
		  
			  OR	WEBSITE=""
			AND	EMAIL=""
			AND	presaleinfo.LOCATION=0
			AND	(ISNULL(ADDR) OR ADDR="")
			AND	ISNULL(IKBENAANWEZIG)
			AND	ISNULL(TICKETMASTER)
			AND	ISNULL(EVENTTICKETS)
			AND	ISNULL(EVENTIM)
			AND	ISNULL(PAYLOGICV2)
			AND	ISNULL(YOURTICKETPROVIDER)
			AND	ISNULL(NOWONLINETICKETS)		)
		  '
	);
	if (!$events) {
		return $events !== false;
	}

	$last_check = db_simple_hash('proclist_done','
		SELECT ID,STAMP
		FROM proclist_done
		WHERE ELEMENT="party"
		  AND ID IN ('.implodekeys(',',$events).')'
	);

	include_js('js/processlist');
	layout_open_box();
	?><table class="regular padded"><?
	?><tr><?
	?><th class="right"><?= Eelement_name('date') ?></th><?
	?><th><?= Eelement_name('event') ?></th><?
	?><th>Facebook ticket link</th><?
	?></tr><?
	foreach ($events as $partyid => $info) {
		extract($info, EXTR_OVERWRITE);

		$party = memcached_party_and_stamp($partyid);

		$last_checked = getifset($last_check,$partyid);

		$check_delay = CURRENTSTAMP - $last_checked;

		$time_left = $party['STAMP'] - CURRENTSTAMP;

		$recently_done =
			$last_checked
		&&	(	$time_left > 3 * ONE_MONTH
			?	($check_delay < ONE_MONTH)
			:	(	$time_left > ONE_MONTH
				?	($check_delay < 2 * ONE_WEEK)
				:	(	$time_left < ONE_WEEK
					?	$check_delay < ONE_DAY
					:	$check_delay < 3.5 * ONE_DAY
					)
				)
			);

		if (/*	!have_super_admin()
		&&*/	$recently_done
		) {
			continue;
		}

		?><tr<?
		if ($recently_done) {
			?> class="light"<?
		}
		?>><?
		?><td class="right"><?
		_date_display($party['STAMP'], time_span: true);
		?></td><?
		?><td><?
			echo str_replace('<a ','<a target="_blank" onclick="return Pf.openFacebookToo(this,\'no-locations\',['.$FBIDS.'])" ',get_element_link('party',$partyid));
		?></td><?
		?><td style="max-width:20em" class="noverflow nowrap"><?
		?><a target="_blank" href="<?= escape_specials($TICKETURI) ?>"><?= escape_specials($TICKETURI) ?></a><?
		?></td><?
		?></tr><?
	}
	?></table><?
	layout_close_box();
}
function processlist_postponed() {
	if (!require_obtainlock(LOCK_PROCLIST_POSTPONED)) {
		return false;
	}
	release_lock_on_unload(LOCK_PROCLIST_POSTPONED);

	if (!($events = db_rowuse_hash(['party','fbid'],"
		SELECT  PARTYID,
				GROUP_CONCAT(fbid.FBID) AS FBIDS
		FROM party_db.party
		LEFT JOIN fbid ON fbid.ELEMENT = 'party' AND fbid.ID = PARTYID
		WHERE POSTPONED
		GROUP BY PARTYID
		ORDER BY STAMP"))
	) {
		return $events !== false;
	}
	if (false === ($last_check = db_simple_hash('proclist_done','
		SELECT ID,STAMP
		FROM proclist_done
		WHERE ELEMENT = "party"
		  AND ID IN ('.implodekeys(',',$events).')'))
	) {
		return false;
	}
	layout_open_box();
	layout_box_header('Uitgestelde evementen');
	?>Controleer huidige uitgestelde evenementen al verplaatst zijn op Facebook.<br /><?
	?><b>Let op!</b> Niet verplaatsen als de datum op Facebook niet definitief is. Dan gewoon bij huidige datum laten.<br /><?
	?>Klaar met een evenement? Klik op grote min bij eventement. De lijst wordt niet automatisch bijgewerkt, ververs voor huidige staat van de lijst.<?
	layout_close_box();
	include_js('js/processlist');
	layout_open_box();
	layout_box_header('Uitgestelde evenementen');
	?><table class="regular padded"><?
	?><tr><?
	?><th class="right"><?= Eelement_name('date') ?></th><?
	?><th><?= Eelement_name('event') ?></th><?
	?><th>Nieuwe startdatum</th><?
	?><th>checked</th><?
	?></tr><?
	foreach ($events as $partyid => $item) {
		extract($item, EXTR_OVERWRITE);
		$party = memcached_party_and_stamp($partyid);
		$last_checked = getifset($last_check,$partyid);
		$time_left = $party['STAMP'] - CURRENTSTAMP;
		$time_since_last = CURRENTSTAMP - $last_checked;
		$delay = safe_random_int(7 * ONE_DAY, 14 * ONE_DAY);
		$recently_done = $time_since_last < $delay;
		if (/*	!have_super_admin()
		&&*/	$recently_done
		) {
			continue;
		}
		?><tr<?
		if ($recently_done) {
			?> class="light"<?
		}
		?>><?
		?><td class="right"><?
		_date_display($party['STAMP'], time_span: true);
		?></td><?
		?><td><?
			echo str_replace('<a ','<a target="_blank" onclick="return Pf.openFacebookToo(this,\'postponeds\',['.$FBIDS.'])" ',get_element_link('party',$partyid));
		?></td><?
		?><td><?
		_datetime_display($last_checked);
		?></td><?
		?></tr><?
	}
	?></table><?
	layout_close_box();
	return true;
}

function processlist_bad_timetables(): bool {
	if (!require_obtainlock(LOCK_PROCLIST_BAD_TIMETABLES)) {
		return false;
	}
	release_lock_on_unload(LOCK_PROCLIST_BAD_TIMETABLES);

	if (!($bads = db_rowuse_hash(['party','lineup'],'
		SELECT DISTINCT PARTYID,STAMP
		FROM party_db.party
		JOIN lineup USING (PARTYID)
		WHERE	lineup.START_STAMP
		  AND	(		lineup.START_STAMP < party.STAMP 
			OR	(lineup.START_STAMP + lineup.DURATION) > (party.STAMP + party.DURATION_SECS)
			)
		ORDER BY STAMP'))
	) {
		return $bads !== false;
	}
	?><table class="bordered hpadded"><?
	foreach ($bads as $partyid => $party) {
		?><tr><?
		?><td><?= str_replace('<a ','<a target="_blank" ',get_element_link('party',$partyid)) ?></td><?
		?><td class="right"><?= _datetime_get($party['STAMP']) ?></td><?
		?></tr><?
	}
	?></table><?
	return true;
}

function processlist_moved() {
	if (!require_obtainlock(LOCK_PROCLIST_MOVEDS)) {
		return;
	}
	release_lock_on_unload(LOCK_PROCLIST_MOVEDS);

	$events = db_rowuse_hash(['party','fbid','scrape_change'],'
		SELECT	fbid.ID,NEW_STAMP,STAMP_TZI,pd.STAMP AS LAST_CHECK,sc.MSTAMP AS LAST_CHANGE,GROUP_CONCAT(fbid.FBID) AS FBIDS,CANCELLED,
			(SELECT COUNT(DISTINCT FBID) FROM fbid ofbid WHERE ofbid.ELEMENT="party" AND ofbid.ID=fbid.ID) AS MULTI_FBIDS,
			ABS(CAST(STAMP_TZI AS SIGNED) - CAST(NEW_STAMP AS SIGNED)) AS DIFF
			
/*			,GROUP_CONCAT(pd.STAMP) AS PROCLIST_DONE_STAMP,
			GROUP_CONCAT(sc.MSTAMP) AS SCRAPE_CHANGE_STAMP,
			IF(ISNULL(pd.ID) OR pd.STAMP < sc.MSTAMP,"PD less than SC","PD bigger than SC") AS CH*/

		FROM scrape_change sc
		JOIN fbid ON ELEMENT="party" AND sc.FBID=fbid.FBID
		JOIN party ON PARTYID=fbid.ID
		LEFT JOIN feedevent_multiday ON feedevent_multiday.MAINID=fbid.FBID
		LEFT JOIN proclist_done pd ON pd.ELEMENT="party" AND pd.ID=fbid.ID
		WHERE 1
		
		  AND (ISNULL(pd.ID) OR pd.STAMP < sc.MSTAMP)
		  
		  AND ISNULL(feedevent_multiday.MAINID)
		  AND ABS(CAST(STAMP_TZI AS SIGNED) - CAST(NEW_STAMP AS SIGNED)) > 1000
		  
		  /* and not contained within */
		  AND NOT (
			  	party.STAMP BETWEEN NEW_STAMP AND NEW_END_STAMP
			  AND	party.STAMP+DURATION_SECS BETWEEN NEW_STAMP AND NEW_END_STAMP
		  )
		  
		  AND NEW_STAMP>='.TODAYSTAMP.'

		GROUP BY fbid.ID'
	);

	if (!$events) {
		return $events !== false;
	}

	foreach ($events as $event) {
		$fbids[$event['FBIDS']][$event['ID']] = $event['STAMP_TZI'];
	}

	foreach ($fbids as $fbid => $parties) {
		# connected parties: keep within unmodified

		$connects = db_same_hash('connect','
			SELECT ASSOCID
			FROM connect
			WHERE MAINTYPE="party"
			  AND MAINID IN ('.implodekeys(',',$parties).')
			  AND ASSOCTYPE="party"'
		);

		$connects[0] = 0;

		$keep_partyid = db_single(['party','fbid'],'
			SELECT PARTYID
			FROM fbid
			JOIN party ON ID=PARTYID
			WHERE FBID IN ('.$fbid.')
			  AND PARTYID NOT IN ('.implodekeys(',',$connects).')
			ORDER BY STAMP_TZI DESC
			LIMIT 1'
		);
		# keep only latest
		foreach ($parties as $partyid => $stamp_tzi) {
			if ($partyid != $keep_partyid) {
				unset($events[$partyid]);
			}
		}
	}

	layout_open_box();
	layout_box_header('Verplaatste  evementen');
	?>Controleer huidige uitgestelde evenementen definitief verplaatst zijn op Facebook.<br /><?
	?><b>Let op!</b> Niet verplaatsen als de datum op Facebook niet definitief is. Dan gewoon bij huidige datum laten.<br /><?
	?><b>Let op!</b> Ook niet verplaatsen als een oud evenement is dat verplaatst is om bezoekers mee te nemen naar nieuw evenement.<br /><?
	?>Klaar met een evenement? Klik op grote min bij eventement. De lijst wordt niet automatisch bijgewerkt, ververs voor huidige staat van de lijst.<?
	layout_close_box();

	include_js('js/processlist');
	layout_open_box();
	layout_box_header('Verplaatste evenementen');
	?><table class="regular padded"><?
	?><tr><?
	?><th class="right"><?= Eelement_name('date') ?></th><?
	?><th><?= Eelement_name('event') ?></th><?
	?><th><?= Eelement_name('new_date') ?></th><?
	?><th><?= Eelement_name('difference') ?></th><?
	?></tr><?
	foreach ($events as $partyid => $item) {
		extract($item);

		$party = memcached_party_and_stamp($partyid);

		if ($party['STAMP_TZI'] == $NEW_STAMP) {
			continue;
		}

		$recently_done = false;

		if (/*	!have_super_admin()
		&&*/	$recently_done
		) {
			continue;
		}

		?><tr<?
		if ($recently_done) {
			?> class="light"<?
		}
		?>><?
		?><td class="right"><?
		_date_display($party['STAMP'], time_span: true);
		?></td><?
		?><td><?
			echo str_replace('<a ','<a target="_blank" onclick="return Pf.openFacebookToo(this,\'movedsv2\',['.$FBIDS.'])" ',get_element_link('party',$partyid));
		?></td><?
		?><td><?
		change_timezone('UTC');
		_date_display($NEW_STAMP, time_span: true);
		change_timezone();
		?></td><?

		?><td><?
		$diff = $DIFF;
		$days  = floor($diff / ONE_DAY);
		$diff -= $days * ONE_DAY;
		$hours = floor($diff / ONE_HOUR);
		$diff -= $hours * ONE_HOUR;
		if ($diff >= 3400) {
			++$hours;
			$diff = 0;
			if ($hours == 24) {
				++$days;
				$hours = 0;
			}
		}
		$parts = [];
		if ($days) {
			$parts[] = $days.' '.element_name('day',$days);
		}
		if ($hours) {
			$parts[] = $hours.' '.element_name('hour',$hours);
		}
		if ($diff) {
			$parts[] = $diff.' '.element_name('second',$diff);
		}
		echo implode(', ',$parts);
		?></td><?

		?></tr><?
	}
	?></table><?
	layout_close_box();
	return;
}
function processlist_display_statistics() {
	$days = have_idnumber($_REQUEST, 'days') ?: 3;

	?><form method="get" action="/test/changed-items" class="block"><?
	?><input type="number" class="three_digits right" value="<?= $days ?>" name="days" /> <?=
		element_plural_name('day') ?> <?
	?><input type="submit" value="<?= __('action:show') ?>" /><?
	?></form><?

	$elements = [
		'process'				 => ['done'],
		'party'					 => ['new', 'changed'],
		'lineup'				 => ['changed'],
		'organization'			 => ['new', 'changed'],
		'location'				 => ['new', 'changed'],
		'artist'				 => ['new', 'changed'],
		'video'					 => ['changed'],
		'contact_ticket_message' => ['new'],
		'ignored'				 => ['done'],
	];

	$period = $days * ONE_DAY;

	$ignore_users = [2269];

	layout_open_box('white');
	layout_box_header(Eelement_name('menu'));

	$action_name = [
		'new'		=> __('attrib:new'),
		'changed'	=> __('status:changed'),
		'done'		=> __('status:done'),
	];

	?><table class="ubb smenu hha"><?
	foreach ($elements as $element => $actions) {
		foreach ($actions as $action) {
			?><tr class="<?
			if ($action === 'new') {
				?>notice-nb<?
			}
			?>"><?
			?><td><?= element_plural_name($element) ?></td><?
			?><td><?= $action_name[$action] ?>: </td><?
			?><td><a href="#<?= $element ?>-<?= $action ?>-stats"><?= element_plural_name('statistics') ?></a></td><?
			?><td><a href="#<?= $element ?>-<?= $action ?>"><?= element_name('list') ?></a></td><?
			?></tr><?
		}
	}
	?></table><?

	layout_close_box();

	$stats = null;

	$only_userid = have_idnumber($_REQUEST,'user') ? $_REQUEST['user'] :  0;

	ob_start();

	foreach ($elements as $element => $actions) {
		$done = [];
		foreach ($actions as $action) {
			switch ($element) {
			case 'lineup':
				$items = db_simple_hash($element,'
					SELECT DISTINCT PARTYID AS ID,MUSERID,MSTAMP
					FROM '.$element.' AS src
					WHERE MSTAMP>'.(TODAYSTAMP - $period).'
					  AND MUSERID!=0
					ORDER BY MSTAMP DESC'
				);
				break;
			case 'ignored':
				$items = db_simple_hash('facebook_ignore','
					SELECT CONCAT(FBID,":",NAME),CUSERID,CSTAMP
					FROM facebook_ignore
					WHERE CSTAMP>'.(TODAYSTAMP - $period).'
					ORDER BY CSTAMP DESC'
				);
				break;
			case 'process':
				$items = db_simple_hash('proclist','
					SELECT DISTINCT CONCAT(ELEMENT,":",ID) AS ID,USERID,STAMP
					FROM proclist_done
					WHERE STAMP>'.(TODAYSTAMP - $period).'
					  AND ELEMENT IN ("location","organization")
					UNION
					SELECT DISTINCT CONCAT(ELEMENT,":",ID) AS ID,USERID,STAMP
					FROM proclist_done_log
					WHERE ELEMENT IN ("location","organization")
					  AND STAMP>'.(TODAYSTAMP - $period).'

					ORDER BY STAMP DESC'
				);
				break;
			default:
				$and_where = null;
				$idname = $element.'ID';
				switch ($element) {
				case 'video':
					$and_where = ' AND STATUS="active"';
					break;
				case 'contact_ticket_message':
					$idname = 'CTMSGID';
					break;
				}

				switch ($action) {
				case 'new':
					switch ($element) {
					default:
						$items = db_simple_hash($element,'
							SELECT DISTINCT '.$idname.' AS ID,USERID,CSTAMP
							FROM '.$element.' AS src
							WHERE CSTAMP>'.(TODAYSTAMP - $period).'
							  AND ISNULL((
								SELECT 1
								FROM '.$element.'_log AS log
								WHERE log.'.$idname.'=src.'.$idname.'
								  AND log.USERID!=src.USERID
								  AND log.MSTAMP BETWEEN src.CSTAMP AND src.MSTAMP)
							) '.$and_where.'
							ORDER BY CSTAMP DESC'
						);
						break;
					case 'contact_ticket_message':
						$items = db_simple_hash($element,'
							SELECT DISTINCT '.$idname.' AS ID,USERID_FROM,CSTAMP
							FROM '.$element.' AS src
							WHERE CSTAMP>'.(TODAYSTAMP - $period).'
							  AND USERID_FROM!=0
							  AND DIRECTION!="toadmin"
							ORDER BY CSTAMP DESC'
						);
						break;
					}
					break;
				case 'changed':
					$items = db_simple_hash($element,'
						SELECT DISTINCT '.$element.'ID,MUSERID,MSTAMP
						FROM '.$element.' AS src
						WHERE MUSERID!=0
						  AND MSTAMP>'.(CURRENTSTAMP - $period).'
						  AND MSTAMP!=CSTAMP '.
						 $and_where.'
						ORDER BY MSTAMP DESC'
					);
					break;
				}
				break;
			}
			if (!$items) {
				continue;
			}
			ob_start();
			$item_element = $element;
			$hash = false;
			if ($element === 'lineup') {
				$item_element = 'party';
#				$hash = '#lineupbox';
			}

			$prev_item = null;

			foreach ($items as $id => $info) {
				[$userid,$stamp] = keyval($info);


				if (!isset($stats[$element][$action][$userid])) {
					  $stats[$element][$action][$userid] = 1;
				} else	++$stats[$element][$action][$userid];

				if (isset($done[$id])) {
					continue;
				}
				$done[$id] = true;


				if ($prev_item) {
					if ($prev_item[0] === $id
					&&	$prev_item[1] === $userid
					) {
						continue;
					}
				}

				$prev_item = [$id,$userid,$stamp];

				if ($element === 'process'
				||	$element === 'contact_ticket_message'
				) {
					continue;
				}

				if ($only_userid && $userid !== $only_userid) {
					continue;
				}

				?><tr<?
				if (in_array($userid,$ignore_users)) {
					?> class="light small"<?
				}
				?>><?
				?><td style="width:50%"><?
				switch ($element) {
				case 'ignored':
					[$fbid,$name] = explode(':',$id);
					?><a target="_blank" href="https://www.facebook.com/events/<?= $fbid ?>"><?= escape_utf8($name) ?></a><?
					break;
				case 'process':
					[$item_element,$item_id] = explode(':',$id);
					$id = $item_id;
				default:
					echo str_replace('<a ','<a target="_blank" ',get_element_link($item_element, $id, fragment: $hash));
					break;
				}
				?></td><?

				?><td><?= $userid ? get_element_link('user',$userid) : null ?></td><?
				?><td class="right"><? _datedaytime_display($stamp) ?></td><?

				?></tr><?
			}
			if ($data = ob_get_clean()) {
				layout_open_box(null,$element.'-'.$action);
				layout_box_header('<span class="win">'.Eelement_plural_name($element).($element === 'lineup' ? null : ' '.MIDDLE_DOT_ENTITY.' '.$action_name[$action]).'</span>');
				?><table class="fw"><?
				echo $data;
				?></table><?
				layout_close_box();
			}
		}
	}

	$list_data = ob_get_clean();

	$party_admins = db_same_hash('rights',"SELECT USERID FROM rights WHERE `PORTION` = 'party'");

	layout_open_box('white');
	layout_box_header(Eelement_name('statistics'));

	?><table><?

	foreach ($stats as $element => $actions) {
		foreach ($actions as $action => $users) {
			$total = array_sum($users);

			?><tr id="<?= $element ?>-<?= $action ?>-stats"><?
			?><th colspan="3"><?= element_plural_name($element) ?> <?= MIDDLE_DOT_ENTITY ?> <?= $action_name[$action] ?></th><?
			?></tr><?

			arsort($users);

			foreach ($users as $userid => $cnt) {
				if (!isset($party_admins[$userid])) {
					continue;
				}
				?><tr><?
				?><td class="right hpad"><?= round(100 * $cnt / $total) ?>%</td><?
				?><td class="right hpad"><?= $cnt ?></td><?
				?><td><?= get_element_link('user',$userid) ?></td><?
				?></tr><?
			}
		}
	}
	?></table><?

	layout_close_box();

	echo $list_data;
}

function set_proclist_done(?string $element = null, int $id = 0): int {
	if (!$element) {
		if (!($element = require_element($_POST, 'ELEMENT', ['organization', 'location', 'artist', 'party', 'topartist', 'toporganization', 'toplocation'], strict: true))
		||	!($id = require_idnumber($_POST, 'ID'))
		) {
			return 400;
		}
		require_once '_currentuser.inc';
		global $currentuser;
		$currentuser = new _currentuser(SKIP_SETTINGS | TINY_USER);
		if (!require_admin('party')) {
			return 403;
		}
	} elseif (!have_admin('party')) {
	 	return 403;
	}
	switch ($element) {
	default:
		$future_cnt = 'NULL';
		break;
	case 'artist':
		$future_cnt = db_single(['party','lineup'],'
			SELECT COUNT(DISTINCT PARTYID)
			FROM party
			JOIN lineup USING (PARTYID)
			WHERE STAMP > '.CURRENTSTAMP.'
			  AND ARTISTID = '.$id
		);
		break;
	case 'location':
		$future_cnt = db_single('party','
			SELECT COUNT(DISTINCT PARTYID)
			FROM party
			WHERE STAMP > '.CURRENTSTAMP.'
			  AND LOCATIONID = '.$id
		);
		break;
	case 'organization':
		$future_cnt = db_single(['party','connect'],'
			SELECT COUNT(DISTINCT PARTYID)
			FROM party
			JOIN connect ON PARTYID=ASSOCID
			WHERE STAMP>'.CURRENTSTAMP.'
			  AND ASSOCTYPE = "party"
			  AND MAINTYPE = "organization"
			  AND MAINID = '.$id
		);
		break;
	}
	if ($future_cnt === false) {
		return 500;
	}
	if (!db_insert('proclist_done_log','
		INSERT INTO proclist_done_log
		SELECT * FROM proclist_done
		WHERE ELEMENT="'.$element.'"
		  AND ID='.$id)
	||	!db_replace('proclist_done','
		REPLACE INTO proclist_done SET
			FUTURE_CNT	='.$future_cnt.',
			ELEMENT		="'.$element.'",
			ID			='.$id.',
			USERID		='.CURRENTUSERID.',
			STAMP		='.CURRENTSTAMP)
	||	!db_delete('proclist_lock','
		DELETE FROM proclist_lock
		WHERE USERID = '.CURRENTUSERID.'
		  AND ELEMENT = "'.$element.'"
		  AND ID = '.$id)
	||	$element === 'party'
	&&	(	!(require_once '_checkup.inc')
		||	!set_checkup('party', $id)
		)
	) {
		return 500;
	}
	return 200;
}
function lock_proclist_item(bool $do_lock = true): never {
	if (!($element = require_element($_POST, 'ELEMENT', ['organization', 'location', 'artist', 'party'], strict: true))
	||	!($id = require_idnumber($_POST, 'ID'))
	) {
		bail(400);
	}
	require_once '_currentuser.inc';
	global $currentuser;
	$currentuser = new _currentuser(SKIP_SETTINGS | TINY_USER);
	if (!require_admin('party')) {
		bail(403);
	}
#	if (!db_lock_tables('proclist_lock')) {
#		bail(500);
#	}
	$lock = db_single_assoc('proclist_lock','
		SELECT USERID,STAMP
		FROM proclist_lock
		WHERE ELEMENT="'.$element.'"
		  AND ID='.$id
	);
	if ($lock
	&&	(	$lock['USERID'] != CURRENTUSERID
		&&	$lock['STAMP'] > CURRENTSTAMP - LOCK_PERIOD
		)
	) {
		# locked
		bail(405);
	}
	if ($do_lock) {
		if (!db_replace('proclist_lock','
			REPLACE INTO proclist_lock SET
				USERID	='.CURRENTUSERID.',
				STAMP	='.CURRENTSTAMP.',
				ELEMENT	="'.$element.'",
				ID	='.$id)
		) {
			bail(500);
		}
	} else {
#		error_log('unlocking '.$element.':'.$id);
		if (!db_delete('proclist_lock','
			DELETE FROM proclist_lock
			WHERE USERID='.CURRENTUSERID.'
			  AND ELEMENT="'.$element.'"
			  AND ID='.$id)
		) {
			bail(500);
		}
	}
	bail(200);
}

function get_locked_items($element): array|false {
	return db_same_hash('proclist_lock','
		SELECT ID
		FROM proclist_lock
		WHERE USERID  != '.CURRENTUSERID."
		  AND ELEMENT  = '$element'
		  AND STAMP    > ".(CURRENTSTAMP - LOCK_PERIOD)
	);
}

function processlist_need_update(
	string	$element,
	int		$id,
	?array 	&$args			= null,
	?string	&$filter_reason	= null,
	?array	&$new_events	= null,
): bool {
	if (SHOW_ALL_ELEMENTS) {
		return true;
	}
	$filter = '';
	if ($args) {
		extract($args, EXTR_OVERWRITE);
	}
	$debug ??= false;
#	if ($element == 'organization' && $id == 13927) {
#		$debug = true;
#	}
	switch ($element) {
	case 'party':
		$party = memcached_party_and_stamp($partyid = $id);
		if (!$party
		||	$party['STAMP'] + $party['DURATION_SECS'] < CURRENTSTAMP
		) {
			# no party or past party
			return false;
		}
		if (!isset($have_timetable)) {
			$have_timetables = db_simple_hash('lineup','
				SELECT PARTYID,IF(COUNT(IF(TYPE IN ("mc","vj","lj","show") OR START_STAMP,1,NULL))/COUNT(*) > .9,1,0)
				FROM lineup
				WHERE PARTYID='.$partyid.'
				GROUP BY PARTYID'
			);
			$have_timetable = getifset($have_timetables,$partyid);

		}
		$have_lineup = $have_timetable !== null;
		break;

	case 'location':
		$location = memcached_location_info($id);
		if (!$location
		||	$location['FOLLOWUPID']
		||	$location['DEAD']
		) {
			if ($debug) {
				error_log('location:'.$id.' closed');
			}
			# no location or closed/followupped
			return false;
		}
		break;

	case 'organization';
		$organization = db_single_assoc('organization','
			SELECT DEADSTAMP,FOLLOWUPID
			FROM organization
			WHERE ORGANIZATIONID='.$id
		);
		if (!$organization
		||	$organization['DEADSTAMP']
		||	$organization['FOLLOWUPID']
		) {
			if ($debug) {
				error_log('organization:'.$id.' not active');
			}
			# no organization or stopped/followupped
			return false;
		}
		break;
	}

	if (!isset($done_stamp)) {
		$done_stamp = $args['done_stamp'] = db_single(['proclist_done', $element], '
			SELECT GREATEST(COALESCE(proclist_done.STAMP,0),CSTAMP)
			FROM '.$element.'
			LEFT JOIN proclist_done ON ELEMENT="'.$element.'" AND ID='.$id.'
			WHERE '.$element.'ID='.$id
		);
/*	} else {
		$cstamp = db_single($element,'
			SELECT CSTAMP
			FROM '.$element.'
			WHERE '.$element.'ID='.$id
		)
		if ($cstamp > $done_stamp) {
			$done_stamp = $cstamp;
		}*/
	}

	if (DO_FBTOR)
	switch ($element) {
	case 'location':
	case 'organization':
		if (!isset($eventidstr)) {
			$eventinfo = db_single_array('fbtor','
				SELECT MIN(CHECKSTAMP),GROUP_CONCAT(EVENTIDS)
				FROM fbtor
				JOIN fbid USING (FBID)
				WHERE ELEMENT="'.$element.'"
				  AND ID='.$id.'
				  AND NOT IMPOSSIBLE
				  AND CHECKSTAMP>'.(TODAYSTAMP - 3 * ONE_WEEK)
			);
			if (!$eventinfo) {
				$eventidstr = false;
				$checkstamp = 0;
			} else {
				[$checkstamp,$eventidstr] = $eventinfo;
			}
		}

		if ($eventidstr !== false
		&&	$eventidstr !== null
		) {
			$eventidstr = trim($eventidstr, ',');
			$eventidstr = preg_replace('",{2,}"','',$eventidstr);

			if (!$eventidstr) {
#				$filtered = 'no events found via tor';
				if ($debug) {
					error_log($element.':'.$id.' no events found via tor');
				}
				return false;
			}
			if ($done_stamp > $checkstamp) {
#				$filtered = 'done after checkstamp';
				if ($debug) {
					error_log($element.':'.$id.' done ('._datetime_get($done_stamp).') after check ('._datetime_get($checkstamp).')');
				}
				return false;
			}
			$eventids = explode_to_hash(',',$eventidstr);
			$eventcnt = count($eventids);
			$ignored = db_same_hash('facebook_ignore','
				SELECT FBID
				FROM facebook_ignore
				WHERE FBID IN ('.$eventidstr.')'
			);
			if ($ignored === false) {
				break;
			}
			$done = db_same_hash('fbid',$q = '
				SELECT FBID
				FROM fbid
				WHERE ELEMENT="party"
				  AND FBID IN ('.$eventidstr.')'
			);
			if ($done === false) {
				break;
			}

			# all ignored or all added?

			$new = array_diff_key($eventids, $done, $ignored);

			if ($new) {
				if ($debug) {
					error_log($element.':'.$id.' NEW events found via tor: '.implodekeys(', ',$new));
				}
				return true;
			}

			if ($checkstamp < 1566898200) { # 2019-08-27 11:30
				if ($eventcnt <= 4) {
					# Facebook limits future events to 5, so 5 may mean 4 or more

#					$filtered = 'no new events found via tor';
					if ($debug) {
						error_log($element.':'.$id.' no new events found via tor (old)');
					}
					return false;
				}
			} else {
				if ($debug) {
					error_log($element.':'.$id.' no new events found via tor');
				}
				return false;
			}
		}
	}

	if ($done_stamp
	&&	$done_stamp > CURRENTSTAMP - ONE_DAY
	) {
		if ($element === 'party') {
			if (CURRENTSTAMP < $party['STAMP'] - ONE_DAY) {
				# no check within 24 hours for event starting farther in the future
				if ($debug) {
					error_log($element.':'.$id.' no check for event done within past 24 hours and further away than 24 hours');
				}
				return false;
			}
		} else {
			if ($debug) {
				error_log($element.':'.$id.' no check within 24 hours');
			}
			# never check again same day
			return false;
		}
	}

	$customer = is_customer($element, $id);

	$delay = get_delay($element, $id);

	if ($customer) {
		$delay = min($delay, ONE_WEEK);
	}

	switch ($element) {
	case 'party':
		if (!isset($had_map)) {
			$had_map = $party['ORGIDS'] ? memcached_single(['party', 'connect', 'uploadimage_link'], '
				SELECT 1
				FROM party
				JOIN uploadimage_link
					 ON TYPE = "eventmap"
					AND ID = PARTYID
				JOIN connect
					 ON MAINTYPE = "party"
					AND MAINID = PARTYID
					AND ASSOCTYPE = "organization"
					AND ASSOCID IN ('.$party['ORGIDS'].')
				WHERE CANCELLED = 0
				  AND MOVEDID = 0
				  AND NAME IN ("'.addslashes($party['NAME']).'")
				  AND PARTYID != '.$partyid.'
				  AND PARTYID IN ((SELECT DISTINCT ID FROM uploadimage_link WHERE TYPE = "eventmap"))
				  AND STAMP < '.TODAYSTAMP.'
				  AND STAMP > '.(TODAYSTAMP - 3 * ONE_YEAR).'
				LIMIT 1',
				ONE_DAY
			) : false;
		}
		if (!isset($have_map)) {
			$have_map = memcached_single('uploadimage_link','
				SELECT 1
				FROM uploadimage_link
				WHERE TYPE="eventmap"
				  AND ID='.$partyid
			);
		}
		if (!isset($area_count)) {
			$area_count = memcached_single('partyarea','
				SELECT COUNT(*)
				FROM partyarea
				WHERE PARTYID='.$partyid
			);
		}
		if (!isset($lineup_count)) {
			$lineup_count = db_single('lineup','SELECT COUNT(*) FROM lineup WHERE PARTYID='.$partyid);
		}
		if (!isset($want_map)) {
			$want_map =
				!$have_map
			&&	($had_map || $area_count >= 3)
			&&	$lineup_count >= 6
			&&	(CURRENTSTAMP > $party['STAMP'] - ONE_MONTH);
		}
		break;
	}

	if (!$done_stamp
	||	$done_stamp < (CURRENTSTAMP - $delay)
	) {
		# at least once per delay

		switch ($element) {
		case 'party':
			if (!isset($no_presale)) {
				require_once '_presale.inc';
				ob_start();
				show_no_presale_indication($party);
				$no_presale = ob_get_clean();
			}
			if (!isset($incomplete)) {
				$incomplete = memcached_same_hash('lineupneedupdate','
					SELECT 1
					FROM lineupneedupdate
					WHERE PARTYID='.$partyid
				) ? true : false;
			}

			$want_presale = $no_presale && (!$party['PRESALE_STAMP'] || CURRENTSTAMP > $party['PRESALE_STAMP']);

			if (!isset($want_timetable)) {
				$want_timetable =
					$have_lineup
				&&	!$have_timetable
				&&	$lineup_count > 2
				&&	!$incomplete && (CURRENTSTAMP > $party['STAMP'] - ONE_MONTH);
			}

			if (DO_FBTOR
			&&	!isset($last_change)
			) {
				$last_change = db_single(['fbid','fbtor_event','proclist_done'],'
					SELECT IF(!ISNULL(MAX(proclist_done.STAMP)) AND MAX(LASTCHANGE)<=MAX(proclist_done.STAMP),"SAME",MAX(LASTCHANGE))
					FROM fbid
					JOIN fbtor_event USING (FBID)
					LEFT JOIN proclist_done ON proclist_done.ELEMENT="party" AND proclist_done.ID=fbid.ID
					WHERE TYPE="event"
					  AND fbid.ELEMENT="party"
					  AND fbid.ID='.$partyid.'
					GROUP BY fbid.ID'
				);
			}

			$name = addslashes($party['NAME']);

			if ($args['never_had_lineup'] = !$have_lineup) {
				$had_lineup =
				memcached_simple_hash('party',$q = '
					SELECT NOT ISNULL(lineup.LINEUPID) AS HAVE_LINEUP,COUNT(DISTINCT PARTYID)
					FROM (	SELECT PARTYID
						FROM party
						WHERE LOCATIONID='.$party['LOCATIONID'].'
						  AND CANCELLED = 0
						  AND MOVEDID = 0
						  AND NAME="'.$name.'"
						  AND STAMP<'.TODAYSTAMP.'
						  AND STAMP>'.(TODAYSTAMP - ONE_YEAR).
						(	$party['ORGIDS']
						?	' UNION
							SELECT PARTYID
							FROM party
							JOIN connect ON MAINTYPE="party" AND MAINID=PARTYID AND ASSOCTYPE="organization" AND ASSOCID IN ('.$party['ORGIDS'].')
							WHERE NAME="'.$name.'"
							  AND CANCELLED = 0
							  AND MOVEDID = 0
							  AND PARTYID!='.$partyid.'
							  AND STAMP<'.TODAYSTAMP.'
							  AND STAMP>'.(TODAYSTAMP - ONE_YEAR)
						:	null
						).'
						) AS parties
					LEFT JOIN lineup USING (PARTYID)
					GROUP BY HAVE_LINEUP',
					ONE_DAY
				);
				if (!$had_lineup) {
					$args['never_had_lineup'] = null;
				} else {
					$total = array_sum($had_lineup);
					$args['never_had_lineup'] = $total > 3 && !empty($had_lineup[0]) && ($had_lineup[0] / $total > .90);
				}
			}

			if ($args['had_timetable'] = !$args['never_had_lineup'] && !$have_timetable) {
				# (same name same org) or (same name same loc)
				#	-> time-table in past?
				$args['had_timetable'] =
				(	$party['ORGIDS']
				?	memcached_single(['party', 'lineup'], '
					SELECT /* DEBUG:name='.addslashes($name).':organizations='.$party['ORGIDS'].' */ COUNT(DISTINCT IF(START_STAMP, PARTYID, NULL)) / COUNT(DISTINCT PARTYID)
					FROM lineup
					JOIN party USING (PARTYID)
					JOIN connect ON MAINTYPE = "party"
							AND MAINID = PARTYID
							AND ASSOCTYPE = "organization"
							AND ASSOCID IN ('.$party['ORGIDS'].')
					WHERE CANCELLED = 0
					  AND MOVEDID = 0
					  AND NAME = "'.$name.'"
					  AND PARTYID != '.$partyid.'
					  AND STAMP < '.TODAYSTAMP.'
					  AND STAMP > '.(TODAYSTAMP - ONE_YEAR),
					ONE_DAY)
				: false)
				?: (	$party['LOCATIONID']
				?	memcached_single(['party', 'lineup'], '
					SELECT /* DEBUG:name='.addslashes($name).':location='.$party['LOCATIONID'].' */ COUNT(DISTINCT IF(START_STAMP, PARTYID,NULL)) / COUNT(DISTINCT PARTYID)
					FROM lineup
					JOIN party USING (PARTYID)
					WHERE CANCELLED	= 0
					  AND MOVEDID = 0
					  AND LOCATIONID = '.$party['LOCATIONID'].'
					  AND NAME = "'.$name.'"
					  AND PARTYID != '.$partyid.'
					  AND STAMP < '.TODAYSTAMP.'
					  AND STAMP > '.(TODAYSTAMP - ONE_YEAR),
					ONE_DAY
				) : false);
			}


			if ($debug) {
				error_log_r([
					'have_lineup'	=> $have_lineup,
					'want_map'	=> $want_map,
					'want_timetable'=> $want_timetable,
					'last_change'	=> $last_change ?? null,
					'done_stamp'	=> $done_stamp,
				]);
			}

			if (DO_FBTOR
			&&	(!$have_lineup || $have_lineup && !$incomplete)
			#	^^ finishjed line-up is ok, but also no line-up at all! We expect line-up to be mentioned in descr
			&&	!$want_map
			&&	!$want_timetable
			&&	$last_change
			) {
				if ($last_change === 'SAME') {
					if ($debug) {
						error_log(
							$element.':'.$id.' skipping '.$party['NAME'].' due to SAME @ '.
							db_single('fbtor_event','SELECT FROM_UNIXTIME(MAX(CHECKSTAMP)) FROM fbtor_event JOIN fbid USING (FBID) WHERE TYPE="event" AND ELEMENT="party" AND ID='.$partyid)
						);
					}
					return false;
				}
				if ($last_change < $done_stamp) {
					if ($debug) {
						error_log(
							$element.':'.$id.' skipping '.$party['NAME'].' due to no change @ '.
							db_single('fbtor_event','SELECT FROM_UNIXTIME(MAX(CHECKSTAMP)) FROM fbtor_event JOIN fbid USING (FBID) WHERE TYPE="event" AND ELEMENT="party" AND ID='.$partyid)
						);
					}
					return false;
				}
			}

#			if ($partyid == 385038) {
#				print_rr(get_defined_vars());
#			}

			if (($have_map || !$want_map)
			&&	($have_timetable || !$want_timetable)
			&&	$have_lineup
			&&	!$incomplete
			&&	(!$no_presale || !$want_presale)
			) {
				# have everything
				if ($debug) {
					error_log($element.':'.$id.' have everything');
				}
				return false;
			}
			break;
		}
		if ($debug) {
			error_log($element.':'.$id.' fallback need_update, done_stamp: '._datetime_get($done_stamp));
		}
		return true;
	}
	if ($debug) {
		error_log($element.':'.$id.' done recently');
	}

/*	if ($customer
	&&	$done_stamp < CURRENTSTAMP - ONE_WEEK
	) {
		# customers at least once per week
		return true;
	}*/

	# extend this with complex selection of useful events to check:
	#	- no line-up / incomplete line-up / time-table

	switch ($element) {
	case 'party':
		if ($debug) {
			error_log($element.':'.$id.' event in '.round(($party['STAMP'] - CURRENTSTAMP) / ONE_DAY, 1).' days');
			error_log($element.':'.$id.' done '.	round(($party['STAMP'] - $done_stamp) / ONE_DAY,  1).' days before event');
		}

		if (CURRENTSTAMP < $party['STAMP'] - ONE_WEEK) {
			if ($debug) {
				error_log($element.':'.$id.' not within 3 days');
			}
			break;
		}
		# event is now less than three days before start

		if (!array_key_exists('need_update', $args)) {
			if ($debug) {
				error_log('get need_update');
			}
			$need_update = db_single('needupdate','SELECT 1 FROM lineupneedupdate WHERE PARTYID = '.$id) ? true : false;
		}
		if (!array_key_exists('flock_v',$args)) {
			$flock_v = memcached_single('going','
				SELECT COUNT(*)
				FROM going
				WHERE MAYBE=0
				  AND PARTYID='.$id,
				ONE_DAY
			);
		}
		if (!array_key_exists('appic_v',$args)) {
			$appic_v = db_single('appic_event','
				SELECT MAX(VISITORS)
				FROM appic_event
				JOIN fbid USING (FBID)
				WHERE ELEMENT="party"
				  AND ID='.$id
			);
		}
		if (!array_key_exists('flock_views',$args)) {
			$flock_views = memcached_single('party_view','
				SELECT SUM(HITS)
				FROM party_view
				WHERE/* DAYNUM>'.(CURRENTDAYNUM - (STATS_PERIOD / ONE_DAY)).'
				  AND*/ PARTYID='.$id,
				ONE_DAY
			);
		}
		if (!array_key_exists('ticket_hits',$args)) {
			$ticket_hits = memcached_simple_hash('tickethit','
				SELECT 0+APPIC,COUNT(DISTINCT IPBIN,IDENTID,USERID)
				FROM tickethit
				WHERE PARTYID='.$id.'
				  AND APPIC IN (0,1)
				GROUP BY APPIC',
				ONE_DAY
			);
			$ticket_hits_flock = $ticket_hits[1] ?? 0;
			$ticket_hits_appic = $ticket_hits[0] ?? 0;
		}
		if (!array_key_exists('flock_views',$args)) {
			$appic_views = memcached_single(['party','appic_event'],$q = '
				SELECT UNIQUE_VIEWS
				FROM party
				LEFT JOIN appic_event ON appic_event.PARTYID='.PARTY_MAIN_ID_INC.'
				WHERE NOT ISNULL(UNIQUE_VIEWS)
				  AND UNIQUE_VIEWS
				  AND party.PARTYID='.$id,
				ONE_DAY
			);
			if ($appic_views === null) {
				$appic_views = memcached_single(['fbid','appic_event'],'
					SELECT UNIQUE_VIEWS
					FROM fbid
					JOIN appic_event USING (FBID)
					WHERE NOT ISNULL(UNIQUE_VIEWS)
					  AND PARTYID < 376699
					  AND UNIQUE_VIEWS
					  AND ELEMENT="party"
					  AND ID='.$id,
					ONE_DAY
				);
			}
		}
		if (!array_key_exists('importance', $args)) {
			$importance = get_importance(
				$flock_views		?: 0,
				$flock_v		?: 0,
				$appic_views		?: 0,
				$appic_v		?: 0,
				$ticket_hits_appic	?: 0,
				$ticket_hits_flock	?: 0,
				$party['CSTAMP'],
				$party['STAMP'],
				$debug
			);
		}

		if ($debug) {
/*			if (!isset($args['flock_views'])) {
				$args += [
					'done_stamp'	=> $done_stamp,
					'flock_views'	=> $flock_views,
					'appic_views'	=> $appic_views,
					'appic_v'	=> $appic_v,
					'flock_v'	=> $flock_v,
					'importance'	=> $importance,
					'have_lineup'	=> $have_lineup,
					'need_update'	=> $need_update,
				];
			}
			$args['done_stamp'] = _datetime_get($args['done_stamp']);
			$args['have_lineup'] = $have_lineup;*/
		}

		if (!$have_lineup
		||	$need_update
		) {
			# want (more complete) line-up!

			if (# last checked longer than 3 days before event
				CURRENTSTAMP > $party['STAMP'] - 3 * ONE_DAY
			&&	$done_stamp < $party['STAMP'] - 3 * ONE_DAY

				# last checked before 2 days before event, and importance > x
			||	CURRENTSTAMP > $party['STAMP'] - 2 * ONE_DAY
			&&	$done_stamp < $party['STAMP'] - 2 * ONE_DAY
			&&	$importance >= 50

				# last checked 1 day before event and importance > x
			||	CURRENTSTAMP > $party['STAMP'] - ONE_DAY
			&&	$done_stamp < $party['STAMP'] - ONE_DAY
			&&	$importance >= 200
			) {
				if ($debug) {
					error_log('need (more) line-up!');
				}
				return true;
			}
		}
		if ($debug) {
			error_log($element.':'.$id.' have_lineup:	'.($have_lineup ? 'true' : 'false'));
			error_log($element.':'.$id.' have_timetable: '.($have_timetable ? 'true' : 'false'));
			error_log($element.':'.$id.' want_map:	   '.($want_map ? 'true' : 'false'));
			error_log($element.':'.$id.' importance:	 '.$importance);
		}

		if ($have_lineup
		&&	!$have_timetable
		||	!$have_map
		&&	$want_map
		) {
			# want time-table!

			if (# last checked longer than 2 days before event
				$done_stamp < $party['STAMP'] - 3 * ONE_DAY
			&&	$importance >= 100

				# last checked before 1 days before event
			||	CURRENTSTAMP > $party['STAMP'] - 2 * ONE_DAY
			&&	$done_stamp < $party['STAMP'] - 2 * ONE_DAY
			&&	$importance >= 200

				# last checked 1 day before event and importance > 500
			||	CURRENTSTAMP > $party['STAMP'] - ONE_DAY
			&&	$done_stamp < $party['STAMP'] - ONE_DAY
			&&	$importance >= 300

			) {
				if ($debug) {
					error_log($element.':'.$id.' need time-table last days');
				}
				return true;
			}
			[$y, $m, $d, $hour, $mins] = _getdate($party['STAMP_TZI'],'UTC');

			$last_morning = strtotime($y.'-'.$m.'-'.$d.' 08:00');

			if (CURRENTSTAMP >  $last_morning
			&&	$done_stamp  <  $last_morning
			&&	$importance  >= 100
			) {
				if ($debug) {
					error_log($element.':'.$id.' need time-table last morning');
				}
				return true;
			}
		}
		break;
	}

	if ($debug) {
		error_log($element.':'.$id.' fallback NO need_update');
	}
	return false;
}

function proclist_status(): never {
	require_once '_date.inc';
	define('CURRENTDAYNUM',to_days());
	if (!have_something($_POST, 'ITEMSTR')) {
		bail(400);
	}
	require_once '_currentuser.inc';
	global $currentuser;
	$currentuser = new _currentuser(SKIP_SETTINGS | TINY_USER);
	if (!require_admin('party')) {
		bail(403);
	}
	$for_locks = [];
	foreach (explode(',',$_POST['ITEMSTR']) as $item) {
		$info = explode(':',$item);
		if (!isset($info[1])
		||	isset($info[2])
		) {
			bail(400);
		}
		[$element, $id] = $info;
		if (!in_array($element,['organization','location','artist','party'], true)
		||	!is_number($id)
		) {
			bail(400);
		}
		$for_locks[] = '("'.$element.'",'.$id.')';
		$items[] = [$element,$id];
	}
	# delete really old locks
	if (!db_delete('proclist_lock','DELETE FROM proclist_lock WHERE STAMP < '.(CURRENTSTAMP - 2 * ONE_HOUR))) {
		bail(500);
	}
	$locks = db_simple_hash('proclist_lock','
		SELECT ELEMENT, ID, USERID, STAMP
		FROM proclist_lock
		WHERE (ELEMENT, ID) IN ('.implode(',',$for_locks).')'
	);
	if ($locks === false) {
		bail(500);
	}
	$result = [];
	foreach ($items as [$element, $id]) {
		$locked = getifset($locks, $element, $id) ?: false;
		if ($locked) {
			[$userid, $stamp] = keyval($locked);
			if (CURRENTUSERID === $userid) {
				$locked = false;
			} elseif ($stamp > CURRENTSTAMP - LOCK_PERIOD) {
				$locked = true;
			}
		}
		$args = ['debug' => false];
		$need_update = processlist_need_update($element, $id, $args, $filter_reason);
		if ($filter_reason) {
			$need_update = true;
		}
		$result[$element][$id] = [
			'locked'	=> $locked,
			'done'		=> !$need_update,
		];
	}
	header('Content-Type: application/json');
	echo safe_json_encode($result);
	bail(200);
}

function processlist_show_closer(string $element, int $id): void {
	$needupdate = processlist_need_update($element, $id, $args);

	$hidden = false;
	if ($done_stamp = getifset($args,'done_stamp')) {
		$hidden = $done_stamp > CURRENTSTAMP - ONE_HOUR;
	}

	include_js('js/processlist');

	?><div style="width:32px;margin-left:1em" class="<?= $hidden ? 'hidden ' : null ?>divcloser ib relative">&nbsp;<?=
		get_remove_char([
			'data-element'	=> $element,
			'data-id'		=> $id,
			'id'			=> 'procmarkdone',
			'title'			=> __('action:mark_checked'),
			'class'			=> 'abs large'.($needupdate ? false : ' light'),
			'style'			=> 'margin-top:-.25em',
			'onclick'		=> 'Pf.doneItem(this, true)',
		])
	?></div><?
}
function processlist_low_resolution_images() {
	$images = db_rowuse_array(['uploadimage','uploadimage_link','uploadimagemeta'],'
		SELECT	TYPE,uploadimage_link.ID,
			(SELECT GROUP_CONCAT(FBID) FROM fbid WHERE ELEMENT="party" AND fbid.ID=uploadimage_link.ID) AS FBIDS
		FROM uploadimage
		JOIN uploadimage_link USING (UPIMGID)
		JOIN uploadimagemeta USING (UPIMGID)
		JOIN party ON PARTYID=ID
		LEFT JOIN proclist_done ON ELEMENT="party" AND proclist_done.ID=PARTYID AND proclist_done.USERID='.CURRENTUSERID.'
		WHERE party.STAMP>'.CURRENTSTAMP.'
		  AND (WIDTH<570 OR WIDTH=843)
		  AND CANCELLED = 0
		  AND SIZE IN ("original","regular@2x")
		  AND WIDTH>HEIGHT
		  AND TYPE="party"
		  AND uploadimage.USERID!=2269
		  AND (	ISNULL(proclist_done.ELEMENT)
		  	OR proclist_done.STAMP < '.(CURRENTSTAMP - ONE_WEEK).'
		)
		GROUP BY TYPE,ID'
	);
	if (!$images) {
		if ($images === false) {
			return;
		}
		?><div class="block">Geen lage resolutie afbeeldingen te verbeteren.</div><?
	}
	include_js('js/processlist');
	?><ul><?
	foreach ($images as $image) {
		?><li><?= str_replace('<a ','<a target="_blank" onclick="return Pf.openFacebookToo(this,\'lowresimgs\',['.$image['FBIDS'].'])" ',get_element_link('party',$image['ID'])) ?></li><?
	}
	?></ul><?
}
function processlist_missing_presales() {
	if (!require_obtainlock(LOCK_PROCLIST_MISSING_PRESALES)) {
		return;
	}
	release_lock_on_unload(LOCK_PROCLIST_MISSING_PRESALES);

	$parties = db_rowuse_array(['presaleinfo','party','party_log'],'
		SELECT	PARTYID,STAMP_TZI,
			(SELECT GROUP_CONCAT(FBID) FROM fbid WHERE ELEMENT="party" AND ID=PARTYID) AS FBIDS,
			PRESALE_STAMP,PRESALE_STAMP_NOTIME
		FROM party 
		LEFT JOIN presaleinfo USING (PARTYID)
		WHERE PRESALE_STAMP!=0
/*		  AND PRESALE_STAMP<='.CURRENTSTAMP.'*/
		  AND STAMP>='.CURRENTSTAMP.'
		  AND NOT PRESALE_SOLD_OUT
		  AND NOT SOLD_OUT
		  AND CANCELLED = 0
		  AND (	ISNULL(presaleinfo.PARTYID)
		  OR	(	WEBSITE=""
		  	AND	EMAIL=""
		  	AND	LOCATION=0
		  	AND	(ISNULL(ADDR) OR ADDR="")
		  	AND	ISNULL(IKBENAANWEZIG)
		  	AND	ISNULL(TICKETMASTER)
		  	AND	ISNULL(EVENTTICKETS)
		  	AND	ISNULL(EVENTIM)
		  	AND	ISNULL(PAYLOGICV2)
		  	AND	ISNULL(YOURTICKETPROVIDER)
		  	AND	ISNULL(NOWONLINETICKETS)
		  	)
		  )
		  ORDER BY PRESALE_STAMP ASC'
	);
	if (!$parties) {
		if ($parties === false) {
			return;
		}
		?><div class="block">Alle voorverkopen zijn ok!</div><?
		return;
	}
	include_js('js/processlist');

	show_presales_list($parties,true);
}
function show_all_presales() {
	$parties = db_rowuse_array(['party','party_log'],'
		SELECT	PARTYID,STAMP_TZI,party.MSTAMP,
			(SELECT GROUP_CONCAT(CONCAT(PRESALE_STAMP,":",MSTAMP)) FROM party_log WHERE party_log.PARTYID=party.PARTYID ORDER BY MSTAMP DESC) AS LOGS,
			(SELECT GROUP_CONCAT(FBID) FROM fbid WHERE ELEMENT="party" AND ID=PARTYID) AS FBIDS,
			PRESALE_STAMP,PRESALE_STAMP_NOTIME
		FROM party 
		WHERE PRESALE_STAMP>'.CURRENTSTAMP.'
		  AND STAMP>='.CURRENTSTAMP.'
		  AND CANCELLED = 0
		ORDER BY PRESALE_STAMP'
	);
	layout_open_box();
	layout_box_header('Alle voorverkopen die nog moeten starten');
	show_presales_list($parties,false);
	layout_close_box();
}
function show_presales_list(array $parties, bool $edit = false): void {
	if (!$edit) {
		foreach ($parties as &$party) {
			if (!$party['LOGS']) {
				$party['PRESALE_CHANGE'] = $party['MSTAMP'];
				continue;
			}
			$logs = explode(',',$party['LOGS']);
			$prev_mstamp = $party['MSTAMP'];
			foreach ($logs as [$old_presale_stamp,$old_mstamp]) {
				if ($old_presale_stamp != $party['PRESALE_STAMP']) {
					$party['PRESALE_CHANGE'] = $prev_mstamp;
					continue 2;
				}
				$prev_mstamp = $old_mstamp;
			}
			$party['PRESALE_CHANGE'] = $prev_mstamp;
		}
		unset($party);

		usort($parties, fn(array $a, array $b): int =>
				$b['PRESALE_CHANGE'] <=> $a['PRESALE_CHANGE']
			?:	$a['STAMP_TZI'	   ] <=> $b['STAMP_TZI'		]
		);
	}
	?><table class="bordered hpadded"><?
	?><tr><?
	if (!$edit) {
		?><th class="right"><?= __C('field:added') ?></th><?
	}
	?><th class="right rpad" colspan="2"><?= Eelement_name('presale_start') ?></th><?
	?><th><?= Eelement_name('event') ?></th><?
	?><th class="right"><?= Eelement_name('event_date') ?></th><?
	?></tr><?

	$now = CURRENTSTAMP;
	$end_today = strtotime('00:00 tomorrow',CURRENTSTAMP);
	$end_tomorrow = strtotime('00:00 tomorrow +1 day',CURRENTSTAMP);
	$end_dayaftertomorrow = strtotime('00:00 tomorrow +2 days',CURRENTSTAMP);

	foreach ($parties as $partyid => $party) {
		extract($party, EXTR_OVERWRITE);

		$day = null;
		$class = null;

		if ($PRESALE_STAMP < TODAYSTAMP) {
			$class = $PRESALE_STAMP >= TODAYSTAMP - ONE_DAY ? 'notice' : 'notice-nb';
		} elseif (
			$PRESALE_STAMP >= TODAYSTAMP
		&&	$PRESALE_STAMP < $end_today
		) {
			$class = $PRESALE_STAMP < CURRENTSTAMP ? 'notice-nb' : 'notice';
			$day = __('date:today');
		} elseif (
			$PRESALE_STAMP >= $end_today
		&&	$PRESALE_STAMP < $end_tomorrow
		) {
			$day = __('date:tomorrow');
		} elseif (
			$PRESALE_STAMP >= $end_tomorrow
		&&	$PRESALE_STAMP < $end_dayaftertomorrow
		) {
			$day = __('date:dayaftertomorrow');
		}

		?><tr<?
		if ($edit) {
			?> class="<?= $class ?>"<?
		}
		?>><?
		if (!$edit) {
			?><td class="right"><? _date_printnice(PRINTNICE_DATE_TIME | PRINTNICE_SHORT,$PRESALE_CHANGE,' ') ?></td><?
		}
		?><td class="right"><?
		if ($day) {
			echo $day;
		} else {
			_date_display($PRESALE_STAMP, short: true, time_span: true);
		}
		?></td><?
		?><td class="hpad"><?
		if (!$PRESALE_STAMP_NOTIME) {
			_time_display($PRESALE_STAMP);
		}
		?></td><?
		?><td><?
		if ($edit) {
			echo str_replace('<a ','<a target="_blank" onclick="return Pf.openFacebookToo(this,\'missing-presales\',['.$FBIDS.'])" ',get_element_link('party',$PARTYID));
		} else {
			echo get_element_link('party',$PARTYID);
		}
		?></td><?
		?><td class="right"><?
		change_timezone('UTC');
		_datetime_display($STAMP_TZI, short:true);
		change_timezone();
		?></td><?
	}
	?></table><?
}

function processlist_really_incomplete(): bool {
	if (!($parties = db_rowuse_array(['party', 'lineup', 'lineupneedupdate'],'
		SELECT	PARTYID,
				SUM(IF(START_STAMP, 1, 0)) AS WITH_TIMES,
				COUNT(*) AS TOTALS
		FROM party
		JOIN lineupneedupdate USING (PARTYID)
		JOIN lineup USING (PARTYID)
		WHERE STAMP > '.CURRENTSTAMP.'
		GROUP BY PARTYID
		HAVING WITH_TIMES != 0
		   AND WITH_TIMES < TOTALS'
	))) {
		return $parties !== false;
	}
	?><ul><?
	foreach ($parties as $party) {
		extract($pary, EXTR_OVERWRITE);
		?><li><?= get_element_link('party',$PARTYID) ?>, <?= round(100 * $WITH_TIMES / $TOTALS) ?>%</li><?
	}
	?></ul><?
	return true;
}

function get_priorities() {
	static $__prios = null;
	if ($__prios === null) {
		$__prios = db_read('proclist_priority','
			SELECT ELEMENT AS _ELEMENT, ID AS _ID, DELAY
			FROM proclist_priority',
			ROW_USE_MULTI_HASH
		);
	}
	return $__prios;
}

function get_delay(string $element, int $id): int {
	$prio = get_priorities()[$element][$id] ?? null;
	if (!$prio) {
		return 2 * ONE_WEEK;
	}
	if ($prio['DELAY']) {
		return $prio['DELAY'] * ONE_WEEK;
	}
	if ($element === 'party') {
		$party = memcached_party_and_stamp($id);

		$months = floor(($party['STAMP'] - CURRENTSTAMP) / ONE_MONTH);

		return max($months, 2) * ONE_WEEK;
	}
	return 2 * ONE_WEEK;
}

function show_lockable(int $lock_type, string $url, string $name, string|int|null $postfix = null): void {
	if ($postfix) {
		if (is_string($postfix)
		&&	str_contains($postfix, '/')
		) {
			[$part, $total] = explode('/', $postfix);
			$postfix = " &middot; $part<span class=\"light\"> = $total</span>";
		} else {
			$postfix = " &middot; $postfix";
		}
	}
	if (is_locked($lock_type)) {
		?><li class="locked-item"><?= $name, $postfix ?></li><?
	} else {
		?><li><a target="_blank" href="<?= $url ?>"><?= $name ?></a><?= $postfix ?></li><?
	}
}

function processlist_top(string $element, int $locktype): void {
	$period_days = 60;
	$check_before = CURRENTSTAMP - (ONE_YEAR / 2);
	$min_views = 50;
	$show = 500;
	$shown = 0;

	layout_open_box();
	layout_box_header('Top '.element_plural_name($element));
	?>Controleer of onderstaande elementen voorzien kunnen worden van nieuwere informatie.<br /><?
	?>Te denken valt aan nieuwe afbeelding, aanwezigheden controle, aanvullen basis informatie.<?
	?>Klaar met een element? Klik op het minnetje aan het einde van de regel.<?
	layout_close_box();

	if (!require_obtainlock($locktype)) {
		return;
	}
	release_lock_on_unload($locktype);

	$and_where_domain = null;

	if (!empty($_REQUEST['DOMAIN'])
	&&	in_array($_REQUEST['DOMAIN'], ['nl', 'be'])
	) {
		$and_where_domain = ' AND DOMAIN = "'.$_REQUEST['DOMAIN'].'" ';
		if ($_REQUEST['DOMAIN'] === 'be') {
			$min_views = 20;
		}
	}

	switch ($element) {
	case 'artist':
		if (false === ($event_count = memcached_simple_hash(['lineup', 'party'], '
			SELECT ARTISTID, COUNT(DISTINCT PARTYID)
			FROM lineup
			JOIN party USING (PARTYID)
			WHERE STAMP > '.CURRENTSTAMP.'
			GROUP BY ARTISTID',
			expires: ONE_DAY))
		||	false === ($views = memcached_simple_hash('artist_counter', '
			SELECT ARTISTID, SUM(VIEWS)
			FROM artist_counter
			WHERE DAYNUM > TO_DAYS(NOW()) - '.$period_days.'
			'.$and_where_domain.'
			GROUP BY ARTISTID
			HAVING SUM(VIEWS) > '.$min_views.'
			ORDER BY SUM(VIEWS) DESC',
			expires: ONE_DAY
		))) {
			return;
		}
		$proc_item = 'topartist';
		$idname = 'ARTISTID';
		break;

	case 'location':
		if (false === ($event_count = memcached_simple_hash('party', '
			SELECT LOCATIONID, COUNT(DISTINCT PARTYID)
			FROM party
			WHERE STAMP > '.CURRENTSTAMP.'
			GROUP BY LOCATIONID'))
		||	false === ($views = memcached_simple_hash('location_counter', '
			SELECT LOCATIONID,SUM(VIEWS)
			FROM location_counter
			WHERE DAYNUM > TO_DAYS(NOW()) - '.$period_days.'
			'.$and_where_domain.'
			GROUP BY LOCATIONID
			HAVING SUM(VIEWS) > '.$min_views.'
			ORDER BY SUM(VIEWS) DESC',
			expires: ONE_DAY
		))) {
			return;
		}
		$proc_item = 'toplocation';
		$idname = 'LOCATIONID';
		break;

	case 'organization':
		if (false === ($event_count = memcached_simple_hash(['lineup', 'party'], '
			SELECT MAINID, COUNT(DISTINCT PARTYID)
			FROM party
			JOIN connect
			  ON MAINTYPE = "organizaton"
			 AND ASSOCTYPE = "party"
			 AND ASSOCID = PARTYID
			WHERE STAMP > '.CURRENTSTAMP.'
			GROUP BY MAINID',
			expires: ONE_DAY))
		||	false === ($views = memcached_simple_hash('organization_counter','
			SELECT ORGANIZATIONID, SUM(VIEWS)
			FROM organization_counter
			WHERE DAYNUM > TO_DAYS(NOW()) - '.$period_days.'
			'.$and_where_domain.'
			GROUP BY ORGANIZATIONID
			HAVING SUM(VIEWS) > '.$min_views.'
			ORDER BY SUM(VIEWS) DESC',
			expires: ONE_DAY
		))) {
			return;
		}
		$proc_item = 'toporganization';
		$idname = 'ORGANIZATIONID';
		break;
	}

	$all_elements = $event_count + $views;

	if (!$all_elements) {
		return;
	}

	uksort($all_elements, fn($a,$b) => ($views[$b] ?? 0) <=> ($views[$a] ?? 0));

/*	$last_modified = db_simple_hash($element,'
		SELECT '.$idname.',MSTAMP
		FROM '.$element.'
		WHERE '.$idname.' IN ('.implodekeys(',',$all_elements).')'
	);*/

	if (false === ($last_modified_proclist = db_simple_hash($element,'
		SELECT ID, MAX(STAMP)
		FROM proclist_done
		WHERE ELEMENT = "'.$proc_item.'"
		  AND ID IN ('.implodekeys(', ', $all_elements).')
		GROUP BY ID'
	))) {
		return;
	}

	include_js('js/processlist');

	?><table class="hhla hpadded"><?
	?><tr><?
	?><th><?= Eelement_name($element) ?></th><?
	?><th class="right"><?= Eelement_plural_name('event') ?></th><?
	?><th class="right"><?= Eelement_plural_name('view') ?></th><?
	?><th></th><?
	?></tr><?

	foreach ($all_elements as $id => $null) {
		if (
		#	($last_modified[$id] ?? 0) >= $check_before
			($last_modified_proclist[$id] ?? 0) >= $check_before
		||	($views[$id] ?? 0) < $min_views
		) {
			continue;
		}
		?><tr><?
		?><td><?= str_replace('<a ','<a target="_blank" ',get_element_link($element,$id)) ?></td><?
		?><td class="right"><?= $event_count[$id] ?? null ?></td><?
		?><td class="right"><?= $views[$id] ?? null ?></td><?
		?><td><?

		echo get_special_char('remove',[
			'data-element'	=> 'top'.$element,
			'data-id'	=> $id,
			'title'		=> __('action:mark_done'),
			'onclick'	=> 'Pf.doneItem(this,false)',
		]);

		?></td><?
		?></tr><?
		if (++$shown > $show) {
			break;
		}
	}
	?></table><?
}
