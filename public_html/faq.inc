<?php

require_once '_help.inc';

function perform_commit() {
	switch ($_REQUEST['ACTION']) {
	case 'commit':		return faq_commit();
	}
}
function display_body() {
	switch ($_REQUEST['ACTION']) {
	default:
	case null:		return faq_display_overview();
	case 'single':
	case 'commit':		return faq_display_single();
	case 'form':		return faq_display_form();
	}
}
function faq_menu($faq = null) {
	layout_open_menu();
	layout_menuitem(Eelement_name('overview'),'/faq',!$_REQUEST['ACTION']);
	layout_close_menu();
	if (!have_admin('faq')) {
		return;
	}
	layout_open_menu();
	if ($faq) {
		layout_menuitem(__C('action:change'),'/faq/'.$faq['FAQID'].'/form');
	} else {
		layout_menuitem(__C('action:add'),'/faq/form');
	}
	layout_close_menu();
}
function faq_display_overview() {
	layout_show_section_header();

	faq_menu();

	$faqs = db_rowuse_hash('faq','SELECT FAQID,HELPSECTION,ADMINONLY FROM faq');
	if ($faqs === false) {
		return;
	}
	if (!$faqs) {
		?><p><?= __('faq:no_questions_LINE'); ?></p><?
		return;
	}
	foreach ($faqs as $faqid => &$faq) {
		$faq['QUESTION'] = __('faq:'.$faqid.':question_LINE');
		$faq['SECTIONNAME'] = __C('helpsection:'.$faq['HELPSECTION']);
	}
	unset($faq);
	doublestring_asort($faqs,'SECTIONNAME','QUESTION');
	layout_open_box('faq');
	layout_open_table(TABLE_FULL_WIDTH);
	$currenthelp = false;
	$anyadmin = have_admin();
	foreach ($faqs as $faqid => $faq) {
		if ($faq['ADMINONLY']
		&&	!$anyadmin
		) {
			continue;
		}
		if (!$currenthelp
		||	$currenthelp != $faq['HELPSECTION']
		) {
			layout_start_spanned_row(2);
			?><b><a href="/help/<?= $faq['HELPSECTION']; ?>"><?= $faq['SECTIONNAME']; ?></a></b><?
			layout_stop_row();
			$currenthelp = $faq['HELPSECTION'];
		}
		layout_start_rrow();
		if ($faq['ADMINONLY']) {
			?><b><?= __('help:adminonly_prefix'); ?></b>: <?
		}
		?><a href="/faq/<?= $faqid; ?>"><?= $faq['QUESTION']; ?></a><?
		layout_stop_row();
	}
	layout_close_table();
	layout_close_box();
}
function faq_show_overview($selected) {
	$adminonly = db_simple_hash('faq','
		SELECT FAQID,ADMINONLY
		FROM faq
		WHERE HELPSECTION="'.$selected['HELPSECTION'].'"
		ORDER BY QUESTION ASC'
	);
	if ($adminonly === false) {
		return;
	}
	if (!$adminonly) {
		?><p><?= __('faq:no_questions_LINE'); ?></p><?
		return;
	}
	$anyadmin = have_admin('faq');
	foreach ($adminonly as $faqid => $adminonly) {
		$questions[$faqid] = __('faq:'.$faqid.':question_LINE');
	}
	asort($questions);
	layout_open_box('faq');
	layout_open_table(TABLE_FULL_WIDTH);
	foreach ($questions as $faqid => $question) {
		if (($onlyadmins = !empty($adminonly[$faqid]))
		&&	!$anyadmin
		) {
			continue;
		}
		layout_start_rrow();
		if ($onlyadmins) {
			?><b><?= __('help:adminonly_prefix'); ?></b>: <?
		}
		if ($selected
		&&	$selected['FAQID'] == $faqid
		) {
			?><b><?= $question; ?></b><?
		} else {
			?><a href="/faq/<?= $faqid; ?>"><?= $question; ?></a><?
		}
		layout_stop_row();
	}
	layout_close_table();
	layout_close_box();
}
function faq_display_single() {
	if (!($faqid = require_idnumber($_REQUEST,'FAQID'))) {
		return false;
	}
	$faq = db_single_assoc('faq','
		SELECT FAQID,HELPSECTION,CUSERID,MUSERID,CSTAMP,MSTAMP,ADMINONLY
		FROM faq
		WHERE FAQID='.$faqid
	);
	if ($faq === false) {
		return;
	}
	if (!$faq) {
		_error(__('faq:error:nonexistent_LINE',array('ID'=>$faqid)));
		return;
	}
	layout_show_section_header();

	if ($faq['ADMINONLY']
	&&	!have_admin()
	) {
		_error(__('error:for_admins_only_LINE'));
		return;
	}
	faq_menu($faq);

	?><article itemscope itemtype="https://schema.org/Article"><?
	layout_open_box('help','faq');
	layout_box_header(__('faq:'.$faqid.':question_LINE'));
	?><div class="block"><?= __('faq:'.$faqid.':answer_TEXT',DO_UBB | DO_NL2BR); ?></div><?
	layout_display_alteration_note($faq,'faq:'.$faqid.':answer_TEXT');
	layout_close_box();

	faq_show_overview($faq);
	?></article><?
}
function faq_display_form() {
	if (!require_admin('faq')
	||	!optional_number($_REQUEST,'FAQID')
	) {
		return;
	}
/*	layout_open_section_header();
	?>Veel gestelde vraag <? echo isset($_REQUEST['FAQID']) ? 'aanpassen' : 'toevoegen';
	layout_close_section_header();*/
	layout_show_section_header();

	if (isset($_REQUEST['FAQID'])) {
		$faqid = $_REQUEST['FAQID'];
		$faq = db_single_assoc('faq','SELECT * FROM faq WHERE FAQID='.$faqid);
		if ($faq === false) {
			return;
		}
		if (!$faq) {
			_error(__('faq:error:nonexistet_LINE',array('ID'=>$faqid)));
			return;
		}
	} else {
		$faqid = 0;
	}
	?><form onsubmit="return submitForm(this)" method="post" action="/faq<?
	if ($faqid) {
		?>/<? echo $faqid;
	}
	?>/commit"><?
	layout_open_box('faq');
	layout_open_table('');
	layout_start_row();
		echo Eelement_name('section');
		layout_field_value();
		?><select required="required" name="HELPSECTION"><option></option><? helpsection_options($faqid ? $faq['HELPSECTION'] : null); ?></select><?
	layout_restart_row();
		echo Eelement_name('question');
		layout_field_value();
		?><input type="text" required="required" name="QUESTION" value="<? if ($faqid) echo __('faq:'.$faqid.':question_LINE'); ?>" /><?
	layout_restart_row();
		echo Eelement_name('answer');
		layout_field_value();
		?><textarea cols="70" rows="30" required="required" name="ANSWER"><? if ($faqid) echo __('faq:'.$faqid.':answer_TEXT'); ?></textarea><?
	layout_restart_row();
		layout_next_cell();
		?><label for="adminonly"><input<?
		if ($faqid && $faq['ADMINONLY']) {
			?> checked="checked"<?
		}
		?> type="checkbox" value="1" name="ADMINONLY" id="adminonly"> <?= __C('checkbox:for_admins_only'); ?></label><?
	layout_stop_row();
	layout_close_table();
	layout_close_box();
	?><p><input type="submit" value="<?= __(isset($faq) ? 'action:change' : 'action:add');
	?>"></p></form><?
}
function faq_commit() {
	require_once '_ubb_preprocess.inc';
	if (!require_admin('faq')
	||	!require_something_trim($_POST, 'QUESTION')
	||	!require_something_trim($_POST, 'ANSWER')
	||	!require_helpsection($_POST,'HELPSECTION')
	||	!optional_number($_REQUEST,'FAQID')
	) {
		return;
	}
	$setlist[] = 'HELPSECTION="'.	$_POST['HELPSECTION'].'"';
	$setlist[] = 'ADMINONLY='.	(isset($_POST['ADMINONLY']) ? 1 : 0);

	if (isset($_REQUEST['FAQID'])) {
		$faqid = $_REQUEST['FAQID'];
		$setlist[] = 'MSTAMP='.CURRENTSTAMP;
		$setlist[] = 'MUSERID='.CURRENTUSERID;
		if (!store_translation('faq:'.$faqid.':question_LINE',_ubb_preprocess($_POST['QUESTION']))
		||	!store_translation('faq:'.$faqid.':answer_TEXT',_ubb_preprocess($_POST['ANSWER']))
		||	!db_insert('faq_log','
			INSERT INTO faq_log
			SELECT * FROM faq
			WHERE FAQID='.$_REQUEST['FAQID'])
		||	!db_update('faq','
			UPDATE faq SET '.implode(',',$setlist).'
			WHERE FAQID='.$_REQUEST['FAQID'])
		) {
			return;
		}
		_notice(__('faq:notice:faq_changed_LINE'));
	} else {
		$setlist[] = 'CSTAMP='.CURRENTSTAMP;
		$setlist[] = 'CUSERID='.CURRENTUSERID;
		$setlist[] = 'ANSWER=""';
		if (!db_insert('faq','
			INSERT INTO faq SET '.implode(',',$setlist))
		) {
			return;
		}
		$_REQUEST['FAQID'] = $faqid = db_insert_id();
		if (!store_translation('faq:'.$faqid.':question_LINE',_ubb_preprocess($_POST['QUESTION']))
		||	!store_translation('faq:'.$faqid.':answer_TEXT',_ubb_preprocess($_POST['ANSWER']))
		) {
			return;
		}
		_notice(__('faq:notice:faq_added_LINE'));
	}
}
