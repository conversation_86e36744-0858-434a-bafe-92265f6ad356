<?php

declare(strict_types=1);

function set_field(string $field, bool|string $value, string $desc): bool {
	$element = $_REQUEST['sELEMENT'];
	$id		 = $_REQUEST['sID'];
	if (is_string($value)) {
		$set_value = '"'.addslashes($value).'"';
	} else {
		$set_value = $value ? '1' : '0';
	}
	if (!require_admin($element)
	||	!db_insert("{$element}_log", "
		INSERT INTO {$element}_log
		SELECT * FROM `$element`
		WHERE $field != $set_value
		  AND {$element}ID = $id")
	||	!db_update($element, "
		UPDATE `$element` SET
			$field	= $set_value,
			MSTAMP	= ".CURRENTSTAMP.',
			MUSERID	= '.CURRENTUSERID."
		WHERE $field != $set_value
		  AND {$element}ID = $id")
	) {
		return false;
	}
	register_notice("$element:notice:{$desc}_LINE", DO_UBB, [strtoupper($field).'ID' => $id]);
	if ($element === 'flock') {
		require_once '_sphinx.inc';
		sphinx_update_element($element, $id);
	}
	return true;
}
