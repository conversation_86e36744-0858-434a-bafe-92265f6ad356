<?php

declare(strict_types=1);

const NOT_GOING		= 0;
const GOING_CERTAIN	= 1;
const GOING_MAYBE	= 2;
const GOING_ANY		= 3;

function going_to_party(int|array $partyarg, int $userid = CURRENTUSERID): bool {
	if (is_array($partyarg)) {
		return in_array(GOING_CERTAIN, going_to_party_type($partyarg, $userid), true);
	}
	return going_to_party_type($partyarg, $userid) === GOING_CERTAIN;
}

function get_all_going(int $userid = CURRENTUSERID, int $type = GOING_ANY): array {
	static $__going;
	if (!isset($__going[$userid][$type])) {
		switch ($type) {
		case GOING_ANY:
			$__going[$userid][$type] = memcached_simple_hash('going','
				SELECT PARTYID, MAYBE
				FROM going
				WHERE USERID = '.$userid,
				HALF_HOUR,
				'allgoing:'.$userid
			);
			break;

		case GOING_CERTAIN:
		case GOING_MAYBE:
			if (false === ($anygoing = $__going[$userid][GOING_ANY] ?? get_all_going($userid/*, GOING_ANY */))) {
				$going = [false, false];
			} else {
				$going = [[], []];
				foreach ($anygoing as $partyid => $maybe) {
					$going[$maybe][$partyid] = true;
				}
			}
			$__going[$userid][GOING_MAYBE]   = $going[1];
			$__going[$userid][GOING_CERTAIN] = $going[0];
			break;
		}
	}
	return $__going[$userid][$type];
}

function going_to_party_type(int|array $partyarg, int $userid = CURRENTUSERID): array|int|false {
	if (CURRENTUSERID <= 1) {
		if (is_array($partyarg)) {
			return [];
		}
		return false;
	}
	$going = get_all_going($userid);
	if (is_array($partyarg)) {
		$assoc = !isset($partyarg[0]);
		$goings = [];
		foreach ($partyarg as $id => $val) {
			if (!$assoc) {
				$id = $val;
			}
			if (isset($going[$id])) {
				if ($going[$id]) {
					$new_going = GOING_MAYBE;
				} else {
					$new_going = GOING_CERTAIN;
				}
			} else {
				$new_going = NOT_GOING;
			}
			$goings[$id] = $new_going;
		}
		return $goings;
	}
	if (isset($going[$partyarg])) {
		if ($going[$partyarg]) {
			return GOING_MAYBE;
		}
		return GOING_CERTAIN;
	}
	return NOT_GOING;
}

function going_to_party_askonce(int $partyid): bool {
	return all_going_to_party_askonce($partyid) === GOING_CERTAIN;
}

function going_to_party_nocache(int $partyid): int {
	return all_going_to_party_askonce($partyid, true);
}

function all_going_to_party_askonce(int $partyid, bool $nocache = false): int {
	return get_going_info($partyid, $nocache)[0];
}

function get_going_info(int $partyid, bool $nocache = false): array {
	require_once '_require.inc';
	static $__agoingonce;
	if (isset($__agoingonce[$partyid])) {
		return $__agoingonce[$partyid];
	}
	if (!have_user()
	||	!($res = memcached_single_array('going', "
			SELECT MAYBE, CSTAMP
			FROM going
			WHERE PARTYID = $partyid
			  AND USERID = ".CURRENTUSERID,
			TEN_MINUTES,
			'user:'.CURRENTUSERID.',goinginfo:'.$partyid,
			$nocache ? DB_FRESHEN_MEMCACHE : 0))
	) {
		return $__agoingonce[$partyid] = [NOT_GOING, 0];
	}
	return $__agoingonce[$partyid] = [$res[0] ? GOING_MAYBE : GOING_CERTAIN, $res[1]];
}

function have_going(): bool {
	static $__havegoing = (bool)memcached_single_int('going', '
		SELECT 1
		FROM going
		WHERE USERID = '.CURRENTUSERID.'
		LIMIT 1',
		key: 'havegoing:'.CURRENTUSERID
	)	?? false;
	return $__havegoing;
}

function flush_going(int|array|null $partyarg = null, int|array|null $userarg = null): void {
	if ($userarg
	&&	!is_array($userarg)
	) {
		$userarg = [$userarg];
	}
	if ($partyarg
	&&	!is_array($partyarg)
	) {
		$partyarg = [$partyarg];
	}
	if ($partyarg) {
		foreach ($partyarg as $partyid) {
			memcached_delete('partygoings:'.$partyid);
			if ($userarg) {
				foreach ($userarg as $userid) {
					memcached_delete('user:'.$userid.',goinginfo:'.$partyid);
				}
			}
		}
	}
	if ($userarg) {
		foreach ($userarg as $userid) {
			memcached_delete('havegoing:'.$userid);
			memcached_delete('allgoing:'.$userid);
			if ($partyarg) {
				foreach ($partyarg as $partyid) {
					memcached_delete('user:'.$userid.',goingto:'.$partyid);
				}
			}
		}
	}
}
