<?php

function get_video_content_types(array $spec): array {
	return $spec['CONTENTTYPE']->enum;
}

function have_video_contenttype(array &$array, string $field): string|false {
	return require_video_contenttype($array, $field, dont_warn: true);
}

function require_video_contenttype(array &$array, string $field, bool $dont_warn = false): string|false {
	return ($dont_warn ? 'have_enum' : 'require_enum')($array, $field, 'video', 'CONTENTTYPE');
}
