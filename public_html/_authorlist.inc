<?php

define('REVIEW_AUTHOR',		0);
define('INTERVIEW_AUTHOR',	1);
define('CARTOON_AUTHOR',	2);

function authorlist_display_select($selected = 0,$item = null) {
	include_js('js/authorlist');
	?><select name="BYUSERID" onchange="authorlist_onChange(this);"><?
		?><option value="0"></option><?
		$found = authorlist_show_options($selected,true,$item);
	?></select><?
	require_once '_fillelementid.inc';
	?><span id="byuserid_explicit"<?
	if ($found !== 'X') {
		?> class="hidden"<?
	}
	?>><?
	show_elementid_input('user', $selected, [
		'id'       => 'byuserid_explicit_id',
		'name'     => 'BYUSERID',
		'disabled' => $found !== 'X',
		'value'    => $found === 'X' ? $selected : null,
	]);
	?></span><?
	if (isset($item['AUTHOR'])) {
		show_input([
			'name'		=> 'AUTHOR',
			'type'		=> 'text',
			'maxlength'	=> 255,
			'class'		=> 'regular'.($found === 'Y' ? null : ' hidden'),
			'value'		=> $item['AUTHOR'],
			'disabled'	=> $found !== 'Y',
			'allow-bad'	=> true
		]);
	}
}
function authorlist_show_options($selected = 0,$other = false,$item = null) {
	require_once '_creator.inc';
	$element = $_REQUEST['sELEMENT'];
	$author = get_creator($element);
	$userlist = db_rowuse_array(array('rights','user_account',$element),(
		$selected
		?	'(
			SELECT '.$selected.' AS USERID,NICK,4 AS POS
			FROM user_account
			WHERE USERID='.$selected.'
			) UNION '
		:	null
		).'(
		SELECT DISTINCT USERID,NICK,3 AS POS
		FROM rights
		JOIN user_account USING (USERID)
		WHERE `PORTION`="'.$element.'")
		UNION (
		SELECT DISTINCT USERID,NICK,1 AS POS
		FROM rights
		JOIN user_account USING (USERID)
		WHERE `PORTION`="'.$author.'")
		UNION (
		SELECT DISTINCT BYUSERID,NICK,2 AS POS
		FROM '.$element.' AS element
		JOIN user_account ON user_account.USERID=BYUSERID
		WHERE element.CSTAMP>'.(TODAYSTAMP-365*24*3600).')
		ORDER BY POS ASC,NICK ASC'
	);
	if ($userlist) {
		$pos = 0;
		foreach ($userlist as $user) {
			$userid = $user['USERID'];
			if ($user['POS'] != 3
			&&	isset($done[$userid])
			) {
				continue;
			}
			if ($pos != $user['POS']) {
				if ($pos) {
					?></optgroup><?
				}
				?><optgroup label="<?
				switch ($user['POS']) {
					case 1: echo __C('creators:'.$element); break;
					case 2: echo __C('authorlist:previous_authors'); break;
					case 3: echo __C('authorlist:admins'); break;
					case 4: echo __C('authorlist:as_stored'); break;
				}
				?>"><?
				$pos = $user['POS'];
			} 
			?><option<?
			if ($selected == $userid) {
				$found = $user['POS'] == 4 ? 'X' : true;
				?> selected="selected"<?
			}
			?> value="<?= $userid;
			?>"><?= escape_specials($user['NICK']);
			?></option><?
			$done[$userid] = true;
		}
		?></optgroup><?
		if ($other) {
			?><optgroup label="<?= __C('form:select:other_option'); ?>&hellip;"><?
			?><option<?
			if (!isset($found)
			&&	$selected
			) {
				$found = 'X';
				?> selected="selected"<?
			}
			?> value="X">USERID</option><?
			if (isset($item['AUTHOR'])) {
				?><option<?
				if (!isset($found)
				&&	!empty($item['AUTHOR'])
				) {
					$found = 'Y';
					?> selected="selected"<?
				}
				?> value="Y"><?= Eelement_name('name') ?></option><?
			}
			?></optgroup><?
		}
	}
	return isset($found) ? $found : false;
}
