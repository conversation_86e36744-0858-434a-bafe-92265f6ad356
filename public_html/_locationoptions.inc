<?php

function show_location_options(int $selected = 0, int $skip = 0): void {
	require_once '_namedoubles.inc';
	if (!($locations = db_rowuse_array(['location','city','country'],"
		SELECT	LOCATIONID,location.NAME, ACCEPTED, location.USERID, DEAD,FOLLOWUPID, TITLE,
				CITYID, city.NAME AS CITY_NAME,
				COUNTRYID
		FROM location
		LEFT JOIN city USING (CITYID)
		LEFT JOIN country USING (COUNTRYID)
		ORDER BY IF(COUNTRYID = 1, '1', country.NAME), COUNTRYID, location.ORDERNAME"))
	) {
		return;
	}
	generate_name_doubles($locations, 'NAME', 'location');

	$is_admin = have_admin('location');
	$countryid = -1;

	foreach ($locations as $location) {
	/** @noinspection PhpRedundantOptionalArgumentInspection */
	extract($location, \EXTR_OVERWRITE);
		if ($countryid !== $COUNTRYID) {
			if ($countryid !== -1) {
				?></optgroup><?
			}
			$countryid = $COUNTRYID;
			?><optgroup label="<?=
				$CITYID
			?	escape_specials(get_element_title('country',$countryid))
			:	Eelement_plural_name('boat')
			?>"><?
		}
		if ($ACCEPTED
		||	$is_admin
		||	CURRENTUSERID === $USERID
		) {
			?><option value="<?= $LOCATIONID ?>"<?
			if ($skip
			&&	$skip === $LOCATIONID
			) {
				?> disabled<?
			}
			if ($selected
			&&	$selected === $LOCATIONID
			) {
				?> selected<?
			}
			?>><?
			echo escape_utf8($NAME);
			show_dead_option($location);
			if ($stuff = preg_replace('"('.preg_quote($NAME, '"').')"u', '', $TITLE)) {
				?> (<?= escape_utf8(utf8_mytrim($stuff)) ?>)<?
			}
			if ($CITYID) {
				?> (<?= escape_utf8($CITY_NAME) ?>)<?
			}
			if (!$ACCEPTED) {
				?> (<?= __('attrib:not_validated_yet') ?>)<?
			}
			?></option><?
		}
	}
	if ($countryid !== -1) {
		?></optgroup><?
	}
}
