<?php

declare(strict_types=1);

function show_geotag(array $item): void {
	if (empty($item['LAT'])) {
		return;
	}
	$latitude_and_longitude =
			($rounded_latitude  = round($item['LAT'], 6)).
		','.($rounded_longitude = round($item['LON'], 6));

	# NOTE: itemprop=geo not valid in HEAD because SPAN is not valid in HEAD
	/* ?><span itemprop="geo" itemscope itemtype="https://schema.org/GeoCoordinates"><?
		?><meta itemprop="latitude"  content="<?= $rounded_latitude  ?>" /><?
		?><meta itemprop="longitude" content="<?= $rounded_longitude ?>" /><?
	?></span><? */
	?><meta name="ICBM" content="<?= $latitude_and_longitude ?>" /><?
	?><meta name="geo.position" content="<?= $latitude_and_longitude ?>" /><?
	if ($item['CITY_NAME']) {
		?><meta name="geo.placename" content="<?= escape_utf8($item['CITY_NAME']);
		if ($item['COUNTRYID']) {
			?>, <?= /** @noinspection UnnecessarySemicolonInspection */
			escape_specials(get_element_title('country',$item['COUNTRYID']));
		}
		?>" /><?
	}
}
