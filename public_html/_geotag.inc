<?php

declare(strict_types=1);

function show_geotag(array $item): void {
	if (empty($item['LAT'])) {
		return;
	}
	# NOTE: itemprop=geo not valid in HEAD because SPAN is not valid in HEAD
	$latitude_and_longitude = "{$item['LAT']},{$item['LON']}"
	?><meta name="ICBM" content="<?= $latitude_and_longitude ?>" /><?
	?><meta name="geo.position" content="<?= $latitude_and_longitude ?>" /><?
	if ($item['CITY_NAME']) {
		?><meta name="geo.placename" content="<?= escape_utf8($item['CITY_NAME']);
		if ($item['COUNTRYID']) {
			?>, <?= /** @noinspection UnnecessarySemicolonInspection */
			__('country:'.$item['COUNTRYID']);
		}
		?>" /><?
	}
}
