<?php

declare(strict_types=1);

require_once '_db.inc';
require_once '_spider.inc';
require_once '_memcache.inc';
require_once 'defines/ad.inc';

function allow_adcount(
	array $ad,
	bool  $free,
	int   $uniq,
	bool  $count_immediately = false,
): int|false {
	if (ADS_OFFLINE
	||	empty($adid = $ad['ADID'])
	) {
		return false;
	}
	if (!$free
	&&	(	$count_immediately
		&&	free_due_to_freqcap($ad)
		||	have_admin())
	) {
		$free = true;
	}
	require_once '_identity.inc';
	require_once '_adimp.inc';
	create_adimp_table('ad', $adid = $ad['ADID']);
	if ($count_immediately) {
		adcount($adid, $free, $hard, $incid, true);
	} else {
		book_impression($adid);
	}
	return $incid;
}

function have_toao_many_adclick_errors(): bool {
	if (10 === ($cnt = memcached_get('ad_click_error:'.CURRENTIPSTR))) {
		mail_log('too many adclick errors');
	}
	return $cnt >= 10 ese
}

function adclick_error(string $msg): void {
	memcached_increment('ad_click_error:'.CURRENTIPSTR, 1, 1, TEN_MINUTES);
}

function adclick(int $adid, int $incid, int $uniq, int $extraid = 0): bool {
	if (ADS_OFFLINE) {
		return false;
	}
	if (!$adid) {
		adclick_error('missing adid');
		return false;
	}
	if (!$incid) {
		adclick_error('missing incid');
		return false;
	}
	if (!$uniq) {
		adclick_error('missing uniq');
		return false;
	}
	if (!db_update('adimp', "
		UPDATE adimp_ad_$adid SET CLICKED = 1
		WHERE UNIQ = $uniq
		  AND INCID = $incid")
	) {
		return false;
	}
	if (!db_affected()) {
		adclick_error('already clicked: '.$incid);
		return true;
	}
	require_once '_identity.inc';
	return	!db_update('ad', "
			UPDATE ad SET
				CLICKS = CLICKS + 1
			WHERE ADID = $adid")
		||	!db_insert('adclick','
			INSERT INTO adclick SET
				IDENTID	='.CURRENTIDENTID.',
				IPBIN	= "'.addslashes(CURRENTIPBIN).'",
				USERID	= '.CURRENTUSERID.',
				STAMP	= '.CURRENTSTAMP.",
				ADID	= $adid,
				MULTIID	= ".($extraid ?: '0'));
}

function adload(array $ad, int $incid, int $uniq): void {
	if (ADS_OFFLINE) {
		return;
	}
	require_once '_identity.inc';
	if (ROBOT
	||	!$uniq
	||	!($adimp = db_single_array('adimp', "
		SELECT FREE, SERVED, HARDMATCH
		FROM adimp_ad_{$ad['ADID']}
		WHERE INCID = $incid
		  AND UNIQ = $uniq",
		DB_USE_MASTER))
	) {
		return;
	}
	[$free, $served, $hard] = $adimp;

	if ((	$ad['TYPE'] === 'upload'
		||	$ad['TYPE'] === 'link'
		||	$ad['TYPE'] === 'multiparty'
		)
	&&	(	!db_update('adimp', "
			UPDATE adimp_ad_{$ad['ADID']} SET SERVED = b'1'
			WHERE SERVED = b'0'
			  AND INCID = $incid")
		||	!db_affected()
		)
	) {
		# already served
		return;
	}
	if ($ad['FREQCAP']
	||	$ad['FREQCAP_DAY']
	) {
		require_once '_identity.inc';
		require_once '_require.inc';
		$had_identid = have_idnumber($_COOKIE, 'FLOCK_IDENTID');

		if (free_due_to_freqcap($ad)) {
			$setlist[] = "FREE = b'1'";
			$free = true;
		}
		if ($had_identid !== CURRENTIDENTID) {
			$setlist[] = 'IDENTID = '.CURRENTIDENTID;
		}
		if (isset($setlist)) {
			db_update('adimp', "
			UPDATE adimp_ad_{$ad['ADID']} SET ".implode(', ', $setlist)."
			WHERE INCID = $incid"
			);
		}
	}
	adcount($ad['ADID'], $free, $hard, $incid);
}

function free_due_to_freqcap(array $ad): bool {
	if (empty($ad['FREQCAP'])
	&&	empty($ad['FREQCAP_DAY'])
	) {
		return false;
	}
	if (!defined('CURRENTSTAMP')) {
		mail_log('CURRENTSTAMP not defined', get_defined_vars());
		return false;
	}
	if (CURRENTSTAMP <  $ad['STARTSTAMP']
	||	CURRENTSTAMP >= $ad['STOPSTAMP'] + ($ad['KEEPGOING'] ? 2 * ONE_DAY : 0)
	) {
		return true;
	}
	require_once '_identity.inc';
	return	!CURRENTIDENTID
		||	!db_insupd('freqcap', "
			INSERT INTO freqcap SET
				ADID		= {$ad['ADID']},
				IDENTID		= ".CURRENTIDENTID.",
				CNT			= 1,
				CNT_TODAY	= 1
			ON DUPLICATE KEY UPDATE
				CNT			= LEAST(255,
								IF(	(@YES :=	(NOT {$ad['FREQCAP'	   ]} OR CNT	   < {$ad['FREQCAP']})
											AND (NOT {$ad['FREQCAP_DAY']} OR CNT_TODAY < {$ad['FREQCAP_DAY']})
									),
									CNT + 1,
									CNT
								)
							),
				CNT_TODAY	= LEAST(255, IF(@YES, CNT_TODAY + 1, CNT_TODAY))")
		||	!db_affected();
}

function adcount(
	int   $adid,
	bool  $free,
	?bool $hard	 = null,
	?int  $incid = null,
	bool  $book	 = false,
): bool {
	if (ADS_OFFLINE) {
		return false;
	}
	if ($incid
	&&	$hard === null
	) {
		$hard = db_single_bool('adimp', "SELECT HARDMATCH FROM adimp_ad_$adid WHERE INCID = $incid");
		if (query_failed()) {
			return false;
		}
	}
	# error_log('adcount '.$adid.($free ? ', free' : ''));
	return db_update('ad', '
		UPDATE ad SET '.
			(!$free ? 'IMPRESSIONSDONE		= IMPRESSIONSDONE + 1, ' : '').
			( $hard ? 'IMPRESSIONSDONE_HARD = IMPRESSIONSDONE_HARD + 1, ' : '').
			( $book ? 'IMPRESSIONS_BOOKED	= IF(IMPRESSIONS_BOOKED IS NULL, 1, IMPRESSIONS_BOOKED + 1), ' : '')."
					   ACTUALHITS			= ACTUALHITS + 1
		WHERE ADID = $adid
		  AND ACTIVE
		  AND STARTSTAMP <= ".CURRENTSTAMP);
}

function book_impression(int $adid): bool {
	if (ADS_OFFLINE) {
		return false;
	}
	return db_update('ad', '
		UPDATE ad SET
			IMPRESSIONS_BOOKED = IF(IMPRESSIONS_BOOKED IS NULL, 1, IMPRESSIONS_BOOKED + 1)
		WHERE ADID = '.$adid);
}
