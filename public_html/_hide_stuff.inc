<?php

declare(strict_types=1);

function noindex_if_needed(array $item): bool {
	static $__noindex;
	if ($__noindex === null) {
		if ($item['HIDDEN_FOR_SEARCH_ENGINES']
#		&&	(require_once '_lastparty.inc')
#				# no event:
#		&&	(	null === ($lastparty = get_last_party())
#				# event is more than 2 years agop
#			||	$lastparty
#			&&	$lastparty['STAMP'] < CURRENTSTAMP - 2 * ONE_YEAR
#			)
		) {
			# set noindex if there is no
			robot_action('noindex');
			return $__noindex = true;
		}
	}
	return $__noindex = false;
}

function show_noindex_if_needed(array $item): void {
	if ($item['HIDDEN_FOR_SEARCH_ENGINES']
	&&	have_admin($_REQUEST['sELEMENT'])
	&&	have_admin('hide_stuff')
	) {
		?><div class="<?
		if (!noindex_if_needed($item)) {
			?>unavailable <?	
		}
		?>warning block"><?= __('status:hidden_for_search_engines') ?></div><?
	}
}
