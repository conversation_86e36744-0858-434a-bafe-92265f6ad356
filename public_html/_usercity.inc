<?php

declare(strict_types=1);

function user_check_city(string $city, array &$setlist, ?int $countryid = null): bool {
	require_once '_cityvalidation.inc';
	require_once '_counthit.inc';

	$countryid ??= (int)$_POST['COUNTRYID'];

	$city = preg_replace('"[\d\s[:punct:]]+"u', '', $city);
	$city_slashed = addslashes($city);

	if (false === ($city_invalid = db_single_assoc('cityinvalid', "
		SELECT CINVID, TYPE
		FROM cityinvalid
		WHERE BINARY NAME = '$city_slashed'"))
	) {
		return false;
	}
	if ($city_invalid) {
		// mark invalid right away
		set_city_parts($setlist, $city_invalid['TYPE'], $city, 0, $countryid);
		counthit('cityinvalid', $city_invalid['CINVID']);
		return true;
	}
	if (false === ($city_auto_rename = db_single_assoc(['cityautorename', 'city'], "
		SELECT cityautorename.CITYID AS ID, ALIASID, TYPE, city.COUNTRYID
		FROM cityautorename
		LEFT JOIN city USING (CITYID)
		WHERE (TYPE = 'country' OR city.CITYID IS NOT NULL AND NOT DELETED)
		  AND ALIAS = '$city_slashed'"))
	) {
		return false;
	}
	if ($city_auto_rename) {
		if ($city_auto_rename['TYPE'] === 'city') {
			set_city_parts($setlist, 0, '', $city_auto_rename['ID'], $city_auto_rename['COUNTRYID']);
		} else {
			set_city_parts($setlist, 0, '', 0, $city_auto_rename['ID']);
		}
		counthit('cityautorename', $city_auto_rename['ALIASID']);
		return true;
	}
	$option_parts = [
		"	SELECT CITYID, NAME, COUNTRYID, 1 AS RANK, 'city' AS TYPE
			FROM city
			WHERE NOT DELETED
			  AND NAME = '$city_slashed'",

		"	SELECT CITYID, NAME, COUNTRYID, 2 AS RANK, 'city' AS TYPE
			FROM city
			WHERE NOT DELETED
			  AND NAME != '$city_slashed'
			  AND CLEANNAME != ''
			  AND CLEANNAME = '$city_slashed'",

		"	SELECT 0 AS CITYID, NAME, COUNTRYID, 3 AS RANK, 'country' AS TYPE
			FROM country
			WHERE NAME = '$city_slashed'",

		"	SELECT 0 AS CITYID, NAME, COUNTRYID, 3 AS RANK, 'country' AS TYPE
			FROM country
			WHERE NAME != '$city_slashed'
			  AND CLEANNAME != ''
			  AND CLEANNAME = '$city_slashed'"
	];

	if (false === ($options = db_rowuse_array(['city','country'], '
		('.implode(') UNION (', $option_parts).')
		ORDER BY RANK'))
	) {
		return false;
	}
	if (!$options) {
		set_city_parts($setlist, CITY_CHECK, $city, 0, $countryid);
		return true;
	}
	if (!isset($options[1])) {
		$chosen = $options[0];
		set_city_parts($setlist, 0, '', $chosen['CITYID'], $chosen['COUNTRYID']);
		return true;
	}
	foreach ($options as /* $i => */ $option) {
		if (!isset($previous_option)) {
			$previous_option = $option;

		} elseif ($option['RANK'] > $previous_option['RANK']) {
			# rest is unsuitable
			break;
		}
		$real_options[$option['TYPE']][] = $option;
	}
	if (isset($real_options)) {
		foreach ($real_options as $type => $options) {
			if (!isset($options[1])) {
				$chosen = $options[0];

			} elseif ($type === 'city') {
				$cityids = array_column($options, 'CITYID');
				$chosen = db_single_assoc(['user', 'city'], '
					SELECT CITYID, city.COUNTRYID, COUNT(*) AS CNT
					FROM city
					JOIN user USING (CITYID)
					WHERE CITYID IN ('.implode(', ', $cityids).')
					GROUP BY CITYID
					ORDER BY CNT DESC
					LIMIT 1'
				);
			} else {
				$countryids = array_unique(array_column($options, 'COUNTRYID'));
				$chosen = db_single_assoc(['user', 'city'],'
					SELECT 0 AS CITYID, city.COUNTRYID, COUNT(*) AS CNT
					FROM city
					JOIN user USING (CITYID)
					WHERE city.COUNTRYID IN ('.implode(', ', $countryids).')
					GROUP BY city.COUNTRYID
					ORDER BY CNT DESC
					LIMIT 1'
				);
			}
			if ($chosen === false) {
				return false;
			}
			if ($chosen) {
				set_city_parts($setlist, 0, '', $chosen['CITYID'], $chosen['COUNTRYID']);
				return true;
			}
		}
	}
	set_city_parts($setlist, CITY_CHECK, $city, 0, $countryid);
	return true;
}

function set_city_parts(
	array	&$setlist,
	int		$invalid_city,
	string	$city,
	int		$cityid,
	int		$countryid,
): void {
	$setlist[] = "
		INVALID_CITY	= $invalid_city,
		CITYID			= $cityid,
		COUNTRYID		= $countryid,
		CITY			= ".($city ? '"'.addslashes($city).'"' : 'NULL');
}
