<?php

function preamble(): void {
	if ($_REQUEST['sID'] === 7222) {
		moved_permanently(get_element_href('location', 22302));
	}
	require_once '_status_overlay.inc';
}

function display_header(): void {
	require_once '_feed.inc';
	show_feed('agenda', FEED_HEADER);
}

function perform_commit(): ?bool {
	switch ($_REQUEST['ACTION']) {
	default:
	case null:											return null;
	case 'accept':
	case 'unaccept':	require_once '_accept.inc';		return item_set_accept($_REQUEST['ACTION']);
	case 'commit':										return item_commit();
	case 'remove':		require_once '_remove.inc';		return remove_element();
	case 'employees':	require_once '_employees.inc';	return employee_action();
	case 'combine':		require_once '_combine.inc';	return combine_element();
	case 'removeartist':require_once '_artistpool.inc';	return artistpool_remove_artist();
	case 'setstatus':	require_once '_artistpool.inc';	return artistpool_status_artist();
	case 'names':		require_once '_itemnames.inc';	return item_commit_names();
	}
}

function display_body(): void {
	switch ($_REQUEST['ACTION']) {
	default:
		require_once '_search_via_url.inc';
		search_via_url();
		not_found();
		return;

	case 'searchresult':
	case 'remove':
	case null:								organization_display_overview(); return;
	case 'archive':		$_REQUEST['sID'] ? 	organization_display_single() : organization_display_overview(); return;

	case 'news':
	case 'camera':
	case 'photos':
	case 'commit':
	case 'comments':
	case 'comment':
	case 'ratings':
	case 'rating':
	case 'rate':
	case 'reports':
	case 'votes':
	case 'combine':
	case 'description':
	case 'single':										 organization_display_single();	 return;
	case 'fans':		ROBOT ? not_found() : 			 organization_display_single();	 return;
	case 'removeartist':
	case 'setstatus':
	case 'artists':		require_once '_artistpool.inc';	 artistpool_display_overview();	return;
	case 'bioform':		require_once '_bio.inc'; 		 show_bio_form();				return;
	case 'combinewith':	require_once '_combine.inc';	 show_combine_with();			return;
	case 'names':		require_once '_itemnames.inc';	 item_display_names_overview();	return;
	case 'register':
	case 'form':										 organization_display_form();	return;
	case 'needupdate':	require_once '_needupdates.inc'; show_needupdates(); 			return;
	case 'employees':	(	!$GLOBALS['commitresult']
						&&	$_REQUEST['SUBACTION'] !== 'form'
						) ?	display_employees() : display_employee_form(); return;
	}
}

function organization_display_overview(): void {
	layout_show_section_header();

	main_menu();

	$name = require_something_trim_clean_or_none($_REQUEST, 'NAME', utf8: true);

	?><form accept-charset="utf-8" onsubmit="return submitForm(this);" method="get" action="/organization/searchresult"><?
	?><div class="block"><?
	?><input placeholder="<?= __('action:search_for_organization') ?>" type="search" autosave="organizationname" name="NAME" id="organizationname" autofocus required<?
	if ($name) {
		?> value="<?= escape_utf8($name) ?>"<?
	}
	?>> <?
	?><input type="submit" value="<?= __('action:search') ?>" /></div><?
	?></form><?
	if ($_REQUEST['ACTION'] === 'searchresult') {
		if ($name) {
			?><div class="bold block"><?= Eelement_name('search_term'); ?>: &quot;<?= escape_utf8($name) ?>&quot;</div><?
			require_once '_organizationlist.inc';
			$organizationlist = new _organizationlist;
			$organizationlist->show_city = true;
			$organizationlist->show_partycnt = true;
			$organizationlist->show_last_date = true;
			$organizationlist->name_like($name);
			if ($organizationlist->query()) {
				if ($organizationlist->size()) {
					$organizationlist->display();
				} else {
					?><div class="block"><? __('search:info:nothing_found_LINE') ?></div><?
				}
			}
		}
	} elseif (
		!$_REQUEST['ACTION']
	||	 $_REQUEST['ACTION'] === 'remove'
	) {
		if (have_user()) {
			require_once '_favourite.inc';
			show_favourites();
		}
	} else {
		not_found();
		return;
	}
}

function main_menu($orgmenu = null): void {
	layout_open_menu();
	layout_menuitem(Eelement_name('overview'),'/organization',!$_REQUEST['ACTION'] && !isset($_REQUEST['ARCHIVE']));
	if (have_user() && $_REQUEST['ACTION'] != 'register') {
		layout_continue_menu();
		layout_menuitem(__C('action:add_organization'),'/organization/register');
	}
	layout_close_menu();
}

function single_menu(array $organization): void {
	require_once '_element_access.inc';

	$is_admin = have_admin('organization');
	$organizationid = $organization['ORGANIZATIONID'];
	if (!$is_admin
	&&	!may_change_element('organization', $organizationid, CURRENTUSERID, false, $organization)
	) {
		return;
	}
	$orghref = '/organization/'.$organizationid;
	layout_open_menu();
	layout_menuitem(__C('action:change'),$orghref.'/form');
	layout_menuitem(__C('action:change_information'),$orghref.'/bioform');
	if ($is_admin) {
		if ($organization['ACCEPTED'])
			layout_menuitem(__C('action:reject'),$orghref.'/unaccept');
		else	layout_menuitem(__C('action:accept'),$orghref.'/accept');
		layout_menuitem(Eelement_plural_name('name'),$orghref.'/names');
		layout_menuitem(Eelement_plural_name('employee'),$orghref.'/employees');
		layout_menuitem(Eelement_plural_name('artist'),$orghref.'/artists');
		layout_continue_menu();
		show_element_menuitems();
	}
	layout_close_menu();
	if ($is_admin) {
		require_once '_element_to_ticket_menu.inc';
		layout_open_menu();
		show_element_to_ticket_menu('organization',$organizationid,$organization);
		layout_close_menu();
	}
}

function organization_display_single() {
	require_once '_organizationpart.inc';
	require_once '_vote.inc';
	require_once '_presence.inc';

	if (!($organizationid = require_idnumber($_REQUEST,'sID'))) {
		return;
	}

	if ($locationid = isset($_REQUEST['SHOW']) ? 0 : get_hidden_orgs($organizationid)) {
		moved_permanently(get_element_href('location', $locationid));
	}

	if (!($organization = memcached_single_assoc_if_not_admin(
		'organization',
		['organization','contact_ticket','subof'], '
		SELECT	ORGANIZATIONID,HIDDEN_FOR_SEARCH_ENGINES,ADMIN_TEXT,ADMIN_PARTY_TEXT,ACCEPTED, organization.DELETED, MIN_AGE,
				organization.NAME,DEADSTAMP,EMAIL,SITE,organization.MSTAMP,organization.MUSERID,organization.CSTAMP,organization.USERID,
				ADDRESS_HIDDEN,ADDRESS,COUNTRYID,country.SHORT,CITYID,ZIPCODE,POBOX,POBOX_ZIPCODE,POBOX_CITYID,PHONE,KVKNUM,FOLLOWUPID,ACTIVITIES,
				FOUNDED,CAMREQ_NAME,CAMREQ_EMAIL,CAMREQ_USERID,EROTIC,NEEDUPDATE,COPY_GENRES,
			TICKETID,contact_ticket.STATUS AS TICKET_STATUS,
			(SELECT GROUP_CONCAT(PARENTID) FROM subof JOIN organization ON PARENTID=ORGANIZATIONID WHERE   ACTIVE=1 AND FOLLOWUPID=0  AND ELEMENT="organization" AND PELEMENT="organization" AND CONCEPTID='.$organizationid.') AS PARENTIDS,
			(SELECT GROUP_CONCAT(PARENTID) FROM subof JOIN organization ON PARENTID=ORGANIZATIONID WHERE !(ACTIVE=1 AND FOLLOWUPID=0) AND ELEMENT="organization" AND PELEMENT="organization" AND CONCEPTID='.$organizationid.') AS INACTIVE_PARENTIDS,
			(SELECT GROUP_CONCAT(PARENTID) FROM subof WHERE ACTIVE=1 AND ELEMENT="organization" AND PELEMENT="location" AND CONCEPTID=ORGANIZATIONID) AS LOCATION_PARENTIDS
		FROM organization
		LEFT JOIN contact_ticket USING (TICKETID)
		LEFT JOIN country USING (COUNTRYID)
		WHERE ORGANIZATIONID='.$organizationid,
		TEN_MINUTES
	))) {
		if ($organization !== false) {
			not_found();
		}
		return;
	}

	$organization_admin = have_admin('organization');

	require_once '_hide_stuff.inc';
	noindex_if_needed($organization);

	if (!available_meta()) {
		include_meta('description', __('metad:organization:single',DO_UBBFLAT,['ORGANIZATIONID'=>$organizationid]));
	}

	layout_show_section_header($organization);

	main_menu();

	single_menu($organization);

	#show_check_mark_for_aspiring();

	if (have_user()) {
		require_once '_ticketlist.inc';
		show_connected_tickets();

		if (have_admin('logbook')) {
			require_once '_logbook.inc';
			show_logbook();
		}

		require_once '_favourite.inc';
		show_marking_menu();
	}

	$artist_cnt = memcached_single('artistpool','
		SELECT COUNT(*)
		FROM artistpool
		WHERE ACTIVE = 1
		  AND ELEMENT = "organization"
		  AND ID = '.$organizationid
	);

	require_once '_news.inc';
	[$total_news,$teaser_news] = get_news_counts();
	$cnts = memcached_single_assoc(['connect','party'],'
		SELECT	COUNT(*) AS TOTAL_CNT,
			COUNT(IF(DELETED = 0 AND ACCEPTED = 1 AND CANCELLED = 0 AND MOVEDID = 0, 1, NULL)) AS TOTAL_OK_CNT,
			COUNT(IF(STAMP <='.($pivot = strtotime('-2 days', TODAYSTAMP)).', 1, NULL)) AS PAST_CNT,
			COUNT(IF(STAMP <='.$pivot.' AND DELETED = 0 AND ACCEPTED = 1 AND CANCELLED = 0 AND MOVEDID = 0, 1, NULL)) AS PAST_OK_CNT
		FROM party
		JOIN (	SELECT ASSOCID AS PARTYID
			FROM connect
			WHERE MAINTYPE IN ("organization","orgashost")
			  AND MAINID='.$organizationid.'
			  AND ASSOCTYPE="party"
			UNION
			SELECT PARTYID
			FROM partyarea
			WHERE HOSTEDBYID='.$organizationid.'
		) AS applic USING (PARTYID)',
		TEN_MINUTES, null,
		partylist_refresh_needed() ? DB_FRESHEN_MEMCACHE : 0
	);
	if (!$cnts) {
		return;
	}
	if (!have_admin('party')) {
		$may_future = $cnts['TOTAL_CNT'] - $cnts['PAST_CNT'];
		$cnts['TOTAL_CNT'] = $cnts['TOTAL_OK_CNT'];
		$cnts['PAST_CNT'] = $cnts['PAST_OK_CNT'];
	}
	$cnts['FUTURE_CNT'] = $cnts['TOTAL_CNT'] - $cnts['PAST_CNT'];
	$shootinfo = memcached_single_assoc(['image', 'connect', 'gallery'],'
		SELECT	COUNT(DISTINCT image.GALLERYID) AS SHOOT_CNT,
			COUNT(DISTINCT IF(image.HIDDEN=0,IMGID,NULL)) AS PHOTO_CNT
		FROM connect
			JOIN image ON image.PARTYID=ASSOCID
			JOIN gallery USING (GALLERYID)
		WHERE VISIBLE="yes"
		  AND MAINTYPE="organization" AND ASSOCTYPE="party"
		  AND MAINID='.$organizationid
	);
	if (!$shootinfo) {
		return;
	}
	require_once '_background_queries.inc';
	$visitorcnts = get_bgquery(BGQRY_VISITORS,'organization',$organizationid);
	if ($visitorcnts === false) {
		return;
	}
	$am_employee = am_employee('organization',$organizationid);
	if ($am_employee
	||	have_admin('camerarequest')
	) {
		$caminfo = memcached_single(['camerarequest', 'connect', 'contact_ticket'],'
			SELECT COUNT(DISTINCT PARTYID)
			FROM connect
				LEFT JOIN camerarequest ON PARTYID=ASSOCID
				LEFT JOIN contact_ticket ON ELEMENT="camerarequest" AND ID=ASSOCID
			WHERE MAINTYPE="organization"
			  AND MAINID='.$organizationid.'
			  AND ASSOCTYPE="party"
			  AND (NOT ISNULL(PARTYID) OR NOT ISNULL(ID))',
			TEN_MINUTES
		);
	} else {
		$caminfo = false;
	}

	require_once '_admin_texts.inc';
	show_admin_texts('organization', $organizationid, $organization);

	require_once '_favourite.inc';
	$fancnt = get_fan_count();
	if ($fancnt === false) {
		return;
	}

	require_once '_star.inc';
	require_once '_favourite.inc';
	$is_fav = check_favourite();

	require_once '_status_overlay.inc';
	open_status_overlay($organization);

	?><article itemscope itemtype="https://schema.org/LocalBusiness"><?

	?><meta itemprop="url" content="<?= FULL_HOST,get_element_href('organization',$organizationid) ?>" /><?

	require_once '_drag.inc';
	open_drag();

	layout_open_box($organization['ACCEPTED'] ? 'organization' : 'unaccepted organization');
	layout_open_box_header($is_fav ? BOX_HEADER_FAN : 0, 'box-header');
	?><h1 itemprop="name"><?
	echo escape_utf8($organization['NAME']);
	?></h1><?

	echo ($dead = get_dead($organization)),
		 get_header_star();

	if ($organization_admin) {
		if ($organization['NEEDUPDATE']) {
			require_once '_needupdates.inc';
			?> <?
			show_new_profile_info('organization', $organizationid);
		}
		require_once '_needupdates.inc';
		show_needupdate_mark($organization);
		require_once '_checkup.inc';
		show_checkup_mark($organization);
	}

	if ($cnts['PAST_CNT']
	||	$total_news
	||	$caminfo
	||	$shootinfo['SHOOT_CNT']
	||	$fancnt
	) {
		layout_continue_box_header();
		layout_open_menu(MENU_IN_HEADER);
		// CAMERAREQUESTS
		if ($caminfo) {
			layout_open_menuitem();
			[$linkopen,$linkclose] = get_action_open_close('camera');
			echo $linkopen,element_name('camerarequest',$caminfo),$linkclose;
			layout_close_menuitem();
		}
		// SHOOTS
		if ($shootinfo['SHOOT_CNT']) {
			layout_open_menuitem();
			[$linkopen,$linkclose] = get_action_open_close('photos');
			echo $linkopen,element_plural_name('photo'),$linkclose;
			layout_close_menuitem();
		}
		// NEWS
		if ($total_news) {
			layout_open_menuitem();
			if ($total_news == $teaser_news) {
				$linkopen = '<a href="#news">';
				$linkclose = '</a>';
			} else {
				[$linkopen,$linkclose] = get_action_open_close('news');
			}
			echo $linkopen,element_name('news'),$linkclose;
			layout_close_menuitem();
		}
		# FANS
		if ($fancnt) {
			layout_open_menuitem();
			[$fanlinkopen,$fanlinkclose] = get_action_open_close('fans');
			echo $fanlinkopen,$fancnt,' ',element_name('fan',$fancnt),$fanlinkclose;
			layout_close_menuitem();
		}
		layout_close_menu();
	}
	layout_close_box_header();

	require_once '_presence.inc';
	show_presence_search($organization);

	require_once '_appic.inc';
	show_appic_link();

	show_noindex_if_needed($organization);

	require_once 'defines/processlist.inc';
	PROCLIST_DO_PRIORITIES && show_proclist_priority();

	if (  $organization['DEADSTAMP']
	&&	empty($organization['FOLLOWUPID'])
	) {
		?><div class="block"><?= __('organization:info:ceased_to_exist_LINE',[
				'DATE'	=> $organization['DEADSTAMP'] > 1 ? _date_get($organization['DEADSTAMP']) : 0
			]
		);
		?></div><?
	}

	if (have_admin('pixel')) {
		require_once '_pixel.inc';
		show_pixel_status();
	}

	require_once '_uploadimage.inc';
	if ($img = uploadimage_get('organization', $organizationid)) {
		uploadimage_show_from_img(
			$img,
			  UPIMG_SCHEMA
			| UPIMG_SHOW_HISTORY
			| UPIMG_LINK_ORIGINAL
			| (SMALL_SCREEN ? UPIMG_MAX_WIDTH : UPIMG_R)
		);
	}

	if ($organization['ACTIVITIES']) {
		$shows = [];
		foreach ($organization['ACTIVITIES'] = explode_to_hash(',', $organization['ACTIVITIES']) as $func) {
			$shows[] = '<span>'.__('activity:'.$func).'</span>';
		}
		?><div class="activities block"><?
		echo implode(' ', $shows);
		?></div><?

	}

	if ($organization_admin
	&&	$organization['EROTIC']
	) {
		?><div class="block"><span class="erobg"><?= __('erotic:info:mainly_erotic_events') ?></span></div><?
	}

	require_once '_followup.inc';
	show_successors('organization', $organizationid, $organization['FOLLOWUPID']);
	show_parents('organization', $organizationid, 'organization', $organization['PARENTIDS']);
	show_parents('organization', $organizationid, 'location', $organization['LOCATION_PARENTIDS']);
	if ($organization_admin
	&&	$organization['PARENTIDS']
	) {
		show_parents('organization',$organizationid,'organization',$organization['INACTIVE_PARENTIDS'],true);
	}

	$list = new deflist('deflist vtop');

	require_once '_site.inc';
	show_site_and_email_rows($list,$organization);

	if ($organization['ADDRESS_HIDDEN']
	&&	!$organization_admin
	&&	!$am_employee
	) {
		$organization['ADDRESS'] = $organization['ZIPCODE'] = null;
	}

	$show_no_country
	=	have_admin('organization')
	&&	!$organization['CITYID']
	&&	!$organization['COUNTRYID'];

	if ($organization['ADDRESS']
	||	$organization['CITYID']
	||	$organization['COUNTRYID']
	||	$organization['ZIPCODE']
	||	$show_no_country
	) {
		if ($organization['ADDRESS_HIDDEN']) {
			$hidden_open = '<span class="light">';
			$hidden_close = '</span>';
		} else {
			$hidden_open = $hidden_close = null;
		}
		if ($organization['ADDRESS']) {
			$addr[] = $hidden_open.'<span itemprop="streetAddress">'.escape_utf8($organization['ADDRESS']).'</span>'.$hidden_close;
		}
		if ($organization['ZIPCODE']) {
			$addr[] = $hidden_open.'<span itemprop="postalCode">'.escape_utf8($organization['ZIPCODE']).'</span>'.$hidden_close;
		}
		$countryid = $organization['COUNTRYID'];
		if ($cityid = $organization['CITYID']) {
			$city = memcached_city_info($cityid);
			if ($city) {
				$countryid = $city['COUNTRYID'];
			}
		}
		if (!empty($countryid)) {
			require_once '_countryflag.inc';

			ob_start();
			if ($cityid) {
				?><span itemprop="addressLocality"><?
				echo get_element_link('city',$cityid) ?></span><br /><?
			}
			?><span itemprop="addressCountry" itemscope itemtype="https://schema.org/Country"><?
			?><span itemprop="name"><?
			echo get_element_link('country', $countryid);
			?></span><?
			?><meta itemprop="alternateName" content="<?= $city['SHORT'] ?? $organization['SHORT'] ?>" /><?
			?></span><?

			?> <? echo get_country_flag($countryid, 'light', false, false);

			$addr[] = ob_get_clean();
		}
		if ($show_no_country) {
			$addr[] = '<span class="warning">'.__('country:error:no_country_supplied_LINE').'</span>';
		}

		ob_start();
		?><span itemprop="location" itemscope itemtype="https://schema.org/Place"><?
		?><span itemprop="address" itemscope itemtype="https://schema.org/PostalAddress"><?
		echo implode('<br />',$addr);
		$addr = ob_get_clean();
		?></span><?
		?></span><?

		$list->add_row(Eelement_name('address'),$addr);
	}

	if ($organization['POBOX']
	||	$organization['POBOX_ZIPCODE']
	||	$organization['POBOX_CITYID']
	) {
		$addr = [];
		ob_start();
		if ($organization['POBOX']) {
			$addr[] = Eelement_name('pobox').' '.$organization['POBOX'];
		}
		if ($organization['POBOX_ZIPCODE']) {
			$addr[] = escape_utf8($organization['POBOX_ZIPCODE']);
		}
		if ($cityid = $organization['POBOX_CITYID']) {
			$addr[] = get_element_link('city', $organization['POBOX_CITYID']);
		} elseif ($cityid = $organization['CITYID']) {
			$addr[] = get_element_link('city', $organization['CITYID']);
		}
		if ($cityid
		&&	($city = memcached_city_info($cityid))
		&&	$city['COUNTRYID']
		) {
			require_once '_countryflag.inc';
			$addr[] = get_element_link('country', $city['COUNTRYID']).
				  ' '.get_country_flag($city['COUNTRYID'], 'light', false, false);
		}
		echo implode('<br />',$addr);
		$list->add_row(Eelement_name('postal_address'),ob_get_clean());
	}

	require_once '_phone.inc';
	add_phone_to_list($list, $organization, $dead);

	if ($organization['KVKNUM']) {
		require_once '_kvk.inc';
		show_kvk_row($list, $organization);
	}
	if ($organization['FOUNDED']
	&&	$organization['FOUNDED'] !== '0000-00-00'
	) {
		[$y, $m, $d] = explode('-',$organization['FOUNDED']);
		if ($y) {
			$y = (int)$y;
			$m = (int)$m;
			$d = (int)$d;
			ob_start();
			if ($m && $d) {
				_date_display(mktime(0, 0, 0, $m, $d, $y));
			} else {
				if ($m) {
					echo _month_name($m);
					?> <?
				}
				echo $y;
			}
			$list->add_row(Eelement_name('foundation'), ob_get_clean());
		}
	}
	show_presence_row($list);

	require_once '_minimum_age.inc';
	show_minimum_age($list, $organization);

	if (have_admin('party')) {
		require_once '_facebook.inc';
		add_facebook_rows($list);
	}
	if (have_admin('video')) {
		require_once '_youtube.inc';
		add_youtube_rows($list);
	}

	$list->display();

	require_once '_genrelist.inc';
	show_genres_block($organization);

	show_predecessors('organization', $organizationid);

	show_children('organization', $organizationid, 'organization');

	require_once '_itemnames.inc';
	show_item_renames('organization', $organizationid);

	require_once '_alternate_name.inc';
	show_alternate_names('organization', $organizationid, $organization['NAME']);

	require_once '_employees.inc';
	show_employees();

	if (have_admin('relation')) {
		show_same_facebook_connected();

		require_once '_relationlist.inc';
		show_relations();
	}

	# show BIOGRAPHY
	require_once '_bio.inc';
	show_bio();

	require_once '_articles.inc';
	$total_reviews = show_articles('review','organization',$organizationid);

	$reports = memcached_rowuse_array(['report','connect'],'
		SELECT REPORTID,TITLE,USERID,PARTYID,BODY,report.CSTAMP
		FROM report
		JOIN connect ON MAINTYPE="party" AND MAINID=PARTYID AND ASSOCTYPE="organization"
		WHERE ACCEPTED=1
		  AND OFFICIAL=1
		  AND ASSOCID='.$organizationid.'
		  AND PSTAMP>'.(TODAYSTAMP - ONE_YEAR)
	);
	if ($reports) {
		require_once '_reports.inc';
		show_reports($reports);
	}

	layout_display_alteration_note($organization, true);
	layout_close_box();

	if (may_speak($organization)) {
		vote_display_choices();
	}
	require_once '_ratinglist.inc';
	$ratinglist = new _ratinglist;
	$ratinglist->item($organization);
	$ratinglist->show_form();

	if (have_admin(['organization', 'camerarequest'])) {
		require_once '_cameracontacts.inc';
		show_cameracontacts($organization, $am_employee);
	}

	drag_store();

	layout_open_box('organization agenda', 'agenda');

	$future = $cnts['FUTURE_CNT'];
	$past = $cnts['PAST_CNT'];

	if ($show_agenda = $future || $past || (!$future && !$past && !$artist_cnt) || !empty($may_future)) {
		layout_open_box_header();
		?><h2><?

		if (have_user()) {
			echo Eelement_name('agenda');
		} else {
			echo Eelement_name('party_agenda');
			?> <?
			echo escape_utf8($organization['NAME']);
		}
		?></h2><?

		$parts = [];
		if (!ROBOT) {
			$parts[] = get_ical_feed_link();
			if (SHOW_FEED_ICON) {
				$parts[] = get_feed('agenda',FEED_ICON);
			}
		}
		if ($am_employee || have_admin() || have_relation()) {
			ob_start();
			?><a href="/party/form?ORGANIZATIONID=<?= $organizationid ?>"><?= __('action:add_party'); ?></a><?
			$parts[] = ob_get_clean();
		}
		if ($past) {
			[$linkopen, $linkclose] = get_action_open_close('archive', null, element_name('agenda_archive').' '.escape_utf8($organization['NAME']));
			$parts[] = $linkopen.element_name('archive').$linkclose;
		}

		ob_start();
		if (have_admin('party')) {
			require_once '_processlist.inc';
			processlist_show_closer('organization', $organizationid);
		}
		$closer = ob_get_clean();

		if ($parts
		||	$closer
		) {
			layout_continue_box_header();
			?><nav><?= implode(' '.MIDDLE_DOT_ENTITY.' ', $parts), $closer ?></nav><?
		}
		layout_close_box_header();

		if ($future
		||	!empty($may_future)
		) {
			require_once '_partylist.inc';
			$partylist = new _partylist;
			$partylist->by_organization($organizationid);
			$partylist->show_stars = true;
			if ($_REQUEST['ACTION'] === 'single') {
				$partylist->show_vevent = true;
			}
			$partylist->show_lineups = true;
			$partylist->show_date = true;
			$partylist->show_camera = true;
			$partylist->show_buddy_hearts = true;
			$partylist->show_city =
			$partylist->show_location = true;
			$partylist->show_people = !SMALL_SCREEN;
			$partylist->show_contests = true;
			$partylist->one_table = true;
			$partylist->hide_month_headers = true;
			$partylist->hide_separators = true;
			if ($organizationid === ORGANIZATIONID_ADE) {
				$partylist->order_chronologically_but_notime();
			} else {
				$partylist->order_chronologically();
			}
			$partylist->select_future(allow_postponed: true);
			if ($partylist->query()) {
				$partylist->display();
			}
		}
		if (empty($partylist)
		||	!$partylist->size()
		) {
			require_once '_lastparty.inc';
			show_last_party();
		}

		if ($_REQUEST['ACTION'] !== 'archive'
		&&	$past
		) {
			[$linkopen, $linkclose] = get_action_open_close('archive', null, element_name('agenda_archive').' '.escape_utf8($organization['NAME']));
			?><div class="light6 block" style="margin-top:1em"><i><?
			echo $linkopen,__('action:show_archive'),', ', $past, ' ', element_name('event', $past),$linkclose;
			?></i></div><?
		}
	}

	if ($artist_cnt) {
		layout_box_header(Eelement_name('artist',$artist_cnt),$show_agenda ? 'ttmrgn' : null,null,'artists');
		require_once '_artistlist.inc';
		$artistlist = new _artistlist;
		$artistlist->show_camera = true;
		$artistlist->nocache = 'organization';
		$artistlist->use_columns = true;
		$artistlist->hide_prefix = false;
		$artistlist->member_of_artistpool('organization',$organizationid);
		$artistlist->order_by_name();
		if (!$artistlist->query()) {
			return;
		}
		if ($artistlist->have_rows()) {
			$artistlist->display();
		}
	}

	layout_close_box();
	layout_open_box('organization');

	layout_box_header(Eelement_plural_name('statistic'));

	layout_open_table('fw vtop default', max_cols: SMALL_SCREEN ? 1 : 3);

	$none = __('counter:none');

	layout_start_reverse_row(!($total = $cnts['TOTAL_CNT']) ? ROW_LIGHT : 0);
		$linkopen  = $cnts['TOTAL_CNT'] ? (!$future ? get_action_open_close('archive')[0] : '<a href="#agenda">') : '';
		$linkclose = $linkopen ? '</a>' : '';
		echo $total ? $linkopen.$total.$linkclose : $none;
		layout_value_field();
		echo $linkopen,element_name('party',$total),$linkclose;

	if ($future = $cnts['FUTURE_CNT']) {
		layout_restart_reverse_row(!$future ? ROW_LIGHT : 0);
			$linkopen  = $future ? '<a href="#agenda">' : '';
			$linkclose = $linkopen ? '</a>' : '';
			echo $future ? $linkopen.$future.$linkclose : $none;
			layout_value_field();
			echo $linkopen, __('when:in_the_future'), $linkclose;
		}

	if ($past = $cnts['PAST_CNT']) {
		layout_restart_reverse_row();
		// ARCHIVE
		[$linkopen, $linkclose] = get_action_open_close('archive');
		echo $linkopen, $past, $linkclose;
		layout_value_field();
		echo $linkopen, __('when:in_the_past'), $linkclose;
	}

	if ($artist_cnt) {
		[$linkopen,$linkclose] = get_action_open_close('artists');
		layout_restart_reverse_row();
		echo $linkopen, $artist_cnt, $linkclose;
		layout_value_field();
		echo $linkopen, element_name('artist', $artist_cnt), $linkclose;
	}

	show_views_restart_row();
	if ($total_news) {
		// NEWS
		if ($total_news === $teaser_news) {
			$linkopen = '<a href="#news">';
			$linkclose = '</a>';
		} else {
			[$linkopen,$linkclose] = get_action_open_close('news');
		}
		layout_restart_reverse_row();
		echo $linkopen,$total_news,$linkclose;
		layout_value_field();
		echo $linkopen,element_name('news_message',$total_news),$linkclose;
	}
	if ($shootinfo['SHOOT_CNT']) {
		// PHOTOS
		[$linkopen,$linkclose] = get_action_open_close('photos');
		layout_restart_reverse_row();
			echo $linkopen,$shootinfo['SHOOT_CNT'],$linkclose;
			layout_value_field();
			echo $linkopen,element_name('photoshoot',$shootinfo['SHOOT_CNT']),$linkclose;
		layout_restart_reverse_row();
			echo $linkopen,$shootinfo['PHOTO_CNT'],$linkclose;
			layout_value_field();
			echo $linkopen,element_name('photo',$shootinfo['PHOTO_CNT']),$linkclose;
	}
	require_once '_videototal.inc';
	show_video_statistics_row();
	$contestcnts = [0,0];
	if ($party_contestcnts = memcached_simple_hash(
		['connect','contest','party'],'
		SELECT	COUNT(IF(ACTIVE=1 AND CLOSED=0 AND CANCELLED=0 AND MOVEDID=0,1,NULL)),
			COUNT(*)
		FROM contest
		JOIN party USING (PARTYID)
		JOIN connect ON ASSOCID=PARTYID
		WHERE PARTYID!=0
		  AND ASSOCTYPE="party"
		  AND MAINTYPE="organization"
		  AND MAINID='.$organizationid)
	) {
		[$a,$t] = keyval($party_contestcnts);
		$contestcnts[0] += $a;
		$contestcnts[1] += $t;
	}
	if ($other_contestcnts = memcached_simple_hash(
		['connect','contest'],'
		SELECT	COUNT(IF(ACTIVE=1 AND CLOSED=0,1,NULL)),
			COUNT(*)
		FROM contest
		JOIN connect ON ASSOCID=CONTESTID
		WHERE PARTYID=0
		  AND ASSOCTYPE="contest"
		  AND MAINTYPE="organization"
		  AND MAINID='.$organizationid)
	) {
		[$a,$t] = keyval($other_contestcnts);
		$contestcnts[0] += $a;
		$contestcnts[1] += $t;
	}
	if ($contestcnts[1]) {
		[$activecnt,$totalcnt] = $contestcnts;
		# FIXME: get gold in between, vrijkaarten voor medewerkers
		if ($activecnt) {
			$linkopen = '<a href="/contest/organization/'.$organizationid.'">';
			$linkclose = '</a>';
			layout_restart_reverse_row();
			echo $linkopen,$activecnt,$linkclose;
			layout_value_field();
			echo $linkopen,element_name('active_contest',$activecnt > 1).' <span class="win">'.__('partylist:win').'</span>',$linkclose;
		}
		if ($totalcnt) {
			$linkopen = '<a href="/contest/organization/'.$organizationid.'/all">';
			$linkclose = '</a>';
			layout_restart_reverse_row();
			echo $linkopen,$totalcnt,$linkclose;
			layout_value_field();
			echo $linkopen,element_name('contest',$totalcnt > 1),$linkclose;
		}
	}

	if ($visitorcnts) {
		[$total,$uniq] = keyval($visitorcnts);
		if ($total) {
			layout_restart_reverse_row();
			echo $total;
			layout_value_field();
			echo element_name('visitor',$total);

			if ($uniq) {
				layout_restart_reverse_row();
				echo $uniq;
				layout_value_field();
				echo element_name('unique_visitor',$uniq);
			}
		}
	}
	$fancnt = get_fan_count();
	if ($fancnt) {
		[$linkopen,$linkclose] = get_action_open_close('fans');
		layout_restart_reverse_row();
		echo $linkopen,$fancnt,$linkclose;
		layout_value_field(get_partyflock_icon());
		echo $linkopen,element_name('fan',$fancnt),$linkclose;
	}

	require_once '_fblikes.inc';
	show_fb_likes();

	require_once '_album.inc';
	album_show_stats_row();

	$reportcnt = memcached_single(['report','connect'],'
		SELECT COUNT(*)
		FROM report
		JOIN connect ON MAINTYPE="party" AND MAINID=PARTYID AND ASSOCTYPE="organization"
		WHERE ACCEPTED=1
		  AND ASSOCID='.$organizationid
	);
	if ($reportcnt) {
		$linkopen = '<a href="/report/organization/'.$organizationid.'" title="'.element_name('report',$reportcnt).'">';
		$linkclose = '</a>';

		layout_restart_reverse_row();
		echo $linkopen,$reportcnt,$linkclose;
		layout_value_field();
		echo $linkopen,element_name('report',$reportcnt),$linkclose;
	}

	require_once '_commentlist.inc';
	require_once '_ratinglist.inc';
	_commentlist::show_stats_row();
	_ratinglist::show_stats_row();
	vote_show_row();
	layout_stop_row();
	layout_close_table();

	if (SMALL_SCREEN) {
		?></div><?
	}
	layout_close_box();

	close_drag();

	if ($_REQUEST['ACTION']) {
		require_once '_fans.inc';
		switch ($_REQUEST['ACTION']) {
		// CAMERAREQUEST
		case 'camera':
			if ($caminfo) {
				require_once '_camerarequests.inc';
				show_camerarequests('organization',$organizationid);
			} else {
				http_response_code(404);
			}
			break;
		// NEWS
		case 'news':
			if ($total_news) {
				require_once '_news.inc';
				show_news_teasers();
			} else {
				http_response_code(404);
			}
			break;

		// ARCHIVE
		case 'archive':
			if ($cnts['PAST_CNT']) {
				show_organization_archive($organization);
			} else {
				http_response_code(404);
			}
			break;
		// PHOTOS
		case 'photos':
			if ($shootinfo['SHOOT_CNT']) {
#				show_organization_photos($organization);
				require_once '_galleryoverview.inc';
				$galleryoverview = new galleryoverview;
				$galleryoverview->show_organization($organizationid);
				$galleryoverview->query();
				$galleryoverview->show_thumbs();
				$galleryoverview->show_shoots();
			} else {
				http_response_code(404);
			}
			break;
		// FANS
		case 'fans':
			if ($fancnt) {
				show_fans('organization',$organizationid,$organization);
			} else {
				http_response_code(404);
			}
			break;

		// VOTES
		case 'votes':
			vote_show_box();
			break;

		# REPORTS
		case 'reports':
			break;

		default:
			$showcmts = true;
			break;
		}
	} else {
		$showcmts = true;
	}
	if (isset($showcmts)) {
		require_once '_videosandphotos.inc';
		show_videos_and_photos('organization',$organizationid);

		require_once '_showcase.inc';
		show_presence_showcase();

		show_news_teasers();
		$ratinglist->display();

		$cmts = new _commentlist;
		$cmts->item($organization);
		$cmts->display();
	}
	?></article><?

	close_status_overlay();

	counthit('organization',$organizationid);
}

function organization_display_form() {
	if (!require_user()) {
		return;
	}
	require_once '_citylist.inc';
	require_once '_presence.inc';
	require_once '_fillelementid.inc';
	$organizationid = $_REQUEST['sID'];
	layout_show_section_header(null,__($organizationid ? 'action:change' : 'action:add'));

	$organization_admin = have_admin('organization');

	if ($organizationid) {
		$organization = db_single_assoc(['organization','subof'],'
			SELECT	organization.*,
				(SELECT GROUP_CONCAT(PARENTID) FROM subof WHERE ELEMENT="organization" AND PELEMENT="organization" AND CONCEPTID=ORGANIZATIONID) AS PARENTIDS,
				(SELECT GROUP_CONCAT(PARENTID) FROM subof WHERE ELEMENT="organization" AND PELEMENT="location" AND CONCEPTID=ORGANIZATIONID) AS LOCATION_PARENTIDS
			FROM organization
			WHERE ORGANIZATIONID='.$organizationid
		);
		if ($organization === false) {
			return;
		}
		if (!$organization) {
			not_found();
			return;
		}
		if (!may_change_element('organization',$organizationid,CURRENTUSERID,true,$organization)) {
			return false;
		}
	} else {
		$organization = null;
	}

	?><form<?
	?> enctype="multipart/form-data"<?
	?> accept-charset="utf-8"<?
	?> onsubmit="return submitForm(this)"<?
	?> method="post"<?
	?> autocomplete="off"<?
	?> action="/organization<?
	if ($organizationid) {
		?>/<? echo $organizationid;
	}
	?>/commit"><?

	require_once '_ticket.inc';
	ticket_passthrough();

	require_once '_process_appic_changes.inc';
	passthrough_update_from_appic();

	$spec = explain_table('organization');

	require_once '_uploadimage.inc';
	show_uploadimage_for_element_form('organization', $organizationid);

	if ($organization
	&&	$organization_admin
	) {
		require_once '_presence.inc';
		show_presence_search($organization);
	}

	layout_open_box('organization');
	layout_open_table('fw');
	layout_start_row();

	if ($organization_admin) {
		require_once '_needupdates.inc';
		show_needupdate_form_part($organization);
	}

	echo Eelement_name('name');
	layout_field_value();
	show_input([
		'spec'		=> $spec,
		'type'		=> 'text',
		'name'		=> 'NAME',
		'autocomplete'	=> 'off',
		'id'		=> 'name',
		'autofocus'	=> true,
		'required'	=> true,
		'value_utf8'	=> $organization,
	]);

	require_once '_alternate_name.inc';
	show_alternate_name_form('organization', $organizationid);

	layout_restart_row();
		echo Eelement_plural_name('activity');
		layout_field_value();

		$funcs = $organization ? explode(',', $organization['ACTIVITIES']) : [];
		foreach (explain_table('organization')['ACTIVITIES']->set as $func) {
			$functions[$func] = __('activity:'.$func);
		}
		asort($functions);
		?><div style="columns:<?= SMALL_SCREEN ? 2 : 4 ?>"><?
		foreach ($functions as $func => $desc) {
			$checked = in_array($func, $funcs);
			?><label class="<?= $checked ? '' : 'not-' ?>hilited nicer cbi"><?
			show_input([
				'type'		=> 'checkbox',
				'checked'	=> $checked,
				'name'		=> 'ACTIVITIES[]',
				'value'		=> $func,
				'class'		=> 'upLite',
			]);
			?><div class="description"><?= $desc ?></div><?
			?></label><?
		}
		?></div><?

	if (have_admin('organization')) {
		layout_restart_row();
		?><label for="umbrella"><?
		echo Eelement_name('umbrella(organization)');
		?></label><?
		layout_field_value();
		$checked = !empty($organization['WHOLE']);
		?><label class="cbi <?= $checked ? null : 'not-' ?>hilited"><?
		show_input([
			'id'		=> 'umbrella',
			'class'		=> 'upLite',
			'type'		=> 'checkbox',
			'name'		=> 'WHOLE',
			'value'		=> '1',
			'checked'	=> $checked,
		]);
		?> <?
		echo __('organization:info:umbrella(organization)');
		?></label><?

	} elseif (!empty($organization['WHOLE'])) {
		?><input type="hidden" name="WHOLE" value="1" /><?
	}

	layout_restart_row();
		?><label for="erotic"><?= __C('attrib:erotic') ?><label><?
		layout_field_value();
		?><label class="cbi <?
		if (empty($organization['EROTIC'])) {
			?>not-<?
		}
		?>hilited"><?
		show_input([
			'type'		=> 'checkbox',
			'id'		=> 'erotic',
			'class'		=> 'upLite',
			'name'		=> 'EROTIC',
			'value'		=> '1',
			'checked'	=> !empty($organization['EROTIC'])
		]);
		?> <?= __('erotic:info:mainly_erotic_events');
		?></label><?

	layout_restart_row();
		echo Eelement_name('foundation');
		layout_field_value();
		if (!empty($organization['FOUNDED'])) {
			[$year, $month, $day] = explode('-',$organization['FOUNDED']);
		} else {
			$year = $month = $day = 0;
		}
		global $__year;
		_date_display_select('FOUND', $year, $month, $day, $__year - 50, $__year + 1, 0);

	layout_restart_row();
		echo Eelement_name('site');
		layout_field_value();
		show_input([
			'spec'		=> $spec,
			'type'		=> 'url',
			'name'		=> 'SITE',
			'value_utf8'	=> $organization,
		]);
	layout_restart_row();
		echo Eelement_name('email');
		layout_field_value();
		show_input([
			'spec'		=> $spec,
			'type'		=> 'email',
			'data-valid'	=> 'working-email',
			'name'		=> 'EMAIL',
			'value_utf8'	=> $organization,
		]);
	if (!show_presence_form_row()) {
		return;
	}
	layout_restart_row();
		echo Eelement_name('address');
		layout_field_value();
		show_input(array(
			'spec'		=> $spec,
			'type'		=> 'text',
			'name'		=> 'ADDRESS',
			'value_utf8'	=> $organization,
		));
		?><br /><label><input<?
		if (!empty($organization['ADDRESS_HIDDEN'])) {
			?> checked="checked"<?
		}
		?> type="checkbox" name="ADDRESS_HIDDEN" value="1"> <?= __('action:dont_show') ?></label><?
	layout_restart_row();
		echo Eelement_name('zipcode');
		layout_field_value();
		show_input(array(
			'spec'		=> $spec,
			'class'		=> 'id',
			'type'		=> 'text',
			'name'		=> 'ZIPCODE',
			'value'		=> $organization
		));

	layout_restart_row();
		echo Eelement_name('country'); ?> &amp; <? echo Eelement_name('city');
		layout_field_value();
		display_dynamic_city_pair(
			cityid: $organization['CITYID'] ?? 0,
			countryid: $organization['COUNTRYID'] ?? 0,
			flags: INCLUDE_EMPTY_COUNTRIES,
		);

	layout_restart_row();
		echo Eelement_name('pobox');
		layout_field_value();
		show_input([
			'spec'		=> $spec,
			'class'		=> 'id',
			'type'		=> 'number',
			'name'		=> 'POBOX',
			'value'		=> $organization ? ($organization['POBOX'] ?: null) : null,
		]);

	layout_restart_row();
		echo Eelement_name('pobox_zipcode');
		layout_field_value();
		show_input(array(
			'spec'		=> $spec,
			'class'		=> 'id',
			'type'		=> 'text',
			'name'		=> 'POBOX_ZIPCODE',
			'value'		=> $organization
		));

	layout_restart_row();
		echo Eelement_name('pobox_city');
		layout_field_value();
		display_dynamic_city_pair(
			cityid: $organization['POBOX_CITYID'] ?? 0,
			flags: INCLUDE_EMPTY_COUNTRIES,
			prefix: 'POBOX_',
		);

	layout_restart_row();
		echo Eelement_name('phone');
		layout_field_value();
		show_input(array(
			'spec'		=> $spec,
			'class'		=> 'id',
			'data-valid'	=> 'intl-tel',
			'type'		=> 'tel',
			'name'		=> 'PHONE',
			'value'		=> $organization
		));
	require_once '_kvk.inc';
		show_kvk_form_row($organization);
	layout_restart_row();
		?><label for="dead"><?= Eelement_name('cessation'); ?></label><?
		layout_field_value();
		?><input<?
		if ($organizationid
		&&	$organization['DEADSTAMP']
		) {
			?> checked<?
		}
		?> type="checkbox" value="1" name="DEAD" id="dead" onclick="setdisplay('deadinfo',this.checked)"><?

	layout_restart_row(!isset($organization) || !$organization['DEADSTAMP'] ? ROW_HIDDEN : 0, 'deadinfo');
		echo Eelement_name('date_known');
		layout_field_value();
		?><select onchange="setdisplay('deaddate',this.value=='1')" name="KNOWNDEADDATE"><?
			?><option value="0"><?= __('answer:no') ?></option><?
			?><option value="1"<?
			if (isset($organization)
			&&	$organization['DEADSTAMP'] > 1
			) {
				?> selected<?
			}
			?>><?= __('answer:yes') ?></option><?
		?></select><?

	layout_restart_row(!isset($organization) || $organization['DEADSTAMP'] <= 1 ? ROW_HIDDEN : 0,'deaddate');
		echo Eelement_name('date_of_cessation');
		layout_field_value();

		$last_event_stamp =
			$organizationid
		?	(	db_single(['party', 'connect'], '
				SELECT MAX(STAMP)
				FROM party
				JOIN connect
				  ON MAINTYPE = "organization"
				 AND MAINID = '.$organizationid.'
				 AND ASSOCTYPE = "party"
				 AND ASSOCID = PARTYID')
			?:	CURRENTSTAMP
			)
		:	CURRENTSTAMP;

		_date_display_select_stamp(
			stamp:			 isset($organization) && $organization['DEADSTAMP'] > 1 ? $organization['DEADSTAMP'] : ($last_event_stamp ?: CURRENTSTAMP),
			prefix: 		 'DEAD',
			relative_start_year: -2,
			relative_end_year:	1
		);
	layout_stop_row();

	require_once '_followup.inc';
	show_followup_and_parent_form_rows('organization', $organizationid, $organization);

	layout_restart_row();
		echo Eelement_name('minimum_age');
		layout_field_value();
		require_once '_minimum_age.inc';
		show_minimum_age_input($organization);

	layout_start_row();
		echo Eelement_plural_name('genre');
	layout_field_value();
		require_once '_genrelist.inc';
		show_genre_selection(
			$organization ? db_boolean_hash('organization_genre', 'SELECT GID FROM organization_genre WHERE ORGANIZATIONID = '.$organizationid) : null
		);
	layout_stop_row();

	layout_start_row();
	layout_next_cell();
		$copy_genres = !empty($organization['COPY_GENRES']);

		?><label class="<?= $copy_genres ? null : 'not-' ?>bold-hilited-green"><?
		show_input([
			'type'		=> 'checkbox',
			'class'		=> 'upLite',
			'name'		=> 'COPY_GENRES',
			'checked'	=> $copy_genres,
		]);
		?> <?
		echo __('genres:info:copy_to_new_event');
		?></label><?

	layout_stop_row();

	if (have_admin('organization')) {
		layout_start_row();
			echo __C('header:admin_comments');
			layout_field_value();
			?><textarea class="growToFit" name="ADMIN_TEXT" cols="50" rows="5"><? if ($organizationid) echo escape_utf8($organization['ADMIN_TEXT']); ?></textarea><?
		layout_restart_row();
			echo __C('header:admin_party_comments');
			layout_field_value();
			?><textarea class="growToFit" name="ADMIN_PARTY_TEXT" cols="50" rows="5"><? if ($organizationid) echo escape_utf8($organization['ADMIN_PARTY_TEXT']); ?></textarea><?

		if (have_admin('hide_stuff')) {
			layout_restart_row();
				echo Eelement_name('visibility');
			layout_field_value();
				?><label class="cbi <?= empty($organization['HIDDEN_FOR_SEARCH_ENGINES']) ? 'not-' : null ?>hilited-light-orange"><?
				show_input([
					'type'		=> 'checkbox',
					'class'		=> 'upLite',
					'name'		=> 'HIDDEN_FOR_SEARCH_ENGINES',
					'value'		=> 1,
					'checked'	=> !empty($organization['HIDDEN_FOR_SEARCH_ENGINES']),
				]);
				?> <?= __('status:hidden_for_search_engines');
				?></label><?
				?><br /><?
				?><label class="cbi <?= empty($organization['HIDE_AGE_STATS']) ? 'not-' : null ?>hilited-light-orange"><?
				show_input([
					'type'		=> 'checkbox',
					'class'		=> 'upLite',
					'name'		=> 'HIDE_AGE_STATS',
					'value'		=> 1,
					'checked'	=> !empty($organization['HIDE_AGE_STATS']),
				]);
				?> <?= __('action:hide_age_statistics');
				?></label><?
			layout_restart_row();
		}
	}
	require_once '_cameracontacts.inc';
	show_camreq_form_part($organizationid ? $organization : null);
	layout_close_table();
	layout_close_box();
	?><div class="block"><?
	?><input type="submit" value="<?= __(isset($organization) ? 'action:change' : 'action:add'); ?>" /><?
	?></div></form><?
}

function actual_organization_commit(): int|false {
	require_once '_genrelist.inc';
	require_once '_elementchanged.inc';
	require_once '_site.inc';
	require_once '_ticket.inc';
	require_once '_presence.inc';
	require_once '_namefix.inc';
	require_once '_ubb_preprocess.inc';
	require_once '_followup.inc';
	require_once '_phone.inc';
	require_once '_minimum_age.inc';

	if (!require_user()
	||	!require_anything_trim($_POST, 'EMAIL', utf8: true)
	||	!optional_working_email($_POST, 'CAMREQ_EMAIL', utf8: true)
	||	!require_something_trim($_POST, 'NAME', null, null, utf8: true)
	||	!require_anything_trim($_POST, 'ADDRESS', utf8: true)
	||	!require_anything_trim($_POST, 'ZIPCODE', utf8: true)
	||	!require_anything_trim($_POST, 'POBOX_ZIPCODE', utf8: true)
	||	!require_anything_trim($_POST, 'PHONE', utf8: true)
	||	!require_anything_trim($_POST, 'CAMREQ_NAME', utf8: true)
	||	!optional_number($_POST, 'CAMREQ_USERID')
	||	!require_minimum_age_or_none($_POST, 'MIN_AGE')
	||	false === optional_anything_trim($_POST, 'ADMIN_TEXT', utf8: true)
	||	false === optional_anything_trim($_POST, 'ADMIN_PARTY_TEXT', utf8: true)
	||	!require_number_or_empty($_POST,  'POBOX')
	||	!optional_number($_POST, 'POBOX_CITYID')
	||	!optional_number($_POST, 'CITYID')
	||	false === require_number($_POST, 'COUNTRYID')
	||	false === require_number($_POST, 'KVKNUM', utf8: true)
	||	false === require_number($_POST, 'FOUNDDAY', utf8: true)
	||	false === require_number($_POST, 'FOUNDMONTH', utf8: true)
	||	false === require_number($_POST, 'FOUNDYEAR', utf8: true)
	||	!require_number_or_empty($_POST, 'FOLLOWUPID')
	||	!require_number_array($_POST, 'PARENTID', allow_empty: true)
	||	isset($_POST['DEAD'])
	&&	!require_date($_POST,'DEAD')
	||	false === require_element($_POST, 'KNOWNDEADDATE', ['0', '1'])
	||	!empty($_POST['ACTIVITIES'])
	&&	!require_array($_POST, 'ACTIVITIES', utf8: true)
	||	!require_anything_trim($_POST, 'SITE', utf8: true)
	) {
		return false;
	}
	if ($_REQUEST['sID']
	&&	$_POST['EMAIL']
	) {
		require_once '_urlcheck.inc';
		$status = get_url_status('organization', $_REQUEST['sID']);
		if (!($offline = url_is_bad($status))
		&&	!require_working_email($_POST, 'EMAIL')
		) {
			return false;
		}
	}
	$organization_admin = have_admin('organization');

	move_site_to_presence();

	if ($organization_admin) {
		$setlist[] = 'NEEDUPDATE='.	(isset($_POST['NEEDUPDATE']) ? "b'1'" : "b'0'");
	}
	$setlist[] = 'FOUNDED='.($_POST['FOUNDYEAR'] || $_POST['FOUNDMONTH'] || $_POST['FOUNDDAY']
		?	'"'.$_POST['FOUNDYEAR'].'-'.$_POST['FOUNDMONTH'].'-'.$_POST['FOUNDDAY'].'"'
		:	'NULL'
		);


	$setlist[] = 'COPY_GENRES='.	(isset($_POST['COPY_GENRES']) ? "b'1'" : "b'0'");
	$setlist[] = 'NAME="'.			addslashes(get_fixed_name($_POST['NAME'], true)).'"';
	$setlist[] = 'EMAIL="'.			addslashes($_POST['EMAIL']).'"';
	$setlist[] = 'ADDRESS="'.		addslashes($_POST['ADDRESS']).'"';
	$setlist[] = 'ADDRESS_HIDDEN='.	(isset($_POST['ADDRESS_HIDDEN']) ? 1 : 0);
	$setlist[] = 'ZIPCODE="'.		addslashes(clean_zip($_POST['ZIPCODE'])).'"';
	$setlist[] = 'POBOX_ZIPCODE="'.	addslashes(clean_zip($_POST['POBOX_ZIPCODE'])).'"';
	$setlist[] = 'PHONE="'.			addslashes(clean_phone($_POST['PHONE'])).'"';
	$setlist[] = 'WHOLE='.			(isset($_POST['WHOLE']) ? "b'1'" : "b'0'");
	$setlist[] = 'MIN_AGE='.		get_minimum_age_for_setlist($_POST, 'MIN_AGE');

	if ($organization_admin) {
		$setlist[] = 'CAMREQ_EMAIL		= "'.addslashes($_POST['CAMREQ_EMAIL']).'"';
		$setlist[] = 'CAMREQ_NAME		= "'.($_POST['CAMREQ_EMAIL'] ? addslashes($_POST['CAMREQ_NAME']) : null).'"';
		$setlist[] = 'CAMREQ_USERID		= '.$_POST['CAMREQ_USERID'];
		$setlist[] = 'ADMIN_TEXT		= "'.addslashes(_ubb_preprocess($_POST['ADMIN_TEXT'], utf8: true)).'"';
		$setlist[] = 'ADMIN_PARTY_TEXT		= "'.addslashes(_ubb_preprocess($_POST['ADMIN_PARTY_TEXT'], utf8: true)).'"';

		if (have_admin('hide_stuff')) {
			$setlist[] = 'HIDDEN_FOR_SEARCH_ENGINES='.	(isset($_POST['HIDDEN_FOR_SEARCH_ENGINES']) ? "b'1'" : "b'0'");
			$setlist[] = 'HIDE_AGE_STATS='.			(isset($_POST['HIDE_AGE_STATS']) ? "b'1'" : "b'0'");
		}
	}

	$setlist[] = 'SITE="'.			($_POST['SITE'] ? addslashes(make_proper_site($_POST['SITE'],KEEP_MAIN_DOMAIN)) : null).'"';
	#$setlist[] = 'SITE="'.			($_POST['SITE'] ? addslashes($_POST['SITE']) : '').'"';
	$setlist[] = 'POBOX='.			($_POST['POBOX'] ?: 0);
	$setlist[] = 'CITYID='.			($_POST['CITYID'] ?? 0);
	$setlist[] = 'COUNTRYID='.		$_POST['COUNTRYID'];
	$setlist[] = 'POBOX_CITYID='.		($_POST['POBOX_CITYID'] ?? 0);
	$setlist[] = 'KVKNUM="'.		addslashes($_POST['KVKNUM'] ?: '').'"';
	$setlist[] = 'FOLLOWUPID='.		($_POST['FOLLOWUPID'] ?: 0);
	$setlist[] = 'EROTIC='.			(isset($_POST['EROTIC']) ? "b'1'" : "b'0'");
	$setlist[] = 'DEADSTAMP='.
		(	isset($_POST['DEAD'])
		?	(	$_POST['KNOWNDEADDATE']
			?	_date_getstamp($_POST,'DEAD')
			:	'1'
			)
		:	'0'
		);

	$setlist[] = 'ACTIVITIES = "'.(empty($_POST['ACTIVITIES']) ? '' : implode(',', array_map('addslashes', $_POST['ACTIVITIES']))).'"';

	require_once '_status_overlay.inc';
	add_status_to_setlist($setlist);

	if ($organizationid = $_REQUEST['sID']) {
		if (!($old_needupdate = db_single('organization', 'SELECT NEEDUPDATE FROM organization WHERE ORGANIZATIONID = '.$organizationid))
		&&	db_failed()
		) {
			return false;
		}
		require_once '_element_access.inc';
		if (!may_change_element('organization', $organizationid, CURRENTUSERID, true)) {
			return false;
		}
		reset_site();
		if (!db_insert('organization_log','
			REPLACE INTO organization_log
			SELECT * FROM organization
			WHERE NOT '.binary_equal($setlist).'
			  AND ORGANIZATIONID = '.$organizationid)
		) {
			return false;
		}
		if (!db_affected()) {
			register_notice('item:notice:no_change_needed_LINE');
		} else {
			if (!db_update('organization','
				UPDATE organization SET 
					MUSERID	= '.CURRENTUSERID.',
					MSTAMP	= '.CURRENTSTAMP.',
					'.implode(', ', $setlist).'
				WHERE ORGANIZATIONID = '.$organizationid)
			) {
				return false;
			}
			register_notice('organization:notice:changed_LINE');
		}
		if ($organization_admin
		&&	isset($_POST['NEEDUPDATE']) !== $old_needupdate
		) {
			require_once '_needupdates.inc';
			flush_needupdates();
		}
	} else {
		if (!isset($_POST['ADMIN_TEXT'])) {
			$setlist[] = 'ADMIN_TEXT = ""';
			$setlist[] = 'ADMIN_PARTY_TEXT = ""';
		}
		if (!db_insert('organization','
			INSERT INTO organization SET
				ACCEPTED = '.($organization_admin ? 1 : 0).',
				USERID	 = '.CURRENTUSERID.',
				CSTAMP	 = '.CURRENTSTAMP.',
				MSTAMP	 = '.CURRENTSTAMP.',
				'.implode(', ', $setlist))
		) {
			return false;
		}
		$_REQUEST['sID'] = $organizationid = db_insert_id();

		register_notice('organization:notice:added_LINE');
		ticket_update('organization', $organizationid);
		init_site();
	}
	if ($organization_admin) {
		require_once '_alternate_name.inc';
		commit_alternate_names('organization', $organizationid);
	}
	require_once '_uploadimage.inc';
	uploadimage_actual_receive('organization', $organizationid);
	require_once '_process_appic_changes.inc';
	cleanup_update_from_appic($organizationid);
	_genrelist_commit();
	process_parents('organization', $organizationid);
	page_changed('organization', $organizationid);
 	store_presence();
	element_changed();
	return $organizationid;
}
