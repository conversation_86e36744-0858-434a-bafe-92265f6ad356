<?php

declare(strict_types=1);

function __debug(string $str): void {
	if (!function_exists('debug')) {
		return;
	}
	debug('eventlist: '.$str);
}

require_once '_facebook_parse.inc';

function parse_facebook_eventlist(
	string	$dom_str,
	string	$element,
	int		$id
): array|false {
	$doc = new DOMDocument();
	$dom_str = prepare_dom_str($dom_str);
	set_libxml_error_handler();
	$doc->loadHTML($dom_str, LIBXML_COMPACT | LIBXML_NOWARNING);
	restore_error_handler();
	$xpath = new DOMXPath($doc);
	return parse_new_facebook_eventlist($xpath, $element, $id);
}

function parse_new_facebook_eventlist(
	DOMXPath	$xpath,
	string		$element,
	int			$id
): array|false {

	$events = [];

	[$curr_year, $curr_month, $curr_day] = _getdate();

	$planned_events = $xpath->query(
		'//h2/span/span[contains(text(),"Geplande evenementen")]'
	);
	$eventNode = null;
	$top = null;
	if (/* $planned_events_v1 = */ $planned_events?->length) {
		__debug('found old planned_events string');

		$top = $planned_events[0]
		?->parentNode
		?->parentNode
		?->parentNode
		?->parentNode
		?->parentNode
		?->parentNode
		?->parentNode
		?->parentNode;

		if ($top) {
			$eventNode = $top->nextSibling;
		}
	} else {
		foreach (['Gepland', 'Afgelopen'] as $type) {
			$planned_events = $xpath->query(
				'.//span[contains(text(),"'.$type.'")]'
			);

			if (/* $planned_events_v2 = */ $planned_events?->length) {
				__debug('found v2 planned_events string');

				$top = $planned_events[0]
				?->parentNode
				?->parentNode
				?->parentNode
				?->parentNode
				?->parentNode
				?->parentNode
				?->parentNode;

				if ($top) {
					$eventNode = $top->nextSibling->firstChild;
					break;
				}
			}
		}
	}

	if (empty($top)) {
		register_error('facebook:error:drag_not_understood_LINE');
		return false;
	}

	__debug('got top: '.$top->nodeValue);

	for (; $eventNode; $eventNode = $eventNode->nextSibling) {
		$val = $eventNode->nodeValue;
		if ($val === 'Meer weergeven') {
			register_error('facebook:error:please_click_open_more_LINE');
			return false;
		}

		$val = preg_replace([
			# NOTE: Remove something
			"!Ge\xc3\xaf.*$!",
			# NOTE: Remove organizer. Might conflict with name since we 'split' the value on the name of the event
			"'Evenement van.*$'u",
		], '', $val);

		# find fbid

		$fb_anchors = $xpath->query('.//a[starts-with(@href,"https://www.facebook.com/events/")]',$eventNode);
		if (!$fb_anchors->length) {
			continue;
		}

		$ok_anchor = null;
		foreach ($fb_anchors as $fb_anchor) {
			if ($fb_anchor->nodeValue) {
				$ok_anchor = $fb_anchor;
				break;
			}
		}
		if (!$ok_anchor) {
			mail_log("could not find proper anchor for $element:$id -- $val", error_log: 'WARNING');
		}
		$fb_anchor = $ok_anchor;
		$fb_href = $fb_anchor->getAttribute('href');
		if (!preg_match('"/events/(?<fbid>\d+)"', $fb_href, $fb_match)) {
			mail_log("bad fb_anchor href for $element:$id -- $val", error_log: 'WARNING');
			continue;
		}

		$fbid = $fb_match['fbid'];

		$name = $fb_anchor->nodeValue;

		$parts = explode($name, $val);
		if (count($parts) !== 2) {
			mail_log('explode on event name did not result in 2 parts', get_defined_vars(), error_log: 'WARNING');
			continue;
		}
		[$datestr/*, $location */] = $parts;

		# NOTE: Used at the bottom to set the 'time' key if it is set by date detection
		unset($start_hour);

		$monthstr = implode('|',get_month_from_string(true));
		$daystr   = implode('|',get_day_names());

		$time_re = '(?<hour>\d+)(?::(?<mins>\d+))?\s*(?<ampm>AM|PM)?';

		$parse_time = static function (array $match): array {
			$hour = (int)($match['hour'] ?? 0);
			$mins = (int)($match['mins'] ?? 0);
			if (!empty($match['ampm'])
			&&	mb_strtolower($match['ampm']) === 'PM'
			) {
				$hour += 12;
			}
			return [$hour, $mins];
		};

		$timezone_re = '(?<timezone>UTC[+-]\d+)?';

		# Vr, 15 jan. 2021 om 20:00 UTC+01
		if (preg_match('"'.
			'('.$daystr.'),?'.
			'\h*(?<day_before_month>\d+)?'.
			'\h*(?<month_name>'.$monthstr.')\.?'.
			'\h*(?<day_after_month>\d+)?,?'.
			'\h*(?<year>\d{4})?'.
			'\h*(?:at|om)\h*'.
			'\h*'.$time_re.
			'\h*'.$timezone_re.
			'"uis',
			$datestr,
			$datematch)
		) {
			$start_day = $datematch['day_before_month'] ?? $datematch['day_after_month'];
			$start_month = get_month_from_string($datematch['month_name']);
			$start_year = (int)$datematch['year'] ?? $curr_year;
			[$start_hour, $start_mins] = $parse_time($datematch);
			__debug('recognized date type 1: '.$datematch[0]);

		# Za, 20 feb.
		# Za 20 feb. 2021
		# Za 20 feb. 2021 en nog 1
		} elseif (preg_match('"'.
			'('.$daystr.'),?\h*(\d+)\h*('.$monthstr.')\.?'.
			'\h*(\d{4})?'.
			'\h*(en\h*nog\h*\d)?'.
			'"uis',
			$datestr,
			$datematch)
		) {
			[,	/* $start_day_name */,
				$start_day,
				$start_monthname
			] = $datematch;
			$start_year = $datematch[4] ?? $curr_year;
			$start_month = get_month_from_string($start_monthname);
			__debug('recognized date type 2: '.$datematch[0]);

		# Vr, 13 nov. - 15 nov.
		# Do, 11 nov. 2021 - 14 nov. 2021
		} elseif (preg_match('"'.
			'('.$daystr.'),?'.
			'\h*(\d+)\h*('.$monthstr.')\.?'.
			'\h*(\d{4})?'.
			'\h*-'.
			'\h*(\d+)\h*('.$monthstr.')\.?'.
			'"uis',
			$datestr,
			$datematch)
		) {
			[,	/* $start_day_name */,
				$start_day,
				$start_monthname,
				$start_year,
				/* $end_day */,
				/* $end_monthname */,
			] = $datematch;

			$start_hour = 22;
			$start_mins = 0;

			if (!$start_year) {
				$start_year = $curr_year;
			}
			$start_month = get_month_from_string($start_monthname);

			__debug('recognized date type 3: '.$datematch[0]);

		} elseif (preg_match('"'.
			'(?:Deze|This)'.
			'\h*('.$daystr.')'.
			'\h*(?:at|om)'.
			'\h*'.$time_re.
			'\h*'.$timezone_re.
			'"uis',
			$datestr,
			$datematch)
		) {
			$start_day_name = $datematch[1];

			$start_daynum = get_weekday_for_day_name($start_day_name);

			[$start_hour, $start_mins] = $parse_time($datematch);

			# note, this still results in wrong date for other timezones that viewer timezone
			# we don't know the timezone of current event
			$stamp  = strtotime('next '.daynum_to_day_name($start_daynum).' '.$start_hour.':'.$start_mins);

			[$start_year, $start_month, $start_day] = _getdate($stamp);

			__debug('recognized date type 4: '.$datematch[0]);

		} elseif (preg_match('"'.
			'(Vandaag|Morgen|Today|Tomorrow)'.
			'\h*(?:at|om)'.
			'\h*'.$time_re.
			'\h*'.$timezone_re.
			'"uis',
			$datestr,
			$datematch)
		) {
			[,	$start_day_name,
				$start_hour,
			] =	$datematch;

			__debug('start_day_name: '.$start_day_name.', start_hour: '.$start_hour);

			[$start_hour, $start_mins] = $parse_time($datematch);

			switch (mb_strtolower($start_day_name)) {	# NOSONAR
			case 'vandaag':
			case 'today':
				$start_day_name = 'today';
				break;
			case 'tomorrow':
			case 'morgen':
				$start_day_name = 'tomorrow';
				break;
			}

			# note, this still results in wrong date for other timezones that viewer timezone
			# we don't know the timezone of current event
			$stamp  = strtotime("$start_day_name $start_hour:$start_mins");

			[$start_year, $start_month, $start_day] = _getdate($stamp);

			__debug('recognized date type 5: '.$datematch[0]);

		} elseif (str_starts_with($datestr,'Nu bezig')) {
			$start_year = $curr_year;
			$start_month = $curr_month;
			$start_day = $curr_day;

		} else {
			mail_log("date on facebook eventlist page not recognized for $element:$id, datestr: $datestr", error_log: 'WARNING');
			continue;
		}
		$event = [
			'fbid'		=> $fbid,
			'name'		=> $name,
			'year'		=> (int)$start_year,
			'month'		=> (int)$start_month,
			'day'		=> (int)$start_day,
			# 'location'	=> getifset($location,$main_name),
		];

		if (isset($start_hour)) {
			$event['time'] = sprintf('%02d:%02d', $start_hour, $start_mins ?? 0);
		}

		$events[] = $event;
	}
	return finish_events($element, $id, $events);
}

function finish_events(string $element, int $id, array $events): array|false {
	foreach ($events as $event) {
		# date in here is not really useful, it contains dates in local time for all old facebook events
		# it contains date in CET/CEST for al newer events (they are not converted to timezone independent dates/stampes here yet, because location/timezone
		# specs are not explicitly known

		$inslist[] = "('$element', $id, ".CURRENTSTAMP.", {$event['fbid']}. '{$event['year']}-{$event['month']}-{$event['day']}".(empty($event['time']) ? '' : " {$event['time']}").'")';
	}
	if (!empty($inslist)
	&&	!db_insert('facebook_found_drag','
		INSERT INTO facebook_found_drag (ELEMENT, ID, ESTAMP, EVENTID, DATE)
		VALUES '.implode(',', $inslist).'
		ON DUPLICATE KEY UPDATE DATE = VALUES(DATE)')
	) {
		return false;
	}
	ob_start();
	$rc = show_eventlist_html($element,$id,$events);
	$html = ob_get_clean();
	if (!$rc) {
		return false;
	}
	return [
		'events'	=> $events,
		'html'		=> $html,
	];
}

function show_eventlist_html(string $element, int $id, array $events): bool {
	require_once '_layout.inc';
	require_once '_processlist.inc';
	require_once 'defines/appic.inc';
	require_once 'defines/facebook.inc';

	if (false === ($oldeventnames = memcached_simpler_array('party','
		SELECT DISTINCT NAME
		FROM party
		WHERE STAMP > '.(TODAYSTAMP - 2 * ONE_YEAR)))
	) {
		return false;
	}
	$debug = '';
	$find_use = [];
	$fbids = [];

	layout_open_box($element);
	ob_start();
	processlist_show_closer($element,$id);
	$closer = ob_get_clean();
	layout_box_header(get_facebook_icon().' Facebook verwerk-opties',$closer);
	ob_start();
	foreach ($events as $event) {
		$location = null;
		unset($time);
		/** @noinspection PhpRedundantOptionalArgumentInspection */
		extract($event, \EXTR_OVERWRITE);

		$no_time = empty($time);

		[$hour, $mins] = $no_time ? [12, 0] : explode(':', $time);

		$stamp_tzi = gmmktime(
			$hour,
			$mins,
			0,
			$event['month'],
			$event['day'],
			$event['year']
		);

		$recycled = false;
		$force_update = false;
		$show_old = false;

		if (false === ($partyids = db_simpler_array('fbid','
			SELECT ID
			FROM fbid
			WHERE ELEMENT = "party"
			  AND FBID = '.$fbid))
		) {
			return false;
		}
		if ($partyids) {
			if (false === ($assoc = db_same_hash('connect','
				SELECT MAINID, ASSOCID
				FROM connect
				WHERE MAINTYPE = "party"
				  AND MAINID IN ('.($partyidstr = implode(', ', $partyids)).')
				  AND ASSOCTYPE = "party"'))
			) {
				return false;
			}
			foreach ($partyids as $partyid) {
				/** @noinspection SuspiciousArrayElementInspection */
				$assoc[$partyid] = [$partyid => $partyid];
			}
			if ($assoc) {
				$groups = [];
				$id_to_group = '';
				foreach ($assoc as $mainid => $associds) {
					foreach ($groups as $group) {
						if (isset($group[$mainid])) {
							continue 2;
						}
					}
					$group = $associds;
					$group[$mainid] = $mainid;
					$groups[] = $group;
				}
				$this_event_in_group = [];
				foreach ($groups as $ndx => $group) {
					if (!($info = db_single_assoc(['party', 'fbid', 'feedevent'], '
						SELECT	MIN(STAMP_TZI) AS MIN_STAMP,
								MAX(STAMP_TZI + DURATION_SECS) AS MAX_STAMP,
								GROUP_CONCAT(PARTYID) AS PARTYIDSTR
						FROM party
						LEFT JOIN fbid ON ELEMENT = "party" AND ID = PARTYID
						LEFT JOIN feedevent_multiday ON MAINID = FBID
						WHERE PARTYID IN ('.($groupidstr = implode(', ', $group)).')
						  AND feedevent_multiday.MAINID IS NULL'))
					) {
						if ($info === false) {
							return false;
						}
						continue;
					}
					$id_to_group .= "IF(PARTYID IN ($groupidstr), $ndx, ";

					# add & substract 1 day, to allow us to not include camping days

					$this_event_in_group[] =
						$stamp_tzi.
						' BETWEEN '.($info['MIN_STAMP'] - ONE_DAY).
							' AND '.($info['MAX_STAMP'] + ONE_DAY);
				}
				$id_to_group .= '"rest"'.str_repeat(')',$ndx+1);

				if (false === ($find_use = db_single_assoc('party', /** @lang MariaDB */ "
					SELECT	COUNT(DISTINCT $id_to_group) AS TOTAL_USES,
							SUM(IF(".implode(' OR ', $this_event_in_group).", 1, 0)) AS SIMILAR_GROUPS,
							MAX(IF(PARTYID, PARTYID, 0)) AS PARTYID,
							party.STAMP_TZI
					FROM party
					WHERE PARTYID IN ($partyidstr)"))
				) {
					return false;
				}
				assert(is_array($find_use)); # Satisfy EA inspection
				if ($find_use) {
					if ($find_use['SIMILAR_GROUPS']) {
						continue;
					}
					if ($find_use['TOTAL_USES'] > 1) {
						$recycled = 'notice';
						$show_old = true;
					} elseif (
						$find_use['STAMP_TZI'] > CURRENTSTAMP
					&&	$stamp_tzi > CURRENTSTAMP
					) {
						$recycled = 'notice';
						$force_update = true;
					} else {
						$recycled = 'warning';
						$show_old = true;
					}
				}
			}
		}
		if (!($ignore = db_single_int('facebook_ignore', "
			SELECT 1
			FROM facebook_ignore
			WHERE FBID = $fbid"))
		) {
			if ($ignore === false) {
				return false;
			}
			continue;
		}
		?><tr<?
		?> data-fbid="<?= $fbid ?>"<?
		?>><?
		?><td><?
		$locationid = 0;
		$bestpct = 0;
		$organizationids = [];
		$location_name = null;
		if (!empty($location)) {
			$location_name = $location['name'] ?? null;
			if ($location_fbid = $location['fbid'] ?? null) {
				$locations = get_elements('location', $location_fbid);
				if ($locations) {
					$locationid = $locations[0]['id'];
				}
			}
		}
		if ($element === 'organization') {
			/** @noinspection SuspiciousArrayElementInspection */
			$organizationids = [$id => $id];
		}
		if ($locationid) {
			$use_locationid = $locationid;
		} elseif ($element === 'location') {
			$use_locationid = $id;
		} else {
			$use_locationid = 0;
		}
 		[$parsed_event_name/*, $subtitle */] = utf8_parse_event_title($name, [
			'locationid'		=> $use_locationid,
			'location'			=> $location_name,
			'organizationids'	=> $organizationids,
		]);

		// find parsed_event_name in past for this element

		$lower_ok_name = mb_strtolower($parsed_event_name);
		foreach ($oldeventnames as $old_name) {
			$pct = 0;
			$old_name = mb_strtolower($old_name);
			mb_similar_text($old_name, $lower_ok_name, $pct);
			$bestpct = max($pct, $bestpct);
		}

		if ($element === 'location'
		&&	$locationid
		&&	$locationid !== $id
		) {
			$other_locationid = $locationid;
		} else {
			$other_locationid = false;
		}

		$add = $bestpct >= 90;

		$win_id = 'fbopt-'.$element.'-'.$id;

		?><a class="<?
		if ($add) {
			?>bold <?
		}
		if ($recycled === 'warning') {
			?>warning <?
		} elseif ($force_update) {
			?>notice <?
		}
		?>" target="_blank"<?
		?> onclick="Pf.openFacebookToo(this,'<?= $win_id ?>',<?= $fbid ?>)"<?
		?> href="<?
		if ($force_update) {
			if ($find_use) {
				echo get_element_href('party',$find_use['PARTYID']);
			} else {
				?>abount:blank<?
			}
		} else {
			?>/party/register?<?
			if ($element === 'location') {
				?>LOCATIONID=<? echo $other_locationid ?: $id;
			} else {
				echo $upper_element ?>ID=<? echo $id;
				if ($locationid) {
					?>;LOCATIONID=<? echo $locationid;
				}
			}
		}
		?>"<?
		?>><?

		if (false !== ($pos = mb_stripos($name, $parsed_event_name))) {
			if ($pos) {
				?><span class="light7"><?= escape_utf8(mb_substr($name, 0, $pos)) ?></span><?=
					escape_utf8($parsed_event_name)
				?><span class="light7"><?= escape_utf8(mb_substr($name, $pos + mb_strlen($parsed_event_name))) ?></span><?

			} else {
				echo escape_utf8($parsed_event_name);
				?><span class="light7"><?= escape_utf8(mb_substr($name, mb_strlen($parsed_event_name))) ?></span><?
			}
		} else {
			echo escape_utf8($name);
		}
		if ($recycled) {
			?> <span<?
			?> class="<?= $recycled ?>"<?
			?>><?= BLACK_UNIVERSAL_RECYCLING_ENTITY
			?></span><?
		}
		if ($force_update) {
			?> &rarr; <?= mb_strtoupper(__('action:update'), 'windows-1252') ?> <?= element_name('old_event') ?>!<?
		}
		?></a><?

		if ($show_old
		&&	$find_use
		&&	($old = memcached_party_and_stamp($find_use['PARTYID']))
		) {
			?><br /><span class="light">&rarr; <?= element_name('old_event') ?>: <a<?
			?> target="_blank"<?
			?> onclick="Pf.openFacebookToo(this,'<?= $win_id ?>',<?= $fbid ?>)"<?
			?> href="<?= get_element_href('party',$find_use['PARTYID']) ?>"<?
			?> class="notice-nb"><?= escape_specials(get_element_title('party',$find_use['PARTYID'])) ?></a><?
			?> <small>@ <?
			_datetime_display($old['STAMP']);
			?></small><?
			?></span><?
		}
		if ($element === 'location') {
			if ($other_locationid) {
				?>, <span class="warning"><?=
					element_name('other_location') ?>: <?=
					get_element_link('location',$locationid)
				?></span><?
			}
		} else {
			?></td><td><?
			if (!empty($location)) {
				?><small><?
				if (!empty($location['elements'])) {
					# pick first
					?><span class="notice-nb"><?=
						escape_utf8(get_element_title('location',$location['elements'][0]['id']))
					?></span><?
				} else {
					?><span<?
					if (empty($location['fbid'])) {
						?> class="light7"<?
					}
					?>><?
					if (!empty($location['name'])) {
						echo escape_utf8($location['name']);
					}
					if (!empty($location['address'])) {
						?><small<?
						if (!empty($location['fbid'])) {
							?> class="light7"<?
						}
						?>><?
						if (!empty($location['name'])) {
							?>, <?
						}
						echo escape_utf8($location['address']);
						?></small><?
					}
					?></span><?
				}
				?><small><?
			}
		}
		?></td><?
		?><td class="right nowrap"><?

		change_timezone('UTC');
		$stamp = gmmktime($hour, $mins, 0, $month, $day, $year);
		if ($no_time) {
			_dateday_display($stamp, short: true);
			?> <span class="light">@ ??:??</span><?
		} else {
			_datedaytime_display($stamp, time_separator: ' <span class="light">@</span> ', short: true);
		}
		change_timezone();

		?></td><?
		?><td class="right nowrap"><?

		echo get_special_char('remove', [
			'title'			=> __('action:ignore_temporarily'),
			'onclick'		=> 'Pf.ignoreFacebookEvent(this, '.$fbid.', true)',
			'onmouseover'	=> "changerow(this, 'addclass', 'hilited-light-orange')",
			'onmouseout'	=> "changerow(this, 'remclass', 'hilited-light-orange')",
		]);

		if (!$recycled) {
			?> &nbsp; <?
			echo get_special_char('close', [
				'title'			=> __('action:ignore_permanently'),
				'onclick'		=> 'Pf.ignoreFacebookEvent(this, '.$fbid.')',
				'onmouseover'	=> "changerow(this, 'addclass', 'hilited-red')",
				'onmouseout'	=> "changerow(this, 'remclass', 'hilited-red')",
			]);
		}
		?></td><?
		?></tr><?

		$fbids[$fbid] = $stamp;
	}
	$data = ob_get_clean();

	if (HOME_THOMAS) {
		echo $debug;
	}

	?><div style="min-height: 250px;"><?
	if ($data) {
		?><table<?
		?> class="fw partylist nocellbrd vtop"<?
		?> id="fbeventlist"<?
		?> data-fbids="<?= escape_specials(safe_json_encode($fbids)) ?>"<?
		?>><?=
			$data
		?></table><?
	}
	?><table id="fbeventlistdone" class="<?
	if ($data) {
		?>hidden <?
	}
	?>fw" style="height: 250px;"><?
	?><tr><td class="center middle"><?=
		__('facebook:info:all_events_processed_LINE')
		?><br /><br /><?
		?><span style="font-size: 300%;"><?= SMILING_FACE_WITH_HEART_SHAPED_EYES_ENTITY ?></span><?
	?></td></tr><?
	?></table><?
	?></div><?

	layout_close_box();
	return true;
}

function which_facebook_events_remain(): http_status {
	require_once '_currentuser.inc';
	require_once '_identity.inc';
	global $currentuser;
	$currentuser ??= new _currentuser(TINY_USER | SKIP_SETTINGS);
	if (!have_admin('party')) {
		return http_status::FORBIDDEN;
	}
	if (!have_something($_POST, 'FBIDS')) {
		return http_status::BAD_REQUEST;
	}
	if (!($fbids = safe_json_decode($_POST['FBIDS'], true))) {
		error_log('BAD JSON: '.$_POST['FBIDS']);
		return http_status::BAD_REQUEST;
	}
	if (!is_array($fbids)) {
		return http_status::BAD_REQUEST;
	}
	$arr = ['FBIDS' => $fbids];

	if (!require_number_array($arr, 'FBIDS')) {
		return http_status::BAD_REQUEST;
	}
	$fbidstr = implodekeys(',', $fbids);

	if (false === ($ignore = db_same_hash('facebook_ignore', "
		SELECT FBID
		FROM facebook_ignore
		WHERE FBID IN ($fbidstr)"))
	) {
		return http_status::INTERNAL_SERVER_ERROR;
	}
	if ($remaining = array_diff_key($fbids, $ignore)) {
		if (false === ($haves = db_same_hash(['fbid', 'party'], "
			SELECT FBID, STAMP_TZI
			FROM fbid
			JOIN party ON PARTYID = ID
			WHERE ELEMENT = 'party'
			  AND FBID IN ($fbidstr)"))
		) {
			return http_status::INTERNAL_SERVER_ERROR;
		}
		assert(is_array($haves)); # Satisfy EA inspection
		foreach ($remaining as $fbid => $stamp) {
			if (!isset($haves[$fbid])) {
				continue;
			}
			# only register valid 'haves' if date is within range of 1 day from current
			foreach ($haves[$fbid] as $have_stamp) {
				if (abs($stamp - $have_stamp) <= ONE_DAY) {
					# prolly done!
					unset($remaining[$fbid]);
					break;
				}
			}
		}
	}
	header('Content-Type: application/json');
	echo safe_json_encode($remaining);
	return http_status::OK;
}

function ignore_facebook_event(): http_status {
	if (!require_idnumber($_POST, 'FBID')
	||	!require_element($_POST, 'ELEMENT', ['location', 'organization'])
	||	!require_idnumber($_POST, 'ID')
	) {
		return http_status::BAD_REQUEST;
	}
	require_once '_currentuser.inc';
	require_once '_identity.inc';
	global $currentuser;
	$currentuser ??= new _currentuser(TINY_USER | SKIP_SETTINGS);
	if (!have_admin('party')) {
		return http_status::FORBIDDEN;
	}
	if (!db_insert('facebook_ignore','
		INSERT IGNORE INTO facebook_ignore SET
			NAME	= "'.addslashes($_POST['NAME'] ?? '').'",
			CSTAMP	= '.CURRENTSTAMP.',
			CUSERID	= '.CURRENTUSERID.',
			FBID	= '.$_POST['FBID'].',
			ELEMENT	= "'.$_POST['ELEMENT'].'",
			ID		= '.$_POST['ID'].',
			TMPED	= '.(isset($_POST['TEMPORARY']) ? CURRENTSTAMP : 0))
	) {
		return http_status::INTERNAL_SERVER_ERROR;
	}
	return http_status::OK;
}
