<?php

function invoicelist_show($invoices) {
	if (!$invoices) {
		return;
	}
	require_once '_invoice.inc';
	require_once '_price.inc';

	if (!isset($invoices[0])) {
		list($ndx,$first) = keyval($invoices);
		reset($invoices);
	} else {
		$first = $invoices[0];
	}
	$do_ideal = array_key_exists('IDEALED',$first);

	layout_open_table('default fw vtop');
	layout_start_header_row();
	layout_start_header_cell();
	# DOWNLOAD
	layout_next_header_cell();
	echo Eelement_name('invoice');
	if ($show_mailed = array_key_exists('MAILED_AT',$first)) {
		layout_next_header_cell();
	}
	if ($show_relation = array_key_exists('RELATIONID',$first) || array_key_exists('DEBITOR_NAME',$first)) {
		require_once '_complete.inc';
		layout_next_header_cell();
		echo Eelement_name('relation');
	}
	layout_next_header_cell_right();
	echo Eelement_name('money_amount');
	if ($do_ideal) {
		layout_next_cell();
	}
	layout_next_header_cell_right();
	echo Eelement_name('date');
	layout_stop_header_cell();
	layout_stop_header_row();
	foreach ($invoices as $invoice) {
		extract($invoice);
		layout_start_rrow(0,
			//!empty($invoice['ALARM'])
			false
		?	'error ns'
		:	(
				$STATUS == 'ignore'
			?	'light unavailable'
			:	(	(	$STATUS == 'notpaid'
					||	$STATUS == 'irrecoverable'
					)
				?	(	$SENTSTAMP <= TODAYSTAMP - $invoice['PAY_WITHIN'] * ONE_DAY
					?	(	$SENTSTAMP <= TODAYSTAMP - $invoice['PAY_WITHIN'] * INVOICE_FAIL_MULT * ONE_DAY
						?	'error ns'
						:	'warning ns'
						)
					:	'warning-nb ns'
					)
				:	null
				)
			)
		);
		if ($invoice['PDF']
		||	db_single('invoicedata','SELECT 1 FROM data_db.invoicedata WHERE INVOICENR='.$INVOICENR)
		) {
			?><a href="/invoice_<?= $INVOICENR ?>.obj" target="_blank"><?
			?><img src="<?= STATIC_HOST ?>/images/download<?= is_high_res() ?>.png" class="icon" /><?
			?></a>&nbsp;<?
		}
		layout_next_cell();

		?><a href="/invoice/<?= $INVOICENR ?>"><?= $INVOICENR ?></a><?
		
		if ($show_mailed) {
			layout_next_cell(class: 'nowrap');
			if ($MAILED_AT) {
				?><<?
				if (!empty($MAILED_TICKETID)) {
					?>a href="/ticket/<?= $MAILED_TICKETID ?>" target="_blank"<?
				} else {
					?>span<?
				}
				?> title="<? _datetime_display($MAILED_AT) ?>" class="light seemtext"<?
				?>><?
				?><img class="mail lower" src="<?= STATIC_HOST ?>/images/mail<?= is_high_res() ?>.png" /><?
				?>&#10003;<?
				?></<?= !empty($MAILED_TICKETID) ? 'a' : 'span' ?>><?

			} elseif (!empty($CANNOT_MAIL)) {
				$show = true;
				$alt = __('status:no_email_registered');
				?><div class="relative ib" style="width:20px" title="<?= $alt ?>"><?
				?><img class="mail"<?
				?> src="<?= STATIC_HOST ?>/images/mail<?= is_high_res() ?>.png"<?
				?> alt="<?= $alt ?>"><?
				?><div class="abs z1" style="top:0;left:6px"><?
				show_incomplete_icon($show);
				?></div><?
				?></div><?
			}
		}
		
		if ($show_relation) {
			layout_next_cell();
			if ($RELATIONID) {
				echo get_element_link('relation',$RELATIONID);
			} elseif (!empty($DEBITOR_NAME)) {
				static $__debisrel;
				if (!isset($__debisrel[$DEBNR])) {
					$__debisrel[$DEBNR] = invoice_maybe_relation($INVOICENR);
				}
				if ($__debisrel[$DEBNR]) {
					?><span class="r">(<?= __('attrib:maybe'); ?>: <?
					foreach ($__debisrel[$DEBNR] as $relid => $bool) {
						if (!isset($firsts)) {
							$first = true;
						} else {
							?>, <?
						}
						echo get_element_link('relation',$relid);
					}
					?>)</span><?
				}
				echo escape_utf8($DEBITOR_NAME);
			}
		}
		layout_next_cell(class: 'nowrap');
		echo get_price($AMOUNT_NEW,'EUR',true);

		if ($do_ideal) {
			layout_next_cell();
			if ($IDEALED) {
				?>&nbsp;<small class="notice">iDEAL</small><?
			}
		}

		layout_next_cell(class: 'nowrap rigth');
		_date_display($SENTSTAMP, short: true, time_span: true);

		layout_stop_row();
	}
	layout_close_table();
}
