<?php

const FXS_FREE		  = 0;
const FXS_INVITE		= 1;
const FXS_REQUEST	   = 2;
const FXS_ALL   	= 3; # FXS_INVITE | FXS_REQUEST

// e.g. FXS_INVITE | FXS_REQUEST == both invites and requests are allowed

function flock_access_name(string $access): string {
	static  $__name;
	return	$__name[$access]
	??=	  __('flockaccess:'.$access);
}

function have_invitation(int $flockid): int {
	return memcached_single('flockinvitation','
		SELECT 1
		FROM flockinvitation
		WHERE FLOCKID = '.$flockid.'
		  AND USERID = '.CURRENTUSERID
	) ?: 0;
}
