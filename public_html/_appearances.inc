<?php

function appearance_comma(): string {
	return ', ';
}

function appearances_info(): array|false {
	static $__apptotals = null;
	if ($__apptotals !== null) {
		return $__apptotals;
	}
	if (!have_admin(['photographer','appearance'])) {
		return $__apptotals = false;
	}
	if (!($imgids = memcached_rowuse_array(['onphoto', 'image', 'locks'], '
		SELECT onphoto.IMGID, image.USERID, LOCK_LOCKERID, LOCK_EXPIRES
		FROM onphoto
		JOIN image USING (IMGID)
		LEFT JOIN locks
		  ON LOCK_ID = IMGID 
		 AND LOCK_TYPE = '.LOCK_APPEARANCE
	))) {
		return $__apptotals = false;
	}
	require_once '_photographers.inc';
	$iscammer = get_photographers();
	$minefirst = $restfirst = $minesize = $restsize = $camrestsize = 0;
	foreach ($imgids as $image) {
		if (!$image['LOCK_LOCKERID']
		||	 $image['LOCK_EXPIRES'] < CURRENTSTAMP
		||	 $image['LOCK_LOCKERID'] === CURRENTUSERID
		) {
			if ($image['USERID'] === CURRENTUSERID) {
				++$minesize;
				if (!$minefirst) {
		  			$minefirst = $image['IMGID'];
				}
			} elseif (isset($iscammer[$image['USERID']])) {
				++$camrestsize;
				$camrest[] = $image['IMGID'];
			} else {
				++$restsize;
				$rest[] = $image['IMGID'];
			}
		}
	}
	return $__apptotals = [
		$minesize,
		$minefirst,
		$restsize + $camrestsize,
		isset($rest) ? $rest[mt_rand(0, $restsize -1)] : (isset($camrest) ? $camrest[mt_rand(0, $camrestsize - 1)] : 0)
	];
}

function may_have_appearances(): bool {
	return	have_user()
	&&	have_admin(['appearance', 'photographer']);
}

function show_appearance_counters() {
	if (!have_user()
	||	!have_admin(['appearance', 'photographer'])
	) {
		return;
	}
	if (!($apptotals = appearances_info())) {
		return;
	}
	[$minesize,, $restsize] = $apptotals;

	if (($appadmin = have_admin('appearance'))
	&&	$restsize
	||	$minesize
	) {
		?>&nbsp;&middot;&nbsp;<?
		?><a href="/photo/sequence#thephoto"><?
		if ($minesize) {
			?><b><?= $minesize; ?></b><?
		}
		if ($appadmin
		&& $restsize
		) {
			if ($minesize) {
				?>.<?
			}
			echo $restsize;
		}
		?></a><?
	}
}
