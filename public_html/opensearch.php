<?php

define('CURRENTSTAMP',time());

header(	isset($_REQUEST['VIEWTEST'])
?	'Content-Type: application/xml'
:	'Content-Type: application/opensearchdescription+xml'
);

require_once '_modified.inc';

$mstamp = filemtime('opensearch.php');

if (not_modified($mstamp)) {
	http_response_code(304);
	exit;
}

#error_log('opensearch spec requested by agent '.$_SERVER['HTTP_USER_AGENT']);

require_once '_helper.inc';
require_once '_layout.inc';
require_once '_hosts.inc';
require_once '__translation.php';

echo '<?xml version="1.0" encoding="UTF-8"?>';

$prefix = 'https://'.$_SERVER['HTTP_HOST'];

?><OpenSearchDescription xmlns="https://a9.com/-/spec/opensearch/1.1/"><?
	?><ShortName><?= __('searchspec:action') ?></ShortName><?
	?><Description>Partyflock</Description><?
	?><Contact><EMAIL></Contact><?
	?><Image width="16" height="16"><?= $prefix, get_favicon() ?></Image><?
	?><InputEncoding>UTF-8</InputEncoding><?
	?><Url<?
	?> type="text/html"<?
	?> template="<?= $prefix ?>/search?TERMS={searchTerms}"<?
	?> method="get"<?
	?>><?
	?><SearchForm><?= $prefix ?>/search</SearchForm><?
?></OpenSearchDescription><?
