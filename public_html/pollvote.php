<?php

declare(strict_types=1);

define('CURRENTSTAMP',time());

require_once '_exit_if_readonly.inc';
require_once '_error.inc';
require_once '_currentuser.inc';
require_once '_identity.inc';
require_once '_memcache.inc';
require_once '_sort.inc';
require_once '_layout.inc';
require_once '_smiley.inc';
require_once '_ubb.inc';
require_once '_poll.inc';
require_once '_nocache.inc';
require_once '_run_and_exit.inc';

send_no_cache_headers();

header('Content-Type: text/html; charset=windows-1252');

function bail(int $errno): never {
	http_response_code($errno);
	display_messages_only();
	exit;
}

run_and_exit();

function main(): never {
	global $currentuser;
	$currentuser ??= new _currentuser();

	if (!have_user()) {
		bail(403);
	}
	if (!isset($_SERVER['ePOLLID'])) {
		bail(404);
	}
	$_REQUEST['NOMEMCAHCE'] = true;
	if (!commit_pollvote($_SERVER['ePOLLID'])) {
		bail(412);
	}
	if (isset($_REQUEST['COMPACT'])) {
		_poll_display_result_compact($_SERVER['ePOLLID'],isset($_REQUEST['ONPROFILE']));
	} else {
		_poll_display_result($_SERVER['ePOLLID'],isset($_REQUEST['SHOWBOX']));
	}
	bail(200);
}
