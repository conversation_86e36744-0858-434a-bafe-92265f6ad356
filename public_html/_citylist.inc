<?php

require_once '_countries.inc';
require_once '_namedoubles.inc';

class _citylist {
	private $cities	= null;
	private $total	= 0;
	private $wheres = null;

	public $show_people	= true;
	public $show_header	= true;
	public $show_country	= true;

	function query() {
		$this->wheres[] = 'city.DELETED=0';
		$this->cities = memcached_rowuse_hash_if_not_admin(
			'city',
			['city', 'province', 'country'], '
			SELECT	CITYID, city.NAME,
				COUNTRYID,
				PROVINCEID,province.NAME AS PROVINCE_NAME,
				municipality.NAME AS MUNICIPALITY_NAME
			FROM city
			LEFT JOIN country USING (COUNTRYID)
			LEFT JOIN province USING (COUNTRYID, PROVINCEID)
			LEFT JOIN municipality USING (MUNICIPALITID)'.
			($this->wheres ? ' WHERE '.implode(' AND ',$this->wheres) : ''),
			ONE_HOUR
		);
		if ($this->cities === false) {
			return false;
		}
		if ($this->cities) {
			$this->total = count($this->cities);
			if ($this->show_people) {
				$this->people = memcached_simple_hash(array('user','user_account'),'
					SELECT CITYID,COUNT(*)
					FROM user
					LEFT JOIN user_account USING (USERID)
					WHERE CITYID IN ('.implodekeys(',',$this->cities).')
					  AND STATUS="active"
					GROUP BY CITYID',
					ONE_HOUR
				);
			}
			country_city_sort($this->cities);
			generate_name_doubles($this->cities, 'NAME', 'city');
		}
		return true;
	}
	function have_rows() {
		return $this->total;
	}
	function display_header() {
		layout_start_header_row();
		layout_start_header_cell();
		echo Eelement_name('name');
		if ($this->show_people) {
			layout_next_header_cell_right();
			echo Eelement_plural_name('active_member');
		}
		layout_stop_header_cell();
		layout_stop_header_row();
	}
	function display_city($city) {
		extract($city);

		layout_start_rrow();
		echo get_element_link('city', $CITYID, $NAME);
		if (isset($city['DOUBLE'])
		&&	($city['DOUBLE'] & SAME_COUNTRY)
		) {
			?> <span class="light">(<?
			if ($DOUBLE & SAME_COUNTRY) {
				if ($PROVINCE_NAME) {
					echo get_element_link('province', $PROVINCEID, $PROVINCE_NAME);
				}
				if ($DOUBLE & SAME_PROVINCE) {
					if ($MUNICIPALITY_NAME) {
						?>, <? echo escape_utf8($MUNICIPALITY_NAME);
					}
				}
			}
			?>)</span><?
		} elseif ($city['MUNICIPALITY_NAME']) {
			?> <span class="light">(<?= escape_utf8($city['MUNICIPALITY_NAME']) ?>)</span><?
		}
		if ($this->show_people) {
			layout_next_cell(class: 'light right');
			if (isset($this->people[$CITYID])
			&&	($cnt = $this->people[$CITYID])
			) {
				?><a href="/city/<?= $CITYID ?>/users"><?= $cnt ?></a><?
			}
		}
		layout_stop_row();
	}
	function display() {
		if (!$this->cities) {
			return;
		}
		layout_open_table(TABLE_FULL_WIDTH);
		if ($this->show_header) {
			$this->display_header();
		}
		$countryid = 1;
		foreach ($this->cities as $city) {
			if ($countryid != $city['COUNTRYID']) {
				$countryid = $city['COUNTRYID'];
				if ($this->show_country) {
					?><tr><th colspan="2" class="left bold"><?= get_element_link('country',$city['COUNTRYID']);
					?></th></tr><?
				}
			}
			$this->display_city($city);
		}
		layout_close_table();
	}
	function name_like(string $str): void {
		$this->wheres[] = 'city.NAME LIKE "%'.str_replace(' ','%',addslashes($str)).'%"';
	}
	function in_country($id) {
		$this->wheres[] = 'city.COUNTRYID='.$id;
	}
	function in_province($id) {
		$this->wheres[] = 'city.PROVINCEID='.$id;
	}
	function in_region($id) {
		$this->wheres[] = 'city.REGIONID='.$id;
	}
	function within_radius(int $cityid, int $radius): void {
		if (!($city = db_single_assoc('city','
			SELECT LONGITUDE,LATITUDE
			FROM city
			WHERE CITYID = '.$cityid))
		||	!($cities = memcached_cities_within_radius(
			$city['LATITUDE'],
			$city['LONGITUDE'],
			$radius))
		) {
			$this->wheres[] = '0';
			return;
		}
		$this->wheres[] = 'city.CITYID IN ('.implode(',',$cities).')';
	}
}

# selected:			selected CITYID
# showother:		false = no, null => empty, true => 'other option text'
# skip:				disabled CITYID
# includedeleted:	show deleted cities

function citylist_display_all_options(
	int	  $selected			= 0,
	?bool $show_other		= null,
	int	  $skip				= 0,
	bool  $include_deleted	= false
) {
	$cities = memcached_rowuse_array_if_not_admin(
		'city',
		['city','province','country','municipality'], '
		SELECT	CITYID,city.NAME,city.COUNTRYID,city.PROVINCEID,
			city.COUNTRYID,
			province.NAME AS PROVINCE_NAME,
			municipality.NAME AS MUNICIPALITY_NAME
		FROM city
			LEFT JOIN country ON country.COUNTRYID = city.COUNTRYID
			LEFT JOIN province ON province.PROVINCEID = city.PROVINCEID
			LEFT JOIN municipality ON municipality.MUNICIPALITID = city.MUNICIPALITID'.
		($include_deleted ? null : ' WHERE city.DELETED=0').'
		ORDER BY
			IF(country.NAME = "Nederland", "1", country.NAME) ASC,
			city.NAME ASC,
			province.NAME ASC,
			municipality.NAME ASC',
		ONE_HOUR
	);
	if ($cities === false) {
		return;
	}
	if ($show_other !== false) {
		?><option value="0"><?
		if ($show_other) {
			echo __('form:select:other_option');
			?>: <?
		}
		?></option><?
	}
	citylist_display_options(MULTIPLE_COUNTRIES, $cities, $selected, $skip);
}

const MULTIPLE_COUNTRIES	= 1;
const SINGLE_COUNTRY		= 2;

function citylist_display_options(int $type, array $cities, int $selected = 0, int $skip = 0): void {
	generate_name_doubles($cities, 'NAME', 'city');
	if ($type === MULTIPLE_COUNTRIES) {
		$countryid = 0;
		foreach ($cities as $city) {
			/** @noinspection PhpRedundantOptionalArgumentInspection */
			extract($city, \EXTR_OVERWRITE);

			if ($countryid !== $COUNTRYID) {
				if ($countryid) {
					?></optgroup><?
				}
				$countryid = $COUNTRYID;
				?><optgroup<?
				?> data-countryid="<?= $COUNTRYID ?>"<?
				?> label="<?= __('country:'.$COUNTRYID)
				?>"><?
			}
			?><option value="<?= $CITYID?>"<?
			if ($skip
			&&	$skip === $CITYID
			) {
				?> disabled<?
			}
			if ($selected
			&&	$selected === $CITYID
			) {
				?> selected<?
			}
			?>><?
			echo escape_utf8($NAME);

			// show only doubles for cities in The Netherlands

			if ($COUNTRYID === 1
			&&	isset($city['DOUBLE'])
			) {
				?> (<?
				if ($PROVINCE_NAME) {
					echo escape_utf8($PROVINCE_NAME);
				}
				if ($MUNICIPALITY_NAME) {
					?>, <?
					echo escape_utf8($MUNICIPALITY_NAME);
				}
				?>)<?
			} elseif ($MUNICIPALITY_NAME) {
				?> (<?= escape_utf8($MUNICIPALITY_NAME) ?>)<?
			}
			?></option><?
		}
		if ($countryid) {
			?></optgroup><?
		}
	} else {
		foreach ($cities as $city) {
			extract($city);

			?><option value="<?= $CITYID; ?>"<?
			if ($CITYID === $selected) {
				?> selected="selected"<?
			}
			?>><?
			echo escape_utf8($NAME);

			// show only doubles for cities in The Netherlands
			if (isset($city['DOUBLE'])) {
				?> (<?
				if ($PROVINCE_NAME) {
					echo escape_utf8($PROVINCE_NAME);
					if ($MUNICIPALITY_NAME) {
						?>, <?
					}
				}
				if ($MUNICIPALITY_NAME) {
					echo escape_utf8($MUNICIPALITY_NAME);
				}
				?>)<?
			} elseif ($MUNICIPALITY_NAME) {
				?> (<?= escape_utf8($MUNICIPALITY_NAME) ?>)<?
			}
			?></option><?
		}
	}
}

const INCLUDE_OTHER				= 0x1;
const SHOW_RADIUS				= 0x2;
const INCLUDE_EMPTY_COUNTRIES	= 0x4;
const SHOW_NOTICE_IF_EMPTY		= 0x8;
const COUNTRY_REQUIRED			= 0x20;
const CITY_REQUIRED				= 0x40;
const OTHER_SELECTED			= 0x80;
const DYNLOC_GROUP_INTERESTING	= 0x100;
const DYNLOC_DISABLED			= 0x200;
const UPDATE_MAP				= 0x400;

function display_dynamic_country_options(
	int $selected = 1,
	int $flags = 0,
	string $prefix = '',
	string $postfix = ''
): void {
	global $_paired_countryid;
	$_paired_countryid = $selected;
	$countries = ($flags & INCLUDE_EMPTY_COUNTRIES) ? get_countries() : get_nonempty_countries();
	if (!$countries) {
		return;
	}
	include_js('js/choosecity');
	?><select<?
	?> onchange="changeCountry(this, <?= $flags ?>, '<?= $prefix ?>', '<?= $postfix ?>')"<?
	?>  onkeyup="changeCountry(this, <?= $flags ?>, '<?= $prefix ?>', '<?= $postfix ?>')"<?
	?> id="<?= $prefix ?>countryid"<?
	?> name="<?= $prefix ?>COUNTRYID"<?
	if ($required = $flags & COUNTRY_REQUIRED) {
		?> required<?
	}
	if ($flags & DYNLOC_DISABLED) {
		?> disabled<?
	}
	?>><?
	?><option<?
	if (!$required) {
		?> value="0"<?
	}
	?>></option><?
	if ($flags & DYNLOC_GROUP_INTERESTING) {
		# show important countries first
		if ($city = get_current_city()) {
			$countryid = $city['COUNTRYID'];
			$name = $countries[$countryid];
			?><option<?
			if ($selected
			&&	$selected === $countryid
			) {
				$selected = null;
				?> selected="selected"<?
			}
			?> value="<?= $countryid;
			?>"><?= escape_specials($name);
			?></option><?
			?><option disabled></option><?
		}
	}
	foreach ($countries as $countryid => $name) {
		?><option<?
		if ($selected
		&&	$selected === $countryid
		) {
			?> selected<?
		}
		?> value="<?= $countryid;
		?>"><?= escape_specials($name);
		?></option><?
	}
	?></select><?
}

function display_dynamic_city_options(
	int		$selected	= 0,
	int		$flags		= 0,
	string	$prefix		= '',
	string	$postfix	= '',
): void {
	global $_paired_countryid;
	require_once 'include.php';
	include_something([
		'INCLUDE'	=> 'cities',
		'COUNTRYID'	=> $_paired_countryid,
		'SELECTED'	=> $selected,
		'FLAGS'		=> $flags,
		'RADIUS'	=> have_idnumber($_REQUEST,'RADIUS'),
		'PREFIX'	=> $prefix,
		'POSTFIX'	=> $postfix,
	]);
}

function display_dynamic_radius(
	int		$selected	= 0,
	int		$flags		= 0,
	string	$prefix		= '',
	string	$postfix	= '',
): void {
	?><input<?
	if ($selected) {
		?> value="<?= $selected ?>"<?
	}
	if ($flags & DYNLOC_DISABLED) {
		?> disabled<?
	}
	?> type="number"<?
	?> data-valid="number"<?
	?> class="right three_digits"<?
	?> name="<?= $prefix ?>RADIUS<?= $postfix ?>" /> <?
	echo __('abbr:kilometer');
}

function display_dynamic_city_pair(
	int		$cityid		= 0,
	string	$separator	= '<br />',
	int		$countryid	= 0,
	int		$flags		= 0,
	int		$radius		= 0,
	string	$prefix		= '',
): bool {
	if ( $cityid
	&&	!$countryid
	&&	!($countryid = memcached_single('city','SELECT COUNTRYID FROM city WHERE CITYID = '.$cityid))
	) {
		return false;
	}
	display_dynamic_country_options(selected: $countryid, flags: $flags, prefix: $prefix);
	echo $separator;
	display_dynamic_city_options(selected: $cityid, flags: $flags, prefix: $prefix);
	if ($flags & SHOW_RADIUS) {
		echo $separator;
		display_dynamic_radius(selected: $radius, flags: $flags, prefix: $prefix);
	}
	return true;
}

function include_cities_list(array $args): int {
	if (false === require_number($args, 'COUNTRYID')
	||	false === require_number($args, 'FLAGS')
	||	!optional_number($args, 'SELECTED')
	) {
		return 400;
	}
	if (!($mstamp = memcached_single_if_not_admin('city', 'city', 'SELECT GREATEST(MAX(MSTAMP), MAX(CSTAMP)) FROM city'))) {
		return $mstamp === false ? 503 : 404;
	}
	if (str_starts_with($_SERVER['SCRIPT_URI'], '/include/')) {
		require_once '_modified.inc';
		if (not_modified($mstamp, 'v2')) {
			return 304;
		}
	}
	$countryid		= $args['COUNTRYID'];
	$flags			= $args['FLAGS'];
	$include_other	= $flags & INCLUDE_OTHER;
	$other_selected = $flags & OTHER_SELECTED;

	if ($countryid
	&&	false === ($cities = memcached_rowuse_array_if_not_admin('city', ['city', 'province', 'municipality'], "
			SELECT	CITYID, city.NAME, city.COUNTRYID, city.PROVINCEID,
					province.NAME AS PROVINCE_NAME,
					municipality.NAME AS MUNICIPALITY_NAME
			FROM city
			LEFT JOIN province ON province.PROVINCEID = city.PROVINCEID
			LEFT JOIN municipality ON municipality.MUNICIPALITID = city.MUNICIPALITID
			WHERE city.DELETED = 0
			  AND city.COUNTRYID = $countryid
			ORDER BY
				city.NAME,
				province.NAME,
				municipality.NAME",
			ONE_HOUR
	))) {
		return 503;
	}
	$prefix  = $args['PREFIX']  ?? '';
	$postfix = $args['POSTFIX'] ?? '';

	?><span id="<?= $prefix ?>cityselect<?= $postfix ?>"><?
	if ($include_other
	||	!empty($cities)
	) {
		if ($include_other) {
			$onchange[] = "setdisplay('{$prefix}cityfield$postfix' ,this.value ==='0' &amp;&amp; this.selectedIndex !== 0)";
		}
		if ($flags & UPDATE_MAP) {
			$onchange[] = 'update_mapsqf(this.form)';
		}
		?><select<?
		if (isset($onchange)) {
			?> onchange="<?= implode(';',$onchange) ?>"<?
			?> onkeyup="<?= implode(';',$onchange) ?>"<?
		}
		if ($required = $flags & CITY_REQUIRED) {
			?> required<?
		}
		if ($flags & DYNLOC_DISABLED) {
			?> disabled<?
		}
		?> name="<?= $prefix ?>CITYID<?= $postfix ?>"<?
		?>   id="<?= $prefix ?>cityid<?= $postfix ?>"><?
		?><option<?
		if (!$required) {
			?> value="0"<?
		}
		?>></option><?
		if ($include_other) {
			?><option<?
			if ($other_selected) {
				?> selected<?
			}
			?> value="0"><?= __('form:select:other_option'); ?>&hellip;</option><?
		}
		if (!empty($cities)) {
			citylist_display_options(SINGLE_COUNTRY, $cities, $args['SELECTED'] ?? 0);
		}
		?></select><?

	} elseif (
		$countryid
	&&	($flags & SHOW_NOTICE_IF_EMPTY)
	) {
		echo __('include:cities:no_cities_LINE', DO_UBB, ['COUNTRYID' => $countryid]);
	}

	$empty = empty($cities);

	if (($empty = empty($cities))
	&&	($flags & CITY_REQUIRED)
	) {
		show_input([
			'type'		=> 'hidden',
			'name'		=> $prefix.'CITYID'.$postfix,
			'required'	=> true,
		]);
	}

	?></span><?
	if (!empty($GLOBALS['inced'])
	&&	$empty
	) {
		return 204;
	}
	return 200;
}
