<?php

require_once '_directmessage.inc';

class _directcontentlist {
	private $dmsgs;
	private $dmsgcnt = 0;

	function numrows() {
		return $this->dmsgcnt;
	}
	function query_conversation($withid) {
		$this->total = db_single('directmessage','
			SELECT COUNT(*)
			FROM directmessage
			WHERE READM IN (0,1) AND   TO_DELETED=0 AND TO_USERID='.CURRENTUSERID.' AND FROM_USERID='.$withid.	   '
			   OR READM IN (0,1) AND FROM_DELETED=0 AND TO_USERID='.$withid.	  ' AND FROM_USERID='.CURRENTUSERID
		);
		if (!$this->total) {
			return false;
		}
		$maxperpage = setting('DMESSAGES_PER_PAGE');
		if ($this->total > $maxperpage) {
			require_once '_pagecontrols.inc';
			$this->controls = new _pagecontrols;
			$this->controls->set_per_page($maxperpage);
			$this->controls->include_all(false);
			$this->controls->show_prev_next();
			$this->controls->set_total($this->total);
			$this->controls->set_url('/msg/conversation/'.$withid);
			$this->controls->set_order('MESSAGEID');
			$this->dmsgs = db_rowuse_array(
				'directmessage','
				SELECT '.DMSG_FIELDS.'
				FROM directmessage 
				WHERE READM IN (0,1) AND   TO_DELETED=0 AND TO_USERID='.CURRENTUSERID.' AND FROM_USERID='.$withid.'
				   OR READM IN (0,1) AND FROM_DELETED=0 AND TO_USERID='.$withid.	  ' AND FROM_USERID='.CURRENTUSERID.
				$this->controls->order_and_limit()
			);
		} else {
			$this->dmsgs = db_rowuse_array(
				'directmessage','
				SELECT '.DMSG_FIELDS.'
				FROM directmessage 
				WHERE READM IN (0,1) AND   TO_DELETED=0 AND TO_USERID='.CURRENTUSERID.' AND FROM_USERID='.$withid.'
				   OR READM IN (0,1) AND FROM_DELETED=0 AND TO_USERID='.$withid.	  ' AND FROM_USERID='.CURRENTUSERID
			);
		}
		$this->dmsgcnt = $this->dmsgs ? count($this->dmsgs) : 0;
		return $this->dmsgs ? true : false;
	}
	function query_to($to,$from) {
		# FIXME: almost same as dmsgunread query in _currentuser
		$this->total = db_single('directmessage','
			SELECT COUNT(*)
			FROM directmessage
			WHERE READM=0
			  AND '.(CURRENTUSERID == $to ? 'TO_DELETED=0' : 'FROM_DELETED=0').'
			  AND TO_USERID='.$to.'
			  AND FROM_USERID='.$from
		);
		if (!$this->total) {
			return false;
		}
		$maxperpage = setting('DMESSAGES_PER_PAGE');
		if ($this->total > $maxperpage) {
			$otherid = ($incoming = CURRENTUSERID == $to ? $from : $to);
			require_once '_pagecontrols.inc';
			$this->controls = new _pagecontrols;
			$this->controls->set_per_page($maxperpage);
			$this->controls->include_all(false);
			$this->controls->show_prev_next();
			$this->controls->set_total($this->total);
			$this->controls->set_url('/msg/'.($incoming ? 'incoming' : 'outgoing').'/'.$otherid);
			$this->controls->set_order('MESSAGEID');
			$this->dmsgs = db_rowuse_array('directmessage','
				SELECT '.DMSG_FIELDS.'
				FROM directmessage
				WHERE READM=0
				  AND '.($incoming ? 'TO_DELETED=0' : 'FROM_DELETED=0').'
				  AND TO_USERID='.$to.'
				  AND FROM_USERID='.$from.
				$this->controls->order_and_limit()
			);
		} else {
			$this->dmsgs = db_rowuse_array('directmessage','
				SELECT '.DMSG_FIELDS.'
				FROM directmessage
				WHERE READM = 0
				  AND '.(CURRENTUSERID == $to ? 'TO_DELETED=0' : 'FROM_DELETED=0').'
				  AND TO_USERID = '.$to.'
				  AND FROM_USERID = '.$from
			);
		}
		$this->dmsgcnt = $this->dmsgs ? count($this->dmsgs) : 0;
		return $this->dmsgs ? true : false;
	}
	function display() {
		if (!$this->dmsgs) {
			return;
		}
		require_once 'defines/directmessage.inc';
		if (isset($this->controls)) {
			ob_start();
			$this->controls->display();
			$controls = ob_get_contents();
			ob_end_flush();
		}
		require_once '_commentobject.inc';
		number_reverse_sort($this->dmsgs,'MESSAGEID');
		$shown = 0;
		foreach ($this->dmsgs as $dmsg) {
			if (CURRENTUSERID == $dmsg['TO_USERID']
			&&	$dmsg['TO_DELETED']
			||	CURRENTUSERID == $dmsg['USERID']
			&&	$dmsg['FROM_DELETED']
			) {
				continue;
			}
			++$shown;
			comment_display('directmessage', null, $dmsg);
			$this->firstmessageid = $dmsg['MESSAGEID'];
			if (!($dmsg['FLAGS'] & DMSG_SEEN)) {
				$mark_seen_ids[] = $dmsg['MESSAGEID'];
			}
		}
		if (!$shown) {
			layout_open_box('white');
			?><div class="block"><?= __('directcontentlist:all_messages_removed_LINE'); ?></div><?
			layout_close_box();
		} elseif (!empty($mark_seen_ids)) {
			$idstr = implode(',',$mark_seen_ids);
			db_update('directmessage','
				UPDATE directmessage SET FLAGS = FLAGS | '.DMSG_SEEN.'
				WHERE MESSAGEID IN ('.$idstr.')'
			);
		}
		if (isset($controls)) {
			echo $controls;
		}
	}
	function get_first_messageid() {
		return isset($this->firstmessageid) ? $this->firstmessageid : 0;
	}
}
