<?php

require_once '_date.inc';

const PARTY_FLAG_HIDDEN_LINEUP_PSTAMP    = 1;
const PARTY_FLAG_HIDDEN_TIMETABLE_PSTAMP = 2;

function offset_party_stamp(int $stamp): int {
	static $actual;
	$tz = date_default_timezone_get();
	return $actual[$tz][$stamp] ?? ($actual[$tz][$stamp] = strtotime('-6 hours', $stamp));
}

function offset_day_start(int $day_start): int {
	static $actual;
	$tz = date_default_timezone_get();
	return $actual[$tz][$day_start] ?? ($actual[$tz][$day_start] = strtotime('+6 hours', $day_start));
}

function show_party_time(array $party, bool $rich_snippet = true, ?string $timezone = null): void {
	$duration = $party['DURATION_SECS'];

	if (HOME_OFFICE
	&&	$timezone
	&&	!str_contains($timezone, 'Europe')
	) {
		change_timezone('Europe/Amsterdam');
		?><div class="warning block"><?
		?>Europe/Amsterdam: <?
		if ($party['DURATION_SECS']) {
			?><br /><?
		}
		_datedaytime_display($party['STAMP']);
		if ($party['DURATION_SECS']) {
			?><br /><?
			_datedaytime_display($party['STAMP'] + $party['DURATION_SECS']);
		}
		?></div><?
		change_timezone();
	}

	change_timezone($timezone ?: 'UTC');

	if (!$timezone) {
		if (!isset($party['STAMP_TZI'])) {
			error_log('no stamp_tzi at '.$_SERVER['REQUEST_URI'],0);
		}
		$stamp = $party['STAMP_TZI'];
	} else {
		$stamp = $party['STAMP'];
	}
	if ($at2400 = !empty($party['AT2400'])) {
		--$stamp;
		if ($duration) {
			++$duration;
		}
	}
	$start_stamp = $stamp;
	$stop_stamp  = $stamp + $duration;
	if (!$rich_snippet) {
		$start_open = '';
		$stop_open = '';
		$close = '';
	} else {
		$start = date('c',$start_stamp);
		if ($party['NOTIME']) {
			$start = preg_replace('"^(\d+-\d+-\d+)T\d+:\d+:\d+(.*)$"','$1$2',$start);
		} else {
			# endDate is interpreted as inclusive in Google, so make sure it is 1 second less than start + duration
			$stop = date('c',$stop_stamp - 1);
			$stop_open  = '<time itemprop="endDate" datetime="'.$stop.'">';
		}
		$start_open = '<time itemprop="startDate" datetime="'.$start.'">';
		$close = '</time>';
	}
	if ($stop_stamp
		# only 1 transition supported
	&&	($switch = get_dst_switch_times($timezone ?: 'UTC',$start_stamp,$stop_stamp))
	) {
		[$switch_stamp, $switch_offset, $pre_switch_hour, $pre_switch_mins, $switch_hour, $switch_mins] = $switch;
		ob_start();
		?><small class="light8" style="margin: 0 1em;"><?
		?><?= __($switch_offset > 0 ? 'party:info:start_daylight_savings' : 'party:info:stop_daylight_savings') ?>: <?
		?> <?
		printf('%02d:%02d',$pre_switch_hour, $pre_switch_mins) ?> &rarr; <? printf('%02d:%02d',$switch_hour, $switch_mins);
		?></small><br /><?
		$dst_info = ob_get_clean();
	} else {
		$dst_info = '';
	}
	if (!$duration
	||	$party['NOTIME']
	) {
		echo $start_open;
		?><span class="date"><? dateday_display_link($start_stamp) ?></span><?
		if (!$party['NOTIME']) {
			?><br /><?
			if ($at2400) {
				?>24:00<?
			} else {
				_time_display($start_stamp);
			}
		}
	} else {
		[,,, $start_hour, $start_mins] = _getdate($start_stamp);
		[,,,   $end_hour,   $end_mins] = _getdate( $stop_stamp);

		if (!$start_hour
		&&	!$start_mins
		&&	!$end_hour
		&&	!$end_mins
		) {
			# special case 00:00 - 24:00
			# using start/stop hour and mins to support DST switches

			echo $start_open;
			?><span class="date nowrap"><? dateday_display_link($start_stamp,' ',false,true) ?></span> <?
			?>00:00<?
			?></span><?= $close ?> &ndash; <?= $stop_open ?>24:00<?

		} elseif ($party['DURATION_SECS'] < ONE_DAY) {
			echo $start_open;
			?><span class="date"><? dateday_display_link($start_stamp); ?></span><br /><?
			echo $dst_info;

			if ($at2400) {
				?>24:00<?
			} else {
				_time_display($start_stamp);
			}
			echo $close;
			?> &ndash; <?
			echo $stop_open;

			if ($dst_info) {
				if ($stop_stamp !== $switch_stamp
				||	$switch_offset < 0
				) {
					[,,, $stop_hour, $stop_mins] = _getdate($stop_stamp);
					printf('%02d:%02d',$stop_hour,$stop_mins);
					?> <small>(<?= __($stop_stamp >= $switch_stamp ? 'party:info:after_dst_transition' : 'party:info:before_dst_transition') ?>)</small><?
				} else {
					# forward, show non-existent time, feel more logical
					printf('%02d:%02d',$pre_switch_hour, $pre_switch_mins);
						?> <small>(<?= __('party:info:before_dst_transition') ?>)</small><?
				}
				if ($switch_offset < 0
				&&	$stop_stamp >= $switch_stamp + $switch_offset
				&&	$stop_stamp <  $switch_stamp - $switch_offset
				&&	$party['CSTAMP'] > (CURRENTSTAMP - ONE_HOUR)
				) {
					?><br /><span class="warning">Controleer of dit evenement voor of na ingang wintertijd plaatsvindt!</span><?
				}
			} else {
				_time_display($stop_stamp);
			}
		} else {
			echo $start_open;
			?><span class="date nowrap"><? dateday_display_link($start_stamp,' ',false,true) ?></span> <?
			if ($at2400) {
				?>24:00<?
			} else {
				_time_display($start_stamp);
			}
			?></span><?= $close ?> &ndash; <?= $stop_open ?><?
			?><span class="date nowrap"><? dateday_display_link($stop_stamp,' ',false,true) ?></span> <?
			_time_display($stop_stamp);
		}
	}
	if (isset($party['DOOR_CLOSE_HOUR'])) {
		?> <small class="light">(<?= __('attrib:entry_till') ?>: <? printf('%02d:%02d',$party['DOOR_CLOSE_HOUR'],$party['DOOR_CLOSE_MINS']); ?>)</small><?
	}
	echo $close;

	if ($party['LIVESTREAM'] === 'only'
	&&	!$timezone
	) {
		[,,,,,,, $dst] = _getdate($party['STAMP']);
		?> <span class="light"><?=
			!$dst ? 'CEST' : 'CET';
		?></span><?
	}

	change_timezone();

	if ($party['DURATION_SECS'] > 5 * ONE_DAY) {
		?><br /><?
		?><span class="warning"><?=
			__('party:warning:long_party_LINE', ['DAYS' => round($party['DURATION_SECS'] / ONE_DAY)])
		?></span><?
	}
}

function get_for_future_stamp_tzi(int $offset = -2): int {
	return TODAYSTAMP_TZI + HOUR_DAY_START * ONE_HOUR + $offset * ONE_DAY;
}

function show_vevent(array $party): void {
	if ($party['POSTPONED']) {
		?><meta itemprop="eventStatus" content="https://schema.org/EventPostponed" /><?
	} elseif ($party['CANCELLED']) {
		?><meta itemprop="eventStatus" content="https://schema.org/EventCancelled" /><?
	} else {
		?><meta itemprop="eventStatus" content="https://schema.org/EventScheduled" /><?
	}
	?><meta itemprop="eventAttendanceMode" content="<?=
		match ($party['LIVESTREAM']) {
			'only'		=> 'https://schema.org/OnlineEventAttendanceMode',
			'+event'	=> 'https://schema.org/MixedEventAttendanceMode',
			default		=> 'https://schema.org/OfflineEventAttendanceMode',
		}
	?>" /><?
	if (!empty($party['VISITORCNT'])) {
		?><meta itemprop="maximumPhysicalAttendeeCapacity" content="<?= $party['VISITORCNT'] ?>" /><?
	}
}

function get_fbs_for_party(int $partyid, ?string &$absolute_fb_jump = null): array {
	$where_url_is_bad = where_url_is_bad();
	if (!($fbs = db_rowuse_array(['fbid', 'feedevent_multiday', 'feedevent', 'urlcheck', 'fbcounters', 'facebook_guests', 'facebook_event_name', 'presence'], <<<MARIADB
		SELECT PRESENCEID,SITE, $where_url_is_bad AS BAD,
			REPLACE(REPLACE(SITE, 'https://www.facebook.com/events/', ''), '/', '') AS FBID,
			(SELECT 1 FROM feedevent_multiday WHERE MAINID     = REPLACE(REPLACE(SITE, 'https://www.facebook.com/events/', ''), '/', '') LIMIT 1) AS MAIN,
			(SELECT 1 FROM feedevent_multiday WHERE INSTANCEID = REPLACE(REPLACE(SITE, 'https://www.facebook.com/events/', ''), '/', '') LIMIT 1) AS INSTANCE,
			COALESCE(GOING,ATTENDING) AS ATTENDING, /* facebook_guests.GOING is newer */
			GUESTS,
			(	SELECT ACCESS
				FROM feedevent
				WHERE FEEDID=REPLACE(REPLACE(SITE,  'https://www.facebook.com/events/', ''), '/', '')
				ORDER BY FEEDEVENTID DESC
				LIMIT 1
			) AS ACCESS,
			COALESCE(fen.NAME,(
				SELECT NAME
				FROM feedevent
				WHERE FEEDID=REPLACE(REPLACE(SITE,'https://www.facebook.com/events/',''),'/','')
				ORDER BY FEEDEVENTID DESC
				LIMIT 1
			)) AS NAME
		FROM presence
		LEFT JOIN urlcheck ON urlcheck.ELEMENT = 'presence' AND urlcheck.ID = PRESENCEID
		LEFT JOIN fbcounters ON fbcounters.FBID = REPLACE(REPLACE(SITE, 'https://www.facebook.com/events/',''), '/', '')
		LEFT JOIN facebook_guests ON facebook_guests.FBID = REPLACE(REPLACE(SITE, 'https://www.facebook.com/events/', ''), '/', '')
		LEFT JOIN facebook_event_name fen ON fen.FBID = REPLACE(REPLACE(SITE, 'https://www.facebook.com/events/', ''), '/', '')
		WHERE presence.ELEMENT = 'party'
		  AND presence.ID= $partyid
		  AND SITE LIKE '%facebook.com/events/%'
		ORDER BY ATTENDING DESC, MAIN DESC, NAME
		MARIADB))
	) {
		return [];
	}
	foreach ($fbs as $fb) {
		if ($fb['BAD']) {
			continue;
		}
		$absolute_fb_jump = "https://{$_SERVER['HTTP_HOST']}/jumpto/presence/{$fb['PRESENCEID']}";
		break;
	}
	return $fbs;
}
