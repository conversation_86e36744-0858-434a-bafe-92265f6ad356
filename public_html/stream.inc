<?php

require_once '_genrelist.inc';
require_once '_stream.inc';

function perform_commit(): ?bool {
	switch ($_REQUEST['ACTION']) {
	default:
	case null:												return null;
	case 'commit':											return stream_commit();
	case 'removemember':									return stream_remove_member();
	case 'activate':		require_once '_object.inc';		return set_field('ACTIVE',"b'1'",'activated');
	case 'deactivate':		require_once '_object.inc';		return set_field('ACTIVE',"b'0'",'deactivated');
	case 'remove':			require_once '_remove.inc';		return remove_element();
	case 'combine':			require_once '_combine.inc';	return combine_element();
	case 'employees':		require_once '_employees.inc';	return employee_action();
	case 'check':											return stream_check();
	}
}
function display_body() {
	switch ($_REQUEST['ACTION']) {
	default:		not_found(); return;
	case null:
	case 'remove':
	case 'archive':
	case 'offline':
	case 'popular':
	case 'genre':		return stream_display_overview();
	case 'commit':
	case 'activate':
	case 'deactivate':
	case 'comment':
	case 'comments':
	case 'check':
	case 'fans':
	case 'combine':
	case 'description':
#		robot_action('noindex');
	case 'single':
		return stream_display_single();
	case 'combinewith':	require_once '_combine.inc'; return show_combine_with();
	case 'form':		return stream_display_form();
	case 'bioform':		require_once '_bio.inc'; return show_bio_form();
	case 'employees':
		if (!$GLOBALS['commitresult']
		&&	$_REQUEST['SUBACTION'] != 'form'
		) {
			return display_employees();
		} else {
			return display_employee_form();
		}
	}
}
function get_basedir() {
	return	SERVER_SANDBOX ? 'sandbox' : 'party';
}
function stream_check() {
	require_once '_execute.inc';
	if (!require_admin('stream')
	||	!($streamid = require_idnumber($_REQUEST,'sID'))
	) {
		return;
	}
	$stream = db_single_assoc('stream','SELECT ACTIVE,FAILED FROM stream WHERE STREAMID='.$streamid);
	if ($stream === false) {
		return false;
	}
	if (!$stream) {
		not_found(); return;
	}
	[$rc,$stdout,$stderr] = execute('/home/'.get_basedir().'/maintenance/check_streams.php '.$_REQUEST['sID'].' 2>&1');
	#print_rr($rc,'rc');
	#print_rr($stdout,'stdout');
	#print_rr($stderr,'stderr');
	$GLOBALS['__checkoutput'] = $stdout;
}
function stream_overview_menu() {
	layout_open_menu();
	layout_menuitem(Eelement_name('overview'),'/stream',!$_REQUEST['ACTION']);
	layout_menuitem(__C('field:popular'),'/stream/popular/month',$_REQUEST['ACTION'] == 'popular' && $_REQUEST['SUBACTION'] == 'month');
	layout_menuitem(__C('action:offline'),'/stream/offline',$_REQUEST['ACTION'] == 'offline');
	layout_menuitem(Eelement_name('archive'),'/stream/archive',$_REQUEST['ACTION'] == 'archive');
	layout_continue_menu();
	layout_menuitem(__C('action:help'),'/help/stream');
	layout_close_menu();
}

function stream_single_menu(?array $stream = null): void {
	if (!have_user()) {
		return;
	}
	if (!have_admin('stream')) {
		layout_open_menu();
		layout_menuitem(__C('action:add'),'/ticket/form?ELEMENT=stream');
		layout_close_menu();
		return;
	}
	layout_open_menu();
	if ($stream) {
		$streamhref = '/stream/'.$_REQUEST['sID'];
		layout_menuitem(__C('action:change'),$streamhref.'/form');
		layout_menuitem(__C('action:change_information'),$streamhref.'/bioform');
		layout_menuitem(Eelement_plural_name('employee'),$streamhref.'/employees');
		if ($stream['ACTIVE']) {
			layout_menuitem(__C('action:deactivate'),$streamhref.'/deactivate');
		} else {
			layout_menuitem(__C('action:activate'),$streamhref.'/activate');
		}
		layout_next_menuitem();
		?><a<?
		if (!$stream['FAILED']) {
			?> class="light"<?
		}
		?> href="<?= $streamhref; ?>/check"><?= __C('action:online_check'); ?></a><?
		layout_next_menuitem();
		connect_menuitem();
		layout_close_menuitem();
		layout_continue_menu();
		show_element_menuitems();
	} else {
		layout_menuitem(__C('action:add'),'/stream/form');
	}
	layout_close_menu();
}
function stream_display_overview() {
	layout_show_section_header();

	stream_overview_menu();
	stream_single_menu();

	$gid =	$_REQUEST['ACTION'] == 'genre'
	?	have_idnumber($_REQUEST,'subID')
	:	(	$_REQUEST['SUBACTION'] == 'genre'
		?	have_idnumber($_REQUEST,'ssID')
		:	(	$_REQUEST['ACTION'] == 'popular'
			&&	!empty($_SERVER['eDATA'])
			&&	preg_match('"^genre/(\d+)$"',$_SERVER['eDATA'],$match)
			?	$match[1]
			:	0
			)
		);

	$styles = memcached_rowuse_array_if_not_admin(
		'stream',//admin
		'stream_genre','
		SELECT DISTINCT stream_genre.GID,NAME
		FROM stream_genre
		JOIN genre USING (GID)
		ORDER BY NAME ASC',
		TEN_MINUTES
	);
	require_once 'defines/stream.inc';
	$orderpart = null;
	switch ($_REQUEST['ACTION']) {
	case 'archive':
		$archive = true;
		$wherepart = 'ACTIVE=0';
		break;
	case 'offline':
		$offline = true;
		$wherepart = 'ACTIVE=1 AND (FAILED>0 OR OKSTAMP<'.(TODAYSTAMP - STREAM_MAX_STALE).')';
		break;
	case 'popular':
		if (false === ($period = /*!$_REQUEST['SUBACTION'] ? null :*/ require_element($_REQUEST, 'SUBACTION', ['month', 'year', 'alltime']))) {
			return;
		}
		switch ($period) {
		case 'month':
			$range = CURRENTDAYNUM - 31;
			break;
		case 'year':
			$range = CURRENTDAYNUM - 365;
			break;
		case 'alltime':
			$range = false;
			break;
		}
		$plays = memcached_simple_hash('stream_play_counter','
			SELECT STREAMID,SUM(CLICKS) AS TOTAL_CLICKS
			FROM stream_play_counter
			WHERE '.($range ? 'DAYNUM>'.$range : 1).'
			GROUP BY STREAMID
			ORDER BY TOTAL_CLICKS ASC',
			ONE_DAY
		);
#		$wherepart = '1';
		$wherepart = 'ACTIVE=1 AND FAILED<2 AND OKSTAMP>='.(TODAYSTAMP - STREAM_MAX_STALE);
		$orderpart = ' ORDER BY FIELD(STREAMID,'.implodekeys(',',$plays).') DESC';
		$popular = true;
		break;
	default:
		$wherepart = 'ACTIVE=1 AND FAILED<2 AND OKSTAMP>='.(TODAYSTAMP - STREAM_MAX_STALE);
	}
	if ($styles) {
		$basehref = '/stream/';
		if (isset($archive)) {
			$basehref .= 'archive';
		} elseif (isset($offline)) {
			$basehref .= 'offline';
		} elseif (isset($popular)) {
			$basehref .= 'popular';
		}
		ob_start();
		?><div class="block"><select onchange="this.form.onsubmit()" name="GID"><option value="0"><?= __('attrib:all'); ?></option><?
		foreach ($styles as $style) {
			?><option<?
			if ($gid == $style['GID']) {
				?> selected="selected"<?
			}
			?> value="<?= $style['GID'];
			?>"><?= escape_utf8($style['NAME']);
			?></option><?
		}
		?></select> <?
		if (isset($popular)) {
			?><select name="PERIOD" onchange="this.form.onsubmit(null)"><?
			foreach (['month', 'year', 'alltime'] as $tmpperiod) {
				?><option<?
				if ($tmpperiod === $period) {
					?> selected<?
				}
				?> value="<?= $tmpperiod ?>"><?= element_name($tmpperiod) ?></option><?
			}
			?></select> <?
		}
		?><input type="submit" value="<?= __('action:show') ?>" /></div><?
		$form = ob_get_clean();

		?><form<?
		?> accept-charset="utf-8"<?
		?> onsubmit="submitForm(this);location.href='<?= $basehref  ?>'+(this.PERIOD ? '/'+this.PERIOD.value : '')+(this.GID.selectedIndex?'/genre/'+this.GID.value:'');return false"<?
		?> method="get"<?
		?> action="<?= $basehref ?>"><?
		echo $form;
		?></form><?
	}
	$streams = memcached_rowuse_array_if_not_admin(
		'stream',//admin
		$gid ? ['stream','stream_genre'] : 'stream','
		SELECT '.($gid ? 'DISTINCT ' : null).'
			STREAMID,LISTENERS,MAXLISTENERS,SONGPLAYING,NAME,ACTIVE,FAILED,CHECKSTAMP,OKSTAMP,TYPE,KBITRATE,
			(SELECT GROUP_CONCAT(genre.NAME SEPARATOR ", ") FROM stream_genre sg JOIN genre USING (GID) WHERE sg.STREAMID=stream.STREAMID ORDER BY NAME) AS GENRES,
			(SELECT GROUP_CONCAT(genre.GID SEPARATOR ",") FROM stream_genre sg JOIN genre USING (GID) WHERE sg.STREAMID=stream.STREAMID ORDER BY NAME) AS GIDS
		FROM stream'.
		($gid ? ' JOIN stream_genre USING (STREAMID) ' : null).'
		WHERE '.$wherepart.
		($gid ? ' AND GID='.$gid : null).'
		  AND ACCEPTED=1'.
		$orderpart,
		TEN_MINUTES
	);
	if (!$streams) {
		?><div class="block"><? __('search:info:nothing_found_LINE') ?></div><?
		return;
	}
	$is_admin = have_admin('stream');
	$small = true;//SMALL_SCREEN;

	if (!isset($popular)) {
		string_sort($streams,'NAME');
	}
	$allowplay =
		!isset($archive)
	&&	!isset($offline)
	||	$is_admin;
	layout_open_box('stream ovisi');
	layout_open_table('fw default vtop'.($small ? null : ' hhla'));

	if (!$small) {
		layout_start_header_row();
		if (isset($plays)) {
			layout_header_cell();
		}
		if ($allowplay) {
			layout_header_cell(null,COLUMN_DONT_SORT);
		}
		layout_header_cell(Eelement_name('name'));
		if (isset($offline)
		||	isset($archive)
		) {
			layout_header_cell_right(Eelement_plural_name('try'),CELL_RIGHT_SPACE);
			layout_header_cell_right(__C('field:last_online'),CELL_RIGHT_SPACE);
		}
		layout_header_cell('<small>'.Eelement_name('song').'</small>');
		layout_header_cell_right(Eelement_plural_name('listener'));
		layout_header_cell_right(__C('field:kbps'));
		layout_stop_header_row();
	} else {
		$cols = 1;
		if (isset($offline)
		||	isset($archive)
		) {
			$cols += 2;
		}
		$cols += 3;
	}
	foreach ($streams as $stream) {
		extract($stream);
		$GLOBALS['__streamid'] = $STREAMID;

		if ($small) {
			?><tbody class="hhl brdgrp"><?
		}
		?><tr<?
		if (!isset($archive)
		&&	$OKSTAMP < TODAYSTAMP - STREAM_MAX_STALE
		) {
			?> class="unconnected"<?
		}
		?>><td class="rpad<?
		if ($allowplay) {
			if (!isset($plays)) {
				?> center<?
			} else {
				?> right<?
			}
		}
		?>"><?
		if ($allowplay) {
			if (isset($plays)) {
				echo getifset($plays,$STREAMID) ?: null;
				layout_next_cell(class: 'center');
			}
			show_stream_play_button($stream);
			layout_next_cell();
		}
		?><a class="<?
		if (!isset($archive)
		&&	!isset($offline)
		&&	(	!$ACTIVE
			||	$OKSTAMP < TODAYSTAMP - STREAM_MAX_STALE
			)
		) {
			?>unavailable <?
		}
		if ($small) {
			?>bold <?
		}
		?>nowrap" href="<?= get_element_href('stream', $STREAMID, $NAME);
		?>"><?=	true
		?	escape_utf8($NAME)
		:	preg_replace('"\s+(?!\()"', '&nbsp;', escape_utf8($NAME))
		?></a><?
/*		if ($small) {
			?><br /><?
			echo $song;
		}*/
		if (isset($offline)
		||	isset($archive)
		) {
			layout_next_cell(class: 'rpad right');
			echo $FAILED;
 			layout_next_cell(class: 'rpad right nowrap');
			if ($OKSTAMP) {
				_date_display($OKSTAMP, short: $small, time_span: true);
			}
/* 			layout_next_cell(class: 'rpad right nowrap');
			if ($CHECKSTAMP) {
				_date_display($CHECKSTAMP);
			}*/
		}
		if (!$small) {
			layout_next_cell();
			if ($SONGPLAYING) {
				?><small><?= get_escaped_track_name($SONGPLAYING) ?></small><?
			}
		}

		if (!$small) {
			layout_next_cell(class: 'right');
			if ($LISTENERS) {
				echo $LISTENERS;
			}
			layout_next_cell(class: 'right');
			if ($KBITRATE) {
				echo $KBITRATE;
			}
		}
		layout_stop_row();
		if ($small
		&&	(	$SONGPLAYING
			||	$GENRES
			)
		) {
			if ($GENRES) {
				?><tr><?
				if (isset($plays)) {
					?><td></td><?
				}
				if ($allowplay) {
					?><td></td><?
				}
				?><td colspan="<?= $cols ?>" class="small genres" style="padding-top:0;padding-bottom:0"><?

/*				require_once '_mastergenres.inc';
				$master_genres = get_master_for_genres($GIDS);
				if ($master_genres) {
					$genres = explode(', ',$GENRES);
					if (array_values($master_genres) != array_values($genres)) {
						?><span class="master-genre"><?= implode(', ',$master_genres) ?></span>: <?
					}
				}*/
				echo escape_utf8($GENRES);
				?></td><?
				?></tr><?
			}

			?><tr><?
			if (isset($plays)) {
				?><td></td><?
			}
			if ($allowplay) {
				?><td></td><?
			}
			?><td colspan="<?= $cols ?>" class="small"><?
			echo get_escaped_track_name($SONGPLAYING);
			?></td><?
			?></tr><?
		}
		if ($small) {
			?></tbody><?
		}
	}
	layout_close_table();
	layout_close_box();
}
function stream_display_single() {
	require_once '_connect.inc';
	require_once '_site.inc';
	require_once '_status.inc';
	require_once '_uploadimage.inc';
	require_once '_stream.inc';
	require_once 'defines/stream.inc';
	if (!require_idnumber($_REQUEST,'sID')) {
		return;
	}
	$streamid = $_REQUEST['sID'];
	if (!($stream = memcached_single_assoc_if_not_admin(
		'stream',// admin
		'stream','
		SELECT *
		FROM stream
		WHERE STREAMID = '.$streamid
	))) {
		if ($stream === false) {
			return;
		}
		not_found();
		return;
	}
	$admin = have_admin('stream');
	$online = $stream['ACTIVE'] && !$stream['FAILED'];
	layout_show_section_header();

	stream_overview_menu();
	stream_single_menu($stream);

	if (have_user()) {
		require_once '_ticketlist.inc';
		show_connected_tickets();

		require_once '_favourite.inc';
		show_marking_menu();

		if ($admin) {
			_connect_display_form('stream',$streamid);

			if (isset($GLOBALS['__checkoutput'])) {
				layout_open_box('white');
				layout_box_header(__C('stream:header:online_check_output'));
				if ($GLOBALS['__checkoutput']) {
					echo nl2br(escape_utf8($GLOBALS['__checkoutput']));
				} else {
					?><span class="error"><?= __C('stream:no_output'); ?></span><?
				}
				layout_close_box();
			}
			if ($stream['ADMINTEXT']) {
				layout_open_box('white');
				?><div class="block"><?
				?><div class="warning"><?= __C('header:admin_comments'); ?></div><?
				echo make_all_html($stream['ADMINTEXT'], UBB_UTF8);
				?></div><?
				layout_close_box();
			}
		}
	}
	require_once '_favourite.inc';
	require_once '_star.inc';
	$is_fav = check_favourite();

	?><article itemscope itemtype="https://schema.org/Article"><?

	require_once '_drag.inc';
	open_drag();

	layout_open_box('stream');
	layout_open_box_header($is_fav ? BOX_HEADER_FAN : 0,'box-header');
	if ($online) {
		show_stream_play_button($stream);
		?> <?
	}
	?><h1><?
	echo escape_utf8($stream['NAME']),get_header_star();
	?></h1><?
	if ($currentsong = $stream['SONGPLAYING']) {
		layout_continue_box_header();
		?><span class="light"><?= __('attrib:playing'); ?>:</span> <?
		echo get_escaped_track_name($currentsong);
	}
	layout_close_box_header();

	require_once '_presence.inc';
	show_presence_search($stream);

	uploadimage_show('stream',$streamid,UPIMG_SHOW_HISTORY | (SMALL_SCREEN ? UPIMG_MAX_WIDTH : UPIMG_R));

	// CHECKSTAMP, OKSTAMP

	if (!$stream['OKSTAMP']) {
		?><p><?= __('stream:never_reachable_LINE'); ?></p><?
	} elseif ($stream['CHECKSTAMP'] >= $stream['OKSTAMP'] + STREAM_MAX_STALE) {
		?><p><?= __('stream:not_reachable_for_LINE',array('DAYS'=>floor(($stream['CHECKSTAMP'] - $stream['OKSTAMP'])/86400))); ?></p><?
	}

	$list = new deflist('deflist vtop');
	// SITE, EMAIL

	require_once '_site.inc';
	show_site_and_email_rows($list,$stream);

	// GIDS / STYLES
	$styles = _genrelist_get_item_styles('stream',$streamid);
	if ($styles) {
		$list->add_row(Eelement_plural_name('genre'), $styles);
	}
	// STREAM ADDRESS

	$list->add_row(Eelement_name('status'),
		$stream['ACTIVE']
	?	__(	!$stream['FAILED']
		?	'stream:status:active_online'
		:	'stream:status:active_offline'
		)
	:	status_name('inactive'));

	$kbitrates = memcached_simpler_array('streamaddress','SELECT KBITRATE FROM streamaddress WHERE KBITRATE!=0 AND STREAMID='.$_REQUEST['sID'],600);
	if ($kbitrates || $stream['KBITRATE']) {
		// KBITRATE
		ob_start();
		if ($kbitrates) {
			sort($kbitrates);
				echo implode(', ',$kbitrates);
		} else {
			echo $stream['KBITRATE'];
		}
		$list->add_row(__C('field:kbps'),ob_get_clean());
	}
	if ($stream['MAXLISTENERS']) {
		$list->add_row(Eelement_plural_name('listener'),__('attrib:max').' '.$stream['MAXLISTENERS']);
	}
	require_once '_presence.inc';
	show_presence_row($list);

	$list->display();

	$list = new deflist('deflist vtop');

	if ($stream['FAILED']
	&&	$stream['OKSTAMP']
	) {
		$list->add_row(__C('field:last_online'), _datetime_get($stream['OKSTAMP']));
		$list->add_row(Eelement_plural_name('try'), $stream['FAILED']);
	}
	$list->add_row(__C('field:last_check'), _datetime_get($stream['CHECKSTAMP']));

	$list->display();

	// connections
	$connections = _connect_get_links('stream',$streamid);
	if ($connections) {
		?><div class="block"><b><?= Eelement_plural_name('connection'); ?></b><br /><?
		echo implode(', ',$connections);
		?></div><?
	}

	require_once '_employees.inc';
	show_employees();

	if (have_admin('relation')) {
		show_same_facebook_connected();

		require_once '_relationlist.inc';
		show_relations();
	}

	# show BIOGRAPHY
	require_once '_bio.inc';
	show_bio();

	layout_display_alteration_note($stream);

	layout_close_box();

	close_drag();

	layout_open_box('stream');
	$tracks = memcached_simple_hash('stream_track_log','
		SELECT NOWPLAYING, MAX(STAMP)
		FROM stream_track_log
		WHERE STREAMID = '.$streamid.'
		  AND STAMP > '.((CURRENTSTAMP - CURRENTSTAMP % TEN_MINUTES) - ONE_DAY).'
		GROUP BY NOWPLAYING
		ORDER BY MAX(STAMP) DESC'
	);
	layout_box_header($tracks ? Eelement_name('tracklist') : '<span class="light">'.Eelement_name('tracklist').'</span>');

	if ($tracks) {
		?><div class="block"><?= __('stream:info:tracklist_TEXT',DO_NL2BR) ?></div><?
		?><hr class="slim"><?

		layout_open_table('default vtop fw');
		$i = 0;
		foreach ($tracks as $name => $stamp) {
			?><tr<?
			if (++$i % 2 == 0) {
				?> class="panther"<?
			}
			?>><?
			?><td class="nowrap"><? _datetime_display_numeric($stamp) ?></td><?
			?><td><?
			echo get_escaped_track_name($name);
			?></td><?
			?></tr><?
		}
		layout_close_table();
	}

	layout_continue_box(1/3);
	layout_box_header(Eelement_plural_name('statistic'));
	layout_open_table(TABLE_FULL_WIDTH);
	if ($stream['LISTENERS']) {
		layout_start_reverse_row();
		echo ($listeners = $stream['LISTENERS']);
		layout_value_field();
		echo element_name('listener',$listeners),' (',__('attrib:max'),' ',$stream['MAXLISTENERS'],')';
		layout_stop_row();
	}
	$clicks = memcached_single('stream_play_counter','SELECT CAST(SUM(CLICKS) AS UNSIGNED) FROM stream_play_counter WHERE STREAMID='.$_REQUEST['sID']);
	if ($clicks) {
		$linkopen = $linkclose = null;
		layout_restart_reverse_row();
		echo $linkopen,$clicks,$linkclose;
		layout_value_field();
		echo $linkopen,element_name('click',$clicks),$linkclose;
		layout_stop_row();
	}
	require_once '_favourite.inc';
	$fancnt = get_fan_count('stream',$streamid);
	if ($fancnt) {
		[$linkopen,$linkclose] = get_action_open_close('fans');
		layout_start_reverse_row();
		echo $linkopen,$fancnt,$linkclose;
		layout_value_field();
		echo $linkopen,element_name('fan',$fancnt),$linkclose;
		layout_stop_row();
	}

	layout_close_table();
	layout_close_box();

	require_once '_videosandphotos.inc';
	show_videos_and_photos('stream',$streamid);

	switch ($_REQUEST['ACTION']) {
	case 'fans':
		if ($fancnt) {
			require_once '_fans.inc';
			show_fans('stream',$streamid,$stream);
		}
		break;
	default:
		$showcmts = true;
	}

	if (isset($showcmts)) {
		require_once '_commentlist.inc';
		$cmts = new _commentlist;
		$cmts->item($stream);
		$cmts->display();
	}
	?></article><?
}
function stream_display_form() {
	if (!require_admin('stream')) {
		return;
	}
	require_once '_ticket.inc';
	require_once '_presence.inc';
	layout_show_section_header();

	if ($streamid = $_REQUEST['sID']) {
		$stream = db_single_assoc('stream','SELECT * FROM stream WHERE STREAMID='.$streamid);
		if ($stream === false) {
			return;
		}
		if (!$stream) {
			not_found(); return;
		}
	}
	?><form<?
	?> accept-charset="utf-8"<?
	?> onsubmit="return submitForm(this)"<?
	?> method="post"<?
	?> action="/stream<?
	if ($streamid) {
		?>/<? echo $streamid;
	}
	?>/commit"><?

	ticket_passthrough();

	require_once '_uploadimage.inc';
	show_uploadimage_for_element_form('stream',$streamid);

	if ($streamid) {
		require_once '_presence.inc';
		show_presence_search($stream);
	}

	layout_open_box('stream');
	layout_open_table('fw');
	layout_start_row();
		echo Eelement_name('name');
		layout_field_value();
		?><input required="required" type="text" name="NAME" id="name" value="<? if ($streamid) echo escape_utf8($stream['NAME']) ?>" autofocus="autofocus" /><?

	layout_restart_row();
		echo __C('field:max_listeners',DO_NBSP);
		layout_field_value();
		?><input type="number" class="id" name="MAXLISTENERS" value="<? if ($streamid) echo $stream['MAXLISTENERS']; ?>" /><?

	layout_restart_row();
		echo __C('field:kbps');
		layout_field_value();
		?><input type="number" class="three_digits" name="KBITRATE" value="<? if ($streamid) echo $stream['KBITRATE']; ?>" /><?

	layout_restart_row();
		echo Eelement_name('site');
		layout_field_value();
		?><input type="url" data-valid="url" name="SITE" value="<? if ($streamid) echo escape_ascii($stream['SITE']); ?>" /><?

	layout_restart_row();
		echo Eelement_name('stream_address');
		layout_field_value();
		?><input type="text" name="ADDRESS" value="<? if ($streamid) echo escape_ascii($stream['ADDRESS']); ?>" /><?
	if (!show_presence_form_row()) {
		return;
	}
	// STYLES
	layout_restart_row();
		echo Eelement_plural_name('genre');
		layout_field_value();
		show_genre_selection(
			$streamid ? db_boolean_hash('stream_genre', 'SELECT GID FROM stream_genre WHERE STREAMID = '.$streamid) : null
		);
/*	// ARCHIVE
	layout_restart_row();
		?><label for="archive"><?= Eelement_name('archive'); ?></label><?
		layout_field_value();
		?><input type="checkbox" name="ARCHIVE" id="archive" value="1"<?
		if (!empty($stream['ARCHIVE'])) {
			?> checked="checked"<?
		}
		?>><?*/
	// ADMIN_TEXT
	layout_restart_row();
		echo __C('header:admin_comments');
		layout_field_value();
		show_textarea([
			'name'		=> 'ADMINTEXT',
			'cols'		=> 50,
			'rows'		=> 5,
			'class'		=> 'growToFit',
			'value_utf8'	=> $stream,
		]);
	layout_stop_row();
	layout_close_table();
	layout_close_box();

	?><div class="block"><?
	?><input type="submit" value="<?= __($streamid ? 'action:change' : 'action:add'); ?>" /><?
	?></div><?

	?></form><?
}
function stream_commit(): void {
	require_once '_execute.inc';
	require_once '_site.inc';
	require_once '_ticket.inc';
	require_once '_presence.inc';
	require_once '_ubb_preprocess.inc';

	if (!require_admin('stream')
	||	!require_something_trim($_POST, 'NAME', null, null, utf8: true)
	||	!require_anything_trim($_POST, 'ADMINTEXT', utf8: true)
	||	!optional_number($_REQUEST, 'STREAMID')
	||	false === require_number($_POST, 'MAXLISTENERS')
	||	false === require_number($_POST, 'KBITRATE')
	||  !require_anything_trim($_POST, 'ADDRESS', utf8: true)
	||  !require_anything_trim($_POST, 'SITE', utf8: true)
	) {
		return;
	}
	move_site_to_presence();
	$setlist[] = 'MAXLISTENERS='.	$_POST['MAXLISTENERS'];
	$setlist[] = 'KBITRATE='.	$_POST['KBITRATE'];
	$setlist[] = 'ADMINTEXT="'.	addslashes(_ubb_preprocess($_POST['ADMINTEXT'], utf8: true)).'"';
	$setlist[] = 'NAME="'.		addslashes($_POST['NAME']).'"';
	$setlist[] = 'ADDRESS="'.	($_POST['ADDRESS'] ? addslashes(make_proper_site($_POST['ADDRESS'], utf8: true)) : null).'"';
	$setlist[] = 'SITE="'.		($_POST['SITE'] ? addslashes(make_proper_site($_POST['SITE'], utf8: true)) : null).'"';

	if ($streamid = $_REQUEST['sID']) {
		$address = db_single('stream','SELECT ADDRESS FROM stream WHERE STREAMID='.$streamid);
		if ($address === false) {
			return;
		}
		if ($address === null) {
			not_found(); return;
		}
		reset_site();
		if (!db_insert('stream_log','
			INSERT INTO stream_log
			SELECT * FROM stream
			WHERE STREAMID = '.$streamid)
		||	!db_update('stream','
			UPDATE stream SET
				MUSERID	= '.CURRENTUSERID.',
				MSTAMP	= '.CURRENTSTAMP.',
				'.implode(', ', $setlist).'
			WHERE STREAMID = '.$streamid)
		) {
			return;
		}
		if (strtolower($address) !== strtolower($_POST['ADDRESS'])) {
			[$rc, $stdout, $stderr] = execute('/home/'.get_basedir().'/maintenance/check_streams.php '.$streamid.' 2>&1');
			$GLOBALS['__checkoutput'] = $stdout;
		}
		register_notice('stream:notice:changed_LINE');
	} else {
		if (!db_insert('stream','
			INSERT INTO stream SET
				SONGPLAYING	= "",
				MUSERID		= '.CURRENTUSERID.',
				MSTAMP		= '.CURRENTSTAMP.',
				'.implode(', ', $setlist))
		) {
			return;
		}
		$streamid = $_REQUEST['sID'] = db_insert_id();

		[$rc, $stdout, $stderr] = execute('/home/'.get_basedir().'/maintenance/check_streams.php '.$streamid.' 2>&1');
		$GLOBALS['__checkoutput'] = $stdout;

		register_notice('stream:notice:added_LINE');

		ticket_update('stream', $streamid);
		init_site();
	}
	require_once '_uploadimage.inc';
	uploadimage_actual_receive('stream', $streamid);
	store_presence();
	_genrelist_commit('stream', $streamid);
}
