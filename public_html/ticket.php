<?php

declare(strict_types=1);

require_once '_date.inc';

define('CURRENTSTAMP',	time());
define('CURRENTDAYNUM',	to_days());

require_once '_exit_if_readonly.inc';
require_once '_spider.inc';
require_once '_db.inc';
require_once '_memcache.inc';
require_once '_require.inc';
require_once '_nocache.inc';
require_once '_combinable.inc';
require_once '_http_status.inc';

function bail(int $errno): never {
	http_response_code($errno);
	exit;
}

send_no_cache_headers();

header('X-Robots-Tag: nofollow');

if (!($partyid = have_idnumber($_SERVER, 'ePARTYID'))) {
	bail(400);
}

if ($partyid !== ($combined_partyid = get_combined_id('party', $partyid))) {
	if (null === ($new_uri = preg_replace("!/$partyid\b!", "/$combined_partyid", $_SERVER['REQUEST_URI']))) {
		bail(503);
	}
	moved_permanently($new_uri);
}

if ('generic' === ($type = $_SERVER['eTYPE'])) {
	require_once '_presale.inc';
	[$url] = get_presale_link($partyid, parameters: isset($_REQUEST['appic']) ? ['appic'] : []);
	if (!$url
	||	!preg_match('!/order_ticket/(?<type>[^/]+)/(\d+)!u', $url, $match)
	){
		bail(404);
	}
	header('Location: '.$url);
	exit;
}

require_once '__translation.php';
require_once '_presale.inc';
require_once '_shoplink.inc';
require_once '_ticketseller.inc';

if (false === ($presaleinfo = get_presale_info('party', $partyid))) {
	bail(500);
}
if (!($url = get_shop_url($type, $partyid, $presaleinfo))) {
	bail(404);
}
header('Location: '.$url);

if (ROBOT) {
	bail(200);
}

require_once '_currentuser.inc';
require_once '_identity.inc';
$currentuser = new _currentuser(TINY_USER | SKIP_SETTINGS);
# Remove the following assertion once the _identity.inc file does not initialize CURRENTIDENTID automatically and thus can be included higher up
assert(is_array($presaleinfo)); # Satisfy EA inspection

fastcgi_finish_request();

$tickethit_type = $type;

if ($type !== 'site') {
	if (!($seller_id = memcached_single('ticketseller', "
		SELECT SELLERID
		FROM ticketseller
		WHERE SELLER = '$type'"))
	) {
		if ($seller_id === false) {
			bail(500);
		}
		mail_log("could not obtain SELLERID for tickethit type '$type'");
	}
} else {
		!($seller_id = $presaleinfo['SELLERID'])
	&&	($stamp = db_single_int('party', "SELECT STAMP FROM party WHERE PARTYID = $partyid"))
	&&	($seller = get_seller_info_for_url($url, include_inactive: $stamp <  CURRENTSTAMP))
	&&	!in_array($seller['SELLER'], ['appic', 'ticketswap'], true)
	&&	($seller_id = $seller['SELLERID'])
	&&	db_insert('presaleinfo_log', "
		INSERT INTO presaleinfo_log
		SELECT * FROM presaleinfo
		WHERE SELLERID != $seller_id
		  AND PARTYID = $partyid")
	&&	db_update('presaleinfo', "
		UPDATE presaleinfo SET
			MSTAMP		= UNIX_TIMESTAMP(),
			SELLERID	= $seller_id
		WHERE SELLERID != $seller_id
		  AND PARTYID = $partyid");
}

if ($type === 'eventtickets') {
	# tickethit table has event-tickets instead of eventtickets in TYPE enum
	$type = 'event-tickets';
}

$seller_id ??= 'NULL';

db_insert('tickethit', "
INSERT INTO tickethit SET
	ELEMENT	= 'party',
	APPIC	= ".(isset($_REQUEST['appic']) ? "b'1'" : "b'0'").",
	TYPE	= '$type',
	DAYNUM	= ".CURRENTDAYNUM.',
	STAMP	= '.CURRENTSTAMP.',
	USERID	= '.CURRENTUSERID.',
	IDENTID	= '.CURRENTIDENTID.",
	IPBIN	= '".addslashes(CURRENTIPBIN)."',
	PARTYID	= $partyid,
	SELLERID= $seller_id"
);
