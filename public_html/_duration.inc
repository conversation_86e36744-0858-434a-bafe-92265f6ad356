<?php

const DURATION_HIDE_START	= 1;
const DURATION_HIDE_STOP	= 2;

function show_duration_rows(deflist &$list, array $item, int $flags = 0): void {
	$shows = [];
	if (!($flags & DURATION_HIDE_START)) {
		$shows['STARTSTAMP'] = [0, 'start', 'start'];
	}
	if (!($flags & DURATION_HIDE_STOP)) {
		$shows['STOPSTAMP'] = [-1, 'stop', 'last_day'];
	}
	if ($shows) {
		foreach ($shows as $key => [$offset, $startname, $day_name]) {
			[,,, $hour, $min] = _getdate($item[$key]);
			if ($hour || $min) {
				$field = Eelement_name($startname);
				ob_start();
				 _datedaytime_display($item[$key]);
			} else {
				$field = Eelement_name($day_name);
				ob_start();
				_dateday_display($item[$key] + $offset);
			}
			if ($startname == 'stop') {
				if (!$item['KEEPGOING']) {
					?> (<?= element_name('hard_stop') ?>)<?
				}
			}
			$list->add_row($field, ob_get_clean());
		}
	}
	// DAYS
	$secs = $item['STOPSTAMP'] - $item['STARTSTAMP'];
	$hours = (int)($secs / ONE_HOUR);
	$days  = (int)($secs / ONE_DAY);
	$hours -= $days * 24;
	ob_start();
	if ($days) {
		echo __('duration:x_days', ['DAYS' => $days]);
	}
	if ($hours) {
		if ($days) {
			?> &amp; <?
		}
		echo __('duration:x_hours', ['HOURS' => $hours]);
	}
	$list->add_row(Eelement_name('duration'), ob_get_clean());
}

function show_last_day_duration_form_part(?array $item = null, ?array $args = null): void {
	layout_start_row();
	include_js('js/form/duration');
	echo Eelement_name('publication');

	layout_field_value();
	$startstamp = $item['STARTSTAMP'] ?? CURRENTSTAMP;
	if (isset($args['time'])) {
		_datetime_display_select_stamp(
			stamp:		$startstamp,
			prefix:		'START',
			no_time:	false,
			onchange:	/** @lang JavaScript */ 'Pf_updateDuration(this, 0);'
		);
	} else {
		_date_display_select_stamp(
			stamp:		$startstamp,
			prefix:		'START',
			required:	false,
			onchange:	/** @lang JavaScript */ 'Pf_updateDuration(this, 0);'
		);
	}
	layout_restart_row();
	echo Eelement_name('last_day');
	layout_field_value();

	# FIXME: /ad/form ... stop is one day age after 12:00 ?
	$stopstamp =
		$args['stopstamp']
	??	(	$item
		?	(	isset($args['time'])
			?	$item['STOPSTAMP']
			:	$item['STOPSTAMP'] - 1
			)
		:	CURRENTSTAMP
		);
	[,,, $hours, $minutes] = _getdate($stopstamp);

	if (isset($args['time'])) {
		if (!$hours
		&&	!$minutes
		) {
			$stopstamp = strtotime('-1 day', $stopstamp);
		}
		_datetime_display_select_stamp(
			stamp:		$stopstamp,
			prefix:		'STOP',
			no_time:	!$hours && !$minutes,
			onchange:	/** @lang JavaScript */ 'Pf_updateDuration(this, 1);'
		);
	} else {
		if (!$hours && !$minutes) {
			--$stopstamp;
		}
		_date_display_select_stamp(
			stamp:		$stopstamp,
			prefix:		'STOP',
			onchange:	/** @lang JavaScript */ 'Pf_updateDuration(this, 1);'
		);
	}

	if (isset($args['hardstop'])) {
		?> <label id="hardstop" class="<?
		if (!$args['hardstop']) {
			?>not-<?
		}
		?>hilited"><?
		show_input([
			'type'		=> 'checkbox',
			'name'		=> 'HARDSTOP',
			'class'		=> 'upLite',
			'value'		=> '1',
			'checked'	=> $args['hardstop']
		])
		?> <?= element_name('hard_stop') ?></label><?
	}

	layout_restart_row();
	echo Eelement_name('duration');
	layout_field_value();
	$start = strtotime('0:0', $item['STARTSTAMP'] ?? null);
	$stop  = strtotime('0:0', $item['STOPSTAMP']  ?? null);
	$days  = !$item ? 1 : (round(($stop - $start) / ONE_DAY) ?: 1);

	show_input([
		'type'		=> 'number',
		'class'		=> 'three_digits right',
		'required'	=> true,
		'min'		=> 1,
		'max'		=> $args ? (have_idnumber($args, 'maxdays') ?: null) : null,
		'name'		=> 'DURATION',
		'value'		=> $days,
		'onchange'	=> /** @lang JavaScript */ 'Pf_changeDuration(event, this)',
		'onkeyup'	=> /** @lang JavaScript */ 'Pf_changeDuration(event, this)',
	]);
	?> <?
	echo element_plural_name('day');
	?> <?
	?><select name="DURREL"><?
		?><option value="0"><?= __('duration:from_publication') ?></option><?
		?><option value="1"><?= __('duration:to_last_day') ?></option><?

	?></select><?
	layout_stop_row();
}

function process_duration_form_parts(int $max = 0): array|false {
	if (!require_date($_POST, 'START')
	||	!require_date($_POST, 'STOP')
	||	false === require_number($_POST, 'DURATION')
	) {
		return false;
	}
	if ($max
	&&	$max < $_POST['DURATION']
	) {
		register_error('duration:error:maximum_LINE', ['DAYS' => $max]);
		return false;
	}

	$startstamp = _date_getstamp($_POST, 'START');
	$stopstamp  = _date_getstamp($_POST, 'STOP');

	$last_day =
		!isset($_POST['STOPHOUR'])
	||	($_POST['STOPHOUR'] === 24);

	if ($last_day) {
		$stopstamp = strtotime('+1 day', $stopstamp);
		unset($_POST['STOPHOUR'],
			  $_POST['STOPMINS']);
	}
	if ($stopstamp <= $startstamp) {
		register_error('duration:error:stop_is_same_or_before_start_LINE');
		return false;
	}
	return [$startstamp, $stopstamp];
}
