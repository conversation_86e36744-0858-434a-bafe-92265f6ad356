<?php

declare(strict_types=1);

# do filemtime an touch to fix change date back
# support 'hardlink' version
# support 'reflink' version

function recursive_copy(string $src, string $dst): bool {
	if (false === ($dir = @opendir($src))) {
		syslog_last_error(LOG_CRIT, "failed to opendir $src");
		return false;
	}
	if (false === mkdir($dst)) {
		syslog_last_error(LOG_CRIT, "failed to mkdir $dst");
		return false;
	}
	while(false !== ($file = readdir($dir)) ) {
		if ($file === '.'
		||	$file === '..'
		) {
			continue;
		}
		if (is_dir($src_file = "$src/$file")) {
			if (!recursive_copy($src_file, "$dst/$file")) {
				return false;
			}
		} elseif (copy($src_file, $dst_file = "$dst/$file")) {
			syslog_last_error(LOG_CRIT, "failed to copy $src_file ⇨ $dst_file");
			return false;
		}
	}
	closedir($dir);
	return true;
}
