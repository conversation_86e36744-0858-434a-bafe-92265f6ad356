<?php

function missing_register_pct($type, $pct = null, $checkpct = null): ?string {
	# 0 -> have signed
	# 1 -> have x chars
	static $__stored = [[false, 1], [false, 1]];
	if ($pct === null
	&&	$checkpct === null
	) {
		return null;
	}
	$__have = &$__stored[$type];
	if ($pct === null) {
		$hid = '';
		if ($__have[0]
		&&	$checkpct >= 0
		) {
			$hid .= '-';
		}
		$checkpct = abs($checkpct);
		switch ($__have[1]) {
		case 3:
			if ($checkpct < 100) {
				$hid .= '0';
			}
		case 2:
			if ($checkpct < 10) {
				$hid .= '0';
			}
			break;
		}
		return $hid;
	}
	if ($pct < 0) {
		$__have[0] = true;
	}
	$pct = abs($pct);
		  if ($pct == 100) { $__have[1] = max($__have[1],3);
	} elseif ($pct >= 10) {  $__have[1] = max($__have[1],2);
	}
	return null;
}

function calculate_missing(array &$infos): void {
	foreach ($infos as &$info) {
		[$missing, $pct, $cpct] = get_missing_info($info);
		missing_register_pct(0, $pct);
		missing_register_pct(1, $cpct);
	}
}

function get_missing_info(array &$item, bool $try = false): array {
	if (isset($item['__M'])) {
		return $item['__M'];
	}
	if (empty($item['OLD'])
	&&	$item['D_IMPRESSIONS'] !== null
	) {
		static $__minute = null;
		static $__secs;

		if ($__minute === null) {
			if (isset($GLOBALS['__mins'])) {
				$__minute = $GLOBALS['__mins'];
				$__secs = $GLOBALS['__secs'];
			} else {
				require_once '_date.inc';
				[,,,, $__minute, $__secs] = _getdate();
			}
		}
		$m = $__minute;
		$s = $__secs;
	} else {
		$m = $s = 59;
		if ($item['D_IMPRESSIONS'] === null) {
			if ($id = getifset($item,'NEWSADID')) {
				$element = 'newsad';
			} else {
				$element = 'ad';
				$id = $item['ADID'];
			}
			require_once '_distribution.inc';
			$dinfo = best_known_dinfo($element, $id, $try);
			$item['D_IMPRESSIONS'] = $dinfo['D_IMPRESSIONS'] ?? 0;
			$item['THISHOUR'] = $dinfo['THISHOUR'] ?? 0;
		}
	}
	$done = $item[$try ? 'IMPRESSIONSDONETRY' : 'IMPRESSIONSDONE'];
	if ($done >= $item['IMPRESSIONS']) {
		$missing_info = [
			'missing'		=> null,
			'pct'			=> 0,
			'cpct'			=> 0,
			'max_behind'		=> false,
			'max_missing'		=> 0,
			'missing_after_filter'	=> null,
			'pct_after_filter'	=> 0,
			'cpct_after_filter'	=> 0,
		];
		return $item['__M'] = array_merge($missing_info, array_values($missing_info));
	}

	$need = $item['D_IMPRESSIONS'] + $item['THISHOUR'] * ($m * ONE_MINUTE + $s) / ONE_HOUR;
	$missing = $need - $done;
	$pct = $need ? 100 * (1 - $done / $need) : 0;

	# cpct is optional time corrected, usefull for determining which ads need to run now

	if (isset($item['STOPSTAMP'])) {
		$left = $item['STOPSTAMP'] - CURRENTSTAMP;
		$cpct = $pct * ONE_DAY / ($left ? ($left > 1 ? $left : -1) : 1);

		# correct for profilefilter

		$max_behind
		=	!empty($item['FILTERS'])
		&&	$item['FILTERS_ENFORCE'] === 'soft'
		?	profile_filter_allow_behind($item)
		:	false;

		$max_missing = 0;
		$missing_after_filter = $missing;
		$pct_after_filter = $pct;
		$cpct_after_filter = $cpct;

		if ($max_behind) {
			$max_missing = $max_behind / $item['SECONDS_PER_IMPRESSION'];

/*			print_rr([
				'adid'		=> $item['ADID'],
				'type'		=> ad_position_name($item['POSITION']),
				'max_missing'	=> $max_missing,
				'missing'	=> $missing,
			]);*/

			$missing_after_filter = $missing - $max_missing;

			if ($missing_after_filter > 0) {
				$need -= $max_missing;
				$pct_after_filter = 100 * (1 - $done / $need);
				$cpct_after_filter = $pct_after_filter * ONE_DAY / ($left ?: 1);
			} else {
				$missing_after_filter = 0;
				$pct_after_filter = 0;
				$cpct_after_filter = 0;
			}
		}
	}

	$missing_info = [
		/* 0 */ 'missing'		=> $missing,			# missing
		/* 1 */ 'pct'			=> $pct,			# percentage behind
		/* 2 */ 'cpct'			=> $cpct ?? 0,			# time corrected percentage behind

		# there are max_behind and max_missing after filter
		# max_behind might be false if the filter
		/* 3 */ 'max_behind'		=> $max_behind ?? 0,		# max behind, warn if higher
		/* 4 */ 'max_missing'		=> $max_missing ?? 0,		# max missing, warn if higher
		/* 5 */ 'missing_after_filter'	=> $missing_after_filter ?? 0,	# missing after filter
		/* 6 */ 'pct_after_filter'	=> $pct_after_filter ?? 0,	# percentage behind after filter
		/* 7 */ 'cpct_after_filter'	=> $cpct_after_filter ?? 0,	# time corrected percentage behind after filter

/*		'need'		=> $need,
		'done'		=> $done,
		'd_impressions'	=> $item['D_IMPRESSIONS'],
		'thishour'	=> $item['THISHOUR'],
		'minutes'	=> $m,
		'seconds'	=> $s,*/
	];
	return $item['__M'] = array_merge($missing_info, array_values($missing_info));
}

function get_missing_color(float $pct, ?bool &$light = null): string {
	if ($pct >= 10) return 'text-ultra-red';
	if ($pct >=  5) return 'text-super-red';
	if ($pct >=  4) return 'text-red';
	if ($pct >=  3) return 'text-orange-red';
	if ($pct >=  2) return 'text-orange';
	if ($pct >=  1) return 'text-orange-green';
	if ($pct >= .5) return 'text-green';
	$light = true;
	return 'text-green';
}

function open_row_with_missing(array $item, ?array $rowclasses = null): int {
	if (!empty($item['KEEPTOP'])) {
		layout_start_rrow(0, $rowclasses);
		layout_next_cell();
		layout_next_cell();
		return 0;
	}
	include_style('votes');

	[	$initial_missing,
		$pct,
		$cpct,
		$max_behind,
		$max_missing,
		$missing_after_filter,
		$pct_after_filter,
		$cpct_after_filter
	] = get_missing_info($item);

	# show corrected values after filter always, since it is used as indication if ads run worse than expected.
	if ($missing_after_filter) {
		$missing = $missing_after_filter;
		$pct = $pct_after_filter;
		$cpct = $cpct_after_filter;
	} else {
		$missing = $initial_missing;
	}

	$initial_missing ??= 0;

	$ccolor = get_missing_color($cpct, $clight);
	$color = get_missing_color($pct, $light);

	layout_start_rrow(CELL_RIGHT_SPACE | CELL_ALIGN_RIGHT | NOWRAP, $rowclasses, $ccolor);
		if ($missing !== null) {
			echo round($missing);
		}

	if (SMALL_SCREEN) {
		# show only explicit missing on mobile
		return $initial_missing;
	}

	layout_next_cell(class: 'rpad nowrap '.($light ? 'light ' : '').$color);
		if ($item['STOPSTAMP'] > CURRENTSTAMP) {
			$pct = round($pct, 1);
			if ($pct) {
				if ('' != ($hid = missing_register_pct(0,null,$pct))) {
					?><span style="visibility:hidden"><?= $hid ?></span><?
				}
				printf('%0.1f', $pct);
				?>%<?
			}
		}
	layout_next_cell(class: 'rpad nowrap '.($clight ? 'light ' : '').$ccolor);
		if ($item['STOPSTAMP'] > CURRENTSTAMP) {
			$cpct = round($cpct, 1);
			if ($cpct) {
				if ('' != ($hid = missing_register_pct(1,null,$cpct))) {
					?><span style="visibility:hidden"><?= $hid ?></span><?
				}
				printf('%0.1f', $cpct);
				?>%<?
			}
		}
	return $initial_missing;
}
