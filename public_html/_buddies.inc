<?php

require_once 'defines/buddies.inc';

function flush_buddies(int $userid): void {
	memcached_delete(
		'budreqcnt:'.$userid,
		'buddiesfhash:'.$userid,
		'buddiesflist:'.$userid,
		'buddieshhash:'.$userid
	);
}
function _buddies_half_hash($userid) {
	static $__buddies_hhash;
	if (!isset($__buddies_hhash[$userid])) {
		if ($buddies = memcached_boolean_hash('buddy_request', "
			SELECT USERID_INI FROM buddy_request
			WHERE USERID_ACC = $userid
			UNION
			SELECT USERID_ACC FROM buddy_request
			WHERE USERID_INI = $userid",
			HALF_HOUR,
			'buddieshhash:'.$userid)
		) {
			ksort($buddies);
		}
		return $__buddies_hhash[$userid] = $buddies;
	}
	return $__buddies_hhash[$userid];
}
function _buddies_full_hash(int $userid): array|false {
	static $__buddies_full_hash;
	if (!isset($__buddies_full_hash[$userid])) {
		if ($buddies = memcached_simple_hash('buddy', "
			SELECT USERID_INI, USERID_INI
			FROM buddy
			WHERE USERID_ACC = $userid
			UNION
			SELECT USERID_ACC, USERID_ACC
			FROM buddy
			WHERE USERID_INI = $userid",
			HALF_HOUR,
			'buddiesfhash:'.$userid)
		) {
			ksort($buddies);
		}
		return $__buddies_full_hash[$userid] = $buddies;
	}
	return $__buddies_full_hash[$userid];
}
function _buddies_full_cached_list(int $userid) {
	return _buddies_full_hash($userid);
}
function have_new_buddy_requests(int $userid): int|false {
	require_once '_ignores.inc';
	return memcached_single(['buddy_request_seen', 'buddy_request', 'ignorelist'], "
		SELECT COUNT(*)
		FROM buddy_request
		JOIN user_account ON USERID_INI = USERID
		LEFT JOIN ignorelist ON IGNOREID = USERID_INI AND ignorelist.USERID = $userid AND (FLAGS & ".IGNORE_BUDDY_REQUEST.")
		WHERE COALESCE((SELECT STAMP FROM buddy_request_seen WHERE USERID = $userid), 0) < CSTAMP
		  AND ignorelist.USERID IS NULL
		  AND USERID_ACC = $userid",
		TEN_MINUTES,
		'budreqcnt:'.$userid
	);
}
function buddy_request_counts(int $userid): array|false|null {
	require_once '_ignores.inc';
	# returns incoming, outgoing buddy requests
	$info = db_single_array(['buddy_request', 'ignorelist'], "
		SELECT	(	SELECT COUNT(*)
					FROM buddy_request
					LEFT JOIN ignorelist ON IGNOREID = USERID_INI AND ignorelist.USERID = $userid AND (FLAGS & ".IGNORE_BUDDY_REQUEST.")
					WHERE ignorelist.USERID IS NULL
					  AND USERID_ACC = $userid),
				(	SELECT COUNT(*) FROM buddy_request
					WHERE USERID_INI = $userid
				)"
	);
	return $info ?: [0, 0];
}
function is_buddy(int $userid): bool {
	if (!have_user()) {
		return false;
	}
	require_once '_buddies.inc';
	$buddies = _buddies_full_hash(CURRENTUSERID);
	return	$buddies
		&&	isset($buddies[$userid]);
}
