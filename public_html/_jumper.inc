<?php

function get_site(string $element, int $id,&$args = null): string|null|false {
	switch ($element) {
	default:
		return db_single($element,'SELECT SITE FROM '.$element.' WHERE '.$element.'ID='.$id);
	case 'artist':
		return db_single('artist','SELECT SITE FROM artist WHERE ARTISTID='.$id);
	case 'bookartist':
		return db_single('artist','SELECT BOOKSITE FROM artist WHERE ARTISTID='.$id);
	case 'prereg':
		return db_single('party','SELECT PREREG_URL FROM party WHERE PARTYID='.$id);
	case 'newsad':
		$newsid = db_single($element,'SELECT NEWSID FROM newsad WHERE NEWSADID='.$id);
		return $newsid ? get_element_href('news',$newsid) : null;
	case 'job':
		return db_single($element,'SELECT '.($args ?: 'JOB').'URL FROM job WHERE JOBID='.$id);
	case 'livestream':
		return db_single('party','SELECT LIVESTREAM_ADDRESS FROM party WHERE PARTYID='.$id);
	case 'presence':
		$info = db_single_array($element,'SELECT SITE,TYPE FROM '.$element.' WHERE '.$element.'ID='.$id);
		if (!$info) {
			return null;
		}
		list($site,$type) = $info;
		switch ($type) {
		case 'juno':
		case 'junodownload':
			$site .= !str_contains($site, '?' ? '?' : '&').'ref=partyflock';
			break;
		case 'itunes':
			require_once '_affiliates.inc';
			$site = make_affiliate_url($site,$element,$id);
			break;
		}
		return $site;
	}
}

function jump_to(string $element, int $id, ?string $args = null): string|null|false {
	require_once '_spider.inc';
	return ROBOT ? get_site($element, $id) : '/jumpto/'.$element.'/'.$id.($args ? '?'.$args : null);
}
