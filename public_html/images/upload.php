<?php

declare(strict_types=1);

# FIXME: PSTAMP of news, interview, review, column are not honored currently!

define('CURRENTSTAMP',	time());
define('TODAYSTAMP',	mktime(0,0,0));

require_once __DIR__.'/../_exit_if_offline.inc';
require_once __DIR__.'/../_globalsettings.inc';
deny_robots_if_partyflock_under_load();

require_once __DIR__.'/../_image_bail.inc';
require_once __DIR__.'/../_db.inc';
require_once __DIR__.'/../_flockmod.inc';
require_once __DIR__.'/../_currentuser.inc';
require_once __DIR__.'/../_helper.inc';
require_once __DIR__.'/../_modified.inc';
require_once __DIR__.'/../_execute.inc';
require_once __DIR__.'/../_delayedflyers.inc';
require_once __DIR__.'/../_hosts.inc';
require_once __DIR__.'/../_tooslow.inc';
require_once __DIR__.'/../_smartcrop.inc';
require_once __DIR__.'/../_http_status.inc';
require_once __DIR__.'/../_uploadimage.inc';
require_once __DIR__.'/../_urltitle.inc';
require_once __DIR__.'/../defines/cache_path.inc';

global $dbgid, $debug_upload_images, $super_search;

$super_search ??= (HOME_THOMAS || SERVER_SANDBOX) && isset($_REQUEST['SUPERSEARCH']);
/** @noinspection NonSecureUniqidUsageInspection */
$dbgid ??= uniqid();
$debug_upload_images ??= HOME_THOMAS || SERVER_SANDBOX;

const REDO_BEFORE			= 1540751700;	# 2018-10-28 19:35, pre this stamp, non need_work images went through 'convert'
const UPLOAD_OUTPUT_VERSION = 1;
const USE_FLOCK				= true;

# leniency for background processing
set_memory_limit(GIGABYTE);
set_time_limit(TEN_MINUTES);

if (!empty($_SERVER['eLANG'])) {
	moved_permanently(preg_replace("^/{$_SERVER['eLANG']}\b!", '', $_SERVER['REQUEST_URI']));
}

/** @noinspection NotOptimalIfConditionsInspection */
if ( SERVER_VIP
&&	!$debug_upload_images
&&	(require_once __DIR__.'/../_spider.inc')
&& 	ROBOT
) {
	moved_permanently($_SERVER['REQUEST_URI'], forced_host: 'partyflock.nl');
}

$currentuser = new _currentuser(TINY_USER | SKIP_SETTINGS);

if ($standard_invocation = isset($_SERVER['eTYPE'])) {
	$type			 = $_SERVER['eTYPE'];
	$filetype		 = $_SERVER['eFILETYPE'];
	$format			 = $_SERVER['eFORMAT'];
	$id				 = (int)$_SERVER['eID'];
	$upimgid		 = (int)$_SERVER['eUPIMGID'];
	$original_format = $format;

	# fetch all current images for element $type:$id
	if (!($currentids = db_simpler_array('uploadimage_link', "
		SELECT UPIMGID
		FROM uploadimage_link
		WHERE TYPE = '$type'
		  AND ID = $id
		ORDER BY `DEFAULT` DESC"))
	||	!in_array($upimgid, $currentids, true)
	) {
		# element $type:$id is not a current image for any element
		# let's check whether it's active

		$image_is_active = db_single_bool('uploadimage_link_log', "
			SELECT ACTIVE
			FROM uploadimage_link_log
			WHERE UPIMGID = $upimgid
			  AND TYPE = '$type'
			  AND ID = $id
			LIMIT 1"
		);
		if (db_errno()) {
			image_bail(503);
		}
		if ($image_is_active === null) {
			# no log entry found (should not happen)
			if ($currentids) {
				moved_permanently(preg_replace("!(?<=_)$upimgid(?=[/.]|$)!", $currentids[0], $_SERVER['REQUEST_URI']));
			}
			image_bail(404);
		}
		if (!$image_is_active
		&&	!have_admin($type)
		) {
			# hide only for non-admins
			image_bail(410);
		}
	}
	if (!($element = is_uploadimage_type($type))) {
		image_bail(404);
	}
	if (false === ($need = get_urltitle($element, $id))
	||	null  ===  $need
	) {
		error_log('HERE');
		image_bail($need === false ? 503 : 404);
	}
	if (!$need
	&&	preg_match('"(.*)/(.[a-z]+)$"iu', $_SERVER['REQUEST_URI'], $match)
	) {
		$new_uri = $match[1].$match[2];
	}
	error_log('NOTICE need: '.$need);
	if ($need !== ($supplied = fix_urlencode($_SERVER['eTITLE'] ?? ''))) {
		$new_uri = preg_replace("!(?<=_$upimgid)(/.*)?(?=\.[a-z]+)$!", "/$need", $_SERVER['REQUEST_URI']);
		error_log("DEBUG wanted $need, got $supplied, new uri: $new_uri");
	}
	$super_search && ($prefix = "$type:$id,{$_SERVER['REQUEST_URI']}");
} elseif (isset($_SERVER['eUPIMGID'])) {
	# NOTE: This direct call is used a lot in forum message
	if (!empty($_SERVER['HTTP_REFERER'])
	&&	!str_contains($_SERVER['HTTP_REFERER'], '://partyflock.nl')
	) {
		$debug_upload_images && error_log("$dbgid uploadimage without context requested by {$_SERVER['REMOTE_ADDR']}: {$_SERVER['REQUEST_URI']} called from {$_SERVER['HTTP_REFERER']}");
	}
	$upimgid  = (int)$_SERVER['eUPIMGID'];
	$format	  = $_SERVER['eFORMAT'];
	$filetype = $_SERVER['eFILETYPE'];
	$original_format = $format;
} else {
	mail_log('uploadimage called with non standard parameters', get_defined_vars(), error_log: 'WARNING');
	image_bail(404);
}
if (false === ($is_filetype = memcached_single_string('uploadimagemeta', '
	SELECT FILETYPE
	FROM uploadimage
	WHERE UPIMGID = '.$upimgid,
	TEN_MINUTES))
) {
	image_bail(503);
}
if ($is_filetype !== $filetype) {
	$new_uri = preg_replace("!(?<=\.)$filetype$!", $is_filetype, $new_uri ?? $_SERVER['REQUEST_URI']);
	error_log("supplied filetype $filetype is not stored filetype $is_filetype, redirect to: $new_uri");

}
if (isset($new_uri)) {
	moved_permanently($new_uri);
}
if (in_array($upimgid, [
		74235,		# see https://vip.partyflock.nl/ticket/1414071	... cannot change visibility for only weblog link I suppose
		221101,		# see https://vip.partyflock.nl/ticket/1409834
		295059,		# see https://vip.partyflock.nl/ticket/1382248
		298851,		# see https://vip.partyflock.nl/ticket/1447351
		338424,		# see https://vip.partyflock.nl/ticket/1407927
		339194,		# see https://vip.partyflock.nl/ticket/1414667
		341230,		# see https://vip.partyflock.nl/ticket/1410617
		344058,		# see https://vip.partyflock.nl/ticket/1389783
		350209,		# see https://vip.partyflock.nl/ticket/1386286
		374770,		# see https://vip.partyflock.nl/ticket/1329356
		445216,		# see https://vip.partyflock.nl/ticket/1409731
		476378,		# see https://vip.partyflock.nl/ticket/1414394
		584259,		# see https://vip.partyflock.nl/ticket/1455898
	], true)
&&	!have_admin()
) {
	image_bail(410);
}

$format_info = explode('_', $format, 2);
$format_type = $format_info[0];

if ($format_data = $format_info[1] ?? null) {
	switch ($format_type) {
	case 'manualcrop':
		if (!preg_match('"^(\d+)_(\d+)x(\d+),(\d+),(\d+)$"', $format_data, $match)) {
			# This cannot happen
			image_bail(404);
		}
		[, $want_size, $W, $H, $X, $Y] = $match;
		$want_width  =
		$want_height = (int)$want_size;
		$W /= 1000000000;
		$H /= 1000000000;
		$X /= 1000000000;
		$Y /= 1000000000;
		break;

	case 'smartcrop':
	case 'pillarbox':
	case 'letterbox':
		$want_width  =
		$want_height = (int)$format_data;
		break;

	default:
		error_log("WARNING unrecognized format: $format, in {$_SERVER['REQUEST_URI']}");
		image_bail(404);
	}
} elseif (str_contains($format, 'x')) {
	$format_type = 'dimensions';
	# Explicit dimensions:
	[$want_width, $want_height] = int_explode('x', $format);
} elseif (in_array($format, [
	'tiny',
	'tiny@2x',
	'thumb',
	'thumb@2x',
	'regular',
	'regular@2x',
	'high',
	'high@2x',
	'original',
], true)) {
	$format_type = 'named_size';
	# Descriptive size-text: tiny|thumb|regular|high|original, optionally with @2x postfix
	if (!($uploadimage = db_single_assoc(['uploadimage', 'uploadimagemeta'], "
		SELECT	SIZE, MSTAMP, USERID, BUSY,
				WIDTH, HEIGHT, FILETYPE
		FROM uploadimage
		JOIN uploadimagemeta USING (UPIMGID)
		WHERE SIZE = '$format'
		  AND UPIMGID = $upimgid"))
	) {
		image_bail($uploadimage === false ? 503 : 404);
	}
	$want_width  = $uploadimage['WIDTH'];
	$want_height = $uploadimage['HEIGHT'];
} else {
	error_log("WARNING unrecognized format: $format, in {$_SERVER['REQUEST_URI']}");
	image_bail(404);
}

$$format_type = true;

if (!$want_width) {
	image_bail(404);
}
if (empty($uploadimage)) {
	# Pick image with nearest resolution higher than requested
	if (!($uploadimage = db_single_assoc(['uploadimage', 'uploadimagemeta'], /** @lang MariaDB */ "
		(	SELECT	SIZE, MSTAMP, BUSY, USERID,
					FILETYPE, WIDTH, HEIGHT,
					1 AS PREFER
			FROM uploadimage
			JOIN uploadimagemeta USING (UPIMGID)
			WHERE UPIMGID = $upimgid
			  AND WIDTH  >= $want_width
			  AND HEIGHT >= $want_height
			ORDER BY WIDTH * HEIGHT
			LIMIT 1
		) UNION (
			SELECT	SIZE, MSTAMP, BUSY, USERID,
					FILETYPE, WIDTH, HEIGHT,
					0 AS PREFER
			FROM uploadimage
			JOIN uploadimagemeta USING (UPIMGID)
			WHERE UPIMGID = $upimgid
			ORDER BY WIDTH * HEIGHT DESC
			LIMIT 1
		)
		ORDER BY PREFER DESC
		LIMIT 1"))
	) {
		image_bail($uploadimage === false ? 503 : 404);
	}
	$debug_upload_images && error_log("$dbgid requested: {$want_width}x$want_height, picked: {$uploadimage['WIDTH']}x{$uploadimage['HEIGHT']} @ {$_SERVER['REQUEST_URI']}");
/*	if (isset($dimensions)
	&&	(	$uploadimage['WIDTH']  < $want_width
		||	$uploadimage['HEIGHT'] < $want_height)
	) {
		$new_uri = str_replace("_{$want_width}x{$want_height}_", "_{$uploadimage['WIDTH']}x{$uploadimage['HEIGHT']}_", $_SERVER['REQUEST_URI']);

		$debug_upload_images && error_log("$dbgid jumping to $new_uri @ {$_SERVER['REQUEST_URI']}");

		see_other($new_uri);
	}*/

}

if ($debug_upload_images
&&	$filetype === 'webp'
&&	$uploadimage['FILETYPE'] !== 'webp'
) {
	error_log(
		"$dbgid conversion to webp in {$_SERVER['REQUEST_URI']} for {$_SERVER['REMOTE_ADDR']}, should we redirect to ".
		preg_replace('"\.webp$"i', '', $_SERVER['REQUEST_URI']).
		'?'.
		(empty($_SERVER['HTTP_USER_AGENT']) ? '' : ", user agent: {$_SERVER['HTTP_USER_AGENT']}")
	);
}

$debug_upload_images && error_log("$dbgid uploadimage busy: {$uploadimage['BUSY']} ".($uploadimage['BUSY'] ? 'yes' : 'no')." @ {$_SERVER['REQUEST_URI']}");

$debug_upload_images && error_log("$dbgid uploadimage last modified: "._datetime_get($uploadimage['MSTAMP'])." @ {$_SERVER['REQUEST_URI']}");

#$smart_crop &&= should_smart_crop($uploadimage['WIDTH'], $uploadimage['HEIGHT'], $want_width, $want_height);

if (isset($manualcrop)) {
	$W = (int)round($W * $uploadimage['WIDTH']);
	$H = (int)round($H * $uploadimage['HEIGHT']);
	$X = (int)round($X * $uploadimage['WIDTH']);
	$Y = (int)round($Y * $uploadimage['HEIGHT']);
}

if ((!isset($type) || in_array($type, ['party', 'party2'], true))
&&	!have_admin('party')
&&	(	($delayed = is_delayed_flyer($upimgid))
	||	 $delayed === null)
) {
	image_bail($delayed ? 403 : 503);
}

$cache_file = get_cache_path("/uploadimage/$upimgid/$format");

/*if (isset($manualcrop)
||	isset($letterbox)
||	isset($pillarbox)
) {
	$cache_file .= $format_type.'/'.$format_data;
} elseif (isset($smartcrop)) {
	$cache_file .= 'smartcrop/'.$format;
} else {
	$cache_file .= $format;
}*/

if (file_exists($cache_file)) {
	if (false === ($cache_size  = filesize ($cache_file))
	||	false === ($cache_stamp	= filemtime($cache_file))
	) {
		image_bail(503);
	}
	assert(is_int($cache_stamp));	# Satisfy EA inspection
} else {
	$cache_size  = 0;
	$cache_stamp = 0;
}
$done_redo = $cache_stamp > REDO_BEFORE;

$debug_upload_images && error_log("$dbgid cache_file: $cache_file, cache size: $cache_size, cache_stamp: "._datetime_get($cache_stamp)." {$_SERVER['REQUEST_URI']}");

# need work for temporary, not final
$need_work =
	$want_width  !== $uploadimage['WIDTH']
||	$want_height !== $uploadimage['HEIGHT']
||	isset($letterbox)
||	isset($pillarbox)
||	isset($manualcrop)
||	isset($smartcrop);

$debug_upload_images && error_log("$dbgid need_work: ".($need_work ? 'yes' : 'no')." {$_SERVER['REQUEST_URI']}");

# NOTE: $database_exact will be set for queries for exact same dimensions as are stored in the database.
# 		No need to perform any changes on them.

$database_exact =
	!$need_work
&&	(	isset($named_size)
	||	$want_width  === $uploadimage['WIDTH']
	&&	$want_height === $uploadimage['HEIGHT']);

$debug_upload_images && error_log("$dbgid database_exact version: ".($database_exact ? 'yes' : 'no')." @ {$_SERVER['REQUEST_URI']}");

if ($have_cache
=	$cache_size
&&	$cache_stamp >= $uploadimage['MSTAMP']
) {
	$debug_upload_images && error_log("$dbgid cache size: $cache_size @ {$_SERVER['REQUEST_URI']}");
	header('X-Have-Cache: '.$cache_size);
}

if ($refresh_cache
=	force_refresh()
||	(	# Before this stamp, non need_work images went through 'convert'
		$cache_stamp < 1540751700	# 2018-10-28 19:35
	#	Bug in PNG processing between these dates:
	||	$filetype === 'png'
	&&	$cache_stamp > 1729901425	# 2024-10-26 02:10:25
	&&	$cache_stamp < 1730311864	# 2024-10-30 19:11:04
	#	Letterboxes where always generated with black bars instead of average color:
	||	(	isset($letterbox)
		||	isset($pillarbox))
	&&	$cache_stamp > 1706448000	# 2024-01-28 14:20:00
	&&	$cache_stamp < 1707935362	# 2024-02-14 19:29:22
	#	Wrong sizes original image was used to determine crop:
	||	isset($manualcrop)
	&&	$cache_stamp > 1730329200	# 2024-10-31 00:00:00
	&&	$cache_stamp < 1730502971)	# 2024-11-02 00:16:11
) {
	header('X-Force-Fresh: true');
	$debug_upload_images && error_log("$dbgid refresh_cache to bad date range @ {$_SERVER['REQUEST_URI']}");
}

$debug_upload_images && error_log("$dbgid refresh_cache: ".($refresh_cache ? 'yes' : 'no')." @ {$_SERVER['REQUEST_URI']}");

$return_definitive =
	$refresh_cache
||	$database_exact
||	isset($_REQUEST['definitive'])
||	!$uploadimage['BUSY']
&&	$cache_stamp >= $uploadimage['MSTAMP']
?	'.definitive'
:	'';

$debug_upload_images && error_log("$dbgid return definitive: ".($return_definitive ? 'yes' : 'no')." @ {$_SERVER['REQUEST_URI']}");

$etag =	'202310050.'.UPLOAD_OUTPUT_VERSION.$return_definitive.$format;

$debug_upload_images && error_log("$dbgid etag: $etag @ {$_SERVER['REQUEST_URI']}");

if (not_modified($uploadimage['MSTAMP'], $etag)) {
	$debug_upload_images && error_log("$dbgid not modified @ {$_SERVER['REQUEST_URI']}");
	header('X-In-User-Cache: true');
	if (!$refresh_cache) {
		image_bail(304);
	}
}

if (!$database_exact
&&	(	 $return_definitive
	||	 $refresh_cache
	||	!$have_cache)
&&	($load = sys_getloadavg())
&&	$load[0] < 100	# more than 100 seems bogus
&&	(	ROBOT
	&&	$load[0] >= 5
	||	empty($_COOKIE['FLOCK_IDENTID'])
	&&	$load[0] >= 10
	||	$load[0] >= 20)
) {
	$after = ONE_HOUR;
	header('Retry-After: '.$after);
	image_bail(429);
}

header('Content-Type: '.(
	$filetype === 'swf'
	?	'application/x-shockwave-flash'
	:	'image/'.($filetype === 'jpg' ? 'jpeg' : $filetype)));	# NOSONAR

header('Cache-Control: public,'.(
	!$have_cache
||	!$return_definitive
||	have_admin()
||	CURRENTUSERID === $uploadimage['USERID']
||	$uploadimage['BUSY']
?	'must-revalidate,max-age=0'
:	'must-revalidate,max-age='.ONE_WEEK
));

if ( $have_cache
&&	 $return_definitive
&&	!$refresh_cache
) {
	$debug_upload_images && error_log("$dbgid DEBUG sending cache file of size $cache_size and existing @ {$_SERVER['REQUEST_URI']}");
	header('Content-Length: '.$cache_size);
	HEAD_method() && exit;
	header('X-Sendfile: '.$cache_file);
	exit;
}

if ($database_exact
&&	(	!$have_cache
	||	$refresh_cache)
) {
	if (!($data = db_single_string('uploadimage_data', "
		SELECT DATA
		FROM uploadimage_data
		WHERE SIZE = '{$uploadimage['SIZE']}'
		  AND UPIMGID = $upimgid"))
	) {
		if ($data !== false) {
			mail_log("missing data '{$uploadimage['SIZE']}' of upload image $upimgid");
		}
		image_bail(503);
	}
	if (!safe_mkdir(dirname($cache_file), 0777, true)) {
		image_bail(503);
	}
	if (($cache_size = strlen($data)) !== file_put_contents($cache_file, $data)) {
		image_bail(503);
	}
	update_uploadimage_use_count($uploadimage['SIZE']);
	$debug_upload_images && error_log("$dbgid DEBUG exiting after sending stored database exact '{$uploadimage['SIZE']}' of size $cache_size) in cache @ {$_SERVER['REQUEST_URI']}");
	header('Content-Length: '.$cache_size);
	HEAD_method() && exit;
	echo $data;
	exit;
}
# NOTE: This routine is called to make the default uploadimage sizes.
#		We lock for the entire procedure, so we make sure the non-quick	variants are produced before any other routine needs them.
#		images/upload.php uses the original, we want it to get the non-quick version of course.
#		element/id is preferred but uploadimage can also be called with explicit upimgid only

/*$extra_id = $want_width.'x'.$want_height;
if (isset($letterbox)
||	isset($pillarbox)) {
	$extra_id .= ":$boxtype";
} elseif (isset($smartcrop)) {
	$extra_id .= ':smartcrop';
} elseif (isset($manualcrop)) {
	$extra_id .= ":manualcrop:$X,$Y,$W,$H";
}*/

if (!lock_uploadimage_for_processing(
	$element ?? (string)$upimgid,
	$id ?? null,
	$format, //extra_id,
	$return_definitive ? FIVE_MINUTES : null)
) {
	image_bail(503);
}

if (!($data = db_single_string('uploadimage_data', "
	SELECT DATA
	FROM data_db.uploadimage_data
	WHERE UPIMGID = $upimgid
	  AND SIZE = '{$uploadimage['SIZE']}'"))
) {
	mail_log("missing data '{$uploadimage['SIZE']}' of upload image $upimgid", error_log: 'ERROR');
	image_bail($orig === false ? 503 : 404);
}

update_uploadimage_use_count($uploadimage['SIZE']);

if (isset($smartcrop)
&& false === ($crop = determine_smart_crop_for_uploadimage($upimgid, $data, $want_width, $want_height))
) {
	image_bail(503);
}

$orig_data = $data;

if (!$return_definitive
&&	$need_work
) {
	$debug_upload_images && error_log("$dbgid creating immediate low quality version @ {$_SERVER['REQUEST_URI']}");

	$img = new Imagick();

	$low_quality = $debug_upload_images;

	try {
		# for now, make a good image, replace it later with _imageprocessing (why?)
		$img->readImageBlob($data);
		/** @noinspection NestedTernaryOperatorInspection */
		$img->setImageCompressionQuality($low_quality ? 5 : ($filetype === 'jpg' ? 93 : 85));    # NOSONAR
		$img->setInterlaceScheme(Imagick::INTERLACE_LINE);

		$filter = $low_quality ? Imagick::FILTER_BOX : Imagick::FILTER_LANCZOS;
		# Previously, good filter was forced for determining top and bottom bars of
		# letterboxes and pillarboxes. Currently, they use the same filter.
		$good_filter = $filter;

		if (isset($manualcrop)) {
			$img->cropImage($W, $H, $X, $Y);
			$img->resizeImage($want_width, $want_height, $filter, 1);

		} elseif (
			isset($letterbox)
		||	isset($pillarbox)
		) {
			$orig_w = $uploadimage['WIDTH'];
			$orig_h = $uploadimage['HEIGHT'];

			$scaled_w = $orig_w;
			$scaled_h = $orig_h;

			scale(
				$scaled_w,
				$scaled_h,
				isset($letterbox) ? $want_width  : 0,
				isset($pillarbox) ? $want_height : 0
			);

			!$return_definitive
			?	$img->scaleImage ($scaled_w, $scaled_h)
			:	$img->resizeImage($scaled_w, $scaled_h, $filter, 1);

			if (!$img->getImageAlphaChannel()) {
				$clone = clone $img;
				if ($letterbox = $boxtype === 'letter') {
					$bar_w = $scaled_w;
					$bar_h = round(($want_height - $scaled_h) / 2);
					$ch = floor(.2 * $scaled_w);
					$clone->cropImage($scaled_w, $ch, 0, 0);
				} else {
					$bar_w = round(($want_width - $scaled_w) / 2);
					$bar_h = $scaled_h;
					$cw = floor(.2 * $scaled_w);
					$clone->cropImage($cw, $scaled_h, 0, 0);
				}
				$clone->resizeImage(1, 1, $good_filter, 1);
				$pixel = $clone->getImagePixelColor(0, 0);
				$color = $pixel->getColor();

				$bar_one = new ImagickDraw();
				$bar_one->setFillColor($pixel);
				$bar_one->rectangle(0, 0, $bar_w - 1, $bar_h - 1);

				$clone = clone $img;
				isset($letterbox) ?
					$clone->cropImage($scaled_w, $ch, 0, $scaled_h - $ch)
				:	$clone->cropImage($cw, $scaled_h, $scaled_w - $cw, 0);

				$clone->resizeImage(1, 1, $good_filter, 1);
				$pixel = $clone->getImagePixelColor(0, 0);
				$color = $pixel->getColor();

				$bar_two = new ImagickDraw();
				$bar_two->setFillColor($pixel);

				isset($letterbox) ?
					$bar_two->rectangle(0, $bar_h + $scaled_h - 1, $bar_w, $want_height - 1)
				:	$bar_two->rectangle($bar_w + $scaled_w - 1, 0, $want_width - 1, $bar_h);
			} else {
				$img->setImageBackgroundColor(new ImagickPixel('transparent'));
			}
			$img->extentImage(
				$want_width,
				$want_height,
				-(($want_width  - $scaled_w) >> 1),
				-(($want_height - $scaled_h) >> 1),
			);
			if (!empty($bar_one)) {
				$img->drawImage($bar_one);
				$img->drawImage($bar_two);
			}
		} elseif (!empty($crop)) {
			$super_search && error_log("$dbgid tmp: crop {$crop->width}x$crop->height @ {$_SERVER['REQUEST_URI']}");

			$img->cropImage($crop->width, $crop->height, $crop->x, $crop->y);

			if ($want_width !== $crop->width) {
				$super_search && error_log("$dbgid tmp: want width ($want_width) != crop width $crop->width scaling to {$want_width}x$want_height @ {$_SERVER['REQUEST_URI']}");

				$img->scaleImage($want_width, $want_height);
			}
		} else {
			$img->scaleImage($want_width, $want_height);
		}
		$data = $img->getImageBlob();

		$debug_upload_images && error_log("$dbgid generated immediate low quality of size ".strlen($data)." @ {$_SERVER['REQUEST_URI']}");

	} catch (Exception $e) {
		mail_log('something failed while resizing: '.$e->getMessage(), error_log: 'ERROR');
		image_bail(503);
	}
}

# store cache might stall waiting for slow process
# so show tmp version for current connection

if ($closed_connection = !$return_definitive) {
	header('Content-Length: '.($data_size = strlen($data)));
	$debug_upload_images && error_log("$dbgid sending low quality generated of size $data_size @ {$_SERVER['REQUEST_URI']}");
	HEAD_method() && exit;
	echo $data;

	$debug_upload_images && error_log("$dbgid closing connection @ {$_SERVER['REQUEST_URI']}");

	fastcgi_finish_request();

	$debug_upload_images && error_log("$dbgid DEBUG WARNING finished request, served $data_size @ {$_SERVER['REQUEST_URI']}");

	# Make sure only one process is processing the this image:
	# This is a very slow process, so we don't want to do this in the main process
	if (!($ok = db_getlock($key = "processing_uploadimage:$upimgid:$format:$filetype", 0))) {
		$debug_upload_images && error_log("$dbgid ERROR failed to get processing lock: $key @ {$_SERVER['REQUEST_URI']}");
		exit;
	}
}

if ($uploadimage['BUSY']) {
	$sleep_count = 0;
	$sleep_seconds = 0.5;
	$sleep_microseconds = (int)($sleep_seconds * 1000000);
	$warn_every_seconds = 4;
	$warn_every_count = (int)($warn_every_seconds  / $sleep_seconds);
	while (1 === ($busy = db_single_int('uploadimage', 'SELECT CAST(BUSY AS UNSIGNED) FROM uploadimage WHERE UPIMGID = '.$upimgid))) {
		usleep($sleep_microseconds);	# Sleep for 0.5 seconds
		++$sleep_count;
		if ($debug_upload_images && !($sleep_count % $warn_every_count)) {
			error_log("$dbgid WARNING slept for ".round($warn_every_count * $sleep_seconds, 1)." seconds due too busy @ {$_SERVER['REQUEST_URI']}");
		}
	}
	$debug_upload_images && error_log("$dbgid finally done sleeping, uploadimage non-busy @ {$_SERVER['REQUEST_URI']}");
	if ($busy !== 0) {
		error_log($error = 'unexpected busy value '.var_get($busy));
		mail_log($error);
	}
	$debug_upload_images && error_log("$dbgid slept for $sleep_count seconds waiting for uploadimage to be non-busy @ {$_SERVER['REQUEST_URI']}");
	if (false === ($existing_upimgid = db_single_int('uploadimage_exists', "
		SELECT EXISTING_UPIMGID
		FROM uploadimage_exists
		WHERE UPIMGID = $upimgid"))
	) {
		image_bail(503);
	}
	if ($existing_upimgid) {
		$new_uri = preg_replace(
			[
				"!(?<=_)$upimgid(?=/|\.|$)!",		# CURRENT
				"!(?<=/upload/)$upimgid(?=[._])!"],	# LEGACY
			(string)$existing_upimgid,
			$_SERVER['REQUEST_URI']);

		mail_log("redirecting {$_SERVER['REQUEST_URI']} to $new_uri", error_log: 'DEBUG');
		moved_permanently($new_uri);
	}
	if (!($orig_data = db_single_string('uploadimage_data', "
		SELECT DATA
		FROM data_db.uploadimage_data
		WHERE UPIMGID = $upimgid
		  AND SIZE = '{$uploadimage['SIZE']}'"))
	) {
		if (!$closed_connection) {
			image_bail(503);
		}
		exit;
	}
	update_uploadimage_use_count($uploadimage['SIZE']);
}
if (!safe_mkdir(dirname($cache_file), 0777, true)) {
	error_log("$dbgid ERROR safe_mkdir failed @ {$_SERVER['REQUEST_URI']}");
	if (!$closed_connection) {
		image_bail(503);
	}
	exit;
}
if (USE_FLOCK) {
	if (!($fp = fopen($cache_file, 'wb'))) {
		error_log("$dbgid ERROR fopen of $cache_file failed @ {$_SERVER['REQUEST_URI']}");
		if (!$closed_connection) {
			image_bail(503);
		}
		exit;
	}
} elseif (!($fp = dio_open($cache_file, O_RDWR | O_CREAT, 0644))) {
	error_log("$dbgid ERROR dio_open of $cache_file failed @ {$_SERVER['REQUEST_URI']}");
	if (!$closed_connection) {
		image_bail(503);
	}
	exit;
}
if (USE_FLOCK) {
	if (!flock($fp, LOCK_EX)) {
		mail_log("could not flock lock $cache_file", ['last_error' => error_get_last()]);
		error_log("$dbgid ERROR could not flock $cache_file @ {$_SERVER['REQUEST_URI']}");
		if (!$closed_connection) {
			image_bail(503);
		}
		exit;
	}
} elseif (-1 === ($rc = dio_fcntl($fp, F_SETLK, [
	'start'		=> 0,
	'length'	=> 0,
	'whence'	=> SEEK_SET,
	'type'		=> F_WRLCK,
]))) {
	mail_log("could not dio_fcntl lock $cache_file", ['rc' => $rc, 'last_error' => error_get_last()]);
	error_log("$dbgid ERROR dio_fnctl failed with -1 response, last error: ".var_get(error_get_last())." @ {$_SERVER['REQUEST_URI']}");
	if (!$closed_connection) {
		image_bail(503);
	}
	exit;
}
if (!$need_work) {
	$debug_upload_images && error_log("$dbgid WARNING need_work is no, but we're in definitive space @ {$_SERVER['REQUEST_URI']}");
	$definitive_data = $orig_data;
} else {
	# process_images is very slow proces, done in background
	# should make new upimgid?
	if (isset($letterbox)
	||	isset($pillarbox)) {
		$generate_sizes = [[$want_width, $want_height, isset($letterbox) ? 'letter' : 'pillar']];
	} elseif (isset($smartcrop)) {
		$generate_sizes = [[$want_width, $want_height, $crop->x, $crop->y, $crop->width, $crop->height]];
	} elseif (isset($manualcrop)) {
		$generate_sizes = [[$want_width, $want_height, $X, $Y, $W, $H]];
	} else {
		$generate_sizes = [[$want_width, $want_height]];
	}
	require_once __DIR__.'/../_imageprocessing.inc';
	if (!($info = process_images($generate_sizes, [
		'info'			=> null,
		'src'			=> null,
		'source_data'	=> $orig_data,
		'prefix'		=> $prefix ?? null]))
	) {
		if ($info === null) {
			# already processing, gracefully quit, don't remove cache file as it is not ours
			error_log("$dbgid ERROR already processing, info === null @ {$_SERVER['REQUEST_URI']}");
			exit;
		}
		error_log("$dbgid ERROR process_images failed with info === ".var_get($info)." @ {$_SERVER['REQUEST_URI']}");
		if (!$closed_connection) {
			image_bail(503);
		}
		exit;
	}
	$definitive_data = $info[0][0];
}
if (USE_FLOCK) {
	$bytes_written = fwrite($fp, $definitive_data);
	fclose($fp);
} else {
	$bytes_written = dio_write($fp, $definitive_data);
	dio_close($fp);
}
$definitive_data_size = strlen($definitive_data);
if ($bytes_written !== $definitive_data_size) {
	# Not all data was written, remove the cache file so next invocation can try again.
	unlink($cache_file);
}
if (!$closed_connection) {
	header('Content-Length: '.$deinitive_data_size);
	$debug_upload_images && error_log("$dbgid DEBUG exiting after returning definitive data of size $definitive_data_size @ {$_SERVER['REQUEST_URI']}");
	HEAD_method() && exit;
	echo $definitive_data;
}

function update_uploadimage_use_count(string $size): bool {
	return db_insupd('uploadimage_use_count', "
	INSERT INTO uploadimage_use_count SET
		SIZE	= '$size',
		HITS	= 1
	ON DUPLICATE KEY UPDATE
		HITS	= HITS + 1"
	);
}
