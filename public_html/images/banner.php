<?php

declare(strict_types=1);

define('CURRENTSTAMP',time());

require_once '../_exit_if_offline.inc';
require_once '../_argparse.inc';
require_once '../_db.inc';
require_once '../_require.inc';
require_once '../_modified.inc';
require_once '../_servertype.inc';
require_once '../_http_status.inc';
require_once '../_image_bail.inc';

if (!empty($_SERVER['eLANG'])) {
	moved_permanently(preg_replace("!^/{$_SERVER['eLANG']}!", '', $_SERVER['REQUEST_URI']));
}
if (!($bannerid = (int)$_SERVER['eBANNERID'])) {
	image_bail(http_status::NOT_FOUND);
}
if (!($info = db_single_array('banner','
	SELECT TYPE, GREATEST(MSTAMP, CSTAMP), WIDTH, HEIGHT
	FROM banner
	WHERE BANNERID = '.$bannerid))
) {
	image_bail($info);
}
[$type, $stamp, $w, $h] = $info;

header('Cache-Control: public,max-age=600');
#header('Cache-Control: public,must-revalidate');

switch ($type) {
case 'multiparty':
	if (!isset($_SERVER['e2x'], $_SERVER['eCRC'], $_SERVER['eFILETYPE'])
	||	!preg_match('"^_(\d+)(b)?_(\d+x\d+)$"',$_SERVER['e2x'],$match)
	) {
		image_bail(http_status::NOT_FOUND);
	}
	[, $partyid, $backside, $resolution] = $match;
	see_other('/images/'.($backside ? 'party2' : 'party')."/{$partyid}_{$resolution}_".hexdec($_SERVER['eCRC']).'.'.$_SERVER['eFILETYPE']);

case 'upload':
	require_once '../_bannerupload.inc';
	if (!($banner = get_bannerupload($bannerid,!empty($_SERVER['e2x'])))) {
		image_bail($banner === false ? http_status::INTERNAL_SERVER_ERROR : http_status::NOT_FOUND);
	}
	if (not_modified($stamp, $banner['CRC32'].':'.$banner['SIZE'])) {
		image_bail(http_status::NOT_MODIFIED);
	}
	switch ($banner['FILETYPE']) {
	case 'jpg':
		$banner['FILETYPE'] = 'jpeg';
		# fall through
	case 'avif':
	case 'gif':
	case 'png':
	case 'webp':
		header('Content-Type: image/'.$banner['FILETYPE']);
		break;
	case 'mp4':
		header('Content-Type: video/mp4');
		break;
	case 'swf':
		header('Content-Type: application/x-shockwave-flash');
		break;
	case 'swiffy':
		header('Content-Type: text/html; charset=utf-8');
		require_once '../_swiffy.inc';
		$banner['WIDTH']  = $w;
		$banner['HEIGHT'] = $h;
		show_swiffy_html($banner);
		exit;
	default:
		error_log("ERROR banner:$bannerid has unsupported file type: {$banner['FILETYPE']}");
		image_bail(http_status::INTERNAL_SERVER_ERROR);
	}
	header('Content-Length: '.$banner['SIZE']);
	echo $banner['DATA'];
	break;

/** @noinspection PhpMissingBreakStatementInspection */
case 'link':
	$url = db_single_string('bannerlink', 'SELECT URL FROM bannerlink WHERE BANNERID = '.$bannerid);
	# fall through
/** @noinspection PhpMissingBreakStatementInspection */
case 'iframe':
	$url ??= db_single_string('banneriframe', 'SELECT URL FROM banneriframe WHERE BANNERID = '.$bannerid);
	# fall through
case 'html':
	$url ??= db_single_string('bannerhtml', 'SELECT SCRIPT_URL FROM bannerhtml WHERE BANNERID = '.$bannerid);
	if (!$url) {
		image_bail($url);
	}
	require_once '../_nocache.inc';
	send_no_cache_headers();
	redirect($url, http_status::SEE_OTHER);
}
