<?php

declare(strict_types=1);

define('CURRENTSTAMP',time());

require_once '../_exit_if_offline.inc';
require_once '../_adstuff.inc';
require_once '../_argparse.inc';
require_once '../_currentuser.inc';
require_once '../_hosts.inc';
require_once '../_http_status.inc';
require_once '../_image_bail.inc';
require_once '../_memcache.inc';
require_once '../_nocache.inc';
require_once '../_require.inc';

# make sure we count every ad load
must_revalidate();

if (!empty($_SERVER['eLANG'])) {
	moved_permanently(
		preg_replace(
			"!^/{$_SERVER['eLANG']}!",
			'',
			$_SERVER['REQUEST_URI']));
}

if (!($adid = (int)$_SERVER['eADID'])) {
	image_bail(http_status::NOT_FOUND);
}
if (!($ad = memcached_single_assoc(['ad', 'adinfo', 'banner'], '
	SELECT	ADID, adinfo.CSTAMP, banner.BANNERID, FREQCAP, FREQCAP_DAY, CITYLIMIT,
	       	banner.TYPE, banner.FALLBACKID, STARTSTAMP, STOPSTAMP, KEEPGOING,
			IF(banner.MSTAMP, banner.MSTAMP, banner.CSTAMP) AS STAMP
	FROM ad
	JOIN adinfo USING (ADID)
	JOIN banner USING (BANNERID)
	WHERE ADID = '.$adid))
) {
	image_bail($ad);
}
assert(is_array($ad)); # Satisfy EA inspection
$skip_jump = false;
switch ($ad['TYPE']) {
case 'multiparty':
	if (!isset($_SERVER['e2x'], $_SERVER['eCRC'], $_SERVER['eFILETYPE'])
	||	!preg_match('"^_(\d+)(b)?_(\d+x\d+)$"', $_SERVER['e2x'], $match)
	) {
		image_bail(http_status::NOT_FOUND);
	}
	[, $partyid, $backside, $resolution] = $match;
	redirect(
		'/images/'.($backside ? 'party2' : 'party').
		"/{$partyid}_{$resolution}_".hexdec($_SERVER['eCRC']).'.'.$_SERVER['eFILETYPE'],
		http_status::SEE_OTHER,
	);
	$skip_jump = true;
	$crc = null;
	break;
case 'upload':
	if (!($bannerupload = memcached_single_array('bannerupload', '
		SELECT FILETYPE, CRC32
		FROM bannerupload
		WHERE BANNERID = '.$ad['BANNERID']))
	) {
		image_bail(http_status::INTERNAL_SERVER_ERROR);
	}
	[$filetype, $crc] = $bannerupload;
	break;
case 'link':
	$crc = db_single_int('bannerlink', '
		SELECT CRC32(URL)
		FROM bannerlink
		WHERE BANNERID = '.$ad['BANNERID']);
	$filetype = 'img';
	break;
case 'iframe':
	$crc = db_single_int('banneriframe', '
		SELECT CRC32(URL)
		FROM banneriframe
		WHERE BANNERID = '.$ad['BANNERID']);
	$filetype = 'frm';
	break;
case 'html':
	$crc = db_single_int('bannerhtml', '
		SELECT CRC32(SCRIPT_URL)
		FROM bannerhtml
		WHERE BANNERID = '.$ad['BANNERID']);
	$filetype = 'js';
	break;
default:
	error_log("WARNING unsupported banner type: {$ad['TYPE']}");
	image_bail(http_status::INTERNAL_SERVER_ERROR);
}
assert(isset($filetype)); # Satisfy EA inspection
if ($skip_jump) {
	if (!$crc) {
		mail_log("crc is not set for ad: $adid", get_defined_vars(), error_log: 'WARNING');
	}
	$element = ($crc ?? null) ? create_ad_stuff(STUFF_BANNER, $crc) : 'banner';
	see_other(
		"/images/$element/{$ad['BANNERID']}".($crc ? '_'.dechex($crc) : '').
		(empty($_SERVER['e2x']) ? '' : '@2x').'.'.($filetype === 'swiffy' ? 'frm' : $filetype).
		(!empty($_SERVER['QUERY_STRING']) ? '?'.$_SERVER['QUERY_STRING'] : '')
	);
}

fastcgi_finish_request();

# In the past, doing adload after a fastcgi_finish_request(), somehow failed (sometimes)
require_once '../_adcount.inc';
adload($ad, (int)$_SERVER['eINCID'], (int)$_SERVER['eUNIQ']);
