<?php

declare(strict_types=1);

define('CURRENTSTAMP',time());

require_once '../_exit_if_offline.inc';
require_once '../_argparse.inc';
require_once '../_flockmod.inc';
require_once '../_image_bail.inc';
require_once '../_db.inc';
require_once '../_servertype.inc';
require_once '../_currentuser.inc';
require_once '../_modified.inc';
require_once '../_require.inc';
require_once '../_hosts.inc';

deny_robots_if_partyflock_under_load();

# see _userimage.inc for LARGE specification

if (!($userid = have_idnumber($_SERVER, 'eUSERID'))
||	!($dataid = have_idnumber($_SERVER, 'eDATAID'))
||	!($filetype = have_element($_SERVER, 'eFILETYPE', ['jpg', 'gif', 'png', 'webp', 'avif'], true))
) {
	image_bail(404);
}
if (!empty($_SERVER['eLANG'])) {
	moved_permanently(str_replace("/{$_SERVER['eLANG']}", '', $_SERVER['REQUEST_URI']));
}
if (!empty($_SERVER['eSIZE'])
||	!empty($_SERVER['e2x'])
) {
	moved_permanently("/images/user/{$userid}_$dataid.{$_SERVER['eFILETYPE']}");
}
if (!($user = memcached_user($userid))) {
	image_bail(404);
}
$currentuser = new _currentuser(TINY_USER | SKIP_SETTINGS);
if (invisible_profile($userid)) {
	image_bail(404);
}
$no_cache = isset($_REQUEST['NOMEMCACHE']);
if (!($userimage = db_single_assoc(['userimage', 'userimagedatameta', 'userimagemisc', 'userimage_history'], "
	SELECT DATAID, MSTAMP, LEN, CRC, TYPE, COPYRIGHT
	FROM userimage
	JOIN userimagedatameta USING (DATAID)
	LEFT JOIN userimagemisc USING (DATAID)
	WHERE USERID = $userid
	  AND DATAID = $dataid
	UNION
	SELECT DATAID, MSTAMP, LEN, CRC, TYPE, COPYRIGHT
	FROM userimage_history
	JOIN userimagedatameta USING (DATAID)
	JOIN userimagemisc USING (DATAID)
	WHERE USERID = $userid
	  AND DATAID = $dataid",
	$no_cache ? DB_USE_MASTER : 0))
) {
	image_bail($userimage === false ? 500 : 404);
}
if ($userimage['COPYRIGHT']
&&	!have_admin()
) {
	image_bail(404);
}
if (empty($_SERVER['eFILETYPE'])
||	$_SERVER['eFILETYPE'] !== $userimage['TYPE']
) {
	moved_permanently("/images/user/{$userid}_$dataid.{$userimage['TYPE']}");
}
header('Cache-Control: public,max-age='.(have_admin('helpdesk') || CURRENTUSERID === $userid ? '0,must-revalidate' : '43200'));

if (not_modified($userimage['MSTAMP'])) {
	image_bail(304);
}

header('Content-Type: image/'.($userimage['TYPE'] === 'jpg' ? 'jpeg' : $userimage['TYPE']));

if (!($data = db_single('userimagedata', /** @lang MariaDB */ '
	SELECT SQL_NO_CACHE DATA
	FROM data_db.userimagedata
	WHERE DATAID = '.$userimage['DATAID'],
	$no_cache ? DB_USE_MASTER : 0))
) {
	image_bail($data === false ? 500 : 404);
}

header('Content-Length: '.strlen($data));
echo $data;
