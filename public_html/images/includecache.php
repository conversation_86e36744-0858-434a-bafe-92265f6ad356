<?php

declare(strict_types=1);

define('CURRENTSTAMP',time());
define('TODAYSTAMP',mktime(0,0,0));

require_once '../_exit_if_offline.inc';
require_once '../_image_bail.inc';
require_once '../_db.inc';
require_once '../_memcache.inc';
require_once '../_currentuser.inc';
require_once '../_helper.inc';
require_once '../_modified.inc';
require_once '../_hosts.inc';
require_once '../_spider.inc';
require_once '../_date.inc';
require_once '../_require.inc';

header('X-Robots-Tag: noindex');

if (!have_hexadecimal($_SERVER, 'eDATAID')
||	!have_hexadecimal($_SERVER, 'eURLID')
||	!have_hexadecimal($_SERVER, 'eCRC')
) {
	image_bail(404);
}

if (have_hexadecimal($_SERVER, 'eELEMENTID')
&&	have_hexadecimal($_SERVER, 'eID')
) {
	$info = db_single_array(['includecacheurl', 'includecache', 'includecachemisc'], "
		SELECT	URL, COPYRIGHT,
				(SELECT 0 + MAX(MALWARE)  FROM includecachemisc WHERE DATAID = 0x{$_SERVER['eDATAID']}) AS MALWARE,
				(SELECT 0 + MAX(NEEDUSER) FROM includecachemisc WHERE DATAID = 0x{$_SERVER['eDATAID']}) AS NEEDUSER
		FROM includecacheurl
		JOIN includecache USING (URLID)
		LEFT JOIN includecachemisc USING (DATAID, URLID, ELEMENT, ID)
		WHERE ELEMENT	= ".hexdec($_SERVER['eELEMENTID'])."
		  AND URLID		= 0x{$_SERVER['eURLID']}
		  AND CRC		= 0x{$_SERVER['eCRC']}
		  AND DATAID	= 0x{$_SERVER['eDATAID']}
		  AND ID		= 0x{$_SERVER['eID']}"
	);
} else {
	$info = db_single_array(['includecacheurl', 'includecache', 'includecachemisc'], "
		SELECT	URL,
				(SELECT 0 + MAX(COPYRIGHT) FROM includecachemisc WHERE DATAID = 0x{$_SERVER['eDATAID']} AND URLID = 0x{$_SERVER['eURLID']}) AS COPYRIGHT,
				(SELECT 0 + MAX(MALWARE)   FROM includecachemisc WHERE DATAID = 0x{$_SERVER['eDATAID']}) AS MALWARE,
				(SELECT 0 + MAX(NEEDUSER)  FROM includecachemisc WHERE DATAID = 0x{$_SERVER['eDATAID']}) AS NEEDUSER
		FROM includecacheurl
		JOIN includecache USING (URLID)
		WHERE DATAID	= 0x{$_SERVER['eDATAID']}
		  AND CRC		= 0x{$_SERVER['eCRC']}
		  AND URLID		= 0x{$_SERVER['eURLID']}"
	);
}

if (!$info) {
	image_bail($info === false ? 503 : 404);
}

[$url, $copyright, $malware, $need_user] = $info;

if ($need_user) {
	$currentuser = new _currentuser(TINY_USER | SKIP_SETTINGS);
	if (!have_user()) {
		image_bail(403);
	}
}
if ($malware) {
	image_bail(410);
}

# this Link makes the original image source the canonical:

header('Link: <'.$url.'>; rel="canonical"');

if (ROBOT || $copyright) {
	header("Location: $url", true, 303);
	image_bail(303);
}

if (!($include_image = memcached_single_assoc(['includecachemeta', 'mimetype'], "
	SELECT MSTAMP, MIMETYPE, SIZE, DATAID
	FROM includecachemeta
	LEFT JOIN mimetype USING (MIMETYPEID)
	WHERE DATAID = 0x{$_SERVER['eDATAID']}",
	FIVE_MINUTES
))) {
	image_bail($include_image === false ? 503 : 404);
}

/** @noinspection PhpRedundantOptionalArgumentInspection */
extract($include_image, EXTR_OVERWRITE);

if (not_modified($MSTAMP)) {
	image_bail(304);
}

$year = gmdate('Y', $MSTAMP);
if (!file_exists($file = "/mnt/gluster/includecache/$year/$DATAID")) {
	image_bail(404);
}

header('Content-Length: '.$SIZE);
header('Content-Type: '.($MIMETYPE ?: 'image/'));
header('Cache-Control: public');
header('X-Sendfile: '.$file);
