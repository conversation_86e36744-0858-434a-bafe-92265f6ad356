<?php

define('CURRENTSTAMP',time());
define('TODAYSTAMP',mktime(0,0,0));

require_once '_require.inc';
require_once '_currentuser.inc';
require_once '_vote.inc';
require_once '_flockmod.inc';
require_once '_nocache.inc';

send_no_cache_headers();

function bail($errno) {
	display_messages();
	http_response_code($errno);
	exit;
}

if (!have_valid_origin()) {
	bail(403);
}

$currentuser = new _currentuser(TINY_USER | SKIP_SETTINGS);

if (!have_user()) {
	bail(403);
}
if (!vote_process()) {
	bail(500);
}

vote_display_choices(null, [$_POST['CASTVOTE'],CURRENTSTAMP], null, null, isset($_POST['TMP']));
