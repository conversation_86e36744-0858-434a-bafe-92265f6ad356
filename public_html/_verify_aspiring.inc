<?php

/*function is_valid_aspiring_item(string $table, int $id, string &$error_message = null): bool {
	$item = db_single_assoc($table, 'SELECT USERID, ACCEPTED FROM '.$table.' WHERE '.$table.'ID='.$id);
	if (!$item) {
		$error_message = 'item not found';
		return false;
	}
	if ($item['USERID'] !== CURRENTUSERID) {
		$error_message = 'this item was not created by you';
		return false;
	}
	if ($item['ACCEPTED']) {
		$error_message = 'this item is already verified';
		return false;
	}
	return true;
}*/

/*function show_check_mark_for_aspiring() {
	if (!is_aspiring_content_admin()) {
		return;
	}
	$element = $_REQUEST['sELEMENT'];
	$id = $_REQUEST['sID'];
	$desc = null;

	if (is_valid_aspiring_item($element, $id, $error_message)) {
		$verify_cstamp = db_single('verify_aspiring', '
			SELECT CSTAMP
			FROM verify_aspiring
			WHERE USERID = '.CURRENTUSERID.'
			  AND ELEMENT = "'.$element.'"
			  AND ID = '.$id
		);
		if (query_failed()) {
			$error_message = 'could not obtain verification state';
		}
	}

	layout_open_menu();
	layout_open_menuitem();
	?><label class="<?= $error_message ? 'error' : 'notice' ?>"><?
	if ($error_message) {
		echo $error_message;
	} else {
		include_js('js/form/verify_aspiring');

		?>item is eligible for verification:<?
		?> <?
		show_input([
			'type'		=> 'checkbox',
			'checked'	=> (bool)$verify_cstamp,
			'onclick'	=> 'Pf.markVerifyAspiring(this, "'.$element.'", '.$id.')',
		]);
		?> verify<?
	}
	?></label><?
	layout_close_menuitem();
	layout_close_menu();
}*/

/*function process_mark_verify_aspiring() {
	if (!require_post()
	||	!($element = require_element($_POST, 'ELEMENT', ['artist', 'location', 'organization', 'party']))
	||	!($id = require_idnumber($_POST, 'ID'))
	||	false === ($mark = require_number($_POST, 'MARK'))
	||	$mark > 1
	) {
		bail(400);
	}

	global $currentuser;
	$currentuser = new _currentuser(TINY_USER | SKIP_SETTINGS);
	if (!is_aspiring_content_admin()
	||	!is_valid_aspiring_item($element, $id, $error_message)
	) {
		bail(403);
	}
	if ($mark) {
		if (!db_replace('verify_aspiring', '
			REPLACE INTO verify_aspiring SET
				USERID	='.CURRENTUSERID.',
				ELEMENT	="'.$element.'",
				ID	='.$id.',
				CSTAMP	='.CURRENTSTAMP)
		)  {
			bail(503);
		}
	} else {
		if (!db_delete('verify_aspiring', '
			DELETE FROM verify_aspiring
			WHERE USERID = '.CURRENTUSERID.'
			  AND ELEMENT = "'.$element.'"
			  AND ID = '.$id)
		) {
			bail(503);
		}
	}
	bail(200);
}*/
