<?php

function perform_commit(): ?bool {
	switch ($_REQUEST['ACTION']) {
	case 'commit':		return prohib_commit();
	case 'active':		return prohib_process_active();
	case 'inactive':	return prohib_process_inactive();
	case null:
	default:			return null;
	}
}
function display_body(): void {
	switch ($_REQUEST['ACTION']) {
	case 'form':
	case 'commit':
	case 'single':		prohib_display_single_or_form(); break;
	case 'inactive':
	case 'active':		prohib_display_list(); break;
	case null:
	default:			prohib_display_overview(); break;
	}
}
function prohib_menu(): void {
	layout_open_menu();
	layout_menuitem(Eelement_name('overview'),'/prohib',!isset($_REQUEST['ACTION']));
	layout_menuitem(__C('status:inactive'),'/prohib/inactive',$_REQUEST['ACTION'] === 'inactive');
	layout_close_menu();
}

function prohib_process_active(): bool {
	require_once '_offense_types.inc';
	if (!have_post()
	||	!require_admin('prohib')
	||	!require_hash($_POST, 'STATUS',HASH_NUMBER,HASH_ELEMENT,$size,array('active','deleted'))
	||	!require_hash($_POST, 'OFFENSE_TYPE',HASH_NUMBER,HASH_STRING,$size,array_keys(is_offense_type()))
	||	!($orig_types = db_simple_hash('prohibited','
		SELECT PROHIBID,OFFENSE_TYPE
		FROM prohibited
		WHERE PROHIBID IN ('.implodekeys(',',$_POST['STATUS']).')'))
	) {
		return false;
	}
	$status_cases = $type_cases = '';
	$updated = array();
	$firstid = null;
	$lastid = 0;
	foreach ($_POST['STATUS'] as $prohibid => $status) {
		if (!isset($_POST['OFFENSE_TYPE'][$prohibid])
		||	!is_offense_type($type = $_POST['OFFENSE_TYPE'][$prohibid])
		||	!isset($orig_types[$prohibid])
		) {
			register_error('error:form:invalid_data_LINE');
			return false;
		}
		$lastid = max($lastid,$prohibid);
		if ($status === 'active'
		&&	$type == $orig_types[$prohibid]
		) {
			continue;
		}
		$updated[] = $prohibid;
		$status_cases .= ' WHEN '.$prohibid.' THEN "'.$status.'" ';
		$type_cases .= ' WHEN '.$prohibid.' THEN "'.$type.'" ';
		$firstid = $firstid ? min($firstid,$prohibid) : $prohibid;
	}
	/** @noinspection NotOptimalIfConditionsInspection */
	if ($updated
	&&	(	!db_insert('prohibited_log','
			INSERT INTO prohibited_log
			SELECT * FROM prohibited
			WHERE PROHIBID IN ('.($updatedstr = implode(',',$updated)).')')
		||	!db_update('prohibited', "
			UPDATE prohibited SET 
				STATUS			= CASE PROHIBID $status_cases END,
				OFFENSE_TYPE	= CASE PROHIBID $type_cases END,
				MUSERID			= ".CURRENTUSERID.',
				MSTAMP			= '.CURRENTSTAMP."
			WHERE PROHIBID IN ($updatedstr)"))
	||	!db_insert('prohibited_checked','
		INSERT INTO prohibited_checked SET
			USERID	= '.CURRENTUSERID.',
			STAMP	= '.CURRENTSTAMP.",
			STARTID	= $firstid,
			STOPID	= $lastid")
	) {
		return false;
	}
	register_notice('prohib:notice:processed_range_LINE');
	$_REQUEST['STARTID'] = db_single('prohibited','SELECT MIN(PROHIBID) FROM prohibited WHERE PROHIBID>'.$lastid);
	return true;
}
function prohib_process_inactive(): bool {
	require_once '_offense_types.inc';
	if (!have_post()
	||	!require_admin('prohib')
	||	!require_hash($_POST, 'ACT',HASH_NUMBER,HASH_ELEMENT,$size,array('accept','reject','ignore'))
	||	!require_hash($_POST, 'OFFENSE_TYPE',HASH_NUMBER,HASH_ELEMENT,$size2,array_keys(is_offense_type()))
	) {
		return false;
	}
	$ignorecnt = $acceptcnt = $rejectcnt = 0;
	foreach ($_POST['ACT'] as $prohibid => $action) {
		switch ($action) {
		case 'accept':
			$accepts[] = $prohibid;
			++$acceptcnt;
			break;
		case 'reject':
			$rejects[] = $prohibid;
			++$rejectcnt;
			break;
		case 'ignore':
			++$ignorecnt;
			break;
		}
	}
	if ($ignorecnt) {
		register_notice('prohib:notice:ignored_LINE',array('CNT'=>$ignorecnt));
	}
	if (isset($rejects)) {
		$range = '('.implode(',',$rejects).')';
		foreach ($rejects as $prohibid) {
			$dels[] = '("prohibited",'.$prohibid.','.CURRENTUSERID.','.CURRENTSTAMP.')';
		}
		if (!db_insert('deleted','
			INSERT INTO deleted (ELEMENT,ID,DUSERID,DSTAMP)
			VALUES '.implode(',',$dels))
		) {
			return false;
		}
		foreach (array(
			'prohibited_nick',
			'prohibited_image',
			'prohibited_crclen',
			'prohibited'
		) as $table) {
			if (!db_insert($table.'_log','
				INSERT INTO '.$table.'_log
				SELECT * FROM '.$table.'
				WHERE PROHIBID IN '.$range)
			||	!db_delete($table,'
				DELETE FROM '.$table.'
				WHERE PROHIBID IN '.$range)
			) {
				return false;
			}
		}
		register_notice('prohib:notice:rejected_LINE',array('CNT'=>$rejectcnt));
	}
	if (isset($accepts)) {
		$range = '('.implode(',',$accepts).')';
		foreach ($accepts as $prohibid) {
			if (!isset($_POST['OFFENSE_TYPE'][$prohibid])) {
				register_error('form:error:invalid_data_LINE');
				return false;
			}
			$cases[] = 'WHEN '.$prohibid.' THEN "'.addslashes($_POST['OFFENSE_TYPE'][$prohibid]).'"';
		}
		if (!db_insert('prohibited_log','
			INSERT INTO prohibited_log
			SELECT * FROM prohibited
			WHERE PROHIBID IN '.$range)
		||	!db_update('prohibited','
			UPDATE prohibited SET
				STATUS		="active",
				OFFENSE_TYPE	=CASE PROHIBID '.implode(' ',$cases).' END,
				MUSERID		='.CURRENTUSERID.',
				MSTAMP		='.CURRENTSTAMP.'
			WHERE PROHIBID IN '.$range)
		) {
			return false;
		}
		register_notice('prohib:notice:accepted_LINE',array('CNT'=>$acceptcnt));
	}
	return true;
}

function prohib_display_list(): void {
	if (!require_admin('prohib')
	||	!require_element($_REQUEST, 'ACTION', ['active', 'inactive'])
	) {
		return;
	}
	layout_section_header(Eelement_plural_name('inactive_prohibition'));
	if (!obtain_lock(LOCK_PROHIB_PROPOSALS)) {
		?><div class="block"><?
		display_lockinfo(LOCK_PROHIB_PROPOSALS);
		?></div><?
		return;
	}
	$status = $_REQUEST['ACTION'];
	$active = $status === 'active';
	if ($active) {
		$startid = 0+have_idnumber($_REQUEST,'STARTID');
		if (!$startid) {
			$startid = 1+db_single('prohibited_checked','SELECT MAX(STOPID) FROM prohibited_checked WHERE USERID='.CURRENTUSERID);
		}
	}
	$prohibs = db_rowuse_hash(
		array('prohibited','prohibited_nick','prohibited_image','offense'),'
		SELECT p.PROHIBID,p_n.*,p_i.*,p.*,offense.USERID AS OFFENDERID,offense.EXTRA
		FROM prohibited AS p
		LEFT JOIN prohibited_nick  AS p_n ON TYPE="nick" AND p.PROHIBID=p_n.PROHIBID
		LEFT JOIN prohibited_image AS p_i ON TYPE="image" AND p.PROHIBID=p_i.PROHIBID
		LEFT JOIN offense ON p.OFFENSEID=offense.OFFENSEID
		WHERE STATUS="'.$status.'"'.
		(	$active
		?	' AND p.PROHIBID>='.$startid.' ORDER BY p.PROHIBID ASC LIMIT 100'
		:	' ORDER BY TYPE ASC,OFFENSE_TYPE ASC,p.PROHIBID ASC'
		)
	);
	if ($prohibs === false) {
		return;
	}
	prohib_menu();
	if (!$prohibs) {
		?><div class="block"><?=
			$active
		?	__('prohib:info:no_actives_left_LINE')
		:	__('prohib:info:no_inactives_LINE')
		?></div><?
		if (!$active) {
			return;
		}
	}
	if ($active) {
		require_once '_sort.inc';
		doublestring_asort($prohibs,'TYPE','OFFENSE_TYPE');
	}
	require_once 'style/_coloradjust.inc';
	$GLOBALS['LITE'] = LITE;

	?><style>.reject { background-color: <?= diff_color(0xEEE4E4) ?> }</style><?
	?><script>
	/**
	 * @param {HTMLSelectElement} selectobj
	 * @param {integer} prohibid
	 */
	function changeAction(selectobj, prohibid) {
		const rightdiv = selectobj.parentNode.parentNode.parentNode.parentNode;
		const maindiv = rightdiv.parentNode;
		switch (selectobj.selectedIndex) {
		case 0:	 maindiv.className = 'block';
				rightdiv.className = '';
				break;
		case 1:	 maindiv.className = 'light block';
				rightdiv.className = 'reject';
				break;
		case 2:	 maindiv.className = 'light block';
				rightdiv.className = '';
				break;
		}
		const offense_type = getobj(`offense_type_${prohibid}`, true);
//		if (offense_type) {
//			offense_type.disabled = selectobj.selectedIndex > 0;
//		}
	}
	</script><?
	require_once '_offense_types.inc';
	?><form method="post" action="/prohib/<?= $status ?>/process" onsubmit="return submitForm(this)"><?
	if ($active) {
		?><input type="hidden" name="STARTID" value="<?= $startid ?>" /><?
	}
	$first = true;
	foreach ($prohibs as $prohibid => $prohib) {
		if ($first) {
			$first = false;
			layout_open_box('white');
		} else {
			?><hr class="slim clear"><?
		}
		?><div class="block"><?
		?><div style="float:left;width:200px;margin-right:1em"><?
		switch ($prohib['TYPE']) {
		case 'image':
			$image = db_single_assoc(
				array('prohibited_image','image_reference_meta'),'
				SELECT prohibited_image.DATAID,THMBID,WIDTH,HEIGHT,FILETYPE
				FROM prohibited_image
				JOIN image_reference_meta ON prohibited_image.THMBID=image_reference_meta.DATAID
				WHERE PROHIBID='.$prohibid
			);
			if ($image) {
				?><a href="/images/reference/<?= $image['DATAID'] ?>.<?= $image['FILETYPE'] ?>" target="_blank"><?
				?><img style="width:100%;max-width:200px<?
				if ($image['WIDTH'] <= 200) {
					?>;height:<?= $image['HEIGHT'] ?>px<?
				}
				?>" src="/images/reference/<?= $image['THMBID'] ?>.<?= $image['FILETYPE'] ?>"><?
				?></a><?
			}
			break;
		case 'nick':
			echo escape_specials($prohib['NICK']);
			break;
		}
		?></div><?
		layout_open_table(TABLE_CLEAN);
		layout_start_row();
		echo Eelement_name('type');
		layout_field_value();
		echo element_name($prohib['TYPE']);

		layout_restart_row();
		?><label for="status"><?= Eelement_name('status') ?></label><?
		layout_field_value();
		if (!$active) {
			?><select id="status" name="ACT[<?= $prohibid ?>]" onchange="changeAction(this, <?= $prohibid ?>)"><?
				?><option value="accept"><?= __('action:accept') ?></option><?
				?><option value="reject"><?= __('action:reject') ?></option><?
				?><option value="ignore"><?= __('action:ignore') ?></option><?
			?></select><?
		} else {
			require_once '_status.inc';
			?><select id="status" name="STATUS[<?= $prohibid ?>]"><?
			foreach (['active', 'deleted'] as $status) {
				?><option<?
				if ($prohib['STATUS'] === $status) {
					?> selected<?
				}
				?> value="<?= $status ?>"><?= status_name($status) ?></option><?
			}
			?></select><?
		}
		layout_restart_row();
		echo Eelement_name('date');
		layout_field_value();
		_datedaytime_display($prohib['CSTAMP']);

		if ($prohib['CUSERID']) {
			layout_restart_row();
			echo Eelement_name('officer');
			layout_field_value();
			echo get_element_link('user',$prohib['CUSERID']);
		}
		if ($prohib['EXTRA']) {
			layout_restart_row();
			echo Eelement_name('explanation');
			layout_field_value();
			echo make_all_html($prohib['EXTRA']);
		}
		if ($prohib['OFFENDERID']) {
			layout_restart_row();
			echo Eelement_name('offender');
			layout_field_value();
			echo get_element_link('user',$prohib['OFFENDERID']);
		}
		if ($prohib['OFFENSEID']) {
			layout_restart_row();
			echo Eelement_name('offense');
			layout_field_value();
			?><a href="/offense/<?= $prohib['OFFENSEID'] ?>"><?= $prohib['OFFENSEID'] ?></a><?
		}
		layout_restart_row();
		echo Eelement_name('offense');
		layout_field_value();
		?><select name="OFFENSE_TYPE[<?= $prohibid ?>]" id="offense_type_<?= $prohibid ?>"><?
			show_offense_type_for_prohib_options($prohib['OFFENSE_TYPE']);
		?></select><?

		layout_close_table();
		?></div><?
	}
	if (!$first) {
		layout_close_box();
	}
	?><div class="block"><input type="submit" value="<?= __('action:process') ?>" /></div><?
	?></form><?
}
function prohib_display_type(): void {
	if (!require_element($_REQUEST, 'ACTION', ['nick', 'image', 'obsolete'])) {
		return;
	}
	layout_show_section_header();
	prohib_menu();
	$type = $_REQUEST['ACTION'];
	if (!($prohibs = db_rowuse_array("prohibited_$type", "
		SELECT PROHIBID, STATUS, p_x.*
		FROM prohibited
		JOIN prohibited_$type AS p_x USING (PROHIBID)
		WHERE prohibited.TYPE = '$type'"))
	) {
		return;
	}
	layout_open_box('white');
	?><div class="block"><?
	foreach ($prohibs as $prohib) {
		?><a<?
		if ($prohib['STATUS'] === 'inactive') {
			?> class="light"<?
		} elseif ($prohib['STATUS'] === 'deleted') {
			?> class="unavailable"<?
		}
		?> href="/prohib/<?= $prohib['PROHIBID'] ?>/form"><?
		if ($type === 'image') {
			echo $prohib['DATAID'],':',$prohib['THMBID'];
		} elseif ($type === 'nick') {
			echo escape_specials($prohib['NICK']);
		} elseif ($type === 'obsolete') {
			echo escape_specials($prohib['BODY']);
		}
		?></a><br /><?
	}
	?></div><?
	layout_close_box();
}
function prohib_display_overview(): void {
	if (!require_admin('prohib')) {
		return;
	}
	if ($_REQUEST['ACTION']) {
		prohib_display_type();
		return;
	}
	layout_show_section_header();
	prohib_menu();

	if (!($prohibs = db_rowuse_array('prohibited', "
		SELECT TYPE,COUNT(*) AS TOTAL_CNT, COUNT(IF(STATUS = 'active', 1, NULL)) AS ACTIVE_CNT
		FROM prohibited
		GROUP BY TYPE"))
	) {
		if ($prohibs !== false) {
			?><div class="block"><?= __C('prohib:info:no_prohibs_LINE') ?></div><?
		}
		return;
	}
	require_once '_status.inc';
	layout_open_box('white');
	layout_open_table();
	layout_start_header_row();
	layout_header_cell(Eelement_name('type'));
	layout_header_cell(Eelement_name('total'));
	layout_header_cell(__C('status:active'));
	layout_stop_header_row();
	foreach ($prohibs as $prohib) {
		layout_start_row();
		if ($prohib['TYPE'] === 'nick') {
			?><a href="/prohib/<?= $prohib['TYPE'] ?>"><?= Eelement_name($prohib['TYPE']) ?></a><?
		} else {
			echo Eelement_name($prohib['TYPE']);
		}
		layout_next_cell(class: 'right');
		echo $prohib['TOTAL_CNT'];
		layout_next_cell(class: 'right');
		echo $prohib['ACTIVE_CNT'];
		layout_stop_row();
	}
	layout_close_table();
	layout_close_box();
}
function prohib_display_single_or_form() {
	layout_show_section_header();

	$form = ($_REQUEST['ACTION'] === 'form' || $_REQUEST['ACTION'] === 'commit');

	if (!($prohibid = require_idnumber($_REQUEST,'sID'))
	||	!require_user()
	) {
		return;
	}
	if (!($admin = have_admin($form ? 'prohib' : null))) {
		if (!db_single('prohib_encounter','SELECT 1 FROM prohib_encounter WHERE USERID='.CURRENTUSERID.' AND PROHIBID='.$prohibid)) {
			require_admin();
			return;
		}
	}
	$prohib = db_single_assoc(
		array('prohibited','offense'),'
		SELECT p.*,o.EXTRA,o.ELEMENT,o.ID
		FROM prohibited AS p
		LEFT JOIN offense AS o USING (OFFENSEID)
		WHERE p.PROHIBID='.$prohibid
	);
	if ($prohib === false) return;
	if (!$prohib) {
		register_error('prohib:error:nonexistent_LINE',array('ID'=>$prohibid));
		return;
	}
	if ($form) {
		?><form onsubmit="return submitForm(this)" method="post" action="/prohib/<?= $prohibid ?>/commit"><?
		?><input type="hidden" name="TYPE" value="<?= $prohib['TYPE'] ?>" /><?
	}
	?><article itemscope itemtype="https://schema.org/Article"><?
	layout_open_box('prohib');
	layout_open_table(TABLE_CLEAN | TABLE_VTOP);
	layout_start_row();
	echo Eelement_name('content');
	layout_field_value();
	switch ($prohib['TYPE']) {
	case 'image':
		$image = db_single_assoc(
			array('prohibited_image','image_reference_meta'),$qstr = '
			SELECT meta.DATAID,WIDTH,HEIGHT,FILETYPE
			FROM prohibited_image AS img
			JOIN image_reference_meta AS meta ON (meta.DATAID=img.DATAID OR meta.DATAID=img.THMBID)
			WHERE PROHIBID='.$prohibid.'
			ORDER BY img.DATAID=meta.DATAID DESC
			LIMIT 1'
		);
		if ($image) {
			?><img src="/images/reference/<?= $image['DATAID'] ?>.<?= $image['FILETYPE']
			?>" width="<?= $image['WIDTH'] ?>" height="<?= $image['HEIGHT'] ?>" /><?
		} else {
			$crclens = db_rowuse_array('prohibited_crclen','
				SELECT CRC,LEN
				FROM prohibited_crclen
				WHERE PROHIBID='.$prohibid
			);
			if ($crclens) {
				foreach ($crclens as $crclen) {
					$pairs[] = '(crc = '.$crclen['CRC'].',len = '.$crclen['LEN'].')';
				}
				echo implode(', ',$pairs);
			}
		}
		break;
	case 'nick':
		$nick = db_single('prohibited_nick','SELECT NICK FROM prohibited_nick WHERE PROHIBID='.$prohibid);
		if ($nick) {
			echo escape_specials($nick);
		}
		break;
	case 'obsolete':
		$obsolete = db_single_assoc('prohibited_obsolete','SELECT * FROM prohibited_obsolete WHERE PROHIBID='.$prohibid);
		if ($obsolete) {
			?><pre><?
			ob_start();
			print_r($obsolete);
			echo escape_specials(ob_get_clean());
			?></pre><?
		}
		break;
	}
	if (!$admin) {
		layout_restart_row();
		echo __C('field:proposed_by');
		layout_field_value();
		echo get_element_link('user',$prohib['CUSERID']);
	}
	if ($prohib['EXTRA']) {
		layout_restart_row();
		echo Eelement_name('explanation');
		layout_field_value();
		echo make_all_html($prohib['EXTRA']);
	}

	layout_restart_row();
	echo Eelement_name('status');
	layout_field_value();
	require_once '_status.inc';
	if ($form) {
		?><select name="STATUS"><?
			foreach (array('active','inactive','deleted') as $status) {
				?><option<?
				if ($prohib['STATUS'] == $status) {
					?> selected="selected"<?
				}
				?> value="<?= $status ?>"><?= status_name($status) ?></option><?
			}
		?></select><?
	} else {
		echo status_name($prohib['STATUS']);
	}

	layout_restart_row();
	echo Eelement_name('type');
	layout_field_value();
	if ($form) {
		require_once '_offense_types.inc';
		?><select name="OFFENSE_TYPE"><?
		$types = show_offense_type_for_prohib_options($prohib['OFFENSE_TYPE']);
		?></select><?
	} else {
		echo __('offense:type:'.$prohib['OFFENSE_TYPE']);
	}

	$offense = db_single_assoc(array('prohibited','offense'),
		'SELECT OFFENSEID,USERID FROM prohibited JOIN offense USING (OFFENSEID) WHERE prohibited.PROHIBID='.$prohibid);
	if ($offense
	&&	(	$offense['USERID'] == CURRENTUSERID
		||	have_offense_admin()
		)
	) {
		$offenseid = $offense['OFFENSEID'];
		layout_restart_row();
		echo Eelement_name('source');
		layout_field_value();
		?><a href="/offense/<?= $offenseid ?>"><?= $offenseid ?></a><?
	}

	layout_stop_row();
	layout_close_table();
	layout_display_alteration_note($prohib);
	layout_close_box();
	if ($form) {
		?><div class="block"><input type="submit" value="<?= __('action:change'); ?>" /></div><?
		?></form><?
	}
	?></article><?
}
function prohib_commit(): bool {
	if (!($prohibid = require_idnumber($_REQUEST,'sID'))
	||	!require_admin('prohib')
	||	!require_something($_POST, 'OFFENSE_TYPE')
	||	!require_element($_POST, 'STATUS', ['active', 'inactive', 'deleted'])
		||	!db_insert('prohibited_log','
		INSERT INTO prohibited_log
		SELECT * FROM prohibited
		WHERE PROHIBID='.$prohibid)
	||	!db_update('prohibited','
		UPDATE prohibited SET
			OFFENSE_TYPE	="'.addslashes($_POST['OFFENSE_TYPE']).'",
			STATUS		="'.$_POST['STATUS'].'",
			MUSERID		='.CURRENTUSERID.',
			MSTAMP		='.CURRENTSTAMP.'
		WHERE PROHIBID='.$prohibid)
	) {
		return false;
	}
	register_notice('prohib:notice:changed_LINE');
	return true;
}
