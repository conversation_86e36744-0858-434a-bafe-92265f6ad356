<?php

require_once '_browser.inc';
require_once '_ubb.inc';

# NOTE: 0xA9 == copyright';
#	0x99 == trademark, utf8: "\xE2\x84\xA2"

/*define('ARE_DELIMITERS',	FIREFOX ? '\s\t\x1A'
			: (	WEBKIT	? '\s\t\-'//.chr(0xAD)
			:		  '\s\t\?\-\!'//.chr(0xAD)
			));
define('MAKE_DELIMITERS',	FIREFOX	? '\.,;\-_\!\?\*#"\|\/\^=\+$&@\'\\\\'//.chr(0xAD)
			: (	WEBKIT	? '\.;_@&$#\+=\^#\*"\!\/'
			:		  '\.;_@&$#\+=\^#\*"'));*/

function wrap_get_shy($flags = 0) {
	return $flags & WRAP_UTF8 ? "\x{C2AD}" : "\xAD";
}
function wrap_are_delimiters($flags = 0) {
	$shy = wrap_get_shy($flags);
	return	FIREFOX ? '\s\t\x1A'
		: (	WEBKIT	? '\s\t\-'.$shy
		:		  '\s\t\?\-\!'.$shy
		);
}
function wrap_make_delimiters($flags = 0) {
	return	FIREFOX	? '\.,;\-_\!\?\*#"\|\/\^=\+$&@\'\\\\'.wrap_get_shy($flags)
		: (	WEBKIT	? '\.;_@&$#\+=\^#\*"\!\/'
		:			  '\.;_@&$#\+=\^#\*"'
	);
}

# FIXME: best operation would be to prepare the entire string by prepping smileys and ubb, and then skipping over those
#	  and wrapping content only

const WRAP_SAVE_SMILEYS = 0x1;
const WRAP_SAVE_UBB = 0x2;
const WRAP_SAVE_ALL = 0x3;
const WRAP_UTF8 = 0x4;
const WRAP_FINALIZE = 0x8;

function wrap_safe($arg1, $arg2 = null, $arg3 = null) {
	require_once '_smiley.inc';
	if (is_string($arg1)) {
		$text = $arg1;
		$maxlen = $arg2;
		$flags = is_number($arg3) ? $arg3 : 0;
	} else {
		$flags = $arg1;
		$text = $arg2;
		$maxlen = $arg3;
	}
	if (!$maxlen) {
		$maxlen = 50;
	}

	$utf8_in = $flags & WRAP_UTF8;
	$utf8_mod = $utf8_in ? 'u' : '';

	if (str_contains($text, UBB_BEGIN)) {
		$text = internal_wrap_with_skip($text,'"('.UBB_BEGIN.'.*?'.UBB_END.'|'.SMILEY_START.'.*?'.SMILEY_SEP.'.*?'.SMILEY_END.')"Ss'.$utf8_mod,$maxlen,$flags);
	} elseif (str_contains($text,SMILEY_START)) {
		$text = internal_wrap_with_skip($text,'"('.SMILEY_START.'.*?'.SMILEY_SEP.'.*?'.SMILEY_END.')"Ss'.$utf8_mod,$maxlen,$flags);
	} else {
		$text = wrap_text($text,$maxlen,12,$flags);
		if ($flags & WRAP_FINALIZE) {
			$text = nl2br(escape_specials($text,$utf8_in));
		}
	}
	if ($flags & WRAP_FINALIZE) {
		$text = str_replace(
			($flags & WRAP_UTF8 ? "\xC2\xAD" : chr(0xAD)),
			($flags & WRAP_UTF8 ? "\xC2\xAD" : chr(0xAD)).'<wbr>',
			$text
		);
	}
	return $text;
}
function internal_wrap_with_skip(string $text,string $skipregex,int $maxlen,int $flags): string {
	$res = '';
	$utf8_mod = $flags & WRAP_UTF8 ? 'u' : '';
	foreach (preg_split($skipregex, $text, -1, PREG_SPLIT_DELIM_CAPTURE | PREG_SPLIT_NO_EMPTY) as $part) {
		if ($part[0] == SMILEY_START
		||	$part[0] == UBB_BEGIN
		) {
			$res .= $part;
		} else {
			$len = call_user_func($utf8_mod ? 'mb_strlen' : 'strlen', $part);

			$extr =	$len > $maxlen
			&&	preg_match('"[^'.wrap_are_delimiters($flags).']{'.$maxlen.',}"s'.$utf8_mod,$part)
			?	wrap_text($part, $maxlen, 12, $flags)
			:	$part;

			$res .=	$flags & WRAP_FINALIZE
			?	nl2br(escape_specials($extr, $flags & WRAP_UTF8))
			:	$extr;
		}
	}
	return $res;
}

function wrap_text(
	string $text,
	int $maxlen = 50,
	int $ins = 24,
	int $flags = 0
): string {
	$utf8_mod = $flags & WRAP_UTF8 ? 'u' : '';
	$realshy = $utf8_mod ? "\xC2\xAD" : chr(0xAD);
	$text = preg_replace('/(['.wrap_make_delimiters($flags).']'.(FIREFOX ? '{1,6}' : '+').')/'.$utf8_mod,'\\1'.$realshy,$text);
	$result = '';
	foreach (preg_split('"([^'.($are_delims = wrap_are_delimiters($flags)).']{'.$maxlen.',})"s'.$utf8_mod,$text,-1,PREG_SPLIT_DELIM_CAPTURE | PREG_SPLIT_NO_EMPTY) as $word) {
		if (FIREFOX) {
			$word = str_replace(chr(0x1A),'',$word);
		}
		$len = $utf8_mod ? mb_strlen($word) : strlen($word);
		if ($len < $maxlen
		||	preg_match('"['.$are_delims.']"'.$utf8_mod,$word)
		) {
			$result .= $word;
			continue;
		}
		for ($cnt = 0; $cnt + $ins < $len; $cnt += $ins) {
			$result .= call_user_func($utf8_mod ? 'mb_substr' : 'substr', $word, $cnt, $ins).$realshy;
		}
		$result .= call_user_func($utf8_mod ? 'mb_substr' : 'substr', $word, $cnt, $ins);
	}
	return $result;
}

function wrap_nick(int $userid, ?string $text = null, int $flags = 0): string {
	static $__oknick;
	if (isset($__oknick[$userid])) {
		return $__oknick[$userid];
	}
	if (!$text) {
		$text = get_element_title('user',$userid);
		if (!$text) {
			# nick might be empty due to privacy reasons
			return '';
		}
	}
	if (strlen($text) < 13) {
		$__oknick[$userid] = $text;
		return $text;
	}
	return $__oknick[$userid] = wrap_text($text,12,6,$flags);
}

function replace_shy(string $text, bool $utf8 = false): string {
	return	FIREFOX
	&&	($text && str_contains($text, $srch = $utf8 ? "\xC2\xAD" : chr(0xAD)))
	?	str_replace($srch, '<wbr>', $text)
	:	$text;
}
