<?php

declare(strict_types=1);

const CITY_VALID					= 0;
const CITY_INVALID_NONSENSE			= 1;
const CITY_INVALID_MORE_THAN_ONE	= 2;
const CITY_INVALID_VAGUE			= 3;
const CITY_INVALID_IS_COUNTRY		= 4; # FREE!
//const CITY_INVALID_ZIPCODE_MISMATCH	= 5;
const CITY_INVALID_IP_MISMATCH		= 6;
const CITY_INVALID_DONT_UNDERSTAND	= 7;
const CITY_INVALID_MAKE_CONTACT		= 8;
const CITY_INVALID_IS_PROVINCE		= 9;
const CITY_INVALID_TEMPORARY		= 10;
//const CITY_INVALID_ZIPCODE			= 11;
const CITY_INVALID_IS_MUNICIPALITY	= 12;
//const CITY_INVALID_FILL_ZIPCODE		= 13;
const CITY_INVALID_IS_ADDRESS		= 14;
const CITY_INVALID_IS_CITYPART		= 15;
const CITY_INVALID_IS_TEMPORARY		= 16;
//const CITY_INVALID_IS_HOLIDAY		= 17;
const CITY_INVALID_IS_FUTURE		= 18;
const CITY_INVALID_NEED_MORE_INFO	= 19;
const CITY_CHECK					= 20;
const CITY_INVALID_LAST				= 20;

const COUNTRIES_REQUIRING_PROVINCE	= '1';	# '1,2,6'

function require_valid_invalid_city_reason(array &$arr, string $field): bool {
	require_once '_error.inc';
	if (false === require_number($arr, $field)) {
		return false;
	}
	if ($arr[$field] > CITY_INVALID_LAST
	||	in_array($arr[$field], [
			CITY_INVALID_IS_COUNTRY,
			CITY_INVALID_IS_CITYPART,
			CITY_INVALID_IS_TEMPORARY], true)
	) {
		register_error('cityinvalid:error:invalid_cityinvalid_LINE', ['NAME' => $field, 'VALUE' => $arr[$field]]);
		return false;
	}
	return true;
}

function city_show_invalid_reason(int $type): void {
	static	$__invalid_reason = [];
	echo	$__invalid_reason[$type]
	??=	__('cityinvalid:'.$type);
}

function flush_city_stuff_on_user_change(): void {
	memcached_delete('user_invalid_city_cnt');
}

function flush_city_stuff_on_city_change(): void {
	memcached_delete('city_no_province_cnt', 'city_no_lonlat_cnt');
}

/**
 * @return array{
 *      user_invalid_city_cnt:	int|false,
 *      city_no_lonlat_cnt: 	int|false,
 *      city_no_province_cnt:	int|false
 *  }
 */
function get_city_attention_counts(): array {
	static $__city_need_attention = [
		'user_invalid_city_cnt' => memcached_single_int(['user', 'user_account'], "
			SELECT COUNT(*)
			FROM user FORCE KEY (CITY)
			JOIN user_account FORCE KEY (USERID) USING (USERID)
			WHERE STATUS = 'active'
			  AND CITY IS NOT NULL
			  AND INVALID_CITY = ".CITY_CHECK,
		/*	SELECT COUNT(*)
			FROM user_account
			WHERE STATUS = "active"
			  AND USERID IN ('.(
				memcached_single_string('user', '
					SELECT GROUP_CONCAT(USERID ORDER BY USERID)
					FROM user
					WHERE CITY IS NOT NULL
					  AND INVALID_CITY = '.CITY_CHECK,
					TEN_MINUTES,
					'user_invalid_city_list') ?: '0').
			')',*/
			TEN_MINUTES,
			'user_invalid_city_cnt'),

		'city_no_lonlat_cnt' => memcached_single_int(['city', 'country'], '
			SELECT COUNT(*)
			FROM city
			JOIN country USING (COUNTRYID)
			WHERE (LONGITUDE = 0 OR LATITUDE = 0)',
			TEN_MINUTES,
			'city_no_lonlat_cnt'),

		'city_no_province_cnt' => memcached_single_int('city','
			SELECT COUNT(*)
			FROM city
			WHERE COUNTRYID IN ('.COUNTRIES_REQUIRING_PROVINCE.')
			  AND PROVINCEID = 0',
			TEN_MINUTES,
			'city_no_province_cnt')
	];
	return $__city_need_attention;
}
