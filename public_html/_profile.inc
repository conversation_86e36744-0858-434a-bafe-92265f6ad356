<?php

require_once '_genres.inc';
require_once '_memcache.inc';
require_once '_spider.inc';

function profile_allows_erotic(string $who, int|string $id): ?string {
	if (!($profile_info = db_single_assoc("profile_for_$who", "
		SELECT EROTIC, EROTIC_TMP
		FROM profile_for_$who
		WHERE ID = $id"
	))) {
		return null;
	}
	if ($profile_info['EROTIC']) {
		return 'full';
	}
	if ($profile_info['EROTIC_TMP']) {
		return 'tmp';
	}
	return null;
}

function current_profile(): array|false {
	if (ROBOT) {
		return [];
	}
	# only argument supported: user, with id = USERID
	if (CURRENTUSERID > 1) {
		$who = 'user';
		$id = CURRENTUSERID;
	} elseif (CURRENTIDENTID) {
		$who = 'identity';
		$id = CURRENTIDENTID;
	} else {
		$who = 'ip';
		$id = '"'.addslashes(CURRENTIPBIN).'"';
	}
	static $__profile;
	if ( isset($__profile[$who][$id])) {
		return $__profile[$who][$id];
	}
	$profile = [];
	if ($erotic = profile_allows_erotic($who, $id)) {
		$profile['erotic'] = $erotic;
	}
	if ($genres = db_simple_hash("profile_genres_for_$who", "
		SELECT MGID, TYPE
		FROM profile_genres_for_$who
		WHERE ID = $id
		ORDER BY TYPE = 'full' ASC"
	)) {
		$profile['genres'] = $genres;
	}
	if ($subgenres = db_simple_hash("profile_subgenres_for_$who", "
		SELECT GID, TYPE
		FROM profile_subgenres_for_$who
		WHERE ID = $id
		ORDER BY TYPE = 'full' ASC"
	)) {
		$profile['subgenres'] = $subgenres;
	}

	require_once '_geo.inc';
	if (!empty(($geo = get_geolocation_cache())[0])) {
		$profile['positions'][] = [$geo[0], $geo[1], 'geo'];
	}
	if (defined('CURRENTUSERID')
	&&	CURRENTUSERID > 1
	&&	($current_city = get_current_city())
	) {
		$profile['positions'][] = [$current_city['LATITUDE'], $current_city['LONGITUDE'], 'set'];
	}

	if ($poss = db_rowuse_array("profile_positions_for_$who", "
		SELECT LATITUDE, LONGITUDE, TYPE
		FROM profile_positions_for_$who
		WHERE ID = $id
		ORDER BY TYPE = 'full'",
		DB_NON_ASSOC
	)) {
		$profile['positions'] = [...$profile['positions'] ?? [], ...$poss];
	}
	if (false === ($page_profile = page_profile())) {
		return false;
	}
	$profile += $page_profile;
	return $__profile[$who][$id] = $profile;
}

function page_profile(): array|false {
	if (ROBOT) {
		return [];
	}
	static $__profile;
	if (!($element = $_REQUEST['sELEMENT'])
	||	!($id 	   = $_REQUEST['sID'])
	||	$element === 'user' # ignore user: not indiciative of visitor intereset in genres and pos of user
	) {
		return $__profile[$element][$id] = [];
	}
	if (isset($__profile[$element][$id])) {
		return $__profile[$element][$id];
	}
	if (!($element_profile = element_profile($element, $id))) {
		return $__profile[$element][$id] = [];
	}

	$profile = [];

	if (!empty($element_profile['master_genres'])) {
		$profile['page_genres'] = $element_profile['master_genres'];
	}
	if (!empty($element_profile['genres'])) {
		$profile['page_subgenres'] = $element_profile['genres'];
	}
	if (!empty($element_profile['positions'])) {
		$profile['page_positions'] = $element_profile['positions'];
	}
	if (!empty($element_profile['erotic'])) {
		$profile['page_erotic'] = true;
	}

	$updlist = [];
	if (!empty($profile['page_erotic'])) {
		$updlist['EROTIC_TMP = 1'] = 'EROTIC_TMP = LEAST(EROTIC_TMP + 1, 255)';
	}

	if ($updlist
	||	!empty($profile['page_subgenres'])
	||	!empty($profile['page_genres'])
	||	!empty($profile['page_positions'])
	) {
		$stores = [];
		if (CURRENTUSERID > 1) {
			$stores['user'] = CURRENTUSERID;
		}
		if (CURRENTIDENTID) {
			$stores['identity'] = CURRENTIDENTID;
		}
		$stores['ip'] = '"'.addslashes(CURRENTIPBIN).'"';

		if (!empty($profile['page_genres'])
		||	!empty($profile['page_positions'])
		) {
			$setlists = [];
			foreach ($stores as $store_who => $store_whoid) {
				if (!empty($profile['page_genres'])) {
					foreach ($profile['page_genres'] as $mgid => $true) {
						$setlists['genres'][$store_who][] = "('tmp', $store_whoid, $mgid, 1)";
					}
				}
				if (!empty($profile['page_subgenres'])) {
					foreach ($profile['page_subgenres'] as $gid => $true) {
						$setlists['subgenres'][$store_who][] = "('tmp', $store_whoid, $gid, 1)";
					}
				}
				if (!empty($profile['page_positions'])) {
					foreach ($profile['page_positions'] as [$latitude, $longitude]) {
						$setlists['positions'][$store_who][] = "('tmp', $store_whoid, $latitude, $longitude, 1)";
					}
				}
			}
			static $__vals = [
				'subgenres'	=> '(TYPE, ID, GID, WORTH)',
				'genres'	=> '(TYPE, ID, MGID, WORTH)',
				'positions'	=> '(TYPE, ID, LATITUDE, LONGITUDE, WORTH)',
			];
			foreach ($setlists as $who => $whos) {
				foreach ($whos as $store_who => $setlist) {
					if (!db_insupd("profile_{$who}_for_$store_who", "
						INSERT INTO profile_{$who}_for_$store_who $__vals[$who]
							VALUES ".implode(',', $setlist).'
						ON DUPLICATE KEY UPDATE
							WORTH = WORTH + 1')
					) {
						return false;
					}
				}
			}
		}
		if ($updlist) {
			foreach ($stores as $store_who => $store_whoid) {
				if (!db_insupd("profile_for_$store_who", "
					INSERT INTO profile_for_$store_who SET
						ID	= $store_whoid, ".
						implodekeys(', ', $updlist).'
					ON DUPLICATE KEY UPDATE '.
						implode(', ', $updlist))
				) {
					return false;
				}
			}
		}
	}
	return $__profile[$element][$id] = $profile;
}

function clear_element_profile(string $element, int|array $id): void {
	if (is_array($idstr = $id)) {
		$ids = $id;
		sort($ids);
		$idstr = implode(',', $ids);
	}
	$key = "vprof_v5_$element:$idstr";
	memcached_delete($key);
}

/**
 * @return array{
 *     genres?:			array<int,bool>,
 *     positions?:		array<array{float,float}>,
 *     erotic?:			bool,
 *     master_genres?:	array<int,bool>,
 * }|false
 */
function element_profile(string $element, int|array $id): array|false {
	if (ROBOT) {
		return [];
	}
	static $__profile = [];
	if (is_array($idstr = $id)) {
		$ids = $id;
		sort($ids);
		$idstr = implode(',', $ids);
	} else {
		$ids = [$id];
	}
	if (isset($__profile[$element][$idstr])) {
		return $__profile[$element][$idstr];
	}

	$key = "vprof_v5_$element:$idstr";
	if (!isset($_REQUEST['NOMEMCACHE'])
	&&	($profile = memcached_get($key))
	) {
		return $__profile[$element][$idstr] = $profile;
	}

	require_once '_helper.inc';
	if (have_page_problem()) {
		# no profile for error pages
		return [];
	}

	$profile = [];

	switch ($element) {
	case 'artist':
		if (false === ($genres = db_same_hash('artist_genre',"
			SELECT DISTINCT GID
			FROM artist_genre
			WHERE ARTISTID IN ($idstr)"
		))) {
			return false;
		}
		if ($genres) {
			$profile['genres'] = $genres;
		}
		break;

	case 'gallery':
		# Add check on PARTYID being non-zero, because PARTYID=0 is not useful
		if (false === ($partyids = db_simpler_array('gallery', "
			SELECT PARTYID
			FROM gallery
			WHERE PARTYID
			  AND GALLERYID IN ($idstr)"
		))) {
			return false;
		}
		if ($partyids) {
			return element_profile('party', $partyids);
		}
		break;

	case 'photo':
		# Add check on PARTYID being non-zero, because PARTYID=0 is not useful
		if (false === ($partyids = db_simpler_array('image', "
			SELECT PARTYID
			FROM image
			WHERE PARTYID
			  AND IMGID IN ($idstr)"
		))) {
			return false;
		}
		if ($partyids) {
			return element_profile('party', $partyids);
		}
		break;

	case 'interview':
	case 'review':
		# Add check on ASSOCID being non-zero, because ASSOCID=0 is not useful
		if (false === ($artistids = db_simpler_array('connect', "
			SELECT ASSOCID
			FROM connect
			WHERE MAINTYPE = '$element'
			  AND MAINID IN ($idstr)
			  AND ASSOCTYPE = 'artist'
			  AND ASSOCID"
		))) {
			return false;
		}
		if ($artistids) {
			return element_profile('artist', $artistids);
		}
		break;

	case 'location':
		iF (false === ($genres = db_same_hash('location_genre', "
			SELECT GID
			FROM location_genre
			WHERE LOCATIONID IN ($idstr)"
		))) {
			return false;
		}
		if ($genres) {
			$profile['genres'] = $genres;
		}
		if (false === ($location = memcached_single_assoc(['location', 'city'], "
			SELECT	location.LATITUDE, location.LONGITUDE, EROTIC,
					city.LATITUDE AS CITY_LATITUDE, city.LONGITUDE AS CITY_LONGITUDE
			FROM location
			LEFT JOIN city USING (CITYID)
			WHERE LOCATIONID IN ($idstr)"
		))) {
			return false;
		}
		if ($location) {
			if ($location['CITY_LATITUDE']) {
				$profile['positions'][] = [$location['CITY_LATITUDE'], $location['CITY_LONGITUDE']];

			} elseif ($location['LATITUDE']) {
				$profile['positions'][] = [$location['LATITUDE'], $location['LONGITUDE']];
			}
			if ($location['EROTIC']) {
				$profile['erotic'] = true;
			}
		}
		break;

	case 'music':
		break;

	case 'party':
		if (false === ($genres = db_same_hash('party_genre', "
			SELECT DISTINCT GID
			FROM party_genre
			WHERE PARTYID IN ($idstr)"
		))) {
			return false;
		}
		if ($genres) {
			$profile['genres'] = $genres;
		} else {
			if (false === ($stats = get_determined_genres($id))) {
				return false;
			}
			if (!empty($stats['for_display'])) {
				$profile['genres'] = $stats['for_display'];
				$for_display = true;
			}
		}
		foreach ($ids as $partyid) {
			if (!($party = memcached_party_and_stamp($partyid))) {
				if ($party === false) {
					return false;
				}
				# might be event that has now been deleted
				continue;
			}
			if ($party['EROTIC']) {
				$profile['erotic'] = true;
			}
			if ($party['CITYID']
			&&	($city = memcached_city_info($party['CITYID']))
			) {
				$profile['positions'][] = [$city['LATITUDE'], $city['LONGITUDE']];

			} elseif ($party['LAT']) {
				$profile['positions'][] = [$party['LAT'], $party['LON']];
			}
		}
		break;

	case 'organization':
		if (false === ($genres = db_same_hash('organization_genre', "
			SELECT GID
			FROM organization_genre
			WHERE ORGANIZATIONID IN ($idstr)"
		))) {
			return false;
		}
		if ($genres) {
			$profile['genres'] = $genres;
		}
		if (false === ($partyids = db_simple_hash(['party', 'connect'], "
			SELECT PARTYID, LOCATIONID
			FROM party
			JOIN connect
				 ON MAINTYPE = 'organization'
				AND MAINID  IN ($idstr)
				AND ASSOCTYPE = 'party'
				AND ASSOCID = PARTYID
			WHERE STAMP BETWEEN ".(CURRENTSTAMP - ONE_YEAR).' AND '.(CURRENTSTAMP + ONE_YEAR)
		))) {
			return false;
		}
		if ($partyids) {
			asort($partyids);
			if (false === ($stats = get_determined_genres(array_keys($partyids)))) {
				return false;
			}
			if (!isset($profile['genres'])
			&&	!empty($stats['for_display'])
			) {
				$profile['genres'] = $stats['for_display'];
				$for_display = true;
			}
			/*$diffs = [];
			foreach ($partyids as $partyid => $locationid) {
				$diffs[$locationid] = $locationid;
			}
			if (count($diffs) === 1) {
				$locationid = array_key_first($diffs);
				if (false === ($location = memcached_single_assoc(['location', 'city'], "
					SELECT	location.LATITUDE, location.LONGITUDE, EROTIC,
							city.LATITUDE AS CITY_LATITUDE, city.LONGITUDE AS CITY_LONGITUDE
					FROM location
					LEFT JOIN city USING (CITYID)
					WHERE LOCATIONID IN ($idstr)"
				))) {
					return false;
				}
				if ($location) {
					if ($location['CITY_LATITUDE']) {
						$profile['positions'][] = [$location['CITY_LATITUDE'], $location['CITY_LONGITUDE']];

					} elseif ($location['LATITUDE']) {
						$profile['positions'][] = [$location['LATITUDE'], $location['LONGITUDE']];
					}
				}
			}*/
			if (false === ($location = memcached_single_assoc(['location', 'city'], '
				SELECT	location.LATITUDE, location.LONGITUDE, EROTIC,
						city.LATITUDE AS CITY_LATITUDE, city.LONGITUDE AS CITY_LONGITUDE
				FROM location
				LEFT JOIN city USING (CITYID)
				WHERE LOCATIONID IN ('.implode(',', $partyids).')'
			))) {
				return false;
			}
			if ($location) {
				if ($location['CITY_LATITUDE']) {
					$profile['positions'][] = [$location['CITY_LATITUDE'], $location['CITY_LONGITUDE']];
				} elseif ($location['LATITUDE']) {
					$profile['positions'][] = [$location['LATITUDE'], $location['LONGITUDE']];
				}
			}
		}
		$profile['erotic'] = db_single('organization', "
			SELECT b'1'
			FROM organization
			WHERE ORGANIZATIONID IN ($idstr)
			  AND EROTIC = 1"
		);
		if (query_failed()) {
			return false;
		}
		break;

	case 'video':
		if (false === ($connects = db_multirow_hash('connect', "
			SELECT ASSOCTYPE, ASSOCID
			FROM connect
			WHERE MAINTYPE = 'video'
			  AND MAINID IN ($idstr)
			  AND ASSOCTYPE IN ('party', 'artist')"
		))) {
			return false;
		}
		if ($connects) {
			if (!empty($connects['party'])) {
				return element_profile('party', $connects['party']);

			} elseif (!empty($connects['artist'])) {
				return element_profile('artist', $connects['artist']);

			}
		}
		break;

	case 'news':
		if (false === ($partyids = db_simpler_array('connect', "
			SELECT ASSOCID
			FROM connect
			WHERE MAINTYPE = 'news'
			  AND MAINID IN ($idstr)
			  AND ASSOCTYPE = 'party'
			  AND ASSOCID"
		))) {
			return false;
		}
		if ($partyids) {
			return element_profile('party', $partyids);
		}
		break;

	case 'city':
		foreach ($ids as $local_id) {
			if (!($city = memcached_city_info($local_id))) {
				if ($city === false) {
					return false;
				}
				mail_log("city $local_id does not exist", subject: 'nonexistent city', error_log: 'ERROR');
				break;
			}
			if (!empty($city['LATITUDE'])) {
				$profile['positions'][] = [$city['LATITUDE'], $city['LONGITUDE']];
			}
		}
		break;

	default:
		return [];
	}

	if (!empty($profile['genres'])) {
		static $__genre_to_master = db_simple_hash('genre_to_master', 'SELECT GID, MGID FROM genre_to_master') ?: [];
		$masters = [];
		if (isset($for_display)) {
			# already have worths
			foreach ($profile['genres'] as $gid => /* $worth = */ $ignored) {
				if ($master_gid = $__genre_to_master[$gid] ?? null) {
					increment_or_set($masters, $master_gid, 1);
				}
			}
		} else {
			foreach ($profile['genres'] as $gid) {
				if ($master_gid = $__genre_to_master[$gid] ?? null) {
					increment_or_set($masters, $master_gid, 1);
				}
			}
		}
		if ($masters) {
			$max_worth = max($masters);
			$min_worth = .5 * $max_worth;
			foreach ($masters as $master_gid => $worth) {
				if ($worth >= $min_worth) {
					$profile['master_genres'][$master_gid] = true;
				}
			}
		}
	}
	memcached_set($key, $profile, TEN_MINUTES);
	return $__profile[$element][$idstr] = $profile;
}

if (isset($_REQUEST['SHOW_PROFILES'])) {
	show_profile_debug();
}

function show_profile_debug(): void {
	if (false === ($page_profile = page_profile())) {
		return;
	}
	assert(is_array($page_profile)); # Satisfy EA inspection
	if (!empty($page_profile['page_genres'])) {
		$page_profile['page_genres'] = db_simpler_array('master_genre', '
			SELECT NAME
			FROM master_genre
			WHERE MGID IN ('.implodekeys(', ', $page_profile['page_genres']).')'
		);
	}
	if (!empty($page_profile['page_positions'])) {
		$positions = [];
		foreach ($page_profile['page_positions'] as [$latitude, $longitude]) {
			if ($cityid = memcached_find_nearest_city($latitude, $longitude, 10000, $distance)) {
				$positions[] = memcached_city($cityid).($distance ? ', '.$distance.' km' : '');
			} else {
				$positions[] = $latitude.','.$longitude;
			}
		}
		$page_profile['page_positions'] = $positions;
	}
	print_rr($page_profile, 'page_profile');

	if ($element_profile = element_profile($_REQUEST['sELEMENT'], $_REQUEST['sID'])) {
		$element_profile['genres'] = db_simpler_array('genre', '
			SELECT genres.NAME, master_genre.NAME
			FROM genre
			LEFT JOIN genre_to_master USING (GID)
			LEFT JOIN master_genre USNG (MGID)
			WHERE GID IN ('.implodekeys(', ', $element_profile['genres']).')'
		);
		$positions = [];
		foreach ($element_profile['positions'] as [$latitude, $longitude]) {
			if ($cityid = memcached_find_nearest_city($latitude, $longitude, 10000, $distance)) {
				$positions[] = memcached_city($cityid).($distance ? ', '.$distance.' km' : '');
			} else {
				$positions[] = $latitude.','.$longitude;
			}
		}
		$element_profile['positions'] = $positions;

		print_rr($element_profile, 'elemment_profile');
	}
}

/*function get_filter_wheres(): array {
	$profile = current_profile();
	$result = [];

	if ($and_where_genres = !empty($profile['genres'])) {
		$result['and_where_genres'] = ' AND NOT ISNULL( (
				SELECT 1
				FROM event_master_genre emg
				WHERE emg.PARTYID=party.PARTYID
				  AND emg.MGID IN ('.implodekeys(',',$profile['genres']).')
				LIMIT 1
			) )';
	}
	if ($and_where_positions = !empty($profile['positions'])) {
		$posses = [];
		foreach ($profile['positions'] as $pos) {
			list($lat,$lon) = $pos;

			$posses[] = distance($lat,$lon,
				'COALESCE(boarding.LATITUDE,location.LATITUDE,city.LATITUDE)',
				'COALESCE(boarding.LONGITUDE,location.LONGITUDE,city.LONGITUDE)'
			).' < '.MAX_FREE_FLYERS;
		}
		$result['and_where_positions']  = ' AND ('.implode(' OR ',$posses).')';
	}
	if ($and_not_erotic = empty($profile['erotic'])) {
		$result['and_not_erotic'] = ' AND NOT party.EROTIC ';
	}
	return $result;
}*/
