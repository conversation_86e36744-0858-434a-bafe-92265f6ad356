<?php

declare(strict_types=1);

function check_memory_limit(): void {
	require_once '_flockmod.inc';
	$mem_used = memory_get_usage(true);
	if ($mem_used < .9 * ($limit = get_memory_limit())) {
		# not nearing limit
		return;
	}
	require_once '_layout.inc';
	$error_str = 'lots of memory used in mail_log ('.get_human_bytes($mem_used).') of max ('.get_human_bytes($limit).'): '.floor(100 * $mem_used / $limit).'%';
	error_log_r($error_str);
	error_log_r(debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS), 'backtrace');
	static $__sent_mail = false;
	if (!$__sent_mail) {
		$__sent_mail = true;
		mail_log($error_str);
	}
	# increase memory limit bij 128 MB
	error_log('mail_log increasing memory by 128MB');
	increase_memory_limit(128 * MEGABYTE);
}
