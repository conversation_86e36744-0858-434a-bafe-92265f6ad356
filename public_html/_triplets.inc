<?php

const WIN1252_TO_ASCII = [
	0xC0 => 65,
	0xC1 => 65,
	0xC2 => 65,
	0xC3 => 65,
	0xC4 => 65,
	0xC5 => 65,
	0xC6 => 65,
	0xC7 => 99,
	0xC8 => 101,
	0xC9 => 101,
	0xCA => 101,
	0xCB => 101,
	0xCC => 105,
	0xCD => 105,
	0xCE => 105,
	0xCF => 105,
	0xD1 => 110,
	0xD2 => 111,
	0xD3 => 111,
	0xD4 => 111,
	0xD5 => 111,
	0xD6 => 111,
	0xD8 => 111,
	0xD9 => 117,
	0xDA => 117,
	0xDB => 117,
	0xDC => 117,
	0xDD => 121,
	0xDF => 115,
	0xE0 => 97,
	0xE1 => 97,
	0xE2 => 97,
	0xE3 => 97,
	0xE4 => 97,
	0xE5 => 97,
	0xE6 => 97,
	0xE7 => 99,
	0xE8 => 101,
	0xE9 => 101,
	0xEA => 101,
	0xEB => 101,
	0xEC => 105,
	0xED => 105,
	0xEE => 105,
	0xEF => 105,
	0xF0 => 208,
	0xF1 => 110,
	0xF2 => 111,
	0xF3 => 111,
	0xF4 => 111,
	0xF5 => 111,
	0xF6 => 111,
	0xF8 => 111,
	0xF9 => 117,
	0xFA => 117,
	0xFB => 117,
	0xFC => 117,
	0xFD => 121,
	0xFE => 222,
];

const CREATE_TRIPLETS	= 1;
const WHERE_TRIPLETS	= 2;

function walk_triplets(string $str, int $action): array|string|null {
	$parts = explode(' ', $str);
	if ($action === WHERE_TRIPLETS) {
		$jc = 0;
		$wherep = '';
	}
	foreach ($parts as $part) {
		$part = strtolower($part);
		$len = strlen($part);
		if ($len < 3) {
			continue;
		}
		for ($i = 0; $i <= $len - 3; ++$i) {
			$a = ord($part[$i]);
			$b = ord($part[$i+1]);
			$c = ord($part[$i+2]);
			if (isset(WIN1252_TO_ASCII[$a])) {
				$a = WIN1252_TO_ASCII[$a];
			}
			if (isset(WIN1252_TO_ASCII[$b])) {
				$b = WIN1252_TO_ASCII[$b];
			}
			if (isset(WIN1252_TO_ASCII[$c])) {
				$c = WIN1252_TO_ASCII[$c];
			}
			$triplet = sprintf('%02x%02x%02x', $a, $b, $c);
			if ($action === WHERE_TRIPLETS) {
				if ($wherep) {
					$wherep .= ' AND ';
				}
				$wherep .= 't'.$jc.'.TRIPLET = 0x'.$triplet;
				++$jc;
			} else {
				$trips[] = sprintf('0x%02x%02x%02x', $a, $b, $c);
			}
		}
	}
	return $action === WHERE_TRIPLETS ? [$wherep, $jc] : ($trips ?? null);
}

function create_triplets(string $str): ?array {
	return walk_triplets($str, CREATE_TRIPLETS);
}

function build_triplets_where(string $str): array {
	return walk_triplets($str, WHERE_TRIPLETS);
}

function delete_old_triplets(int $userid, string $name): bool {
	return	($trips = create_triplets($name))
	?	db_delete('usertriplets', '
		DELETE FROM usertriplets
		 WHERE USERID = '.$userid.'
		   AND TRIPLET IN ('.implode(',', $trips).')'
		)
	: false;
}

function insert_new_triplets(int $userid, string $name): bool {
	if (!($trips = create_triplets($name))) {
		return false;
	}
	foreach ($trips as $trip) {
		$vals[] = '('.$userid.', '.$trip.')';
	}
	return db_insert('usertriplets', '
		INSERT IGNORE INTO usertriplets (USERID,TRIPLET)
		VALUES '.implode(',', $vals)
	);
}
