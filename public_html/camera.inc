<?php

require_once '_contact.inc';

# camera.OFFER === 1, offer created by admin
# camera.OFFER === 2, offer created by organizationmember

# time-out in seconds
function get_set_timeout(?int $timeout = null): int {
	static $__timeout;
	if ($timeout !== null) {
		$__timeout = $timeout;
	}
	return $__timeout;
}
function perform_commit(): ?bool {
	return match($_REQUEST['ACTION']) {
		default				=> null,
		'commit'			=> camera_commit(),
		'commit-settings'	=> camera_commit_settings(),
		'commitrequest'		=> camera_commit_request(),
		'commit-upload'		=> camera_commit_upload(),
		'remove'			=> camera_remove(),
		'mark-seen'			=> camera_mark_seen(),
		'denyrequest'		=> camera_deny_request(),
	};
}

function display_body(): void {
	switch ($_REQUEST['ACTION']) {
	default:							not_found(); return;
	case null:
	case 'mark-seen':
	case 'remove':						camera_display_overview(); return;
	case 'single':
	case 'commit':
	case 'denyrequest':
	case 'request':
	case 'commitrequest':				camera_display_single(); return;
	case 'confirmrequest':
	case 'form':						camera_display_form(); return;
	case 'upload-form':					camera_display_upload_form(); return;
	case 'commit-upload':				camera_display_upload_result(); return;
	case 'requests-per-photographer':	camera_display_requests_per_photographer(); return;
	case 'commit-settings':
	case 'settings-form':				camera_display_settings_form(); return;
	case 'interestingevents':			camera_display_interesting_events(); return;
	}
}

function register_message_from_STATUS(string $status, string $line): void {
	if ($status === 'ERROR') {
		$register_message = 'register_error';
		$direct_message = '_error';
	} else {
		$register_message = 'register_notice';
		$direct_message = '_notice';
	}
	# ERROR: Old way
	# ERROR: [ARG=yes]Old way too
	# ERROR:photo_process:notice:new_style_LINE::ARG=yes;ORG=no
	# 01234567
	$line = mb_substr($line, ($old_style = ($line[6] === ' ')) ? 7 : 6);

	if ($old_style) {
		if ($line[0] === '['
		&&	0 !== ($end = strpos($line, ']', 1))
		) {
			$mapstr = mb_substr($line, 1, $end - 1);
			$pairs = explode(';', $mapstr);
			$errstr = mb_substr($line, $end + 1);
			foreach ($pairs as $pair) {
				$keyval = explode('=', $pair);
				if (!isset($keyval[1])) {
					mail_log('bad pair parsed from photo server response', get_defined_vars());
					continue;
				}
				[$key, $val] = $keyval;
				if ($val[0] === '"') {
					$map[$key] = $val;
				} else {
					$map[$key] = (integer)$val;
				}
			}
			$register_message($errstr, $map);
			mail_log('failure: '.$errstr, get_defined_vars());
			return;
		}
		mail_log('response from photo server not understood: '.$line);

	} elseif (preg_match('"^(?<error_key>photo_process:.+?)(?:::(?<arguments>.*))?$"', $line, $match)) {
		if (!empty($match['arguments'])) {
			$map = [];
			foreach (explode(';', $match['arguments']) as $key_value) {
				[$key, $value] = explode('=', $key_value, 2);
				$map[$key] = $value;
			}
			$register_message($match['error_key'], $map);
			return;
		}
		$register_message($match['error_key']);
		return;
	}
	mail_log('response from photo server not understood: '.$line);
	if ($status === 'ERROR') {
		register_error('camera:error:something_went_wrong_on_the_photo_server_LINE', DO_UBB, ['LINE' => $line]);
	}
}

function show_photographers_info_button(): void {
	require_once 'defines/camera.inc';
	if (!($head = get_camerarequest_head())) {
		return;
	}
	?> <?= MIDDLE_DOT_ENTITY ?> <?
	[$first_name] = explode(' ', $head['REALNAME'], 2);

	show_info_button(
		'infotographers',
		Eelement_name('information'),
		__(
			'infomessage:4_TEXT',
			DO_UBB | DO_NL2BR | DO_CONDITIONAL,
			[	'CAMERA_HEAD'	=> $head['USERID'],
					'FIRST_NAME'	=> $first_name,
		]),
		__C('camera:header:information_for_photographers_LINE')
	);
}

/** @noinspection PhpMissingReturnTypeInspection  */
function camera_upload_ready_wait(Socket $sock) {
	$total_bytes_read = 0;
	while (false !== ($line = fgets($sock))) {
		$line = utf8_myrtrim($line);

		?><!-- ready_wait got: <?= escape_specials($line) ?> --><?

		if (str_starts_with($line, 'READY')) {
			return true;
		}
		if (str_starts_with($line, 'ERROR')) {
			register_message_from_STATUS('ERROR', $line);
			return null;
		}

		register_error('camera:error:unrecognized_response_from_photo_server_LINE', DO_UBB, ['LINE' => $line], send_email: true);

		$total_bytes_read += strlen($line);
	}
	if (!$total_bytes_read) {
		register_error('camera:error:no_response_from_photo_server_LINE', send_email: true);
	}
	return null;
}

/** @noinspection PhpUndefinedClassInspection  */
function camera_upload_send(Socket $sock, string $str): bool {
	?><!-- upload_send: <?= escape_specials($str, true) ?> --><?
	$len = strlen($str);
	if (false === ($wbytes = @fwrite($sock, $str, $len))) {
		if (!($info = stream_get_meta_data($str))) {
			register_error(@fwrite ($sock, $str, $len));
			return false;
		}
		if ($info['timed_out']) {
			register_error('camera:error:timeout_waiting_for_resonse_from_photo_server_LINE', ['SECONDS' => get_set_timeout()], send_email: true);
			return false;
		}
		if ($info['eof']) {
			register_error('camera:error:data_abruptly_closed_the_connection_while_writing_from_photo_server_LINE', send_email: true);
			return false;
		}
		register_error('camera:error:something_went_wrong_while_writing_from_photo_server_LINE', send_email: true);
		return false;
	}
	assert(is_int($wbytes)); # Satisfy EA inspection
	if ($wbytes < $len) {
		register_error('camera:error:not_everything_could_be_written_to_photo_server_LINE', ['OFFERED' => $len, 'SENT' => $wbytes], send_email: true);
		return false;
	}
	return true;
}

function camera_display_upload_result() {
	layout_show_section_header(__('camera:header:upload_processing'));
}

function camera_commit_upload(): bool {
	if (!require_admin(['camerarequest', 'photographer', 'aspiring_photographer'])
	||	!($partyid = require_idnumber($_POST, 'PARTYID'))
	||	!require_array($_POST, 'NAME')
	||	!require_anything($_POST, 'PATH')
	||	!isset($_POST['REPLACE'])
	&&	(	!require_something($_POST, 'SOCIALDESC')
		||	false === require_number($_POST, 'RANDOMIZE_GROUPS')
		)
	) {
		return false;
	}
	if (have_admin('camerarequest')) {
		if (isset($_POST['USERID'])) {
			if (false === require_number($_POST, 'USERID')) {
				return false;
			}
			$userid = $_POST['USERID'];
		}
	}
	$userid ??= CURRENTUSERID;

	if ( isset($_POST['ADD'])
	||	!isset($_POST['REPLACE'])
	) {
		$newset = true;
	}
	require_once 'defines/camera.inc';
	if (!($sock = fsockopen(
		PHOTO_UPLOAD_PROCESSOR_HOST,
		PHOTO_UPLOAD_PROCESSOR_PORT,
		$errno,
		$errstr,
		PHOTO_UPLOAD_PROCESSOR_TIMEOUT))
	) {
		register_error('camera:error:could_not_connect_to_photo_server_LINE');
		mail_log('could not connect to photo server', item: [
			'errno'  => $errno,
			'errstr' => $errstr,
		]);
		return false;
	}
	if (!stream_set_timeout($sock, get_set_timeout(10))
	||	!camera_upload_ready_wait($sock)
	||	!camera_upload_send($sock, "USERID=$userid\r\n")
	||	!camera_upload_ready_wait($sock)
	||	!camera_upload_send($sock, "PARTYID=$partyid\r\n")
	||	!camera_upload_ready_wait($sock)
	||	!camera_upload_send($sock, 'UNLINK='.(isset($_POST['UNLINK']) ? '1' : '0')."\r\n")
	||	!camera_upload_ready_wait($sock)
	) {
		return false;
	}
	$size = count($_POST['NAME']);

	if ($per_group = $_POST['RANDOMIZE_GROUPS'] ?? null) {
		$offset = 0;
		$shuffled = [];
		while ($part = array_slice($_POST['NAME'], $offset, $per_group)) {
			safe_shuffle($part);
			$shuffled += $part;
			$offset += $per_group;
		}
		$_POST['NAME'] = $shuffled;
	}

	if (isset($newset)) {
		for ($cnt = 0; $cnt < $size; ++$cnt) {
			if (!camera_upload_send($sock, "INCLUDE PATH={$_POST['PATH']}|NAME={$_POST['NAME'][$cnt]}\r\n")
			||	!camera_upload_ready_wait($sock)
			) {
				return false;
			}
		}
		if (isset($_POST['ANONYMOUS'])
		&&	(	!camera_upload_send($sock, "ANONYMOUS\r\n")
			||	!camera_upload_ready_wait($sock)
			)
		||	isset($_POST['INVISIBLE'])
		&&	(	!camera_upload_send($sock, "INVISIBLES\r\n")
			||	!camera_upload_ready_wait($sock)
			)
		||	!camera_upload_send($sock, "PUBLISH\r\n")
		||	!camera_upload_ready_wait($sock)
		) {
			return false;
		}
		register_notice('camera:notice:photos_are_being_processed_LINE');
		if (isset($_POST['ANONYMOUS'])) {
			register_notice('camera:notice:gallery_anonymous_LINE');
		}
		if (isset($_POST['INVSIBLE'])) {
			register_notice('camera:notice:gallery_invisible_LINE');
		}

		db_insert('gallerysocialdesc','
		INSERT INTO gallerysocialdesc SET
			SOCIALDESC	="'.addslashes($_POST['SOCIALDESC']).'",
			PARTYID		='.$partyid.',
			CSTAMP		='.CURRENTSTAMP.',
			CUSERID		='.CURRENTUSERID
		);
	} else {
		for ($cnt = 0; $cnt < $size; ++$cnt) {
			if (!camera_upload_send($sock, "REPLACE PATH={$_POST['PATH']}|NAME={$_POST['NAME'][$cnt]}\r\n")
			||	!camera_upload_ready_wait($sock)
			) {
				return false;
			}
		}
		if (!camera_upload_send($sock, "PROCESS\r\n")
		||	!camera_upload_ready_wait($sock)
		) {
			return false;
		}
		register_notice('camera:notice:photos_are_being_processed_LINE');
	}
	fclose($sock);
	return true;
}

function camera_display_upload_form(): void {
	require_once 'defines/camera.inc';

	layout_show_section_header(Eelement_name('upload_overview'));

	if (!require_admin(['camerarequest', 'photographer', 'aspiring_photographer'])) {
		return;
	}

	if (isset($_REQUEST['USERID'])
	&&	!have_admin('camerarequest')
	) {
		if (false === require_number($_REQUEST,'USERID')) {
			return;
		}
		$userid = $_REQUEST['USERID'];
	}
	$userid ??= CURRENTUSERID;

	if (!($sock = @fsockopen(
		PHOTO_UPLOAD_PROCESSOR_HOST,
		PHOTO_UPLOAD_PROCESSOR_PORT,
		$errno,
		$errstr,
		PHOTO_UPLOAD_PROCESSOR_TIMEOUT))
	) {
		register_error('camera:error:could_not_connect_to_photo_server_LINE');
		return;
	}
	if (!stream_set_timeout($sock, get_set_timeout(TEN_MINUTES))
	||	!camera_upload_ready_wait($sock)
	||	!camera_upload_send($sock, "USERID=$userid\r\n")
	||	!camera_upload_ready_wait($sock)
	||	!camera_upload_send($sock, "GETIMAGES\r\n")
	) {
		return;
	}
	$add_images = [];
	$replace_images = [];
	while (false !== ($line = fgets($sock))) {
		$line = rtrim($line);
		?><!-- upload_form: <?= escape_specials($line) ?> --><?
		if (str_starts_with($line, 'READY')) {
			break;
		}
		if (str_starts_with($line, 'ERROR')) {
			register_message_from_STATUS('ERROR', $line);
			return;
		}
		$attribs = explode('|', $line);
		$image = null;
		foreach ($attribs as $attrib) {
			$info = explode('=', $attrib, 2);
			if (!isset($info[1])) {
				register_error('camera:error:unknown_response_from_photo_server_LINE', DO_UBB, ['LINE' => $line]);
				continue;
			}
			[$key, $val] = $info;
			$image[$key] = $val;
		}
		$PATH = $image['PATH'];
		if (isset($image['NAME'])
		&&	preg_match('/^(\d+)(?:\.(?:[\.a-z]+))?$/iu',$image['NAME'],$matches)
		) {
			if (false === ($photo = db_single_assoc(['image', 'image_processing'], '
				SELECT	PARTYID, image.USERID,
						IF(image_processing.IMGID IS NOT NULL AND image_processing.FINISHED = 0, 1, 0) AS PROCESSING, FINISHED
				FROM image
				LEFT JOIN image_processing USING (IMGID)
				WHERE IMGID = '.$matches[1]
			))) {
				return;
			}
			if ($photo && $photo['USERID'] === $userid) {
				$image['PROCESSING'] = $photo['PROCESSING'];
				$image['FINISHED']	 = $photo['FINISHED'];
				$PARTYID = $photo['PARTYID'];
				$replace_images[$PATH][$PARTYID]['IMAGELIST'][] = $image;
				if ($photo['PROCESSING']) {
					if (isset($replace_images[$PATH][$PARTYID]['PROCESSING'])) {
						++$replace_images[$PATH][$photo['PARTYID']]['PROCESSING'];
					} else {
						$replace_images[$PATH][$PARTYID]['PROCESSING'] = 1;
					}
				}
				if (isset($replace_images[$PATH][$PARTYID]['TOTAL'])) {
					++$replace_images[$PATH][$PARTYID]['TOTAL'];
				} else {
					 $replace_images[$PATH][$PARTYID]['TOTAL'] = 1;
				}
				continue;
			}
		}
		if (isset($PATH)) {
			$add_images[$PATH][] = $image;
		}
	}
	if (!empty($replace_images)) {
		foreach ($replace_images as $path => &$parties) {
			foreach ($parties as $partyid => &$info) {
				if (count($info['IMAGELIST']) > 300) {
					warning('Je wilt meer dan 300 foto\'s vervangen. Dat lijkt niet logisch dus worden de foto\'s als nieuw gezien! Neem contact op indien je wel wilt vervangen.');
					$ndxs = [];
					foreach ($info['IMAGELIST'] as $ndx => &$image) {
						if (!$image['FINISHED']) {
							--$info['TOTAL'];
							if ($image['PROCESSING']) {
								--$info['PROCESSING'];
							}
							unset($image['PROCESSING'],$image['FINISHED']);
							$add_images[$path][] = $image;
							$ndxs[] = $ndx;
							unset($info['IMAGELIST'][$ndx]);
						}
					}
					unset($image);
					if (!$info['TOTAL']) {
						unset($parties[$partyid]);
					}
				}
			}
			if (empty($parties)) {
				unset($replace_images[$path]);
			}
		}
	}
	if (!camera_upload_send($sock,"QUIT\r\n")
	||	!camera_upload_ready_wait($sock)
	) {
		return;
	}

	fclose($sock);

	if (empty($add_images)
	&&	empty($replace_images)
	) {
		register_notice('camera:notice:found_no_photos_to_process_TEXT', DO_UBB);
		register_notice('camera:notice:upload_photos_using_ftp_TEXT',    DO_UBB);
		return;
	}

	if (!empty($add_images)) {
		if (false === ($options = db_rowuse_array(['camerarequest', 'party', 'image'], /** @lang MariaDB */ "(
			SELECT	DISTINCT camerarequest.PARTYID,
					party.NAME, SUBTITLE, STAMP,
					0 AS HAVE_IMAGES
			FROM camerarequest
			JOIN party USING (PARTYID)
			WHERE STATUS IN ('accepted', 'self', 'extern')
			  AND camerarequest.USERID = $userid
			  AND STAMP <= ".CURRENTSTAMP.'
			  AND STAMP > '.(CURRENTSTAMP - 3 * ONE_MONTH)."
			  AND NOT PROCESSING
			  AND (SELECT 1 FROM image WHERE image.PARTYID = camerarequest.PARTYID AND USERID = $userid LIMIT 1) IS NULL
			) UNION (
			SELECT	DISTINCT camerarequest.PARTYID,
					party.NAME, SUBTITLE, STAMP,
					1 AS HAVE_IMAGES
			FROM camerarequest
			JOIN party USING (PARTYID)
			WHERE STATUS IN ('accepted', 'self', 'extern')
			  AND camerarequest.USERID = $userid
			  AND STAMP <= ".CURRENTSTAMP.'
			  AND STAMP > '.(CURRENTSTAMP - 3 * ONE_MONTH)."
			  AND NOT PROCESSING
			  AND (SELECT 1 FROM image WHERE image.PARTYID = camerarequest.PARTYID AND USERID = $userid LIMIT 1) IS NOT NULL
			ORDER BY STAMP DESC
			)
			ORDER BY HAVE_IMAGES, STAMP DESC"
		))) {
			return;
		}
		$optcnt = $options ? count($options) : 0;
	}
	if ($add_images) {
		ksort($add_images);
		layout_open_box('white');
		?><table class="hha fw"><?
		foreach ($add_images as $path => $image_list) {
			if (!$path) {
				$path = '~';
			}
			?><tr><td class="fyel hpad" style="width: 1em;">&bull;</td><td><?
			?><a href="#<?= escape_utf8($path) ?>" class-="seemtext as-block"><?= escape_utf8($path) ?></a><?
			?></td></tr><?
		}
		?></table><?
		layout_close_box();
	}
	foreach ($add_images as $path => $image_list) {
		foreach ($image_list as &$image) {
			$image['CMP_NAME'] = preg_replace('"(?<!\d)0+(\d+)"', '$1', $image['NAME']);
		}
		unset($image);
		usort($image_list, static fn(array $a, array $b): int => strnatcasecmp($a['CMP_NAME'], $b['CMP_NAME']));

		if ($options) {
			?><form<?
			?> accept-charset="utf-8"<?
			?> onsubmit="return submitForm(this);"<?
			?> method="post"<?
			?> action="/camera/commit-upload"><?
			?><input type="hidden" name="PATH" value="<?= escape_utf8($path) ?>" /><?
			if ($userid !== CURRENTUSERID) {
				?><input type="hidden" name="USERID" value="<?= $userid ?>" /><?
			}
		}

		$photo_total = camera_display_images($image_list, $userid, $path, $optcnt ? true : false);

		if ($options) {
			if (!($default_anonymous = db_single('gallery_default','SELECT ANONYMOUS FROM gallery_default WHERE USERID='.CURRENTUSERID))
			&&	db_failed()
			) {
				return;
			}
			show_textarea_utf8([
				'required'		=> true,
				'name'			=> 'SOCIALDESC',
				'placeholder'	=> 'Vul hier wat kernwoorden in, een zin, meerdere zinnen, waaruit een enkele zin voor op de social media (Facebook, Instagram & Twitter) gedestilleerd kan worden.',
			]);
			layout_open_menu();
			layout_open_menuitem();
			?><div><?
			?><input type="submit" name="ADD" value="<?= __('action:add'); ?>" id="addpart" /> als <?
			?><select required onchange="setdisplay('contactpr', this.value === 'X');" name="PARTYID"><?
			if ($optcnt !== 1) {
				?><option></option><?
			}
			foreach ($options as $opt) {
				if (!isset($have_images)
				&&	$opt['HAVE_IMAGES']
				) {
					$have_images = true;
					?><optgroup label="<?= __('status:already_processed') ?>"><?
				}
				?><option value="<?= $opt['PARTYID'];
				?>"><? _date_display_numeric($opt['STAMP']) ?>: <?
				echo escape_utf8($opt['NAME']);
				if ($opt['SUBTITLE']) {
					?><small> <?= MIDDLE_DOT_ENTITY ?> <?= escape_utf8($opt['SUBTITLE']) ?></small><?
				}
				?></option><?
			}
			if (isset($have_images)) {
				?></optgroup><?
				unset($have_images);
			}
			?><option value="X"><?= __('form:select:other_option') ?>&hellip;</option><?
			?></select><?
			?></div><?

			require_once 'defines/camera.inc';
			?><div class="hidden body block" id="contactpr"><?=
				__('camera:notice:contact_head_to_add_event_TEXT', DO_UBB | DO_NL2BR, ['USERID' => get_camerarequest_head('USERID')]);
			?></div><?

			layout_close_menuitem();

			layout_continue_menu();
			layout_open_menuitem();
			?><div class="ib right"><?

			?><div><label class="nowrap"><?= __C('action:remove_photos_after_processing') ?>: <?
			?><input type="checkbox" value="1" checked name="UNLINK" /></label></div><?

			?><div><label class="nowrap"><?= __C('action:make_gallery_anonymous') ?>: <?
			?><input type="checkbox" value="1" name="ANONYMOUS"<?
			if ($default_anonymous) {
				?> checked<?
			}
			?> /></label></div><?

			?><div><label class="nowrap"><?= __C('action:publish_invisible') ?>: <?
			?><input type="checkbox" value="1" name="INVISIBLE"<?
			if ($default_anonymous) {
				?> checked<?
			}
			?> /></label></div><?

			# prefer groups of 25 photos to randomize

			$groups =	  (int)round($photo_total / 25);
			$max_groups = (int) ceil($photo_total /  5);

			?><div><label><?= __C('action:randomize_groups') ?>: <select name="RANDOMIZE_GROUPS"><?
			?><option value="0"></option><?
			for ($i = 1; $i <= $max_groups; ++$i) {
				$photos_per_group = ceil($photo_total / $i);
				?><option value="<?= $photos_per_group ?>"><?= $i ?>, per <?= ceil($photo_total / $i),' ',element_plural_name('photo') ?></option><?
			}
			?></select></label></div><?
			?></div><?
			layout_close_menuitem();
			layout_close_menu();
			?></form><?

		} else {
			require_once 'defines/camera.inc';
			register_warning('camera:warning:no_events_availble_for_upload_please_contact_LINE', DO_UBB, ['HEADADMINID' => get_camerarequest_head('USERID')]);
			display_messages();
		}
	}

	foreach ($replace_images as $path => $parties) {
		foreach ($parties as $partyid => $info) {
			if (!($all_processing = (
				isset($info['PROCESSING'])
			&&	$info['PROCESSING'] === $info['TOTAL']
			))) {
				if (($party = memcached_party_and_stamp($partyid))
				&&	$party['STAMP'] < CURRENTSTAMP - ONE_MONTH
				) {
					_notice(get_element_link('party', $partyid, $party['NAME']).
						' was meer dan 30 dagen geleden! Neem eventueel contact op met '.get_element_link('user', 2269)
					);
					$too_long_ago = true;
				}
				?><form<?
				?> accept-charset="utf-8"<?
				?> onsubmit="return submitForm(this)"<?
				?> method="post"<?
				?> action="/camera/commit-upload"><?

				?><input type="hidden" name="PATH" value="<?= escape_utf8($path) ?>" /><?
				if ($userid !== CURRENTUSERID) {
					?><input type="hidden" name="USERID" value="<?= $userid ?>" /><?
				}
			}
			camera_display_images($info['IMAGELIST'], $userid, $path, !$all_processing);
			if (!$all_processing) {
				if (isset($too_long_ago)) {
					display_messages();
				} else {
					layout_open_menu();
					layout_open_menuitem();
					?><input type="submit" name="REPLACE" value="<?= __('action:replace') ?>" /><?
					?> <?= __('camera:in_photo_set');
					?> <select name="PARTYID"><?
						?><option value="<?= $partyid ?>"><?= escape_utf8($party['NAME']) ?></option><?
					?></select><?
					layout_close_menuitem();
					layout_continue_menu();
					layout_open_menuitem();
					?><label><?= __('action:remove_photos_after_processing') ?> <input type="checkbox" value="1" checked name="UNLINK" /></label><?
					layout_close_menuitem();
					layout_close_menu();
				}
				?></form><?
			}
		}
	}
}

function camera_display_images($image_list, $userid, $path, $formelems = true): int {
	layout_open_box('camera', escape_specials($path));
	layout_box_header(escape_utf8($path));
	layout_open_table('fw default vtop');
	layout_start_header_row();
	layout_header_cell(); # number
	layout_header_cell(Eelement_name('name'));
	layout_header_cell(Eelement_name('type'));
	?><th colspan="3" class="center"><?= Eelement_plural_name('dimension'); ?></th><?
	?><th><?= Eelement_name('aspect') ?></th><?
	layout_header_cell_right(Eelement_name('size'));
	layout_stop_header_row();
	$cnt = 1;
	$okcnt = 0;
	$brokencnt = 0;
	foreach ($image_list as $image) {
		$image['WIDTH']	 = (float)$image['WIDTH'];
		$image['HEIGHT'] = (float)$image['HEIGHT'];

		?><tr<?
		if (!empty($image['PROCESSING'])
		||	!$image['WIDTH']
		||	!$image['HEIGHT']
		||	!$image['SIZE']
		||	isset($image['BAD'])
		) {
			++$brokencnt;
			?> class="broken-image invalid"<?
		} else {
			++$okcnt;
		}
		switch ($image['ORIENTATION']) {
		case 5:
		case 6:
		case 7:
		case 8:
			$tmp = $image['WIDTH'];
			$image['WIDTH'] = $image['HEIGHT'];
			$image['HEIGHT'] = $tmp;
			break;
		}

		?>><td class="right rpad"><?
		if ($formelems
		&&	empty($image['PROCESSING'])
		&&	$image['WIDTH']
		&&	$image['HEIGHT']
		) {
			?><input type="hidden" name="NAME[]" value="<?= escape_utf8($image['NAME'], true) ?>" /><?
		}
		echo $cnt;
		?>.</td><?

		?><td><?
		echo escape_utf8($image['NAME']);
		if (!empty($image['PROCESSING'])) {
			?><br /><?
			?><span class="light"><?= __('status:being_processed_not_available') ?><span><?
		} elseif (!empty($image['FINISHED'])) {
			?><br /><?
			?><span class="light"><?= __('status:last_processed_on_date', DO_UBB, ['DATE' => $image['FINISHED']]) ?></span><?
		}
		if ($image['ERROR']) {
			?><br /><?
			?><span class="nb"><?
			if ($image['ERROR'] === 'unrecognized') {
				echo __('status:unrecognized');
			} else {
				echo escape_utf8($image['ERROR']);
			}
			?></span><?
		}

		?></td><?

		?><td><?= $image['TYPE'] === '???' ? null : escape_specials($image['TYPE']) ?></td><?
		if ($image['WIDTH'] || $image['HEIGHT']) {
			?><td class="right"><?= $image['WIDTH'] ?></td><?
			?><td class="center hpad"><?= MULTIPLICATION_SIGN_ENTITY ?></td><?
			?><td class="left"><?= $image['HEIGHT'] ?></td><?
			?><td class="light"><?=
				$image['WIDTH']
			&&	$image['HEIGHT']
			?	round($image['WIDTH'] > $image['HEIGHT'] ? $image['WIDTH'] / $image['HEIGHT'] : $image['HEIGHT'] / $image['WIDTH'],1)
			:	'X'
			?></td><?
		} else {
			?><td colspan="4"></td><?
		}
		?><td class="right"><?
		print_human_bytes($image['SIZE'])
		?></td></tr><?
		++$cnt;
	}
	layout_close_table();
	layout_box_header(escape_utf8($path), 'ilfake');
	layout_close_box();
	if ($brokencnt) {
		register_warning('camera:warning:broken_photos_TEXT', DO_NL2BR, ['BROKEN' => $brokencnt]);
	}
	return $okcnt;
}

function camera_mark_seen(): bool {
	if (!require_admin('photographer')
	||	!db_update('camerarequest','
		UPDATE camerarequest SET LSTAMP	='.CURRENTSTAMP.'
		WHERE LSTAMP < '.CURRENTSTAMP.'
		  AND USERID = '.CURRENTUSERID)
	) {
		return false;
	}
	register_notice('camerarequest:notice:all_marked_read_LINE');
	see_other('/camera');
	return true;
}

function camera_deny_request(): bool {
	if (!require_admin('camerarequest')
	||	!($partyid = require_idnumber($_REQUEST, 'sID'))
	||	!db_insert('camera_log', "
		INSERT INTO camera_log
		SELECT * FROM camera
		WHERE STATUS != 'denied'
		  AND PARTYID = $partyid")
	||	!db_update('camera', '
		UPDATE camera SET
			MSTAMP	= '.CURRENTSTAMP.",
			STATUS	= 'denied'
		WHERE STATUS != 'denied'
		  AND PARTYID = $partyid")
	||	!db_insert('camerarequest_log', "
		INSERT INTO camerarequest_log
		SELECT * FROM camerarequest
		WHERE STATUS != 'denied'
		  AND PARTYID = $partyid")
	||	!db_update('camerarequest','
		UPDATE camerarequest SET
			MSTAMP	= '.CURRENTSTAMP.',
			MUSERID	= '.CURRENTUSERID.",
			STATUS	= 'denied'
		WHERE STATUS != 'denied'
		  AND PARTYID = $partyid")
	) {
		return false;
	}
	register_notice('camerarequest:notice:denied_LINE');
	if (isset($_REQUEST['TICKETID'])) {
		$_REQUEST['STATUS'] = 'closed';
		contact_set_status();
	}
	return true;
}

function camera_display_requests_per_photographer(): void {
	if (!require_admin('camerarequest')) {
		return;
	}

	layout_show_section_header();

	camera_menu();

	if (!($cammers = memcached_multirowuse_hash(['camerarequest', 'party'], '
		SELECT camerarequest.USERID AS _U, PARTYID, camerarequest.STATUS, STAMP, NAME
		FROM camerarequest
		JOIN user_account USING (USERID)
		LEFT JOIN party USING (PARTYID)
		WHERE STAMP >= '.TODAYSTAMP.'
		ORDER BY NICK, camerarequest.USERID, party.STAMP'
	))) {
		return;
	}
	include_style('camerarequest');
	layout_open_box('white');
	foreach ($cammers as $userid => $parties) {
		layout_box_header(get_element_link('user', $userid));
		?><div class="block"><?
		foreach ($parties as $party) {
			extract($party, \EXTR_OVERWRITE);
			?><div class="hh<?
			switch ($STATUS) {
			case 'accepted':
			case 'denied':
			case 'extern':
			case 'failed_organization':
			case 'failed_photographer':
			case 'chosen':
			case 'not_chosen':
			case 'no_response':
			case 'self':
				?> cam-bg-<? echo $STATUS;
				break;
			}
			?>"><a href="/camera/<?= $PARTYID ?>"><?= escape_utf8($NAME) ?></a><?
			?><div class="r"><? date_display_link($STAMP) ?></div><?
			?></div><?
		}
		?></div><?
	}
	layout_close_box();
}

function camera_menu(): void {
	require_once '_infobutton.inc';
	layout_open_menu();
	layout_menuitem(Eelement_name('overview'), '/camera', !$_REQUEST['ACTION']);
	if (have_admin('camerarequest')) {
		layout_menuitem(Eelement_name('requests_photographer'), '/camera/requests-per-photographer', $_REQUEST['ACTION'] === 'requests-per-photographer');
	}
	layout_menuitem(Eelement_plural_name('interesting_event'), '/camera/interestingevents', $_REQUEST['ACTION'] === 'interestingevents');

	show_photographers_info_button();

	if (have_admin(['photographer', 'aspiring_photographer'])) {
		layout_continue_menu();
		layout_menuitem(
			Eelement_plural_name('setting'),
			'/camera/settings-form',
			$_REQUEST['ACTION'] === 'settings-form'
		);
	}
	layout_close_menu();
}

function camera_single_menu(bool $am_employee): void {
	if ((	$am_employee
		||	have_admin('camerarequest')
		)
	&&	empty($_REQUEST['ACTION'])
	) {
		layout_open_menu();
		layout_menuitem(__C('action:add'), '/camera/form', $_REQUEST['ACTION'] === 'form');
		layout_close_menu();
	}
}

function camera_display_form(): void {
	require_once '_element_access.inc';
	require_once '_ticketlist.inc';
	require_once '_viavia.inc';

	$is_camera_admin = have_admin('camerarequest');

	if (($camera = ($partyid = $_REQUEST['sID']) ? true : null)
	&&	false === ($camera = db_single_assoc('camera','
		SELECT *
		FROM camera
		WHERE PARTYID = '.$partyid))
	) {
		return;
	}
	if (!$is_camera_admin) {
		if ($partyid) {
			[$read, $write] = element_access('camerarequest',$partyid);
			if (!$write) {
				require_admin('camerarequest');
				return;
			}
		} elseif (!($parties = parties_for_employer())) {
			require_admin('camerarequest');
			return;
		}
	}

	layout_show_section_header();

	camera_menu();

	if ($is_camera_admin) {
		if ($partyid || $camera) {
			show_connected_tickets(forced: $camera['TICKETID'] ?? null);
		}
		if ($partyid) {
			if (false === ($requestlist = db_rowuse_hash('camerarequest', "
				SELECT USERID, STATUS, CSTAMP, REQUESTED
				FROM camerarequest
				WHERE PARTYID = $partyid"
			))) {
				return;
			}
			if ($requestlist) {
				foreach ($requestlist as $userid => &$request) {
					$request['NICK'] = memcached_nick($userid);
				}
				unset($request);
				require_once '_sort.inc';
				string_asort($requestlist, 'NICK');
			}
		} else {
			$requestlist = [];
		}
		if (isset($_REQUEST['STATUS'])) {
			mail_log("_REQUEST[STATUS] is set: {$_REQUEST['STATUS']}", get_defined_vars(), error_log: 'WARNING');
			  $camera['STATUS'] = $status;
			$_REQUEST['STATUS'] = $status;
			if ($requestlist) {
				foreach ($requestlist as $userid => &$request) {
					$request['STATUS'] = $request['REQUESTED'] ? $status : 'denied';
				}
				unset($request);
			}
		}
	}
	$new_offer = !$partyid;
	$skips = [];
	if ($new_offer) {
		if (false === ($skips = db_same_hash('camera', '
			SELECT PARTYID
			FROM camera
			JOIN party USING (PARTYID)
			WHERE STAMP > '.(TODAYSTAMP - ONE_DAY)
		))) {
			return;
		}
	}
	include_style('camerarequest');
	include_js('js/form/camera');

	?><form<?
	?> accept-charset="utf-8"<?
	?> onsubmit="return submitForm(this)"<?
	?> method="post"<?
	?> action="/camera/<?= $partyid ? $partyid.'/' : '' ?>commit"><?

	if (($ticketid = have_idnumber($_REQUEST, 'TICKETID'))
	&&	$is_camera_admin
	) {
		if ($new_offer) {
			require_once '_contact_ticket_first_message.inc';
			if (!($info = contact_display_first_ticket_message($ticketid, 'white'))) {
				return;
			}
			[$ticket, $parties] = $info;
		}
		?><input type="hidden" name="TICKETID" value="<?= $ticketid ?>" /><?
	}
	if (!$is_camera_admin) {
		layout_open_box('white');
		?><div class="block"><?= __('camera:info:add_offer_TEXT', DO_NL2BR | DO_UBB) ?></div><?
		layout_close_box();
	}
	layout_open_box('camera');

	ob_start();
	if ($new_offer) {
		$ignores = db_same_hash('party','
			SELECT PARTYID
			FROM party
			WHERE STAMP > '.(TODAYSTAMP - ONE_DAY).'
			  AND (CANCELLED != 0 OR MOVEDID != 0)'
		);
		if ($ignores !== false) {
			$skips += $ignores;
		}
		require_once '_partyoptions.inc';
		?><select name="PARTYID" required="required" class="fw"><?
		?><option></option><?
		show_party_options(
			$is_camera_admin && isset($parties)
		?	$parties
		:	$partyid
		,
			true,false,$is_camera_admin && isset($parties) ? party_source_labels() : null,!$is_camera_admin ? $parties : null,$skips,
			__('camerarequest:info:already_exists')
			);
		?></select><?
	} else {
		echo get_element_link('party',$partyid);
	}
	layout_box_header(ob_get_clean());

	layout_open_table(TABLE_CLEAN);
		if ($is_camera_admin) {
			$show_offermsg =
				!empty($camera['OFFER'])
			||	!$is_camera_admin
			||	(!$_REQUEST['sID'] && $ticketid);

			$offer_required =
				!$is_camera_admin
			||	($camera&& $camera['OFFER'] === 2);

			layout_start_row();
			?><label for="offer"><?= Eelement_name('offer') ?></label><?
			layout_field_value();
			show_input([
				'id'		=> 'offer',
				'checked'	=> $show_offermsg,
				'required'	=> $offer_required,
				'readonly'	=> $offer_required,
				'type'		=> 'checkbox',
				'name'		=> 'OFFER',
				'value'		=> $offer_required ? 2 : 1,
				'onclick'	=> "changerow(this.form.OFFERMSG, this.checked ? 'show' : 'hide')",
			]);

		} else {
			$show_offermsg = true;
			show_input([
				'type'	=> 'hidden',
				'name'	=> 'OFFER',
				'value'	=> 1
			]);
		}


	layout_restart_row($show_offermsg || !empty($camera['OFFER']) ? 0 : ROW_HIDDEN);
		echo Eelement_name('real_message');
		layout_field_value();
		show_textarea([
			'class'			=> 'growToFit',
			'rows'			=> !empty($camera['OFFERMSG']) ? 5 : 1,
			'cols'			=> 60,
			'name'			=> 'OFFERMSG',
			'value_utf8'	=> $camera['OFFERMSG'] ?? '',
		]);

	# EXTERN
		layout_restart_row();
		?><label for="extern"><?= Eelement_name('external_supply') ?></label><?
		layout_field_value();
		?><label><?
		show_input([
			'id'		=> 'extern',
			'type'		=> 'checkbox',
			'name'		=> 'EXTERN',
			'value'		=> 1,
			'checked'	=> !empty($camera['EXTERN'])
		]);
		?> <?
		require_once '_camreqspec.inc';
		show_camreq_icon('extern');
		?></label><?

	# ENTRY
		layout_restart_row();
		echo Eelement_name('access');
		layout_field_value();
		$spec = explain_table('camera');
		?><select name="ENTRY"><?
		?><option></option><?
		foreach ($spec['ENTRY']->enum as $entry_type) {
			if (!$entry_type) {
				continue;
			}
			?><option<?
			if ($camera
			&&	$camera['ENTRY'] === $entry_type
			) {
				?> selected<?
			}
			?> value="<?= $entry_type ?>"><?
			echo __('entry:'.$entry_type);
			?></option><?
		}
		?></select><?

	# GLISTPLUS
	layout_restart_row();
		?><label for="glistplus"><?= Eelement_name('list') ?></label><?
		layout_field_value();
		require_once '_camreqspec.inc';
		?><select name="GLISTPLUS" id="glistplus"><?
		?><option></option><?
		for ($cnt = 0; $cnt <= 20; ++$cnt) {
			?><option<?
			if (isset($camera['GLISTPLUS'])
			&&	$camera['GLISTPLUS'] === $cnt
			) {
				?> selected<?
			}
			?> value="<?= $cnt;
			?>">+ <?= $cnt ?></option><?
		}
		?></select><?
		show_camreq_icon('man', 'lmrgn');

	# CONSUMPTIONS
	layout_restart_row();
		echo Eelement_plural_name('free_consumption');
		layout_field_value();
		?><select name="CONSUMPTIONS"><?
			?><option value="256"><?= __('answer:no') ?></option><?
			?><option<?
			if ( $camera
			&&  $camera['CONSUMPTIONS'] !== null
			&& !$camera['CONSUMPTIONS']
			) {
				?> selected<?
			}
			?> value="0"><?= __('answer:yes') ?></option><?
			for ($i = 1; $i <= 50; ++$i) {
				?><option<?
				if ($camera
				&& $camera['CONSUMPTIONS'] === $i) {
					?> selected<?
				}
				?> value="<?= $i ?>"><?= $i ?></option><?
			}
		?></select><?
		show_camreq_icon('drink', 'lmrgn');


	# TRAVEL EXPENSES
	layout_restart_row();
		echo Eelement_name('travel_allowance');
		layout_field_value();
		?><select name="TRAVEL_EXPENSES"><?
		$options = [
			null	=> __('answer:no'),
			0		=> __('answer:maybe'),
		];
		if ($is_camera_admin) {
			$options[1] = element_name('organization');
			$options[2] = 'Partyflock';
		} else {
			$options[1] = __('answer:yes');
		}
		if (!$camera || $camera['TRAVEL_EXPENSES'] != 0) {
			unset($options[0]);
		}
		show_options($camera['TRAVEL_EXPENSES'] ?? null, $options);
		?></select><?
		show_camreq_icon('car', 'lmrgn');

	# REWARD
	layout_restart_row();
		echo Eelement_name('compensation');
		?> (euro)<?
		layout_field_value();
		$options = [
			null	=> __('answer:no'),
			0		=> __('answer:maybe'),
			1		=> __('answer:yes')
		];
		?><table class="dens nomargin"><?
		if ($is_camera_admin) {
			$rewards = [
				'REWARD_EXTERNAL'	=> element_name('organization'),
				'REWARD_FLOCK'		=> 'Partyflock'
			];
		} else {
			$rewards = [
				'REWARD_EXTERNAL'	=> null
			];
		}
		foreach ($rewards as $rewardname => $desc) {
			?><tr><?
			if ($desc) {
				?><td class="rpad"><?= $desc ?></td><?
			}
			?><td class="rpad"><?

			?><select<?
			?> name="<?= $rewardname ?>"<?
			?> onchange="setdisplay(this.form.<?= $rewardname ?>_VALUE, this.value == 1);"><?

			$opts = $options;

			if (!$camera
			||	 $camera[$rewardname] != 0
			) {
				unset($opts[0]);
			}
			show_options(
					$camera
				?	(	$camera[$rewardname] > 0
					?	1
					:	$camera[$rewardname]
					)
				:	null,
				$opts
			);
			?></select><?
			?></td><td><?
			show_input([
				'class'		=> 'right three_digits'.(empty($camera[$rewardname]) ? ' hidden' : ''),
				'name'		=> $rewardname.'_VALUE',
				'type'		=> 'number',
				'min'		=> 0,
				'max'		=> 10000,
				'value'		=> $camera[$rewardname] ?? null,
			]);
			?></td><?
			if (!isset($money)) {
				$money = true;
				?><td rowspan="2" class="lpad"><?
				show_camreq_icon('money');
				?></td><?
			}
			?></tr><?
		}
		?></table><?

	# ACTIVE
	layout_restart_row();
		?><label for="active"><?= __C('status:active') ?></label><?
		layout_field_value();
		?><input id="active"<?
		if (!$camera || $camera['ACTIVE']) {
			?> checked<?
		}
		?> type="checkbox" name="ACTIVE" value="1" /><?

	if ($is_camera_admin) {
		layout_restart_spanned_row();
			?><hr class="slim"><?


		# WRITTEN
		layout_start_row();
			?><label for="written"><?= __C('camstatus:written') ?></label><?
			layout_field_value();
			?><input type="checkbox" id="written" name="WRITTEN"<? if (!empty($camera['WRITTEN'])) { echo ' checked'; } ?> /><?

		show_viavia_form_rows($camera);

		# REQUESTBYSELF, photographer requested it him/herself
		layout_restart_row();
			?><label for="requestbyself">Zelf aangevraagd</label><?
			layout_field_value();

			?><input type="checkbox" id="requestbyself" name="REQUESTBYSELF"<? if (!empty($camera['REQUESTBYSELF'])) echo ' checked'; ?> /><?

			$opts = [];
			if (!empty($camera['REQUESTFOR'])) {
				$opts[$camera['REQUESTFOR']] = memcached_nick($camera['REQUESTFOR']);
			}
			foreach ($requestlist as $request) {
				if (!isset($opts[$request['USERID']])) {
					$opts[$request['USERID']] = memcached_nick($request['USERID']);
				}
			}

			asort($opts);

			if ($opts) {
				?> <select<?
				?> name="REQUESTFOR"<?
				?> onchange="this.form['REQUESTBYSELF'].checked = Boolean(this.selectedIndex);"><?
				?><option></option><?
				foreach ($opts as $userid => $nick) {
					?><option<?
					?> value="<?= $userid ?>"<?
					if ($userid === $camera['REQUESTFOR']) {
						?> selected<?
					}
					?>><?= escape_specials($nick) ?></option><?
				}
				?></select><?
			} else {
				show_input([
					'name'	=> 'REQUESTFOR',
					'type'	=> 'hidden',
				]);
			}

		# STATUS
		layout_restart_row(rowuserdef: $camera && $camera['STATUS'] ? 'cam-bg-'.$camera['STATUS'] : null);
			echo Eelement_name('status');
			layout_field_value();

			$spec = explain_table('camera');

			?><select<?
			?> name="STATUS"<?
			?> onchange="Pf_changeGlobalCameraStatus(this);"><?
			?><option<?
			if (empty($camera['STATUS'])) {
				?> selected<?
			}
			?> value=""></option><?

			$statuses = [];
			foreach ($spec['STATUS']->enum as $status) {
				if (!$status) {
					continue;
				}
				$statuses[$status] = __("camstatus:$status");
			}

			asort($statuses);
			foreach ($statuses as $status => $description) {
				?><option<?
				if ($camera
				&&	$camera['STATUS'] === $status
				) {
					?> selected<?
				}
				?> value="<?= $status ?>"><?= $description ?></option><?
			}
			?></select><?

		# COMMENT FOR PHOTOGRAPHERS
		layout_restart_row();
			echo Eelement_name('comment_for_photographer');
			layout_field_value();
			show_textarea([
				'class'			=> 'growToFit',
				'name'			=> 'MSG',
				'cols'			=> 50,
				'rows'			=> $camera && $camera['MSG'] ? 5 : 2,
				'value_utf8'	=> $camera['MSG'] ?? '',
			]);
	}
	layout_stop_row();
	layout_close_table();
	layout_close_box();

	include_style('camerarequest');

	if ($is_camera_admin) {
		layout_open_box('camera');
		layout_box_header(Eelement_name('photographer'),
			'<span class="unhideanchor" onclick="Pf_addPhotographer(this);">'.__('action:add_photographer').'</span>');
		layout_open_table(TABLE_FULL_WIDTH);
		$requestlist[] = null;
		foreach ($requestlist as $request) {
			if ($request) {
				extract($request, \EXTR_OVERWRITE);
			} else {
				$CSTAMP = null;
				$STATUS = null;
				$USERID = null;
			}
			?><tr<?
			$hidden  = false;
			if ($STATUS) {
				?> class="cam-bg-<?= $STATUS ?>"<?
			} elseif (!$request) {
				?> class="hidden" id="aspiring"<?
				$hidden = true;
			}
			?>><td><?
			if ($request) {
				?><a href="/user/<?= $USERID ?>"><?= escape_specials($NICK) ?></a><?
			} else {
				require_once '_fillelementid.inc';
				show_elementid_input('user',0,[
					'name'		=> 'NEWUSER[]',
					'min'		=> 0,
					'disabled'	=> $hidden,
					'on-user'	=> 'Pf_addPhotographerToOptions',
				]);
			}
			layout_next_cell(class: 'right');
			if ($request) {
				datedaytime_display_link($CSTAMP);
			}
			layout_next_cell(class: 'right');

			?><select<?
			?> onchange="Pf_changeSingleCameraStatus(this);"<?
			?> name="<?= $USERID ? 'USER['.$USERID.']' : 'NEWSTATUS[]' ?>"<?
			?> data-initial="<?= $STATUS ?>"><?

			$spec = explain_table('camerarequest');
			$statuses = ['' => null];
			foreach ($spec['STATUS']->enum as $status) {
				if (!$status) {
					continue;
				}
				$statuses[$status] = __('camstatus:'.$status);
			}
			asort($statuses);
			show_options($STATUS, $statuses);
			?></select><?
			layout_stop_row();
		}
		layout_close_table();
		layout_close_box();
	}
	?><div class="block"><input type="submit" value="<?= __($camera ? 'action:change' : 'action:add') ?>" /></div><?
	?></form><?
}

function camera_display_single(): void {
	if (!($partyid = require_idnumber($_REQUEST,'sID'))) {
		return;
	}
	if (!($is_admin_or_photographer = have_admin(['camerarequest', 'photographer']))) {
		require_once '_element_access.inc';
		[$read, $write] = element_access('camerarequest', $partyid);
		if (!$read) {
			require_admin(['camerarequest', 'photographer']);
			return;
		}
	}

	require_once 'defines/camera.inc';

	$camera = db_single_assoc(['camera','party','location'], '
		SELECT	WRITTEN, REQUESTFOR, REQUESTBYSELF, camera.STATUS, OFFER, camera.TICKETID,  VIA_GMAIL,  VIA_FACEBOOK,
				MSG, GLISTPLUS, OFFERMSG, CONSUMPTIONS, TRAVEL_EXPENSES, REWARD_EXTERNAL, ENTRY,
				REWARD_FLOCK, EXTERN, camera.MSTAMP, camera.MUSERID, camera.CSTAMP, camera.CUSERID,
				party.NAME, party.STAMP, MOVEDID, CANCELLED, party.ACCEPTED, ACTIVE,
				LOCATIONID, location.NAME AS LOCATION_NAME
		FROM camera
		RIGHT JOIN party USING (PARTYID)
		LEFT JOIN location USING (LOCATIONID)
		WHERE PARTYID = '.$partyid
	);

	layout_show_section_header(to_userid: $camera['CUSERID'] ?? 0);

	camera_menu();

	if ($camera === false) {
		return;
	}
	if (!$camera) {
		register_error('camerarequest:error:nonexistent_LINE', ['ID' => $partyid]);
		return;
	}

	$camera['PARTYID'] = $partyid;
	extract($camera, \EXTR_OVERWRITE);

	if (false === ($requestlist = db_rowuse_hash(['camerarequest','user'], "
		SELECT	USERID, camerarequest.STATUS, camerarequest.CSTAMP, REQMSG, REQUESTED,
				user.NICK, ADDRESS, ZIPCODE, EMAIL, PHONE, REALNAME, CITYID, user_account.STATUS AS USER_STATUS
		FROM camerarequest
		LEFT JOIN user USING (USERID)
		LEFT JOIN user_account USING (USERID)
		WHERE PARTYID = $partyid"))
	) {
		return;
	}
	if (($is_camera_admin = have_admin('camerarequest'))
	||	!empty($write)
	) {
		layout_open_menu();
		layout_menuitem(__C('action:change'), "/camera/$partyid/form");
		layout_close_menu();
	}
	if ($is_camera_admin
	&&	$TICKETID
	) {
		layout_open_menu();
		if (($template = $STATUS === 'accepted' ? 'camerathanks' : 'cameraoffer')
		&&	CURRENTSTAMP < $camera['STAMP']
		) {
			layout_menuitem(__C('action:make_response'),'/ticket/'.$TICKETID.'?USETEMPLATE='.$template.';PARTYID='.$partyid);
			?> (<?= __('attrib:thanks') ?>)<?
		}
		if (CURRENTSTAMP > $camera['STAMP']
		&&	(require_once 'defines/camera.inc')
		&&	is_accepted_camera_status($camera['STATUS'])
		) {
			layout_menuitem(__C('action:make_response'),'/ticket/'.$TICKETID.'?USETEMPLATE=camerapublished;PARTYID='.$partyid);
			?> (<?= __('status:published') ?>)<?
		}
		layout_close_menu();
	}

	require_once '_ticketlist.inc';
	show_connected_tickets();

	include_style('camerarequest');

	if ($CANCELLED) {
		?><h3 class="error"><?= __C('status:cancelled') ?>!</h3><?
	}
	if ($MOVEDID) {
		?><h3 class="warning"><?= __C('status:moved') ?>!</h3><?
	}
	$show_requested = false;
	if ($requestlist) {
		foreach ($requestlist as $userid => &$request) {
			$request['NICK'] = memcached_nick($userid);
			if (!$show_requested
			&&	SMALL_SCREEN
			&&	$request['REQUESTED']
			) {
				$show_requested = true;
			}
		}
		unset($request);
		string_asort($requestlist, 'NICK');
	}

	require_once '_customer.inc';

	$customerstr =
		is_customer_event($partyid)
	?	' <div class="win ib">&rarr; '.Eelement_name('customer').'</div>'
	:	'';

	layout_open_box($ACTIVE || $ACTIVE === null ? 'camera' : 'light camera');
	layout_box_header(get_element_link('party', $partyid, $NAME).$customerstr, $ACTIVE ? null : __('status:inactive'));


	require_once '_uploadimage.inc';
	$front_image = uploadimage_get('party',$partyid);
	if (have_uploadimage($front_image)) {
		?><div class="r block"><?
		uploadimage_show_from_img($front_image, UPIMG_NOCHANGE);
		?></div><?
	}

	layout_open_table(TABLE_VTOP);
	layout_start_row();
		echo Eelement_name('date');
		layout_field_value();
		dateday_display_link($STAMP);
	layout_stop_row();

	// organizations
	if ($orglist = db_rowuse_array(['connect', 'organization'], "
		SELECT ASSOCID,NAME
		FROM connect LEFT JOIN organization ON ORGANIZATIONID = ASSOCID
		WHERE MAINTYPE = 'party'
		  AND ASSOCTYPE = 'organization'
		  AND MAINID = $partyid")
	) {
		$size = count($orglist);
		layout_start_row();
		echo Eelement_name('organization', isset($orglist[1]));
		layout_field_value();
		foreach ($orglist as $org) {
			echo get_element_link('organization', $org['ASSOCID'], $org['NAME']);
			if (--$size) {
				?>, <?
			}
		}
		layout_stop_row();
	}
	if ($LOCATIONID) {
		layout_start_row();
		echo Eelement_name('location');
		layout_field_value();
		echo get_element_link('location',$LOCATIONID,$LOCATION_NAME);
		layout_stop_row();
	}

	if ($OFFER) {
		layout_start_row();
		echo Eelement_name('offer');
		layout_field_value();
		echo __($OFFER ? 'answer:yes' : 'answer:no');
	}

	if ($EXTERN) {
		layout_restart_row();
		echo Eelement_name('external_supply');
		layout_field_value();
		echo __($EXTERN ? 'answer:yes' : 'answer:no');
		if ($EXTERN) {
			require_once '_camreqspec.inc';
			?> <? show_camreq_icon('extern');
		}
	}

	if ($ENTRY) {
		layout_restart_row();
			echo Eelement_name('entry');
			layout_field_value();
			echo __('entry:'.$ENTRY);
	}

	if ($WRITTEN) {
		layout_restart_row();
		echo __C('field:written');
		layout_field_value();
		echo __($WRITTEN ? 'answer:yes' : 'answer:no');
		if ($REQUESTBYSELF
		&&	$REQUESTFOR
		) {
			layout_restart_row();
			echo __C('field:written_by');
			layout_field_value();
			?><b><?= get_element_link('user', $REQUESTFOR) ?></b><?
		}

		if ($WRITTEN > 1) {
			layout_restart_row();
			echo Eelement_name('written_date');
			layout_field_value();
			_datedaytime_display($WRITTEN);
		}
	}

	require_once '_viavia.inc';
	show_viavia_rows($camera);

	layout_restart_row();
		echo Eelement_name('status');
		layout_field_value();
		if ($STATUS === null) {
			mail_log('camera.STATUS seems NULL', get_defined_vars());
		}
		show_camera_status($STATUS);

	require_once '_camreqspec.inc';
	show_camreq_specs($camera,CAMREQ_ROWS);

	layout_stop_row();
	layout_close_table();

	if ($OFFERMSG) {
		layout_box_header(Eelement_name('offer'));
		?><div class="cam-bg-msg body block"><?= make_all_html($OFFERMSG, UBB_UTF8) ?></div><?
	}

	if ($is_admin_or_photographer
	&&	$MSG
	) {
		layout_box_header(Eelement_name('comment_for_photographer'));
		?><div class="cam-bg-msg body block"><?= make_all_html($MSG, UBB_UTF8) ?></div><?
	}

	layout_display_alteration_note($camera);
	layout_close_box();

	if ($is_admin_or_photographer
	&&	$partyid
	&&	have_admin('photographer')
	) {
		$request = $requestlist[CURRENTUSERID] ?? null;

		if ($_REQUEST['ACTION'] === 'request') {
			?><form<?
			?> accept-charset="utf-8"<?
			?> onsubmit="return submitForm(this)"<?
			?> method="post"<?
			?> action="/camera/<?= $partyid ?>/commitrequest"><?

			layout_open_box('camera');
			layout_box_header(Eelement_name('camerarequest'));

			$party = memcached_party_and_stamp($partyid);

			?><div class="block"><?= __('camera:request:request_comments_info_TEXT', DO_NL2BR | DO_UBB) ?></div><?
			?><div class="block"><?

			$longer_than_a_day = $party['DURATION_SECS'] > ONE_DAY;

			show_textarea([
				'required'		=> $longer_than_a_day,
				'placeholder'	=> $longer_than_a_day ? __('camera:request:multi_day_choose_placeholder_TEXT', DO_NL2BR) : false,
				'name'			=> 'REQMSG',
				'cols'			=> 60,
				'rows'			=> empty($request['REQMSG']) ? 1 : 5,
				'class'			=> 'growToFit',
				'value_utf8'	=> $request['REQMSG'] ?? '',
			]);
			?></div><?
			layout_close_box();

			?><div class="block"><input type="submit" name="<?= $request ? 'CHANGE' : 'ADD' ?>" value="<?= __($request ? 'action:change' : 'action:add') ?>" /></div><?
			?></form><?
		} else {
			show_camreq_menu($camera, $request);
		}
	}
	?><div class="clear"></div><?
	if (!$requestlist || $_REQUEST['ACTION'] === 'request') {
		return;
	}
	$okparty = $is_camera_admin && !$camera['MOVEDID'] && !$camera['CANCELLED'] && $camera['ACCEPTED'];

	if ($okparty) {
		include_js('js/hiliting');
		include_js('js/form/camerarequest');
		?><form method="get" action="/ticket/outgoing-form" onsubmit="return submitRequestForm(this)"><?
		?><input type="hidden" name="ELEMENT" value="camerarequest" /><?
		?><input type="hidden" name="ID" value="<?= $partyid ?>" /><?
		?><input type="hidden" name="USETEMPLATE" value="camerarequest" /><?
	}

	layout_open_box('camera');
	layout_box_header(Eelement_plural_name('photographer'));
	layout_open_table(TABLE_FULL_WIDTH | TABLE_VTOP,'ptable','tmphidden');
	if ($is_camera_admin) {
		?><colgroup><?
			?><col id="requestforcol" data-error="<?= __('camera:error:choose_photographer_LINE') ?>"><?
		?></colgroup><?
	}

	$cols = 0;

	layout_start_header_row();
	if ($okparty) {
		layout_header_cell(null,0,'tmphidden');
		++$cols;
	}
	layout_header_cell(Eelement_name('photographer'));
	++$cols;

	if ($is_camera_admin) {
		if (!SMALL_SCREEN) {
			layout_header_cell();
			layout_header_cell();
			layout_header_cell();
			layout_header_cell();
			$cols += 4;
		}
	}
	layout_header_cell_right(Eelement_name('request_date'));
	++$cols;
	if ($show_requested) {
		layout_header_cell_right(Eelement_name('written_date'));
		++$cols;
	}
	if ($STATUS) {
		layout_header_cell_right(Eelement_name('status'));
		++$cols;
	}
	layout_stop_header_row();
	$requestcnt = count($requestlist);
	foreach ($requestlist as $request) {
		extract($request, \EXTR_OVERWRITE);
		?><tr class="hl not-light-hilited<?
		if (!$STATUS) {
			if ($WRITTEN && $REQUESTED) {
				?> cam-bg-written<?
			}
		} else {
			?> cam-bg-<? echo $STATUS;
		}
		?>"><?
		$somebody = false;
		if ($is_camera_admin) {
			?><td class="tmphidden center"><?
			?><input<?
			?> class="upLite"<?
			?> id="requestfor<?= $USERID ?>"<?
			?> onclick="clearRefill.call(getobj('requestforcol'),event)"<?
			?> type="checkbox"<?
			?> name="REQUESTFOR[]"<?
			?> value="<?= $USERID ?>"<?
			if ($requestcnt === 1) {
				?> checked<?
			}
			?> /><?
			?></td><?
		}
		?><td<?
		if ($USER_STATUS !== 'active') {
			?> class="unavailable"<?
		}
		?>><?
		if ($is_camera_admin) {
			$info = [];
			if ($REALNAME) {
				$info[] = escape_specials($REALNAME);
			}
			if ($ADDRESS) {
				$info[] = escape_specials($ADDRESS);
			}
			if ($ZIPCODE || $CITYID) {
				$info[] = ($ZIPCODE ? escape_specials($ZIPCODE) : null).' '.($CITYID ? get_element_title('city',$CITYID) : null);
			}
			if ($PHONE) {
				$info[] = '';
				$info[] = escape_specials($PHONE);
			}

			$info[] = '';
			$info[] = escape_specials($EMAIL);

			if ($USERID) {
				require_once '_bubble.inc';
				$bubble = new bubble(BUBBLE_CLEAN | BUBBLE_CURSOR);
				$bubble->catcher(get_element_link('user',$USERID));//'<label for="requestfor'.$USERID.'">'.escape_specials($NICK).'</label>');
				$bubble->content(implode('<br />', $info));
				$bubble->display();
			} else {
				echo __('field:anonymous');
			}
		} else {
			echo $USERID ? get_element_link('user',$USERID) : __('field:anonymous');
		}

		if ($is_camera_admin) {
			$stats = get_photographer_stats($USERID);

			if (SMALL_SCREEN) {
				foreach ($stats as $i => $data) {
					if (!$data) {
						unset($stats[$i]);
					}
				}
				?><br /><?
				?><span class="small"><?
				echo  implode(', ', $stats);
				?></span><?
				?></td><?
			} else {
				?></td><?
				?><td class="right"><?= $stats['average_photo_count'] ?></td><?
				?><td><?= $stats['average_rating'] ?></td><?
				?><td><?= $stats['average_delay'] ?></td><?
				?><td><?= $stats['failures'] ?></td><?
			}
		} else {
			?></td><?
		}

		if ($somebody) {
			?><td></td><?
		} else {
			?><td class="right"><?
			if ($CSTAMP) {
				_datetime_display($CSTAMP, short: true);
			}
			?></td><?
		}
		if ($show_requested) {
			?><td class="right"><?
			switch ($REQUESTED) {
			case 1: echo __('answer:yes');
			case 0: break;
			default: _datetime_display($REQUESTED, short: true);
			}
			?></td><?
		}
#		if ($STATUS) {
			?><td class="right"><? show_camera_status($STATUS) ?></td><?
#		}
		?></tr><?
		if ($REQMSG
		&&	(	$is_camera_admin
			||	$USERID === CURRENTUSERID
			)
		) {
			?><tr><td style="padding:.3em 2em;font-style:italic" colspan="<?= $cols ?>"><?= make_all_html($REQMSG, UBB_UTF8) ?></td></tr><?
		}
	}
	layout_close_table();
	layout_close_box();
	if ($okparty) {
		if ($TICKETID) {
			$contactoptions[element_name('offer').' ('.element_name('contact_ticket').')'] = 'tTYPE=offer;TICKETID='.$TICKETID;

			$tickets = db_simpler_array('contact_ticket','
				SELECT TICKETID
				FROM contact_ticket
				WHERE ELEMENT="camerarequest"
				  AND ID = '.$partyid.'
				  AND TICKETID != '.$TICKETID
			);
			if ($tickets) {
				foreach ($tickets as $ticketid) {
					$contactoptions[element_name('contact_ticket').': '.$ticketid] = 'tTYPE=ticket;TICKETID='.$ticketid;
				}
			}
		} elseif ($OFFER === 2) {
			$contactoptions[element_name('offer').' ('.escape_specials(memcached_nick($CUSERID)).')'] = 'tTYPE=offer;tUSERID='.$CUSERID;
		}
		if ($connects = db_simple_hash(['connect', 'party', 'hidden_orgs', 'organization', 'subof'], '
			SELECT	ASSOCTYPE, ASSOCID,
				CASE ASSOCTYPE
				WHEN "organization"
				THEN  10
				WHEN "location"
				THEN  20
				END
			FROM (
				SELECT ASSOCTYPE, ASSOCID, PELEMENT
				FROM connect
				JOIN organization ON ASSOCID = ORGANIZATIONID
				LEFT JOIN hidden_orgs ON ORGANIZATIONID = ID
				LEFT JOIN subof ON ELEMENT = "organization" AND CONCEPTID = ASSOCID
				WHERE MAINTYPE = "party"
				  AND ASSOCTYPE = "organization"
				  AND MAINID = '.$partyid.'
				  AND hidden_orgs.ID IS NULL
			UNION
				SELECT "location" AS ASSOCTYPE, LOCATIONID AS ASSOCID, NULL AS PELEMENT
				FROM party
				WHERE LOCATIONID != 0
				  AND PARTYID = '.$partyid.'
 			) AS connections
			ORDER BY
				ASSOCTYPE = "organization" DESC,
				PELEMENT IS NULL')
		) {
			foreach ($connects as $element => $ids) {
				foreach ($ids as $id => $history) {
					$contactoptions[element_name($element).': '.escape_specials(get_element_title($element,$id))] = 'tTYPE='.$element.';tID='.$id;
				}
			}
		}
		$contactoptions[escape_specials(element_name('photographer'))] = 'tTYPE=photographer';
		$contactoptions[__('form:select:other_option').'&hellip;'] = 'tTYPE=other';

		?><div onclick="
			remclass('ptable', 'tmphidden')'
			hide(this);
			unhide(this.nextSibling);
			<? if ($requestcnt === 1) { ?>
				(function(){
					const o = getobj('requestfor<?= $USERID ?>', true);
					if (o) {
						o.checked = true;
						Pf_updateBox(o);
					}
				})();
			<? } ?>"<?
		?> class="unhideanchor block"><?= __C('action:request(permission)') ?></div><?

		?><div class="hidden" id="requestpart"><?
		?><div class="block"><?

		$cnt = count($contactoptions);
		foreach ($contactoptions as $name => $argstr) {
			?><div><?
			?><label class="not-hilited"><?
			show_input([
				'required'	=> true,
				'class'		=> 'upLite',
				'type'		=> 'radio',
				'name'		=> 'ARGS',
				'value'		=> escape_specials($argstr),
			]);
			?> <?= $name
			?></label><?
			?></div><?
		}
		?></div><?
		?><div class="block"><input type="submit" value="<?= __('action:request(permission)') ?>" /></div><?
		?></div><?
		?></form><?
	}
}

function camera_commit(): bool {
	if (!have_number_or_empty($_POST, 'GLISTPLUS')
	||	false === require_number($_POST, 'CONSUMPTIONS')
	||	false === require_element($_POST, 'TRAVEL_EXPENSES', ['', 0, 1, 2])
	||	false === require_element($_POST, 'REWARD_EXTERNAL', ['', 0, 1])
	||	false === require_number($_POST, 'REWARD_EXTERNAL_VALUE')
	||	!require_anything_trim($_POST, 'OFFERMSG', utf8: true)
	||	!($partyid = $_REQUEST['sID'] ?: require_idnumber($_POST, 'PARTYID'))
	||	false === ($camera = db_single('camera','SELECT 1 FROM camera WHERE PARTYID='.$partyid, DB_USE_MASTER))
	||	false === require_enum($_POST, 'ENTRY', 'camera', 'ENTRY')
	||	!($partyid = $_REQUEST['sID'] ?: require_idnumber($_POST, 'PARTYID'))
	) {
		return false;
	}
	if (!($is_camera_admin = have_admin('camerarequest'))) {
		# organizations can add an offer
		if ($camera) {
			require_once '_element_access.inc';
			[$read, $write] = element_access('camerarequest', $partyid);
			if (!$write) {
				require_admin('camerarequest');
				return false;
			}
			if (false === require_element($_POST, 'TRAVEL_EXPENSES', ['', 0, 1])) {
				return false;
			}
			$active = isset($_POST['ACTIVE']);
		}  else {
			if (!($parties = parties_for_employer())
			||	!!isset($parties[$partyid])
			) {
				require_admin('camerarequest');
				return false;
			}
			$active = true;
		}
		$setlist[] = 'OFFER = 2';
		$setlist[] = 'MSG = ""';
	} else {
		if (false === require_enum($_POST, 'STATUS', 'camera')
		||	false === require_number($_POST, 'REQUESTFOR')
		||	false === require_element($_POST, 'REWARD_FLOCK', ['', 0, 1])
		||	false === require_element($_POST, 'TRAVEL_EXPENSES', ['', 0, 1, 2])
		||	false === require_number($_POST, 'REWARD_FLOCK_VALUE')
		||	!require_anything_trim($_POST, 'MSG', utf8: true)
		||	!optional_number($_POST, 'TICKETID')
		) {
			return false;
		}
		if (isset($_POST['NEWUSER'])) {
			if (!require_array($_POST, 'NEWUSER')) {
				return false;
			}
			$size = count($_POST['NEWUSER']);
			foreach ($_POST['NEWUSER'] as $userid) {
				$new_status = array_shift($_POST['NEWSTATUS']);
				if (!$userid) {
					$userid = 0;
				} else {
					if (empty($userid)) {
						continue;
					}
					if (!is_number($userid)) {
						return false;
					}
				}
				$inslist[] = '('.$userid.','.CURRENTSTAMP.','.CURRENTSTAMP.','.CURRENTUSERID.','.$partyid.',"'.addslashes($new_status).'","")';
			}
		}
		if (isset($_POST['USER'])) {
			if (!require_array($_POST, 'USER')) {
				return false;
			}
			foreach ($_POST['USER'] as $userid => $status) {
				if (!is_number($userid)
				||	false === require_enum($_POST['USER'], $userid, 'camerarequest', 'STATUS')
				) {
					_error(escape_specials('USER['.$userid.']='.$status.' is niet correct'));
					return false;
				}
			}
			foreach ($_POST['USER'] as $userid => $status) {
				if (!db_insert('camerarequest_log', "
					INSERT INTO camerarequest_log
					SELECT * FROM camerarequest
					WHERE STATUS != '".addslashes($status)."'
					  AND USERID = $userid
					  AND PARTYID = $partyid")
				||	db_affected()
				&&	!db_update('camerarequest', '
					UPDATE camerarequest SET
						MSTAMP	= '.CURRENTSTAMP.',
						MUSERID	= '.CURRENTUSERID.",
						STATUS	= '".addslashes($status)."'
					WHERE STATUS != '".addslashes($status)."'
					  AND USERID = $userid
					  AND PARTYID = $partyid")
				) {
					return false;
				}
			}
		}
		if (!empty($inslist)) {
			if (!db_insert('camerarequest','
				INSERT IGNORE INTO camerarequest (USERID, CSTAMP, MSTAMP, MUSERID, PARTYID, STATUS, REQMSG)
				VALUES '.implode(', ', $inslist))
			) {
				return false;
			}
		}
		$setlist[] = 'REWARD_FLOCK	='.(!strlen($_POST['REWARD_FLOCK'])	? 'NULL' : (!$_POST['REWARD_FLOCK'] ? 0 : $_POST['REWARD_FLOCK_VALUE']));
		$setlist[] = 'STATUS		="'.addslashes($_POST['STATUS']).'"';
		$setlist[] = 'WRITTEN		='.(isset($_POST['WRITTEN']) ? 'IF(WRITTEN,WRITTEN,UNIX_TIMESTAMP())' : '0');
		$setlist[] = 'MSG			="'.addslashes(utf8_mytrim($_POST['MSG'])).'"';
		$setlist[] = 'OFFER			='.(isset($_POST['OFFER']) ? 1 : 0);
		$setlist[] = 'REQUESTBYSELF	='.(isset($_POST['REQUESTBYSELF']) ? "b'1'" : "b'0'");
		$setlist[] = 'REQUESTFOR	='.$_POST['REQUESTFOR'];

		if (isset($_POST['TICKETID'])) {
			$setlist[] = 'TICKETID='.$_POST['TICKETID'];
		}
		$active = isset($_POST['ACTIVE']);
	}

	require_once '_viavia.inc';
	add_viavia_to_setlist($setlist);
	$setlist[] = 'ACTIVE			=b\''.($active ? 1 : 0).'\'';
	$setlist[] = 'ENTRY				="'.addslashes($_POST['ENTRY']).'"';
	$setlist[] = 'GLISTPLUS			='.(strlen($_POST['GLISTPLUS']) ? $_POST['GLISTPLUS'] : 'NULL') ;
	$setlist[] = 'EXTERN			=b\''.(isset($_POST['EXTERN']) ? 1 : 0).'\'';
	$setlist[] = 'CONSUMPTIONS		='.($_POST['CONSUMPTIONS'] == 256 ? 'NULL' : $_POST['CONSUMPTIONS']);
	$setlist[] = 'TRAVEL_EXPENSES	='.(!strlen($_POST['TRAVEL_EXPENSES'])	? 'NULL' : 'b\''.decbin($_POST['TRAVEL_EXPENSES']).'\'');
	$setlist[] = 'REWARD_EXTERNAL	='.(!strlen($_POST['REWARD_EXTERNAL'])	? 'NULL' : (!$_POST['REWARD_EXTERNAL'] ? 0 : $_POST['REWARD_EXTERNAL_VALUE']));
	$setlist[] = 'OFFERMSG			="'.addslashes(utf8_mytrim($_POST['OFFERMSG'])).'"';

	if (!db_insert('camera_log', '
		INSERT INTO camera_log
		SELECT * FROM camera
		WHERE NOT '.binary_equal($setlist).'
		  AND PARTYID='.$partyid)
	) {
		return false;
	}

	if (db_affected() || !$camera) {
		if (!db_update('camera','
			INSERT INTO camera SET '.
				($setstr = implode(',',$setlist)).',
				PARTYID	='.$partyid.',
				CUSERID	='.CURRENTUSERID.',
				CSTAMP	='.CURRENTSTAMP.',
				MUSERID	='.CURRENTUSERID.',
				MSTAMP	='.CURRENTSTAMP.'
			ON DUPLICATE KEY UPDATE '.
				$setstr.',
				MUSERID	=VALUES(MUSERID),
				MSTAMP	=VALUES(MSTAMP)')
		) {
			return false;
		}
		$_REQUEST['sID'] = $partyid;
	}
	register_notice($camera ? 'camerarequest:notice:changed_LINE' : 'camerarequest:notice:added_LINE');
	return true;
}

function camera_remove(): bool {
	if (!require_admin('photographer')
	||	!($partyid = require_idnumber($_REQUEST,'sID'))
	||	!require_getlock('camera_'.$partyid, 1)
	) {
		return false;
	}
	if (!($camera = db_single_assoc('camera','SELECT WRITTEN, OFFER, OFFERMSG FROM camera WHERE PARTYID='.$partyid))) {
		if ($camera === null) {
			register_error('camerarequest:error:nonexistent_LINE', ['ID' => $partyid]);
		}
		return false;
	}
	if ($camera['WRITTEN']) {
		require_once 'defines/camera.inc';
		register_error('camerarequest:error:already_written_TEXT', DO_UBB | DO_NL2BR, ['PARTYID' => $partyid, 'HEADADMINID' => get_camerarequest_head('USERID')]);
		return false;
	}
	if (!db_insert('camerarequest_log', '
		INSERT INTO camerarequest_log
		SELECT * FROM camerarequest
		WHERE USERID = '.CURRENTUSERID."
		  AND PARTYID = $partyid
		  AND (STATUS IS NULL OR STATUS = '')")
	) {
		return false;
	}
	if (!db_affected()) {
		register_notice('camerarequest:error:irremovable_status_changed_LINE');
		return false;
	}
	if (!db_insert('deleted', "
		INSERT INTO deleted SET
			ELEMENT	= 'camerarequest',
			ID		= $partyid,
			DUSERID	= ".CURRENTUSERID.',
			DSTAMP	= '.CURRENTSTAMP)
	||	!db_delete('camerarequest','
		DELETE FROM camerarequest
		WHERE USERID  = '.CURRENTUSERID.'
		  AND PARTYID = '.$partyid)
	) {
		return false;
	}
	if (!$camera['OFFER']
	&&	!$camera['OFFERMSG']
	) {
		if (!db_lock_tables(['camerarequest', 'camera'])) {
			return false;
		}
		if (!($request_count = db_single('camerarequest', "
			SELECT COUNT(*)
			FROM camerarequest
			WHERE PARTYID = $partyid",
			DB_USE_MASTER))
		) {
			if ($request_count === false) {
				return false;
			}
			if (!$request_count) {
				if (!db_delete('camera', "
					DELETE FROM camera
					WHERE PARTYID = $partyid")
				) {
					return false;
				}
			}
		}
		db_unlock_tables();

		if (!db_insert('deleted', "
			INSERT INTO deleted SET
				ELEMENT	= 'camera',
				ID		= $partyid,
				DUSERID	= ".CURRENTUSERID.',
				DSTAMP	= '.CURRENTSTAMP)
		) {
			return false;
		}
	}
	register_notice('camerarequest:notice:removed_LINE');
	return true;
}

function camera_commit_request(): bool {
	if (!require_admin('photographer')
	||	!($partyid = require_idnumber($_REQUEST, 'sID'))
	||	!require_anything_trim($_POST, 'REQMSG', utf8: true)
	||	!require_getlock('camera_'.$partyid, 1)
	) {
		return false;
	}

	require_once '_okpartyforphotos.inc';

	if (!($wantparty = require_party_for_photos($partyid))) {
		$wantparty === null && not_found();
		return false;
	}

	if (!require_ok_party_for_photos($wantparty)
	||	false === ($camera = db_single_assoc('camera', '
		SELECT STATUS, WRITTEN, ACTIVE
		FROM camera
		WHERE PARTYID = '.$partyid))
	) {
		return false;
	}
	if (!$camera) {
		if (!db_insert('camera','
			INSERT IGNORE INTO camera SET
				OFFERMSG="",
				MSG	="",
				CUSERID	='.CURRENTUSERID.',
				MUSERID	='.CURRENTUSERID.',
				CSTAMP	='.CURRENTSTAMP.',
				MSTAMP	='.CURRENTSTAMP.',
				PARTYID	='.$partyid)
		) {
		}
	} else {
		if ($camera['STATUS']
		||	$camera['WRITTEN']
		) {
			register_warning('camerarequest:warning:on_the_reserve_LINE');
		}
		if (!$camera['ACTIVE']) {
			if (!db_insert('camera_log','
				INSERT INTO camera_log
				SELECT * FROM camera
				WHERE PARTYID='.$partyid)
			||	!db_update('camera','
				UPDATE camera SET
					ACTIVE	=1,
					MUSERID	='.CURRENTUSERID.',
					MSTAMP	='.CURRENTSTAMP.'
				WHERE PARTYID='.$partyid)
			) {
				return false;
			}
		}

	}
	$setlist['REQMSG'] = 'REQMSG="'.addslashes(utf8_mytrim($_POST['REQMSG'])).'"';

	if (isset($_POST['CHANGE'])) {
		if (!db_insert('camerarequest_log','
			INSERT INTO camerarequest_log
			SELECT * FROM camerarequest
			WHERE NOT '.binary_equal($setlist).'
			  AND USERID='.CURRENTUSERID.'
			  AND PARTYID='.$partyid)
		) {
			return false;
		}
		if (!db_affected()) {
			register_notice('camerarequest:warning:nothing_changed_LINE');
			return true;
		}
	}
	if (!db_insupd('camerarequest','
		INSERT INTO camerarequest SET '.
			implode(',',$setlist).',
			PARTYID	='.$partyid.',
			USERID	='.CURRENTUSERID.',
			CSTAMP	='.CURRENTSTAMP.',
			MUSERID	='.CURRENTUSERID.',
			MSTAMP	='.CURRENTSTAMP.',
			LSTAMP	='.CURRENTSTAMP.'
		ON DUPLICATE KEY UPDATE '.
			value_parts($setlist).',
			MUSERID	='.CURRENTUSERID.',
			MSTAMP	='.CURRENTSTAMP.',
			LSTAMP	='.CURRENTSTAMP)
	) {
		return false;
	}
	register_notice(isset($_POST['CHANGE']) ? 'camerarequest:notice:changed_LINE' : 'camerarequest:notice:added_LINE');
	return true;
}

function camera_display_overview() {
	require_once '_camerarequestlist.inc';

	layout_show_section_header();

	$is_cammer		 = have_admin('photographer');
	$is_camera_admin = have_admin('camerarequest');
	$is_aspiring	 = have_admin('aspiring_photographer');
	$is_cammer_or_aspiring = $is_cammer || $is_aspiring;

	if ($am_employee =
		!$is_cammer_or_aspiring
	&&	!$is_camera_admin
	) {
		$am_of_locations	 = am_employee('location');
		$am_of_organizations = am_employee('organization');
		if (!$am_of_locations
		&&	!$am_of_organizations
		) {
			require_admin(['camerarequest', 'photographer', 'aspiring_photographer']);
			return;
		}
	}
	camera_menu();
	camera_single_menu($am_employee);

	if ($am_employee) {
		camera_show_offers($am_of_organizations ?: null, $am_of_locations ?: null);
		return;
	}
	if ($is_cammer_or_aspiring) {
		layout_open_menu();
		layout_menuitem(__C('action:process_upload'), '/camera/upload-form', ' onclick="Pf.markPageBusy(); return true;"');
		layout_close_menu();
	}
	if ($is_cammer_or_aspiring) {
		camerarequestlist_display_user();

		if (db_single('camerarequest','SELECT COUNT(*) FROM camerarequest WHERE USERID = '.CURRENTUSERID) <= 2) {
			layout_open_box('camera');
			layout_box_header(Eelement_plural_name('request'));
			?><div class="block"><?=
				__('camera:info:go_to_party_and_click_you_want_to_shoot_there_LINE', DO_NL2BR);
			?></div><?
			layout_close_box();
		}
		camera_show_offers();
	}
	if ($is_camera_admin) {
		if (false === ($awaiting = db_rowuse_array(['camera', 'party', 'camerarequest', 'user_account'], '
			SELECT	camera.PARTYID,
					party.NAME,SUBTITLE,STAMP,
					GROUP_CONCAT(camerarequest.USERID) AS USERIDS
			FROM camera
			JOIN party
			  ON party.PARTYID = camera.PARTYID
			JOIN camerarequest
			  ON camerarequest.PARTYID = camera.PARTYID
			 AND camerarequest.STATUS IS NOT NULL
			 AND camerarequest.STATUS != ""
			 AND camerarequest.STATUS IN ("accepted", "self", "extern")
			 AND NOT PROCESSING
			LEFT JOIN user_account
			  ON user_account.USERID = camerarequest.USERID
			LEFT JOIN rights
			  ON rights.USERID = camerarequest.USERID AND rights.`PORTION` = "photographer"
			LEFT JOIN image
			  ON image.PARTYID = camera.PARTYID
			 AND image.USERID = camerarequest.USERID
			WHERE camera.STATUS IN ("accepted", "self", "extern")
			  AND (		 STAMP BETWEEN '.(TODAYSTAMP - 2 * ONE_MONTH).' AND '.(TODAYSTAMP + 25 * ONE_HOUR).'
					OR	camera.CSTAMP >= UNIX_TIMESTAMP() - '.ONE_WEEK.'
					)
			  AND MOVEDID = 0
			  AND CANCELLED = 0
			GROUP BY camera.PARTYID, camerarequest.USERID
			HAVING 0 = COUNT(image.IMGID)
			ORDER BY STAMP'))
		) {
			return;
		}
		if ($awaiting) {
			layout_open_box('camera');
			layout_box_header(Eelement_name('expected_photo_set', count($awaiting)));
			layout_open_table(TABLE_FULL_WIDTH | TABLE_VTOP);
			foreach ($awaiting as $await) {
				$userids = explode(',',$await['USERIDS']);
				$size = count($userids);
				// DATE
				layout_start_rrow_right();
				dateday_display_link($await['STAMP'],' ',false,false,true);
				// PARTY
				layout_next_cell(class: 'lpad');
				?><a href="/camera/<?= $await['PARTYID']?>"><?= escape_utf8($await['NAME']) ?></a></span><?
				if ($await['SUBTITLE']) {
					?><small> <?= MIDDLE_DOT_ENTITY ?> <?= escape_utf8($await['SUBTITLE']) ?></small><?
				}
				// NICKS + PROCESS_ACTION for SMALL_SCREEN
				if (SMALL_SCREEN) {
					?><ul class="fw"><?
					foreach ($userids as &$userid) {
						$userid = (int)$userid;
						?><li><?= $userid ? get_element_link('user', $userid) : __('field:anonymous');
						?><span class="r"><?
						?><a onclick="Pf.markPageBusy(); return true;" href="/camera/upload-form?USERID=<?= $userid ?>"><?= __('action:process_photos') ?></span><?
						?></li><?
					}
					?></ul><?
				} else {
					layout_next_cell();
					foreach ($userids as $userid) {
						$userid = (int)$userid;
						echo  $userid ? get_element_link('user',$userid) : __('field:anonymous') ?><br /><?
					}
				}

				// PROCESS_ACTION for DESKTOP
				if (!SMALL_SCREEN) {
					layout_next_cell(class: 'right');
					foreach ($userids as $userid) {
						$userid = (int)$userid;
						?><a onclick="Pf.markPageBusy(); return true" href="/camera/upload-form?USERID=<?= $userid ?>"><?= __('action:process_photos') ?></a><br /><?
					}
				}
				layout_stop_row();
			}
			layout_close_table();
			layout_close_box();
		}
		camera_show_offers();
	}

	///////////// show cammers

	if ($is_cammer
	||	$is_camera_admin
	) {
		camerarequestlist_display_overview();
	}
}

function camera_show_offers(?array $am_of_organizations = null, ?array $am_of_locations = null): void {
	if ($mine
	=	$am_of_organizations
	||	$am_of_locations
	) {
		if ($am_of_locations) {
			$joins[] = 'SELECT PARTYID FROM party WHERE LOCATIONID IN ('.implode(', ', $am_of_locations).')';
		}
		if ($am_of_organizations) {
			$joins[] = '
				SELECT DISTINCT PARTYID
				FROM party
				JOIN connect ON
						ASSOCTYPE	= "party"
					AND ASSOCID		= PARTYID
					AND MAINTYPE	= "organization"
					AND MAINID IN ('.implode(', ', $am_of_organizations).')';
		}
		$offers = db_rowuse_hash(['camera', 'party', 'camerarequest', 'location', 'boarding', 'city'], '
			SELECT	PARTYID,camera.WRITTEN,camera.STATUS,camera.OFFERMSG, GLISTPLUS,CONSUMPTIONS,TRAVEL_EXPENSES,REWARD_EXTERNAL,REWARD_FLOCK,ACTIVE,EXTERN, OFFER,
					party.NAME,party.STAMP,
					party.LOCATIONID,location.NAME AS LNAME,
					location.CITYID,
					COUNT(camerarequest.PARTYID) AS REQCNT
			FROM camera
				LEFT JOIN camerarequest USING (PARTYID)
				LEFT JOIN party USING (PARTYID)
				LEFT JOIN location USING (LOCATIONID)
				LEFT JOIN boarding USING (BOARDINGID)
				LEFT JOIN city ON city.CITYID=COALESCE(boarding.CITYID,location.CITYID,party.CITYID)
			WHERE PARTYID IN ('.implode(' UNION ',$joins).')
			GROUP BY camera.PARTYID
			ORDER BY party.STAMP DESC'
		);
	} else {
		require_once '_favourite.inc';
		if (false === ($interesting = db_rowuse_hash(['camera', 'party', 'camerarequest', 'location', 'boarding', 'city'],'
			SELECT	camera.PARTYID, camera.WRITTEN, camera.STATUS, camera.OFFERMSG, GLISTPLUS, CONSUMPTIONS ,TRAVEL_EXPENSES, REWARD_EXTERNAL, REWARD_FLOCK, ACTIVE, EXTERN, OFFER,
					party.NAME,party.STAMP,
					party.LOCATIONID,location.NAME AS LNAME,
					location.CITYID,
					COUNT(camerarequest.PARTYID) AS REQCNT
			FROM camera
			LEFT JOIN party USING (PARTYID)
			LEFT JOIN location USING (LOCATIONID)
			LEFT JOIN boarding USING (BOARDINGID)
			LEFT JOIN city ON city.CITYID = COALESCE(boarding.CITYID, location.CITYID, party.CITYID)
			LEFT JOIN camerarequest
				   ON camerarequest.PARTYID = camera.PARTYID
				  AND (	camerarequest.STATUS IS NULL
				    OR	camerarequest.STATUS = ""
				  	OR	camerarequest.STATUS NOT IN (
						"accepted",
						"denied",
						"extern",
						"failed_organization",
						"failed_photographer",
						"chosen",
						"not_chosen",
						"no_response"))
			WHERE party.STAMP >= '.TODAYSTAMP.'
			  AND (OFFER > 0 OR EXTERN)
			  AND MOVEDID = 0
			  AND POSTPONED = 0
			  AND CANCELLED = 0
			GROUP BY camera.PARTYID
			HAVING NOT REQCNT
			   AND SUM(ACTIVE) /* hide deleted */
			ORDER BY party.STAMP'))
		) {
			return;
		}
		$favinfo = new favouriteinfo($interesting);
		if (!$favinfo->have_favourites_for_parties()) {
			unset($favinfo);
		}
		if ($interesting) {
			foreach ($interesting as $partyid => $camera) {
				if ($camera['EXTERN']) {
					$externs[$partyid] = $camera;
				}
				if ($camera['OFFER']) {
					$offers[$partyid] = $camera;
				}
			}
		}
	}
	if (!empty($offers)) {
		show_camera_offers($offers, $mine);
	}
	if (!empty($externs)) {
		show_camera_offers($externs, false, true);
	}
}

function show_camera_offers($offers,$mine,$extern = false) {
	$is_camera_admin = have_admin('camerarequest');

	$collapse = false; // $is_camera_admin && !$extern;

	include_style('camerarequest');
	layout_open_box('camera');
	if ($collapse) {
		ob_start();
		expand_collapse('offers');
		$expandy = ob_get_clean();
		?><div class="ptr" onclick="<? expand_collapse_clicker('offers') ?>"><?
	}
	layout_box_header(
		Eelement_plural_name($extern ? 'external_supply' : ($mine ? 'camerarequest' : 'offer')),
		$expandy ?? null
	);
	if ($collapse) {
		?></div><?
		?><div class="hidden" id="offers"><?
	}
	require_once '_camreqspec.inc';
	layout_open_table('default fw vtop hha');
	?><tbody><?
	$future = null;
	foreach ($offers as $offer) {
		/** @noinspection PhpRedundantOptionalArgumentInspection */
		extract($offer, \EXTR_OVERWRITE);
		$isfuture = $STAMP > CURRENTSTAMP;
		if ($future !== $isfuture) {
			if ($future) {
				?></tr></tbody><?
				?><tbody><tr class="nohl"><td colspan="10"><?
				layout_box_header(__C('date:past'));
				?></td></tr></tbody><?
				?><tbody><?
			}
			$future = $isfuture;
		}
		layout_start_rrow(0,!$ACTIVE ? 'light' : null,'nowrap right rpad');
		dateday_display_link($STAMP,' ',false,true,true);

		?></td><td class="lpad forcewrap" id="namecell_<?= $PARTYID ?>"><?
 		?><a<?
 		if (isset($showname)) {
 			?> title="<?= escape_utf8($NAME);
 			?>"<?
 		}
 		?> id="offerlink_<?= $PARTYID ?>" href="/camera/<?= $PARTYID ?>"><?=
 			escape_utf8($NAME)
 		?></a><?
 		if (!SMALL_SCREEN) {
			if (!empty($favinfo)) {
				$favinfo->show_stars($PARTYID);
			}
			show_camreq_specs($offer,CAMREQ_CELLS);
		}
		// OFFERMSG?
		layout_next_cell();
		if (!empty($OFFERMSG)) {
			?>&nbsp;<span class="small unhideanchor" onclick="<?
			?>swapdisplay('offermsgrow:<?= $PARTYID ?>');<?
			?>toggleclass('offerlink_<?= $PARTYID ?>', 'selected');<?
			?>toggleclass('namecell_<?= $PARTYID ?>', 'cam-bg-msg')<?
			?>"><?= element_name('comment'); ?></span>&nbsp;<?
		}

		layout_next_cell();
		if ($LOCATIONID) {
			echo get_element_link('location', $LOCATIONID, $LNAME);
		}

		layout_next_cell();
		if ($CITYID) {
			echo get_element_link('city',$CITYID);
		}
		if ($mine) {
			layout_next_cell(class: 'right');
			if ($REQCNT) {
				echo $REQCNT;
			}
		}
		layout_stop_row();
		if (!empty($OFFERMSG)) {
			?><tr id="offermsgrow:<?= $PARTYID ?>" class="hidden"><?
			?><td></td><td class="cam-bg-msg body" colspan="8"><?= nl2br(make_all_html($OFFERMSG, UBB_UTF8));
			?></td></tr><?
		}
	}
	?></tbody><?
	layout_close_table();
	if ($collapse) {
		?></div><?
	}
	layout_close_box();
}
function parties_for_employer(): array|false {
	$am_of_locations	 = am_employee('location');
	$am_of_organizations = am_employee('organization');
	if (!$am_of_locations
	&&	!$am_of_organizations
	) {
		return false;
	}
	$parties = [];
	if ($am_of_locations) {
		if (false === ($newparties = db_rowuse_hash('party','
			SELECT DISTINCT PARTYID, NAME, STAMP, CANCELLED, MOVEDID, ACCEPTED, SUBTITLE
			FROM party
			WHERE LOCATIONID IN ('.implode(', ', $am_of_locations).')
			  AND STAMP > '.(TODAYSTAMP - ONE_DAY)
		))) {
			return false;
		}
		if ($newparties) {
			$parties += $newparties;
		}
	}
	if ($am_of_organizations) {
		if (false === ($newparties = db_rowuse_hash(['party', 'connect'], '
			SELECT DISTINCT PARTYID, NAME, STAMP, CANCELLED, MOVEDID, ACCEPTED, SUBTITLE
			FROM party
			JOIN connect
			  ON MAINTYPE = "organization"
			 AND MAINID IN ('.implode(', ', $am_of_organizations).')
			 AND ASSOCTYPE = "party"
			 AND ASSOCID = PARTYID
			WHERE STAMP > '.(TODAYSTAMP - ONE_DAY)
		))) {
			return false;
		}
		if ($newparties) {
			$parties += $newparties;
		}
	}
	return $parties;
}

function camera_display_settings_form() {
	if (!require_admin(['photographer', 'aspiring_photographer'])) {
		return;
	}

	layout_section_header(Eelement_name('camera'));

	camera_menu();

	if (false === ($gallery_default = db_single_assoc('gallery_default', '
			SELECT *
			FROM gallery_default
			WHERE USERID = '.CURRENTUSERID))
	) {
		return;
	}

	$spec = explain_table('gallery_default');

	?><form<?
	?> accept-charset="utf-8"<?
	?> onsubmit="return submitForm(this)"<?
	?> method="post"<?
	?> action="/camera/commit-settings"><?

	layout_open_box('white');
	layout_box_header(Eelement_plural_name('photo').' '.MIDDLE_DOT_ENTITY.' '.element_plural_name('setting'));
	layout_open_table(TABLE_CLEAN);
	layout_start_row();

	// PHOTOGRAPHER
		echo Eelement_name('photographer');
		layout_field_value();
		show_input([
			'type'			=> 'text',
			'name'			=> 'PHOTOGRAPHER',
			'spec'			=> $spec,
			'value_utf8'	=> $gallery_default,
		]);

	// PHOTOGRAPHER SITE
	layout_restart_row();
		echo Eelement_name('site');
		?> <?
		echo element_name('photographer');
		layout_field_value();
		show_input([
			'type'			=> 'url',
			'name'			=> 'PHOTOGRAPHER_SITE',
			'spec'			=> $spec,
			'value_utf8'	=> $gallery_default,
		]);
	# TEXT BELOW LOGO
	layout_restart_row();
		?><label for="text-overlay"><?= Eelement_name('text_below_logo') ?></label><?
		layout_field_value();
		show_input([
			'type'			=> 'text',
			'id'			=> 'text-overlay',
			'name'			=> 'TEXTOVERLAY',
			'spec'			=> $spec,
			'value_utf8'	=> $gallery_default,
		]);

	// ANONYMOUS
	layout_restart_row();
		?><label for="anonymous"><?= __C('field:anonymous') ?></label><?
		layout_field_value();
		show_input([
			'type'			=> 'checkbox',
			'name'			=> 'ANONYMOUS',
			'id'			=> 'anonymous',
			'spec'			=> $spec,
			'checked'		=> $gallery_default
		]);
	layout_close_table();
	layout_close_box();
	?><div class="block"><?
	?><input type="submit" value="<?= __('action:change') ?>" /> <?
	?><label class="not-hilited"><?
	show_input([
		'type'		=> 'checkbox',
		'name'		=> 'UPDALL',
		'value'		=> 1,
		'class'		=> 'upLite',
	])
	?> <?= __('camera:info:update_all_galleries_LINE')	?></label><?
	?></div><?
	?></form><?
}

function camera_commit_settings(): bool {
	require_once '_postedin.inc';
	require_once '_messageindex.inc';
	require_once '_subscriptions.inc';

	if (!require_anything_trim($_POST, 'PHOTOGRAPHER', true)
	||	!require_anything_trim($_POST, 'PHOTOGRAPHER_SITE', true)
	||	!require_anything_trim($_POST, 'TEXTOVERLAY', true)
	||	!require_admin(['photographer', 'aspiring_photographer'])
	) {
		return false;
	}
	$setlist[] = 'ANONYMOUS			='.(isset($_POST['ANONYMOUS']) ? "b'1'" : "b'0'");
	$setlist[] = 'PHOTOGRAPHER		="'.addslashes($_POST['PHOTOGRAPHER']).'"';
	$setlist[] = 'PHOTOGRAPHER_SITE	="'.addslashes($_POST['PHOTOGRAPHER_SITE']).'"';
	$setlist[] = 'TEXTOVERLAY		="'.addslashes($_POST['TEXTOVERLAY']).'"';
	$allsetlist = $setlist;
	$setlist[] = 'UPDALL='.			(isset($_POST['UPDALL']) ? '1' : '0');

	if (!db_insert('gallery_default_log','
		INSERT INTO gallery_default_log
		SELECT * FROM gallery_default
		WHERE USERID='.CURRENTUSERID)
	||	!db_update('gallery_default','
		INSERT INTO gallery_default SET
			USERID	= '.CURRENTUSERID.',
			MUSERID	= '.CURRENTUSERID.',
			CSTAMP	= '.CURRENTSTAMP.',
			MSTAMP	= '.CURRENTSTAMP.','.
			implode( ', ', $setlist).'
		ON DUPLICATE KEY UPDATE
			MUSERID	= '.CURRENTUSERID.',
			MSTAMP	= '.CURRENTSTAMP.','.
			implode(', ', $setlist))
	) {
		return false;
	}

	register_notice('generic:notice:changed_settings_LINE');

	if (db_affected()
	&&	isset($_POST['UPDALL'])
	) {
		if (!db_insert('gallery_log','
			INSERT INTO gallery_log
			SELECT * FROM gallery
			WHERE USERID = '.CURRENTUSERID.'
			  AND NOT ('.implode(' AND ', $allsetlist).')')
		||	!db_update('gallery','
			UPDATE gallery SET
				MUSERID	= '.CURRENTUSERID.',
				MSTAMP	= '.CURRENTSTAMP.','.
				implode(', ',$allsetlist).'
			WHERE USERID = '.CURRENTUSERID.'
			  AND NOT ('.implode(' AND ',$allsetlist).')')
		) {
			return false;
		}
		register_notice('camera:notice:all_gallery_attributions_changed_LINE');
	}
	return true;
}

function camera_display_interesting_events(): void {
	if (!require_admin(['camerarequest', 'photographer', 'aspiring_photographer'])) {
		no_permission();
		return;
	}

	layout_show_section_header();

	camera_menu();

	require_once 'defines/appic.inc';

	# join appuic_event using PARTY_MAIN_ID_INC, so appic counter are duplicated to every day

	if (!($parties = db_rowuse_hash('party',/** @lang MariaDB */ '
		SELECT	party.PARTYID,
				WEEK(FROM_UNIXTIME(party.STAMP_TZI - '.ONE_DAY.')) AS WEEK,

				camera.STATUS,

				MAX(VISITORS)  AS APPIC_ATTENDING,
				MAX(FOLLOWERS) AS APPIC_INTERESTED,

				(SELECT COUNT(*) FROM going WHERE going.PARTYID = party.PARTYID AND MAYBE = 0) AS PARTYFLOCK_ATTENDING,
				(SELECT COUNT(*) FROM going WHERE going.PARTYID = party.PARTYID AND MAYBE = 1) AS PARTYFLOCK_INTERESTED,

				(SELECT 1 FROM camerarequest WHERE camerarequest.PARTYID = party.PARTYID AND STATUS = "accepted" LIMIT 1) AS PHOTO,
				(SELECT 1 FROM camerarequest WHERE camerarequest.PARTYID = party.PARTYID LIMIT 1) AS ANY_PHOTO,

				appic_event.PARTNER   AS APPIC_PARTNER,
				party_partner.PARTNER AS FLOCK_PARTNER
		FROM party
		LEFT JOIN camera ON camera.PARTYID = party.PARTYID
		LEFT JOIN appic_event ON appic_event.PARTYID = '.PARTY_MAIN_ID_INC.'
		LEFT JOIN party_partner ON party_partner.PARTYID = party.PARTYID
		WHERE party.STAMP > '.(TODAYSTAMP + ONE_WEEK).'
		GROUP BY PARTYID
		HAVING PARTYFLOCK_ATTENDING >= 50
			OR APPIC_ATTENDING >= 50
			OR FLOCK_PARTNER
			OR APPIC_PARTNER AND APPIC_PARTNER NOT IN ("", "Prospect")
		ORDER BY STAMP_TZI',
		DB_UTC))
	) {
		return;
	}

	include_style('camerarequest');

	$current_location = db_single_assoc('city','SELECT LATITUDE,LONGITUDE FROM city JOIN user USING (CITYID) WHERE user.USERID='.CURRENTUSERID);

	layout_open_box('white');
	layout_box_header(Eelement_plural_name('interesting_event'));

	?><table class="fw hhla intevents"><?
	?><tr><?
	if (!SMALL_SCREEN) {
		?><th class="center"><?= element_name('time') ?></th><?
	}
	?><th class="right"><?= element_name('distance') ?></th><?
	?><th></th><?
	?><th class="center"><?= element_name('customer') ?></th><?
	?><th><?= element_name('event') ?></th><?
	if (SMALL_SCREEN) {
		?></tr><tr><th colspan="4"><table class="fw no-bottom"><tr><th><?
	} else {
		?><th class="center" colspan="4"><?
	}
	echo element_plural_name('visitor') ?> + <?= element_plural_name('doubter') ?></th><?
	if (SMALL_SCREEN) {
		?></tr></table></th><?
	}
	?></tr><?

	change_timezone('UTC');
	$current_week = null;
	$cell_width = SMALL_SCREEN ? ' style="width:'.(100/6).'%"' : null;
	$prev_y = $prev_m = $prev_d = $prev_hour = $prev_mins = 0;
	foreach ($parties as $partyid => $party) {
		if (!($local_party = memcached_party_and_stamp($partyid))) {
			return;
		}
		/** @noinspection PhpRedundantOptionalArgumentInspection */
		extract($party,			\EXTR_OVERWRITE);
		extract($party,			\EXTR_OVERWRITE);
		extract($local_party, 	\EXTR_OVERWRITE);
		$bg = null;
		if ($STATUS === 'denied') {
			$bg = ' style="background-color:rgba(255,0,0,.2)"';
		} elseif ($PARTYFLOCK_ATTENDING >= 50 && $APPIC_ATTENDING >= 50) {
			$t = 3;
			$bg = ' style="background-color:rgba(0,255,0,.4)"';
		} elseif ($PARTYFLOCK_ATTENDING >= 50 || $APPIC_ATTENDING >= 50) {
			$t = 2;
			$bg = ' style="background-color:rgba(0,255,0,.2)"';
		}
		if ($current_week !== $WEEK) {
			?><tr><td colspan="2"><b>week <?= $WEEK ?></b></td><td colspan="10"><hr></td></tr><?
			$current_week = $WEEK;
		}
		[$y, $m, $d, $hour, $mins] = _getdate($STAMP_TZI);
		if ($prev_y !== $y
		||	$prev_m !== $m
		||	$prev_d !== $d
		) {
			?><tr><td class="party-day bg" colspan="20"><h2><b><?
			dateday_display_link($STAMP_TZI - ($AT2400 ? 1 : 0));
			?></b></h2></td></tr><?
		}

		$prev_y = $y;
		$prev_m = $m;
		$prev_d = $d;
		$prev_hour = $hour;
		$prev_mins = $mins;

		?><tbody class="introw"><?
		?><tr<?= $bg ?>><?
		if (!SMALL_SCREEN) {
			?><td><?
				if ($AT2400) {
					?>24:00<?
				} else {
					_time_display($STAMP_TZI);
				}
			?></td><?
		}
		if ($current_location) {
			if ($distance =
				$LAT && $LON
				?
					calculate_distance(
						$LAT,
						$LON,
						$current_location['LATITUDE'],
						$current_location['LONGITUDE']
					)
				: 0
			) {
				?><td class="right"><? echo round($distance);
				?><span class="small">&nbsp;<? echo __('abbr:kilometer') ?></span><?
				?></td><?
			} else {
				?><td></td><?
			}
		}
		?><td class="hpad center relative"><?
		if ($ANY_PHOTO || $STATUS === 'denied') {
			?><span<?
			if (!$PHOTO) {
				?> class="light"<?
			}
			?>><?
			if ($STATUS === 'denied') {
				?><div class="abs z2 oglo">x</div><?
			} elseif (!$PHOTO) {
				?><div class="abs z2 oglo">?</div><?
			}
			echo get_camera_icon();
			?></span><?
		}
		?></td><?

		?><td class="hpad center"><?

		if (!$APPIC_PARTNER
		||	$APPIC_PARTNER === 'Prospect'
		) {
			$APPIC_PARTNER = null;
		}
		if ($FLOCK_PARTNER) {
			if ($APPIC_PARTNER) {
				echo '<img'.
					' alt="'.element_plural_name('visitor').'"'.
					' class="lower"'.
					' style="width:24px;height:16px"'.
					' src="'.STATIC_HOST.'/presence/apf'.is_high_res().'.png"'.
					'>';
			} else {
				echo get_partyflock_icon();
			}
		} elseif ($APPIC_PARTNER) {
			echo get_appic_icon();
		}
		?></td><?

		?><td><?
		echo get_element_link('party',$partyid);
		?><span class="light"><?
		if ($CITYID) {
			?>, <?
			echo get_element_link('city',$CITYID);
		}
		?></span><?
		?></td><?

		if (SMALL_SCREEN) {
			?></tr><tr<?= $bg ?>><td colspan="6"><table class="fw no-bottom light"><?
		}
		if (!$PARTYFLOCK_ATTENDING) {
			?><td<?= $cell_width ?>></td><?
		} else {
			?><td<?= $cell_width ?> class="right nowrap"><?= $PARTYFLOCK_ATTENDING ?> <?= get_partyflock_icon() ?></td><?
		}
		if (!$PARTYFLOCK_INTERESTED) {
			?><td<?= $cell_width ?>></td><?
		} else {
			?><td<?= $cell_width ?> class="light right nowrap"><?= $PARTYFLOCK_INTERESTED ?> <?= get_partyflock_icon() ?></td><?
		}

		if (!$APPIC_ATTENDING) {
			?><td<?= $cell_width ?>></td><?
		} else {
			?><td<?= $cell_width ?> class="right nowrap"><?= $APPIC_ATTENDING ?> <?= get_appic_icon() ?></td><?
		}
		if (!$APPIC_INTERESTED) {
			?><td<?= $cell_width ?>></td><?
		} else {
			?><td<?= $cell_width ?> class="light right nowrap"><?= $APPIC_INTERESTED ?> <?= get_appic_icon() ?></td><?
		}

		if (SMALL_SCREEN) {
			?></tr></table></td><?
		}
		?></tr><?
		?></tbody><?
	}
	change_timezone();
	?></table><?

	layout_close_box();
}

function get_photographer_stats(int $userid): array {
	ob_start();
	if ($counts = memcached_simpler_array('image','
		SELECT COUNT(*)
		FROM image
		WHERE CSTAMP > '.(TODAYSTAMP - 2 * ONE_YEAR)."
		  AND HIDDEN = 0
		  AND USERID = $userid
		GROUP BY GALLERYID
		ORDER BY COUNT(*)",
		ONE_DAY)
	) {
		$median = $counts[floor(count($counts) / 2)];

		echo '<span class="light">&plusmn;</span> ',$median;
	}
	$stats['average_photo_count'] = ob_get_clean();

	ob_start();
	if ($galleries = memcached_multirow_hash(['gallery', 'party', 'votenew'], "
		SELECT gallery.CSTAMP, VOTEID
		FROM gallery
		JOIN party USING (PARTYID)
		JOIN votenew ON TYPE = 'gallery' AND ID = GALLERYID
		WHERE gallery.CSTAMP > ".(TODAYSTAMP - ONE_YEAR)."
		  AND gallery.USERID = $userid")
	) {
		# Q1: count once
		# Q2: count twice
		# Q3: count three times
		# Q4: count four times
		$votes = [];
		foreach ($galleries as $gallery_CSTAMP => $voteids) {
			foreach ($voteids as $VOTEID) {
				$votes[] = $VOTEID;
				if ($gallery_CSTAMP > CURRENTSTAMP - 3 * (ONE_YEAR / 4)) {
					$votes[] = $VOTEID;
				}
				if ($gallery_CSTAMP > CURRENTSTAMP - 2 * (ONE_YEAR / 4)) {
					$votes[] = $VOTEID;
				}
				if ($gallery_CSTAMP > CURRENTSTAMP - (ONE_YEAR / 4)) {
					$votes[] = $VOTEID;
				}
			}
		}
		sort($votes);

		$total = count($votes);
		$median = $votes[$total>>1];

		include_style('votes');
		require_once '_vote.inc';
		?><span class="vote text-<?= VOTE_BAR_NAME[$median] ?>"><?= vote_name($median) ?></span><?
	}
	$stats['average_rating'] = ob_get_clean();

	ob_start();
	if (($galleries = memcached_rowuse_array(['gallery','party'],'
		SELECT gallery.CSTAMP AS gallery_CSTAMP, gallery.CSTAMP - party.STAMP AS DIFF
		FROM gallery
		JOIN party USING (PARTYID)
		JOIN camera USING (PARTYID)
		WHERE gallery.CSTAMP > '.(TODAYSTAMP - ONE_YEAR).'
		  AND gallery.USERID = '.$userid.'
		ORDER BY DIFF'))
	) {
		# 1st half year: 1x count
		# 2nd half year: 2x count

		$diffs = [];

		foreach ($galleries as $gallery) {
			extract($gallery, \EXTR_OVERWRITE);
			$diffs[] = $DIFF;
			if ($gallery_CSTAMP >= CURRENTSTAMP - ONE_YEAR / 2) {
				$diffs[] = $DIFF;
			}
		}

		$total = count($diffs);
		$median = $diffs[$total>>1];

		$days = floor($median / ONE_DAY);

		echo '<span class="nowrap"><span class="light">&plusmn;</span> ',$days,' '.element_name('day',$days > 1).'</span>';
	}
	$stats['average_delay'] = ob_get_clean();

	ob_start();
	if (($failures = memcached_single_array(['camerarequest', 'party'], "
		SELECT COUNT(IF(STATUS = 'failed_photographer', 1, NULL)), COUNT(*)
		FROM camerarequest
		JOIN party USING (PARTYID)
		WHERE camerarequest.USERID = $userid
		  AND STATUS IN ('accepted', 'self', 'failed_photographer')
		  AND STAMP > ".(TODAYSTAMP - ONE_YEAR)))
	&&	$failures[0]
	) {
		[$failed, $total] = $failures;
		$percentage_failed = (int)(100 * $failed / $total);
		?><span class="<?
		if ($percentage_failed > 40) {
			?>error<?
		} elseif ($percentage_failed > 10) {
			?>warning<?
		}
		?>"><?= $failed
		?> <?= MULTIPLICATION_SIGN_ENTITY
		?> <?= CROSS_MARK_ENTITY
		?> = <?= $percentage_failed ?>%<?
		?></span><?
	}
	$stats['failures'] = ob_get_clean();
	return $stats;
}
