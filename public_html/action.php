<?php

/** @noinspection SuspiciousAssignmentsInspection */

define('CURRENTSTAMP',	time());
define('TODAYSTAMP',	mktime(0,0,0));

require_once '_exit_if_readonly.inc';
require_once '_currentuser.inc';
require_once '_require.inc';
require_once '_http_status.inc';

function bail(int|array $arg): never {
	if (is_int($arg) && $arg !== 200) {
		http_response_code($arg);
		if (!have_errors()) {
			register_error('error:generic_LINE');
		}
	}
	ob_start();
	display_messages_only();
	$data = ob_get_clean();
	if (is_array($arg)) {
		if ($data) {
			$arg['MESSAGES'] = $data;
		}
		header('Content-Type: application/json');
		require_once '_flockmod.inc';
		echo safe_json_encode_windows1252($arg);
	} elseif ($data) {
		header('Content-Type: text/html; charset=windows-1252');
		echo $data;
	}
	exit;
}

if (empty($_SERVER['ACTION'])) {
	bail(400);
}

require_once '_nocache.inc';
send_no_cache_headers();

switch ($_SERVER['ACTION']) {
default:
	bail(404);

case 'appearinfo':
	$currentuser = new _currentuser(TINY_USER | SKIP_SETTINGS);
	if (!require_admin()) {
		bail(403);
	}
	require_once '_appearinfo.inc';
	!empty($_POST['HIDE']) ? unset_appear_info() : set_appear_info();
	bail(200);

case 'cookieconsent':
	if (!require_post()) {
		bail(400);
	}
	$GLOBALS['__allow_new_identity'] = true;
	require_once '_identity.inc';
	if (!CURRENTIDENTID) {
		bail(200);
	}
	require_once '_cookie_consent.inc';
	bail(set_cookie_consent() ? 200 : 500);

case 'smallscreen':
	require_once '_cookie.inc';
	set_flag_flock_cookie(FLOCK_FLAG_SMALL_SCREEN, isset($_REQUEST['yes']));
	set_flag_flock_cookie(FLOCK_FLAG_ASKED_SMALL_SCREEN);
	$destination = null;
	if (preg_match('"(?:yes|no);(?<base64_url>[a-zA-Z\d+=/]*)"', $_SERVER['QUERY_STRING'], $match)
	&&	(	!$match['base64_url']
		||	!($destination = base64_decode(urldecode($match['base64_url'])))
		||	!preg_match('"^[\x00-\x7F]+$"i', $destination)
		)
	) {
		$destination = '/';
	}
	if ($destination !== null
	||	empty($_SERVER['HTTP_REFERER'])
	) {
		see_other($destination ?: '/');
	} else {
		see_other($_SERVER['HTTP_REFERER']);
	}

case 'camrequpd':
	$currentuser = new _currentuser(TINY_USER | SKIP_SETTINGS);
	if (!require_admin('camerarequest')) {
		bail(403);
	}
	if (!($ticketid = require_idnumber($_POST,'TICKETID'))) {
		bail(400);
	}
	if (isset($_POST['newname'])) {
		if (!require_something_trim($_POST, 'newname')) {
			bail(400);
		}
		$setlist[] = 'CAMREQ_NAME="'.addslashes($_POST['newname']).'"';
	}
	if (isset($_POST['newemail'])) {
		if (!require_email($_POST,'newemail')) {
			bail(400);
		}
		$setlist[] = 'CAMREQ_EMAIL="'.addslashes($_POST['newemail']).'"';
	}
	if (isset($_POST['newuser'])) {
		if (!require_idnumber($_POST,'newuser')) {
			bail(400);
		}
		$setlist[] = 'CAMREQ_USERID='.$_POST['newuser'];
	}
	if (isset($setlist)) {
		if (!($element = require_element($_POST, 'ELEMENT', ['organization', 'location']))
		||	!($id = require_idnumber($_POST, 'ID'))
		) {
			bail(400);
		}
		if (!db_insert($element.'_log','
			INSERT INTO '.$element.'_log
			SELECT * FROM '.$element.'
			WHERE NOT '.binary_equal($setlist).'
			  AND '.$element.'ID = '.$id)
		) {
			bail(503);
		}
		header('Content-Type: text/html;charset=windows-1252');
		if ($aff = db_affected()) {
			if (!db_update($element,'
				UPDATE '.$element.' SET '.
					implode(',',$setlist).',
					MUSERID	='.CURRENTUSERID.',
					MSTAMP	='.CURRENTSTAMP.'
				WHERE '.$element.'ID = '.$id)
			) {
				bail(503);
			}
			register_notice('camerarequest:notice:contact_person_changed_LINE');
		} else {
			register_warning('camerarequest:warning:contact_person_identical_LINE');
		}
	}
	if (!db_update('camerarequest_ticket','
		UPDATE camerarequest_ticket SET
			DONE = '.CURRENTSTAMP.'
		WHERE TICKETID = '.$ticketid)
	) {
		bail(503);
	}
	bail(200);

case 'invoicemailonly':
	require_once '_nocache.inc';
	send_no_cache_headers();
	if (!require_post()) {
		bail(400);
	}
	require_once '_identity.inc';
	require_once '_currentuser.inc';
	$currentuser = new _currentuser(TINY_USER | SKIP_SETTINGS);
	if (!require_user()) {
		bail(403);
	}
	require_once '_invoicemailonly.inc';
	bail(store_invoicemailonly() ? 200 : 503);

case 'storedating':
	require_once '_nocache.inc';
	send_no_cache_headers();
	if (!require_post()) {
		bail(400);
	}
	require_once '_identity.inc';
	require_once '_currentuser.inc';
	$currentuser = new _currentuser(TINY_USER | SKIP_SETTINGS);
	if (!require_user()) {
		bail(403);
	}
	require_once '_dating.inc';
	bail(store_dating() ? 200 : 500);

case 'findby':
	require_once '_nocache.inc';
	send_no_cache_headers();
	if (!require_post()) {
		bail(400);
	}
	require_once '_identity.inc';
	require_once '_currentuser.inc';
	$currentuser = new _currentuser(TINY_USER | SKIP_SETTINGS);
	if (!require_user()) {
		bail(403);
	}
	require_once '_findby.inc';
	bail(set_find_by() ? 200 : 503);

case 'markpromo':
	$currentuser = new _currentuser(TINY_USER | SKIP_SETTINGS);
	if (!require_user()) {
		bail(403);
	}
	if (!($promoid = require_idnumber($_POST, 'PROMOID'))
	||	!($action  = require_element ($_POST, 'ACTION', ['seen', 'read']))
	) {
		bail(400);
	}
	# needed for old promo's:
	require_once '_promo.inc';
	if (!($action = match ($action) {
		'seen'	=> PROMO_SET_SEEN,
		'read'	=> PROMO_SET_READ,
		default => 0,
	})) {
		bail(400);
	}
	if (!promo_letter_alter($action,$promoid)) {
		bail(500);
	}
	break;

case 'seenquote':
	global $currentuser;
	$currentuser = new _currentuser(TINY_USER | SKIP_SETTINGS);
	if (!require_user()) {
		bail(403);
	}
	require_once '_comment.inc';
	$elements = commenttables();
	$elements[] = 'flockmessage';
	$elements[] = 'message';

	if (!($element = require_element ($_POST, 'ELEMENT', $elements))
	||	!($id	   = require_idnumber($_POST,'ID'))
	) {
		bail(400);
	}
	require_once '_quote.inc';
	if (!mark_new_quote_seen(
		CURRENTUSERID,
		CURRENTSTAMP,
		$element,
		$id,
		have_idnumber($_POST,'MSGNO')
	)) {
		bail(503);
	}
	break;

case 'seenquotes':
	global $currentuser;
	$currentuser = new _currentuser(TINY_USER | SKIP_SETTINGS);
	if (!require_user()) {
		bail(403);
	}
	if (!($stamp = require_idnumber($_POST,'STAMP'))) {
		bail(400);
	}
	require_once '_quote.inc';
	if (!mark_new_quotes_seen(CURRENTUSERID,$stamp)) {
		bail(503);
	}
	break;

case 'changebio':
	require_once '_bio.inc';
	bail(commit_bio());

case 'confirmcontestcodes':
	require_once '_contestcode.inc';
	bail(confirm_codes());

case 'complete':
	$complete = true;
	# fall through
case 'incomplete':
	require_once '_complete.inc';
	bail(process_complete(complete: isset($complete)));

case 'allowagenda':
	if (!($ipstr = require_something($_POST,'IPSTR'))) {
		bail(400);
	}
	$currentuser = new _currentuser(TINY_USER | SKIP_SETTINGS);
	if (!require_super_admin()) {
		bail(403);
	}
	$ipbinstr = addslashes(inet_pton($_POST['IPSTR']));
	if (isset($_POST['REMOVE'])) {
		if (!db_insert('agenda_allowed_log','
			INSERT INTO agenda_allowed_log
			SELECT *,'.CURRENTSTAMP.'
			FROM agenda_allowed
			WHERE IPBIN="'.$ipbinstr.'"')
		||	!db_delete('agenda_allowed','
			DELETE FROM agenda_allowed
			WHERE IPBIN="'.$ipbinstr.'"')
		) {
			bail(500);
		}
	} elseif (
		!db_insert('agenda_allowed','
		INSERT IGNORE INTO agenda_allowed SET
			IPBIN	="'.$ipbinstr.'",
			CSTAMP	='.CURRENTSTAMP)
	) {
		bail(500);
	}
	memcached_delete('agenda_alloweds');
	break;

case 'loginhitsokip':
	if (!($ipstr = require_something($_POST, 'IPSTR'))) {
		bail(400);
	}
	$currentuser = new _currentuser(TINY_USER | SKIP_SETTINGS);
	if (!require_super_admin()) {
		bail(403);
	}
	$ipbinstr = addslashes(inet_pton($_POST['IPSTR']));
	if (isset($_POST['REMOVE'])) {
		if (!db_delete('login_hits_ok_ip','
			DELETE FROM login_hits_ok_ip
			WHERE IPBIN = "'.$ipbinstr.'"')
		) {
			bail(500);
		}
	} elseif (
		!db_insert('login_hits_ok_ip', '
		INSERT IGNORE INTO login_hits_ok_ip SET
			IPBIN	="'.$ipbinstr.'",
			STAMP	='.CURRENTSTAMP)
	) {
		bail(500);
	}
	break;

case 'agendachecked':
	if (!($id = require_idnumber($_POST,'ID'))) {
		bail(400);
	}
	$currentuser = new _currentuser(TINY_USER | SKIP_SETTINGS);
	if (!require_super_admin()) {
		bail(403);
	}
	if (!db_update('agenda_check','
		UPDATE agenda_check SET DONE=1
		WHERE ID='.$id)
	) {
		bail(500);
	}
	break;

case 'changeuimg':
	require_once '_uploadimage.inc';
	if (!($element = require_uploadimage_type($_POST,'ELEMENT'))
	||	!($id = require_idnumber($_POST,'ID'))
	||	!($upimgid = require_idnumber($_POST,'UPIMGID'))
	) {
		bail(400);
	}
	$currentuser = new _currentuser(TINY_USER | SKIP_SETTINGS);
	if (!may_change_uploadimage($element,$id)) {
		bail(403);
	}
	if (!db_update('uploadimage_link_log', '
		UPDATE uploadimage_link_log SET
			ACTIVE	= '.(empty($_POST['ACTIVATE']) ? '0' : '1')."
		WHERE TYPE = '$element'
		  AND ID = $id
		  AND UPIMGID = $upimgid")
	) {
		bail(500);
	}
	clear_uploadimage_cache($element,$id);
	echo empty($_POST['ACTIVATE']) ? '0' : '1';
	break;

case 'hitlogo':
	if (!($id = require_idnumber($_POST,'ID'))) {
		bail(400);
	}
	require_once '_getcurrentaddr.inc';
	require_once '_identity.inc';
	getcurrentaddr();
	$currentuser = new _currentuser(TINY_USER | SKIP_SETTINGS);
	if (!db_update('rndlogo','
		UPDATE rndlogo SET
			CLICKED	=1
		WHERE USERID	='.CURRENTUSERID.'
		  AND IDENTID	='.CURRENTIDENTID.'
		  AND IPBIN	="'.addslashes(CURRENTIPBIN).'"
		  AND ID	='.$id)
	||	!db_affected()
	) {
		bail(404);
	}
	break;

case 'storelog':
	require_once '_logbook.inc';
	store_log();
	break;

case 'debug':
	error_log('WARNING debug'.(isset($_REQUEST['DATA']) ? ': '.$_REQUEST['DATA'] : null));
	break;

case 'archivepayment':
	require_once '_payment.inc';
	archive_payment();
	break;

case 'processgoing':
	require_once '_goingprocess.inc';
	process_going();
	break;

case 'showlineups':
	require_once '_identity.inc';
	$currentuser = new _currentuser(TINY_USER | SKIP_SETTINGS);
	if (!have_user()
	&&	!require_identity()
	) {
		bail	(403);
	}
	if (have_user()) {
		$who = 'user';
		$who_id = CURRENTUSERID;
	} else {
		$who = 'identity';
		$who_id = CURRENTIDENTID;
	}
	if (!db_replace('show_lineups','
		REPLACE INTO show_lineups SET
			STATE	="'.(!empty($_POST['YES']) ? 'yes' : 'no').'",
			WHO		="'.$who.'",
			ID		='.$who_id)
	) {
		bail(503);
	}
	break;

case 'choosedefupimg':
	require_once '_uploadimage.inc';
	bail(uploadimage_choose_default());

case 'removeupimg':
	require_once '_uploadimage.inc';
	bail(uploadimage_remove_link());

case 'storeupimgcrop':
	require_once '_uploadimage.inc';
	bail(uploadimage_store_crop());

case 'savecp':
	require_once '_uploadimage.inc';
	bail(uploadimage_save_copyright());

case 'getimage':
	require_once '_uploadimage.inc';
	bail(uploadimage_get_image_from_url());

case 'choosegenerated':
	require_once '_uploadimage.inc';
	bail(uploadimage_choose_generated());

case 'getcrop':
	require_once '_uploadimage.inc';
	bail(uploadimage_get_crop());

case 'receive_upload_image':
	require_once '_uploadimage.inc';
	bail(uploadimage_receive_inline());

case 'noperson':
	require_once '_uploadimage.inc';
	bail(uploadimage_change_no_person());

case 'setpricesoldout':
	require_once '_partyprices.inc';
	bail(set_partyprice_sold_out());

case 'proclistdone':
	require_once '_processlist.inc';
	bail(set_proclist_done());

case 'proclistlock':
	require_once '_processlist.inc';
	lock_proclist_item(true);

case 'proclistunlock':
	require_once '_processlist.inc';
	lock_proclist_item(false);

case 'procliststatus':
	require_once '_processlist.inc';
	proclist_status();
	break;

case 'fbignorevnt':
	require_once '_facebook_eventlist_parse.inc';
	bail(ignore_facebook_event());

case 'pixelitem':
	require_once '_pixel.inc';
	pixelitem_process();
	break;

case 'appicsync':
	require_once '_appic.inc';
	bail(appic_sync());

case 'adviceclick':
	if (!have_post()) {
		http_response_code(405);
	}
	if (!($partyid	  = require_idnumber($_POST, 'PARTYID'))
	||	!($partyid_advice = require_idnumber($_POST, 'PARTYID_ADVICE'))
	) {
		http_response_code(400);
		break;
	}
	$currentuser = new _currentuser(TINY_USER | SKIP_SETTINGS);
	if (!db_insert('advice_click','
		INSERT INTO advice_click SET
			PARTYID			='.$partyid.',
			PARTYID_ADVICE	='.$partyid_advice.',
			STAMP			='.CURRENTSTAMP.',
			IPBIN			="'.addslashes(CURRENTIPBIN).'",
			IDENTID			='.CURRENTIDENTID.',
			USERID			='.CURRENTUSERID)
	) {
		http_response_code(503);
	}
 	break;

case 'setpartypartner':
	require_once 'defines/appic.inc';

	if (!($partyid = require_idnumber($_POST,'PARTYID'))
	||	false === ($set_partner = empty($_POST['PARTNER']) ? null : require_enum($_POST,'PARTNER','party_partner','PARTNER'))
	||	!($party = memcached_party_and_stamp($partyid))
	) {
		bail(400);
	}

	$currentuser = new _currentuser(TINY_USER | SKIP_SETTINGS);
	if (!require_admin('party')) {
		bail(403);
	}

	$partyids = get_partyids_for_mainid($party['MAIN_ID']);
	if ($partyids === false) {
		bail(500);
	}

	$and_must_be_different = $set_partner ? ' AND PARTNER!="'.addslashes($set_partner).'" ' : null;

	foreach ($partyids as $partyid) {
		if (!db_insert('party_partner_log','
			INSERT INTO party_partner_log
			SELECT *,'.CURRENTUSERID.','.CURRENTSTAMP.'
			FROM party_partner
			WHERE PARTYID='.$partyid.
			 $and_must_be_different)
		||	$set_partner
		?	!db_insert('party_partner','
			INSERT INTO party_partner SET
				PARTYID	='.$partyid.',
				PARTNER	="'.addslashes($set_partner).'",
				CUSERID	='.CURRENTUSERID.',
				CSTAMP	='.CURRENTSTAMP.'
			ON DUPLICATE KEY UPDATE
				CUSERID	=IF(PARTNER=VALUES(PARTNER),CUSERID,VALUES(CUSERID)),
				CSTAMP	=IF(PARTNER=VALUES(CSTAMP), CSTAMP, VALUES(CSTAMP)),
				PARTNER	=IF(PARTNER=VALUES(PARTNER),PARTNER,VALUES(PARTNER))')
		:	!db_delete('party_partner','
			DELETE FROM party_partner
			WHERE PARTYID='.$partyid)
		) {
			bail(500);
		}
	}
	require_once '_customer.inc';
	flush_customers();
	break;

case 'unlockitem':
	require_once '_lock.inc';
	bail(unlock_on_unload());

case 'urlcheck':
	$currentuser = new _currentuser(TINY_USER | SKIP_SETTINGS);
	require_once '_urlcheck.inc';
	if (!require_admin()) {
		bail(403);
	}
	if (!($element = require_element($_POST, 'ELEMENT', ['organization', 'party', 'artist', 'bookartist', 'location', 'presence', 'stream']))
	||	!($id = require_idnumber($_POST, 'ID'))
	) {
		bail(400);
	}
	require_once '_execute.inc';
	[$rc, $stdout, $stderr] = execute('/home/<USER>/maintenance/urlchecker.php '.$element.' '.$id);
	header('Content-Type: text/plain; charset=utf-8');
	echo $stdout;
	exit;

case 'remove_active_item_for_appic':
	require_once '_process_appic_changes.inc';
	bail(alter_active_item(REMOVE_ACTIVE_ITEM));

case 'clear_active_item_for_appic':
	require_once '_process_appic_changes.inc';
	bail(alter_active_item(CLEAR_ACTIVE_ITEM));

case 'changerequestdone':
	require_once '_process_appic_changes.inc';
	bail(check_if_request_is_done());

case 'changerequeststatus':
	require_once '_process_appic_changes.inc';
	bail(change_appic_request_status());

case 'marknotemployee':
	if (!require_idnumber($_POST, 'ID')
	||	!require_element($_POST, 'ELEMENT', ['artist', 'location', 'organization'])
	||	!require_idnumber($_POST, 'USERID')
	) {
		exit;
	}
	if (!db_insert('not_employees', '
		INSERT INTO not_employees SET
			ELEMENT	="'.$_POST['ELEMENT'].'",
			ID		='.$_POST['ID'].',
			USERID	='.$_POST['USERID']
		)
	) {
		bail(503);
	}
	exit;

case 'photoload':
	if (!require_element( $_POST, 'LOAD', ['photo_load_thumbnail', 'photo_load_large', 'photo_load_overlay'])
	||	!require_idnumber($_POST, 'PHOTOID')
	) {
		bail(400);
	}
	if (!($galleryid = memcached_single('image', 'SELECT GALLERYID FROM image WHERE IMGID = '.$_POST['PHOTOID']))) {
		bail($galleryid === null ? 404 : 503);
	}
	require_once '_currentuser.inc';
	$currentuser = new _currentuser(TINY_USER | SKIP_SETTINGS);
	require_once '_counthit.inc';
	counthit($_POST['LOAD'], $_POST['PHOTOID'], $galleryid);
	bail(200);

/*case 'mark_verify_aspiring':
	require_once '_layout.inc';
	process_mark_verify_aspiring();
	bail(200);*/

case 'verify_scancodes':
	require_once '_scancodes.inc';
	$scancodes = verify_scancodes();
	bail($scancodes !== false ? 200 : 400);

case 'get_scancode':
	require_once '_scancodes.inc';
	bail(get_scancode());

case 'get_scancodes_type':
	require_once '_scancodes.inc';
	get_scancodes_type();
	# FIXME:
	# Make get_scancodes_type return HTTP status like with get_scancode() above.
	# It now never returns anyway, this last bail is for clarity. Looked as if there
	# was a fall through with all those cases missing a bail().
	bail(200);

case 'set_needupdate':
	require_once '_needupdates.inc';
	bail(act_needupdate());

case 'set_checkup':
	require_once '_checkup.inc';
	bail(act_checkup());
}
