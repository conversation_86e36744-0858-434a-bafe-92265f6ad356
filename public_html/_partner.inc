<?php

declare(strict_types=1);

function show_partner_state(string $partner): void {
	require_once '_star.inc';
	?><span<?
	?> class="win nowrap"<?
	?> style="<?= get_appic_partner_style($partner) ?>"><?= get_star('yellow') ?> <?
	echo strtolower($partner);
	if ($partner !== 'Prospect') {
		?> <?
		echo element_name('partner');
		if ($partner === 'Diamond') {
			?> &#128142;<?
		}
	}
	?></span><?
}

function get_appic_partner_style($partner): string {
	switch ($partner) {
	case 'Prospect':
		$style = 'font-weight:normal;opacity:.7';
		break;
	case 'Bronze':
		$style = 'font-weight:normal;opacity:.8';
		break;
	case 'Silver':
		$style = 'font-weight:normal;opacity:.9';
		break;
	case 'Gold':
		$style = 'font-weight:normal';
		break;
	default:
#	case 'Platinum':
#	case 'Diamond':
#	case 'Festival':
		$style = '';
		break;
	}
	return $style;
}
