<?php

function get_base_chars($ndx = null) {
	static $__chars = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
	return $ndx ? $__chars[$ndx] : $__chars;
}
function to_base($num,$base = 62) {
	$chars = get_base_chars();
	$r = $num % $base;
	$res = $chars[$r];
	$q = (int)($num / $base);
	while ($q) {
		$r = $q % $base;
		$q = (int)($q / $base);
		$res = $chars[$r].$res;
	}
	return $res;
}
function to_10($num,$base = 62) {
	$chars = get_base_chars();
	$limit = strlen($num);
	if (!$limit) {
		return false;
	}
	$res = strpos($chars,$num[0]);
	for ($i = 1; $i < $limit; ++$i) {
		$res = $base * $res + strpos($chars,$num[$i]);
	}
	return $res;
}
