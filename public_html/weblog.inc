<?php

require_once '_pagecontrols.inc';
require_once '_uploadimage.inc';
require_once '_visibility.inc';

function display_header() {
	if (!$_REQUEST['ACTION']
	&&	have_idnumber($_REQUEST,'USERID')
	) {
		header('Location: https://'.$_SERVER['HTTP_HOST'].'/weblog/user/'.$_REQUEST['USERID'],true,301);
		exit;
	}
}
function perform_commit() {
	switch ($_REQUEST['ACTION']) {
	default:
	case null:		return;
	case 'commit':		return weblog_commit();
	case 'remove':		require_once '_remove.inc'; return remove_element();
	}
}
function display_body() {
	switch ($_REQUEST['ACTION']) {
	default:		not_found(); return;
	case null:
	case 'user':
	case 'remove':		return weblog_display_overview();
	case 'single':
	case 'commit':
	case 'comments':
	case 'comment':		return weblog_display_single();
	case 'form':		return weblog_display_form();
	}
}
function weblog_display_element($weblog,$color,$funcs = false) {
	$viewadmins = ['weblog', 'helpdesk'];
	static $alwaysview = 0;
	if ($alwaysview === 0) {
		$alwaysview = have_self_or_admin($weblog['USERID'], $viewadmins);
	}
	if (!$alwaysview
	&&	(	!$weblog['ACCEPTED']
		||	!($visi = _visibility($weblog, 'WEBLOG', null, false))
		)
	||	$weblog['PSTAMP'] > CURRENTSTAMP
	) {
		return;
	}
	layout_open_box($color);
	layout_open_box_header();
	if ($_REQUEST['ACTION'] != 'single') {
		?><a href="<?= get_element_href('weblog',$weblog['WEBLOGID']) ?>"><?
		$linkend = '</a>';
	} else {
		$linkend = null;
	}
	if ($weblog['TITLE']) {
		echo escape_utf8($weblog['TITLE']);
	} elseif ($weblog['NOTIME']) {
		_dateday_display($weblog['PSTAMP']);
	} else {
		_datedaytime_display($weblog['PSTAMP']);
	}
	echo $linkend;
	layout_continue_box_header();
	if (!$weblog['ACCEPTED']) {
		?><span class="invalid">(<?= __('field:unaccepted') ?>)</span><?
	}
	layout_display_offense_link($weblog['USERID'], 'weblog', $weblog['WEBLOGID']);
	layout_display_contacts('weblog', $weblog['WEBLOGID']);
	layout_close_box_header();

	if ($img = uploadimage_get('weblog', $weblog['WEBLOGID'])) {
		?><div class="<?= SMALL_SCREEN ? 'center' : 'right-float' ?>"><?
		uploadimage_show_from_img($img, UPIMG_SHOW_HISTORY);
		?></div><?
	}

	$notforeverybody =
		have_self_or_admin($weblog['USERID'],$viewadmins)
	&&	$weblog['VISIBILITY_WEBLOG'] != EVERYBODY;

	if (!$notforeverybody
	||	$weblog['TITLE']
	) {
		?><p class="light small"><?
		if ($weblog['TITLE']) {
			if ($weblog['NOTIME']) {
				_dateday_display($weblog['PSTAMP']);
			} else {
				_datedaytime_display($weblog['PSTAMP']);
			}
			if ($notforeverybody) {
				?> <?
			}
		}
		if ($notforeverybody) {
			?>(<?= visible_to($weblog['VISIBILITY_WEBLOG']); ?>)<?
		}
		?></p><?
	}
	?><div class="block body noverflow"><?= make_all_html($weblog['BODY'], UBB_UTF8, 'weblog', $weblog['WEBLOGID']) ?></div><?
	layout_close_box();
	if ($funcs) {
		$webloghref = '/weblog/'.$weblog['WEBLOGID'];
		?><div class="funcs"><?
			?><a href="<?= $webloghref; ?>/form"><?= __C('action:change'); ?></a><?
			?><div class="l"><?
			with_confirm(
				__C('action:remove'),
				$webloghref.'/remove/'.$weblog['USERID'],
				__('weblog:confirm:removal_LINE')
			);
			?></div><?
		?></div><?
	}
}
function weblog_display_overview() {
	if (!($userid = require_idnumber($_REQUEST,'subID'))
	||	!($nick  = require_nick($_REQUEST,'subID'))
	||	!($color = memcached_color($userid))
	||	!require_non_hidden_profile($userid)
	) {
		return;
	}
	layout_open_section_header();
	echo __C('weblog:pageheader:weblogs_of_user',DO_UBB,array('USERID'=>$userid,'WEBLOGSURL'=>'/weblog/user/'.$userid));
	layout_close_section_header();

	$allowchange = have_self_or_admin($userid,'weblog');

	if ($allowchange) {
		if (CURRENTUSERID == $userid) {
			layout_open_menu();
			layout_menuitem(__C('action:add'),'/weblog/form');
			layout_close_menu();
		}

		require_once '_itemlist.inc';
		_itemlist_display_prelist();
	}
	if (!($cnts = memcached_single_assoc('weblog', '
		SELECT	COUNT(*) AS TOTAL_CNT,
				COUNT(IF(ACCEPTED=1,1,NULL)) AS OK_CNT
		FROM weblog
		WHERE PSTAMP<'.(TODAYSTAMP+24*3600).'
		  AND USERID='.$userid))
	) {
		$cnts !== false && not_found();
		return;
	}
	if (!$cnts['TOTAL_CNT']) {
		?><div class="block"><?= __('weblog:user_has_no_weblog_LINE', DO_UBB, ['USERID' => $userid]); ?></p><?
		return;
	}
	if ($cnts['TOTAL_CNT'] > 10) {
		$controls = new _pagecontrols;
		$controls->set_per_page(10);
		$controls->include_all(false);
		$controls->show_prev_next();
		$controls->set_total($cnts['TOTAL_CNT']);
		$controls->set_url('/weblog/user/'.$userid);
		$controls->set_order('PSTAMP');
		$weblogs = db_rowuse_array('weblog','
			SELECT WEBLOGID,USERID,MUSERID,CSTAMP,MSTAMP,PSTAMP,BODY,TITLE,VISIBILITY_WEBLOG,ACCEPTED,NOTIME
			FROM weblog
			WHERE USERID='.$userid.'
			  AND PSTAMP<'.(TODAYSTAMP+24*3600).
			$controls->order_and_limit()
		);
	} else {
		$weblogs = db_rowuse_array('weblog','
			SELECT WEBLOGID,USERID,MUSERID,CSTAMP,MSTAMP,PSTAMP,BODY,TITLE,VISIBILITY_WEBLOG,ACCEPTED,NOTIME
			FROM weblog
			WHERE PSTAMP < '.(TODAYSTAMP+24*3600).'
			  AND USERID = '.$userid
		);
	}
	if (!$weblogs) {
		$weblogs !== false && not_found();
		return;
	}
	number_reverse_sort($weblogs,'PSTAMP');
	if (isset($controls)) {
		$controls->display();
	}
	foreach ($weblogs as $weblog) {
		$updatevals[] = $weblog['WEBLOGID'];
		weblog_display_element($weblog,$color,$allowchange);
	}
	if (isset($controls)) {
		$controls->display();
	}
	if (isset($updatevals)) {
/*		db_insupd('weblog_counter','
		INSERT INTO weblog_counter (WLID,VIEWS)
		VALUES '.implode(',',$updatevals).'
		ON DUPLICATE KEY UPDATE VIEWS=VIEWS+1');*/
		counthit('weblog',$updatevals);
	}
}

function weblog_display_single(): void {
	if (!($weblogid = require_idnumber($_REQUEST,'sID'))) {
		return;
	}
	if (!($weblog = db_single_assoc('weblog','SELECT * FROM weblog WHERE WEBLOGID='.$weblogid))) {
		$weblog !== false && not_found();
		return;
	}
	if (!require_non_hidden_profile($userid = $weblog['USERID'])) {
		no_permission();
		return;
	}
	layout_open_section_header();
	echo __C('weblog:pageheader:weblogs_of_user', DO_UBB, ['USERID' => $userid, 'WEBLOGSURL' => '/weblog/user/'.$userid]);
	?> <small class="light"><?= MIDDLE_DOT_ENTITY, NO_BREAK_SPACE_ENTITY, $_REQUEST['sID'] ?></small><?
	layout_close_section_header();

	$allowchange = have_self_or_admin($weblog['USERID'],'weblog');

	weblog_display_element($weblog, memcached_color($weblog['USERID']), $allowchange);

	counthit('weblog',$_REQUEST['sID']);

	require_once '_commentlist.inc';
	$cmts = new _commentlist($weblog['USERID']);
	$cmts->item($weblog);
	$cmts->display();
}

function weblog_display_form(): void {
	if (!require_user()) {
		return;
	}
	if ($weblogid = $_REQUEST['sID']) {
		if (!($weblog = db_single_assoc('weblog','SELECT * FROM weblog WHERE WEBLOGID='.$weblogid))) {
			if ($weblog !== false) {
				register_error('weblog:error:nonexistent_LINE', ['ID' => $weblogid]);
			}
			return;
		}
		if (!require_self_or_admin($weblog['USERID'],'weblog')) {
			return;
		}
		$userid = $weblog['USERID'];
	} else {
		$weblog = null;
		$weblogid = 0;
		$userid = CURRENTUSERID;
	}
	$color = memcached_color($userid);
	layout_show_section_header();
	?><form<?
	?> accept-charset="utf-8"<?
	?> method="post"<?
	?> onsubmit="return submitForm(this);"<?
	?> action="/weblog/<?
	if ($weblogid) {
		echo $weblogid; ?>/<?
	}
	?>commit"><?
	?><input type="hidden" name="USERID" value="<?= $userid ?>" /><?
	layout_open_box($color);
	layout_open_box_header();
	?><table class="dens nomargin"><?
	?><tr><?
	?><td><label for="title"><?= Eelement_name('title') ?></label>:&nbsp;</td><?
	?><td><input id="title" type="text" name="TITLE" value="<? if ($weblog) { echo escape_utf8($weblog['TITLE']); } ?>" /></td><?
	?></tr><?

	?><tr><td><?= Eelement_name('date') ?>:&nbsp;</td><td><?
	_datetime_display_select_stamp(
		$weblog ? $weblog['PSTAMP'] : null,
		null,
		-2,
		+2,
		null,
		!empty($weblog['NOTIME'])
	);
	?></td></tr><?
	?></table><?
	layout_close_box_header();
	?><table class="vtop fw dyntab"><tr><td style="width: 50%;"><?

	?><b><label for="real-message"><?= Eelement_name('real_message') ?></label>:</b><br /><?

	?><textarea id="real-message" required name="BODY" cols="50" rows="30"><?
	if ($weblog) {
		echo escape_utf8($weblog['BODY']);
	}
	?></textarea><?
	?></td><?
	 ?><td style="padding-left: 1em;"><?
	?><b><?= Eelement_name('summary') ?>:</b><br /><?
	?><textarea name="TEASER" cols="40" rows="10"><?
	if ($weblog) {
		echo escape_utf8($weblog['TEASER']);
	}
	?></textarea><br /><?
	?><b><?= __C('field:visibility') ?></b>:<br /><?
	_visibility_display_form_part($weblogid ? $weblog : null,'WEBLOG');
	?><div class="block"><?= __('weblog:form_information_TEXT') ?></div><?

	?></td></tr></table><?
	layout_close_box();
	?><div class="block"><?
	?><input type="submit" value="<?= __($weblogid ? 'action:change' : 'action:add') ?>" /></div><?
	?></form><?
}

function weblog_commit(): void {
	require_once '_offense.inc';
	require_once '_ubb_preprocess.inc';
	if (!require_post()
	||	!require_user()
	||	!require_anything_trim($_POST, 'TITLE', true)
	||	!require_something_trim($_POST, 'BODY',null,null,utf8:true)
	||	!require_anything_trim($_POST, 'TEASER', true)
	||	false === require_number($_POST, 'VISIBILITY_WEBLOG')
	||	!require_date_and_time($_POST, max_hour: 25)
	) {
		return;
	}
	$notime = isset($_POST['HOUR']) && $_POST['HOUR'] >= 24;
	$setlist[] = "NOTIME=b'".($notime ? 1:0)."'";
	$pstamp = $notime ? _dateonly_getstamp($_POST) : _date_getstamp($_POST);
	if (!$pstamp) {
		register_error('error:form:invalid_date_LINE');
		return;
	}
	$setlist[] = 'TITLE="'.addslashes($_POST['TITLE']).'"';
	$setlist[] = 'BODY="'.addslashes($body = _ubb_preprocess($_POST['BODY'], true)).'"';
	$setlist[] = 'TEASER="'.addslashes(_ubb_preprocess($_POST['TEASER'], true)).'"';
	$setlist[] = 'PSTAMP='.$pstamp;
	$setlist[] = 'ACCEPTED=b\'1\'';
	$setlist[] = 'VISIBILITY_WEBLOG='.$_POST['VISIBILITY_WEBLOG'];

	if ($weblogid = $_REQUEST['sID']) {
		if (!have_admin('weblog')
		&&	!require_self_or_admin(db_single('weblog','SELECT USERID FROM weblog WHERE WEBLOGID='.$weblogid),'weblog')
		) {
			return;
		}

		if (!db_insert('weblog_log','
			INSERT INTO weblog_log
			SELECT * FROM weblog
			WHERE WEBLOGID='.$weblogid)
		||	!db_update('weblog','
			UPDATE weblog SET
				MSTAMP	='.CURRENTSTAMP.',
				MUSERID	='.CURRENTUSERID.','.
				implode(',',$setlist).'
			WHERE WEBLOGID='.$weblogid)
		) {
			return;
		}
		register_notice('weblog:notice:weblog_changed_LINE');
	} else {
		if (!db_insert('weblog','
			INSERT INTO weblog SET
				CSTAMP	='.CURRENTSTAMP.',
				USERID	='.CURRENTUSERID.','.
				implode(',',$setlist))
		) {
			return;
		}
		$_REQUEST['sID'] = $weblogid = db_insert_id();
		register_notice('weblog:notice:weblog_added_LINE');
	}
	taint_include_cache('weblog',$weblogid,$body);
}
