<?php

require_once '_donators.inc';

function display_body() {
	switch ($_REQUEST['ACTION']) {
	default:	return donation_display_overview();
	case 'form':	return donation_display_form();
	case 'commit':
		if (donation_commit()) {
			donation_display_form();
		}
		break;
	case 'archive':	return donation_display_archive();
	}
}
function show_donation_menu() {
	if (have_admin('donation')) {
		global $__year;
		layout_open_menu();
		layout_menuitem(Eelement_name('overview'),'/donation',!$_REQUEST['SUBACTION']);
		layout_menuitem(Eelement_name('archive'),'/donation/archive/'.$__year,$_REQUEST['SUBACTION'] == 'archive' && $_REQUEST['subID'] == $__year);
		layout_close_menu();

		layout_open_menu();
		layout_menuitem(__C('action:add'),'/donation/form');
		layout_close_menu();
	}
}
function donation_display_overview() {
	layout_show_section_header();

	$info = db_single_assoc('donation','SELECT COUNT(DISTINCT USERID) AS CNT FROM donation WHERE AMOUNT>0');

	show_donation_menu();

	require_once '_albumspace.inc';

	?><div class="block"><?
	echo str_replace(
		'%VIPSERVER%',
		'vip.partyflock.nl'
		,__('donation:information_TEXT', DO_UBB | DO_COMBINE_2BR | KEEP_EMPTY_KEYWORDS, [
			'DONATORCNT'	=> $info ? $info['CNT'] : 0,
		])
	);
	?></div><?

	?><div class="clear"></div><?

	require_once '_ideal.inc';
	show_ideal_form('donation');

	$last50 = memcached_rowuse_array(
		$arr = array('donation','user','artist','organization','location'),($qstr = '
		SELECT	SUM(AMOUNT) AS AMOUNT,
			donation.USERID,
			donation.ARTISTID,
			donation.ORGANIZATIONID,
			organization.NAME AS ORG_NAME,
			donation.LOCATIONID,
			location.NAME AS LOC_NAME,
			artist.NAME,
			NICK,
			COUNT(*) AS CNT,
			MAX(STAMP) AS LSTAMP
		FROM donation
			LEFT JOIN user ON user.USERID = donation.USERID
			LEFT JOIN artist ON artist.ARTISTID = donation.ARTISTID
			LEFT JOIN organization ON organization.ORGANIZATIONID = donation.ORGANIZATIONID
			LEFT JOIN location ON location.LOCATIONID = donation.LOCATIONID
		WHERE AMOUNT>=0 AND (donation.USERID!=0 OR donation.ARTISTID!=0 OR donation.ORGANIZATIONID!=0 OR donation.LOCATIONID!=0)
		  AND ANONYMOUS=0
		  AND SURVEY=0
		GROUP BY donation.USERID,donation.ARTISTID,donation.ORGANIZATIONID,donation.LOCATIONID
		').' ORDER BY LSTAMP DESC LIMIT 50'
	);
	$top50 = memcached_rowuse_array($arr,$qstr.' ORDER BY AMOUNT DESC LIMIT 50');

	layout_open_box('white');
	layout_open_box_header(BOX_HEADER_CENTER);
	$map = array('AMOUNT'=>50);
	echo __C('donation:header:last_donators',$map);
	layout_close_box_header();
	if ($last50) {
		layout_open_table(TABLE_FULL_WIDTH | TABLE_ROW_HILITE_ONMOUSEOVER);
		?><tr><?
		?><th class="left"><?= $field_user = Eelement_name('user'); ?></th><?
		?><th class="center">#</th><?
		?><th class="right"><?= $field_lastdonation = __C('donation:field:last_donation'); ?></th><?
		?></tr><?
		foreach ($last50 as $donator) {
			?><tr><? donation_display_row($donator); ?></tr><?
		}
		layout_close_table();

	}
	layout_continue_box();
	layout_open_box_header(BOX_HEADER_CENTER);
	echo __C('donation:header:top_donators',$map);
	layout_close_box_header();
	if ($top50) {
		layout_open_table(TABLE_FULL_WIDTH | TABLE_ROW_HILITE_ONMOUSEOVER);
		?><tr><?
		?><th class="right rpad">#<span style="visibility:hidden">.</span></th><?
		?><th class="left"><?= $field_user; ?></th><?
		?><th class="center">#</th><?
		?><th class="right"><?= $field_lastdonation; ?></th><?
		?></tr><?
		$cnt = 1;
		foreach ($top50 as $donator) {
			?><tr><td class="rpad right"><?= $cnt; ?>.<?
			?></td><? donation_display_row($donator);
			?></tr><?
			++$cnt;
		}
		layout_close_table();
	}
	layout_close_box();
}
function donation_display_row($donator) {
	?><td><?
	if ($donator['USERID']) {
		echo get_element_link('user',$donator['USERID'],$donator['NICK']);
	} elseif ($donator['ARTISTID']) {
		echo get_element_link('artist',$donator['ARTISTID'],$donator['NAME']);
	} elseif ($donator['ORGANIZATIONID']) {
		echo get_element_link('organization',$donator['ORGANIZATIONID'],$donator['ORG_NAME']);
	} elseif ($donator['LOCATIONID']) {
		echo get_element_link('location',$donator['LOCATIONID'],$donator['LOC_NAME']);
	}
	?></td><td class="center"><?= $donator['CNT']; ?></td><td class="right"><?
	if ($donator['LSTAMP']) {
		_date_display_numeric($donator['LSTAMP']);
	} else {
		echo __('field:unknown');
	}
	?></td><?
}
function donation_display_form() {
	if (!require_admin('donation')) {
		return;
	}
	layout_show_section_header(__C('donation:pageheader:form'));

	?><form onsubmit="return submitForm(this)" method="post" action="/donation/commit"><?
	layout_open_box('white');
	layout_open_table('fw');
	layout_start_row();
	echo Eelement_name('date');
	layout_field_value();
	if (isset($_POST['DAY']) && isset($_POST['MONTH']) && isset($_POST['YEAR'])) {
		_date_display_select(null,$_POST['YEAR'],$_POST['MONTH'],$_POST['DAY']);
	} else {
		_date_display_select();
	}
	require_once '_fillelementid.inc';
	foreach (['user', 'artist', 'organization', 'location'] as $element) {
		layout_restart_row();
		echo Eelement_name($element);
		layout_field_value();
		show_elementid_input($element, args: [
			'min'  => 1,
			'name' => strtoupper($element.'ID'),
		]);
	}
	layout_restart_row();
	echo Eelement_name('money_amount');
	layout_field_value();
	?><input required="required" class="right rpad id" type="number" data-valid="number" min="1" name="AMOUNT" placeholder="<?= __('donation:currency'); ?>" /><?

	layout_restart_row(0,null,null,'not-hilited');
	?><label for="anonymous"><?= __C('field:anonymous'); ?></label><?
	layout_field_value();
	?><input type="checkbox" name="ANONYMOUS" id="anonymous" class="upLite" /><?

	layout_restart_row(0,null,null,'not-hilited');
	?><label for="flockey">Flockie &amp; <?= element_name('keychain') ?></label><?
	layout_field_value();
	?><input type="checkbox" name="FLOCKEY" id="flockey" class="upLite" /><?

	layout_stop_row();
	layout_close_table();
	layout_close_box();
	?><div class="block"><input type="submit" value="<?= __('action:add'); ?>" /></div></form><?
}

function donation_single($donation) {
	layout_open_box('white');
	layout_open_table();
	layout_start_row();
	echo Eelement_name('date');
	layout_field_value();
	_date_display($donation['STAMP']);

	foreach (array('user','artist','organization','location') as $element) {
		$idname = strtoupper($element).'ID';
		if (!$donation[$idname]) continue;
		layout_restart_row();
		echo Eelement_name($element);
		layout_field_value();
		echo get_element_link($element,$donation[$idname]);
		?> (<?= $donation[$idname]; ?>)<?
	}

	layout_restart_row();
	echo Eelement_name('money_amount');
	layout_field_value();
	printf('%.02f',$donation['AMOUNT']/100);

	layout_restart_row();
	echo __C('field:anonymous');
	layout_field_value();
	echo __($donation['ANONYMOUS'] ? 'answer:yes' : 'answer:no');

	layout_restart_row();
	?>Flockie<?
	layout_field_value();
	echo __($donation['FLOCKEY'] ? 'answer:yes' : 'answer:no');

	layout_restart_row();
	echo Eelement_name('keychain');
	layout_field_value();
	echo __($donation['FLOCKEY'] ? 'answer:yes' : 'answer:no');
	layout_stop_row();
	layout_close_table();
	layout_close_box();
}

function donation_commit() {
	if (!require_admin('donation')
	||	!require_date($_POST)
	||	!require_number_or_empty($_POST,'USERID')
	||	!require_number_or_empty($_POST,'ARTISTID')
	||	!require_number_or_empty($_POST,'ORGANIZATIONID')
	||	!require_number_or_empty($_POST,'LOCATIONID')
	||	!require_number_trim($_POST,'AMOUNT')
	) {
		return false;
	}
	if (!isset($_POST['SURE'])) {
		layout_show_section_header(__C('donation:pageheader:verification'));

		$_POST['ANONYMOUS'] = isset($_POST['ANONYMOUS']) ? 1 : 0;
		$_POST['FLOCKEY'] = isset($_POST['FLOCKEY']) ? 1 : 0;
		$_POST['STAMP'] = _date_getstamp($_POST);
		donation_single($_POST);
		?><form onsubmit="return submitForm(this);" method="post" action="/donation/commit"><?
		?><input type="hidden" name="YEAR" value="<?= $_POST['YEAR']; ?>" /><?
		?><input type="hidden" name="MONTH" value="<?= $_POST['MONTH']; ?>" /><?
		?><input type="hidden" name="DAY" value="<?= $_POST['DAY']; ?>" /><?
		?><input type="hidden" name="USERID" value="<?= $_POST['USERID']; ?>" /><?
		?><input type="hidden" name="ARTISTID" value="<?= $_POST['ARTISTID']; ?>" /><?
		?><input type="hidden" name="ORGANIZATIONID" value="<?= $_POST['ORGANIZATIONID']; ?>" /><?
		?><input type="hidden" name="LOCATIONID" value="<?= $_POST['LOCATIONID']; ?>" /><?
		?><input type="hidden" name="AMOUNT" value="<?= $_POST['AMOUNT']; ?>" /><?
		?><input type="hidden" name="ANONYMOUS" value="<?= $_POST['ANONYMOUS']; ?>" /><?
		?><input type="hidden" name="FLOCKEY" value="<?= $_POST['FLOCKEY']; ?>" /><?
		?><p><input type="submit" value="<?= __C('donation:form:ok'); ?>" name="SURE" /></p></form><?
		return false;
	}
	if (false === require_number($_POST,'ANONYMOUS')) {
		return true;
	}
	$nick = memcached_nick($_POST['USERID']);
	if (!$nick) {
		_error(__C('user:error:nonexistent_LINE',array('ID'=>$_POST['USERID'])));
		return;
	}

	// really commit
	if (($donationid = have_idnumber($_POST,'DONATIONID') ?: 0)
	&&	!db_insert('donation_log','
		INSERT INTO donation_log
		SELECT * FROM donation
		WHERE DONATIONID='.$donationid)
	||	!db_insert('donation','
		INSERT INTO donation SET
			DONATIONID	='.$donationid.',
			CSTAMP		='.CURRENTSTAMP.',
			MSTAMP		='.CURRENTSTAMP.',
			STAMP		='._date_getstamp($_POST).',
			USERID		='.(0+$_POST['USERID']).',
			ORGANIZATIONID	='.(0+$_POST['ORGANIZATIONID']).',
			ARTISTID	='.(0+$_POST['ARTISTID']).',
			LOCATIONID	='.(0+$_POST['LOCATIONID']).',
			AMOUNT		='.$_POST['AMOUNT'].',
			FLOCKIE		='.$_POST['FLOCKEY'].',
			KEYCHAIN	='.$_POST['FLOCKEY'].',
			ANONYMOUS	='.($_POST['ANONYMOUS'] ? '1' : '0').'
		ON DUPLICATE KEY UPDATE
			MSTAMP		=VALUES(MSTAMP),
			STAMP		=VALUES(STAMP),
			USERID		=VALUES(USERID),
			ARTISTID	=VALUES(ARTISTID),
			ORGANIZATIONID	=VALUES(ORGANIZATIONID),
			LOCATIONID	=VALUES(LOCATIONID),
			AMOUNT		=VALUES(AMOUNT),
			FLOCKIE		=VALUES(FLOCKIE),
			KEYCHAIN	=VALUES(KEYCHAIN),
			ANONYMOUS	=VALUES(ANONYMOUS)'
		)
	) {
		return false;
	}
	flush_donators();
	register_notice('donation:notice:donation_added_LINE');
	return true;
}
function donation_year_stats() {
	static $__stats = 0;
	if ($__stats === 0) {
		$__stats = memcached_simple_hash('invoice','
			SELECT FROM_UNIXTIME(STAMP,"%Y") AS YEAR,COUNT(*)
			FROM donation
			WHERE SURVEY=0
			GROUP BY YEAR'
		);
	}
	return $__stats;
}
function donation_display_archive() {
	if (!require_admin('donation')) {
		return;
	}
	show_donation_menu();

	global $__year;

	$stats = donation_year_stats();

	$show_year = $_REQUEST['subID'] ?: $__year;

	?><div class="block"><?
	?><select onchange="location.href='/donation/archive/'+this.value;return false;"><?
	for ($y = $__year; $y >= 2005; --$y) {
		?><option<?
		if ($y == $show_year) {
			?> selected="selected"<?
		}
		?> value="<?= $y ?>"><?
		echo $y;

		if (isset($stats[$y])) {
			?> <?= MIDDLE_DOT_ENTITY ?> <?
			echo $stats[$y];
		}
		?></option><?
	}
	?></select><?
	?></div><?

	$donations = db_rowuse_array('donation','
		SELECT AMOUNT,USERID,STAMP,IDEALID
		FROM donation
		WHERE STAMP BETWEEN UNIX_TIMESTAMP("'.$show_year.'-01-01") AND UNIX_TIMESTAMP("'.($show_year+1).'-01-01")-1
		  AND SURVEY=0
		ORDER BY STAMP DESC'
	);

	if ($donations === false) {
		return;
	}
	require_once '_price.inc';
	layout_open_box('white');
	layout_box_header(Eelement_plural_name('donation'));
	?><table class="fw default"><?
	?><tr><?
	?><th class="right"><?= Eelement_name('date') ?></th><?
	?><th class="right"><?= Eelement_name('money_amount') ?></th><?
	?><th><?= Eelement_name('user') ?></th><?
	?></tr><?
	foreach ($donations as $donation) {
		extract($donation);
		?><tr><?
		?><td class="right"><? _date_display($STAMP, short: true, time_span: true) ?></td><?
		?><td class="right"><?= get_price($AMOUNT); ?></td><?
		?><td><?
		if ($USERID) {
			echo get_element_link('user',$USERID);
		}
		?></td><?
		?></tr><?
	}
	?></table><?
	layout_close_box();
}
