<?php

declare(strict_types=1);

function counthit(
	string			$element,
	array|int|null	$ids	 = null,
	?int			$extraid = null,
): bool {
	if (!CLI
	&&	(	(require_once '_spider.inc')
		&&	ROBOT
		||	(require_once '_dontregister.inc')
		&&	dont_register()
		)
	) {
		return true;
	}
	require_once '_domain.inc';
	$currentuserid	= defined('CURRENTUSERID')  ? CURRENTUSERID						: 0;
	$currentidentid = defined('CURRENTIDENTID') ? CURRENTIDENTID 					: 0;
	$currentipnum	= defined('CURRENTIPNUM')   ? CURRENTIPNUM						: 0;
	$currentipbin	= defined('CURRENTIPBIN')   ? '"'.addslashes(CURRENTIPBIN).'"'	: 'NULL';
	$extraid ??= 0;
	$vals = [];
	foreach ((is_array($ids) ? $ids : [$ids ?: 0]) as $id) {
		$vals[] = "(
			'".CURRENTDOMAIN."',
			".CURRENTSTAMP.",
			'$element',
			$id,
			$extraid,
			$currentuserid,
			$currentidentid,
			$currentipnum,
			$currentipbin)";
	}
	require_once '_db.inc';
	return db_insert('counthitnew','
		INSERT INTO counthitnew (DOMAIN, STAMP, ELEMENT, ID, EXTRAID, USERID, IDENTID, IPNUM, IPBIN)
		VALUES '.implode(', ', $vals)
	);
}
