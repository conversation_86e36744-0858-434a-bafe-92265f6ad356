<?php

require_once '_albummap.inc';

function disconnect_albummap_contents($albumid,$mapid) {
	if (false === ($mapids = get_all_innermaps($albumid, $mapid))) {
		return false;
	}
	$mapidstr = '('.implode(', ', $mapids).')';
	if (false === ($albumelementids = db_simpler_array('albumelement', "
		SELECT ALBUMELEMENTID
		FROM albumelement
		WHERE MAPID IN $mapidstr"))
	) {
		return false;
	}
	if (!db_update(
		'albumelement','
		UPDATE albumelement SET
			PARTYID	= 0,
			MUSERID	= '.CURRENTUSERID.',
			MSTAMP	= '.CURRENTSTAMP.'
		WHERE PARTYID != 0
		  AND ALBUMELEMENTID IN ('.implode(', ', $albumelementids).')')
	||	!flush_albumelements($albumelementids)
	||	!db_update(
		'albummap','
		UPDATE albummap SET
			PARTYID	= 0,
			MUSERID	= '.CURRENTUSERID.',
			MSTAMP	= '.CURRENTSTAMP.'
		WHERE PARTYID != 0
		  AND MAPID IN '.$mapidstr)
	) {
		return false;
	}
	return true;
}
