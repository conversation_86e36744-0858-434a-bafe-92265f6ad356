<?php

# v2 was neaded so no memcache restart needed to fix people getting 2024 Christmas image.
const SPECIAL_OVERLAY_VERSION	= 'v2';

function get_special_overlay(): string|bool {
	if (ROBOT) {
		return false;
	}
	global $__year, $__month, $__day, $__hour, $__mins, $__weekday, $__whatover;
	$__whatover = false;
	$year = 0;
	$forced = false;

	static $__types = [
		# type			=> full overlay (false means only image next to logo)
		'newyear'		=> true,
		'christmas'		=> true,
		'memorialday'	=> true,
		'valentinesday'	=> false,
		'kingsday'		=> false,
		'sinterklaas'	=> false,
	];

	if (isset($_GET['SPECIALOVERLAY'],
	 $__types[$_GET['SPECIALOVERLAY']])
	) {
		$forced = true;
		$__whatover = $_GET['SPECIALOVERLAY'];
		$year = have_idnumber($_GET, 'YEAR') ?: $__year;
	} elseif (
		$__month === 1
	&&	$__day <= 3
	) {
		$__whatover = 'newyear';
	} elseif (
		$__month === 12
	&&	($__day === 25 || $__day === 26 || $__day === 24 && $__hour >= 20)
	) {
		$__whatover = 'christmas';
	} elseif (
		$__month === 5
	&&	$__day === 4
	&&	$__hour === 20
	&&	$__mins <= 2
	) {
		$__whatover = 'memorialday';
	} elseif (
		$__month === 2
	&&	$__day === 14
	) {
		$__whatover = 'valentinesday';
	} elseif (
		$__month === 4
	&&	(	$__day === 26
		&&	$__weekday === 6
		||	$__day === 27
		&&	$__weekday !== 0
		)
	) {
		$__whatover = 'kingsday';
	} elseif (
		$__month === 12
	&&	$__day === 5
	) {
		$__whatover = 'sinterklaas';
	}
	if (!$__whatover) {
		return false;
	}
	if (!$__types[$__whatover]) {
		# not full overlay, but logo thingy
		return true;
	}
	if (!$__whatover
	||	!CURRENTIDENTID
	) {
		return false;
	}
	ob_start();
	if ($forced
	||	count_special_overlay($__whatover)
	) {
		show_actual_special_overlay($__whatover, $year ?: $__year);
	}
	return ob_get_clean();
}

function show_actual_special_overlay(string $type, int $year): void {
	require_once '_hosts.inc';
	$wish = null;
	switch ($type) {
	case 'newyear':
		require_once '_smallscreen.inc';
	 	$title = __('special:partyflock_wishes_you_a_happy_new_year_LINE');
	 	if ($year < 2019) {
		 	$x1img = '/special/newyear/newyear'.$year.'.jpg';
		 	$img = '/special/newyear/newyear'.$year.($year >= 2013 && !SMALL_SCREEN ? is_high_res() : null).'.jpg';
		} elseif ($year === 2019) {
		 	$x1img = $img = '/special/newyear/newyear'.$year.'@2x.jpg';
	 	} elseif ($year === 2020) {
		 	$x1img = '/special/newyear/newyear'.$year.'.jpg';
		 	$img = '/special/newyear/newyear'.$year.'@2x.jpg';
	 	} else {
			$use_year = 2021;
			$img = '/special/newyear/newyear'.$use_year.'_'.(SMALL_SCREEN ? 'portrait' : 'landscape').is_high_res().'.jpg';
			$x1img = '/special/newyear/newyear'.$use_year.'_'.(SMALL_SCREEN ? 'portrait' : 'landscape').'.jpg';
	 	}
		$bg = 'black; background-color: rgba(0,0,0,.9);';
		switch ($year) {
		default:
			$attribution = null;
			break;
		case 2015:
		case 2016:
		case 2017:
		case 2018:
		case 2019:
			$attribution = '<small class="light">'.(CURRENTLANGUAGE === 'nl' ? 'collage door' : 'collage by').' <a target="_blank" href="https://dennisvandenbroek.nl/">Dennis van den Broek</a></small>';
			break;
		case 2020:
			$attribution =
				'<small class="light">'.__('field:by').
				' '.
				(SMALL_SCREEN ? 'Kelly Jiang' : 'Ian Schneider').
				'<small>';
			break;
		}
		ob_start();
		?><span class="nowrap"><?
		?><b><?= get_partyflock_icon() ?> Partyflock</b><?
		/*?> & <b><?= get_appic_icon() ?> Appic</b><?*/
		?></span><?
		?> <?
		?><span class="nowrap"><?
		if (CURRENTLANGUAGE === 'nl') {
			?>wenst je een fantastisch <?= $year ?>!<?
		} else {
			?>wishes you a fantastic <?= $year ?>!<?
		}
		?></span><?
		$wish = ob_get_clean();
	 	break;

	case 'christmas':
		require_once '_smallscreen.inc';
		$title = __('special:partyflock_wishes_you_a_merry_christmas_LINE');

		if ($year >= 2011
		&&	$year <= 2017
		) {
			  $img = '/special/'.$year.'/christmas'.($year !== 2014 && !SMALL_SCREEN ? is_high_res() : null).'.jpg';
			$x1img = '/special/'.$year.'/christmas.jpg';
		} else {
			  $img = '/special/'.$year.'/christmas_'.(SMALL_SCREEN ? 'portrait' : 'landscape').is_high_res().'.jpg';
			$x1img = '/special/'.$year.'/christmas_'.(SMALL_SCREEN ? 'portrait' : 'landscape').'.jpg';
		}
		$bg = '#3B1701';
		switch ($year) {
		default:
			$attribution = null;
			break;
		case 2014:
			$attribution = '<small>original by <a target="_blank" href="//www.flickr.com/photos/korosirego/5306521706">Rego Korosi</a></small>';
			break;
		case 2015:
			$attribution = '<small>original by <a target="_blank" href="//www.flickr.com/photos/rstanek/4235397612">Rebecca Stanek</a></small>';
			break;
		case 2017:
			$bg = '#000';
			$attribution = '<small class="light">door <a target="_blank" href="//dennisvandenbroek.nl/">Dennis van den Broek</a></small>';
			break;
		case 2018:
			$bg = SMALL_SCREEN ? '#6D2126' : '#3B1701';
			break;
		case 2020:
		case 2021:
		case 2022:
			$bg = SMALL_SCREEN ? '#003' : '#000';
			$attribution = '<small class="light">'.
				(	SMALL_SCREEN
				?	'<a target="_blank" href="//unsplash.com/photos/5PQn41LFsQk">Roberto Nickson</a>'
				:	'<a target="_blank" href="//unsplash.com/photos/ySNkCkdKyTY">Rodion Kutsaev</a>'
				).	'</small>';
		}
		ob_start();
		?><span class="nowrap"><?
		?><b><?= get_partyflock_icon() ?> Partyflock</b><?
		/*?> & <b><?= get_appic_icon() ?> Appic</b><?*/
		?></span><?
		?> <?
		?><span class="nowrap"><?
		if (CURRENTLANGUAGE === 'nl') {
			?>wenst je fijne feestdagen!<?
		} else {
			?>wishes you a merry Christmas!<?
		}
		?></span><?
		$wish = ob_get_clean();
		break;

	case 'memorialday':
		$img = $x1img = '/images/dodenherdenking.png';
		$bg = 'black';
		$padding = '10';
		break;
	}

	$src_img = $x1img ?? $img;
	$src_url = $src_img[0] === '/' && $src_img[1] !== '/' ? __DIR__.'/../static_html'.$src_img : $src_img;
	$imgurl = $img[0] === '/' && $img[1] !== '/' ? STATIC_HOST.$img : $img;

	if (!($info = memcached_get($key = "$type:$year:".SPECIAL_OVERLAY_VERSION.':info'))
	&&	($info = getimagesize($src_url))
	) {
		memcached_set($key, $info, ONE_DAY);
	}
	?><div<?
	?> style="<?
		?>position: fixed; <?
		?>z-index: 10000000000000; <?
		?>width: 100%; <?
		?>height: 100%; <?
		?>background-color: <?= $bg ?>; <?
		?>padding: <?= $padding ?? '1' ?>em; <?
		?>cursor: pointer;<?
	?>" onclick="remclass(document.body, 'noverflow'); this.parentNode.removeChild(this);"><?
	if ($year < 2020) {
		?><div<?
		if (isset($title)) {
			?> title="<?= $title ?>"<?
		}
		?> style="<?
			?>margin:0 auto;<?
			?>max-width:<?= $info[0] ?>px;<?
			?>height:100%;<?
			?>background-position:center;<?
			?>background-image:url(<?= $imgurl ?>);<?
			?>background-repeat:no-repeat;<?
			?>background-size:contain;<?
		?>"><?
		?></div><?
	} else {
		?><table style="width: 100%; height: 100%;" id="specialo"><?
		?><tr><td><?
		?><img<?
		?> loading="eager"<?
		?> id="specialimg"<?
		if (isset($title)) {
			?> title="<?= $title ?>"<?
		}
		?> src="<?= $imgurl ?>"<?
		?> style="<?
			?>object-fit: contain; <?
			?>width: 100%; <?
			# 8em give enough space for attribution and top buttons in mobile browsers
			?>max-height: calc(100vh - 8em);<?
		?>" /><?
		if ($wish) {
			?><div class="center" style="margin-top: .5em;" id="dropje"><?= $wish ?></div><?
		}
		?></td></tr><?
		?></table><?
	}
	if (isset($attribution)) {
		?><div class="abs" style="margin: .5em; bottom: 0; right: 0;"><?= $attribution ?></div><?
	}
	?></div><?
}

function count_special_overlay(string $type): bool {
	global $__year;
	if (memcached_get($key = 'shown:'.SPECIAL_OVERLAY_VERSION.":$type:$__year:".CURRENTIDENTID.':'.CURRENTUSERID)
	||	false === ($which = db_rowuse_array('shownoverlay', "
		SELECT IDENTID,USERID
		FROM shownoverlay
		WHERE TYPE = '$type'
		  AND YEAR = $__year
		  AND (IDENTID = ".CURRENTIDENTID.(have_user() ? ' OR USERID = '.CURRENTUSERID : '').')',
		DB_FORCE_MASTER | DB_NON_ASSOC))
	) {
		return false;
	}
	$show = true;
	if ($which) {
		foreach ($which as [$identid, $userid]) {
			if (CURRENTUSERID === $userid
			&&	(	CURRENTUSERID !== 1
				||	CURRENTIDENTID === $identid
				)
			) {
				$show = false;
				break;
			}
		}
	}
	if ($show) {
		db_insert('shownoverlay', "
		INSERT IGNORE INTO shownoverlay SET
			TYPE	= '$type',
			YEAR	= $__year,
			IDENTID	= ".CURRENTIDENTID.',
			USERID	= '.CURRENTUSERID
		);
	}
	memcached_set($key, true, ONE_DAY);
	return $show;
}

function show_logo_overlay(): void {
	global $__whatover;
	if (ROBOT || !$__whatover) {
		return;
	}
	require_once '_browser.inc';
	switch ($__whatover) {
	case 'valentinesday':
		?><img loading="eager" class="z1 abs" src="<?= STATIC_HOST
		?>/images/logo/valentine_v2_<?= CURRENTTHEME,is_high_res() ?>.png" width="90" height="90" /><?
		return;
	case 'kingsday':
		if (HOME_THOMAS) {
			error_log('WARNING showing kingsday logo to Thomas');
			?><div class="ib"><?
			?><img loading="eager" class="z1 abs" src="<?= STATIC_HOST
			?>/images/logo/kingsday<?= is_high_res() ?>.png" width="90" height="90" /><?
			?></div><?
		}
		return;
	case 'sinterklaas':
		?><img<?
		?> loading="eager"<?
		?> class="z1 abs"<?
		?>  src="<?= STATIC_HOST ?>/special/sinterklaas/staff<?= is_high_res() ?>.png"<?
		?> width="60"<?
		?> height="86"<?
		?> style="left: -20px;" /><?
		?><img<?
		?> loading="eager"<?
		?> class="z2 abs"<?
		?> src="<?= STATIC_HOST ?>/special/sinterklaas/presents<?= is_high_res() ?>.png"<?
		?> width="100"<?
		?> height="53"<?
		?> style="left: -60px; bottom: -10px;" /><?
		break;
	}

	include_js('js/specialoverlay');
	?><img<?
	?> loading="eager"<?
	?> class="z2 abs ptr"<?
	?> onclick="load_overlay('<?= $__whatover ?>')"<?
	switch ($__whatover) {
	case 'christmas':
		?> title="<?= __('special:merry_christmas') ?>"<?
		?> src="<?= STATIC_HOST ?>/special/christmas/mistletoe<?= is_high_res() ?>.png"<?
		?> style="width:60px;<?
			?>z-index:2;<?
			?>height:60px;<?
			?>left:50%;<?
			?>top:-7px;<?
			?>margin-left:-78px;<?
			?>"<?
		break;
	case 'newyear':
		?> title="<?= __('special:happy_newyear') ?>"<?
		?> src="<?= STATIC_HOST ?>/special/newyear/champagne<?= is_high_res() ?>.png"<?
		?> style="<?
			?>width:50px;<?
			?>height:100px;<?
			?>left:-.4em;<?
			?>bottom:0;<?
			?>margin-bottom:-.2em;<?
			?>"<?
		break;
	}
	?> /><?
}
