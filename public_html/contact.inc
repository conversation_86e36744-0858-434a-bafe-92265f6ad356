<?php

require_once '_element.inc';
require_once '_connect.inc';
require_once '_contact.inc';
require_once '_contactelements.inc';
require_once '_contactemails.inc';
require_once '_contactlist.inc';
require_once '_contactoutgoing.inc';
require_once '_contactrequire.inc';
require_once '_contactstars.inc';
require_once '_contactticketremove.inc';
require_once '_contact_ticket_creation.inc';
require_once '_directmessage.inc';
require_once '_message.inc';
require_once '_mimeheader.inc';
require_once '_quote.inc';
require_once '_status.inc';
require_once '_template.inc';
require_once '_ticketvote.inc';
require_once '_ubb_preprocess.inc';

const TMPWD_MAX_DAYS = 14;

function preamble(): void {
	switch ($_REQUEST['ACTION']) {
	case 'form':
		if (!have_post()
		&&	isset($_REQUEST['ELEMENT'])
		&&	$_REQUEST['ELEMENT'] === 'image'
		) {
			if (isset($_SERVER['HTTP_REFERER'])) {
				error_log('bad link to ELEMENT=image from '.$_SERVER['HTTP_REFERER']);
			}
			moved_permanently(str_replace('ELEMENT=image', 'ELEMENT=photo', $_SERVER['REQUEST_URI']));
		}
		break;

	case 'oform':
		see_other(str_replace('/oform', '/outgoing-form', $_SERVER['REQUEST_URI']));
	}
}

function display_title(): void {
	if ($ticketid = have_idnumber($_REQUEST, 'sID')) {
		if ($ticket = db_single_assoc('contact_ticket', "
			SELECT ELEMENT, ID
			FROM contact_ticket
			WHERE TICKETID = $ticketid")
		) {
			?>t &#10609; <?
			if (isset(USE_URLTITLE[$ticket['ELEMENT']])
			&&	$ticket['ID']
			&&	($title = get_element_title($ticket['ELEMENT'],$ticket['ID']))
			) {
				echo escape_specials($title, isset(USE_UNICODE[$ticket['ELEMENT']]));
			} else {
				echo element_name($ticket['ELEMENT']);
			}
		}
	} elseif (!$_REQUEST['ACTION']) {
		echo __('action:contact');
	} else {
		switch ($_REQUEST['ACTION']) {
		case 'options':
			echo __('action:contact');
			?> <?= MIDDLE_DOT_ENTITY ?> <?
			echo element_plural_name('option');
			break;

		default:
			echo element_plural_name('contact_ticket');
			break;
		}
	}
}

function perform_commit(): ?bool {
	switch ($_REQUEST['ACTION']) {
	default:
	case null:				return null;
	case 'markread':		return contact_mark_read();
	case 'insert':			return contact_insert();
	case 'commitsignatures':return contact_commit_signatures();
	case 'commitgroups':	return contact_commit_groups();
	case 'commit':
	case 'single':			return contact_commit();
	case 'movetome':		return contact_inbox_move(true);
	case 'moveaway':		return contact_inbox_move(false);
	case 'setstatus':		return contact_set_status();
	case 'trackon':			return contact_set_tracking(true);
	case 'trackoff':		return contact_set_tracking(false);
	case 'release':			return contact_release();
	case 'duplicate':		return contact_duplicate();
	case 'stdanswercommit':	return contact_commit_stdanswer();
	case 'erase-ticket':	return contact_erase_ticket();
	case 'split':			return contact_split_ticket();
	}
}
function display_body() {
	switch ($_REQUEST['ACTION']) {
	default:
		not_found();
		return;

	case null:
	case 'moveaway':
	case 'release':
	case 'erase-ticket':
	case 'trackoff':
		contact_display_overview();
		return;

	case 'form':
		contact_display_form();
		return;

	case 'outgoing-form':
		if ($ticketid = have_idnumber($_REQUEST,'TICKETID')) {
			$_REQUEST['sID'] = $ticketid;
			contact_display_single();
		} else {
			contact_display_outgoing_form();
		}
		return;

	case 'options':
		contact_section_overview();
		return;

	case 'answerform':
	case 'movetome':
	case 'trackon':
	case 'insert':
	case 'duplicate':
	case 'split':
	case 'commit':
		contact_display_single();
		return;

	case 'single':
		if (!isset($_REQUEST['OVERVIEW'])
		&&	(	!have_post()
			||	isset($_REQUEST['KEEPLOCK'])
			||	isset($_POST['ANSWER'])
			||	isset($_POST['ANSWERCLOSE'])
			||	isset($_POST['OUTGOING'])
			)
		) {
			contact_display_single();
		} else {
			contact_display_overview();
		}
		return;

	case 'archive':
		contact_display_archive();
		return;
	case 'pending':
		contact_display_pending();
		return;

	case 'markread':
	case 'mine':
		contact_display_mine();
		return;

	case 'search':
		contact_display_search();
		return;

	case 'myhistory':
		contact_display_search(CURRENTUSERID,false);
		return;
	case 'myfuture':
		contact_display_search(CURRENTUSERID,true);
		return;

	case 'setstatus':
			!empty($_REQUEST['STATUS'])
		&&	$_REQUEST['STATUS'] === 'open'
		?	contact_display_single()
		:	contact_display_overview();
		return;

	case 'undeliverable':
		contact_display_undeliverable();
		return;

	case 'commitsignatures':
	case 'signatures':
		contact_display_signatures();
		return;

	case 'commitgroups':
	case 'groups':
		contact_display_groups();
		return;

	case 'stdanswers':
	case 'stdanswercommit':
		contact_display_stdanswers();
		return;

	case 'stdanswerform':
		contact_display_stdanswer_form();
		return;

	case 'old':
		contact_display_old();
		return;
	}
}

function contact_split_ticket(): bool {
	if (!require_admin()
	||	!require_post()
	||	!($oldticketid = require_idnumber($_REQUEST,'sID'))
	||	!($startmsgid  = require_idnumber($_REQUEST,'subID'))
	||	!require_ticket_lock($_REQUEST['sID'])
	) {
		return false;
	}
	if (!($message = db_single_assoc('contact_ticket_message', "
		SELECT *
		FROM contact_ticket_message
		WHERE CTMSGID = $startmsgid"))
	) {
		if ($message !== false) {
			_error('Ticketbericht met ID '.$startmsgid.' niet gevonden!');
		}
		return false;
	}
	if ($message['TICKETID'] !== $oldticketid) {
		_error('Opgegeven ticket ID '.$oldticketid.' komt niet overeen met ticket ID '.$message['TICKETID'].' gevonden bij ticketbericht '.$startmsgid);
		return false;
	}
	if ($message['DIRECTION'] !== 'toadmin') {
		_error('Je kunt alleen een ticket splitsen op een bericht naar medewerker!');
		return false;
	}
	if (!($oldticket = db_single_assoc('contact_ticket', '
		SELECT * FROM contact_ticket
		WHERE TICKETID = '.$oldticketid
	))) {
		if ($oldticket !== false) {
			register_error('contact_ticket:error:nonexistent_LINE', ['ID' => $oldticketid]);
		}
		return false;
	}
	if (!($lastmessage = db_single_assoc('contact_ticket_message', "
		SELECT *
		FROM contact_ticket_message
		WHERE TICKETID = $oldticketid
		  AND CTMSGID  < $startmsgid
		ORDER  BY CTMSGID DESC
		LIMIT 1"
	))) {
		if ($lastmessage !== false) {
			mail_log('no last messages found to start split from', get_defined_vars());
			_error('Is splitsticketbericht het eerste bericht van dit ticket?');
		}
		return false;
	}
	if (!($msgcnts = db_single_assoc('contact_ticket_message', "
		SELECT	COUNT(IF(CTMSGID <  $startmsgid, 1, NULL)) AS OLD_MSGCNT,
				COUNT(IF(CTMSGID <	$startmsgid AND DIRECTION = 'touser', 1, NULL)) AS OLD_OMSGCNT,
				COUNT(IF(CTMSGID >= $startmsgid, 1, NULL)) AS NEW_MSGCNT,
				COUNT(IF(CTMSGID >= $startmsgid AND DIRECTION = 'touser', 1, NULL)) AS NEW_OMSGCNT
			FROM contact_ticket_message
			WHERE DIRECTION != 'forwardmsg'
			  AND TICKETID   = $oldticketid"))
	||	(!($is_mail = db_single('contact_ticket_mail', "
		SELECT b'1'
		FROM contact_ticket_mail
		WHERE TICKETID = $oldticketid
		  AND CTMSGID >= $startmsgid
		LIMIT 1"))
	&& 	query_failed())
	) {
		return false;
	}
	foreach ($oldticket as $key => $val) {
		$setlist[] = match($key) {
			default			=> "$key = '".addslashes($val)."'",
			'READM',
			'MAILING'		=> $key." = b'0'",
			'SRCTICKETID'	=> $key.' = '.$oldticketid,
			'CSTAMP'		=> $key.' = '.$message['CSTAMP'],
			'MSGCNT'		=> $key.' = '.$msgcnts['NEW_MSGCNT'],
			'OMSGCNT'		=> $key.' = '.$msgcnts['NEW_OMSGCNT'],
			'TICKETID'		=> $key.' = 0',
			'TRACKIT',
			'OUTGOING',
			'LASTMAIL'		=> $key.' = '.($val ? "b'1'" : "b'0'"),
			'ID'			=> $key.' = '.$val,
			'ISMAIL'		=> $key.' = '.($is_mail ? "b'1'" : "b'0'"),
		};
	}
	if (!db_insert('contact_ticket', '
		INSERT INTO contact_ticket
		SET '.implode(', ', $setlist))
	||	!($newticketid = db_insert_id())
	||	!db_update('contact_ticket_message','
		UPDATE contact_ticket_message
			SET TICKETID = '.$newticketid.'
		WHERE TICKETID = '.$oldticketid.'
		  AND CTMSGID >= '.$startmsgid)
	||	!db_update('contact_ticket_mail','
		UPDATE contact_ticket_mail
			SET TICKETID = '.$newticketid.'
		WHERE TICKETID = '.$oldticketid.'
		  AND CTMSGID >= '.$startmsgid)
	||	!db_insert('contact_ticket_log','
		INSERT INTO contact_ticket_log
		SELECT * FROM contact_ticket
		WHERE TICKETID = '.$oldticketid)
	||	!db_update('contact_ticket','
		UPDATE contact_ticket SET
			LSTAMP		= '.$lastmessage['CSTAMP'].',
			MSGCNT		= '.$msgcnts['OLD_MSGCNT'].',
			OMSGCNT		= '.$msgcnts['OLD_OMSGCNT'].',
			LASTDIRECTION	= "'.$lastmessage['DIRECTION'].'",
			MSTAMP		= '.$lastmessage['CSTAMP'].',
			MUSERID		= '.$lastmessage['USERID_FROM'].',
			LASTMAIL	= '.(
				db_single('contact_ticket_message','
					SELECT ISMAIL
					FROM contact_ticket_message
					WHERE DIRECTION="toadmin"
					  AND TICKETID='.$oldticketid.'
					ORDER BY CTMSGID DESC
					LIMIT 1',DB_USE_MASTER) ?: 0
				).',
			STATUS		='.(isset($_POST['CLOSEOLD']) ? '"closed"' : 'STATUS').'
		WHERE TICKETID='.$oldticketid)
	||	!db_insert('connect', "
		INSERT INTO connect (MAINTYPE, MAINID, ASSOCTYPE, ASSOCID, CSTAMP, CUSERID)
		SELECT MAINTYPE, $newticketid, ASSOCTYPE, ASSOCID, CSTAMP, CUSERID
		FROM connect
		WHERE MAINTYPE = 'contact_ticket'
		  AND MAINID = $oldticketid
		UNION
		SELECT MAINTYPE, MAINID, ASSOCTYPE, $newticketid, CSTAMP, CUSERID
		FROM connect
		WHERE ASSOCTYPE = 'contact_ticket'
		  AND ASSOCID = $oldticketid")
	) {
		return false;
	}
	$_REQUEST['sID'] = $newticketid;
	db_insert('ticketaction','
	INSERT INTO ticketaction SET
		TICKETID	='.$oldticketid.',
		NEWTICKETID	='.$newticketid.',
		ACTION		="split",
		CTMSGID		='.$startmsgid.',
		STAMP		='.CURRENTSTAMP.',
		USERID		='.CURRENTUSERID
	);
	register_notice('contact_ticket:notice:split_LINE',DO_UBB,['OLDTICKETID' => $oldticketid]);
	contact_clear_cache();
	return true;
}

function flush_standard_answers($arg) {
	if (!is_array($arg)) {
		memcached_delete('stdanswers:'.$arg);
	} else {
		foreach ($arg as $element) {
			memcached_delete('stdanswers:'.$element);
		}
	}
}

function contact_commit_stdanswer() {
	require_once 'defines/contactstandardanswer.inc';
	require_once '_namefix.inc';
	if (!require_admin('stdanswers')) {
		return;
	}
	if (isset($_POST['REMOVE'])) {
		if (!require_idnumber($_REQUEST,'DATAID')) {
			return;
		}
		$dataid = $_REQUEST['DATAID'];
		$elements = db_simpler_array('contact_standard_answer','
			SELECT DISTINCT ELEMENT
			FROM contact_standard_answer
			WHERE DATAID='.$dataid
		);
		if ($elements === false) {
			return;
		}
		if (/*	!db_delete('contact_standard_answer_data','
			DELETE FROM contact_standard_answer_data
			WHERE DATAID='.$dataid)
		||*/
			!db_insert('contact_standard_answer_log','
			INSERT INTO contact_standard_answer_log
			SELECT * FROM contact_standard_answer
			WHERE DATAID='.$dataid)
		||	!db_delete('contact_standard_answer','
			DELETE FROM contact_standard_answer
			WHERE DATAID='.$dataid)
		) {
			return;
		}
		register_notice('standard_answer:notice:removed_LINE');
		flush_standard_answers($elements);
		return;
	}
	if (!require_something_trim($_POST, 'TITLE')
	||	!require_something_trim($_POST, 'BODY')
	||	!require_array($_POST, 'ELEMENTS')
	) {
		return;
	}
	$flags = 0;
	if (isset($_POST['FLAGS'])) {
		if (!require_number_array($_POST, 'FLAGS')) {
			return;
		}
		foreach ($_POST['FLAGS'] as $flg) {
			$flags |= $flg;
		}
	}
	$setlist[] = 'TITLE="'.	addslashes($_POST['TITLE']).'"';
	$setlist[] = 'BODY="'.	_ubb_preprocess(addslashes(get_fixed_name($_POST['BODY']))).'"';
	$setlist[] = 'FLAGS='.	$flags;

	if (isset($_REQUEST['DATAID'])) {
		$setlist[] = 'MSTAMP='.	CURRENTSTAMP;
		$setlist[] = 'MUSERID='.CURRENTUSERID;
		if (!require_idnumber($_REQUEST,'DATAID')
		||	!db_insert('contact_standard_answerdata_log','
			INSERT INTO contact_standard_answer_data_log
			SELECT * FROM contact_standard_answer_data
			WHERE DATAID='.$_REQUEST['DATAID'])
		) {
			return;
		}
		if (!db_update('contact_standard_answer_data','
			UPDATE contact_standard_answer_data SET '.implode(',',$setlist).'
			WHERE DATAID='.$_REQUEST['DATAID'])
		||	!db_delete('contact_standard_answer','
			DELETE FROM contact_standard_answer
			WHERE DATAID='.$_REQUEST['DATAID'])
		) {
			return;
		}
		$dataid = $_REQUEST['DATAID'];
	} else {
		$setlist[] = 'CSTAMP='.	CURRENTSTAMP;
		$setlist[] = 'CUSERID='.CURRENTUSERID;
		if (!db_insert('contact_standard_answer_data','
			INSERT INTO contact_standard_answer_data SET '.implode(',',$setlist))
		) {
			return;
		}
		$dataid = db_insert_id();
	}
	foreach ($_POST['ELEMENTS'] as $element) {
		if (!db_insert('contact_standard_answer','
			INSERT INTO contact_standard_answer SET
				ELEMENT	="'.addslashes($element).'",
				MSTAMP	='.CURRENTSTAMP.',
				MUSERID	='.CURRENTUSERID.',
				DATAID	='.$dataid)
		) {
			return;
		}
	}
	register_notice(isset($_REQUEST['DATAID']) ? 'standard_answer:notice:changed_LINE' : 'standard_answer:notice:added_LINE');
	flush_standard_answers($_POST['ELEMENTS']);
}

function contact_display_stdanswers(): void {
	require_once 'defines/contactstandardanswer.inc';
	if (!require_admin('stdanswers')) {
		return;
	}
	layout_show_section_header(Eelement_plural_name('standard_answer'));

	contact_menu();

	layout_open_menu();
	layout_menuitem(__C('action:add'),'/contact/stdanswerform');
	layout_close_menu();

	if (!($tmpanswers = db_multirowuse_hash(['contact_standard_answer', 'contact_standard_answer_data'], '
		SELECT ELEMENT, TITLE, DATAID, ANSWERID, FLAGS
		FROM party_db.contact_standard_answer_data
		JOIN party_db.contact_standard_answer USING (DATAID)
		WHERE NOT (FLAGS & '.STDANSWER_REMOVED.')
		ORDER BY ELEMENT, TITLE'
	))) {
		return;
	}
	$stdanswers = [1 => [], 0 => []];
	foreach ($tmpanswers as $element => $answers) {
		foreach ($answers as $answer) {
			$which = array();
			if ($answer['FLAGS'] & STDANSWER_OUTGOING) {
				$which[] = 1;
			}
			if ($answer['FLAGS'] & STDANSWER_INCOMING) {
				$which[] = 0;
			}
			if (!$which) {
				# default
				$which[] = null;
			}
			foreach ($which as $outgoing) {
				$stdanswers[$outgoing][$answer['ELEMENT']][] = $answer;
			}
		}
	}

	foreach ($stdanswers as $outgoing => $elements) {
		if (!$elements) {
			return;
		}

		layout_open_box('contact');
		layout_box_header(escape_specials($outgoing ? 'Standaard antwoorden voor uitgaande tickets' : 'Standaard antwoorden voor inkomende tickets'));

		unset($newelements, $iselem);

		foreach ($elements as $element => $stdanswers) {
			$newelements[$elemname = element_plural_name($element)] = $stdanswers;
			$iselem[$elemname] = $element;
		}
		ksort($newelements);
		foreach ($newelements as $elementname => $stdanswers) {
			?><div class="ptr block" onclick="swapdisplay(this.nextSibling); toggleclass(this, 'bold-hilited');"><?
			?><b><?= $elementname ?></b> <?= MIDDLE_DOT_ENTITY ?> <?= count($stdanswers)
			?></div><?

			?><div class="hidden block"><?
			layout_open_table('fw');
			foreach ($stdanswers as $stdanswer) {
				layout_start_rrow();
				?><a href="/contact/stdanswerform/DATAID/<?= $stdanswer['DATAID'] ?>"><?=
					escape_specials($stdanswer['TITLE']);
				?></a><?
				layout_stop_row();
			}
			layout_close_table();
			?></div><?
		}
		layout_close_box();
	}
}

function show_element_choices($selected = null,$onlyaccess = false) {
	?><table class="split vtop"><tr><td><?
	$contactelements = get_contact_elements();
	foreach ($contactelements as $element => &$name) {
		$name = element_plural_name($element);
	}
	asort($contactelements);
	$cnt = 0;
	$percel = ceil(count($contactelements) / 3);
	foreach ($contactelements as $element => $descr) {
		if ($onlyaccess
		&&	!may_change_element($element,0,false)
		) {
			continue;
		}
		++$cnt;
		?><label<?
		if ($selected && in_array($element,$selected)) {
			$checked = true;
			?> class="bold-hilited"<?
		} else {
			$checked = false;
		}
		?> for="elem_<?= $element; ?>"><input<?
		if ($checked) {
			?> checked="checked"<?
		}
		?> name="ELEMENTS[]" id="elem_<?= $element; ?>" type="checkbox" onclick="cbclickp(this)" value="<?= $element;
		?>"> <?= $descr; ?></label><br /><?
		if ($cnt === $percel) {
			?></td><td><?
			$cnt = 0;
		}
	}
	?></td></tr></table><?
}
function contact_display_stdanswer_form() {
	require_once 'defines/contactstandardanswer.inc';
	if (!require_admin('stdanswers')
	||	!optional_number($_REQUEST,'DATAID')
	) {
		return;
	}
	layout_show_section_header(Eelement_name('standard_answer'));

	if (isset($_REQUEST['DATAID'])) {
		if (!($stdanswer = db_single_assoc(
			['contact_standard_answer','contact_standard_answer_data'], "
			SELECT	GROUP_CONCAT(ELEMENT) AS ELEMENTS,COUNT(*) AS ELEMENTCNT,
					TITLE,BODY,FLAGS
			FROM contact_standard_answer
			JOIN contact_standard_answer_data USING (DATAID)
			WHERE DATAID = {$_REQUEST['DATAID']}
			GROUP BY DATAID
			ORDER BY ELEMENT"
		))) {
			if ($stdanswer !== false) {
				register_error('standard_answer:error:nonexistent_LINE',array('ID'=>$_REQUEST['DATAID']));
			}
			return;
		}
	} else {
		$stdanswer = null;
	}
	$spec = explain_table('contact_ticket_message');

	?><form onsubmit="return submitForm(this)" method="post" action="/contact/stdanswercommit<?
	if ($stdanswer) {
		?>/DATAID/<? echo $_REQUEST['DATAID'];
	}
	?>"><?
	layout_open_box('contact');
	layout_open_table(TABLE_CLEAN | TABLE_VTOP);
	layout_start_row();
		echo Eelement_name('title');
		layout_field_value();
		?><input type="text" required="required" autofocus="autofocus" name="TITLE" value="<? if ($stdanswer['TITLE']) echo escape_specials($stdanswer['TITLE']) ?>" /><?
	layout_restart_row();
		echo Eelement_name('message');
		layout_field_value();
		?><textarea name="BODY" required="required" cols="80" rows="15" class="growToFit" maxlength="<?= $spec['BODY']->maxlength ?? '' ?>"><?
		if (!empty($stdanswer['BODY'])) {
			echo escape_specials($stdanswer['BODY']);
		}
		?></textarea><?
	layout_restart_row(($stdanswer['FLAGS'] ?? 0) & STDANSWER_THANKS ? ROW_HILITE : 0);
		?><label for="thanks">Bedankje</label><?
		layout_field_value();
		?><input<?
		if ($stdanswer['FLAGS'] & STDANSWER_THANKS) {
			?> checked="checked"<?
		}
		?> onclick="cbclickpp(this,'hilited')" name="FLAGS[]" id="thanks" type="checkbox" value="<?= STDANSWER_THANKS ?>" /> <?
		?><label for="thanks">&quot;Bedankt voor je melding!&quot;</label><?
	layout_restart_row($checked = ($stdanswer['FLAGS'] ?? 0)  & STDANSWER_INFO_MSG ? ROW_HILITE : 0);
			?><label for="infomsg"><?= Eelement_name('flock_info'); ?></label><?
		layout_field_value();
		?><input <?
		if ($checked) {
			?>checked="checked" <?
		}
		?>onclick="cbclickpp(this,'hilited')" <?
		?>name="FLAGS[]" id="infomsg" type="checkbox" value="<?= STDANSWER_INFO_MSG ?>"><?
	layout_restart_row(($stdanswer['FLAGS'] ?? 0) & STDANSWER_INCOMING ? ROW_HILITE : 0);
		?><label for="incoming"><?= __C('status:incoming') ?></label><?
		layout_field_value();
		?><input <?
		if ($stdanswer['FLAGS'] & STDANSWER_INCOMING) {
			?>checked="checked" <?
		}
		?>onclick="cbclickpp(this,'hilited');setdisplay('autocloserow',this.checked)" <?
		?>name="FLAGS[]" id="incoming" type="checkbox" value="<?= STDANSWER_INCOMING ?>" /><?

	layout_restart_row(($stdanswer['FLAGS'] ?? 0) & STDANSWER_OUTGOING ? ROW_HILITE : 0);
		?><label for="outgoing"><?= __C('status:outgoing') ?></label><?
		layout_field_value();
		?><input <?
		if ($stdanswer['FLAGS'] & STDANSWER_OUTGOING) {
			?>checked="checked" <?
		}
		?>onclick="cbclickpp(this,'hilited');setdisplay('autocloserow',this.checked)" <?
		?>name="FLAGS[]" id="outgoing" type="checkbox" value="<?= STDANSWER_OUTGOING ?>"><?

	layout_restart_row(
			(($stdanswer['FLAGS'] ?? 0) & STDANSWER_AUTO_CLOSE ? ROW_HILITE : 0)
		|	(($stdanswer['FLAGS'] ?? 0) & STDANSWER_OUTGOING ? 0 : ROW_HIDDEN),
		'autocloserow'
	);
		?><label for="auto-close" class="nowrap"><?= __C('action:close_automatically') ?></label><?
		layout_field_value();
		?><input<?
		if (($stdanswer['FLAGS'] ?? 0) & STDANSWER_AUTO_CLOSE) {
			?> checked="checked"<?
		}
		?> onclick="cbclickpp(this,'hilited')" name="FLAGS[]" id="auto-close" type="checkbox" value="<?= STDANSWER_AUTO_CLOSE ?>"><?
	layout_restart_row();
		echo Eelement_plural_name('element');
		layout_field_value();
		show_element_choices(!empty($stdanswer['ELEMENTS']) ? explode(',',escape_specials($stdanswer['ELEMENTS'])) : null);
	layout_close_table();
	layout_close_box();
	?><div class="block"><?
	?><input class="l" type="submit" value="<?= __($stdanswer ? 'action:change' : 'action:add') ?>" /><?
	?><input class="r" type="submit" name="REMOVE" value="<?= __('action:remove') ?>" /><?
	?></div><?
	?><div class="clear"></div><?
	?></form><?
}

function contact_duplicate(): void {
	if (!($ticketid = require_idnumber($_REQUEST,'sID'))
	||	!($ctmsgid  = require_idnumber($_REQUEST,'subID'))
	) {
		return;
	}
	require_once '_duplicate_children.inc';
	if (!($ticket = db_single_assoc('contact_ticket','SELECT * FROM contact_ticket WHERE TICKETID='.$ticketid))) {
		if ($ticket !== false) {
			register_error('contact_ticket:error:nonexistent_LINE', ['ID' => $ticketid]);
		}
		return;
	}
	if (!require_ticket_lock($_REQUEST['sID'])) {
		return;
	}
	$onlythis = isset($_POST['ONLYTHISMESSAGE']);

	if ($ticket['ORG_ELEMENT']
	&&	$ticket['ORG_ID']
	||	!(	$ticket['ELEMENT']
		&&	$ticket['ID']
		)
	) {
		# set org element to current org element if both org_element and org_id is set, or
		# if the current element and current id are not both set
		# this way, most information is kept
		$newsetlist[] = 'ORG_ELEMENT="'.$ticket['ORG_ELEMENT'].'",ORG_ID='.$ticket['ORG_ID'];
	} else {
		$newsetlist[] = 'ORG_ELEMENT="'.$ticket['ELEMENT'].'",ORG_ID='.$ticket['ID'];
	}
	foreach ($ticket as $key => $val) {
		switch ($key) {
		default:
			$newsetlist[] = $key.'="'.addslashes($val).'"';
			continue 2;
		case 'CSTAMP':
		case 'LSTAMP':
		case 'MSTAMP':
			$newsetlist[] = $key.'='.CURRENTSTAMP;
			continue 2;

		case 'MAILING':
		case 'ISMAIL':
		case 'LASTMAIL':
		case 'Oq
		
		
		UTGOING':
		case 'TRACKIT':
			$newsetlist[] = $key.'='.($val ? "b'1'" : "b'0'");
			continue 2;

		case 'TICKETID':
		case 'READM':
			continue 2;
		case 'OMSGCNT':
			$newsetlist[] = 'OMSGCNT=0';
			continue 2;
		case 'MSGCNT':
			$newsetlist[] = 'MSGCNT=1';
			continue 2;
		case 'STATUS':
			$newsetlist[] = 'STATUS="open"';
			continue 2;
		case 'ORG_ID':
		case 'ORG_ELEMENT':
			continue 2;
		case 'ELEMENT':
		case 'OWNERID':
		case 'ID':
			if (!isset($_POST['MAKEMEOWNER'])) {
				$newsetlist[] = $key.'="'.addslashes($val).'"';
				break;
			}
			switch ($key) {
			case 'OWNERID':
				$newsetlist[] = 'OWNERID='.CURRENTUSERID;
				break;
			case 'ELEMENT':
				$newsetlist[] = 'ELEMENT="sm"';
				break;
			case 'ID':
				$newsetlist[] = 'ID=0';
				break;
			}
			break;
		}
	}
	if ($onlythis) {
		if (!($message = db_single_assoc('contact_ticket_message', '
			SELECT * FROM contact_ticket_message
			WHERE CTMSGID='.$ctmsgid
		))) {
			if ($message !== false) {
				register_error('contact_ticket_message:error:nonexistent_LINE', ['ID' => $ctmsgid]);
			}
			return;
		}
		$messages = [$message];
	} else {
		if (!($messages = db_rowuse_array('contact_ticket_message','
			SELECT * FROM contact_ticket_message
			WHERE TICKETID = '.$ticket['TICKETID'].'
			  AND CTMSGID >= '.$ctmsgid
		))) {
			if ($messages !== false) {
				register_error('contact_ticket_message:error:no_message_for_ticket_LINE',[ 'TICKETID' => $ticket['TICKETID']]);
			}
			return;
		}
		number_sort($messages, 'CTMSGID');
	}

	if (!db_insert('contact_ticket', 'INSERT INTO contact_ticket SET '.implode(', ', $newsetlist))) {
		return;
	}
	$newticketid = db_insert_id();

	number_sort($messages, 'CTMSGID');

	$msgcnt = $omsgcnt = 0;
	$lastmail = 0;
	$havemail = false;

	foreach ($messages as $message) {
		++$msgcnt;
		switch ($message['DIRECTION']) {
		case 'touser':
			++$omsgcnt;
			break;
		case 'toadmin':
			$lastmail = $message['ISMAIL'];
			break;
		}
		$ctmsgid = $message['CTMSGID'];
		unset($message['CTMSGID'], $setlist);
		$message['TICKETID'] = $newticketid;
		$setlist = [];
		foreach ($message as $key => $val) {
			$setlist[] = match ($key) {
				'CSTAMP',
				'CTMSGID',
				'FORMSTAMP',
				'HTMLSRC',
				'INFOMSG',
				'ISMAIL',
				'TICKETID'	=> "$key = $val",
				default		=> "$key = '".addslashes($val)."'"
			};
		}
		if (!db_insert('contact_ticket_message','INSERT INTO contact_ticket_message SET '.implode(',',$setlist))) {
			return;
		}
		$newctmsgid = db_insert_id();
		unset($setlist);
		if (!($mail = db_single_assoc('contact_ticket_mail', "
			SELECT * 
			FROM contact_ticket_mail
			WHERE CTMSGID = $ctmsgid"))
		) {
			return;
		}
		if ($mail) {
			$havemail = true;
			$mail['TICKETID'] = $newticketid;
			$mail['CTMSGID'] = $newctmsgid;
			foreach ($mail as $key => $val) {
				$setlist[] = '$key = '.match ($key) {
					'UTF8',
					'CTMSGID',
					'TICKETID',
					'CIMID',
					'OUTGOING',
					'INVALIDFROM',
					'INFECTED',
					'AUTOGEN',
					'SPAMSCORE'	=> $key.' = '.(int)$val,
					default		=> $key.'="'.addslashes($val).'"',
				};
			}
			if (!db_insert('contact_ticket_mail','INSERT INTO contact_ticket_mail SET '.implode(',',$setlist))) {
				return;
			}
		}
		// copy optional attachment info

		duplicate_children('CTMSGID',$ctmsgid,$newctmsgid,'contact_ticket_attachment');
	}
	if (!db_update('contact_ticket','
		UPDATE contact_ticket SET
			LASTMAIL='.$lastmail.',
			ISMAIL	='.($havemail ? 1 : 0).',
			MSGCNT	='.$msgcnt.',
			OMSGCNT	='.$omsgcnt.'
		WHERE TICKETID='.$newticketid)
	||	!db_insert('connect','
		INSERT INTO connect (MAINTYPE,MAINID,ASSOCTYPE,ASSOCID,CSTAMP,CUSERID)
		SELECT MAINTYPE,'.$newticketid.',ASSOCTYPE,ASSOCID,CSTAMP,CUSERID
		FROM connect
		WHERE MAINTYPE="contact_ticket"
		  AND MAINID='.$ticketid.'
		UNION
		SELECT MAINTYPE,MAINID,ASSOCTYPE,'.$newticketid.',CSTAMP,CUSERID
		FROM connect
		WHERE ASSOCTYPE="contact_ticket"
		  AND ASSOCID='.$ticketid)
	) {
		return;
	}
	$_REQUEST['sID'] = $newticketid;
	db_insert('ticketaction','
	INSERT INTO ticketaction SET
		TICKETID	='.$ticketid.',
		NEWTICKETID	='.$newticketid.',
		ACTION		="duplicate",
		CTMSGID		='.$ctmsgid.',
		ONLYTHIS	=b\''.($onlythis ? 1 : 0).'\',
		STAMP		='.CURRENTSTAMP.',
		USERID		='.CURRENTUSERID
	);
	register_notice('contact_ticket:notice:new_ticket,new_shown_LINE',DO_UBB,array('NEWTICKETID'=>$newticketid,'OLDTICKETID'=>$ticketid));
	contact_clear_cache();
}
function contact_release() {
	if (!($ticketid = require_idnumber($_REQUEST,'sID'))
	||	!require_ticket_lock($ticketid)
	) {
		return;
	}
	release_lock(LOCK_TICKET,$ticketid);
	register_notice('contact_ticket:notice:released_LINE',DO_UBB, ['TICKETID' => $ticketid]);
}
function contact_clear_cache() {
	tickets_updated(true);
	if (!db_delete('contact_ticket_cache','
		TRUNCATE contact_ticket_cache')
	) {
		return;
	}
}
function contact_inbox_move(bool $to_me): bool {
	if (!($ticketid = require_idnumber($_REQUEST, 'sID'))
	||	!require_ticket_lock($ticketid)
	) {
		return false;
	}
	$ownerid = $to_me ? CURRENTUSERID : 0;

	if (!db_insert('contact_ticket_log','
		INSERT INTO contact_ticket_log
		SELECT * FROM contact_ticket
		WHERE TICKETID = '.$ticketid.'
		  AND OWNERID != '.$ownerid)
	) {
		return false;
	}
	if (db_affected()) {
		contact_clear_cache();
		if (!db_update('contact_ticket', "
			UPDATE contact_ticket SET
				OWNERID	= $ownerid,
				MUSERID	= ".CURRENTUSERID.',
				MSTAMP	= '.CURRENTSTAMP."
			WHERE TICKETID = $ticketid
			  AND OWNERID != $ownerid")
		) {
			return false;
		}
	}
	release_lock(LOCK_TICKET, $ticketid);
	register_notice(
			$to_me
		?	'contact_ticket:notice:moved_to_inbox_LINE'
		:	'contact_ticket:notice:moved_out_of_inbox_LINE',
		DO_UBB,
		['TICKETID' => $ticketid]
	);
	return true;
}

function contact_forward(): bool {
	if (!($userid = require_idnumber($_POST, 'TO_USERID'))
	||	!($ticketid = require_idnumber($_REQUEST, 'sID'))
	||	!require_ticket_lock($ticketid)
	||	!db_insert('contact_ticket_log','
		INSERT INTO contact_ticket_log
		SELECT * FROM contact_ticket
		WHERE TICKETID = '.$ticketid.'
		  AND OWNERID != '.$userid)
	) {
		return false;
	}
	if (db_affected()
	&&	!db_update('contact_ticket','
		UPDATE contact_ticket SET
			MSTAMP		= '.CURRENTSTAMP.',
			MUSERID		= '.CURRENTUSERID.",
			OWNERID		= $userid,
			TRACKIT		= IF(STATUS = 'open', TRACKIT, 1)
		WHERE TICKETID = $ticketid
		  AND OWNERID != $userid")
	) {
		return false;
	}
	release_lock(LOCK_TICKET, $ticketid);
	register_notice('contact_ticket:notice:forwarded_LINE', DO_UBB, ['TICKETID' => $ticketid, 'USERID' => $userid]);
	contact_clear_cache();
	return true;
}

function contact_display_groups() {
	layout_section_header($focus_groups_name = Eelement_plural_name('focus_group'));
	if (!require_admin()) {
		return;
	}
	if (false === ($focus_groups = db_simpler_array('ticketgroup', 'SELECT ELEMENT FROM ticketgroup WHERE USERID = '.CURRENTUSERID))) {
		return;
	}
	?><form method="post" onsubmit="return submitForm(this);" action="/contact/commitgroups"><?
	layout_open_box('contact');
	layout_box_header($focus_groups_name);
	show_element_choices($focus_groups, true);
	layout_close_box();
	?><div class="bock"><?
		?><input type="submit" value="<?= __('action:change'); ?>" /><?
	?></div><?
	?></form><?
}

function contact_display_signatures(): void {
	if (!require_admin()) {
		return;
	}
	require_once '_bubble.inc';
	layout_show_section_header(Eelement_plural_name('contact_setting'));

	require_once '_salutes.inc';
	if (false === ($elems = get_contact_elements())) {
		return;
	}
	# Satisfy EA inspection
	$elems = (array)$elems;
	$elems['default'] = 'default';
	foreach ($elems as $element => $true) {
		if ($element === 'default'
		||	have_admin($element)
		) {
			$elements[$element] = [
				'ELEMENT'		=> $element,
				'ELEMENTNAME'	=> element_name($element),
				'SIGNATURES'	=> query_contact_signature($element)
			];
		}
	}
	uasort($elements, static function(array $a, array $b): int {
		# default first
		$rc = ($a['ELEMENT'] === 'default' ? 0 : 1) - ($b['ELEMENT'] === 'default' ? 0 : 1);
		if ($rc) {
			return $rc;
		}
		$rc = (($a['SIGNATURES']['ENABLED'] ?? false) ? 0 : 1) - (($b['SIGNATURES']['ENABLED'] ?? false) ? 0 : 1);
		if ($rc) {
			return $rc;
		}
		return strcasecmp($a['ELEMENTNAME'], $b['ELEMENTNAME']);
	});

	?><form<?
	?> accept-charset="utf-8"<?
	?> method="post"<?
	?> onsubmit="return submitForm(this);"<?
	?> action="/contact/commitsignatures"><?

	layout_open_box('contact');
	foreach ($elements as $info) {
		extract($info, \EXTR_OVERWRITE);

		ob_start();
		?><label><?= __C('action:use_default') ?>: <input<?
		if ($use_default = empty($SIGNATURES['ENABLED'])) {
			?> checked<?
		}
		?> type="checkbox"<?
		?> name="DEFAULT['<?= $ELEMENT ?>']" value="<?= $ELEMENT ?>"<?
		?> onclick="<?
			?>setdisplay('set_' + this.value.replace('-', '_'), !this.checked);<?
			?>Pf.enable(this.form.set_<?= $UNDERSCORE_ELEMENT = str_replace('-', '_', $ELEMENT) ?>, !this.checked)<?
			if ($ELEMENT === 'default') {
				?>;setdisplay('generated', this.checked);<?
			}
		?>"></label><?
		layout_box_header($ELEMENTNAME, ob_get_clean(), 'hl');

		?><fieldset<?
		?> id="set_<?= $UNDERSCORE_ELEMENT ?>"<?
		?> name="fields_<?= $UNDERSCORE_ELEMENT ?>"<?
		?> class="clean<?
		if ($use_default) {
			?> hidden<?
		}
		?>"<? $disabled = $SIGNATURES ? '' : ' disabled' ?>><?

		extract($SIGNATURES ?: generate_contact_signature(), \EXTR_OVERWRITE);

		layout_open_table(TABLE_CLEAN | TABLE_FULL_WIDTH);
		layout_start_row();
		echo Eelement_name('sender');
		layout_field_value();
		?><input<?= $disabled ?> type="text" maxlength="64" type="text" name="FROMNAME[<?= $ELEMENT ?>]" value="<?= escape_utf8($FROMNAME) ?>" /><?

		// HEADER
		layout_restart_row();
		echo Eelement_name('salutation');
		layout_field_value();
		?><input<?= $disabled ?> type="text" maxlength="255" name="HEADER[<?= $ELEMENT ?>]" value="<?= escape_utf8($HEADER) ?>" /><?

		 // HEADER_LS
		layout_restart_row(0,null,'nowrap');
		echo Eelement_name('salutation_LS');
		layout_field_value();
		?><input<?= $disabled ?> type="text" maxlength="255" name="HEADER_LS[<?= $ELEMENT ?>]" value="<?= escape_utf8($HEADER_LS) ?>" /><?

		// FOOTER
		layout_restart_row();
		echo Eelement_name('closing');
		layout_field_value();
		?><textarea<?= $disabled ?> cols="40" rows="5" class="growToFit" data-max="<?= max(3, mb_substr_count($FOOTER, "\n")+1) ?>" name="FOOTER[<?= $ELEMENT ?>]"><?= escape_utf8($FOOTER);
		?></textarea><?
		layout_stop_row();
		layout_close_table();
		?></fieldset><?

		if ($ELEMENT === 'default') {
			extract(generate_contact_signature(), \EXTR_OVERWRITE);
			layout_open_table('vtop', 'generated', $SIGNATURES ? 'hidden' : '');
			// FROMNAME
			layout_start_row();
			echo Eelement_name('sender');
			layout_field_value();
			echo escape_utf8($FROMNAME);

			// HEADER
			layout_restart_row();
			echo Eelement_name('salutation');
			layout_field_value();
			echo escape_utf8($HEADER);

			// HEADER_LS
			layout_restart_row();
			echo Eelement_name('salutation_LS');
			layout_field_value();
			echo escape_utf8($HEADER_LS);

			// FOOTER
			layout_restart_row();
			echo Eelement_name('closing');
			layout_field_value();
			echo nl2br(escape_utf8($FOOTER));

			layout_stop_row();
			layout_close_table();
		}
	}
	layout_close_box();
	?><div class="block"><input type="submit" value="<?= __('action:change'); ?>" /></div><?
	?></form><?
}

function contact_commit_groups() {
	if (!require_admin()
	||	!require_array_of_contact_elements($_POST, 'ELEMENTS',ACCEPT_EMPTY)
	) {
		return;
	}
	if (!empty($_POST['ELEMENTS'])) {
		foreach ($_POST['ELEMENTS'] as $element) {
			$setlist[] = '('.CURRENTUSERID.',"'.addslashes($element).'")';
		}
	}
	if (!db_delete('ticketgroup','
		DELETE FROM ticketgroup
		WHERE USERID='.CURRENTUSERID)
	||	(	isset($setlist)
		?	!db_insert('ticketgroup','INSERT INTO ticketgroup (USERID,ELEMENT) VALUES '.implode(',',$setlist))
		:	false
		)
	) {
		return;
	}
	register_notice('contact_ticket:notice:ticket_groups_changed_LINE');
	contact_clear_cache();
}

function contact_commit_signatures() {
	require_once '_salutes.inc';
	if (!require_admin()) {
		return false;
	}
	if (isset($_POST['FROMNAME'])) {
		$size = 0;
		if (!require_array_of_size($_POST, 'FROMNAME',  $size)
		||	!require_array_of_size($_POST, 'HEADER',	$size)
		||	!require_array_of_size($_POST, 'HEADER_LS', $size)
		||	!require_array_of_size($_POST, 'FOOTER',	$size)
		||	!require_array_with_keys_of_contact_element($_POST, 'FROMNAME',  ACCEPT_DEFAULT)
		||	!require_array_with_keys_of_contact_element($_POST, 'HEADER',	ACCEPT_DEFAULT)
		||	!require_array_with_keys_of_contact_element($_POST, 'HEADER_LS', ACCEPT_DEFAULT)
		||	!require_array_with_keys_of_contact_element($_POST, 'FOOTER',	 ACCEPT_DEFAULT)
		) {
			return false;
		}
		foreach ($_POST['FROMNAME'] as $element => $ignored) {
			foreach (['FROMNAME', 'HEADER', 'HEADER_LS', 'FOOTER'] as $key) {
				if (!require_something_trim($_POST[$key], $element, null, null, utf8: true)) {
					return false;
				}
				$$key = $_POST[$key][$element];
			}
			$defsignature = generate_contact_signature();

			if ($defsignature['FROMNAME']	=== $FROMNAME
			&&	$defsignature['HEADER']		=== $HEADER
			&&	$defsignature['HEADER_LS']	=== $HEADER_LS
			&&	$defsignature['FOOTER']		=== $FOOTER
			) {
				$_POST['DEFAULT'][$element] = $element;
				continue;
			}
			$setlist['FROMNAME']	= $FROMNAME;
			$setlist['HEADER']		= $HEADER;
			$setlist['HEADER_LS']	= $HEADER_LS;
			$setlist['FOOTER']		= $FOOTER;
			$setlist['ENABLED']		= true;

			[$instr, $valstr, $bincmp] = inserts_values_and_bincmp($setlist);

			if (!db_insert('contact_signatures','
				INSERT INTO contact_signatures_log
				SELECT * FROM contact_signatures
				WHERE NOT '.$bincmp.'
				  AND ELEMENT = "'.$element.'"
				   AND USERID = '.CURRENTUSERID)
			||	(	!db_affected()
				&&	db_single('contact_signatures','SELECT 1 FROM contact_signatures WHERE ELEMENT = "'.$element.'" AND USERID = '.CURRENTUSERID)
				?	false
				:	!db_insupd('contact_signatures','
					INSERT INTO contact_signatures SET '.
						$instr.',
						ELEMENT		= "'.$element.'",
						USERID		= '.CURRENTUSERID.',
						CSTAMP		= '.CURRENTSTAMP.'
					ON DUPLICATE KEY UPDATE '.
						$valstr.',
						MSTAMP		= '.CURRENTSTAMP)
				)
			) {
				return false;
			}
		}
	}
	if (isset($_POST['DEFAULT'])) {
		if (!db_insert('contact_signatures_log','
			INSERT INTO contact_signatures_log
			SELECT * FROM contact_signatures
			WHERE ELEMENT IN ('.($defstr = stringsimplode(',', $_POST['DEFAULT'])).')
			  AND ENABLED = b\'1\'
			  AND USERID = '.CURRENTUSERID)
		||	!db_update('contact_signatures','
			UPDATE contact_signatures SET
				MSTAMP	= '.CURRENTSTAMP.',
				ENABLED	= b\'0\'
			WHERE ENABLED = b\'1\'
			  AND ELEMENT IN ('.$defstr.')
			  AND USERID = '.CURRENTUSERID)
		) {
			return false;
		}
	}
	register_notice('settings:notice:changed_LINE');
	return true;
}

function contact_display_undeliverable() {
	layout_open_section_header();
	echo __C('status:undeliverable');
	layout_close_section_header();

	if ($cimid = have_idnumber($_REQUEST,'subID')) {
		if (!require_admin()) {
			return;
		}
		if (!($undeliverable = db_single_assoc('contact_undeliverable','
			SELECT CSTAMP, FAILMAIL, FAILREASON, SUBJECT, CONTENT
			FROM contact_undeliverable
			WHERE CIMID = '.$cimid))
		) {
			register_error('contact_undeliverable:error:nonexistent_LINE', ['ID' => $cimid]);
			#_error('Onberzorgbaar bericht met ID '.$cimid.' is niet opvraagbaar!');
			return;
		}
		layout_open_box('contact');
		layout_open_table();
		layout_start_row();
			echo Eelement_name('email');
			layout_field_value();
			echo _make_illegible($undeliverable['FAILMAIL']);
		layout_restart_row();
			echo Eelement_name('subject');
			layout_field_value();
			echo escape_specials($undeliverable['SUBJECT']);
		layout_restart_row();
			echo Eelement_name('reason');
			layout_field_value();
			?><b><?= escape_specials($undeliverable['FAILREASON']);
			?></b><?
		layout_restart_row();
			echo Eelement_name('entire_message');
			layout_field_value();
			?><pre><?
			if (have_super_admin()) {
				echo escape_specials($undeliverable['CONTENT']);
			} else {
				echo escape_specials(preg_replace('/((?:Je|Your)\s*(?:wachtwoord|password|nick|USERID)(?:\s*is)?:\s*.*)/','\1*****',$undeliverable['CONTENT']));
			}
			?></pre><?
		layout_stop_row();
		layout_close_table();
		layout_close_box();
		return;
	}
	if (!require_super_admin()) {
		return;
	}
	// display overview

	if (!($undeliverables = db_rowuse_array('contact_undeliverable','
		SELECT CSTAMP, CIMID, FAILMAIL, FAILREASON, SUBJECT
		FROM party_db.contact_undeliverable
		ORDER BY CIMID DESC
		LIMIT 300'))
	) {
		return;
	}
	layout_open_box('contact');
	layout_open_table(TABLE_FULL_WIDTH);
	$mailto = _make_illegible('mailto:');
	foreach ($undeliverables as $undeliverable) {
		$email = _make_illegible($undeliverable['FAILMAIL']);
		// CSTAMP
		layout_start_row();
		_datetime_display($undeliverable['CSTAMP'],'&nbsp;', short: true);
		// EMAIL
		layout_next_cell();
		?><a href="/contact/undeliverable/<?= $undeliverable['CIMID'];
		?>"><?= ($email ?: 'onbekend');
		?></a>&nbsp;<?
		// FAILREASON
		layout_next_cell();
		echo escape_specials($undeliverable['FAILREASON']);
		layout_stop_row();
	}
	layout_close_table();
	layout_close_box();
}
function contact_insert() {
	if (!require_admin()
	||	!($oldid = require_idnumber($_REQUEST,'sID'))
	||	!($ticketid = require_idnumber($_REQUEST,'subID'))
	) {
		return;
	}
	$newticket = db_single_array(
		array('contact_ticket','contact_ticket_message'),'
		SELECT	MSGCNT,OMSGCNT,
			(SELECT MAX(CTMSGID) FROM contact_ticket_message AS ctm WHERE ctm.TICKETID=ct.TICKETID)
		FROM contact_ticket AS ct
		WHERE TICKETID='.$ticketid
	);
	if ($newticket === false) {
		return;
	}
	if (!$newticket) {
		register_notice('contact_ticket:error:nonexistent_LINE',array('ID'=>$oldid));
		return;
	}
	$ctmsgid = db_single('contact_ticket_message','SELECT MAX(CTMSGID) FROM contact_ticket_message WHERE TICKETID='.$ticketid);

	[$msgcnt, $omsgcnt, $ctmsgid] = $newticket;

	if (!db_update('contact_ticket_message','
		UPDATE contact_ticket_message SET
			TICKETID = '.$oldid.'
		WHERE TICKETID = '.$ticketid)
	||	!db_update('contact_ticket_mail','
		UPDATE contact_ticket_mail SET
			TICKETID = '.$oldid.'
		WHERE TICKETID = '.$ticketid)
	||	!db_insert('contact_ticket_log', '
		INSERT INTO contact_ticket_log
		SELECT * FROM contact_ticket
		WHERE TICKETID = '. $oldid)
	||	!db_update('contact_ticket','
		UPDATE contact_ticket SET
			MUSERID	= '.CURRENTUSERID.',
			MSTAMP	= '.CURRENTSTAMP.',
			LSTAMP	= '.CURRENTSTAMP.',
			MSGCNT	= MSGCNT+'.$msgcnt.',
			OMSGCNT	= OMSGCNT+'.$omsgcnt.',
			STATUS	= "open"
		WHERE TICKETID='.$oldid)
	||	!db_delete('contact_ticket','
		DELETE FROM contact_ticket
		WHERE TICKETID='.$ticketid)
	) {
		return;
	}
	db_insert('ticketaction','
	INSERT INTO ticketaction SET
		TICKETID	='.$ticketid.',
		NEWTICKETID	='.$oldid.',
		ACTION		="insert",
		CTMSGID		='.($ctmsgid ?: 0).',
		STAMP		='.CURRENTSTAMP.',
		USERID		='.CURRENTUSERID
	);
	register_notice('contact_ticket:notice:inserted_LINE',DO_UBB, ['TICKETID' => $ticketid]);
}

function contact_erase_ticket(): bool {
	if (!require_super_admin()
	||	!require_post()
	||	!($ticketid = require_idnumber($_REQUEST,'sID'))
	||	!remove_contact_tickets([$ticketid => $ticketid])
	) {
		return false;
	}
	register_notice('contact_ticket:notice:removed_LINE');
	contact_clear_cache();
	return true;
}

function contact_menu() {
	$have_user  = have_user();
	$have_admin = $have_user && have_admin();

	layout_open_menu();
	if ($have_admin) {
		layout_menuitem(Eelement_name('overview'), '/contact/', !$_REQUEST['ACTION']);
		if (have_contact_archive()) {
			layout_menuitem(Eelement_name('archive'),'/contact/archive', $_REQUEST['ACTION'] === 'archive' && !isset($_REQUEST['YEAR']));
		}
		layout_menuitem(__C('status:pending'),'/contact/pending', $_REQUEST['ACTION'] === 'pending');
		layout_menuitem(Eelement_plural_name('signature'), '/contact/signatures', $_REQUEST['ACTION'] === 'signatures');
		layout_menuitem(Eelement_plural_name('interest_group'), '/contact/groups', $_REQUEST['ACTION'] === 'groups');

		if (have_super_admin()) {
			layout_menuitem(__C('status:undeliverable'),'/ticket/undeliverable', $_REQUEST['ACTION'] === 'undeliverable');
		}
		if (have_admin('contact_ticket_old')) {
			layout_menuitem(__C('status:old'),'/ticket/old', $_REQUEST['ACTION'] === 'old');
		}
	} else {
		layout_menuitem(Eelement_name('advertising_information'), '/advertising');
	}
	if ($have_admin
	&&	memcached_single('contact_ticket', '
		SELECT SQL_NO_CACHE 1
		FROM contact_ticket
		WHERE WITHUSERID = '.CURRENTUSERID."
		  AND STATUS IN ('open', 'pending')
		LIMIT 1",
		TEN_MINUTES
	)) {
		layout_menuitem(Eelement_plural_name('open_ticket'), '/ticket/myfuture', $_REQUEST['ACTION'] === 'myfuture');
	}
	if ($have_user
	&&	memcached_single('contact_ticket', '
		SELECT SQL_NO_CACHE 1
		FROM contact_ticket
		WHERE WITHUSERID = '.CURRENTUSERID."
		  AND STATUS NOT IN ('open', 'pending')
		LIMIT 1",
		TEN_MINUTES
	)) {
		layout_menuitem(Eelement_name('ticket_history'), '/ticket/myhistory', $_REQUEST['ACTION'] === 'myhistory');
	}
	if ($have_admin) {
		layout_menuitem(__C('action:search'), '/ticket/search', $_REQUEST['ACTION'] === 'search');
		if (have_admin('stdanswers')) {
			layout_menuitem(Eelement_plural_name('standard_answer'), '/ticket/stdanswers', $_REQUEST['ACTION'] === 'stdanswers');
		}
	}
	layout_close_menu();

	layout_open_menu();
	if (have_outgoing_allowance()) {
		layout_menuitem(Eelement_name('outgoing_form'), '/ticket/outgoing-form', $_REQUEST['ACTION'] === 'outgoing-form');
	}
	layout_menuitem(Eelement_plural_name('contact_option'), '/contact/options', $_REQUEST['ACTION'] === 'options');
	layout_close_menu();
}

function contact_mark_read(): void {
	if (!require_user()) {
		return;
	}
	if ($_REQUEST['sID']) {
		$ticketids = array($_REQUEST['sID']);
	} elseif (!($ticketids = have_number_list($_REQUEST,'TICKETIDS'))) {
		return;
	}

	$where_part = ' WITHUSERID = '.CURRENTUSERID.'
			AND TICKETID IN ('.implode(',',$ticketids).')';

	if (!db_update('contact_ticket_log','
		INSERT INTO contact_ticket_log
		SELECT * FROM contact_ticket_log
		WHERE '.$where_part)
	||	!db_update('contact_ticket','
		UPDATE contact_ticket SET READM = 1
		WHERE '.$where_part)
	) {
		return;
	}
	if ($cnt = db_affected()) {
		register_notice('contact_ticket:notice:marked_read_LINE', ['CNT' => $cnt]);
	}
}

function contact_set_tracking(bool $trackit): bool {
	$outbox = $_REQUEST['SUBACTION'] === 'outbox';
	$new_ownerid = $outbox ? 0 : 'OWNERID';

	if (!($ticketid = require_idnumber($_REQUEST,'sID'))
	||	!require_admin()
	||	!require_ticket_lock($ticketid)
	) {
		return false;
	}

	$where_part = '	  TICKETID='.$ticketid.'
		 	AND (	TRACKIT!='.($trackit ? 1 : 0).'
				 OR	OWNERID!='.$new_ownerid.')';

	if (!db_insert('contact_ticket_log','
		INSERT INTO contact_ticket_log
		SELECT * FROM contact_ticket
		WHERE '.$where_part)
	) {
		return false;
	}
	if (db_affected()) {
		if (!db_update('contact_ticket','
			UPDATE contact_ticket SET
				MSTAMP	='.CURRENTSTAMP.',
				MUSERID	='.CURRENTUSERID.',
				TRACKIT	='.($trackit ? 1 : 0).',
				OWNERID	='.$new_ownerid.'
			WHERE '. $where_part)
		) {
			return false;
		}
	}
	register_notice($trackit ? 'contact_ticket:notice:tracked_LINE' : 'contact_ticket:notice:not_tracked_LINE', DO_UBB, ['TICKETID' => $ticketid]);
	if ($outbox) {
		register_notice('contact_ticket:notice:moved_out_of_inbox_LINE', DO_UBB, ['TICKETID' => $ticketid]);
	}
	contact_clear_cache();
	return true;
}

function contact_move() {
	require_once '_contactconcerning.inc';
	if (!($ticketid = require_idnumber($_REQUEST,'sID'))
	||	!require_something($_POST, 'ELEMENT')
	||	false === optional_idnumber($_POST, 'ID')
	||	!require_ticket_lock($ticketid)
	||	false === ($ticket = db_single_assoc('contact_ticket', "
		SELECT ELEMENT, ID, USERID, OWNERID
		FROM contact_ticket
		WHERE TICKETID = $ticketid"
	))) {
		return;
	}
	if (!$ticket) {
		register_error('contact_ticket:error:nonexistent_LINE', ['ID' => $ticketid]);
		return;
	}
	db_insert('ticketmove','
	INSERT INTO ticketmove SET
		STAMP		='.CURRENTSTAMP.',
		SRCELEMENT	="'.$ticket['ELEMENT'].'",
		DSTELEMENT	="'.addslashes($_POST['ELEMENT']).'",
		SRCID		='.$ticket['ID'].',
		DSTID		='.have_idnumber($_POST, 'ID')
	);
	if (!have_idnumber($_POST, 'ID')) {
		$_POST['ID'] = 0;
	}
	// auto close some elements
	// move to owner for pr, contest
	$extra = null;
	if (str_starts_with($_POST['ELEMENT'],'junk')) {
		switch ($_POST['ELEMENT']) {
		case 'junk_spam':
			$firstmsgcrc = db_single_assoc('contact_ticket_message', "
				SELECT CRC32(BODY) AS CRC, LENGTH(BODY) AS LEN
				FROM contact_ticket_message
				WHERE TICKETID = $ticketid.
				ORDER BY CTMSGID
				LIMIT 1"
			);
			if ($firstmsgcrc) {
				db_insupd('spam_message_crc', "
					INSERT INTO spam_message_crc SET
						CRC		= {$firstmsgcrc['CRC']},
						LEN		= {$firstmsgcrc['LEN']},
						CSTAMP	= ".CURRENTSTAMP.',
						CUSERID	= '.CURRENTUSERID.'
					ON DUPLICATE KEY UPDATE HITS = HITS + 1'
				);
			}
		default:
		case 'junk_nolearn':
			if ($ticket['OWNERID'] === 2269
			&&	CURRENTUSERID === 2269
			) {
				$extra = ', STATUS = "closed", OWNERID = 0';
				break;
			}

		case 'junk_failure':
		case 'junk_recognize':
			unset($_POST['KEEPLOCK']);
			$extra = ', OWNERID = 2269';
			break;
		}
	}

	if (str_starts_with( $_POST['ELEMENT'], 'junk_')
	||	str_starts_with($ticket['ELEMENT'], 'junk_')
	) {
		$set_type = match($_POST['ELEMENT']) {
			'junk_recognize',
			'junk_spam'			=> 'spam',
			default				=> '',
		};
		$set_status = match($_POST['ELEMENT']) {
			'junk_failure',
			'junk_mailinglist',
			'junk_nolearn'		=> 'not-learned',
			default 			=> 'new',
		};
		if (db_update(['completemsg', 'contact_ticket_mail'], "
			UPDATE completemsg
			JOIN contact_ticket_mail USING (CTMSGID)
			SET	completemsg.TYPE   = '$set_type',
				completemsg.STATUS = '$set_status'
			WHERE STATUS IN ('new', 'not-learned')
			  AND CIMID = 0
			  AND TICKETID= $ticketid")
		) {
			if ($set_type === 'spam') {
				register_notice('contact_ticket:notice:will_learn_LINE');
			}
		}
	}
	if (!isset($_POST['KEEPLOCK'])
	&&	$ticket['ELEMENT'] === $_POST['ELEMENT']
	) {
		$_POST['KEEPLOCK'] = true;
	}
	if (!db_insert('contact_ticket_log','
		INSERT INTO contact_ticket_log
		SELECT * FROM contact_ticket
		WHERE TICKETID = '.$ticketid)
	||	!db_update('contact_ticket','
		UPDATE contact_ticket SET
			MUSERID		= '.CURRENTUSERID.',
			MSTAMP		= '.CURRENTSTAMP.',
			CONCERNING	= '.get_concerning($_POST['ELEMENT'],$_POST['ID']).",
			ELEMENT		= '".addslashes($_POST['ELEMENT'])."',
			ID			= {$_POST['ID']}
			$extra
		WHERE TICKETID = $ticketid")
	) {
		return;
	}
	if (!isset($_POST['KEEPLOCK'])) {
		release_lock(LOCK_TICKET, $ticketid);
	}
	register_notice('contact_ticket:notice:moved_LINE',DO_UBB,array('TICKETID'=>$ticketid,'ELEMENT'=>$_POST['ELEMENT'],'ID'=>$_POST['ID']));
	contact_clear_cache();
}
function contact_section_overview(): void {
	contact_menu();
	$sections = [
		'artist'		=> true,
		'ad'			=> true,
		'camerarequest'	=> true,
		'contest'		=> true,
		'helpdesk'		=> true,
		'news'			=> true,
		'party'			=> true,
	];
	foreach ($sections as $element => &$name) {
		$name = $name ? __C("contactsection:$element") : Eelement_name('general');
	}
	unset($name);
	asort($sections);
	$sections = [...['info' => Eelement_name('general')], ...$sections];

	$have_user = have_user();
	layout_open_box('contact');
	layout_open_table('fw default hha');
	foreach ($sections as $element => $name) {
		?><tr class="hh>"><?
		?><td><a href="/ticket/form?ELEMENT=<?= $element, ($have_user ? '' : ';EMAIL') ?>"><?
			?><img class="mail rmrgn" src="<?= STATIC_HOST ?>/images/mail<?= is_high_res() ?>.png" /> <?= $name ?></a><?
		?></td><?

		?><td class="right"><?
		if ($email = contact_get_email($element)) {
			?><a href="mailto:<?= $email ?>"><?= $email ?></a><?
		}
		?></td><?
		?></tr><?
	}
	layout_close_table();
	layout_close_box();
}
function contact_display_old(): void {
	if (!require_admin('contact_ticket_old')) {
		return;
	}
	layout_show_section_header();

	contact_menu();

	require_once '_contactlist.inc';
	$contactlist = new _contactlist();
	$contactlist->all_old();
	$contactlist->query();
	$contactlist->display();
}
function contact_display_overview() {
	layout_show_section_header();

	if (isset($_REQUEST['NOVERVIEW'])) {
		contact_menu();
		return;
	}

	if (!have_admin()) {
		contact_section_overview();
		$contactlist = new _contactlist();
		$contactlist->with_user(CURRENTUSERID);
		$contactlist->hide_header = true;
		$contactlist->not_closed_and_read();
		$contactlist->useful_for_nonadmin();
		$contactlist->with_status('open');
		$contactlist->query();
		if ($contactlist->have_tickets()) {
			$contactlist->header = __C('contactlist:ticketstatus:open');
			$contactlist->display();
		}
		return;
	}

	contact_menu();
	$contactlist = new _contactlist();
	$contactlist->show_groups = true;
	$contactlist->admin_list = true;
	$contactlist->check_states = true;
	$contactlist->for_me();
	$contactlist->query();
	$contactlist->display();
}
function contact_display_pending() {
	if (!require_admin()) {
		return;
	}
	layout_open_section_header();
	echo Eelement_plural_name('pending_ticket');
	layout_close_section_header();

	contact_menu();

	$contactlist = new _contactlist();
	$contactlist->show_groups = true;
	$contactlist->emphasize_old = false;
	$contactlist->for_me();
	$contactlist->only_pending();
	$contactlist->query();
	$contactlist->display();
}

function contact_display_mine() {
	if (!have_user()) {
		return;
	}
	layout_show_section_header();

	if (isset($_REQUEST['NOVERVIEW'])) {
		return;
	}
	contact_menu();

	foreach (['pending', 'closed', 'open_progress', 'open'] as $status) {
		$contactlist = new _contactlist();
		$contactlist->with_user(CURRENTUSERID);
		$contactlist->hide_header = true;
		$contactlist->only_me = true;
		$contactlist->not_closed_and_read();
		$contactlist->useful_for_nonadmin();
		$contactlist->with_status($status);
		$contactlist->query();
		if (!($cnt = $contactlist->get_ticket_total())) {
			continue;
		}
		$contactlist->header = __C('contactlist:ticketstatus:'.$status).' '.MIDDLE_DOT_ENTITY.' '.$cnt;
		$contactlist->collapse = ($status === 'open');

		$contactlist->display();
		if (in_array($status, ['closed', 'open_progress'])
		&&	($ticketids = $contactlist->get_ticketids())
		) {
			?><div class="funcs"><?
			with_confirm(
				__('action:mark_all_read'),
				'/contact/markread?TICKETIDS='.implode(',', $ticketids),
				__('contact:confirm:mark_all_read_LINE')
			);
			?></div><?
		}
	}
}
function contact_display_search($userid = false,$future = false) {
	if (!require_user()) {
		return;
	}
	if (!empty($_REQUEST['SRCH'])) {
		$_REQUEST['SRCH'] = strip_shy($_REQUEST['SRCH']);
	}
#	$userid = 163264;
	if (!$userid) {
		if (!require_admin()) {
			return;
		}
		layout_show_section_header(Eelement_plural_name('contact_ticket'));
#		if (empty($_REQUEST['SRCH'])
#		||	!($type = require_element($_REQUEST,'TYPE',array('withuser','asadmin','body')))
#		) {
			?><form method="get" onsubmit="return submitForm(this)" action="/contact/search"><?
			layout_open_box('white');
			layout_box_header(__C('action:search'));
			layout_open_table('fw');

				layout_start_row();
				echo Eelement_name('type');
				layout_field_value();

				$type = getifset($_REQUEST,'TYPE');
				$withuser = $type === 'withuser' ? ' checked' : '';
				$asadmin  = $type === 'asadmin'  ? ' checked' : '';
				$body =	    $type === 'body'	 ? ' checked' : '';

				$placeholder = __('generic:placholder:USERID_email_or_name');

				?><label class="<? if (!$withuser) echo 'not-' ?>hilited"><?
				?><input<?= $withuser ?> class="upLite" onclick="/*getobj('relev').disabled=true;*/this.form.SRCH.placeholder='<?= $placeholder ?>'" required type="radio" name="TYPE" value="withuser" /> <?= element_plural_name('user') ?>, <?= element_plural_name('relation') ?></label><br /><?
				?><label class="<? if (!$asadmin) echo 'not-' ?>hilited"><?
				?><input<?= $asadmin ?> class="upLite" onclick="/*getobj('relev').disabled=true;*/this.form.SRCH.placeholder='<?= $placeholder ?>'" required type="radio" name="TYPE" value="asadmin" /> <?= element_name('employee') ?> (<?= element_plural_name('newest_ticket', 1000) ?>)</label><br /><?
				?><label class="<? if (!$body) echo 'not-' ?>hilited"><?
				?><input<?= $body ?> class="upLite" onclick="/*getobj('relev').disabled=false;*/this.form.SRCH.placeholder=''" required="required" type="radio" name="TYPE" value="body" /> <?= element_name('real_message') ?> (<?= element_plural_name('newest_matches', 100) ?></label><?

			layout_restart_row();
				echo Eelement_name('search_term');
				layout_field_value();
				?> <input<?
				?> type="search"<?
				?> required<?
				?> autofocus<?
				?> name="SRCH"<?
				if ($search_terms = $_REQUEST['SRCH'] ?? null) {
					?> value="<?= escape_specials($search_terms) ?>"<?
				}
				if (!$body) {
					?> placeholder="<?= $placeholder ?>"<?
				}
				?> /><?
/*			layout_restart_row();
				echo Eelement_name('order');
				layout_field_value();

				$order = getifset($_REQUEST,'ORDER');
				$o_group = $order == 'group' ? ' checked="checked"' : null;
				$o_date =  $order == 'date' ? ' checked="checked"' : null;
				$o_relevance = $order == 'relevance' ? ' checked="checked"' : null;
				if (!$o_group && !$o_date && !$o_relevance) {
					$o_group = ' checked="checked"';
				}
				?><label class="<? if (!$o_group) echo 'not-' ?>hilited"><?
				?><input<?= $o_group ?> type="radio" class="upLite" name="ORDER" value="group" /> <?= element_plural_name('group') ?></label><br /><?

				?><label class="<? if (!$o_date) echo 'not-' ?>hilited"><?
				?><input<?= $o_date ?> type="radio" class="upLite" name="ORDER" value="date" /> <?= element_name('date') ?></label><br /><?

				?><label class="<? if (!$o_relevance) echo 'not-' ?>hilited"><?
				?><input<? echo $o_relevance;
				if (!$body) {
					?> disabled="disabled"<?
				}
				?> type="radio" id="relev" class="upLite" name="ORDER" value="relevance"> <?= element_name('relevance') ?></label><?*/

			layout_restart_row();
				?><label for="junk"><?= Eelement_name('junk') ?></label><?
				layout_field_value();
				show_input([
					'name'		=> 'JUNK',
					'type'		=> 'checkbox',
					'value'		=> 1,
					'checked'	=> isset($_REQUEST['JUNK'])
				]);

			layout_stop_row();
			layout_close_table();
			layout_close_box();
			?><div class="block"><input type="submit" value="<?= __('action:search') ?>" /></div><?
			?></form><?
		if (empty($_REQUEST['SRCH'])
		||	(	!($type = require_element($_REQUEST, 'TYPE', ['withuser', 'asadmin', 'body']))
#			||	!($order = require_element($_REQUEST,'ORDER',['group','date','relevance']))
			)
		) {

			return;
		}
#		}
		layout_open_box('white');
		$srch = trim($_REQUEST['SRCH']);
		switch ($type) {
		case 'asadmin':
		case 'withuser':
			$typename = $asadmin ? element_name('employee') : element_name('user');
			if (is_number($srch)) {
				$userid = $srch;
				layout_box_header(get_element_link('user',$userid).' '.MIDDLE_DOT_ENTITY.' '.$typename);
			} else {
				$email = $srch;
				layout_box_header(_make_illegible($email).' '.MIDDLE_DOT_ENTITY.' '.$typename);
				if ($asadmin) {
					?>bzzzzt wrong<?
				}
			}
			unset($srch);
			break;
		case 'body':
			layout_box_header(escape_specials($srch));
			break;
		}
		layout_close_box();
	} else {
		layout_show_section_header(Eelement_name('ticket_history'));

		layout_open_box('white');
		layout_box_header(get_element_link('user',$userid).' '.MIDDLE_DOT_ENTITY.' '.element_name('ticket_history'));
		layout_close_box();
	}
/*	if (!$all) {
		layout_open_menu();
		layout_menuitem(__C('action:all'),($uri = $_SERVER['REQUEST_URI']).(
			!str_contains($uri,'?')
		?	'?ALL'
		:	';ALL'
		));
		layout_close_menu();
	}*/
	$contactlist = new _contactlist;
	if ($_REQUEST['ACTION'] === 'myfuture') {
		$contactlist->hide_header = true;
	}
	if (isset($_REQUEST['JUNK'])) {
		$contactlist->junk = true;
	}
	$contactlist->name_inbox = true;
	if (!have_admin()) {
		$contactlist->hide_header = true;
	}
	if (isset($srch)) {
		$contactlist->not_self = true;
	}
	if ($userid) {
		if (!empty($asadmin)) {
			$contactlist->as_admin($userid);
		} else {
			$contactlist->with_user($userid);
			$email = db_single('user','SELECT EMAIL FROM user WHERE USERID='.$userid);
			if ($email) {
				$contactlist->with_email($email, true);
			}
		}
	} else {
		if (isset($srch)) {
			$contactlist->with_body($srch);
		} else {
			$contactlist->with_email($email);
		}
	}
	if ($future) {
		$contactlist->open_and_pending();
#	} else
#		$contactlist->closed_or_read();
	}
	# FIXME: ^^^^ all is always most useful
	$contactlist->reversed = true;
	$contactlist->query();
	$contactlist->display();
}
function contact_display_archive() {
	if (!require_admin()) {
		return;
	}

	global $__year, $__month;
	if (!isset($_REQUEST['ALLINBOX'])
	&&	(	!have_number($_REQUEST,'YEAR')
		||	!have_number($_REQUEST,'MONTH')
		)
	) {
		layout_show_section_header(Eelement_name('contact_ticket_archive'));

		contact_menu();

		if (!have_contact_archive()) {
			?><p>Geen archief.</p><?
			return;
		}
		include_js('js/contactarchive');
		layout_open_box('contact');
		?><p>Sorteer op <select id="sorttype"><?
		?><option value="0"><?= element_name('date_last_contact') ?></option><?
		?><option value="1"><?= element_name('opening_date') ?></option><?
		?></select></p><?
		layout_open_table();

		$tmplist = _contact_construct_element_list();

		if (!$tmplist) {
			_error('Bel de brandweer, snel!');
			return;
		}
		if (have_admin()) {
			$tmplist += ['junk_spam', 'junk_nolearn', 'junk_recognize', 'junk_failure', 'junk_mailinglist'];
		}
		foreach ($tmplist as $element) {
			$elemlist[$element] = element_plural_name($element);
		}
		asort($elemlist);

		for ($year_count = $__year; $year_count >= 2004; --$year_count) {
			if (!have_contact_archive($year_count)) {
				continue;
			}
			for ($month_count = ($year_count === $__year ? $__month : 12); $month_count >= ($year_count === 2004 ? 9 : 1); --$month_count) {
				if ($year_count === $__year && $month_count > $__month) {
					break;
				}
				if (!have_contact_archive($year_count, $month_count)) {
					continue;
				}
				layout_start_row();
				echo $year_count,' ',_month_name($month_count);
				layout_field_value();
				?><a href="/contact/archive?YEAR=<?= $year_count ?>;MONTH=<?= $month_count ?>"<?
				?> onclick="selectArchive(this.href); return false;"><?= Eelement_name('inbox') ?></a> <?= MIDDLE_DOT_ENTITY ?> <?
				?><select onchange="selectArchive(this.value)"><option value="x"></option><?
				foreach ($elemlist as $element => $name) {
					?><option value="/contact/archive?ALL<?
						?>;YEAR=<?= $year_count
						?>;MONTH=<?= $month_count
						?>;ELEMENT=<?= $element
						?>"><?= $name ?></option><?
				}
				?></select><?
				layout_stop_row();
			}
		}

		layout_close_table();
		layout_close_box();

		return;
	}
	if (!($month = require_month($_REQUEST, 'MONTH'))
	||	!($year  = require_year ($_REQUEST, 'YEAR', 2004, $__year))
	) {
		return;
	}
	layout_show_section_header(
		__('contact:header:ticket_overview_of_month', [
			'MONTH_NAME'	=> _month_name($month),
			'YEAR'			=> $year
	]));
	contact_menu();
	$contactlist = new _contactlist();
	$contactlist->show_groups = true;
	$contactlist->allow_junk = true;
	if (isset($_REQUEST['ALL'])) {
		$contactlist->for_me();
	} else {
		$contactlist->only_mine();
	}
	if (isset($_REQUEST['ELEMENT'])) {
		if (!require_contact_element($_REQUEST, 'ELEMENT')) {
			return;
		}
		$contactlist->elements($_REQUEST['ELEMENT']);
	}
	if (!isset($_REQUEST['ALLINBOX'])) {
		$contactlist->in_year_and_month(empty($_REQUEST['SORT']) ? 'LSTAMP' : 'CSTAMP', $year, $month);
	}
	if (have_idnumber($_REQUEST,'FORUMID')) {
		$contactlist->in_forum($_REQUEST['FORUMID']);
	}
	$contactlist->query();
	$contactlist->display();
}
function contact_holiday_info(int $userid): ?array {
	if (false === ($user = db_single_assoc('holiday','
		SELECT USERID, SUBSTITUTID
		FROM holiday
		WHERE STATUS = "active"
		  AND USERID = '.$userid))
	) {
		return null;
	}
	return $user;
}
function contact_display_form() {
	layout_open_section_header();
	if (isset($_REQUEST['TO_USERID'])) {
		mail_log('TO_USERID set at '.$_SERVER['REQUEST_URI']);
		echo __C('contact:header:contact_LINE');
	} else {
		echo __C('contact:header:contact_an_administrator_LINE');
	}
	layout_close_section_header();

	if (!have_idnumber($_REQUEST,'ID')) {
		$_REQUEST['ID'] = 0;
	}
	layout_open_box('white');

	?><div class="block"><span class="warning">Let op!</span><br />Met dit formulier kun je een bericht naar een <b>medewerker van Partyflock</b> sturen en niet naar een willekeurig lid.</div><?
	if (empty($_REQUEST['ID'])) {
		$advice_connected = true;

/*	NOTE: obsolete?

	} elseif (isset($_REQUEST['ELEMENT'])) {
		switch ($_REQUEST['ELEMENT']) {
		case 'valentine':
			?><p>We kunnen nooit bekendmaken wie een anoniem valentijnsbericht heeft verstuurd!<br /><?
			?>In het geval van een kwetsende tekst kunnen we <i>wel</i> ingrijpen.</p><?
			break;
		case 'directmessage':
			$msg = db_single_assoc(
				'directmessage','
				SELECT FROM_USERID,TO_USERID
				FROM directmessage
				WHERE MESSAGEID='.$_REQUEST['ID']
			);
			?><p>In principe zullen we niet snel overgaan tot het bestraffen van priv&eacute;berichten. Je kunt <?
			?>in het geval van ongewenste berichten deze persoon het beste negeren via <?
			if ($msg
			&&	$msg['TO_USERID'] === CURRENTUSERID
			) {
				?><span class="underline"><i><a href="/user/<?= $msg['FROM_USERID'];
				?>/ignoreform">het speciale negeerformulier</a></i></span><?
			} else {
				?>het speciale negeerformulier<?
			}
			?>.</p><?
		}*/
	}
	if (!have_admin()) {
		$not_sure = true;
	}
	if (isset($advice_connected)) {
		?><div class="block"><?= __('contact:info:notify_info_TEXT' ,DO_UBB | DO_NL2BR) ?></div><?
	}
	layout_close_box();

	$usemail = !have_user() || isset($_REQUEST['EMAIL']);

	if (have_contact_element($_REQUEST,'ELEMENT')) {
		if ($_REQUEST['ELEMENT'] === 'chat') {
			$_REQUEST['ID'] = db_single('chat_message','
				SELECT ID
				FROM chat_message
				'.(have_idnumber($_REQUEST,'ID') ? 'WHERE CHANNELID='.$_REQUEST['ID'] : null).'
				ORDER BY ID DESC LIMIT 1'
			);
		}
		if ($_REQUEST['ID']) {
			$contactlist = new _contactlist;
			$contactlist->element_and_id($_REQUEST['ELEMENT'],$_REQUEST['ID']);
			$contactlist->open_and_pending();
			if (!have_admin()) {
				$contactlist->only_link_with_currentuser();
				$contactlist->show_withuser = false;
			}
			$contactlist->query();
			if ($cnt = $contactlist->get_ticket_total()) {
				layout_open_box('white');
				?><div class="block"><span class="warning">Let op!</span><br />Er <?
				if ($cnt > 1) {
					?>zijn al tickets<?
				} else {
					?>is al een ticket<?
				}
				?> met betrekking tot hetzelfde element geopend of in behandeling!</div><?
				layout_close_box();
				$contactlist->display();
			}
		}

		if (isset($not_sure)) {
			?><div id="notsure"><?
			?><div class="block"><?= __('contact:info:are_you_sure_you_want_contact_LINE') ?></div><?
			?><div><?
			?><input type="button" onclick="<?
				ob_start();
				?>(function(){<?
					?>hide('notsure');<?
					?>unhide('ticketform');<?
					?>let rf = getobj('ticketform');<?
					?>if (rf) {<?
						?>Pf.focus(rf.<?= $item = $usemail ? 'SUBJECT' : 'MSGBODY' ?>);<?
						?>Pf_fitTextarea(rf.MSGBODY);<?
					?>}<?
				?>})();<?=
				minify_js(ob_get_clean());
			?>" value="<?= __C('action:contact_admin') ?>"><?
			?></div><?
			?></div><?
		}
	} else {
		$_REQUEST['ELEMENT'] = 'helpdesk';
		$_REQUEST['ID'] = '0';
	}
	if (false === ($lastticketid = db_single('contact_ticket','SELECT MAX(TICKETID) FROM party_db.contact_ticket'))) {
		return;
	}
	include_js('js/form/contactuser');
	?><form<?
	?> id="ticketform" <?
	if (isset($not_sure)) {
		?> class="hidden"<?
	}
	?> enctype="multipart/form-data"<?
	?> method="post"<?
	?> onsubmit="return submitForm(this)"<?
	?> action="/ticket/commit"><?

	?><input type="text" name="NEWSUBJECT" class="hidden" /><?
	?><input type="hidden" name="LAST_TICKETID" value="<?= $lastticketid ?>" /><?
	?><input type="hidden" name="FORMSTAMP" value="<?= CURRENTSTAMP ?>" /><?
	if (isset($newcontest)) {
		?><input type="hidden" name="NEWCONTEST" value="1" /><?
	}
	layout_open_box('contact');
	if (!NO_ALTER_ATTACHMENT) {
		require_once '_attachments.inc';
		?><table class="dyntab dens fw nomargin"><tr><td><?
	}
	layout_open_table('fw');
	layout_start_row();
		echo Eelement_name('section');
		layout_field_value('fw');
		if ($_REQUEST['ELEMENT'] === 'user') {
			?><select name="ELEMENT"><?
			?><option value="helpdesk"><?= Eelement_name('helpdesk'); ?></option><?
			?><option value="user"><?= Eelement_name('user'); ?></option><?
			?></select><?
		} elseif ($_REQUEST['ELEMENT'] === 'development') {
			?><select name="ELEMENT"><?
			?><option value="development"><?= Eelement_name('development'); ?></option><?
			?><option value="bug"><?= Eelement_name('bug'); ?></option><?
			?></select><?
		} else {
			?><input type="hidden" name="ELEMENT" value="<?= escape_specials($_REQUEST['ELEMENT']); ?>" /><?
			?><b><?= $name = Eelement_name(isset($newcontest) ? 'contest' : $_REQUEST['ELEMENT']); ?></b><?
		}
	if ($_REQUEST['ID']) {
		[$link, $content] = _element_display_link($_REQUEST['ELEMENT'], $_REQUEST['ID'], include_link: true);
		layout_restart_row();
		if ($link) {
			?><a target="_blank" href="<?= $link ?>"><?= Eelement_name('element'); ?></a><?
		} else {
			echo Eelement_name('element');
		}
		layout_field_value();
		?><input value="<?= $_REQUEST['ID']; ?>" type="hidden" name="ID" /><?
		echo $content;
	}
	if ($_REQUEST['ELEMENT'] === 'directmessage'
	&&	$_REQUEST['ID']
	) {
		layout_restart_spanned_row();
		?><div class="hr"></div><?
		layout_restart_row();
		echo Eelement_name('comment');
		layout_field_value();
		?>Door bovenstaand bericht te melden aan een medewerker, kan deze beslissen een waarschuwing of
		bestraffing uit te delen aan de verzender.<br />De medewerker zal in dat geval het bericht als bewijsmateriaal
		aanvoeren daar hij niet zomaar mensen kan bestraffen zonder bewijslast.<br />Houd daar rekening mee!<?
	}
	if (!isset($usemail)) {
		mail_log('usemail is not set?', item: get_defined_vars());
	}
	if ($usemail) {
		layout_restart_row();
		echo Eelement_name('subject');
		layout_field_value();
		?><input<?
		if (!isset($not_sure)) {
			?> autofocus="autofocus"<?
		}
		?> type="text" id="subject" name="SUBJECT" maxlength="255" required="required"><?

		layout_restart_row(NOWRAP);
		echo Eelement_name('your_name');
		layout_field_value();
		?><input type="text" id="name" name="NAME" required="required" /><?

		layout_restart_row(NOWRAP);
		echo Eelement_name('your_email_adres');
		layout_field_value();
		?><input type="email" name="EMAIL" required="required" data-valid="working-email" /><?
	} else {
		?><input type="hidden" name="WASUSERID" value="<?= CURRENTUSERID; ?>" /><?
		?><input type="hidden" name="_COOKIEINFO" value="<?= escape_specials(get_r($_COOKIE)); ?>" /><?
	}
	layout_restart_row();
		echo Eelement_name('comment');
		layout_field_value();

		if (!empty($_POST['MSGBODY'])) {
			$body = mytrim(escape_specials($_POST['MSGBODY']));
		} elseif (isset($newcontest)) {
			ob_start();
			?>Geef hier duidelijk wat er te winnen valt (e-tickets of gastenlijst) en hoeveel.<?= "\n\n";
			?>(bijvoorbeeld 5x 2 gastenlijstplaatsen)<?
			$body = ob_get_clean();
		} else {
			$body = null;
		}

		show_textarea([
			'onclick'	=> isset($newcontest) ? "this.value='';this.onclick=null" : null,
			'name'		=> 'MSGBODY',
			'cols'		=> 70,
			'rows'		=> 2,
			'id'		=> 'msgbody',
			'class'		=> 'growToFit',
			'required'	=> true,
			'autofocus'	=> !$usemail && !isset($not_sure),
			'value'		=> $body,
		]);
	if (!$usemail) {
		layout_restart_row(NOWRAP);
		echo __C('field:via_email_too');
		layout_field_value();
		via_email_too(new_ticket: true);
	}
	layout_stop_row();
	if (!NO_ALTER_ATTACHMENT) {
		show_attachment_options(false);
	}
	layout_close_table();

	if (!NO_ALTER_ATTACHMENT) {
		?></td><td class="right bpad vbottom nowrap"><?
		?><label><?= Eelement_name('attachment') ?> <input onclick="showAttachments(this)" type="checkbox" name="ATTACHMENT_C" value="1" /></label><?
		?></td></tr></table><?
	}
	layout_close_box();

	?><input type="hidden" name="NEW" value="1" /><?

	if (!have_user()) {
		require_once '_recaptcha.inc';
		show_recaptcha_button('new_ticket',__('action:send'),'submitContactForm');
	} else {
		?><input type="submit" value="<?= __('action:send') ?>" /><?
	}

	?></form><?
}

function contact_display_outgoing_form(): void {
	require_once '_fillelementid.inc';
	require_once '_salutes.inc';
	require_once 'defines/contactstandardanswer.inc';

	if (false === ($last_ticketid = db_single('contact_ticket','SELECT MAX(TICKETID) FROM party_db.contact_ticket'))) {
		return;
	}
	layout_show_section_header();

	if ($element = have_contact_element($_REQUEST,'ELEMENT')) {
		$id = have_idnumber($_REQUEST, 'ID');
	} else {
		$element = null;
		$id = 0;
	}
	if (false === ($template = template_obtain())) {
		return;
	}

	if ($template) {
		if (!empty($template['ELEMENT'])) {
			$element =  $template['ELEMENT'];
		}
		if (!empty($template['ID'])) {
			$id = $template['ID'];
		}
	} elseif (/* $ufa_id = */ have_idnumber($_REQUEST, 'UPDATE_FROM_APPICID')) {
#		$info = db_single('update_from_appic', '
#			SELECT
	}

	if (!require_outgoing_allowance($element, $id)) {
		return;
	}
	$spec = explain_table('contact_ticket_message');


	$to_userid = $template['TO_USERID'] ?? ($forced_userid = have_idnumber($_REQUEST, 'TO_USERID'));

	if ($user =	$to_userid > 1
		?	db_single_assoc('user','SELECT EMAIL, REALNAME, NAME, NICK FROM user WHERE USERID = '.$to_userid)
		:	null
	) {
		fix_user_personalia($user);
	} elseif ($user === false) {
		return;
	}

	if (!empty($template['TO_EMAIL'])) {
		$to_name = $template['TO_NAME' ] ?? '';
		$to_mail = $template['TO_EMAIL'];
		if ($to_name
		&&	'UTF-8' !== ($detected = mb_detect_encoding($to_name, 'windows-1252, UTF-8'))
		) {
			register_error('generic:error:encoding_LINE', ['EXPECTED' => 'UTF-8', 'GOT' => $detected]);
			mail_log('TO_NAME in template doest not seem UTF8!', item: get_defined_vars());
	#		return;
		}

	} elseif (!empty($user['EMAIL'])) {
		$to_mail = $user['EMAIL'];
		if ($user['REALNAME']) {
			$to_name = $user['REALNAME'];
		} elseif ($user['NAME']) {
			$to_name = $user['NAME'];
		} else {
			$to_name = $user['NICK'];
		}
	} elseif (!empty($_REQUEST['TO_EMAIL'])) {
		if (!require_working_email($_REQUEST, 'TO_EMAIL')) {
			return;
		}
		$to_mail = $_REQUEST['TO_EMAIL'];
		$to_name = $_REQUEST['TO_NAME' ] ?? '';
	} else {
		$to_name = null;
		$to_mail = null;
	}

	if (!$to_userid
	&&	 $to_mail
	&&	false === ($to_userid = db_single(['user', 'user_account'], '
			SELECT USERID
			FROM user
			JOIN user_account USING (USERID)
			WHERE EMAIL = "'.($slashed_to_email = addslashes($to_mail)).'"
			ORDER BY EMAIL = "'.$slashed_to_email.'" DESC,
				  STATUS = "active" DESC,
				  STATUS = "banned" DESC,
				  STATUS = "inactive" DESC'
	))) {
		return;
	}

	$do_user  = $to_userid;
	$do_email = (bool)$to_mail;

	$first_name = $to_mail ? get_email_salute($to_mail, $to_name, $template) : null;

	if (!$to_name
	&&	 $to_mail
	) {
		$to_name = $first_name;
	}

	if ($to_userid === 1) {
		mail_log('to_userid === 1', get_defined_vars());

		_error('Ticket ook geadresseerd aan USERID 1 ongeregistreerd; Overleg met Thomas!');

		$to_userid = 0;
		$forced_userid = 0;
		$do_user = false;
	}

	?><form<?
	?> enctype="multipart/form-data"<?
	?> method="post"<?
	?> onsubmit="return submitTicketForm(this)"<?
	?> action="/ticket/commit"><?

	?><input type="hidden" name="LAST_TICKETID" value="<?= $last_ticketid ?>" /><?
	?><input type="hidden" name="FORMSTAMP" value="<?= CURRENTSTAMP ?>" /><?
	if ($template) {
		template_passthrough($template);
	}

	$signature =
		($element ? get_contact_signature($element) : null)
	?:	get_contact_signature('default')
	?:	generate_contact_signature();
	assert(is_array($signature)); # Satisfy EA inspection

	foreach (get_all_contact_signatures() as $tmpelement => $tmpsignature) {
		/** @noinspection PhpRedundantOptionalArgumentInspection */
		extract($tmpsignature, \EXTR_OVERWRITE);
		?><input disabled type="hidden" name="<?= $tmpelement ?>_fromname"	value="<?= escape_utf8_to_win1252($FROMNAME) ?>" /><?
		?><input disabled type="hidden" name="<?= $tmpelement ?>_header"	value="<?= escape_utf8_to_win1252($HEADER) ?>" /><?
		?><input disabled type="hidden" name="<?= $tmpelement ?>_header_ls"	value="<?= escape_utf8_to_win1252($HEADER_LS) ?>" /><?
		?><input disabled type="hidden" name="<?= $tmpelement ?>_footer"	value="<?= escape_utf8_to_win1252($FOOTER) ?>" /><?
	}

	$prtlistsize = 0;

#	if (!($firstelement = $element)) {
		if (have_admin('oticket')) {
			global $currentuser;
			$remainder = array_intersect_key($currentuser->rights, get_contact_elements());
			if ($remainder) {
				foreach ($remainder as $elem => $yes) {
					$prtlist[$elem] = element_name($elem);
					++$prtlistsize;
				}
			}
		} else {
			foreach (always_allow_outgoing() as $elem => $needid) {
				switch ($elem) {
				case 'message':
				case 'topic':
					if (!have_forum_admin()) {
						continue 2;
					}
					break;
				default:
					if (!have_admin($elem)) {
						continue 2;
					}
				}
				$prtlist[$elem] = element_name($elem);
				++$prtlistsize;
			}
		}
		if (!$prtlistsize) {
			_error('Er zijn geen elementen waar jij uitgaande tickets voor mag openen!');
			return;
		}
#	}
	if (false === ($stdanswers = memcached_multirowuse_hash(['contact_standard_answer', 'contact_standard_answer_data'],'
		SELECT ELEMENT, DATAID, BODY, TITLE, FLAGS
		FROM contact_standard_answer
		JOIN contact_standard_answer_data USING (DATAID)
		WHERE (FLAGS & '.STDANSWER_OUTGOING.')
		  AND NOT (FLAGS &  '.STDANSWER_REMOVED.')
		  AND '.(	$prtlistsize > 1
			  ?	'ELEMENT IN ('.stringsimplode(', ', array_keys($prtlist)).')'
			  :	'ELEMENT = "'.$element.'"'
			  ).'
		ORDER BY TITLE'
	))) {
		return;
	}

	ob_start();
	layout_open_table('fw body force-link');
	layout_start_row();
	if ($id) {
		[$link, $content] = _element_display_link($element, $id);
	}
	if (!empty($link)) {
		?><a target="_blank" href="<?= $link; ?>"><?= Eelement_name('element'); ?></a><?
	} else {
		echo Eelement_name('element');
	}
	layout_field_value();

	if ($prtlistsize === 1) {
		[$elem, $descr] = keyval($prtlist);
		$element = $firstelement = $elem;
		?><input type="hidden" name="ELEMENT" value="<?= $elem; ?>" /><?
		?><b><?= ucfirst($descr); ?></b><?

	} elseif ($prtlistsize) {
		$firstelement = $element;
		asort($prtlist);
		?><select onchange="<?
		if (!$to_userid) {
			?>changeElement(this);<?
		}
		if ($stdanswers) {
			?>changeStandardAnswers(this)<?
		}
		if (isset($prtlist['party'])
		||	isset($prtlist['camerarequest'])
		) {
			?>;<? changeExpires();
		}
		?>" name="ELEMENT" id="element" required><?
		?><option></option><?
		foreach ($prtlist as $elem => $descr) {
			?><option<?
			if ($elem === $element) {
				?> selected="selected"<?
			}
			?> value="<?= $elem ?>"><?= ucfirst($descr); ?></option><?
		}
		?></select><?

		# require_once '_fillelementid.inc';
		# show_elementid_input($element, $id, ['min' => 0, 'name' => 'ID']);
	} else {
		?><input type="hidden" name="ELEMENT" value="<?= $firstelement ?>" /><?
		?><b><?= Eelement_name($firstelement); ?></b><?
	}

	if ($id) {
		layout_restart_row();
		layout_next_cell();

		?><input value="<?= $id ?>" type="hidden" name="ID" /><?
		?><div><?= $content ?></div><?
	}

	include_js('js/form/outgoingcontact');

	// outgoing to either userid or email

	layout_restart_row();
		?><label class="<?
		if (!$do_user) {
			?>not-<?
		}
		?>bold-hilited"><?= Eelement_name('user') ?> <?
		show_input([
			'checked'	=> $do_user,
			'onclick'	=> /** @lang JavaScript */ 'Pf_targetUser(this);',
			'name'		=> 'DESTINATION[user]',
			'value'		=> '1',
			'type'		=> 'checkbox',
			'class'		=> 'upLite',
		]);
		?></label><?
		layout_next_cell();
		?><div id="destination_userid"<?
		if (!$do_user) {
			?> class="hidden"<?
		}
		?>><?

		show_elementid_input(
			'user',
			$to_userid, [
				'min'			=> 0,
				'name'			=> 'TO_USERID',
				'allow-bad'		=> true,
				'long-names'	=> true,
			],
				!empty($template['TO_NONCOST'])
			?	'<span class="warning">'.__('contact_ticket:warning:non_invoice_connect').'</span>'
			:	null
		);
		?></div><?

	if (!empty($template['SUBJECT'])) {
		$subject_key = 'value';
	} else {
		$subject_key = 'value_escaped';
		if ($id) {
			$subject =
				isset(USE_URLTITLE[$element])
			?	flat_with_entities(get_element_title($element, $id), isset(USE_UNICODE[$element]) ? UBB_UTF8 : 0)
			:	element_name($element).' '.$id;

			if ($element === 'party'
			&&	($party = memcached_party_and_stamp($id))
			) {
				$subject .= ', '._dateday_get($party['STAMP']);
			}
		} else {
			$subject = $element ? element_name($element) : null;
		}
	}

		$emailflags = $do_email ? 0 : ROW_HIDDEN;
		layout_restart_row($emailflags,'fromrow');
		echo Eelement_name('sender');
		layout_field_value();
		if (!isset($signature['FROMNAME'])) {
			mail_log('no FROMNAME in signature', item: get_defined_vars());
		}
		?><input type="text" maxlength="80" name="FROMNAME" value="<?= escape_utf8_to_win1252($signature['FROMNAME'] ?? '') ?>" /><?

		$badinfo = null;
		if ($to_mail
		&&	false === ($badinfo = db_single_array(['contact_undeliverable', 'realemailcheck', 'contact_ticket_mail'],'
			SELECT	GROUP_CONCAT(CIMID),
					MAX(CSTAMP),
					COUNT(*),
					GROUP_CONCAT(CSTAMP),
					GROUP_CONCAT(TICKETID),
					GROUP_CONCAT(FAILREASON)
			FROM contact_undeliverable
			JOIN realemailcheck ON EMAIL = FAILMAIL
			LEFT JOIN contact_ticket_mail USING (CIMID)
			WHERE FAILS
			  AND FAILMAIL = "'.addslashes($to_mail).'"'
		))) {
			return;
		}
		if (!$badinfo || empty($badinfo[2])) {
			$badinfo = null;
		}

		layout_restart_row();
			?><label for="DESTINATION[email]" class="nowrap <?
			if (!$do_email) {
				?>not-<?
			}
			?>bold-hilited"><?= Eelement_name('email')
			?> <?
			show_input([
				'checked'	=> $do_email,
				'onclick'	=> /** @lang JavaScript */ 'Pf_targetEmail(this);',
				'name'		=> 'DESTINATION[email]',
				'value'		=> '1',
				'type'		=> 'checkbox',
				'class'		=> 'upLite',
			]);
			?></label><?

			layout_next_cell();
			?><div id="destination_email"<?
			if (!$do_email) {
				?> class="hidden"<?
			}
			?>><?
			?><div class="nowrap"><?
			show_input([
				'onchange'		=> /** @lang JavaScript */ 'parsecontactrecipient(this);',
				'required'		=> $do_email,
				'type'			=> 'email',
				'data-valid'	=> 'working-email',
				'id'			=> 'to',
				'name'			=> 'TO',
				'value'			=> $to_mail ? escape_specials($to_mail) : null,
			]);
			if (SMALL_SCREEN) {
				?><br /><?
			} else {
				?> <?
			}
			?><label class="cbi not-bold-hilited"><?
			show_input([
				'onclick'	=> 'Pf.checkMulti(this)',
				'name'		=> 'MAILING',
				'type'		=> 'checkbox',
				'class'		=> 'upLite',
				'value'		=> '1',
			]);
			?> <?= element_name('mailing')
			?></label><?
			?></div><?
			?><textarea class="growToFit hidden" cols="50" data-max="10" name="TOMULTI" disabled></textarea><?
			?></div><?
			if (!empty($badinfo)) {
				[$cimidstr, $last, $cnt, $stampstr, $ticketstr, $reasonstr] = $badinfo;
				?><div id="bad-email" class="warning-nb"><?=
					__('contact:info:email_bad_info_LINE',[
						'CNT'		=> $cnt,
						'LAST'		=> _date_get($last),
					])
				?> <?
				?><span class="unhideanchor" onclick="swapdisplay('cimids');swaphtml(this);" data-otherhtml="<?= __('action:hide') ?>"><?= __('action:show') ?></span><?
				?></div><?
				layout_restart_row(id: 'cimids', rowuserdef: 'hidden body');
				layout_next_cell();
				$tickets = explode(',', $ticketstr);
				$stamps  = explode(',', $stampstr);
				$cimids  = explode(',', $cimidstr);
				$reasons = explode(',', $reasonstr);
				arsort($cimids);
				?><dl><?
				foreach ($cimids as $i => $cimid) {
					$ticketid = $tickets[$i] ?? null;
					$stamp = $stamps[$i];
					?><dt><a href="/contact/undeliverable/<?= $cimid ?>"><?= _date_get($stamp) ?></a><?
					if ($ticketid) {
						?> &rarr; <a href="/ticket/<?= $ticketid ?>"><?= element_name('contact_ticket') ?> <?= $ticketid ?></a><?
					}
					?></dt><?
					if ($reason = $reasons[$i] ?? null) {
						?><dd><?= escape_specials(preg_replace('"<?(\S*)@(\S*)>?"i', '', $reason)) ?></dd><?
					}
				}
				?></dl><?
			}

		layout_restart_row($emailflags, 'email_name');
			?><label for="toname"><?= Eelement_name('name') ?></label><?
			layout_field_value();

			?><input type="text" name="TONAME" id="toname" value="<?= escape_specials($to_name) ?>" onchange="parsetoname(this)" /><?

		layout_restart_row($emailflags, 'email_subject');
			echo Eelement_name('subject');
			layout_field_value();
			show_input([
				'maxlength'		=> 150,
				'type'			=> 'text',
				'name'			=> 'SUBJECT',
				$subject_key	=> $template['SUBJECT'] ?? $subject ?? null,
				'required'		=> $do_email,
			]);

		layout_restart_row($emailflags, 'email_header');
			echo Eelement_name('salutation');
			layout_field_value();
			?><input type="hidden" id="header_template"	value="<?= escape_utf8_to_win1252($signature['HEADER']) ?>" /><?
			?><input type="hidden" id="header_ls_template" value="<?= escape_utf8_to_win1252($signature['HEADER_LS']) ?>" /><?
			require_once '_email.inc';
			?><input type="text" data-impersonal="<?= implodekeys('|', IMPERSONAL_USERS) ?>" name="HEADER" id="header" value="<?
			ob_start();
			echo escape_specials(
					$first_name
				?	str_replace('#NAAM#', $first_name, $signature['HEADER'])
				:	$signature['HEADER_LS']
			);
			$hdr = ob_get_flush();
			?>" /><?
			?><input type="hidden" name="ORIG_HEADER" value="<?= $hdr ?>" /><?
		$dofooter = true;
#	}
	if ($stdanswers) {
		layout_restart_row(isset($stdanswers[$firstelement]) ? 0 : ROW_HIDDEN, 'stdanswers');
		include_js('js/contact_'.$element, base: 'js/contact');
		echo Eelement_name('standard');

		layout_field_value();

		?><select<?
		?> name="ANSWERDATAID"<?
		?> onchange="changeStandardAnswer(this)"<?
		?> data-active-answers="<?= $firstelement ?>"><?

		if ($firstelement
		&&	isset($stdanswers[$firstelement])
		) {
			?><option value="0"></option><?
			foreach ($stdanswers[$firstelement] as $stdanswer) {
				?><option value="<?= $stdanswer['DATAID'] ?>"><?= escape_specials($stdanswer['TITLE']) ?></option><?
				$bodies[$stdanswer['DATAID']] = $stdanswer;
			}

		}
		?></select><?
		foreach ($stdanswers as $tmpelement => $answers) {
			?><select class="hidden" id="stdanswers-<?= $tmpelement ?>"><?
			?><option value="0"></option><?
			foreach ($answers as $stdanswer) {
				?><option value="<?= $stdanswer['DATAID']; ?>"><?= escape_specials($stdanswer['TITLE']); ?></option><?
				$bodies[$stdanswer['DATAID']] = $stdanswer;
			}
			?></select><?
		}
		foreach ($bodies as $dataid => $stdanswer) {
			if ($stdanswer['FLAGS'] & STDANSWER_AUTO_CLOSE) {
				?><input type="hidden" id="autoclose-<?= $dataid; ?>" value="yes" /><?
			}
			?><div class="hidden" id="stdanswer-<?= $dataid; ?>"><?= nl2br(escape_specials($stdanswer['BODY'])); ?></div><?
		}
	}
	layout_restart_row();
	echo Eelement_name('comment');
	layout_field_value('fw');

	?><textarea<?
	?> data-check-placeholders="#!([\w_]+)!#"<?
	?> name="MSGBODY"<?
	?> rows="10"<?
	?> cols="70"<?
	?> data-min="8"<?
	?> data-max="20"<?
	?> id="msgbody"<?
	?> required="required"<?
	?> class="growToFit"<?
	?> maxlength="<?= $spec['BODY']->maxlength ?>"<?
	?>><?
	if (!empty($template['BODY'])) {
		echo escape_specials($template['BODY']);

	} elseif (!empty($_REQUEST['BODY'])) {
		echo escape_utf8($_REQUEST['BODY']);
	}
	?></textarea><?

	if (isset($dofooter)) {
		layout_restart_row($emailflags, 'email_footer');
		echo Eelement_name('closing');
		layout_field_value();
		?><textarea name="FOOTER" rows="3" cols="40" data-max="3" class="growToFit"><?
		if (!empty($signature['FOOTER'])) {
			echo escape_utf8_to_win1252($signature['FOOTER']);
		}
		?></textarea><?
	}
	layout_stop_row();
	if (!NO_ALTER_ATTACHMENT) {
		require_once '_attachments.inc';
		show_attachment_options(include_table: false, template: $template);
	}
	layout_close_table();
	$leftpart = ob_get_clean();

	ob_start();
	layout_open_table(TABLE_CLEAN, null, 'nodyn');

	layout_start_row();
	if (!NO_ALTER_ATTACHMENT) {
		?><label for="attachment-c"><?= Eelement_name('attachment') ?></label><?
		layout_field_value();
		show_input_with_label(
			checked: false,
			hilited: 'bold-hilited',
			base:	 'basehl cbi',
			input:	[	'type'		=> 'checkbox',
						'name'		=> 'ATTACHMENT_C',
						'id'		=> 'attachment-c',
						'value'		=> '1',
						'onclick'	=> 'showAttachments(this)']);

		layout_restart_row();
	}

	?><label class="not-bold-hilited" for="inbox"><?= Eelement_name('inbox') ?></label><?
	layout_field_value();
	show_input_with_label(
		checked: false,
		hilited: 'bold-hilited',
		base:	 'basehl cbi',
		input:	[	'type'		=> 'checkbox',
				'name'		=> 'INBOX',
				'id'		=> 'inbox',
				'value'		=> 1
	]);

	layout_restart_row();
	?><label for="trackit"><?= __C('action:follow') ?></label><?
	layout_field_value();

	show_input_with_label(
		checked: false,
		hilited: 'bold-hilited',
		base:	 'basehl cbi',
		input:	[	'type'		=> 'checkbox',
				'name'		=> 'TRACKIT',
				'id'		=> 'trackit',
				'value'		=> 1,
	]);

	layout_stop_row();
	layout_close_table();

	$rightpart = ob_get_clean();

/*	layout_open_box('contact-touser relative');
	?><table class="fw nomargin"><tr><td class="fw" style="padding-right:2em"><?
	echo $leftpart;
	?></td><td class="right vbottom"><?
	echo $rightpart;
	?></td></tr></table><?
	layout_close_box();*/

	layout_open_box('contact-touser');
	?><table class="dyntab fw nomargin"><?
	?><tr><?
	?><td class="fw"><?= $leftpart ?></td><?
	?><td class="right vbottom" style="padding-left: 1em;"><?= $rightpart ?></td><?
	?></tr></table><?
	layout_close_box();

	?><div class="block"><?
	if (!isset($no_submit)) {
		?><input type="submit" name="OUTGOING" value="<?= __('action:send'); ?>" /> <?
	}
	?><span id="return-part" class="lmrgn"><?
	ob_start();
	show_return_selection($firstelement, class: 'lmrgn');
	$select = ob_get_clean();
	echo str_replace('%DURATION%', $select, __('attrib:back_in_duration', KEEP_EMPTY_KEYWORDS));
	?></span><?

	?><input style="margin-left:2em" type="submit" name="CLOSEOUT" value="<?= __('action:send_and_close') ?>" /><?
	?></div></form><?
}

function contact_commit() {
	if (!have_post()) {
		return null;
	}
	if (have_something_trim($_POST, 'MSGBODY')
	&&	str_contains($_POST['MSGBODY'], '&#8201;')
	) {
		$_POST['MSGBODY'] = str_replace('&#8201;', '', $_POST['MSGBODY']);
	}
	if (isset($_POST['NEW'])) {
		if (!have_user()) {
			require_once 'recaptcha.inc';
			if (!empty($_POST['EMAIL'])
			&&	!require_email($_POST, 'EMAIL')
			||	!validate_recaptcha('new_ticket', $_POST['EMAIL'])
			) {
				return false;
			}
		}
		if ($ticketid = contact_commit_new()) {
			if (!have_user()) {
				if (($uniq = getifset($_COOKIE,'FLOCK_EMAILUNIQ'))
				&&	strlen($uniq) !== 32
				) {
					$uniq = null;
				}
				if (!$uniq) {
					$uniq = create_password(32);
					setflockcookie('FLOCK_EMAILUNIQ',$uniq,COOKIE_SET_LONG_LIVED);
				}
				db_insert('emailticketuniq','
				REPLACE INTO emailticketuniq SET
					UNIQ	="'.addslashes($uniq).'",
					STAMP	='.CURRENTSTAMP.',
					TICKETID='.$ticketid
				);
			}
			if (false !== store_messages_in_cookie()) {
				header('Location: https://'.$_SERVER['HTTP_HOST'].'/ticket/'.$ticketid,true,303);
				exit;
			}
			$GLOBALS['ALLOW_TICKET_VIEW'] = true;
		}
		if (!empty($_POST['NEWSUBJECT'])) {
			mail_log('WARNING NEWSUBJECT is set for new ticket '.$ticketid, item: $_POST);
		}
		return $ticketid;
	}
	if (isset($_POST['OUTGOING'])
	||	isset($_POST['CLOSEOUT'])
	) {
		return contact_commit_outgoing();
	}
	if ($ticketid = $_REQUEST['sID']) {
		if ($formstamp = have_idnumber($_POST, 'FORMSTAMP')) {
			$lstamp = db_single('contact_ticket_message','
				SELECT CSTAMP
				FROM contact_ticket_message
				WHERE TICKETID='.$ticketid.'
				  AND USERID_FROM!='.CURRENTUSERID.'
				ORDER BY CTMSGID DESC LIMIT 1'
			);
			if ($lstamp > $formstamp) {
				register_warning('contact_ticket:warning:new_unseen_message_TEXT',DO_NL2BR|DO_UBB,[
					'TICKETID'	=> $ticketid
				]);
				$recheck = true;
			}
		}
		if (!isset($recheck)) {
			if (!have_lock(LOCK_TICKET, $ticketid, ALLOW_STALE)) {
				if ($tickethash = have_something($_POST, 'TICKETHASH')) {
					$nowhash = get_ticket_hash($ticketid);
					if ($tickethash !== $nowhash) {
						register_warning('contact_ticket:warning:ticket_has_changed_TEXT', DO_NL2BR | DO_UBB, ['TCKETID' => $ticketid]);
						$recheck = true;
					}
				}
			}
		}
	}
	if (isset($_POST['ANSWER'])
	||	isset($_POST['ANSWERCLOSE'])
	) {
		return contact_commit_answer();
	}
	if (isset($_POST['FORWARD'])) {
		return contact_forward();
	}
	if (isset($_POST['MOVE'])) {
		return contact_move();
	}
	return contact_commit_response();
}

function get_ticket_hash(int $ticketid): string|false {
	static $__hash;
	if (!isset($__hash[$ticketid])) {
		if (!($ticket = db_single_array('contact_ticket', 'SELECT * FROM contact_ticket WHERE TICKETID = '.$ticketid, DB_USE_MASTER))) {
			return false;
		}
		$__hash[$ticketid] = hash('xxh128', igbinary_serialize($ticket));
	}
	return $__hash[$ticketid];
}

function contact_commit_outgoing(): int|false {
	if (!require_user()
	||	!require_idnumber		($_POST, 'LAST_TICKETID')
	||	!require_outgoing_element	($_POST, 'ELEMENT')
	||	!require_outgoing_allowance	($_POST['ELEMENT'], have_idnumber($_POST, 'ID'))
	||	!require_hash			($_POST, 'DESTINATION', HASH_ELEMENT, null, $size, ['email', 'user'])
	||	!require_contact_message_input	($_POST)
	||	false === require_regex		($_POST, 'RETURN', '"^(\d+),(\d+)$"')
	||	!verify_attachments()
	) {
		return false;
	}
	require_once '_contact_ticket_outgoing.inc';
	return contact_create_outgoing();
}

function contact_commit_response(): bool {
	require_once '_contactsubject.inc';
	require_once '_attachments.inc';
	if (!($ticketid = require_idnumber($_REQUEST,'sID'))
	||	!require_something_trim($_POST, 'MSGBODY')
	||	false === ($return = require_regex	($_POST, 'RETURN','"^(\d+),(\d+)$"'))
	||	!optional_number($_POST, 'TO_USERID')
	||	isset($_POST['MOVEIT'])
	&&	(	!require_contact_element($_POST, 'ELEMENT')
		||	false === require_number($_POST, 'ID')
		)
	||	!require_contact_message_input($_POST)
	||	!verify_attachments()
	||	!require_admin()
	) {
		return false;
	}
	require_once '_lock.inc';
	if (!($ticket = db_single_assoc('contact_ticket','
		SELECT OUTGOING, OWNERID, TICKETID, LASTMAIL, ISMAIL, WITHUSERID, ELEMENT, ID, STATUS, TRACKIT, MUSERID
		FROM contact_ticket
		WHERE TICKETID = '.$ticketid))
	) {
		if ($ticket !== false) {
			register_error('contact_ticket:error:nonexistent_LINE', ['ID' => $ticketid]);
		}
		return false;
	}
	if (!have_lock(LOCK_TICKET, $ticket['TICKETID'], ALLOW_STALE)) {
		if ($tickethash = have_something($_POST, 'TICKETHASH')) {
			if ($tickethash === ($nowhash = get_ticket_hash($ticket['TICKETID']))) {
				if (!require_obtainlock(LOCK_TICKET, $ticket['TICKETID'])) {
					return false;
				}
			} elseif (!require_lock(LOCK_TICKET, $ticket['TICKETID'], ALLOW_STALE)) {
				return false;
			}
		}
	}

	$do_email = $ticket['LASTMAIL'] || isset($_POST['SEND_TO']);
	$cc = '';

	if ($do_email
	&&	!isset($_POST['COMMENT'])
	&&	!isset($_POST['DISCUSSION'])
	) {
		if (!require_anything_trim($_POST, 'HEADER')
		||	!require_anything_trim($_POST, 'FOOTER')
		||	!require_something_trim($_POST, 'FROMNAME')
		||	!require_something($_POST, 'SEND_TO')
		) {
			return false;
		}
		switch ($_POST['SEND_TO']) {
		case 'OTHER':
			if (!require_working_email($_POST, 'EMAIL')) {
				return false;
			}
			$to_name = null;
			$to_mail = $_POST['EMAIL'];
			break;
		default:
			if (!preg_match('"^(?<CTMSGID>\d+),(?<mail_option_type>\d)(?:;(?<base64_emails>[a-zA-Z0-9=]+))?$"', $_POST['SEND_TO'], $send_to_match)) {
				mail_log_register_generic('contact_commit_response: BAD_SEND_TO', get_defined_vars());
				return false;
			}
			$send_to_match['CTMSGID']		   = (int)$send_to_match['CTMSGID'];
			$send_to_match['mail_option_type'] = (int)$send_to_match['mail_option_type'];

			require_once '_contactmailoptions.inc';
			switch ($send_to_match['mail_option_type']) {
			case MAIL_EVERYONE:
				if (false === ($contact_info = get_contact_mails_for_everyone($ticketid))) {
					return false;
				}
				/** @noinspection PhpRedundantOptionalArgumentInspection */
				extract($contact_info, \EXTR_OVERWRITE);
				$ccs[] = $original_tomail;
				$cc = implode(', ',$ccs);
				break;
			case MAIL_FROM:
			case MAIL_FROM_FORWARDED:
			case MAIL_REPLYTO:
			case MAIL_TO:
				if (false === ($mail_options = get_contact_mail_options($ticketid))) {
					return false;
				}
				$found = false;
				foreach ($mail_options as $mail_option) {
					if ($send_to_match['CTMSGID']		   === $mail_option['CTMSGID']
					&&	$send_to_match['mail_option_type'] === $mail_option['TYPE']
					) {
						$found = true;
						break;
					}
				}
				if (!$found) {
					mail_log_register_generic('contact_commit_response: NO email option', get_defined_vars());
					return false;
				}
				$to_mail = $mail_option['EMAIL'];
				$to_name = $mail_option['NAME'];
				break;

			case MAIL_FROM_TEMPLATE:
				if (!($mail_info = db_single_assoc(	['contact_ticket_message','contact_ticket_mail'], "
					SELECT FROM_EMAIL, FROM_NAME, TO_EMAIL, TO_NAME, SUBJECT, REPLYTO, REPLYTONAME, BODY, CSTAMP, DIRECTION, TYPE AS MIMETYPE
					FROM contact_ticket_message
					LEFT JOIN contact_ticket_mail AS ctm USING (CTMSGID, TICKETID)
					WHERE CTMSGID = {$send_to_match['CTMSGID']}"))
				) {
					mail_log_register_generic('contact_commit_response: NO email option', get_defined_vars());
					return false;
				}
				$to_mail = $mail_info['FROM_EMAIL'];
				$to_name = $mail_info['FROM_NAME'];
				break;

			case MAIL_FROM_PROFILE:
			case MAIL_FROM_BODY:
			case MAIL_CC:
				if (empty($send_to_match['base64_email'])
				||	!($to_mail = base64_decode($send_to_match['base64_email']))
				) {
					mail_log_register_generic('contact_commit_response: BAD_OR_NO_BASE64_EMAIL', get_defined_vars());
					return false;
				}
				$to_name = ucfirst(substr($to_mail, 0, strpos($to_mail, '@')));
				break;

			default:
				mail_log_register_generic('contact_commit_response: unsupported mail_option_type', get_defined_vars());
				return false;
			}
		}
		if (isset($_POST['QUOTE'])
		&&	false === ($quotemsgs = db_rowuse_array(['contact_ticket_mail','contact_ticket_message'], "
			SELECT CTMSGID, CSTAMP, BODY, BODYUTF8, FROM_NAME, FROM_EMAIL, TYPE AS MIMETYPE, USERID_FROM, UNCOMPRESS(HEADERS) AS HEADERS, DIRECTION
			FROM contact_ticket_message
			LEFT JOIN contact_ticket_mail USING (CTMSGID, TICKETID)
			WHERE TICKETID = $ticketid
			  AND DIRECTION IN ('toadmin', 'touser')
			  AND (ISNULL(FROM_EMAIL) OR FROM_EMAIL NOT LIKE 'mailer-daemon@%')
			  AND CTMSGID >= (SELECT MAX(CTMSGID) FROM contact_ticket_message WHERE TICKETID = $ticketid AND DIRECTION = 'toadmin')
			ORDER BY CTMSGID DESC"))
		) {
			return false;
		}
	}
	if (isset($_POST['COMMENT'])) {
		$direction = 'toadminfromadmin';
		$forward = null;
	} elseif (isset($_POST['DISCUSSION'])) {
		$direction = 'toadminfromadmin';
	} else {
		$direction = 'touser';
		$touser = true;
	}
	if (isset($to_name)) {
		$to_name = preg_replace('"<(.*)>"', '', $to_name);
	}
	if (str_contains($_POST['MSGBODY'], '#UNIQCODE#')) {
		global $__year, $__month, $__day;
		$uniqcode = sprintf('PF %04d%02d%02d %s', $__year, $__month, $__day, substr(crc32($ticketid), 0, 5));
		$_POST['MSGBODY'] = str_replace('#UNIQCODE#', $uniqcode, $_POST['MSGBODY']);
	}
	if (str_contains($_POST['MSGBODY'],'%MATCHED_CNT%')) {
		$cnt =	 ($offenses = db_simpler_array('offense','SELECT OFFENSEID FROM offense WHERE ELEMENT="'.$ticket['ELEMENT'].'" AND ID='.$ticket['ID']))
			?	db_single_int('spamremoved','SELECT MATCHED_CNT FROM spamremoved WHERE OFFENSEID IN ('.implode(',', $offenses).') LIMIT 1')
			:	0;
		$ticket['MATCHED_CNT'] = $cnt ?: 0;
	}
	$_POST['MSGBODY'] = parse_text($_POST['MSGBODY'],$ticket);

	if (!db_insert('contact_ticket_log','
		INSERT INTO contact_ticket_log
		SELECT * FROM contact_ticket
		WHERE TICKETID = '.$ticketid)
	) {
		return false;
	}
	if ($to_inbox = isset($_POST['INBOXACTION']) && $_POST['INBOXACTION'] === 'into') {
		$forward = ',OWNERID='.CURRENTUSERID;
		$trackit = isset($_POST['TRACKIT']) || $ticket['STATUS'] !== 'open' ? 1 : 0;
	} elseif (
		isset($_POST['FORWARDIT'])
	&&	($touserid = have_idnumber($_POST, 'TO_USERID'))
	) {
		$forward = ',OWNERID='.$touserid;
		$trackit = isset($touser) || $ticket['STATUS'] !== 'open' ? 1 : 0;
	} elseif (isset($_POST['COMMENT'])) {
		$forward = null;
		if (!have_idnumber($_POST, 'TO_USERID')) {
			require_once '_crew.inc';
			$touserid =
				db_single('contact_ticket_message','
				SELECT USERID_FROM
				FROM contact_ticket_message
				WHERE DIRECTION IN ("toadminfromadmin","touser")
				  AND TICKETID='.$ticketid.'
				  AND USERID_FROM!='.CURRENTUSERID.'
				ORDER BY /*DIRECTION="toadminfromadmin" DESC,*/CTMSGID DESC
				LIMIT 1')
			?:	(	($crew = get_crewlist())
				&&	isset($crew[$ticket['MUSERID']])
				&&	$ticket['MUSERID'] !== CURRENTUSERID
				?	$ticket['MUSERID']
				:	0
				);

			if ($touserid) {
				if (($huser = contact_holiday_info($touserid))
				&&	$huser['SUBSTITUTID']
				) {
					$touserid = $huser['SUBSTITUTID'] === 1 ? 0 : $huser['SUBSTITUTID'];
				}
				if ($touserid) {
					$forward = ',OWNERID='.$touserid;
				}
			}
		}
		$trackit = isset($_POST['TRACKIT']) ? 1 : 0;
	} elseif ($out_inbox = isset($_POST['INBOXACTION']) && $_POST['INBOXACTION'] === 'outof') {
		$forward = ',OWNERID=0';
		$trackit = $ticket['STATUS'] === 'open' ? 0 : 1;
	} else {
		$trackit = isset($_POST['TRACKIT']) ? 1 : 0;
	}

	$mail_ticket =
		!isset($_POST['COMMENT'])
	&&	!isset($_POST['DISCUSSION'])
	&&	$do_email;

	if ($mail_ticket) {
		if (false === ($references = db_simpler_array('contact_ticket_mail', "
			SELECT DISTINCT MESSAGEID
			FROM contact_ticket_mail
			WHERE TICKETID = $ticketid
			  AND MESSAGEID
			ORDER BY CTMSGID DESC"
		))) {
			return false;
		}
	}

	if (!($ctmsgid = contact_commit_message($direction, $ticket['WITHUSERID'], $mail_ticket))) {
		return false;
	}

	[,$expires, $expstamp] = $return;
	$msgarg = ['TICKETID' => $ticketid];

	if ($expires % 3600) {
		error_log_r($_POST ,'WARNING strange EXPIRES for ticket '.$ticketid.' @ '.$_SERVER['REQUEST_URI']);
	}

	if (isset($_POST['RESPOND'])) {
		$setstr = "
			LASTDIRECTION	= 'touser',
			EXPIRES			= $expires,
			EXPSTAMP		= $expstamp,
			OMSGCNT			= OMSGCNT + 1,
			READM			= 0,
			STATUS			= 'pending'";
		$msgkey = 'contact_ticket:notice:response_added_LINE';
		$msg_to_user = true;
	} elseif (isset($_POST['CLOSE'])) {
		$setstr = "
			LASTDIRECTION	= 'touser',
			EXPIRES			= $expires,
			EXPSTAMP		= $expstamp,
			OMSGCNT			= OMSGCNT + 1,
			READM			= 0,
			STATUS			= 'closed',
			CLOSETYPE		= 'normal'";
		$trackit = 0;
		$msgkey = 'contact_ticket:notice:response_added_and_ticket_closed_LINE';
		$msg_to_user = true;
	} elseif (isset($_POST['DISCUSSION'])) {
		$setstr = '
			LASTDIRECTION	= "toadminfromadmin",
			OWNERID			= 0';
		$_REQUEST['KEEPLOCK'] = true;
		if ($ticket['STATUS'] === 'closed'
		||	$ticket['STATUS'] === 'pending'
		) {
			$trackit = 1;
		}
		$msgkey = 'contact_ticket:notice:discussion_added_LINE';
	} elseif (isset($_POST['PROGRESS'])) {
		$setstr = "
			LASTDIRECTION	= 'touser',
			OMSGCNT			= OMSGCNT + 1,
			READM			= 0";
		$msgkey = 'contact_ticket:notice:progress_added_LINE';
		$msg_to_user = true;
	} elseif (isset($_POST['COMMENT'])) {
		$setstr = '
			LASTDIRECTION	= "toadminfromadmin"';
		if ($ticket['STATUS'] === 'closed'
		||	$ticket['STATUS'] === 'pending'
		) {
			$trackit = 1;
		}
		$msgkey = 'contact_ticket:notice:comment_added_LINE';
	} else {
		mail_log_register_generic('contact_commit_response: no action', item: ['_POST' => $_POST, 'vars' => get_defined_vars()]);
		return false;
	}
	if ($move
	=	isset($_POST['MOVEIT'])
	&&	(	$ticket['ELEMENT'] !== $_POST['ELEMENT']
		||	$ticket['ID'] !== $_POST['ID']
		)
	) {
		require_once '_contactconcerning.inc';
		$setstr .= ",
			ELEMENT		= '{$_POST['ELEMENT']}',
			ID			= {$_POST['ID']},
			CONCERNING	= ".get_concerning($_POST['ELEMENT'], $_POST['ID']);
	}
	if (!empty($to_mail)) {
		$setstr .= ',WITHEMAIL='.(have_other_emails($ticketid, $to_mail) ? '""' : '"'.addslashes($to_mail).'"');
	}

	if ($do_email) {
		$setstr .= ',LASTMAIL=1';
	}

	if (!db_update('contact_ticket','
		UPDATE contact_ticket SET '.
			$setstr.
			(!isset($_POST['PROGRESS']) ? ',LSTAMP='.CURRENTSTAMP : null).',
			MSTAMP		='.CURRENTSTAMP.',
			MUSERID		='.CURRENTUSERID.',
			MSGCNT		=MSGCNT+1,
			TRACKIT		='.$trackit.
			(!empty($forward) ?	 $forward : null).'
		WHERE TICKETID  ='.$ticketid)
	) {
		return false;
	}
	register_notice($msgkey,DO_UBB,$msgarg);
	if ($move) {
		register_notice('contact_ticket:notice:moved_LINE',DO_UBB, ['TICKETID' => $ticketid, 'ELEMENT' => $_POST['ELEMENT'], 'ID' => $_POST['ID']]);
	}
	if (!empty($touserid)) {
		register_notice('contact_ticket:notice:forwarded_LINE',DO_UBB, ['TICKETID' => $ticketid, 'USERID' => $touserid]);
	} elseif (!empty($to_inbox)) {
		register_notice('contact_ticket:notice:moved_to_inbox_LINE',DO_UBB, ['TICKETID' => $ticketid]);
	} elseif (!empty($out_inbox)) {
		register_notice('contact_ticket:notice:moved_from_inbox_LINE',DO_UBB, ['TICKETID' => $ticketid]);
	}
	if ($trackit) {
		if (!$ticket['TRACKIT']) {
			register_notice('contact_ticket:notice:tracked_LINE',DO_UBB, ['TICKETID' => $ticketid]);
		}
	} elseif ($ticket['TRACKIT']) {
		register_notice('contact_ticket:notice:untracked_LINE',DO_UBB, ['TICKETID' => $ticketid]);
	}
	if (!isset($_POST['KEEPLOCK'])) {
		release_lock(LOCK_TICKET, $ticketid);
	}
	$is_ham =
		isset($msg_to_user)
	&&	$do_email;

	if ($is_ham) {
		db_update(['completemsg', 'contact_ticket_mail'],'
		UPDATE completemsg
		JOIN contact_ticket_mail USING (CTMSGID)
		SET completemsg.TYPE = "ham"
		WHERE STATUS	= "new"
		  AND CIMID	= 0
		  AND TICKETID	= '.$ticketid
		);
	}

	if (isset($uniqcode)) {
		db_insert('uniquecode','
			INSERT INTO uniquecode SET
				TICKETID='.$ticketid.',
				CTMSGID	='.$ctmsgid.',
				UNIQ	="'.$uniqcode.'",
				STAMP	='.CURRENTSTAMP
		);
	}

	contact_clear_cache();

	if ($ticket['WITHUSERID']) {
		memcached_delete('contactcnts:'.$ticket['WITHUSERID']);
	}

	template_post_process($ticketid, $ctmsgid);
	if (!$mail_ticket) {
		create_message_and_attachments($ticketid, false, $ctmsgid);
		return true;
	}
	require_once '_contactemails.inc';
	$frommail = contact_get_email($ticket['ELEMENT']);

	# don't change subject, gmail won't thread alright then
#	$subject = make_ticket_subject(isset($_POST['SUBJECT']) ? $_POST['SUBJECT'] : $mailinfo['SUBJECT'],$ticketid);
	$subject = $_POST['SUBJECT'] ?? $mail_info['SUBJECT'];

	$mailstr =
		(!empty($_POST['HEADER']) ? $_POST['HEADER']."\r\n\r\n" : '').
		$_POST['MSGBODY'];

	if (!empty($_POST['FOOTER'])) {
		$mailstr .= "\r\n".$_POST['FOOTER'];
	}

	$from =	empty($_POST['FROMNAME'])
	?	$frommail
	:	mime_header_address($_POST['FROMNAME'])." <$frommail>";

	$quote_str = '';

	if (!empty($quotemsgs)) {
		$prefix = '';

		$quotes_left = 5;

		foreach ($quotemsgs as $quote_msg) {
			if ($quote_msg['MIMETYPE'] === 'text/html') {
				$quote_msg['BODY'] = strip_tags(preg_replace('"(</div>|<br ?/>)"i',"\r\n",$quote_msg['BODY']));
			}
			[$body, $quotedmessage, $quotedlines, $utf8] = split_message($quote_msg);
			if ($utf8) {
				$body = utf8_to_win1252($body);
			}

			# sanitize for SPAM filters:
			$body = preg_replace(
					['"\?(?:\s*\?)+"', '"^https?://tinyurl\.com/\w+"'],
					['?',				'LINK'],
					$body
			);
			if ($prefix) {
				$quote_str .= $prefix."\r\n";
			}
			[$y,$m,$d,$hour,$mins] = _getdate($quote_msg['CSTAMP']);
			$quote_str .=
				$prefix.'On '.sprintf('%04d-%02d-%02d %02d:%02d',$y,$m,$d,$hour,$mins).', '.
					($quote_msg['FROM_NAME'] ? $quote_msg['FROM_NAME'].', ' : '').
					($quote_msg['FROM_EMAIL'] ?: memcached_nick($quote_msg['USERID_FROM'])).
					" wrote:\r\n";

			$prefix .= '>';

			$body = strip_shy($body);

			$quote_str .= $body;

			--$quotes_left;

			if ($quote_msg['DIRECTION'] === 'toadmin'
			||	!$quotes_left
			) {
				break;
			}
		}
	}
	[$msg, $extra_headers] = create_message_and_attachments($ticketid, $mailstr, $ctmsgid, $to_mail, $quote_str, $cc);

	if (!isset($to_mail)) {
		mail_log('contact_commit_response: to_mail is not set for ticket '.$ticketid, get_defined_vars());
		_error('GEEN goede to_mail, is dit wel een e-mailticket?');
		return false;
	}

	if (isset($to_name)) {
		$to_name = preg_replace('"<(.*)>"','',$to_name);
	}
	require_once 'defines/nomail.inc';

	if (no_mail($to_mail)) {
		$rc = true;
		$messageid = '';
		$all_headers = '';
	} else {
		$rc = pf_mail(
		isset($to_name) ? mime_header_address($to_name).' <'.$to_mail.'>' : $to_mail,
		mime_header_subject($subject),
		$msg,
		$all_headers =
		'From: '.$from."\r\n".
		($extra_headers ? implode("\r\n", combine_mail_headers($extra_headers))."\r\n" : '').
		($references ? 'References: '.implode("\r\n ", array_map('mime_header_subject', $references))."\r\n" : '').
		($references ? 'In-Reply-To: '.mime_header_subject($references[0])."\r\n" : '').
		'Message-ID: '.($messageid = "<$ticketid.$<EMAIL>>"),
		'-f'.$frommail
		);
	}
	if (!$rc) {
		_error(	'Email is <i>niet</i> verzonden, neemt contact op met <a href="/msg/form/2269?B64BODY='.
			base64_encode('[ticket='.$ticketid."]\n\nIk kan geen mail versturen bij bovenstaand ticket!").
			'">'.escape_specials(memcached_nick(2269)).'</a>!'
		);
		if (isset($to_name)) {
			error_log('ERROR TICKET '.$ticketid.' FAILMAIL - toname: '.mime_header_address($to_name));;
		}
		error_log('ERROR TICKET '.$ticketid.' FAILMAIL - tomail: '.$to_mail);
		error_log('ERROR TICKET '.$ticketid.' FAILMAIL - subject: '.mime_header_subject($subject));
		error_log('ERROR TICKET '.$ticketid.' FAILMAIL - from: '.$from);
#		return false;
	}
	if (!db_insert('contact_ticket_mail',"
		INSERT INTO contact_ticket_mail SET
			TICKETID	= $ticketid,
			OUTGOING	= 1,
			CTMSGID		= $ctmsgid,
			MESSAGEID	= '".addslashes($messageid)."',
			SUBJECT		= '".addslashes($subject)."',
			HEADERS		= COMPRESS('".addslashes($all_headers)."'),
			MREFERENCES	= '',
			FROM_NAME	= '".(!empty($signature['FROMNAME']) ? addslashes($signature['FROMNAME']) : '')."',
			FROM_EMAIL	= '".addslashes($frommail)."',
			TO_EMAIL	= '".addslashes($to_mail)."',
			TO_NAME		= '".(isset($to_name) ? addslashes($to_name) : '')."',
			CC			= '".addslashes($cc)."'"
	)) {
		register_error('generic:error_LINE', ['ELEMENT' => 'contact_ticket_mail']);
	}


	require_once '_salutes.inc';
	store_email_salute($ticketid, $ctmsgid, $to_mail);

	if ($ticket['ELEMENT'] === 'camerarequest'
	&&	$ticket['ID']
	) {
		// only set written if not an offer
		// or if actually was written already
		if (!db_insert('camera_log','
			INSERT INTO camera_log
			SELECT * FROM camera
			WHERE PARTYID = '.$ticket['ID'])
		||	!db_update('camera', '
			UPDATE camera SET
				MSTAMP	= '.CURRENTSTAMP.',
				WRITTEN	= '.CURRENTSTAMP.'
			WHERE (OFFER = 0 OR WRITTEN NOT IN (0, 1))
			  AND PARTYID = '.$ticket['ID'])
		) {
			register_error('generic:error_LINE', ['ELEMENT' => 'camera']);
		}
	}
	if ($rc) {
		register_notice('email:notice:sent_LINE');
	}
	return $rc;
}

function contact_commit_answer(): bool {
	require_once '_attachments.inc';
	if (!($ticketid = require_idnumber	($_REQUEST,'sID'))
	||	!require_contact_message_input	($_POST)
	||	!require_user()
	||	!verify_attachments()
	||	false === ($ticket = db_single_array('contact_ticket', "
		SELECT WITHUSERID, ELEMENT, ID
		FROM contact_ticket
		WHERE TICKETID = $ticketid"))
	) {
		return false;
	}
	if (!$ticket) {
		not_found();
		return false;
	}
	[$withuserid, $element, $id] = $ticket;
	if (!$withuserid) {
		_error('Jij bent niet het contactpersoon van dit ticket!');
	}
	if (!require_self($withuserid)) {
		return false;
	}
	$lastmail = !empty($_POST['EMAILTYPE']) ? 1 : 0;

	if (!($ctmsgid = contact_commit_message('toadmin',$withuserid,$lastmail))) {
		return false;
	}
	if ($lastmail) {
		switch ($_POST['EMAILTYPE']) {
		case 'other':
			$email = getifset($_POST, 'EMAIL');
		case 'profile':
			$user = db_single_array('user','SELECT EMAIL,NICK,NAME,REALNAME FROM user WHERE USERID='.CURRENTUSERID);
			if (empty($email)) {
				$email = $_POST['EMAIL'] = $user ? $user[0] : false;
			}
			break;
		case 'last':
			$email = null;
			break;
		}

		if (!empty($_REQUEST['SUBJECT'])) {
			$subject = $_REQUEST['SUBJECT'];
		} else {
			# inherit subject from last mail
			if (!($subjects = db_single_assoc('contact_ticket_mail', "
				SELECT SUBJECT, SUBJECT_UTF8
				FROM contact_ticket_mail
				WHERE SUBJECT != ''
				  AND TICKETID = $ticketid
				ORDER BY CTMSGID DESC"))
			) {
				$subject =
					$id
				&&	isset(USE_URLTITLE[$element])
				?	escape_specials(get_element_title($element, $id), isset(USE_UNICODE[$element]))
				:	__C('elements:'.$element, DONT_ESCAPE);
			} else {
				$subject = escape_specials(
						$subjects['SUBJECT_UTF8']
					?	iconv('UTF-8', 'windows-1252//TRANSLIT', $subjects['SUBJECT_UTF8'])
					:	$subjects['SUBJECT']
				);
			}
		}

		if ($email) {
			[, $nick, $name, $realname] = $user;
			if (!db_insert('contact_ticket_mail','
				INSERT INTO contact_ticket_mail SET
					SUBJECT		="'.addslashes($subject).'",
					TICKETID	='.$ticketid.',
					FROM_NAME	="'.addslashes($realname ?: $name ?: $nick).'",
					FROM_EMAIL	="'.addslashes($email).'",
					HEADERS		="",
					MREFERENCES	="",
					CTMSGID		='.$ctmsgid)
			) {
			}
		} else {
			$lastmail = 0;
		}
	}
	store_via_email();

	if (!db_insert('contact_ticket_log','
		INSERT INTO contact_ticket_log
		SELECT * FROM contact_ticket
		WHERE TICKETID = '.$ticketid)
	||	!db_update('contact_ticket','
		UPDATE contact_ticket SET
			WITHEMAIL	='.(!$lastmail ? 'WITHEMAIL' : (have_other_emails($ticketid,$email) ? '""' : '"'.addslashes($email).'"')).',
			LASTDIRECTION	="toadmin",
			LASTMAIL	='.$lastmail.',
			MSGCNT		=MSGCNT+1,
			READM		=1,
			STATUS		="'.(($close = isset($_POST['ANSWERCLOSE'])) ? 'closed' : 'open').'",
			CLOSETYPE	='.($close ? '"normal"' : 'CLOSETYPE').',
			TRACKIT		='.($close ? 1 : 'TRACKIT').',
			LSTAMP		='.CURRENTSTAMP.',
			MSTAMP		='.CURRENTSTAMP.',
			MUSERID		='.CURRENTUSERID.'
		WHERE TICKETID = '.$ticketid)
	) {
		return false;
	}

	create_message_and_attachments($ticketid, false, $ctmsgid);

	register_notice('response:notice:processed_LINE');

	contact_clear_cache();

	unset($_POST['MSGBODY']);

	return true;
}

function contact_display_single() {
	require_once '_lock.inc';
	if (!($ticketid = require_idnumber($_REQUEST,'sID'))) {
		not_found();
		return;
	}
	$ticket = db_single_assoc('contact_ticket','SELECT * FROM contact_ticket WHERE TICKETID='.$ticketid);
	if ($ticket === false) {
		return;
	}
	if (!$ticket) {
		register_error('contact_ticket:error:nonexistent_LINE', ['ID' => $ticketid]);
		return;
	}
	$messages = db_rowuse_array(
		['contact_ticket_message','contact_ticket_mail'],'
		SELECT	CTMSGID, contact_ticket_message.CSTAMP, DIRECTION, BODY, BODYUTF8, USERID_FROM, ISMAIL, CIMID, TEMPLATE,
				FROM_EMAIL, FROM_NAME, TO_EMAIL, TO_NAME, OUTGOING, FORWARD, SUBJECT, SUBJECT_UTF8,
				UNCOMPRESS(HEADERS) AS HEADERS, TYPE AS MIMETYPE, AUTOGEN, PRECEDENCE, SPAMSCORE, INFECTED, INFOMSG, HTMLSRC, UNSUBSCRIBE
		FROM contact_ticket_message
		LEFT JOIN contact_ticket_mail USING (CTMSGID,TICKETID)
		WHERE TICKETID='.$ticketid.'
		ORDER BY CTMSGID DESC'
	);
	if ($messages === false) {
		return;
	}
	$firstmessage = end($messages);
	$lastmessage = reset($messages);
	$element = $ticket['ELEMENT'];
	$id = $ticket['ID'];

	layout_show_section_header($ticket);

	if ($noverview = isset($_REQUEST['NOVERVIEW'])) {
		if (!isset($_REQUEST['KEEPLOCK'])
		&&	$_REQUEST['ACTION'] !== 'single'
		) {
			return;
		}
	} elseif (!isset($_REQUEST['KEEPLOCK'])) {
		$noverview = $_REQUEST['NOVERVIEW'] = true;
	}

	$with_userid = $ticket['WITHUSERID'];
	$am_withu = (CURRENTUSERID === $with_userid);
	$am_admin = false;

	if (isset($GLOBALS['ALLOW_TICKET_VIEW'])
	||	!have_user()
	&&	($uniq = getifset($_COOKIE,'FLOCK_EMAILUNIQ'))
	&&	db_single('emailticketuniq','SELECT 1 FROM emailticketuniq WHERE UNIQ="'.addslashes($uniq).'" AND TICKETID='.$ticketid)
	) {
		$am_admin = false;

	} elseif (have_super_admin()) {
		$am_admin = true;

	} elseif ($am_withu) {
		$am_admin = false;

	} elseif (have_admin()) {
		if ($ticket['OWNERID']) {
			if (CURRENTUSERID === $ticket['OWNERID']) {
				$am_admin = true;

			} elseif (may_change_element($element, $id)) {
				$am_admin = true;
				$locked = false;

			} else {
				_error('Dit ticket is alleen te lezen door '.get_element_link('user',$ticket['OWNERID']));
				return;
			}
		} elseif (
			have_ticket_lock($ticketid)
		||	may_change_element($element, $id)
		||	str_starts_with($element, 'junk_')
		) {
			$am_admin = true;

		} elseif (have_admin('contact_ticket_old')) {
			$am_admin = $am_withu = false;
		} else {
			_error('Je hebt niet voldoende rechten om dit ticket te lezen!');
			no_permission();
			return;
		}
	} else {
		$current_email = memcached_single('user','SELECT EMAIL FROM user WHERE USERID = '.CURRENTUSERID);
		$ok = false;
		if (!$ok
		&&	$ticket['ISMAIL']
		&&	have_user()
		) {
			$ticketemails = memcached_simpler_array(['contact_ticket_mail', 'contact_ticket_message'], '
				SELECT DISTINCT FROM_EMAIL
				FROM contact_ticket_mail
				JOIN contact_ticket_message USING (CTMSGID, TICKETID)
				WHERE DIRECTION = "toadmin"
				  AND TICKETID = '.$ticketid
			);
			if (count($ticketemails) === 1
			&&	$ticketemails[0] === $current_email
			) {
				$ok = true;
			}
		}

		$okusers[CURRENTUSERID] = true;
		$okmails[$current_email] = true;

		if (!$ok
		&&	($rels = have_relation())
		) {
			switch ($element) {
			case 'ad':		$relid = memcached_single(array('ad','banner'),'SELECT RELATIONID FROM ad JOIN banner USING (BANNERID) WHERE ADID='.$id); break;
			case 'banner':	$relid = memcached_single('banner','SELECT RELATIONID FROM banner WHERE BANNERID='.$id); break;
			case 'invoice': $relid = memcached_single(array('invoice','relation'),'SELECT RELATIONID FROM relation JOIN invoice USING (DEBNR) WHERE INVOICENR='.$id); break;
			case 'newsad':	$relid = memcached_single('newsad','SELECT RELATIONID FROM newsad WHERE NEWSADID='.$id); break;
			case 'job':		$relid = memcached_single('job','SELECT RELATIONID FROM job WHERE JOBID='.$id); break;
			case 'promo':	$relid = memcached_single('promo','SELECT RELATIONID FROM promo WHERE PROMOID='.$id); break;
			default		:	$relid = 0;
			}

			if ($relid && isset($rels[$relid])) {
				$relstr = implode(',', $rels);
				if ($contactemails = memcached_simpler_array('relation','SELECT CONTACTEMAIL FROM relation WHERE RELATIONID IN ('.$relstr.')')) {
					foreach ($contactemails as $cmail) {
						$okmails[$cmail] = true;
					}
				}
				if ($members = memcached_simple_hash(array('relationmember','user'),'
					SELECT USERID, EMAIL
					FROM relationmember AS rm
					JOIN user USING (USERID)
					WHERE rm.RELATIONID IN ('.$relstr.')')
				) {
					foreach ($members as $muserid => $memail) {
						$okmails[$memail] = true;
						$okusers[$muserid] = true;
					}
				}
			}
		}
		if ($with_userid
		&&	isset($okusers[$with_userid])
		) {
			$ok = true;
		} else {
			foreach ($messages as $message) {
				switch ($message['DIRECTION']) {
				case 'touser':
					if (isset($okmails[$message['TO_EMAIL']])) {
						$ok = true;
						break 2;
					}
					break;
				case 'toadmin':
					if (isset($okmails[$message['FROM_EMAIL']])) {
						$ok = true;
						break 2;
					}
					break;
				}
			}
		}
		if (!$ok) {
			_error('Dit ticket is alleen door medewerkers en ticketopener te lezen!');
			no_permission();
			return;
		}
#		error_log('special ticket case for ticket '.$ticketid.' by user '.CURRENTUSERID,0);
		$am_withu = false;
		$am_admin = false;
	}
	include_js($am_withu ? 'js/form/contactuser' : 'js/form/contactadmin');

	if (!isset($locked)) {
		$locked =
			 $am_admin
		&&	!$am_withu
		&&	obtain_lock(LOCK_TICKET, $ticketid);
	}
	if ($locked) {
		# add delay because when we click on button to, for example, move the ticket, the
		# page gets unloaded and an immediate release_lock would then lose the lock before
		# the move ticket action is even started
		release_lock_on_unload(LOCK_TICKET, $ticketid, delay_release: 5);
	}
	if ($am_admin
	&&	$locked
	) {
		layout_open_menu();
		if ($ticket['OWNERID']) {
			layout_menuitem(__C('action:move_out_of_inbox'),'/ticket/'.$ticketid.'/moveaway');
			if ($ticket['OWNERID'] !== CURRENTUSERID) {
				layout_menuitem(__C('action:move_to_inbox'),'/ticket/'.$ticketid.'/movetome');
			}
		} else {
			layout_menuitem(__C('action:move_to_inbox'),'/ticket/'.$ticketid.'/movetome');
			layout_menuitem(__C('action:release'),'/ticket/'.$ticketid.'/release');
		}
		layout_open_menuitem();
		connect_menuitem();
		layout_close_menuitem();

		if ($ticket['TRACKIT']) {
			layout_menuitem(__C('action:unfollow'),'/ticket/'.$ticketid.'/trackoff');
			if ($ticket['OWNERID'] === CURRENTUSERID) {
				layout_menuitem(__C('action:unfollow_and_outbox'),'/ticket/'.$ticketid.'/trackoff/outbox');
			}
		} else {
			layout_menuitem(__C('action:follow'),'/ticket/'.$ticketid.'/trackon');
		}
		layout_close_menu();
		_connect_display_form('contact_ticket', $ticketid);
	}

	if (!$messages) {
		error_log('no messages for ticket '.$ticketid);
	}

	$have_real = $toadmin = false;
	$last_subject = null;
	$total = count($messages);
	$reordered_messages = $msgcnts = [];

	for ($i = 0; $i < $total; ++$i) {
		$message = $messages[$i];
		increment_or_set($msgcnts, $message['DIRECTION']);

		if (!$last_subject
		&&	$message['SUBJECT']
		&&	!$message['CIMID']
		&&	in_array($message['DIRECTION'], ['touser', 'toadmin', 'progress', 'forwardinfo'])
		) {
			$last_subject = $message['SUBJECT_UTF8'] ?: win1252_to_utf8($message['SUBJECT']);
		}
		switch ($message['DIRECTION']) {
		case 'touser':
			if (!isset($lstamp_touser)) {
				$lstamp_touser = $message['CSTAMP'];
			}

		case 'toadminfromadmin':
			$nospam = true;
			break;

		case 'toadmin':
			$toadmin = true;
			break;
		}
		if ($ticket['ISMAIL']) {
			if ($message['DIRECTION'] === 'toadmin'
			&&	!$message['AUTOGEN']
			&&	$message['PRECEDENCE'] !== 'bulk'
			) {
				$have_real = true;
			}
		}
		if ($message['DIRECTION'] === 'forwardinfo'
		&&	isset($messages[$i + 1])
		&&	$message['CSTAMP'] === $messages[$i + 1]['CSTAMP']
		) {
			$messages[$i + 1]['fwd'] = $message;
			$reordered_messages[$messages[$i + 1]['CTMSGID']] = $messages[$i + 1];
			++$i;
		}
		$reordered_messages[$message['CTMSGID']] = $message;
	}
	$messages = $reordered_messages;
	if ($am_withu) {
		db_update('ticketseen', 'REPLACE INTO ticketseen SET TICKETID = '.$ticketid.', STAMP = '.CURRENTSTAMP);
		if ($ticket['MSGMOVEDFROM']) {
			$userid =
				$ticket['MSGMOVEDFROM'] > FORCE_NON_INLINE_IF_BIGGER
			?	db_single('directmessage','SELECT TO_USERID FROM directmessage WHERE MESSAGEID = '.$ticket['MSGMOVEDFROM'])
			:	$ticket['MSGMOVEDFROM'];

			layout_open_box('white');
			?><div class="warning">Let op!</div><?
			?><div class="block">Je had oorspronkelijk een priv&eacute;bericht naar <?= get_element_link('user',$userid);
			?> gestuurd, maar deze is verplaatst naar de contact sectie opdat een willekeurige medewerker met afdoende rechten jouw bericht kan behandelen!<br /><?
			?>Probeer in de toekomst onderscheid te maken tussen werkelijke priv&eacute;erichten en berichten voor medewerkers!</div><?
			?><div class="block"><i>Bedankt!</i></div><?
			layout_close_box();
		}
		if ($ticket['ISMAIL']
		&&	$ticket['MSGCNT'] <= 2
		) {
			layout_open_box('white');
			?><div class="block"><?= __('contact:info:also_sent_via_email_TEXT',DO_UBB) ?></div><?
			layout_close_box();
		}
		if ($ticket['ELEMENT'] === 'development'
		&&  $ticket['OMSGCNT'] <= 1
		&&	!$ticket['OUTGOING']
		) {
			layout_open_box('white');
			?><div class="block"><?= __('contact_ticket:info:development_TEXT',DO_UBB) ?></div><?
			layout_close_box();
		}
	}


	if ($ticket['MAYBEID']		// ticket might be of this id, but email address was not found in known list
	&&	($srcticket = db_single_assoc('contact_ticket', 'SELECT TICKETID, MAILING FROM contact_ticket WHERE TICKETID = '.$ticket['MAYBEID']))	// does it actually exist?
	&&	!$srcticket['MAILING']
	&&	$ticket['MSGCNT'] === 1		// only first message is useful to connect to other ticket
	&&	$am_admin			// must be an admin
	) {
		layout_open_box('white');
		?><div class="warning">Bericht hoort misschien bij ander ticket</div><?
		?><p>Het bericht impliceerde behoren bij <a href="/ticket/<?= $ticket['MAYBEID'];
		?>">ticket #<?= $ticket['MAYBEID'];
		?></a>, maar het email adres in het bericht kwam niet overeen met de email adressen behorend bij dat ticket.</p><?
		?><p>Desondanks kan het zijn dat dit bericht bij bovenstaand ticket hoort. Je kunt het dan bij bovenstaand ticket invoegen.</p><?
		layout_close_box();
		?><form method="post" onsubmit="return submitForm(this)" action="/ticket/<?= $ticket['MAYBEID'] ?>/insert/<?= $ticketid ?>"><?
		?><div class="right block"><input type="submit" name="INSERT" value="<?= __('action:insert') ?>" /></div><?
		?></form><?
	}
	if ($old_ticket = db_single_assoc(['ticketaction', 'contact_ticket'], "
		SELECT TICKETID, ACTION, ELEMENT, ID
		FROM ticketaction
		JOIN contact_ticket USING (TICKETID)
		WHERE ACTION IN ('split', 'duplicate')
		  AND NEWTICKETID = $ticketid"
	)) {
		layout_open_box('white');
		?><div class="body block"><?= __(
				$old_ticket['ACTION'] === 'split'
			?	'contact:info:split_ticket_LINE'
			:	'contact:info:duplicate_ticket_LINE',
			DO_UBB,
			$old_ticket
		) ?></div><?
		layout_close_box();
	}
	if ($ticketactions = db_rowuse_array('ticketaction','
		SELECT NEWTICKETID, ACTION, ELEMENT, ID
		FROM ticketaction
		JOIN contact_ticket ON contact_ticket.TICKETID = NEWTICKETID
		WHERE ACTION IN ("split", "duplicate")
		  AND ticketaction.TICKETID = '.$ticketid
	)) {
		layout_open_box('white');
		foreach ($ticketactions as $newticket) {
			if (!may_view_element('ticket', $newticket['NEWTICKETID'])) {
				continue;
			}
			?><div class="force-link block"><?=
				__(		$newticket['ACTION'] === 'split'
					?	'contact:info:ticket_split_to_LINE'
					:	'contact:info:ticket_duplicated_to_LINE',
					DO_UBB, $newticket)
			?></div><?
		}
		layout_close_box();
	}

	layout_open_box('relative '.($contactbox = 'contact-'.($ticket['OUTGOING'] ? 'touser' : 'toadmin')),null,box::FLUID);
	layout_open_table('vtop deflist fw');

	// ELEMENT
	layout_start_row();
	echo Eelement_name('section');
	layout_field_value();
	?><b><?= $element === 'party' ? Eelement_name('agenda') : Eelement_plural_name($element) ?></b><?

	// OPENER
	$donerels = [];
	if (!$ticket['ISMAIL']
	&&	!$ticket['LASTMAIL']
) {
		layout_restart_row();
		echo __C('contactlist:with_user');
		layout_field_value();
		echo get_element_link('user', $with_userid);
		if (have_admin()) {
			search_ticket_link($with_userid);
		}
		$contactstars = new contactstars($with_userid);
		$contactstars->show_rows($with_userid);
		$donerels = $contactstars->get_relations();
	} else {
		layout_restart_row($ticket['LASTMAIL'] ? 0 : ROW_LIGHT);
		echo __C('field:with');
		layout_field_value();
		require_once '_contactmailoptions.inc';
		if (!($mail_options = get_contact_mail_options($ticketid, true))) {
			error_log('ERROR empty mail toptions at '.$_SERVER['REQUEST_URI']);
			$mail_option_count = 0;
		} else {
			$mail_option_count = count($mail_options);
		}
		if (!$mail_option_count) {
			?><span class="error">Waarschuw technische dienst (2)</span><?
			#$argh = true;
		} else {
			print_email_link($mail_options[0]['EMAIL'], $mail_options[0]['NAME'], PRINT_EMAIL_IMAGE_AFTER | PRINT_EMAIL_TICKET);
		}
		if ($with_userid) {
			layout_restart_row();
			echo __C('contactlist:with_user');
			layout_field_value();
			echo get_element_link('user',$with_userid);
			$userids[$with_userid] = $with_userid;
		}
		if ($mail_options) {
			$maybeusers = [];
			foreach ($mail_options as $mailoption) {
				if (!$mailoption['EMAIL']
				||	$mailoption['FLOCK_FORWARD']
				||	$mailoption['DIRECTION'] !== 'toadmin'
				||	$mailoption['TYPE'] === MAIL_TO
				) {
					continue;
				}
				$emails[] = '"'.addslashes($mailoption['EMAIL']).'"';
				$maybeusers += get_users_with_email($mailoption['EMAIL']);
			}
			if ($maybeusers) {
//				foreach ($maybeusers as $user) {
//					$userids[$user['USERID']] = $user['USERID'];
//				}
				$userids = array_column($maybeusers, 'USERID', 'USERID');
			}
		}
		if (!empty($userids)) {
			$contactstars = new contactstars($userids);
			$contactstars->show_combined_rows(true);
			$donerels += $contactstars->get_relations();
		}
		if (!empty($emails)) {
			if ($relations = memcached_rowuse_hash('relation','
				SELECT RELATIONID, NAME, DEAD, CONTACTEMAIL, INVOICEMAIL
				FROM relation
				WHERE REMOVED = 0
				  AND (		CONTACTEMAIL IN ('.($emailstr = implode(',',$emails)).')
					  OR	 INVOICEMAIL IN ('.$emailstr.')
				) '.
				(	isset($contactstars)
				&&	$donerels
				?	' AND RELATIONID NOT IN ('.implode(',',$donerels).')'
				:	null
				)
			)) {
				$size = count($relations);
				layout_restart_row();
				echo Eelement_name('relation', $size);
				layout_field_value();
				$first = true;
				require_once '_star.inc';
				foreach ($relations as $relationid => $relation) {
					$donerels[$relationid] = $relationid;
					?><div><?
					echo get_element_link('relation', $relationid, $relation['NAME']);
					show_dead($relation);
					?> <?= get_crown() ?> <?
					?><small class="light3"><?
					print_email_link($relation['CONTACTEMAIL'] ?: $relation['INVOICEMAIL'])
					?></small><?
					?></div><?
				}
			}
		}
	}
	// IN USE BY / OWNERID

	layout_restart_row();
	echo __C('field:assigned_to');
	layout_field_value();
	[, $lock_userid, $lock_expires] = __lock_info(LOCK_TICKET, $ticketid);
	if ($ticket['OWNERID']) {
		if ($ticket['OWNERID'] === $lock_userid) {
			show_admin_first_name($lock_userid, $am_admin);
		} else {
			show_admin_first_name($ticket['OWNERID'], $am_admin);
			if ($lock_userid) {
				?> <small>(<?= __('attrib:temporarily') ?>: <?
				show_admin_first_name($lock_userid, $am_admin);
				?>, <?=
				__('lock:expires_in', ['TIMELEFT' => get_locktime_left($lock_expires)]);
				?>)</small><?
			}
		}
	} elseif (
		$lock_userid					// need locker
	&&	$lock_expires >= CURRENTSTAMP	// and active lock
	) {
		echo escape_specials(get_admin_first_name($lock_userid));
		if ($am_admin) {
			?> <small class="light"><?= get_element_link('user',$lock_userid) ?></small><?
		}
		?> (<?= __('lock:expires_in', ['TIMELEFT' => get_locktime_left($lock_expires)]) ?>)<?
	} else {
		echo __('contact_ticket:info:not_yet_assigned');
	}

	layout_restart_row();
	echo Eelement_name('status');
	layout_field_value('ticket-'.$ticket['STATUS']);
	echo __('status:'.$ticket['STATUS']);
	if ($ticket['STATUS'] === 'closed') {
		if ($ticket['CLOSETYPE'] === 'auto') {
			?> (<?= __('attrib:automatic') ?>)<?
		} elseif ($ticket['CLOSETYPE'] === 'nomsg') {
			?> (<?= __('attrib:without_message') ?>)<?
		}
	}
	if ($am_admin)  {
		if ($ticket['STATUS'] === 'pending') {
			layout_restart_row();
			echo __C('attrib:back');
			layout_field_value();
			echo _datedaytime_display($ticket['EXPSTAMP']);
			[$y, $m, $d] = _getdate($ticket['EXPSTAMP']);
			$returnstamp = mktime(0, 0, 0, $m, $d, $y);
			?> (<?
			switch ($days = floor(($returnstamp - TODAYSTAMP) / ONE_DAY)) {
			case -1:
			case 0: echo __('date:today'); break;
			case 1: echo __('date:tomorrow'); break;
			case 2: echo __('date:dayaftertomorrow'); break;
			default: echo __('duration:x_days', ['DAYS' => $days]);
			}
			?>)<?
		}
	}
	$conns = get_connections('contact_ticket', $ticketid);
	$conns[$ticket['ELEMENT']][$ticket['ID']] = $ticket['ID'];
	if ($conns) {
		$connparts = [];
		$conncnt = 0;
		require_once '_elementshort.inc';
		foreach ($conns as $tmpelement => $tmpids) {
			foreach ($tmpids as $tmpid) {
				if (!$tmpid) {
					continue;
				}
				++$conncnt;
				[$link, $content] = _element_display_link($tmpelement, $tmpid, include_link: true);

				ob_start();
				echo $content;

				if ($am_admin
				&&	$tmpelement === 'directmessage'
				) {
					require_once '_anonymize.inc';
					show_anonymous_part($tmpid);
				}

				$connparts[] = ob_get_clean();
			}
		}
		if ($connparts) {
			layout_restart_row();
			echo Eelement_name('connection', $conncnt);
			layout_field_value();
			?><div class="connectpart"><?= implode('</div><div class="connectpart">', $connparts)
			?></div><?
		}
	}

	if ($am_admin
	&&	(	($subject = escape_specials($ticket['SUBJECT']))
		||	$locked
		&&	!$conns
		)
	) {
		layout_restart_row();
		echo Eelement_name('subject');
		layout_field_value();
		if (!$locked) {
			echo $subject;
		} else {
			?><input<?
			?> type="text"<?
			?> size="30"<?
			?> data-orig="<?= $subject ?>"<?
			?> value="<?= $subject ?>"<?
			?> onkeyup="change_subject(event,this,<?= $ticketid ?>)"><?
		}
	}

	if ($am_admin
	&&	$id
	&&	(	($user_elem = $element === 'helpdesk' || $element === 'user')
		||	(require_once '_offense_access.inc')
		&&	is_offense_element($element)
		)
	) {
		$offenseids = db_simple_hash('offense','
			SELECT '.($user_elem ? 'COUNT(*), MAX(OFFENSEID), MAX(CSTAMP)' : 'OFFENSEID, CSTAMP').'
			FROM offense
			WHERE '.($user_elem
			?	'USERID = '.$id
			:	'ELEMENT IN ("'.$element.'") AND ID='.$id
			)
		);
		if ($offenseids) {
			if ($user_elem) {
				[$cnt, $info] = keyval($offenseids);
				[$offenseid, $last] = keyval($info);
			} else {
				$cnt = count($offenseids);
			}
			if ($cnt) {
				layout_restart_row();
				echo Eelement_name('offense',$cnt);
				layout_field_value();
				if ($user_elem) {
					?><a href="/offense/user/<?= $id ?>"><?= $cnt ?></a><?
					?>, <?= __('field:last') ?>: <a href="/offense/<?= $offenseid ?>"><? _date_display($last) ?></a><?
				} else {
					$first = true;
					foreach ($offenseids as $offenseid => $cstamp) {
						if ($first) {
							$first = false;
						} else {
							?>, <?
						}
						?><a href="/offense/<?= $offenseid ?>"><? _date_display($cstamp) ?></a><?
					}
				}
			}
		}
	}
	layout_stop_row();
	layout_close_table();

	// ORIGINAL
	if ($ticket['ORG_ELEMENT']
	&&	(	$ticket['ORG_ELEMENT'] !== $element
		||	$ticket['ORG_ID'] !== $id
		)
	) {
		layout_open_table('vtop deflist small light');
		layout_start_row();
		echo __C('field:original');
		layout_field_value();
		?><b><?= ucfirst($ticket['ORG_ELEMENT'] === 'party' ? element_name('agenda') : element_plural_name($ticket['ORG_ELEMENT'])); ?></b><?
		if ($ticket['ORG_ID']) {
			layout_restart_row();
			[$link, $content] = _element_display_link($ticket['ORG_ELEMENT'],$ticket['ORG_ID']);
			if ($link) {
				?><a target="_blank" href="<?= $link; ?>"><?= Eelement_name('element') ?></a><?
			} else {
				echo Eelement_name('element');
			}
			layout_field_value();
			if (isset(USE_URLTITLE[$ticket['ORG_ELEMENT']])) {
				?><div><?
				echo get_element_link($ticket['ORG_ELEMENT'], $ticket['ORG_ID']);
				if ($ticket['ELEMENT'] === 'contest'
				&&	$ticket['ORG_ELEMENT'] === 'party'
				&&	(require_once '_contests.inc')
				&&	($contests = get_contests('party', $ticket['ORG_ID']))
				) {
					show_wins($contests, $ticket['ORG_ID']);
				}
				?></div><?
			} elseif ($content) {
				echo $content;
			}
		}
		layout_close_table();
	}
	layout_stop_row();
	layout_close_table();
	layout_close_box();

	ob_start();

	$show_last_here = $am_admin && $with_userid && isset($lstamp_touser);

	$hidden_last_here = $show_last_here && $ticket['STATUS'] === 'open';

	if ($hidden_last_here) {
		?><div class="r unhideanchor" onclick="hide(this); unhide('lasthereinfo');"><?=
			escape_specials(memcached_nick($with_userid)) ?> <?= __('field:last_here') ?>? <?
		?></div><?
	}

 	?><div id="correspondence" class="block"><?= Eelement_name('correspondence'); ?>:</div><?

	if ($show_last_here) {
		$lasthere = db_single('user_data','SELECT LAST_USED FROM user_data WHERE USERID='.$with_userid);
		if ($lasthere) {
			$lastseen =  db_single('ticketseen','SELECT STAMP FROM ticketseen WHERE TICKETID='.$ticketid);
			if (!$lastseen && $lstamp_touser < 1311067428) {
				$lastseen = false;
			}

			layout_open_box($hidden_last_here ? 'hidden grey' : 'grey','lasthereinfo');
			?><div class="block"><?
				?><table class="dens r"><?
				?><tr><td><?= __('field:last_online') ?>:</td><td class="right lpad"><? _datedaytime_display($lasthere) ?></td></tr><?
				if ($lastseen) {
					?><tr><td><?= __('field:last_seen') ?>:</td><td class="right lpad"><? _datedaytime_display($lastseen) ?></td></tr><?
				}
				?></table><?
			echo __(
				$lasthere > $lstamp_touser
			?	'contact:info:user_has_been_online_LINE'
			:	'contact:info:user_has_not_been_online_LINE',
				DO_UBB,
				array('USERID'=>$with_userid)
			);
			if ($lastseen !== false
			&&	isset($lstamp_touser)
			) {
				?><br /><?
				echo __(
					$lastseen >= $lstamp_touser
				?	'contact:info:message_has_been_seen_LINE'
				:	'contact:info:message_has_not_been_seen_LINE',
					DO_UBB
				);
			}
			if ($ticket['STATUS'] !== 'open') {
				?><br /><?
				echo __($ticket['READM']
				?	'contact:info:message_has_been_read_LINE'
				:	'contact:info:message_has_not_been_read_LINE',
					DO_UBB
				);
			}
			?></div><?
			layout_close_box();
		}
	}

	if ($messages) {
		if ($attinfo = memcached_multirowuse_hash(['contact_ticket_attachment', 'contact_ticket_attachment_meta'], '
			SELECT CTMSGID, CONTACT_TICKET_ATTACHMENTID, MIMETYPE, FILENAME, SIZE, DATAID, CONTENTID
			FROM contact_ticket_attachment
			JOIN contact_ticket_attachment_meta USING (DATAID)
			WHERE CTMSGID IN ('.implodekeys(', ', $messages).')
			ORDER BY FILENAME LIKE "message.%" DESC, FILENAME')
		) {
			if (preg_match('"\b(?:Mac\s*OS|Macintosh)\b"', USER_AGENT)) {
				foreach ($attinfo as $ctmsgid => $atts) {
					foreach ($atts as $ndx => $att) {
						if ($att['MIMETYPE'] === 'multipart/appledouble') {
							unset($attinfo[$ctmsgid][$ndx]);
						}
					}
				}
			}
		} else {
			$attinfo = [];
		}
	}
	$msgcnt = count($messages);

	# NOTE: Determine reecommended email for VIA_EMAIL_TOO option.
	#		Determine first 'to admin' ticket to place users with identical email.
	#		If no 'to admin' message exists, then place on first 'to user' message.

	$recommended_email = null;
	$have_to_admin = null;
	$have_to_user  = null;

	foreach ($messages as $ctmsgid => $message) {
		switch ($message['DIRECTION']) {
		case 'toadmin':
			$recommended_mail ??= $message['FROM_EMAIL'] ?? null;
			if (($have_to_admin ??= $ctmsgid)
			&&	  $have_to_user
			) {
				break 2;
			}
			break;
		case 'touser':
			$recommended_mail ??= $message['TO_EMAIL'] ?? null;
			if (($have_to_user ??= $ctmsgid)
			&&	 $have_to_admin
			) {
				break 2;
			}
			break;
		}
	}

	if (null !== ($ctmsgid = $have_to_admin)
	||	null !== ($ctmsgid = $have_to_user)
	) {
		$messages[$ctmsgid]['SHOW_IDENTICAL_EMAIL_USERS'] = true;
	}

	static $__shown_junk_options = false;

	?><div><?

	foreach ($messages as $message) {
		extract($message, \EXTR_OVERWRITE);

		static $i = 0;

		if ($utf8
		=	$BODYUTF8
		||	detect_utf8($BODY)
		) {
			if ($BODYUTF8) {
				$BODY = $BODYUTF8;
			}
			$BODY = clean_utf8($BODY);
			$utf8_mod = 'u';
		} else {
			$utf8_mod = '';
		}

		$FROM_NAME = str_replace('\\', '', $FROM_NAME);

		if ((	!$DIRECTION
			||	 $DIRECTION === 'toadminfromadmin'
			)
		&&	!$am_admin
		) {
			// don't show toadminfromadmin messages
			// to other than admins
			continue;
		}
		if ($CIMID) {
			$failmail = memcached_single_assoc('contact_undeliverable','
				SELECT FAILMAIL, FAILREASON
				FROM contact_undeliverable
				WHERE CIMID = '.$CIMID,
				ONE_HOUR
			);
			layout_open_box('contact-failure');
			?><div class="block"
			?><p><b>Probleem bij verzenden van bericht naar <?= $failmail ? print_email_link($failmail['FAILMAIL']) : 'iemand';
			?></b><br /><small class="light"><? _datedaytime_display($CSTAMP);
			?></small><br />Reden: <?= $failmail ? escape_specials($failmail['FAILREASON']) : 'onbekend';
			?></p><?
			if ($am_admin
			&&	$locked
			&&	$failmail
			&&	0 === strncmp($failmail['FAILREASON'],'454 ',4)
			) {
				?><p><span class="underline"><a href="/ticket/<?= $ticketid;
				?>/forward?USERID=2269">Stuur door naar <?= escape_specials(memcached_nick('2269'));
				?></a></span>, dit is een probleem van de ontvangende mailserver, maar kan omzeild worden!<?
			}
			?><p><i><a href="/contact/undeliverable/<?= $CIMID ?>"><?= Eelement_name('entire_message') ?></a></i></p><?
			layout_close_box();
			continue;
		}

		$show_junk_options =
			$ISMAIL
		&&	!$__shown_junk_options
		&&	(	$SPAMSCORE >= 30
			||	$PRECEDENCE === 'bulk'
			||	$AUTOGEN
			&&	$msgcnt <= 1
			||	$msgcnt === 1
			&&	preg_match('"^\s*https?://\S+\s*$"i'.$utf8_mod, $BODY)
			)
		&&	empty($nospam);

		if (!$__shown_junk_options
		&&	$show_junk_options
		) {
			$__shown_junk_options = true;
			require_once '_junk.inc';
			show_junk_information($ticketid, $noverview);
		}

		if ($message['TEMPLATE'] === 'advertising_info'
		&&	preg_match('"[\r\n]([a-z]{8})[\r\n]"m'.$utf8_mod, $BODY, $match)
		&&	($useinfo = db_single_array('promoinfopassword','
				SELECT LAST_USED,LAST_USERID,HITS
				FROM promoinfopassword
				WHERE PASSWD="'.addslashes($match[1]).'"
				  AND LAST_USED BETWEEN '.$message['CSTAMP'].' AND '.($message['CSTAMP'] + (TMPWD_MAX_DAYS + 1) * ONE_DAY))
			)
		) {
			[$last_used, $last_userid, $hits] = $useinfo;
			layout_open_box('white');
			layout_open_table(TABLE_CLEAN);

			layout_start_row();
			echo Eelement_name('password');
			layout_field_value();
			?><b><?= $match[1] ?></b><?

			layout_restart_row();
			echo __C('field:last_used');
			layout_field_value();
			_datedaytime_display($last_used);

			layout_restart_row();
			echo __C('field:by');
			layout_field_value();
			echo $last_userid ? get_element_link('user',$last_userid) : element_name('non_user');

			if ($hits) {
				layout_restart_row();
				echo Eelement_plural_name('hit');
				layout_field_value();
				echo $hits;
			}
			layout_stop_row();

			layout_close_table();
			layout_close_box();
		}

		# try replacing cid: content images
		if ($message['FORWARD'] === 'forwardmsg') {
			$checkatts = [];
		} else {
			$checkatts = $attinfo[$CTMSGID] ?? [];
			if (isset($message['fwd'])
			&& ($moreatts = $attinfo[$message['fwd']['CTMSGID']])
			) {
				$checkatts += $moreatts;
			}
		}
		if ($checkatts) {
			$srcstr = $dststr = null;
			$okatts = [];
			foreach ($checkatts as $att) {
				$okatts[] = $att;
				if (!$att['CONTENTID']) {
					continue;
				}
				$srcstr[] = 'cid:'.$att['CONTENTID'];
				$dststr[] = 'https://'.$_SERVER['HTTP_HOST'].'/attachment/'.$att['CONTACT_TICKET_ATTACHMENTID'].'/'.rawurlencode($att['FILENAME']);
			}
			$checkatts = $okatts;

			if ($srcstr) {
				$BODY = str_replace($srcstr, $dststr, $BODY);
			}
		}
		if ($utf8) {
			$message['BODYUTF8'] = $BODY;
		}
		[$BODY, $quotedmessage, $quotedlines, $utf8, $dear] = split_message($message, $attinfo);
		$body_mod = $utf8 ? 'u' : '';
		if (have_admin()
		&&	(	$DIRECTION === 'toadmin'
			||	$DIRECTION === 'forwardinfo'
			)
		&&	preg_match($find = '"\b(win[\s\-]*acties?|verloten|gastenlijst(?:plek(?:ken)?|plaats(?:en)?)|vrijkaart(?:en)?)\b"i',$BODY,$match)
		) {
			$terms = [];
			$BODY = preg_replace_callback($find,function($match) use (&$terms) {
				$terms[] = $match[0];
				return '[notice]'.$match[0].'[/notice]';
			},$BODY);

			layout_open_box('green');
			?><div class="block"><?= __('contact_ticket:info:maybe_contest_note_TEXT', DO_UBB | DO_NL2BR, ['TERMS' => implode(', ', $terms)])
			?></div><?
			layout_close_box();
		}

		layout_open_box(
			'forcewrap '.(
				$DIRECTION === 'forwardinfo'
			? 	'purple'
			: 	'contact-'.$DIRECTION.($AUTOGEN ? ' light' : null)
			)
		);

		if ($DIRECTION === 'forwardinfo') {
			?><div class="r"><i><?= element_name('forwarded_message') ?></i></div><?
		}
		?><div class="block"><?
		if ($ticket['ISMAIL']
		&&	$DIRECTION === 'toadmin'
		) {
			$extraclass = null;
			if ($PRECEDENCE === 'bulk') {
				?><div class="r clear warning-nb block"><?= element_name('bulk') ?></div><?
			} elseif ($AUTOGEN) {
				?><div class="r clear warning-nb block"><?= element_name('automatic_response') ?></div><?
			} elseif (
				$HEADERS
			#	^^ without headers is prolly ticket via site
			&&	(	$FROM_EMAIL && !$TO_EMAIL && !$USERID_FROM
				||	  $TO_EMAIL &&  $TO_EMAIL === $FROM_EMAIL
				)
			) {
				?><div class="r clear warning-nb block"><?= element_name('mailing') ?></div><?
			}

			if ($SPAMSCORE) {
				include_style('votes');
				?><div class="text-<?
				if ($SPAMSCORE >= 70) {
					?>ultra-red<?
				} elseif ($SPAMSCORE >= 60) {
					?>super-red<?
				} elseif ($SPAMSCORE >= 50) {
					?>red<?
				} elseif ($SPAMSCORE >= 35) {
					?>orange-red<?
				} elseif ($SPAMSCORE >= 25) {
					?>orange<?
				} elseif ($SPAMSCORE >= 10) {
					?>orange-green<?
				} elseif ($SPAMSCORE > -10 && $SPAMSCORE < 10) {
					?>green<?
				} elseif ($SPAMSCORE <= 30) {
					?>ultra-green<?
				} elseif ($SPAMSCORE <= 10) {
					?>super-green<?
				}
				?> nb r clear block"><?= element_name('spam_score') ?>: <?= $SPAMSCORE ?></div><?
			}
		}
		if ($am_admin
		&&	$DIRECTION === 'toadmin'
		) {
			?><div class="clear r block"><?
			if ($locked
			&&	($unsub = $message['UNSUBSCRIBE'])
			) {
				foreach (explode(',', $unsub) as $part) {
					$part = mytrim($part, '<>');
					if (!str_starts_with($part, 'http')) {
						continue;
					}
					?><span<?
					if (preg_match('"^https?://(?P<host>[^/]+)\b"', $part, $match)) {
						?> title="@ <?= escape_specials($match['host']) ?>"<?
					}
					?> class="small light"><?
					?><a<?
					?> target="_blank"<?
					?> href="<?= escape_specials($part) ?>"><?= __('action:unsubscribe_from_mailinglist') ?></a><?
					?> <?= MIDDLE_DOT_ENTITY ?> </span><?
				}
			}
			if (!isset($bubble)) {
				require_once '_bubble.inc';
				$bubble = new bubble(CLICK_BUBBLE);
			} else {
				$bubble->flags(CLICK_BUBBLE);
			}
			if ($locked
			&&	$message['CTMSGID'] !== $firstmessage['CTMSGID']
			) {
				$bubble->catcher();
				?><span class="small light"><?= __('action:split') ?></span><?
				$bubble->content();
				?><form style="margin: .5em;" target="_blank" onsubmit="setTimeout('location.href=&quot;/ticket/<?= $ticketid ?>?NOMEMCACHE&quot;',1000);return submitForm(this)" method="post" action="/ticket/<?= $ticketid; ?>/split/<?= $CTMSGID; ?>"><?
				?><div class="block"><span class="warning"><?= __C('warningheader') ?></span><br /><?= __('contact_ticket:info:you_sure_you_want_to_split_LINE') ?></div><?
				?><div class="right block"><?
				?><label class="bold-hilited"><?= __('contact_ticket:checkbox:close_old_ticket') ?> <?
				?><input type="checkbox" value="1" name="CLOSEOLD" checked="checked" onclick="cbclickp(this)" /></label><br /><?
				?><input type="submit" value="<?= __('action:yes_split') ?>" /><?
				?></div><?
				?></form><?
				$bubble->display();
				?><span class="small light"> <?= MIDDLE_DOT_ENTITY ?> </span><?
			}
			$bubble->catcher();
			?><span class="small light"><?= __('action:duplicate') ?></span><?
			$bubble->content();
			?><form style="margin:.5em" target="_blank" method="post" action="/ticket/<?= $ticketid; ?>/duplicate/<?= $CTMSGID; ?>" onsubmit="return submitForm(this)"><?
			?><div class="block"><span class="warning"><?= __C('warningheader'); ?></span><br /><?= __('contact_ticket:info:you_sure_you_want_to_duplicate_LINE') ?></div><?
			?><div class="right block"><?
			?><label class="bold-hilited"><?= __('contact_ticket:checkbox:use_only_this_message') ?> <?
			?><input type="checkbox" value="1" name="ONLYTHISMESSAGE" onclick="cbclickp(this)" checked="checked" /></label><br /><?
			?><input type="submit" value="<?= __('action:yes_duplicate') ?>" /><?
			?></div><?
			?></form><?
			$bubble->display();
			?></div><?
		}
		if ($DIRECTION === 'touser'
		||	$DIRECTION === 'toadmin'
		) {
			$flock = $mail = null;
			if ($DIRECTION === 'touser') {
				if ($with_userid) {
					ob_start();
					?><a href="<?= get_element_href('user',$with_userid) ?>" title="&rarr; <?= memcached_nick($with_userid) ?>"><?
					?><img class="presence" src="<?= get_favicon() ?>" /><?
					?></a> <?
					$flock = ob_get_clean();
				}
				if ($TO_EMAIL) {
					ob_start();
					?><img title="&rarr; <?= _make_illegible($TO_EMAIL) ?>" class="help-cursor mail" src="<?= STATIC_HOST; ?>/images/mail<?= is_high_res() ?>.png" /> <?
					$mail = ob_get_clean();
				}
			} elseif ($DIRECTION === 'toadmin') {
				if ($with_userid) {
					ob_start();
					?><a href="<?= get_element_href('user',$with_userid) ?>" title="<?= memcached_nick($with_userid) ?> &rarr;"><?
					?><img class="presence" src="<?= get_favicon() ?>" /><?
					?></a> <?
					$flock = ob_get_clean();
				}
				if ($FROM_EMAIL) {
					ob_start();
					?> <a href="/ticket/outgoing-form?TO_EMAIL=<?= $FROM_EMAIL; if ($with_userid) echo ';TO_USERID='.$with_userid ?>"><?
					?><img title="<?= escape_specials($FROM_EMAIL) ?> &rarr;" class="mail" src="<?= STATIC_HOST ?>/images/mail<?= is_high_res() ?>.png" /><?
					?></a> <?
					$mail = ob_get_clean();
				}
			}
			echo $mail, $flock;
		}

		if ($USERID_FROM) {
			?><b><?
			if ($DIRECTION === 'toadmin') {
				echo get_element_link('user', $USERID_FROM);
			} else {
				show_admin_first_name($USERID_FROM, $am_admin);
			}
			?></b><?
		} elseif ($FROM_EMAIL) {
			ob_start();
			print_email_link(
				$FROM_EMAIL,
				$FROM_NAME,
				PRINT_EMAIL_TICKET | PRINT_EMAIL_SHOW_EMAIL | PRINT_EMAIL_NO_NAME,
				userid: $with_userid
			);
			$email_link = ob_get_clean();
			if (!$USERID_FROM) {
				?><b><?= $email_link ?></b><?
			} else {
				?>, <span class="small light7"><?= $email_link ?></span><?
			}
		}

		if ($SUBJECT) {
			require_once '_contactsubject.inc';
			?>: <?
			if (isset($SUBJECT[1])
			&&	$SUBJECT[0] === '='
			&&	$SUBJECT[1] === '?'
			) {
				require_once '_mimeheader.inc';
				$SUBJECT = decode_quoted_printable_header($SUBJECT, $made_utf8);
			}
			echo html_entity_decode(escape_utf8(mytrim(str_replace('_',' ',strip_ticket_subject($SUBJECT, utf8: true)), utf8: true)));
		}
		if ($OUTGOING) {
			ob_start();
			print_email_link($TO_EMAIL, $TO_NAME, PRINT_EMAIL_TICKET);
			$email_link = ob_get_clean();
			?><small class="light">, <?= str_replace('%LINK%', $email_link, __('contact:info:sent_to_LINE', KEEP_EMPTY_KEYWORDS)) ?></small><?
		}
		if ($HEADERS) {
			require_once '_bubble.inc';
			?> <small class="light">(<?
			if (!isset($bubble)) {
				$bubble = new bubble();
			}
			$bubble->catcher('headers');
			$bubble->content();
			$headers = preg_replace('"\b([,.])\b"u', '$1'.SOFT_HYPHEN_ENTITY, escape_utf8($HEADERS));
			?><small><?= nl2br($headers) ?></small><?
			$bubble->display();
			?>)</small><?
		}

		switch ($DIRECTION) {
		case 'action':
			?> (<?= element_name('action') ?>)<?
			break;
		case 'toadminfromadmin':
			?> (<?= element_name('comment') ?>)<?
			break;
		}

		if (($other_email = ($DIRECTION === 'toadmin' ? $FROM_EMAIL : null))
		||	($other_email = ($DIRECTION === 'touser'  ? $TO_EMAIL   : null))
		) {
			if (!empty($message['SHOW_IDENTICAL_EMAIL_USERS'])
			&&	($maybeusers = get_users_with_email($other_email))
			) {
				static $__done_maybe;
				if (!isset($__done_maybe[$other_email])) {
					$__done_maybe[$other_email] = true;
					?><br /><small><span class="light"><?= __('answer:maybe') ?> <?= element_name('user') ?>:</span> <?
					$maybe = reset($maybeusers);
					while (true) {
						$next = next($maybeusers);
						echo get_element_link('user', $maybe['USERID'], $maybe['NICK']);
						if ($maybe['STATUS'] !== 'active') {
							?> <?
							show_status_name($maybe['STATUS'], SSNFLG_LIGHT | SSNFLG_BRACKETS);
						}
						if ($maybe = $next) {
							?>, <?
						} else {
							break;
						}
					}
					?></small><?
				}
			}
			if ($TO_EMAIL || $TO_NAME) {
				?><br /><small>&nbsp;&rarr; <?
				if (str_contains($TO_NAME, 'undisclosed-recipients')) {
					echo element_name('mailing_list');
				} else {
					print_email_link($TO_EMAIL, null, PRINT_EMAIL_TICKET);
				}
				?></small><?
			}
			# find other TO and CC

			if ($HEADERS) {
				if (preg_match('"\nTo:(.*?)\n"si', $HEADERS, $match)) {
					foreach (explode(',',$match[1]) as $to) {
						$email = $name = null;
						if (preg_match('"<(.*?)>"',$to,$mailmatch)) {
							$email = $mailmatch[1];
							$name = str_replace('<'.$email.'>','',$to);
							$email = mytrim($email);
							$name = mytrim($name);
						} elseif (match_email($match[1])) {
							$email = $match[1];
						} else {
							continue;
						}
						if ($email === $TO_EMAIL) {
							continue;
						}
						?><br /><small>&nbsp;&rarr; <?
						print_email_link($email,$name, PRINT_EMAIL_TICKET | PRINT_EMAIL_SHOW_EMAIL);
						?></small><?
					}
				}
				if (preg_match('"\nCC:(.*?)\n"si', $HEADERS, $match)) {
					foreach (explode(',',$match[1]) as $cc) {
						$email = $name = null;
						if (preg_match('"<(.*?)>"',$cc,$mailmatch)) {
							$email = $mailmatch[1];
							$name = str_replace('<'.$email.'>','',$cc);
							$email = mytrim($email);
							$name = mytrim($name);
						} elseif (match_email($match[1])) {
							$email = $match[1];
						} else {
							continue;
						}
						?><br /><small>&nbsp;cc&nbsp;&rarr; <?
						print_email_link($email, $name, PRINT_EMAIL_TICKET | PRINT_EMAIL_SHOW_EMAIL);
						?></small><?
					}
				}
			}
		}
		?><br /><?
		?><small class="light"><? _datedaytime_display($CSTAMP) ?></small></div><?

		if (memcached_single('contact_ticket_attachment','
			SELECT 1
			FROM contact_ticket_attachment
			WHERE FILENAME = "message.html"
			AND CTMSGID = '.$CTMSGID)
		) {
			static $__htmli = 0;

			$iframe_id = 'html_message'.(++$__htmli);

			?><script><?
			if ($__htmli === 1) {
				?>
				const Pf_html_messages = [];
				Pf_html_message_resizer = function() {
					for (const id of Pf_html_messages) {
						/** @type {HTMLIFrameElement} | null */
						const o = document.getElementById(id);
						if (!o) {
							continue;
						}
						o.style.height = o.contentWindow.document.documentElement.scrollHeight + 'px';
					}
				};
				addreadyevent(Pf_html_message_resizer);
				addevent(window, 'resize', Pf_html_message_resizer);
				<?
			}
			?>
			Pf_html_messages.push('<?= $iframe_id ?>');
			</script><?

			?><button class="r" onclick="
				unhide(this.nextSibling);
				this.nextSibling.src = this.nextSibling.getAttribute('data-src');
				hide(this);
			"><?= __C('action:show_html') ?></button><?
			?><iframe<?
			?> class="hidden ib"<?
			?> id="<?= $iframe_id ?>"<?
			?> onload="Pf_html_message_resizer()"<?
			?> data-src="/include/contacttickethtml.inc?CTMSGID=<?= $CTMSGID ?>"<?
			?> style="background-color: white; width: 100%;"></iframe><?
		}

		?><div class="body block"><?
		if ($dear) {
			?><div class="hidden" id="dearmessage_<?= $CTMSGID ?>"><?
			?><div class="block"><?=
				make_all_html(
					mytrim(
						_ubb_preprocess($dear, $utf8),
						null,
						$utf8
					),
					UBB_ALL |  ($utf8 ? UBB_UTF8 : 0)
				)
			?></div><?
			?><hr class="slim"><?
			?></div><?
		}
		if ($ISMAIL
		&&	$MIMETYPE === 'text/html'
		) {
			?><div id="ubbed"<?
				?> onclick="hideobj(this); unhide('sourcehtml');"<?
				?> class="small r unhideanchor"<?
			?>><?= __('action:view_html') ?></div><?

			?><div id="sourcehtml" class="hidden"><?
				?><div onclick="hideobj(this.parentNode); unhide('ubbed');"<?
					?> class="small r unhideanchor"<?
				?>><?= __('action:view_text') ?></div><?=
				preg_replace('"(<(?:meta|body)[^>]*>|<(?:style|head)>.*?</(?:style|head)>)"ism'.$body_mod, '', $BODY)
			?></div><?

			$BODY = preg_replace('/([<>]{3,})/'.$body_mod,'**',$BODY);
			if (false !== stripos($BODY, '<br')) {
				// catch BR
				$BODY = str_replace("\n", '', $BODY);
				$BODY = preg_replace('/<br\s*\/?>/i'.$body_mod, "\n", $BODY);
			}
			if (false !== stripos($BODY, '<a ')) {
				if (null !== ($new_BODY = preg_replace(
					$regex = '!<a\s*.*?href="mailto:([^"]*)"[^>]*>(.*?)</a>!i.$body_mod',
					'[email name=\\2]\\1[/email]',
					$BODY))
				) {
					$BODY = $new_BODY;
				} else {
					preg_failure($BODY, $regex);
				}
				if (null !== ($new_BODY = preg_replace_callback(
					$regex = '!<a\s*.*?href="(?<url>[^"]*)"[^>]*>(?<display></display>.*?)</a>!i'.$body_mod,
					static fn(array $match): string =>
						is_safe_url($match['url']) ? "[url={$match['url']}]{$match['display']}[/url]" : $match['display'],
					$BODY))
				) {
					$BODY = $new_BODY;
				} else {
					preg_failure($BODY, $regex);
				}
			}
			echo html_entity_decode(
				preg_replace(
					'"\x1E(\d+)"', '&#$1;',
					make_all_html(
						mytrim(
							_ubb_preprocess(
								strip_tags(
									preg_replace(
										[	'!(</(?:div|p)>|<br\s*/?>)!i'.$body_mod,
											'!&#(\d+);!'.$body_mod,
											'!<(b|i|small)>(.*?)</\\1>!i'.$body_mod,
											'!<style[^>]*>.*?</style>!'.$body_mod,
											'!<img .*?src="([^"]+)"[^>]*>!i'.$body_mod],
										[	"\n",
											"\x1E\$1",
											'[$1]$2[/$1]',
											'',
											'[img]$1[/img]',
										],	$BODY
									)
								)
							),
							null,
							$utf8
						),
						UBB_ALL | ($utf8 ? UBB_UTF8 : 0)
					)
				)
			);
			?></div><?
		}

		if ($utf8) {
			require_once '_unicode.inc';
			$BODY = mb_convert_encoding($BODY, 'UTF-8', 'UTF-8');
			$BODY = str_replace([REPLACEMENT_CHARACTER, OBJECT_REPLACEMENT_CHARACTER], ['', ''], $BODY);
		}

		# combinne 2+ newlines into max 2 newlines
		$BODY = preg_replace(['"\n{2,}"s'.$body_mod, '"\s+$"s'.$body_mod], ["\n\n", ''], $BODY);

		echo make_all_html(
			mytrim(
				_ubb_preprocess($BODY, $utf8),
				null,
				$utf8
			),
			UBB_ALL | ($utf8 ? UBB_UTF8 : 0)
		);
		?></div><?
		if (have_super_admin()
		&&	!$am_withu
		&&	!$DIRECTION
		) {
			?><div class="block"><?
			?><label for="toadmin<?= $CTMSGID ?>" class="yellow"><?
				?><input id="toadmin<?= $CTMSGID ?>" type="radio" name="DIRECTION[<?= $CTMSGID ?>]" value="toadmin" /> toadmin<?
			?></label> <?= MIDDLE_DOT_ENTITY ?> <label for="touser<?= $CTMSGID ?>" class="white"><?
				?><input id="touser<?= $CTMSGID ?>" type="radio" name="DIRECTION[<?= $CTMSGID ?>]" value="touser" /> touser<?
			?></label> <?= MIDDLE_DOT_ENTITY ?> <label for="toadminfromadmin<?= $CTMSGID ?>" class="blue"><?
				?><input id="toadminfromadmin<?= $CTMSGID ?>" type="radio" name="DIRECTION[<?= $CTMSGID ?>]" value="toadminfromadmin" /> toadminfromadmin<?
			?></label><?
			?></div><?
		}
		if ($quotedlines) {
			?><div class="hidden" id="quotedmessage_<?= $CTMSGID ?>"><?
			?><hr class="slim"><?
			?><div class="block"><?=
				make_all_html(
					_ubb_preprocess(
						mytrim(	$quotedmessage, null, $utf8),
						$utf8
					),
					$utf8 ? UBB_UTF8 : 0
				)
			?></div></div><?
		}

		if ($have_att = $checkatts) {
			$att = reset($checkatts);
			if ($att['FILENAME'] === 'message.html'
			&&	$message['HTMLSRC']
			) {
//				$att = $checkatts[0];
				?><div class="abs small light" style="right:0;margin-top:-2em"><?
				?><a target="_blank" href="/attachment/<?= $att['CONTACT_TICKET_ATTACHMENTID'];
				?>/<?= escape_utf8($att['FILENAME']);
				?>"><?= __('contact_ticket:info:first_only_html_LINE') ?></a><?
				?></div><?
			}
			?><div class="light hr"></div><?
		}
		if ($quotedlines
		||	$dear
		) {
			?><div<?
			?> class="light showquote unhideanchor"<?
			?> onclick="hideobj(this); setdisplay(['quotedmessage_<?= $CTMSGID ?>','dearmessage_<?= $CTMSGID ?>'], true, true)"><?=
				__('action:show_x_hidden_lines', ['X' => $quotedlines, 'DEAR' => (bool)$dear])
			?></div><?
		}
		if ($have_att) {
			$cnt = count($checkatts);

			static $show_attachment = null;
			static $__atti = 1;

			$starti = $__atti;

			$dls = [];

			if ($show_attachment === null) $show_attachment = function(array $att, bool $first) use ($message, &$__atti, &$dls) {
				static $__done;
				$classes = null;
				if ($no_dl
				=	$att['CONTENTID']
				&&	$att['SIZE'] < FORCE_NON_INLINE_IF_BIGGER
				||	$att['MIMETYPE'] === 'multipart/appledouble'
				||	$att['FILENAME'] === 'message.html'
				) {
					$classes[] = 'light6';
				} else {
					$dls[] = $__atti;
				}
				if ($att['SIZE']) {
					?><a target="_blank" href="/attachment/<?= $att['CONTACT_TICKET_ATTACHMENTID'];
					?>/<?= escape_utf8($att['FILENAME']);
					?>"><?
				} else {
					$classes[] = 'line-through';
				}
				if ($classes) {
					?><span class="<?= implode(' ',$classes) ?>"><?
				}
				echo escape_utf8($att['FILENAME']);
				if ($classes) {
					?></span><?
				}
				if ($att['SIZE']) {
					?></a><?
				}
				?><small class="light"> (<?
				if ($first
				&&	$att['FILENAME'] === 'message.html'
				&&	$message['HTMLSRC']
				) {
					echo element_name('original') ?>, <?
				}
				if ($att['CONTENTID']) {
					echo __('status:inline') ?>, <?
				}
				print_human_bytes($att['SIZE']);
				if ($att['SIZE']) {
					?>, <a<?
					if (!$no_dl) {
						?> id="att-dl-<?= $__atti++ ?>"<?
					}
					?> download <?
					?> href="/attachment/<?= $att['CONTACT_TICKET_ATTACHMENTID'];
					?>/<?= escape_utf8($att['FILENAME']);
					?>?download"><?= __('action:download') ?></a><?
				}
				?>)</small><?
			};

			usort($checkatts, function (array $a, array $b): int {
				if ($a['FILENAME'] === 'message.html') {
					return 1;
				}
				if ($b['FILENAME'] === 'message.html') {
					return -1;
				}
				$a_inline = $a['CONTENTID'] && $a['SIZE'] < FORCE_NON_INLINE_IF_BIGGER ? 1 : 0;
				$b_inline = $b['CONTENTID'] && $b['SIZE'] < FORCE_NON_INLINE_IF_BIGGER ? 1 : 0;
				if ($inline_diff = $a_inline - $b_inline) {
					return $inline_diff;
				}
				return utf8_strcasecmp($a['FILENAME'], $b['FILENAME']);
			});

			$first = true;
			$have_real = null;

			$from_appic = str_contains($message['FROM_EMAIL'], '@beappic.com');

			$fakes = 0;
			$count = 0;
			ob_start();
			foreach ($checkatts as $att) {
				if ($att['CONTENTID']
				&&	$from_appic
				&&	preg_match('"^(?:image\d+|~WRD\d+).(?:gif|png|jpe?g|webp)$"', $att['FILENAME'])
				) {
					continue;
				}

				++$count;

				if ($fake
				=	$att['CONTENTID']
				&&	$att['SIZE'] < FORCE_NON_INLINE_IF_BIGGER
				||	$att['FILENAME'] === 'message.html'
				||	$att['MIMETYPE'] === 'multipart/appledouble'
				) {
					++$fakes;
				}

				$margin = false;
				if (!$fake) {
					$have_real = true;
				} elseif ($have_real) {
					$margin = true;
					$have_real = false;
				}
				?><li<?
				if ($margin) {
					?> style="margin-top:.5em"<?
				}
				?>><?
				$show_attachment($att, $first);
				$first = false;
				?></li><?
			}

			if ($data = ob_get_clean()) {
				if ($fakes === $count) {
					?><div class="ib light"><?
				}
				?><div><?
				?><i><?= Eelement_name('attachment', $cnt) ?>:</i> <?

				if ($dls) {
					?><span class="light">(<span class="unhideanchor" onclick="<?
					foreach ($dls as $i) {
						?>getobj('att-dl-<?= $i ?>').click();<?
					}
					?>"><?= __('action:download_all') ?></span>)</span><?
				}
				?></div><?
				?><ul><?= $data ?></ul><?
				if ($fakes === $count) {
					?></div><?
				}
			}
		}
		layout_close_box();
	}
	?></div><?

	$messagestr = ob_get_clean();

	if ($am_admin) {
		if (!empty($srcticket['MAILING'])) {
			layout_open_box('white');
			?><div class="block"><?= __('contact:info:belongs_to_mailing_TEXT', DO_UBB, ['TICKETID' => $srcticket['TICKETID']]) ?></div><?
			layout_close_box();
		} elseif ($ticket['MAILING']) {
			if ($bccs = db_simple_hash('mailinglist','
				SELECT EMAIL,GROUP_CONCAT(CONCAT(t.TICKETID,":",CSTAMP))
				FROM mailinglist AS m
				LEFT JOIN contact_ticket AS t ON WITHEMAIL=EMAIL AND MAYBEID='.$ticketid.'
				WHERE m.TICKETID='.$ticketid.'
				GROUP BY EMAIL')
			) {
				ksort($bccs);
				layout_open_box('white');
				?><div class="header bold"><?
				show_email_img	() ?> <?=
				Eelement_name('mailing') ?>: <?
				$cnt = count($bccs);
				if ($cnt > 10) {
					?><span onclick="swapdisplay('receivers')" class="unhideanchor"><?
				}
				echo $cnt ?> <?= element_name('receiver',$cnt);
				if ($cnt > 10) {
					?></span><?
				}
				?></div><?
				?><div class="<?
				if ($cnt > 10) {
					?>hidden <?
				}
				?>block" id="receivers"><ul><?
				foreach ($bccs as $bcc => $responses) {
					?><li><?
					print_email_link($bcc);
					if ($responses) {
						$parts = array();
						foreach (explode(',',$responses) as $info) {
							[$tmpticketid,$cstamp] = explode(':',$info);
							$parts[] = '<a href="/ticket/'.$tmpticketid.'">'._datedaytime_get($cstamp).'</a>';
						}
						echo ', ',element_name('response',count($parts)),': ',implode(', ',$parts);
					}
					?></li><?
				}
				?></ul></div><?
				layout_close_box();
			}
		}
	}

	$spec = explain_table('contact_ticket_message');
	if ($am_withu) {
		layout_open_menu();
		if ($_REQUEST['ACTION'] !== 'answerform'
		&&	(	$ticket['STATUS'] !== 'closed'
			||	$ticket['CLOSETYPE'] !== 'enforced'
			)
		) {
			layout_open_menuitem();
			?><a id="processlink" href="/ticket/<?= $ticketid ?>/answerform" onclick="return openContactForm(this);"><?= __C('action:answer'); ?></a><?
			layout_close_menuitem();
		}
		if (have_super_admin()) {
			layout_open_menuitem();
			with_confirm(
				__C('action:remove'),
				'/ticket/'.$ticketid.'/erase-ticket'.($noverview ? '?NOVERVIEW' : ''),
				__('contact:confirm:ticket_removal_LINE')
			);
			layout_close_menuitem();
		}
		if (!$ticket['READM']
		&&	 $ticket['STATUS'] !== 'pending'
		) {
			layout_continue_menu();
			layout_menuitem(__C('action:mark_read'),'/ticket/'.$ticketid.'/markread');
			layout_close_menuitem();
		}
		layout_close_menu();
		if ($element === 'contest'
		&&	!$id
		) {
			show_contest_history($ticket, $mail_options ?? null);
		}
		if ($_REQUEST['ACTION'] !== 'answerform') {
			?><div class="hidden" id="contactanswerform"><?
		}
		?><form enctype="multipart/form-data" method="post" onsubmit="return submitForm(this)" action="/ticket/<?= $ticketid ?>"><?
		?><input type="hidden" name="FORMSTAMP" value="<?= CURRENTSTAMP; ?>" /><?

		layout_open_box('contact-toadmin');
		if (!NO_ALTER_ATTACHMENT) {
			require_once '_attachments.inc';
			?><table class="dyntab dens fw"><tr><td><?
		}
		?><div><b><?= get_element_link('user',CURRENTUSERID,$GLOBALS['currentuser']->row['NICK']);
		?></b><br /><small class="light"><? _datedaytime_display(CURRENTSTAMP);
		?></small></div><?
		?><textarea name="MSGBODY" cols="70" rows="15" data-min="8" data-max="15" id="msgbody" required="required" class="growToFit" maxlength="<?= $spec['BODY']->maxlength ?? '' ?>"><?
		if (!empty($_POST['MSGBODY'])) {
			echo escape_specials($_POST['MSGBODY']);
		}
		?></textarea><?

		$allowmail = true;

		show_attachment_options(true, $template ?? null);

		ob_start();

			if (!NO_ALTER_ATTACHMENT) {
				if ($allowmail) {
					?><br /><?
				}
				?><label><?= Eelement_name('attachment') ?> <input onclick="cbclickp(this);showAttachments(this)" type="checkbox" name="ATTACHMENT_C" value="1" /></label><?
			}
		if ($data = ob_get_clean()) {
			?></td><td class="right vbottom nowrap lpad"><?
			echo $data;
		}
		?></td><?
		?></tr><?
		?></table><?
		layout_close_box();

		?><div class="r block"><? __C('field:via_email_too') ?>: <?
		via_email_too(recommended_email: $recommended_email);
		?></div><?

		?><div class="block"><?
		?><input type="submit" name="ANSWER" value="<?= __('action:respond') ?>" /><?
		?><input type="submit" name="ANSWERCLOSE" style="margin-left:2em" value="<?= __('action:respond_and_close') ?>" /><?
		?></div><?

		if ($_REQUEST['ACTION'] !== 'answerform') {
			?></div><?
		}
	} elseif ($locked) {
		if (false === ($template = template_obtain())) {
			return;
		}
			# Satisfy EA inspection
		if (($template = (array)$template)
		&&	!empty($template['FORCE_EMAIL'])
		&&	!$ticket['LASTMAIL']
		) {
			$ticket['LASTMAIL'] = 1;
			require_once '_contactmailoptions.inc';
			if (false === ($mail_options = get_contact_mail_options($ticketid))) {
				return;
			}
				# Satisfy EA inspection
			if ($mail_options = (array)$mail_options) {
				foreach ($mail_options as $i => $mail_option) {
					if (!strcasecmp($mail_option['EMAIL'],$template['TO_EMAIL'])) {
						$selected_opt = $i;
						break;
					}
				}
			}
			if (!isset($selected_opt)) {
				require_once '_email.inc';
				$ctmsgid = db_single('contact_ticket_mail','
					SELECT CTMSGID
					FROM contact_ticket_mail
					WHERE FROM_EMAIL = "'.addslashes($template['TO_EMAIL']).'"
					LIMIT 1'
				);
				$mail_options[] = [
					'NAME'		=> $template['TO_NAME'],
					'FIRSTNAME'	=> name_is_impersonal($template['TO_NAME'])
								?  get_name_from_email($template['TO_EMAIL'])
								:  preg_split('"[\h_]+"', $template['TO_NAME'])[0],
					'EMAIL'		=> $template['TO_EMAIL'],
					'TYPE'		=> $ctmsgid ? MAIL_FROM_TEMPLATE : MAIL_FROM_PROFILE,
					'CTMSGID'	=> $ctmsgid ?: 0,
				];
				$mail_option_count = count($mail_options);
				$selected_opt = $mail_option_count-1;
			} else {
				$mail_option_count = count($mail_options);
			}
		}
		if (!$ticket['MAILING']
		&&	show_vote_results_for_element($element)
		) {
			if (have_enough_ticket_votes($ticketid,$actualvote,$element)) {
				show_ticket_vote_menu($actualvote);
				show_ticket_votes($ticketid);
			}
			show_old_ticket_votes($ticketid);
		}

		?><form<?
		?> id="responseform"<?
		?> enctype="multipart/form-data"<?
		?> method="post"<?
		?> onsubmit="return submitResponseForm(this)"<?
		?> action="/ticket/<?= $ticketid ?>"<?
		?>><?

		$show_forward = show_forward_form($ticket);

		$move_visible = show_move_form($ticket, TICKET_MOVE_SHOW_HIDDEN_STORAGE, $ticket);

		layout_open_menu();
		layout_open_menuitem();
		?><span id="processlink" class="unhideanchor" onclick="openContactForm(this)"><?= __C('action:process(contact)') ?></span><?
		layout_close_menuitem();

		if (have_super_admin()
		||	$ticket['MSGCNT'] === 1
		&&	$lastmessage
		&&	$lastmessage['AUTOGEN']
		||	$ticket['ISMAIL']
		&&	$firstmessage
		&&	false !== mb_stripos($firstmessage['SUBJECT'],'ALERT partyflock')
		&&	false !== stripos($firstmessage['FROM_EMAIL'],'.equinix.com')
		) {
			layout_open_menuitem();
			with_confirm(
				__C('action:remove'),
				'/ticket/'.$ticketid.'/erase-ticket'.($noverview ? '?NOVERVIEW' : '')
				,__('contact:confirm:ticket_removal_LINE')
			);
			layout_close_menuitem();
		}
		$allow_close = true;
/*		=	have_super_admin()
		||	$ticket['MSGCNT'] !== 1
		||	in_array($ticket['ELEMENT'], [
				'office',
				'affiliate',
				'invoice',
				'sm',
				'b2b',
				'ad',
				'banner',
				'newsad',
				'promo',
				'chat',
				'camerarequest',
			], strict: true)
		||	$ticketp['ELEMENT'] === 'camerarequest'
		&&	$ticket['ID']
		&&	($camera_info = get_camera_info($ticket['ID']));*/

			require_once '_bubble.inc';
			layout_open_menuitem();
			$bubble = new bubble(CLICK_BUBBLE | BUBBLE_BLOCK | CLICK_KEEP_BUBBLE | CONTENT_AT_BOTTOM);
			$bubble->catcher(Eelement_name('status'));
			$bubble->content();

			foreach ([
				__('action:open')		=> 'open',
				__('action:pend')		=> 'pending',
				__('action:close')		=> 'closed',
			] as $name => $status) {
				if (is_array($status)) {
					[$status, $closetype] = $status;
				}
				if ($status === 'closed'
				&&	!$allow_close
				||	$status !== 'pending'
				&&	$status === $ticket['STATUS']
				) {
					continue;
				}
				?><div><?
				?><a class="ticket-<?= $status ?>" href="/ticket/<?= $ticketid
				?>/setstatus?STATUS=<? echo $status;
				if (isset($closetype)) {
					?>;CLOSETYPE=<? echo $closetype;
				}
				if ($noverview) {
					?>;NOVERVIEW<?
				}
				if ($status === 'pending') {
					?>;RETURN=<?= $ticket['EXPIRES'] ?>,<?
					$hours = ($expires = $ticket['EXPIRES']) / 3600;
					$days = $hours / 24;
					global $__mins;
					$expstamp = $days < 1
					?	$expires - $__mins * 60
					:	strtotime('today +'.$days.' days 8:00');
					echo $expstamp;
				}
				?>"><?= $name;
				?></a><?
				if ($status === 'pending') {
					?> <? show_return_selection($ticket['EXPIRES'], ' onchange="Pf_changePendingReturn(this)"');
				}
				?></div><?
			}
			$bubble->display();
			layout_close_menuitem();

		if (!$ticket['MAILING']) {
			require_once 'defines/contactstandardanswer.inc';
			$stdanswers = memcached_rowuse_hash(['contact_standard_answer', 'contact_standard_answer_data'],'
				SELECT	contact_standard_answer.DATAID, ANSWERID,
					BODY, TITLE
				FROM contact_standard_answer
				JOIN contact_standard_answer_data USING (DATAID)
				WHERE ELEMENT = "'.$element.'"
				  AND (FLAGS & '.STDANSWER_INCOMING.' OR NOT (FLAGS & '.STDANSWER_OUTGOING.'))
				  AND NOT (FLAGS & '.STDANSWER_REMOVED.')
				ORDER BY TITLE',
				key: 'stdanswers:'.$element
			);
			if ($stdanswers === false) {
				return;
			}
			if ($stdanswers) {
				layout_open_menuitem();
				include_js('js/contact_'.$element, base: 'js/contact');
				?><select name="ANSWERDATAID" onchange="openContactForm();stdAnswer(this)"><?
				?><option value="0"></option><?
				foreach ($stdanswers as $stdanswer) {
					?><option value="<?= $stdanswer['DATAID'] ?>"><?= escape_specials($stdanswer['TITLE']) ?></option><?
				}
				?></select><?
				layout_close_menuitem();
			}
		}
		layout_continue_menu();

		if (!$__shown_junk_options
		&&	!$ticket['MAILING']
		&&	empty($msgcnts['touser'])
		&&	$lastmessage
		) {
			include_js('js/form/contactjunk');
			include_js('js/form/postlink');
			layout_open_menuitem();
			?><span onclick="showJunkInformation(this, <?= $ticketid ?>, <?= $lastmessage['CTMSGID'] ?>);" class="unhideanchor"><?=
				Eelement_name('junk')
			?></span><?
			layout_close_menuitem();
		}

		layout_open_menuitem();
		?><span onclick="openForwardOptions(this)" class="unhideanchor" id="forwardlink"><?= __C('action:forward') ?></span> <?
		?><span onclick="openMoveOptions(this)" class="unhideanchor" id="movelink"><?= __C('action:move') ?></span><?
		layout_close_menuitem();

		layout_close_menu();

		if (!$ticket['MAILING']) {
			switch ($element) {
			case 'video':
				if (!$id) {
					$arg = null;
					foreach ($messages as $message) {
						if (preg_match('"/v[/=]([a-zA-Z0-9_\-=]+)"'.$utf8_mod, $BODY, $match)
						||	preg_match('"type=youtube id=([a-zA-Z0-9_\-=]+)"'.$utf8_mod, $BODY, $match)
						) {
							$arg = ';TYPE=youtube;ID='.$match[1];
							break;
						}
					}
					layout_open_menu();
					layout_menuitem(__C('action:add_video'),'/video/form?TICKETID='.$ticketid.$arg,BLANK_TARGET);
					layout_menuitem(__C('action:add_channel'),'/video/channel/?TICKETID='.$ticketid,BLANK_TARGET);
					layout_close_menu();
				}
				break;
			case 'label':
				if (!$id) {
					layout_open_menu();
					layout_menuitem(__C('action:add_label'),'/label/form?TICKETID='.$ticketid,BLANK_TARGET);
					layout_menuitem(__C('action:search_for_label'),'/label',BLANK_TARGET);
					layout_close_menu();
				}
				break;
			case 'party':
				if (!$id) {
					layout_open_menu();
					layout_menuitem(__C('action:add_party'),'/party/register?TICKETID='.$ticketid,BLANK_TARGET);
					layout_menuitem(__C('action:search_for_party'),'/agenda/search',BLANK_TARGET);
					layout_close_menu();
				}
				break;
			case 'promo':
				layout_open_menu();
				if (!$id) {
					layout_menuitem(__C('action:add_promo'),'/promo/form?TICKETID='.$ticketid.pass_relationlist($donerels),BLANK_TARGET);
				}
				show_advertising_link();
				layout_close_menu();
				break;
			case 'city':
			case 'column':
			case 'development':
			case 'label':
			case 'stream':
				if (!$id) {
					layout_open_menu();
					layout_menuitem(__C('action:add_'.$element),'/'.$element.'/form?TICKETID='.$ticketid,BLANK_TARGET);
					layout_close_menu();
				}
				break;
			case 'ad':
				layout_open_menu();
				if (!$id) {
					layout_menuitem(__C('action:add_ad'),'/ad/form?TICKETID='.$ticketid,pass_relationlist($donerels),BLANK_TARGET);
					layout_menuitem(__C('action:add_banner'),'/banner/form?TICKETID='.$ticketid.pass_relationlist($donerels),BLANK_TARGET);
					show_advertising_link();
				}
				layout_close_menu();
				break;
			case 'b2b':
			case 'job':
			case 'tvad':
				layout_open_menu();
				show_advertising_link();
				layout_close_menu();
				break;
			case 'newsad':
				layout_open_menu();
				if (!$id) {
					layout_menuitem(__C('action:add_newsad'),'/newsad/form?TICKETID='.$ticketid.pass_relationlist($donerels),BLANK_TARGET);
					layout_menuitem(__C('action:search_for_party'),'/agenda/search',BLANK_TARGET);
					layout_menuitem(__C('action:search_for_location'),'/location',BLANK_TARGET);
				}
				show_advertising_link();
				layout_close_menu();
				break;
			case 'office':
				layout_open_menu();
				show_advertising_link();
				layout_close_menu();
				break;
			case 'news':
				if (!$id) {
					layout_open_menu();
					layout_menuitem(__C('action:add_news'),'/news/form?TICKETID='.$ticketid,BLANK_TARGET);
					layout_menuitem(__C('action:search_for_party'),'/agenda/search',BLANK_TARGET);
					layout_menuitem(__C('action:search_for_location'),'/location',BLANK_TARGET);
					layout_close_menu();
				} elseif (have_admin('newsad')) {
					layout_open_menu();
					layout_menuitem(__C('action:add_newsad'),'/newsad/form?TICKETID='.$ticketid.pass_relationlist($donerels),BLANK_TARGET);
					layout_close_menu();
				}
				break;
			case 'artist':
			case 'organization':
				if (!$id) {
					layout_open_menu();
					layout_menuitem(__C('action:add_'.$element),'/'.$element.'/register?TICKETID='.$ticketid,BLANK_TARGET);
					layout_menuitem(__C('action:search_for_'.$element),'/'.$element,BLANK_TARGET);
					layout_menuitem(__C('action:search_for_party'),'/agenda/search',BLANK_TARGET);
					layout_close_menu();
				}
				break;
			case 'location':
				if (!$id) {
					layout_open_menu();
					layout_menuitem(__C('action:add_location'),'/location/register?TICKETID='.$ticketid,BLANK_TARGET);
					layout_menuitem(__C('action:search_for_location'),'/location',BLANK_TARGET);
					layout_close_menu();
				}
				break;
			case 'contest':
				layout_open_menu();
				if (!$id) {
					layout_menuitem(__C('action:add_contest'),'/contest/form?TICKETID='.$ticketid,BLANK_TARGET);
					layout_menuitem(__C('action:search_for_party'),'/agenda/search',BLANK_TARGET);
				} else {
					if (db_single('contest', 'SELECT CLOSED FROM contest WHERE CONTESTID = '.$id, DB_USE_MASTER)) {
						layout_open_menuitem();
						with_confirm(
							__C('action:notify_winners'),
							'/contest/'.$id.'/msgwins',
							__C('contest:confirm:notify_winners_LINE'),
							target: '_blank'
						);
						layout_close_menuitem();
					}
					require_once '_contestcode.inc';
					show_contestcode_menu($id);
				}
				layout_close_menu();
				if (!$id) {
					show_contest_history($ticket, $mail_options ?? null);
				}
				break;

			case 'camerarequest':
				if (!$id) {
					layout_open_menu();
					layout_menuitem(__C('action:add_camerarequest'),'/camera/form?TICKETID='.$ticketid);
					layout_close_menu();
					break;
				}
				if ($element === 'camerarequest'
				&&	$id
				&&	($status = db_single('camera', "SELECT STATUS FROM camera WHERE PARTYID = $id"))
				&&	(require_once 'defines/camera.inc')
				&&	is_accepted_camera_status($status)
				) {
					layout_open_menu();
					layout_menuitem(__C('action:make_response'),'/ticket/'.$ticketid.'?USETEMPLATE=camerapublished;PARTYID='.$id);
					?> (<?= __('status:published') ?>)<?
					layout_close_menu();
				}
				if (!$toadmin) {
					break;
				}
				if ($reqinfo = get_camreq_request_info($ticket,$with_userid, $mail_options ?? null)) {
					if (empty($mail_options)) {
						$try_mail = $try_name = null;
					} else {
						$try_mail = $mail_options[0]['EMAIL'];
						$try_name = $mail_options[0]['NAME'];
					}
					[$askelement,$askid,$camreq_name,$camreq_email,$camreq_userid] = $reqinfo;
					if ($try_mail && !preg_match('"@partyflock\.nl$"',$try_mail)) {
						if (strcasecmp($camreq_email,$try_mail)) {
							$diff['email'] = array($try_mail,$camreq_email);
						}
						if ($try_name && !match_email($try_name) && strcasecmp($camreq_name,$try_name)) {
							$diff['name'] = array($try_name,$camreq_name);
						}
					}
					if ($with_userid && $with_userid !== $camreq_userid) {
						$diff['user'] = array($with_userid,$camreq_userid);
					}
					if (isset($diff)) {
						include_js('js/form/contactcamreq');
						?><div id="camrequpd" data-question="<?= __('camerarequest:confirm:ignore_new_contact_person_LINE') ?>"><?
						layout_open_box('white');
						layout_box_header(get_element_link($askelement,$askid));
						layout_open_table(TABLE_CLEAN);
						if ($haveold = $camreq_email || $camreq_name || $camreq_userid) {
							?><tr><th></th><th><?= __('status:new') ?></th><th><?= __('status:current') ?></th></tr><?
						}
						foreach ($diff as $what => $info) {
							[$newvalue,$oldvalue] = $info;
							layout_start_rrow(0,null,'rpad');
							?><input checked="checked" class="rmrgn" type="checkbox" onclick="setattr(this.form.new<?= $what ?>,'disabled',!this.checked)" /><?
							layout_next_cell(class: 'rpad');
							if ($what === 'user') {
								?><input type="hidden" name="new<?= $what ?>" value="<?= $newvalue ?>" /><?
								echo escape_specials(get_element_title('user',$newvalue));
							} else {
								?><input type="text" name="new<?= $what ?>" value="<?= escape_specials($newvalue) ?>" /><?
							}
							if ($haveold) {
								layout_next_cell(class: 'rpad');
								if ($oldvalue) {
									echo	$what === 'user'
									?	get_element_link('user',$oldvalue)
									:	escape_specials($oldvalue);
								}
							}
							layout_stop_row();
						}
						layout_close_table();
						layout_close_box();
						?><div class="block"><?
							?><input type="button" onclick="store_camreq(this.form,'<?= $askelement ?>',<?= $askid ?>,<?= $ticketid ?>)" value="<?= __('action:store_as_contact_person') ?>" /> <?
							?><input type="button" onclick="ignore_camreq(this.form,<?= $ticketid ?>)" value="<?= __('action:ignore') ?>" /><?
						?></div><?
						?></div><?
					}
				}
				layout_open_menu();
				layout_menuitem(
					__C('action:accept_request'),
					'/camera/'.$id.'/form?STATUS=accepted;TICKETID='.$ticketid,
					' class="notice-nb"',
					isset($diff) ? 'onclick="return check_camreq()"' : null
				);
				layout_menuitem(
					__C('action:deny_request'),
					'/camera/'.$id.'/form?STATUS=denied;TICKETID='.$ticketid,
					' class="warning-nb"'
				);
				layout_close_menu();
				break;
			case 'talentupload':
				if ($with_userid) {
					layout_open_menu();
					if (!db_single('talentupload','SELECT 1 FROM talentupload WHERE USERID='.$with_userid)) {
						layout_menuitem(__C('action:add_talentupload'),'/talentupload/'.$with_userid.'/addform');
					} else {
						layout_open_menuitem();
						?><small class="light">Talentupload bestaat reeds</small><?
						layout_close_menuitem();
					}
					layout_close_menu();
				}
				break;
			}
		}
			?><div id="contactresponseform"<?
			if (!$template) {
				?> class="hidden"<?
			}
			?>><?
			if ($noverview) {
				?><input type="hidden" name="NOVERVIEW" value="1" /><?
			}
			if (!empty($template)) {
				template_passthrough($template);
			}
			?><input type="hidden" name="FORMSTAMP" value="<?= CURRENTSTAMP ?>" /><?
			?><input type="hidden" name="TICKETHASH" value="<?= get_ticket_hash($ticketid) ?>" /><?

			layout_open_box('contact-tounknown', 'responsebox');

			?><div class="r" id="optstore"><?
			$move_visible = show_move_form($ticket, TICKET_MOVE_SHOW_IN_RESPONSE, $template);
			?></div><?

			?><div class="block"><?
			if ($ticket['LASTMAIL']) {
				require_once '_salutes.inc';
				$signature = get_contact_signature($element) ?: get_contact_signature('default') ?: generate_contact_signature();
				show_input([
					'style'	=> 'min-width: 50%',
					'class'	=> 'regular',
					'type'	=> 'text',
					'name'	=> 'FROMNAME',
					'value'	=> escape_utf8_to_win1252($signature['FROMNAME']),
				]);
			} else {
				?><b><?= get_element_link('user', CURRENTUSERID, $GLOBALS['currentuser']->row['NICK']); ?></b><?
			}
			?><br /><?
			?><small class="light"><? _datedaytime_display(CURRENTSTAMP); ?></small><?
			?></div><?
			?><table class="dyntab fw nomargin"><tr><td class="fw"><?

			if ($ticket['LASTMAIL']) {
				$from_email_options = [];
				foreach ($messages as $message) {
					if (preg_match_all('"[a-z0-9_.\-]+@[a-z0-9_.\-]+\.[a-z]{2,}"i'.$utf8_mod, $BODY, $matches)) {
						foreach ($matches[0] as $email) {
							$from_email_options[strtolower($email)] = [$email, $message];
						}
					}
				}
				if ($from_email_options) {
					if ($mail_options) {
						foreach ($mail_options as $mailoption) {
							$lowerexisting = strtolower($mailoption['EMAIL']);
							$alreadyhave = false;
							foreach ($from_email_options as $loweremail => $something) {
								if ($loweremail === $lowerexisting) {
									unset($from_email_options[$loweremail]);
									break;
								}
							}
						}
					}
					foreach ($from_email_options as $loweremail => [$email, $message]) {
						$mail_options[] = [
							'FIRSTNAME'		=> ($first_name = get_email_salute($email)),
							'NAME'			=> $first_name,
							'EMAIL'			=> $email,
							'CTMSGID'		=> $message['CTMSGID'],
							'SUBJECT'		=> $message['SUBJECT'],
							'TYPE'			=> MAIL_FROM_BODY,
							'NOREPLY'		=> dont_reply($email),
							'PREFERENCE'	=> 10000,
						];
						++$mail_option_count;
					}
				}

				if ($mail_option_count) {
					$everyone = get_contact_mails_for_everyone($ticketid);

					$ccs = !empty($everyone['ccs']);

					?><select<?
					?> onchange="changeSendto(this)"<?
					?> name="SEND_TO"<?
					?> class="fw<?
					if ($ccs) {
						# Show CC options as warning, as sometimes mailing list senders
						# use CC instead of BCC by accident.
						# We should not responsd to CC in that case.
						?> warning<?
					}
					?>"><?
					if ($ccs) {
						?><option <?
						?> data-ccs <?
						?> data-first-name="<?= escape_specials($mailoption['FIRSTNAME']) ?>"<?
						?> value="<?= $mailoption['CTMSGID'] ?>,<?= MAIL_EVERYONE ?>"<?
						?>>&rarr; <?= element_name('everyone')
						?> &rarr; <?= escape_specials(implode(', ', $everyone['all']))
						?></option><?
					}
					foreach ($mail_options as $i => $mailoption) {
						?><option<?
						?> data-first-name="<?= escape_specials($mailoption['FIRSTNAME']) ?>"<?
						if (!empty($mailoption['NOREPLY'])) {
							?> disabled<?
						} else {
							$have_valid_receiver = true;
						}
						?> value="<?= $mailoption['CTMSGID']; ?>,<?= $mailoption['TYPE'] ?>;<?= base64_encode($mailoption['EMAIL']) ?>"><?
						?>&rarr; <?
						if ($mailoption['NAME']) {
							echo escape_specials($mailoption['NAME']);
							?> &lt;<?= escape_specials($mailoption['EMAIL']) ?>&gt;<?
						} else {
							echo escape_specials($mailoption['EMAIL']);
						}
						?> (<?= __('contact:mailoption:'.$mailoption['TYPE']) ?>)</option><?
					}
					?></select><?
					show_input([
						'name'		=> 'EMAIL',
						'type'		=> 'email',
						'placeholder'	=> element_name('email'),
						'class'		=> isset($have_valid_receiver) ? 'hidden' : null,
						'onchange'	=> 'changeEmail(this)'
					]);
				}
				require_once '_contactsubject.inc';
				if (!empty($template['SUBJECT'])) {
					$subject = win1252_to_utf8($template['SUBJECT']);

				} elseif ($last_subject) {
					$subject = $last_subject;

				} elseif ($id && isset(USE_URLTITLE[$element])) {
					$subject = get_element_title($element, $id) ?: win1252_to_utf8(Eelement_plural_name($element));

				} else {
					$subject = win1252_to_utf8(Eelement_plural_name($element));
				}

				?><input type="text" maxlength="150" name="SUBJECT" value="<?= escape_utf8($subject) ?>" /><br /><?
				require_once '_email.inc';
				?><input type="text" data-impersonal="<?= implodekeys('|', IMPERSONAL_USERS) ?>" name="HEADER"<?
				?> id="header"<?
				?> data-template-ls="<?= escape_utf8_to_win1252($signature['HEADER_LS']) ?>"<?
				?> data-template="<?=	 escape_utf8_to_win1252($signature['HEADER']) ?>"<?
				?> value="<?
				ob_start();
				$usename = $mail_options[0]['NAME']  ?? null;
				$usemail = $mail_options[0]['EMAIL'] ?? null;

				require_once '_salutes.inc';
				$first_name = $usemail ? get_email_salute($usemail, $usename) : null;

				echo escape_specials(
					$first_name
				?	str_replace('#NAAM#', $first_name, $signature['HEADER'])
				:	$signature['HEADER_LS']
				);

				$hdr = ob_get_flush();
				?>"><?
				?><input type="hidden" name="ORIG_HEADER" value="<?= $hdr ?>" /><?

				?><br /><?
			} elseif ($subject = $_POST['SUBJECT'] ?? $template['SUBJECT'] ?? '') {
				?><input type="hidden" name="SUBJECT" value="<?= ($subject); ?>" /><?
			}
			?><div class="block"><?
			?><textarea id="msgbody" name="MSGBODY" cols="70" rows="15" data-min="8" data-max="15" class="growToFit" maxlegnth="<?= $spec['BODY']->maxlength ?? '' ?>"><?
			if (!empty($_POST['BODY'])) {
				echo escape_specials($_POST['BODY']);
			} elseif (!empty($template['BODY'])) {
				echo escape_specials($template['BODY']);
			}
			?></textarea><?
			if (#!isset($argh)  &&
				$ticket['LASTMAIL']
			) {
				?><textarea name="FOOTER" cols="50" rows="3" data-max="3"<?
				?>><?= escape_utf8_to_win1252($signature['FOOTER']) ?></textarea><?
			}
			if (!NO_ALTER_ATTACHMENT) {
				require_once '_attachments.inc';
				show_attachment_options(true, $template);
			}
			?></div><?
			?></td><td class="vbottom lpad"><?
			require_once '_bubble.inc';

			$trackit_bubble = new bubble(BUBBLE_30_WIDE | BUBBLE_BLOCK | HELP_BUBBLE);
			$trackit_bubble->catcher();
			?><label for="trackit"><?= __C('action:track') ?></label><?
			$trackit_bubble->title(__C('action:watch(list)'));
			$trackit_bubble->content();
			?>Door een ticket te volgen blijft hij ook in jouw inbox staan wanneer de status 'onbeantwoord' of 'gesloten' is.<?
			$trackit_bubble->close();

			layout_open_table('nodyn');
			if ($ticket['LASTMAIL']) {
				layout_start_row(0,'bold-hilited','implicitrow');
				?><label for="quote"><?= __C('action:implicit_quote') ?></label><?
				layout_field_value();
				?><input onclick="cbclickpp(this)" type="checkbox" value="1" name="QUOTE" id="quote" checked="checked" /><?
				layout_stop_row();
			}
			layout_start_row();
			?><label for="forwardit"><?= __C('action:forward'); ?></label><?
			layout_field_value();
			?><input onclick="forwarditclick(this)" type="checkbox" name="FORWARDIT" id="forwardit" value="1" /><?

			layout_restart_row();
			?><label<?
			if ($move_visible) {
				?> class="bold-hilited"<?
			}
			?> for="moveit"><?= __C('action:move'); ?></label><?
			layout_field_value();
			?><input<?
			if ($move_visible) {
				?> checked<?
			}
			?> onclick="moveitclick(this)" type="checkbox" name="MOVEIT" id="moveit" value="1"><?
			layout_restart_row();

			if (CURRENTUSERID !== $ticket['OWNERID']) {
				?><label for="movetome">&rarr; <?= Eelement_name('inbox') ?></label><?
				layout_field_value();
				?><input onclick="inboxaction(this)" type="checkbox" name="INBOXACTION" id="movetome" value="into" /><?
			} else {
				?><label for="moveaway"><?= Eelement_name('inbox') ?> &rarr;</label><?
				layout_field_value();
				?><input onclick="inboxaction(this)" type="checkbox" name="INBOXACTION" id="moveaway" value="outof" /><?
			}

			layout_restart_row();
			?><span id="keeplocklabel"><?
			?><label for="keeplock"><?= __C('action:keep(lock)') ?></label><?
			?></span><?
			layout_field_value();

			?><div id="keeplockcheckbox"><?
			?><input onclick="cbclickppp(this)" type="checkbox" name="KEEPLOCK" id="keeplock" value="1" /><?
			?></div><?

#			layout_restart_row((CURRENTUSERID != $ticket['OWNERID'] ? ROW_HIDDEN : 0) | ($ticket['TRACKIT'] ? ROW_BOLD_HILITED : 0),'trackit-row');
			$trackit =	$ticket['TRACKIT']
				&&	(	$ticket['STATUS'   ] !== 'closed'
					||	$ticket['CLOSETYPE'] !== 'auto');

			layout_restart_row($trackit ? ROW_BOLD_HILITED : 0);
			$trackit_bubble->display_catcher();
			layout_field_value();
			?><input onclick="cbclickpp(this)" type="checkbox" value="1" name="TRACKIT" id="trackit"<?
			if ($trackit) {
				?> checked<?
			}
			?>><?
			if (!NO_ALTER_ATTACHMENT) {
				layout_restart_row();
				?><label for="attachment-c"><?= Eelement_name('attachment') ?></label><?
				layout_field_value();
				?><input onclick="showAttachments(this); cbclickpp(this);" type="checkbox" name="ATTACHMENT_C" id="attachment-c" value="1" /><?
			}
			layout_stop_row();
			layout_close_table();

			$trackit_bubble->display_hidden_part();

			?></td></tr></table><?

			layout_close_box();

			?><input type="hidden" id="direction" name="DIRECTION" /><?

			ob_start();
			if ( !$ticket['MAILING']
			&&	(!$ticket['LASTMAIL'] || $mail_option_count)
			) {
				show_input([
					'name'			=> 'RESPOND',
					'type'			=> 'submit',
					'onmouseout'	=> "overSubmit(this, 'tounknown')",
					'onmouseover'	=> "overSubmit(this, 'touser')",
					'value'			=> __('action:respond'),
					'title'			=> '&rarr; '.element_name('user'),
				]);

				?> <span class="small"><?

				ob_start();
				show_return_selection(getifset($template, 'EXPIRES') ?: $ticket['EXPIRES']);
				$select = ob_get_clean();
				echo str_replace('%DURATION%', $select, __('attrib:back_in_duration', KEEP_EMPTY_KEYWORDS)) ?></span><?

				?><input<?
				?> name="CLOSE"<?
				?> type="submit"<?
				?> style="margin-left:2em"<?
				?> onmouseout="overSubmit(this, 'tounknown');"<?
				?> onmouseover="overSubmit(this, 'touser');"<?
				?> value="<?= __('action:respond_and_close') ?>"<?
				?> title="&rarr; <?= element_name('user') ?>"><?

				?><input name="PROGRESS"<?
				?> type="submit"<?
				?> style="margin-left:2em"<?
				?> onmouseout="overSubmit(this, 'tounknown');"<?
				?> onmouseover="overSubmit(this, 'touser');"<?
				?> value="<?= element_name('progress') ?>"<?
				?> title="&rarr; <?= element_name('user') ?>"><?
				$parts[] = ob_get_clean();
				ob_start();
			}
			?><input<?
			?> class="blue"<?
			?> name="DISCUSSION"<?
			?> type="submit"<?
			?> onmouseout="overSubmit(this, 'tounknown');"<?
			?> onmouseover="overSubmit(this, 'toadminfromadmin');"<?
			?> value="<?= element_name('comment') ?> &amp; <?= __('action:keep(lock)') ?>"<?
			?> title="&rarr; <?= element_plural_name('all_employee') ?>"> <?

			?><input class="blue"<?
			?> name="COMMENT"<?
			?> type="submit"<?
			?> onmouseout="overSubmit(this, 'tounknown');"<?
			?> onmouseover="overSubmit(this,' toadminfromadmin');"<?
			?> value="<?= element_name('comment') ?>"<?
			?> title="&rarr; <?= element_name('employee') ?>"><?

			$parts[] = ob_get_clean();

			if (!isset($parts[1])) {
				?><div class="right block"><?= $parts[0] ?></div><?
			} else {
				?><div class="l block"><?= $parts[0] ?></div><?
				?><div class="r block"><?= $parts[1] ?></div><?
				?><div class="clear"></div><?
			}

			?></div><?
			?></form><?
	}
	echo $messagestr;
}

function contact_commit_new() {
	require_once '_attachments.inc';
	if (!require_post()
	||	!require_idnumber				($_POST, 'LAST_TICKETID')
	||	!require_number					($_POST, 'FORMSTAMP')
	||	!require_contact_element		($_POST, 'ELEMENT')
	||	!require_contact_message_input	($_POST)
	||	!verify_attachments()
	) {
		return false;
	}
	if (False === ($ticketid = db_single('contact_ticket', '
		SELECT TICKETID
		FROM contact_ticket
		WHERE USERID = '.(have_user() ? CURRENTUSERID : 0).'
		  AND TICKETID > '.$_POST['LAST_TICKETID'].'
		  AND FORMSTAMP = '.$_POST['FORMSTAMP'].'
		LIMIT 1',
		DB_USE_MASTER
	))) {
		return false;
	}
	if ($ticketid) {
		$_REQUEST['sID'] = $ticketid;
		register_warning('contact_ticket:warning:already_created_LINE', ['TICKETID' => $ticketid]);
		return false;
	}
	if (!have_idnumber($_POST, 'ID')) {
		$_POST['ID'] = '0';
	}
#	if (isset($_POST['EMAIL'])) {
#		_error('Op het moment is het niet mogelijk om direct email tickets te openen!');
#		return;
#	}
	if ($emailtype = getifset($_POST, 'EMAILTYPE')) {
		if (!have_user()
		||	empty($emailtype)
		) {
			unset($_POST['EMAIL']);
		} elseif ($emailtype === 'profile') {
			$info = db_single_array('user', 'SELECT NICK, NAME, REALNAME, EMAIL FROM user WHERE USERID = '.CURRENTUSERID);
			if (!$info) {
				unset($_POST['EMAIL']);
			} else {
				[$nick, $name, $realname, $_POST['EMAIL']] = $info;
				$_POST['NAME'] = $realname ?: $name ?: $nick;
			}
		} else {
			$_POST['NAME'] = '';
		}
	}

	$ownerid = 0;

	store_via_email();

	if (isset($_POST['EMAIL'])) {
		$GLOBALS['__forcemailcheck'] = true;
		if (!require_anything_trim($_POST, 'NAME')
		||	!require_working_email($_POST, 'EMAIL')
		) {
			return false;
		}
	} elseif (!require_user()) {
		error_log_r($_SERVER,'_SERVER');
		error_log_r($_REQUEST,'_REQUEST');
		error_log_r($_COOKIE,'_COOKIE');
		error_log_r(apache_request_headers(),'apache_request_headers');
		error_log_r($GLOBALS['currentuser'],'currentuser');
		if ($wasuserid = have_number($_POST, 'WASUSERID')) {
			require_once '_settings.inc';
			error_log_r(setting('FLAGS',$wasuserid),'settings.FLAGS for userid '.$wasuserid);
		}
		_error('Deze fout komt af en toe voor, neem contact op met de helpdesk als het probleem aanhoudt!');
		return false;
	}
	$newcontest = isset($_POST['NEWCONTEST']);
	$element = $org_element = $_POST['ELEMENT'];
	$id = $org_id = $_POST['ID'];

	if ($newcontest) {
		$element = 'contest';
		$id = 0;
	}
	$userid = CURRENTUSERID === 1 ? 0 : CURRENTUSERID;
	require_once '_contactconcerning.inc';

	if (!db_insert('contact_ticket','
		INSERT INTO contact_ticket SET
			LASTDIRECTION	="toadmin",
			READM		=1,
			FORMSTAMP	='.$_POST['FORMSTAMP'].',
			USERID		='.$userid.',
			WITHUSERID	='.$userid.',
			ELEMENT		="'.$element.'",
			ID		='.$id.',
			ORG_ELEMENT	="'.$org_element.'",
			ORG_ID		='.$org_id.',
			CONCERNING	='.get_concerning($newcontest ? 'contest' : $_POST['ELEMENT'],$_POST['ID']).',
			CSTAMP		='.CURRENTSTAMP.',
			LSTAMP		='.CURRENTSTAMP.',
			OWNERID		='.$ownerid.',
			STATUS		="open",
			ISMAIL		='.(isset($_POST['EMAIL']) ? 1 : 0).',
			LASTMAIL	='.(isset($_POST['EMAIL']) ? 1 : 0).',
			WITHEMAIL	="'.(isset($_POST['EMAIL']) ? addslashes($_POST['EMAIL']) : null).'",
			TRACKIT		=0,
			OUTGOING	=0,
			OMSGCNT		=0')
	) {
		return false;
	}
	$_REQUEST['sID'] = $ticketid = db_insert_id();
	register_notice('contact_ticket:notice:created_LINE');

	if (!($ctmsgid = contact_commit_message('toadmin',$userid,!empty($_POST['EMAIL'])))) {
		return false;
	}
	if (isset($_POST['EMAIL'])) {
		if (empty($_REQUEST['SUBJECT'])) {
			if ($id && isset(USE_URLTITLE[$element])) {
				$subject = get_element_title($element, $id);
				if (isset(USE_UNICODE[$element])) {
					$subject = utf8_to_win1252($subject);
				}
			} else {
				$subject = __C('elements:'.$element, DONT_ESCAPE);
			}
		} else {
			$subject = $_REQUEST['SUBJECT'];
		}
		if (!db_insert('contact_ticket_mail', '
			INSERT INTO contact_ticket_mail SET
				SUBJECT		= "'.addslashes($subject).'",
				TICKETID	= '.$_REQUEST['sID'].',
				FROM_NAME	= "'.addslashes($_POST['NAME']).'",
				FROM_EMAIL	= "'.addslashes($_POST['EMAIL']).'",
				HEADERS		= "",
				MREFERENCES	= "",
				CTMSGID		= '.$ctmsgid)
		) {
			return false;
		}
	}
	create_message_and_attachments($ticketid, false, $ctmsgid);
	return $ticketid;
}

function require_contact_message_input(array &$input): bool {
	return	require_number(			$input,	'FORMSTAMP')
		&&	require_something_trim($input, 'MSGBODY')
		&&	optional_number(		$input,	'ANSWERDATAID')
		&&	(	empty($_POST['EMAILTYPE'])
			|| 	require_element($_POST, 'EMAILTYPE', ['profile', 'other', 'last'], true));
}

const MAILPART = '[a-zA-Z0-9!#$%&\'*+/=?^_`{|}~\\-\\.]+';
const QUOTE_START_STRIP	= 1;
const QUOTE_START_PREV	= 2;

function detect_quote(int $line_number, array $message, string &$line, string &$quoteline = null, bool $utf8 = false): int|bool {
	static $__searches = null;

	if (!$utf8) {
		$utf8 = detect_utf8($line);
	} else {
		rem_utf8_bom($line);
	}

	$line = mytrim($line, utf8: $utf8);


	$utf8_mod = $utf8 ? 'u' : '';

	if (!isset($__searches[$utf8])) {
		$searchstrs = [
		'"\h*Sent with \[?Proton\s*Mail\]?(?:\([^)]*\))? Secure Email"i'	=> 2,

		'"^>"'									=> 2,
		'"\bOn .*? (?:wrote|sent):"i'						=> 4,
		'"\bLe .*? a \w+crit\s*:"i'						=> 4,

		'"^\-\-\-\-\-\-\-\-\-\- [\w]+"'					=> 2,
		'"\-{2,} Op .*? schreef \-{2,}"i'					=> 2,
		# <AUTHOR> <EMAIL> schreef:
		#'"^Op .*? schree1f:"i'			=> 2,
/*		"[\w.]+, [\w.]+ \d+, \d{4} om \d{2}:\d{2}, [\w\s]+? <.?@.?> schreef:"i'			=> 2,
*/	 	'"Op\s.*?\s20\d{2}\s.*?schreef[^<]*<[^@]*@[^>]*>"i'			=> 2,

		'"[\w\.\s]+\d+\.?\s+\w+\s*\d{4}, \d+:\d+ skrev [^<]+ <[^@]+@[^>]+>"i'	=> 2,
		# <AUTHOR> <EMAIL> escribió:
		'"\h*[\w\h\.]+,\h*\d+\h*[\w\.]+\h*\d{4}\h*.*?\h*escribió:\h*$"i'	=> 2,
		 '"^_{10,}"'								=> 2,
		'"[\s\-]*\-+\s*(?:Original|Origineel|Oorspronkelijk|Opdrindelig|Urspr.ngliche|Reply message|Weitergeleitete Message|Message d\'origine)"i'	=> 2,
		'"^Als antwoord op uw email van"'					=> 2,
		'"^(?:Citeren |Quoting ).*:"'						=> 2,
		'"^.*[\s\b>](?:schreef|wrote|sent):"i'					=> 2,
		# <AUTHOR> <EMAIL> schrieb am Do., 12. Mai 2022, 00:03:
		'".*\s+<?[^@]+@[^>]+>?\s*schrieb am"' 					=> 2,
		'"^(AtO) \d+:\d+ \d+\-\d+\-\d+, you wrote:"i'				=> 2,

		# On 2023-10-04 03:03, Thomas wrote:
		'"^On \d{4}-\d{2}-\d{2} \d{2}:\d{2}, [\w\s]+ wrote:"i'		=> 2,

		'"Van: .*@partyflock\.nl"i'						=> 2,
		'"From:.*Partyflock helpdesk"i'						=> 2,
		'"^\-+ Bericht van [\w\d ]+ <.*?> \-+"i'				=> 2,
		'"^Sent: .*?, \d{4} at \d+:\d+"i'					=> 2,
		'"^\s*From: .*'.MAILPART.'@'.MAILPART.'"i'				=> 2,
		'"^\d+/\d+/\d+,?\s.*\s<'.MAILPART.'@'.MAILPART.'>:?"'			=> 2,
		'"^>\s*From: "i'							=> 2,
		'"^\s*20\d\d[/\-]\d+[/\-]\d+(?: \d?\d:\d\d)?(?: GMT[\+\-]\d\d:\d\d)?,? [\w\s]+ <'.MAILPART.'@'.MAILPART.'>:"i'	=> 2,
		'"^\s*Op ([a-z]+ )?\d+(-\d+-|\s\w+\s)\d+ heeft [a-z\s]*?<?'.MAILPART.'@'.MAILPART.'>?"i'				=> 2,
		'"^\s*Op .* heeft (?:.+?) (?:het volgende )?geschreven:"i'		=> 2,
		'"^\s*[\@\w\d\s\(\)]+ schreef op [\s\-\d:]+:"i'				=> 2,
		'"^\s*(?:Op .* schreef|Am .* schrieb) .+:"i'				=> 2,
		'"^\s*On \d+/\d+/\d+ \d+:\d+"i'						=> 2,
		'"^\s*\d{4}/\d+/\d+\s+.*?@partyflock\.nl"i'				=> 2,
		'"^[\s\-]*Op (?:\w+ )?\d+[\-\s]+(?:[a-z]+\.?|\d+)[\-\s\.]+\d+(?:,? om)?\s+(?:\d+:\d+\s+)?(?:heeft|schreef)"i'	=> 2,
		'"^\s*Il.*?\d+.*?\d{4}.*?\d+:\d+.*?<?'.MAILPART.'@'.MAILPART.'>? ha"i'	=> 2,
		'"^\s*On .*'.MAILPART.'@'.MAILPART.'.*"i'				=> 2,
		'"^\s*'.MAILPART.'@'.MAILPART.' wrote:"i'				=> 2,
		'"^\s*(.+)\s+(>\s+To:\s+.*)$"i'						=> 3,
		'"^> To: <?'.MAILPART.'@'.MAILPART.'>?"i'				=> 2,
		'"^Yours Sincerely,"i'							=> 2,
		'"^> Subject: (.+)> Date"i'						=> 2,
		'"^\d+/\d+/\d+ .* <'.MAILPART.'@'.MAILPART.'>:\s*$"i'			=> 2,
		'"^\s*(?:Disclaimer:?|Verzonden vanuit (Mail|Outlook)(?: voor Windows 10)?|(?:Verstuurd vanaf mijn|Sent from(?: my)?) iP(?:hone|ad)|Sent from Yahoo Mail)\b"i'	=> 2,
		'"^\s*Verstuurd\s*door\s*[\w\d\s_]+"i'	=> 2,
		'"^Verzonden\s*via\s*BlackBerry"i'	=> 2,
		'"Sent\s*from\s*(?:Outlook|Mail)(?:<http[^>]*>)?\s*for\s*(?:Windows|Android)"i'					=> 2,
		'"^\s*Outlook\s*(?:voor|for)\s*(?:Android|iOS)<https://.*?downloaden"i'	=> 2,
		'"^\s*Outlook\s*(?:voor|for)\s*(?:Android|iOS) downloaden"i'		=> 2,
		'"^\s*Get\s*Outlook\s*for\s*(?:Android|iOS)"i'				=> 2,
		'"^\s*Sen[dt]\s*(?:by\s*iPhone|by\s*Android|from\s*Outlook\s*for\s*iOs)"i'	=> 2,
		'"^\s*Obtenir\h*Outlook\h*pour"i'					=> 2,
		'"\[https?://www\.artofdance\.nl/logo-art-of-dance\.png\]"i'		=> 2,
		'"^\s*>\s*Date: "i'							=> 2,
		'"^\s*(?:m[.,]?v[.,]?g[.,]?|gr)(?:\s+[\w\d\s\-()&;,]+)?[,.]?\s*$"i'		=> 2,
		'"^NOTE: The information in this email"i'				=> 2,
		'"^\.{10,}$"i'								=> 2,
		'"^\s*(?:(?:tha?n(?:x|ks))\s*(?:again|alvast)?)?(?:\s*en(?:\s*een)?)?[,\.\s]*Fijne?\s*(?:ochted|dag|middag|avond|nacht|weekend|maandag|dinsdag|woensdag|donderdag|vrijdag|zaterdag|zondag)[\.,!]\s*?$"i'			=> 2,
		'"^.*?Confidential & Copyright protected.?$"i'				=> 2,
		'!^Von:.*"[^"]*".*<\w+@partyflock\.nl>$!i'			=> 2,
		'!^M:\s*[^\s]+@[^\s]+!i' => 2,
		'"^['."\x1F".'\s\*]*(?:(?:Met\s*)?(?:(?:de|een\s*)?vriendelijk(st)?e?|hartelijke)?\s*groet(?:en)?|Hartelijk|Best|Alvast\s+een\s+(?:fijn|goed|prettig)(?:e\s*avond|weekend)|Groet(?:jess?|en)|liefs|Yours Sincerely)[\s!,.]*$"i'				=> 2,
		'"^\*?(?:Met\s*)?(?:vriendelijke|hartelijke)\s*groet(?:en|jes)?[\s/,;\.]+(?:(?:Best|Kind(?:ly)?|Friendly)\s*regards|Yours\s*sincerely)[,|\.\'\*]*$"i'	=> 2,
		'"^Gesendet\s*von\s*[\w\d\s]+\s*auf\s*\w+"i'	=> 2,
		'"^The information transmitted, including attachments, is intended only f"i'	=> 2,
		'"^--|BOKS$"i' => 2,
		'"^\*Thomas van Gulick\*$"' => 2,
		'"\*Official\s+website"' => 2,
		'"^Happy\s*Days!?$"i' => 2,
		'"^Gesendet mit der mobilen Mail App"i'					=> 2,
		'"^Thank\s*you\s*and\s*Greetz[,\.!]?"i'					=> 2,
		'"^Thanks\s*for\s*your\s*timely\s*response[,\.!]?"i'			=> 2,
		'"^Have\s*a\s*nice\s*(?:day|morning|evening)[,\.!]?"i'			=> 2,
		'"^(?:Thanks|Dank(?:\s*je\s*wel)?|Bedankt)(?:\s*alvast)?[\s\.,!]*$"i'						=> 2,
		'"^Ik\s*hoor\s*het\s*graag[\s\.,!]*$"i'					=> 2,
		'"^(?:Hoor\s*het\s*graag,?\s*)?al\s*vast\s*bedankt[!\s\.,]*"i'	 	=> 2,
		'"^Bedankt\s*voor\s*de\s*hulp[\s\.,!]*$"i'				=> 2,
		'"^(?:Thank\s*you\s*and\s*)?best\s*wishes[\s*,\.]*"i'			=> 2,
		'"^Groet(?:en|jes)[\s,\.\'!]*"i'					=> 2,
		'"^(?:Ik\s*hoop\s*het,\s*)(?:ik hoor het graag,\s*)?alvast\s*bedankt[\.,|]*"i'	=> 2,
		'"^(?:alvast\s*)?(?:bedankt|thanks|thn?x)(?:[,\.!]\s*|$)"i'						=> 2,
		'"^['."\x1F".'\s\*]*(Met\s+vr(?:iendelijke?)?\s+)?(?:Groe(?:ten|tjes|jtes)?|Greetz|Bij voorbaat dank|bvd)(?:\s\w+)?[!,\.]?\s*$"i'=> 2,
		'"^(?:Thank\h*you\h*and\h*)?\h*(?:With)?\h*(?:kind(?:ly)|best|friendly)\h*regards\h*(?:/\h*(?:Met\h*)?vriendelijke\h*groet(?:en|jes))?[,.|\']?\h*$"i'	=> 2,
		'"^(?:Thank\h*you\h*and\h*)?\h*(?:With)?\h*(?:Kind(?:ly)?|Best|Friendly)\h*Regards,?$"i'						=> 2,
		'"^\h*Met\h*(?:muzikale|circulaire)\h*groet(?:en)?[,\.]?\h*$"i'			=> 2,
		'"^Met vriendelijke groeten – Kind regards – Cordialement - Mit freundlichen Grüßen$"i'	=> 2,
		'"^De informatie verzonden met dit emailbericht is uitsluitend bestemd voor de geadresseerde"i' => 2,
#		'"^Groeten,$"i' => 2,
		'"^['."\x1F".'\s\*]*(?:'.
			'Ciao|'.
			'Cheers|'.
			'Groeten|'.
			'Fijne?\s*(?:dag|avond|weekend)(?:\s*nog)?(?:\s*gewenst)?|'.
			'grt?z?|'.
			'Muchos\s*gracias|'.
			'(?:grt|m\.?v\.?g\.?r?)(?:\s+[\w\s]+\.?)?|'.
			'all\s*the\s*best|'.
			'Met\s*vriendelijke\s*groet(?:en)?\s*/\s*With\s*kind\s*regards|'.
			'(?:(?:with\s+)?kind|best|warm|friendly|)\s*regards(?:\s*/\s*Met\s*(?:de\s*)?vriendelijk(?:st)?e?\s*groet(?:en)?)?(?:\s*/\s*Mit\s*freundlichen\s*Gr\x{00FC}ssen)?|'.
			'Gr(?:oet(?:en)?)?'.
		')[,\.]*(?:\s*\w+|\s*en\s*ik\s*verneem\s*graag)?[\s!,\.]*$"i'	=> 2,
		'"^\s*(?:Verzonden van(?:af)?(?: mijn)?|Sent from my) "i'					=> 2,

		# Avast signature indicator:
		'"https://www\.avast\.com/sig\-email"i'								=> 2,

		# Appic signature:
#		'"\n[\w\h]+\nClient Success Manager\n\nM:"'					=> 2,
		'"^Client Succes Manager$"'							=> 2,
		];

		foreach ($searchstrs as $search => $type) {
			if ($utf8) {
				$search .= 'u';
			}
			if (str_contains($search, "\x1F")) {
				$search = str_replace("\x1F", $utf8 ? '\x{200b}' : '', $search);
			}
			$__searches[$utf8][$search] = $type;
			$__searches_x[$utf8][$search] = $type;
		}
	}

	$searches = $__searches[$utf8];

	# from_name and from_first_name differs per message!

	static $__froms = [];
	$from_name = $message['FROM_NAME'];
	if ($from_name) {
		if (!isset($__froms[$from_name])) {
			$strpos = $utf8 ? 'mb_strpos' : 'strpos';
			$substr = $utf8 ? 'mb_substr' : 'substr';

			if (false !== ($bar_pos = $strpos($from_name, '|'))) {
				$from_name = $substr($from_name, 0, $bar_pos);
			}

			if (false !== ($space_pos = $strpos($from_name, ' '))) {
				$from_first_name = $substr($from_name, 0, $space_pos);
			}

			if ($utf8) {
				$from_name = win1252_to_utf8($from_name);
			}
			$from_name_search[] = '"^\s*'.preg_quote($from_name, '"').'[\s\n\.,!]*$"i'.$utf8_mod;

			if (isset($from_first_name)) {
				if ($utf8) {
					$from_first_name = win1252_to_utf8($from_first_name);
				}
				$from_name_search[] = '"^\s*'.preg_quote($from_first_name, '"').'[\s\n\.,!]*$"i'.$utf8_mod;
			}

			$__froms[$from_name] = $from_name_search;
		}
		foreach ($__froms[$from_name] as $from_name_search) {
			$searches[$from_name_search] = 2;
		}
	}

	$cnt = 0;

	foreach ($searches as $srch => $type) {
		switch ($type) {
		case 1:
			if (preg_match($srch, $line, $match)) {
				$line = $match[1].$match[2]."\n\n".$match[3];
				return QUOTE_START_STRIP;
			}
		 	break;
		case 2:
			if (preg_match($srch, $line, $match)) {
/*				print_rr([
				'cnt'	=> $cnt,
					'line'	=> $line,
					'srch'	=> $srch,
					'match'	=> $match,
				]);*/
				return QUOTE_START_STRIP;
			}
			break;
		case 3:
			if (preg_match($srch, $line, $match)) {
				$line = $match[1];
				$quoteline = $match[2];
				return false;
			}
			break;
		case 4:
			if (preg_match($srch, $line)) {
				return QUOTE_START_STRIP;
			}
			static $previous_line = null;
			if ($previous_line) {
				$check = $previous_line.' '.$line;
				if (preg_match($srch, $check)) {
					return QUOTE_START_PREV;
				}
			}
			$previous_line = $line;
			break;
		}
		++$cnt;
	}
	return false;
}

function pass_relationlist(?adday $donerels): string {
	if (empty($donerels)) {
		return '';
	}
	return ';RELATIONID'.(count($donerels) > 1 ? 'S='.implode(',', $donerels) : '='.current($donerels));
}

function show_advertising_link(): void {
	layout_open_menuitem();
	include_js('js/advertisinginfo');
	?><span onclick="get_advertising_information(this)" class="ptr unhideanchor"><?
		__C('action:send_advertising_information')
	?></span><?
	layout_close_menuitem();
}

function split_message(array &$message, ?array $attinfo = []): array {
	if (detect_utf8($message)) {
		$message['BODYUTF8'] = $message['BODY'];
	} elseif (
		!empty($message['BODYUTF8'])
	&&	!empty($message['HEADERS'])
	&&	str_contains($message['HEADERS'],'SquirrelMail')
	&&	str_contains($message['BODYUTF8'], "\x0A\xA0")
	) {
		$message['BODYUTF8'] = null;
	}
	$BODY = ($utf8 = $message['BODYUTF8']) ? $message['BODYUTF8'] : $message['BODY'];

	if (!$BODY) {
		return ['',null,null,!empty($message['BODYUTF8']),null];
	}

	$utf8_mod = $utf8 ? 'u' : '';

	while ($BODY && preg_match('"^\s*(\s*>[^\n]*)*\s*$"is'.$utf8_mod, $BODY)) {
		$lines = [];
		foreach (explode("\n", $BODY) as $line) {
			$line = mytrim ($line, utf8: $utf8);
			$line = myltrim($line, '> ', $utf8);
			$line = mytrim ($line, utf8: $utf8);
			$lines[] = $line;
		}
		$BODY = mytrim(implode("\n", $lines), null, $utf8);
	}

	$FROM_EMAIL = $message['FROM_EMAIL'];
	$CTMSGID = $message['CTMSGID'];

	$news = [];
	$inquote = false;
	$linecnt = 0;
	$quotedlines = 0;
	if ($checkbadbody =
		$FROM_EMAIL
	&&	isset($attinfo[$CTMSGID])
	&&	(	str_contains($FROM_EMAIL,'@live.nl')
		||	str_contains($FROM_EMAIL,'@hotmail.com')
		)
	) {
		$gt = ord('>');
	}
	$quoted = [];
	$DEAR = '';

	$strlen = $utf8 ? 'mb_strlen' : 'strlen';

	if (preg_match('"^\s*(?<dear>'.
		'Goe[di]e[n\s]*(?:middag|dag|morgen|avond)|'.
		'H\xE9|'. # é
		'He[ey]?|'.
		# 'Goedendag|'. NOTE: already in first group opart?
		'Hallo|'.
		'Beste?|'.
		'Dag|'.
		'Dear|'.
		'Geachte|'.
		'H[oa]*i|'.
		'Morning|'.
		'Evenening|'.
		'Good\s*(?:morning|day|evening|night)'.
	')\s*(?<name>[^\r\n,.!?]+)?[\r\n,.!?]*(?<tail>.*)$"is'.$utf8_mod, $BODY, $match)) {
		$DEAR = mytrim($match['dear'], null, $utf8);
		$BODY = '';
		if ($strlen($match['name']) > 15) {
			$BODY = $match['name']."\n";
		} else {
			$DEAR .= $match['name'];
		}
		$BODY .= my_ucfirst(myltrim(mytrim($match['tail'], null, $utf8), '.', $utf8), $utf8);
	}

	$lines = preg_split('"\r?\n"'.$utf8_mod, $BODY) ?: [];

	foreach ($lines as $i => $line) {
		if ($checkbadbody) {
			$chrs = count_chars($line);
			if ($chrs[$gt] > 3) {
				$checkbadbody = 1;
				mail_log('checkbadbody', item: get_defined_vars());
				break;
			}
		}
		if (!$inquote) {
			# line 0 is never a quote 😊
			$quoteline = null;
			if (!($qtype = $i === 0 ? false : detect_quote($i, $message, $line, $quoteline, $utf8))) {
				$news[] = $line;
				if (!$quoteline) {
					if (!empty($line)) {
						++$linecnt;
					}
					continue;
				}
				$line = $quoteline;
			} else {
				if ($linecnt) {
					$inquote = true;
				}
				if ($qtype === QUOTE_START_PREV) {
					$oldline = array_pop($news);
					$quoted[] = $oldline;
				}
			}
		}
		$quoted[] = $line;
		++$quotedlines;
	}
	$quotedmessage = implode("\n",$quoted);
	$newmessage = implode("\n",$news);

	if ($checkbadbody !== 1) {
		if (str_contains($newmessage, 'BODY {')) {
			$newmessage = preg_replace('"\s+BODY\s+{.*?}\s*"'.$utf8_mod,'',$newmessage);
		}
		$BODY = mytrim($newmessage, '-*', $utf8);

	} else {
		foreach ($attinfo[$CTMSGID] as $att) {
			if ($att['FILENAME'] === 'message.html'
			&&	($data = db_single('contact_ticket_attachment_data', 'SELECT UNCOMPRESS(DATA) FROM data_db.contact_ticket_attachment_data WHERE DATAID='.$att['DATAID']))
			) {
				$newmessage = null;
				$linecnt = 0;
				foreach (explode("\n",html_entity_decode(
					preg_replace('"\n{2,}"'.$utf8_mod,"\n",
					strip_tags(
						preg_replace('"<style>.*</style>"ism'.$utf8_mod,'',
						preg_replace('"<hr ?/? ?>"i'.$utf8_mod, "\n-xxSPAMxx-\n",
						preg_replace('"<br ?/? ?>"i'.$utf8_mod, "\n", $data)))
					)
					)
					)) as $line
				) {
					if (!$line
					||	(	$line[0] === '>'
						&&	isset($line[1])
						&&	$line[1] === ' '
						)
					) {
						continue;
					}
					if ($line === '-xxSPAMxx-') {
						break;
					}
					if ($line !== "\n") {
						++$linecnt;
					}
					$newmessage .= $line."\n";
				}
				if ($linecnt) {
					$BODY = trim($newmessage);
				}
				break;
			}
		}
	}
	return [$BODY,$quotedmessage,$quotedlines,!empty($message['BODYUTF8']),$DEAR];
}

function show_forward_form(array $ticket): bool {
	require_once '_crew.inc';
	$crewlist = get_crewlist();
	if (!($useroptions = memcached_simple_hash(['rights','rights_forum','user_account'],'
		SELECT user_account.USERID, HOLIDAYID
		FROM user_account
		LEFT JOIN holiday
			 ON holiday.STATUS = "active" 
			AND holiday.USERID = user_account.USERID
		WHERE user_account.STATUS = "active"
		  AND user_account.USERID IN ('.implode(', ', $crewlist).')
		ORDER BY ISNULL(HOLIDAYID) DESC, NICK'
	))) {
		return false;
	}

	$ok_admins = [];
	$noright_admins	= $ok_admins;
	$holiday_admins	= $ok_admins;
	$element		= $ticket['ELEMENT'];
	$id 			= $ticket['ID'];

	foreach ($useroptions as $userid => $holidayid) {
		if ($userid === CURRENTUSERID
		||	$userid === $ticket['WITHUSERID']
		) {
			continue;
		}
		$nick = memcached_nick($userid);
		if ($holidayid) {
			$holiday_admins[$userid] = $nick;
		} elseif ($element ? ($id ? may_change_element($element, $id, $userid) : have_admin($element, $userid)) : false) {
			$ok_admins[$userid] = $nick;
		} else {
			$noright_admins[$userid] = $nick;
		}
	}

	?><span id="forwardoptions" class="hidden"><?
	?><select name="TO_USERID" class="placeholder"><?
		?><option value="" selected disabled><?= __('action:forward_to') ?></option><?

	foreach ([
		''									=> $ok_admins,
		__('banneraction:without_rights')	=> $noright_admins,
		__('status:on_holiday')				=> $holiday_admins,
	] as $label => $admins) {
		if (!$admins) {
			continue;
		}
		if ($label) {
			?><optgroup label="<?= escape_specials($label) ?>"><?
		}
		natcasesort($admins);
		foreach ($admins as $userid => $nick) {
			?><option value="<?= $userid ?>"><?= escape_specials($nick) ?></option><?
		}
		if ($label) {
			?></optgroup><?
		}
	}
	?></select> <?
	?><input type="submit" name="FORWARD" value="<?= __('action:forward'); ?>" /><?
	?></span><?
	return true;
}

const TICKET_MOVE_SHOW_HIDDEN_STORAGE	= 0;
const TICKET_MOVE_SHOW_IN_RESPONSE	= 1;

# returns true if form block is visible

function show_move_form(array $ticket, int $position, ?array $template = null): bool {
	$orig_element = $ticket['ELEMENT'];
	$orig_id	  = $ticket['ID'];

	if ($visible
	=	$template
	&&	!empty($template['ELEMENT'])
	&&	!empty($template['ID'])
	&&	(	$template['ELEMENT'] !== $ticket['ELEMENT']
		||	$template['ID']		 !== $ticket['ID']
		)
	) {
		# template visible, so move form parts visible in response form, not the hidden storage
		if ($position !== TICKET_MOVE_SHOW_IN_RESPONSE) {
			return false;
		}
		$element = $template['ELEMENT'];
		$id		 = $template['ID'];
	} else {
		# no template directly visible, store in form so, show hidden storage
		if ($position !== TICKET_MOVE_SHOW_HIDDEN_STORAGE) {
			return false;
		}
		$element = $orig_element;
		$id		 = $orig_id;
	}

	$elements = get_contact_element_names(capitalize: true);

	if ($id) switch ($element) {
	case 'user':
	case 'user_text':
	case 'helpdesk':
		$trans = 'user,user_text,helpdesk='.$id;
		break;
	case 'ad':
		if ($bannerid = db_single('ad','SELECT BANNERID FROM ad WHERE ADID='.$id)) {
			$trans = 'ad='.$id.';banner='.$bannerid;
		}
		break;
	case 'banner':
		if ($adid = db_single('ad','SELECT MAX(ADID) FROM ad WHERE BANNERID='.$id)) {
			$trans = 'ad='.$adid.';banner='.$id;
		}
		break;
	case 'message':
		if ($topicid = db_single('message','SELECT TOPICID FROM message WHERE MESSAGEID='.$id)) {
			$trans = 'message='.$id.';topic='.$topicid;
		}
		break;
	case 'contest':
		if ($partyid = db_single('contest','SELECT PARTYID FROM contest WHERE CONTESTID = '.$id)) {
			$trans = 'contest='.$id.';party='.$partyid;
		}
		break;
	case 'news':
		if ($newsadid = db_single('newsad','SELECT NEWSADID FROM newsad WHERE NEWSID='.$id.' ORDER BY NEWSADID DESC LIMIT 1')) {
			$trans = 'news='.$id.';newsad='.$newsadid;
		}
		break;
	case 'newsad':
		if ($newsid = db_single('news','SELECT NEWSID FROM newsad WHERE NEWSADID='.$id)) {
			$trans = 'newsad='.$id.';news='.$newsid;
		}
		break;
	}

	?><span<?
	?> id="moveoptions"<?
	?> class="relative<?
	if (!$visible) {
		?> hidden<?
	}
	?>"><?

	?><select<?
	if (isset($trans)) {
		?> data-idtranslation="<?= $trans ?>"<?
	}
	?> onchange="changeMoveOption(this,'<?= $element ?>',<?= $id ?>)"<?
	?> data-original="<?= $orig_element ?>"<?
	?> name="ELEMENT"<?
	?> onkeydown="Pf_onEnterSubmit(event, 'MOVE')"<?
	?>><?
		?><option value=""></option><?

		foreach ($elements as $tmpelement => $name) {
			if (($junk = (str_starts_with($tmpelement, 'junk')))
			&&	$ticket['MSGCNT'] > 1
			||	!$ticket['LASTMAIL']
			&&	$junk
			) {
				continue;
			}
			?><option<?
			if ($tmpelement === $element) {
				?> selected<?
			}
			?> value="<?= $tmpelement; ?>"><?= $name; ?></option><?
		}
	?></select> <?

	show_input([
		'data-original'	=> $orig_id ?: '',
		'type'		=> 'number',
		'class'		=> 'right id',
		'id'		=> 'moveid',
		'min'		=> 0,
		'max'		=> 4294967296,
		'name'		=> 'ID',
		'onkeydown'	=> "Pf_onEnterSubmit('MOVE')",
		'value'		=> $id ?: null
	]);
	?> <?
	show_input([
		'class'			=> $visible ? null : 'hidden',
		'_data-errormsg'=> __('contact:error:move_destination_unchanged_LINE'),
		'type'			=> 'submit',
		'name'			=> 'MOVE',
		'value_escaped'	=> __('action:move')
	]);

	?><br /><span id="keeplockfloat"></span><?

	?></span><?

	return $visible;
}

function defaultExpires(?string $element = ''): array|int {
	# Satisfy EA inspection
	static $__expires = (array)(db_simple_hash('contactreturn', 'SELECT ELEMENT, EXPIRES FROM party_db.contactreturn') ?: []);
	if (!$element) {
		return $__expires;
	}
	return $__expires[$element] ?? $__expires[''] ?? (7 * ONE_DAY);
}

function changeExpires(): void {
	# 0 - 7 == hours 1 to 8
	# 8 - 21 == days 1 to 14

	$js = 'this.form.RETURN.selectedIndex=(function(value){switch(value){';
	foreach (defaultExpires() as $element => $expires) {
		$js .= 'case \''.$element.'\':return '.(7 + $expires / ONE_DAY).';';
	}
	echo $js;
}

function show_return_selection(
	int|string|null	$arg		= null,
	?string			$postfix	= null,
	?string			$class		= null
): void {
	$expires = defaultExpires($arg ?: '');

	global $__mins, $__hour;

	?><select<?
	if ($class) {
		?> class="<?= $class ?>"<?
	}
	?> name="RETURN"<?= $postfix ?>><?

	for (	$hours  = 1;
			$hours <= 8;
			++$hours
	) {
		$delay = $hours * ONE_HOUR;
		$expstamp = CURRENTSTAMP + $delay - $__mins * 60;
		?><option<?
		if ($expires === $delay) {
			?> selected<?
		}
		?> value="<?= $delay ?>,<?= $expstamp ?>"><?=
			__('duration:x_hours', ['HOURS' => $hours]);
		?>, <?
		$back = $__hour + $hours;
		if ($back >= 24) {
			/** @noinspection SummerTimeUnsafeTimeManipulationInspection */
			$back -= 24;
		}
		echo $back ?>:00<?
		?></option><?
	}

	for (	$days  = 1;
		$days <= 31;
		++$days
	 ) {
		$delay = $days * ONE_DAY;
		$expstamp = strtotime('today +'.$days.' days 8:00');
		?><option<?
		if ($expires === $delay) {
			?> selected<?
		}

		?> value="<?= $delay ?>,<?= $expstamp ?>"><?= __('duration:x_days', ['DAYS' => $days])
		?> (<? _datedaytime_display($expstamp, short: true) ?>)<?
		?></option><?
	}

	for (	$weeks  = 4;
		$weeks <= 12;
		++$weeks
	) {
		$delay = $weeks * ONE_WEEK;
		$expstamp = strtotime('this monday +'.$weeks.' weeks 8:00');
		?><option<?
		if ($expires === $delay) {
			?> selected<?
		}
		?> value="<?= $delay ?>,<?=$expstamp ?>"><?= __('duration:x_weeks', ['WEEKS' => $weeks])
		?> (<?  _datedaytime_display($expstamp, short: true) ?>)</option><?
	}
	?></select><?
}

function store_via_email(): bool|null {
	if (!isset($_POST['REMEMBERVIA'])) {
		return null;
	}
	if (false === have_element($_POST, 'EMAILTYPE', ['','profile','other'], strict: true)) {
		return false;
	}
	return	db_replace('contactusemail','
		REPLACE INTO contactusemail SET
			EMAIL	= "'.addslashes($_POST['EMAIL'] ?? '').'",
			ANSWER	= "'.$_POST['EMAILTYPE'].'",
			USERID	= '.CURRENTUSERID
	);
}

function via_email_too(?string $recommended_email = null, ?bool $new_ticket = false): void {
	$setting = db_single_array('contactusemail', 'SELECT ANSWER, EMAIL FROM contactusemail WHERE USERID = '.CURRENTUSERID);
	[$answer, $email]= $setting ?: ['profile', null];

	if (!empty($recommended_email)) {
		$answer =
			($recommended_email === db_single_string('user','SELECT EMAIL FROM user WHERE USERID = '.CURRENTUSERID))
		?	'profile'
		:	'other';
	}

	?><select name="EMAILTYPE" onchange="changeEmailType(this)" data-saved="<?= $answer ?>"><?
		?><option value=""><?= __('answer:no') ?></option><?
		?><option<?
		if ($answer === 'profile') {
			?> selected<?
		}
		?> value="profile"><?= __('contact:emailtoo:use_from_profile') ?></option><?
		?><option<?
		if ($answer === 'other') {
			?> selected<?
		}
		?> value="other"><?= __('contact:emailtoo:use:') ?></option><?

	?></select><?
	show_input([
		'class'			=> $answer !== 'other' ? 'hidden' : '',
		'style'			=> 'width:20em',
		'disabled'		=> true,
		'type'			=> 'email',
		'data-valid'	=> 'working-email',
		'value'			=> $email,
		'data-orig'		=> $email,
		'name'			=> 'EMAIL',
		'onkeyup'		=> 'changeREmail(this)',
	]);
	if (!empty($new_ticket)) {
		$hidden = true;
		?> <label class="<?
		if ($hidden) {
			?>hidden <?
		}
		?>not-hilited"><?
		show_input([
			'type'	=> 'checkbox',
			'class'	=> 'upLite',
			'name'	=> 'REMEMBERVIA',
		]);
		?> <?= __('action:remember');
		?></label><?
	}
}

function have_other_emails(int $ticketid, string $email) {
	return db_single('contact_ticket_mail','
		SELECT 1
		FROM contact_ticket_mail
		JOIN contact_ticket_message USING (TICKETID,CTMSGID)
		WHERE TICKETID='.$ticketid.'
		  AND CIMID=0
		  AND CASE DIRECTION
		  WHEN "touser" THEN TO_EMAIL!="'.addslashes($email).'"
		  WHEN "toadmin" THEN FROM_EMAIL!="'.addslashes($email).'"
		  ELSE 0 END
		LIMIT 1'
	);
}

function get_camreq_request_info($ticket,$with_userid,$mailoptions) {
	if ((	!$mailoptions
		||	!$mailoptions[0]['EMAIL']
		&&	!$mailoptions[0]['NAME']
		)
	&&	!$with_userid
	) {
		return null;
	}
	$reqinfo = db_single_array(['camerarequest_ticket', 'organization', 'location'],'
		SELECT	ASKTYPE, ASKID,
			COALESCE(organization.CAMREQ_NAME,   location.CAMREQ_NAME),
			COALESCE(organization.CAMREQ_EMAIL,  location.CAMREQ_EMAIL),
			COALESCE(organization.CAMREQ_USERID, location.CAMREQ_USERID)
		FROM camerarequest_ticket AS ct
		LEFT JOIN organization
		  ON ASKTYPE = "organization"
		 AND ASKID = ORGANIZATIONID
		LEFT JOIN location
		  ON ASKTYPE = "location"
		 AND ASKID = LOCATIONID
		WHERE ASKTYPE IN ("location", "organization")
		  AND DONE = 0
		  AND ct.TICKETID = '.$ticket['TICKETID'].'
		GROUP BY ct.TICKETID'
	);
	if (!$reqinfo) {
		return $reqinfo;
	}
	if ($with_userid
	&&	$ticket['ID']
	&&	$reqinfo[0] === 'offer'
	) {
			($party_orgs = db_same_hash('connect','SELECT ASSOCID FROM connect WHERE ASSOCTYPE="organization" AND MAINTYPE="party" AND MAINID='.$ticket['ID']))
		&&	($orgs = am_employee('organization',$party_orgs,$with_userid));

			($party_loc = db_single('party','SELECT LOCATIONID FROM party WHERE PARTYID='.$ticket['ID']))
		&&	($loc = am_employee('location',$party_loc,$with_userid));

		$total = 0;
		if (!empty($orgs)) {
			$total += count($orgs);
		}
		if (!empty($loc)) {
			++$total;
		}
		if (!$total || $total > 1) return null;
		if (!empty($orgs)) {
			$element = 'organization';
			[$id] = keyval($orgs);
		} else {
			$element = 'location';
			$id = $loc;
		}
		$reqinfo = db_single_array($element,'
			SELECT "'.$element.'",'.$id.',CAMREQ_NAME,CAMREQ_EMAIL,CAMREQ_USERID
			FROM '.$element.'
			WHERE '.$element.'ID='.$id
		);
	}
	return $reqinfo;
}
function show_contest_history($ticket, $mailoptions) {
	$checkemails =
	$checkuserids =
	$checkrelationids = [];

	function add_userid($arg, &$checkuserids, &$checkemails, &$checkrelationids) {
		if (!is_array($arg)) {
			$arg = [$arg => true];
		}
		$checkuserids += $arg;
		$extraemails = db_simple_hash('user','
			SELECT DISTINCT LOWER(CONVERT(EMAIL USING latin1)),1
			FROM user
			WHERE USERID IN ('.implodekeys(',',$arg).')'.(
				$checkemails
			?	' AND EMAIL NOT IN ('.stringsimplodekeys(',',$checkemails).')'
			:	null)
		);
		if ($extraemails) {
			add_email($extraemails,$checkuserids,$checkemails,$checkrelationids);
		}
		$extrarelationids = db_simple_hash('relationmember','
			SELECT DISTINCT RELATIONID,1
			FROM relationmember
			WHERE USERID IN ('.implodekeys(',',$arg).')'.(
				$checkrelationids
			?	' AND RELATIONID NOT IN ('.implodekeys(',',$checkrelationids).')'
			:	null)
		);
		if ($extrarelationids) {
			add_relationid($extrarelationids,$checkuserids,$checkemails,$checkrelationids);
		}
	}
	function add_email($arg,&$checkuserids,&$checkemails,&$checkrelationids) {
		if (!is_array($arg)) {
			$arg = array($arg=>true);
		}
		$checkemails += $arg;
		$extrauserids = db_simple_hash('user','
			SELECT DISTINCT USERID,1
			FROM user
			WHERE EMAIL IN ('.stringsimplodekeys(',',$arg).')'.(
				$checkuserids
			?	' AND USERID NOT IN ('.implodekeys(',',$checkuserids).')'
			:	null)
		);
		if ($extrauserids) {
			add_userid($extrauserids,$checkuserids,$checkemails,$checkrelationids);
		}
		$extrarelationids = db_simple_hash('relation','
			SELECT DISTINCT RELATIONID,1
			FROM relation
			WHERE CONTACTEMAIL IN ('.stringsimplodekeys(',',$arg).')'.(
				$checkrelationids
			?	' AND RELATIONID NOT IN ('.implodekeys(',',$checkrelationids).')'
			:	null)
		);
		if ($extrarelationids) {
			add_relationid($extrarelationids, $checkuserids, $checkemails, $checkrelationids);
		}
	}
	function add_relationid($arg,&$checkuserids,&$checkemails,&$checkrelationids) {
		$checkrelationids += $arg;
		$extrauserids = db_simple_hash('relationmember','
			SELECT DISTINCT USERID,1
			FROM relationmember
			WHERE RELATIONID IN ('.implodekeys(',',$arg).')'.(
				$checkuserids
			?	' AND USERID NOT IN ('.implodekeys(',',$checkuserids).')'
			:	null)
		);
		if ($extrauserids) {
			add_userid($extrauserids,$checkuserids,$checkemails,$checkrelationids);
		}
		$extraemails = db_simple_hash('relation','
			SELECT DISTINCT CONTACTEMAIL, 1
			FROM relation
			WHERE RELATIONID IN ('.implodekeys(',',$arg).')'.(
				$checkemails
			?	' AND CONTACTEMAIL NOT IN ('.stringsimplodekeys(',',$checkemails).')'
			:	null)
		);
		if ($extraemails) {
			add_email($extraemails,$checkuserids,$checkemails,$checkrelationids);
		}
	}
	if ($ticket['WITHUSERID']) {
		add_userid($ticket['WITHUSERID'],$checkuserids,$checkemails,$checkrelationids);
	}
	if ($ticket['ISMAIL']) {
		if ($mailoptions) {
			foreach ($mailoptions as $mailoption) {
				$extraemails[strtolower($mailoption['EMAIL'])] = true;
			}
			add_email($extraemails,$checkuserids,$checkemails,$checkrelationids);
		}
	}
	$baduserids = memcached_multirow_hash(
		array('contest','party','contact_ticket'),'
		SELECT DISTINCT WITHUSERID,TOLD,CONCAT(CONTESTID,":",CLOSE_STAMP)
		FROM contest
		JOIN contact_ticket USING (TICKETID)
		LEFT JOIN party USING (PARTYID)
		WHERE CLOSED=1
		  AND WITHUSERID!=0
		  AND WITHUSERID IN ('.(implodekeys(',',$checkuserids) ?: 0).')
		  AND contest.TICKETID!=0
		  AND (SELECT COUNT(*) FROM contestresponse cr WHERE cr.CONTESTID = contest.CONTESTID)
		  AND (	ISNULL(party.PARTYID)
			OR CANCELLED=0 AND ACCEPTED=1 AND MOVEDID=0)
		  AND CLOSE_STAMP>'.($threeyearsago = TODAYSTAMP - ONE_YEAR)
	);
	$bademails = memcached_multirow_hash(
		array('contest','party','contact_ticket_mail','contact_ticket_message'),'
		SELECT DISTINCT LOWER(CONVERT(FROM_EMAIL USING latin1)),TOLD,CONCAT(CONTESTID,":",CLOSE_STAMP)
		FROM contest
		JOIN contact_ticket_mail AS ctm USING (TICKETID)
		JOIN contact_ticket_message USING (TICKETID,CTMSGID)
		LEFT JOIN party USING (PARTYID)
		WHERE CLOSED=1
		  AND FROM_EMAIL!=""
		  AND FROM_EMAIL NOT LIKE "mailer-daemon@%"
		  AND FROM_EMAIL IN ('.(stringsimplodekeys(',',$checkemails) ?: '""').')
		  AND DIRECTION="toadmin"
		  AND contest.TICKETID!=0
		  AND (SELECT COUNT(*) FROM contestresponse cr WHERE cr.CONTESTID = contest.CONTESTID)
		  AND (	ISNULL(party.PARTYID)
			OR CANCELLED=0 AND ACCEPTED=1 AND MOVEDID=0)
		  AND CLOSE_STAMP>'.$threeyearsago
	);
	if ($baduserids === false
	||	$bademails === false
	) {
		return;
	}

	$is_admin = have_admin('contest');
	$bads = array();
	$shows = array();
	foreach ($checkuserids as $userid => $null) {
		if (isset($baduserids[$userid])
		&&	false !== ($seenstamp = $is_admin ? db_single('contestseen_userid','SELECT SEENSTAMP FROM contestseen_userid WHERE USERID='.$userid) : 0)
		) {
			foreach ($baduserids[$userid] as $told => $tolds) {
				foreach ($tolds as $info) {
					[$contestid,$stamp] = explode(':',$info);
					if (!$seenstamp
					||	$stamp > $seenstamp
					) {
						$shows[$contestid][$userid] = array($told,$stamp);
						if (!$told) {
							$bads[$contestid] = true;
						}
					}
				}
			}
		}
	}
	foreach ($checkemails as $email => $null) {
		if (isset($bademails[$email])
		&&	false !== ($seenstamp = $is_admin ? db_single('contestseen_email','SELECT SEENSTAMP FROM contestseen_email WHERE EMAIL="'.addslashes($email).'"') : 0)
		) {
			foreach ($bademails[$email] as $told => $tolds) {
				foreach ($tolds as $info) {
					[$contestid,$stamp] = explode(':',$info);
					if (!$seenstamp
					||	$stamp > $seenstamp
					) {
						$shows[$contestid][$email] = array($told,$stamp);
						if (!$told) {
							$bads[$contestid] = true;
						}
					}
				}
			}
		}
	}
	if (!$bads) {
		return;
	}
	include_js('js/contactcontests');
	?><div id="contestseen"><?
	layout_open_box('white');
	layout_box_header(Eelement_plural_name('unfinished_contest'));
	if ($is_admin) {
		?><div class="block"><?
	} else {
		?><div class="r"><?
		expand_collapse('contestlist');
		?></div><?
		?><div class="ptr block" onclick="<? expand_collapse_clicker('contestlist') ?>"><?
	}
	echo __(	$is_admin
		?	'contact:info:unfinished_contests_TEXT'
		:	'contact:info:unfinished_contests_(non_admin)_TEXT',
		DO_UBB,['BADCNT'=>count($bads),'TOTALCNT'=>count($shows)]
	);
	?></div><?
	krsort($shows);
	layout_open_table((!$is_admin ? 'hidden ' : null).'hha default','contestlist');
	?><tr><?
	?><th class="right rpad"><?= Eelement_name('date') ?></th><?
	?><th class="hpad"><?= Eelement_name('contest') ?></th><?
	?><th class="lpad"><?= Eelement_name('contact_person') ?></th><?
	?></tr><?
	foreach ($shows as $contestid => $contacts) {
		$dspcontacts = array();
		foreach ($contacts as $arg => $info) {
			[$told,$close_stamp] = $info;
			ob_start();
			if (is_number($arg)) {
				$contest_userids[$arg] = $arg;
				echo get_element_link('user',$arg);
			} else {
				$contest_emails[$arg] = escape_specials($arg);
				print_email_link($arg);
			}
			$dspcontacts[] = ob_get_clean();
		}
		?><tr class="<?= $told ? 'connected' : 'unconnected' ?>"><?
		?><td class="right rpad"><? _date_display($close_stamp) ?></td><?
		?><td class="hpad"><?= get_element_link('contest',$contestid) ?></td><?
		?><td class="lpad"><?= implode(', ',$dspcontacts) ?></td><?
		?></tr><?
	}
	layout_close_table();
	layout_close_box();
	if (!$is_admin) {
		return;
	}
	?><div class="funcs"><?
	?><span onclick="markSeen(<?=
		isset($contest_userids)
	?	'['.implode(',',$contest_userids).']'
	:	'null'
	?>,<?=	isset($contest_emails)
	?	'['.stringsimplode_noslash(',',$contest_emails,"'").']'
	:	'null'
	?>)" class="unhideanchor"><?= __C('action:mark_seen'); ?></span><?
	?></div><?
	?></div><?
}
