<?php

declare(strict_types=1);

const CAMREQ_CELLS	= 1;
const CAMREQ_ROWS	= 2;
const CAMREQ_BLOCK	= 3;

function show_camreq_specs(array $request, int $type): void {
	/** @noinspection PhpRedundantOptionalArgumentInspection */
	extract($request, \EXTR_OVERWRITE);
	$cells = $type === CAMREQ_CELLS;
	$rows = $type === CAMREQ_ROWS;
	$horiz = $cells || $type === CAMREQ_BLOCK;
	if ($GLISTPLUS !== null
	&&	($cells || $rows)
	) {
		if ($horiz) {
			if ($cells) {
				layout_next_cell(class: 'right rpad smallest');
			} else {
				echo ' ';
			}
			if ($GLISTPLUS) {
				?>+<?= $GLISTPLUS ?> <?
				show_camreq_icon('man');//,false,'+'.$GLISTPLUS);
			}
		} elseif ($rows || $GLISTPLUS) {
			layout_restart_row();
			echo Eelement_name('list');

			layout_field_value();
			show_camreq_icon('man');
			?> +<? echo $GLISTPLUS;
		}
	}

	if ($CONSUMPTIONS !== null
	||	$cells
	) {
		if ($horiz) {
			if ($cells) {
				layout_next_cell(class: 'right rpad smallest');
			} else {
				echo ' ';
			}

			if ($CONSUMPTIONS !== null) {
				if ($CONSUMPTIONS) {
					echo $CONSUMPTIONS,' ';
				}
				show_camreq_icon('drink',null,$CONSUMPTIONS ? null : __('answer:yes'));
			}
		} elseif ($CONSUMPTIONS !== null) {
			layout_restart_row();
			echo Eelement_plural_name('free_consumption');

			layout_field_value();
			show_camreq_icon('drink');
			echo ' ',($CONSUMPTIONS ?: __('answer:yes'));
		}
	}
	if ($TRAVEL_EXPENSES !== null
	||	$cells
	) {
		if ($horiz) {
			if ($cells) {
				layout_next_cell(class: 'right rpad smallest');
			} else {
				echo ' ';
			}
			if ($TRAVEL_EXPENSES !== null) {
				show_camreq_icon('car',!$TRAVEL_EXPENSES ? 'light' : null,!$TRAVEL_EXPENSES ? __('answer:maybe') : ($TRAVEL_EXPENSES === 2 ? 'Partyflock' : element_name('organization')));
			}
		} elseif ($TRAVEL_EXPENSES !== null) {
			layout_restart_row();
			echo Eelement_name('travel_allowance');

			layout_field_value();
			show_camreq_icon('car',!$TRAVEL_EXPENSES ? 'light' : null);
			?> <?
			switch ($TRAVEL_EXPENSES) {
			case 0:	echo __('answer:maybe'); break;
			case 1: echo element_name('organization'); break;
			case 2: ?>Partyflock<? break;
			}
		}
	}
	if ($REWARD_EXTERNAL !== null
	||	$REWARD_FLOCK	!== null
	||	$cells
	) {
		if ($horiz) {
			if ($cells) {
				layout_next_cell(class: 'right rpad smallest');
			} else {
				echo ' ';
			}

			$reward = $REWARD_EXTERNAL + $REWARD_FLOCK;

			if ($REWARD_EXTERNAL !== null
			||	$REWARD_FLOCK	!== null
			||	$reward
			) {
				if ($reward) {
					echo $reward ?> <?
				}
				show_camreq_icon(
					'money',
					!$reward ? 'light' : null,
					$reward ? null : __('answer:maybe')
				);
			}
		} else {
			foreach ([
				element_name('organization')	=> $REWARD_EXTERNAL,
				'Partyflock'			=> $REWARD_FLOCK
			] as $desc => $amount) {
				if ($amount === null) {
					continue;
				}
				layout_restart_row();
				echo Eelement_name('compensation'); ?> (<?= $desc ?>)<?

				layout_field_value();
				show_camreq_icon('money');
				?> <? echo $amount ?: __('answer:maybe');
			}
		}
	}
}
function show_camreq_icon(string $which, ?string $class = null, ?string $title = null): void {
	?><img<?
	if ($title) {
		?> title="<?= $title ?>"<?
	}
	?> class="<?
	if ($class) {
		echo $class,' ';
	}
	$icon = $which === 'extern' ? 'net' : $which;
	?>icon lower" src="<?= STATIC_HOST ?>/images/<?= $icon,($which === 'extern' ? is_high_res() : null) ?>.png" /><?
}
# request: true then employee, false then photographer

const AS_EMPLOYEE = 0xDEADCAFE;

function show_camreq_menu(array $party, ?int $request = null): void {
	if ($_REQUEST['sELEMENT'] === 'camera'
	&&	!have_admin('photographer')
	) {
		return;
	}

	$href = '/camera/'.$party['PARTYID'];

	if (($is_camadm = have_admin('camerarequest'))
	||	$request
	&&	$request === AS_EMPLOYEE
	) {
		$camera = db_single('camera','SELECT MUSERID FROM camera WHERE PARTYID='.$party['PARTYID']);
		if (!$is_camadm) {
			if ($camera === false) {
				return;
			}
			if ($party['STAMP'] < CURRENTSTAMP
			||	$camera !== null
			&&	$camera !== CURRENTUSERID
			) {
				?><div class="r block"><?
				?><a class="seembutton seemtext" href="<?= $href ?>"><?=
					Eelement_name('camerarequest') ?>: <?=
					__('action:show') ?></a><?
				?></div><?
				return;
			}
		}
		?><div class="r block"><?
		?><a class="seembutton seemtext" href="<?= $href ?>/form"><?=
			Eelement_name('camerarequest') ?>: <?=
			__($camera !== null ? 'action:change' : 'action:add')
		?></a><?
		?></div><?
		return;
	}
	if (!$request) {
		if (false === ($camreqs = db_simple_hash(['camerarequest', 'camera'],'
			SELECT cr.USERID,WRITTEN,cr.STATUS
			FROM camerarequest AS cr
			LEFT JOIN camera USING (PARTYID)
			WHERE PARTYID='.$party['PARTYID']))
		) {
			return;
		}
		assert(is_array($camreqs)); # Satisfy EA inspection
		if (isset($camreqs[CURRENTUSERID])) {
			[$written/* , $status */] = keyval($camreqs[CURRENTUSERID]);
		}
	} else {
		$written = $party['WRITTEN'];
		// $status = $party['STATUS'];
	}
	require_once '_okpartyforphotos.inc';
	if (!empty($written)
	&&	!(/* $ok = */ party_ok_for_photos($party))
	||	!have_admin('photographer')
	) {
		return;
	}

	?><div class="r block"><?= Eelement_name('camerarequest') ?>: <?
	if (isset($written)) {
		?><a class="seembutton" href="<?= $href ?>/request"><?= __('action:change') ?></a><?
		if (!$written) {
			?> <?= MIDDLE_DOT_ENTITY ?> <?
			?><span class="seembutton"><?
			with_confirm(
				__('camreq:action:withdraw'),
				$href.'/remove',
				__('camerarequest:confirm:withdrawal_LINE')
			);
			?></span><?
		}
	} else {
		?><a class="seembutton" href="<?= $href ?>/request"><?= __('action:add') ?></a><?
	}
	?></div><?
}
