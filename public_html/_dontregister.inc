<?php

declare(strict_types=1);

# dont_register() is used exclusively by impersonators, admins who 'relogin'
# a user to check or test something.
# So regular users never see this cookie. Guessing the cookie name is difficult,
# guessing the value below even more so.c

const DONTREG_SECRET = 'fimyccKjM9e6x4T0f3x61$HGqUTwwDdqAUVboAUNh9cJBqW1f56Son4L';

function dont_register(?bool $set = null): ?bool {
	static $__dont = null;
	if ($set !== null) {
		require_once '_cookie.inc';
		if ($__dont = $set) {
			setflockcookie('FLOCK_DONTREG', DONTREG_SECRET, COOKIE_SET_LONG_LIVED);
		} else {
			clearflockcookie('FLOCK_DONTREG');
		}
		return null;
	}
	return $__dont ??= isset($_COOKIE['FLOCK_DONTREG']) && $_COOKIE['FLOCK_DONTREG'] === DONTREG_SECRET;
}
