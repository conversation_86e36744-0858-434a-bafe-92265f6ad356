<?php

require_once 'defines/promo.inc';

define('ANY_RIGHT',		1);
define('ANY_BUT_CAM_RIGHT',	2);
define('BESTUUR_RIGHT',		3);

function promo_get_connects($promoid) {
	static $__connects;
	if (!isset($__connects[$promoid])) {
		return $__connects[$promoid] = memcached_rowuse_array(array('connect','party'),'
			SELECT ASSOCID,STAMP,NAME
			FROM connect JOIN party ON PARTYID=ASSOCID
			WHERE MAINTYPE="promo"
			  AND MAINID='.$promoid.'
			  AND ASSOCTYPE="party"
			ORDER BY STAMP ASC'
		);
	}
	return $__connects[$promoid];
}
function show_promo_right_part($connects,$center,$clickbox = false,$past = false) {
	ob_start();
	promo_display_connects($connects,$clickbox,$past);
	if (!($connect_info = ob_get_clean())) {
		return;
	}
	if (!$center) {
		layout_continue_box_header();
	} else {
		?></div><?
		?><div class="r"><?
	}
	?><div<?
	if (!SMALL_SCREEN) {
		?> class="nowrap"<?
	}
	?>><?= $connect_info ?></div><?
}
function promo_display_connects($connects,$clickbox = false,$past = false) {
	$cnt = 0;
	foreach ($connects as $connect) {
		if (!$past
		&&	$connect['STAMP'] < TODAYSTAMP
		) {
			continue;
		}
		$parties[] = $connect;
		list($y,$m) = _getdate($connect['STAMP']);
		$have[$y][$m] = _month_name($m);
		++$cnt;
	}
	if (empty($parties)) {
		return;
	}
	?><div class="small right"><?
	if ($cnt <= 2) {
		foreach ($parties as $connect) {
			?><a<?
			if ($clickbox) {
				?> onclick="return openLink(event,this.href,true)"<?
			}
			?> href="<?= get_element_href('party',$connect['ASSOCID'],$connect['NAME']);
			?>"><? _dateday_display($connect['STAMP']);
			?>: <b><?= escape_utf8($connect['NAME']);
			?></b></a><br /><?
		}
	} else {
		ksort($have);
		require_once '_bubble.inc';
		$bubble = new bubble(BUBBLE_CLEAN | BUBBLE_CURSOR);
		$bubble->catcher();
		if (count($have) === 1) {
			list($year,$months) = keyval($have);
			echo implode(', ',$months),' ',$year;
		} else {
			$first = true;
			$prevyear = 0;
			foreach ($have as $year => $months) {
				if ($prevyear
				&&	$prevyear != $year
				) {
					echo ' ',$prevyear;
				}
				$prevyear = $year;
				ksort($months);
				foreach ($months as $month => $name) {
					if ($first) {
						$first = false;
					} else {
						?>, <?
					}
					echo $name;
				}
			}
			echo ' ',$year;
		}
		$bubble->content();
		?><table class="dens nomargin small" style="padding:.2em"><?
		foreach ($parties as $connect) {
			?><tr><?
			?><td class="right"><? _dateday_display($connect['STAMP']) ?></td><?
			?><td><a<?
			if ($clickbox) {
				?> onclick="return openLink(event,this.href,true)"<?
			}
			?> href="<?= get_element_href('party',$connect['ASSOCID'],$connect['NAME']); ?>"><?
			?>: <b><?= escape_utf8($connect['NAME']);
			?></b><?
			if (!empty($connect['SUBTITLE'])) {
				?> <small>&middot; <?= escape_utf8($connect['SUBTITLE']) ?></small><?
			}
			?></a><?
			?></td></tr><?
		}
		?></table><?
		$bubble->display();
	}
	?></div><?
}
function _promo_display($promo,$hidepast = false,$is_admin = false) {
	require_once '_connect.inc';
	require_once '_uploadimage.inc';
	$connects = promo_get_connects($promoid = $promo['PROMOID']);
	db_insert('promoseen','
	INSERT INTO promoseen SET
		TEASER	=0,
		PROMOID	='.$promoid.',
		USERID	='.CURRENTUSERID.',
		STAMP	='.CURRENTSTAMP
	);
	layout_open_box($promo['COLOR']);
	layout_open_box_header();
	if ($center = ($promo['FLAGS'] & PROMO_TEASER_CENTER_TITLE)) {
		?><div class="center"><?
	}
	if ($only_one
	=	$connects
	&&	!isset($connects[1])
	) {
		ob_start();
		?><a href="<?= get_element_href('party',$connects[0]['ASSOCID']); ?>"><?
		$only_one = ob_get_flush();
	}
	echo make_all_html($promo['MESSAGETITLE']);
	if ($only_one) {
		?></a><?
	}
	if ($connects) {
		require_once '_favourite.inc';
		$inpast = $total = 0;
		foreach ($connects as $connect) {
			++$total;
			if ($hidepast
			&&	$connect['STAMP'] < TODAYSTAMP
			) {
				++$inpast;
			}
			$partyids[] = $connect['ASSOCID'];
		}
		if ($hidepast
		&&	$inpast == $total
		) {
			$hidepast = false;
		}
		$favinfo = new favouriteinfo($partyids);
		$favinfo->show_stars($partyids);

		show_promo_right_part($connects,$center);
	}
	if ($center) {
		?></div><?
	}
	layout_close_box_header();


	ob_start();
	if ($promoid !== 14244
	&&	$promoid !== 14245
	) {
		$img = uploadimage_show_with_fallback('promo', $promoid,
		flags:	($is_admin ? 0 : UPIMG_NOCHANGE)
			| 	(have_admin('promo') ? UPIMG_SHOW_HISTORY : 0)
		);
	}
	$imgdata = ob_get_clean();
	ob_start();
	_connect_display('promo',$promoid,$hidepast ? CONNECT_HIDE_PAST_PARTIES : 0);
	$condata = ob_get_clean();
	$rows = determine_connect_rows($condata);

	if ($side = $promo['FLAGS'] & PROMO_MESSAGE_INFOBOX_SIDE) {
		?><table class="vtop dens fw split"><tr><td class="rpad"><?
		ob_start();
	} elseif ($fade = $promo['FLAGS'] & PROMO_MESSAGE_INFOBOX_FADE) {
		include_style('infobox');
		?><div class="relative"<?
/*		if ($img) {
			?> style="min-height:<?= $img['HEIGHT']+20+$rows*22+20 ?>px"<?
		}*/
		?>><?
		?><div class="r"><div style="height:<?= (getifset($img,'HEIGHT') ?: 0) + $rows * 3 ?>px"></div><div style="height:<?= ($is_admin ? 1 : 0) + $rows + .7 + .7 + .4 + 2*.4 ?>em"></div></div><?
		?><div class="<?= $promo['COLOR'] ?> main infobox"><?
	} else {
		?><div class="right-float"><?
	}
	if ($imgdata) {
		if ($only_one) {
			echo $only_one,$imgdata,'</a>';
		} else {
			echo $imgdata;
		}
	}
	echo $condata;
	if ($side) {
		$infobox = ob_get_clean();
	} else {
		?></div><?
	}
	?><div class="block body"><?
	if ($promo['DISCOUNT'] == 100) {
		$aff_element = 'promo';
		$aff_id = $promoid;
	} else {
		$aff_element = null;
		$aff_id = 0;
	}
	echo make_all_html(make_promo_unique($promo,'BODY'),0,$aff_element,$aff_id);
	?></div><?

	if ($side) {
		?></td><td class="center"><?= $infobox ?></td></tr></table><?
	} elseif ($fade) {
		?></div><?
		?><div style="clear:both"></div><?
	}
	layout_close_box();
}
function make_promo_unique($promo,$which) {
	require_once 'defines/promo.inc';
	$body = $promo[$which];
	if (!($promo['FLAGS'] & PROMO_UNIQUE_PART)) {
		return $body;
	}
	$promoid = $promo['PROMOID'];
	return	str_replace(
			'%UNIQ%',
				$_REQUEST['sELEMENT'] != 'promo'
			||	$_REQUEST['ACTION'] == 'letter'
			?	(	($uniqs = db_simpler_array('promo_letter_unique','SELECT UNIQ FROM promo_letter_unique WHERE PROMOID='.$promoid.' AND USERID='.CURRENTUSERID))
				?	implode(', ',$uniqs)
				:	'geen code gevonden'
				)
			:	'[light]'.
				(	($uniq = db_single('promo_letter_unique','SELECT UNIQ FROM promo_letter_unique WHERE PROMOID='.$promoid.' ORDER BY RAND() LIMIT 1'))
				?	$uniq
				:	'NONE'
				).'[/light]'
			,
			$body
		);
}
function _promo_display_teaser($promo,$connects = null,$favinfo = null,$is_admin = false,$past = false) {
	require_once '_connect.inc';
	require_once '_uploadimage.inc';

	$promoid = $promo['PROMOID'];

	db_insert('promoseen','
	INSERT INTO promoseen SET
		TEASER	=1,
		PROMOID	='.$promoid.',
		USERID	='.CURRENTUSERID.',
		STAMP	='.CURRENTSTAMP
	);

	$clickbox = $promo['CLICKBOX'];

	$v2 = $promo['TEASER_DESIGN'] == PROMO_TEASER_NORMAL_V2;
	$fade = $promo['TEASER_DESIGN'] == PROMO_TEASER_INFOBOX_FADE;

	if ($promoid == 14727
	||	$promoid == 14731
	) {
		# client request
		$img = $imgdata = null;
	} else {
		ob_start();
		if (!$is_admin) {
			ob_start();
			?><a<?
			if ($clickbox) {
				?> onclick="return false"<?
			}
			?> href="/promo/<?= $promoid ?>/letter"><?
			$opentag = ob_get_clean();
			$closetag = '</a>';
		} else {
			$opentag = $closetag = null;
		}
		$img = uploadimage_show_with_fallback(
					   'promoteaser',
					   $promoid,
			flags:     ($is_admin ? 0 : UPIMG_NOCHANGE) | ($v2  || $fade ? UPIMG_ABS : 0) | UPIMG_SHOW_HISTORY,
			size:      'thumb',
			open_tag:  $opentag,
			close_tag: $closetag
		);
		$imgdata = ob_get_clean();
	}

	if ($promo['DISCOUNT'] == 100) {
		$aff_element = 'promo';
		$aff_id = $promoid;
	} else {
		$aff_element = null;
		$aff_id = 0;
	}

	ob_start();
	?><div class="block body"><?

	echo make_all_html(
		make_promo_unique($promo,'TEASER'),
		$clickbox
	?	UBB_NO_LINKS | UBB_NO_LINK_ALT
	:	0,
		$aff_element,
		$aff_id
	);
	?></div><?
	$bodypart = ob_get_clean();

	box::open($promo['COLOR'].($clickbox ? ' ptr' : null));
	if ($clickbox) {
		box::onclick("openLink(event,'/promo/".$promoid."/letter')");
	}

	$minh = empty($img['HEIGHT']) ? '10em' : $img['HEIGHT'].'px';

	if ($v2) {
		?><div class="ib abs" style="margin-top:-.2em;margin-left:-.2em"><?
		echo $imgdata;
		?></div><?

		?><div style="min-height:<?= $minh ?>"><?
	}

	layout_open_box_header();

	if ($v2) {
		?><div style="margin-left:<?= $img['WIDTH'] ?>px;padding-left:.5em"><?
	}

	$center = $promo['FLAGS'] & PROMO_TEASER_CENTER_TITLE;


	if ($center) {
		?><div class="fw center"><?
	}

	?><a<?
	if ($clickbox) {
		?> onclick="return false"<?
	}
	?> href="/promo/<?= $promoid ?>/letter"><?= flat_with_entities($promo['TITLE'],0,'promo',$promoid) ?></a><?

	if ($connects) {
		foreach ($connects as $connect) {
			$partyids[] = $connect['ASSOCID'];
		}
		if ($favinfo) {
			$favinfo->show_stars($partyids);
		}
		show_promo_right_part($connects,$center,$clickbox,$past);
	}

	if ($center) {
		?></div><?
	}
	if ($v2) {
		?></div><?
	}
	layout_close_box_header();

	if ($v2) {
		?><div style="margin-left:<?= $img['WIDTH'] ?>px;padding-left:.5em"><?
	}

	switch ($promo['TEASER_DESIGN']) {
	case PROMO_TEASER_INFOBOX_SIDE:
		?><table class="dens vtop fw"><?
		?><tr><?
		?><td style="padding-right:.5em"><?= $bodypart ?></td><?
		?><td class="center"><?= $imgdata ?></td><?
		?></tr><?
		?></table><?
		break;
	case PROMO_TEASER_INFOBOX_FADE:
		include_style('infobox');
		?><div class="relative"<?
		if ($img) {
			?> style="min-height:<?
			echo $minh;
			if (empty($img['HEIGHT'])) {
				?>;margin-bottom:.5em<?
			}
			?>"<?
		}
		?>><?
		?><div class="abs infobox"><?= $imgdata ?></div><?
		echo $bodypart;
		?></div><?
		?><div class="clear"></div><?
		break;
	case PROMO_TEASER_NORMAL_V1:
		?><div class="block"><?
			if ($imgdata) {
				?><div class="r block lmrgn"><?= $imgdata ?></div><?
			}
			echo $bodypart;
		?></div><?
		break;
	case PROMO_TEASER_NORMAL_V2:
		?><div class="block"><?
		echo $bodypart;
		?></div><?
		break;
	}

	if ($v2) {
		?></div><?
		?></div><?
	}
	layout_close_box();
}
define('PROMO_SET_SEEN',	1);
define('PROMO_SET_READ',	2);
define('PROMO_SET_KEEP',	4);
define('PROMO_SET_REMOVE',	8);
function promo_letter_alter($flags,$promoid = 0) {
	if (!$promoid) {
		if (!require_user()
		||	!($promoid = $_REQUEST['subID'])
		&&	!($promoid = !require_idnumber($_REQUEST,'PROMOID'))
		) {
			return false;
		}
	}
	if ($flags & PROMO_SET_SEEN) {
		$closed = PROMO_TEASER_CLOSED;
		$needflags = PROMO_TEASER_SHOWN;
	} elseif ($flags & PROMO_SET_READ) {
		$closed = PROMO_MESSAGE_CLOSED | PROMO_TEASER_SHOWN;
		$needflags = PROMO_MESSAGE_SHOWN;
	}
	if (isset($closed)) {
		$v = get_v($promoid);
		if ($v) {
			if ($v == 1) {
				global $__day;
				if (!db_update('promo_letter','
					UPDATE promo_letter SET
						FLAGS	=FLAGS|'.$closed.',
						DAYREAD	='.$__day.'
					WHERE FLAGS&'.$needflags.'
					  AND PROMOID='.$promoid.'
					  AND USERID='.CURRENTUSERID)
				) {
					return false;
				}
			} else {
				$startstamp = db_single('promo','SELECT STARTSTAMP FROM promo WHERE PROMOID='.$promoid);
				if (!db_update('promo_letter_v2','
					UPDATE promo_letter_v2 SET
						FLAGS = FLAGS|'.$closed.'
					WHERE FLAGS&'.$needflags.'
					  AND PROMOID='.$promoid.'
					  AND USERID='.CURRENTUSERID)
				||	!db_insert('promoseen','
					INSERT INTO promoseen SET
						TEASER	='.($flags & PROMO_SET_SEEN ? 1 : 0).',
						MARK	=1,
						PROMOID	='.$promoid.',
						USERID	='.CURRENTUSERID.',
						STAMP	='.CURRENTSTAMP)
				) {
					return false;
				}
			}
		}
	}
	if ($flags & PROMO_SET_KEEP) {
		if (!db_insupd('promokept','
			INSERT INTO promokept SET
				PROMOID	='.$promoid.',
				USERID	='.CURRENTUSERID.',
				STAMP	='.CURRENTSTAMP.'
			ON DUPLICATE KEY UPDATE
				STAMP	=VALUES(STAMP),
				KEPT	=b\'1\'')
		) {
			return false;
		}
	} elseif ($flags & PROMO_SET_REMOVE) {
		if (!db_update('promokept','
			UPDATE promokept SET
				KEPT=b\'0\'
			WHERE PROMOID='.$promoid.'
			  AND USERID='.CURRENTUSERID)
		) {
			return false;
		}
	}
	return true;
}
function get_v($promoid) {
	static $vs;
	if (isset($vs[$promoid])) {
		return $vs[$promoid];
	}
	$v = db_single('promo','SELECT VERSION FROM promo WHERE PROMOID='.$promoid);
	if (!$v) {
		if ($v === null) {
			register_error('promo:error:nonexistent_LINE',['ID'=>$promoid]);
		}
		$v = false;
	}
	return $vs[$promoid] = $v;
}
