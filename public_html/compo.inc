<?php

function perform_commit() {
	switch ($_REQUEST['ACTION']) {
	case 'commit':	return compo_commit();
	}
}
function display_body() {
	switch ($_REQUEST['ACTION']) {
	default:	not_found(); return;
	case null:	return compo_display_overview();
	case 'single':
	case 'commit':	return compo_display_single();
	case 'form':	return compo_display_form();
	case 'top':	return compo_display_top6();
	case 'author':	return compo_display_author();
	}
}
function compo_types() {
	return array('image','flyer','dualtheme');
}
function compo_overview_menu() {
	if (have_admin('compo')) {
		$menuset[] =
			'<a href="/compo/form">Toevoegen</a>';
	}
	if (isset($menuset)) {
		?><p><?= implode(' '.MIDDLE_DOT_ENTITY.' ',$menuset);
		?></p><?
	}
}
function compo_menu($compo) {
	if (!have_admin('compo')) {
		return;
	}
	?><p><a href="/compo/<?= $compo['COMPOID'];
	?>/form">Aanpassen</a></p><?
}
function entry_menu($entry) {
	if (!have_admin('compo')) {
		return;
	}
	?><p><a href="/compoentry/form/COMPOID/<?= $entry['COMPOID'];
	?>"><?= escape_specials(ucfirst($entry['ELEMNAME']));
	?> toevoegen</a></p><?
}
function compo_display_overview() {
	layout_open_section_header();
	?>Competities overzicht<?
	layout_close_section_header();

	compo_overview_menu();

	// FIXME: COUNT(compoentry) faster than (SELECT COUNT(*)) ?

	$compos = db_rowuse_array(array('compo','compoentry'),'
		SELECT	COMPOID,
			TITLE,
			TYPE,
			STATUS,
			COUNT(compoentry.COMPOID) AS ENTRYCNT,
			COUNT(DISTINCT compoentry.CREATORID) AS ENTRANTCNT
		FROM compo
		LEFT JOIN compoentry USING (COMPOID)
		GROUP BY COMPOID
		ORDER BY COMPOID DESC'
	);
	if (!$compos) {
		return;
	}

	?><table class="regular" width="100%"><tr>
		<th align="left">Omschrijving</th>
		<th align="center">Status</th>
		<th align="right">Deelnemers</th>
		<th align="right">Inzendingen</th>
	</tr><?

	foreach ($compos as $compo) {
		?><tr><td><a href="/compo/<?= $compo['COMPOID'];
		?>"><?
		if ($compo['TITLE']) {
			echo escape_specials($compo['TITLE']);
		} else {
			echo ucfirst($compo['TYPE']);
			?> competitie<?
		}
		?></a><?
		if ($compo['STATUS'] == 'closed'
		||		$compo['STATUS'] == 'open'
			&&	have_admin('compo')
		) {
			?> (<a href="/compo/<?= $compo['COMPOID'];
			?>/top">top 6</a>)<?
		}
		?></td><td align="center"<?
		switch ($compo['STATUS']) {
			case 'acquire':
				?> class="compo-invisible">inzenden<?
				break;

			case 'acquireopen':
				?> class="compo-preopen">inzenden en zien<?
				break;

			case 'preopen':
				?> class="compo-preopen">voorbeschouwing<?
				break;

			case 'open':
				?> class="compo-open">geopend<?
				break;

			case 'closed':
				?> class="compo-closed">gesloten<?
				break;
		}
		?></td><td align="right"><?= $compo['ENTRANTCNT'];
		?></td><td align="right"><?= $compo['ENTRYCNT'];
		?></td></tr><?
	}
	?></table><?
}

function compo_display_pages($compoid,$abstotal,$page) {
	require_once 'defines/compo.inc';
	if ($abstotal <= COMPO_PER_PAGE) {
		return;
	}
	$pages = ceil($abstotal / COMPO_PER_PAGE);

	?><nav><?
	?><table class="page-controls"><tr><td class="previous"><?
	if ($page > 1) {
		?><a rel="prev" href="/compo/<?= $compoid;
		?>/page/<?= $page-1;
		?>"><?= __('pagecontrols:previous') ?></a><span class="light"> = <?= $page-1;
		?></span><?
	}
	?></td><td class="pages"><?
	$cnt = 1;
	while ($cnt <= $pages) {
		if ($page == $cnt) {
			?><span class="selected-page"><?= $cnt;
			?></span><?
		} else {
			?><a href="/compo/<?= $compoid;
			if ($cnt > 1) {
				?>/page/<? echo $cnt;
			}
			?>"><?= $cnt;
			?></a><?
		}
		if ($cnt != $pages) {
			?> <?= MIDDLE_DOT_ENTITY ?> <?
		}
		++$cnt;
	}
	?></td><td class="next"><?
	if ($page < $pages) {
		?><span class="light"><?= $page+1;
		?> = </span><a rel="next" href="/compo/<?= $compoid;
		?>/page/<?= $page+1;
		?>"><?= __('pagecontrols:next') ?></a><?
	}
	?></td></tr></table><?
	?></nav><?
}
function compo_display_list($compoid,$abstotal) {
	$creators = db_simple_hash(
		array('compoentry','user_account'),'
		SELECT CREATORID,COUNT(*)
		FROM compoentry
		WHERE COMPOID='.$compoid.'
		  AND COMPOENTRYID NOT IN (763,773)
		GROUP BY CREATORID
		ORDER BY NICK ASC'
	);
	if (!$creators) {
		return;
	}
	$rowcnt = count($creators);
	?><p>Er <?
	if ($abstotal > 1) {
		?> zijn <?= $abstotal;
		?> inzendingen<?
	} else {
		?> is &eacute;&eacute;n inzending<?
	}
	?> binnen van <?
	if ($rowcnt > 1) {
		?> de volgende <b><?= $rowcnt; ?></b> leden:<?
	} else {
		?> het volgende lid:<?
	}
	?></p><ul><?
	foreach ($creators as $creatorid => $entrycnt) {
		?><li<?
		if (CURRENTUSERID == $creatorid) {
			?> class="marked"<?
		}
		?>><?
		echo get_element_link('user',$creatorid);
		if ($entrycnt > 1) {
			?> <?= MIDDLE_DOT_ENTITY ?> <?
			echo $entrycnt;
		}
		?></li><?
	}
	?></ul><?
	return;
}
function compo_display_author() {
	if (!($compoid = require_idnumber($_REQUEST,'sID'))
	||	!require_idnumber($_REQUEST,'subID')
	||	!require_self_or_admin($_REQUEST['subID'],'compo')
	) {
		return;
	}
	$compo = memcached_single_assoc('compo','
		SELECT COMPOID,TYPE,STATUS,TITLE,ELEMNAME,PERROW
		FROM compo
		WHERE COMPOID='.$compoid,
		600
	);
	if ($compo === false) {
		return;
	}
	if (!$compo) {
		_error('Competitie met ID '.$compoid.' bestaat niet!');
		return;
	}
	layout_open_section_header();
	if ($compo['TITLE']) {
		echo escape_utf8($compo['TITLE']);
	} elseif ($compo['ELEMNAME']) {
		echo escape_utf8(utf8_ucfirst($compo['ELEMNAME']));
		?>competitie<?
	} else {
		?>Competitie<?
	}

	layout_close_section_header();

	$entries = memcached_rowuse_array_if_not_admin(
		'compo',
		array('compoentry','compodata_'.$compo['TYPE'].'_meta','user_account'), '
		SELECT meta.*,compoentry.COMPOENTRYID,CREATORID,NICK
		FROM compoentry
			JOIN compodata_'.$compo['TYPE'].'_meta AS meta USING (COMPOENTRYID)
			JOIN user_account ON user_account.USERID=CREATORID
		WHERE COMPOID='.$compoid.'
		  AND CREATORID='.$_REQUEST['subID'],
		TEN_MINUTES
	);
	if ($entries === false) {
		return;
	}
	if (!$entries) {
		?><p>Geen inzendingen.</p><?
		return;
	}
	layout_open_box('white');
	layout_open_box_header();
	if (CURRENTUSERID == $_REQUEST['subID']) {
		?>Jouw inzendingen<?
	} else {
		?>Inzendingen van <a href="/user/<?= $_REQUEST['subID'];
		?>"><?= escape_specials($entries[0]['NICK']);
		?></a><?
	}
	layout_close_box_header();
	compo_show_entries($compo, $entries, count($entries), false);
	layout_close_box();
}
function compo_display_single() {
	require_once 'defines/compo.inc';
	if (!($compoid = require_idnumber($_REQUEST,'sID'))) {
		return;
	}

	$compo = memcached_single_assoc('compo','
		SELECT COMPOID,TYPE,STATUS,TITLE,ELEMNAME,PERROW,RANDOM
		FROM compo
		WHERE COMPOID='.$compoid,
		600
	);
	if (!$compo) {
		_error('Competitie met ID '.$compoid.' bestaat niet!');
		return;
	}

	layout_open_section_header();
	if ($compo['TITLE']) {
		echo escape_utf8($compo['TITLE']);
	} elseif ($compo['ELEMNAME']) {
		echo escape_utf8(utf8_ucfirst($compo['ELEMNAME']));
		?>competitie<?
	} else {
		?>Competitie<?
	}
	layout_close_section_header();

	$authors = memcached_boolean_hash('compoentry','
		SELECT DISTINCT CREATORID
		FROM compoentry
		WHERE COMPOID='.$compoid,
		600
	);
	if (isset($authors[CURRENTUSERID])) {
		?><p><a href="/compo/<?= $compoid;
		?>/author/<?= CURRENTUSERID;
		?>">Jouw inzendingen</a></p><?
	}
	compo_menu($compo);
	entry_menu($compo);

	if (have_user()
	&&	$compo['STATUS'] === 'acquire'
	&&	db_single('compoentry','SELECT 1 FROM compoentry WHERE COMPOID='.$compoid.' AND CREATORID='.CURRENTUSERID.' LIMIT 1')
	) {
		$createdby = CURRENTUSERID;
		?><p>De volgende creaties van jouw hand zijn binnen gekomen:</p><?
	} else {
		$createdby = null;
	}

	$abstotal = db_single('compoentry','SELECT COUNT(*) FROM compoentry WHERE COMPOID='.$compoid);

	if (!$abstotal) {
		?><p>Er zijn nog geen inzendingen ontvangen.</p><?
		return;
	}

	if (!have_admin('compo')
	&&	!$createdby
	&&	$compo['STATUS'] === 'acquire'
	) {
		compo_display_list($compoid,$abstotal);
		return;
	}

	$page = isset($_REQUEST['PAGE']) && ctype_digit($_REQUEST['PAGE']) ? $_REQUEST['PAGE'] : 1;

	$entries = memcached_rowuse_array(
		array('compoentry','compodata_'.$compo['TYPE'].'_meta','user_account'),'
		SELECT	meta.*,compoentry.COMPOENTRYID,CREATORID,NICK
		FROM compoentry
			LEFT JOIN compodata_'.$compo['TYPE'].'_meta AS meta USING (COMPOENTRYID)
			LEFT JOIN user_account ON user_account.USERID=CREATORID
		WHERE COMPOID='.$compoid.'
		  AND COMPOENTRYID NOT IN (763,773)
		'.($createdby ? ' AND CREATORID='.$createdby : null).'
		ORDER BY '.($compo['RANDOM'] ? 'RAND('.(have_user() ? CURRENTUSERID : '1').')' : 'compoentry.COMPOENTRYID').
		($createdby ? null : ' LIMIT '.(($page-1)*COMPO_PER_PAGE).','.COMPO_PER_PAGE)
	);
	if ($entries === false) {
		return;
	}
	if (!$entries) {
		?><p>Geen inzendingen.</p><?
		return;
	}
	if (!$createdby) {
		compo_display_pages($compoid,$abstotal,$page);
	}
	$showcreator =
		have_admin('compo')
	||	$compo['STATUS'] === 'closed';

	layout_open_box('white');
	compo_show_entries($compo, $entries, count($entries), $showcreator);
	layout_close_box();
	if (!$createdby) {
		compo_display_pages($compoid,$abstotal,$page);
	} else {
		compo_display_list($compoid,$abstotal);
	}
}
function compo_show_entries($compo, $entries, int $size, bool $showcreator) {
	?><table width="100%"><?
	$cnt = 0;
	foreach ($entries as $entry) {
		if (($cnt % $compo['PERROW']) == 0) {
			if ($cnt) {
				?></tr><?
			}
			?><tr><?
		}
		$meta = $entry;
		switch ($compo['TYPE']) {
			case 'dualtheme':
			if (!LITE &&  $meta['DARK_THUMB_WIDTH']
			||	 LITE && !$meta['LIGHT_THUMB_WIDTH']
			) {
				$theme = 'dark';
				$width = $meta['DARK_THUMB_WIDTH'];
				$height = $meta['DARK_THUMB_HEIGHT'];
				$type = $meta['DARK_FORMAT'];
			} else {
				$theme = 'light';
				$width = $meta['LIGHT_THUMB_WIDTH'];
				$height = $meta['LIGHT_THUMB_HEIGHT'];
				$type = $meta['LIGHT_FORMAT'];
			}
			?><td align="center" valign="bottom" width="25%"><a href="/compoentry/<?= $entry['COMPOENTRYID'];
			?>"><?
			?><img<?
			if (have_user()
			&&	db_single('compovote','SELECT 1 FROM compovote WHERE USERID='.CURRENTUSERID.' AND COMPOENTRYID='.$entry['COMPOENTRYID'])
			) {
				?> class="compo-chosen"<?
			}
			?> alt="Inzending #<?= $entry['COMPOENTRYID'];
			?>" src="/images/<?= $theme;
			?>/compoentry/<?= $entry['COMPOENTRYID'];
			?>_thumb.<?= $type;
			?>" width="<?= $width <= 200 ? $width : 200;
			?>" height="<?= $width <= 200 ? $height : $height * 200 / $width;
			?>"><?
			?></a><?
			if ($showcreator) {
				?><br /><a href="/user/<?= $entry['CREATORID'];
				?>"><?= escape_specials($entry['NICK']);
				?></a><?
			}
			?></td><?
			break;


			case 'image':
			case 'flyer':
			?><td align="center" valign="bottom" width="25%"><a href="/compoentry/<?= $entry['COMPOENTRYID'];
			?>"><?
			if (!$entry['THUMB_WIDTH'] || !$entry['THUMB_HEIGHT']) {
				?>???<?
			} else {
				?><img<?
				if (have_user()
				&&	db_single('compovote','SELECT 1 FROM compovote WHERE USERID='.CURRENTUSERID.' AND COMPOENTRYID='.$entry['COMPOENTRYID'])
				) {
					?> class="compo-chosen"<?
				}
				?> alt="Inzending #<?= $entry['COMPOENTRYID'];
				?>" src="/images/compo/<?= $entry['COMPOENTRYID'];
				?>_thumb.jpg" width="<?= $entry['THUMB_WIDTH'];
				?>" height="<?= $entry['THUMB_HEIGHT'];
				?>"><?
			}
			?></a><?
			if ($showcreator) {
				?><br /><a href="/user/<?= $entry['CREATORID'];
				?>"><?= escape_specials($entry['NICK']);
				?></a><?
			}
			?></td><?
			break;
		}
		++$cnt;
	}
	if ($cnt) {
		?></tr><?
	}
	?></table><?
}
function compo_display_top6() {
	if (!($compoid = require_idnumber($_REQUEST,'sID'))) {
		return;
	}
	layout_open_section_header();
	?>Competitie top 6<?
	layout_close_section_header();


	$compo = db_single_assoc('compo','SELECT TYPE,STATUS FROM compo WHERE COMPOID='.$compoid);
	if ($compo === false) {
		return;
	}
	if (!$compo) {
		register_error('compo:error:nonexistent_LINE',array('ID'=>$compoid));
		return;
	}
	if ($compo['STATUS'] != 'closed'
	&&	(	$compo['STATUS'] != 'open'
		||	!have_admin('compo')
		)
	) {
		_error('Competitie is nog niet gesloten!');
		return;
	}

	switch ($compo['TYPE']) {
		default:
			$entries = db_rowuse_array(
				array('compoentry','compovote','compodata_'.$compo['TYPE'].'_meta','user_account'),'
				SELECT	compoentry.COMPOENTRYID,CREATORID,
					COUNT(compovote.USERID) AS VOTES,
					THUMB_WIDTH,THUMB_HEIGHT
				FROM compoentry
					LEFT JOIN compovote ON compovote.COMPOENTRYID = compoentry.COMPOENTRYID
					LEFT JOIN compodata_'.$compo['TYPE'].'_meta ON compodata_'.$compo['TYPE'].'_meta.COMPOENTRYID=compoentry.COMPOENTRYID
				WHERE COMPOID='.$compoid.'
				GROUP BY compoentry.COMPOENTRYID
				ORDER BY VOTES DESC LIMIT 6'
			);
			break;
		case 'dualtheme':
			$entries = db_rowuse_array(
				array('compoentry','compovote','compodata_'.$compo['TYPE'].'_meta','user_account'),'
				SELECT	compoentry.COMPOENTRYID,CREATORID,
					COUNT(compovote.USERID) AS VOTES,
					LIGHT_THUMB_WIDTH,
					LIGHT_THUMB_HEIGHT,
					LIGHT_FORMAT,
					DARK_THUMB_WIDTH,
					DARK_THUMB_HEIGHT,
					DARK_FORMAT
				FROM compoentry
					LEFT JOIN compovote ON compovote.COMPOENTRYID = compoentry.COMPOENTRYID
					LEFT JOIN compodata_'.$compo['TYPE'].'_meta ON compodata_'.$compo['TYPE'].'_meta.COMPOENTRYID=compoentry.COMPOENTRYID
				WHERE COMPOID='.$compoid.'
				GROUP BY compoentry.COMPOENTRYID
				ORDER BY VOTES DESC LIMIT 6'
			);
			break;
	}
	if (!$entries) {
		return;
	}
	$wincnt = 1;
	$cnt = 1;
	for ($rowcnt = 1; $rowcnt <= 3; ++$rowcnt) {
		$cnt = $rowcnt;
		?><table class="vtop"><tr><?
		while ($entry = array_shift($entries)) {
			?><td><b>#<?= $wincnt++;
			?></b><br /><a href="/compoentry/<?= $entry['COMPOENTRYID'];
			?>"><img src="/images/<?
			if ($compo['TYPE'] == 'dualtheme') {
				if (!LITE
				&&	!empty($compo['DARK_THUMB_WIDTH'])
				) {
					$usetheme = 'DARK';
					?>dark<?
				} else {
					$usetheme = 'LIGHT';
					?>light<?
				}
				?>/compoentry/<?= $entry['COMPOENTRYID'];
				?>_thumb.<?= $entry[$usetheme.'_FORMAT'];
				?>" width="<?= $entry[$usetheme.'_THUMB_WIDTH'];
				?>" height="<?= $entry[$usetheme.'_THUMB_HEIGHT'];
				?>"<?
			} else {
				?>compo/<?= $entry['COMPOENTRYID'];
				?>_thumb.jpg" width="<?= $entry['THUMB_WIDTH'];
				?>" height="<?= $entry['THUMB_HEIGHT'];
				?>"<?
			}
			?> alt="[#<?= $wincnt ?>]"></a><br /><span class="light"><?= $entry['VOTES'];
			?> stemmen</span></td><?

			--$cnt;
			if ($cnt == 0) {
				break;
			}
		}
		?></tr></table><?
	}
}

function compo_display_form() {
	if (!require_admin('compo')) {
		return;
	}
	if ($compoid = have_idnumber($_REQUEST,'sID')) {
		$compo = db_single_assoc('compo','SELECT * FROM compo WHERE COMPOID='.$compoid);
		if ($compo === false) {
			return;
		}
		if (!$compo) {
			register_error('compo:nonexistent_LINE',array('ID'=>$compoid));
			return;
		}
	} else {
		$compo = null;
	}

	layout_open_section_header();
	?>Competitie <?
	echo $compo ? 'aanpassen' : 'toevoegen';
	layout_close_section_header();

	?><form onsubmit="return submitForm(this);" method="post" action="/compo<?
	if ($compoid) {
		?>/<?
		echo $compoid;
	}
	?>/commit"><?
	layout_open_box('blue');
	layout_open_table();
	layout_start_row();
		?>Title<?
		layout_field_value();
		?><input type="text" name="TITLE" value="<?= escape_utf8($compo['TITLE']) ?>" /><?
	layout_restart_row();
		?>Elementnaam<?
		layout_field_value();
		?><input type="text" name="ELEMNAME" value="<?= escape_utf8($compo['ELEMNAME']);
		?>"><?
	layout_restart_row();
		?>Soort<?
		layout_field_value();
		?><select name="TYPE"><?
		foreach (compo_types() as $type) {
			?><option<?
			if ($compo && $compo['TYPE'] == $type) {
				?> selected="selected"<?
			}
			?> value="<?= $type;
			?>"><?= ucfirst($type);
			?></option><?
		}
		?></select><?
	layout_restart_row();
		?>Status<?
		layout_field_value();
		?><select name="STATUS">
			<option<? if ($compo && $compo['STATUS'] == 'acquire') echo ' selected'; ?> value="acquire">inzenden</option>
			<option<? if ($compo && $compo['STATUS'] == 'acquireopen') echo ' selected'; ?> value="acquireopen">inzenden en zien</option>
			<option<? if ($compo && $compo['STATUS'] == 'preopen') echo ' selected'; ?> value="preopen">voorbeschouwing</option>
			<option<? if ($compo && $compo['STATUS'] == 'open') echo ' selected'; ?> value="open">geopend</option>
			<option<? if ($compo && $compo['STATUS'] == 'closed') echo ' selected'; ?> value="closed">gesloten</option>
		</select><?
	layout_stop_row();
	layout_close_table();
	layout_close_box();
	?><p><input type="submit" value="<?= $compo ? 'aanpassen' : 'toevoegen';
	?>"></p></form><?
}

function compo_commit() {
	if (!require_admin('compo')
	||	!require_anything($_POST,'TYPE')
	||	!require_anything($_POST,'STATUS')
	||	!require_something($_POST,'TITLE')
	||	!require_something($_POST,'ELEMNAME')
	) {
		return;
	}
	$setlist[] = 'TITLE="'.		addslashes(trim($_POST['TITLE'])).'"';
	$setlist[] = 'ELEMNAME="'.	addslashes(trim($_POST['ELEMNAME'])).'"';
	$setlist[] = 'TYPE="'.		addslashes($_POST['TYPE']).'"';
	$setlist[] = 'STATUS="'.	addslashes($_POST['STATUS']).'"';

	if ($compoid = have_idnumber($_REQUEST,'sID')) {
		$setlist[] = 'MUSERID='.CURRENTUSERID;
		$setlist[] = 'MSTAMP='.CURRENTSTAMP;

		if (!db_insert('compo_log','
			INSERT INTO compo_log
			SELECT * FROM compo
			WHERE COMPOID='.$compoid)
		||	!db_update('compo','
			UPDATE compo SET '.implode(', ',$setlist).'
			WHERE COMPOID='.$compoid)
		) {
			return;
		}
		_notice('Competitie is aangepast.');
	} else {
		$setlist[] = 'USERID='.CURRENTUSERID;
		$setlist[] = 'CSTAMP='.CURRENTSTAMP;

		if (!db_insert('compo','
			INSERT INTO compo SET '.implode(', ',$setlist))
		) {
			return;
		}
		$_REQUEST['sID'] = db_insert_id();
		_notice('Competitie is toegevoegd.');
	}
}
