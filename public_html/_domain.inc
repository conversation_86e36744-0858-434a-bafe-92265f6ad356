<?php

declare(strict_types=1);

define('CURRENTDOMAIN', get_domain());

const COUNTRYID_NETHERLANDS	= 1;
const COUNTRYID_BELGIUM		= 6;

function get_domain(): string {
	static  $__currentdomain = null;
	return	$__currentdomain
	??=	(	!empty($_SERVER['HTTP_HOST'])
		&&	preg_match('"\.(?<domain>be|nl)$"i', $_SERVER['HTTP_HOST'], $match)
		?	$match['domain']
		:	'nl'
		);
}

function may_be_domain_specific($set = false) {
	static $__yes = null;
	if ($set) {
		$__yes = true;
	}
	return $__yes;
}

function is_domain_specific(bool $set = false): bool {
	static $__yes = false;
	if ($set) {
		$__yes = true;
	}
	return $__yes;
}

function and_where_only_current_domain() {
	may_be_domain_specific(true);
	if (CURRENTDOMAIN === 'be') {
		is_domain_specific(true);
		return ' AND DOMAIN = "be" ';
	}
	# NOTE: Specifying the two possible domains explicitly, allows <PERSON>D<PERSON>
	#		to use a key on DOMAIN, like KEY (DOMAIN, DAYNUM) and query
	#		WHERE DOMAIN IN ('be','nl') AND DAYBUM BETWEEN $start AND $stop.
	#		Leave the DOMAIN out of the query, and the key on (DOMAIN, DAYNUM)
	#		won't be used.
	return " AND DOMAIN IN ('be','nl') ";
}

function join_only_events_for_current_country() {
	return join_only_for_current_country('party');
}

function join_only_news_for_current_country() {
	return join_only_for_current_country('news','NEWSID');
}

function join_only_for_current_country($source_element,$idname = null) {
	return '';

	may_be_domain_specific(true);
	if (CURRENTDOMAIN === 'be') {
		is_domain_specific(true);
		switch ($source_element) {
		case 'contest':
		case 'party':
			return "
				JOIN elementforcountry efc
					 ON efc.ELEMENT = 'party'
					AND efc.ID = $source_element.PARTYID
					AND efc.COUNTRYID = ".COUNTRYID_BELGIUM;
		default:
			return "
				JOIN connect AS forb
					 ON forb.MAINTYPE = '$source_element'
					AND forb.MAINID = $idname
					AND forb.ASSOCTYPE IN ('organization', 'location', 'artist', 'party')
				JOIN elementforcountry efc
					 ON efc.ELEMENT = forb.ASSOCTYPE
					AND efc.ID = forb.ASSOCID
					AND efc.COUNTRYID = ".COUNTRYID_BELGIUM;
		}
	}
	return null;
}

function determine_domain_interest(string $element, int $id): bool {
	static $__keep_countries = [
		COUNTRYID_BELGIUM => COUNTRYID_BELGIUM,
	];
	if (false === ($countryid = match($element) {
		'location'	=> db_single(['location', 'city'],'
			SELECT COUNTRYID
			FROM location
			JOIN city USING (CITYID)
			WHERE LOCATIONID = '.$id),

		'organization' => db_single(['organization', 'city'], '
			SELECT COUNTRYID
			FROM organization
			JOIN city USING (CITYID)
			WHERE ORGANIZATIONID = '.$id),

		'artist' => db_single('artist', '
			SELECT IF(LIVE_COUNTRYID, LIVE_COUNTRYID, COUNTRYID)
			FROM artist
			WHERE ARTISTID = '.$id),

		'party' => db_single(['party', 'location', 'boarding'], '
			SELECT city.COUNTRYID
			FROM party
			LEFT JOIN location USING (LOCATIONID)
			LEFT JOIN boarding USING (BOARDINGID)
			LEFT JOIN city ON city.CITYID = COALESCE(boarding.CITYID, location.CITYID, party.CITYID)
			WHERE PARTYID = '.$id),

		default => false,
	})) {
		return false;
	}
	if (!$countryid
	||	!isset($__keep_countries[$countryid])
	) {
		return true;
	}
	return db_insert('elementforcountry', "
	INSERT IGNORE INTO elementforcountry SET
		COUNTRYID	= $countryid,
		ELEMENT		= '".addslashes($element)."',
		ID		    = $id"
	);
}

function show_domain_form_part(?array $item = null): void {
	?><select<?
	?> name="DOMAIN"<?
	?> onchange="<?
		?>setdisplay(this.form['OTHER_DOMAIN_FORCED_FILTERS'].parentNode, Boolean(this.selectedIndex));<?
	?>"<?
	?>><?
	foreach (['', 'nl', 'be'] as $domain) {
		?><option<?
		if ($item
		?	($domain === $item['DOMAIN'])
		:	 $domain === ''
		) {
			?> selected<?
		}
		?> value="<?= $domain ?>"><?= $domain ?: __('attrib:all(items)') ?></option><?
	}
	?></select><?

	if (in_array($_REQUEST['sELEMENT'], ['ad', 'newsad'])) {
		$checked = !$item || $item['OTHER_DOMAIN_FORCED_FILTERS'];
		?> <label<?
		?> class="<? if (!$checked) { ?>not-<? } ?>hilited-green<?
		if (empty($item['DOMAIN'])) {
			?> hidden<?
		}
		?>"><?
		show_input([
			'type'		=> 'checkbox',
			'class'		=> 'upLite',
			'name'		=> 'OTHER_DOMAIN_FORCED_FILTERS',
			'checked'	=> $checked,
			'value'		=> '1',
		]);
		?> <?= __('ad:info:other_domains_forced_filters')
		?></label><?
	}
}

function show_domain_in_list(deflist $list, array $item): void {
	$list->add_row(
		Eelement_name('domain', count: !$item['DOMAIN']),(
			$item['DOMAIN']
		?	$item['DOMAIN'].(
				$item['FILTERS']
			&&	$item['OTHER_DOMAIN_FORCED_FILTERS']
			?	' ('.__('ad:info:other_domains_forced_filters').')'
			:	''
			)
		:	element_name('all(items)')
		)
	);
}
