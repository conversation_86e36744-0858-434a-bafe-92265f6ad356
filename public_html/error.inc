<?php

function preamble() {
	if (!empty($_SERVER['REDIRECT_STATUS'])
	&&	(	preg_match('"^/images/"i',$_SERVER['REQUEST_URI'])
		||	preg_match('"\.(?:gif|jpe?g|tiff?|bmp|png)(?:\?.*)?$"i',$_SERVER['REQUEST_URI'])
		)
	) {
		require_once '_image_bail.inc';
		image_bail((int)$_SERVER['REDIRECT_STATUS']);
	}
}
function display_title() {
	if (!isset($_SERVER['REDIRECT_STATUS'])) {
		echo __('errorpage:no_error_title');
		return;
	}
	switch ((int)$_SERVER['REDIRECT_STATUS']) {
	case 404:	echo __('errorpage:404_title'); break;
	default:	echo __('errorpage:generic_title',['STATUS'=>$_SERVER['REDIRECT_STATUS']]); break;
	}
}
function display_body() {
	layout_show_section_header(__('errorpage:header'));

	if (!isset($_SERVER['REDIRECT_STATUS'])) {
		?><div class="block"><?= __('errorpage:no_actual_error_LINE'); ?></div><?
		return;
	}
	switch ($status = (int)$_SERVER['REDIRECT_STATUS']) {
		case 404:
			show_page_problem([404, null, null, null]);
			break;

		default:
			require_once '_http_status.inc';
			?><div class="error block"><?= __('errorpage:header:generic_LINE', ['STATUS' => $status]); ?> <?= get_http_status($status); ?></div><?
			break;
	}
}
