<?php

require_once '_buddies.inc';
require_once '_buddybubble.inc';
require_once '_citylist.inc';
require_once '_connect.inc';
require_once '_contactstars.inc';
require_once '_countries.inc';
require_once '_favourite.inc';
require_once '_genrelist.inc';
require_once '_lineup.inc';
require_once '_okpartyforphotos.inc';
require_once '_partylist.inc';
require_once '_photographers.inc';
require_once '_ticket.inc';
require_once '_vote.inc';
require_once '_party.inc';
require_once '_reportlist.inc';
require_once '_site.inc';
require_once '_userlist.inc';

const REQUIRED_VISITORS_FOR_STATISTICS	= 10;
const COLLAPSE_PRICES_STARTING_FROM		= HOME_THOMAS ? 10 : false;

function preamble(): void {
	switch ($_REQUEST['ACTION']) {	# NOSONAR
	case 'single':
		if (have_idnumber($_REQUEST, 'PAGE')
		&& ($new_uri = preg_replace('"(?:/single)?/page/"i', '/comments/page/', $_SERVER['REQUEST_URI'], -1, $reps))
		&&	$reps
		) {
			moved_permanently($new_uri);
		}
		break;

	case 'maybemap':
	case 'maybecity':
		if (!($partyid = $_REQUEST['sID'])) {
			not_found();
			return;
		}
		moved_permanently(get_element_href('party', $partyid));
	}
}

function display_header(): void {
	/** @noinspection NotOptimalIfConditionsInspection */
	if (($partyid = $_REQUEST['sID'])
	&&	(require_once '_partyinfo.inc')
	&&	($party = get_party_info($partyid))
	) {
		require_once '_geotag.inc';
		show_geotag($party);
	}
}

function perform_commit(): ?bool {
	switch ($_REQUEST['ACTION']) {
	default:
	case null:			return null;
	case 'commit':		return party_commit();
	case 'remove':		require_once '_remove.inc';  return remove_element();
	case 'combine':		require_once '_combine.inc'; return combine_element();
	case 'accept':
	case 'unaccept':	require_once '_accept.inc';  return item_set_accept($_REQUEST['ACTION']);
	}
}

function display_body(): void {
	switch ($_REQUEST['ACTION']) {
	default:				not_found(); return;
	case 'visitors':
		# NOTE: Huge page, let's allow it for now again:
		# if ($_REQUEST['sID'] === 85147) {
		# 	see_other(get_element_href('party', 85147));
		# }
	case 'commit':
	case 'rate':
	case 'rating':
	case 'combine':
	case 'comments':
	case 'everdoubters':
	case 'fans':
	case 'doubtingfans':
	case 'age':
	case 'ages':
	case 'reports':
	case 'meetings':
	case 'ratings':
	case 'votes':
	case 'news':
	case 'promos':
	case 'buddies':
	#	robot_action('noindex');
	case 'single':				party_display_single(); return;
	case 'bioform':				require_once '_bio.inc'; show_bio_form(); return;
	case 'combinewith':			require_once '_combine.inc'; show_combine_with(); return;
	case 'hostimages':			party_display_images('host'); return;
	case 'lineupimages':		party_display_images('lineup'); return;
	case 'register':
	case 'form':				party_display_form(); return;
	case 'statistics':			require_once '_party_statistics.inc'; party_display_statistics(); return;
	case 'remove':				return;
	}
}

function party_display_images(string $what): void {
	if (!require_admin('party')) {
		no_permission();
		return;
	}
	if (!($partyid = $_REQUEST['sID'])) {
		not_found();
		return;
	}

	require_once '_uploadimage.inc';
	show_connected_images($what, $partyid);
}

function show_sales_contact_requested(?int $partyid): void {
	if (!$partyid
	||	!have_admin(['party', 'organization'])
	||	!($info = db_single_assoc('salescontactme', "
		SELECT	salescontactme.*,
				EMAIL, salescontactme.LANGID
		FROM salescontactme
		LEFT JOIN allowinquirymail
			   ON ELEMENT = 'party'
			  AND allowinquirymail.ID = PARTYID
		WHERE PARTYID = $partyid"))
	) {
		return;
	}
	require_once '_star.inc';
	require_once '__translation.php';
	layout_open_box('white potential-customer');
	layout_box_header(get_crown().' '.Eelement_name('potential_customer'));
	/** @noinspection PhpRedundantOptionalArgumentInspection */
	extract($info, \EXTR_OVERWRITE);
	?><div class="body"><?=
	__('event:info:potential_customer_sales_requested_TEXT', [
		'LANGUAGE'	=> $LANGID !== null ? __('language:'.get_language($LANGID)) : null,
		'NOTIFIED'	=> $NSTAMP ? _datetime_get($NSTAMP) : null,
		'USERID'	=> $USERID,
		'EMAIL'		=> $EMAIL,
	],	DO_UBB | DO_NL2BR)
	?></div><?
	layout_close_box();
}

function party_display_form(): void {
	if (!($have_user = have_user())) {
		if (!CURRENTIDENTID) {
			return;
		}
		$token = safe_random_int(0, PHP_INT_MAX);
	}
	require_once '_alternate_name.inc';
	require_once '_appic.inc';
	require_once '_bio.inc';
	require_once '_drag.inc';
	require_once '_element_access.inc';
	require_once '_elementoption.inc';
	require_once '_finditem.inc';
	require_once '_findorganization.inc';
	require_once '_inquirymail.inc';
	require_once '_locationtype.inc';
	require_once '_partyprices.inc';
	require_once '_presale.inc';
	require_once '_presence.inc';
	require_once '_process_appic_changes.inc';
	require_once '_status_overlay.inc';
	require_once '_uploadimage.inc';
	require_once 'defines/appic.inc';
	require_once 'defines/dresscodes.inc';

	global $subbar;
	if ($subbar
	&&	!isset($subbar['NAME'])
	&&	$subbar['TYPE'] === 'ubb'
	) {
		# hijack ad and show next to info, bit of a hack
		$ad = $subbar;
		$subbar = null;
	}
	layout_show_section_header(extra: __($_REQUEST['sID'] ? 'action:change' : (have_idnumber($_REQUEST, 'SRCID') ? 'action:duplicate' : 'action:add')));

	$party_admin = have_admin('party');
	$party = [];

	$new = !($partyid = $_REQUEST['sID']);

	if ($partyid) {
		if (!require_last_or_no_lock(LOCK_PARTY, $partyid, !$party_admin)) {
			return;
		}
		release_lock_on_unload(LOCK_PARTY, $partyid, delay_release: 5);

		if (!($party = db_single_assoc('party', '
			SELECT *, '.PARTY_MAIN_ID."
			FROM party
			WHERE PARTYID = $partyid"))
		) {
			if ($party === null) {
				not_found('party', $partyid);
			}
			return;
		}
		// must be admin,
		// or the event is unaccepted and currentuser
		//   was the one who entered it in the first place
		if ($party['ACCEPTED']
		&&	!may_change_element('party',$partyid)
		&&	!require_admin('party')
		) {
			return;
		}
		# FIXME: Remove code below when moving WHATPART as set to production... (PRICE_WHATPART_SET_TEST)
		if (false === ($have_multiple_whatparts = db_single_int('partyprice', "
			SELECT 1
			FROM partyprice
			WHERE WHATPART LIKE '%,%'
			  AND PARTYID = $partyid"))
		) {
			return;
		}
		if ($have_multiple_whatparts) {
			_error('Only t0mm1e can change this event due to some beta stuff, so, ask him!');
			return;
		}
	} elseif (isset($_REQUEST['CLEAN'])) {
		$party = [];
	} elseif ($srcid = have_idnumber($_REQUEST, 'SRCID')) {
		if (!($party = db_single_assoc('party', '
			SELECT *, '.PARTY_MAIN_ID."
			FROM party
			WHERE PARTYID = $srcid".
			($party_admin ? '' : ' AND (ACCEPTED OR USERID = '.CURRENTUSERID.')')))
		) {
			if (!$party !== false) {
				not_found('party', $srcid);
			}
			return;
		}
		if (false === ($have_ads = db_simple_hash(['ad', 'adinfo'], "
			SELECT ADID, NULL
			FROM ad
			WHERE PARTYID = $srcid
			UNION
			SELECT ADID, URL
			FROM adinfo
			WHERE URL LIKE '%/event/{$party['SLUG']}'
			   OR URL LIKE '%/party/$srcid'
			   OR URL LIKE '%/party/$srcid:%'"))
		) {
			return;
		}
		if ($have_ads) {
			require_once '_ubb.inc';
			$warned = false;
			foreach ($have_ads as $adid => $url) {
				$warned = $warned || !$url || !str_contains($url, '/event/');
				warning(get_element_link('ad', $adid));
			}
			if ($warned) {
				_error(make_all_html('Er zijn advertenties aan dit evenement gekoppeld. Overleg met [user=2269].'));
				return;
			}
		}
		if (false === ($camreqcnt = db_single('camerarequest', "
			SELECT COUNT(*)
			FROM camerarequest
			WHERE PARTYID = $srcid"
		))) {
			return;
		}
		if ($camreqcnt) {
			require_once '_ubb.inc';
			warning(make_all_html('Er is minstens 1 cameraverzoek aan dit evenement gekoppeld. Overleg met [user=2269].'));
			if (!HOME_THOMAS) {
				return;
			}
		}
	}
	// ORG CONNECTS

	if (!$party) {
		$party = [];
		$presale_info = [];
		$original_organizations = [];
		$original_organizations_as_host = [];
	} elseif (false === ($original_organizations = db_simple_hash(['connect','organization'],"
		SELECT ORGANIZATIONID, NAME
		FROM connect
		JOIN organization ON ASSOCID = ORGANIZATIONID
		WHERE MAINTYPE  = 'party'
		  AND MAINID	= {$party['PARTYID']}
		  AND ASSOCTYPE = 'organization'"))
	||	false === ($presale_info = get_presale_info('party', $srcid ?? $party['PARTYID']))
	||	false === ($original_organizations_as_host = db_simple_hash(['connect','organization'],"
		SELECT DISTINCT ORGANIZATIONID, NAME
		FROM connect
		JOIN organization ON ASSOCID = ORGANIZATIONID
		WHERE  MAINTYPE = 'party'
		  AND ASSOCTYPE = 'orgashost'
		  AND MAINID = {$party['PARTYID']}
		UNION
		SELECT DISTINCT ORGANIZATIONID, organization.NAME
		FROM partyarea
		JOIN organization ON HOSTEDBYID = ORGANIZATIONID
		WHERE HOSTEDBYID
		  AND PARTYID = {$party['PARTYID']}"))
	) {
		return;
	}
	assert(
		is_array($party)
	&&	is_array($presale_info)
	&&	is_array($original_organizations_as_host)); # Satisfy EA inspection:
	$appic_event_info = null;
	if ($party_admin
	&&	!empty($party['MAIN_ID'])
	&&	false === ($appic_event_info = db_single_string('appic_event_info', "
			SELECT BODY
			FROM appic_event_info
			WHERE MAINID = {$party['MAIN_ID']}"))
	||	!($spec = explain_table('party'))
	||	!($presalespec = explain_table('presaleinfo'))
	) {
		return;
	}
	if (!$party_admin
	&&	have_user()
	) {
		if (false === ($member_organizations = db_simple_hash(['organizationmember', 'organization'], "
			SELECT ORGANIZATIONID, NAME
			FROM organizationmember
			JOIN organization ON ORGANIZATIONID = ID
			LEFT JOIN hidden_orgs USING (ID)
			WHERE NAME NOT IN ('partyflock', 'appic')
			  AND hidden_orgs.ID IS NULL
			  AND organizationmember.USERID = ".CURRENTUSERID))
		||	false === ($member_locations = db_rowuse_hash(['locationmember', 'location', 'city', 'country'], /** @lang MariaDB */ '
			SELECT LOCATIONID, location.NAME, LOCATIONTYPE, CURRENCY, DEAD, TITLE, FOLLOWUPID, location.MIN_AGE, city.NAME AS CITY_NAME
			FROM locationmember
			JOIN location ON LOCATIONID = ID
			LEFT JOIN city USING (CITYID)
			LEFT JOIN country USING (COUNTRYID)
			WHERE DEAD = 0
			  AND FOLLOWUPID = 0
			  AND locationmember.USERID = '.CURRENTUSERID))
		||	false === ($recent_organizations = db_simple_hash(['party','connect','organization'], /** @lang MariaDB */ "
		(	SELECT DISTINCT ORGANIZATIONID, organization.NAME
			FROM party
			JOIN connect
			  ON MAINID = PARTYID
			 AND MAINTYPE = 'party'
			 AND ASSOCTYPE = 'organization'
			JOIN organization
			  ON ASSOCID = ORGANIZATIONID
			LEFT JOIN hidden_orgs
			  ON ORGANIZATIONID = ID
			WHERE organization.NAME NOT IN ('partyflock', 'appic')
			  AND party.USERID = ".CURRENTUSERID.'
			  AND party.MSTAMP > '.(TODAYSTAMP - ONE_MONTH)."
			  AND ID IS NULL
			ORDER BY party.MSTAMP DESC
			LIMIT 10
		) UNION (
			SELECT ORGANIZATIONID, NAME
			FROM organization
			LEFT JOIN hidden_orgs ON ID = ORGANIZATIONID
			WHERE organization.NAME NOT IN ('partyflock', 'appic')
			  AND CSTAMP > ".(TODAYSTAMP - ONE_MONTH).'
			  AND USERID = '.CURRENTUSERID.'
			  AND ID IS NULL
			ORDER BY ORGANIZATIONID DESC
			LIMIT 10
		)'))) {
			return;
		}
		if ($member_organizations
		&&	$recent_organizations
		) {
			$member_organizations = array_diff($member_organizations, $recent_organizations);
		}
		if (false === ($recent_locations = db_rowuse_hash(['party', 'location', 'city', 'country'], /** @lang MariaDB */ '
		(	SELECT DISTINCT LOCATIONID, location.NAME, location.LOCATIONTYPE, country.CURRENCY, DEAD, FOLLOWUPID, TITLE, location.MIN_AGE, city.NAME AS CITY_NAME
			FROM party
			JOIN location USING (LOCATIONID)
			LEFT JOIN city ON city.CITYID = IF(location.CITYID, location.CITYID, party.CITYID)
			LEFT JOIN country ON country.COUNTRYID=city.COUNTRYID
			WHERE party.USERID = '.CURRENTUSERID.'
			  AND party.MSTAMP > '.(TODAYSTAMP - ONE_MONTH).'
			ORDER BY IF(party.MSTAMP, party.MSTAMP, party.CSTAMP) DESC
			LIMIT 10
		) UNION (
			SELECT DISTINCT LOCATIONID, location.NAME, location.LOCATIONTYPE, country.CURRENCY, DEAD, FOLLOWUPID, TITLE, location.MIN_AGE, city.NAME AS CITY_NAME
			FROM location
			LEFT JOIN city USING (CITYID)
			LEFT JOIN country USING (COUNTRYID)
			WHERE location.CSTAMP > '.(TODAYSTAMP - ONE_MONTH).'
			  AND location.USERID = '.CURRENTUSERID.'
			ORDER BY LOCATIONID DESC
			LIMIT 10
		)'))) {
			return;
		}
		if ($member_locations
		&&	$recent_locations
		) {
			$member_locations = array_diff_key($member_locations, $recent_locations);
		}
	}
	include_js('js/form/locationtype');
	include_js('js/form/party');

	$old_partyid = $party && empty($srcid) ? $party['PARTYID'] : 0;

	?><form<?
	?> data-partyid="<?= $partyid ?: 0 ?>"<?
	?> data-edit="<?= __('party:confirm:want_to_edit_submitted_LINE') ?>"<?
	?> onsubmit="return submitPartyForm(this, <?= $old_partyid ?>)"<?
	?> id="agendaform"<?
	?> autocomplete="off"<?
	?> method="post"<?
	?> action="/party<?
	if ($old_partyid) {
		?>/<? echo $old_partyid;
	}
	?>/commit"<?
	?> data-unwanted-chars="<?= __('form:error:unwanted_chars_LINE') ?>"<?
	?> data-invalid-date="<?= __('form:error:need_valid_date_LINE') ?>"<?
	?> data-missing-location="<?= __('form:error:need_a_location_LINE') ?>"<?
	?> data-missing-locstuff="<?= __('form:error:location_problem_TEXT') ?>"<?
	?> data-prices-and-free="<?= __('form:question:prices_and_free_LINE') ?>"<?
	?> data-no-livestream-address="<?= __('form:question:no_livestream_address_LINE') ?>"<?
	?> data-livestream-min-age="<?= __('form:question:livestream_min_age_LINE') ?>"<?
	?> enctype="multipart/form-data"<?
	?> accept-charset="utf-8"<?
	?>><?
	if (!$old_partyid) {
		?><input type="hidden" name="NEW" value="1" /><?
	}
	?><input type="hidden" name="IS_SENT" /><?
	?><input type="hidden" name="enc" value="&#129392;" /><?
	if (isset($_REQUEST['APPIC'])) {
		?><input type="hidden" name="APPIC" value="1" /><?
	}

	passthrough_update_from_appic();

	if ($old_eventobj = db_single('facebook_event', "
		SELECT EVENTOBJ
		FROM facebook_event
		WHERE PARTYID = $partyid")
	) {
		$obj = safe_json_decode($old_eventobj,true);
		if (isset($obj['fbdescription'])) {
			if ($obj['fbdescription'] = utf8_mytrim(plain_text($obj['fbdescription'], UBB_UTF8)) ?: null) {
				foreach (explode("\n", $obj['fbdescription']) as $line) {
					$new_desc[] = utf8_mytrim(preg_replace('"\s{2,}"u', ' ', $line));
				}
				$obj['fbdescription'] = implode("\n",$new_desc);
			}
			$old_eventobj = json_encode($obj);
		}
		?><input type="hidden" id="oldeventobj" value="<?= escape_specials($old_eventobj) ?>" /><?
	}
	if (!empty($srcid)) {
		if (false === ($lineup = db_single('lineup', "SELECT 1 FROM lineup WHERE PARTYID = $srcid LIMIT 1"))
		||	false === ($cnt = have_uploadimage('party',$srcid))
		) {
			return;
		}
		if ($cnt || $lineup || $party_admin) {
			layout_open_box('white');
			if ($lineup) {
				$checked = false;
				?><div class="block"><?
				?><label class="cbi <?
				if (!$checked) {
					?>not-<?
				}
				?>bold-hilited"><?
				show_input([
					'type'		=> 'checkbox',
					'checked'	=> $checked,
					'name'		=> 'DUPLICATE[lineup]',
					'class'		=> 'upLite',
					'value'		=> 1,
				]);
				?> <?= element_name('lineup') ?></label><?
				?></div><?
			}
			if ($cnt) {
				?><div class="block"><?
				$checked = true;
				?><label class="cbi <?
				if (!$checked) {
					?>not-<?
				}
				?>bold-hilited"><?
				show_input([
					'type'		=> 'checkbox',
					'checked'	=> $checked,
					'name'		=> 'DUPLICATE[flyers]',
					'class' 	=> 'upLite',
					'value'		=> 1,
					'onclick'	=> "setdisplay(['partyimgblock','party2imgblock'],this.checked,true)",
				]);
				?> <?= element_name('flyer',$cnt) ?></label><?
				?></div><?
			}
			if ($party_admin) {
				if (!empty($srcid)
				&&	($src_presence = db_single('presence', "
						SELECT SITE
						FROM presence
						 WHERE ELEMENT = 'party'
						   AND ID = $srcid
						   AND TYPE = 'facebook'"
					))
				) {
					?><input type="hidden" name="SRC_PRESENCE" value="<?= escape_specials($src_presence) ?>" /><?
				}

				?><div class="block"><?
				?><label class="cbi not-bold-hilited"><?
				show_input([
					'type'		=> 'checkbox',
					'checked'	=> false,
					'name'		=> 'DUPLICATE[stuff]',
					'class' 	=> 'upLite',
					'value'		=> 1,
				]);
				?> <?= __('party:info:duplicate_visitors,connections_and_interconnect')  ?></label><?
				?></div><?
			}
			layout_close_box();
		}
	}

	if (!$have_user && !$partyid) {
		?><input type="hidden" name="TOKEN" value="<?= $token ?>" /><?
	}
	if (!empty($srcid)) {
		?><input type="hidden" name="SRCID" value="<?= $srcid ?>" /><?
	}
	?><input type="hidden" name="FORMSTAMP" value="<?= CURRENTSTAMP ?>" /><?

	ticket_passthrough();

	if (isset($ad)) {
		?><table class="dens fw"><tr><td style="width: 50%; padding-right: .5em;" class="vtop"><?
	}
	if (!$party_admin) {
		if (!am_employee()) {
			layout_open_box('white');
			?><div class="body block"><?=
				__('party:form:org_or_loc_employee_TEXT',DO_UBB | DO_NL2BR | DO_CONDITIONAL)
			?></div><?
			layout_close_box();
		}
		layout_open_box('white');
		?><div class="body block"><?=
			__('party:form:general_info_TEXT',DO_UBB | DO_NL2BR)
		?></div><?
		layout_close_box();

	}
	if (!have_admin()) {
		layout_open_box();
		?><div class="win"><?= __('advertising:info:want_to_advertise_LINE') ?></div><?
		?><div class="block"><?=
		str_replace(
			['%PF_ICON%', '%APPIC_ICON%'],
			[get_partyflock_icon(), get_appic_icon()],
			__('advertising:info:want_to_advertise_want_our_contact_TEXT', KEEP_EMPTY_KEYWORDS)
		)
		?></div><?
		show_input_with_label(
			hilited: 'bold-hilited',
			base:	 'cbi',
			input:	 [	'type'	=> 'checkbox',
						'class'	=> 'upLite',
						'name'	=> 'SALESCONTACTME',
						'value'	=> 1
			],
			text: __('advertising:info:yes_via_email_LINE')
		);
		layout_close_box();
	}
	show_inquiry_mail_question();

	if (isset($ad)) {
		?></td><td class="vtop"><?
		display_ad($ad);
		?></td><?
		?></tr></table><?
	}
	$location_updated = false;
	if (!($locationid = $party['LOCATIONID'] ?? 0)
	&&	($locationid = have_idnumber($_REQUEST,'LOCATIONID'))
	) {
		$location_updated = true;
	}
	if ($locationid) {
		if (!($location = get_location($locationid))) {
			return;
		}
	} else {
		$location = [];
	}
	$boat =
		db_single('location', "SELECT b'1' FROM location WHERE LOCATIONID = $locationid AND FIND_IN_SET('boat', LOCATIONTYPE)")
	||	!empty($party['LOCATIONTYPE'])
	&&	str_contains($party['LOCATIONTYPE'], 'boat');
	$unknown_location = !empty($party['UNKNOWN_LOCATION']);
	$boardingid = empty($party['BOARDINGID']) ? 0 : $party['BOARDINGID'];
	$boarding = $boardingid ? get_boarding($boardingid) : null;
	$timezonelement = $boarding ?: $location ?: $party;
	$timezone =
		!empty($timezonelement['CITYID'])
	&&	($timezone = memcached_timezone_city($timezonelement['CITYID']))
	?	$timezone
	:	'Europe/Amsterdam';

	show_uploadimage_for_element_form('party',empty($srcid) ? $partyid : $srcid);

	if ($party_admin) {
		show_uploadimage_for_element_form('eventmap',empty($srcid) ? $partyid : $srcid);
	}

	// PRESENCE

	$added_presence = [];

	open_drag();

	if ($party_admin) {
		$separate = !empty($party['SEPARATE']);
		?><div style="padding:0 1em"><?
		?><div class="block"><?
		?><label class="<?
		if (!$separate) {
			?>not-<?
		}
		?>bold-hilited"><?
		show_input([
			'class'		=> 'upLite',
			'type'		=> 'checkbox',
			'name'		=> 'SEPARATE',
			'checked'	=> $separate,
			'onclick'	=> "setdisplay('sepdesc',this.checked)",
		]);
		?> <?= get_appic_icon(), NO_BREAK_SPACE_ENTITY, element_name('separate_event') ?> <?= BROKEN_CIRCLE_WITH_NORTHWEST_ARROW_ENTITY, NO_BREAK_SPACE_ENTITY
		?></label><?
		?></div><?
		?><div id="sepdesc" class="<?
		if (!$separate) {
			?>hidden <?
		}
		?>warning-nb block"><?
		?>Dit evenement wordt ook indien gekoppeld aan andere evenementen met zelfde naam, als apart evenement opgenomen in Appic.<br /><?
		?>Deze instelling is binnen Appic niet eenvoudig weer uit te schakelen.<?
		?></div><?
		?></div><?
	}

	show_sales_contact_requested($partyid);

	layout_open_box('party');
	layout_open_table('padded fw');

	$show_fbopt = static function(string $field, string $id): void {
		?><tr class="hidden optfilled"><?
		?><td class="field"><?= get_facebook_icon() ?> <?
		echo $field;
		layout_field_value();
		?><span<?
		if ($id === 'LOCATION') {
			?> onclick="Pf.selectNode(this)"<?
		}
		?> id="fb_<?= $id ?>"></span><?
		?></td><?
		?></tr><?
	};

	if ($party_admin) {
		$show_fbopt(Eelement_name('name'), 'NAME');
	}

	layout_start_row();

	// LIVESTREAM
		?><label for="type"><?= Eelement_name('type') ?></label><?
		layout_field_value();
		?><select id="type" name="LIVESTREAM" onchange="Pf_clickPartyType(this);"><?
		foreach ([
			[['event'],		 		  ''],
			[['livestream'],		  'only'],
			[['event', 'livestream'], '+event'],
		] as [$effective_elements, $LIVESTREAM_value]) {
			?><option<?
			if ($party
			&&	$party['LIVESTREAM'] === $LIVESTREAM_value
			) {
				?> selected<?
			}
			?> value="<?= $LIVESTREAM_value ?>"><?=
			implode(
				' + ',
				array_map('element_name', $effective_elements)
			)
			?></option><?
		}
		?></select><?

	if (have_admin('party_placeholder')) {
		# PLACEHOLDER
		?> <?
		show_input_with_label(
			checked: !empty($party['PLACEHOLDER']),
			hilited: 'bold-hilited-cyan',
			base:	 'cbi',
			input:	['type'		=> 'checkbox',
					 'name'		=> 'PLACEHOLDER',
					 'class'	=> 'upLite',
					 'value'	=> 1],
			text:	 __('event:info:placeholder_event_LINE'));
	}

	layout_restart_row(0,'livestream_address',null,!empty($party['LIVESTREAM']) ? null : 'hidden');
		?><label for="livestream-address"><?= Eelement_name('livestream') ?> <?= element_name('address') ?></label><?
		layout_field_value();
		?><input <?
		?> id="livestream-address"<?
		?> type="url"<?
		if (!$party
		||	($in_future ??= $party['STAMP'] + $party['DURATION_SECS'] > CURRENTSTAMP)
		) {
			?> data-valid="working-url"<?
		}
		?> placeholder="https://"<?
		?> name="LIVESTREAM_ADDRESS"<?
		?> value="<?= escape_utf8($party['LIVESTREAM_ADDRESS'] ?? '') ?>" <?
		?> maxlength="<?= $spec['LIVESTREAM_ADDRESS']->maxlength ?>"<?
		?> /><?

	if ($party_admin) {
		$show_fbopt(Eelement_name('livestream').' '.element_name('address'), 'LIVESTREAM_ADDRESS');
	}

	// NAME
	layout_restart_row();
		echo Eelement_name('name');
		layout_field_value();
		show_input([
			'spec'			=> $spec,
			'type'			=> 'text',
			'name'			=> 'NAME',
			'value_utf8'	=> $party,
			'autofocus'		=> true,
			'autocomplete'	=> 'off',
			'required'		=> true,
			'onchange'		=> 'Pf_changeEventNameOrSubtitle(this);',
		]);

	if ($party_admin) {

		show_alternate_name_form('party', $partyid);
	}

	// SUBTITLE
	layout_restart_row();
		echo Eelement_name('subtitle');
		layout_field_value();
		show_input([
			'spec'			=> $spec,
			'type'			=> 'text',
			'name'			=> 'SUBTITLE',
			'value_utf8'	=> $party,
			'onchange'		=> 'Pf_changeEventNameOrSubtitle(this);',
		]);


	$fb_partyid = $srcid ?? $partyid;
	$fb_info = $fb_partyid ? db_single_assoc('facebook_info', "
		SELECT NAME, DESCRIPTION
		FROM facebook_info
		WHERE PARTYID = $fb_partyid"
	) :	null;

	if ($party_admin) {
		// FOR APPIC
		$show_others = !empty($party['NAME_FOR_APPIC']);

		if ($partyid) {
			[$appic_title] = get_appic_titles($partyid, $original_organizations + $original_organizations_as_host);

			$auto_appic = $appic_title;
		} else {
			$auto_appic = null;
		}

		if (!$show_others) {
			layout_restart_row();
			layout_next_cell();
			?><span class="ptr" onclick="Pf_showAlternativeTitles(this)"><?
			?>+ <span class="colorless appiclogo"><?= get_appic_icon() ?></span> <small class="light7 lmrgn"><?
			if ($auto_appic) {
				echo escape_utf8($auto_appic);
			}
			?></small><?
			?></span><?
		}

		layout_restart_row($show_others ? 0 : ROW_HIDDEN, 'name_for_appic_row');
			echo get_appic_icon() ?> <? echo Eelement_name('name');
			layout_field_value();
			show_input([
				'spec'				=> $spec,
				'type'				=> 'text',
				'name'				=> 'NAME_FOR_APPIC',
				'value_utf8'		=> $party['NAME_FOR_APPIC'] ?? null,
			]);

	}

	if (!show_presence_form_row(srcid: $srcid ?? null)) {
		return;
	}

	layout_restart_row();
		echo Eelement_name('source_title');
		layout_field_value();
		?><input<?
		?> type="text"<?
		?> name="FBTITLE"<?
		?> value="<?= !empty($fb_info['NAME']) ? escape_utf8($fb_info['NAME']) : null ?>"<?
		?> onkeyup="Pf_parseFacebookInfo(this, <?= $partyid ?: '0' ?>);"<?
		?>><?

 	layout_restart_row();
 		echo Eelement_name('source_description');
		layout_field_value();
		?><textarea<?
		?> rows="3"<?
		?> name="FBDESCRIPTION"<?
		?> onkeyup="Pf_parseFacebookInfo(this,<?= $partyid ?: 0 ?>)"<?
		?>><?
		if ($fb_info) {
			echo escape_utf8($fb_info['DESCRIPTION']);
		}
		?></textarea><?

	if ($party_admin) {
		if (!$appic_event_info) {
			?><span class="ptr" onclick="unhide('appic_event_info_row'); hide(this);"><?
			?>+ <span class="colorless appiclogo"><?= get_appic_icon() ?></span><?
			?></span><?
		}
		layout_restart_row($appic_event_info ? 0 : ROW_HIDDEN,'appic_event_info_row');
			echo get_appic_icon() ?> <? echo element_name('description');
			layout_field_value();
			?><textarea<?
			?> rows="3"<?
			?> name="APPICDESC"<?
			?> class="growToFit"<?
			?>><?
			if ($appic_event_info) {
				echo escape_utf8($appic_event_info);
			}
			?></textarea><?
	}

	// EDITION
	if (have_admin('party')) {
		if ($party_admin) {
			$show_fbopt(Eelement_name('edition'),'EDITION');
		}
		layout_restart_row();
			?><label for="edition"><?= Eelement_name('edition') ?></label><?
			layout_field_value();
			?><select id="edition" name="EDITION"><?
				?><option value="0"></option><?
				for ($i = 1; $i < 500; ++$i) {
					?><option<?
					if ($party && $party['EDITION'] === $i) {
						?> selected<?
					}
					?> value="<?= $i ?>"><?= $i ?></option><?
				}
			?></select><?
	} else {
		?><input type="hidden" name="EDITION" value="<?= $party['EDITION'] ?? 0 ?>" /><?
	}

	$stamp = $party['STAMP_TZI'] ?? 0;
	$tz_for_date = 'UTC';

	if ($at2400 = !empty($party['AT2400'])) {
		--$stamp;
	}
	// DATE

	$show_fbopt(Eelement_name('date'),'DATE');

	change_timezone($tz_for_date);
	layout_restart_row();
		echo Eelement_name('date');
		layout_field_value();

		display_dateselect_agenda(
			$stamp,
			memcached_single('party', 'SELECT MIN(STAMP) FROM party_db.party', ONE_HOUR) - 5,
			memcached_single('party', 'SELECT MAX(STAMP) FROM party_db.party', ONE_HOUR)
		);

		if ($party_admin) {
			# used when dragging Facebook event which are for people in Netherlands always in UTC+1 or UTC+2
			?> <select name="TZ"<?
			if (str_starts_with($timezone, 'Europe/')) {
				?> class="hidden"<?
			}
			?>><?
			?><option value=""><?= escape_utf8($timezone) ?></option><?
			?><option value="CEST">CEST</option><?
			?><option value="CET">CET</option><?
			?><option value="UTC+01">UTC+01</option><?
			?><option value="UTC+02">UTC+02</option><?
			?></select><?
		}

	// TIME
	layout_restart_row();
		echo Eelement_name('time');
		layout_field_value();
		if ($at2400) {
			$hour = 24;
			$mins = 0;
		} elseif (
			$stamp
		&&	(	!$party
			|	!$party['NOTIME']
			)
		) {
			[,,, $hour, $mins] = _getdate($stamp);
			if ($hour === 23
			&&	$mins >= 55
			) {
				$hour = 24;
				$mins = 0;
				$at2400 = true;
			}
		} else {
			$hour = null;
			$mins = null;
		}
		?><select<?
		if (empty($party['NOTIME'])) {
			?> required<?
		}
		?> onchange="Pf_changePartyHour(this);"<?
		?> name="HOUR"><?
			if ($party && $party['NOTIME']) {
				?><option value="25"></option><?
			} else {
				?><option></option><?
			}
			show_hour_options($hour);
			?><option<?
			if ($hour === 24) {
				?> selected<?
			}
			?> value="24">24</option><?

		?></select> : <select<?
		if ($disabled =
			$hour === null
		||	$hour === 24
		) {
			?> disabled<?
		}
		?> name="MINS"><?
			show_minute_options($mins, $disabled ? '' : null);
		?></select><?

	$mins  = 0;
	$hours = 0;
	if ($party) {
		$hours = (int)( $party['DURATION_SECS'] / ONE_HOUR);
		$mins  = (int)(($party['DURATION_SECS'] - $hours * ONE_HOUR) / ONE_MINUTE);
	}
	if ($at2400
	&&	$mins <= 5
	) {
		$mins = 0;
	}
	if ($mins === 59) {
		++$hours;
		$mins = 0;
	}
	$show_fbopt(Eelement_name('duration(party)'), 'DURATION');

	layout_restart_row();
		echo Eelement_name('duration(party)');

		layout_field_value('middle');
		show_input([
			'type'	=> 'number',
			'min'	=> 0,
			'class'	=> 'right two_digits',
			'name'	=> 'DURATION_HOURS',
			'value'	=> $hours ?: null,
			'data-allow-calculation'
					=> true,
		]);
		?> <?= element_name('hour') ?> <?

		show_input([
			'type'	=> 'number',
			'min'	=> 0,
			'max'	=> 59,
			'class'	=> 'right two_digits',
			'name'	=> 'DURATION_MINS',
			'value'	=> $mins ?: null,
		]);
		?> <? echo element_plural_name('minute');

	change_timezone();

	layout_restart_row();
		echo __C('attrib:no_entry_after');
		layout_field_value();

		if (isset($party['DOOR_CLOSE_HOUR'])) {
			$hour = $party['DOOR_CLOSE_HOUR'];
			$mins = $party['DOOR_CLOSE_MINS'];
		} else {
			$hour = $mins = null;
		}
		?><select onchange="Pf_changePartyHour(this);" name="DOOR_CLOSE_HOUR"><?
		?><option value="25"></option><?
			show_hour_options($hour);
		?></select> : <select name="DOOR_CLOSE_MINS"<?
		if ($disabled
		=	$hour === null
		||	$hour === 24
		) {
			?> disabled<?
		}
		?>><?
			show_minute_options(
				select_minutes: $mins,
				zero_text:		$disabled ? '' : null,
			);
		?></select><?

	?></td></tr><?

	if ($party_admin) {
		$show_fbopt(Eelement_name('location'),'LOCATION');
	}

	$all_orgs = $original_organizations;
	if (($organizationid = have_idnumber($_REQUEST,'ORGANIZATIONID'))
	&&	(false === ($all_orgs[$organizationid] = memcached_organization($organizationid)))
	) {
		return;
	}

	foreach ($all_orgs as $organizationid => $name) {
		if (!($default = db_single_assoc('organization', "
			SELECT EROTIC, FIND_IN_SET('festival', ACTIVITIES) AS FESTIVAL
			FROM organization
			WHERE ORGANIZATIONID = $organizationid"))
		) {
			if ($default === false) {
				return;
			}
			continue;
		}
		if ($default['EROTIC']) {
			$default_erotic = true;
		}
		if ($default['FESTIVAL']) {
			$default_festival = true;
		}
	}
	if (!isset($default_erotic)
	&&	!empty($location['EROTIC'])
	) {
		$default_erotic = true;
	}

	?><tbody id="locstuff"<?
	if ($party && $party['LIVESTREAM'] === 'only') {
		?> class="hidden"<?
	}
	?>><tr<?
	if ($party && $location_updated) {
		?> class="updated"<?
	}
	?>><td class="field"><?

		echo Eelement_name('location');
		layout_field_value();

		?><div><?
		?><input class="forcerequire rmrgn regular<?
		if ($unknown_location) {
			?> hidden<?
		}
		?>"<?
		?> type="text"<?
		if (!$unknown_location
		&&	(	!$party
			||	 $party['LIVESTREAM'] !== 'only'
			)
		) {
			?> required<?
		}
		?> name="LOCATION_ADDR"<?
		?> onkeyup="locationLookup.searchFor(this);"<?
		?> onmouseup="locationLookup.searchFor(this);"<?
		?> onchange="locationLookup.searchFor(this);"<?
		?> value="<?
		if ($location) {
			echo escape_utf8($location['NAME'] ?: $location['TITLE']);
		} elseif ($party) {
			echo escape_utf8($party['LOCATION_ADDR']);
		}
		?>"><?

		?><label class="nowrap <?
		if (!$unknown_location) {
			?>not-<?
		}
		?>bold-hilited"><?
		?><input type="checkbox"<?
		if ($unknown_location) {
			?> checked<?
		}
		?> class="upLite"<?
		?> onclick="checkSecretLoc(this)"<?
		?> name="UNKNOWN_LOCATION"<?
		?> value="1" /> <?= __('field:unknown') ?></label><?

		?></div><?

		$alllocs = [];
		if (!empty($recent_locations)) {
			$alllocs += $recent_locations;
		}
		if (!empty($member_locations)) {
			$alllocs += $member_locations;
		}
		if ($locationid) {
			$alllocs[$locationid] = $locationid;
		}

		?><div id="locationoptions"><?
		if ($location) {
			show_element_option($location, !$unknown_location);
		}
		?></div><?
		if (!empty($recent_locations)) {
			// remove current location from recent_locations
			if ($locationid) {
				$recent_locations = array_diff_key($recent_locations,array($locationid=>null));
			}
			string_asort($recent_locations,'NAME');
			?><div><?= __('party:form:recently_used_by_you_LINE') ?>:</div><div><?
			foreach ($recent_locations as /* $id => */ $location_option) {
				show_element_option($location_option, false) ?><br /><?
			}
			?></div><?
		}
		if (!empty($member_locations)) {
			string_asort($member_locations,'NAME');
			?><div class="block"><?= __('party:form:your_locations_LINE') ?>:</div><div><?
			foreach ($member_locations as /* $id => */ $location_option) {
				show_element_option($location_option, false) ?><br /><?
			}
			?></div><?
		}

	# Show possible location types in green on web form.
	# If more than 1 type is possible, and 'indoor' is included, indoor will be picked as default
	$possible_location_types = $location ? explode_to_hash(',', $location['LOCATIONTYPE']) : [];
	if (!empty($party['LOCATIONTYPE'])) {
		$selected_location_types = explode_to_hash(',', $party['LOCATIONTYPE']);
	} elseif (!empty($location['LOCATIONTYPE'])) {
		$selected_location_types =
			count($possible_location_types) <= 1
		?	$possible_location_types
		:	(	isset($possible_location_types['indoor'])
			?	['indoor' => 'indoor']
			:	[]
			);
	} else {
		$selected_location_types = [];
	}

	layout_restart_row($unknown_location ? 0 : ROW_HIDDEN, 'cityidrow');
		?><label for="city"><?= Eelement_name('city') ?></label><?
		layout_field_value();

		show_find_item([
			'element'		=> 'city',
			'id'			=> 'city',
			'class'			=> 'regular',
			'selected'		=> $party['CITYID'] ?? null,
			'placeholder'	=> element_name('city'),
		]);

	// LOCATIONTYPE
	layout_restart_row(id: 'typerow');
		echo Eelement_name('type');
		layout_field_value();
		show_locationtype_checkboxes($selected_location_types, /** @lang JavaScript */ 'Pf_changePartyLocType(this);', mark_green: $possible_location_types);

		?><hr class="slim" style="margin:0"><?

		foreach (['festival', 'concert', 'afterparty', 'preparty'] as $type) {
			$TYPE = strtoupper($type);

			$checked =	!$party
				?	(	!empty($default_festival)
					&&	$type === 'festival'
					)
				:	!empty($party[$TYPE]);

			?><label onpointerdown="Pf_pointerdownCategory(event, this.firstChild)" class="cbi <?
			if (!$checked) {
				?>not-<?
			}
			?>bold-hilited"><?
			show_input([
				'checked'	=> $checked,
				'class'		=> 'upLite',
				'type'		=> 'radio',
				'name'		=> 'CATEGORY',
				'value'		=> $type,
				'id'		=> "category-$type",
				'onclick'	=> /** @lang JavaScript */ 'Pf_clickCategory(event, this);',
			]);
			?> <?= element_name($type)
			?></label><br /><?
		}

	?></td></tr></tbody><?
	// BOARDING LOCATION
#	$boat = true;
	layout_start_row($boat ? 0 : ROW_HIDDEN,null,'boardingrow');
		echo Eelement_name('boarding_place');
		layout_field_value();

		?><div><?
		?><input type="text" autosave="boardingname" name="BOARDING_ADDR" onkeyup="boardingLookup.searchFor(this)" value="<?=
			$boarding
		?	escape_utf8($boarding['NAME'])
		:	(	$party
			?	escape_utf8($party['BOARDING_ADDR'])
			:	''
			)
		?>"><?
		?></div><?

		?><div id="boardingoptions"<?
		if ($boarding) {
			?>><?
			show_element_option($boarding,true);
		} else {
			?> class="hidden"><?
		}
		?></div><?

	$departure = $party['DEPARTURE'] ?? null;
	$arrival = $party['ARRIVAL'] ?? null;
	layout_restart_row($boat ? 0 : ROW_HIDDEN,'departurerow');
		echo Eelement_name('departure');
		layout_field_value();
		if (!$party
		||	$departure >= 2400
		||	(!$departure && !$arrival)
		) {
			$h = 25;
			$m = null;
		} else {
			$h = (int)($departure / 100);
			$m = $departure - $h * 100;
		}
		_time_display_select('DEPARTURE_',$h,$m,true);

	layout_restart_row($boat ? 0 : ROW_HIDDEN,'arrivalrow');
		echo Eelement_name('arrival');
		layout_field_value();
		if (!$party
		||	$arrival >= 2400
		||	(!$departure && !$arrival)
		) {
			$h = 25;
			$m = null;
		} else {
			$h = (int)($arrival / 100);
			$m = $arrival - $h * 100;
		}
		_time_display_select('ARRIVAL_',$h,$m,true);

	// ORGANIZATIONS
	$prefill_gids = [];
	layout_restart_row();
		echo Eelement_name('organization(s)');
		layout_field_value();

		if ($original_organizations
		&&	empty($srcid)
		) {
			foreach ($original_organizations as $organizationid => $name) {
				?><input type="hidden" name="ORGORGANIZATIONID[<?= $organizationid; ?>]" value="1" /><?
			}
		}

		$orgparts = [];

		if ($all_orgs) {
			foreach ($all_orgs as $organizationid => $name) {
				$orgparts[] = $name.'~'.$organizationid;
			}
		}

		show_input([
			'type'			=> 'text',
			'class'			=> 'fw',
			'autosave'		=> 'organizationname',
			'name'			=> 'ORGANIZATION_NAME',
			'id'			=> 'organization_name',
			'onkeyup'		=> /** @lang JavaScript */ 'searchForOrganization(this, event);',
			'onblur'		=> /** @lang JavaScript */ "Pf_addSeparator(this, ', ');",
			'value_utf8'	=> $orgparts ? implode(', ', $orgparts).', ' : '',
		]);
		?><br /><?
		?><div id="organizationoptions"><?
		if ($all_orgs) {
			natcasesort($all_orgs);
			foreach ($all_orgs as $local_organizationid => $local_organization_name) {
				$prefill_gids += find_organization($local_organization_name.'~'.$local_organizationid, !isset($original_organizations_as_host[$organizationid]));
			}
		}
		?></div><?
		if (!empty($recent_organizations)) {
			if (!empty($all_orgs)) {
				$recent_organizations = array_diff_key($recent_organizations, $all_orgs);
			}
			// remove current orgs from recent_organizations
			natcasesort($recent_organizations);
			?><div><?= __('party:form:recently_used_by_you_LINE') ?>:</div><?
			?><div><?
			foreach ($recent_organizations as $tmporgid => $tmpname) {
				$prefill_gids += find_organization($tmpname.'~'.$tmporgid,false);
			}
			?></div><?
		}
		if (!empty($member_organizations)) {
			natcasesort($member_organizations);
			?><div><?= __('party:form:your_organizations_LINE') ?>:</div><?
			?><div><?
			foreach ($member_organizations as $tmporgid => $tmpname) {
				$prefill_gids += find_organization($tmpname.'~'.$tmporgid,false);
			}
			?></div><?
		}

	layout_restart_row(empty($party['LOCORG']) ? ROW_HIDDEN : 0, 'locorgrow');
		layout_next_cell();
		?><div class="cbi"><label class="<?
		if (empty($party['LOCORG'])) {
			?>not-<?
		}
		?>bold-hilited"<?
		?>><input class="upLite" type="checkbox" name="LOCORG" value="1"<?
		if (!empty($party['LOCORG'])) {
			?> checked<?
		}
		?>><span class="lmrgn" id="locorgstore"><?
		if (!empty($party['LOCORG'])) {
			echo escape_utf8(get_element_title('location',$locationid));
		}
		?></span></label> <span>(<?= __('party:info:location_organizes_info') ?>)</span><?
		?></div><?

	layout_restart_row();
		echo Eelement_name('host(s)');
		layout_field_value();
		show_find_item([
			'element'		=> 'organization',
			'id_name'		=> 'ORGASHOSTID[]',
			'multi_input'	=> true,
			# 'name'		=> 'ORGASHOST',
			'selected'		=> $original_organizations_as_host,
		]);

	layout_restart_row();
		echo Eelement_name('site_of_party');
		layout_field_value();
		show_input([
			'type'			=> 'url',
			'data-valid'	=> 'url',
			'name'			=> 'SITE',
			'spec'			=> $spec,
			'value'			=> $party ? escape_utf8($party['SITE']) : '',
		]);

	layout_restart_row();
		echo Eelement_name('max_visitors');
		layout_field_value();
		show_input([
			'type'		=> 'number',
			'min'		=> 0,
			'class'		=> 'right id',
			'name'		=> 'VISITORCNT',
			'spec'		=> $spec,
			'value'		=> $party['VISITORCNT'] ?? null,
		]);

	// LOCKERS
	layout_start_row();
		?><label for="lockers"><?= Eelement_plural_name('locker') ?></label><?
		layout_field_value();
		/** @noinspection ThisExpressionReferencesGlobalObjectJS */
		show_input([
			'type'		=> 'checkbox',
			'name'		=> 'LOCKERS',
			'id'		=> 'lockers',
			'value'		=> '1',
			'onclick'	=> 'Pf_clickLockers(this);',
			'checked'	=> $checked = !empty($party['LOCKERS']),
			'data-keep-lockers'
						=> $checked
		]);

	// EROTIC

	layout_restart_row();
	?><label for="erotic"><?= __C('attrib:erotic') ?></label><?
	layout_field_value();
	show_input([
		'type'		=> 'checkbox',
		'id'		=> 'erotic',
		'name'		=> 'EROTIC',
		'value'		=> '1',
		'checked'	=> isset($party['EROTIC']) ? !empty($party['EROTIC']) : isset($default_erotic)
	]);

	layout_restart_row();
	?><label for="restriction"><?= Eelement_name('restriction') ?></label><?
	layout_field_value();
	?><select id="restriction" name="RESTRICTION"><?
		foreach ([
			null,
			'no_single_men',
			'no_single_person',
			'maybe_single_men',
			'maybe_single_person',
			'only_men',
			'only_women',
			'only_couples',
		] as $restriction) {
			?><option<?
			if ($party && $restriction === $party['RESTRICTION']) {
				?> selected<?
			}
			?> value="<?= $restriction ?>"><?
			if ($restriction) {
				echo __('party:info:'.$restriction);
			}
			?></option><?
		}
	?></select><?

	// MIN_AGE
	layout_restart_row();
		echo Eelement_name('minimum_age');
		layout_field_value();
		show_input([
			'type'		=> 'number',
			'class'		=> 'three_digits center',
			'min'		=> 0,
			'max'		=> 120,
			'name'		=> 'MIN_AGE',
			'value'		=> $party ? ($party['MIN_AGE'] ?: null) : ($location ? ($location['MIN_AGE'] ?: 18) : 18),
		]);
		?> <?
		show_input([
			'type'			=> 'number',
			'class'			=> 'three_digits center',
			'min'			=> 0,
			'max'			=> 120,
			'name'			=> 'MIN_AGE_FEMALE',
			'_placeholder'	=> '&#9792;',
			'value'			=> $party ? ($party['MIN_AGE_FEMALE'] ?: null) : null,
		]);

	# MAX_AGE
	layout_restart_row();
		?><label for="max-age"><?= Eelement_name('maximum_age') ?></label><?
		layout_field_value();
		?><select name="USE_MAX_AGE" onchange="setdisplay('max-age', this.value !== '0');"><?
			?><option value="0"><?= __('answer:no') ?></option><?
			?><option<?
			if ($show_max_age
			=	$party
			&&	$party['MAX_AGE']
			) {
				?> selected<?
			}
			?> value="1"><?= __('answer:yes') ?></option><?
		?></select><?
		?><span id="max-age"<?
		if (!$show_max_age) {
			?> class="hidden"<?
		}
		?>> <?
		show_input([
			'type'			=> 'number',
			'data-valid'	=> 'number',
			'class'			=> 'three_digits center',
			'min'			=> 0,
			'max'			=> 120,
			'name'			=> 'MAX_AGE',
			'value'			=> $party ? $party['MAX_AGE'] : 0,
		]);
		?></span><?

	// DRESSCODE
	layout_restart_row(ROW_HIDDEN,'fb_dresscode_row',null,'optfilled');
		echo get_facebook_icon(); ?> <? echo Eelement_name('dresscode');
		layout_field_value();
		?><span id="fb_dresscode" onclick="Pf.selectNode(this);"></span><?

	foreach (DRESSCODE_FOR_WHO as $field => $for_who) {
		layout_restart_row();
		show_dresscode($for_who);
		layout_field_value();
		show_textarea_utf8([
			'spec'		=> $spec,
			'type'		=> 'text',
			'class'		=> 'growToFit',
			'name'		=> $field,
			'value'		=> $party,
		]);
		if ($field === 'DRESSCODE') {
			show_input_with_label(
				!empty($party['DRESSCODE_MANDATORY']),
				'bold-hilited-green',
				input:	 ['type'	 	=> 'checkbox',
						  'name'		=> 'DRESSCODE_MANDATORY',
						  'value'		=> '1',
				],
				text:	 __('dresscode:mandatory'));
		}
	}

	// GENRES
	layout_restart_row();
		echo Eelement_plural_name('genre');
		layout_field_value();
		show_genre_selection(
				$party
			?	db_boolean_hash('party_genre', "SELECT GID FROM party_genre WHERE PARTYID = {$party['PARTYID']}")
			:	(	$prefill_gids
				&&	!$old_partyid
				?	$prefill_gids
				:	null
				),
			make_bold: $prefill_gids
		);

	if ($party_admin) {
		layout_restart_row();
			echo __C('videotype:aftermovie');
			layout_field_value();
			show_input([
				'name'			=> 'AFTERMOVIE',
				'value'			=> $party['AFTERMOVIE'] ?? null,
				'type'			=> 'number',
				'placeholder'	=> element_name('video').' ID',
				'min'		   	=> 0,
				'max'			=> db_single('video', "SELECT MAX(VIDEOID) FROM party_db.video")
			]);
	}

	layout_restart_spanned_row();
	?><hr class="slim" /><?

	// FREE_ENTRANCE
	layout_restart_row();
		?><label for="forfree"><?= Eelement_name('free_entrance') ?></label><?
		layout_field_value();
		show_input(array(
			'id'		=> 'forfree',
			'type'		=> 'checkbox',
			'name'		=> 'FREE_ENTRANCE',
			'value'		=> 1,
			'checked'	=> $party ? $party['FREE_ENTRANCE'] : false
		));
		$costflags = 0;

	// ONLY DOOR SALE
	layout_restart_row();
		?><label for="dooronly"><?= Eelement_name('doorsale_only') ?></label><?
		layout_field_value();
		show_input([
			'id'		=> 'dooronly',
			'type'		=> 'checkbox',
			'name'		=> 'DOORONLY',
			'value'		=> 1,
			'onclick'	=> /** @lang JavaScript */ 'Pf_clickOnly(this);',
			'checked'	=> $party ? $party['DOORONLY'] : false,
		]);

	layout_restart_row();
		?><label for="presaleonly"><?= Eelement_name('presale_only') ?></label><?
		layout_field_value();
		show_input([
			'id'		=> 'presaleonly',
			'type'		=> 'checkbox',
			'name'		=> 'PRESALEONLY',
			'value'		=> 1,
			'onclick'	=> /** @lang JavaScript */ 'Pf_clickOnly(this);',
			'checked'	=> !empty($party['PRESALEONLY']),
		]);

		$costflags = 0;

	// INVITE ONLY
	layout_restart_row();
		?><label for="inviteonly"><?= Eelement_name('invitation_only') ?></label><?
		layout_field_value();
		show_input([
			'id'		=> 'inviteonly',
			'type'		=> 'checkbox',
			'name'		=> 'INVITEONLY',
			'value'		=> 1,
			'checked'	=> $party ? $party['INVITEONLY'] : false
		]);


	// DOOR MORE
	layout_restart_row();
		?><label for="doormore"><?= Eelement_name('door_more') ?></label><?
		layout_field_value();
		show_input([
			'id'		=> 'doormore',
			'type'		=> 'checkbox',
			'name'		=> 'DOORMORE',
			'value'		=> 1,
			'checked'	=> $party ? $party['DOORMORE'] : false,
		]);
		$costflags = 0;

	// SOLD OUT

		layout_restart_row($costflags);

		?><label for="sold_out"><?= __C('status:sold_out') ?></label><?
		layout_field_value();
		show_input([
			'type'		=> 'checkbox',
			'checked'	=> $party ? !empty($party['SOLD_OUT']) : false,
			'name'		=> 'SOLD_OUT',
			'id'		=> 'sold_out',
			'onclick'	=> "if(this.checked&&!this.form['PRESALE_SOLD_OUT'].checked)this.form['PRESALE_SOLD_OUT'].click()",
		]);

		layout_restart_row($costflags);

		?><label for="presale_sold_out"><?= __C('status:presale_sold_out') ?></label><?
		layout_field_value();
		show_input([
			'type'		=> 'checkbox',
			'checked'	=> $party ? !empty($party['PRESALE_SOLD_OUT']) : false,
			'name'		=> 'PRESALE_SOLD_OUT',
			'id'		=> 'presale_sold_out'
		]);


	# PRESALE START
	layout_restart_row($costflags,'presalestart');
		?><label for="presale-date-c"><?= Eelement_name('presale_start') ?></label><?
		layout_field_value();
		$presale_start = $party ? $party['PRESALE_STAMP'] : false;
		show_input([
			'id'		=> 'presale-date-c',
			'type'		=> 'checkbox',
			'name'		=> 'PRESALE_DATE_C',
			'onclick'	=> /** @lang JavaScript */ "setdisplay('presale_date', this.checked);",
			'value'		=> 1,
			'checked'	=> $presale_start,
		]);
#		layout_next_cell();
		?><span id="presale_date" class="lmrgn<?
		if (!$presale_start) {
			?> hidden<?
		}
		?>"><?

		change_timezone($timezone);
		[$year] = _getdate($presale_start ?: ($party['STAMP'] ?? null));

		_datetime_display_select_stamp(
			$pstamp = $presale_start,
			'PRESALE_',
			$pstamp ? -2 : $year - 2,
			$pstamp ? +2 : $year + 2,
			0,
			!$party || $party['PRESALE_STAMP_NOTIME'],
		);
		change_timezone();
		?></span><?

	# PRICES

	?></td></tr><?

		if (!$party) {
			$partyprices = null;
		} elseif (false === ($partyprices = db_rowuse_array('partyprice', "
			SELECT *
			FROM partyprice
			WHERE PARTYID = {$party['PARTYID']}".
			  get_order_for_partyprices($location['CURRENCY'] ??  null)))
		||	!show_party_prices_form($party, $partyprices, $timezone, $location)
		) {
			return;
		}

	if (!empty($party['EXTRA'])) {
		// EXTRA
		layout_restart_row();
			?><label for="extrac"><?= Eelement_plural_name('comment') ?></label><?
			layout_field_value();
			?><div<?
			if (!$party['EXTRA']) {
				?> class="hidden"<?
			}
			?> id="extrainfo"><?
			?><div><?
			show_textarea([
				'class'		=> 'growToFit',
				'rows'		 => 5,
				'cols'		 => 80,
				'id'		 => 'extra',
				'name'		 => 'EXTRA',
				'value_utf8' => $party['EXTRA'] ?? '',
			]);
			?></div><?
			?><div><?= __('party:info:extra_comments_TEXT', DO_NL2BR) ?></div><?
			?></div><?
	}

	if (!$party_admin) {
		layout_restart_spanned_row();
		?><hr class="slim" /><?
	}
	# VALICMT
	layout_restart_row();
		?><label for="valicmt"><?= Eelement_plural_name('validation_comment')?></label><?
		layout_field_value();
		?><textarea class="growToFit" rows="5" cols="80" id="valicmt" name="VALICMT"><?= $party ? escape_specials($party['VALICMT'],true) : null ?></textarea><?
		if (!$party_admin) {
			?><div><?= __('party:info:validation_comments_LINE') ?></div><?
		}

	if ($party_admin) {
		ob_start();
		$have_bios = show_bio_form_part();
		$bio_stuff = ob_get_clean();
		layout_restart_row();
			?><label><?= Eelement_name('information') ?> <?
			show_input([
				'type'		=> 'checkbox',
				'checked'	=> $have_bios,
				'onclick'	=> /** @lang JavaScript */ "setdisplay('bio-stuff', this.checked);",
			]);
			?></label><?
			layout_field_value();
			?><div id="bio-stuff"<?
			if (!$have_bios) {
				?> class="hidden"<?
			}
			?>><?= $bio_stuff ?></div><?
		}

	layout_restart_spanned_row();
		?><hr class="slim" /><?

	show_status_checkboxes($party);

	// LINEUP_PSTAMP, TIMETABLE_PSTAMP'
	change_timezone($timezone);
	foreach (['event', 'lineup', 'timetable', 'flyer'] as $what) {
		if (!$party_admin
		&&	$what === 'event'
		) {
			if (!empty($party['EVENT_PSTAMP'])) {
				?><input type="hidden" name="EVENTCB" value="1" /><?
				?><input type="hidden" name="EVENT_PSTAMP" value="<?= $party['EVENT_PSTAMP'] ?>" /><?
			}
			continue;
		}
		$uwhat = strtoupper($what);
		$pstamp = $party[$uwhat.'_PSTAMP'] ?? null;
		layout_restart_row();
		?><label for="<?= $what ?>cb"><?= Eelement_name('publication') ?> <?= element_name($what) ?></label><?
		layout_field_value();
		?><label class="<?
		if (!($checked = (bool)$pstamp)) {
			?>not-<?
		}
		?>hilited-white"><?
		show_input([
			'class'		=> 'upLite',
			'name'		=> $uwhat.'CB',
			'checked'	=> $checked,
			'type'		=> 'checkbox',
			'id'		=> $what.'cb',
			'value'		=> '1',
			'onclick'	=> "setdisplay('$what-pstamp', this.checked);",
		]);
		?></label> <span<?
		if (!$pstamp) {
			?> class="hidden"<?
		}
		?> id="<?= $what ?>-pstamp"><?
		_datetime_display_select_stamp($pstamp ?: TODAYSTAMP,$uwhat.'_P');
		if ($flag
		=	(	$what === 'lineup'
			?	PARTY_FLAG_HIDDEN_LINEUP_PSTAMP
			:	(	$what === 'timetable'
				?	PARTY_FLAG_HIDDEN_TIMETABLE_PSTAMP
				:	false
				)
			)
		) {
			$checked = $party && ($party['FLAGS'] & $flag);
			?> <label class="<? if (!$checked) { ?>not-<? } ?>hilited"><?
			show_input([
				'type'		=> 'checkbox',
				'class'		=> 'upLite',
				'name'		=> 'FLAGS[]',
				'value'		=> $flag,
				'checked'	=> $checked
			]);
			?> <?= __('party:info:dont_publish_date_LINE') ?></label><?
		}
		?></span><?
	}
	change_timezone();

	layout_close_table();
	layout_close_box();

	close_drag();

	layout_open_box('party', 'presaleinfo');
	layout_box_header(Eelement_name('presale_information'));
	layout_open_table('fw');
	layout_start_row();

	echo Eelement_name('preregistration');
	layout_field_value();
	show_input([
		'type'	=> 'url',
		'name'	=> 'PREREG_URL',
		'value'	=> $party,
	]);

	# Tickets at physical location is a legacy option, only show when set,
	# so it can be unset and is kept if set.
	if (!empty($presale_info['LOCATION'])) {
		layout_restart_row();
		?><label for="presaleloc"><?= __C('presale:at_phyisical_location_itself') ?></label><?
		layout_field_value();
		?><input type="checkbox" id="presaleloc" name="PRESALE_LOCATION" value="1"<?
		if (!empty($presale_info['LOCATION'])) {
			?> checked<?
		}
		?> /><?
	}

	layout_restart_row();
		$placeholder = memcached_single(['presaleinfo', 'party'], "
			SELECT WEBSITE
			FROM party
			JOIN presaleinfo USING (PARTYID)
			WHERE WEBSITE != ''
			  AND WEBSITE NOT LIKE '%\\n%'
			  AND STAMP > ".TODAYSTAMP.'
			ORDER BY RAND()
			LIMIT 1',
			TEN_MINUTES
		);
		$presale_website_count = 0;
		ob_start();
		if (!empty($presale_info['WEBSITE'])) {
			# 1 newline indicates 2 lines, so count + 1 is number of lines added.
			$presale_website_count += substr_count($presale_info['WEBSITE'], "\n") + 1;
			echo escape_utf8($presale_info['WEBSITE']),"\n";
		}
		if (!empty($presale_info['PAYLOGICV2'])) {
			++$presale_website_count;
			?>PAYLOGIC:<?= $presale_info['PAYLOGICV2'] ?>:<? echo $presale_info['PAYLOGICPOS'],"\n";
		}
		foreach ([
			'YOURTICKETPROVIDER',
			'TICKETMASTER',
			'NOWONLINETICKETS',
			'IKBENAANWEZIG',
			'EVENTTICKETS',
		] as $old_way) {
			if (!empty($presale_info[$old_way])) {
				++$presale_website_count;
				echo $old_way ?>:<? echo $presale_info[$old_way],"\n";
			}
		}
		$presale_websites_str = ob_get_clean();

		$single_presale_site = !$partyid || $presale_website_count <= 1;

		?><label for="presale-website"><?= Eelement_name('presale_site', !$single_presale_site) ?></label><?
		layout_field_value();

		if ($single_presale_site) {
			show_input([
				'id'				=> 'presale-website',
				'name'				=> 'PRESALE_WEBSITE',
				# NOTE: Don't set type to 'url', we want to support URLs with view-source prefix too:
				'type'				=> 'text',
				# FIXME: This data-valid value is not implemented yet:
				'data-valid'		=> 'url_or_view_source',
				# Ticketmaster and Fourvenues fail for valid URLs, so don't use for now:
				# 'data-valid'		=> 'working-url',
				'placeholder_utf8'	=> $placeholder,
				'data-want-charset'	=> 'ascii',
				'spec'				=> $presalespec['WEBSITE'],
				'value_utf8'		=> utf8_mytrim($presale_websites_str),
			]);
		} else {
			?><textarea<?
			?> id="presale-website"<?
			?> class="growToFit"<?
			?> rows="5"<?
			?> cols="80"<?
			?> name="PRESALE_WEBSITE"<?
			?> data-want-charset="ascii"<?
			?> placeholder="<?= escape_utf8($placeholder) ?>"<?
			?> maxlength="<?= $presalespec['WEBSITE']->maxlength ?>"<?
			?>><?= $presale_websites_str
			?></textarea><?
			# still supported via ID only in presale_info":
			# - ticketmaster
			# - event-tickets
			# - yourticketprovider
			# - ikbenaanwezig
			?><div><?= __('party:presale:presale_website_per_line_LINE') ?></div><?
		}

	if (have_admin('party')
	&&	!empty($presale_info['EMAIL'])
	) {
		layout_restart_row();
			echo Eelement_name('email');
			layout_field_value();
			?><input type="email" data-valid="email" maxlength="<?= $presalespec['EMAIL']->maxlength; ?>" name="PRESALE_EMAIL" value="<?
			if (!empty($presale_info['EMAIL'])) {
				echo escape_specials($presale_info['EMAIL']);
			}
			?>"><?
	}

	layout_restart_row();
		$placeholder = __('party:form:presale_address_placeholder');
		$placeholder .= "\n$placeholder 2";

		echo Eelement_plural_name('presale_address');
		layout_field_value();

		?><textarea<?
		?> data-want-charset="ascii"<?
		?> placeholder="<?= escape_utf8($placeholder) ?>"<?
		?> class="growToFit"<?
		?> name="PRESALE_ADDR"<?
		?> rows="5"<?
		?> cols="80"><?

		if (!empty($presale_info['ADDR'])) {
			echo escape_utf8($presale_info['ADDR']);
		}
		?></textarea><?
		?><div><?= __('party:presale:presale_address_per_line_LINE') ?></div><?
	layout_stop_row();
	layout_close_table();
	layout_close_box();

	?><div class="block"><?
	show_input([
		'onclick'	=> !$new ? 'Pf_keepLock = true;' : null,
		'type'		=> 'submit',
		'value'		=> __(!$new ? 'action:change' : 'action:add'),
	]);
	?></div><?

	?></form><?
}

function party_menu(
	array			$party,
	?bool			$have_lock	 = null,
	bool|array|null $am_employee = false
): bool {
	$partyid = $party['PARTYID'];
	$party_admin = have_admin('party');
	$party_remove_admin = have_admin(['party_remove', 'party_remove_unaccepted']);
	$party_accept_admin = have_admin('party_accept');

	if (!$party_admin
	&&	!$am_employee
	&&	!$have_lock
	&&	$party['USERID'] !== CURRENTUSERID
	) {
		return true;
	}
	$bad = false;
	$keep_lock = ' onclick="Pf_keepLock = true; return true;"';
	if (!$party_admin) {
		layout_open_menu();
		if ($party['ACCEPTED']) {
			require_once '_element_access.inc';
			if (may_change_element('party' ,$partyid)) {
				layout_menuitem(__C('action:change'), '/party/'.$partyid.'/form', $keep_lock);
			} else {
				layout_menuitem(__C('action:submit_change'), '/ticket/form?ELEMENT=party;ID='.$partyid);
			}
		} elseif ($have_lock) {
			layout_menuitem(__C('action:change'), '/party/'.$partyid.'/form', $keep_lock);
			layout_menuitem(__C('action:duplicate'), '/party/register?SRCID='.$partyid);
		} elseif ($have_lock === false) {
			layout_open_menuitem();
			display_lockinfo(LOCK_PARTY, $partyid, true);
			layout_close_menuitem();
		}
		if ($party['STAMP'] > CURRENTSTAMP + 2 * ONE_DAY) {
			layout_menuitem(__C('action:add_contest'),'/ticket/form?ELEMENT=newcontest;ID='.$partyid,' class="win"');
		}
		layout_close_menu();
		return true;
	}

	layout_open_menu();
	if ($have_lock) {
		layout_menuitem(__C('action:change'), "/party/$partyid/form", $keep_lock);
		layout_menuitem(__C('action:change_information'), "/party/$partyid/bioform", $keep_lock);
		layout_menuitem(__C('action:duplicate'), '/party/register?SRCID='.$partyid);
		if (!$party['ACCEPTED']) {
			if ($party_accept_admin
			||	(	!$party['ACCEPTED']
				&&	$party['MUSERID'] === CURRENTUSERID
				&&	($func = function(int $partyid, array $party): bool {
						if (!($history = db_rowuse_array('party_log', "
							SELECT ACCEPTED, MUSERID
							FROM party_log
							WHERE PARTYID = $partyid
							ORDER BY MSTAMP DESC"))
						) {
							return false;
						}
						$prevlog = $party;
						foreach ($history as $log) {
							if ($log['ACCEPTED']) {
								if ($prevlog['MUSERID'] === CURRENTUSERID) {
									# currentuser was one who unaccepted this party
									return true;
								}
								break;
							}
							$prevlog = $log;
						}
						return false;
					})
				&&	$func($partyid, $party)
				)
			) {
				$reasons = [];
				if (!$party['UNKNOWN_LOCATION']
				&&	(	!$party['LIVESTREAM']
					||	$party['LIVESTREAM'] === '+event'
					)
				&&	(	!isset($party['location'])
					||	!$party['location']['ACCEPTED']
					)
				) {
					$reasons[] = __('party:bubble:cannot_accept_location_not_accepted_LINE', DO_UBB, ['LOCATIONID' => $party['LOCATIONID']]);
				}
				if (false === ($unaccepted_artiststr = db_single_string(['artist', 'lineup'], '
					SELECT GROUP_CONCAT(ARTISTID)
					FROM lineup
					JOIN artist USING (ARTISTID)
					WHERE ACCEPTED = 0
					  AND PARTYID = '.$partyid))
				) {
					$bad = true;
					$reasons[] = get_generic_error();
				}
				if ($unaccepted_artiststr) {
					$reasons[] = __('party:bubble:cannot_accept_unaccepted_artists_LINE', DO_UBB, ['ARTISTIDS' => $unaccepted_artiststr]);
				}
				if ($party['NOTIME']) {
					$reasons[] = __('party:bubble:no_time_supplied');
				}
				if ($party['EVENT_PSTAMP'] > CURRENTSTAMP) {
					$reasons[] = __('party:info:event_publication_LINE', ['DATE' => _datedaytime_get($party['EVENT_PSTAMP'])]);
				}
				if ($reasons) {
					require_once '_bubble.inc';
					layout_open_menuitem();
					$bubble = new bubble(BUBBLE_BLOCK | CATCHER_UNAVAILABLE | BUBBLE_CURSOR);
					$bubble->catcher(__C('action:accept'));
					$bubble->content(implode('<br />', $reasons));
					$bubble->display();
					layout_close_menuitem();
				} else {
					layout_menuitem(__C('action:accept'), '/party/'.$partyid.'/accept');
				}
			}
		} else {
			layout_menuitem(__C('action:reject'), '/party/'.$partyid.'/unaccept');
			if ($party['STAMP'] > CURRENTSTAMP + 2 * ONE_DAY
			&&	have_admin('contest')
			) {
				layout_menuitem(__C('action:add_contest'),'/contest/form?PARTYID='.$partyid);
			}
		}
		if ($party_admin) {
			layout_open_menuitem();
			connect_menuitem();
			layout_close_menuitem();
		}
	} else {
		if ($have_lock === false) {
			layout_open_menuitem();
			display_lockinfo(LOCK_PARTY,$partyid);
			layout_close_menuitem();
		}
		layout_close_menu();
		return true;
	}
	layout_continue_menu();
	show_element_menuitems();
	layout_close_menu();
	if ($bad) {
		return false;
	}
	require_once '_element_to_ticket_menu.inc';
	layout_open_menu();
	show_element_to_ticket_menu('party', $partyid, $party);
	layout_close_menu();
	return true;
}

const CLOSED_CONTEST	= 'closed';
const OPEN_CONTEST		= 'open';
const INACTIVE_CONTEST	= 'inactive';
const FUTURE_CONTEST	= 'future';

function party_display_single(): void {
	if (!(	$partyid =
			have_errors()
		?	have_idnumber($_REQUEST, 'sID')
		:	require_idnumber($_REQUEST,'sID'))
	) {
		not_found();
		return;
	}
	if (!($party = db_single_assoc('party',  "
		SELECT PARTYID, USERID, MUSERID, ACCEPTED
		FROM party
		WHERE PARTYID = $partyid"))
	) {
		if ($party === false) {
			return;
		}
		not_found();
		return;
	}
	require_once '_element_access.inc';
	$party_admin = have_admin('party');
	$have_lock =
		(	$party_admin
		||	may_change_element('party', $partyid)
		||	(	CURRENTUSERID === $party['USERID']
			||	$party['USERID']
			&&	isset($_COOKIE['FLOCK_TOKEN_'.LOCK_PARTY.'_'.$partyid])
			)
			&&	!$party['ACCEPTED']
			&&	(	$party['MUSERID'] === 0
				||	$party['MUSERID'] === CURRENTUSERID
				||	!(/* $once_accepted = */ db_single_bool('party_log', "
						SELECT ACCEPTED
						FROM party_log
						WHERE ACCEPTED = 1
						  AND PARTYID = $partyid
						LIMIT 1",
						DB_FORCE_MASTER
					))
				&&	!query_failed()
				)
		)
	?	obtain_lock(LOCK_PARTY, $partyid)
	:	null;

	if (!TRY_WITHOUT_FRESHEN
	&&	$have_lock
	) {
		set_nomemcache();
	}
	require_once '_appic.inc';
	if (false === ($appic_event = get_appic_for_party($party))) {
		return;
	}
	$appic_event ??= [];
	assert(is_array($appic_event)); # Satisfy EA inspection
	$party['appic_event'] = $appic_event;
	if (!($party = memcached_single_assoc_if_not_self_nor_admin(
		$party['USERID'],	// owner
		'party',			// admin
		['party', 'lineupneedupdate', 'connect', 'organization'], '
		SELECT	*,
			(SELECT CSTAMP FROM lineupneedupdate WHERE lineupneedupdate.PARTYID = party.PARTYID) AS LINEUP_NEEDUPDATE,
			'.PARTY_MAIN_ID.",
			(	SELECT 1
				FROM connect
				JOIN organization ON ORGANIZATIONID = ASSOCID
				WHERE MAINTYPE = 'party'
				  AND MAINID = $partyid
				  AND ASSOCTYPE = 'organization'
				  AND HIDE_AGE_STATS = 1
				LIMIT 1
			) AS HIDE_AGE_STATS
		FROM party
		WHERE PARTYID = $partyid",
		TEN_MINUTES,
		PARTY_CACHE.$partyid
	))) {
		if ($party === false) {
			return;
		}
		if ($_REQUEST['ACTION'] !== 'remove') {
			not_found();
		}
		return;
	}
	require_once '_going.inc';
	assert(is_array($party)); # Satisfy EA inspection
	$going = all_going_to_party_askonce($partyid);
	$maybe = $going === GOING_MAYBE;

	fill_location_and_boarding($party);

	$locationid = $party['LOCATIONID'];
	$location = $party['location'] ?? [];
	$boarding = $party['boarding'] ?? [];

	$camerarequest_admin = have_admin('camerarequest');

	counthit('party',$_REQUEST['sID']);

	# needed for some fields. which??
	if (!($cparty = memcached_party_and_stamp($partyid))) {
		if ($cparty !== false) {
			not_found();
		}
		return;
	}
	$party += $cparty;
	assert(is_array($party)); # Satisfy EA inspection

	require_once '_date.inc';
	assert(is_array($party)); # Satisfy EA inspection
	change_timezone('UTC');
	$datestr = _date_get($party['STAMP_TZI']);
	change_timezone();

	$meta_desc = str_replace(
		['%CITY%', '%NAME%'],
		[		$party['CITYID']
					?	escape_utf8(get_element_title('city', $party['CITYID']))
					:	null,
					escape_utf8($party['NAME'].($party['SUBTITLE'] ? ' '.MIDDLE_DOT.' '.$party['SUBTITLE'] : ''))
		],
		__('metad:party:single', DO_UBBFLAT | KEEP_EMPTY_KEYWORDS, [
			'DATE'		=> $datestr,
			'CITY'		=> $party['CITYID'] ? '%CITY%' : 0,
			'LINEUP'	=> $party['HAVE_LINEUP'] ? 1 : 0,
			'MAP'		=> $party['HAVE_MAP'] ? 1 : 0,
			'TIMETABLE'	=> $party['TIMETABLE_RELEASED'],
			'TICKETS'	=> !$party['FREE_ENTRANCE'] && CURRENTSTAMP < $party['STAMP'] + $party['DURATION_SECS'],
		])
	);

	if ($_REQUEST['ACTION'] === 'single') {
		include_og('og:description', $meta_desc);
	}

	require_once '_background_queries.inc';
	[	$certain_cnt,
		$female_cnt,
		$male_cnt,
		$maybe_cnt,
		$total_cnt
	] =	get_bgquery(BGQRY_VISITORS, 'party', $partyid)
	?:	[null,null,null,null,null];


	if ($have_lock) {
		# see for info about delay contact.inc
		release_lock_on_unload(LOCK_PARTY, $party['PARTYID'], delay_release: 5);
	}

	layout_show_section_header(to_userid: $party['USERID']);

	### WARNINGS
	if ($party_admin
	&&	str_contains($party['SITE'], ' ')
	) {
		?><div class="warning block"><?= __C('party:warning:site_with_space') ?>: <?= escape_utf8($party['SITE']) ?></div><?
	}
	require_once '_organizationstuff.inc';
	if (!($stuff = get_organization_stuff($party))) {
		return;
	}

	[$orgs, /* $orglocs */, $allorgs, $parties, $wholes, $all_erotic_org] = $stuff;
	$party['allorgs'] = $allorgs;

	if ($party_admin) {
		$am_employee = false;
	} else {
		require_once '_employees.inc';
		$am_employee = null;
		if ($party['LOCATIONID']
		&&	($tmpids = am_employee('location',$party['LOCATIONID']))
		) {
			$am_employee['location'] = $tmpids;
		}
		if ($orgs
		&&	($tmpids = am_employee('organization',$allorgs))
		) {
			$am_employee['organization'] = $tmpids;
		}
	}

	party_menu($party, $have_lock, $am_employee);

	?><hr />TOST<hr /><?

	# show_check_mark_for_aspiring();
	require_once '_ticketlist.inc';
	show_connected_tickets();

	if (!$party['ACCEPTED']) {
		if (!have_user()
		&&	!$have_lock
		||	!have_self($party['USERID'])
		) {
			if (!require_admin('party')) {
				return;
			}
		} elseif (!$party_admin) {
			layout_open_box('white');
			?><div class="block"><?=
				__(	'party:info:not_validated_yet_TEXT',
					DO_NL2BR|DO_UBB, [
						'CNT' => memcached_single_int('party', <<<MARIADB
									SELECT COUNT(*)
									FROM party_db.party
									WHERE ACCEPTED = 0
						MARIADB) ?: 0])
			?></div><?
			layout_close_box();
		}
	}
	if ((	$camerarequest_admin
		||	$am_employee
		)
	&&	(false === ($camreq = db_single_int('camera', "SELECT 1 FROM camera WHERE PARTYID = {$party['PARTYID']}", DB_FORCE_MASTER)))
	) {
		return;
	}
	/** @noinspection SqlJoinCount */
	if (have_admin(['ad', 'newsad', 'promo', 'pixel', 'invoice', 'relation'])
	&&	($connections = db_rowuse_array('connect', "
			SELECT ASSOCTYPE AS ELEMENT, ASSOCID AS ID
			FROM connect
			LEFT JOIN ad		ON ASSOCTYPE = 'ad'		  AND ASSOCID = ADID
			LEFT JOIN newsad	ON ASSOCTYPE = 'newsad'   AND ASSOCID = NEWSADID
			LEFT JOIN promo		ON ASSOCTYPE = 'promo'	  AND ASSOCID = PROMOID
			LEFT JOIN pixel		ON ASSOCTYPE = 'pixel'	  AND ASSOCID = PIXELID
			LEFT JOIN invoice	ON ASSOCTYPE = 'invoice'  AND ASSOCID = invoice.INVOICENR
			LEFT JOIN relation	ON ASSOCTYPE = 'relation' AND ASSOCID = relation.RELATIONID
			LEFT JOIN banner	ON ad.BANNERID = banner.BANNERID
			WHERE ASSOCTYPE IN ('ad', 'newsad', 'promo', 'pixel', 'invoice', 'relation')
			  AND MAINTYPE = 'party'
			  AND MAINID = $partyid
			ORDER BY COALESCE(ad.STOPSTAMP, newsad.STOPSTAMP, promo.STOPSTAMP, pixel.STOPSTAMP, relation.CSTAMP, invoice.SENTSTAMP) DESC,
				ad.POSITION = ".ADPOS_PARTYLIST.',
				banner.NAME'))
	) {
		ob_start();
		$ad_count = 0;
		foreach ($connections as $connection) {
			/** @noinspection PhpRedundantOptionalArgumentInspection */
			extract($connection, \EXTR_OVERWRITE);
			if (!have_admin($ELEMENT)) {
				continue;
			}
			if (in_array($ELEMENT, ['ad', 'newsad'])) {
				++$ad_count;
			}
			[/* $link */, $content] = _element_display_link($ELEMENT, $ID, ELEMENT_PAST_AD_LESS_PROMINENT, true);
			?><li><?= $content ?></li><?
		}
		if ($data = ob_get_clean()) {
			layout_open_box();
			if ($ad_count) {
				show_party_statistics_button($party);
			}
			?><ul><?= $data ?></ul><?
			layout_close_box();
		}
	}

	if ($party_admin) {
		require_once '_connect.inc';
		_connect_display_form('party',$party['PARTYID']);
	}

	/** @noinspection NotOptimalIfConditionsInspection */
	if (!$party['ACCEPTED']
	&&	$party_admin
	&&	!empty($location)
	&&	(require_once '_lastparty.inc')
	&&	($lastparty = get_lastparty_in_dead_location($locationid, $partyid))
	) {
		layout_open_box('white');
		?><div class="body block"><?
		?><b class="warning"><?= __('party:warning:event_in_closed_location_header_LINE') ?></b><br /><?=
			__('party:warning:event_in_closed_location_TEXT', DO_UBB | DO_NL2BR, [
				'LAST_PARTYID'	=> $lastparty['PARTYID'],
				'LAST_DATE'		=> _dateday_get_tzi($lastparty['STAMP_TZI']),
			])
		?></div><?
		layout_close_box();
	}

 	if ($party['MOVEDID']) {
		if (false === ($moved_to = memcached_single_assoc_if_not_admin('party' ,'party', "
 			SELECT LOCATIONID, STAMP
 			FROM party
 			WHERE PARTYID = {$party['MOVEDID']}"))
		) {
		 	return;
		}
		if ($moved_to) {
			layout_open_box('white');
			?><div class="block body"><?
			$parts = [];
			if ($moved_to['LOCATIONID']
			&&	$moved_to['LOCATIONID'] !== $locationid
			) {
				$parts[] = get_element_link('location', $moved_to['LOCATIONID']);
			}

			[		  $year,	 	  $month,		   $day] = _getdate($party['STAMP']);
			[$moved_to_year, $moved_to_month, $moved_to_day] = _getdate($moved_to['STAMP']);

			if ($year	!== $moved_to_year
			||	$month	!== $moved_to_month
			||	$day	!== $moved_to_day
			) {
				ob_start();
				datedaytime_display_link($moved_to['STAMP']);
				$parts[] = '<i>'.ob_get_clean().'</i>';
			}
			?><h3 class="warning"><?=
				str_replace(
					'%DESTINATION%',
					implode(', ', $parts),
					__('party:warning:this_event_has_been_moved_LINE', DO_UBB, ['DESTINATION' => $parts ? '%DESTINATION%' : null])
				)
			?></h3><?
			?><div class="block"><?=
				__('party:info:see_new_information_on_moved_event_LINE', DO_UBB, ['PARTYID' => $party['MOVEDID']])
			?></div><?
			layout_close_box();
		}
 	} elseif ($party['STAMP'] < CURRENTSTAMP) {
		require_once '_vote.inc';
		$votes = get_votes('party',$partyid);
		$totalvotes = $votes ? $votes[''] : 0;
		$medianvote = $votes ? get_median($votes) : 0;
	}

	if ($party['CANCEL_REASON']
	&&	(	$party['CANCELLED']
		||	$party['POSTPONED']
		)
	) {
		layout_open_box('white');
		layout_box_header('<span class="error">'.__C($party['CANCELLED'] ? 'status:cancelled' : 'status:postponed').'</span>');
		?><div class="body block"><?=
			make_all_html($party['CANCEL_REASON'], UBB_UTF8)
		?></div><?
		layout_close_box();
	}
	require_once '_album.inc';
	$userphotocnt = get_album_total_for_element('party',$partyid);
	if ($party_admin) {
		$organization_notes =
			$allorgs
		?	db_simple_hash('organization', "
			SELECT ORGANIZATIONID, ADMIN_PARTY_TEXT
			FROM organization
			WHERE ADMIN_PARTY_TEXT != ''
			  AND ORGANIZATIONID IN (".implode(',', $allorgs).')',
			DB_FORCE_MASTER)
		:	null;
		$location_note =
			$locationid
		?	db_single_string('location', "
			SELECT ADMIN_PARTY_TEXT
			FROM location
			WHERE LOCATIONID = $locationid",
			DB_FORCE_MASTER)
		:	null;

		if ($party['VALICMT']
		||	$organization_notes
		||	$location_note
		) {
			layout_open_box('white');
			if ($party['VALICMT']) {
				?><div class="body block"><?
				?><span class="warning"><?= __C('header:admin_comments') ?></span><br /><?=
					make_all_html($party['VALICMT'], UBB_UTF8)
				?></div><?
			}
			if ($organization_notes) {
				foreach ($organization_notes as $organizationid => $organization_note) {
					?><div class="body block"><?
					?><span class="warning"><?= __C('header:admin_comments') ?> (<?= get_element_link('organization',$organizationid) ?>)</span><br /><?=
						make_all_html($organization_note, UBB_UTF8)
					?></div><?
				}
			}
			if ($location_note) {
				?><div class="body block"><?
				?><span class="warning"><?= __C('header:admin_comments') ?> (<?= get_element_link('location',$locationid,$location['NAME']) ?>)</span><br /><?=
					make_all_html($location_note, UBB_UTF8)
				?></div><?
			}
			layout_close_box();
		}
	}
	require_once '_presence.inc';
	$presences = have_presence(include_bad: true, show_bubbles: false);

	$no_facebook = !isset($presences['facebook']);

	require_once '_partydescription.inc';
	$party_desc = get_party_description($partyid);

	$party['USECITYID'] = ($boarding['CITYID'] ?? null) ?: ($location['CITYID'] ?? null) ?: $party['CITYID'];
	$party['TIMEZONE'] = $party['USECITYID'] ? memcached_timezone_city($party['USECITYID']) : null;

	$show_lineup = !$party['PLACEHOLDER'];

	$warnings = [];

	$lineup_errors = [];

	if ($lineupstr = $show_lineup) {
		require_once '_lineup.inc';
		ob_start();
		lineup_display(
			$party,
				str_starts_with($_REQUEST['ACTION'], 'lineup')
			&&	$have_lock,
			null,
			false,
			$lineup_errors
		);
		$lineupstr = ob_get_clean();
	}

	require_once '_partyimage.inc';
	require_once '_uploadimage.inc';
	[	$front_image,
		$back_image,
		$alternative_image,
		$have_front,
		$have_back,
		/* $have_alternative */
	] = get_front_and_back_flyer($party);

	$eventmap = uploadimage_get('eventmap', $partyid, 'regular');

		print_rr($eventmap);

if (false) {
	if ($party_admin
	||	$party['USERID'] === CURRENTUSERID
	) {
		if (!$party['DURATION_SECS']) {
			$warnings[] = '<span class="warning">'.__('party:error:make_sure_there_is_a_duration_LINE', DO_UBB).'</span>';
		}
		if (!$have_front) {
			ob_start();
			$warnings[] = '<span class="error">'.__('party:error:no_front_flyer_LINE', DO_UBB).'</span>';
			$warnings[] = ob_get_clean();
		}
		if ($lineup_errors) {
			ob_start();
			?><div class="error block"><?= implode('<br />', $lineup_errors) ?></div><?
			$warnings[] = ob_get_clean();
			if ($party['STAMP'] > CURRENTSTAMP) {
				?><script>
				addreadyevent(function() {
					alert('<?= implode("\n", $lineup_errors) ?>');
				});
				</script><?
			}
		}
		change_timezone('UTC');
		[/* $y */, $m, $d, $hour, $mins] = _getdate($party['STAMP_TZI']);
		change_timezone();

		if ($party['LIVESTREAM']
		&&	empty($party['LIVESTREAM_ADDRESS'])
		&&	!db_single_int('lineup', "SELECT 1 FROM lineup WHERE PARTYID = $partyid AND LIVESTREAM_ADDRESS != ''")
		) {
			$warnings[] = '<span class="warning">'.__('party:warning:livestream_address_is_missing_LINE', DO_UBB).'</span>';
		}

		if ($m === 12
		&&	$d === 31
		&&	$hour === 23
		&&	$mins >= 46
		) {
			$warnings[] = '<span class="warning">'.__('party:warning:probably_starts_in_the_new_year_LINE', DO_UBB).'</span>';
		}
		if (!empty($party_desc[1])
		&&	$m === 1
		&&	$d === 1
		&&	!$hour
		&&	!$mins
		&&	preg_match('"(00:30|00:45|01:00)"',$party_desc[1],$match)
		&&	false !== ($have_before_time =  db_single('lineup', "
				SELECT 1
				FROM lineup
				WHERE PARTYID = $partyid
				  AND FROM_UNIXTIME(START_STAMP_UTC,'%H%i') BETWEEN 0 AND ".str_replace(':','',$match[1]),
				DB_UTC
			))
		&&	!$have_before_time
		) {
			$warnings[] = '<span class="warning">'.__('party:warning:start_from_desc_differs_LINE', DO_UBB, ['DESC_START' => $match[1]]).'</span>';
		}
		if ($party['CSTAMP'] > CURRENTSTAMP - ONE_HOUR) {
			$period = 2 * ONE_WEEK;

			# check whether same name event exists within 2 month range the same year
			if (($sames = db_simple_hash('party', "
				SELECT PARTYID, STAMP, NAME
				FROM party
				WHERE LOCATIONID = {$locationid}
				  AND STAMP BETWEEN ".($party['STAMP'] - $period).' AND '.($party['STAMP'] + $period)."
				  AND '".addslashes($party['NAME'])."' LIKE CONCAT(party.NAME, '%')
				  AND ISNULL((
				  	SELECT 1
				  	FROM connect
				  	WHERE  MAINTYPE = 'party' AND  MAINID = $partyid
				  	  AND ASSOCTYPE = 'party' AND ASSOCID = PARTYID
				  	LIMIT 1
				  ))
				ORDER BY STAMP"))
			&&	count($sames) > 1
			) {
				ob_start();
				?><div class="block"><?
				?><span class="warning"><?= __('party:info:identical_events_same_period_LINE', DO_UBB) ?></span><?
				?><table class="nb"><?
				foreach  ($sames as $tmppartyid => $event_info) {
					[$stamp,$name] = keyval($event_info);
					?><tr<?
					if ($tmppartyid === $partyid) {
						?> class="light"<?
					}
					?>><?
					?><td class="right"><?= _date_get($stamp,short: true) ?>:&nbsp;</td><?
					?><td class="lpad"><?
					echo get_element_link('party',$tmppartyid,$name);
					if ($tmppartyid === $partyid) {
						?> (<?= element_name('current_event') ?>)<?
					}
					?></td><?
					?></tr><?
				}
				?></table><?
				?></div><?
				$warnings[] = ob_get_clean();
			}
		}
		if (!empty($no_facebook)) {
			$warnings[] =
				'<span class="warning">'.
				str_replace(
					'%FBICON%',
					get_facebook_icon('redhue'),
					__('party:warning:no_facebook_connected', DO_UBB | KEEP_EMPTY_KEYWORDS)
				).'</span>';
		}
		ob_start();
		require_once '_presale.inc';
		show_no_presale_indication($party,true);
		if ($data = ob_get_clean()) {
			$warnings[] = $data;
		}
		if (empty($party['EROTIC'])) {
			if ($all_erotic_org) {
				$warnings[] = '<span class="warning">'.__('erotic:info:non_erotic_but_mainly_erotic_organizer', DO_UBB).'</span>';
			} elseif (!empty($location['EROTIC'])) {
				$warnings[] = '<span class="warning">'.__('erotic:info:non_erotic_but_mainly_erotic_location', DO_UBB).'</span>';
			}
		}

		if ($party['MIN_AGE'] >= 1
		&&	$party['MIN_AGE'] < 15
		&&	!$party['MAX_AGE']
		) {
			$warnings[] = '<span class="warning">'.__('party:warning:minimum_age_unusual_LINE', DO_UBB, ['MIN_AGE' => $party['MIN_AGE']]).'</span>';
		}
		if ($party['MAX_AGE'] > 50) {
			$warnings[] = '<span class="warning">'.__('party:warning:maximum_age_unusual_LINE', DO_UBB, ['MAX_AGE' => $party['MAX_AGE']]).'</span>';
		}

		if (!$party['LIVESTREAM']
		&&	!$party['LOCATIONID']
		&&	!$party['CITYID']
		) {
			$warnings[] = '<span class="warning">'.__('party:warning:no_location_no_city_LINE', DO_UBB).'</span>';
		}
	}

	if (!empty($warnings)) {
		layout_open_box('orange');
		?><div style="font-size: 200%; position: absolute; right: 1em;"><?
		show_warning_icon();
		?></div><?
		echo implode('<hr class="slim" />', $warnings);
		layout_close_box();
	}


	///////////////////////// ORGANIZATION

	$GLOBALS['__show_fb_events'] = true;
	if (!($result = do_organization_stuff($party, $stuff))) {
		return;
	}
	[/* $orgparts */, $show_site_spec] = $result;
	unset($GLOBALS['__show_fb_events']);

	if ($party['STAMP'] + $party['DURATION_SECS'] < CURRENTSTAMP
	&&	$allorgs
	) {
		require_once '_conceptsimilars.inc';
		$all_orgs_idstr = implode(', ', $allorgs);
		$similars = get_concept_similars($party['NAME']);
		$namestr = stringsimplode(', ', $similars);
		if ($next_edition = memcached_rowuse_hash(['party','boarding','location','connect'], "
			SELECT	PARTYID,STAMP, STAMP_TZI, SUBTITLE, LOCATIONID,
					COALESCE(location.CITYID, boarding.CITYID, party.CITYID) AS CITYID,
					(	SELECT GROUP_CONCAT(ASSOCID)
						FROM connect
						WHERE MAINTYPE = 'party'
						  AND MAINID = PARTYID
						  AND ASSOCTYPE = 'organization'
						  AND ASSOCID IN ($all_orgs_idstr)
					) AS ORGIDS
			FROM party
			LEFT JOIN boarding USING (BOARDINGID)
			LEFT JOIN location USING (LOCATIONID)
			WHERE party.NAME IN ($namestr)
			  AND PARTYID != $partyid
			  AND STAMP > ".CACHESTAMP.'
			  AND STAMP >= '.($party['STAMP'] + ONE_WEEK)."
			  AND CANCELLED = 0
			  AND NOT ISNULL((
				SELECT ASSOCID
				FROM connect
				WHERE MAINTYPE = 'party'
				  AND MAINID = PARTYID
				  AND ASSOCTYPE = 'organization'
				  AND ASSOCID IN ($all_orgs_idstr)
				LIMIT 1))
			ORDER BY STAMP
			LIMIT 1")
		) {
			$next_edition_id = array_key_first($next_edition);
			$next_edition_series = memcached_rowuse_hash(['connect','party','boarding','location'], "
				SELECT	PARTYID, STAMP, STAMP_TZI, SUBTITLE, LOCATIONID,
						COALESCE(location.CITYID, boarding.CITYID, party.CITYID) AS CITYID
				FROM connect
				JOIN party ON PARTYID=ASSOCID
				LEFT JOIN boarding USING (BOARDINGID)
				LEFT JOIN location USING (LOCATIONID)
				WHERE party.NAME IN ($namestr)
				  AND MAINTYPE = 'party'
				  AND MAINID = $next_edition_id
				  AND ASSOCTYPE = 'party'
				  AND CANCELLED = 0
				  AND NOT ISNULL((
					SELECT ASSOCID
					FROM connect
					WHERE MAINTYPE = 'party'
					  AND MAINID = PARTYID
					  AND ASSOCTYPE = 'organization'
					  AND ASSOCID IN ($all_orgs_idstr)
					  LIMIT 1
				  ))"
			);
			$single = !$next_edition_series;

			$next_edition_series += $next_edition;

			number_asort($next_edition_series,'STAMP');

			foreach ($next_edition_series as $e_partyid => $edition) {
				$subtitles[mb_strtolower($edition['SUBTITLE'])] = true;
			}
			$subs = count($subtitles);

			[$first_id] = keyval($next_edition_series);

			if (!($first_event = memcached_party_and_stamp($first_id))) {
				if ($first_event !== false) {
					register_nonexistent($party, $first_id);
				}
				return;
			}
			change_timezone('UTC');
			layout_open_box('larger white new-event');
			?><div class="block"><?
			?><span class="win"><?= Eelement_name('new_event') ?></span> <?
			if ($single) {
				?>&rarr; <a href="<?= get_element_href('party',$first_id) ?>"><?
			} else {
				$popular_id = db_single('going','
					SELECT PARTYID
					FROM (	SELECT PARTYID,COUNT(*) AS CNT
							FROM going
							WHERE PARTYID IN ('.implodekeys(',',$next_edition_series).')
					) countevents
					ORDER BY CNT DESC
					LIMIT 1'
				) ?: $first_id;
				?><a href="<?= get_element_href('party',$popular_id) ?>"><?
			}
			?><b><?= escape_utf8($first_event['NAME']) ?></b><?
			if ($subs <= 1) {
				if ($first_event['LOCATIONID']) {
					?>, <?
					echo escape_utf8(get_element_title('location', $first_event['LOCATIONID']));
				} elseif ($first_event['CITYID']) {
					?>, <?
					echo escape_utf8(get_element_title('city', $first_event['CITYID']));
				}
				if ($first_event['SUBTITLE']) {
					?> <?= MIDDLE_DOT_ENTITY ?> <?
					echo escape_utf8($first_event['SUBTITLE']);
				}
			}
			?> <?= MIDDLE_DOT_ENTITY ?> <?
			_dateday_display($first_event['STAMP_TZI']);
			?></a><?
			?></div><?
			if (!$single) {
				?><table class="dens"><?
				foreach ($next_edition_series as $e_partyid => $edition) {
					?><tr><?
					?><td>&rarr; <?= get_element_link('party',$e_partyid) ?></td><?
					if ($subs > 1
					&&	$edition['SUBTITLE']
					) {
						?><td>&nbsp;&middot; <?= escape_utf8($edition['SUBTITLE']) ?>,&nbsp;</td><?
					} else {
						?><td>,&nbsp;</td><?
					}
					?><td class="right"><?
					if ($edition['LOCATIONID']) {
						echo get_element_link('location',$edition['LOCATIONID']) ?>,&nbsp;<?

					} elseif ($edition['CITYID']) {
						echo get_element_link('city',$edition['CITYID']) ?>,&nbsp;<?
					}
					?></td><?
					?><td class="right"><? _dateday_display($edition['STAMP_TZI']) ?></td><?
					?></tr><?
				}
				?></table><?
			}
			change_timezone();
			layout_close_box();
		}
	}
	require_once '_status_overlay.inc';
	open_status_overlay($party);

	?><article itemscope itemtype="https://schema.org/MusicEvent"><?
	?><meta itemprop="url" content="<?= FULL_HOST, get_element_href('party',$partyid) ?>" /><?
	?><meta itemprop="description" content="<?= $meta_desc ?>" /><?
	show_vevent($party);
	if ($party['LOCATIONTYPE']) {
		$types = explode_to_hash(',', $party['LOCATIONTYPE']);
		if (isset($types['outdoor'], $types['beach'])) {
			unset($types['outdoor']);
		}
		foreach ($types as &$type) {
			$type = __('locationtype:'.$type);
		}
		unset($type);
		asort($types);
	} else {
		$types = [];
	}
	$party['locationtypes'] = $types;

	show_sales_contact_requested($partyid);

	require_once '_drag.inc';
	open_drag();

if (false) {

	layout_open_box();
	?>HAHA<?
	layout_close_box();

	?>BUT WHY<?

	layout_open_box('party');
	?><header>HIUHI</header><?
	layout_close_box();

	 layout_open_box(
	  	(!$party['ACCEPTED'] ? ($party['EVENT_PSTAMP'] ? 'light6 ' : 'unaccepted ') : '').
	  	'party'
	 );

	?><meta itemprop="url" content="https://<?= $_SERVER['HTTP_HOST'], get_element_href('party',$partyid) ?>" /><?

	ob_start();
	if (have_user()) {
		?><img class="star<?
		if (!$going
		||	$going === GOING_MAYBE
		&&	$party['STAMP'] < CURRENTSTAMP
		) {
			?> hidden<?
		}
		?>" src="<?= STATIC_HOST ?>/star/<?= (empty($maybe) ? 'green' : 'transparent'),is_high_res() ?>.png"<?
		?> id="star" alt="<?= __('attrib:favourite') ?>" /><?
		?> <?
	}

	?><h1 itemprop="name"><?

	if ($show_site_spec) {
		[$show_element, $show_id, $show_site] = $show_site_spec;
		if ($party_admin
		||	!url_is_bad($show_element,$show_id)
		) {
			require_once '_jumper.inc';
			?><a target="_blank" title="<?= escape_specials($show_site) ?>" href="<?= jump_to($show_element,$show_id) ?>"><?
		} else {
			$show_site = $show_site_spec = false;
		}
	}
	echo escape_utf8($party['NAME']);

	if ($show_site_spec) {
		?></a><?
	}
	?></h1><?

	$name_part = ob_get_clean();

	if ($party['EDITION']) {
		$name_part .= ' <small>#'.$party['EDITION'].'</small>';
	}

	if (!empty($party['SUBTITLE'])) {
		ob_start();
		$subtitle_str = make_all_html($party['SUBTITLE'], UBB_UTF8);
		?><span class="subtitle"><?= $subtitle_str ?></span><?
		$subtitle_part = ob_get_clean();

		$no_middot = subtitle_no_middot($party['SUBTITLE']);
	} else {
		$subtitle_part = null;
	}

	$parts = [];
	if (!empty($camreq)) {
		ob_start();
		?><a href="/camera/<?= $party['PARTYID'] ?>"><?= element_name('camerarequest') ?></a><?
		$parts[] = ob_get_clean();
	}
	if ($userphotocnt) {
		ob_start();
		?><a href="/album/party/<?= $party['PARTYID']
		?>"><?= element_name('album') ?></a><small>,<?= $userphotocnt
		?></small><?
		$parts[] = ob_get_clean();
	}
	if ($show_votes
	=	!empty($medianvote)
	&&	$totalvotes >= NEED_VOTES
	) {
		include_style('votes');
		ob_start();
		?><a class="vote text-<?= vote_bar_name($medianvote)
		?>" href="#votes"><?= vote_name($medianvote)
		?></a><?
		$parts[] = ob_get_clean();
	}

	$parts[] = get_ical_feed_link();

	if (!$party['ACCEPTED']) {
		ob_start();
		?><b><?= __($party['EVENT_PSTAMP'] ? 'attrib:unpublished' : 'attrib:unapproved') ?></b><?
		$parts[] = ob_get_clean();
	}

	$right_part = $parts ? implode(' '.MIDDLE_DOT_ENTITY.' ',$parts) : null;

	ob_start();
	require_once '_videosandphotos.inc';
	$shoots = show_photos('party', $partyid);
	$photo_str = ob_get_clean();

	?><header><?
	?><div class="<?
	if ($going === GOING_CERTAIN) {
		?> going<?
	} elseif ($maybe) {
		?> going maybe<?
	}
	?> header" id="party-header"><?

	?><div class="l"><?
		?><div><?
		if ($party['CANCELLED']) {
			?><span class="ns error"><?= __('status:cancelled') ?>:</span> <?
		}
		if ($party['POSTPONED']) {
			?><span class="ns warning"><?= __('status:postponed') ?>:</span> <?
		}
		if ($party['LIVESTREAM'] === 'only') {
			echo element_name('livestream') ?>:<?
		}
		echo $name_part;
		if ($subtitle_part) {
			if (!$no_middot) {
				?> <?= MIDDLE_DOT_ENTITY ?> <?
			} else {
				?> <?
			}
			echo $subtitle_part;
		}
		if ($show_site_spec) {
			require_once '_site.inc';
			?><span class="lmrgn"> <?
			show_site_button($show_element, $show_id, $show_site, true, null, true);
			?></span><?
		}
		if ($galleries = memcached_simpler_array(['image', 'gallery'], "
			SELECT DISTINCT GALLERYID
			FROM image
			JOIN gallery USING (GALLERYID, PARTYID)
			WHERE PARTYID = $partyid
			  AND VISIBLE IN ('yes', 'visitors')
			  AND HIDDEN = 0")
		) {
			foreach ($galleries as $galleryid) {
				if (!($gallery = memcached_gallery($galleryid))
				||	!visible_gallery($gallery)
				) {
					continue;
				}
				?> <a href="<?= get_element_href('gallery',$galleryid) ?>"><?=
					get_camera_icon(element_plural_name('photo'))
				?></a><?
			}
		}

	if (isset($presences['facebook'])) {
		if ($fbs = get_fbs_for_party($partyid, $absolute_fb_jump)) {
			if (!$party_admin) {
				# only main and instances? pick main

				$main = null;
				$non_instance = false;
				$keep_fbs = [];

				foreach ($fbs as $fb) {
					if ($fb['ACCESS'] === 'private') {
						continue;
					}
					$keep_fbs[] = $fb;
					if ($fb['MAIN']) {
						$main = $fb;
					} elseif (!$fb['INSTANCE']) {
						$non_instance = true;
					}
				}
				$fbs =	$main
					&&	!$non_instance
					?	[$main]
					:	$keep_fbs;
			}
			require_once '_bubble.inc';
			if (($fb_total = count($fbs)) === 1) {
				require_once '_urlcheck.inc';
				?> <?
				/** @noinspection PhpRedundantOptionalArgumentInspection */
				extract($fbs[0], \EXTR_OVERWRITE);
			  		$fb_presence_jump = '/jumpto/presence/'.$PRESENCEID;
				if ($bubble
				=	$BAD
				&&	$party_admin
				&&	($status = get_url_status('presence', $PRESENCEID))
				?	new bubble(BUBBLE_CLEAN)
				:	null
				) {
					$bubble->catcher();
				}
				?><a<?
				?> target="_blank"<?
				?> title="facebook: <?= escape_utf8($SITE) ?>"<?
				?> class="hpad"<?
				?> href="<?= $fb_presence_jump ?>"<?
				?>><?= get_facebook_icon('middle bshd'.($BAD ? ($party_admin ? ' redhue' : ' light') : '')) ?></a><?
				if ($bubble) {
					$bubble->content();
					show_url_bubble($status, $SITE, 'presence', $PRESENCEID);
					$bubble->display();
				}
			} else {
				ob_start();
				$have_ok_url = false;
				foreach ($fbs as $fb) {
					/** @noinspection PhpRedundantOptionalArgumentInspection */
					extract($fb, \EXTR_OVERWRITE);
					$jump = '/jumpto/presence/'.$PRESENCEID;
					if (!$BAD) {
						$have_ok_url = true;
					}
					?><div<?
					if ($ACCESS !== null
					&&	$ACCESS !== 'public'
					) {
						?> class="light warning-nb"<?
					}
					?>><?
					if ($bubble
					=	$BAD
					&&	$party_admin
					&&	($status = get_url_status('presence', $PRESENCEID))
					?	new bubble(BUBBLE_CLEAN)
					:	null
					) {
						$bubble->catcher();
					}
					?><a<?
					?> target="_blank"<?
					?> title="facebook: <?= escape_utf8($SITE) ?>"<?
					?> class="hpad"<?
					?> href="<?= $jump ?>"<?
					?>><?
					echo get_facebook_icon('rmrgn middle bshd'.($BAD ? ' light' : null));
					if ($NAME) {
						echo escape_utf8($NAME);
					}
					if ($MAIN) {
						?> <small class="notice">= <?= __('party:whole') ?></small><?
					}
					if ($INSTANCE) {
						?> <small class="notice-nb">= <?= __('party:part') ?></small><?
					}
					if ($ATTENDING) {
						?> <small>&middot; <b><?= $ATTENDING ?></b></small><?
					} else {
						?> <small class="light">&middot; <b><?= $GUESTS ?></b></small><?
					}
					?></a><?
					if ($bubble) {
						$bubble->content();
						show_url_bubble($status, $SITE, 'presence', $PRESENCEID);
						$bubble->display();
					}
					?></div><?
				}
				$data = ob_get_clean();

				?> <div class="relative ib"><span class="hpad ptr" onclick="
					if (is_visible(this.nextSibling)) {
						return;
					}
					/** @type {Event} event */
					event?.stopPropagation();
					unhide(this.nextSibling);
					addevent(getobj('bodycontainer'), 'click', () => hide('multifb'));
				"><?=
					get_facebook_icon('middle bshd'.(!$have_ok_url ? ' light' : ''));
				?> <?= MULTIPLICATION_SIGN_ENTITY ?> <?=
					$fb_total
				?></span><?
				?><div<?
				?> id="multifb"<?
				?> class="hidden abs z10 blue nowrap"<?
				?> style="margin-top: 1em;"<?
				?>><?
				?><div class="ib header" onclick="event.stopPropagation()"><?= $data ?></div><?
				?></div><?

				?></div><?
			}
		}
	}
	if (have_admin('party')) {
		foreach (['instagram', 'residentadvisor'] as $social) {
			if (isset($presences[$social])
			&&	($link = array_key_first($presences[$social]))
			&&	($icon_html = $presences[$social][$link])
			&&	preg_match(match($social) {
					'instagram'			=> '!\binstagram\.com/(?:[^/]+/)?p/!u',
					'residentadvisor'	=> '!\bra\.co/events/\d+!u',
				}, $link)
			) {
				?><span class="lmrgn"><?= $icon_html ?></span><?
			}
		}
	}

	?></div><?
	?></div><?
	?><div class="r"><?

	echo $right_part;

	if (have_admin('party')) {
		require_once '_processlist.inc';
		processlist_show_closer('party',$partyid);
	}

	?></div><?
	?></div><?

	?></header><?

	ob_start();
	require_once '_buybutton.inc';
	$buy_button = party_display_presale_info($party);
	$presalestr = ob_get_clean();

	$image_above = SMALL_SCREEN;
	$flyer_swap = !SMALL_SCREEN && screen_smaller_than(1025);
	$flags = UPIMG_SCHEMA | UPIMG_LINK_ORIGINAL | ($image_above ? UPIMG_MAX_WIDTH : 0);

	if (!$have_front
	&&	!$have_back
	) {
		$flags |= UPIMG_SHOW_ELEMENT;
		if ($image_above) {
			if ($alternative_image) {
				?><div class="center block"><?
				uploadimage_show_from_img($alternative_image, UPIMG_NOCHANGE | $flags);
				?></div><?
			}
		} else {
			?><div class="right-float center"><?
			if ($alternative_image) {
				uploadimage_show_from_img($alternative_image, UPIMG_NOCHANGE | $flags);
			}
			?></div><?
		}
	} elseif (
		$have_front
	&&	$have_back
	) {
		$flags |= UPIMG_SHOW_HISTORY;
		if ($image_above) {
			?><div class="center block" id="front-image"><?
				?><div style="position:absolute;right:.2em;" onclick="swapdisplay('front-image', 'back-image');">&#9654;</div><?
				uploadimage_show_from_img($front_image,$flags);
			?></div><?
		} elseif ($flyer_swap && empty($front_image['MAY_CHANGE']) && empty($back_image['MAY_CHANGE'])) {
			// over each other
			$total_width = $front_image['WIDTH'] + $back_image['WIDTH'] / 4;

			include_js('js/flyerswap');
			$flags |= UPIMG_SWAPPER;

			?><div class="relative r" style="width:<?= $total_width ?>px; height:<?= max($front_image['HEIGHT'], $back_image['HEIGHT']) ?>px;"><?

			?><div class="flyer z2 abs" style="left: 0;" onclick="Pf_swapPartyFlyer(this);"><?
			uploadimage_show_from_img($front_image, $flags);
			?></div><?

			?><div class="flyer z1 abs light" style="right: 0;" onclick="Pf_swapPartyFlyer(this);"><?
			uploadimage_show_from_img($back_image, $flags);
			?></div><?

			?></div><?
		} else {
			?><div class="right-float center"><?
			uploadimage_show_from_img($front_image, $flags);
			if ($front_image['HEIGHT'] >= $front_image['WIDTH']
			&&	 $back_image['HEIGHT'] >=  $back_image['WIDTH']
			) {
				?> <?
			} else {
				?><br /><?
			}
			?> <?
			uploadimage_show_from_img($back_image,$flags);
			?></div><?
		}
	} else {
		$flags |= UPIMG_SHOW_HISTORY;
		?><div class="<?= $image_above ? 'center block' : 'right-float' ?>"><?
			$have_front
		?	uploadimage_show_from_img($front_image,$flags)
		:	uploadimage_show_from_img( $back_image,$flags);
		?></div><?
	}

	?><div style="width: 10em;" class="noverflow"></div><?

	/// EVENT_PSTAMP

	if ($party_admin) {
		if ($party['EVENT_PSTAMP'] > CURRENTSTAMP) {
			?><div class="<?
			if ($party['ACCEPTED']) {
				?>warning <?
			}
			?>block"><?= __('party:info:event_publication_LINE', ['DATE' => _datedaytime_get($party['EVENT_PSTAMP'])]);
			?></div><?
		}
	}

	if (!empty($party['EROTIC'])) {
		?><div class="block"><span class="erobg"><?= __('attrib:erotic') ?></span></div><?
	}

	$appic_admin = have_admin('appic');

	show_appic_link('party', $party, $allorgs);

	if (have_admin(['invoice', 'ad', 'newsad', 'promo'])
	&&	($party_partner_spec = explain_table('party_partner'))
	&&	!empty($party_partner_spec['PARTNER']->enum)
	&&	false !== ($party_partner = db_single_assoc('party_partner', '
			SELECT PARTNER, CNT
			FROM party_partner
			WHERE PARTYID = '.$partyid))
	&&	false !== ($package_price = db_rowuse_hash('partyflock_package_price', '
			SELECT PARTNER, PRICE, AGENDA_SPOTLIGHT_WEEKS
			FROM party_db.partyflock_package_price
			WHERE (PARTNER, STAMP) IN (
				SELECT PARTNER, MAX(STAMP) AS STAMP
				FROM party_db.partyflock_package_price
				GROUP BY PARTNER
			)'))
	) {
		assert(is_array($package_price)); # Satisfy EA inspection
		include_js('js/adminparty');
		?><div class="partnerchoice block<?
		if (!$party_partner) {
			?> no<?
		}
		?>"><?= get_partyflock_icon() ?> <?
		?><select<?
		?> data-value="<?
		if ($party_partner && $party_partner['PARTNER']) {
			echo addslashes($party_partner['PARTNER']);
		}
		?>"<?
		?> class="small"<?
		?> onchange="Pf.changePartyPartner(this, <?= $partyid ?>);"<?
		?>><?
		?><option value=""><?= element_name('no_partner') ?></option><?
		foreach ($party_partner_spec['PARTNER']->enum as $partner_type) {
			?><option<?
			if ($selected
			=	$party_partner
			&&	$party_partner['PARTNER'] === $partner_type
			) {
				?> selected<?
			}
			$partner_type = addslashes($partner_type);
			?> value="<?= $partner_type ?>"><?

			if ($selected
			&&	$party_partner
			&&	$party_partner['CNT'] > 1
			) {
				echo $party_partner['CNT'], MULTIPLICATION_SIGN_ENTITY ?> <?
			}
			echo $partner_type === 'Partyflock' ? escape_utf8('🥰') : $partner_type;

			if (!empty($package_price[$partner_type]['PRICE'])) {
				?><small><?
				?> &middot; &euro; <?
				echo $package_price[$partner_type]['PRICE'];
				if ($weeks = $package_price[$partner_type]['AGENDA_SPOTLIGHT_WEEKS'] ?? 0) {
					?>, <?=
						__('date:week_cnt', ['CNT' => $weeks])
					?> <?
					echo element_name('agenda');
				}
				?></small><?
			}
			?></option><?
		}
		?></select><?
		?></div><?
	}

	if (have_admin('pixel')) {
		require_once '_pixel.inc';
		show_pixel_status();
	}

	if (
		#$party_admin
		([$year, $month, $day] = _getdate($party['STAMP_TZI'], 'UTC'))
	&&	($tags = get_special_day_event_tags($party))
	) {
		$tag_names = [];
		foreach ($tags as $tag) {
			$tag_names[] = __('tag:'.$tag);
		}
		?><div class="bold tags block"><?
		?><span class="tag-field-name"><?= element_name('tag', isset($tags[1])) ?>:</span> <?
		?><?= implode(', ', $tag_names) ?></div><?
	}

	ob_start();
	if ($wholes) {
		require_once '_connect.inc';
		?><div class="block"><?= __('field:part_of') ?> <?
		$donewholes = [];
		$GLOBALS['__show_fb_events'] = true;
		show_org($wholes,'organization',$donewholes,' ');
		unset($GLOBALS['__show_fb_events']);
		?></div><?

		if (isset($donewholes['organization'])) {
			# remove umbrella orgs from regular orgs if they're singly used (happens when parent of umbrella is not umbrella itself)
			foreach ($donewholes['organization'] as $organizationid => $done) {
				unset($orgparts['organization:'.$organizationid]);
			}
		}
	}
	$whole_str = ob_get_clean();

	if (!empty($orgparts)) {
		foreach ($orgparts as $key => &$orgpart) {
			$orgpart = preg_replace_callback('!<a href="(/(?:organization|location)/.*?)">(.*?)</a>!', static function(array $match): string {
				[, $url, $name] = $match;
				ob_start();
				?><span itemprop="organizer" itemscope itemtype="https://schema.org/Organization"><?
				?><a itemprop="url" href="https://<?= $_SERVER['HTTP_HOST'],$url ?>"><?
					?><span itemprop="name"><?= $name ?></span><?
				?></a><?
				?></span><?
				return ob_get_clean();
			},$orgpart);
		}
		unset($orgpart);

		?><div class="block"><?= __('party:info:organized_by') ?> <?= implode(', ',$orgparts) ?></div><?

		if ($allorgs
		&&	$party_admin
		) {
			$warns = [];
			foreach ($allorgs as $organizationid) {
				if (!($linfo = db_single_array(['party','connect'], "
					SELECT PARTYID,STAMP
					FROM party
					JOIN connect
							 ON MAINTYPE = 'organization'
							AND	MAINID = $organizationid
							AND	ASSOCTYPE = 'party'
							AND	ASSOCID = PARTYID
					WHERE ASSOCID != $partyid
					  AND STAMP < {$party['STAMP']}
					ORDER BY STAMP DESC
					LIMIT 1"))
				) {
					continue;
				}
				[$lpartyid, $lstamp] = $linfo;

				$diff = $party['STAMP'] - $lstamp;

				if ($diff < 2 * ONE_YEAR) {
					continue;
				}
				$warns[] = __('party:warning:previous_long_ago_LINE', DO_UBB, [
						'ORGANIZATIONID'=> $organizationid,
						'PARTYID'		=> $lpartyid,
						'DATE'			=> _date_get($lstamp),
					]);
			}
			if ($warns) {
				?><div class="warning block"><?= implode('<br />', $warns) ?></div><?
			}
		}
	}

	if ($hosts = db_same_hash('partyarea','
		SELECT ELEMENT, host.ID, HAVE_IMAGE
		FROM (	(
				SELECT	"organization" AS ELEMENT, HOSTEDBYID AS ID, AREAID,
					(	SELECT 1
						FROM uploadimage_link ul
						WHERE TYPE = "orgashost"
						  AND ul.ID = HOSTEDBYID
						  AND PARENTELEMENT = "party"
						  AND PARENTID = PARTYID
						LIMIT 1
					) AS HAVE_IMAGE
				FROM partyarea
				WHERE PARTYID = '.$partyid.'
				  AND HOSTEDBYID != 0
			) UNION (
				SELECT	"organization" AS ELEMENT, ASSOCID AS ID, 1000000 AS AREAID,
					(	SELECT 1
						FROM uploadimage_link ul
						WHERE TYPE = ASSOCTYPE
						  AND ul.ID = ASSOCID
						  AND PARENTELEMENT = MAINTYPE
						  AND PARENTID = MAINID
						LIMIT 1
					) AS HAVE_IMAGE
				FROM connect
				WHERE MAINTYPE = "party"
				  AND MAINID = '.$partyid.'
				  AND ASSOCTYPE = "orgashost"
			) UNION (
				SELECT	"location" AS ELEMENT, ASSOCID AS ID, 1000000 AS AREAID,
					(	SELECT 1
						FROM uploadimage_link ul
						WHERE TYPE = ASSOCTYPE
						  AND ul.ID = ASSOCID
						  AND PARENTELEMENT = MAINTYPE
						  AND PARENTID = MAINID
						LIMIT 1
					) AS HAVE_IMAGE
				FROM connect
				WHERE MAINTYPE = "party"
				  AND MAINID = '.$partyid.'
				  AND ASSOCTYPE = "locashost"
			)
		) AS host
		LEFT JOIN hidden_orgs
			   ON ELEMENT = "organization"
			  AND host.ID = hidden_orgs.ID
		WHERE hidden_orgs.ID IS NULL
		ORDER BY AREAID'
	)) {
		$hostparts = [];
		$done_host = [];
		foreach ($hosts as $element => $hostids) {
			foreach ($hostids as $hostid => $hostinfo) {
				if ($element === 'organization'
				&&	isset($allorgs[$hostid])
				) {
					# already done this organization
					continue;
				}
				[, $have_custom_image] = keyval($hostinfo);

				ob_start();
				show_org($hostid, $element, $done_host, ', ', true, null, 0, false, 'party', $partyid, has_host_image: (bool)$have_custom_image);
				if ($party_admin) {
					link_to_facebook_events($element, $hostid);
					link_to_instagram($element, $hostid);
				}
				$hostparts[] = ob_get_clean();
			}
		}
		if ($hostparts) {
			?><div class="block"><?=
			str_replace(
				'%HOST%',
				implode(', ', $hostparts),
				__('party:info:hosted_by', KEEP_EMPTY_KEYWORDS, ['CNT' => count($hostparts)])
			);
			?> <?
			if ($have_lock) {
				?><a class="nowrap seembutton" href="/party/<?= $partyid ?>/hostimages"><?= __('action:change_images') ?></a><?
			} else {
				?><span class="light nowrap seembutton"><?= __('action:change_images') ?></span><?
			}
			?></div><?
		}
	}

	if ($party['LIVESTREAM'] === 'only') {
		if ($locationids = db_rowuse_array(['connect', 'location'], '
			SELECT ASSOCID AS LOCATIONID, SITE
			FROM connect
			JOIN location ON ASSOCID = LOCATIONID
			WHERE MAINTYPE = "party"
			  AND MAINID = '.$partyid.'
			  AND ASSOCTYPE = "location"'
		)) {
			$show_list = [];
			foreach ($locationids as $location) {
				extract($location, EXTR_OVERWRITE);
				ob_start();
				echo get_element_link('location', $LOCATIONID);
				if (!empty($location['SITE'])) {
					?> <?
					show_site_button('location', $LOCATIONID, $SITE);
				}
				$show_list[] = ob_get_clean();
			}
			?><div class="block"><?
			echo element_name('location', count($show_list));
			?>: <?
			echo implode(', ', $show_list);
			?></div><?
		}

	}

	echo $whole_str;

	show_party_info($party);

	if ($party_admin
	&&	!$party['ACCEPTED']
	) {
		show_long_past($party);
		show_party_maybe_identicals($party);
	}

	if ($buy_button) {
		echo $buy_button;
	}

	show_genres($partyid, $party, true);

	if (have_uploadimage($eventmap)) {
		?><div class="block"><?
		?><a<?
		?> target="_blank"<?
		?> title="<?= __C('action:view_event_map') ?>"<?
		?> href="<?= IMAGES_HOST, uploadimage_get_url($eventmap, UPIMG_GET_ORIGINAL) ?>"><?
		show_map_icon();
		?><span class="lmrgn notice-nb"><?= element_name('event_map') ?></span><?
		?></a><?
		?></div><?
	}

	if ($parties) {
		$parties[$partyid] = null;
		# $basename = mb_strtolower($party['NAME']);
		$series = [];
		foreach ($parties as $tmp_partyid => &$tmp_party) {
			if (!($tmp_party = memcached_party_and_stamp($tmp_partyid))) {
				continue;
			}
			$series[
				$non_series =
				$tmp_party['LOCATIONID'] !== $party['LOCATIONID']
			||	$tmp_party['FESTIVAL']	 !== $party['FESTIVAL']
			||	$tmp_party['CONCERT']	 !== $party['CONCERT']
			||	$tmp_party['PREPARTY']	 !== $party['PREPARTY']
			||	$tmp_party['AFTERPARTY'] !== $party['AFTERPARTY']
			][$tmp_partyid] = $tmp_party;
		}
		unset($tmp_party);
		if (isset($series[0])) {
			$series[0][$partyid] = $party;
		}
		foreach ($series as $non_series => &$parties) {
			number_asort($parties, 'STAMP');
			$previous_stamp = null;
			$same_stamps[$non_series] = [];
			foreach ($parties as $tmp_partyid => $tmp_party) {
				$same_stamps[$non_series][$tmp_party['STAMP']][$tmp_party['PARTYID']] = true;
			}
		}
		unset($parties);
		ksort($series);
		$shown = [];
		foreach ($series as $non_series => $parties) {
			$show_subtitle = false;
			foreach ($same_stamps[$non_series] as $stamp => $stamps) {
				if (count($stamps) > 1) {
					$show_subtitle = true;
					break;
				}
			}
			$table_data = '';
			foreach ($parties as $tmp_partyid => $tmp_party) {
				if (!($iscurrent = $tmp_partyid === $partyid)
				&&	(	!$tmp_party['ACCEPTED']
					||	(	(	$tmp_party['CANCELLED']
							||	$tmp_party['MOVEDID']
							)
							&&	!going_to_party($tmp_partyid)
						)
					)
				&&	!have_admin('party')
				) {
					continue;
				}
				$shown[$tmp_partyid] = true;

				ob_start();
				?><tr class="<?
				if (!$tmp_party['ACCEPTED']) {
					?>unaccepted <?
				}
				if ($iscurrent) {
					?>win <?
				}
				?>"><?
				?><td><?
				echo get_element_link('party', $tmp_partyid, $tmp_party['NAME']);
				if ($tmp_party['SUBTITLE']
				&&	(	$show_subtitle
					||	preg_match('"^(?:night|day|opening|after\*(?:hour|party)?)$"iu', $tmp_party['SUBTITLE'])
					#	^^ always show short descriptive subtitle
					)
				) {
					?><small> <?= MIDDLE_DOT_ENTITY ?> <?= esccape_utf8($tmp_party['SUBTITLE']) ?></small><?
				}
				?>,&nbsp;<?
				if ($non_series
				&&	$tmp_party['LOCATIONID']
				) {
					?></td><td><?= get_element_link('location', $tmp_party['LOCATIONID']) ?>,&nbsp;<?
				}

				?></td><?
				?><td class="right"><?
				_datedaytime_display($tmp_party, time_separator: ', ', short: true);
				?></td><?

				?><td><?
				if ($iscurrent) {
					?>&nbsp;&larr;<?
				}
				?></td><?

				?><td><?
				if ($tmp_party['CANCELLED'] || $tmp_party['MOVEDID']) {
					if ($tmp_party['CANCELLED']) {
						?> <span class="error"><?= __('status:cancelled') ?></span><?
					} else {
						?> <span class="warning"><? __('status:moved') ?></span><?
					}
				}
				?></td><?

				if ($party_admin
				&&	$tmp_party['SEPARATE']
				) {
					?><td class="light">&nbsp;<?= get_appic_icon(), NO_BREAK_SPACE_ENTITY, BROKEN_CIRCLE_WITH_NORTHWEST_ARROW_ENTITY ?></td><?
				}
				?></tr><?
				$table_data .= ob_get_clean();

			}
			if ($table_data
			&&	(	($cnt = count($shown)) > 1
				||	!isset($shown[$partyid])
				)
			) {
				?><div class="block"><b><?=
					 	$non_series
					?	__C('action:see_also')
					:	escape_utf8(get_element_title('party', $partyid)).' '.element_plural_name('day')
				?></b><br /><?
				?><table class="<?
				if ($cnt === 1) {
					?>faketable <?
				}
				?>hla dens nomargin vtop"><?= $table_data ?></table><?
				?></div><?
			}
		}
	}

	if ($party['RESTRICTION']) {
		?><div class="block"><b><?= Eelement_name('restriction') ?></b><br /><?=
			__C('party:info:'.$party['RESTRICTION']) ?>.<?
		?></div><?
	}

	if ($party['LIVESTREAM']
	&&	$party['LIVESTREAM_ADDRESS']
	&&	(	($in_future ??= $party['STAMP'] + $party['DURATION_SECS'] > CURRENTSTAMP)
		||	$party_admin
		||	have_admin('appic'))
	) {
		if (!$in_future) {
			?><div class="light colorless"><?
		}
		?><div class="block"><?
			require_once '_jumper.inc';
			?><a href="<?= jump_to('livestream',$partyid) ?>" target="_blank"><?
			?><div class="partner-button livestream" title="<?= __('action:list_to_livestream') ?>"><?
			echo __('action:list_to_livestream');
			?></div><?
			?></a><?
		?></div><?
		if ($party_admin){
			?><div class="notice block"><?
			?><b>SITE:</b> <?
			?><span class="light"><?=
				escape_utf8(db_single_string('party','SELECT LIVESTREAM_ADDRESS FROM party WHERE PARTYID='.$partyid) ?? '');
			?></span><?
			?></div><?
		}
		if (!$in_future) {
			?></div><?
		}
	}

	ob_start();
	require_once '_partyprices.inc';
	$party['prices'] = show_party_prices(
		$partyid,
		$party,
		$min_price,
		$max_price,
		$total_prices,
	);
	$party_prices_str = ob_get_clean();

	require_once '_presale.inc';
	[$sale_url] = get_generic_presale_link($partyid, $party);
	if ($sale_url) {
		$free = false;

		$agg = $min_price !== $max_price;

		?><div itemprop="offers" itemtype="<?= $agg ? 'https://schema.org/AggregateOffer' : 'htt[s://schema.org/Offer' ?>" itemscope><?
			?><meta itemprop="url" content="<?= $sale_url ?>" /><?
			if ($min_price) {
				if ($agg) {
					?><meta itemprop="lowPrice"  content="<? printf('%.2f',$min_price['PRICE'] / 100) ?>" /><?
					?><meta itemprop="highPrice" content="<? printf('%.2f',$max_price['PRICE'] / 100) ?>" /><?
				} else {
					?><meta itemprop="price" content="<?	 printf('%.2f',$min_price['PRICE'] / 100) ?>" /><?
				}
				?><meta itemprop="priceCurrency" content="<?= $min_price['CURRENCY'] ?>" /><?
			}
			?><meta itemprop="availability" content="<?= $party['PRESALE_SOLD_OUT'] ? 'https://schema.org/SoldOut' : 'https://schema.org/InStock' ?>" /><?
			?><meta itemprop="validFrom" content="<?= gmdate(ISO8601Z,$party['PRESALE_STAMP'] ?: $party['CSTAMP']) ?>" /><?
		?></div><?
	} else {
		$free = $party['FREE_ENTRANCE'];
	}

	if ($free) {
		?><meta itemprop="isAccessibleForFree" content="<?= $free ? 'True' : 'False' ?>" /><?
	}

	if ($party_prices_str
	||	$presalestr
	||	$party['DOORONLY']
	||	$party['PRESALEONLY']
	||	$party['INVITEONLY']
	) {
		if ($party_prices_str
		||	$presalestr
		) {
			?><div class="block bmrgn"><?
			?><h2 id="tickets"><?
			echo Eelement_plural_name('ticket');
			if (!empty($party['prices'])) {
				?> &amp; <?
				echo element_name('price', count($party['prices']));
			}
			?> <span class="light"><?= escape_utf8($party['NAME']) ?></span><?
			?></h2><?
			?></div><?
		}
		if ($party['DOORONLY'] || $party['PRESALEONLY']) {
			?><div class="block"><?
			if ($party['DOORONLY']) {
				echo __('party:info:only_doorsale_TEXT', DO_NL2BR);
			}
			if ($party['PRESALEONLY']) {
				echo __('party:info:only_presale_TEXT', DO_NL2BR);
			}
			?></div><?
		}
		if ($party['INVITEONLY']) {
			?><div class="warning block"><?= __('party:info:only_on_invitation_TEXT', DO_NL2BR) ?></div><?
		}
	}

	if ($party_prices_str) {
		if (COLLAPSE_PRICES_STARTING_FROM
		&&	$total_prices >= COLLAPSE_PRICES_STARTING_FROM
		) {
			?><div class="block"><?
			if ($min_price) {
				require_once '_currency.inc';
				$linfo = get_locale_info();
				$price = (get_currency_prefix($min_price['CURRENCY']) ?: $min_price['CURRENCY']).' '.
					str_replace(' ','&thinsp;',number_format((int)($min_price['PRICE']/100),0,$linfo['decimal_point'],' ')).
					$linfo['decimal_point'].
					(!($min_price['PRICE'] % 100) ? '-' : sprintf('%02d',$min_price['PRICE'] % 100));

				echo __C('party:info:tickets_starting_from',['PRICE' => $price]);
				?>, <?
			}
			?><span class="unhideanchor" onclick="swapdisplay('prices')"><i><?= __('action:view_all_prices',$min_price ? 0 : DO_CAPFIRST) ?></i> &rarr;</span><?
			?></div><?
			?><div id="prices" class="hidden"><?= $party_prices_str ?></div><?
		} else {
			echo $party_prices_str;
		}
	}

	if ($presalestr) {
		echo $presalestr;
	}

	$party['CERTAIN_CNT'] = $certain_cnt;
	$party[  'MAYBE_CNT'] =   $maybe_cnt;

	ob_start();
	$booking_com_href = show_booking_com('party',$partyid,$party);
	$booking_com_str = ob_get_clean();

	$contest_lists = [];
	if ($contests = memcached_rowuse_array(['contest','contestresponse'],'
		SELECT	CONTESTID, TYPE, CLOSED, AMOUNT, GROUPS, NAME, EXTERNAL, PSTAMP, ACTIVE, ONLYPROMO, VISIBLE_GROUPS, ENTRANCE_INCLUDED, BODY,
				(SELECT COUNT(*) FROM contestresponse WHERE contestresponse.CONTESTID = contest.CONTESTID) AS CNT
		FROM contest
		JOIN (	SELECT CONTESTID
				FROM contest
				WHERE EXTERNAL = 0
				  AND PARTYID = '.$party['PARTYID'].'
				UNION
				SELECT ASSOCID AS CONTESTID
				FROM connect
				WHERE MAINTYPE = "party"
				  AND MAINID = '.$party['PARTYID'].'
				  AND ASSOCTYPE = "contest"
		) AS connected_contests USING (CONTESTID)
		WHERE EXTERNAL = 0')
	) {
		$contest_admin = have_admin('contest');
		foreach ($contests as $contest) {
			if ($contest['PSTAMP'] > CURRENTSTAMP) {
				if ($contest_admin) {
					$contest_lists[FUTURE_CONTEST][] = $contest;
				}
				continue;
			}
			if (!may_view_element('contest', $contest['CONTESTID'], $contest)) {
				continue;
			}
			if (!$contest['ACTIVE']) {
				if ($contest_admin) {
					$contest_lists[INACTIVE_CONTEST][] = $contest;
				}
				continue;
			}
			$contest_lists[$contest['CLOSED'] ? CLOSED_CONTEST : OPEN_CONTEST][] = $contest;
		}
	}

	$bookingcombut_id = false;

	$partybussen = $party['partybussen'] = memcached_simple_hash(['party', 'partybussen'], '
		SELECT PARTYID,OTHERID
		FROM partybussen
		JOIN party USING (PARTYID)
		WHERE STAMP > '.(TODAYSTAMP - ONE_DAY)
	);

	$partybussenbut_id = $party['partybussenbut_id'] = false;
	$partybussen = $partybussen[$partyid] ?? null;

	ob_start();

	$setlist = [];

	$parts = [];

	if ($booking_com_href) {
		echo $booking_com_str;

		$parts[] = element_name('accomodation/stayover');
	}

	if ($partybussen
	&&	$party['STAMP'] > CURRENTSTAMP
	) {
		$title = escape_utf8(
			CURRENTLANGUAGE === 'nl'
		?	"Boek je bus en reis naar {$party['NAME']} via Partybussen.nl"
		:	"Book your bus trip and travel to {$party['NAME']} via Partybussen.nl"
		);
		?><a rel="nofollow" href="/order_ticket/partybussen/<?= $partyid ?>" target="_blank"><?
		?><div class="partner-button partybussen" title="<?= $title ?>"><?
		?><img alt="<?= $title ?>" src="<?= STATIC_HOST ?>/buttons/partybussen.png" /><?
		?></div><?
		?></a> <?

		$parts[] = element_name('travel');

		db_insert('partybussenhit','
		INSERT INTO partybussenhit SET
			IPBIN	="'.addslashes(CURRENTIPBIN).'",
			USERID	='.CURRENTUSERID.',
			IDENTID	='.CURRENTIDENTID.',
			STAMP	='.CURRENTSTAMP.',
			PARTYID	='.$partyid
		);
	}

	if (!empty($contest_lists[OPEN_CONTEST])) {
		$open_contest_count = count($contest_lists[OPEN_CONTEST]);
		if ($open_contest_count > 1) {
			$have_unique = [];
			foreach ($contest_lists[OPEN_CONTEST] as $contest) {
				increment_or_set($have_unique, $contest['TYPE'].':'.$contest['BODY'], 1);
			}
			if (count($have_unique) !== $open_contest_count) {
				mail_log('too many non unique contests for party '.$partyid.': '.$party['NAME'], item: $contest_lists);
			}
		}
		foreach ($contest_lists[OPEN_CONTEST] as $contest) {
			?><a<?
			?> title="<?= contest_get_type($contest['AMOUNT'], $contest['TYPE']) ?>"<?
			?> href="<?= get_element_href('contest', $contest['CONTESTID']) ?>"<?
			?> target="_blank"<?
			?>><?
			?><div class="partner-button winticket"><?
				?><span class="win"><?= __('incentive:WIN') ?></span><?
			?></div><?
			?></a> <?
			$parts[] = __('action:win');
		}
	}

	if (!empty($appic_event['WINGAME'])
	&&	CURRENTSTAMP < $party['STAMP']
	) {
		$url = "https://appic.events/event/{$appic_event['APPICID']}?source=PF";
		?> <a<?
		?> title="<?= escape_utf8($appic_event['WINGAME_TITLE']) ?>"<?
		?> href="<?= add_utm($url) ?>"<?
		?> target="_blank"><?
		?><div class="partner-button appicwin"><?=
			get_appic_icon() ?> <span class="win"><?= __('incentive:WIN') ?></span><?
		?></div><?
		?></a><?
		$parts[] = get_appic_icon().' '.__('action:win');
	}

	if ($buttons_str = ob_get_clean()) {
		?><div class="block"><?
		?><div class="bold bmrgn"><?= ucfirst(implode(', ', $parts)) ?></div><?
		?><div><?= $buttons_str ?></div><?
		?></div><?
	}

	require_once '_bubble.inc';
	$bubble = new bubble(HELP_BUBBLE | BUBBLE_30_WIDE | LIGHT_SUP);
	$bubble->content(__('minimum_age:information_TEXT',DO_NL2BR | DO_UBB,['SPEC'=>true]));

	if (!$party['LIVESTREAM']) {
		if ($party['MAX_AGE']
		||	$party['MIN_AGE']
		||	$party['MIN_AGE_FEMALE']
		) {
			if (have_user()) {
				global $currentuser;
				if ($currentuser->row
				&&	!empty($currentuser->row['BIRTH_YEAR'])
				) {
					$age_range = _age_range(
						$currentuser->row['BIRTH_YEAR'],
						$currentuser->row['BIRTH_MONTH'],
						$currentuser->row['BIRTH_DAY'],
						$party['STAMP'],
						$currentuser->row['LDAY']
					);
					if ($age_range) {
						if (count($age_range) === 1) {
							$min_age = key($age_range);
							$max_age = $min_age;
						} else {
							$min_age = key($age_range);
							$max_age = key($age_range);
						}

						$use_age =
							$party['MIN_AGE_FEMALE']
						&&	$currentuser->row['SEX'] === 'F'
						?	'MIN_AGE_FEMALE'
						:	'MIN_AGE';

						$age_warn =
							$party[$use_age] && $min_age < $party[$use_age]
						||	$party['MAX_AGE'] && $max_age > $party['MAX_AGE'];
					}
				}
			}
			?><div class="block"><?
			if ($party['MAX_AGE']) {
				if ($party['MIN_AGE']) {
					$bubble->catcher(Eelement_name('age'));
					ob_start();
					$bubble->display();
					$header = ob_get_clean();
					?><div class="bold"><?= $header ?></div><?
					echo __('party:info:age_range_LINE',['MIN_AGE'=>$party['MIN_AGE'],'MAX_AGE'=>$party['MAX_AGE']]);
				} else {
					$bubble->catcher(Eelement_name('maximum_age'));
					ob_start();
					$bubble->display();
					$header = ob_get_clean();
					?><div class="bold"><?= $header ?></div><?
					echo $party['MAX_AGE'];
				}
			} else {
				$bubble->catcher(Eelement_name('minimum_age'));
				ob_start();
				$bubble->display();
				$header = ob_get_clean();
				?><div class="bold"><?= $header ?></div><?
				echo $party['MIN_AGE'];
			}
			if ($party['MIN_AGE_FEMALE']) {
				?> (<?= element_plural_name('woman') ?> <?= $party['MIN_AGE_FEMALE'] ?>)<?
			}
			?></div><?
			if (!empty($age_warn)
			&&	CURRENTSTAMP < $party['STAMP'] + $party['DURATION_SECS']
			) {
				?><div class="small warning block"><?= __('party:warning:age_not_right_LINE') ?></div><?
			}
		} else {
			$bubble->catcher(__C('party:info:no_minimum_age_LINE'));
			ob_start();
			$bubble->display();
			$header = ob_get_clean();
			?><div class="block"><b><?= $header ?></b><?
			if (!$party['ACCEPTED']
			&&	$party['LOCATIONTYPE'] === 'indoor'
			) {
				?> <span class="warning">(<?= __('locationtype:indoor') ?>?)</span><?
			}
			?></div><?
		}
	}
	if ($party['VISITORCNT']) {
		?><div class="block"><?
		?><b><?= Eelement_plural_name('visitor') ?></b><br /><?
		if (!$party['ACCEPTED']
		&&	$party['VISITORCNT']
		&&	(	empty($location['CAPACITY'])
			||	$location['CAPACITY'] !== $party['VISITORCNT']
			)
		&&	(	($caploc = !empty($location['CAPACITY']) && $party['VISITORCNT'] > $location['CAPACITY'])
			||	$party['VISITORCNT'] < 100
			||	$party['VISITORCNT'] > 100000
			)
		) {
			?><span class="warning"><?
			echo __($caploc ? 'party:warning:capacity_party_exceeds_location_LINE' : 'party:warning:strange_visitorcnt_LINE',DO_UBB,[
				'LOCATIONID'	=> $locationid,
				'PARTYID'		=> $partyid,
				'CAPACITY'		=> $location['CAPACITY'] ?? null,
				'MAXVISITORS'	=> $party['VISITORCNT']
			]);
			?></span><br /><?
		}
		echo __C('attrib:up_to');
		?> <?=
			$party['VISITORCNT'];
		?>.<?
		?></div><?
	}
	require_once 'defines/dresscodes.inc';
	foreach (DRESSCODE_FOR_WHO as $field => $for_who) {
		if (!$party[$field]) {
			continue;
		}
		?><div class="block"><?
		?><b><? show_dresscode($for_who) ?></b><?
		if ($party['DRESSCODE_MANDATORY']) {
			?> <span class="warning">(<?= __('dresscode:mandatory') ?>)</span><?
		}
		?><br /><?= make_all_html(utf8_ucfirst($party[$field]), UBB_UTF8) ?></div><?
	}
	if ($meetings = memcached_rowuse_array(['meeting', 'meet'],'
		SELECT	meeting.MEETINGID, HOUR, MINS,
				(SELECT COUNT(*) FROM meet WHERE meet.MEETINGID = meeting.MEETINGID) AS CNT
		FROM meeting
		WHERE PARTYID = '.$party['PARTYID'].'
		ORDER BY CNT DESC'
	)) {
		$meetinglistsize = count($meetings);
		?><div class="block"><b><?
		if ($meetinglistsize === 1) {
			?><a href="/meeting/<?= $meetings[0]['MEETINGID'] ?>"><?= Eelement_name('meeting') ?></a><?
		} else {
			?><a href="/party/<?= $party['PARTYID'] ?>/meetings#meetings"><?= Eelement_plural_name('meeting') ?></a><?
		}
		?></b><br /><?
		$endstamp = $party['STAMP'] + $party['DURATION_SECS'];
		if ($meetinglistsize === 1) {
			$meeting = $meetings[0];

			$link =	'<a href="/meeting/'.$meeting['MEETINGID'].'">';

			?>Er <?=
			$endstamp < CURRENTSTAMP
			?	'was'
			:	'is'
			?> <?= $link;
			?>een meeting</a>, om <?
			echo $link,$meeting['HOUR'];
			?>:<?= $meeting['MINS'];
			?></a>, met <?= $meeting['CNT'];
			?> bezoeker<?
			if ($meeting['CNT'] > 1) {
				?>s<?
			}
		} else {
			?>Er <?=
			$endstamp < CURRENTSTAMP
			?	'waren'
			:	'zijn'
			?> <a href="/party/<?= $party['PARTYID'] ?>/meetings#meetings"><?= $meetinglistsize ?> meetings</a>, <?
			if ($meetinglistsize > 3) {
				$meetingcnt = 3;
				?> de drukste drie <?
			} else {
				$meetingcnt = $meetinglistsize;
			}
			foreach ($meetings as $meeting) {
				?>om <?
				$link = '<a href="/meeting/'.$meeting['MEETINGID'].'">';
				echo $link,$meeting['HOUR'];
				?>:<?= $meeting['MINS']
				?></a> met <?= $meeting['CNT']
				?> bezoeker<?
				if ($meeting['CNT'] > 1) {
					?>s<?
				}
				--$meetingcnt;
				if ($meetingcnt > 1) {
					?>, <?
				} elseif ($meetingcnt === 1) {
					?> en <?
				} else {
					break;
				}
			}
		}
		?>.</div><?
	}


	if (!empty($party['LOCKERS'])) {
		?><div class="block"><b><?= Eelement_plural_name('locker'); ?></b><br /><?=
			__('party:info:lockers_are_present_LINE');
		?></div><?
	}

	if ($contest_lists) {
		if (!have_admin('contest')) {
			$contest_lists = isset($contest_lists[CLOSED_CONTEST]) ? [CLOSED_CONTEST => $contest_lists[CLOSED_CONTEST]] : [];
		}

		foreach ($contest_lists as $type => $list) {
			$contestcnt = count($list);
			?><div class="bold"><?=
			match($type) {
				CLOSED_CONTEST		=> Eelement_name('contest', $contestcnt).' ('.__('status:closed').')',
				OPEN_CONTEST		=> Eelement_name('contest', $contestcnt),
				INACTIVE_CONTEST	=> Eelement_name('inactive_contest', $contestcnt),
				FUTURE_CONTEST		=> Eelement_name('future_contest', $contestcnt),
			}
			?></div><?
			$total = 0;
			require_once '_contest.inc';
			foreach ($list as $contest) {
				?><div class="body block"><?
				?><a class="link" href="<?= get_element_href('contest',$contest['CONTESTID']) ?>"><?=
					$contest['VISIBLE_GROUPS'], MULTIPLICATION_SIGN_ENTITY
				?> <?=
					$contest['AMOUNT'];
				?> <?=
					contest_get_type(2, $contest['TYPE'])
				?>: <?
				if ($contest['CNT']) {
					echo $contest['CNT'] ?: __('answer:no');
					?> <?
					echo element_name('participant', $contest['CNT']);
				} else {
					echo element_plural_name('no_participant');
				}
				?></a><?
				?></div><?
			}
		}
	}

	if (!empty($party['EXTRA'])) {
		if ($party_admin) {
			?><div class="block"><?
			?><div class="bold"><?= Eelement_plural_name('comment') ?></div><?
			?><div><?
			echo make_all_html($party['EXTRA'], UBB_UTF8);
			?></div><?
			?></div><?
		} else {
			?><div class="block"><?
			?><div class="bold"><span class="unhidelink" onclick="swapdisplay('extra')"><?= Eelement_plural_name('comment') ?></span></div><?
			?><div id="extra" class="hidden"><?=
				make_all_html($party['EXTRA'], UBB_UTF8);
			?></div><?
			?></div><?
		}
	}

	require_once '_alternate_name.inc';
	show_alternate_names('party', $partyid, $party['NAME']);
	require_once '_bio.inc';
	show_bio();

	if ($party['STAMP'] < CURRENTSTAMP
	&&	($reports = memcached_multirowuse_hash(['report','user_account'], "
			SELECT DISTINCT OFFICIAL, REPORTID, PARTYID, USERID, IF(OFFICIAL, TITLE, NULL) AS TITLE, IF(OFFICIAL, BODY, NULL) AS BODY
			FROM report
			JOIN user_account USING (USERID)
			WHERE ACCEPTED
			  AND PARTYID = $partyid
			UNION
			SELECT DISTINCT OFFICIAL, REPORTID, PARTYID, USERID, IF(OFFICIAL, TITLE, NULL) AS TITLE, IF(OFFICIAL, BODY, NULL) AS BODY
			FROM connect
			JOIN report ON ASSOCID=REPORTID
			JOIN user_account USING (USERID)
			WHERE ACCEPTED
			  AND ASSOCTYPE = 'report'
			  AND MAINTYPE = 'party'
			  AND MAINID = $partyid"))
	) {
		$reportids = [];
		foreach ($reports as /* $official => */ $reportlist) {
			$reportids = [...$reportids, array_column($reportlist, 'REPORTID')];
		}
		sort($reportids);
		$reportviews = memcached_simple_hash('report_counter','
			SELECT REPORTID,SUM(VIEWS)
			FROM report_counter
			WHERE REPORTID IN ('.implode(',',$reportids).')
			GROUP BY REPORTID'
		);
		if (isset($reports[0])) {
			$size = count($reports[0]);
			?><div class="body block"><b><?= Eelement_name('report',$size) ?></b><br /><?
			foreach ($reports[0] as $report) {
				/** @noinspection PhpRedundantOptionalArgumentInspection */
				extract($report, \EXTR_OVERWRITE);
				$reportlinks[] =
					'<a'.
						' class="itemlink"'.
						' title="'.__('report:title:of_event_by_user',DO_UBBFLAT,['PARTYID'=>$PARTYID,'USERID'=>$USERID]).'"'.
						' href="'.get_element_href('report',$REPORTID).'">'.(
						invisible_profile($USERID)
					?	__('field:anonymous')
					:	escape_specials(get_element_title('user',$USERID))
					).
					'</a> <small>('.($reportviews[$REPORTID] ?? 0).')</small>';
			}
			echo str_replace(
				'%REPORTLIST%',
				implode(', ', $reportlinks) ,__('report:info:reports_by', KEEP_EMPTY_KEYWORDS, [
					'CNT'	=> $size,
				]
			));
			?></div><?
		}
		if (isset($reports[1])) {
			require_once '_reports.inc';
			show_reports($reports[1]);
		}
	}

	$shown = layout_display_alteration_note($party, true);

	layout_close_box();

}

	close_drag();

	?><hr />TEST<hr /><?

	if (!TRY_WITHOUT_FRESHEN
	&&	$have_lock
	) {
		unset_nomemcache();
	}

	if ($camerarequest_admin || $am_employee || have_admin('photographer')) {
		require_once '_camreqspec.inc';
		show_camreq_menu($party,$am_employee ? AS_EMPLOYEE : null);
	}

	///// going menu:
	layout_open_menu();
	ob_start();
	require_once '_goingmenu.inc';
	show_going_menu($going, $party);
	if ($goingmenu = ob_get_clean()) {
		layout_open_menuitem();
		include_js('js/going');
		include_js('js/bubble');
		?><div id="goingmenu" class="ib"><?= $goingmenu ?></div><?
		layout_close_menuitem();
	}
	if (setting_isset(SHOW_GOOGLE_CALENDAR)) {
		$livestream = $party['LIVESTREAM'] === 'only';
		$uselocation = $boarding ?: $location;
		$city = $party['city'] ?? null;
		layout_open_menuitem();
		ob_start();
		?>https://www.google.com/calendar/event?action=TEMPLATE<?
		?>&amp;text=<?= urlencode($party['NAME']);
		?>&amp;dates=<?= gmdate('Ymd\\THis\\Z',$party['STAMP']) ?>/<?= gmdate('Ymd\\THis\\Z',$party['STAMP'] + $party['DURATION_SECS']);

		if ($location_part
		=	$party['LIVESTREAM'] === 'only'
		?	$party['LIVESTREAM_ADDRESS']
		:	(	$uselocation
			?	preg_replace('"\s*\([^)]*\)"u', '', $uselocation['ADDRESS']).
				($uselocation['ZIPCODE'] ? ', '.$uselocation['ZIPCODE'] : '').
				($city ? ', '.$city['NAME'].', '.win1252_to_utf8(get_element_title('country', $city['COUNTRYID'])) : '')
			:	(	$party['LOCATION_ADDR']
				?:	($city_name = $party['CITYID'] ? get_element_title('city', $party['CITYID']) : '')
				)
			)
		) {
			?>&amp;location=<? echo urlencode($location_part);
		}

		?>&amp;trp=true<?
		?>&amp;sprop=<?= urlencode(FULL_HOST.'/');
		?>&amp;sprop=name:<? echo urlencode('Partyflock');
		?>&amp;details=<?= urlencode(
			FULL_HOST.get_element_href('party', $partyid)."\n\n".
			Eelement_name('location').': '.(
				$livestream
			?	$party['LIVESTREAM_ADDRESS']
			:	(	$uselocation
				?	$uselocation['NAME'].($city ? ', '.$city['NAME'] : '')
				:	(	$party['LOCATION_ADDR']
					?:	(	$party['UNKNOWN_LOCATION']
						?	win1252_to_utf8(__('field:unknown'))
						:	$city_name
						)
					)
				)
			)
		);
		$url = ob_get_clean();
		if ($lineup = get_party_lineup($partyid)) {
			$amongstr = urlencode('('.Eelement_name('among_others').') ');
			$spaceleft = 2048 - strlen($url) - 1 - strlen($amongstr);
			$commastr = urlencode(', ');
			$parts = [];
			foreach ($lineup as $name) {
				$nextpart = urlencode($name);
				$spaceleft -= strlen($nextpart) + strlen($commastr);
				if ($spaceleft <= 0) {
					$doamong = true;
					break;
				}
				$parts[] = $nextpart;
			}
			$url .= urlencode("\n\n".Eelement_name('line-up').': ').(isset($doamong) ? $amongstr : null).implode($commastr,$parts);
		}
		?><a target="_blank" class="nowrap" href="<?= $url ?>"><?= __('agenda:add_to_google') ?></a><?
		layout_close_menuitem();
	}
	layout_close_menu();

	require_once '_advice.inc';
	show_event_advice($party);

	if ($show_votes) {
		vote_show_box($medianvote);
	}
	require_once '_voice.inc';
	if (may_speak($party)) {
		vote_display_choices();
	}

	require_once '_ratinglist.inc';
	$ratinglist = new _ratinglist();
	$ratinglist->item($party);
	$ratinglist->show_form();
	$ratinglist->display();

	echo $photo_str;

	if (!empty($eventmap))
	if (have_uploadimage($eventmap)) {
		layout_open_box('party');
		layout_box_header('<h2>'.Eelement_name('event_map').' '.escape_utf8(get_element_title('party',$partyid)).'</h2>');
		uploadimage_show_from_img($eventmap, UPIMG_LINK_ORIGINAL | UPIMG_NOCHANGE);
		layout_close_box();
	}

	if ($show_lineup_options
	=	$show_lineup
	&&	$have_lock
	&&	require_admin_or_my_party($party)
	) {
		require_once '_lineup.inc';
		show_lineup_menu($party);
	}
	?><div class="clear"></div><?

	ob_start();

	# FB visitiors
	$fbcounters = memcached_single_assoc('fbcounters','
		SELECT ATTENDING,MAYBE,FBID
		FROM fbcounters
		JOIN fbid USING (FBID)
		WHERE ELEMENT="party"
		  AND ID='.$partyid.'
		ORDER BY ATTENDING+MAYBE DESC LIMIT 1',
		FIVE_MINUTES,
		'fbcounters:party:'.$partyid
	);

	layout_open_table(TABLE_FULL_WIDTH | TABLE_VTOP);

	# VISITORS
	#	$evercnt = $party['STAMP'] < 1192373387 ? null : get_bgquery(BGQRY_VISITORS_EVER,'party',$partyid);
	#
	#	PARTYFLOCK counters

	if ($certain_cnt) {
		layout_start_reverse_row();
		[$linkopen, $linkclose] = get_action_open_close('visitors');
		$linkopen = str_replace('<a ','<a rel="nofollow" ',$linkopen);
		echo $linkopen,$certain_cnt,$linkclose;
		layout_value_field(get_partyflock_icon());
		echo $linkopen,element_name('visitor',$certain_cnt),$linkclose;
		layout_stop_row();
	}
	if ($maybe_cnt) {
		layout_start_reverse_row();
		[$linkopen, $linkclose] = get_action_open_close('visitors','maybevisitors');
		$linkopen = str_replace('<a ','<a rel="nofollow" ',$linkopen);
		echo $linkopen,$maybe_cnt,$linkclose;
		layout_value_field(get_partyflock_icon('light6'));
		echo $linkopen,__('status:interested'),$linkclose;
		layout_stop_row();
	}

	# APPIC counters
	if (!empty($appic_event)) {
		$url = "https://appic.events/event/{$appic_event['APPICID']}?source=PF";
		$linkopen = $iconopen = '<a href="'.add_utm($url).'" target="_blank">';
		$linkclose = $iconclose = '</a>';
		if ($appic_certain_cnt = $appic_event['VISITORS']) {
			layout_start_reverse_row();
			echo $linkopen,$appic_certain_cnt,$linkclose;
			layout_value_field($iconopen.get_appic_icon().$iconclose);
			echo $linkopen,element_name('visitor',$appic_certain_cnt),$linkclose;
			layout_stop_row();
		}
		if ($appic_maybe_cnt = $appic_event['FOLLOWERS']) {
			layout_start_reverse_row();
			echo $linkopen,$appic_maybe_cnt,$linkclose;
			layout_value_field($iconopen.get_appic_icon('light6').$iconclose);
			echo $linkopen,__('status:interested'),$linkclose;
			layout_stop_row();
		}
	}

	if ($fbcounters
	&&	$party['STAMP'] < FACEBOOK_STOP_COUNTERS
	) {
		if ($fb_attending = $fbcounters['ATTENDING']) {
			layout_start_reverse_row();
			$link = '//www.facebook.com/'.$fbcounters['FBID'];
			$linkopen = '<a rel="nofollow" target="_blank" href="'.$link.'">';
			$linkclose = '</a>';

			echo $linkopen,$fb_attending,$linkclose;
			layout_value_field($linkopen.get_facebook_icon().$linkclose);
			echo $linkopen,element_name('visitor',$fb_attending),$linkclose;
			layout_stop_row();
		}
		if ($fb_maybe = $fbcounters['MAYBE']) {
			layout_start_reverse_row();
			$link = '//www.facebook.com/browse/event_members/?id='.$fbcounters['FBID'].'&amp;edge=temporal_groups%3Aassociates_of_temporal_group';
			$linkopen = '<a rel="nofollow" target="_blank" href="'.$link.'">';
			$linkclose = '</a>';

			echo $linkopen,$fb_maybe,$linkclose;
			layout_value_field($linkopen.get_facebook_icon('light6').$linkclose);
			echo $linkopen,__('status:interested'),$linkclose;
			layout_stop_row();
		}
	} elseif (
		(	FACEBOOK_SHOW_GUESTS
		||	have_admin()
		)
	&&	($guests = memcached_single_assoc(['fbid','facebook_guests'],'
			SELECT	MAX(GUESTS) AS GUESTS,
					MAX(GOING) AS GOING,
					MAX(INTERESTED) AS INTERESTED,
					STAMP,
					GI_STAMP
			FROM fbid
			JOIN facebook_guests USING (FBID)
			WHERE ELEMENT="party"
			  AND ID='.$partyid
		))
	) {
		/** @noinspection PhpRedundantOptionalArgumentInspection */
		extract($guests, \EXTR_OVERWRITE);
		$fbvis = [
			element_name('visitor',$GOING)	=> [$GOING,		null,		$GI_STAMP],
			__('status:interested')					=> [$INTERESTED,'light6',	$GI_STAMP],
		];
		if ($STAMP >= $GI_STAMP) {
			$fbvis[	element_plural_name('invitation').
					' + '.__('status:interested').
					' + '.element_name('visitor',$GUESTS)
			] = [$GUESTS,	'light',	$STAMP];
		}
		foreach ($fbvis as $desc => $info) {
			[$cnt,$class,$stamp] = $info;
			if (!$cnt) {
				continue;
			}
			layout_start_reverse_row();
			$linkclose = null;
			$linkopen =  null;
			echo $linkopen,$cnt,$linkclose;
			layout_value_field($linkopen.get_facebook_icon($class).$linkclose);
			echo $linkopen,$desc,$linkclose,' <span class="light">@ '._date_get($stamp).'</span>';
			layout_stop_row();
		}
	}

	if ($total_cnt
	&&	have_user()
	&&	($buddyids = _buddies_full_cached_list(CURRENTUSERID))
	&&	($buddyidstr = implode(',',$buddyids))
	&&	($buddies = memcached_simple_hash('going','
			SELECT MAYBE, USERID, MAYBE AS SECOND_MAYBE
			FROM going
			WHERE PARTYID='.$party['PARTYID'].'
			  AND USERID IN ('.$buddyidstr.')',
			TEN_MINUTES)
		)
	) {
		require_once '_bubble.inc';
		require_once '_heart.inc';
		$all_buddies = ($buddies[0] ?? []) + ($buddies[1] ?? []);
		$bbubble = new bubble(BUBBLE_CLEAN | BUBBLE_CURSOR);
		$bbubble->content();
		show_buddy_bubble($all_buddies);
		$bbubble->close();

		# going buddies
		if (isset($buddies[0])) {
			[$linkopen,$linkclose] = get_action_open_close('buddies');
			$linkopen = str_replace('<a ','<a rel="nofollow" ',$linkopen);
			$buddycnt = count($buddies[0]);
			layout_start_reverse_row();
				$bbubble->catcher($linkopen.$buddycnt.$linkclose);
				$bbubble->display_and_keep_once();
			layout_value_field(get_heart());
				$bbubble->catcher($linkopen.element_name('buddy',$buddycnt).$linkclose);
				$bbubble->display_catcher();
			layout_stop_row();
		}
		# douting budddies
		if (isset($buddies[1])) {
			[$linkopen, $linkclose] = get_action_open_close('buddies','maybebuddies');
			$linkopen = str_replace('<a ','<a rel="nofollow" ',$linkopen);
			$buddycnt = count($buddies[1]);
			layout_start_reverse_row();
				$bbubble->catcher($linkopen.$buddycnt.$linkclose);
				$bbubble->content();
				show_buddy_bubble($all_buddies);
				$bbubble->display_and_keep_once();
			layout_value_field(get_half_heart());
				$bbubble->catcher($linkopen.element_name('interested_buddy',$buddycnt).$linkclose);
				$bbubble->display_catcher();
			layout_stop_row();
		}
	}
	# visiting fans
	if (($artistids = $GLOBALS['currentuser']->is_artist())
		# FORCE KEY (PRIMARY) makes sure the biggest index is used.
		# Checking number of rows examined shows that this is the fastest way.
	&&	($fans = memcached_simple_hash(['going','user_account','favourite'], "
		SELECT 0 + MAYBE, COUNT(*)
		FROM going
		JOIN user_account FORCE KEY (USERID) USING (USERID)
		JOIN favourite FORCE KEY (`PRIMARY`)
		  ON favourite.USERID = going.USERID
		 AND ELEMENT = 'artist'
		 AND favourite.ID IN (".implode(',',$artistids).")
		WHERE STATUS = 'active'
		  AND PARTYID = $partyid
		GROUP BY MAYBE
		ORDER BY MAYBE"))
	) {
		foreach ($fans as $maybe => $count) {
			[$linkopen, $linkclose] = get_action_open_close(($maybe ? 'doubting' : '').'fans');
			$linkopen = str_replace('<a ','<a rel="nofollow" ', $linkopen);
			layout_start_reverse_row();
			echo $linkopen, $count, $linkclose;
			layout_value_field();
			echo $linkopen, element_name(($maybe ? 'interested_' : '').'fan', $count), $linkclose;
			layout_stop_row();
		}
	}

	$show_visitor_statistics = $certain_cnt > REQUIRED_VISITORS_FOR_STATISTICS;

	if ($party_admin
	||	$show_visitor_statistics
	) {
	// MALE / FEMALE
		layout_start_reverse_row(userdef: !$show_visitor_statistics ? 'light' : null);
		$mperc = $certain_cnt ? (int)(  $male_cnt * 100 / $certain_cnt) : 0;
		$fperc = $certain_cnt ? (int)($female_cnt * 100 / $certain_cnt) : 0;
		?><span class="nowrap"><?= $mperc ?>&nbsp;/ <?= $fperc ?></span><?
		layout_value_field();
		echo element_plural_name('man') ?> / <? echo element_plural_name('woman');

		if (empty($party['HIDE_AGE_STATS'])) {
			// AVG AGE
			if (false === ($ages = get_bgquery(BGQRY_AGES, 'party', $partyid))) {
				return;
			}
			if ($ages) {
				[,,,,,, $average_age] = $ages;
				[$linkopen, $linkclose, $link] = get_action_open_close('ages');
				$linkopen = str_replace('<a ', '<a rel="nofollow" ', $linkopen);
				layout_restart_reverse_row(userdef: !$show_visitor_statistics ? 'light' : null);
				printf('%.1f', $average_age);
				layout_value_field(get_partyflock_icon());
				echo str_replace('%LINKOPEN%', $linkopen, str_replace('%LINKCLOSE%','</a>',__('partystat:average_age', KEEP_EMPTY_KEYWORDS)));
				// MEDIAN AGE

				$median = get_bgquery(BGQRY_AGE_MEDIAN, 'party', $partyid);
				layout_restart_reverse_row(userdef: !$show_visitor_statistics ? 'light' : null);
				printf('%.1f', $median);
				layout_value_field(get_partyflock_icon());
				echo str_replace('%LINKOPEN%', $linkopen, str_replace('%LINKCLOSE%', '</a>', __('partystat:age_median', KEEP_EMPTY_KEYWORDS)));
			}
		}
		layout_stop_row();

		if (empty($party['HIDE_AGE_STATS'])
		&&	!empty($appic_event['AVERAGE_AGE'])
		) {
			layout_restart_reverse_row(userdef: !$show_visitor_statistics ? 'light' : null);
			printf('%.1f', $appic_event['AVERAGE_AGE'] / 100);
			layout_value_field(get_appic_icon());
			echo str_replace(['%LINKOPEN%','%LINKCLOSE%'],'',__('partystat:average_age', KEEP_EMPTY_KEYWORDS));

			layout_restart_reverse_row(userdef: !$show_visitor_statistics ? 'light' : null);
			printf('%.1f', $appic_event['MEDIAN_AGE'] / 100);
			layout_value_field(get_appic_icon());
			echo str_replace(['%LINKOPEN%','%LINKCLOSE%'],'',__('partystat:age_median', KEEP_EMPTY_KEYWORDS));
		}
	}

	show_views_row();

 	if (have_admin(['party', 'appic'])
	||	am_employee('organization', $allorgs)
	) {
		if (($ticketclicks = memcached_simple_hash('tickethit','
			SELECT IF(APPIC, "APPIC", "FLOCK"), COUNT(DISTINCT USERID, IDENTID, IPBIN)
			FROM tickethit
			WHERE PARTYID = '.$partyid.'
			GROUP BY APPIC'))
		&&	(	!empty($ticketclicks['APPIC'])
			||	!empty($ticketclicks['FLOCK']))
		) {
			foreach ([
				'FLOCK'	=> get_partyflock_icon(),
				'APPIC'	=> get_appic_icon(),
			] as $type => $icon) {
				if (empty($ticketclicks[$type])) {
					continue;
				}
				layout_start_reverse_row();
				echo $ticketclicks[$type];
				layout_value_field($icon);
				echo element_name('unique_ticket_click', $ticketclicks[$type]);
				?> <?
				echo get_ticket_icon('colorless');
				layout_stop_row();
			}
		}
	}

	# NEWS
	require_once '_news.inc';
	[$total_news /* ,$teaser_news */] = get_news_counts();
	if ($total_news) {
		layout_start_reverse_row();
		?><a href="#news"><?= $total_news ?></a><?
		layout_value_field();
		?><a href="#news"><?= element_name('news_message',$total_news) ?></a><?
		layout_stop_row();
	}

	# TOPICS
	require_once '_forum.inc';
	$topix = memcached_single_array(
		['connect','topic'], /** @lang MariaDB */ '
		SELECT COUNT(*),COUNT(IF(FORUMID IN ('.implode(',',get_sale_forums() ?: [0]).'),1,NULL))
		FROM connect
		JOIN topic ON TOPICID=ASSOCID
		WHERE MAINTYPE="party"
		  AND MAINID='.$party['PARTYID'].'
		  AND ASSOCTYPE="topic"
		  AND topic.STATUS="open"',
		TEN_MINUTES,
		$topic_key = 'connected_topics:v3:party:'.$party['PARTYID']
	);
	if (empty($topix[0])) {
		$topix = false;
		$show_sale = true;
	}

	if ($topix) {
		[$topix, $sale_topix] = $topix;

		$show_sale = !$sale_topix || CURRENTSTAMP < $party['STAMP'] + $party['DURATION_SECS'];

		$linkopen = '<a href="#topics">';
		$linkclose = '</a>';

		layout_start_reverse_row();
		echo $linkopen,(!$show_sale ? ($topix - $sale_topix).' / ' : null).$topix,$linkclose;
		layout_value_field();
		echo $linkopen,element_name('forumtopic',$topix),$linkclose;
		layout_stop_row();
	}

	# REPORTS
	if ($cnt = memcached_single_int('report','SELECT COUNT(*) FROM report WHERE PARTYID='.$party['PARTYID'],600)) {
		layout_start_reverse_row();
		[$linkopen,$linkclose] = get_action_open_close('reports');
		echo $linkopen,$cnt,$linkclose;
		layout_value_field();
		echo $linkopen,element_name('report',$cnt),$linkclose;
		layout_stop_row();
	}

	# PHOTOSHOOTS
	if (!empty($shoots)) {
		layout_start_reverse_row();
		foreach ($shoots as $shoot) {
			?><a href="<?= get_element_href('gallery',$shoot['GALLERYID']) ?>"><?
			if ($shoot['USERID']) {
				?><small class="light"><?= escape_specials(get_element_title('user', $shoot['USERID'])) ?>:</small> <?
			}
			echo $shoot['CNT'];
			?></a><br /><?
		}
		layout_value_field();
		if ($closer = count($shoots) === 1 ? '</a>' : '') {
			?><a href="<?= get_element_href('gallery',$shoot['GALLERYID']) ?>"><?
		}
		echo element_plural_name('photo');
		echo $closer;
		layout_stop_row();
	}

	# VIDEOS
	require_once '_videototal.inc';
	show_video_statistics_row(false);

	# CONTESTS
	if (!empty($contests)) {
		layout_start_reverse_row();
		$contest = reset($contests);
		while ($contest) {
			?><small class="light"><?= $contest['CNT'] ?> @</small> <?
			?><a href="<?= get_element_href('contest', $contest['CONTESTID']) ?>"><?=
				$contest['VISIBLE_GROUPS'], MULTIPLICATION_SIGN_ENTITY
			?> <?=
				$contest['AMOUNT']
			?></a><?
			$contest = next($contests);
			if ($contest) {
				?><br /><?
			}
		}
		layout_value_field();
		if (!isset($contests[1])) {
			?><a href="<?= get_element_href('contest',$contests[0]['CONTESTID']) ?>"><?= element_name('contest'); ?></a><?
		} else {
			echo element_plural_name('contest');
		}
		layout_stop_row();
	}

	# MEETINGS
	if (!empty($meetings)) {
		if ($meetinglistsize === 1) {
			$linkopen = '<a href="/meeting/'.$meetings[0]['MEETINGID'].'">';
			$linkclose = '</a>';
		} else {
			[$linkopen,$linkclose] = get_action_open_close('meetings');
		}
		layout_start_reverse_row();
		echo $linkopen,$meetinglistsize,$linkclose;
		layout_value_field();
		echo $linkopen,element_name('meeting',$meetinglistsize !== 1),$linkclose;
		layout_stop_row();
	}

	# ALBUMELEMENTS
	album_show_stats_row('party',$partyid,$userphotocnt,false);
	require_once '_commentlist.inc';
	_commentlist::show_stats_row(false);
	require_once '_ratinglist.inc';
	_ratinglist::show_stats_row(false);
	vote_show_row(false);

	# PROMOS
	if (have_admin('promo')) {
		$info = memcached_single_assoc(['promo', 'connect'],'
			SELECT COUNT(*) AS CNT, SUM(ASSOCID) AS PROMOID, MAX(TITLE) AS TITLE
			FROM connect
			JOIN promo ON PROMOID=ASSOCID
			WHERE MAINTYPE="party"
			  AND MAINID='.$party['PARTYID'].'
			  AND ASSOCTYPE="promo"',
			TEN_MINUTES
		);
		if ($info['CNT']) {
			if ($info['CNT'] > 1) {
				$linkstart = '<a href="/party/'.$party['PARTYID'].'/promos#promos">';
			} else {
				$linkstart = '<a href="'.get_element_href('promo',$info['PROMOID'],$info['TITLE']).'">';
			}
			layout_start_reverse_row();
			echo $linkstart,$info['CNT']; ?></a><?
			layout_value_field();
			echo $linkstart,element_name('promo',$info['CNT']); ?></a><?
			layout_stop_row();
		}
	}
	layout_close_table();
	$stats = ob_get_clean();

	if ($show_lineup_options) {
		?><div class="relative"><?

		?><div id="unreps"></div><?
		?><div id="lineup-form"></div><?
		?><div id="lineup-startup-error"></div><?

		?><div id="lineupbox"<?
		if ($party['CANCELLED']) {
			?> class="cancelled-box"<?
		}
		?>><?
	}
	layout_open_box('party');
	if ($show_lineup) {
		$timetable =	array_key_exists('TIMETABLE', $party)
					?	$party['TIMETABLE']
					:	(memcached_party_and_stamp($party['PARTYID'])['TIMETABLE'] ?? false);

		layout_box_header(
			'<h2>'.
				Eelement_name('line-up').
				($timetable ? ' &amp; '.Eelement_name('timetable') : '').
				' <span class="small light">'.escape_utf8($party['NAME']).' </span>'.
			'</h2>'
		);
		echo $lineupstr;
		layout_continue_box();
	}
	if ($show_lineup_options) {
		?><div id="stats"><?
	}
	layout_box_header(Eelement_plural_name('statistic'));
	echo $stats;
	if ($show_lineup_options) {
		?></div><?
	}
	layout_close_box();
	if ($show_lineup_options) {
		?></div><?
		?></div><?
	}
	require_once '_videoaftermovie.inc';
	$video_ids = get_aftermovie($party, $allorgs, 2 * ONE_YEAR);
	if ($video_id = get_aftermovie_from_party($party)) {
		$video_ids[] = $video_id;
	}
	if ($videos = memcached_rowuse_hash(['video', 'connect', 'videochannel'], "
		SELECT EXTERNALID, WIDTH, HEIGHT, CHANNELID, VIDEOID, PSTAMP, OLD, applicable_videos.TYPE
		FROM (
				SELECT DISTINCT VIDEOID,EXTERNALID, CHANNELID, CONTENTTYPE, TYPE, STATUS, PSTAMP, WIDTH, HEIGHT, 0 AS OLD
				FROM video
				JOIN connect
					 ON MAINTYPE = 'party'
					AND MAINID = $partyid
					AND ASSOCTYPE = 'video'
					AND ASSOCID = VIDEOID
				WHERE video.TYPE IN ('youtube', 'vimeo')
				  AND STATUS = 'active'
				  AND CONTENTTYPE IN ('aftermovie', 'videoreport', 'teaser', 'promo', 'trailer', 'liveregistration', 'endshow', 'showmovie')
				".(	$video_ids
				? "	UNION
					SELECT DISTINCT VIDEOID, EXTERNALID, CHANNELID, CONTENTTYPE, TYPE, STATUS, PSTAMP, WIDTH, HEIGHT, 1 AS OLD
					FROM video
					WHERE video.TYPE IN ('youtube', 'vimeo')
					  AND STATUS = 'active'
					  AND VIDEOID IN (".implode(', ', $video_ids).')'
				: '')."
		) AS applicable_videos
		LEFT JOIN videochannel USING (CHANNELID)
		WHERE (videochannel.DISABLED IS NULL OR NOT videochannel.DISABLED)
		ORDER BY
			OLD,
			CONTENTTYPE IN ('aftermovie', 'videoreport', 'liveregistration', 'showmovie', 'endmovie') DESC,
			CONTENTTYPE IN ('trailer', 'teaser', 'promo') DESC,
			PSTAMP DESC",
		TEN_MINUTES)
	) {
		$have_new = false;
		foreach ($videos as $video) {
			if (!$video['OLD']) {
				$have_new = true;
				break;
			}
		}
		$video = array_shift($videos);
		layout_open_box('party');
		layout_box_header('<h2>'.Eelement_name('video',count($videos) > 1).'</h2>',
			$have_new ? '<a href="/video/party/'.$partyid.'">'.element_name('overview_of_all_videos').'</a>' : null
		);

		# Reload to work around chrome bug:
		# https://issuetracker.google.com/issues/249707272
		# When bug is fixed, remove code below and remove enablejsapi=1 from iframe src.
		# Strange thing is, reload seems to not be triggerd. Maybe connecting the player to the
		# iframe this way, also is a workaround.

		$chrome_workaround = !HOME_THOMAS && CHROME && !SMALL_SCREEN;

		if ($src = match ($video['TYPE']) {
			'youtube'	=>	'https://www.youtube.com/embed/'.$video['EXTERNALID'].
							($videos ? '?playlist='.$video['EXTERNALID'].','.implodekeys(',', $videos).($chrome_workaround ? '&amp;enablejsapi=1' : '') : ''),
			'vimeo'		=>	'https://player.vimeo.com/video/'.$video['EXTERNALID'],
		}) {
			show_aspect_embed($src, id: $id = $chrome_workaround ? 'event-video' : '');
			if ($chrome_workaround) {
				show_chrome_video_workaround($id);
			}
			global $__shown_video;
			$__shown_video = $video;
		}
		layout_close_box();
	}
	show_videos('party', $partyid, $party);
	require_once '_news.inc';
	show_news_teasers(true);

	switch ($_REQUEST['ACTION']) {
	// (MAYBE) VISITORS
	case 'everdoubters':
		$ever = true;
		$tmpmaybe = true;
		require_once '_userlist.inc';
		$user_list = new _userlist();
		$user_list->going_to_party($partyid,isset($tmpmaybe),isset($ever));
		$user_list->non_permbanned_non_admin = true;
		$user_list->show_camera = true;
		$user_list->show_heart = true;
		if (!isset($tmpmaybe) && !isset($ever)) {
			$user_list->itemprop = 'attendee';
		}
		if (!$user_list->query()) {
			return;
		}
		if ($user_list->size) {
			layout_open_box('party',isset($tmpmaybe) ? (isset($ever) ? 'everdoubters' : 'maybevisitors') : 'visitors');
			layout_box_header(Eelement_plural_name(isset($tmpmaybe) ? (isset($ever) ? 'ever_interested' : 'interested') : 'visitor'));
			$user_list->display();
			layout_close_box();
		}
		break;

	case 'visitors':
		require_once '_userlist.inc';
		foreach ([0,1] as $tmpmaybe) {
			$user_list = new _userlist();
			$user_list->going_to_party($partyid,$tmpmaybe);
			$user_list->non_permbanned_non_admin = true;
			$user_list->show_camera = true;
			$user_list->show_heart = true;
			if (!isset($tmpmaybe) && !isset($ever)) {
				$user_list->itemprop = 'attendee';
			}
			if (!$user_list->query()) {
				return;
			}
			if ($user_list->size) {
				layout_open_box('party',$tmpmaybe ? 'maybevisitors' : 'visitors');
				layout_box_header(Eelement_plural_name($tmpmaybe ? 'interested' : 'visitor'));
				$user_list->display();
				layout_close_box();
			}
		}
		break;

	case 'doubtingfans':
		$doubting = true;
	case 'fans':
		if (!$artistids) {
			http_response_code(404);
			return;
		}
		require_once '_userlist.inc';
		$user_list = new _userlist();
		$user_list->fan_going_to_party($partyid,$artistids,isset($doubting));
		$user_list->non_perm_banned_non_admin = true;
		$user_list->show_camera = true;
		$user_list->show_heart = true;
		if (!$user_list->query()) {
			return;
		}
		if ($user_list->size) {
			layout_open_box('party',(isset($doubting) ? 'doubting' : null).'fans');
			layout_box_header(Eelement_plural_name((isset($doubting) ? 'interested_' : null).'fan'));
			$user_list->display();
			layout_close_box();
		}
		break;

	case 'buddies':
		require_once '_userlist.inc';
		?><div id="buddies"><?

		$user_list = new _userlist();
		$user_list->going_to_party($partyid);
		$user_list->only_full_buddies();
		$user_list->show_heart = false;
		$user_list->show_camera = true;
		if (!$user_list->query()) {
			return;
		}
		if ($user_list->size) {
			layout_open_box('party');
			layout_box_header(Eelement_plural_name('buddy'));
			$user_list->display();
			layout_close_box();
		}

		$user_list = new _userlist();
		$user_list->maybe_going_to_party($partyid);
		$user_list->only_full_buddies();
		$user_list->non_permbanned_non_admin = true;
		$user_list->show_camera = true;
		$user_list->show_heart = true;
		if (!$user_list->query()) {
			return;
		}
		if ($user_list->size) {
			layout_open_box('party');
			layout_box_header(Eelement_plural_name('interested_buddy'));
			$user_list->display();
			layout_close_box();
		}
		?></div><?
		break;

	case 'reports':
		require_once '_reports.inc';
		layout_open_box('party','reports');
		layout_box_header(Eelement_plural_name('report'));
		$reportlist = new _reportlist;
		$reportlist->of_party($party['PARTYID']);
		$reportlist->show_all = true;
		$reportlist->query();
		$reportlist->display();
		layout_close_box();
		break;

	case 'age':
		if (!$_REQUEST['subID']) {
			http_response_code(404);
		}
	case 'ages':
		layout_open_box('party','ages');
		layout_box_header(Eelement_name('age_distribution'));
		if (!isset($ages)) {
			$ages = get_bgquery(BGQRY_AGES,'party',$partyid);
			$median = get_bgquery(BGQRY_AGE_MEDIAN,'party',$partyid);
		}
		if ($ages) {
			[$elderly_age, $actual_ages,,, $total,, $average_age] = $ages;
			$ages = $actual_ages;
		}
		if (!$ages) {
			?><div class="block"><?= __('party:info:no_age_distribution_available_LINE') ?></div><?
			layout_close_box();
			break;
		}
		if ($average_age) {
			?><div class="block"><?= Eelement_name('average_age') ?>: <?= round($average_age,1) ?>, <?
			if ($median) {
				echo element_name('median_age') ?>: <? echo round($median,1);
			}
			?><br /><?
		}
		?><small class="light"><?= __('party:info:below_1%_and_elderly_ignored_for_age_averages_LINE',['ELDERLY_AGE' => $elderly_age]) ?></small><?
		?></div><?
		layout_open_table('bordered fw hpadded hha');
		layout_start_header_row();
		layout_header_cell_right(Eelement_name('age'));
		layout_header_cell_right(__C('field:absolute'));
		layout_header_cell_right(Eelement_name('percentage'));
		layout_header_cell();
		layout_stop_header_row();
		ksort($ages,SORT_NUMERIC);
		$show_age = $_REQUEST['ACTION'] === 'age' ? $_REQUEST['subID'] : false;
		foreach ($ages as $age => $cnt) {
			$perc = (int)(100*$cnt/$total);
			if (!$show_age
			||	 $show_age !== $age
			) {
				$linkopen = "<a rel=\"nofollow\" onclick=\"return false;\" href=\"/party/{$party['PARTYID']}/age/$age#age\">";
				$linkclose = '</a>';
			} else {
				$linkopen = '<b>';
				$linkclose = '</b>';
			}
			// AGE
			layout_set_onclick("openLink(event, '/party/{$party['PARTYID']}/age/$age#age');");
			layout_start_rrow_right(userdef: 'ptr'.($perc < 1 || $age >= $elderly_age ? ' light' : ''));
			echo $linkopen,$age,$linkclose;
			// ABSOLUTE
			layout_next_cell(class: 'right');
			echo $linkopen, $cnt, $linkclose;
			// PERCENTAGE
			layout_next_cell(class: 'right');
			if ($perc) {
				echo $linkopen, $perc, '%', $linkclose;
			}
			?></td><td style="padding: 1px 0; width: 90%;"><?
			if ($perc) {
				?><div class="age-bar" style="height: 100%; width: <?= $perc ?>%;">&nbsp;</div><?
			}
			layout_stop_row();
		}
		layout_close_table();
		layout_close_box();
		if (!$show_age) {
			break;
		}
		require_once '_userlist.inc';
		$user_list = new _userlist();
		$user_list->going_to_party($party['PARTYID']);
		$user_list->of_age_going($show_age);
		$user_list->show_camera = true;
		$user_list->show_heart = true;
		if (!$user_list->query()) {
			return;
		}
		if ($user_list->size) {
			layout_open_box('party','age');
			layout_box_header(__C('elements:visitor_of_age',['AGE'=>$show_age]));
			$user_list->display();
			layout_close_box();
		}
		break;

	case 'meetings':
		if (!($meetings = memcached_rowuse_array(
			'meetings','
			SELECT meeting.MEETINGID, HOUR, MINS, DESCR,meeting.USERID,COUNT(*) AS CNT
			FROM meeting
			JOIN meet USING (MEETINGID)
			WHERE PARTYID='.$party['PARTYID'].'
			GROUP BY meeting.MEETINGID
			ORDER BY HOUR, MINS',
			TEN_MINUTES
		))) {
			break;
		}
		sort_meetings($meetings,$party['STAMP']);
		layout_open_box('party','meetings');
		foreach ($meetings as $meeting) {
			layout_open_box_header();
			?><a href="/meeting/<?= $meeting['MEETINGID'];
			?>"><?= $meeting['HOUR'] ?>:<?= $meeting['MINS']
			?><small> &raquo; <?= $meeting['CNT']
			?></small></a><?
			layout_close_box_header();
			?><div class="block"><?= make_all_html($meeting['DESCR'], UBB_UTF8);
			?></div><?
			?><div class="small light" style="margin-bottom: 1em;"><?= $meeting['CNT']
			?> <?
			echo element_name('attendee',$meeting['CNT']);
			if ($meeting['USERID']) {
				?>, <?= element_name('organizer') ?>: <? echo get_element_link('user',$meeting['USERID']);
			}
			?></div><?
		}
		layout_close_box();
		break;

 	case 'promos':
		if (!have_admin('promo')) {
			break;
		}
		$promos = db_rowuse_array(
			['promo','connect','relation'],'
			SELECT PROMOID,TITLE,STARTSTAMP,STOPSTAMP,relation.RELATIONID,ACTIVE,NAME,INVOICENR,SENT_STAMP,promo.REMOVED,VERSION,BUDGET,TARGETPRICE
			FROM connect
			JOIN promo ON ASSOCID=PROMOID
			LEFT JOIN relation USING (RELATIONID)
			WHERE MAINTYPE="party"
			  AND MAINID='.$party['PARTYID'].'
			  AND ASSOCTYPE="promo"'
		);
		if (!$promos) {
			break;
		}
		number_sort($promos,'STARTSTAMP');
		layout_open_box('party', 'promos');
		layout_box_header(Eelement_plural_name('promo'));
		layout_open_table(TABLE_FULL_WIDTH);
		require_once '_promolist.inc';
		_promolist_display_rows($promos);
		layout_close_table();
		layout_close_box();

		break;

	default:
		$showcmts = true;
		break;
	}

	if ($party_desc) {
		[$teaser, $desc, $same, $fbid, $fbtitle] = $party_desc;

		$url = $fb_presence_jump ?? ($fbid ? 'https://www.facebook.com/events/'.$fbid : null);

		layout_open_box('party body light8');
		layout_box_header(
			($url ? '<a target="_blank" href="'.$url.'">'.get_facebook_icon().' ' : '').($fbtitle ? escape_utf8($fbtitle) : Eelement_name('information')).($url ? '</a>' : '')
		);

		$flags = UBB_UTF8 | UBB_NEW_TAB;

		?><div class="block forcewrap"><?=
			make_all_html(_ubb_preprocess($teaser, utf8: true), $flags, 'party', $partyid)
		?></div><?
		if (!$same) {
			?><div class="block"><?
			?><span<?
			?> class="unhideanchor"<?
			?> onclick="<?
				?>unhide(this.parentNode.nextSibling);<?
				?>hide(this,this.parentNode.previousSibling);<?
			?>"<?
			?>><i>&rarr; <?= __('action:read_more') ?></i></span><?
			?></div><?
			?><div class="hidden block forcewrap"><?=
				make_all_html(_ubb_preprocess($desc, utf8: true), $flags)
			?></div><?
		}
		layout_close_box();
	}
	if (($appic_admin || $party_admin)
	&&	($appic_event_info = $party['MAIN_ID'] ? db_single('appic_event_info','
			SELECT BODY
			FROM appic_event_info
			WHERE MAINID='.$party['MAIN_ID']
		) : null)
	) {
		require_once '_bio.inc';
		layout_open_box('party light8');
		layout_box_header(get_appic_icon().' '.Eelement_name('information'));

		$appic_event_info = clean_utf8($appic_event_info);

		$teaser = get_teaser($appic_event_info,$same);

		?><div class="block"><?=
			make_all_html(_ubb_preprocess($teaser,true),UBB_UTF8,'party',$partyid)
		?></div><?
		if (!$same) {
			?><div class="block"><span class="unhideanchor" onclick="hide(this,this.parentNode.previousSibling); unhide(this.parentNode.nextSibling); this.parentNode.nextSibling.scrollIntoView()"><i>&rarr; <?= __('action:read_more') ?></i></span></div><?
			?><div class="hidden block"><?=
				make_all_html(_ubb_preprocess($appic_event_info,true),UBB_UTF8)
			?></div><?
		}
		layout_close_box();
	}

	if (isset($showcmts)
	&&	(	$topix
		||	$party['STAMP'] > CURRENTSTAMP
		&&	have_user()
		&&	memcached_single_int('topic','SELECT 1 FROM postedinv3 WHERE ELEMENT="topic" AND USERID='.CURRENTUSERID.' LIMIT 1',ONE_DAY)
		)
	) {
		layout_open_box('party','topics');
		layout_box_header(Eelement_name('forum'));

		if (!$show_sale) {
			include_js('js/tpxlst');
		}

		$target = 'party_topic_'.$party['PARTYID'];

		if (have_user() || !$show_sale) {
			layout_open_menu();
			if (have_user()) {
				layout_menuitem(Eelement_name('new_topic'),'/topic/form?PARTYID='.$party['PARTYID'],' target="'.$target.'"');
			}
			if (!$show_sale) {
				layout_open_menuitem();
				?><span onclick="Pf.toggleSaleTopics(this)" class="unhideanchor" data-other="<?= __('action:hide_sale_topics') ?>"><?=
					__C('action:show_sale_topics')
				?></span><?
				layout_close_menuitem();
			}
			layout_close_menu();
		}

		if ($topix) {
			require_once '_topiclist.inc';
			$topiclist = new _topiclist();
			$topiclist->show_heart = true;
			$topiclist->show_header = true;
			$topiclist->show_short = true;
			$topiclist->no_limit = true;
			$topiclist->use_key = "{$topic_key}_list";
			$topiclist->connected_to_party($party['PARTYID']);
			if (!$show_sale) {
				$topiclist->hide_sales();
			}
			if ($topiclist->query()) {
				if (!$show_sale) {
					?><div id="tpxlst" class="tmphidden"><?
				}
				$topiclist->display();
				if (!$show_sale) {
					?></div><?
				}
			}
		}
		layout_close_box();
	}
	if (isset($showcmts)
	&&	(	$party['ACCEPTED']
		||	!strncmp($_REQUEST['ACTION'],'comment',7))
	) {
		require_once '_commentlist.inc';
		$cmts = new _commentlist();
		$cmts->item($party);
		$cmts->display();
	}
	?></article><?
}
	close_status_overlay();

}

function party_display_presale_info(array $party): ?string {
	$partyid = $party['PARTYID'];
	require_once '_presale.inc';
	if (!($presaleinfo = get_presale_info('party', $partyid))) {
		require_once '_presale_affiliates.inc';
		if (!$party['PREREG_URL']
		&&	!get_presale_affiliate($partyid)
		) {
			return null;
		}
		$presaleinfo = ['PARTYID' => $partyid];
	}
	# FIXME: if presaleinfo is set => show block (delete presaleinfo when no info inside)
	require_once '_presale.inc';
	$locals = prepare_presale_info($presaleinfo, $party);
	/** @noinspection PhpRedundantOptionalArgumentInspection */
	extract($locals, \EXTR_OVERWRITE);
	$buy_button = null;
	$partner = $party['appic_event']['PARTNER'] ?? null;
	if ((/* $future = */ CURRENTSTAMP < $party['STAMP'] + ($party['DURATION_SECS'] ?: 6 * ONE_HOUR))
	||	have_admin(['appic', 'party', 'helpdesk'])
	) {
		[$href, $onclick] = get_generic_presale_link($partyid, $party);

		require_once '_customer.inc';
		$is_customer_event ??= is_customer_event($partyid, $partner);

		ob_start();
		require_once '_buybutton.inc';
		show_buy_buttons([
			'force_customer'=> $is_customer_event,

			'class'			=> 'block',

			'href'			=> $href,
			'onclick'		=> $onclick,
			'partyid'		=> $partyid,
			'party'			=> $party,
			'partner'		=> $partner,

			'presaleinfo'	=> $presaleinfo,
			'icons'			=> $icons,
			'main_link'		=> $main_link,
			'main_info'		=> $main_info,
			'websites'		=> $websites,
		]);
		$buy_button = ob_get_clean();

		foreach ([
			'early_access_presale'	=> 'EARLY_ACCESS_PRESALE_STAMP',
			'presale'				=> 'PRESALE_STAMP',
		] as $presale_name => $stamp_name) {
			if ($party[$stamp_name]
			&&	(	CURRENTSTAMP < $party[$stamp_name]
				||	(	!$buy_button
					||	have_admin(['party', 'appic'])
					)
				)
			) {
				?><div class="<?= CURRENTSTAMP > $party[$stamp_name] && $buy_button ? 'light ' : '' ?>block"><?
				$day_offset = to_days($party[$stamp_name]) - CURRENTDAYNUM;
				if ($day_offset < -1 || $party['SOLD_OUT']) {
					if ($day_offset < -1) {
						$key = ':info:started_LINE';
					} elseif ($day_offset < 0) {
						$key = ':info:started_yesterday(soldout)_LINE';
					} elseif ($day_offset === 0) {
						$key = ':info:started_today(soldout)_LINE';
					} else {
						$key = ':info:will_not_necessary_anymore_LINE';
					}
				} elseif ($day_offset < 0) {
					$key = ':info:started_yesterday_LINE';
				} elseif ($day_offset === 0) {
					if ($party['PRESALE_STAMP'] > CURRENTSTAMP) {
						$key = ':info:starts_today_LINE';
					} else {
						$key = ':info:started_today_LINE';
					}
				} elseif ($day_offset === 1) {
					$key = ':info:starts_tomorrow_LINE';
				} else {
					$key = ':info:starts_LINE';
				}
				echo __($presale_name.$key, [
					'DATE'	=> _dateday_get($party[$stamp_name]),
					'TIME'	=> $party["{$stamp_name}_NOTIME"] ? '' : _time_get($party[$stamp_name])
				]);
				?></div><?
			}
		}
		# require_once '_discount_tickets.inc';
		# show_discount_ticket_info($party, false);

		if (!SMALL_SCREEN
		||	!$is_customer_event
		) {
			# desktop OR non-customer
			echo $buy_button;
			$buy_button = null;
		}
		if (!empty($trailer)) {
			?><div class="block tmrgn"><?=
				__C('presale:also_available')
				?> <?=
				implode(', ',$trailer)
			?></div><?
		}
	}
	if (# No regular ticket button:
		!$mains
		# There is a preregistration URL:
	&&	$party['PREREG_URL']
		# Event is in the future, at least one week, but event that is quite short, preregistration probably ends way earlier:
	&&	CURRENTSTAMP < $party['STAMP'] - ONE_WEEK
	) {
		require_once '_jumper.inc';
		require_once '_customer.inc';
		?><div class="event-actions block<?
		/** @noinspection PhpUnusedLocalVariableInspection */
		if (!($is_customer_event ??= is_customer_event($partyid, $partner))) {
			?> noncust<?
		}
		?>"><?

		?><a rel="nofollow" target="_blank" href="<?= jump_to('prereg',$partyid) ?>"><?
			?><div class="action-button generic-button buy"><?=
				__C($party['FREE_ENTRANCE'] ? 'action:register' : 'action:preregister')
			?></div><?
		?></a><?

		?></div><?

		if (have_admin(['party', 'appic'])) {
			$href = escape_specials($party['PREREG_URL']);
			?><div class="notice block">SITE: <?
			?><a class="ns light" href="<?= $href ?>" target="_blank"><?= $href ?></a><?
			?></div><?
		}
	}
	if (!empty($presaleinfo['ADDR'])) {
		?><div id="presale-addresses" class="hidden block"><?
		?><div><b><?= Eelement_plural_name('presale_address') ?></b></div><?
		?><ul><?
		$address_groups = explode("\n\n", $presaleinfo['ADDR']);
		if (count($address_groups) > 1) {
			foreach ($address_groups as $address_group) {
				?><li><?= nl2br(escape_utf8($address_group)) ?></li><?
			}
		} else {
			foreach (explode("\n", $presaleinfo['ADDR']) as $address) {
				if ($address = utf8_mytrim($address)) {
					?><li><?= escape_utf8($address) ?></li><?
				}
			}
		}
		?></ul></div><?
	}
	# FIXME: remove:
	#		travel:info:org_bus_trips_multiple_departures_LINE
	#		travel:info:list_organizes_bus_trips_multiple_departures_LINE
	return $buy_button;
}

function party_commit(): bool {
	set_memory_limit(GIGABYTE);

	$orig_post = $_POST;

	if ($partyid = actual_party_commit()) {
		#store_messages_in_cookie();
		#see_other(get_element_href('party', $partyid));
		# exit;
		# FIXME solve caching problem
		return true;
	}

	if (ROBOT) {
		return false;
	}

	if (!empty($GLOBALS['__error'][0])
	&&	(	!empty($_POST['URL'][0])
		&&	preg_match('"^(?:https?://)?[^/]+/?$"',$_POST['URL'][0])

		||	!isset($_POST['LINEUP_PDAY'])
		)
	) {
		return false;
	}

	if (have_admin()) {
		# Exceptional error

		if (!empty($GLOBALS['__error'][0])
		&&	0 === strncmp($GLOBALS['__error'][0], 'Recentelijk behandeld door', 26)
		) {
			mail_log(
				"waited too long, check logs for\n\n".
				'LOCK_TYPE = '.LOCK_PARTY."\n".
				'LOCK_ID = '.$_REQUEST['sID']
			);
		} else {
			mail_log('POST to party form failed', item: [
				'__error'		=> $GLOBALS['__error'] ?? null,
				'__warning'		=> $GLOBALS['__warning'] ?? null,
				'_SERVER'		=> $_SERVER,
				'_POST'			=> $_POST,
				'_REQUEST'		=> $_REQUEST,
				'orig_post'		=> $orig_post,
				'super_orig_post'	=> $GLOBALS['super_orig_post'] ?? null,
			]);
		}
	}
	return false;
}

function actual_party_commit(): int|false {
	require_once '_pagechanged.inc';
	require_once '_partyprices.inc';
	require_once '_delayedflyers.inc';
	require_once '_ubb_preprocess.inc';
	require_once '_presence.inc';
	require_once '_facebook_description_parse.inc';
	require_once '_find_ticket_uri.inc';

	# FIXME: Remove this forced nocache of everything once 'dirty' tables registration works
	$_REQUEST['NOMEMCACHE'] = true;

	$partyid = $_REQUEST['sID'];

/*	convert_array_to_win1252($_POST, keep_utf8: [
		'APPICDESC',
		'BOARDING_ADDR',
		'BODY',
		'DRESSCODE',
		'FBTITLE',
		'FBDESCRIPTION',
		'LOCATION_ADDR',
		'LIVESTREAM_ADDRES',
		'NAME',
		'NAME_FOR_APPIC',
		'ORGANIZATION_NAME',
		'PREREG_URL',
		'RENAMES',
		'SITE',
		'SUBTITLE',
		'VALICMT',
		'CANCEL_REASON',
		'PRESALE_ADDR',
		'PRESALE_WEBSITE',
	]);*/
	if (!require_post()
	||	!require_something_trim($_POST, 'NAME', utf8: true)
	||	isset($_POST['NAME_FOR_APPIC'])
	&&	!require_anything_trim($_POST, 'NAME_FOR_APPIC', true)
	||	!optional_number($_POST, 'EDITION')
	||	!require_date_and_time($_POST, max_hour: 24)
	||	!require_number($_POST, 'HOUR')
	||	!empty($_POST['LINEUPCB'])
	&&	!require_date($_POST, 'LINEUP_P')
	||	!empty($_POST['TIMETABLECB'])
	&&	!require_date_and_time($_POST, 'TIMETABLE_P')
	||	!empty($_POST['FLYERCB'])
	&&	!require_date_and_time($_POST, 'FLYER_P')
	||	!empty($_POST['EVENTCB'])
	&&	!require_date_and_time($_POST, 'EVENT_P')
	||	!require_anything_trim($_POST, 'SUBTITLE', true)
	||	!require_anything_trim($_POST, 'DRESSCODE', true)
	||	!require_anything_trim($_POST, 'CANCEL_REASON', true)
	||	!optional_number($_POST, 'CITYID')
	||	!optional_number_array($_POST, 'ORGANIZATIONID', utf8: true)
	||	!optional_hash($_POST, 'ORGORGANIZATIONID', HASH_NUMBER, HASH_NUMBER)
	||	!optional_number_array($_POST, 'ORGASHOSTID', allow_empty: true, utf8: true)
	||	!optional_hash($_POST, 'ORGORGASHOSTID', HASH_NUMBER, HASH_NUMBER)
	||	!optional_number($_POST, 'TICKETID')
	||	!require_anything_trim($_POST, 'VALICMT', true)
	||	!require_number_or_empty($_POST, 'VISITORCNT')
	||	!optional_number($_POST, 'LOCATIONID')
	||	!require_anything_trim($_POST, 'LOCATION_ADDR', true)
	||	!optional_number($_POST, 'BOARDINGID')
	||	!optional_number($_POST, 'DEPARTURE_HOUR')
	||	!optional_number($_POST, 'DEPARTURE_MINS')
	||	!optional_number($_POST, 'ARRIVAL_HOUR')
	||	!optional_number($_POST, 'ARRIVAL_MINS')
	||	!optional_number($_POST, 'DOOR_CLOSE_HOUR')
	||	!optional_number($_POST, 'DOOR_CLOSE_MINS')
	||	!require_anything_trim($_POST, 'BOARDING_ADDR', true)
	||	!require_number_or_empty($_POST, 'MIN_AGE')
	||	!require_number_or_empty($_POST, 'MIN_AGE_FEMALE')
	||	!require_number_or_empty($_POST, 'USE_MAX_AGE')
	||	$_POST['USE_MAX_AGE']
	&&	!require_number_or_empty($_POST, 'MAX_AGE')
	||  !require_anything_trim($_POST, 'SITE', utf8: true, want_ascii: true)
	||	!require_anything_trim($_POST, 'PRESALE_WEBSITE', utf8: true, want_ascii: true)
	||	!require_anything_trim($_POST, 'PRESALE_ADDR', utf8: true)
	||	false === require_facebook_text($_POST, 'FBTITLE')
	||	false === require_facebook_text($_POST, 'FBDESCRIPTION')
	||	!optional_number_array($_POST, 'FLAGS', utf8: true)
	||	false === require_element($_POST, 'LIVESTREAM',['','only','+event'])
	||	!empty($_POST['LIVESTREAM_ADDRESS'])
	&&	(	!$partyid
			# FIXME: stamp_tzi compared to 'regular' CURRENTSTAMP, may differ 1 or 2 hours
		||	($stamp_tzi = _dateonly_getstamp($_POST, utc: true))
		&&	CURRENTSTAMP < $stamp_tzi
		?	!require_working_url_or_none($_POST, 'LIVESTREAM_ADDRESS', utf8: true)
		:	!require_url_or_none($_POST, 'LIVESTREAM_ADDRESS', true)
		)
	||	!empty($_POST['CATEGORY'])
	&&	!require_element($_POST, 'CATEGORY', ['festival', 'concert', 'afterparty', 'preparty'], strict:true)
	||	!optional_number_array($_POST, 'GID', utf8: true)
	||	false === require_url_or_none($_POST, 'PREREG_URL', true)
	||	!verify_party_prices()
	) {
		return false;
	}
	if (($party_admin = have_admin('party'))
		# only admins may change these fields
	&&	!require_number_or_empty($_POST, 'AFTERMOVIE')
	) {
		return false;
	}

	move_site_to_presence();

	if ($_POST['SUBTITLE']) {
		if (preg_match('"^(\bafter$|after\s*(?:hour|party))$"iu', $_POST['SUBTITLE'], $match)) {
			# move 'after' from subtitle to title
			$_POST['NAME'] .= ' '.$_POST['SUBTITLE'];
			$_POST['SUBTITLE'] = '';
		}
		if (preg_match('!^[\'"](.*)[\'"]$!u', $_POST['SUBTITLE'], $match)) {
			$_POST['SUBTITLE'] = $match[1];
		}
		if (false !== ($subtitle_start = mb_stripos($_POST['NAME'], $_POST['SUBTITLE']))
		&&	$subtitle_start > 1
		&&	$_POST['NAME'][$subtitle_start-1] === ' '
		) {
			# strip subtitle from title
			$_POST['NAME'] = mytrim_title(str_ireplace($_POST['SUBTITLE'], '', $_POST['NAME']), utf8: true);
		}
		if (preg_match('"^([\'\"])(.*?)\$1$"u', $_POST['SUBTITLE'], $match)) {
			$_POST['SUBTITLE'] = $match[2];
		}
	}

	if (!have_user()) {
		if ($partyid) {
			if (!($cookie_token = require_token_number($_COOKIE,'FLOCK_TOKEN_'.LOCK_PARTY.'_'.$partyid))
			||	!($need_token = db_single('element_token','SELECT TOKEN FROM element_token WHERE ELEMENT="party" AND ID='.$partyid,DB_FORCE_MASTER))
			||	$need_token !== $cookie_token
			) {
				register_error('token(element):error:invalid_LINE',DO_UBB,array('ELEMENT'=>'party','ID'=>$partyid));
				return false;
			}
		} elseif (!($token = require_token_number($_POST, 'TOKEN'))) {
			return false;
		}
	}

	if (isset($_POST['TICKETID'])) {
		$setlist[] = 'TICKETID = '.$_POST['TICKETID'];
	}

	require_once '_connect.inc';

	$all_orgs = getifset($_POST, 'ORGANIZATIONID') ?: [];

	foreach ($all_orgs as $ndx => $organizationid) {
		if (!is_number($organizationid)) {
			unset($all_orgs[$ndx]);
			continue;
		}
		if (false === ($extra_orgs = connect_family(
				'party',
				0,
				'organization',
				$organizationid,
				get: true
		))) {
			return false;
		}
		if ($extra_orgs && is_array($extra_orgs)) {
			$all_orgs = [...$all_orgs, ...$extra_orgs];
		}
	}

	if (preg_match('"\b(open air|outdoor|beach)$"iu', $_POST['NAME'], $match)
	&&	(	!is_array($_POST['LOCATIONTYPE'])
		||	(	!in_array('outdoor', $_POST['LOCATIONTYPE'], true)
			&&	!in_array('beach', $_POST['LOCATIONTYPE'], true)
			)
		)
	) {
		warning('LET OP: '.$match[1].' in de titel, maar evenement is binnen?');
	}

	$store_type =
		isset($_POST['LOCATIONTYPE'])
	&&	is_array($_POST['LOCATIONTYPE'])
	?	addslashes(implode(',',$_POST['LOCATIONTYPE']))
	:	null;

	$locationid = have_idnumber($_POST, 'LOCATIONID');
	$boardingid = have_idnumber($_POST, 'BOARDINGID');

	$no_boat = $store_type && !in_array('boat', $_POST['LOCATIONTYPE'], true);

	if (false === (
			$location =
			$locationid
		?	db_single_assoc(['location', 'city'],'
			SELECT LOCATIONTYPE, TIMEZONE, SITE, CITYID, location.NAME, location.TITLE, location.ORDERNAME
			FROM location
			LEFT JOIN city USING (CITYID)
			WHERE LOCATIONID = '.$locationid,
			DB_FORCE_MASTER)
		:	null)
	||	false === (
			$boarding =
			$boardingid
		?	db_single_assoc(['boarding', 'city'], '
			SELECT TIMEZONE, boarding.NAME
			FROM boarding
			LEFT JOIN city USING (CITYID)
			WHERE BOARDINGID = '.$boardingid,
			DB_FORCE_MASTER)
		:	null)
	) {
		return false;
	}

	require_once '_namefix.inc';
	$_POST['NAME'] = get_fixed_name($_POST['NAME'], true);

	[$name, $subtitle, $edition] = utf8_parse_event_title($_POST['NAME'],[
		'partyid'			=> $partyid,
		'locationid'		=> $locationid,
		'location'			=> 'LOCATION_ADDR',
		'organizationids'	=> array_flip($all_orgs),
	]);

	$_POST['EDITION'] ??= $edition;

	if ($_POST['SUBTITLE']) {
		# ignore parsed version
		$subtitle = $_POST['SUBTITLE'];
	}

	if ($subtitle
	&&	!empty($location)
	&&	($cmp_subtitle = preg_replace('"\W+"iu', '', $subtitle))
	) {
		foreach (['TITLE', 'NAME', 'ORDERNAME'] as $key) {
			if (!($cmp_name = preg_replace('"\W+"iu', '', $location[$key]))) {
				continue;
			}
			if ($cmp_name === $cmp_subtitle) {
				$subtitle = '';
				break;
			}
		}
	}

	$subtitle = get_fixed_name($subtitle, true);

	if (!empty($_POST['SITE'])
	||	!empty($_POST['PRESALE_WEBSITE'])
	||	!empty($_POST['PRESENCE'])
	) {
		$skip_urls = [];
		if (!empty($location['SITE'])) {
			$skip_urls[base_url($location['SITE'])] = true;
		}
		if (!empty($all_orgs)) {
			$organizationid_list = addslashes(implode(',', $all_orgs));
			if (false === ($urls = db_simpler_array(['organization', 'presence'], "
				SELECT SITE
				FROM organization
				WHERE SITE != ''
				  AND ORGANIZATIONID IN ($organizationid_list)
				UNION
				SELECT SITE
				FROM presence
				WHERE ELEMENT = 'organization' AND ID IN ($organizationid_list)
				   OR ELEMENT = 'location'	   AND ID = $locationid",
				DB_FORCE_MASTER
			))) {
				return false;
			}
			foreach ($urls as $url) {
				$skip_urls[base_url($url)] = true;
			}
		}
	}
	if (empty($all_orgs)
	&&	($organization_name = $_POST['ORGANIZATION_NAME'])
	&&	($organization_names = explode(',',$organization_name))
	) {
		foreach ($organization_names as $organization_name) {
			if (!($organization_name = utf8_mytrim($organization_name))) {
				continue;
			}
			if (!($organizationid = db_single('organization','
				SELECT ORGANIZATIONID
				FROM organization
				WHERE NAME = "'.addslashes($organization_name).'"',
				DB_FORCE_MASTER
			))) {
				register_warning(
					'organization:warning:name_not_recognized_LINE',
					['NAME' => $organization_name]
				);
			} elseif (
				empty($all_orgs)
			||	!in_array($organizationid, $all_orgs, true)
			) {
				$all_orgs[] = $organizationid;
			}
		}
	}

	// ------ presale

	$nocreate_presaleinfo =
		!isset($_POST['PRESALE_DATE_C'])
	&&	!$_POST['PRESALE_WEBSITE']
	&&	empty($_POST['PRESALE_EMAIL'])
	&&	!$_POST['PRESALE_ADDR']
	&&	!isset($_POST['PRESALE_LOCATION']);

	# FIXME: Test new stamp conversion stuff
	$test = false;

	$timezonelement = $boarding ?: $location ?: true;

	# FIXME: timezone for livestream (no location) is currently always Europe/Amsterdam. Should be configurable
	$use_tz = (
			$timezonelement === true
		?	(($cityid = have_idnumber($_REQUEST,'CITYID')) ? memcached_timezone_city($cityid) : null)
		:	($timezonelement['TIMEZONE'] ?: null))
		?:	'Europe/Amsterdam';

	# Make a timezone independent STAMP entry.
	# FIXME: An extra conversion to timezonde independent stamp and then to a proper
	#		 timestamp can be done more effectively by just pa

	if ($test) {
		$tz = date_default_timezone_get();
		date_default_timezone_set($use_tz);
	}

	foreach ([
		'event',
		'flyer',
		'lineup',
		'timetable',
	] as $stamp_type) {
		$STAMP_TYPE = strtoupper($stamp_type);
		${$stamp_type.'_pstamp'} =
			isset($_POST[$STAMP_TYPE.'CB'])
		?	_date_getstamp($_POST, $STAMP_TYPE.'_P', utc: !$test)
		:	0;
	}

	$presale_stamp = isset($_POST['PRESALE_DATE_C']) ? _date_getstamp($_POST, 'PRESALE_',true) : 0;

	if ($test) {
		date_default_timezone_set($tz);
	}

	$at2400 = ($_POST['HOUR'] === 24);

	# Make a timezone independent STAMP entry.
	# FIXME: An extra conversion to timezonde independent stamp and then to a proper
	#		 timestamp can be done more effectively by just pa

	if ($test) {
		$tz = date_default_timezone_get();
		date_default_timezone_set($use_tz);
	}

	foreach ([
		'event',
		'flyer',
		'lineup',
		'timetable',
	] as $stamp_type) {
		$STAMP_TYPE = strtoupper($stamp_type);
		${$stamp_type.'_pstamp'} =
			isset($_POST[$STAMP_TYPE.'CB'])
		?	_date_getstamp($_POST, $STAMP_TYPE.'_P', utc: !$test)
		:	0;
	}

	$presale_stamp = isset($_POST['PRESALE_DATE_C']) ? _date_getstamp($_POST, 'PRESALE_',true) : 0;

	if ($test) {
		date_default_timezone_set($tz);
	}

	$at2400 = ($_POST['HOUR'] === 24);

	if (!empty($_POST['TZ'])) {
		if (preg_match('"^CE(?<summer>S)?T$"u', $_POST['TZ'], $match)) {
			$mins_offset = 0;
			$plus_min = '+';
			$hour_offset = !empty($match['summer']) ? 2 : 1;
		} elseif (preg_match('"^UTC([+\-])(\d+)(?::(\d+))?$"u', $_POST['TZ'], $match)) {
			[, $plus_min, $hour_offset] = $match;
			$mins_offset = $match[3] ?? 0;
		} else {
			register_error('party:error:timezone_not_recognized_LINE', ['TZ' => $_POST['TZ']]);
			return false;
		}
		if ($test) {
			print_rr($plus_min, 'plus_min');
			print_rr($hour_offset, 'hour_offset');
		}
		$offset = $plus_min.sprintf('%02d:%02d', $hour_offset, $mins_offset);
		# correct for Facebook dragged event
		# convert UTC\+\d+|CES?T date to local date
		$base_date = '%04d-%02d-%02dT%02d:%02d:%02d';
		if ($at2400) {
			$_POST['HOUR'] = 23;
			$_POST['MINS'] = 59;
			$secs = 59;
		} else {
			$secs = 0;
		}
		$datestr = sprintf($base_date.$offset, $_POST['YEAR'], $_POST['MONTH'], $_POST['DAY'], $_POST['HOUR'], $_POST['MINS'], $secs);
		$stamp = strtotime($datestr);
		$tz = date_default_timezone_get();
		date_default_timezone_set($use_tz);
		[$y, $m, $d, $hour, $mins] = _getdate($stamp);
		$stamp_tzi = gmmktime($hour, $mins, 0, $m, $d, $y);
		date_default_timezone_set($tz);
	} elseif ($at2400) {
		$stamp_tzi = _dateonly_getstamp($_POST, utc: true);
		[$y, $m, $d] = _getdate($stamp_tzi);
		$stamp_tzi = gmmktime(23, 59, 59, $m, $d, $y) + 1;
	} else {
		$stamp_tzi = _date_getstamp($_POST, utc: true);
	}
	if ($test) {
		change_timezone('UTC');
		print_rr($datestr, 'datestr');
		print_rr($stamp_tzi, 'stamp_tzi');
		print_rr(_datetime_get($stamp_tzi), '_datetime_get');
		change_timezone();
		# exit;
	}
	$conv = [
		'STAMP'				=> $stamp_tzi,
		'LINEUP_PSTAMP'		=> $lineup_pstamp,
		'TIMETABLE_PSTAMP'	=> $timetable_pstamp,
		'EVENT_PSTAMP'		=> $event_pstamp,
		'FLYER_PSTAMP'		=> $flyer_pstamp,
		'PRESALE_STAMP'		=> $presale_stamp ?? 0,
	];
	if ($test) {
		print_rr($conv, 'conv before');
		print_rr($use_tz, 'use_tz');
		exit;
	}
	$tz = date_default_timezone_get();
	foreach ($conv as $type => &$stamp) {
		if (!$stamp
		||	!is_int($stamp)
		) {
			continue;
		}
		date_default_timezone_set('UTC');
		[$year, $month, $day, $hour, $mins, $secs] = _getdate($stamp);
		/** @noinspection DisconnectedForeachInstructionInspection */
		date_default_timezone_set($use_tz);
		$stamp = mktime($hour, $mins, $secs, $month, $day, $year);
	}
	unset($stamp);
	date_default_timezone_set($tz);
	if ($test) {
		print_rr($conv, 'conf after');
		print_rr(_datetime_get($conv['STAMP']), 'STAMP');
		exit;
	}
	if ($conv['PRESALE_STAMP'] > $conv['STAMP']) {
		register_error('party:error:presale_start_must_be_before_event_starts_LINE');
		return false;
	}
	if ($partyid) {
		set_delayed_flyer($partyid,$conv['FLYER_PSTAMP']);
	}

	$setlist[] = 'STAMP_TZI			= '.$stamp_tzi;
	$setlist[] = 'STAMP				= '.$conv['STAMP'];
	$setlist[] = 'LINEUP_PSTAMP		= '.$conv['LINEUP_PSTAMP'];
	$setlist[] = 'TIMETABLE_PSTAMP	= '.$conv['TIMETABLE_PSTAMP'];
	$setlist[] = 'FLYER_PSTAMP		= '.$conv['FLYER_PSTAMP'];
	$setlist[] = 'EVENT_PSTAMP		= '.$conv['EVENT_PSTAMP'];


	$presale_setlist['LOCATION'] = isset($_POST['PRESALE_LOCATION']);
	$sites = [];
	$plgc = 0;

	# these providers are now done via url or shorthand in WEBSITE, to allow removing it
	$presale_setlist['PAYLOGICV2']			= null;
	$presale_setlist['PAYLOGICPOS']			= 0;
	$presale_setlist['YOURTICKETPROVIDER']	= null;
	$presale_setlist['IKBENAANWEZIG']		= null;
	$presale_setlist['EVENTTICKETS']		= null;
	$presale_setlist['TICKETMASTER']		= null;
	$seller = null;

	if ($_POST['PRESALE_WEBSITE']) {
		require_once '_presale.inc';
		require_once '_url.inc';

		$done_hosts = [];

		foreach (explode("\n", $_POST['PRESALE_WEBSITE']) as $site) {
			if (!($site = cleanup_url($site, true))) {
				continue;
			}
			if (preg_match('"^(?<url>https?://(?:'.
					# Generic links containing terms that indicate registration form
					'.+/(?:pre|voor)[_-]?registrati(?:e|on).*|'.
					'arep\.co/.+?(?:/finished)?|'.
					'docs\.google\.com/.+|'.
					# Google Docs shortened forms URL, indicating registration form
					'forms\.gle/.+|'.
					# JOTForm links indicating a registration form
					'form\.jotform(?:eu)?\.com/.+|'.
					# Typeform links indicating, again, a registration form
					'(?:form|([a-z\d]+\.)?pro)\.typeform\.com/.+|'.
					# Mailchimp links indicating a registration form (for mailing list)
					'mailchi\.mp/.+|'.
					# Subscriber Signup Forms:
					'[a-z\d]+\.sibforms\.com/.*|'.
					# Form Builder seems like a form to me
					'www\.123formbuilder\.com/.+|'.
					# Supremacy event form:
					'\?members=register$'.
				'))$"', $site, $match)
			) {
				register_warning('party:warning:preregistration_or_other_form_link_LINE', ['SITE' => $site]);
				if ($_POST['PREREG_URL']) {
					if ($_POST['PREREG_URL'] !== $site) {
						register_warning('party:warning:preregistration_url_is_explicit_cannot_move_from_presale_url_LINE');
					}
					continue;
				}
				$_POST['PREREG_URL'] = $match['url'];;
				register_notice('party:notice:preregistration_or_other_form_link_moved_to_preregistration_field_LINE');
				continue;
			}
			# NOTE: TivoliVredenburg == Eventim
			if (str_contains($site, $host = 'tivolivredenburg.nl')) {
				$seller = 'eventim';
				$sites[$site] = $site;
				$done_hosts[$host] = true;
			}
			if (false !== mb_stripos($site, 'ticketswap')) {
				# EXAMPLE: https://www.ticketswap.com/event/vunzige-deuntjes-festival-2024/3fd4b75b-97f1-43f0-ad2b-f26365ab831f
				if (preg_match('"^https?://(?:www\.)?ticketswap\.com/event/[-\w]+/[a-f\d]{8}-[a-f\d]{4}-[a-f\d]{4}-[a-f\d]{4}-[a-f\d]{12}$"ui', $site)) {
					$presale_setlist['TICKETSWAP'] = $site;
					continue;
				}
				register_error('party:error:ticketswap_link_not_understood_LINE', ['LINK' => $site]);
				return false;
			}

			# recognize shorthands
			if (preg_match('"^(PAYLOGIC):(\d+)(?::(\d+))?$"iu', $site, $match)
			||	preg_match('"^(YOURTICKETPROVIDER):(\d+)$"iu', $site, $match)
			||	preg_match('"^(NOWONLINETICKETS):([a-fA-F\d]+)$"iu', $site, $match)
			||	preg_match('"^(IKBENAANWEZIG):(.+)$"iu', $site, $match)
			||	preg_match('"^(EVENTTICKETS):(\w+)$"iu', $site, $match)
			||	preg_match('"^(TICKETMASTER):(\d+)$"iu', $site, $match)
			) {
				$seller = strtolower($match[1]);
				switch ($match[1]) {	# NOSONAR
				case 'PAYLOGIC':
					$presale_setlist['PAYLOGICV2' ] = (int)$match[2];
					$presale_setlist['PAYLOGICPOS'] = (int)($match[3] ?? 0);
					break;

				case 'TICKETMASTER':
				case 'YOURTICKETPROVIDER':
					$presale_setlist[$match[1]] = (int)$match[2];
					break;

				case 'EVENTTICKETS':
				case 'IKBENAANWEZIG':
				case 'NOWONLINETICKETS':
					$presale_setlist[$match[1]] = $match[2];
					break;
				}
				continue;
			}
			[$site, $data_url] = find_actual_ticket_url($site, data: $data, info: $info, utf8: true);

			if (false !== mb_stripos($site, 'bit.ly')) {
				if (!isset($info['url'])) {
					warning('Kon bit.ly link volgen om werkelijke URL te bemachtigen!');
				} else {
					$site = $info['url'];
				}
			}
			# Club Rodenburg convert to direct frame link
			if (preg_match('"^https?://(?:www\.)?clubrodenburg\.nl/event/(?<event_id>\d+)"ui', $site, $match)) {
				$site = "https://www.clubrodenburg.nl/tickets/{$match['event_id']}";
			}
			if ($site[0] === '/') {
				register_warning('party:warning:no_presale_there_LINE', DO_UBB, ['SITE' => $site]);
				continue;
			}
			$allow_non_seller = false;

			$search_for_presale = static function(string &$site, ?string &$new_site = null) use(&$seller, &$allow_non_seller, &$presale_setlist, &$search_for_presale) {
				# iHaveGotATicket.com
				if (false !== stripos($site, 'iHaveGotATicket.com')) {
					$seller = 'ihavegotaticket';
					return false;
				}
				if (str_contains($site, 'https://has-ticket.')) {
					$seller = 'paylogic';
					return false;
				}
				# NOTE: Paylogic
				if (preg_match_all('"queue\.paylogic\.com/(?<event_id>\d+)/(?<shop_id>\d+)"u', $site, $matches)
				||	preg_match_all('"EventID\":\s*(?<event_id>\d+).*?PosID\":\s*(?<shop_id>\d+)"su', $site, $matches)
				||	preg_match_all('!\.paylogic\.\w{2,}/[^\'"]*?\bevent_id=(?<event_id>\d+)[^\'"]*?point_of_sale_id=(?<shop_id>\d+)!u', $site, $matches)
				||	preg_match_all('!\.paylogic\.\w{2,}/[^\'"]*?point_of_sale_id=(?<shop_id>\d+)[^\'"]*?event_id=(?<event_id>\d+)!u', $site, $matches)
				||	preg_match_all('!\.paylogic\.\w{2,}/[^\'"]*?\bevent_id=(?<event_id>\d+)!u', $site, $matches)
				) {
					$seller = 'paylogic';
					if (isset($matches['event_id'][1])) {
						register_warning('party:warning:too_many_presales_one_presale_site,could_not_choose_LINE', ['PROVIDER' => 'Paylogic']);
						$allow_non_seller = true;
					} else {
						$presale_setlist['PAYLOGICV2' ] = (int)$matches['event_id'][0];
						$presale_setlist['PAYLOGICPOS'] = (int)($matches['shop_id'][0] ?? 0);
					}
					return true;
				}
				if (preg_match_all('"\b(?<url>shop\.paylogic\.[a-z]{2,}/[a-f\d]+)"iu', $site, $matches)
				||	preg_match_all('"\b(?<url>https://tickets\.[^/]+/[a-f\d]{16})$"iu', $site, $matches)
				) {
					$seller = 'paylogic';
					if (isset($matches['url'][1])) {
						register_warning('party:warning:too_many_presales_one_presale_site,could_not_choose_LINE', ['PROVIDER' => 'Paylogic']);
						$allow_non_seller = true;
						return false;
					}
					$new_link = $matches['url'][0];
					$new_site = str_starts_with($new_link, 'http') ? $new_link : 'https://'.$new_link;
				}
				if (preg_match('"\.paylogic\.[a-z]{2,}/flows/\w+/"iu', $site)) {
					$seller = 'paylogic';
					if (!isset($presale_setlist['PAYLOGICV2'])) {
						if ($data = safe_file_get_contents($site)) {
							if (preg_match('"event_id=(?<event_id>\d+)&amp;point_of_sale_id=(?<shop_id>\d+)"u', $data, $match)
							||	preg_match('"EventID\":\s*(?<event_id>\d+).*?PosID\":\s*(?<shop_id>\d+)"su', $data, $match)
							||	preg_match('"event_id[^:]*:[^u]*u0022(?<event_id>\d+).*?point_of_sale_id[^:]*:[^u]*u0022(?<shop_id>\d+)"u', $data, $match)
								# ^^ problem matching \u0022, workaround
							) {
								$presale_setlist['PAYLOGICV2' ] = (int)$match['event_id'];
								$presale_setlist['PAYLOGICPOS'] = (int)$match['shop_id'];
								return true;
							}
							if (preg_match('!event_id"\s+value="(?<event_id>\d+)"!u', $data, $match)) {
								$presale_setlist['PAYLOGICV2' ] = (int)$match['event_id'];
								$presale_setlist['PAYLOGICPOS'] = 0;
								register_warning('presale:warning:check_paylogic_might_not_work_LINE');
								return true;
							}
						}
						register_warning('presale:warning:within_session_LINE', DO_UBB, ['SITE' => $site]);
					}
					return true;
				}
				# NOTE: Tibbaa
				if (preg_match_all('"(?<url>http.*?\btibbaa\.com/order/[a-z\d]+)"iu', $site, $matches)
				||	preg_match_all('"\b(?<url>https?://tibbaa\.com/events/listing/\w+)"iu', $site, $matches)
				||	preg_match_all('!src="(?<url>https?://tibbaa\.com/iframes/show/\w+)!iu', $site, $matches)
				) {
					$seller = 'tibbaa';
					if (isset($matches['url'][1])) {
						register_warning('party:warning:too_many_presales_one_presale_site,could_not_choose_LINE', ['PROVIDER' => 'Tibbaa']);
						$allow_non_seller = true;
					} else {
						$new_site = 'https://tibbaa.com/order/'.$matches['url'][0];
					}
					return false;
				}
				# NOTE: Event-Tickets
				if (preg_match_all('"\bevent-tickets\.be/Event/(?<event_id>\w+)"iu', $site, $matches)) {
					$seller = 'eventtickets';
					if (isset($matches['event_id'][1])) {
						register_warning('party:warning:too_many_presales_one_presale_site,could_not_choose_LINE', ['PROVIDER' => 'Event-tickets']);
						$allow_non_seller = true;
						return false;
					}
					$presale_setlist['EVENTTICKETS'] = $matches['event_id'][0];
					return true;
				}
				# NOTE: TicketMaster / TicketService
				if (preg_match_all('!https://[^/]*?ticket(?:master|service)\.nl[^"]*?\beventId=(?<event_id>\d+)!iu', $site, $matches)
				||	preg_match_all('!href="[^"]*\bticketmaster\.nl/event/(?:[^"]*?/)?(?<event_id>\d+)\b!iu', $site, $matches)
				) {
					$seller = 'ticketmaster';
					if (isset($matches['event_id'][1])) {
						register_warning('party:warning:too_many_presales_one_presale_site,could_not_choose_LINE', ['PROVIDER' => 'Event-tickets']);
						$allow_non_seller = true;
						return false;
					}
					$presale_setlist['TICKETMASTER'] = (int)$matches['event_id'][0];
					return true;
				}
				# NOTE: YourTicketProvider
				# Legacy using storage of only ID:
				if (preg_match('"\byourticketprovider\.nl(?:/events/(\d+)|.*?productid=(\d+))(?:$|\")"u', $site, $match)) {
					$seller = 'yourticketprovider';
					$id = $match[empty($match[1]) ? 2 : 1];
					$presale_setlist['YOURTICKETPROVIDER'] = (int)$id;
					return true;
				}
				# <iframe frameborder="0" height="550" scrolling="yes" src="https://shop.yourticketprovider.nl/cd737ec0-f32c-d3e4-7938-a83fe97b9986" width="100%" class="normalIframe"></iframe>
				if (preg_match_all('!<iframe.*?src="(?<url>https://shop\.yourticketprovider\.nl/[a-f\d]{8}-[a-f\d]{4}-[a-f\d]{4}-[a-f\d]{4}-[a-f\d]{12})"!', $site, $matches)) {
					$seller = 'yourticketprovider';
					if (isset($matches['url'][1])) {
						register_warning('party:warning:too_many_presales_one_presale_site,could_not_choose_LINE', ['PROVIDER' => 'YourTicketProvider']);
						$allow_non_seller = true;
						return false;
					}
					$new_site = $matches['url'][0];
					return false;
				}
				# NOTE: Find YourTicketProvider iframe starting with shop. and check if we can deduce the ticketseller
				# <iframe id="ytp-widget-frame" src="https://shop.thuishaven.nl/12ec030d-8397-7ff5-2a2d-c07da924dda6" width="100%" style="border: none; overflow: auto"></iframe>
				if (preg_match('!iframe[a-z\d\s_+="]+?id="ytp-widget-frame"[a-z\d\s_+="]+?src="(?<shop_url>https?://shop\.[^"]+)"!iu', $site, $match)) {
					[$site /*, $data_url */] = find_actual_ticket_url($match['shop_url'], data: $site, utf8: true);
					#return $search_for_presale($match['shop_url'], $new_site);
					$new_site = $site;
					return false;
				}
				# NOTE: NowOnlineTickets
				if (preg_match('"nowonlinetickets\.nl/.*?(?<event_id>[A-F0-9]{32})"iu', $site, $match)) {
					$seller = 'nowonlinetickets';
					$presale_setlist['NOWONLINETICKETS'] = $match['event_id'];
					return true;
				}
				# NOTE: Chipta
				if (preg_match('!src="(?<url>https://iframeshop\.chipta\.com[^"]*)"!iu', $site, $match)) {
					$seller = 'chipta';
					$new_site = $match['url'];
					return false;
				}
				# NOTE: Eventbrite
				if (preg_match_all('!<iframe[^>]*src="(?<url>https?://(?:www\.)?eventbrite\..*?)"!iu', $site, $matches)) {
					$seller = 'eventbrite';
					if (isset($matches['url'][1])) {
						register_warning('party:warning:too_many_presales_one_presale_site,could_not_choose_LINE', ['PROVIDER' => 'Eventbrite']);
						$allow_non_seller = true;
					} else {
						$new_site = $matches['url'][0];
					}
					return false;
				}
				# NOTE: CM
				# <iframe class="wuksD5" title="Embedded Content" name="htmlComp-iframe" width="100%" height="100%" data-src="" src="https://store.ticketing.cm.com/raveolution20251129"></iframe>
				if (preg_match_all('!<iframe[^>]*src="(?<url>https?://(?:store\.)?(?:ticketing\.)?cm\.com[^"]*)"!iu', $site, $matches)) {
					$seller = 'cm';
					if (isset($matches['url'][1])) {
						register_warning('party:warning:too_many_presales_one_presale_site,could_not_choose_LINE', ['PROVIDER' => 'CM.com']);
						$allow_non_seller = true;
					} else {
						$new_site = $matches['url'][0];
					}
					return false;
				}
				# NOTE: GUTS
				if (preg_match_all('!<iframe[^>]*src="(?<url>https?://(?:(?:app|widget)\.)?guts\.(?:events|tickets)/[a-z\d])$"!iu', $site, $matches)) {
					$seller = 'guts';
					if (isset($matches['url'][1])) {
						register_warning('party:warning:too_many_presales_one_presale_site,could_not_choose_LINE', ['PROVIDER' => 'GUTS']);
						$allow_non_seller = true;
					} else {
						$new_site = $matches['url'][0];
					}
					return false;
				}
				if (preg_match_all('"\beventbrite\.[a-z]{2,}/(?:tickets|checkout)-external\?eid=(?<event_id>\d+)"iu', $site, $matches)
				||	preg_match_all('"\beventbrite\.[a-z]{2,}/e/(?:[\w\-]+-)?(?<event_id>\d+)$"iu', $site, $matches)
				||	preg_match_all('"(?:iframeContainerId|modalTriggerElementId):\h*\'eventbrite[^\']*?-(?<event_id>\d+)\'"iu', $site, $matches)
				) {
					$seller = 'eventbrite';
					if (isset($matches['event_id'][1])) {
						register_warning('party:warning:too_many_presales_one_presale_site,could_not_choose_LINE', ['PROVIDER' => 'Eventbrite']);
						$allow_non_seller = true;
					} else {
						$new_site = 'https://www.eventbrite.nl/e/'.$matches['event_id'][0];
					}
					return false;
				}
				# NOTE: Stager
				# <iframe src="https://so-what.stager.nl/web/tickets/170565?wmode=opaque"
				if (preg_match_all('!<iframe[^>]*?src="(?<url>https://[^/]*?\.stager\.nl/[^"]*)"!u', $site, $matches)
				||	preg_match_all('!<iframe src="(?<url>https://[^/]+\.stager\.nl/web/tickets/\d+)!iu', $site, $matches)
				||	preg_match_all('!data-ticket="(?<url>[^"]+)"!iu', $site, $matches)
				) {
					if (!str_contains($matches['url'][0], 'stager')) {
						mail_log('no stager URL found but did match', get_defined_vars());
					} else {
						$seller = 'stager';
						if (isset($matches['url'][1])) {
							register_warning('party:warning:too_many_presales_one_presale_site,could_not_choose_LINE', ['PROVIDER' => 'Stager']);
							$allow_non_seller = true;
						} else {
							$new_site = $matches['url'][0];
						}
						return false;
					}
				}
				# NOTE: Eventix
				if (preg_match_all('!data-url="(?<url>https://shop\.eventix\.io/[\da-f]+-[\da-f]+-[\da-f]+-[\da-f]+-[\da-f]+)!iu', $site, $matches)
				# <a href="https://eventix.shop/fuf4qrsg">deze link</a>
				||	preg_match_all('!\b(?:src|href)="(?<url>https://eventix\.shop/[a-zA-Z\d]+)"!iu', $site, $matches)
				# id="shop-frame" style="max-width: 600px; margin: 0 auto;" data-url="//shop.eventix.io/76f27d50-e801-11e6-ac6d-d70c9443cd7d
				||	preg_match_all('!data-url="(?<url>//shop\.eventix\.io/[a-f\d\-]+)"!iu', $site, $matches)
				) {
					$seller = 'eventix';
					if (isset($matches['url'][1])) {
						register_warning('party:warning:too_many_presales_one_presale_site,could_not_choose_LINE', ['PROVIDER' => 'Eventix']);
						$allow_non_seller = true;
					} else {
						$new_site = ($matches['url'][0][0] === '/' ? 'https:' : '').$matches['url'][0];
					}
					/** @noinspection RedundantReturnPointInspection */
					return false;
				}
				return false;
			};
			if ($search_for_presale($site, $newsite)) {
				continue;
			}
			if (!$allow_non_seller) {
				if (!empty($newsite)
				&&	$site !== $newsite
				) {
					$site = $newsite;
				} elseif ($data = safe_file_get_contents($data_url ?: $site)) {
					file_put_contents('/tmpdisk/data.html', $data);
					$newsite = null;
					if (str_contains($data, 'shopping-api.paylogic.com')) {
						$seller = 'paylogic';
					} elseif (str_contains($data, 'stager-ticketshop')) {
						$seller = 'stager';
					} elseif (preg_match('!agenda-sidebar__buy-button.*?href="(?<bitly_link>https://bit.ly/[^"]+)"!', $data, $match)) {
						[$newsite] = find_actual_ticket_url($match['bitly_link']);
					}
					if (!$newsite
					&&	$search_for_presale($data, $newsite)
					) {
						continue;
					}
					if (!empty($newsite)) {
						$site = $newsite;
					}
				}
			}
			if (!$site) {
				mail_log('party.commit: site for presale is empty');
				continue;
			}
			$base = base_url($site);
			if (!$allow_non_seller
			&&	isset($skip_urls[$base])
			) {
				register_warning('party:warning:need_direct_link_to_presale_site_LINE', DO_UBB, ['SITE' => $site]);
				continue;
			}
			if (is_bad_presale_site($site)
				# no amsterdamnightlifeticket.com tickets on other events!
			||	str_contains($site, 'amsterdamnightlifeticket.com')
			&&	(	!$all_orgs
				||	!in_array(16805, $all_orgs, true)
				)
			) {
				register_warning('party:warning:presale_site_not_supported_LINE', DO_UBB, ['SITE' => $site]);
				continue;
			}
			$host = parse_url($site, PHP_URL_HOST);
			if (!isset($done_hosts[$host])) {
				$done_hosts[$host] = true;
				$sites[$site] = $site;
			}
		}
	}
	$sellerid = 0;
	if ($seller || $sites) {
		require_once '_ticketseller.inc';
		if ($seller
		&& !($sellerid = get_seller_id($seller))
		) {
			mail_log('no seller id found for: '.$seller);
		}
		if (!$sellerid
		&&	($seller_url = array_key_first($sites))
		&&	($seller = get_seller_info_for_url($seller_url, include_inactive: $conv['STAMP'] < CURRENTSTAMP))
		) {
			$sellerid = $seller['SELLERID'];
		}
	}
	if ($presale_site = $_POST['PRESALE_WEBSITE'] ?? null) {
		$presale_host = parse_url($presale_site, PHP_URL_HOST);
		mail_log(
			"event commit seller detection, SELLERID = $sellerid for site $presale_site",
			get_defined_vars(),
			"event commit, SELLERID = $sellerid for host $presale_host",
			error_log: 'NOTICE'
		);
	}
	$presale_setlist['SELLERID'] = $sellerid;
	$presale_setlist['WEBSITE'] = $sites ? implode("\n", $sites) : '';

	if ($_POST['PRESALE_ADDR']) {
		$newaddr = null;
		foreach (explode("\n",$_POST['PRESALE_ADDR']) as $line) {
			$line = utf8_mytrim($line);
			if (false !== stripos($line, 'http')
			||	false !== stripos($line, 'www')
			) {
				register_warning('party:warning:presale_addr:no_sites_LINE', DO_UBB, ['SITE' => $line]);
				continue;
			}
			$newaddr .= $line."\n";
		}
		$_POST['PRESALE_ADDR'] = $newaddr;
	}

	if (isset($_POST['PRESALE_ADDR'])) {
		$presale_setlist['ADDR']  = $_POST['PRESALE_ADDR'];
	}
	if (isset($_POST['PRESALE_EMAIL'])) {
		$presale_setlist['EMAIL'] = $_POST['PRESALE_EMAIL'];
	}
	if ($_POST['PREREG_URL']) {
		$setlist[] = "PREREG_URL = '".addslashes($_POST['PREREG_URL'])."'";
	}
/*	if (HOME_THOMAS) {
		print_rr($setlist, 'setlist');
		print_rr($presale_setlist, 'presale_setlist');
		exit;
	}*/
	$accepted =
		!$conv['EVENT_PSTAMP']
	||	 $conv['EVENT_PSTAMP'] < CURRENTSTAMP;

	$setlist[] = 'PRESALE_STAMP = '.$conv['PRESALE_STAMP'];
	$setlist[] = 'PRESALE_STAMP_NOTIME = '.($presale_stamp && have_idnumber($_POST, 'PRESALE_HOUR') === 24 ? "b'1'" : "b'0'");

	if ($conv['STAMP'] >= CURRENTSTAMP) {
		if (!optional_working_email($_POST, 'PRESALE_EMAIL')) {
			$_POST['PRESALE_EMAIL'] = '';
		}
	}

	if (false !== ($hour = have_number($_POST, 'DOOR_CLOSE_HOUR'))) {
		$mins = have_number($_POST, 'DOOR_CLOSE_MINS') ?: 0;

		$setlist[] = 'DOOR_CLOSE_HOUR = '.($hour === 25 ? 'NULL' : $hour);
		$setlist[] = 'DOOR_CLOSE_MINS = '.$mins;
	}

	$setlist[] = 'VISITORCNT='.		($_POST['VISITORCNT'] ?: '0');
	$setlist[] = 'MIN_AGE='.		($_POST['MIN_AGE'] <= 1 ? '0' : $_POST['MIN_AGE']);
	$setlist[] = 'MIN_AGE_FEMALE='.	($_POST['MIN_AGE'] !== $_POST['MIN_AGE_FEMALE'] ? (int)$_POST['MIN_AGE_FEMALE'] : 0);
	$setlist[] = 'MAX_AGE='.		($_POST['USE_MAX_AGE'] ? $_POST['MAX_AGE'] : 0);
	$setlist[] = 'EROTIC='.			(isset($_POST['EROTIC'])   ? "b'1'" : "b'0'");
	$setlist[] = 'SEPARATE='.		(isset($_POST['SEPARATE']) ? "b'1'" : "b'0'");
	$setlist[] = 'PLACEHOLDER='.	(isset($_POST['PLACEHOLDER']) ? "b'1'" : "b'0'");

	foreach (['SITE', 'PRESALE_WEBSITE'] as $key) {
		if ($_POST[$key] === '-') {
			$_POST[$key] = '';
		}
	}

	if ($location) {
		require_once '_lastparty.inc';
		$lastparty = get_lastparty_in_dead_location($locationid, $_REQUEST['sID']);
		if ($lastparty === false) {
			return false;
		}
		if ($lastparty
		&&	$lastparty['STAMP'] < $conv['STAMP']
		) {
			if (empty($lastparty['PARTYID'])) {
				mail_log('lastparty PARTYID is null', item: $lastparty);
			}
			register_warning('party:warning:event_in_closed_location_TEXT', DO_UBB | DO_NL2BR, [
				'LAST_PARTYID'	=> $lastparty['PARTYID'],
				'LAST_DATE'	=> _dateday_get_tzi($lastparty['STAMP_TZI']),
			]);

			warning('Opgegeven locatie staat vermeld als gesloten en '.
				'je voegt nu een feest toe dat na het laaste al reeds geregistreerde feest aldaar ('.
				(empty($lastparty['PARTYID']) ? '???' : get_element_link('party',$lastparty['PARTYID'],$lastparty['NAME'])).
				', '._date_get($lastparty['STAMP']).') heeft plaatsgevonden!'
			);
		}
	}
	$setlist[] = 'LOCATIONID='.$locationid;

	$duration = have_number($_POST, 'DURATION_HOURS') ? $_POST['DURATION_HOURS'] * ONE_HOUR : 0;

	if (have_number($_POST, 'DURATION_MINS')) {
		$duration += $_POST['DURATION_MINS'] * 60;
	}

	require_once '_partyoverlap.inc';
	if (false === ($overlaps = warn_about_overlapping_parties($partyid, [
		'LOCATIONID'	=> have_idnumber($_POST, 'LOCATIONID'),
		'LOCATION_ADDR'	=> $_POST['LOCATION_ADDR'] ?? '',
		'STAMP'			=> $conv['STAMP'],
		'DURATION_SECS'	=> $duration
	]))) {
		return false;
	}

	require_once '_partytimes.inc';
	add_partytimes_to_setlist($setlist, $use_tz, $conv['STAMP'], $stamp_tzi, $duration);

	foreach (['ARRIVAL', 'DEPARTURE'] as $boat_time) {
		$hour_key = "{$boat_time}_HOUR";
		$mins_key = "{$boat_time}_MINS";
		if (!isset($_POST[$hour_key],
				   $_POST[$mins_key])
		) {
			$$boat_time = 0;
		} elseif ($_POST[$hour_key] === 24) {
			$$boat_time = 2500;
		} else {
			$$boat_time = $_POST[$hour_key] * 100 + $_POST[$mins_key];
		}
	}
	if ($DEPARTURE === 2500
	&&	$ARRIVAL   === 2500
	) {
		$DEPARTURE = 0;
		$ARRIVAL   = 0;
	}
	if (isset($_POST['UNKNOWN_LOCATION'])) {
		$_POST['LOCATION_ADDR'] = '';
		$setlist[] = 'CITYID = '.have_idnumber($_POST, 'CITYID');
	}

	if (empty($_POST['LOCKERS'])
	&&	!empty($_POST['PRICE_WHAT'])
	&&	is_array($_POST['PRICE_WHAT'])
	) {
		foreach ($_POST['PRICE_WHAT'] as $ndx => $whats) {
			if (is_array($whats)
			&&	in_array('locker',$whats, true)
			) {
				$_POST['LOCKERS'] = 1;
			}
		}
	}

	$setlist[] = 'EDITION='.	$_POST['EDITION'];
	$setlist[] = 'NAME="'.		addslashes($name).'"';

	$country_short = null;
	foreach (['boarding', 'location', 'party'] as $field) {
		$idname = "{$field}id";
		if (!$$idname) {
			continue;
		}
		if (false === ($country_short = db_single($field, "
			SELECT SHORT
			FROM `$field`
			JOIN city USING (CITYID)
			JOIN country USING (COUNTRYID)
			WHERE {$field}ID = {${$idname}}",
			DB_FORCE_MASTER
		))) {
			return false;
		}
		if ($country_short) {
			break;
		}
	}

	require_once '_stable_slug.inc';
	$setlist[] = 'SLUG="'.($country_short ? addslashes(get_slug_name($name, $country_short)) : '').'"';

	if (isset($_POST['NAME_FOR_APPIC'])) {
		$setlist[] = 'NAME_FOR_APPIC="'.addslashes($_POST['NAME_FOR_APPIC']).'"';
	}
	$setlist[] = 'SUBTITLE="'.	addslashes($subtitle).'"';
	if (isset($_POST['EXTRA'])) {
		$setlist[] = 'EXTRA="'.		addslashes($_POST['EXTRA']).'"';
	}
	$setlist[] = 'LOCKERS='.		(isset($_POST['LOCKERS']) ? "b'1'" : "b'0'");
	$setlist[] = 'AT2400='.			($at2400 ? "b'1'" : "b'0'");
	$setlist[] = 'FREE_ENTRANCE='.	(isset($_POST['FREE_ENTRANCE']) ? "b'1'" : "b'0'");
	$setlist[] = 'INVITEONLY='.		(isset($_POST['INVITEONLY']) ? "b'1'" : "b'0'");
	$setlist[] = 'DOORONLY='.		(isset($_POST['DOORONLY']) ? "b'1'" : "b'0'");
	$setlist[] = 'PRESALEONLY='.	(isset($_POST['PRESALEONLY']) ? "b'1'" : "b'0'");
	$setlist[] = 'DOORMORE='.		(isset($_POST['DOORMORE']) ? "b'1'" : "b'0'");
	$setlist[] = 'SOLD_OUT='.		(isset($_POST['SOLD_OUT']) ? "b'1'" : "b'0'");
	$setlist[] = 'PRESALE_SOLD_OUT='.(isset($_POST['PRESALE_SOLD_OUT']) ? "b'1'" : "b'0'");

	require_once '_status_overlay.inc';
	add_status_to_setlist($setlist);

	$setlist[] = 'LOCATION_ADDR="'.		(empty($_POST['LOCATIONID']) && !empty($_POST['LOCATION_ADDR']) ? addslashes($_POST['LOCATION_ADDR']) : '').'"';
	$setlist[] = 'LOCATIONTYPE="'.		($store_type ? addslashes($store_type) : null).'"';
	$setlist[] = 'DRESSCODE="'.			addslashes(utf8_ucfirst(utf8_mytrim($_POST['DRESSCODE'],'-:'))).'"';
	$setlist[] = 'DRESSCODE_MANDATORY=b\''.	(!empty($_POST['DRESSCODE_MANDATORY']) ? 1 : 0).'\'';
	$setlist[] = 'LIVESTREAM="'.		addslashes($_POST['LIVESTREAM']).'"';
	$setlist[] = 'LIVESTREAM_ADDRESS="'.(!empty($_POST['LIVESTREAM']) ? addslashes(cleanup_url($_POST['LIVESTREAM_ADDRESS'], true)) : null).'"';
	$setlist[] = 'CANCEL_REASON="'.		addslashes($_POST['CANCEL_REASON']).'"';
	$setlist[] = 'BOARDINGID='.			$boardingid;
	$setlist[] = 'BOARDING_ADDR="'.		(!$no_boat && !$boardingid ? addslashes($_POST['BOARDING_ADDR']) : null).'"';
	$setlist[] = 'DEPARTURE='.			$DEPARTURE;
	$setlist[] = 'ARRIVAL='.			$ARRIVAL;
	$setlist[] = 'LOCORG='.				(isset($_POST['LOCORG']) ? "b'1'" : "b'0'");
	$setlist[] = 'UNKNOWN_LOCATION='.	(isset($_POST['UNKNOWN_LOCATION']) ? "b'1'" : "b'0'");
	$setlist[] = 'RESTRICTION="'.		(isset($_POST['RESTRICTION']) ? addslashes($_POST['RESTRICTION']) : null).'"';

	$category = [
		'FESTIVAL'	 => false,
		'CONCERT'	 => false,
		'PREPARTY'	 => false,
		'AFTERPARTY' => false,
	];

	if (!empty($_POST['CATEGORY'])) {
		$category[strtoupper($_POST['CATEGORY'])] = true;
	}

	if ($category['FESTIVAL']
	&&	preg_match('"(?:after|pre)[\s-]*(?:hour|party)|after$"ui', $name)
	) {
		# after/pre party cannot be festival at the same time
		$category['FESTIVAL'] = false;
	}
	foreach ($category as $CATEGORY_TYPE => $set) {
		$setlist[] = $CATEGORY_TYPE.' = '.($set ? "b'1'" : "b'0'");
	}

	$setlist[] = 'DEMONSTRATION = '.(isset($_POST['DEMONSTRATION']) ? "b'1'" : "b'0'");

	if ($_REQUEST['sID']) {
		if (!empty($_POST['BOARDINGID'])
		||	!empty($_POST['LOCATIONID'])
		&&	$location
		&&	$location['CITYID']
		) {
			$setlist[] = 'CITYID=0';
		}
	}
	$setlist[] = 'DURATION_SECS = '.$duration;
	$setlist[] = 'ENDSTAMP_TZI = '.($stamp_tzi + $duration);

	if ($party_admin) {
		$setlist[] = 'AFTERMOVIE = '.have_idnumber($_POST, 'AFTERMOVIE');
	}

	if (!empty($_POST['VALICMT'])) {
		if (preg_match('"\b(?P<facebook_event_url>https?://(?:www\.)?facebook\.\w{2,3}/events/\d+)(?:/(?:\?\S+)?)?\b"u', $_POST['VALICMT'], $match)) {
			if ($_POST['PRESENCE']) {
				$_POST['PRESENCE'] .= "\n";
			}
			$_POST['PRESENCE'] .= $match['facebook_event_url'];

			$_POST['VALICMT'] = preg_replace(
				'"(?:^|\n)((?:\[url])?https?://(?:www\.)?facebook\.\w{2,3}/events/\d+(?:/(?:\?\S+)?)??(?:\[/url])?)\s*(?:\n|$)"iu',
				'',
				$_POST['VALICMT']
			);
		}
	}

	$_POST['VALICMT'] = _ubb_preprocess($_POST['VALICMT'], utf8: true);
	$setlist[] = 'VALICMT="'.addslashes($_POST['VALICMT']).'"';

	$flags = 0;
	if (!empty($_POST['FLAGS'])) {
		foreach ($_POST['FLAGS'] as $flg) {
			$flags |= $flg;
		}
	}
	$setlist[] = 'FLAGS='.$flags;

	if (!empty($_POST['SITE'])) {
		require_once '_ticketseller.inc';
		require_once '_site.inc';
		$val = make_proper_site($_POST['SITE'], utf8: true);
		#$val = $_POST['SITE'];
		if (false !== stripos($val, 'facebook.com')
		||	get_seller_info_for_url($val, false, true, true)
		) {
			register_warning('item:warning:ignored_site_LINE', ['SITE' => $val]);
			$val = '';
		} elseif (isset($skip_urls[base_url($val)])) {
			register_warning('party:warning:site_not_party_specific_LINE', ['SITE' => $val]);
			$val = '';
		} elseif (!require_working_url_or_none($_POST, 'SITE', utf8: true)) {
			$val = '';
		}
	} else {
		$val = '';
	}
	$_POST['SITE'] = $val;
	$setlist[] = 'SITE="'.($val ? addslashes($val) : null).'"';
	if ($partyid) {
		if (!require_last_lock(LOCK_PARTY, $partyid)) {
			return false;
		}
		if (!($old_party = db_single_assoc(['party', 'location', 'boarding', 'city'], '
			SELECT	party.NAME, SUBTITLE, STAMP, CANCELLED, LOCATIONID, BOARDINGID,
					city.CITYID
			FROM party
			LEFT JOIN location USING (LOCATIONID)
			LEFT JOIN boarding USING (BOARDINGID)
			LEFT JOIN city ON city.CITYID = COALESCE(boarding.CITYID, location.CITYID, party.CITYID)
			WHERE PARTYID = '.$partyid,
			DB_FORCE_MASTER))
		) {
			if ($old_party === null) {
				register_error('party:error:nonexistent_LINE', ['PARTYID' => $partyid]);
			}
			return false;
		}
		assert(is_array($old_party)); # Satisfy EA inspection
		if (false === ($have_presaleinfo = db_single('presaleinfo','SELECT 1 FROM presaleinfo WHERE PARTYID='.$partyid,DB_FORCE_MASTER))) {
			return false;
		}
		# if the user is no admin, he must be the creator
		# and the entry may not be valid yet..
		$extra = !$party_admin ? ' AND ACCEPTED = b\'0\' AND USERID = '.CURRENTUSERID : '';

		if (!$accepted) {
			$setlist[] = 'ACCEPTED = b\'0\'';
		}

		reset_site();

		if (!db_insert('party_log','
			INSERT INTO party_log
			SELECT * FROM party
			WHERE NOT '.binary_equal($setlist).'
			  AND PARTYID = '.$partyid.
			  $extra,
			DB_FORCE_MASTER)
		) {
			return false;
		}

		if (db_affected()) {
			if (!db_update('party','
				UPDATE party SET
					MUSERID	='.CURRENTUSERID.',
					MSTAMP	='.CURRENTSTAMP.',
					'.implode(',',$setlist).'
				WHERE PARTYID = '.$partyid.
				$extra)
			) {
				return false;
			}
		}
		register_notice('party:notice:changed_LINE');
		if ($old_party['LOCATIONID'] !== $locationid) {
			page_changed('location', $old_party['LOCATIONID']);
			partylist_needs_refresh('location', $old_party['LOCATIONID']);
		}
		if ($conv['STAMP'] > (CURRENTSTAMP - 2 * ONE_DAY)
		&&	(	$old_party['NAME']		 !== $_POST['NAME']
			||	$old_party['SUBTITLE']	 !== $_POST['SUBTITLE']
			||	$old_party['LOCATIONID'] !== $locationid
			||	$old_party['STAMP']		 !== $conv['STAMP']
			)
		) {
			page_changed('location', $locationid,);
			partylist_needs_refresh('location', $locationid);
			require_once '_profile.inc';
			clear_element_profile('party', $partyid);
		}
		if ($old_party['BOARDINGID'] !== $boardingid) {
			page_changed('boarding', [$boardingid, $old_party['BOARDINGID']]);
			partylist_needs_refresh('boarding', [$boardingid, $old_party['BOARDINGID']]);
			require_once '_profile.inc';
			clear_element_profile('party', $partyid);
		}
	} else {
		$have_presaleinfo = false;
		if (!db_insert('party','
			INSERT INTO party SET
				CSTAMP			= '.CURRENTSTAMP.',
				USERID			= '.CURRENTUSERID.',
				MUSERID			= '.CURRENTUSERID.',
				MSTAMP			= '.CURRENTSTAMP.',
				ACCEPTED		= '.($set_accept = $party_admin && $accepted ? '1' : '0').',
				LAST_VALIDATED	= '.($set_accept ? CURRENTSTAMP : 0).',
				FIRST_VALIDATED	= '.($set_accept ? CURRENTSTAMP : 0).','.
				implode(',', $setlist))
		) {
			return false;
		}
		$_REQUEST['sID'] = $partyid = db_insert_id();

		if (isset($_POST['APPIC'])) {
			db_replace('via_appic','REPLACE INTO via_appic SET PARTYID='.$partyid);
		}

		if ($accepted) {
			require_once '_pagechanged.inc';
			page_changed('location', $locationid, 'boarding', $boardingid);

			partylist_needs_refresh('new');
			if ($locationid) {
				partylist_needs_refresh('location',$locationid);
			}
			if ($boardingid) {
				partylist_needs_refresh('boarding',$boardingid);
			}
		}
		if ($srcid = have_idnumber($_POST, 'SRCID')) {
			if (HOME_THOMAS
			# 	FIXME: Needs to be tested on sandbox first:
			&&	SERVER_SANDBOX
			) {
				$updates = [];
				if (isset($_POST['DUPLICATE']['lineup'])) {
					require_once '_duplicate_children.inc';
					foreach (['lineup', 'partyarea'] as $table) {
						if(!duplicate_children('PARTYID', $srcid, $partyid, $table)) {
							return false;
						}
					}
					$updates['lineupneedupdate'] = ["PARTYID = $src", "PARTYID = $partyid, CUSERID = 0, CSTAMP = ".CURRENTSTAMP];
				}
				if (isset($_POST['DUPLICATE']['stuff'])) {
					# FIXME: duplicate agenda spotlight ads -> not done

					db_insert('connect', "
					INSERT IGNORE INTO connect (MAINTYPE, MAINID, ASSOCTYPE, ASSOCID, CSTAMP, CUSERID)
					SELECT	 MAINTYPE, IF ( MAINTYPE = 'party' AND  MAINID = $srcid, $partyid,  MAINID),
							ASSOCTYPE, IF (ASSOCTYPE = 'party' AND ASSOCID = $srcid, $partyid, ASSOCID),
							".CURRENTSTAMP.',
							'.CURRENTUSERID."
					FROM connect
					WHERE  MAINTYPE = 'party' AND  MAINID = $srcid
					   OR ASSOCTYPE = 'party' AND ASSOCID = $srcid"
					);
					if (db_insert('connect', "
						INSERT IGNORE INTO connect (MAINTYPE, MAINID, ASSOCTYPE, ASSOCID, CSTAMP, CUSERID)
						VALUES	('party', $partyid, 'party', $srcid,   ".CURRENTSTAMP.', '.CURRENTUSERID."),
								('party', $srcid,	'party', $partyid, ".CURRENTSTAMP.', '.CURRENTUSERID.')')
					) {
						page_changed('party', $srcid);
					}
					$updates = [...$updates,
						'going'			=> ["PARTYID = $srcid", "PARTYID = $srcid, CSTAMP = ".CURRENTSTAMP.', MSTAMP = '.CURRENTSTAMP],
						'alternate_name'=> ["ID = $srcid AND ELEMENT = 'party'", "ID = $partyid, CUSERID = 0, CSTAMP = ".CURRENTSTAMP],
						'party_partner'	=> ["PARTYID = $srcid", "PARTYID = $partyid, CUSERID = 0, CSTAMP = ".CURRENTSTAMP],
						'pixelparty'	=> ["PARTYID = $srcid", "PARTYID = $partyid, CUSERID = 0, CSTAMP = ".CURRENTSTAMP.', AUTOEXTENDED = 1'],
						'presaleinfo'	=> ["PARTYID = $srcid", "PARTYID = $partyid, MUSERID = 0, MSTAMP = ".CURRENTSTAMP],
						'flush_pixels'	=> static function() use ($partyid): void {
							update_pixel_stopstamp(partyid: $partyid);
							flush_active_pixels();
						}];
				}
				if (isset($_POST['DUPLICATE']['flyers'])) {
					$updates = [...$updates,
						'uploadimage_link'		=> [$where = "ID = $srcid AND TYPE IN ('party', 'party2', 'eventmap', 'campingmap')", "ID = $partyid"],
						'uploadimage_generated'	=> [$where, $update = "ID = $partyid, USERID = 0, CSTAMP = ".CURRENTSTAMP],
						'uploadimage_crop'		=> [$where, $update],
						'flush_uploadimages'	=> static function() use ($srcid): void {
							require_once '_uploadimage.inc';
							clear_uploadimage_cache('party', $srcid);
					}];
				}
				if ($updates) {
					foreach ($updates as $table => $extra_set) {
						if (is_callable($extra_set)) {
							$extra_set();
							continue;
						}
						[$where, $update] = $extra_set;

						if (!db_create($table, "
							CREATE TEMPORARY TABLE {$table}_tmp
							SELECT * FROM $table
							WHERE $where")
						) {
							return false;
						}
						/** @noinspection SqlWithoutWhere */
						if (!db_update($table, "
							UPDATE {$table}_tmp SET $update")
						) {
							return false;
						}
						if (!db_insert($table, "
							INSERT INTO $table
							SELECT * FROM {$table}_tmp")
						) {
							return false;
						}
					}
				}
			} else {
				if (isset($_POST['DUPLICATE']['lineup'])) {
					require_once '_duplicate_children.inc';
					foreach (['lineup', 'partyarea'] as $table) {
						if(!duplicate_children('PARTYID', $srcid, $partyid, $table)) {
							return false;
						}
					}
					if (!(db_insert('lineupneedupdate','
						INSERT INTO lineupneedupdate (PARTYID, CSTAMP, CUSERID)
						SELECT '.$partyid.','. CURRENTSTAMP.', '.CURRENTUSERID.'
						FROM lineupneedupdate
						WHERE PARTYID = '.$srcid,
						DB_FORCE_MASTER))
					) {
						return false;
					}
				}
				if (isset($_POST['DUPLICATE']['stuff'])) {
					# duplicate agenda spotlight ads -> not done

					db_insert('going','
					INSERT INTO going (PARTYID,USERID,MAYBE,CSTAMP,MSTAMP)
					SELECT '.$partyid.',USERID,MAYBE,CSTAMP,'.CURRENTSTAMP.'
					FROM going
					WHERE PARTYID='.$srcid
					);
					db_insert('alternate_name','
					INSERT INTO alternate_name (ELEMENT, ID, NAME, CUSERID, CSTAMP)
					SELECT "party", '.$partyid.', NAME, '.CURRENTUSERID.', '.CURRENTSTAMP.'
					FROM alternate_name AS source_alternate_name
					WHERE ELEMENT = "party"
					  AND ID = '.$srcid
					);
					db_insert('presaleinfo','
					INSERT INTO presaleinfo (PARTYID,ADDR,LOCATION,WEBSITE,EMAIL,TICKETSCRIPTV2,PAYLOGICV2,PAYLOGICPOS,TICKETMASTER,YOURTICKETPROVIDER,NOWONLINETICKETS,IKBENAANWEZIG,EVENTIM,AUTO, MSTAMP)
					SELECT '.$partyid.',ADDR,LOCATION,WEBSITE,EMAIL,TICKETSCRIPTV2,PAYLOGICV2,PAYLOGICPOS,TICKETMASTER,YOURTICKETPROVIDER,NOWONLINETICKETS,IKBENAANWEZIG,EVENTIM,AUTO, '.CURRENTSTAMP.'
					FROM presaleinfo
					WHERE PARTYID='.$srcid
					);
					db_insert('connect','
					INSERT IGNORE INTO connect (MAINTYPE,MAINID,ASSOCTYPE,ASSOCID,CSTAMP,CUSERID)
					SELECT	MAINTYPE,
						IF(MAINTYPE="party" AND MAINID='.$srcid.','.$partyid.',MAINID),
						ASSOCTYPE,
						IF(ASSOCTYPE="party" AND ASSOCID='.$srcid.','.$partyid.',ASSOCID),
						'.CURRENTSTAMP.','.CURRENTUSERID.'
					FROM connect
					WHERE MAINTYPE="party" AND MAINID='.$srcid.'
					   OR ASSOCTYPE="party" AND ASSOCID='.$srcid
					);
					if (db_insert('connect','
						INSERT IGNORE INTO connect (MAINTYPE,MAINID,ASSOCTYPE,ASSOCID,CSTAMP,CUSERID)
						VALUES	("party",'.$partyid.',"party",'.$srcid.  ','.CURRENTSTAMP.','.CURRENTUSERID.'),
							("party",'.$srcid.  ',"party",'.$partyid.','.CURRENTSTAMP.','.CURRENTUSERID.')')
					) {
						page_changed('party', $srcid);
					}

					db_insert('party_partner','
					INSERT INTO party_partner (PARTYID,PARTNER,CUSERID,CSTAMP)
					SELECT '.$partyid.',PARTNER,CUSERID,CSTAMP
					FROM party_partner
					WHERE PARTYID='.$srcid
					);

					require_once '_pixel.inc';
					db_insert('pixelparty','
					INSERT IGNORE INTO pixelparty (PIXELID, PARTYID, CSTAMP, CUSERID, ACTIVE, AUTOEXTENDED)
					SELECT PIXELID, '.$partyid.', '.CURRENTSTAMP.', '.CURRENTUSERID.', ACTIVE, 1
					FROM pixelparty
					WHERE PARTYID='.$srcid
					);
					update_pixel_stopstamp(partyid: $partyid);
					flush_active_pixels();
				}
				if (isset($_POST['DUPLICATE']['flyers'])) {
					db_insert('uploadimage_link', "
					INSERT INTO uploadimage_link (TYPE, ID, UPIMGID, FALLBACK, `DEFAULT`)
					SELECT TYPE, $partyid, UPIMGID, FALLBACK, `DEFAULT`
					FROM uploadimage_link
					WHERE TYPE IN ('party', 'party2', 'eventmap', 'campingmap')
					  AND ID = $srcid"
					);
					db_insert('uploadimage_generated', "
						INSERT INTO uploadimage_generated (`GENERATED`, TYPE, ID, UPIMGID, CSTAMP, USERID)
						SELECT `GENERATED`, TYPE, $partyid, UPIMGID, ".CURRENTSTAMP.", 0
						FROM uploadimage_generated
						WHERE TYPE IN ('party', 'party2', 'eventmap', 'campingmap')
						  AND ID = $srcid"
					);
					db_insert('uploadimage_crop','
						INSERT INTO uploadimage_crop (UPIMGID, CSTAMP, USERID, WIDTH, HEIGHT, X, Y, TYPE, ID)
						SELECT UPIMGID, '.CURRENTSTAMP.", 0, WIDTH, HEIGHT, X, Y, TYPE, $partyid
						FROM uploadimage_crop
						WHERE TYPE IN ('party', 'party2', 'eventmap', 'campingmap')
						  AND ID = $srcid"
					);
					require_once '_uploadimage.inc';
					clear_uploadimage_cache('party', $partyid);
				}
			}
			foreach (['camera', 'camerarequest'] as $table) {
				if (!duplicate_children('PARTYID', $srcid, $partyid, 'camera', false)
				||	!duplicate_children('PARTYID', $srcid, $partyid, 'camerarequest', [
					'MSTAMP = '.CURRENTSTAMP,
					'MUSERID = '.CURRENTUSERID,
					'LSTAMP = '.CURRENTSTAMP,
				])) {
					# $ok = false;
				}
			}
		}
		register_notice('party:notice:added_LINE');

		ticket_update('party',$partyid);
		init_site();

		if (!empty($token)) {
			if (!db_insert('element_token','
				INSERT INTO element_token SET
					ELEMENT	="party",
					ID	='.$partyid.',
					TOKEN	='.$token)
			) {
				# $ok = false;
			} else {
				setflockcookie('FLOCK_TOKEN_'.LOCK_PARTY.'_'.$partyid, $token, ONE_MONTH);
			}
		}
	}

	if ($party_admin
	&&	$conv['STAMP'] > CURRENTSTAMP
	&&	(false !== ($cityid = db_single('party', "
			SELECT city.CITYID
			FROM party
			LEFT JOIN location USING (LOCATIONID)
			LEFT JOIN boarding USING (BOARDINGID)
			JOIN city ON city.CITYID = COALESCE(boarding.CITYID, location.CITYID, party.CITYID)
			WHERE PARTYID = $partyid",
			DB_FORCE_MASTER)))
	) {
		if (!empty($old_party)) {
			if ($old_party['CITYID'] !== $cityid) {
				if ($cityid) {
					page_changed('city', $cityid);
				}
				if ($old_party['CITYID']) {
					page_changed('city', $old_party['CITYID']);
				}
			}
		} elseif ($cityid) {
			page_changed('city',$cityid);
		}
	}

	if ($links = getifset($_POST, 'DESCRIPTION_LINKS')) {
		$link_setlist = [];
		foreach (explode("\x1F",$links) as $link) {
			$link_setlist[$link_quoted = '"'.addslashes($link).'"'] = "($partyid, ".CURRENTSTAMP.", $link_quoted)";
		}
		if (!db_insert('facebook_description_link_log','
			INSERT INTO facebook_description_link_log
			SELECT * FROM facebook_description_link
			WHERE LINK NOT IN ('.implodekeys(',',$link_setlist).')
			  AND PARTYID='.$partyid)
		||	!db_insert('facebook_description_link','
			INSERT INTO facebook_description_link (PARTYID,STAMP,LINK)
			VALUES '.implode(',',$link_setlist))
		) {
			# $ok = false;
		}
	}
	if ($eventobj = $_POST['EVENTOBJ'] ?? null) {
		$eventobj_s = addslashes($eventobj);
		if (false === ($have_event_obj = db_single_int('facebook_event', "
			SELECT 1
			FROM facebook_event
			WHERE EVENTOBJ = '$eventobj_s'
			  AND PARTYID = $partyid",
			DB_FORCE_MASTER))
		) {
			# $ok = false;
		} elseif (!$have_event_obj) {
			foreach (['facebook_event', 'facebook_event_log'] as $table) {
				if (!db_replace($table, "
					REPLACE INTO `$table` SET
						DBL		 = 0,
						EVENTOBJ = '$eventobj_s',
						PARTYID	 = $partyid,
						STAMP	 = ".CURRENTSTAMP)
				) {
					# $ok = false;
				}
			}
		}
	}

	# store uuid for this commit

	if (!empty($_POST['IS_SENT'])
	&&	!db_insert('party_is_sent','
		INSERT INTO party_is_sent SET
			UNIQID	= UNHEX(REPLACE("'.addslashes($_POST['IS_SENT']).'", "-", "")),
			USERID	= '.CURRENTUSERID.',
			STAMP	= '.CURRENTSTAMP.',
			PARTYID	= '.$partyid)
	) {
		# $ok = false;
	}

	commit_party_prices($partyid, $conv['STAMP'], $use_tz);

	if (isset($presale_setlist)) {
		[$inserts, $values, $bincmp] = inserts_values_and_bincmp($presale_setlist);

		if ($have_presaleinfo) {
			if (!db_insert('presaleinfo_log', '
				INSERT INTO presaleinfo_log
				SELECT * FROM presaleinfo
				WHERE NOT '.$bincmp.'
				  AND PARTYID = '.$partyid)
			) {
				return false;
			}
			$nocreate_presaleinfo = !db_affected();
		}
		if (!$nocreate_presaleinfo) {
			if (!db_insupd('presaleinfo','
				INSERT INTO presaleinfo SET
					PARTYID	= '.$partyid.',
					MSTAMP	= '.CURRENTSTAMP.',
					MUSERID	= '.CURRENTUSERID.','.
					$inserts.'
				ON DUPLICATE KEY UPDATE
					MSTAMP	= VALUES(MSTAMP),
					MUSERID	= VALUES(MUSERID),'.
					$values)
			) {
				return false;
			}
			# force recheck of ticket link
			db_delete('urlcheck', 'DELETE FROM urlcheck WHERE ELEMENT = "presale" AND ID = '.$partyid);
		}
	}

	flush_party($partyid);

	_genrelist_commit('party', $partyid);

	$orghosts = getifset($_POST, 'ORGASHOSTID');
	$org_process = [];

	if (!empty($all_orgs)) {
		$org_process['organization'] = $all_orgs;
	}
	if (!empty($orghosts)) {
		$org_process['orgashost'] = $orghosts;
	}
	$updorgids = [];
	if (!empty($org_process)) {
		unset($setlist);
		$setlist = [];
		$setdirlist = [];
		foreach ($org_process as $type => $organizationids) {
			$doneorg = [];
			foreach ($organizationids as $organizationid) {
				if (!$organizationid) {
					continue;
				}
				if (isset($_POST[$org_key = 'ORG'.strtoupper($type).'ID'][$organizationid])) {
					// connect already exists!
					$_POST[$org_key][$organizationid] = false;
					continue;
				}
				if (isset($doneorg[$organizationid])) {
					continue;
				}
				$doneorg[$organizationid] = true;

				$setlist[] = "('party', '$type', $partyid, $organizationid, ".CURRENTUSERID.', '.CURRENTSTAMP.')';
				$setlist[] = "('$type', 'party', $organizationid, $partyid, ".CURRENTUSERID.', '.CURRENTSTAMP.')';

				$updorgids[] = $organizationid;

				$setdirlist[] = "('party', '$type', ".CURRENTSTAMP.')';
			}
		}
		if ($setlist) {
			if (!db_insert('connect','
				INSERT IGNORE INTO connect (MAINTYPE, ASSOCTYPE, MAINID, ASSOCID, CUSERID, CSTAMP)
				VALUES '.implode(', ', $setlist))
			||	!db_insert('connect_direction','
				INSERT INTO connect_direction (MAINTYPE, ASSOCTYPE, CSTAMP)
				VALUES '.implode(', ', $setdirlist))
			) {
				//
			}
		}
		if (!empty($all_orgs)) {
			require_once '_pixel.inc';
			foreach ($all_orgs as $organizationid) {
				connect_family('party', $partyid, 'organization', $organizationid);
				auto_extend_pixel($organizationid, $partyid);
			}
		}
	}
	$remlist = [];
	foreach ([
		'ORGORGANIZATIONID'	=> 'organization',
		'ORGORGASHOSTID'	=> 'orgashost',
	] as $org_id_name => $type) {
		if (empty($_POST[$org_id_name])) {
			continue;
		}
		require_once '_pixel.inc';
		foreach ($_POST[$org_id_name] as $organizationid => $remove) {
			auto_extend_pixel($organizationid, $partyid, extend: !$remove);
			if (!$remove) {
				continue;
			}
			$remlist[] = '("party", "'.$type.'", '.$partyid.', '.$organizationid.')';
			$remlist[] = '("'.$type.'", "party", '.$organizationid.', '.$partyid.')';
			$updorgids[] = $organizationid;
		}
		if (isset($remlist)) {
			$wherep = '(MAINTYPE, ASSOCTYPE, MAINID, ASSOCID) IN ('.implode(', ', $remlist).')';
			if (!db_insert('connect_log','
				INSERT INTO connect_log
				SELECT *, '.CURRENTSTAMP.', '.CURRENTUSERID.'
				FROM connect
				WHERE '.$wherep)
			||	!db_delete('connect','
				DELETE FROM connect
				WHERE '.$wherep)
			) {
				//
			}
		}
	}
	if (!empty($doneorg)
	&&	$conv['STAMP'] > (CURRENTSTAMP - 2 * ONE_DAY)
	&&	!empty($old_party)
	&&	(	$old_party['NAME']		!== $_POST['NAME']
		||	$old_party['SUBTITLE']	!== $_POST['SUBTITLE']
		||	$old_party['STAMP']		!== $conv['STAMP']
		||	$old_party['CANCELLED'] !== (empty($_POST['CANCELLED']) ? false : true)
		)
	) {
		$tmp_orgids = array_keys($doneorg);
		page_changed('organization', $tmp_orgids);
		partylist_needs_refresh('organization', $tmp_orgids);

	} elseif (!empty($updorgids)) {
		page_changed('organization', $updorgids);
		partylist_needs_refresh('organization', $updorgids);
	}

	store_presence($skip_urls ?? null);

	if ($party_admin) {
		require_once '_alternate_name.inc';
		commit_alternate_names('party', $partyid);
	}

	if (isset($_POST['PLACEHOLDER'])) {
			db_insert('lineup_log', '
			INSERT INTO lineup_log
			SELECT *, '.CURRENTSTAMP.', '.CURRENTUSERID.'
			FROM lineup
			WHERE PARTYID = '.$partyid)
		&&	db_delete('lineup', '
			DELETE FROM lineup
			WHERE PARTYID = '.$partyid);

		if (db_affected()) {
			register_notice('party:info:lineup_has_been_archived_LINE');
		}
	}

	require_once '_uploadimage.inc';
	uploadimage_actual_receive('party',$partyid);

	require_once '_process_appic_changes.inc';
	cleanup_update_from_appic($partyid);

	require_once '_elementchanged.inc';
	element_changed();

	require_once '_inquirymail.inc';
	process_inquiry_allowance();

	if (!empty($_POST['SALESCONTACTME'])) {
		db_insert('salescontactme','
			INSERT IGNORE INTO salescontactme SET
				CSTAMP	= '.CURRENTSTAMP.',
				USERID	= '.CURRENTUSERID.',
				LANGID	= '.CURRENTLANGID.',
				PARTYID = '.$partyid
		);
	}

	if (have_admin('party')) {
		require_once '_bio.inc';
		commit_bios_only('party', $partyid);

		# facebook_info
		if (require_anything_trim($_POST, 'FBTITLE', utf8: true)
		&&	require_anything_trim($_POST, 'FBDESCRIPTION', utf8: true)
		) {
			$current_fbids = [];
			if (preg_match_all('"facebook\.\w{2,}/events/(?:s/[^/]+/)?(\d+)"u', $_POST['PRESENCE'], $matches)) {
				$current_ids = array_values($matches[1]);
			}
			if (preg_match_all('"facebook\.\w{2.}/events/(?:s/[^/]+/)?(\d+)(?:/?\?event_time_id=|/)(\d+)"u', $_POST['PRESENCE'], $matches)) {
				$current_fbids = [...$current_ids, ...array_values($matches[1])];
			}
			sort($current_fbids);

			if (false === ($previous = db_single_assoc('facebook_info','
				SELECT NAME, DESCRIPTION
				FROM facebook_info
				WHERE PARTYID = '.$partyid,
				DB_FORCE_MASTER))
			||	false === ($partyids = db_same_hash(['connect','party'],'
				SELECT ASSOCID
				FROM connect
				JOIN party ON ASSOCID=PARTYID
				WHERE NAME = "'.addslashes($name).'"
				  AND STAMP BETWEEN '.($conv['STAMP'] - ONE_WEEK).' AND '.($conv['STAMP'] + ONE_WEEK).'
				  AND MAINTYPE = "party"
				  AND MAINID = '.$partyid.'
				  AND ASSOCTYPE = "party"',
				DB_FORCE_MASTER))
			) {
				return false;
			}
			$partyids[] = $partyid;

			$wherep =  '
					 PARTYID IN ('.implode(',',$partyids).')
				  AND '.(   $current_fbids
						?   '(SELECT GROUP_CONCAT(FBID) FROM fbid WHERE ELEMENT = "party" AND ID = PARTYID ORDER BY FBID) = "'.implode(',', $current_fbids).'"'
						:   '(SELECT GROUP_CONCAT(FBID) FROM fbid WHERE ELEMENT = "party" AND ID = PARTYID ORDER BY FBID) IS NULL'
					);

			if ($previous) {
				if (false === ($sames = db_same_hash(['facebook_info', 'fbid'],'
					SELECT PARTYID
					FROM facebook_info
					WHERE NAME		  = "'.addslashes($previous['NAME']).'"
					  AND DESCRIPTION = "'.addslashes($previous['DESCRIPTION']).'"
					  AND '.$wherep,
					DB_FORCE_MASTER))
				) {
					return false;
				}
			} else {
				if (false === ($haves = db_same_hash('facebook_info','
					SELECT PARTYID
					FROM facebook_info
					WHERE '.$wherep,
					DB_FORCE_MASTER))
				) {
					return false;
				}
				$sames = array_diff_key($partyids, $haves);
			}

			if ($sames) {
				$changed = [];
				if ($previous) {
					if ($previous['DESCRIPTION']) {
						$previous['DESCRIPTION'] = plain_text(str_replace("\r", '', utf8_mytrim($previous['DESCRIPTION'])), UBB_UTF8);
					}
				}

				$_POST['FBDESCRIPTION'] = str_replace("\r", '', utf8_mytrim($_POST['FBDESCRIPTION']));

				if (!$previous || ($previous['DESCRIPTION'] ?? null) !== $_POST['FBDESCRIPTION']) {
					$changed[] = 'DESCRIPTION = VALUES(DESCRIPTION)';

/*					if ($current_fbids) {
						db_update('fbtor_event','
						UPDATE fbtor_event SET DRAGGED_DESC='.CURRENTSTAMP.'
						WHERE FBID IN ('.implode(',',$current_fbids).')'
						);
					}*/
				}
				if (!$previous || ($previous['NAME'] ?? null) !== $_POST['FBTITLE']) {
					$changed[] = 'NAME = VALUES(NAME)';
				}

				if ($changed) {
					if (!db_insert('facebook_info_log','
						INSERT INTO facebook_info_log
						SELECT *,'.CURRENTUSERID.','.CURRENTSTAMP.'
						FROM facebook_info
					 	WHERE PARTYID IN ('.($samestr = implode(',',$sames)).')')
					) {
						return false;
					}
					foreach ($sames as $sameid) {
						if ($_POST['FBTITLE']
						||	$_POST['FBDESCRIPTION']
						) {
							if (!db_replace('facebook_info','
								INSERT INTO facebook_info SET
									PARTYID		= '.$sameid.',
									NAME		= "'.addslashes($_POST['FBTITLE']).'",
									DESCRIPTION	= "'.addslashes($_POST['FBDESCRIPTION']).'",
									CUSERID		= '.CURRENTUSERID.',
									CSTAMP		= '.CURRENTSTAMP.'
								ON DUPLICATE KEY UPDATE
									CUSERID		= VALUES(CUSERID),
									CSTAMP		= VALUES(CSTAMP),
									'.implode(', ', $changed) )
							) {
								return false;
							}
						} elseif (!db_delete('facebook_info','
							DELETE FROM facebook_info
							WHERE PARTYID='.$sameid)
						) {
							return false;
						}
					}
				}
			}
		}
	}

	if (empty($old_party)) {
		# move visitors of fb event to pf event
		if (false === ($fbid = db_single('fbid', 'SELECT FBID FROM fbid WHERE ELEMENT = "party" AND ID = '.$partyid, DB_FORCE_MASTER))) {
			return false;
		}
		if ($fbid) {
			require_once '_fbstore.inc';
			fb_store_upgrade($partyid,$fbid);

			if (!db_delete('facebook_ignore', 'DELETE FROM facebook_ignore WHERE FBID = '.$fbid)) {
				return false;
			}
		}
	} else {
		require_once '_stable_slug.inc';
		flush_slugs();
	}
	if (have_admin('party')) {
		$desc = $_POST['APPICDESC'] ?? null;
		$desc = utf8_mytrim($desc);
		if (false === ($mainid = db_single('party', '
			SELECT '.PARTY_MAIN_ID.'
			FROM party
			WHERE PARTYID = '.$partyid,
			DB_FORCE_MASTER))
		) {
			return false;
		}
		if ($mainid === null) {
			error_log('mainid is null for partyid '.$partyid, get_defined_vars());
		}
		if ($mainid !== $partyid) {
			if (false === ($have_old = db_single('appic_event_info','
				SELECT 1
				FROM appic_event_info
				WHERE MAINID = '.$partyid,
				DB_FORCE_MASTER
			))) {
				return false;
			}
			if ($have_old) {
				if (!db_update('appic_event_info_log','
					UPDATE appic_event_info_log SET
						MAINID = '.$mainid.'
					WHERE MAINID = '.$partyid)
				||	!db_update('appic_event_info','
					UPDATE IGNORE appic_event_info SET
						MAINID = '.$mainid.'
					WHERE MAINID = '.$partyid)
				||	!db_insert('appic_event_info_log','
					INSERT INTO appic_event_info_log (MAINID, BODY, MUSERID, MSTAMP)
					SELECT '.$mainid.', BODY, MUSERID, MSTAMP
					FROM appic_event_info
					WHERE MAINID = '.$partyid)
				||	!db_delete('appic_event_info','
					DELETE FROM appic_event_info
					WHERE MAINID = '.$partyid)
				) {
					return false;
				}
			}
		}

		$have_same =
			empty($desc)
		?	!db_single('appic_event_info','
			SELECT 1
			FROM appic_event_info
			WHERE MAINID = '.$mainid,
			DB_FORCE_MASTER)
		:	db_single('appic_event_info','
			SELECT 1
			FROM appic_event_info
			WHERE BODY = "'.addslashes($desc).'"
			  AND MAINID = '.$mainid,
			DB_FORCE_MASTER);

		if (!$have_same) {
			if (!db_insert('appic_event_info_log','
				INSERT INTO appic_event_info_log
				SELECT * FROM appic_event_info
				WHERE MAINID = '.$mainid)
			) {
				return false;
			}
			if (empty($desc)) {
				if (!db_insert('appic_event_info_log','
					INSERT INTO appic_event_info_log SET
						MAINID	= "'.$mainid.'",
						BODY	= "",
						MSTAMP	= '.CURRENTSTAMP.',
						MUSERID	= '.CURRENTUSERID)
				||	!db_delete('appic_event_info','
					DELETE FROM appic_event_info
					WHERE MAINID ='.$mainid)
				) {
					return false;
				}
			} elseif (!db_replace('appic_event_info','
				REPLACE INTO appic_event_info SET
					MAINID	= "'.$mainid.'",
					BODY	= "'.addslashes($desc).'",
					MSTAMP	= '.CURRENTSTAMP.',
					MUSERID	= '.CURRENTUSERID)
			) {
				return false;
			}
		}

		if (have_something($_POST, 'LIVESTREAM_ADDRESS')
		&&	(require_once '_livestream.inc')
		&&	($embed_address = get_embeddable_livestream($_POST['LIVESTREAM_ADDRESS']))
		&&	preg_match('"youtube\.com/watch\?v=(.*)"u', $embed_address, $match)
		) {
			[, $externalid] = $match;

			if (false !== ($videoid = db_single('video', '
				SELECT VIDEOID
				FROM video
				WHERE EXTERNALID = "'.addslashes($externalid).'"
				  AND TYPE = "youtube"',
				DB_FORCE_MASTER))
			&&	!$videoid
			) {
				require_once '_videoinfo.inc';

				$video_data = get_videoinfo_data('youtube', $externalid, $videoid);
				$video_info = fill_videoinfo_youtube($video_data);

				/** @noinspection NotOptimalIfConditionsInspection */
				if ($video_info
				&&	empty($video_info['INVISIBLE'])
				&&	db_insert('video', "
					INSERT INTO video SET
						STATUS		= 'inactive',
						TYPE		= 'youtube',
						PSTAMP		= {$video_info['PSTAMP']},
						DURATION	= {$video_info['DURATION']},
						CONTENTTYPE	= 'liveset',
						EXTERNALID	= '".addslashes($externalid)."',
						USERID		= ".CURRENTUSERID.',
						CSTAMP		= '.CURRENTSTAMP)
				&&	($videoid = db_insert_id())
				) {
					require_once '_cachevideothumbs.inc';
					cache_video_thumbs($videoid, 'youtube', $externalid);
					fill_videoinfo('youtube', $videoid, $externalid, null, $video_data);
					page_changed('video', $videoid);
				}
			}
			if ($videoid) {
				$vid_setlist = [];
				$vid_setlist[] = "('party', $partyid, 'video', $videoid, ".CURRENTSTAMP.', '.CURRENTUSERID.')';
				$vid_setlist[] = "('video', $videoid, 'party', $partyid, ".CURRENTSTAMP.', '.CURRENTUSERID.')';

				db_insert('connect','
					INSERT IGNORE INTO connect (MAINTYPE, MAINID, ASSOCTYPE, ASSOCID, CSTAMP, CUSERID)
					VALUES '.implode(', ', $vid_setlist)
				);
			}
		}

		# mark done for proclist

		require_once '_processlist.inc';
		set_proclist_done('party', $partyid);

		# page_changed also calls reset_urltitle
		page_changed('party', $partyid);
	}
	return $partyid;
}

function show_long_past($party) {
	if ($party['ACCEPTED']
	&&	$party['CSTAMP'] < CURRENTSTAMP - 2 * ONE_DAY
	||	($diff = $party['STAMP'] + ONE_YEAR - $party['CSTAMP']) < 0
	||	$diff > 4 * ONE_MONTH
	) {
		return;
	}
	change_timezone('UTC');
	[$y]= _getdate($party['STAMP_TZI']);
	?><div class="block"><?=
		__('party:info:maybe_in_year_TEXT',DO_UBB, [
			'YEAR'		=> $y + 1,
			'CREATED'	=> _date_get($party['CSTAMP'])
		])
	?></div><?
	change_timezone();
}


function show_party_maybe_identicals(array $party): void {
	$location = getifset($party, 'location');

	if (!$maybes = db_rowuse_hash(['party', 'location', 'boarding', 'connect'], '
		SELECT	PARTYID, party.NAME, SUBTITLE, STAMP, STAMP_TZI, NOTIME, UNKNOWN_LOCATION, DEPARTURE, ARRIVAL,
				DURATION_SECS, LOCATION_ADDR, BOARDING_ADDR, party.LOCATIONTYPE, party.CITYID, CAST_TO_BIT(0+party.ACCEPTED) AS ACCEPTED,
				LIVESTREAM, LIVESTREAM_ADDRESS,
			LOCATIONID,location.NAME AS LOC_NAME,
			BOARDINGID,
			COALESCE(boarding.CITYID, location.CITYID, party.CITYID) AS USECITYID
/*			GROUP_CONCAT(ASSOCID) AS ORGIDS*/
		FROM party
		LEFT JOIN location USING (LOCATIONID)
		LEFT JOIN boarding USING (BOARDINGID)
		LEFT JOIN connect ON MAINTYPE = "party"
				 AND MAINID = PARTYID
				 AND ASSOCTYPE = "organization"
		WHERE STAMP BETWEEN '.($party['STAMP'] - 12 * ONE_HOUR).' AND '.($party['STAMP'] + 12 * ONE_HOUR).'
		  AND PARTYID != '.$party['PARTYID'].'
		GROUP BY PARTYID'
	)) {
		return;
	}

	$stripper = function(string $name): string {
		return	mb_strtolower(
			utf8_to_ascii(
			preg_replace('"[^a-z]+"iu', '', $name
			)));
	};
	$party_name	 = $stripper($party['NAME']);
	$location_name  = $location ? $stripper($location['NAME'])  : '';
	$location_title = $location ? $stripper($location['TITLE']) : '';

	foreach ($maybes as $maybe) {
		foreach (['NAME', 'LOCATION_ADDR'] as $key) {
			$name = $maybe[$key];
			$key .= '_cmp';
			$$key = $stripper($name);
		}
		$name_pct =
		$location_name_pct =
		$location_title_pct =
		$addr_name_pct =
		$addr_title_pct = 0;

		similar_text($NAME_cmp, $party_name, $name_pct);

		if ($party['LOCATIONID']
		&&	$maybe['LOCATIONID'] === $party['LOCATIONID']
		) {
			$maybe['maybeTIME'] = true;
		}
		if ($LOCATION_ADDR_cmp) {
			similar_text($LOCATION_ADDR_cmp, $location_name,  $addr_name_pct);
			similar_text($LOCATION_ADDR_cmp, $location_title, $addr_title_pct);
		}
		if ($name_pct >= 75) {
			$maybe['maybeNAME'] = true;
		}
		if ($location_title_pct	>= 75
		||	$location_name_pct	>= 75
		||	$addr_title_pct		>= 75
		||	$addr_name_pct		>= 75
		) {
			$maybe['maybeLOCATION'] = true;
		}
		if (isset($maybe['maybeLOCATION'])
		||	isset($maybe['maybeNAME'])
		||	isset($maybe['maybeTIME'])
		) {
			$shows[] = $maybe;
		}
	}
	if (empty($shows)) {
		return;
	}
	foreach ($shows as $show) {
		fill_location_and_boarding($show);
		show_party_info($show, true);
	}
}

function show_party_info(array &$party, bool $showmaybe = false): void {
	$partyid = $party['PARTYID'];
	$location = $party['location'] ?? null;
	$boarding = $party['boarding'] ?? null;
	if ($showmaybe) {
		static $__header = false;
		if (!$__header) {
			$__header = true;
			?><div class="block" style="margin-left: 2.5em;">&rarr; <?= __('status:maybe_identical') ?>:</div><?
		}
	}

	?><div class="block"><?
	?><div class="relative party-info ib" style="<?
	?>display: table-cell;<?
	if ($showmaybe) {
		?>margin-left: 5em;<?
	}
	?>"><?
	if ($showmaybe) {
		?><div class="header bold maybe"><?
		echo get_element_link('party',$partyid);
		if ($party['SUBTITLE']) {
			?> <small>&middot; <?= escape_utf8($party['SUBTITLE']) ?></small><?
		}
		?><div class="r <?= $party['ACCEPTED'] ? 'notice' : 'error' ?> lmrgn"><?=
			__($party['ACCEPTED'] ? 'attrib:validated' : 'attrib:not_validated_yet')
		?></div><?
		?></div><?
	}

	///////////////////// DATE & TIME

	if (!$showmaybe) {
		# header only for main event
		include_og('partyflock:event:start', gmdate($party['NOTIME'] ? 'Y-m-d' : ISO8601Z, $party['STAMP_TZI']));
	}

	if ($party['STAMP']) {
		show_party_time($party, !$showmaybe, $party['TIMEZONE'] ?? null); ?><br /><?
	}

	/** @noinspection NotOptimalIfConditionsInspection */
	if (!$showmaybe
	&&	CURRENTSTAMP < $party['STAMP'] + ($party['DURATION_SECS'] ?: 6 * ONE_HOUR)
	&&	(require_once '_weather.inc')
	&&	($weather = get_weather($partyid))
	&&	!empty($weather['temperature'])
	) {
		?><span class="light7 lmrgn rmrgn"><?
		show_temperature($weather);
		?> <?
		?> <img style="height: 2em; vertical-align: -.625em;" src="<?= STATIC_HOST ?>/weather/<?= $weather['iconcode'] ?>.<?= webp_supported() ?: 'png' ?>" /><?
		?></span><?
		?><br /><?
	}

	$departure = $party['DEPARTURE'];
	$arrival = $party['ARRIVAL'];
	if ($departure || $arrival) {
		printf('%02d:%02d', $h = (int)($departure / 100), $departure - $h * 100);
		if ($arrival < 2400) {
			printf(' - %02d:%02d',
				$h = (int)($arrival   / 100), $arrival   - $h * 100
			);
		}
		?> (<?= __('action:sailing') ?>)<br /><?
	}
	//////////////////// LOCATION

	if (!$showmaybe) {
		?><span itemprop="location" itemscope itemtype="https://schema.org/<?= $party['LIVESTREAM'] === 'only' ? 'VirtualLocation' : 'Place' ?>"><?
	}
	$city = [];
	if ($party['LIVESTREAM'] === 'only') {
		if ($party['LIVESTREAM_ADDRESS']
		&&	!$showmaybe
		) {
			?><meta itemprop="url" content="<?= escape_utf8($party['LIVESTREAM_ADDRESS']) ?>" /><?
		}
	} else {
		if ($party['USECITYID']
		&&	($city = memcached_single_assoc(['city', 'province'], "
			SELECT	CITYID, city.NAME, COUNTRYID, LATITUDE, LONGITUDE, REGIONID,
				PROVINCEID, SHOWPROVINCE, province.NAME AS PNAME
			FROM city
			LEFT JOIN province USING (PROVINCEID,COUNTRYID)
			WHERE CITYID = {$party['USECITYID']}",
			TEN_MINUTES))
		) {
			$party['city'] = $city;
		}

		$parts = [];

		# FIXME: When $showmaybe is true, no rich snipped information should be output

		ob_start();
		if ($location) {
			if (!($removed = $location['REMOVED'])) {
				?><a href="<?= get_element_href('location', $location['LOCATIONID'], $location['NAME']); ?>"><?
			}
			?><span<?
			if ($removed) {
				?> class="warning"<?
			}
			if (!$showmaybe) {
				?> itemprop="name"<?
			}
			?>><?
			echo escape_utf8($location['TITLE']);
			if (!$removed) {
				?></span></a><?
			} else {
				?> (<?= __('elementstatus:removed'); ?>)</span><?
			}

			if (!empty($party['locationtypes'])) {
				echo ' <span class="small">('.implode(', ',$party['locationtypes']).')</span>';
			}

			if (!empty($location['NEEDUPDATE'])
			&&	have_admin('location')
			) {
				require_once '_needupdates.inc';
				show_new_profile_info('location',$location['LOCATIONID']);
			}

			show_dead($location);

			require_once '_virtualtour.inc';
			if ($tourbutton = get_virtualtour_button('location',$location['LOCATIONID'])) {
				?> <?
				?><div class="ib insidehzw"><?
					?><div class="abs middle" style="margin-top:-20px;"><?= $tourbutton ?></div><?
				?></div><?
			}
			if (!$boarding
			&&	$location['LATITUDE']
			) {
				show_distance_to_item($location,' ');
				?> <?
				require_once '_maplink.inc';
				show_map_link($location, 16, 'minimap');

			}
			if (!empty($location['SITE'])) {
				?> <?
				show_site_button('location', $location['LOCATIONID'], $location['SITE']);
			}
			if (have_admin('party')) {
				require_once '_presence.inc';
				link_to_facebook_events('location', $location['LOCATIONID']);
				link_to_instagram('location', $location['LOCATIONID']);
			}
			if (!$location['ACCEPTED']) {
				?> <span class="warning">(<?= __('attrib:not_validated_yet') ?>)</span><?
			}
		} elseif ($party['UNKNOWN_LOCATION']) {
			if (!$party['IS_WHOLE']) {
				?><span<?
				if (!$showmaybe) {
					?> itemprop="name"<?
				}
				?>><?=
					element_name($boarding ? 'unknown_boat' : 'unknown_location')
				?></span><?
			}
			if (!empty($party['locationtypes'])) {
				$cnt = count($party['locationtypes']);
				$locationtypestr = '';
				if ($cnt > 1) {
					foreach ($party['locationtypes'] as $location_type => $location_type_desc) {
						--$cnt;
						$locationtypestr .=
							$locationtypestr
						?	($cnt ? ', ' : ' & ').$location_type_desc
						:	$location_type;
					}
				} else {
					[, $locationtypestr] = keyval($party['locationtypes']);
				}
				echo ' <span class="small">('.$locationtypestr.')</span>';
			}
		}
		if ($data = ob_get_clean()) {
			$parts[] = $data;
		}
		if ($boarding) {
			ob_start();
			?><a href="<?= get_element_href('boarding', $boarding['BOARDINGID'], $boarding['ADDRESS']) ?>"><?
			if (str_contains($boarding['ADDRESS'], $boarding['NAME'])) {
				# substr, show addr only
				echo escape_utf8($boarding['ADDRESS']);
			} else {
				echo escape_utf8($boarding['NAME']);
				if ($boarding['NAME'] !== $boarding['ADDRESS']) {
					?>, <? echo escape_utf8($boarding['ADDRESS']);
				}
			}
			?></a><?

			if ($boarding['LATITUDE']) {
				show_distance_to_item($boarding,' ');
				?> <?
				require_once '_maplink.inc';
				show_map_link($boarding, 16, 'minimap');
			}
			$parts[] = ob_get_clean();

		} elseif (!empty($party['location']['ADDRESS'])) {
			if (ROBOT
			||	$party['location']['ADDRESS'] !== $party['location']['TITLE']
			) {
				ob_start();
				?><small class="light7"><?= escape_utf8($party['location']['ADDRESS']) ?></small><?
				$parts[] = ob_get_clean();
			}
		}

		ob_start();
		?><span<?
		if (!$showmaybe) {
			?> itemprop="address"<?
			?> itemscope<?
			?> itemtype="https://schema.org/PostalAddress"<?
		}
		?>><?
			if (($useaddress = $boarding['ADDRESS'] ?? $location['ADDRESS'] ?? null)
			&&	!$showmaybe
			) {
				?><meta itemprop="streetAddress" content="<?= escape_utf8($useaddress) ?>" /><?
				/*?><span class="hidden"><?
				?><span itemprop="streetAddress"><?= escape_utf8($useaddress) ?></span>, <?
				?></span><?*/
			}
			if ($city) {
				$show_country = $city['COUNTRYID'];
				$show_city	  = $city['CITYID'];
			} elseif (
				$party['UNKNOWN_LOCATION']
			&&	$party['CITYID']
			) {
				$show_country = $party['COUNTRYID'];
				$show_city	  = $party['CITYID'];
			} else {
				$show_city	  = false;
				$show_country = false;
			}

			if ($show_city || $show_country) {
				?><span<?
				if (!$showmaybe) {
					?> itemprop="addressLocality"<?
				}
				?>><?= get_element_link('city', $show_city) ?></span><?
				if ($city && !empty($city['REGIONID'])) {
					?><br /><?
					?><span<?
					if (!$showmaybe) {
						?> itemprop="addressRegion"<?
					}
					?>><?= get_element_link('region', $city['REGIONID']) ?></span><?
				}

				if (!$show_country) {
					mail_log('show_country is empty', item: get_defined_vars());
				} else {
					require_once '_countryflag.inc';
					?><br /><?
					?><span<?
					if (!$showmaybe) {
						?> itemprop="addressCountry"<?
					}
					?>><?= get_element_link('country', $show_country) ?></span><?
					show_country_flag($show_country, 'lmrgn light');

					if ($city && !empty($city['SHOWPROVINCE'])) {
						?> <span<?
						if (!$showmaybe) {
							?> itemprop="addressRegion"<?
						}
						?> class="light">(<?= get_element_link('province', $show_city) ?>)</span><?
					}
				}
			}
			if (!$showmaybe
			&&	($zipcode = getifset($boarding, 'ZIPCODE') ?: getifset($location, 'ZIPCODE'))
			) {
				?><meta itemprop="postalCode" content="<?= escape_utf8($zipcode) ?>" /><?
			}
		?></span><?

		if (!$showmaybe
		&&	($info = $boarding ?: $location ?: getifset($party,'city'))
		&&	($latitude  = $info['LATITUDE'])
		&&	($longitude = $info['LONGITUDE'])
		) {
			?><span itemprop="geo" itemscope itemtype="https://schema.org/GeoCoordinates"><?
				?><meta itemprop="latitude"  content="<?= $latitude  ?>" /><?
				?><meta itemprop="longitude" content="<?= $longitude ?>" /><?
			?></span><?
		}

		$parts[] = ob_get_clean();

		echo implode('<br />', $parts);
	}

	?></span><?

	$warnparts = ['LOCATION_ADDR', 'BOARDING_ADDR'];

	$addr = [];
	foreach ($warnparts as $key) {
		if (!$party[$key]) {
			continue;
		}
		$addr[] = escape_utf8($party[$key]);
	}
	if ($addr) {
		?><div<?
		if (!$showmaybe) {
			?> class="warning"<?
		}
		?>><?= implode('<br />', $addr) ?></div><?
	}
	?></div><?
	?></div><?
}

function fill_location_and_boarding(array &$party): void {
	$selects = [
		'location'	=> [
			$party['LOCATIONID'],
			'LOCATIONID, NEEDUPDATE, DEAD, SITE, ACCEPTED, FOLLOWUPID, item.NAME, CAPACITY, EROTIC, item.CITYID, ADDRESS, ZIPCODE, item.LATITUDE, item.LONGITUDE, CURRENCY, '.
			'COALESCE((SELECT NAME FROM itemname WHERE ELEMENT="location" AND ID=LOCATIONID AND ENDSTAMP>'.$party['STAMP'].' ORDER BY ENDSTAMP DESC LIMIT 1), item.TITLE) AS TITLE'
		],
		'boarding'	=> [
			$party['BOARDINGID'],
			'BOARDINGID, item.NAME, item.CITYID, ADDRESS, ZIPCODE, item.LATITUDE, item.LONGITUDE, CURRENCY'
		]
	];
	foreach ($selects as $element => $info) {
		[$id, $selects] = $info;
		if (!$id) {
			$party[$element] = [];
			continue;
		}
		/** @noinspection ArrayPackableInspection */
		foreach ([
			0 => $element,
			1 => "{$element}_log"
		] as $removed => $table) {
			if (!($data = memcached_single_assoc_if_not_admin([$element, 'city', 'country'],$table, "
				SELECT $selects, COUNTRYID, $removed AS REMOVED, REGIONID
				FROM $table AS item
				LEFT JOIN city USING (CITYID)
				LEFT JOIN country USING (COUNTRYID)
				WHERE {$element}ID = $id
				ORDER BY item.MSTAMP DESC
				LIMIT 1",
				TEN_MINUTES))
			) {
				if ($data === false) {
					return;
				}
				break;
			}
		}
		$party[$element] = $data ?? [];
	}
}

function get_location(int $id): array|false|null {
	foreach ([
		false	=> 'location',
		true	=> 'location_log'
	] as $removed => $table) {
		if (!($location = db_single_assoc([$table, 'city', 'country'], "
			SELECT	LOCATIONID, FOLLOWUPID, loc.NAME, loc.TITLE, DEAD, LOCATIONTYPE, ZIPCODE, ADDRESS,
					$removed AS REMOVED, EROTIC, MIN_AGE, NEEDUPDATE,
					CITYID, city.NAME AS CITY_NAME,
					country.COUNTRYID, CURRENCY
			FROM `$table` AS loc
			LEFT JOIN city USING (CITYID)
			LEFT JOIN country USING (COUNTRYID)
			WHERE LOCATIONID = $id"))
		) {
			if ($location === false) {
				return false;
			}
			continue;
		}
		if ($removed) {
			mail_log('get_location got result from log', item: get_defined_vars());
		}
		return $location;
	}
	return null;
}

function get_boarding(int $id): array {
	foreach ([
		false	=> 'boarding',
		true	=> 'boarding_log'
	] as $removed => $table) {
		if (($boarding = db_single_assoc(['city', 'country'], "
			SELECT	BOARDINGID, item. NAME, ADDRESS, ZIPCODE, $removed AS REMOVED,
					CITYID, city.NAME AS CITY_NAME,
					country.COUNTRYID, CURRENCY
			FROM `$table` AS item
			LEFT JOIN city USING (CITYID)
			LEFT JOIN country USING (COUNTRYID)
			WHERE BOARDINGID = $id"))
		||	$boarding === false
		) {
			break;
		}
	}
	return $boarding;
}
