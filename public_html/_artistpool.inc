<?php

declare(strict_types=1);

function artistpool_remove_artist(): bool {
	require_once '_remove.inc';
	$element = $_REQUEST['sELEMENT'];
	return	require_admin($element)
		&&	require_post()
		&&	($id = require_idnumber($_REQUEST, 'sID'))
		&&	($artistid = require_idnumber($_REQUEST, 'subID'))
		&&	remove_artistpool($artistid, $element, $id);
}

function artistpool_status_artist(): bool {
	$element = $_REQUEST['sELEMENT'];
	if (!require_admin($element)
	||	!require_post()
	||	!($id = require_idnumber($_REQUEST,'sID'))
	||	!($artistid = require_idnumber($_REQUEST,'subID'))
	||	!($status = require_element($_REQUEST,'SUBACTION',['active', 'inactive'], true))
	) {
		return false;
	}
	$active = $status === 'active' ? '1' : '0';
	if (!db_insert('artistpool_log','
		INSERT INTO artistpool_log
		SELECT *, '.CURRENTUSERID.', '.CURRENTSTAMP."
		FROM artistpool
		WHERE ARTISTID = $artistid
		  AND ELEMENT = '$element'
		  AND ID = $id
		  AND ACTIVE != $active")
	||	!db_update('artistpool','
		UPDATE artistpool SET
			MUSERID	= '.CURRENTUSERID.',
			MSTAMP	= '.CURRENTSTAMP.",
			ACTIVE	= $active
		WHERE ARTISTID = $artistid
		  AND ELEMENT = '$element'
		  AND ID = $id
		  AND ACTIVE != $active")
	) {
		return false;
	}
	register_notice($active ? 'artistpool:notice:artist_activated_LINE' : 'artistpool:notice:artist_deactivated_LINE');
	return true;
}

function artistpool_display_overview(): void {
	$element = $_REQUEST['sELEMENT'];
	if (!($id = require_idnumber($_REQUEST,'sID'))
	||	!require_admin($element)
	) {
		return;
	}
	layout_show_section_header();

	if (!($name = get_element_title($element,$id))) {
		if ($name !== false) {
			not_found();
		}
		return;
	}
	if (!empty($_POST['NAME'])) {
		if (!require_something_trim($_POST, 'NAME', null, null, utf8: true)) {
			return;
		}
		require_once '_artist.inc';
		# $members = db_simpler_array('artistpool','SELECT ARTISTID FROM artistpool WHERE ELEMENT="'.$element.'" AND ID='.$id);
		if (!($options = get_artist_choices(0, $_POST['NAME'], $option_count))) {
			register_error('artistpool:error:no_artist_found_with_name_LINE',DO_UBB, ['NAME' => $_POST['NAME']]);
		} elseif ($option_count > 1) {
			register_warning('artistpool:warning:multiple_artists_possible_LINE', ['CNT' => $option_count]);
		} else {
			[$_REQUEST['ARTISTID']] = keyval($options);
			unset($options);
		}
	}
	if ($artistid = have_idnumber($_REQUEST,'ARTISTID')) {
		if (false === ($artist = db_single_assoc('artist', '
				SELECT FIND_IN_SET("producer",TYPE) AS ISPRODUCER
				FROM artist
				WHERE ARTISTID = '.$artistid))
		) {
			return;
		}
		if (!$artist) {
			not_found('artist', $artistid);
			return;
		}
		if (!db_insert('artistpool','
			INSERT IGNORE INTO artistpool SET
				ELEMENT	 = "'.$element.'",
				ID		 = '.$id.',
				ARTISTID = '.$artistid.',
				ACTIVE	 = '.(isset($_POST['ACTIVE']) ? 1 : 0).',
				CUSERID	 = '.CURRENTUSERID.',
				CSTAMP	 = '.CURRENTSTAMP,
			DB_DONT_WARN_DUP_ENTRY)
		&&	!is_duplicate()
		) {
			return;
		}
		if (db_affected()) {
			register_notice('artist:notice:added_LINE', DO_UBB, ['ARTISTID' => $artistid]);
			unset($_POST['NAME']);

		} elseif (is_duplicate()) {
			register_warning('artistpool:warning:artist_already_connected_LINE');
		}
	}
 	display_messages();
	layout_open_box($element);
	layout_box_header(get_element_link($element,$id));

	if (false === ($artists = db_rowuse_hash(['artistpool','artist'], "
		SELECT ARTISTID, NAME, ACTIVE, COUNTRYID
		FROM artistpool
		JOIN artist USING (ARTISTID)
		WHERE ELEMENT = '$element'
		  AND ID = $id
		ORDER BY NAME"))
	) {
		return;
	}
	if ($artists) {
		require_once '_countryflag.inc';
		layout_open_table('default fw hha nobrd');
		foreach ($artists as /* $artistid => */ $artist) {
			/** @noinspection PhpRedundantOptionalArgumentInspection */
			extract($artist, \EXTR_OVERWRITE);
			?><tr<?
			if (!$ACTIVE) {
				?> class="light"<?
			}
			?>><td style="width: 1px; padding: 0 .3em;"><?
			if ($COUNTRYID) {
					show_country_flag_for_artist($ARTISTID, $COUNTRYID, 0, always: true);
			}
			layout_next_cell();
			echo get_element_link('artist', $ARTISTID, $NAME);
			layout_next_cell(class: 'right');
			post_link(
				__($ACTIVE ? 'action:deactivate' : 'action:activate'),
				"/$element/$id/setstatus/$ARTISTID/".($ACTIVE ? 'inactive' : 'active')
			);
			?> <?= MIDDLE_DOT_ENTITY ?> <?
			post_link(__('action:remove_as_error'), "/$element/$id/removeartist/$ARTISTID");
			layout_stop_row();
		}
		layout_close_table();
	}
	layout_close_box();

	?><form<?
	?> accept-charset="utf-8"<?
	?> onsubmit="return submitForm(this);"<?
	?> method="post"<?
	?> action="/<?= $element ?>/<?= $id ?>/artists"><?

	?><div class="block"><?= Eelement_name('artist') ?> <?
	/** @noinspection PhpUndefinedVariableInspection */
	if (!empty($options)
	&&	$option_count > 1
	) {
		generate_name_doubles($options, 'ORDER_NAME', 'artist');
		sort_lineup_artists($options);
		?><select id="artistid" required name="ARTISTID" style="max-width: 40em;"><?
		foreach ($options as $option) {
			# allow dead and stopped;
			$option['ALLOW'] = true;
			show_artist_option($option, $addempty);
		}
		?></select><?
	} else {
		?><input<?
		if (!empty($_POST['NAME'])) {
			?> value="<?= escape_utf8($_POST['NAME']) ?>"<?
		}
		?> type="search" name="NAME" autofocus required class="short"><?
	}
	?> <label><input type="checkbox" name="ACTIVE" value="1" checked /> <?= __('status:active') ?></label> <?
	?><input type="submit" value="<?= __('action:add') ?>" /></div></form><?
}
