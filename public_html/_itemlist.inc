<?php

require_once '_connect.inc';
require_once '_elementnames.inc';

function _itemlist_getname($section = null) {
	return element_plural_name($section ?: $_REQUEST['sELEMENT']);
}

function _itemlist_display_overview_header(): void {
	layout_show_section_header();
}

function _itemlist_display_archive_header(): void {
	layout_show_section_header();
}

function itemlist_is_admin($item) {
	if (have_admin($element = $_REQUEST['sELEMENT'])) {
		return true;
	}
	switch ($element) {
	case 'review':
	case 'interview':
		return have_admin($element.'er');
	}
	return false;
}

function _itemlist_display_menu($item = null) {
	$element = $_REQUEST['sELEMENT'];
	$id = $_REQUEST['sID'];
	$action = $_REQUEST['ACTION'];
	$elementhref = '/'.$element;
	layout_open_menu();
	layout_menuitem(Eelement_name('overview'), $elementhref, !$action);
	layout_menuitem(Eelement_name('archive'), $elementhref.'/archive', $action === 'archive' && empty($_REQUEST['YEAR']));
	if ($element === 'review') {
		layout_menuitem(Eelement_name('rating'), $elementhref.'/top', $action === 'top');
	}
	layout_continue_menu();
	if ($element === 'column') {
		layout_menuitem('Jouw column hier?','/faq/98');
	}
	if ($element === 'news'
	||	$element === 'review'
	) {
		layout_menuitem(__C('action:search'), $elementhref.'/search', $action === 'search');
	}
	require_once '_feed.inc';
	if (SHOW_FEED_ICON) {
		layout_open_menuitem();
		show_feed($element,FEED_ICON);
		layout_close_menuitem();
	}
	layout_close_menu();

	if ($_REQUEST['ACTION'] === 'archive'
	||	!isset($element)
	||	!have_admin([$element, $element.'er'])
	) {
		if ($element === 'report') {
			layout_open_menu();
			layout_menuitem(__C('action:add'), $elementhref.'/form');
			layout_close_menu();
		}
		return;
	}
	if (!$item) {
		layout_open_menu();
		layout_menuitem(__C('action:add'), $elementhref.'/form');
		layout_close_menu();
		return;
	}

	$lock_type = get_lock_type_for_element();

	if (is_locked($lock_type, $id)) {
		?><div class="block"><?
		display_lockinfo($lock_type, $id);
		?></div><?
		return;
	}
	$is_admin = have_admin($element);
	$may_change = $is_admin || ((require_once '_element_access.inc') && may_change_element($element, $id, null, false, $item));
	if ((	!$is_admin
		&&	$item['ACCEPTED']
		)
	||	!$may_change
	) {
		return;
	}

	layout_open_menu();
	$elementhref .= '/'.$id;

	layout_menuitem(__C('action:change'), $elementhref.'/form');
	if ($is_admin) {
		if (!$item['ACCEPTED']) {
			layout_menuitem(__C('action:accept'), $elementhref.'/accept');
		} else {
			layout_menuitem(__C('action:reject'), $elementhref.'/unaccept');
		}
	}
	layout_open_menuitem();
	connect_menuitem();
	layout_close_menuitem();

	if ($element === 'news') {
		layout_menuitem(__C('action:add_poll'), '/poll/newform/news?ELEMENTID='.$_REQUEST['sID']);
	}
	if ($is_admin) {
		layout_continue_menu();
		require_once '_adlink.inc';
		show_adlink($item);
		require_once '_removable.inc';
		show_element_menuitems();
	}
	layout_close_menu();
}

function _item_hash(array $item, ?int $elementid = null, ?string $element = null): int {
	$element ??= $_REQUEST['sELEMENT'];
	return crc32($item['CSTAMP'].'.'.$item['USERID'].'.'.($elementid ?: $item[strtoupper($element).'ID']));
}

function _item_hashed(array $item, ?int $elementid = null, ?string $element = null): bool {
	if (!isset($_GET['ITEMHASH'])) {
		return false;
	}
	return (int)$_GET['ITEMHASH'] === _item_hash($item, $elementid, $element);
}

function _itemlist_display_archive(): void {
	if ($year = $_REQUEST['subID']) {
		if ($month = $_REQUEST['ssID']) {
			_itemlist_display($year, $month);
			return;
		}
		_itemlist_display($year);
		return;
	}
	$element = $_REQUEST['sELEMENT'];
	if ($element === 'agenda') {
		$element = 'party';
	}
	$idname = strtoupper($element).'ID';
	$key = null;
	$itemlist = memcached_rowuse_array(
		$element,
		$element === 'party'
	?	/** @lang MariaDB */ '
		SELECT	FROM_UNIXTIME(STAMP_TZI, "%Y") AS YEAR,
				FROM_UNIXTIME(STAMP_TZI, "%c") AS MONTH,
				COUNT(DISTINCT '.$idname.') AS CNT
		FROM `'.$element.'`
		GROUP BY YEAR, MONTH WITH ROLLUP'

	:	/** @lang MariaDB */ '
		SELECT	FROM_UNIXTIME(PSTAMP, "%Y") AS YEAR,
				FROM_UNIXTIME(PSTAMP, "%c") AS MONTH,
				COUNT(DISTINCT '.$idname.') AS CNT
		FROM `'.$element.'`
		'.($element === 'news' ? join_only_news_for_current_country() : null).'
		WHERE PSTAMP < '.CACHESTAMP.'
		GROUP BY YEAR, MONTH WITH ROLLUP',
		0,
		$key,
		$element === 'party' ? DB_UTC : 0
	);
	if (!$itemlist) {
		return;
	}
	usort($itemlist, static fn(array $a, array $b): int =>
				$b['YEAR' ] <=> $a['YEAR' ]
			?:	$a['MONTH'] <=> $b['MONTH']);

	$first = true;
	$cmonth = null;

	foreach ($itemlist as $item) {
		if (!$item['MONTH']) {
			if ($first) {
				$first = false;
			} else {
				while ($cmonth <= 12) {
					?><tr><td></td><td class="lighter"><?= _month_name($cmonth) ?></td></tr><?
					++$cmonth;
				}
				?></table></div><?
			}
			if (!$item['YEAR']) {
				break;
			}
			?><div class="<?= $element ?> box ib left nopad" style="margin: 0 0 1em 1em;"><?
			?><div class="blackbox"><?
			if ($element !== 'party' && $item['CNT'] < 500) {
				?><a href="/<?= $element ?>/archive/<?= $item['YEAR'] ?>"><?= $item['YEAR'] ?> &rarr;</a><?
			} else {
				echo $item['YEAR'];
			}
			?></div><?
			?><table style="margin: .5em; width: 10em;"><?
			$cmonth = 1;
			continue;
		}
		if ($cmonth !== $item['MONTH']) {
			while ($cmonth < $item['MONTH']) {
				?><tr><td></td><td class="lighter"><?= _month_name($cmonth) ?></td></tr><?
				++$cmonth;
			}
		}
		layout_start_rrow_right();
		echo $item['CNT'];
		?> &middot;&nbsp;<?
		layout_next_cell();
		?><a href="/<?= $element ?>/archive/<?= $item['YEAR'] ?>/<?= $item['MONTH'] ?>"><?= _month_name($item['MONTH']) ?></a><?
		layout_stop_row();
		++$cmonth;
	}
/*	?></div><?*/
}

function _itemlist_display(int $year = 0, int $month = 0, int $userid = 0): void {
	$element  = $_REQUEST['sELEMENT'];
	$itemid	  = "{$element}ID";
	$is_admin = have_admin($element);

	$extra_where = $extra_join = '';
	if (!$is_admin) {
		$extra_where .= '
			AND (	item.ACCEPTED '.
					(	have_user()
					&&	in_array($element, ['review','interview'], true)
					?	' OR BYUSERID = '.CURRENTUSERID
					:	''
					).
			')';
		if ($element === 'news') {
			if (!have_admin('newsad')) {
				$extra_where .= ' AND item.TYPE != "teaserad"';
			}
		}
	}
	$extra_url = null;
	$include_all = false;

	switch ($element) {
	case 'interview':
	case 'column':
		$extra_select = ', TEASER, BYUSERID ';
		break;

	case 'review':
		$extra_select = ', TEASER, BYUSERID/*, RATING*/, RETRO ';
		break;

	case 'news':
		$extra_select = ', TEASER, item.TYPE ';

		if (!have_super_admin()) {
			$extra_where .= ' AND NEWSID != 34290 ';
		}
		$extra_join = join_only_news_for_current_country();
		break;

	case 'report':
		$extra_select = ', BODY, NULL AS TEASER, PARTYID ';
		global $__report_goings;
		if ($__report_goings) {
			$extra_where .= ' AND PARTYID IN ('.implodekeys(', ', $__report_goings).') ';
		}
		if ($userid) {
			$extra_url = "/user/$userid";
			$include_all = true;
		} elseif ($__report_goings) {
			$extra_url = "/{$_REQUEST['ACTION']}";
		}
		if ($userid) {
			$extra_where .= " AND item.USERID = $userid ";
			if (!have_admin('report')) {
				$extra_where .= ' AND NOT ANONYMOUS ';
			}
		}
		if ($_REQUEST['ACTION'] === 'organization'
		&&	($organization_id = $_REQUEST['subID'])
		) {
			$extra_join .= "
			JOIN party USING (PARTYID)
			JOIN connect rpc ON	rpc.MAINTYPE  = 'organization'
							AND rpc.MAINID	  = $organization_id
							AND	rpc.ASSOCTYPE = 'party'
							AND	rpc.ASSOCID   = PARTYID ";
		}
		break;
	default:
		$extra_select = ', TEASER ';
		break;
	}
	if ($year) {
		global $__left_section;
		if ($month) {
			if ($month === 12) {
				$next_month = 1;
				$next_year = $year + 1;
			} else {
				$next_month = $month + 1;
				$next_year = $year;
			}
			$endpart = "
				WHERE item.PSTAMP
				BETWEEN UNIX_TIMESTAMP('$year-$month-01')
					AND UNIX_TIMESTAMP('$next_year-$next_month-01') - 1";

			$__left_section = ' '.MIDDLE_DOT_ENTITY.' '._month_name($month).' '.$year;
		} else {
			$next_year = $year + 1;
			$endpart = "
				WHERE item.PSTAMP
				BETWEEN UNIX_TIMESTAMP('$year-01-01')
					AND UNIX_TIMESTAMP('$next_year-01-01') - 1";
			$__left_section = ' '.$year;
		}
	} else {
		if (false === ($total = memcached_single_if_not_admin($element, $element, "
			SELECT COUNT(DISTINCT $itemid)
			FROM `$element` AS item
			$extra_join ".
			join_only_for_current_country($element, $itemid).'
			WHERE PSTAMP < '.CACHESTAMP.
			$extra_where))
		) {
			return;
		}
		require_once '_pagecontrols.inc';
		$page_controls = new _pagecontrols();
		$page_controls->set_total($total);
		$page_controls->include_all($include_all);
		$page_controls->set_per_page(21);
#		$page_controls->clear_hash();
		$page_controls->show_prev_next(true);
		$page_controls->set_url('/'.$element.$extra_url);
		$page_controls->set_order('PSTAMP');

	}

#	require_once '_resolution.inc';
#	$res = get_resolution();

	# use DISTINCT to make sure join_only_for_current_country get grouped
	$itemlist = db_rowuse_hash($element, "
		SELECT	DISTINCT $itemid, item.USERID, item.PSTAMP, TITLE, item.ACCEPTED
				$extra_select
		FROM `$element` AS item ".
		join_only_for_current_country($element, $itemid).
		$extra_join.(
				isset($endpart)
			?	$endpart.' ORDER BY RAND() LIMIT 50'
			:	' WHERE PSTAMP < '.CURRENTSTAMP.
				$extra_where.
				$page_controls->order_and_limit()
		)
	);
	if (!$itemlist) {
		return;
	}
	number_reverse_asort($itemlist, 'PSTAMP', $itemid);

	if ($element === 'news') {
		$current_day   = 0;
		$current_month = 0;
		$current_year  = 0;
	}
	$idstr = implodekeys(', ', $itemlist);
	require_once '_contests.inc';
	$contests = get_contests($element, $idstr);

	if (!ROBOT) {
		if (false === ($commentcnt = memcached_simple_hash($commentable = $element.'_comment', "
			SELECT ID, COUNT(*)
			FROM $commentable
			WHERE ID IN ($idstr)
			  AND ACCEPTED
			GROUP BY ID",
			TEN_MINUTES))
		) {
			return;
		}
		require_once '_lastseen.inc';
		$last_seen = get_last_seen($element, $idstr);
	}
	if (isset($page_controls)) {
		$page_controls->display_and_store();
	}
	function item_invisible(array $item, bool $is_admin): bool {
		return 	!$is_admin
		&&	(	!$item['ACCEPTED']
			&&	(	!isset($item['BYUSERID'])
				||	$item['BYUSERID'] !== CURRENTUSERID
				)
			||	isset($item['TYPE'])
			&&	$item['TYPE'] === 'teaserad'
			&&	!have_admin('newsad')
			)
		||	$item['PSTAMP'] > CURRENTSTAMP;
	}
	$visi_cnt = null;

	foreach ($itemlist as $id => $item) {
		if (!item_invisible($item, $is_admin)) {
			++$visi_cnt;
		}
	}

	require_once '_favourite.inc';
	$favinfo = new favouriteinfo();

	require_once '_uploadimage.inc';

	$use_table = false;// && !SMALL_SCREEN;
	if ($use_table) {
		?><table><?
	}
	$per_row = 1;//!empty($res[1]) && $res[1] < 1200 ? 2 : 3;
	$cnt = 0;
	$offset = 0;
	$show_auth = $element === 'report' ? 'USERID' : ($element === 'column' ? 'BYUSERID' : '');

	foreach ($itemlist as $id => &$item) {
		$is_on_last = isset($page_controls) && $page_controls->is_on_last_page($offset);

		$color = $element;
		++$offset;
		if (item_invisible($item, $is_admin)) {
			continue;
		}
		if (!$item['TITLE']) {
			$item['TITLE'] = get_element_title($element, $id);
		}
		$onclick = "addclass(this,'light');openLink(event,'".get_element_href($element, $id, $item['TITLE'])."')";

		$lighter = isset($last_seen[$id]) ? ' seenlight' : '';

		if ($use_table) {
			if (!($cnt % $per_row)) {
				if ($cnt) {
					?></td></tr><?
					?><tr><td colspan="<?= 2 * $per_row - 1 ?>" style="height: .4em;"></td></tr><?
				}
				?><tr><?
			} else {
				?></td><td style="width: .4em;"></td><?
			}
			?><td<?
			?> class="<?= $color ?> ptr box vtop hlbox<?= ($item['ACCEPTED'] ? null : ' unaccepted'),$lighter ?>"<?
			?> style="width: <?= (100 / $per_row) - .25 ?>%;" onclick="<?= $onclick ?>"><?
		} else {
			box::open($color.$lighter.' ptr hlbox'.(SMALL_SCREEN ? '' : ' fh').($item['ACCEPTED'] ? '' : ' unaccepted').(isset($last_seen[$id]) ? ' seenitem' : ''));
			box::onclick($onclick);
		}
		if (isset($last_seen[$id])) {
			show_seen_overlay();
		}
		if (!ROBOT
		&&	$is_on_last
		&&	!$page_controls->is_last()
		&&	($last_url = $page_controls->get_last_url())
		) {
			?><div class="isonlast"><?= __('pagecontrols:info:also_on_last', DO_UBB, ['URL' => $last_url]) ?></div><?
		}
		ob_start();
		uploadimage_show_with_fallback($element, $id, flags: UPIMG_NOCHANGE, size: 'thumb');
		if ($img = ob_get_clean()) {
			?><div class="right-float"<?
			if (SMALL_SCREEN) {
				?> style="max-width: 50%;"<?
			}
			?>><?
			?><a<?
			?> href="<?= get_element_href($element,$id,$item['TITLE']) ?>"<?
			?> onclick="return false"><?= $img ?></a><?
			?></div><?
		}
		?><div><?
		?><b><?
		?><a<?
		?> href="<?= get_element_href($element,$id,$item['TITLE']) ?>"<?
		?> onclick="return false"><?= flat_with_entities($item['TITLE'], UBB_UTF8) ?></a></b><?

		if (!empty($item['RETRO'])) {
			?> <small class="nowrap">(<?= element_name('retro_review') ?>)</small><?
		}
		if (!$item['ACCEPTED']) {
			?> <span class="light">(<?= __('field:unaccepted') ?>)</span><?
		}

		show_wins($contests, $id);

		$item['conns'] = get_connections($element, $id);
		ob_start();
		$favinfo->show_stars($item['conns']);
		$item['stars'] = ob_get_flush();

		if (!empty($item['PARTYID'])
		&&	($party = memcached_party_and_stamp($item['PARTYID']))
		) {
			?><br /><?
			?><small><?
			if ($item['TITLE'] !== $party['NAME']) {
				echo escape_utf8($party['NAME']) ?>, <?
			}
			_date_display_tzi($party['STAMP_TZI'], time_span: true);
			?></small><?
		}

		?><br /><?
		if (!isset($endpart)
		&&	isset($current_day)
		) {
			$stamp = memcached_single(['connect', 'party'], "
				SELECT STAMP
				FROM connect JOIN party ON PARTYID = ASSOCID
				WHERE MAINTYPE = '$element'
				  AND MAINID = $id
				  AND ASSOCTYPE ='party'",
				ONE_HOUR
			);
		} else {
			$stamp = $item['PSTAMP'];
		}
		if ($stamp && !$show_auth) {
			?><small class="light"><? _dateday_display($stamp); ?></small><?
		}
		?></div><div><?

		if ($element === 'report'
		||	(	!$item['TEASER']
			&&	isset($item['BODY'])
			)
		) {
			require_once '_bio.inc';
			$item['TEASER'] = get_teaser(remove_ubb($item['BODY'], utf8: true), $same, 500);
		}
		$teaser = $item['TEASER'];
		if ($element === 'report') {
			$teaser = beautify_text($teaser);
		}
		echo make_all_html($teaser, UBB_NO_LINKS | UBB_UTF8, $element.'_teaser', $id);

		if (!empty($commentcnt[$id])) {
			?><small class="light"> <?
			?><a onclick="return openLink(event, this.href, true, true);"<?
			?> href="<?= link_to_comments($element, $id) ?>"><?= $commentcnt[$id] ?></a></small><?
		}
		?></div><?
		if ($show_auth
		&&	$stamp
		&&	empty($item['ANONYMOUS'])
		&&	$item[$show_auth]
		) {
			?><br /><?
			?><div class="small light6 abs" style="bottom: .3em; right: .3em;"><?=
				str_replace('<a ', /** @lang HTML */ '<a onclick="event.stopPropagation(); return true;"',get_element_link('user', $item[$show_auth]));
			?>, <?
				_datetime_display($stamp);
			?></div><?
		}
		if (!$use_table) {
			layout_close_box();
		}
		++$cnt;
	}
	unset($item);
	if ($use_table) {
		?></table><?
	}
	if (!$itemlist) {
		return;
	}
	if (isset($page_controls)) {
		$page_controls->display_stored();
	}
	layout_open_box($element);
	layout_open_table('fw default itemlist hla');
	if ($element === 'report') {
		layout_start_header_row();
		layout_header_cell(Eelement_name('party'));
		layout_header_cell(Eelement_name('date'));
		layout_header_cell(Eelement_name('author'));
		layout_header_cell_right(Eelement_name('publication'));
		layout_stop_header_row();
	}
	foreach ($itemlist as $id => $item) {
		if (item_invisible($item,$is_admin)) {
			continue;
		}
		if (isset($current_day)) {
			[$year, $month, $day] = _getdate($item['PSTAMP']);
			if (!$current_day
			||	$current_day	!== $day
			||	$current_month	!== $month
			||	$current_year	!== $year
			) {
				$current_day	= $day;
				$current_month	= $month;
				$current_year	= $year;
				layout_start_rrow();
				?><b><?= $current_day ?> <?= _month_name($current_month) ?>  <?= $current_year ?></b><?
				layout_stop_row();
			}
		}
		layout_start_rrow(0, $item['ACCEPTED'] ? '' : 'unaccepted');
		?><a href="<?= get_element_href($element, $id, $item['TITLE']) ?>"><?=
			flat_with_entities($item['TITLE'], UBB_UTF8)
		?></a><?
		if (!empty($item['RETRO'])) {
			?> <small class="nowrap">(<?= element_name('retro_review') ?>)</small><?
		}

		show_wins($contests, $id, true);

		if (!isset($item['conns'])) {
			$item['conns'] = get_connections($element,$id);
			$favinfo->show_stars($item['conns']);
		} else {
			echo $item['stars'];
		}
		if (!empty($commentcnt[$id])) {
			?><small class="light"> <?= MIDDLE_DOT_ENTITY ?> <a href="<?= link_to_comments($element,$id) ?>"><?= $commentcnt[$id]; ?></a></small><?
		}
		if ($element === 'report') {
			layout_next_cell();
			if ($item['PARTYID']
			&&	($party = memcached_party_and_stamp($item['PARTYID']))
			) {
				_date_display_tzi($party['STAMP_TZI'], short: true, time_span: true);
			}
		}
		if ($show_auth) {
			layout_next_cell();
			if (empty($item['ANONYMOUS'])
			&&	$item[$show_auth]
			) {
				echo get_element_link('user', $item[$show_auth]);
			}
		}
		if (!isset($current_day)) {
			layout_next_cell(class: 'right');
			_date_display($item['PSTAMP'], '&nbsp;', short: true);
		}
		layout_stop_row();
	}
	layout_close_table();
	layout_close_box();

	if (isset($page_controls)) {
		$page_controls->display_stored();
	}
}
function _itemlist_display_prelist(): void {
	if (!have_admin($element = $_REQUEST['sELEMENT'])
	||	(	$weblog
		=	$element === 'weblog'
		&&	$_REQUEST['ACTION'] === 'user'
		&&	CURRENTUSERID === $_REQUEST['subID']
		)
	) {
		return;
	}
	$itemid = "{$element}ID";
	if (!($prelist = db_rowuse_array(
		$element,'
		SELECT '.$itemid.',TITLE, PSTAMP, ACCEPTED
		FROM `'.$element.'`
		WHERE PSTAMP >= '.CURRENTSTAMP.
		($weblog ? ' AND USERID = '.CURRENTUSERID : null).'
		ORDER BY PSTAMP ASC'))
	) {
		return;
	}
	layout_open_box(empty($weblog) ? $element : memcached_color(CURRENTUSERID));
	layout_box_header(Eelement_plural_name('prepublication'));
	layout_open_table(TABLE_VTOP);
	layout_start_header_row();
	layout_header_cell(Eelement_name('date'),CELL_ALIGN_RIGHT | CELL_RIGHT_SPACE);
	layout_header_cell(Eelement_name('time'),CELL_ALIGN_CENTER | CELL_RIGHT_SPACE);
	layout_header_cell(Eelement_name('title'));
	layout_stop_header_row();
	foreach ($prelist as $prelem) {
		[,,, $h, $m] = _getdate($prelem['PSTAMP']);
		layout_start_rrow(NOWRAP | CELL_ALIGN_RIGHT | CELL_RIGHT_SPACE,!$prelem['ACCEPTED'] ? 'unaccepted' : null);
		_dateday_display($prelem['PSTAMP'],null,false,true);
		layout_next_cell(class: 'center rpad');
		if ($h || $m) {
			printf('%02d:%02d',$h,$m);
		}
		layout_next_cell();
		echo get_element_link($element, $prelem[$itemid], $prelem['TITLE']);
		layout_stop_row();
	}
	layout_close_table();
	layout_close_box();
}
function itemlist_get_authors($item) {
	$authors = memcached_simpler_array_if_not_self_nor_admin(
		$item['USERID'],
		$_REQUEST['sELEMENT'],
		'connect','
		SELECT ASSOCID
		FROM connect
		WHERE MAINTYPE="'.$_REQUEST['sELEMENT'].'"
		  AND MAINID='.$_REQUEST['sID'].'
		  AND ASSOCTYPE="author"'
	);
	if ($authors === false) {
		return false;
	}
	if ($byuserid = getifset($item,'BYUSERID')) {
		$authors[] = $byuserid;
	}
	return $authors;
}

function itemlist_show_publish_info(array $item): void {
	$element = $_REQUEST['sELEMENT'];
	$anon = !empty($item['ANONYMOUS']) || (isset($item['SHOWUSER']) && !$item['SHOWUSER']);
	$author = 'author';
	switch ($element) {
	case 'news':
		$published = true;
		$author = 'editor';
		$authors = [$item['USERID']];
		$authorcnt = 1;
		break;

	case 'report':
		$authors = [$item['USERID']];
		$authorcnt = 1;
		break;

	case 'column':
		$authors = [$item['BYUSERID']];
		$authorcnt = 1;
		break;
	default:
		$authors = itemlist_get_authors($item);
		$authorcnt = count($authors);
		break;
	}
	$byuserid = $element === 'news' ? $item['USERID'] : ($item['BYUSERID'] ?? null);

	$visibles = false;
	foreach ($authors as $tmp_userid) {
		if (!invisible_profile($tmp_userid)) {
			$visibles = true;
			break;
		}
	}

	$anonymous = !$visibles;

	$authoridstr = $authors && (!$anonymous || have_admin($element) || in_array(CURRENTUSERID, $authors)) ? implode(',', $authors) : '';
	$show_name = !$authoridstr && !empty($item['AUTHOR']) ? 'wname:' : '';

	?><div class="<?= $element === 'cartoon' ? 'center' : 'pubinfo' ?> small block"<?
	if (!SMALL_SCREEN) {
		?> style="margin-bottom: 1em;"<?
	}
	?>><?
	$pubinfo = str_replace(
		# shy replacement because of strange behaviour
		[	SOFT_HYPHEN_ENTITY,
			'%DATETIME%',
		],
		[	'',
			_datedaytime_get(($item['PSTAMP'] ?? null) ?: $item['CSTAMP'], null, 0, 'pubdate itemprop="datePublished"', false, true),
		],
		__(isset($published) ? "filler:{$show_name}published_by_on" : "filler:{$show_name}published_and_written_by_on", DO_UBB, [
			'AUTHORS'		=> $authoridstr,
			'AUTHORNAME'	=> $show_name ? $item['AUTHOR'] : null,
			'ANONYMOUS'		=> $anonymous,
		])
	);

	if ($anonymous) {
		?><div itemprop="author" itemscope itemtype="https://schema.org/Organization"><?
			?><meta itemprop="name" content="Partyflock" /><?
		?></div><?
	} elseif ($new_pubinfo = preg_replace_callback('"<a (?<more_attribstr>[^>]*?)>(?<content>.*?)</a>"', static function(array $match) use ($author): string {
		ob_start();
			?><span<?
			?> itemprop="<?= $author ?>"<?
			?> itemscope<?
			?> itemtype="https://schema.org/Person"><?
				?><a itemprop="url" rel="<?= $author ?>"<?= $match['more_attribstr'] ?>><?
					?><span itemprop="name"><?= $match['content'] ?></span><?
				?></a><?
			?></span><?
			return ob_get_clean();
		},	$pubinfo)
	) {
		$pubinfo = $new_pubinfo;
	}

	if (!empty($item['RATING'])) {
		ob_start();
		?>, <?= element_name('rating') ?>: <?
		?><span itemprop="reviewRating" itemscope itemtype="https://schema.org/Rating"><?
		?><meta itemprop="worstRating" content="0" /><?
		?><span itemprop="ratingValue"><?= $item['RATING'] ?></span>/<?
		?><span itemprop="bestRating">100</span><?
		?></span><?
		$pubinfo .= ob_get_clean();
	}
	if (!ROBOT
	&&	have_admin($element)
	&&	($views = 1 + memcached_single_int("{$element}_counter", "
			SELECT CAST(SUM(VIEWS) AS UNSIGNED)
			FROM {$element}_counter
			WHERE {$element}ID = {$_REQUEST['sID']}",
			ONE_DAY))
	) {
		$pubinfo .= ", $views".MULTIPLICATION_SIGN_ENTITY.' '.element_name('view', $views);
	}
	?><span class="ib"><?= str_replace(',', ',</span> <span class="ib">', $pubinfo) ?></span><?
	?></div><?
}

function show_item_teaser(string $element, int $id, array $item): void {
	?><table class="dens nomargin fw"><tr><td style="width: 50%;"><?

	include_style('home');
	layout_open_box($element.' scaled-front');
	ob_start();
	uploadimage_show_with_fallback($element, $id, UPIMG_NOCHANGE, 'thumb');
	if ($img = ob_get_clean()) {
		?><div<?
		?> class="right-float"<?
		if (SMALL_SCREEN) {
			?> style="max-width: 50%;"<?
		}
		?>><?= $img ?></div><?
	}
	?><div><b><?
	$teaser_element = $element.'_teaser';
	echo make_all_html($item['TITLE'], UBB_UTF8, $teaser_element, $id);
	if (!empty($item['SUBTITLE'])) {
		?><br /><?
		?><small class="light"><?= make_all_html($item['SUBTITLE'], UBB_UTF8, $teaser_element, $id) ?></small><?
	}
	?></a></b><?
	if (!$item['ACCEPTED']) {
		?> <span class="light">(<?= __('attrib:not_accepted') ?>)</span><?
	}
	?><br /><?
	?><small class="light"><?= element_name('teaser') ?></small><?
	?></div><?
	?><div><?= make_all_html($item['TEASER'], UBB_NO_LINKS | UBB_UTF8) ?></div><?
	layout_close_box(no_ad: true);

	?></td><td style="width: 336px;"></td></tr></table><?
}
