<?php

declare(strict_types=1);

const BUIENRADAR_KEY = '87a9120ff9284c4faed9d71fcea239da';

const MAXIMUM_DISTANCE_TO_MEASUREMENT_STATION = 20;	# kilometers

require_once '_memcache.inc';

function get_weather_point(int|array $party_or_id): int|false|null {
	if (is_int($party_or_id)) {
		if (!($party = memcached_party_and_stamp($party_or_id))) {
			return false;
		}
	} else {
		$party = $party_or_id;
		assert(is_array($party)); # Satisfy static analysis
	}
	if (!($latitude  = $party['LAT'])
	||	!($longitude = $party['LON'])
	) {
		return null;
	}
	if (null !== ($weather_point_id = memcached_get($weather_point_key = "weatherpoint_for:$latitude,$longitude"))) {
		return $weather_point_id;
	}
/*	# find station within 1.5 km and updated in last day
	if ($weather_point_id = db_single('weatherpoint','
		SELECT ID
		FROM weatherpoint
		WHERE '.distance($latitude, $longitude, 'LATITUDE', 'LONGITUDE').' <= 1.5
		  AND LSTAMP > '.(TODAYSTAMP - ONE_DAY)
	)) { */
	# find station within 10 km and from our archive, since we cannot get locations anymore
	if ($weather_point_id = db_single('weatherpoint','
		SELECT ID
		FROM weatherpoint
		WHERE '.distance($latitude, $longitude, 'LATITUDE', 'LONGITUDE').' <= '.MAXIMUM_DISTANCE_TO_MEASUREMENT_STATION.'
		ORDER BY '.distance($latitude, $longitude, 'LATITUDE', 'LONGITUDE').' DESC
		LIMIT 1'
	)) {
		memcached_set($weather_point_key, $weather_point_id, ONE_HOUR);
		return $weather_point_id;
	}
	return null;
/*	if (!($data = safe_file_get_contents(
		'https://location.buienradar.nl/1.1/location/geo/'.
			"?latitude=$latitude".
			"&longitude=$longitude".
			"&locale=nl-NL".
			'&ak='.BUIENRADAR_KEY))
	) {
		# find nearest weatherpoint
		$weather_point_id = db_single('weatherpoint','
			SELECT ID
			FROM weatherpoint
			ORDER BY '.distance($latitude, $longitude, 'LATITUDE', 'LONGITUDE').' DESC,
				LSTAMP DESC
			LIMIT 1'
		);
		memcached_set($weather_point_key, $weather_point_id ?: false, ONE_HOUR);
		return $weather_point_id;
	}
	if (!($obj = safe_json_decode($data))
	||	empty($obj->id)
	||	empty($obj->location?->lat)
	||	empty($obj->location?->lon)
	||	!db_insert('weatherpoint', "
		REPLACE INTO weatherpoint SET
			ID				= $obj->id,
			LATITUDE		= $latitude,
			LONGITUDE		= $longitude,
			POINT_LATITUDE	= ".round($obj->location->lat, 12).',
			POINT_LONGITUDE	= '.round($obj->location->lon, 12).',
			NAME			= "'.addslashes($obj->name ?? '').'",
			COUNTRYCODE		= "'.addslashes($obj->countrycode ?? '').'",
			STATIONID		= '.($obj->weatherstationid ?? 0).',
			STATIONDISTANCE	= '.($obj->weatherstationdistance ?? 0).',
			LSTAMP			= '.CURRENTSTAMP
		)
	) {
		return false;
	}
	memcached_set($weather_point_key, $obj->id, ONE_HOUR);
	return $obj->id;*/
}

function store_weather(int $partyid, ?int $weather_point_id = null): ?bool {
	if (!($party = memcached_party_and_stamp($partyid))) {
		return false;
	}
	if (!$weather_point_id) {
		if (!($new_weather_point_id = get_weather_point($party))) {
			return $new_weather_point_id;
		}
		$weather_point_id = $new_weather_point_id;
	}
	if (!($data = safe_file_get_contents('https://forecast.buienradar.nl/2.0/forecast/'.$weather_point_id))
	||	!($obj = safe_json_decode($data))
	) {
		return false;
	}
	static $__winds = [
		'Z'		=> 'S',
		'O'		=> 'E',
		'ZW'	=> 'SW',
		'ZO'	=> 'SE',
		'NO'	=> 'NE',
	];
	change_timezone($party['TIMEZONE'] ?? 'Europe/Amsterdam');
	foreach ($obj->days as $day) {
		if (property_exists($day, 'hours')) {
			foreach ($day->hours as $hour) {
				change_timezone('UTC');
				$stamp  = strtotime($hour->datetimeutc);
				change_timezone();

				db_replace('weatherforecast', '
				REPLACE INTO weatherforecast SET
					LSTAMP			= '.CURRENTSTAMP.",
					POINTID			= $weather_point_id,
					STAMP			= $stamp,
					ISDAY			= 0,
					UVINDEX			= NULL,
					MINTEMPERATURE	= NULL,
					SUNRISE			= ".strtotime($day->sunrise).',
					SUNSET			= '.strtotime($day->sunset).',
					CLOUDCOVER		= '.(0 + $hour->cloudcover).',
					ICONCODE		= "'.addslashes($hour->iconcode).'",
					PRECIPITATION	= '.(0 + $hour->precipitation).',
					PRECIPITATIONMM	= '.(0 + $hour->precipitationmm).',
					TEMPERATURE		= '.(0 + $hour->temperature).',
					FEELTEMPERATURE	= '.(0 + $hour->feeltemperature).',
					SUNSHINE		= '.(0 + $hour->sunshine).',
					WINDDIRECTION	= "'.addslashes($__winds[$hour->winddirection] ?? $hour->winddirection).'",
					WINDSPEEDMS		= '.(0 + $hour->windspeed).',
					VISIBILITY		= '.(0 + $hour->visibility)
				);
			}
		}

		change_timezone('UTC');
		$stamp  = strtotime($day->datetimeutc);
		change_timezone();

		db_replace('weatherforecast', '
		REPLACE INTO weatherforecast SET
			LSTAMP			= '.CURRENTSTAMP.",
			POINTID			= $weather_point_id,
			STAMP			= $stamp,
			ISDAY			= 1,
			SUNRISE			= ".strtotime($day->sunrise).',
			SUNSET			= '.strtotime($day->sunset).',
			CLOUDCOVER		= '.(0 + $day->cloudcover).',
			UVINDEX			= '.(property_exists($day,'uvindex') ? (0 + $day->uvindex) : 'NULL').',
			ICONCODE		= "'.addslashes($day->iconcode).'",
			PRECIPITATION	= '.(0 + $day->precipitation).',
			PRECIPITATIONMM	= '.(0 + $day->precipitationmm).',
			FEELTEMPERATURE	= NULL,
			TEMPERATURE		= '.(0 + $day->maxtemp).',
			MINTEMPERATURE	= '.(0 + $day->mintemp).',
			SUNSHINE		= NULL,
			WINDDIRECTION	= "'.addslashes($__winds[$day->winddirection] ?? $day->winddirection).'",
			WINDSPEEDMS		= '.(0 + $day->windspeed).',
			VISIBILITY		= NULL'
		);
	}
	change_timezone();
	return true;
}

const WEATHER_CACHE_VERSION	= 2;

function get_weather(int $partyid, ?int $start_stamp = null, ?int $end_stamp = null): array|false|null {
	if (!($party = memcached_party_and_stamp($partyid))
	||	!($weather_point_id = get_weather_point($party))
	) {
		return false;
	}
	$start_stamp ??= $party['STAMP'];
	  $end_stamp ??= $party['STAMP'] + ($party['DURATION_SECS'] ?: 6 * ONE_HOUR);

	if ($start_stamp < CURRENTSTAMP) {
		$hour_offset = floor((CURRENTSTAMP - $start_stamp) / ONE_HOUR);
		$start_stamp += $hour_offset * ONE_HOUR;
	}

	$key = 'event_weather:'.WEATHER_CACHE_VERSION.":$weather_point_id:$start_stamp-$end_stamp";

	if (false !== ($weather = isset($_REQUEST['NOMEMCACHE']) ? false : memcached_get($key))) {
		return $weather;
	}
	if ($end_stamp > CURRENTSTAMP + 14 * ONE_DAY) {
		# no weather info this long ahead
		return null;
	}

	# calculate hours_left, for how many hours we need a forecast

	$time_left = $end_stamp - $start_stamp;
	if (CURRENTSTAMP > $start_stamp) {
		$time_left -= CURRENTSTAMP - $start_stamp;
	}
	if (!($hours_left = floor($time_left / ONE_HOUR))) {
		# for events shorter than 1 hour
		$hours_left = 1;
	}

	static $tries = 1;

	retry:

	if (!($all_forecasts = db_rowuse_array('weatherforecast', "
		SELECT *
		FROM weatherforecast
		WHERE POINTID = $weather_point_id
		  AND STAMP >= ".CURRENTSTAMP
	))) {
		if ($all_forecasts === false) {
			return false;
		}
		store:
		if (!store_weather($partyid, $weather_point_id)) {
			memcached_set($key, false, 4 * ONE_HOUR);
			return false;
		}
		if ($tries) {
			--$tries;
			goto retry;
		}
		return false;
	}
	$forecasts = [];
	foreach ($all_forecasts as $forecast) {
		if ($forecast['LSTAMP'] < CURRENTSTAMP - ONE_DAY
		&&	$tries
		) {
			goto store;
		}
		if ($forecast['ISDAY']) {
			if ($forecast['STAMP'] >= $start_stamp - 2 * ONE_DAY
			&&	$forecast['STAMP'] <=   $end_stamp + 2 * ONE_DAY
			) {
				$forecast['DATE'] = _date_get($forecast['STAMP']);
			}
		} elseif (
			$start_stamp <= $forecast['STAMP']
		&&	  $end_stamp >= $forecast['STAMP']
		) {
			$forecasts[] = $forecast;
		}
	}
	if ($hours_left > count($forecasts)) {
		memcached_set($key, null, 4 * ONE_HOUR);
		return null;
	}
	$result = [
		'start_stamp'	=> $start_stamp,
		'end_stamp'		=> $end_stamp,
	];
	static $spec = [
		'temperature'		=> 'C',
		'feeltemperature'	=> 'C',
		'cloudcover'		=> '%',
		'sunshine'			=> '%',
		'iconcode'			=> 'string',
		'precipitation'		=> '%',
		'precipitationmm'	=> 'mm',
		'winddirection'		=> 'N,S,W,E,NW,NE,SW,SE',
		'windspeedms'		=> 'ms',
		'visibility'		=> 'm',
	];
	$crunches = [];
	foreach ($forecasts as $forecast) {
		foreach ($spec as $ndx => $unit) {
			$crunches[$ndx][] = $forecast[strtoupper($ndx)];
		}
	}
	if (!$crunches) {
		mail_log('no weather crunches', get_defined_vars());
		return null;
	}
	$forecast_count = count($forecasts);
	$median_two		= !($forecast_count % 2);
	$forecast_half	= $forecast_count >> 1;

	foreach ($crunches as $ndx => $vals) {
		# average:
		# $crunch = array_sum($vals) / count($vals);

		# median:
		sort($vals, \SORT_NUMERIC);

		switch ($ndx) {
		case 'iconcode':
		case 'winddirection':
			# get most appearing value
			$appearances = array_count_values($vals);
			arsort($appearances);
			$crunch = array_key_first($appearances);
			break;
		default:
			$crunch = $median_two ? ($vals[$forecast_half - 1] + $vals[$forecast_half]) / 2: $vals[$forecast_half];
			break;
		}
		$result[$ndx] = $crunch;
		if ($ndx === 'temperature') {
			$result['temperature_min'] = (float)min($vals);
			$result['temperature_max'] = (float)max($vals);
		}
	}
	memcached_set($key, $result, ONE_DAY);
	return $result;
}

function show_temperature_value(float $temperature): void {
	if (!($local_info = get_locale_info())) {
		$local_info = [
			'decimal_point'	=> '.',
			'thousands_sep'	=> '&thinsp;',
		];
	}
	$int_temperature = (int)$temperature;
	$decimal_part = $temperature - $int_temperature;
	/** @noinspection OffsetOperationsInspection */
	echo number_format(
		(float)$temperature,
		$decimal_part ? 1 : 0,
		$local_info['decimal_point'],
		$local_info['thousands_sep']
	);
}

function show_temperature(array $weather): void {
	if ((/* $time_diff		  = */ $weather['end_stamp'] - $weather['start_stamp']) < 6 * ONE_HOUR
	&&	(/* $temperature_diff = */ $weather['temperature_max'] - $weather['temperature_min']) <= 2
	) {
		show_temperature_value($weather['temperature']);
		?> &deg;C<?
	} else {
		show_temperature_value($weather['temperature_min']);
		?> &ndash; <?
		show_temperature_value($weather['temperature_max']);
		?> &deg;C<?
	}
}
