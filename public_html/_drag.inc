<?php

function drag_store(?string $element = null) {
	open_drag($element, close: 2);
}

function close_drag(?string $element = null): void {
	open_drag($element, close: true);
}

function open_drag(
	?string  $element			= null,
	bool|int $close				= false,
	bool	 $auto_submit		= false,
	string	 $execute_on_drop	= ''
): void {
	$element ??= $_REQUEST['sELEMENT'];

	static $__opened	  = null;
	static $__store_shown = null;

	if ($close) {
		if (!isset($__opened[$element])) {
			return;
		}
		if (!isset($__store_shown[$element])) {
			$__store_shown[$element] = true;
			switch ($element) {
			case 'location':
			case 'organization':
			case 'artist':
				# easy solution to include js here (instead of loading dynamically)
				include_js('js/processlist');
				?><div id="eventliststore"></div><?
			}
		}
		if ($close === 2) {
			return;
		}
		unset($__opened[$element], $__store_shown[$element]);
		?></div><?
		return;
	}
	switch ($element) {
	default:
		error_log('no drag known for '.$element);
		return;
	case 'organization':
	case 'location':
		if (!have_admin('party')) {
			return;
		}
		include_js('js/form/eventlist');
	case 'uploadimage':
	case 'artist':
	case 'contest':
	case 'interview':
	case 'news':
	case 'review':
	case 'party':
	case 'stream':
		include_js('js/form/upload');
		include_js('js/form/drag');
		?><div<?
		?> class="dragcatch" <?
		?> data-element="<?= $element ?>"<?
		if ($element !== 'uploadimage'
		&&	!empty($_REQUEST['sID'])
		) {
			?> data-id="<?= $_REQUEST['sID'] ?>"<?
		}
		if ($is_form
		=	!empty($_REQUEST['ACTION'])
		&&	in_array($_REQUEST['ACTION'], ['form', 'register'])
		) {
			?> data-form="true"<?
		}

		if ($auto_submit) {
			?> data-auto-submit="true"<?
		}

		?> ondrop="<?
			if ($execute_on_drop) {
				echo $execute_on_drop ?>; Pf.genericDragLeave(event, this);<?
			}
			?>Pf.genericDragDrop(event, this);"<?
		?> ondragstart="Pf.genericDragStart(event, this);"<?
		?> ondragover="Pf.genericDragOver(event, this);"<?
		?> ondragleave="Pf.genericDragLeave(event, this);"<?
		?> ondragend="Pf.genericDragEnd(event, this);"><?

		if ($element !== 'uploadimage') {
			?><div<?
			?> id="uploadimage-form-store"<?
			?> data-element="<?= $_REQUEST['sELEMENT'] ?>"<?
			?> data-id="<?= $_REQUEST['sID'] ?>"<?
			?>></div><?
		}
		if ($element === 'party') {
			include_js('js/form/fbpartyparse');
			?><div class="relative"><?
			?><div id="fbidentical" class="hidden abs z2" style="font-size:200%;top:-20px;left:-15px">&#128527;</div><?
			if ($is_form) {
				?><div id="fbfillcnt" class="hidden fbfillC bigger circle abs z2" style="top:-13px">0</div><?
				?><div id="fboptscnt" class="hidden fboptsC bigger circle abs z2" style="top:-13px;left:30px">0</div><?
			}
			?></div><?
		}
		break;
	}
	$__opened[$element] = true;
}
