<?php

require_once '_sort.inc';

class _directmessagelist {
	private function get_dmsginfos($me,$other) {
		if (!empty($this->dmsginfos)) {
			return $this->dmsginfos;
		}
		require_once 'defines/directmessage.inc';
		require_once '_ignores.inc';
		
/*		$ignored_by =
			$me == 'FROM'
		?	memcached_simpler_array('ignorelist','
				SELECT USERID
				FROM ignorelist
				WHERE FLAGS&'.IGNORE_PRIVATE_MESSAGES.'
				  AND IGNOREID='.CURRENTUSERID
			)
		:	null;*/
		
		$ignored_by = null;
		
		# use pages for 100+ distinct userids?
		
		$this->dmsginfos = memcached_rowuse_hash(['directmessage','dmsgtype'],'
			SELECT	'.$other.'_USERID AS USERID,
				MAX(CSTAMP) AS MAX_CSTAMP,
				MAX(MESSAGEID) AS MAX_MESSAGEID,
				COUNT(*) AS CNT
			FROM directmessage
			LEFT JOIN dmsgtype USING (MESSAGEID)
			WHERE '.($me === 'TO' || empty($ignored_by) ? 'READM=0' : '(READM = 0 OR READM = 1 AND TO_USERID IN ('.implode(',',$ignored_by).') AND FLAGS & '.DMSG_IGNORED.')').'
			  AND '.($me === 'TO' ? 'TO_DELETED = 0' : 'FROM_DELETED = 0').'
			  AND '.$me.'_USERID = '.CURRENTUSERID.
			(isset($this->startfrom) ? ' AND CSTAMP>'.$this->startfrom : null).'
			GROUP BY '.$other.'_USERID
			ORDER BY MAX_MESSAGEID DESC',
			20
		);
		if ($this->dmsginfos === false) {
			return false;
		}
		return true;
	}
	function amount() {
		if (empty($this->dmsginfos)) {
			return 0;
		}
		$amount = 0;
		foreach ($this->dmsginfos as $dmsginfo) {
			$amount += $dmsginfo['CNT'];
		}
		return $amount;
	}
	function query_incoming() {
		$this->incoming = true;
		$this->totalmsgcnt = 0;
		return $this->get_dmsginfos('TO','FROM');
	}
	function query_outgoing() {
		$this->incoming = false;
		return $this->get_dmsginfos('FROM','TO');
	}
	function display_header() {
		layout_start_header_row();
		layout_start_header_cell(CELL_ALIGN_LEFT);
		echo Eelement_name($this->incoming ? 'sender' : 'addressee');
		layout_next_header_cell_right();
		echo SMALL_SCREEN ? '#' : __C('field:unread');
		layout_next_header_cell_right();
		echo __C('field:last_message');
		layout_stop_header_cell();
		layout_stop_header_row();
	}
	function display() {
		if (empty($this->dmsginfos)) {
			return;
		}
		require_once '_notify.inc';
		require_once '_visibility.inc';
		reset($this->dmsginfos);
		list($userid,$dmsg) = keyval($this->dmsginfos);
		notify_register_seen(NOTIFY_MSG,$dmsg['MAX_MESSAGEID']);
		
		layout_open_table('default fw');
		$this->display_header();
		foreach ($this->dmsginfos as $userid => $dmsginfo) {
			$online = is_online($userid);

			?><tr><?
			?><td<?
			if ($userid && $online) {
				?> class="online"<?
			}
			?>><?
			// USER
			?><a href="/msg/<?= $this->incoming ? 'incoming' : 'outgoing';
			?>/<?= $userid;
			?>"><?= $userid ? escape_specials(memcached_nick($userid)) : __('field:unknown');
			?></a><?
			// UNREAD CNT
			layout_next_cell(class: 'right');
			echo $dmsginfo['CNT'];
			// LAST STAMP
			layout_next_cell(class: 'right');
			_datetime_display($dmsginfo['MAX_CSTAMP']);
			layout_stop_row();
		}
		layout_close_table();
	}
	function start_from($stamp) {
		$this->startfrom = $stamp;
	}
}
