<?php

function get_front_and_back_flyer(
	int|array			$party,
	string 				$size = '',
	int					$flags = 0,
	string|array|null	$orientation_preference = null,
): array {
	if (is_array($party)) {
		$partyid = $party['PARTYID'];
	} else {
		$partyid = $party;
		if (!($party = memcached_party_and_stamp($party))) {
			return [false, false, false, false, false, false];
		}
	}

	if (!isset($partyid)) {
		mail_log('get_front_back_flyer with null partyid', item: get_defined_vars());
	}

	require_once '_uploadimage.inc';
	$front_flyer = uploadimage_get('party', $partyid, $size, $flags, $orientation_preference);
	$back_flyer  = uploadimage_get('party2', $partyid, $size, $flags, $orientation_preference);
	$have_front = have_uploadimage($front_flyer);
	$have_back  = have_uploadimage($back_flyer);
	$have_alternative =
	$alternative_flyer = false;

	if (!$have_front
	&&	!$have_back
	) {
		$tryorgs = [];
		require_once '_organizationstuff.inc';
		if (($stuff = get_organization_stuff($party))
		&&	($orgs = $stuff[0])
		) {
			$cporgs = $orgs;
			$parentorgs = getifset($orgs, null);

			unset($cporgs[null]);

			if (count($cporgs) === 1) {
				# have only one parent for one or more childeren
				# choose child if it is the only one
				[, $children] = keyval($cporgs);
				if (count($children) === 1) {
					[$chosenorgid] = keyval($children);
					$tryorgs[] = $chosenorgid;
				}
			}
			if (!$cporgs
			||	$parentorgs
			) {
				if (count($parentorgs) === 1) {
					# 1 main org, try it
					[$chosenorgid] = keyval($parentorgs);
					$tryorgs[] = $chosenorgid;
				} else {
					# more main orgs, choose best match
					# TEST: Try without cleaning for a while
					#$party_name = clean_org_name($party['NAME'].($party['SUBTITLE'] ? ' '.$party['SUBTITLE'] : ''));
					$party_name = $party['NAME'].($party['SUBTITLE'] ? ' '.$party['SUBTITLE'] : '');
					$best_pct = 0;
					$best_orgid = 0;
					foreach ($parentorgs as $orgid) {
						# TEST: Try without cleaning for a while
						# $name = clean_org_name(get_element_title('organization', $orgid));
						$name = get_element_title('organization', $orgid);
						similar_text($party_name, $name, $pct);
						if ($pct > $best_pct) {
							$best_pct = $pct;
							$best_orgid = $orgid;
						}

					}
					$tryorgs[] = $best_orgid;
				}
			}
		}
		if (!empty($wholes)) {
			$tryorgs = array_merge($tryorgs, $wholes);
		}
		if (isset($tryorgs)) {
			foreach ($tryorgs as $chosenorgid) {
				$alternative_flyer = uploadimage_get('organization', $chosenorgid, $size, $flags, $orientation_preference
				);
				if (!have_uploadimage($alternative_flyer)) {
					$alternative_flyer = false;
					continue;
				}
				if ($alternative_flyer) {
					break;
				}
			}
		}

		if (!$alternative_flyer
		&&	!empty($party['LOCATIONID'])
		&&	($alternative_flyer = uploadimage_get('location', $party['LOCATIONID'], $size, $flags, $orientation_preference
			))
		&&	!($have_alternative = have_uploadimage($alternative_flyer))
		) {
			$alternative_flyer = false;
		}
	}
	return [
		$front_flyer,
		$back_flyer,
		$alternative_flyer,
		$have_front,
		$have_back,
		$have_alternative
	];
}
