<?php

declare(strict_types=1);

const NEED_CHECKUP_AFTER = ONE_YEAR;

function set_checkup(string $element, int $id): bool {
	return db_replace('checkup', '
		REPLACE INTO checkup SET
			USERID		 = '.CURRENTUSERID.",
			ELEMENT		 = '$element',
			ID			 = $id,
			LAST_CHECKUP = ".CURRENTSTAMP);
}

/**
 * @return ?bool - true if checked recently, false if not checked, null if never checked.
 */
function had_recent_checkup(string $element, int $id): ?bool {
	return db_single_bool('checkup', /** @lang MariaDB */'
		SELECT IF(LAST_CHECKUP > '.CURRENTSTAMP - NEED_CHECKUP_AFTER.", b'1', b'0')
		FROM checkup
		WHERE ELEMENT = '$element'
		  AND ID = $id
		  AND LAST_CHECKUP > ".CURRENTSTAMP - NEED_CHECKUP_AFTER
	);
}

function show_recent_checkup_mark(string $element, int $id): void {
	if (null !== ($recent = had_recent_checkup($element, $id))) {
		?> <span class="checkup<?
		if (!$recent) {
			?> light colorless<?
		}
		?>"><?= WHITE_HEAVY_CHECK_MARK_ENTITY ?></span><?
	}
}

function act_checkup(): int {
	if (!($element = require_element ($_POST, 'ELEMENT', ['artist', 'location', 'organization']))
	||	!($id	   = require_idnumber($_POST, 'ID'))
	) {
		return 400;
	}
	global $currentuser;
	$currentuser ??= new _currentuser(TINY_USER | SKIP_SETTINGS);
	if (!require_admin($element)) {
		return 403;
	}
	if (!set_checkup($element, $id)) {
		return 500;
	}
	return 200;
}

function show_checkup_mark(): void {
	$element = $_REQUEST['sELEMENT'];
	$id		 = $_REQUEST['sID'];
	if (false === ($last_checkup = db_single_int('checkup', "
		SELECT LAST_CHECKUP
		FROM checkup
		WHERE ELEMENT = '$element'
		  AND ID = $id
		ORDER BY LAST_CHECKUP DESC
		LIMIT 1"))
	) {
		return;
	}
	$checked = __('status:exhaustively_checked');

	?> <span class="checkup <?
	if (!$last_checkup
	||	 $last_checkup < CURRENTSTAMP - NEED_CHECKUP_AFTER
	) {
		?>not-<?
	}
	?>checked" onclick="
		/** @lang JavaScript */
		<? if (!HOME_OFFICE) { ?>
			if (!confirm('<?= __('checkup:question:did_you_check_exhaustively_TEXT', IS_JAVASCRIPT) ?>')) {
				return;
			}
		<? } ?>
		do_inline('POST', '/set_checkup.act', 'ACT', function(req) {
			if (req.status === <?= http_status::OK->value ?>) {
				event.target.className = 'checkup now-checked';
				event.target.innerHTML = '<?= WHITE_HEAVY_CHECK_MARK_ENTITY ?> <?= $checked ?>!';
			}
		}, 'ELEMENT=<?= $element ?>&amp;ID=<?= $id ?>');
	"><?
	echo $checked;
	if ($last_checkup) {
		if ((int)((CURRENTSTAMP - $last_checkup) / ONE_DAY)) {
			?>: <? show_simple_diff($last_checkup);
		} else {
			?>!<?
		}
	} else {
		?>?<?
	}
	?></span><?
}
