<?php

require_once '_banner.inc';
require_once '_domain.inc';
require_once '_profilefilter.inc';
require_once '_adcount.inc';
require_once 'defines/ad.inc';

# The ads with class great-thing are horizontal ads that might be too near each other.
# If that is the case, js/greatly.js hides the ones that are to near their predecessors.
# FIXME: Currently the content is always loaded and thus the 'hit' counted.
#		 These ads should store the content source in a separate value and only
#		 make the browser load the content if the banner is not too near abn other.

const MIN_LEADERBOARD_WIDTH 	= 400;
const AD_PLACEHOLDER			= '<div class="invisible great-thing"></div>';
const ADS_CACHE_PREFIX			= 'ads:v3:';

# const DEBUG_AD = 'log';	# log only
# const DEBUG_AD = true;	# log and error_log
const DEBUG_AD = false;		# no debugging

function show_ad_placeholder_tbody(int $colcnt): bool {
	return show_ad_placeholder(which: 'tbody', colcnt: $colcnt);
}

function show_ad_placeholder_tr(int $colcnt): bool {
	return show_ad_placeholder(which: 'tr', colcnt: $colcnt);
}

function show_ad_placeholder(?bool $set = null, ?string $which = null, ?int $colcnt = null): bool {
	static $__show_placeholder = false;
	if ($set) {
		return $__show_placeholder = true;
	}
	if (!no_banners()) {
		if (!$which) {
			echo AD_PLACEHOLDER;
		} elseif ($which === 'tbody') {
			?><tbody class="sort-ignore"><?
			?><tr><td class="great-span" colspan="<?= $colcnt ?>"><?= AD_PLACEHOLDER ?></td></tr><?
			?></tbody><?
		} else {
			?><tr><td class="great-span" colspan="<?= $colcnt ?>"><?= AD_PLACEHOLDER ?></td></tr><?
		}
		increase_ad_placeholder();
	}
	return $__show_placeholder;
}

function remove_ad_placeholders(string &$body): string {
	return $body = preg_replace(
			'!'.
				'(?:<tbody class="sort-ignore">)?'.
				'(?:<tr><td class="great-span" colspan="\d+">)?'.
					AD_PLACEHOLDER.
				'(?:</td></tr>)?'.
				'(?:</tbody>)?'.
			'!',
			'',
			$body
	);
}

function get_ad_placeholder_count(): int {
	return increase_ad_placeholder(true);
}

function increase_ad_placeholder(bool $get = false): int {
	static $__placeholder_count = 0;
	if ($get) {
		return $__placeholder_count;
	}
	return ++$__placeholder_count;
}


function fill_ad_placeholders(string &$body): void {
	if (no_banners()
	||	!($fillcnt = get_ad_placeholder_count())
	) {
		return;
	}
	$ads = [];
	$prefer_ads = [];
	$keeptop_ads = [];
	$ndx = 0;
	$add_ad = static function(array &$ads, string $type, array $arg) use (&$ndx): void {
		if (!isset($arg[0])) {
			$arg = [$arg];
		}
		$keeptop = false;
		$cpct = 0;
		foreach ($arg as $ad) {
			if (!$keeptop
			&&	(	$ad['LATE']
				||	$ad['KEEPTOP']
				)
			) {
				$keeptop = true;
			}
			$cpct = max(
					$cpct,
						$ad['__M']['cpct_after_filter']
					?:	$ad['__M']['cpct']
					?:	$ad['CPCT']);
			$ads[] = [$keeptop, $cpct, $type, $ndx, $arg];
		}
		++$ndx;
	};

	if (SMALL_SCREEN
 	&&	($mobilead = obtain_ad(ADPOS_SMALL_SCREEN))
	) {
		$add_ad($prefer_ads, 'mobile', $mobilead);
		$pick_second = true;
		while ($mobilead = obtain_ad(ADPOS_SMALL_SCREEN, min_missing: 50, min_missing_mult: .1)) {
			if ($pick_second) {
				$add_ad($prefer_ads, 'mobile', $mobilead);
				$pick_second = false;
			} else {
				$add_ad($ads, 'mobile', $mobilead);
			}
		}
	}
	while ($leaderboard = obtain_ad(ADPOS_TOPBOTTOM, min_missing: 50, min_missing_mult: .1)) {
		$add_ad($ads, 'leaderboard', $leaderboard);
	}
	if (($menuad1 = obtain_ad(ADPOS_MENUMINI, min_missing: 50, min_missing_mult: .1))
	&&	($menuad2 = obtain_ad(ADPOS_MENUMINI, min_missing: 50, min_missing_mult: .1))
	) {
		$menuad_row = [$menuad1, $menuad2];
		if ($menuad3 = obtain_ad(ADPOS_MENUMINI, min_missing: 50, min_missing_mult: .1)) {
			$menuad_row[] = $menuad3;
		}
		$add_ad($ads, 'menu', $menuad_row);
	}
	if (SMALL_SCREEN
	&&	$_REQUEST['sELEMENT'] !== 'home'
	&&	($flyer_one = obtain_ad(ADPOS_HOMERECTANGLE, min_missing: 50, min_missing_mult: .1))
	&&	($flyer_two = obtain_ad(ADPOS_HOMERECTANGLE, min_missing: 50, min_missing_mult: .1))
	) {
		$flyers = [$flyer_one, $flyer_two];
		if ($flyer_three = obtain_ad(ADPOS_HOMERECTANGLE, min_missing: 50, min_missing_mult: .1)) {
			$flyers[] = $flyer_three;
			if ($flyer_four = obtain_ad(ADPOS_HOMERECTANGLE, min_missing: 50, min_missing_mult: .1)) {
				$flyers[] = $flyer_four;
			}
		}
		$add_ad($ads, 'flyer', $flyers);
	}
	if (!$ads) {
		remove_ad_placeholders($body);
		return;
	}
/*	usort($ads, function($one, $two) {
		$keeptop_one = $one[0] ? 0 : 1;
		$keeptop_two = $two[0] ? 0 : 1;
		$diff_keeptop = $keeptop_one - $keeptop_two;
		if ($diff_keeptop) {
			return $diff_keeptop;
		}
		$cpct_one = $one[1];
		$cpct_two = $two[1];
		$diff_cpct = $cpct_two - $cpct_one;
		if ($diff_cpct) {
			return $diff_cpct < 0 ? -1 : 1;
		}
		return 0;
	});*/
	foreach ($ads as [$keeptop, $cpct, $type, $ndx /* , $ad */]) {
		if ($keeptop) {
			$keeptop_ads[] = [$keeptop, $cpct, $type, $ndx, $ads];
		}
	}
	safe_shuffle($ads);
	safe_shuffle($prefer_ads);
	safe_shuffle($keeptop_ads);

	foreach ($keeptop_ads + $prefer_ads + $ads as [/* $keeptop */, /* $cpct */, $type, $ndx, $tmpads]) {
		ob_start();
		switch ($type) {
		case 'mobile':
			?><div class="center block"><?
			display_ad($tmpads[0], for_embed: true);
			?></div><?
			break;

		case 'leaderboard':
			foreach ($tmpads as $tmpad) {
				?><div class="center block"><?
				display_ad($tmpad, for_embed: true);
				?></div><?
			}
			break;

		case 'menu':
			?><div class="center block"><?
			$width = isset($tmpads[2]) ? '33' : '50';
			foreach ($tmpads as $tmpad) {
				?><div class="ib" style="width: <?= $width ?>%;"><?
				display_ad($tmpad, for_embed: true);
				?></div><?
			}
			?></div><?
			break;

		case 'flyer':
			# padding left and right is 1%
			# divide it evenly among the three spaces (left, middle, right)
			$multiplier = count($tmpads);
			?><div class="block"><?
			foreach ($tmpads as $tmpad) {
				?><div class="ib center" style="width: <?= 100 / $multiplier ?>%;"><?
				?><div class="ib" style="margin: 0 <?= $multiplier / 3 ?>% 0 <?= $multiplier * 2 / 3 ?>%;"><?
				display_ad($tmpad, for_embed: true);
				?></div><?
				?></div><?
			}
			?></div><?
			break;
		}
		$banner_html = ob_get_clean();

		?><meta class="great-horizontal" name="greatly" content="<?= escape_utf8($banner_html) ?>" /><?

		if (!--$fillcnt) {
			break;
		}
	}
	include_js('js/greatly');
}

function show_right_towers(): bool {
	static	$__show_right_towers = !(
			no_banners()

		||	SMALL_SCREEN

		# NOTE: no right towers for entire sections:
		||	in_array($_REQUEST['sELEMENT'], [
				'ad',
				'banner',
				'camera',
				'contact',
				'disallowuser',
				'home',
				'ip',
				'invoice',
				'newsad',
				'pixel',
				'promo',
				'relation',
				'rights',
				'test',
				'ticket',
			],	true)

		||	$_REQUEST['sELEMENT'] === 'user'
		&& 	$_REQUEST['ACTION'] === 'last'

		||	$_REQUEST['sELEMENT'] === 'city'
		&&	in_array($_REQUEST['ACTION'], [
				'addinvalidate',
				'autoreplace',
				'showunknown',
				'userchange',
			], 	true)

		||	$_REQUEST['sELEMENT'] === 'video'
		&&	$_REQUEST['ACTION'] === 'inactive'

		||	$_REQUEST['sELEMENT'] === 'agenda'
		&&	in_array($_REQUEST['ACTION'], [
				'needupdate',
				'new'
			],	true)

		||	$_REQUEST['sELEMENT'] === 'party'
		&&	(	$_REQUEST['ACTION'] === 'form'
			# ||	$_REQUEST['ACTION'] === 'single'
			||	$_REQUEST['ACTION'] === 'statistics'
			)

		||	!($res = get_resolution())
		||	$res['window_width'] < 920
	);
	return $__show_right_towers;
}

function show_double_leaderboards(): bool {
	static	$__show_double_leaderboards =
			($res = get_resolution())
		&&	$res['window_width'] >= (2 * MIN_LEADERBOARD_WIDTH + 20);
	return $__show_double_leaderboards;
}

function flush_adlist(int $position): void {
	memcached_delete($cache = ADS_CACHE_PREFIX.$position);
	memcached_delete($cache.'_2');
}

function flush_running_ads(): void {
	memcached_delete('running_ads');
	memcached_delete('sections_for_ads');
	memcached_delete('cities_for_ads');
}

function debug_ad(?string $errstr = null, ?int $position = null): void {
	static $__position = 'unknown';
	static $__logs = [];
	if ($position) {
		$__position = $position;
		return;
	}
	if ($errstr) {
		if (DEBUG_AD === true) {
			error_log(ad_position_name($__position).': '.$errstr);
		}
		$__logs[$position] = $errstr;
		return;
	}
	error_log_r($__logs, 'debug_ad');
}

function no_banners(): bool {
 	require_once '_spider.inc';
	require_once '_globalsettings.inc';
	require_once '_identity.inc';
	static $__no =
		ADS_OFFLINE
	||	!CURRENTIDENTID
	||	ROBOT
	||	partyflock_read_only()
	||	(	# disabled ads for admins in some sections admins use a lot, to not distract
			# from their work tasks:
			have_admin()
		&&	($element = $_REQUEST['sELEMENT'] ?? null)
		&&	in_array($element, ['ad', 'banner', 'contact'], strict: true)
		)
	||	($element = $_REQUEST['sELEMENT'] ?? null)
	&&	$element === 'user'
	&&	in_array($_REQUEST['ACTION'], ['login', 'setuser'], strict: true)
	&&	empty($_COOKIE)
	||	$element === 'party'
	&&	$_REQUEST['ACTION'] === 'statistics';
	return	$__no;
}

function obtain_ad(
	int		$position,
	int		$max_height			= 0,
	int		$min_missing		= 0,
	float	$min_missing_mult	= 0,
): array|false|null {
	if (no_banners()) {
		return false;
	}

	require_once '_banner.inc';

	DEBUG_AD && debug_ad(position: $position);

	if ($adids = have_number_list($_GET, 'ADID') ?: false) {
		static $__done;
		$ok_ads = [];
		foreach ($adids as $adid) {
			if (!isset($__done[$adid])) {
				$ok_ads[$adid] = $adid;
			}
		}
		if (!$ok_ads) {
			return null;
		}
		require_once '_adlink.inc';
		if ($ad = db_single_assoc(['ad','adinfo','banner'],'
			SELECT	ad.ADID, POSITION, STARTSTAMP, STOPSTAMP,
					1 AS FORCED,
					1 AS FREE,
					0 AS MISSING,
					0 AS TOO_LATE,
					0 AS TAKEOVER,
					ad.BANNERID,
					FREQCAP, FREQCAP_DAY, adinfo.URL,
					banner.TYPE, WIDTH, HEIGHT, SIZETYPE, BG, ADBOX, PARTYID
			FROM ad
			JOIN adinfo USING (ADID)
			LEFT JOIN banner USING (BANNERID)
			WHERE ADID IN ('.implode(', ', $ok_ads).')
			  AND POSITION = '.$position.
			  and_where_adhash('ad', 'adinfo.RELATIONID'))
		) {
			$__done[$ad['ADID']] = true;
			if ($ad['PARTYID']) {
				$ad = [$ad['PARTYID'] => $ad];
			}
			return $ad;
		}
	}
	if (hide_ad_at($position)) {
		# error_log('hide_ad_at '.$position.' for user '.CURRENTUSERID);
		return false;
	}
	if ($position === ADPOS_PARTYLIST) {
		$key = ADS_CACHE_PREFIX.CURRENTDOMAIN.':'.$position;
		if ($ads = memcached_get($key)) {
			return $ads;
		}
		if (!memcached_not_found()) {
			# memcache failure
			return false;
		}
		$currentstamp = CURRENTSTAMP;
		if (false === ($ads = db_rowuse_hash('ad', "
			SELECT SQL_NO_CACHE PARTYID, ADID, POSITION
			FROM ad
			WHERE POSITION = $position
			  AND ACTIVE
			  AND STARTSTAMP <= $currentstamp
			  AND STOPSTAMP   > $currentstamp"))
		) {
			return false;
		}
		memcached_set($key, $ads ?: [], AD_CACHE_TIME_PARTYLIST);
		return $ads;
	}
	$cachetime = AD_CACHE_TIME;
	$key = ADS_CACHE_PREFIX.CURRENTDOMAIN.':'.$position;
	$ads = false;
	if (!isset($_REQUEST['NOMEMCACHE'])) {
		if (false !== ($ads = memcached_get($key))) {
			# use primary cache
		} elseif (!db_getlock($key,0)) {
			if (false !== ($ads = memcached_get($key.'_2'))) {
				# use secondary cache
			}
		} elseif (false !== ($ads = memcached_get($key))) {
			# use primary cache on second try
		}
	}
	if ($ads === false) {
		# recalculating
		$ads = __obtain_ads($position);
		memcached_set($key, $ads, $cachetime);
		memcached_set($key.'_2', $ads, $cachetime + ONE_MINUTE);
		db_releaselock($key);
	}

	if ($ads) {
		return pick_ad($ads, $max_height, $min_missing, $min_missing_mult);
	}
	return null;
}

function __obtain_ads(int $position): array|false|null {
	global $__hour, $__mins, $__secs;
	$currentstamp = CURRENTSTAMP;
	$currentdaynum = CURRENTDAYNUM;
#	$__hour = 0;
#	$__mins = 25;
#	$__secs = 0;
#	$currentstamp = mktime(0,25,0,9,3,2011);
#	$currentdaynum = to_days($currentstamp);
	if (!($ads = db_rowuse_hash(['ad','adinfo','banner','bannerhtml','distribution_ad','profilefiltergroup'], /** @lang MariaDB */ '
		SELECT	SQL_NO_CACHE
				ad.ADID, ad.POSITION, STARTSTAMP, STOPSTAMP, FREQCAP, FREQCAP_DAY, FREE AS FREEMULT, FLAGS, DELAY, DELAY_POSITION,
				KEEPTOP, TAKEOVER, DOMAIN, OTHER_DOMAIN_FORCED_FILTERS, ad.IMPRESSIONS,
				ad.ACTUALHITS, ad.BANNERID,banner.NAME AS BANNER_NAME,banner.TYPE, BG, WIDTH, HEIGHT, SIZETYPE, KEEPGOING,
				adinfo.RELATIONID, ADBOX, adinfo.URL,
				THISHOUR,
				(@LATE := '.$currentstamp.' > STOPSTAMP) AS LATE,
				(@NEED := IF(@LATE, ad.IMPRESSIONS, d.IMPRESSIONS + THISHOUR * '.($__mins * ONE_MINUTE + $__secs).' / '.ONE_HOUR.')) AS NEED,
				d.IMPRESSIONS AS D_IMPRESSIONS,
				ad.IMPRESSIONSDONE, 
				(@DONE := ad.IMPRESSIONSDONE) AS DONE,
				(@MISSING := @NEED - @DONE) AS MISSING,
				@MISSING < 0 AS FREE,
				(@PCT := IF(@NEED, 100 * (1 - @DONE / @NEED), 0)) AS PCT,
				(@TIME_LEFT := CAST(STOPSTAMP AS SIGNED) - '.$currentstamp.') AS TIME_LEFT,
				@PCT * '.ONE_DAY.'  / IF(@TIME_LEFT, IF(@TIME_LEFT > 0, @TIME_LEFT, -1 / @TIME_LEFT), 1) AS CPCT,
				'.profile_filters_for_query('ad')."
		FROM ad
		JOIN adinfo USING (ADID)
		LEFT JOIN banner USING (BANNERID)
		LEFT JOIN distribution_ad AS d
			ON	d.ADID 	= ad.ADID
			AND	HOUR	= $__hour
			AND	DAYNUM	= $currentdaynum
		WHERE ad.POSITION = $position
		  AND ACTIVE
		  AND NOT ad.REMOVED
		  AND (KEEPTOP OR IMPRESSIONSDONE < ad.IMPRESSIONS)
		  AND STOPSTAMP  >  $currentstamp + IF(KEEPGOING AND IMPRESSIONSDONE < ad.IMPRESSIONS, ".ADLATE_CONTINUE.", 0)
		  AND STARTSTAMP <= $currentstamp
		ORDER BY
			LATE DESC,
			KEEPTOP DESC,
			CPCT DESC,
			RAND()"))
	) {
		if ($ads === false) {
			return false;
		}
		return null;
	}
	$keep = [];
	foreach ($ads as $adid => $ad) {
		/** @noinspection PhpRedundantOptionalArgumentInspection */
		extract($ad, \EXTR_OVERWRITE);

		if ($POSITION !== ADPOS_PARTYLIST) {
			static $__done_right;
			$done_right = 0;
			# already done banner in right column for this url
			if ($POSITION === ADPOS_RIGHT_TOWER
			&&	!($done_right = isset($__done_right[$URL]))
			) {
				$__done_right[$URL] = true;
			}
			if (!$KEEPTOP) {
				# add halve missing as leeway to do more impressions than absolutely necessary
				memcached_set('admissing:'.$adid, ($missing = (int)$MISSING) + ($missing >> 1), TEN_MINUTES);
			}
			if ($LATE
			||	$STOPSTAMP   > CURRENTSTAMP
			&&	$STARTSTAMP <= CURRENTSTAMP
			&&	(	$KEEPTOP
				||	$MISSING >= $done_right ? DOUBLE_RIGHT_MISSING : 0
				)
			) {
				# show banner!
			} else {
				continue;
			}
		}
		profile_filters_process_item($ad);
		$keep[$ADID] = $ad;
	}
	require_once '_missing.inc';
	calculate_missing($keep);

	return $keep;
}

function pick_ad(array $ads, int $max_height = 0, int $min_missing = 0, float $min_missing_mult = 0): ?array {
	require_once '_hosts.inc';

	static $__have_intrusive = false;
	static $__picked;

	#$ad = reset($ads);
	#$DEBUG_AD = $ad['POSITION'] === ADPOS_TOPBOTTOM;
	$DEBUG_AD = DEBUG_AD;

/*	static $__done;
	if (!isset($__done[$pos = $ad['POSITION']])) {
		$__done[$pos] = true;
		foreach ($ads as $adid => $ad) {
			print_rkr($ad['__M'] ?? null, $adid);
		}
	}*/

	foreach ($ads as $adid => $ad) {
		if (!item_match_profile_filters($ad)) {
			$DEBUG_AD && debug_ad($adid.'('.CURRENTIDENTID.'): not picked due to profile filters');
			continue;
		}
		# NOTE: Not used anymore:
		/*if (!empty($ad['GENDERONLY'])
		&&	(	!($have_user ??= have_user())
			||	$ad['GENDERONLY'] !== $currentuser->row['SEX']
			)
		) {
			$DEBUG_AD && debug_ad($adid.'('.CURRENTUSERID.'): not picked tuo gender mismatch, need '.$ad['GENDERONLY'].' have '.$currentuser->row['SEX']);
			continue;
		}*/
		# NOTE: Not used anymore:
/*		if ($ad['SKIP_LINUX']
		&&	(	ANDROID
			||	false !== stripos(USER_AGENT,'linux')
			)
		) {
			$DEBUG_AD && debug_ad($adid.'('.CURRENTIDENTID.'): not picked due to linux');
			continue;
		}*/
		if (isset($__picked[$adid])) {
			$DEBUG_AD && debug_ad($adid.'('.CURRENTIDENTID.'): not picked, already picked');
			continue;
		}
		if ($max_height
		&&	$ad['HEIGHT'] > $max_height
		) {
			$DEBUG_AD && debug_ad($adid.'('.CURRENTIDENTID."): not picked, {$ad['HEIGHT']} too high (max $max_height)");
			continue;
		}
		# NOTE: Seems debug stuff:
		/*if (!$ad['KEEPTOP']
		&&	(	$min_missing
			||	$min_missing_mult
			)
		) {
			$DEBUG_AD && debug_ad(
				$adid.'('.CURRENTIDENTID.'): '.
				'missing: '.round($ad['MISSING']).
				', min_missing: '.$min_missing.
				', max_missing: '.round($ad['__M']['max_missing'] ?? 0).
				', need missing: '.($min_missing + (1 + $min_missing_mult) * ($ad['__M']['max_missing'] ?? 0))
			);
		}*/
		if ((	$min_missing
			||	$min_missing_mult
			)
		&&	!$ad['KEEPTOP']
		&&	!$ad['LATE']
			# add max_missing, because we might be behind that much due to filters not honoured, add extra missing to that
		&&	$ad['MISSING'] < $min_missing + (1 + $min_missing_mult) * ($ad['__M']['max_missing'] ?? 0)
		) {
			$DEBUG_AD && debug_ad($adid.'('.CURRENTIDENTID.'): not picked not enough missing, need: '.($min_missing + (1 + $min_missing_mult) * ($ad['__M']['max_missing'] ?? 0)));
			continue;
		}
		# NOTE: Not used anymore:
		/*if (($minage = $ad['MINAGE'])
		&&	$minage > get_current_age()
		) {
			$DEBUG_AD && debug_ad($adid.'('.CURRENTIDENTID.'): not picked need age '.$minage);
			continue;
		}*/
		# NOTE: Not used anymore:
		/*if ($ad['COUNTIMG']
		&&	str_contains($ad['COUNTIMG'], 'http:')
		) {
			$DEBUG_AD && debug_ad($adid.'('.CURRENTIDENTID.'): not picked need secure for countimg');
			continue;
		}*/
		if (!take_over_ok($ad)) {
			if ($ad['TAKEOVER']) {
				$DEBUG_AD && debug_ad($adid.'('.CURRENTIDENTID.'): not picked, take-over only home');
			} else {
				$DEBUG_AD && debug_ad($adid.'('.CURRENTIDENTID.'): not picked, take-over running and not in take-over group');
			}
			continue;
		}
		if (($ad['FLAGS'] & ADFLG_SECTION_LIMIT)
		&&	!section_ok($adid)
		) {
			$DEBUG_AD && debug_ad($adid.'('.CURRENTIDENTID.'): not picked need ok section');
			continue;
		}
		if (($is_intrusive_ad = ($ad['FLAGS'] & ADFLG_INTRUSIVE || $ad['POSITION'] === ADPOS_OVERLAY))
		&&	$__have_intrusive
		) {
			$DEBUG_AD && debug_ad($adid.'('.CURRENTIDENTID.'): not picked already done intrusive ad '.$__have_intrusive);
			continue;
		}
		# NOTE: Not used anymore:
		/*if (($ad['FLAGS'] & ADFLG_NEVER_TO_ADMINS)
		&&	have_admin()
		) {
			$DEBUG_AD && debug_ad($adid.'('.CURRENTIDENTID.'): not picked need no admin');
			continue;
		}*/
		if (CURRENTIDENTID
		&&	(	$ad['FREQCAP']
			||	$ad['FREQCAP_DAY'])
		&&	!freqcap_ok($ads, $adid)
		) {
			$DEBUG_AD && debug_ad($adid.'('.CURRENTIDENTID.'): not picked freqcap filled: '.
							db_single('freqcap', "SELECT CONCAT(CNT, ',', CNT_TODAY) FROM freqcap WHERE ADID = $adid AND IDENTID = ".CURRENTIDENTID));
			continue;
		}
/*		if ($__isolated_relationids
		&&	($ad['FLAGS'] & ADFLG_ISOLATED)
		&&	in_array($ad['RELATIONID'],$__isolated_relationids)
		) {
			$DEBUG_AD && debug_ad($adid.'('.CURRENTIDENTID.'): not picked due to isolation');
			continue;
		}*/

		if ($is_intrusive_ad
		&&	($done_intrusive_ad = get_cache_for_unique($intrusive_ad_key = 'intrusive_ad'))
		&&	(CURRENTSTAMP - $done_intrusive_ad < OVERLAY_DEFAULT_DELAY)
		) {
			$DEBUG_AD && debug_ad($adid.'('.CURRENTIDENTID.'): not picked done intrusive on '._datetime_get($done_intrusive_ad).', min delay '.OVERLAY_DEFAULT_DELAY);
			continue;
		}
		if (($delay_position_seconds = $ad['DELAY_POSITION'])
		&&	($done_delayed_position = get_cache_for_unique($delayed_position_key = 'delayed_position:'.$ad['POSITION']))
		&&	(CURRENTSTAMP - $done_delayed_position < $delay_position_seconds)
		) {
			$DEBUG_AD && debug_ad($adid.'('.CURRENTIDENTID.'): not picked due to delay position of '.$delay_position_seconds.' seconds');
			continue;
		}
		if (($delay_seconds = $ad['DELAY'])
		&&	($done_delayed_ad = get_cache_for_unique($delayed_ad_key = 'delayed_ad:'.$adid))
		&&	(CURRENTSTAMP - $done_delayed_ad < $delay_seconds)
		) {
			$DEBUG_AD && debug_ad($adid.'('.CURRENTIDENTID.'): not picked due to delay of '.$delay_seconds.' seconds');
			continue;
		}
		if (!$ad['KEEPTOP']) {
			if (false !== ($left = memcached_get($key = 'admissing:'.$adid))
			&&	$left <= 0
			) {
				$DEBUG_AD && debug_ad($adid.'('.CURRENTIDENTID.'): not picked, no missing left');
				continue;
			}
			if (false !== ($new_left = memcached_decrement($key))
			&&	$left ===  $new_left
			) {
				$DEBUG_AD && debug_ad($adid.'('.CURRENTIDENTID.'): not picked, no missing left second try');
				continue;
			}
		}
/*		# NOTE: Not used anymore:
		if ($ad['FLASHONLY']
		&&	!memcached_add('flashdone:'.$adid.':'.CURRENTIDENTID.':'.CURRENTIPSTR,true,HALF_HOUR)
		) {
			$DEBUG_AD && debug_ad($adid.'('.CURRENTIDENTID.'): not picked, trying flash only once per half hour per user');
			continue;
		}*/
/*		# NOTE: Not used anymore, but might be useful in the future:
		if (($id = $_REQUEST['sID'])
		&&	$ad['ONLYNONCUSTOMERS']
		) {
			require_once '_customer.inc';
			switch ($element = $_REQUEST['sELEMENT']) {
			case 'party':
				if (is_customer_event($id)) {
					$DEBUG_AD && debug_ad($adid.'('.CURRENTIDENTID.'): not picked because page is for customer event');
					continue 2;
				}
				break;
			case 'location':
			case 'organization':
				if (is_customer($element, $id)) {
					$DEBUG_AD && debug_ad($adid.'('.CURRENTIDENTID.'): not picked because page is for customer org/loc');
					continue 2;
				}
				break;
			}
		}*/
		$DEBUG_AD && debug_ad($adid.'('.CURRENTIDENTID.'): picked!');
/*		# NOTE: Not used anymore:
		if ($ad['FLAGS'] & ADFLG_ISOLATED) {
			$__isolated_relationids[] = $ad['RELATIONID'];
		}*/
		if ($is_intrusive_ad) {
			$__have_intrusive = $adid;
			set_cache_for_unique($intrusive_ad_key, ONE_DAY * 2);
		}
		if ($delay_seconds) {
			// 255 is max, need to store for that long because admin might change delay while ad is running
			set_cache_for_unique($delayed_ad_key, ONE_HOUR);
		}
		if ($delay_position_seconds) {
			// 255 is max, need to store for that long because admin might change delay while ad is running
			set_cache_for_unique($delayed_position_key, ONE_HOUR);
		}

		DEBUG_AD && debug_ad($adid.' picked');

		$__picked[$adid] = true;
		return $ad;
	}
	return null;
}

/*function delayed_turn_on() {
	static $__min_delay = null;
	if ($__min_delay !== null) {
		return $__min_delay;
	}
	if ($delay = memcached_get('min_ad_delay')) {
		return $__min_delay = $delay;
	}

	$currentstamp = CURRENTSTAMP;

	$delay = db_single('ad','
		SELECT MIN(IF(DELAY, DELAY * '.ONE_MINUTE.', '.TEN_MINUTES.'))
		FROM ad
		WHERE ACTIVE = 1
		  AND	(  DELAY != 0
			OR POSITION = '.ADPOS_OVERLAY.'
		 	OR FLAGS & '.ADFLG_INTRUSIVE.'
		 	)
		  AND ad.REMOVED = 0
		  AND STARTSTAMP <= '.$currentstamp.'
		  AND STOPSTAMP > '.($currentstamp - ADLATE_CONTINUE).'
		  AND ((KEEPTOP = 0 AND KEEPGOING = 1) OR STOPSTAMP > '.$currentstamp.')
		  AND (	   KEEPTOP = 1
		  	OR IMPRESSIONSDONE < ad.IMPRESSIONS
			  )'
	) ?: 5;

	memcached_set('min_ad_delay', $delay);

	return $__min_delay = $delay;
}*/

function section_ok(int $adid): bool {
	static $__sectionlist = 0;
	static $__elements;

	# /topic/	=> section 64
	# /forum/	=> section 24

	if ($__sectionlist === 0) {
		if (false === ($adidstr = get_running_adidstr())) {
			return false;
		}
		if ($adidstr
		&&	($__sectionlist = memcached_simple_hash('adforsection',"
			SELECT ADID, SECTIONID, REGEX
			FROM adforsection
			WHERE ADID IN ($adidstr)",
			key: 'sections_for_ads'
		))) {
			# Prepare the __elements array
			$__elements = [];
			$forum_sectionid = __SECTIONS['forum'];
			$party_sectionid = __SECTIONS['party'];
			foreach ($__sectionlist as $tmp_adid => $sections) {
				foreach ($sections as $sectionid => $regex) {
					if (!$regex) {
						continue;
					}
					if ($sectionid === $party_sectionid
					&&	preg_match('"^(?<element>party|event):(?<id>\d+)$"', $regex, $match)
					) {
						$__elements[$tmp_adid][$match['element']][(int)$match['id']] = true;
						continue;
					}
					if ($sectionid === $forum_sectionid
					&&	preg_match('"^/forurm/(?<forumid>\d+)$"', $regex, $match)
					) {
						$__elements[$tmp_adid]['forum'][(int)$match['forumid']] = true;
						unset($__sectionlist[$tmp_adid][$sectionid]);
					}
				}
			}
		}
	}
	if (!isset($__sectionlist[$adid])) {
		# no limitation
		return true;
	}
	global $__currtopic;
	$current_sectionid = __SECTIONS[$_REQUEST['sELEMENT']];
	if ($current_sectionid === __SECTIONS['topic']
	&&	!empty($__currtopic['FORUMID'])
	&&	isset($__elements[$adid]['forum'],
			  $__elements[$adid][__SECTIONS['forum']][$__currtopic['FORUMID']])
	) {
		return true;
	}

	if (!isset($__sectionlist[$adid][$current_sectionid])) {
		# current section is not allowed
		return false;
	}

	if ($_REQUEST['sID']
	&&	isset($__elements [$adid][$_REQUEST['sELEMENT']][$_REQUEST['sID']])

	# section matches, no regex means entire section
	||	!($regex = $__sectionlist[$adid][$current_sectionid])
	) {
		return true;
	}
	if ($regex[0] !== '^') {
		$regex = "^$regex";
	}
	return preg_match(chr(6).$regex.chr(6), $_SERVER['REQUEST_URI']);
}

function freqcap_ok(array $ads, int $adid): bool {
	if (!CURRENTIDENTID) {
		return false;
	}
	static $__freqs =
			($adidstr = get_running_adidstr())
		?	db_rowuse_hash('freqcap', '
			SELECT ADID, CNT, CNT_TODAY
			FROM freqcap
			WHERE IDENTID = '.CURRENTIDENTID."
			  AND ADID IN ($adidstr)")
		:	$adidstr;

	if ($__freqs === false) {
		return false;
	}
	if (!isset($__freqs[$adid])) {
		return true;
	}
	return	(!($fq = $ads[$adid]['FREQCAP'])		|| $__freqs[$adid]['CNT']	 	< $fq)
		&&	(!($fq = $ads[$adid]['FREQCAP_DAY'])	|| $__freqs[$adid]['CNT_TODAY']	< $fq);
}

function get_running_adidstr(): string|false {
	static $__adidstr = memcached_single_string('ad', '
		SELECT GROUP_CONCAT(ADID)
		FROM ad
		WHERE STARTSTAMP <= '.(CACHESTAMP + TEN_MINUTES).'
		  AND STOPSTAMP   > '.(CACHESTAMP - TEN_MINUTES - ADLATE_CONTINUE),
		key: 'running_ads')
	?:	false;
	return $__adidstr;
}

const ALLOWED_AD_POSITIONS = [
	ADPOS_TOPBOTTOM,
	ADPOS_LEFTMENU,
	ADPOS_MENUMINI,
	ADPOS_HOMERECTANGLE,
	ADPOS_SMALL_SCREEN,
	ADPOS_SUBBAR,
	ADPOS_RIGHT_TOWER,
	ADPOS_OVERLAY,
	ADPOS_PARTYLIST,
	ADPOS_CUSTOM,
	ADPOS_HPA,
];

function require_ad_position(array &$arr, string $field): int {
	return require_number_element($arr, $field, ALLOWED_AD_POSITIONS);
}

function ad_position_options(?array $ad = null, ?array $banner = null): void {
	$disabled = [];
	$names = [];
	foreach (ALLOWED_AD_POSITIONS as $ad_position) {
		if (!(/* $selected = */ $ad && $ad['POSITION'] === $ad_position)
		#	These positions are not valid for new ads:
		&&	in_array($ad_position, [ADPOS_HPA, ADPOS_CUSTOM], true)) {
			continue;
		}

		$names[$ad_position] = ad_position_name($ad_position);

		if ($ad
		&&	$ad['TAKEOVER']
		#	These positions are not valid for take-overs:
		&&	in_array($ad_position, [ADPOS_PARTYLIST, ADPOS_RIGHT_TOWER], true)
		) {
			$disabled[$ad_position] = true;
			continue;
		}
		if (!$banner) {
			continue;
		}
		if (!($ok_position = BANNER_SIZE_TO_AD_POSITION[$banner['SIZETYPE']] ?? null)) {
			continue;
		}
		if ( is_array($ok_position)
		?	!in_array($ad_position, $ok_position, true)
		:	$ad_position !== $ok_position
		) {
			$disabled[$ad_position] = true;
		}
	}

	$only_one_allowed = count($names) === count($disabled) + 1;

	$selected = $ad['POSITION'] ?? 0;

	asort($names);
	foreach ($names as $ad_position => $name) {
		?><option<?
		?> value="<?= $ad_position ?>"<?
		if ($selected === $ad_position) {
			$found = true;
			?> selected<?

		} elseif (isset($disabled[$ad_position])) {
			?> disabled<?

		} elseif ($only_one_allowed) {
			$found = true;
			?> selected<?
		}
		?>><?= $name ?></option><?
	}
	if ($selected
	&&	!isset($found)
	) {
		?><option selected value="<?= $selected ?>"><?=
			ad_position_name($selected)
		?></option><?
	}
}

function ad_subposition_name(string $ad_position): string {
	return match ($ad_position) {
		'subagendas' => __('section:party'),
		default		 => '',
	};
}

function ad_position_name(int $ad_position): string {
	static $__ad_position_name = [];
	return $__ad_position_name[$ad_position] ??= (__('adpos:'.$ad_position) ?: __('adpos:unknown'));
}

function get_shown_adids(?int $adid = null): array {
	static $__shown = [];
	if ($adid) {
		$__shown[$adid] = $adid;
	}
	return $__shown;
}

function display_ad(array &$ad, ?string $class = null, bool $for_embed = false, string $loading = 'lazy'): void {
	if (isset($ad['done'])) {
		return;
	}
	$ad['done'] = true;
	get_shown_adids($ad['ADID']);

	$free = !empty($ad['FREE']);

	$id = ($ad['POSITION'] ?? null) === ADPOS_SUBBAR ? 'subbar' : null;

	if (empty($ad['WIDTH'])
	&&	!empty($ad['SIZETYPE'])
	) {
		[$ad['WIDTH']] = banner_get_width_height($ad['SIZETYPE']);
	}

	$classes = [];

	?><div<?
	if (!empty($ad['SIZETYPE'])
	&&	$ad['SIZETYPE'] === BANNER_SIZE_BILLBOARD
	&&	!empty($ad['NAME'])
	) {
		$classes[] = 'mw100 noverflow';
	}
	if ($id) {
		?> id="<?= $id ?>"<?
		if ($id === 'subbar') {
			$classes[] = 'block';
		}
	}
	if (!empty($ad['WIDTH'])) {
		?> data-width="<?= $ad['WIDTH'] ?>"<?
	}
	if (!$for_embed
	&&	in_array($ad['POSITION'], ADPOS_HORIZONTAL, true)
	) {
		$classes = ['great-thing'];
	}
	if (empty($ad['PARTYID'])) {
		$classes[] = 'center';
	}
	if ($class) {
		$classes[] = $class;
	}
	if ($classes) {
		?> class="<?= implode(' ', $classes) ?>"<?
	}
	?>><?
	$uniq = display_banner($ad, $free, true, loading: $loading);
	?></div><?

	if (HOME_OFFICE) {
		?><!-- ADID:<?= $ad['ADID'] ?>,<?= $uniq ?> --><?
	}
	/*if (!empty($ad['COUNTIMG'])) {
		?><img loading="<?= $loading ?>" class="xie" width="1" height="1" src="<?=
			escape_specials(str_replace('#UNIQUE#', $uniq, $ad['COUNTIMG']))
		?>" /><?
	}
	if (!empty($ad['COUNTHTML'])) {
		echo str_replace('#UNIQUE#', $uniq, $ad['COUNTHTML']);
	}*/
}

function take_over_ok(?array $ad = null): bool {
	if (empty($_REQUEST['sELEMENT'])
	||	$_REQUEST['sELEMENT'] !== 'home'
	) {
		# takeover on home only
		return !$ad['TAKEOVER'];
	}
	static $__takeovers = memcached_simple_hash('ad','
		SELECT STARTSTAMP, STOPSTAMP, ADID, ADID
		FROM ad
		WHERE TAKEOVER = 1
		  AND STOPSTAMP > '.(TODAYSTAMP - ONE_HOUR));

	if (!$__takeovers) {
		return true;
	}
	$in_take_over = false;
	foreach ($__takeovers as $startstamp => $stopstamps) {
		foreach ($stopstamps as $stopstamp => $adids) {
			# in take-over time?
			if (CURRENTSTAMP >= $startstamp
			&&	CURRENTSTAMP  < $stopstamp
			) {
				$in_take_over = true;
				foreach ($adids as $adid) {
					if ($ad
					&&	$ad['ADID'] === $adid
					) {
						return true;
					}
				}
			}
		}
	}
	return !$in_take_over;
}

function get_cache_for_unique(string $key) {
	if (CURRENTIDENTID) {
		if ($done = memcached_get($key.':'.CURRENTIDENTID)) {
			return $done;
		}
		$done = 0;
		if (isset($_COOKIE['FLOCK_SECRET'])) {
			if ($skip = memcached_get($skipkey = $key.':ip:'.CURRENTIPSTR.':s:'.$_COOKIE['FLOCK_SECRET'])) {
				[$done, $timeout] = $skip;
				$timeout -= (CURRENTSTAMP - $done);
				if ($timeout > 0) {
					memcached_set($key.':'.CURRENTIDENTID, $done, $timeout);
				}
				memcached_delete($skipkey);
			}
		}
		memcached_delete($key.':ip:'.CURRENTIPSTR);
		return $done;
	}
	if ($done = memcached_get($key.':ip:'.CURRENTIPSTR)) {
		return $done;
	}
	return 0;
}

function set_cache_for_unique(string $key, int $timeout): void {
	if (CURRENTIDENTID) {
		memcached_set($key.':'.CURRENTIDENTID, time(), $timeout);
	} else {
		memcached_set($key.':ip:'.CURRENTIPSTR.':s:'.getifset($_COOKIE, 'FLOCK_SECRET') ?: 0, [time(), $timeout], $timeout);
		memcached_set($key.':ip:'.CURRENTIPSTR, time(), $timeout);
	}
}

function hide_ad_at(int $adposition): bool {
	static $__hidable = [
		ADPOS_TOPBOTTOM		=> ADS_SHOW_TOP_BANNER,
		ADPOS_LEFTMENU		=> ADS_SHOW_LEFT_TOWER,
		ADPOS_RIGHT_TOWER	=> ADS_SHOW_RIGHT_TOWER,
		ADPOS_SUBBAR		=> ADS_SHOW_SUB_BAR,
		ADPOS_MENUMINI		=> ADS_SHOW_MENU_MINI,
		ADPOS_OVERLAY		=> ADS_SHOW_OVERLAY,
		ADPOS_PARTYLIST		=> ADS_SHOW_PARTYLIST,
	];
	return
		isset($__hidable[$adposition])
	&&	have_user()
	&&	!setting_isset($__hidable[$adposition])
	&&	(require_once '_donators.inc')
	&&	may_hide_ads();
}

function ad_distribution_alright(int $adid): bool {
	# alright if:
	# - there is a distribution
	# - OR banner should be kept at top anyway (KEEPTOP)
	# - OR ad is an agenda sportlight, always visible, also no distribution

	return	(bool)db_single(['distribution_ad', 'ad'], '
		SELECT 1
		FROM ad
		LEFT JOIN distribution_ad USING (ADID)
		WHERE ADID = '.$adid.'
		  AND (	   distribution_ad.ADID IS NOT NULL
				   OR KEEPTOP
				   OR POSITION = '.ADPOS_PARTYLIST.'
			  )'
	);
}

function get_ad_url(array $ad, string $url): string {
	require_once '_url.inc';
	if (!str_contains($url, 'partyflock.')
	&&	!str_contains($url, 'utm_')
	&&	$ad['URL_ADD_DEFAULT_UTM']
	) {
		require_once '_flockmod.inc';
		add_utm($url, 'ad', $ad['ADID']);
	}
	$url = make_absolute_url($url);
	if ($ad['DISCOUNT'] !== 100) {
		require_once '_affiliates.inc';
		$url = make_affiliate_url($url, 'ad', $ad['ADID'], ['keep_utm' => true]);
	}
	return $url;
}

const BANNER_SIZE_TO_AD_POSITION = [
	BANNER_SIZE_TOPBOTTOM		=> ADPOS_TOPBOTTOM,
	BANNER_SIZE_LEADERBOARD		=> ADPOS_TOPBOTTOM,
	BANNER_SIZE_TOWER			=> [ADPOS_RIGHT_TOWER, ADPOS_LEFTMENU],
	BANNER_SIZE_WIDETOWER		=> [ADPOS_RIGHT_TOWER, ADPOS_LEFTMENU],
	BANNER_SIZE_HUGERECTANGLE	=> ADPOS_HOMERECTANGLE,
	BANNER_SIZE_FRONTFLYER		=> ADPOS_HOMERECTANGLE,
	BANNER_SIZE_INLINE			=> 0,
	BANNER_SIZE_MENUMINI		=> ADPOS_MENUMINI,
	BANNER_SIZE_BILLBOARD		=> ADPOS_SUBBAR,
	BANNER_SIZE_OVERLAY			=> ADPOS_OVERLAY,
	BANNER_SIZE_RIGHTFLYER		=> ADPOS_RIGHT_TOWER,
	BANNER_SIZE_SMALL_MOBILE	=> ADPOS_SMALL_SCREEN,
	BANNER_SIZE_LARGE_MOBILE	=> ADPOS_SMALL_SCREEN,
	BANNER_SIZE_HUGE_MOBILE		=> ADPOS_SMALL_SCREEN,
];

function bannersize_to_adpositionstr(?int $banner_size_type = null): string {
	$ad_position = BANNER_SIZE_TO_AD_POSITION[$banner_size_type] ?? '';
	if (is_array($ad_position)) {
		return implode(',', $ad_position);
	}
	return (string)$ad_position;
}
