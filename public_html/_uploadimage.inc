<?php

# NOTE: loading=eager by default, as uploadimages appear at the top of the page almost always

require_once '_orientation.inc';
require_once '_smartcrop.inc';
require_once '_execute.inc';
require_once '_browser.inc';
require_once '_ubb.inc';
require_once '_layout.inc';

global $dbgid, $debug_upload_images;
/** @noinspection NonSecureUniqidUsageInspection */
$dbgid ??= uniqid();
$debug_upload_images ??= HOME_THOMAS || SERVER_SANDBOX;

const FORCE_PROCESSING			= SERVER_SANDBOX;	# to make a new uploadimage, even if same already exists

const UPIMG_PREVIEW_HEIGHT		= 150;
const UPIMG_MINIMUM_SQUARE		= 500;	# pixels
const UPIMG_CACHE_VERSION		= 13;

const UPIMG_NOCHANGE			= 0b0000_0000_0000_0000_0000_0001;
const UPIMG_LINK_ORIGINAL		= 0b0000_0000_0000_0000_0000_0010;
const UPIMG_GET_ORIGINAL		= 0b0000_0000_0000_0000_0000_0100;
const UPIMG_PREVIEW				= 0b0000_0000_0000_0000_0000_1000;
const UPIMG_MAX_WIDTH			= 0b0000_0000_0000_0000_0001_0000;
const UPIMG_R					= 0b0000_0000_0000_0000_0010_0000;
const UPIMG_SCHEMA				= 0b0000_0000_0000_0000_0100_0000;
const UPIMG_SHOW_CROP			= 0b0000_0000_0000_0000_1000_0000;
const UPIMG_SHOW_HISTORY		= 0b0000_0000_0000_0001_0000_0000;
const UPIMG_PREFER_ORIENTATION	= 0b0000_0000_0000_0010_0000_0000;	# prefer orientation to generated or not
const UPIMG_SWAPPER				= 0b0000_0000_0000_0100_0000_0000;
const UPIMG_SHOW_ELEMENT		= 0b0000_0000_0000_1000_0000_0000;
const UPIMG_ABS					= 0b0000_0000_0001_0000_0000_0000;
const UPIMG_SHOW_DIMS			= 0b0000_0000_0010_0000_0000_0000;
const UPIMG_NO_OPENGRAPH		= 0b0000_0000_0100_0000_0000_0000;
const UPIMG_DEFINITIVE			= 0b0000_0000_1000_0000_0000_0000;
const UPIMG_SHOW_SEARCH			= 0b0000_0001_0000_0000_0000_0000;
const UPIMG_HIDE_FORM			= 0b0000_0010_0000_0000_0000_0000;
const UPIMG_FORCE_CROP			= 0b0000_0100_0000_0000_0000_0000;	# show, even when UPIMG_NOCHANGE is set, cropping will be possible
const UPIMG_FORCE_REMOVE		= 0b0000_1000_0000_0000_0000_0000;	# show, even when UPIMG_BOCHANGE is set
const UPIMG_SHOW_FALLBACK		= 0b0001_0000_0000_0000_0000_0000;

function get_sizetypes(): array {
	return [
		'original'		=> null,

		# 'size'		=> [max pixels, max width, max height]
		'high@2x'		=> [2560*2048,	2880,	2880],
		'high'			=> [1280*1024,	1600,	1600],

		'regular@2x'	=> [960*680,	1280,	1280],
		'regular'		=> [480*340,	 640,	 640],

		'thumb@2x'		=> [200*142*4,	500,	 500],
		'thumb'			=> [200*142,	300,	 300],

		'tiny@2x'		=> [200*150,	300,	 300],
		'tiny'			=> [100* 75,	150,	 150],
	];
}
function is_uploadimage_type(?string $type = null): string|array|false {
	static $__upload_types = [
	'news'			=> 'news',
	'location'		=> 'location',
	'artist'		=> 'artist',
	'organization'	=> 'organization',
	'review'		=> 'review',
	'contest'		=> 'contest',
	'interview'		=> 'interview',
	'party'			=> 'party',
	'weblog'		=> 'weblog',
	'party2'		=> 'party',
	'report'		=> 'report',
	'promo'			=> 'promo',
	'promoteaser'	=> 'promo',
	'stream'		=> 'stream',
	'column'		=> 'column',
	'job'			=> 'job',
	'music'			=> 'music',

	'eventmap'		=> 'eventmap',
	'lineup'		=> 'lineup',
	'orgashost'		=> 'organization',
	'locashost'		=> 'location',
	];
	if ($type) {
		return $__upload_types[$type] ?? false;
	}
	return $__upload_types;
}

function require_uploadimage_size(array $array, string $field): string|false {
	if (!($spec = explain_table('uploadimagemeta'))) {
		return false;
	}
	return require_element($array, $field, $spec['SIZE']->enum, strict: true);
}

function uploadimage_info(string $key = 'FILE'): ?array {
	return uploadimage_file_info($_FILES[$key]['tmp_name'], $_FILES[$key]['name']);
}

# NOTE: Make sure $tmp does not require escaping
function uploadimage_file_info(string $tmp, ?string $original_name = null, bool $get_alpha = false): ?array {
	# FIXME: Support 10 bit AVIF (ImageMagick supports it, but getimagesize() does not recogmize the file)
	require_once 'defines/imagick.inc';
	$error = null;
	if ($info = getimagesize($tmp)) {
		if ($get_alpha
		&&	in_array($info[2], [
			IMAGETYPE_AVIF,
			IMAGETYPE_GIF,
			IMAGETYPE_PNG,
			IMAGETYPE_WEBP,
		], true)) {
			# $command will be in error mail or messages
			[$rc, $stdout, $stderr] = execute("identify -format %w#%h#%A $tmp");
			if (!$rc) {
				$parts = explode('#', $stdout);
				if (count($parts) === 3) {
					$info['alpha'] = $parts[2] !== 'Undefined';
				} else {
					$error = 'identify for alpha result not understood: '.$stdout;
				}
			} else {
				$error = 'identify for alpha failed: '.$stderr;
			}
		}
	} else {
		$mime_type = mimetype_file($tmp, $original_name, false, $description);
		mail_log("mime type: $mime_type, has description: $description", get_defined_vars());
		if (str_contains($mime_type, ';')) {
			[$mime_type] = explode(';', $mime_type);
			$mime_type = trim($mime_type);
		}
		switch ($mime_type) {
		# TODO: Would be nice if we supported SVG.
		#		It's deteced but imageprocessing still fails, does not support SVG yet.
		#		Store the SVG as the 'original', no need to generate different sizes,
		#		width & height of original is only useful for aspect and determining dimensions of other sizes.
		case 'image/svg+xml':
			$info = [0, 0, 'SVG'];
			break;

		case 'application/pdf':
			# fall through
		case 'application/postscript':
			$type = str_ends_with($original_name, '.ai') ? 'AI' : 'PDF';
			$new_tmp = "{$tmp}_{$type}_identified.ppm";
			[$rc, $stdout, $stderr] = execute('magick convert -verbose '.IM_LIMITS." -quality 100 -density 300 $tmp ppm:$new_tmp");
			if ($rc) {
				$error = 'magick convert of PDF/AI failed: '.$stderr;
				break;
			}
			if ($stderr) {
				error_log('WARNING magick convert of PDF/AI stderr: '.$stderr);
			}
			unlink_at_end($new_tmp);
			if (!preg_match('"\s(?<width>\d+)x(?<height>\d+)\s"u', $stdout, $match)) {
				$error = 'magick convert of PDF/AI failed to recognize dimensions';
				break;
			}
			$info = [
				(int)$match['width'],
				(int)$match['height'],
				$type,
				'ppm_file' => $tmp
			];
			break;

		default:
			$error = 'mime type not supported: '.$mime_type;
		}
	}
	if ($error) {
		mail_log('something failed in uploadimage_file_info: '.$error, get_defined_vars());
		register_error('uploadimage:error:no_info_LINE', DO_UBB, ['FILE' => $original_name]);
		if (!empty($mime_type)) {
			$total_type = $mime_type;
			if (!empty($description)) {
				$total_type .= " ($description)";
			}
			register_error('uploadimage:error:analyzation_LINE', ['FILETYPE' => $total_type]);
		}
		return null;
	}
	return $info;
}

function get_uploadimage_key(string $type, int $id, string $size, ?bool $debug = null): string {
	global $debug_upload_images;
	$debug ??= $debug_upload_images;
	return "uploadimages:$size:$type:$id:".UPIMG_CACHE_VERSION.($debug ? ':debug' : '');
}

function clear_uploadimage_cache(string|int $arg, int $id = 0): bool|array {
	# NOTE: call methods:
	#	type,id == element,id
	#	upimgid,0

	$clear = [];
	if (!$id) {
		# argument is UPIMGID
		if ($items = db_rowuse_array('uploadimage_link', "
			SELECT TYPE, ID
			FROM uploadimage_link
			WHERE UPIMGID = $arg",
			DB_NON_ASSOC
		)) {
			foreach ($items as [$element, $local_id]) {
				$clear[$element][$local_id] = $local_id;
			}
		}
	} else {
		$clear[$arg][$id] = $id;
	}
	if (!($spec = explain_table('uploadimagemeta'))) {
		return false;
	}
	$keys = [];
	foreach ($clear as $element => $ids) {
		foreach ($ids as $local_id) {
			foreach ($spec['SIZE']->enum as $size) {
				$keys[] = ($key = get_uploadimage_key($element, $local_id, $size, false));
				$keys[] = "$key:debug";
			}
		}
	}
	return memcached_delete($keys);
}

function have_front_flyer(array $party): bool {
	return (bool)uploadimage_get('party',  $party['ID'], 'original');
}
function have_back_flyer(array $party): bool {
	return (bool)uploadimage_get('party2', $party['ID'], 'original');
}

const CMP_ORIENTATION	= 1;
const CMP_GENERATION	= 3;

function uploadimage_get(
	string				$type,
	int					$id,
	?string				$size	= null,
	int					$flags	= 0,
	array|string|null	$orientation_preference = null,
): array|false {
	# use dimensions to select.. e.g. MAX_THUMB, select all <= MAX_THUMB
	$size ??= 'regular';
	require_once '_smallscreen.inc';
	if (SMALL_SCREEN
	&&	$type !== 'eventmap'
	&&	!$orientation_preference
	) {
		$orientation_preference = ['landscape', 'square'];
	}

	$may_change = !($flags & UPIMG_NOCHANGE) && may_change_uploadimage($type, $id);

	$key = get_uploadimage_key($type, $id, $size);

	$img = !isset($_REQUEST['NOMEMCACHE']) ? memcached_get($key) : null;

	$orientation_worth = [];
	if ($orientation_preference) {
		if (is_array($orientation_preference)) {
			$i = 0;
			foreach ($orientation_preference as /* $ndx => */ $orientation) {
				$orientation_worth[$orientation] = ++$i;	// NOSONAR
			}
		} else {
			$orientation_worth[$orientation_preference] = 1;
		}
	}

	$prefer_orientation = $flags & UPIMG_PREFER_ORIENTATION;

	$cmp_order = [];
	if ($orientation_worth) {
		$cmp_order[] = CMP_ORIENTATION;
	}
	if ($prefer_orientation) {
		$cmp_order[] = CMP_GENERATION;
	} else {
		$cmp_order = [CMP_GENERATION, ...$cmp_order];
	}

	# Don't make $sort_image static! The function is fine, but making the variable static,
	# binds the 'use'd variables $cmp_order and $orientation_worth to the $sort_imgs static var and thus
	# don't change for later invocations.

	$sort_imgs = static function(array $img_one, array $img_two) use ($cmp_order, $orientation_worth): int {
		foreach ($cmp_order as $cmp) {
			if ($diff = match($cmp) {
				CMP_GENERATION	=> !empty($img_one['GENERATED'])						<=> !empty($img_two['GENERATED']),
				CMP_ORIENTATION	=> ($orientation_worth[$img_one['ORIENTATION']] ?? 20)	<=> ($orientation_worth[$img_two['ORIENTATION']] ?? 20),
			}) {
				return $diff;
			}
		}
				# default first
		return	$img_two['DEFAULT'] <=> $img_one['DEFAULT']
				# non-generated first
			?:	!empty($img_one['GENERATED']) <=> !empty($img_two['GENERATED'])
				# chosen square first
			?:	empty($img_one['CHOSEN']) <=> empty($img_two['CHOSEN']);
	};

	if ($cached = $img && empty($img['BUSY'])) {
		if (!empty($img['IMGS'])) {
			$images = $img['IMGS'];
			usort($images, $sort_imgs);
			$img = $images[0];
			$img['IMGS'] = $images;
		}
	} elseif (false === ($images = db_rowuse_array(['uploadimage_link','uploadimage','uploadimagemeta','uploadimagecp','uploadimage_crop','uploadimage_generated'], "
		SELECT	UPIMGID, BUSY, USERID,
				CSTAMP, MSTAMP, FILETYPE,
				meta.WIDTH, meta.HEIGHT, NO_PERSON,
				ORIENTATION, `DEFAULT`,
				COPYRIGHT,
				CONCAT(FIRSTPART, '_', FBID, '_', LASTPART) AS FB_IDSTR,
				(SELECT CONCAT(UPIMGID, ':', `GENERATED`) FROM uploadimage_generated WHERE TYPE = '$type' AND ID = '$id') AS GENERATED_CHOSEN,
				(SELECT CONCAT(UPIMGID, ':', WIDTH, ':', HEIGHT, ':', X, ':', Y) FROM uploadimage_crop AS uc WHERE TYPE = '$type' AND ID = $id AND uc.UPIMGID = uploadimage_link.UPIMGID) AS MANUAL_CROP,
				(SELECT CONCAT(WIDTH, ' x ', HEIGHT) FROM uploadimagemeta AS orig WHERE orig.UPIMGID = meta.UPIMGID ORDER BY WIDTH * HEIGHT DESC LIMIT 1) AS ORIG_SIZE,
				(SELECT GROUP_CONCAT(CONCAT(WIDTH, 'x', HEIGHT)) FROM uploadimagemeta uim WHERE uim.UPIMGID = meta.UPIMGID) AS VERSIONS
		FROM uploadimage_link
		JOIN uploadimage USING (UPIMGID)
		JOIN uploadimagemeta AS meta USING (UPIMGID)
		LEFT JOIN uploadimage_facebook_ids USING (UPIMGID)
		LEFT JOIN uploadimagecp USING (UPIMGID)
		WHERE SIZE = '$size'
		  AND TYPE = '$type'
		  AND ID   = $id"))
	) {
		return false;
	} elseif (!$images) {
		$img = null;
	}
	if (!empty($images)
	&&	!$cached
	) {
		require_once '_urltitle.inc';
		$element = is_uploadimage_type($type);
		if (false === ($url_title = get_urltitle($element, $id))) {
			return false;
		}
		$square    = null;
		$portrait  = null;
		$landscape = null;

		foreach ($images as &$tmp_img) {
			${$tmp_img['ORIENTATION']} = $tmp_img;

			if ($tmp_img['ORIENTATION'] !== 'square') {
				$tmp_img['NEED_CROP'] = UPIMG_SHOW_CROP;
			}
		}
		unset($tmp_img);

		if (!$square) {
			if ($chosen = $images[0]['GENERATED_CHOSEN'] ? explode(':', $images[0]['GENERATED_CHOSEN']) : false) {
				$chosen[0] = (int)$chosen[0];
			}
			# prepare manual_crops
			$manuals = [];
			foreach ($images as $img) {
				if (empty($img['MANUAL_CROP'])) {
					continue;
				}
				[, $width, $height, $x, $y] = int_explode(':', $img['MANUAL_CROP']);
				$new_img = $img;
				$s = (int)round(min(
					$height	* $new_img['HEIGHT'] / 1000000000,
					$width	* $new_img['WIDTH']  / 1000000000
				));
				$new_img['WIDTH']			= $s;
				$new_img['HEIGHT']			= $s;
				$new_img['DEFAULT']			= false;
				$new_img['COPYRIGHT']		= null;
				$new_img['GENERATED']		= 'manualcrop';
				$new_img['GENERATED_SRC']	= $img['ORIENTATION'];
				$new_img['ORIENTATION']		= 'square';

				[$w, $h] = int_explode(' x ', $new_img['ORIG_SIZE']);
				$full_s = (int)round(min(
					$width  * $w / 1000000000,
					$height * $h / 1000000000
				));

				# choose max if it's within .1%
				# due to rounding errors probably 1 or few pixels too small

				$min_part = min($w, $h);
				if ($full_s < $min_part
				&&	($min_part - $full_s) / $min_part < .1
				) {
					$full_s = $min_part;
				}

				$new_img['ORIG_SIZE'] = $full_s.' x '.$full_s;
				$new_img['FORMAT'] = ['manualcrop', $s, $full_s, "_{$width}x$height,$x,$y"];

				if ($chosen
				&&	$chosen[0] === $new_img['UPIMGID']
				&&	$chosen[1] === $new_img['GENERATED']
				) {
					$new_img['CHOSEN'] = true;
				}

				$manuals[] = $new_img;
			}
			if ($manuals) {
				$images = array_merge($images, $manuals);
			}
			$autos = [];
			foreach ([
				['portrait',	'crop'],
				['portrait',	'pillarbox'],
				['landscape',	'crop'],
				['landscape',	'letterbox'],
			] as $info) {
				[$from, $generated_type] = $info;
				if (!$$from) {
					continue;
				}
				$new_img = $$from;
				$new_img['MAY_CHANGE'] = false;
				$crop = ($generated_type === 'crop');

				$s =	$crop
					?	min($new_img['WIDTH'], $new_img['HEIGHT'])
					:	max($new_img['WIDTH'], $new_img['HEIGHT']);

				$new_img['WIDTH']  = $s;
				$new_img['HEIGHT'] = $s;

				[$w, $h] = int_explode(' x ', $new_img['ORIG_SIZE']);
				$full_s = $crop ? min($w, $h) : max($w, $h);

				$new_img['ORIENTATION']		= 'square';
				$new_img['GENERATED_SRC']	= $from;
				$new_img['GENERATED']		= $generated_type;
				$new_img['DEFAULT']			= 0;
				$new_img['ORIG_SIZE']		= $full_s.' x '.$full_s;
				$new_img['FORMAT']			= [$crop ? 'smartcrop' : $generated_type, $s, $full_s];

				if ($chosen
				&&	$chosen[0] === $new_img['UPIMGID']
				&&	$chosen[1] === $new_img['GENERATED']
				) {
					$new_img['CHOSEN'] = true;
				}
				$autos[] = $new_img;
			}
			if ($autos) {
				if (!$chosen) {
					static $__type = [
						'manualcrop'	=> 0,
						'crop'			=> 1,
						'letterbox'		=> 2,
						'pillarbox'		=> 3,
					];
					static $__type_for_party = [
						'manualcrop'	=> 0,
						'letterbox'		=> 1,
						'pillarbox'		=> 2,
						'crop'			=> 3,
					];
					static $__src = [
						'portrait'		=> 0,
						'landscape'		=> 1,
					];
					$__use_type = $type === 'party' || $type === 'party2' ? $__type_for_party : $__type;

					usort($autos, static fn(array $a, array $b): int =>
							$__use_type[$a['GENERATED']] <=> $__use_type[$b['GENERATED']]
						?:	$__src[$a['GENERATED_SRC']]	 <=> $__src[$b['GENERATED_SRC']]
						?:	$b['UPIMGID']				 <=> $a['UPIMGID']
					);
					$autos[0]['CHOSEN'] = true;
				}
				$images = array_merge($images,$autos);
			}
		}
		if (!$landscape) {
			$want_aspect = 1.8;
			$new_img = $portrait ?: $square;
			$new_img['HEIGHT'		] = floor($new_img['WIDTH'] / $want_aspect);
			$new_img['ASPECT_CROP'	] = true;
			$new_img['GENERATED'	] = 'smartcrop';
			$new_img['GENERATED_SRC'] = $new_img['ORIENTATION'];
			$new_img['ORIENTATION'	] = 'landscape';
			$images[] = $new_img;
		}
		$title_part = $url_title ? "/$url_title" : '';

		foreach ($images as /* $i => */ &$tmp_img) {
			/** @noinspection PhpRedundantOptionalArgumentInspection */
			extract($tmp_img, \EXTR_OVERWRITE);
			[$tmp_img['ORIG_WIDTH'], $tmp_img['ORIG_HEIGHT']] = int_explode(' x ', $ORIG_SIZE);
			global $debug_upload_images;
			$tmp_img['URL_PREFIX']	= ($debug_upload_images ? '/test-images' : '/images')."/$type/{$id}_";
			$tmp_img['URL_POSTFIX']	= "_$UPIMGID$title_part.$FILETYPE";
		}
		unset($tmp_img);

		usort($images, $sort_imgs);

		$img = $images[0];
		$img['HAVE_SQUARE'] = (bool)$square;
		$img['IMGS'] = $images;
	}
	if ($img || $may_change) {
		$img ??= ['WIDTH' => 100];
		$img['TYPE'		 ] = $type;
		$img['ID'		 ] = $id;
		$img['LINKTYPE'	 ] = $type;
		$img['PARENTID'	 ] = $id;
		$img['MAY_CHANGE'] = $may_change;
	} else {
		$img = [];
	}
	memcached_set($key, $img, ONE_HOUR);
	return $img;
}

# NOTE: uploadimage_get_url() also sets the INTRINSIC_WIDTH and INTRINSIC_HEIGHT
#		properties of $tmp_img. They represent the actual width and height of the
#		image file.

function uploadimage_get_url(
	array	&$tmp_img,
	int		$flags		= 0,
	int		$max_width	= 0,
	?int	$dpr		= null
): string {
	# NOTE: get_rounded_dpr() only when needed.
	#		Does all kinds of stuff to get that value, which might not be necessary.
	if (empty($tmp_img['FORMAT'])) {
		if ($max_width) {
			$tmp_w = $tmp_img['ORIG_WIDTH'];
			$tmp_h = $tmp_img['ORIG_HEIGHT'];
			scale($tmp_w, $tmp_h, $max_width);
			$format = $tmp_w.'x'.$tmp_h;

		} elseif ($flags & UPIMG_GET_ORIGINAL) {
			$format = ($tmp_w = $tmp_img['ORIG_WIDTH']).'x'.($tmp_h = $tmp_img['ORIG_HEIGHT']);

		} elseif ($flags & UPIMG_PREVIEW) {
			$dpr ??= get_rounded_dpr();
			$tmp_w = 0;
			$tmp_h = $dpr * UPIMG_PREVIEW_HEIGHT;
			scale_fill($tmp_img['ORIG_WIDTH'], $tmp_img['ORIG_HEIGHT'], $tmp_w, $tmp_h);
			$format = $tmp_w.'x'.$tmp_h;

		} else {
			$dpr ??= get_rounded_dpr();
			$tmp_w = $tmp_img['WIDTH'];
			$tmp_h = $tmp_img['HEIGHT'];

			if ($dpr > 1
			&&	$tmp_img['ORIG_WIDTH'] > $tmp_w
			) {
				if ($tmp_img['ORIG_WIDTH'] >= $tmp_w * $dpr) {
					$tmp_w = (int)round($tmp_w * $dpr);
					$tmp_h = (int)round($tmp_h * $dpr);
				} else {
					$tmp_h = min($tmp_img['ORIG_HEIGHT'], (int)round($tmp_h * $tmp_img['ORIG_WIDTH'] / $tmp_w));
					$tmp_w = $tmp_img['ORIG_WIDTH'];
				}
			}
			$format = $tmp_w.'x'.$tmp_h;
		}
	} else {
		$type = $tmp_img['FORMAT'][0];
		[, $s, $full_s] = $tmp_img['FORMAT'];
		if ($max_width) {
			$s = min($s, $max_width);
		} elseif ($flags & UPIMG_GET_ORIGINAL) {
			$s = $full_s;
		} else {
			if ($flags & UPIMG_PREVIEW) {
				$s = min($s, UPIMG_PREVIEW_HEIGHT);
			}
			$dpr ??= get_rounded_dpr();
			if ($dpr > 1
			&&	$tmp_img['ORIG_WIDTH'] > $s
			) {
				if ($tmp_img['ORIG_WIDTH'] >= $s * $dpr) {
					$s = (int)round($dpr * $s);
				} else {
					$s = $tmp_img['ORIG_WIDTH'];
				}
			}
		}
		$format = match ($type) {
			'manualcrop'	=> "manualcrop_{$tmp_img['FORMAT'][3]}",
			#'smartcrop'		=> "{$s}x$s",
			'smartcrop',
			'pillarbox',
			'letterbox'		=> "{$type}_$s",
		};
		$tmp_w = $s;
		$tmp_h = $s;
	}
	$tmp_img['INTRINSIC_WIDTH']  = $tmp_w;
	$tmp_img['INTRINSIC_HEIGHT'] = $tmp_h;
	$definitive = $flags & UPIMG_DEFINITIVE ? '?definitive' : '';
	return "{$tmp_img['URL_PREFIX']}$format{$tmp_img['URL_POSTFIX']}$definitive";
}

function uploadimages_get_with_fallback(
	string				$element,
	int					$id,
	int					$flags			 		= 0,
	string				$size					= '',
	array|string|null	$orientation_preference = null,
): array|false {
	$result = [];
	if ($img = uploadimage_get($element, $id, $size, $flags, $orientation_preference)) {
		/** @noinspection NotOptimalIfConditionsInspection */
		if (($haveimg = have_uploadimage($img))
		||	!($flags & UPIMG_NOCHANGE)
		) {
			$result[] = $img;
		}
		if ($haveimg) {
			return $result;
		}
	}
	$flags |= UPIMG_SHOW_ELEMENT;
	$fallbacks = [];
	switch ($element) { // NOSONAR
	case 'contest':
		if (($contest = memcached_contest($id))
		&& 	$contest['PARTYID']
		&&	($party = memcached_party_and_stamp($contest['PARTYID']))
		) {
			$fallbacks['party'] = [$contest['PARTYID']];
			if ($party['ORGIDS']) {
				$fallbacks['organization'] = explode(',',$party['ORGIDS']);
			}
			if ($party['LOCATIONID']) {
				$fallbacks['location'] = [$party['LOCATIONID']];
			}
		}
		break;

	case 'report':
		if (($report = memcached_report($id))
		&&	$report['PARTYID']
		) {
			$fallbacks['party'] = [$report['PARTYID']];
		}
		break;

	case 'party':
		require_once '_partyimage.inc';
		[	$front_image,
			$back_image,
			$alternative_image
		] = get_front_and_back_flyer(memcached_party_and_stamp($id), $size, $flags, $orientation_preference);
		if (have_uploadimage($front_image)) {
			$result = [$front_image];
		} elseif (have_uploadimage($back_image)) {
			$result = [$back_image];
		} elseif (have_uploadimage($alternative_image)) {
			$result = [$alternative_image];
		} else {
			$result = [];
		}
		break;

	case 'news':
	case 'promo':
	case 'promoteaser':
		require_once '_connect.inc';
		if ($element === 'promoteaser') {
			$element = 'promo';
		}
		if (!($connections = get_connections($element, $id))
		||	!isset($connections['party'])
		) {
			return $result;
		}
		safe_shuffle($connections['party']);
		$fallbacks['party'] = $connections['party'];
		break;

	case 'lineup':
		if ($artistid = db_single('lineup', '
			SELECT ARTISTID
			FROM lineup
			WHERE LINEUPID = '.$id
		)) {
			$fallbacks['artist'] = [$artistid];
		}
		break;

	case 'orgashost':
		if ($organizationid = db_single('organization', 'SELECT ORGANIZATIONID FROM organization WHERE ORGANIZATIONID = '.$id)) {
			$fallbacks['organization'] = [$organizationid];
		}
		break;

	case 'locashost':
		if ($locationid = db_single('loation', 'SELECT LOCATIONID FROM location WHERe LOCATIONID = '.$id)) {
			$fallbacks['location'] = [$locationid];
		}
		break;
	}

	if ($fallbacks) {
		foreach ($fallbacks as $tmpelement => $tmpids) {
			$elems = $tmpelement === 'party' ? ['party', 'party2'] : [$tmpelement];
			foreach ($elems as $type) {
				foreach ($tmpids as $tmpid) {
					if (false !== ($img = uploadimage_get($type, $tmpid, $size, $flags, $orientation_preference))
					&&	assert(is_array($img))
					&&	have_uploadimage($img)
					) {
						$img['NOCHANGE'] = true;
						$img['FALLBACK'] = true;
						$result[] = $img;
						break 3;
					}
				}
			}
		}
	}
	return array_reverse($result);
}

function uploadimage_show_with_fallback(
	string			  $element,
	int				  $id,
	?int			  $flags				  = null,
	?string			  $size					  = null,
	?string			  $open_tag				  = null,
	?string			  $close_tag			  = null,
	string|array|null $orientation_preference = null,
	?int			  $max_width			  = null,
	?string			  $loading				  = null,
): ?array {
	$flags 		   ??= 0;
	$size		   ??= '';
	$open_tag 	   ??= '';
	$close_tag	   ??= '';
	$max_width	   ??= 0;
	$loading	   ??= 'eager'; # uploadimage are generally at the top of the page
	if (!($imgs = uploadimages_get_with_fallback(
		$element,
		$id,
		$flags,
		$size,
		$orientation_preference
	))) {
		return null;
	}
	usort($imgs, static fn(array $a, array $b): int => (empty($a['WIDTH']) ? 1 : 0) <=> (empty($b['WIDTH']) ? 1 : 0));
	# NOTE: Multiple images happens for example for news message connect to an event but without explicit upload image.
	#		The first $img in $imgs are the ones from the event. Square, landscape, portrait, all of the in $img['IMGS'].
	#		The second is an 'empty' upload image for the news message itself. Show ing that, yield a button
	#		with 'uploadimage' add.
	foreach ($imgs as $img) {
		$backup_flags = $flags;
		if (isset($img['NOCHANGE'])) {
			$flags |= UPIMG_NOCHANGE;
			$flags &= ~(UPIMG_FORCE_CROP | UPIMG_FORCE_REMOVE);
		}
		uploadimage_show_from_img(
			img:		$img,
			flags:		$flags,
			open_tag:	$open_tag,
			close_tag:	$close_tag,
			max_width:	$max_width,
			loading:	$loading
		);
		$flags = $backup_flags;
	}
	return $img;
}

function have_uploadimage(string|array $arg, int $id = 0): bool|int|null {
	if (!$id) {
		# img object as argument
		return !empty($arg['UPIMGID']);
	}
	# element,id as argument (used in duplicate party routine)
	$types = [$arg];
	if ($arg === 'party') {
		$types[] = 'party2';
	}
	return db_single_int('uploadimage_link', '
		SELECT COUNT(*)
		 FROM uploadimage_link
		 WHERE TYPE IN ('.stringsimplode(', ', $types).')
		   AND ID = '.$id
	);
}

function uploadimage_show(
	string	$type,
	int		$id,
	int		$flags 		= 0,
	string	$size		= '',
	string	$opentag	= '',
	string	$closetag	= '',
	string	$loading	= 'eager',
): array|false {
	if ($size === 'tiny') {
		$flags |= UPIMG_NOCHANGE;
	}
	if ($img = uploadimage_get($type, $id, $size, $flags)) {
		uploadimage_show_from_img(
			$img,
			$flags,
			$opentag,
			$closetag,
			loading: $loading
		);
	}
	return $img;
}

function uploadimage_show_from_img(
	array		&$img,
	int			$flags		= 0,
	?string		$open_tag	= null,
	?string		$close_tag	= null,
	float|int	$max_width	= 0,
	float|int	$max_height	= 0,
	string		$loading 	= 'eager',
): bool {
	if (!$img) {
		return false;
	}
	if (!($haveimg = !empty($img['UPIMGID']))
	&&	($flags & UPIMG_NOCHANGE)
	) {
		return false;
	}
	static $__hidden = null;

	$element = in_array($img['LINKTYPE'], ['party','party2'], strict: true) ? 'party' : $img['LINKTYPE'];
	$id = $img['ID'];

	$__hidden[$element][$id] ??=
			$haveimg
		&&	$element === 'party'
		&&	(require_once '_delayedflyers.inc')
		&&	(	($delayed = is_delayed_flyer($img['UPIMGID']))
			||	 $delayed ===  null);

	if (true === ($hidden_or_delayed = $__hidden[$element][$id])) {
		# is_hidden
		return false;
	}
	if ($hidden_or_delayed
	&&	!have_admin('party')
	) {
		# delayed
		return false;
	}
	$may_change
	=	$img['MAY_CHANGE']
	&&	!($flags & UPIMG_NOCHANGE)
	&&	!NO_ALTER_UPLOADIMAGE;

	$abs = $flags & UPIMG_ABS;

	if (!empty($img['NEED_CROP'])) {
		$flags |= UPIMG_SHOW_CROP;
	}
	?><div class="relative uimg clear center<?
	if ($flags & UPIMG_R) {
		?> right-float rclr<?
	}
	if ($hidden_or_delayed) {
		?> light<?
	}
	?>"<?
	?> data-linktype="<?= $img['TYPE'] ?>"<?
	?> data-linkid="<?= $img['ID'] ?>"<?
	if (!empty($img['UPIMGID'])) {
		?> data-default="<?= $img['UPIMGID'] ?>"<?
	}
	?> data-flags="<?= $flags ?>"<?
	?>><?

	echo $open_tag;
	if ($haveimg) {
		uploadimage_show_actual($img, $flags, $max_width, $max_height, $loading);
	}
	echo $close_tag;

	if (!empty($img['CSTAMP'])
	&&	have_admin(is_uploadimage_type($img['TYPE']))
	) {
		?><div class="light small date"><? show_date($img) ?></div><?
	}
	if ($may_change) {
		include_style('uploadimage');
		if (!empty($img['IMGS'])) {
			include_js('js/form/uploadimage');
			$generated = false;
			?><div class="small uimg-selector center"><?
			foreach ($img['IMGS'] as $tmp_img) {
				if ($tmp_img['UPIMGID'] === $img['UPIMGID']
				&&	 empty($tmp_img['GENERATED'])
				||	!empty($tmp_img['ASPECT_CROP'])
				) {
					continue;
				}
				$tmp_img = [...$img, ...$tmp_img];
				if ($generated !== !empty($tmp_img['GENERATED'])) {
					?><hr class="slim"><?
				}
				$generated = !empty($tmp_img['GENERATED']);
				uploadimage_show_with_chooser($tmp_img,$flags | UPIMG_PREVIEW | ($tmp_img['NEED_CROP'] ?? 0));
			}
			?></div><?
		}
	}
	if ($hidden_or_delayed) {
		[,,, $hour, $mins] = _getdate($hidden_or_delayed);
		$func = $hour || $mins ? '_datedaytime_display' : '_dateday_display';
		ob_start();
		$func($hidden_or_delayed);
		$date = ob_get_clean();
		$info = __('party:info:flyer_release_LINE',['DATE'=>$date]);
		if (!$abs) {
			?><div class="inf"><?= $info ?></div><?
		}
	}

	$parts = [];

	if (($flags & UPIMG_SHOW_SEARCH)
	&&	$img['TYPE'] === 'artist'
	&&	have_admin('artist')
	) {
		global $__year, $__month, $__day;
		if ($artist = db_single_assoc('artist', '
			SELECT NAME, REALNAME, TYPE
			FROM artist
			WHERE ARTISTID = '.$img['ID'])
		) {
			$q = ($artist['TYPE'] ? str_replace(',', ' ', $artist['TYPE']).' ' : '').
				"'{$artist['NAME']}' -site:gettyimages.*";
			ob_start();
			?><a target="_blank" href="<?
			?>//www.google.com/search<?
			?>?q=<?=  urlencode($q)
			?>&amp;es_sm=93<?
			?>&amp;biw=1504<?
			?>&amp;bih=1010<?
			?>&amp;source=lnt<?
			?>&amp;tbs=isz%3Alt%2Cislt%3Axga%2Ccdr%3A1%2Ccd_min%3A<?= $__month ?>%2F<?= $__day ?>%2F<?= $__year - 2 ?>%2Ccd_max%3Anow,imgo:1<?
			?>&amp;tbm=isch#facrc=_<?
			?>&amp;imgdii=_<?
			?>"><?= __('action:search_image') ?></a><?
			$parts[] = ob_get_clean();
		}
	}
	if ($history_counts = get_history_counts($img, $flags)) {
		ob_start();
		show_history_link($img, $history_counts);
		$parts[] = ob_get_clean();
	}

	ob_start();
	if ($abs && $hidden_or_delayed) {
		?><img<?
		?> loading="<?= $loading ?>"<?
		?> class="icon lower help-cursor"<?
		?> src="<?= STATIC_HOST ?>/images/info<?= is_high_res() ?>.png"<?
		?> alt="<?= $info ?>"<?
		?> title="<?= $info ?>"> <?
	}
	if ($may_change) {
		?><a href="/upload/form/<?= $img['LINKTYPE'] === 'party2' ? 'party' : $img['LINKTYPE'] ?>/<?= $img['PARENTID'] ?>"><?

		if ($abs) {
			echo get_special_char('change');
		} else {
			echo __('uploadimage:add');
		}
		?></a><?
	}
	if ($data = ob_get_clean()) {
		if ($parts) {
			?><div class="acts"><?
			?><div class="smenu ib lmrgn"><?= $data ?></div><?
			?><div><?= implode(' '.MIDDLE_DOT_ENTITY.' ',$parts) ?></div><?
			?></div><?
		} else {
			?><div class="acts smenu"><?= $data ?></div><?
		}
	} elseif ($parts) {
		?><div class="acts"><?= implode(' '.MIDDLE_DOT_ENTITY.' ', $parts)  ?></div><?
	}
	?></div><?

	return $haveimg ?: false;
}

function uploadimage_show_with_chooser(array $tmp_img, int $flags): void {
	?><div<?
	if (!empty($tmp_img['GENERATED'])) {
		?> onclick="Pf.chooseGenerated(this, <?= $tmp_img['UPIMGID'] ?>, '<?= $tmp_img['GENERATED'] ?>');"<?
	}
	?> class="uimg-chooser <?
	if (!empty($tmp_img['GENERATED'])) {
		?>uimg-generated <?
		if (!empty($tmp_img['CHOSEN'])) {
			?>uimg-generated-chosen <?
		}
	}
	?>ib"><?
		if (!empty($tmp_img['GENERATED'])) {
			?><div><?= $tmp_img['GENERATED'] === 'crop' ? 'smartcrop' : $tmp_img['GENERATED'] ?></div><?
		}

		?><div<?
		if (empty($tmp_img['GENERATED'])) {
			?> onclick="Pf_chooseDefaultImage(this, <?= $tmp_img['UPIMGID'] ?>);"<?
		}
		?>><?
		uploadimage_show_actual($tmp_img, UPIMG_PREVIEW | UPIMG_SHOW_DIMS | $flags | ($tmp_img['NEED_CROP'] ?? 0));
		?></div><?

	if (!empty($tmp_img['CSTAMP'])
	&&	empty($tmp_img['GENERATED'])
	) {
		?><div class="small light ib"><? show_short_date($tmp_img) ?></div><?
	}
	?></div><?
}

function uploadimage_show_actual(
	// #[ArrayShape]
	array		&$image,
	int			$flags		= 0,
	float|int	$max_width	= 0,
	float|int	$max_height	= 0,
	string		$loading	= 'eager',
): void {
	require_once '_urltitle.inc';
	require_once '_smallscreen.inc';
	require_once '_hosts.inc';
	/** @noinspection PhpRedundantOptionalArgumentInspection */
	extract($image, \EXTR_OVERWRITE);

	if ($FILETYPE === 'swf') {
		if (!ROBOT) {
			echo '<div class="error">FLASH/SWF not supported.</div>';
		}
		return;
	}
	if ($flags & UPIMG_SCHEMA) {
		# show original
		?><meta itemprop="image" content="<?= FULL_IMAGES_HOST, uploadimage_get_url($image, UPIMG_GET_ORIGINAL) ?>" /><?
	}
	$allow_change =
		!empty($MAY_CHANGE)
	&&	!($flags & UPIMG_NOCHANGE)
	&&	!NO_ALTER_UPLOADIMAGE;

	$crop =	(!iOS && !ANDROID && !SMALL_SCREEN)
		&&	(	$allow_change
			&&	($flags & UPIMG_SHOW_CROP)
			||	($flags & UPIMG_FORCE_CROP)
			)
		&&	empty($image['HAVE_SQUARE'])
		&&	$ORIENTATION !== 'square'
		&&	$allow_change;

	if ($relative
	=	$allow_change
	||	!empty($COPYRIGHT)
	||	$BUSY
	||	($flags & (UPIMG_SHOW_DIMS | UPIMG_FORCE_REMOVE | UPIMG_SHOW_FALLBACK))
	||	$crop
	||	!empty($DEFAULT)
	) {
		?><div class="<?
		?>uimg-container <?
		?>ib relative middle"<?
		?> data-upimgid="<?= $UPIMGID ?>"<?
		?>><?
		if ((	$allow_change
			||	($flags & UPIMG_FORCE_REMOVE)
			)
		&&	empty($AUTO)) {
			show_remove_icon($image);
		}
		if (!empty($MAY_CHANGE)
		||	$flags & (UPIMG_SHOW_DIMS | UPIMG_SHOW_ELEMENT)
		) {
			show_orig_dims($image, $flags);
		}
		if (($flags & UPIMG_SHOW_FALLBACK)
		&&	!empty($image['FALLBACK'])
		) {
			?><div class="fallback">fallback</div><?
		}
		if (empty($GENERATED)) {
			show_copyright($image, $allow_change);
			if ($allow_change) {
				show_no_person($image);
			}
		}
		if ($crop) {
			# want at least 500 px squares
			$s = min($image['ORIG_WIDTH'], $image['ORIG_HEIGHT']);
			if ($s <= UPIMG_MINIMUM_SQUARE) {
				$mw = 1;
				$mh = 1;
			} else {
				$w_part = UPIMG_MINIMUM_SQUARE / $image['ORIG_WIDTH'];
				$h_part = UPIMG_MINIMUM_SQUARE / $image['ORIG_HEIGHT'];

				if ($w_part * $image['WIDTH' ] < 50
				||	$h_part * $image['HEIGHT'] < 50
				) {
					# need 50px to grab cropper
					$mw = max(50 / $image['WIDTH'], 50 / $image['HEIGHT']);
					$mh = $mw;
				} else {
					$mw = $w_part;
					$mh = $h_part;
				}
			}

			?><div<?
			?> class="crop-button"<?
			?> onclick="Pf.startCrop(event, this, <?= $image['UPIMGID'] ?>, <?= $mw ?>, <?= $mh ?>);"<?
			?>><div><?= WHITE_SQUARE_WITH_UPPER_LEFT_QUADRANT_ENTITY ?></div><?
			?></div><?
		}
	}
	if ($linked =
		$flags & UPIMG_LINK_ORIGINAL
	&&	(	$image['TYPE'] === 'eventmap'
		||	have_admin(is_uploadimage_type($image['TYPE']))
		)
	) {
		?><a<?
		if ($flags & UPIMG_SWAPPER) {
			?> onclick="return Pf_clickPartyFlyer(this);"<?
		}
		?> target="_blank"<?
		?> title="<?= __C('action:view_large_version') ?>"<?
		?> href="<?= IMAGES_HOST, uploadimage_get_url($image, UPIMG_GET_ORIGINAL) ?>"><?
	}

	if (# reload when busy => done
		$BUSY
		# reload if uploader within 10 seconds
	||	CURRENTUSERID === $image['USERID']
	&&	CURRENTSTAMP < $image['CSTAMP'] + 10
		# reload when result of POST and have FILES
	||	have_post()
	&&	!empty($_FILES)
	&&	have_files()
	) {
		include_js('js/refreshupload');
		include_style('spinners');

		?><div<?
		?> class="centerspin refreshimg"<?
		?> data-upimgid="<?= $UPIMGID ?>"<?
		?>><?
		?><div class="barcontainer"><?
		?><div class="circleloader"><?
		?><div class="bounce"></div><?
		?></div><?
		?></div><?
		?></div><?
	}
	if ($flags & UPIMG_PREVIEW) {
		$WIDTH	= 0;
		$HEIGHT	= UPIMG_PREVIEW_HEIGHT;
		scale_fill($image['ORIG_WIDTH'], $image['ORIG_HEIGHT'], $WIDTH, $HEIGHT);
	}
	if ($max_width) {
		scale($WIDTH, $HEIGHT, $max_width);
	}
	if ($max_height
	&&	$max_height < $HEIGHT
	) {
		scale($WIDTH, $HEIGHT, 0, $max_height);
	}
	$base_src = IMAGES_HOST.uploadimage_get_url($image, $flags, $max_width);
	$srcset = [];
	foreach (explode(',', $VERSIONS) as $dims) {
		[$width/*, $height */] = int_explode('x', $dims);
		if ($width <= $WIDTH) {
			continue;
		}
		ob_start();
		echo IMAGES_HOST, uploadimage_get_url($image, $flags, $width);
		?> <?= $width / $WIDTH ?>x<?
		$srcset[] = ob_get_clean();
	}
	?><img<?
	?> loading="<?= $loading ?>"<?
	show_uploadimage_alt($image);
	?> class="<?= $relative ? 'ib' : 'middle' ?> the-uimg"<?
	if ($crop) {
    	?> data-intrinsic-width="<?=  $image['INTRINSIC_WIDTH' ] ?>"<?
		?> data-intrinsic-height="<?= $image['INTRINSIC_HEIGHT'] ?>"<?
	}
	?> src="<?= $base_src ?>"<?
	?> srcset="<?= implode(', ', $srcset) ?>"<?
	if (aspect_objects()) {
		?> width="<?=  $WIDTH  ?>"<?
		?> height="<?= $HEIGHT ?>"<?
	}
	?> style="<?
	if (aspect_objects()) {
		if ((SMALL_SCREEN || ($flags & UPIMG_MAX_WIDTH))
		&&	!$max_width
		&&	!$max_height
		) {
			?>max-width: 95%;<?
		} else {
			?>max-width: 100%;<?
		}
		?> width: <?= $WIDTH ?>px; height: auto;<?
	} elseif (!SMALL_SCREEN) {
		?>width: 100%; max-width: <?= $WIDTH ?>px;<?
	} else {
		?>width: <?= $WIDTH ?>px;<?
		if ($flags & UPIMG_MAX_WIDTH) {
			if (!$max_width && !$max_height) {
				?> max-width: 95%;<?
			}
		} else {
			?>height: <?= $HEIGHT ?>px;<?
		}
	}
	?>" /><?
	if ($linked) {
		?></a><?
	}
	if ($relative) {
		?></div><?
	}
	$image['SHOWN_WIDTH']  = $WIDTH;
	$image['SHOWN_HEIGHT'] = $HEIGHT;
	if (!($flags & UPIMG_PREVIEW)
	&&	!($flags & UPIMG_NO_OPENGRAPH)
	) {
		foreach ($image['IMGS'] as $tmp_img) {
			if (!empty($tmp_img['GENERATED'])) {
				continue;
			}
			include_og('og:image', FULL_IMAGES_HOST.uploadimage_get_url($tmp_img, UPIMG_GET_ORIGINAL));
			include_og('og:image:width',  $tmp_img['ORIG_WIDTH']);
			include_og('og:image:height', $tmp_img['ORIG_HEIGHT']);
		}
	}
}

function have_uploadimage_type(array $arr, string $field): string|false {
	return have_element($arr, $field, is_uploadimage_type());
}

function require_uploadimage_type(array $arr, string $field): string|false {
	return require_element($arr, $field, is_uploadimage_type());
}

function uploadimage_remove_generated(string $type, int $id, int $upimgid, string $generated): bool {
	# generated always manualcrop
	$where_part = "UPIMGID = $upimgid
				  AND TYPE = '$type'
				  AND ID   = $id";
	return db_insert('uploadimage_generated_log', '
		INSERT INTO uploadimage_generated_log
		SELECT *, UNIX_TIMESTAMP(), '.CURRENTUSERID."
		FROM uploadimage_generated
		WHERE `GENERATED` = '$generated'
		  AND $where_part")
	&&	db_delete('uploadimage_generated', "
		DELETE FROM uploadimage_generated
		WHERE `GENERATED` = '$generated'
		  AND $where_part")
	&&	(	$generated !== 'manualcrop'
		||	db_insert('uploadimage_crop_log', '
			INSERT INTO uploadimage_crop_log
			SELECT *, UNIX_TIMESTAMP(), '.CURRENTUSERID."
			FROM uploadimage_crop
			WHERE $where_part")
		&&	db_delete('uploadimage_crop', "
			DELETE FROM uploadimage_crop
			WHERE $where_part"));
}

function uploadimage_removelink(
	string	$element,
	int		$id,
	?string $parentelement	= '',
	?int	$parentid		= 0,
	int		$upimgid		= 0,
	bool	&$refresh		= false,
	bool	$deactivate		= false,
): bool {
	$where_part = "TYPE = '$element' AND ID = $id";
	if ($upimgid) {
		$where_part .= " AND UPIMGID = $upimgid";
	}
	if ($parentelement) {
		$where_part .= " AND PARENTELEMENT = '$parentelement' ";
	}
	if ($parentid) {
		$where_part .= " AND PARENTID = $parentid";
	}
	if (false === ($is_square = db_single_int(['uploadimage', 'uploadimage_link'], "
		SELECT 1
		FROM uploadimage_link
		JOIN uploadimage USING (UPIMGID)
		WHERE ORIENTATION = 'square'
		  AND $where_part"))
	) {
		return false;
	}
	if ($is_square) {
		$refresh = true;
	}
	if (!db_delete('uploadimage_link', "
		DELETE FROM uploadimage_link
		WHERE $where_part")
	||	!db_update('uploadimage_link_log', '
		UPDATE uploadimage_link_log SET
			DSTAMP	= UNIX_TIMESTAMP(),
			DUSERID	= '.CURRENTUSERID.
			($deactivate ? ', ACTIVE = 0' : '')."
		WHERE DSTAMP = 0
		  AND $where_part")
	) {
		return false;
	}
	if (!$upimgid) {
		return true;
	}
	if (false === ($have_default = db_single('uploadimage_link', "
		SELECT 1
		FROM uploadimage_link
		WHERE `DEFAULT` = 1
		  AND $where_part",
		DB_FORCE_MASTER))
	) {
		return false;
	}
	if ($have_default) {
		# have default, so no need to choose new one
		return true;
	}
	# choose new default

	if (!($newest = db_single(['uploadimage_link', 'uploadimage'], "
		SELECT UPIMGID
		FROM uploadimage_link
		JOIN uploadimage USING (UPIMGID)
		WHERE $where_part
		ORDER BY CSTAMP DESC
		LIMIT 1"
	))) {
		# !$newwest and $newest !== false => no remaining
		return $newest !== false;
	}
	if (!db_update('uploadimage_link', "
		UPDATE uploadimage_link SET
			`DEFAULT` = 1
		WHERE UPIMGID = $newest
		  AND $where_part")
	) {
		return false;
	}
	$refresh = true;
	return true;
}
function uploadimage_get_upimgids(
	string	$element,
	int		$id,
	string	$parentelement	= '',
	int		$parentid		= 0,
	int		$upimgid		= 0,
): array|false|null {
	if (!$upimgid) {
		# return any uploadimage
		return db_simpler_array('uploadimage_link', "
			SELECT DISTINCT UPIMGID
			FROM uploadimage_link
			WHERE TYPE = '$element'
			  AND ID = $id
			  AND PARENTELEMENT = '$parentelement'
			  AND PARENTID = $parentid",
			DB_FORCE_MASTER
		);
	}
	# return uploadimage with same orientation
	return	db_simpler_array('uploadimage_link', "
		SELECT DISTINCT l.UPIMGID
		FROM uploadimage_link l
		JOIN uploadimage new
		  ON new.UPIMGID = $upimgid
		JOIN uploadimage stored
		  ON stored.UPIMGID = l.UPIMGID
		 AND stored.ORIENTATION = new.ORIENTATION
		WHERE l.TYPE = '$element'
		  AND l.ID = $id
		  AND l.PARENTELEMENT = '$parentelement'
		  AND l.PARENTID = $parentid",
		DB_FORCE_MASTER
	);
}

function process_upload_form(): void {
	# process both front side (null) and potential back side of flyers (2)
	foreach (['', '2', 'EVENTMAP'] as $type_index) {
		process_upload_url ($type_index);
		process_upload_data($type_index);
	}
}

function process_upload_data(string $type_index = ''): void {
	$key = 'DATA'.$type_index;
	if (!isset($_POST[$key])
	||	!is_array($_POST[$key])
	) {
		return;
	}
	# make sure we don't overwrite anything:
	$add_index =
		count($_FILES['FILE'.$type_index]['name'])
	+	(isset($_POST['COPYRIGHT'.$type_index]) ? count($_POST['COPYRIGHT'.$type_index]) : 0)
	+	(isset($_POST['NO_PERSON'.$type_index]) ? count($_POST['NO_PERSON'.$type_index]) : 0);

	foreach ($_POST[$key] as $i => $data) {
		if (!preg_match('"^(?<data>data:(?<mimetype>[^;]+);base64,)"', $data, $match)) {
			mail_log('process_upload_data: data not understood: '.substr($data, 0, 100));
			continue;
		}
		$mimetype = $match['mimetype'];
		$data = substr($data, strlen($match['data']));
		if (!($decoded = base64_decode($data))) {
			mail_log('process_upload_data: decode of data failed: '.substr($data, 0, 100));
			continue;
		}
		if (!$tmp_file = tempnam('/tmpdisk', '__data_for_upload_')) {
			continue;
		}
		unlink_at_end($tmp_file);

		if (!file_put_contents($tmp_file, $decoded)) {
			continue;
		}
		foreach ([
			'name'		=> $_POST['NAME'.$type_index][$i] ?? null,
			'type'		=> $mimetype,
			'size'		=> $size = filesize($tmp_file),
			'tmp_name'	=> $tmp_file,
			'error'		=> $size ? UPLOAD_ERR_OK : UPLOAD_ERR_NO_FILE
		] as $file_key => $file_val) {
			$_FILES['FILE'.$type_index][$file_key][$add_index] = $file_val;
		}
		if ($copyright = $_POST['COPYRIGHT'.$type_index][$i] ?? null) {
			$_POST['COPYRIGHT'.$type_index][$add_index] = $copyright;
		}
		if ($no_person = $_POST['NO_PERSON'.$type_index][$i] ?? null) {
			$_POST['NO_PERSON'.$type_index][$add_index] = $no_person;
		}
		++$add_index;
	}
	unset($_POST[$key]);
}

function process_upload_url(string $type_index = ''): void {
	$key = 'URL'.$type_index;
	if (!isset($_POST[$key])
	||	!is_array($_POST[$key])
	) {
		return;
	}
	# make sure we don't overwrite anything:
	$add_index =
		(isset($_FILES['FILE'.$type_index]['name']) ? count($_FILES['FILE'.$type_index]['name'])	: 0)
	+	(isset($_POST['COPYRIGHT'.$type_index])		? count($_POST['COPYRIGHT'.$type_index])		: 0)
	+	(isset($_POST['NO_PERSON'.$type_index])		? count($_POST['NO_PERSON'.$type_index])		: 0);

	require_once 'defines/uploadimage.inc';

	foreach ($_POST[$key] as $i => $url) {
		$url = mytrim(strip_shy($url));
		if (empty($url)) {
			continue;
		}
		if (!FORCE_PROCESSING
		&& ($upimgid = get_upimgid_from_url($url))
		) {
			foreach ([
				'name'		=> $url,
				'type'		=> null,
				'size'		=> null,
				'tmp_name'	=> null,
				'error'		=> UPLOAD_ERR_OK,
				'upimgid'	=> $upimgid,
			] as $file_key => $file_val) {
				$_FILES['FILE'.$type_index][$file_key][$add_index] = $file_val;
			}
			if ($copyright = $_POST['COPYRIGHT'.$type_index][$i] ?? null) {
				$_POST['COPYRIGHT'.$type_index][$add_index] = $copyright;
			}
			if ($no_person = $_POST['NO_PERSON'.$type_index][$i] ?? null) {
				$_POST['NO_PERSON'.$type_index][$add_index] = $no_person;
			}
			++$add_index;
			continue;
		}
		if (!$tmp_file = tempnam('/tmpdisk','url_download_')) {
			continue;
		}
		#error_log("DEBUG4 register_shutdown_function for register_shutdown_function for unlink $tmp_file");
		register_shutdown_function(static function(string $tmp_file): void {
			#error_log("DEBUG4 inside shutdown_function register_shutdown_function for unlink $tmp_file");
			# need to be absolute last, due to processing in background
			register_shutdown_function(function(string $file_to_unlink): void {
				#error_log("DEBUG4 unlink $file_to_unlink");
				unlink($file_to_unlink);
			}, $tmp_file);
		},$tmp_file);

		# NOTE: these partyflock urls are now directly supported because they are imported
		#	by the browser
		if (preg_match('"^(.*?//)(?:(vip|album|dump)\.)?partyflock\.nl(/.*)$"i', $url, $match)) {
			if (!($dst = fopen($tmp_file, 'wb'))) {
				continue;
			}
			if (preg_match('"^/attachment/(?<id>\d+)/"', $match[3], $attachment_match)) {
				$att = db_single_assoc(	['contact_ticket_attachment', 'contact_ticket_attachment_meta', 'contact_ticket_message'], "
					SELECT FILENAME, COMPRESSED, MIMETYPE, ctm.CSTAMP, DATAID, TICKETID, SIZE, WITHUSERID, OWNERID, ELEMENT, ID
					FROM contact_ticket_attachment AS cta
					JOIN contact_ticket_message AS ctm USING (CTMSGID)
					JOIN contact_ticket_attachment_meta USING (DATAID)
					JOIN contact_ticket USING (TICKETID)
					WHERE CONTACT_TICKET_ATTACHMENTID = {$attachment_match['id']}"
				);
				require_once '_contactrequire.inc';
				if ($att
				&&	(	CURRENTUSERID === $att['WITHUSERID']
					||	CURRENTUSERID === $att['OWNERID']
					||	require_ticket_lock($att['TICKETID'])
					)
				) {
					$data_field = $att['COMPRESSED'] ? 'UNCOMPRESS(DATA)' : 'DATA';
					if ($data = db_single('contact_ticket_attachment_data', "
						SELECT SQL_NO_CACHE $data_field
						FROM data_db.contact_ticket_attachment_data
						WHERE DATAID = {$att['DATAID']}"
					)) {
						fwrite($dst, $data);
					}
				}
				$file = null;
			} else {
				switch ($match[2]) {
				case 'album':
					$connect = SERVER_VIP ? 'webone' : 'localhost';
					$host = 'album.partyflock.nl';
					break;
				case 'dump':
					$connect = SERVER_VIP ? 'webone' : 'localhost';
					$host = 'dump.partyflock.nl';
					break;

				case 'vip':
				default:
					$connect = SERVER_SANDBOX ? 'partyflock.nl' : 'localhost';
					$host = 'partyflock.nl';
					break;
				}
				$context = stream_context_create([
					'http' => [
						'timeout' => 5,
						'header'  => "Host: $host",
					],
					'ssl' => [
						'peer_name'	 => $host,
					],
				]);
				$url = 'https://'.$connect.$match[3];
				$file = fopen($url, 'rb', false, $context);
			}
			if ($file) {
				require_once 'defines/storage_sizes.inc';
				while ($data = fread($file, 10 * MEGABYTE)) {
					fwrite($dst, $data);
				}
				fclose($file);
			}
			fclose($dst);
		} else {
			while (true) {
				if (!($data = safe_file_get_contents($url, [CURLOPT_HTTPHEADER => [ACCEPT_HEADER_FOR_IMAGE]], $info))) {
					register_error('uploadimage:error:no_data_TEXT', DO_NL2BR | DO_UBB, [
						'URL'=> $url,
						'ERR' => $info['http_code']
					]);
					continue 2;
				}
				break;
			}
			file_put_contents($tmp_file,$data);
		}
		require_once '_mimetype.inc';
		foreach ([
			'name'		=> $url,
			'type'		=> mimetype_file($tmp_file,$url),
			'size'		=> $size = filesize($tmp_file),
			'tmp_name'	=> $tmp_file,
			'error'		=> $size ? UPLOAD_ERR_OK : UPLOAD_ERR_NO_FILE,
			#
		] as $file_key => $file_val) {
			$_FILES['FILE'.$type_index][$file_key][$add_index] = $file_val;
		}
		if ($copyright = $_POST[$ndx = 'COPYRIGHT'.$type_index][$i] ?? null) {
			$_POST[$ndx][$add_index] = $copyright;
		}
		if ($no_person = $_POST[$ndx = 'NO_PERSON'.$type_index][$i] ?? null) {
			$_POST[$ndx][$add_index] = $no_person;
		}
		++$add_index;
	}
}

function uploadimage_process(
	string	$element,
	int		$id,
	string	$parentelement	= '',
	int		$parentid		= 0,
	?array	$info			= null,
	?string	$tmp			= null,
	?string	$name 			= null,
	bool	$no_person		= false,
): int {
	require_once '_imageprocessing.inc';
	require_once 'defines/uploadimage.inc';
	require_once 'defines/storage_sizes.inc';

	$quick = false;	# also set quick to false if we can connect using crc/len combo
	global $debug_upload_images, $dbgid;

	if (!$name) {
		mail_log('uploadimage_process(): $name is empty!', get_defined_vars());
		$name = '';
	}
	if ($upimgid = $info['upimgid'] ?? (!FORCE_PROCESSING ? get_upimgid_from_url($name) : null)) {
		# $found_upimgid = $info['upimgid'] ? 'upimgid found in \$info' : 'upimgid found in url: '.$name;
		goto got_upimgid; // NOSONAR
	}
	$upload = file_get_contents($tmp);
	[$width, $height/*, $type */] = $info;
	$length = strlen($upload);
	$sha512 = hash('sha512', $upload);
	$sha512_slashed = addslashes($sha512);

	if (!FORCE_PROCESSING) {
		if (false === ($upimgid = db_single_int('uploadimageorig', "
			SELECT UPIMGID
			FROM uploadimageorig
			WHERE SHA512 = '$sha512_slashed'
			LIMIT 1"))
		) {
			return 0;
		}
		if ($upimgid) {
			register_notice('uploadimage:notice:same_exists_connected_LINE');
			goto got_upimgid; // NOSONAR
		}
	}
	$sizetypes = get_sizetypes();
	$orientation = get_orientation($width, $height);
	$widths  = [];
	$heights = [];
	$generate_sizes = [];
	foreach ($sizetypes as $size_type => $max_pixels) {
		$widths [$size_type] = $width;
		$heights[$size_type] = $height;
		if (!$max_pixels) {
			$generate_sizes[$size_type] = [$widths[$size_type], $heights[$size_type]];
			continue;
		}
		scale_max_pixels(
			$widths[$size_type],
			$heights[$size_type],
			$max_pixels
		);
		if (!$widths [$size_type]
		||	!$heights[$size_type]
		) {
			register_error('uploadimage:error:too_small_LINE');
			return 0;
		}
		$generate_sizes[$size_type] = $widths[$size_type] * $heights[$size_type];
	}
	if (!($quick = !CLI
				&&	(	($width * $height) >= (320 * 240)
				||	filesize($tmp) >= 25 * KILOBYTE))
	) {
		set_memory_limit(GIGABYTE);
	}
	$debug_upload_images && error_log("$dbgid source width x height: $width x $height, source filesize: ".filesize($tmp)." {$_SERVER['REQUEST_URI']}")
						 && error_log("$dbgid NOTICE going to create new ".($quick ? 'quick' : 'permanent')." uploadimage for $element:$id @ {$_SERVER['REQUEST_URI']}");

	$immediate_generate_sizes = $generate_sizes;

	log_executes($element.':'.$id.':foreground:'.($quick_str = $quick ? 'quick' : 'best'));

	# NOTE:`This routine is called to make the default uploadimage sizes.
	# 		We lock for the entire procedure, so we make sure the non-quick
	#		variants are produced before any other routine needs them.
	#		images/upload.php uses the original, we want it to get the non-quick version of course.

	if (!lock_uploadimage_for_processing($element, $id, $quick_str)
	||	!($results = process_images($immediate_generate_sizes, $args = [
		'quick'			=> $quick,
		'info'			=> $info,
		'src'			=> $tmp,
		'source_data'	=> $upload,
		'allow_flash'	=> have_admin(is_uploadimage_type($element))]))
	) {
		return 0;
	}
	[$blobs, $filetype, /* $unique_size */, /* $identicals */, $info/*, $pixmap */] = $results;

	$lengths = [];
	foreach ($blobs as $size_type => $blob) {
		if (!$blob) {
			return 0;
		}
		$crcs	[$size_type] = $crc	   = crc32($blob);
		$lengths[$size_type] = $length = strlen($blob);

		if (!FORCE_PROCESSING) {
			if (false === ($upimgid = uploadimage_find_crclen_and_connect($element, $id, $crc, $length, $blob))) {
				return 0;
			}
			if ($upimgid) {
				$quick = false;
				# $found_upimgid = 'upimgid found when comparing blobs';
				goto got_upimgid; // NOSONAR
			}
		}
		require_once '_layout.inc';
		if (max_file_size_surpassed('uploadimage_data', 'DATA', $length)) {
			return 0;
		}
	}
	if (empty($crcs)
	||	!db_insert('uploadimage', "
		INSERT INTO uploadimage SET
			ORIENTATION	= '$orientation',
			BUSY		= ".($quick		? '1' : '0').',
			NO_PERSON	= '.($no_person ? '1' : '0').',
			USERID		= '.CURRENTUSERID.',
			MUSERID		= '.CURRENTUSERID.',
			CSTAMP		= '.CURRENTSTAMP.',
			MSTAMP		= '.CURRENTSTAMP.",
			FILETYPE	= '$filetype'")
	) {
		return 0;
	}
	$debug_upload_images && error_log("$dbgid created new ".($quick ? 'quick' : 'permanent')." uploadimage for $element:$id on time ".CURRENTSTAMP." @ {$_SERVER['REQUEST_URI']}");

	$upimgid = db_insert_id();

	if (!db_insert('uploadimageorig', "
		INSERT IGNORE INTO uploadimageorig SET
			SHA512	= '$sha512_slashed',
			UPIMGID	= $upimgid,
			SIZE	= $length")
	) {
		return 0;
	}
	$data_set_list = [];
	$meta_set_list = [];
	foreach ($crcs as $size_type => $crc) {
		$length = $lengths[$size_type];

		[$width, $height] = $immediate_generate_sizes[$size_type];

		$data_set_list[] = "($upimgid, '$size_type', '".addslashes($blobs[$size_type])."')";
		$meta_set_list[] = "($upimgid, '$size_type', $width, $height, $length, $crc)";

		# FIXME: Do a quick smartcrop so we can show the image in the right orientation
		// if ($orientation !== 'square'
		// &&	  $size_type === 'regular'
		// ) {
		// 	update_smart_crop_for_uploadimage(
		// 		$upimgid,
		// 		$pixmap,
		// 		'image/x-portable-pixmap',
		// 		min($width, $height)
		// 	);
		// }
	}
	if (($new_mstamp = time()) === CURRENTSTAMP) {
		++$new_mstamp;
	}
	/** @noinspection NotOptimalIfConditionsInspection */
	if (!db_insert('uploadimagemeta','
		INSERT INTO uploadimagemeta (UPIMGID, SIZE, WIDTH, HEIGHT, DATASIZE, CRC)
		VALUES '.implode(', ', $meta_set_list))
	||	!db_insert('uploadimage_data','
		INSERT INTO data_db.uploadimage_data (UPIMGID, SIZE, DATA)
		VALUES '.implode(', ', $data_set_list))
	#	If we've done the non-quick version in foreground, we're 'ready' now.
	||	!$quick
	&&	!db_update('uploadimage', '
		UPDATE uploadimage SET
			BUSY	= 0,
			MUSERID	= '.CURRENTUSERID.",
			MSTAMP	= $new_mstamp
		WHERE UPIMGID = $upimgid")
	) {
		return 0;
	}
	unset($data_set_list, $meta_set_list);

	$debug_upload_images && error_log("$dbgid created in foreground, ".($quick ? 'quick' : 'definitive')." version of uploadimage for $element:$id @ ".$_SERVER['REQUEST_URI']);

	got_upimgid:

	if (uploadimage_connect($upimgid, $element, $id, $parentelement, $parentid)) {
		register_notice(
			empty($info['upimgid'])
		?	'uploadimage:notice:processed_and_connected_LINE'
		:	'uploadimage:notice:same_exists_connected_LINE'
		);
	} else {
		register_error('uploadimage:error:processed_but_connect_failed_LINE');
	}

	clear_uploadimage_cache($element, $id);

	if (!$quick) {
		return $upimgid;
	}
	$debug_upload_images && error_log("$dbgid registering shutdown processing @ {$_SERVER['REQUEST_URI']}");

	register_shutdown_function(
		uploadimage_process_and_store(...),
		$element,
		$id,
		$generate_sizes,
		$args,
		$upimgid,
		$name,
	);
	return $upimgid;
}

function uploadimage_process_and_store(
	string	$element,
	int		$id,
	array	$generate_sizes,
	array	$args,
	int		$upimgid,
): bool {
	close_connection_for_background();

	log_executes("$element:$id:background:best");

	$start = microtime(true);
	unset($args['quick']);

	if (!($results = process_images($generate_sizes, $args, true))) {
		mail_log('process_images yielded no results');
		return false;
	}
	global $debug_upload_images, $dbgid;
	[$blobs] = $results;
	$crcs = [];
	$lens = [];
	foreach ($blobs as $size_type => $blob) {
		if (!$blob) {
			mail_log("blob for size_type $size_type is empty");
			return false;
		}
		$crcs[$size_type] = crc32 ($blob);
		$lens[$size_type] = strlen($blob);
	}
	if (!FORCE_PROCESSING) {
		foreach ($blobs as $size_type => $blob) {
			$crc = $crcs[$size_type];
			$len = $lens[$size_type];
			if (!($find_upimgid = uploadimage_find_crclen_and_connect($element, $id, $crc, $len, $blob, $upimgid))) {
				if ($find_upimgid === false) {
					return false;
				}
				continue;
			}
			if (!db_insert('uploadimage_exists', "
				INSERT INTO uploadimage_exists SET
					STAMP			 = UNIX_TIMESTAMP(),
					UPIMGID			 = $upimgid,
					EXISTING_UPIMGID = $find_upimgid")
			||	!db_update('uploadimage', "
				UPDATE uploadimage SET BUSY = 0
				WHERE UPIMGID = $upimgid")
			) {
				return false;
			}
			$debug_upload_images && error_log("$dbgid found existing upimgid $find_upimgid as replacement for $upimgid @ {$_SERVER['REQUEST_URI']}");
			mail_log("found existing upimgid $upimgid after processing ($find_upimgid)");
			return true;
		}
	}
	$data_set_list = [];
	$meta_set_list = [];
	foreach ($crcs as $size_type => $crc) {
		$len = $lens[$size_type];

		[$width, $height] = $generate_sizes[$size_type];

		$data_set_list[] = "($upimgid, '$size_type', '".addslashes($blobs[$size_type])."')";
		$meta_set_list[] = "($upimgid, '$size_type', $width, $height, $len, $crc)";
	}
	# Make sure our updated version has a modification time newer than the old temproary version:
	if (($new_mstamp = time()) === CURRENTSTAMP) {
		++$new_mstamp;
	}
	/** @noinspection NotOptimalIfConditionsInspection */
	if (!db_replace('uploadimagemeta','
		REPLACE INTO uploadimagemeta (UPIMGID, SIZE, WIDTH, HEIGHT, DATASIZE, CRC)
		VALUES '.implode(', ', $meta_set_list))
	||	!db_replace('uploadimage_data','
		REPLACE INTO data_db.uploadimage_data (UPIMGID, SIZE, DATA)
		VALUES '.implode(', ', $data_set_list))
	||	!waitforslaves('uploadimage_data')
	||	!db_update('uploadimage', '
		UPDATE uploadimage SET
			BUSY	= 0,
			MUSERID	= '.CURRENTUSERID.",
			MSTAMP	= $new_mstamp
		WHERE UPIMGID = $upimgid")
	) {
		return false;
	}

	global $debug_upload_images, $dbgid;
	$debug_upload_images && error_log("$dbgid created in background, definitive version of uploadimage for $element:$id on time $new_mstamp @ {$_SERVER['REQUEST_URI']}");

	$end = microtime(true);

	db_insert('uploadimagetime', "
	INSERT INTO uploadimagetime SET
		UPIMGID	  = $upimgid,
		TIMETAKEN = 1000 * ".($end - $start)
	);
	clear_uploadimage_cache($element, $id);
	return true;
}

function uploadimage_find_crclen_and_connect(
	string	$element,
	int		$id,
	int		$crc,
	int		$len,
	string	$data,
	int		$not_upimgid = 0,
): int|false {
	if (false === ($upload_images = db_rowuse_array('uploadimage',"
		SELECT UPIMGID, SIZE
		FROM uploadimagemeta
		WHERE CRC = $crc
		  AND DATASIZE = $len
		  AND UPIMGID != $not_upimgid",
		DB_FORCE_MASTER | DB_NON_ASSOC))
	) {
		return false;
	}
	if (!$upload_images) {
		return 0;
	}
	foreach ($upload_images as [$upimgid, $size]) {
		if (false === ($have = db_single('uploadimage_data', "
			SELECT SQL_NO_CACHE DATA
			FROM data_db.uploadimage_data
			WHERE SIZE = '$size'
			  AND UPIMGID = $upimgid",
			DB_FORCE_MASTER))
		) {
			return false;
		}
		if ($have !== $data) {
			continue;
		}
		if (uploadimage_connect($upimgid, $element, $id)) {
			register_notice('uploadimage:notice:same_exists_connected_LINE');
			return $upimgid;
		}
	}
	return 0;
}

function uploadimage_connect(
	int		$upimgid,
	string	$element,
	int		$id,
	string	$parentelement 	 = '',
	int		$parentid 		 = 0,
	bool	$inside_eventmap = false
): bool {
	if (false === ($oldupimgids = uploadimage_get_upimgids($element, $id, $parentelement, $parentid, $upimgid))) {
		return false;
	}
	if (!$oldupimgids
	||	count($oldupimgids) > 1
	||	$oldupimgids[0] !== $upimgid
	) {
		if ($oldupimgids) {
			# have same orientation for this element and id
			# set all with same orientation to 'deleted'

			$oldupimgidstr = implode(',',$oldupimgids);

			$wherep = " UPIMGID IN ($oldupimgidstr)
					AND TYPE = '$element'
					AND ID = $id";

			if (!db_update('uploadimage_link_log','
				UPDATE uploadimage_link_log SET
					DUSERID	= '.CURRENTUSERID.',
					DSTAMP	= UNIX_TIMESTAMP()
				WHERE NOT DSTAMP
				  AND '.$wherep)
			||	!db_delete('uploadimage_link','
				DELETE FROM uploadimage_link
				WHERE '.$wherep)
			||	!db_delete('uploadimage_generated_log','
				INSERT INTO uploadimage_generated_log
				SELECT *, UNIX_TIMESTAMP(), '.CURRENTUSERID.'
				FROM uploadimage_generated
				WHERE '.$wherep)
			||	!db_delete('uploadimage_generated','
				DELETE FROM uploadimage_generated
				WHERE '.$wherep)
			) {
				return false;
			}
		}
		# insert new upimgid as connection
		if (!uploadimage_link($element, $id, $parentelement, $parentid, $upimgid)) {
			return false;
		}
		if ($element !== 'lineup') {
			require_once '_pagechanged.inc';
			page_changed(
					$element === 'party2'
				||	$element === 'eventmap'
				?	'party'
				:	$element,
				$id
			);
		}
	}
	if (!$inside_eventmap
	&&	$element === 'eventmap'
	) {
		# have other connected days?
		if (false === ($other_days = db_simple_hash(['connect','party'], "
			SELECT	connect.ASSOCID,
					(	SELECT UPIMGID
						FROM uploadimage_link
						WHERE TYPE = 'eventmap'
						  AND ID = ASSOCID
					) AS HAVE_UPLOADIMAGE
			FROM connect
			JOIN party AS main ON main.PARTYID = MAINID
			JOIN party ON party.PARTYID = ASSOCID
			WHERE MAINTYPE = 'party'
			  AND MAINID = $id
			  AND ASSOCTYPE = 'party'
			  AND main.NAME = party.NAME
			  AND main.SUBTITLE = party.SUBTITLE"))
		) {
			return false;
		}
		foreach ($other_days as $other_partyid => $other_upimgid) {
			if (!$other_upimgid
			||	$oldupimgids
			&&	in_array($other_upimgid, $oldupimgids, true)
			) {
				# replace eventmap of connected events (other days)
				if (!uploadimage_connect($upimgid, 'eventmap', $other_partyid, inside_eventmap: true)) {
					return false;
				}
			}
		}
	}
	return true;
}

function may_change_uploadimage(string $type, int $id): bool {
	/** @var string $element */
	if (!($element = is_uploadimage_type($type))
	||	!have_user()
	&&	$type !== 'party'
	&&	$type !== 'party2'
	) {
		return false;
	}
	/** @var array<string, array<int>> $__may_change */
	static $__may_change = [];
	if (isset($__may_change[$element][$id])) {
		return $__may_change[$element][$id];
	}
	switch ($element) {
	default:
		$ok = have_admin($element);
		break;

	case 'artist':
	case 'location':
	case 'organization':
		$ok =	have_admin($element)
			||	have_user()
			&&	db_single($element, "
				SELECT 1
				FROM `$element`
				WHERE ACCEPTED = 0
				  AND USERID = ".CURRENTUSERID."
				  AND {$element}ID = $id"
			);
		break;

	case 'lineup':
	case 'eventmap':
	case 'party':
		$ok =	have_admin('party')
			||	have_user()
			&&	memcached_party_and_stamp($id)
			&&	(require_once '_lock.inc')
			&&	have_lock(LOCK_PARTY, $id);
		break;

	case 'music':
		$ok =	have_admin('music')
			||	may_upload_music()
			&&	CURRENTUSERID === memcached_single('music','SELECT CUSERID FROM music WHERE MUSICID='.$id, TEN_MINUTES);
		break;

	case 'review':
	case 'interview':
		$ok =	have_admin($element)
			||	have_admin($element.'er')
			# reviewers and interviews may change image next to their interview/review while element is unaccepted
			&&	CURRENTUSERID === memcached_single($element, "
				SELECT USERID
				FROM `$element`
				WHERE ACCEPTED = 0
				  AND {$element}ID = $id"
			);
		break;

	case 'weblog':
	case 'report':
		$ok =	have_admin($element)
			||	have_user()
			&&	CURRENTUSERID === memcached_single($element, "
				SELECT USERID
				FROM `$element`
				WHERE {$element}ID = $id",
			);
		break;

	case 'promo':
	case 'promoteaser':
		$ok = have_admin('promo');
		break;
	}
	return $__may_change[$element][$id] = !empty($ok);
}

function require_may_change_uploadimage(string $type, int $id): bool {
	if (!may_change_uploadimage($type, $id)) {
		register_error('uploadimage:error:no_rights_for_change_LINE');
		return false;
	}
	return true;
}

function show_upload_form_part(
	string	$type,
	int		$id			  = 0,
	bool	$hide_current = false,
	bool	$inline 	  = false,
	int 	$flags		  = 0,
): array|false|null {
	if ($id) {
		/** @var array $img */
		if (false === ($img = uploadimage_get($type, $id))) {
			return false;
		}
		if (!$img) {
			$img = [];
		}
	} else {
		$img = [];
	}
	$type_index = match($type) {
		'party2'	=> '2',
		'eventmap'	=> 'EVENTMAP',
		default		=> '',
	};
	$action_element = null;
	$title_prefix = null;
	switch ($type) {
	default:		 $func = '__C'; break;
	case 'party':	 $func = '__';  $title_prefix = Eelement_name('flyer_front').' '.MIDDLE_DOT_ENTITY.' '; break;
	case 'party2':	 $func = '__';  $title_prefix = Eelement_name('flyer_back'). ' '.MIDDLE_DOT_ENTITY.' '; break;
	case 'eventmap': $func = null;  $action_element = $type; break;
	}

	require_once '_drag.inc';
	open_drag('uploadimage', auto_submit: $inline);

	layout_open_box('white');

	if (!$hide_current
	&&	$id
	/** @var array $img */
	&&	have_uploadimage($img)
	) {
		layout_box_header($action_element ? __C('action:change_'.$action_element) : $title_prefix.$func('action:change'));
		?><div<?
		?> class="uimg block"<?
		?> id="<?= $type ?>imgblock"<?
		?> data-linktype="<?= $img['TYPE'] ?>"<?
		?> data-linkid="<?= $img['ID'] ?>"<?
		?>><?

		$flags = UPIMG_SHOW_DIMS | UPIMG_PREVIEW;

		uploadimage_show_actual($img, $flags);

		if ($type !== 'eventmap'
		&&	!empty($img['IMGS'])
		) {
			foreach ($img['IMGS'] as $tmp_img) {
				if (empty($tmp_img['GENERATED'])
				?	($tmp_img['UPIMGID'] === $img['UPIMGID'])
				:	empty($tmp_img['CHOSEN'])
				) {
					continue;
				}
				$tmp_img = [...$img, ...$tmp_img];
				?> <?
				uploadimage_show_actual($tmp_img, $flags);
			}
		}
		?></div><?
	} else {
		layout_box_header($action_element ? __C('action:add_'.$action_element) : $title_prefix.$func('action:add_image'));
	}
	static $__initialized = false;
	if (!$__initialized) {
		$__initialized = true;
		?><input type="hidden" name="ALLOWBG" value="1" /><?
		include_js('js/form/upload');
		include_style('uploadimage');
	}
	?><div class="hidden uimg-remove-icon"><?= get_remove_char([
		'class'		=> 'abs z2',
		'style'		=> 'left: .5em; bottom: .5em;',
		'onclick'	=> /** @lang JavaScript */ 'Pf_removeUploadPreview(this)'
	]) ?></div><?
	?><div class="hidden upload-previews"><div></div></div><?
	?><div<?
	?> class="uimg-form-parts"<?
	?> data-cp="&copy;"<?
	?> data-allow-cp="data-allow-cp"<?
	?>><?

	?><div class="uimg-form-part"><?
	?><table class="fw<?
	if ($type !== 'party') {
		?> no-bottom<?
	}
	if ($flags & UPIMG_HIDE_FORM) {
		?> hidden<?
	}
	?>"><?
	layout_start_row();
	echo element_name('url');
	layout_field_value();
	static $__focus = 0;

	require_once 'defines/image_processing.inc';

	show_input([
		'type'				=> 'url',
		'data-valid'		=> 'working-url',
		'data-fb-idstr'		=> $img['FB_IDSTR']		?? false,
		'data-orig-width'	=> $img['ORIG_WIDTH']	?? false,
		'data-orig-height'	=> $img['ORIG_HEIGHT']	?? false,
		'accept'			=> ($accept = get_image_processing_accepted_mime_types()),
		'name'				=> "URL{$type_index}[]",
		'autofocus'			=> !empty($_REQUEST['sELEMENT']) && $_REQUEST['sELEMENT'] === 'upload' && !$__focus++,
		'class'				=> 'regular fw urlupload',
		'onkeydown'			=> /** @lang JavaScript */ 'Pf_handleUploadEvent(event, this)',
		'onblur'			=> /** @lang JavaScript */ 'Pf_handleUploadEvent(event, this)',
		# 'value'			=> $url
		# FIXME: url may be set!
	]);

	layout_restart_row();
	echo Eelement_name('upload');
	layout_field_value();
	show_input([
		'type'			=> 'file',
		'accept'		=> $accept,
		'name'			=> "FILE{$type_index}[]",
		'onchange'		=> /** @lang JavaScript */ 'Pf_handleUploadEvent(event, this);',
		'multiple'		=> true,
	]);
	layout_stop_row();

	if (str_starts_with($type, 'promo')) {
		$name = isset($type[5]) ? 'promo' : 'promoteaser';
		if (!db_single_int('uploadimage_link', "SELECT 1 FROM uploadimage_link WHERE TYPE = '$name' AND ID = $id")) {
			?><tr><td colspan="2"><?
				?><label><input type="checkbox" name="ALSO" value="1" /> ook voor <?= $name ?></label><?
			?></td></tr><?
		}
	}
	?></table><?

	?></div><? # uimg-form-part-$i
	?></div><? # uimg-form-parts

	close_drag('uploadimage');

	layout_close_box(no_ad: true);

	if (empty($img)) {
		return null;
	}
	return $img;
}

function uploadimage_receive_inline(): int {
	global $currentuser;
	$currentuser = new _currentuser(TINY_USER | SKIP_SETTINGS);
	header('Content-Type: text/html; charset=windows-1252');
	if (!uploadimage_actual_receive()) {
		display_messages_only();
		return 503;
	}
	uploadimage_show_with_fallback(
		$_POST['TYPE'],
		$_POST['ID'],
		flags: UPIMG_NOCHANGE | UPIMG_FORCE_REMOVE | UPIMG_FORCE_CROP | UPIMG_SHOW_DIMS,
		size : 'thumb',
	);
	return 200;
}

function uploadimage_actual_receive(
	?string	$type = null,
	?int	$id	  = null,
): bool {
	if (NO_ALTER_UPLOADIMAGE) {
		return false;
	}
	if ($type) {
		$_was_POST = [
			'TYPE'	=> $_POST['TYPE'] ?? null,
			'ID'	=> $_POST['ID']	  ?? null,
		];
		$_POST['TYPE'] = $type;
		$_POST['ID']   = $id;
	} elseif (
		!($type = require_uploadimage_type($_POST, 'TYPE'))
	||	!($id = require_idnumber($_POST, 'ID'))
	||	!require_may_change_uploadimage($type, $id)
	) {
		return false;
	}

	$overwrite_original = !empty($_POST['OVERWRITE_ORIGINAL']);

	if (in_array($type, ['orgashost', 'locashost'])) {
		if (!($parentelement = require_uploadimage_type($_POST, 'PARENTELEMENT'))
		||	!($parentid = require_idnumber($_POST, 'PARENTID'))
		) {
			return false;
		}
	} else {
		$parentelement = '';
		$parentid = 0;
	}

	process_upload_form();

	$rc = true;

	foreach ($type === 'party' ? ['', '2', 'EVENTMAP'] : [''] as $type_index) {
		$key = 'FILE'.$type_index;
		if (!isset($_FILES[$key])) {
			continue;
		}
		$do_type = $type_index === 'EVENTMAP' ? 'eventmap' : $type.$type_index;

		foreach ($_FILES[$key]['error'] as $i => $errno) {
			if ($errno !== UPLOAD_ERR_OK) {
				continue;
			}
			if ($upimgid = $_FILES[$key]['upimgid'][$i] ?? 0) {
				if (uploadimage_connect($upimgid, $do_type, $id)) {
					register_notice('uploadimage:notice:same_exists_connected_LINE');
				} else {
					register_error('uploadimage:error:processed_but_connect_failed_LINE');
				}
			} else {
				if (!($info = uploadimage_file_info($_FILES[$key]['tmp_name'][$i], $_FILES[$key]['name'][$i]))) {
					$rc = false;
					break;
				}
				if (!($upimgid = uploadimage_process(
					$do_type,
					$id,
					$parentelement,
					$parentid,
					$info,
					$_FILES[$key]['tmp_name'][$i],
					$_FILES[$key]['name'][$i],
					$_POST['NO_PERSON'.$type_index][$i] ?? false))
				) {
					$rc = false;
					break;
				}
			}
			if ($overwrite_original) {
				$original_element = null;
				$original_ids = [];
				switch ($type) { // NOSONAR
				case 'lineup':
					$original_element = 'artist';
					$original_ids = db_single('lineup', '
						SELECT ARTISTID
						FROM lineup_artist
						WHERE LINEUPID = '.$id
					);
					break;

				case 'locashost':
					$original_element = 'location';
					$original_ids = [$id];
					break;

				case 'orgashost':
					$original_element = 'organization';
					$original_ids = [$id];
					break;
				}
				foreach ($original_ids as $original_id) {
					uploadimage_link($original_element, $original_id, upimgid: $upimgid);
				}
			}
			if (isset($_POST['ALSO'])) {
				switch ($type) { // NOSONAR
				/** @noinspection PhpMissingBreakStatementInspection */
				case 'promo':
					$also_element = 'promoteaser';
				case 'promoteaser':
					$also_element ??= 'promo';
					$have_other = db_single('uploadimage_link', "
						SELECT 1
						FROM uploadimage_link
						WHERE TYPE = '$also_element'
						  AND ID = $id"
					);
					if ($have_other !== false
					&&	!$have_other
					) {
						uploadimage_link($also_element, $id, upimgid: $upimgid);
					}
					break;
				}
			}
			$cp = empty($_POST['COPYRIGHT'.$type_index][$i]) ? null : utf8_mytrim($_POST['COPYRIGHT'.$type_index][$i]);

			if (!uploadimage_actual_save_copyright($upimgid, $cp ?: '')) {
				$rc = false;
				break;
			}
		}
	}
	clear_uploadimage_cache($type, $id);
	if (!empty($_was_POST)) {
		$_POST = [...$_POST, ...$_was_POST];
	}
	return $rc;
}

function show_uploadimage_form(
	string	$connect_type,
	string	$element,
	int		$id,
	string	$parentelement	= '',
	int		$parentid		= 0,
	bool	$inline			= false,
	bool	$hide_current	= false,
): void {
	include_js('js/form/upload');
	if ($inline) {
		include_js('js/refreshupload');
		include_style('spinners');
	}
	?><form<?
	?> accept-charset="utf-8"<?
	?> class="uploadimage-form"<?
	?> method="post"<?
	?> action="/upload/receive"<?
	?> enctype="multipart/form-data"<?
	if ($inline) {
		?> onsubmit="return Pf_submitUploadForm(this);"<?
		?> id="<?= $inline ?>-form"<?
		?> data-auto-submit="true"<?
		?> data-inline-id="<?= $inline ?>"<?
		?> data-parent-element="<?= $parentelement ?>"<?
		?> data-parent-id="<?= $parentid ?>"<?
		?> data-size="thumb"<?
		?> style="padding-top: 1.4em;"<?
	} else {
		?> onsubmit="return submitForm(this)"<?
	}
	?>><?
	if ($inline) {
		?><input type="hidden" name="INLINE" value="1" /><?
		?><input type="hidden" name="OVERWRITE_ORIGINAL" /><?
	}
	?><input type="hidden" name="enc" value="&#129392;" /><?
	?><input type="hidden" name="TYPE" value="<?= escape_specials($connect_type) ?>" /><?
	?><input type="hidden" name="ID" value="<?= $id ?>" /><?
	show_max_file_size_input('uploadimage_data', 'DATA');

	if (have_idnumber($_REQUEST,'PARTYID')) {
		?><input type="hidden" name="PARTYID" value="<?= $_REQUEST['PARTYID'] ?>" /><?
	}

	show_uploadimage_for_element_form($element, $id, $hide_current, $inline);

	?><div class="block"><?
	show_input([
		'class'	=> $inline ? 'hidden' : null,
		'type'	=> 'submit',
		'value'	=> __('action:send'),
	]);
	?></div><?
	?></form<?
}

function show_uploadimage_for_element_form(
	string 	$element,
	int 	$id,
	bool	$hide_current	= false,
	bool 	$inline 		= false,
	int 	$flags 			= 0,
): void {
	include_js('js/form/uploadimage');
	if ($inline) {
		$flags |= UPIMG_HIDE_FORM;
	}
	if ($element === 'party') {
		show_upload_form_part('party',		$id, $hide_current, $inline, $flags);
		show_upload_form_part('party2',	$id, $hide_current, $inline, $flags);
	} else {
		show_upload_form_part($element, $id, $hide_current, $inline, $flags);
		if ($inline) {
			$base_element = is_uploadimage_type($element);
			open_drag('uploadimage', execute_on_drop: 'Pf_dropUploadImageAndReplaceArtist(event, this)');
			layout_open_box();
			layout_box_header(__C('action:add_image,replace_'.$base_element));
			layout_close_box(no_ad: true);
			close_drag('uploadimage');
		}
	}
}

function get_history_counts(array $img, int $flags): ?array {
	$change_admin = have_admin($type = is_uploadimage_type($img['TYPE']));

	if (($flags & UPIMG_SHOW_HISTORY)
	&& (	ROBOT
		||	$img['TYPE'] === 'artist'
		|| $change_admin)
	) {
		return memcached_read_if_not_admin(
			$type,
			['uploadimage', 'uploadimage_link_log'], "
			SELECT	COUNT(IF(DSTAMP AND ACTIVE, 1, NULL)) AS ACTIVE,
					COUNT(IF(DSTAMP, 1, NULL))			  AS TOTAL
			FROM uploadimage_link_log
			JOIN uploadimage USING (UPIMGID)
			WHERE TYPE = '{$img['TYPE']}'
			  AND ID   =  {$img['ID']}
			HAVING TOTAL",
			SINGLE_ARRAY
		) ?: null;
	}
	return null;
}

function show_history_link(array $img, array $counts): void {
	$change_admin = have_admin(is_uploadimage_type($img['TYPE']));
	?><div class="nowrap"><?
	[$active, $total] = $counts;
	?><a<?
	if (!$change_admin) {
		?> class="small"<?
	}

	?> href="/upload/<?= $img['TYPE'] ?>/<?= $img['ID'] ?>"><?= element_name('history') ?>: <?
	echo $active;
	if ($active !== $total) {
		?> / <? echo $total;
	}
	?></a><?
	?></div><?
}

function show_orig_dims(array $upimg, int $flags = 0): void {
	$show_element = $flags & UPIMG_SHOW_ELEMENT;

	if (!$show_element
	&&	!$upimg['ORIG_SIZE']
	) {
		return;
	}
	?><div class="dims"><?
	ob_start();
	if ($upimg['ORIG_SIZE']) {
		echo str_replace(' x ',' '.MULTIPLICATION_SIGN_ENTITY.' ',$upimg['ORIG_SIZE']);
	}
	if ($show_element) {
		if (ob_get_length()) {
			?> <?= MIDDLE_DOT_ENTITY ?> <?
		}
		echo element_name($upimg['TYPE'] === 'party2' ? 'party' : $upimg['TYPE']);
	}
	if (!empty($upimg['ORIENTATION'])
	&&	$upimg['TYPE']  === 'party'
	&&	$upimg['WIDTH'] !== $upimg['HEIGHT']
	&&	($aspect = $upimg['WIDTH'] / $upimg['HEIGHT']) < 1.25
	&&	$aspect > .75
	) {
		?><small><?
		if (ob_get_length()) {
			?> <?= MIDDLE_DOT_ENTITY ?> <?
		}
		echo __('orientation:'.$upimg['ORIENTATION']);
		?></small><?
	}
	ob_end_flush();
	?></div><?
}

function uploadimage_readies(): int {
	if (!($upimgids = require_number_list($_POST, 'UPIMGIDSTR'))) {
		return 400;
	}
	/** @var array<int,int> $states */
	if (false === ($states = db_simple_hash('uploadimage', "
		SELECT UPIMGID, BUSY
		FROM uploadimage
		WHERE UPIMGID IN ({$_POST['UPIMGIDSTR']})"))
	||	false === ($existings = db_simple_hash('uploadimage_exists', "
		SELECT UPIMGID, EXISTING_UPIMGID
		FROM uploadimage
		WHERE UPIMGID IN ({$_POST['UPIMGIDSTR']})"))
	) {
		return 500;
	}
	assert(is_array($existings)); # Satisfy EA inspection
	$result = [];
	foreach ($upimgids as $upimgid) {
		if ($existing_upimgid = $existings[$upimgid] ?? null) {
			$result[$upimgid] = $existing_upimgid;

		} elseif (isset($states[$upimgid])) {
			$result[$upimgid] = $states[$upimgid] ? 'busy' : 'ready';

		} else {
			$result[$upimgid] = true;
			mail_log("could not find state or existing of UPIMGID $upimgid", error_log: 'WARNING');
		}
	}
	header('Content-Type: application/json');
	echo safe_json_encode($result);
	return 200;
}

function uploadimage_require_for_link(): array|int {
	$parent_element = '';
	$parent_id = 0;
	$size = '';
	$flags = 0;
	if (!require_post()
	||	!($type = require_uploadimage_type($_POST, 'TYPE'))
	||	!($id = require_idnumber($_POST, 'ID'))
	||	!($upimgid = require_idnumber($_POST, 'UPIMGID'))

	||	isset($_POST['PARENTELEMENT'])
	&&	is_string($_POST['PARENTELEMENT'])
	&&	'' !== $_POST['PARENTELEMENT']
	&&	!($parent_element = require_element($_POST, 'PARENTELEMENT', ['party'], true))

	||	isset($_POST['PARENTID'])
	&&	is_string($_POST['PARENTID'])
	&&	'' !== $_POST['PARENTID']
	&&	!($parent_id = require_idnumber($_POST, 'PARENTID'))

	||	isset($_POST['FLAGS'])
	&&	is_string($_POST['FLAGS'])
	&&	'' !== $_POST['FLAGS']
	&&	!require_number($_POST, 'FLAGS')

	||	isset($_POST['SIZE'])
	&&	is_string($_POST['SIZE'])
	&&	'' !== $_POST['SIZE']
	&&	!($size = require_uploadimage_size($_POST, 'SIZE'))
	) {
		return 400;
	}
	require_once '_currentuser.inc';
	global $currentuser;
	$currentuser ??= new _currentuser(TINY_USER | SKIP_SETTINGS);
	if (!may_change_uploadimage($type, $id)) {
		return 403;
	}
	return [$type,
			$id,
			$parent_element,
			$parent_id,
			$upimgid,
			$flags,
			$size,];
}

function uploadimage_choose_default(): int {
	if (is_int($uploadimage_post_info = uploadimage_require_for_link())) {
		return $uploadimage_post_info;
	}
	[$type, $id, /* $parentelement */, /* $parentid */, $upimgid] = $uploadimage_post_info;
	if (!db_update('uploadimage_link', "
		UPDATE uploadimage_link SET
			`DEFAULT`	= IF(UPIMGID = $upimgid, 1, 0)
		WHERE TYPE = '$type'
		  AND ID = $id")
	) {
		return 503;
	}
	clear_uploadimage_cache($type, $id);
	$img = uploadimage_get($type, $id);

	uploadimage_show_from_img(img: $img, flags: have_idnumber($_POST, 'FLAGS'));

	return 200;
}

function uploadimage_remove_link(): int {
	if (is_int($uploadimage_post_info = uploadimage_require_for_link())) {
		return $uploadimage_post_info;
	}
	[$type, $id, $parentelement, $parentid, $upimgid, $flags, $size] = $uploadimage_post_info;

	if (isset($_POST['GENERATED'])
	&&	!require_element($_POST, 'GENERATED', ['', 'manualcrop'])
	) {
		return 400;
	}
	if (!($generated = $_POST['GENERATED'] ?? null)
	&&	in_array($type, ['party', 'party2'])
		# banner connected?
	&&	(	($connected = db_single('bannermultiparty', "
			SELECT GROUP_CONCAT(BANNERID SEPARATOR ', ')
			FROM bannermultiparty
			WHERE WHAT IN ('both', '".($type === 'party' ? 'front' : 'back')."')
			  AND PARTYID = $id"))
		||	$connected === false
		)
	) {
		register_error('uploadimage:error:still_connected_to_banner_LINE', ['BANNERIDS'	=> $connected]);
		return 400;
	}
	$refresh = false;

	if ($generated
	?	!uploadimage_remove_generated($type, $id,							 $upimgid, $generated)
	:	!uploadimage_removelink		 ($type, $id, $parentelement, $parentid, $upimgid, $refresh, (bool)($_POST['DEACTIVATE'] ?? false))
	) {
		return 500;
	}

	clear_uploadimage_cache($type, $id);

	if ($parentelement) {
		# this are useed on party/ID/hostimages, inline form, want the original image back?
		$original_element = match($type) {
			'orgashost'	=> 'organization',
			'locashost'	=> 'location',
		};
		$img = uploadimage_get($original_element, $id, size: $size);

		uploadimage_show_from_img(img: $img, flags: $flags);

	} elseif ($refresh) {
		$img = uploadimage_get($type, $id, size: $size);

		uploadimage_show_from_img(img: $img);
	}
	return 200;
}

function uploadimage_store_crop(?int $arg_upimgid = null): int {
	if (is_int($uploadimage_post_info = uploadimage_require_for_link())) {
		return $uploadimage_post_info;
	}
	[$type, $id,,, $upimgid] = $uploadimage_post_info;

	if ($arg_upimgid) {
		$upimgid = $arg_upimgid;
	}
	if (!require_number($_POST, 'W')
	||	!require_number($_POST, 'H')
	||	!require_number($_POST, 'X')
	||	!require_number($_POST, 'Y')
	) {
		return 400;
	}
	if (!db_insert('uploadimage_crop_log','
		INSERT INTO uploadimage_crop_log
		SELECT *, UNIX_TIMESTAMP(), '.CURRENTUSERID."
		FROM uploadimage_crop
		WHERE TYPE = '$type'
		  AND ID = $id
		  AND UPIMGID = $upimgid")
	||	!db_replace('uploadimage_crop',"
		REPLACE INTO uploadimage_crop SET
			TYPE	= '$type',
			ID		= $id,
			UPIMGID	= $upimgid,
			WIDTH	= {$_POST['W']},
			HEIGHT	= {$_POST['H']},
			X		= {$_POST['X']},
			Y		= {$_POST['Y']},
			USERID	= ".CURRENTUSERID.',
			CSTAMP	= UNIX_TIMESTAMP()')
	) {
		return 500;
	}
	$_REQUEST['NOMEMCACHE'] = true;
	$_POST['GENERATED'] = 'manualcrop';
	uploadimage_choose_generated($arg_upimgid);
	ob_start();
	$img = uploadimage_get($type, $id);
	foreach ($img['IMGS'] as $tmp_img) {
		if ($tmp_img['UPIMGID'] === $upimgid
		&&	!empty($tmp_img['GENERATED'])
		&&	$tmp_img['GENERATED'] === 'manualcrop'
		) {
			header('Content-Type: text/html; charset=win1252');
			$tmp_img = array_merge($img, $tmp_img);
			uploadimage_show_with_chooser($tmp_img, UPIMG_PREVIEW | have_idnumber($_POST, 'FLAGS') ?: 0);
			break;
		}
	}
	if (!$arg_upimgid
	&&	!ob_get_length()
	) {
		# maybe upimgid of old image used now?
		if (false === ($new_upimgid = db_single_int('uploadimage_link', "
			SELECT UPIMGID
			FROM uploadimage_link
			WHERE TYPE = '$type'
			  AND ID = $id"))
		) {
			return 503;
		}
		if ($new_upimgid !== $upimgid) {
			static $__calls		 = [];
			static $__call_count = 0;

			$__calls[] = "new_upimgid === $new_upimgid, upimgid: $upimgid";

			if (++$__call_count === 3) {
				mail_log('uploadimage_store_crop() called itself too many times', $__calls);
			}
			uploadimage_store_crop($new_upimgid);
			ob_end_flush();
			return 200;
		}
	}
	if (!ob_get_flush()) {
		mail_log('no data for store crop', item: $img);
	}
	return 200;
}

function uploadimage_save_copyright(): int {
	if (is_int($uploadimage_post_info = uploadimage_require_for_link())) {
		return $uploadimage_post_info;
	}
	[/* $type */,/* $id */, /* $parent_element */, /* $parent_id */, $upimgid] = $uploadimage_post_info;
	if (!require_anything_trim($_POST, 'COPYRIGHT', true)) {
		return 400;
	}
	$copyright = $_POST['COPYRIGHT'];
	if (!uploadimage_actual_save_copyright($upimgid, $copyright)) {
		return 503;
	}
	return 200;
}

function uploadimage_actual_save_copyright(int $upimgid, string $copyright): bool {
	if (false === ($had = db_single('uploadimagecp','SELECT COPYRIGHT FROM uploadimagecp WHERE UPIMGID='.$upimgid))) {
		return false;
	}
	$have = $copyright;
	if ($had
	?	($had !== $have)
	:	$have
	) {
		$ins = false;
		if ($had) {
			if (!db_insert('uploadimagecp_log','
				INSERT INTO uploadimagecp_log
				SELECT * FROM uploadimagecp
				WHERE UPIMGID = '.$upimgid)
			) {
				return false;
			}
			if ($have) {
				$ins = true;
			} elseif (!db_insert('uploadimagecp_log', "
					INSERT INTO uploadimagecp_log SET
						UPIMGID		= $upimgid,
						COPYRIGHT	= '',
						STAMP		= UNIX_TIMESTAMP()")
				||	!db_delete('uploadimagecp', '
					DELETE FROM uploadimagecp
					WHERE UPIMGID = '.$upimgid)
			) {
				return false;
			}
		} else {
			$ins = true;
		}
		if ($ins &&	!db_replace('uploadimagecp', "
			REPLACE INTO uploadimagecp SET
				COPYRIGHT	= '".addslashes($have)."',
				UPIMGID		= $upimgid,
				STAMP		= UNIX_TIMESTAMP()")
		) {
			return false;
		}
	}
	return true;
}

function show_remove_icon(array $img): void {
	$upimgid = $img['UPIMGID'];
	?><div class="abs z1 nowrap" style="bottom: .2em; left: .2em;"><?
	if (empty($img['GENERATED'])) {

		echo get_remove_char([
			'class'		=> 'ib',
			'title'		=> __('action:archive'),
			'onclick'	=> /** @lang JavaScript */ 'Pf_removeImageLink(event, this, '.$upimgid.', null, false);',
		]);
		?> <?
	}
	$generated_value = empty($img['GENERATED']) ? 'null' : "'{$img['GENERATED']}'";
	echo get_close_char([
		'class'			=> 'ib',
		'title'			=> __('action:remove'),
		'onclick'		=> /** @lang JavaScript */ 'Pf_removeImageLink(event, this, '.$upimgid.', '.$generated_value.', true);',
		'data-confirm'	=> empty($img['GENERATED']) ? __('uploadimage:warning:sure_to_remove_TEXT') : false
	]);
	?></div><?
}

function show_copyright(array|string $img, bool $changable = false): void {
	$cp = is_scalar($img) ? $img : getifset($img,'COPYRIGHT');
	if (!$cp
	&&	!$changable
	) {
		return;
	}
	if ($changable) {
		include_js('js/form/uploadimage');
	}
	?><div<?
	if ($changable) {
		?> onclick="Pf.clickCopyright(event, this);"<?
	}
	?> class="uimg-cp ovbg<?= empty($img['MAY_CHANGE']) ? null : ' ptr' ?>"<?
	?>><span>&copy; </span><span><?= escape_specials($cp,true) ?></span></div><?
}

function show_short_date(array $img): void {
	show_date($img, '_date_get_numeric');
}

function show_date(array $img, string $date_get = '_date_get'): void {
	echo $date_get($img['CSTAMP']);
}

function uploadimage_link(
	string	$element,
	int		$id,
	string	$parentelement	= '',
	int		$parentid		= 0,
	int		$upimgid		= 0,
	bool	$fallback		= false,
): bool {
	if ($fallback) {
		return true;
	}
	if (false === ($current_default = db_single('uploadimage_link',"
		SELECT ORIENTATION
		FROM uploadimage_link
		JOIN uploadimage USING (UPIMGID)
		WHERE TYPE = '$element'
		  AND ID   = $id",
		DB_USE_MASTER))
	||	!db_insert('uploadimage_link_log',"
		INSERT INTO uploadimage_link_log SET
			TYPE			= '$element',
			ID				= $id,
			PARENTELEMENT	= '$parentelement',
			PARENTID	 	= $parentid,
			UPIMGID			= $upimgid,
			CSTAMP			= UNIX_TIMESTAMP(),
			CUSERID			= ".CURRENTUSERID)
	) {
		return false;
	}
	$set_default = 1;
	if ($element === 'party'
	&&	(	$current_default === 'square'
		||	$current_default === 'portrait')
	) {
		# keep current default
		$set_default = 0;
	}
	if ($set_default
	&&	!db_update('uploadimage_link', "
		UPDATE uploadimage_link SET
			`DEFAULT` = 0
		WHERE TYPE = '$element'
		  AND ID   = $id")
	||	!db_insert('uploadimage_link', "
		INSERT INTO uploadimage_link SET
			`DEFAULT`		= $set_default,
			TYPE			= '$element',
			ID				= $id,
			PARENTELEMENT	= '$parentelement',
			PARENTID		= $parentid,
			UPIMGID			= $upimgid,
			FALLBACK		= b'0'")
	) {
		return false;
	}
	clear_uploadimage_cache($element, $id);
	return true;
}

function uploadimage_get_image_from_url(?string $url = null): int {
	if ($url === null) {
		if (!require_something_trim($_POST, 'URL', utf8: true)) {
			return 400;
		}
		$url = $_POST['URL'];
	}
	if (($type = 'soundcloud')	&& false !== stripos($url, 'soundcloud.com/')
	||	($type = 'instagram')	&& false !== stripos($url, 'instagram.com/')
	||	($type = 'ra')			&& false !== stripos($url, 'imgproxy.ra.co/')
	||	($type = 'generic')
	) {
		# ok
	} else {
		return 404;
	}
	switch ($type) { // NOSONAR
	case 'generic':
	case 'instagram':
	case 'soundcloud':
		if (!($data = safe_file_get_contents($url, [CURLOPT_HTTPHEADER => [ACCEPT_HEADER_FOR_IMAGE]], info: $info))
		||	false === stripos($data, '<!DOCTYPE')
		||	false === stripos($data, '<html')
		) {
			register_error('uploadimage:error:no_data_TEXT', DO_NL2BR | DO_UBB, [
				'URL' => $url,
				'ERR' => $info['http_code']
			]);
			return 400;
		}
		# unable to get biggest format from instagram.
		if (!preg_match('!property="og:image" content="(?<url>.*?)"!', $data, $match)) {
			return 404;
		}
		echo str_replace('&amp;', '&', $match['url']);
		break;

	case 'ra':
		# EXAMPLE: https://imgproxy.ra.co/_/quality:66/w:1442/rt:fill/aHR0cHM6Ly9pbWFnZXMucmEuY28vZWMwM2FhZjlhMmM3Yjg0ZGFhMzViN2M5NDVjMjI4MzIzNDRiZmNlNS5qcGc=
		echo str_replace('_/quality:66/', '_/quality:100/', $url);
		break;
	}
	return 200;
}

function uploadimage_choose_generated($arg_upimgid = null): int {
	if (is_int($uploadimage_post_info = uploadimage_require_for_link())) {
		return $uploadimage_post_info;
	}
	[$type, $id, /* $parentelement */, /* $parentid */, $upimgid] = $uploadimage_post_info;

	if ($arg_upimgid) {
		$upimgid = $arg_upimgid;
	}
	if (!($generated = require_element($_POST, 'GENERATED', ['letterbox', 'pillarbox', 'crop', 'manualcrop']))) {
		return 400;
	}
	if (!db_lock_tables(['uploadimage_generated', 'uploadimage_generated_log'])) {
		return 503;
	}
	if (false === ($current = db_single('uploadimage_generated', "
		SELECT `GENERATED`
		FROM uploadimage_generated
		WHERE UPIMGID = $upimgid
		  AND TYPE = '$type'
		  AND ID = $id",
		DB_FORCE_MASTER))
	) {
		return 503;
	}
	if ($current
	&&	$current === $generated
	) {
		db_unlock_tables();
		return 200;
	}
	if (!db_insert('uploadimage_generated_log','
		INSERT INTO uploadimage_generated_log
		SELECT *, UNIX_TIMESTAMP(), '.CURRENTUSERID."
		FROM uploadimage_generated
		WHERE `GENERATED` != '$generated'
		  AND TYPE = '$type'
		  AND ID = $id")
	||	!db_replace('uploadimage_generated', "
		REPLACE INTO uploadimage_generated SET
			UPIMGID		= $upimgid,
			TYPE		= '$type',
			ID			= $id,
			`GENERATED`	= '$generated',
			CSTAMP		= UNIX_TIMESTAMP(),
			USERID		= ".CURRENTUSERID)
	) {
		return 500;
	}
	db_unlock_tables();
	clear_uploadimage_cache($type, $id);
	return 200;
}

function uploadimage_get_crop(): int {
	if (is_int($uploadimage_post_info = uploadimage_require_for_link())) {
		return $uploadimage_post_info;
	}
	[$type, $id, /* $parentelement */, /* $parentid */, $upimgid] = $uploadimage_post_info;

	require_once '_smartcrop.inc';

	$spec = db_single_assoc('uploadiamge_crop', "
		SELECT WIDTH, HEIGHT, X, Y
		FROM uploadimage_crop
		WHERE UPIMGID = $upimgid
		ORDER BY TYPE = '$type' AND ID = $id DESC
		LIMIT 1")
	?:	get_stored_crop('uploadimage',$upimgid);

	echo safe_json_encode($spec);

	return 200;
}

function show_no_person(array $img): void {
	if ($img['LINKTYPE'] !== 'artist') {
		return;
	}
	?><div<?
	?> onclick="Pf.clickNoPerson(this,<?= $img['UPIMGID'] ?>)"<?
	?> class="emoji uimg-no-person<?
	if (!empty($img['NO_PERSON'])) {
		?> isset<?
	}
	?>"><?= SEE_NO_EVIL_MONKEY_ENTITY ?></div><?
}

function uploadimage_change_no_person(): int {
	if (!($upimgid = require_idnumber($_POST, 'UPIMGID'))
	||	false === ($no_person = require_number_element ($_POST, 'NO_PERSON', [0, 1], true))
	) {
		return 400;
	}
	global $currentuser;
	$currentuser = new _currentuser(TINY_USER | SKIP_SETTINGS);
	if (!require_admin()) {
		return 403;
	}
	if (!db_update('uploadimagemeta', "
		UPDATE uploadimage SET
			NO_PERSON = $no_person
		WHERE UPIMGID = $upimgid")
	) {
		return 500;
	}
	clear_uploadimage_cache($upimgid);
	return 200;
}

function show_connected_images(string $what, int $partyid): void {
	require_once 'defines/appic.inc';
	if (!($partyids = get_partyids_for_multiday($partyid))) {
		return;
	}
	switch ($what) {
	case 'lineup':
		$items = db_rowuse_hash(['lineup', 'artist'], "
			SELECT	DISTINCT LINEUPID AS ID,
				'lineup-image-' AS ID_PREFIX, ARTISTID AS FALLBACKID, NAME,
				'lineup' AS UPLOAD_ELEMENT,
				'artist' AS BASE_ELEMENT
			FROM lineup
			JOIN artist USING (ARTISTID)
			WHERE PARTYID IN (".implode(', ', $partyids).')
			ORDER BY NAME'
		);
		break;

	case 'host':
		$items = db_rowuse_hash(['connect', 'location', 'organization'],  "
			SELECT	DISTINCT ASSOCID AS ID, CONCAT(ASSOCTYPE, '-') AS ID_PREFIX, ASSOCID AS FALLBACKID,
					COALESCE(organization.NAME, location.NAME) AS NAME,
					IF(ASSOCTYPE = 'orgashost', 'orgashost',	'locashost') AS UPLOAD_ELEMENT,
					IF(ASSOCTYPE = 'orgashost', 'organization', 'location')  AS BASE_ELEMENT
			FROM connect
			LEFT JOIN location	   ON	  location.LOCATIONID	  = ASSOCID AND ASSOCTYPE = 'locashost'
			LEFT JOIN organization ON organization.ORGANIZATIONID = ASSOCID AND ASSOCTYPE = 'orgashost'
			WHERE MAINTYPE = 'party'
			  AND MAINID IN (".implode(', ', $partyids).")
			  AND ASSOCTYPE IN ('orgashost', 'locashost')
			ORDER BY NAME"
		);
		break;

	default:
		mail_log("unknown connected images type: item: $what");
		return;
	}
	if (!$items) {
		return;
	}
	require_once '_uploadimage.inc';
	foreach ($items as $item) {
		/** @noinspection PhpRedundantOptionalArgumentInspection */
		extract($item, \EXTR_OVERWRITE);

		$id = $ID_PREFIX.$ID;

		?><div style="width:300px; clear: left; float: left;" class="center"><?
			?><div class="bold"><?= get_element_link($BASE_ELEMENT, $FALLBACKID) ?></div><?

			$flags =
				UPIMG_NOCHANGE
			|	UPIMG_FORCE_CROP
			|	UPIMG_FORCE_REMOVE
			|	UPIMG_SHOW_DIMS
			|	UPIMG_SHOW_FALLBACK;

			?><div<?
			?> data-flags="<?= $flags ?>"<?
			?> class="with-inline-id ib"<?
			?> id="<?= $id ?>"><?
			uploadimage_show_with_fallback($UPLOAD_ELEMENT,	$ID, flags: $flags, size: 'thumb');
			?></div><?
		?></div><?
		?><div style="margin-left: 300px;" id="uploadimage-form-stop"><?
			show_uploadimage_form(
				$UPLOAD_ELEMENT,
				$BASE_ELEMENT,
				$ID,
				'party',
				$partyid,
				(bool)$id,
				true
			);
		?></div><?
		?><hr class="clear slim"><?
	}
}

function lock_uploadimage_for_processing(
	string	$element,
	?int 	$id			= null,
	?string	$extra_id	= null,
	?int	$max_wait	= null,
): ?bool {
	global $debug_upload_images, $dbgid;
	$lock_key = "uploadimage:$element:$id:$extra_id";
	$max_wait = $max_wait ?: 10;
	if ($debug_upload_images) {
		error_log("$dbgid getting lock on $lock_key, wait max $max_wait seconds @ {$_SERVER['REQUEST_URI']}");
		$before_lock = microtime(true);
	}
	$rc = db_getlock($lock_key, $max_wait);
	if ($debug_upload_images) {
		error_log("$dbgid lock on $lock_key result ".var_get($rc).', waited '.round(microtime(true) - $before_lock, 1)."s @ {$_SERVER['REQUEST_URI']}");
	}
	$rc = db_getlock($lock_key, $max_wait);
	$debug_upload_images && error_log("$dbgid got lock on $lock_key, waited ".round(microtime(true) - $before_lock, 1)."s @ {$_SERVER['REQUEST_URI']}");
	return $rc;
}

function show_uploadimage_alt(array $image): void {
	if (!($element = is_uploadimage_type($image['TYPE']))
	||	!($title = get_element_title($element, $image['ID']))
	) {
		return;
	}
	?> alt="<?
	if ($element === 'eventmap') {
		echo element_name('event_map') ?> <?
	}
	echo escape_specials($title, isset(USE_UNICODE[$element]));
	?>"<?
}
