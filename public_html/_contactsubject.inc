<?php

declare(strict_types=1);

/*function make_ticket_subject(string $subject, int $ticketid): string {
	$stripped = strip_ticket_subject($subject);
	$idstr = '[Partyflock #'.$ticketid.']';
	return $stripped.' '.$idstr;
}*/
function strip_ticket_subject(string $subject, bool $utf8 = false): string {
	$utf8_mod = $utf8 ? 'u' : '';
	return preg_replace([
		'/^\s*(?:(?:re|fwd?|antw|aw|autoreply):\s*)*/i'.$utf8_mod,
		'/\s*\[(?:Partyflock[\s_]*#?(\d+)|([\da-f]+)PF|PF[\da-f]+)]\s*/i'.$utf8_mod
	], '', $subject);
}

function show_ticket_subject(string $subject, ?string &$unescaped_subject = null): void {
	if (isset($subject[1])
	&&	$subject[0] === '='
	&&	$subject[1] === '?'
	) {
		require_once '_mimeheader.inc';
		$subject = decode_quoted_printable_header($subject, $made_utf8);
		$utf8 = true;
	}  else {
		$utf8 = mb_detect_encoding($subject, 'windows-1252, UTF-8') === 'UTF-8';
	}
	$unescaped_subject = strip_ticket_subject($subject, $utf8);
	echo escape_specials($unescaped_subject, $utf8);
}
