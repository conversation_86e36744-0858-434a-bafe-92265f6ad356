<?php

declare(strict_types=1);

define('CURRENTSTAMP',	time());
define('TODAYSTAMP',	mktime(0,0,0));

const CACHESTAMP = CURRENTSTAMP - CURRENTSTAMP % 300;

require_once '_date.inc';
require_once '_db.inc';
require_once '_exit_if_offline.inc';
require_once '_argparse.inc';
require_once '_flockmod.inc';
require_once '_layout.inc';
require_once '_nocache.inc';
require_once '_wrap.inc';
require_once '_require.inc';

define('CURRENTDAYNUM',to_days());

function bail(int|http_status $errno): never {
	if ($errno instanceof http_status) {
		$errno = $errno->value;
	}
	http_response_code($errno === 500 ? 503 : $errno);
	ob_start();
	display_messages_only();
	if (ob_get_length()) {
		header('Content-Type: text/html; charset=windows-1252');
	}
	ob_end_flush();
	exit;
}

main();

function main(): void {
	if (empty($_REQUEST['OBJTYPE'])) {
		bail(404);
	}

	switch ($_REQUEST['OBJTYPE']) {
	case 'netname':
		header('Content-Type: text/plain; charset=utf-8');
		# NOTE: hostname is escaped in memcached_netname_from_netstr
		if (!($netstr = require_net_address($_REQUEST, 'NETSTR'))) {
			bail(400);
		}
		echo memcached_netname_from_netstr($netstr);
		exit;

	case 'hostname':
		header('Content-Type: text/plain; charset=utf-8');
		# NOTE: hostname is escape in memcached_hostname_from_ipbin
		if (false !== ($ipnum = have_number($_REQUEST, 'IPNUM'))) {
			$_REQUEST['IPSTR'] = long2ip($ipnum);
		}
		if (!($ipstr = require_ip_address($_REQUEST, 'IPSTR'))) {
			bail(400);
		}
		/** @noinspection NotOptimalIfConditionsInspection */
		if ($ipstr !== ($hostname = memcached_hostname_from_ipbin(inet_pton($ipstr), $ipstr))
		||	!isset($_REQUEST['FB'])
		) {
			echo $hostname;
		} else {
			[$netstr/*, $masksize */] = get_netip_and_masksize($ipstr);
			echo memcached_netname_from_netstr($netstr);
		}
		exit;

	case 'provinces':
		if (!($countryid = have_idnumber($_REQUEST,'COUNTRYID'))) {
			bail(400);
		}
		header('Content-Type: text/html;charset=windows-1252');
		require_once '_provincelist.inc';
		?><select name="PROVINCEID" id="provid"><?
		?><option value="0"></option><?
		provincelist_display_options(0,$countryid);
		?></select><?
		exit;

	case 'timezones':
		if (!($countryid = have_idnumber($_REQUEST,'COUNTRYID'))) {
			bail(400);
		}
		require_once '_timezoneoptions.inc';
		header('Content-Type: text/html;charset=windows-1252');
		show_timezone_options($countryid,have_something_trim($_REQUEST,'SELECTED') ? $_REQUEST['SELECTED'] : null,true);
		exit;

	case 'passwdstrength':
		require_once '_passwordstrength.inc';
		header('Content-Type: text/plain; charset=windows-1252');
		if (!password_strong_enough(isset($_POST['PASSWD']) ? $_POST['PASSWD'] : '', $messages)) {
			header('X-FAIL: 1');
		}
		if ($messages) {
			echo implode('<br />', $messages);
		}
		exit;

	case 'filterparties':
		require_once '_partylistfilter.inc';
		require_once '_identity.inc';
		require_once '_currentuser.inc';
		_currentuser::load_current_user(TINY_USER | SKIP_SETTINGS);
		if (isset($_POST['ONLY'])) {
			if (!require_element($_POST, 'ORDER', ['NAME', 'LOCATION', 'CITY', 'VISITORS'])) {
				bail(400);
			}
			if (!update_stored_filters(['SORT="'.$_POST['ORDER'].'"'])) {
				bail(500);
			}
		} else {
			if (!require_element($_POST, 'PART', ['all', 'region', 'choose'])) {
				bail(400);
			}
			actually_filter_parties();
		}
		exit;

	case 'anonymize-info':
		send_no_cache_headers();
		header('Content-Type: text/html;charset=windows-1252');
		require_once '_anonymize.inc';
		if (!($id = have_idnumber($_REQUEST,'MESSAGEID'))) {
			bail(400);
		}
		require_once '_currentuser.inc';
		_currentuser::load_current_user(TINY_USER | SKIP_SETTINGS);
		if (!have_admin('directmessage')) {
			bail(403);
		}
		header('Content-Type: text/html;charset=windows-1252');
		if ($info = find_anonymised_matches($id,true)) {
			[$total, $current] = get_anonymize_counts($info);
			echo	$total === 1
				?	1
				:	__('directmessage:amount:info',array(
					'TOTAL'	=> $total,
					'USER'	=> $current
				));
		}
		exit;

	case 'findsameaddress':
		require_once '_currentuser.inc';
		require_once '_identity.inc';
		_currentuser::load_current_user(TINY_USER | SKIP_SETTINGS);
		send_no_cache_headers();
		if (!($address = require_something_trim($_POST, 'ADDRESS') ? $_POST['ADDRESS'] : null)
		||	false === require_number($_POST, 'CITYID')
		||	!($element = require_element($_POST, 'ELEMENT', ['location', 'boarding']))
		||	!empty($_POST['LOCATIONID']) && !require_idnumber($_POST, 'LOCATIONID')
		||	!empty($_POST['FOLLOWUPID']) && !require_idnumber($_POST, 'FOLLOWUPID')
		) {
			bail(400);
		}
		if (!($cityid = have_idnumber($_POST, 'CITYID') ?: 0)) {
			bail(200);
		}
				$id = have_idnumber($_POST, 'ELEMENTID' );
		$followupid = have_idnumber($_POST, 'FOLLOWUPID');
		require_once '_addresscheck.inc';
		header('Content-Type: text/html; charset=windows-1252');
		address_exists($element,$address,$cityid,$id,$followupid);
		bail(200);

	case 'checkremovable':
		require_once '_irremovable.inc';
		require_once '_removable.inc';
		require_once '_currentuser.inc';
		_currentuser::load_current_user(TINY_USER);
		send_no_cache_headers();
		if (!($element = require_removable_element($_POST, 'sELEMENT'))
		||	!($id = require_idnumber($_POST, 'sID'))
		) {
			bail(400);
		}
		if (!have_admin([$element.'_remove', $element.'_remove_unaccepted'])
		) {
			bail(403);
		}
		if (false === ($reason = irremovable_reason($element, $id))) {
			bail(500);
		}
		if ($reason) {
			require_once '_bubble.inc';
			$bubble = new bubble(CATCHER_UNAVAILABLE | BUBBLE_BLOCK | BUBBLE_CURSOR);
			$bubble->catcher(__C('action:remove'));
			$bubble->content($reason);
			$bubble->display();
			bail(405);
		}
		header('Content-Type: text/html; charset=windows-1252');
		with_confirm(
			__C('action:remove'),
			"/$element/$id/remove",
			__($element.':confirm:removal_LINE',
					$element === 'contest'
				?	['CNT' => (int)db_single('contestresponse', 'SELECT COUNT(*) FROM contestresponse WHERE CONTESTID = '.$id)]
				:	null
			)
		);
		bail(200);

	case 'havegeo':
		send_no_cache_headers();
		if (false === ($lat = have_float($_POST, 'LATITUDE'))
		||	false === ($lon = have_float($_POST, 'LONGITUDE'))
		) {
			bail(400);
		}
		require_once '_identity.inc';
		require_once '_geo.inc';
		store_geolocation_cache($lat, $lon);
		bail(200);

	case 'nick':
		if (!($userid = have_idnumber($_GET,'USERID'))) {
			bail(400);
		}
		require_once '_currentuser.inc';
		_currentuser::load_current_user(TINY_USER | SKIP_SETTINGS);
		if (!have_admin()) {
			bail(403);
		}
		header('Content-Type: text/html; charset=windows-1252');
		if (!($user = memcached_user($userid))) {
			header('X-USER-BAD: true');
			?><span class="error"><?= __('error:does_not_exist') ?></span><?
		} else {
			echo get_element_link('user', $userid, $user['NICK']);
			if ($user['STATUS'] !== 'active') {
				header('X-USER-BAD: true');
				?> (<span class="<?= $user['STATUS'] ?>"><?= __('status:'.$user['STATUS']) ?></span>)<?
			}
		}
		break;

	case 'elementname':
		if (!($element = have_element($_REQUEST,'ELEMENT', [
				'ad',
				'artist',
				'boarding',
				'city',
				'contest',
				'location',
				'organization',
				'party',
				'pixel',
				'relation',
				'user',
				'video',
			]))
		||	!($id = have_idnumber($_REQUEST,'ELEMENTID'))
		) {
			bail(400);
		}
		require_once '_currentuser.inc';
		_currentuser::load_current_user(TINY_USER | SKIP_SETTINGS);
		if (!have_admin()) {
			bail(403);
		}
		header('Content-Type: text/html; charset=windows-1252');
		$user = null;
		$name = null;
		$show = null;
		switch ($element) {
		default:
			$name = get_element_title($element, $id);
			break;

		case 'user':
			$user = memcached_user($id);
			$name = $user ? $user['NICK'] : false;
			break;

		case 'party':
			$party = memcached_party_and_stamp($id);
			if ($party) {
				$name = true;
				ob_start();
				echo get_element_link('party',$id);
				if ($party['SUBTITLE']) {
					?> <?= MIDDLE_DOT_ENTITY ?> <? echo escape_utf8($party['SUBTITLE']);
				}
				?><small>, <?
				change_timezone('UTC');
				_dateday_display($party['STAMP_TZI']);
				change_timezone();
				?></small><?
				$show = ob_get_clean();
			}
			break;
		}
		if (!$name) {
			header('X-BAD: true');
			?><span class="error"><?= __('error:does_not_exist') ?></span><?
		} else {
			echo $show ?: get_element_link($element,$id);
			if (!empty($user)
			&&	$user['STATUS'] !== 'active'
			) {
				# header('X-BAD: true');
				?> (<span class="<?= $user['STATUS'] ?>"><?= __('status:'.$user['STATUS']) ?></span>)<?
			}
		}
		break;

	case 'invoice':
		if (!($invoicenr = have_idnumber($_SERVER,'ID'))) {
			bail(400);
		}
		require_once '_currentuser.inc';
		_currentuser::load_current_user(TINY_USER | SKIP_SETTINGS);
		if (!have_user()) {
			bail(403);
		}
		if (!($need_relations = db_simpler_array(['invoice', 'relation'], "
			SELECT DISTINCT relation.RELATIONID
			FROM invoice
			LEFT JOIN relation USING (DEBNR)
			WHERE INVOICENR = $invoicenr
			UNION
			SELECT DISTINCT RELATIONID
			FROM invoice
			WHERE INVOICENR = $invoicenr"))
		) {
			bail($need_relations === false ? 500 : 404);
		}
		if (!have_admin('invoice')
		&&	!have_relation($need_relations)
		) {
			bail(403);
		}
		if (!($invoice_data = db_single_array('invoicedata', "
			SELECT CSTAMP, DATA
			FROM invoicedata
			WHERE INVOICENR = $invoicenr"))
		) {
			bail($invoice_data === false ? 500 : 404);
		}
		require_once '_hitlog.inc';
		log_hit('invoice',$invoicenr,'download');
		[$cstamp, $data] = $invoice_data;
		require_once '_modified.inc';
		if (not_modified($cstamp)) {
			bail(304);
		}
		header('Content-Type: application/pdf');
		header('Content-Length: '.strlen($data));
		header('Content-Disposition: filename="partyflock_invoice_'.$invoicenr.'.pdf"');
		echo $data;
		break;

	case 'invoices':
		require_once '_currentuser.inc';
		require_once '_execute.inc';
		_currentuser::load_current_user(TINY_USER | SKIP_SETTINGS);
		if (!require_admin('invoice')) {
			bail(403);
		}
		if (!($last_pdfs = require_idnumber($_SERVER,'ID'))) {
			bail(400);
		}
		if (false === ($invoices = db_simpler_array(['invoice', 'invoice_pdf_processed', 'relation'], "
			SELECT INVOICENR
			FROM invoice_pdf_processed
			LEFT JOIN invoice USING (INVOICENR)
			LEFT JOIN relation USING (DEBNR)
			WHERE STAMP = $last_pdfs
			  AND (	relation.RELATIONID IS NULL
				OR	INVOICEMAILONLY IS NULL
				OR	INVOICEMAILONLY = 0)"))
		) {
			bail(500);
		}
		if (!$invoices) {
			bail(404);
		}
		foreach ($invoices as $INVOICENR) {
			if (!($DATA = db_single('invoicedata','SELECT DATA FROM data_db.invoicedata WHERE INVOICENR = '.$INVOICENR))) {
				bail($DATA === false ? 500 : 400);
			}
			$filename = uniqid("/tmpdisk/Pf__invoice_{$INVOICENR}_", true);
			file_put_contents($filename,$DATA);
			$files[] = $filename;
			register_shutdown_function(unlink(...),$filename);

			$insert_list[] = "($INVOICENR, ".CURRENTSTAMP.','.CURRENTUSERID.')';
		}
		[$rc, $stdout, $stderr] = execute('pdfunite '.implode(' ',$files).' /dev/stdout');
		if ($rc) {
			bail(503);
		} else {
			if (!db_insert('invoice_for_print','
				INSERT IGNORE INTO invoice_for_print (INVOICENR, STAMP, USERID)
				VALUES '.implode(', ', $insert_list))
			) {
				bail(503);
			}
			require_once '_nocache.inc';
			send_no_store_headers();
			header('Content-Type: application/pdf');
			header('Content-Length: '.strlen($stdout));
			require_once '_date.inc';
			[$y,$m,$d,$h,$min,$s] = _getdate();
			header('Content-Disposition: filename="partyflock_invoices_'.sprintf('%04d%02d%02d.%02d%02d%02d',$y,$m,$d,$h,$min,$s).'.pdf"');
			echo $stdout;
		}
		break;

	case 'timezone':
		require_once '_currentuser.inc';
		_currentuser::load_current_user(TINY_USER | SKIP_SETTINGS);
		if (!require_admin()
		||	!( $latitude = require_float($_GET,'LATITUDE'))
		||	!($longitude = require_float($_GET,'LONGITUDE'))
		) {
			bail(400);
		}
		if ($countryid = have_idnumber($_GET,'COUNTRYID')) {
			$short = db_single('country','SELECT SHORT FROM country WHERE COUNTRYID='.$countryid);
			if ($short) {
				$allowed = DateTimeZone::listIdentifiers(DateTimeZone::PER_COUNTRY,$short);
			}
		}
		# https://www.scribd.com/doc/2569355/Geo-Distance-Search-with-MySQL
		for ($i = 1; $i < 20; ++$i) {
			$longitude_diff = $i / abs(cos(deg2rad($latitude)))*111.3;
			 $latitude_diff = $i / 111.3;

			if (false === ($tz = db_single('tzlookup','
				SELECT TIMEZONE
				FROM tzlookup
				WHERE LATITUDE  BETWEEN '.( $latitude -  $latitude_diff).' AND '.( $latitude +  $latitude_diff).'
				  AND LONGITUDE BETWEEN '.($longitude - $longitude_diff).' AND '.($longitude + $longitude_diff).
				(empty($allowed) ? null : ' AND TIMEZONE IN ('.stringsimplode(',',$allowed).')').'
				ORDER BY '.distance($latitude,$longitude,'LATITUDE','LONGITUDE').'
				LIMIT 1'))
			) {
				bail(503);
			}
			if ($tz !== null) {
				break;
			}
		}
		if ($tz) {
			header('Content-Type: text/plain; charset=us-ascii');
			echo $tz;
		} else {
			register_warning('warning:could_not_find_timezone_LINE');
			bail(404);
		}
		break;

	case 'relationaddresses':
		require_once '_currentuser.inc';
		_currentuser::load_current_user(TINY_USER | SKIP_SETTINGS);
		if (!require_admin('relation')) {
			bail(403);
		}
		# FIXME: don't include relations with only non-active profiles
		$userlist = db_rowuse_hash(['relation','city','invoice'],'
			SELECT DISTINCT RELATIONID,relation.NAME AS REALNAME,IF(POBOX,CONCAT("Postbus ",POBOX),ADDRESS) AS ADDRESS,ZIPCODE,CITYID,COUNTRYID,POBOX
			FROM relation
			LEFT JOIN city USING (CITYID)
			WHERE (POBOX OR ADDRESS!="") AND CITYID
			ORDER BY ((SELECT CSTAMP FROM invoice WHERE invoice.DEBNR=relation.DEBNR ORDER BY INVOICENR DESC LIMIT 1)) DESC
			LIMIT '.(have_idnumber($_REQUEST,'LIMIT') ?: 600)
		);

		$title = count($userlist).' addressen van relaties';
		if ($userlist === false) {
			bail(500);
		}
		if (!$userlist) {
			bail(400);
		}

		require_once '_addressesforprint.inc';
		header('Content-Type: text/html; charset=utf-8');
		sticker_addresses($title,$userlist,getifset($_POST, 'BODY'));
		break;

	case 'contestaddresses':
		if (!($contestid = require_idnumber($_REQUEST,'CONTESTID'))) {
			bail(400);
		}
		# fall through
	case 'crewaddresses':
		require_once '_currentuser.inc';
		_currentuser::load_current_user(TINY_USER | SKIP_SETTINGS);
		if (!require_admin('user')) {
			bail(403);
		}
	/*	if (isset($flockie)) {
			$done = db_simpler_array('specialcongrats2011','SELECT USERID FROM specialcongrats2011 WHERE DONE!=0 AND VALID>0');
			if ($done === false) bail(500);
			$done[] = 0;
			$userlist = db_rowuse_hash(array('user','specialcongrats2011'),'
				SELECT DISTINCT USERID,NICK,REALNAME,ADDRESS,ZIPCODE,CITYID,COUNTRYID
				FROM user
				JOIN specialcongrats2011 USING (USERID)
				WHERE USERID NOT IN ('.implode(',',$done).')
				  AND VALID>0
				  AND DONE=0
				  AND FLOOR(ID/'.FLOCKIE_BATCH_SIZE.')='.($batch-1)
			);
			$title = count($userlist).' adressen voor flockies';*/
		if (isset($contestid)) {
			$userlist = db_rowuse_hash(['contestresponse', 'user'],'
				SELECT USERID, NICK, REALNAME, ADDRESS, ZIPCODE, CITYID, COUNTRYID
				FROM contestresponse
				JOIN user USING (USERID)
				WHERE WINSTATUS = "winner"
				  AND CONTESTID = '.$contestid.'
				ORDER BY NICK'
			);
			$title = count($userlist).' addressen van winnaars';
		} else {
			$userlist = db_rowuse_hash(array('rights','user'),'
				( SELECT DISTINCT rights.USERID,NICK,REALNAME,ADDRESS,ZIPCODE,CITYID,COUNTRYID
				FROM rights JOIN user USING (USERID)
				) UNION (
				SELECT DISTINCT rights_forum.USERID,NICK,REALNAME,ADDRESS,ZIPCODE,CITYID,COUNTRYID
				FROM rights_forum JOIN user USING (USERID)
				WHERE TYPE="admin"
				) ORDER BY USERID'
			);
			$title = count($userlist).' addressen van crew';
		}
		if ($userlist === false) {
			bail(500);
		}
		if (!$userlist) {
			bail(400);
		}
		require_once '_addressesforprint.inc';
		header('Content-Type: text/html; charset=utf-8');
		print_addresses($title,$userlist,getifset($_POST, 'BODY'));
		break;

	case 'userlist':
		require_once '_currentuser.inc';
		_currentuser::load_current_user(TINY_USER);
		if (isset($_REQUEST['SETTING'])) {
			if ($_REQUEST['SETTING'] === 'tiles') {
				$_REQUEST['SHOWIMGS'] = true;
			}
		} else {
			if (!($cacheid = require_idnumber($_REQUEST,'CACHEID'))
			||	!require_something($_REQUEST,'HASH')
			||	($bad_length = strlen($hash = $_REQUEST['HASH']) !== 32)
			) {
				if (!empty($bad_length)) {
					register_error('userlist:error:try_reloading_page_LINE',['EXTRA'=>'bad hash']);
				}
				if (isset($hash)) {
					mail_log('userlist supplied HASH has length: '.strlen($hash).', expected 32');
				}
				bail(400);
			}
			if (false === ($data = db_single('userlist_cache', '
				SELECT UNCOMPRESS(DATA)
				FROM userlist_cache
				WHERE HASH = UNHEX("'.addslashes($hash).'")
				  AND INCID = '.$cacheid
			))) {
				bail(503);
			}
			if (!$data) {
				mail_log('userlist cache with id '.$cacheid.' is gone');
				register_error('userlist:error:try_reloading_page_LINE',['EXTRA'=>'cache gone']);
				bail(404);
			}

			db_update('userlist_cache', 'UPDATE userlist_cache SET LASTUSE = '.CURRENTSTAMP.' WHERE INCID='.$cacheid);

			require_once '_userlist.inc';

			$userlist = igbinary_unserialize($data);
			$userlist->show_images = isset($_REQUEST['SHOWIMGS']);
			$userlist->cacheid = $cacheid;
			$userlist->hash = $hash;
			$userlist->query();
			ob_start();
			$userlist->display();
			$data = ob_get_clean();
			if ($userlist->show_images) {
				require_once '_lazyload.inc';
				$data = process_lazies($data);
			}
			echo $data;
		}
		require_once '_identity.inc';
		if (($have_user = have_user())
		||	CURRENTIDENTID
		) {
			$user_lists = setting('USERLISTS');
			if ($user_lists & USERLISTS_REMEMBER) {
				$user_lists = USERLISTS_REMEMBER | (isset($_REQUEST['SHOWIMGS']) ? USERLISTS_TILES : 0);
				if ($have_user) {
					db_update('settings','
						UPDATE settings SET
							USERLISTS = '.$user_lists.'
						WHERE USERID = '.CURRENTUSERID
					);
					flush_settings(CURRENTUSERID);
				} else {
					db_insupd('settingsi', '
						INSERT INTO settingsi SET
							IDENTID		= '.CURRENTIDENTID.",
							USERLISTS	= $user_lists
						ON DUPLICATE KEY UPDATE
							USERLISTS	= VALUES(USERLISTS)"
					);
					flush_settings_user_lists(CURRENTIDENTID);
				}
			}
		}
		break;

	case 'voucher':
		require_once '_voucher.inc';
		show_voucher_object();
		break;

	case 'overlaps':
		if (!isset($_POST['LOCATIONID'])) {
			$_POST['LOCATIONID'] = 0;
		}
		if (!isset($_POST['LOCATION_ADDR'])) {
			$_POST['LOCATION_ADDR'] = '';
		}
		if (!isset($_POST['MINS'])) {
			$_POST['MINS'] = 0;
		}
		foreach ([
			'PARTYID',
			'LOCATIONID',
			'DAY',
			'MONTH',
			'YEAR',
			'HOUR',
			'MINS',
			'DURATION_HOURS',
			'DURATION_MINS'
		] as $field) {
			if (false === require_number($_POST, $field)) {
				bail(400);
			}
		}
		if (!$_POST['LOCATIONID']
		&&	!require_something_trim($_POST, 'LOCATION_ADDR', null, null, utf8: true)
		) {
			bail(400);
		}
		require_once '_currentuser.inc';
		require_once '_partyoverlap.inc';

		extract($_POST, \EXTR_PREFIX_ALL, 'post');

		_currentuser::load_current_user(TINY_USER | SKIP_SETTINGS);

		if (false === ($overlaps = warn_about_overlapping_parties(
			$post_PARTYID, [
				'LOCATIONID'	=> $post_LOCATIONID,
				'LOCATION_ADDR'	=> $post_LOCATION_ADDR,
				'DURATION_SECS'	=> $post_DURATION_HOURS * ONE_HOUR
								 + $post_DURATION_MINS  * ONE_MINUTE,
				'STAMP_TZI'		=> gmmktime($post_HOUR, $post_MINS, 0, $post_MONTH, $post_DAY, $post_YEAR)
		]))) {
			bail(500);
		}
		if (!$overlaps) {
			bail(200);
		}
		header('Content-Type: application/json');
		ob_start();
		display_messages_only();
		echo safe_json_encode([
			ob_get_clean(),
			__('party:confirm:continue_with_overlap_TEXT', DO_UBB | RETURN_UTF8, [
				'PARTYID'	=> $post_PARTYID,
				'PARTYIDS'	=> implodekeys(',', $overlaps),
				'CNT'		=> count($overlaps)
			])
		]);
		bail(200);

	case 'getlog':
		require_once '_logbook.inc';
		get_log();
		break;

	case 'getlogbook':
		require_once '_logbook.inc';
		get_logbook();
		break;

	case 'readyimgs':
		require_once '_uploadimage.inc';
		bail(uploadimage_readies());

	case 'readyaimgs':
		require_once '_albumelement.inc';
		bail(albumelement_readies());

	case 'sentparty':
		if (empty($_POST['IS_SENT'])) {
			bail(400);
		}
		if (!($party = db_single_assoc(['party_is_sent', 'party'], '
			SELECT PARTYID, NAME
			FROM party_is_sent
			JOIN party USING (PARTYID)
			WHERE UNIQID = UNHEX(REPLACE("'.addslashes($_POST['IS_SENT']).'","-",""))
			ORDER BY party_is_sent.STAMP DESC
			LIMIT 1'))
		) {
			bail($party === false ? 503 : 204);
		}
		header('Content-Type: application/json');
		echo safe_json_encode($party);
		break;

	# TODO: Make a generic duplicate_presence_check()
	case 'facebook_presence_check':
		require_once '_fb.inc';
		show_facebook_presence_check();
		break;

	case 'fbinfoparse':
		if (SERVER_SANDBOX
		||	HOME_THOMAS
		||	SERVER_VIP
		) {
			function debug(string $test): void {
				error_log($test);
			}
		}
		require_once '_currentuser.inc';
		_currentuser::load_current_user(TINY_USER | SKIP_SETTINGS);
		if (!require_user()) {
			bail(401);
		}
		require_once '_facebook_description_parse.inc';
		if (is_int($result = parse_facebook_info())) {
			bail($result);
		}
		header('Content-Type: application/json');
		echo safe_json_encode($result);
		break;

	case 'parsefacebookeventlist':
		$list = true;
		# fall through
	case 'parsefacebookevent':
		if (SERVER_SANDBOX
		||	HOME_THOMAS
		||	SERVER_VIP
		) {
			function debug(string $test): void {
				error_log($test);
			}
		}
		require_once '_currentuser.inc';
		_currentuser::load_current_user(TINY_USER | SKIP_SETTINGS);
		if (!require_user()) {
			bail(403);
		}
		if (!($dom_str = require_something($_POST, 'DOMSTR'))) {
			bail(400);
		}
		if (!empty($list)) {
			if (!($element = require_element($_POST, 'ELEMENT', ['location', 'organization'], true))
			||	!($id = require_idnumber($_POST, 'ID'))
			||	!db_single_int($_POST['ELEMENT'], "
				SELECT 1
				FROM $element 
				WHERE {$element}ID = $id")
			) {
				bail(400);
			}
			require_once '_facebook_eventlist_parse.inc';
			if (false === ($obj = parse_facebook_eventlist($dom_str,$_POST['ELEMENT'],$_POST['ID']))) {
				bail(500);
			}
		} else {
			$partyid = have_idnumber($_POST, 'PARTYID');
			require_once '_facebook_event_parse.inc';
			if (false === ($obj = parse_facebook_event($dom_str, $partyid))) {
				bail(500);
			}
			same_facebook_event($partyid, $obj, utf8: true);
		}
		header('Content-Type: application/json');
		echo safe_json_encode($obj);
		break;

	case 'facebookeventlistremaining':
		require_once '_facebook_eventlist_parse.inc';
		bail(which_facebook_events_remain());

	case 'get_active_item_for_appic':
		require_once '_process_appic_changes.inc';
		bail(get_active_item());

	case 'ticketstates':
		require_once '_currentuser.inc';
		require_once '_identity.inc';
		_currentuser::load_current_user(TINY_USER | SKIP_SETTINGS);
		if (!have_admin()) {
			bail(401);
		}
		if (!($ticketids = require_number_list($_POST, 'TICKETIDS'))) {
			bail(400);
		}
		if (false === ($tickets = db_rowuse_array('contact_ticket', "
			SELECT TICKETID, STATUS, OWNERID, TRACKIT
			FROM contact_ticket
			WHERE TICKETID IN ({$_POST['TICKETIDS']})"))
		) {
			bail(500);
		}
		$result = [];
		require_once '_lock.inc';
		foreach ($tickets as $ticket) {
			/** @noinspection PhpRedundantOptionalArgumentInspection */
			extract($ticket, \EXTR_OVERWRITE);
			$result[$TICKETID] = [
				'STATUS'	=> $STATUS,
				'LOCKED'	=> is_locked(LOCK_TICKET, $TICKETID),
				'TRACKIT'	=> $TRACKIT,
			];
		}
		header('Content-Type: application/json');
		echo safe_json_encode($result);
		bail(200);

	case 'profilefilterpart':
		require_once '_profilefilter.inc';
		get_postfilter_part();
		bail(200);

	case 'ticketswapinfo':
		require_once '_ticketswap.inc';
		bail(call_get_ticketswap_info());

	// case 'tickethitstats':
	// 	require_once 'defines/appic.inc';
	// 	require_once '_date.inc';
	// 	require_once '_currentuser.inc';
	// 	_currentuser::load_current_user(TINY_USER | SKIP_SETTINGS);
	// 	if (!require_admin(['party', 'appic'])) {
	// 		bail(401);
	// 	}
	// 	# header('Content-Type: text/plain; charset=utf-8');
	// 	header('Content-Type: text/csv; charset=utf-8');
	// 	header('Content-Disposition: attachment; filename="2XPR_ticket_statistics.csv"');
	//
	// 	$partyidstr = memcached_single('party', '
	// 		SELECT GROUP_CONCAT(PARTYID ORDER BY PARTYID)
	// 		FROM party
	// 		WHERE STAMP > UNIX_TIMESTAMP() - '.ONE_YEAR,
	// 		ONE_HOUR
	// 	);
	// 	if ($partyidstr === false) {
	// 		bail(503);
	// 	}
	// 	$multidays = memcached_simple_hash('party', '
	// 		SELECT PARTYID, '.PARTY_MAIN_ID_INC.' AS MAINID
	// 		FROM party
	// 		WHERE STAMP > UNIX_TIMESTAMP() - '.ONE_YEAR.'
	// 		  AND PARTYID IN ('.$partyidstr.')
	// 		HAVING PARTYID != MAINID',
	// 		ONE_HOUR
	// 	);
	// 	if ($multidays === false) {
	// 		bail(503);
	// 	}
	// 	$tickethits_per_source = memcached_simple_hash('tickethit','
	// 		SELECT PARTYID, CAST_TO_BIT(APPIC), COUNT(DISTINCT USERID, IDENTID, IPBIN)
	// 		FROM tickethit
	// 		WHERE PARTYID IN ('.$partyidstr.')
	// 		GROUP BY PARTYID, 0+APPIC',
	// 		ONE_HOUR,
	// 		flags: DB_FORCE_SERVER,
	// 		arg: 'dbbc'
	// 	);
	// 	if ($tickethits_per_source === false) {
	// 		bail(503);
	// 	}
	// 	$stats = [];
	//
	// 	foreach ($tickethits_per_source as $partyid => $sources) {
	// 		$partyid = isset($multidays[$partyid]) ? $multidays[$partyid] : $partyid;
	// 		foreach ($sources as $appic => $hits) {
	// 			if ($appic) {
	// 				if (!isset($stats[$partyid][$appic])) {
	// 					$stats[$partyid][$appic] = $hits;
	// 				}
	// 			} else {
	// 				$stats[$partyid][$appic] ??= 0;
	// 				$stats[$partyid][$appic] += $hits;
	// 			}
	// 		}
	// 	}
	// 	uasort($stats, static fn(array $a, array $b): int =>
	// 		($b[false] ?? 0) + ($b[true] ?? 0) <=> ($a[false] ?? 0) + ($a[true] ?? 0)
	// 	);
	//
	// 	if ($fp = fopen('php://memory', 'r+')) {
	// 		$header = ['PARTYID', 'NAME', 'LINK', 'PARTYFLOCK', 'APPIC'];
	// 		fputcsv($fp, $header);
	// 		$remaining = 200;
	// 		foreach ($stats as $partyid => $sources) {
	// 			fputcsv($fp, [
	// 				$partyid,
	// 				get_element_title('party', $partyid),
	// 				'https://'.$_SERVER['HTTP_HOST'].get_element_href('party', $partyid),
	// 				($sources[false] ?? ''),
	// 				($sources[true] ?? ''),
	// 			]);
	// 			if (!--$remaining) {
	// 				 break;
	// 			}
	// 		}
	// 		fseek($fp, 0);
	// 		fpassthru($fp);
	// 		fclose($fp);
	// 	} else {
	// 		error_log('failed to open memory file');
	// 	}
	// 	bail(200);

	case 'scancode_release_date':
		if (!($partyid = require_idnumber($_REQUEST, 'PARTYID'))
		||	!($release_hour = require_idnumber($_REQUEST, 'RELEASE_HOUR'))
		) {
			bail(400);
		}
		$party = memcached_party_and_stamp($partyid);
		if (!$party) {
			bail($party === false ? 503 : 404);
		}
		header('Content-Type: text/html; charset=win-1252');
		_datedaytime_display($party['STAMP'] - $release_hour * ONE_HOUR);
		break;

	case 'alternate_names':
		require_once '_alternate_name.inc';
		bail(get_alternate_names());

	case 'get_scancodes_type':
		require_once '_scancodes.inc';
		get_scancodes_type();
		bail(200);

	case 'get_contest_close_stamp_and_days':
		require_once '_contest_update.inc';
		get_contest_close_stamp_days();
		bail(200);

	default:
		error_log('WARNING unknown object requested: '.$_REQUEST['OBJTYPE']);
		bail(404);
	}
}
