<?php

require_once '_sections.inc';

function perform_commit() {
	if ($_REQUEST['ACTION'] != 'commit'
	||	!require_admin('metad')
	||	!require_array($_POST,'SECTION')
	||	!require_last_lock(LOCK_METAD)
	) {
		return;
	}
	foreach ($_POST['SECTION'] as $id => $desc) {
		if (!is_number($id)) {
			_error('Onjuiste forumlierdata! ID ('.escape_specials($id).') niet numeriek of geen sectie!');
			return;
		}
		if ($desc) {
			$ids[] = $id;
			$setlist[] = '('.$id.',"'.addslashes($desc).'",'.CURRENTUSERID.','.CURRENTSTAMP.')';
		}
	}
	if (!db_insert('metad_log','INSERT INTO metad_log SELECT * FROM metad')
	||	!db_replace('metad','REPLACE INTO metad (SECTIONID,DESCRIPTION,MUSER<PERSON>,MSTAMP) VALUES '.implode(',',$setlist))
	||	!db_delete('metad','DELETE FROM metad WHERE SECTIONID NOT IN ('.implode(',',$ids).')')
	) {
		return;
	}
	register_notice('metad:notice:changed_LINE');
}
function display_title() {
	?>omschrijvingen<?
}
function display_body() {
	if (!require_admin('metad')) {
		return;
	}
	if (!obtain_lock(LOCK_METAD)) {
		?><p><?
		display_lockinfo(LOCK_METAD);
		?></p><?
		return;
	}
	layout_open_section_header();
	?>Meta descriptions<?
	layout_close_section_header();
	
	layout_open_box('white');
	?><p>Maximale lengte per omschrijving is 160 karakters.</p><?
	layout_close_box();

	$metad = db_simple_hash('metad','SELECT SECTIONID,DESCRIPTION FROM metad');
	if ($metad === false) {
		return;
	}
	?><form method="post" action="/metad/commit" onsubmit="return submitForm(this)"><?
	layout_open_box('white');
	layout_open_table('fw');
	foreach (__SECTIONS as $name => $id) {
		layout_start_rrow();
		echo $name;
		layout_next_cell(class: 'fw');
		?><input name="SECTION[<?= $id; ?>]" type="text" maxlength="160" value="<?
		if (isset($metad[$id])) {
			echo escape_specials($metad[$id]);
		}
		?>"><?
		layout_stop_row();
	}
	layout_close_table();
	layout_close_box('white');
	?><div class="block"><?
	?><input type="submit" value="<?= __('action:change'); ?>" /><?
	?></div><?
	?></form><?
}
