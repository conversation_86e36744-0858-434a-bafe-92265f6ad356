<?php

declare(strict_types=1);

if (!defined('SMALL_SCREEN')) {
	define('SMALL_SCREEN', get_small_screen());
}

function get_small_screen(): bool {
	if (PHP_SAPI === 'cli') {
		return false;
	}
	require_once '_cookie.inc';
	# prioritize settings cookie over detection:
	if (is_set_flag_flock_cookie(FLOCK_FLAG_ASKED_SMALL_SCREEN)) {
		return is_set_flag_flock_cookie(FLOCK_FLAG_SMALL_SCREEN);
	}
	require_once '_browser.inc';
	return probably_small_screen();
}
