<?php

require_once '_flockmod.inc';
require_once '_require.inc';
require_once '_artist_types.inc';
require_once '_freshness.inc';

const PERFORMANCE_TYPE_LOCATION = ARTIST_TYPE_SUFFIX;

set_memory_limit(512 * MEGABYTE);

const HAVE_PHOTO_PART = '
	IFNULL(
		(	SELECT IF(NO_PERSON,"no_person","upload")
			FROM uploadimage_link
			JOIN uploadimage USING (UPIMGID)
			WHERE TYPE = "artist"
			  AND ID = ARTISTID
			 LIMIT 1
		),
		(	SELECT "pf"
			FROM artist_appearance app
			WHERE app.ARTISTID = lineup.ARTISTID
			LIMIT 1
		)
	) AS HAVE_PHOTO';

require_once '_date.inc';
require_once '_artist_types.inc';
require_once '_memcache.inc';
require_once '_urltitle.inc';
require_once '_countryflag.inc';
require_once '_namedoubles.inc';
require_once '_sort.inc';
require_once '_artist.inc';
require_once '_layout.inc';
require_once '_genres.inc';

function string_is_same(string $field, ?string $value): string {
	return ' IF('.$field.' IS NULL,
			  	'.($value === null ? '1' : '0').',
			  	'.($value === null ? '0' : $field.' = "'.$value.'"').') ';
}

function bool_is_same(string $field, ?bool $value): string {
	return ' IF('.$field.' IS NULL,
			  	'.($value === null ? '1' : '0').',
			  	'.($value === null ? '0' : $field.' = '.($value ? "b'1'" : "b'0'")).') ';
}

function number_is_same(string $field, ?int $value): string {
	return ' IF('.$field.' IS NULL,
				'.($value === null ? '1' : '0').',
				'.($value === null ? '0' : $field.' = '.$value).') ';
}

function get_lineup_order(bool $times = true): string {
	return	'AREAID ASC,'.
		(	$times
		?	'ISNULL(START_STAMP) ASC,
				START_STAMP ASC,'
		:	null
		).
		get_lineup_typeorder().',
		artist.NAME ASC';
}

function get_lineup_typeorder(): string {
	if (PERFORMANCE_TYPE_LOCATION === ARTIST_TYPE_SUFFIX) {
		# main artists/acts all grouped, only vj,mc that play/perform entire night, separate

		return '
		CASE lineup.TYPE
			WHEN "vj"			THEN 100
			WHEN "mc"			THEN 100
			WHEN "presenter"	THEN 200

			ELSE 50
		END
		ASC';
	} else {
		return '
		CASE lineup.TYPE
			WHEN "rapper"			THEN 0
			WHEN "vocalist"			THEN 0
			WHEN "backing_vocalist"	THEN 1

			WHEN "act"				THEN 2
			WHEN "band"				THEN 2

			WHEN "dj"				THEN 5

			WHEN "saxophonist"		THEN 10
			WHEN "trumpet_player"	THEN 10
			WHEN "drummer"			THEN 10
			WHEN "percussionist"	THEN 10
			WHEN "intrumentalist"	THEN 10

			WHEN "show"				THEN 15
			WHEN "drag"				THEN 15

			WHEN "vj"				THEN 100
			WHEN "mc"				THEN 100
			WHEN "presenter"		THEN 200

			ELSE 50
		END
		ASC';
	}
}

function lineup_display(array $party, array|bool|null $alter = false, ?array $attribs = null, bool $ubb = false, ?array &$errors = null) {
	# Europe/Amsterdam for events without location
	$party['TIMEZONE'] ? change_timezone($party['TIMEZONE']) : change_timezone('Europe/Amsterdam');
	$rc = actual_lineup_display($party, $alter, $attribs, $ubb, $errors);
	change_timezone();
	return $rc;
}

function actual_lineup_display(array $party, array|bool|null $alter = false, ?array $attribs = null, bool $ubb = false, ?array &$errors = null) {
	if ($attribs !== null) {
		$transp = true;
		$notimes = isset($attribs['notimes']);
		$show_only_area = have_number($attribs,'area') ? $attribs['area'] : null;
		$columns = have_idnumber($attribs,'columns');
		$horiz = (!$columns || $columns > 1) && !isset($attribs['vertical']);
	} else {
		$transp = $notimes = null;
		$show_area_numbers = true;
		$horiz = false;
		$show_only_area = null;
		$columns = false;
	}

	$party_admin = have_admin('party');

	require_once '_element_access.inc';
	$flags =
		$party_admin
	||	$party['USERID']
	&&	CURRENTUSERID == $party['USERID']
	||	may_change_element('party',$party['PARTYID'])
	?	DB_FRESHEN_MEMCACHE
	:	0;

	$partyid = $party['PARTYID'];
	$party_admin = have_admin('party');
	$my_party = $party['USERID'] > 1 && CURRENTUSERID == $party['USERID'];
	$my_party_or_admin = $party_admin || $my_party;

	$lstamp_part = ',(	SELECT CONCAT(STAMP,",",PARTYID)
				FROM lineup AS l2
				JOIN party USING (PARTYID)
				WHERE l2.ARTISTID=lineup.ARTISTID
				  AND STAMP<'.$party['STAMP'].'
				ORDER BY STAMP DESC
				LIMIT 1
			) AS LSTAMP';

	$lstamp_part = null;

	require_once 'defines/party.inc';
	$single_lineup = single_lineup_multi_days($party);

	$read_partyid = $single_lineup ?: $partyid;

	$have_areas = memcached_rowuse_hash(['partyarea','lineup'],'
		SELECT PARTYAREAID, partyarea.*, COUNT(lineup.LINEUPID) AS HAVE_LINEUP
		FROM partyarea
		LEFT JOIN lineup USING (PARTYID,AREAID)
		WHERE PARTYID='.$read_partyid.'
		GROUP BY PARTYAREAID
		ORDER BY AREAID ASC',
		TENMINS,
		null,
		$flags
	);
	if ($have_areas === false) {
		return;
	}

	$party['areas'] = $have_areas;

	?><div<?
	if ($transp || $transp === false || $ubb) {
		?> class="lineupcont ubb"<?
	} else {
		?> id="lineup"<?
	}
	?> data-areas="<?= count($have_areas) ?>"<?
	?>><?
	$party['COUNTRYID'] = $party['city']['COUNTRYID'] ?? $party['boarding']['COUNTRYID'] ?? $party['location']['COUNTRYID'] ?? 0;

	require_once '_complete.inc';
	show_incomplete_notice($party);

	if ($single_lineup) {
		?><div class="notice block"><?
		echo __('partylist:spread_over_days');
		if ($party_admin) {
			if ($read_partyid != $partyid) {
				?>, <?= element_name('source') ?>: <?
				echo get_element_link('party',$read_partyid);
			}
		}
		?></div><?
	}

	$area_has_times = db_same_hash('lineup','
		SELECT DISTINCT AREAID
		FROM lineup
		WHERE PARTYID = '.$read_partyid.'
		  AND START_STAMP'
	);

	$party['HAVETIMES'] = $havetimes =
			!$notimes
		&&	(	!$party['TIMETABLE_PSTAMP']
			||	$party['TIMETABLE_PSTAMP'] <= CURRENTSTAMP
			||	$my_party_or_admin
			||	$area_has_times
			);

	if ($area_has_times
	&&	setting('FONT_TYPE') !== 'Titillium Web') {
		# Titillium is used for the timetable
		include_style('font_titillium');
	}

	if (!$alter) {
		if (isset($attribs['hideflags'])) {
			foreach (explode(',',$attribs['hideflags']) as $id) {
				$hideflags[$id] = $id;
			}
		}

#		$hideflags[$party['COUNTRYID']] = $party['COUNTRYID'];

		$allflags = isset($attribs['allflags']);

		if ($havetimes) {
			if (!$party['DURATION_SECS']) {
				?><div class="error larger block"><?
				echo __('party:error:make_sure_there_is_a_duration_LINE');
				?></div><?
			}

			$switch = get_dst_switch_times(
				$party['TIMEZONE'] ?: 'UTC',
				$party['STAMP'],
				$party['STAMP'] + $party['DURATION_SECS'] + 1
			);

			if ($switch) {
				[$switch_stamp, $switch_offset, $pre_switch_hour, $pre_switch_mins, $switch_hour, $switch_mins] = $switch;

				if (db_single('lineup','SELECT MAX(START_STAMP + DURATION) FROM lineup WHERE PARTYID='.$party['PARTYID'])
				==	$party['STAMP'] + $party['DURATION_SECS']
				) {
					?><div class="ns nb block"><?
					echo __('party:info:timetable_time_switch_ignored_TEXT', DO_NL2BR | DO_UBB, ['WINTER' => $switch[1] < 0 ? 1 : 0]);
					?></div><?
				} else {
					?><div class="warning ns nb block"><?
					echo __('party:info:timetable_time_switch_ambiguous_LINE', ['WINTER' => $switch[1] < 0 ? 1 : 0]);
					?></div><?
				}
			}

			$lineup_rows = memcached_rowuse_array(['lineup', 'partyarea', 'artist', 'itemname'],'
				SELECT	LINEUPID,PARTYID,lineup.ARTISTID,NEEDUPDATE,artist.MSTAMP,
					COALESCE((SELECT NAME FROM itemname WHERE ELEMENT="artist" AND ID=ARTISTID AND ENDSTAMP>'.$party['STAMP'].' ORDER BY ENDSTAMP DESC LIMIT 1),artist.NAME) AS NAME,
					artist.ACCEPTED, artist.DELETED, GENERIC,artist.USERID,'.get_deceased_part($party['STAMP']).',COUNTRYID,LIVE_COUNTRYID,
					(SELECT GROUP_CONCAT(GID) FROM artist_genre WHERE artist_genre.ARTISTID=artist.ARTISTID) AS ARTIST_GENRES,
					lineup.TYPE,LIVESTREAM_ADDRESS,
					AREAID,PARTYAREAID,
					START_STAMP,START_STAMP_UTC,DURATION,DST,END_DST,
					(SELECT GROUP_CONCAT(MEMBERID) FROM artistgroup WHERE artistgroup.ARTISTID=lineup.ARTISTID AND HIDDEN=0 AND ACTIVE=1) AS MEMBERIDS,
					(SELECT GROUP_CONCAT(UPIMGID) FROM uploadimage_link WHERE TYPE = "lineup" AND uploadimage_link.ID = LINEUPID) AS HAS_CUSTOM_PHOTO,
					'.HAVE_PHOTO_PART.',
					partyarea.NAME AS AREA_NAME,HOSTEDBY,HOSTEDBYID,GENRES'.
					($my_party_or_admin ? $lstamp_part : null).'
				FROM lineup
				JOIN artist USING (ARTISTID)
				LEFT JOIN partyarea USING (PARTYID,AREAID)
				WHERE PARTYID = '.$read_partyid.'
				ORDER BY '.get_lineup_order(times: true),
				TENMINS,
				null,
				$flags
			);
			if ($lineup_rows === false) {
				?></div><?
				return false;
			}
		} else {
			$lineup_rows = memcached_rowuse_array(
				['lineup', 'partyarea', 'artist', 'artist_genre', 'artistgroup', 'itemname'], '
				SELECT	LINEUPID,PARTYID,ARTISTID,NEEDUPDATE,artist.MSTAMP,
					COALESCE((SELECT NAME FROM itemname WHERE ELEMENT="artist" AND ID=ARTISTID AND ENDSTAMP>'.$party['STAMP'].' ORDER BY ENDSTAMP DESC LIMIT 1),artist.NAME) AS NAME,
					artist.ACCEPTED, artist.DELETED, GENERIC,artist.USERID,'.get_deceased_part($party['STAMP']).',COUNTRYID,LIVE_COUNTRYID,
					(SELECT GROUP_CONCAT(GID) FROM artist_genre WHERE artist_genre.ARTISTID=artist.ARTISTID) AS ARTIST_GENRES,
					lineup.TYPE,LIVESTREAM_ADDRESS,
					(SELECT GROUP_CONCAT(MEMBERID) FROM artistgroup WHERE artistgroup.ARTISTID=lineup.ARTISTID AND HIDDEN=0 AND ACTIVE=1) AS MEMBERIDS,
					(SELECT GROUP_CONCAT(UPIMGID) FROM uploadimage_link WHERE TYPE = "lineup" AND uploadimage_link.ID = LINEUPID) AS HAS_CUSTOM_PHOTO,
					'.HAVE_PHOTO_PART.',
					AREAID,PARTYAREAID,partyarea.NAME AS AREA_NAME,HOSTEDBY,HOSTEDBYID,GENRES'.
					($my_party_or_admin ? $lstamp_part : null).'
				FROM lineup
				JOIN artist USING (ARTISTID)
				LEFT JOIN partyarea USING (PARTYID,AREAID)
				WHERE PARTYID = '.$read_partyid.'
				ORDER BY AREAID ASC,'.get_lineup_order(times: false).',NAME ASC',
				TENMINS,
				null,
				$flags
			);
			if ($lineup_rows === false) {
				?></div><?
				return false;
			}

			# filter out identical lineuprows, because same artist may have multiple timeslots
			# which are not shown in this path

			$areas = [];
			foreach ($lineup_rows as $ndx => $lineup_row) {
				if (isset($areas[$lineup_row['AREAID']][$lineup_row['ARTISTID']])) {
					# double artist, don't display
					unset($lineup_rows[$ndx]);
					continue;
				}
				$areas[$lineup_row['AREAID']][$lineup_row['ARTISTID']] = true;
			}
		}

		if ($show_only_area !== null) {
			$newlineuprows = [];
			foreach ($lineup_rows as $lineup_row) {
				if ($lineup_row['AREAID'] == $show_only_area) {
					$newlineuprows[] = $lineup_row;
				}
			}
			$lineup_rows = $newlineuprows;
		} else {
			$lineup_rows = inject_empty_areas($lineup_rows,$have_areas);
		}

		if (!$lineup_rows) {
			?></div><?
			return true;
		}

		$shows = ['lineup'];
		if (!$notimes) {
			$shows[] = 'timetable';
		}
		foreach ($shows as $what) {
			if (!($pstamp = $party[strtoupper($what).'_PSTAMP'])
			||	$pstamp <= CURRENTSTAMP
			) {
				continue;
			}
			require_once '_party.inc';
			$flg = $what === 'lineup' ? PARTYFLG_HIDDEN_LINEUP_PSTAMP : PARTYFLG_HIDDEN_TIMETABLE_PSTAMP;
			$hidden = $party['FLAGS'] & $flg;
			$maynot_view =
				!have_user()
			||	!$my_party_or_admin;

			list($year,$month,$day,$hour,$mins) = _getdate($pstamp);
			$func = $hour || $mins ? '_datedaytime_display' : '_dateday_display';
			ob_start();
			$func($pstamp);
			$date = ob_get_clean();
			?><div class="block"><?
			echo __('party:info:'.$what.'_release_LINE',['DATE'=>$hidden ? null : $date]);
			if ($hidden && !$maynot_view) {
				?> <span class="light">(<?= $date ?>)</span><?
			}
			?></div><?
			if ($maynot_view && $what == 'lineup') {
				?></div><?
				return null;
			}
		}
		require_once '_favourite.inc';
		require_once '_star.inc';

		if ($transp) {
/*			if (!$horiz) {
				?><ul class="lineup"><?
			}*/
		} else {
			$classes = [$transp ? 'dens' : 'lineup fw'];
			if ($notimes !== null) {
				$classes[] = 'nomargin';
			}
			?><table<?
			if ($classes) {
				?> class="<?= implode(' ',$classes) ?>"<?
			}
			?>><?
		}
		$prevareaid = null;
		$areacnt = 0;
		foreach ($lineup_rows as $lineup_row) {
			if ($prevareaid !== $lineup_row['AREAID']) {
				$prevareaid = $lineup_row['AREAID'];
				++$areacnt;
			}
		}

		// create favourite list
		$favs = get_favourites(CURRENTUSERID,'artist') ?: null;
		$areaid = -1;
		$isadmin = have_admin(['party','artist']);
		$prev =  null;

		$first = true;
		$area_per_column = $per_column = $left_column = false;
		if (SMALL_SCREEN) {
			$horiz = false;
			$left_column = $per_column = false;
			$area_per_column = $columns = 1;
		} else {
			if ($areacnt > 1) {
				$area_per_column = $columns ?: ($columns ? ceil($areacnt/$columns) : false);
			} else {
				$left_column = $per_column = !$horiz && $columns ? ceil(count($lineup_rows)/$columns) : false;
			}
		}
		if (isset($attribs['setinfo'])) {
			foreach ($attribs['setinfo'] as $info) {
				if (preg_match('"^(\d+),(.*)$"',$info,$match)) {
					$setinfo[$match[1]] = $match[2];
				}
			}
		}
		$current_area = 0;
		$no_time_group = false;
		$prev_time = [$party['STAMP'], 0, true];

		/*$show_weekday
		=	(	$party_admin
			||	$party['DURATION_SECS'] > ONEDAY + 6*ONEHOUR
			);*/

		$show_weekday = true;
		$weekday_placeholder = $show_weekday ? str_repeat('&nbsp;', get_short_weekday_len()) : '';

		foreach ($lineup_rows as $ndx => $lineup_row) {
			if (!isset($lineup_row['DELETED'])) {
				mail_log('DELETED not set in lineup_row', item: $lineup_rows);
			}
			if ((!$lineup_row['ACCEPTED'] || $lineup_row['DELETED'])
			&&	!$isadmin
			&&	$lineup_row['USERID'] !== CURRENTUSERID
			) {
				continue;
			}
			fill_lineup($lineup_row);

			if ($areaid != $lineup_row['AREAID']) {
				if ($current_area != 0) {
					# show empty space at end of previous area:
					show_empty_spaces($party, null, $prev_time, 1, $errors);
				}
				$prev_time = [$party['STAMP'], 0, true];

				$no_time_group = false;
				++$current_area;

				if ($transp) {
					if ($areaid !== -1) {
						?></ul><?
					}
					?><ul class="vtop lineup<?
					if ($per_column || $horiz) {
						?> il<?
					}
					?>"<?
					if ($horiz && ($areawidth = getifset($attribs,'areawidth') ?: ($area_per_column ? (100 / $area_per_column).'%' : ($notimes ? '15em' : false)))) {
						?> style="<?
						if (!preg_match('"%$"',$areawidth)) {
							?>width:100%;max-<?
						}
						?>width:<?= escape_specials($areawidth) ?>"<?
					}
					?>><?
				}
				$area = getifset($have_areas,$lineup_row['PARTYAREAID']);

				if ((	$areacnt > 1
					||	$area
					&&	(	$area['NAME']
						||	$area['HOSTEDBY']
						||	$area['HOSTEDBYID']
						||	$area['GENRES']
						)
					)
				&&	(	$show_only_area === null
					||	isset($attribs['showareaname'])
					)
				) {
					if ($transp) {
						?><li class="<?
						if ($areaid != -1
						&&	!$horiz
						) {
							?>next <?
						}
						?>area"><?
					} else {
						?><tr><th<?
						if ($transp) {
							?> class="underline"<?
						}
						?>><?
						if ($lineup_row['AREAID']
						&&	$show_area_numbers
						) {
							?><span class="r"><?= $lineup_row['AREAID'] ?></span><?
						}
					}
					$areaname = !empty($area['NAME']) ? escape_utf8($area['NAME']) : null;
					$hostedbyname = !empty($area['HOSTEDBYID']) ? get_element_title('organization',$area['HOSTEDBYID']) : null;
					$host = empty($area['HOSTEDBY']) ? (empty($area['HOSTEDBYID']) ? null : $area['HOSTEDBYID']) : $area['HOSTEDBY'];
					$genres = !empty($area['GENRES']) ? escape_utf8($area['GENRES']) : '';

					$areaname_is_host =
						$hostedbyname
					&&	(	!$areaname
						||	strtolower(utf8_to_ascii(str_replace(' ','',$areaname))) ===
							strtolower(utf8_to_ascii(str_replace(' ','',$hostedbyname)))
						);

					ob_start();

					if ($lineup_row['HOSTEDBYID'] && $areaname_is_host) {
						echo get_element_link('organization',$lineup_row['HOSTEDBYID']);
					} elseif ($areaname) {
						echo $areaname;
					} elseif ($lineup_row['HOSTEDBY']) {
						echo escape_utf8($lineup_row['HOSTEDBY']);
					} else {
						?>Area <? echo $lineup_row['AREAID'];
					}
					if ($areastr = ob_get_clean()) {
						?><span class="name"><?= $areastr ?></span><?
					}
					if (!$areaname_is_host && $areaname && $host) {
						if ($areaname) {
							if ($horiz) {
								?><br /><?
								?><small style="margin: 0 1em"><?
							} else {
								?>, <small><?
							}
						} else {
							?><small><?
						}
						if ($lineup_row['HOSTEDBYID']) {
							$hosts = [get_element_link('organization', $lineup_row['HOSTEDBYID'])];
							if ($lineup_row['HOSTEDBY']) {
								$hosts[] = escape_utf8($lineup_row['HOSTEDBY']);
							}
						} else {
							$hosts = [escape_utf8($lineup_row['HOSTEDBY'])];
						}

						echo str_replace('%HOST%', implode(', ', $hosts), __('area:hosted_by', KEEP_EMPTY_KEYWORDS | ($areaname ? 0 : DO_CAPFIRST)));

						?></small><?
					}
					if ($genres) {
						?><br /><small class="nb hpad ib"><?= $genres ?></small><?
					}
					if ($transp) {
						?></li><?
					} else {
						?></th></tr><?
					}
				}
				$areaid = $lineup_row['AREAID'];

				if (!empty($lineup_row['EMPTY_AREA'])) {
					continue;
				}

				$sametime = false;
			} else {
				$sametime = $havetimes
						&&	$prev
						&&	$prev['START_STAMP'] === $lineup_row['START_STAMP']
						&&	$prev['DURATION']	 === $lineup_row['DURATION'];

				if ($per_column
				&&	!--$left_column
				) {
					$left_column = $per_column;
					?></ul><ul class="vtop lineup<?
					if ($per_column) {
						?> il<?
					}
					?>"><?
				}
				if (!$first && !$sametime) {
					if ($transp) {
						?></li><?
					} else {
						?></td></tr><?
					}
				}
			}

			$classes = $superclasses = [];

			if (!$transp) {
				$rclass = show_empty_spaces($party, $lineup_row, $prev_time, 1, $errors);
				if ($rclass) {
					$superclasses[] = $rclass;
				}
			}

			$is_fav = isset($favs[$lineup_row['ARTISTID']]);
			if (!$lineup_row['ACCEPTED']) {
				$classes[] = 'unaccepted';
			}

			if ($lineup_row['DELETED']) {
				$classes[] = 'light';
			}

			if (!isset($lineup_row['DECEASED'])) {
				mail_log('missing DECEASED', item: $lineup_row, include_trace: true);
			}

			if ($lineup_row['DECEASED']
			||	$lineup_row['STOPPED']
			) {
				$superclasses[] = 'light';
			}
			$havetime = isset($havetimes[$lineup_row['AREAID']]);
			if ($havetime) {
				$no_end = !$lineup_row['DURATION'];

				if ($nowplaying
				=	$party['STAMP']
				&&	$party['STAMP'] <= CURRENTSTAMP
				&&	$party['STAMP'] + ($party['DURATION_SECS'] ?: 6*ONEHOUR) > CURRENTSTAMP
				&&	$lineup_row['START_STAMP'] <= CURRENTSTAMP
				&&	$lineup_row['END_STAMP'] > CURRENTSTAMP
				) {
					$superclasses[] = 'active-performance';
				}
			}
			if (!$sametime) {
				if ($transp) {
					?><li><?
				} else {
					layout_start_rrow();
				}
			}
			if (!$transp && $is_fav) {
				$superclasses[] = 'favourite-artist';
			}
			if ($party_admin
			&&	$lineup_row['HAS_CUSTOM_PHOTO']
			) {
				?><div class="abs" style="left: -.3em;"><?= CAMERA_ENTITY ?></div><?
			}
			?><div<?
			if ($classes += $superclasses) {
				?> class="<?= implode(' ', $classes) ?>"<?
			}
			?>><?


			$show_line = false;
			if ($havetime) {
				if ($show_line
				=	$transp
				&&	!$lineup_row['START_STAMP']
				&&	!$no_time_group
				) {
					$no_time_group = true;
					?><span class="lineupsep"><?
				}
				if ($nowplaying) {
					?><span style="float:right">&larr;</span><?
				}
				if ($lineup_row['START_STAMP']) {
					if (!$sametime || !ROBOT) {
						if ($show_weekday) {
							$weekday = $lineup_row['START_DATE'][6] ?? null;
						}

						if (!$sametime
						&&	$no_end
						) {
							?><span class="light times"><?
							if ($show_weekday && $weekday !== null) {
								echo short_weekday_name($weekday) ?> <?
							}
							$time_start = $lineup_row['TIME_START_TZI'];
							$time_end =   $lineup_row['TIME_END_TZI'];
							printf('%02u:%02u<span class="relative"><div class="abs" style="top:0; left: 0;"> &rarr; ?</div><span class="invisible"> - %02u:%02u:</span></span>',
								$hourpart = (int)($time_start / 100), $time_start - $hourpart * 100,
										$hourpart = (int)($time_end   / 100), $time_end   - $hourpart * 100
							);
							?></span>&nbsp;<?
						} else {
							?><span class="<?= $sametime ? 'invisible' : 'light' ?> times"><?

							if ($show_weekday && $weekday !== null) {
								?><span class="weekday"><?= short_weekday_name($weekday) ?></span> <?
							}

							if (!empty($switch)) {
								?><span class="notice ns nb"><?
								$pre_dst  = '<span class="light6 dst-indicator">'.($lineup_row['DST']	  ? __('dst:s(ummertime)') : __('dst:w(intertime)')).'</span> ';
								$post_dst = '<span class="light6 dst-indicator">'.($lineup_row['END_DST'] ? __('dst:s(ummertime)') : __('dst:w(intertime)')).'</span> ';
								$time_start = $lineup_row['TIME_START'];
								$time_end =   $lineup_row['TIME_END'];
								printf($pre_dst.'%02u:%02u &ndash; '.$post_dst.'%02u:%02u:&nbsp;',
									$hourpart = (int)($time_start / 100), $time_start - $hourpart * 100,
											$hourpart = (int)($time_end   / 100), $time_end   - $hourpart * 100
								);
								?></span><?
							}
							$time_start = $lineup_row['TIME_START_TZI'];
							$time_end   = $lineup_row['TIME_END_TZI'];
							printf('%02u:%02u - %02u:%02u:',
								$hourpart = (int)($time_start / 100), $time_start - $hourpart * 100,
										$hourpart = (int)($time_end   / 100), $time_end   - $hourpart * 100
							);

							?></span>&nbsp;<?
						}
					}
				} elseif (!ROBOT) {
					?><span class="invisible times"><?
					if ($show_weekday) {
						# works for Titillium
						?><span class="weekday"><?= $weekday_placeholder ?></span><?
					}
					if (!empty($switch)) {
						?>00:00&nbsp;&ndash;&nbsp;00:00:&nbsp;<?
					}
					?>00:00&nbsp;&ndash;&nbsp;00:00:&nbsp;<?
					?></span><?
				}
				$prev = $lineup_row;
			}
			if (PERFORMANCE_TYPE_LOCATION === ARTIST_TYPE_PREFIX) {
				if ($lineup_row['TYPE']) {
					show_artist_type_indicator($lineup_row['TYPE'], ARTIST_TYPE_PREFIX);

				} elseif (have_admin(['artist', 'party'])) {
					?><span class="error"><?= element_name('function') ?>?: </span><?
				}
			}

			$title = $lineup_row['NAME'];
			$href = get_element_href('artist',$lineup_row['ARTISTID'],$lineup_row['NAME']);

			?><span itemprop="performer" itemscope itemtype="https://schema.org/Person"><?
			?><a itemprop="url" href="https://<?= $_SERVER['HTTP_HOST'],$href ?>"><?
			?><span itemprop="name"><?= escape_utf8($title) ?></span><?
			?></a><?
			?></span><?

			if (PERFORMANCE_TYPE_LOCATION === ARTIST_TYPE_SUFFIX) {
				if ($lineup_row['TYPE']) {
					?><span class="small light7"><?
					show_artist_type_indicator($lineup_row['TYPE'], ARTIST_TYPE_SUFFIX);
					?></span><?

				} elseif (have_admin(['artist', 'party'])) {
					?><span class="lmrgn error"><?= element_name('function') ?>?</span><?
				}
			}
			show_dead($lineup_row);
			show_need_update($lineup_row);

			if ($lineup_row['DELETED']) {
				?> &rarr; <span class="deleted"><?= __('status:deleted') ?></span><?
			}

			$show_country =
				$lineup_row['COUNTRYID']
			&&	(	$allflags
				||	!isset($hideflags[$lineup_row['COUNTRYID']])
				);

			$show_live_country =
#				(HOME_OFFICE || have_admin() || SERVER_VIP)
				$lineup_row['LIVE_COUNTRYID']
			&&	($lineup_row['LIVE_COUNTRYID'] != $lineup_row['COUNTRYID'])
			&&	(	$allflags
				||	!isset($hideflags[$lineup_row['LIVE_COUNTRYID']])
				);

			if ($show_country || $show_live_country) {
				if ($show_country) {
					show_country_flag_for_artist(
						$lineup_row['ARTISTID'],
						$lineup_row['COUNTRYID'],
						$party['COUNTRYID'],
						'light',
						false,
						$allflags
					);
				}
				if ($show_live_country) {
					?><span style="transform: scale(.9);"> <span class="light">&rarr;</span> <?
					show_country_flag_for_artist(
						$lineup_row['ARTISTID'],
						$lineup_row['LIVE_COUNTRYID'],
						$party['COUNTRYID'],
						'light',
						false,
						true
					);
					?></span><?
				}
			}
			if ($is_fav) {
				show_star('artist');
			}
			if (isset($setinfo)
			&&	($setstr = getifset($setinfo, $lineup_row['ARTISTID']))
			) {
				?><small class="nowrap light">&nbsp;<?= escape_specials($setstr) ?></small><?
			}

			if ($show_line) {
				?></span><?
			}

			show_freshness_indicators('artist', $lineup_row);
			show_master_genres($lineup_row['ARTIST_GENRES']);

			?></div><?

			if (!$ubb) {
				# useful for noticing wrong connections
				show_lineup_warning($lineup_row, $party);
			}

			if (!$ubb
			&&	$lineup_row['MEMBERIDS']
			) {
				$artistids = explode(',', $lineup_row['MEMBERIDS']);
				if (count($artistids) <= 6) {
					$parts = [];
					foreach ($artistids as $artistid) {
						ob_start();
						$is_fav = isset($favs[$artistid]);
						echo get_element_link('artist',$artistid);
						if ($is_fav) {
							?> <? show_star('artist');
						}
						$parts[] = ob_get_clean();
					}
					?><div style="padding-top:0"><?
					if (!ROBOT
					&&	$havetime
					) {
						?><span class="invisible nowrap times"><?= $weekday_placeholder ?>&nbsp;00:00&nbsp;&ndash;&nbsp;00:00:&nbsp;</span><?
					}
					?><span class="light5"><?
					echo implode(', ', $parts);
					?></span><?
					?></div><?
				}
				?></div><?
			}

			$first = false;
		}
		if ($transp) {
			?></li></ul><?
		} else {
			show_empty_spaces($party, null, $prev_time, 1, $errors);
			?></td></tr></table><?
		}
		?></div><?
		return null;
	} else {
		?><div class="bold block" style="color:#c100ffe6"><?
		?>Check voor lineup / timetable ook <a href="https://festivalfans.nl/" target="_blank">Festivalfans</a><?
		?></div><?
	}

	$box_title = '<h2>'.Eelement_name('lineup').' &amp; '.Eelement_name('timetable').'</h2>';

	static $__warn = null;
	if ($__warn === null) {
		?><div<?
		?> data-warn="<?= str_replace('"','&quot;',__('partyarea:warn:sure_to_remove_LINE')) ?>"<?
		?> id="area-remove-warn"<?
		?>><?
		?></div><?
		$__warn = true;
	}

	if ($alter === true) {
		require_once '__translation.php';
		$lineup_rows = db_rowuse_array(
			['lineup','partyarea','artist','itemname'],'
			SELECT	LINEUPID,NEEDUPDATE,
				lineup.ARTISTID,artist.MSTAMP,
				(SELECT GROUP_CONCAT(GID) FROM artist_genre WHERE artist_genre.ARTISTID=artist.ARTISTID) AS ARTIST_GENRES,
				COALESCE((SELECT NAME FROM itemname WHERE ELEMENT="artist" AND ID=ARTISTID AND ENDSTAMP>'.$party['STAMP'].' ORDER BY ENDSTAMP DESC LIMIT 1),artist.NAME) AS NAME,
				artist.ACCEPTED, artist.DELETED, GENERIC,'.get_deceased_part($party['STAMP']).',COUNTRYID,
				(SELECT GROUP_CONCAT(UPIMGID) FROM uploadimage_link WHERE TYPE = "lineup" AND uploadimage_link.ID = LINEUPID) AS HAS_CUSTOM_PHOTO,
				'.HAVE_PHOTO_PART.',
				lineup.TYPE,LIVESTREAM_ADDRESS,
				lineup.AREAID,
				START_STAMP,START_STAMP_UTC,DURATION,
				artist.TYPE AS TYPES,
				partyarea.PARTYAREAID,partyarea.NAME AS AREA_NAME,HOSTEDBY,HOSTEDBYID,GENRES'.
				$lstamp_part.'
			FROM lineup
			JOIN artist USING (ARTISTID)
			LEFT JOIN partyarea ON partyarea.AREAID=lineup.AREAID AND partyarea.PARTYID='.$partyid.'
			WHERE lineup.PARTYID = '.$partyid.'
			ORDER BY '.get_lineup_order(times: $havetimes ? true : false),
			$flags
		);

		if ($lineup_rows === false) {
			?></div><?
			return false;
		}

		ob_start();

		$lineup_rows = inject_empty_areas($lineup_rows,$have_areas);

		$showtypeselect = false;
		$maxareaid = 0;
		$totalopts = [];

		foreach ($lineup_rows as &$lineup_row) {
			if ($lineup_row['AREAID'] > $maxareaid) {
				$maxareaid = $lineup_row['AREAID'];
			}
			if (!empty($lineup_row['EMPTY_AREA'])) {
				continue;
			}
			fill_lineup($lineup_row);
			if ($lineup_row['TYPES']) {
				$opts = null;
				$optcnt = 0;
				foreach (explode(',', $lineup_row['TYPES']) as $type) {
					if (artist_type_ok_for_lineups($type)) {
						$opts[$type] = $type;
						$totalopts[$type] = true;
						++$optcnt;
					}
				}
				if ($optcnt > 1) {
					$type_stats[$lineup_row['ARTISTID']] = sort_artist_types($opts, $lineup_row['ARTISTID'], stamp: $party['STAMP']);
					$lineup_row['OPTS'] = $opts;
				} else {
					$lineup_row['OPT'] = ($optcnt == 1) ? keyval($opts)[0] : $lineup_row['TYPE'];
				}
			} else {
				$lineup_row['OPT'] = $lineup_row['TYPE'];
			}
		}
		unset($lineup_row);
		$showtypeselect = count($totalopts) > 1;

		foreach (get_artist_type() as $type => $true) {
			?><meta id="pprfx-<?= $type ?>" content="<?
			show_artist_type_indicator($type, PERFORMANCE_TYPE_LOCATION);
			?>"><?
		}

		layout_open_box('party');
		layout_box_header($box_title);

		?><form<?
		?> accept-charset="utf-8"<?
		?> autocomplete="off"<?
		?> method="post"<?
		?> action=""<?
		?> onsubmit="return submitLineupForm(this,<?= $partyid; ?>)"><?

		?><table class="lineup hha alter"><?
		?><tr><?
		?><th class="left"><?= Eelement_name('artist') ?></th><?
		?><th class="center" colspan="2"><?= Eelement_name('start') ?></th><?
		?><th class="center"><?= Eelement_name('stop') ?></th><?
		?><th class="center"><?= Eelement_name('area') ?></th><?
		if ($showtypeselect) {
			?><th></th><?
		}
		?><th></th></tr><?
		$areaid = -1;
		$maxareaid = max(30,$maxareaid+5);
		$prev_time = [$party['STAMP'],0,true];
		foreach ($lineup_rows as $lineup_row) {
			if ($areaid != $lineup_row['AREAID']) {
				if ($areaid != -1) {
					# show empty space at end of previous area:
					show_empty_spaces($party, null, $prev_time, $showtypeselect ? 7 : 6, $errors);
				}
				$prev_time = [$party['STAMP'],0,true];

				$areaid = $lineup_row['AREAID'];

				?><tr class="nohl arearow"><th class="left" colspan="<?= $showtypeselect ? 7 : 6; ?>"><?
/*				?><span class="r"><?= $areaid ?></span><?*/
				show_area_formpart(
					$partyid,
					$areaid,
					getifset($have_areas,$lineup_row['PARTYAREAID']) ?: null
				);
				?></th></tr><?
			}
			if (!empty($lineup_row['EMPTY_AREA'])) {
				continue;
			}
			fill_lineup($lineup_row);

			$pclass = show_empty_spaces($party, $lineup_row, $prev_time, $showtypeselect ? 7 : 6, $errors);

			$lineupid = $lineup_row['LINEUPID'];
			$deceased = $lineup_row['DECEASED'];

			$classes = [];

			if ($pclass) {
				$classes[] = $pclass;
			}
			if (!$lineup_row['ACCEPTED']) {
				$classes[] = 'unaccepted';
			}
			if ($lineup_row['DELETED']) {
				$classes[] = 'light';
			}

			layout_start_rrow(0,$classes ? implode(' ',$classes) : null);

			?><input type="hidden" name="ARTISTID[<?= $lineupid; ?>]" value="<?= $lineup_row['ARTISTID']; ?>"><?
			if (!$showtypeselect) {
				?><input type="hidden" name="TYPE[<?= $lineupid; ?>]" value="<?= $lineup_row['OPT']; ?>"><?
			}
			if (PERFORMANCE_TYPE_LOCATION === ARTIST_TYPE_PREFIX) {
				?><span id="prfx<?= $lineupid ?>"><?
				if ($lineup_row['TYPE']) {
					show_artist_type_indicator($lineup_row['TYPE'], ARTIST_TYPE_PREFIX);

				} elseif (have_admin(['artist', 'party'])) {
					?><span class="lmrgn error"><?= element_name('function') ?>?: </span><?
				}
				?></span><?
			}
			echo str_replace('<a ','<a tabindex="-1" ',get_element_link('artist',$lineup_row['ARTISTID'],$lineup_row['NAME']));

			if (PERFORMANCE_TYPE_LOCATION === ARTIST_TYPE_SUFFIX) {
				?><span class="small<?
				if ($lineup_row['TYPE']) {
					?> light7<?
				}
				?>" id="prfx<?= $lineupid ?>"><?
				if ($lineup_row['TYPE']) {
					echo show_artist_type_indicator($lineup_row['TYPE'], ARTIST_TYPE_SUFFIX);

				} elseif (have_admin(['artist', 'party'])) {
					?>&middot; <span class="error"><?= element_name('function') ?>?</span><?
				}
				?></span><?
			}

			show_dead($lineup_row);
			show_need_update($lineup_row);
			show_country_flag_for_artist(
				$lineup_row['ARTISTID'],
				$lineup_row['COUNTRYID'],
				$party['COUNTRYID'],
				'light'
			);
			show_freshness_indicators('artist', $lineup_row);
			show_master_genres($lineup_row['ARTIST_GENRES']);

			if (!$transp) {
				show_lineup_warning($lineup_row,$party);
			}

			$timeNotice = null;

			if ($party['LIVESTREAM']) {
				?><br /><?
				show_livestream_field("LIVESTREAM_ADDRESS[$lineupid]", $lineup_row);
			}

			?></td><?
			?><td class="center"><?
				show_day_field($party,"DATE_START[$lineupid]",$lineup_row);
			?></td><?
			?><td class="center"><?
				show_time_field("TIME_START[$lineupid]",$lineup_row['TIME_START_TZI'],$timeNotice);
			?></td><?
			?><td class="center"><?
				show_time_field("TIME_END[$lineupid]",$lineup_row['TIME_END_TZI'],$timeNotice);
			?></td><?
			?><td class="center"><?
				?><select<?
				?> data-stored="<?= $lineup_row['AREAID'] ?>"<?
				?> name="AREAID[<?= $lineupid; ?>]"<?
				?> onchange="needSave(this)"<?
				?> class="areaselect_performance"><?
				for ($i = 0; $i <= $maxareaid; ++$i) {
					?><option<?
					if ($i
					&&	$lineup_row['AREAID'] === $i
					) {
						?> selected="selected"<?
					}
					?> value="<?= $i; ?>"><?
					if ($i) {
						echo $i;
					}
					?></option><?
				}
				?></select><?
			?></td><?
			if ($showtypeselect) {
				?><td><?
				if (isset($lineup_row['OPTS'])) {
					require_once '_artist_types.inc';
					?><select<?
					?> required<?
					?> data-stored="<?= $lineup_row['TYPE'] ?>"<?
					?> onchange="<?
						?>needSave(this);<?
						?>getobj('prfx<?= $lineupid ?>').innerHTML = getobj('pprfx-' + this.value).getAttribute('content');<?
					?>"<?
					?> name="TYPE[<?= $lineupid; ?>]"><?
					foreach ($lineup_row['OPTS'] as $opt) {
						?><option<?
						if ($opt === $lineup_row['TYPE']) {
							?> selected<?
						}
						?> value="<?= $opt; ?>"><?= get_artist_type($opt); ?></option><?
					}
					?></select><?
				} else {
					?><input type="hidden" name="TYPE[<?= $lineupid; ?>]" value="<?= $lineup_row['OPT']; ?>"><?
				}
				?></td><?
			}
			?><td class="center"><?
			echo get_remove_char([
				'onclick'	=> 'removeArtist(this,'.$partyid.','.$lineupid.')'
			]);
			?></td></tr><?
		}
		show_empty_spaces($party,null,$prev_time,$showtypeselect ? 7 : 6,$errors);
		?></table><?
		?><div id="processresult"></div><?
		?><div id="savelink" class="hidden block"><input type="submit" value="<?= __('action:change'); ?>"></div><?
		?></form><?
		layout_close_box();
		?></div><?

		$data = ob_get_flush();

		return true;
	}
	if (!require_element($_POST, 'PROCESS_ACTION', ['replace_all', 'replace_parts', 'add_to_area'])) {
		?></div><?
		return false;
	}

	# processing of single text into processible form

	$lineup_rows = $alter['ROWS'];

	if (!$lineup_rows) {
		?></div><?
		return false;
	}

	$showtypeselect = false;
	$totalopts = [];
	$maxareaid = 0;
	foreach ($lineup_rows as &$lineup_row) {
		if (!isset($lineup_row['CHOICES'])) {
			$unreps = true;
			continue;
		}
		if ($lineup_row['AREAID'] > $maxareaid) {
			$maxareaid = $lineup_row['AREAID'];
		}

		foreach ($lineup_row['CHOICES'] as &$choicelist) {
			if (!is_array($choicelist)) {
				continue;
			}
			foreach ($choicelist as $artistid => &$choice) {
				if ($choice['TYPE']) {
					$opts = null;
					$optcnt = 0;
					foreach (explode(',', $choice['TYPE']) as $type) {
						if (artist_type_ok_for_lineups($type)) {
							$totalopts[$type] = true;
							$opts[$type] = $type;
							++$optcnt;
						}
					}
					if ($optcnt > 1) {
						$type_stats[$choice['ARTISTID']] = sort_artist_types($opts, $choice['ARTISTID'], stamp: $party['STAMP']);
						$choice['OPTS'] = $opts;
					} else {
						$choice['OPT'] = ($optcnt == 1) ? keyval($opts)[0] : '';
					}
				} else {
					$choice['OPT'] = '';
					$totalopts[''] = true;
				}
			}
			unset($choice);
		}
		unset($choicelist);
	}
	unset($lineup_row);

	$showtypeselect = count($totalopts) > 1;

	layout_open_box('party');
	layout_box_header($box_title);
	?><form<?
	?> accept-charset="utf-8"<?
	?> class="needsave"<?
	?> autocomplete="off"<?
	?> method="post"<?
	?> action=""<?
	?> onsubmit="return submitLineupForm(this,<?= $partyid; ?>)"<?
	?>><?
	?><table class="lineup hha alter"><?

	?><tr><?
	?><th class="left"><?= Eelement_name('artist') ?></th><?
	?><th class="center" colspan="2"><?= Eelement_name('start') ?></th><?
	?><th class="center"><?= Eelement_name('stop') ?></th><?
	?><th class="center"><?= Eelement_name('area') ?></th><?
	?><th></th><?
	?><th></th></tr><?
	$areaid = -1;
	$rowid = 0;
	$areas = $alter['AREAS'];
	$maxareaid = max(20,$maxareaid+5);
	require_once '_artist_types.inc';
	$prev_time = [$party['STAMP'],0,true];
	foreach ($lineup_rows as $ndx => $lineup_row) {
		if ($areaid != $lineup_row['AREAID']) {
			if ($areaid != -1) {
				# show empty space at end of previous area:
				show_empty_spaces($party, null, $prev_time, 6, $errors);
			}

			$areaid = $lineup_row['AREAID'];

			$prev_time = [$party['STAMP'], 0, true, $areaid];

			?><tr class="arearow"><th class="left" colspan="6"><?

			static $__existing_areas = null,$__stored_in_area = null;

			if ($__existing_areas === null) {
				$__existing_areas = db_rowuse_hash('partyarea','
					SELECT AREAID,partyarea.*
					FROM partyarea
					WHERE PARTYID='.$partyid
				);
				if ($__existing_areas === false) {
					return false;
				}
				if ($__existing_areas) {
					$__stored_in_area = get_stored_in_area($partyid);
					if ($__stored_in_area === false) {
						return false;
					}
				}
			}
			if ($__existing_areas
			&&	empty($areas[$areaid]['PARTYAREAID'])
			) {
				$use_areaid = 0;
				if (!empty($areas[$areaid]['GENERIC'])) {
					$use_areaid = isset($__existing_areas[$areaid]) ? $areaid : 0;
				}
				if (!$use_areaid) {
					$i = $ndx;
					$artists_in_area = [];
					do {
						$row = $lineup_rows[$i];
						if (!empty($row['CHOICES'])) {
							foreach ($row['CHOICES'] as $choices) {
								if (is_array($choices)) {
									foreach ($choices as $artistid => $artist) {
										$chosenid = $artistid;
										if (isset($artist['BESTFIT'])) {
											break;
										}
									}
								}
								break;
							}
							$artists_in_area[$row['AREAID']][$chosenid] = $chosenid;
						}
						++$i;
					}
					while (isset($lineup_rows[$i]) && $areaid == $lineup_rows[$i]['AREAID']);

					list(,$new_to_old) = get_area_mappings($__stored_in_area,$artists_in_area);

					# get area to copy from based on most probable

					if (isset($new_to_old[$areaid])) {
						$use_areaid = $new_to_old[$areaid];
					}
				}

				if ($use_areaid) {
					foreach ([
						'PARTYAREAID',
						'NAME',
						'HOSTEDBY',
						'HOSTEDBYID',
						'GENRES'
					] as $key) {
						if (!empty($areas[$areaid][$key])) {
							continue;
						}
						$areas[$areaid][$key] = getifset($__existing_areas, $use_areaid, $key);
					}
				}
			}
			show_area_formpart(
				$partyid,
				$areaid,
				getifset($areas,$areaid) ?: null
			);
			?></th></tr><?
		}

		show_empty_spaces($party, $lineup_row, $prev_time, 6, $errors);

		if (!isset($lineup_row['CHOICES'])) {
			$lineup_row['CHOICES'] = array($lineup_row['NAME']);
		}
		foreach ($lineup_row['CHOICES'] as $artists) {
			if (is_scalar($artists)) {
				layout_start_rrow();
				?><span class="error"><?= escape_utf8($artists); ?></span><?
				?></td><?
				# day choice:
				?><td></td><?
				?><td class="center rpad light7"><?

				if ($showtime
				=	!empty($lineup_row['TIME_START_TZI'])
				||	!empty($lineup_row['TIME_END_TZI'])
				) {
					echo str_pad($lineup_row['TIME_START_TZI'],4,0,STR_PAD_LEFT);
				}
				?></td><td class="right rpad light"><?
				if ($showtime) {
					echo str_pad($lineup_row['TIME_END_TZI'],4,0,STR_PAD_LEFT);
				}
				?></td><td class="lpad light"><?
					echo $lineup_row['AREAID'];
				?></td><?
				?><td></td><?
				?><td></td></tr><?
				continue;
			}
			if (preg_match('"https?://(?:vip\.)partyflock\.nl/artist/(\d+:.*?)\."',$lineup_row['SRC'],$match)) {
				$lineup_row['SRC'] = $match[1];
			}
			$allowed_types = [];
			$totalopts = [];
			$disabled = false;
			if (($artistcnt = count($artists)) === 1) {
				[$artistid, $artist] = keyval($artists);
				$selectedartist = $artist;
				$deceased = $artist['DECEASED'];
				$stopped = $artist['STOPPED'];
				layout_start_rrow(0,$deceased || $stopped ? 'light' : null);

				################> single artist possible for line

				$warn = isset($artist['DST']) && $artist['DST'] < 80;
				if ($warn) {
					?><span class="warning"><?
				}

				?><span class="small light7 nowrap<?
				?>"><?

				echo escape_utf8($lineup_row['SRC']);

				?>:</span> <?
				echo str_replace('<a ','<a tabindex="-1" ',get_element_link('artist',$artistid,$artist['NAME']));

				if ($warn) {
					if ($artist['REALNAME']) {
						?> <small class="light7">= <?= escape_utf8($artist['REALNAME']) ?></small><?
					}
					?></span><?
				}

				show_dead($artist);
				show_need_update($artist);
				show_country_flag_for_artist(
					$artist['ARTISTID'],
					$artist['COUNTRYID'],
					$party['COUNTRYID'],
					'light'
				);
				show_freshness_indicators('artist', $artist);
				show_master_genres($artist['ARTIST_GENRES']);
				show_previous_performance_warning($artist, $party);
				$disabled = null;

				if ($deceased || $stopped) {
					?></td><td></td><?
					?><td></td><?
					?><td></td><?
					?><td></td><?
					?><td></td><?
					?></tr><?
					continue;
				}
				?><input<?= $disabled ?> type="hidden" name="ARTISTID[<?= $rowid; ?>]" value="<?= $artistid; ?>"><?
			} else {
				require_once '_artist.inc';
#				generate_name_doubles($artists,'ORDER_NAME','artist',$party['STAMP']);
				sort_lineup_artists($artists,$party['STAMP']);
				layout_start_rrow();

				###############> multiple artists are option for this line

				ob_start();

				?><span class="small light7 nowrap"><?
				echo escape_utf8($lineup_row['SRC']);
				?>:</span><br /><?
				?><select style="max-width:40em" name="ARTISTID[<?= $rowid; ?>]" onchange="
				let type_select = getobj('type<?= $rowid ?>', false);
				if (!type_select) {
					return;
				}
				let allowed_types;
				switch(this.selectedIndex){<?
					?>default: allowed_types = ['dj']; break;<?
				$i = 1;
				foreach ($artists as $artistid => $artist) {
					$allowed_typestr = isset($artist['OPT']) ? "'".$artist['OPT']."'" : stringsimplode_noslash(',',$artist['OPTS'],"'");
					if ($allowed_typestr !== 'dj') {
						$allowed_types[$allowed_typestr][] = $i;
					}
					foreach (isset($artist['OPT']) ? [$artist['OPT']] : $artist['OPTS'] as $allowed_type) {
						if (artist_type_ok_for_lineups($allowed_type)) {
							$totalopts[$allowed_type] = true;
						}
					}
					++$i;
				}
				foreach ($allowed_types as $allowed_typestr => $is) {
					foreach ($is as $i) {
						?>case <?= $i ?>:<?
					}
					?>allowed_types = [<?= $allowed_typestr ?>]; break;<?
				} ?>
				}
				console.log('selectedIndex: ', type_select.selectedIndex);
				console.log('value: ', type_select.value);
				console.log('type.options: ', type.options);
				console.log('allowed_types: ', allowed_types);

				if (allowed_types.length === 1) {
					type_select.value = allowed_types[0];
				} else {
					let repick = false;
					for (const option of type_select.options) {
						let show = false;
						for (const allowed_type of allowed_types.length) {
							if (allowed_type === option.value) {
								show = true;
								break;
							}
						}
						option.disabled = !show;
						if (!show && option.selected) {
							repick = true;
						}
					}
					console.log('repick: ' + (repick ? 'yes' : 'no'));
					if (repick) {
						for (const option of type_select.options) {
							if (!option.disabled) {
								option.selected = true;
								break;
							}
						}
					}
				}
				"><?
				$addempty = false;
				$selectedartist = null;
				?><option value="0"></option><?
				foreach ($artists as $artistid => $artist) {
					show_artist_option($artist, $addempty, $selectedartist, $party['STAMP']);
				}
				if ($addempty) {
					?><option value="0" selected="selected"></option><?
				}
				?></select><?
				$data = ob_get_clean();
				$warn = isset($selectedartist['DST']) && $selectedartist['DST'] < 90;
				if ($warn) {
					?><span class="warning"><?= $data ?></span><?
				} else {
					echo $data;
				}
			}

			$timeNotice = null;//!empty($lineup_row['TIME_ERROR']) ? ' time-error' : null;

			?></td><?
			?><td class="center"><?
				show_day_field($party,"DATE_START[$rowid]",$lineup_row,$disabled);
			?></td><?
			?><td class="center"><?
				show_time_field("TIME_START[$rowid]",$lineup_row['TIME_START_TZI'] ?? null,$timeNotice);
			?></td><?
			?><td class="center"><?
				show_time_field("TIME_END[$rowid]",$lineup_row['TIME_END_TZI'] ?? null,$timeNotice);
			?></td><?
			?><td class="center"><?
				?><select<?= $disabled
				?> name="AREAID[<?= $rowid; ?>]"<?
				?> class="areaselect_performance"><?
				for ($i = 0; $i < $maxareaid; ++$i) {
					?><option<?
					if ($i
					&&	$lineup_row['AREAID'] == $i
					) {
						?> selected="selected"<?
					}
					?> value="<?= $i; ?>"><?
					if ($i) {
						echo $i;
					}
					?></option><?
				}
				?></select><?
			?></td><td><?
			if ($showtypeselect) {
				if ($artistcnt === 1) {
					if (isset($artist['OPTS'])) {
						?><select<?= $disabled
						?> class="type"<?
						?> required<?
						?> name="TYPE[<?= $rowid; ?>]"><?
						$islive = isset($lineup_row['LIVE']);

						if ($islive) {
							$islive = false;

							foreach ([
								'rapper',
								'drummer',
								'percussionist',
								'vocalist',
								'backing_vocalist',
								'band',
								'instrumentalist',
								'violinist',
								'trumpet_player',
								'harmonica_player',
								'dancer',
								'choreographer',
								'show',
								'guitarist',
								'orchestra',
								'act',
								'live_av',
							] as $act) {
								if (in_array($act, $artist['OPTS'])) {
									$islive = $act;
									break;
								}
							}
						}

						foreach ($artist['OPTS'] as $opt) {
							?><option<?
							if (isset($lineup_row['TYPE'])
							&&	$lineup_row['TYPE'] === $opt

							||	$islive === $opt

							||	isset($lineup_row['HOST'])
							&&	$opt === 'mc'
							) {
								?> selected<?
							}
							?> value="<?= $opt; ?>"><?= get_artist_type($opt); ?></option><?
						}
						?></select><?
					} else {
						?><input<? echo $disabled;
						?> required<?
						?> type="hidden"<?
						?> name="TYPE[<?= $rowid; ?>]" value="<?= $artist['OPT']; ?>"><?
						if (empty($artist['OPT'])) {
							?><span class="lmrgn error"><?= element_name('function') ?>?</span><?
						} else {
							?><span class="lmrgn light"><?= get_artist_type($artist['OPT']); ?></span><?
						}
					}
				} else {
					$okcnt = 0;
					foreach ($totalopts as $opt => &$enabled) {
						$enabled =
							isset($selectedartist['OPT'])
						?	($selectedartist['OPT'] === $opt)
						:	(	isset($selectedartist['OPTS'])
							?	in_array($opt, $selectedartist['OPTS'])
							:	false
							);

						if ($enabled) {
							++$okcnt;
						}
					}
					unset($enabled);

					# NOTE For now, sort on types of selected artist. If an other artist is selected, the order might change.
					#	   This is not handled currently at all. Also, show percentages in front of type and change if artist changes.

					if (!empty($selectedartist)
					&&	($use_stats = $type_stats[$selectedartist['ARTISTID']] ?? null)
					) {
						# sort high => low
						uksort($totalopts, function (string $type_a, string $type_b) use ($use_stats): int  {
							return ($use_stats[$type_b] ?? 0) - ($use_stats[$type_a] ?? 0);
						});
					}
					?><select<? echo $disabled;
					?> required<?
					?> id="type<?= $rowid ?>"<?
					?> class="type"<?
					?> name="TYPE[<?= $rowid ?>]"><?

					$islive = isset($lineup_row['LIVE']);

					foreach ($totalopts as $opt => $enabled) {
						?><option<?
						if ($enabled) {
							if (isset($lineup_row['TYPE'])
							&&	$lineup_row['TYPE'] === $opt

							||	$islive === $opt

							||	isset($lineup_row['HOST'])
							&&	$opt === 'mc'
							) {
								?> selected<?
							}
						} else {
							?> disabled<?
						}
						?> value="<?= $opt; ?>"><?= $opt ? get_artist_type($opt) : '';
						?></option><?
					}
					?></select><?
				}
			} else {
				?><input<?= $disabled ?> type="hidden" name="TYPE[<?= $rowid; ?>]" value="<?= $artist['OPT']; ?>"><?
				if ($artist['OPT']) {
					?><span class="lmrgn light"><?= get_artist_type($artist['OPT']); ?></span><?
				} else {
					?><span class="lmrgn error">???</span><?
				}
			}
			?></td><td class="center"><?
			echo get_remove_char(array('onclick' => 'removeArtist(this,'.$party['PARTYID'].',false)'));
			?></td></tr><?
			++$rowid;
		}
	}
	?></table><?
	?><input type="hidden" name="PROCESS_ACTION" value="<?= $_POST['PROCESS_ACTION']; ?>"><?
	?><div id="processresult"></div><?
	?><div id="savelink" class="block"><?
	?><input type="submit" value="<?=
		db_single('lineup','SELECT 1 FROM lineup WHERE PARTYID='.$partyid.' LIMIT 1',DB_USE_MASTER)
	?	(	$_POST['PROCESS_ACTION'] == 'add_to_area'
		?	__('action:add')
		:	(	$_POST['PROCESS_ACTION'] == 'replace_parts'
			?	__('action:replace_areas')
			:	__('action:replace_all')
			)
		)
	:	__('action:add')
	?>"></div><?
	?></form><?
	layout_close_box();
	if (isset($unreps)) {
		echo "\x1F";
		?><table class="regular hha"><?
		?><caption>niet vervangen</caption><?
		?>><tr><?
		?><th class="left"><?= Eelement_name('name') ?></th><?
		?><th class="hpad"><?= __C('field:start') ?></th><?
		?><th class="hpad"><?= __C('field:stop') ?></th><?
		?><th class="hpad"><?= Eelement_name('area') ?></th><?
		?><th></th><?
		?></tr><?
		foreach ($lineup_rows as $lineup_row) {
			if (isset($lineup_row['CHOICES'])) {
 				continue;
			}
			fill_lineup($lineup_row);
			?><tr onclick="fillSingleForm(this);"><td><?
			?><span class="error"><?= escape_utf8($lineup_row['NAME']); ?></span><?
			if (!empty($lineup_row['TIME_START_TZI'])
			||	!empty($lineup_row['TIME_END_TZI'])
			) {
				?></td><td class="center"><?
				echo str_pad($lineup_row['TIME_START_TZI'],4,'0',STR_PAD_LEFT);
				?></td><td class="center"><?
				echo str_pad($lineup_row['TIME_END_TZI'],4,'0',STR_PAD_LEFT);
			} else {
				?></td><td colspan="2"><?
			}
			?></td><td class="center hpad"><?
			echo $lineup_row['AREAID'];
			?></td><td><?
			if (isset($lineup_row['LIVE'])) {
				?>live<?
			}
			if (isset($lineup_row['HOST'])) {
				?>mc<?
			}
			?></td></tr><?
		}
		?></table><?
	}
	?></div><?
}

function lineup_remove_area($partyid) {
	require_once '_pagechanged.inc';
	if (!($partyareaid = require_idnumber($_POST,'PARTYAREAID'))) {
		return false;
	}
	$artistids = db_simpler_array('lineup','
		SELECT DISTINCT ARTISTID
		FROM lineup
		JOIN partyarea USING (PARTYID,AREAID)
		WHERE PARTYAREAID='.$partyareaid
	);
	if ($artistids === false) {
		return false;
	}
	if ($artistids) {
		if (!db_insert('lineup_log','
			INSERT INTO lineup_log
			SELECT lineup.*,'.CURRENTSTAMP.','.CURRENTUSERID.' FROM lineup
			JOIN partyarea USING (PARTYID,AREAID)
			WHERE PARTYAREAID='.$partyareaid)
		||	!db_delete('lineup','
			DELETE lineup
			FROM lineup
			JOIN partyarea USING (PARTYID,AREAID)
			WHERE PARTYAREAID ='.$partyareaid)
		) {
			return false;
		}
		flush_party($partyid);
		page_changed('artist',$artistids);
		partylist_needs_refresh('artist',$artistids);
		partylist_needs_refresh_if_in_future($partyid);
	}
	require_once '_profile.inc';
	clear_element_profile('party',$partyid);
	page_changed('party',$partyid);
	clear_determined_genres($partyid);
	require_once '_processlist.inc';
	set_proclist_done('party', $partyid);

	if (!db_insert('partyarea_log','
		INSERT INTO partyarea_log
		SELECT *,'.CURRENTSTAMP.','.CURRENTUSERID.'
		FROM partyarea
		WHERE PARTYAREAID='.$partyareaid)
	||	!db_delete('partyarea','
		DELETE FROM partyarea
		WHERE PARTYAREAID='.$partyareaid)
	) {
		return false;
	}
	detect_almost_complete_timetable($partyid);
	return true;
}
function lineup_remove_artist($partyid) {
	if (false === require_number($_POST,'LINEUPID')) {
		return false;
	}
	if (!($artistid = db_single('lineup','SELECT ARTISTID FROM lineup WHERE LINEUPID='.$_POST['LINEUPID'],DB_USE_MASTER))) {
		if ($artistid !== false) {
			_error('Geen artiest behorende bij lineup met ID '.$_POST['LINEUPID'].' gevonden!');
		}
		return false;
	}
	if (!db_insert('lineup_log','
		INSERT INTO lineup_log
		SELECT *,'.CURRENTSTAMP.','.CURRENTUSERID.' FROM lineup
		WHERE LINEUPID='.$_POST['LINEUPID'])
	||	!db_delete('lineup','
		DELETE FROM lineup
		WHERE LINEUPID='.$_POST['LINEUPID'])
	) {
		return false;
	}
	if (!db_affected()) {
		register_warning('artist:warning:already_removed_LINE');
		return false;
	}
	flush_party($partyid);
	clear_determined_genres($partyid);
	require_once '_profile.inc';
	clear_element_profile('party',$partyid);
	require_once '_pagechanged.inc';
	page_changed('party',$partyid,'artist',$artistid);
	require_once '_processlist.inc';
	set_proclist_done('party', $partyid);
	partylist_needs_refresh('artist',$artistid);
	partylist_needs_refresh_if_in_future($partyid);
	detect_almost_complete_timetable($partyid);
	return true;
}

function lineup_process($party, &$areamove = null) {
	$size = null;
	if (!require_number_array_of_size($_POST,'ARTISTID',$size)
	||	!require_number_array_of_size($_POST,'AREAID',$size)
	||	!require_lineup_time_array_of_size($_POST,'TIME_START',$size)
	||	!require_lineup_time_array_of_size($_POST,'TIME_END',$size)
	||	!require_array_of_size($_POST,'TYPE',$size)
	||	isset($_POST['DATE_START'])
	&&	!require_array_of_size($_POST,'DATE_START',$size)
	) {
		return false;
	}

	$partyid = $party['PARTYID'];

	$party = memcached_party_and_stamp($partyid);
	if (!$party) {
		return false;
	}

	$oldlineup = db_rowuse_hash(['lineup', 'artist'], '
		SELECT LINEUPID, ARTISTID, AREAID, lineup.TYPE, START_STAMP, DURATION, LIVESTREAM_ADDRESS
		FROM lineup
		JOIN artist USING (ARTISTID)
		WHERE PARTYID = '.$partyid
	);
	if ($oldlineup === false) {
		return false;
	}

	$old_areas = db_rowuse_hash('partyarea','
		SELECT PARTYAREAID, partyarea.*
		FROM partyarea
		WHERE PARTYID = '.$partyid
	);
	if ($old_areas === false) {
		return false;
	}
	$artistpage_update = [];

	foreach ($_POST['ARTISTID'] as $ndx => $artistid) {
		if (!$artistid) {
			continue;
		}
		if ($oldlineup
		&&	!isset($_POST['PROCESS_ACTION'])
		&&	!isset($oldlineup[$ndx])
		) {
			_error('Line-up regel met ID '.$ndx.' niet gevonden in opgeslagen line-up!');
			return false;
		}
		if (!isset($_POST['AREAID'][$ndx])
		||	!isset($_POST['TYPE'][$ndx])
		) {
			_error('Formulier is niet compleet!');
			return false;
		}
		if (!require_lineup_time($_POST['TIME_START'],$ndx)
		||	!require_lineup_time($_POST['TIME_END'],$ndx)
		) {
			return false;
		}

		if (isset($_POST['DATE_START'])
		&&	!empty($_POST['DATE_START'][$ndx])
		&&	!require_datestr($_POST['DATE_START'],$ndx)
		) {
			return false;
		}
	}


	if (!isset($_POST['PROCESS_ACTION'])) {
		# this is called when changing line-up in-place, so not processing, but adding times or changing areas

		if ($size != ($oldsize = count($oldlineup))) {
			_error('Opgeslagen line-up heeft andere grootte ('.$oldsize.') dan degene die je aanpast ('.$size.')!');
			return false;
		}
		$areamove = false;
		$artists_in_area = [];
		$last_performance_end_utc = 0;

		foreach ($_POST['ARTISTID'] as $lineupid => $artistid) {
			$artistid 	= $_POST['ARTISTID'][$lineupid];
			if (!$artistid) {
				# probably select box with no chosen artist
				continue;
			}
			$areaid		= false !== have_number($_POST['AREAID'],$lineupid) ? $_POST['AREAID'][$lineupid] : 1;
			$date_start	= $_POST['DATE_START'][$lineupid] ?? null;
			$time_start	= is_valid_lineup_time($_POST['TIME_START'][$lineupid]);
			$time_end	= is_valid_lineup_time($_POST['TIME_END'][$lineupid]);
			$type 		= $_POST['TYPE'][$lineupid];
			$livestream_address
					= $_POST['LIVESTREAM_ADDRESS'][$lineupid] ?? '';

			if ((	!$type
				||	!artist_type_ok_for_lineups($type)
				)
			&&	$party['ACCEPTED']
			&&	!memcached_add('missing_or_wrong_type:'.$type.':'.$lineupid.':'.$artistid, true, FIVEMINS)
			) {
				$missings[] =
					'type: '.$type.', lineupid: '.$lineupid.', artist: '.$artistid."\n".
					'https://vip.partyflock.nl/artist/'.$artistid.' '.get_element_title('artist', $artistid);
			}

			[	$start_stamp,
				$start_stamp_utc,
				$duration,
				$dst,
				$end_dst
			] =	get_start_and_duration($party, $time_start, $time_end, $date_start);

			$lineup_rows[$lineupid] = compact(
				'artistid',
				'areaid',
				'type',
				'date_start',
				'time_start',
				'time_end',
				'start_stamp',
				'start_stamp_utc',
				'duration',
				'dst',
				'end_dst',
				'livestream_address'
			);

			$last_performance_end_utc = max(
				$last_performance_end_utc,
				$start_stamp_utc + $duration
			);
		}

		if (!empty($missings)) {
			?>Artiest(en) zonder type-aanduiding!<? echo "\n";
			print_r($missings);
			return false;

			mail_log(
				implode("\n\n", $missings),
				subject: "missing artist type in line-up",
				item: ['_POST'	=> $_POST],
				include_trace: true,
			);
		}

		$switch = get_dst_switch_times($party['TIMEZONE'] ?: 'UTC', $party['STAMP'], $party['STAMP'] + $party['DURATION_SECS'] + 1);
		if ($switch) {
			[$switch_stamp, $switch_offset] = $switch;
			if ($switch_offset > 0) {
				# clock moves forward
				if ($last_performance_end_utc == $party['STAMP_TZI'] + $party['DURATION_SECS'] + $switch_offset) {
					$is_utc = false;
					foreach ($lineup_rows as $lineupid => $lineup) {
						if ($lineup['start_stamp'] == $switch_stamp) {
							$is_utc = true;
							break;
						}
					}
					if (!$is_utc) {
						error_log('correcting dst switch');
						# event ends one hour before last performance, the submitted timetable honors dst and we should fix any time after
						# switch by subtracting the offset
						foreach ($lineup_rows as $lineupid => &$lineup) {
							if ($lineup['start_stamp'] >= $switch_stamp) {
								$lineup['start_stamp']	 -= $switch_offset;
								$lineup['start_stamp_utc'] -= $switch_offset;
							}
							if ($lineup['start_stamp'] < $switch_stamp
							&&	$lineup['start_stamp'] + $lineup['duration'] >= $switch_stamp
							) {
								$lineup['duration'] -= $switch_offset;
							}
						}
						unset($lineup);
					}
				}
			} else {
				# clock moved backwards

			}
		}

/*		print_rr($last_performance_end_utc,'last_performance_end_utc');
		print_rr($party['STAMP_TZI'] + $party['DURATION_SECS'],'event_end');
		change_timezone('UTC');
		print_rr(_datetime_get($party['STAMP_TZI']),'tzi_start');
		print_rr(_datetime_get($party['STAMP_TZI'] + $party['DURATION_SECS']),'tzi_end');
		print_rr(_datetime_get($last_performance_end_utc),'perf_end');
		change_timezone();
#		exit;*/

		foreach ($lineup_rows as $lineupid => $lineup) {
			extract($lineup);

			$old = $oldlineup[$lineupid];
			if ($thisareachange =
				$old['AREAID'] != $_POST['AREAID'][$lineupid]
			) {
				$areamove = true;
			}
			if ($thisareachange
			||	$old['ARTISTID']		!= $artistid
			||	$old['TYPE']			!= $type
			||	$old['START_STAMP']		!== $start_stamp
			||	$old['DURATION']		!== $duration
			||	$old['LIVESTREAM_ADDRESS']	!= $livestream_address
			) {
				$replst[$lineupid] =
					'('.$partyid.
					','.$lineupid.
					','.$artistid.
					','.$areaid.
					','.($start_stamp	=== null ? 'NULL' : $start_stamp).
					','.($start_stamp_utc	=== null ? 'NULL' : $start_stamp_utc).
					','.($dst		=== null ? 'NULL' : "b'".($dst ? 1 : 0)."'").
					','.($end_dst		=== null ? 'NULL' : "b'".($end_dst ? 1 : 0)."'").
					','.($duration		=== null ? 'NULL' : $duration).
					',"'.addslashes($type).'"'.
					','.CURRENTSTAMP.
					','.CURRENTUSERID.
					',"'.addslashes($livestream_address).'"'.
					')';

				$artists_in_area[$areaid][$artistid] = $artistid;

				$duo_checks[] = [
					$partyid,
					$artistid,
					$type,
					$areaid,
					$start_stamp,
					$start_stamp_utc,
					$dst,
					$end_dst,
					$duration,
					$time_start,
					$time_end
				];

				if ($old['ARTISTID'] != $_POST['ARTISTID'][$lineupid]) {
					$artistpage_upds[] = $_POST['ARTISTID'][$lineupid];
					$artistpage_upds[] = $old['ARTISTID'];
				}
			}
		}

		$storearea = parse_area_names(
			$partyid,
			$old_areas,
			$artists_in_area,
			$changed_partyareaids
		);
		if ($storearea === false) {
			return false;
		}
		check_storearea($storearea);

		if (!$storearea
		&&	empty($replst)
		) {
			_error('Er hoeft niets aangepast te worden!');
			return null;
		}
		if (!empty($replst)) {
			if (!db_replace('lineup_log','
				INSERT INTO lineup_log
				SELECT *, '.CURRENTSTAMP.', '.CURRENTUSERID.' FROM lineup
				WHERE LINEUPID IN ('.implodekeys(', ', $replst).')
				  AND PARTYID = '.$partyid)
			||	!db_replace('lineup','
				REPLACE INTO lineup (PARTYID, LINEUPID, ARTISTID, AREAID, START_STAMP, START_STAMP_UTC, DST, END_DST, DURATION, TYPE, MSTAMP, MUSERID, LIVESTREAM_ADDRESS)
				VALUES '.implode(', ', $replst))
			) {
				return false;
			}
			register_notice('lineup:notice:changed_LINE');
		}
	} else {
		$used_lineupids = [];
		foreach ($_POST['ARTISTID'] as $rowid => $artistid) {
			$artistid 	= $_POST['ARTISTID'][$rowid];
			if (!$artistid) {
				# probably select box with no chosen artist
				continue;
			}
			$areaid		= false !== have_number($_POST['AREAID'],$rowid) ? $_POST['AREAID'][$rowid] : 1;
			$date_start	= $_POST['DATE_START'][$rowid] ?? null;
			$time_start	= is_valid_lineup_time($_POST['TIME_START'][$rowid]);
			$time_end	= is_valid_lineup_time($_POST['TIME_END'][$rowid]);
			$type 		= $_POST['TYPE'][$rowid];

			if ((	!$type
				||	!artist_type_ok_for_lineups($type)
				)
			&&	$party['ACCEPTED']
			&&	!memcached_add('missing_or_wrong_type:'.$type.':'.$rowid.':'.$artistid, true, FIVEMINS)
			) {
				$missings[] =
					'type: '.$type.', rowid: '.$rowid.', artist: '.$artistid."\n".
					'https://vip.partyflock.nl/artist/'.$artistid.' '.get_element_title('artist', $artistid);
			}

			[	$start_stamp,
				$start_stamp_utc,
				$duration,
				$dst,
				$end_dst
			] =	get_start_and_duration($party, $time_start, $time_end, $date_start);

			# get best match from current line-up, otherwise get best match from log
			foreach (['lineup','lineup_log'] as $table) {
				$lineupid = get_lineupid(
					$table,
					$party['PARTYID'],
					$artistid,
					$areaid,
					$type,
					$start_stamp,
					$duration,
					$used_lineupids
				);
				if ($lineupid === false) {
					return false;
				}
				if ($lineupid) {
					break;
				}
			}

			$replst[] =
				'('.$partyid.
				','.($lineupid ?: 0).
				','.$artistid.
				','.$areaid.
				','.($start_stamp	=== null ? 'NULL' : $start_stamp).
				','.($start_stamp_utc	=== null ? 'NULL' : $start_stamp_utc).
				','.($dst		=== null ? 'NULL' : "b'".($dst ? 1 : 0)."'").
				','.($end_dst		=== null ? 'NULL' : "b'".($end_dst ? 1 : 0)."'").
				','.($duration		=== null ? 'NULL' : $duration).
				',"'.addslashes($type).'"'.
				','.CURRENTSTAMP.
				','.CURRENTUSERID.
				',""'.
				')';

			$artists_in_area[$areaid][$artistid] = $artistid;

			$duo_checks[] = [
				$partyid,
				$artistid,
				$type,
				$areaid,
				$start_stamp,
				$dst,
				$end_dst,
				$duration,
				$time_start,
				$time_end
			];

			$artistpage_upds[] = $_POST['ARTISTID'][$rowid];

			$update_areaid[$areaid] = true;

			if ($lineupid
			&&	!isset($used_lineupids[$lineupid])
			) {
				$used_lineupids[$lineupid] = $lineupid;
			}

			if ($rowid
			&&	isset($oldlineup[$rowid])
			) {
				$old = $oldlineup[$rowid];
				if ($old['AREAID'] != $_POST['AREAID'][$rowid]) {
					$areamove = true;
				}
			}
		}

		if (!empty($missings)) {
			mail_log(
				implode("\n\n", $missings),
				subject: "missing artist type in line-up",
				item: ['_POST'	=> $_POST],
				include_trace: true,
			);
		}

		if (!isset($update_areaid)) {
			return false;
		}

		if ($oldlineup
		&&	$_POST['PROCESS_ACTION'] !== 'add_to_area'
		) {
			$update_areaidstr = implodekeys(',', $update_areaid);

			$move_lineup = false;

			if ($artists_in_area) {
				$stored_in_area = get_stored_in_area($partyid);
				if ($stored_in_area === false) {
					return false;
				}
				if ($stored_in_area) {
					# find most resembling area

					[$old_to_new, $new_to_old] = get_area_mappings($stored_in_area, $artists_in_area);

					if ($_POST['PROCESS_ACTION'] == 'replace_parts') {
						$remaining_areas = $stored_in_area;
						foreach ($old_to_new as $old_areaid => $new_areaid) {
							unset($remaining_areas[$old_areaid]);
						}

						# try to keep remaining areas at same position if possible.

						# first fill all remaining that can be mapped to same area
						foreach ($remaining_areas as $old_areaid => $null) {
							if (!isset($new_to_old[$old_areaid])) {
								$old_to_new[$old_areaid] = $old_areaid;
								$new_to_old[$old_areaid] = $old_areaid;
								unset($remaining_areas[$old_areaid]);
							}
						}

						# now fill any holes
						$first = isset($new_to_old[0]) || isset($stored_in_area[0])  ? 0 : 1;

						foreach ($remaining_areas as $old_areaid => $null) {
							for ($i = $first; ; ++$i) {
								if (!isset($new_to_old[$i])) {
									$old_to_new[$old_areaid] = $i;
									$new_to_old[$i] = $old_areaid;
									break;
								}
							}
						}
					}
					foreach ($old_to_new as $old_areaid => $new_areaid) {
						if ($new_areaid != $old_areaid) {
							$move_lineup = $old_to_new;
							break;
						}
					}
				}
			}

			if ($move_lineup) {
				if (!db_lock_tables(['lineup','lineup_log','partyarea','partyarea_log'])
				||	!db_create('lineup_tmp','
					CREATE TEMPORARY TABLE lineup_tmp LIKE lineup')
				||	!db_create('partyarea_tmp','
					CREATE TEMPORARY TABLE partyarea_tmp LIKE partyarea')
				) {
					return false;
				}
				foreach ($move_lineup as $old_areaid => $new_areaid) {
					if ($old_areaid == $new_areaid) {
						continue;
					}
					if (# overwrite changed areas in tmp table
						!db_insert('partyarea_tmp','
						REPLACE INTO partyarea_tmp (PARTYAREAID, PARTYID, AREAID, NAME, NAME_FOR_APPIC, MSTAMP, MUSERID, HOSTEDBY, GENRES, HOSTEDBYID)
						SELECT PARTYAREAID, PARTYID, '.$new_areaid.', NAME, NAME_FOR_APPIC, '.CURRENTSTAMP.', '.CURRENTUSERID.', HOSTEDBY, GENRES, HOSTEDBYID
						FROM partyarea
						WHERE PARTYID = '.$partyid.'
						  AND AREAID = '.$old_areaid)
					) {
						return false;
					}
					if (!db_insert('lineup_tmp','
						INSERT INTO lineup_tmp (PARTYID, ARTISTID, AREAID, MSTAMP, MUSERID, TYPE, LINEUPID, DST, END_DST, START_STAMP, START_STAMP_UTC, DURATION)
						SELECT PARTYID, ARTISTID, '.$new_areaid.', '.CURRENTSTAMP.', '.CURRENTUSERID.', TYPE, LINEUPID, DST, END_DST, START_STAMP, START_STAMP_UTC, DURATION
						FROM lineup
						WHERE PARTYID='.$partyid.'
						  AND AREAID='.$old_areaid)
					) {
						return false;
					}
				}
				foreach ($_POST['ORDERID'] as $partyareaid => $orderid) {
					if (!isset($move_lineup[$orderid])
					||	$move_lineup[$orderid] == $orderid
					) {
						# no change for this area
						continue;
					}
					$new_partyareaid = db_single('partyarea','
						SELECT PARTYAREAID
						FROM partyarea_tmp
						WHERE PARTYID='.$partyid.'
						  AND AREAID='.$orderid
					);
					if (!$new_partyareaid) {
						if ($new_partyareaid === false) {
							return false;
						}
						_error('new_partyareaid (select from tmp partyid '.$partyid.' and area '.$areaid.' empty)');
						return false;
					}
					foreach ([
						'AREANAME',
						'AREANAME_FOR_APPIC',
						'HOSTEDBY',
						'HOSTEDBYID',
						'ORDERID',
					] as $field) {
						if (!isset($_POST[$field][$partyareaid])) {
							_error('_POST for '.$field.' with index '.$partyareaid.' is empty');
							return false;
						}
						$newpost[$field][$new_partyareaid] = $_POST[$field][$partyareaid];
						unset($_POST[$field][$partyareaid]);
					}
				}
				if (!empty($newpost)) {
					foreach ($newpost as $field => $info) {
						foreach ($info as $partyareaid => $data) {
							$_POST[$field][$partyareaid] = $data;
						}
					}
				}

				$swapped_areas = db_rowuse_hash('partyarea_tmp','
					SELECT PARTYAREAID,partyarea_tmp.*
					FROM partyarea_tmp
					WHERE PARTYID='.$partyid
				);
				if ($swapped_areas === false) {
					return false;
				}
				foreach ($swapped_areas as $partyareaid => $data) {
					$old_areas[$partyareaid] = $data;
				}

				$storearea = parse_area_names($partyid,$old_areas,$artists_in_area,$changed_partyareaids);
				if ($storearea === false) {
					return false;
				}
				check_storearea($storearea);

				if (!db_delete('lineup_tmp','
					DELETE FROM lineup_tmp
					WHERE AREAID IN ('.$update_areaidstr.')
					  AND PARTYID='.$partyid)
				||	!db_insert('lineup_tmp','
					INSERT INTO lineup_tmp (PARTYID, LINEUPID, ARTISTID, AREAID, START_STAMP, START_STAMP_UTC, DST, END_DST, DURATION, TYPE, MSTAMP, MUSERID, LIVESTREAM_ADDRESS)
					VALUES '.implode(', ', $replst))
				) {
					return false;
				}
				if ($storearea) {
					if (!db_replace('partyarea_tmp','
						REPLACE INTO partyarea_tmp (PARTYAREAID, PARTYID, AREAID, MUSERID, MSTAMP, NAME, NAME_FOR_APPIC, HOSTEDBY, HOSTEDBYID, GENRES)
						VALUES '.implode(', ', $storearea))
					) {
						return false;
					}
					unset($storearea);
				}

				if ($_POST['PROCESS_ACTION'] == 'replace_all') {
					# backup all lineup entries an partyarea entries, then remove them all

					if (!db_insert('lineup_log','
						INSERT INTO lineup_log
						SELECT DISTINCT lineup.*,'.CURRENTSTAMP.','.CURRENTUSERID.'
						FROM lineup
						WHERE PARTYID='.$partyid)
					||	!db_delete('lineup','
						DELETE FROM lineup
						WHERE PARTYID='.$partyid)

					||	!db_insert('partyarea_log','
						INSERT INTO partyarea_log
						SELECT DISTINCT partyarea.*,'.CURRENTSTAMP.','.CURRENTUSERID.'
						FROM partyarea
						WHERE PARTYID='.$partyid)
					||	!db_delete('partyarea','
						DELETE FROM partyarea
						WHERE PARTYID='.$partyid)

					) {
						return false;
					}
				} else { // PROCESS_ACTION == replace_parts

					# log and then delete, performances found in lineup_tmp
					# then insert lineup_tmp into lineup

					if (!db_insert('lineup_log','
						INSERT INTO lineup_log
						SELECT DISTINCT lineup.*, '.CURRENTSTAMP.', '.CURRENTUSERID.'
						FROM lineup
						JOIN lineup_tmp USING (PARTYID, AREAID)
						WHERE PARTYID = '.$partyid)
					||	!db_delete('lineup','
						DELETE lineup
						FROM lineup
						JOIN lineup_tmp USING (PARTYID,AREAID)
						WHERE PARTYID = '.$partyid)

					||	!db_insert('partyarea_log','
						INSERT INTO partyarea_log
						SELECT DISTINCT partyarea.*, '.CURRENTSTAMP.', '.CURRENTUSERID.'
						FROM partyarea
						JOIN partyarea_tmp USING (PARTYID, AREAID)
						WHERE PARTYID = '.$partyid)
					||	!db_delete('partyarea','
						DELETE partyarea
						FROM partyarea
						JOIN partyarea_tmp USING (PARTYID, AREAID)
						WHERE PARTYID = '.$partyid)
					) {
						return false;
					}
				}
				if(	!db_insert('lineup','
					INSERT INTO lineup
					SELECT *
					FROM lineup_tmp')
				||	!db_insert('partyarea','
					INSERT INTO partyarea
					SELECT *
					FROM partyarea_tmp')
				) {
					return false;
				}

				if (!db_unlock_tables()) {
					return false;
				}

				$areamove = true;
			} else {
				# straightforward inserting supplied areas
				# no area change
				# replace all or replace only supplied areas

				$storearea = parse_area_names($partyid,$old_areas,$artists_in_area,$changed_partyareaids);
				if ($storearea === false) {
					return false;
				}
				check_storearea($storearea);

				if ($_POST['PROCESS_ACTION'] == 'replace_parts') {
					# replace_parts

					if (!db_insert('lineup_log','
						INSERT INTO lineup_log
						SELECT *,'.CURRENTSTAMP.','.CURRENTUSERID.'
						FROM lineup
						WHERE AREAID IN ('.$update_areaidstr.')
						  AND PARTYID='.$partyid)
					||	!db_delete('lineup','
						DELETE FROM lineup
						WHERE AREAID IN ('.$update_areaidstr.')
						  AND PARTYID='.$partyid)
					) {
						return false;
					}
				} else {
					# replace all
					if (!db_insert('partyarea_log','
						INSERT INTO partyarea_log
						SELECT *, '.CURRENTSTAMP.', '.CURRENTUSERID.'
						FROM partyarea
						WHERE PARTYID = '.$partyid)
					||	!db_delete('partyarea','
						DELETE FROM partyarea
						WHERE PARTYID = '.$partyid)
					||	!db_insert('lineup_log','
						INSERT INTO lineup_log
						SELECT *, '.CURRENTSTAMP.', '.CURRENTUSERID.'
						FROM lineup
						WHERE PARTYID = '.$partyid)
					||	!db_delete('lineup','
						DELETE FROM lineup
						WHERE PARTYID = '.$partyid)
					) {
						return false;
					}
				}
				# replst might containt existing LINEUPID.
				# shouldn't replace but just 'ignore' prolly
				if (!db_insert('lineup','
					REPLACE INTO lineup (PARTYID, LINEUPID, ARTISTID, AREAID, START_STAMP, START_STAMP_UTC, DST, END_DST, DURATION, TYPE, MSTAMP, MUSERID, LIVESTREAM_ADDRESS)
					VALUES '.implode(',',$replst))
				) {
					return false;
				}
			}
		} else {
			# no existing line-up
			# or adding to existing area

			$storearea = parse_area_names($partyid,$old_areas,$artists_in_area,$changed_partyareaids);
			if ($storearea === false) {
				return false;
			}
			check_storearea($storearea);

			if (!db_insert('lineup','
				INSERT IGNORE INTO lineup (PARTYID, LINEUPID, ARTISTID, AREAID, START_STAMP, START_STAMP_UTC, DST, END_DST, DURATION, TYPE, MSTAMP, MUSERID, LIVESTREAM_ADDRESS)
				VALUES '.implode(',',$replst))
			) {
				return false;
			}
		}

		register_notice($oldlineup ? ($_POST['PROCESS_ACTION'] == 'replace_parts' ? 'lineup:notice:part_changed_LINE' : 'lineup:notice:replaced_LINE') : 'lineup:notice:added_LINE');
	}

	if (!empty($storearea)) {
		if (!empty($changed_partyareaids)) {
			if (!db_insert('partyarea_log','
				INSERT INTO partyarea_log
				SELECT *, '.CURRENTSTAMP.', '.CURRENTUSERID.'
				FROM partyarea
				WHERE PARTYAREAID IN ('.implode(',',$changed_partyareaids).')
				  AND PARTYID = '.$partyid)
			) {
				return false;
			}
		}
		if (!db_replace('partyarea','
			REPLACE INTO partyarea (PARTYAREAID, PARTYID, AREAID, MUSERID, MSTAMP, NAME, NAME_FOR_APPIC, HOSTEDBY, HOSTEDBYID, GENRES)
			VALUES '.implode(', ', $storearea))
		) {
			return false;
		}
		register_notice('partyarea:notice:changed_LINE');
		$areamove = true;
	}

	if (!empty($duo_checks)) {
		foreach ($duo_checks as $info) {
			if (false === ($updated = check_duos($info))) {
				return false;
			}
			if ($updated) {
				$artistpage_update = array_merge($artistpage_update,$updated);
				$areamove = true;
			}
		}
	}
	flush_party($partyid);
	clear_determined_genres($partyid);
	require_once '_profile.inc';
	clear_element_profile('party', $partyid);
	require_once '_pagechanged.inc';
	page_changed('party', $partyid,'artist',isset($artistpage_upds) ? $artistpage_upds : 0);
	require_once '_processlist.inc';
	set_proclist_done('party', $partyid);
	if (!empty($artistpage_upds)) {
		partylist_needs_refresh('artist', $artistpage_upds);
	}
	partylist_needs_refresh_if_in_future($partyid);
	detect_almost_complete_timetable($partyid);
	$_REQUEST['NOMEMCACHE'] = true;
	return true;
}

function lineup_show_add_form(array $party): void {
	$partyid = $party['PARTYID'];

	require_once '__translation.php';
	include_js('js/form/lineup');

	layout_open_box('party');
	?><form<?
	?> accept-charset="utf-8"<?
	?> autocomplete="off"<?
	?> id="lineup-single-form"<?
	?> onsubmit="return submitSingleForm(this,<?= $partyid; ?>)"<?
	?>><?
	?><input type="hidden" name="TYPE"><?
		show_input([
			'id'		=> 'artistname',
			'type'		=> 'search',
			'style'		=> 'max-width:20em',
			'autosave'	=> 'artistname',
			'name'		=> 'NAME',
			'required'	=> true,
			'placeholder'	=> __('lineup:info:name_of_artist_to_add'),
			'onkeydown'	=> 'Pf.catchLineupTab(event,this)',
		]);
		?> <?
		show_day_field($partyid,'DATE_START',null);
		?> <?
		show_time_field('TIME_START',null,null,'start');
		?> &ndash; <?
		show_time_field('TIME_END',null,null,'stop');
		?> <?
		show_input([
			'type'		=> 'number',
			'min'		=> 0,
			'max'		=> 100,
			'name'		=> 'AREAID',
			'class'		=> 'three_digits right',
			'placeholder'	=> element_name('area').' #',
		]);
		?> <?
		?><input type="submit" value="&#65291;"><?
		?><div class="hidden" id="artist-options-part"><span id="artist-options"></span></div><?
		?><div id="lineup-error"></div><?

	?></form><?

	layout_close_box();
}

function lineup_add_artist($party): int {
	if ($name = $_POST['NAME'] ?? null) {
#		$name = utf8_mytrim($_POST['NAME']);
#		if (empty($name)) {
#			_error('Je moet wel een naam opgeven!');
#			return 400;
#		}
		if (!($choices = get_artist_choices($party, $name, $size))) {
			if (false === $choices) {
				return 500;
			}
			register_error('lineup:notice:no_artist_with_given_name_LINE');
			header('X-OPTIONS: none');
			return 206;
		}
		if (!($showselect = $size !== 1)) {
			[$artistid, $choice] = keyval($choices);
			$lowername = mb_strtolower($choice['NAME']);
			$lowerinput = mb_strtolower($name);
			if ($choice['DECEASED']
			||	(	$succeeded
				=	!empty($artist['FOLLOW_FIRST'])
				&&	($artist['FOLLOW_FIRST'] > $party['STAMP'])
				)
			||	$choice['STOPPED']
			) {
				register_warning(
					$choice['DECEASED']
				?	'lineup:warning:artist_deceased_LINE'
				:	(	$succeeded
					?	'lineup:warning:artist_succeeded_LINE'
					:	'lineup:warning:artist_stopped_LINE'
					),
					DO_UBB,
					['ARTISTID' => $artistid]
				);
				header('X-OPTIONS: none');
				return 206;
			}
			if ($choice['PREV_STAMP']
			&&	$choice['PREV_STAMP'] < $party['STAMP'] - 2 * ONEYEAR
			) {
				# show select if last performance was more than 2 years ago
				$showselect = 'artist_performed_long_ago';
			} elseif (
				!str_contains($lowername, $lowerinput)
			&&	$lowerinput != $artistid
			) {
				similar_text($lowername, $lowerinput, $pct);
				if ($pct < 80) {
					$showselect = 'name_differs_a_lot';
				}
			}
		}
		if ($showselect) {
			sort_lineup_artists($choices, $party['STAMP']);
			?><select name="ARTISTID" class="fw rethink"><?
			$addempty = false;
			$selectedartist = null;
			require_once '_artist.inc';
			foreach ($choices as $choice) {
				show_artist_option($choice, $addempty, $selectedartist, $party['STAMP']);
			}
			if ($addempty) {
				?><option value="0" selected></option><?
			}
			?></select><?
			if (headers_sent()) {
				error_log_r($_REQUEST,'headers already sent at '.$_SERVER['REQUEST_URI']);
			}
			?>&nbsp;<?
			if ($size > 1) {
				echo __('lineup:info:multiple_choices_LINE');
			}
			if ($showselect !== true) {
				?><span class="warning"><?= __('lineup:warning:'.$showselect.'_LINE') ?></span><?
			}
			header('X-OPTIONS: '.implodekeys(',',$choices));
			return 206;
		}
	} elseif (have_idnumber($_POST,'ARTISTID')) {
		$artistid = $_POST['ARTISTID'];
	} else {
		_error('Ik weet niets te doen!');
		return 400;
	}
	if (have_something($_POST, 'TYPE')
	&&	is_artist_type($_POST['TYPE'])
	) {
		$type = $_POST['TYPE'];
	} else {
		$type = null;
		if (isset($choice)) {
			$types = $choice['TYPE'];
		} else {
			if (!$artistid) {
				error_log_r($_POST,'_POST');
			}
			if (false === ($types = db_single('artist','SELECT TYPE FROM artist WHERE ARTISTID = '.$artistid))) {
				return 500;
			}
		}
		$type = '';
		if ($types) {
			$opts = [];
			foreach (explode(',', $types) as $type) {
				if (artist_type_ok_for_lineups($type)) {
					$opts[$type] = $type;
				}
			}
			if ($opts) {
				$type_stats[$artistid] = sort_artist_types($opts, $artistid, stamp: $party['STAMP']);
				$type = reset($opts);
			}
		}
	}

	$partyid = $party['PARTYID'];

	if (!have_number($_POST, 'AREAID')) {
		if (false === ($areainfo = db_single_array('lineup','
			SELECT MIN(AREAID), COUNT(DISTINCT AREAID)
			FROM lineup
			WHERE PARTYID = '.$partyid
		))) {
			return 500;
		}
		if ($areainfo) {
			[$areaid, $cnt] = $areainfo;
			switch ($cnt) {
			case 1:
				$_POST['AREAID'] = $areaid;
				break;
			case 0:
				$_POST['AREAID'] = 1;
				break;
			}
		} else {
			$_POST['AREAID'] = 1;
		}
	}

	if (!require_lineup_time($_POST,'TIME_START')
	||	!require_lineup_time($_POST,'TIME_END')
	) {
		return 400;
	}

	$areaid		= have_number($_POST,'AREAID') ?: 0;
	$time_start	= is_valid_lineup_time($_POST['TIME_START']);
	$time_end	= is_valid_lineup_time($_POST['TIME_END']);
	$date_start	= empty($_POST['DATE_START']) ? null : (require_datestr($_POST,'DATE_START') ? $_POST['DATE_START'] : null);

	[	$start_stamp,
		$start_stamp_utc,
		$duration,
		$dst,
		$end_dst
	] =	get_start_and_duration(
			$party,
			$time_start,
			$time_end,
			$date_start
		);

	if (false === ($have = db_single('lineup', '
		SELECT 1
		FROM lineup
		WHERE PARTYID  = '.$partyid.'
		  AND ARTISTID = '.$artistid.'
		  AND AREAID   = '.$areaid.'
		  AND '.number_is_same('START_STAMP', $start_stamp).'
		  AND '.number_is_same('DURATION',	$duration)
	))) {
		return 500;
	}
	if ($have) {
		header('X-WARNING: already_exists');
		register_warning('lineup:warning:artiest_already_in_lineup_LINE',DO_UBB,['ARTISTID'=>$artistid]);
		return 400;
	}

	# get lineupid from _log if exists, for entry most resembling what we want (keep lineupid as stable as possible)

	if (false === ($have_area = db_single('partyarea', "
		SELECT 1 FROM partyarea
		WHERE PARTYID = $partyid
		  AND AREAID = $areaid",
		DB_FORCE_MASTER
	))) {
		return 500;
	}
	if (false === ($lineupid = get_lineupid('lineup_log',
		$partyid,
		$artistid,
		$areaid,
		$type,
		$start_stamp,
		$duration
	))) {
		return 500;
	}

	if (!$have_area) {
		if (false === ($partyareaid = get_partyareaid_for_single_artist(
			$partyid,
			$artistid,
			$areaid,
			$type,
			$start_stamp,
			$duration))
		||	false === ($partyarea_fields = explain_table('partyarea'))
		) {
			return 500;
		}
	}

	if (	$lineupid === false
	||	  !db_lock_tables(['partyarea','lineup','partyarea_log'])
	) {
		return 500;
	}

	if (!db_replace('lineup','
		INSERT INTO lineup SET
			LINEUPID		= '.($lineupid ?: 0).',
			PARTYID			= '.$partyid.',
			MSTAMP			= '.CURRENTSTAMP.',
			MUSERID			= '.CURRENTUSERID.',
			TYPE			= "'.$type.'",
			ARTISTID		= '.$artistid.',
			AREAID			= '.$areaid.',
			DST				= '.($dst				=== null ? 'NULL' : 'b\''.(	$dst ? '1' : '0').'\'').',
			END_DST			= '.($end_dst			=== null ? 'NULL' : 'b\''.($end_dst ? '1' : '0').'\'').',
			START_STAMP		= '.($start_stamp		=== null ? 'NULL' : $start_stamp).',
			START_STAMP_UTC	= '.($start_stamp_utc	=== null ? 'NULL' : $start_stamp_utc).',
			DURATION		= '.($duration			=== null ? 'NULL' : $duration)
		)
	) {
		return 500;
	}
	if (!$lineupid) {
		$lineupid = db_insert_id();
	}
	# create empty area if none exists yet:
	if (!$have_area) {
		if ($partyareaid) {
			$fieldstr = implodekeys(',', $partyarea_fields);
			$ok = db_insert('partyarea','
				INSERT INTO partyarea ('.$fieldstr.')
				SELECT '.preg_replace('"\b(AREAID)\b"',$areaid,str_replace(['MUSERID','MSTAMP'],[CURRENTUSERID,CURRENTSTAMP],$fieldstr)).'
				FROM partyarea_log
				WHERE PARTYAREAID='.$partyareaid.'
				ORDER BY DSTAMP DESC
				LIMIT 1'
			);
		} else {
			$ok = db_insert('partyarea','
				INSERT IGNORE INTO partyarea SET
					MUSERID		='.CURRENTUSERID.',
					MSTAMP		='.CURRENTSTAMP.',
					PARTYID		='.$partyid.',
					AREAID		='.$areaid
			);
		}
		if (!$ok) {
			db_delete('lineup','DELETE FROM lineup WHERE LINEUPID='.$lineupid);
			return 500;
		}
	}
	db_unlock_tables();

	# check for duo's
	if (false === ($updated = check_duos(
		$partyid,
		$artistid,
		$type,
		$areaid,
		$start_stamp,
		$start_stamp_utc,
		$dst,
		$end_dst,
		$duration,
		$time_start,
		$time_end)
	)) {
		return 500;
	}

	flush_party($partyid);

	clear_determined_genres($partyid);

	require_once '_profile.inc';
	clear_element_profile('party', $partyid);

	require_once '_pagechanged.inc';
	page_changed('party',$partyid, 'artist', $updated ?: $artistid);

	partylist_needs_refresh('artist', $updated ?: $artistid);
	partylist_needs_refresh_if_in_future($partyid);

	detect_almost_complete_timetable($partyid);

	register_notice('lineup:notice:artist_added_LINE', DO_UBB, ['ARTISTID' => $artistid]);

	return 200;
}

function lineup_show_parse_form(int $partyid): bool {
	if (false === $areas = db_simple_hash(['lineup', 'partyarea'], '
		SELECT DISTINCT lineup.AREAID,NAME
		FROM lineup
		LEFT JOIN partyarea USING (PARTYID,AREAID)
		WHERE PARTYID='.$partyid.'
		ORDER BY AREAID'
	)) {
		return false;
	}
	?><form<?
	?> accept-charset="utf-8"<?
	?> autocomplete="off"<?
	?> id="lineupform"<?
	?> onsubmit="submitParseForm(this,<?= $partyid; ?>);return false"<?
	?>><?
	layout_open_box('party');
	?><div class="block"><?
	?><div><?
	?><textarea<?
	?> autofocus<?
	?> required<?
	?> name="BODY"<?
	?> cols="80"<?
	?> data-max="20"<?
	?> data-min="2"<?
	?> rows="20"<?
	?> class="growToFit"<?
	?>></textarea></div><?
	?><div><select name="PROCESS_ACTION" onchange="setdisplay(this.nextSibling,this.selectedIndex==2)"><?
		?><option value="replace_all"><?= __('lineup:form:replace_entire_lineup') ?></option><?
		?><option value="replace_parts" selected="selected"><?= __('lineup:form:only_replace_area_above') ?></option><?
		?><option value="add_to_area"><?= __('lineup:form:add_to_area') ?>: &hellip;</option><?
	?></select><?
	?><select name="AREAID" class="hidden"><?
		if (!$areas) {
			$lastid = 0;
		} else {
			$lastid = 0;
			foreach ($areas as $areaid => $name) {
				while (++$lastid < $areaid) {
					?><option value="<?= $lastid ?>"><?= $lastid ?>: <?= element_name('new_area') ?></option><?
				}
				?><option value="<?= $areaid ?>"><?= $areaid ?>: <?= escape_utf8($name) ?></option><?
			}
		}
		?><option value="<?= $lastid + 1 ?>"><?= $lastid + 1 ?>: <?= element_name('new_area') ?></option><?
	?></select></div><?
	?></div><?
	layout_close_box();
	?><div id="lineup-parse-error"></div><?
	?><div class="block"><input type="submit" name="PARSEFORM" value="<?= __('action:process') ?>"></div><?
	?></form><?
	return true;
}

function lineup_parse(array $party): array|false {
	$shown_body = false;
	if (!($body = require_something_trim($_POST, 'BODY', utf8: true) ? $_POST['BODY'] : '')) {
		return false;
	}

	# convert to win1252 because rest does not support utf8
	#$body = iconv('UTF-8','WINDOWS-1252//TRANSLIT',$body);

	$process_action = have_element($_POST, 'PROCESS_ACTION', ['replace_all', 'replace_parts', 'add_to_area']) ?: '';

	if (false === $oldareas = db_rowuse_hash('partyarea','
		SELECT AREAID,partyarea.*
		FROM partyarea
		WHERE PARTYID='.$party['PARTYID'],
		DB_USE_MASTER
	)) {
		return false;
	}
	require_once '_namefix.inc';
	$body = get_fixed_name_lineup($body, utf8: true);

	extract(count_characters($body));

	$body_length = mb_strlen($body);

	$normal_characters = $body_length ? $regular_characters   / $body_length : 0;
	$irregular_part	= $body_length ? $irregular_characters / $body_length : 0;

	error_log('irregular_part: '.$irregular_part);

	if ($irregular_part > .5) {
		$body = transliterate_irregular_letters_to_regular($body);
		error_log($body);
	}

	$lines = explode("\n", $body);
	$newlines = null;

	$add_to_areaid = $process_action === 'add_to_area' ? have_idnumber($_POST, 'AREAID') : 0;
	$areaid = 0;

	foreach ($lines as $line) {
		$line = preg_replace('"[\(\"\[]?(?:_ofc|official(?:\h*(?:fan|music|artist)\h*page)?|(?:the\h*)?artist(?!\w))(?:\s*page)?[\]\)\"]?$"ui', '', $line);
		$line = preg_replace('"\h+DJ$"ui', '', $line);
		$line = preg_replace('"^[\h\*\-\.\,]*(?:DJ\h+)?"ui', '', $line);
		$line = preg_replace('"^special\h*guest\h+"ui', '', $line);
		$line = utf8_mytrim($line);
		$line = preg_replace('"^(\?+\h*)"u', '', $line);

		# 23:00-00:00 // Chema, strip separator
		$line = preg_replace('"(\b\d{1,2}:\d{2})\h+//\h+"ui', '$1 ', $line);

		# line-up: artist1, artist2, artist3
		# lineup house area: artist1, artist2 artist3
		if (preg_match('"^line-?up\s*(?P<area_name>.*)?:\s+(?P<artist_names>.*?)\.?$"ui', $line, $matches)) {
			++$areaid;
			$newlines[] =
				!empty($match['area_name'])
			?	'area '.$areaid.': '.$match['area_name']
			:	'area '.$areaid;

			foreach (preg_split('"(?:[,\|]|(?<!w)/)\s* \s*"', $match['artist_names']) as $part) {
				$newlines[] = $part;
			}
		} elseif (
			!contains_partyflock_domain($line)
		#	artist1 // artist2 // artist3, ...
		#	artist1 | artist2 | $artist3, ...
		#	artist1, artist2, artist3, ...
		&&	preg_match('"[^/\|,]{3,}[/\|,]+[^/\|,]{3,}[/\|,]+[^/\|,]{3,}"u', $line)
		) {
			# recognize comma or slash separated line-up
			$parts = preg_split('"\s*(?:[,/\|]+|\h+en\h+|\h+and\h+|\h+&\h+)\s*"u', $line);
			foreach ($parts as $part) {
				$newlines[] = $part;
			}
		} else {
			$newlines[] = $line;
		}
	}

	$lines = $newlines;
	$onlyartists = false;
	$timepart = '(?<!\w)(?:(\d{1,2})[:\.\suh]?(\d{2}|u)\s*)(?:(?:u(?:ur|hr)?|hr?)\s*)?(?:(?:[\-'.MATCH_EN_DASH.MATCH_EM_DASH.'/\s]+|t[/\.]?m|tot)?\s*((?:(\d{1,2})[:\.\suh]?(\d{2}|u))|end))?(?:\s*(?:u(?:ur|hr)?|hr?)\b)?(?:\s*[:\-])?';

	set_memory_limit(GIGABYTE);

	require_once '_date.inc';

	if ($party['NOTIME']) {
		$stophour = $stopmins = false;
	} else {
		[,,, $stophour, $stopmins] = _getdate($party['STAMP'] + ($party['DURATION_SECS'] ?: 4 * ONEHOUR));
	}

	while (true) {
	$rows = null;
	$areas = null;
	$rowid = 0;
	$areaid = $add_to_areaid;
	$prevstarttime = null;
	$emptycnt = 0;
	$prevrowid = -1;
	$usetime = 0;
	$notime = null;
	$havenotime = false;
	$have_explicit_room = false;
	$havebullet = false;
	$bulletcnt = 0;

	$oklines = [];
	$no_match_cnt = 0;

	foreach ($lines as $line) {
		$newline = '';
		foreach (preg_split('"[\s\n\t]+"u', $line) as $part) {
			$part = utf8_mytrim($part);
			if (!$part) {
				continue;
			}
			if (preg_match('"^(?:https?://(?:vip\.)?partyflock\.nl/artist/(\d+)|\[(?:artist|dj)=(\d+)\])$"ui', $part, $match)) {
				$oklines[] = $match[0];
			} else {
				if ($newline) {
					$newline .= ' ';
				}
				$newline .= $part;
				++$no_match_cnt;
			}
		}
		$oklines[] = $newline;
	}
	if ($no_match_cnt < 4) {
		$lines = $oklines;
	}
	$oklines = [];
	$times = 0;
	$prev_lower_line = null;

	$regular_characters = 0;
	$irregular_characters = 0;	 # maths chars for example

	foreach ($lines as $line) {
		# strip - and / preceding line-up entry
		$line = utf8_mytrim($line,'-/');

		# strip dj preceding artist name
		if (preg_match('"^dj "ui', $line)) {
			$line = mb_substr($line, 3);
		}
		if (!$line) {
			continue;
		}
		$curr_lower_line = mb_strtolower($line);
		if ($prev_lower_line !== null) {
			if ($curr_lower_line === $prev_lower_line) {
				# skip identical lines
				continue;
			}
		}
		$prev_lower_line = $curr_lower_line;

		# is name exact artist name?
		$exacts = db_simpler_array('artist','SELECT ARTISTID FROM artist WHERE NAME="'.addslashes($line).'"');
		if (!$exacts) {
			if (preg_match('"'.$timepart.'"u', $line)) {
				++$times;
			}
		}
		require_once '_unicode.inc';
		if (preg_match('"^(?P<artist1>.*)(?:'.
			'\hversus\h'.
			'|\hw/'.
			'|\h[x'.MATCH_MULTIPLICATION_SIGN.']\h'.
			'|\hvs?\.?\h'.
			'|\hb(?:ack)?2b(?:ack)?(?:\h*with)?\h'.
			'|\hft\.?\h'.
			'|\hfeat\.?(?:uring)?\h'.
			')(?P<artist2>.*)$"ui', $line, $match)
		) {
			$oklines[] = [false, utf8_mytrim($match['artist1'])];

			# \x6: inherit time indicator
			# trim dot from vs/ft/v
			$line = "\x6".utf8_myltrim($match['artist2'],'.');
		}
		$oklines[] = [$exacts && count($exacts) === 1 ? $exacts[0] : false, $line];
	}
	$lines = $oklines;

	$timeparts = [];
	$nowdjs = 1;
	$cnt = 0;

	static $__mc_regex = '"^[\h-]*(?:(?:hosted|vocals)\h*by[:\h]*(?:mc)?|host|mc\'?s?)[:\h]+(?P<mc_name>.*?)\h*(?:\[(?P<maybe_artistid>\d+)])?$"ui';

	foreach ($lines as $line_ndx => $info) {
		[$exact, $line] = $info;
		++$cnt;
		if (!$line) {
			++$emptycnt;
			continue;
		}

		$line = utf8_myltrim($line,'>');

		$totalarea = false;
		if (!preg_match('"^https?://"u', $line)
		&&	(	preg_match('"^(?:time|area|stage|zaal|room)\s*#?(?P<areaid>\d+)(?:[\s'.MATCH_EN_DASH.MATCH_EM_DASH.MATCH_ANY_WHITESPACE.':]+(?P<name>.*))?$"ui', $line, $area_match)
			||	(	!$onlyartists
				&&	preg_match('"^(?:time table )?([\p{L}\d\w\-\s\(\),-=+\'&]+):+\s*$"ui', $line, $area_match)
				&&	!preg_match('"^mc\h"u', $line)
				)
			||	preg_match('"^(?P<name>.*\b(?:zaal|area))$"ui', $line, $area_match)
			||	preg_match('"^(?P<name>(?:zaal|area)\b.*)$"ui', $line, $area_match)
			||	(	$cnt == 1
				||	$emptycnt
				)
			&&	(	$totalarea = $times > 4
				&&	!$exact
				&&	!preg_match('"'.$timepart.'"u', $line)
				&&	$line[0] !== "\x6"
				)
			&&	!preg_match($__mc_regex, $line)
			||
				# input data is mainly normal characters
				$normal_characters > .8
			&&	($counts = count_characters($line))
				# this line has more than 80 irregular characters (math characters for example)
			&&	$counts['irregular_characters'] / mb_strlen($line) > .8
				# ^^^ probably an area name!
			)
		) {
			$have_explicit_room = true;
			if ($prevrowid > -1) {
				if (!$rows[$prevrowid]['TIME_END_TZI']
				&&	!isset($notime[$prevrowid])
				) {
					$rows[$prevrowid]['TIME_END_TZI'] = $stophour*100 + $stopmins;
				}
				$prevrowid = -1;
			}
			if (!empty($area_match['name'])) {
				$area_match['name'] = preg_replace('"^(?:zaal|area)\s+"u', '', $area_match['name']);
				if (!empty($area_match['areaid'])) {
					$areaid = $area_match['areaid'];
				} else {
					++$areaid;
				}
				$name = false;
				if ($area_match['name']
				&&	($parts = preg_split('"\s+|(\/)"u', $area_match['name'], -1, PREG_SPLIT_DELIM_CAPTURE))
				) {
					foreach ($parts as $part) {
						if ($name) {
							$name .= ' ';
						}
						$part = utf8_mytrim($part);
						if ($part == '/') {
							$name .= '/';
							continue;
						}
						$name .= utf8_ucfirst(mb_strtolower($part));
					}
				}
				$areas[$areaid] = parse_area_name($name);
			} else {
				if (!empty($area_match['areaid'])) {
					$areaid = $area_match['areaid'];
				} elseif (
					!$areaid
				||	$cnt - $emptycnt > 1
				) {
					++$areaid;
				}
				if (!isset($area_match[0])) {
					mail_log('area_match[0] is not set', item: $_POST, include_trace: true);
				}
				$areas[$areaid] = parse_area_name($totalarea ? $line : ($area_match[0] ?? ''));
			}
			$emptycnt = 0;
			continue;
		}

		$row = null;
		$testnames = null;
		$matchtype1 = $matchtype2 = $matchtype3 = false;

		$type_re = '[\(\[\-/\h\*\’\']*'.
			'(live(?:[\h\-/]*a[\h\-/]*v)?'.
			'|hybrid(?:\h*set)?'.
			'|vinyl(?:\h*set)?'.
			'|\bmc'.
			'|\bdj'.
			'|soundsystem'.
			'|\bss'.
			'|\bsom[\h\-]*systema?'.
			')?[\-\]\)/\h\*\’\']*';

		$type_from_match = function($arg): ?string {
			foreach ([
				'"\blive[\h\-/]*a[\h\-/]*v\b"i'	=> 'live_av',
				'"\blive\b"i'					=> 'act',
				'"\bhybrid\b"i'					=> 'hybrid',
				'"\bmc\b"i'						=> 'mc',
				'"\bsoundsystem\b"i'			=> 'soundsystem',
				'"(\h+ss$|^ss\h+)"i'			=> 'soundsystem',
				'"\bsom[\h\-]*systema?"i'		=> 'soundsystem',
				'"\bvocals\b"i'					=> 'singer',
			] as $recognize_type => $_type) {
				if (preg_match($recognize_type, $arg)) {
					return $_type;
				}
			}
			return null;
		};
		$found_type = null;

 		if ($inherit_time = $line[0] === "\x6") {
 			$line = mb_substr($line, 1);

		} elseif (!is_number($line)) {
			# ^^ skip single number on a line, prolly no time but ARTISTID
			if (!$exact && preg_match('"^'.$timepart.'$"ui', $line, $match)) {
				if ($nowdjs) {
					$nowdjs = false;
					$timeparts = [];
				}
				$timeparts[] = $line;
				continue;
			}
			if ($timeparts) {
				$nowdjs = true;
				$part = array_shift($timeparts);
				$line = $part.' '.$line;
			}
		}
		$name = $full_name = null;
		if (preg_match('"^\s*(?P<mc_type>MC[\s:])?(?P<artist_name>.*?)\[(?P<maybe_artistid>\d+)\]\s*$"ui', $line, $matches)) {
				#	 1	  2	  3
			$name = utf8_mytrim($matches['artist_name']);
			$row['MAYBEID'] = $matches['maybe_artistid'];
			if ($matches['mc_type']) {
				$row['HOST'] = true;
				$hostedby[$areaid] = $name;
			}

		} elseif (preg_match($__mc_regex, $line, $match)) {
			$name = utf8_mytrim($match['mc_name']);
			if (!empty($match['maybe_artistid'])) {
				$row['MAYBEID'] = $match['maybe_artistid'];
			}
			$hostedby[$areaid] = $name;
			if (!preg_match('"^mc\h"ui', $name)) {
				$name = 'mc '.$name;
			}
			$row['HOST'] = true;

		} elseif (preg_match('"^(visuals(?:\h*by)?|vj)[:\h]*(?:vj\h+)?(.*)$"ui', $line, $match)) {
			$name = 'vj '.utf8_mytrim($match[2]);

		} elseif (preg_match('"^(?:([^:]+)\s*[:>])?\s*https?://(?:[a-z\.]+)/artist/(\d+):(?:[a-z0-9_]+)(?:\.html)?"ui', $line, $matches)) {
			$row['ID'] = trim($matches[is_number($matches[1]) ? 1 : 2]);
			$name = memcached_artist($row['ID']);

		} elseif (
			$exact
		&&	preg_match('"(.*?)'.$timepart.'(.*)$"u', $line, $match)
		&&	$match[1].$match[2] != $line
		#	^^ only include exact when it also matches timepart and resulting line without the timepart is NOT identical to the line, so exact is not 'exact'
		#
		#	exact artist match that seems to have time (e.g. Johnny 500)
		) {
			$row['ID'] = $exact;
			$name = memcached_artist($row['ID']);

		} elseif (preg_match('"^\[(?:artist|dj)=(\d+)\]$"ui', $line, $matches)	) {
			$row['ID'] = $matches[1];
			$name = memcached_artist($row['ID']);

		} elseif (is_number($line)) {
			$name = $line;

		} elseif (preg_match('"^[\w\d\_\-]+$"u', $line)) {
			$name = $line;

		} elseif (
			($matchtype2 = preg_match(
				'"^\s*'.
				'([\xB7\x95\-])?'.
				'?\s*(?:(DJ|MC|live)[:\s])?'.
				'(.*?)'.
				$type_re.
				'\s(?'.$timepart.')?$"ui', $line, $matches))
		||

			($matchtype1 = preg_match(
				'"^\s*'.
				'([\xB7\x95\-])?'.		# optional prefix
				'\s*(?:'.$timepart.'\s*)?'.	# optional time
				'(?:(DJ|MC|live)[:\s]+)?'.
				'(.*?)'.
				$type_re.
				'\s*(?P<brackets>[\(\[][^\)\]]+[\)\]])?'.
				'\s*'.
				'$"ui', $line, $matches))
	) {
			if ($matchtype2) {
				if (!isset($matches[7])
				||	!isset($matches[8])
				||	!isset($matches[9])
				) {
					error_log_r($matches, 'notallset.matches');
					error_log_r($lines, 'lines');
				}
				[,	$bullet,
					$type,
					$name,
					$islive,
					$hstart,
					$mstart,
					$stop,
					$hstop,
					$mstop
				] = $matches;

				$found_type = $type_from_match($islive);

				if (!isset($matches[7])) {
					static $done = false;
					if (!$done) {
						error_log_r($_REQUEST,'_REQUEST');
						$done = true;
					}
				}
			} else {
				[,	$bullet,
					$hstart,
					$mstart,
					$stop,
					$hstop,
					$mstop,
					$type,
					$name
				] = $matches;

				if (!empty($matches[9])) {
					$found_type = $type_from_match($matches[9]);
				}
				if (!empty($matches['brackets'])) {
					if (empty($name)) {
						# sfsdfsdf [SOMETHING]
						# -> but some names are:
						# [SOMETHING]
						# fix this here, sow name is not empty but contains and not skip [SOMETHING]
						$name = $matches['brackets'];
						unset($matches['brackets']);
					} else {
						$full_name = $name.' '.$matches['brackets'];
					}
				}
			}

			if (mb_strlen($hstart)
			||	mb_strlen($hstop)
			) {
				if (!$hstop) {
					$hstop = 0;
				}
				$mstart =
					!$mstart
				||	$mstart === 'u'
				?	0
				:	(int)$mstart;

				$mstop =
					!$mstop
				||	$mstop === 'u'
				?	0
				:	(int)$mstop;

				if ($hstart == 24) {
					$hstart = 0;
				}

				if (!$stop) {
					if (!isset($lines[$line_ndx+1])) {
						# last entry
						$stop = 'end';
					}
					if ($prevrowid > -1) {
						$tmprowid = $prevrowid;
						do {
							if (!$rows[$tmprowid]['TIME_END_TZI']) {
								 $rows[$tmprowid]['TIME_END_TZI'] = $hstart * 100 + $mstart;
							}
						} while (!empty($rows[$tmprowid]['INHERITTIME']) && (--$tmprowid > -1));
					}
				}
				if (strtolower($stop) === 'end') {
					$row['TIME_END_TZI'] =
						!$party['NOTIME']
					&&	$party['STAMP']
					&&	$party['DURATION_SECS']
					?	$stophour * 100 + $stopmins
					:	null;

				} else {
					$row['TIME_END_TZI'] =
						!$hstop
					&&	!$mstop
					&&	$nowdjs === true
					&&	empty($timeparts)
					?	$stophour * 100 + $stopmins
					:	$hstop	* 100 + $mstop;
				}

				$row['TIME_START_TZI'] = ($hstart * 100 + $mstart) ?: '000';
				if (!$row['TIME_START_TZI']) {
					if (!$row['TIME_END_TZI']) {
						$row['TIME_START_TZI'] =
						$row['TIME_END_TZI'] = null;
					}
				}
				# need START_STAMP to be set in show_empty_spaces
				$row['START_STAMP'] = null;
			}

			if (preg_match('"partyflock\.nl/artist/(?<artistidstr>\d+):"ui', $name, $artist_match)
			||	preg_match('"^\[(?:artist|dj)=(?<artistidstr>\d+)\]$"ui',	$name, $artist_match)
			) {
				$row['ID'] = (int)$artist_matches['artistidstr'];
			}

			$row['TYPE'] = $found_type;
			$type = strtolower($type);

			if ($found_type === 'live'
			|| 		  $type === 'live'
			) {
				$row['LIVE'] = true;
			}
			if ($found_type === 'mc'
			||		  $type === 'mc'
			) {
				$row['HOST'] = true;
			}

		} else {
			continue;
		}
			if (!isset($row['TIME_START_TZI'])) {
				$row['TIME_START_TZI'] = null;
			}
			if (!isset($row['TIME_END_TZI'])) {
				$row['TIME_END_TZI'] = null;
			}
			$row['SRC'] = $line;
			$row['SAMETIME'] = 0;
			$row['INHERITTIME'] = $inherit_time;

			if (isset($row['ID'])) {
				if ($artist = query_artists('ARTISTID='.$row['ID'], $party['STAMP'])) {
					[$artistid, $artist] = keyval($artist);
					++$row['SAMETIME'];
					# 100% match
					$artist['DST'] = 100;
					$row['CHOICES'][] = [$artistid => $artist];
				}
			} else {
				# for number artists which are recognized as time part
				# for artist's within [] which are reecognized as type
				if (!$name) {
					if ($row['TIME_START_TZI']) {
						$name = $row['TIME_START_TZI'];
						$row['TIME_START_TZI'] = null;
					} else {
						# [KRTM] for example
						$name = $row['SRC'];
					}
				}

				$name = utf8_mytrim($name, EN_DASH.EM_DASH.'"');
				$testnames = [];

				if ($full_name) {
					# best match
					$testnames[] = $full_name;
				}
				if (preg_match('"^\s*(.* present(?:s)?(?::\s*|\s+))(.*?)\s*$"ui', $name, $matches)) {
					$testnames[] = $matches[1].' '.$matches[2];
					$testnames[] = $matches[2];
				} else {
					$testnames[] = $name;
				}
				$first = true;
				foreach ($testnames as $testname) {
					if (!preg_match('"\bpresents?\b"ui', $testname)) {
						$parts = preg_split('"(\s+(&|v\.?s\.?|and|en|feat(?:uring|\.)?)|[,/])\s+"ui', $testname);
						if (count($parts) > 1) {
							foreach ($parts as $part) {
								$testnames[] = $part;
							}
						}
					}
					if (preg_match('"^(.*?) (?:sound\s*system|ss)$"ui', $testname, $match)) {
						$testnames[] = [$match[1], 'soundsystem'];
					}
				}
				$gotone = false;
				$party['PROCESS_ACTION'] = $process_action;
				$party['AREAID'] = $areaid ?: 1;

				foreach ($testnames as &$testname) {
					$prefer_type = null;
					if (is_array($testname)) {
						[$newname, $prefer_type] = $testname;
						$testname = $newname;
						$row['TYPE'] = $prefer_type;
					}
					if (false === ($choices = get_artist_choices(
							party:		 $party,
							input:		 $testname,
							size:		 $size,
							include:	 40,
							prefer_type: $prefer_type
					))) {
						return false;
					}

					if ($choices) {
						++$row['SAMETIME'];
						$row['CHOICES'][] = $choices;
						if ($first) {
							break;
						}
						$gotone = true;
						$testname = null;
					}
					if ($first) {
						$testname = null;
					}
					$first = false;
				}
				unset($testname);
				if (!$first
				&&	$gotone
				) {
					foreach ($testnames as $testname) {
						if ($testname) {
							$row['CHOICES'][] = $testname;
						}
					}
				}
			}
			if (isset($row['MAYBEID'])) {
				$maybeid = $row['MAYBEID'];
				$found = false;
				if (!empty($row['CHOICES'])) {
					foreach ($row['CHOICES'] as $ndx => &$artists) {
						foreach ($artists as $artistid => &$artist) {
							if ($artistid === $maybeid) {
								$artist['BESTFIT'] = true;
								$found = true;
							}
						}
						unset($artist);
					}
					unset($artists);
				}
				if (!$found) {
					if ($artist = query_artists('ARTISTID='.$maybeid, $party['STAMP'])) {
						[$artistid, $artist] = keyval($artist);
						$artist['BESTFIT'] = true;
						$row['CHOICES'][0][$artistid] = $artist;
					}

				}
			}
			$row['AREAID'] = $areaid ?: 1;
			if ($prevstarttime != $row['TIME_START_TZI']
			&&	$emptycnt
			) {
				if (!$areaid) {
					// no areas named
					foreach ($rows as $tmprowid => &$tmprow) {
						++$tmprow['AREAID'];
					}
					unset($tmprow);
					$areaid = 2;
				} else {
					++$areaid;
				}
			}
			if ($inherit_time
			&&	$prevrowid >= 0
			) {
				$prevrow = $rows[$prevrowid];
				if (isset($prevrow['TIME_START_TZI'])) {
					$row['TIME_START_TZI'] = $prevrow['TIME_START_TZI'];
					$row['TIME_END_TZI']   = $prevrow['TIME_END_TZI'];
				}
			}
			if (!empty($row['TIME_START_TZI'])
			||	!empty($row['TIME_END_TZI'])
			) {
				++$usetime;
			} else {
				$notime[$rowid] = true;
				$havenotime = true;
			}

			$row['NAME'] = $name;

			if (!isset($bullet)) {
				$shown_body = true;

			} elseif ($bullet) {
				$row['HAVEBULLET'] = true;
				++$bulletcnt;
			}
			$rows[$prevrowid = $rowid++] = $row;
			$prevstarttime = $row['TIME_START_TZI'];

		$emptycnt = 0;
	}

		if (!$rows) {
			$onlyartists = true;
			continue;
		}
		break;
	}
	$rowcnt = count($rows);

	if ($areaid
	&&	$oldareas
	) {
		for ($i = 0; $i <= $areaid; ++$i) {
			if (isset($oldareas[$i])
			&&	!isset($areas[$i])
			) {
				$areas[$i] = $oldareas[$i];
			}
		}
	}

	return [
		'ROWS'	=> $rows,
		'AREAS'	=> $areas,
	];
}

function parse_area_names(
	int		$partyid,
	array	$have_area,
	?array  $artists_in_area	  = null,
	?array &$changed_partyareaids = null,
): array|false {
	if (!isset($_POST['AREANAME'])) {
		return [];
	}

	if (!require_hash($_POST, 'AREANAME',		HASH_NUMBER, HASH_STRING)
	||	!require_hash($_POST, 'AREANAME_FOR_APPIC',	HASH_NUMBER, HASH_STRING)
	||	!require_hash($_POST, 'HOSTEDBY',		HASH_NUMBER, HASH_STRING)
	||	!empty($_POST['HOSTEDBYID'])	# might be empty when last hostedbyid is removed
	&&	!require_hash($_POST, 'HOSTEDBYID',		HASH_NUMBER, HASH_NUMBER)
	||	!require_hash($_POST, 'GENRES',			HASH_NUMBER, HASH_STRING)
	) {
		return false;
	}

	$add_to_area = isset($_POST['PROCESS_ACTION']) && $_POST['PROCESS_ACTION'] === 'add_to_area';

	foreach ($_POST['AREANAME'] as $partyareaid => $name) {
		$name_for_appic = $_POST['AREANAME_FOR_APPIC'][$partyareaid] ?? '';
		[$name, $hostedby] = parse_area_host($name);

		if ($name_for_appic === $name) {
			$name_for_appic = '';
		}
		if (isset($_POST['HOSTEDBY'][$partyareaid])) {
			[, $morehostedby] = parse_area_host('', $_POST['HOSTEDBY'][$partyareaid]);
			if ($morehostedby) {
				$hostedby = $hostedby ? ', '.$morehostedby : $morehostedby;
			}
		}
		$areaid = have_idnumber($_POST['ORDERID'], $partyareaid) ?: 0;
		$hostedbyid = isset($_POST['HOSTEDBYID']) && have_idnumber($_POST['HOSTEDBYID'],$partyareaid) ? $_POST['HOSTEDBYID'][$partyareaid] :  0;
		$genres = isset($_POST['GENRES'][$partyareaid]) ? parse_genres($_POST['GENRES'][$partyareaid]) : null;

		$have_areaids[$areaid] = $areaid;

		if (!isset($have_area[$partyareaid])) {
			# new area
			$partyareaid = 0;
		} else {
			if ($have_area[$partyareaid]['AREAID']		=== $areaid
			&&	$have_area[$partyareaid]['HOSTEDBY']		=== $hostedby
			&&	$have_area[$partyareaid]['HOSTEDBYID']		=== $hostedbyid
			&&	$have_area[$partyareaid]['GENRES']		=== $genres
			&&	$have_area[$partyareaid]['NAME']		=== $name
			&&	$have_area[$partyareaid]['NAME_FOR_APPIC']	=== $name_for_appic
			) {
				# no changes needed
				continue;
			}
		}

		if (!$partyareaid) {
			# find best matching

			$partyareaid = get_partyareaid($partyid, [
				'AREAID'	=> $areaid,
				'NAME'		=> $name,
				'NAME_FOR_APPIC'=> $name_for_appic,
				'HOSTEDBY'	=> $hostedby,
				'HOSTEDBYID'	=> $hostedbyid,
				'GENRES'	=> $genres
			], $artists_in_area);

			if ($partyareaid === false) {
				return false;
			}
		}

		static $__used_partyarea;
		if ($partyareaid) {
			if (isset($__used_partyarea[$partyareaid])) {
				$partyareaid = 0;
			} else {
				$__used_partyarea[$partyareaid] = $partyareaid;
			}
		}
		$storearea[] =
			'('.($partyareaid ?: 0).
			','.$partyid.
			','.$areaid.
			','.CURRENTUSERID.
			','.CURRENTSTAMP.
			',"'.addslashes($name).
			'","'.addslashes($name_for_appic).
			'","'.addslashes($hostedby).
			'",'.$hostedbyid.
			',"'.addslashes($genres).
			'")';

		if ($partyareaid) {
			$changed_partyareaids[] = $partyareaid;
		}
	}
	foreach ($artists_in_area as $areaid => $artists) {
		if (!isset($have_areaids[$areaid])) {
			# create new empty area
			$storearea[] =
				'('.($partyareaid = get_partyareaid($partyid, [], $artists_in_area)).
				','.$partyid.
				','.$areaid.
				','.CURRENTUSERID.
				','.CURRENTSTAMP.
				',"'.addslashes($name = '').
				'","'.addslashes($name_for_appic = '').
				'","'.addslashes($hostedby = '').
				'",'.($hostedbyid = 0).
				',"'.addslashes($genres = '').
				'")';

			if ($partyareaid) {
				$changed_partyareaids[] = $partyareaid;
			}
		}
	}
	return isset($storearea) ? $storearea : [];
}

function parse_area_name(string $area_name): array {
	# to get rid of emoji's:
	$area_name = remove_emojis($area_name);

	# get rid of math chars:
	$area_name = transliterate_irregular_letters_to_regular($area_name);

	require_once '_namefix.inc';
	$area_name = get_fixed_utf8_name($area_name, is_title: true);
	$area_name = str_replace(' vs. ',' vs ',$area_name);
	$name = $genres = $host = '';

	list($area_name, $host) = parse_area_host($area_name);

	if (preg_match('"^(.*)\((.*)\)\s*$"u', $area_name, $matches)
	&&	false === mb_strpos($matches[1], '(')
	) {
		$area_name = $matches[1];
		$genres = strip(parse_genres($matches[2]));
	}
	$area_name = strip($area_name);
	if (preg_match('"^(.*)?(?:(?:hosts|presents):?\s+(.*))$"ui', $area_name, $matches)) {
		list(,$host,$name) = $matches;
	} else {
		$name = $area_name;
	}
	if (!$name && $genres) {
		$name = $genres;
		$genres = '';
	}
	$name	= fix_upper_words($name);
	$host	= fix_upper_words($host);
	$genres	= fix_upper_words($genres);

	return ['NAME' 		=> $name,
		'HOSTEDBY'	=> $host,
		'GENRES'	=> $genres,
		'GENERIC'	=> !$name && !$host &&!$genres
	];
}

function parse_genres($genres) {
	if (!HOME_OFFICE) {
		return $genres;
	}
	$genres = preg_replace('"(\d{4}\s*)\-(\s*\d{4})"u', "\\1\x1F\\2", $genres, -1, $count1);
	$genres = preg_replace('"(\d{1,2}:\d{1,2}\s*)\-(\s*\d{1,2}:\d{1,2})"u', "\\1\x1F\\2", $genres, -1, $count2);
	$count = $count1 + $count2;
	$genres = str_replace('x-tra', "\x1A", $genres);

	foreach (preg_split('"[,/\-]"',strip($genres)) as $genre) {
		$newgenres[] = fix_upper_words(strip(str_replace("\x1A", 'x-tra', $genre)));
	}

	$result = implode(', ', $newgenres);
	if ($count) {
		$result = str_replace("\x1F",'-',$result);
	}
	return $result;
}

function parse_area_host(string $area, string $host = ''): array {
	$input = $area ? $area : $host;

	if (preg_match('"^(.*?)?\('.($inner = '(?:(?:(?:hosted|gehost|powered)\s+)?\s+(?:by|door):?\s*(.*))').'\)(.*?)$"ui', $input, $matches)
	||	preg_match('"^(.*?)?'.$inner.'$"ui', $input, $matches)
	) {
		list(, $area, $host) = $matches;
	}
	if (preg_match('"^(?:area\s*\d+|line[\s\-]*up)$"ui', $area)) {
		$area = '';
	}
	require_once '_namefix.inc';
	if ($area) {
		$area = strip($area);
		$area = get_fixed_utf8_name($area, is_title: true);
	}
	if ($host) {
		$host = strip($host);
		$host = get_fixed_utf8_name($host, is_title: true);
	}

	if (preg_match('"^\((.*?)\)$"u', $area, $matches)) {
		$area = utf8_ucfirst($matches[1]);
	}

	return	have_admin('party')
	?	[	$area,
			$host
		]
	:	[	utf8_ucfirst(fix_upper_words($area)),
			utf8_ucfirst(fix_upper_words($host))
		];
}

function fix_upper_word($word) {
	return	mb_strlen($word) > 3
	&&	ctype_upper($word)
	?	ucfirst(mb_strtolower($word))
	:	$word;
}

function fix_upper_words(string $name): string {
	if (!$name) {
		return $name;
	}
	$newname = '';
	foreach (preg_split('"([\s\-,!\?_]+)"u', $name, -1, PREG_SPLIT_DELIM_CAPTURE) as $part) {
		$newname .= fix_upper_word($part);
	}
	return $newname;
}

function strip(string $word): string {
	static
		$__stripstr	= " \r\n\t\v".NO_BREAK_SPACE.SOFT_HYPHEN.'–-.:;_|/,',
		$__stripright	= '}])';
		$__stripleft	= '{[(';

	return utf8_myrtrim(utf8_myltrim(utf8_mytrim($word, $__stripstr), $__stripleft), $__stripright);
}

function show_area_formpart(int $partyid, int $areaid, ?array $area = null): bool {
	$maxareaid = db_single('partyarea','
		SELECT AREAID
		FROM partyarea
		WHERE PARTYID='.$partyid.'
		ORDER BY AREAID DESC
		LIMIT 1'
	);
	if ($maxareaid === false) {
		return false;
	}

	$partyareaid = getifset($area,'PARTYAREAID') ?: null;

	$fillcnt = $area
	?	(($area['NAME'] ? 1 : 0) + (!empty($area['HOSTEDBY']) ? 1 : 0) + (!empty($area['GENRES']) ? 1 : 0))
	:	0;

	$none_saved = isset($_POST['PROCESS_ACTION']);

	$add_to_area = isset($_POST['PROCESS_ACTION']) && $_POST['PROCESS_ACTION'] == 'add_to_area';

	?><table class="dens fw nomargin nowrap"><?
	foreach ([			#  data					extra			placeholder
		'AREANAME'		=> [$area['NAME'] ?? null,		'area',			null],
		'AREANAME_FOR_APPIC'	=> [$area['NAME_FOR_APPIC'] ?? null,	get_appic_icon(),	null],
		'HOSTEDBYID'		=> [$area['HOSTEDBYID'] ?? null,	'host(area)',		element_name('organization')],
		'HOSTEDBY'		=> [$area['HOSTEDBY'] ?? null,		'host(area)',		__('lineup:info:area_host_description')],
		'GENRES'		=> [$area['GENRES'] ?? null,		'extra',		__('lineup:info:for_example_genres')],

	] as $key => [$data, $extra, $placeholder]) {

		$class = $extra === 'area' ? null : ' class="light"';
		?><tr><?
		?><td><?
		if ($extra[0] === '<') {
			echo $extra;
		} else {
			if ($extra === 'extra') {
				?> (<?= Eelement_name($extra) ?>)<?
			} else {
				echo Eelement_name($extra);
				if ($extra === 'area') {
					?> <? echo $areaid;
				}
			}
			?>:&nbsp;<?
		}
		?></td><td class="fw"><?
		if ($key === 'HOSTEDBYID') {
			require_once '_finditem.inc';
			show_find_item([
				'element'		=> 'organization',
				'data-areaid'		=> $partyareaid,
				'id_name'		=> 'HOSTEDBYID_multi_'.uniqid().'['.$partyareaid.']',
				'selected'		=> $data,
				'data-stored'		=> $none_saved ? false : $data,
				'data-multi-instance'	=> true,
				'onchange'		=> 'Pf.changeHOSTEDBYID',
				'placeholder'		=> $placeholder,
				'multiple'		=> false,
				'class'			=> $data ? null : 'light'
			]);
		} else {
			?><input<?
			if ($placeholder) {
				?> placeholder="<?= $placeholder ?>"<?
			}
			if ($dol = $key === 'HOSTEDBY' || $key === 'GENRES') {
				if (!$data) {
					?> class="light"<?
				}
			}
			if ($key === 'AREANAME') {
				?> id="areanameinput-<?= $partyareaid ?>"<?
			}
			?> maxlength="255"<?
			?> style="min-width:20em"<?
			?> onkeyup="<?
				if (!$none_saved) {
					?>needSave(this);<?
				}
				if ($dol) {
					echo 'Pf.change'.$key.'(this,'.$partyareaid.')';
				}
			?>"<?
			if ($data) {
				?> value="<?= escape_utf8($data) ?>"<?
			}
			?> type="text"<?
			?> name="<?= $key ?>[<?= $partyareaid ?>]"<?
			?>><?
		}
		?></td></tr><?
	}
	?></table><?
	if (!$partyareaid
	||	$add_to_area
	) {
		# new area or adding to existing (don't change then)
		?><input type="hidden" name="ORDERID[<?= $partyareaid ?: null ?>]" value="<?= $areaid ?>"><?
		return true;
	}


	?><table class="fw nb no-bottom"><tr><td><?
	?><span<?
	?> class="unhideanchor ptr"<?
	?> onclick="Pf.removeArea(<?
		?>this,<?= $partyid
		?>,<?= $partyareaid
		?>,'<?= escape_utf8(htmlentities($area['NAME'],ENT_QUOTES))
		?>',<?= empty($area['HAVE_LINEUP']) || !$partyareaid ? 'false' : 'true'
		?>)"<?
	?>><?
	echo get_remove_char([
		'class'		=> 'lower',
		'label'		=> __('action:remove'),
	]);
	?></span><?
	?></td><?

	?><td class="right nowrap"><?
	?>verplaats area: <?
	$maxareaid = max(30, $maxareaid+5);
	?><select<?
	?> id="areaorderidselect-<?= $partyareaid ?>"<?
	?> data-stored="<?= $areaid ?>"<?
	?> data-original="<?= $areaid ?>"<?
	?> name="ORDERID[<?= $partyareaid ?>]"<?
	?> onchange="Pf.moveArea(this);needSave(this)"<?
	?> class="three_digits areaselect"<?
	?>><?
	for ($i = 0; $i <= $maxareaid; ++$i) {
		?><option<?
		if ($i
		&&	$areaid == $i
		) {
			?> selected="selected"<?
		}
		?> value="<?= $i; ?>"><?
		if ($i) {
			echo $i;
		}
		?></option><?
	}
	?></select><?
	?></td><?

	?></tr></table><?
	return true;
}

function check_duos(
	int|array	$partyid,
	int		$artistid		= null,
	string		$type			= null,
	int		$areaid			= null,
	int		$start_stamp		= null,
	int		$start_stamp_utc	= null,
	bool		$dst			= null,
	bool		$end_dst		= null,
	int		$duration		= null,
	int		$time_start		= null,
	int		$time_end		= null
): array|false {
	if (!$artistid) {
		[	$newpartyid,
			$artistid,
			$type,
			$areaid,
			$start_stamp,
			$start_stamp_utc,
			$dst,
			$end_dst,
			$duration
		] = $partyid;

		$partyid = $newpartyid;
	}
	if (!($artist_name = memcached_artist($artistid))) {
		mail_log(
			"check duos for non-existent artistid $artistid",
			item: ['_REQUEST'	=> $_REQUEST,
			 	'_POST'		=> $_POST
			],
			include_trace: true
		);
		return [];
	}
	if (!($parents = db_simple_hash('artistgroup','
		SELECT ARTISTID,NAME
		FROM artistgroup
		JOIN artist USING (ARTISTID)
		WHERE !STOPPED
		  AND NAME LIKE "%'.addslashes($artist_name).'%"
		  AND MEMBERID='.$artistid,
		DB_USE_MASTER
	))) {
		return $parents === false ? false : [];
	}
	$updated = [];
	foreach ($parents as $parentid => $pname) {
		if (!($members = db_simple_hash(['artist', 'artistgroup'], '
			SELECT artist.ARTISTID, NAME
			FROM artistgroup
			JOIN artist ON artist.ARTISTID = artistgroup.MEMBERID
			WHERE artistgroup.ARTISTID = '.$parentid,
			DB_USE_MASTER
		))) {
			if ($members === false) {
				return false;
			}
			continue;
		}
		$stripname = $pname;
		foreach ($members as $memberids => $mname) {
			$stripname = str_ireplace($mname, '', $stripname);
		}
		$stripname = str_replace([' en ',' and '], '', $stripname);
		$stripname = utf8_mytrim($stripname, ',&.!');

		if ($stripname) {
			continue;
		}
		# this duo might be possible
		# are all members in lineup with identical areaid, time_start and time_end?

		if (false === $have_cnt = db_single('lineup','
			SELECT COUNT(DISTINCT ARTISTID)
			FROM lineup
			WHERE '.($wherepart = '
				ARTISTID IN ('.($artistidstr = implodekeys(',',$members)).')
			  AND PARTYID	='.$partyid.'
			  AND TYPE	="'.$type.'"
			  AND AREAID	='.$areaid.'
			  AND '.number_is_same('START_STAMP', $start_stamp).'
			  AND '.number_is_same('DURATION', $duration)
			  ),
			DB_USE_MASTER
		)) {
			return false;
		}
		if ($have_cnt !== count($members)) {
			continue;
		}
		# remove all constituents

		if (!db_insert('lineup_log','
			INSERT INTO lineup_log
			SELECT *,'.CURRENTSTAMP.',0 FROM lineup
			WHERE '.$wherepart)
		||	!db_delete('lineup','
			DELETE FROM lineup
			WHERE '.$wherepart)
		||	!db_insert('lineup','
			INSERT INTO lineup SET
				PARTYID		='.$partyid.',
				MSTAMP		='.CURRENTSTAMP.',
				MUSERID		=0,
				TYPE		="'.$type.'",
				ARTISTID	='.$parentid.',
				AREAID		='.$areaid.',
				START_STAMP	='.($start_stamp	=== null ? 'NULL' : $start_stamp).',
				START_STAMP_UTC	='.($start_stamp_utc	=== null ? 'NULL' : $start_stamp_utc).',
				DST			='.($dst		=== null ? 'NULL' : 'b\''.($dst ? 1 : 0).'\'').',
				END_DST		='.($end_dst		=== null ? 'NULL' : 'b\''.($end_dst ? 1 : 0).'\'').',
				DURATION	='.($duration		=== null ? 'NULL' : $duration)
			)
		) {
			return false;
		}
		$updated = array_merge($updated, array_keys($members));
		$updated[] = $parentid;
	}

	flush_party($partyid);

	partylist_needs_refresh('artist', $updated);
	partylist_needs_refresh_if_in_future($partyid);

	return $updated;
}

function get_last_performance(int $artistid, int $since): array|null|false {
	return memcached_single_assoc(['lineup','party'],'
		SELECT PARTYID, STAMP
		FROM lineup
		JOIN party USING (PARTYID)
		WHERE ARTISTID='.$artistid.'
		  AND STAMP<'.$since.'
		ORDER BY STAMP DESC
		LIMIT 1'
	);
}

function show_previous_performance_warning(array $lineup_row, array $party): bool {
	if (($last_performance = get_last_performance($lineup_row['ARTISTID'], $party['STAMP']))
	&&	($class		   = get_long_ago_class($party, $last_performance['STAMP']))
	) {
		?><div class="<?= $class ?>"><?= Eelement_name('previous_performance') ?>: <?= get_element_link('party',$last_performance['PARTYID']) ?>, <? _date_display($last_performance['STAMP']) ?></div><?
		return true;
	}
	return false;
}

function show_lineup_warning(array $lineup_row, array $party): bool {
	if (!have_user()
	||	(	!have_admin('party')
		&&	CURRENTUSERID !== $party['USERID']
		)
	) {
		return true;
	}

	show_previous_performance_warning($lineup_row, $party);

	if (false === ($artistids = memcached_rowuse_hash(['artist','lineup','party'],'
		SELECT	ARTISTID, artist.COUNTRYID, LIVE_COUNTRYID, REALNAME, REALNAME_FULL, artist.NAME,
			(SELECT GROUP_CONCAT(GID) FROM artist_genre WHERE artist_genre.ARTISTID = artist.ARTISTID) AS GIDS,
			MAX(IF(party.PARTYID = '.$party['PARTYID'].', NULL, party.STAMP)) AS LSTAMP
		FROM artist
		LEFT JOIN lineup USING (ARTISTID)
		LEFT JOIN party USING (PARTYID)
		WHERE ARTISTID = '.$lineup_row['ARTISTID'].'
		OR (  FIND_IN_SET("'.$lineup_row['TYPE'].'", artist.TYPE)
		  AND DECEASED = 0
		  AND NOT STOPPED
		  AND FOLLOWUPID = 0
		  AND (party.PARTYID IS NULL OR STAMP > '.(TODAYSTAMP - 2 * ONEYEAR).')
		  AND '.where_search_title('artist.NAME_FOR_SEARCH', $lineup_row['NAME'], LIMIT_TO_VALUE | NAME_UTF8).'
		)
		GROUP BY ARTISTID',
		ONEHOUR
	))) {
		return false;
	}
	if (($cnt = count($artistids)) <= 1) {
		return true;
	}
	require_once '_genres.inc';
	$opts = [];
	$genres = get_genres();
	$dots = substr_count($lineup_row['NAME'], '.');
	foreach ($artistids as $artistid => $info) {
		extract($info);
		if ($dots > 1
		&&	mb_substr_count($NAME, '.') < ($dots - 1)
		) {
			# need similar amount of dots
			continue;
		}
		$gidstrs = [];
		if ($GIDS) {
			foreach (explode(',', $GIDS) as $gid) {
				$gidstrs[] = escape_utf8($genres[$gid]);
			}
			sort($gidstrs);
		}
		$is_chosen = $ARTISTID == $lineup_row['ARTISTID'];

		if ($last_performance = get_last_performance($ARTISTID, $party['STAMP'])) {
			list($y,$m) = _getdate($last_performance['STAMP']);
			$class = get_long_ago_class($party, $last_performance['STAMP']);
			$ym = ($class ? '<span class="'.$class.' nb">' : null).short_month_name($m).' '.$y.($class ? '</span>' : null);
		} else {
			$ym = null;
		}

		$opts[] =
			'<span class="'.($is_chosen ? 'notice-nb' : 'light8').'">'.
			str_replace('<a ','<a tabindex="-1" ',get_element_link('artist', $ARTISTID)).
			(	$ym
			?	' '.MIDDLE_DOT_ENTITY.' '.$ym
			:	null
			).(	$COUNTRYID
			?	' '.get_country_flag_for_artist($ARTISTID, $COUNTRYID, class: 'light')
			:	null
				).(	$LIVE_COUNTRYID && $LIVE_COUNTRYID != $COUNTRYID
			?	' &rarr; '.get_country_flag_for_artist($ARTISTID, $LIVE_COUNTRYID, class: 'light')
			:	null
			).(	($realname = escape_utf8((have_admin(['artist','party']) ? $REALNAME_FULL : '') ?: $REALNAME))
			?	' '.MIDDLE_DOT_ENTITY.' '.$realname
			:	null
			).
			(	$GIDS
			?	' <small>&middot; '.implode(', ',$gidstrs).'</small>'
			:	null
			).
			($is_chosen ? ' ('.__('status:chosen').') ' : null).
			'</span>';
	}
	?><div style="margin:0 1em" class="small light7"><?
	?><div>&rdsh; <?= __('party:warning:more_artists_identical_name_LINE',['CNT'=>$cnt]) ?></div><?
	?><ul><li><?= implode('</li><li>',$opts) ?></li></ul><?
	?></div><?
	return true;
}

function show_lineup_menu(int|array $partyarg): void {
	if (is_int($partyarg)) {
		$partyid = $partyarg;
		$party = memcached_party_and_stamp($partyid);
	} else {
		$party = $partyarg;
		$partyid = $party['PARTYID'];
	}

	include_js('js/form/finditem');	# needed?
	include_js('js/form/lineup');
	include_js('js/bubble');
	layout_open_menu();

	layout_open_menuitem();
	?><span id="changelineup" onclick="showFormParts(this,<?= $partyid ?>)" class="unhideanchor"><?= __C('action:change_lineup') ?></span><?

	layout_next_menuitem();
	include_js('js/form/growtofit');
	?><span id="processlineup" onclick="showProcessorForm(this,<?= $partyid ?>)" class="unhideanchor"><?= __C('action:process_lineup') ?></span><?

	layout_next_menuitem();
	?><a class="unhideanchor" href="/party/<?= $partyid ?>/lineupimages"><?= __C('action:update_images') ?></a><?

	layout_next_menuitem();
	require_once '_complete.inc';
	showMarkComplete($party);
	layout_close_menuitem();
	layout_close_menu();
}

function show_need_update(array $artist): void {
	if (empty($artist['NEEDUPDATE'])
	&&	!empty($artist['COUNTRYID'])
	) {
		return;
	}
	if (!empty($artist['NEEDUPDATE'])) {
		if (have_admin(['party','artist'])) {
			require_once '_needupdates.inc';
			show_new_profile_info('artist', $artist['ARTISTID']);
		}
	}
	if (empty($artist['COUNTRYID'])
	&&	empty($artist['GENERIC'])
	) {
		if (have_admin(['party','artist'])
		||	isset($party['USERID'])
		&&	$party['USERID'] === CURRENTUSERID
		) {
			?><span class="warning"> (<?= __('status:missing_origin') ?>)</span><?
		}
	}
}

function get_lineupid($table,$partyid,$artistid,$areaid,$type,$start_stamp,$duration,$used_lineupids = []) {
	$and_lineupid_not = $used_lineupids
	?	' AND ll.LINEUPID NOT IN ('.implodekeys(',',$used_lineupids).') '
	:	null;

	$partyidstr = $partyid;

	switch ($table) {
	case 'lineup_log':
		return db_single('lineup_log','
			SELECT ll.LINEUPID
			FROM lineup_log AS ll
			LEFT JOIN lineup USING (LINEUPID)
			WHERE ll.ARTISTID='.$artistid.'
			  AND ll.PARTYID IN ('.$partyidstr.')
			  AND ISNULL(lineup.LINEUPID)
			'.$and_lineupid_not.'
			ORDER BY
				ll.PARTYID	='.$partyid.' DESC,
				ll.AREAID	='.$areaid.' DESC,
				ll.TYPE		="'.addslashes($type).'" DESC,
				'.number_is_same('ll.START_STAMP',$start_stamp).' DESC,
				'.number_is_same('ll.DURATION',$duration).' DESC,
				ll.LINEUPID DESC
			LIMIT 1'
		);
	case 'lineup':
		return db_single('lineup','
			SELECT LINEUPID
			FROM lineup AS ll
			WHERE ARTISTID	='.$artistid.'
			  AND PARTYID IN ('.$partyidstr.')
			  AND AREAID	='.$areaid.'
			'.$and_lineupid_not.'
			ORDER BY
				PARTYID	='.$partyid.' DESC,
				ABS(CAST(AREAID AS SIGNED)-'.$areaid.') ASC,
			 	TYPE		="'.addslashes($type).'" DESC,
				'.number_is_same('ll.START_STAMP',$start_stamp).' DESC,
				'.number_is_same('ll.DURATION',$duration).' DESC
			LIMIT 1'
		);
	}
	return false;
}
function get_partyareaid(int $partyid, array $area, array $artists_in_area): int {

	# have current already?
	if (!empty($area['AREAID'])) {
		$partyareaid = db_single('partyarea','
			SELECT PARTYAREAID
			FROM partyarea
			WHERE PARTYID='.$partyid.'
			  AND AREAID='.$area['AREAID']
		);
		if ($partyareaid === false) {
			return false;
		}
		if ($partyareaid) {
			# just keep current partyareaid
			return $partyareaid;
		}
	}

	$partyareaid = 0;
	if ($artists_in_area) {
		$old_areas = db_same_hash('partyarea_log','
			SELECT DSTAMP,AREAID,ARTISTID
			FROM partyarea_log
			JOIN lineup_log USING (PARTYID,AREAID,DSTAMP)
			WHERE PARTYID='.$partyid.'
			ORDER BY DSTAMP DESC'
		);
		if ($old_areas === false) {
			return false;
		}
		if ($old_areas) {
			$mappings = get_area_mappings($old_areas,$artists_in_area,true);
			if ($mappings) {
				foreach ($mappings as $map_areaid => $info) {
					foreach ($info as $dstamp => $old_areaid) {
						$partyareaid = db_single('partyarea_log','
							SELECT pl.PARTYAREAID
							FROM partyarea_log pl
							LEFT JOIN partyarea USING (PARTYAREAID)
							WHERE ISNULL(partyarea.PARTYAREAID)
							  AND pl.PARTYID='.$partyid.'
							  AND pl.DSTAMP='.$dstamp.'
							  AND pl.AREAID='.$old_areaid.'
							ORDER BY pl.PARTYAREAID DESC
							LIMIT 1'
						);
						if ($partyareaid) {
							break 2;
						}
					}
				}
			}
		}
	}

	if (!$partyareaid
	&&	$area
	) {
		$partyareaid = db_single('partyarea_log','
			SELECT pl.PARTYAREAID
			FROM partyarea_log pl
			LEFT JOIN partyarea USING (PARTYAREAID)
			WHERE pl.PARTYID='.$partyid.'
			  AND ISNULL(partyarea.PARTYAREAID)
			ORDER BY
				LOWER(CONVERT(REPLACE(REPLACE(pl.NAME," ",""),"-","") USING latin1))="'.addslashes(strtolower($area['NAME'])).'" DESC,
				pl.GENRES="'.addslashes($area['GENRES']).'" DESC,
				pl.HOSTEDBY="'.addslashes($area['HOSTEDBY']).'" DESC,
				pl.HOSTEDBYID='.$area['HOSTEDBYID'].' DESC,
				pl.AREAID='.$area['AREAID'].' DESC,
				pl.PARTYAREAID DESC
			LIMIT 1'
		);
	}

	return $partyareaid ?: 0;
}
function get_partyareaid_for_single_artist($partyid,$artistid,$areaid,$type,$start_stamp,$duration) {
	return db_single(['lineup_log', 'lineup', 'partyarea', 'partyarea_log'],'
		SELECT pa.PARTYAREAID
		FROM lineup_log AS ll
		LEFT JOIN lineup USING (LINEUPID)
		LEFT JOIN partyarea_log AS pa ON pa.PARTYID = ll.PARTYID AND pa.AREAID = ll.AREAID
		LEFT JOIN partyarea USING (PARTYAREAID)
		WHERE ll.ARTISTID = '.$artistid.'
		  AND ll.PARTYID = '.$partyid.'
		  AND partyarea.PARTYAREAID IS NULL	/* partyarea may not exist currently */
		ORDER BY
			ll.AREAID	='.$areaid.' DESC,
			ll.TYPE		="'.addslashes($type).'" DESC,
			'.number_is_same('ll.START_STAMP',$start_stamp).' DESC,
			'.number_is_same('ll.DURATION',$duration).' DESC,
			pa.DSTAMP	=ll.DSTAMP DESC,
			pa.PARTYAREAID DESC
		LIMIT 1'
	);
}

function find_next_slot_same_area(array $lineup_rows, int $ndx): bool {
	$lineup_row = $lineup_rows[$ndx];
	extract($lineup_row);

	++$ndx;

	while (true) {
		if (!isset($lineup_rows[$ndx])) {
			# no slots left
			return false;
		}
		$lineup_row = $lineup_rows[$ndx];

		if ($AREAID != $lineup_row['AREAID']) {
			# next is not same area
			return false;
		}
		if ($TIME_START !== $lineup_row['TIME_START']
		||	$TIME_END   !== $lineup_row['TIME_END']
		) {
			# new slot, same area
			return true;
		}
		++$ndx;
	}
}

function inject_empty_areas($lineup_rows,$have_areas) {
	# inject empty areas if exist

	$have_empty_area = [];
	foreach ($have_areas as $area) {
		if (!$area['HAVE_LINEUP']) {
			$have_empty_area[$area['AREAID']] = $area;
		}
	}
	if (!$have_empty_area) {
		return $lineup_rows;
	}
	reset($have_empty_area);

	$empty_areaid = key($have_empty_area);
	$areaid = null;
	$newlineuprows = [];
	$get_empty_area_row = function($areaid, $area) {
		return [
			'EMPTY_AREA'		=> true,
			'AREAID'		=> $areaid,
			'ARTISTID'		=> 0,
			'TIME_START'		=> null,
			'TIME_END'		=> null,
			'START_STAMP'		=> null,
			'START_STAMP_UTC'	=> null,
			'DURATION'		=> 0,
			'ACCEPTED'		=> true,
			'DELETED'		=> false,
		] + $area;
	};
	foreach ($lineup_rows as $lineup_row) {
		if ($areaid === null
		||	$areaid != $lineup_row['AREAID']
		) {
			while (	$empty_areaid !== false
			&&	$lineup_row['AREAID'] > $empty_areaid
			) {
				$newlineuprows[] = $newx = $get_empty_area_row($empty_areaid,current($have_empty_area));
				$empty_area = next($have_empty_area);
				$empty_areaid = $empty_area ? $empty_area['AREAID'] : false;
			}
			$areaid = $lineup_row['AREAID'];
		}
		$newlineuprows[] = $lineup_row;
	}
	while ($empty_areaid !== false) {
		$newlineuprows[] = $newx = $get_empty_area_row($empty_areaid,current($have_empty_area));
		$empty_area = next($have_empty_area);
		$empty_areaid = $empty_area ? $empty_area['AREAID'] : false;
	}
	return $newlineuprows;
}
function get_stored_in_area($partyid) {
	return db_same_hash('lineup','
		SELECT AREAID,ARTISTID
		FROM lineup
		WHERE PARTYID='.$partyid.'
		ORDER BY AREAID ASC'
	);
}
function get_area_mappings($arg_in_area,$artists_in_area,$log = false) {
	$used_area =
	$new_to_old =
	$old_to_new = [];

	$besties = [];

	foreach ($artists_in_area as $areaid => $artistids) {
		if (!$log) {
			$arg_in_area = [0=>$arg_in_area];
		}
		foreach ($arg_in_area as $mstamp => $stored_in_area) {
			foreach ($stored_in_area as $old_areaid => $stored_artistids) {
				$same = array_intersect_key($artistids,$stored_artistids);
				$new = array_diff_key($artistids,$stored_artistids);
				$rem = array_diff_key($stored_artistids,$artistids);

				$value = count($same) - count($new) - count($rem);

				if (!isset($besties[$value][$mstamp])) {
					$besties[$value][$mstamp][] = [$old_areaid,$areaid];
				} else {
					$besties[$value][$mstamp] = [[$old_areaid,$areaid]];
				}
			}
		}
	}

	krsort($besties);

	if ($log) {
		$new_from_old = [];
		$todo = $artists_in_area;
		foreach ($besties as $value => $mstamps) {
			foreach ($mstamps as $mstamp => $combos) {
				foreach ($combos as $combo) {
					list($log_areaid,$new_areaid) = $combo;

					$new_from_old[$new_areaid] = [$mstamp,$log_areaid];

					unset($todo[$new_areaid]);

					if (!$todo) {
						break 3;
					}
				}
			}
		}
		return $new_from_old;
	} else {
		foreach ($besties as $value => $mstamps) {
			foreach ($mstamps[0] as $combos) {
				if ($value < 0) {
					# bad value, to little similarities
					continue;
				}
				foreach ($combos as $combo) {
					list($old_areaid,$new_areaid) = $combo;

					if (!isset($old_to_new[$old_areaid])) {
						$new_to_old[$new_areaid] = $old_areaid;
						$old_to_new[$old_areaid] = $new_areaid;
					}
				}
			}
		}
		return [$old_to_new,$new_to_old];
	}
}

function show_time_field(string $name, ?string $value = null, ?string $class = null, ?string $placeholder = null, bool $disabled = false): void {
	?><input<?
	if ($disabled) {
		?> disabled="disabled"<?
	}
	if ($placeholder) {
		?> placeholder="<?= $placeholder ?>"<?
	}
	if ($value !== null) {
		?> data-stored="<?= $value ?>"<?
	}
	?> onkeyup="needSave(this)"<?
	?> onblur="cleanTime(this)"<?
	?> onchange="Pf.changeTime(this)"<?
	?> class="center time<?= $class ? ' '.$class : null ?>"<?
	?> type="number"<?
	?> pattern="[0\s]*(?:(?:\d|1\d|2[0123])[0-5]\d|\d|[1-5]\d|2[0123])\s*"<?
	?> data-valid="number"<?
	?> min="0"<?
	?> max="2400"<?
	?> name="<?= $name ?>"<?
	?> value="<?
	if ($value !== null) {
		echo str_pad($value,4,0,STR_PAD_LEFT);
	}
	?>"><?
}

function show_livestream_field(string $name, ?array $row = null): void {
	?><input<?
	?> name="<?= $name ?>"<?
	?> type="url"<?
	?> data-stored="<?= $val = escape_utf8($row['LIVESTREAM_ADDRESS']) ?>"<?
	?> value="<?= $val ?>"<?
	?> placeholder="<?= element_name('livestream') ?>"<?
	?>><?
}

function show_day_field(array|int $party, string $name, ?array $row = null, ?bool $disabled = null): void {
	if (is_scalar($party)) {
		$party = memcached_party_and_stamp($party);
	}

	if (($party['DURATION_SECS'] ?: (6 * ONEHOUR)) < 24 * ONEHOUR) {
		if (!empty($row['START_DATE'])) {
			[,,,,,, $weekday] = $row['START_DATE'];
			?><tt><?
			echo short_weekday_name($weekday);
			?></tt><?
		}
		return;
	}

	if (!empty($row['START_DATE'])) {
		# get datepart from START field
		list($y,$m,$d) = $row['START_DATE'];
		$value = sprintf('%04d-%02d-%02d',$y,$m,$d);
	} else {
		$value = null;
	}
	$end_stamp = $party['STAMP'] + ($party['DURATION_SECS'] ?: (6*ONEHOUR));
	$cur_stamp = $party['STAMP'];

	while ($cur_stamp < $end_stamp) {
		list($year,$month,$mday,,,,$weekday) = _getdate($cur_stamp);
		$days[sprintf('%04d-%02d-%02d',$year,$month,$mday)] = $weekday;
		$cur_stamp = strtotime('+1 day 00:00',$cur_stamp);
	}

	?><select<?
	if ($disabled) {
		?> disabled="disabled"<?
	}
	?> name="<?= $name ?>"<?
	?> data-stored="<?= $value ?>"<?
	?> onchange="needSave(this)"<?
	?>><?
	?><option></option><?

	$weekdaycnt = [];

	foreach ($days as $date => $weekday) {
		?><option<?
		if ($date == $value) {
			?> selected<?
		}
		?> value="<?= $date ?>"><?
		echo (!$weekday ? 7 : $weekday) ?>.<?
		echo short_weekday_name($weekday);

		$prev = incorset($weekdaycnt, $weekday);

		if ($prev) {
			?> <?= MIDDLE_DOT_ENTITY ?> <?
			echo $prev + 1;
		}

		?></option><?
	}
	?></select><?
}

function require_lineup_time(array $arr, string $field): bool {
	if (!empty($arr[$field])
	&&	false === is_valid_lineup_time($arr[$field])
	) {
		register_error('require_lineup_time:error:invalid_time_LINE', ['NAME' => $field, 'VALUE' => $arr[$field]]);
		return false;
	}
	return true;
}

function is_valid_lineup_time(string $value): int|false|null {
	$value = utf8_mytrim($value);
	if (!mb_strlen($value)) {
		return null;
	}
	return	preg_match('"^\s*(0*(?:(?:\d|1\d|2[0123])[0-5]\d|\d|[1-5]\d|2[0123]))\s*$"u', $value, $match)
	?	(int)$match[1]
	:	false;
}

function require_lineup_time_array_of_size(array $arr, string $field, int &$size = 0): bool {
	if (!require_array_of_size($_POST,$field,$size)) {
		return false;
	}
	foreach ($_POST[$field] as $time) {
		if (!empty($time)
		&&	false === is_valid_lineup_time($time)
		) {
			register_error('require_lineup_time:error:invalid_time_LINE', ['NAME' => $field, 'VALUE' => $time]);
			return false;
		}
	}
	return true;
}

function check_storearea($storearea) {
	if (empty($storearea)) {
		return;
	}
	foreach ($storearea as $area_values) {
		$area_values = trim($area_values,'()');
		list($partyareaid,$partyid,$areaid) = explode(',',$area_values);
		if (!$partyareaid) {
			$new_areaids[$areaid] = $areaid;
		}
	}
	if (empty($new_areaids)) {
		return;
	}
	$already_have = db_simpler_array('partyarea','
		SELECT AREAID
		FROM partyarea
		WHERE PARTYID='.$partyid.'
		  AND AREAID IN ('.implode(',',$new_areaids).')'
	);
	if ($already_have === false) {
		bail(500);
	}
	if (!$already_have) {
		return;
	}
	?><div class="error block">DUBBEL AREA BUG: Waarschuw <a href="/user/2269">t0mm1e</a></div><?
	bail(500);
}

function show_empty_spaces(array $party, ?array $lineup_row = null, &$prev_time, int $colspan, ?array &$errors = null): ?string {
	if (empty($lineup_row['START_STAMP'])) {
		return null;
	}
	$party_admin = have_admin('party');

	[$prev_stamp, $prev_duration] = $prev_time;

	if ($lineup_row
	&&	(	$prev_stamp 	=== $lineup_row['START_STAMP']
		&&	$prev_duration	=== $lineup_row['DURATION']
		#	^^ multi-artist slot
		)
	) {
		return null;
	}

	static $__event_end_stamp = 0;
	if ($__event_end_stamp === 0) {
		$__event_end_stamp = $party['STAMP'] + ($party['DURATION_SECS'] ?: 6 * ONEHOUR);
	}

	$prev_end_stamp = $prev_stamp + $prev_duration;

	$rc = null;

	if ($lineup_row) {
		$use_start_stamp = $lineup_row['START_STAMP'];
	} else {
		# check for empty space at end of event
		$use_start_stamp = $__event_end_stamp;
	}

	if ($prev_end_stamp !== $use_start_stamp) {
		if (!$party_admin) {
			if ($prev_end_stamp !== $party['STAMP']
			&&	$use_start_stamp !== $party['STAMP'] + $party['DURATION_SECS']
			) {
				?><tr class="nohl empty"><td colspan="<?= $colspan ?>"><?
				?></td><?
				?></tr><?
			}
		} else {
			$type = 'timeless';
			$str = null;

			if ($prev_end_stamp > $use_start_stamp) {
				$type = 'overlap';
				$rc = ' overlap-inner';
				if ($prev_end_stamp > $__event_end_stamp) {
					$str = 'timetable:warning:event_ends_early';
				} elseif ($use_start_stamp < $party['STAMP']) {
					$str = 'timetable:warning:event_starts_too_late';
				} else {
					$str = 'timetable:warning:overlap';
				}
			}

			?><tr class="nohl <?= $type ?>"><td colspan="<?= $colspan ?>"><?
			?><div><?
			if ($type !== 'overlap') {
				?><span class="light times"><?
				[,,, $prev_hour, $prev_mins,, $prev_weekday] = _getdate($prev_end_stamp);
				[,,, $curr_hour, $curr_mins,, $curr_weekday] = _getdate($use_start_stamp);
				?><span class="weekday"><?= short_weekday_name($prev_weekday) ?></span><?
				?> <?
				printf('%02d:%02d', $prev_hour, $prev_mins);
				?> &ndash; <?
				if ($curr_weekday !== $prev_weekday) {
					?><span class="weekday"><?= short_weekday_name($curr_weekday) ?></span><?
					?> <?
				}
				printf('%02d:%02d',$curr_hour,$curr_mins);
				?></span><?
			}
			if ($str) {
				?><b><?= $errstr = __($str) ?></b><?
				$errors[$errstr] =  element_name('timetable').': '.$errstr;
			}
			?></div><?
			?></td><?
			?></tr><?
		}
	}
	$prev_time = [$use_start_stamp,$lineup_row['DURATION'] ?? null,false,$lineup_row['AREAID'] ?? null];
	return $rc;
}

function get_long_ago_class(array $party, int $lstamp): string {
	$diff = $party['STAMP'] - $lstamp;
	return	$diff >= 4 * ONEYEAR
	?	'error'
	:	(	$diff >= 2 * ONEYEAR
		? 	'warning'
		:	''
		);
}

function show_master_genres($artist_genrestr) {
	if (!$artist_genrestr) {
		return;
	}
	static $__genre_to_master = 0;
	if ($__genre_to_master === 0) {
		$__genre_to_master = db_simple_hash('genre_to_master','SELECT GID,NAME FROM genre_to_master JOIN master_genre USING (MGID)');
	}
	$mgids = [];
	foreach (explode(',',$artist_genrestr) as $gid) {
		$master_name = getifset($__genre_to_master,$gid);
		if (!$master_name) {
			continue;
		}
		$mgids[$master_name] = escape_specials($master_name);
	}
	?> <?
	?><span class="small master-genre"><?= implode(', ',$mgids) ?></span><?
}

function get_start_and_duration(
	array	$party,
		$time_start,
		$time_end,
		$date_start = 0
): array {
	# input in UTC times

	if (!$time_start
	&&	!$time_end
	) {
		return [null, null, null, null, null];
	}

	# NOTE: make sure TIMEZONE is correct!

	$hours_start = floor($time_start / 100);
	$mins_start = $time_start - 100 * $hours_start;

	change_timezone('UTC');

	if ($party['DURATION_SECS'] <= ONEDAY) {
		[,,, $hour, $mins] = _getdate($party['STAMP_TZI']);

		$event_time_start = 100 * $hour + $mins;
		$time_start_str = $hours_start.':'.$mins_start;

		$start_stamp_utc = strtotime((($time_start < $event_time_start) ? '+1 day' : '').$time_start_str, $party['STAMP_TZI']);

		[$y, $m, $d, $hour, $mins] = _getdate($start_stamp_utc);

		$date_start = sprintf('%04d-%02d-%02d', $y, $m, $d);
	}

	$start_date = $date_start.' '.sprintf('%02d:%02d:00', $hours_start, $mins_start);
	$start_stamp_utc = strtotime($start_date);

	change_timezone();

	if ($time_end === null
	||	$time_start < 1000
	&&	!$time_end
	) {
		$duration = null;
	} else {
		$time_end_for_duration = $time_end < $time_start ? 2400 + $time_end : $time_end;
		$hours_end = floor($time_end_for_duration / 100);
		$mins_end = $time_end_for_duration - 100 * $hours_end;
		$start_secs = $hours_start * ONEHOUR + $mins_start * ONEMINUTE;
		$end_secs   = $hours_end   * ONEHOUR + $mins_end   * ONEMINUTE;
		$duration   = $end_secs - $start_secs;
		$end_stamp = $start_stamp_utc + $duration;
	}

	# convert UTC to proper timezone

	$offset = $party['STAMP'] - $party['STAMP_TZI'];

	$start_stamp = $start_stamp_utc + $offset;

	$start	= _getdate($start_stamp);
	$end	= _getdate($start_stamp + $duration);

	$dst 	 = $start[7];
	$end_dst =   $end[7];

	return [(int)$start_stamp,
			(int)$start_stamp_utc,
			(int)$duration,
			(bool)$dst,
			(bool)$end_dst
	];
}

function fill_lineup(?array &$lineup_row = null): void {
	if (!$lineup_row) {
		mail_log('empty lineuprow in fill_lineup', include_trace: true);
		return;
	}
	if (!empty($lineup_row['EMPTY_AREA'])) {
		return;
	}
	if (!empty($lineup_row['START_STAMP'])) {
		$lineup_row['START_DATE'] = _getdate($lineup_row['START_STAMP']);

		[$y, $m, $d, $hour, $mins] = _getdate($lineup_row['START_STAMP']);
		$lineup_row['TIME_START'] = $hour * 100 + $mins;

		if ($lineup_row['END_STAMP'] =
			$lineup_row['DURATION']
		?	$lineup_row['START_STAMP'] + $lineup_row['DURATION']
		:	null
		) {
			[$y, $m, $d, $hour, $mins] = _getdate($lineup_row['END_STAMP']);
			$lineup_row['TIME_END'] = $hour * 100 + $mins;
		} else {
			$lineup_row['TIME_END'] = null;
		}

		change_timezone('UTC');
		[$y, $m, $d, $hour, $mins] = _getdate($lineup_row['START_STAMP_UTC']);
		$lineup_row['TIME_START_TZI'] = $hour * 100 + $mins;

		if ($lineup_row['END_STAMP']) {
			list($y,$m,$d,$hour,$mins) = _getdate($lineup_row['START_STAMP_UTC'] + $lineup_row['DURATION']);
			$lineup_row['TIME_END_TZI'] = $hour * 100 + $mins;
		} else {
			$lineup_row['TIME_END_TZI'] = null;
		}
		change_timezone();
	} else {
		$lineup_row['START_STAMP'] =
		$lineup_row['END_STAMP'] =
		$lineup_row['DURATION'] =
		$lineup_row['TIME_START'] =
		$lineup_row['TIME_START_TZI'] =
		$lineup_row['TIME_END'] =
		$lineup_row['TIME_END_TZI'] = null;
	}
}

function partylist_needs_refresh_if_in_future(int $partyid): void {
	$party = memcached_party_and_stamp($partyid);
	if (!$party
	||	($party['STAMP'] + $party['DURATION_SECS']) < (CURRENTSTAMP - 2 * ONEDAY)
	) {
		return;
	}
	if ($party['LOCATIONID']) {
		partylist_needs_refresh('location',$party['LOCATIONID']);
	}
	if ($party['ORGIDS']) {
		partylist_needs_refresh('organization',explode(',',$party['ORGIDS']));
	}
}

function count_characters(string $input): array {
	$regular_characters = $irregular_characters = 0;
	if (preg_match_all('"[\w\d\&\-_\s]+"u', $input, $matches)) {
		foreach ($matches[0] as $str) {
			$regular_characters += mb_strlen($str);
		}
	}
	if (preg_match_all('"['.IRREGULAR_LETTERS_RANGE.'\s\n]+"u', $input, $matches)) {
		foreach ($matches[0] as $str) {
			$irregular_characters += mb_strlen($str);
		}
	}
	return ['regular_characters'	=> $regular_characters,
			'irregular_characters'	=> $irregular_characters
	];
}

function detect_almost_complete_timetable(int $partyid): void {
	if (($timed = db_single_assoc('lineup','
		SELECT COUNT(IF(START_STAMP, 1, NULL)) / COUNT(*) AS PART, COUNT(*) AS TOTAL
		FROM lineup
		WHERE PARTYID = '.$partyid.'
		  AND TYPE != "mc"'))
	&&	$timed['PART'] >= .9
	&&	$timed['TOTAL'] >= 10
	) {
		header('X-MARK-COMPLETE: '.__('lineup:notice:marking_complete_LINE'));
	}
}
