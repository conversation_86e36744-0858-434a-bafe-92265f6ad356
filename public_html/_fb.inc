<?php

require_once 'vendor/autoload.php';

use Facebook\WebDriver\Remote\RemoteWebDriver;

require_once '_require.inc';
require_once '_helper.inc';

const DO_FBID_SYSLOG	= CLI;
const DO_FBID_ERR_LOG	= HOME_THOMAS;

function local_log(int $level, string $log_str): void {
	if (DO_FBID_SYSLOG) {
		syslog($level, $log_str);
	}
	if (DO_FBID_ERR_LOG) {
		error_log($log_str);
	}
}

function show_facebook_presence_check() {
	if (!require_post()
	||	!require_element($_POST, 'ELEMENT', ['party', 'artist', 'location', 'organization', 'stream'], true)
	||	false === require_number($_POST, 'ID')
	||	!require_array($_POST, 'LINKS')
	) {
		bail(400);
	}
	require_once '_currentuser.inc';
	global $currentuser;
	$currentuser = new _currentuser(TINY_USER | SKIP_SETTINGS);
	if (!have_admin()) {
		bail(401);
	}
	require_once '_url.inc';
	header('Content-Type: text/html; charset=windows-1252');
	$do_links = [];
	$channel = null;
	ob_start();
	foreach ($_POST['LINKS'] as $link) {
		$link = cleanup_url($link);
		if (preg_match('"^https?://(?:www\.)?facebook\.com/events/(?P<eventid>\d+)(?:/?\?event_time_id=|/)(?P<instanceid>\d+)"', $link, $match)) {
			if ($match['eventid']) {
				$do_links[$fbid = $match['eventid']] = $fbid;
			}
			if (!empty($match['instanceid'])) {
				$do_links[$fbid = $match['instanceid']] = $fbid;
			}

		} elseif (
			preg_match('"^https?://(?:\w+\.)?facebook\.com/pages/[^/]+/(?P<fbid>\d+)$"u', $link, $match)
		||	preg_match('"^https?://(?:\w+\.)?facebook\.com/profile\.php\?id=(?P<fbid>\d+)"u', $link, $match)
		||	preg_match('"^https?://(?:\w+\.)?facebook\.com/(?P<fbid>\d+)/?$"u', $link, $match)
		) {
			$do_links[$fbid = $match['fbid']] = $fbid;
		} elseif ($fbid = get_fbid_from_link($link, webdriver: $channel)) {
			$do_links[$fbid] = $fbid;
		} else {
			$do_links[$link] = true;
		}
	}
	$outputs = [];
	foreach ($do_links as $link_or_fbid => $fbid) {
		$link = is_int($link_or_fbid) ? 'https://www.facebook.com/'.$link_or_fbid : $link_or_fbid;

		if ($fbid === true) {
			?><div class="block"><?
			?><div class="warning-nb"><?= __('facebook:info:try_dragging_user_page') ?>:</div><?
			?><a href="<?= $escaped_link = escape_utf8($link) ?>"><?= $escaped_link ?></a><?
			?></div><?
			$outputs[1][] = ob_get_clean();
			continue;
		}
		if (!($infos = db_rowuse_array('fbid', "
			SELECT	ELEMENT, ID,
					(	SELECT 1
						FROM connect
						WHERE MAINTYPE = '{$_POST['ELEMENT']}'
						  AND MAINID = {$_POST['ID']}
						  AND ASSOCTYPE = fbid.ELEMENT
						  AND ASSOCID = fbid.ID
					) AS CONNECTED_TO_CURRENT,
					ELEMENT = 'party' AND NOT ISNULL((SELECT 1 FROM feedevent_multiday WHERE MAINID		= FBID LIMIT 1)) AS IS_MAIN,
					ELEMENT = 'party' AND NOT ISNULL((SELECT 1 FROM feedevent_multiday WHERE INSTANCEID	= FBID LIMIT 1)) AS IS_INSTANCE
			FROM fbid
			WHERE ELEMENT NOT IN ('presence', 'user')
			  AND NOT (ELEMENT = '{$_POST['ELEMENT']}' AND ID = {$_POST['ID']})
			  AND FBID = $fbid"))
		) {
			if ($infos === false) {
				bail(500);
			}
			continue;
		}
		$warns	= 0;
		$oks	= 0;
		$total	= 0;
		ob_start();
		foreach ($infos as $info) {
			/** @noinspection PhpRedundantOptionalArgumentInspection */
			extract($info, \EXTR_OVERWRITE);
			?><div<?
			if ($IS_MAIN) {
				?> class="light7"<?
			}
			?>><?
			?><span class="light"><?= element_name($ELEMENT) ?>:</span> <?
			if ($ELEMENT === 'party'
			&&	($party = memcached_party_and_stamp($ID))
			) {
				$party['TIMEZONE'] ? change_timezone($party['TIMEZONE']) : null;
				_datetime_display_numeric($party['STAMP'],null,false,true);
				$party['TIMEZONE'] ? change_timezone() : null;
				?>: <?
			}
			?><strong><?= get_element_link($ELEMENT,$ID) ?></strong><?
			$parts = [];
			++$total;
			if ($IS_MAIN) {
				++$oks;
				$parts[] = '<span class="notice">'.__('party:whole').'</span>';
			} elseif ($IS_INSTANCE) {
				++$warns;
				$parts[] = '<span class="warning">'.__('party:part').'</span>';
			}
			if ($CONNECTED_TO_CURRENT) {
				if (!$IS_MAIN) {
					++$oks;
				}
				$parts[] = __('attrib:connected');
			}
			if ($parts) {
				?> <small>(<?= implode(', ',$parts) ?>)</small><?
			}
			?></div><?
		}
		$severity = $warns ? 2 : ($oks !== $total ? 1 : 0);
		$data = ob_get_clean();
		ob_start();
		?><div class="<?= $warns ? 'warning' : ($oks >= $total ? 'notice-nb' : 'warning-nb') ?>"><?=
			__C('facebook:info:already_connected_to_same_page')
		?>: <em><a target="_blank" href="<?= escape_utf8($link) ?>"><?= escape_utf8($fbid) ?></a></em><?	// NOSONAR
		?></div><?
		$outputs[$severity][] = ob_get_clean().$data;
	}
	rsort($outputs);
	ob_start();
	foreach ($outputs as /* $severity => */ $stuffs) {
		foreach ($stuffs as $stuff) {
			echo $stuff;
		}
	}
	# error_log('show_facebook_presence_check data length: '.ob_get_length().' @ '.$_SERVER['REQUEST_URI']);
	ob_end_flush();
	bail(200);
}

function get_fbid_from_link(
	string				&$link,
	?string				&$get_type   = null,
	?RemoteWebDriver     $webdriver  = null,
): int|false|null {

	local_log(LOG_INFO, 'get_fbid_from link: '.$link);

	static $__link_to_fbid = [];
	if (array_key_exists($link, $__link_to_fbid)) {
		return $__link_to_fbid[$link];
	}

	if ($stored = memcached_get($store_key = 'FB_getid:'.$link)) {
		[$fbid, $get_type] = $stored;
		return $fbid;
	}
	$fbid = actual_get_fbid_from_link($link, $get_type, $webdriver);

	local_log(LOG_INFO, 'actual_get_fbid_from_link got fbid '.$fbid);

	memcached_set($store_key, [$fbid, $get_type], ONE_MINUTE);

	return $fbid;
}

function actual_get_fbid_from_link(
	string			 &$link,
	?string			 &$get_type   = null,
	?RemoteWebDriver  $webdriver  = null,
): int|false|null {
	require_once 'defines/facebook.inc';
#	if (!FACEBOOK_ENABLED) {
		if (!preg_match('"^https?://(?:www|m)\.facebook\.com(?<path>/.*)$"', $link, $match)) {
			local_log(LOG_INFO, 'facebook link not recognize');
			return null;
		}
		$path = $match['path'];
		if (preg_match('"^/events/(?<fbid>\d+)$"', $path, $match)) {
			$get_type = 'event';
			return (int)$match['fbid'];
		}
		if (preg_match('"^/pages/[^/]+/(?<fbid>\d+)$"', $path, $match)) {
			$get_type = 'page';
			return (int)$match['fbid'];
		}
		if (preg_match('"facebook\.com/groups/(?<fbid>\d+)"', $link, $match)) {
			$get_type = 'group';
			return (int)$match['fbid'];
		}
		if (preg_match('"^/profile\.php\?id=(?<fbid>\d+)"', $path, $match)) {
			local_log(LOG_INFO, 'got type user through profile.php '.$match['fbid']);
			$get_type = 'user';
			return (int)$match['fbid'];
		}
		if (preg_match('"^/(?<fbid>\d+)$"', $path, $match)) {
			return (int)$match['fbid'];
		}
		$data = get_facebook_data($webdriver, $path, syslog: true);

		local_log(LOG_INFO, 'got '.strlen($data).' bytes from facebook');

		if (empty($data)) {
			return null;
		}
		if (preg_match('!delegate_page_id":"(?<fbid>\d+)"!', $data, $match)) {
			$get_type = 'page';
			return (int)$match['fbid'];
		}
		if ($fbid = get_fbid_from_facebook_html($path, $data,get_type: $get_type)) {
			return $fbid;
		}
		$get_type = '';
		return null;

		#}

///////////////////////////////////////////////////  old Facebook queries when we were allowed to query API:
/*
	if (preg_match('"facebook\.com/login/\?next=https%3A%2F%2Fwww\.facebook\.com%2Fprofile\.php%3Fid%3D(?P<fbid>\d+)"', $link, $match)) {
		$link = 'https://www.facebook.com/profile.php?id='.$match['fbid'];
	}

	if (preg_match('"facebook\.com/groups/(?P<fbid>\d+)"', $link, $match)
	||	preg_match('"facebook\.com/people/[^/]*?/(?<fbid>\d+)"', $link, $match)
	) {
		return (int)$match['fbid'];
	}

	if (preg_match('"facebook\.com/search/"', $link)
	||	!preg_match('"facebook\.com/(?:(?:pg|pages)/)?(?P<desc>.*?)(?:\?|/|$)"ui', $link, $match)
	) {
		return null;
	}

	$desc = $match['desc'];

	if (preg_match('"[\-/](?P<fbid>\d{5,})\b"', $link, $match)) {
		$desc = $match['fbid'];
	}

	if (preg_match('"facebook\.com/profile\.php\?id=(?P<fbid>\d+)$"i', $link, $match)) {
		$get_type = 'facebook';
		$desc = $match['fbid'];
	}


	retry_with_new_token:

	static $access_token = null;
	if ($access_token === null) {
		require_once 'defines/facebook.inc';

		$access_token = db_single(['facebook_token', 'facebook', 'rights'], '
			SELECT DISTINCT ACCESSTOKEN
			FROM facebook_token
			JOIN facebook USING (FACEBOOKID)
			JOIN rights USING (USERID)
			WHERE EXPIRES>'.CURRENTSTAMP.'
			  AND APPID='.FACEBOOK_LIVE_APPID.'
			ORDER BY
				EXPIRES - '.CURRENTSTAMP.' DESC		/ prefer new tokens /
				USERID = '.CURRENTUSERID.' DESC		/ prefer personal token /
				`PORTION` = "party" DESC		/ prefer party-admin token /
				`PORTION` = "artist" DESC'		/ prefer artist-admin token /
			LIMIT 1'
		);
		if ($access_token === false) {
			memcached_set($store_key, null, ONE_MINUTE);
			return null;
		}
		if (!$access_token) {
			mail_log('Facebook access_tokens for lookup exhausted in get_fbid_from_link("'.$link.'", "", ...', subject: 'Facebook tokens for lookup exhausted');
			memcached_set($store_key, null, ONE_MINUTE);
			return null;
		}
	}
	$data = safe_file_get_contents($r =
		'https://graph.facebook.com/'.urlencode($desc).
		'?fields=id,link,metadata.fields(type)&metadata=1&access_token='.$access_token.'&appsecret_proof='.appsecret_proof($access_token,FACEBOOK_LIVE_APPID),
		info: $info
	);

	if (FACEBOOK_LOG_GRAPH_QUERIES) {
		db_insert('graph_query','INSERT INTO graph_query SET Q="'.addslashes($r).'",STAMP='.CURRENTSTAMP);
	}

	if (!$data
	||	!($obj = safe_json_decode($data))
	||	!property_exists($obj, 'metadata')
	||	!property_exists($obj, 'id')
	||	!property_exists($obj->metadata, 'type')
	||	(	$obj->metadata->type === 'user'	# no link for users is ok
		?	false
		:	!property_exists($obj, 'link')
		)
	) {
		require_once '_fbsession.inc';
		$log_error = true;
		if ($data
		&&	$obj
		&&	property_exists($obj, 'error')
		&&	property_exists($obj->error, 'code')
		&&	property_exists($obj->error, 'error_subcode')
		) {
			if ($obj->error->code === 803	# Cannot query users by their username
			||	$obj->error->code === 100	# Generic failure: Unsupported get request. Object with ID '<$link>' does not exist
			) {
				# ignore these errors, cannot find user
				$is_user_page = true;
				$log_error = false;
			} elseif (
				$access_token
			&&	token_need_renew($obj->error->code, $obj->error->error_subcode)
			) {
				fb_expire_token($access_token);
				$access_token = null;
				goto retry_with_new_token;
			}
		}

		if ($log_error) {
			ob_start();
			echo	"could not find id for $desc\n",
				"http status: ", $info['http_code'], "\n",
				"full url: $link\n",
				"type: $get_type\n",
				"graph request: $r\n",
				"data:\n";
				print_r($data);
			mail_log(ob_get_clean(), subject: 'get_fbid_from_link failed');
		}
		return false;
	}

	$get_type = getifset($obj, 'metadata', 'type') ?: '';

	if (!property_exists($obj, 'link')) {
		# user
		$fbid = (int)$obj->id;
		memcached_set($store_key, $fbid, TEN_MINUTES);
		return $fbid;
	}

	$link = $obj->link;
	$add_link = $obj->link;
	if (preg_match('"^https://www.facebook.com/(?P<path>.*?)/$"ui', $add_link, $match)) {
		$add_link = $match['path'];
	}
	$fbid = (int)$obj->id;

	if (!db_delete('facebook_link','
		DELETE FROM facebook_link
		WHERE FBID!='.($fbid_s = addslashes($fbid)).'
		  AND LINK="'.($link_s = addslashes($add_link)).'"')
	||	!db_replace('facebook_link',$q = '
		REPLACE INTO facebook_link SET
			TYPE	="'.addslashes($get_type).'",
			LINK	="'.$link_s.'",
			FBID	='.$fbid_s.',
			STAMP	='.CURRENTSTAMP)
	) {
		return false;
	}
	return $fbid;*/
}

function get_facebook_data(
	?RemoteWebDriver &$webdriver,
	string			  $path,
	bool			  $syslog		= false,
	?bool			  $force_login 	= null,
): string|false {
	try {
		if (!$webdriver) {
			static $__webdriver = null;
			if (!$__webdriver) {
				require_once '_webdriver.inc';
				$__webdriver = get_webdriver();
				# $__webdriver = get_webdriver('socks5://127.0.0.1:9050');
			}
			if (!($webdriver = $__webdriver)) {
				return false;
			}
		}
		require_once 'ipresolve.inc';
		require_once 'defines/scrape_cookie.inc';
		require_once 'defines/facebook_limit.inc';
		register_facebook_hit($path);
		global $__cookie;
		if ($checkok = facebook_fetch_check()) {
			# fetch_check sets the cookie
			if (!$__cookie) {
				return false;
			}
		} else {
			$users = may_hit_facebook(syslog: $syslog);
			if ($force_login) {
				$users = 'ablas';
#				$users = 'thomas';
			}
			if (!initialize_cookie($users)) {
				return false;
			}
		}

		if (str_starts_with($path, '/pg/')) {
			$path = substr($path, 3);
			local_log(LOG_INFO, 'checking path '.$path);
		}

		static $__current_cookie = false;

		if ($checkok) {
			if ($__current_cookie !== $__cookie['owner']) {
				if ($__current_cookie) {
					$syslog && local_log(LOG_INFO, 'cleanup cookie '.$__current_cookie);
					$webdriver->manage()->deleteAllCookies();
				}

				$__current_cookie = $__cookie['owner'];

				$syslog && local_log(LOG_INFO, 'set cookie to '.($__current_cookie ?: 'anonymous'));

				if ($__current_cookie) {
					set_facebook_cookie($webdriver, $__cookie['data']);
				}
			}

			$url = 'https://www.facebook.com'.$path;
			$syslog && local_log(LOG_INFO, 'facebook webdriver: getting '.$url);
			$syslog && local_log(LOG_INFO, 'webdriver '.($__current_cookie ? 'with '.$__current_cookie.' cookie' : 'without cookie').': getting '.$url);
			$webdriver->get($url);

			$data = $webdriver->getPageSource();

			# $syslog && local_log(LOG_DEBUG, 'making screenshot of '.$url);
			# static $i = 1;
			# file_put_contents('facebook_dump_'.$i.'.html', $data);
			# $webdriver->takeScreenshot('/tmpdisk/facebook_screenshsot_'.$i.'.png');
			# ++$i;

		} else {
			if (!$force_login
			&&	$__current_cookie
			) {
				$syslog && local_log(LOG_INFO, 'cleanup cookie '.$__current_cookie);
				$webdriver->manage()->deleteAllCookies();
				$__current_cookie = null;
			}

			$done_jump = false;

			retry_get:

			$syslog && local_log(LOG_INFO, 'using cookie '.($__current_cookie ?: 'anonymous'));

			$url = 'https://www.facebook.com'.$path;
			$syslog && local_log(LOG_INFO, 'webdriver '.($__current_cookie ? 'with '.$__current_cookie.' cookie' : 'without cookie').': getting '.$url);
			$webdriver->get($url);

			$data = $webdriver->getPageSource();

			if (!$done_jump
			&&	$__current_cookie !== $__cookie['owner']
			&&	(	$force_login
				||	detect_facebook_login_page($data)
				)
			) {
				if ($__current_cookie) {
					$syslog && local_log(LOG_INFO, 'see bounce to login page as not found');
					return $data;
				}

				$syslog && local_log(LOG_INFO, 'current cookie is not the one selected, and got login header');

				$__current_cookie = $__cookie['owner'];

				set_facebook_cookie($webdriver, $__cookie['data'] ?? '');

				$done_jump = true;

				goto retry_get;
			}
		}

		$current_url = $webdriver->getCurrentURL();

		if (str_starts_with($current_url, '/login')) {
			mail_log('get_facebook_data went to /login', item: get_defined_vars());
			return false;
		}
		if (str_contains($data, 'Je verzoek kon niet worden verwerkt')) {
			mail_log('get_facebook_data got denied request', item: get_defined_vars());
			return false;
		}
	} catch (Exception $exception) {
		mail_log($error = 'some exception happened in get_facebook_data: '.$exception->getMessage());
		error_log($error);
		return false;
	}

	return $data;
}

function get_fbid_from_facebook_html(
	string   $path,
	string   $data,
	?int	 $presenceid		= null,
	?string &$get_type			= null,
	?string  $found_link		= null,
	?int	&$found_http_status = null,
	?int	&$page_id			= null,
	bool	 $syslog 			= true,
	array	&$fbids				= [],	# all encountered fbids that point to this page
): int|false {
	if (preg_match('!<title(?:\s*id="pageTitle")?>(?P<title>.*?)</title>!ui', $data, $match)) {
		$title = $match['title'];
		if (detect_facebook_login_page($data)) {
			# see bounce to login as not found
			$syslog && local_log(LOG_WARNING, 'bounced to login page according to title');
			if (CLI && SERVER_VIP) {
				$fin = fopen('php://stdin', 'rb');
				while (true) {
					echo 'mark as bad? ';
					$line = strtolower(trim(fgets($fin)));
					switch ($line) {
					case 'y';
					case 'yes':
						$found_http_status = 404;
						break 2;

					case 'n':
					case 'no':
						$found_http_status = 200;
						break 2;
					}
				}
				fclose($fin);
				return 0;
			}
			return false;
		}
		if ($title === 'Messenger') {
			$syslog && local_log(LOG_INFO, 'Messenger link');
			$found_http_status = 200;
			return 0;
		}
		if (str_contains($title, 'zoekresultaten')) {
			$syslog && local_log(LOG_INFO, 'search results found');
			$found_http_status = 200;
			return 0;
		}
		if ($title === 'Error') {
			$syslog && local_log(LOG_INFO, 'generic Error in title');
			if (($cnt = memcached_increment('get_fbid_from_facebook_html_fb_error', 1, 1, TEN_MINUTES)) >= 10) {
				mail_log('get_fbid_from_facebook_html facebook error, '.$cnt.'×');
			}
			$found_http_status = 500;
			return 0;
		}
		if ($title === 'Op Facebook plaatsen') {
			$syslog && local_log(LOG_INFO, 'proper sharer url');
			$found_http_status = 200;
			return 0;
		}
	}
	if (str_contains($data, $search_for = 'This page isn\'t available')
	&&	str_contains($data, $search_for = 'The link you followed may be broken')
	||	str_contains($data, $search_for = 'Deze inhoud is momenteel niet beschikbaar')
	||	str_contains($data, $search_for = 'Deze pagina is niet beschikbaar')
	) {
		$syslog && local_log(LOG_INFO, "page unavailable: $search_for");
		$found_http_status = 404;
		return 0;
	}
	if (str_contains($data, '<body>Sorry, this URL is invalid or has expired.</body>')
	||	str_contains($data, '<body>Deze URL is ongeldig of verlopen.</body>')
	) {
		$found_http_status = 419;
		return 0;
	}
	if (preg_match('!<title>(?:\(\d+\)\s*)?\x{202a}#\x{200e}\w+\x{202c}!ui', $data, $match)) {
		$syslog && local_log(LOG_INFO, 'found OK hashtag');
		$found_http_status = 200;
		return 0;
	}
	require_once 'defines/facebook_limit.inc';
	if (!facebook_fetch_check()) {
		if (str_contains($data, 'Het lijkt erop dat je deze functie misbruikt omdat je de functie te vaak gebruikt')
		||	str_contains($data, 'Je bent tijdelijk geblokkeerd')
		) {
			$syslog && local_log(LOG_INFO, 'rate limit page detected');
			require_once 'defines/facebook_limit.inc';
			register_facebook_state('facebook', false);
			$found_http_status = 429;
			return false;
		}
	}
	# Using '<'.'meta' instead of normal tag start to avoid HTML injection by IntelliJ
	if (preg_match('!<'.'meta property="al:ios:url" content="fb://profile/(?<fbid>\d+)"!ui', $data, $match)) {	// NOSONAR
		$match['role'] = 'user';
	} elseif (
		preg_match('!"path":\["currMedia"],"data":{"id":"(?<fbid>\d+)","owner":{"__typename":"Page","id":"(?<pageid>\d+)"}!ui', $data, $match)
	||	preg_match('!"params":{"fbid":"(?P<fbid>\d+)","set":"gm\.(?<pageid>\d+)","vanity":"\w+!ui', $data, $match)
	) {
		$syslog && local_log(LOG_INFO, 'media belongs to '.$match['pageid']);

		db_insert('fbid_belongs_to_page',"
		INSERT IGNORE INTO fbid_belongs_to_page SET
			FBID	= {$match['fbid']},
			PAGEID	= {$match['pageid']}"
		);
		$match['role'] = 'media';

	} elseif (
		preg_match('!<title>(?:\(\d+\)\s*)?Dingen\s*(?:om\s*te|die\s*je\s*kunt)\s*doen\s*in!ui', $data, $match)
	&&	preg_match('!"params":{"id":"(?P<fbid>\d+)","slug":"Dingen-die-je-kunt-doen-in-!ui', $data, $match)
	) {
		if (!str_contains($path, $match['fbid'])) {
			error_log($str = 'could not find id from data in url');
			mail_log($str);
			return false;
		}
		$match['role'] = 'page';

	} elseif (
		preg_match('!^/media/set/\?set=a\.(?P<fbid>\d+)\.\d+\.(?P<pageid>\d+)!ui', $path, $match)
	&&	preg_match('!"mediasetToken":"a\.(?P<fbid>\d+)\.\d+\.(?P<pageid>\d+)"!ui', $data, $match)
	) {
		$syslog && local_log(LOG_INFO, 'album belongs to '.$match['pageid']);
		db_insert('fbid_belongs_to_page','
		INSERT IGNORE INTO fbid_belongs_to_page SET
			FBID	= '.$match['fbid'].',
			PAGEID	= '.$match['pageid']
		);
		$match['role'] = 'album';

	} elseif (
		preg_match('!^/media/set/\?set=a\.(?<fbid>\d+)&!ui', $path, $match)
	&&	preg_match('!href="/photo/\?fbid=(\d+)&amp;set=a.'.$match['fbid'].'"!ui', $data)
	) {
		$match['role'] = 'album';

	} elseif (
		# Using '<'.'meta' instead of normal tag start to avoid HTML injection by IntelliJ
		preg_match('!<'.'link rel="canonical" href="https://www\.facebook\.com/\w+/photos/a\.(?<albumid>\d+)/(?P<fbid>\d+)/"!ui', $data, $match)
	&&	str_contains($data, '{"media":{"__typename":"Photo","__isNode":"Photo","id":"'.$match['fbid'].'"}')
	) {
		$syslog && local_log(LOG_INFO, 'photo belongs to album '.$match['albumid']);
		db_insert('fbid_belongs_to_page','
		INSERT IGNORE INTO fbid_belongs_to_page SET
			FBID	= '.$match['fbid'].',
			PAGEID	= '.$match['albumid']
		);
		$match['role'] = 'photo';

	} elseif (!preg_match('~"(?<role>application|event|group|page|photo|user|video)ID":"?(?<fbid>\d+)"?(?!,"deferredCookies")\b~ui', $data, $match)) {
		$syslog && local_log(LOG_INFO, 'could not find ID in page data');
		return 0;
	}
	$fbid = (int)$match['fbid'];

	$fbids[$fbid] = $fbid;

	if (!$fbid) {
		$syslog && local_log(LOG_INFO, 'found userID 0, not usable');
		# Using '<'.'link' instead of normal tag start to avoid HTML injection by IntelliJ
		if (str_contains($data, '<'.'link name="canonical" content="')) {	// NOSONAR
			mail_log('force OK due to canonical: '.$path);
			$found_http_status = 200;
		}
		return 0;
	}

	$page_id = 0;
 	if (preg_match('!"associated_page_id":"(?P<fbid>\d+)"!u', $data, $associated_page_match)) {
		$page_id = (int)$associated_page_match['fbid'];
		$syslog && local_log(LOG_INFO, 'found associated page: '.$page_id);

	} elseif (preg_match('!{"id":"'.$fbid.'","delegate_page_id":"(?<fbid>\d+)"!u', $data, $delegate_page_match)) {
		$page_id = (int)$delegate_page_match['fbid'];
		$syslog && local_log(LOG_INFO, 'found delegate page: '.$page_id);
	}

	if (preg_match('"[&?](?<role>album)_id=(?<fbid>\d+)"', $path, $album_match)) {
		$syslog && local_log(LOG_INFO, 'album detected');
		$match = $album_match;
	}

	if ($page_id) {
		$fbids[$page_id] = $page_id;
	}

	$syslog && local_log(LOG_INFO, 'detected role '.$match['role']);

	$syslog && local_log(LOG_INFO, 'found fbid '.$fbid.($fbid === 707270994 || $fbid === 100084453563882 ? '(t0mm1e!)' : ''));

	if ((	$fbid === 707270994
		||	$fbid === 100084453563882
		)
	&&	!in_array($presenceid, [
			335812,
			612385,
		], strict: true)
	) {
		if (preg_match('!<title(?:\s+id="pageTitle")?>(?:\(\d+\)\s+)?Facebook</title>!ui', $data)) {
			# content not available
			$found_http_status = 404;
			return 0;
		}

		if ((	str_contains($data, '<title>Thomas van Gulick</title>')
			||	str_contains($data, '<title>Thomas Ablas</title>')
			)
		&&	$path !== '/707270994'
		&&	$path !== '/100084453563882'
		&&	$path !== '/t0mm1e'
		) {
			# bad link that points to profile
			$found_http_status = 404;
			return 0;
		}

		if (memcached_add('Facebook_get_data_got_Thomas', true, ONE_MINUTE)) {
			mail_log(
				'got Thomas facebook page id as result', [
					'path'		 => $path,
					'presenceid' => $presenceid,
					'title'		 => preg_match('"<title>(?P<title>.*?)</title>"u', $data, $match) ? $match['title'] : ''
			]);
		}
		return false;
	}

	if (($partyids = db_simpler_array('fbid', 'SELECT ID FROM fbid WHERE ELEMENT = "party" AND FBID = '.$fbid))
		# Using '<'.'meta' instead of normal tag start to avoid HTML injection by IntelliJ
	&&	preg_match('!<'.'meta property="og:title" content="(?<'.'title>[^"]*)"!u', $data, $title_match)	// NOSONAR
	&&	'Facebook' !== ($title = html_entity_decode($title_match['title'], ENT_HTML5 | ENT_QUOTES, 'utf-8'))
	&&	'Log Into Facebook' !== $title
	) {
		if (!db_insert('facebook_info_log', '
			INSERT INTO facebook_info_log
			SELECT *, '.CURRENTUSERID.', '.CURRENTSTAMP.'
			FROM facebook_info
			WHERE NAME != "'.addslashes($title).'"
			  AND PARTYID IN ('.implode(', ', $partyids).')')
		||	!db_update('facebook_info', '
			UPDATE facebook_info SET
				CUSERID		= 0,
				CSTAMP		= '.time().',
				NAME		= "'.addslashes($title).'"
			WHERE NAME != "'.addslashes($title).'"
			  AND PARTYID IN ('.implode(', ', $partyids).')')
		) {
			return false;
		}
		$multiday = '';
		if (preg_match('"/events\\?/(\d+)(?:\\?/?\?event_time_id=|/)(\d+)(.{0,100})"s', $path, $multiday_match)) {
			[,$main, $day] = $multiday_match;

			if ($main === $fbid) {
				$multiday = 'main';
			} elseif ($day === $fbid) {
				$multiday = 'day';
			}
		}

		if (!db_insert('facebook_event_name', '
			INSERT INTO facebook_event_name SET
				MULTIDAY	= "'.$multiday.'",
				NAME		= "'.addslashes($title).'",
				STAMP		= UNIX_TIMESTAMP(),
				FBID		= '.$fbid.'
			ON DUPLICATE KEY UPDATE
				MULTIDAY	= IF(MULTIDAY = VALUES(MULTIDAY) AND NAME = VALUES(NAME), MULTIDAY, VALUES(MULTIDAY)),
				STAMP		= IF(MULTIDAY = VALUES(MULTIDAY) AND NAME = VALUES(NAME), STAMP, VALUES(STAMP)),
				FBID		= IF(MULTIDAY = VALUES(MULTIDAY) AND NAME = VALUES(NAME), FBID, VALUES(FBID)),
				NAME		= IF(MULTIDAY = VALUES(MULTIDAY) AND NAME = VALUES(NAME), NAME, VALUES(NAME))')
		) {
			return false;
		}
	}

	$get_type = $match['role'];
	$is_page  = $get_type === 'page';
	$is_event = $get_type === 'event';

	if (!$is_page
	&&	!$is_event
	&&	!$page_id
	) {
		if (str_contains($data, '<b>Pagina</b>')	// NOSONAR
		||	str_contains($data, '"text":"Pagina \\u00b7 ')
		) {
			$syslog && local_log(LOG_INFO, 'actual role: page');
			$is_page = true;
			$get_type = 'page';
		}
	}

	$newsite = 'https://www.facebook.com/'.($get_type === 'event' ? 'events/' : '').($page_id ?: $fbid);
	$newtype = ($page_id || $is_page) ? 'facebookpage' : 'facebook';

	if ((	$get_type === 'user'
		||	$get_type === 'page'
		)
	&&	!preg_match('"^/\d+/events$"', $path)
	) {
		$link = trim($path, '/');
		if (!$link || is_number($link)) {
			if (preg_match('!"vanity":"(?P<link>[^\"]+)"!', $data, $match)) {
				$link = $match['link'];
			}
		}
		if (!$link || is_number($link)) {
			if (preg_match('!"productAttributionId":"\d+","url":"(?P<link>.*?)","params"!', $data, $match)) {
				$link = $match['link'];
				$link = str_replace('\/', '/', $link);
				$link = trim($link, '/');
			}
		}
		if (!$link || is_number($link)) {
			if (preg_match('!hreflang="x-default" href="https://www\.facebook\.com/people/(?P<link>[^/]*)/\d{6,}/">!iu', $data, $match)) {
				$link = $match['link'];
			}
		}

		$syslog && local_log(LOG_INFO, 'found facebook_link reference: '.$link);

		if (!is_number($link)
		&&	$link
		) {
			require_once '_url.inc';
			# strip anything behind ampersand
			$link = cleanup_url($link, true);
			$link = preg_replace('"\?(.*)$"u', '', $link);
			$link = utf8_mytrim($link, '/');

			if ($link !== 'profile.php') {
				foreach ([
					$fbid		=> $get_type,
					$page_id	=> 'page'
				] as $store_fbid => $type) {
					if (!$store_fbid) {
						continue;
					}
					if (!db_replace('facebook_link', '
						INSERT INTO facebook_link SET
							FBID	='.$store_fbid.',
							LINK	="'.addslashes($link).'",
							TYPE	="'.$type.'",
							STAMP	='.time().'
						ON DUPLICATE KEY UPDATE
							LINK	= VALUES(LINK),
							TYPE	= VALUES(TYPE),
							STAMP	= IF(LINK = VALUES(LINK) AND TYPE = VALUES(TYPE), STAMP, VALUES(STAMP))')
					) {
						return false;
					}
				}
			}
		}
	}
	if ($presenceid) {
		if (!db_update('presence_log', "
			INSERT INTO presence_log
			SELECT *, UNIX_TIMESTAMP(), 0 FROM presence
			WHERE (	SITE != '$newsite'
				OR	TYPE != '$newtype'
			) AND PRESENCEID = $presenceid")
		||	!db_update('presence', "
			UPDATE presence SET
				CSTAMP	= UNIX_TIMESTAMP(),
				CUSERID	= 0,
				SITE	= '$newsite',
				TYPE	= '$newtype'
			WHERE (	SITE != '$newsite'
				OR	TYPE != '$newtype'
			) AND PRESENCEID = $presenceid")
		||	!db_replace(['fbid', 'presence'], "
			REPLACE INTO fbid (ELEMENT, ID, FBID, TYPE)
			SELECT ELEMENT, ID, $fbid, '$get_type'
			FROM presence
			WHERE PRESENCEID = $presenceid
			UNION
			SELECT 'presence', $presenceid, $fbid, '$get_type'")
		||	$page_id
		&&	$page_id !== $fbid
		&&	(	!db_replace(['fbid', 'presence'], "
				REPLACE INTO fbid (ELEMENT, ID, FBID, TYPE)
				SELECT ELEMENT, ID, $page_id, 'page'
				FROM presence
				WHERE PRESENCEID = $presenceid
				UNION
				SELECT 'presence', $presenceid, $page_id, 'page'")
			||	!db_insert('fbid_identical', "
				INSERT IGNORE INTO fbid_identical SET
					STAMP	= UNIX_TIMESTAMP(),
					PAGEID	= $page_id,
					FBID	= $fbid")
			)
		) {
			return false;
		}
		if ($event_fbids = db_rowuse_array('fbid', $q = "
			SELECT ELEMENT, ID, FBID, TYPE FROM party_db.fbid
			WHERE ELEMENT IN ('organization', 'location', 'artist')
			 AND TYPE = 'event'")
		) {
			mail_log('got event role for element that should not have event role', [
				'event_fbids' => $event_fbids,
				'query'		  => $q
			]);
			if (CLI) {
				exit(1);
			}
		}
	}
	if ($page_id) {
		$get_type = 'page';
		return $page_id;
	}
	$found_http_status = 200;
	return $fbid;
}

function detect_facebook_login_page(string $data): bool {
	return	preg_match('!<title(?:\s+id="pagetitle")?>Facebook</title>!ui', $data)
	&&	(	str_contains($data, '<meta property="og:title" content="Log in or sign up to view"')
		||	str_contains($data, '<meta property="og:title" content="Aanmelden of registreren om te bekijken"')
		)
	||	preg_match(
			'!<title(?:\s+id="pagetitle")?>'.
				'(?:Log into Facebook|Aanmelden of registreren om te bekijken|Aanmelden bij Facebook)'.
			'</title>!ui',
			$data
		);
}

function set_facebook_cookie(RemoteWebDriver $webdriver, string $cookie_data): void {
	foreach (explode(';', $cookie_data) as $cookie) {
		$cookie = mytrim($cookie);
		$pair = explode('=', $cookie, 2);
		if (!isset($pair[1])) {
			error_log('set_facebook_cookie got bad cookie pair: '.$cookie);
			continue;
		}
		[$name, $value] = explode('=', $cookie, 2);
		$webdriver->manage()->addCookie(['name' => $name, 'value' => $value, 'domain' => 'www.facebook.com']);
	}

}
