<?php

require_once '_memcache.inc';
require_once '_salutes.inc';

# most important mailoption has lower value:
const MAIL_EVERYONE			= 0;
const MAIL_REPLYTO			= 1;
const MAIL_FROM				= 2;
const MAIL_FROM_FORWARDED	= 3;
const MAIL_TO				= 4;
const MAIL_FROM_BODY		= 5;
const MAIL_FROM_PROFILE		= 6;
const MAIL_FROM_TEMPLATE	= 7;
const MAIL_CC				= 8;

# order preference
const MAIL_NORMAL			= 1;
const MAIL_MAILING			= 2;
const MAIL_POSTMASTER		= 3;
const MAIL_FLOCK			= 4;
const MAIL_DAEMON			= 5;

const INTERNAL_EMAIL_REGEX	= /** @lang PhpRegExp */ '"^(?<user>[^@]*)@partyflock\.(?:be|net|nl)$"i';

function get_contact_mails_for_everyone(int $ticketid): array|false {
	if (false === ($mail_options = get_contact_mail_options($ticketid))) {
		return false;
	}
	$reply_to_mail = false;
	$reply_to_name = false;
	$to_name = null;
	$to_mail = null;
	$original_to_mail = null;
	$ccs = [];
	$all = [];
	foreach ($mail_options as $mail_option) {
		switch ($mail_option['TYPE']) {
		case MAIL_FROM_FORWARDED:	# respond to the forwarder, not to the original messenger
		case MAIL_FROM_BODY:		# skip e-mail found in body
			continue 2;
		case MAIL_REPLYTO:
			$reply_to_mail = $mail_option['EMAIL'];
			$reply_to_name = $mail_option['NAME'];
			$all[$reply_to_mail] = $reply_to_mail;
			break;
		case MAIL_FROM:
			if ($mail_option['DIRECTION'] === 'touser') {
				# don't message ourselves
				break;
			}
			if (!$to_name) {
				$to_mail = $mail_option['EMAIL'];
				$to_name = $mail_option['NAME'];
			}
			$all[$mail_option['EMAIL']] = $mail_option['EMAIL'];
			break;
		case MAIL_TO:
			if ($mail_option['DIRECTION'] === 'toadmin') {
				# don't message ourselves
				break;
			}
			$original_to_mail = $mail_option['EMAIL'];
			$all[$mail_option['EMAIL']] = $original_to_mail;
			break;
		case MAIL_CC:
			if (empty($mail_option['EMAIL'])) {
				ob_start();
				print_rr($mail_option,'mail_option');
				print_rr($ticketid,'ticketid');
				mail_log("empty EMAIL for MAIL_CC: ".ob_get_clean());
			} else {
				$ccs[] = $mail_option['EMAIL'];
				$all[$mail_option['EMAIL']] = $mail_option['EMAIL'];
			}
			break;
		}
	}
	if ($reply_to_mail) {
		# 'reply_to' supersedes 'to'
		$to_mail = $reply_to_mail;
		$to_name = $reply_to_name;
	}
	return [
		'ccs'				=> $ccs,
		'all'				=> $all,
		'toname'			=> $to_name,
		'tomail'			=> $to_mail,
		'original_tomail'	=> $original_to_mail,
	];
}
function get_contact_mail_options(int $ticketid, bool $nocache = false): array|false {
	static $__mail_options = [];
	if ($nocache) {
		if (!isset($_REQUEST['NOMEMCACHE'])) {
			$_REQUEST['NOMEMCACHE'] = true;
			$reset = true;
		}
	} elseif (isset($__mail_options[$ticketid])) {
		return $__mail_options[$ticketid];
	}
	# prefer to-admin, then to-user, to duplicate in to-user are ignored (using done hash)
	if (!($mail_infos = memcached_rowuse_array(['contact_ticket_message', 'contact_ticket_mail'], "
		SELECT DIRECTION, TO_EMAIL, TO_NAME, FROM_EMAIL, FROM_NAME, REPLYTO, REPLYTONAME, CTMSGID, SUBJECT, CIMID, UNCOMPRESS(HEADERS) AS HDRS, CC
		FROM (	SELECT DIRECTION, MAX(CTMSGID) AS CTMSGID
				FROM contact_ticket_message AS msg
				LEFT JOIN contact_ticket_mail AS mail USING (CTMSGID, TICKETID)
				WHERE TICKETID = $ticketid
				  AND DIRECTION IN ('touser', 'toadmin', 'forwardinfo')
				GROUP BY DIRECTION, TO_EMAIL, FROM_EMAIL, FROM_NAME, REPLYTO, REPLYTONAME
			 ) AS g
		LEFT JOIN contact_ticket_mail USING (CTMSGID)
		ORDER BY
			DIRECTION = 'toadmin' DESC,
			DIRECTION = 'touser' DESC,
			CTMSGID DESC",
		TEN_MINUTES))
	) {
		return $__mail_options[$ticketid] = $mail_infos;
	}
	if (isset($reset)) {
		unset($_REQUEST['NOMEMCACHE']);
	}
	$__mail_options[$ticketid] = [];
	$done = [];
	$add_option = static function(array $mail_info, int $type, ?array $set = null) use (&$add_option, &$done, &$__mail_options, $ticketid): int|false {
		$name  = '';
		$email = '';
		switch ($type) {
		case MAIL_FROM_FORWARDED:
			$is_forward = true;
			# fall through
		case MAIL_FROM:
			if (!$mail_info['FROM_EMAIL']) {
				return false;
			}
			$name  = $mail_info['FROM_NAME'];
			$email = $mail_info['FROM_EMAIL'];
			break;

		case MAIL_TO:
			if (!$mail_info['TO_EMAIL']) {
				return false;
			}
			$name  = $mail_info['TO_NAME'];
			$email = $mail_info['TO_EMAIL'];
			break;

		case MAIL_REPLYTO:
			if ((!$mail_info['REPLYTO'	  ]	|| $mail_info['FROM_EMAIL'] === $mail_info['REPLYTO'])
			&&	(!$mail_info['REPLYTONAME']	|| $mail_info['FROM_NAME' ] === $mail_info['REPLYTONAME'])
			) {
				return false;
			}
			$name  = $mail_info['REPLYTONAME'] ?: $mail_info['FROM_NAME'];
			$email = $mail_info['REPLYTO']	   ?: $mail_info['FROM_EMAIL'];
			if (!$email) {
				return false;
			}
			break;

		case MAIL_CC:
			if ($set) {
				[$email, $name] = $set;
				break;
			}
			if ($mail_info['CC']) {
				$cc_src = $mail_info['CC'];
			} elseif ($mail_info['HDRS']
				&&	preg_match('"CC:.*(?:\r?\n\s+.*)*"mi',$mail_info['HDRS'],$match)
			) {
				$cc_src = preg_replace('"^CC:\s*"i','',$match[0]);
			} else {
				return false;
			}

			$cc_cnt = 0;
			foreach (explode(',', $cc_src) as $cc_line) {
				$cc_line = trim($cc_line);
				$cc_line = str_replace("\n", '', $cc_line);

				if (preg_match('"(.*)?[<\s]([\w._\-]+@[\w.\-]+)"i', $cc_line, $match)) {
					[, $name, $email] = $match;
					$name = utf8_to_win1252(mytrim($name,'"'));
					$email = mytrim($email);
					if ($name === $email) {
						$name = '';
					}
					# don't CC to internal mails
					if (ticket_section_flock_email($email)) {
						continue;
					}
					$cc_cnt += $add_option($mail_info, $type, [$email,$name]);
				}
			}
			return $cc_cnt;
		}
		if (!$email) {
			return false;
		}
		$lower_name  = strtolower($name);
		$lower_email = strtolower($email);
		if (isset($done[$lower_name][$lower_email])) {
			return false;
		}
		$done[$lower_name][$lower_email] = true;
		if (($preference = mail_preference($email)) === MAIL_DAEMON
		||	$mail_info['CIMID']
		) {
			return false;
		}
		$__mail_options[$ticketid][] = [
			'FIRSTNAME'		=> get_email_salute($email, $name),
			'NAME'			=> $name,
			'EMAIL'			=> $email,
			'DIRECTION'		=> $mail_info['DIRECTION'],
			'SUBJECT'		=> $mail_info['SUBJECT'],
			'CTMSGID'		=> $mail_info['CTMSGID'],
			'TYPE'			=> $type,
			'NOREPLY'		=> dont_reply($email),
			'PREFERENCE'	=> $preference,
			'FLOCK_FORWARD'	=> isset($is_forward) && is_flock_email($email),
		];
		return 1;
	};
	foreach ($mail_infos as $mail_info) {
		switch ($mail_info['DIRECTION']) {
		case 'toadmin':
			$add_option($mail_info, MAIL_REPLYTO);
			$add_option($mail_info, MAIL_FROM);
			$add_option($mail_info, MAIL_CC);
			$add_option($mail_info, MAIL_TO);
			break;

		case 'touser':
			$add_option($mail_info, MAIL_TO);
			break;

		case 'forwardinfo':
			$add_option($mail_info, MAIL_FROM_FORWARDED);
			break;
		}
	}
	if (!empty($__mail_options[$ticketid])) {
		sort_contact_mail_options($__mail_options[$ticketid]);
	}
	return $__mail_options[$ticketid];
}

/** @noinspection PhpMultipleClassDeclarationsInspection */
function is_flock_email(#[SensitiveParameter] string $email): string|false {
	# returns user part
	if (!preg_match(INTERNAL_EMAIL_REGEX, $email, $match)) {
		return false;
	}
	return strtolower($match['user']);
}

function is_non_ticket_flock_user(string $user): bool {
	return match($user) {
		'bart',
		'danielle',
		'lisa',
		'sven',
		'thomas'	=> true,
		default		=> false,
	};
}

/** @noinspection PhpMultipleClassDeclarationsInspection */
function ticket_section_flock_email(#[SensitiveParameter] string $email): bool {
	if ($email === '<EMAIL>') {
		return true;
	}
	if (!($user_part = is_flock_email($email))) {
		return false;
	}
	return !is_non_ticket_flock_user($user_part);
}

function sort_contact_mail_options(array &$mail_options): void {
	usort($mail_options, static function(array $a, array $b): int {
		# don't prefer internal e-mail, unless it's a forward
		$a_is_internal = preg_match(INTERNAL_EMAIL_REGEX,$a['EMAIL']) ? 10 : 0;
		$b_is_internal = preg_match(INTERNAL_EMAIL_REGEX,$b['EMAIL']) ? 10 : 0;
		if ($is_internal = $a_is_internal - $b_is_internal) {
			return $is_internal;
		}
		if (isset($a['PREFERENCE'], $b['PREFERENCE'])
		&&	($rc = $a['PREFERENCE'] - $b['PREFERENCE'])
		||	($rc = $a['TYPE'] - $b['TYPE'])
		) {
			return $rc;
		}
		return $b['CTMSGID'] - $a['CTMSGID'];
	});
}

/** @noinspection PhpMultipleClassDeclarationsInspection */
function mail_preference(#[SensitiveParameter] string $email): int {
	$lower_email = strtolower($email);
	if (ticket_section_flock_email($lower_email)) {
		return MAIL_FLOCK;
	}
	if ($lower_email === 'undisclosed-recipient:;') {
		return MAIL_MAILING;
	}
	if (preg_match('"^(?<user_part>(?:dono?t|no)[\-_.]?reply.*|mailer-daemon|(?:[a-zA-Z\d_+\-]*-)return|undisclosed-recipient:;)@"', $lower_email, $match)) {
		if ($match['user_part'] === 'mailer-daemon') {
			return MAIL_DAEMON;
		}
		if ($match['user_part'] === 'postmaster') {
			return MAIL_POSTMASTER;
		}
		return MAIL_MAILING;
	}
	return MAIL_NORMAL;
}

/** @noinspection PhpMultipleClassDeclarationsInspection */
function dont_reply(#[SensitiveParameter] string $email, ?int $preference = null): bool {
	$preference ??= mail_preference($email);
	return	$preference === MAIL_DAEMON
		||	$preference === MAIL_MAILING
		||	preg_match('"@partyflock\.\w+l"i', $email)
		&&	!is_non_ticket_flock_user($email);
}
