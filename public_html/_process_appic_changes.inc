<?php

declare(strict_types=1);

use JetBrains\PhpStorm\ExpectedValues;

require_once '_ubb_preprocess.inc';
require_once 'defines/appic.inc';

# alter_active_item $action parameter:

const REMOVE_ACTIVE_ITEM	= 1;
const CLEAR_ACTIVE_ITEM		= 2;

const ACTION_NEEDS_ACTIVE_ITEM = [
	'event_main'		=> 'party',
	'missing_event'		=> 'party',
	'new_event'			=> 'party',
	'new_event_group'	=> 'organization',
	'new_venue'			=> 'location',
	'new_artist'		=> 'artist',
];

const PROCESS_SECTION_ORDER = [
	'event_cancel'				=> 0,
	'new_event'					=> 1,
	'missing_event'				=> 2,
	'event_detail'				=> 3,
	'event_main'				=> 4,
	'event_lineup'				=> 5,
	'event_lineup_missed'		=> 6,
	'event_lineup_missing'		=> 6,
	'event_lineup_incorrect'	=> 7,
	'event_timetable'			=> 8,
	'event_timetable_missed'	=> 9,
	'event_timetable_missing'	=> 9,
	'event_timetable_incorrect'	=> 10,
	'event_info'				=> 11,
	'event_info_incorrect'		=> 12,
	'new_event_group'			=> 13,
	'concept_main'				=> 14,
	'venue_main'				=> 15,
	'new_venue'					=> 16,
	'new_artist'				=> 17,
	'artist_main'				=> 18,
	'product_ticket_price'		=> 19,
];

function processlist_change_requests(): bool {
	if (!require_admin()) {
		no_permission();
		return false;
	}

	layout_open_menu();
	layout_open_menuitem();
	?><a target="_blank" href="https://dashboard.beappic.com/admin/companies-reports"><?= get_appic_icon() ?> Company requests</a><?
	layout_close_menuitem();
	layout_open_menuitem();
	?><a target="_blank" href="https://dashboard.beappic.com/admin/users-reports"><?= get_appic_icon() ?> User requests</a><?
	layout_close_menuitem();
	layout_close_menu();

	if (!require_obtainlock(LOCK_PROCLIST_APPIC_CHANGES)) {
		return false;
	}
	release_lock_on_unload(LOCK_PROCLIST_APPIC_CHANGES);

	if (!($updates = db_rowuse_array('update_from_appic', "
		SELECT *
		FROM update_from_appic
		WHERE STATUS = 'open'
		ORDER BY ID"
	))) {
		return $updates !== false;
	}

	foreach ($updates as &$update) {
		$obj = safe_json_decode($update['JSON'], true);

		# superseded bym music_styles_names:
		unset($obj['data']['music_styles']);

		$element = $obj['element']			?? null;
		$id		 = $obj['data'][$element]	?? null;
		$detail  = $obj['action_details']	?? null;
		$action  = $obj['action']			?? null;
		$action = mytrim($action, '/');

		$update['obj']		= $obj;
		$update['action']	= $action;

		$tree[$action.($detail ? '_'.$detail : '')][$element][$id][] = $update;
	}
	unset($update);

	include_js('js/form/appicchangerequests');
	include_style('appic_requests');

	?><script>addevent(window, 'beforeunload', function(/* event */) {
		Pf_removeActiveItemForAppic(true);
	});</script><?

	layout_open_box('appic-changes', 'appic-changes');

	$prev_action = null;

	uksort($tree, static fn(string $a, string $b): int => PROCESS_SECTION_ORDER[$a] <=> PROCESS_SECTION_ORDER[$b]);

	foreach ($tree as $action => $elements) {
		$unknown_action = false;
		$action_title = match($action) {
			'event_cancel'				=> element_name('cancelled_event'),
			'event_main',
			'event_details'				=> element_name('changed_event'),
			'event_lineup'				=> element_name('changed_lineup'),
			'event_lineup_missed'		=> element_name('missed_lineup'),
			'event_lineup_missing'		=> element_name('missing_lineup'),
			'event_lineup_incorrect'	=> element_name('incorrect_lineup'),
			'event_timetable'			=> element_name('changed_timetable'),
			'event_timetable_missed',
			'event_timetable_missing'	=> element_name('missing_timetable'),
			'event_timetable_incorrect'	=> element_name('incorrect_timetable'),
			'event_info'				=> element_name('changed_event_information'),
			'event_info_incorrect'		=> element_name('incorrect_event_information'),
			'concept_main'				=> element_name('changed_organization'),
			'new_artist'				=> element_name('new_artist'),
			'artist_main'				=> element_name('changed_artist'),
			'new_venue'					=> element_name('new_location'),
			'venue_main'				=> element_name('changed_location'),
			'new_event_group'			=> element_name('new_organization'),
			'new_event'					=> element_name('new_event'),
			'missing_event'				=> element_name('missing_event'),
			'product_ticket_price'		=> element_name('product_ticket_price'),
			default						=> ($unknown_action = true) ? element_name('unknown_request').': '.__('action:warn_user', ['USERID' => 22269]) : false,
		};
		if ($unknown_action) {
			mail_log('unsupported appic change request action: '.$action, error_log: 'WARNING');
		}
		foreach ($elements as $element => $ids) {
			$pf_element = APPIC_ELEMENT_TO_PARTYFLOCK[$element] ?? null;
			foreach ($ids as $id => $updates) {
				if (str_starts_with($action, 'event_lineup')
				||	str_starts_with($action, 'event_timetable')
				) {
					# group identical requests
					$element_name = mb_strtolower(get_element_title($pf_element, $id));
					$element_stripped = preg_replace('"\s*fest(?:ival)?$"u', '', $element_name);
					/** @noinspection DuplicatedCode */
					$remove_regexp = '"\b(?:'.
						 '(?:time?|tijd)[-\s]*[tl][asw]?[bv][ler]+s?(?:\s*op)?|tt|line[-\s]*u[po]s?'.
						'|time[\s-]line'.
						'|tineyeblr|tible|implenent|pageand|wrop|raptoren|inst|errf|appwebsite'.
						'|[iuj]ns?te[n ]t?se?(?:fe[sa]t(?:ival)?|pagina|page|city)?'.
						'|def[qc]on[\s.]*1?(?:com|qdance)?|quincecom|q[\s-]*dance[\s.]*coml\s*defqon[\s.]*1(?:\s*2024\d*time[\s-]*table)?'.
						'|a+|b+|c+|d+|e+|f+|g+|h+|i+|j+|k+|l+|m+|n+|o+|p+|q+|r+|s+|t+|u+|v+|w+|x+|y+|z+'.
						'|0+|1+|2+|3+|4+|5+|6+|7+|8+|9+'.
						'|twee|drie|vier|vijf|zes|zeven|acht|negen|tien'.
						'|appic|partyflock|j[as]+|ne+|ji|jo|oo|om|eens|joh+|erin|meerdere?|vdf|vd|gvht'.
						'|via|v[aes]n|en|of|i[ns]|o[pn]|aan|ist|met|er+|op|erop|te|net|onder|andere|eral'.
						'|gw|al|ff|ans|pls+|please|aub+|als(?:je|tu)blieft|ks|us|jn|a[pn]|zo|ac+|bij|naast|over|langs|voor|achter'.
						'|alleen|helaas|hoezo|zowel|als|hier\s*naar\s*toe|per|[fm]ijn|dan|week|plan(?:ning)?|[pn]aar'.
						'|videos?|djs?|waar|dit|rap|daar|du+rt|waarom|vanavond|wanneer|wordt?|i[nm]gevuld'.
						'|dus|hierop|kan|even|uitstippelen|u[ip]|to|uo|sd|tim|graag|(?:zomer[\s-]*)?spektakel'.
						'|(?:te\s*)?laat|a+l+|joe|hup|tempo+|inmiddels|sinds|amigos?|ook|feit(?:en)?|reeds'.
						'|di?e|dat|het|een|niet|hier|maar|nu|nvt|nog|door|tot|t/?m|binnen|uit|zelfs?|hem|haar|we|wil(?:len)?'.
						'|that|this|at?|from|on|t?he(?:re|ir)?|it\'?s?|saw|see|has|(?:yes+)+|(?:[nxg]o)+|last|first|for|and'.
						'|[hzw]ijs?|[jz]e|ik|hun|zel|jullie|artiest(?:en)?|iets|(?:bekend)?gemaakt|schiet|markeren'.
						'|mai?n|mattie|moet(?:en)?|kom(?:en|t)?|maken|maak|mag|voort|lang|tientallen?|intentie'.
						'|favou?riet(?:en)?|favou?rites?|verwerkt?|lijkt?|doorgevoerd|op\s*site'.
						'|dispo|aanwezig|aangekondigd|wo+ho+|zichtbaar|a+nma[aken]+|meest|recente?'.
						'|kwartier|(?:half\s*)?uur|jaar|karnaval|mand|(?:eer)?gister(?:en|avond)?|h[ae]llo+|zou(?:den)?|me(?:zelf)?'.
						'|kunnen|deze|(?:moge|eigen?)?lijk|gooi(?:en)?|opschiet(?:en)?|lope[nm]|vermel[dt]+'.
						'|heeft|heb(ben|t)?|(?:ge)?zien?|zag|st[as]+[tn]|gevonden|hoor|spelen|doe?|(?:ge)?[zs]et|zette+n+|gedeel[dt]'.
						'|account|(?:ge)?post(?:ed|en)?|link|bio|bericht|verhaal|niks|alles|overal|vandaag|eigen|volledige?|schema'.
						'|snel+|zojuist|social[es]?|media|kanaal|kanalen|comple[te]+|gek|gooi|gram|gast|gespot'.
						'|online+|onlunr|beken[dt]|kun(?:t|en)?|vinden|staa[tn]|kijk(?:t|en)?|(?:(?:vrij)?ge)?geven|toevoegen|zijn|geplaatst|laatste?'.
						'|ontbreekt|helemaal|geen|probleem|wel|elke|alle|dag(?:en)?|gas|(?:ge)?publiceer[dt]'.
						'|offici[ëe]*le?|gehe[le]+|beschikbaar|ge[üu]pload|organisatie|bak|dames|pagina|tijd(?:en)?'.
						'|official|availa?ble|page|(?:web[\s-]*)?s[io]te+|event|missing|hi|out|now|they(?:\'?re)?|(?:ge)?release?d?|announced?|was|been'.
						'|al+ready|you|had|one|job|live|just|upload(?:e[dn]+)?|publish(?:ed)?|shows?|got|bee|latest'.
						'|full|made|public|time|final|days?|are|pictures?|also|dude|add|ban|hours?|by|can|be|found'.
						'|not|yet|non|went|with|seen|interns?|drop(?:ped)?|reports?|rest|careful+|have|some|errors?|problems?'.
						'|find|issues?|times?|but|launch(?:ed)?|night|can(?:no|\'|)t|make|schedule|everywhere|every|where'.
						'|q?dance|eas(?:y|ier)?|understand|story|short|check|com|base|know|need|network|why|isn\'?t'.
						'|missen|wrong|compared?|sommigen?|klopt|genres?|horen|bekijken|vs|veel'.
						'|[ij]nstr?a+(?:gra+[mn])?|ig|[fd]aceboo[kn](?:pagina|page)?|fb|tiktok'.
						'|fe[sa]t(?:ival)?'.
						'|'.preg_quote($element_name, '"').
						'|'.preg_quote($element_stripped, '"').
						')\b"u';
					$updates_tree = [];
					foreach ($updates as $update) {
						$action_details = $update['obj']['action_details'] ?? null;
						$type		    = $update['obj']['data']['type'] ?? null;
						if ($text		= $update['obj']['data']['text'] ?? null) {
							$i = 0;
							$map = [];
							$text = mb_strtolower($text);
							$text = preg_replace_callback('"https?://[^\s\"]+"', static function(array $match) use (&$map, &$i) : string {
								$map[$i] = preg_replace('"\?.*$"u', '', $match[0]) ?? $match[0];
								return "\x6".($i++)."\x6";
							}, $text);
							if (null !== ($new_text = preg_replace($regexps = [
									'"[^\w '.chr(0x6).']+"u',
									'"\b(?:app[se]|applicati(?:on|e)s?)\b"u',
									'"\bvrij\b"',
									$remove_regexp,
									'"\s{2,}"u'
								], ['',
									'app',
									'vrijdag',
									' ',
									' '
								], $text))
							) {
								$text = $new_text;
							} else {
								preg_failure($text, $regexps);
							}
							if ('hele' === ($text = utf8_mytrim($text))) {
								$text = '';
							}
							if ($i) {
								if (null !== ($new_text = preg_replace_callback(
									$regexp = "'\x6(?<i>\d+)\x6'",
									static fn(array $match) => $map[(int)$match['i']],
									$text))
								) {
									$text = $new_text;
								} else {
									preg_failure($text, $regexp);
								}
							}
							if (preg_match('"app\b"', $text)) {
								$update['obj']['data']['type'] = $type = 'in_app';
								if (null !== ($new_text = preg_replace($regexp = '"app\b"', '', $text))) {
									$text = $new_text;
								} else {
									preg_failure($text, $regexp);
								}
							}
							$update['obj']['data']['text'] = $text;
						}
						$updates_tree[$action_details][$type][$text][] = $update;
					}
					$new_updates = [];
					foreach ($updates_tree as /* $sub_detail => */ $sub_types) {
						ksort($sub_types);
						foreach ($sub_types as /* $sub_type => */ $sub_texts) {
							ksort($sub_texts);
							foreach ($sub_texts as /* $sub_text => */ $sub_updates) {
								if (count($sub_updates) > 1) {
									$new_updates[] = ['grouped' => $sub_updates];
								} else {
									$new_updates[] = $sub_updates[0];
								}
							}
						}
					}
					$updates = $new_updates;
				}

				if ($prev_action !== $action) {
					?><hr class="small" style="margin: 2em 0 2em 0;"><?
					$prev_action = $action;
				}

				if ($id) {
					?><table class="fw default vtop body"><?
					if (!($party_link = get_element_link('party', $id, target: 'blank'))) {
						?><caption class="item-removed"><?= __C('status:removed') ?></caption><?
					} else {
						?><caption class="event-name"><?
						echo $party_link;
						if ($party = memcached_party_and_stamp($id)) {
							?><small class="light"><?
							if ($party['SUBTITLE']) {
								?><span class="subtitle"> <?= MIDDLE_DOT_ENTITY ?> <?= escape_utf8($party['SUBTITLE']) ?></span><?
							}
							?><small> <?= MIDDLE_DOT_ENTITY ?> <?
							change_timezone($party['TIMEZONE'] ?: 'UTC');
							_datetime_display($party['TIMEZONE'] ? $party['STAMP'] : $party['STAMP_TZI']);
							change_timezone();
							?></small><?
							?></small><?
						}
						?></caption><?
					}
					?><caption class="action-header"><?= $action_title ?></caption><?
					?><tbody class="requests"><?

					foreach ($updates as $update) {
						if ($sub_updates = $update['grouped'] ?? null) {
							$sub_ids = [];
							foreach ($sub_updates as $inner_update) {
								$sub_ids[] = $inner_update['ID'];
							}
							$cnt  = count($sub_updates);
							$text = $inner_update['obj']['data']['text'] ?? null;
							$type = $inner_update['obj']['data']['type'] ?? null;
							$id	  = "'".implode(',', $sub_ids)."'";
						} else {
							$cnt  = 1;
							$text = $update['obj']['data']['text'] ?? null;
							$type = $update['obj']['data']['type'] ?? null;
							$id   = $update['ID'];
						}
						?><tr class="request"><?
						?><td class="action-decline"><?
						?><span <?
						?> class="action-act"<?
						?> onclick="changerow(this,'addclass','light'); Pf.changeAppicRequestStatus(this, <?= $id ?>, 'declined')"<?
						?> onmouseover="Pf.changeRequestLine(this, 'hilited-red', true)"<?
						?>  onmouseout="Pf.changeRequestLine(this, 'hilited-red', false)"<?
						?>>&#10008;</span><?
						?></td><?
						?><td class="action-exists"><?
						?><span <?
						?> class="action-act"<?
						?> onclick="changerow(this,'addclass','light'); Pf.changeAppicRequestStatus(this, <?= $id ?>, 'exists')"<?
						?> onmouseover="Pf.changeRequestLine(this, 'hilited-yellow', true)"<?
						?>  onmouseout="Pf.changeRequestLine(this, 'hilited-yellow', false)"<?
						?>>&#10004;</span><?
						?></td><?
						?><td class="action-accept"><?
						?><span <?
						?> class="action-act"<?
						?> onclick="changerow(this,'addclass','light'); Pf.changeAppicRequestStatus(this, <?= $id ?>, 'accepted')"<?
						?> onmouseover="Pf.changeRequestLine(this, 'hilited-green', true)"<?
						?>  onmouseout="Pf.changeRequestLine(this, 'hilited-green', false)"<?
						?>>&#10004;</span><?
						?></td><td class="nowrap"><?
						if ($cnt > 1) {
							echo $cnt, MULTIPLICATION_SIGN_ENTITY; ?> <?
						}
						?></td><td><?
						if ($type) {
							?><span class="light7 nowrap"><?= escape_utf8($type) ?></span><?
						}
						?></td><td class="fw"><?
						if ($text) {
							$text = preg_replace_callback('"(?:^|\b)@(?<user>[\w-]+)"u',
								static fn(array $match): string => "[url=https://www.instagram.com/{$match['user']}]@{$match['user']}[/url]",
								$text);
							echo make_all_html(_ubb_preprocess($text, utf8: true), UBB_UTF8);
						}
						?></td><?
						?></tr><?
					}
					?></tbody><?
					?></table><?
				} else {
					?><table class="fw default vtop body"><?
					?><caption class="action-header"><?= $action_title ?></caption><?
					?><tbody class="requests"><?
					foreach ($updates as $update) {
						?><tr class="request add-add" onmouseover="Pf.checkRequestDone(this, <?= $update['ID'] ?>)"><?

						?><td class="action-decline"><?
						?><div <?
						?> class="action-nok action-act ib"<?
						?> onclick="Pf.changeAppicRequestStatus(this, <?= $update['ID'] ?>, 'declined')"<?
						?> onmouseover="Pf.changeRequestLine(this, 'hilited-red', true)"<?
						?> onmouseout="Pf.changeRequestLine(this, 'hilited-red', false)"<?
						?>>&#10008;</div><?
						?></td><?

						?><td class="action-exists"><?
						?><div <?
						?> class="action-act ib"<?
						?> onclick="Pf.changeAppicRequestStatus(this, <?= $update['ID'] ?>, 'exists')"<?
						?> onmouseover="Pf.changeRequestLine(this, 'hilited-yellow', true)"<?
						?> onmouseout="Pf.changeRequestLine(this, 'hilited-yellow', false)"<?
						?>>&#10004;</div><?
						?></td><?

						?><td class="action-accept"><?
						?><div <?
						?> class="action-ok action-act ib"<?
						?> onclick="Pf.changeAppicRequestStatus(this, <?= $update['ID'] ?>, 'accepted')"<?
						?> onmouseover="Pf.changeRequestLine(this, 'hilited-green', true)"<?
						?> onmouseout="Pf.changeRequestLine(this, 'hilited-green', false)"<?
						?>>&#10004;</div><?
						?></td><?

						if ($action !== 'event_cancel') {
							$pf_element = APPIC_ELEMENT_TO_PARTYFLOCK[$update['ELEMENT']] ?? null;
							?><td><?
							if ($pf_element) {
								?><a <?
								?> class="ib"<?
								?> target="_blank"<?
								?> href="/<?= $pf_element ?>/register?REQUEST_FROM_APPIC=<?= $update['ID'] ?>"<?
								?>><?=
									get_special_char('add', [
										'class'			=> 'action-new ib',
										'onmouseover'	=> "Pf.changeRequestLine(this, 'hilited', true)",
										'onmouseout'	=> "Pf.changeRequestLine(this, 'hilited', false)",
									])
								?></a><?
							}
							?></td><?
						}

						?><td class="fw"><?

						switch ($action) {
						case 'event_cancel':
						case 'concept_main':
						case 'event_main':
						case 'venue_main':
						case 'new_event':
						case 'new_event_group':
							if (empty($update['obj']['data'])) {
								if (!$update['PF_ID']
								&&	$update['APPIC_ID']
								) {
									$update['PF_ID'] =
										$element === 'event'
									?	db_single('appic_event', 'SELECT PARTYID FROM appic_event WHERE APPICID='.$update['APPIC_ID'])
									:	db_single('appic_element', 'SELECT ID FROM appic_element WHERE ELEMENT="'.$element.'" AND APPICID='.$update['APPIC_ID']);
								}
								if ($update['PF_ID']) {
									echo get_element_link($pf_element, $update['PF_ID'], target: '_blank');
								}
								break;
							}
							$obj = $update['obj']['data'];
							show_update_item($obj);
							break;

						default:
							show_update_item_generic($update['obj']['data'], skip_fields: ['type']);
							break;
						}
						?></td><?

						# support new event
						?></tr><?
					}
					?></tbody><?
					?></table><?
				}
			}
		}
	}
	layout_close_box();
	return true;
}

function show_update_item_generic(
	array	$array,
	string	$prefix				= '',
	array	$skip_fields		= [],
	array	$skip_fields_always = [],
): void {
	if (!$prefix) {
		?><table class="body vtop"><?
	}
	foreach ($array as $key => $value) {
		if (!$value
		||	$skip_fields
		&&	in_array($key, $skip_fields, true)
		||	$skip_fields_always
		&&	in_array($key, $skip_fields_always, true)
		) {
			continue;
		}
		if (is_array($value)) {
			show_update_item_generic(
				$value,
				$key.'.',
				skip_fields_always: $skip_fields_always
			);
		} else {
			if ($key === 'user_data') {
				$key = 'user';
			}
			?><tr><?
			?><td class="light7 nowrap"><?= escape_specials($prefix.$key) ?></td><?
			?><td><?
			if ($key === 'user'
			&&	preg_match('"^(?<name>.*?)\s*\((?<email>.*?)\)$"', $value, $user)
			) {
				$to_userid = db_single(['user', 'user_account'], '
					SELECT USERID
					FROM user
					JOIN user_account USING (USERID)
					WHERE EMAIL = "'.addslashes($user['email']).'"
					  AND STATUS = "active"
					ORDER BY USERID DESC
					LIMIT 1'
				);
				?><a class="link" target="_blank" href="/ticket/outgoing-form<?
					?>?TO_EMAIL=<?= escape_utf8($user['email'])
					?>;TO_NAME=<? echo escape_utf8($user['name']);
					if ($to_userid) {
						?>;TO_USERID=<? echo $to_userid;
					}
					?>"><?= escape_utf8($user['name']) ?></a><?

			} elseif (str_starts_with($value, 'http')) {
				print_rr($value);
				?><a target="_blank" class="link" href="<?= escape_utf8($value) ?>"><?
				if (str_contains($value, 'facebook.com')) {
					echo get_facebook_icon();
					?> <?
				}
				echo escape_utf8($value);
				?></a><?
			} else {
				require_once '_ubb_preprocess.inc';
				require_once '_ubb.inc';
				echo make_all_html(_ubb_preprocess($value, utf8: true), UBB_UTF8);
			}
			?></td><?
			?></tr><?
		}
	}
	if (!$prefix) {
		?></table><?
	}
}

function show_update_item(array $obj): void {
	$skip_fields = ['name', 'subtitle', 'start', 'end'];

	?><b><?= escape_utf8($obj['name']) ?></b><?

	if (!empty($obj['subtitle'])) {
		?> <small>&middot; <?= escape_utf8($obj['subtitle']) ?></small><?
	}
	if (!empty($obj['facebook_page_url'])) {
		?> <a target="_blank" href="<?= escape_utf8($obj['facebook_page_url']) ?>"><?= get_facebook_icon() ?></a><?
	}
	?><br /><?
	if (!empty($obj['start'])) {
		$start_stamp = strtotime(str_replace('/', '-', $obj['start']));
		_datedaytime_display($start_stamp);
		if (!empty($obj['end'])) {
			$stop_stamp = strtotime(str_replace('/', '-', $obj['end']));
			?> &ndash; <? _time_display($stop_stamp);
		}
		?><br /><?
	}
	if (!empty($obj['address']['venue_id'])) {
		$locationid = db_single('appic_element', '
			SELECT ID FROM appic_element
			WHERE ELEMENT = "location"
			AND APPICID = '.$obj['address']['venue_id']
		);
		if ($locationid) {
			$skip_fields[] = 'address';
			echo get_element_link('location', $locationid);
		}
	}
	show_update_item_generic(
		$obj,
		skip_fields: $skip_fields,
		skip_fields_always: ['venue_id', 'location_type'],
	);
}

function change_appic_request_status(): int {
	require_once '_http_status.inc';
	require_once '__translation.php';
	if (!(	$request_idstr =
			require_idnumber($_POST, 'ID')
		?:	require_number_list($_POST, 'ID'))
	||	!($status = require_element($_POST, 'STATUS', ['accepted', 'declined', 'exists']))
	) {
		return http_status::BAD_REQUEST->value;
	}
	global $currentuser;
	$currentuser ??= new _currentuser(TINY_USER | SKIP_SETTINGS);
	if (!require_admin('party')) {
		return http_status::FORBIDDEN->value;
	}
	if (!($requests = db_rowuse_array('update_from_appic', "
		SELECT JSON, STATUS, DONE_STAMP, DONE_BY, ELEMENT, PF_ID, CALLBACK_URL, CHANGE_REQUEST_ID, ACTION
		FROM update_from_appic
		WHERE ID IN ($request_idstr)"))
	) {
		return ($requests === false ? http_status::SERVICE_UNAVAILABLE : http_status::NOT_FOUND)->value;
	}
	foreach ($requests as $request) {
		if ($request['DONE_STAMP']) {
			if ($request['DONE_BY'] === CURRENTUSERID) {
				return http_status::OK->value;
			}
			register_notice('appic_request:notice:already_processed_LINE', DO_UBB, ['DONE_BY' => $request['DONE_BY']]);
			return http_status::GONE->value;
		}
		if (!require_something($request, 'CALLBACK_URL')) {
			return http_status::PRECONDITION_FAILED->value;
		}
	}
	foreach ($requests as &$request) {
		$request['need_pf_id'] = ($status !== 'declined' && isset(ACTION_NEEDS_ACTIVE_ITEM[$request['ACTION']]));
	}
	unset($request);
	if (false === ($active_item = db_single_assoc('update_from_appic_active_item', '
		SELECT ELEMENT, ID
		FROM update_from_appic_active_item
		WHERE USERID = '.CURRENTUSERID))
	) {
		return http_status::SERVICE_UNAVAILABLE->value;
	}
	foreach ($requests as $request) {
		if (!isset(APPIC_ELEMENT_TO_PARTYFLOCK[$request['ELEMENT']])) {
			mail_log('APPIC_ELEMENT_TO_PARTYFLOCK '.$request['ELEMENT'].' does not exist');
		}
		if ($request['need_pf_id']
		&&	(	!$active_item
			||	!isset(APPIC_ELEMENT_TO_PARTYFLOCK[$request['ELEMENT']])
			||	APPIC_ELEMENT_TO_PARTYFLOCK[$request['ELEMENT']] !== $active_item['ELEMENT']
			)
		) {
			register_error('appic_request:error:element_mismatch_LINE');
			return http_status::PRECONDITION_FAILED->value;
		}
	}
	foreach ($requests as $request) {
		if ($status === 'exists') {
			$appic_status = $request['ACTION'] === 'missing_event' ? 'declined' : $status;
			$reason = 'already_there';
		} else {
			$appic_status = $status;
		}
		$response = [
			'status' => $appic_status,
		];
		if ($request['CHANGE_REQUEST_ID']) {
			$response['change_request_id'] = $request['CHANGE_REQUEST_ID'];
		}
		$pf_id = null;
		if ($request['need_pf_id']
		&&	$active_item['ELEMENT']
		&&	$active_item['ID']
		) {
			$pf_id = $active_item['ID'];
			$response['pf_id'] = $pf_id;
		}
		if (!empty($reason)) {
			$response['reason'] = $reason;
		}
		if (!($rc = call_appic_callback($request['CALLBACK_URL'], $response))) {
			return http_status::SERVICE_UNAVAILABLE->value;
		}
		[, $info] = $rc;
		if ($info['http_code'] !== 200) {
			return $info['http_code'];
		}
	}
	if (!done_update_from_appic($request_idstr, $status, $pf_id)) {
		return http_status::SERVICE_UNAVAILABLE->value;
	}
	return http_status::OK->value;
}

function done_update_from_appic(string|int $request_idstr, string $appic_status, ?int $pf_id = null): bool {
	return	db_update('update_from_appic','
		UPDATE update_from_appic SET
			STATUS		="'.$appic_status.'",
			DONE_BY		='.CURRENTUSERID.',
			DONE_STAMP	='.CURRENTSTAMP.
			($pf_id ? ', PF_ID = IF(PF_ID, PF_ID, '.$pf_id.')' : '').'
		WHERE ID IN ('.$request_idstr.')'
	);
}

function call_appic_callback(string $url, array $response): array|false {
	$ch = curl_init();
	if (!$ch) {
		return false;
	}
	curl_setopt_array($ch, [
		CURLOPT_RETURNTRANSFER	=> true,
		CURLOPT_FOLLOWLOCATION	=> true,
		CURLOPT_HTTPHEADER		=> [
			'X-DATA-INTEGRATION-API-KEY: '.(SERVER_SANDBOX ? APPIC_API_DEV_KEY : APPIC_API_KEY),
			'Content-Type: application/json',
		],
		CURLOPT_URL				=> $url,
		CURLOPT_POSTFIELDS		=> safe_json_encode($response),
	]);
	$data = curl_exec($ch);
	$info = curl_getinfo($ch);
	return [$data, $info];
}

function passthrough_update_from_appic(): void {
	if (!have_admin('party')) {
		return;
	}
	if (!($request_id = have_idnumber($_REQUEST, 'REQUEST_FROM_APPIC'))) {
		return;
	}
	if (!($request = db_single_assoc('update_from_appic','
		SELECT DONE_STAMP
		FROM update_from_appic
		WHERE ID = '.$request_id
	))) {
		if ($request !== false) {
			register_error('appic_request:error:nonexistent_LINE');
		}
		return;
	}
	if ($request['DONE_STAMP']) {
		register_error('appic_request:error:already_completed_LINE');
		return;
	}
	?><input type="hidden" name="REQUEST_FROM_APPIC" value="<?= $request_id ?>" /><?
}

function cleanup_update_from_appic(int $pf_id): ?bool {
	if (!($request_id = have_idnumber($_POST, 'REQUEST_FROM_APPIC'))) {
		return false;
	}
	if (!($request = db_single_assoc('update_from_appic', "
		SELECT *
		FROM update_from_appic
		WHERE ID = $request_id"))
	) {
		if ($request === false) {
			return false;
		}
		return null;
	}
	if (!update_active_item($_REQUEST['sELEMENT'], $pf_id)) {
		return false;
	}
	$response = [
		'status' => 'accepted',
		'pf_id'	 => $_REQUEST['sID'],
	];
	if ($request['CHANGE_REQUEST_ID']) {
		$response['change_request_id'] = $request['CHANGE_REQUEST_ID'];
	}
	if (!($rc = call_appic_callback($request['CALLBACK_URL'], $response))) {
		return false;
	}
	[/* $data */, $info] = $rc;
	if ($info['http_code'] !== 200) {
		register_error('process_appic_changes:error:returned_non_200_LINE');
		return false;
	}
	return done_update_from_appic($request_id, 'accepted', $pf_id);
}

function check_if_request_is_done(): int {
	if (!($id = require_idnumber($_POST, 'ID'))) {
		return http_status::BAD_REQUEST->value;
	}
	$done = db_single('update_from_appic', "
		SELECT STATUS = 'accepted'
		FROM update_from_appic
		WHERE ID = $id"
	);
	return ($done ? http_status::NOT_FOUND : http_status::OK)->value;
}

function alter_active_item(int $action): int {
	if (!require_post()) {
		return http_status::METHOD_NOT_ALLOWED->value;
	}
	global $currentuser;
	$currentuser ??= new _currentuser(TINY_USER | SKIP_SETTINGS);
	if (!require_admin('party')) {
		return http_status::FORBIDDEN->value;
	}
	if (!db_insert('update_from_appic_active_item_log', '
		INSERT INTO update_from_appic_active_item_log
		SELECT *, '.CURRENTSTAMP.'
		FROM update_from_appic_active_item
		WHERE USERID = '.CURRENTUSERID)
	||	$action === REMOVE_ACTIVE_ITEM
	?	!db_delete('update_from_appic_active_item','
		DELETE FROM update_from_appic_active_item
		WHERE USERID = '.CURRENTUSERID)
	:	!db_replace('update_from_appic_active_item','
		REPLACE INTO update_from_appic_active_item SET
			USERID	='.CURRENTUSERID.',
			ELEMENT	= NULL,
			ID	= NULL')
	) {
		return http_status::SERVICE_UNAVAILABLE->value;
	}
	return http_status::OK->value;
}

function get_active_item(): int {
	require_once '_currentuser.inc';
	require_once '_nocache.inc';
	send_no_cache_headers();
	global $currentuser;
	$currentuser ??= new _currentuser(TINY_USER | SKIP_SETTINGS);
	if (!require_admin('party')) {
		return http_status::FORBIDDEN->value;
	}
	if (false === ($active_item = db_single_assoc('update_from_appic_active_item', '
		SELECT ELEMENT, ID
		FROM update_from_appic_active_item
		WHERE USERID = '.CURRENTUSERID))
	) {
		return http_status::SERVICE_UNAVAILABLE->value;
	}
	if (!$active_item) {
		return http_status::GONE->value;
	}
	if (empty($active_item['ELEMENT'])
	&&	empty($active_item['ID'])
	) {
		return http_status::NO_CONTENT->value;
	}
	header('Content-Type: application/json');
	echo safe_json_encode([
		'element'		=> $active_item['ELEMENT'],
		'id'			=> $active_item['ID'],
		'elementName'	=> $active_item['ELEMENT']	? element_name	  ($active_item['ELEMENT'])							: null,
		'itemLink'		=> $active_item['ID']		? get_element_link($active_item['ELEMENT'], $active_item['ID'])	: null,
	]);
	return http_status::OK->value;
}

function show_active_item_overlay(): void {
	if (!have_admin('party')
	||	false === ($active_item = db_single_assoc('update_from_appic_active_item', '
		SELECT ELEMENT, ID
		FROM update_from_appic_active_item
		WHERE USERID='.CURRENTUSERID))
	) {
		return;
	}
	$update = false;
	$element = null;
	if (!$active_item) {
		if (!str_contains($_SERVER['REQUEST_URI'], '/test/process/change-requests')) {
			return;
		}
		# force new active token
		$id		= null;
		$update = true;
	} elseif(
		($id = $_REQUEST['sID'])
	&&	($element = in_array($_REQUEST['sELEMENT'], ['artist', 'location', 'organization', 'party'], true) ? $_REQUEST['sELEMENT'] : null)
	&&	(	$id		 !== $active_item['ID']
		||	$element !== $active_item['ELEMENT'])
	) {
		$update = true;
	}

	if ($update) {
		if (!update_active_item($element, $id)) {
			return;
		}
		$active_item = [
			'ELEMENT'=> $element,
			'ID'	 => $id,
		];
	}

	include_js('js/form/appicchangerequests');
	include_style('appic_requests');

	?><div class="appic-changes" id="active-item"><?
	?><div class="abs active-item"><?=
		get_close_char([
			'id'		=> 'close-active',
			'onclick'	=> /** @lang JavaScript */ 'Pf_removeActiveItemForAppic(true);',
		])
	?> <?=
		get_remove_char([
			'id'		=> 'remove-active',
			'onclick'	=> /** @lang JavaScript */ 'Pf_removeActiveItemForAppic();',
			'class'		=> !empty($active_item['ELEMENT'])
						&& !empty($active_item['ID']) ? '' : 'hidden',
		])
	?> <?
	?><span id="element-name"><?= $active_item['ELEMENT'] ? element_name($active_item['ELEMENT']) : '' ?></span><?
	?> <?
	?><span id="item-link"><?= $active_item['ID'] ? get_element_link($active_item['ELEMENT'], $active_item['ID']) : '' ?></span><?
	?></div><?
	?></div><?
}

function update_active_item(
	#[ExpectedValues(values: [null, '', 'event', 'concept', 'artist', 'venue', 'organization', 'product', 'timetable_report', 'location'])]
	?string $element = null,
	?int $id = null
): bool {
	return	db_insert('update_from_appic_active_item_log', '
		INSERT INTO update_from_appic_active_item_log
		SELECT *, '.CURRENTSTAMP.'
		FROM update_from_appic_active_item
		WHERE USERID = '.CURRENTUSERID)
	&&	db_insupd('update_from_appic_active_item', '
		INSERT INTO update_from_appic_active_item SET
			USERID	= '.CURRENTUSERID.',
			ELEMENT	= '.($element === null ? 'NULL' : "'$element'").',
			ID		= '.($id	  === null ? 'NULL' : (string)$id).'
		ON DUPLICATE KEY UPDATE
			ELEMENT	= VALUE(ELEMENT),
			ID		= VALUE(ID)');
}
