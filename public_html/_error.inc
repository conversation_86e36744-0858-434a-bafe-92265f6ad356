<?php

declare(strict_types=1);

function have_errors(): bool {
	return isset($GL<PERSON><PERSON><PERSON>['__error']);
}

function have_messages(): bool {
	return	isset($GLOBALS['__error'])
		||	isset($GL<PERSON><PERSON><PERSON>['__warning'])
		||	isset($GL<PERSON><PERSON>LS['__notice']);
}

function clear_messages(): void {
	unset(	$GLOBALS['__error'],
			$GL<PERSON><PERSON>LS['__warning'],
			$<PERSON>L<PERSON><PERSON>LS['__notice'],
	);
}

function _message(string $severity, string|array $arg): void {
	$list_name = '__'.$severity;
	$all_list_name = 'all__'.$severity;
	# ^^ all used by count_pagehit
	if (is_array($arg)) {
		foreach ($arg as $txt) {
			$GLOBALS[$list_name][] = $txt;
			$GLOBALS[$all_list_name][] = $txt;
		}
	} else {
		$GLOBALS[$list_name][] = $arg;
		$GLOBALS[$all_list_name][] = $arg;
	}
}

function register_message(string $severity, string $key, $arg1 = null, $arg2 = null): void {
	require_once '__translation.php';
	_message($severity, __($key, $arg1, $arg2));
}

function register_error(string $key, $arg1 = null, $arg2 = null, bool $send_email = false): void {
	if ($send_email) {
		mail_log(
			preg_match('"^([a-z_-]+):error:(?P<message>.*?)_(?:TEXT|LINE)$"i', $key, $match)
			?	str_replace('_', ' ', $match['message'])
			:	$key,
			item: get_defined_vars()
		);
	}
	register_message('error', $key, $arg1, $arg2);
}

function get_generic_error(): string {
	ob_start();
	register_generic_error();
	return ob_get_clean();
}

function register_generic_error(): string {
	$code = dechex(safe_random_int());
	error_log('ERROR generic error with CODE = '.$code);
	register_error('error:generic_LINE', ['CODE' => $code]);
	return $code;
}

function get_nonexistent(string $element, int $id): string|false {
	return __('error:nonexistent_LINE', DO_UBB, [
		'ID'			=> $id,
		'ELEMENT'		=> $element,
	]);
}

function register_nonexistent(string $element, int $id): void {
	_error(get_nonexistent($element, $id));
}

function register_warning(string $key, $arg1 = null, $arg2 = null): void {
	register_message('warning', $key, $arg1, $arg2);
}

function register_notice(string $key, $arg1 = null, $arg2 = null): void {
	register_message('notice', $key, $arg1, $arg2);
}

function _error(): void {
	$arg = func_num_args() === 1 ? func_get_arg(0) : func_get_args();
	_message('error', $arg);
}

function _notice(): void {
	$arg = func_num_args() === 1 ? func_get_arg(0) : func_get_args();
	_message('notice', $arg);
}

function warning(): void {
	$arg = func_num_args() === 1 ? func_get_arg(0) : func_get_args();
	_message('warning', $arg);
}

function display_messages_only(bool $ajax = false): void {
	$args = func_get_args();
	if (empty($args)) {
		$args = ['error', 'warning', 'notice'];
	}
	if ($ajax || isset($_POST['AJAX'])) {
		header('Content-Type: text/html; charset=windows-1252');
	}
	foreach ($args as $type) {
		$ndx = '__'.$type;
		if (!isset($GLOBALS[$ndx])) {
			continue;
		}
		?><div class="<?= $type ?> block"><?= implode('<br />', $GLOBALS[$ndx]) ?></div><?
		unset($GLOBALS[$ndx]);
	}
}

function display_messages(): void {
	static $__load_and_show_header = false;
	if (!$__load_and_show_header) {
		?><div id="messages" class="clear"><?
		load_messages();
	}
	if (isset($GLOBALS['__error'])
	||	isset($GLOBALS['__warning'])
	||	isset($GLOBALS['__notice'])
	) {
		display_messages_only();
	}
	if (!$__load_and_show_header) {
		$__load_and_show_header = true;
		?></div><?
	}
}

function store_messages(): int|false|null {
	if (empty($GLOBALS['__error'])
	&&	empty($GLOBALS['__notice'])
	&&	empty($GLOBALS['__warning'])
	) {
		return null;
	}
	if (db_insert('messages', '
		INSERT INTO messages SET
			ERRORS	 = "'.(empty($GLOBALS['__error'])   ? '' : addslashes(igbinary_serialize($GLOBALS['__error']))).'",
			NOTICES	 = "'.(empty($GLOBALS['__notice'])  ? '' : addslashes(igbinary_serialize($GLOBALS['__notice']))).'",
			WARNINGS = "'.(empty($GLOBALS['__warning']) ? '' : addslashes(igbinary_serialize($GLOBALS['__warning']))).'",
			USERID	 = '.(defined('CURRENTUSERID') ? CURRENTUSERID : 1).',
			CSTAMP	 = '.CURRENTSTAMP)
	) {
		return db_insert_id();
	}
	return false;
}

function store_messages_in_cookie(): int|false|null {
	if (!($message_id = store_messages())) {
		return $message_id;
	}
	require_once '_cookie.inc';
	setflockcookie('FLOCK_MESSAGE_ID', $message_id, ONE_MINUTE);
	return $message_id;
}

function was_redirected(): bool {
	return isset($_REQUEST['message_id'])
		|| isset($_COOKIE['FLOCK_MESSAGE_ID']);
}

function preload_messages(): int|string|null {
	static $__message_id = null;
	if (  $__message_id === null
	&&	!($__message_id = $_REQUEST['message_id'] ?? null)
	&&	 ($__message_id = $_COOKIE['FLOCK_MESSAGE_ID'] ?? null)
	) {
		require_once '_cookie.inc';
		clearflockcookie('FLOCK_MESSAGE_ID');
	}
	return $__message_id = ($__message_id === 'x' ? 'x' : (int)$__message_id);
}

function load_messages(): void {
	if (!($message_id = preload_messages())) {
		return;
	}
	static $__messages_loaded = false;
	if ($__messages_loaded) {
		return;
	}
	$__messages_loaded = true;

	if ($message_id === 'x') {
		$code = dechex(safe_random_int());
		error_log('ERROR generic error (1) with CODE = '.$code);
		$GLOBALS['__error'][] = __C('error:generic_LINE', ['CODE' => $code]);

	} elseif (
		is_number($message_id)
	&&	($messages = db_single_assoc('messages', '
		SELECT SQL_NO_CACHE ERRORS, NOTICES, WARNINGS
		FROM messages
		WHERE USERID = '.CURRENTUSERID.'
		  AND MSGID = '.$message_id,
		DB_USE_MASTER
	))) {
		foreach ([
			'ERRORS'	=> '__error',
			'WARNINGS'	=> '__warning',
			'NOTICES'	=> '__notice'
		] as $src => $dst) {
			if (empty($messages[$src])) {
				continue;
			}
			$extra_messages = igbinary_unserialize($messages[$src]);
			$GLOBALS[$dst] ??= [];
			$GLOBALS[$dst] = array_push($GLOBALS[$dst], $extra_messages);
		}
	}
}

function show_messages_cli(): int {
	# returns number of errors and warnings shown
	require_once '_syslog.inc';

	global $argv;

	$shown_header = false;
	$message_count = 0;

	if (isset($GLOBALS['__error'])) {
		echo basename($argv[0]), " FAILED:\n\n";
		foreach ($GLOBALS['__error'] as $error) {
			echo $error, "\n";
			syslog_error(LOG_ERR, $error);
		}
		$shown_header = true;
		++$message_count;
	}
	if (isset($GLOBALS['__warning'])) {
		echo	$shown_header
			?	"\nand warnings:\n\n"
			:	basename($argv[0])." succeeded, but with warnings:\n\n";

		foreach ($GLOBALS['__warning'] as $warning) {
			echo $warning, "\n";
			syslog_message(LOG_WARNING, $warning);
		}
		$shown_header = true;
		++$message_count;
	}
	if (isset($GLOBALS['__notice'])) {
		echo	$shown_header
			?	"\nand notices:\n\n"
			:	basename($argv[0])." succeeded with following notices:\n\n";

		foreach ($GLOBALS['__notice'] as $notice) {
			echo $notice, "\n";
 			syslog(LOG_NOTICE, $notice);
		}
	}
	return $message_count;
}

function show_messages_headers(): void {
	foreach (['error', 'warning', 'notice'] as $type) {
		$var = '__'.$type;
		if (empty($GLOBALS[$var])) {
			continue;
		}
		foreach ($GLOBALS[$var] as $str) {
			header('Pf-'.ucfirst($type).': '.win1252_to_utf8($str));
		}
	}
}

function show_page_problem(?array $force = null): void {
	global $page_problem;

	[$status, $element, $id, $desc] = $force ?: $page_problem;

	db_insert('pageproblem', '
	INSERT INTO pageproblem SET
		ROBOT	= '.(ROBOT ? "b'1'" : "b'0'").',
		STAMP	= '.CURRENTSTAMP.',
		USERID	= '.CURRENTUSERID.',
		IDENTID	= '.CURRENTIDENTID.",
		URI		= '".addslashes($_SERVER['REQUEST_URI'])."',
		REF 	= '".addslashes($_SERVER['HTTP_REFERER'] ?? '')."'"
	);

	if (!empty($GLOBALS['__error'])) {
		# have an explicit error, no need to display genericx
		return;
	}

	if ($status === 404) {
		if ($element) {
			?><div class="error block"><?= get_nonexistent($element,$id) ?></div><?
		} else {
			?><div class="error block"><?= __('errorpage:header:404_LINE') ?></div><?
		}

		?><div class="block"><?= __('errorpage:info:404_TEXT',DO_UBB, [
			'REFERER'	=> $_SERVER['HTTP_REFERER'] ?? null
		])
		?></div><?
	} elseif ($desc) {
		?><div class="error block"><?= $desc ?></div><?
	} else {
		require_once '_http_status.inc';
		?><div class="error block"><?= __('errorpage:header:generic_LINE', ['STATUS' => $status]) ?> <?= get_http_status($status) ?></div><?
	}
}
