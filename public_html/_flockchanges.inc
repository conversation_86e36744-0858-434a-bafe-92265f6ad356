<?php

function show_flock_changes($flock) {
	if (!$flock['MSTAMP']) {
		return;
	}
	$flockid = $flock['FLOCKID'];
	$logs = db_rowuse_array('flock_log','
		SELECT CUSERID,CSTAMP,MUSERID,MSTAMP,NAME,LEADERID,ACCESS
		FROM flock_log
		WHERE FLOCKID='.$flockid.'
		ORDER BY MSTAMP ASC'
	);
	if (!$logs) {
		return;
	}
	$logs[] = $flock;
	$prevlog = null;
	layout_open_box('white');
	layout_open_table(TABLE_FULL_WIDTH | TABLE_ROW_HILITE_ONMOUSEOVER);
	layout_start_header_row();
	layout_header_cell_right(Eelement_name('date'),CELL_RIGHT_SPACE);
	layout_header_cell(Eelement_name('modifier'));
	layout_header_cell(Eelement_name('name'));
	layout_header_cell(Eelement_name('leader'));
	layout_header_cell(Eelement_name('access'));
	layout_stop_header_row();
	foreach ($logs as $log) {
		if ($prevlog) {
			$differ = 0;
			$identical = null;
			foreach (array('NAME','LEADERID','ACCESS') as $key) {
				if ($prevlog[$key] == $log[$key]) {
					$identical[$key] = true;
				} else {
					++$differ;
				}
			}
			if (!$differ) {
				continue;
			}
		}
		layout_start_rrow_right(CELL_RIGHT_SPACE | NOWRAP);
		_datetime_display_numeric($log[$log['MSTAMP'] ? 'MSTAMP' : 'CSTAMP']);
		layout_next_cell();
		echo get_element_link('user',$log[$log['MUSERID'] ? 'MUSERID' : 'CUSERID']);
		layout_next_cell(class: $prevlog ? (isset($identical['NAME']) ? 'light' : 'bold') : '');
		echo escape_utf8($log['NAME']);
		layout_next_cell(class: $prevlog ? (isset($identical['LEADERID']) ? 'light' : 'bold') : '');
		echo get_element_link('user',$log['LEADERID']);
		layout_next_cell(class: $prevlog ? (isset($identical['ACCESS']) ? 'light' : 'bold') : '');
		echo flock_access_name($log['ACCESS']);
		layout_stop_row();
		$prevlog = $log;
	}
	layout_close_table();
	layout_close_box();
}
