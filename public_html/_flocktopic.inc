<?php

require_once '_pagecache.inc';

function get_flocktopic($topicid) {
	return db_single_assoc(
		array('flocktopic','flock'),'
		SELECT  FLAGS,TOPICID,SUBJECT,USERID,MSGCNT,STATUS,STICKY,FIRST_MESSAGEID,LSTAMP,flocktopic.MSTAMP,flocktopic.MUSERID,flocktopic.CSTAMP,
			flocktopic.FLOCKID,NAME,REMOVED,PRIVATE,ACCEPTED
		FROM flocktopic
		JOIN flock USING (FLOCKID)
		WHERE TOPICID='.$topicid
	);
}

function update_flockmessage(int $topicid, int $messageid, int $post_flags = 0) {
	require_once 'defines/topic.inc';
	require_once 'defines/post.inc';
	require_once '_post.inc';
	require_once '_quotes.inc';
	require_once '_ubb_preprocess.inc';
	if (!require_user()
	||	!require_something_trim($_POST, 'BODY', utf8: true)
	) {
		return false;
	}
	$flockmessage = db_single_assoc(
		array('flockmessage','flocktopic','poll'),'
		SELECT	flockmessage.CSTAMP,flockmessage.USERID,flockmessage.MUSERID,MSGNO,BODY,flockmessage.FLAGS AS MESSAGE_FLAGS,
			TOPICID,FLOCKID,flocktopic.STATUS,flocktopic.FLAGS,FIRST_MESSAGEID,
			POLLID
		FROM flockmessage
		LEFT JOIN flocktopic USING (TOPICID)
		LEFT JOIN poll ON MESSAGEID=FIRST_MESSAGEID AND poll.ELEMENT="flocktopic" AND ELEMENTID=flockmessage.TOPICID
		WHERE MESSAGEID='.$messageid
	);
	if ($flockmessage === false) {
		return false;
	}
	if (!$flockmessage) {
		register_error('flockmessage:error:nonexistent_LINE', ['ID' => $messageid]);
		return false;
	}
	if (!$flockmessage['FLOCKID']) {
		register_error('flockmessage:error:no_topic_LINE', ['MESSAGEID' => $messageid]);
		return false;
	}
	if ($flockmessage['TOPICID'] !== $topicid) {
		register_error('flockmessage:error:in_wrong_topic_LINE', ['MESSAGEID' => $messageid, 'TOPICID' => $topicid]);
		return false;
	}
	if ($is_poll =	$flockmessage['FLAGS'] & TOPIC_IS_POLL
				&&	$messageid === $flockmessage['FIRST_MESSAGEID']
				&&	isset($_POST['QUESTION'])
	) {
		require_once '_poll.inc';
		if (!poll_post_valid()) {
			return false;
		}
	} else {
		if (!require_something_trim($_POST, 'BODY', null, null, utf8: true)) {
			return false;
		}
		if ($_POST['BODY'] == '') {
			_error(__('message:error:need_content_LINE'));
			return false;
		}
	}
	$body = isset($_POST['BODY']) ? $_POST['BODY'] : '';
	$body = _ubb_preprocess($body, utf8: true);
	$flags = get_post_flags('flockmessage', $flockmessage['MESSAGE_FLAGS'], $post_flags);

	if ($flockmessage['BODY'] === $body
	&&	$flockmessage['MESSAGE_FLAGS'] === $flags
	) {
		register_warning('message:warning:same_LINE');
		return false;
	}
	if (have_admin('flocktopic')
	&&	CURRENTUSERID != $flockmessage['USERID']
	) {
		$flags |= POST_CHANGED_BY_ADMIN;
	}
	if (!db_update(
		'flockmessage','
		UPDATE flockmessage SET
			MSTAMP	='.CURRENTSTAMP.',
			MUSERID	='.CURRENTUSERID.',
			BODY	="'.addslashes($body).'",
			FLAGS	='.($post_flags | $flags).'
		WHERE MESSAGEID='.$messageid.'
		  AND TOPICID='.$topicid)
	) {
		return false;
	}
	if ($is_poll
	&&	!commit_poll('flocktopic',$flockmessage['TOPICID'],$flockmessage['POLLID'])
	) {
		return false;
	}

	sync_topic_flags(CONTEXT_FLOCK, $flockmessage['MESSAGE_FLAGS'], $post_flags, $topicid, $flockmessage['FIRST_MESSAGEID'], $messageid);

	require_once '_plaintext.inc';
	store_plain_text('flockmessage', $messageid, $body, utf8: true);
	process_quotes($body, 'flockmessage', $messageid, $flockmessage['TOPICID'], $flockmessage['USERID'], CURRENTSTAMP, true);
	flush_page_cache('flocktopic', $topicid, !empty($_REQUEST['PAGE']) ? $_REQUEST['PAGE'] : 0);
	taint_include_cache('flockmessage', $messageid, $body);
	return true;
}

function add_flockmessage($topicid) {
	require_once '_counthit.inc';
	require_once '_pagecache.inc';
	require_once '_quotes.inc';
	require_once '_topicactions.inc';
	require_once '_post.inc';
	require_once '_postedin.inc';
	require_once '_subscriptions.inc';
	require_once '_messageindex.inc';
	require_once '_ubb_preprocess.inc';
	if (!require_user()
	||	!require_idnumber($_POST, 'LAST_MESSAGEID')
	||	false === require_number($_POST, 'FORMSTAMP')
	||	!require_something_trim($_POST, 'BODY', null, null, utf8: true)
	||	!require_valid_origin_or_secret()
	) {
		return false;
	}
	if ($_POST['BODY'] == '') {
		_error(__('message:error:need_content_LINE'));
		return false;
	}
	$topicexists = db_single('flockmessage','
		SELECT TOPICID
		FROM flockmessage
		WHERE MESSAGEID>='.$_POST['LAST_MESSAGEID'].'
		  AND FORMSTAMP='.$_POST['FORMSTAMP'].'
		  AND USERID='.CURRENTUSERID
	);
	if ($topicexists === false) {
		return false;
	}
	if ($topicexists) {
		warning(__('message:warning:message_already_exists_LINE'));
		return false;
	}
	if (!require_getlock($lockname = 'flocktopiclock:'.$topicid,15)) {
		return false;
	}
	$intopic = db_single_assoc(['flock',' flocktopic'],'
		SELECT REMOVED, FLOCKID, ACCEPTED, MSGCNT, STATUS, STICKY, LSTAMP, FIRST_MESSAGEID
		FROM flocktopic
		JOIN flock USING (FLOCKID)
		WHERE TOPICID='.$topicid
	);
	if ($intopic === false) {
		return false;
	}
	if (!$intopic) {
		_error(__('flocktopic:error:nonexistent_LINE',array('ID'=>$topicid)));
		return false;
	}

	require_once '_maxposts.inc';
	if (max_new_posts('flockmessage')) {
		return false;
	}

	// FIXME: check status of flock and topic somewhere (ACCEPTED, REMOVED, STATUS)
	if (!have_admin(array('flock','flocktopic'))) {
		require_once '_comment.inc';
		if (!may_reply_to_comment('flocktopic',$topicid,$intopic)) {
			register_error('flocktopic:error:may_not_reply_LINE');
			error_log_r($topic,CURRENTUSERID.' WARNING may not reply in topic:'.$topicid);
			return false;
		}
	}

	$body = _ubb_preprocess($_POST['BODY'], utf8: true);

	// was INSERT INTO
	if (!db_insert('flockmessage','
		INSERT INTO flockmessage SET
			MSGNO		='.($msgno = ($intopic['MSGCNT'] ? $intopic['MSGCNT'] + 1 : 0)).',
			USERID		='.CURRENTUSERID.',
			BODY		="'.addslashes($body).'",
			TOPICID		='.$topicid.',
			CSTAMP		='.CURRENTSTAMP.',
			MSTAMP		='.CURRENTSTAMP.',
			FORMSTAMP	='.$_POST['FORMSTAMP'].',
			FLAGS		='.get_post_flags('flockmessage'))
	) {
		return false;
	}
	$messageid = db_insert_id();

	require_once '_plaintext.inc';
	store_plain_text('flockmessage', $messageid, $body, utf8: true);

	if (!update_topicstats('flocktopic', $topicid,'
		UPDATE flocktopic SET
			LSTAMP		='.CURRENTSTAMP.',
			LUSERID		='.CURRENTUSERID.',
			MSGCNT		=MSGCNT + 1
		WHERE TOPICID='.$topicid)
	) {
		return false;
	}
	db_releaselock($lockname);

	sync_topic_flags(CONTEXT_FLOCK, $info['MESSAGE_FLAGS'], $post_flags, $info);

	update_subscription('flocktopic',$topicid,CURRENTUSERID,CURRENTSTAMP,$messageid,$msgno);
	update_message_index('flock',$intopic['FLOCKID'],$messageid,CURRENTUSERID);
	update_postedin('flocktopic',$topicid,$messageid,$msgno);
	process_quotes($body,'flockmessage',$messageid,$topicid,CURRENTUSERID,CURRENTSTAMP,false);
	taint_include_cache('flockmessage',$messageid,$body);
	flush_page_cache('flocktopic',$topicid);
	counthit('flockmessage');
	return $messageid;
}
