<?php

declare(strict_types=1);

# TYPE can be found in:
#	party_db.artist
#	party_db.artist_log
#	party_db.lineup
#	party_db.lineup_log

const ARTIST_TYPES = [
	'accordion_player'	=> [],
	'act'				=> [],
	'activity'			=> [],
	'actor'				=> [],
	'art'				=> [],
	'backing_vocalist'	=> [],
	'band'				=> [],
	'brass_band'		=> [],
	'cabaret'			=> [],
	'choir'				=> [],
	'choreographer'		=> [],
	'comedy'			=> [],
	'composer'			=> [],
	'conductor'			=> [],
	'creative_producer' => [],
	'dancer'			=> [],
	'deco'				=> [],
	'director'			=> [],
	'dj'				=> [],
	'dj+mc'				=> [],
	'drag'				=> [],
	'drummer'			=> [],
	'fanfare'			=> [],
	'food'				=> [],
	'ghost_producer'	=> [],
	'ghostwriter'		=> [],
	'group'				=> [],
	'guitarist'			=> [],
	'harmonica_player'	=> [],
	'harmony'			=> [],
	'host'				=> [],
	'hybrid'			=> [],
	'influencer'		=> [],
	'instrumentalist'	=> [],
	'lasers'			=> [],
	'live_av'			=> [],
	'lj'				=> [],
	'market'			=> [],
	'melodeon_player'	=> [],
	'mc'				=> [],
	'musician'			=> [],
	'orchestra'			=> [],
	'percussionist'		=> [],
	'performance'		=> [],
	'performer'			=> [],
	'photographer'		=> [],
	'pianist'			=> [],
	'poet'				=> [],
	'politician'		=> [],
	'presenter'			=> [],
	'producer'			=> [],
	'rapper'			=> [],
	'saxophonist'		=> [],
	'show'				=> [],
	'songwriter'		=> [],
	'soundsystem'		=> [],
	'speaker'			=> [],
	'spokenword'		=> [],
	'theatre'			=> [],
	'trumpet_player'	=> [],
	'videographer'		=> [],
	'violinist'			=> [],
	'vj'				=> [],
	'vocalist'			=> [],
	'workshop'			=> [],
	'writer'			=> [],
];

const ARTIST_TYPES_NOT_OK_FOR_LINEUP = [
	'composer'			=> 'composer',
	'creative_producer'	=> 'creative_producer',
	'ghost_producer'	=> 'ghost_producer',
	'ghostwriter'   	=> 'ghostwriter',
	'group'				=> 'group',
	'influencer'		=> 'influencer',
	'model'				=> 'model',
	'producer'			=> 'producer',
	'songwriter'		=> 'songwriter',
	'writer'			=> 'writer',
];

const ARTIST_TYPES_ARE_GROUPS = [
	'band'				=> 'band',
	'brass_band'		=> 'brass_band',
	'choir'				=> 'choir',
	'fanfare'			=> 'fanfare',
	'group'				=> 'group',
	'harmony'			=> 'harmony',
	'orchestra'			=> 'orchestra',
	'soundsystem'		=> 'soundsystem',
];

require_once '__translation.php';

function get_artist_type(
	?string	$type		= null,
	?string	$gender		= null,
	?bool	$multiple	= null,
): array|string|false {
	static $__artist_types = ARTIST_TYPES;
	if (!$type) {
		return $__artist_types;
	}
	if (!isset($__artist_types[$type])) {
		return false;
	}
	if (isset($__artist_types[$type][$gender][$multiple])) {
		return $__artist_types[$type][$gender][$multiple];
	}
	if (!($type_name = __($key = ($multiple ? 'artiststype:' : 'artisttype:').$type, ['GENDER' => $gender]))) {
		ob_start();
		?>no translation for <?= $key
		?>, gender: <?= $gender
		?>, type_name: <? echo var_get($type_name);
		mail_log(ob_get_clean(), item: 'missing translation', subject: 'missing translation');
	}
	return $__artist_types[$type][$gender][$multiple] = $type_name;
}

function show_artist_types_checkboxes(
	array|string|null $selected		= null,
	bool			  $artist_form	= false
): array {
	require_once 'defines/artist.inc';

	$types = get_artist_type();

	foreach ($types as $type => &$name) {
		# The _prefix version of the artist types often contains a more neutral version (non gender specific)
		# For now, use it when selecting types on the artist form.
		$name = __C("artisttype:{$type}_prefix", DO_CONDITIONAL);
	}
	unset($name);

	asort($types);

	?><div class="nice-boxes" style="columns: <?= SMALL_SCREEN ? 2 : 4 ?>;"><?

	foreach ($types as $type => $name) {
		$checked =
			$selected
		&&	(	is_array($selected)
			?	isset($selected[$type])
			:	str_contains($selected, $type)
			);
		static $__done_vars = false;
		if (!$__done_vars) {
			$__done_vars = true;
			?><meta<?
			?> name="ARTIST_TYPES_NOT_OK_FOR_LINEUP"<?
			?> content="<?= implode(',', ARTIST_TYPES_NOT_OK_FOR_LINEUP) ?>" /><?
			?><meta<?
			?> name="ARTIST_TYPES_ARE_GROUPS"<?
			?> content="<?= implode(',', ARTIST_TYPES_ARE_GROUPS) ?>" /><?
		}
		?><label class="<?
		if (!$checked) {
			?>not-<?
		}
		?>bold-hilited"><?
		?><input<?
		?> name="TYPE[]"<?
		?> type="checkbox" value="<?= $type ?>"<?
		?> class="upLite"<?
		if ($checked) {
			?> checked<?
		}
		if ($artist_form) {
			?> onclick="Pf_clickArtistType(this);"<?
		}
		?> data-not-lineup-ok=
		?> /><div class="description"><?= $name ?></div></label><?
	}
	?></div><?

	return $types;
}

const ARTIST_TYPE_PREFIX				= 1;
const ARTIST_TYPE_SUFFIX				= 2;
const HIDDEN_ARTIST_TYPE_FOR_SINGLES	= ['dj', 'group'];	# dj is default, and group isn't actually a type

function get_artist_type_neutral(string $type): string {
	return __("artisttype:{$type}_prefix");
}
function show_artist_type_indicator(
	string|array	$artist,
	int				$where
): void {
	$types_str	= is_string($artist) ? $artist : $artist['TYPE'];
	$gender		= is_array ($artist) ? ($artist['GENDER'] ?? '') : '';
	if (!$types_str
	||	in_array($types_str, HIDDEN_ARTIST_TYPE_FOR_SINGLES, true)
	) {
		# not interesting
		return;
	}
	$types = [];
	if (str_contains($types_str, ',')) {
		foreach (explode(',', $types_str) as $type) {
			if ($type !== 'group') {
				$types[] = $type;
			}
		}
	} elseif ($types_str !== 'group') {
		$types = [$types_str];
	}
	if (!$types
	||	!isset($types[1])
	&&	in_array($types[0], HIDDEN_ARTIST_TYPE_FOR_SINGLES, true)
	) {
		return;
	}
	foreach ($types as &$type) {
		$type = __(
			"artisttype:{$type}_prefix",
			DO_CONDITIONAL | ($where === ARTIST_TYPE_SUFFIX ? 0 : DO_CAPFIRST),
			['GENDER' => $gender]
		);
	}
	unset($type);

	if ($where === ARTIST_TYPE_SUFFIX) {
		?> <?= MIDDLE_DOT_ENTITY ?> <? echo implode(', ', $types);
	} else {
		echo implode(', ', $types) ?>: <?
	}
}

function get_artist_type_counts(int $artistid): array|false {
	static $__counts = [];
	return $__counts[$artistid] ??= memcached_simple_hash_if_not_admin('artist', 'lineup',
		have_admin('party')
	? "	SELECT TYPE, COUNT(*)
		FROM lineup
		WHERE ARTISTID = $artistid
		GROUP BY TYPE"
	: "	SELECT TYPE, COUNT(*)
		FROM lineup
		JOIN party USING (PARTYID)
		WHERE ARTISTID = $artistid
		  AND ACCEPTED
		  AND CANCELLED = 0
		  AND MOVEDID = 0
		  AND POSTPONED = 0
		GROUP BY TYPE",
		TEN_MINUTES);
}
