<?php

/*

required:

event

.name		-> event name
.description	-> facebook description
.timezone
.stamp

.pf_PARTYID	-> partyid

*/

const RE_BRACKETS		= /** lang PhpRegExp */ '[\[\]()|\h/*?.!]*';
const RE_NEED_BRACKETS	= /** lang PhpRegExp */ '[\[\]()|/*?.!]+';
const RE_PRICE			= '[\h:€/-]*(?!\d+[h:]\d+)(?!00)(\d+)(?:[,.](\-|\d+))?\h*(?:eur(?:o|o\'?s)?|€|[,.-]+)?';
const END_MARK			= '\h*(?:\n|'.RE_NEED_BRACKETS.'|(?<=\h)[il](?:\h+|$|\n)|$)';
const START_MARK		= '(?:Let\h*op:\h*|^|\n|'.RE_NEED_BRACKETS.'|(?!\h\d{1,2})\.|\n[^\w\d]+)\h*';
#						^ time part
const RE_AFTER			= '(?:(?:daar)?na|after(?:wards)?)';
const RE_CASH_ONLY		= '[\*\(\[]*(?:alleen\h*cash|cash\h*only)[\!\*\)\]]*';
const RE_TIME			= '(\d+\h*(?:[ap]m)|(\d+)(?:[:\.](\d+)[hu]?)?|middernacht|midnight|noon)';
const RE_TILL_TIME		= '(?:till|\'til|until|tot|voor|before)\h*'.RE_TIME;
const RE_FREE			= '(?:free|gratis)';
const RE_EX_FEE			= 'ex(?:cl(?:usief)?)?[\.\h]*fee';
const RE_ENTRANCE		= '(?:entr[e\x{e9}]e|entry|entrance|toegang)';
const RE_PRESALE		= '(?:(?:th )?pre[\h\-]*sale|(de )?voor[\-\h]*verkoop|vvk)';
const RE_ONLY			= '(?:enkel|alleen|uitsluitend|slechts|only)';
const RE_SOLD_OUT		= '(?:sold[\-\h]*out|uitverkocht)';
# 'tickets' might be the ending of a path, so try to ignore those:
const RE_TICKETS		= '(?<!/)(?:tickets|kaart(?:en|jes)|ticket\h*link)';
const RE_DOOR			= '(?:door|deur)';
const RE_DOOR_SALE		= RE_DOOR.'\h*(?:sales?|verkoop)?';
const RE_AT_THE_DOOR	= '(?:aan\h*(?:de\h*)?deur|at\h*(?:the\h*)?door|\bADD\b)';
const RE_AVAIL_SOLD		= '(?:beschikbaar|available|verkrijgbaar|verkocht)';
const RE_ONLY_DOOR_base	= /** lang PhpRegExp */	// NOSONAR
	'no\h*(?:online\h*)?'.RE_PRESALE.'|' .
	'door\h*(?:sales?\h*)?only|' .
	RE_ONLY.'\h*door\h*sales?|' .
	'geen\h*'.RE_PRESALE.'|' .
	'(?:tickets|kaarten)?\h*'.RE_ONLY.'\h*'.RE_AT_THE_DOOR.'\h*(?:verkrijgbaar|te\h*koop|verkocht)?|' .
	'nur\h*abendkasse|' .
	'kein\h*vorverkauf|' .
	'(?:(?:er\h*is\h*)?deze\h*avond\h*)?'.RE_ONLY.'\h*(?:deur\h*verkoop|dvk)|' .
	RE_ONLY.'\h*'.RE_AT_THE_DOOR;

const RE_COLON_IS		 = '(?:\h+is\h+|[:\h]+)';
const RE_ALLOW_SOME_CHARS = '.{0,3}?';

function __debug(string $str): void {
	if (!function_exists('debug')) {
		return;
	}
	debug('descr: '.$str);
}

function parse_facebook_info(?int &$status = null): array|int {
	if (false === require_number($_POST, 'PARTYID')) {
		return 400;
	}
	if ($partyid = $_POST['PARTYID']) {
		if (!($party = memcached_party_and_stamp($partyid))) {
			return 400;
		}
		$use_tz = $party['TIMEZONE'];
		$start  = $party['STAMP'];
	} else {
		$use_tz = 'Europe/Amsterdam';
		$start  = 0;
	}

	__debug('parse_facebook_info called for '.($partyid ? 'event '.$partyid : 'new event'));

	__debug('event title: '.($_POST['FBTITLE'] ?? ''));

	$event = (object)[
		'name'			=> transliterate_irregular_letters_to_regular(getifset($_POST, 'FBTITLE') ?? ''),
		'description'	=> transliterate_irregular_letters_to_regular(getifset($_POST, 'FBDESCRIPTION') ?? ''),
		'date'			=> have_something($_POST, 'DATE') && preg_match('"^\d+-\d+-\d+$"', $_POST['DATE']) ? $_POST['DATE'] : 0,
		'timezone'		=> $use_tz,
		'stamp'			=> $start,
		'start_time'	=> '',
		'pf_PARTYID'	=> $partyid
	];

	$min_age = null;

	$only_doorsale = detect_only_doorsale($event, $doorprice, $min_age);

	$prices = [];

	$new_prices = detect_doorprice($event, $only_doorsale);
	if ($new_prices) {
		array_push($prices, $new_prices);
	}
	$lockers = detect_lockers($event, $prices);

	if (false === ($ladies_info = detect_ladies_free($event))) {
		return 503;
	}
	if ($ladies_info) {
		[$ladies_free, $free_till] = $ladies_info;

		$price = [
			'PRICE_SALE_TYPE'	=> 'door',
			'PRICE_INCFEE'		=> false,
			'PRICE_AMOUNT'		=> 0,
			'PRICE_ACCESS'		=> 'women',
		];
		if ($free_till !== null) {
			[$hour, $mins] = parse_time($free_till);
			$price += [
				'PRICE_TIME_TYPE' => 'till',
				'PRICE_TILL_HOUR' => $hour,
				'PRICE_TILL_MINS' => $mins,
			];
		}
		$prices[] = $price;
	}

	if ($doorprice) {
		$prices[] = [
			'PRICE_SALE_TYPE' => 'door',
			'PRICE_INCFEE'	=> false,
			'PRICE_AMOUNT'	=> $doorprice,
		];
	}

	detect_presales($event, $prices);

	$prices = array_unique($prices, SORT_REGULAR);

	detect_parking($event, $prices);

	detect_attending_free($event, $prices);

	$maybe_free = detect_free($event, $prices, $min_age);

	if (!$maybe_free
	||	empty($prices)
	&&	!$only_doorsale
	) {
		$free = $maybe_free;
	} else {
		$free = false;
		if ($maybe_free) {
			__debug('ignoring free due to prices or only doorsale detection');
		}
	}

	$ages = detect_min_age($event);

	if ($min_age !== null) {
		$ages = !$ages
			  ? [$min_age, null, null]
			  : [$ages[0] === null ? $min_age : min($ages[0], $min_age), $ages[1], $ages[2]];
	}

	$sold_out = detect_sold_out($event);
	# 0: completely sold out
	# 1: presale sold out

	if (detect_presale_sold_out($event)) {
		if (!$sold_out) {
			$sold_out = [null, true];
		} elseif (!$sold_out[0]) {
			$sold_out[0] = true;
		}
	}

	return ['sold_out'		=> $sold_out,
			'lockers'		=> $lockers,
			'ages'			=> $ages,
			'only_doorsale'	=> $only_doorsale,
			'free'			=> $free,
			'max_visitors'	=> detect_max_visitors($event),
			'door_more'		=> detect_door_more($event),
			'presale_start' => detect_presale_start($event),
			'door_closes'   => detect_door_closes($event),
			'prices'		=> $prices,
			'dresscode'		=> detect_dresscode($event),
			'genres'		=> detect_genres($event, $genrestr),
			'genrestr'		=> $genrestr,
			'ticketlink'	=> detect_ticketlink($event),
			'cancelled'		=> detect_cancelled($event),
			'postponed'		=> detect_postponed($event),
	];
}

function detect_presale_sold_out(stdClass $event): ?bool {
	foreach (['name', 'description'] as $key) {
		if (!property_exists($event, $key)) {
			continue;
		}
		if (($m = 1) && preg_match(
					'"'.START_MARK.RE_BRACKETS.'(?:'.
						RE_PRESALE.'\h*'.RE_SOLD_OUT.
					')'.RE_BRACKETS.END_MARK.'"uis', $event->$key, $match
			)
		) {
			__debug("detected.$m presale_sold_out: $match[0]");
			return true;
		}
	}
	return null;
}

function detect_sold_out(stdClass $event): array|false|null {
	static $at_the_door_regex = '"(?:' .
		'(?:\d+|laatste(?:\h*(\d+))?|last(?:\h*(\d+))?)\h*'.RE_TICKETS.'\h*'.RE_AVAIL_SOLD.'?'.RE_AT_THE_DOOR .
		'|'.'(\d+)\h*deur\h*tickets' .
		'|'.'(\d+)\h*tickets\h*(?:will\h*be\h*)?available\h*'.RE_AT_THE_DOOR .
		'|'.'there\h*(?:are|will\h*be)\h(?:still\h*)?(\d+\h*)?tickets\h*(?:available\h*)?at\h*the\h*door' .
		'|'.'laatste\h*(\d+\h*)?'.RE_TICKETS.'\h*zijn\h*voor\h*€?[\d+,-]+\h*aan\h*de\h*deur\h*verkrijgbaar' .
		'|'.'There\h*will\h*be\h*tickets\h*at\h*the\h*door\b' .
		')"iu';

	$lower_name = mb_strtolower($event->name);

	if (!preg_match($q = '"(?:^|\b)'.RE_SOLD_OUT.'(?:\b|$)"iu', $lower_name, $match)) {
		# not at all sold out
		return null;
	}

	__debug('detected sold out: '.$match[0]);

	if (preg_match('"(?:'.RE_PRESALE.'|\d+%|almost|bijna)[\h=]+'.RE_SOLD_OUT.'"iu', $lower_name, $match)) {
		# not sold out completely
		__debug('not sould out completely: '.$match[0]);
		return null;
	}

	$partyid = $event?->pf_PARTYID ?? null;

	$titl_m = preg_match($at_the_door_regex, $lower_name);
	$desc_m = property_exists($event, 'description')
		   && preg_match($at_the_door_regex, $event->description, $door_match);

	if ($titl_m || $desc_m) {
		# sold out only presale

		__debug('event seems sold out but still some at the door or description tells otherwise');
		if (CLI && $partyid) {
			if (!db_insert('partyprice_log', '
				INSERT INTO partyprice_log
				SELECT *,'.CURRENTSTAMP.', 0
				FROM partyprice
				WHERE SOLD_OUT = 0
				  AND TYPE = "presale"
				  AND WHAT = "entrance"
				  AND PARTYID = '.$partyid
				)
				|| !db_update('partyprice', '
				UPDATE partyprice SET
					SOLD_OUT	= 1,
					MUSERID		= 0,
					MSTAMP		= '.CURRENTSTAMP.'
				WHERE SOLD_OUT = 0
				  AND TYPE = "presale"
				  AND WHAT = "entrance"
				  AND PARTYID = '.$partyid
				)
			) {
				return false;
			}

			if ($desc_m
			&& ($last = end($door_match))
			&& is_number($last)
			) {
				# set amount of door tickets
			}
		}
		return [false, true];
	}
	return [true, true];
}

function detect_ticketlink(stdClass $event): ?string {
	if (!property_exists($event, 'description')) {
		return null;
	}
	if (($m = 1) && preg_match('"'.START_MARK.RE_TICKETS.'[:\h]*(https?://\S+)'.END_MARK.'"uis', $event->description, $match)
	||	($m = 2) && preg_match('"'.START_MARK.'(?:online\h*shop)[:\h]*(https?://\S+)'.END_MARK.'"uis', $event->description, $match)
	) {
		__debug('detected.'.$m.' ticket link in '.utf8_mytrim($match[0]));
		require_once '_url.inc';
		return cleanup_url($match[1]);
	}
	return null;
}

function detect_parking(stdClass $event, array &$prices): ?bool {
	if (!property_exists($event, 'description')) {
		return null;
	}
	if (preg_match('"'.START_MARK.'(?:parkeren\h*is\h*gratis(?:\h+en\h+|\n|$))"uis', $event->description, $match)) {
		__debug('detected free parking: '.utf8_mytrim($match[0]));

		$prices[] = [
			'PRICE_SALE_TYPE' => 'door',
			'PRICE_INCFEE'	  => false,
			'PRICE_AMOUNT'	  => 0,
			'PRICE_WHAT'	  => 'parking',
		];
		return true;
	}
	return false;
}

function detect_lockers(stdClass $event, array &$prices): ?bool {
	if (!property_exists($event, 'description')) {
		return null;
	}
	if (preg_match('"(?:There are no lockers|Er zijn geen kluisjes)"ui', $event->description, $match)) {
		__debug('detected no lockers: '.utf8_mytrim($match[0]));
		return false;
	}
	if (preg_match(
		'"(?:'.'\n[\W\h]*(?:elektronische\h*)?(?:lockers|kluisjes)(?:(?:\h*(?:are\h*)?available|\h*\:?\h*(?:zijn\h*)?aanwezig|\h*cost)\h*(?:for)?[:\(\h]*(?:'.RE_PRICE.')?|[\W\h]*(?:\n|$))'.
			'|'.'\h*(?:Lockers|kluisjes)(?:\n|$)'.
		')"uis',
		$event->description,
		$match
	)
	) {
		__debug('detected lockers: '.utf8_mytrim($match[0]));

		if (isset($match[1])) {
			$lockerprice = parse_price($match, 1);

			__debug('detected locker price: '.$lockerprice);

			$prices[] = [
				'PRICE_AMOUNT'		=> $lockerprice,
				'PRICE_WHAT'		=> 'locker',
				'PRICE_SALE_TYPE'	=> 'door',
				'PRICE_INCFEE'		=> false,
			];
		}
		return true;
	}
	return null;
}

function detect_door_closes(stdClass $event): ?array {
	if (!property_exists($event, 'description')) {
		return null;
	}
	if (($m = 1) && preg_match(
			'"(?:' .
			'(?:onze\h*)?deur(?:en)?\h*sluit(?:en)?' .
			'|no\h*access' .
			'|geen\h*toegang' .
			'|final\h*entry\h*' .
			'|deur(?:en)?\h*dicht' .
			'|(?:door\h*closes|doors\h*close)' .
			')' .
			'\h*(?:(?:om|at|after|na|:)\h*)?'.RE_TIME.'"ui', $event->description, $match
		)
	||	($m = 2) && preg_match('"je\h*kunt\h*tot\h*'.RE_TIME.'(?:\h*uur)?\h*naar\h*binnen"ui', $event->description, $match)
	) {
		$time = parse_till($match, 1);

		[$hour, $mins] = parse_time($time);

		__debug("detected.$m door closes at $hour:".sprintf('%02d', $mins).": ".utf8_mytrim($match[0]));

		$mins = (int)(5 * round($mins / 5));

		return [$hour, $mins];
	}
	return null;
}

function detect_min_age(stdClass $event): ?array {
	$min_age		 = null;
	$max_age		 = null;
	$min_age_females = null;
	$max_min_age	 = null;

	if (property_exists($event, 'name')) {
		if (($m = 201) && preg_match('"\h+(?P<min_age>\d{2})\+$"uis', $event->name, $match)
		) {
			__debug('detected.'.$m.' min_age '.$match['min_age'].' in name: '.$match[0]);

			$min_age = (int)$match['min_age'];
		}
	}
	if (property_exists($event, 'description')) {
		if (($m = 1) && preg_match('"\bage[:\h]+(?:m|men|mannen|guys|boys|gents|gentlemen)\h+(\d+)\+?[\h/|]+(?:v|f|women|ladies)\h+(\d+)\+?\b"ui', $event->description, $match)
		||	($m = 2) && preg_match('"\bM:\s*(\d+)\+\s*F:\s*(\d+)\+"ui', $event->description, $match)
		) {
			__debug('detected.'.$m.' min_age male/female information in description: '.$match[0]);

			$min_age		 = (int)$match[1];
			$min_age_females = (int)$match[2];
		}

		if (($m = 10) && preg_match_all('"(heren|dames|vrouwen|(?:wo)?men|mannen|(?:fe)?males?|ladies|gent(?:s|lemen)|boys|guys)[\h*:]*(\d+)\+?"ui', $event->description, $match)
		) {
			__debug('detected.'.$m.' min_age male/female information in description: '.implode(', ', $match[0]));
			foreach ($match[1] as $i => $gender) {
				$age = (int)$match[2][$i];
				if ($age > 60) {
					continue;
				}
				if ($age > 0 && $age < 12
				||  $age > 30
				) {
					# ignore
					continue;
				}
				$max_min_age = max($max_min_age, $age);
				if (in_array(mb_strtolower($gender), [
					'women',
					'vrouwen',
					'dames',
					'ladies',
					'female',
					'females',
				], true)) {
					$min_age_females = $age;
					__debug('detected min_age women: '.$age);
				}
			}
			if ($max_min_age !== null) {
				__debug('detected.'.$m.' max_min_age: '.$max_min_age);
			}
		}

		$min_age_re = '(?:min(?:[ui]mum|imale|\.)?\h*(?:leeftijd|lftd|age|alter)|vanaf|leeftijd[:\h]*vanaf)';

		if (($m = 100) && preg_match('"'.START_MARK.RE_ALLOW_SOME_CHARS.'\W*(?<!in de )(?:leeftijd|lftd|(?<!at )\bage)(?:\h*is\h*|\h*[:;]\h*|)\h*(\d+|alle\h*leeftijden)(?:\h*(?:tot|t/m)\h*(\d+)\h*\+?)?"uis', $event->description, $match)
		||	($m = 101) && preg_match('"'.START_MARK.$min_age_re.'\h*(?:is|=|[\–:])?\h*(\d+(?!\h*personen)|alle\h*leeftijden(?:\h*toegankelijk)?)(?:\h*tot\h*(\d+)\h*\+?)?"ui', $event->description, $match)
		||	($m = 103) && preg_match('"'.START_MARK.RE_ALLOW_SOME_CHARS.'/*\h*(\d\d)\h*\+'.END_MARK.'"uis', $event->description, $match)
		||	($m = 104) && preg_match('"\h*is\h*an?\h*(\d+)\h*\+\h*event'.END_MARK.'"ui', $event->description, $match)
		||	($m = 105) && preg_match('"'.START_MARK.RE_ALLOW_SOME_CHARS.'de\h*'.$min_age_re.'\h*voor\h*[\w\h&]+\h*is\h*(\d+)\+"uis', $event->description, $match)
		||	($m = 106) && preg_match('"'.START_MARK.RE_ALLOW_SOME_CHARS.$min_age_re.'[\h:]*(\d+)[\+\(\)]*(?:\h*jaar)?(?:\h*\(?id\h*verplicht\)?)?'.END_MARK.'(?!\d\d)"uis', $event->description, $match)
		||	($m = 107) && preg_match('"'.START_MARK.RE_ALLOW_SOME_CHARS.'(?:leeftijd|toegang)\h*(?:is|:)\h*(?:ben\h*je\h*)?(\d+)\+"uis', $event->description, $match)
		||	($m = 108) && preg_match('"'.START_MARK.RE_ALLOW_SOME_CHARS.'(\d+)\+\h*ID\h*(?:is\h*)?verplicht"uis', $event->description, $match)
		||	($m = 109) && preg_match('"'.START_MARK.RE_ALLOW_SOME_CHARS.'(\d+)\+(?:\h*&\h*ID)?'.END_MARK.'"uis', $event->description, $match)
		||	($m = 110) && preg_match('"'.START_MARK.RE_ALLOW_SOME_CHARS.'age:\h*(\d+)\+'.END_MARK.'"uis', $event->description, $match)
		||	($m = 111) && preg_match('"'.START_MARK.RE_ALLOW_SOME_CHARS.'(?:mindest\h*alter|einlass(?:\h*ab:)?)[\h:]*(\d+)\h*jahr"uis', $event->description, $match)
		||	($m = 112) && preg_match('"\bdit\h*is\h*een (\d+)\+\h*event\b"ui', $event->description, $match)
		||	($m = 113) && preg_match('"\bthis\h*event\h*is\h*(\d{2})+\b"ui', $event->description, $match)
		||	($m = 114) && preg_match('"\btoegang:\h*(\d+)\h*jaar\h*en\h*ouder\b"ui', $event->description, $match)
		||	($m = 115) && preg_match('"'.START_MARK.RE_ALLOW_SOME_CHARS.'toegang\h*vanaf\h*(\d+)\h*jaar'.END_MARK.'"ui', $event->description, $match)
		||	($m = 116) && preg_match('"'.START_MARK.RE_ALLOW_SOME_CHARS.'(\d+)\h+jaar\h*en\h*ouder\b"uis', $event->description, $match)
		||	($m = 117) && preg_match('"'.START_MARK.RE_ALLOW_SOME_CHARS.$min_age_re.'[\h:]*\n(\d+)"uis', $event->description, $match)
		||	($m = 118) && preg_match('"'.START_MARK.RE_ALLOW_SOME_CHARS.'\w+\h*is\h*toegankelijk\h*vanaf\h*(?:(?:de\h*)?leeftijd\h*)?(\d+)"uis', $event->description, $match)
		||	($m = 119) && preg_match('"'.START_MARK.RE_ALLOW_SOME_CHARS.'età\h*minima[:\h]*\+?(\d+)'.END_MARK.'"uis', $event->description, $match)
		||	($m = 120) && preg_match('"'.START_MARK.RE_ALLOW_SOME_CHARS.'voor\h*(\d+)\h*(?:jaar\h*)?en\h*ouder'.END_MARK.'"uis', $event->description, $match)
		||	($m = 121) && preg_match('"'.START_MARK.RE_ALLOW_SOME_CHARS.'ab (\d+) jahren?'.END_MARK.'"uis', $event->description, $match)
		||	($m = 122) && preg_match('"'.START_MARK.RE_ALLOW_SOME_CHARS.'(\d+)\+[,\h]*(?:event|evenement|feest|party|legitimatie\h*verplicht|ID\s*required)'.END_MARK.'"ui', $event->description, $match)
		||	($m = 123) && preg_match('"'.START_MARK.RE_ALLOW_SOME_CHARS.'age(?:\h*limit)?[:+\h]*(\d{2})\b"ui', $event->description, $match)
		||	($m = 124) && preg_match('"'.START_MARK.RE_ALLOW_SOME_CHARS.'A\h*partir\h*de\h*[\w\d]+\h*et\h*(\d+)\h*ans\b"ui', $event->description, $match)

		#	ÂGE MINIMUM : 16 ANS
		||	($m = 125) && preg_match('"'.START_MARK.RE_ALLOW_SOME_CHARS.'[ÂA]GE\h*MINIMUM[\h:]*(\d+)\b"ui', $event->description, $match)
		#	minumum age for this event: 21+
		#	||	($m = 125) && preg_match('"'.START_MARK.RE_ALLOW_SOME_CHARS.'min[ui]mum\h*age\h*(?:for\h*this\h*event\h*)?:\h*(\d+)\+"ui', $event->description, $match)
		||	($m = 126) && preg_match('"'.START_MARK.RE_ALLOW_SOME_CHARS.'min[ui]mum\h*age\h*.*?(\d+)\+"ui', $event->description, $match)

		) {

			$min_age = is_number($match[1]) ? (int)$match[1] : 0;
			$max_age = isset($match[2])		? (int)$match[2] : null;

			__debug('detected.'.$m.' age (min: '.$min_age.($max_age ? ', max: '.$max_age : null).') information in description: '.utf8_mytrim($match[0]));

			if ($min_age
			&&	$min_age > 60
			) {
				$min_age = null;
			}
			if ($min_age !== null) {
				__debug('detected.'.$m.' min_age: '.$min_age);
			}
			if ($max_age !== null) {
				__debug('detected.'.$m.' max_age: '.$max_age);
			}
		}
	}

	return $min_age			!== null
		|| $max_min_age		!== null
		|| $max_age			!== null
		|| $min_age_females !== null
		? [$min_age ?? $max_min_age, (int)$max_age, $min_age_females]
		: null;
}

function detect_only_doorsale(stdClass $event, ?int &$doorprice, ?int &$min_age) : ?bool {
	if (!property_exists($event, 'description')) {
		return null;
	}
	if (($m = 1) && preg_match('"'.START_MARK.'(?:'.RE_ONLY_DOOR_base.')(?:[\.\!,:;\r\n\h]*(?:for\h*)?'.RE_PRICE.'|\h+\-\h+|\h*'.RE_NEED_BRACKETS.'|\n|$)"uis', $event->description, $match)
	||	($m = 2) && preg_match('"'.START_MARK.'(?:er|bij\h*[\w\h]{1,20})\h*worden\h*'.RE_ONLY.'\h*'.RE_TICKETS.'\h*'.RE_AT_THE_DOOR.'\h*'.RE_AVAIL_SOLD.'?'.END_MARK.'"uis', $event->description, $match)
	||	($m = 3) && preg_match('"'.START_MARK.'no\h*'.RE_PRESALE.'\b"uis', $event->description, $match)
	) {
		__debug('detected.'.$m.' only doorsale: '.utf8_mytrim($match[0]));
		if (isset($match[1])) {
			$doorprice = parse_price($match, 1);
			__debug('detected.'.$m.' only doorsale price ('.$doorprice.'): '.utf8_mytrim($match[0]));
		}
		return true;
	}

	$brackets_re	   = '[\[\]\(\)\h\/\*]*';
	$only_doorprice_re = $brackets_re.'(?:'.RE_ONLY_DOOR_base.')';

	if (($m = 100) && preg_match('"'.START_MARK.RE_PRICE.'\h*at\h*the\h*door,\h*no\h*'.RE_PRESALE.END_MARK.'"uis', $event->description, $match)
	||	($m = 101) && preg_match('"'.START_MARK.RE_TICKETS.'[:\h]*'.RE_PRICE.'[\h/|]*'.$only_doorprice_re.'"uis', $event->description, $match)
	||	($m = 102) && preg_match('"'.START_MARK.RE_PRICE.$brackets_re.$only_doorprice_re.$brackets_re.'(?:\h*(\d+)\+)?"uis', $event->description, $match)
	||	($m = 103) && preg_match('"'.START_MARK.RE_TICKETS.'\h*zijn\h*(?:'.RE_ONLY_DOOR_base.')(?:\h*te koop)?\h*(?:voor\h*)?(?:maar\h*)?'.RE_PRICE.END_MARK.'"uis', $event->description, $match)
	||	($m = 104) && preg_match('"'.START_MARK.RE_TICKETS.'\h*zijn\h*'.RE_ONLY.'\h*(?:voor\h*)?(?:maar\h*)?'.RE_PRICE.'\h*te\h*koop\h*aan?\h*de\h*deur'.END_MARK.'"uis', $event->description, $match)
	||	($m = 105) && preg_match('"'.START_MARK.'prijs[:\h]*'.RE_PRICE.'[\h\|]*'.$only_doorprice_re.END_MARK.'"uis', $event->description, $match)
	||	($m = 106) && preg_match('"'.START_MARK.RE_ENTRANCE.RE_BRACKETS.'(?:'.RE_ONLY_DOOR_base.')'.RE_BRACKETS.'[:\h]*'.RE_PRICE.END_MARK.'"uis', $event->description, $match)
	) {
		$doorprice = parse_price($match, 1);

		if (!empty($match[3])) {
			$min_age = (int)$match[3];
			__debug('detected.'.$m.' only doorsale with min_age '.$min_age.': '.utf8_mytrim($match[0]));
		}
		__debug('detected.'.$m.' only doorsale price ('.$doorprice.'): '.utf8_mytrim($match[0]));
		return true;
	}
	return null;
}

function detect_cancelled(stdClass $event) {
	static $re_cancelled = '(?:cancell?ed|geann?uleerd|afgelast|gaat\h*niet\h*door)';
	if (property_exists($event, 'name')) {
		if (false === stripos($event->name, 'club cancelled presenteert')
		&&	preg_match('"\b'.$re_cancelled.'\b"ui', $event->name, $match)
		) {
			__debug('detected.name cancelled event: '.$match[0]);
			return true;
		}
	}
	if (property_exists($event, 'description')) {
		if (preg_match('"'.START_MARK.RE_BRACKETS.$re_cancelled.RE_BRACKETS.END_MARK.'"ui', $event->description, $match)) {
			__debug('detected.desc cancelled event: '.$match[0]);
			return true;
		}
	}
	return null;
}

function detect_postponed(stdClass $event) {
	static $re_postponed = '(?:post[\h\-]*poned|uitgesteld|delayed(?:\h*till)?(?:\h*further)?(?:\h*notice)?$)';
	if (property_exists($event, 'name')) {
		if (preg_match('"(?:^|\b)\h*'.$re_postponed.'\h*(\b|$)"ui', $event->name, $match)) {
			__debug('detected.name postponed event: '.$match[0]);
			return true;
		}
	}
	if (property_exists($event, 'description')) {
		if (preg_match('"'.START_MARK.RE_BRACKETS.$re_postponed.RE_BRACKETS.END_MARK.'"ui', $event->description, $match)) {
			__debug('detected.desc cancelled event: '.$match[0]);
			return true;
		}
	}
	return null;
}

function detect_free(stdClass $event, array &$prices, ?int &$min_age = null): ?bool {
	# remove booth/table info for free detection

	if (preg_match_all('"[^\n]*Booth[^\n]*:\n+(-[^\n]+\n+)+"ui', $event->description, $matches)) {
		foreach ($matches[0] as $complete) {
			$event->description = str_replace($complete, '', $event->description);
		}
	}

	$free = null;
	foreach (['name', 'description'] as $key) {
		if (!property_exists($event, $key)) {
			continue;
		}
		if (($m = 1) && preg_match(
				$q = '"'.($key === 'description' ? START_MARK : null) .
					RE_BRACKETS.RE_FREE.'\h*(?:event|entrance|entree|party|admission)'.RE_BRACKETS .
					END_MARK .
					'"uis', $event->$key, $match
			)
		) {
			__debug('detected.'.$m.' free entrance: '.utf8_mytrim($match[0]));
			$free = true;
		}
	}
	if (property_exists($event, 'name')) {
		if (($m = 10001) && preg_match('"'.START_MARK.RE_FREE.'\h*'.RE_TILL_TIME.'[\r\n\h]*(?:'.RE_PRICE.'\h*'.RE_AFTER.'|'.RE_AFTER.'\h*'.RE_PRICE.')?'.END_MARK.'"uis', $event->description, $match)
		) {
			$till_time = parse_till($match, 1);

			[$hour, $mins] = parse_time($till_time);

			__debug("detected.$m free till $hour:".sprintf('%02d', $mins).': '.utf8_mytrim($match[0]));

			$prices[] = [
				'PRICE_SALE_TYPE'	=> 'door',
				'PRICE_INCFEE'		=> false,
				'PRICE_AMOUNT'		=> 0,
				'PRICE_TIME_TYPE'	=> 'till',
				'PRICE_TILL_HOUR'	=> $hour,
				'PRICE_TILL_MINS'	=> $mins,
			];

			if (isset($match[4])) {
				$price = parse_price($match, 4);

				__debug('detected.'.$m.' non free price '.$price.': '.utf8_mytrim($match[0]));

				$prices[] = [
					'PRICE_SALE_TYPE' => 'door',
					'PRICE_INCFEE'	=> false,
					'PRICE_AMOUNT'	=> $price,
				];
			}
		}
	}
	foreach (['name', 'description'] as $key) {
		if (!property_exists($event, $key)) {
			continue;
		}
		$all_night = '[\h,]*(?:all\h*night\h*|no\h*tickets?\h*needed)?';

		if (($m = 100) && preg_match('"'.START_MARK.'(?:the|de)?\h*'.RE_ENTRANCE.'\h*(?::|=|is)?\h*'.RE_FREE.'!?\h*(?:\n|$|\.|\h*as\h*always\h*\b|'.RE_TILL_TIME.')"uis', $event->$key, $match)
		||	($m = 101) && preg_match('"'.START_MARK.RE_TILL_TIME.'\h*'.RE_ENTRANCE.'\h*'.RE_FREE.'"uis', $event->$key, $match)
		||	($m = 102) && preg_match('"'.START_MARK.RE_TILL_TIME.'\h*'.RE_FREE.'\h*'.RE_ENTRANCE.'"uis', $event->$key, $match)
		||	($m = 103) && preg_match('"'.START_MARK.'[\h\-]*'.RE_FREE.'\h*'.RE_ENTRANCE.'[\h\-]*(?:'.RE_TILL_TIME.'|\n|$|[.!])"uis', $event->$key, $match)
		||	($m = 104) && preg_match('"\W*'.RE_FREE.'\h*'.RE_ENTRANCE.',?[\h\-]*(?:(?:de\h*)?hele\h*(?:nacht|dag)|all\h*night\h*long)"ui', $event->$key, $match)
		||	($m = 105) && preg_match('"(?:\n|, )geen\h*entree\h*prijs(?:\.|'.RE_TILL_TIME.')"uis', $event->$key, $match)
		||	($m = 106) && preg_match('"\.\h*'.RE_ENTRANCE.'\h*'.RE_FREE.'\h*'.$all_night.'[!.]"ui', $event->$key, $match)
		||	($m = 107) && preg_match('"'.START_MARK.'[\[(\h]*'.RE_FREE.'\h*'.RE_ENTRANCE.'[])\h]*'.$all_night.END_MARK.'"uis', $event->$key, $match)
		||	($m = 108) && preg_match('"(?:^|[.\h]*)this\h*is\h*a\h*free\h*event(?:\.|\h|$)"ui', $event->$key, $match)
		||	($m = 109) && preg_match('"'.START_MARK.'gratis\h*entree\h*(?:&|en)\h*free\h*shots'.END_MARK.'"ui', $event->$key, $match)
		||	($m = 110) && preg_match('"'.START_MARK.RE_ENTRANCE.'\h*is\h*'.RE_FREE.'\h*as\h*usual'.END_MARK.'"ui', $event->$key, $match)
		||	($m = 111) && preg_match('"'.START_MARK.RE_ENTRANCE.'\h*(?::|is)?\h*'.RE_FREE.END_MARK.'"ui', $event->$key, $match)
		||	($m = 112) && preg_match('"'.START_MARK.'(?:(?:dit|this)\h*)?(?:evenement|event|feest)\h*is\h*'.RE_FREE.END_MARK.'"uis', $event->$key, $match)
		||	($m = 113) && preg_match('"'.START_MARK.RE_FREE.'\h*'.RE_ENTRANCE.'\h*\W*\h*(?:free\h*spirits)?'.END_MARK.'"uis', $event->$key, $match)
		||	($m = 114) && preg_match('"'.START_MARK.'(?:prijs|price)[\s:]+'.RE_FREE.END_MARK.'"uis', $event->$key, $match)
		) {
			$time = parse_till($match, 1);

			__debug('detected.'.$m.' free entrance'.($time !== null ? ' till '.$time : null).': '.utf8_mytrim($match[0]));
			if ($time !== null) {
				[$hour, $mins] = parse_time($time);

				$prices[] = [
					'PRICE_SALE_TYPE'	=> 'door',
					'PRICE_INCFEE'		=> false,
					'PRICE_AMOUNT'		=> 0,
					'PRICE_TIME_TYPE'	=> 'till',
					'PRICE_TILL_HOUR'	=> $hour,
					'PRICE_TILL_MINS'	=> $mins,
				];
			}
			return $time === null;
		}
		if (($m = 1001) && preg_match('"'.START_MARK.'gratis\h*toegankelijk(\h*voor\h*alle\h*leeftijden)?'.END_MARK.'"uis', $event->$key, $match)
		) {
			__debug('detected.'.$m.' free entrance'.(empty($match[1]) ? null : ' (for all ages)').': '.utf8_mytrim($match[0]));
			if (!empty($match[1])) {
				$min_age = 0;
			}
			return true;
		}
	}
	return $free;
}

function detect_ladies_free(stdClass $event, ?array &$setlist = null): array|false|null {
	$ladies_free	  = null;
	$ladies_free_till = null;
	foreach (['name', 'description'] as $key) {
		if (!property_exists($event, $key)) {
			continue;
		}
		if (($m = 1) && preg_match('"'.START_MARK.'(?:dames|vrouwen|ladies)\h*(?:gratis|free)\h*(?:entree|entrance)?(?:\h*hebben)?(?:'.RE_TILL_TIME.')?'.END_MARK.'"uis', $event->$key, $match)
#		||	preg_match('"\bladies\h*free(?:\h*entrance)?(?:'.RE_TILL_TIME.')?'.END_MARK.'"ui',$event->$key,$match)
		||	($m = 2) && preg_match('"'.START_MARK.'\W*(?:free|gratis)\h*(?:entrance|entree)?\h*(?:for(?:\h*all)?|voor)\h*(?:the|de)?\h*(?:ladies|dames)\h*(?:'.RE_TILL_TIME.')?\W*'.END_MARK.'"uis', $event->$key, $match)
		) {
			$ladies_free = true;

			if (isset($match[1])) {
				$time = parse_till($match, 1);
				[$hour, $mins] = parse_time($time);
				$ladies_free_till = $hour * 100 + $mins;
			}
			__debug("detected.$m ladies free entrance".($ladies_free_till !== null ? " (till $hour:".sprintf('%02d', $mins).')' : '').': '.utf8_mytrim($match[0]));
		}
	}
	if ($setlist) {
		$setlist['LADIES_FREE']		 = $ladies_free ? "b'1'" : "b'0'";
		$setlist['LADIES_FREE_TILL'] = $ladies_free_till ?? 'NULL';
	}
	if (CLI && ($partyid = $event?->pf_PARTYID ?? null)) {
		if ($ladies_free) {
			if (false === ($have = db_single('partyprice', "
				SELECT PRICEID
				FROM partyprice
				WHERE PARTYID	= $partyid
				  AND TYPE		= 'door'
				  AND ACCESS	= 'women'
				  AND WHAT		= 'entrance'
				  AND PRICE = 0
				  AND NOT GROUP_AMOUNT"))
			) {
				return false;
			}
			$set_hour = $ladies_free_till !== null ? $hour : -1;
			$set_mins = $ladies_free_till !== null ? $mins :  0;

			if (!$have) {
				__debug('new free entry for women'.($ladies_free_till ? 'till '.$ladies_free_till : '').' for party https://vip.partyflock.nl/party/'.$partyid);

				if (!db_insert('partyprice', "
					INSERT INTO partyprice SET
						TIME_HOUR	= $set_hour,
						TIME_MINS	= $set_mins,
						TIME_TYPE	= 'till',
						MUSERID		= 0,
						MSTAMP		= ".time().",
						WHAT		= 'entrance',
						TYPE		= 'door',
						ACCESS		= 'women',
						PRICE		= 0,
						PARTYID		= $partyid")
				) {
					return false;
				}
			} else {
				$wherep = '
						  TYPE		= "door"
					  AND ACCESS	= "women"
					  AND WHAT		= "entrance"
					  AND PRICE		= 0
					  AND PARTYID	= '.$partyid;

				if (!db_insert('partyprice_log', '
					INSERT INTO partyprice_log
					SELECT *,'.time().", 0
					FROM partyprice
					WHERE NOT	( TIME_HOUR	!= $set_hour
							  AND TIME_MINS != $set_mins
							  AND TIME_TYPE != 'till'
							)
					  AND  $wherep")
				) {
					return false;
				}
				if (db_affected()) {
					__debug('updated free entry for women'.($ladies_free_till ? 'till '.$ladies_free_till : null).' for party https://vip.partyflock.nl/party/'.$partyid);

					if (!db_update('partyprice', "
						UPDATE partyprice SET
							TIME_HOUR	= $set_hour,
							TIME_MINS	= $set_mins,
							TIME_TYPE	= 'till',
							MUSERID		= 0,
							MSTAMP		= ".time()."
						WHERE $wherep")
					) {
						return false;
					}
				}
			}
		}
	}
	return $ladies_free === null ? null : [$ladies_free, $ladies_free_till];
}

function detect_max_visitors(stdClass $event): ?int {
	if (!property_exists($event, 'description')) {
		return null;
	}
	if (($m = 10) && preg_match('"'.START_MARK.'max(?:imum)?\h*capacity:?\h*(\d+)(?:\h*people)?'.END_MARK.'"uis', $event->description, $match)
	||	($m = 11) && preg_match('"'.START_MARK.'there\h*are\h*only\h*(\d+)\h*(?:spots|tickets)\h*available'.END_MARK.'"uis', $event->description, $match)
	||	($m = 12) && preg_match('"'.START_MARK.'er\h*zullen\h*(?:slechts\h*)?(\d+)\h*'.RE_TICKETS.'\h*beschikbaar\h*zijn'.END_MARK.'"uis', $event->description, $match)
	||	($m = 13) && preg_match('"'.START_MARK.'er\h*zijn\h*(?:slechts\h*)?(\d+)\h*'.RE_TICKETS.'\h*beschikbaar'.END_MARK.'"uis', $event->description, $match)
	||	($m = 14) && preg_match('"'.START_MARK.'limited\h*capacity\h*(\d+)'.END_MARK.'"uis', $event->description, $match)
	) {
		$max = (int)$match[1];
		__debug('detected.'.$m.' max capacity '.$max.': '.utf8_mytrim($match[0]));
		return $max;
	}
	return null;
}

function detect_door_more(stdClass $event): ?bool {
	if (!property_exists($event, 'description')) {
		return null;
	}
	if (($m = 1) && preg_match('"'.START_MARK.RE_ALLOW_SOME_CHARS.'door\h*(?:=|is)\h*more\b"ui', $event->description, $match)
	||	($m = 2) && preg_match('"'.START_MARK.RE_ALLOW_SOME_CHARS.'deur\h*(?:verkoop|tickets)?[\h=]+meer'.END_MARK.'"uis', $event->description, $match)
	||	($m = 3) && preg_match('"'.START_MARK.RE_ALLOW_SOME_CHARS.'door(?:\h*sale)?[\h:]*more\b"ui', $event->description, $match)
	||	($m = 4) && preg_match('"'.START_MARK.RE_ALLOW_SOME_CHARS.'more\h*at\h*the\h*door\b"ui', $event->description, $match)
	||	($m = 5) && preg_match('"'.START_MARK.RE_ALLOW_SOME_CHARS.'deurverkoop\h*mogelijk[.!]"uis', $event->description, $match)
	) {
		__debug('detected.'.$m.' door = more: '.utf8_mytrim($match[0]));
		return true;
	}
	return null;
}

function detect_doorprice(stdClass $event, ?bool &$only_doorsale): ?array {
	if (!property_exists($event, 'description')) {
		return null;
	}

	$prices = null;

	$sep = '[\h<>]+';

	if (preg_match('"'.RE_DOOR.'\h*'.RE_PRICE.$sep.RE_TIME.$sep.RE_PRICE.END_MARK.'"uis', $event->description, $match)) {
		$doorprice = parse_price($match, 1);
		$till_time = parse_till($match, 3);
		[$hour, $mins] = parse_time($till_time);

		$prices[] = [
			'PRICE_SALE_TYPE'	=> 'door',
			'PRICE_INCFEE'		=> false,
			'PRICE_AMOUNT'		=> $doorprice,
			'PRICE_TIME_TYPE'	=> 'till',
			'PRICE_TILL_HOUR'	=> $hour,
			'PRICE_TILL_MINS'	=> $mins,
		];

		$afterprice = parse_price($match, 6);

		__debug('detect price <> time <> price: '.$doorprice.' <> '.$till_time.' <> '.$afterprice);

		$prices[] = [
			'PRICE_SALE_TYPE'	=> 'door',
			'PRICE_INCFEE'		=> false,
			'PRICE_AMOUNT'		=> $afterprice,
		];
	}

	# BASIS
	if (preg_match(
		'"' .
		'BASISPLAATS[:\h]*'.RE_PRICE.RE_BRACKETS.RE_ENTRANCE.'\h*'.RE_TILL_TIME.RE_BRACKETS.'\s*' .
		RE_ENTRANCE.'\h*'.RE_AFTER.'\h*'.RE_TIME.'[:\h]*'.RE_PRICE .
		'"uis', $event->description, $match
	)) {
		$doorprice		= parse_price($match, 1);
		$till_time		= parse_till ($match, 3);
		[$hour, $mins]	= parse_time($till_time);

		$prices[] = [
			'PRICE_SALE_TYPE'	=> 'door',
			'PRICE_INCFEE'		=> false,
			'PRICE_AMOUNT'		=> $doorprice,
			'PRICE_TIME_TYPE'	=> 'till',
			'PRICE_TILL_HOUR'	=> $hour,
			'PRICE_TILL_MINS'	=> $mins,
		];

		$afterprice					= parse_price($match, 9);
		$then_time  				= parse_till ($match, 6);
		[$after_hour, $after_mins]	= parse_time($then_time);

		$prices[] = [
			'PRICE_SALE_TYPE'	=> 'door',
			'PRICE_INCFEE'		=> false,
			'PRICE_AMOUNT'		=> $afterprice,
			# 'PRICE_TILL_HOUR'	=> $after_hour,
			# 'PRICE_TILL_MINS'	=> $after_mins,
		];
	}


	$doorsale = '(?:' .
		RE_DOOR.'\h*(?:verkoop|tickets)?|' .
		'aan\h*de\h*deur|' .
		RE_TICKETS.'\h*'.RE_DOOR.'|' .
		RE_AT_THE_DOOR.'\h*geen\h*gezeur[\-—>\h]*|' .
		'doors?(?:\h*sale|\h*price|\h*tickets)?|' .
		'(?:tickets\h*)?'.RE_AT_THE_DOOR.'|' .
		'entrance(?:\h*fee)?|' .
		'toegang|' .
		'entree|' .
		'door\h+is\h+more|' .
		'ingresso|' .
		'eintritt' .
		')(?:\h*\(?kassa\)?)?';

	$m = 0;
	if (
#		($m = 1) && preg_match($q = '"'.START_MARK.$doorsale.'[:\h]*'.RE_PRICE.RE_BRACKETS.RE_PRESALE.RE_BRACKETS.RE_PRICE.RE_BRACKETS.'(?:door|deur|'.RE_AT_THE_DOOR.')'.RE_BRACKETS.END_MARK.'"uis',$event->description,$match)

		($m = 2) && preg_match('"'.START_MARK.$doorsale.RE_COLON_IS.RE_PRICE.'[\h\(\[]*('.RE_ONLY_DOOR_base.')[\)\]]?'.END_MARK.'"uis', $event->description, $match)
#	||	($m = 3) && preg_match($q = '"'.START_MARK.$doorsale.RE_COLON_IS.RE_PRICE.'(\h*'.RE_CASH_ONLY.')?(?!'.RE_BRACKETS.RE_PRESALE.')'.END_MARK.'"uis',$event->description,$match)

	||	($m = 3) && preg_match('"'.START_MARK.$doorsale.RE_COLON_IS.RE_PRICE.'(\h*'.RE_CASH_ONLY.'|'.RE_BRACKETS.'\h*'.RE_PRICE.'\h*'.RE_TILL_TIME.')?(?!'.RE_BRACKETS.'(?:'.RE_PRESALE.'|'.RE_EX_FEE.'))'.END_MARK.'"uis', $event->description, $match)
	&& !preg_match('"\b'.$match[1].'[:h]\b"ui', $match[0])	# no times

	||	($m = 4) && preg_match('"'.START_MARK.RE_PRICE.'[\(\h]*'.$doorsale.'\)?'.END_MARK.'"uis', $event->description, $match)
	||	($m = 5) && preg_match(
		$q =
			'"\b(?:entrance|entree|'.RE_TICKETS.'(?:\h*zijn)?)' .
			'[:\h]*'.RE_PRICE.'\h*' .
			'\(?((?:alleen\h*(?:tickets\h*)?)?(?:at\h*the\h*door|aan\h*de\h*deur))\)?\b"ui', $event->description, $match
	)
	||	($m = 6) && preg_match('"'.START_MARK.'(?:if\h*you.?re\h*)?not\h*on\h*the\h*list,\h*tickets\h*(?:are\h*)?'.RE_PRICE.'\h*at\h*the\h*door'.END_MARK.'"uis', $event->description, $match)
	||	($m = 7) && preg_match('"'.START_MARK.RE_TICKETS.RE_COLON_IS.RE_ONLY.RE_PRICE.RE_BRACKETS.$doorsale.END_MARK.'"uis', $event->description, $match)
	||	($m = 8) && preg_match('"'.START_MARK.RE_PRICE.RE_BRACKETS.'(?:door|deur|'.RE_AT_THE_DOOR.')'.RE_BRACKETS.END_MARK.'"uis', $event->description, $match)
	||	($m = 9) && preg_match('"'.START_MARK.RE_TICKETS.RE_COLON_IS.'(?:'.RE_ONLY.')?'.RE_PRICE.RE_BRACKETS.'(?:(?:\b|\h+)and\h+)?('.RE_ONLY_DOOR_base.'!?)'.RE_BRACKETS.END_MARK.'"ui', $event->description, $match)
	||	($m = 10) && preg_match('"'.START_MARK.'ADK[:\h]*'.RE_PRICE.END_MARK.'"uis', $event->description, $match)
	) {
		$doorprice = parse_price($match, 1);

		$cash_only = null;

		$prices = [];

		if (!empty($match[3])) {
			if (preg_match('"(?:'.RE_ONLY_DOOR_base.')"ui', $match[3])) {
				$only_doorsale = true;
				__debug('detected.'.$m.' door only in door price: '.utf8_mytrim($match[0]));
			}
			if (preg_match('"'.RE_CASH_ONLY.'"ui', $match[3])) {
				$cash_only = true;
				__debug('detected.'.$m.' cash only in door price: '.utf8_mytrim($match[0]));
			}
			if (preg_match('"'.RE_PRICE.'\h*'.RE_TILL_TIME.'"uis', $match[3])) {
				$price			= parse_price($match, 4);
				$time			= parse_till ($match, 6);
				[$hour, $mins]	= parse_time($time);
				__debug('detected.'.$m.'  door price ('.$price.') till ('.$time.'): '.utf8_mytrim($match[3]));
				$prices[] = [
					'PRICE_SALE_TYPE'	=> 'door',
					'PRICE_INCFEE'		=> false,
					'PRICE_AMOUNT'		=> parse_price($match, 4),
					'PRICE_CASHONLY'	=> $cash_only,
					'PRICE_TIME_TYPE'	=> 'till',
					'PRICE_TILL_HOUR'	=> $hour,
					'PRICE_TILL_MINS'	=> $mins,
				];
			}
		}

		__debug('detected.'.$m.' door price ('.$doorprice.'): '.utf8_mytrim($match[0]));
		$prices[] = [
			'PRICE_SALE_TYPE' => 'door',
			'PRICE_INCFEE'	=> false,
			'PRICE_AMOUNT'	=> $doorprice,
			'PRICE_CASHONLY'  => $cash_only,
			#			'PRICE_TIME_TYPE'	=> null
		];
	}
	$on_entry = '(?:on\h*(?:entry|entrance))';
	$time_re  = '(?:(\d+)[:\.](\d+)[uh]?|(midnight))';
	$time_sep = '(before|until|till|na|after|from|voor)';
	if (($m = 101) && preg_match_all('"'.START_MARK.RE_PRICE. '\h*'.$on_entry.'?\h*'.$time_sep.'\h*'.$time_re.'"uis', $event->description, $matches)
	||	($m = 102) && preg_match_all('"'.START_MARK.$doorsale.'\h*'.$on_entry.'?\h*'.$time_sep.'\h*'.$time_re.'[:\h]*'.RE_PRICE.'"uis', $event->description, $matches)
#	||	($m = 103) && preg_match_all($q = '"'.START_MARK.$doorsale.'\h*'.$on_entry.'?\h*'.$time_sep.'\h*midnight[:\h]*'.RE_PRICE.'"uis',$event->description,$matches)
	||	($m = 104) && preg_match_all('"'.START_MARK.RE_TILL_TIME.'[:\h]*'.RE_PRICE.END_MARK.'"uis', $event->description, $match)
	) {
		$pivot = [];
		foreach ($matches[0] as $i => $match) {
			switch ($m) {
			case 101:
				$euros = (int)$matches[1][$i];
				$cents = (int)$matches[2][$i];
				$till  = $matches[3][$i];
				if (empty($matches[6][$i])) {
					$hour = (int)$matches[4][$i];
					$mins  = (int)$matches[5][$i];
				} else {
					$hour = $mins = 0;
				}
				break;
			case 102:
				$till = $matches[1][$i];
				if (empty($matches[3][$i])) {
					$hour = (int)$matches[2][$i] ?: 0;
					$mins  = (int)$matches[3][$i] ?: 0;
				} else {
					$hour = $mins = 0;
				}
				$euros = (int)$matches[5][$i] ?: 0;
				$cents = (int)$matches[6][$i] ?: 0;
				break;
			default:
				error_log($error = 'Unsupported case detect_doorprice().'.$m);
				_error($error);
				break;
			}

			$doorprice = $euros * 100 + $cents;

			$time = $hour * 100 + $mins;

			switch (strtolower($till)) {
			case 'before':
			case 'till':
			case 'until':
			case 'voor':
				$till = 'till';
				break;
			case 'from':
			case 'after':
			case 'na':
				if (isset($pivot['till'])
				&& $pivot['till'] == $time
				) {
					$till = null;
					$time = $hour = $mins = null;
				} else {
					$till = 'from';
				}
				break;
			}

			$pivot[$till] = $time;

			__debug('detected.'.$m.' door ?till price ('.$doorprice.($till ? ' '.$till.' '.$time : null).'): '.utf8_mytrim($matches[0][$i]));

			$price = [
				'PRICE_SALE_TYPE' => 'door',
				'PRICE_INCFEE'	  => false,
				'PRICE_AMOUNT'	  => $doorprice,
			];
			if ($till) {
				[$hour, $mins] = parse_time($time);
				$price += [
					'PRICE_TIME_TYPE' => $till,
					'PRICE_TILL_HOUR' => $hour,
					'PRICE_TILL_MINS' => $mins,
				];
			}
			$prices[] = $price;
		}
	}
	if (preg_match(
		'"'.START_MARK.'\W*\h*'.RE_FREE.'\h*'.RE_ENTRANCE.'?\h*'.RE_TILL_TIME.'[\r\n,]+' .
		'[^\w\d]*\h*daarna '.RE_PRICE.END_MARK.'"uis', $event->description, $match
	)
	) {
		$time = parse_till($match, 1);
		[$hour, $mins] = parse_time($time);
		$price	= [
			'PRICE_SALE_TYPE'	=> 'door',
			'PRICE_INCFEE'		=> false,
			'PRICE_AMOUNT'		=> 0,
			'PRICE_TIME_TYPE'	=> 'till',
			'PRICE_TILL_HOUR'	=> $hour,
			'PRICE_TILL_MINS'	=> $mins,
		];
		$prices[] = $price;

		$price	= [
			'PRICE_SALE_TYPE' => 'door',
			'PRICE_INCFEE'	=> false,
			'PRICE_AMOUNT'	=> ($pprice = parse_price($match, 4)),
		];
		$prices[] = $price;

		__debug('free till time '.$time.' then price '.$pprice.' detected: '.utf8_mytrim($match[0]));
	}
	if (($m = 1000) && preg_match($r = '"'.START_MARK.'(?:'.RE_ENTRANCE.'|damage|'.RE_DOOR_SALE.')[\h:]+'.RE_PRICE.'\h*[<>]\h*'.RE_TIME.'\h*[<>]\h*'.RE_PRICE.END_MARK.'"uis', $event->description, $match)
#	||	($m = 1001) && preg_match($r = '"'.START_MARK.RE_PRICE.'\h*<\h*'.RE_TIME.'\h*>\h*'.RE_PRICE.END_MARK.'"uis',$event->description,$match)
	) {
		$pre_price		= parse_price($match, 1);
		$post_price		= parse_price($match, 6);
		$till_time 		= parse_till ($match, 3);
		[$hour, $mins] = parse_time ($till_time);

		$prices[] = [
			'PRICE_SALE_TYPE'	=> 'door',
			'PRICE_INCFEE'		=> false,
			'PRICE_AMOUNT'		=> $post_price,
		];
		$prices[] = [
			'PRICE_SALE_TYPE'	=> 'door',
			'PRICE_INCFEE'		=> false,
			'PRICE_AMOUNT'		=> $pre_price,
			'PRICE_TIME_TYPE'	=> 'till',
			'PRICE_TILL_HOUR'	=> $hour,
			'PRICE_TILL_MINS'	=> $mins,
		];
		__debug("detected.$m door price ($post_price), till $till_time ($pre_price): ".utf8_mytrim($match[0]));
	}
	if (($m = 10000) && preg_match('"'.START_MARK.RE_TILL_TIME.'[:\h]*'.RE_PRICE.END_MARK.'"uis', $event->description, $match)
	) {
		$price			= parse_price($match, 4);
		$till_time		= parse_till ($match, 1);
		[$hour, $mins] = parse_time ($till_time);
		$prices[] = [
			'PRICE_SALE_TYPE'	=> 'door',
			'PRICE_INCFEE'		=> false,
			'PRICE_AMOUNT'		=> $price,
			'PRICE_TIME_TYPE'	=> 'till',
			'PRICE_TILL_HOUR'	=> $hour,
			'PRICE_TILL_MINS'	=> $mins,
		];
		__debug("detected.$m door price ($price), till $till_time): ".utf8_mytrim($match[0]));
	}
	return $prices;
}

function detect_presale_start(stdClass $event): ?array {
	if (!property_exists($event, 'description')) {
		return null;
	}
	static $__re_months = null;
	static $__datepart;
	if ($__re_months === null) {
		$__re_months	= implode('|', get_month_names());
		$__re_day_names = implode('|', get_day_names());
		$__datepart	 = '(?:'.$__re_day_names.')?\.?\h*'.
			'(?:'.
			'(?:the\h+)?(\d+)(?:st|th|nd)?\h*(?:of\h*)?('.$__re_months.')\.?'.
			'|'.'('.$__re_months.')\.?\h*(?:the\h+)?(\d+)(?:st|th|nd)?'.
			'|'.'(\d+)[\-\.](\d+)[\-\.]'.
			')\h*(20\d\d)?';
	}

	static $__timepart =
		/** lang PhpRegExp */
		'(?:(?:starten\h*)?(?:om|at|,)\h*)?[\h-]*(\d+)[:.](\d{2})(?:u(?:ur)?|h(?:our)?)?';
	static $__tickets =
		/** lang PhpRegExp */
		'(?:(?:de|the)\h*)?(?:'.
			'ticket(?:s|\h*sales?|\h*verkoop)|'.
			'pre[\h-]*sale(?:kaarten)?|'.
			'(?:kaart|voor|de\s+)verkoop|'.
			'["\'\h]*early[\h-]*bird[s"\'\h]*(?:\h*ticket(?:s|sale|\h*verkoop))?'.
		')';

	$__datetime = $__datepart.'(?:\h*'.$__timepart.')?';

	# easier than making more complex regexes:
	$event->description = preg_replace('"\b(?:12PM|noon)\b"ui', '12:00', $event->description);
	$event->description = preg_replace('"\bmidnight\b"ui', '24:00', $event->description);
	$event->description = preg_replace('"\h+(\d{1,2})u\b"ui', ' $1:00', $event->description);
	$event->description = preg_replace('"\h+(\d{1,2})[:.](\d{2})\h*uur\b"ui', ' $1:$2 ', $event->description);

	if (($m = 1) && preg_match('"'.START_MARK.RE_ALLOW_SOME_CHARS.$__tickets.'[^\n.]*vanaf[^\n.]*?(?:a\.s\.[^\n.]*)?\h*'.$__datetime.'\b"uis', $event->description, $match)
	||	($m = 2) && preg_match('"'.START_MARK.RE_ALLOW_SOME_CHARS.$__tickets.'[^\n.]*zal(?:a\.s\.[^\n.]*)?\h*'.$__datetime.'\b"uis', $event->description, $match)
	||	($m = 3) && preg_match('"'.START_MARK.RE_ALLOW_SOME_CHARS.$__tickets.'[^\n.]*(?:starts|zijn\h*verkrijgbaar\h*vanaf|(?:start|begint|online)(?:\h+op)?|will\h*go\h*on\h*sale|will start on)[^\n.]*?'.$__datetime.'\b"uis', $event->description, $match)
	||	($m = 4) && preg_match('"'.START_MARK.RE_ALLOW_SOME_CHARS.'\W*(?:'.RE_TICKETS.'|(?:start\h+)?'.RE_PRESALE.'|start\h*kaartverkoop)\W*\h*(?:starts?\h*)?'.$__datetime.END_MARK.'"uis', $event->description, $match)
	||	($m = 5) && preg_match('"'.$__datetime.'\h*(?:beginnen\h*we\h*met|begint)\h*de\h*(?:voor)?verkoop van de (?:early\h*birds|tickets)'.END_MARK.'"uis', $event->description, $match)
	||	($m = 6) && preg_match('"'.START_MARK.RE_ALLOW_SOME_CHARS.$__tickets.'\h*gaan\h*'.$__datetime.'\h*in\h*de\h*(?:voor)?verkoop'.END_MARK.'"uis', $event->description, $match)
	) {
		change_timezone($event->timezone);
		if ($event->stamp) {
			$event_stamp = $event->stamp;
		} elseif ($event->start_time) {
			$event_stamp = strtotime($event->start_time);
		} elseif ($event->date) {
			$event_stamp = strtotime($event->date);
		} else {
			$event_stamp = CURRENTSTAMP + ONE_YEAR;
		}
		__debug('found presale date '._datetime_get($event_stamp).' in: '.$match[0]);

		/*		if (!isset($match[5])) {
					$pos = mb_strpos($event->description,$match[0]);
					error_log('WARNING weird presale detected in: '.mb_substr($event->description,$pos,mb_strlen($match[0]) + 50));
				}*/

		$sale_month = 0;
		if (!empty($match[5])) {
			$sale_month = $match[6];
			$sale_day   = $match[5];
			if ($sale_month > 12) {
				$store_day  = $sale_month;
				$sale_month = $sale_day;
				$sale_day   = $store_day;
			}
		} else {
			# 27th of augustus
			# 18 september
			# OR:
			# august 27th
			if (!empty($match[3])) {
				[,,, $sale_month_str, $sale_day] = $match;
			} else {
				[, $sale_day, $sale_month_str] = $match;
			}
		}
		$sale_year = getifset($match, 7);

		if ($have_time = isset($match[8])) {
			$sale_hour = $match[8];
			$sale_mins = $match[9];
		} else {
			$sale_hour = $sale_mins = 0;
		}

		if (empty($sale_year)) {
			# if event_stamp = 0, then current year is used
			[$sale_year] = _getdate($event_stamp);
		}

		$try_sale_year = $sale_year;
		do {
			$sale_date  = $try_sale_year.'-'.($sale_month ?: get_month_from_string($sale_month_str)).'-'.$sale_day.' '.$sale_hour.':'.$sale_mins;
			$sale_stamp = strtotime($sale_date);
			--$try_sale_year;
		} while ($sale_stamp >= $event_stamp);

		__debug('detected presale.'.$m.', built presale date: '.$sale_date);

		change_timezone();

		if ($sale_stamp) {
			__debug('detected.'.$m.' presale start: '.utf8_mytrim($match[0]));
			return [$sale_stamp, !$have_time, $sale_date];
		}
	}
	return null;
}

function detect_genres(stdClass $event, ?string &$genrestr = null): ?array {
	if (!property_exists($event, 'description')) {
		return null;
	}
	if (($m = 1) && preg_match('"'.START_MARK.'(?:music(?:\h*style)?|muziek(?:\h*(?:style|genres?))?|genres?)\h*:\h*(.*?)(?:\n|$)"uis', $event->description, $match)
	) {
		$genrestr = $match[1];
		$genres   = [];
		$parts	= preg_split('"([,|/&]+\h*|\h+(?:[\-\x{2013}]|en|I)\h+)"ui', $match[1]);
		if (empty($parts)) {
			mail_log('empty parts', get_defined_vars());
		}
		require_once '_genres.inc';
		foreach ($parts as $part) {
			$part = utf8_mytrim($part, '*!.');
			$part = get_clean_genre($part);
			if (!$part) {
				continue;
			}
			$genre = get_explicit_genre(recognize_genre($part) ?? $part);
			if ($genre) {
				$genres[] = $genre;
			}
		}
		__debug('found.'.$m.' matching genres: '.implode(', ', $genres));
		return $genres;
	}
	return null;
}

function detect_dresscode(stdClass $event): ?string {
	if (!property_exists($event, 'description')) {
		return null;
	}
	if (preg_match('"'.START_MARK.'(?:dress\h*code|kleding\h*voorschrift(?:en)?)\h*:\h*([^\n.]+)"uis', $event->description, $match)) {
		$dresscode = utf8_mytrim($match[1]);
		if ($dresscode) {
			__debug('detected dresscode ('.$dresscode.'): '.utf8_mytrim($match[0]));
			return utf8_mytrim($dresscode);
		}
	}
	return null;
}

function detect_presales(stdClass $event, array &$prices): null {
	if (!property_exists($event, 'description')) {
		return null;
	}

	if (preg_match('"'.START_MARK.RE_PRESALE.'\h*'.RE_PRICE.'\h*\((inc|ex)\.? fee\)"uis', $event->description, $match)) {
		$price = parse_price($match, 1);

		__debug('detected presale ('.$price.'): '.utf8_mytrim($match[0]));

		$prices[] = [
			'PRICE_SALE_TYPE'	=> 'presale',
			'PRICE_AMOUNT'		=> $price,
			'PRICE_INCFEE'		=> $match[3] === 'inc',
		];
	}
	return null;
}

function detect_attending_free(stdClass $event, array &$prices): void {
	if (!property_exists($event, 'description')) {
		return;
	}

	foreach (['name', 'description'] as $key) {
		if (preg_match(
			'"\b(?:aanwezig|attend(?:ing)?)\h*(?:on\h*our\h*facebook[\h\-]*event)?' .
			'\h*(?:=|is|for)\h*' .
			'(?:'.RE_FREE.'(?:\h*'.RE_ENTRANCE.')?|guest\h*list|gasten\h*lijst)(?:\h*'.RE_TILL_TIME.')?'.END_MARK.'"uis', $event->$key, $match
		)
		) {

			$price = [
				'PRICE_SALE_TYPE' => 'door',
				'PRICE_INCFEE'	  => false,
				'PRICE_AMOUNT'	  => 0,
				'PRICE_FBPRESENT' => true,
			];

			if (!empty($match[1])) {
				if (!is_number($match[1])
				||	 isset($match[2])
				&&	!is_number($match[2])
				) {
					mail_log('match for detect_attending_free contains non-numbers', item: $match);
				}

				[$hour, $mins] = parse_time($time = $match[1] * 100 + ($match[2] ?? 0));
				$price += [
					'PRICE_TIME_TYPE' => 'till',
					'PRICE_TILL_HOUR' => $hour,
					'PRICE_TILL_MINS' => $mins,
				];

			}
			__debug('detected attending = free'.(empty($time) ? null : ' (till '.$time.')').': '.utf8_mytrim($match[0]));

			$prices[] = $price;

			return;
		}
	}
}
/* @return positive-int|null */
function parse_till(array $match, int $ndx): ?int {
	if (!($time_str = $match[$ndx] ?? null)) {
		return null;
	}
	switch ($base = str_replace(' ', '', strtolower($time_str))) {
	case 'midnight':
	case 'middernacht':
		return 0;

	case 'noon':
		return 1200;

	default:
		if (preg_match('"^(?<hour>\d+)[\h.:]?(?:(?<mins>\d{2})[uh]?)?$"', $base, $time_match)) {
			$time = (int)$time_match['hour'] * 100;
			if (isset($time_match['mins'])) {
				$time += (int)$time_match['mins'];
			}
			return $time;
		}
		if (preg_match('"^(?<hour>\d+)\s*(?<xm>[ap]m)$"', $base, $time_match)) {
			$hour = (int)$time_match['hour'];
			$xm	  = $time_match['xm'];
			$time = $hour < 100 ? $hour * 100 : $hour;
			if ($xm[0] === 'p') {
				$time += 1200;
			}
			return $time;
		}
		mail_log("parse_till, time not understood, match: $match[$ndx], source: $match[0]", get_defined_vars());
		return null;
	}
}

/* @return list<int<0, 23>, int<0,59>> */
function parse_time(int|string $time): array {
	if ( $time === 2400
	||	 $time === '2400'
	||	!$time
	) {
		return [0, 0];
	}
	$hour = (int)($time / 100);
	$mins  = $time - 100 * $hour;
	# round to nearest 5 minutes
	$mins  = (int)(5 * ceil($mins / 5));
	if ($mins === 60) {
		++$hour;
		$mins = 0;
		if ($hour === 24) {
			$hour = 0;
		}
	}
	return [$hour, $mins];
}

/* @return positive-int */
function parse_cents(array $match, int $ndx): int {
	if (!empty($match[$ndx])
	&&	$match[$ndx] !== '-'
	) {
		return (int)$match[$ndx];
	}
	return 0;
}

/* @return positive-int */
function parse_price(array $match, int $ndx): int {
	$euros = (int)$match[$ndx];
	$cents = parse_cents($match, $ndx + 1);
	return $euros * 100 + $cents;
}
