<?php

# FIXME: 'group' type is actually not a type indicator, and should be extract from the TYPE field
#		 into it's own MULTIPLE field

const NEW_STUFF = SERVER_SANDBOX;

require_once 'defines/artist.inc';

function preamble(): void {
	if (!have_admin('artist')
	&&  $_REQUEST['sID']
	&&  $_REQUEST['sID'] === 121706
	) {
		page_problem(410);
	}
}

function display_header(): void {
	require_once '_feed.inc';
	show_feed('agenda', FEED_HEADER);
}

function perform_commit(): ?bool {
	switch ($_REQUEST['ACTION']) {
	default:
	case null:											return null;
	case 'commit':										return item_commit();
	case 'accept':
	case 'unaccept':	require_once '_accept.inc';		return item_set_accept($_REQUEST['ACTION']);
	case 'remove':		require_once '_remove.inc';		return remove_element();
	case 'combine':		require_once '_combine.inc';	return combine_element();
	case 'employees':	require_once '_employees.inc';	return employee_action();
	case 'names':		require_once '_itemnames.inc';	return item_commit_names();
	}
}

function display_body(): void {
	switch ($_REQUEST['ACTION']) {
	case 'searchresult':
	case null:
		artist_display_overview();
		return;

	case 'interviews':
	case 'reviews':
	case 'news':
	case 'archive':
	case 'commit':
	case 'combine':
	case 'comments':
	case 'comment':
	case 'ratings':
	case 'rating':
	case 'rate':
	case 'votes':
	case 'biography':
	case 'fans':
	case 'single':											artist_display_single(); return;
	case 'register':
	case 'form':											artist_display_form(); return;
	case 'bioform':		require_once '_bio.inc';			show_bio_form(); return;
	case 'photos':											artist_display_photos(); return;
	case 'needupdate':	require_once '_needupdates.inc';	show_needupdates(); return;
	case 'remove':											$GLOBALS['commitresult'] ? artist_display_overview() : artist_display_single(); return;
	case 'combinewith':	require_once '_combine.inc'; 		show_combine_with(); return;
	case 'aliasoverview':									artist_display_alias_overview(); return;
	case 'memberoverview':									artist_display_member_overview(); return;
	case 'names':		require_once '_itemnames.inc';		item_display_names_overview(); return;
	case 'employees':
		require_once '_employees.inc';
			!$GLOBALS['commitresult']
		&&	$_REQUEST['SUBACTION'] !== 'form'
		?	display_employees()
		:	display_employee_form();
		return;

	default:
		require_once '_search_via_url.inc';
		search_via_url();
		not_found();
	}
}

function artist_display_alias_overview(): void {
	if (!require_admin('artist')
	||	!($artistid = require_idnumber($_REQUEST,'sID'))
	) {
		return;
	}
	layout_show_section_header(null,element_plural_name('alias'));

	if (!($name = memcached_artist($artistid))) {
		if ($name !== false) {
			not_found();
		}
		return;
	}
	if (have_something_trim($_POST, 'NAME', utf8: true)) {
		require_once '_artist.inc';
		$sname = $_POST['NAME'];
		if (!($options = query_artists('
				(	artist.NAME LIKE "%'.addslashes($sname).'%"'.
					(is_number($sname) ? ' OR ARTISTID = '.$sname : '').'
				)
			AND ARTISTID NOT IN (SELECT ALSOID FROM artistalias WHERE ARTISTID = '.$artistid.')
			AND ARTISTID != '.$artistid
		))) {
			register_error('artist:error:no_artists_found_with_name_LINE', DO_UBB, ['NAME' => utf8_to_win1252($sname)]);
		} elseif (($optioncnt = count($options)) > 1) {
			register_warning('artist:warning:multiple_artists_found_choose_one_LINE', ['CNT' => $optioncnt]);
			generate_name_doubles($options, 'NAME', 'artist');
			sort_lineup_artists($options);
		} else {
			$_REQUEST['ALSOID'] = array_key_first($options);
			unset($options);
		}
	}
	if ($alsoid = have_idnumber($_REQUEST,'ALSOID')) {
		if (!($ok = db_single('artist', "SELECT 1 FROM artist WHERE ARTISTID != $artistid AND ARTISTID = $alsoid"))) {
			if ($ok !== false) {
				not_found('artist', $alsoid);
			}
			return;
		}
		if (false === ($artistlist = db_simpler_array('artistalias','
			SELECT ALSOID
			FROM artistalias
			WHERE ARTISTID = '.$artistid))
		) {
			return;
		}
		assert(is_array($artistlist)); # Satisfy EA inspection
		$artistlist[] = $artistid;
		$hidden = isset($_POST['HIDDEN']) ? 1 : 0;
		foreach ($artistlist as $tmpartistid) {
			$vals[] = '('.$tmpartistid.','.$alsoid.','.CURRENTUSERID.','.CURRENTSTAMP.','.$hidden.')';
			$vals[] = '('.$alsoid.','.$tmpartistid.','.CURRENTUSERID.','.CURRENTSTAMP.','.$hidden.')';
		}
		if (db_replace('artistalias','
			REPLACE INTO artistalias (ARTISTID,ALSOID,CUSERID,CSTAMP,HIDDEN)
			VALUES '.implode(',', $vals))
		) {
			register_notice('artist:notice:alias_added_LINE');
		}

		# copy artistmembers from other artist and to current artist
		require_once '_artistmember.inc';
		copy_artistmember($artistid, $alsoid);
		copy_artistmember($alsoid, $artistid);
	}
	layout_open_box('artist');
	layout_box_header(__('artist:header:aliases_of',DO_UBB,array('ARTISTID'=>$artistid)));

	if (false === ($aliases = db_simple_hash(['artistalias', 'artist'],'
		SELECT ALSOID,artistalias.HIDDEN,NAME
		FROM artistalias
		JOIN artist ON artist.ARTISTID = ALSOID
		WHERE artistalias.ARTISTID = '.$_REQUEST['sID']
	))) {
		return;
	}
	if ($aliases) {
		layout_open_table(TABLE_FULL_WIDTH);
		foreach ($aliases as $alsoid => $info) {
			[$hidden, $name] = keyval($info);
			layout_start_rrow($hidden ? ROW_LIGHT : 0);
			echo get_element_link('artist', $alsoid, $name);
			layout_next_cell(class: 'right');
			do_inline(
				$hidden ? 'action:make_visible' : 'action:make_invisible',
				null,
				'POST', '/artist.act',
				'ACT', "function(req, self) { if (req.status === 200) { changerow(self, 'toggleclass', 'light'); } return req.ok; }", [
					'ARTISTID'	=> $artistid,
					'OTHERID'	=> $alsoid,
					'PART'		=> 'alias',
					'ACTION'	=> $hidden ? 'unhide' : 'hide'
			]);
			?> <?= MIDDLE_DOT_ENTITY ?> <?
			do_inline(
				'action:remove',
				'artist:confirm:alias_removal_LINE',
				'POST', '/artist.act',
				'ACT', "function(req, self) { if (req.status === 200) { changerow(self, 'remove'); } }", [
					'ARTISTID'	=> $artistid,
					'OTHERID'	=> $alsoid,
					'PART'		=> 'alias',
					'ACTION'	=> 'remove'
			]);
			layout_stop_row();
		}
		layout_close_table();
	}
	layout_close_box();
	?><form<?
	?> class="block"<?
	?> accept-charset="utf-8"<?
	?> autocomplete="off"<?
	?> onsubmit="return submitForm(this);"<?
	?> method="post"<?
	?> action="/artist/<?= $_REQUEST['sID'] ?>/aliasoverview"<?
	?>><?= Eelement_name('alias') ?> <?
	if (isset($optioncnt)
	&&	$optioncnt > 1
	) {
		?><select required name="ALSOID"><?
		foreach ($options as $option) {
			$option['ALLOW'] = true;
			$selected = !utf8_strcasecmp($option['NAME'], $sname);
			show_artist_option($option, $addempty, $selected, false);
		}
		?></select><?
	} else {
		show_input([
			'type'			=> 'search',
			'name'			=> 'NAME',
			'id'			=> 'name',
			'autofocus'		=> true,
			'required'		=> true,
			'class'			=> 'short',
			'placeholder'	=> element_name('name_or_id'),
		]);
	}
	show_input([
		'type'			=> 'submit',
		'value_escaped'	=> __('action:add'),
	]);
	?><label><?
	show_input([
		'type'		=> 'checkbox',
		'name'		=> 'HIDDEN',
		'checked'	=> isset($_POST['HIDDEN']),
		'value'		=> '1'
	]);
	?> <?= __('status:invisible')
	?></label></form><?
}

function artist_display_member_overview() {
	if (!require_admin('artist')
	||	!require_idnumber($_REQUEST,'sID')
	) {
		return;
	}
	layout_section_header(Eelement_plural_name('groupmember'));

	if (!($name = memcached_artist($artistid = $_REQUEST['sID']))) {
		if (false !== $name) {
			not_found();
		}
		return;
	}
	if (have_something_trim($_POST, 'NAME')) {
		require_once '_artist.inc';
		// name!
		$sname = $_POST['NAME'];
		$options = query_artists(
			'(artist.NAME LIKE "%'.addslashes($sname).'%"'.(is_number($sname) ? ' OR ARTISTID = '.$sname : '').')
			AND ARTISTID NOT IN (SELECT MEMBERID FROM artistgroup WHERE ARTISTID = '.$artistid.')
			AND ARTISTID != '.$artistid
		);
		if (!$options) {
			register_error('artist:error:no_artists_found_with_name_LINE', DO_UBB, ['NAME' => $sname]);

		} elseif (($optioncnt = count($options)) > 1) {
			register_warning('artist:warning:multiple_artists_found_choose_one_LINE', ['CNT' => $optioncnt]);
			generate_name_doubles($options, 'NAME', 'artist');
			sort_lineup_artists($options);

		} else {
			[$_REQUEST['MEMBERID']] = keyval($options);
			unset($options);
		}
	}
	if (have_idnumber($_REQUEST,'MEMBERID')) {
		$memberid = $_REQUEST['MEMBERID'];
		if (!($ok = db_single('artist','SELECT 1 FROM artist WHERE ARTISTID='.$memberid))) {
			if ($ok !== false) {
				not_found('artist', $memberid);
			}
			return;
		}
		if (db_insert('artistgroup','
			INSERT IGNORE INTO artistgroup SET
				CUSERID	 ='.CURRENTUSERID.',
				CSTAMP	 ='.CURRENTSTAMP.',
				ARTISTID ='.$artistid.',
				MEMBERID ='.$memberid)
		) {
			register_notice('artist:notice:added_to_group_LINE');
		}

		# copy artistmembers from memberid to artistid
		require_once '_artistmember.inc';
		copy_artistmember($artistid, $memberid);
	}
	display_messages();
	layout_open_box('artist');
	layout_box_header(__('artist:header:groupmembers_of',DO_UBB,array('ARTISTID'=>$artistid)));

	if (false === ($members = db_rowuse_hash(['artistgroup', 'artist'],'
		SELECT MEMBERID, NAME, ACTIVE
		FROM artistgroup
		JOIN artist ON artist.ARTISTID = MEMBERID
		WHERE artistgroup.ARTISTID = '.$artistid
	))) {
		return;
	}
	if ($members) {
		layout_open_table('fw default');
		foreach ($members as $memberid => $member) {
			layout_start_rrow($member['ACTIVE'] ? 0 : ROW_LIGHT);
			echo get_element_link('artist', $memberid, $member['NAME']);
			layout_next_cell(class: 'right');
			do_inline(
				$member['ACTIVE'] ? 'action:deactivate' : 'action:activate',
				null,
				'POST','/artist.act',
				'ACT','function(req,self){if(req.status==200)changerow(self,\'toggleclass\',\'light\');return req.status==200}',
				[	'ARTISTID'	=> $artistid,
						'OTHERID'	=> $memberid,
						'PART'		=> 'member',
						'ACTION'	=> $member['ACTIVE'] ? 'deactivate' : 'activate']
			);
			?> <?= MIDDLE_DOT_ENTITY ?> <?
			do_inline(
				'action:remove',
				'artist:confirm:member_removal_TEXT',
				'POST','/artist.act',
				'ACT','function(req,self){if(req.status==200)changerow(self,\'remove\')}',
				[	'ARTISTID'	=> $artistid,
						'OTHERID'	=> $memberid,
						'PART'		=> 'member',
						'ACTION'	=> 'remove']
			);
			layout_stop_row();
		}
		layout_close_table();
/*	} else {
		?><p><?= __('artist:info:no_groupmembers_LINE'); ?></p><?*/
	}
	layout_close_box();
	?><form<?
	?> accept-charset="utf-8"<?
	?> autocomplete="off"<?
	?> onsubmit="return submitForm(this)"<?
	?> method="post"<?
	?> action="/artist/<?= $artistid ?>/memberoverview"><?
	?><div class="block"><?= Eelement_name('groupmember') ?> <?
	if (isset($optioncnt)
	&&	$optioncnt > 1
	) {
		?><select required name="MEMBERID"><?
		foreach ($options as $option) {
			$selected = !utf8_strcasecmp($option['NAME'], $sname);
			show_artist_option($option, $addempty, $selected,false);
		}
		?></select><?
	} else {
		show_input([
			'type'			=> 'search',
			'name'			=> 'NAME',
			'id'			=> 'name',
			'autofocus'		=> true,
			'required'		=> true,
			'class'			=> 'short',
			'placeholder'	=> element_name('name_or_id'),
		]);
	}
	?><input type="submit" value="<?= __('action:add') ?>" /></div></form><?
}
function main_menu($artist = null) {
	if (!$_REQUEST['sID']
	&&	!$_REQUEST['ACTION']
	) {
		layout_open_menu();
		layout_menuitem(Eelement_name('overview'), '/artist', !$_REQUEST['ACTION']);
		if (have_user() && $_REQUEST['ACTION'] !== 'register') {
			layout_continue_menu();
			layout_menuitem(__C('action:add_artist'), '/artist/register');
		}
		layout_close_menu();
	}

	if (!have_user()) {
		return;
	}
	layout_open_menu();
	if ($artist) {
		$artist_href = '/artist/'.($artistid = $artist['ARTISTID']);
		if (have_admin('artist')) {
			layout_menuitem(__C('action:change'), $artist_href.'/form');
			layout_menuitem(__C('action:change_biography'), $artist_href.'/bioform');
			if ($artist['ACCEPTED']) {
				layout_menuitem(__C('action:reject'), $artist_href.'/unaccept');
			} else {
				layout_menuitem(__C('action:accept'), $artist_href.'/accept');
			}
			layout_menuitem(__C('action:connect_to_users'), $artist_href.'/employees');
			layout_menuitem(Eelement_plural_name('name'), $artist_href.'/names');
			layout_menuitem(Eelement_plural_name('alias'), $artist_href.'/aliasoverview');
			if (artist_type_is_group($artist['TYPE'])) {
				layout_menuitem(Eelement_plural_name('groupmember'), $artist_href.'/memberoverview');
			}
			layout_continue_menu();
			show_element_menuitems();
		} else {
			global $currentuser;
			$am_artist = $currentuser->is_artist($artistid);
			$may_change = !$artist['ACCEPTED'] && CURRENTUSERID === $artist['USERID'];
			if ($am_artist
			||	$may_change
			) {
				layout_menuitem(
					__C($may_change ? 'action:change' : 'action:submit_change'),
					$may_change ? $artist_href.'/form' : '/ticket/form?ELEMENT=artist;ID='.$artistid
				);
				layout_menuitem(__C('action:change_biography'), $artist_href.'/bioform');
			}
		}
	} else {
		layout_menuitem(__C('action:add'),'/artist/register');
	}
	layout_close_menu();
	if ($artist
	&&	have_admin('artist')
	) {
		require_once '_element_to_ticket_menu.inc';
		layout_open_menu();
		show_element_to_ticket_menu('artist', $artist['ARTISTID'], $artist);
		layout_close_menu();
	}
}

function artist_display_overview(): void {
	layout_show_section_header();

	main_menu();

	$show_advanced_search = false;

	$name = require_something_trim_clean_or_none($_REQUEST, 'NAME', utf8: true);

	if ($show_search_result = $_REQUEST['ACTION'] === 'searchresult') {
		if ($name) {
			?><div class="bold block"><?= Eelement_name('search_term') ?>: &quot;<?= escape_utf8($name) ?>&quot;</div><?

			$limit = 1000;
			require_once '_artistlist.inc';
			$artistlist = new _artistlist;
			$artistlist->show_camera = true;
			$artistlist->use_columns = true;
			$artistlist->name_like($name);
			$artistlist->order_by_name();
			$artistlist->limit = $limit + 1;
			if (!empty($_REQUEST['GID'])) {
				$show_advanced_search = true;
				if (!require_number_array($_REQUEST,'GID')) {
					return;
				}
				$artistlist->genres($_REQUEST['GID']);
			}
			if ($gender = have_element($_REQUEST,'GENDER', ['male', 'female', 'multi'])) {
				$show_advanced_search = true;
				$artistlist->gender($gender);
			}
			if ($countryid = have_idnumber($_REQUEST, 'COUNTRYID')) {
				$show_advanced_search = true;
				$artistlist->in_country($countryid);
			}
			if (!empty($_REQUEST['TYPE'])) {
				$show_advanced_search = true;
				if (!require_array($_REQUEST,'TYPE')) {
					return;
				}
				$artistlist->types($_REQUEST['TYPE']);
			}
		}
	}

	?><div id="normalsearch"><?
	?><form<?
	?> accept-charset="utf-8"<?
	?> onsubmit="return submitForm(this);"<?
	?> method="get"<?
	?> action="/artist/searchresult"><?

	if (!$show_advanced_search) {
		?><div class="block"><?
		?><input<?
		?> placeholder="<?= __('action:search_for_artist') ?>"<?
		?> onchange="getobj('advname').value = this.value;"<?
		?> type="search"<?
		?> name="NAME"<?
		?> id="artistname"<?
		?> required<?
		?> autofocus<?
		if (HOME_THOMAS) {
			?> autocomplete="name"<?
		}
		if ($name) {
			?> value="<?= escape_utf8($name) ?>"<?
		}
		?> /><?
		?><input class="lmrgn" type="submit" value="<?= __('action:search') ?>" /> <?= MIDDLE_DOT_ENTITY ?> <?
		?><a class="nowrap" onclick="unhide('advancedsearch'); hide('normalsearch'); return false;" href="/user/search"><?= __('action:advanced_search') ?></a><?
		?></div></form></div><?
	}

	?><div<?
	if (!$show_advanced_search) {
		?> class="hidden"<?
	}
	?> id="advancedsearch"><?
	artist_show_advanced_search();
	?></div><?

	if ($show_search_result) {
		if ($artistlist->query()) {
			if ($cnt = $artistlist->num_rows()) {
				if ($cnt === $limit + 1) {
					?><div class="block"><?= __('search:info:too_broad_TEXT', DO_NL2BR, ['LIMIT' => $limit])  ?></div><?
				} else {
					$artistlist->display();
				}
			} else {
				?><div class="block"><? __('search:info:nothing_found_LINE') ?></div><?
			}
		}

	} elseif (have_user()) {
		require_once '_favourite.inc';
		show_favourites();
	}
}

function artist_show_advanced_search(): void {
	require_once '_genrelist.inc';
	?><form accept-charset="utf-8" method="get" action="/artist/searchresult" onsubmit="return submitForm(this)"><?
	layout_open_box('white');
	layout_open_table('fw');

	# NAME
	layout_start_row();
	echo Eelement_name('name');
	layout_field_value();
	show_input([
		'id'			=> 'advname',
		'type'			=> 'search',
		'name'			=> 'NAME',
		'value_utf8'	=> $_REQUEST['NAME'] ?? null,
		'autocomplete'	=> HOME_THOMAS ? 'name' : null,
	]);

	# TYPE
	layout_restart_row();
	echo Eelement_name('function');
	layout_field_value();
	require_once '_artist_types.inc';
	show_artist_types_checkboxes();

	# GENDER
	layout_restart_row();
	echo Eelement_name('gender');
	layout_field_value();
	?><select name="GENDER"><?
		?><option></option><?
		?><option value="male"><?= element_name('male') ?></option><?
		?><option value="female"><?= element_name('female') ?></option><?
		?><option value="multi"><?= __('gender:mix') ?></option><?
	?></select><?

	# COUNTRY
	layout_restart_row();
	echo Eelement_name('country');
	layout_field_value();
	require_once '_countrylist.inc';
	?><select name="COUNTRYID"><?
		?><option></option><?
		_countrylist_display_options();
	layout_stop_row();

	# GENRES
	layout_restart_row();
	echo Eelement_plural_name('genre');
	layout_field_value();
	show_genre_selection(uncollapsed: false);

	layout_close_table();
	layout_close_box();
	?><div class="block"><input type="submit" value="<?= __('action:search') ?>" /></div></form><?
}

function artist_display_single(): void {
	require_once '_artistpart.inc';
	require_once '_presence.inc';

	if (!($artistid = require_idnumber($_REQUEST,'sID'))) {
		not_found();
		return;
	}

	if (!($artist = memcached_single_assoc_if_not_admin(
		['artist', 'contact_ticket'],
		['artist', 'contact_ticket'],'
		SELECT	artist.*,
				contact_ticket.STATUS AS TICKET_STATUS
		FROM artist
		LEFT JOIN contact_ticket ON contact_ticket.TICKETID = artist.TICKETID
		WHERE ARTISTID = '.$artistid,
		TEN_MINUTES
	))) {
		if ($artist !== false) {
			not_found();
		}
		return;
	}
	/** @noinspection PhpRedundantOptionalArgumentInspection */
	extract($artist, \EXTR_OVERWRITE);

	$artist_admin = have_admin('artist');

	require_once '_hide_stuff.inc';
	noindex_if_needed($artist);

	if (!$artist['ACCEPTED']
	&&	$artist['USERID'] !== CURRENTUSERID
	&&	!$artist_admin
	) {
		register_error('artist:error:not_approved_yet_LINE', ['ID' => $artistid]);
		return;
	}

	layout_show_section_header($artist);

	main_menu($artist);
	#show_check_mark_for_aspiring();

	if (have_user()) {
		require_once '_ticketlist.inc';
		show_connected_tickets();

		if (have_admin('logbook')) {
			require_once '_logbook.inc';
			show_logbook();
		}
		require_once '_favourite.inc';
		show_marking_menu();
	}
	if ($is_users = memcached_rowuse_hash_if_not_admin('artist', ['artistmember','user','user_account'], "
		SELECT	USERID, USERID AS ID, am.VISIBILITY_NAME, NICK AS NAME, ACTIVE, STATUS, ID AS ARTISTID,
				NAME AS USER_NAME, REALNAME AS USER_REALNAME
		FROM artistmember AS am
		JOIN user_account USING (USERID)
		JOIN user USING (USERID, NICK)
		WHERE ID = $artistid
		ORDER BY NICK",
		TEN_MINUTES)
	) {
		$visisize = 0;
		$invisisize = 0;
		require_once '_visibility.inc';
		require_once '_notify.inc';
		foreach ($is_users as $userid => &$user) {
			extract($user, \EXTR_OVERWRITE);
			if (
			#	$STATUS !== 'active'
				$STATUS === 'permbanned'
			||	!$artist_admin
			&&	!$ACTIVE
			) {
				continue;
			}
			if ($userid === CURRENTUSERID) {
				$i_am_this_artist = true;
			}
			if ($user['VISI'] = _visibility($user, 'NAME')) {
				if ($user['VISI'] !== ROW_FULL
				||	!is_reachable($userid, NOTIFY_MSG)
				) {
					$user['INVISI'] = true;
					continue;
				}
				++$visisize;
				if ($STATUS !== 'active') {
					++$invisisize;
				}
				$visilist[$userid] = $user;
				$visids[] = $userid;
			}
			if ($ACTIVE
			&&	($info = memcached_user_city($userid))
			) {
				$tmp_countryid = $info['COUNTRYID'];
				$member_countries['user'][$userid] = $tmp_countryid;
				if ($tmp_countryid > 0
				&&	$artist['COUNTRYID'] <= 0
				) {
					$diffcountries[$tmp_countryid] = true;
				}
			}
		}
		unset($user);
	}
	// CAMCNTS
	if (isset($visilist)) {
		require_once '_visibility.inc';
		if (false === ($artist['USER_CAMCNT'] = memcached_simple_hash(['user_appearance', 'image', 'user_account', 'externalsettings'],'
			SELECT user_appearance.USERID, COUNT(*)
			FROM user_appearance
			JOIN image USING (IMGID)
			JOIN user_account ON user_account.USERID = user_appearance.USERID
			JOIN externalsettings e ON e.USERID = user_account.USERID
			WHERE image.HIDDEN = 0
			   '._where_visible('e', 'APPEARANCES').'
			  AND STATUS NOT IN ("permbanned", "deleted")
			  AND user_appearance.USERID IN ('.implode(', ', $visids).')
			GROUP BY user_appearance.USERID'))
		) {
			return;
		}
	} else {
		$artist['USER_CAMCNT'] = false;
	}
	require_once '_visibility.inc';
	if (false === ($artist['ARTIST_CAMCNT'] = db_single(['artist_appearance', 'image', 'artist'],'
		SELECT COUNT(*)
		FROM artist_appearance
		JOIN image USING (IMGID)
		JOIN artist USING (ARTISTID)
		WHERE image.HIDDEN = 0
		  AND ARTISTID = '.$artistid.
		_where_visible('artist', 'APPEARANCES')))
	) {
		return;
	}
	$show_partylists = [];
	// CNTS
	if (!($cnts = memcached_single_assoc(['lineup', 'party'], '
		SELECT	COUNT(DISTINCT lineup.PARTYID) AS TOTAL_CNT,
				COUNT(DISTINCT IF(ACCEPTED = 1 AND CANCELLED = 0 AND MOVEDID = 0, lineup.PARTYID, NULL)) AS TOTAL_OK_CNT,
				COUNT(DISTINCT IF(STAMP < '.TODAYSTAMP.',lineup.PARTYID, NULL)) AS PAST_CNT,
				COUNT(DISTINCT IF(STAMP < '.TODAYSTAMP.' AND ACCEPTED = 1 AND CANCELLED = 0 AND MOVEDID = 0, lineup.PARTYID, NULL)) AS PAST_OK_CNT
		FROM lineup
		JOIN party USING (PARTYID)
		WHERE ARTISTID = '.$artistid.
		($artist_admin || isset($i_am_this_artist) ? '' : ' AND LINEUP_PSTAMP<'.(CURRENTSTAMP - CURRENTSTAMP % TEN_MINUTES)),
		TEN_MINUTES,
		null,
		partylist_refresh_needed() ? DB_FRESHEN_MEMCACHE : 0))
	) {
		return;
	}
	if (!have_admin('party')) {
		$cnts['TOTAL_CNT'] = $cnts['TOTAL_OK_CNT'];
		$cnts[ 'PAST_CNT'] = $cnts[ 'PAST_OK_CNT'];
	}
	$cnts['FUTURE_CNT'] = $cnts['TOTAL_CNT'] - $cnts['PAST_CNT'];
	// FANS
	require_once '_favourite.inc';
	if (false === ($fancnt = get_fan_count())) {
		return;
	}
	if ($artist_admin
	&&	$artist['ADMININFO']
	) {
		layout_open_box('white');
		?><div class="block"><?
		?><div class="warning"><?= __C('header:admin_comments') ?></div><?=
			make_all_html($artist['ADMININFO'], UBB_UTF8)
		?></div><?
		layout_close_box();
	}
	require_once '_favourite.inc';
	$is_favourite = check_favourite();

	require_once '_star.inc';

	?><article itemscope itemtype="https://schema.org/Person"><?
	?><meta itemprop="url" content="<?= FULL_HOST, get_element_href('artist', $artistid) ?>" /><?

	require_once '_drag.inc';
	open_drag();

	$visi = _visibility($artist,'APPEARANCES');

	layout_open_box($artist['ACCEPTED'] ? 'artist' : 'unaccepted artist');
	layout_open_box_header($is_favourite ? BOX_HEADER_FAN : 0, 'box-header');
	?><h1 itemprop="name"><?= get_element_link('artist', $artistid, $artist['NAME']) ?></h1><?

	show_dead($artist);
	echo get_header_star();

 	if ($artist['ARTIST_CAMCNT']) {
		?><a class="lmrgn<?
		if ($visi !== ROW_FULL) {
			?> light<?
		}
		?>" href="/artist/<?= $artistid ?>/photos"><?= get_camera_icon(element_name('photo', $artist['ARTIST_CAMCNT'])) ?></a><?
	}
 	if ($artist['USER_CAMCNT']) {
 		foreach ($artist['USER_CAMCNT'] as $userid => /* $cnt = */ $ignored) {
			?><a class="lmrgn light" href="/user/<?= $userid ?>/photos"><?=
				get_camera_icon(escape_specials(memcached_nick($userid)))
			?></a><?
		}
 	}

	if ($artist_admin) {
		if ($artist['NEEDUPDATE']) {
			require_once '_needupdates.inc';
			?> <?
			show_new_profile_info('artist', $artistid);
		}
		require_once '_needupdates.inc';
		show_needupdate_mark($artist);
		require_once '_checkup.inc';
		show_checkup_mark($artist);
	}

	if ($fancnt) {
		layout_continue_box_header();
		layout_open_menu(MENU_IN_HEADER);
		layout_open_menuitem();
		[$fanlinkopen, $fanlinkclose] = get_action_open_close('fans',null,escape_utf8($artist['NAME']).' '.Eelement_name('fan', $fancnt));
		echo $fanlinkopen, $fancnt,' ',element_name('fan', $fancnt), $fanlinkclose;
		layout_close_menuitem();
		layout_close_menu();
	}

	layout_close_box_header();

	require_once '_presence.inc';
	show_presence_search($artist);

	require_once '_appic.inc';
	show_appic_link();

	show_noindex_if_needed($artist);

	if ($artist['GENERIC']) {
		?><div class="activities block"><?= __('artist:generic') ?></div><?
	}

	require_once '_uploadimage.inc';
	$img = uploadimage_get('artist', $artistid);

	$haveimg = have_uploadimage($img);
	if (!$haveimg
	&&	$artist['ARTIST_CAMCNT']
	) {
		require_once '_visibility.inc';
		$image = memcached_single_assoc(
			['artist_appearance','image','party','gallery'],'
			SELECT	IMGID,
				image.PARTYID,image.GALLERYID,WIDTH,HEIGHT,image.CSTAMP,
				IF(party.NAME!="",party.NAME,gallery.TITLE) AS NAME,STAMP
			FROM artist_appearance
			JOIN artist USING (ARTISTID)
			LEFT JOIN image USING (IMGID)
			LEFT JOIN party USING (PARTYID)
			LEFT JOIN gallery ON gallery.GALLERYID=image.GALLERYID
			WHERE ARTISTID = '.$artistid.
			_where_visible('artist','APPEARANCES').'
			  AND image.HIDDEN=0
			ORDER BY RAND()
			LIMIT 1'
		);
		if ($image) {
			$highres = is_high_res();
			/** @noinspection PhpRedundantOptionalArgumentInspection */
			extract($image, \EXTR_OVERWRITE);
			$aspect = $WIDTH / $HEIGHT;
			$long_edge = 500;
			if ($aspect >= 1) {
				$w = $long_edge;
				$h = round($w / $aspect);
				if ($highres) {
					$dw = 2 * $h;
					$dh = round($dw / $aspect);
					$size_type = $dw.'x'.$dh;
				} else {
					$size_type = $w.'x'.$h;
				}
			} else {
				$h = $long_edge;
				$w = round($h * $aspect);
				if ($highres) {
					$dh = 2 * $h;
					$dw = round($dh * $aspect);
					$size_type = $dw.'x'.$dh;
				} else {
					$size_type = $w.'x'.$h;
				}
			}


			?><div class="<? if (!SMALL_SCREEN) echo 'right-float ' ?>center<?
			if ($visi !== ROW_FULL) {
				?> light<?
			}
			?>"><?
			?><a href="<?= get_element_href('photo', $IMGID) ?>"><?
			?><img itemprop="image" src="<?= $url = get_photo_url($IMGID, $size_type)
			?>" class="mw90"<?
			?> width="<?= $w ?>"<?
			?> alt="<?= escape_utf8($artist['NAME']) ?>"<?
			?> /></a><?
			?><br /><?
			?><small class="light"><?
			if (!empty($PARTYID)) {
				echo get_element_link('party', $PARTYID, $NAME);
				?> <?= MIDDLE_DOT_ENTITY ?> <?
				_date_display($STAMP);
			} else {
				echo escape_utf8($NAME);
				?> <?= MIDDLE_DOT_ENTITY ?> <?
				_date_display($CSTAMP);
			}
			?></small><?
			?></div><?
			include_og('og:image', $url);
			include_og('og:image:width', $WIDTH);
			include_og('og:image:height', $HEIGHT);
		}
	}
	if ($haveimg
	||	$artist_admin
	) {
		?><div class="<? if (!SMALL_SCREEN) { echo 'right-float '; } ?>center rclr"><?
		uploadimage_show_from_img(
			$img,
			flags:	UPIMG_SCHEMA
			|	UPIMG_LINK_ORIGINAL
			|	UPIMG_SHOW_SEARCH
			| 	UPIMG_SHOW_HISTORY
			|	(SMALL_SCREEN ? UPIMG_MAX_WIDTH : 0)
		);
		?></div><?
	}

	if ($artist_admin
	&&	($undefined = db_single('lineup','SELECT COUNT(*) FROM lineup WHERE TYPE="" AND ARTISTID='.$artistid))
	) {
		?><div class="warning block"><?= __('artist:warning:undefined_performances_LINE',['CNT' => $undefined]) ?></div><?
	}

	if ($artist['TRIBUTE_TO']) {
		?><div class="block"><?= get_element_link('artist', $artist['TRIBUTE_TO']) ?> <?= element_name('tribute') ?></div><?
	}

	$list = new deflist('deflist vtop');

	if ($artist['REALNAME']) {
		$list->set_itemprop('givenName');
		$list->add_row(Eelement_name('name'), get_realnames_list($artist['REALNAME']));
	}
	if ((	$artist_admin
		||	isset($i_am_this_artist)
		)
	&&	$artist['REALNAME_FULL']
	) {
		$list->set_row_class('light');
		$list->add_row(Eelement_name('name'), get_realnames_list($artist['REALNAME_FULL']));
	}

	$is_group = false;

	if ($artist['TYPE']) {
		$list->set_itemprop('jobTitle');
		require_once '_artist_types.inc';
		$typestr = [];


if (NEW_STUFF) {
		if ($function_pasts = memcached_rowuse_array('artist_function', '
			SELECT `FROM`, TILL, TYPE
			FROM artist_function
			WHERE ARTISTID = '.$artistid.'
			ORDER BY TILL')
		) {
			foreach ($function_pasts as &$function_past) {
				$function_past['TYPE'] = explode_to_hash(',', $function_past['TYPE']);
			}
			unset($function_past);

			$function_pasts[] = [
				'FROM'	=> $function_pasts[count($function_pasts) - 1]['TILL'],
				'TILL'	=> 0,
				'TYPE'	=> explode_to_hash(',', $artist['TYPE']),
			];

			print_rr($function_pasts, 'function_pasts');

			$type_stamps = db_same_hash('lineup', '
				SELECT party.STAMP, TYPE
				FROM lineup
				LEFT JOIN party USING (PARTYID)
				WHERE ARTISTID = '.$artistid
			);

			print_rr($type_stamps, 'type_stamps');

			foreach ($type_stamps as $stamp => $types) {
				foreach ($types as $type) {
					foreach ($function_pasts as $function_past) {
						if ($type_stamp['STAMP'] >= $function_past['FROM']
						&&	(	!$type_stamp['STAMP']
							||	$type_stamp['STAMP'] < $function_past['TILL']
							)
						) {
							unset($function_past['TYPE'][$type]);
						}
					}
				}
			}

			print_rr($type_stamps, 'type_stamps');
		}
}

		$type_cnts = get_artist_type_counts($artistid);

		$current_types = explode(',', $artist['TYPE']);

		$types = array_unique([...$current_types, ...array_keys($type_cnts)]);

		$is_group = artist_type_marked_group($artist['TYPE']);

		foreach ($types as $type) {
			if ($type === 'group') {
				$explicit_group = true;
				continue;
			}

if (NEW_STUFF) {
			if ($function_pasts) {
			} else {
				$typestr[$type] =
					($type_name = get_artist_type($type, $artist['GENDER'], $is_group))
				?	(!empty($type_cnts[$type]) ? '<small class="light">'.$type_cnts[$type].' '.MULTIPLICATION_SIGN_ENTITY.' </small>' : '').$type_name
				:	'<span class="error">'.$type.'?</span>';
			}
} else {
#			if (HOME_THOMAS) {
				$type_name = $type ? get_artist_type_neutral($type) : '';
#			} else {
#				$type_name = $type ? get_artist_type($type, $artist['GENDER'], $is_group) : '';
#			}

			ob_start();
			if ($non_active_type = !in_array($type, $current_types, true)) {
				?><span class="light"><?
			}
			if (!empty($type_cnts[$type])) {
				?><small class="light"><?= $type_cnts[$type], MULTIPLICATION_SIGN_ENTITY ?> </small><?
			}
			echo $type_name;
			if ($non_active_type) {
				?></span><?
			}
			$typestr[$type] = ob_get_clean();

			unset($type_cnts[$type]);
}
		}

		if (isset($explicit_group)) {
			$typestr['group'] = get_artist_type('group', $artist['GENDER']);
		}

		require_once '_artist.inc';
		sort_artist_types($typestr, $artistid);

		$list->add_row(Eelement_name('function'), implode(', ', $typestr));
	}

	if ($artist['GENDER']) {
		require_once '_gender.inc';
		show_gender_schema($artist['GENDER']);
		$list->add_row(Eelement_name('gender'), $artist['GENDER'] === 'multi' ? __('gender:mix') : element_name($artist['GENDER']));
	}

	// aliases
	$aliaslist = memcached_simple_hash_if_not_admin('artist', ['artistalias','artist'], '
		SELECT artist.ARTISTID AS ID, artistalias.HIDDEN, NAME
		FROM artistalias
		JOIN artist ON artist.ARTISTID = artistalias.ALSOID
		WHERE artistalias.ARTISTID = '.$artistid.'
		ORDER BY ORDER_NAME'
	);
	if ($aliaslist) {
		$predecessor = memcached_single_if_not_admin('artist','artist','SELECT ARTISTID FROM artist WHERE FOLLOWUPID='.$artistid);
		$size = 0;
		$hiddencnt = 0;
		foreach ($aliaslist as $tmpartistid => $info) {
			[$hidden, $name] = keyval($info);
			if ($hidden && !$artist_admin && !isset($i_am_this_artist)) {
				continue;
			}
			++$size;
			if ($hidden) {
				++$hiddencnt;
			}
			$showthese[$tmpartistid] = $info;
		}
		if ($size) {
			$allhidden = $size === $hiddencnt;
			foreach ($showthese as $tmpartistid => $info) {
				[$hidden, $name] = keyval($info);

				if (!$hidden) {
					$show_partylists[$tmpartistid] = [false, $tmpartistid];
				}

				$str = !$allhidden && $hidden ? '<span class="light">' : '';

				$str .= get_element_link('artist', $tmpartistid, $name, class: 'link');

				if ($tmpartistid === $predecessor) {
					$str .= ' <small>('.element_name('predecessor').')</small>';
				} elseif ($tmpartistid === $artist['FOLLOWUPID']) {
					$str .= ' <small>('.element_name('successor').')</small>';
				}
				if (!$allhidden && $hidden) {
					$str .= '</span>';
				}
				$aliases[] = $str;
			}
			if ($allhidden) {
				$list->set_row_class('light');
			}
			$list->add_row(Eelement_name('alias', $size), implode(', ', $aliases));
		}
	}
	$groups = memcached_read_if_not_admin('artist',
		['artistgroup', 'artist'],'
		SELECT (ACTIVE AND NOT STOPPED), ARTISTID AS ID,NAME
		FROM artistgroup
		JOIN artist USING (ARTISTID)
		WHERE artistgroup.HIDDEN = 0
		  AND MEMBERID='.$artistid.'
		ORDER BY ACTIVE DESC, NAME',
		MULTI_ROW_USE_HASH
	);
	if ($groups) {
		$curr_active = null;
		ob_start();
		foreach ($groups as $active => $info) {
			if ($curr_active !== $active) {
				if ($curr_active !== null) {
					?><br /><?
				}
				if (!$active) {
					?><span class="small light"><?=
						__('date:past(previously)')
					?>:<?
					echo count($info) === 1 ? ' ' : '<br />';
				}
				$curr_active = $active;
			}
			if ($curr_active) {
				foreach ($info as $tmp_artist) {
					$show_partylists[$tmp_artist['ID']] = [true, $tmp_artist['ID']];
				}
			}
			layout_display_list('artist', $info);
			if (!$curr_active) {
				?></span><?
			}
		}
		$list->add_row(
			Eelement_name('is_member_of_group',(isset($groups[0]) ? count($groups[0]) : 0) + (isset($groups[1]) ? count($groups[1]) : 0)),
			ob_get_clean()
		);
	}
	if ( $artist['BIRTH_DAY']
	||	 $artist['BIRTH_MONTH']
	||	 $artist['BIRTH_YEAR']
	&&	!$artist['BIRTH_YEAR_HIDDEN']
	) {
		if (!($hidden = $artist['BIRTH_HIDDEN'])
		||	$artist_admin
		||	isset($i_am_this_artist)
		) {
			if ($hidden) {
				$list->set_row_class('light');
			}
			ob_start();

			if ($BIRTH_DAY) {
				$bdate[] = $BIRTH_DAY;
			}
			if ($BIRTH_MONTH) {
				$bdate[] = _month_name($BIRTH_MONTH);
			}
			if ($BIRTH_YEAR) {
				$bdate[] = $artist['BIRTH_YEAR_HIDDEN'] ? '<span class="light">'.$BIRTH_YEAR.'</span>' : $BIRTH_YEAR;
			}

			if ($showprop = (count($bdate) === 3)) {
				?><time itemprop="birthDate" datetime="<? printf('%d-%02d-%02d', $BIRTH_YEAR, $BIRTH_MONTH, $BIRTH_DAY) ?>"><?
			}
			echo implode(' ', $bdate);
			if ($showprop) {
				?></time><?
			}

			$list->add_row(!empty($is_group) ? __C('field:founded') : Eelement_name('birthdate'), ob_get_clean());

			if ($artist['BIRTH_YEAR']
			&&	(   !$artist['DECEASED']
				||   $artist['DECEASED_YEAR']
				||  !$artist['STOPPED']
				||   $artist['STOPYEAR'])
			) {
				if ($hidden) {
					$list->set_row_class('hidden');
				}
				if (($prefix = 'DECEASED_')	&& $artist['DECEASED']	&& $artist['DECEASED_YEAR']
				||  ($prefix = 'STOP')		&& $artist['STOPPED']	&& $artist['STOPYEAR']
				) {
					$stamp = mktime(	0,
						month:  $artist[$prefix.'MONTH'],
						day:	$artist[$prefix.'DAY'],
						year:   $artist[$prefix.'YEAR']);
				} else {
					$stamp = CURRENTSTAMP;
				}
				$list->add_row(Eelement_name('age'), implode(' &ndash; ', _age_range(
					$artist['BIRTH_YEAR'],
					$artist['BIRTH_MONTH'],
					$artist['BIRTH_DAY'],
					$stamp
				)));
			}
		}
	}
	// STOPSTAMP
	if ($artist['STOPPED']) {
		if ($artist['STOPDAY']) {
			$sdate[] = $artist['STOPDAY'];
		}
		if ($artist['STOPMONTH']) {
			$sdate[] = _month_name($artist['STOPMONTH']);
		}
		if ($artist['STOPYEAR']) {
			$sdate[] = $artist['STOPYEAR'];
		}
		$list->add_row(__C('status:stopped'), isset($sdate) ? implode(' ', $sdate) :  __('answer:yes'));
	}
	if ($DECEASED) {
		require_once '_status.inc';
		ob_start();
		if ($DECEASED_DAY) {
			$ddate[] = $DECEASED_DAY;
		}
		if ($DECEASED_MONTH) {
			$ddate[] = _month_name($DECEASED_MONTH);
		}
		if ($DECEASED_YEAR) {
			$ddate[] = $DECEASED_YEAR;
		}
		if (isset($ddate)) {
			if ($showprop = count($ddate) === 3) {
				?><time itemprop="deathDate" datetime="<? printf('%d-%02d-%02d', $DECEASED_YEAR, $DECEASED_MONTH, $DECEASED_DAY) ?>"><?
			}
			echo implode(' ', $ddate);
			if ($showprop) {
				?></time><?
			}
		} else {
			echo __('answer:yes');
		}
		$list->add_row( __C('status:deceased'), ob_get_clean());
	}
	// groupmembers
	if (false === ($member_list = memcached_rowuse_hash_if_not_admin('artist', ['artistgroup', 'artist'], "
		SELECT artist.ARTISTID AS ID, NAME, REALNAME, REALNAME_FULL, COUNTRYID, IF(ACTIVE, b'1', b'0') AS CURRENT
		FROM artistgroup
		JOIN artist ON artist.ARTISTID = artistgroup.MEMBERID
		WHERE NOT artistgroup.HIDDEN
		  AND artistgroup.ARTISTID = $artistid
		ORDER BY CURRENT DESC, NAME"))
	) {
		return;
	}
	if ($is_users) {
		foreach ($is_users as $userid => $user) {
			if (isset($user['INVISI'])) {
				continue;
			}
			$info = memcached_user_city($userid);
			$member_countries['user'][$userid] = $info ? $info['COUNTRYID'] : 0;
		}
	}
	$one_country = $artist['COUNTRYID'] > 0 ? $artist['COUNTRYID'] : false;

	if ($groupsize = count($member_list)) {
		foreach ($member_list as $local_artist) {
			$tmpcountryid = $local_artist['COUNTRYID'];
			$member_countries['artist'][$local_artist['ID']] = $tmpcountryid;
			if ($local_artist['CURRENT']
			&&	$tmpcountryid > 0
			) {
				$diffcountries[$tmpcountryid] = true;
			}
		}
		if (!$one_country
		&&	(	$one_country
			=	!empty($diffcountries)
			&&	count($diffcountries) === 1
			)
		) {
			[, $one_country] = keyval($member_countries['artist']);
		}
	}

	require_once '_countryflag.inc';
	if (/* $showmembers = */ $groupsize) {
		if ($artist_admin) {
			$use_real_name = $artist['REALNAME_FULL'] ?: $artist['REALNAME'];
		} else {
			$use_real_name = $artist['REALNAME'];
		}
		$names = preg_split('"[,&]"u', $use_real_name);

		$get_clean_real_name = static fn(string $real_name): string =>
			mb_strtolower(
				preg_replace('"\W+"u', '', iconv('UTF-8', 'ASCII//TRANSLIT', $real_name)) ?? '',
				'utf-8'
			);

		foreach ($names as $real_name) {
			$real_name = utf8_mytrim($real_name);
			$clean_real_name = $get_clean_real_name($real_name);
			$member_names[$clean_real_name] = $real_name;
		}

		$is_user = memcached_simple_hash(['artistmember', 'user_account'], '
			SELECT ID, USERID
			FROM artistmember
			JOIN user_account USING (USERID)
			WHERE ID IN ('.implodekeys(', ', $member_list).")
			ORDER BY STATUS = 'active'"
		) ?: [];
		assert(is_array($is_user) && is_array($member_list)); # Satisfy EA inspection

		$user_count = $artist_count = 0;
		$stripped_name = strip_artist_prefix($artist['NAME']);
		$real_name = null;
		foreach (['artist', 'user'] as $element) {
			if (!isset($member_countries[$element])) {
				continue;
			}
			foreach ($member_countries[$element] as $id => $countryid) {
				$user = $userid = null;
				switch ($element) { # NOSONAR
				case 'artist':
					$userid = $is_user[$id] ?? null;
					$tmp_artist = $member_list[$id];
					$real_name = $tmp_artist['REALNAME'] ?: $tmp_artist['NAME'];

					if (!$userid
					||	(!$user = $is_users[$userid] ?? null)
					) {
						break;
					}
					break;

				case 'user':
					if (!$user) {
						$user = $is_users[$id];
					}
					if (!($real_name = $member_names[$user['USER_NAME']] ?? $member_names[$user['USER_REALNAME']] ?? null)) {
						# find real names from connect profiles
						if ($other_artists = memcached_rowuse_hash(['artistmember', 'artist'], "
							SELECT	am.ID,
									artist.NAME, artist.REALNAME
							FROM artistmember AS am
							LEFT JOIN artist ON ARTISTID = ID
							WHERE am.USERID = $id")
						) {
							foreach ($other_artists as $other_artist) {
								$real_name = $other_artist['REALNAME'] ?: $other_artist['NAME'];
							}
						}
					}
					break;
				}

				$is_artist = $element === 'artist';

				$clean_real_name = $real_name ? $get_clean_real_name($real_name) : null;

				$have_user = $element === 'user' ? $id : $userid;
				$nick = $have_user ? win1252_to_utf8(get_element_title('user', $have_user)) : null;
				$stripped_nick = $nick ? strip_artist_prefix($nick) : null;
				$members[$clean_real_name][] = $item = [
					$real_name,																		# 0. real name
					$have_user,																		# 1. userid
					$have_artist = $element === 'artist' ? $id : 0,									# 2 .artistid
					!$one_country && $countryid > 0 ? $countryid : 0,								# 3. countryid
					$have_artist ? get_element_title('artist', $have_artist) : null,	# 4. artist name
					$nick && !utf8_strcasecmp($stripped_nick, $stripped_name),			# 5. stripped nick equals stripped artist name
					$have_artist ? $member_list[$id]['CURRENT'] : $is_users[$id]['ACTIVE'],			# 6. active artist or active user
					$is_artist,																		# 7. true if element is artist
					$have_artist && isset($member_list[$id])										# 8. connected using artistmember entry
				];
				if (!$real_name) {
					mail_log("WARNING no realname for groupmember of artist with id https://vip.partyflock.nl/artist/$artistid",
						item: $item
					);
				}
				if ($nick
				&&	(!$is_artist || $userid)
				&&	utf8_strcasecmp($nick, $artist['NAME'])
				#	^^ if identical, then not really member but artistuserprofile
				) {
					++$user_count;
				}
				if ($is_artist) {
					++$artist_count;
				}
				if ($is_artist
				&&	$userid
				) {
					unset($member_countries['user'][$userid]);
				}
			}
		}

		uksort($members,function($a, $b) use ($members) {
			foreach ($members[$a] as $amember) {
				[,,, $aname, /* $anick */, /* $aspecial */, $active, $is_artist, $connected] = $amember;
				if ($is_artist
				&&	$active
				&&	$connected
				) {
					break;
				}
			}
			foreach ($members[$b] as $bmember) {
				[,,, $bname, /* $bnick */, /* $bspecial */, $active, $is_artist, $connected] = $bmember;
				if ($is_artist
				&&	$active
				&&	$connected
				) {
					break;
				}
			}
			return strcasecmp($aname ?? '', $bname ?? '');
		});

		$memberstr = [];

		foreach ($members as $membergroups) {
			foreach ($membergroups as $member) {
				[$real_name, $userid, $tmpartistid, $countryid, $name, $special, $active, $is_artist, $is_connected] = $member;

				if (!$is_connected) {
					continue;
				}

				if ($special) {
					$groupsize = 0;
					if (!$is_artist) {
						continue;
					}
				} else {
					$skips[$userid] = $userid;
				}

				$hide_user =	!isset($is_users[$userid]['VISI'])
							||	$is_users[$userid]['VISI'] !== ROW_FULL;

				if ($hide_user
				&&	!$tmpartistid
				) {
					continue;
				}

				$parts = [];
				if ($tmpartistid) {
					$parts[] = get_element_link('artist', $tmpartistid, $name, itemprop: 'me', class: 'link');
				}
				if ($real_name
				&&	(	!$tmpartistid
					||	$real_name !== get_element_title('artist', $tmpartistid)
						# don't show name if it is identical to artist nick
					)
				) {
					$parts[] = '<span class="groupmemberinfo">'.escape_utf8($real_name).'</span>';
				}
				if (!$one_country
				&&	$countryid > 0
				) {
					$parts[] =
						'<span class="groupmemberinfo">'.
						(	$countrypart =	get_element_link('country', $countryid).' '.
									get_country_flag($countryid, 'light', false, false)
						).'</span>';

					$diffcountries[$countryid] = $countrypart;
				}
				if (!$hide_user && $userid) {
					$user = memcached_user($userid);
					$user_is_active = $user && $user['STATUS'] === 'active';
					if ($artist_admin
					||	$user_is_active
					) {
						$parts[] =
							'<span class="groupmemberinfo'.($user_is_active ? '' : ' unavailable').'">'.
							'<img class="baseline presence" alt="partyflock" src="'.get_favicon().'" /> '.
							get_element_link('user', $userid, class: 'link').
							'</span>';
					}
				}
				if (!$parts) {
					continue;
				}
				ob_start();
				echo  implode(', ', $parts);
				$memberstr[$active][] = ob_get_clean();
			}
		}
		foreach ($memberstr as $active => $strs) {
			if (!$active) {
				$list->set_row_class('light');
			}
			$list->add_row(
				Eelement_name($active ? 'groupmember' : 'former_groupmember',count($strs)),
				'<ul class="no-bottom"><li>'.implode('</li><li>', $strs).'</li></ul>'
			);
		}
	}

	if (empty($diffcountries)
	&&	$one_country
	) {
		$diffcountries[$one_country] = true;
	}
	if (!empty($diffcountries)) {
		require_once '_countryflag.inc';
		foreach ($diffcountries as $tmpcountryid => &$str) {
			if ($str === true) {
				$str = get_element_link('country', $tmpcountryid, class: 'link').' '.get_country_flag($tmpcountryid, 'light', false, false);
			}
		}
		unset($str);
		ob_start();
		ksort($diffcountries);
		if (false === ($shorts = memcached_simple_hash('country','
			SELECT COUNTRYID,SHORT
			FROM country
			WHERE COUNTRYID IN ('.implodekeys(',', $diffcountries).')'))
		) {
			return;
		}
		foreach ($diffcountries as $countryid => $str) {
			?><span itemprop="nationality" itemscope itemtype="https://schema.org/Country"><?
			?><span itemprop="name"><?= $str ?></span><?
			?><meta itemprop="alternateName" content="<?= $shorts[$countryid] ?>" /><?
			?></span><?

			?></span> <?
		}
		$list->add_row(Eelement_name('origin'),ob_get_clean());
	}

	if (($tmpcountryid = $artist['LIVE_COUNTRYID'])
	&&	$one_country !== $tmpcountryid
	) {
		$list->add_row(
			Eelement_name('country_of_residence'),
			get_element_link('country', $tmpcountryid, class: 'link').' '.get_country_flag($tmpcountryid, 'light', false, false)
		);
	}

	// styles
	require_once '_genrelist.inc';
	$styles = _genrelist_get_item_styles('artist', $artistid);
	if ($styles) {
		$list->add_row(Eelement_plural_name('genre'), $styles);
	}
	if ($artist['IDENT']) {
		# show identification to everyone for now
		$list->set_row_class('light');
		$list->add_row(Eelement_name('identification'), escape_utf8($artist['IDENT']));
	}
	require_once '_site.inc';
	show_site_and_email_rows($list, $artist);

	$have_presence = show_presence_row($list);

	if (have_admin('party')) {
		require_once '_facebook.inc';
		add_facebook_rows($list);
	}
	if (have_admin('video')) {
		require_once '_youtube.inc';
		add_youtube_rows($list);
	}

	// get labels
	$affiliations = memcached_multirow_hash(
		['organization','artistpool'],'
		SELECT	IF(organization.DEADSTAMP = 0 AND ACTIVE = 1, 1, 0) AS ACT,
			CONCAT(ID, ":", IF(ISNULL(ACTIVITIES), "", ACTIVITIES))
		FROM artistpool
		JOIN organization ON ELEMENT="organization" AND ORGANIZATIONID = ID
		WHERE ARTISTID = '.$artistid.'
		ORDER BY ACT DESC'
	);
	foreach ([1] as $active) {
		if (!isset($affiliations[$active])) {
			continue;
		}
		$cnt = 0;
		$items = [];
		foreach ($affiliations[$active] as $info) {
			[$itemid/*, $activities */] = explode(':', $info);
			$items[get_element_title('organization', $itemid)] = get_element_link('organization', $itemid, class: 'link');
			++$cnt;
/*			if ($activities
			&&	!str_contains($activities, 'label')
			) {
				++$visicnt;
			}*/
		}
		ksort($items);
		# $show = $visicnt === $cnt;
		$show = true;
		ob_start();
		if (!$show) {
			?><div class="light ptr" onclick="swapdisplay(this,this.nextSibling)"><?= $cnt ?></div><?
			?><div class="hidden"><?
		}
		echo implode('<br />', $items);
		if (!$show) {
			?></div><?
		}
		$list->add_row(
			$active ? Eelement_name('affiliation', $cnt) : Eelement_name('affiliation_history'),
			ob_get_clean()
		);
	}

	if (isset($visilist)
	&&	!$groupsize
	) {
		$allinvisi = ($visisize === $invisisize);

		if (isset($skips)) {
			$visilist = array_diff_key($visilist, $skips);
		}

		$last_useds = db_simple_hash('user_data', 'SELECT USERID, LAST_USED FROM user_data WHERE USERID IN ('.implodekeys(', ',$visilist).')');

		uasort($visilist, static function(array $a, array $b) use ($last_useds): int {
			return	(($a['STATUS'] === 'active' ? 0 : 1) <=> ($b['STATUS'] === 'active' ? 0 : 1))
				?:	(($last_useds[$b['USERID']] ?? 0)	 <=> ($last_useds[$a['USERID']] ?? 0));
		});

		if ($allinvisi) {
			# admin: order by last used, show date
			# normal: show only most recent
			$list->set_row_class('light');

		}
#		if (HOME_THOMAS) {
#			print_rr($invisisize,'invisisize');
#			print_rr($visisize,'visisize');
#			print_rr($visilist,'visilist');
#		}
		$prevactive = null;
		$have_block = false;
		ob_start();
		foreach ($visilist as $userid => $user) {
			$classes = ['link'];
			$current_active = ($user['STATUS'] === 'active');

			if (!$user['ACTIVE']) {
				$classes[] = 'unavailable';
			}
			if ($prevactive === true
			&&	!$current_active
			) {
				?><div class="hidden" id="inactiveusers"><?
				$have_block = true;
			}
			$prevactive = $current_active;
			?><div<?
			if (!$current_active) {
				?> class="light"<?
			}
			?>><?
			?><a<?
			if ($classes) {
				?> class="<?= implode(' ', $classes) ?>"<?
			}
			?> rel="me" href="<?= get_element_href('user', $userid, $user['NAME']) ?>"><?
			?><img class="presence" alt="partyflock" src="<?= get_favicon() ?>" /><?
			?> <?= escape_specials($user['NAME'])
			?></a><?
			if (!$current_active
			&&	isset($last_useds[$userid])
			) {
				?> <small>(<? _date_display($last_useds[$userid], short: true) ?>)</small><?
			}
			?></div><?
		}
		if ($have_block) {
			?></div><?
		}
		$data = ob_get_clean();
		if ($data) {
			if ($have_block) {
				$data = '<div class="r light unhideanchor" onclick="swapdisplay(\'inactiveusers\')">+</div>'.$data;
			}
			$list->add_row(Eelement_name('user', $visisize), $data);
		}
	}

	$list->display();

	require_once '_itemnames.inc';
	show_item_renames('artist', $artistid);

	require_once '_alternate_name.inc';
	show_alternate_names('artist', $artistid, $artist['NAME']);

	if ($artist_admin) {
		require_once '_employees.inc';
		show_employees();
	}

	show_same_facebook_connected();

	# show BIOGRAPHY
	require_once '_bio.inc';
	$have_bio = show_bio();

	// show INTERVIEWS
	$total_interviews = get_artist_interview_count($artistid);
	$interview_cnt = 3;
	$interviews = get_artist_interviews($artistid, $interview_cnt);
	if ($interviews) {
		?><div class="block"><b><?= Eelement_name('interview', $interview_cnt) ?></b><?
		foreach ($interviews as $interview) {
			?><div class="body block"><i><?
			?><a class="extra" href="<?= get_element_href('interview', $interview['INTERVIEWID'], $interview['TITLE'])
			?>"><?= flat_with_entities($interview['TITLE'], UBB_UTF8)
			?></a></i><?
			?> <small class="light"><?= MIDDLE_DOT_ENTITY ?> <?
			_date_display($interview['PSTAMP']);
			?></small><br /><?=
				make_all_html($interview['TEASER'], UBB_UTF8)
			?></div><?
		}
		?></div><?
		if ($total_interviews !== $interview_cnt) {
			?><div class="body block"><i><small><a href="/artist/<?= $artistid ?>/interviews#interviews"><?=
				__('interviews:show_more',['CNT' => $total_interviews])
			?></a></small></i></div><?
		}
	}
	// show REVIEWS
	require_once '_articles.inc';
	$total_reviews = show_articles('review','artist', $artistid, 3);

	require_once '_news.inc';
	[$total_news/* , $recent_news */] = get_news_counts();
	// show recent NEWS
	$newss = memcached_rowuse_array(['news','connect'],'
		SELECT ASSOCID, PSTAMP, TITLE, TEASER
		FROM connect
		JOIN news ON NEWSID = ASSOCID
		WHERE MAINTYPE = "artist"
		  AND MAINID = '.$artistid.'
		  AND ASSOCTYPE = "news"
		  AND TYPE != "compilation"
		  AND PSTAMP > '.(TODAYSTAMP - 2*ONE_YEAR).'
		ORDER BY PSTAMP DESC',
		TEN_MINUTES
	);
	if ($newss) {
		?><div class="block"><b><?= Eelement_name('news', isset($newss[1])) ?></b><br /><?
		foreach ($newss as $news) {
			?><div class="body block"><i><?
			?><div class="title"><a class="extra" href="<?= get_element_href('news', $news['ASSOCID'], $news['TITLE'])
			?>"><?= flat_with_entities($news['TITLE'], UBB_UTF8)
			?></a></i><small class="light"> <?= MIDDLE_DOT_ENTITY ?> <?
			_date_display($news['PSTAMP']);
			?></small></div><?=
				make_all_html($news['TEASER'], UBB_UTF8)
			?></div><?
		}
		?></div><?
	}

	$columns = memcached_rowuse_array(['column','connect'],'
		SELECT ASSOCID, PSTAMP, TITLE, TEASER
		FROM connect
		JOIN `column` ON COLUMNID = ASSOCID
		WHERE MAINTYPE = "artist"
		  AND ASSOCTYPE = "column"
		  AND MAINID = '.$artistid.'
		ORDER BY `column`.PSTAMP DESC',
		TEN_MINUTES
	);
	if ($columns) {
		?><div class="block"><b><?= Eelement_name('column', isset($columns[1])) ?></b><br /><?
		foreach ($columns as $column) {
			?><div class="body block"><i><?
			?><a class="extra" href="<?= get_element_href('column', $column['ASSOCID'], $column['TITLE'])
			?>"><?= escape_utf8($column['TITLE'], UBB_UTF8)
			?></a></i><small class="light"> <?= MIDDLE_DOT_ENTITY ?> <?
			_date_display($column['PSTAMP']);
			?></small><br /><?=
				make_all_html($column['TEASER'], UBB_UTF8)
			?></div><?
		}
		?></div><?
	}

	if (false === ($musicinfo = memcached_single_assoc(['music', 'musicproducer'],'
		SELECT	COUNT(IF(ACTIVE=1 AND ACCEPTED=1 AND RIGHTSFREE=1,1,NULL)) AS ACTIVE_TOTAL,
			COUNT(*) AS TOTAL
		FROM music
		JOIN musicproducer USING (MUSICID)
		WHERE TYPE="artist"
		  AND musicproducer.ID='.$artistid,
		ONE_HOUR))
	) {
		return;
	}
	$usecnt =
		$musicinfo
	?	(	have_admin('music')
		||	$GLOBALS['currentuser']->is_artist($artistid)
		?	$musicinfo['TOTAL']
		:	$musicinfo['ACTIVE_TOTAL'])
		:	0;

	if ($musicinfo['ACTIVE_TOTAL']) {
		require_once '_music.inc';
		show_playlist_button('/playlist/'.$artistid.':artist');
	}

	layout_display_alteration_note($artist, true);
	layout_close_box();

	close_drag();

	require_once '_vote.inc';
	if (may_speak($artist)) {
		vote_display_choices();
	}
	require_once '_ratinglist.inc';
	$ratinglist = new _ratinglist;
	$ratinglist->item($artist);
	$ratinglist->show_form();

	$total_cnt  = $cnts[$artist_admin ? 'TOTAL_CNT' : 'TOTAL_OK_CNT'];
	 $past_cnt  = $cnts[$artist_admin ?  'PAST_CNT' :  'PAST_OK_CNT'];
	$future_cnt = $total_cnt - $past_cnt;

	layout_open_box('artist agenda', 'agenda');
	layout_open_box_header();
	?><h2><?= Eelement_name(!have_user() ? 'party_agenda' : 'agenda') ?> <?= escape_utf8($artist['NAME']) ?></h2><?

	$parts = [];
	if (!ROBOT) {
		$parts[] = get_ical_feed_link();
		if (SHOW_FEED_ICON) {
			$parts[] = get_feed('agenda',FEED_ICON);
		}
	}
	if ($cnts['PAST_CNT']) {
		[$linkopen, $linkclose] = get_action_open_close('archive',null,element_name('agenda_archive').' '.escape_utf8($artist['NAME']));
		$parts[] = $linkopen.element_name('archive').$linkclose;
	}
	if ($parts) {
		layout_continue_box_header();
		?><nav><?= implode(' '.MIDDLE_DOT_ENTITY.' ', $parts) ?></nav><?
	}
	layout_close_box_header();

	if ($cnts['FUTURE_CNT']) {
		require_once '_partylist.inc';
		$partylist = new _partylist();
		$partylist->show_date = true;
		if ($_REQUEST['ACTION'] === 'single') {
			$partylist->show_vevent = true;
		}
		$partylist->show_people = !SMALL_SCREEN;
		$partylist->show_lineups =
		$partylist->show_stars =
		$partylist->show_camera =
		$partylist->show_buddy_hearts =
		$partylist->show_location =
		$partylist->show_city =	true;
		$partylist->select_future(null,true);
		$partylist->only_artist($artistid);
		$partylist->order_chronologically();
		$partylist->hide_separators = true;
		$partylist->show_contests = true;
		if (!$partylist->query()) {
			return;
		}
		$partylist->display();

	} else {
		require_once '_lastparty.inc';
		show_last_party();
	}

	if ($_REQUEST['ACTION'] !== 'archive'
	&&	$cnts['PAST_CNT']
	) {
		[$linkopen, $linkclose] = get_action_open_close('archive',null,element_name('agenda_archive').' '.escape_utf8($artist['NAME']));
		?><div class="light6 block" style="margin-top: 1em;"><i><?=
		$linkopen, __('action:show_archive') ?>, <?= $cnts['PAST_CNT'] ?> <?= element_name('event', $cnts['PAST_CNT']), $linkclose
		?></i></div><?
	}

	foreach ($show_partylists as $info) {
		[$group, $id] = $info;
		require_once '_partylist.inc';
		$partylist = new _partylist;

		$partylist->show_date = true;
		$partylist->show_vevent = false;
		$partylist->show_people = !SMALL_SCREEN;
		$partylist->show_lineups =
		$partylist->show_stars =
		$partylist->show_camera =
		$partylist->show_buddy_hearts =
		$partylist->show_location =
		$partylist->show_city = true;

		$partylist->select_future();
		$partylist->only_artist($id);
		$partylist->order_chronologically();
		$partylist->hide_separators = true;

		if (!$partylist->query()) {
			continue;
		}

		ob_start();
		$partylist->display();
		if ($data = ob_get_clean()) {
			?><br /><?
			layout_open_box_header();
			echo Eelement_name(!have_user() ? 'party_agenda' : 'agenda');
			?> <?= get_element_link('artist', $id, class: 'link')
			?> (<?= element_name($group ? 'group' : 'alias') ?>)<?
			layout_close_box_header();
			echo $data;
		}
	}

	layout_close_box();
	layout_open_box('artist');

	// STATISTICS
	layout_box_header(Eelement_plural_name('statistic'));
	layout_open_table('fw vtop default', max_cols: SMALL_SCREEN ? 1 : 3);

	layout_start_reverse_row(!$total_cnt ? ROW_LIGHT : 0);
		$linkopen = $total_cnt ? (!$future_cnt ? get_action_open_close('archive')[0] : '<a href="#agenda">') : null;
		$linkclose = $linkopen ? '</a>' : null;
		// TOTAL
		echo $total_cnt ? $linkopen.$total_cnt.$linkclose : __('counter:none');
		layout_value_field();
		echo $linkopen,element_name('performance', $total_cnt), $linkclose;

	layout_restart_reverse_row(!$future_cnt ? ROW_LIGHT : 0);
		// FUTURE
		$linkopen = $future_cnt ? '<a href="#agenda">' : null;
		$linkclose = $linkopen ? '</a>' : null;
		echo $future_cnt ? $linkopen.$future_cnt.$linkclose : __('counter:none');
		layout_value_field();
		echo $linkopen.__('when:in_the_future').$linkclose;

	if ($past_cnt) {
		// ARCHIVE
		layout_restart_reverse_row();
		[$linkopen, $linkclose] = get_action_open_close('archive');
		echo $linkopen, $past_cnt, $linkclose;
		layout_value_field();
		echo $linkopen,__('when:in_the_past'), $linkclose;
	}

	show_views_restart_row();

	if ($usecnt) {
		layout_restart_reverse_row();
		$linkopen = '<a href="/music/artist/'.$artistid.'">';
		$linkclose = '</a>';
		echo $linkopen, $usecnt, $linkclose;
		layout_value_field();
		echo $linkopen,element_name('musicproduction', $usecnt), $linkclose;
	}
	// INTERVIEWS
	if ($interviews) {
		layout_restart_reverse_row();
		[$linkopen, $linkclose] = get_action_open_close('interviews');
		echo $linkopen, $total_interviews, $linkclose;
		layout_value_field();
		echo $linkopen,element_name('interview', $interview_cnt), $linkclose;
	}
	if ($fancnt) {
		// FANS
		layout_restart_reverse_row();
		echo $fanlinkopen, $fancnt, $fanlinkclose;
		layout_value_field(get_partyflock_icon());
		echo $fanlinkopen,element_name('fan', $fancnt), $fanlinkclose;
	}
	// INTERVIEWS
	if ($interviews) {
		layout_restart_reverse_row();
		[$linkopen, $linkclose] = get_action_open_close('interviews');
		echo $linkopen, $total_interviews, $linkclose;
		layout_value_field();
		echo $linkopen,element_name('interview', $interview_cnt), $linkclose;
	}
	// REVIEWS
	if ($total_reviews) {
		layout_restart_reverse_row();
		[$linkopen, $linkclose] = get_action_open_close('reviews');
		echo $linkopen, $total_reviews, $linkclose;
		layout_value_field();
		echo $linkopen,element_name('review', $total_reviews), $linkclose;
	}
	// NEWS
	if ($total_news) {
		layout_restart_reverse_row();
		[$linkopen, $linkclose] = get_action_open_close('news');
		echo $linkopen, $total_news, $linkclose;
		layout_value_field();
		echo $linkopen,element_name('news', $total_news), $linkclose;
	}

	require_once '_fblikes.inc';
	show_fb_likes();

	if ($artist['ARTIST_CAMCNT']) {
		layout_restart_reverse_row();
		$linkopen = '<a href="/artist/'.$artistid.'/photos">';
		echo $linkopen, $artist['ARTIST_CAMCNT']; ?></a><?
		layout_value_field();
		echo $linkopen,element_name('photo_while_performing', $artist['ARTIST_CAMCNT']);
		?></a><?
	}
	if ($artist['USER_CAMCNT']) {
		require_once '_externalsettings.inc';
		$size = count($artist['USER_CAMCNT']);
		$offense_admin = have_offense_admin();
		foreach ($artist['USER_CAMCNT'] as $userid => $cnt) {
			if (!($esettings = external_settings_list($userid))
			||	!($show_stats = _visibility($esettings,'STATISTICS', $offense_admin))
			) {
				continue;
			}
			layout_restart_reverse_row($is_users[$userid]['VISI'] !== ROW_FULL || $show_stats !== ROW_FULL ? ROW_LIGHT : 0);
			$linkopen = '<a href="/user/'.$userid.'/photos">';
			echo $linkopen, $cnt; ?></a><?
			layout_value_field();
			echo $linkopen,element_name('photo_as_user', $cnt),': ',escape_specials(memcached_nick($userid));
			?></a><?
		}
	}


	$videos_visible = _visibility($artist, 'VIDEOS');

	if ($videos_visible) {
		require_once '_videototal.inc';
		show_video_statistics_row();
	}

	require_once '_commentlist.inc';
	_commentlist::show_stats_row();

	require_once '_ratinglist.inc';
	_ratinglist::show_stats_row();

	vote_show_row();

	layout_stop_row();
	layout_close_table();
	layout_close_box();

	if ($_REQUEST['ACTION']) {
		switch ($_REQUEST['ACTION']) {
		// INTERVIEWS
		case 'interviews':
			if ($total_interviews) {
				show_artist_interviews(
					$artistid,
					$total_interviews === $interview_cnt ? $interviews : false
				);
			} else {
				http_response_code(404);
			}
			break;
		// REVIEWS
		case 'reviews':
			if ($total_reviews) {
				show_artist_reviews($artistid);
			} else {
				http_response_code(404);
			}
			break;
		case 'news':
			if ($total_news) {
				require_once '_news.inc';
				show_news_teasers();
			} else {
				http_response_code(404);
			}
			break;
		// ARCHIVE
		case 'archive':
			if ($past_cnt) {
				show_artist_archive($artist);
			} else {
				http_response_code(404);
			}
			break;
		// FANS
		case 'fans':
			if ($fancnt) {
				require_once '_fans.inc';
				show_fans('artist', $artistid, $artist);
			} else {
				http_response_code(404);
			}
			break;
		// VOTES
		case 'votes':
			vote_show_box();
			break;
		default:
			$showcmts = true;
			break;
		}
	} else {
		$showcmts = true;
	}

	if (isset($showcmts)) {
		require_once '_showcase.inc';
		show_presence_showcase(hide_videos: !$videos_visible);

		require_once '_videosandphotos.inc';
		show_videos_and_photos('artist', $artistid, hide_videos: !$videos_visible);

		$ratinglist = new _ratinglist;
		$ratinglist->display();

		$cmts = new _commentlist;
		$cmts->item($artist);
		$cmts->display();
	}
	?></article><?

	counthit('artist', $artistid);

	if (!available_meta()) {
		$presences = have_presence();
		include_meta('description', __('metad:artist:single', DO_UBBFLAT, [
			'ARTISTID'	=> $artistid,
			'BIO'		=> $have_bio,
			'MUSIC'		=> isset($presences['spotify']) || isset($presences['soundcloud']),
			'VIDEOS'	=> isset($presences['youtube']),
			'SOCIALS'	=> $have_presence,
			'GENDER'	=> $is_group ? 'group' : $artist['GENDER'],
		]));
	}
}

function artist_display_form(): void {
	require_once '_presence.inc';

	layout_show_section_header(null, __(!empty($_REQUEST['sID']) ? 'action:change' : 'action:add'));

	$artist_admin = have_admin('artist');
	if ($artistid = $_REQUEST['sID']) {
		if (!($artist = db_single_assoc('artist','SELECT * FROM artist WHERE ARTISTID='.$_REQUEST['sID']))) {
			if ($artist === null) {
				not_found();
			}
			return;
		}
		if (!$artist_admin
		&&	(	$artist['ACCEPTED']
			?	!require_admin('artist')
			:	!require_self($artist['MUSERID'])
			)
		) {
			return;
		}
		if (false === ($artist_genres = db_boolean_hash('artist_genre', 'SELECT GID FROM artist_genre WHERE ARTISTID = '.$artist['ARTISTID']))) {
			return;
		}
	} elseif (!require_user()) {
		return;
	} else {
		$artist = [];
	}
	assert(is_array($artist)); # Satisfy EA inspection

	if (!$artist_admin) {
		layout_open_box('white');
		?><div class="block"><span class="warning"><?= __C('warningheader') ?></span><br /><?
		echo __('artist:info:addition_information_for_nonadmin_TEXT');
		layout_close_box();
	}
	include_js('js/form/artist');

	$generic = !empty($artist['GENERIC']);

	?><form<?
	if ($generic) {
		?> class="generic"<?
	}
	?> onsubmit="return submitForm(this)"<?
	?> enctype="multipart/form-data"<?
	?> accept-charset="utf-8"<?
	?> autocomplete="off"<?
	?> method="post"<?
	?> action="/artist<?
	if ($artist) {
		?>/<? echo $artist['ARTISTID'];
	}
	?>/commit"><?
	?><input type="hidden" name="enc" value="&#129392;" /><?

	require_once '_ticket.inc';
	ticket_passthrough();

	require_once '_process_appic_changes.inc';
	passthrough_update_from_appic();

	require_once '_inquirymail.inc';
	show_inquiry_mail_question();

	$spec = explain_table('artist');

	require_once '_uploadimage.inc';
	show_uploadimage_for_element_form('artist', $artistid);

	if ($artist
	&&	$artist_admin
	) {
		require_once '_presence.inc';
		show_presence_search($artist);
	}

	layout_open_box('artist');
	layout_open_table('vtop fw');

	layout_start_row();

	if ($artist_admin) {
		require_once '_needupdates.inc';
		show_needupdate_form_part($artist);

		layout_next_cell();
		?><label class="cbi <?= !$generic ? 'not-' : '' ?>hilited"><?
		show_input([
			'type'		=> 'checkbox',
			'class'		=> 'upLite',
			'name'		=> 'GENERIC',
			'value'		=> 1,
			'checked'	=> $generic,
			'onclick'	=> /** @lang JavaScript */ 'Pf_clickGeneric(this);',
		]);
		?> <?= __('artist:generic')
		?></label><?
		?></div><?
		layout_restart_row();
	}
		echo Eelement_name('artist_name');
		layout_field_value();
		show_input([
			'type'			=> 'text',
			'name'			=> 'NAME',
			'id'			=> 'name',
			'required'		=> true,
			'autofocus'		=> true,
			'value_utf8'	=> $artist,
			'spec'			=> $spec,
			'autocomplete'	=> HOME_THOMAS ? 'name' : null,
		]);

	if (!$artist_admin) {
		?><br /><label><?
		show_input([
			'type'		=> 'checkbox',
			'name'		=> 'ITS_ME',
			'checked'	=> $artistid && db_single('artistmember', 'SELECT 1 FROM artistmember WHERE ID = '.$artistid.' AND ACTIVE = 1 LIMIT 1'),
			'value'		=> 1
		]);
		?> <?= __('artist:info:im_this_artist') ?></label><?
	} else {
		require_once '_alternate_name.inc';
		show_alternate_name_form('artist', $artistid);
	}

	layout_restart_row(rowuserdef: 'nongeneric');
		echo Eelement_name('name');
		layout_field_value();
		show_input([
			'type'			=> 'text',
			'name'			=> 'REALNAME',
			'value_utf8'	=> $artist,
			'spec'			=> $spec,
			'autocomplete'	=> HOME_THOMAS ? 'name' : null,
		]);

	$is_group = $artist && artist_type_is_group($artist['TYPE']);

	if ($artist_admin) {
		layout_restart_row(id: 'past-members-row', rowuserdef: 'nongeneric'.($is_group ? '' : ' hidden'));
			echo Eelement_plural_name('past_member');
			layout_field_value();
			show_input([
				'type'			=> 'text',
				'name'			=> 'PAST_MEMBERS',
				'value_utf8'	=> $artist,
				'spec'			=> $spec,
				'placeholder'	=> Eelement_name('past_member').' #1, '.
								   Eelement_name('past_member').' #2 (?-2021), '.
								   Eelement_name('past_member').' #3 (2015-2022)'
			]);
	}

	layout_restart_row(rowuserdef: 'nongeneric');
		echo Eelement_name('name') ?> (<?= __('attrib:internal') ?>)<?
		layout_field_value();
		show_input([
			'type'			=> 'text',
			'name'			=> 'REALNAME_FULL',
			'value_utf8'	=> $artist,
			'spec'			=> $spec,
			'autocomplete'	=> HOME_THOMAS ? 'name' : null,
		]);


	if (have_admin('artist')) {
		layout_restart_row();
		echo Eelement_name('identication');
		layout_field_value();
		show_input([
			'type'			=> 'text',
			'name'			=> 'IDENT',
			'value_utf8'	=> $artist,
			'spec'			=> $spec,
		]);
	} else {
		show_input([
			'type'			=> 'hidden',
			'name'			=> 'IDENT',
			'value_utf8'	=> $artist,
			'spec'			=> $spec,
		]);
	}

	layout_restart_row(rowuserdef: 'nongeneric');
		echo Eelement_name('site');
		layout_field_value();
		show_input([
			'id'			=> 'site',
			'type'			=> 'url',
			'data-valid'	=> 'url',
			'name'			=> 'SITE',
			'value_utf8'	=>  $artist,
			'spec'			=> $spec,
		]);

	layout_restart_row(rowuserdef: 'nongeneric');
		echo Eelement_name('email');
		layout_field_value();
		show_input([
			'type'			=> 'email',
			'data-valid'	=> 'working-email',
			'name'			=> 'EMAIL',
			'value_utf8'	=> $artist,
			'autocomplete'	=> HOME_THOMAS ? 'email' : null,
			'spec'			=> $spec,
		]);

	assert(is_array($artist)); # Satisfy EA inspection
	layout_restart_row(rowuserdef: 'nongeneric');
		?><label for="book-site"><?= Eelement_name('site'), ' ('.element_plural_name('booking').')' ?></label><?
		layout_field_value();
		show_input([
			'id'			=> 'book-site',
			'type'			=> 'url',
			'data-valid'	=> 'url',
			'name'			=> 'BOOKSITE',
			'value_utf8'	=> $artist['BOOKSITE'] ?? null,
		]);

	if (!show_presence_form_row(class: 'nongeneric')) {
		return;
	}
	layout_restart_row();
		echo Eelement_name('function');
		layout_field_value();
		require_once '_artist_types.inc';

		$selected_types = $artist ? explode_to_hash(',', $artist['TYPE']) : null;

		$types = show_artist_types_checkboxes($selected_types, artist_form: true);

	if ($artist) {
		foreach (ARTIST_TYPES_NOT_OK_FOR_LINEUP as $not_ok => $false) {
			unset($selected_types[$not_ok]);
		}

		$disabled = count($selected_types) > 1;

		if ($show_update_lineups
		=	have_admin('artist')
		&&	db_single('lineup', 'SELECT 1 FROM lineup WHERE TYPE = "" AND ARTISTID = '.$artistid.' LIMIT 1')
		) {
			layout_restart_row($disabled ? ROW_HIDDEN : 0, 'update-lineups');
			echo __C('action:update_lineups');
			layout_field_value();
			?><select<?
			if ($disabled) {
				?> disabled<?
			}
			?> name="UPDATE_LINEUP"><?
			?><option></option><?

			if (!$disabled && !empty($selected_types)) {
				[$selected, $name] = keyval($selected_types);
				?><option value="<?= $selected ?>"><?= $name ?></option><?
			} else {
				?><option></option><?
			}
			?></select><?

			$undefined = db_single('lineup','SELECT COUNT(*) FROM lineup WHERE TYPE="" AND ARTISTID = '.$artistid);
			if ($undefined) {
				?> <span class="warning"><?=
					__('artist:warning:undefined_performances_LINE',['CNT'=>$undefined])
				?></span><?
			}
		}

		if (have_super_admin()) {
			layout_restart_row($disabled ? ROW_HIDDEN : 0, 'update-all-lineups');
			echo __C('action:update_all_lineups');
			layout_field_value();
			?><select<?
			if ($disabled) {
				?> disabled<?
			}
			if ($show_update_lineups) {
				?> onchange="setdisplay('update-lineups', !this.selectedIndex)"<?
			}
			?> name="UPDATE_ALL_LINEUP"><?
			?><option></option><?

			if (!$disabled && !empty($selected_types)) {
				[$selected, $name] = keyval($selected_types);
				?><option value="<?= $selected ?>"><?= $name ?></option><?
			} else {
				?><option></option><?
			}
			?></select><?
		}
	}

	layout_restart_row();
		echo __('field:tribute_to');
		layout_field_value();
		require_once '_fillelementid.inc';
		show_elementid_input('artist', args: [
			'name'     => 'TRIBUTE_TO'
		]);

	layout_restart_row(rowuserdef: 'nongeneric');
		echo Eelement_name('gender');
		layout_field_value();

		foreach ([
			''		=> '?',
			'male'		=> __('gender:male'),
			'female'	=> __('gender:female'),
			'multi'		=> __('gender:mix'),
		] as $gender => $desc) {
			$checked =
				!$artist
			&&	!$gender
			||	$artist
			&&	$artist['GENDER'] === $gender;

			?><div><?
			show_input_with_label(
				checked:	$checked,
				hilited:	'bold-hilited',
				base:		'cbi',
				input:	 [	'type'		=> 'radio',
							'name'		=> 'GENDER',
							'value'		=> $gender,
							'disabled'	=> !$is_group && $gender === 'multi'],
				text:		$desc,
			);
			?></div><?
		}

	layout_restart_row(rowuserdef: 'nongeneric');
		$founded = __C('field:founded');
		$birthdate = Eelement_name('birthdate');
		?><span<?
			?> id="found-name"<?
			?> data-group="<?= $founded ?>"<?
			?> data-non-group="<?= $birthdate ?>"<?
		?>><?= $is_group ? $founded : $birthdate ?></span><?
		layout_field_value();

		display_dateselect_artistform('BIRTH_', $artist, $is_group);

		?><span id="birth_hidden_option"<?

		if ($disallow_hidden
		=	!$artist
		||	(	!$artist['BIRTH_MONTH']
			&&	!$artist['BIRTH_YEAR']
			&&	!$artist['BIRTH_DAY']
			)
		) {
			?> class="hidden"<?
		}
		?>><?

		assert(is_array($artist)); # Satisfy EA inspection
		show_input_with_label(
			checked:	!$disallow_hidden &&!empty($artist['BIRTH_HIDDEN']),
			hilited:	'bold-hilited-red',
			base:		'basehl cbi',
			input:	[	'type'		=> 'checkbox',
						'value'		=> '1',
						'name'		=> 'BIRTH_HIDDEN',
			],
			text:		SERVER_VIP ? __('action:hide_entirely') :  __('action:hide_(dont_show)'),
		);

		if (SERVER_VIP) {
			show_input_with_label(
				checked:	!$disallow_hidden &&!empty($artist['BIRTH_YEAR_HIDDEN']),
				hilited:	'bold-hilited-red',
				base:		'basehl cbi',
				input:	[	'type'		=> 'checkbox',
							'value'		=> '1',
							'name'		=> 'BIRTH_YEAR_HIDDEN',
				],
				text:		__('action:hide_year'),
			);
		}
		?></span><?

	layout_restart_row(rowuserdef: 'nongeneric');
		?><label for="deceased"><?= __C('status:deceased') ?></label><?
		layout_field_value();
		show_input_with_label(
			checked:	!empty($artist['DECEASED']),
			hilited:	'bold-hilited',
			base:		'basehl cbi',
			input:		[	'type'		=> 'checkbox',
							'class'		=> 'upLite',
							'value'		=> '1',
							'name'		=> 'DECEASED',
							'id'		=> 'deceased',
							'onclick'	=> "setdisplay('deceaseddate',this.checked)",
			]
		);
		?></labe><?

	layout_restart_row(
		flags:		!empty($artist['DECEASED']) ? 0 : ROW_HIDDEN,
		id:		'deceaseddate',
		rowuserdef: 'nongenric'
	);
		echo Eelement_name('date_of_death');
		layout_field_value();
		display_dateselect_artistform('DECEASED_', $artist);

	layout_restart_row(rowuserdef: 'nongeneric');
		?><label for="stopped"><?= __C('status:stopped') ?></label><?
		layout_field_value();
		show_input_with_label(
			checked:	$stopped = !empty($artist['STOPPED']),
			hilited:	'bold-hilited',
			base:		'basehl cbi',
			input:		[	'id'		=> 'stopped',
							'onclick'	=> "setdisplay('stopdate', this.checked);",
							'type'		=> 'checkbox',
							'value'		=> '1',
							'name'		=> 'STOPPED',
			]
		);

	if ($artist
	&&	(	($aliases = db_simple_hash(['artistalias', 'artist'], "
			SELECT artist.ARTISTID, NAME
			FROM artistalias
			JOIN artist ON ALSOID = artist.ARTISTID
			WHERE artistalias.ARTISTID = $artistid
			ORDER BY NAME"))
		||	$artist['FOLLOWUPID']
		)
	) {
		layout_restart_row(rowuserdef: 'nongeneric');
		?><label for="followup-id"><?= Eelement_name('successor') ?></label><?
		layout_field_value();
		?><select id="followup-id" name="FOLLOWUPID"><?
			?><option value="0"></option><?
			foreach ($aliases as $tmpartistid => $alias) {
				?><option<?
				if ($artist
				&&	$tmpartistid === $artist['FOLLOWUPID']
				) {
					?> selected<?
				}
				?> value="<?= $tmpartistid ?>"><?= escape_utf8($alias) ?></option><?
			}
		?></select><?
	}
	assert(is_array($artist)); # Satisfy EA inspection
	layout_restart_row($stopped ? 0 : ROW_HIDDEN, id: 'stopdate', rowuserdef: 'nongeneric');
		echo Eelement_name('stop_date');
		layout_field_value();
		global $__year;
#		if (!empty($artist['STOPDAY'])
#		||	!empty($artist['STOPMONTH'])
#		||	!empty($artist['STOPYEAR'])
#		) {
			$start_year = (!empty($artist['STOPYEAR']) ? $artist['STOPYEAR'] : $__year) - 20;
			$end_year   = $start_year + 20 > $__year ? $__year + 1 : $start_year + 20;
			_date_display_select(
				prefix:			'STOP',
				select_year:	$artist['STOPYEAR']  ?? 0,
				select_month:	$artist['STOPMONTH'] ?? 0,
				select_day:		$artist['STOPDAY']   ?? 0,
				start_year:		$start_year,
				end_year:		$end_year,
				empty_value:	0
			);
#		} else {
#			display_dateselect_include_empty(0,'STOP', $__year-20, $__year + 1);
#		}

	# COUNTRYID
	require_once '_countrylist.inc';
	assert(is_array($artist)); # Satisfy EA inspection
	layout_restart_row(rowuserdef: 'nongeneric');
		echo Eelement_name('origin');
		layout_field_value();
		?><select name="COUNTRYID"> <?
		?><option value="0"></option><?
		?><option<?
		if ($artist
		&&	$artist['COUNTRYID'] === -1
		) {
			?> selected<?
		}
		if (!$artist
		||	!artist_type_is_group($artist['TYPE'])
		) {
			?> disabled<?
		}
		?> value="-1"><?= __('action:inherit_from_connected_profiles') ?></option><?
		?><optgroup><?
		_countrylist_display_options($artist['COUNTRYID'] ?? 0);
		?></optgroup><?
		?></select><?

	# LIVE_COUNTRYID
	layout_restart_row(rowuserdef: 'nongeneric');
		echo Eelement_name('country_of_residence');
		layout_field_value();
		require_once '_countrylist.inc';
		?><select name="LIVE_COUNTRYID"><?
		?><option value="0"></option><?
		_countrylist_display_options($artist['LIVE_COUNTRYID'] ?? 0);
		?></select><?

	layout_restart_row();
		echo Eelement_plural_name('genre');
		layout_field_value();
		require_once '_genrelist.inc';
		show_genre_selection($artist_genres ?? null);

	if ($artist_admin) {
		assert(is_array($artist)); # Satisfy EA inspection
		layout_restart_row();
			echo str_replace(' ', '<br />', __C('header:admin_comments'));
			layout_field_value();
			?><textarea name="ADMININFO" cols="50" rows="5"><?
			if (!empty($artist['ADMININFO'])) {
				echo escape_utf8($artist['ADMININFO']);
			}
			?></textarea><?

		if (have_admin('hide_stuff')) {
			require_once '_visibility.inc';

			layout_restart_row();
				echo Eelement_name('visibility');
				layout_field_value();
				_visibility_display_form_part($artist, 'APPEARANCES');
				?> <?
				echo element_plural_name('appearance(photo)');

			layout_restart_row();
				layout_field_value();
				_visibility_display_form_part($artist, 'VIDEOS');
				?> <?
				echo element_plural_name('video');

			assert(is_array($artist)); # Satisfy EA inspection
			layout_restart_row();
				?><label for="hide-for-search-engines"><?=
					Eelement_plural_name('search_engine')
				?></label><?
				layout_field_value();
				?><label class="cbi <?= empty($artist['HIDDEN_FOR_SEARCH_ENGINES']) ? 'not-' : null ?>hilited-light-orange"><?
				show_input([
					'type'		=> 'checkbox',
					'class'		=> 'upLite',
					'name'		=> 'HIDDEN_FOR_SEARCH_ENGINES',
					'id'		=> 'hide-for-search-engines',
					'value'		=> 1,
					'checked'	=> $artist['HIDDEN_FOR_SEARCH_ENGINES'] ?? false,
				]);
				?> <?= __('status:hidden_for_search_engines')
				?></label><?
		}
	}
	layout_stop_row();
	layout_close_table();
	layout_close_box();

	?><div class="block"><input type="submit" value="<?= __($artist ? 'action:change' : 'action:add') ?>" /></div></form><?
}

function artist_display_photos(): void {
	if (!($artistid = require_idnumber($_REQUEST, 'sID'))) {
		return;
	}
	if (!($name = memcached_artist($artistid))) {
		if ($name === null) {
			not_found();
		}
		return;
	}

	layout_section_header(__C('elements:photo_of_artist',DO_UBB,array('ARTISTID'=>$artistid)));

	if (false === ($visibility = db_single('artist', 'SELECT VISIBILITY_APPEARANCES FROM artist WHERE ARTISTID='.$artistid))) {
		return;
	}

	require_once '_visibility.inc';
	if (!have_admin('artist')
	&&	!is_visible(0, $visibility)
	) {
		not_found();
		return;
	}

	require_once '_photolist.inc';
	$photolist = new _photolist();
	$photolist->of_artist($artistid);
	$photolist->show_all = true;
	$photolist->show_controls = false;
	$photolist->align_left = true;
	if ($photolist->query()) {
		$photolist->display();
	}
}

function actual_artist_commit(): int|false {
	require_once '_genrelist.inc';
	require_once '_site.inc';
	require_once '_ticket.inc';
	require_once '_presence.inc';
	require_once '_namefix.inc';
	require_once '_elementchanged.inc';
	require_once '_inquirymail.inc';
	require_once '_artist.inc';

	$artist_admin = have_admin('artist');

	if (!require_user()
	||	!require_something_trim($_POST, 'NAME',null,null,utf8:true)
	||	!require_anything_trim($_POST, 'REALNAME', utf8: true)
	||	false === require_number($_POST, 'BIRTH_DAY')
	||	false === require_number($_POST, 'BIRTH_MONTH')
	||	false === require_number($_POST, 'BIRTH_YEAR')
	||	false === require_number($_POST, 'STOPYEAR')
	||	false === require_number($_POST, 'STOPMONTH')
	||	false === require_number($_POST, 'STOPDAY')
	||	false === require_number($_POST, 'DECEASED_YEAR')
	||	false === require_number($_POST, 'DECEASED_MONTH')
	||	false === require_number($_POST, 'DECEASED_DAY')
	||	!require_anything_trim($_POST, 'SITE', utf8: true)
	||	!require_anything_trim($_POST, 'BOOKSITE', utf8: true)
	||	false === optional_idnumber($_POST, 'FOLLOWUPID')
	||	false === require_element($_POST, 'GENDER', ['', 'male', 'female', 'multi'])
	||	!require_anything_trim($_POST, 'EMAIL', utf8: true)
	||	!optional_number($_POST, 'TICKETID')
	||	$artist_admin
	&&	!require_anything_trim($_POST, 'IDENT', utf8: true)
	||	false === ($countryid = require_artist_countryid_or_empty($_POST, 'COUNTRYID', utf8: true))
	||	false === require_number($_POST, 'LIVE_COUNTRYID')
	||	false === optional_number($_POST, 'TRIBUTE_TO')
	) {
		return false;
	}

	if ($artist_admin) {
		if (!require_anything_trim($_POST, 'PAST_MEMBERS', true)) {
			return false;
		}
		$setlist['PAST_MEMBERS'] = "PAST_MEMBERS = '".addslashes($_POST['PAST_MEMBERS'])."'";
	}

	if ($_REQUEST['sID']
	&&	$_POST['EMAIL']
	) {
		require_once '_urlcheck.inc';
		$status = get_url_status('artist', $_REQUEST['sID']);
		if (!url_is_bad($status)) {
			if (!require_working_email($_POST, 'EMAIL')) {
				return false;
			}
		}
	}
	$hide_stuff_admin = $artist_admin && have_admin('hide_stuff');

	if ($artist_admin) {
		if (!require_anything_trim($_POST, 'REALNAME_FULL', utf8: true)) {
			return false;
		}
		foreach (['REALNAME', 'REALNAME_FULL'] as $key) {
			$_POST[$key] = preg_replace('"\h+(?:en|und|and|et)\h+"ui', ' & ', $_POST[$key]);
			# trim exclamation mark
			$_POST[$key] = utf8_mytrim($_POST[$key], '!');
			# strip too many spaces
			$_POST[$key] = preg_replace('"\s*,\s*"u', ', ', $_POST[$key]);
		}
		$setlist['REALNAME_FULL'] = 'REALNAME_FULL = "'.addslashes($_POST['REALNAME'] === $_POST['REALNAME_FULL'] ? '' : get_fixed_utf8_name($_POST['REALNAME_FULL'])).'"';

		require_once '_ubb_preprocess.inc';
		if (!require_anything_trim($_POST, 'ADMININFO', utf8: true)) {
			return false;
		}
		$setlist['ADMININFO'] = 'ADMININFO = "'.addslashes(_ubb_preprocess($_POST['ADMININFO'], utf8: true)).'"';
	}

	$_POST['NAME'] = get_fixed_name($_POST['NAME'], true);

	// strip 'DJ ' if exists
	if (!$artist_admin) {
		if (0 === utf8_strncasecmp($_POST['NAME'],'DJ ', 3)) {
			$_POST['NAME'] = mb_substr($_POST['NAME'], 3);
		}
	}
	if (!require_something_trim($_POST, 'NAME',null,null,utf8:true)) {
		return false;
	}
	$_POST['REALNAME'] = get_fixed_utf8_name($_POST['REALNAME']);
	$_POST['REALNAME'] = str_replace([' en ',' and '],[' & ',' & '], $_POST['REALNAME']);

	$artistid = $_REQUEST['sID'];
	// check of artist reeds bestaat
	// only check if the name has changed.
	$existing_artistid =
		$artistid
	?	0
	:	db_single('artist','
		SELECT ARTISTID
		FROM artist
		WHERE COUNTRYID		=' .($countryid ?: 0).'
		  AND DECEASED		= '.(isset($_POST['DECEASED']) ? "b'1'" : "b'0'").'
		  AND BIRTH_DAY		= '.$_POST['BIRTH_DAY'].'
		  AND BIRTH_MONTH	= '.$_POST['BIRTH_MONTH'].'
		  AND BIRTH_YEAR	= '.$_POST['BIRTH_YEAR'].'
		  AND STOPPED		= '.(isset($_POST['STOPPED']) ? "b'1'" : "b'0'").'
		  AND NAME			= "'.addslashes($_POST['NAME']).'"
		  AND TYPE IN		("", "'.(empty($_POST['TYPE']) ? '' : addslashes(implode(',', $_POST['TYPE']))).'")
		  AND GENDER IN		("" , "'.addslashes($_POST['GENDER']).'")
		  AND REALNAME		= "'.addslashes($_POST['REALNAME']).'"'.
	  ( $artist_admin
	  ? ' AND IDENT			= "'.addslashes($_POST['IDENT']).'" ' : '').'
		LIMIT 1',
		DB_USE_MASTER
	);
	if ($existing_artistid === false) {
		return false;
	}
	if ($existing_artistid
	&&	$existing_artistid !== $artistid
	) {
		register_error('artist:error:already_exists_LINE', DO_UBB, ['ARTISTID' => $existing_artistid]);
		$_REQUEST['sID'] = $existing_artistid;
		return true;
	}

	if ($hide_stuff_admin) {
		$setlist['VISIBILITY_VIDEOS'	 ] = 'VISIBILITY_VIDEOS		 = '.($_POST['VISIBILITY_VIDEOS'] ?? 0);
		$setlist['VISIBILITY_APPEARANCES'] = 'VISIBILITY_APPEARANCES	= '.($_POST['VISIBILITY_APPEARANCES'] ?? 0);
		$setlist['HIDDEN_FOR_SEARCH'  	 ] = 'HIDDEN_FOR_SEARCH_ENGINES = '.(isset($_POST['HIDDEN_FOR_SEARCH_ENGINES']) ? "b'1'" : "b'0'");
	}

	if ($artistid
	&&	!$_POST['COUNTRYID']
	&&	false !== ($rc = db_single('artist', 'SELECT COUNTRYID FROM artist WHERE ARTISTID = '.$artistid))
	&&	!$rc
	) {
		$_POST['COUNTRYID'] = 0;
	}

	move_site_to_presence(['SITE', 'BOOKSITE'], true);

	if ($artist_admin) {
		$setlist['NEEDUPDATE' ] = 'NEEDUPDATE='.	(isset($_POST['NEEDUPDATE']) ? "b'1'" : "b'0'");
		$setlist['GENERIC'	  ] = 'GENERIC='.		(isset($_POST['GENERIC']) ?  "b'1'" : "b'0'");
	}
	$setlist['NAME'		 	  ] = 'NAME="'.			addslashes($_POST['NAME']).'"';
	$setlist['NAME_FOR_SEARCH'] = 'NAME_FOR_SEARCH= REGEXP_REPLACE(NAME,"[^[:alnum:]]","")';
	if ($artist_admin) {
		$setlist['IDENT'	  ] = 'IDENT="'.		addslashes(get_fixed_utf8_name($_POST['IDENT'])).'"';
	}
	$setlist['BIRTH_DAY'	  ] = 'BIRTH_DAY='.		$_POST['BIRTH_DAY'];
	$setlist['BIRTH_MONTH'	  ] = 'BIRTH_MONTH='.	$_POST['BIRTH_MONTH'];
	$setlist['BIRTH_YEAR'	  ] = 'BIRTH_YEAR='.	$_POST['BIRTH_YEAR'];
	$setlist['EMAIL'	 	  ]	= 'EMAIL="'.		addslashes($_POST['EMAIL']).'"';
	$setlist['REALNAME'		  ] = 'REALNAME="'.		addslashes($_POST['REALNAME']).'"';
	$setlist['COUNTRYID'	  ] = 'COUNTRYID='.		$_POST['COUNTRYID'];
	$setlist['LIVE_COUNTRYID' ] = 'LIVE_COUNTRYID='.$_POST['LIVE_COUNTRYID'];
	$setlist['FOLLOWUPID'	  ] = 'FOLLOWUPID='.	($_POST['FOLLOWUPID'] ?? 0);
	$setlist['GENDER'		  ] = 'GENDER="'.		$_POST['GENDER'].'"';
	$setlist['SITE'			  ] = 'SITE="'.			($_POST['SITE'] ? addslashes(make_proper_site_utf8($_POST['SITE'], KEEP_ORIGINAL)) : '').'"';
#	$setlist['SITE'			  ] = 'SITE="'.			($_POST['SITE'] ? addslashes($_POST['SITE']) :'').'"';
	$setlist['TRIBUTE_TO'	  ] = 'TRIBUTE_TO='.	$_POST['TRIBUTE_TO'];

	if (!str_contains($_POST['BOOKSITE'], '@')) {
		$setlist['BOOKSITE'] =
			!strcasecmp($_POST['BOOKSITE'], $_POST['SITE'])
			?	'BOOKSITE = "'.($_POST['BOOKSITE'] = '').'"'
			:	'BOOKSITE = "'.($_POST['BOOKSITE'] ? addslashes(make_proper_site_utf8($_POST['BOOKSITE'])) : '').'"';
			#:	'BOOKSITE = "'.($_POST['BOOKSITE'] ? addslashes($_POST['BOOKSITE']) : '').'"';
	} else {
		register_warning('artist:warning:booksite_incorrect_LINE');
	}
	if (isset($_POST['TICKETID'])) {
		$setlist['TICEKTID'] = 'TICKETID='.$_POST['TICKETID'];
	}
	// ORDER_NAME
	$ordername = $_POST['NAME'];
	# FIXME: what is this 0x80 ?
	#if (str_contains($ordername, "\x80")) {
	#	$ordername = str_replace("\x80", 'E', $ordername);
	#}
	$ordername = strip_artist_prefix($ordername);
	$setlist['ORDER_NAME'] = 'ORDER_NAME = "'.addslashes($ordername).'"';

	// CHECKBOXES
	$setlist['BIRTH_HIDDDEN'] = 'BIRTH_HIDDEN = '.(isset($_POST['BIRTH_HIDDEN']) ? "b'1'" : "b'0'");

	// TYPE
	if (!isset($_POST['TYPE'])) {
		$setlist['TYPE'] = 'TYPE = ""';
	} elseif (!require_array($_POST, 'TYPE')) {
		return false;
	} else {
		$setlist['TYPE'] = 'TYPE = "'.addslashes(implode(',', $_POST['TYPE'])).'"';
	}
	// DECEASED
	$setlist['DECEASED'		 ] = 'DECEASED	   = '.(isset($_POST['DECEASED']) ? "b'1'" : "b'0'");
	$setlist['DECEASED_DAY'  ] = 'DECEASED_DAY   = '.$_POST['DECEASED_DAY'];
	$setlist['DECEASED_MONTH'] = 'DECEASED_MONTH = '.$_POST['DECEASED_MONTH'];
	$setlist['DECEASED_YEAR' ] = 'DECEASED_YEAR  = '.$_POST['DECEASED_YEAR'];
	// STOPPED
	$setlist['STOPPED'  ] = 'STOPPED   = '.(isset($_POST['STOPPED']) ? "b'1'" : "b'0'");
	$setlist['STOPDAY'  ] = 'STOPDAY   = '.$_POST['STOPDAY'];
	$setlist['STOPMONTH'] = 'STOPMONTH = '.$_POST['STOPMONTH'];
	$setlist['STOPYEAR' ] = 'STOPYEAR  = '.$_POST['STOPYEAR'];

	if ($artistid) {
		if (!($artist = db_single_assoc('artist','
			SELECT ACCEPTED, MUSERID, MAXDSTAMP, MAXSSTAMP, NAME, NEEDUPDATE
			FROM artist
			WHERE ARTISTID = '.$artistid,
			DB_USE_MASTER))
		) {
			if ($artist !== false) {
				not_found();
			}
			return false;
		}
		if (!$artist_admin
		&&	(	$artist['ACCEPTED']
			?	!require_admin('artist')
			:	!require_self($artist['MUSERID'])
			)
		) {
			return false;
		}
		$setlist['MAXDSTAMP'] = 'MAXDSTAMP = '.(isset($_POST['DECEASED']) ? ($artist['MAXDSTAMP'] ? min($artist['MAXDSTAMP'], CURRENTSTAMP) : CURRENTSTAMP) : 0);
		$setlist['MAXSSTAMP'] = 'MAXSSTAMP = '.(isset($_POST['STOPPED' ]) ? ($artist['MAXSSTAMP'] ? min($artist['MAXSSTAMP'], CURRENTSTAMP) : CURRENTSTAMP) : 0);
		if (!$artist_admin) {
			// if the user is no admin, he must be the creator
			// and the entry may not be valid yet..
			// or he must be the artist himself

			$extra = ' AND ACCEPTED=0 AND MUSERID='.CURRENTUSERID;
		} else {
			$extra = null;
		}

		$setlist = array_values($setlist);

		if (!db_insert('artist_log','
			INSERT INTO artist_log
			SELECT * FROM artist
			WHERE NOT '.binary_equal($setlist).'
			  AND ARTISTID = '.$artistid.
			  $extra)
		) {
			return false;
		}
		if (!db_affected()) {
			register_notice('item:notice:no_change_needed_LINE');
		} else {
			reset_site();
			$setlist['MUSERID'] = 'MUSERID = '.CURRENTUSERID;
			$setlist['MSTAMP' ] = 'MSTAMP = '.CURRENTSTAMP;
			if (!db_update('artist', '
				UPDATE artist SET '.implode(',', $setlist).'
				WHERE ARTISTID = '.$artistid)
			) {
				return false;
			}
			register_notice('artist:notice:changed_LINE');
		}

		memcached_set('lastartistnamechange', CURRENTSTAMP, 3*ONE_DAY);

		if ($artist_admin
		&&	isset($_POST['NEEDUPDATE']) !== $artist['NEEDUPDATE']
		) {
			require_once '_needupdates.inc';
			flush_needupdates();
		}
	} else {
		if (!$artist_admin) {
			$setlist['ADMININFO'] = 'ADMININFO = ""';
		}
		if (isset($_POST['DECEASED'])) {
			$setlist['MAXDSTAMP'] = 'MAXDSTAMP = '.CURRENTSTAMP;
		}
		if (isset($_POST['STOPPED'])) {
			$setlist['MAXSTAMP'] = 'MAXSSTAMP = '.CURRENTSTAMP;
		}
		if (!db_insert('artist','
			INSERT INTO artist SET 
				USERID	='.CURRENTUSERID.',
				MUSERID	='.CURRENTUSERID.',
				CSTAMP	='.CURRENTSTAMP.',
				MSTAMP	='.CURRENTSTAMP.',
				ACCEPTED='.($artist_admin ? "b'1'" : "b'0'").',
				'.implode(',', $setlist))
		) {
			return false;
		}
		$_REQUEST['sID'] = $artistid = db_insert_id();
		memcached_set('lastartistnamechange', CURRENTSTAMP, 3 * ONE_DAY);
		ticket_update('artist', $artistid);
		register_notice('artist:notice:added_LINE', DO_UBB | DO_CONDITIONAL);
		init_site();
	}

	if (!$artist_admin
	&&	false !== ($exists = db_single('artistmember', "
		SELECT 1
		FROM artistmember
		WHERE ID = $artistid
		  AND USERID = ".CURRENTUSERID.'
		LIMIT 1'))
	) {
		require_once '_employees.inc';
		require_once '_visibility.inc';
		if (isset($_POST['ITS_ME'])) {
			if (!$exists) {
				commit_employee(
					CURRENTUSERID, [
						'ACTIVE'			=> true,
						'VISIBILITY_NAME'	=> EVERYBODY,
					],
					'artist',
					$artistid
				);
			}
		} elseif ($exists) {
			remove_employee(CURRENTUSERID,'artist', $artistid);
		}
	}
	if ($artist_admin) {
		require_once '_alternate_name.inc';
		commit_alternate_names('artist', $artistid);
	}
	require_once '_uploadimage.inc';
	uploadimage_actual_receive('artist', $artistid);
	require_once '_process_appic_changes.inc';
	cleanup_update_from_appic($artistid);
	_genrelist_commit();
	store_presence();
	element_changed();
	process_inquiry_allowance();

	if (!empty($_POST['UPDATE_ALL_LINEUP'])
	&&	(require_once '_artist_types.inc')
	&&	get_artist_type($_POST['UPDATE_ALL_LINEUP'])
	&&	in_array($_POST['UPDATE_ALL_LINEUP'], $_POST['TYPE'])
	&&	($update_parties = db_simpler_array('lineup','
			SELECT DISTINCT PARTYID
			FROM lineup
			WHERE TYPE != "'.addslashes($_POST['UPDATE_ALL_LINEUP']).'"
			  AND ARTISTID = '.$artistid))
	) {
			db_insert('lineup_log','
			INSERT INTO lineup_log
			SELECT *, '.CURRENTSTAMP.', '.CURRENTUSERID.'
			FROM lineup
			WHERE TYPE != "'.addslashes($_POST['UPDATE_ALL_LINEUP']).'"
			  AND ARTISTID = '.$artistid)
		&&	db_update('lineup','
			UPDATE lineup SET
				MUSERID	='.CURRENTUSERID.',
				MSTAMP	='.CURRENTSTAMP.',
				TYPE	="'.addslashes($_POST['UPDATE_ALL_LINEUP']).'"
			WHERE TYPE != "'.addslashes($_POST['UPDATE_ALL_LINEUP']).'"
			  AND ARTISTID = '.$artistid);
		require_once '_pagechanged.inc';
		page_changed('party', $update_parties);
		register_notice('lineup:notice:lineups_have_been_updated_LINE');

	} elseif (
		!empty($_POST['UPDATE_LINEUP'])
	&&	(require_once '_artist_types.inc')
	&&	get_artist_type($_POST['UPDATE_LINEUP'])
	&&	in_array($_POST['UPDATE_LINEUP'], $_POST['TYPE'])
	&&	($update_parties = db_simpler_array('lineup','
			SELECT DISTINCT PARTYID
			FROM lineup
			WHERE TYPE = ""
			  AND ARTISTID = '.$artistid))
	) {
			db_insert('lineup_log','
			INSERT INTO lineup_log
			SELECT *,'.CURRENTSTAMP.','.CURRENTUSERID.' FROM lineup
			WHERE TYPE = ""
			  AND ARTISTID = '.$artistid)
		&&	db_update('lineup','
			UPDATE lineup SET
				MUSERID	='.CURRENTUSERID.',
				MSTAMP	='.CURRENTSTAMP.',
				TYPE	="'.addslashes($_POST['UPDATE_LINEUP']).'"
			WHERE TYPE = ""
			  AND ARTISTID = '.$artistid);
		require_once '_pagechanged.inc';
		page_changed('party', $update_parties);
		register_notice('lineup:notice:lineups_have_been_updated_LINE');
	}
	flush_artist_name($artistid);
	page_changed('artist', $artistid);
	return $artistid;
}

function strip_artist_prefix(string $name): string {
	if(preg_match('/^(?:de|das|the|tha|da|la|mc|mr\.?(?:(?:\s+and\s+|\s*&\s*)mrs\.?)?|st|dr|d\')[.:]? (?<name>.*)$/iu', $name, $match)){
		return $match['name'];
	}
	return $name;
}

function get_realnames_list(string $text, int &$name_count = 0): string {
	$placeholders = ['...'];
	$replacements = ['&hellip;'];
	if (false === ($text_length = mb_strlen($text))) {
		return $text;
	}
	assert(is_int($text_length)); # Satisfy EA inspection
	if (false !== mb_strpos($text, '(')) {
		$i = 1;
		if (null !== ($new_text = preg_replace_callback($regexp = '"\s*\((?<function>[^)]+)\)"', static function(array $match) use(&$placeholders, &$i, &$replacements): string {
			$placeholders[$i] = ($placeholder = "\x2$i\x3");
			$replacements[$i] = ' <span class="light">('.escape_utf8($match['function']).')</span>';
			++$i;
			return $placeholder;
		}, $text))
		) {
			assert(is_string($new_text)); # Satisfy EA inspection
			$text = $new_text;
		} else {
			preg_failure($text, $regexp);
		}
	}
	if (false === ($names = preg_split($regexp = '![,&]!u', $text))) {
		preg_failure($text, $regexp);
		return $text;
	}
	assert(is_array($names)); # Satisfy EA inspection
	$name_count = count($names);
	foreach ($names as &$name) {
		$name = utf8_mytrim($name);
		if (preg_match('"^(?<name>.*?)\s*\((?<function>[^)]+)\)$"u', $name, $match)) {
			$name = escape_utf8($match['name']).' <span class="light">('.escape_utf8($match['function']).')</span>';
		} else {
			$name = escape_utf8($name);
		}
	}
	unset($name);
	if (!isset($names[1])) {
		$name_str = $names[0];
	} elseif (
		!isset($names[2])
	||	$text_length <= 60
	) {
		$last_name = array_pop($names);
		$name_str = implode(', ', $names).' &amp; '.$last_name;
	} else {
		$name_str = '<div>'.implode('</div><div>', $names).'</div>';
	}
	return str_replace($placeholders, $replacements, $name_str);
}
