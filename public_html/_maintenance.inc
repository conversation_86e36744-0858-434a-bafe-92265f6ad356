<?php

declare(strict_types=1);

require_once 'defines/db.inc';

const SERVER_WEB_REGULAR	= 1;
const SERVER_WEB_VIP		= 2;
const SERVER_DB_MASTER		= 5;
const SERVER_DB_SLAVE		= 6;

const MAINTENANCE_WEB_SERVERS = [
	'webone'		=> SERVER_WEB_REGULAR,
	'webtwo'		=> SERVER_WEB_REGULAR,
	'webfive'		=> SERVER_WEB_REGULAR,
	'webstore'		=> SERVER_WEB_REGULAR,
	'webx'			=> SERVER_WEB_VIP,
];

const MAINTENANCE_DATABASE_SERVERS = [
	'dbu'			=> CURRENT_MASTER_party_db	 === 'dbu'				? SERVER_DB_MASTER : SERVER_DB_SLAVE,
	'dba'			=> CURRENT_MASTER_party_db	 === 'dba'				? SERVER_DB_MASTER : SERVER_DB_SLAVE,
	'dbu_dbc'		=> CURRENT_MASTER_counter_db === 'dbu_dbc'			? SERVER_DB_MASTER : SERVER_DB_SLAVE,
	'dbu_ndbd'		=> CURRENT_MASTER_data_db	 === 'dbu_ndbd'			? SERVER_DB_MASTER : SERVER_DB_SLAVE,
	'dbo'			=> CURRENT_MASTER_party_db	 === 'dbo'				? SERVER_DB_MASTER : SERVER_DB_SLAVE,
	'dbyin'			=> CURRENT_MASTER_party_db	 === 'dbyin'			? SERVER_DB_MASTER : SERVER_DB_SLAVE,
	'dbyin_dbc'		=> CURRENT_MASTER_counter_db === 'dbyin_dbc'		? SERVER_DB_MASTER : SERVER_DB_SLAVE,
	'dbyin_ndbd'	=> CURRENT_MASTER_data_db	 === 'dbyin_ndbd'		? SERVER_DB_MASTER : SERVER_DB_SLAVE,
	'dbyang'		=> CURRENT_MASTER_party_db	 === 'dbyang'			? SERVER_DB_MASTER : SERVER_DB_SLAVE,
	'dbyang_dbc'	=> CURRENT_MASTER_counter_db === 'dbyang_dbc'		? SERVER_DB_MASTER : SERVER_DB_SLAVE,
	'dbyang_ndbd'	=> CURRENT_MASTER_data_db	 === 'dbyang_ndbd'		? SERVER_DB_MASTER : SERVER_DB_SLAVE,
	'dbspecial_dbm'	=> CURRENT_MASTER_party_db	 === 'dbspecial_dbm'	? SERVER_DB_MASTER : SERVER_DB_SLAVE,
	'dbspecial_dbc'	=> CURRENT_MASTER_counter_db === 'dbspecial_dbm'	? SERVER_DB_MASTER : SERVER_DB_SLAVE,
	'dbblack'		=> CURRENT_MASTER_counter_db === 'dbblack'			? SERVER_DB_MASTER : SERVER_DB_SLAVE,
	'dbwhite'		=> CURRENT_MASTER_counter_db === 'dbwhite'			? SERVER_DB_MASTER : SERVER_DB_SLAVE,
	'dbsrch'		=> CURRENT_MASTER_party_db	 === 'dbsrch'			? SERVER_DB_MASTER : SERVER_DB_SLAVE,
	'dbsrchrep'		=> CURRENT_MASTER_party_db	 === 'dbsrchrep'		? SERVER_DB_MASTER : SERVER_DB_SLAVE,
	'ndbd2'			=> CURRENT_MASTER_data_db	 === 'ndbd2'			? SERVER_DB_MASTER : SERVER_DB_SLAVE,
	'ndbd3'			=> CURRENT_MASTER_data_db	 === 'ndbd3'			? SERVER_DB_MASTER : SERVER_DB_SLAVE,
	'dbbm'	 		=> SERVER_DB_SLAVE,
	'dbbc'			=> SERVER_DB_SLAVE,
	'ndbbd'			=> SERVER_DB_SLAVE,
	'dbbds1'		=> SERVER_DB_SLAVE,
];

/*const MAINTENANCE_MEM_SERVERS = [
	'partymem4',
	'partymem5',
];*/

const MAINTENANCE_ALL_SERVERS = [
	'preparty',

	'webone',
	'webtwo',
	'webfive',
	'webstore',
	'webx',

	'cron',

	'ultraparty',
	'alphaparty',
	'omegaparty',
	'yin',
	'yang',
	'special2',

	'searchparty',
	'searchpartyrep',

	'white',
	'black',

	'newdata2',
	'newdata3',

	'backupparty',
	'superstore',

	'partymem4',
	'partymem5',
];

const MAINTENANCE_DATABASE_HANDLE_TO_HOST = [
	'dba'			=> 'alphaparty',
	'dbu'			=> 'ultraparty',
	'dbu_dbc'		=> 'ultraparty',
	'dbu_ndbd'		=> 'ultraparty',
	'dbo'			=> 'omegaparty',
	'dbyin'			=> 'yin',
	'dbyin_dbc'		=> 'yin',
	'dbyin_ndbd'	=> 'yin',
	'dbyang'		=> 'yang',
	'dbyang_dbc'	=> 'yang',
	'dbyang_ndbd'	=> 'yang',
	'dbspecial_dbm'	=> 'special2',
	'dbspecial_dbc' => 'special2',
	'dbblack'		=> 'black',
	'dbwhite'		=> 'white',
	'dbsrch'		=> 'searchparty',
	'dbsrchrep'		=> 'searchpartyrep',
	'ndbd2'			=> 'newdata2',
	'ndbd3'			=> 'newdata3',
	'dbbm'			=> 'backupparty',
	'dbbc'			=> 'backupparty',
	'ndbbd'			=> 'backupparty',
	'dbbds1'		=> 'backupparty',
];

const MAINTENANCE_DATABASE_TYPE_TO_NAME = [
	SERVER_DB_MASTER => 'master',
	SERVER_DB_SLAVE	 => 'slave',
];
