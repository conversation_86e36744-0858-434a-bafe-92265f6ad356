<?php

function showMarkComplete(array $party): void {
	include_js('js/form/complete');
	$partyid = $party['PARTYID'];
	if (false === ($incomplete_stamp = db_single('lineupneedupdate','
		SELECT CSTAMP
		FROM lineupneedupdate
		WHERE PARTYID = '.$partyid
	))) {
		return;
	}
	?><span<?
	if (!$incomplete_stamp) {
		?> data-complete="data-complete"<?
	}
	?> id="complete-marker"<?
	?> onclick="Pf.markComplete(this,<?= $partyid ?>)"<?
	?> class="nowrap unhideanchor"<?
	?> data-other="<?= __C(!$incomplete_stamp ? 'action:mark_complete' : 'action:mark_incomplete') ?>"><?
	echo __C(!$incomplete_stamp ? 'action:mark_incomplete' : 'action:mark_complete');
	?></span><?
}

function show_incomplete_icon(array|bool &$party, ?string $class = null): void {
	$title = null;
	if ($party !== true) {
		$lineup_needupdate = !empty($party['LINEUP_NEEDUPDATE']);

		$title = __($lineup_needupdate ? 'action:check' : 'attrib:incomplete');
	}

	show_warning_icon($title, 'lineupimgneedupd', $class, $party === true ? '16px' : null);
}

function show_incomplete_notice(array &$party): void {
	$lineup_incomplete = !empty($party['LINEUP_NEEDUPDATE']);
	$party_admin = have_admin('party');
	$past = $party['STAMP'] < CURRENTSTAMP;

	?><div class="<?
	if (!$lineup_incomplete) {
		?>hidden <?
	}
	if ($party_admin) {
		?>warning <?
	}
	?>block" id="lineupneedupdate"><?
	echo  __('party:info:lineup_not_complete_LINE', ['PAST'=>$past]);

	if ($party_admin
	&&	!$past
	) {
		show_incomplete_icon($party);
	}
	?></div><?
}

function process_complete(bool $complete = true): int {
	if (!($partyid = require_idnumber($_POST, 'PARTYID'))) {
		return 400;
	}
	require_once '_currentuser.inc';
	require_once '_lock.inc';

	global $currentuser;
	$currentuser = new _currentuser(TINY_USER | SKIP_SETTINGS);

	if (!require_last_lock_and_freshen(LOCK_PARTY, $partyid)
	||	!require_admin_or_my_party($partyid)
	) {
		return 403;
	}
	if ($complete) {
		if (!db_insert('lineupneedupdate_log','
			INSERT INTO lineupneedupdate_log
			SELECT *, '.CURRENTSTAMP.', '.CURRENTUSERID.'
			FROM lineupneedupdate
			WHERE PARTYID = '.$partyid)
		||	!db_delete('lineupneedupdate','
			DELETE FROM lineupneedupdate
			WHERE PARTYID = '.$partyid)
		) {
			bail(500);
		}
	} elseif (!db_insert('lineupneedupdate', '
		INSERT IGNORE INTO lineupneedupdate SET
			PARTYID	= '.$partyid.',
			CSTAMP	= '.CURRENTSTAMP.',
			CUSERID	= '.CURRENTUSERID)
	) {
		return 500;
	}
	flush_party($partyid);
	return 200;
}
