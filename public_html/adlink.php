<?php

declare(strict_types=1);

define('CURRENTSTAMP',	time());
define('TODAYSTAMP',	mktime(0,0,0));

function bail(int $errno): never {
	http_response_code($errno);
	exit;
}

require_once '_exit_if_offline.inc';
require_once '_argparse.inc';
require_once '_require.inc';
require_once '_getcurrentaddr.inc';
require_once '_identity.inc';
require_once '_currentuser.inc';
require_once '_hosts.inc';
require_once '_spider.inc';
require_once '_nocache.inc';

send_no_cache_headers();

require_once '_db.inc';
require_once '_memcache.inc';

if (!($adid = have_number($_REQUEST, 'ADID'))) {
	bail(404);
}

if (!($ad = memcached_single_assoc(['ad', 'banner', 'adinfo'],'
	SELECT ADID, BANNERID, IF(PARTYID, "partylist", TYPE) AS TYPE, PARTYID, DISCOUNT, URL_ADD_DEFAULT_UTM
	FROM ad
	LEFT JOIN adinfo info USING (ADID)
	LEFT JOIN banner USING (BANNERID)
	WHERE ADID = '.$adid
))) {
	bail($ad === false ? 503 : 404);
}

$bannerid = $ad['BANNERID'];
$extraid = 0;
switch ($type = $ad['TYPE']) {
case 'multiparty':
	$extraid = have_idnumber($_GET, 'EXTRA');
	$url = db_single('adinfo', 'SELECT URL FROM adinfo WHERE ADID = '.$adid);
	if ($extraid
	&&	(	!$url
		||	contains_partyflock_domain($url)
		)
	) {
		require_once '_urltitle.inc';
		$url = get_element_href('party', $extraid);
	}
	break;

case 'partylist':
	require_once '_urltitle.inc';
	$url = 'https://'.$_SERVER['HTTP_HOST'].get_element_href('party', $ad['PARTYID']);
	break;

case 'iframe':
	$url = db_single('banneriframe','SELECT URL FROM banneriframe WHERE BANNERID = '.$ad['BANNERID']);
	break;

case 'html':
	if ($url = db_single('adinfo','SELECT URL FROM adinfo WHERE ADID = '.$adid)) {
		break;
	}
	if (preg_match('"^/'.ADLINK_HUSSLE.'/\d+/\d+/\d+/\??(?<url>(?:http|/).*)$"i', $_SERVER['REQUEST_URI'], $match)
	||	preg_match('"^/(?:adlink|[a-z]+lnk)/\d+(?:/\d+/\d+)?/(?<url>(?:http|/).*)$"i', $_SERVER['REQUEST_URI'], $match)
	) {
		$url = $match['url'];
	}
	break;

case 'floormovie':
case 'upload':
case 'link':
	$url = db_single('adinfo', 'SELECT URL FROM adinfo WHERE ADID = '.$adid);
	break;

case 'textlinks':
	if (false === ($id = have_number($_REQUEST, 'EXTRA'))) {
		bail(404);
	}
	require_once '_bannertextlinks.inc';
	if (!($text_links = get_banner_textlinks($bannerid))) {
		bail(404);
	}
	$url = isset($text_links[$id]) ? $text_links[$id][1] : false;
	break;

case 'ubb':
	if (isset($_REQUEST['EXTRA'])
	&&	preg_match('"^(?<element>[a-z_]+)=(?<id>\d+)$"', $_REQUEST['EXTRA'], $match)
	) {
		$url = "https://{$_SERVER['HTTP_HOST']}/{$match['element']}/{$match['id']}";
	} elseif (preg_match('"^/(?:adlink|\wlnk)/\d+/(?<url>(?:http|/).*)$"i', $_SERVER['REQUEST_URI'], $match)) {
		$url = $match[1];
		if ($url[0] === '/') {
			$url = 'https://'.$_SERVER['HTTP_HOST'].$url;
		}
		$dont_count =
			!($body = db_single('bannerubb', 'SELECT BODY FROM bannerubb WHERE BANNERID = '.$bannerid))
		||	!str_contains($body, $url);
	}
	break;
default:
	error_log("invalid banner type: {$ad['TYPE']} for ad: $adid");
	bail(500);
}

if (!isset($url)) {
	mail_log("url is not set for ad: $adid");
	bail(500);
}
if (!$url) {
	if ($url !== false) {
		mail_log("empty url for ad: $adid", $url);
	}
	bail(503);
}

$uniq = have_idnumber($_SERVER, 'eUNIQ');

if (empty($dont_count)
&&	!ROBOT
) {
	$incid = have_idnumber($_SERVER, 'eINCID');
	$currentuser = new _currentuser(TINY_USER | SKIP_SETTINGS);
	require_once '_adcount.inc';
	adclick($adid, $incid, $uniq, $extraid);
}

require_once '_ad.inc';
require_once '_http_status.inc';

$show_url = $url;

$url = get_ad_url($ad, $url);

header('Location: '.$url, true, http_status::SEE_OTHER->value);
header('Content-Type: text/html; charset=us-ascii');

?><a href="<?= escape_specials($url) ?>"><?= escape_specials($show_url) ?></a><?
