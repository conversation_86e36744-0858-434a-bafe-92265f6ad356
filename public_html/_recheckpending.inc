<?php

function recheck_pending_counts(string $element): array {
	require_once '_lock.inc';
	if (false === ($locked_ids = locked_ids($element === 'job' ? LOCK_JOB : LOCK_PROMO))) {
		return [0, 0];
	}
	$inactives = memcached_rowuse_array($element,'
		SELECT JOBID,REQUEST, STARTSTAMP, COSTSENT
		FROM '.$element.'
		WHERE ACTIVE = 0
		  AND REMOVED = 0
		  AND (REQUEST = 0 OR COSTSENT != 0)'.(
		$locked_ids ? ' AND '.strtoupper($element).'ID NOT IN ('.implode(', ', $locked_ids).')' : ''),
		FIVE_MINUTES
	);
	if (!$inactives) {
		return [0, 0];
	}
	static $checkstamps;
	$yesterday = strtotime('-1 day', CURRENTSTAMP);
	$rechecks = 0;
	$pending = 0;
	foreach ($inactives as $element) {
		[,,,,,, $weekday] = _getdate($startstamp = $element['STARTSTAMP']);
		# NOTE:	default is to recheck pending elements 2 days before they should run
		#	but no work on saturday and sunday, so:
		#			wed thu fri (sat sun) mon tue wed
		#	start on sunday -> check 3 days in advance
		#	start on monday or tuesday -> check 4 days in advance

		// NOTE: no correction for leapday
		switch ($weekday) {
		default: $diff = 2;
			break;
		case 0: // sunday
			$diff = 3;
			break;
		case 1: // monday
		case 2:	// tuesday
			$diff = 4;
			break;
		}
		$checkstamp =
			isset($checkstamps[$startstamp])
		?	$checkstamps[$startstamp]
		:	($checkstamps[$startstamp] = strtotime('-'.$diff.' days', $startstamp));
		if ((	!($costsent = $element['COSTSENT'])
			&&	$element['REQUEST']
			)
		||	CURRENTSTAMP > $checkstamp
		&&	$costsent < $yesterday
		&&	$costsent < $checkstamp
		) {
			++$rechecks;
		} else {
			++$pending;
		}
	}
	return [$rechecks, $pending];
}

function recheck_pending_lists($inactives) {
	$rechecks = array();
	$pending = array();
	if ($inactives) {
		static $checkstamps;
		$yesterday = strtotime('-1 day',CURRENTSTAMP);
		foreach ($inactives as $element) {
			list(,,,,,, $weekday) = _getdate($startstamp = $element['STARTSTAMP']);
			// NOTE: no correction for leapday
			switch ($weekday) {
			default:
				$diff = 2;
				break;
			case 0: // sunday
				$diff = 3;
				break;
			case 1:	// monday
			case 2: // tuesday
				$diff = 4;
				break;
			}
			$checkstamp =
				isset($checkstamps[$startstamp])
			?	$checkstamps[$startstamp]
			:	($checkstamps[$startstamp] = strtotime('-'.$diff.' days',$startstamp));
			if ((	!($costsent = $element['COSTSENT'])
				&&	$element['REQUEST']
				)
			||	CURRENTSTAMP > $checkstamp
			&&	$costsent < $yesterday
			&&	$costsent < $checkstamp
			) {
				$rechecks[] = $element;
			} else {
				$pending[] = $element;
			}
		}
	}
	return array($rechecks,$pending);
}
