<?php

// GAP BETWEEN 21317,587582

require_once '_pagecache.inc';
require_once '_pagecontrols.inc';
require_once '_message.inc';
require_once '_profileimage.inc';
require_once '_settings.inc';
require_once '_subscriptions.inc';
require_once '_commentobject.inc';

class _topic {
	// private
	//var $key	= null;
	//private $cached_result= null;

	private int				$topicid;
	private ?array			$messages;
	private array			$first_message;
	private ?_pagecontrols	$controls;
	private int				$total			= 0;

	// public

	public array			$topicrow;
	public bool				$is_admin		= false;
	public bool				$may_view		= true;
	public int|string		$page;

	final public function __construct(int $topicid) {
		$this->topicid = $topicid;

		if (!($topic = memcached_single_assoc(['topic', 'forum'],'
			SELECT	topic.*,
					forum.SHORT, forum.NAME AS FNAME, ANCIENTDIFF, forum.FLAGS AS FORUM_FLAGS
			FROM topic
			JOIN forum USING (FORUMID)
			WHERE TOPICID = '.$topicid,
			FIVE_MINUTES,
			'topic:'.$topicid))
		) {
			mail_log("topic $topicid doesn't exist?");
			return;
		}
		$this->topicrow = $topic;
		global $__FORUMID;
		$__FORUMID = $topic['FORUMID'];
		global $currentuser;
		if (!($rights = $currentuser->forum_rights($this->topicrow['FORUMID']))
		||	!in_array($rights, ['admin', 'normal', 'read'], true)
		) {
			// no rights
			$this->may_view = false;
			return;
		}
		if ((	($this->is_admin = ($rights === 'admin'))
			||	have_user())
			# fix messages per topic
		&&	0 === setting('MESSAGES_PER_TOPIC')
		) {
			setting('MESSAGES_PER_TOPIC', CURRENTUSERID, 50);
		}
	}
	final public function for_sale(): bool {
		require_once '_forum.inc';
		if (!isset($this->topicrow['FORUM_FLAGS'])) {
			mail_log('no topic row', $this->topicrow);
		}
		if ($this->topicrow) {
			return (bool)($this->topicrow['FORUM_FLAGS'] & FORUMFLAG_SALE);
		}
		return false;
	}

	final public function exists(): bool {
		return (bool)$this->topicrow;
	}
	final public function userid(): ?int {
		return $this->topicrow['USERID'] ?? null;
	}
	final public function short(): ?string {
		return $this->topicrow['SHORT'] ?? null;
	}
	final public function forumid(): int {
		return $this->topicrow['FORUMID'] ?? 0;
	}
	final public function topicid(): int {
		return $this->topicid ?? 0;
	}
	final public function sticky(): bool {
		return (bool)$this->topicrow['STICKY'];
	}
	final public function status(): string {
		if (!isset($this->topicrow['STATUS'])) {
			mail_log('topicrow.STATUS is not set', get_defined_vars());
			return '';
		}
		return $this->topicrow['STATUS'];
	}
	final public function query(): array|bool {
		if (!$this->topicrow) {
			return false;
		}
		$this->topicrow['VIEWS'] = db_single('topic_year_counter','SELECT CAST(SUM(VIEWS) AS UNSIGNED) FROM topic_year_counter WHERE TOPICID='.$this->topicid);

		if (!$this->topicrow['MSGCNT']) {
			$this->first_message = [];
			return true;
		}
		if (!($first_messageid = $this->topicrow['FIRST_MESSAGEID'])) {
			mail_log('first message not found for topic:'.$this->topicid);
			if (false === ($first_messageid = memcached_single('message','
				SELECT MIN(message.MESSAGEID)
				FROM message
				WHERE TOPICID = '.$this->topicid,
				ONE_HOUR))
			) {
				return false;
			}
			if ($first_messageid) {
				require_once '_topicactions.inc';
				if (!update_topicstats('topic',$this->topicid,'
					UPDATE topic SET
						FIRST_MESSAGEID = '.$first_messageid.'
					WHERE TOPICID = '.$this->topicid,
					DB_KEEP_CLEAN)
				) {
					return false;
				}
			} else {
				mail_log("empty topic: https://partyflock.nl/topic/{$this->topici}");
				if (!db_delete('topic','
					DELETE FROM topic
					WHERE TOPICID = '.$this->topicid)
				) {
					return false;
				}
				if (db_affected()) {
					memcached_delete('topic:'.$this->topicid);
				}
				register_error('topic:error:is_empty_LINE');
				return false;
			}
		}
		$this->total = $this->topicrow['MSGCNT'];
		if ($this->first_message) {
			# First message is always shown on top and does not
			# belong to any page
			--$this->total;
		}
		if (!($fmsgs = query_message('message', $first_messageid, $this->is_admin ? DB_FRESHEN_MEMCACHE : 0))) {
			return $fmsgs;
		}
		$this->first_message = $fmsgs[0];

		// first get only the MESSAGEIDS
		// this is particularly useful for queries for ancient pages
		// which use a LIMIT 100020,50 part.
		// using only the keyfile at first and then selecting only the
		// messages with specified ids is somehow much faster

		if (!$this->total) {
			$this->controls = null;
		} else {
			$this->controls = new _pagecontrols();
			$this->controls->set_total($this->total - 1);
			$this->controls->show_prev_next();
			$this->controls->set_per_page(setting('MESSAGES_PER_TOPIC'));
			$this->controls->set_url("/topic/$this->topicid");
			if (!ROBOT) {
				$this->controls->include_all();
			}
			$this->controls->set_order('MESSAGEID');
		}
		if (false === ($messageids = memcached_simpler_array('message', "
			SELECT MESSAGEID
			FROM message
			WHERE TOPICID = $this->topicid
			  AND MESSAGEID != {$this->first_message['MESSAGEID']}".
			$this->controls?->order_and_limit(),
			FIVE_MINUTES,
			get_page_cache_key('topic', $this->topicid, $this->controls->obtain_page())))
		) {
			return false;
		}
		$user_id_list = [];
		if ($messageids) {
			if (!($messages = get_comments('message', $this->topicid, implode(',', $messageids)))) {
				return $messages;
			}
			number_sort($messages, 'MESSAGEID');
			foreach ($messages as $message) {
				if (!isset($user_id_list[$userid = $message['USERID']])) {
					$user_id_list[$userid] = $userid;
				}
				if (($muserid = $message['MUSERID'])
				&&	!isset($user_id_list[$muserid])
				) {
					$user_id_list[$muserid] = $muserid;
				}
			}
			if (setting('SHOW_PROFILE_IMAGES')) {
				require_once '_profileimage.inc';
			}
			$this->messages = $messages;
		}
		// add first post
		if (!isset($user_id_list[$userid = $this->first_message['USERID']])) {
			$user_id_list[$userid] = $userid;
		}
		if ($muserid = $this->first_message['MUSERID']) {
			$user_id_list[$muserid] = $muserid;
		}
		$messageids[] = $first_messageid;
		require_once '_karma.inc';
		karma($messageids, 'message');
		memcached_prefetch_users($user_id_list);
		return true;
	}
	final public function have_rows(): bool {
		return (bool)$this->first_message;
	}
	final public function display(): void {
		require_once 'defines/post.inc';
		require_once 'defines/topic.inc';
		require_once '_commentobject.inc';
		?><section><div class="section"<?
		global $__refresh_key;
		if ($__refresh_key) {
			?> data-refreshkey="<?= escape_specials($__refresh_key) ?>"<?
		}
		?>><?
		$flags = 0;
		if (!empty($GLOBALS['__topicmayreply'])) {
			$flags |= CMTFLG_MAY_REPLY;
		}
		if ($this->is_admin) {
			$flags |= CMTFLG_IS_ADMIN;
		}
		if ($lastmsg = $this->first_message) {
			comment_display(
				'message',
				$this->topicrow,
				$this->first_message,
				$flags | CMTFLG_SHOW_FIRST_MESSAGE
			);
		}
		if ($this->messages) {
			$this->controls?->display_and_store();

			foreach ($this->messages as $message) {
				comment_display('message',$this->topicrow,$message,$flags);
			}
			$lastmsg = end($this->messages);
		}
		if ($lastmsg) {
			view_element('topic',$this->topicid,$lastmsg['MESSAGEID'],$lastmsg['MSGNO']);
		}
		if (isset($GLOBALS['__topicmayreply'])) {
			comment_display('message',$this->topicrow);
		}
		if ($this->messages) {
			$this->controls?->display_stored();
		}
		?></div></section><?
	}
	final public function subject(): string {
		if (isset($this->topicrow)) {
			return $this->topicrow['SUBJECT'];
		}
		return 'MISSING_SUBJECT';
	}
}
function _topic_adminnable($topicid): bool {
	return	($forumid = have_topic_forumid($topicid))
		&&	$GLOBALS['currentuser']->is_forum_admin($forumid);
}
function _topic_adminnable_sales($topicid): bool {
	if (!($row = db_single_assoc(['topic', 'forum'],'
		SELECT FORUMID, USERID, forum.FLAGS AS FORUM_FLAGS
		FROM topic
		JOIN forum USING (FORUMID)
		WHERE TOPICID = '.$topicid))
	) {
		if ($row !== false) {
			not_found('topic',$topicid);
		}
		return false;
	}
	require_once '_forum.inc';
	/** @noinspection OffsetOperationsInspection */
	return	($row['FORUM_FLAGS'] & FORUMFLAG_SALE)
		&&	CURRENTUSERID === $row['USERID']
		||	$GLOBALS['currentuser']->is_forum_admin($row['FORUMID']);
}
