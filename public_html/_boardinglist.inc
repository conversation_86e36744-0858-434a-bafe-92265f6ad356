<?php

class boardinglist {
	private $joins		= null;
	private $wheres		= null;

	// in:
	public bool $show_header	= true;
	public bool $show_city		= false;
	public bool $show_country	= true;
	public bool $show_prefix	= false;
	public bool $show_address	= false;
	public bool $show_stats		= false;
	public $order_by			= false;

	// out:
	public  int $numrows		= 0;
	private int $countrycnt		= 0;

	function is_empty(): bool {
		return empty($this->boardings);
	}

	function query(): bool {
		if ($this->boardings = memcached_rowuse_hash(['boarding', 'city'], '
			SELECT BOARDINGID,boarding.NAME,CITYID,COUNTRYID,ADDRESS
			FROM boarding
			JOIN city USING (CITYID)'.
			($this->wheres ? ' WHERE '.implode(' AND ',$this->wheres) : null).'
			ORDER BY COUNTRYID,boarding.NAME ASC',
			TEN_MINUTES
		)) {
			if ($this->show_stats) {
				$this->stats = memcached_simple_hash('party','
					SELECT BOARDINGID,COUNT(*),MAX(STAMP)
					FROM party
					WHERE BOARDINGID IN ('.implodekeys(',',$this->boardings).')
					GROUP BY BOARDINGID'
				);
			}
			foreach ($this->boardings as /* $boardingid => */ $info) {
				$this->countries[$info['COUNTRYID']] = true;
			}
			$this->countrycnt = count($this->countries);
		}
		return $this->boardings !== false;
	}

	function order_by_city_and_name(): void {
		$this->show_city = true;
		$this->orders = 'city.NAME,boarding.NAME';
	}
	function display_header() {
		layout_start_header_row();
		layout_header_cell(Eelement_name('address'));
		if ($this->show_stats) {
			layout_header_cell_right(Eelement_plural_name('party'),CELL_RIGHT_SPACE);
			layout_header_cell_right(__C('field:last'),CELL_RIGHT_SPACE);
		}
		layout_header_cell(Eelement_name('name'));
		if ($this->show_city) {
			layout_header_cell(Eelement_name('city'));
		}
		layout_stop_header_row();
	}
	function display() {
		if (!$this->boardings) return;

		$current = -1;

		foreach ($this->boardings as $boardingid => $boarding) {
			if ($boarding['COUNTRYID'] != $current) {
				if ($current != -1) {
					?></table><?
				}
				if ($this->show_header && $this->show_country) {
					layout_box_header(
						$boarding['COUNTRYID']
					?	get_element_link('country',$boarding['COUNTRYID'])
					:	__C('locationlist:no_mans_land')
					);
				}
				if ($this->show_header) {
					layout_open_table(TABLE_FULL_WIDTH | TABLE_SORTABLE);
					$this->display_header();
				} else {
					layout_open_table(TABLE_FULL_WIDTH);
				}
				$current = $boarding['COUNTRYID'];
			}
			$this->display_row($boardingid,$boarding);
		}
		?></table><?
	}
	function display_row($boardingid,$boarding) {
		?><tr><?
		?><td<?
		if ($this->countrycnt > 1) {
			?> style="width:40%"<?
		}
		?>><?
		?><a href="<?= get_element_href('boarding',$boarding['BOARDINGID'],$boarding['ADDRESS']); ?>"><?
		echo escape_utf8($boarding['ADDRESS']);
		?></a><?
		if ($this->show_stats) {
			if ($info = isset($this->stats[$boardingid]) ? $this->stats[$boardingid] : false) {
				[$cnt, $stamp] = keyval($info);
				layout_next_cell(class: 'rpad right');
				echo $cnt;
				set_cell_value($stamp);
				layout_next_cell(class: 'rpad right nowrap');
				 _date_display($stamp);
			} else {
				layout_next_cell();
				layout_next_cell();
			}
		}
		layout_next_cell();
		if ($boarding['NAME'] !== $boarding['ADDRESS']) {
			echo escape_utf8($boarding['NAME']);
		}
		if ($this->show_city) {
			?></td><?
			?><td<?
			if ($this->countrycnt > 1) {
				?> style="width:20%"<?
			}
			?>><?
			if ($boarding['CITYID']) {
				echo get_element_link('city',$boarding['CITYID']);
			}
		}
		layout_stop_row();
	}
	function in_city($CITYID) {
		$this->wheres[] = 'boarding.CITYID='.$CITYID;
	}
	function in_cities($idstr) {
		$this->wheres[] = 'boarding.CITYID IN ('.$idstr.')';
	}
	function in_country($countryid) {
		$this->wheres[] = 'city.COUNTRYID='.$countryid;
	}
	function like($str) {
		$searchstr = str_replace(' ','%',addslashes(db_quote_wild($str)));
		$this->wheres[] =
			'boarding.NAME LIKE "%'.$searchstr.'%" OR boarding.ADDRESS LIKE "%'.$searchstr.'%"';
	}
	function clear() {
		$this->wheres = array();
	}
}
