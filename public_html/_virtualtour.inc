<?php

function get_virtualtour_button(string $element, int $id): string|false|null {
	require_once '_memcache.inc';
/*	if ($element === false) {
		ob_start();
		?><a href="/virtualtour"><img src="<?= STATIC_HOST ?>/images/inside.png" class="inside" /></a><?
		return ob_get_clean();
	}
	if (is_number($element)) {
		ob_start();
		?><a href="<?= get_element_href('virtualtour',$element) ?>#tour"><img src="<?= STATIC_HOST ?>/images/inside.png" class="inside" /></a><?
		return ob_get_clean();
	}
	if (!$element) $element = $_REQUEST['sELEMENT'];
	if (!$id) $id = $_REQUEST['sID'];*/
	if (!($tourid = memcached_single('virtualtour', "
		SELECT TOURID
		FROM virtualtour
		WHERE ELEMENT = '$element'
		  AND ID = $id
		  AND VISIBLE = 1
		  AND AS_IMAGE = 1
		ORDER BY TOURID DESC
		LIMIT 1",
		HALF_HOUR))
	) {
		return $tourid;
	}
	ob_start();
	?><a href="<?= get_element_href('virtualtour', $tourid) ?>#tour"><?
	?><img<?
	?> alt="<? __('attrib:inside') ?>"<?
	?> src="<?= STATIC_HOST ?>/images/inside_hz<?= is_high_res() ?>.png"<?
	?> class="insidehz" /><?
	?></a><?
	return ob_get_clean();
}
function get_virtualtour_image(): string {
	ob_start();
	?><img<?
	?> alt="<? __('attrib:inside') ?>"<?
	?> src="<?= STATIC_HOST ?>/images/inside<?= is_high_res() ?>.png"<?
	?> class="inside" /><?
	return ob_get_clean();
}
