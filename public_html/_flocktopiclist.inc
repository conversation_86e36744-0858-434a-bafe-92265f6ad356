<?php

class flocktopiclist {
	public bool $show_header	= true;
	public bool $show_controls	= true;
	public bool $show_flock		= true;
	public bool $search			= false;

	public int $size			= 0;

	public array $wheres 		= [];
	public string $order		= 'LSTAMP';
	private int|string $page	= 0;
	private	array $tables		= array('flocktopic');

	function query() {
		if (have_user()) {
			require_once '_ignores.inc';
			require_once '_postedin.inc';
			$this->ignores = ignores(IGNORE_FLOCK_TOPICS);
		}
		if ($this->in_which_user_posted) {
			require_once '_pagecontrols.inc';
			$this->controls = new _pagecontrols();
			// FIXME: use flocktopics_per_page
			$this->controls->set_per_page(setting('TOPICS_PER_PAGE'));
			$this->controls->set_url('/'.$_REQUEST['sELEMENT'].'/'.$_REQUEST['ACTION'].'/'.$_REQUEST['subID']);
#			$this->total = db_single('flocktopic','SELECT COUNT(DISTINCT TOPICID) FROM flockmessage WHERE USERID='.$this->in_which_user_posted);
			if (false === ($total = db_single('postedinv3', "
				SELECT COUNT(*)
				FROM postedinv3
				WHERE ELEMENT = 'flocktopic'
				  AND USERID = $this->in_which_user_posted"))
			) {
				return;
			}
			$this->total = $total;
			$this->controls->set_total($this->total);
			$this->page = $this->controls->obtain_page();

			if (!empty($this->order_by_max_cstamp)) {
				$this->controls->set_order('MAX_CSTAMP');
				$topics = memcached_rowuse_array(['flock','flocktopic','postedinv3'], '
					SELECT	flocktopic.TOPICID,STICKY,SUBJECT,STATUS,flocktopic.USERID,postedinv3.LSTAMP AS MAX_CSTAMP,flocktopic.LSTAMP,LUSERID,MSGCNT,BADMSGCNT,flocktopic.FLAGS,
							postedinv3.LSTAMP AS CSTAMP,MSGNO,MESSAGEID,
							flock.FLOCKID,flock.NAME,REMOVED,PRIVATE
					FROM postedinv3
					JOIN flocktopic ON ID=TOPICID
					JOIN flock USING (FLOCKID)
					WHERE ELEMENT="flocktopic"
					  AND postedinv3.USERID='.$this->in_which_user_posted.
					$this->controls->order_and_limit()
				);
				if ($this->size = $topics ? count($topics) : 0) {
					number_reverse_sort($topics, 'MAX_CSTAMP');
				}
			} else {
				$this->controls->set_order($this->order);
				$topics = memcached_rowuse_array(['flock','flocktopic','flockmessage'], '
					SELECT	TOPICID,STICKY,SUBJECT,STATUS,flocktopic.USERID, LSTAMP,LUSERID,MSGCNT,BADMSGCNT,flocktopic.FLAGS,
							flock.FLOCKID, flock.NAME, REMOVED, PRIVATE
					FROM flockmessage
					JOIN flocktopic USING (TOPICID)
					JOIN flock USING (FLOCKID)
					WHERE flockmessage.USERID='.$this->in_which_user_posted.'
					GROUP BY TOPICID'.
					$this->controls->order_and_limit()
				);
				if ($this->size = $topics ? count($topics) : 0) {
					number_reverse_sort($topics, $this->order);
				}
			}
			if ($topics) {
				$this->add_topics($topics);
			}
		} elseif (isset($this->overview)) {
			$this->show_controls = false;
			$this->show_header = false;
			$this->infos = memcached_rowuse_array(
				array('flocksettings','flock','flocktopic','flockmember'),'
				SELECT	fm.FLOCKID,NAME,flocksettings.FLOCKID AS FLOCKSET,RECENTDIFF,SHOW_RECENT,
					(SELECT MAX(LSTAMP) FROM flocktopic AS ft WHERE ft.FLOCKID=fm.FLOCKID) AS LSTAMP
				FROM flockmember AS fm
				LEFT JOIN flocksettings USING (FLOCKID,USERID)
				JOIN flock ON flock.FLOCKID=fm.FLOCKID
				WHERE fm.USERID='.CURRENTUSERID.'
				  AND ACCEPTED=1
				  AND REMOVED=0
				ORDER BY NAME ASC',
				FIVE_MINUTES,
				'flockoverviewfor:'.CURRENTUSERID
			);
			if (!$this->infos) {
				return;
			}
			$maxdiff = 0;
			$diffpart = null;
			foreach ($this->infos as $info) {
				if ($info['FLOCKSET']
				&&	!$info['SHOW_RECENT']
				) {
					continue;
				}
				if (!$info['RECENTDIFF']) {
					$info['RECENTDIFF'] = 7*86400;
				}
				if ($info['LSTAMP'] < CURRENTSTAMP - $info['RECENTDIFF']) {
					continue;
				}
				$flockids[] = $info['FLOCKID'];
				$diffpart .= ' WHEN '.$info['FLOCKID'].' THEN '.$info['RECENTDIFF'];
				if ($info['RECENTDIFF'] > $maxdiff) {
					$maxdiff = $info['RECENTDIFF'];
				}
			}
			if (isset($flockids)) {
				$topics = memcached_rowuse_array($this->tables, '
					SELECT	flocktopic.FLOCKID,TOPICID,STICKY,SUBJECT,STATUS,flocktopic.USERID,flocktopic.CSTAMP,LSTAMP,LUSERID,MSGCNT,BADMSGCNT,flocktopic.FLAGS,
						flock.NAME,REMOVED,PRIVATE
					FROM flocktopic
					JOIN flock USING (FLOCKID)
					WHERE flocktopic.FLOCKID IN ('.implode(',',$flockids).')
					  AND LSTAMP>='.(CACHESTAMP - $maxdiff).
					(	!$diffpart
					?	null
					:	' AND LSTAMP>='.CACHESTAMP.'-CASE flocktopic.FLOCKID '.$diffpart.' ELSE '.(7*86400).' END'
					).'
					ORDER BY NAME ASC,LSTAMP DESC'
				);
				if ($topics) {
					$this->size += count($topics);
					$this->add_topics($topics, true);
				}
			}
		} else {
			if ($this->show_controls) {
				require_once '_pagecontrols.inc';
				$this->controls = new _pagecontrols;
				$this->controls->set_per_page(setting('TOPICS_PER_PAGE'));
	#			$this->controls->set_url('/flock/'.$_REQUEST['sID');
				$key = null;
				$this->total = memcached_single('flocktopic',
					isset($this->active)
				?	'SELECT COUNT(*) FROM flocktopic JOIN flock USING (FLOCKID)'.(empty($this->wheres) ? null : ' WHERE '.implode(' AND ',$this->wheres))
				:	'SELECT COUNT(*) FROM flocktopic'.(empty($this->wheres) ? null : ' WHERE '.implode(' AND ',$this->wheres)),
					0,$key,
					isset($this->search) ? DB_FORCE_SERVER : 0,
					isset($this->search) ? ['dbsrch','dbsrchrep'] : null
				);
				if ($this->total === false) return;
				$this->controls->set_total($this->total);
				$this->controls->set_order('LSTAMP');
				if (!isset($this->active)
				&&	$this->controls->obtain_page() == 'last'
				) {
					$stickies = memcached_rowuse_array(['flock','flocktopic'], '
						SELECT	TOPICID,STICKY,SUBJECT,STATUS,flocktopic.USERID,flocktopic.CSTAMP,LSTAMP,LUSERID,MSGCNT,BADMSGCNT,flocktopic.FLAGS,
							flock.FLOCKID,flock.NAME,REMOVED,PRIVATE
						FROM flocktopic
						JOIN flock USING (FLOCKID)
						WHERE STICKY=1
						  AND '.implode(' AND ',$this->wheres).'
						ORDER BY LSTAMP DESC',
						key:	$key,
						flags:	isset($this->search) ? DB_FORCE_SERVER : 0,
						arg:	isset($this->search) ? ['dbsrch','dbsrchrep'] : null
					);
					if ($this->size = $stickies ? count($stickies) : 0) {
						$this->add_topics($stickies);
						if (!empty($this->topicids)) {
							$this->wheres[] = 'TOPICID NOT IN ('.implode(',',$this->topicids).')';
						}
					}
				}
				$key = null;
				$topics = memcached_rowuse_array($this->tables, '
					SELECT	TOPICID,STICKY,SUBJECT,STATUS,flocktopic.USERID,flocktopic.CSTAMP,flocktopic.LSTAMP,flocktopic.LUSERID,MSGCNT,BADMSGCNT,flocktopic.FLAGS,
						flock.FLOCKID,flock.NAME,REMOVED,PRIVATE
					FROM flocktopic
					JOIN flock USING (FLOCKID)'.
					(empty($this->wheres) ? null :  ' WHERE '.implode(' AND ',$this->wheres)).
					$this->controls->order_and_limit(),
					key:	$key,
					flags:	isset($this->search) ? DB_FORCE_SERVER : 0,
					arg:	isset($this->search) ? ['dbsrch','dbsrchrep'] : null
				);
				if ($topics) {
					$this->size += count($topics);
					number_reverse_sort($topics, 'LSTAMP');
					$this->add_topics($topics);
				}
			} else {
				$topics = memcached_rowuse_array($this->tables, '
					SELECT '.(isset($this->need_distinct) ? 'DISTINCT ' : null).'
						flocktopic.TOPICID,STICKY,SUBJECT,STATUS,flocktopic.USERID,flocktopic.CSTAMP,flocktopic.LSTAMP,flocktopic.LUSERID,MSGCNT,BADMSGCNT,flocktopic.FLAGS,
						flocktopic.FLOCKID,flock.NAME,REMOVED,PRIVATE
					FROM flocktopic
					'.(!empty($this->joins) ? implode(' ',$this->joins) : null).'
					LEFT JOIN flock ON flock.FLOCKID=flocktopic.FLOCKID'.
					(!empty($this->wheres) ? ' WHERE '.implode(' AND ',$this->wheres) : null).'
					ORDER BY flocktopic.LSTAMP DESC'.
					(isset($this->limit) ? ' LIMIT '.$this->limit : null)
				);
				if ($this->size = $topics ? count($topics) : 0) {
					$this->add_topics($topics);
				}
			}
		}
		if (empty($this->topics)) {
			return true;
		}
		if (!empty($this->userids)) {
			memcached_prefetch_users($this->userids);
		}
		if (have_user()) {
			$topicidstr = implode(',',$this->topicids);
			$this->selfpost = get_self_posts('flocktopic', $topicidstr);
			if ($this->selfpost === false)return false;
			if ((require_once '_settings.inc')
			&&	setting_isset(SHOW_TOPIC_HEARTS)
			&&	!isset($this->hide_buddies)
			&&	have_buddies()
			) {
				require_once '_hearts.inc';
				$this->hearts = new hearts('flockmessage', $topicidstr);
			}
		}
		$this->ok = true;
		return true;
	}
	function size() {
		return $this->size;
	}
	function have_rows() {
		return !empty($this->topics);
	}
	function add_topics($topics, $groupflocks = false) {
		foreach ($topics as $topic) {
			if (isset($this->ignores[$topic['USERID']])) {
				continue;
			}
			$this->topicids[] = $topic['TOPICID'];
			if (is_number($topic['USERID'])) {
				$this->userids[$topic['USERID']] = true;
			} else {
				error_log('USERID not a number ('.$topic['USERID'].') at '.$_SERVER['REQUEST_URI'],0);
			}
			if (is_number($topic['LUSERID'])) {
				$this->userids[$topic['LUSERID']] = true;
			} else {
				error_log('LUSERID not a number ('.$topic['LUSERID'].') at '.$_SERVER['REQUEST_URI'],0);
			}
			if ($groupflocks) {
				$this->topics[$topic['FLOCKID']][] = $topic;
			} else {
				$this->topics[] = $topic;
			}
		}
	}
	function display() {
		if (!isset($this->overview)) {
			if (empty($this->ok)) {
				return;
			}
			if (empty($this->topics)) {
				return;
			}
			if (isset($this->controls)) {
				$this->controls->display();
			}
		}
		layout_open_table('fw regular forum');
		if ($this->show_header) {
			layout_start_header_row();
			layout_header_cell(Eelement_name('flocktopic'));
			if ($this->show_flock) {
				layout_header_cell(Eelement_name('flock'));
			}
			if (!SMALL_SCREEN) {
				layout_header_cell_right('#');
				layout_header_cell_right(Eelement_name('opener'));
				layout_header_cell_right(__C('field:last'));
			}
			layout_header_cell_right(Eelement_name('time'));
			layout_stop_header_row();
		}
		if (isset($this->overview)) {
			if ($this->infos) foreach ($this->infos as $info) {
				if (isset($this->topics[$info['FLOCKID']])) {
					$cnt = count($this->topics[$info['FLOCKID']]);
				} else {
					$cnt = 0;
				}
				?><tr><th colspan="6"><table class="split"><tr><td class="left bold"><?= get_element_link('flock',$info['FLOCKID'],$info['NAME']);
				?></td><td style="width: 10%;" class="right"><?
				if ($cnt) {
					echo $cnt; ?> recent<?
				}
				?></td><td style="width: 30%;" class="right nowrap"><?
				if ($info['LSTAMP']) {
					_date_printnice(PRINTNICE_DATE_DAY_TIME,$info['LSTAMP']);
				} else {
					echo __('date:never');
				}
				?></td></tr></table></th></tr><?
				if ($cnt) {
					require_once '_topicrow.inc';
					foreach ($this->topics[$info['FLOCKID']] as $topic) {
						$this->show_topic($topic);
					}
				}
			}
		} else {
			require_once '_topicrow.inc';
			foreach ($this->topics as $topic) {
				$this->show_topic($topic);
			}
		}
		layout_close_table();
		if (!isset($this->overview)) {
			if ($this->show_controls) {
				$this->controls->display();
			}
		}
	}

	function show_topic($topic) {
		if ((	$topic['STATUS'] === 'hidden'
			||	$topic['REMOVED']
			)
		&&	empty($this->show_remove)
		&&	!have_admin('flocktopic')
		) {
			return;
		}
		if ($topic['PRIVATE']
		&&	!have_admin(array('flock','flocktopic'))
		&&	!have_flock_member($topic['FLOCKID'])
		) {
			return;
		}
		$rowclass = topicrow_get_class($topic,isset($this->selfpost) ? $this->selfpost : null);
		?><tr<?
		if ($topic['STATUS'] == 'hidden') {
			?> class="light"<?
		}
		?>><td<?
		if ($rowclass) {
			?> class="<?= $rowclass; ?>"<?
		}
		?>><?
/*		if ($topic['REMOVED']
		&&	!empty($this->show_remove)
		) {
			?><a class="light right-float" href="/flock/unsubscribetopic/<?= $topic['TOPICID'];
			?>"><?= __('action:remove'); ?></a><?
		}*/
		if ($flags = $topic['FLAGS']) {
			require_once '_topictypes.inc';
			if ($prefix = get_topic_prefix('flocktopic',$topic['TOPICID'],$flags)) {
				echo $prefix; ?>: <?
			}
		}
		if ($topic['STATUS'] == 'closed') {
			?><img src="<?= STATIC_HOST; ?>/images/lock<?= is_high_res() ?>.png" class="icon lower" title="(<?= __('status:closed') ?>)" /> <?
		}
		?><a href="<?= $href = get_element_href('flocktopic',$topic['TOPICID'],$topic['SUBJECT']);
		?>" title="<?= __C('topiclist:topic_started_on', ['DATETIME' => _datetime_get($topic['CSTAMP'])]);
		?>"><?= flat_with_entities($topic['SUBJECT'],UBB_STATIC_SMILEYS,'flocktopic',$topic['TOPICID']);
		?></a><?

		if (!empty($this->hearts)) {
			$this->hearts->show($topic['TOPICID']);
		}

		if ($fetch
		=	isset($topic['MSGNO'])
		?	1
		:	isset($this->selfpost[$topic['TOPICID']])
		) {
			if ($fetch === true) {
				[$topic['MSGNO'], $topic['MESSAGEID']] = keyval($this->selfpost[$topic['TOPICID']]);
			}
			if ($topic['MSGNO']
			&&	$topic['MESSAGEID']
			) {
				static $topicpagesize = null;
				if ($topicpagesize === null) {
					$topicpagesize = setting('MESSAGES_PER_TOPIC');
				}
				?> <a<?
				?> href="<?
				if ($topic['MSGNO'] === 1) {
					echo get_element_href('flocktopic',$topic['TOPICID'],$topic['SUBJECT']);
				} elseif ($topic['MSGNO'] >= $topic['MSGCNT'] - $topicpagesize) {
					echo get_element_href('flocktopic',$topic['TOPICID'],$topic['SUBJECT']).'#m'.$topic['MESSAGEID'];
				} else {
					?>/flocktopic/<?= $topic['TOPICID']; ?>/page/<?= ceil($topic['MSGNO'] / $topicpagesize); ?>#m<? echo $topic['MESSAGEID'];
				}
				// &sim;
				?>"><?= get_user_indicator_icon() ?></a><?
			}
		}

		?> <a class="light" href="<?= $href; ?>#bottom"><?= get_to_end_icon() ?></a><?
		if ($this->show_flock) {
			layout_next_cell(class: 'flock-cell nowrap'.($topic['REMOVED'] ? ' unavailable' : ''));
			echo get_element_link('flock', $topic['FLOCKID'], $topic['NAME']);
		}

		if (!SMALL_SCREEN) {
			layout_next_cell(class: 'msgcnt-cell right');
			echo $topic['MSGCNT'] - $topic['BADMSGCNT'];

			layout_next_cell(class: 'user-cell right');
			require_once 'defines/topic.inc';
			echo	($topic['FLAGS'] & TOPIC_IS_ANONYMOUS)
			?	'<span class="light">'.(
					$topic['USERID'] === CURRENTUSERID
				||	have_admin('flocktopic')
				?	get_element_link('user', CURRENTUSERID)
				:	__('field:anonymous')
				).'</span>'
			:	get_element_link('user', $topic['USERID'], title_attr: true);
			?></td><?

			layout_next_cell(class: 'user-cell right');
			require_once 'defines/post.inc';
			$last_message_flags = db_single('flockmessage','
				SELECT FLAGS
				FROM flockmessage
				WHERE TOPICID = '.$topic['TOPICID'].'
				ORDER BY MESSAGEID DESC
				LIMIT 1'
			);
			echo	(	in_array($last_message_flags, [null, false], true)
				||	($last_message_flags & POST_IS_ANONYMOUS)
				)
			?	'<span class="light">'.(
					$topic['LUSERID'] === CURRENTUSERID
				||	have_admin('flocktopic')
				?	get_element_link('user', CURRENTUSERID)
				:	__('field:anonymous')
				).'</span>'
			:	get_element_link('user', $topic['LUSERID'], title_attr: true);
		}
		if (!empty($this->order_by_max_cstamp)) {
			$stamp = 'MAX_CSTAMP';
		} else {
			$stamp = 'LSTAMP';
		}
		?><td class="right date-cell" title="<? _datedaytime_display($topic[$stamp]); ?>"><?
		show_short_diff($topic[$stamp]);
		layout_stop_row();
	}
	function in_flock($flockid) {
		$this->wheres[] = 'flocktopic.FLOCKID='.$flockid;
	}
/*	function subscribed_by($userid) {
		$this->tables[] = 'subscription';
		$this->joins[] = 'JOIN subscription AS sub ON sub.ID=flocktopic.TOPICID AND sub.ELEMENT="flocktopic"';
		$this->wheres[] = 'sub.USERID='.$userid;
		$this->show_controls = false;
	}*/
	function interecent($userid) {
		$this->tables[] = 'flockmember';
		$this->joins[] = 'JOIN flockmember USING (FLOCKID)';
		$this->wheres[] = 'flockmember.USERID='.$userid;
		$this->limit = 100;
		$this->show_controls = false;
	}
	function opened_by($userid) {
		$this->order = 'TOPICID';
		$this->wheres[] = 'flocktopic.USERID='.$userid;
	}
	function in_which_user_posted(int $userid): void {
		$this->in_which_user_posted = $userid;
	}
	function order_by_max_cstamp(): void {
		$this->order_by_max_cstamp = true;
	}
	function order_reverse_lstamp() {
		$this->order = 'LSTAMP';
	}
	function active() {
#		$this->limit = 100;
		$this->active = true;
		$this->show_controls = true;
		$this->need_distinct = true;
		$ors[] = 'NOT PRIVATE';
		if (have_user()) {
			$flockids = memcached_simpler_array('flockmember','SELECT FLOCKID FROM flockmember WHERE USERID='.CURRENTUSERID);
			if ($flockids) {
				$ors[] = 'FLOCKID IN ('.implode(',',$flockids).')';
			}
		}
		$this->wheres[] = 'REMOVED = 0 AND ('.implode(' OR ',$ors).')';
	}
}
