<?php

declare(strict_types=1);

function answeringmachine_get(int $userid): array|false|null {
	$ndx = '__answer'.$userid;
	if (isset($GLOBALS[$ndx])) {
		print_rr($GLOBALS[$ndx], 'hmm');
		return $GLOBALS[$ndx];
	}
	$GLOBALS[$ndx] = memcached_single_assoc('answeringmachine','
		SELECT PHOTO, BODY, MSTAMP, ACTIVE, ACCEPTED
		FROM answeringmachine
		WHERE USERID='.$userid
	);
	if (!$GLOBALS[$ndx]) {
		return null;
	}
	$GLOBALS[$ndx]['USERID'] = $userid;
	if ($GLOBALS[$ndx]['ACTIVE']) {
		$color = memcached_color($userid);
		$GLOBALS[$ndx]['COLOR'] = $color ?: 'red';
	}
	return $GLOBALS[$ndx];
}

function answeringmachine_show(int $userid, ?array $answer = null): ?array {
	if (!$answer) {
		$answer = answeringmachine_get($userid);
		if (!$answer) {
			return null;
		}
	}
	if (!$answer['ACTIVE']) {
		return $answer;
	}
	if (!$answer['ACCEPTED']
	&&	CURRENTUSERID !== $userid
	&&	!have_admin('answeringmachine')
	) {
		return null;
	}
	if (!isset($answer['COLOR'])) {
		$answer['COLOR'] = memcached_color($userid);
		if (!$answer['COLOR']) {
			$answer['COLOR'] = 'red';
		}
	}
	layout_open_box('white'.(!$answer['ACCEPTED'] ? ' unaccepted-element' : null));
	if ($answer['PHOTO']) {
		?><table class="split vtop"><?
		?><tr><td class="left" style="width: 110px; padding-bottom: .5em;"><?= _profileimage_get($userid) ?></td><?
		?><td class="left"><?
	}
	if ($userid !== CURRENTUSERID) {
		?><span class="r"><?
		layout_display_offense_link($userid,'answeringmachine', $userid);
		layout_display_contacts('answeringmachine', $userid);
		?></span><?
	}
	?><div class="block"><?=
		make_all_html($answer['BODY'], UBB_UTF8, 'answeringmachine', $userid)
	?></div><?
	if ($answer['PHOTO']) {
		?></td></tr></table><?
	}
	?><div class="note"><span><?
	if (!$answer['ACCEPTED']) {
		?><span class="invalid">(<?= __('field:unaccepted'); ?>)</span> <?
	}
	echo __('alteration:lastchange', ['DATETIME' => _datedaytime_get($answer['MSTAMP'])]);
	?></span></div><?
	layout_close_box();

	return $answer;
}
