<?php

declare(strict_types=1);

function show_element_to_ticket_menu(string $element, int $id, array $item): void {
	if ($item['TICKETID']
	&&	$item['CSTAMP'] > CURRENTSTAMP - 10 * ONE_DAY
	&&	(	$item['TICKET_STATUS']
		??	db_single('contact_ticket', "SELECT STATUS FROM contact_ticket WHERE TICKETID = {$item['TICKETID']}")
		) !== 'closed'
	&&	in_array($element, ['location', 'organization', 'artist', 'contest', 'party'], true)
	) {
		layout_open_menu();
		layout_menuitem(
			__C('action:make_confirmation'),
			"/ticket/{$item['TICKETID']}?USETEMPLATE=creation;ELEMENT=$element;ID=$id"
		);
		layout_close_menu();
	} elseif (
		!isset($item['USERID'])
	||	$item['USERID'] !== CURRENTUSERID
	) {
		$to_userid = 0;
		$to_email = '';
		$to_name = '';
		foreach (['CUSERID', 'USERID'] as $field) {
			if (isset($item[$field])
			&&	$item[$field] > 1
			) {
				$to_userid = $item[$field];
				break;
			}
		}
		if (!$to_userid
		&&	($info = db_single_assoc(['allowinquirymail', 'email_salute', 'user'], "
				SELECT	EMAIL, user.USERID,
						IF(user.USERID, REALNAME, email_salute.NAME) AS NAME
				FROM allowinquirymail
				LEFT JOIN email_salute USING (EMAIL)
				LEFT JOIN user USING (EMAIL)
				WHERE ELEMENT = '$element'
				  AND ID = $id
				ORDER BY email_salute.USERID = ".CURRENTUSERID.' DESC
				LIMIT 1'))
		) {
			if ($info['USERID']) {
				$to_userid = $info['USERID'];
			} elseif ($info['EMAIL']) {
				$to_email = $info['EMAIL'];
			}
		}
		layout_open_menu();
		if ($to_userid) {
			echo __('alteration:added_by', DO_UBB, ['USERID' => $to_userid]) ?>: <?
		} elseif (!empty($info)) {
			ob_start();
			print_email_link(
				$info['EMAIL'],
				$info['NAME'],
				element:	$element,
				id:			$id,
				userid:		$info['USERID'] ?? null,
			);
			echo str_replace('%EMAIL_LINK%', ob_get_clean(), __('alteration:added_by_email'));
		}
		layout_menuitem(
			__C('action:confirm_or_inquire'),
			"/ticket/outgoing-form?ELEMENT=$element;ID=$id;CONFIRM_OR_INQUIRE".
				($to_userid ? ';TO_USERID='.$to_userid	: '').
				($to_name 	? ';TO_NAME='. urlencode($to_name)	: '').
				($to_email	? ';TO_EMAIL='.urlencode($to_email)	: '')
		);
		layout_close_menu();
	}
}
