<?php

/** @noinspection EmptyClassInspection */

declare(strict_types=1);

enum http_status: int {
	case SWITCHING_PROTOCOLS			= 101;
	case OK								= 200;
	case CREATED						= 201;
	case ACCEPTED						= 202;
	case NON_AUTHORITATIVE_INFORMATION	= 203;
	case NO_CONTENT						= 204;
	case RESET_CONTENT					= 205;
	case PARTIAL_CONTENT				= 206;
	case MULTIPLE_CHOICES				= 300;
	case MOVED_PERMANENTLY				= 301;
	case MOVED_TEMPORARILY				= 302;
	case SEE_OTHER						= 303;
	case NOT_MODIFIED					= 304;
	case USE_PROXY						= 305;
	case TEMPORARY_REDIRECT				= 307;
	case PERMANENT_REDIRECT				= 308;
	case BAD_REQUEST					= 400;
	case UNAUTHORIZED					= 401;
	case PAYMENT_REQUIRED				= 402;
	case FORBIDDEN						= 403;
	case NOT_FOUND						= 404;
	case METHOD_NOT_ALLOWED				= 405;
	case NOT_ACCEPTABLE					= 406;
	case PROXY_AUTHENTICATION_REQUIRED	= 407;
	case REQUEST_TIMEOUT				= 408;
	case CONFLICT						= 409;
	case GONE							= 410;
	case LENGTH_REQUIRED				= 411;
	case PRECONDITION_FAILED 			= 412;
	case REQUEST_ENTITY_TOO_LARGE		= 413;
	case REQUEST_URI_TOO_LARGE			= 414;
	case UNSUPPORTED_MEDIA_TYPE			= 415;
	case REQUESTED_RANGE_NOT_SATISFIABLE= 416;
	case EXPECTATION_FAILED				= 417;
	case IM_A_TEAPOT					= 418;
 	case ENHANCE_YOUR_CALM				= 420;
 	case MISDIRECTED_REQUEST			= 421;
	case UNPROCESSABLE_CONTENT			= 422; # WebDAV
	case LOCKED							= 423; # WebDAV
	case FAILED_DEPENDENCY				= 424; # WebDAV
	case TOO_EARLY						= 425;
	case UPGRADE_REQUIRED				= 426;
	case PRECONDITION_REQUIRED			= 428;
	case TOO_MANY_REQUESTS				= 429;
	case REQUEST_HEADER_FIELDS_TOO_LARGE= 431;
	case APPLICATION_INACTIVE			= 439;
	case UNAVAILABLE_FOR_LEGAL_REASONS	= 451;
	case INTERNAL_SERVER_ERROR			= 500;
	case NOT_IMPLEMENTED				= 501;
	case BAD_GATEWAY					= 502;
	case SERVICE_UNAVAILABLE			= 503;
	case GATEWAY_TIMEOUT				= 504;
	case HTTP_VERSION_NOT_SUPPORTED		= 505;
	case VARIANT_ALSO_NEGOTIATES		= 506;
	case INSUFFICIENT_STORAGE			= 507; # WebDAV
	case LOOP_DETECTED					= 508;
	case NOT_EXTENDED					= 510;
	case NETWORK_AUTHENTICATION_REQUIRED= 511;
}

function get_http_status(int $status, bool $caps = false): string {
	static $__http_status = [];
	return $__http_status[$caps ? 1 : 0][$status] ??= __('http_status:'.$status, $caps ? DO_CAPFIRST : 0);
}
function redirect(string $url, http_status $status, bool $print_link = true, ?string $forced_host = null): void {
	if ($forced_host) {
		$url = "https://$forced_host$url";
	} elseif (!$url) {
		$url = "https://{$_SERVER['HTTP_HOST']}";
	} elseif ($url[0] === '/') {
		if (isset($url[1]) && $url[1] === '/') {
			$url = "https:$url";
		} else {
			$url = "https://{$_SERVER['HTTP_HOST']}$url";
		}
	}
	header('Cache-Control: no-cache,no-store,max-age=0');
	header('Location: '.$url, true, $status->value);
	if ($print_link) {
		header('Content-Type: text/html; charset=us-ascii');
		?><a href="<?= $escaped = escape_specials($url) ?>"><?= $escaped ?></a><?
	}
}
function moved_permanently(string $url, bool $print_link = true, ?string $forced_host = null): never {
	redirect(
		$url,
			SERVER_SANDBOX || HOME_THOMAS
		?	http_status::SEE_OTHER
		:	http_status::MOVED_PERMANENTLY,
		$print_link,
		$forced_host
	);
	exit;
}
function see_other(string $url, bool $print_link = true): never {
	redirect($url, http_status::SEE_OTHER, $print_link);
	exit;
}
function moved_temporarily(string $url, bool $print_link = true): never {
	redirect($url, http_status::MOVED_TEMPORARILY, $print_link);
	exit;
}
function send_not_modified(): never {
	http_response_code(http_status::NOT_MODIFIED->value);
	exit;
}
