<?php

function show_cameracontacts($element) {
	$parts = '';

	if ($element['CAMREQ_NAME']
	||	$element['CAMREQ_EMAIL']
	||	$element['CAMREQ_USERID']
	) {
		ob_start();
		?><div class="block"><?
		?><img class="cam baseline" src="<?= STATIC_HOST; ?>/images/cam<?= is_high_res() ?>.png" /> <?
		?><b><?= __C('cameracontacts:header:present'); ?></b>:<?
		?></div><?
		?><ul><li><?
		if ($element['CAMREQ_EMAIL'] || $element['CAMREQ_NAME']) {
			print_email_link($element['CAMREQ_EMAIL'],$element['CAMREQ_NAME'], PRINT_EMAIL_IMAGE_AFTER | PRINT_EMAIL_TICKET);
			if ($element['CAMREQ_USERID']) {
				?><br /><?
			}
		}
		if ($element['CAMREQ_USERID']) {
			echo get_element_link('user',$element['CAMREQ_USERID']);
		}
		?></li></ul><?
		$now = true;
		$parts .= ob_get_clean();
	}
	// get previous camreq contacts
	$oldcontacts = memcached_rowuse_array(
		$_REQUEST['sELEMENT'].'_log', '
		SELECT DISTINCT CAMREQ_NAME,CAMREQ_EMAIL,CAMREQ_USERID
		FROM '.$_REQUEST['sELEMENT'].'_log
		WHERE '.strtoupper($_REQUEST['sELEMENT']).'ID='.$_REQUEST['sID'].'
		  AND (	CAMREQ_NAME != ""
			OR	CAMREQ_EMAIL != ""
			OR	CAMREQ_USERID != 0
		  )
		  AND (	CAMREQ_NAME != "'.addslashes($element['CAMREQ_NAME']).'"
			OR	CAMREQ_EMAIL != "'.addslashes($element['CAMREQ_EMAIL']).'"
			OR	CAMREQ_USERID != '.$element['CAMREQ_USERID'].'
		)
		ORDER BY MSTAMP DESC',
		TEN_MINUTES
	);
	if ($oldcontacts) {
		ob_start();
		?><div class="light"><?
		?><div class="block"><img class="cam baseline" src="<?= STATIC_HOST; ?>/images/cam<?= is_high_res() ?>.png" /> <?
		?><b><?= __C('cameracontacts:header:past', ['CNT'=> count($oldcontacts)]); ?></b>:</div><ul><?
		foreach ($oldcontacts as $contact) {
			?><li><?
			if ($contact['CAMREQ_EMAIL'] || $contact['CAMREQ_NAME']) {
				print_email_link($contact['CAMREQ_EMAIL'],$contact['CAMREQ_NAME'], PRINT_EMAIL_IMAGE_AFTER | PRINT_EMAIL_TICKET);
				if ($contact['CAMREQ_USERID']) {
					?><br /><?
				}
			}
			if ($contact['CAMREQ_USERID']) {
				echo get_element_link('user',$contact['CAMREQ_USERID']);
			}
			?></li><?
		}
		?></ul><?
		?></div><?
		$parts .= ob_get_clean();
	}
	if (!$parts) {
		return;
	}
	layout_open_box('white');

	$total = (isset($now) ? 1 : 0) + count($oldcontacts);

	expandable_header('<div class="block bold">'.__C('cameracontacts:header:present').' '.MIDDLE_DOT_ENTITY.' '.$total.'</div>','camreqcontacts');
	?><div class="hidden" id="camreqcontacts"><?
	echo $parts;
	?></div><?
	layout_close_box();
}
function show_camreq_form_part($item) {
	layout_start_spanned_row(2);
		?><b><?= Eelement_plural_name('camerarequest'); ?></b><?
	layout_restart_row();
		echo Eelement_name('name');
		layout_field_value();
		?><input maxlength="80" type="text" name="CAMREQ_NAME" value="<?= escape_specials($item['CAMREQ_NAME'] ?? '') ?>" /><?
	layout_restart_row();
		echo Eelement_name('email');
		layout_field_value();
		?><input maxlength="80" type="email" data-valid="working-email" name="CAMREQ_EMAIL" value="<?= escape_specials($item['CAMREQ_EMAIL'] ?? '') ?>" /><?
	layout_restart_row();
		echo Eelement_name('user');
		layout_field_value('nowrap');
		require_once '_fillelementid.inc';
		show_elementid_input('user', $item['CAMREQ_USERID'] ?? null, [
			'name' => 'CAMREQ_USERID'
		]);
	layout_stop_row();
}
