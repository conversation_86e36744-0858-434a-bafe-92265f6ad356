<?php

require_once '_flockmod.inc';

# FIXME: there are two ways to indicate last page, page==0 and page==L

class _pagecontrols {
	protected int				$total				= 0;
	protected int				$perpage			= 30; // sensible default
	protected int				$pages				= 0;
	protected int|string		$page				= 0;
	protected bool				$is_admin			= false;
	protected string|null		$url				= null;
	protected string			$hash				= 'page-controls';
	protected string			$bottomhash			= 'page-controls-bottom';

	protected bool				$include_last		= true;
	protected bool				$include_all		= false;
	protected bool				$hide_jump			= false;
	protected bool				$show_all_pages		= false;
	protected bool				$show_prev_next		= true;
	protected int				$absolute_limit		= 1000;
	protected string|false|null	$newer				= null;
	protected string|false|null	$older				= null;
	protected bool				$start_at_one		= false;

	private int				$remainder;
	private ?string			$extension;
	private string			$fieldname;
	private string			$all_name;
	private string			$last_name;
	private string			$default_page;
	private string			$default_url;
	private bool			$first_inverted;
	private ?string			$pagepart;
	private ?string			$pagepost;
	private array			$orders;
	private string			$order;
	private string			$stored;
	private string			$mostrecent_url;

	public bool			$show_pages = true;

	public function __construct() {
		$this->extension = null;
		$this->fieldname = 'page';
		$this->last_name = 'last';
		$this->all_name = 'all';
		$this->default_page = $this->last_name;
	}

	public function set_jump_names(string|false|null $older = null, string|false|null $newer = null): void {
		$this->newer = $newer;
		$this->older = $older;
	}

	public function is_on_last_page(int $offset): bool {
		if ($this->page === 'last'
		||	$this->total <= $this->perpage
		) {
			return true;
		}
		if ($this->page === $this->pages) {
			return $offset <= ($this->perpage - $this->remainder);
		}
		return false;
	}

	// INPUT

	public function show_prev_next(bool $prev_next = true): void {
		$this->show_prev_next = true;
	}

	public function hide_jump(bool $hide_jump = true): void {
		$this->hide_jump = $hide_jump;
	}

	public function show_all_pages(bool $all = true): void {
		$this->show_all_pages = $all;
	}

	public function include_last(bool $last = true): void {
		$this->include_last = $last;
	}

	public function absolute_limit(int $limit): void {
		$this->absolute_limit = $limit;
	}

	public function include_all(bool $include_all = true): void {
		$this->include_all = $include_all;
	}

	public function start_at_one($at_one = true): void {
		$this->start_at_one = true;
	}

	public function page_one_is_first(bool $pone): void {
		$this->page_one_is_first = $pone;
	}

	public function set_per_page($perpage) {
		if (!$perpage) {
			$perpage = 30;
		}
		$this->perpage = $perpage;
	}

	public function set_total(int $total): void {
		$this->total = $total;
	}

	protected function get_default_url(): string {
		$this->get_url();
		return $this->default_url ?: $this->url;
	}

	protected function get_url() {
		if (!$this->url) {
			$this->set_url(preg_replace('"((?:/'.$this->fieldname.'/\d+)?$|[&?]'.$this->fieldname.'=\d+|:.*$)"i','',$_SERVER['REQUEST_URI']));
		}
		return $this->url;
	}

	public function set_url(string $url, string $default_url = ''): void {
		if (!$this->extension) {
			$url = rtrim($url, '/');
		}
		$this->url			= $url;
		$this->default_url	= $default_url;

		if (str_contains($this->url, '&')
		||	str_contains($this->url, '?')
		) {
			$this->pagepart = '&'.strtoupper($this->fieldname).'=';
			$this->pagepost = '';
		} else {
			$this->pagepart = '/'.$this->fieldname.'/';
			$this->pagepost = $this->extension;
		}
	}

	public function clear_hash(): void {
		$this->hash = '';
	}

	public function set_hash(string $hash = ''): void {
		$this->hash = $hash;
	}

	public function clear_bottom_hash(): void {
		$this->bottomhash = '';
	}

	public function set_bottom_hash(string $hash = ''): void {
		$this->bottomhash = $hash;
	}

	public function set_default_page(int|string $default_page): void  {
		$this->default_page = $default_page;
	}

	// OUTPUT

	public function is_last(): bool {
		return $this->page === $this->last_name;
	}

	public function pages(): int {
		return $this->pages;
	}

	protected static function get_explosion(string $str): array {
		if (!str_contains($str, ',')) {
			return [$str];
		}
		if (!str_contains($str, 'IF(')) {
			return explode(',', $str);
		}
		$reps = [];
		$result = [];
		$i = 0;
		$str = preg_replace_callback('"IF\((.*?)\)"',function($match) use (&$reps,&$i) {
				$reps[++$i] = $match[1];
				return "\x01$i\x02";
			},
			$str
		);
		foreach (explode(',', $str) as $part) {
			$result[] =
				!str_contains($part, "\x01")
			?	$part
			:	preg_replace_callback("'\x01(\d+)\x02'", function(array $match) use ($reps) {
					return 'IF('.$reps[$match[1]].')';
				},
				$part
			);
		}
		return $result;
	}

	public function set_order(string|array $orders): void {
		if (is_array($orders)) {
			$this->orders = [];
			foreach ($orders as $ndx => $order) {
				if (is_bool($order)) {
					$this->orders[] = $ndx;
				} else {
					$this->orders = [...$this->orders, ...static::get_explosion($order)];
				}
			}
		} else {
			$this->orders = static::get_explosion($orders);
			if (!isset($this->orders[1])) {
				$this->order = $this->orders[0];
			}
		}
		$this->first_inverted = str_contains($this->orders[0] ?? $this->order,' DESC');
	}
	/*public function dump() {
		ob_start();
		print_r($this);
		print_rr(ob_get_clean());
	}*/
	/*public function jump_if_nonexistent_page(): void {
		if (($current_page = $this->obtain_page())
		&&	!is_int($current_page)
		) {
			return;
		}
		error_log('jump if nonexistent page '.$current_page.' of '.$this->pages.' @ '.$_SERVER['REQUEST_URI']);
		if ($current_page > $this->pages) {
			robot_action('noindex');
			error_log('jumping to '.$this->get_default_url());
			#see_other($this->get_default_url());
			exit;
		}
	}*/
	public function order_and_limit(): string {
		#$this->jump_if_nonexistent_page();
		if (empty($this->order)
		&&	empty($this->orders)
		) {
			error_log('no order @ '.$_SERVER['REQUEST_URI']);
			mail_log('pagecontrols->order_and_limit(): no order was supplied',get_defined_vars());
			return ' LIMIT 0 /* no order supplied */ ';
		}
		if ($this->total <= $this->perpage) {
			return ' ORDER BY '.$this->get_orders(!$this->first_is_last());
		}
		if ($this->page === 0) {
			$this->page = $this->obtain_page();
		}
		if ($this->page === false) {
			mail_log('pagecontrols->order_and_limit(): page is false', get_defined_vars());
			robot_action('noindex');
			register_error('page_controls:error:page_is_false_LINE');
			return ' LIMIT 0 /* page is false */ ';
		}
		switch ($this->page) {
		case $this->all_name:
			if ($this->total <= $this->absolute_limit) {
				return '';
			}
			register_error('page_controls:error:all_page_exceed_absolute_limit_LINE', ['LIMIT' => $this->absolute_limit]);
			return ' LIMIT 0 /* don\'t do all */ ';

		case $this->last_name:
			return ' ORDER BY '.$this->get_orders(false)." LIMIT $this->perpage ";

		default:
			if (!$this->first_is_last()) {
				# ascending order limit
				$ascending_order = $this->get_orders(true);
				if ($this->page === 1) {
					return " ORDER BY $ascending_order
							 LIMIT $this->perpage";
				}
				return " ORDER BY $ascending_order
						 LIMIT ".(($this->page - 1) * $this->perpage).','.$this->perpage;
			}
			# descending order limit
			$descending_order = $this->get_orders(false);
			if ($this->pages === $this->page) {
				return " ORDER BY $descending_order
						 LIMIT ".($this->remainder ?: $this->perpage);
			}
			return " ORDER BY $descending_order
					 LIMIT ".(
					 $this->remainder
				?	($this->remainder + ($this->pages - $this->page - 1) * $this->perpage)
				:	($this->pages - $this->page) * $this->perpage).','.$this->perpage;
		}
	}

	function first_is_last(): bool {
		return	$this->page === $this->last_name
			||	$this->page !== $this->all_name
			&&	$this->total > $this->perpage
			&&	$this->page >= ($this->pages / 2);
	}

	protected function get_orders(bool $asc): string {
		$first_postfix = ($asc xor !empty($this->first_inverted)) ? ' ASC' : ' DESC';
		if (!isset($this->orders)) {
			return preg_replace('"\s+(?:ASC|DESC)"','',$this->order).$first_postfix;
		}
		$first = true;
		foreach ($this->orders as $order) {
			if ($first) {
				$first = false;
				$result[] = preg_replace('"\s+(?:ASC|DESC)"','',$order).$first_postfix;
				continue;
			}
			if ($asc) {
				$result[] = $order;
				continue;
			}
			$neworder = preg_replace_callback(
				'"\s+(ASC|DESC)"',
				static fn(array $match): string => $match[1] === 'ASC' ? ' DESC' : ' ASC',
				$order,
				-1,
				$count
			);
			$result[] = $count ? $neworder : $order.' DESC';
		}
		return implode(',', $result);
	}

	// public function first_page(): ?int {
	// 	return match ($this->page) {
	// 		$this->all_name	 =>	null,
	// 		$this->last_name => $this->total - $this->perpage,
	// 		default			 => ($this->page - 1) * $this->perpage,
	// 	};
	// }

	// public function per_page(): int {
	// 	return $this->perpage;
	// }

	public function obtain_page(): int|string {
		if ($this->page) {
			return $this->page;
		}
		if (!$this->pages) {
			$this->pages = (int)ceil($this->total / $this->perpage);
		}
		# remainder contains the amount of items on the last page
		$this->remainder = $this->total % $this->perpage;
		if (!($page = $_REQUEST['PAGE'] ?? $_REQUEST['page'] ?? null)) {
			if (!$this->default_page) {
				return $this->page = $this->pages;
			}
			return $this->page = $this->default_page;
		}
		switch ($page) {
		case $this->all_name:
			if (!$this->include_all) {
				return $this->page = $this->pages;
			}
			return $this->page = $this->all_name;

		case $this->last_name:
			if (!$this->include_last) {
				return $this->page = $this->pages;
			}
			return $this->last_name;

		default:
			/** @noinspection NotOptimalIfConditionsInspection */
			if (!(is_int($page) || ctype_digit($page))
			||	$page > $this->pages
			) {
				$new_url = preg_replace('"/'.$this->fieldname.'/\w+|'.$this->fieldname.'\w+"i', '', $_SERVER['REQUEST_URI']);
				require_once '_comment.inc';
				if (preg_match('"^/('.implodekeys('|',commentables()).')/(\d+)/(comments|condolences)\b"',$new_url,$match)
				&&	([, $parent, $id, $type] = $match)
				&&	!($collapsed = !(
						$parent === 'user'
					&&	$type   === 'comments'
					?	setting('SHOW_USER_GUESTBOOK')
					:	setting('UNCOLLAPSE_COMMENTS') & (1 << constant('CMT_'.strtoupper($type === 'condolences' ? 'deaduser' : $parent)))))
				) {
					$new_url = get_element_href($parent,$id);
				}
/*				error_log('PAGE '.$page.'/'.$this->pages.', out of range (perpage '.$this->perpage.'): '.$_SERVER['REQUEST_URI'].' for user '.CURRENTUSERID.
					(empty($_SERVER['HTTP_REFERER']) ? null : ' referred from '.$_SERVER['HTTP_REFERER']),0);
				error_log('^^^ new uri: '.$newuri,0);
				error_log('^^^ userid: '.CURRENTUSERID,0);
				if (USER_AGENT) {
					error_log('^^^ user_agent: '.USER_AGENT,0);
				}*/
				send_no_cache_headers();
				robot_action('noindex');
				moved_permanently($new_url);
			}
			return $this->page = (int)$page;
		}
	}

	public function display_stored(): void {
		if (!empty($this->stored)) {
			echo	$this->hash
			?	str_replace('id="'.$this->hash.'"','id="'.$this->hash.$this->bottomhash.'"',$this->stored)
			:	$this->stored;
		}
	}

	public function display_and_store(): void {
		if (isset($this->stored)) {
			echo $this->stored;
			return;
		}
		ob_start();
		$this->display();
		$this->stored = ob_get_contents();
		ob_end_flush();
	}

	public function display(): void {
		if ($this->total <= $this->perpage) {
			if ($this->hash) {
				?><div id="<?= $this->hash ?>"></div><?
			}
			return;
		}
		require_once '_helper.inc';
		if ($this->page === 0) {
			$this->page = $this->obtain_page();
		}

		$reverse = !$this->start_at_one;

		?><nav><?
		?><table class="page-controls"<?
		if ($this->hash) {
			?> id="<?= $this->hash ?>"<?
		}
		?>><?

		$next_str = null;
		$previous_str = null;

		if ($this->page !== 'all'
		&&	$this->show_prev_next
		) {
			ob_start();
			?><td class="previous"><?
			if ($this->page !== $this->all_name
			&&	(	$this->page === $this->last_name
				||	$this->page > 1
				)
			) {
				?><a<?
				if ($this->is_admin
				&&	$this->page !== 'last'
				&&	$this->page - 1 <= 0
				) {
					?> class="light"<?
				}
				?> href="<?
				ob_start();
				$prev_page =
					$this->page === $this->last_name
				?	$this->pages - 1
				:	$this->page - 1;
				if ($this->default_page !== $prev_page) {
					echo $this->get_url(), $this->pagepart, $prev_page;
				} else {
					echo $this->get_default_url();
				}
				echo $this->pagepost;
				if ($this->hash) {
					?>#<? echo $this->hash;
				}
				$link = ob_get_flush();
				?>" rel="prev" class="nowrap"><?

				echo	$this->older !== false
				?	($this->older ?: __('pagecontrols:older'))
				:	__('pagecontrols:previous');

				?> &equiv; <?= $prev_page ?></a><?

				include_head('<link rel="prev" href="'.$link.'" />');
			}
			?></td><?
			$previous_str = ob_get_clean();

			ob_start();
			?><td class="next"><?
			if ($this->page !== $this->last_name
			&&	$this->page !== $this->all_name
			&&	$this->page < $this->pages
			) {
				?><a<?
				if ($this->page + 1 <= 0) {
					?> class="light"<?
				}
				?> href="<?
				ob_start();
				if ($this->include_last
				&&	$this->page + 1 === $this->pages
				) {
					echo $this->get_default_url();
					$show_page = 'L';
				} elseif ($this->default_page !== $this->page+1) {
					echo $this->get_url(), $this->pagepart, ($this->page + 1);
					$show_page = $this->page+1;
				} else {
					echo $this->get_default_url();
				}
				echo $this->pagepost;
				if ($this->hash) {
					?>#<? echo $this->hash;
				}
				$link = ob_get_flush();
				?>" rel="next" class="nowrap"><?

				echo	$this->newer !== false
				?	($this->newer ?: __('pagecontrols:newer'))
				:	__('pagecontrols:next');
				?> &equiv; <?= $show_page ?></a><?

				include_head('<link rel="next" href="'.$link.'" />');
			}
			$next_str = ob_get_clean();

			if ($reverse) {
				$tmp = $next_str;
				$next_str = $previous_str;
				$previous_str = $tmp;
			}
		}

		?><tr><?
		echo $previous_str;

		if ($this->show_pages) {
			ob_start();
			if ($this->include_last) {
				$mostrecent = __C('pagecontrols:most_recent');
				?><a<?
				if ($this->page === $this->last_name) {
					?> class="selected-page"<?
				}
				?> href="<?
				ob_start();
				if ($this->default_page !== $this->last_name) {
					echo $this->get_url(), $this->pagepart, $this->last_name;
				} else {
					echo $this->get_default_url();
				}
				echo $this->pagepost;
				if ($this->hash) {
					?>#<? echo $this->hash;
				}
				$this->mostrecent_url = ob_get_flush();
				?>"><?= $mostrecent; ?></a><?
			}
			$last_str = ob_get_clean();

			ob_start();
			if (!ROBOT
			&&	$this->include_all
			&&	$this->total <= $this->absolute_limit
			) {
				$all = __C('pagecontrols:all');
				?><a<?
				if ($this->page === $this->all_name) {
					?> class="selected-page"<?
				}
				?> href="<?
				echo $this->get_url(), $this->pagepart, $this->all_name, $this->pagepost;
				if ($this->hash) {
					?>#<? echo $this->hash;
				}
				?>"><?= $all; ?></a><?
			}
			$all_str = ob_get_clean();

			if ($this->show_all_pages
			||	$this->pages <= 10
			) {
				// less than 10 pages total
				ob_start();
				$this->display_pages(range(1, $this->pages));
				$pages_str = ob_get_clean();
			} else {
				// more than 10 pages total
				if (!SMALL_SCREEN) {
					$pages[1] = 1;
					$pages[$this->pages - 1] = $this->pages - 1;
					$pages[$this->pages] = $this->pages;

					if (is_int($this->page)) {
						if ($this->page > 1) {
							$pages[$this->page - 1] = $this->page - 1;
						}
						$pages[$this->page] = $this->page;
						if ($this->page < $this->pages) {
							$pages[$this->page + 1] = $this->page + 1;
						}
					}

					ob_start();
					$this->display_pages($pages);
					$pages_str = ob_get_clean();
				}

				ob_start();
				if (!$this->hide_jump) {
					if ($this->pages <= 200) {
						$this->display_jump();
					} else {
						$this->display_choose();
					}
				}
				$jump_str = ob_get_clean();
			}
			?><td class="pages"><?
			if (!empty($pages_str)) {
				$parts[] = $pages_str;
			}
			if (!empty($jump_str)) {
				if (!empty($parts)) {
					$parts[] = ' ';//SMALL_SCREEN ? '<br />' : ' ';
				}
				$parts[] = $jump_str;
			}
			if (!empty($all_str)) {
				if (!empty($parts)) {
					$parts[] = ' ';
				}
				$parts[] = $all_str;
			}
			if (!empty($last_str)) {
				if (!empty($parts)) {
					$parts[] = ' ';
				}
				$parts[] = $last_str;
			}
			if ($reverse) {
				$parts = array_reverse($parts);
			}
			echo implode('', $parts);
			?></td><?
		}
		echo $next_str;
		?></tr></table><?
		?></nav><?
	}

	protected function display_pages(array $pages): void {
		$reverse = !$this->start_at_one;

		sort($pages);
		$links = [];
		$prev_page = 0;
		foreach ($pages as $page) {
			ob_start();
			if ($prev_page
			&&	$prev_page < $page - 1
			) {
				$links[] = ' &hellip; ';
			}
			$prev_page = $page;

			$light =
				($page - 1) < 0
			||	(	$page === $this->pages
				&&	$this->include_last
				);

			?><a<?
			$classes = null;
			if ($light) {
				$classes[] = 'light';
			}
			if ($this->page === $page) {
				$classes[] = 'selected-page';
			}
			if ($classes) {
				?> class="<?= implode(' ', $classes) ?>"<?
			}
			?> href="<?
			if ($this->default_page !== $page) {
				echo $this->get_url(), $this->pagepart, $page, $this->pagepost;
			} else {
				echo $this->get_default_url();
			}
			if ($this->hash) {
				?>#<? echo $this->hash;
			}
			?>"><?= $page;
			?></a><?
			$links[] = ob_get_clean();
		}
		if (!$links) {
			return;
		}
		?><span class="ib"><?= implode(' ', $reverse ? array_reverse($links) : $links) ?></span><?
	}

	protected function display_choose(): void {
		include_js('js/pagecontrols');
		?><span<?
		?> class="choose ptr"<?
		?> onclick="Pf.choosePage(this)"<?
		?> data-base-url="<?= $this->get_url() ?>"<?
		?> data-default-page="<?= $this->default_page ?>"<?
		?> data-pagepart="<?= $this->pagepart ?>"<?
		?> data-pagepost="<?= $this->pagepost ?>"<?
		?> data-ask="<?= __C('action:choose_page') ?>"<?
		?> data-last="<?= $this->pages ?>"<?
		?>><?= __C('action:choose') ?></span><?
	}

	protected function display_jump(): void {
		include_js('js/pagecontrols');
		?><select<?
		?> data-base-url="<?= $this->get_url() ?>"<?
		?> data-default-url="<?= $this->get_default_url() ?>"<?
		?> data-default-page="<?= $this->default_page ?>"<?
		?> data-pagepart="<?= $this->pagepart ?>"<?
		?> data-pagepost="<?= $this->pagepost ?>"<?
		if ($this->hash) {
			?> data-hash="#<?= $this->hash ?>"<?
		}
		?> onchange="Pf.pageJump(this)"<?
		?>><?

		ob_start();
		if ($this->include_last) {
			?><option<?
			if ($this->page === $this->last_name) {
				?> selected="selected"<?
				?> disabled="disabled"<?
			}
			?> value="<?
			if ($this->default_page !== $this->last_name) {
				echo $this->last_name;
			}
			?>"><?= __C('pagecontrols:last_page_single_char'); ?></option><?
		}
		$last_str = ob_get_clean();

		$reverse = !$this->start_at_one;

		$start = 1;
		$stop  = $this->pages;

		$options = [];

		for ($i = $start; $i <= $stop; ++$i) {
			ob_start();
			?><option<?
			if ($this->page === $i) {
				?> selected<?
				?> disabled<?
			}
			?> value="<?
			if ($this->default_page !== $i) {
				echo $i;
			}
			?>"><?= $i ?></option><?
			$options[] = ob_get_clean();
		}

		$options[] = $last_str;

		if ($reverse) {
			$options = array_reverse($options);
		}

		echo implode('', $options);

		?></select><?
	}

	public function sort(array &$arr): void {
		require_once '_sort.inc';
		$orderlist = $this->orders ?? $this->order;
		if (!is_array($orderlist)) {
			$orderlist = [$orderlist];
		}
		foreach ($orderlist as $orders) {
			$reps = [];
			if (str_contains($orders,'IF(')) {
				$orders = preg_replace_callback('"IF\((.*?)\)\s*(DESC|ASC)$"',function($match) use (&$reps,&$i) {
						$reps[++$i] = $match[1];
						return "\x01$i\x02 ".$match[2];
					},
					$orders
				);
			}
			foreach (explode(',',$orders) as $order) {
				if ($reps) {
					$order = preg_replace_callback("'\x01(\d+)\x02'",function($match) use ($reps) {
							return "\x01".$reps[$match[1]]."\x02";
						},
						$order
					);
				}
				if (!preg_match("'\.?([\x01\x02A-Z_,]+)(?:\s+(ASC|DESC))?\s*$'",$order,$match)) {
					error_log('order part not nderstood: '.$order,0); continue;
				}
				$result[$match[1]] = !empty($match[2]) && $match[2] === 'DESC' ? dsc : asc;
			}
		}
		multifield_sort($arr,$result,isset($arr[0]) ? 'usort' : 'uasort');
	}

	public function get_last_url(): ?string {
		return $this->mostrecent_url ?? null;
	}
}
