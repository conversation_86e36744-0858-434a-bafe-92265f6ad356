<?php

function show_topic_action_menu(string $element, array $topic): void {
	$href = "/$element/{$topic['TOPICID']}";
	$show_connect = function_exists('show_single_connect');
	ob_start();
	?><div class="nowrap l rmrgn"><?
	?><form<?
	?> accept-charset="utf-8"<?
	?> method="post"<?
	?> onsubmit="return submitForm(this)"<?
	?> action="<?= $href ?>/rename"><?
	?><input class="regular" style="width: auto;" required maxlength="128" type="text" name="SUBJECT" value="<?= escape_utf8($topic['SUBJECT'] ) ?>" /> <?
	?><input type="submit" value="<?= __('action:rename') ?>" /><?
	?></form><?
	?></div><?
	if ($show_connect) {
		?><div class="r"><?
		show_single_connect($topic['TOPICID']);
		?></div><?
	}
	$parts[] = ob_get_clean();
	if ($forumid = $element === 'topic' ? $topic['FORUMID'] : 0) {
		// MOVE
		ob_start();
		?><form<?
		?> class="l"<?
		?> method="get"<?
		?> action="<?= $href ?>/move"<?
		?> onsubmit="if (this.FORUMID.value === '<?= $forumid ?>') { alert('<?= __('topic:error:choose_other_forum_LINE',IS_JAVASCRIPT) ?>'); return false; } else { return submitForm(this); }"><?
		?> <select name="FORUMID"><?
			_forumlist_display_accessible($forumid);
		?></select> <?
		?><input type="submit" value="<?= __('action:move') ?>" /><?
		?></form><?
		$parts[] = ob_get_clean();
	}
	// STICK / UNSTICK
	ob_start();
	?><form class="ib" method="get" onsubmit="return submitForm(this)" action="<?= $href ?>/<?= $topic['STICKY'] ? 'unstick' : 'stick' ?>"> <?
	?><input type="submit" value="<?= __($topic['STICKY'] ? 'action:unstick' :  'action:stick') ?>" /><?
	?></form><?
	switch ($topic['STATUS']) {
	case 'open': 	$isopen = true; break;
	case 'closed':	$isclosed = true; break;
	case 'hidden':	$ishidden = true; break;
	}
	if (isset($isclosed)
	||	isset($ishidden)
	) {
		// OPEN
		?> <form class="ib" method="get" onsubmit="return submitForm(this)" action="<?= $href ?>/open"> <?
		?><input type="submit" value="<?= __('action:reopen') ?>" /><?
		?></form><?
	}
	if (isset($isopen)) {
		// CLOSE
		?> <form class="ib" method="get" onsubmit="return submitForm(this)" action="<?= $href ?>/close"> <?
		?><input type="submit" value="<?= __('action:close') ?>" /><?
		?></form><?
	}
	if (isset($isopen)
	||	isset($isclosed)
	) {
		// HIDE
		?> <form class="ib" method="get" onsubmit="return submitForm(this)" action="<?= $href ?>/hide"> <?
		?><input type="submit" value="<?= __('action:remove') ?>" /><?
		?></form><?
	}
	if ($forumid) {
		require_once '_forum.inc';
		if (get_sale_forums($forumid)) {
			// TYPE
			?> <form class="ib nowrap" method="get" onsubmit="return submitForm(this)" action="<?= $href ?>/changetype"><?
			?><select name="TYPE"><?
			show_topictype_options($topic['FLAGS']);
			?></select><?
			?> <input type="submit" value="<?= element_name('type') ?>" /><?
			?></form><?
		}
	}
	$parts[] = '<div class="l">'.ob_get_clean().'</div>';
	?><div class="sbot block"><?=
		implode(' ',$parts)
		?><div class="clear"></div><?
	?></div><?
}
function show_topic_connect_menu($topicid): void {
	ob_start();
	?><div class="r"><? show_single_connect($topicid); ?></div><?
	$part = ob_get_clean();
	?><div class="sbot block"><?=
		$part
		?><div class="clear"></div><?
	?></div><?
}
