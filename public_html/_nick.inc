<?php

function get_stripped_nick($nick) {
	$len = strlen($nick);
	$newnick = '';
	for ($i = 0; $i < $len; ++$i) {
		$chr = $nick[$i];
		$ord = ord($chr);
		if ($ord < 0x20	# control chars
		||	$ord == 0x7F	# DEL
		||	$ord == 129
		||	$ord == 141
		||	$ord == 143
		||	$ord == 144
		||	$ord == 157
		) {
			continue;
		}
		$newnick .= $chr;
	}

	$newnick = mytrim(preg_replace('/([\r\n\t\s]+)/',' ',strtr($newnick,"\xA0\xAD","  ")));
	
	if ($newnick != $nick) {
		require_once '_error.inc';
		register_warning('user:warning:nick_was_stripped_LINE',array('NDX'=>$i,'ORD'=>$ord,'NICK'=>$nick,'NEWNICK'=>$newnick));
	}
	return $newnick;
}
