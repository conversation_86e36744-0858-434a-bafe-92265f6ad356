<?php

function perform_commit(): ?bool {
	return match($_REQUEST['ACTION']) {
		default		=> null,
		'commit'	=> rights_commit(),
	};
}

function display_body(): void {
	switch ($_REQUEST['ACTION']) {
	default:		not_found(); return;
	case null:		rights_display_overview(); return;
	case 'user':
	case 'commit':	rights_display(); return;
	}
}

function rights_display_overview(): void {
	require_once '_fora.inc';
	if (!require_admin()) {
		return;
	}
	layout_show_section_header();

	if (false === ($fora = get_fora())
	||	false === ($rights = db_rowuse_array('rights','SELECT USERID,`PORTION` FROM rights'))
	||	false === ($adminfora = db_multirow_hash(['forum', 'rights_forum'], '
		SELECT FORUMID,USERID
		FROM forum
		LEFT JOIN rights_forum USING (FORUMID)
		WHERE TYPE = "admin"
		  AND FORUMID IN ('.implodekeys(', ', $fora).')'))
	) {
		return;
	}
	assert(is_array($fora)); # Satisfy EA inspection
	layout_open_box('white');
	if (!$rights) {
		layout_box_header(__C('header:general'));
		?><div class="block"><?= __('rights:no_admins_LINE') ?></div><?
	} else {
		$commentadmins = $ratingadmins = $admins = [];
		foreach ($rights as $right) {
			$portion = $right['PORTION'];
			$right['NICK'] = memcached_nick($right['USERID']);
			if (str_contains($portion, '_comment')) {
				$right['NAME'] = Eelement_name(substr($portion, 0, strpos($portion, '_')));
				$commentadmins[] = $right;
			} elseif (str_contains($portion, '_rating')) {
				$right['NAME'] = Eelement_name(substr($portion, 0, strpos($portion, '_')));
				$ratingadmins[] = $right;
			} else {
				$right['NAME'] = __C('right:'.$portion);
				$admins[] = $right;
			}
		}
		function show_rights($rights) {
			?><dl><?
			$currentportion = null;
			$rightsadmin = have_admin('rights');
			foreach ($rights as $right) {
				if (!$currentportion
				||	 $currentportion !== $right['PORTION']
				) {
					?><dt><b><?= escape_ascii($right['NAME']) ?></b></dt><?
					$currentportion = $right['PORTION'];
				}
				?><dd><?
				if ($rightsadmin) {
					?><a href="/rights/user/<?= $right['USERID'] ?>"><?=
						escape_specials($right['NICK'])
					?></a><?
				} else {
					echo get_element_link('user', $right['USERID'], $right['NICK']);
				}
				?></dd><?
			}
			?></dl><?
		}
		if ($admins) {
			doublestring_sort($admins,'NAME','NICK');
			layout_box_header(__C('header:general'));
			show_rights($admins);
			layout_continue_box();
		}
		if ($commentadmins) {
			doublestring_sort($commentadmins,'NAME','NICK');
			layout_box_header(Eelement_plural_name('comment'));
			show_rights($commentadmins);
		}
		if ($ratingadmins) {
			doublestring_sort($ratingadmins,'NAME','NICK');
			layout_box_header(Eelement_plural_name('rating'));
			show_rights($ratingadmins);
		}
		if ($commentadmins || $ratingadmins) {
			layout_continue_box();
		}
	}
	layout_box_header(Eelement_plural_name('forum'));
	if (!$adminfora) {
		?><p><?= __('rights:no_moderators_LINE'); ?></p><?
	} else {
		uksort($adminfora, static fn(int $a, int $b): int => utf8_strcasecmp($fora[$a], $fora[$b]));
		?><dl><?
		foreach ($adminfora as $forumid => $adminlist) {
			?><dt<?
			if (!$adminlist) {
				?> class="warning"<?
			}
			?>><b><?= escape_specials($fora[$forumid]); ?></b></dt><?
			if ($adminlist) {
				$admins = null;
				foreach ($adminlist as $userid) {
					$admins[$userid] = memcached_nick($userid);
				}
				asort($admins);
				foreach ($admins as $userid => $nick) {
					?><dd><a href="/rights/user/<?= $userid;
					?>"><?= escape_specials($nick); ?></a></dd><?
				}
			 }
		}
		?></dl><?
	}
	layout_close_box();
}

function rights_commit(): bool {
	require_once '_employees.inc';
	require_once '_rights.inc';
	if (!require_post()
	||	!($userid = require_idnumber($_REQUEST,'subID'))
	||	!require_admin('rights')
	||	have_super_admin($_REQUEST['subID'])
	&&	!require_super_admin()
	||	false === ($current_rights = db_boolean_hash('rights', "
		SELECT `PORTION`
		FROM rights
		WHERE USERID = $userid",
		DB_USE_MASTER))
	) {
		return false;
	}
	$is_photographer = false;
	$was_admin = $current_rights ? true : false;
	$admin_count = 0;

	if (isset($_REQUEST['PORTION'])) {
		if (!is_array($_REQUEST['PORTION'])) {
			return false;
		}
		$entries = [];
		foreach ($_REQUEST['PORTION'] as $portion) {
			if ($portion !== 'aspiring_photographer') {
				++$admin_count;
			}
			$new_rights[$portion] = true;
			if (!isset($current_rights[$portion])) {
				$entries[$portion] = "($userid, '".addslashes($portion)."', ".CURRENTUSERID.', '.CURRENTSTAMP.')';
				if ($portion === 'photographer') {
					$is_photographer = true;
				}
			}
		}
		if ($entries
		&&	!db_insert('rights','
			INSERT INTO rights (USERID, `PORTION`, CUSERID, CSTAMP)
			VALUES '.implode(', ', $entries))
		) {
			return false;
		}
	}
	if (($removed = !empty($new_rights) ? array_diff_key($current_rights,$new_rights) : $current_rights)
	&&	(	!db_insert('rights_log','
			INSERT INTO rights_log
			SELECT *,'.CURRENTSTAMP.','.CURRENTUSERID.'
			FROM rights
			WHERE '.($where_part = '`PORTION` IN ('.stringsimplode(',',array_keys($removed)).') AND USERID='.$userid))
		||	!db_delete('rights', "
			DELETE FROM rights
			WHERE $where_part")
		)
	||	false === ($current_rights = db_simple_hash('rights_forum', "
		SELECT FORUMID,TYPE
		FROM rights_forum
		WHERE USERID = $userid",
		DB_USE_MASTER))
	) {
	 	return false;
	}
	unset($new_rights);
	if (!$was_admin) {
		foreach ($current_rights as $forumid => $type) {
			if ($type == 'admin') {
				$was_admin = true;
				break;
			}
		}
	}
	if (isset($_REQUEST['FORUMID'])) {
		$forum_updates = [];
		$forum_entries = [];
		foreach ($_REQUEST['FORUMID'] as $forumid => $type) {
			if (!is_number($forumid)) {
				error_log('ERROR rights_commit, invalid forumid: '.$forumid);;
				register_error('form:error:invalid_data_LINE');
				return false;
			}
			if (!in_array($type, ['inherit', 'admin', 'normal', 'read', 'no'], true)) {
				register_error('form:error:invalid_data_LINE');
				return false;
			}
			switch ($type) {
			case 'inherit':
				break;
			case 'admin':
				++$admin_count;
			case 'normal':
			case 'no':
			default:
				$new_rights[$forumid] = true;
				if (!isset($current_rights[$forumid])) {
					$forum_entries[$forumid] = "($userid, $forumid, '".addslashes($type)."', ".CURRENTUSERID.', '.CURRENTSTAMP.')';

				} elseif ($current_rights[$forumid] !== $type) {
					$forum_updates[$forumid] = "($userid, $forumid, '".addslashes($type)."', ".CURRENTUSERID.', '.CURRENTSTAMP.')';
				}
				break;
			}
		}
		if ($forum_entries
		&&	!db_insert('rights_forum','
			INSERT INTO rights_forum (USERID, FORUMID, TYPE, CUSERID, CSTAMP)
			VALUES '.implode(', ', $forum_entries))
		||	$forum_updates
		&&	(	!db_insert('rights_forum_log','
				INSERT INTO rights_forum_log
				SELECT *, '.CURRENTSTAMP.', '.CURRENTUSERID.'
				FROM rights_forum
				WHERE '.($where_part = 'FORUMID IN ('.implodekeys(',',$forum_updates).') AND USERID='.$userid))
			||	!db_replace('rights_forum','
				REPLACE INTO rights_forum (USERID, FORUMID, TYPE, CUSERID, CSTAMP)
				VALUES '.implode(', ', $forum_updates))
			)
		) {
			return false;;
		}
	}
	if (($removed = !empty($new_rights) ? array_diff_key($current_rights, $new_rights) : $current_rights)
	&&	(	!db_insert('rights_forum_log','
			INSERT INTO rights_forum_log
			SELECT *,'.CURRENTSTAMP.','.CURRENTUSERID.'
			FROM rights_forum
			WHERE '.($where_part = 'FORUMID IN ('.implodekeys(',',$removed).') AND USERID='.$userid))
		||	!db_delete('rights_forum','
			DELETE FROM rights_forum
			WHERE '.$where_part))
	) {
		return false;
	}
	if (!$was_admin) {
		if (!empty($admin_count)) {
			if (!db_insupd('crewinfo', "
				INSERT INTO crewinfo SET
					USERID		= $userid,
					ACTIVE		= 1,
					STARTSTAMP	= ".CURRENTSTAMP.',
					ONLYPHOTO	= '.($admin_count === 1 && $is_photographer ? 1 : 0))
			||	($userinfo = db_single_assoc('user', "
					SELECT REALNAME, EMAIL
					FROM user
					WHERE USERID = $userid"))
			&&	!commit_employee($userid, [
					'REALNAME'			=> $userinfo['REALNAME'],
					'DESCRIPTION'		=> null,
					'EMAIL'				=> $userinfo['EMAIL'],
					'ACTIVE'			=> 1,
					'VISIBILITY_NAME'	=> 0,
				],	'organization',
					ORGANIZATIONID_PARTYFLOCK)
			) {
				return false;
			}
		}
	} else {
		if (empty($admin_count)) {
			if (!db_update('crewinfo','
				UPDATE crewinfo SET
					STOPSTAMP	= '.CURRENTSTAMP.',
					ACTIVE		= 0
				WHERE STOPSTAMP = 0
				  AND USERID 	='.$userid)
			) {
				return false;
			}
			activate_employee($userid, false, 'organization', ORGANIZATIONID_PARTYFLOCK);

		} elseif ($admin_count > 1) {
			if (!db_update('crewinfo', "
				UPDATE crewinfo SET ONLYPHOTO = 0
				WHERE USERID = $userid
				  AND ACTIVE")
			) {
				return false;
			}
		}
	}
	flush_rights_cache($userid);
	register_notice('rights:notice:changed_LINE');
	return true;
}

function rights_display() {
	require_once '_rightslist.inc';
	if (!($userid = require_idnumber($_REQUEST, 'subID'))
	||	!require_admin('rights')
	||	have_super_admin($userid)
	&&	!require_super_admin()
	||	!($nick = require_nick($_REQUEST, 'subID'))
	) {
		return;
	}
	$userid = $_REQUEST['subID'];
	layout_open_section_header();
	echo Eelement_plural_name('right'); ?> <?= MIDDLE_DOT_ENTITY ?> <? echo get_element_link('user',$userid,$nick);
	layout_close_section_header();

	if (false === ($rights = db_simpler_array('rights', '
		SELECT `PORTION`
		FROM rights
		WHERE USERID = '.$userid))
	) {
		return;
	}

	if (false === ($fora = db_rowuse_array(['forum', 'rights_forum'], "
		SELECT	forum.FORUMID,
				forum.NAME,
				rights_forum.TYPE
		FROM forum
		LEFT JOIN rights_forum ON rights_forum.USERID = $userid
							  AND rights_forum.FORUMID = forum.FORUMID
		ORDER BY NAME"))
	) {
		return;
	}
	include_js('js/form/rights');

	foreach ([
		'main'		=> MAIN_RIGHTS,
		'comment'	=> COMMENT_RIGHTS,
		'rating'	=> RATING_RIGHTS,
	] as $type => $list) {
		foreach ($list as $right => $name) {
			$rights_names[$type][$right] = match($type) {
				'main'	=> __C('right:'.$right),
				default	=> Eelement_name(substr($right, 0, strpos($right, '_'))),
			};
		}
		asort($rights_names[$type]);
	}

	?><form<?
	?> accept-charset="utf-8"<?
	?> class="rights"<?
	?> onsubmit="return submitForm(this);"<?
	?> method="post"<?
	?> action="/rights/commit/<?= $userid ?>"><?

	?><div style="columns: auto;"><?

	layout_open_box('white ib vtop');
	layout_box_header(__C('header:general'));
	show_rights_checkboxes($rights_names['main'], $rights);
	layout_close_box(no_ad: true);

	layout_open_box('white ib vtop');
	layout_box_header(Eelement_plural_name('comment'));
	show_rights_checkboxes($rights_names['comment'], $rights);
	layout_close_box(no_ad: true);

	layout_open_box('white ib vtop');
	layout_box_header(Eelement_plural_name('rating'));
	show_rights_checkboxes($rights_names['rating'], $rights);
	layout_close_box(no_ad: true);

	layout_open_box('white ib vtop');
	layout_box_header(Eelement_plural_name('forum'));
	?><table class="padded"><?
	$normal_axs	= __('forum:access:normal');
	$no_axs		= __('forum:access:no');
	$read_axs	= __('forum:access:read');
	$admin_axs	= __('forum:access:admin');
	foreach ($fora as $forum) {
		$normal = $no = $read = $admin = $inherit = false;
		switch ($forum['TYPE']) {
		case 'normal':	$normal = true; break;
		case 'no'	:	$no = true; break;
		case 'read':	$read = true; break;
		case 'admin':	$admin = true; break;
		default:		$inherit = true; break;
		}
		?><tr<?
		if (!$inherit) {
			?> class="<?= $forum['TYPE'] ?>-access"<?
		}
		?>><td><?
		?><select onchange="changeForumAccess(this);" name="FORUMID[<?= $forum['FORUMID']; ?>]"><?
		?><option value="inherit"></option><?
		?><option value="normal"<? if ($normal)	 { ?> selected <? } ?>><?= $normal_axs ?></option><?
		?><option value="no"	<? if ($no)		 { ?> selected <? } ?>><?= $no_axs ?></option><?
		?><option value="read"	<? if ($read)	 { ?> selected <? } ?>><?= $read_axs ?></option><?
		?><option value="admin"	<? if ($admin)	 { ?> selected <? } ?>><?= $admin_axs ?></option><?
		?></select><?
		?></td><td><?=	escape_utf8($forum['NAME']);
		?></td></tr><?
	}
	?></table><?
	layout_close_box(no_ad: true);

	?></div><?

	?><div class="block"><input type="submit" value="<?= __('action:change_rights') ?>" /></div><?
	?></form><?
}

function show_rights_checkboxes(array $sections, array $rights): void {
	?><table><?
	foreach ($sections as $portion => $name) {
		$checked = $rights && in_array($portion, $rights, true);
		?><tr<?
		if ($checked) {
			?> class="admin-access"<?
		}
		?>><?
		?><td><?
		show_input([
			'onclick'	=> "this.parentNode.parentNode.className=this.checked?'admin-access':''",
			'id'		=> $portion,
			'name'		=> 'PORTION[]',
			'type'		=> 'checkbox',
			'value'		=> $portion,
			'checked'	=> $checked,
		]);
		?></td><td><?
		?><label for="<?= $portion ?>"><? echo $name ?></label><?
		?></td></tr><?
	}
	?></table><?
}
