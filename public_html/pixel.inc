<?php

require_once '_pixel.inc';

function display_title(): void {
	echo __('section:pixel');
}

function perform_commit(): ?bool {
	if (!have_admin('pixel')) {
		return false;
	}
	switch ($_REQUEST['ACTION']) {
	case 'commit':
		if ($pixelid = pixel_commit()) {
			store_messages_in_cookie();
			header('Location: https://'.$_SERVER['HTTP_HOST'].'/pixel/'.$pixelid, true, 303);
			exit;
		}
		return false;

	case 'activate':
	case 'deactivate':
		if ($pixelid = pixel_activate($_REQUEST['ACTION'] === 'activate' ? true : false)) {
			store_messages_in_cookie();
			header('Location: https://'.$_SERVER['HTTP_HOST'].'/pixel/'.$pixelid, true, http_status::SEE_OTHER->value);
			exit;
		}
		return false;

	case 'remove':		require_once '_remove.inc'; return remove_element();
	case 'combine':		require_once '_combine.inc'; return combine_element();
	}
	return null;
}

function display_body(): void {
	if (!require_admin('pixel')) {
		no_permission();
		return;
	}
	switch ($_REQUEST['ACTION']) {
	default:			not_found(); return;
	case null:			pixel_display_overview(); return;
	case 'archive':		pixel_display_overview(true); return;
	case 'combine':
	case 'single':		pixel_display_single(); return;
	case 'form':		pixel_display_form(); return;
	case 'combinewith':	require_once '_combine.inc'; show_combine_with(); return;
	case 'commit':		return;
	case 'remove':		$GLOBALS['commitresult'] ? pixel_display_overview() : pixel_display_single(); return;
	}
}

function pixel_menu(?array $pixel = null): void {
	layout_open_menu();
	layout_menuitem(Eelement_name('overview'), '/pixel', !$_REQUEST['ACTION']);
	layout_menuitem(Eelement_name('archive'), '/pixel/archive', $_REQUEST['ACTION'] == 'archive');
	layout_continue_menu();
	show_element_menuitems($pixel);
	layout_close_menu();

	layout_open_menu();
	if ($_REQUEST['sID']) {
		if ($_REQUEST['ACTION'] == 'single') {
			layout_menuitem(__C('action:change'), '/pixel/'.$_REQUEST['sID'].'/form', $_REQUEST['ACTION'] == 'form' && $_REQUEST['sID']);

			$change_state = __C($pixel['ACTIVE'] ? 'action:deactivate' : 'action:activate');

			if (!$pixel['FACEBOOKID']
			&&	!$pixel['GOOGLEID']
			&&	!$pixel['TIKTOKID']
			&&	!$pixel['PINTERESTID']
			||	$pixel['STOPSTAMP'] < $pixel['CSTAMP']
			) {
				?><span class="unavailable"><?=  $change_state ?></span><?
			} else {
				layout_menuitem($change_state, '/pixel/'.$_REQUEST['sID'].'/'.($pixel['ACTIVE'] ? 'deactivate' : 'activate'));
			}
		}
	} else {
		layout_menuitem(__C('action:add'), '/pixel/form', $_REQUEST['ACTION'] == 'form');
	}
	layout_close_menu();
}

function pixel_display_overview(bool $archive = false): void {
	layout_show_section_header();

	pixel_menu();

	$pixels = db_rowuse_hash(['pixel', 'organization', 'party', 'pixelad', 'ad', 'banner'], '
		SELECT	pixel.PIXELID, ORGANIZATIONID, pixel.PARTYID, pixel.RELATIONID, pixel.STOPSTAMP, AUTOEXTEND, pixel.ACTIVE,
				(SELECT COUNT(*) FROM pixelparty WHERE ACTIVE = 1 AND pixelparty.PIXELID = pixel.PIXELID) AS EVENTCNT,
				COALESCE(party.NAME, organization.NAME, banner.NAME) AS NAME
		FROM pixel
		LEFT JOIN organization USING (ORGANIZATIONID)
		LEFT JOIN party USING (PARTYID)
		LEFT JOIN pixelad USING (PIXELID)
		LEFT JOIN ad USING (ADID)
		LEFT JOIN banner USING (BANNERID)
		WHERE pixel.STOPSTAMP'.($archive ? ' < ' : ' > ').CURRENTSTAMP.'
		  AND (pixel.ACTIVE OR NOT INACTIVE_INVISIBLE)
		ORDER BY NAME, pixel.STOPSTAMP ASC'
	);
	if ($pixels) {
		layout_open_box('white');
		layout_box_header(Eelement_plural_name('pixel'));
		show_pixel_table($pixels);
		layout_close_box();
	}
}

function pixel_display_single(): void {
	layout_show_section_header();

	if (!($pixelid = $_REQUEST['sID'])) {
		not_found();
		return;
	}

	$pixel = db_single_assoc('pixel','
		SELECT *
		FROM pixel
		WHERE PIXELID='.$pixelid
	);
	if (!$pixel) {
		if ($pixel === null) {
			not_found();
		}
		return;
	}

	pixel_menu($pixel);

	require_once '_ticketlist.inc';
	show_connected_tickets();

	foreach ([
		'party'	=> ['STAMP_TZI', ' JOIN party USING (PARTYID)'],
		'ad'	=> ['STOPSTAMP', ' JOIN	ad USING (ADID)'],

	] as $element => [$extra_field, $extra_join]) {
		if (false === ($items[$element] = db_rowuse_hash("pixel$element", "
			SELECT {$element}ID AS ID, pixel$element.ACTIVE, $extra_field
			FROM pixel$element
			$extra_join
			WHERE PIXELID = $pixelid
			ORDER BY $extra_field DESC",
			DB_FORCE_MASTER))
		) {
			return;
		}
	}
	if (false === ($identicals = db_rowuse_hash(['pixel', 'relation', 'party', 'organization'], '
		SELECT	PIXELID,'.pixel_name_select().' AS NAME,
				RELATIONID, STOPSTAMP, pixel.CSTAMP
		FROM pixel
		LEFT JOIN relation USING (RELATIONID)
		LEFT JOIN party USING (PARTYID)
		LEFT JOIN organization USING (ORGANIZATIONID)
		WHERE (RELATIONID = 0 OR RELATIONID != '.$pixel['RELATIONID'].')
		  AND IF(FACEBOOKID, FACEBOOKID = '.$pixel['FACEBOOKID'].', 1)
		  AND IF(GOOGLEID != "", GOOGLEID = "'.addslashes($pixel['GOOGLEID']).'", 1)
		  AND IF(TIKTOKID != "", TIKTOKID = "'.addslashes($pixel['TIKTOKID']).'", 1)
		  AND IF(PINTERESTID, PINTERESTID = '.$pixel['PINTERESTID'].', 1)
		  AND (	   FACEBOOKID
		  	OR GOOGLEID != ""
		  	OR TIKTOKID != ""
		  	OR PINTERESTID)
		  AND PIXELID != '.$pixelid,
		DB_FORCE_MASTER))
	) {
		return;
	}
	$list = new deflist('deflist vtop');
	if ($invoicenr = $pixel['INVOICENR']) {
		$list->add_row(Eelement_name('invoice_number'),'<a href="/invoice/'.$invoicenr.'">'.$invoicenr.'</a>');
	}
	if ($pixel['RELATIONID']) {
		$list->add_row(Eelement_name('relation'), get_element_link('relation',$pixel['RELATIONID']));
	}
	require_once '_presence.inc';
	if ($pixel['FACEBOOKID']) {
		$list->add_row('Facebook<span class="r">'.get_presence_icon('facebook').'</span>', $pixel['FACEBOOKID']);
	}
	if ($pixel['GOOGLEID']) {
		$list->add_row('Google<span class="r">'.get_presence_icon('google').'</span>', $pixel['GOOGLEID']);
	}
	if ($pixel['PINTERESTID']) {
		$list->add_row('Pinterest<span class="r">'.get_presence_icon('pinterest').'</span>', $pixel['PINTERESTID']);
	}
	if ($pixel['SNAPCHATID']) {
		$list->add_row('Snapchat<span class="r">'.get_presence_icon('snapchat').'</span>', $pixel['SNAPCHATID']);
	}
	if ($pixel['TIKTOKID']) {
		$list->add_row('TikTok<span class="r">'.get_presence_icon('tiktok').'</span>', $pixel['TIKTOKID']);
	}
	# warn about identical IDs
	foreach ($identicals as $ipixelid => $ipixel) {
		$extra = [];

		if ($pixel['CSTAMP']    >= $ipixel['STOPSTAMP']
		||	$pixel['STOPSTAMP'] <= $ipixel['CSTAMP']
		) {
			# no overlap
			$extra[] = element_name('no_overlap');
		} else {
			$extra[] = element_name('overlap');
		}

		if ($pixel['RELATIONID'] &&	$ipixel['RELATIONID']
		&&	$pixel['RELATIONID'] !== $ipixel['RELATIONID']
		) {
			$list->set_row_class('warning ns');
			$extra[] = element_name('different_relation');
		} else {
			$extra[] = element_name('same_relation');
		}

		$list->add_row(
			__C('status:identical'),
			'<a href="/pixel/'.$ipixelid.'">'.escape_utf8($ipixel['NAME']).'</a>'.
			($extra ? ' ('.implode(', ',$extra).')' : null)
		);
	}
	if ($pixel['STOPSTAMP']) {
		$list->add_row(Eelement_name('stop'), _datetime_get($pixel['STOPSTAMP']));
	}
	$list->add_row(Eelement_name('auto_extend'), __($pixel['AUTOEXTEND'] ? 'answer:yes' : 'answer:no'));
	if ($pixel['PARTYID']) {
		$date = null;
		if ($party = memcached_party_and_stamp($pixel['PARTYID'])) {
			change_timezone('UTC');
			$date = ' <span class="light">&middot; '._datetime_get($party['STAMP_TZI']).'</span>';
			change_timezone();
		}
		$list->add_row(Eelement_name('event'), get_element_link('party', $pixel['PARTYID']).$date);
	}
	if ($pixel['ORGANIZATIONID']) {
		$list->add_row(Eelement_name('organization'), get_element_link('organization', $pixel['ORGANIZATIONID']));
	}
	foreach ([
		'party'	=> null,
		'ad'	=> null,
	] as $element => $null) {
		if (empty($items[$element])) {
			continue;
		}
		ob_start();
		foreach ($items[$element] as $id => $item) {
			extract($item);
			switch ($element) {
			case 'party':
				change_timezone('UTC');
				$date = _date_get($STAMP_TZI);
				change_timezone();
				break;

			case 'ad':
				$date = _date_get($STOPSTAMP);
				break;
			}

			?><tr class="<?
			if (!$ACTIVE) {
				?>light <?
			}
			if ($explicit_connected
			=	$element === 'party'
			&&	$id === $pixel['PARTYID']
			) {
				?>notice-nb <?
			}
			?>"><?
			?><td><?= get_element_link($element, $id) ?></td><?
			?><td class="right"><?= $date ?></td><?
			?><td><?
			if (!$explicit_connected) {
				# activate:
				?><div id="<?= $element ?>-<?= $id ?>-activate"<?
				if ($ACTIVE) {
					?> class="hidden"<?
				}
				?>><?
				echo get_special_char('add', [
					'onclick' => /** @lang JavaScript */ "Pf.setPixelItemStatus(this, $pixelid, '$element', $id, true);",
				]);
				?></div><?

				# deactivate:
				?><div id="<?= $element ?>-<?= $id ?>-deactivate"<?
				if (!$ACTIVE) {
					?> class="hidden"<?
				}
				?>><?
				echo get_special_char('rem', [
					'onclick' => /** @lang JavaScript */ "Pf.setPixelItemStatus(this, $pixelid, '$element', $id, false);",
				]);
				?></div><?
			}
			?></td><?
			?></tr><?
		}
		if ($rows = ob_get_clean()) {
			include_js('js/pixelitem');
			$list->add_rows(
				Eelement_plural_name($element === 'party' ? 'event' : 'ad'),
				'<div style="max-height:500px;padding-right:1em;overflow-y:scroll" class="forcescroll">'.
					'<table class="fw hpadded between nomargin">'.$rows.'</table>'.
				'</div>'
			);
		}
	}

	layout_open_box('pixel');
	layout_box_header(Eelement_name('information'));
	$list->display();
	layout_display_alteration_note($pixel);
	layout_close_box();
}

function pixel_display_form(): void {
	layout_show_section_header();

	pixel_menu();

	require_once '_fillelementid.inc';

	$pixel = null;
	if ($pixelid = $_REQUEST['sID']) {
		if (!($pixel = db_single_assoc('pixel','SELECT * FROM pixel WHERE PIXELID='.$pixelid))) {
			if ($pixel === null) {
				not_found();
			}
			return;
		}
		if (false === ($adids = db_simpler_array('pixelad', '
			SELECT ADID
			FROM pixelad
			WHERE PIXELID = '.$pixelid))
		) {
			return;
		}
	}

	$adids[] = null;

	include_js('js/form/pixel');

	?><form<?
	?> onsubmit="return submitForm(this)"<?
	?> autocomplete="off"<?
	?> method="post"<?
	?> action="/pixel<?
	if ($pixel) {
		?>/<? echo $pixelid;
	}
	?>/commit"><?

	layout_open_box('white');

	layout_open_table('vtop fw');

	layout_start_row();

	if (!empty($pixel['INVOICENR'])) {
		# INVOICENR
		echo Eelement_name('invoice_number');
		layout_field_value();
		show_input([
			'type'	=> 'number',
			'class'	=> 'id right',
			'min'	=> 0,
			'name'	=> 'INVOICENR',
			'value'	=> $pixel
		]);

		layout_restart_row();
	}

		# RELATIONID
		echo Eelement_name('relation');
		layout_field_value();
		require_once '_relationlist.inc';
		?><select style="max-width:30em;width:100%" name="RELATIONID"><option></option><?
		relationlist_show_options($pixel['RELATIONID'] ?? 0);
		?></select><?

	$active_checked = !empty($pixel['ACTIVE']);
	layout_start_row();
		?><label for="active"><?= __C('status:active') ?></label><?
		layout_field_value('middle');
		?><label class="<?
		if (!$active_checked) {
			?>not-<?
		}
		?>bold-hilited-green"><?
		show_input([
			'class'		=> 'upLite',
			'id'		=> 'active',
			'type'		=> 'checkbox',
			'name'		=> 'ACTIVE',
			'checked'	=> $active_checked,
		]);
		?></label><?

	$inactive_invisible_checked = !empty($pixel['INACTIVE_INVISIBLE']);
	layout_restart_row();
		?><label for="inactive-invisible"><?= __C('status:inactive'),' ',__('status:invisible') ?></label><?
		layout_field_value('middle');
		?><label class="<?
		if (!$inactive_invisible_checked) {
			?>not-<?
		}
		?>bold-hilited"><?
		show_input([
			'class'		=> 'upLite',
			'id'		=> 'inactive-invisible',
			'type'		=> 'checkbox',
			'name'		=> 'INACTIVE_INVISIBLE',
			'checked'	=> $inactive_invisible_checked,
		]);
		?></label><?

	?></td></tr><?

	?><tbody><?
	?><tr><td class="field"><?
		echo Eelement_name('event');
		layout_field_value();
		show_elementid_input('party', $pixel['PARTYID'] ?? null, $elementid_input_args = [
			'name'    => 'PARTYIDS[]',
			'onkeyup' => "Pf.changeID(this, 'PARTYIDS[]')"
		]);
	?></td></tr><?
	if (!empty($pixel['PARTYID'])) {
		?><tr><td class="field"><?
			echo Eelement_name('event');
			layout_field_value();
			show_elementid_input('party', args: $elementid_input_args);
		?></td></tr><?
	}
	?></tbody><?

	require_once '_presence.inc';

	layout_start_row();
		# ORGANIZATION
		echo Eelement_name('organization');
		layout_field_value();
		show_elementid_input('organization', $pixel['ORGANIZATIONID'] ?? null, [
			'name' => 'ORGANIZATIONID'
		]);
		?> + <?= element_plural_name('all_event_by_organization');

	?><tbody><?

	$elementid_input_args = [
		'name'		=> 'ADIDS[]',
		'onkeyup'	=> "Pf.changeID(this, 'ADIDS[]')"
	];

	foreach ($adids as $adid) {
		?><tr><td class="field"><?
			echo Eelement_name('ad');
			layout_field_value();
			show_elementid_input('ad', $adid, $elementid_input_args);
		?></td></tr><?
	}
	?></tbody><?

	layout_restart_row();
		$autoextend = !$pixel || $pixel['AUTOEXTEND'];
		?><label for="autoextend"><?
		echo Eelement_name('auto_extend');
		?></label><?
		layout_field_value('middle');
		?><label class="<?
		if (!$autoextend) {
			?>not-<?
		}
		?>bold-hilited"><?
		show_input([
			'class'		=> 'upLite',
			'id'		=> 'autoextend',
			'type'		=> 'checkbox',
			'name'		=> 'AUTOEXTEND',
			'checked'	=> $autoextend,
		]);
		?></label><?

	layout_restart_row();
		# FACEBOOKID
		?>Facebook<span class="r"><?= get_facebook_icon() ?></span><?
		layout_field_value();
		show_input([
			'type'		=> 'number',
			'class'		=> 'regular',
			'name'		=> 'FACEBOOKID',
			'value'		=> $pixel && $pixel['FACEBOOKID'] ? $pixel['FACEBOOKID'] :  null,
		]);
		?> <div class="lmrgn light small ib"><?= db_single('pixel', 'SELECT FACEBOOKID FROM pixel WHERE FACEBOOKID ORDER BY RAND() LIMIT 1') ?></div><?

	layout_restart_row();
		# GOOGLEID
		?>Google<span class="r"><?= get_presence_icon('google') ?></span><?
		layout_field_value();
		show_input([
			'type'		=> 'text',
			'pattern'	=> '^(?:AW-[\d\-]+|G-[\w\d]+|GTM-\w+)$',
			'class'		=> 'regular',
			'name'		=> 'GOOGLEID',
			'value'		=> $pixel
		]);
		?> <div class="lmrgn light small ib"><?=
			db_single('pixel', 'SELECT GOOGLEID FROM pixel WHERE GOOGLEID LIKE "AW-%"  ORDER BY RAND() LIMIT 1') ?>, <?=
			db_single('pixel', 'SELECT GOOGLEID FROM pixel WHERE GOOGLEID LIKE "G-%"   ORDER BY RAND() LIMIT 1') ?>, <?=
			db_single('pixel', 'SELECT GOOGLEID FROM pixel WHERE GOOGLEID LIKE "GTM-%" ORDER BY RAND() LIMIT 1')
		?></div><?

	layout_restart_row();
		# PINTERESTID
		?>Pinterest<span class="r"><?= get_presence_icon('pinterest') ?></span><?
		layout_field_value();
		show_input([
			'type'		=> 'number',
			'class'		=> 'regular',
			'name'		=> 'PINTERESTID',
			'value'		=> $pixel && $pixel['PINTERESTID'] ? $pixel['PINTERESTID'] : null,
		]);
		?> <div class="lmrgn light small ib"><?= db_single('pixel', 'SELECT PINTERESTID FROM pixel WHERE PINTERESTID ORDER BY RAND() LIMIT 1') ?></div><?

	layout_restart_row();
		# SNAPCHATID
		?>Snapchat<span class="r"><?= get_presence_icon('snapchat') ?></span><?
		layout_field_value();
		show_input([
			'type'		=> 'text',
			'pattern'	=> '^[\da-fA-F]{8}\b-[\da-fA-F]{4}\b-[\da-fA-F]{4}\b-[\da-fA-F]{4}\b-[\da-fA-F]{12}$',
			'class'		=> 'regular',
			'name'		=> 'SNAPCHATID',
			'value'		=> $pixel
		]);
		?> <div class="lmrgn light small ib"><?= db_single('pixel', 'SELECT SNAPCHATID FROM pixel WHERE SNAPCHATID IS NOT NULL ORDER BY RAND() LIMIT 1') ?></div><?

	layout_restart_row();
		# TIKTOKID
		?>TikTok<span class="r"><?= show_presence_icon('tiktok') ?></span><?
		layout_field_value();
		show_input([
			'type'		=> 'text',
			'pattern'	=> '^[\w\d]{20}$',
			'class'		=> 'regular',
			'name'		=> 'TIKTOKID',
			'value'		=> $pixel
		]);
		?> <div class="lmrgn light small ib"><?= db_single('pixel', 'SELECT TIKTOKID FROM pixel WHERE TIKTOKID != "" ORDER BY RAND() LIMIT 1') ?></div><?


	layout_close_table();

	layout_close_box();

	?><div class="block"><input type="submit" value="<?= __($pixel ? 'action:change' : 'action:add'); ?>" /></div></form><?
}

function pixel_commit(): int|false {
	if (!require_number_array($_POST, 'PARTYIDS', allow_empty: true, remove_empty: true)
	||	!require_number_array($_POST, 'ADIDS', allow_empty: true, remove_empty: true)
	||	!require_number_or_empty($_POST, 'ORGANIZATIONID')
	||	!optional_number($_POST, 'INVOICENR')
	||	!require_number_or_empty($_POST, 'RELATIONID')
	||	!require_number_or_empty($_POST, 'FACEBOOKID')
	||	!require_number_or_empty($_POST, 'PINTERESTID')
	||	!require_anything($_POST, 'GOOGLEID')
	||	!empty($_POST['GOOGLEID'])
	&&	!require_regex($_POST, 'GOOGLEID', '"^(?:AW\-[\d\-]+|G\-[\w\d]+|GTM\-\w+)$"')
	||	!empty($_POST['TIKTOKID'])
	&&	!require_regex($_POST, 'TIKTOKID', '"^[\w\d]{20}$"')
	||	!empty($_POST['SNAPCHATID'])
	&&	!require_regex($_POST, 'SNAPCHATID', '"^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$"')
	) {
		return false;
	}
	if ($pixelid = $_REQUEST['sID']) {
		if (!($old_pixel = db_single_assoc('pixel', 'SELECT * FROM pixel WHERE PIXELID = '.$pixelid))) {
			if ($old_pixel === false) {
				return false;
			}
			not_found();
			return false;
		}
	}

	$setlist['ORGANIZATIONID']	= $_POST['ORGANIZATIONID'] ?: 0;
	$setlist['INVOICENR']		= empty($_POST['INVOICENR']) ? null : $_POST['INVOICENR'];
	$setlist['RELATIONID']		= $_POST['RELATIONID'] ?: 0;
	$setlist['FACEBOOKID']		= $_POST['FACEBOOKID'] ?: 0;
	$setlist['PINTERESTID']		= $_POST['PINTERESTID'] ?: 0;
	$setlist['SNAPCHATID']		= $_POST['SNAPCHATID'] ? ['ascii', $_POST['SNAPCHATID']] : null;
	$setlist['GOOGLEID']		= $_POST['GOOGLEID'];
	$setlist['TIKTOKID']		= $_POST['TIKTOKID'];
	$setlist['AUTOEXTEND']		= isset($_POST['AUTOEXTEND']);
	$setlist['ACTIVE']			= isset($_POST['ACTIVE']);
	$setlist['INACTIVE_INVISIBLE']
								= isset($_POST['INACTIVE_INVISIBLE']);

	$partyid = $_POST['PARTYIDS'] ? $_POST['PARTYIDS'][0] : 0;

	$setlist['PARTYID'] = $partyid;

	$adids	= $_POST['ADIDS'];
	$partyids = $_POST['PARTYIDS'];

	if ($organizationid = $_POST['ORGANIZATIONID']) {
		# track all events of organization

		$extra_partyids = db_same_hash(['connect', 'party'], '
			SELECT ASSOCID
			FROM connect
			JOIN party ON PARTYID = ASSOCID
			WHERE MAINTYPE = "organization"
			  AND MAINID = '.$organizationid.'
			  AND ASSOCTYPE = "party"'
		);
		if ($extra_partyids === false) {
			return false;
		}
		if ($extra_partyids) {
			$partyids = array_unique(array_merge($partyids, $extra_partyids));
		}
	}

	[$instr, /* $valstr */, $bincmp] = inserts_values_and_bincmp($setlist);

	if ($pixelid) {
		if (!db_insert('pixel_log','
			INSERT INTO pixel_log
			SELECT * FROM pixel
			WHERE NOT '.$bincmp.'
			  AND PIXELID = '.$pixelid)
		) {
			return false;
		}
		if (db_affected()) {
			if (!db_update('pixel','
				UPDATE pixel SET
					MUSERID	='.CURRENTUSERID.',
					MSTAMP	='.CURRENTSTAMP.',
					'.$instr.'
				WHERE PIXELID='.$pixelid)
			) {
				return false;
			}
		}
		register_notice('pixel:notice:changed_LINE');
	} else {
		if (!db_insert('pixel','
			INSERT INTO pixel SET
				CUSERID	='.CURRENTUSERID.',
				CSTAMP	='.CURRENTSTAMP.',
				'.$instr)
		) {
			return false;
		}
		register_notice('pixel:notice:added_LINE');
		$pixelid = $_REQUEST['sID'] = db_insert_id();
	}

	foreach ([
		'party'	=> 'partyids',
		'ad'	=> 'adids',
	] as $element => $items) {
		if (empty($$items)) {
			continue;
		}
		$pixel_setlist = [];
		foreach ($$items as $itemid) {
			$pixel_setlist[$itemid] = '('.$pixelid.', '.$itemid.', '.CURRENTSTAMP.', '.CURRENTUSERID.')';
		}

		db_insert('pixel'.$element.'_log','
			INSERT INTO pixel'.$element.'_log
			SELECT *,'.CURRENTSTAMP.','.CURRENTUSERID.' FROM pixel'.$element.'
			WHERE PIXELID = '.$pixelid.'
			  AND '.$element.'ID NOT IN ('.($itemidstr = implodekeys(', ', $pixel_setlist)).')'
		)
		&&
		db_delete('pixel'.$element, '
			DELETE FROM pixel'.$element.'
			WHERE PIXELID = '.$pixelid.'
			  AND '.$element.'ID NOT IN ('.$itemidstr.')'
		)
		&&
		db_insert('pixel'.$element, '
			INSERT IGNORE INTO pixel'.$element.' (PIXELID, '.$element.'ID, CSTAMP, CUSERID)
			VALUES '.implode(', ', $pixel_setlist)
		);
	}

	update_pixel_stopstamp($pixelid, no_log: true);

	flush_active_pixels();

	return $pixelid;
}

function pixel_activate(bool $activate): int|false {
	if (!require_admin('pixel')
	||	!($pixelid = require_idnumber($_REQUEST, 'sID'))
	) {
		return false;
	}
	if (!db_insert('pixel_log','
		INSERT INTO pixel_log
		SELECT * FROM pixel
		WHERE ACTIVE!='.($activate ? 1 : 0).'
		  AND PIXELID='.$pixelid)
	) {
		return false;
	}
	if (!db_affected()) {
		error_log('no change needed');
		return $pixelid;
	}
	if (!db_update('pixel','
		UPDATE pixel SET
			ACTIVE	='.($activate ? 1 : 0).',
			MUSERID	='.CURRENTUSERID.',
			MSTAMP	='.CURRENTSTAMP.'
		WHERE ACTIVE!='.($activate ? 1 : 0).'
		  AND PIXELID='.$pixelid)
	) {
		return false;
	}
	flush_active_pixels();
	return $pixelid;
}
