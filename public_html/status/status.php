<?php

define('CURRENTSTAMP',time());

require_once '../_date.inc';
require_once '../_db.inc';
require_once '../_helper.inc';
require_once '../_layout.inc';
require_once '../_require.inc';
require_once '../_sort.inc';
require_once '../_currentuser.inc';
require_once '../_nocache.inc';

send_no_cache_headers();

header('Content-Type: text/html; charset=windows-1252');

function bail($errno) {
	http_response_code($errno);
	exit;
}

if (!isset($_SERVER['eELEMENT'])) {
	bail(400);
}

$element = $_SERVER['eELEMENT'];
$currentuser = new _currentuser(TINY_USER | SKIP_SETTINGS);

if (!have_admin($element)) {
	bail(403);
}

// FIXME: late ads are not shown!

require_once '_distribution.inc';

bail(show_distribution_status($element));
