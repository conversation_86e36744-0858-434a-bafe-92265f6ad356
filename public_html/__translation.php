<?php

require_once '_memcache.inc';

const DONT_ESCAPE			= 0x1;
const DO_NL2BR				= 0x2;
const DO_COMBINE_2BR		= 0x4;
const DO_UBB				= 0x8;
const RETURN_FALSE			= 0x10;
const FORCE_READ			= 0x20;
const DO_UBBFLAT			= 0x40;
const DO_NBSP				= 0x80;
const DO_CAPFIRST			= 0x100;
const DO_CONDITIONAL		= 0x200;
const IS_JAVASCRIPT			= 0x400;
const DONT_WARN_MISSING		= 0x800;
const RETURN_UTF8			= 0x1000;
const KEEP_EMPTY_KEYWORDS	= 0x2000;
const DO_STARS				= 0x4000;

# FIXME: when set to false, if not in context, strings cannot be found!
const BUILD_LOCALE = true;

const LANG_ID_ENGLISH	= 0;
const LANG_ID_DUTCH		= 1;
const LANG_ID_FRENCH	= 2;
const LANG_ID_GERMAN	= 3;
const LANG_ID_SPANISH	= 4;
const LANG_ID_ITALIAN	= 5;
const LANG_ID_POLISH 	= 6;
const LANG_ID_PORTUGESE	= 7;

const LOCALES = [
	# LANGID as key
	LANG_ID_ENGLISH		=> ['en','US','en_US','en-us'],	#'Do you speak English?',
	LANG_ID_DUTCH		=> ['nl','NL','nl_NL','nl-nl'],	#'Spreek je Nederlands?',
	LANG_ID_FRENCH		=> ['fr','BE','fr_BE','fr-be'],	#'Parlez-vous fran&ccedil;ais?',
	LANG_ID_GERMAN		=> ['de','DE','de_DE','de-de'],
	LANG_ID_SPANISH		=> ['es','ES','es_ES','es-es'],
	LANG_ID_ITALIAN		=> ['it','IT','it_IT','it-it'],
	LANG_ID_POLISH		=> ['pl','PL','pl_PL','pl-pl'],
	LANG_ID_PORTUGESE	=> ['pt','PT','pt_PT','pt-pt'],
	# don't forget to update /etc/locale.gen and run locale-gen
];

initialize_locale();

function get_language_codes(): array {
	$res = [];
	foreach (LOCALES as $locale) {
		$res[$code = $locale[0]] = $code;
	}
	return $res;
}

function get_current_langid(?int $langid = null): ?int {
	static $__langid = null;
	if ($langid !== null) {
		$__langid = $langid;
	}
	return $__langid;
}

function get_current_locale(): string {
	return LOCALES[get_current_langid()][2];
}

function get_last_langid(int $userid): int {
	return db_single('lastlanguage', '
		SELECT LANGID
		FROM lastlanguage
		WHERE USERID = '.$userid
	) ?: 1;
}

function dont_set_translation_consts(?bool $dont = null): ?bool {
	static $__dont_set_translation_consts = null;
	if ($dont) {
		$__dont_set_translation_consts = true;
	}
	return $__dont_set_translation_consts;
}

function initialize_locale(int|string|null $arg = null): void {
	if ($arg === null) {
		$langid = false;
		if (defined('LANGUAGE_FOR_USER')) {
			$langid = db_single_int('lastlanguage','
				SELECT LANGID
				FROM lastlanguage
				WHERE USERID = '.LANGUAGE_FOR_USER);
		}
		if ($langid === false) {
			$langid = find_current_language();
		}
	} elseif (is_int($arg)) {
		if (get_current_langid() === $arg) {
			return;
		}
		$langid = $arg;

	} else { // $arg is_string
		$langid = false;
		foreach (LOCALES as $local_langid => $spec) {
			if ($arg === $spec[0]) {
				$langid = $local_langid;
				break;
			}
		}
		if ($langid === false) {
			mail_log("unsupported string argument '$arg' to initialize_locale", get_defined_vars());
			return;
		}
	}

	get_current_langid($langid);

	$current_locale = LOCALES[$langid][2];

	if (!CLI && !dont_set_translation_consts()) {
		define('CURRENTLANGID',   $langid);
		# store region in cookie too in future
		define('CURRENTLANGUAGE', LOCALES[$langid][0]);
		define('CURRENTLOCALE',   $current_locale);
	}
	if (!setlocale(LC_ALL, $current_locale)) {
		log_sporadically(
			'translation:setlocale_LC_ALL',
			'failed to set locale to '.$current_locale.
			' @ '.(CLI ? basename($_SERVER['PHP_SELF']) : $_SERVER['REQUEST_URI'])
		);
	}
	if (!setlocale(LC_MESSAGES,'en_US')
	||	!setlocale(LC_NUMERIC,	'en_US')	# need decimal point for various outputs
	) {
		log_sporadically(
			'translation:setlocale_en_US',
			'failed to set locale for MESSAGES and NUMERIC to en_US'
		);
	}
}

function get_collator(?int $langid = null): Collator {
	static $__collators = [];
	$langid ??= CURRENTLANGID;
	$current_locale = LOCALES[$langid][2];
	return $__collators[$langid] ??= new Collator($current_locale);
}

function get_language(int $langid): string {
	return LOCALES[$langid][0];
}

function get_locale_info(): array {
	static	$__info =
			setlocale(LC_NUMERIC, get_current_locale())
		&&	($__info = localeconv())
		&&	setlocale(LC_NUMERIC, 'en_US')
		?	$__info
		:	[	# Fallback defaults in use:
				'decimal_point'	=> '.',
				'thousands_sep' => '&thinsep;',
			];
	return $__info;
}

function find_current_language(): int {
	if (!empty($_SERVER['eLANG'])) {
		$lang = $_SERVER['eLANG'];
		$set_langid = -1;
		foreach (LOCALES as $langid => $locale) {
			if ($lang === $locale[0]) {
				$set_langid = $langid;
				break;
			}
		}
		if ($set_langid !== -1) {
			require_once '_cookie.inc';
			setflockcookie('FLOCK_LANGUAGE', $set_langid, COOKIE_SET_LONG_LIVED);
		}
		require_once '_http_status.inc';
		see_other(substr($_SERVER['REQUEST_URI'], 3));
	}

	if ($fb_locale = $_SERVER['HTTP_X_FACEBOOK_LOCALE'] ?? $_SERVER['X-Facebook-Locale'] ?? null) {
		require_once '_helper.inc';
		foreach (LOCALES as $langid => $locale) {
			if ($locale[2] === $fb_locale) {
				return $langid;
			}
		}
	}
	$locales_cnt = count(LOCALES);

	if (isset($_REQUEST['sELEMENT'], $_REQUEST['ACTION'], $_REQUEST['sID'])
	&&	$_REQUEST['sELEMENT'] === 'language'
	&&	$_REQUEST['ACTION']   === 'set'
	) {
		$langid = (int)$_REQUEST['sID'];
		if ($langid >= $locales_cnt) {
			$langid = LANG_ID_DUTCH;
		}
		require_once '_cookie.inc';
		require_once '_http_status.inc';
		global $__last_langid;
		$__last_langid = $langid;
		setflockcookie('FLOCK_LANGUAGE', $langid, COOKIE_SET_LONG_LIVED);
		see_other('/language');
	}

	if (isset($_COOKIE['FLOCK_LANGUAGE'])) {
		require_once '_require.inc';
		if (!have_number($_COOKIE, 'FLOCK_LANGUAGE')) {
			$langid = $_COOKIE['FLOCK_LANGUAGE'] === 'nl' ? LANG_ID_DUTCH : LANG_ID_ENGLISH;
		} else {
			$langid = (int)$_COOKIE['FLOCK_LANGUAGE'];
			if ($langid > $locales_cnt) {
				$langid = LANG_ID_DUTCH;
			}
			return $langid;
		}
	} else {
		# too many users have browser in English but expect site to be Dutch
		$langid = 1; #detect_language();
	}
	return $langid;
}

//	function detect_language(): int {
//		if (empty($_SERVER['HTTP_ACCEPT_LANGUAGE'])) {
//			return 1;
//		}
//		foreach (explode(',', $_SERVER['HTTP_ACCEPT_LANGUAGE']) as $spec) {
//			if (str_contains($spec, ';q=')) {
//				[$spec] = explode(';q=', $spec);
//			}
//			if (false !== ($minus = strpos($spec, '-'))) {
//				$spec = substr($spec, 0, $minus);
//			}
//			foreach (LOCALES as $langid => $info) {
//				if ($spec === $info[0]) {
//					return $langid;
//				}
//			}
//		}
//		return 1;
//	}

function __(
	string			$key,
	int|array|null	$arg1   = null,
	int|array|null	$arg2   = null,
	?int			$langid = null,
): string|false {
	$flags = 0;
	$map = [];
	if ($arg1 !== null) {
		foreach ([$arg1, $arg2] as $arg) {
			if (!$arg) {
				continue;
			}
			if (is_int($arg)) {
				$flags = $arg;
			} elseif (is_array($arg)) {
				$map = $arg;
			} else {
				mail_log('__() bad argument');
			}
		}
	}
	$newuse = false;
	$text = ($map || $flags & DO_CONDITIONAL)
	?	__translation_parse($key, $flags, $map, $newuse, $langid)
	:	__translation_get  ($key, $flags, $newuse, $langid);
	if (!$text) {
		if ($flags & RETURN_FALSE) {
			return false;
		}
		if (!$map) {
			return $key;
		}
		if ($flags & DONT_ESCAPE) {
			return $key.' ('.get_r($map).')';
		}
		return $key.' ('.escape_specials(get_r($map)).')';
	}
	# NOTE: Input in (translation)map should be in UTF-8, so when the translation texts are also UTF-8,
	#		A single escape_utf8() can be used to make sure the output is safe.
	$utf8 = false;
	if ($flags & RETURN_UTF8) {
		$text = win1252_to_utf8($text);
		$utf8 = true;
	}
	if ($flags) {
		if ($flags & DO_CAPFIRST) {
			if ($utf8) {
				mail_log('DO_CAPFIRST does not work with RETURN_UTF8');
			}
			if ($findchar = ($text[0] === '[') ? '[' : ($text[0] === '(' ? ')' : '')) {
				$firstchar = strpos($text, $findchar, 1);
				if ($firstchar) {
					++$firstchar;
					if ($text[$firstchar] === ' ') {
						++$firstchar;
					}
					$text = substr($text, 0, $firstchar).ucfirst($text[$firstchar]).substr($text, $firstchar + 1);
				}
			} elseif (str_starts_with($text, "'s ")) {
				$text = substr($text, 0, 3).ucfirst(substr($text, 3));
			} else {
				$text = ucfirst($text);
			}
		}
		if ($flags & DO_UBB) {
			require_once '_ubb.inc';
			$context_element = $map['UBBCONTEXT'] ?? '';
			$context_id = $context_element ? ($map['UBBCONTEXTID'] ?? 0) : 0;
			$text = make_all_html($text, UBB_NO_HILITING | ($flags & DO_COMBINE_2BR ? UBB_COMBINE_2BR : 0), $context_element, $context_id);

		} elseif ($flags & DO_UBBFLAT) {
			require_once '_ubb.inc';
			$text = flat_nolinks($text, $utf8 ? UBB_UTF8 : 0);

		} else {
			if ($flags & IS_JAVASCRIPT) {
				if (null === ($new_text = preg_replace(
						'"(\r?\n)"'.($utf8 ? 'u' : ''),
						"\\n",
						str_replace('"','\\"',str_replace("'","\\'",$text))))
				) {
					preg_failure($text);
				} else {
					$text = $new_text;
				}
			} elseif (!($flags & DONT_ESCAPE | IS_JAVASCRIPT)) {
				$text = escape_specials($text, $utf8);
			}
			if ($flags & (DO_NL2BR | DO_COMBINE_2BR)) {
				if ($flags & DO_COMBINE_2BR) {
					$text = nlnl2cmbr($text);
				} else {
					$text = nl2br($text);
				}
			}
			if ($flags & DO_NBSP) {
				$text = str_replace(' ','&nbsp;',$text);
			}
		}
	} else {
		$text = escape_specials($text, $utf8);
	}
	return $text;
}

function __C(
	string         $key,
	int|array|null $arg1 = null,
	int|array|null $arg2 = null,
): string|false {
	if ($arg1 === null
	||	is_int($arg1)
	) {
		$arg1 |= DO_CAPFIRST;

	} elseif (
		$arg2 === null
	||	is_int($arg2)
	) {
		$arg2 |= DO_CAPFIRST;
	}

	return __($key, $arg1, $arg2);
}

function __translation_get(
	string	 $key,
	int		 $flags		= 0,
	?int	&$new_use	= null,
	?int	 $langid	= null,
): string|false {
	static $translation_list = [];
	if (isset($translation_list[$key])) {
		return $translation_list[$key];
	}
	$file	  = '';
	$function = '';

	foreach (debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 4) as $tracepoint) {
		if (!isset($tracepoint['file'])) {
			# somehow process_ubb_tag has no file
			continue;
		}
		if (!$file
		&&	!str_contains($tracepoint['file'], '__translation')
		) {
			$file = $tracepoint['file'];
			continue;
		}
		if ($file) {
			$function = $tracepoint['function'];
			break;
		}
	}
	if ($function === '__'
	||	$function === '__C'
	) {
		$function = '';
	}
	#$file = preg_replace('"^/home/<USER>/public_html/"', '', $file);
	if (!preg_match('"^.*?/(?:party|sandbox)(?:_(?:working|too|clean))?/(?P<dir>public_html|maintenance)/(?P<path>.+)$"', $file, $match)) {
		_error('file not matched in __translation_get: '.$file);
		return $key;
	}
	$file = ($match['dir'] === 'public_html' ? '' : $match['dir'].'/').$match['path'];
	$file_crc = crc32($file);
	$function_crc = crc32($function ?? '');
	if ($langid === null) {
		$langid = get_current_langid();
	}
	$cache_key = "translation:v2:$langid:$file_crc:$function_crc";
	static $__file_func = null;
	if (!isset($__file_func[$langid][$file][$function])) {
		require_once '_require.inc';
		if (!have_admin()
		&&	($translations = memcached_get($cache_key))
		) {
			$__file_func[$langid][$file][$function] = $translations;
		} else {
			if (false === ($__file_func[$langid][$file][$function] = memcached_simple_hash_if_not_admin(
				['translation', 'translationdefault', 'translationsuper'],
				['translationtext', 'translationuse', 'translationcontext', 'translationkey'], /** @lang MariaDB */ '
				SELECT KEYNAME, COALESCE('.(
					$langid !== 1
				?	"IF(ltneed.KEYID IS NOT NULL AND ltneed.BODY != '', ltneed.BODY,
						IF(ltbase.KEYID IS NOT NULL AND ltbase.BODY != '', ltbase.BODY, ltdutch.BODY))"
				:	"IF(ltneed.KEYID IS NOT NULL AND ltneed.BODY != '', ltneed.BODY, ltbase.BODY)"
				).', "")
				FROM translationcontext
				JOIN translationuse USING (CONTEXTID)
				JOIN translationkey USING (KEYID)
				LEFT JOIN translationtext AS ltbase USING (KEYID)'.(
					$langid !== 1
				?	' LEFT JOIN translationtext AS ltdutch
						 ON ltdutch.LANGID = 1
						AND ltdutch.KEYID = ltbase.KEYID'
				:	null)."
				LEFT JOIN translationtext AS ltneed
					   ON  ltneed.LANGID = $langid
					  AND ltneed.KEYID = ltbase.KEYID
				WHERE ltbase.LANGID = 0
				  AND FILENAME = '".addslashes($file)."'
				  AND FILECRC = $file_crc
				  AND FUNCTIONNAME = '".addslashes($function)."'
				  AND FUNCTIONCRC = $function_crc",
				HALF_HOUR,
				$cache_key))
			) {
				return false;
			}
		}
		if ($mapping = $__file_func[$langid][$file][$function]) {
			$translation_list += $mapping;
		}
	}
	if (isset($translation_list[$key])) {
		return $translation_list[$key];
	}
	$result = false;
	if (BUILD_LOCALE
		&& (	!($flags & RETURN_FALSE)
			||	 ($flags & FORCE_READ)
		)
	) {
		static $context_lookup = null;
		if (!db_lock_tables('translationcontext', DBLOCK_WRITE)) {
			return false;
		}
		if (false !== ($context_id = ($context_lookup[$file][$function] ??= db_single_int('translationcontext','
			SELECT CONTEXTID
			FROM translationcontext
			WHERE FILENAME		= "'.addslashes($file).'"
			  AND FILECRC		= '.$file_crc.'
			  AND FUNCTIONNAME	= "'.addslashes($function).'"
			  AND FUNCTIONCRC	= '.$function_crc)))
		&&	!$context_id
		) {
			/** @noinspection NotOptimalIfConditionsInspection */
			if (!db_insert('translationcontext', "
				INSERT INTO translationcontext SET
					FILENAME		= '".addslashes($file)."',
					FILECRC			= $file_crc,
					FUNCTIONNAME	= '".addslashes($function)."',
					FUNCTIONCRC		= $function_crc")
			||	!($context_id = db_insert_id())
			) {
				return false;
			}
			$context_lookup[$file][$function] = $context_id;
		}
		db_unlock_tables('translationcontext');
		if ($context_id) {
			$key_name_str = '"'.addslashes($key).'"';
			if (  $flags & FORCE_READ
			||	!($flags & RETURN_FALSE)
			) {
				$result = db_single(['translationtext', 'translationkey'], "
					SELECT BODY
					FROM translationtext
					JOIN translationkey USING (KEYID)
					WHERE KEYNAME = $key_name_str
					  AND LANGID  = $langid"
				);
			}
			if ($result
			||	!($flags & RETURN_FALSE)
			) {
				if (db_insert('translationkey', 'INSERT IGNORE INTO translationkey SET KEYNAME = '.$key_name_str)
				&&	(	$keyid
					=	db_affected()
					?	db_insert_id()
					:	db_single_int('translationkey', 'SELECT KEYID FROM translationkey WHERE KEYNAME = '.$key_name_str)
					)
				) {
					$new_use = $keyid;
						db_insert('translationuse', "
						INSERT IGNORE INTO translationuse SET
							CONTEXTID	= $context_id,
							KEYID		= $keyid")
					&&	db_insert('translationtext', "
						INSERT IGNORE INTO translationtext SET
							BODY	= '',
							KEYID	= $keyid");
					$result = db_single('translationtext', "
						SELECT BODY
						FROM translationtext
						WHERE KEYID  = $keyid
						  AND LANGID = $langid"
					);
				}
			}
		}
		memcached_delete($cache_key);
	}
	if ($result === null) {
		$result = '';
	}
	return $translation_list[$key] = $result;
}

/** @noinspection t */
function __translation_parse(
	string	$key,
	int		$flags,
	array	$map,
	?int   &$new_use = null,
	?int	$langid	 = null,
): string|false|null {
	$input = __translation_get($key, $flags, $new_use, $langid);
	/** @noinspection NotOptimalIfConditionsInspection */
	if ($new_use
	&&	db_delete('translationmapspec','
		DELETE FROM translationmapspec
		WHERE KEYID = '.$new_use)
	&&	$map
	) {
		foreach ($map as $replace_key => $value) {
			if ($value === null) {
				mail_log('null value in map value', get_defined_vars());
				$value = '';
			}
			if (!db_insert('translationmapspec','
				INSERT INTO translationmapspec SET
					ARGUMENT = "'.addslashes($replace_key).'",
					VALUE	 = "'.addslashes(win1252_to_utf8($value)).'",
					KEYID	 = '.$new_use)
			) {
				return false;
			}
		}
	}
	if (!$input) {
		return false;
	}
	return parse_text($input, $map, $flags);
}

function parse_text(
	string	$input,
	?array	$map	= null,
	int		$flags	= 0,
	?int	$langid	= null,
): ?string {
	global $translationmap, $translationlangid;
	$translationmap = $map ?: [];
	$translationlangid = $langid;

	if (null === ($new_text = preg_replace_callback(
		[
			'"{IF\s+[^}]*[&|<=>!][^}]*}"S',	# <- preprocess
			'"%(?<variable>[A-Z_]+)%"S',
			'"({IF)\s+(.*?)}(.*?)(?:{ELSE}(.*?))?{ENDIF}"sSm',
			'"({SWITCH)(?:\s+(.*?))?}(.*?){ENDSWITCH}"sSm',
			'"({SHOWLIST)\s+([a-z_]+)\s+(?:(\D+)\s+)?([0-9,]+)\s*}"Sm',
			'"({ELEMENTNAME(?:CAP)?)\s+([a-z_]+)\s*}"Sm',
			'"({SHOWSTARS)\s*(?<userid>\d+)}"Sm',
		],
		static function(array $matches) use ($flags): ?string {
			if (!isset($matches[1])) {
				if (null === ($new_subject = preg_replace('"([&|<=>!]+)"', chr(0x1F).'$1'.chr(0x1F), $subject = $matches[0]))) {
					preg_failure($subject);
				} else {
					$subject = $new_subject;
				}
				return $subject;
			}
			global $translationmap;
			if (isset($matches['variable'])) {
				$result = match($matches['variable']) {
					'CURRENTUSERID' 	=> CURRENTUSERID,
					'CURRENTUSERNICK'	=> $GLOBALS['currentuser']->row['NICK'],
					'CURRENTSECTION'	=> $_REQUEST['sELEMENT'],
					# CURRENTLANGID'	=> CURRENTLANGID,
					default				=> $default_return = array_key_exists($matches[1], $translationmap)
										 ?	$translationmap[$matches[1]]
										 :	(	$flags & KEEP_EMPTY_KEYWORDS
											?	$matches[0]
										 	:	''
											)
				};
				if (!empty($default_return)
				&&	is_array($default_return)
				) {
					mail_log('default_return is an array', get_defined_vars());
					return 'BROKEN';
				}
				return $result;
			}
			switch ($matches[1]) {
			case '{SHOWSTARS':
				return "[contactstars={$matches['userid']}]";

			case '{ELEMENTNAMECAP':
				$flags = DO_CAPFIRST;
				# fall through
			case '{ELEMENTNAME':
				return __('element:'.$matches[2], DONT_ESCAPE | ($flags ?? 0));

			case '{ELEMENTSNAMECAP':
				$flags = DO_CAPFIRST;
				# fall through
			case '{ELEMENTSNAME':
				return __('elements:'.$matches[2], DONT_ESCAPE | ($flags ?? 0));

			case '{IF':
				switch ($test = $matches[2]) {
				case 'HAVEUSER':
					$testresult = have_user();
					break;
				case 'HAVERELATION':
					$testresult = have_relation();
					break;
				default:
					if (str_contains($test,"\x1F")) {
						if (preg_match('"^(?:\d+(?:\.\d+)?\s*'.chr(0x1F).'[<=>!]+'.chr(0x1F).'\s*\d+(?:\.\d+)?|(?:'.chr(0x1F).'!'.chr(0x1F).')?\s*\d+)$"', $test)) {
							$testresult = eval('return '.str_replace("\x1F",'',$test).';');
							break;
						}
						if (preg_match('"^(.*?)\s*'.chr(0x1F).'([&|<=>!]+)'.chr(0x1F).'\s*(.*)$"', $test, $match)) {
							$cmp = trim($match[2]);
							$to_eval =
								$cmp === '!'
							?	'return '.$cmp.'"'.addslashes($match[3]).'";'
							:	'return "'.addslashes($match[1]).'"'.$cmp.'"'.addslashes($match[3]).'";';

							$testresult = eval($to_eval);

							break;
						}
						if (preg_match('"^\s*'.chr(0x1F).'!'.chr(0x1F).'\s*(.*)$"',$test,$match)) {
							$test = mytrim($match[1]);
							$testresult = empty($test);
							break;
						}
						error_log('not understood: '.$test.' @ '.$_SERVER['REQUEST_URI']);
					}
					$test = mytrim($test);
					$testresult = !empty($test);
					break;
				}
				if ($testresult) {
					if (!empty($matches[3])) {
						return $matches[3];
					}
					return null;
				}
				if (!empty($matches[4])) {
					return $matches[4];
				}
				return null;

			case '{SHOWLIST':
				$element = $matches[2];
				$ids = explode(',',$matches[4]);
				global $translationlangid;
				$lastsep = $translationlangid === null ? ', ' : __('listseparator:and');
				$sep = '=';
				if ($extra = $matches[3]) {
					if (str_contains($extra, 'noitalics')) {
						$sep = ':=';
						$extra= trim(str_replace('noitalics', '', $extra));
					}
					if ($extra) {
						$lastsep = $extra;
					}
				}
				$cnt = count($ids);
				$result = null;
				foreach ($ids as $id) {
					$result .= "[$element$sep$id]";
					--$cnt;
					if ($cnt >= 2) {
						$result .= ', ';
					} elseif ($cnt === 1) {
						$result .= $lastsep;
					}
				}
				return $result;

			default:
				$start_tag = strpos($matches[3], '{');
				while ($start_tag !== false) {
					if (!($end_tag = strpos($matches[3], '}', $start_tag + 1))) {
						break;
					}
					$tag = substr($matches[3], $start_tag + 1, $end_tag - $start_tag - 1);
					$start_tag = strpos($matches[3], '{', $end_tag + 1);
					$val = $start_tag ? substr($matches[3], $end_tag + 1, $start_tag - $end_tag - 1) : substr($matches[3], $end_tag + 1);
					if ($matches[2]
					||	is_string($matches[2])
					&&	'' !== $matches[2]
					) {
						if (preg_match('"^(?<case>DEFAULT|CASE\s+)(?<case_value>.*)$"', $tag, $case_match)
						&&	(	$case_match['case'] === 'DEFAULT'
							||	$case_match['case_value'] === $matches[2])
						) {
							return $val;
						}
					} elseif (preg_match('"^(DEFAULT|CASE\s+(?:(\w+)\s*([<=>!]+)\s*(\w+)|(!\s*\d+))\s*)$"', $tag, $case_match)) {
						if ($case_match[1] === 'DEFAULT') {
							return $val;
						}
						if (!isset($case_match[4])) {
							error_log_r($case_match,'case_match');
						}
						if (isset($case_match[5])) {
							if (eval('return '.$case_match[5].';')) {
								return $val;
							}
						} elseif (
							is_number($matches[2])
						&&	is_number($matches[4])
						) {
							if (eval('return '.$case_match[2].$case_match[3].$case_match[4].';')) {
								return $val;
							}
						} elseif (eval('return \''.$case_match[2].'\''.$case_match[3].'\''.$case_match[4].'\';')) {
							return $val;
						}
					}
				}
			}
			return null;
		},
		$input))
	) {
		preg_failure($input);
	} else {
		$input = $new_text;
	}
	return $input;
}

/** @noinspection t */
function store_translation(string $key, string $val): bool {
	# USED by faq, guide, policy

	if (!($val = trim($val))) {
		return true;
	}
	$keyname_slashed = '"'.addslashes($key).'"';
	if (!db_insert('translationkey',"INSERT IGNORE INTO translationkey SET KEYNAME = $keyname_slashed")
	||	(false === ($keyid =
			db_affected()
		?	db_insert_id()
		:	db_single('translationkey', "SELECT KEYID FROM translationkey WHERE KEYNAME = $keyname_slashed")
	))) {
		return false;
	}
	if (!$keyid) {
		register_error('translation:error:nonexistent_keyname_LINE', ['KEYNAME' => $key]);
		return false;
	}
	$langid = get_current_langid();
	$had_old_body = db_single('translationtext', "
		SELECT 1
		FROM translationtext
		WHERE BODY != ''
		  AND KEYID = $keyid
		  AND LANGID = $langid"
	);
	if (!db_insupd('translationtext',"
		INSERT INTO  translationtext SET
			KEYID	= $keyid,
			LANGID	= $langid,
			BODY	= '".($slashed_val = addslashes($val))."'
		ON DUPLICATE KEY UPDATE
			BODY	= VALUES(BODY)")
	) {
		return false;
	}
	if (db_affected()) {
		if (!db_insert('translationtexthistory', "
			INSERT INTO translationtexthistory SET
				KEYID	= $keyid,
				LANGID	= $langid,
				BODY	= '$slashed_val',
				MUSERID	= ".CURRENTUSERID.',
				MSTAMP	= '.CURRENTSTAMP)
		) {
			return false;
		}
		if ($had_old_body
		&&	$langid === 1
		&&	!db_replace('translationchanged',"
				REPLACE INTO translationchanged SET
					LANGID	= $langid,
					KEYID	= $keyid,
					MSTAMP	= ".CURRENTSTAMP)
		) {
			return false;
		}
	}
	return true;
}
