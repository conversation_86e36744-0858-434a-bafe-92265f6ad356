<?php

declare(strict_types=1);

require_once 'vendor/autoload.php';

use Facebook\WebDriver\Chrome\ChromeOptions;
use Facebook\WebDriver\Remote\DesiredCapabilities;
use Facebook\WebDriver\Remote\RemoteWebDriver;
use Facebook\WebDriver\WebDriverDimension;

function get_webdriver(string $proxy = ''): RemoteWebDriver|false {
	static $__webdriver;
	if (isset($__webdriver)) {
		return $__webdriver;
	}
	$arguments = [
		'--headless',
		'--disable-gpu',
		'--user-agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/112.0.0.0 Safari/537.3"',
		'--disable-blink-features=AutomationControlled',
	];
	if ($proxy) {
		$arguments[] = '--proxy-server='.$proxy;
	}
	try {
		$chrome_options = new ChromeOptions();
		$chrome_options->addArguments($arguments);

		$chrome_options->setExperimentalOption('excludeSwitches', ['enable-automation']);
		$chrome_options->setExperimentalOption('useAutomationExtension', false);

		$capabilities = DesiredCapabilities::chrome();
		$capabilities->setCapability(ChromeOptions::CAPABILITY, $chrome_options);

		# There is no Google Chrome for arm64, which sandbox now uses.
		# So we install chromedriver on an external machine, to server the requests
		# Machine here 2a10:3781:2354:1::1000
		# See party/config/webserver/run_chromedriver_on_amd64_mac

		$chromedriver_host = SERVER_SANDBOX ? '[2a10:3781:2354:1::1000]' : 'localhost';

		if ($__webdriver = RemoteWebDriver::create('http://'.$chromedriver_host.':9515/', $capabilities, 5)) {
			register_shutdown_function(static function() use ($__webdriver) {
				$__webdriver->quit();
			});
			if (function_exists('pcntl_signal')) {
				pcntl_signal(SIGINT, static function() use (&$__webdriver) {
					error_log('quiting after SIGINT __webdriver width session id: '.$__webdriver->getSessionID());
					$__webdriver->quit();
					$__webdriver = null;
				});
			}
			$__webdriver->manage()->window()->setSize(new WebDriverDimension(1920, 1080))->maximize();
		}
	} catch (Exception $exception) {
		mail_log($error = 'Exception trying to get_webdriver('.$proxy.'): '.$exception->getMessage());
		error_log($error);
	}
	if (!$__webdriver) {
		$__webdriver = false;
	}
	return $__webdriver;
}
