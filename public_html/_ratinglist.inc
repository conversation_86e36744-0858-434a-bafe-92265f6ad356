<?php

require_once '_rating.inc';
require_once 'defines/artist.inc';

if (!test_rating_commit()) {
	test_rating_accept();
}

function test_rating_commit(): bool {
	return	have_user()
		&&	$_REQUEST['ACTION'] === 'rate'
		&&	isset(RATINGABLES[(string)$_REQUEST['sELEMENT']])
		&&	$_REQUEST['sID']
		&&	commit_rating();
}

function test_rating_accept(): bool {
	return	$_REQUEST['ACTION'] === 'rating'
		&&	isset(RATINGABLES[$element = (string)$_REQUEST['sELEMENT']])
		&&	require_admin($table = $element.'_rating')
		&&	require_idnumber($_REQUEST, 'sID')
		&&	require_idnumber($_REQUEST, 'subID')
		&&	require_element($_REQUEST, 'SUBACTION', ['deny', 'accept'])
			# NOTE: Need explicit specification of field to insert into rating_log table, because of the GENERATED / STORED field
		&&	db_insert("{$table}_log", "
			INSERT INTO `{$table}_log` (USERID, ID, BODY, MSTAMP, MUSERID, CSTAMP,  RATINGID, ACCEPTED)
			SELECT USERID, ID, BODY, MSTAMP, MUSERID, CSTAMP,  RATINGID, ACCEPTED FROM `$table`
			WHERE RATINGID = ".($subID = $_REQUEST['subID']))
		&&	db_update($table,/** @lang MariaDB */ '
			UPDATE '.$table.' SET
				MUSERID	 = '.CURRENTUSERID.',
				MSTAMP	 = '.CURRENTSTAMP.',
				ACCEPTED = '.($_REQUEST['SUBACTION'] === 'accept' ? "b'1'" : "b'0'").'
			WHERE RATINGID = '.$subID)
		&&	page_changed($element, $_REQUEST['sID']);
}

class _ratinglist {
	private array	$votes;
	private int		$vote_total;
	private array	$vote_part;
	private float	$vote_max;
	private bool	$id_sent		= false;
	private array	$item;

	public static function show_stats_row(bool $restart = true): void {
		if (!($stats = self::get_stats())
		||	!$stats['OK_CNT']
		) {
			return;
		}
		$hide_from = SMALL_SCREEN ? 20 : setting('HIDE_RATINGS_STARTING_FROM');
		$show_size = have_admin(($element = $_REQUEST['sELEMENT']).'_rating') ? $stats['CNT'] : $stats['OK_CNT'];

		$show_ratings_link =
			$_REQUEST['ACTION'] !== 'ratings'
		&&	$_REQUEST['ACTION'] !== 'rating'
		&&	(	$show_size >= $hide_from
			||	!(setting('SHOW_RATINGS') & (1 << constant('RATING_'.strtoupper($element))))
			);

		$restart ? layout_restart_reverse_row() : layout_start_reverse_row();
		$close_tag = '</a>';
		ob_start();
		?><a href="/<?= $element ?>/<? echo $_REQUEST['sID'];
		if ($show_ratings_link) {
			?>/ratings<?
		} elseif (isset(USE_URLTITLE[$element])) {
			?>:<? echo get_current_url_title();
		}
		?>#ratings"><?
		$open_tag = ob_get_flush();
		echo $show_size, $close_tag;
		layout_value_field();
		echo $open_tag, element_name('rating',$show_size), $close_tag;
		if (!$restart) {
			layout_stop_row();
		}
	}
	final public function item(array $item): void {
		$this->item = $item;
	}
	public static function get_stats(): array {
		static $__stats;
		if (!isset($__stats[$what = ($element = $_REQUEST['sELEMENT']).'_rating'][$id = $_REQUEST['sID']])) {
			$__stats[$what][$id] = memcached_single_assoc($what, "
				SELECT	COUNT(IF(ACCEPTED, 1, NULL)) AS OK_CNT,
						COUNT(*) AS CNT
				FROM `$what`
				WHERE ID = $id",
				TEN_MINUTES,
				get_ratings_cnt_key($element, $id)
			) ?: [];
		}
		return $__stats[$what][$id];
	}
	public static function get_my_comment(): array|false|null {
		if (!have_user()) {
			return null;
		}
		static $__my_comment;
		if (isset($__my_comment)) {
			return $__my_comment;
		}
		$element = $_REQUEST['sELEMENT'];
		$id		 = $_REQUEST['sID'];
		if (null === ($__my_comment = db_single_assoc($element.'_rating', "
			SELECT USERID, BODY, ACCEPTED, RATINGID, LSTAMP AS USTAMP
			FROM {$element}_rating
			WHERE ID = $id
			  AND USERID = ".CURRENTUSERID))
		) {
			$__my_comment = [];
		}
		if ($__my_comment) {
			$__my_comment['NICK'] = memcached_nick($__my_comment['USERID']);
		}
		return $__my_comment;
	}

	private function show_ratings(array $ratings, ?bool $only_buddies = null): void {
		$element   = $_REQUEST['sELEMENT'];
		$table	   = "{$element}_rating";
		$id		   = $_REQUEST['sID'];
		$have_user = have_user();
		$is_admin  = have_admin($table);

		require_once '_wrap.inc';
		foreach ($ratings as $rating) {
			if ($only_buddies !== null
			&&	CURRENTUSERID === $rating['USERID']
			) {
				continue;
			}
			if ($only_buddies) {
				if (!isset($rating['BUDDY'])) {
					continue;
				}
			} elseif (isset($rating['BUDDY'])) {
				continue;
			}
			if (!$is_admin
			&&	CURRENTUSERID !== $rating['USERID']
			&&	(	!$rating['ACCEPTED']
				||	!($user = memcached_user($rating['USERID']))
				||	 $user['STATUS'] === 'permbanned'
				)
			) {
				continue;
			}
			$vote = $this->votes[$rating['USERID']] ?? 0;

			?><tr<?
			?> class="hhl<?
			if (!$rating['ACCEPTED']) {
				?>light line-through<?
			}
			?>"<?
			if ($vote
			&&	isset($this->vote_part[$vote])	# might not be set due to cache mismatch
			) {
				require_once 'style/_coloradjust.inc';
				if (!$this->vote_max) {
					error_log_r([
						'previous'			=> debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, limit: 2)[0] ?? null,
						'vote_max'			=> $this->vote_max,
						'vote_total'		=> $this->vote_total,
						"vote_part[$vote]"	=> $this->vote_part,
					],	$_SERVER['REMOTE_ADDR']);
				}
				$ccolor = adjust_brightness(vote_bg_value($vote), $this->vote_part[$vote] / ($this->vote_max ?: 1));
				?> style="background-color: #<? printf('%06X', $ccolor) ?>;"<?
			}
			?>><td<?
			if (!SMALL_SCREEN) {
				?> class="date"<?
			}
			if ($is_admin) {
				?> id="r<?= $rating['RATINGID'] ?>"<?
			}
			?>><?

			if (!SMALL_SCREEN) {
				_date_display_numeric($rating['USTAMP'],true);//' itemprop="datePublished"');
				layout_next_cell();
			} else {
				?><div><?
			}

			if ($invisible = invisible_profile($rating['USERID'])) {
				?><span class="light"><?
			} else {
				?><a<?
				if (SMALL_SCREEN) {
					?> class="bold"<?
				}
				?> href="<?= get_element_href('user', $rating['USERID']) ?>"><?
			}
			echo replace_shy(escape_specials(wrap_nick($rating['USERID'])));
			if ($invisible) {
				?></span><?
			} else {
				?></a><?
			}

			if (SMALL_SCREEN) {
				?><time class="r date" datetime="<?= gmdate(ISO8601Z,$rating['USTAMP']) ?>"><?
				_date_display_numeric($rating['USTAMP']);
				?></time><?
				?></div><?
			} else {
				?></td><td><?
				# try without fw, how that looks
			}
			?><div class="txt"><?
			echo replace_shy(
					_smiley_replace(
						escape_utf8(
							_smiley_prepare($rating['BODY'], utf8: true)
						),
						utf8: true
					),
					utf8: true
				);

			if ($have_user) {
				?><div class="abs nowrap" style="right: .2em; top: -.25em;"><?
				if ($is_admin) {
					?><span <?= SMALL_SCREEN ? 'onclick' : 'onmouseover' ?>="hide(this); unhide(this.nextSibling);"><?
						?><img alt="<?= __('action:notify') ?>" class="lighter icon" src="<?= STATIC_HOST ?>/images/notify<?= is_high_res() ?>.png" /><?
					?></span><?
 					?><span class="hidden"><?
 					[$url_action, $action] =
						$rating['ACCEPTED']
					?	['deny','action:reject']
					:	['accept','action:accept'];

					?><a href="/<?= $element ?>/<?= $id ?>/rating/<?= $rating['RATINGID'] ?>/<?= $url_action?>#ratings"><?= __($action) ?></a><?
					?> <?
					layout_display_offense_link($rating['USERID'],$_REQUEST['sELEMENT'].'_rating',$rating['RATINGID']);
				} else {
					?><span class="light"><?
				}
				layout_display_contacts($table,$rating['RATINGID']);
				?></span><?
				?></div><?
			}
			?></div><?
			?></td><?
			?></tr><?
		}
	}

	final public function display(): bool {
		if ($_REQUEST['ACTION'] === 'comments'
		||	empty($_REQUEST['sELEMENT'])
		||	!($id = $_REQUEST['sID'])
		||	!isset(RATINGABLES[$element = $_REQUEST['sELEMENT']])
		||	!($stats = self::get_stats())
		||	false === ($ratings =
			$stats['CNT']
		?	memcached_rowuse_array("{$element}_rating", "
			SELECT	USERID, BODY, RATINGID, ACCEPTED, CSTAMP, MUSERID, MSTAMP,
					GREATEST(IF(MUSERID = USERID, MSTAMP, 0), CSTAMP) AS USTAMP
			FROM {$element}_rating AS rating
			WHERE ID = $id".(isset($_REQUEST['ONLYRECENT']) ? ' AND LSTAMP > '.(TODAYSTAMP - ONE_YEAR) : ''),
			TEN_MINUTES,
			get_ratings_key($element, $id, isset($_REQUEST['ONLYRECENT']))
		) :	[]
		)) {
			return false;
		}
		if ($show_size = have_admin($element.'_rating') ? $stats['CNT'] : $stats['OK_CNT']) {
			$hide_from = SMALL_SCREEN ? 20 : setting('HIDE_RATINGS_STARTING_FROM');
			if ($dont_show_all
			=	$_REQUEST['ACTION'] !== 'ratings'
			&&	$_REQUEST['ACTION'] !== 'rate'
			&&	(	($show_link = ($show_size >= $hide_from))
				||	!(setting('SHOW_RATINGS') & (1 << constant('RATING_'.strtoupper($element))))
				)
			) {
				$this->id_sent = true;
				?><h3 class="bold block" id="ratings"><?
				?><a href="/<?= $element ?>/<?= $id ?>/ratings#ratings"><?=
					__('ratinglist:show_ratings', ['CNT' => $show_size])
				?></a></h3><?
			}
			/** @noinspection NotOptimalIfConditionsInspection */
			if (have_user()
			&&	(require_once '_buddies.inc')
			&&	($buddies = _buddies_full_hash(CURRENTUSERID))
			) {
				foreach ($ratings as &$rating) {
					if ($rating['USERID'] === CURRENTUSERID) {
						$rating['NICK'] = memcached_nick($rating['USERID']);
						continue;
					}
					if (isset($buddies[$rating['USERID']])) {
						$have_buddies = true;
						$rating['BUDDY'] = true;
						$buddy_ratings[] = &$rating;
					} else {
						$have_non_buddies = true;
					}
				}
				unset($rating);
			} else {
				$have_non_buddies = true;
			}
			if (!$this->id_sent
			||	!empty($show_link)
			) {
				if (!empty($show_link)) {
					$show_random =
						$show_size > $hide_from
					&&	(	$element === 'party'
						||	$element === 'music'
						);
#					setting_isset(SHOW_RANDOM_RATINGS);
				}
				$is_admin = have_admin($element.'_rating');
				foreach ($ratings as &$rating) {
					if (!isset($rating['NICK'])) {
						$rating['NICK'] = memcached_nick($rating['USERID']);
					}
				}
				unset($rating);
				if (false === ($votes = memcached_simple_hash(['votenew','user_account'], "
					SELECT USERID,VOTEID
					FROM votenew
					JOIN user_account USING (USERID)
					WHERE STATUS != 'permbanned'
					  AND TYPE = '$element'
					  AND ID = $id",
					FIVE_MINUTES
				))) {
					return false;
				}
				$this->votes = $votes;
				require_once '_vote.inc';
				if (($vote_totals = get_votes($element, $id))
				&&	($this->vote_total = $total = array_pop($vote_totals))
				) {
					$max = 0;
					foreach ($vote_totals as $vote_id => $cnt) {
						$part = $cnt / $total;
						$this->vote_part[$vote_id] = $part;
						$max = max($max, $part);
					}
					$this->vote_max = $max;
				}
				?><section class="ratings"<?
				if (!$this->id_sent) {
					?> id="ratings"<?
				}
				?>><?
				?><h3 class="bold block"><?=
				__C(	empty($show_random)
					?	(	$dont_show_all
						?	'ratinglist:recent_header'
						:	'ratinglist:list_header'
						)
					:	'ratinglist:random_list_header'
					,
					$dont_show_all
					?	['CNT' => $hide_from]
					:	['CNT' => $show_size]
				)
				?></h3><?

				layout_open_table('fw'.(SMALL_SCREEN ? ' vtop' : ''));

				if ($my_rating = self::get_my_comment()) {
					$this->show_ratings([$my_rating]);
				}

				number_reverse_sort($ratings, 'USTAMP');

				if (isset($have_buddies)) {
					require_once '_heart.inc';
					string_sort($buddy_ratings, 'NICK');
					?><tr><?
						?><th class="<?= SMALL_SCREEN ? 'lpad' : 'rpad right' ?>">&darr;<?
						if (!SMALL_SCREEN) {
							?></th><th colspan="2"><?
						} else {
							?> <?
						}
						echo Eelement_plural_name('buddy') ?> <? show_heart(Eelement_name('buddy')) ?></th><?
					?></tr><?
					$this->show_ratings($buddy_ratings, true);
				}
				if (isset($have_non_buddies)) {
					if ($my_rating
					||	isset($have_buddies)
					) {
						?><tr class="inbetween"><?
							?><th class="<?= SMALL_SCREEN ? 'lpad' : 'rpad right' ?>">&darr;<?
							if (!SMALL_SCREEN) {
								?></th><th colspan="2"><?
							} else {
								?> <?
							}
							echo Eelement_plural_name('other(people)') ?></th><?
						?></tr><?
					}
					if ($dont_show_all) {
						if (!empty($show_random)) {
							safe_shuffle($ratings);
							$show_ratings =	array_slice($ratings, 0, $hide_from);
							string_sort($show_ratings, 'NICK');
						} else {
							$show_ratings =	array_slice($ratings, 0, $hide_from);
						}
						$this->show_ratings($show_ratings, false);
					} else {
						$this->show_ratings($ratings, false);
					}
				}
				layout_close_table();
				$this->id_sent = true;
				?></section><?
			}
		}
		return $this->id_sent;
	}

	final public function show_form(): void {
		/** @noinspection NotOptimalIfConditionsInspection */
		if (!have_user()
		||	!(require_once '_voice.inc')
		||	!may_speak($this->item)
		) {
			return;
		}
		$my_comment = self::get_my_comment();

		?><div id="my-rating"<?
		if (!$my_comment) {
			?> class="hidden"><?
		} else {
			?>><b><?= Eelement_name('your_rating') ?></b>: <?
			echo
				replace_shy(
					_smiley_replace(
						escape_utf8(
							_smiley_prepare($my_comment['BODY'], utf8: true)
						),
						true
					),
					true
				);
		}
		?></div><?
		$element = $_REQUEST['sELEMENT'];
		$id		 = $_REQUEST['sID'];
		include_js('js/form/rating');
		?><form<?
		?> accept-charset="utf-8"<?
		?> data-your="<?= Eelement_name('your_rating') ?>"<?
		?> onsubmit="return submitRatingForm(this,'<?= $element ?>', <?= $id ?>);"<?
		?> method="post"<?
		?> action="/<?= $element ?>/<?= $id ?>/rate#ratings"<?
		?>><?

		$rate_element = $element;
		$activity = null;

		if ($element === 'organization') {
			if (!isset($this->item['ACTIVITIES'])) {
				mail_log('activities not set', item: $this->item);
			} elseif (
				isset($this->item['ACTIVITIES'])
			&&	($is_array = is_array($this->item['ACTIVITIES']))
			?	count($this->item['ACTIVITIES']) === 1
			:	!str_contains($this->item['ACTIVITIES'], ',')
			) {
				$activity = $is_array ? reset($this->item['ACTIVITIES']) : $this->item['ACTIVITIES'];
			}
		}

		layout_open_box('white bordered');
		layout_box_header(__C('rating:rate_this_'.$rate_element, DO_UBB, ['ACTIVITY' => $activity]));

		/** @noinspection ThisExpressionReferencesGlobalObjectJS */
		show_input([
			'value_utf8'	=> $initial = ($my_comment ? $my_comment['BODY'] : ''),
			'data-initial'	=> $initial,
			'onkeyup'		=> /** @lang JavaScript */ 'Pf_changeRating(this);',
			'onchange'		=> /** @lang JavaScript */ 'Pf_changeRating(this);',
			'type'			=> 'text',
			'class'			=> 'fw',
			'maxlength'		=> 2048,
			'name'			=> 'RATING',
		]);

		layout_close_box();
		?><div class="block"><input class="light" name="SUBMIT" type="submit" value="<?= __('action:send') ?>" /></div><?
		?></form><?
	}
}
