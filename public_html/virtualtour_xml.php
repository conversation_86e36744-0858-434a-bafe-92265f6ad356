<?php

define('CURRENTSTAMP',time());

require_once '_exit_if_offline.inc';
require_once '_require.inc';
require_once '_hosts.inc';
require_once '_db.inc';
require_once '_identity.inc';
require_once '_counthit.inc';

function bail(int $errno): never {
	http_response_code($errno);
	exit($errno !== 200 ? 0 : 1);
}

if (!($id = have_idnumber($_SERVER,'eID'))) {
	bail(404);
}
$tour =	($licenseid = $_SERVER['eLICENSE'] ? $id : 0)
?	db_single_assoc(['vtlicense', 'virtualtour'], '
	SELECT TOURID, vt.CSTAMP, FIRSTID, ROOMS, NOLOGO
	FROM virtualtour AS vt
	JOIN vtlicense USING (TOURID)
	WHERE ACTIVE
	  AND LICENSEID = '.$id
	)
:	db_single_assoc('virtualtour','
	SELECT '.$id.' AS TOURID, CSTAMP, FIRSTID
	FROM virtualtour
	WHERE TOURID = '.$id
	);

if (!$tour) {
	bail($tour === false ? 503 : 404);
}

$tourid = $tour['TOURID'];

$rooms = db_simple_hash('virtualroom',
	!empty($tour['ROOMS'])
&&	($roomids = explode(',', $tour['ROOMS']))
?	'SELECT ROOMID, NAME FROM virtualroom WHERE TOURID='.$tourid.' AND ROOMID IN ('.implode(',', $roomids).') ORDER BY ROOMID'
:	'SELECT ROOMID, NAME FROM virtualroom WHERE TOURID='.$tourid.' ORDER BY ROOMID'
);
if (!$rooms) {
	bail($rooms === false ? 503 : 404);
}

counthit('virtualxml', $tourid);

require_once '_modified.inc';

header('Cache-Control: public,must-revalidate');

$mstamp = filemtime($_SERVER['SCRIPT_FILENAME']);

if (not_modified($mstamp, hash('xxh128', serialize($rooms)))) {
	bail(304);
}

$roomid = have_idnumber($_REQUEST,'ROOMID') ?: (!empty($roomids) ? $roomids[0] : $tour['FIRSTID']);

header('Content-Type: application/xml; charset=utf-8');

echo '<?xml version="1.0" encoding="UTF-8"?>';

?><krpano version="1.16.3"<?
if (SERVER_SANDBOX
||	HOME_OFFICE
) {
	?> logkey="true"<?
	?> showerrors="true"<?
	?> debugmode="true"<?
} else {
	?> logkey="false"<?
}
?> onstart="initializeThumbs()"<?
?>><?
/*	?><security cors="off"></security><?*/
	?><events onresize="initializeThumbs()" /><?
	?><action name="mainloadpano"><?
		?>closeglobalobjects();<?
		?>ifnot(stopSequence === undefined,interruptAnimation(););<?
		?>loadpano(%1,NULL,NULL,BLEND(1));<?
	?></action><?
	?><plugin name="data" firstPanoHasBeenPlayed="false" keep="true" /><?
	?><include url="/virtualroom/<?= $licenseid ? 'l'.$licenseid : $tourid ?>/<?= $roomid ?>.xml" /><?
	?><autorotate enabled="true" /><?
	?><textstyle<?
		?> name="DEFAULT"<?
		?> background="false"<?
		?> bold="true"<?
		?> border="false"<?
		?> effect="dropshadow(0,45,0x000000,4,2)"<?
		?> font="sans"<?
		?> fontsize="16"<?
		?> italic="false"<?
		?> textcolor="0xffffffff" /><?
	if ($showlogo
	=	isset($_REQUEST['LOGO'])
	||	(	$licenseid
		&&	(	empty($tour['NOLOGO'])
			||	!isset($_REQUEST['NOLOGO'])
			)
		)
	) {
		?><plugin name="logo" alpha="1" url="<?= STATIC_HOST ?>/images/flocklogovt.png" keep="true" align="topright" x="10" y="10" width="90" height="90" onclick="openurl(https://partyflock.<?= CURRENTDOMAIN?>'/,_blank)" /><?
	}
	?><plugin name="openfs"<?
		?> align="rightbottom"<?
		?> crop="0|0|65|65"<?
		?> customColor="true"<?
		?> height="prop"<?
		?> keep="true"<?
		?> zorder="2"<?
		?> onclick="set(fullscreen,true)"<?
		?> onovercrop="0|65|65|65"<?
		?> url="<?= VT_HOST ?>/vt/g/menu/menu7.png" width="40" x="10" y="10" /><?

	?><plugin name="closefs"<?
		?> align="rightbottom"<?
		?> crop="0|0|65|65"<?
		?> customColor="true"<?
		?> height="prop"<?
		?> keep="true"<?
		?> zorder="2"<?
		?> onclick="set(fullscreen,false)"<?
		?> onovercrop="0|65|65|65"<?
		?> url="<?= VT_HOST ?>/vt/g/menu/menu8.png" visible="false" width="40" x="10" y="10" /><?

	?><events<?
		?> onenterfullscreen="set(plugin[openfs].visible,false);set(plugin[closefs].visible,true)"<?
		?> onexitfullscreen="set(plugin[openfs].visible,true);set(plugin[closefs].visible,false)" /><?

	?><action name="magnify"><?
		?>tween(plugin[%1].alpha,1.5,.3,easeInOutQuad);<?
		?>tween(plugin[%1].scale,1.05,.3,easeInOutQuad);<?
	?></action><?
	?><action name="lie"><?
		?>tween(plugin[%1].alpha,.8,.3,easeInOutQuad);<?
		?>tween(plugin[%1].scale,1,.3,easeInOutQuad);<?
	?></action><?

	$roomcnt = count($rooms);
	$h = 22;
	if ($roomcnt <= 4) {
		$cols = 1;
		$x = [50];
		$rows = $roomcnt;
		$slider_width = 22.5;
		$rows = 4;
	} else {
		$rows = 4;
		if ($roomcnt <= 8) {
			$cols = 2;
			$x = [25, 75];
			$slider_width = 45;
		} else {
			$cols = ceil($roomcnt / $rows);
			switch ($cols) {
			case 3:
				$x = [18, 50, 82];
				$slider_width = 67.5;
				break;
			case 4:
				$x =[14, 38, 62, 86];
				$slider_width = 90;
				break;
			case 5:
				$slider_width = 98;
				$h *= .88;
				$x = [11, 30.5, 50, 69.5, 89];
				break;
			case 6:
				$slider_width = 98;
				$h *= .75;
				$x = [9, 25.4, 41.8, 58.2, 74.6, 91];
				break;
			}
		}
	}
	$yinc = 100 / $rows;

	?><action name="slide_action_in"><?
		?>set(plugin[slide].onclick,slide_action_out());<?
		?>set(plugin[strap].onclick,slide_action_out());<?
		?>set(plugin[strapArrow].rotate,180);<?
		?>tween(plugin[thumbArray].x,<?= $slider_width ?>%,1,easeOutQuint);<?
	?></action><?
	?><action name="slide_action_out"><?
		?>set(plugin[slide].onclick,slide_action_in());<?
		?>set(plugin[strap].onclick,slide_action_in());<?
		?>set(plugin[strapArrow].rotate,0);<?
		?>tween(plugin[thumbArray].x,0,1,easeOutQuint);<?
	?></action><?

	?><plugin name="thumbArray"<?
		?> keep="true"<?
		?> zorder="1"<?
		?> url="<?= VT_HOST ?>/vt/g/transparent.png"<?
		?> handcursor="false"<?
		?> capture="false"<?
		?> children="true"<?
		?> alpha=".9"<?
		?> smoothing="true"<?
		?> align="left"<?
		?> edge="right"<?
		?> width="<?= $slider_width ?>%"<?
		?> height="100%"<?
	?> /><?

	?><plugin name="slide"<?
		?> parent="thumbArray"<?
		?> keep="true"<?
		?> url="<?= VT_HOST ?>/vt/g/slide.png"<?
		?> handcursor="false"<?
		?> capture="false"<?
		?> children="true"<?
		?> alpha=".9"<?
		?> smoothing="true"<?
		?> align="right"<?
		?> edge="right"<?
		?> width="100%"<?
		?> height="100%"<?
		?> onclick="slide_action_in()"<?
	?> /><?

	?><plugin name="strap"<?
		?> keep="true"<?
		?> url="<?= VT_HOST ?>/vt/g/strap.png"<?
		?> parent="thumbArray"<?
		?> align="right"<?
		?> edge="left"<?
		?> handcursor="true"<?
		?> onclick="slide_action_in()"<?
	?> /><?

	?><plugin name="strapArrow"<?
		?> keep="true"<?
		?> parent="strap"<?
		?> url="<?= VT_HOST ?>/vt/g/arrow.png"<?
		?> align="center"<?
		?> edge="center"<?
		?> capture="false"<?
		?> enabled="false"<?
	?> /><?

	$xinc = 100 / $cols;
	$yinc = 100 / $rows;

	$xinitial = $xinc / 2;
	$yinitial = $yinc / 2;

	$xoffset = $xinitial;
	$yoffset = $yinitial;

	$photo_aspect_w = 3;
	$photo_aspect_h = 2;

	$swap_on_aspect = $cols * $photo_aspect_w / ($rows * $photo_aspect_h);

	$col = 0;

	foreach ($rooms as $roomid => $name) {
		?><plugin<?
		?> name="tr<?= $roomid ?>"<?
		?> keep="true"<?
		?> zorder="2"<?
		?> url="<?= VT_HOST ?>/vt/<?= $tourid ?>/<?= $roomid ?>/t.jpg"<?
		?> parent="thumbArray"<?

		?> width="<?= $xinc-2 ?>%"<?
		?> height="prop"<?

		?> align="topleft"<?
		?> edge="center"<?
		?> x="<?= $xoffset ? $xoffset.'%' : 0 ?>"<?
		?> y="<?= $yoffset ? $yoffset.'%' : 0 ?>"<?
		?> alpha=".8"<?
		?> onclick="mainloadpano('../virtualroom/<?= $licenseid ? 'l'.$licenseid : $tourid ?>/<?= $roomid ?>.xml')"<?
		?> onover="magnify('tr<?= $roomid ?>')"<?
		?> onhover="showtext('<?= escape_specials(win1252_to_utf8($name),true) ?>')"<?
		?> onout="lie('tr<?= $roomid ?>')" /><?
		if (++$col == $cols) {
			$col = 0;
			$yoffset += $yinc;
			$xoffset = $xinitial;
		} else {
			$xoffset += $xinc;
		}
	}
	?><action name="closeglobalobjects">slide_action_out()</action><?
	?><action name="initializeThumbs"><?
	?>mul(sliderwidth,<?= $slider_width / 100 ?>,stagewidth);<?
	?>div(aspect,sliderwidth,stageheight);<?
	?>if(aspect LT <?= $swap_on_aspect ?>,set(filler,'vert');,set(filler,'horiz'););<?
	?>if(aspect LT <?= $swap_on_aspect ?>,<?
		?>for(set(i,0),i LE <?= $roomid ?>,inc(i),<?
			?>txtadd(th,'tr',get(i));<?
			?>set(plugin[get(th)].width,'<?= $xinc-2 ?>%');<?
			?>set(plugin[get(th)].height,'prop');<?
		?>),<?
		?>for(set(i,0),i LE <?= $roomid ?>,inc(i),<?
			?>txtadd(th,'tr',get(i));<?
			?>set(plugin[get(th)].height,'<?= $yinc-2 ?>%');<?
			?>set(plugin[get(th)].width,'prop');<?
		?>)<?
	?>);<?
	?>mul(havepix,stagewidth,stageheight);<?
	?>div(part,havepix,100000);<?
	?>Math.sqrt(sq,part);<?
	?>div(sqq,sq,2);<?
	?>mul(wantscale,.7,sqq);<?
	?>set(plugin[logo].scale,get(wantscale));<?
	?></action><?
	?><action name="interruptAnimation"><?
		# Raise sequence stopper and wait sequence time to break the delayed calls
		?>ifnot (stopSequence === undefined,<?
			?>set(stopSequence,true);<?
			?>wait(0);<?
		?>);<?
	?></action><?
?></krpano>
