<?php

declare(strict_types=1);

require_once '_gallery.inc';

class _gallerylist {
	public array	$galleries		= [];
	public bool		$surplus		= false;

	private ?string	$element		= null;
	private int		$id;
	private string	$loading		= 'eager';	# gallerylist thumbs appear high up the page

	final public function query(int $max = 0): array|false|null {
		switch ($this->element) {
		case 'organization':
			$this->galleries = db_rowuse_hash(['gallery','image','party','connect'],'
				SELECT	GALLERYID,ASSOCID AS PARTYID,STAMP,NAME,gallery.PSTAMP,VISIBLE,
						(SELECT COUNT(*) FROM image WHERE image.GALLERYID=gallery.GALLERYID AND image.HIDDEN = 0 AND ACCEPTED = 1) AS PHOTO_CNT
				FROM connect
				JOIN party ON party.PARTYID=ASSOCID
				JOIN gallery ON gallery.PARTYID=ASSOCID
				WHERE MAINTYPE="organization"
				  AND MAINID = '.$this->id.'
				  AND ASSOCTYPE = "party"
				ORDER BY STAMP DESC'.
				($max ? ' LIMIT '.($max+1) : null)
			);
			break;

		case 'location':
			$this->galleries = db_rowuse_hash(['gallery','image','party','connect'],'
				SELECT	GALLERYID,PARTYID,STAMP,NAME,gallery.PSTAMP,VISIBLE,
						(SELECT COUNT(*) FROM image WHERE image.GALLERYID=gallery.GALLERYID AND image.HIDDEN = 0 AND ACCEPTED = 1) AS PHOTO_CNT
				FROM party
				JOIN gallery USING (PARTYID)
				WHERE LOCATIONID = '.$this->id.'
				ORDER BY STAMP DESC'.
				($max ? ' LIMIT '.($max+1) : null)
			);
			break;

		case 'artist':
			require_once '_visibility.inc';
			$this->galleries = db_rowuse_hash(['gallery','image','artist_appearance','party'],'
				SELECT	GALLERYID, artist_appearance.IMGID, PARTYID,STAMP, party.NAME, gallery.PSTAMP, VISIBLE, COUNT(IF(image.HIDDEN = 0 AND party.ACCEPTED = 1, 1, NULL)) AS PHOTO_CNT
				FROM artist_appearance
				JOIN artist USING (ARTISTID)
				JOIN image ON artist_appearance.IMGID=image.IMGID AND image.HIDDEN=0
				JOIN gallery USING (GALLERYID,PARTYID)
				JOIN party USING (PARTYID)
				WHERE ARTISTID='.$this->id._where_visible('artist','APPEARANCES').'
				GROUP BY GALLERYID
				ORDER BY STAMP DESC'.
				($max ? ' LIMIT '.($max+1) : null)
			);
			break;

		case 'party':
			mail_log('gallerylist with this->partyid ever');
			$this->galleries = memcached_rowuse_hash(['gallery', 'image', 'party'], '
				SELECT	GALLERYID, IMGID, PARTYID, STAMP, NAME, gallery.PSTAMP, VISIBLE, COUNT(IF(image.HIDDEN = 0 AND ACCEPTED = 1, 1, NULL)) AS PHOTO_CNT
				FROM image
				JOIN gallery USING (GALLERYID,PARTYID)
				JOIN party USING (PARTYID)
				WHERE PARTYID='.$this->id.'
				ORDER BY RAND()
				LIMIT 5',
				TEN_MINUTES
			);
			break;

		default:
			error_log("ERROR unsupported \$element ($this->element) gallery list");
		}
		if ($max
		&&	count($this->galleries) > $max
		) {
			array_pop($this->galleries);
			$this->surplus = true;
		}
		return $this->galleries;
	}

	final public function show_thumbs(): void {
		if (empty($this->galleries)) {
			return;
		}
		$overlay = true;
		$is_admin = have_admin(['gallery', 'photo']);
		$orient = safe_random_int(0,4) ? 'DESC' : 'ASC';
		require_once 'defines/zoomover.inc';
		?><div class="rndbrd block center"><?
		foreach ($this->galleries as $galleryid => $gallery) {
			$invisible_gallery = !visible_gallery($gallery);
			if (!$is_admin
			&&	(	$invisible_gallery
				||	$gallery['PSTAMP'] > CURRENTSTAMP
				)
			) {
				continue;
			}
			if (empty($this->artistid)) {
				$photo = memcached_single_assoc('image', "
					SELECT IMGID, PARTYID, GALLERYID, TITLE, THUMB_WIDTH, THUMB_HEIGHT
					FROM image
					LEFT JOIN gallery USING (GALLERYID, PARTYID)
					WHERE GALLERYID = $galleryid
					  AND image.HIDDEN = 0
					ORDER BY
						THUMB_WIDTH > THUMB_HEIGHT $orient,
						RAND()
					LIMIT 1"
				);
			} else {
				$photo = memcached_single_assoc('image', "
					SELECT IMGID, PARTYID, GALLERYID, TITLE, THUMB_WIDTH, THUMB_HEIGHT
					FROM image
					JOIN artist_appearance USING (IMGID)
					LEFT JOIN gallery USING (GALLERYID, PARTYID)
					WHERE GALLERYID = $galleryid
					  AND image.HIDDEN = 0
					  AND ARTISTID = $this->artistid
					ORDER BY
						THUMB_WIDTH > THUMB_HEIGHT $orient,
						RAND()
					LIMIT 1"
				);
			}
			if (!$photo) {
				continue;
			}
			/** @noinspection PhpRedundantOptionalArgumentInspection */
			extract($photo, \EXTR_OVERWRITE);
			if ($invisible_gallery) {
				$class = ' unavailable';
			} elseif ($gallery['PSTAMP'] > CURRENTSTAMP) {
				$class = ' light';
			} else {
				$class = null;
			}
			?><div<?
			?> class="center relative labelled ib <?=$class ?>"><?

			if ($overlay) {
				?><div class="abs label"><?
				?><div class="rvbg ib"><?
					?><div class="bold"><?= get_element_link('gallery',$galleryid) ?></div><?
					?><div class="light small"><? _date_display($gallery['STAMP']) ?></div><?
				?></div><?
				?></div><?
			}
			?><a href="<?
			if (empty($this->artistid)) {
				echo get_element_href('gallery',$galleryid);
			} else {
				?>/artist/<?= $this->artistid ?>/photos#p<? echo $PARTYID;
			}
			?>" class="middle"><?
			$THUMB_WIDTH *= 10;
			$THUMB_HEIGHT *= 10;
			scale_max_pixels($THUMB_WIDTH,$THUMB_HEIGHT, 225 * 150);
#			$load_width = $THUMB_WIDTH * ZOOMOVER_SCALE;
#			$load_height = $THUMB_HEIGHT * ZOOMOVER_SCALE;
#			print_rr($load_width.' x '.$load_height);
			$load_width = $THUMB_WIDTH;
			$load_height = $THUMB_HEIGHT;
			if (is_high_res()) {
				$load_width *= 2;
				$load_height *= 2;
			}
			?><img<?
			?> loading="<?= $this->loading ?>"<?
			?> class="zoomover ib"<?
			?> alt="<?
			ob_start();
			echo Eelement_plural_name('photo');
			if ($PARTYID) {
				if ($party = memcached_party_and_stamp($PARTYID)) {
					[$y, $m, $d] = _getdate($party['STAMP_TZI'] - $party['AT2400'],'UTC');
					echo	', ',escape_utf8($party['NAME']),
						', ',$d,' ',_month_name($m),' ',$y,
						(empty($party['LOCATION_NAME']) ? '' : ', '.escape_utf8($party['LOCATION_NAME'])),
						(empty($party['CITY_NAME'])	? '' : ', '.escape_utf8($party['CITY_NAME']));

				}
			} elseif ($TITLE) {
				echo ' ', escape_utf8($TITLE);
			}
			$title = ob_get_flush();
			?>"<?
			?> title="<?= $title ?>"<?
			?> src="<?= get_photo_url($IMGID, $load_width.'x'.$load_height) ?>"<?
			?> width="<?= $THUMB_WIDTH ?>"<?
			?> height="<?= $THUMB_HEIGHT ?>"<?
			?> /><?
			?></a><?
			if (!$overlay) {
				?><br /><?
				?><div class="small<?
				if (isset($this->no_wrap)) {
					?> nowrap<?
				}
				?>"><div><span class="rvbg"><?= get_element_link('gallery',$galleryid) ?></span></div><?
				?><div class="light"><? _date_display($gallery['STAMP']) ?></div><?
				?></div><?
			}
			?></div><?
		}
		?></div><?
	}
	final public function connected_to(string $element, int $id): void {
		$this->element	= $element;
		$this->id		= $id;
	}
}
