<?php

declare(strict_types=1);

const SESSRM_LOGOUT_ELSEWHERE	= 1;
const SESSRM_INTERNAL_ERROR		= 5;
const SESSRM_LOGGED_OFF			= 6;
const SESSRM_RELOGGED_IN		= 7;
const SESSRM_PASSWORD_RESET		= 9;
const SESSRM_STATUS_NOT_ACTIVE	= 10;
const SESSRM_SINGLE_LOGGED_OFF	= 11;
const SESSRM_DEPERSONATED		= 14;
const SESSRM_IMPERSONATING		= 15;
const SESSRM_FORCED				= 17;

const SESSFLG_SECURE			= 0x1;
const SESSFLG_SINGLE_LOGIN		= 0x2;

# indices of $currentuser->session:
const SESS_CSTAMP		= 0;
const SESS_RM			= 1;
const SESS_SESSIONID	= 2;
const SESS_FLAGS		= 3;
const SESS_IMPERSONATE	= 4;
const SESS_AUTOLOGOFF	= 5;

const SESSION_CACHE		= 'sessionv13:';

function get_session(int $userid, string $sid, int $sessionid): array|false {
	# don't cache for now so we can immediately rm a session
	if (!($session = db_single_array('user_session', "
		SELECT SQL_NO_CACHE CSTAMP, RM, SESSIONID, FLAGS, IMPERSONATE, AUTOLOGOFF_V2
		FROM user_session
		WHERE SID = '".addslashes($sid)."'
		  AND USERID = $userid
		  AND SESSIONID = $sessionid",
		  DB_USE_MASTER
	))) {
		return false;
	}
	return $session;
}
function rm_session(int $userid, int $sessionid, int $rm): bool {
	if (!db_update('user_session','
		UPDATE user_session SET
			RM		= '.$rm.',
			RMSTAMP	= '.CURRENTSTAMP.'
		WHERE RM=0
		  AND SESSIONID = '.$sessionid.'
		  AND USERID = '.$userid)
	) {
		return false;
	}
	return false !== memcached_delete(SESSION_CACHE.$userid.':'.$sessionid);
}
function rm_sessions(int $userid, int $rm, int $except = 0): int|false{
	if (!($sessions = db_simpler_Array('user_session','
		SELECT SESSIONID
		FROM user_session
		WHERE RM=0
		  AND SESSIONID != '.$except.'
		  AND USERID = '.$userid,
		DB_USE_MASTER
	))) {
		return $sessions === false ? false : 0;
	}
	$cnt = 0;
	foreach ($sessions as $sessionid) {
		if (!rm_session($userid,$sessionid,$rm)) {
			return false;
		}
		++$cnt;
	}
	return $cnt;
}
function session_warning(int $rm, int $userid, int $sessionid): void {
	$key = 'sessrm:'.$rm;
	require_once '_error.inc';
	register_warning($key);
}
function create_session(int $userid, $arg1, $flags = null, int $impersonate = 0): array|false {
	if (is_array($arg1)) {
		# create new followup session, when depersonating
		# copy settings from previous session
		$session = $arg1;
		[,,, $flags, $impersonate, $autologoff] = $session;
	} else {
		$autologoff = $arg1;
	}
	# We're always on https
	$flags |= SESSFLG_SECURE;

	if (!($sid = generate_sid())) {
		error_log('failed to create new SID for session');
		return false;
	}
	if (!db_insert('user_session','
		INSERT INTO user_session SET
			SID				= "'.$sid.'",
			CSTAMP			= '.CURRENTSTAMP.',
			LAST_USED		= '.CURRENTSTAMP.',
			USERID			= '.$userid.',
			FLAGS			= '.$flags.',
			IMPERSONATE		= '.$impersonate.',
			AUTOLOGOFF_V2	='.($autologoff ? 1 : 0))

	) {
		return false;
	}
	$sessionid = db_insert_id();

	memcached_set(SESSION_CACHE.$userid.':'.$sid,$session = [
			CURRENTSTAMP,
			0,
			$sessionid,
			$flags,
			$impersonate,
			(bool)$autologoff
		],
		HALF_HOUR
	);

	return [$sid, $sessionid, $session];
}

function generate_sid(): string|false {
	if (!($random_file = fopen('/dev/urandom', 'rb'))) {
		return false;
	}
	$random_bytes = fread($random_file,24);
	fclose($random_file);
	return base64_encode($random_bytes);
}

function singled_login(int $userid): int|false|null {
	return db_single('user_session','
		SELECT SESSIONID
		FROM user_session
		WHERE RM = 0
		  AND FLAGS & '.SESSFLG_SINGLE_LOGIN.'
		  AND USERID = '.$userid.'
		LIMIT 1',
		DB_USE_MASTER
	);
}
