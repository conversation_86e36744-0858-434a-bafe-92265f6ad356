Pf.choosePage = function(self) {
	while (true) {
		let page_number = prompt(self.getAttribute('data-ask'),'');
		if (!page_number) {
			return;
		}
		if (/^\d+$/.test(page_number)
		&&	(page_number = parseInt(page_number)) >= 1
		&&	page_number <= self.getAttribute('data-last')
		) {
			Pf.actualPageJump(self, page_number);
			break;
		}
	}
}

Pf.pageJump = function(self) {
	let default_page_number = self.getAttribute('data-default-page');
	Pf.actualPageJump(
		self,
			self.value === ''
		||	self.value === default_page_number
		?	0
		:	self.value
	);
}

Pf.actualPageJump = function(self, page_number) {
	location.href =
		page_number === 0
	?	self.getAttribute('data-default-url')
	:	self.getAttribute('data-base-url') +
		self.getAttribute('data-pagepart') +
		page_number +
		self.getAttribute('data-pagepost');
		self.getAttribute('data-hash');
}
