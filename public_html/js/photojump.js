function photoGo() {
	if (Pf.ignoreJumps) {
		return;
	}
	location.href = this.getAttribute('data-dst');
}

function ignoreJumps(yes) {
	if (!Pf.jumps) {
		return;
	}
	Pf.ignoreJumps = yes;
	setclass(Pf.jumps, !yes, 'livisi');
}

function initializeJumps() {
	let jb = getobj('jumpblock');
	if (!jb) {
		return;
	}
	let jumps = [];
	let urls = ['purl', 'nurl'];
	let x2 = Pf.high_res;
	for (let i = 0; i < 2; ++i) {
		let urlname = urls[i];
		let url = getobj(urlname);
		let jd = null;
		if (url) {
			let align= i ? 'right' : 'left';
			let dir	= i ? 'r'	  : 'l';
			jd = document.createElement('div');
			jd.className = 'abs jumper ptr livisi';
			jd.setAttribute('data-dst', url.href);
			jd.style.width = '20%';
			jd.style.height = '100%';
			jd.style[align] = '-.5em';
			jd.style.top = '-.5em';
			addevent(jd,['click', 'touchstart'], photoGo);
			jd.innerHTML =
				'<img id="jIMG' + dir + '"'
			+	' alt="&'+dir+'arr;"'
			+	' class="light6 defptr ptr abs"'
			+	' style="top: 50%; margin-top: -10px; ' + align + ': .2em;"'
			+	' src="//static.partyflock.nl/images/small' + dir + 'arr' + x2 + '.png" />';
			jb.appendChild(jd);
		}
		jumps.push(jd);
	}
	Pf.jumps = jumps;
}

addreadyevent(initializeJumps);

addevent(window, 'blur', function() {
	ignoreJumps(true);
});

addevent(window, 'focus', function() {
	setTimeout(function(){
		ignoreJumps(false);
	}, 100);
});

addevent(document, 'keydown', function(event) {
	if (!Pf.jumps) {
		return;
	}

	let t = event.target ? event.target : event.srcElement;

	if (typeof t.form !== 'undefined'
	||	t.nodeName === 'INPUT'
	||	t.nodeName === 'TEXTAREA'
	||	t.nodeName === 'SELECT'
	) {
		return;
	}
	let i;
	switch (event.code) {
	case 'ArrowLeft':  i = 0; break;
	case 'ArrowRight': i = 1; break;
	default:
			return true;
	}
	let o = Pf.jumps[i];
	if (o) {
		remclass(getobj('jIMG'+(i ? 'r' : 'l')), 'light6');
		location.href = o.getAttribute('data-dst');
	}
});
