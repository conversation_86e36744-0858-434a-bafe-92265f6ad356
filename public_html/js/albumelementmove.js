var isMoving = false;
var selectedID = 0;
function changeMoving(me) {
	setdisplay('selectguide',isMoving = isMoving ? false : true);
	me.className = (isMoving ? 'selected unhideanchor' : 'unhideanchor');
//	alert('changeMoving, isMoving == ' + (isMoving ? 'true' : 'false') + ', isRemoving == ' + (isRemoving ? 'true' : 'false'));
	if (!isRemoving
	&&	selectedID
	) {
		var imgobj = getobj('img'+selectedID);
		if (imgobj) {
			remclass(imgobj,'selected-element');
		}
		selectedID = 0;
	}
	if (isMoving 
	&&	typeof isRemoving != 'undefined'
	&&	isRemoving
	) {
		changeRemoving(getobj('removelink'));
	}
}
function movePhoto(a,id,albumid,mapid) {
	if (!isMoving) {
		return true;
	}
	if (!selectedID) {
		selectedID = id;
		var imgobj = getobj('ae'+id,true);
		if (imgobj) {
			addclass(imgobj,'selected-element');
		}
		return false;
	} else if (selectedID != id) {
		var swapreq = initrequest(true);
		var swapID = selectedID;
		if (!swapreq) {
			return false;
		}
		swapreq.open('POST','/swap/albumelement/'+Math.min(id,swapID)+','+Math.max(id,swapID)+'.act',true);
		swapreq.onreadystatechange = function() {
			if (swapreq.readyState !== 4) {
				return;
			}
			if (swapreq.status !== 200) {
				showerrors(swapreq);
				return;
			}
			var obj1 = getobj('ae' + id,true);
			var obj2 = getobj('ae' + swapID,true);
			if (obj1 && obj2) {
				var pobj = obj1.parentNode;
				var nextobj = obj1.nextSibling;
				if (nextobj == obj2) {
					nextobj = obj2.nextSibling;
					var tmp = obj2;
					obj2 = obj1;
					obj1 = tmp;
				}
				pobj.removeChild(obj1);
				pobj.insertBefore(obj1,obj2);
				pobj.removeChild(obj2);
				if (nextobj) {
					pobj.insertBefore(obj2,nextobj);
				} else {
					pobj.appendChild(obj2);
				}
			}
		};
		swapreq.send(null);
	}
	var imgobj = getobj('ae'+selectedID,true);
	if (imgobj) {
		remclass(imgobj,'selected-element');
	}
	selectedID = 0;
	return false;
}
