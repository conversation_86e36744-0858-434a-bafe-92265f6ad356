Pf.checkTicketStates = function(catcher) {
	event.stopPropagation();

	let ticketidstr = catcher.getAttribute('data-ticketids');
	if (!ticketidstr) {
		return;
	}
	let ticketids = ticketidstr.split(',');
//	console.log('checking tickets:',ticketids);

	fetch('/ticketstates.obj',{
		method:	'post',
		headers:{'Content-Type': 'application/x-www-form-urlencoded; charset=utf-8'},
		body:	'TICKETIDS=' + ticketidstr
	})
	.then(function(response){
		if (response.status !== 200) {
			console.warn('failed to get ticketstates',response);
			return;
		}
		response.json().then(function(states){
//			console.log('got states:',states);
			
			let table = catcher.firstChild;
			let have_rows = 0;
			let rem_rows = [];
			let header = null;
			for (i = 0; i < table.tBodies[0].rows.length; ++i) {
				let row = table.tBodies[0].rows[i];
				let ticketid = row.getAttribute('data-ticketid');
				if (!ticketid) {
					if (header
					&&	!have_rows
					) {
						rem_rows.push(header);
					}
					header = row;
					have_rows  = 0;
//					console.log('found header:',header.textContent);
					continue;
				}
//				console.log('in table ticketid:',ticketid);
				
				if (typeof states[ticketid] !== 'undefined') {
					if (states[ticketid].STATUS != 'open'
					&&	!states[ticketid].TRACKIT 
					) {
						rem_rows.push(row);
						continue;
					}
					setclass(row,states[ticketid].LOCKED,'locked-item');
				}
				
				++have_rows;
			}
			if (header
			&&	!have_rows
			) {
				rem_rows.push(header);
			}
			rem_rows.forEach(function(row){
				Pf.removeNode(row);
			});
		});
	})
	.catch(function(error) {
		console.warn('error:',error);
	});
}
