<?php

define('CURRENTSTAMP',time());

require_once '../../_offline.inc';
require_once '../../__translation.php';
require_once '../../_hashes.inc';
require_once '../../_modified.inc';
require_once '../../_unicode.inc';

function bail(int $errno): never {
	http_response_code($errno);
	exit;
}

if (not_modified(etag: '2014012101_'.get_hash('js/main.js.php'))) {
	bail(304);
}

header('Content-Type: text/javascript; charset=windows-1252');
header('Vary: User-Agent');

?>function validateElements(elements) {
	try {
		let radio_dones = {};
		let email_dones = {};

		for (const element of elements) {
			if (element.disabled) {
				continue;
			}
			if (element.name === 'g-recaptcha-response') {
				continue;
			}
			if (-1 !== element.name.indexOf('COPYRIGHT')
			&&	element.outerHTML.match(/Pf\.enterCopyright/)
			) {
				Pf.enterCopyright(null, element);
			}
			if ( element.hasAttribute('data-bad')
			&&	!element.hasAttribute('data-bad-ok')
			) {
				markRefill(element);
				return false;
			}

			if ((	element.getAttribute('data-want-charset') === 'ascii'
				||	null !== element.getAttribute('data-want-ascii')
				||	element.type === 'url'
				||	element.type === 'email'
				)
			&&	!/^[\x00-\x7F]*$/.test(element.value)
			) {
				markRefill(element);
				return false;
			}

			let valid = element.getAttribute('data-valid');
			let value_is_empty;
			if (element.nodeName === 'SELECT') {
				value_is_empty = !element.selectedIndex && !element.value && !element.value.length;

			} else if (element.type === 'button') {
				if (element.getAttribute('data-needclick')) {
					markRefill(element,element.getAttribute('data-errmsg'));
					return false;
				}
			} else if (element.type === 'radio') {
				value_is_empty = true;
				if (typeof radio_dones[element.name] === 'undefined') {
					let radios = element.form[element.name];
					if (radios === element) {
						// only 1
						radios = [radios];
					}
					for (const radio of radios) {
						if (radio.checked) {
							value_is_empty = false;
							break;
						}
					}
					radio_dones[element.name] = value_is_empty;
				} else {
					value_is_empty = radio_dones[element.name];
				}
			} else {
				value_is_empty =
					element.type === 'checkbox'
				?	!element.checked
				:	(	element.type === 'number'
					?	(	valid !== 'lnglat'
						?	!parseInt(element.value)
						:	!parseFloat(element.value)
						)
					:	(	!element.value
						||	element.value.is_empty()
						)
					);
			}

			let required = element.required;

			if (required
			&&	!/^\s*(?:false|no|0+)\s*$/.test(required)
			&&	value_is_empty
			) {
				markRefill(element,'<?= __('js:form:error:need_something',IS_JAVASCRIPT) ?> (%NAME%)');
				return false;
			}

			let span;
			if (element.name.match(/(?:YEAR|MONTH|DAY)$/)
			&&	(span = element.parentNode)
			&&	span.className === 'error'
			&&	span.getAttribute('data-prefix')
			) {
				Pf.scrollToMiddle(span);
				alert('<?= __('js:form:error:date_incorrect',IS_JAVASCRIPT) ?>');
				return false;
			}
			let minlen = element.minlength;
			if (minlen) {
				if (element.value.length < minlen) {
					markRefill(element,String('<?= __('js:form:error:need_at_least_x_characters',IS_JAVASCRIPT) ?>').replace_keywords({'X':minlen}));
					return false;
				}
			}
			if (!valid) {
				valid = element.type;
			}
			if (!value_is_empty) {
				let ok = true;
				let url_or_view_source = valid === 'url_or_view_source';
				if (url_or_view_source
				||	valid === 'url'
				) {
					let url_str = element.value;
					if (url_or_view_source) {
						if (const match = element.value.match(/^view-source:(?<url>.*)$/) {
							url_str = match.groups.url;
						}
					}
					let url;
					try {
						url = new URL(url_str);
					} catch (_) {
						ok = false;
					}
				} else if (element.pattern) {
					ok = RegExp(pattern).test(element.value);
				}
				if (!ok) {
					markRefill(element,'<?= __('js:form:error:input_incorrect',IS_JAVASCRIPT) ?> (%NAME%)' + (lement.title ? "\n" + element.title : ''));
					return false;
				}
			}
			let checkplaceholders = element.getAttribute('data-check-placeholders');
			if (checkplaceholders) {
				let pre = new RegExp(checkplaceholders);
				if (pre.test(element.value)) {
					markRefill(element,'<?= __('js:form:error:input_incorrect',IS_JAVASCRIPT) ?> (%NAME%) ' + (RegExp.$1));
					return false;
				}
			}
			if (element.nodeName === 'TEXTAREA'
			&&	element.name === 'PRESENCE'
			) {
				let lines = element.value.split(/[\r\n]+/);
				let oklines = [];
				let bad = false;
				for (let line of lines) {
					if (line.match(/^\w+\s*:\s*(http.*?)\s*$/i)) {
						line = RegExp.$1;
					}
					if (line.match(/^\s*[^\s]*\s[^\s*]\s*$/)) {
						markRefill(element, '<?= __('js:form:error:presence_spaces_used',IS_JAVASCRIPT) ?>');
						return false;
					}
					oklines.push(line);
				}
				element.value = oklines.join('\n');
				continue;
			}

			if (!valid) {
				continue;
			}
			if (value_is_empty) {
				// skip extra checks if value is empty
				continue;
			}

			switch (valid) {
			case 'lnglat':
				if (/^\s*\-?\d+\s*$/.test(element.value)) {
					markRefill(element,'<?= __('js:form:error:incorrect_coordinate',IS_JAVASCRIPT) ?> (%NAME%)');
					return false;
				}

			case 'float':
				if (!/^\s*\-?\d+(?:\.\d+)?\s*$/.test(element.value)) {
					markRefill(element,'<?= __('js:form:error:need_float',IS_JAVASCRIPT) ?> (%NAME%)');
					return false;
				}
				continue;

			case 'working-url':
				let ureq = initrequest(true);
				if (!ureq) {
					return false;
				}
				ureq.open('POST','/url.check', false);
				ureq.setRequestHeader('Content-Type','application/x-www-form-urlencoded');
				ureq.send('URL=' + encodeURIComponent(element.value));
				if (ureq.status !== 200) {
					markRefill(element, ureq.responseText);
					return false;
				}
				continue;

			case 'email':
			case 'working-email':
				if (!/^\s*(?:[a-zA-Z0-9_\.\-\+]+)@(?:(?:[a-zA-Z0-9_\.\-]+)\.(?:[a-zA-Z]{2,}))\s*$/.test(element.value)) {
					markRefill(element,'<?= __('js:form:error:need_email_address',IS_JAVASCRIPT) ?> (%NAME%)');
					return false;
				}
				if (valid == 'working-email'
				&&	typeof email_dones[element.value] === 'undefined'
				) {
					email_dones[element.value] = true;
					let ereq = initrequest(true);
					if (!ereq) {
						return false;
					}
					ereq.open('POST','/email.check',false);
					ereq.setRequestHeader('Content-Type','application/x-www-form-urlencoded');
					ereq.send('EMAIL=' + encodeURIComponent(element.value));
					if (ereq.status !== 200) {
						markRefill(element,ereq.responseText ? ereq.responseText : '<?= __('js:form:error:need_working_email_address',IS_JAVASCRIPT) ?> (%NAME%)');
						return false;
					}
				}
				continue;

			case 'number':
				if (valid === 'number'
				&&	!(element.getAttribute('step') ? /^\s*\d+(?:[,.]\d+)?\s*$/ : /^\s*\d+\s*$/).test(element.value)
				) {
					markRefill(element, '<?= __('js:form:error:need_number',IS_JAVASCRIPT) ?> (%NAME%)');
					return false;
				}
				if (value_is_empty) {
					// don't check min & max if value is empty
					continue;
				}
				let min = element.min;
				let max = element.max;
				if (min || max) {
					let num = parseInt(element.value);
					if (min && num < min) {
						markRefill(element,'<?= __('js:form:error:need_minimum',IS_JAVASCRIPT) ?> (%NAME%)',{MIN:min});
						return false;
					}
					if (max  && num > max) {
						markRefill(element,'<?= __('js:form:error:need_maximum',IS_JAVASCRIPT) ?> (%NAME%)',{MAX:max});
						return false;
					}
				}
				continue;

			case 'intl-tel':
				if (!/^\s*\+/.test(element.value)) {
					markRefill(element, '<?= __('js:form:error:phone_must_be_international',IS_JAVASCRIPT) ?>');
					return false;
				}

			case 'tel':
				let minlength = !element.form.COUNTRYID || element.form.COUNTRYID.value != 26 ? 10 : 9;
				let clean_phone = element.value.replace(/[\s+\-\+]/, '');
				let re_str = '^\\+?\\s*\\d[\\d\\-\\s]{' + (minlength - 2) + ',}\\d';
				let phone_re = RegExp(re_str);
				if (!phone_re.test(clean_phone)) {
					markRefill(element, '<?= __('js:form:error:invalid_phone', IS_JAVASCRIPT) ?>');
					return false;
				}
				let phone_length = clean_phone.length;
				if (phone_length < minlength) {
					markRefill(element, '<?= __('js:form:error:phone_too_short', IS_JAVASCRIPT) ?>');
					return false;
				}
				if (phone_length >= 20) {
					markRefill(element, '<?= __('js:form:error:phone_too_long', IS_JAVASCRIPT) ?>');
					return false;
				}
				if (clean_phone.match(/^(0|1|2|3|4|5|6|7|8|9|-)$1*$/)) {
					markRefill(element, '<?= __('js:form:error:invalid_phone',IS_JAVASCRIPT) ?>');
					return false;
				}
				continue;
			}
		}
		return true;
	} catch(e) {
		catchLog(e);
	}
	return false;
}

function submitForm(form, func, arg) {
 	try {
		if (form.submitted) {
			return false;
		}
		if (!form.elements) {
			return true;
		}

		form.submitted = true;
		lightForm(form, true);

		let bad = false;
		let captcha = getobj('theCaptcha');
		if (captcha
//		&&	!grecaptcha.getResponse()
		&&	!grecaptcha.enterprise.getResponse()
		) {
			markRefill(captcha);
			alert(captcha.getAttribute('data-fillme'));
			addevent(captcha, 'mouseover', clearRefill);
			bad = true;
		}
		if (bad
		||	!validateElements(form.elements)
		||	func
		&&	!func.call(this, form, arg)
		) {
			lightForm(form, false);
			form.submitted = false;
			return false;
		}
		return true;
	} catch (e) {
		catchLog(e);
	}
	return false;
}


// when back button of browser is pressed, make sure the form is sendable again:
addevent(
	window,
	'pageshow',
	function(event) {
		if (!event.persisted) {
			return;
		}
		for (const form of document.forms) {
			if (form.submitted) {
				lightForm(form, false);
				form.submitted = false;
			}
		}
	},
	false
);

function resetForm(form) {
	form.submitted = false;
	lightForm(form, false);
}

function lightForm(form, hide) {
	setclass(form, hide, 'light');
}

Pf.hideSubmits = function(form, hide) {
	for (const item of form.elements) {
		if (item.type === 'submit') {
			setdisplay(item, !hide);
		}
	}
}

function clearRefill(event) {
	remclass(this, 'refill');
	setdisplay('messages', false, true);
	remevent(this, event.type, clearRefill);
}

function markRefill(obj, errstr, map, type) {
	let form;
	if (obj.form) {
		form = obj.form;
	} else {
		form = obj;
		while ((form = form.parentNode) && !form.form) {}
	}
	obj.scrollIntoView({behavior: 'smooth'});
	Pf.scrollToMiddle(obj);
	addclass(obj, 'refill');
	addevent(obj, obj.nodeName === 'DIV' ? 'click' : ['change', 'keydown'], clearRefill);
	if (form) {
		resetForm(form);
	}
	Pf.focus(obj);
	if (!map) {
		map = {};
	}
	map.NAME = obj.name ? obj.name : obj.id;
	if (errstr) {
		showerrors(errstr, type ? type : ERR_TO_ALERT, map);
	}
}

function build_poststr(form,fields) {
	let utf8 = form.acceptCharset === 'utf-8';
	let poststr = '';
	for (const input of form.elements) {
		if (!input.name
		||	input.disabled
		) {
			continue;
		}
		// multi radio button as used in line-up forms
		// fix them for submission

		let use_name = input.name;
//		if (input.name.match(/^([A-Z_]+)_multi_[\da-f]+(\[\d*\])?$/)) {
//			use_name = RegExp.$1 + RegExp.$2;
//		}

		if (fields) {
			if (!function() {
				for (const field of fields) {
					if (field === use_name) {
						return true;
					}
				}
				return false;
			}()
			) {
				continue;
			}
		}
		switch (input.type) {
		case 'button':
			break;
		case 'checkbox':
		case 'radio':
			if (input.checked) {
				if (poststr) {
					poststr += '&';
				}
				poststr += use_name + '=' + (utf8 ? Pf.encodeForPost(input.value) : urlencode_utf8_to_windows1252(input.value)[0]);
			}
			break;
		default:
			if (poststr) {
				poststr += '&';
			}
			poststr += use_name + '=' + (utf8 ? Pf.encodeForPost(input.value) : urlencode_utf8_to_windows1252(input.value)[0]);
			break;
		}
	}
	return poststr;
}

function Pf_radioValue(radios) {
	if (!radios) {
		return null;
	}
	if (typeof radios.length === 'undefined') {
		// use top parent, because radios might not yet be added to document

		let p = radios.parentNode;
		while (p.parentNode) {
			p = p.parentNode;
		}
		let checked = p.querySelector('INPUT[name="'+radios.name+'"]:checked');
		return checked ? checked.value : null;
	}
	for (const radio of radios) {
		if (radio.checked) {
			return radio.value;
		}
	}
	return null;
}

String.prototype.repeat = function (n) {
	return new Array(n+1).join(this);
}

Pf.disable = function(id) {
	Pf.enable(id, false);
}

Pf.enable = function(arg, yes, ignore) {
	let objs = [];
	if (typeof arg === 'string') {
		arg = getobj(arg, !ignore);
	}
	if (typeof arg !== 'object') {
		return;
	}
	if (!(arg instanceof NodeList)
	&&	!(arg instanceof Array)
	&&	!(arg instanceof HTMLCollection)
	) {
		arg.disabled = !yes;
		if (arg.nodeName === 'FIELDSET') {
			let objs = arg.getElementsByTagName('input');
			if (objs) {
				Pf.enable(objs,yes);
			}
			objs = arg.getElementsByTagName('textarea');
			if (objs) {
				Pf.enable(objs,yes);
			}
		}
		return;
	}
	for (const obj of arg) {
		if (!obj) {
			continue;
		}
		if (obj instanceof NodeList) {
			Pf.enable(obj, yes ,ignore);
		} else {
			obj.disabled = !yes;
		}
	}
}

function focus_and_to_end(container, body, intoview, wtop) {
	if (typeof body === 'string'
	&&	!(body = getobj(body))
	||	typeof container === 'string'
	&&	!(container = getobj(container))
	) {
		return;
	}
	if (!Browser.iOS) {
		move_to_end(body);
	}
	if (intoview) {
		container.scrollIntoView(Browser.iOS || wtop);
	}
	if (!Browser.iOS) {
		Pf.focus(body);
	}
}

function move_to_end(textarea) {
	if (textarea.setSelectionRange) {
		let textLen = textarea.textLength ? textarea.textLength : textarea.value.length;
		textarea.setSelectionRange(textLen,textLen);
	}
	textarea.scrollTop = textarea.scrollHeight;
}

function Pf_addSeparator(input,separator) {
	if (input.value.length
	&&	!input.value.match(/,\s*$/)
	) {
		input.value += separator;
	}
}

Pf.cleanEmail = function(inp) {
	if (inp.value.match(/^(.*?)\?__/)) {
		inp.value = RegExp.$1;
	}
}

Pf.addSelectEnter = function(start) {
	let selects = start.getElementsByTagName('select');
	for (const select of selects) {
		let form = select.form;
		if (select.PF_addSelectEntered) {
			continue;
		}
		select.PF_addSelectEntered = true;
		// keyup make sure the submit button actuallt gets included in post
		// keydown fires too early
		addevent(select, 'keyup', function(event) {
			if (event.code !== 'Enter'
			&&	event.code !== 'NumpadEnter'
			) {
				// not a return? then don't act
				return;
			}
			event.stopPropagation();
			if (!form.onsubmit
			||	form.onsubmit()
			) {
				form.submit();
			}
			//select.disabled = true;
			return false;
		});
	}
}

addreadyevent(function(){
	Pf.addSelectEnter(document);
	for (document.getElementsByTagName('form') of forms) {
		if (form.method === 'get') {
			form.addEventListener('input', function() {
				form.submitted = false;
				lightForm(form, false);
			});
		}
	}
});

function Pf_onEnterSubmit(event, submitname) {
	// FIXME: submit with right value, contact section example submits buttons: MOVE, ANSWER, RESPOND
}
