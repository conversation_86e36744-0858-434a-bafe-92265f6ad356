// noinspection SpellCheckingInspection
function Pf_parseVideoURL(self) {
	let youtube_id =
		self.value.match(/\byoutube\.com\/(?:watch\?v=|v\/)(?<youtube_id>[\w\-_]{11})/i)?.groups.youtube_id
	||	self.value.match(/\byoutube\.com\/.*[?&]v=(?<youtube_id>[\w\-_]{11})(?:&.*)?$/i)?.groups.youtube_id
	||	self.value.match(/\byoutu\.be\/(?<youtube_id>[\w\-_]+)/i)?.groups.youtube_id
	||	self.value.match(/^(?<youtube_id>[\w\-_]{11})/i)?.groups.youtube_id;
	if (youtube_id) {
		self.value = youtube_id;
		self.form['TYPE'].value = 'youtube';
		return;
	}
	let vimeo_id = self.value.match(/\bvimeo\.com\/(?<vimeo_id>\d+)/i)?.groups.vimeo_id;
	if (vimeo_id) {
		self.value = vimeo_id;
		self.form['TYPE'].value = 'vimeo';
		// noinspection UnnecessaryReturnStatementJS
		return;
	}
	// let facebook_id = self.value.match(/\bfacebook\.com\/[^/]+\/videos\/(?<facebook_id>\d+)/i)?.groups.facebook_id;
	// if (facebook_id) {
	// 	self.value = facebook_id;
	// 	self.form['TYPE'].value = 'facebook';
	// 	return;
	// }
}
