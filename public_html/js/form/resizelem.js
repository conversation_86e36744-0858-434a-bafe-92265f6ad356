function resizelem() {
	var selects = document.getElementsByTagName('SELECT');
	if (!selects || !selects.length) return;
	for (var i = 0; i < selects.length; ++i) {
		var select = selects[i];
		if (!is_visible(select)) continue;
		var p = select.parentNode;
		while (p) {
			if (p.nodeName == 'TD') {
				break;
			}
			if (p.className.match(/(^|\\b)[rl](\\b|$)/)) {
				p = false;
				break;
			}
			p = p.parentNode;
		}
		if (p) {
			remclass(select,'fw');
			if (select.clientWidth > select.parentNode.clientWidth - 30) {
				addclass(select,'fw');
			}
		}
	}
}
addevent(window,'resize',resizelem);
addreadyevent(resizelem);

