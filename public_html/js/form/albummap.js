function changeMapsOrder(maporderobj) {
	if (maporderobj.selectedIndex == 1) {
		var showdateobj = getobj('show-connected-date');
		if (showdateobj) {
			showdateobj.checked = true;
		}
	}
}
function clickShowDate(showdateobj) {
	if (showdateobj.checked) {
		var maporderobj = getobj('map-order');
		if (maporderobj) {
			maporderobj.selectedIndex = 1;
		}
	}
}
function changeVisibility(selectobj) {
	if (selectobj.options[selectobj.selectedIndex].disabled) {
		alert('Onmogelijke keuze!');
		selectobj.selectedIndex = selectobj.onfocusIndex;
		return;
	}
	var dobj = getobj('visidescend' + selectobj.name);
	if (!dobj) {
		return;
	}
	dobj.checked = true;
	cbclick(dobj,dobj.parentNode);
}
function changeVisiChildren(slct,lst) {
	for (var i = 0; i < lst.length; ++i) {
		let oslct = getobj(lst[i]);
		if (!oslct) {
			continue;
		}
		let opts = oslct.options;
		let choosenew = false;
		for (var j = 0; j < opts.length; ++j) {
			var opt = opts[j];
			opt.disabled = opt.value < slct.value;
			if (opt.disabled) {
				if (oslct.selectedIndex == j) {
					choosenew = true;
				}
			} else if (choosenew) {
				oslct.selectedIndex = j;
				changeVisibility(oslct);
				choosenew = false;
			}
		}
	}
}
function initVisi() {
	const vmap = getobj('visimap');
	if (!vmap) {
		return;
	}
	changeVisiChildren(vmap,['visicmts','visiwrtecmts']);
}
