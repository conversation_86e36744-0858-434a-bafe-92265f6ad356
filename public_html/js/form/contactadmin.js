function openContactForm(self) {
	unhide('contactresponseform');

	let form = getobj('responseform', true);
	if (!form) {
		return form;
	}
	unhide('movelink', 'forwardlink', 'sep');

	enableProcessLink(self, false);

	setattr(form.MSGBODY,   'required', true);
	setattr(form.TO_USERID, 'required', form.FORWARDIT.checked);

	let optstore = getobj('optstore', true);
	if (optstore) {
		let opts = getobj('forwardoptions', true);
		if (opts) {
			hide(form.FORWARD);
			optstore.appendChild(opts);
		}
		opts = getobj('moveoptions', true);
		if (opts) {
			hide(form.MOVE);
			optstore.appendChild(opts);
		}
	}
	moveKeep(form, false);

	Pf_fitTextarea(form.MSGBODY);
	Pf.focus	  (form.MSGBODY);

	return form;
}

function enableProcessLink(self,enable) {
	if (!self
	&&	!(self = getobj('processlink',true))
	) {
		return;
	}
	self.disabled = !enable;
	self.className = enable ? 'unhideanchor' : 'seemtext light';
}

function moveKeep(form, tofloat) {
	let fs = getobj('keeplockfloat', true);
	if (!fs) {
		return;
	}
	if (tofloat ? fs.firstChild : !fs.firstChild) {
		return;
	}
	let ls = getobj('keeplocklabel',	true);
	let cs = getobj('keeplockcheckbox', true);
	if (!ls || !cs) {
		return;
	}
	let kl;
	if (tofloat) {
		fs.appendChild(cs.firstChild);
		fs.appendChild(ls.firstChild);
		kl = cbclickp;
	} else {
		cs.appendChild(fs.firstChild);
		ls.appendChild(fs.firstChild);
		kl = cbclickppp;
	}
	form.KEEPLOCK.onclick = function(){kl(form.KEEPLOCK)};
	form.KEEPLOCK.onclick();
}

function openMoveOptions(self) {
	let opts = getobj('forwardoptions', true);
	if (!opts) {
		return;
	}
	hide(opts);
	// move forward to end, to make move the default option
	self.parentNode.appendChild(opts);

	if (!(opts = getobj('moveoptions', true))) {
		return;
	}

	hide(self, 'contactresponseform', 'forwardoptions');
	unhide('forwardlink', 'sep');
	//opts.parentNode.removeChild(opts);

	self.parentNode.insertBefore(opts, self);
	enableProcessLink(null, true);

	let form = opts.firstChild.form;

	setattr([form.MSGBODY, form.TO_USERID], 'required', false);
	unhide(opts,form.MOVE);

	if (form.FORWARDIT.checked) {
		form.FORWARDIT.checked = false;
		forwarditclick(form.FORWARDIT);
	}
	if (!form.MOVEIT.checked) {
		form.MOVEIT.checked = true;
		moveitclick(form.MOVEIT);
	}
	updateKeepLock(form);

	moveKeep(form, true);
}

function openForwardOptions(self) {
	let opts = getobj('forwardoptions', true);
	if (!opts) {
		return;
	}
	hide(self, 'contactresponseform', 'moveoptions');
	unhide('movelink', 'sep');
//	opts.parentNode.removeChild(opts);

	self.parentNode.insertBefore(opts, self);
	enableProcessLink(null, true);

	let  form = opts.firstChild.form;

	setattr(form.MSGBODY,   'required', false);
	setattr(form.TO_USERID, 'required', true);
	unhide(opts, form.FORWARD);

	if (!form.FORWARDIT.checked) {
		form.FORWARDIT.checked = true;
		forwarditclick(form.FORWARDIT);
	}
	if (form.MOVEIT.checked) {
		form.MOVEIT.checked = false;
		moveitclick(form.MOVEIT);
	}
}

function updateKeepLock(form) {
	let check = form.ELEMENT.value === form.ELEMENT.getAttribute('data-original');
	if (form.KEEPLOCK.checked !== check) {
		form.KEEPLOCK.checked = check;
		form.KEEPLOCK.onclick();
	}
}

function changeMoveOption(self) {
	updateKeepLock(self.form);
	let trans = self.getAttribute('data-idtranslation');
	if (!trans) {
		self.form.ID.value = self.form.ELEMENT.getAttribute('data-original') === self.form.ELEMENT.value ? self.form.ID.getAttribute('data-original') : '';
		return;
	}
	let pairs = trans.split(';');
	for (let i = 0; i < pairs.length; ++i) {
		let pair = pairs[i];
		let info = pair.split('=');
		let elements = info[0].split(',');
		let id = info[1];
		for (let j = 0; j < elements.length; ++j) {
			let element = elements[j];
			if (element === self.value) {
				self.form.ID.value = id;
				return;
			}
		}
	}
	self.form.ID.value = '';
}

function forwarditclick(self, inboxaction) {
	cbclickpp(self);
	setdisplay('forwardoptions', self.checked);
	if (self.checked) {
		Pf.focus(self.form.TO_USERID);
	}
	setattr(self.form.TO_USERID, 'required', self.checked);

	if (self.form.INBOXACTION.checked) {
		self.form.INBOXACTION.checked = false;
		cbclickpp(self.form.INBOXACTION);
	}
}

function moveitclick(self) {
	cbclickpp(self);
	setdisplay('moveoptions', self.checked);
	if (self.checked) {
		Pf.focus(self. form.ELEMENT);
	}
}

function inboxaction(self) {
	cbclickpp(self);
	if (self.form.FORWARDIT.checked) {
		self.form.FORWARDIT.checked = false;
		setdisplay('forwardoptions', false);
		setattr(self.form.TO_USERID, 'required', false);
		cbclickpp(self.form.FORWARDIT);
	}
}

function overSubmit(self, dir) {
	let form = self.form;
	if (self.form.submitted) {
		return;
	}
	form['DIRECTION'].value = dir;
	let out = dir == 'tounknown';
	let rbox = getobj('responsebox');
	if (rbox) {
		rbox.className = 'contact-' + dir + ' box';
	}
	let other =
			(	dir == 'touser'
			||	dir == 'progress'
			)
		&&	self.form.SENDTO
		?	self.form.SENDTO.value === 'OTHER'
		:	false;

	setattr(self.form.EMAIL,'required', other);
}

function changeSendto(sendto) {
	let other = sendto.value == 'OTHER';
	setdisplay(sendto.nextSibling, other);
	let name = null;
	if (!other) {
		name = sendto.options[sendto.selectedIndex].getAttribute('data-first-name');
	}
	changeHeader(name);
	setclass(sendto, sendto.options[sendto.selectedIndex].hasAttribute('data-ccs'), 'warning');
}

function changeEmail(self) {
	let name = self.value;
	if (name.match(/^([^\s@]+)/)) {
		name = RegExp.$1;
	}
	let parts = RegExp.$1.replace(/[^\w]/,' ').split(' ');
	name = parts[0].charAt(0).toUpperCase() + parts[0].substr(1);
	changeHeader(name);
}

function changeHeader(name) {
	letheaderobj = getobj('header', true);
	if (!headerobj) {
		return;
	}
	let tmpl = headerobj.getAttribute(name ? 'data-template' : 'data-template-ls');
	if (!tmpl) {
		return;
	}
	if (name) {
		if (!tmpl.match(/#(?:NAAM|NAME)#/)) {
			return;
		}
		headerobj.value = tmpl.replace(/#(?:NAAM|NAME)#/,name);
	} else {
		headerobj.value = tmpl;
	}
}

function submitResponseForm(form) {
	if (form.MOVEIT.checked
		// all strings:
	&&	form.ELEMENT.value === form.ELEMENT.getAttribute('data-original')
	&&	form.ID.	 value === form.ID.	 	getAttribute('data-original')
	) {
		alert(form.MOVE.getAttribute('data-errormsg'));
		Pf.focus(form.ELEMENT);
		return false;
	}
	return submitForm(form);
}

function infoMsg(self) {
	cbclickpp(self);
	if (self.getAttribute('data-recently')) {
		setclass(self.parentNode.parentNode,!self.checked,'light');
		setclass('infomsgwarn',self.checked,'warning');
		setclass('infomsgwarn',!self.checked,'light');
	}
	setdisplay('infomsg',self.checked,true);
}

function Pf_changePendingReturn(self) {
	let a = self.parentNode.firstChild;
	a.href = a.href.replace(/;RETURN=\d+,\d+/,';RETURN=' + self.value);
}

function change_subject(event,subj,ticketid) {
	let row = subj.parentNode.parentNode;
	if (event.code === 'Enter'
	||	event.code === 'NumpadEnter'
	) {
		let req = initrequest(true);
		if (!req) {
			return;
		}
		req.open('POST', `/contact_ticket/${ticketid}.act`, false);
		req.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
		req.send('ACTION=set_subject&SUBJECT=' + urlencode_utf8_to_windows1252(subj.value)[0]);
		if (req.status === 200) {
			row.className = '';
			subj.setAttribute('data-orig', subj.value);
		} else {
			let errstr = req.getResponseHeader('Pf-Error');
			if (errstr) {
				alert(errstr);
			} else {
				alert('Er ging iets mis!');
			}
		}
	} else {
		row.className =
			subj.value != subj.getAttribute('data-orig')
		?	'hilited'
		:	'';
	}
}
