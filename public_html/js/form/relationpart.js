function changeRelation(self) {
	var relationid = self.value;
	var previd = self.getAttribute('data-selected');
	if (previd == relationid) return;
	hide('relation_' + previd);
	unhide('relation_' + relationid);
	self.setAttribute('data-selected',relationid);
	var form = self.form;
	setattr([
		form.RELATION_NAME,
		form.RELATION_CONTACT,
		form.RELATION_CONTACTEMAIL,
		form.RELATION_INVOICEMAIL,
		form.RELATION_ADDRESS_OR_POBOX,
		form.RELATION_ZIPCODE,
		form.COUNTRYID,
		form.CITYID ? form.CITYID : null
	],'required',relationid.is_empty());
}
