Pf.submitMusicSearchForm = function(form) {
	if (!form.TERMS.value.mytrim().length) {
		if (form.GENRE.value) {
			location.href = '/music/genre/' + form.GENRE.value;
			return false;
		}
	}
	return submitForm(form);
}

function submitMusicForm(form) {
	if (!checkProducers(form)) {
		markRefill(getobj('produceroptions'),'Je moet ten minste een producer kiezen!');
		return false; 
	}
	return submitForm(form);
}
function checkProducers(form) {
	var types = ['artist','user','alias'];
	for (var typei in types) {
		var type = 'PRODUCER['+types[typei]+'][]';
		if (!form[type]) {
			continue;
		}
		var ids = form[type];
		if (!ids.length) {
			if (ids.checked
			||	ids.type == 'hidden'
			) {
				return true;
			}
		} else {
			var len = ids.length;
			for (var i = 0; i < len; ++i) {
				if (ids[i].checked) {
					return true;
				}
			}
		}
	}
	return false;
}
