function Pf_extendTextareas(obj) {
	let textareas = obj.getElementsByTagName('TEXTAREA');
	if (!textareas
	||	!textareas.length
	) {
		return;
	}
	for (const textarea of textareas) {
		if (textarea.Pf_fake) {
			continue;
		}
		addevent(textarea, 'drop', Pf_dropExtendTextarea);
	}
}
addreadyevent(function(){
	Pf_extendTextareas(document)
});
/** @this {HTMLTextAreaElement} */
function Pf_dropExtendTextarea(event) {
	if (!event.dataTransfer
	||	!event.dataTransfer.getData
	||	Pf.getParent(this, 'dragcatch')
	) {
		return;
	}
	let data = event.dataTransfer.getData('text/html');
	if (!data) {
		return;
	}
	let tag = null;
	if (data.match(/<img.*?src="([^"]+)"/)) {
		tag = '[img]' + RegExp.$1 + '[/img]';
	} else if (data.match(/<a\s+.*href="([^"]+)".*?>(.*?)<\/a>$/)) {
		tag = '[url='+RegExp.$1+']' + RegExp.$2 + '[/url]';
	}
	if (!tag) {
		return;
	}
	event?.preventDefault?.();
	/*	let ss = this.selectionStart ? this.selectionStart : this.savedSelectionStart;
		if(ss) {
			this.value =
				this.value.substring(0, ss) +
				tag +
				this.value.substring(this.savedSelectionEnd, this.value.length);
	} else {*/
	if (this.selectionStart) {
		this.value =
			this.value.substring(0, this.selectionStart) +
			tag +
			this.value.substring(this.selectionEnd, this.value.length);
	} else {
		this.value += tag;
	}
}
