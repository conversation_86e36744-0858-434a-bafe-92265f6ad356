
// Empty word list means the language will not be detected (total counts always 0)

const language_word_lists = [
	['\\bthe\\b',	 '\\bwith\\b', '\\bit\\b', 	   '\\bs?he\\b', '\\band\\b',  '\\bfor\\b',  '\\bas\\b'],	// English
	['\\b[hz]ij\\b', '\\been\\b',  '\\bals\\b',	   '\\bhet\\b',	 '\\ben\\b',   '\\bvan\\b',  '\\bmet\\b'],	// Dutch
	['\\bles\\b',	 '\\bil\\b',   '\\bla\\b',	   '\\best\\b',  '\\bpour\\b', '\\bavec\\b', '\\bpar\\b'],	// French
	['\\bder\\b',	 '\\bist\\b',  '\\bein\\b',	   '\\bund\\b',	 '\\bzu\\b',   '\\bvon\\b',  '\\bzu\\b'],	// German
	['\\bhay\\b',	 '\\busted\\n','\\baños\\b',   '\\bahora\\b','\\bmuy\\b',  '\\béxito\\b','\\bfue\\b', '\\buna\\b', '\\bpara\\b'], // Spanish
	[], // Italian
	[], // Polish
	['\\b[ns]ão\\b', '\\buma?\\b', '\\btambém\\b', '\\bentão\\b', '\\bpois\\b', '\\bdos\\b', '\\bel[ea]\\b'], // Portugese
];

/**
 * @param {String} body
 * @param {HTMLSelectElement} select
 * @returns {void}
 */
function Pf_detectLanguage(body, select) {
	/** @var {number[]} */
	const total_counts = [];
	for (let langid = 0; langid < language_word_lists.length; ++langid) {
		console.log(`checking language ${langid}`);
		total_counts[langid] = 0;
		for (const word of  language_word_lists[langid]) {
			const found_count = Pf.countInstances(body, word, true);
			console.log(`found ${found_count} instances of "${word}"`);
			total_counts[langid] += found_count;
		}
	}
	let best_langid = 0;
	let best_total  = total_counts[0];
	for (let langid = 1; langid < language_word_lists.length; ++langid) {
		if (best_total === null
		||	total_counts[langid] > best_total
		) {
			best_total =  total_counts[langid];
			best_langid = langid;
		}
	}
	select.selectedIndex = best_langid;
}
