/*function typeChange(self) {
	let type = self.value;
	['upload', 'iframe', 'script', 'link', 'html', 'textlinks', 'ubb', 'multiparty'].forEach(function(part) {
		setdisplay(part+'part', part === type, true);
	});
	let form = self.form;
	setattr(form.UBB_BODY,						'required', type === 'ubb');
	setattr(form.UPLOAD_URL,					'required', type === 'upload');
	setattr(form.IFRAME_URL,					'required', type === 'iframe');
	setattr([form.LINK_URL,form.LINK_CLICKURL],'required', type === 'link');
	setattr(form.SCRIPT_SCRIPTURL,				'required', type === 'script');
}*/

function changeBG(self) {
	let nv;
	if (!self.value.length) {
		nv = null;
	} else {
		if (!self.value.match(/^[\da-fA-F]{6}$/)) {
			return;
		}
		nv = '#' + self.value;
	}
	let box = getobj('bannerbox');
	if (box) {
		box.style.backgroundColor = nv;
	}
	self.style.backgroundColor = nv;
}

function Pf_bannerUploadChangeFile(self) {
	setdisplay('upload-to-webp-row', self.files?.[0]?.type !== 'image/webp'
								  && self.files?.[0]?.type !== 'image/png'
								  && self.files?.[0]?.type !== 'image/apng');
	setdisplay('upload-to-mp4-row',  self.files?.[0]?.type === 'image/gif');
}

function Pf_changePartyID(self, row_name) {
	let prntrow = getobj(row_name,true);
	if (!prntrow) {
		return;
	}
	let total_empty = 0;
	let other_empty = null;

	for (const elem of self.form.elements) {
		if (elem.name !== 'PARTYID[]') {
			continue;
		}
		if (elem.value.is_empty()) {
			++total_empty;
			if (elem !== self) {
				other_empty = elem;
			}
		}
	}
	if (total_empty <= 0) {
		let cp = prntrow.firstChild.cloneNode(true);
		if (cp) {
			let inp = cp.firstChild.nextSibling.firstChild;
			inp.value = '';
			inp.onchange(inp);
			setdisplay(cp.lastChild, false);
			prntrow.appendChild(cp);
		}
	} else if (total_empty > 1) {
		changerow(other_empty,'remove');
	}
}
