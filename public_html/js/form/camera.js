function Pf_changeGlobalCameraStatus(global_status_select) {
	const global_status = global_status_select.value;
	const global_status_bg = global_status ? `cam-bg-${global_status}` : '';
	const global_status_row = Pf.getParentNode(global_status_select, 'TR');
	global_status_row.className = global_status_bg;

	const selects = global_status_select.form.getElementsByTagName('SELECT');
	if (!selects) {
		return;
	}

	let empties = 0;

	for (let inputelem of selects) {
		if (inputelem.value === ''
		&&	/^USER\[\d+]$/.test(inputelem.name)
		) {
			++empties;
		}
	}
	if (empties > 1) {
		return;
	}
	for (/** @type {HTMLSelectElement} */ const inputelem of selects) {
		if ( inputelem.type === 'select-one'
		||	!inputelem.name.match(/^(?:NEWSTATUS|USER)\[\d+]$/)
		) {
			continue;
		}
		const initial = inputelem.getAttribute('data-initial');

		if ([null, '', 'chosen', 'accepted', 'denied'].includes(initial)) {
			const status = global_status === 'extern' ? 'not_chosen' : global_status;
			const inputelem_row = Pf.getParentNode(inputelem, 'TR');
			inputelem.value = status;
			inputelem_row.className = `cam-bg-${status}`;
		}
	}
}

function Pf_changeSingleCameraStatus(user_status_select) {
	const row = Pf.getParentNode(user_status_select, 'TR');
	if (!row) {
		return;
	}
	const user_status = user_status_select.value;

	// propagate user status to global status when appropriate
	// statuses in this first array should NOT be propagate

	if (![	'chosen',
			'not_chosen',
			'failed_photographer',
			'failed_photographer_force_majeur',
			'transferred',
			'take_over',
	].includes(user_status)) {
		const selects = user_status_select.form.getElementsByTagName('SELECT');
		if (selects) {
			for (const inputelem of selects) {
				if (inputelem.name === 'STATUS') {
					if (inputelem.value !== user_status) {
						inputelem.value = user_status;
						inputelem.dispatchEvent(new Event('change'));
					}
				} else if (
					user_status !== 'self'
				&&	user_status !== ''
				&&	inputelem.value === ''
				&&	/^(?:NEWSTATUS|USER)\[\d+]$/.test(inputelem.name)
				) {
					inputelem.value = 'not_chosen';
					if (!is_visible(inputelem)) {
						changerow(inputelem, 'hide');
					}
				}
			}
		}
	}
	row.className = user_status ? `cam-bg-${user_status}` : '';
}

/**
 * @param {HTMLSpanElement} self
 * @returns {void}
 */
function Pf_addPhotographer(self) {
	setdisplay('aspiring', true);
	setdisplay(this, 		false);
	const form = Pf.getParentNode(self, 'FORM');
	if (!form) {
		return;
	}
	const new_users = form['NEWUSER[]'];
	const aspiring = new_users.length ? new_users[length - 1] : new_users;
	aspiring.disabled = false;
	Pf.removeNode(self);
}
/**
 * @param {HTMLSelectElement} self
 * @returns {void}
 */
function Pf_addPhotographerToOptions(self) {
	let option;
	if (self.PF_ADDED) {
		option = self.PF_ADDED;
	} else {
		option = document.createElement('option');
		self.form['REQUESTFOR'].add(option);
		self.PF_ADDED = option;
	}
	option.value = self.value;
	option.text  = self.nextSibling.textContent
}
