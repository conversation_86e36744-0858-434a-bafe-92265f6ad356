<?php

define('CURRENTSTAMP',time());

require_once '../../_offline.inc';
require_once '../../_modified.inc';
require_once '../../_hashes.inc';
require_once '../../_currentuser.inc';
require_once '../../_require.inc';

function bail($errno) {
	http_response_code($errno);
	exit;
}

$currentuser = new _currentuser(TINY_USER | SKIP_SETTINGS);

if (!have_user()) {
	bail(403);
}

$albummaps = db_rowuse_array(
	'albummap','
	SELECT MAPID,COLOR,MSTAMP
	FROM albummap
	WHERE ALBUMID='.CURRENTUSERID
);
if ($albummaps === false) {
	bail(500);
}
if (!$albummaps) {
	return;
}
$mstamp = 0;
foreach ($albummaps as $albummap) {
	if ($albummap['MSTAMP'] > $mstamp) {
		$mstamp = $albummap['MSTAMP'];
	}
}

if (not_modified($mstamp, get_hash('js/form/albummapforelement.js.php'))) {
	bail(304);
}

# Overwritten in Apache compression,conf
header('Cache-Control: must-revalidate,private');
header('Content-Type: text/javascript; charset=us-ascii');

?>function setColorToMap(formid,boxid,mapid){<?
	?>var boxobj = getobj(boxid);<?
	?>if (!boxobj) {<?
		?>return<?
	?>}<?
	?>var formobj = getobj(formid);<?
	?>if (!formobj) {<?
		?>return;<?
	?>}<?
	?>switch(mapid){<?
	foreach ($albummaps as $albummap) {
		?>case '<?= $albummap['MAPID']; ?>':<?= "\n";
			?>boxobj.className='<?= $albummap['COLOR']; ?> box';<?= "\n";
			?>formobj.COLOR.value='<?= $albummap['COLOR']; ?>';<?= "\n";
			?>break;<?
	}
	?>default:<?
		?>boxobj.className='<?= $color = memcached_color(CURRENTUSERID); ?> box';<?
		?>formobj.COLOR.value='<?= $color; ?>';<?= "\n";
		?>break<?
	?>}<?
?>}
