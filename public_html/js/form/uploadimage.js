const CROP_VARIABLE_MULTIPLIER = 1000000000;

/**
 * @param {Element} self
 * @param {integer} upimgid
 */
function Pf_chooseDefaultImage(self, upimgid) {
	const sel = Pf.getParent(self,'uimg-selector');
	if (!sel) {
		return;
	}
	const linkdata = Pf.getLinkData(self);
	if (parseInt(linkdata.dflt) === upimgid) {
		return;
	}
	do_inline('POST','/choosedefupimg.act','ACT',function(req) {
		if (req.status === 200) {
			const uimg = Pf.getParent(sel,'uimg');
			if (!uimg) {
				return;
			}
			const newNode = document.createElement('div');
			newNode.innerHTML = req.responseText;
			uimg.parentNode.replaceChild(newNode.firstChild, uimg);
		} else {
			showerrors(req,ERR_TO_ALERT);
		}
	},
	`${linkdata.post}&UPIMGID=${upimgid}`);
}

/**
 * @param {PointerEvent} event
 * @param {Element} self
 * @param {integer} upimgid
 * @param {boolean} generated
 * @param {boolean} really
 * @returns {void}
 */
function Pf_removeImageLink(event, self, upimgid, generated, really) {
	event.stopPropagation();
	let conf;
	if (really
	&&	(conf = self.getAttribute('data-confirm'))
	&&	!confirm(conf)
	) {
		return;
	}
	let flags = 0;
	let size = 'regular';
	const with_id_container = Pf.getParent(self, 'with-inline-id');
	if (with_id_container) {
		const form = getobj(with_id_container.id + '-form');
		if (!form) {
			return;
		}
		size  = form.getAttribute('data-size');
		flags = with_id_container.getAttribute('data-flags');
		if (flags === null) {
			flags = 0;
		}
	}
	const chooser = Pf.getParent(self, 'uimg-chooser');

	do_inline('POST', '/removeupimg.act', 'ACT', function(req) {
		if (req.status !== 200) {
			showerrors(req, ERR_TO_ALERT);
			return;
		}
		if (req.responseText) {
			const uimg = Pf.getParent(self,'uimg');
			let newNode = document.createElement('div');
			newNode.innerHTML = req.responseText;
			uimg.parentNode.replaceChild(newNode.firstChild, uimg);
			return;
		}
		const generated = chooser && -1 !== chooser.className.indexOf('uimg-generated');
		const uimg = Pf.getParent(self,'uimg');
		const rems = [];
		if (generated) {
			Pf.removeNode(chooser);
		}
		let chosen = null;
		const containers = uimg.getElementsByClassName('uimg-container');
		for (const container of containers) {
			if (parseInt(container.getAttribute('data-upimgid')) === upimgid) {
				if (!generated) {
					const tmp_chooser = Pf.getParent(container,'uimg-chooser');
					if (tmp_chooser) {
						// generated
						rems.push(tmp_chooser);
					} else {
						// main image
						rems.push(container);
						if (container.nextSibling
						&&	container.nextSibling instanceof Element
						&&	container.nextSibling.className.match(/date/)
						) {
							rems.push(container.nextSibling);
						}
						if (container.previousSibling
						&&	container.previousSibling instanceof Element
						&&	container.previousSibling.tagName === 'META'
						) {
							rems.push(container.previousSibling);
						}
					}
				}
			} else if (-1 !== container.className.indexOf('uimg-generated-chosen')) {
				chosen = container;
			}
		}
		if (!chosen) {
			const containers = uimg.getElementsByClassName('uimg-generated');
			if (containers && containers.length) {
				addclass(containers[0], 'uimg-generated-chosen');
			}
		}
		if (!generated) {
			Pf.removeNode(rems);
			// no uimg-containers left?
			let selectors = uimg.getElementsByClassName('uimg-selector');
			if (!selectors
			||	!selectors.length
			) {
				return;
			}
			let selector = selectors[0];
			if (selector
			&&	selector.firstChild === selector.lastChild
			&&	selector.firstChild.nodeName === 'HR'
			) {
				Pf.removeNode(selector);
			}
		}
	},
		Pf.getLinkData(self).post
		+ `&UPIMGID=${upimgid}`
		+ (generated ? `&GENERATED=${generated}` : '')
		+ (really	 ? '&DEACTIVATE=1' : '')
		+ (size		 ? `&SIZE=${size}` : '')
		+ (flags	 ? `&FLAGS=${flags}` : '')
	);
}

Pf.getLinkData = function(node) {
	const container	= Pf.getParent(node,'uimg-container');
	const uimg		= Pf.getParent(node,'uimg');

	return {
		type:	uimg.getAttribute('data-linktype'),
		id:		uimg.getAttribute('data-linkid'),
		flags:	uimg.getAttribute('data-flags') || 0,
		dflt:	uimg.getAttribute('data-default'),

		upimgid:container ? container.getAttribute('data-upimgid') : null,

		get post() {
			return	`TYPE=${this.type}`
				+	`&ID=${this.id}`
				+	(this.upimgid ? `&UPIMGID=${this.upimgid}` : '')
				+	`&FLAGS=${this.flags}`
		}
	};
};

Pf.clickCopyright = function(event, self) {
	event.stopPropagation();
	event.preventDefault();
	if (self.PF_INPUT) {
		return;
	}
	self.PF_INPUT = true;
	self.PF_CLASS = self.className;
	self.className = 'uimg-cp wide abs';
	hide(self.firstChild);
	self.lastChild.innerHTML = `<input autofocus name="COPYRIGHT[]" onkeydown="Pf.enterCopyright(event, this)" type="text" value="${self.lastChild.innerHTML}" />`;
	self.lastChild.firstChild.select();
};

Pf.enterCopyright = function(event, self) {
	if (event) {
		if (event.type !== 'keydown'
		||	event.code !== 'Enter'
		&&	event.code !== 'NumpadEnter'
		) {
			return;
		}
		event.stopPropagation();
		event.preventDefault();
	}
	const container = Pf.getParent(self,'uimg-container');
	const upimgid	= container.getAttribute('data-upimgid');
	if (!upimgid) {
		// new image, not yet stored
		return;
	}
	const store = self.value;
	const div = self.parentNode.parentNode;
	// const linktype = container.getAttribute('data-linktype');
	// const linkid = container.getAttribute('data-linkid');

	do_inline('POST', '/savecp.act', 'ACT', function(req) {
		if (!req.ok) {
			showerrors(req, ERR_TO_ALERT);
			return;
		}
		div.PF_INPUT = false;
		div.className = div.PF_CLASS;
		unhide(div.firstChild);
		div.lastChild.innerHTML = store;
	},
	Pf.getLinkData(self).post + '&COPYRIGHT=' + encodeURIComponent(store));
};

Pf.chooseGenerated = function(self,upimgid,generated) {
	if (-1 !== self.className.indexOf('chosen')) {
		return;
	}
	do_inline('POST','/choosegenerated.act','ACT',function(req) {
		if (!req.ok) {
			showerrors(req,ERR_TO_ALERT);
			return;
		}
		const generateds = self.parentNode.getElementsByClassName('uimg-generated');
		if (!generateds || !generateds.length) {
			return;
		}
		for (const generated of generateds) {
			remclass(generated, 'uimg-generated-chosen');
		}
		addclass(self, 'uimg-generated-chosen');
	},
	Pf.getLinkData(self).post + `&UPIMGID=${upimgid}&GENERATED=${generated}`);
};

Pf.startCrop = function(e, self, upimgid, min_width_part, min_height_part) {
	e.stopPropagation();
	if (self.PF_OVERLAY) {
		do_inline('POST', '/getcrop.act', 'ACT', function(req) {
			if (!req.ok) {
				showerrors(req,ERR_TO_ALERT);
				return;
			}
			document.body.appendChild(self.PF_OVERLAY);
			self.PF_OVERLAY.style.visibility = 'visible';
			window.addEventListener('mouseup', self.PF_CROP.Pf_mouseUp);
			window.addEventListener('keydown', self.PF_CROP.Pf_cropCloser);
			if (req.responseText) {
				self.PF_CROP.Pf_setFromStored(JSON.parse(req.responseText));
			}
		},
		Pf.getLinkData(self).post + `&UPIMGID=${upimgid}`);
		return;
	}

	const p = self.parentNode;
	const orig_imgs = p.getElementsByClassName('the-uimg');
	const orig_img = orig_imgs[0];

	const o = document.createElement('div');
	o.className = 'crop-over';

	const i = document.createElement('div');
	i.className = 'container';

	o.appendChild(i);

	const img = orig_img.cloneNode(true);
	img.style = '';
	const intrinsic_width  = img.getAttribute('data-intrinsic-width');
	const intrinsic_height = img.getAttribute('data-intrinsic-height');
	if (intrinsic_width  < window.innerWidth  - 20
	&&	intrinsic_height < window.innerHeight - 20
	) {
		img.width  = intrinsic_width;
		img.height = intrinsic_height;
	}
	img.addEventListener('click', function(e) {
		e.stopPropagation();
	});

	i.appendChild(img);

	document.body.appendChild(o);

	img.onload = function() {
		const crop = document.createElement('div');
		const min = Math.min(
			min_width_part  * img.offsetWidth,
			min_height_part * img.offsetHeight
		) + 'px';
		crop.className = 'uimg-crop';
		crop.style.minWidth  = min;
		crop.style.minHeight = min;


		i.appendChild(crop);

		crop.style.left = img.offsetLeft + 'px';
		crop.style.top  = img.offsetTop  + 'px';

		crop.PF_ORIGINAL = self;
		crop.PF_OVERLAY = o;
		self.PF_OVERLAY = o;
		self.PF_CROP = crop;

		crop.Pf_cropCloser = function(event) {
			switch (event.code) {
			case 'Escape':
				event.stopPropagation();
				Pf.closeCrop(crop);
				break;

			case 'Space':
				event.stopPropagation();
				event.preventDefault();
				crop.Pf_storeSquare();
				break;
			}
		};
		crop.Pf_setFromStored = function(stored) {
			if (!stored
			||	!('WIDTH'  in stored)
			||	!('HEIGHT' in stored)
			||	!('X'	   in stored)
			||	!('Y'	   in stored)
			) {
				console.warn('invalid stored crop', stored);
				return false;
			}
			const w = img.offsetWidth  * stored.WIDTH  / CROP_VARIABLE_MULTIPLIER;
			const h = img.offsetHeight * stored.HEIGHT / CROP_VARIABLE_MULTIPLIER;
			if (w < 50
			||	h < 50
			) {
				return false;
			}
			crop.style.width  = w + 'px';
			crop.style.height = h + 'px';
			let top  = img.offsetTop  + img.offsetHeight * stored.Y / CROP_VARIABLE_MULTIPLIER;
			let left = img.offsetLeft + img.offsetWidth  * stored.X / CROP_VARIABLE_MULTIPLIER;
			crop.style.top  = top  + 'px';
			crop.style.left = left + 'px';
			crop.Pf_setMaxes();
			return true;
		};
		crop.Pf_storeSquare = function() {
			if (crop.offsetWidth !== crop.offsetHeight) {
				const max = Math.max(crop.offsetWidth, crop.offsetHeight) + 'px';
				crop.style.width  = max;
				crop.style.height = max;
			}
			let linkdata = Pf.getLinkData(crop.PF_ORIGINAL);

			let w = crop.offsetWidth  / img.offsetWidth;
			let h = crop.offsetHeight / img.offsetHeight;
			let x = (crop.offsetLeft - img.offsetLeft) / img.offsetWidth;
			let y = (crop.offsetTop  - img.offsetTop)  / img.offsetHeight;

			if (x < 0) {
				alert('x < 0 ' + crop.offsetLeft + ' - ' + img.offsetLeft);
				x = 0;
			}
			if (y < 0) {
				alert('y < 0 ' + crop.offsetTop + ' - ' + img.offsetTop);
				y = 0;
			}

			w = Math.round(w * CROP_VARIABLE_MULTIPLIER);
			h = Math.round(h * CROP_VARIABLE_MULTIPLIER);
			x = Math.round(x * CROP_VARIABLE_MULTIPLIER);
			y = Math.round(y * CROP_VARIABLE_MULTIPLIER);

			crop.PF_OVERLAY.style.visibility = 'hidden';

			do_inline('POST', '/storeupimgcrop.act', 'ACT', function(req) {
				if (req.status !== 200) {
					crop.PF_OVERLAY.style.visibility = 'visible';
					showerrors(req,ERR_TO_ALERT);
					return;
				}
				let newdiv = document.createElement('div');
				newdiv.innerHTML = req.responseText;
				newdiv = newdiv.firstChild;

				const uimg = Pf.getParent(crop.PF_ORIGINAL,'uimg');
				const containers = uimg.getElementsByClassName('uimg-generated');

				// remove other 'chosen' image and remove old manualcrop

				const re = RegExp('_manualcrop_[^/]*?_' + linkdata.upimgid + '/');
				let rm = null;
				for (const cont of containers) {
					if (-1 !== cont.className.indexOf('uimg-generated-chosen')) {
						remclass(cont, 'uimg-generated-chosen');
					}
					const imgs = cont.getElementsByClassName('the-uimg');
					const img = imgs[0];
					if (re.test(img.src)) {
						rm = cont;
					}
				}
				if (rm) {
					Pf.removeNode(rm);
				}
				let first = containers[0];
				let p = first.parentNode;
				p.insertBefore(newdiv, first);
				Pf.closeCrop(crop);
			},
			linkdata.post + `&W=${w}&H=${h}&X=${x}&Y=${y}`);
		};
		crop.Pf_resizer = function() {
			if (crop.WANT_ASPECT === 1) {
				// square:
				if (crop.offsetWidth !== crop.offsetHeight) {
					const max_side = Math.max(crop.offsetWidth, crop.offsetHeight) + 'px';
					crop.style.width  = max_side;
					crop.style.height = max_side;
				}
			}
			crop.Pf_setMaxes();
		};
		crop.Pf_mover = function(e) {
			e.stopPropagation();
			e.preventDefault();
			if (e.button !== 0) {
				return;
			}
			let newX = crop.offsetLeft + e.movementX;
			let newY = crop.offsetTop  + e.movementY;
			// crop.style.cursor = Browser.webkit ? '-webkit-grabbing' : 'grabbing';
			crop.style.cursor = 'grabbing';

			if (-1 === e.target.className.indexOf('uimg-crop')
			&&	-1 === e.target.className.indexOf('the-uimg')
			) {
				// outside image
				if (e.offsetX < img.offsetLeft) {
					newX = img.offsetLeft;
				}
				if (e.offsetY < img.offsetTop) {
					newY = img.offsetTop;
				}
			}

			if (newX < img.offsetLeft) {
				newX = img.offsetLeft + 'px';
			} else if (newX + crop.offsetWidth > img.offsetLeft + img.offsetWidth) {
				newX = img.offsetLeft + img.offsetWidth - crop.offsetWidth;
			}
			if (newY < img.offsetTop) {
				newY = img.offsetTop + 'px';
			} else if (newY + crop.offsetHeight > img.offsetTop + img.offsetHeight) {
				newY = img.offsetTop + img.offsetHeight - crop.offsetHeight;
			}
			crop.style.left = newX + 'px';
			crop.style.top  = newY + 'px';
		};
		crop.Pf_mouseUp = function(e) {
			e.stopPropagation();
			window.removeEventListener('mousemove', crop.Pf_mover,   true);
			window.removeEventListener('mousemove', crop.Pf_resizer, true);
			crop.style.cursor = 'inherit';
		};
		crop.Pf_mouseDown = function(e) {
			e.stopPropagation();
			if (
	//			e.offsetX >= crop.scrollWidth
	//		&&	e.offsetY >= crop.scrollHeight
				e.offsetX >= crop.clientWidth  - 20
			&&	e.offsetY >= crop.clientHeight - 20
			) {
				window.addEventListener('mousemove', crop.Pf_resizer, true);
				return;
			}
			window.addEventListener('mousemove', crop.Pf_mover, true);
		};

		crop.Pf_setMaxes = function() {
			const min_side = Math.min(
				img.offsetTop + img.offsetHeight - crop.offsetTop,
				img.offsetLeft + img.offsetWidth  - crop.offsetLeft
			) + 'px';
			crop.style.maxWidth  = min_side;
			crop.style.maxHeight = min_side;
		};

		o.addEventListener('mousedown',function(e){
			if (e.target.className.match(/uimg/)) {
				return;
			}
			Pf.closeCrop(crop)
		});

		crop.addEventListener('mousedown', crop.Pf_mouseDown,		true);
		crop.addEventListener('mouseup',   crop.Pf_resizer,		true);
		crop.addEventListener('dblclick',  crop.Pf_storeSquare,	true);

		window.addEventListener('keydown', crop.Pf_cropCloser);
		window.addEventListener('mouseup', crop.Pf_mouseUp);

		crop.WANT_ASPECT = 1;

		do_inline('POST', '/getcrop.act', 'ACT', function(req) {
			if (!req.ok) {
				showerrors(req,ERR_TO_ALERT);
				return;
			}
			if (crop.WANT_ASPECT !== 1) {
				let img_aspect = img.offsetWidth / img.offsetHeight;
				let use_width;
				let use_height;
				if (crop.WANT_ASPECT > img_aspect) {
					use_width  = img.offsetWidth;
					use_height = img.offsetWidth / crop.WANT_ASPECT;
				} else {
					use_height = img.offsetHeight;
					use_width  = img.offsetHeight * crop.WANT_ASPECT;
				}
				crop.style.width  = use_width  + 'px';
				crop.style.height = use_height + 'px';
			} else if (
				!req.responseText
			||	!crop.Pf_setFromStored(JSON.parse(req.responseText))
			) {
				const min_side = Math.min(img.offsetWidth, img.offsetHeight) + 'px';
				crop.style.width  = min_side;
				crop.style.height = min_side;
				crop.Pf_setMaxes();
			}
			crop.style.visibility = 'visible';
		},
		Pf.getLinkData(self).post + `&UPIMGID=${upimgid}`);
	};
	img.src = orig_img.src.replace(/lineup\/(\d+)_\d+x\d+_(\d+)/, 'lineup/$1_original_$2');
};

Pf.closeCrop = function(crop) {
	Pf.removeNode(crop.PF_OVERLAY);
	window.removeEventListener('mousemove', crop.Pf_mover,	 true);
	window.removeEventListener('mousemove', crop.Pf_resizer, true);
	window.removeEventListener('keydown',	crop.Pf_cropCloser);
	window.removeEventListener('mouseup',	crop.Pf_mouseUp);
};

Pf.clickNoPerson = function(no_person,upimgid) {
	let set_no_person = !/\bisset\b/.test(no_person.className);

	fetch('/noperson.act',{
		method:	'post',
		headers:{'Content-Type': 'application/x-www-form-urlencoded; charset=utf-8'},
		body:	'UPIMGID=' + upimgid + '&NO_PERSON=' + (set_no_person ? '1' : '0')
	})
	.then(function(response){
		if (!response.ok) {
			console.warn('failed to set no_person', response);
			return;
		}
		setclass(no_person, set_no_person, 'isset');
	})
	.catch(function(error) {
		console.warn('error:',error);
	});
};
