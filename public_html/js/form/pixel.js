Pf.changeID = function(input, field_name) {
	let f = input.form;
	let tbody = Pf.getParentNode(input, 'TBODY');
	if (!tbody) {
		return;
	}

	let total_empty = 0;
	let other_empty = null;
	
	for (let i = 0; i < f.elements.length; i++) {
		let elem = f.elements[i];
		if (elem.name === field_name) {
			if (elem.value.is_empty()) {
				++total_empty;
				if (elem != input) {
					other_empty = elem;
				}
			}
		}
	}
	if (total_empty <= 0) {
		let cloned_first_input = tbody.firstChild.cloneNode(true);
		if (cloned_first_input) {
			let new_input = cloned_first_input.firstChild.nextSibling.firstChild;
			new_input.value = '';
			new_input.onchange(new_input);
			tbody.appendChild(cloned_first_input);
		}
	} else if (total_empty > 1) {
		changerow(other_empty, 'remove');
	}
}
