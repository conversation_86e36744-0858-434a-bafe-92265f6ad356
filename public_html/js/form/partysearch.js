function changeFilterCountry(sobj) {
	var other = getobj('countryid');
	if (!other) {
		return;
	}
	other.value = sobj.value;
	changeCountry(sobj, 0);
}

function changeMinPrice(minPrice) {
 	var maxPrice = minPrice.form.MAXPRICE;
	for (var i = 0; i < minPrice.options.length; ++i) {
		var opt = maxPrice.options[i];
		opt.disabled = parseInt(opt.value) < parseInt(minPrice.value);
	}
	if (minPrice.selectedIndex > maxPrice.selectedIndex) {
		maxPrice.selectedIndex = minPrice.selectedIndex;
	}
}

Pf.changeMinAge = function(self) {
	self.form.MINMAX.selectedIndex = self.value < 18 ? 0 : 1;
}

Pf.clickWhen = function(self) {
	var show_date = self.value == 'date' && self.checked;
	var show_year = self.value == 'year' && self.checked;
	setdisplay('whendate',show_date);
	Pf.enable([
		self.form.START_YEAR,
		self.form.START_MONTH,
		self.form.START_DAY,
		self.form.STOP_YEAR,
		self.form.STOP_MONTH,
		self.form.STOP_DAY
	],	show_date);
	setdisplay('whenyear',show_year);
	Pf.enable(self.form.YEAR,show_year);
}

Pf.submitPartySearch = function(form) {
	if (form.NAME.value.is_empty()) {
		form.NAME.disabled = true;
	}
	form.GENRES.disabled = true;
	
	let rc = submitForm(form);
	
	setTimeout(function() {
		form.NAME.disabled = false;
		form.GENRES.disabled = false;
	}, 1000);

	return rc;
}

addreadyevent(function(){
	let oobj = getobj('countryid');
	if (!oobj) {
		return;
	}
	oobj.onchange = function() {
		changeCountry(this, false, true);
		let sobj = getobj('countrypart');
		if (sobj) {
			sobj.value = this.value;
		}	
	}
});
