/*Pf.markVerifyAspiring = function(self, element, id) {
	fetch('/mark_verify_aspiring.act',{
		method:	'post',
		headers:{'Content-Type': 'application/x-www-form-urlencoded; charset=utf-8'},
		body:	'ELEMENT=' + element + '&ID=' + id + '&MARK=' + (self.checked ? '1' : '0')
	})
	.then(function(response){
		if (response.status !== 200) {
			self.checked = !self.checked;
			console.warn('failed with status ' + response.status)

		}
	})
	.catch(function(error) {
		console.warn('error:', error);
	});
};*/
