/** @param {HTMLSelectElement} banner_select */
function Pf_changeBanner(banner_select) {
	const allowed_pos=
			banner_select.
			options[banner_select.selectedIndex].
			getAttribute('data-allowed-adpos').
			split(/,/);
	const single_option = allowed_pos.length === 1;
	const form = banner_select.form;
	const adpos = form['POSITION'];
	for (const adposopt of adpos) {
		if (!allowed_pos) {
			adposopt.disabled = false;
			continue;
		}
		if (adposopt.disabled = (-1 === allowed_pos.indexOf(adposopt.value))) {
			if (adposopt.selected) {
				adposopt.selected = false;
			}
		} else if (single_option) {
			adposopt.selected = true;
		}
	}
}
/** @param {HTMLInputElement} url_input */
function Pf_changeURL(url_input) {
	const form = url_input.form;
	const add_utm_cb = form['URL_ADD_DEFAULT_UTM'];
	// we already have utm values in url_input:
	const no_add_utm = url_input.value.match(/utm_/);
	add_utm_cb.checked = !no_add_utm;
}

/** @param {HTMLFormElement} form
 *  @param {integer} ad_position_overlay
 *  @param {integer} ad_position_party_ad
 *  */
function submitAdForm(form, ad_position_overlay, ad_position_party_ad ) { try {
	const ad_position = parseInt(form['POSITION'].value);
	if (ad_position !== ad_position_overlay
	&&	ad_position !== ad_position_party_ad
	) {
		if (form.HEIGHT
		&&	!parseInt(form.HEIGHT.value)
		) {
			const o = getobj('actualbanner',true);
			if (!o) {
				return false;
			}
			if (o.firstChild) {
				o.style.lineHeight  = '1.2em';
				o.style.fontSize 	= '1.2em';
				if (!o.clientHeight) {
					alert('Could not determine height of banner!\nTry creating ad from banner page.');
					return false;
				}
				form.HEIGHT.value = o.clientHeight;
			}
		}
	}
	if (ad_position === ad_position_overlay) {
		if (form.FREQCAP.value.is_empty()
		&&	form.FREQCAP_DAY.value.is_empty()
		) {
			markRefill(form.FREQCAP);
			return false;
		}
	}
	return submitForm(form);
} catch(e) {
	catchLog(e);
	return false;
}}

/** @param {HTMLInputElement} self */
function Pf_changeExplicitPrice(self) {
	setdisplay(['commissionrow', 'pricerow'], self.checked);
	setattr(self.form.PRICE, 'required', self.checked);
}
/** @param {HTMLSelectElement} self */
function Pf_changeBannerBox(self) {
	do_inline('POST', '/include/banner.inc', 'ACT', function(req) {
		const o = getobj('bannerboxtop',true);
		if (!o) {
			return;
		}
		o.innerHTML = req.responseText;
	}, 'BANNERID=' + self.value);
}

/** @param {HTMLSelectElement} self */
function Pf_changeAdPosition(self) {
	const form = self.form;
	const partyad = parseInt(self.value) === 9;
	changerow(form.BANNERID, partyad ? 'hide' : 'show');
	changerow(form.PARTYID,  partyad ? 'show' : 'hide');

	setattr([form.URL, form.BANNERID],	'required', !partyad);
	setattr(form.PARTYID,				'required',  partyad);

	setdisplay(['nopartypart', 'hardstop'], !partyad);

	if (parseInt(self.value) === 8) {
		const intr = getobj('intrusive');
		if (intr) {
			intr.checked = true;
			if (intr.onclick) {
				intr.onclick();
			}
		}
		if (form.DELAY.value.is_empty()) {
			form.DELAY.value = 5;
		}
	}
}
