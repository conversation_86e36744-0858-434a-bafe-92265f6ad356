/**
 * @param {HTMLSpanElement} self
 * @param {integer} priceid
 * @returns {void}
 */
function Pf_changePriceSoldOut(self, priceid) {
	const sold_out = self.hasAttribute('data-sold-out');
	const new_sold_out = !sold_out;
	do_inline('POST','/setpricesoldout.act','ACT',function(req){
		if (!req.ok) {
			showerrors(req,ERR_TO_ALERT);
			return;
		}
		setdisplay(self.lastChild,					new_sold_out);
		setdisplay(self.lastChild.previousSibling,	sold_out);
		setattr(self, 'data-sold-out', new_sold_out);

	}, `PRICEID=${priceid}&SOLD_OUT=${new_sold_out ? '1' : '0'}`
	);
}
