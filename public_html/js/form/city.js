function changeCoords(self) {
	var form = self.form;
	var tz = form['TIMEZONE'];
	if (tz.options.length <= 1) {
		return;
	}
	var lato = form['LATITUDE'];
	var lono = form['LONGITUDE'];
	if (lato.getAttribute('data-tz') == lato.value
	&&	lono.getAttribute('data-tz') == lono.value
	) {
		return;
	}
	setattr(lato,'data-tz',lato.value);
	setattr(lono,'data-tz',lono.value);
	var preq = initrequest(true);
	if (!preq) {
		return;
	}
	changerow(tz,'addclass','light');
	tz.selectedIndex = 0;
	tz.readonly = true;
	preq.open('GET','/timezone.obj?LATITUDE=' + lato.value + ';LONGITUDE=' + lono.value + (form.COUNTRYID ? ';COUNTRYID=' + form.COUNTRYID.value : ''),true);
	preq.onreadystatechange = function() {
		if (preq.readyState !== 4) {
			return;
		}
		changerow(tz,'remclass','light');
		tz.readonly = false;
		if (preq.status === 200
		&&	preq.responseText
		) {
			tz.value = preq.responseText;
			if (!tz.selectedIndex) {
				var opt = document.createElement('option');
				opt.value = opt.innerHTML = preq.responseText;
				opt.selected = true;
				tz.appendChild(opt);
			}
		} else {
			showerrors(preq,ERR_TO_ALERT);
		}		
	};
	preq.send(null);
}

function changeCountry(self) {
	if (!self.value) {
		hide('regionrow');
		return;
	}
	var preq = initrequest(true);
	if (!preq) {
		return;
	}
	preq.open('GET','/provinces.obj?COUNTRYID=' + self.value,false);
	preq.send(null);
	getobj('provinces').innerHTML = preq.responseText;
	preq.open('GET','/timezones.obj?COUNTRYID=' + self.value + '&SELECTED=' + urlencode_utf8_to_windows1252(self.form.TIMEZONE.value)[0],false);
	preq.send(null);
	getobj('timezones').innerHTML = preq.responseText;
	
	var visi = false;
	for (var child = self.form['REGIONID'].firstChild; child; child = child.nextSibling) {
		if (child.nodeName != 'OPTGROUP') {
			continue;
		}
		if (child.getAttribute('data-forcountry') == self.value) {
			visi = true;
			break;
		}
	}
	setdisplay('regionrow',visi);
	if (!visi) {
		self.form['REGIONID'].selectedIndex = 0;
	}
}
function lookupCountry(select) {
	if (select.selectedIndex != 0) {
		return;
	}
	geocoder.geocode({
		address:	select.options[select.selectedIndex].text
	},function(results,status) {
		if (!results
		||	!results.length
		) {
			alert('Geen resultaten.');
			return;
		}
		map.panTo(results[0].geometry.location);
	});
}
