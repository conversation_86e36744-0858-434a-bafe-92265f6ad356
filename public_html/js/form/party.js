/**
 *	@param {HTMLSelectElement} self
 *	@param {integer} current_year
 *	@param {integer} current_month
 */
function Pf_changePartyDate(self, current_year, current_month) {
	let maybe;
	const form = self.form;
	const selected_year = parseInt(form.YEAR.value);
	if (current_year === selected_year) {
		const selected_month = parseInt(form.MONTH.value);
		const now = current_year * 100 + current_month;
		const then = selected_year * 100 + selected_month;
		const diff = then + 12 - now;
		maybe = diff >= 0 && diff <= 3;
	} else {
		maybe = false;
	}
	setdisplay('maybe-next-year', maybe);
}
/**
* @param {PointerEvent} event
* @param {HTMLInputElement} radio
* @returns {void}
*/
function Pf_pointerdownCategory(event, radio) {
	if (radio.checked) {
		event.stopPropagation();
		radio.checked   = false;
		radio.stopClick = true;
		Pf_clickBox(radio);
	}
}
/**
 * @param {PointerEvent} event
 * @param {HTMLInputElement} radio
 * @returns {void}
 */
function Pf_clickCategory(event, radio) {
	if (radio.stopClick) {
		Pf.stopEvent(event);
		radio.stopClick = false;
		// event seems to bubble up anyway
		// use this STOPPED var to not hilite again in upLite
		radio.STOPPED = true;
	}
}
/**
 * @param {HTMLSelectElement} select
 * @returns {void}
 */
function Pf_clickPartyType(select) {
	const livestream_only		= select.selectedIndex === 1;
	const  any_livestream 		= select.selectedIndex >=  1;
	const form = select.form;

	if (livestream_only) {
		if (form['UNKNOWN_LOCATION'].checked) {
			form['UNKNOWN_LOCATION'].checked = false;
			form['UNKNOWN_LOCATION'].dispatchEvent(new Event('click'));
		}
		form['LOCATION_ADDR'].required = false;
		if (form['LOCATION_ADDR'].value !== '') {
			form['LOCATION_ADDR'].value = '';
			form['LOCATION_ADDR'].dispatchEvent(new Event('change'));
		}
	}
	setdisplay('locstuff', !livestream_only);
	form['FREE_ENTRANCE'].checked = livestream_only;
	form['FREE_ENTRANCE'].dispatchEvent(new Event('click'));
	form['MIN_AGE'].value = livestream_only ? 0 : 18;

	setdisplay('livestream_address',any_livestream);
}

function Pf_changePartyLocType(self) {
	let form = self.form;
	Pf.locType.init(form);

	setattr('city', 'required', form.UNKNOWN_LOCATION.checked);

	let is_boat = Pf.locType.boat.checked;
	setdisplay(['boardingrow','departurerow','arrivalrow'], is_boat);
	if (!is_boat) {
		form.BOARDING_ADDR.value = '';
		form.BOARDING_ADDR.dispatchEvent(new Event('keyup'));
	}
	if ( Pf.locType.beach.	checked
	&&	!Pf.locType.outdoor.checked
	) {
		Pf.locType.outdoor.checked = true;
		Pf.locType.outdoor.dispatchEvent(new Event('click'));
	}
}

function Pf_changeCurrencyIfNoPrice(form, currency) {
	if (Pf.priceinfo.bodies < 1) {
		return;
	}
	for (let i = 0; i < Pf.priceinfo.bodies; ++i) {
		const price = form[`PRICE_AMOUNT[${i}]`];
		if (!price) {
			console.warn(`could not find PRICE_AMOUNT[${i}]`);
		}
		if (price.value.is_empty()) {
			// change currency only if no price has been entered already
			form[`PRICE_CURRENCY[${i}]`].value = currency;
		}
	}
}

function loc_or_brd_click(self) {
	const currency = self.getAttribute('data-currency');
	Pf_changeCurrencyIfNoPrice(self.form, currency || 'EUR');

	// remove 'requirement' from LOCATION_ADDR since we have LOCATIONID
	const addr = self.form['LOCATION_ADDR'];
	addr.required = false;
	remclass(addr,'forcerequire');

	const possible_location_typestr = self.getAttribute('data-locationtype');
	if (possible_location_typestr) {
		const possible_location_types = possible_location_typestr.split(',');
		Pf.locType.init(self.form);
		for (const location_type of Pf.locType.types) {
			const locationtype_checkbox = Pf.locType[location_type];
			const mark_green = possible_location_types.includes(location_type);
			setclass(Pf.locType[location_type].parentNode, mark_green, 'notice');
			const make_checked =
				possible_location_types
			&&	possible_location_types.length === 1
			&&	possible_location_types[0] === location_type;
			if (locationtype_checkbox.checked !== make_checked) {
				locationtype_checkbox.checked = make_checked;
				Pf_updateBox(locationtype_checkbox);
			}
		}
	}
	Pf_changePartyLocType(self);

	const multiorgallow = self.getAttribute('data-allow-locorg');
	if (!multiorgallow) {
		self.form.LOCORG.checked = false;
	}

	const locorgstore = getobj('locorgstore',true);
	if (!locorgstore) {
		return;
	}
	locorgstore.innerHTML = self.nextSibling.innerHTML;

	setdisplay('locorgrow', multiorgallow);
}

const boardingLookup = newLookup(true);
const locationLookup = newLookup(false);

/**
 * @param {boolean} only_boarding
 * @returns {{
 * 		boarding:	boolean,
 * 		name:		string|null,
 * 		optout:		HTMLElement|null,
 * 		timerid:	number,
 * 		searchFor:	function(HTMLInputElement, boolean=, boolean=): void,
 * 		searchNow:	function(HTMLInputElement, string, boolean=, boolean=): void
 * }}
 */
function newLookup(only_boarding) { return {
	boarding:	only_boarding,	// for boarding locations only
	name:		null,			// current name shown
	optout:		null,			// output options here
	timerid:	0,				// pending lookup

	/**
	 * @param {HTMLInputElement} input
	 * @param {boolean} [immediate]
	 * @param {boolean} [no_default]
	 * @returns {void}
	 */
	searchFor: function(input, immediate, no_default) {
		const name = input.value;
		this.optout = getobj(this.boarding ? 'boardingoptions' : 'locationoptions',true);
		if (!immediate
		&&	name === this.name
		) {
			return;
		}
		Pf.locType.init(input.form);
		if (name.is_empty()) {
			this.name = '';
			if (this.timerid) {
				clearTimeout(this.timerid);
				this.timerid = 0;
			}
			this.optout.innerHTML = '';
			if (!this.boarding) {
				Pf.locType.clear();
			}
			return;
		}
		if (!immediate
		&&	/^\s*\d+!\s*$/.test(name)
		) {
			immediate = true;
		}
		if (!immediate
		&&	this.name
		&&	(	RegExp('^' + RegExp.escape(this.name) + '\\s*$').test(name)
			||	RegExp('^' + RegExp.escape(name)	  + '\\s*$').test(this.name)
			)
		) {
			this.name = name;
			return;
		}
		if (this.timerid) {
			clearTimeout(this.timerid);
		}
		let lookup = this;
		this.name = name;
		if (immediate) {
			this.timerid = 0;
			lookup.searchNow(input, name, true, no_default);
		} else {
			this.timerid = setTimeout(
				() => lookup.searchNow(input, name, false, no_default),
				500
			);
		}
	},
	/**
	 * @param {HTMLInputElement} input
	 * @param {string} name
	 * @param {boolean} [immediate]
	 * @param {boolean} [no_default]
	 * @returns {void}
	 */
	searchNow: function(input, name, immediate, no_default) {
		this.timerid = 0;
		const form = input.form;
		const lookup = this;
		const preq = initrequest();
		preq.open('POST', '/find/' + (lookup.boarding ? 'boarding' : 'location'), !immediate);
		preq.setRequestHeader('Content-Type','application/x-www-form-urlencoded');
		preq.onreadystatechange = function() {
			if ( preq.readyState !== 4
			||	!preq.responseText
			) {
				return;
			}
			changerow(input, 'remclass', 'light');
			unhide(lookup.optout);
			lookup.optout.innerHTML = preq.responseText;
			if (Pf_initUpLites) {
				Pf_initUpLites(lookup.optout);
			}
			let radios = lookup.boarding ? form.BOARDINGID : form.LOCATIONID;
			if (!radios) {
				return;
			}
			if (typeof radios.length === 'undefined') {
				radios = [radios];
			}
			if (radios?.length) {
				for (const radio of radios) {
					if (radio.checked) {
						radio.onchange(radio);
						break;
					}
				}
			}
		};
		changerow(input, 'addclass', 'light');
		preq.send('NAME=' + Pf.encodeForPost(name) + (lookup.boarding ? '' : '&MULTIORGCHECK=1') + (no_default ? '&NODEF=1' : ''));
	}
}}
let curOrg = '';
let orglookid = 0;
let orglookwaiting = null;
let noOrgdef = false;
function searchForOrganization(org, e, no_def) {
	let form = org.form;
	noOrgdef = no_def;
	if (curOrg === org.value) {
		return;
	}
	if (org.value === '') {
		curOrg = '';
		if (orglookid) {
			clearTimeout(orglookid);
		}
		let optout = getobj('organizationoptions',true);
		if (!optout) {
			return;
		}
		optout.innerHTML = '';
		return;
	}
	if (curOrg) {
		if (RegExp('^' + RegExp.escape(curOrg)	  + '[\\s,]*$').test(org.value)
		||	RegExp('^' + RegExp.escape(org.value) + '[\\s,]*$').test(curOrg)
		) {
			return;
		}
	}
	curOrg = org.value;

	if (orglookid) {
		clearTimeout(orglookid);
	}
	let actual_lookup = function(immediate) {
		const preq = initrequest();
		if (!preq) {
			return;
		}
		let options_out = getobj('organizationoptions',true);
		if (!options_out) {
			return;
		}
		preq.open('POST','/find/organizations',!immediate);
		preq.setRequestHeader('Content-Type','application/x-www-form-urlencoded');
		preq.onreadystatechange = function() {
			if (preq.readyState !== 4
			||	!preq.responseText
			) {
				return;
			}
			changerow(form.ORGANIZATION_NAME, 'remclass', 'light');
			options_out.innerHTML = preq.responseText;
			if (Pf_initUpLites) {
				Pf_initUpLites(options_out);
			}
			setdisplay(options_out, Boolean(options_out.innerHTML));
			const gid_str = preq.getResponseHeader('X-GIDS');
			if (gid_str) {
				Pf_fillActualGenres(form, gid_str.split(','));
			}
		};
		let post_str = 'NAME=' + Pf.encodeForPost(curOrg);
		if (noOrgdef) {
			post_str += '&NODEF=1';
		}
		let orgids = form['ORGANIZATIONID[]'];
		if (orgids) {
			if (!orgids.length) {
				orgids = [orgids];
			}
			for (/** @type {HTMLInputElement} */ const orgid of orgids) {
				let value;
				if (!orgid.checked) {
					value = '0';
				} else if (/\bupdated\b/.test(orgid.parentElement.className)) {
					value = '2';
				} else {
					value = '1';
				}
				post_str += `&ORGANIZATIONID[${orgid.value}]=${value}`
			}
		}
		changerow(form.ORGANIZATIO_NAME, 'addclass', 'light');
		preq.send(post_str);
	};
	orglookwaiting = actual_lookup;
	orglookid = setTimeout(actual_lookup,500);
}

function checkSecretLoc(self) {
	setdisplay(self.parentNode.previousSibling, !self.checked);
	setdisplay('cityidrow', self.checked);
	if (self.checked) {
		let opts = getobj('locationoptions');
		if (opts) {
			opts.innerHTML = '';
		}
	}
	Pf.locType.init(self.form);
	// Don't require anything, let user add an event with no info
	// about location whatsoever
	setattr(self.form.LOCATION_ADDR, 'required', !self.checked);
}

/** @param {HTMLFormElement} form
 *  @param {int} old_partyid
 *  @returns {boolean}
 **/
function submitPartyForm(form, old_partyid) { try {
	if (!form) {
		return false;
	}
	// Hardcoded exceptions. Could also do a round-trip to the server and ask whether the NAME is exactly like
	// an element, like artist, location org organization. But that takes more time.
	if ( form.NAME.value !== 'Ondergronds / Bovengronds'
	&&	!form.NAME.value.includes('D|K|OXY')
//	&&	 form.NAME.value.match(/(\s+(?:l|i(?! (?:am|love))|ft\.?)\s+|[\|]+|\/..(?<!50\/50)|\b(?:SOLD\s*OUT\b|uitverkocht))/i)
//											^^^ does not work in Firefox
	&&	 form.NAME.value.match(/(\s+(?:l|i(?! (?:am|love|live|dream|hate))|ft\.?)\s+|\|+|\b(?:SOLD\s*OUT\b|uitverkocht))/i)
	) {
		markRefill(form.NAME,form.getAttribute('data-unwanted-chars').replace_keywords({CHARS: RegExp.$1}));
		return false;
	}
	if (form.DAY.value === '') {
		markRefill(form.DAY,form.getAttribute('data-invalid-date'));
		return false;
	}
	if (form.MONTH.value === '') {
		markRefill(form.MONTH,form.getAttribute('data-invalid-date'));
		return false;
	}
	if (form.YEAR.value === '') {
		markRefill(form.YEAR,form.getAttribute('data-invalid-date'));
		return false;
	}
	if (orglookid) {
		clearTimeout(orglookid);
		orglookwaiting(true);
	}
	if (form.LIVESTREAM.selectedIndex > 0) {
		if (form.LIVESTREAM_ADDRESS.value.is_empty()) {
			if (!confirm(form.getAttribute('data-no-livestream-address'))) {
				markRefill(form.LIVESTREAM_ADDRESS);
				return false;
			}
		}
		if (form.LIVESTREAM.value === 'only'
		&&	parseInt(form.MIN_AGE.value) > 0
		) {
			if (!confirm(form.getAttribute('data-livestream-min-age').replace_keywords({MIN_AGE: form.MIN_AGE.value}))) {
				markRefill(form.MIN_AGE);
				return false;
			}
		}
		if (form.FREE_ENTRANCE.checked) {
			if (Pf_countPrices(form) > 1) {
				if (!confirm(form.getAttribute('data-prices-and-free'))) {
					markRefill(form.FREE_ENTRANCE);
					return false;
				}
			}
		}
	} else {
		if (boardingLookup.timerid) {
			boardingLookup.searchFor(form.BOARDING_ADDR, true);
		}
		if (locationLookup.timerid) {
			locationLookup.searchFor(form.LOCATION_ADDR, true);
		}
		let locid = Pf_radioValue(form.LOCATIONID);
		if (!locid
		&&	(	!form.UNKNOWN_LOCATION
			||	!form.UNKNOWN_LOCATION.checked
			)
		&&	form.LOCATION_ADDR.value === ''
		) {
			markRefill(form.LOCATION_ADDR,form.getAttribute('data-missing-location'));
			return false;
		}
		Pf.locType.init();
		if (typeof Pf.locType === 'undefined'
		||	!Pf.locType.cnt()
		) {
			markRefill(getobj('locstuff'),form.getAttribute('data-missing-locstuff'));
			return false;
		}
		if (Pf.locType.boat.checked
		&&	(	!form.BOARDINGID
			||	 form.BOARDINGID.value.is_empty()
			)
		&&	form.BOARDING_ADDR.value.is_empty()
		) {
			markRefill(form.BOARDING_ADDR,'Je moet een opstapplaats opgeven!');
			return false;
		}
	}
	if (form['PRESALE_DATE_C'].checked) {
		let start	= new Date(
			form['YEAR'].value,
			form['MONTH'].value,
			form['DAY'].value,
			form['HOUR'].value,
			form['MINS'].value
		);
		let presale = new Date(
			form['PRESALE_YEAR'].value,
			form['PRESALE_MONTH'].value,
			form['PRESALE_DAY'].value,
			form['PRESALE_HOUR'].value,
			form['PRESALE_MINS'].value
		);
		if (presale > start) {
			markRefill(form['PRESALE_YEAR'].parentNode, 'Presale moet voor start event beginnen');
			return false;
		}
	}
	if (!Pf_overlapAlright(form, old_partyid)) {
		return false;
	}

	form.IS_SENT.value = Pf.uuidv4();

	return submitForm(form);
} catch (e) {
	catchLog(e);
	return false;
}}
/**
 * @param {HTMLFormElement} form
 * @param {int} partyid
 * @returns {boolean}
 */
function Pf_overlapAlright(form, partyid) {
	let location_id = 0;
	if (form.UNKNOWN_LOCATION?.checked) {
		let locations = form.LOCATIONID;
		if (locations) {
			if (!locations.length) {
				location_id = form.LOCATIONID.value;
			} else {
				for (const location of locations) {
					if (location.checked) {
						location_id = parseInt(location.value);
						break;
					}
				}
			}
		}
	}
	if (!location_id
	&&	(	!form.LOCATION_ADDR
		||	 form.LOCATION_ADDR.value.is_empty()
		)
	) {
		return true;
	}
	let result = true;
	do_inline('POST', '/overlaps.obj', 'ACTNOW', function(req, self, obj) {
		if (req.status !== 200 ){
			showerrors(req,ERR_TO_HTML);
			return;
		}
		if (obj) {
			showerrors(obj[0], ERR_TO_HTML);
			let div = document.createElement('div');
			div.innerHTML = obj[1];
			result = confirm(div.textContent);
		}
	},
		build_poststr(form,['LOCATIONID','LOCATION_ADDR','DAY','MONTH','YEAR','HOUR','MINS','DURATION_HOURS','DURATION_MINS'])
	+	'&PARTYID=' + partyid
//	+	(form.LOCATIONID ? '' : '&LOCATIONID=0')
//	+	(form.LOCATION_ADDR ? '' : '&LOCATION_ADDR=')
	);
	return result;
}

function Pf_removePrice(self) {
	let pricepart;
	if (self.nodeName === 'TBODY') {
		/** @type {HTMLImageElement} */
		pricepart = self;
	} else {
		for (pricepart = self.parentNode; pricepart; pricepart = pricepart.parentNode) {
			/** @type {HTMLTableSectionElement} */
			if (pricepart.nodeName === 'TBODY') {
				break;
			}
		}
	}
	if (pricepart) {
		Pf.disappear(pricepart,0.2,true);
	}
	// noinspection IncrementDecrementResultUsedJS
	if (--Pf.priceinfo.actives === 1) {
		const imgs = pricepart.nextSibling.getElementsByTagName('IMG');
		for (const img of imgs) {
			if (img.getAttribute('data-remove')) {
				setdisplay(img, false);
				break;
			}
		}
	}
}

/**
 * @param {HTMLFormElement} f
 * @returns {void}
 */
function Pf_dedupPrices(f) {
	let have = [];
	let curr = [];
	let remove = [];
	let findPrice = function(price) {
		for (const element of have) {
			if (!element['PRICE_AMOUNT'].length) {
				continue;
			}
			if (JSON.stringify(element) === JSON.stringify(price)) {
				return true;
			}
		}
		return false;
	};
	let prev_ndx = null;
	for (const element of f.elements) {
		const elem = element;
		if (!elem.name.match(/^(PRICE_[^[]*)\[(\d+)]$/)) {
			continue;
		}
		const name = RegExp.$1;
		const ndx = RegExp.$2;

		if (typeof curr[ndx] === 'undefined') {
			curr[ndx] = {};
			if (prev_ndx) {
				if (have.length
				&&	findPrice(curr[prev_ndx])
				) {
					// remove this ndx;
					remove.push(prev_ndx);
				} else {
					have.push(curr[prev_ndx]);
				}
			}
			prev_ndx = ndx;
		}
		switch (elem.type) {
		case 'checkbox':
			curr[ndx][name] = elem.checked;
			break;
		case 'radio':
			curr[ndx][name] = Pf_radioValue(elem);
			break;
		default:
			curr[ndx][name] = elem.value;
			break;
		}
	}
	for (const ndx of remove) {
		const pp = getobj('pricepart' + ndx);
		if (!pp) {
			continue;
		}
		Pf.removeNode(pp);
	}
}

/**
 * @param {HTMLFormElement} f
 * @returns {int}
 */
function Pf_countPrices(f) {
	let prices = 0;
	for (const element of f.elements) {
		if (element.name.startsWith('PRICE_AMOUNT')) {
			++prices;
		}
	}
	return prices;
}

function Pf_copyPrice(event, self, clean, inputhandler) {
	let pricepart;
	if (clean) {
		for (pricepart = self; pricepart && pricepart.nodeName !== 'TBODY';  pricepart = pricepart.parentNode) {
			// nop
		}
		if (!pricepart) {
			return null;
		}
		pricepart = pricepart.previousSibling;
	} else {
		for (pricepart = self.parentNode; pricepart; pricepart = pricepart.parentNode) {
			if (pricepart.nodeName === 'TBODY') {
				break;
			}
		}
	}
	if (!pricepart) {
		return null;
	}
	Pf.hash_arrow.detach();
	let clone = pricepart.cloneNode(true);
	Pf_initUpLites(clone,true);
	clone.firstChild.className = '';
	const dsts = clone.getElementsByTagName('SELECT');
	const srcs = pricepart.getElementsByTagName('SELECT');
	for (let i = 0; i < dsts.length; ++i) {
		dsts[i].selectedIndex = clean && !dsts[i].name.startsWith('PRICE_CURRENCY') ? 0 : srcs[i].selectedIndex;
	}
	const upds = clone.getElementsByTagName('INPUT');
	const form = Pf.getParentNode(self,'FORM');
	const presale_sold_out = form['PRESALE_SOLD_OUT'].checked;
	let is_presale = false;
	for (let i = 0; i < upds.length + dsts.length; ++i) {
		let upd = i < upds.length ? upds[i] : dsts[i - upds.length];
		upd.name = upd.name.replace(/\[\d+]/, `[${Pf.priceinfo.bodies}]`);

		if (presale_sold_out
		&&	upd.name.startsWith('PRICE_SALE_TYPE')
		) {
			is_presale = Pf_radioValue(upd) === 'presale';
		}
		if (clean) {
			switch (upd.type) {
			case 'checkbox':
				if (upd.name.startsWith('PRICE_WHAT')
				&&	upd.value === 'entrance'
				) {
					upd.checked = true;
					Pf_clickBox(upd);
				} else if (upd.name.startsWith('PRICE_INCFEE')) {
					Pf_clickBox(upd);
				} else if (upd.name.startsWith('PRICE_SOLD_OUT')) {
					if (presale_sold_out
					&&	!upd.checked
					) {
						upd.click();
					}
				} else if (upd.checked) {
					upd.click();
				}
				break;
			case 'number':
				upd.value = '';
				if (upd.name.startsWith('PRICE_AMOUNT')) {
					Pf_showActiveness(upd);
				}
				break;
			case 'text':
				upd.value = '';
				break;
			}
		}
		if (presale_sold_out) {
			if (upd.name.startsWith('PRICE_SALE_TYPE')) {
				is_presale = Pf_radioValue(upd) === 'presale';
			} else if (
				upd.name.startsWith('PRICE_SOLD_OUT')
			&&	(is_presale ? !upd.checked : upd.checked)
			) {
				upd.click();
			}
		}
		if (inputhandler) {
			inputhandler(upd);
		}
	}

	clone.id = 'pricepart' + Pf.priceinfo.bodies;

	let rc = Pf.priceinfo.bodies;

	++Pf.priceinfo.bodies;
	++Pf.priceinfo.actives;

	for (const img of clone.getElementsByTagName('IMG')) {
		setdisplay(img, true);
	}
	const imgs = pricepart.getElementsByTagName('IMG');
	for (let i = 0; i < imgs.length; ++i) {
		setdisplay(imgs[i], i || Pf.priceinfo.actives > 1);
	}
	if (clean) {
		Pf.priceinfo.addrow.parentNode.parentNode.insertBefore(clone, Pf.priceinfo.addrow.parentNode);
	} else {
		pricepart.parentNode.insertBefore(clone, pricepart.nextSibling);
	}
	Pf.changeOpacity(clone, 0);
	Pf.appear(clone, .2);
	const fieldset = clone.firstChild.firstChild.nextSibling.firstChild;
	Pf.hash_arrow.insertBefore(fieldset,true);
	fieldset.Pf_clickHandler = function(event){
		if (!event.target?.getAttribute('data-duplicate')) {
			Pf.hash_arrow.detach();
		}
		remevent(this, event.type, fieldset.Pf_clickHandler);
	};
	addevent(fieldset, 'click', fieldset.Pf_clickHandler);
	return rc;
}
function Pf_showActiveness(amount) {
	const fs = Pf.getParentNode(amount,'FIELDSET');
	if (!fs) {
		return;
	}
	const active = amount.value.length;
	for (let n = fs.firstChild.nextSibling; n; n = n.nextSibling) {
		setclass(n,!active,'light');
	}
}

function Pf_validatePrice(self) {
	// calculate simple things in price field, like 10*100 or 2400 - 799
	const calc_re = new RegExp(/^\s*(?<arg1>[\d.,]+)(?:\s*(?<operator>[+*/-])\s*(?<arg2>[\d.,]+))*\s*$/);
	const groups_calc_re = self.value.match(calc_re);
	if (!groups_calc_re) {
		return;
	}
	['arg1', 'arg2'].forEach(function(arg) {
		let arg_value = groups_calc_re.groups[arg].replace(/,/g, '.');
		if (arg_value.indexOf('.') === -1) {
			return;
		}
		arg_value *= 100;
		groups_calc_re.groups[arg] = arg_value;
	});
	const operator = groups_calc_re.groups.operator;
	const arg1 = parseFloat(groups_calc_re.groups.arg1);
	const arg2 = parseFloat(groups_calc_re.groups.arg2);
	let result;
	if (operator === '+') {
		result = arg1 + arg2;
	} else if (operator === '*') {
		result = arg1 * arg2;
	} else if (operator === '/') {
		result = arg1 / arg2;
	} else if (operator === '-') {
		result = arg1;
		const amount_groups = self.name.match(/^PRICE_AMOUNT(?<ndx>\[\d+])/);
		if (amount_groups?.groups?.ndx) {
			self.form[`PRICE_FEE${amount_groups.ndx}`].value = arg2;
		} else {
			result -= arg2;
		}
	}
	if (result) {
		self.value = result;
	}
}

function Pf_oneEmptyPrice(self) {
	Pf_showActiveness(self);

	if (self.emptyTimer) {
		clearTimeout(self.emptyTimer);
	}
	self.emptyTimer = setTimeout(function(){
		// cleanup
		let empties = [];
		for (const elem of self.form.elements) {
			if (!elem.name.startsWith('PRICE_AMOUNT')) {
				continue;
			}
			if (!elem.value.length) {
				empties.push(elem);
			}
		}
		if (self.value.length) {
			if (empties.length < 1) {
				/** @var {HTMLInputElement} */
				const addprice = getobj('addprice');
				if (!addprice) {
					return;
				}
				Pf_copyPrice(null,addprice,true);
				return;
			}
		}
		if (empties.length > 1) {
			for (const empty of empties) {
				if (self === empty) {
					continue;
				}
				const tbody = Pf.getParentNode(empty,'TBODY');
				if (!tbody) {
					continue;
				}
				Pf_removePrice(tbody);
			}
		}
	},350);
}

function Pf_clickTicketType(self) {
	if (!self.name.match(/PRICE_SALE_TYPE\[(\d+)]/)) {
		return;
	}
	let ndx = RegExp.$1;
	let ndx_name;

	let allow_incfee = self.value === 'presale';
	setdisplay(self.form[`PRICE_INCFEE[${ndx}]`].parentNode.parentNode, allow_incfee);

	let is_door_sale = self.value === 'door';
	self.form[ndx_name = `PRICE_CASHONLY[${ndx}]`].disabled = !is_door_sale;
	setclass(self.form[ndx_name].parentNode, !is_door_sale, 'light');

	if (is_door_sale) {
		if (self.form['PRESALE_SOLD_OUT'].checked
		&&	self.form[ndx_name = `PRICE_SOLD_OUT[${ndx}]`].checked
		) {
			self.form[ndx_name].dispatchEvent(new Event('click'));
		}
		if (self.form[ndx_name = `PRICE_INCFEE[${ndx}]`].checked) {
			self.form[ndx_name].dispatchEvent(new Event('click'));
		}
	}
	setdisplay(
		Pf.getParentNode(ndx_name = self.form[`PRICE_INCFEE[${ndx}]`],
		'DIV').nextSibling,
		!is_door_sale && !self.form[ndx_name].checked);
}

function Pf_clickWhat(self) {
	if (self.value === 'locker') {
		const inputs = self.form.getElementsByTagName('INPUT');
		let have_locker_price = false;
		for (const input of inputs) {
			if (input.name.startsWith('PRICE_WHAT')
			&&	input.checked
			&&	input.value === 'locker'
			) {
				have_locker_price = true;
				break;
			}
		}
		if (have_locker_price) {
			if (!self.form['LOCKERS'].checked) {
				 self.form['LOCKERS'].click();
			}
		} else {
			let  keep = self.form['LOCKERS'].getAttribute('data-keep-lockers');
			if (!keep) {
				if (self.form['LOCKERS'].checked) {
					self.form['LOCKERS'].click();
				}
			}
		}
	}
}

function Pf_clickLockers(self) {
	setattr('lockers', 'data-keep-lockers', self.checked);
}

function Pf_changePartyHour(self) {
	const prefix = self.name.match(/^(.*)HOUR$/) ? RegExp.$1 : '';
	const mins = self.form[prefix + 'MINS'];
	mins.disabled = self.value >= 24;
	if (mins.disabled) {
		mins.selectedIndex = 0;
	}
	mins.options[0].innerHTML = mins.disabled ? '' : '00';
}

function Pf_findSubmittedParty() {
	const f = getobj('agendaform');
	if (!f
	||	!f.IS_SENT
	||	f.IS_SENT.value.is_empty()
	) {
		return false;
	}

	let party = do_inline('POST', '/sentparty.obj', 'RETURN', null, 'IS_SENT=' + encodeURIComponent(f.IS_SENT.value));

	if (!party) {
		return false;
	}

	location.href =
		confirm(f.getAttribute('data-edit'))
	?	'/party/' + party.PARTYID + '/form'
	:	'/party/form';

	return true;
}

function Pf_clickOnly(self) {
	const form = self.form;
	let door_only;
	let presale_only;
	if (self.name === 'DOORONLY') {
		door_only = self.checked;
		presale_only = !door_only && form['PRESALEONLY'].checked;
	} else {
		presale_only = self.checked;
		door_only = !presale_only && form['DOORONLY'].checked;
	}

	form['PRESALEONLY'].checked = presale_only;
	form['DOORONLY'].checked = door_only;

	const force_type = door_only !== presale_only;

	for (const elem of form.elements) {
		if (!elem.name.startsWith('PRICE_SALE_TYPE')) {
			continue;
		}
		if (force_type) {
			const want = elem.value === 'presale' ? presale_only : door_only;
			if (elem.checked !== want) {
				elem.checked = want;
				Pf_clickTicketType(elem);
				Pf_clickBox(elem);
			}
		}
		elem.disabled = elem.value === 'presale' ? door_only : presale_only;
	}
}

function Pf_setFacebookPrefillCount(type, fill_count, options_count) {
	let counter_stores = {'fbfillcnt': fill_count, 'fboptscnt': options_count};
	let grand_total = 0;

	for (let counter in counter_stores) {
		let count = counter_stores[counter];
		let counter_store = getobj(counter);
		if (!counter_store) {
			continue;
		}
		counter_store.setAttribute(`data-${type}-${counter}`, count);

		let total_count =
			(parseInt(counter_store.getAttribute(`data-event-${counter}`)) || 0)
		+	(parseInt(counter_store.getAttribute(`data-desc-${counter}`))  || 0);

		grand_total += total_count;

		counter_store.innerHTML = total_count;
		setdisplay(counter_store, total_count);
	}
	if (!grand_total) {
		// Non-admins don't have procmarkdone (no access to process lists)
		let done_obj = getobj('procmarkdone');
		if (done_obj) {
			Pf.doneItem(done_obj, true);
		}
	}
	setdisplay('fbidentical', !grand_total);
}

/**
 * @param {HTMLInputElement} self
 * @param {int} partyid
 * @param {object} event
 * @returns {void}
 */
function Pf_parseFacebookInfo(self, partyid, event) {
	if (self.form.FBTITLE	   .value.is_empty()
	&&	self.form.FBDESCRIPTION.value.is_empty()
	) {
		// No content, no parsing
		return;
	}
	if (this.PARSEFACEBOOK) {
		clearTimeout(this.PARSEFACEBOOK);
	}
	this.PARSEFACEBOOK = setTimeout(Pf_actualParseDescription, 500, self, partyid, event);
}

function Pf_actualParseDescription(self, partyid, event) {
	/** @var {HTMLFormElement} f */
	const f = self.form;

	f.FBTITLE.value		  = f.FBTITLE.value.mytrim();
	f.FBDESCRIPTION.value = f.FBDESCRIPTION.value.mytrim();

	let postdata =
		'PARTYID=' + partyid +
		'&DATE=' + (event && event.start_date ? event.start_date.year + '-' + event.start_date.month + '-' + event.start_date.day : '') +
		'&FBTITLE=' + Pf.encodeForPost(f.FBTITLE.value) +
		'&FBDESCRIPTION=' + Pf.encodeForPost(f.FBDESCRIPTION.value);

	if (self.PF_DONE_FBDATA === postdata) {
		return;
	}

	self.PF_DONE_FBDATA = postdata;

	do_inline('POST','/fbinfoparse.obj','ACT',function(req){
		if (req.status !== 200) {
			showerrors(req,ERR_TO_ALERT);
			return;
		}
		if (!req.responseText) {
			alert('No response: fbinfoparse');
			return;
		}
		/** @var {DetectedEvent} o */
		const o = JSON.parse(req.responseText);

		console.log('%cprocessing facebook description', 'font-weight:bold', o);

		let prefill_count = 0;
		let prefill_options = 0;

		if (typeof o?.dresscode !== 'undefined'
		&&	o.dresscode !== f['DRESSCODE'].value
		) {
			getobj('fb_dresscode').textContent = o.dresscode;
			unhide('fb_dresscode_row');
			++prefill_options;
		}
		if (typeof o?.genrestr !== 'undefined'
		||	typeof o?.genres   !== 'undefined'
		) {
			if (!f['GENRES'].value.is_empty()) {
				f['GENRES'].value = ', ';
			}
			f['GENRES'].value += o?.genrestr ? o.genrestr : o.genres.join(', ');
			f['GENRES'].dispatchEvent(new Event('change'));
			let r = Pf.getParentNode(f.GENRES,'TABLE');
			changerow(r,'addclass','prefilled');
			++prefill_count;
		}
		if (typeof o?.eventbritewarn !== 'undefined') {
			alert('Roep Thomas even!');
		}
		if (typeof o?.ticketlink !== 'undefined') {
			if (f['PRESALE_WEBSITE'].value.is_empty()) {
				// prefill ticketlink, but only if there is none
				// this makes sure the ticketlink from event_parse has priority
				f.PRESALE_WEBSITE.value = o.ticketlink;
				f.PRESALE_WEBSITE.dispatchEvent(new Event('change'));
				changerow(f.PRESALE_WEBSITE,'addclass','prefilled');
				++prefill_count;
			}
		}
		if (typeof o?.prices !== 'undefined') {
			let prices_before = Pf_countPrices(f);

			for (const price of o.prices) {
				/** @var {HTMLInputElement} addprice */
				const  addprice = getobj('addprice');
				if (!addprice) {
					continue;
				}
				let i = Pf_copyPrice(null, addprice, true);
				--i;
				remclass(`pricepart${i}`,'newprice');
				addclass(getobj(`pricepart${i}`)?.firstChild,'prefilled');

				for (const type of Object.keys(price)) {
					let ndx = `${type}[${i}]`;
					if (type === 'PRICE_WHAT') {
						ndx += '[]';
					}
					if (f[ndx].nodeName !== 'SELECT'
					&&	f[ndx].length
					) {
						for (const inp of f[ndx]) {
							if (inp.checked !== (inp.value === price[type])) {
								inp.checked =   (inp.value === price[type]);
								inp.dispatchEvent(new Event('click'));
							}
							inp.dispatchEvent(new Event('change'));
						}
					} else if (f[ndx].type === 'checkbox') {
						f[ndx].checked = price[type];
						f[ndx].dispatchEvent(new Event('click'));
						f[ndx].dispatchEvent(new Event('change'));

					} else {
						f[ndx].value = price[type];
						f[ndx].dispatchEvent(new Event('click'));
						f[ndx].dispatchEvent(new Event('change'));
					}
					if (type === 'PRICE_AMOUNT') {
						Pf_showActiveness(f[ndx]);
					}
				}
			}
			Pf_dedupPrices(f);

			if (prices_before !== Pf_countPrices(f)) {
				++prefill_count;
			}
		}
		if (typeof o?.door_closes !== 'undefined') {
			// 0: hour
			// 1: mins
			f['DOOR_CLOSE_HOUR'].value = parseInt(o.door_closes[0]);
			f['DOOR_CLOSE_HOUR'].dispatchEvent(new Event('change'));
			f['DOOR_CLOSE_MINS'].value = 5 * Math.floor(parseInt(o.door_closes[1]) / 5);
			f['DOOR_CLOSE_MINS'].dispatchEvent(new Event('change'));
			changerow(f.DOOR_CLOSE_HOUR,'addclass','prefilled');

			++prefill_count;
		}
		if (typeof o?.sold_out !== 'undefined') {
			// 0: completely sold out
			// 1: presale sold out
			if (o.sold_out[0]) {
				if (f['SOLD_OUT'].checked !== o.sold_out[0]) {
					f['SOLD_OUT'].click();
					++prefill_count;
				}
			} else if (o.sold_out[1]) {
				if (!f['PRESALE_SOLD_OUT'].checked) {
					 f['PRESALE_SOLD_OUT'].click();
				}
				let is_presale = [];
				for (const input of f.getElementsByTagName('INPUT')) {
					if (input.name.match(/^PRICE_SALE_TYPE\[(\d+)]/)) {
						if (Pf_radioValue(input) === 'presale') {
							is_presale[RegExp.$1] = true;
						}
					} else if(
						typeof is_presale[RegExp.$1] !== 'undefined'
					&&	/^PRICE_SOLD_OUT\[\d+]/.test(input.name)
					&&	!input.checked
					) {
						input.click();
						++prefill_count;
					}
				}

			}
		}
 		if (typeof o?.free !== 'undefined'
		&&	f.FREE_ENTRANCE.checked !== o.free
		) {
			f.FREE_ENTRANCE.click();
			changerow(f.FREE_ENTRANCE,'addclass','prefilled');
			++prefill_count;
		}
		if (typeof o?.lockers !== 'undefined'
		&&	f.LOCKERS.checked !== o.lockers
		) {
			f.LOCKERS.click();
			changerow(f.LOCKERS,'addclass','prefilled');
			++prefill_count;
		}
		if (o.max_visitors !== null) {
			if (parseInt(f.VISITORCNT.value) !== o.max_visitors) {
				f.VISITORCNT.value = o.max_visitors;
				changerow(f.VISITORCNT,'addclass','prefilled');
				++prefill_count;
			}
		}
		if (o.cancelled !== null) {
			if (!f.CANCELLED.checked) {
				 f.CANCELLED.click();
				changerow(f.CANCELLED,'addclass','prefilled');
				++prefill_count;
			}
		}
		if (o.postponed !== null) {
			if (!f.POSTPONED.checked) {
				 f.POSTPONED.click();
				changerow(f.POSTPONED,'addclass','prefilled');
				++prefill_count;
			}
		}
		if (o.ages !== null) {
			// 0: min_age
			// 1: max_age
			// 2: min_age_female

			if (o.ages[0] !== null
			&&	o.ages[0] !== parseInt(f.MIN_AGE.value)
			) {
				f.MIN_AGE.value = o.ages[0];
				f.MIN_AGE.dispatchEvent(new Event('change'));
				changerow(f.MIN_AGE,'addclass','prefilled');
				++prefill_count;
			}
			if (o.ages[1] !== null) {
				if (o.ages[1] !== parseInt(f.MAX_AGE.value)) {
					f.MAX_AGE.value = o.ages[1];
					f.MAX_AGE.dispatchEvent(new Event('change'));
					f.USE_MAX_AGE.selectedIndex = o.ages[1] ? 1 : 0;
					f.USE_MAX_AGE.dispatchEvent(new Event('change'));
					changerow(f.MAX_AGE,'addclass','prefilled');
					++prefill_count;
				}
			}
			if (o.ages[2] !== null) {
				if (o.ages[2] !== o.ages[0]
				&&	o.ages[2] !== parseInt(f.MIN_AGE_FEMALE.value)
				) {
					f.MIN_AGE_FEMALE.value = o.ages[2];
					f.MIN_AGE_FEMALE.dispatchEvent(new Event('change'));
					changerow(f.MIN_AGE_FEMALE,'addclass','prefilled');
					++prefill_count;
				}
			}
		}
		if (o.only_doorsale !== null) {
			if (f.DOORONLY.checked !== o.only_doorsale) {
				f.DOORONLY.click();
				changerow(f.DOORONLY,'addclass','prefilled');
				++prefill_count;
			}
		}
		if (o.presale_start !== null) {
			// 0: presale unix stamp
			// 1: notime indication (date only if true)
			// 2: presale date string

			if (Pf_fillPresale(f,o.presale_start,null,1)) {
				changerow(f.PRESALE_YEAR,'addclass','prefilled');
				++prefill_count;
			}
		}
		if (o.door_more !== null) {
			if (f.DOORMORE.checked !== o.door_more) {
				f.DOORMORE.click();
				changerow(f.DOORMORE,'addclass','prefilled');
				++prefill_count;
			}
		}

		Pf_setFacebookPrefillCount('desc',prefill_count,prefill_options);

	},postdata
	);
}

/**
 * @param {HTMLFormElement} f
 * @param {array} presale_start
 * @param {DetectedEvent|null} old_event
 * @param {int} priority
 * @returns {boolean}
 */
function Pf_fillPresale(f, presale_start, old_event, priority) {
	const fprio = f.PRESALE_DATE_C.getAttribute('data-filled-prio');
	if (fprio && priority < fprio) {
		return false;
	}
	f.PRESALE_DATE_C.setAttribute('data-filled-prio',priority);

	/** @var {boolean} no_time */
	const no_time = presale_start[1];
	const date = new Date(presale_start[2]);
	let filled = false;

	if (!f.PRESALE_DATE_C.checked) {
		f.PRESALE_DATE_C.click();
		filled = true;
	}
	let year	= date.getFullYear();
	let month	= date.getMonth() + 1;
	let day	= date.getDate();
	let hour	= no_time ? 24 : date.getHours();
	let mins	= date.getMinutes();
	let new_mins = 5 * Math.floor(mins / 5);

	if (parseInt(f.PRESALE_YEAR. value) !== year
	||	parseInt(f.PRESALE_MONTH.value) !== month
	||	parseInt(f.PRESALE_DAY.	 value) !== day
	||	parseInt(f.PRESALE_HOUR. value) !== hour
	||	(	no_time
		?	f.PRESALE_MINS.selectedIndex
		:	(	new_mins
			?	parseInt(f.PRESALE_MINS.value) !== new_mins
			:	f.PRESALE_MINS.selectedIndex !== 1
			)
		)
	) {
		f.PRESALE_YEAR .value	= year;
		f.PRESALE_MONTH.value	= month;
		f.PRESALE_DAY  .value	= day;
		f.PRESALE_HOUR .value	= hour;
		if (no_time) {
			f.PRESALE_MINS.selectedIndex = 0;
		} else if (!new_mins) {
			f.PRESALE_MINS.selectedIndex = 1;
		} else {
			f.PRESALE_MINS.value = new_mins;
		}
		filled = true;
	}
	return filled;
}

function Pf_getOldEventObj() {
	if (typeof Pf.oldEventObj === 'undefined') {
		let obj = null;
		const store = getobj('oldeventobj');
		if (store) {
			obj = JSON.parse(store.value);
		}
		Pf.oldEventObj = obj;
		console.log('%cold event obj', 'font-weight:bold', obj);
	}
	return Pf.oldEventObj;
}

/**
 * @param {string} id
 * @param {string} newval
 */
function Pf_optFill(id, newval) {
	let store = getobj('fb_' + id);
	if (!store) {
		return;
	}
	store.innerHTML = newval;
	changerow(store,'show');
}
/**
 * FIXME: Very incomplete, needs to by made in sync with:
 *		  - _facebook_event_parse.inc
 *		  - _facebook_description_parse.inc
 */
 /**
 * @typedef {Object} DetectedEvent
 * @property {integer} version
 * @property {integer} fbid
 * @property {DetectedDate} [start_date]
 * @property {string} [image_src]
 * @property {string} [fbtitle]
 * @property {string} [fbdescription]
 * @property array<string> [description_links]
 * @property {string} [ticketlink]
 * @property {boolean} [eventbritewarn]
 * @property {Object} [location]
 * @property {array<object>} [organization]
 * @property {array<object>} [prices]
 * @property {array<string>} [genres]
 * @property {string} [dresscode]
 * @property {array<number>} [ages]
 * @property {boolean} [free]
 * @property {boolean} [lockers]
 * @property {boolean} [cancelled]
 * @property {boolean} [postponed]
 * @property {boolean} [door_more]
 * @property {array<boolean>} [sold_out]
 * @property {boolean} [only_doorsale]
 * @property {boolean} [no_time]
 * @property {array<number>} [door_closes]
 * @property {array<number>} [presale_start]
 * @property {integer} [max_visitors]
 * @property {array<string>} [fbowners]
 * @property {string} [livestream_address]
 * @property {id} [edition]
 */
/**
 * @typedef {Object} DetectedDate
 * @property {integer} year
 * @property {integer} month
 * @property {integer} day
 * @property {integer} hour
 * @property {integer} mins
 * @property {integer} secs
 * @property {string} [tz]
 */
/**
 * @typedef {Object} PartyflockElement *
 * @property {string} name
 * @property {integer} id
 * @property {boolean} dead
 * @property {?string} [city_name]
 */
 /**
 * @typedef {Object} DetectedElement
 * @property {integer} id
 * @property {string} name
 * @property {array<int>} [fbids]
 */
/**
 * @typedef {object} DetectedOrganization
 * @extends {DetectedElement}
 *
 * @typedef {Object} DetectedLocation
 * @extends {DetectedElement}
 * @properry {array<PartyflockElement>} [elements]
 */

/**
 * @param {integer} partyid
 * @param {string} eventobj
 * @returns {void}
 */
function Pf_processFacebookPage(partyid, eventobj) {
	/** @var {HTMLFormElement} f */
	const f = getobj('agendaform');
	if (!f) {
		return;
	}
	if (f.EVENTOBJ) {
		f.EVENTOBJ.value = eventobj;
	} else {
		const inp = document.createElement('INPUT');
		inp.type = 'hidden';
		inp.name = 'EVENTOBJ';
		inp.value = eventobj;
		f.appendChild(inp);
	}
	/** @var {DetectedEvent} event */
	const event = JSON.parse(eventobj);

	console.log('%cprocessing facebook event', 'font-weight:bold', event);

	let changed = {};
	/** @var {string} fieldname */
	let fieldname;

	let prefillcnt = 0;
	let prefillopts = 0;
	let differs;

	/** @var {DetectedEvent} old_event */
	const old_event = Pf_getOldEventObj();
	let bad_presale = false;

	if (event['ticketlink']
	&&	/facebook\.com\/events\/\d+/.test(event['ticketlink'])
	&&	!f['PRESALE_WEBSITE'].value.includes('eventbrite')
	) {
		bad_presale = true;
		alert('Eventbrite link moet expliciet opgezocht worden!');
		markRefill(f['PRESALE_WEBSITE']);
	}

	if (event['fbtitle']
	||	event['fbdescription']
	) {
		// first do fbtitle and fbdescription and parse description, so our event parse overwrites (presale date for example).
		// event parse is more accurate
		['fbtitle', 'fbdescription'].forEach(function(attrib) {
			if (typeof event[attrib] === 'undefined') {
				return;
			}
			fieldname = attrib.toUpperCase();
			if (!old_event
			||	 old_event[attrib] !== event[attrib]
			) {
				f[fieldname].value = event[attrib];
				changed['FB'] = true;
				changerow(f[fieldname],'addclass','prefilled');
				++prefillcnt;
			}
		});
		if (changed['FB']) {
			Pf_parseFacebookInfo(f['FBDESCRIPTION'],partyid,event);
			delete changed['FB'];
		}
	}
	if (event['eventbritewarn']) {
		alert('Eventbrite zoekoperatie wordt geopend, opdat je de voorverkooplink kunt achterhalen');
		window.open(
			'https://www.google.com/search?num=100&newwindow=1&q='
		+	encodeURI(event['name'])
		+	'+site%3Aeventbrite.nl'
		);
	}
	/** @var {string} attrib */
	for (let attrib in event) {
		/** @var {integer} */
		let form_int_value;
		/** @var {boolean} */
		let differs= false;
		// noinspection BlockStatementJS
		switch (attrib) {
		case 'description_links':
			event[attrib] = event[attrib].join("\x1F");
		case 'fbowners':
			// always overwrite
			fieldname = attrib.toUpperCase();
			if (!f[fieldname]) {
				const inp = document.createElement('INPUT');
				inp.type = 'hidden';
				inp.name = fieldname;
				f.appendChild(inp);
			}
			f[fieldname].value = event[attrib];
			break;

		case 'livestream':
			if (f.LIVESTREAM.selectedIndex === event[attrib] ? 0 : 1) {
				f.LIVESTREAM.selectedIndex = event[attrib] ? 1 : 0;
				f.LIVESTREAM.dispatchEvent(new Event('change'));
				++prefillcnt;
				changerow(f.LIVESTREAM, 'addclass', 'prefilled');
				changed['LIVESTREAM'] = true;
			}
			break;

		case 'livestream_address':
			if (f.LIVESTREAM_ADDRESS.value.is_empty()) {
				changed['LIVESTREAM_ADDRESS'] = true;
				f.LIVESTREAM_ADDRESS.value = event[attrib];
				f.LIVESTREAM_ADDRESS.dispatchEvent(new Event('change'));
				changerow(f.LIVESTREAM_ADDRESS,'addclass','prefilled');
				++prefillcnt;
			} else {
				Pf_optFill('LIVESTREAM_ADDRESS',event[attrib]);
				++prefillopts;
			}
			break;

		case 'edition':
			if (!partyid
			||	!f.EDITION.selectedIndex
			) {
				Pf_optFill('EDITION',String(event.edition));
				changerow(f.EDITION,'addclass','prefilled');
				++prefillopts;
			}
			break;

		case 'clean_title':
			f.NAME_FOR_APPIC.setAttribute('data-prefill',event[attrib]);
			break;

		case 'name':
			if (!partyid) {
				f.NAME.value = event.name;
				changed['NAME'] = true;
				changerow(f.NAME, 'addclass', 'prefilled');
				++prefillcnt;
				f.NAME.dispatchEvent(new Event('change'));

			} else if (
				f.NAME.value !== event.name
			&&	f.FBTITLE.value !== event['fbtitle']
			&&	(	!old_event
				||	 old_event[attrib] !== event[attrib]
				)
			) {
				console.log('show option for ' + attrib + ' because ' + f.NAME.value + ' != ' + event.name);
				Pf_optFill('NAME', event.name);
				++prefillopts;
			}
			break;

		case 'location': {
			// ignore location for livestreams
			if (event['livestream']
			||	f.LIVESTREAM.selectedIndex === 1
			) {
				break;
			}
			let location_id = 0;
			if (event.location.elements) {
				[location_id] = Object.entries(event.location.elements)[0];
				if (!location_id) {
					break;
				}
			}
			if (!partyid) {
				// don't fill location with name, too dangerous (similar locations are then chosen)
				console.log('setting LOCATION_ADDR');
				f.LOCATION_ADDR.value = event.location.name + (location_id ? '~' + location_id : '');
				changed['LOCATION_ADDR'] = true;
				changerow(f.LOCATION_ADDR, 'addclass', 'prefilled');
				++prefillcnt;
			} else if (
				f.LOCATIONID
			&&	parseInt(f.LOCATIONID.value) !== location_id
			&&	(	!old_event
				||	 old_event[attrib] !== event[attrib]
				)
			) {
				Pf_optFill('LOCATION',event.location.name + (location_id ? '~' + location_id : ''));
				++prefillopts;
			} else if (
				f.UNKNOWN_LOCATION.checked
			) {
				f.UNKNOWN_LOCATION.checked = false;
				f.UNKNOWN_LOCATION.dispatchEvent(new Event('click'));
				f.LOCATION_ADDR.value = event.location.name + (location_id ? '~' + location_id : '');
				changed['LOCATION_ADDR'] = true;
				changerow(f.LOCATION_ADDR, 'addclass', 'prefilled');
				++prefillcnt;
			}
			break;
		}

		case 'free_entrance':
			fieldname = attrib.toUpperCase();
			if (!partyid
			||	!old_event
			||	old_event[attrib] !== event[attrib]
			) {
				f[fieldname].checked = event[attrib];
				changed[fieldname] = true;
				changerow(f[fieldname],'addclass','prefilled');
				++prefillcnt;
			}
			break;

		case 'genres':
			fieldname = attrib.toUpperCase();
			if (!partyid
			||	!old_event
			||	!old_event[attrib].equals(event[attrib])
			) {
				f[fieldname].value += event[attrib].join('!, ') + '!';
				changed[fieldname] = true;
				changerow(f[fieldname],'addclass','prefilled');
				++prefillcnt;
			}
			break;

		case 'presale_start':
			// always fill
			if (Pf_fillPresale(f,event[attrib],old_event, 2)) {
				changerow(f.PRESALE_YEAR, 'addclass', 'prefilled');
				++prefillcnt;
			}
			break;

		case 'cancelled':
			if (!partyid
			||	!f.CANCELLED.checked
			) {
				f.CANCELLED.click();
				changerow(f.CANCELLED, 'addclass', 'prefilled');
				++prefillcnt;
			}
			break;

		case 'min_age':
			fieldname = attrib.toUpperCase();
			form_int_value = parseInt(f?.[fieldname]?.value);
			differs = false;
			if (!partyid
			||	(differs =
					form_int_value !== event[attrib]
				&&  (form_int_value ||  event[attrib])  // Not both 0 or empty
				)
			&&	(	!old_event
				||	(parseInt(f[fieldname].value) !== old_event[attrib])
				)
				// ^ no previous event or oldevent is same as flock stored
			) {
				console.log(`${fieldname}/${attrib}:${f[fieldname].value} !==? ${event[attrib]}`);

				f[fieldname].value = event[attrib];
				changed[fieldname] = true;
				++prefillcnt;
				if (partyid) {
					addclass( f[fieldname], 'prefilled');
					changerow(f[fieldname], 'addclass', 'newfromfb');
				}
			} else if (differs) {
				console.log('show option for 0000' + fieldname + ' because ' + f[fieldname].value + ' != ' + event[attrib]);
			}

		case 'duration_hours':
		case 'duration_mins':
			fieldname = attrib.toUpperCase();
			form_int_value = parseInt(f?.[fieldname]?.value);
			differs = false;
			if (!partyid
			||	(differs =
					form_int_value !== event[attrib]
				&&  (form_int_value ||  event[attrib])              // not both 0
				)
			&&	(	!old_event
				||	(f[fieldname].value.is_empty() ? false : f[fieldname].value) === old_event[attrib]
				)
				// ^ no previous event or oldevent is same as flock stored
//			||	!f[fieldname].value.length
			) {
				console.log(`${fieldname}/${attrib}:${f[fieldname].value} !==? ${event[attrib]}`);

				f[fieldname].value = event[attrib];
				changed[fieldname] = true;
				++prefillcnt;
				if (partyid) {
					addclass (f[fieldname],'prefilled');
					changerow(f[fieldname],'addclass','newfromfb');
				}
			} else if (differs) {
				console.log('show option for ' + fieldname + ' because ' + f[fieldname].value + ' != ' + event[attrib]);
				Pf_optFill('DURATION',`${event['duration_hours']}:${event['duration_mins'] < 10 ? '0' : ''}${event['duration_mins']}`);
			}
			break;

		case 'start_date':
			differs = false;

			if (!partyid
			||	// doing this comparison goes wrong when dragging Facebook event in non UTC+[12] timezone
				// is this comparison actually needed?

				(	differs =
					parseInt(f['YEAR' ].value) !== event.start_date.year
				||	parseInt(f['MONTH'].value) !== event.start_date.month
				||	parseInt(f['DAY'  ].value) !== event.start_date.day
				||	parseInt(f['HOUR' ].value) !== event.start_date.hour
				||	parseInt(f['MINS' ].value) !== event.start_date.mins
				)
			&&	(	!old_event
				||	!old_event.start_date
				||	!(	event.start_date.year	=== old_event.start_date.year
					&&	event.start_date.month	=== old_event.start_date.month
					&&	event.start_date.day	=== old_event.start_date.day
					&&	event.start_date.hour	=== old_event.start_date.hour
					&&	event.start_date.mins	=== old_event.start_date.mins
					&&	event.start_date.tz		=== old_event.start_date.tz
					)
				)
			) {
				for (const field of Object.keys(event.start_date)) {
					fieldname = field.toUpperCase();
					if (fieldname === 'TZ') {
						let i;
						for (i = 0; i < f['TZ'].options.length; ++i) {
							const option = f['TZ'].options[i];
							if (option.value === event.start_date['TZ']) {
								f['TZ'].selectedIndex = i;
								unhide(f['TZ']);
								break;
							}
						}
						// not found create new option
						console.log('creating new option for ' + event.start_date['tz']);
						const opt = document.createElement('option');
						// noinspection NestedAssignmentJS
						opt.value = opt.textContent = event.start_date['tz'];
						f['TZ'].appendChild(opt);
						f['TZ'].selectedIndex = i;
						unhide(f['TZ']);
						continue;
					}
					f[fieldname].value = event.start_date[field];
					changed[fieldname] = true;
					if (partyid) {
						addclass(f[fieldname],'prefilled');
					}
				}
				if (partyid) {
					changerow([f['YEAR'], f['HOUR']], 'addclass', 'newfromfb');
				}
				++prefillcnt;

			} else if (differs) {
				let dt = new Date(
					event.start_date['year'],
					event.start_date['month'] - 1,
				 	event.start_date['day'],
				 	event.start_date['hour'],
				 	event.start_date['mins']
				);
				Pf_optFill('DATE', dt.toLocaleDateString('nl-NL', {
					day:	'numeric',
					month:	'long',
					year:	'numeric',
					hour:	'numeric',
					minute:	'numeric',
				}) + ' ' + Intl.DateTimeFormat().resolvedOptions().timeZone);
			}
			break;

		case 'only_doorsale':
			if (f.DOORONLY.checked !== event[attrib]) {
				f.DOORONLY.click();
				changerow(f.DOORONLY, 'addclass', 'prefilled');
				++prefillcnt;
			}
			break;

		case 'ticketlink':
			if (!bad_presale
			&&	(	!partyid
				||	f.PRESALE_WEBSITE.value.is_empty()
				&&	(	!old_event
					||	old_event[attrib] !== event[attrib]
					)
				)
			) {
				f.PRESALE_WEBSITE.value = event.ticketlink;
				changed['PRESALE_WEBSITE'] = true;
				changerow(f.PRESALE_WEBSITE,'addclass','prefilled');
				++prefillcnt;
			}
			break;

		case 'organizations': {
			let orgs = [];
			let have_orgs = [];
			for (const key of ['ORGANIZATIONID[]', 'ORGASHOSTID[]']) {
				const inputs = document.getElementsByName(key);
				if (!inputs) {
					continue;
				}
				for (const organization_input of inputs) {
					const orgid = parseInt(organization_input.value);
					if (!orgid) {
						continue;
					}
					have_orgs.push(orgid);
				}
			}
			let new_orgs = false;
			for (let single_org of event.organizations) {
				if (!single_org.elements) {
					continue;
				}
				for (const [/* orgid*/, org] of Object.entries(single_org.elements)) {
					// orgid is a string
					// org.id is a number
					if (-1 === have_orgs.includes(org.id)) {
						orgs.push(`${org.name}~${org.id}`);
						new_orgs = true;
					}
				}
			}
			if (orgs.length) {
				if (!/(?:^\s*|,\s*)$/.test(f.ORGANIZATION_NAME.value)) {
					f.ORGANIZATION_NAME.value += ', ';
				}
				f.ORGANIZATION_NAME.value += orgs.join(', ');
				changed['ORGANIZATION_NAME'] = true;
				++prefillcnt;
				if (new_orgs) {
					changerow(f.ORGANIZATION_NAME,'addclass','newfromfb');
				}
			}
			break;
		}

		case 'image_src':
			if (!partyid) {
				Pf_addUploadURL(f, event.image_src, 'URL[]');
				++prefillcnt;
			}
			break;

		case 'subtitle':
			if (!partyid) {
				f.SUBTITLE.value = event.subtitle;
				changed['SUBTITLE'] = true;
				changerow(f.SUBTITLE, 'addclass', 'prefilled');
				++prefillcnt;
				f.SUBTITLE.dispatchEvent(new Event('change'));
			}
			break;

		case 'fbid': {
			let fb_id = event.fbid;
			let time_id = event.event_time_id;
			if (partyid
			||	!f.PRESENCE.value.is_empty()
			) {
				let fbs = f.PRESENCE.value.split('\n');
				for (const fbl of fbs) {
					if (fbl.is_empty()) {
						continue;
					}
					if (fbl.match(/events\/(\d+)/)) {
						const event_id = parseInt(RegExp.$1);
						if (fb_id === event_id) {
							fb_id = null;
						}
						if (time_id === event_id) {
							time_id = null;
						}
					}
					if (fbl.match(/event_time_id=(\d+)/)) {
						if (time_id === parseInt(RegExp.$1)) {
							time_id = null;
						}
					}
				}
			}
			if (fb_id || time_id) {
				if (!f.PRESENCE.value.is_empty()
				&&	!/\n\s*$/.test(f.PRESENCE.value)
				) {
					f.PRESENCE.value += "\n";
				}
				if (fb_id) {
					f.PRESENCE.value += 'https://www.facebook.com/events/' + fb_id + "\n";
				}
				if (time_id) {
					f.PRESENCE.value += 'https://www.facebook.com/events/' + event.fbid + '/?event_time_id=' + time_id + "\n";
				}
				changed['PRESENCE'] = true;
				changerow(f.PRESENCE,'addclass','prefilled');
				++prefillcnt;
			}
			break;
		}}
	}

	Pf_setFacebookPrefillCount('event',prefillcnt,prefillopts);

	let event_names = { change: true, blur: true, keyup: true };

	for (let change_name in changed) {
		for (let event_name in event_names) {
			f[change_name].dispatchEvent(new Event(event_name));
		}
		let change_base =
			change_name === 'GENRES'
		?	Pf.getParentNode(f.GENRES,'TABLE')
		:	f[change_name];

		changerow(change_base,'addclass','prefilled');
	}
}
function Pf_showAlternativeTitles(self) {
	changerow(self,'hide');
	unhide('name_for_appic_row');
	let f = getobj('agendaform');
	if (!f) {
		return;
	}
	if (f.NAME_FOR_APPIC.value.is_empty()) {
		f.NAME_FOR_APPIC.value = f.NAME_FOR_APPIC.getAttribute('data-prefill');
	}
}

function Pf_fillGenres(org) {
	if (!org.checked
	||	!org.form.NEW
	) {
		return;
	}
	let gids = org.getAttribute('data-gids').split(',');
	Pf_fillActualGenres(org.form,gids);
}

function Pf_fillActualGenres(form, gids) {
	for (const inp of form.elements) {
		if (inp.name !== 'GID[]') {
			continue;
		}
		if (-1 !== gids.indexOf(inp.value)) {
			if (!inp.checked) {
				inp.checked = true;
				inp.dispatchEvent(new Event('click'));
			}
		}
	}
}

function Pf_changeEventNameOrSubtitle(input) {
	let form = input.form;
	['NAME','SUBTITLE'].forEach(function(field) {
		let new_category;
		if (/after[\s-]*(?:party|hours?)?/i.test(form[field].value)
		&&	form['CATEGORY'].value !== 'afterparty'
		) {
			new_category = 'afterparty';
		}
		if (/pre[\s-]*party/i.test(form[field].value)
		&&	form['CATEGORY'].value !== 'preparty'
		) {
			new_category = 'preparty';
		}
		if (/festival|open[\s-]*air|outdoor/i.test(form[field].value.match())
		//	if name or subtitle contains both festival and after/pre-party then it's not a festival, but after/pre-party
		&&	!/after[\s-]*(?:party|hours?)?|pre[\s-]*party/i.test(form[field].value)
		&&	 form['CATEGORY'].value !== 'festival'
		) {
			new_category = 'festival';
		}
		if (new_category) {
			form['CATEGORY'].value = new_category;
			// for upLite, it only registers click handler, not change, which in this
			// case is a little weird
			form['CATEGORY'].forEach(input => input.dispatchEvent(new Event('click')));
		}
	});

	// Fetch alternate names, based on the current event NAME.
	// So when this is not the NAME input, NAME has not changed.
	if (input.name !== 'NAME'
	//	Also, empty NAME can of course not be used for finding alternates.
	||	!input.value
	//	The RENAMES field must exist of course, otherwise we cannot
	//	fill it with the result.
	//	Non-admins don't have a RENAMES field.
	||	form['RENAMES']
	&&	form['RENAMES']?.value.is_empty()
	) {
		return;
	}
	fetch('/alternate_names.obj', {
		method:	'post',
		headers:{'Content-Type': 'application/x-www-form-urlencoded; charset=utf-8'},
		body:	'ELEMENT=party&NAME=' + Pf.encodeForPost(input.value)
	})
	.then(function(response){
		if (!response.ok) {
			console.warn('failed to get alternate names', response);
			return;
		}
		response.json().then(function(alternate_names) {
			if (!alternate_names.length) {
				return;
			}
			input.form['RENAMES'].value = alternate_names.join("\n");
			Pf_fitTextarea(input.form['RENAMES']);
		});
	})
	.catch(function(error) {
		console.warn('error:', error);
	});
}
function Pf_addPercentage(self, percentage) {
	if (!percentage) {
		return;
	}
	const match = self.name.match(/(?<ndx>\[\d+])$/);
	if (!match) {
		console.warn(`no index in name: ${self.name}`);
		return;
	}
	const ndx = match.groups.ndx;
	const form = getobj('agendaform', true);
	if (!form?.[`PRICE_AMOUNT${ndx}`]
	||	!form?.[`PRICE_FEE${ndx}`]
	) {
		return;
	}
	form[`PRICE_TRANSACTION_FEE${ndx}`].value =
		self.checked
	?	Math.round(
		(	(parseInt(form[`PRICE_AMOUNT${ndx}`].value) || 0)
			+	(parseInt(form[`PRICE_FEE${ndx}`].	 value) || 0)
			)
			* percentage / 100
		)
	:	'';
}

addreadyevent(function() {
	if (Pf_findSubmittedParty()) {
		return;
	}
	let bodies;
	let addrow;
	Pf.priceinfo = {
		addrow: addrow = getobj('addrow', true),
		bodies:	bodies = addrow ? parseInt(addrow.getAttribute('data-bodies')) : 1,
		actives: bodies
	};
	let form = getobj('agendaform');
	if (!form) {
		return;
	}
	// have FBPARSE?
	let partyid = form.getAttribute('data-partyid');
	if (!partyid) {
		return;
	}
	let key = 'FBPARSE:party:' + partyid;
	let eventobj = sessionStorage.getItem(key);
	if (eventobj) {
		sessionStorage.removeItem(key);
		Pf_processFacebookPage(partyid,eventobj);
	}
});
