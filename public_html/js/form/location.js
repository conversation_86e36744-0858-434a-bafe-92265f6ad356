function changeLocType(sobj, updateparties) {
	let form = sobj.form;
	Pf.locType.init(form);
	if (updateparties) {
		setdisplay(
			'update-parties-row',
				Pf.locType.cnt() === 1
			&&	!Pf.locType[form.UPDATEPARTIES.getAttribute('data-current')].checked
		);
	}
	let typecnt = Pf.locType.cnt();
	let boat = Pf.locType.boat.checked && typecnt === 1;
	if (boat) {
		setattr([form.LATITUDE, form.LONGITUDE, form.ADDRESS, form.CITYID], 'required', false);
	} else {
		setattr([form.LATITUDE,form.LONGITUDE, form.CITYID], 'required', true)
		setattr(form.ADDRESS, 'required',
				!form.ALLOW_EMPTY_ADDR
			||	!form.ALLOW_EMPTY_ADDR.checked
		);
	}
}
function clickAllowEmpty(self) {
	if (self.checked) {
		self.form.ADDRESS.className = '';
	}
	setattr(self.form.ADDRESS,'required',!self.checked);
}
function submitLocationForm(form,locationid) { try {
	Pf.locType.init(this);
	let typecnt = Pf.locType.cnt();
	if (!typecnt) {
		alert('Je moet minstens een type aangeven!');
		let location_types = getobj('location-types');
		if (location_types) {
			markRefill(location_types);
		}
		return false;
	}
	if (form.NAME
	&&	!RegExp(RegExp.escape(form.NAME.value),'i').test(form.TITLE.value)
	) {
		alert('Naam moet een onderdeel van de titel zijn!');
		markRefill(form.NAME);
		return false;
	}
	if (form.ORDERNAME
	&&	!RegExp(RegExp.escape(form.ORDERNAME.value),'i').test(form.TITLE.value)
	) {
		alert('Sorteernaam moet een onderdeel van de titel zijn!');
		markRefill(form.NAME);
		return false;
	}
	if (!form.ADDRESS.value.is_empty()
	&&	(	!form.OVERLAP
		||	!form.OVERLAP.checked
		)
	) {
		let creq = initrequest(true);
		if (!creq) {
			return false;
		}
		creq.open('POST', '/findsameaddress.obj', false);
		creq.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
		creq.send(
			'CITYID='			+ form.CITYID.value +
			'&ELEMENT=location'	+
			'&ELEMENTID='		+ locationid +
			'&FOLLOWUPID='		+ encodeURIComponent(form.FOLLOWUPID.value)+  
			'&ADDRESS='			+ encodeURIComponent(form.ADDRESS.value)
		);
		if (creq.status !== 200) {
			alert('HTTP status ' + creq.status);
			return false;
		}
		if (creq.responseText) {
			markRefill(form.ADDRESS, creq, null, ERR_TO_HTML | ERR_TO_ALERT | ERR_CLEAR);
			return false;
		}
	}
	return submitForm(form);
} catch(e) {
	catchLog(e);
	return false;
}}
