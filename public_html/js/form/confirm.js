function get_confirmation(self,question,href,target,data) {
	self.className = 'light';
	if (confirm(question)) {
		if (typeof href === 'function') {
			href(self,data);
		} else {
			var f = document.createElement('form');
			if (!f) return;
			if (secret = self.getAttribute('data-secret')) {
				var i = document.createElement('input');
				if (!i) return;
				i.type = 'hidden';
				i.name = 'SECRET';
				i.value = secret;
				f.appendChild(i);
			}
			f.className = 'hidden';
			f.method = 'POST';
			f.action = href;
			if (target) {
				f.target = target;
			}
			if (data) {
				for (var key in data) {
					var inp = document.createElement('input');
					inp.type = 'hidden';
					inp.name = key;
					inp.value = data[key];
					f.appendChild(inp);
				}
			}
			document.body.appendChild(f);
			f.submit();
			f.parentNode.removeChild(f);
		}
	}
	self.className = 'unhideanchor';
}
