function submitRequestForm(form) { try {
	let requestfor = form['REQUESTFOR[]'];
	if (!requestfor) {
		return false;
	}
	if (!requestfor.checked) {
		let checked = 0;
		for (let i = 0; i < requestfor.length; ++i) {
			if (requestfor[i].checked) {
				++checked;
			}
		}
		if (!checked) {
			let col = getobj('requestforcol');
			if (!col) {
				return false;
			}
			markRefill(col, col.getAttribute('data-error'));
			return false;
		}
	}
	return submitForm(form);
} catch(e) {
	catchLog(e);
	return false;
}}
