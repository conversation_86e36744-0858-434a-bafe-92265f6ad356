function strongEnough(self) {
	if (self.pftimerID) {
		clearTimeout(self.pftimerID);
	}
	setclass(['pwderr','passadv'],true,'light');
	self.pftimerID = setTimeout(function(){
		self.pftimerID = null;
		do_inline('POST','/passwdstrength.obj','ACT',function(req){
			if (req.status !== 200) {
				return;
			}
			var fail = req.getResponseHeader('X-FAIL');
			var msg = getobj(fail ? 'pwderr' : 'pwdadv');
			if (!msg) {
				return;
			}
			msg.innerHTML = req.responseText;
			if (!fail) {
				var msg = getobj('pwderr');
				if (msg) {
					msg.innerHTML = '';
				}
			}
			setdisplay('passadv',!fail && req.responseText);
			setclass(['pwderr','passadv'],false,'light');
		},'PASSWD='+urlencode_utf8_to_windows1252(self.value)[0])
	},
		500
	);
}
function passwordsOK(form) {
	var msg = getobj('pwderr',true);
	if (msg
	&&	msg.innerHTML
	) {
		markRefill(form.NEW_PASSWD);
		return false;
	}
	if (form.NEW_PASSWD.value != form.NEW_PASSWD_TWO.value) {
		markRefill(form.NEW_PASSWD_TWO);
		return false;
	}
	return true;
}
function submitPasswordForm(form) {
	return passwordsOK(form) && submitForm(form);
}

