Pf.showLogBook = function(self) {
	if (self.nextSibling) {
		swapdisplay(self.nextSibling);
		return;
	}
	do_inline(
		'GET', 
			'/getlogbook.obj'
		+	'?ELEMENT=' + document.body.getAttribute('data-element')
		+	'&ID='	 + document.body.getAttribute('data-id'),
		'ACT', function(req) {

		if (req.status !== 200) {
			showerrors(req, ERR_TO_ALERT);
			return;
		}
		let newdiv = document.createElement('div');
		newdiv.innerHTML = req.responseText;
		let ins = newdiv.firstChild;
		self.parentNode.appendChild(ins);
		if (Pf_initGrowToFits) {
			Pf_initGrowToFits(ins);
		}
	});
}

Pf.newLogEntry = function(self) {
	let newlog = getobj('newlog');
	if (!newlog) {
		return;
	}
	let nl = newlog.cloneNode(true);
	nl.className = '';
	newlog.parentNode.insertBefore(nl, newlog);
}

Pf.storeLogEntry = function(form, logid) {
	if (form.busy) {
		return;
	}
	form.busy = true;
	
	let poststr = build_poststr(form)
		+ '&ELEMENT=' + document.body.getAttribute('data-element')
		+ '&ID='	  + document.body.getAttribute('data-id');
	
	if (logid) {
		poststr += '&LOGID=' + logid;
	}
	
	addclass(form, 'light novents');
	
	do_inline('POST', '/storelog.act', 'ACT', function(req) {
		remclass(form, 'light noevents');
		if (req.status !== 200) {
			showerrors(req, ERR_TO_ALERT);
			return;
		}
		let newdiv = document.createElement('div');
		newdiv.innerHTML = req.responseText;
		Pf.insertAfter(newdiv.firstChild, form);
		form.parentNode.removeChild(form);
	}, poststr);
}

Pf.changeLogEntry = function(self, logid) {
	let logitem = Pf.getParent(self.parentNode, 'logitem');
	if (!logitem) {
		return;
	}
	addclass(logitem, 'light noevents');
	
	do_inline('GET', '/getlog.obj?LOGID=' + logid, 'ACT', function(req) {
		remclass(logitem, 'light noevents');
		if (req.status !== 200) {
			showerrors(req, ERR_TO_ALERT);	
			return;
		}
		let newdiv = document.createElement('div');
		newdiv.innerHTML = req.responseText;
		let ins = newdiv.firstChild;
		logitem.parentNode.replaceChild(ins,logitem);
		if (Pf_initGrowToFits) {
			Pf_initGrowToFits(ins);
		}
		ins.parentNode.scrollTop = ins.offsetTop - ins.parentNode.offsetTop;
	});
}

Pf.deleteLogEntry = function(form, logid) {
	addclass(form, 'light noevents');

	do_inline('POST', '/storelog.act', 'ACT', function(req) {
		remclass(form, 'light noevents');
		if (req.status !== 200) {
			showerrors(req,ERR_TO_ALERT);
			return;
		}
		form.parentNode.removeChild(form);
	},
	'LOGID=' + logid + '&REMOVE');
}
