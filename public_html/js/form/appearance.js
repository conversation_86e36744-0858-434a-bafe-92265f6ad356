let lineo = false;
let boxo = false;

function get_lineo() {
	return lineo ? lineo : (lineo = getobj('appearanceline'));
}

function get_boxo() {
	return boxo ? boxo : (boxo = getobj('appearancetextbox'));

}

function addcomma(comma) {
	let lineo = get_lineo();
	if (lineo) {
		lineo.value += comma;
	}
	let boxo = get_boxo();
	if (boxo) {
		boxo.value += "\n";
	}
}

function copynick(node) {
	let lineo = get_lineo();
	let nodeText = Pf.getText(node);
	if (lineo) {
		lineo.value += nodeText;
	}
	let boxo = get_boxo();
	if (boxo) {
		boxo.value += nodeText + "\n";
	}
}

function copyall() {
	let lineo = get_lineo();
	if (lineo) {
		let allnicks_line = getobj('allnicksline');
		if (allnicks_line) {
			if (lineo.value.length
			&&	!/[,\u201a\u0082][\s\u00ad]*$/.test(lineo.value)
			) {
				lineo.value += ', ';
			}
			lineo.value += allnicks_line.value;
		}
	}
	let boxo = get_boxo();
	if (boxo) {
		let allnicks_box = getobj('allnicksbox');
		if (allnicks_box) {
			if (boxo.value.length
			&&	!boxo.value.match(/\n$/)
			) {
				boxo.value += "\n";
			}
			boxo.value += allnicks_box.value;
		}
	}
}

addevent(window, 'keydown', function (e) {
	if (e.code !== 'ShiftLeft'
	&&	e.code !== 'ShiftRight'
	) {
		return;
	}
	if (document.activeElement
	&&	(	document.activeElement.tagName === 'INPUT'
		||	document.activeElement.tagName === 'TEXTAREA'
		)
	) {
		return;
	}
	['appearanceline', 'appearancebox'].forEach(function(id) {
		let appearance_input= getobj(id);
		if (!appearance_input) {
			return;
		}
		appearance_input.focus();
	});
});
