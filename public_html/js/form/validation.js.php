<?php

declare(strict_types=1);

define('CURRENTSTAMP',time());

require_once '../../_offline.inc';
require_once '../../__translation.php';
require_once '../../_hashes.inc';
require_once '../../_modified.inc';
require_once '../../_unicode.inc';

function bail(int $errno): never {
	http_response_code($errno);
	exit;
}

if (not_modified(etag: '2014012101_'.get_hash('js/main.js.php'))) {
	bail(304);
}

header('Content-Type: text/javascript');
header('Vary: User-Agent');

?>
const COUNTRYID_LUXEMBOURG 				= 26;
const MINIMUM_PHONE_LENGTH				= 10;
const MINIMUM_PHONE_LENGTH_LUXEMBOURG	= 9;
const NAXIMUM_PHONE_LENGTH				= 20;
const MINIMUM_LATITUDE					= -90;
const MAXIMUM_LATITUDE					=  90;
const MINIMUM_LONGITUDE					= -180;
const MAXIMUM_LONGITUDE					=  180;

/** @param {HTMLFormControlsCollection} elements
 *  @return {boolean}
 */
function validateElements(elements) {
	try {
		const radio_dones = {};
		const email_dones = {};

		for (const element of elements) {
			if (element.disabled) {
				continue;
			}
			if (element.name === 'g-recaptcha-response') {
				continue;
			}
			if (-1 !== element.name.indexOf('COPYRIGHT')) {
				Pf?.enterCopyright?.(null, element);
			}
			if ( element.hasAttribute('data-bad')
			&&	!element.hasAttribute('data-bad-ok')
			) {
				markRefill(element);
				return false;
			}
			if ((	element.getAttribute('data-want-charset') === 'ascii'
				||	null !== element.getAttribute('data-want-ascii')
				||	element.type === 'url'
				||	element.type === 'email'
				)
			&&	!/^[\x00-\x7F]*$/.test(element.value)
			) {
				markRefill(element);
				return false;
			}
			let valid = element.getAttribute('data-valid');
			let value_is_empty;
			if (element.nodeName === 'SELECT') {
				value_is_empty = !element.selectedIndex && !element.value && !element.value.length;
			} else if (element.type === 'button') {
				if (element.getAttribute('data-needclick')) {
					markRefill(element,element.getAttribute('data-errmsg'));
					return false;
				}
			} else if (element.type === 'radio') {
				value_is_empty = true;
				if (typeof radio_dones[element.name] === 'undefined') {
					let radios = element.form[element.name];
					if (radios === element) {
						// only 1
						radios = [radios];
					}
					for (const radio of radios) {
						if (radio.checked) {
							value_is_empty = false;
							break;
						}
					}
					radio_dones[element.name] = value_is_empty;
				} else {
					value_is_empty = radio_dones[element.name];
				}
			} else {
				if (element.type === 'checkbox') {
					value_is_empty = !element.checked;
				} else if (element.type === 'number') {
					if (valid !== 'lnglat'
					&&	element.name !== 'LATITUDE'
					&&	element.name !== 'LONGITUDE'
					) {
						value_is_empty = !parseInt(element.value);
					} else {
						value_is_empty = !parseFloat(element.value);
					}
				} else {
					value_is_empty = !element.value || element.value.is_empty();
				}
			}
			let required = element.required;
			if (required
			&&	!/^\s*(?:false|no|0+)\s*$/.test(required)
			&&	value_is_empty
			) {
				markRefill(element,'<?= __('js:form:error:need_something',IS_JAVASCRIPT) ?> (%NAME%)');
				return false;
			}
			let span;
			if (/(?:YEAR|MONTH|DAY)$/.test(element.name)
			&&	(span = element.parentNode)
			&&	span.className === 'error'
			&&	span.getAttribute('data-prefix')
			) {
				Pf.scrollToMiddle(span);
				alert('<?= __('js:form:error:date_incorrect',IS_JAVASCRIPT) ?>');
				return false;
			}
			const min_length = element.minLength;
			if (min_length
			&&	element.value.length < min_length
			) {
				markRefill(element,String('<?= __('js:form:error:need_at_least_x_characters',IS_JAVASCRIPT) ?>').replace_keywords({'X':min_length}));
				return false;
			}
			if (!valid) {
				valid = element.type;
			}
			if (!value_is_empty) {
				const url_or_view_source = valid === 'url_or_view_source';
				let ok = true;
				if (url_or_view_source
				||	valid === 'url'
				) {
					let url_str = element.value;
					if (url_or_view_source) {
						const url_match = element.value.match(/^view-source:(?<url>.*)$/);
						if (url_match) {
							url_str = url_match.groups.url;
						}
					}
					let url;
					try {
						url = new URL(url_str);
					} catch (error) {
						console.error(error);
						ok = false;
					}
				} else if (element.pattern) {
					ok = RegExp(element.pattern).test(element.value);
				}
				if (!ok) {
					markRefill(element,'<?= __('js:form:error:input_incorrect',IS_JAVASCRIPT) ?> (%NAME%)' + (element.title ? "\n" + element.title : ''));
					return false;
				}
			}
			let check_placeholders = element.getAttribute('data-check-placeholders');
			if (check_placeholders) {
				let pre = new RegExp(check_placeholders);
				if (pre.test(element.value)) {
					markRefill(element,'<?= __('js:form:error:input_incorrect',IS_JAVASCRIPT) ?> (%NAME%)'+(
						RegExp.$1
					));
					return false;
				}
			}
			if (element.nodeName === 'TEXTAREA'
			&&	element.name === 'PRESENCE'
			) {
				let lines = element.value.split(/[\r\n]+/);
				let ok_lines = [];
				for (let line of lines) {
					if (line.match(/^\w+\s*:\s*(http.*?)\s*$/i)) {
						line = RegExp.$1;
					}
					if (line.match(/^\s*\S*(?:\s+\S+)+\s*$/)) {
						markRefill(element, '<?= __('js:form:error:presence_spaces_used',IS_JAVASCRIPT) ?>');
						return false;
					}
					ok_lines.push(line);
				}
				element.value = ok_lines.join('\n');
				continue;
			}
			if (!valid) {
				continue;
			}
			if (value_is_empty) {
				// skip extra checks if value is empty
				continue;
			}
			switch (valid) {
			case 'lnglat':
				if (/^\s*-?\d+\s*$/.test(element.value)) {
					markRefill(element,'<?= __('js:form:error:incorrect_coordinate',IS_JAVASCRIPT) ?> (%NAME%)');
					return false;
				}
				break;

			case 'latitude':
			case 'longitude':
				const MINIMUM_COORDINATE = valid === 'latitude' ? MINIMUM_LATITUDE : MINIMUM_LONGITUDE;
				const MAXIMUM_COORDINATE = valid === 'latitude' ? MAXIMUM_LATITUDE : MAXIMUM_LONGITUDE;
				let form_coordinate;
				if (/^\s*-?\d+\s*$/.test(element.value)
				||	(form_coordinate = parseFloat(element.value)) < MINIMUM_COORDINATE
				||	 form_coordinate > MAXIMUM_COORDINATE
				) {
					markRefill(element,'<?= __('js:form:error:incorrect_longitude',IS_JAVASCRIPT) ?> (%NAME%, %VALUE%, %MIN%, %MAX%)', {
						'NAME':	 element.name,
						'VALUE': element.value,
						'MIN':	 MINIMUM_COORDINATE,
						'MAX':	 MAXIMUM_COORDINATE
					});
					return false;
				}
				break;

			// noinspection FallThroughInSwitchStatementJS
			case 'float':
				if (!/^\s*-?\d+(?:\.\d+)?\s*$/.test(element.value)) {
					markRefill(element,'<?= __('js:form:error:need_float',IS_JAVASCRIPT) ?> (%NAME%)');
					return false;
				}
				break;

			case 'working-url':
				let url_request = initrequest(true);
				if (!url_request) {
					return false;
				}
				url_request.open('POST', '/url.check', false);
				url_request.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
				url_request.send('URL=' + encodeURIComponent(element.value));
				if (url_request.status !== 200) {
					markRefill(element, url_request.responseText);
					return false;
				}
				break;

			case 'email':
			case 'working-email':
				if (!/^\s*[a-zA-Z0-9_.+-]+@[a-zA-Z0-9_.-]+\.[a-zA-Z]{2,}\s*$/.test(element.value)) {
					markRefill(element,'<?= __('js:form:error:need_email_address',IS_JAVASCRIPT) ?> (%NAME%)');
					return false;
				}
				if (valid === 'working-email'
				&&	typeof email_dones[element.value] === 'undefined'
				) {
					email_dones[element.value] = true;
					let ereq = initrequest(true);
					if (!ereq) {
						return false;
					}
					ereq.open('POST','/email.check', false);
					ereq.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
					ereq.send('EMAIL=' + encodeURIComponent(element.value));
					if (ereq.status !== 200) {
						markRefill(element,ereq.responseText ? ereq.responseText : '<?= __('js:form:error:need_working_email_address',IS_JAVASCRIPT) ?> (%NAME%)');
						return false;
					}
				}
				break;

			case 'number':
				if (valid === 'number'
				&&	!(element.getAttribute('step') ? /^\s*\d+(?:[,.]\d+)?\s*$/ : /^\s*\d+\s*$/).test(element.value)
				) {
					markRefill(element, '<?= __('js:form:error:need_number',IS_JAVASCRIPT) ?> (%NAME%)');
					return false;
				}
				if (value_is_empty) {
					// don't check min & max if value is empty
					break;
				}
				let min = element.min;
				let max = element.max;
				if (min || max) {
					let num = parseInt(element.value);
					if (min && num < min) {
						markRefill(element,'<?= __('js:form:error:need_minimum',IS_JAVASCRIPT) ?> (%NAME%)',{MIN:min});
						return false;
					}
					if (max && num > max) {
						markRefill(element,'<?= __('js:form:error:need_maximum',IS_JAVASCRIPT) ?> (%NAME%)',{MAX:max});
						return false;
					}
				}
				break;

			case 'intl-tel':
				if (!/^\s*\+/.test(element.value)) {
					markRefill(element, '<?= __('js:form:error:phone_must_be_international',IS_JAVASCRIPT) ?>');
					return false;
				}
			// noinspection FallThroughInSwitchStatementJS
			case 'tel':
				let min_length =
					!element.form.COUNTRYID
				||	parseInt(element.form.COUNTRYID.value) !== COUNTRYID_LUXEMBOURG
				?	MINIMUM_PHONE_LENGTH
				:	MINIMUM_PHONE_LENGTH_LUXEMBOURG;
				let phone_re = RegExp(String.raw`^+?\s*\d[\d\s-]{${min_length - 2},}\d`);
				if (!phone_re.test(element.value)) {
					markRefill(element, '<?= __('js:form:error:invalid_phone', IS_JAVASCRIPT) ?>');
					return false;
				}

				let clean_phone = element.value.replace(/[\s\-+]/, '');
				let phone_length = clean_phone.length;
				if (phone_length < min_length) {
					markRefill(element, '<?= __('js:form:error:phone_too_short', IS_JAVASCRIPT) ?>');
					return false;
				}
				if (phone_length >= NAXIMUM_PHONE_LENGTH) {
					markRefill(element, '<?= __('js:form:error:phone_too_long', IS_JAVASCRIPT) ?>');
					return false;
				}
				if (clean_phone.match(/^([\d-])$1*$/)) {
					markRefill(element, '<?= __('js:form:error:invalid_phone',IS_JAVASCRIPT) ?>');
					return false;
				}
				break;
			}
		}
	} catch(e) {
		catchLog(e);
		return false;
	}
	return true;
}
/** @param {HTMLFormElement} form
 *  @param {function} [func]
 *  @param {*} [arg]
 *  @return {boolean}
 */
function submitForm(form, func, arg) {
 	try {
		if (form.submitted) {
			return false;
		}
		if (!form.elements) {
			return true;
		}

		form.submitted = true;
		lightForm(form, true);

		let bad = false;
		let captcha = getobj('theCaptcha');
		if (captcha
//		&&	!grecaptcha.getResponse()
		&&	!grecaptcha?.enterprise?.getResponse?.()
		) {
			markRefill(captcha);
			alert(captcha.getAttribute('data-fillme'));
			addevent(captcha, 'mouseover', clearRefill);
			bad = true;
		}
		if (bad
		||	!validateElements(form.elements)
		||	func
		&&	!func.call(this, form, arg)
		) {
			lightForm(form, false);
			form.submitted = false;
			return false;
		}
		return true;
	} catch (e) {
		catchLog(e);
	}
	return false;
}


// when back button of browser is pressed, make sure the form is sendable again:
addevent(
	window,
	'pageshow',
	function(event) {
		if (!event.persisted) {
			return;
		}
		for (const form of document.forms) {
			if (form.submitted) {
				lightForm(form, false);
				form.submitted = false;
			}
		}
	}
);

function resetForm(form) {
	form.submitted = false;
	lightForm(form, false);
}

function lightForm(form, hide) {
	setclass(form, hide, 'light');
}

Pf.hideSubmits = function(form, hide) {
	for (const item of form.elements) {
		if (item.type === 'submit') {
			setdisplay(item, !hide);
		}
	}
};

function clearRefill(event) {
	remclass(this, 'refill');
	setdisplay('messages', false, true);
	remevent(this, event.type, clearRefill);
}

function markRefill(obj, errstr, map, type) {
	let form;
	if (obj.form) {
		form = obj.form;
	} else {
		form = obj;
		while ((form = form.parentNode) && !form.form) {}
	}
	obj.scrollIntoView({behavior: 'smooth'});
	Pf.scrollToMiddle(obj);
	addclass(obj, 'refill');
	addevent(obj, obj.nodeName === 'DIV' ? 'click' : ['change', 'keydown'], clearRefill);
	if (form) {
		resetForm(form);
	}
	Pf.focus(obj);
	if (!map) {
		map = {};
	}
	map.NAME = obj.name ? obj.name : obj.id;
	if (errstr) {
		showerrors(errstr, type ? type : ERR_TO_ALERT, map);
	}
}

function build_poststr(form,fields) {
	let utf8 = form.acceptCharset === 'utf-8';
	let poststr = '';
	for (const input of form.elements) {
		if (!input.name
		||	input.disabled
		) {
			continue;
		}
		// multi radio button as used in line-up forms
		// fix them for submission

		let use_name = input.name;
//		if (input.name.match(/^([A-Z_]+)_multi_[\da-f]+(\[\d*\])?$/)) {
//			use_name = RegExp.$1 + RegExp.$2;
//		}

		if (fields) {
			if (!function() {
				for (const field of fields) {
					if (field === use_name) {
						return true;
					}
				}
				return false;
			}()
			) {
				continue;
			}
		}
		switch (input.type) {
		case 'button':
			break;
		case 'checkbox':
		case 'radio':
			if (input.checked) {
				if (poststr) {
					poststr += '&';
				}
				poststr += use_name + '=' + (utf8 ? Pf.encodeForPost(input.value) : urlencode_utf8_to_windows1252(input.value)[0]);
			}
			break;
		default:
			if (poststr) {
				poststr += '&';
			}
			poststr += use_name + '=' + (utf8 ? Pf.encodeForPost(input.value) : urlencode_utf8_to_windows1252(input.value)[0]);
			break;
		}
	}
	return poststr;
}

function Pf_radioValue(radios) {
	if (!radios) {
		return null;
	}
	if (typeof radios.length === 'undefined') {
		// use top parent, because radios might not yet be added to document

		let p = radios.parentNode;
		while (p.parentNode) {
			p = p.parentNode;
		}
		let checked = p.querySelector('INPUT[name="'+radios.name+'"]:checked');
		return checked ? checked.value : null;
	}
	for (const radio of radios) {
		if (radio.checked) {
			return radio.value;
		}
	}
	return null;
}

String.prototype.repeat = function (n) {
	return new Array(n+1).join(this);
};

Pf.disable = function(id) {
	Pf.enable(id, false);
};

Pf.enable = function(arg, yes, ignore) {
	if (typeof arg === 'string') {
		arg = getobj(arg, !ignore);
	}
	if (typeof arg !== 'object') {
		return;
	}
	if (!(arg instanceof NodeList)
	&&	!(arg instanceof Array)
	&&	!(arg instanceof HTMLCollection)
	) {
		arg.disabled = !yes;
		if (arg.nodeName === 'FIELDSET') {
			['input','textarea'].forEach(function(tag) {
				const objs = arg.getElementsByTagName(tag);
				if (objs) {
					Pf.enable(objs, yes);
				}
			});
		}
		return;
	}
	for (const obj of arg) {
		if (!obj) {
			continue;
		}
		if (obj instanceof NodeList) {
			Pf.enable(obj, yes ,ignore);
		} else {
			obj.disabled = !yes;
		}
	}
};

function focus_and_to_end(container, body, intoview, wtop) {
	if (typeof body === 'string'
	&&	!(body = getobj(body))
	||	typeof container === 'string'
	&&	!(container = getobj(container))
	) {
		return;
	}
	if (!Browser.iOS) {
		move_to_end(body);
	}
	if (intoview) {
		container.scrollIntoView(Browser.iOS || wtop);
	}
	if (!Browser.iOS) {
		Pf.focus(body);
	}
}

function move_to_end(textarea) {
	if (textarea.setSelectionRange) {
		let textLen = textarea.textLength ? textarea.textLength : textarea.value.length;
		textarea.setSelectionRange(textLen,textLen);
	}
	textarea.scrollTop = textarea.scrollHeight;
}

function Pf_addSeparator(input,separator) {
	if (input.value.length
	&&	!input.value.match(/,\s*$/)
	) {
		input.value += separator;
	}
}

Pf.cleanEmail = function(inp) {
	if (inp.value.match(/^(.*?)\?__/)) {
		inp.value = RegExp.$1;
	}
};

Pf.addSelectEnter = function(start) {
	for (const select of start.getElementsByTagName('select')) {
		const form = select.form;
		if (select.PF_addSelectEntered) {
			continue;
		}
		select.PF_addSelectEntered = true;
		// keyup make sure the submit button actually gets included in post
		// keydown fires too early.
		addevent(select, 'keyup', function(event) {
			if (event.code !== 'Enter'
			&&	event.code !== 'NumpadEnter'
			) {
				// Not a return press? Then do not act.
				return;
			}
			event.stopPropagation();
			if (!form.onsubmit
			||	 form.onsubmit()
			) {
				form.submit();
			}
			//select.disabled = true;
		});
	}
};

addreadyevent(function(){
	Pf.addSelectEnter(document);
	for (/* @type {HTMLFormElement} */ const form of document.getElementsByTagName('form')) {
		if (form.method === 'get') {
			form.addEventListener('input', function() {
				form.submitted = false;
				lightForm(form, false);
			});
		}
	}
});

Pf_onEnterSubmit = function(event, submitname) {
	// FIXME: submit with right value, contact section example submits buttons: MOVE, ANSWER, RESPOND
};
