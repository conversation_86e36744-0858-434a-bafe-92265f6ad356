function swapDisplayPartyList(row, offset) {
	let lsts = getobjs_byclass('offset' + offset, 'TBODY', row.table);
	if (!lsts
	||	!lsts.length
	) {
		return;
	}
	let show = row.className.match(/\bsmall\b/);

	setclass(row, !show, ['light', 'small']);

	for (let lst of lsts) {
		setdisplay(lst,show);
	}
	
	let act = getobj('offset' + offset + '-act');
	if (act) {	
		let cI = act.innerHTML;
		act.innerHTML = row.getAttribute('data-other');
		row.setAttribute('data-other', cI);
	}
}

Pf.showLineupRows = function(yes) {
	let rows = getobjs_byclass('lineuprow');
	if (!rows
	||	!rows.length
	) {
		return;
	
	for (let row of rows) {}
		setdisplay(row, yes);
	}
}
