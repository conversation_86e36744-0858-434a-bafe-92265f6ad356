function openReplyForm(self) {
	let newform = getobj('newform');
	if (newform.getAttribute('data-inplace')) {
		let form = newform.firstChild;
		let nmsg = Pf.getParent(self,'nmsg');
		let data = getobjs_byclass('replyData', 'SPAN', nmsg);
		if (!data) {
			return;
		}
		data = data[0];
		form.Pf_data = data;
		form.Pf_snmsg = nmsg;
		
		let prntelem = data.getAttribute('data-parent-element');
		let lastid = getobj('lastid:parent:' + prntelem,true);
		if (!lastid) {
			return;
		}
		form['LAST_MESSAGEID'].value = lastid.getAttribute('data-value');
		let date = new Date;
		form['FORMSTAMP'].value = Math.floor(date.getTime() / 1000);
		form['REPLY_TO_MSGNO'].value = nmsg.getAttribute('data-msgno');
		
		let art = nmsg.parentNode.parentNode;
		if (art.nextSibling) {
			art.parentNode.insertBefore(newform,art.nextSibling);
		} else {
			art.parentNode.appendChild(newform);
		}
		newform.style.marginLeft = '2em';
		newform.Pf_parent = art;
		if (!art.Pf_children) {
			art.Pf_children = [];
		}
	} else {
		disableResponseLinks(true);
	}
	unhide(newform);
	focus_and_to_end(newform,'inputbody',true,false);
	if (Pf_fitTextarea) {
		Pf_fitTextarea('inputbody');
	}
}

function openDirectMessageForm(messageid) {
	setdisplay('newdirectmessage_' + messageid,true,true);
	setdisplay('admsg_' + messageid,false,true);
	let ibody = getobj('inputbody_' + messageid,true);
	if (!ibody) {
		return;
	}
	focus_and_to_end(ibody.form,ibody);
	if (Pf_fitTextarea) {
		Pf_fitTextarea(ibody);
	}
}

function disableResponseLinks(disable) {
	let opts = ['response_00','response_01','response_10','response_11'];
	for (let i = 0; i < opts.length; ++i) {
		let resp = getobj(opts[i]);
		if (!resp) {
			continue;
		}
		resp.disabled = disable;
		if (disable) {
			resp.className = 'light';
			resp.disabled = true;
			resp.onclickbackup = resp.onclick;
		} else {
			resp.className = 'unhideanchor';
			resp.onclick = resp.onclickbackup;
		}
	}
}

function submitReplyForm(formobj) { try {
	let infobj = formobj.Pf_data ? formobj.Pf_data : formobj;
	let element = infobj.getAttribute('data-parent-element');
	let elementid = infobj.getAttribute('data-parent-id');
	let id = infobj.getAttribute('data-commentid');
	let page = infobj.getAttribute('data-page');

	let inplace = formobj.getAttribute('data-inplace');

	let req;
	if (formobj.submitted
	||	!(req = initrequest(true))
	||	!submitForm(formobj)
	) {
		return false;
	}
	let poststr = build_poststr(formobj);
	if (page) poststr += '&PAGE='+page;
//	let replyerror = getobj('replyerror');
//	if (replyerror) replyerror.innerHTML = '';
	let tries = 3;
	req.open('POST', '/' + element + '/' + elementid + '/comment/new', true);
	req.setRequestHeader('X-NOW',Math.floor((new Date()).getTime()/1000));
	req.setRequestHeader('X-TRIES',tries);
	req.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
	let rkey = Pf.getRefreshKey(formobj);
	if (rkey) {
		req.setRequestHeader('X-REFRESHKEY',rkey);
	}
	req.onreadystatechange = function () {
		if (req.readyState !== 4) {
			return;
		}
		resetForm(formobj);
		if (req.getResponseHeader('X-NODATA')) {
			if (--tries) {
				submitReplyForm(formobj,element,elementid,page,tries);
				return;
			}
		}
		formobj.className = '';
		if (req.status !== 200) {
			showerrors(req,ERR_TO_ALERT);
			return;
		}
		if (!req.responseText) {
			return;
		}
		updateFollowLinks(formobj);
		if (req.getResponseHeader('X-SUBREAD')
		&&	updateSubscriptionCounter
		) {
			updateSubscriptionCounter(1);
		}
		let id = req.getResponseHeader('X-ID');
		let secret = req.getResponseHeader('X-SECRET');
		let currentstamp = req.getResponseHeader('X-CURRENTSTAMP');
		let newdiv = document.createElement('div');
		if (!newdiv) {
			alert('failed to create new div');
			return;
		}
//		newdiv.setAttribute('id','m'+id);
		newdiv.innerHTML = req.responseText;
		let newform = getobj('newform',true);
		if (!newform) {
			return;
		}
		let actual = newdiv.firstChild;
		newform.parentNode.insertBefore(actual,newform);
		if (newform.getAttribute('data-inplace')) {
			actual.style.marginLeft = '2em';
			actual.Pf_parent = newform.Pf_parent;
			actual.Pf_parent.Pf_children.push(actual);
//			addclass([actual.Pf_parent,actual],'light');
		}
		let forms = newform.getElementsByTagName('FORM');
		if (!forms.length) {
			alert('no forms?');
			return;
		}
		let form = forms[0];
		if (!id) {
			form.disabled = true;
			form.parentNode.removeChild(form);
		} else {
			if (form.LAST_MESSAGEID) {
				form.LAST_MESSAGEID.value = id;
			}
			if (form.FORMSTAMP) {
				form.FORMSTAMP.value = currentstamp;
			}
			if (form.SECRET) {
				form.SECRET.value = secret;
			}
			if (form.BODY) {
				form.BODY.value = '';
			}
			disableResponseLinks(false);
		}
		hide('newform');
		newdiv.scrollIntoView(false);
		if (form.ALLOW_SEEN) {
			Pf.seenQuote(form.Pf_snmsg,null,true);
		}
	}
	formobj.className = 'light';
	req.send(poststr);
	if (!poststr) {
		alert('poststr is leeg, er ging iets mis, neem even contact op met de helpdesk!');
	}
	return false;
} catch(e) {
	catchLog(e);
	return false;
}}

function openChangeForm(self) { try {
	let element = self.getAttribute('data-parent-element');
	let elementid = self.getAttribute('data-parent-id');
	let id = self.getAttribute('data-commentid');
	let page = self.getAttribute('data-page');
	let allow_seen = self.getAttribute('data-allow-seen');
	let show_parent = self.getAttribute('data-show-parent');

	let nmsgp = Pf.getParent(self,'nmsg');
	if (!nmsgp) {
		return;
	}
	nmsgp = nmsgp.parentNode;
	let formreq = initrequest(true);
	if (!formreq) {
		return;
	}
	let args = [];
	if (allow_seen) {
		args.push('ALLOW_SEEN=1');
	}
	if (show_parent) {
		args.push('SHOW_PARENT=1');
	}
	if (page) {
		args.push('PAGE='+page);
	}
	formreq.open('GET', '/' + element + '/' + elementid + '/comment/form/' + id + (args.length ? '?' + args.join(';') : ''),true);
	formreq.onreadystatechange = function () {
		if (formreq.readyState !== 4) {
			return;
		}
		if (formreq.status !== 200) {
			showerrors(formreq,ERR_TO_ALERT);
			return;
		} 
		if (!formreq.responseText) {
			alert('no content');
			return;
		}
		nmsgp.innerHTML = formreq.responseText;
		if (typeof makeResizable === 'function') {
			makeResizable(nmsgp);
		}
		if (typeof Pf_initUpLites === 'function') {
			Pf_initUpLites(nmsgp);
		}
		if (typeof Pf_initGrowToFits === 'function') {
			Pf_initGrowToFits(nmsgp);
		}
//		catch_ctrls(obj);
		focus_and_to_end(nmsgp,'inputbody_' + id,true,true);
	};
	formreq.send(null);
	return false;
} catch(e) {
	catchLog(e);
	return false;
}}

function submitChangeForm(formobj) { try {
	let element = formobj.getAttribute('data-parent-element');
	let elementid = formobj.getAttribute('data-parent-id');
	let id = formobj.getAttribute('data-commentid');
	let page = formobj.getAttribute('data-page');

	let req;
	if (formobj.submitted
	||	formobj.QUESTION
	&&	!checkPollPart(formobj)
	||	!(req = initrequest(true))
	||	!submitForm(formobj)
	) {
		return false;
	}
	let poststr = build_poststr(formobj);
	if (page) poststr += '&PAGE=' + page;
//	let replyerror = getobj('replyerror'+id);
//	if (replyerror) replyerror.innerHTML = '';
	let tries = 3;
	req.open('POST', '/' + element + '/' + elementid + '/comment/change/' + id, true);
	req.setRequestHeader('X-NOW',Math.floor((new Date()).getTime()/1000));
	req.setRequestHeader('X-TRIES',tries);
	let rkey = Pf.getRefreshKey(formobj);
	if (rkey) {
		req.setRequestHeader('X-REFRESHKEY',rkey);
	}
	req.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
	req.onreadystatechange = function () {
		if (req.readyState !== 4) {
			return;
		}
		resetForm(formobj);
		if (req.getResponseHeader('X-NODATA')) {
			if (--tries) {
				submitChangeForm(formobj,element,elementid,id,page,tries);
				return;
			}
		}
		formobj.className = '';
		if (!req.responseText) {
			return;
		}
		if (req.status !== 200) {
/*			if (replyerror)
				replyerror.innerHTML = req.responseText;
			else 	alertMessage(req.responseText);
			formobj.scrollIntoView();*/
			showerrors(req,ERR_TO_ALERT);
			return;
		}
		updateFollowLinks(formobj);
		formobj.parentNode.innerHTML = req.responseText;
	};
	formobj.className = 'light';
	req.send(poststr);
	if (!poststr) {
		alert('poststr is leeg, er ging iets mis, neem even contact op met de helpdesk!');
	}
	return false;
} catch(e) {
	alert(e.message);
	return false;
}}

function updateFollowLinks(formobj) {
	if (typeof changeFollowLinks == 'undefined') {
		return;
	}
	if (formobj.FOLLOW) {
		changeFollowLinks(formobj.FOLLOW.checked);
		if (formobj.WASFOLLOWED) {
			formobj.WASFOLLOWED.value = formobj.FOLLOW && formobj.FOLLOW.checked ? '1' : '0';
		}
	}
}

function commentSetAccept(self, elementname, elementid, id, action, hook) {
	let remove = (action === 'remove');
	if (remove) {
		let suremove = getobj('suremove', true);
		if (!suremove
		||	!confirm(suremove.value)
		) {
			return;
		}
	}
	let setreq = initrequest(true);
	if (!setreq) {
		return;
	}
	let nmsg;
	if (!hook) {
		nmsg = Pf.getParent(self, 'nmsg');
		if (!nmsg) {
			return;
		}
	}
	setreq.open('POST','/'+elementname+'/'+elementid+'/comment/'+action+'/'+id,true);
	let rkey = Pf.getRefreshKey(self);
	if (rkey) {
		setreq.setRequestHeader('X-REFRESHKEY',rkey);
	}
	setreq.onreadystatechange = function() {
		if (setreq.readyState !== 4) {
			return;
		}
		if (setreq.status !== 200) {
			showerrors(setreq,ERR_TO_ALERT);
			return;
		}
		if (remove) {
			hideobj(nmsg.parentNode);
			return;
		}
		let deny = action == 'deny';
		let otheraction = deny ? 'accept' : 'deny';
		let otheractionname = setreq.responseText;
		if (otheractionname) {
			self.innerHTML = otheractionname;
			self.onclick = function() {
				commentSetAccept(self,elementname,elementid,id,otheraction,hook);
			};
		}
		if (hook) {
			hook.call(self,deny);
		} else {
			setclass(nmsg.parentNode,deny,'light');
		}
	};
	setreq.send(null);
	return false;
}

Pf.showPermalink = function(self) {
	let pl = Pf.permaLink;
	if (!pl) {
		pl = document.createElement('div');
		pl.className = 'permalink';
		addevent(pl,'click',function(event){
			pl.ALLsel = !pl.ALLsel;
			setTimeout(function(){
				Pf.selectNode((pl.ALLsel || !pl.lastChild) ? pl : pl.lastChild);
			},1);
		});
		let bd = getobj('bodycontainer');
		(bd ? bd : document.body).appendChild(pl);
		Pf.permaLink = pl;
	}
	if (pl.open) {
		pl.style.visibility = 'hidden';
		remclass(pl.open,'light');
		pl.open = null;
		if (pl.selHREF == self.href) {
			return false;
		}
	}
	let pos = findPos(self);
	// remove Google tracking:
	pl.selHREF = self.href.replace(/[\?&]+_gl=[^&]*/, '');
	pl.innerHTML = pl.selHREF.match(/^(.*)\/(.*?)$/) ? '<span>'+RegExp.$1+'</span>/<span>'+RegExp.$2+'</span>' : pl.selHREF;
	pl.style.visibility = 'visible';
	pl.style.left = (pos[0] - pl.clientWidth - 6) + 'px';
	pl.style.top = (pos[1] + 18) + 'px';
	pl.ALLsel = true;
	Pf.selectNode(pl);
	addclass(self,'light');
	pl.open = self;
	return false;
}

Pf.getRefreshKey = function(self) {
	while (self = self.parentNode) {
		if (self.className === 'section') {
			return self.getAttribute('data-refreshkey');
		}
	}
	return null;
}
