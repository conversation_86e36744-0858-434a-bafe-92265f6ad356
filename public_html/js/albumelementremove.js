let isRemoving = false;
let removeThese;

function changeRemoving(me) {
	setdisplay('removebutton', isRemoving = isRemoving ? false : true);
	me.className = (isRemoving ? 'selected unhideanchor' : 'unhideanchor');
	if (isRemoving) {
		removeThese = [];
	} else {
		for (let removeThis of removeThese) {
			remclass(removeThis, 'remove-element');
		}
		delete removeThese;
	}
	if (typeof isMoving !== 'undefined'
	&&	isMoving
	&&	isRemoving
	) {
		changeMoving(getobj('movelink'));
	}
}

function addElement(self, id) {
	if (!isRemoving) {
		return;
	}
	let found = false;
	let useid = 'ae' + id;
	for (let elem of removeThese) {
		let elemid = elem.parentNode.id;
		if (useid === elemid) {
			found = true;
			removeThese.splice(i, 1);
			remclass(elem, 'remove-element');
			return false;
		}
	}
	removeThese.push(self);
	addclass(self, 'remove-element');
	return false;
}

function removeElements(form, none, sure) {
	if (!removeThese
	||	!removeThese.length
	) {
		alert(none);
		return false;
	}
	if (!confirm(sure)) {
		return false;
	}
	for (let removeThis of removeThese) {
		if (!removeThis.parentNode.id.match(/^ae(\d+)/)) {
			return false;
		}
		let elemid = RegExp.$1;
		let newinput = document.createElement('input');
		if (!newinput) {
			return false;
		}
		newinput.setAttribute('type', 'hidden');
		newinput.setAttribute('name', 'ALBUMELEMENTID[]');
		newinput.setAttribute('value', elemid);
		form.appendChild(newinput);
	}
	return true;
}
