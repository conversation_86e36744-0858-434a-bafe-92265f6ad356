Pf.showUserList = function(info, tiles) {
	if (!tiles === (info.className !== 'tiled')) {
		return;
	}
	let cacheid = info.getAttribute('data-cacheid');
	let dst = getobj('userlist-' + cacheid, true);
	if (!dst) {
		return;
	}
	if (info.oldData) {
		let tmp = dst.firstChild;
		tmp.parentNode.removeChild(tmp);
		dst.appendChild(info.oldData);
		info.oldData = tmp;
		if (info.getAttribute('data-remember')) {
			do_inline('GET', '/userlist.obj?SETTING=' + (tiles ? 'tiles' : 'list'), 'NULL_ACT');
		}
		info.className = tiles ? 'tiled' : 'listed';
		return;
	}

	let hash = info.getAttribute('data-hash');

	addclass(dst, 'light');

	do_inline('GET', '/userlist.obj?CACHEID=' + cacheid + ';HASH=' + hash + (tiles ? ';SHOWIMGS' : ''), 'ACT', function(req) {
		remclass(dst,'light');
		if (req.status !== 200) {
			showerrors(req, ERR_TO_ALERT);
			return;
		}
		if (!req.responseText) {
			return;
		}
		info.className = tiles ? 'tiled' : 'listed';
		info.oldData = dst.firstChild;
		dst.removeChild(info.oldData);
		dst.innerHTML = req.responseText;
		if (tiles
		&&	Pf_initializeLazies
		) {
			Pf_initializeLazies(dst);
		}
	});
}
