Pf.hiliteBuddyRequest = function(self,accept,over) {
	var p = Pf.getParent(self,'bddyrqst');
	if (!p) return;
	if (over) {
		addclass(p,accept ? 'hilited-green' : 'hilited-red');
	} else {
		remclass(p,['hilited-red','hilited-green']);
	}
}
Pf.hiliteAllBuddies = function(self,accept,over) {
	var p = self.parentNode.parentNode.previousSibling;
	if (over) {
		addclass(p,accept ? 'hilited-green' : 'hilited-red');
	} else {
		remclass(p,['hilited-red','hilited-green']);
	}
}
Pf.alterBuddyRequest = function(form,act) {
	if (form.submitted) return false;
	form.submitted = true;
	return !do_inline('POST',form.action,'ACT',function(req){
		form.submitted = false;
		if (req.status == 204) {
			return;
		}
		if (req.status !== 200) {
			showerrors(req,ERR_TO_ALERT);
			return;
		}
		if (act == 'seen') {
			var p = Pf.getParent(form,'seenParent');
			p.parentNode.removeChild(p);
			Pf.removeNode(['newbudmenu','newbudmenu_footer'],true);
			var breqs = getobj('allbuds');
			if (!breqs) return;
			var breq;
			for (breq = breqs.firstChild; breq; breq = breq.nextSibling) {
				breq.removeAttribute('data-new');
				remclass(breq,'lighter-hilited');
			}
			var top = getobj('top');
			if (top) top.scrollIntoView(true);
			showerrors(req,ERR_TO_HTML);
			return;
		}
		var p = Pf.getParent(form,'bddyrqst');
		form.parentNode.innerHTML = req.responseText;
		if (p.getAttribute('data-new')) {
			var cnt = getobj('newbudcnt');
			if (cnt) {
				var newcnt = parseInt(cnt.innerHTML)-1;
				if (!newcnt) {
					Pf.removeNode(['newbudmenu','newbudmenu_footer'],true);
				} else {
					cnt.innerHTML = newcnt;
					Pf.update_totalmsg(-1)
				}
			}
			var cnt_foot = getobj('newbudcnt_footer');
			if (cnt_foot) {
				cnt_foot.innerHTML = newcnt;
			}
		}
		remclass(p,'lighter-hilited');
		addclass(p,act == 'accept' ? 'hilited-green' : 'hilited-red');
	},'AJAX=1');
}
