function Pf_refreshUploads() {
	let todos = getobjs_byclass('refreshimg', 'DIV');
	if (!todos?.length) {
		return;
	}
	let checks = [];
	let done = {};
	for (const todo of todos) {
		let upimgid = todo.getAttribute('data-upimgid');
		if (upimgid
		&&	!todo.getAttribute('data-refresh-init')
		) {
			setattr(todo, 'data-refresh-init', true);
			if (typeof done[upimgid] === 'undefined') {
				done[upimgid] = true;
				checks.push(upimgid);
			}
		}
	}
	let checkInt = setInterval(function(){
		const upimgidstr = checks.join(',');
		do_inline('POST', '/readyimgs.obj', 'ACT', function(req, self, obj) {
			if (req.status !== 200) {
				clearInterval(checkInt);
				return;
			}
			let new_checks = [];
			let removes = [];
			for (const img of todos) {
				let upimgid = img.getAttribute('data-upimgid');
				if (!upimgid) {
					continue;
				}
				upimgid = parseInt(upimgid);
				console.log(`checking status of upimgid ${upimgid}`);
				if (obj[upimgid]
				&&	obj[upimgid] !== 'busy'
				) {
					const test = document.body.hasAttribute('data-home-thomas');
					console.log(`upimgid ${upimgid} is not busy anymore`);
					removes.push(img);
					let uimg;
					if (!(uimg = img.nextSibling)) {
						console.warn('img div has no sibling');

					} else if (obj[upimgid] === 'ready') {
						console.log(`upimgid ${upimgid} is ready`);
						uimg.onload = '';
						const rnd = Math.random();
						const sep = (-1 === uimg.src.indexOf('?') ? '?': ';');
						if (test) {
							uimg.srcset = uimg.srcset.replaceAll(/(?<=\S+)\s+(?=\d+(?:\.\d+)?x)/g, `${sep}definitive;${rnd} `);
						} else {
							uimg.srcset = uimg.srcset.replaceAll(/(\S+)\s+(\d+(?:\.\d+)?x)/g, `$1${sep}definitive;${rnd}$2`);
						}
						uimg.src += `${sep}definitive;${rnd}`;
					} else if (typeof obj[upimgid] === 'number') {
						let new_upimgid = obj[upimgid];
						console.log(`upimgid ${upimgid} has been replaced by ${new_upimgid}`);
						if (test) {
							uimg.srcset = uimg.srcset.replaceAll(/(?<=_)${upimgid}(?=[@/.])/, 'g', new_upimgid);
						} else {
							uimg.srcset = uimg.srcset.replaceAll(/_(\d+)([@./])/g, `_{$new_upimgid}$2`);
						}
						if (test) {
							uimg.src = uimg.src.replace(RegExp(`(?<=_)${upimgid}(?=[@/.])`), new_upimgid);
						} else {
							uimg.src = uimg.src.replace(/_(\d+)([@./])/, `_${new_upimgid}$2`);
						}
					}
				} else {
					new_checks.push(upimgid);
				}
			}
			for (let remove of removes) {
				Pf.removeNode(remove);
			}
			if (!new_checks.length) {
				console.log('checked all uploadimages, stopping');
				clearInterval(checkInt);
				return;
			}
			console.log('still need to check: ', new_checks);
			checks = new_checks;
		}, `UPIMGIDSTR=${upimgidstr}`);
	}, 1000);
}

addreadyevent(Pf_refreshUploads);
