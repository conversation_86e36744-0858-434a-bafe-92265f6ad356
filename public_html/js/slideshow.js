let pdiv;
let photoid;

Pf.startSlideShow = function(self){
	if (!Pf.slideshow) {
		alert('NO SLIDESHOW');
		return;
	}

	document.body.style.overflow = 'hidden';

	let ss = JSON.parse(Pf.slideshow);

	let cmts = self.getAttribute('data-comments');

	ss.last = ss.images.length - 1;
	ss.current = 0;

	let sizes = [
		[1920,1280],
		[1500,1000],
		[1200,800],
		[960,640],
	];

	let add_img = function() {
		let img = document.createElement('img');

		addevent(img, 'load', function(event) {
			Pf_photoLoad('photo_load_overlay', photoid);
		});

		// imginfo:
		// 0: photoid
		// 1: widthest
		// 2: heightest
		// 3: extraid

		let imginfo = ss.images[ss.current];
		photoid = imginfo[0];
		let mw = imginfo[1];
		let mh = imginfo[2];

		let win = getWindowInfo();

		if (win.w < mw) {
			mh = win.w * mh / mw;
			mw = win.w;
		}
		if (win.h < mh) {
			mw = win.h * mw / mh;
			mh = win.h;
		}

		mw = Math.round(.9 * mw);
		mh = Math.round(.9 * mh);

		img.style.width = mw + 'px';
		img.style.height = mh + 'px';

		let loadw = sizes[0][0];
		let loadh = sizes[0][1];
		for (let i = 1; i < sizes.length; ++i) {
			if (sizes[i][0] * sizes[i][1] < mw * mh) {
				break;
			}
		}
		img.className = 'abs';
		img.style.left = ((win.w - mw) / 2) + 'px';
		img.style.top = ((win.h - mh) / 2) + 'px';
		img.style.visibility = 'hidden';

		if (mw < mh) {
			let swap = loadw;
			loadw = loadh;
			loadh = swap;
		}

		img.src = ss.base_url + loadw + 'x' + loadh + '/' + imginfo[0] + '.jpg';

		img.onload = function() {
			this.style.visibility = 'visible';
			if (ss.img) {
				Pf.removeNode(ss.img);
			}
			ss.img = this;
		};
		pdiv.appendChild(img);
	};
	let fill_cntr = function() {
		let str = (ss.current + 1) + ' / ' + (ss.last+1);
		let cmtcnt = ss.images[ss.current][3];
		if (cmtcnt) {
			photoid = ss.images[ss.current][0];
			str += ', <a href="/photo/' + photoid + '">' + cmts + ': ' + cmtcnt + '</a>';
		}
		ss.cntr.innerHTML = str;
	};

	pdiv = document.createElement('div');
	pdiv.className = 'abs slideshow bgov darkbg center';
	pdiv.style.position = 'fixed';

	let closer_div = document.createElement('div');
	closer_div.innerHTML = self.getAttribute('data-closer');
	pdiv.appendChild(closer_div);

	add_img();

	let cntr = document.createElement('div');
	cntr.className = 'ovbg';
	cntr.style.left = '.5em';
	cntr.style.bottom = '.5em';
	ss.cntr = cntr;
	fill_cntr();

	pdiv.appendChild(cntr);

	document.body.appendChild(pdiv);

	addevent(document, 'click', Pf.slideShowClick);
	Pf.slideShowClick = function(event) {
		event.stopPropagation();
		event.preventDefault();

		let left_or_right = event.offsetX / window.innerWidth;
		if (left_or_right < 0.5) {
			if (ss.current <= 0) {
				return false;
			}
			--ss.current;
		} else {
			if (ss.current > ss.last) {
				return false;
			}
			++ss.current;
		}
		add_img();
		fill_cntr();
		return false;
	};

	Pf.slideShowKeyUp = function(event) {
		event.stopPropagation();
		event.preventDefault();
		switch (event.code) {
		case 'Escape':
		case 'Enter':
		case 'NumpadEnter':
		case 'ArrowLeft':
		case 'ArrowRight':
		case 'Space':
			return false;
		}
		return true;
	};


	Pf.slideShowKeyDown = function(event) {
		event.stopPropagation();
		event.preventDefault();
		switch (event.code) {
		case 'Escape':
		case 'Enter':
		case 'NumpadEnter':
			photoid = ss.images[ss.current][0];
			Pf.stopSlideShow();
			return false;
		case 'ArrowLeft':
			if (ss.current <= 0) {
				return false;
			}
			--ss.current;
			break;
		case 'ArrowRight':
			if (ss.current > ss.last) {
				return false;
			}
			++ss.current;
			break;
		default:
			return true;
		}
		add_img();
		fill_cntr();
		return false;
	};

	addevent(document, 'click', Pf.slideShowClick);
	addevent(document, 'keyup', Pf.slideShowKeyUp);
	addevent(document, 'keydown', Pf.slideShowKeyDown)
}

Pf.stopSlideShow = function() {
	document.body.style.overflow = 'inherit';
	location.hash = '#ph' + photoid;
	Pf.removeNode(pdiv);
	remevent(document, 'click', Pf.slideShowClick);
	remevent(document, 'keyup', Pf.slideShowKeyUp);
	remevent(document, 'keydown', Pf.slideShowKeyDown)

}
