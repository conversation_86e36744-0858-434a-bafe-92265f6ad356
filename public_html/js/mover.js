function moveSubber() {
	let s  = getobj('subbar');
	let cc = getobj('contentcol');

	if (!s || !cc) {
		return;
	}

	let have = cc.clientWidth;
	let need = s.getAttribute('data-width') || 970;

	let source_name = (have >= need ? 'wide' : 'narrow') + 'subbar';
	let store_name =  (have >= need ? 'narrow' : 'wide') + 'subbar';

	let source = getobj(source_name);
	let store  = getobj(store_name);

	if (!source || !store) {
		return;
	}

	if (!store.contains(s)) {
		let source_first = source.firstChild;
		let store_first  =  store.firstChild

		store.appendChild(source_first);

		if (store_first) {
			source.appendChild(store_first);
		}
	}
}

addreadyevent(function() {
	addevent(window, 'resize', moveSubber);
	moveSubber();
});
