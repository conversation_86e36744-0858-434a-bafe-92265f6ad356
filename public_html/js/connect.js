function Pf_loadconnectbox(self, element, id) {
	let obj = getobj('connectbox-' + element + '-' + id, true);
	if (!obj) {
		return true;
	}
	// Firefox workaround, does not understand 'obj.src' somehow
	let src = obj.getAttribute('src');
	let show = false;
	if (src) {
		obj.removeAttribute('src');
	} else {
		show = true;
		obj.setAttribute('src', '/connect?ELEMENT=' + element + ';ID=' + id);
	}
	if ( self
	||	(self = obj.Pf_connect)
	) {
		setclass(self, show, 'light');
		obj.Pf_connect = self;
	}
	setdisplay(obj, show);
	if (!show) {
		setdisplay(obj.previousSibling, false);
	}
	if (show) {
		obj.PF_HANDLESC = function(event) {
			if (event.code === 'Escape') {
				Pf_loadconnectbox(self, element, id);
			}
		};
		window.addEventListener('keydown', obj.PF_HANDLESC);
	} else {
		window.removeEventListener('keydown', obj.PF_HANDLESC);
	}
}

function Pf_connectFrameLoaded(inline, element, id, show_back) {
	let frame = getobj('connectbox-' + element + '-' + id, true);
	if (!frame) {
		return;
	}
	if (!inline) {
		frame.style.height = frame.contentWindow.document.body.clientHeight + 'px';
	}
	setdisplay(frame.previousSibling, show_back);
}
