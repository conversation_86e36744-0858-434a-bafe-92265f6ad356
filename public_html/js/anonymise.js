function check_counts(self, id) {
	let body = getobj('anonymous-body',true);
	let amount_info = getobj('amount-info', true);
	let req = initrequest(true);
	if (!body || !amount_info || !req) {
		return;
	}
	fetch ('/anonymize-info.obj', {
		method: 'POST',
		headers: {'Content-Type': 'application/x-www-form-urlencoded'},
		body:	'MESSAGEID=' + id +
				'&MAKE_ANONYMOUS=0' +
				'&ANONYMOUS_BODY=' + encodeURIComponent(body.value)
	}).then(response => {
		if (!response.ok) {
			alert(Pf.genericError);
			return;
		}
		return response.text();
	}).then(text => {
		unhide('anonymize-done');
		amount_info.innerHTML = text;
	});
}
