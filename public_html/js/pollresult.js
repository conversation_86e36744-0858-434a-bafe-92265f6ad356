var pollvote_HTML_content;
var pollresult_HTML_content = [];
function showPollResult(pollid,extra) {
	if (typeof pollresult_HTML_content != 'undefined'
	&&	typeof pollresult_HTML_content[extra] != 'undefined'
	) {
		var obj = getobj('pollspace');
		if (obj) {
			obj.innerHTML = pollresult_HTML_content[extra];
			return false;
		}
	}
	let req = initrequest();
	if (!req) {
		return true;
	}
	req.open('GET','/poll/result/' + pollid + extra, true);
	req.onreadystatechange = function () {
		if (req.readyState !== 4) {
			return;
		}
		switch (req.status) {
		case 404:	req.responseText = '<span class="error">Actie kon niet uitgevoerd worden!</span>'; break;
		default:	var fillname = 'pollerror_' + pollid; break
		case 200:	var fillname = 'pollspace_' + pollid; break;
		}
		if (fillname) {
			var obj = getobj(fillname);
			if (obj) {
				if (req.status == 200) {
					pollvote_HTML_content = obj.innerHTML;
					pollresult_HTML_content[extra] = req.responseText;
				}
				obj.innerHTML = req.responseText;
			}
		}
	};
	req.send(null);
	return false;
}
function showPollVoteForm() {
	var obj = getobj('pollspace');
	if (obj) {
		obj.innerHTML = pollvote_HTML_content;
		return false;
	}
	return true;
}
