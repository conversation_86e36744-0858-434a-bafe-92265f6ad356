// original: https://github.com/n0s/itunesdetect

var _detect = {
	activeX: function(){
		var b = document.getElementById("iTunesDetectorIE");
		var a = false;
		if ((b != null) && (typeof b !== "undefined")) {
			if (typeof(b.IsITMSHandlerAvailable) != "undefined") {
				a = b.IsITMSHandlerAvailable;
			}
			if ((a == null) || (typeof a === "undefined")) {
				a = false
			}
		}
		return a
	},
	plugin: function(){
		if (navigator.plugins && navigator.plugins.length > 0) {
			for (var b = 0; b < navigator.plugins.length; b++) {
				var c = navigator.plugins[b];
				var d = c.name;
				if (d.indexOf("iTunes Application Detector") > -1) {
					return true;
				}
			}
		}
		return false;
	},
	iTunes: function(){
		return	navigator.userAgent.match(/Macintosh|iP(?:[ao]d|hone)/)
		||	(_detect.isIE() ? _detect.activeX() : _detect.plugin());
	},
	isIE: function() {
		return '\v' == 'v';
	}
};
