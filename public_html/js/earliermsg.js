function importEarlierMessage(self) {
	let fmsgobj = getobj('firstmessageid', true);
	if (!fmsgobj) {
		return;
	}
	let dreq = initrequest(true);
	if (!dreq) {
		return;
	}
	let oldClassName = self.className;
	dreq.open('GET','/directmessage/before/' + fmsgobj.value, true);
	dreq.onreadystatechange = function() {
		if (dreq.readyState !== 4) {
			return;
		}
		self.className = oldClassName;
		switch (dreq.status) {
		case 404:
			alert(self.getAttribute('data-no-more'));
			let addmsgobj = getobj('addmsg');
			if  (addmsgobj) {
				addmsgobj.onclick = null;
				addmsgobj.className = 'line-through';
			}
			return;

		case 200:
			let messageid = dreq.getResponseHeader('X-COMMENTID');
			if (!messageid) {
				console.warn('no X-COMMENTID response header!');
				return;
			}
			let newdiv = document.createElement('div');
			newdiv.className = 'clear';

			let ibefore = getobj('insertbefore');
			if (!ibefore) {
				
				return;
			}
			let fmsgobj = getobj('firstmessageid');
			if (!fmsgobj) {
				alert('firstmessageid niet gevonden!');
				return;
			}
			newdiv.innerHTML = dreq.responseText;
			ibefore.parentNode.insertBefore(newdiv,ibefore);
			fmsgobj.value = messageid;
			location.hash = 'm'+messageid;
			return;

		default:
			alert('Er ging wat mis bij het ophalen van een eerder bericht!');
			return;
		}
	}
	self.parentNode.removeChild(self);
	dreq.send(null);
}

