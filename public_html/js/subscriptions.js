function changeSubscription(self,element,id) {
	let req = initrequest(true);
	if (!req) return;
	var subscribed = self.getAttribute('data-subscribed');
	req.open('POST','/'+element+'/'+id+'/'+(subscribed ? 'unsubscribe' : 'subscribe'),true);
	req.onreadystatechange = function() {
		if (req.readyState !== 4) {
			return;
		}
		if (req.status !== 200) {
			alertMessage(req.responseText ? req.responseText : 'HTTP status ('+req.status+')');
			return;
		}
		changeFollowLinks(!subscribed,self);
	};
	req.send(null);
}
function changeFollowLinks(subscribed,self) {
	var links = self ? [self,self.id == 'follow_0' ? 'follow_1' : 'follow_0'] : ['follow_0','follow_1'];
	for (let link of links) {
		if (typeof link === 'string') {
			link = getobj(link);
			if (!link) continue;
		}
		if (subscribed) {
			if (link.getAttribute('data-subscribed')) continue;
			setattr(link,'data-subscribed',true);
		} else {
			if (!link.getAttribute('data-subscribed')) continue;
			setattr(link,'data-subscribed',false);
		}
		var tmp = link.innerHTML;
		link.innerHTML = link.getAttribute('data-other');
		link.setAttribute('data-other',tmp);
	}
	var follow = getobj('follow');
	if (!follow) return;
	follow.checked = subscribed;
	follow.onclick();
}
Pf.remSub = function(self,element,id,unseen) { try {
	var t = Pf.getParentNode(self,'TABLE');
	if (!t) {
		return;
	}
	if (!confirm(t.getAttribute('data-confirm'))) {
		return;
	}
	let req = initrequest(true);
	if (!req) {
		return;
	}
	req.open('POST', '/'+element+'/'+id+'/unsubscribe', true);
	req.onreadystatechange = function() {
		if (req.readyState !== 4) {
			return;
		}
		if (req.status !== 200) {
			alertMessage(req.responseText ? req.responseText : 'HTTP status (' + req.status + ')');
			return;
		}
		let row = Pf.getParentNode(self, 'TR');
		let tbody = row.parentNode;
		tbody.removeChild(row);
		if (tbody.rows.length === 1) {
			let table = tbody.parentNode;
			table.parentNode.removeChild(table);
		}
		if (unseen) {
			updateSubscriptionCounter(1);
		}
	};
	req.send(null);
} catch(e) {
	catchLog(e);
}}

function catchupSubscriptions(self,element) {
	let req = initrequest(true);
	if (!req) {
		return;
	}
	let table = self.parentNode.parentNode.parentNode.parentNode;
	req.open('POST','/' + (element ? element : 'all') + '.catchup',true);
	req.onreadystatechange = function() {
		if (req.readyState !== 4) {
			return;
		}
		if (req.status !== 200) {
			remclass(table, 'light');
			alertMessage(req.responseText ? req.responseText : 'HTTP status ('+req.setatus+')');
			return;
		}
		let remcnt;
		if (element) {
			table.parentNode.removeChild(table);
			remcnt = table.rows.length - 1;
		} else {
			let anchors = table.getElementsByTagName('A');
			for (let anchor of anchors) {
				if (anchor.className === 'unseen') {
					anchor.parentNode.parentNode.removeChild(anchor.parentNode);
				} else if (anchor.className.match(/\bunseen\b/)) {
					anchor.parentNode.removeChild(anchor);
				}
			}
			self.parentNode.removeChild(self);
			let unseencnt = getobj('totalunseen');
			remcnt = unseencnt ? unseencnt.value : 0;
			remclass(table, 'light');
		}
		if (remcnt) {
			updateSubscriptionCounter(remcnt);
		}
	};
	if (table) {
		addclass(table,'light');
	}
	req.send(null);
}

function updateSubscriptionCounter(remcnt) {
	let subcnt = getobj('subcnt');
	if (!subcnt) {
		return;
	}
	let  remaining = parseInt(subcnt.innerHTML) - remcnt;
	if (remaining > 0) {
		subcnt.innerHTML = remaining;
	} else {
		subcnt.parentNode.parentNode.removeChild(subcnt.parentNode);
	}
	Pf.update_totalmsg(-remcnt);
	let subcnt_foot = getobj('subcnt_footer');
	if (subcnt_foot) {
		subcnt_foot.innerHTML = remaining;
	}
}

function hideUnseen(self, uniq, row) {
	updateSubscriptionCounter(1);
	if (!row) {
		let rems;
		if (self.id.match(/^uns([ab])(\d+)$/)) {
			rems = [self];
			rems.push('uns' + (RegExp.$1 === 'a' ? 'b' : 'a') + RegExp.$2);
		} else {
			rems = ['unsa' + uniq, 'unsb' + uniq];
		}
		for (let rem of rems) {
			if (typeof rem === 'string') {
				rem = getobj(rem);
				if (!rem) {
					continue;
				}
			}
			rem.parentNode.removeChild(rem);
		}
	} else {
		var row = self.parentNode.parentNode;
		row.parentNode.removeChild(row);
	}
}

function clickSub(self) {
	// add 'n' here, because actual id+n does not exist, only for javascript enabled browsers
	if (self.hash
	&&	!self.getAttribute('self-hashed')
	) {
		self.hash += 'n';
		setattr(self, 'self-hashed', true);
	}
}
