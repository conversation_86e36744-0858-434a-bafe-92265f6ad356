function karma(thumb, amount) {
	if (thumb.busy) {
		return;
	}
	thumb.busy = true;
	let req = initrequest(true);
	if (!req) {
		return;
	}
	let info = thumb.parentNode.getAttribute('data-info');
	if (!info.match(/^([a-z_]+):(\d+):(\d+)$/)) {
		return;
	}
	let element = RegExp.$1;
	let id = RegExp.$2;
	let crc = RegExp.$3;
	let karma = parseInt(thumb.parentNode.getAttribute('data-karma'));
	let remove =
		amount > 0 && karma > 0
	||	amount < 0 && karma < 0;

	let poststr = 'ELEMENT=' + encodeURIComponent(element) + '&ID=' + encodeURIComponent(id) + '&CRC=' + crc;
	if (amount < 0) {
		poststr += '&NEGATIVE=true';
	}
	if (remove) {
		poststr += '&REMOVE=true';
	}
	req.open('POST','/karma.act',true);
	req.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
	req.onreadystatechange = function() {
		if (req.readyState !== 4) {
			return;
		}
		if (req.status === 200) {
			var karmaCNT;
			if (remove) {
				thumb.parentNode.setAttribute('data-karma',0);
				thumb.src = thumb.src.replace(/_active/,'_inactive');
				thumb.className = 'inactive';
				karmaCNT = thumb.previousSibling;
				amount = -amount;
			} else {
				thumb.parentNode.setAttribute('data-karma',amount);
				thumb.src = thumb.src.replace(/_inactive/,'_active');
				thumb.className = 'active';
				karmaCNT = thumb.previousSibling;
				if (amount > 0) {
					var otherKarmaCNT = thumb.nextSibling;
					var otherKarmaTHMB = otherKarmaCNT.nextSibling;
				} else {
					var otherKarmaTHMB = karmaCNT.previousSibling;
					var otherKarmaCNT = otherKarmaTHMB.previousSibling;
				}
				if (!otherKarmaTHMB.src.match(/_inactive/)) {
					otherKarmaTHMB.src = otherKarmaTHMB.src.replace(/_active/,'_inactive');
					var val = parseInt(otherKarmaCNT.innerHTML);
					val += amount;
					otherKarmaCNT.innerHTML = val ? (val > 0 ? '+'+val : val) : '';
				}
			}
			var val = parseInt(karmaCNT.innerHTML);
			if (!val) val = 0;
			val += amount;
			karmaCNT.innerHTML = val ? (val > 0 ? '+'+val : val) : '';
		}
		thumb.busy = false;
	};
	req.send(poststr);
}
