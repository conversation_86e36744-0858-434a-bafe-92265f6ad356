Pf.keepComment = function(self,currentuserid,element,id,disappear) {
	var kept = self.getAttribute('data-kept') ? true : false;
	var keepreq = initrequest(true);
	if (!keepreq) return;
	keepreq.open('POST','/' + element + '/' + id + '.' + (kept ? 'unkeep' : 'keep'),true);
	keepreq.onreadystatechange = function() {
		if (keepreq.readyState !== 4) {
			return;
		}
		if (keepreq.status !== 200) {
			showerrors(keepreq);
		} else {
			var nowkept = !kept;
			setattr(self,'data-kept',nowkept);
			var swap = self.title;
			self.title = self.getAttribute('data-other');
			setattr(self,'data-other',swap);
			Pf.changeKeptCount(kept ? -1 : 1);
			if (disappear && !nowkept) {
				var nmsg = Pf.getParent(self,'nmsg');
				Pf.removeNode(nmsg);
			}
			setclass(self,!nowkept,'notyet');
		}
	};
	keepreq.send(null);
}
Pf.changeKeptCount = function(diff) {
	var keptcnt = getobj('keptcnt');
	if (!keptcnt) return;
	var k = parseInt(keptcnt.innerHTML);
	k += diff;
	keptcnt.innerHTML = k;
	setdisplay(keptcnt.parentNode,k);
}
