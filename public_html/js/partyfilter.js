function process_position(lat,lon,dofilter) { try {
	let filterspart = getobj('filters');
	if (!filterspart) {
		return;
	}
	let req = initrequest(true);
	if (!req) {
		return;
	}

	let latlon = lat + ',' + lon;

	let formobj = getobj('filterform');
	if (formobj) {
		if (formobj.REGION_LATLNG) {
			formobj.REGION_LATLNG.value = latlon;
		}
		if (formobj.LOCATION_LATLNG) {
			formobj.LOCATION_LATLNG.value = latlon;
		}
	}
	
	if (dofilter) {
		filter_parties(formobj);
	}

	let cgeo = getobj('CGEO');
	if (cgeo) {
		let cco = cgeo.content.split(',');
		if (Math.abs(lat - cco[0]) <= 0.02
		&&	Math.abs(lon - cco[1]) <= 0.02
		) {
			// almost no change in coordinates, no need to re-query
			return;
		}
		cgeo.content = latlon;
	}
	
	return;
	
	req.open('GET','/include/agendafilters.inc' + (lat && lon ? '?lat='+lat+';lon='+lon : ''),true);
	req.onreadystatechange = function() {
		if (req.readyState !== 4) {
			return;
		}
		if (req.responseText) {
			filterspart.innerHTML = req.responseText;
		}
		if (formobj) {
			switch (formobj.PART.value) {
			case 'region':
			case 'location':
				if (!dofilter) {
					let key = formobj.PART.value.toUpperCase() + '_LATLNG';
					dofilter = formobj[key].value != latlon;
				}
				break;
			}
			if (formobj.REGION_LATLNG) {
				formobj.REGION_LATLNG.value = latlon;
			}
			if (formobj.LOCATION_LATLNG) {
				formobj.LOCATION_LATLNG.value = latlon;
			}
			if (dofilter) {
				filter_parties(formobj);
			}
		}
	};
	req.send(null);
} catch(e) {
	catchLog(e);
	return false;
}
}
function load_default_filters(error) {
	do_inline('GET','/include/agendafilters.inc','FILL','filters');
}

function change_order(part) {
	let req = initrequest(true);
	if (!req) return
	req.open('POST', '/filterparties.obj', true);
	req.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
	req.send('ONLY=1&ORDER='+part.value);

	let tables = document.getElementsByTagName('table');

	for (let i = 0; i < tables.length; ++i) {
		let table = tables[i];
		if (!table.className.match(/\bpartylist\b/)) {
			continue;
		}
		let sorttbodies = [];
		let header = null;
		for (let j = 0; j < table.tBodies.length; ++j) {
			let tbody = table.tBodies[j];
			if (tbody.className.match(/\bparty\-day\b/)) {
				Pf.sortPartylistRows(tbody,sorttbodies,part.value);
				header = tbody;
				sorttbodies = [];
				continue;
			}
			sorttbodies.push(tbody);
		}
		Pf.sortPartylistRows(null,sorttbodies,part.value);
	}
}

Pf.sortPartylistRows = function(header, tbodies, sorttype) {
	if (!tbodies.length) {
		return;
	}
	
	tbodies.sort(function(a,b) {
		let a_ignore = a.className.match(/\bsort\-ignore\b/)  ? -1 : 1;
		let b_ignore = b.className.match(/\bsort\-ignore\b/) ? -1 : 1;
		let rc = a_ignore - b_ignore;
		if (rc) {
			return rc;
		}
		if (a_ignore == -1 || b_ignore == -1) {
			return 0;
		}

		let accpt = a.className.match(/\bunaccepted\b/) ? 1 : -1;
		let bccpt = b.className.match(/\bunaccepted\b/) ? 1 : -1;
		rc = accpt - bccpt;
		if (rc) {
			return rc;
		}
		let ndx = -1;
		switch (sorttype) {
		case 'CITY':
			ndx = 0;
		case 'LOCATION':
			if (ndx == -1) ndx = 1;
		case 'VISITORS':
			if (ndx == -1) ndx = 2;
			
			let a_orders = a.getAttribute('data-order').split(',');
			let b_orders = b.getAttribute('data-order').split(',');
			rc = b_orders[ndx] - a_orders[ndx];
			if (rc) {
				return rc;
			}
		}
		return Pf.getText(b.rows[0].cells[0]).localeCompare(Pf.getText(a.rows[0].cells[0]));
	});
	for (let r = tbodies.length-1; r >= 0; --r) {
		let tbody = tbodies[r];
		if (header) {
			tbody.parentNode.insertBefore(tbody,header);
		} else {
			tbody.parentNode.appendChild(tbody);
		}
	}
}
function change_part(part) {
	switch (part.value) {
	case 'all':
		setdisplay(['region_part','choose_part'],false,true);
		filter_parties(part.form);
		break
	case 'region':
		Pf.initFilter(part.form,function(){
			setdisplay('region_part',true,true);
			hide('choose_part');
		});
		break;
	case 'choose':
		Pf.initFilter(part.form,function(){
			unhide('choose_part');
			setdisplay('region_part',false,true);
		});
		break;
	}
}
function filter_parties(form) {
	if (!form) {
		form = getobj('filterform',true);
		if (!form) {
			return;
		}
	}
	form.className = 'light';
	let str = '';
	if (!form.partyids) {
		form.partyids = {};
		let tables = document.getElementsByTagName('table');
		for (let i = 0; i < tables.length; ++i) {
			let table = tables[i];
			if (!table.className.match(/\bpartylist\b/)) {
				continue;
			}
			for (let r = 0; r < table.tBodies.length; ++r) {
				let tbody = table.tBodies[r];
				if (tbody.id
				&&	tbody.id.match(/^p((\d+)(?:\.\d+)?)$/)
				) {
					// partyid.multidaycnt
					
					let partyid = RegExp.$2;
					let partyid_with_day = RegExp.$1;
					
					if (typeof form.partyids[partyid] === 'undefined') {
						form.partyids[partyid] = [partyid_with_day];
					} else {
						form.partyids[partyid].push(partyid_with_day);
					}
				}
			}
		}
	}
	if (!form.partyids) {
		return;
	}
	let part = Pf_radioValue(form.PART);
	
	switch (part) {
	case 'all':
		for (let partyid in form.partyids) {
			form.partyids[partyid].forEach(function(id){
				let party_row = getobj('p'+id);
				if (!party_row) {
					return;
				}
				if (party_row.className.match(/\b(offset-(?:\d+))\b/)) {
					// maybe hidden day
					let party_day = getobj(RegExp.$1 + '-row');
					if (party_day
					&&	party_day.className.match(/\bsmall\b/)
					) {
						// day is hidden
						return;
					}
				}
				setdisplay(party_row,true,true);
			});
		}
		fix_day_visibility();
		break;
	case 'region':
		str += get_radpart(form,'REGION');
		break;
	case 'choose':
		str += get_radpart(form,'LOCATION');
		str += get_radpart(form,'CITY');
		let selects = [	'SHOW_COUNTRY',
				'SHOW_GENRES',
				'SHOW_BUDDIES',
				'SHOW_BUDDIES_MAYBE',
				'SHOW_GOING',
				'SHOW_FAVOURITES',
				'SHOW_COMPATRIOTS',
				'COMPATRIOTS',
				'SHOW_VISITORS',
				'VISITORS'
		];//SHOW_GID
		for (let i = 0; i < selects.length; ++i) {
			let name = selects[i];
			if (form[name]) {
				str += '&' + name + '=' + form[name].value;
			}
		}
		if (form['COUNTRYID']) {
			str += '&COUNTRYID='+form.COUNTRYID.value;
		}
	}
	str += '&PART='+part;
	if (part != 'all') {
		if (!form.partyidstr) {
			let partyidlist = [];
			for (let partyid in form.partyids) {
				partyidlist.push(partyid);
			}
			partyidlist.sort();
			form.partyidstr = partyidlist.join(',');
		}
		str += '&PARTYIDS='+form.partyidstr;
	}
	let req = initrequest(true);
	if (!req) {
		form.className = '';
		return;
	}
	req.open('POST', '/filterparties.obj', true);
	req.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
	req.onreadystatechange = function () {
		if (req.readyState !== 4) {
			return;
		}
		form.className = '';
		if (req.status !== 200) {
			showerrors(req,ERR_TO_ALERT);
			return;
		}
		if (!req.responseText) {
			return;
		}
		let parties = parseJSON(req.responseText);
		if (typeof parties === 'boolean') {
			fix_day_visibility(parties)
		} else {
			for (let partyid in parties) {
				form.partyids[partyid].forEach(function(id) {
					let party_body = getobj('p' + id,false);
					if (!party_body) {
						return;
					}
					setdisplay(party_body,parties[partyid],true);
					let next_body = party_body.nextSibling;
					if (next_body
					&&	next_body.firstChild.className === 'party-spot'
					) {
						setdisplay(next_body,parties[partyid], true);
					}
				});
			}
			fix_day_visibility();
		}
	}
	req.send(str);
}
function get_radpart(form,type) {
	let showpart = 'SHOW_'+type;
	let radpart = type+'_RADIUS';
	let latpart = type+'_LATLNG';
	if (!form[showpart]
	||	!form[showpart].value
	||	!form[latpart]
	||	form[latpart].value.is_empty()
	) {
		return '';
	}
	let showvalue = form[showpart].options[form[showpart].selectedIndex].value;
	let radvalue = form[radpart]. options[form[radpart]. selectedIndex].value;
	return '&'+showpart+'=' + showvalue + '&'+radpart+'=' + radvalue + '&' + latpart + '=' + form[latpart].value;
}
function fix_day_visibility(visible) {
	function setdisplay_daytbody(tbody, visi, viscnt) {
		if (!tbody) {
			return;
		}
		let trow = tbody.firstChild;
		setdisplay(trow,visi);
		if (!trow.id.match(/^offset\-(\d+)/)) {
			return;
		}
		let cnt = getobj('offset-' + RegExp.$1 + '-cnt');
		if (!cnt) {
			return;
		}
		cnt.innerHTML = viscnt;
	}
	let tables = document.getElementsByTagName('table');
	for (let i = 0; i < tables.length; ++i) {
		let table = tables[i];
		if (!table.className.match(/\bpartylist\b/)) {
			continue;
		}
		let current_day_tbody = false;
		let day_visible = false;
		let month_visible = false;
		let viscnt = 0;
		
		for (let r = 0; r < table.tBodies.length; ++r) {
			let tbody = table.tBodies[r];
			if (tbody.id
			&&	tbody.id.match(/^p(\d+)(\.\d+)?$/)
			) {
//				let dbgstr = 'found party ' + RegExp.$1 + (RegExp.$2 ? ' of multi day ' + RegExp.$2 : '');
				if (typeof visible === 'boolean') {
					setdisplay(tbody,visible);
					if (visible) {
						day_visible = month_visible = true;
						++viscnt;
					}
				} else if (is_visible(tbody,true)) {
//					dbgstr += ' VISIBLE';
					day_visible = month_visible = true;
					++viscnt;
				} else {
//					dbgstr += ' invisible';
				}
			} else if (tbody.className.match(/\bparty\-day\b/)) {
				// set previous day visibility
				setdisplay_daytbody(current_day_tbody, day_visible, viscnt);
				viscnt = 0;
				current_day_tbody = tbody;
				day_visible = false;
			}
		}
		if (current_day_tbody) {
			setdisplay_daytbody(current_day_tbody, day_visible, viscnt);
		}
		if (table.previousSibling.className.match(/\bparty\-month\b/)) {
			setdisplay(table.previousSibling,month_visible);
		}
		setdisplay(table,month_visible);
	}
}
Pf.initFilter = function(ff,okhandler) {
	if (ff.getAttribute('data-geodone')) {
		if (okhandler) {
			okhandler();
		}
		filter_parties(ff);
		return;
	}
	setattr(ff,'data-geodone',true);
	if (!have_geoposition()) {
		load_default_filters();
		if (okhandler) {
			okhandler();
		}
		filter_parties(ff);
	} else {
		get_geoposition(function(lat,lon) {
			if (okhandler) {
				okhandler();
			}
			process_position(lat,lon,true);
		},function(){
			load_default_filters();
			filter_parties(ff);
		},10000);
	}
}
addreadyevent(function() {
	let ff = getobj('filterform');
 	if (!ff) {
 		return;
	}
	let have_geo = have_geoposition();
	if (!have_geo) {
		if (ff.PART.selectedIndex == 1) {
			ff.PART.selectedIndex = 0;
			ff.PART.onchange(ff);		
		}
	}
	if ('all' === Pf_radioValue(ff.PART)) {
		return;	
	}
	Pf.initFilter(ff);
});
