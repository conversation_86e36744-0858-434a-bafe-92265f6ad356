<?php

define('CURRENTSTAMP',time());

require_once '../_offline.inc';
require_once '../_modified.inc';
require_once '../_hashes.inc';

function bail($errno) {
	http_response_code($errno);
	exit;
}

if (not_modified(etag: get_hash('js/commission.js.php'))) {
	bail(304);
}

header('Content-Type: text/javascript; charset=us-ascii');

require_once '../_db.inc';

$commies = db_simple_hash('relation','
	SELECT DISTINCT BANNERID,DEFAULT_COMMISSION
	FROM relation JOIN banner USING (RELATIONID)
	WHERE DEFAULT_COMMISSION!=0'
);
if ($commies === false) {
	bail(500);
}
$discounts = db_simple_hash('relation','
	SELECT BANNERID,DISCOUNT
	FROM relation
	JOIN banner USING (RELATIONID)
	WHERE DISCOUNT!=0
	  AND DISCOUNT_STOPSTAMP>'.CURRENTSTAMP
);
if ($discounts === false) {
	bail(500);
}

?>function changeBanner(selectobj){<?

if ($commies) {
	?>var commission;<?
	?>switch (selectobj.value){<?
	?>default:commission=0;break;<?
	foreach ($commies as $bannerid => $commission) {
		?>case '<?= $bannerid; ?>':commission=<?= $commission; ?>;break;<?
	}
	?>}<?
	?>setdisplay(['commissionrow','pricerow'],commission);<?
	?>selectobj.form.COMMISSION.value=commission;<?
	?>selectobj.form.EXPLICIT.checked=commission?true:false;<?
}
if ($discounts) {
	?>var dobj=getobj('discount');<?
	?>if(!dobj)return;<?
	?>switch(selectobj.value){<?
	foreach ($discounts as $relationid => $discount) {
		?>case '<?= $relationid; ?>':dobj.value='<?= $discount; ?>';break;<?
	}
	?>default:dobj.value='0'}<?
}
?>}
