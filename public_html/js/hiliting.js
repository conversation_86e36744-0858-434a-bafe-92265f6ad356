let selectclass = 'bold-hilited';
lethiliteclass = 'hilited';
let doubleclass = 'double-hilited';

function hiliteparent(node,type,cls) {
	while (node && node.nodeName != type) node = node.parentNode;
	if (node) hiliteobj(node,cls);
}
function deliteparent(node,type) {
	while (node && node.nodeName != type) node = node.parentNode;
	if (node) deliteobj(node);
}
function hiliteobj(obj,cls) {
	if (cls) {
		addclass(obj,obj._hilited = cls);
	} else if (obj.className.match(RegExp('\\b' + selectclass + '\\b'))) {
		addclass(obj,obj._doubled = doubleclass);
	} else {
		addclass(obj,obj._hilited = hiliteclass);
	}
}
function deliteobj(obj) {
	if (obj._hilited) {
		remclass(obj,obj._hilited);
		obj._hilited = false;
	}
	if (obj._doubled) {
		remclass(obj,obj._doubled);
		obj._doubled = false;
	}
}
function cbclickppp(cbobj,hclass) {
	return cbclick(cbobj,cbobj.parentNode.parentNode.parentNode,hclass);
}
function cbclickpp(cbobj,hclass) {
	return cbclick(cbobj,cbobj.parentNode.parentNode,hclass);
}
function cbclickp(cbobj,hclass) {
	return cbclick(cbobj,cbobj.parentNode,hclass);
}
function cbclick(cbobj,obj,hclass) {
	if (!hclass) {
		hclass = selectclass;
	}
	if (cbobj.checked) {
		if (obj._hilited) {
			obj._doubled = true;
			addclass(obj,doubleclass);
		}
		addclass(obj,hclass);
	} else {
		if (obj._doubled) {
			obj._doubled = false;
			remclass(obj,doubleclass);
		}
		remclass(obj,hclass);
	}
}
function radioclickp(radiobj,upcnt) {
	radioclick(radiobj,radiobj.parentNode,upcnt);
}
function radioclickpp(radiobj,upcnt) {
	radioclick(radiobj,radiobj.parentNode.parentNode,upcnt);
}
function radioclick(radiobj,obj,upcnt) {
	if (typeof prevradio !== 'undefined') { 
		remclass(prevradio, selectclass);
	} else {
		if (!upcnt) {
			upcnt = 1;
		}
		var inputs = document.getElementsByTagName('input');
		if (inputs)
		for (let node of inputs) {
			if (node.type === 'radio'
			&&	node.name === radiobj.name
			&&	node !== radiobj
			) {
				let u = upcnt;
				while (u--) {
					node = node.parentNode;
					if (node.className.match(RegExp('\\b' + selectclass + '\\b'))) {
						remclass(node,selectclass);
						upcnt = 0;
						break;
					}
				}
			}
			if (!upcnt) {
				break;
			}
		}
	}
	addclass(obj,selectclass);
	prevradio = obj;
}
