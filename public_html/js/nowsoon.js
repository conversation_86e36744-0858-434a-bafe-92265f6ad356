let gmap = null;
let specialMap = 0;
let doCluster = 1;

function radiusToZoom(mapsize, lat, max_distance) {
	let wantsize = mapsize * 2/3;
	let zoom
	for (zoom = 20; zoom > 0; --zoom) {
		let pixels = max_distance * 1000 * metresToPixels(lat,zoom);
		if (pixels <= wantsize) {
			break;
		}
	}
	return zoom - 1;
}

function metresToPixels(lat,zoom) {
	return 256 * Math.pow(2, zoom) / (12756000 * Math.PI * Math.cos(lat * Math.PI / 180));
}

function showMap(form, lat, lon, max_distance) {
	let holder = getobj('map', true);
	if (!holder) {
		return;
	}
	if (specialMap) {
		document.body.insertBefore(holder,document.body.firstChild);
	}
	let dosmall = holder.getAttribute('data-dosmall');
	let smallscreen = holder.getAttribute('data-smallscreen');
	google.maps.visualRefresh = true;
	let pt = new google.maps.LatLng(lat, lon);
	let zoom = radiusToZoom(holder.clientWidth, lat, max_distance);
	gmap = new google.maps.Map(holder, {
		zoom:		zoom,
		center:		pt,
		mapTypeId:	google.maps.MapTypeId.ROADMAP
	});
	if (doCluster) {
		gmap.Pf_cluster = new MarkerClusterer(gmap, [], {
			gridSize:		40,
			enableRetineIcons:	true,
			maxZoom:		15
		});
	}
	gmap.Pf_infos = {};
	gmap.Pf_hilited_rows = [];
	gmap.Pf_infowindow = new google.maps.InfoWindow;
	gmap.Pf_holder = holder;
	gmap.Pf_circle = new google.maps.Circle({
		radius:			max_distance * 1000,
		fillOpacity:	.2,
		center:			pt,
		map:			gmap,
		clickable:		false,
		draggable:		true,
		editable:		true,
		zIndex:			20,
	});
	let iconSize = new google.maps.Size(22, 40);
	gmap.Pf_icon = {
		url:		'//static.partyflock.nl/images/flockmarkerv2' + Pf.high_res + '.png',
		size:		iconSize,
		scaledSize:	iconSize	// required for HPPI screens
	};
	gmap.fitBounds(gmap.Pf_circle.getBounds());
	let high_res = false; // Pf.high_res => bug in googlemaps 
	let div = dosmall ? 1 : 2;

/*	gmap.Pf_star = new google.maps.MarkerImage(
		smallscreen
	?	document.location.protocol+'//static.partyflock.nl/star/yellow_special_'+(high_res ? 64 : 32)+'.png'
	:	document.location.protocol+'//static.partyflock.nl/star/yellow'+(high_res ? '@2x' : '')+'.png',
		new google.maps.Size(32/div,32/div),
		null,
		new google.maps.Point(16/div,16/div),
		high_res ? new google.maps.Size(32/div,32/div) : null
	);*/

/*	gmap.Pf_z = 10;
	gmap.Pf_marker = new google.maps.Marker({
		map:		gmap,
		icon:		gmap.Pf_star,
		position:	pt,
		zIndex:		gmap.Pf_z,
		draggable:	true,
		clickable:	false
	});
	google.maps.event.addListener(gmap.Pf_marker,'dragend',function(event){
		form.CHOSEN_LATITUDE.value = event.latLng.lat();
		form.CHOSEN_LONGITUDE.value = event.latLng.lng();
		form.WHERE.options[1].disabled = false;
		form.WHERE.selectedIndex = 1;
		getNowSoon(form);
	});*/
	google.maps.event.addListener(gmap.Pf_circle, 'center_changed', function(){
		let center_lat = gmap.Pf_circle.center.lat();
		let center_lon = gmap.Pf_circle.center.lng();
		
		let diffLat = Math.abs(Pf.curLat - center_lat);
		let diffLon = Math.abs(Pf.curLon - center_lon);

		if (diffLat < 0.0001
		&&	diffLon < 0.0001
		) {
			return;
		}
		
		form.CHOSEN_LATITUDE.value = center_lat;
		form.CHOSEN_LONGITUDE.value = center_lon;
		form.WHERE.options[1].disabled = false;
		form.WHERE.selectedIndex = 1;
		getNowSoon(form);
	});

	google.maps.event.addListener(gmap.Pf_circle, 'radius_changed', function(){
		let radius = Math.round(parseInt(gmap.Pf_circle.radius) / 1000);
		let prev = 0;
		let done = false;
		for (let opt of form.MAX_DISTANCE.options) {
			let curr = opt.value;
			if (radius === curr) {
				form.MAX_DISTANCE.selectedIndex = i;
				done = true;
				break;
			}
			if (radius > prev && radius < curr) {
				let newopt = document.createElement('OPTION');
				newopt.value = newopt.innerHTML = radius;
				newopt.selected = true;
				opt.parentNode.insertBefore(newopt, opt);
				done = true;
				break;
			}
			prev = curr;
		}
		if (!done) {
			let newopt = document.createElement('OPTION');
			newopt.value = newopt.innerHTML = radius;
			newopt.selected = true;
			opt.parentNode.appendChild(newopt);
		}
		form.MAX_DISTANCE.value = radius;
		gmap.fitBounds(gmap.Pf_circle.getBounds());
		getNowSoon(form);
	});
}

function getNowSoon(obj) {
	let form = obj.form ? obj.form : obj;
	let nowsoon = getobj('nowandsoon', true);
	if (!nowsoon) {
		return;
	}
	let req = initrequest(true);
	if (!req) {
		return;
	}

	let latitude = null;
	let longitude = null;
	switch (form.WHERE.value) {
	case 'current':
		latitude  = form.CURRENT_LATITUDE.value;
		longitude = form.CURRENT_LONGITUDE.value;
		break;

	case 'chosen':
		latitude  = form.CHOSEN_LATITUDE.value;
		longitude = form.CHOSEN_LONGITUDE.value;
		break;
	}
	
	let poststr =
		latitude === null
	?	'' 
	:	'&LATITUDE=' + latitude +
		'&LONGITUDE=' + longitude;
		
	let stamp = 0;

	setdisplay('time-stuff', form.DATE.selectedIndex);

	if (form.DATE.selectedIndex) {
		let date = form.DATE.value;
		let year = Math.floor(date / 10000);
		date -= year * 10000;
		let month = Math.floor(date / 100);
		date -= month * 100;
		let day = date;
		let hour = parseInt(form.HOUR.value);
		let mins = parseInt(form.MINS.value);
		date = new Date(year, month - 1, day, hour, mins);
		stamp = Math.floor(date.getTime() / 1000);
	}
	req.open('GET',
		'/include/nowandsoon.inc?' +
			'MAX_TIME=' + form.MAX_TIME.value +
			'&MAX_DISTANCE=' + form.MAX_DISTANCE.value +
			'&MIN_DURATION=' + form.MIN_DURATION.value +
			'&WHERE=' + form.WHERE.value +
			'&STAMP=' + stamp +
			(form.CITY ? '&CITY=1' : '') +
			poststr,
 		true
	);

	req.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
	req.onreadystatechange = function() {
		if (req.readyState !== 4) {
			return;
		}
		if (req.status !== 200) {
			showerrors(req, ERR_TO_ALERT);
			return;
		}
		form.className = '';
		nowsoon.innerHTML = req.responseText;
		sorttable.init();
		let lat = req.getResponseHeader('X-LATITUDE');
		let lon = req.getResponseHeader('X-LONGITUDE');
		let pt = new google.maps.LatLng(lat, lon);
		Pf.curLat = lat;
		Pf.curLon = lon;
		if (!gmap) {
			showMap(form,lat,lon,parseInt(form.MAX_DISTANCE.value));
		} else {
			if (gmap.Pf_circle.center.lat() != pt.lat()
			||	gmap.Pf_circle.center.lng() != pt.lng()
			) {
				gmap.Pf_circle.setCenter(pt);
			}
			if (obj.form
			&&	obj.name === 'MAX_DISTANCE'
			) {
				gmap.Pf_circle.setRadius(parseInt(obj.value) * 1000);
				gmap.fitBounds(gmap.Pf_circle.getBounds());
			}
			if (gmap.Pf_marker) {
				gmap.Pf_marker.setPosition(pt);
			}
			gmap.panTo(pt);
		}
		Pf.nowsoonAddMarkers();
		if (gmap.hilited) {
			if (typeof gmap.Pf_infos[gmap.hilited] === 'undefined') {
				gmap.Pf_infowindow.close();
				delete gmap.hilited;
				delete gmap.Pf_hilited_rows;
			} else {
				build_hilited_rows(gmap.Pf_infos[gmap.hilited].pids);
			}
		}
		if (obj.form
		&&	obj.name === 'DATE'
		) {
			form.MAX_TIME.options[0].innerHTML = obj.selectedIndex ? form.ONLYTHEN.value : form.ONLYNOW.value;
		}
	};
	form.className = 'light';
	req.send(null);
}
function focusOnParty(icnt, lat, lng) {
	gmap.panTo(new google.maps.LatLng(lat, lng));
	let mk = gmap.Pf_infos[icnt].marker;
	if (doCluster) {
		gmap.setZoom(16);
	}
	setTimeout(function() {
			clickOnMarker(mk)
		},500
	); // without timeout, map jumps back to old position somehow

	gmap.Pf_holder.scrollIntoView(true);
}

function clear_hilited_rows() {
	if (!gmap.Pf_hilited_rows) {
		return;
	}
	for (let hilited_row of gmap.Pf_hilited_rows) {
		hilited_row.className = 'hl';
	}
}

function build_hilited_rows(pids) {
	gmap.Pf_hilited_rows = [];
	for (let pid of pids) {
		let prow = getobj('p' + pid, true);
		if (prow) {
			prow.className = 'light-hilited';
			gmap.Pf_hilited_rows.push(prow);
		}
	}
}

function resizeMap() {
	if (RegExp("\\bdev\\b").test(document.body.className)) {
		return;
	}
	if (specialMap) {
		return;
	}
	let  m = getobj('map', true);
	if (!m) {
		return;
	}

	let smallscreen = m.getAttribute('data-smallscreen');
	let win = getWindowInfo();
	let dosmall;
	let w;
	let h;
	
	if (!smallscreen) {
		dosmall = win.w < 960;
		setattr(m, 'data-dosmall', dosmall);
		if (dosmall) {
			m.style.width = 'auto';
		}
	} else {
		dosmall = true;
	}
	m.className = dosmall ? 'block mw90' : 'block hide-on-small';
	if (dosmall) {
		w = m.parentNode.clientWidth;
		h = Math.min(500, Math.floor(win.h * 2 / 4), w);
	} else {
		w = m.clientWidth;
		h = Math.min(500, win.h * 2 / 3, w);
	}
	if (m.getAttribute('data-h') == h
	&&	m.getAttribute('data-w') == w
	) {
		return;
	}
	if (!dosmall) {
		m.style.width = w + 'px';
		setattr(m, 'data-w', w);
	}
	m.style.height = h + 'px';
	if (gmap) {
		let c = gmap.getCenter();
		google.maps.event.trigger(gmap,'resize');
		gmap.setCenter(c);
	}
	setattr(m, 'data-h', h);
}

Pf.nowsoonAddMarkers = function() {
	let infos = getobj('nowsoonjson');
	if (infos) {
		infos.parentNode.removeChild(infos);
		infos = parseJSON(infos.innerHTML);
		let markers = [];
		for (let icnt in infos) {
			let info = infos[icnt];
			if (typeof gmap.Pf_infos[icnt] === 'undefined') {
				let newmark = info.marker = new google.maps.Marker({
					map:		gmap,
					icon:		gmap.Pf_icon,
	//				shadow:		gmap.Pf_shadow,
	//				shape:		gmap.Pf_shape,
					zIndex:		5,
					position:	new google.maps.LatLng(info.lat,info.lon),
					draggable:	false
				});
				info.marker.icnt = icnt;
				info.marker.info = info;
				info.marker.div = getobj('i'+icnt);
				google.maps.event.addListener(info.marker,'click',function(){
					clickOnMarker(this);
				});
				gmap.Pf_infos[icnt] = info;
				markers.push(info.marker);
			}
		}
		if (gmap.Pf_cluster) {
			gmap.Pf_cluster.addMarkers(markers);
		}
	}
	for (let icnt in gmap.Pf_infos) {
		if (typeof infos[icnt] === 'undefined') {
			let oldinfo = gmap.Pf_infos[icnt];
			oldinfo.marker.setMap(null);
			if (doCluster) {
				gmap.Pf_cluster.removeMarker(oldinfo.marker);
			}
			oldinfo.marker = null;
			delete gmap.Pf_infos[icnt];
		}
	}
}

function clickOnMarker(marker) {
	clear_hilited_rows();
	build_hilited_rows(marker.info.pids);
	gmap.hilited = marker.icnt;
	gmap.hilited_pids = marker.info.pids;

	gmap.Pf_infowindow.setContent(marker.div);
	gmap.Pf_infowindow.open(gmap, marker);
}

addevent(window, ['ready', 'resize'], resizeMap);
