<?php

declare(strict_types=1);

define('CURRENTSTAMP',time());

require_once '../_exit_if_offline.inc';
require_once '../__translation.php';
require_once '../_hashes.inc';
require_once '../_modified.inc';
require_once '../defines/facebook.inc';

function bail(int $errno): never {
	http_response_code($errno);
	exit;
}

if (not_modified(etag: '2023060401_'.get_hash('js/main.js.php'))) {
	bail(304);
}

require_once '../_servertype.inc';
require_once '../_browser.inc';
require_once '../_hosts.inc';

header('Content-Type: text/javascript');

?>var Pf = Pf || {
	includedJS:	[],

	high_res:	window.devicePixelRatio >= 1.5 ? '@2x' : '',

	update_totalmsg: function(cnt) {
		const total = document.getElementById('totalmsgs');
		if (!total) {
			return;
		}
		const old_count = parseInt(total.innerHTML);
		total.innerHTML = (old_count + cnt) ? old_count + cnt : '';
	},
	trackLink: function(url,category,label) {
		if (typeof ga === 'function') {
			ga('send', 'event', category, 'click', label);
		}
		return true;
	},
	hash_arrow: {
		init: function() {
			let hr = document.createElement('img');
			hr.alt = Pf.smallScreen ? '&darr;' : '&rarr;';
			hr.src = '<?= STATIC_HOST ?>/images/' + (Pf.smallScreen ? 'darrmb' : 'rarr') + Pf.high_res + '.png';
			hr.className = Pf.smallScreen ? 'darr' : 'z2 abs rarr';
			this.hr = hr;
		},

		insertBefore: function(node, noscroll) {
			this.hr || this.init();
			node.parentNode.insertBefore(this.hr, node);
			if (!noscroll) {
				this.hr.scrollIntoView();
			}
		},
		detach: function() {
			if (this.hr && this.hr.parentNode) {
				this.hr.parentNode.removeChild(this.hr);
			}
		}
	},
	insertAfter: function(newelem,ref) {
		if (ref.nextSibling) {
			ref.parentNode.insertBefore(newelem,ref.nextSibling);
		} else {
			ref.parentNode.appendChild(newelem);
		}
	},
	focus: function(elem) {
		if (!elem.focus) {
			return;
		}
		try {
			if (typeof pf_disableZoom !== 'undefined') {
				pf_disableZoom();
				elem.focus();
				pf_enableZoom();
			} else {
				elem.focus();
			}
		} catch (e) {
			catchLog(e,'INPUT ' + elem.name);
		}
	},
	auto_focus: function(doc,tagname) {
		if (!tagname) {
			tagname = 'input';
		}
		let elems = doc.getElementsByTagName(tagname);
		if (!elems) {
			return false;
		}
		for (const elem of elems) {
			if (!elem.disabled
			&&	elem.getAttribute('autofocus')
			) {
				if (focus_and_to_end) {
					focus_and_to_end(elem.parentNode,elem);
				} else {
					Pf.focus(elem);
				}
				return true;
			}
		}
		return false;
	},
	encodeForPost: function(data) {
		// does encodeURIComponent suffice nowadays?
		return encodeURIComponent(data);
	},
	getParentNode: function(node, nodeName) {
		while (node && node.nodeName !== nodeName) {
			node = node.parentNode;
		}
		return node;
	},
	getParent: function(node,cnt) {
		if (typeof cnt === 'string') {
			let nodeN = cnt.match(/^[A-Z]+$/);
			let regex = nodeN ? null : RegExp('(?:^|\\s)' + cnt + '(?:\\s|$)');
			while (node = node.parentNode) {
				if (nodeN
				?	node.nodeName === cnt
				:	regex.test(node.className)
				) {
					return node;
				}
			}
			return node;
		}
		if (!cnt) {
			cnt = 1;
		}
		while (cnt--) {
			node = node.parentNode;
			if (!node) {
				return null;
			}
		}
		return node;
	},
	removeNode: function(arg,ignore) {
		walkarg(arg,function(obj){
			if (!obj
			||	!obj.parentNode
			) {
				return;
			}
			obj.parentNode.removeChild(obj);
		},ignore);
	},
	changeParentNode: function(node, parentNodeName, action, arg) {
		let parent = Pf.getParentNode(node, parentNodeName);
		if (!parent) {
			return;
		}
		Pf.changeNode(parent, action, arg);
	},
	changeNode: function(node,action,arg,arg2) {
		switch (action) {
		case 'remove':		node.parentNode.removeChild(node); break;
		case 'addclass':	addclass(node,arg); break;
		case 'remclass':	remclass(node,arg); break;
		case 'setclass':	setclass(node,arg,arg2); break;
		case 'toggleclass':	toggleclass(node,arg); break;
		case 'hide':		setdisplay(node,false); break;
		case 'show':		setdisplay(node,true); break;
		}
	},
	scrollToMiddle: function(obj) {
		let pos = findPos(obj);
		let win = getWindowInfo();
		window.scrollTo(0,pos[1] - win.h / 2 + obj.clientHeight / 2);
	},
	changeOpacity: function(node,opacity) {
		node.style.opacity = opacity / 100;
	},
	appear: function(node,delay) {
		if (typeof node === 'string') {
			if (!(node = getobj(node))) {
				return;
			}
		}
		let inc = 5;
		let opacity = 0;
		let intr = setInterval(function(){
			opacity += inc;
			Pf.changeOpacity(node,opacity);
			if (opacity >= 100) {
					clearInterval(intr);
			}
		},delay ? (delay * 1000 / (100 / inc)) : 10);
	},
	disappear: function(node,delay,remove) {
		if (typeof node === 'string') {
			if (!(node = getobj(node))) {
				return;
			}
		}
		let inc = 5;
		let opacity = 100;
		let intr = setInterval(function(){
			opacity -= inc;
			Pf.changeOpacity(node, opacity);
			if (opacity <= 0) {
				clearInterval(intr);
				if (remove) {
					if (typeof remove === 'function') {
						remove.call(node);
					} else {
						node.parentNode.removeChild(node);
					}
				}
			}
		},delay ? (delay * 1000 / (100 / inc)) : 10);
	},
	isIncludedScript: function(id) {
		return typeof this.includedJS[id] !== 'undefined';
	},
	/**
	 * @param {string} file
	 * @param {string} id
	 * @param {boolean} [async]
	 * @returns {boolean}
	 */
	includeScript: function(file, id, async) {
		if (!id) {
			id = file;
		}
		if (!this.isIncludedScript(id)) {
			this.includedJS[id] = true;
			let scr = document.createElement('script');
			scr.type = 'text/javascript';
			if (async) {
				scr.async = true;
			}
			if (id === 'facebook-jssdk') {
				scr.async = true;
				scr.defer = true;
				scr.crossOrigin = 'anonymous'
			}
			scr.src = file;
			document.getElementsByTagName("head")[0].appendChild(scr);
			return true;
		}
		return false;
	},
	/**
	 * @param {Node} node
	 * @returns {string|undefined}
	 */
	getText: function(node) {
		if (node instanceof HTMLInputElement
		||	node instanceof HTMLTextAreaElement
		||	node instanceof HTMLSelectElement
		) {
			return node.value;
		}
		// Exact text content, including whitespace and line breaks that are not rendered in the browser,
		// and hidden text (like text inside a <script> tag, or with display: none)
		if (node instanceof Node) {
			return node.textContent;
		}
		// Since a Node is the lowest level of the DOM hierarchy, this should never be reached.
		// Maybe we want innerText instead of textContent in some situations where getText() is used?
		if (node instanceof HTMLElement) {
		 	return node.innerText;
		}
		return undefined;
	},
	/**
	 * @param {Node} node
	 * @returns {void}
	 */
	selectNode: function(node) {
		if (!window.getSelection) {
			console.warn('window.getSelection not available');
			return;
		}
		const range = document.createRange();
		range.selectNodeContents(node);
		const selection = window.getSelection();
		selection.removeAllRanges();
		selection.addRange(range);
	},
	/**
	 * @param {Node} node
	 * @param {Node} hasNode
	 * @returns {boolean}
	 */
	containsNode: function(node, hasNode) {
		if (node.contains) {
			return node.contains(hasNode);
		}
		return Boolean(node.compareDocumentPosition(hasNode) & Node.DOCUMENT_POSITION_CONTAINED_BY);
	},
	/**
	 * @param {boolean} yes
	 * @param {boolean} long
	 * @returns {void}
	 */
	busy: function(yes, long) {
		let bb = getobj('bb');
		if (!bb) {
			return;
		}
		setclass(bb, yes, 'active');
		if (!long) {
			return;
		}
		bb.firstChild && setdisplay(bb.firstChild, yes);
	},
	uuidv4: function() {
		return ([1e7]+-1e3+-4e3+-8e3+-1e11).replace(
			/[018]/g, function(c) {
				return (c ^ crypto.getRandomValues(new Uint8Array(1))[0] & 15 >> c / 4).toString(16)
			}
		);
	},
	isInView: function(el) {
		const rect		= el.getBoundingClientRect();
		const vWidth	= window.innerWidth  || document.documentElement.clientWidth;
		const vHeight	= window.innerHeight || document.documentElement.clientHeight;

		// Return false if it is not in the viewport
		if (rect.right  < 0
		||	rect.bottom < 0
		||	rect.left > vWidth
		||	rect.top  > vHeight
		) {
			return false;
		}
		// Return true if any of its four corners are visible
		return 	el.contains(document.elementFromPoint(rect.left,  rect.top))
			||	el.contains(document.elementFromPoint(rect.right, rect.top))
			||	el.contains(document.elementFromPoint(rect.right, rect.bottom))
			||	el.contains(document.elementFromPoint(rect.left,  rect.bottom));
	},

	stopEvent: function(event) {
		event.preventDefault();
		event.stopPropagation();
	},

	markPageBusy: function() {
		addclass(document.body, 'light');
		addevent(window, 'unload', function(){
			remclass(document.body, 'light');
		});
	},

	submitFormIf: (field) => {
		if (field.value.is_empty()) {
			return;
		}
		Pf.busy(true, false);
		field.form.submit();
	},

	countInstances: (sentence, word, make_regex) => {
		if (make_regex) {
			word = new RegExp(word);
		}
		return sentence.split(word).length - 1;
	},

	genericError: '<?= __('error:generic_LINE', IS_JAVASCRIPT) ?>',

	alertGenericError: function() {
		alert(Pf.genericError);
	},
};

let Browser = {
	iOS:		<?= iOS ?: 'false' ?>
};

/**
 * @param {string} classname
 * @param {string} tag
 * @param {Document|HTMLElement|string} [startobj]
 * @returns {HTMLElement[]|HTMLCollectionOf<Element>}
 */
function getobjs_byclass(classname, tag, startobj) {
	if (!startobj) {
		startobj = document;
	} else if (typeof startobj === 'string') {
		if (!(startobj = getobj(startobj, true))) {
			return [];
		}
	}
	if (startobj.getElementsByClassName) {
		return startobj.getElementsByClassName(classname);
	}
	const objs = startobj.getElementsByTagName(tag);
	if (!objs.length) {
		return [];
	}
	const re = RegExp(`\\b${classname}\\b`);
	let result = [];

	for (let obj of objs) {
		if (re.test(obj.className)) {
			result.push(obj);
		}
	}
	return result;
}

/**
 * @param {string} name
 * @param {string} value
 * @param {int} expires
 */
function set_cookie(name, value, expires) {
	let cookie = name + '=' + encodeURIComponent(value) + '; path=/; secure';
	if (!document.body.hasAttribute('data-sandbox')) {
		cookie += '; domain=' + location.hostname.split('.').slice(-2).join('.');
	}
	if (expires) {
		const expire_date = new Date();
		expire_date.setTime(expire_date.getTime() + expires * 1000);
		cookie += '; expires=' + expire_date.toUTCString();
	}
	document.cookie = cookie;
}

function getWindowInfo() {
	return {
		w:  window.innerWidth,
		h:  window.innerHeight,
		sx: window.pageXOffset,
		sy: window.pageYOffset,
		dw: document.body.clientWidth,
		dh: document.body.clientHeight
	};
}

/**
 * @param {Object<string, string>} reps
 * @returns {string}
 */
String.prototype.replace_keywords = function(reps) {
	let str = this;
	for (const [key, value] of Object.entries(reps)) {
		str = str.replace(RegExp(`%${key}%`), value);
	}
	return str;
};

String.prototype.numericEntities = function() {
	let result = String();
	const len = this.length;
	for (let i = 0; i < len; ++i) {
		result += '&#' + this.charCodeAt(i) + ';';
	}
	return result;
};

function unhide(arg) {
	setdisplay(arguments, true);
}

function unhideobj(arg) {
	setdisplay(arg, true);
}

function hide() {
	setdisplay(arguments, false);
}

function hideobj(obj) {
	setdisplay(obj, false);
}

function setdisplay(arg, show, ignore) {
	setclass(arg, !show, 'hidden', ignore);
}

function is_visible(obj, shallow) {
	let style;
	return	obj
	&&	(	obj.nodeName === 'BODY'
		||	obj.parentNode
		&&	!obj.className.match(/\bhidden\b/)
		&&	obj.style.display !== 'none'
		&&	obj.style.visibility !== 'hidden'
		&&	(style = window.getComputedStyle ? window.getComputedStyle(obj, null) : obj.currentStyle)
		&&	style.display !== 'none'
		&&	style.visibility !== 'hidden'
		&&	(shallow || is_visible(obj.parentNode))
	);
}
function swapdisplay() {
	toggleclass(arguments,'hidden');
}
function swaphtml(self) {
	let tmp = self.getAttribute('data-otherhtml');
	self.setAttribute('data-otherhtml', self.innerHTML);
	self.innerHTML = tmp;
}
function toggleclass(arg,className) {
	walkarg(arg, function(obj){
		if (!obj.className) {
			obj.className = className;
			return;
		}
		let re = new RegExp('(\\b'+className+'\\b)');
		if (re.test(obj.className)) {
			obj.className = obj.className.replace(re,'');
		} else {
			obj.className += ' ' + className;
		}
	});
}
function swapclass(obj, newclassname, otherclassname) {
	if (typeof obj === 'string') {
		if (obj = getobj(obj, true)) {
			return;
		}
	}
	obj.className =
		(	obj.className === newclassname
		?	(otherclassname ? otherclassname : null)
		:	newclassname
	);
}

/**
 * @param {string} id
 * @param {boolean} [show_alert]
 * @returns {HTMLElement|false}
 */
function getobj(id, show_alert) {
	const obj = document.getElementById(id);
	if (obj) {
		return obj;
	}
	console.warn(`Element with id ${id} not found`);
	if (show_alert) {
		alert(String('<?= __('js:error:getobj_failed_LINE',IS_JAVASCRIPT) ?>').replace_keywords({'ID': id}));
	}
	return false;
}
function initrequest() {
	return new XMLHttpRequest();
}
function request_alert_status(req) {
	alert(String('<?= __('js:requesterror:bad_status_LINE',IS_JAVASCRIPT) ?>').replace_keywords({'STATUS': req.status, 'URL': req.url}));
}
/**
 * @param {string} data
 * @param {string} src
 * @returns {false|object}
 */
function parseJSON(data, src) {
	if (!data) {
		return false;
	}
	const actual_data = data.match(/^throw /) ? data.substring(14 + 2, data.length - 2) : data;
	let actual_json;
	try {
		actual_json = JSON.parse(actual_data);
	} catch(e) {
		const warnobj = getobj('jsonwarning');
		let newHTML = `<div class="error block">BAD JSON: ${e.message}</div><div class="tt warning">`;
		if (src) {
			newHTML += `source: ${src}<br />`;
		}
		newHTML += `data:   ${data.substring(0, 1024).numericEntities()}`;
		if (warnobj) {
			warnobj.innerHTML = newHTML;
		} else {
			const warning = document.createElement('div');
			if (warning) {
				warning.id = 'jsonwarning';
				warning.className = 'top-error';
				warning.innerHTML = newHTML;
				document.body.insertBefore(warning, document.body.firstChild);
			}
		}
		return false;
	}
	return actual_json;
}
/**
 * @param {string} method
 * @param {string} url
 * @param {string} action
 * @param {function(XMLHttpRequest, HTMLElement, object|null): boolean} [handler]
 * @param {string|null} [data]
 * @param {XMLHttpRequest|null} [req]
 * @param {object} [self]
 * @returns {XMLHttpRequest|string|any|boolean}
 */
function do_inline(method, url, action, handler, data, req, self) {
	if (! req
	&&	!(req = initrequest(true, false))
	) {
		return false;
	}
	req.open(
		method,
		url,
		   action !== 'RETURN'
		&& action !== 'RETURNREQ'
		&& action !== 'REQ'
		&& action !== 'ACTNOW'
	);
	if (!data && self) {
		data = self.getAttribute('data-data');
	}
	if (data) {
		req.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
	}
	switch (action) {
	case 'RETURNREQ':
		req.send(data);
		return req;

	case 'RETURN':
		req.send(data);
		req.ok = req.status >= 200 && req.status < 300;
		if (!req.ok) {
			showerrors(req, ERR_TO_ALERT);
			return;
		}
		let content_type = req.getResponseHeader('Content-Type');
		if (content_type.startsWith('application/json')) {
			return JSON.parse(req.responseText);
		}
		return req.responseText;

	case 'FILL':
		if (!handler) {
			console.error('empty FILL handler');
			return;
		}
		if (typeof handler === 'string') {
			if (!(handler = getobj(handler, true))) {
				return;
			}
		}
		req.onreadystatechange = function() {
			if (req.readyState !== 4) {
				return;
			}
			if (req.status === 200) {
				handler.innerHTML = req.responseText;
			}
		};
		break;

	case 'NULL_ACT':
		break;

	case 'ACT':
	case 'ACTNOW':
		req.onreadystatechange = function() {
			if (req.readyState !== 4) {
				return;
			}
			req.ok = req.status >= 200 && req.status < 300;
			let obj = null;
			if (req.responseText) {
				const content_type = req.getResponseHeader('Content-Type');
				if (content_type.startsWith('application/json')) {
					obj = JSON.parse(req.responseText);
				}
				let msgs;
				if (obj
				&&	typeof obj['MESSAGES'] !== 'undefined'
				&&	(msgs = getobj('messages'))
				) {
					msgs.innerHTML = obj['MESSAGES'];
				}
			}
			if ((handler ? handler(req, self, obj) : req.ok) && self) {
				let new_part;
				if (new_part = req.getResponseHeader('X-NEWNAME')) {
					self.innerHTML = new_part;
				}
				if (new_part = req.getResponseHeader('X-NEWDATA')) {
					self.setAttribute('data-data', new_part);
				}
			}
		};
	}
	req.send(data);
	return true;
}

function changeparent(node, find_class, action, arg1, arg2) {
	const find_class_regexp = RegExp('\\b' + find_class + '\\b');
	while (node && (!node.className || !find_class_regexp.test(node.className))) {
		node = node.parentNode;
	}
	if (node) {
		Pf.changeNode(node, action, arg1, arg2);
	}
}

function changerow(arg, action, arg2, arg3) {
	walkarg(arg, function(node) {
		while (node && node.nodeName !== 'TR') {
			node = node.parentNode;
		}
		if (node) {
			Pf.changeNode(node, action, arg2, arg3);
		}
	});
}

const ERR_TO_HTML	= 1;
const ERR_TO_ALERT	= 2;
const ERR_CLEAR		= 4;

function showerrors(req, type, map) {
	let errmsg;
	let data;
	let ctype;
	if (typeof req === 'string') {
		errmsg = req;
	} else {
		if (req instanceof Response) {
			// ctype = req.headers.get('Content-Type');
			req.text().then(function(txt){
				showerrors(txt, type, map);
			});
			return;
		}
		data  = req.responseText;
		ctype = req.getResponseHeader('Content-Type');
		if (!data
		||	!ctype.match(/^([^\s;]*)/)
		) {
			errmsg = `${Pf.genericError} (${req.status})`;
		} else {
			switch (RegExp.$1) {
			case 'text/html':
				errmsg = data;
				break;
			case 'application/json':
				let lines = parseJSON(data);
				if (!lines) {
					return;
				}
				let len = lines.length;
				let i = 0;
				errmsg = '';
				while (i < len) {
					errmsg += lines[i];
					if (++i === len) {
						break;
					}
					errmsg += "\n";
				}
				break;
			}
		}
		if (!map) {
			map = {};
		}
		map.STATUS = req.status;
	}
	let msgs = getobj('messages');
	if (msgs && (type & ERR_CLEAR)) {
		msgs.innerHTML = '';
	}
	if (map) {
		errmsg = errmsg.replace_keywords(map);
	}
	if (!type) {
		type = msgs ? ERR_TO_HTML : ERR_TO_ALERT;
	}
	if ((type & ERR_TO_HTML) && msgs) {
		msgs.innerHTML += errmsg;
		msgs.scrollIntoView(false);
	}
	if (type & ERR_TO_ALERT) {
		alertMessage(errmsg);
	}
}

function selectContents(node) {
	let selection = document.selection ? document.selection : (window.getSelection ? window.getSelection() : false);
	if (!selection) {
		return false;
	}
	let range =
		selection.createRange
	?	selection.createRange()
	:	(	document.createRange
		?	document.createRange()
		:	(	document.body.createTextRange
			?	document.body.createTextRange()
			:	false
			)
		);
	if (!range) {
		return false;
	}
	if (range.selectNodeContents) {
		range.selectNodeContents(node);
	}
	if (selection.removeAllRanges) {
		selection.removeAllRanges();
		selection.addRange(range);
	} else if (range.moveToElementText) {
		range.moveToElementText(node);
	}
	if (range.select) {
		range.select();
	}
	return range;
}
function catchLog(e,extra) {
	let m = '<?= __('js:error:window.onerror_LINE',IS_JAVASCRIPT) ?>\n\n';
	if (!e.stack) {
		if (e.fileName) {
			m += e.fileName + ' @ ' + e.lineNumber + '\n\n';
		} else if (e.sourceURL) {
			m += e.sourceURL + ' @ ' + e.line + '\n\n';
		}
		// NOTE: Deprecated arguments.callee use, when was this invoked?
		// } else if (arguments.callee.caller) {
		// 	m += arguments.callee.caller.toString().substr(0,100).replace(/[\t\r\n\s]+/g,' ') + '\n\n';
		// }
	}
	if (e.message && (!e.stack || -1 === e.stack.indexOf(e.message))) {
		m += e.message + '\n\n';
	}
	if (e.stack) {
		m += e.stack + '\n\n';
	}
	if (extra) {
		m += extra + '\n\n';
	}
	alert(m);
}

/**
 * @param {Window|Document|HTMLElement} elem
 * @param {Array|string} event_arg
 * @param {(function(Event): void)|(function(): void)} handler
 * @returns {void}
 */
function remevent(elem, event_arg, handler) {
	if (event_arg instanceof Array) {
		for (const event_name of event_arg) {
			remevent(elem, event_name, handler);
		}
		return;
	}
	if (elem.removeEventListener) {
		elem.removeEventListener(event_arg, handler);
	}
}

/**
 * @param {function(): void} handler
 * @returns {void}
 */
function addreadyevent(handler) {
	if (document.readyState
	&&	(	document.readyState === 'complete'
		||	document.readyState === 'interactive'
		)
	) {
		handler.pffired = true;
		handler();
		return;
	}
	addevent(document, ['DOMContentLoaded', 'load'], function() {
		if (!handler
		||	 handler.pffired
		) {
			return;
		}
		handler.pffired = true;
		handler();
	});
}

/**
 * @param {Window|Document|HTMLElement|Node} element
 * @param {Array|string} event_arg
 * @param {(function(Event): void)|(function(): void)} handler
 * @returns {void}
 */
function addevent(element, event_arg, handler) {
	if (event_arg instanceof Array) {
		for (const event_name of event_arg) {
			addevent(element, event_name, handler);
		}
		return;
	}
	if (event_arg === 'load'
	&&	element === document
	&&	document.readyState === 'complete'
	) {
		handler(null);
		return;
	}
	if (event_arg === 'ready') {
		addreadyevent(handler);
		return;
	}
	element?.addEventListener(event_arg, handler);
}
<?
// do hashChange on ready, earliest possible and Ghostery sometimes borks load event
// do hashChange on load, because images are loaded then. only jump to hash if within 2 seconds ?>

function hashChange() {
	if (Pf.hashed
	&&	Pf.hashed === location.hash
	) {
		if (Pf.hash_dst
		&&	(Date.now() - Pf.hash_time) < 2000
		) {
			Pf.hash_dst.scrollIntoView(location.hash !== 'bottom');
		}
		return;
	}
	if (location.hash === '#_=_') {
		if (history.replaceState) {
			history.replaceState(null, null, location.href.split('#')[0]);
		} else {
			location.hash = '';
		}
		return;
	}
	Pf.hash_dst = false;
	if (location.hash === '#bottom') {
		let b = getobj('bottom');
		if (b) {
			Pf.hashed = location.hash;
			Pf.hash_dst = b;
			Pf.hash_time = Date.now();
			b.scrollIntoView(false);
		}
		return;
	}
	if (location.hash.match(/^#tour,room/)) {
		let b = getobj('tour');
		if (b) {
			Pf.hashed = location.hash;
			Pf.hash_dst = b;
			Pf.hash_time = Date.now();
			b.scrollIntoView(true);
		}
		return;
	}
	if (location.hash.match(/^#m(\d+)(n)?$/)) {
		let cid = RegExp.$1;
		let msg = getobj('m' + cid);
		if (!msg) {
			return;
		}
		Pf.hash_time = Date.now();
		Pf.hashed = location.hash;

		msg = msg.parentNode; <? // <article><div id="m\d+"> ?>
		Pf.hash_dst = msg;

		if (RegExp.$2) {
			let ptr;
			if ((ptr = msg.nextSibling.firstChild)
			&&	ptr.id.match(/^m(\d+)/)
			&&	RegExp.$1 > cid
			||	msg.previousSibling
			&&	(ptr = msg.previousSibling.firstChild)
			&&	ptr.id.match(/^m(\d+)/)
			&&	RegExp.$1 > cid
			) {
				Pf.hash_arrow.insertBefore(ptr.firstChild);
				Pf.hash_dst = ptr;
				Pf.hashed = '#m' + RegExp.$1;
				location.replace(Pf.hashed);
				return;
			}
			msg.scrollIntoView(true);
		}
		Pf.hash_arrow.insertBefore(msg.firstChild.firstChild);

	}
}
function isIterable(obj) {
	// checks for null and undefined
	if (!obj) {
		return false;
	}
	return typeof obj[Symbol.iterator] === 'function';
}
function walkarg(arg, func, ignore) {
	if (!arg) {
		return;
	}
	if (typeof arg === 'string'
	||	arg instanceof Element
	||	!Array.isArray(arg)
	&&	!(arg instanceof NodeList)
	&&	!(arg instanceof HTMLCollection)
	&&	(	typeof arg !== 'object'
		||	typeof arg[Symbol.iterator] !== 'function'
		)
	) {
		arg = [arg];
	}
	for (let item of arg) {
		if (typeof item === 'string') {
			item = getobj(item);
		}
		if (!item) {
			console.trace('walkarg list contains empty item');
			continue;
		}
		func(item);
	}
}

function repclass(arg, className) {
	walkarg(arg, function(obj) {
		if (!obj) {
			console.warn('walkarg encountered an empty obj', arg);
			return;
		}
		obj.className = className;
	});
}

function setclass(arg, set, args, ignore) {
	if (set) {
		addclass(arg, args, ignore);
	} else {
		remclass(arg, args, ignore);
	}
}

function addclass(arg, cls, ignore) {
	if (!arg) {
		return;
	}
	if (cls instanceof Array) {
		let newcls = '';
		for (let clsj of cls) {
			newcls += clsj + ' ';
		}
		cls = newcls;
	}
	walkarg(arg, function(obj){
		if (obj.className) {
			if (!obj.className.match('\\b' + cls + '\\b')) {
				obj.className += ' ' + cls;
			}
		} else {
			obj.className = cls;
		}
	}, ignore);
}

function remclass(arg, cls, ignore) {
	let re = new RegExp("(\\b(\\s*)(" + (cls instanceof Array ? cls.join('|') : cls) + ")(\\s*)\\b)",'g');
	walkarg(arg, function(obj) {
		if (typeof obj?.className === 'undefined') {
			console.warn('remclass.walkarg encountered empty object or missing className');
			return;
		}
		obj.className = obj.className.replace(re, ' ').trim();
	}, ignore);
}

function switchTheme(self) {
	addclass(document.body, 'light');
	let havetheme = self.getAttribute('data-theme');
	let maketheme = havetheme === 'dark' ? 'light' : 'dark';
	let ottr = self.getAttribute('data-other');
	let prevattr;
	if (self.nodeName === 'IMG') {
		prevattr = self.getAttribute('src');
		self.setAttribute('src', ottr);
	} else {
		prevattr = self.innerHTML;
		self.innerHTML = self.getAttribute('data-other');
	}
	self.setAttribute('data-other', prevattr);
	self.setAttribute('data-theme', maketheme);
	if (self.title) {
		let oldtitle = self.title;
		self.title = self.getAttribute('data-title');
		self.setAttribute('data-title', oldtitle);
	}
	let objs = document.getElementsByTagName('link');
	for (let link of objs) {
		if (link.rel === 'stylesheet') {
			if (link.href.match(/\?SWAPTHEME/)) {
				link.href = link.href.replace(/\?SWAPTHEME/, '');
			} else {
				link.href += '?SWAPTHEME';
			}
		}
	}
	let imgs = document.getElementsByTagName('img');
	let re = RegExp('(' + havetheme + ')');
	for (let img of imgs) {
		if (!img.src) {
			continue;
		}
		if (re.test(img.src)) {
			img.src = img.src.replace(re,maketheme);
		}
	}
	set_cookie('FLOCK_SETTHEME', maketheme);
	remclass(document.body, 'light');
}

function urlencode_utf8_to_windows1252(str) {
	if (!str) {
		return [str,false];
	}
	let HEX = '0123456789ABCDEF';
	let result = '';
	let ok = true;
	for (let i = 0; i < str.length; ++i) {
		let ord = str.charCodeAt(i);
		if (ord === 32) {
			result += '+';
		} else if (ord < 256) {
			let chr = str.charAt(i);
			if (chr.match(/[a-zA-Z0-9]/)) {
				result += chr;
			} else {
				result += '%' + HEX.charAt(ord >> 4) + HEX.charAt(ord & 0xF);
			}
		} else {
			// javascript/browser sends unicode chars, so convert to windows1252
			switch (ord) {
			case 8364:	ord = 128; break;
			case 8218:	ord = 130; break;
			case 402:	ord = 131; break;
			case 8222:	ord = 132; break;
			case 8230:	ord = 133; break;
			case 8224:	ord = 134; break;
			case 8225:	ord = 135; break;
			case 710:	ord = 136; break;
			case 8240:	ord = 137; break;
			case 352:	ord = 138; break;
			case 8249:	ord = 139; break;
			case 338:	ord = 140; break;
			case 381:	ord = 142; break;
			case 8216:	ord = 145; break;
			case 8217:	ord = 146; break;
			case 8220:	ord = 147; break;
			case 8221:	ord = 148; break;
			case 8226:	ord = 149; break;
			case 8211:	ord = 150; break;
			case 8212:	ord = 151; break;
			case 732:	ord = 152; break;
			case 8482:	ord = 153; break;
			case 353:	ord = 154; break;
			case 8250:	ord = 155; break;
			case 339:	ord = 156; break;
			case 382:	ord = 158; break;
			case 376:	ord = 159; break;
			case 8201:	ord = 0; break;
			default:	ord = 0; ok = false; break;
			}
			if (ord) {
				result += '%' + HEX.charAt(ord >> 4) + HEX.charAt(ord & 0xF);
			}
		}
	}
	return [result,ok];
}

function alertMessage(txt) {
	let new_div = document.createElement('div');
	new_div.innerHTML = txt.replace(/<br\s*\/?>/g, "\n");
	alert(new_div.textContent ? new_div.textContent : new_div.innerText);
}

/**
 * @param {MouseEvent} event
 * @param {string|HTMLElement} url
 * @param {boolean} [stop]
 * @param {boolean} [new_tab]
 * @returns {boolean}
 */
function openLink(event, url, stop, new_tab) {
	if (typeof url !== 'string') {
		/** @var {Node} result_node */
		let result_node = url;
		while (
			result_node
		//&& result_node.nodeName !== 'A'
		&&	!(result_node instanceof HTMLAnchorElement)
		) {
			result_node = result_node.firstChild ?? result_node.nextSibling;
		}
		if (!result_node) {
			console.warn('openLink: no child with href found');
			return false;
		}
		/** @var {HTMLAnchorElement} anchor */
		const anchor = result_node;
		url = anchor.href;
	}
	if (stop) {
		event.stopPropagation();
	}
	if (event.shiftKey
	||	event.ctrlKey
	||	event.metaKey
	||	event.altKey
	||	new_tab
	) {
		window.open(url, '_blank');
	} else {
		location.href = url;
	}
	return false;
}

/**
 * @param {HTMLElement} obj
 * @returns {number[]}
 */
function findPos(obj) {
	let current_left = 0;
	let current_top  = 0;
	if (obj.offsetParent) {
		do {
			current_left += obj.offsetLeft;
			current_top  += obj.offsetTop;
		}
		while (obj = obj.offsetParent);
	}
	return [current_left, current_top];
}

function setattr(arg) {
	let args = arguments;
	walkarg(arg, function(obj) {
		for (let i = 1; i < args.length; i += 2) {
			const key = args[i];
			const val = args[i + 1];
			if (typeof val === 'boolean') {
				if (val) {
					obj.setAttribute(key, 'true');
				} else {
					obj.removeAttribute(key);
				}
			} else {
				obj.setAttribute(key, val);
			}
			if (key === 'data-valid') {
				if (!val) {
					obj.type = 'text';
				} else {
					switch (val) {
					case 'number':
					case 'email':
					case 'url':
					case 'tel':
						obj.type = val;
						break;
					}
				}
			}
		}
	});
}

RegExp.escape = function(text) {
	return text.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, "\\$&");
};

Pf.markOnePageLinks = function() {
	if (-1 === document.body.className.indexOf('isadmin')) {
		return;
	}
	let anchors = document.getElementsByTagName('A');
	for (let a of anchors) {
		if (a.href.match(/\/latest\//)) {
			a.className += ' onepage';
		}
	}
};

addevent(window,['load','hashchange'],hashChange);
addreadyevent(function(){
	Pf.smallScreen = (document.body ? document.body : document.documentElement).getAttribute('data-small-screen');
	hashChange();
});

if (!Array.prototype.equals) {
	Array.prototype.equals = function(array) {
		array = array.sort();

		return	this.length === array.length
			&&	this.sort().every(function(value, index) { return value === array[index]} );
	};
}

let Pf_empties;
let Pf_is_empty;
function init_empties() {
	Pf_empties	??= '\\n\\r\\t\\s\\u00A0\\u00AD\\p{Z}\\p{C}';
	Pf_is_empty ??= new RegExp(`^[${Pf_empties}]*(?:[0${Pf_empties}]*)?\$`);
}
String.prototype.is_empty = function() {
	if (!this.length) {
		return true;
	}
	init_empties();
	return Pf_is_empty.test(this);
};

String.prototype.mytrim = function() {
	if (!this.length) {
		return this;
	}
	init_empties();
	return this.
		replace(RegExp(`^${Pf_empties}+`, 'g'), '').
		replace(RegExp(`${Pf_empties}+$`, 'g'), '');
};
