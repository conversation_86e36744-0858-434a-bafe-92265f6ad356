function resizy(obj) {
	let content = obj.getAttribute('data-content');
	if (content) {
		obj.removeAttribute('data-content');
	}
	actual_resizy(obj);
	if (content) {
		obj.innerHTML = content;
	}
}
function actual_resizy(obj) {
	let	 w = getWindowInfo();

	let padding = w.h < 900 ? 5 : 30;
	let maxScale = obj.getAttribute('data-max-width');
	let minScale = obj.getAttribute('data-min-width');
	if (!minScale) minScale = .6;
	if (!maxScale) maxScale = 1;
	let parent = obj.parentNode;
	let ow = obj.offsetWidth;
	let parentWidth = parent.offsetWidth;
	if (!ow || !parentWidth) return;

	let pos = findPos(obj);
	let aspect = obj.getAttribute('data-aspect');
	if (!aspect) aspect = 16/9;

	let wantw = parentWidth * maxScale;
	let wanth = wantw / aspect;
	
	if (pos[1] - w.sy < padding) {
		return;
	}
	let maxh = w.h - (pos[1] - w.sy) - padding;
	if (wanth > maxh) {
		wanth = maxh;
		wantw = wanth * aspect;
		maxScale = wantw / parentWidth;
	}
	if (wantw < minScale * parentWidth) {
		wantw = minScale * parentWidth;
		wanth = wantw / aspect;
	}
	let nw = 100 * wantw / parentWidth;
	let nh = wanth;
	
	obj.style.width = nw + '%';
	obj.style.height = nh + 'px';
	if (obj.getAttribute('width')) {
		obj.setAttribute('width',nw+'%');
		obj.setAttribute('height',nh);
	}
}
