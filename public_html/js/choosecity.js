function changeCountry(self, flags, prefix, postfix) {
	let countryid = self.value;
	let creq = initrequest(true);
	if (!creq) { 
		return;
	}
	if (!flags) {
		flags = 0;
	}
	
	Pf.locType.init(self.form);
	let typecnt = Pf.locType.cnt();
	let boat = Pf.locType.boat.checked && typecnt === 1;
	if (boat) {
		// remove CITY_REQUIRED flag
		flags &= ~0x40;
	}
	
	creq.open(
		'GET',
		'/include/cities.inc?' + 
			'COUNTRYID=' + countryid +
			'&FLAGS=' + flags +
			(prefix ? '&PREFIX=' + prefix : '') +
			(postfix ? '&POSTFIX=' + postfix : ''),
		true
	);

	creq.onreadystatechange = function() {
		if (creq.readyState !== 4) {
			return;
		}
		if (creq.status === 200
		||	creq.status === 204
		) {
			let newpart = document.createElement('div');
			newpart.innerHTML = creq.responseText;

			let selectobj = getobj(prefix + 'cityselect' + postfix);
			if (selectobj) {
				if (selectobj.firstChild) {
					if (newpart.firstChild.firstChild) {
						selectobj.replaceChild(newpart.firstChild.firstChild, selectobj.firstChild);
					} else {
						selectobj.removeChild(selectobj.firstChild);
					}
				} else {
					selectobj.appendChild(newpart.firstChild.firstChild);
				}
			}
		} else {
			showerrors(creq);
		}
	}
	creq.send(null);
}
