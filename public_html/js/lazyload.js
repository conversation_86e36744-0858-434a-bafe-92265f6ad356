function Pf_initializeLazies(container) {
	const divs = getobjs_byclass('lazycontainer', 'DIV', container);
	if (!divs.length) {
		return;
	}
	const allNodes = [];
	let totalLeft = 0;
	for (const div of divs) {
		const nodes = getobjs_byclass('lazyload', 'DIV', div);
		if (!nodes.length) {
			continue;
		}
		for (let node of nodes) {
			allNodes.push(node);
		}
		totalLeft += nodes.length;
	}
	if (allNodes.length) {
		const div = {
			Pf_nodes: 		allNodes,
			Pf_nodesLeft:	totalLeft
		};
		div.Pf_scrollHandler = function() {
			Pf_showLazies(div);
		};
		div.Pf_resizeHandler = function() {
			const refPos = findPos(div);
			Pf_determineOffsets(allNodes, refPos);
			Pf_showLazies(div);
		};
		addevent(window, 'scroll', div.Pf_scrollHandler);
		addevent(window, 'resize', div.Pf_resizeHandler);
		div.Pf_resizeHandler();
	}
};

function Pf_determineOffsets(nodes, refpos){
	for (const node of nodes) {
		let node_position = findPos(node);
		node.Pf_offset = node_position[1] - refpos[1];
		node.Pf_endset = node.Pf_offset + node.clientHeight;
	}
}

function Pf_showLazies(div) {
	if (!div.Pf_nodesLeft) {
		return;
	}
	const w = getWindowInfo();
	let foldStart = w.sy - (2 * w.h);
	if (foldStart < 0) {
		foldStart = 0;
	}
	const foldStop = w.sy + (2 * w.h);
	Pf_showLaziesBetween(div, foldStart, foldStop);
}

function Pf_showLaziesBetween(div, foldStart, foldStop) {
	for (let node of div.Pf_nodes) {
		if (node.Pf_loaded) {
			continue;
		}
		if (node.Pf_offset >= foldStart
		&&	node.Pf_offset <= foldStop
		||	node.Pf_endset >= foldStart
		&&	node.Pf_endset <= foldStop
		) {
			let html = node.getAttribute('data-lazy-html');
			if (html) {
				node.innerHTML = node.getAttribute('data-lazy-html');
				node.removeAttribute('data-lazy-html');
			} else {
				const style = node.getAttribute('data-lazy-style');
				if (style) {
					console.log('data-lazy-style: ' + style);
					node.style.cssText += style;
				} else {
					let str;
					if (str = node.getAttribute('data-ipstr')) {
						Pf_fill_ip(node, str, false, node.getAttribute('data-fallback'));
					} else if (str = node.getAttribute('data-netstr')) {
						Pf_fill_ip(node, str, true);
					}
				}
			}
			node.Pf_loaded = true;

			if (!--div.Pf_nodesLeft) {
				remevent(window, 'scroll', div.Pf_scrollHandler);
				remevent(window, 'resize', div.Pf_resizeHandler);
				break;
			}
		}
	}
}
addreadyevent(function() {
	Pf_initializeLazies();
});
