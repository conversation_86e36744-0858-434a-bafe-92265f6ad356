function markSeen(userids, emails) {
	let sreq = initrequest(true);
	if (!sreq) {
		return;
	}
	sreq.open('POST', '/contact.php?ACTION=markcontestsseen', true);
	sreq.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
	sreq.onreadystatechange = function() {
		if (sreq.readyState !== 4) {
			return;
		}
		if (sreq.status === 200) {
			hide('contestseen');
		} else {
			alert('status: ' + sreq.status);
		}
	};
	let poststr;
	if (userids) {
		for (let userid of userids) {
			poststr = poststr ? poststr + '&' : '';
			poststr += 'USERID[]=' + userid;
		}
	}
	if (emails) {
		for (let email of emails) {
			poststr = poststr ? poststr + '&' : '';
			poststr += 'EMAIL[]=' + email;
		}
	}
	sreq.send(poststr);
}
