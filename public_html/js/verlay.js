addreadyevent(function() {
	let verlay = getobj('verlay');
	if (!verlay) {
		return;
	}
	let verlay_content = getobj(verlay.getAttribute('data-content'));
	if (verlay_content?.nodeName === 'IMG') {
		let width =  verlay.getAttribute('data-width');
		let height = verlay.getAttribute('data-height');
		let win = getWindowInfo();
		if (width && height && win) {
			if (width > win.w) {
				let new_width = win.w - 20;
				height *= new_width / width;
				width = new_width;
			}
			if (height > win.h) {
				let new_height = win.h - 20;
				width *= new_height / height;
			}
			verlay_content.style.maxWidth = Math.floor(width) + 'px';
		}
		verlay_content.src = verlay_content.getAttribute('data-src');
	}
	unhide(verlay);
});
