function Pf_fill_ip(cell, ipstr, net, fallback) {
	let req = initrequest(true);
	if (!req) {
		return;
	}
	req.open('GET',net ? '/netname.obj?NETSTR=' + ipstr : '/hostname.obj?IPSTR=' + ipstr + (fallback ? ';FB' : ''),true);
	req.onreadystatechange = function() {
		if (req.readyState !== 4
		||	req.status !== 200
		||	!req.responseText
		) {
			return;
		}
		remclass(cell,'light');
		cell.innerHTML = (cell.innerHTML === req.responseText) ? '-' : req.responseText;
	};
	req.send(null);
}

addreadyevent(function() {
	let table = getobj('iptable', true);
	if (!table) {
		return;
	}
	if (table.className.match(/\blazycontainer\b/)) {
		// done in lazyload.js
		// seem these can be combined
		return;
	}
	for (let row of table.rows) {
		for (let cell of row.cells) {
			let str;
			if (str = cell.getAttribute('data-ipstr')) {
				Pf_fill_ip(cell, str, false, cell.getAttribute('data-fallback'));
			} else if (str = cell.getAttribute('data-netstr')) {
				Pf_fill_ip(cell, str, true);
			}
		}
	}
});
