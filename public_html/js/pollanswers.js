let maxanswers = 15;

function poll_answer_add() {
	let answercntobj = getobj('answercnt');
	if (!answercntobj) {
		return;
	}
	if (answercntobj.value >= maxanswers) {
		alert('Je mag maximaal ' + maxanswers + ' antwoorden gebruiken');
		return;
	}
	let pollanswersobj = getobj('pollanswers');
	if (!pollanswersobj) {
		return;
	}
	let answercnt = answercntobj.value;
	let lastdown = getobj('down' + (answercnt - 1));
	if (!lastdown) {
		return;
	}
	let firstspan = getobj('answerspan0');
	if (!firstspan) {
		return;
	}
	let newspan = firstspan.cloneNode(true);
	if (!newspan) {
		return;
	}
	newspan.id = 'answerspan' + answercnt;
	for (let child of newspan.childNodes) {
		if (child.id === 'updown0') {
			child.id = 'updown' + answercnt;
			for (let subchild of child.childNodes) {
				if (subchild.id === 'up0') {
					subchild.id = 'up' + answercnt;
					subchild.className = 'ptr';

				} else if (subchild.id === 'down0') {
					subchild.id = 'down' + answercnt;
					subchild.className = 'light';
				}
			}
		} else if (child.nodeName === 'INPUT') {
			child.name = 'ANSWER[0][]';
			child.value = '';
		}
	}
	lastdown.className = 'ptr';
	pollanswersobj.appendChild(newspan);
	if (++answercntobj.value >= maxanswers) {
		hide('addanswer');
	}
}

function poll_answer_move(self, up) {
	let id = self.id;
	if (self.className === 'light'
	||	!id.match(/(\d+)/)
	) {
		return;
	}
	let answercntobj = getobj('answercnt');
	if (!answercntobj) {
		return;
	}
	id = Number(RegExp.$1);
	if (up ? !id : id >= answercntobj.value - 1) {
		return;
	}
	let a = getobj('answerspan' + (id + (up ? -1 : 1)), true);
	if (!a) return;
	let b = getobj('answerspan' + id, true);
	if (!b) return;
	let store = a.firstChild.name;
	a.firstChild.name = b.firstChild.name;
	b.firstChild.name = store;
	
	store = a.firstChild.value;
	a.firstChild.value = b.firstChild.value;
	b.firstChild.value = store;
}
