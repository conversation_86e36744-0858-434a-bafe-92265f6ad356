function check_removable(self, element, id) {
	self.onmouseover = null;
	let creq = initrequest(true);
	if (!creq) {
		return;
	}

	creq.open('POST', '/checkremovable.obj', true);
	creq.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
	creq.onreadystatechange = function () {
		if (creq.readyState !== 4) {
			return;
		}
		switch (creq.status) {
		case 405:
		case 200:
			remclass(self, 'light');
			self.parentNode.innerHTML = creq.responseText;
			break;
		case 0:
			break;
		default:
			alert('HTTP status ' + creq.status);
			break;
		}
	};
	creq.send('sELEMENT=' + element + '&sID=' + id);
}
