<?php

declare(strict_types=1);

require_once '_browser.inc';

function show_swiffy_html(array $banner): void {
	?><!DOCTYPE html><?
	?><html lang="en"><?
	?><head><?
	?><meta charset="utf-8" /><?
	?><base target="_blank"><?
	?><title>swiffy:<?= $banner['BANNERID'] ?></title><?
	?><script src="<?= STATIC_HOST ?>/swiffy/v7.3.0/runtime.js"></script><?
	?><script>let swiffy_object = <?= $banner['DATA'] ?>;</script><?
	?><style>html, body { width: 100%; height: 100%; }</style><?
	?></head><?
	?><body style="margin: 0; overflow: hidden; background-color: black;"><?
	?><div id="swiffy-container" style="width: <?= $banner['WIDTH'] ?>px; height: <?= $banner['HEIGHT'] ?>px;"><?
	?></div><?

	?><script>
		// noinspection JSUnresolvedReference
		let stage = new swiffy.Stage(document.getElementById('swiffy-container'), swiffy_object, {});
		stage.start();
	</script><?
	?></body><?
	?></html><?

}
