<?php

function display_body() {
	switch ($_REQUEST['ACTION']) {
	case null:
	default:		not_found(); return;
	case 'archive':
	case 'cities':
#	case 'users':
	case 'locations':
	case 'boardings':
#	case 'organizations':
#	case 'comments':
#	case 'comment':
#		robot_action('noindex');
	case 'single':
		return region_display_single();
	}
}
function region_display_single() {
	if (!require_idnumber($_REQUEST,'sID')) {
		return;
	}
	$regionid = $_REQUEST['sID'];
	$region = db_single_assoc('region','
		SELECT COUNTRYID,LATITUDE,LONGITUDE,ZOOM
		FROM region
		WHERE REGIONID='.$regionid
	);
	if ($region === false) {
		return;
	}
	if (!$region) {
		register_error('region:error:nonexistent_LINE',['ID'=>$regionid]);
		return;
	}
	$region['NAME'] = get_element_title('region',$regionid);

	layout_show_section_header();

	$location_cnt = memcached_single(['location','city'],'SELECT COUNT(*) FROM location JOIN city USING (CITYID) WHERE REGIONID='.$regionid);
	if ($location_cnt === false) {
		return;
	}
	$boarding_cnt = memcached_single(['boarding','city'],'SELECT COUNT(*) FROM boarding JOIN city USING (CITYID) WHERE REGIONID='.$regionid);
	if ($boarding_cnt === false) {
		return;
	}
	$cities = memcached_simpler_array('city','SELECT CITYID FROM city WHERE REGIONID='.$regionid);
	$city_cnt = count($cities);
	$cityidstr = $cities ? implode(',',$cities) : -1;
	$cnts = memcached_single_assoc(
		array('party','location','boarding'), '
		SELECT	COUNT(IF(STAMP >= '.TODAYSTAMP.',1,NULL)) AS FUTURE_CNT,
			COUNT(IF(STAMP >= '.TODAYSTAMP.' AND party.ACCEPTED=1,1,NULL)) AS FUTURE_OK_CNT,
			COUNT(*) AS TOTAL_CNT,
			COUNT(IF(party.ACCEPTED=1,1,NULL)) AS TOTAL_OK_CNT
		FROM party
		JOIN (	SELECT PARTYID
			FROM party
			WHERE CITYID IN ('.$cityidstr.')
			UNION
			SELECT PARTYID
			FROM party
			JOIN location USING (LOCATIONID)
			WHERE BOARDINGID=0
			  AND location.CITYID IN ('.$cityidstr.')
			UNION
			SELECT PARTYID
			FROM party
			JOIN boarding USING (BOARDINGID)
			WHERE boarding.CITYID IN ('.$cityidstr.')
		) AS party_city USING (PARTYID)',
		TEN_MINUTES
	);
	if (!$cnts) {
		return;
	}
	if (!have_admin('party')) {
		$cnts['TOTAL_CNT'] = $cnts['TOTAL_OK_CNT'];
		$cnts['FUTURE_CNT'] = $cnts['FUTURE_OK_CNT'];
	}
	$cnts['PAST_CNT'] = $cnts['TOTAL_CNT'] - $cnts['FUTURE_CNT'];
	layout_open_box('city');
	layout_open_box_header();
	?><h1 itemprop="name"><?
	echo escape_specials(get_element_title('region',$regionid));
	?></h1><?
/*	if ($city_cnt
	||	$user_cnt
	||	$cnts['PAST_CNT']
	||	$location_cnt
	) {
		$provhref = '/region/'.$regionid;
		layout_continue_box_header();
		layout_open_menu(MENU_IN_HEADER);
		if ($city_cnt) {
			layout_menuitem(element_plural_name('city'),$provhref.'/cities#cities',$_REQUEST['ACTION'] == 'cities');
		}
		if ($user_cnt <= 50000) {
			layout_menuitem(element_plural_name('user'),$provhref.'/users#users',$_REQUEST['ACTION'] == 'users');
		}
		if ($location_cnt) {
			layout_menuitem(element_plural_name('location'),$provhref.'/locations#locations',$_REQUEST['ACTION'] == 'locations');
		}
		if ($cnts['PAST_CNT']) {
			layout_menuitem(element_name('archive'),$provhref.'/archive#archive',$_REQUEST['ACTION'] == 'archive');
		}
		layout_close_menu();
	}*/

	layout_close_box_header();

	require_once '_google_maps.inc';
	show_mini_google_map('region',$regionid,$region);

	$mapping =	($region['LONGITUDE']+0)
		&&	($region['LATITUDE']+0);

	if ($mapping) {
		?><div class="l"><?
	}

	require_once '_countryflag.inc';

	$list = new deflist('deflist vtop');
	$list->add_row(Eelement_name('country'), get_element_link('country',$region['COUNTRYID']).' '.get_country_flag($region['COUNTRYID'],'light'));
	$list->display();

	if ($mapping) {
		require_once '_maplink.inc';
		?></div><div class="l lmrgn"><?
		show_map_link($region,$region['ZOOM']);
		?></div><?
		?><div class="lclr"></div><?
	}

	require_once '_affiliates.inc';
	show_booking_com('region',$regionid,$region);
#	show_worldticketcenter('region',$regionid);

	layout_close_box();

	ob_start();
	layout_open_table(TABLE_FULL_WIDTH);

	# CITIES
	if ($city_cnt) {
		list($linkopen,$linkclose) = get_action_open_close('cities');
	} else {
		$linkopen = $linkclose = null;
	}
	$none = __('counter:none');
	layout_start_reverse_row($city_cnt ? 0 : ROW_LIGHT);
	echo $linkopen,$city_cnt ? $city_cnt : $none,$linkclose;
	layout_value_field();
	echo $linkopen,element_name('city',$city_cnt),$linkclose;

	# LOCATIONS
	if ($location_cnt) {
		list($linkopen,$linkclose) = get_action_open_close('locations');
	} else {
		$linkopen = $linkclose = null;
	}
	layout_restart_reverse_row($location_cnt ? 0 : ROW_LIGHT);
	echo $linkopen,$location_cnt ? $location_cnt : $none,$linkclose;
	layout_value_field();
	echo $linkopen,element_name('location',$location_cnt),$linkclose;

	# BOARDINGS
	if ($boarding_cnt) {
		list($linkopen,$linkclose) = get_action_open_close('boardings');
	} else {
		$linkopen = $linkclose = null;
	}
	layout_restart_reverse_row($boarding_cnt ? 0 : ROW_LIGHT);
	echo $linkopen,$boarding_cnt ? $boarding_cnt : $none,$linkclose;
	layout_value_field();
	echo $linkopen,element_name('boarding',$boarding_cnt),$linkclose;

	# EVENTS
	if ($cnts['TOTAL_CNT']) {
		layout_restart_reverse_row();
		echo $cnts['TOTAL_CNT'];
		layout_value_field();
		echo element_name('party',$cnts['TOTAL_CNT']);
	}
	if ($cnts['FUTURE_CNT']) {
#		if ($_REQUEST['ACTION'] != 'single') {
#			$linkopen  = '<a href="'.get_element_href('region',$regionid,$region['NAME']).'">';
#			$linkclose = '</a>';
#		} else {
#			$linkopen = $linkclose = null;
#		}
		layout_restart_reverse_row();
		echo $linkopen,$cnts['FUTURE_CNT'],$linkclose;
		layout_value_field();
		echo $linkopen,__('when:in_the_future'),$linkclose;
	}
	if ($cnts['PAST_CNT']) {
		list($linkopen,$linkclose) = get_action_open_close('archive');
		layout_restart_reverse_row();
		echo $linkopen,$cnts['PAST_CNT'],$linkclose;
		layout_value_field();
		echo $linkopen,__('when:in_the_past'),$linkclose;
	}
	layout_stop_row();
	layout_close_table();
	$stats = ob_get_clean();

	layout_open_box('city');
	if (SMALL_SCREEN) {
		layout_box_header(Eelement_plural_name('statistic'));
		echo $stats;
	}

	layout_box_header(Eelement_name('agenda'));

	require_once '_partylist.inc';
	$partylist = new _partylist;
	$partylist->in_cities($cityidstr);
	$partylist->select_future();
	$partylist->show_date =
	$partylist->hide_subtitle =
	$partylist->show_location =
	$partylist->show_city =
	$partylist->hide_flag =
	$partylist->show_stars =
	$partylist->show_date_headers =
	$partylist->show_buddy_hearts = true;
	$partylist->order_chronologically_but_notime();
	if (!$partylist->query()) {
		return;
	}
	if ($partylist->have_rows()) {
		$partylist->display();
	}
	unset($partylist);

	if (!SMALL_SCREEN) {
		layout_continue_box(1/3);
		layout_box_header(Eelement_plural_name('statistic'));
		echo $stats;
	}



	layout_close_box();

	switch ($_REQUEST['ACTION']) {
	case 'cities':
		require_once '_citylist.inc';
		$citylist = new _citylist;
		$citylist->in_region($regionid);
		if ($citylist->query()) {
			layout_open_box('city','cities');
			layout_box_header(Eelement_plural_name('city'));
			$citylist->display();
			layout_close_box();
		}
		unset($citylist);
		break;

/*	case 'users':
		if ($user_cnt > 50000) {
			?><p><?= __('region:info:too_many_users_for_sensible_overview_LINE'); ?></p><?
			break;
		}
		require_once '_userlist.inc';
		$userlist = new _userlist;
		$userlist->show_camera = true;
		$userlist->show_heart = true;
		$userlist->in_region($regionid);
		if ($userlist->query()) {
			layout_open_box('city','users');
			layout_box_header(Eelement_plural_name('user'));
			$userlist->display();
			layout_close_box();
		}
		unset($userlist);
		break;*/

	case 'locations':
		require_once '_locationlist.inc';
		$locationlist = new _locationlist;
		$locationlist->in_cities($cityidstr);
		$locationlist->show_header = true;
		$locationlist->show_city = true;
		$locationlist->show_country = false;
		$locationlist->show_address = false;
		$locationlist->order_by_city_and_name();
		$locationlist->select_future();
		layout_open_box('city','locations');
		if ($locationlist->query()) {
			layout_box_header(Eelement_plural_name('location'));
			$locationlist->display();
		}
		$locationlist->clear();
		$locationlist->in_cities($cityidstr);
		$locationlist->select_past();
		$locationlist->query();
		if (!$locationlist->is_empty()) {
			layout_box_header(Eelement_plural_name('inactive_location'));
			$locationlist->display();
		}
		layout_close_box();
		unset($locationlist);
		break;

	case 'boardings':
		require_once '_boardinglist.inc';
		$boardinglist = new boardinglist;
		$boardinglist->in_cities($cityidstr);
		$boardinglist->show_header = true;
		$boardinglist->show_city = true;
		$boardinglist->show_country = false;
		$boardinglist->show_address = false;
		$boardinglist->order_by_city_and_name();
		if ($boardinglist->query()) {
			layout_open_box('city','boardings');
			layout_box_header(Eelement_plural_name('boarding'));
			$boardinglist->display();
			layout_close_box();
		}
		unset($boardinglist);
		break;

	case 'archive':
		$partylist = new _partylist;
		$partylist->in_cities($cityidstr);
		$partylist->select_past();
		$partylist->show_header = true;
		$partylist->show_subtitle = false;
		$partylist->show_city = true;
		$partylist->show_date = true;
		$partylist->show_location = true;
		$partylist->order_reverse_chronologically_but_notime();
		if ($partylist->query()) {
			layout_open_box('city','archive');
			layout_box_header(Eelement_name('archive'));
			$partylist->display();
			layout_close_box();
		}
		unset($partylist);
		break;

/*	case 'organizations':
		require_once '_organizationlist.inc';
		$organizationlist = new _organizationlist;
		$organizationlist->in_region($regionid);
		$organizationlist->openbox = false;
		$organizationlist->show_header = true;
		$organizationlist->order_by_city_and_name();
		if ($organizationlist->query()) {
			layout_open_box('city','organizations');
			layout_box_header(Eelement_plural_name('organization'));
			$organizationlist->display();
			layout_close_box();
		}
		unset($organizationlist);
		break;
*/
	}
/*	if (isset($showcmts)) {
		require_once '_commentlist.inc';
		$cmts = new _commentlist;
		$cmts->item($region);
		$cmts->display();
	}*/
}
