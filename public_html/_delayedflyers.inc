<?php

# true:  delayed
# false: error
# null:	 not delayed

function get_delayed_flyers(): array|false|null {
	return memcached_simple_hash('party','
		SELECT PARTYID, FLYER_PSTAMP
		FROM party
		WHERE STAMP>'.TODAYSTAMP.'
		  AND FLYER_PSTAMP!=0',
		FIVE_MINUTES,
		'delayed_flyers'
	);
}

# true:  success
# false: error

function set_delayed_flyer(int $partyid, int $pstamp): bool {
	if (false === ($flyers = memcached_get('delayed_flyers'))) {
		return false;
	}
	if ($pstamp) {
		$flyers[$partyid] = $pstamp;
	} else {
		unset($flyers[$partyid]);
	}
	# update identical flyers
	if (false === ($upimgids = db_simpler_array('uploadimage_link','
		SELECT UPIMGID
		FROM uploadimage_link
		WHERE TYPE IN ("party","party2")
		  AND ID = '.$partyid))
	) {
		return false;
	}
	if ($upimgids) {
		if (false === ($partyids = db_simpler_array(['party', 'uploadimage_link'],"
			SELECT DISTINCT ID
			FROM uploadimage_link
			JOIN party ON PARTYID = ID
			WHERE ID != $partyid
			  AND FLYER_PSTAMP != $pstamp
			  AND UPIMGID IN (".implode(',',$upimgids).')'))
		) {
			return false;
		}
		if ($partyids) {
			if (!db_insert('party_log','
				INSERT INTO party_log
				SELECT * FROM party
				WHERE PARTYID IN ('.implode(',',$partyids).')')
			) {
				return false;
			}
			require_once '_elementchanged.inc';
			foreach ($partyids as $local_partyid) {
				if (!db_update('party', '
					UPDATE party SET
						MUSERID		 = '.CURRENTUSERID.',
						MSTAMP		 = '.CURRENTSTAMP.",
						FLYER_PSTAMP = $pstamp
					WHERE PARTYID = $local_partyid
					  AND FLYER_PSTAMP != $pstamp")
				) {
					return false;
				}
				if (db_affected()) {
					element_changed('party',$local_partyid);
				}
				if ($pstamp) {
					$flyers[$local_partyid] = $pstamp;
				} else {
					unset($flyers[$local_partyid]);
				}
			}
		}
	}
	memcached_set('delayed_flyers', $flyers, FIVE_MINUTES);
	return true;
}

# int:	 delayed, max publication stamp
# false: error
# null:	 not delayed

function is_delayed_flyer(int $upimgid): int|false|null {
	if (!($delayed = get_delayed_flyers())) {
		return $delayed;
	}
	if (!($connects = memcached_boolean_hash('uploadimage_link', "
		SELECT ID
		FROM uploadimage_link
		WHERE UPIMGID = $upimgid
		  AND TYPE IN ('party', 'party2')"))
	) {
		if ($connects === false) {
			return null;
		}
		return false;
	}
	if (!($intersect = array_intersect_key($delayed, $connects))) {
		return null;
	}
	$max_pstamp = 0;
	foreach ($intersect as /* $partyid => */ $pstamp) {
		if ($pstamp > CURRENTSTAMP
		&&	$pstamp > $max_pstamp
		) {
			$max_pstamp = $pstamp;
		}
	}
	return $max_pstamp ?: null;
}
