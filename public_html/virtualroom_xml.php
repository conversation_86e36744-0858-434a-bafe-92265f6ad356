<?php

define('CURRENTSTAMP', time());

require_once '_exit_if_offline.inc';
require_once '_require.inc';
require_once '_hosts.inc';
require_once '_db.inc';
require_once '_counthit.inc';
require_once '_identity.inc';

function bail(int $errno): never {
	http_response_code($errno);
	exit($errno !== 200 ? 1 : 0);
}

if (!($id = have_idnumber($_SERVER,'eID'))) {
	bail(404);
}
$licenseid = $_SERVER['eLICENSE'] ? $id : 0;
if (!($roomid = have_idnumber($_SERVER,'eROOMID'))) {
	bail(404);
}

if (!(	$room
	=	$licenseid
	?	db_single_array(['virtualroom', 'vtlicense'], "
			SELECT TOURID, LEVELS, TILESIZE, VIEWV, VIEWH, ROOMS
			FROM virtualroom
			JOIN vtlicense USING (TOURID)
			WHERE LICENSEID = $licenseid
			  AND ACTIVE
			  AND ROOMID = $roomid")
	:	db_single_array('virtualroom', "
			SELECT TOURID, LEVELS, TILESIZE, VIEWV, VIEWH, NULL
			FROM virtualroom
			WHERE TOURID = $id
			  AND ROOMID = $roomid")
)) {
	bail($room === false ? 503 : 404);
}

[$tourid, $levels, $tilesize, $viewv, $viewh, $rooms] = $room;

$etag = serialize($room);

if (!($levels = db_simple_hash('virtuallevel', "
	SELECT LEVEL, TILESIZE
	FROM virtuallevel
	WHERE TOURID = $tourid
	  AND ROOMID = $roomid
	ORDER BY TILESIZE DESC"
))) {
	bail($levels === false ? 503 : 404);
}

$etag .= "\x1F".serialize($levels);

if (false === ($hotspots = db_rowuse_hash(['virtualroom', 'virtualhotspot'], "
	SELECT SPOTID, DSTID, NAME, HCENTER, VCENTER
	FROM virtualhotspot AS hs
	JOIN virtualroom AS vr ON hs.TOURID = vr.TOURID AND hs.DSTID = vr.ROOMID
	WHERE hs.TOURID = $tourid
	  AND hs.ROOMID = $roomid".
	  ($rooms ? ' AND hs.DSTID IN ('.addslashes($rooms).')' : '')
))) {
	bail(503);
}
if ($hotspots) {
	if (false === ($points = db_multirowuse_hash('virtualhotspotpoint','
		SELECT SPOTID,ATH,ATV
		FROM virtualhotspotpoint
		WHERE SPOTID IN ('.implodekeys(',',$hotspots).')
		ORDER BY POINTID'
	))) {
		bail(503);
	}
	$etag .= "\x1F".serialize($points);

}
$etag .= "\x1F".serialize($hotspots);

counthit('virtualxml',$tourid,$roomid);

require_once '_modified.inc';

header('Cache-Control: public,must-revalidate');

if (not_modified(filemtime($_SERVER['SCRIPT_FILENAME']), hash('xxh128', $etag))) {
	bail(304);
}

header('Content-Type: application/xml; charset=utf-8');

echo '<?xml version="1.0" encoding="UTF-8"?>';

?><krpano version="1.6.13"<?
if (SERVER_SANDBOX
||	HOME_OFFICE
) {
	?> logkey="true"<?
	?> showerrors="true"<?
	?> debugmode="true"<?
} else {
	?> logkey="false"<?
}
?>><?
	?><panoview h="<?= $viewh ?>" v="<?= $viewv ?>" fov="90" /><?
	?><view fisheye="0" limitview="range" hlookatmin="-180" hlookatmax="180" vlookatmin="-90" vlookatmax="90" maxpixelzoom="1.0" fovmax="112.5" fov="70" hlookat="0" vlookat="0" /><?
	?><autorotate horizon="0" tofov="70" waittime="2" speed="5" /><?
	?><preview url="<?= VT_HOST ?>/vt/<?= $tourid ?>/<?= $roomid ?>/preview.jpg" type="CUBESTRIP" striporder="FRBLUD" details="16" /><?
	?><image type="CUBE" multires="true" tilesize="<?= $tilesize ?>" baseindex="0"><?
	foreach ($levels as $level => $tilesize) {
		?><level tiledimagewidth="<?= $tilesize ?>" tiledimageheight="<?= $tilesize ?>"><?
			?><left url="<?= VT_HOST ?>/vt/<?= $tourid ?>/<?= $roomid ?>/3/<?= $level ?>/%v_%u.jpg"/><?
			?><front url="<?= VT_HOST ?>/vt/<?= $tourid ?>/<?= $roomid ?>/0/<?= $level ?>/%v_%u.jpg"/><?
			?><right url="<?= VT_HOST ?>/vt/<?= $tourid ?>/<?= $roomid ?>/1/<?= $level ?>/%v_%u.jpg"/><?
			?><back url="<?= VT_HOST ?>/vt/<?= $tourid ?>/<?= $roomid ?>/2/<?= $level ?>/%v_%u.jpg"/><?
			?><up url="<?= VT_HOST ?>/vt/<?= $tourid ?>/<?= $roomid ?>/4/<?= $level ?>/%v_%u.jpg"/><?
			?><down url="<?= VT_HOST ?>/vt/<?= $tourid ?>/<?= $roomid ?>/5/<?= $level ?>/%v_%u.jpg"/><?
		?></level><?
	}
	foreach (array('mobile','tablet') as $special) {
		?><<?= $special ?>><?
			?><left url="<?= VT_HOST ?>/vt/<?= $tourid ?>/<?= $roomid ?>/3/<?= $special ?>_face.jpg"/><?
			?><front url="<?= VT_HOST ?>/vt/<?= $tourid ?>/<?= $roomid ?>/0/<?= $special ?>_face.jpg"/><?
			?><right url="<?= VT_HOST ?>/vt/<?= $tourid ?>/<?= $roomid ?>/1/<?= $special ?>_face.jpg"/><?
			?><back url="<?= VT_HOST ?>/vt/<?= $tourid ?>/<?= $roomid ?>/2/<?= $special ?>_face.jpg"/><?
			?><up url="<?= VT_HOST ?>/vt/<?= $tourid ?>/<?= $roomid ?>/4/<?= $special ?>_face.jpg"/><?
			?><down url="<?= VT_HOST ?>/vt/<?= $tourid ?>/<?= $roomid ?>/5/<?= $special ?>_face.jpg"/><?
		?></<?= $special ?>><?
	}
	?></image><?
	$i = 0;
	foreach ($hotspots as $hotspot) {
		extract($hotspot);
		?><hotspot<?
		?> name="spot<?= ++$i ?>"<?
		?> fovview="90"<?
		?> hcenter="<?= $HCENTER ?>"<?
		?> vcenter="<?= $VCENTER ?>"<?
/*		?> url="<?= STATIC_HOST ?>/images/logo.png"<?*/
		?> onclick="<?
			?>closeglobalobjects();<?
			?>ifnot(stopSequence === undefined,interruptAnimation());<?
			?>lookto(get(hcenter),get(vcenter),get(view.fovmin),smooth(400,20,100));<?
			?>loadpano('../virtualroom/<?= $licenseid ? 'l'.$licenseid : $tourid ?>/<?= $DSTID ?>.xml',null,null,BLEND(1));<?
/*			?>lookat(get(hview),get(vview),get(fovview));<?
			?>wait(blend);<?
			?>lookto(get(panoview.h),get(panoview.v),get(panoview.fov),smooth(100,20,50))<?*/
		?>"<?
		?> onhover="showtext('<?= escape_utf8(win1252_to_utf8($NAME)) ?>')"<?
		?> alturl="<?= VT_HOST ?>/vt/g/spots/zoom.png"<?
		?> ath="<?= $HCENTER ?>"<?
		?> atv="<?= $VCENTER ?>"<?
		
/*		?> borderwidth="4"<?
		?> bordercolor="0xFFFF00"<?
		?> borderalpha=".7"<?*/
/*		?> fillcolor="0xfafffa"<?
		?> fillalpha=".2"<?*/
		
		?> bordercolorhover="0xffffff"<?
		?> borderalphahover="1"<?
		?> fillcolorhover="0xffffff"<?
		?> fillalphahover=".25"><?
		if (isset($points[$SPOTID])) foreach ($points[$SPOTID] as $point) {
			extract($point);
			?><point ath="<?= $ATH ?>" atv="<?= $ATV ?>"/><?
		}
		?></hotspot><?
	}
?></krpano>