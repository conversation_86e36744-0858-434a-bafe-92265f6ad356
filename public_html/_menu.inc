<?php

declare(strict_types=1);

function get_menu(bool $small_screen = false): array {
	static $__menu;
	if (!empty($__menu[$small_screen])) {
		return $__menu[$small_screen];
	}
	$is_admin = have_admin();
	$menu_one = [
		'USER'	=> null,

		'SHARE'	=> null,

		'B2B'	=> null,

		'MAIN' => [
			'home',
			'news',
			'party',
			'contest',
			'trends',
			'gallery',
			'video',
			'interview',
			'review',
			'column',
			'stream',
		],
		'COMM' => [
			'user',
			'forum',
			'report',
			'music',
		],
		'DATA' => [
			'artist',
			'location',
			'organization',
			'city',
			($is_admin ? 'country' : null),
		],
		'MISC' => [
			'contact',
			'crew',
			($is_admin ? 'holiday' : null),
			'crewjob',
			'donation',
			'policy',
			'help',
			'info',
		],
	];

	if ($is_admin) {
		foreach ([
			'prohib',
			'offense',
			'announce',
			'logbook',
		] as $sect) {
			if (have_admin($sect)) {
				$menu_two[] = $sect;
			}
		}
		if (have_admin(['disallowuser', 'helpdesk'])) {
			$menu_two[] = 'disallowuser';
		}
		if (have_admin('metad')) {
			$menu_two[] = 'metad';
		}
		$menu_two[] = 'dump';
		if (have_admin('maintenance')) {
			$menu_two[] = 'maintenance';
		}
		$menu_one['ADMIN'] = $menu_two;
	}
	$locs = am_employee('location');
	$orgs = am_employee('organization');
	$campart = [];
	if ($locs) {
		$campart[] = '(
			SELECT 1
			FROM party
			JOIN camerarequest USING (PARTYID)
			WHERE LOCATIONID IN ('.implode(', ', $locs).')
			LIMIT 1
		)';
	}
	if ($orgs) {
		assert(is_array($orgs)); # Satisfy type checker
		unset(	$orgs[ORGANIZATIONID_PARTYFLOCK],
				$orgs[ORGANIZATIONID_APPIC]);
		if ($orgs) {
			$campart[] = '(
				SELECT 1
				FROM party
				JOIN connect
					 ON	MAINTYPE = "organization"
					AND	MAINID IN ('.implode(',',$orgs).')
					AND	ASSOCTYPE = "party"
					AND	ASSOCID = PARTYID
				JOIN camerarequest USING (PARTYID)
				LIMIT 1
			)';
		}
	}
	if (($is_relation = have_relation())
	||	$is_admin
	) {
		foreach ([
			$is_admin ? 'contact' : 'info',
			'ad',
			'newsad',
			'promo',
			'pixel',
			'relation',
		] as $section) {
			if ($is_relation
			||	$section === 'contact'
			||	have_admin($section)
			) {
				$menu_b2b[] = $section;
			}
		}
	}
	if (have_admin(['camerarequest', 'photographer', 'aspiring_photographer'])
	||	$campart
	&&	memcached_single(['camerarequest', 'party', 'connect'], implode(' UNION ', $campart), HALF_HOUR)
	) {
		$menu_b2b[] = 'camera';
	}
	if (isset($menu_b2b)) {
		$menu_one['B2B'] = $menu_b2b;
	}

	ob_start();
	global $totalmsg;
	$totalmsg = display_user_menu();

	$ok_menu_one = [
		'USER' => ob_get_clean(),
	];

	foreach ($menu_one as $head => $parts) {
		if ($head === 'SHARE') {
			$ok_menu_one[$head] = $parts;
		}
		if (!$parts) {
			continue;
		}
		$parts = fill_menu($parts, $is_admin && $head === 'MISC');
		if ($parts) {
			$ok_menu_one[$head] = $parts;
		}
	}
	$menu_one = $ok_menu_one;

	if (!ROBOT
	&&	setting_isset(SHOW_SHARE_MENU)
	) {
		require_once '_presence.inc';
		ob_start();
		?><div<?
		?> class="relative ptr light8"<?
		?> onmouseover="remclass(this,'light8')"<?
		?> onmouseout="addclass(this,'light8')"<?
		?> style="padding-top: 1px;"<?
		?> onclick="
			if (Pf.socialed) {
				Pf.socialed = false;
				hide(this.nextSibling);
			} else {
				Pf.socialed = true;
				unhide(this.nextSibling);
			}"><?

		show_presence_icon('facebook');
		if (SMALL_SCREEN) {
			?>&#8197;<?
			show_presence_icon('whatsapp');
		}
		?> <?= __C('action:share') ?> <?
		?><span class="light">&rarr;</span><?
		?></div><?
		?><div<?
		?> class="hidden abs socialboxv2 nowrap"<?
		?>><?
		?><div class="bg"><?=
			get_close_char([
				'class'		=>'abs medium',
				'style'		=>'right: 5px; top: 5px;',
				'onclick'	=>'event.stopPropagation(); Pf.socialed = false; hide(this.parentNode.parentNode.parentNode);',
			])
		?><div><?
		?><a<?
		?> class="light8 seemtext"<?
		?> onmouseover="remclass(this, 'light8');"<?
		?> onmouseout="addclass(this, 'light8');"<?
		?> target="_blank"<?
		?> href="<?
			?>https://www.facebook.com/dialog/share<?
			?>?app_id=<?= FACEBOOK_APPID
			?>&amp;display=page<?
			?>&amp;href=<?= urlencode(FULL_HOST.$_SERVER['REQUEST_URI']) ?>"><?
		?><img<?
		?> style="vertical-align:-6px;width:24px;height:24px;"<?
		?> alt="<?= __('action:share_on_facebook') ?>"<?
		?> src="<?= STATIC_HOST ?>/images/share_facebook<?= is_high_res() ?>.png"> <?
		?>Facebook<?
		?></a><?
		?></div><?

		if (SMALL_SCREEN) {
			?><div><?
			?><a<?
			?> class="light8 seemtext"<?
			?> onmouseover="remclass(this,'light8')"<?
			?> onmouseout="addclass(this,'light8')"<?
			?> href="whatsapp://send?text=<?= urlencode(FULL_HOST.$_SERVER['REQUEST_URI']) ?>"><?
			?><img style="vertical-align:-6px;width:24px;height:24px;" src="<?= STATIC_HOST ?>/presence/whatsapp@<?= is_high_res() ? 4 : 2 ?>x.png" /> <?
			?>WhatsApp<?
			?></a><?
			?></div><?
		}

		?></div><?
		?></div><?

		$menu_one['SHARE'] = ob_get_clean();
	}

	if ($small_screen) {
		$menu_one['MISC'] =
			'<a href="/search">'.__C('action:search').'</a>'.
			'<div class="menusep"></div>'.
			$menu_one['DATA'].
			'<div class="menusep"></div>'.
			$menu_one['MISC'];

		if (!empty($menu_one['SHARE'])) {
			$menu_one['USER'] .=
				'<div class="menusep"></div>'.
				$menu_one['SHARE'];
		}

		unset($menu_one['DATA'],$menu_one['SHARE']);

		$menu_one['SEARCH'] = true;
	}
	return $__menu[$small_screen] = $menu_one;
}

function display_menu(array $menu_one): void {
	$have_user = have_user();
	?><nav class="main-nav"><?
	if (SMALL_SCREEN) {
		$widths = ['USER' => 14, 'SEARCH' => 10];
		if (isset($menu_one['COMM'])) {
			$widths = [...$widths, 'MAIN' => 28, 'COMM' => 28, 'MISC' => 20];
		} else {
			$widths = [...$widths, 'MAIN' => 46, 'MISC' => 30];
		}
		include_js('js/smallmenu');
		?><table class="menustrip"><tr><?
	} else {
		$widths = [];
	}

	foreach ($menu_one as $head => $data) {
		if (!$data) {
			continue;
		}
		if (SMALL_SCREEN) {
			if ($head === 'ME'
			||	$head === 'B2B'
			||	$head === 'ADMIN'
			) {
				continue;
			}
			if ($head === 'SEARCH'
			) {
				?><td class="center menubutton menubg"><a class="seemtext ptr" href="/search" title="<?= __('action:search') ?>">&#128269;</a></td><?
				continue;
			}
			?><td<?
			?> style="width: <?= $widths[$head] ?>%;"<?
			?> class="menubutton menubg"<?
			?> onclick="open_menu(this, '<?= $head ?>');"><?
			if ($usermenu = $head === 'USER') {
				?><div class="menume novents"><?
				if (!$have_user) {
					?><span class="light novents"><?
				}
			}
			echo __("smallscreen:{$head}_menu");
			if ($usermenu) {
				if ($have_user) {
					global $totalmsg;
					?><div class="light" id="totalmsgs"><?
					if ($totalmsg) {
						echo $totalmsg;
					}
					?></div><?
				} else {
					?></span><?
				}
				?></div><?
			}
		} else {
			?><div class="<?
			global $miniad;
			if (!$miniad) {
				?>nbut <?
			}
			if ($head === 'SHARE') {
				?>share <?
			}
			?>main-menu"><?= $data
			?></div><?
			if ($head === 'USER') {
				global $miniad;
				if ($miniad) {
					?><hr class="slim"><?
					display_ad($miniad, loading: 'eager');
				}
			} elseif ($head === 'MAIN') {
				global $miniad_delayed;
				if ($miniad_delayed) {
					?><hr class="slim"><?
					display_ad($miniad_delayed, loading: 'eager');
				}
			}
	   			?><hr class="slim"><?
		}
	}
	if (SMALL_SCREEN) {
		?></tr></table><?
		$pos = 0;
		$cnt = 5;
 		foreach ($menu_one as $head => $data) {
 			if ($head === 'ME'
 			||	$head === 'ADMIN'
 			||	$head === 'B2B'
 			) {
 				continue;
			}
			--$cnt;
			if (!$data) {
				continue;
			}
			?><div id="menu_<?= $head ?>" class="menublock menubg" style="<?
			if ($cnt > 0) {
				?>left: <?= $pos ?>%;<?
			} else {
				?>right: 0;<?
			}
			?>"><?
			if ($have_user
			&&	$head === 'USER'
			) {
				echo get_me(Eelement_name('profile'));
				?><div class="menusep"></div><?
			}
			echo $data;
			switch ($head) {
			case 'USER':
				if (isset($menu_one['B2B'])) {
					?><div class="menusep"></div><?
					echo $menu_one['B2B'];
				}
				break;
			case 'MISC':
				?><div class="menusep"></div><?= Eelement_name('language') ?>: <?= get_language_switch() ?><br /><?

				$othertheme = LITE ? 'dark' : 'light';
				?><span<?
				?> id="tsw"<?
				?> data-theme="<?= CURRENTTHEME ?>"<?
				?> data-other="<?= __C('setting:'.CURRENTTHEME.'_site') ?>"<?
				?> class="unhideanchor"<?
				?> onclick="switchTheme(this);"><?= __C("setting:{$othertheme}_site") ?></span><?
				if (!have_post()) {
					?><br /><?
					?><a href="/smallscreen.act?no"><?= Eelement_name('regular_site') ?></a><?
				}
				if (isset($menu_one['ADMIN'])) {
					?><div class="menusep"></div><?
					echo $menu_one['ADMIN'];
				}
				break;
			}
			?></div><?
		}
	}
	?></nav><?
}

function fill_menu(array $parts, bool $no_contact): string {
	ob_start();
	foreach ($parts as $section) {
		if (!$section
		||	$section === 'contact'
		&&	$no_contact
		) {
			continue;
		}

		$class = null;
		$link = null;
		$name = __C('section:'.$section);

		switch ($section) {
		case 'column':
			$last_column = memcached_single('column','SELECT MAX(PSTAMP) FROM `column` WHERE ACCEPTED=1',ONE_DAY);
			if ($last_column > CURRENTSTAMP - 3 * ONE_MONTH) {
				break;
			}
			continue 2;
		case 'report':
			if (CURRENTDOMAIN !== 'nl') {
				continue 2;
			}
			break;
		case 'virtualtour':
			$class = ' small lmrgn';
			break;
		case 'info':
			$name = Eelement_name('b2b');
			break;
		case 'home':
			?><div><?
			?><a<?
			if ($_REQUEST['sELEMENT'] === 'home') {
				?> class="selected"<?
			}
			?> href="/"><?= $name ?></a><?
			?></div><?
			continue 2;

/*		NOTE: obsolete

		case 'flock':
			?><div><?
			if (setting('FLOCKSFIRSTPAGE') === 'O') {
				?><a<?
				if ($_REQUEST['SECTION'] === 'flock' && !$_REQUEST['ACTION']) {
					?> class="selected"<?
				}
				?> href="/flock"><?= $name ?></a><?
			} else {
				?><a<?
				if ($_REQUEST['SECTION'] === 'flock' && $_REQUEST['ACTION'] === 'interecent') {
					?> class="selected"<?
				}
				?> href="/flock/interecent"><?= $name ?></a><?
			}
			?></div><?
			continue 2;*/

		case 'video':
			$link = '/video/recent';
			break;

		case 'trends':
			$link = '/trends/party';
			break;

		case 'dump':
			if (memcached_single('dumpmeta','SELECT 1 FROM dumpmeta WHERE USERID='.CURRENTUSERID)) {
				$link = '/dump/user/'.CURRENTUSERID;
			}
			break;
		}
		if (!$link) {
			$link = "/$section";
		}
		if ($section === $_REQUEST['SECTION']
		&&	(	# non contact section hilite ok
				$section !== 'contact'
				# if contact section, only hilite if action is not 'mine' as that is hilited elsewhere
			||	$_REQUEST['ACTION'] !== 'mine'
			&&	(	# also, fine if no ticketid
					!$_REQUEST['sID']
					# but if we are in a ticket, then only hilite if it is not 'ours' (belonging to /mine)
				||	CURRENTUSERID !== db_single_int('contact_ticket', "SELECT WITHUSERID FROM contact_ticket WHERE TICKETID = {$_REQUEST['sID']}")
				)
			)
		) {
			$class .= ' selected';
		}

/*		NOTE: Function agenda_disallowed could have set this global $disallow, but didn't.
			  And now doesn't because it's commented out for the time being.

		switch ($section) {
		case 'artist':
		case 'location':
		case 'organization':
		case 'party':
				global $disallow;
				switch ($disallow) {
				case DISALLOW_PROXY:
				case DISALLOW_TOR:
				case DISALLOW_HARVEST:
				$class .= ' unavailable';
				break;
			}
			break;
		}*/
		if ($class) {
			$class = trim($class);
		}
		?><div><?
		?><a<?
		if ($class) {
			?> class="<?= $class ?>"<?
		}
		?> href="<?= $link ?>"><?
		echo	($section === 'user' && have_buddies())
			?	$name.'<small>, '.element_plural_name('buddy').'</small>'
			:	$name;

		if ($section === 'contest') {
			?> <span class="win"><?= __('partylist:win') ?></span><?
		}

		?></a><?

		switch ($section) {
/*		case 'ad':
			?> <?
			?><a href="/banner/form"><?
			echo get_special_char('add',['title' => __('action:add_banner')]);
			?></a><?
			break;*/

		case 'camera':
			if (!have_admin('camerarequest')) {
				break;
			}
			require_once 'defines/camera.inc';

			$newcnt = memcached_single(['camera', 'party', 'camerarequest'],'
				SELECT COUNT(*)
				FROM camera
				JOIN party USING (PARTYID)
				WHERE NOT WRITTEN
				  AND STATUS = ""
				  AND ACTIVE
				  AND CANCELLED = 0
				  AND POSTPONED = 0
				  AND MOVEDID = 0
				  AND NOT REQUESTBYSELF
				  AND EXISTS (
				  		SELECT 1
				  		FROM camerarequest
				  		WHERE camerarequest.PARTYID = camera.PARTYID
					  )
				  AND STAMP >= '.TODAYSTAMP.'
				  AND STAMP < '.(TODAYSTAMP + CAMREQS_REQUEST_PERIOD),
				FIVE_MINUTES,
			);
			$recnt = memcached_single('contact_ticket', "
				SELECT COUNT(*)
				FROM contact_ticket
				WHERE ELEMENT IN ('camera', 'camerarequest')
				  AND ID
				  AND (TRACKIT OR STATUS = 'open')",
			);
			if ($newcnt || $recnt) {
				?> <?= MIDDLE_DOT_ENTITY ?> <?
				if ($newcnt) {
					?><b><?= $newcnt ?></b><?
					if ($recnt) {
						?>.<? echo $recnt;
					}
				} elseif ($recnt) {
					echo $recnt;
				}
			}
			break;

		case 'stream':
			if (!have_admin('stream')) {
				break;
			}
			require_once 'defines/stream.inc';
			$todo = memcached_single('stream','
				SELECT COUNT(*)
				FROM stream
				WHERE ACTIVE=1
				  AND OKSTAMP<'.(TODAYSTAMP - STREAM_MAX_STALE),
			);
			if ($todo) {
				?> <?= MIDDLE_DOT_ENTITY ?> <a href="/stream/offline"><?= $todo ?></a><?
			}
			break;

		case 'city':
			if (!have_admin('city')
			||	is_locked(LOCK_INVALIDCITYLIST)
			) {
				break;
			}
			require_once '_cityvalidation.inc';
			if (!($city_attention_counts = get_city_attention_counts())
			||	!array_sum($city_attention_counts)
			) {
				break;
			}
			/** @noinspection PhpRedundantOptionalArgumentInspection */
			extract($city_attention_counts, \EXTR_OVERWRITE);

			$parts = [];
			if ($user_invalid_city_cnt) {
				$parts[] = '<b><a href="/city/showunknown">'.$user_invalid_city_cnt.'</a></b>';
			}
			if ($city_no_lonlat_cnt) {
				$parts[] = '<a href="/city/shownolonlat">'.$city_no_lonlat_cnt.'</a>';
			}
			if ($city_no_province_cnt) {
				$parts[] = '<a href="/city/emptyprovince">'.$city_no_province_cnt.'</a>';
			}

			?> <?= MIDDLE_DOT_ENTITY ?> <? echo implode('.', $parts);

			break;


		case 'party':
			require_once '_needupdates.inc';
			show_needupdate_counters('party');

			?> <a href="/party/register"><?= get_special_char('add', ['title' => __('action:add_event')])
			?></a><?
			break;

		case 'artist':
			if (have_admin('artist')
			||	is_aspiring_content_admin()
			) {
				require_once '_needupdates.inc';
				show_needupdate_counters('artist');
			}
			?> <a href="/artist/register"><?= get_special_char('add', ['title' => __('action:add_artist')]) ?></a><?
			break;

		case 'location':
			if (have_admin('location')
			||	is_aspiring_content_admin()
			) {
				require_once '_needupdates.inc';
				show_needupdate_counters('location');
			}
			?> <a href="/location/register"><?= get_special_char('add',['title'=>__('action:add_location')]) ?></a><?
			break;

		case 'organization':
			if (have_admin('organization')
			||	is_aspiring_content_admin()
			) {
				require_once '_needupdates.inc';
				show_needupdate_counters('organization');
			}
			?> <a href="/organization/register"><?= get_special_char('add',['title'=>__('action:add_organization')]) ?></a><?
			break;

		case 'prohib':
			if (!have_admin('prohib')
			||	is_locked(LOCK_PROHIB_PROPOSALS)
			) {
				break;
			}
			if ($cnt = memcached_single('prohibited', "
				SELECT COUNT(*)
				FROM prohibited
				WHERE STATUS = 'inactive'",
				300,
			)) {
				?> <?= MIDDLE_DOT_ENTITY ?> <a href="/prohib/inactive"<?
				if ($cnt >= 50) {
					if ($cnt >= 100) {
						?> class="error"<?
					} else {
						?> class="warning ns"<?
					}
				}
				?>><?= $cnt ?></a><?
			}
			break;

		case 'contact':
			if (!have_admin()) {
				break;
			}
			require_once '_contactcounts.inc';
			if (!($counts = get_contact_counts())) {
				break;
			}
			$subparts = [];
			[$mycnt, $scnt, $cnt] = $counts;
			if ($mycnt) {
				$subparts[] = '<b>'.$mycnt.'</b>';
			}
			if ($scnt) {
				$subparts[] = '<span class="underline">'.$scnt.'</span>';
			}
			if ($cnt) {
				$subparts[] = $cnt;
			}
			?> <?= MIDDLE_DOT_ENTITY ?> <?
			echo implode('.', $subparts);
			break;

		case 'gallery':
			require_once '_appearances.inc';
			if (!may_have_appearances()) {
				break;
			}
			show_appearance_counters();
			break;

		case 'promo':
			if (!have_admin('promo')) {
				break;
			}
			require_once '_recheckpending.inc';
			$requests = db_single('promo',"
				SELECT COUNT(*)
				FROM promo
				WHERE REMOVED=0
				  AND ACTIVE=0
				  AND REQUEST=1
				  AND PROMOID!=9842
				  AND COSTSENT=0
				  AND ISNULL((
					SELECT 1 FROM contact_ticket
					WHERE ELEMENT = 'promo'
					  AND PROMOID = ID
					  AND STATUS = 'pending'
					LIMIT 1))",
			);
			$rechecks = db_single(['promo','contact_ticket'], "
				SELECT COUNT(DISTINCT PROMOID)
				FROM promo
				LEFT JOIN contact_ticket ON PROMOID = ID
				WHERE ACTIVE=0
				  AND REMOVED=0
				  AND ELEMENT = 'promo'
				  AND IF(	ISNULL(ID),
							COSTSENT != 0,
							STATUS = 'open' OR STATUS = 'closed' AND TRACKIT
						)
				  AND PROMOID != 9842",
			);
			if ($requests || $rechecks) {
				?> <?= MIDDLE_DOT_ENTITY ?> <?
				if ($requests) {
					?><b><?= $requests ?></b><?
					if ($rechecks) {
						?>.<? echo $rechecks;
					}
				} elseif ($rechecks) {
					echo $rechecks;
				}
			}
			break;

		case 'contest':
			if (!have_admin('contest')) {
				break;
			}
			if ($close_stamps = memcached_simple_hash(['contest', 'party'], '
				SELECT CONTESTID, CLOSE_STAMP
				FROM contest
				LEFT JOIN party USING (PARTYID)
				WHERE contest.CLOSED=0
				AND (	party.PARTYID IS NOT NULL
					AND (MOVEDID OR CANCELLED OR POSTPONED)
				 	 OR contest.CLOSE_STAMP<'.(TODAYSTAMP + ONE_DAY).')',
				TEN_MINUTES)
			) {
				$cnt = 0;
				foreach ($close_stamps as $close_stamp) {
					if ($close_stamp < CURRENTSTAMP) {
						++$cnt;
					}
				}
				if ($cnt) {
					?> <?= MIDDLE_DOT_ENTITY ?> <?
					?><b><a href="/contest/needclosure"><?= $cnt ?></a></b><?
				}
			}
			break;

		case 'video':
			if (!have_admin('video')
			||	false === ($lastdone = db_single('videodone','SELECT MAX(VIDEOID) FROM videodone WHERE BASE = 1'))
			) {
				break;
			}
			if ($cnt = memcached_single('video','
				SELECT COUNT(*)
				FROM video
				WHERE STATUS="inactive"
				  AND VIDEOID>'.($lastdone ?: 0))
			) {
				?> <?= MIDDLE_DOT_ENTITY ?> <?
				?><a<?
				if ($_REQUEST['ACTION'] === 'inactive'
				&&	!$_REQUEST['SUBACTION']
				) {
					?> class="selected"<?
				}
				?> href="/video/inactive?base"><?= $cnt ?></a><?
			}
			?> <?
			?><a href="/video/register"><?= get_special_char('add', ['title' => __('action:add_video')])
			?></a><?
			break;
		}
		?></div><?
		if ($section === 'party') {
			global	$__year, $__month, $__day, $__hour, $__whatover;

			if (($next_ade = memcached_simpler_array(['party','connect'], /** @lang MariaDB */ "
				(	SELECT STAMP
					FROM connect
					JOIN party ON ASSOCID = PARTYID
					WHERE MAINTYPE = 'organization'
					  AND MAINID = ".ORGANIZATIONID_ADE."
					  AND ASSOCTYPE = 'party'
					  AND STAMP > UNIX_TIMESTAMP('$__year-07-01')
					ORDER BY STAMP
					LIMIT 1
				) UNION (
					SELECT STAMP
					FROM connect
					JOIN party ON ASSOCID=PARTYID
					WHERE MAINTYPE = 'organization'
					  AND MAINID = ".ORGANIZATIONID_ADE."
					  AND ASSOCTYPE = 'party'
					  AND STAMP > UNIX_TIMESTAMP('$__year-07-01')
					  AND STAMP < UNIX_TIMESTAMP('$__year-12-01')
					ORDER BY STAMP DESC
					LIMIT 1
				)",	ONE_DAY))
			&&	CURRENTSTAMP > $next_ade[0] - 2 * ONE_MONTH
			&&	CURRENTSTAMP < $next_ade[1] +	  ONE_DAY
			) {
				?><div class="lmrgn"><?
				?>"><a class="ADE" class="<?
				if (CURRENTSTAMP > $next_ade[0] - ONE_WEEK) {
					?>bold<?
				} else {
					?>small<?
				}
				if ($_REQUEST['sELEMENT'] === 'party'
				&&	$_REQUEST['sID'		] === ORGANIZATIONID_ADE
				&&	$_REQUEST['ACTION'	] === 'archive'
				) {
					?> selected<?
				}
				?>" href="<?=
					CURRENTSTAMP > $next_ade[0] - 2 * ONE_MONTH
				&&	CURRENTSTAMP < $next_ade[1] + 6 * ONE_HOUR
				?	get_element_href('organization', ORGANIZATIONID_ADE)
				:	'/organization/'.ORGANIZATIONID_ADE.'/archive'
				?>">ADE <?= $__year ?><?
				?><img<?
				?> alt="ADE"<?
				?> width="16" height="16"<?
				?> class="lmrgn lower icon"<?
				?> src="<?= STATIC_HOST ?>/images/ADE<?= is_high_res() ?>.png" /><?
				?></a><?

				if (have_admin('party')
				&&	($todo = memcached_single('party', "
						SELECT COUNT(*)
						FROM connect
						JOIN party ON PARTYID = ASSOCID
						WHERE ACCEPTED = 0
						  AND MAINTYPE = 'organization'
						  AND MAINID = 3776
						  AND ASSOCTYPE = 'party'",
					))
				) {
					?> <?= MIDDLE_DOT_ENTITY ?> <?
					?><a href="/agenda/needupdate/ADE"><?= $todo ?></a><?
				}

				?></div><?
			}

			?><div class="lmrgn colorhover small relative"><?

			?><a class="<?
			if ($selected =$_REQUEST['ACTION'] === 'now-and-soon') {
				?> selected<?
			}
			?>" href="/agenda/now-and-soon"><?= $name = __('section:now_and_soon') ?></a><?

			?><a class="<?
			if ($selected) {
				?> selected<?
			}
			?>" href="/agenda/now-and-soon"><?
			?><img alt="<?= $name ?>" src="<?= STATIC_HOST ?>/images/marker<?= is_high_res() ?>.png" class="icon lower lmrgn abs" style="margin-top: 1em;" /><?
			?></a><?
			?><br /><?

			?><a class="<?
			if ($selected) {
				?> selected<?
			}
			?>" href="/agenda/now-and-soon"><?= element_name('map') ?></a><?
			?></div><?

			require_once '_favourite.inc';
			if (($favs = get_favourites())
			&&	(	isset($favs['organization'])
				||	isset($favs['location'])
				||	isset($favs['artist'])
				)
			) {
				?><div><a class="lmrgn small<?
				if ($_REQUEST['sELEMENT'] === 'party'
				&&	$_REQUEST['ACTION'] === 'favourites'
				) {
					?> selected<?
				}
				?>" href="/agenda/favourites"><?= element_plural_name('favourite') ?></a></div><?
			}

			?><div><a class="lmrgn small<?
			if ($_REQUEST['ACTION'] === 'festivals') {
				?> selected<?
			}
			?>" href="/agenda/festivals"><?= element_plural_name('festival') ?></a></div><?

			[, $easter_start, $easter_end] = get_easter($__year);
			if (CURRENTSTAMP > $easter_start - 4 * ONE_WEEK - 2 * ONE_DAY
			&&	CURRENTSTAMP < $easter_end
			) {
				?><div> <a class="lmrgn small" href="/agenda/easter"><?= __('party:menu:easter_weekend') ?></a></div><?
			}

			$carnival_start = get_carnival_start($__year);
			$carnival_end = $carnival_start + 3 * ONE_DAY + HOUR_DAY_START;

			if (CURRENTSTAMP > $carnival_start - ONE_MONTH
			&&	CURRENTSTAMP < $carnival_end
			) {
				?><div> <a class="lmrgn small"  href="/agenda/carnival/<?= $__year ?>"><?= __('party:menu:carnival') ?></a></div><?
			}
			if ($__month === 12 || $__month === 1) {
				if ($__month === 12 && $__day >= 1 || $__day <= 2) {
					if ($__month === 12 && ($__day <= 26 || $__day === 27 && $__hour < 7)) {
						?><div> <a class="lmrgn small" href="/agenda/christmas"><?= __('party:menu:christmas') ?></a></div><?
					}
					?><div> <a class="lmrgn small"  href="/agenda/new-years-eve/<?= $__year ?>"><?= __('day_name:new_years_eve') ?></a></div><?
					?><div> <a class="lmrgn small"  href="/agenda/new-years-day/<?= $__year + 1 ?>"><?= __('day_name:new_years_day') ?></a></div><?
				}
			} else {
				if (CURRENTDOMAIN === 'nl'
				&&	$__year !== 2020
				) {
					# Dutch holidays
					if ($__month === 2
					||	$__month === 3
					||	(	$__month === 4
						&&	$__day >= 1
						&&	(	$__day <= 26
							||	$__day === 27
							&&	$__whatover === 'kingsday'
							)
						)
					) {
						?><div> <a class="lmrgn small"  href="/agenda/kings-night-and-day"><?
						# &thinsp; for SEO
						echo str_replace('&amp;','&thinsp;&amp;&thinsp;',__('party:menu:kings_night_and_day'));
						?></a></div><?
					}
					if ($__month === 3
					&&	$__day >= 5
					||	$__month === 4
					||	$__month === 5
					&&	$__day <= 5
					) {
						?><div> <a class="lmrgn small"  href="/agenda/liberation-day"><?= __('party:menu:liberation_day') ?></a></div><?
					}
				}
				[, $ascension_start, $ascension_end] = get_ascension($__year);
				if (CURRENTSTAMP > $ascension_start - ONE_MONTH - 2 * ONE_DAY
				&&	CURRENTSTAMP < $ascension_end
				) {
					?><div> <a class="lmrgn small" href="/agenda/ascension-day"><?= __('party:menu:ascension_day') ?></a></div><?
				}
				[, $pentecost_start, $pentecost_end] = get_pentecost($__year);
				if (CURRENTSTAMP > $pentecost_start - ONE_MONTH - 2 * ONE_DAY
				&&	CURRENTSTAMP < $pentecost_end
				) {
					?><div> <a class="lmrgn small" href="/agenda/pentecost"><?= __('party:menu:pentecost_weekend') ?></a></div><?
				}
				$halloween = gmmktime(22, 0, 0, 10, 31, $__year);

				# $weekend_before = strtotime('last thursday 6 AM', $halloween);
				$weekend_after  = strtotime(  'next monday 6 AM', $halloween);

				if ($__year !== 2020		# almost no events due to COVID-19
				&&	(	$__month === 10
					&&	$__day >= 7
					||	$__month === 11
					&&	CURRENTSTAMP < ($weekend_after - 1)
					)
				) {
					?><div> <?
					?><a class="lmrgn <?= ($__day >= 31 - 7) ? 'bold' : 'small' ?>" style="color:#FA5;"<?
					?> href="/agenda/halloween"><?= __('day_name:halloween') ?></a><?
					?> <a class="lmrgn small"  href="/agenda/halloween"><?
					if (SAFARI || MACOS || iOS) {
						?><span<?
						?> class="abs block"<?
						?> style="<?
							?>font-size: 200%;<?
							if (!SMALL_SCREEN) {
								?> margin-top: -12px;<?
							}
						?>"><?= JACK_O_LANTERN_ENTITY ?></span><?
					} else {
						?><img<?
						?> src="<?= STATIC_HOST ?>/images/pumpkin<?= is_high_res() ?>.png"<?
						?> class="abs"<?
						?> style="<?
							?>width:24px;<?
							?>height:24px;<?
							?>margin-top:<?= SMALL_SCREEN ? -6 : -2 ?>px<?
						?>;"><?
					}
					?></a><?
					?></div><?
				}
			}

			# NOTE: need to rename to 'below agenda'

			global $miniad_agenda;
			if ($miniad_agenda) {
				?><hr class="slim"><?
				display_ad($miniad_agenda, loading: 'eager');
				?><hr class="slim"><?
			}
		}
	}
	return ob_get_clean();
}

function get_me(string|int|null $shownick = null): ?string {
	if ($shownick === 2) {
		$shownick = null;
	} elseif (!SMALL_SCREEN) {
		return null;
	}
	global $currentuser;
	$titlenick = $currentuser->row['NICK'];
	if (!$shownick) {
		$shownick = _smiley_replace(escape_specials(_smiley_prepare(strip_animated_smileys($titlenick))));
	}
	ob_start();
	?><a<?
	if ($_REQUEST['SECTION'] === 'user'
	&&	$_REQUEST['ACTION'] === 'single'
	&&	isset($_REQUEST['USERID'])
	&&	$_REQUEST['USERID'] === CURRENTUSERID
	) {
		?> class="selected"<?
	}
	?> title="<?= escape_specials($titlenick) ?>"<?
	?> href="<?= get_element_href('user', CURRENTUSERID, $titlenick) ?>"><?= $shownick ?></a><?
	return ob_get_clean();
}

function display_user_menu(): ?int {
	$element = $_REQUEST['sELEMENT'];
	$action  = $_REQUEST['ACTION'];

	global $currentuser;

	require_once '_fbsession.inc';
	show_fb_menu();

	if (HOME_OFFICE ||have_admin()) {
		?><div class="win"><?= escape_utf8(gethostname()) ?></div><?
	}
	if (!have_post()
	&&	(	HOME_OFFICE
		||	have_admin('development')
		)
	) {
		?><div><?
		foreach([
			'vip'				=> 'VIP',
			''					=> __('adjective:production'),
			'sandbox'			=> null,
			'sandbox-too'		=> null,
			'sandbox-clean'		=> null,
		] as $host => $description) {
			if ($full_host = $host) {
				$full_host .= '.';
			}
			$full_host .= 'partyflock.nl';
			?><a<?
			if ($_SERVER['SERVER_NAME'] === $full_host) {
				?> class="selected"<?
			}
			?> href="//<?= $full_host, $_SERVER['REQUEST_URI'] ?>"><?= $description ?? $host ?></a><br /><?
		}
		?></div><?
	}
	if ($logqueries = LOGQUERIES) {
		ob_start();
		?><div><?
		?><span<?
			?> class="unhideanchor"<?
			?> onclick="<?
				?>swapdisplay('queries');<?
				?>location.hash = 'queries';<?
			?>"><?= __C('mainmenu:queries') ?></span> <?

		# Hidden slow-query-link with warning sign.
		# Alway including it here, because it makes it possible to generate the menu earlier then the
		# displaying ot the query list.
		?><span<?
			?> id="slow-query-link"<?
			?> class="ptr hidden"<?
			?> onclick="<?
				?>swapdisplay('queries');<?
				?>location.hash = 'queries_slow';<?
			?>"><?= WARNING_SIGN_EMOJI_ENTITY ?></span><?
		?></div><?
		$logqueries = ob_get_clean();
	}
	if (!have_user()) {
		echo $logqueries;

		?><div><?
		?><a<?
		if (login_disallowed()) {
			?> class="light unavailable"<?
		}
		?> href="/user/login"><?= __C('action:login') ?></a><?
		?></div><?
		?><div><?
		?><a<?
		if (user_creation_disallowed()) {
			?> class="light unavailable"<?
		}
		?> href="/user/signup"><?= __C('action:signup') ?></a><?
		?></div><?
		return null;
	}

	echo $logqueries;
	if ($currentuser->is_impersonated()) {
		?><div><a href="/?DEPERSONATE"><?= __C('action:depersonate') ?></a></div><?
	} elseif (have_super_admin()) {
		?><div><a href="/user/impersonate"><?= __C('action:impersonate') ?></a></div><?
	}

	$totalmsg = 0;

	require_once '_contestwins.inc';
	if ($contestmsg = get_won_contest_unread_count(CURRENTUSERID)) {
		?><div class="win"><a<?
		if ($action === 'winmsg'
		&&	$_REQUEST['SUBACTION'] !== 'all'
		&&	!$_REQUEST['sID']
		) {
			?> class="selected"<?
		}
		?> href="/contest/winmsg"><?= __C('field:won') ?></a> <?= MIDDLE_DOT_ENTITY ?> <?= $contestmsg ?></div><?
		$totalmsg += $contestmsg;
	}
	require_once '_scancodes.inc';
	$activecnt = 0;
	if ($tickets = get_future_scancodes($activecnt)) {
		$ticketcnt = count($tickets);
		?><div<?
		if ($activecnt) {
			?> class="notice-nb"<?
		}
		?>><a<?
		if ($action === 'scancodes') {
			?> class="selected"<?
		}
		?> href="/contest/scancodes"><?= Eelement_name('ticket', $ticketcnt) ?></a> <?= MIDDLE_DOT_ENTITY ?> <?
		if ($activecnt) {
			echo $activecnt ?>.<?
		}
		echo $ticketcnt ?></div><?
		if ($ticketcnt) {
			$totalmsg += $ticketcnt;
		}
	}
	if ($offenseinfo = memcached_single_array('offense','
		SELECT COUNT(*) AS AMOUNT,SUM(IF(REMOVED OR REGONLY,0,POINTS)) AS POINTS
		FROM offense
		WHERE READM=0
		  AND USERID='.CURRENTUSERID)
	) {
		[$offensemsg, $points] = $offenseinfo;
		if ($element === 'offense'
		&&	$action === 'markread'
		) {
			--$offensemsg;
		}
		if ($offensemsg > 0) {
			$term = $points ? __C('mainmenu:offensewarning') : __C('mainmenu:offensenotice');
			?><div><span class="<?= $points ? 'error' : 'warning' ?> ns"><?
			?><a<?
			if ($element === 'offense'
			&&	$action === 'activemine'
			) {
				?> class="selected"<?
			}
			?> href="/offense/activemine"><?= $term ?></a><?
			?> <?= MIDDLE_DOT_ENTITY ?> <?= $offensemsg ?></span></div><?
			$totalmsg += $offensemsg;
		}
	}

	require_once '_announcementcount.inc';
	if ($anncnt = announcement_count()) {
		$totalmsg += $anncnt;
		?><div><?
		?><a href="/announcement/letters"><?= __C('section:announcement') ?></a> <?= MIDDLE_DOT_ENTITY ?> <span class="underline"><?= $anncnt ?></span></div><?
	}
	require_once '_buddies.inc';
	if ($budcnt = have_new_buddy_requests(CURRENTUSERID)) {
		?><div id="newbudmenu"><?
		?><a href="/buddyrequest/<?= CURRENTUSERID ?>/overview/incoming"><?= Eelement_plural_name('request')
		?></a> <?= MIDDLE_DOT_ENTITY ?> <span id="newbudcnt"><?= $budcnt ?></span><?
		?></div><?
		$totalmsg += $budcnt;
	}
	require_once '_goingprocess.inc';
	$totalmsg += show_going_process_menu();

	?><div><?
	?><a<?
	if (!isset($_REQUEST['USERID'])
	&&	$element === 'msg'
	&&	!$action
	) {
		?> class="selected"<?
	}
	?> href="/msg"><?= __C('section:msg') ?></a><?

	$msginfo = $currentuser->unread_info();
	$msgcnt = $msginfo ? ($msginfo['CNT'] ?? 0) : 0;
	require_once '_promolist.inc';
	$promocnt = _promolist_active_count();
	if ($msgcnt || $promocnt) {
		$totalmsg += $msgcnt + $promocnt;
		?> <?= MIDDLE_DOT_ENTITY ?> <?

		if ($msgcnt) {
			if ($msginfo['UNSEEN'] ?? false) {
				show_numb($msgcnt, 'blue');
			} else {
				echo $msgcnt;
			}
		}
		if ($promocnt) {
			?>&nbsp;<a href="/msg/"><?
			show_numb($promocnt,'green');
			?></a><?
		}
	}
	?></div><?

	if ($ticketcnts = memcached_single_array('contact_ticket','
		SELECT 	COUNT(IF(READM = 0 OR STATUS = "pending", 1, NULL)) AS TOTAL,
				COUNT(IF(STATUS = "pending", 1, NULL)) AS PENDING,
				COUNT(IF(READM = 0 AND STATUS IN ("closed", "open"), 1, NULL)) AS UNREAD,
				COALESCE(MIN(IF(STATUS = "pending" OR STATUS IN ("closed", "open") AND READM = 0, LSTAMP, NULL)), 0) AS LSTAMP
		FROM contact_ticket
		WHERE ( READM = 0
			OR  STATUS IN ("pending", "open")
			OR  STATUS="closed"
			AND TRACKIT = 1)
		  AND WITHUSERID = '.CURRENTUSERID)
	) {
		[$totalcnt, $pending, $unread, $lstamp] = $ticketcnts;
		if ($totalcnt) {
			$showcnt = $pending + $unread;
			?><div><?
			if ($do_span = $pending && $lstamp) {
				?><span class="<?
				if ($lstamp < TODAYSTAMP - ONE_WEEK
				||	$pending > 5
				) {
					?>error ns<?
				} elseif (
					$lstamp < TODAYSTAMP - 4 * ONE_DAY
				||	$pending > 3
				) {
					?>warning ns<?
				} else {
					?>dark pending<?
				}
				?>"><?
			}
			?><a<?
			if ($_REQUEST['SECTION'] === 'contact'
			&&	$action === 'mine'
			) {
				?> class="selected"<?
			}
			?> href="/contact/mine"><?= __C('section:contact') ?></a><?
			if ($showcnt && $lstamp) {
				?> <?= MIDDLE_DOT_ENTITY ?> <? echo $showcnt;
				$totalmsg += $showcnt;
			}
			if ($do_span) {
				?></span><?
			}
			?></div><?
		}
	}
	if (setting_isset(SHOW_PERSONAL_SUBS)) {
		require_once '_subscriptions.inc';
		if (($info = get_subscription_counts())
		&&	$info[0]
		) {
			$totalmsg += $info[1];
			?><div><?
			?><a<?
			if ($element === 'user'
			&&	$action  === 'subscriptions'
			) {
				?> class="selected"<?
			}
			?> href="/user/subscriptions"><?= __C('section:following') ?></a><?
			if ($info[1]) {
				?><span> <?= MIDDLE_DOT_ENTITY ?> <span id="subcnt"><?= $info[1] ?></span></span><?
			}
			?></div><?
		}
	}
	if (setting_isset(SHOW_NEW_QUOTES)
	&&	(require_once '_quote.inc')
	&&	($quotei = get_new_quotes(CURRENTUSERID))
	&&	($cnt = $quotei[0])
	) {
		$totalmsg += $cnt;
		?><div id="newquotes"><?
		?><a<?
		if ($element === 'user'
		&&	$action === 'quoted'
		) {
			?> class="selected"<?
		}
		?> href="/user/<?= CURRENTUSERID ?>/quoted/new"><?= __C('field:quoted') ?></a> <?= MIDDLE_DOT_ENTITY ?> <?
		?><span id="nqcnt"><?= $cnt ?></span><?
		?></div><?
	}
	require_once '_commentkeep.inc';
	show_saved_comments_menuitem();

	if (have_admin('logbook')) {
		require_once '_logbook.inc';
		show_logbook_menuitem();
	}

	if (have_admin('photographer')
	&&	($cnt = memcached_single(['camerarequest', 'camera', 'party'],'
			SELECT COUNT(*)
			FROM camerarequest
			JOIN camera USING (PARTYID)
			JOIN party USING (PARTYID)
			WHERE party.STAMP >= '.TODAYSTAMP.'
			  AND camerarequest.USERID = '.CURRENTUSERID.'
			  AND LSTAMP < GREATEST(camerarequest.MSTAMP, camera.MSTAMP)'))
	) {
		?><div><?
		?><a href="/camera"><?= __C('section:camera') ?></a> <?= MIDDLE_DOT_ENTITY ?> <?= $cnt
		?></div><?
	}
	if (setting_isset(SHOW_PERSONAL_NOTE)) {
		?><div><a<?
		if ($element === 'user'
		&&	$action  === 'personalnote'
		) {
			?> class="selected"<?
		}
		?> href="/user/personalnote"><?= __C('section:personalnote') ?></a><?

		if (memcached_single('personalnote','SELECT 1 FROM personalnote WHERE USERID='.CURRENTUSERID, TEN_MINUTES)) {
			?> <small>!</small><?
		}
		?></div><?
	}
	/*	NOTE: Currently not used
	if (have_super_admin()) {
		if ($misuse = db_single('agenda_check','SELECT COUNT(*) FROM agenda_check WHERE DONE=0')) {
			?><div><a href="/test/agendamisuse">Misbruik</a> <?= MIDDLE_DOT_ENTITY ?> <?= $misuse ?></div><?
		}
	}*/
	require_once '_fbsession.inc';
	?><div><a href="/user/logoff"><?= __C('action:logoff') ?></a></div><?
	return $totalmsg;
}

function show_numb(int $count, string $color): void {
	?><div class="<?= $color ?>C circle"><?= $count ?></div><?
}
