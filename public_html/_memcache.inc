<?php

/** @noinspection DuplicatedCode, LongLine */

declare(strict_types=1);

const LONG_WAIT_DEBUG_MAIL	= false;
# NOTE: Once db_dirty_cache is fixed, try using cache more.
#		What happens if admin changes line-up? The display page must then be updated also!
#		So for that situation, a forced freshen must be used (at least once)
const TRY_WITHOUT_FRESHEN	= false;

require_once 'defines/memcache.inc';
require_once '_helper.inc';
require_once '_querylog.inc';
require_once '_servertype.inc';
require_once '_db.inc';
require_once '_require.inc';

global $memcache;
$memcache = memcached_init();

function have_memcache(): bool {
	global $memcache;
	return (bool)($memcache ?? false);
}

function memcached_init(): Memcached|false {
	static $__memcache = 0;
	if ($__memcache !== 0) {
		return $__memcache;
	}
	if (!class_exists('Memcached')) {
		if (log_sporadically('missing_Memcached')) {
			mail_log('missing Memcached class');
		}
		return false;
	}
	# don't use persistence, as we use memcache for locking
	# and not sure whether connection sharing poses problems
	# $__memcache = new Memcached((string)getmypid());
	$__memcache = new Memcached();
	$__memcache->setOptions([
		Memcached::OPT_SERIALIZER			=> Memcached::SERIALIZER_IGBINARY,
#		Memcached::OPT_DISTRIBUTION			=> Memcached::DISTRIBUTION_CONSISTENT,
#		Memcached::OPT_LIBKETAMA_COMPATIBLE	=> true,
		Memcached::OPT_NO_BLOCK				=> true,
		Memcached::OPT_BINARY_PROTOCOL		=> true,
		Memcached::OPT_TCP_NODELAY			=> true,
#		Memcached::OPT_COMPRESSION			=> true,
#		Memcached::OPT_BUFFER_WRITES		=> true,
#		Memcached::OPT_CACHE_LOOKUPS		=> true,

		Memcached::OPT_SERVER_FAILURE_LIMIT	=> 10,
		Memcached::OPT_RETRY_TIMEOUT		=> 10,

#		Memcached::OPT_RETRY_TIMEOUT		=> 0,		# seconds, default: 0
#		Memcached::OPT_CONNECT_TIMEOUT		=> 1000,	# milliseconds, default: 1000
#		Memcached::OPT_POLL_TIMEOUT			=> 1000,	# milliseconds, default: 1000
	]);

	$have_server_list = $__memcache->getServerList();

	$add_servers =
		SERVER_SANDBOX ? [
			['127.0.0.1', 11211],
			['127.0.0.1', 11211],
		] :	[
			['**********', 11211],
			['**********', 11211],
		];

	foreach ($add_servers as $i => [$host, $port]) {
		foreach ($have_server_list as $have_server) {
			if ($have_server['host'] === $host
			&&	$have_server['port'] === $port
			) {
				unset($add_servers[$i]);
				continue 2;
			}
		}
	}

	if ($add_servers) {
		$__memcache->addServers($add_servers);
	}

	return $__memcache;
}

function memcached_delete(string|array $key_or_keys): bool|array {
	global $memcache;
	if (is_array($key_or_keys)) {
		return $memcache?->deleteMulti($key_or_keys) ?? false;
	}
	if (func_num_args() > 1) {
		return $memcache?->deleteMulti(func_get_args()) ?? false;
	}
	return $memcache?->delete($key_or_keys) || memcached_not_found();
}

function memcached_increment(
	string	$key,
	?int	$value	 = null,
	?int	$initial = null,
	?int	$expires = null,
): int|false|null {
	# if key does not exists, sets it to initial value and returns it
	# otherwise, key is increased by one en value returned
	global $memcache;
	return $memcache?->increment($key, $value ?? 1, $initial ?? 1, $expires ?: DEFAULT_EXPIRATION) ?? false;
}

function memcached_decrement(string	$key, int $value = 1): int|false {
	global $memcache;
	return $memcache?->decrement($key, $value) ?? false;
}

function memcached_get(string|array $key_or_keys): mixed {
	global $memcache;
	# returns FALSE for error or no value
	if ((	$result =
			is_array($key_or_keys)
		?	$memcache?->getMulti($key_or_keys)
		:	$memcache?->get($key_or_keys)
		)
	||	$result !== false
	) {
		return $result;
	}
	if (null === ($result_code = $memcache?->getResultCode())
	||	$result_code !== Memcached::RES_SUCCESS
	&&	$result_code !== Memcached::RES_NOTFOUND
	) {
		memcached_set_failed('get', $key_or_keys, $result_code);
	}
	return false;
}

function memcached_failed(): bool {
	global $memcache;
	return match ($memcache?->getResultCode()) {
		Memcached::RES_SUCCESS,
		Memcached::RES_NOTFOUND	=> false,
		default					=> true,
	};
}
function memcached_success(): bool {
	global $memcache;
	return $memcache?->getResultCode() === Memcached::RES_SUCCESS;
}
function memcached_not_found(): bool {
	global $memcache;
	return $memcache?->getResultCode() === Memcached::RES_NOTFOUND;
}
function memcached_set(string $key, mixed $val, ?int $expires = null): bool {
	$expires = $expires ?: DEFAULT_EXPIRATION;
	if ($expires >= MEMCACHE_MAXIMUM_RELATIVE_EXPIRATION
	&&	$expires <  CURRENTSTAMP
	) {
		error_log("WARNING memcached_set with key $key and expiration > MEMCACHE_MAXIMUM_RELATIVE_EXPIRATION");
		$expires += CURRENTSTAMP;
	}
	global	$memcache;
	return	$memcache?->set($key, $val, $expires)
		||	memcached_set_failed('set', $key);
}

/*function memcached_set_multi(array $items, ?int $expiration = null): bool {
	global $memcache;
	if (!$memcache) {
		return false;
	}
	if (!$memcache->setMulti($items, $expiration)) {
		memcached_failed('set', array_keys($items));
		return false;
	}
	return true;
}*/

function memcached_add(string $key, mixed $val, ?int $expires = null): bool {
	# returns:
	#	- true on success
	#	- false on failure or if key already exists
	$expires = $expires ?: DEFAULT_EXPIRATION;
	if ($expires >= MEMCACHE_MAXIMUM_RELATIVE_EXPIRATION
	&&	$expires <  CURRENTSTAMP
	) {
		error_log("WARNING memcached_set with key $key and expiration > MEMCACHE_MAXIMUM_RELATIVE_EXPIRATION");
		$expires += CURRENTSTAMP;
	}
	global $memcache;
	if (!$memcache?->add($key, $val, $expires ?: DEFAULT_EXPIRATION)) {
		if (null === ($result_code = $memcache?->getResultCode())
		||	!in_array($result_code, [
				Memcached::RES_SUCCESS,
				Memcached::RES_NOTSTORED,
				Memcached::RES_DATA_EXISTS], true)
		) {
			memcached_set_failed('add', $key. $result_code);
		}
		return false;
	}
	return true;
}

function memcached_read(
	int						 $type,
	string|array|null		 $tables,
	string					 $qstr,
	?int					 $expires	= null,
	string|false|null		&$key		= null,
	int						 $flags		= 0,
	int|string|array|null	 $arg		= null
): mixed {
	if (LOGQUERIES) {
		$start_time = microtime(true);
	}
	if ($key === false) {
		mail_log('memcached_read with key === false');
	}
	$freshen_cache =
		!($flags & DB_DONT_FRESHEN)
	&&	(	$key === false
		||	($flags & DB_FRESHEN_MEMCACHE)
		||	isset($_REQUEST['NOMEMCACHE'])
		||	($tables && db_cache_dirty($tables)));

	if (TRY_WITHOUT_FRESHEN
	&&	$freshen_cache
	) {
		if ($key === false) {
			$reason = 'false $key';
		} elseif ($flags & DB_FRESHEN_MEMCACHE) {
			$reason = 'DB_FRESHEN_MEMCACHE flag';
		} elseif (isset($_REQUEST['NOMEMCACHE'])) {
			$reason = 'NOMEMCACHE is set';
		} elseif (db_cache_dirty($tables)) {
			$reason = 'db_cache_dirty for tables '.(is_array($tables) ? implode(', ', $tables) : $tables);
		} else {
			$reason = 'unknown';
		}
		error_log("freshen_cache because $reason for key $key and query: ".str_replace("\n", ' ', substr($qstr, 0, 50)));
	}
	if (!$key) {
		# Since queries might contain binary data, preg_replace does not work and fails.
		$qstr_for_key = $qstr;
		$close_comment = 0;
		while (false !==  ( $open_comment = strpos($qstr_for_key, '/*', $close_comment))) {
			if (false !== ($close_comment = strpos($qstr_for_key, '*/',  $open_comment))) {
				$qstr_for_key = substr_replace($qstr_for_key, '', $open_comment, $close_comment - $open_comment + 2);
				$close_comment = $open_comment;
			} else {
				break;
			}
		}
		$tables_for_key = is_array($tables) ? implode(' ', $tables) : ($tables ?? '');
		$key = $type.':'.hash('xxh128', $qstr_for_key.':'.$tables_for_key);
	}
	assert(is_string($key)); # Satisfy EA inspection

	static $__cache = [];
	if (!$freshen_cache
	&&	array_key_exists($key, $__cache)
	) {
		return $__cache[$key];
	}
	$lock = null;
	$try = 0;
	global $memcache;

	if ($memcache
	&&	!$freshen_cache
	&&	(	false === ($failures = apcu_fetch_int('memcache_failures'))
			# Cast to Satisfy EA inspection
		||	$failures < MAXIMUM_MEMCACHE_FAILURES)
	) {
		# do advisory lock first!
		if (WAIT_FOR_QUERY) {
			retry_memcache_get:
			++$try;
		}
		if (false !== ($result = $memcache->get($key))) {
			if ($lock) {
				db_releaselock($lock);
			}
			if (LOGQUERIES) {
				assert(isset($start_time)); # Satisfy EA inspection
				logmemquery(LOG_READ, $tables, $qstr, $key, 1000 * (microtime(true) - $start_time));
			}
			return $__cache[$key] = $result;
		}
		if (memcached_failed()) {
			apcu_increment('memcache_failures', ttl: REMEMBER_MEMCACHE_FAILURES);
		} elseif (
			WAIT_FOR_QUERY
		&&	(	!$lock
			||	$try > 1
			)
		) {
			$before_wait_lock = microtime(true);
			# do advisory lock first!
			if (db_getlock($key, 0)) {
				# don't retry memcache, since we got lock immediately, no running query probably!
				$lock = $key;

			}
			/** @noinspection NotOptimalIfConditionsInspection */
			elseif (
				memcached_set($host_key = $key.'_host', gethostname(), WAIT_FOR_QUERY + 100)
			&&	db_getlock($key, 10)
			) {
				$waited = microtime(true) - $before_wait_lock;
				if ($waited > 2) {
					require_once '_spider.inc';
					if (!ROBOT) {
						warning('De site kan soms traag zijn. We zijn op zoek naar het probleem');
					}
					if (LONG_WAIT_DEBUG_MAIL) {
						ob_start();
						echo "Waited $waited seconds for query to finish.\n".
							 "Running processes on all databases:\n\n";

						$ok_processlist = [];
						foreach (['dbu', 'dba', 'dbo', 'dbyin', 'dbyang', 'dbspecial_dbm', 'dbsrch', 'dbsrchrep'] as $server) {
							if (!($processlist = db_rowuse_array(null, 'SHOW FULL PROCESSLIST', DB_FORCE_SERVER, $server))) {
								echo "failed to get processlist from $server\n";
							}
							foreach ($processlist as $process) {
								if ($process['Command'] === 'Sleep'
								||	$process['Info']	=== 'SHOW FULL PROCESSLIST'
								) {
									continue;
								}
								$ok_processlist[$server] = $process;
							}
						}

						?><pre><?
						print_r($ok_processlist);
						?></pre><?

						mail_log(
							ob_get_clean(),
							[	'context'	=> memcached_get("$key\x0Fcontext"),
									'qstr'		=> $qstr,
									'key'		=> $key],
							'Long wait; '.hash('xxh128', db_clean_query($qstr))
						);

						# TODO: store stats in database & send user warning met question to make contact about slowness
					}
				}

				# exact same query is running, wait for it
				$lock = $key;
				if ($try === 1) {
					goto retry_memcache_get;
				}
			} else {
				# failed to get lock
				# - db bad
				# - query VERY slow
				# -> try db anyway

				$waited = microtime(true) - $before_wait_lock;

				if (!in_array(
					$GLOBALS['__db_errno'] ?? null, [
					MYSQL_CR_CONNECTION_ERROR,
					MYSQL_CR_CONN_HOST_ERROR,
					MYSQL_CR_GONE_AWAY,
					MYSQL_CR_SERVER_LOST,
					MYSQL_CR_SERVER_LOST_EXTENDED,
				],  true
				)
				) {
					static $__warned = false;
					if (!$__warned) {
						if (false === ($increment_ok = memcached_increment('getlockwait', 1, 1, TEN_MINUTES))
						||	assert(is_int($increment_ok)) # Satisfy EA inspection
						&&	$increment_ok < 20
						) {
							ob_start();
							?>WARNING getlock failed after <?= $waited ?> seconds!<?= "\n\n"
							?><pre><?
							?>lockhost: <?= memcached_get($host_key), "\n"
							?>host:     <?= gethostname(), "\n"
							?>errno:    <?= ($GLOBALS['__db_errno'] ?? null), "\n"
							?>uri:      <?= ($_SERVER['REQUEST_URI'] ?? $GLOBALS['argv'][0] ?? '?'), "\n"
							?>user:     <?= (defined('CURRENTUSERID') ? CURRENTUSERID : ''), "\n"
							?>key:      <?= $key, "\n"
							?>query:    <?= $qstr, "\n"
							?></pre><?

							foreach(array_keys($GLOBALS['_db_servers']) as $servername) {
								if (str_starts_with($servername, 'dbsphinx')) {
									continue;
								}
								if (!($handle = db_get_connection($servername))) {
									echo "\nfailed to connect to $servername\n";
									continue;
								}
								if ($sub_result = db_query($handle, DB_SHOW, 'SHOW FULL PROCESSLIST', $servername)) {
									$qstr = combine_whitespace($qstr);
									$do_all = str_contains($qstr, 'FORCE KEY');
									while ($query = mysqli_fetch_assoc($sub_result)) {
										if (empty($query['Info'])) {
											continue;
										}
										if ($do_all) {
											$info = 'DO_ALL';
										} else {
											$i_qstr = preg_replace("'\s+'", ' ', $query['Info']);

											similar_text($qstr, $i_qstr, $qstr_pct);

											if ($qstr_pct > 95) {
												$info = 'SIMILAR';
											} elseif (
													$query['Time'] > 2
												&&	$query['State'] !== 'Locked'
												&&	$query['State'] !== 'Waiting for table level lock'
												&&	str_contains($query['Info'], 'MASTER_POS_WAIT')
												# 	notify only once per 10 seconds per unique query:
												&&	memcached_add('show_slow:'.hash('xxh128', $query['Info']), true, 10)
											) {
												$info = 'NO_GETLOCK';
											} else {
												$info = null;
											}
										}
										if ($info) {
											echo "\n";
											echo $servername.'.'.$info.' running: '.$query['Time'].', state: '.$query['State'].', id: '.$query['Id'],"\n";
											echo $servername.'.'.$info.' query: '.combine_whitespace($query['Info']),"\n";
										}
									}
									mysqli_free_result($sub_result);
								}
							}
							mail_log(ob_get_clean());
						}
						$__warned = true;
					}
				}
			}
		}
	}

#	if ($debug) {
#		error_log($debug.': getting from db instead of mem');
#	}

	$context = [];

	$result = db_read(
		$tables,
		$qstr,
		$type,
		$flags | ($freshen_cache ? DB_USE_MASTER : 0),
		$arg,
		context: $context,
	);

	if ($memcache) {
		if ($result !== false
		&&	!$memcache->set($key, $result, $expires ?: DEFAULT_EXPIRATION)
		) {
			memcached_set_failed('set', $key);
		}
		if (WAIT_FOR_QUERY
		&&	!$memcache->set($context_key = "$key\x0Fcontext", $context, WAIT_FOR_QUERY + 100)
		) {
			memcached_set_failed('set', $context_key);
		}
	}
	if ($lock) {
		db_releaselock($lock);
	}
	return $__cache[$key] = $result;
}

function memcached_geo_for_party(int $id): array|false {
	static $__info = [];
	return $__info[$id] ??= memcached_single_assoc(['location', 'city'], '
		SELECT	city.NAME AS CITY_NAME,
				IF(location.LATITUDE, location.LATITUDE,  city.LATITUDE)  AS LAT,
				IF(location.LATITUDE, location.LONGITUDE, city.LONGITUDE) AS LON
		FROM party
		LEFT JOIN location USING (LOCATIONID)
		LEFT JOIN city ON city.CITYID = COALESCE(location.CITYID, location.CITYID, party.CITYID)
		WHERE PARTYID = '.$id
	) ?: false;
}

function memcached_cities_within_radius(float $latitude, float $longitude, int $radius): array|false|null {
	return memcached_simpler_array('city', '
		SELECT CITYID
		FROM city
		WHERE '.distance($latitude, $longitude, 'LATITUDE', 'LONGITUDE').' <= '.$radius,
		TEN_MINUTES
	);
}

function memcached_genre(int $gid): string|false|null {
	static $__genre = [];
	return $__genre[$gid] ??= memcached_single('genre', "SELECT NAME FROM genre WHERE GID = $gid", ONE_HOUR) ?: "genre:$gid";
}

function memcached_prefetch_users(array $userids): void {
	if (!$userids) {
		return;
	}
	global $__user;
	$__user ??= [];
	if (isset($userids[0])) {
		foreach (array_diff($userids, array_keys($__user)) as $userid) {
			if ($userid === true) {
				mail_log('array passed to memcached_prefetch_users has $userid = true', get_defined_vars(), error_log: 'ERROR');
				return;
			}
			$key = get_user_mem_key($userid);
			$keys[] = $key;
		}
	} else {
		foreach (array_diff_key($userids, $__user) as $userid => $ignored) {
			$key = get_user_mem_key($userid);
			$keys[] = $key;
		}
	}
	if (!isset($keys)
	||	!($results = $GLOBALS['memcache']->getMulti($keys))
	) {
		return;
	}
	$__user += $results;
}

function memcached_nick(int $userid): string|false {
	if ($user = memcached_user($userid)) {
		return $user['NICK'];
	}
	return false;
}

function get_user_mem_key(int $userid): string {
	return "ua:v10:$userid";
}

function memcached_user(int $userid): array|false {
	global $__user;
	if (!isset($__user)) {
		$__user = [];
	} elseif (isset($__user[$userid])) {
		return $__user[$userid];
	}
	if (false === ($user = memcached_single_assoc(
		['user_account', 'externalsettings', 'user', 'city', 'country'], '
		SELECT	ua.USERID, ua.NICK, STATUS, EMAIL, user.NAME, REALNAME,
			  	DATING, VISIBILITY_GENDER, SEX, SEXPREF,
				user.CSTAMP, BIRTH_YEAR, BIRTH_MONTH, BIRTH_DAY, LDAY,
				CITYID, country.COUNTRYID, city.LATITUDE, city.LONGITUDE,
				VISIBILITY_ONLINE, VISIBILITY_PROFILE
		FROM user_account ua
		LEFT JOIN externalsettings USING (USERID)
		LEFT JOIN user USING (USERID)
		LEFT JOIN city USING (CITYID)
		LEFT JOIN country ON country.COUNTRYID = COALESCE(city.COUNTRYID, user.COUNTRYID)
		WHERE ua.USERID = '.$userid,
		HALF_HOUR,
		get_user_mem_key($userid)))
	) {
		return $__user[$userid] = false;
	}
	return $__user[$userid] = $user ?: [];
}

function memcached_get_login(int $userid): ?array {
	# this is not a slow query.
	# sporadic cache problems seemed to occur recently
	# force no memcache
	return db_single_assoc(['user_account', 'user_login', 'user'], "
		SELECT	USERID, user_account.NICK, STATUS,
				user_login.PASSWD,
				EMAIL, DATING, SEX, SEXPREF, CSTAMP, NAME, REALNAME
		FROM user_account
		JOIN user_login USING (USERID)
		JOIN user USING (USERID)
		WHERE USERID = $userid",
		DB_FORCE_MASTER
	) ?: null;
}

function flush_user(int $userid): void {
	global $__user;
	/** @noinspection UnusedFunctionResultInspection */
	memcached_delete(
		$key = get_user_mem_key($userid),
		'userprofile:'.$userid,
		'dating:'.$userid
	);
	unset($__user[$key]);
}

function memcached_forum(int $forumid, ?string $attrib = null): int|string|array|false|null {
	static $__forum = [];
	if (!($forum = memcached_single_assoc('forum', "
		SELECT NAME, SHORT, FLAGS
		FROM forum
		WHERE FORUMID = $forumid",
		ONE_HOUR))
	) {
		return $forum;
	}
	$__forum[$forumid] = $forum;
	if (!$attrib) {
		return $__forum[$forumid];
	}
	return	$__forum[$forumid][$attrib] ?? false;
}

function memcached_topic(int $topicid): array|false {
	static	$__topic = [];
	return	$__topic[$topicid]
	??= (	memcached_single_assoc('topic', "
			SELECT SUBJECT, FLAGS, STATUS, LSTAMP, FORUMID
			FROM topic
			WHERE TOPICID = $topicid",
			TEN_MINUTES)
		?:	false
		);
}

function memcached_status(int $userid): string|false {
	global $__user;
	if (!isset($__user[$userid])) {
		if (!($user = memcached_user($userid))) {
			return false;
		}
		if (!isset($user['STATUS'])) {
			mail_log('memcached_status: no STATUS for user '.$userid);
			return false;
		}
		return $user['STATUS'];
	}
	if (!$__user[$userid]) {
		return false;
	}
	return $__user[$userid]['STATUS'];
}

function memcached_organization(int $organizationid): string|false {
	static	$__organization = [];
	return	$__organization[$organizationid]
	??=	(	memcached_single('organization', "
			SELECT NAME
			FROM organization
			WHERE ORGANIZATIONID = $organizationid",
			TEN_MINUTES)
		?:	false
		);
}

function memcached_location(int $locationid): string {
	if ($location = memcached_location_info($locationid)) {
		return $location['NAME'];
	}
	return '';
}

function memcached_location_info(int $locationid): array|false {
	static	$__location = [];
	return	$__location[$locationid]
	??=	(	memcached_single_assoc('location', "
			SELECT	IF(NAME != '', NAME, TITLE) AS NAME,
					ORDERNAME,
					CITYID,
					TITLE,
					FOLLOWUPID,
					DEAD
			FROM location
			WHERE LOCATIONID = $locationid",
			TEN_MINUTES)
		?:	false
		);
}

function memcached_albummap(int $id): array|false {
	static	$__albummap = [];
	return	$__albummap[$id]
	??=	(	memcached_single_assoc('albummap',"
			SELECT	MAPID,
					UPMAPID,
					TITLE,
					ALBUMID
			FROM albummap
			WHERE MAPID = $id")
		?:	false
		);
}

function memcached_albumelement(int $id): array|false {
	static $__albumelement = [];
	return	$__albumelement[$id]
	??=	(	memcached_single_assoc('albumelement',"
			SELECT ALBUMID, ORIGNAME, TITLE, FLAGS, ACCEPTED
			FROM albumelement
			WHERE ALBUMELEMENTID = $id")
		?:	false
		);
}
function memcached_flockname(int $flockid): string|false {
	static	$__flock = [];
	return	$__flock[$flockid]
	??=	(	memcached_single('flock', "
			SELECT NAME
			FROM flock
			WHERE FLOCKID = $flockid", ONE_HOUR)
		?:	false
		);
}
function memcached_timezone_city(int $cityid): false|string {
	static $__timezone_city = [];
	return $__timezone_city[$cityid] ??= ($city = memcached_city_info($cityid)) ? $city['TIMEZONE'] : false;
}
function memcached_city(int $cityid): string|false {
	if ($city = memcached_city_info($cityid)) {
		return $city['NAME'];
	}
	return false;
}

function memcached_city_info(int $cityid): array|false|null {
	static $__city = [];
	return $__city[$cityid] ??= memcached_single_assoc(['city','country'],"
			SELECT	city.CITYID,
					city.NAME,
					city.PROVINCEID,
					city.COUNTRYID,
					city.TIMEZONE,
					city.LATITUDE,
					city.LONGITUDE,
					country.SHORT
			FROM city
			LEFT JOIN country USING (COUNTRYID)
			WHERE CITYID  = $cityid",
			ONE_HOUR
		) ?: false;
}
const NO_GEO = 1;

function get_current_city(int $flags = 0): array|false {
	static $__current_city;
	if (isset($__current_city)) {
		return $__current_city;
	}

	if (is_server_ip()
	||	in_array(is_disallowed(), [DISALLOW_TOR, DISALLOW_PROXY, DISALLOW_HARVEST], true)
	) {
		# don't use servers to detect city, nor proxies or tor exit nodes
		return $__current_city = false;
	}

	# get from profile

	if (have_user()
	&&	($__current_city = memcached_user_city(CURRENTUSERID))
	) {
		$__current_city['SURE'] = true;
		return $__current_city;
	}

	require_once '_geo.inc';
	if ($geo = ($flags & NO_GEO) ? false : get_geolocation_cache()) {
		# try recent geo's
		[$latitude, $longitude, $mstamp, $small_screen] = $geo;

		if ($mstamp > CURRENTSTAMP - ONE_MONTH
		&&	$latitude !== null
		&&	($nearest = memcached_find_nearest_city($latitude, $longitude, 10))
		) {
			# count as 'sure' if recently determined
			if ($__current_city = memcached_city_info($nearest)) {
				$__current_city['SURE'] = $mstamp > CURRENTSTAMP - 60;
			}
			return $__current_city;
		}
	}

	# try recent ipcity
	if (IPV6) {
		[$prefix, $iident] = ipbin_to_prefix_and_iident(CURRENTIPBIN);
		$cityid = memcached_single('ip6city', '
			SELECT CITYID
			FROM ip6city
			WHERE DAYNUM > '.(CURRENTDAYNUM - ONE_MONTH_DAYS)."
			  AND PREFIX = 0x$prefix
		  /*  AND IIDENT = 0x$iident */
				GROUP BY CITYID
				ORDER BY SUM(HITS) DESC
				LIMIT 1",
			TEN_MINUTES
		);
	} else {
		$cityid = memcached_single('ipcity', '
			SELECT CITYID
			FROM ipcity
			WHERE DAYNUM > '.(CURRENTDAYNUM - ONE_MONTH_DAYS).'
			  AND IPNUM = '.CURRENTIPNUM.'
			GROUP BY CITYID
			ORDER BY SUM(HITS) DESC
			LIMIT 1',
			TEN_MINUTES
		);
	}

	if ($cityid) {
		if ($__current_city = memcached_city_info($cityid)) {
			$__current_city['SURE'] = false;
		}
		return $__current_city;
	}

	/** @noinspection NotOptimalIfConditionsInspection */
	if ($geo
	&&	isset($latitude, $longitude, $small_screen)
		# NOTE: try any geo, but only for non small-screens
		# QUESTION: Why only for non small-screens?
	&&	!$small_screen
	&&	($nearest = memcached_find_nearest_city($latitude, $longitude, 10))
	) {
		$__current_city = memcached_city_info($nearest);
		if ($__current_city) {
			$__current_city['SURE'] = false;
		}
		return $__current_city;
	}
	# try any ipcity
	if (IPV6) {
		assert(isset($prefix, $iident)); # Satisfy EA inspection
		$cityid = memcached_single_int('ip6city', "
			SELECT CITYID
			FROM ip6city
			WHERE PREFIX = 0x$prefix
			  AND IIDENT = 0x$iident
			GROUP BY CITYID
			ORDER BY SUM(HITS) DESC
			LIMIT 1",
			ONE_HOUR
		);
	} else {
		$cityid = memcached_single_int('ipcity', '
			SELECT CITYID
			FROM ipcity
			WHERE IPNUM = '.CURRENTIPNUM.'
			GROUP BY CITYID
			ORDER BY SUM(HITS) DESC
			LIMIT 1',
			ONE_HOUR
		);
	}
	if ($cityid) {
		if ($__current_city = memcached_city_info($cityid)) {
			$__current_city['SURE'] = false;
		}
		return $__current_city;
	}
	return false;
}

function memcached_find_nearest_city(
	float	$near_latitude,
	float	$near_longitude,
	?int	 $max_distance	= 2,
	?float	&$distance		= null
): int|false {
	if (!($city = memcached_single_array('city','
		SELECT CITYID, LATITUDE, LONGITUDE
		FROM city
		ORDER BY '.distance($near_latitude, $near_longitude, 'LATITUDE', 'LONGITUDE').'
		LIMIT 1'))
	) {
		return false;
	}
	[$cityid, $latitude, $longitude] = $city;

	$distance = calculate_distance($near_latitude, $near_longitude, $latitude, $longitude);

	if (!$max_distance
	||	 $max_distance > $distance
	) {
		return $cityid;
	}
	return false;
}

function memcached_user_city(int $userid): array {
	static	$__cc = [];
	if (isset($__cc[$userid])) {
		return $__cc[$userid];
	}
	if (!($cityid = memcached_single_int('user', "
		SELECT CITYID
		FROM user
		WHERE NOT INVALID_CITY
		  AND USERID = $userid"))
	) {
		return $__cc[$userid] = [];
	}
	return $__cc[$userid] = memcached_city_info($cityid) ?: [];
}
function memcached_gallery(int $galleryid): array {
	static $__gallery = [];
	if (isset($__gallery[$galleryid])) {
		return $__gallery[$galleryid];
	}
	if ($gallery = memcached_single_assoc('gallery', "
		SELECT PARTYID, TITLE, VISIBLE, USERID, PSTAMP, BODY, PHOTOGRAPHER, PHOTOGRAPHER_SITE
		FROM gallery
		WHERE GALLERYID = $galleryid")
	) {
		$gallery['GALLERYID'] = $galleryid;
		$gallery['party']	  = $gallery['PARTYID'] ? memcached_party_and_stamp($gallery['PARTYID']) : null;
	} else {
		$gallery = [];
	}
	return $__gallery[$galleryid] = $gallery;
}

function memcached_party(int $partyid): string|false|null {
	if ($party = memcached_party_and_stamp($partyid)) {
		return $party['NAME'];
	}
	if ($party === false) {
		return false;
	}
	return null;
}

const PARTY_AND_STAMP	= 'party_and_stamp:v35:';
const PARTY_CACHE		= 'party_cache:v9:';
const PARTY_CACHE_APPIC	= 'party_cache_appic:v6:';
const PARTY_META_IMAGE	= 'party:meta:location:image:';
const PARTY_LINEUP_ROW	= 'party:lineup_row:v3:';

function memcached_party_and_stamp(int $partyid, bool $freshen_cache = false, bool $mail_when_nonexistent = false): array|false {
	# returns:
	# array:	party array
	# false:	query failure
	# []:		event not found

	require_once 'defines/appic.inc';
	static $__party = [];
	if (isset($__party[$partyid])) {
		return $__party[$partyid];
	}
	if (!($freshen_cache || isset($_REQUEST['NOMEMCACHE']))
	&&	($party = memcached_get(PARTY_AND_STAMP.$partyid))
	) {
		return $__party[$partyid] = $party;
	}
	if (!($party = db_single_assoc(['party', 'location', 'boarding', 'city', 'connect', 'lineup', 'uploadimage_link'], /** @lang MariaDB */'
		SELECT	PARTYID, '.PARTY_MAIN_ID.", party.NAME, party.CSTAMP, SUBTITLE, LOCATIONID, BOARDINGID, NOTIME, AT2400,
				STAMP, STAMP_TZI, DURATION_SECS, MOVEDID, CANCELLED, party.ACCEPTED, party.DELETED, party.USERID,
				FREE_ENTRANCE, SOLD_OUT, party.EROTIC, UNKNOWN_LOCATION, party.MUSERID, DOORMORE, DOORONLY, PRESALEONLY,
				DRESSCODE, RESTRICTION, SLUG,
				LINEUP_PSTAMP, EDITION, party.SITE, PRESALE_SOLD_OUT, PRESALE_STAMP, SEPARATE, PREREG_URL,POSTPONED,
				party.LOCATIONTYPE, LIVESTREAM, AFTERMOVIE, PLACEHOLDER,
				FESTIVAL, CONCERT, PREPARTY, AFTERPARTY,
				COALESCE((	SELECT NAME
							FROM itemname
							WHERE ELEMENT = 'location'
							  AND ID = LOCATIONID
							  AND ENDSTAMP > STAMP
							ORDER BY ENDSTAMP DESC
							LIMIT 1
					),	location.NAME
				) AS LOCATION_NAME,
				city.CITYID, city.NAME AS CITY_NAME, city.COUNTRYID, TIMEZONE, REGIONID,
				boarding.NAME AS BOARDING_NAME,
				COALESCE(boarding.ADDRESS,  location.ADDRESS) AS ADDRESS,
				COALESCE(boarding.LATITUDE, location.LATITUDE, city.LATITUDE) as LAT,
				COALESCE(boarding.LONGITUDE,location.LONGITUDE,city.LONGITUDE) as LON,
				(	SELECT GROUP_CONCAT(ASSOCID)
					FROM connect
					WHERE MAINTYPE = 'party'
					  AND MAINID = PARTYID
					  AND ASSOCTYPE = 'organization'
				) AS ORGIDS,
				SUM(IF(WHOLE AND organization.NAME = party.NAME, 1, 0)) AS IS_WHOLE,
				(	SELECT GROUP_CONCAT(ASSOCID, ':', day.STAMP)
					FROM connect
					JOIN party AS day ON day.PARTYID = ASSOCID
					WHERE MAINTYPE = 'party'
					  AND MAINID = $partyid
					  AND ASSOCTYPE = 'party'
					  AND day.NAME = party.NAME
					  AND day.LOCATIONID = party.LOCATIONID
					  AND NOT SEPARATE
				) AS PARTY_DAYS,
				
				(SELECT 1 FROM lineup WHERE lineup.PARTYID = party.PARTYID LIMIT 1) AS HAVE_LINEUP,
				
				(SELECT 1 FROM lineup WHERE lineup.PARTYID = party.PARTYID AND START_STAMP LIMIT 1) AS HAVE_TIMETABLE,

					(SELECT 1 FROM lineup WHERE lineup.PARTYID = party.PARTYID AND START_STAMP LIMIT 1)
					/* May be published: */
				AND	TIMETABLE_PSTAMP < UNIX_TIMESTAMP()
				AND IF (
					(   SELECT COALESCE(COUNT(DISTINCT ARTISTID), 0)
						FROM lineup
						WHERE lineup.PARTYID = party.PARTYID)
						<= 6
					,
					/* All non-entire-area types have time (MC can perform entire area): */
					(
						SELECT 1 FROM lineup
						WHERE lineup.PARTYID = party.PARTYID
						  AND TYPE NOT IN ('mc', 'vj', 'lj', 'show')
						  AND (START_STAMP IS NULL OR NOT START_STAMP)
						LIMIT 1
					) IS NULL
					,
					/* Allow 10% non-timed-performances: */
					(	SELECT COUNT(*)
						FROM lineup
						WHERE lineup.PARTYID = party.PARTYID
						  AND TYPE NOT IN ('mc', 'vj', 'lj', 'show')
						  AND (START_STAMP IS NULL OR NOT START_STAMP)
					)
					/
					(	SELECT COUNT(*)
						FROM lineup
						WHERE lineup.PARTYID = party.PARTYID
						  AND TYPE NOT IN ('mc', 'vj', 'lj', 'show') AND START_STAMP
					)
					< .1
				) AS TIMETABLE_RELEASED,
				
				(SELECT 1 FROM uploadimage_link WHERE TYPE = 'eventmap' AND ID = PARTYID LIMIT 1) AS HAVE_MAP

		FROM party
		LEFT JOIN location USING (LOCATIONID)
		LEFT JOIN boarding USING (BOARDINGID)
		LEFT JOIN city ON city.CITYID = COALESCE(boarding.CITYID, location.CITYID, party.CITYID)
		LEFT JOIN connect ON MAINTYPE = 'party'
				 AND MAINID = PARTYID
				 AND ASSOCTYPE = 'organization'
		LEFT JOIN organization ON ORGANIZATIONID = ASSOCID
		WHERE PARTYID = $partyid"))

	# FIXME: Weird bug that returns entire array with NULL for all fields if non-existent party
	||	!$party['PARTYID']
	) {
		if ($party !== false) {
			$party = [];
			if ($mail_when_nonexistent) {
				mail_log("party $partyid does not exist", subject: 'nonexistent party');
			}
		}
		return $__party[$partyid] = $party;
	}
	if (!$party['PARTY_DAYS']) {
		$party['PARTY_DAYS'] = [];
	} else {
		$party['PARTY_DAYS'] .= ",$partyid:{$party['STAMP']}";
		$stamp_groups = [];
		$prev_stamp = 0;
		$group = 0;
		foreach (explode(',', $party['PARTY_DAYS']) as $partyid_stamp) {
			[$partyid, $stamp] = explode(':', $partyid_stamp);
			if (!$prev_stamp) {
				$prev_stamp = $stamp;
			} elseif ($stamp > $prev_stamp + 2 * ONE_DAY) {
				++$group;
				$prev_stamp = 0;
			} else {
				$prev_stamp = $stamp;
			}
			$stamp_groups[$group][$partyid] = $stamp;
		}
		# pick the group with current id
		foreach ($stamp_groups as /* $group => */ $stamp_events) {
			if (isset($stamp_events[$partyid])) {
				$days = $stamp_events;
				break;
			}
		}
		asort($days);
		$party['PARTY_DAYS'] = $days;
	}
	if ($party['ORGIDS']) {
		$party['ORGANIZATIONIDS'] = explode_to_hash(',', $party['ORGIDS']);
		asort($party['ORGANIZATIONIDS']);
	} else {
		$party['ORGANIZATIONIDS'] = [];
	}
	memcached_set(PARTY_AND_STAMP.$partyid, $party, ONE_HOUR);
	return $__party[$partyid] = $party;
}

function flush_party(int|array $arg, array $more_prefixes = []): void {
	$flush = [];
	foreach ((is_array($arg) ? $arg : [$arg]) + $more_prefixes as $partyid) {
		foreach ([
			PARTY_AND_STAMP,
			PARTY_CACHE,
			PARTY_CACHE_APPIC,
			PARTY_META_IMAGE
		] as $key_prefix) {
			$flush[] = $key_prefix.$partyid;
		}
	}
	memcached_delete($flush);
}

function memcached_report(int $report_id): array|false {
	static $__report = [];
	return $__report[$report_id] ??= memcached_single_assoc(['report', 'party'], '
			SELECT report.USERID, PARTYID, party.NAME, STAMP, TITLE
			FROM report
			LEFT JOIN party USING (PARTYID)
			WHERE REPORTID = '.$report_id,
			TEN_MINUTES
		) ?: false;
}

function memcached_contest(int $contest_id): array|false {
	static $__contest = [];
	return $__contest[$contest_id] ??= memcached_single_assoc(['contest', 'party'], '
			SELECT	contest.NAME, contest.NAME AS CONTEST_NAME, contest.PSTAMP, CLOSE_STAMP, TYPE, ACTIVE, VISIBLE_GROUPS, AMOUNT,
					PARTYID, party.NAME AS PARTY_NAME, STAMP
			FROM contest
			LEFT JOIN party USING (PARTYID)
			WHERE CONTESTID = '.$contest_id,
			TEN_MINUTES
		) ?: false;
}

function memcached_color(int $userid): string|false {
	static $__user_color = [];
	return $__user_color[$userid] ??= memcached_single('user', 'SELECT COLOR FROM user WHERE USERID = '.$userid, TEN_MINUTES) ?: false;
}

function memcached_artist(int $artistid, bool $flush = false): string|false {
	static $__artist = [];
	return $__artist[$artistid] ??= memcached_single('artist','
			SELECT NAME
			FROM artist
			WHERE ARTISTID = '.$artistid,
			ONE_HOUR,
			'artist:v3:'.$artistid,
			$flush ? DB_FORCE_MASTER | DB_FRESHEN_MEMCACHE : 0	# NOSONAR
	) ?: false;
}

function flush_artist_name(int $artistid): void {
	memcached_delete('artist:v3:'.$artistid);
}

function memcached_producer_alias(int $id): string|false {
	static $__pal = [];
	return $__pal[$id] ??= memcached_single('musicproduceralias', 'SELECT NAME FROM musicproduceralias WHERE ALIASID = '.$id, ONE_HOUR) ?: false;
}

function memcached_relation(int $relationid): string|false {
	static $__relation = [];
	return $__relation[$relationid] ??= memcached_single('relation', 'SELECT NAME FROM relation WHERE RELATIONID = '.$relationid, TEN_MINUTES) ?: false;
}
/*function memcached_photographer(int $imgid): int|false {
	static $__photographer = [];
	return $__photographer[$imgid] ??= memcached_single('image', 'SELECT USERID FROM image WHERE IMGID = '.$imgid, HALF_DAY) ?: false;
}*/

function memcached_try_netname_from_netstr(string $netipstr): string {
	return memcached_netname_from_netstr($netipstr, try: true);
}

function memcached_netname_from_netstr(string $netipstr, bool $try = false): string {
	return memcached_hostname_from_ipbin(null, $netipstr, $try);
}

function memcached_try_hostname_from_ipbin(string $ipbin, ?string $ipstr = null): string {
	return memcached_hostname_from_ipbin($ipbin, $ipstr, try: true);
}

# FIXME: This function should support both ascii & html better.
function memcached_hostname_from_ipbin(
	?string			$ipbin,
	?string			$ipstr		= null,
	bool			$try 		= false,
	?bool		   &$fetched	= null,
): string {
	# NOTE: Returns escaped string!
	# TODO: Don't escape here, since it seems a little weird to escape here.

	global $__name_from_ip;
	$net = !$ipbin;
	$ndx = $net ? $ipstr : $ipbin;
	if (isset($__name_from_ip[$ndx])) {
		return $__name_from_ip[$ndx];
	}
	global $memcache;
	$key = $net ? 'netname:v4:'.$ipstr : 'hostname:v3:'.bin2hex($ipbin);

	if ($memcache
	&&	!isset($_REQUEST['NOMEMCACHE'])
	) {
		$name = $memcache->get($key);
	}
	if (!empty($name)) {
		return $__name_from_ip[$ndx] = $name;
	}
	if ($try) {
		return '';
	}
	if ($net) {
		require_once '_execute.inc';
		[$rc, $stdout, $stderr] = execute('timeout 5 whois '.$ipstr);
		if ($rc) {
			$name = $stderr;
			#mail_log('whois '.$ipstr.': '.$stderr);
		} else {
			$count = 0;
			$parts = [];
			foreach (explode("\n", $stdout) as $line) {
				if (preg_match('"^(descr|netname|orgname):(.*)"i', mb_strtolower($line), $match)) {
					$parts[$match[1]] = '<span class="nb">'.match($match[1]) {
						'descr'		=> 'description',
						'netname'	=> 'netname',
						'orgname'	=> 'organization name',
						default		=> $match[1],
					}.':</span> <b>'.escape_utf8(utf8_mytrim($match[2])).'</b>';

					if (++$count === 3) {
						break;
					}
				}
			}
			$name = implode(", \n", $parts);
		}
	} else {
		putenv('RES_OPTIONS=timeout:30 attempts:5');
		$name = escape_utf8(gethostbyaddr($ipstr ?: inet_ntop($ipbin)));
	}
	$fetched = true;
	if ($memcache) {
		$memcache->set($key, $name, ONE_MONTH);
	}
	return $__name_from_ip[$ndx] = $name ?: '';
}

function memcached_single			(string|array|null $tables, string $qstr, ?int $expires = null, string|false|null $key = null, int $flags = 0, int|string|array|null $arg = null): int|float|string|bool|null	{ return memcached_read(SINGLE, 			$tables, $qstr, $expires, $key, $flags, $arg); }
# function memcached_single_bool	(string|array|null $tables, string $qstr, ?int $expires = null, string|false|null $key = null, int $flags = 0, int|string|array|null $arg = null): ?bool						{ return memcached_read(SINGLE, 			$tables, $qstr, $expires, $key, $flags, $arg); }
function memcached_single_int		(string|array|null $tables, string $qstr, ?int $expires = null, string|false|null $key = null, int $flags = 0, int|string|array|null $arg = null): int|false|null				{ return memcached_read(SINGLE, 			$tables, $qstr, $expires, $key, $flags, $arg); }
function memcached_single_string	(string|array|null $tables, string $qstr, ?int $expires = null, string|false|null $key = null, int $flags = 0, int|string|array|null $arg = null): string|false|null			{ return memcached_read(SINGLE, 			$tables, $qstr, $expires, $key, $flags, $arg); }
function memcached_single_assoc		(string|array|null $tables, string $qstr, ?int $expires = null, string|false|null $key = null, int $flags = 0, int|string|array|null $arg = null): array|false|null				{ return memcached_read(SINGLE_ASSOC,		$tables, $qstr, $expires, $key, $flags, $arg); }
/** @noinspection DuplicatedCode */
function memcached_single_array		(string|array|null $tables, string $qstr, ?int $expires = null, string|false|null $key = null, int $flags = 0, int|string|array|null $arg = null): array|false|null				{ return memcached_read(SINGLE_ARRAY,		 $tables, $qstr, $expires, $key, $flags, $arg); }
function memcached_rowuse_array		(string|array|null $tables, string $qstr, ?int $expires = null, string|false|null $key = null, int $flags = 0, int|string|array|null $arg = null): array|false 					{ return memcached_read(ROW_USE_ARRAY, 	$tables, $qstr, $expires, $key, $flags, $arg); }
function memcached_simpler_array	(string|array|null $tables, string $qstr, ?int $expires = null, string|false|null $key = null, int $flags = 0, int|string|array|null $arg = null): array|false					{ return memcached_read(SIMPLE_ARRAY,		$tables, $qstr, $expires, $key, $flags, $arg); }

function memcached_same_hash		(string|array|null $tables, string $qstr, ?int $expires = null, string|false|null $key = null, int $flags = 0, int|string|array|null $arg = null): array|false 					{ return memcached_read(SAME_HASH, 		$tables, $qstr, $expires, $key, $flags, $arg); }
/** @noinspection DuplicatedCode */
function memcached_simple_hash		(string|array|null $tables, string $qstr, ?int $expires = null, string|false|null $key = null, int $flags = 0, int|string|array|null $arg = null): array|false					{ return memcached_read(SIMPLE_HASH, 		$tables, $qstr, $expires, $key, $flags, $arg); }
function memcached_boolean_hash		(string|array|null $tables, string $qstr, ?int $expires = null, string|false|null $key = null, int $flags = 0, int|string|array|null $arg = null): array|false 					{ return memcached_read(BOOLEAN_HASH,	 	$tables, $qstr, $expires, $key, $flags, $arg); }
function memcached_rowuse_hash		(string|array|null $tables, string $qstr, ?int $expires = null, string|false|null $key = null, int $flags = 0, int|string|array|null $arg = null): array|false					{ return memcached_read(ROW_USE_HASH,		$tables, $qstr, $expires, $key, $flags, $arg); }
function memcached_multirow_hash	(string|array|null $tables, string $qstr, ?int $expires = null, string|false|null $key = null, int $flags = 0, int|string|array|null $arg = null): array|false 					{ return memcached_read(MULTI_ROW_HASH, 	$tables, $qstr, $expires, $key, $flags, $arg); }
function memcached_multirowuse_hash	(string|array|null $tables, string $qstr, ?int $expires = null, string|false|null $key = null, int $flags = 0, int|string|array|null $arg = null): array|false 					{ return memcached_read(MULTI_ROW_USE_HASH,$tables, $qstr, $expires, $key, $flags, $arg); }

// DEPENDENT CACHING
function memcached_single_assoc_if_not_self_nor_any_admin(?int $userid, string|array|null $tables, string $qstr, ?int $expires = null, string|false|null $key = null, int $flags = 0): array|false|null {
	return memcached_read_if_not_self_nor_admin($userid,null,$tables,$qstr,SINGLE_ASSOC,$expires,$key,$flags);
}
function memcached_single_assoc_if_not_self_nor_admin(?int $userid, $portion, string|array|null $tables, string $qstr, ?int $expires = null, string|false|null $key = null, int $flags = 0): array|false|null {
	return memcached_read_if_not_self_nor_admin($userid, $portion, $tables, $qstr, SINGLE_ASSOC, $expires, $key, $flags);
}
function memcached_single_if_not_self_nor_any_admin(?int $userid, string|array|null $tables, string $qstr, ?int $expires = null, $key = null): int|float|string|bool|null {
	return memcached_read_if_not_self_nor_admin($userid, null, $tables, $qstr, SINGLE, $expires, $key);
}
function memcached_single_if_not_self(?int $userid, string|array|null $tables, string $qstr, ?int $expires = null, string|false|null $key = null): int|float|string|bool|null {
	return memcached_read_if_not_self($userid, $tables, $qstr,SINGLE, $expires, $key);
}
function memcached_single_if_not_admin(string|array|null $portion, string|array|null $tables, string $qstr, ?int $expires = null, string|false|null $key = null): int|float|string|bool|null {
	return memcached_read_if_not_admin($portion, $tables, $qstr, SINGLE, $expires, $key);
}
function memcached_simple_hash_if_not_admin(string|array|null $portion, string|array|null $tables, string $qstr, ?int $expires = null, string|false|null $key = null): array|false {
	return memcached_read_if_not_admin($portion, $tables, $qstr, SIMPLE_HASH, $expires, $key);
}
function memcached_simpler_array_if_not_admin(string|array|null $portion, string|array|null $tables, string $qstr, ?int $expires = null, string|false|null $key = null): array|false {
	return memcached_read_if_not_admin($portion, $tables, $qstr,SIMPLE_ARRAY, $expires, $key);
}
function memcached_simpler_array_if_not_self_nor_admin(?int $userid, string|array|null $portion, string|array|null $tables,$qstr, ?int $expires = null, string|false|null $key = null): array|false {
	return memcached_read_if_not_self_nor_admin($userid, $portion, $tables, $qstr, SIMPLE_ARRAY, $expires, $key);
}
function memcached_single_assoc_if_not_self(?int $userid, string|array|null $tables, string $qstr, ?int $expires = null, string|false|null $key = null): array|false|null {
	return memcached_read_if_not_self($userid, $tables, $qstr, SINGLE_ASSOC, $expires, $key);
}
function memcached_rowuse_hash_if_not_self(?int $userid, string|array|null $tables, string $qstr, ?int $expires = null, string|false|null $key = null): array|false {
	return memcached_read_if_not_self($userid, $tables, $qstr, ROW_USE_HASH, $expires, $key);
}
function memcached_simple_hash_if_not_self(?int $userid, string|array|null $tables, string $qstr, ?int $expires = null, string|false|null $key = null): array|false{
	return memcached_read_if_not_self($userid, $tables, $qstr, SIMPLE_HASH, $expires, $key);
}
function memcached_rowuse_hash_if_not_admin(string|array|null $portion, string|array|null	$tables, string $qstr, ?int $expires = null, string|false|null $key = null, int $flags = 0): array|false {
	return memcached_read_if_not_admin($portion, $tables, $qstr, ROW_USE_HASH, $expires, $key, $flags);
}
function memcached_single_assoc_if_not_admin($portion,$tables,$qstr, ?int $expires = null,$key = null) {
	return memcached_read_if_not_admin($portion,$tables,$qstr,SINGLE_ASSOC,$expires,$key);
}
function memcached_rowuse_array_if_not_admin(string|array|null $portion, string|array|null $tables, string $qstr, ?int $expires = null, string|false|null $key = null, int $flags = 0): array|false {
	return memcached_read_if_not_admin($portion, $tables, $qstr, ROW_USE_ARRAY, $expires, $key, $flags);
}
function memcached_rowuse_array_if_not_self_nor_admin(int $userid, string|array|null $portion, string|array|null $tables, string $qstr, ?int $expires = null, string|false|null $key = null): array|false {
	return memcached_read_if_not_self_nor_admin($userid, $portion, $tables, $qstr, ROW_USE_ARRAY, $expires, $key);
}
function memcached_rowuse_array_if_not_self(int $userid, string|array|null $tables, string $qstr, ?int $expires = null, string|false|null $key = null): array|false {
	return memcached_read_if_not_self($userid, $tables, $qstr, ROW_USE_ARRAY, $expires, $key);
}
# almost not used:
function memcached_single_assoc_if_not_owner(string|array $id_names, string|array|null $tables, string $qstr, ?int $expires = null, string|false|null $key = null, int $flags = 0): array|false|null {
	if (!($result = memcached_read(SINGLE_ASSOC, $tables, $qstr, $expires, $key, $flags))) {
		return $result;
	}
	if (is_array($id_names)) {
		$nocache = false;
		foreach ($id_names as $id_name) {
			if (CURRENTUSERID === $result[$id_name]) {
				$nocache = true;
				break;
			}
		}
		if (!$nocache) {
			return $result;
		}
	} elseif (CURRENTUSERID !== $result[$id_names]) {
		return $result;
	}
	return memcached_read(SINGLE_ASSOC, $tables, $qstr, $expires, $key, $flags | (TRY_WITHOUT_FRESHEN ? 0 : DB_FRESHEN_MEMCACHE));
}

// GENERIC
function memcached_read_if_not_self(int $userid, string|array|null $tables, string $qstr, int $type, ?int $expires = null, string|false|null $key = null, int $flags = 0): int|float|string|bool|array|null {
	return memcached_read($type, $tables, $qstr, $expires, $key, $flags | (!TRY_WITHOUT_FRESHEN && CURRENTUSERID === $userid ? DB_FRESHEN_MEMCACHE : 0));
}

function memcached_read_if_not_admin(
	string|array|null	$portion,
	string|array|null	$tables,
	string				$qstr,
	int 				$type,
	?int				$expires	= null,
	?string				$key		= null,
	int					$flags		= 0,
): mixed {
	return memcached_read(
		$type,
		$tables,
		$qstr,
		$expires,
		$key,
		$flags | (!TRY_WITHOUT_FRESHEN && have_admin($portion) ? DB_FRESHEN_MEMCACHE : 0)
	);
}

function memcached_read_if_not_self_nor_admin(
	?int				$userid,
	string|array|null	$portion,
	string|array|null	$tables,
	string				$qstr,
	int					$type,
	?int				$expires = null,
	string|false|null	$key	 = null,
	int					$flags	 = 0
): int|float|string|bool|array|null {
	return memcached_read(
		$type,
		$tables,
		$qstr,
		$expires,
		$key,
		$flags | (!TRY_WITHOUT_FRESHEN && ($userid && $userid === CURRENTUSERID || $portion && have_admin($portion)) ? DB_FRESHEN_MEMCACHE : 0)
	);
}

function set_nomemcache(): void {
	if (!isset($_REQUEST['NOMEMCACHE'])) {
		$_REQUEST['NOMEMCACHE'] = 'tmp';
	}
}

function unset_nomemcache(): void {
	if (isset($_REQUEST['NOMEMCACHE'])
	&&	$_REQUEST['NOMEMCACHE'] === 'tmp'
	) {
		unset($_REQUEST['NOMEMCACHE']);
	}
}

function partylist_refresh_needed(?string $refresh_element = null, ?int $refresh_id = null): bool {
	static $__needed;
	if (isset($__needed)) {
		return $__needed;
	}
	$refresh_element ??= $_REQUEST['sELEMENT'];
	$refresh_id	 	 ??= $_REQUEST['sID'];
	$refresh_key = 'partylist_refresh:'.$refresh_element.($refresh_id ? ':'.$refresh_id : '');

	if ($refresh_start = memcached_get($refresh_key)) {
		# during entire CACHE_PERIOD_PARTYLIST, the partylists queries must be refreshed for this item.
		# not optimal, but all other events are cached for longer period
		if ($refresh_start < CURRENTSTAMP - CACHE_PERIOD_PARTYLIST) {
			memcached_delete($refresh_key);
		}
		return $__needed = true;
	}
	return $__needed = false;
}

function partylist_needs_refresh(string $element, int|array|null $arg = null): void {
	foreach (is_array($arg) ? $arg : [$arg] as $id) {
		$refresh_key = 'partylist_refresh:'.$element.($id ? ':'.$id : '');
		memcached_set($refresh_key, CURRENTSTAMP, ONE_WEEK);
	}
}

function tickets_updated(bool $set = false): bool {
	static $__updated = null;
	if ($set) {
		$__updated = true;
		memcached_set('tickets_updated:'.CURRENTUSERID, CURRENTSTAMP, TEN_MINUTES);
		return true;
	}
	if ($__updated === null) {
		$mstamp = memcached_get('tickets_updated:'.CURRENTUSERID);
		$__updated = $mstamp && $mstamp > (CURRENTSTAMP - ONE_MINUTE);
	}
	return $__updated;
}

function memcached_set_failed(
	string		 $action,
	string|array $key_or_keys,
	?int		 $result_code = null,
): false {
	# useful for queries that might return false as proper result (memcached_get)
	global $memcache;
	static $__shown;
	$result_code ??= $memcache->getResultCode();
	if (isset($__shown[$result_code])) {
		return false;
	}
	$__shown[$result_code] = true;
	error_log(
		'memcached '.$action.' failed for '.(is_array($key_or_keys) ? implode(', ', $key_or_keys) : $key_or_keys).
		' with result code '.$result_code.
		': '.$GLOBALS['memcache']->getResultMessage().
		(empty($_SERVER['REQUEST_URI']) ? '' : ' @ '.$_SERVER['REQUEST_URI'])
	);
	return false;
}
