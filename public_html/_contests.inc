<?php

declare(strict_types=1);

function get_contests(string $element, array|string $idstr): array {
	if (is_array($idstr)) {
		sort($idstr);
		$idstr = implode(', ', $idstr);
	}
	static $__contests = [];
	return	$__contests[$idstr] ??= memcached_same_hash(['contest','connect'], "
		SELECT DISTINCT MAINID, CONTESTID
		FROM connect
		JOIN contest ON CONTESTID = ASSOCID
		WHERE MAINTYPE = '$element'
		  AND MAINID IN ($idstr)
		  AND ASSOCTYPE = 'contest'
		  AND PSTAMP < ".strtotime('+1 day', TODAYSTAMP).'
		  AND CLOSED = 0
		  AND NOT ONLYPROMO
		UNION
		'.(	$element === 'party'
		? "	SELECT DISTINCT PARTYID, CONTESTID
			FROM contest
			WHERE PARTYID IN ($idstr)
			  AND PSTAMP < ".strtotime('+1 day', TODAYSTAMP).'
			  AND CLOSED = 0
			  AND NOT ONLYPROMO'
		: " SELECT DISTINCT MAINID, CONTESTID
			FROM connect
			JOIN contest ON PARTYID = ASSOCID
			WHERE MAINTYPE = '$element'
			  AND MAINID IN ($idstr)
			  AND ASSOCTYPE = 'party'
			  AND PSTAMP < ".strtotime('+1 day', TODAYSTAMP).'
			  AND CLOSED = 0
			  AND NOT ONLYPROMO'
		),
		ONE_MINUTE
	) ?: [];
}

function show_wins(array $contests, int|false|null $id, bool $dot_prefix = false): void {
	if (!$contests
	||	!$id
	||	!isset($contests[$id])
	){
		return;
	}
	ob_start();
	foreach ($contests[$id] as $contestid) {
		?> <a target="_blank" class="win" href="<?= get_element_href('contest',$contestid) ?>"><?= __('partylist:win') ?></a><?
	}
	if (!($inner = ob_get_clean())) {
		return;
	}
	if ($dot_prefix) {
		?><small> <?= MIDDLE_DOT_ENTITY ?> <?= $inner ?></small><?
	} else {
		echo $inner;
	}
}
