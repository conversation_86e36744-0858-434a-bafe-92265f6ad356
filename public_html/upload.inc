<?php

require_once '_flockmod.inc';
require_once '_uploadimage.inc';

set_memory_limit(GIGABYTE);
function perform_commit(): ?bool {
	switch ($_REQUEST['ACTION']) {
	case 'receive':
		return uploadimage_perform_receive();
	}
	return null;
}

function display_body(): void {
	switch ($action = $_REQUEST['ACTION']) {
	case null:
		not_found();
		return;

	default:
			(require_once '_uploadimage.inc')
		&&	is_uploadimage_type($action)
		&&	($id = have_idnumber($_REQUEST, 'subID'))
		?	uploadimage_show_overview(
				$action,
				$id,
				$_REQUEST['SUBACTION'] && is_uploadimage_type($_REQUEST['SUBACTION']) ? $_REQUEST['SUBACTION'] : '',
				$_REQUEST['ssID'] ?: 0
			)
		:	not_found();
		return;

	case 'form':
		uploadimage_display_form();
		return;

	case 'search':
		uploadimage_display_search_result();
		return;

	case 'single':
		uploadimage_display_single();
		break;
	}
}

function uploadimage_display_single(): void {
	if (!($upimgid = $_REQUEST['sID'])) {
		not_found();
		return;
	}
	if (!($uploadimage = db_single_assoc('uploadimage', '
		SELECT *
		FROM uploadimage
		WHERE UPIMGID = '.$upimgid
	))) {
		$uploadimage === false ? server_failure() : not_found();
		return;
	}

	if (!require_may_change_uploadimage($uploadimage['TYPE'], $uploadimage['ID'])) {
		no_permission();
		return;
	}

	print_rr($uploadimage, 'uploadimage', force: true);

	if ($links = db_rowuse_array('uploadimage_link_log', '
		SELECT ELEMENT, ID, CUSERID, CSTAMP, DUSERID, DSTAMP
		FROM uploadimage_link_log
		WHERE UPIMGID = '.$upimgid
	)) {
		print_rr($links, 'links', force: true);
	}
}

function uploadimage_show_overview(
	string	$type,
	int		$id,
	string	$parentelement = '',
	int		$parentid = 0,
): void {
	$element = is_uploadimage_type($type);

	if (!($url_title = get_urltitle($element, $id))) {
		return;
	}
	
	print_rr($url_title, 'url_title');

	layout_show_section_header(get_element_link($element, $id));

	$maychange = may_change_uploadimage($element, $id);

	if (!($history = db_rowuse_array(['uploadimage_link_log', 'uploadimage', 'uploadimagecp', 'uploadimagemeta'], '
		SELECT ull.UPIMGID, ull.CSTAMP, FILETYPE, DSTAMP, WIDTH, HEIGHT, ACTIVE, COPYRIGHT, NO_PERSON, TYPE, ID,
			(	SELECT CONCAT(WIDTH, "x", HEIGHT)
				FROM uploadimagemeta AS meta2
				WHERE meta2.UPIMGID = ull.UPIMGID
				  AND SIZE = "regular@2x"
			) AS HAVE2x,
			(	SELECT CONCAT(WIDTH," x ",HEIGHT)
				FROM uploadimagemeta AS orig
				WHERE orig.UPIMGID = uploadimagemeta.UPIMGID
				ORDER BY WIDTH * HEIGHT DESC
				LIMIT 1
			) AS ORIG_SIZE
		FROM uploadimage_link_log AS ull
		LEFT JOIN uploadimage USING (UPIMGID)
		LEFT JOIN uploadimagecp USING (UPIMGID)
		LEFT JOIN uploadimagemeta ON uploadimagemeta.UPIMGID = ull.UPIMGID AND SIZE="regular"
		WHERE TYPE = "'.$element.'"
		  AND ID = '.$id.'
		  AND PARENTELEMENT = "'.$parentelement.'"
		  AND PARENTID = '.$parentid
	))) {
		return;
	}
	include_js('js/form/upload');

	number_reverse_sort($history, 'CSTAMP');

	foreach ($history as $image) {
		extract($image);

		if (!$maychange && !$ACTIVE) {
			continue;
		}

		if (is_high_res() && $HAVE2x) {
			[$WIDTHx2, $HEIGHTx2] = explode('x', $HAVE2x);
		} else {
			$WIDTHx2  = $WIDTH;
			$HEIGHTx2 = $HEIGHT;
		}

		?><div class="<?
		if (!$ACTIVE) {
			?>light <?
		}
		?>ib globox hlbox relative vtop"<?
		?> style="margin:1em"<?
		?> data-element="<?= $element ?>"<?
		?> data-id="<?= $id ?>"<?
		?> data-upimgid="<?= $UPIMGID ?>"><?

		?><div class="<?
		if ($ACTIVE) {
			?>hidden <?
		}
		?>abs z2" style="left:.2em;bottom:.2em" onclick="Pf_activateUploadLink(this,true)"><?=
			get_special_char('add')
		?></div><?

		?><div class="<?
		if (!$ACTIVE) {
			?>hidden <?
		}
		?>abs z2" style="left:.2em;bottom:.2em" onclick="Pf_activateUploadLink(this,false)"><?=
			get_close_char()
		?></div><?

		?><div style="padding:.5em"><?
		?><div class="bannerbg bold" style="padding:.4em"><?
			?><table class="fw nomargin"><tr><?
			?><td><? _datetime_display($CSTAMP) ?></td><?
			?><td class="center">&rarr;</td><?
			?><td class="right"><?
			if ($DSTAMP) {
				_datetime_display($DSTAMP);
			} else {
				echo __('date:now');
			}
			?></td><?
			?></tr></table><?
		?></div><?
		?><div class="bannerbg light"><?
		?><div class="small center"><?= get_duration(($DSTAMP ?: CURRENTSTAMP) - $CSTAMP) ?></div><?
		?></div><?
		?><div class="center"><?
		?><div class="ib relative"><?
		if ($COPYRIGHT) {
			show_copyright($COPYRIGHT);
		}
		if (have_admin()) {
			show_orig_dims($image);
		}
		?><img<?
		show_uploadimage_alt($image);
		?> class="middle"<?
		?> width="<?=  $WIDTH ?>"<?
		?> height="<?= $HEIGHT ?>"<?
		?> src="<?=
			IMAGES_HOST ?>/images/<?= $element ?>/<?= $id
			?>_<?= $WIDTHx2 ?>x<?= $HEIGHTx2
			?>_<?= $UPIMGID ?>/<?= $url_title
			?>.<?= $FILETYPE ?>" /><?
		?></div><?
		?></div><?
		?></div><?
		?></div><?
	}
}

function uploadimage_display_form(): void {
	if (!require_uploadimage_type($_REQUEST,'SUBACTION')
	||	!($id = require_idnumber($_REQUEST,'ssID'))
	||	!require_may_change_uploadimage($type = $_REQUEST['SUBACTION'], $id)
	) {
		return;
	}
	$element = is_uploadimage_type($type);

	layout_open_section_header();
	echo	get_element_link($element, $id),
			' ', MIDDLE_DOT_ENTITY, ' ', Eelement_name('upload'), ' ', element_name('form');
	layout_close_section_header();

	show_uploadimage_form($type, $element, $id);
}

function uploadimage_perform_receive(): bool {
	$ok = uploadimage_actual_receive();

	if (($element = have_uploadimage_type($_POST, 'TYPE'))
	&&	($id	  = have_idnumber	 ($_POST, 'ID'))
	) {
		$element = $element === 'eventmap' ? 'party' : is_uploadimage_type($element);
		if ($ok) {
			register_notice('uploadimage:notice:connected_to_LINE', DO_UBB, ['ELEMENT' => $element, 'ID' => $id]);
		}
		if (false !== ($msgid = store_messages_in_cookie())) {
			#error_log_r(get_dirty_cache(), 'uploadimage_perform_receive');
			see_other(get_element_href($element, $id));
		}
		if ($ok) {
			uploadimage_show_connect($element, $id);
		}
	}
	return $ok;
}
