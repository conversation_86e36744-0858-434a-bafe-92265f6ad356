<?php

declare(strict_types=1);

require_once '_videotype.inc';

function display_body(): void {
	switch ($_REQUEST['ACTION']) {
	default:
		not_found();
		return;

	case null:
	case 'party':
	case 'artist':
	case 'location':
#	case 'organization':
		trends_display_populars($_REQUEST['ACTION']);
		return;
	}
}

function trends_display_populars(?string $element = null): void {

	require_once '_profile.inc';

	layout_section_header(Eelement_plural_name('daily_trend'));

	layout_open_box('mspace white');
	?><ul><?
	foreach (['party','artist'/*,'organization','location'*/] as $tmp_element) {
		?><li<?
		if ($element == $tmp_element) {
			?> class="bold"<?
		}
		?>><a href="/trends/<?= $tmp_element ?>">trending <?= element_plural_name($tmp_element) ?></a></li><?
	}
	?></ul><?
	layout_close_box();

	if (!$element) {
		return;
	}

	$user_profile = current_profile();

	$global_keep = null;
	$global_hide = null;

	$period = get_cmp_period($element);

	$days_offset = get_days_offset();

	$and_not_erotic = isset($user_profile['erotic']) ? null : ' AND NOT party.EROTIC ';

	switch ($element) {
	case 'party':
		if (!$user_profile) {
			$global_keep = memcached_same_hash('party',$q = '
				SELECT PARTYID
				FROM party
				'.join_only_events_for_current_country().'
				WHERE ACCEPTED
				  AND CANCELLED = 0
				  AND MOVEDID = 0
				  AND EROTIC = 0
				  AND STAMP>'.(TODAYSTAMP - ($period + $days_offset) * ONE_DAY).'
				ORDER BY PARTYID'
			);
			break;
		}
		$period *= 2;

		if (false === ($partyids = memcached_same_hash('party','
			SELECT DISTINCT PARTYID	/* for user: '.CURRENTUSERID.', identity: '.CURRENTIDENTID.', ip: '.CURRENTIPSTR.' */
			FROM party
			'.join_only_events_for_current_country().'
			WHERE ACCEPTED
			  AND CANCELLED = 0
			  AND MOVEDID = 0
			  AND STAMP>'.(TODAYSTAMP - ($period + $days_offset) * ONE_DAY).
			 $and_not_erotic.'
			ORDER BY PARTYID'
		))) {
			return;
		}

		# select only events that are within country of domain, extended with positions in profile

		$geo_selectors = [];

		if (!empty($user_profile['positions'])) {
			foreach ($user_profile['positions'] as $pos) {
				list($lat,$lon) = $pos;
				$geo_selectors[] = distance(
					'COALESCE(boarding.LATITUDE,location.LATITUDE,city.LATITUDE)',
					'COALESCE(boarding.LONGITUDE,location.LONGITUDE,city.LONGITUDE)',
					$lat,
					$lon
				).' < 10';
			}
		}

		if ($geo_selectors) {
			$extra_partyids = memcached_same_hash(['party','location','boarding','city'],'
				SELECT PARTYID
				FROM party
				LEFT JOIN location USING (LOCATIONID)
				LEFT JOIN boarding USING (BOARDINGID)
				JOIN city ON city.CITYID=COALESCE(boarding.CITYID,location.CITYID,party.CITYID)
				WHERE party.ACCEPTED
				  AND CANCELLED = 0
				  AND MOVEDID = 0
				  AND STAMP>'.(TODAYSTAMP - ($period + $days_offset) * ONE_DAY).'
				  AND ('.implode(' OR ',$geo_selectors).')'.
				 $and_not_erotic.'
				ORDER BY PARTYID',
				ONE_DAY
			);

			if ($extra_partyids === false) {
				return;
			}
			if ($extra_partyids) {
				$partyids += $extra_partyids;
			}
		}
		$global_keep = $partyids;
		break;

	case 'location':
	case 'organization':
		if ($and_not_erotic) {
			$global_hide = db_boolean_hash($element, "
				SELECT {$element}ID
				FROM $element
				WHERE EROTIC = 1"
			);
		}
		break;
	}

	$master_genres = db_simple_hash('master_genre','SELECT MGID,NAME FROM master_genre');
#	$master_genres[null] = null;

	$top_block = [];

	foreach ($master_genres as $mgid => $master_genre) {
		[$itemviews_period1, $itemviews_period2] = get_item_views($element);
		if (!$itemviews_period1) {
			continue;
		}
		if ($mgid) {
			switch ($element) {
			case 'party':
				# now filter on genres
				$partyids = $itemviews_period1 + $itemviews_period2;
				$keep = memcached_same_hash('event_master_genre', '
					SELECT DISTINCT PARTYID
					FROM event_master_genre
					WHERE PARTYID IN ('.implodekeys(', ', $partyids).")
					  AND MGID = $mgid"
				);
				break;

			case 'artist':
				$gids = db_same_hash('genre_to_master','SELECT MGID,GID FROM genre_to_master');
				$keep = memcached_boolean_hash('artist_genre','
					SELECT DISTINCT ARTISTID
					FROM artist_genre
					WHERE GID IN ('.implode(', ', $gids[$mgid]).')'
				);
				break;

			case 'organization':
				$orgids = $itemviews_period1 + $itemviews_period2;
				$keep = db_boolean_hash(['party', 'connect', 'event_master_genre'], "
					SELECT DISTINCT ASSOCID
					FROM party
					JOIN connect
					JOIN event_master_genre USING (PARTYID)
					WHERE MAINTYPE = 'party'
					  AND MAINID = PARTYID
					  AND ASSOCTYPE = 'organization'
					  AND ASSOCID IN (".implodekeys(',',$orgids).")
					  AND MGID = $mgid"
				);
				break;

			case 'location':
				$location_ids = $itemviews_period1 + $itemviews_period2;
				$keep = db_boolean_hash(['party', 'event_master_genre'], "
					SELECT DISTINCT ASSOCID
					FROM party
					JOIN event_master_genre USING (PARTYID)
					WHERE LOCATIONID IN (".implodekeys(', ',$location_ids).")
					  AND MGID = $mgid"
				);
				break;
			}
			if ($keep === false) {
				return;
			}
			if ($keep !== true) {
				$itemviews_period1 = array_intersect_key($itemviews_period1, $keep);
				$itemviews_period2 = array_intersect_key($itemviews_period2, $keep);
			}
		}

		if (!empty($global_keep)) {
			$itemviews_period1 = array_intersect_key($itemviews_period1, $global_keep);
			$itemviews_period2 = array_intersect_key($itemviews_period2, $global_keep);
		}
		if (!empty($global_hide)) {
			$itemviews_period1 = array_diff_key($itemviews_period1, $global_hide);
			$itemviews_period2 = array_diff_key($itemviews_period2, $global_hide);
		}
		$top_block[$element][$mgid] = get_item_top($element, $itemviews_period1, $itemviews_period2);
	}

	if (empty($top_block)) {
		return;
	}

	# first order on determined master genre of visitor
	# then order on amount of total views

	foreach ($top_block as $element => $mgids) {

		uksort($mgids, function($a, $b) use ($element, $user_profile, $top_block, $master_genres) {
			$a_all = $a ? 0 : 1;
			$b_all = $b ? 0 : 1;
			if ($diff = $b_all - $a_all) {
				return $diff;
			}
			if (!empty($user_profile['genres'])) {
				$have_a = isset($user_profile['genres'][$a]) ? 0 : 1;
				$have_b = isset($user_profile['genres'][$b]) ? 0 : 1;
				if ($diff = $have_a - $have_b) {
					return $diff;
				}
			}
			$a_views = $top_block[$element][$a][0];
			$b_views = $top_block[$element][$b][0];
			if ($diff = $b_views - $a_views) {
				return $diff;
			}
			return strcmp($master_genres[$a], $master_genres[$b]);
		});

		$top_block[$element] = $mgids;
	}

	foreach ($top_block as $element => $mgids) {
		foreach ($mgids as $mgid => [$views, $data]) {
			?><div class="ib simple-box"><?
			?><div class="bold header"><?
			echo __('trends:header:'.$element,['GENRE' => $master_genres[$mgid]]);
			?></div><?
			echo $data;
			?></div><?
		}
	}
}

function get_item_views(string $element): array {
	$days_offset = get_days_offset();
	$period = get_cmp_period($element);

	switch ($element) {
	case 'party':
		$itemviews_period1 = memcached_simple_hash('party_view','
			SELECT PARTYID,SUM(HITS) AS TOTAL
			FROM party_view
			WHERE DAYNUM BETWEEN '.(CURRENTDAYNUM - $period - 1 - $days_offset).' AND '.(CURRENTDAYNUM - 1 - $days_offset).'
			'.and_where_only_current_domain().'
			GROUP BY PARTYID
			ORDER BY TOTAL DESC',
			ONE_DAY
		);
		$itemviews_period2 = memcached_simple_hash('party_view','
			SELECT PARTYID,SUM(HITS) AS TOTAL
			FROM party_view
			WHERE DAYNUM BETWEEN '.(CURRENTDAYNUM - 2 * $period - 1 - $days_offset).' AND '.(CURRENTDAYNUM - $period - 2 - $days_offset).'
			'.and_where_only_current_domain().'
			GROUP BY PARTYID
			ORDER BY TOTAL DESC',
			ONE_DAY
		);

		break;

	case 'artist':
		$itemviews_period1 = memcached_simple_hash('artist_counter','
			SELECT ARTISTID,SUM(VIEWS) AS TOTAL
			FROM artist_counter
			WHERE DAYNUM BETWEEN '.(CURRENTDAYNUM - $period - 1 - $days_offset).' AND '.(CURRENTDAYNUM - 1 - $days_offset).'
			  AND DOMAIN="'.CURRENTDOMAIN.'"
			GROUP BY ARTISTID
			ORDER BY TOTAL DESC',
			ONE_DAY
		);
		$itemviews_period2 = memcached_simple_hash('artist_counter','
			SELECT ARTISTID,SUM(VIEWS) AS TOTAL
			FROM artist_counter
			WHERE DAYNUM BETWEEN '.(CURRENTDAYNUM - 2 * $period - 1 - $days_offset).' AND '.(CURRENTDAYNUM - $period - 2 - $days_offset).'
			  AND DOMAIN="'.CURRENTDOMAIN.'"
			GROUP BY ARTISTID
			ORDER BY TOTAL DESC',
			ONE_DAY
		);
		break;

	case 'location':
	case 'organization':
		$period = 7;
		$itemviews_period1 = memcached_simple_hash($element.'_counter','
			SELECT '.$element.'ID,SUM(VIEWS) AS TOTAL
			FROM '.$element.'_counter
			WHERE DAYNUM BETWEEN '.(CURRENTDAYNUM - $period - 1 - $days_offset).' AND '.(CURRENTDAYNUM - 1 - $days_offset).'
			  AND DOMAIN="'.CURRENTDOMAIN.'"
			GROUP BY '.$element.'ID
			ORDER BY TOTAL DESC',
			ONE_DAY
		);
		$itemviews_period2 = memcached_simple_hash($element.'_counter','
			SELECT '.$element.'ID,SUM(VIEWS) AS TOTAL
			FROM '.$element.'_counter
			WHERE DAYNUM BETWEEN '.(CURRENTDAYNUM - 2 * $period - 1 - $days_offset).' AND '.(CURRENTDAYNUM - $period - 2 - $days_offset).'
			  AND DOMAIN="'.CURRENTDOMAIN.'"
			GROUP BY '.$element.'ID
			ORDER BY TOTAL DESC',
			ONE_DAY
		);
		break;
	default:
		return [null, null];
	}
	return [$itemviews_period1, $itemviews_period2];
}

function get_item_top(string $element, array $itemviews_period1, array $itemviews_period2): array {
/*	$left = 40;
	?><div class="l"><?
	?><ol><?
	foreach ($itemviews_period1 as $id => $views) {
		?><li value="<?= $views ?>"><?= get_element_link($element,$id) ?></li><?
		if (!--$left) {
			break;
		}
	}
	?></ol><?
	?></div><?

	$left = 40;
	?><div class="l"><?
	?><ol><?
	foreach ($itemviews_period2 as $id => $views) {
		?><li value="<?= $views ?>"><?= get_element_link($element,$id) ?></li><?
		if (!--$left) {
			break;
		}
	}
	?></ol><?
	?></div><?

	$past = [];

	# remove views from period1 where event is done
	foreach ($itemviews_period1 as $id => $views) {
		if ($element == 'party') {
			$party = memcached_party_and_stamp($id);
			if ($party['STAMP'] < CURRENTSTAMP) {
				$past[$id] = true;
		#		unset($itemviews_period1[$id]);
			}
		}
	}*/

	$period1 = array_flip(array_keys($itemviews_period1));
	$period2 = array_flip(array_keys($itemviews_period2));

/*	$left = 40;
	?><div class="l"><?
	?><ol><?
	foreach ($period1 as $id => $views) {
		?><li value="<?= $views ?>"><?= get_element_link($element,$id) ?></li><?
		if (!--$left) {
			break;
		}
	}
	?></ol><?
	?></div><?

	$left = 40;
	?><div class="l"><?
	?><ol><?
	foreach ($period2 as $id => $views) {
		?><li value="<?= $views ?>"><?= get_element_link($element,$id) ?></li><?
		if (!--$left) {
			break;
		}
	}
	?></ol><?
	?></div><?*/

	$moves = [];

	$total_views = 0;

	foreach ($itemviews_period1 as $id => $views) {
		$pos1 = $period1[$id];
		$pos2 = $period2[$id] ?? null;

		$moves[$id] = $pos2 === null ? null : $pos2-$pos1;//$pos2.' => '.$pos1;

		$total_views += $views;
	}

	ob_start();

	$left = 20;
	?><table class="mspace fw" style="width:20em"><?
	foreach ($moves as $id => $move) {

		$class = ($move === null ? 'win' : ($move < 0 ? ($move <= -8 ? 'error nb' : 'warning nb') : ($move > 0 ? ($move >= 8 ? 'notice' : 'notice nb') : 'win')));

		?><tr><?
		?><td class="right <?= $class ?>"><?
		if ($move) {
			if ($move < 0) {
				echo -$move;
			} else {
				echo $move;
			}
		}
		?></td><?
		?><td class="center <?= $class ?>"><?
		if (!$move) {
			if ($move ===  null) {
				?>&#9733;<?
			} else {
				?>&#9632;<?
			}
		} elseif ($move > 0) {
			?>&#9650;<?
		} else {
			?>&#9660;<?
		}
		?></td><?
		?><td><?
		echo get_element_link($element,$id);
		if (isset($past[$id])) {
			?> <small class="light">(<?= __('date:past') ?>)</small><?
		}
		?></td><?
		?></tr><?
		if (!--$left) {
			break;
		}
	}
	?></table><?

	return [$total_views, ob_get_clean()];
}

function get_cmp_period(string $element): int {
	static $__period = [
		'party'			=> 7,
		'organization'	=> 7,
		'artist'		=> 7,
		'location'		=> 7,
	];
	return $__period[$element];
}

function get_days_offset(): int {
	return SERVER_SANDBOX ? 3 : 0;
}
