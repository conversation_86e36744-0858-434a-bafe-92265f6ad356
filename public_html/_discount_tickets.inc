<?php

declare(strict_types=1);

/* # NOTE: This file is not used anymore.

function show_discount_ticket_info(int|array $partyid, bool $ad): void {
	if (!($party = is_scalar($partyid) ? memcached_party_and_stamp($partyid) : $partyid)
	||	!empty($party['PRESALE_SOLD_OUT'])
	||	CURRENTSTAMP > $party['STAMP'] + ($party['DURATION_SECS'] ?: (6 * ONE_HOUR))
	) {
		return;
	}
	switch ($partyid = $party['PARTYID']) {	// NOSONAR
	case 351382:
	case 374699:
		require_once '_presale.inc';
		$presaleinfo = get_presale_info('party',$partyid);
		$locals = prepare_presale_info($presaleinfo, $party);
		# @noinspection PhpRedundantOptionalArgumentInspection
		extract($locals, \EXTR_OVERWRITE);

		if (preg_match('!href="([^"]*)".*?onclick="([^"]*)"!',$main_link,$match)) {
			[, $href, $onclick] = $match;
			?><div class="win vtop<?= $ad ? ' ib' : '' ?>" style="padding: .5em;"><?
			?><a onclick="<?= $onclick ?>" href="<?= $href ?>" target="_blank"><?
			?>Koop je tickets met 5 euro korting via Partyflock! <?
			?></a><?
			?></div><?
		}
		break;

	case 344810:
		require_once '_presale.inc';

		# show_actual_ticket_frame(true);

		?><div class="win vtop<?= $ad ? ' ib' : '' ?>" style="padding:.5em"><?
		?><a rel="nofollow"<?
		?> href="/order_ticket/paylogic/<?= $partyid ?>"<?
		?> target="_blank"<?
		?> onclick="<?
			?>Pf.trackLink(this.href, 'presale');<?
			?>return show_ticket_frame(event, 600, 700,'paylogic, 344810, false,'')<?
		?>"><?
		?>Koop je tickets met 25 euro korting via Partyflock met deze code: <span class="orange">IR2018XO3991</span><?
		?></a><?
		?></div><?
		break;
	}
}*/
