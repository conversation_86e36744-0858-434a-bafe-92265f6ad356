<?php

# winners: [userid => [contestid => contestid]]

function blacklist_contest_winners(int $contestid, array $userids): int|bool {
	foreach ($userids as $userid) {
		$setlist[] =
			'('.$contestid.
			','.CURRENTSTAMP.
			','.$userid.
			','.(CURRENTSTAMP + 3 * ONE_MONTH).
			')';
	}
	return	db_insert('contestblacklist','
		INSERT INTO contestblacklist (CONTESTID, STAMP, USERID, STOPSTAMP)
		VALUES '.implode(', ', $setlist))
	?	db_affected()
	:	false;
}
