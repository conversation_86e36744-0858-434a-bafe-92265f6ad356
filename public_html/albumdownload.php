<?php

define('CURRENTSTAMP',time());

require_once '_exit_if_offline.inc';
require_once '_db.inc';
require_once '_error.inc';
require_once '_require.inc';
require_once '_modified.inc';

require_once '_currentuser.inc';
require_once '_execute.inc';
require_once '_urltitle.inc';

function bail(int $errno): never {
	http_response_code($errno);
	exit;
}

header('Cache-Control: private');

if (!isset($_SERVER['eALBUMID'])
||	!isset($_SERVER['eMAPID'])
) {
	bail(400);
}

$albumid = (int)$_SERVER['eALBUMID'];
$mapid   = (int)$_SERVER['eMAPID'];

$currentuser = new _currentuser(TINY_USER | SKIP_SETTINGS);

if (!have_admin('album')
&&	CURRENTUSERID !== $albumid
) {
	bail(403);
}

$mstamp = db_single(['albummap','albumelement'],'
	SELECT GREATEST(
		(SELECT MAX(MSTAMP) AS MSTAMP FROM albumelement WHERE ALBUMID = '.$albumid.'),
		(SELECT MAX(MSTAMP) AS MSTAMP FROM albummap WHERE ALBUMID = '.$albumid.')
	)'
);
if ($mstamp && not_modified($mstamp)) {
	bail(304);
}

$maps = db_simple_hash(['albummap', 'party'],'
	SELECT SQL_NO_CACHE UPMAPID, MAPID, TITLE, STAMP_TZI
	FROM albummap
	LEFT JOIN party USING (PARTYID)
	WHERE ALBUMID='.$albumid,
	DB_USE_MASTER
);
if (!$maps) {
	bail($maps === false ? 503 : 404);
}
$map = db_simple_hash(['albummap', 'party'],'
	SELECT SQL_NO_CACHE TITLE, STAMP_TZI
	FROM albummap
	LEFT JOIN party USING (PARTYID)
	WHERE ALBUMID = '.$albumid.'
	  AND MAPID = '.$mapid,
	DB_USE_MASTER
);
if (!$map) {
	bail($map === false ? 503 : 404);
}
list($basename, $stamp) = keyval($map);

header('Content-Type: application/zip');

$basename = myurlencode($basename, true);
if ($stamp) {
	change_timezone('UTC');
	list($y,$m,$d) = _getdate($stamp);
	change_timezone();
	$basename = sprintf('%4d%02d%02d',$y,$m,$d).'_'.$basename;
} elseif (!$basename) {
	$basename = 'album_'.$albumid;
}

header('Content-Disposition: attachment; filename="'.$basename.'.zip"');

$tmpdir = '/tmpdisk/'.uniqid();
$tmpbase = $tmpdir.'/'.$basename;
$zipfile = $tmpbase.'.zip';
safe_mkdir($tmpbase, 0777, true);
create_map($albumid, $mapid, $maps, $tmpbase);
chdir($tmpdir);
execute('zip -0 -r -m '.$zipfile.' '.$basename);

header('Content-Length: '.filesize($zipfile));

register_shutdown_function('unlink',$zipfile);
register_shutdown_function('rmdir',$tmpdir);

ob_end_clean();

readfile($zipfile);

function create_map(int $albumid, int $mapid, array $maps, string $storedir): void {
	store_images($albumid, $mapid, $storedir);
	if (!isset($maps[$mapid])) {
		return;
	}
	foreach ($maps[$mapid] as $innermapid => $info) {
		list($title,$stamp) = keyval($info);
		$basename = myurlencode($title,true);
		if ($stamp) {
			change_timezone('UTC');
			list($y, $m, $d) = _getdate($stamp);
			change_timezone();
			$basename = sprintf('%4d%02d%02d', $y, $m, $d).'_'.$basename;
		} elseif (!$basename) {
			$basename = 'map_'.$innermapid;
		}
		$cnt = 0;
		while (isset($done[$dirname = $basename.($cnt ? '.'.$cnt : null)])) {
			++$cnt;
		}
		$done[$dirname] = true;
		if (!safe_mkdir($newdir = $storedir.'/'.$dirname)) {
			error_log('mkdir of '.$newdir.' failed');
			continue;
		}
		create_map($albumid, $innermapid, $maps, $newdir);
	}
}

function store_images(int $albumid, int $mapid, string $storedir): void {
	if (!($images = db_rowuse_array(['albumelement', 'albumimagedatameta'], "
		SELECT	SQL_NO_CACHE ALBUMELEMENTID, TITLE, ORIGNAME, FILETYPE, STOREID,
				IF(ORIGID, ORIGID, IF(HUGEID, HUGEID, DATAID)) AS DATAID
		FROM albumelement
		JOIN albumimagedatameta USING (DATAID)
		WHERE ALBUMID= $albumid
		  AND MAPID = $mapid",
		DB_USE_MASTER))
	) {
		return;
	}
	foreach ($images as $info) {
		extract($info);
		if (!($data = dbdata_single('albums', $STOREID, "
			SELECT SQL_NO_CACHE DATA
			FROM albums.u$STOREID
			WHERE DATAID = $DATAID",
			DB_USE_MASTER))
		) {
			mail_log("no data found for DATAID $DATAID and STOREID $STOREID");
			error_log('no data',0);
			continue;
		}
		if ($TITLE) {
			$basename = myurlencode($TITLE, true);
		} elseif ($ORIGNAME) {
			$basename = myurlencode(preg_replace('"\.(?:avif|jpe?g|gif|bmp|png|tiff?|webp)$"i','',$ORIGNAME), true);
		} else {
			$basename = 'albumelement_'.$ALBUMELEMENTID;
		}
		$cnt = 0;
		while (isset($done[$filename = $basename.($cnt ? '.'.$cnt : null)])) {
			++$cnt;
		}
		$done[$filename] = true;
		file_put_contents($storedir.'/'.$filename.'.'.$FILETYPE,$data);
	}
}
