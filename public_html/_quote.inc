<?php

function show_quote_link($allow_noselect = false) {
	require_once '_helper.inc';
	include_js('js/quote');
	?><span<?
	?> class="cite nb<?
	if (!$allow_noselect) {
		?> light<?
	}
	?>"<?
	if ($allow_noselect) {
		?> data-range="<?= $allow_noselect === true ? 'all' : $allow_noselect ?>"<?
	}
	?> onmouseup="Pf.stopCite(event)"<?
	?> ontouchstart="Pf.startCite(this,event)"<?
	?> onclick="return quoteElement(this)"><?= __('action:quote') ?></span><?
}

function get_quoteseen(int $userid): int {
	return db_single('quoteseen','SELECT STAMP FROM quoteseen WHERE USERID='.$userid)  ?: (
		have_admin()
	?	   1352904923
	:	   1353080098
	);
}

function get_new_quotes($userid) {
	static $__quotei = 0;
	if ($__quotei !== 0) {
		return $__quotei;
	}
	$quoteseen = get_quoteseen($userid);
	$quotecnt = memcached_get($new_quotes_cnt_key = 'newquotes:cnt:'.$userid);
	if ($quotecnt) {
		return $__quotei = [$quotecnt,$quoteseen];
	}
	$quotes = db_multirow_hash(['quoted','quoteseen_element'],get_quote_query($userid,$quoteseen));
	if (!$quotes) {
		return $__quotei = [0,$quoteseen];
	}
	$quotecnt = 0;
	foreach ($quotes as $element => $ids) {
		$prefix = null;
		switch ($element) {
		case 'flockmessage':
			$prefix = 'flock';
		case 'message':
			$quotecnt += db_single([$prefix.'message',$prefix.'topic'],'
				SELECT COUNT(*)
				FROM '.$prefix.'message
				JOIN '.$prefix.'topic USING (TOPICID)
				WHERE STATUS!="hidden"
				  AND ACCEPTED=1
				  AND MESSAGEID IN ('.implode(',',$ids).')'
			) ?: 0;
			break;
		default:
			require_once '_comment.inc';
			require_once '_commentsinfo.inc';
			require_once '_comment.inc';
			$parent = is_comment_table($element);

			[$idname, $table] = get_parent_idname_and_table($parent);

			if (comment_has_accept($parent)) {
				switch ($table) {
				case 'image':
					$checkp = 'p.HIDDEN=0';
					break;
				case 'poll':
					require_once '_pollstatus.inc';
					$checkp = 'p.STATUS='.POLLSTATUS_ACCEPTED;
					break;
				default:
					$checkp = 'p.ACCEPTED=1';
					break;
				}
			} else {
				$checkp = '1';
			}

			$okids = db_multirow_hash([$table,$element],'
				SELECT c.ID,COMMENTID
				FROM '.$element.' AS c
				JOIN `'.$table.'` AS p ON p.'.$idname.'=c.ID
				WHERE '.$checkp.'
				  AND c.ACCEPTED=1
				  AND COMMENTID IN ('.implode(',',$ids).')'
			);
			if (!$okids) {
				break;
			}
			if ($infos = get_commentsinfos($parent, $okids)) {
				foreach ($infos as $parentid => $info) {
					if (!$info[CMTI_HIDEALL]) {
						$quotecnt += count($okids[$parentid]);
					}
				}
			}
			break;
		}
	}
	memcached_set($new_quotes_cnt_key, $quotecnt, TEN_MINUTES);

	return $__quotei = [$quotecnt, $quoteseen];
}
function mark_new_quote_seen($userid,$stamp,$element,$id,$msgno = null) {
	if ($msgno) {
		if (($parent
			=	($cmt = is_comment_table($element))
			?:	(	$element === 'message'
				?	'topic'
				:	(	$element === 'flockmessage'
					?	'flocktopic'
					:	null
					)
				)
			)
		&&	($substat = db_single_array(['subscription',$element],'
				SELECT LASTIDSEEN,'.(/* $parentidname = */ $cmt ? 'ID' : 'TOPICID').' AS PARENTID
				FROM subscription
				JOIN '.$element.($cmt ? ' USING (ID)' : ' ON TOPICID=ID').'
				WHERE ELEMENT="'.$parent.'"
				  AND '.($commentidname = $cmt ? 'COMMENTID' : 'MESSAGEID').'='.$id.'
				  AND subscription.USERID='.$userid)
			)
		) {
			[$lastidseen, $parentid] = $substat;

			$have_notseen = db_single([$element,'quoteseen_element'], "
				SELECT 1
				FROM $element AS comment
				LEFT JOIN quoteseen_element AS qse ON
							qse.ELEMENT = '$element'
						AND qse.ID = $commentidname
						AND qse.USERID = $userid
				WHERE $commentidname > $lastidseen
				  AND $commentidname != $id
				  AND ".($cmt ? 'comment.ID' : 'TOPICID')." = $parentid
				  AND ISNULL(qse.ID)
				  AND ACCEPTED
				LIMIT 1"
			);
			if ($have_notseen !== false
			&&	!$have_notseen
			) {
				require_once '_subscriptions.inc';
				view_subscription($parent,$parentid,$id,$msgno,$userid);
				header('X-SUBREAD: 1');
			}
		}
	}
	if (!db_insert('quoteseen_element','
		INSERT IGNORE INTO quoteseen_element SET
			USERID	='.$userid.',
			STAMP	='.$stamp.',
			ELEMENT	="'.addslashes($element).'",
			ID	='.$id)
	) {
		return false;
	}
	$quoteseen = get_quoteseen($userid);

	$minstamp = db_single(['quoted','quoteseen_element'],'
		SELECT MIN(quoted.STAMP)
		FROM quoted
		LEFT JOIN quoteseen_element ON
				quoteseen_element.USERID='.$userid.'
			AND quoteseen_element.ELEMENT=SRCELEM
			AND quoteseen_element.ID=SRCID
		WHERE ISNULL(quoteseen_element.USERID)
		  AND QOTUSERID='.$userid.'
		  AND quoted.STAMP>'.$quoteseen
	);
	if ($minstamp) {
		mark_new_quotes_seen($userid,$minstamp-1);
	} else {
		flush_new_quotes($userid);
	}
	return true;
}
function mark_new_quotes_seen($userid,$stamp) {
	if (!db_replace('quoteseen','
		INSERT INTO quoteseen SET
			USERID	='.$userid.',
			STAMP	='.$stamp.'
		ON DUPLICATE KEY
			UPDATE STAMP=GREATEST(STAMP,VALUES(STAMP))')
	) {
		return false;
	}
	flush_new_quotes($userid);
	return true;
}
function mark_all_quotes_before_seen(
	int		$userid,
	string	$element,
	int		$id,
	int		$lastid,
	int		$lastno,
): void {
	$quoteseen = get_quoteseen($userid);
	if (!($quotecnt = memcached_get('newquotes:cnt:'.$userid))
	&&	 $quotecnt !== false
	&&	 $quotecnt !== null
	||	!($quotes = db_multirow_hash(['quoted', 'quoteseen_element'], get_quote_query(
		$userid,
		$quoteseen,
		[get_comment_table($element), $lastid])))
	) {
		return;
	}
	$insert_list = [];
	foreach ($quotes as $comment_table => $ids) {
		sort($ids);
		/* $lastid = */ array_pop($ids);
		foreach ($ids as $local_id) {
			$insert_list[] = '('.CURRENTUSERID.', '.CURRENTSTAMP.", '$comment_table', $local_id)";
		}
	}
	if ($insert_list
	&&	!db_insert('quoteseen_element', '
		INSERT INTO quoteseen_element (USERID, STAMP, ELEMENT, ID)
		VALUES '.implode(', ', $insert_list))
	) {
		return;
	}
	mark_new_quote_seen(CURRENTUSERID,CURRENTSTAMP, $comment_table, $lastid);
}
function flush_new_quotes($arg) {
	foreach (is_scalar($arg) ? [$arg] : (isset($arg[0]) ? $arg : array_keys($arg)) as $userid) {
#		error_log('flush newquotes:cnt:'.$userid.' @ '.$_SERVER['REQUEST_URI'],0);
		memcached_delete('newquotes:cnt:'.$userid);
		memcached_set('newquotes:reload:'.$userid,CURRENTSTAMP,2*DEFAULT_EXPIRATION);
	}
}
function flush_new_quotes_element(
	string	$parent_element,
	int		$parent_id,
	?string	$comment_element	= null,
	?int	$comment_id			= null
) {
	if ($comment_element) {
		$and_where = "
		  AND SRCELEM = '$comment_element'
		  AND SRCID = $comment_id";
	} else {
		$comment_element = get_comment_table($parent_element);
		$and_where       = "
		  AND SRCELEM = '$comment_element'";
	}
	$quote_userids = db_simpler_array('quoted', "
		SELECT DISTINCT QOTUSERID
		FROM quoted
		WHERE PARENTID = $parent_id".
		 $and_where
	);
	if ($quote_userids) {
		flush_new_quotes($quote_userids);
	}
}

function get_quote_query(int $userid, int $quoteseen, ?array $up_to = null): string {
	static $__qstr;
	if (isset($__qstr)) {
		return $__qstr;
	}
	require_once '_ignores.inc';
	$and_not_ignored = '';
	if ($ignores = ignores()) {
		foreach ($ignores as $itype => $userids) {
			switch ($itype) {
			case IGNORE_FORUM_MESSAGES:
				$element_idstr = '"message"';
				break;

			case IGNORE_FLOCK_MESSAGES:
				$element_idstr = '"flockmessage"';
				break;

			case IGNORE_COMMENTS:
				require_once '_comment.inc';
				$commenttables = [];
				foreach (commentables() as $parent => /* $info = */ $ignored) {
					$commenttables[] = $parent.'_comment';
				}
				$element_idstr = stringsimplode(', ', $commenttables);
				break;
			default:
				continue 2;
			}
			$and_not_ignored .= "
				AND NOT (	SRCELEM IN ($element_idstr)
						AND	quoted.USERID IN (".implode(', ', $userids).'))';
		}
	}
	if ($up_to) {
		[$element, $messageid] = $up_to;
		$and_currently_viewed = " AND SRCELEM = '$element' AND SRCID <= $messageid ";
	}
	return "
		SELECT DISTINCT SRCELEM, SRCID
		FROM quoted
		LEFT JOIN quoteseen_element ON
				quoteseen_element.USERID = $userid
			AND quoteseen_element.ELEMENT = SRCELEM
			AND quoteseen_element.ID = SRCID
		WHERE ISNULL(quoteseen_element.USERID)
		  AND QOTUSERID = $userid
		  AND quoted.USERID != $userid
		  AND quoted.STAMP > $quoteseen
		  $and_not_ignored ".
		  ($and_currently_viewed ?? '');
}

function get_comment_table(string $parent_element): string {
	return match($parent_element) {
		'flocktopic'	=> 'flockmessage',
		'topic'			=> 'message',
		default			=> $parent_element.'_comment',
	};
}
