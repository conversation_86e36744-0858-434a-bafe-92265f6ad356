<?php

function get_advertising_parts(): array {
	static $__parts = [	#  section
		'ad'		=> 'banner',
		'agendaspot'	=> false,
		'promo'		=> null,
		'newsad'	=> null,
#		'job'		=> null,
#		'tvad'		=> null,
#		'virtualtour'	=> null,
#		'package'	=> false,
		'logo'		=> false,
		'contest'	=> null,
		'gallery'	=> null,
	];
	return $__parts;
}
function get_sub_headers(?string $sub = null): bool|array {
	static $__subs = [
		'info'		=> true,
#		'prices'	=> true,
		'requirements'	=> true,
		'quotation'	=> true,
	];
	return $sub ? isset($__subs[$sub]) : $__subs;
}
function display_title() {
	echo element_name('information_on_advertising_on_partyflock');
	if (!$_REQUEST['ACTION']) {
		return;
	}
	switch ($_REQUEST['ACTION']) {
	case 'terms':
		?> <?= MIDDLE_DOT_ENTITY ?> <?
		echo __('doc:terms_and_conditions');
		break;
	case 'general':
		?> <?= MIDDLE_DOT_ENTITY ?> <?
		echo __('doc:general');
		break;
	default:
		if (!isset(get_advertising_parts()[$_REQUEST['ACTION']])) {
			break;
		}
	case 'contact':
		?> <?= MIDDLE_DOT_ENTITY ?> <?
		echo element_plural_name($_REQUEST['ACTION']);
		break;
	}
	$sub = $_REQUEST['SUBACTION'] ?? 'info';
	if (get_sub_headers($sub)) {
		?> <?= MIDDLE_DOT_ENTITY ?> <?
		echo __('doc:'.$sub);
	}
}
function display_body() {
	switch ($_REQUEST['ACTION']) {
	case null:	return advertising_display_overview();
	default:	return advertising_display_single();
	}
}

function advertising_menu(): void {
	$email_element = 'b2b';
	layout_open_menu();
	layout_menuitem(Eelement_name('overview'),'/advertising',empty($_REQUEST['ACTION']));
	switch ($_REQUEST['ACTION']) {
	case 'general':
	case 'terms':
	case 'contact':
		foreach ([
			'general'	=> 'info',
			'terms'		=> 'terms_and_conditions',
			'contact'	=> null,
		] as $sub => $keypart) {
			layout_open_menuitem();
			layout_menuitem(__C('doc:'.($keypart ?: $sub)), '/advertising/'.$sub, $sub == $_REQUEST['ACTION']);
			layout_close_menuitem();
		}
		break;

	case 'discount':
		not_found();
		return;
	case 'ad':
	case 'newsad':
	case 'promo';
	case 'job':
	case 'contest':
	case 'gallery':
#	case 'virtualtour':
		$email_element = $_REQUEST['ACTION'];
#	case 'tvad':
#	case 'package':
		$parts = get_advertising_menu($_REQUEST['ACTION'], DO_CAPFIRST);
		foreach ($parts as $sub => $part) {
			layout_open_menuitem();
			if ($active = $_REQUEST['SUBACTION'] == $sub) {
				?><b><?= __C('doc:'.$sub) ?></b><?
				continue;
			}
			echo $part;
			layout_close_menuitem();
		}
	}
	layout_continue_menu();

	require_once '_contactemails.inc';
	layout_open_menuitem();
	print_email_link(contact_get_email($email_element), [__C('action:contact'), 'Partyflock'], PRINT_EMAIL_IMAGE_BEFORE);
	layout_close_menuitem();

	if (have_user()) {
		layout_menuitem('<img class="presence srmrgn" src="'.get_favicon().'" />'.__C('action:contact'),'/ticket/form?ELEMENT='.$email_element);
	}
	layout_close_menu();
}

function get_advertising_menu($element,$flags = 0) {
	if ($element != 'job'
#	&&	$element != 'package'
	) {
		$parts['info'] = '<a href="/advertising/'.$element.'">'.__('doc:info',$flags).'</a>';
	}
	if ($element == 'discount'
	||	$element == 'logo'
	) {
		return $parts;
	}

#	if (have_admin()) {
/*		if ($element != 'contest'
		&&	$element != 'gallery'
		) {
			$parts['prices'] = '<a href="/advertising/'.$element.'/prices">'.__('doc:prices',$flags).'</a>';
		}*/
#	}
#	if ($element == 'package') {
#		$parts['prices'] = '<a href="/advertising/'.$element.'/prices">'.__('doc:prices',$flags).'</a>';
#	}
	if (
#		$element !== 'package'
#	&&	$element !== 'virtualtour'
		$element !== 'agendaspot'
	&&	$element !== 'contest'
	&&	$element !== 'gallery'
	) {
		$parts['requirements'] = '<a href="/advertising/'.$element.'/requirements">'.__('doc:requirements',$flags).'</a>';
	}
	if ($element === 'promo'
	||	$element === 'job'
	) {
		$parts['quotation'] = '<a href="/'.$element.'/requestform">'.__('doc:quotation',$flags).'</a>';
	}
	return $parts;
}
function advertising_display_overview() {
	layout_show_section_header();

	advertising_menu();

	db_insert('infohit','
	INSERT INTO infohit SET
		PASSWD	=b\'0\',
		RELATION=b\''.(have_relation() ? 1 : 0).'\',
		ELEMENT	="overview",
		PART	="",
		IPBIN	="'.addslashes(CURRENTIPBIN).'",
		IDENTID	='.CURRENTIDENTID.',
		USERID	='.CURRENTUSERID.',
		STAMP	='.CURRENTSTAMP
	);

	require_once '_contactemails.inc';

	function show_contact_now($element) {
		?><li><a href="<?=
			have_user()
		?	'/ticket/form?ELEMENT='.$element
		:	_make_illegible('mailto:'.contact_get_email($element,null))
		?>"><?= __('doc:contact_immediately') ?></a></li><?
	}

	layout_open_box('white');
	?><div class="body block"><?= __('doc:no_time_TEXT',DO_UBB | DO_NL2BR,[
		'URL'	=> '/ticket/form?ELEMENT=b2b'
	]) ?></div><?
	layout_close_box();

	layout_open_box(SMALL_SCREEN ? 'white' : 'white third ib vtop');
	layout_box_header(__C('doc:general'));
	?><ul><?
		?><li><a href="/advertising/general"><?= __('doc:info') ?></a></li><?
		?><li><a href="/advertising/terms"><?= __('doc:terms_and_conditions') ?></a></li><?
		?><li><a href="/advertising/contact"><?= __('doc:contact') ?></a></li><?
	?></ul><?
	if (!SMALL_SCREEN) {
		layout_close_box();
	}

	$have_relation = have_relation();

	$i = 1;

	static $__free = [
		'contest'	=> true,
		'gallery'	=> true,
	];

	foreach (get_advertising_parts() as $element => $section) {
		$sectionname = $element == 'gallery' ? Eelement_plural_name('photo') : Eelement_name($section ?: $element,$element != 'discount');

		if (!SMALL_SCREEN) {
			layout_open_box('white third ib vtop');
		}

		layout_box_header(
			($element == 'logo' ? '<img class="presence" src="'.get_favicon().'" /> ' : null).
			(!$have_relation || $section === false ? $sectionname : '<a href="/'.$element.'">'.$sectionname.'</a>').
			(isset($__free[$element]) ? ' <small>(<span class="notice-nb">'.__('cost:free').'</span>)</small>' : null)
		);
		$parts = get_advertising_menu($element);
		?><ul><?
			?><li><?= implode('</li><li>',$parts) ?></li><?
		?></ul><?
		if (!SMALL_SCREEN) {
			layout_close_box();
			if (++$i == 3) {
				$i = 0;
				?><div class="clear"></div><?
			}
		}
	}
	if (SMALL_SCREEN) {
		layout_close_box();
	}
}
function advertising_display_single() {
	layout_show_section_header();

	$args = [];

	switch ($element = $_REQUEST['ACTION']) {
	case 'ad':
	case 'agendaspot':
	case 'promo':
	case 'newsad':
#	case 'job':
#	case 'tvad':
#	case 'virtualtour':
#	case 'package':
	case 'logo':
	case 'contest':
	case 'gallery':
		switch ($_REQUEST['SUBACTION']) {
		case null:
			$_REQUEST['SUBACTION'] = 'info';
#		case 'prices':
/*			if ($_REQUEST['SUBACTION'] == 'prices'
			&&	$_REQUEST['ACTION'] != 'package'
			&&	!have_admin()
			) {
				not_found();
				return;
			}*/
		case 'requirements':
			$key = 'doc:'.$element.':'.$_REQUEST['SUBACTION'];
#			$args['PRICEINFO_URL'] = '/advertising/'.$element.'/prices';
			switch ($element) {
			case 'job':
			case 'promo':
				$args['QUOTE_URL'] = '/'.$element.'/requestform';
				break;
			}
			break;
		default:
			not_found();
			return;
		}
		break;
	case 'general':
	case 'contact':
	case 'terms':
		$key = 'doc:'.$element;
		break;
	default:
		not_found();
		return;
	}

	advertising_menu();

	?><article itemscope itemtype="https://schema.org/Article"><?
	layout_open_box('white');
	?><div class="body block"><?
	$body = __($key.='_TEXT',DO_UBB|DO_NL2BR|DO_CONDITIONAL,$args);
	echo $body;
	?></div><?
	layout_display_alteration_note(null,$key);
	layout_close_box();
	?></article><?

	db_insert('infohit','
	INSERT INTO infohit SET
		PASSWD	=b\'0\',
		RELATION=b\''.(have_relation() ? 1 : 0).'\',
		ELEMENT	="'.addslashes($_REQUEST['ACTION']).'",
		PART	="'.addslashes($_REQUEST['SUBACTION'] ?? '').'",
		IPBIN	="'.addslashes(CURRENTIPBIN).'",
		IDENTID	='.CURRENTIDENTID.',
		USERID	='.CURRENTUSERID.',
		STAMP	='.CURRENTSTAMP
	);
}
