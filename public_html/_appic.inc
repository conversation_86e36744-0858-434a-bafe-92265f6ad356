<?php

require_once 'defines/appic.inc';
require_once '_urltitle.inc';

function get_appic_partner_prio(?string $partner = null): ?int {
	static $__partner_to_prio = [
		'Prospect'	=> 1,
		'Bronze'	=> 2,
		'Silver'	=> 3,
		'Gold'		=> 4,
		'Platinum'	=> 5,
		'Diamond'	=> 6,
	];
	return $__partner_to_prio[$partner] ?? 0;
}

function show_appic_link(
	?string			$element	= null,
	int|array|null	$arg		= null,
	?array			$orgs		= null,
): void {
	$element ??= $_REQUEST['sELEMENT'];
	$arg	 ??= $_REQUEST['sID'];

	if ($link = get_appic_link($element, $arg, $orgs)) {
		?><div class="block"><?= $link ?></div><?
	}
}

function get_appic_link(
	string		$element,
	int|array	$arg,
	?array		$orgs = null
): string {
	if (!($dev_admin = have_admin('development'))
	&&	!have_admin(['appic', $element])
	) {
		return '';
	}
	$add_middot = false;
	ob_start();
	if ($element === 'party') {
		$party = $arg;
		$id = $party['MAIN_ID'];
		if (!($appic_event = $party['appic_event'] ?? null)) {
			?><a class="appic" target="_blank" href="https://dashboard.beappic.com/"><?=
				get_appic_icon('light rmrgn')
			?> <?
		} else {
			if (!($appic_title = $appic_event['NAME'])) {
				[$appic_title] = get_appic_titles($party['PARTYID'], $orgs);
			}

			/** @noinspection PhpRedundantOptionalArgumentInspection */
			extract($appic_event, \EXTR_OVERWRITE);

			?><a class="appic" target="_blank" href="https://dashboard.beappic.com/rest-api/admin-panel/events/<?= $APPICID ?>/settings/main"><?=
				get_appic_icon('rmrgn'),
				escape_utf8($appic_title)
			?></a> <?

			?> <?= MIDDLE_DOT_ENTITY ?> <a target="_blank" class="small appic" href="https://appic.events/event/<?= $APPICID ?>">web</a><?

			if ($PARTNER) {
				require_once '_partner.inc';
				?> <?
				show_partner_state($PARTNER);
			}
			$add_middot = true;
			if ($SYNC_ENABLED === false) {
				$sync_is_off = true;
			}
		}
	} else {
		$id = $arg;
		if (!($appic_element = db_single_array('appic_element', "
			SELECT NAME, APPICID
			FROM appic_element
			WHERE ELEMENT = '$element'
			  AND ID = $id"))
		) {
			echo get_appic_icon('light rmrgn') ?> <?
		} else {
			[$appic_title, $APPICID] = $appic_element;

			ob_start();
			switch ($element) {
			case 'artist':
				?><a class="appic" target="_blank" href="https://dashboard.beappic.com/rest-api/admin-panel/artists/settings/<?= $APPICID ?>/main"><?
				break;
			case 'location':
				?><a class="appic" target="_blank" href="https://dashboard.beappic.com/rest-api/admin-panel/venues/<?= $APPICID ?>/settings/main/"><?
				break;
			case 'organization':
				break;
			}
			$linkopen = ob_get_flush();

			echo get_appic_icon('rmrgn'),  escape_utf8($appic_title);

			if ($linkopen) {
				?></a><?
			}
			$add_middot = true;
		}
	}

	if ($dev_admin) {
		if ($add_middot) {
			?> <?= MIDDLE_DOT_ENTITY ?> <?
		}
		$add_middot = true;
		?><a target="_blank" class="bold" href="/appic/<?= $element === 'party' ? 'event' : $element ?>/<?= $id ?>.json">JSON</a><?
	}

	if ($add_middot) {
		?> <?= MIDDLE_DOT_ENTITY ?> <?
	}

	if (isset($sync_is_off)) {
		?><span class="warning unavailable syncbutton">sync</span><?
	} else {
		include_js('js/appic');
		?><span onclick="Pf.syncToAppic(this, '<?= $element ?>', <?= $id ?>);" class="seembutton syncbutton">sync</span><?
	}
	return ob_get_clean();
}

function get_appic_titles(
	int|array $party,
	?array	  $orgs = null
): array|false|null {
	require_once '_memcache.inc';

	$partyid = is_int($party) ? $party : $party['PARTYID'];

	$key = PARTY_CACHE_APPIC.$partyid;

	if (!isset($_REQUEST['NOMEMCACHE'])
	&&	($cache = memcached_get($key))
	) {
		return $cache;
	}

	$cache = actually_get_appic_titles($party, $orgs);

	memcached_set($key, $cache, ONE_HOUR);

	return $cache;
}

function actually_get_appic_titles(array|int $party_or_id, ?array $orgs = null): array|false {
	static $__titles;
	if (isset($__titles)) {
		return $__titles;
	}
	require_once '_date.inc';
	if (is_int($party_or_id)
	||	!array_key_exists('FBTITLE', $party_or_id)
	||	!array_key_exists('FBTITLE_FROM_FEEDEVENT', $party_or_id)
	) {
		if (!($partyid = is_int($party_or_id) ? $party_or_id : $party_or_id['PARTYID'])) {
			mail_log('could not get_appic_titles');
			return $__titles = false;
		}
		if (!($party = memcached_single_assoc(['party', 'facebook_info', 'feedevent'],'
			SELECT	PARTYID, party.NAME, STAMP, STAMP_TZI,
					NAME_FOR_APPIC,
					SUBTITLE, LOCATIONID,
					facebook_info.NAME AS FBTITLE,
					(SELECT NAME FROM feedevent WHERE FEEDID = FBID ORDER BY MSTAMP DESC LIMIT 1) AS FBTITLE_FROM_FEEDEVENT,
					city.NAME AS CITY_NAME
			FROM party
			LEFT JOIN facebook_info USING (PARTYID)
			LEFT JOIN fbid ON ELEMENT = "party" AND ID = PARTYID
			LEFT JOIN boarding USING (BOARDINGID)
			LEFT JOIN location USING (LOCATIONID)
			LEFT JOIN city ON city.CITYID = COALESCE(boarding.CITYID, location.CITYID, party.CITYID)
			WHERE party.PARTYID = '.$partyid))
		) {
			return $__titles = false;
		}
	} else {
		$party = $party_or_id;
	}
	if (!$party) {
		return false;
	}
	assert(is_array($party)); # Satisfy EA inspection
	/** @noinspection PhpRedundantOptionalArgumentInspection */
	extract($party, \EXTR_OVERWRITE);

	$fb_title =
		$FBTITLE
	||	$FBTITLE_FROM_FEEDEVENT
	?	get_limited_utf8($FBTITLE ?: $FBTITLE_FROM_FEEDEVENT)
	:	'';
	$title = $NAME;
	$year  = null;

	if (preg_match('"\b(?<year>20[123456]\d)\b"ui', $fb_title, $match)) {
		$year = (int)$match['year'];
		if (!str_contains($title, (string)$year)) {
			[$y, $m, $d] = _getdate($party['STAMP_TZI'],'UTC');
			if ($y === $year) {
				$title .= ' '.$year;
			} else {
				$year = false;
			}
		}
	}
	if ($SUBTITLE) {
		 $title .= ' '.MIDDLE_DOT.' '.$SUBTITLE;
	}
	if (!($event_city = $party['CITY_NAME'] ?? null)
	&&	($cached_party = memcached_party_and_stamp($party['PARTYID']))
	) {
		$event_city = $cached_party['CITY_NAME'];
	}
	if ($event_city
	&&	false === mb_stripos($title, $event_city)
	&&	false === mb_stripos($SUBTITLE, $event_city)
	&&	false !== mb_stripos($fb_title, $event_city)
	) {
		$title .= ' '.MIDDLE_DOT.' '.$event_city;
	}
	if (false === ($have_similar = db_single('party', "
		SELECT 1
		FROM party
		WHERE LOCATIONID = {$party['LOCATIONID']}
		  AND NAME		 = '".addslashes($party['NAME'])."'
		  AND SUBTITLE	 = '".addslashes($party['SUBTITLE'])."'
		  AND STAMP		 > ".(TODAYSTAMP - 2 * ONE_MONTH)."
		  AND PARTYID	!= {$party['PARTYID']}
		LIMIT 1"))
	) {
		return false;
	}
	if (0 === strncmp($title, 'Happy Feelings', 14)) {
		if (!isset($m)) {
			[, $m, $d] = _getdate($party['STAMP_TZI'],'UTC');
		}
		$title .= ' '.MIDDLE_DOT.' '.$d.' '.short_month_name($m);
	}
	$addendum = null;
	if ($have_similar) {
		# many similar named events in the 2 month period
		# add artist names if they were used in Facebook title
		if (false === ($artists_in_lineup_names = db_simpler_array(['artist', 'lineup'], "
			SELECT DISTINCT NAME
			FROM artist
			JOIN lineup USING (ARTISTID)
			WHERE PARTYID = {$party['PARTYID']}"))
		) {
			return false;
		}
		if ($artists_in_lineup_names) {
			$posses = [];
			foreach ($artists_in_lineup_names as $artist_name) {
				# do a match with \b borders, so dj ND is not recognized in Ostend Beach
				if (preg_match(chr(0x1F).'\b'.preg_quote($artist_name, chr(0x1F)).'\b'.chr(0x1F).'ui', $fb_title)) {
					$posses[mb_stripos($fb_title, $artist_name)][] = $artist_name;
				}
			}
			if ($posses) {
				ksort($posses);
				$addendums = [];
				foreach ($posses as /* $pos => */ $artist_names) {
					$addendums += $artist_names;
				}
				$addendum = implode(', ', $addendums);

				$title .= ' '.MIDDLE_DOT.' '.$addendum;
			}
		}
	}
	db_insert('appic_titles_debug', "
	INSERT INTO appic_titles_debug SET
		PARTYID			= {$party['PARTYID']},
		STAMP			= UNIX_TIMESTAMP(),
		GENERATED_TITLE	= '".addslashes($title)."',
		CALCULATED_YEAR	= ".($year ?: '0').",
		DETERMINED_CITY	= '".addslashes($event_city)."',
		ADDENDUM		= '".addslashes($addendum)."'"
	);
	return [$NAME_FOR_APPIC ?: $title, $year, $event_city, $addendum];
}

function get_limited_utf8(string $str): string {
	if (preg_match('"'.
		'\xF0[\x90-\xBF][\x80-\xBF]{2}'.	# planes 1-3
		'|[\xF1-\xF3][\x80-\xBF]{3}'.		# planes 4-15
		'|\xF4[\x80-\x8F][\x80-\xBF]{2}'.	# plane 16
		'"ux',$str)
	) {
		return iconv('windows-1252', 'utf-8', iconv('utf-8', 'windows-1252//TRANSLIT', $str));
	}
	return $str;
}

function sync_to_appic(string $element, array|int $arg): bool {
	static $__pf_to_appic = [
		'organization'	=> 'organization',
		'party'			=> 'event',
		'artist'		=> 'artist',
		'location'		=> 'venue',
	];
	if ($element === 'party') {
		if (is_int($arg)) {
			if (!($party = memcached_party_and_stamp($arg))) {
				return false;
			}
		} else {
			$party = $arg;
		}
		$id = $party['MAIN_ID'] ?: $party['PARTYID'];
	} else {
		$id = $arg;
	}

	$ch = curl_init();
	if (!$ch) {
		return false;
	}

	$host = SERVER_SANDBOX ? APPIC_API_DEV_HOST : APPIC_API_HOST;

	curl_setopt_array($ch, [
		CURLOPT_RETURNTRANSFER	=> true,
		CURLOPT_POST			=> true,
		CURLOPT_FOLLOWLOCATION	=> true,
		CURLOPT_URL				=> 'https://'.$host.'/rest-api/v1/data-integrations/partyflock/webhooks/'.$__pf_to_appic[$element].'/'.$id,
		CURLOPT_HTTPHEADER		=> [
			'X-DATA-INTEGRATION-API-KEY: tNDqkNDiVbPLsWLxBHYFW26uLuuPR48qGGuLdxgCjEmCNNiEo?MhoAzrvykqmLzn',
			'Content-Type: application/json',
		],
		# CURLOPT_VERBOSE	=> true,
	]);

	$result = curl_exec($ch);
	$info = curl_getinfo($ch);

	if ($info['http_code'] !== 200) {
		error_log('failed to sync to appic, http status: '.$info['http_code']);
	}
	return $result !== false && $info['http_code'] === 200;
}

function appic_sync(): int {
	require_once '_currentuser.inc';
	global $currentuser;
	$currentuser ??= new _currentuser(TINY_USER | SKIP_SETTINGS);
	if (!require_post()
	&&	!require_admin()
	) {
		return http_status::FORBIDDEN->value;
	}
	if (!require_idnumber($_POST, 'ID')
	||	!require_element($_POST, 'ELEMENT', ['party', 'location', 'artist', 'organization'], true)
	) {
		return http_status::BAD_REQUEST->value;
	}
	if (!sync_to_appic($_POST['ELEMENT'], $_POST['ID'])) {
		return http_status::INTERNAL_SERVER_ERROR->value;
	}
	return http_status::OK->value;
}

function get_appic_for_party(array $party): array|false|null {
	$select_partyid = $party['MAIN_ID'] ?? $party['PARTYID'];
	return memcached_single_assoc('appic_event',
		$party['PARTYID'] < 376699
	?	/** @lang MariaDB */ '
		(	SELECT DISTINCT appic_event.*
			FROM appic_event
			WHERE PARTYID = '.$select_partyid.'
		) UNION (
			SELECT DISTINCT appic_event.*
			FROM appic_event
			JOIN fbid USING (FBID)
			WHERE ELEMENT = "party" AND ID = '.$select_partyid.'
		)
		ORDER BY MSTAMP DESC
		LIMIT 1'
	: '		SELECT *
			FROM appic_event
			WHERE PARTYID = '.$select_partyid
	);
}
