<?php

class flocklist {
	public $show_header	= true;
	public $show_cstamp	= false;

	private $tables		= array();
	private $size		= 0;
	private $selects	= array('flock.FLOCKID,NAME,flock.ACCEPTED,REMOVED,MEMBERCNT');
	private $sortbyname	= true;

	function query() {
		$this->tables[] = 'flock';
		if ($this->show_cstamp) {
			$this->selects[] = 'flock.CSTAMP';
		}
		if (have_admin('flock')) {
			$this->selects[] = 'PRIVATE';
		}
		$this->flocks = memcached_rowuse_array(
			$this->tables,'
			SELECT '.implode(',',$this->selects).'
			FROM flock '.
			(!empty($this->joins)	? implode(' ',$this->joins) : null).
			(!empty($this->wheres)	? ' WHERE '.implode(' AND ',$this->wheres) : null).
			(!empty($this->orderby)	? ' ORDER BY '.$this->orderby : null).
			(!empty($this->limit)	? ' LIMIT '.$this->limit : null)
		);
		if ($this->flocks === false) {
			return false;
		}
		$this->size = $this->flocks ? count($this->flocks) : 0;
		if (!$this->flocks) {
			return true;
		}
		if ($this->sortbyname) {
			string_sort($this->flocks,'NAME');
		}
		return true;
	}
	function size() {
		return $this->size;
	}
	function display() {
		if (empty($this->flocks)) {
			return;
		}
		layout_open_box('flock');
		layout_open_table(TABLE_VTOP | TABLE_FULL_WIDTH);
		if (!empty($this->show_header)) {
			layout_start_header_row();
			layout_start_header_cell();
			echo Eelement_name('name');
			if ($this->show_cstamp) {
				layout_next_header_cell_right();
				echo __C('field:created');
			}
			layout_next_header_cell_right();
			echo Eelement_plural_name('user');
			layout_stop_header_cell();
			layout_stop_header_row();
		}
		$is_admin = have_admin('flock');
		foreach ($this->flocks as $flock) {
			if (!$is_admin
			&&	(	$flock['REMOVED']
				||	!$flock['ACCEPTED']
				)
			) {
				continue;
			}
			$rflags = 0;
			if ($flock['REMOVED']) {
				$rflags |= ROW_LIGHT;
			}
			layout_start_rrow($rflags);
			if (!$flock['ACCEPTED']) {
				?><img src="<?= STATIC_HOST; ?>/images/lock<?= is_high_res() ?>.png" class="icon lower" title="<?= __('status:rejected') ?>" /> <?
			}
			echo get_element_link('flock',$flock['FLOCKID'],$flock['NAME']);
			if ($is_admin
			&&	$flock['PRIVATE']
			) {
				?> <b>(priv&eacute;)</b><?
			}
			if ($this->show_cstamp) {
				layout_next_cell(class: 'right');
				_datetime_display($flock['CSTAMP'],chr(160));
			}
			layout_next_cell(class: 'right');
			if ($flock['MEMBERCNT']) {
				echo $flock['MEMBERCNT'];
			}
			layout_stop_row();
		}
		layout_close_table();
		layout_close_box();
	}
	function name_like($part) {
		$this->wheres[] = 'NAME LIKE "%'.addslashes(str_replace(' ','%',$part)).'%"';
	}
	function with_leader($leaderid) {
		$this->wheres[] = 'LEADERID='.$leaderid;
	}
	function only_accepted() {
		$this->wheres[] = 'ACCEPTED=1 AND REMOVED=0';
	}
	function only_private() {
		$this->wheres[] = 'PRIVATE=1';
	}
	function am_member() {
		$this->tables[] = 'flockmember';
		$this->joins[] = 'JOIN flockmember USING (FLOCKID)';
		$this->wheres[] = 'flockmember.USERID='.CURRENTUSERID;
	}
	function newest() {
		$this->orderby = 'CSTAMP DESC';
		$this->limit = 100;
		$this->show_cstamp = true;
		$this->sortbyname = false;
	}
	function popular() {
		$this->popular = true;
		$this->limit = 100;
		$this->orderby = 'MEMBERCNT DESC';
		$this->sortbyname = false;
	}
	function flockids() {
		foreach ($this->flocks as $flock) {
			$flockids[] = $flock['FLOCKID'];
		}
		return isset($flockids) ? $flockids : null;
	}
}
function flocklist_display_invites(?int $stamp = null): int|false {
	require_once '_flock.inc';
	if (!($invites = memcached_rowuse_array(['flockinvitation', 'flock'], '
		SELECT FLOCKID, NAME, LEADERID
		FROM flockinvitation
		JOIN flock USING (FLOCKID)
		WHERE ACCEPTED = 1
		  AND (ACCESS & '.FXS_INVITE.' OR NOT ACCESS & '.FXS_REQUEST.')
		  AND REMOVED = 0
		  '.($stamp ? " AND flockinvitation.CSTAMP > $stamp" : '').'
		  AND flockinvitation.USERID = '.CURRENTUSERID,
		DEFAULT_EXPIRATION,
		'flockinvites:'.CURRENTUSERID
	))) {
		/** @noinspection ReturnTernaryReplacementInspection */
		return $invites === false ? false : 0;
	}
	layout_open_box('flock');
	layout_box_header(Eelement_plural_name('invitation'));
	layout_open_table(TABLE_FULL_WIDTH);
	layout_start_header_row();
	layout_header_cell(Eelement_name('name'));
	layout_header_cell(Eelement_plural_name('leader'));
	layout_stop_header_row();
	$count = 0;
	foreach ($invites as $invite) {
		++$count;
		layout_start_rrow();
		echo get_element_link('flock',$invite['FLOCKID'],$invite['NAME']);
		layout_next_cell();
		echo get_element_link('user',$invite['LEADERID']);
		layout_stop_row();
	}
	layout_close_table();
	layout_close_box();
	return $count;
}
function flocklist_display_requests() {
	$requests = memcached_rowuse_array(
		array('flockrequest','flock'),'
		SELECT FLOCKID,NAME,COUNT(*) AS CNT
		FROM flockrequest
		JOIN flock USING (FLOCKID)
		WHERE LEADERID='.CURRENTUSERID.'
		  AND ACCEPTED=1
		  AND REMOVED=0
		GROUP BY FLOCKID',
		DEFAULT_EXPIRATION,
		'flockrequests_for:'.CURRENTUSERID
	);
	if ($requests === false) {
		return;
	}
	if ($requests) {
		layout_open_box('flock');
		layout_box_header(Eelement_plural_name('request'));
		layout_open_table(TABLE_FULL_WIDTH);
		layout_start_header_row();
		layout_header_cell(Eelement_name('name'));
		layout_header_cell_right(__C('field:amount'));
		layout_stop_header_row();
		foreach ($requests as $request) {
			layout_start_rrow();
			?><a href="/flock/<?= $request['FLOCKID'] ?>/requestsform"><?= escape_utf8($request['NAME']) ?></a><?
			layout_next_cell(class: 'right');
			echo $request['CNT'];
			layout_stop_row();
		}
		layout_close_table();
		layout_close_box();
	}
}
