<?php

declare(strict_types=1);

const STUFF_AD		= 0;
const STUFF_BANNER	= 1;

# FIXME: Update _banner.inc calling creata_ad_stuff to check return for false.

function create_ad_stuff(int $type, ?int $seed = null): string|false {
	if (!$seed && $type === STUFF_BANNER) {
		error_log('WARNING create_ad_stuff STUFF_BANNER requires a seed!');
		return false;
	}
	$stuff = '';
	# 32 bit $seed is okish for us for now, number of repeatable random
	# squences is only 2^32 due to the fact it has only 32 bits.
	# Using a repeatble Random Engine for banners, because we don't want people to load
	# the same banner multitple times due to uniqueness.
	# For ads it has to be unique, because we want to count it for sure.
	try {
		$randomizer =
			$type === STUFF_BANNER
		?	new Random\Randomizer(new Random\Engine\PcgOneseq128XslRr64($seed))
		:	new Random\Randomizer();
		$nums  = $randomizer->getBytesFromString('0123456789', 4);
		$chars = $randomizer->getBytesFromString('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ', 4);
	} catch (Random\RandomException $e) {
		mail_log('create_add_stuff failed to fill stuff: '.$e->getMessage());
		return false;
	}
	for ($i = 0; $i < 4; ++$i) {
		$stuff .=
			$type === STUFF_AD
		?	$chars[$i].$nums[$i]
		: 	$nums[$i].$chars[$i];
	}
	return $stuff;
}
