<?php

require_once '_help.inc';

function perform_commit() {
	switch ($_REQUEST['ACTION']) {
	case 'commit':		return policy_commit();
#	case 'remove':		return policy_remove();
	}
}
function display_body() {
	switch ($_REQUEST['ACTION']) {
	case null:
#	case 'remove':
		return policy_display_overview();
	case 'single':
	case 'commit':		return policy_display_single();
	case 'form':		return policy_display_form();
#	case 'removenotsure':	return policy_display_remove_notsure();
	default:		return policy_display_section();
	}
}
function policy_menu($policy = null) {
	layout_open_menu();
	layout_menuitem(Eelement_name('overview'),'/policy',!$_REQUEST['ACTION']);
	layout_close_menu();
	if (!have_admin('policy')) {
		return;
	}
	layout_open_menu();
	if ($policy) {
		layout_menuitem(__C('action:change'),'/policy/'.$policy['POLICYID'].'/form');
	}
	layout_close_menu();
}
function policy_display_overview() {
	layout_show_section_header();
	
	policy_menu();
	policy_show_overview();
}
function policy_show_overview($selected = null) {
	$policies = db_simple_hash('policy','
		SELECT POLICYID,HELPSECTION
		FROM policy'
	);
	if ($policies === false) {
		return;
	}
	if (!$policies) {
		?><p><?= __('policy:no_policies_LINE'); ?></p><?
		return;
	}
	foreach ($policies as $policyid => $section) {
		$sectionnames[$policyid] = __C('helpsection:'.$section);
	}
	asort($sectionnames);
	layout_open_box('policy');
	layout_open_table(TABLE_FULL_WIDTH);
	foreach ($sectionnames as $policyid => $sectionname) {
		layout_start_rrow();
		if ($selected
		&&	$selected['POLICYID'] == $policyid
		) {
			?><b><?= $sectionname; ?></b><?
		} else {
			?><a href="/policy/<?= $policies[$policyid]; ?>"><?= $sectionname; ?></a><?
		}
		layout_stop_row();
	}
	layout_close_table();
	layout_close_box();
}
function policy_display_section() {
	if (!require_helpsection($_REQUEST,'ACTION')) {
		return;
	}
	$policyid = db_single('policy','SELECT POLICYID FROM policy WHERE HELPSECTION="'.$_REQUEST['ACTION'].'"');
	if ($policyid === false) {
		return;
	}
	if (!$policyid) {
		_error(__('policy:error:nonexistent_policy_section_LINE',array('SECTION'=>$_REQUEST['ACTION'])));
		return;
	}
	$_REQUEST['POLICYID'] = $policyid;
	policy_display_single();
}
function policy_display_single() {
	if (!require_idnumber($_REQUEST,'POLICYID')) {
		return;
	}
	layout_show_section_header();
	$policyid = $_REQUEST['POLICYID'];
	$policy = db_single_assoc('policy','
		SELECT POLICYID,HELPSECTION,CUSERID,MUSERID,CSTAMP,MSTAMP,GENERAL,GUIDELINES
		FROM policy
		WHERE POLICYID='.$policyid
	);
	if ($policy === false) {
		return;
	}
	if (!$policy) {
		_error(__('policy:error:nonexistent_LINE',array('ID'=>$policyid)));
		return;
	}
	policy_menu($policy);
	//policy_show_overview($policy);

#	print_rr($policy);

	$general	= __($generalkey = 'policy:'.$policyid.':general_TEXT',DO_UBB | DO_NL2BR | DONT_WARN_MISSING);
	$guidelines	= __($guidelineskey = 'policy:'.$policyid.':guidelines_TEXT',DO_UBB | DO_NL2BR | RETURN_FALSE | DONT_WARN_MISSING);

	?><article itemscope itemtype="https://schema.org/Article"><?
	layout_open_box('policy');
	if ($general) {
		layout_open_box_header();
		if ($policy['HELPSECTION'] == 'general') {
			echo __C('policy:general_policy');
		} else {
			echo __C('policy:policy');
			?> <?
			echo __('helpsection:'.$policy['HELPSECTION']);
		}
		layout_close_box_header();
		?><div class="block"><?= $general ?></div><?
	}
	if ($guidelines) {
		if ($general) {
			layout_continue_box();
		}
		layout_open_box_header();
		if ($policy['HELPSECTION'] == 'general') {
			echo __C('policy:general_guidelines');
		} else {
			echo __C('policy:guidelines');
			?> <?
			echo __('helpsection:'.$policy['HELPSECTION']);
		}
		layout_close_box_header();
		?><div class="block"><?= $guidelines; ?></div><?
	}
	layout_display_alteration_note($policy,$general ? $generalkey : null,$guidelines ? $guidelineskey : null);

	layout_close_box();
	?></article><?
}
function policy_display_form() {
	if (!require_admin('policy')
	||	!optional_number($_REQUEST,'POLICYID')
	) {
		return;
	}
	layout_show_section_header();

	if (isset($_REQUEST['POLICYID'])) {
		$policyid = $_REQUEST['POLICYID'];
		$helpsection = db_single('policy','SELECT HELPSECTION FROM policy WHERE POLICYID='.$policyid);
		if ($helpsection === false) {
			return;
		}
		if (!$helpsection) {
			_error(__('policy:error:nonexistent_LINE',array('ID'=>$policyid)));
			return;
		}
	} else {
		$policyid = 0;
	}
	?><form onsubmit="return submitForm(this);" method="post" action="/policy/<?
	if ($policyid) {
		echo $policyid; ?>/<?
	}
	?>commit"><?
	layout_open_box('policy');
	layout_open_table(TABLE_VTOP);
	layout_start_row();
		echo Eelement_name('section');
		layout_field_value();
		?><select name="HELPSECTION"><option></option><? helpsection_options($policyid ? $helpsection : null);
		?></select><?
	layout_restart_row();
		echo Eelement_name('policy');
		layout_field_value();
		?><textarea name="GENERAL" cols="80" rows="15"><?
		if ($policyid) {
			echo __('policy:'.$policyid.':general_TEXT',RETURN_FALSE | FORCE_READ);
		}
		?></textarea><?
	layout_restart_row();
		echo Eelement_plural_name('guideline');
		layout_field_value();
		?><textarea name="GUIDELINES" cols="80" rows="15" wrap="off"><?
		if ($policyid) {
			echo __('policy:'.$policyid.':guidelines_TEXT',RETURN_FALSE | FORCE_READ);
		}
		?></textarea><?
	layout_close_table();
	layout_close_box();
	?><div class="block"><input type="submit" value="<?= __($policyid ? 'action:change' : 'action:add') ?>" /></div></form><?
}
function policy_commit() {
	require_once '_ubb_preprocess.inc';
	if (!require_admin('policy')
	||	!require_anything_trim($_POST,'GENERAL')
	||	!require_anything_trim($_POST,'GUIDELINES')
	||	!require_helpsection($_POST,'HELPSECTION')
	||	!optional_number($_REQUEST,'POLICYID')
	) {
		return;
	}
	$setlist[] = 'GENERAL='.	($_POST['GENERAL'] ? 1 : 0);
	$setlist[] = 'GUIDELINES='.	($_POST['GUIDELINES'] ? 1 : 0);
	$setlist[] = 'HELPSECTION="'.	addslashes($_POST['HELPSECTION']).'"';

	if (isset($_REQUEST['POLICYID'])) {
		$policyid = $_REQUEST['POLICYID'];
		$setlist[] = 'MUSERID='.CURRENTUSERID;
		$setlist[] = 'MSTAMP='.CURRENTSTAMP;
		if ($_POST['GENERAL']	&& !store_translation('policy:'.$policyid.':general_TEXT',_ubb_preprocess($_POST['GENERAL']))
		||	$_POST['GUIDELINES'] && !store_translation('policy:'.$policyid.':guidelines_TEXT',_ubb_preprocess($_POST['GUIDELINES']))
		||	!db_insert('policy_log','
			INSERT INTO policy_log
			SELECT * FROM policy
			WHERE POLICYID='.$policyid)
		||	!db_update('policy','
			UPDATE policy SET '.implode(',',$setlist).'
			WHERE POLICYID='.$policyid)
		) {
			return;
		}
		_notice(__('policy:notice:policies_and_guidelines_changed_LINE'));
	} else {
		$setlist[] = 'CUSERID='.CURRENTUSERID;
		$setlist[] = 'CSTAMP='.CURRENTSTAMP;
		if (!db_insert('policy','
			INSERT INTO policy SET '.implode(',',$setlist))
		) {
			return;
		}
		$_REQUEST['POLICYID'] =
		$_REQUEST['sID'] =
		$policyid = db_insert_id();
		if ($_POST['GENERAL'] && !store_translation('policy:'.$policyid.':general_TEXT',_ubb_preprocess($_POST['GENERAL']))
		||	$_POST['GUIDELINES'] && !store_translation('policy:'.$policyid.':guidelines_TEXT',_ubb_preprocess($_POST['GUIDELINES']))
		) {
			return;
		}
		_notice(__('policy:notice:policies_and_guidelines_added_LINE'));
	}
}
