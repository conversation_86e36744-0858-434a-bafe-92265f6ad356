<?php

declare(strict_types=1);

define('CURRENTSTAMP',time());

require_once '_db.inc';
require_once '_nocache.inc';

send_no_cache_headers();

function bail(int $errno): never {
	http_response_code($errno);
	exit;
}

if (!isset($_SERVER['eID'])) {
	bail(404);
}

require_once '_shortlink.inc';

$id = to_10($_SERVER['eID']);

if (!$id) {
	bail(404);
}
if (!($url = db_single('shortlink','SELECT URL FROM shortlink WHERE ID='.$id))) {
	bail($url === false ? 503 : 404);
}

require_once '_url.inc';

$url = make_absolute_url($url);

header('Content-Type: text/html; charset=us-ascii');
header('Location: '.$url, true, 303);

?><a href="<?= $escaped_url = escape_utf8($url) ?>"><?= $escaped_url ?></a><?

require_once '_identity.inc';
require_once '_getcurrentaddr.inc';
require_once '_currentuser.inc';

$currentuser = new _currentuser(TINY_USER | SKIP_SETTINGS);

if (($len = strlen(CURRENTIPBIN)) > 16) {
	mail_log('CURRENTIPBIN is too large', [
		'strlen'	=> strlen(CURRENTIPBIN),
		'inet_ntop'	=> inet_ntop(CURRENTIPBIN),
		'_SERVER'	=> $_SERVER,
	]);
}

db_insert('shortlinkstats', '
INSERT INTO shortlinkstats SET
	ID		= '.$id.',
	STAMP	= '.CURRENTSTAMP.',
	IPBIN	= " '.addslashes(CURRENTIPBIN).'",
	IDENTID	= '.CURRENTIDENTID.',
	USERID	= '.CURRENTUSERID
);
