<?php

declare(strict_types=1);

function show_videos_and_photos(
	string	$element,
	int		$id,
	?array 	$info		 = null,
	bool	$hide_videos = false
): array|false|null {
	return $shoots = show_photos($element, $id);
	if (!$hide_videos) {
		show_videos($element, $id, $info);
	}
	return $shoots;
}

function show_photos(string $element, int $id): array|false|null {
	require_once '_browser.inc';
	ob_start();
	if ($element === 'party') {
		if (!($shoots = db_rowuse_hash(['image', 'gallery', 'user_account'],"
			SELECT	GALLERYID, USERID, VISIBLE, ANONYMOUS, PARTYID,
					(	SELECT CONCAT(COUNT(*), ',', COUNT(IF(HIDDEN, 1, NULL)))
						FROM image
						WHERE image.GALLERYID = gallery.GALLERYID
					)	AS COUNTS
			FROM gallery
			JOIN user_account USING (USERID)
			WHERE PARTYID = $id
			ORDER BY NICK"))
		) {
			return $shoots;
		}
		if (!have_admin(['photo', 'gallery', 'camerarequest'])) {
			require_once '_gallery.inc';
			foreach ($shoots as $galleryid => $shoot) {
				if (!visible_gallery($shoot)
				&&	CURRENTUSERID !== $shoot['USERID']
				) {
					/** @noinspection OffsetOperationsInspection, PhpAutovivificationOnFalseValuesInspection */
					unset($shoots[$galleryid]);
				}
			}
		}
		if (!$shoots) {
			return $shoots;
		}
		foreach ($shoots as $galleryid => &$shoot) {
			[$total_cnt, $hidden_cnt] = explode(',', $shoot['COUNTS']);
			$shoot['CNT'] = $total_cnt;
			layout_open_box_header(id: 'photos');
			?><h2><a href="<?= get_element_href('gallery',$galleryid) ?>"><?=
				Eelement_plural_name('photo') ?> <?
			?>(&rarr; <?= __('action:show_all_photos',['CNT' => $total_cnt - $hidden_cnt]) ?>)</a></h2><?

			layout_continue_box_header();
			if ($shoot['USERID']
			&&	(	!$shoot['ANONYMOUS']
				||	$shoot['USERID'] === CURRENTUSERID
				||	have_admin('gallery')
				)
			) {
				if ($shoot['ANONYMOUS']) {
					?><span class="light"><?
				}
				echo element_name('photographer') ?>:  <? echo get_element_link('user',$shoot['USERID']);
				if ($shoot['ANONYMOUS']) {
					?></span><?
				}
			}
			layout_close_box_header();
			if ($photos = memcached_rowuse_hash('image','
				SELECT IMGID,THUMB_WIDTH,THUMB_HEIGHT
				FROM image
				WHERE HIDDEN=0
				  AND GALLERYID='.$galleryid.'
				ORDER BY
					THUMB_WIDTH'.(safe_random_int(0, 3) ? '>=' : '<').'THUMB_HEIGHT DESC,
					RAND()
				LIMIT '.(SMALL_SCREEN ? 4 : 10))
			) {
				?><div class="center"><?
				foreach ($photos as $photoid => $photo) {
					/** @noinspection PhpRedundantOptionalArgumentInspection */
					extract($photo, \EXTR_OVERWRITE);
					?><div<?
					?> class="center unlabelled ib"<?
					?>><?

					?><a href="<?= get_element_href('photo', $photoid) ?>" class="middle"><?
					?><img<?
						?> class="zoomover ib"<?
						?> alt="<?= escape_utf8(get_element_title('party', $id)) ?> foto"<?
						?> src="<?= get_photo_url($photoid, 'thumb') ?>"<?
						?> width="<?= $THUMB_WIDTH ?>"<?
					?>></a><?
					?></div><?
				}
				?></div><?
			}
		}
		unset($shoot);
	} else {
		require_once '_gallerylist.inc';
		$gallery_list =  new _gallerylist();
		$gallery_list->connected_to($element, $id);
		if ($gallery_list->query(SMALL_SCREEN ? 4 : 10)) {
			$linkopen = "<a href=\"/$element/$id/photos#photos\">";
			layout_box_header(
			'<h2>'.$linkopen.Eelement_plural_name('photo').'</a></h2>',
			$gallery_list->surplus ? $linkopen.element_name('overview_of_all_photos').'</a>' : null
			);
			$gallery_list->no_wrap = true;
			$gallery_list->show_thumbs();
		}
	}
	if ($photo_data = ob_get_clean()) {
		layout_open_box($element);
		echo $photo_data;
		layout_close_box();
	}
	return $shoots ?? null;
}

function show_videos(string $element, int $id, ?array $info = null): void {
	require_once '_browser.inc';
	ob_start();
	layout_open_box($element);

	######### NEW MOVIES
	ob_start();
	require_once '_videolist.inc';
	$video_list = new _videolist();
	$video_list->only_active = true;
	$video_list->connected_to($element, $id);
	$video_list->not_only_music = true;

	if (!empty($info['allorgs'])) {
		require_once '_videoaftermovie.inc';
		$video_list->add_aftermovie = get_aftermovie($info, $info['allorgs'], 2 * ONE_YEAR);
	}
	if ($video_list->query(SMALL_SCREEN ? 4 : 10)
	&&	($video_count = $video_list->have_videos()) > 1
	) {
		# NOTE	only show when we have more than one, because
		#		showcase will show the one immediately anyway (first of playlist)
		$linkopen = '<a href="/video/'.$element.'/'.$id.'">';
		?><article id="videos"><?
		layout_box_header(
			'<h2>'.$linkopen.Eelement_name('video', $video_count).'</a></h2>',
			$linkopen.element_name('overview_of_all_videos').'</a>',
		);
		?><div class="block"><?
		$video_list->dont_open_box = true;
		$video_list->per_row(SMALL_SCREEN ? 2 : 5);
		$video_list->show_thumbs();
		?></div><?
		?></article><?
	}
	$video_data = ob_get_clean();

	ob_start();
	$video_list = new _videolist();
	$video_list->only_active = true;
	$video_list->connected_to($element, $id);
	$video_list->only_music = true;
	$video_list->dont_open_box = true;
	$video_list->hide_content_type = true;

	if ($element !== 'party') {
		$video_list->newer_than(TODAYSTAMP - ONE_YEAR);
	}
	if ($video_list->query(SMALL_SCREEN ? 4 : 10)
	&&	($video_count = $video_list->have_videos())
	) {
		$linkopen = '<a href="/video/'.$element.'/'.$id.'/musiconly">';
		?><article itemscope itemtype="https://schema.org/VideoGallery"><?
		layout_box_header(
			'<h2>'.$linkopen.Eelement_name('music', $video_count).'</a></h2>',
			$linkopen.element_name('overview_of_all_music_videos').'</a>',
		);
		$video_list->per_row(SMALL_SCREEN ? 2 : 5);
		$video_list->show_thumbs();
		?></article><?
	}
	$music_data = ob_get_clean();

	if ($video_data || $music_data) {
		echo $video_data, $music_data;
	}
	layout_close_box();
	$data = ob_get_clean();
	if ($video_data || $music_data) {
		echo $data;
	}
}
