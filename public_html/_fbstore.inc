<?php

#use Facebook\FacebookSession;
#use Facebook\FacebookRequest;

require_once '_fbsession.inc';	# for fb_process_exception
require_once 'defines/facebook.inc';

define('FBGOING_MAX_ROWS',	10);

function store_fb_info($obj,$access_token = null,$partyid = 0) {
	$rc = true;
	if (property_exists($obj,'fan_count')) {
		if (!db_insert('fblikes_log','
			INSERT INTO fblikes_log
			SELECT * FROM fblikes
			WHERE FBID='.$obj->id)
		||	!db_replace('fblikes','
			REPLACE INTO fblikes SET
				STAMP	='.time().',
				FBID	='.$obj->id.',
				LIKES	='.$obj->fan_count)
		) {
			$rc = false;
		}
	}
	if (property_exists($obj,'attending_count')) {
		if (!db_insert('fbcounters_log','
			INSERT INTO fbcounters_log
			SELECT * FROM fbcounters
			WHERE FBID='.$obj->id)
		||	!db_replace('fbcounters','
			REPLACE INTO fbcounters SET
				STAMP		='.time().',
				FBID		='.$obj->id.',
				INVITED		='.(getifset($obj,'invited_count') ?: 0).',
				ATTENDING	='.(getifset($obj,'attending_count') ?: 0).',
				MAYBE		='.(getifset($obj,'maybe_count') ?: 0).',
				DECLINED	='.(getifset($obj,'declined_count') ?: 0).',
				NOREPLY		='.(getifset($obj,'noreply_count') ?: 0))
		) {
			$rc = false;
		}
		if (!$partyid) {
			$partyid = db_single('fbid','SELECT ID FROM fbid WHERE ELEMENT="party" AND FBID='.$obj->id);
			if ($partyid === false) {
				return false;
			}
		}
		$not_all_removed = db_single('feedevent','
			SELECT SUM(IF(REMOVED AND PARTYID=0,0,1))
			FROM feedevent
			WHERE FEEDID='.$obj->id
		);
		if ($not_all_removed === false) {
			return false;
		}	
		if ($not_all_removed !== null
		&&	!$not_all_removed
		) {
#			error_log('fbid '.$obj->id.' ALL REMOVED! skipping getting attending, partyid '.$partyid);
		} else {
			$lstamp = db_single($table = $partyid ? 'fbgoing_done' : 'fbgoingpre_done','
				SELECT STAMP
				FROM '.$table.'
				WHERE '.($partyid ? 'PARTYID='.$partyid : 'EVENTID='.$obj->id)
			);
			if ($lstamp === false) {
				return false;
			}
			$get = false;
			if (!property_exists($obj,'start_time')) {
				error_log('start_time not set in obj!!!');
				return false;
			}
			if (!$lstamp) {
				$get = true;
			} else {
				$delay = 2 * ONE_WEEK;
				$stamp = strtotime($obj->start_time);
				if ($stamp > CURRENTSTAMP) {
					# future events
					if ($partyid) {
						# with partyflock event
						$info = db_simpler_array('going','
							SELECT COUNT(MAYBE=0),COUNT(MAYBE=1)
							FROM going
							WHERE PARTYID='.$partyid
						);
					} else {
						# without partyflock event
						$info = db_simpler_array(['fbgoingpre','facebook'],'
							SELECT COUNT(MAYBE=0),COUNT(MAYBE=1)
							FROM fbgoingpre
							LEFT JOIN facebook ON FBID=FACEBOOKID
							WHERE EVENTID='.$obj->id
						);
					}
					if (!$info) {
						list($attending,$interested) = $info;
						if ($attending >= 5 || $interested >= 10) {
							$delay = 2 * ONE_DAY;
						} elseif ($attending || $interested) {
							$delay = 4 * ONE_DAY;
						} else {
							$delay = ONE_WEEK;
						}
					}
				} else {
					# past events
					if ($partyid) {
						# with flock event
						if ($stamp < CURRENTSTAMP - ONE_MONTH) {
							$delay = ONE_MONTH;
						} else {
							$delay = 2 * ONE_WEEK;
						}
					} else {
						$delay = ONE_MONTH;
					}
				}
				$get = $lstamp < CURRENTSTAMP - $delay;
			}
			if ($get) {
				$allfbids = [];
				
				static $__sessions;
				
				if (!$access_token) {
					$access_token = FACEBOOK_APPID.'|'.FACEBOOK_APPSECRET;
				}
				if (isset($__sessions[$access_token])) {
					$session = $__sessions[$access_token];
				} else {
/*					require_once 'defines/facebook.inc';
					FacebookSession::setDefaultApplication(FACEBOOK_APPID, FACEBOOK_APPSECRET);
					$session = null;
					try {
						$session = new FacebookSession($access_token);
					} catch (Exception $e) {
						fb_process_exception($session,$e);
						return false;
					}
					$__sessions[$access_token] = $session;*/
				}
				
				$ok = true;
				
/*				new Facebook limitations don't allow this anymore

				foreach (['attending','maybe'] as $rsvp) {
					$maybe = $rsvp == 'maybe';
					$q = null;
					try {
						$q = '/'.$obj->id.'/'.$rsvp.'?limit=1000';
						
						fbgraph_access($obj->id,$q,$rsvp);

						$request = new FacebookRequest($session,'GET',$q);

						for (; $request; $request = $response->getRequestForNextPage()) {
							if (!($response = $request->execute())
							||	!($data = $response->getGraphObject()->asArray())
							) {
								break;
							}

							if (empty($data['data'])) {
								break;
							}
							foreach ($data['data'] as $going) {
								if (isset($allfbids[$maybe][$going->id])) {
									break 2;
								}
								$allfbids[$maybe][$going->id] = $going->id;

						}
					} catch (Exception $e) {
						fb_process_exception($session,$e,0,null,$q,$access_token);//,[2 => true]);
						$ok = false;
						break;
					} catch (FacebookException $e) {
						fb_process_exception($session,$e,0,null,$q,$access_token);//,[2 => true]);
						$ok = false;
						break;
					}
				}*/
			if ($ok) {
				if ($allfbids) {
					require_once '_goingparty.inc';
					foreach ($allfbids as $maybe => $fbids) {
						if (!$partyid) {
							$inslist = [];
							foreach ($fbids as $fbid => $true) {
								$inslist[$fbid] = '('.$fbid.','.$obj->id.','.($maybe ? "b'1'" : "b'0'").')';
							}

							$keystr = implodekeys(',',$inslist);

							if (!db_delete('fbgoingpre','
								DELETE FROM fbgoingpre
								WHERE MAYBE='.($maybe ? 1 : 0).'
								  AND FBID NOT IN ('.$keystr.')
								  AND EVENTID='.$obj->id)
							) {
							}
							$size = count($fbids);
							reset($fbids);
							unset($qstr);
							for ($i = 0; $i < $size; ++$i) {
								if (!isset($qstr)) {
									$qstr = 'INSERT LOW_PRIORITY IGNORE INTO fbgoingpre (FBID,EVENTID,MAYBE) VALUES ';
								} else {
									$qstr .= ',';
								}
								$qstr .= '('.key($fbids).','.$obj->id.','.($maybe ? "b'1'" : "b'0'").')';
								if ( ($i == $size-1)
								||	($i % FBGOING_MAX_ROWS == 0)
								) {
									if (!db_insert('fbgoingpre',$qstr)) {
										return false;
									}
									unset($qstr);
								}
								next($fbids);
							}
						} else {
							fbs_join_party($fbids,$partyid,$maybe);
						}
					}
				}
				db_insupd($table = $partyid ? 'fbgoing_done' : 'fbgoingpre_done','
					INSERT INTO '.$table.' SET
						STAMP	='.CURRENTSTAMP.',
						'.($partyid ? 'PARTYID='.$partyid : 'EVENTID='.$obj->id).'
					ON DUPLICATE KEY UPDATE
						STAMP	=VALUES(STAMP)'
				);
			}
			}
		}
	}
	return true;
}
function fb_store_upgrade($partyid,$fbid) {
	require_once '_goingparty.inc';
	$fbidlists = db_same_hash('fbgoingpre','SELECT MAYBE,FBID FROM fbgoingpre WHERE EVENTID='.$fbid);

	if (!$fbidlists) {
		return $fbidlists !== false;
	}

	$rc = true;

	foreach ($fbidlists as $maybe => $fbids) {
		$rc = fbs_join_party($fbids,$partyid,$maybe) && $rc;
		
		$cnts[$maybe] = count($fbids);		
	}

	db_insert('fbcounters','
	INSERT IGNORE INTO fbcounters SET
		STAMP		='.time().',
		FBID		='.$fbid.',
		ATTENDING	='.(getifset($cnts,0) ?: 0).',
		MAYBE		='.(getifset($cnts,1) ?: 0)
	);
	
	db_delete('fbgoingpre','
	DELETE FROM fbgoingpre
	WHERE EVENTID='.$fbid
	);

	memcached_delete('fbcounters:party:'.$partyid);
	
	return $rc;
}
