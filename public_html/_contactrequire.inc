<?php

require_once '_contactelements.inc';

function require_contact_element($array, string $field) {
	return require_element($array, $field, get_contact_elements());
}

function have_contact_element(array $array, string $field): string|false {
	return	isset($array[$field])
		&&	is_string($array[$field])
		&&	is_contact_element($array[$field])
		?	$array[$field]
		:	false;
}

function have_contact_archive(?int $year = null, ?int $month = null, ?string $order = null): bool {
	require_once '_contact.inc';
	require_once '_date.inc';
	global $__year, $__month;
	$elems = _contact_construct_element_list();
	$elems[] = 'fake';
	if (have_admin()) {
			$elems += ['junk_spam', 'junk_nolearn', 'junk_recognize', 'junk_failure', 'junk_mailinglist'];
	}
	if ($year) {
		if ($month) {
			$start = mktime(0,0,0, year: $year ?? $__year, month: $month ?? $__month, day: 1);
			$stop  = strtotime('+1 month -1 second', $start);
		} else {
			$start = mktime(0,0,0, year: $year,		month: 1, day: 1);
			$stop  = mktime(0,0,0, year: $year + 1, month: 1, day: 1) - 1;
		}
	} else {
		$start = null;
	}

	return (bool)memcached_single('contact_ticket', '
		SELECT 1
		FROM contact_ticket
		WHERE (	ELEMENT IN ("'.implode('", "', $elems).'")
			 OR OWNERID = '.CURRENTUSERID.')'.(
			 $start
		?	' AND '.($order ?? 'LSTAMP').' BETWEEN '.$start.' AND '.$stop
		:	'').'
		LIMIT 1');
}

const ACCEPT_DEFAULT	= 1;
const ACCEPT_EMPTY		= 2;

function require_array_of_contact_elements(array $array, string $field, int $flags = 0): string|false {
	if ($flags & ACCEPT_EMPTY) {
		if (!isset($array[$field])) {
			return true;
		}
	}
	if (!require_array($array, $field)) {
		return false;
	}
	foreach ($array[$field] as $ndx => $element) {
		if (($flags & ACCEPT_DEFAULT)
		&&	$element === 'default'
		) {
			continue;
		}
		if (!is_contact_element($element)) {
			register_error('require:array_of_contact_elements:invalid_element_LINE', [
				'NAME'	=> $field,
				'INDEX'	=> $ndx,
				'VALUE'	=> $element
			]);
			return false;
		}
	}
	return true;
}
function require_array_with_keys_of_contact_element(array $array, string $field, int $flags): bool {
	if (!require_array($array, $field, utf8: true)) {
		return false;
	}
	foreach ($array[$field] as $element => $val) {
		if (($flags & ACCEPT_DEFAULT)
		&&	$element === 'default'
		) {
			continue;
		}
		if (!is_contact_element($element)) {
			register_error('require:array_of_contact_elements:invalid_index_LINE', [
				'NAME'	=> $field,
				'INDEX'	=> $element,
				'VALUE'	=> $val
			]);
			return false;
		}
	}
	return true;
}

function have_ticket_lock(int $ticketid): bool {
	require_once '_lock.inc';
	return	have_admin()
		&&	have_lock(LOCK_TICKET, $ticketid, ALLOW_STALE);
}

function require_ticket_lock(int $ticketid): bool {
	require_once '_lock.inc';
	return	require_admin()
		&&	require_lock(LOCK_TICKET, $ticketid, ALLOW_STALE);
}

function require_outgoing_element(array $array, string $field): bool {
	return require_contact_element($array, $field);
}
