<?php

require_once '_memcache.inc';

function flush_pingdom_ips(): void {
	memcached_delete('pingdom_ipv4', 'pingdom_ipv6');
}

function is_pingdom_ip(?int $ipnum = null, ?string $ipbin = null): bool {
	if ($ipnum === null
	&&	$ipbin === null
	) {
		$ipnum = CURRENTIPNUM;
		$ipbin = CURRENTIPBIN;
	}

	static $__pingdom;
	foreach ([4 => 'ipnum', 6 => 'ipbin'] as $v => $var) {
		if (!$$var) {
			continue;
		}
		$__pingdom[$v] ??= memcached_same_hash('pingdom_ipv'.$v, 'SELECT '.$var.' FROM pingdom_ipv'.$v, ONE_WEEK, 'pingdom_ipv'.$v) ?: [];
		if (isset($__pingdom[$v][$$var])) {
			db_insert('pingdom_hit', '
			INSERT INTO pingdom_hit SET
				REQUEST_URI	= "'.addslashes($_SERVER['REQUEST_URI']).'",
				IPBIN		= "'.addslashes(CURRENTIPBIN).'",
				STAMP		='.CURRENTSTAMP);
			return true;
		}
		return false;
	}
	return false;
}
