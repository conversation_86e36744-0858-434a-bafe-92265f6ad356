<?php

function process_quotes($body,$srcelem,$srcid,$parentid,$userid,$cstamp,$existing = false, bool $force = false) {
	if ($existing) {
		require_once '_sphinx.inc';
		sphinx_update_element($srcelem,$srcid);
	}

#	error_log('existing '.($existing ? 'YES' : 'no').', userid: '.$userid.', srcelem,srcid == '.$srcelem.','.$srcid,0);
	$srcpart = "PARENTID = $parentid
			AND USERID	 = $userid
			AND SRCELEM	 ='$srcelem'
			AND SRCID	 = $srcid";

	$flush_users = [];

	if ($existing) {
		$flush_users = db_same_hash('quoted','
			SELECT DISTINCT QOTUSERID
			FROM quoted
			WHERE '.$srcpart
		);
		if ($flush_users === false) {
			return false;
		}
		$haves = db_boolean_hash('quoted','
			SELECT DISTINCT QOTELEM,QOTID
			FROM quoted
			WHERE '.$srcpart
		);
		if ($haves === false) {
			return false;
		}
	}
	if (preg_match_all('"\[quote .*?\]"ums',$body,$matches)) {
		foreach ($matches[0] as $match) {
			if (!preg_match('"element=([a-z_]+)"u',$match,$elematch)
			||	!preg_match('"id=(\d+)"u',$match,$idmatch)
			) {
				continue;
			}
			$qotelem = $elematch[1];
			$useridname = 'USERID';
			switch ($qotelem) {
			case 'message':
			case 'flockmessage':
				$messageidname = 'MESSAGEID';
				$parentidname = 'TOPICID';
				break;
			case 'privatemessage':
			case 'directmessage':
				$qotelem = 'directmessage';
				$messageidname = 'MESSAGEID';
				$parentidname = 0;
				$useridname = 'FROM_USERID';
				break;
			case 'contact_ticket_message':
				$messageidname = 'CTMSGID';
				$parentidname = 'TICKETID';
				$useridname = 'USERID_FROM';
				break;
			default:
				require_once '_comment.inc';
				if (!($parent = is_comment_table($qotelem))) {
					error_log('unsupported quoted element "'.$qotelem.'" in '.$srcelem.':'.$srcid,0);
					continue 2;
				}
				$messageidname	= 'COMMENTID';
				$parentidname = 'ID';
				break;
			}
			$qot = db_single_array($qotelem,'SELECT '.$useridname.' AS USERID,CSTAMP,'.$parentidname.' AS PARENTID FROM '.$qotelem.' WHERE '.$messageidname.'='.($qotid = $idmatch[1]));
			if ($qot === false) return false;
			if (!$qot) {
				if (isset($haves)) {
					unset($haves[$qotelem][$qotid]);
				}
				continue;
			}
			$havequotes = true;

			[$qotuserid, $qotstamp, $qotparentid] = $qot;

			if ($force !== false
			||	!isset($haves[$qotelem][$qotid])
			) {
				$haves[$qotelem][$qotid] = '('.
					$cstamp.','.
					$qotstamp.','.
					$userid.',"'.
					$srcelem.'",'.
					$srcid.','.
					$parentid.','.
					$qotuserid.',"'.
					$qotelem.'",'.
					$qotid.','.
					$qotparentid.
				')';

				$flush_users[$qotuserid] = $qotuserid;

			} elseif ($haves[$qotelem][$qotid] === true) {
				$haves[$qotelem][$qotid] = false;
			}
		}
	}
	if ($flush_users) {
		require_once '_quote.inc';
		register_shutdown_function('flush_new_quotes',$flush_users);
	}
	if (!isset($havequotes)) {
		if (!empty($haves)) {
			if (!db_delete('quoted','DELETE FROM quoted WHERE '.$srcpart)) {
				return false;
			}
		}
		return true;
	}
	if (!empty($haves)) {
		foreach ($haves as $have_qotelem => $have_qotids) {
			foreach ($have_qotids as $have_qotid => $remove) {
				if ($remove === true || $force !== false) {
					$remove_these[] = '(QOTELEM="'.$have_qotelem.'" AND QOTID='.$have_qotid.' AND PARENTID='.$parentid.')';
				}
				if (is_string($remove)) {
					$addthese[] = $remove;
				}
			}
		}
		if (isset($remove_these)) {
			if (!db_delete('quoted','
				DELETE FROM quoted
				WHERE '.$srcpart.'
				  AND ('.implode(' OR ',$remove_these).')')
			) {
				return false;
			}
		}
		if (isset($addthese)) {
			if (!db_insert('quoted','
				INSERT IGNORE INTO quoted (STAMP,QOTSTAMP,USERID,SRCELEM,SRCID,PARENTID,QOTUSERID,QOTELEM,QOTID,QOTPARENTID)
				VALUES '.implode(',',$addthese))
			) {
				return false;
			}
		}
	}
	return true;
}
