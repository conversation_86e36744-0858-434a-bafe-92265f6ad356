<?php

function remove_albummap_and_contents(int $mapid, ?int $albumid = null, bool $go_down = true): bool {
	require_once '_albummap.inc';
	require_once '_albumelementremove.inc';
	ignore_user_abort(true);
	if (!$albumid
	&&	(require_once '_album.inc')
	&&	!($albumid = clean_get_albumid_from_mapid($mapid))
	||	!require_album_not_in_use($albumid)
	||	!require_getlock('remove_albummap:'.$mapid, 5)
	) {
		return false;
	}
	$ok = __remove_albummap_and_contents($mapid, $albumid, $go_down);
	db_releaselock('remove_albummap:'.$mapid);
	return $ok;
}
function __remove_albummap_and_contents(int $mapid, int $albumid, bool $go_down): bool {
	if ($go_down) {
		require_once '_album.inc';
		if (false === ($mapids = get_all_innermaps($albumid, $mapid))) {
			return false;
		}
	} else {
		$mapids = [$mapid];
	}
	if (!db_insert('remove_albummap_debug', "
		INSERT INTO remove_albummap_debug SET
			USERID	= $albumid,
			STAMP	= UNIX_TIMESTAMP(),
			BODY	= '".CURRENTSTAMP." __remove_albummap_and_contents($mapid, $albumid, $go_down): mapids from get_all_innermaps: ".implode(', ', $mapids)."'")
	||	!($debug_id = db_insert_id())
	||	!db_insert('albummap_for_context', "
		INSERT INTO albummap_for_context
		SELECT *, $debug_id, UNIX_TIMESTAMP()
		FROM albummap
		WHERE ALBUMID = $albumid")
	||	!db_insert('albumelement_for_context', "
		INSERT INTO albumelement_for_context
		SELECT *, $debug_id
		FROM albumelement
		WHERE ALBUMID = $albumid")
	) {
		return false;
	}	
	$inslist = ["($albumid, $mapid, ".CURRENTSTAMP.', 0)'];

	foreach ($mapids as $remove_mapid) {
		if ($remove_mapid === $mapid) {
			$found = true;
			continue;
		}
		$inslist[] = "($albumid, $remove_mapid, ".CURRENTSTAMP.', 1)';
	}
	if (!isset($found)) {
		mail_log("map $mapid not in innermaps");
		register_error('albummap:error:missing_map_building_removelist_LINE', ['MAPID' => $mapid]);
		return false;
	}
	$mapidstr = '('.implode(', ', $mapids).')';
	if (false === ($albumelementids = db_simpler_array('albumelement', "
		SELECT ALBUMELEMENTID
		FROM albumelement
		WHERE MAPID IN $mapidstr",
		DB_USE_MASTER))
	) {
		return false;
	}
	if ($inslist
	&&	!db_insert('albummap_remove_log','
		INSERT IGNORE INTO albummap_remove_log (ALBUMID, MAPID, DSTAMP, AUTO)
		VALUES '.implode(', ', $inslist))
	) {
		return false;
	}
	foreach ($albumelementids as $albumelementid) {
		if (!remove_albumelement($albumelementid)) {
			return false;
		}
	}
	return db_insert('albummap_todeleted','
		INSERT IGNORE INTO albummap_tobedeleted
		SELECT *, '.CURRENTSTAMP."
		FROM albummap
		WHERE MAPID IN $mapidstr")
	&&	db_delete('albummap', "
		DELETE FROM albummap
		WHERE MAPID IN $mapidstr");
}
