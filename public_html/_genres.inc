<?php

function get_genres(?int $gid = null): string|array|false {
	static $__genres = db_simple_hash('genre', 'SELECT GID, NAME FROM party_db.genre ORDER BY NAME') ?: [];
	if ($gid !== null) {
		/** @noinspection OffsetOperationsInspection */
		return $__genres[$gid] ?? false;
	}
	return $__genres;
}

/**
 * @return array<int,string>|false
 */
function get_available_genres(?string $element = null): array|false {
	$element ??= $_REQUEST['sELEMENT'];
	static $__genres;
	if (isset($__genres[$element])) {
		return $__genres[$element];
	}
	switch ($element) {
	case 'music':
		$genres = memcached_simple_hash(['music', 'genre'],'
			SELECT DISTINCT GID, genre.NAME
			FROM party_db.music
			JOIN party_db.genre USING (GID)
			WHERE GID != 0
			ORDER BY genre.NAME'
		);
		break;

	case 'review':
		break;
	case 'party':
	case 'agenda':
		require_once '_domain.inc';
		require_once '_party.inc';
		$genres = memcached_simple_hash(['party', 'party_genre', 'genre', 'lineup', 'artist_genre'],'
			SELECT GID, NAME, COUNT(DISTINCT PARTYID)
			FROM (
				SELECT DISTINCT party_genre.GID, PARTYID
				FROM party
			/*	'.join_only_events_for_current_country().'*/
				JOIN party_genre USING (PARTYID)
				WHERE STAMP_TZI > '.get_for_future_stamp_tzi().'
				  AND ACCEPTED = 1
				
				UNION

				SELECT DISTINCT party_genre_determined_v2.GID, PARTYID
				FROM party
			/*	'.join_only_events_for_current_country().' */
				JOIN party_genre_determined_v2 USING (PARTYID)
				WHERE STAMP_TZI > '.get_for_future_stamp_tzi().'
				  AND ACCEPTED = 1

/*				UNION

				SELECT DISTINCT artist_genre.GID, PARTYID
				FROM party
				'.join_only_events_for_current_country().'
				JOIN lineup USING (PARTYID)
				JOIN artist_genre USING (ARTISTID)
				LEFT JOIN party_genre USING (PARTYID)
				WHERE STAMP_TZI > '.get_for_future_stamp_tzi().'
				  AND ACCEPTED = 1
				  AND TYPE != "mc"
				  AND ISNULL(party_genre.GID)
*/

			) AS cmb
			JOIN genre USING (GID)
			GROUP BY GID
			ORDER BY NAME',
			ONE_DAY
		);
		break;
	}
	return $__genres[$element] = empty($genres) ? [] : $genres;
}

function get_genre_from_genre_in_url(string $genre_in_url, ?array $genres = null): ?array {
	# returns [gid, name, redirect_to]
	static $__result = [];
	if (array_key_exists($genre_in_url, $__result)) {
		return $__result[$genre_in_url];
	}
	$genres ??= get_genres();
	foreach ($genres as $gid => $name) {
		if (genre_for_url($name) === $genre_in_url) {
			return $__result[$genre_in_url] = [$gid, $name, null];
		}
	}
	static $__recognize = memcached_simple_hash('genre_recognize', "
		SELECT genre_recognize.NAME, genre.NAME
		FROM party_db.genre_recognize
		JOIN party_db.genre USING (GID)
		UNION
		SELECT REPLACE(REPLACE(NAME, '-', '') , ' ', ''), genre.NAME
		FROM party_db.genre
		WHERE NAME LIKE '% %'
		   OR NAME LIKE '%-%'"
	) ?: [];
	foreach ($__recognize as $name => $redirect_to) {
		if (genre_for_url($name) === $genre_in_url) {
			return $__result[$genre_in_url] = [null, null, genre_for_url($redirect_to)];
		}
	}
	return $__result[$genre_in_url] = null;
}

function genre_for_url(string $name): string {
	return	iconv(
			'UTF-8',
			'ASCII//TRANSLIT',
			strtolower(
				str_replace(
					[' ',	'&'],
					['-',	'and'],
					$name
			)));
}

function agenda_favourite_genres_count(): int {
	require_once '_genrelist.inc';
	if (!($favourite_genres = user_favourite_genres(CURRENTUSERID))
	||	false === ($avail = get_available_genres('agenda'))
	) {
		return 0;
	}
	$total = 0;
	foreach (array_intersect_key($avail, $favourite_genres) as /* $gid => */ $names_to_count) {
		$total += array_sum($names_to_count);
	}
	return $total;
}

function clear_determined_genres(int $partyid): void {
#	db_delete('party_genre_determined','DELETE FROM party_genre_determined WHERE PARTYID='.$partyid);
	db_replace('update_event_master_genres', 		 "REPLACE INTO update_event_master_genres SET PARTYID = $partyid, MSTAMP = ".CURRENTSTAMP);
	db_delete('party_genre_determined_v2', 			 "DELETE FROM party_genre_determined_v2 WHERE PARTYID = $partyid");
	db_delete('party_genres_for_determination', 	 "DELETE FROM party_genres_for_determination WHERE PARTYID = $partyid");
	db_delete('party_genres_for_determination_done', "DELETE FROM party_genres_for_determination_done WHERE PARTYID = $partyid");
	memcached_delete("gidetermined:v2:$partyid");
	memcached_delete("gidstats:v2:$partyid");
	require_once '_profile.inc';
	clear_element_profile('party', $partyid);
}

function get_determined_genres(int|array $arg): array|false {
	if (is_array($arg)) {
		sort($arg);
		$partyids = $arg;
		$key_party_part = hash('xxh128', implode(',', $arg));
	} else {
		$partyids = [$arg];
		$key_party_part = $arg;
	}
	$single_party = !isset($partyids[1]) ? $partyids[0] : false;
	$key = "gidstats:v2:$key_party_part";
	if (!isset($_REQUEST['NOMEMCACHE'])
	&&	($stats = memcached_get($key))
	) {
		return $stats;
	}
	if (!db_getlock($key, 5)) {
		return false;
	}
	if ($single_party) {
		if (false === ($stats = actual_get_determined_genres($partyids))) {
			db_releaselock($key);
			return false;
		}
		assert(is_array($stats)); # Satisfy EA inspection
		if (!empty($stats['for_display'])) {
			# want only genres with at least 10% artists
			$min_part = .1;
			$all_count = array_sum($stats['for_display']);

			foreach ($stats['for_display'] as $gid => $cnt) {
				$part = $cnt / $all_count;
				if ($part < $min_part) {
					unset($stats['for_display'][$gid]);
				}
			}
		}
	} else {
		$statss = [];
		foreach ($partyids as $partyid) {
			if (false === ($stats = get_determined_genres($partyid))) {
				db_releaselock($key);
				return false;
			}
			assert(is_array($stats)); # Satisfy EA inspection
			if (empty($stats['for_display'])) {
				continue;
			}
			$statss[] = $stats;
		}
		$grands = [];
		$all_count = 0;
		foreach ($statss as $stat) {
			foreach ($stat['for_display'] as $gid => $cnt) {
				increment_or_set($grands,$gid);
				++$all_count;
			}
		}
		arsort($grands);

		$stats = ['for_display' => []];
		$min_part = .2;
		$max_have = .7;
		$have = 0;

		foreach ($grands as $gid => $cnt) {
			$part = $cnt / $all_count;

#			print_rR($part,'gid '.db_single('genre','SELECT NAME FROM genre WHERE GID='.$gid));

			if ($part < $min_part
			&&	$have >= $max_have
			) {
				continue;
			}
			$have += $part;
			$stats['for_display'][$gid] = $cnt;
		}
	}
	memcached_set($key, $stats, ONE_DAY);
	db_releaselock($key);
	return $stats;
}

function actual_get_determined_genres(array $partyids): array|false {
	$partyid_str = implode(', ', $partyids);
	$single_party = !isset($partyids[1]) ? $partyids[0] : false;

	$updated = false;
	static $__updated = [];

	foreach ($partyids as $partyid) {
		$party = memcached_party_and_stamp($partyid);
		if (!$party) {
			if ($party === false) {
				return false;
			}
			# might be deleted party
			continue;
		}
		# NOTE: only update genres for future events!
		#	keep old events stable
		if (!isset($__updated[$partyid])
		&&	(	isset($_REQUEST['REDETERMINE'])
			||	false !== ($mstamp = db_single_int('party_genres_for_determination_done', "SELECT MSTAMP FROM party_genres_for_determination_done WHERE PARTYID = $partyid"))
			&&	$mstamp === null
			||	$mstamp
			&&	($max_lineup_modification_stamp = db_single_int('lineup', "SELECT MAX(MSTAMP) FROM lineup WHERE PARTYID = $partyid"))
			&&	$mstamp < $max_lineup_modification_stamp
			&&	$party['STAMP'] > CURRENTSTAMP
			)
		) {
			# only update if genres for determination have changed, or when algorhythm changes (just do a time compare to mstamp then)
			$updated = true;
			$__updated[$partyid] = true;
			if (!db_delete('party_genres_for_determination','
				DELETE FROM party_genres_for_determination
				WHERE PARTYID='.$partyid)
			) {
				return false;
			}
			store_genres_for_determination($party);
		}
	}
	if (!($areas = $single_party ?
		db_multirowuse_hash('party_genres_for_determination', "
		SELECT PARTYAREAID, GID, GIDCNT, IF(ARTISTWGIDCNT IS NULL, ARTISTCNT, ARTISTWGIDCNT) AS GROUPCNT, TYPE
		FROM party_genres_for_determination
		WHERE PARTYID IN ($partyid_str)
		  AND GID
		ORDER BY PARTYAREAID DESC, GIDCNT / GROUPCNT DESC")
	:	db_rowuse_array('party_genres_for_determination', "
		SELECT GID, SUM(GIDCNT) AS GIDCNT, TYPE, SUM(ARTISTWGIDCNT) AS GROUPCNT, SUM(GIDCNT) / SUM(ARTISTWGIDCNT) AS PART, SUM(GIDCNT / ARTISTWGIDCNT) AS PART2
		FROM party_genres_for_determination
		WHERE PARTYID IN ($partyid_str)
		  AND GID
		GROUP BY GID
		WITH ROLLUP")
	)) {
		if ($areas === false) {
			return false;
		}
		return [];
	}

	$stats = ['per_gid' => []];

	$area_cnt = count($areas);

	static $__cap = [
		1	=> 5,
		2	=> 4,
		3	=> 3,
	];

	$cap = getifset($__cap,$area_cnt) ?: 2;

	foreach ($areas as $partyareaid => $infos) {
		$have_gids = 0;
		$have = 0;
		$min_part = .2;
		$max_diff = 2;
		$max_have = .7;
		$prev_PART = null;
#		print_rr($partyareaid,'area');
		foreach ($infos as $info) {
			/** @noinspection PhpRedundantOptionalArgumentInspection */
			extract($info, \EXTR_OVERWRITE);

			increment_or_set($stats['per_gid'],$GID,$GIDCNT);

			$PART = $GIDCNT / $GROUPCNT;

#			if (HOME_OFFICE) {
#				$blurp[$partyareaid ? db_single('partyarea','SELECT IF(NAME,NAME,AREAID) FROM partyarea WHERE PARTYAREAID='.$partyareaid) : 0][$GID] = $PART;
#			}

			if ($have_gids >= $cap
			||	(	$PART < $min_part
				&&	$have >= $max_have
				)
			||	$prev_PART
			&&	$prev_PART / $PART > $max_diff
			#	^ if new genre fits more than twice in previous, ignore it (not important)
			) {
				continue;
			}
			$prev_PART = $PART;
			if ($single_party) {
				$stats['per_area'][$partyareaid][$GID] = $PART;
			}
			$have += $PART;
			++$have_gids;
		}
	}
#	print_rr($blurp);

	arsort($stats['per_gid']);

	if ($single_party
	&&	!empty($stats['per_area'])
	) {
		$unified_parts = [];
		foreach ($stats['per_area'] as /* $partyareaid => */ $gids) {
			foreach ($gids as $gid => $part) {
				$unified_parts[$gid][] = $part;
			}
		}
		uksort($unified_parts,static fn(int $gid_a, int $gid_b): int => $stats['per_gid'][$gid_b] <=> $stats['per_gid'][$gid_a]);

		$stats['unified_parts'] = $unified_parts;

		$setlist = [];
		foreach ($stats['unified_parts'] as $gid => $parts) {
			$cnt = $stats['per_gid'][$gid];
			$stats['for_display'][$gid] = $cnt;
			$setlist["($gid, $cnt)"] = "($single_party, $gid, $cnt)";
		}
		if ($updated
		&&	$setlist
		) {
			/** @noinspection NotOptimalIfConditionsInspection */
			if (!db_delete('party_genre_determined_v2', "
				DELETE FROM party_genre_determined_v2
				WHERE PARTYID = $single_party
				  AND (GID, CNT) NOT IN (".implodekeys(', ',$setlist).')')
			||	!db_insert('party_genre_determined_v2','
				REPLACE INTO party_genre_determined_v2 (PARTYID, GID, CNT)
				VALUES '.implode(', ',$setlist))
			||	!db_insert('update_event_master_genres',"
				REPLACE INTO update_event_master_genres SET
					PARTYID	= $single_party,
					MSTAMP	= ".CURRENTSTAMP)
			) {
				return false;
			}
		}
	}
	return $stats;
}

function store_genres_for_determination(array $party): bool {
	$partyid = $party['PARTYID'];
	if (false === ($have_non_parents = db_single(['connect','subof'], "
		SELECT 1
		FROM connect
		JOIN subof
		WHERE MAINTYPE			= 'party'
		  AND MAINID			= $partyid
		  AND ASSOCTYPE			= 'organization'
		  AND subof.PELEMENT	= 'organization'
		  AND subof.ELEMENT		= 'organization'
		  AND subof.CONCEPTID	= ASSOCID"))
	) {
		return false;
	}
	foreach ([
		'artist' =>
			db_simple_hash(['lineup', 'partyarea', 'artist_genre'], '
			SELECT PARTYAREAID, ARTISTID, COALESCE(GROUP_CONCAT(GID), 0)
			FROM lineup
			LEFT JOIN partyarea USING (PARTYID,AREAID)
			LEFT JOIN artist_genre USING (ARTISTID)
			WHERE PARTYID = '.$partyid.'
			GROUP BY PARTYAREAID, ARTISTID'
		),
		'organization' =>
			db_simple_hash(['connect', 'organization_genre'], '
			SELECT NULL, ORGANIZATIONID, COALESCE(GROUP_CONCAT(GID), 0)
			FROM connect
			JOIN organization_genre ON ORGANIZATIONID = ASSOCID
			'.($have_non_parents ? '
				LEFT JOIN subof
					   ON subof.ELEMENT  = "organization"
					  AND subof.PELEMENT = "organization"
					  AND subof.PARENTID = ASSOCID ' : '').'
			WHERE MAINTYPE = "party"
			  AND MAINID = '.$partyid.'
			  AND ASSOCTYPE = "organization"
			'.($have_non_parents ? ' AND subof.PARENTID IS NULL ' : '').'
			GROUP BY ORGANIZATIONID'
		),
		'location' =>
			db_simple_hash(['party', 'location_genre'], '
			SELECT NULL, LOCATIONID, COALESCE(GROUP_CONCAT(GID), 0)
			FROM party
			JOIN location_genre USING (LOCATIONID)
			WHERE PARTYID = '.$partyid.'
			GROUP BY LOCATIONID'
		),
		'host' =>
			db_simple_hash(['partyarea', 'organization_genre', 'connect'], '
			SELECT PARTYAREAID, ORGANIZATIONID, COALESCE(GROUP_CONCAT(GID), 0)
			FROM (
				SELECT PARTYAREAID, ORGANIZATIONID, GID
				FROM partyarea 
				JOIN organization_genre ON ORGANIZATIONID = HOSTEDBYID
				WHERE PARTYID = '.$partyid.'
				UNION
				SELECT NULL, ORGANIZATIONID, GID
				FROM connect
				JOIN organization_genre ON ORGANIZATIONID = ASSOCID
				WHERE MAINTYPE = "party"
				  AND MAINID = '.$partyid.'
				  AND ASSOCTYPE IN ("orgashost")
			) AS o
			GROUP BY PARTYAREAID, ORGANIZATIONID'
		)
	] as $element => $areas) {
		if ($areas === false) {
			return false;
		}
		if (empty($areas)) {
			continue;
		}
		$setlist = [];
		foreach ($areas as $partyareaid => $members) {
			$member_cnt = count($members);
			$memberwgid_cnt = 0;
			$gidcnts = [];
			foreach ($members as /* $memberid => */ $gidstr) {
				if ($gidstr) {
					foreach (explode(',',$gidstr) as $gid) {
						increment_or_set($gidcnts,$gid);
					}
					++$memberwgid_cnt;
				}
			}
			foreach ($gidcnts as $gid => $cnt) {
				$setlist[] =
					"($partyid,".
					($partyareaid === '' ? 'NULL' : $partyareaid).','.
					$gid.','.
					($cnt ?: 0).','.
					$member_cnt.','.
					$memberwgid_cnt.','.
					'"'.$element.'")';
			}
		}
		if ($setlist
		&&	!db_insert('party_genres_for_determination','
			INSERT INTO party_genres_for_determination (PARTYID, PARTYAREAID, GID, GIDCNT, ARTISTCNT, ARTISTWGIDCNT, TYPE)
			VALUES '.implode(',',$setlist))
		) {
			return false;
		}
	}
	return db_replace('party_genres_for_determination_done','
		REPLACE INTO party_genres_for_determination_done SET
			PARTYID	='.$partyid.',
			MSTAMP	='.CURRENTSTAMP);
}

const GENRES_SHOW_EXPLICIT	= 0b1;
const GENRES_SHOW_ESTIMATE	= 0b10;
const GENRES_SHOW_MASTER	= 0b100;
const GENRES_SHOW_ALL		= 0b111;

function show_genres(
	int		$partyid,
	array	$party,
	bool	$no_header	= false,
	?string $class		= null,
	string	$separator	= ', ',
	int		$show		= GENRES_SHOW_ALL,
): void {
	require_once 'defines/party.inc';
	if ($single_lineup = single_lineup_multi_days($partyid)) {
		$partyid = $single_lineup;
	}

	$show_estimate =
		($show & GENRES_SHOW_ESTIMATE)
	&&	(	have_admin('party')
		||	have_user()
		&&	$party['USERID'] === CURRENTUSERID
		);

	$gidstats = get_determined_genres($partyid);

	?><div class="block"><?

	require_once '_genrelist.inc';
	if ($styles = _genrelist_get_item_styles(
		'party',
		$partyid,
		$cnt,
		empty($gidstats['per_gid']) ? null : $gidstats['per_gid'],
		true,
		$separator,
	)) {
		?><div<?
		if ($class) {
			?> class="<?= $class ?>"<?
		}
		?>><?
		if (!$no_header) {
			?><b><?= Eelement_name('genre', $cnt) ?></b><br /><?
		}
		echo $styles;
		?></div><?
	}

	if ((	!$styles
		||	$show_estimate
		)
	&&	!empty($gidstats['for_display'])
	) {
		$show_genres = [];
		foreach ($gidstats['for_display'] as $gid => $cnt) {
			$show_genres[] = get_genre_link($gid).(have_admin() ? ' <small class="light">'.MULTIPLICATION_SIGN_ENTITY.' '.$cnt.'</small>' : '');
		}
		$class = '';
		$styles && $class .= 'light ';
		$class  && $class .= $class;
		?><div<?
		if ($class) {
			?> class="<?= $class ?>"<?
		}
		?>><?
		if (!$no_header) {
			?><b><?= Eelement_name('genre_estimation') ?></b><br /><?
		} else {
			?><span class="light"><?= element_name('estimation') ?>: </span><?
		}
		echo implode($separator, $show_genres);
		?></div><?
	}

	if ($master_genres
	=	(	($show & GENRES_SHOW_MASTER)
		&&	$show_estimate
		&&	(require_once '_profile.inc')
		&&	($profile = element_profile('party', $partyid))
		&&	!empty($profile['master_genres'])
		?	memcached_single('master_genre','
				SELECT GROUP_CONCAT(NAME ORDER BY NAME SEPARATOR ", ")
				FROM master_genre
				WHERE MGID IN ('.implodekeys(', ', $profile['master_genres']).')'
			)
		:	null
		)
	) {
		$setlist = [];
		foreach ($profile['master_genres'] as $mgid => $true) {
			$setlist[$mgid] = '('.$partyid.', '.$mgid.')';
		}
			db_delete('event_master_genre','
			DELETE FROM event_master_genre
			WHERE PARTYID = '.$partyid.'
			  AND MGID NOT IN ('.implodekeys(', ', $setlist).')')
		&&	db_insert('event_master_genre','
			INSERT IGNORE INTO event_master_genre (PARTYID, MGID)
			VALUES '.implode(', ', $setlist));

		?><div class="master-genre"><?
		?><span class="light"><?= element_name('master_genre', count($profile['master_genres'])) ?>:</span> <?=
			escape_specials($master_genres)
		?></div><?
	}
	?></div><?
}

function get_clean_genre(string $str): string {
	return str_replace(
			[' ', '-', '&', 'alternatieve'],
			['',  '',  '',  'alternative'],
			mb_strtolower($str)
	);
}

function get_explicit_genre(string $str): string|false|null {
	$str = get_clean_genre($str);
	return db_single('genre','
		SELECT NAME
		FROM genre
		WHERE REPLACE(REPLACE(REPLACE(NAME, " ", ""), "-", ""), "&", "") = "'.addslashes($str).'"'
	);
}

function get_genre_recognitions(): array|false|null {
	return db_simple_hash('genre_recognize', '
		SELECT genre_recognize.NAME, genre.NAME
		FROM party_db.genre_recognize
		JOIN party_db.genre USING (GID)'
	);
}

function recognize_genre(string $genre): ?string {
	static $__recognitions = get_genre_recognitions() ?: [];
	/** @var array<string, string> $__recognitions */
	return $__recognitions[$genre] ?? null;
}
