<?php

require_once 'defines/artist.inc';

function require_artist_countryid_or_empty(
	array &$array,
	string $field,
	bool   $utf8 = false,
): int|false {
	if (!require_anything($array, $field, utf8: $utf8, want_ascii: true)) {
		return false;
	}
	if (!$array[$field]) {
		return $array[$field] = 0;
	}
	if ($array[$field] === '-1') {
		return $array[$field] = -1;
	}
	return $array[$field] = require_idnumber($array, $field, utf8: $utf8);
}

function get_artist_realname(array $artist): string {
	return $artist[have_admin('artist') && $artist['REALNAME_FULL'] ? 'REALNAME_FULL' : 'REALNAME'];
}

function query_artists(string|array $where, int $stamp = 0, int $inputlen = 0): array|false|null {
	require_once '_namedoubles.inc';
	require_once '_sort.inc';

	$lastchange = memcached_get('lastartistnamechange') ?: 0;

	return memcached_rowuse_hash(['artist', 'artistmember', 'lineup', 'user', 'country', 'itemname', 'alternate_name', 'party'], /** @lang MariaDB */ '
		SELECT	ARTISTID,artist.CSTAMP,NEEDUPDATE,artist.MSTAMP,artist.NAME,GENDER,IDENT,TYPE,REALNAME,REALNAME_FULL,ORDER_NAME,
			ACCEPTED, artist.DELETED, '.get_deceased_part($stamp).',artist.COUNTRYID,LIVE_COUNTRYID,FOLLOWUPID,
			IF(FOLLOWUPID,(SELECT MIN(STAMP) FROM lineup JOIN party USING (PARTYID) WHERE ARTISTID=FOLLOWUPID),NULL) AS FOLLOW_FIRST,
			(SELECT GROUP_CONCAT(NAME SEPARATOR "'."\x1F".'") FROM alternate_name WHERE ELEMENT="artist" AND ID=artist.ARTISTID) AS AUTO_RENAMES,
			(SELECT GROUP_CONCAT(NAME SEPARATOR ", ") FROM itemname WHERE ID=artist.ARTISTID AND ELEMENT="artist") AS OLD_NAMES,
			COALESCE(
				(	SELECT MAX(STAMP)
					FROM party
					JOIN lineup AS llast USING (PARTYID)
					WHERE CANCELLED = 0
					  AND MOVEDID = 0
					  AND STAMP<'.($stamp ?: CACHESTAMP).'
					  AND llast.ARTISTID = artist.ARTISTID
				),0
			) AS PREV_STAMP,
			(	SELECT CONCAT(STAMP,",",PARTYID)
				FROM party
				JOIN lineup USING (PARTYID)
				WHERE ARTISTID = artist.ARTISTID
				  AND STAMP<'.($stamp ?: CACHESTAMP).'
				ORDER BY STAMP DESC
				LIMIT 1
			) AS LSTAMP,
			
			IF(artist.COUNTRYID!=-1,country.SHORT,(
				SELECT GROUP_CONCAT(DISTINCT SHORT)
				FROM user
				JOIN artistmember USING (USERID)
				JOIN country USING (COUNTRYID)
				WHERE ID = ARTISTID)) AS SHORT,
			live_country.SHORT AS LIVE_SHORT,
			(SELECT GROUP_CONCAT(GID) FROM artist_genre JOIN genre USING (GID) WHERE artist_genre.ARTISTID = artist.ARTISTID) AS ARTIST_GENRES
		FROM artist
		LEFT JOIN country ON country.COUNTRYID = artist.COUNTRYID
		LEFT JOIN country live_country ON live_country.COUNTRYID=LIVE_COUNTRYID
		WHERE '.($uniq = is_array($where) ? implode(' OR ',$where) : $where),
		key: 'query_artists_v7:'.$lastchange.':'.$stamp.':'.hash('xxh128', $uniq)
	);
}

function show_artist_option(
	array			 $artist,
	?bool			&$addempty		 = null,
	array|bool|null &$selectedartist = null,
	int				 $stamp			 = 0,
	bool			 $disabled		 = false,
): void {
	if ($artist['DELETED']
	&&	!have_admin('artist')
	) {
		# hide deleted options
		return;
	}
	if (!$stamp) {
		$stamp = CURRENTSTAMP;
	}
	?><option<?
	if ($selectedartist) {
		if (is_bool($selectedartist)
		?	$selectedartist
		:	$selectedartist === $artist['ARTISTID']
		) {
			?> selected<?
		}
	} elseif (
		$disabled
	||	!empty($artist['DISABLED'])
	||	(	!isset($artist['ALLOW'])
		&&	(	$artist['DECEASED']
			||	$artist['STOPPED']
			||	isset($artist['DISABLED'])
			)
		)
	) {
		?> disabled<?
		if (isset($artist['BESTFIT'])) {
			$addempty = true;
		}

	} elseif (
		$stamp !== false
	&&	!empty($artist['FOLLOW_FIRST'])
	&&	(!$stamp || $artist['FOLLOW_FIRST'] < $stamp)
	) {
		?> disabled<?
		$parts[] = __('field:succeeded');

	} elseif (isset($artist['BESTFIT'])) {
		$selectedartist = $artist;
		?> selected<?

	} elseif ($artist['DELETED']) {
		?> disabled<?
	}
	?> value="<?= $artist['ARTISTID'] ?>"><?

	echo escape_utf8($artist['NAME']);

	show_dead_option($artist);

	if ($artist['DELETED']) {
		$parts[] = __('status:deleted');
	} elseif (
		isset($artist['ACCEPTED'])
	&&	!$artist['ACCEPTED']
	) {
		$parts[] = __('attrib:not_accepted');
	}

	if (isset($artist['IDINPUT'])) {
		$parts[] = 'ID: '.$artist['ARTISTID'];
	}
	$show_extra =
		($double = isset($artist['DOUBLE']) ? $artist['DOUBLE'] : 0)
	||	!str_contains($artist['TYPE'], 'artist');

	$show_extra = true;

	if ($show_extra) {
		if ($artist['SHORT']) {
			$parts[] = $artist['SHORT'].(!empty($artist['LIVE_SHORT']) && $artist['LIVE_SHORT'] != $artist['SHORT'] ? ' &rarr; '.$artist['LIVE_SHORT'] : null);
		} elseif (!empty($artist['LIVE_SHORT'])) {
			$parts[] = $artist['LIVE_SHORT'];
		}
	}
	if (!empty($artist['PREV_STAMP'])) {
		[$y, $m] = _getdate($artist['PREV_STAMP']);
		$parts[] = sprintf('%d-%02d', $y, $m);
	}
	if ($show_extra) {
		if (!empty($artist['CNTS'])) {
			# $parts[] = ($cnt = round($artist['CNTS'],1)).' '.element_name('performance', $cnt);
			$parts[] = ($cnt = round($artist['CNTS'],1)).MULTIPLICATION_SIGN_ENTITY;
		}
		if ($artist['GENDER']) {
			$parts[] = __('gender:'.($artist['GENDER'] === 'multi' ? 'mix' : $artist['GENDER']));
		}
		if ($artist['TYPE']) {
			foreach (explode(',', $artist['TYPE']) as $type) {
				require_once '_artist_types.inc';
				$types[] = get_artist_type($type, $artist['GENDER']);
			}
			if (isset($types)) {
				$parts[] = implode(' &amp; ',$types);
			}
		}
		if ($artist['IDENT']) {
			$parts[] = escape_utf8($artist['IDENT']);
		}
		if (!empty($artist['ARTIST_GENRES'])
		&&	($gids = int_explode(',', $artist['ARTIST_GENRES']))
		) {
			$genres = [];
			foreach ($gids as $gid) {
				$genres[] = memcached_genre($gid);
			}
			sort($genres);
			$parts[] = escape_utf8(implode(',', $genres));
		}
		if ($realname = get_artist_realname($artist)) {
			$parts[] = escape_utf8($realname);
		}
	}
	if ($artist['OLD_NAMES']) {
		$parts[] = __('date:past').': '.escape_utf8($artist['OLD_NAMES']);
	}
	if ($artist['AUTO_RENAMES']) {
		$renames = [];
		foreach (explode("\x1F",$artist['AUTO_RENAMES']) as $rename) {
			$renames[] = escape_utf8(make_clean_realname($rename, true));
		}
		$parts[] = __('attrib:also').': '.implode(', ',$renames);
	}

	if (isset($parts)) {
		?> (<?= implode(', ',$parts) ?>)<?
	}
	?></option><?
}

function get_deceased_part(int $stamp = 0): string {
	return $stamp
	?	'IF(	DECEASED=1
		AND	(	MAXDSTAMP
			AND	'.$stamp.' >= MAXDSTAMP
			OR	DECEASED_YEAR
			AND	UNIX_TIMESTAMP(CONCAT(
					DECEASED_YEAR,
				"-",	IF(DECEASED_MONTH,DECEASED_MONTH,12),
				"-",	IF(DECEASED_DAY,DECEASED_DAY,28)
				)) < '.$stamp.'
			),1,0
		) AS DECEASED,
		
		IF(	STOPPED=1
		AND	(	STOPYEAR
			AND	'.$stamp.' >= UNIX_TIMESTAMP(CONCAT(
					STOPYEAR,
				"-",	IF(STOPMONTH,STOPMONTH,12),
				"-",	IF(STOPDAY,STOPDAY,28)
				))
			OR	!STOPYEAR
			AND	MAXSSTAMP
			AND	'.$stamp.' >= MAXSSTAMP
			),1,0
		) AS STOPPED'
	:	'DECEASED,STOPPED';
}

function get_artist_choices(
	int|array $party,
	string	  $input,
	?int	 &$size			= null,		# will be filled with total number of choices
	int		  $include		= 40,		# include items with at least this pct/worth, 100 === perfect match
	?string	  $prefer_type	= null,
	int	 	  $max_age		= 8 * ONE_YEAR
): array|false {
	$stamp = is_scalar($party) ? $party : $party['STAMP'];

	$name = utf8_mytrim($input);
	$donames[] = [null, $name];
	$is_mc = false;
 	if (preg_match('"^\s*(MC|DJ|VJ)\s+(.*?)\s*$"iu', $name, $match)) {
		$is_mc = mb_strtolower($match[1]) === 'mc';
		$donames[] = $is_mc ? ['mc', $match[2]] : $match[2];
	}
	if (preg_match('"^\s*(?:(MC|DJ|VJ)\s+)?(?:the|da|das|een|de|het)\s+(.*?)\s*$"iu', $name, $match)) {
		$donames[] = $is_mc ? ['mc', $match[2]] : $match[2];
	}
	if (preg_match('"^\s*(.*?)\s*\((.*?)\)\s*$"iu', $name, $match)) {
		if ($match[1]) {
			$donames[] = [null, $match[1]];
		}
		$donames[] = [null, $match[2]];
	}
	require_once '_namefix.inc';
	$numberparts	 = [];
	$whereparts		 = [];
	$whereparts_real = [];

	foreach ($donames as $name) {
		$type = null;
		$binary = false;
		if (is_array($name)) {
			[$type, $actualname] = $name;
			$binary = !empty($name[2]);
			$name = $binary ? mb_strtolower($actualname) : $actualname;
		}
		if ($is_mc) {
			$type = 'mc';
		}

		$newname = get_fixed_name_lineup($name, utf8: true);

		$whereparts[$newname] =
			'('.	where_search_title('LOWER(artist.NAME)',$newname,($binary ? SEARCH_BINARY : 0) | WILD_BOTH | NAME_UTF8).
				($type ? ' AND FIND_IN_SET("'.$type.'",TYPE)' : null).
			')';

		$whereparts_real[$newname] =
			'('.	where_search_title('LOWER(artist.REALNAME)',$newname,($binary ? SEARCH_BINARY : 0) | WILD_BOTH | NAME_UTF8).
				($type ? ' AND FIND_IN_SET("'.$type.'",TYPE)' : null).
			')';

		if (($ids = is_number_list($name, '[,\s]+'))) {
			$numberparts += $ids;
		}
	}
	if (empty($whereparts)) {
		_error('no whereparts');
		return false;
	}

	if (false === ($moreids = db_simpler_array(['artist', 'alternate_name'], '
		SELECT ID
		FROM alternate_name AS artist
		JOIN artist AS real_artist ON artist.ID = real_artist.ARTISTID
		WHERE ELEMENT = "artist"
		  AND '.implode(' OR ', $whereparts)
	))) {
		return false;
	}

	if ($moreids) {
		$numberparts += $moreids;
	}

	if (false === ($moreids = db_simpler_array(['itemname', 'artist'], '
		SELECT ID
		FROM itemname AS artist
		JOIN artist AS real_artist ON ARTISTID=ID
		WHERE ELEMENT = "artist"
		  AND ENDSTAMP > '.($stamp ?: 0).'
		  AND '.implode(' OR ', $whereparts)
	))) {
		return false;
	}
	if ($moreids) {
		$numberparts += $moreids;
	}
	if ($numberparts) {
		$whereparts[null] = 'ARTISTID IN ('.implode(', ', $numberparts).')';
	}
	if (false === ($choices = query_artists($whereparts, $stamp, mb_strlen($input)))) {
		return false;
	}
	if (!$choices) {
		# try realname query if no results
		if (!($choices = query_artists($whereparts_real, $stamp, $max_age, mb_strlen($input))))  {
			$size = 0;
			return $choices === false ? false : [];
		}
	}

	if (is_array($party)) {
		$artists_in_description = db_same_hash(['facebook_description_link', 'facebook_link', 'fbid', 'fbids_in_description'], '
			SELECT DISTINCT ID
			FROM facebook_description_link
			JOIN facebook_link ON facebook_link.LINK = TRIM(TRAILING "/" FROM SUBSTRING(facebook_description_link.LINK, 26))
			JOIN fbid USING (FBID)
			WHERE PARTYID = '.$party['PARTYID'].'
			  AND ELEMENT = "artist"
			UNION
			SELECT DISTINCT fbid.ID
			FROM fbid
			JOIN fbids_in_description USING (FBID)
			JOIN fbid AS events ON events.FBID = fbids_in_description.EVENTID
			WHERE events.ID = '.$party['PARTYID'].'
			  AND events.ELEMENT = "party"
			  AND fbid.ELEMENT = "artist"'
		);
#		$okchoices = [];
		foreach ($choices as $artistid => &$choice) {
			if (isset($artists_in_description[$choice['ARTISTID']])
			||	(	$choice['LSTAMP']
				?	explode(',', $choice['LSTAMP'])[0]
				:	$choice['CSTAMP']
				) >= abs($stamp - 8 * ONE_YEAR)
			||	$choice['ARTISTID'] === (int)$input
			) {
#				$okchoices[$artistid] = $choice;
			} else {
				$choice['DISABLED'] = true;
			}
		}
		unset($choice);
	}

	$size = count($choices);
	$bestpct = 0;
	$lowerinput = preg_replace('"[\-\!\?]+"u', ' ', mb_strtolower($input));

#	$input = utf8_myrtrim($input, '.');
	$inputid = is_number($input) ? (int)$input : false;
	$ascii   = utf8_to_ascii($lowerinput);
	$find_mc = preg_match('"^mc\b"iu', $lowerinput);

	generate_name_doubles($choices, 'ORDER_NAME', 'artist', $stamp);

	$old_lineup = null;

	if (!empty($party['PROCESS_ACTION'])) {
		# caveat: we presume same name DJ will be same as one already in line-up if we're replacing

		switch ($party['PROCESS_ACTION']) {
		case 'replace_all':
		case 'replace_parts':
			# areaid not checked, entire old lineup used
			# allows for area suffling
			# edge: multiple artists with exact same name
			$old_lineup = db_boolean_hash('lineup','
				SELECT ARTISTID
				FROM lineup
				WHERE PARTYID = '.$party['PARTYID']
			);
			break;
		}
	}

	foreach ($choices as $artistid => &$choice) {
		if ($choice['TYPE']) {
			$types = explode_to_hash(',', $choice['TYPE']);
			sort_artist_types($types, $artistid, $is_mc ? 'mc' : null, $stamp);
			$choice['TYPE'] = implode(',', $types);
		}
		if (isset($old_lineup[$artistid])) {
			$choice['FOUNDINPREVIOUS'] = true;
			$pct = 110;

		} elseif ($inputid === $artistid) {
			$pct = 100;
			$choice['IDINPUT'] = true;

		} elseif (
			# NOTE: addding dot here, makes sure Rossi. is seen the same as Rossi, hopefully this works ok
			$input 	   === $choice['NAME']
		||	$input.'.' === $choice['NAME']
		) {
			# matches case also
			$pct = 108;

		} elseif (
			($lower_input = mb_strtolower($input))  === ($lower_choice = mb_strtolower($choice['NAME']))
		||	 $lower_input.'.'						=== $lower_choice
		) {
			$pct = 104;

		} else {
			$tests = [$choice['NAME']];
			if (isset($moreids[$artistid])) {
				$tests[] = $moreids[$artistid];
			}
			if (false !== mb_stripos($choice['NAME'], 'soundsystem')
			&&	false !== mb_stripos($choice['TYPE'], 'soundsystem')
			) {
				# soundsytem is used in some instances where flock does not
				$tests[] = utf8_mytrim(str_ireplace('soundsystem', '', $choice['NAME']));
			}
			$curpct = null;

			foreach ($tests as $i => $name) {
				if ($i) {
					$choice['ALIASES'][] = $name;
				}

				$name = utf8_myrtrim($name, '.');

				similar_text($input, $name, $pct);
				similar_text($lower_input, mb_strtolower($name), $newpct);

				if ($newpct > $pct) {
					$pct = $newpct;
				}
				if ($i) {
					# every secondary match a little less
					# suplied input of ae should give ae 100 and æ 99
					--$pct;
				}

				$lowername = preg_replace('"[\-\!\?\.\,]+"u', ' ', mb_strtolower($name));
				$asciiname = utf8_to_ascii($lowername);

				similar_text($lowerinput, $lowername, $newpct);

				if ($newpct > $pct) {
					$pct = $newpct;
				}

				if ($lowername !== $asciiname
				||	$name	   !== $ascii
				) {
					similar_text($ascii, $asciiname, $newpct);
					if ($newpct > $pct) {
						$pct = $newpct;
					}
				}
				if ($find_mc
				&&	preg_match('"\bmc\b"u', $choice['TYPE'])
				) {
					$lowerinput = preg_replace('"^mc\b"u', '', $lowerinput);
					similar_text($lowerinput, $lowername, $newpct);
					if ($newpct > $pct) {
						$pct = ($pct + $newpct) / 2;
					}
				}
				if ($curpct === null) {
					$curpct = $pct;

				} elseif ($curpct < $pct) {
					$curpct = $pct;
				}
			}
			$pct = $curpct;
		}

		if ($prefer_type
		&&	preg_match('"\b'.$prefer_type.'\b"i', $choice['TYPE'])
		) {
			$pct += 10;
		}

		if ($pct >= 95
		&&	isset($artists_in_description[$choice['ARTISTID']])
		) {
			# if almost perfect match, and in description, then add 10 points
			$pct += 10;
		}

		$choice['DST'] = $pct;
		# tmp to check deceaseds
		$choice['SRCHFOR'] = $name;

		if ($bestpct < $pct
		||	$bestpct === $pct
		&&	isset($choice['CNTS'])
		&&	(	!isset($bestchoice['CNTS'])
			||	$bestchoice['CNTS'] < $choice['CNTS']
			)
		) {
			$bestchoice = &$choice;
			$bestpct = $pct;
		}
	}
	unset($choice);

	$bestchoice['BESTFIT'] = true;

	reset($choices);

	if ($size <= 40) {
		return $choices;
	}

	if (!$include || $include >= 100) {
		return $choices;
	}

	$newchoices = [];
	foreach ($choices as $artistid => $choice) {
		if ($bestpct - $choice['DST'] < $include) {
			$newchoices[$artistid] = $choice;
		}
	}
	return $newchoices;
}

function sort_artist_types(
	# list should be associative array, with types as keys
	array   &$list,
	int		 $artistid,
	?string  $force = null,
	?int	 $stamp = null,
): array|false {
	$stamp ??= TODAYSTAMP;

	if (false === ($type_cnts = memcached_simple_hash(['lineup', 'party'],'
		SELECT TYPE, CAST(SUM( '.ONE_YEAR.' / POW(ABS( CAST(STAMP AS SIGNED) - '.$stamp.' ), 0.1)) AS UNSIGNED) AS IMPORTANCE
		FROM lineup
		JOIN party USING (PARTYID)
		WHERE ARTISTID = '.$artistid.'
		GROUP BY TYPE
		ORDER BY IMPORTANCE DESC',
		TEN_MINUTES,
	))) {
		return false;
	}

	$stats = [];

	foreach ($list as $type => $ignored) {
		$stats[$type] = $type_cnts[$type] ?? 0;
	}

	$prefer_dj = false;

	if (isset($type_cnts['dj'])
	&&	isset($type_cnts['act'])
	) {
		# prefer dj to act for dj's that can perform live
		$prefer_dj = true;
	}

	if ($artistid === 46888) {
		# workaround, doesn't mc a lot anymore
		$list = [
			'act'		=> $list['act']		?? null,
			'rapper'	=> $list['rapper']	?? null,
			'dj'		=> $list['dj']		?? null,
			'mc'		=> $list['mc']		?? null,
		];
		return $stats;
	}

	# sort on keys
	uksort($list, function(string $a, string $b) use ($type_cnts, $force, $prefer_dj): int {
		if ($force) {
			if ($a === $force) {
				return -1;
			}
			if ($b === $force) {
				return 1;
			}
		}
		if ($prefer_dj) {
			if ($a === 'act' && $b === 'dj') {
				return 1;
			}
			if ($b === 'act' && $a === 'dj') {
				return -1;
			}
		}
		return (int)(
				($type_cnts[$b] ?? 0) -
				($type_cnts[$a] ?? 0)
		);
	});

	return $stats;
}
