<?php

declare(strict_types=1);

echo 'HI!';

?><script src="/js/main.js"></script><?
?><script src="/js/form/validation.js"></script><?

require_once '_helper.inc';
require_once '_url.inc';
require_once '_find_ticket_uri.inc';
require_once '_site.inc';


?><form onsubmit="alert(submitForm(this));"><?
?><input type="text" data-valid="url_or_view_source" name="URL_OR_VIEW_SOURCE" size="30" /><?
?></form><?

exit;

$presale_setlist = [];
$_POST['PRESALE_WEBSITE'] = 'https://www.raveolution-event.nl/tickets';

?><pre><?

if ($_POST['PRESALE_WEBSITE']) {
	require_once '_presale.inc';
	require_once '_url.inc';

	$done_hosts = [];

	foreach (explode("\n", $_POST['PRESALE_WEBSITE']) as $site) {
		if (!($site = cleanup_url($site, true))) {
			continue;
		}
		if (preg_match('"^(?<url>https?://(?:'.
		# Generic links containing terms that indicate registration form
		'.+/(?:pre|voor)[_-]?registrati(?:e|on).*|'.
		'arep\.co/.+?(?:/finished)?|'.
		'docs\.google\.com/.+|'.
		# Google Docs shortened forms URL, indicating registration form
		'forms\.gle/.+|'.
		# JOTForm links indicating a registration form
		'form\.jotform(?:eu)?\.com/.+|'.
		# Typeform links indicating, again, a registration form
		'form\.typeform\.com/.+|'.
		# Mailchimp links indicating a registration form (for mailing list)
		'mailchi\.mp/.+|'.
		# Subscriber Signup Forms:
		'[a-z\d]+\.sibforms\.com/.*|'.
		# Form Builder seems like a form to me
		'www\.123formbuilder\.com/.+|'.
		# Supremacy event form:
		'\?members=register$'.
		'))$"', $site, $match)
		) {
			register_warning('party:warning:preregistration_or_other_form_link_LINE', ['SITE' => $site]);
			if ($_POST['PREREG_URL']) {
				if ($_POST['PREREG_URL'] !== $site) {
					register_warning('party:warning:preregistration_url_is_explicit_cannot_move_from_presale_url_LINE');
				}
				continue;
			}
			$_POST['PREREG_URL'] = $match['url'];;
			register_notice('party:notice:preregistration_or_other_form_link_moved_to_preregistration_field_LINE');
			continue;
		}
		# NOTE: TivoliVredenburg == Eventim
		if (str_contains($site, $host = 'tivolivredenburg.nl')) {
			$seller = 'eventim';
			$sites[$site] = $site;
			$done_hosts[$host] = true;
		}
		if (false !== mb_stripos($site, 'ticketswap')) {
			# EXAMPLE: https://www.ticketswap.com/event/vunzige-deuntjes-festival-2024/3fd4b75b-97f1-43f0-ad2b-f26365ab831f
			if (preg_match('"^https?://(?:www\.)?ticketswap\.com/event/[-\w]+/[a-f\d]{8}-[a-f\d]{4}-[a-f\d]{4}-[a-f\d]{4}-[a-f\d]{12}$"ui', $site)) {
				$presale_setlist['TICKETSWAP'] = $site;
				continue;
			}
			register_error('party:error:ticketswap_link_not_understood_LINE', ['LINK' => $site]);
			return false;
		}

		# recognize shorthands
		if (preg_match('"^(PAYLOGIC):(\d+)(?::(\d+))?$"iu', $site, $match)
		||	preg_match('"^(YOURTICKETPROVIDER):(\d+)$"iu', $site, $match)
		||	preg_match('"^(NOWONLINETICKETS):([a-fA-F\d]+)$"iu', $site, $match)
		||	preg_match('"^(IKBENAANWEZIG):(.+)$"iu', $site, $match)
		||	preg_match('"^(EVENTTICKETS):(\w+)$"iu', $site, $match)
		||	preg_match('"^(TICKETMASTER):(\d+)$"iu', $site, $match)
		) {
			$seller = strtolower($match[1]);
			switch ($match[1]) {	# NOSONAR
			case 'PAYLOGIC':
				$presale_setlist['PAYLOGICV2' ] = (int)$match[2];
				$presale_setlist['PAYLOGICPOS'] = (int)($match[3] ?? 0);
				break;

			case 'TICKETMASTER':
			case 'YOURTICKETPROVIDER':
				$presale_setlist[$match[1]] = (int)$match[2];
				break;

			case 'EVENTTICKETS':
			case 'IKBENAANWEZIG':
			case 'NOWONLINETICKETS':
				$presale_setlist[$match[1]] = $match[2];
				break;
			}
			continue;
		}
		[$site, $data_url] = find_actual_ticket_url($site, data: $data, info: $info, utf8: true);

		if (false !== mb_stripos($site, 'bit.ly')) {
			if (!isset($info['url'])) {
				warning('Kon bit.ly link volgen om werkelijke URL te bemachtigen!');
			} else {
				$site = $info['url'];
			}
		}
		# Club Rodenburg convert to direct frame link
		if (preg_match('"^https?://(?:www\.)?clubrodenburg\.nl/event/(?<event_id>\d+)"ui', $site, $match)) {
			$site = "https://www.clubrodenburg.nl/tickets/{$match['event_id']}";
		}
		if ($site[0] === '/') {
			register_warning('party:warning:no_presale_there_LINE', DO_UBB, ['SITE' => $site]);
			continue;
		}
		$allow_non_seller = false;

		$search_for_presale = static function(string &$site, ?string &$new_site = null) use (&$seller, &$allow_non_seller, &$presale_setlist, &$search_for_presale): bool {
			?></pre><?
			?><hr /><?
			?><pre><?
			echo "searching for presale in ", escape_utf8($site), "\n";
			# NOTE: Paylogic
			if (preg_match_all('"queue\.paylogic\.com/(?<event_id>\d+)/(?<shop_id>\d+)"u', $site, $matches)
			||	preg_match_all('"EventID\":\s*(?<event_id>\d+).*?PosID\":\s*(?<shop_id>\d+)"su', $site, $matches)
			||	preg_match_all('!\.paylogic\.\w{2,}/[^\'"]*?\bevent_id=(?<event_id>\d+)[^\'"]*?point_of_sale_id=(?<shop_id>\d+)!u', $site, $matches)
			||	preg_match_all('!\.paylogic\.\w{2,}/[^\'"]*?point_of_sale_id=(?<shop_id>\d+)[^\'"]*?event_id=(?<event_id>\d+)!u', $site, $matches)
			||	preg_match_all('!\.paylogic\.\w{2,}/[^\'"]*?\bevent_id=(?<event_id>\d+)!u', $site, $matches)
			) {
				$seller = 'paylogic';
				if (isset($matches['event_id'][1])) {
					register_warning('party:warning:too_many_presales_one_presale_site,could_not_choose_LINE', ['PROVIDER' => 'Paylogic']);
					$allow_non_seller = true;
				} else {
					$presale_setlist['PAYLOGICV2' ] = (int)$matches['event_id'][0];
					$presale_setlist['PAYLOGICPOS'] = (int)($matches['shop_id'][0] ?? 0);
				}
				return true;
			}
			if (preg_match_all('"\b(?<url>shop\.paylogic\.[a-z]{2,}/[a-f\d]+)"iu', $site, $matches)
			||	preg_match_all('"\b(?<url>https://tickets\.[^/]+/[a-f\d]{16})$"iu', $site, $matches)
			) {
				$seller = 'paylogic';
				if (isset($matches['url'][1])) {
					register_warning('party:warning:too_many_presales_one_presale_site,could_not_choose_LINE', ['PROVIDER' => 'Paylogic']);
					$allow_non_seller = true;
					return false;
				}
				$new_link = $matches['url'][0];
				$new_site = str_starts_with($new_link, 'http') ? $new_link : 'https://'.$new_link;
			}
			if (preg_match('"\.paylogic\.[a-z]{2,}/flows/\w+/"iu', $site)) {
				$seller = 'paylogic';
				if (!isset($presale_setlist['PAYLOGICV2'])) {
					if ($data = safe_file_get_contents($site)) {
						if (preg_match('"event_id=(?<event_id>\d+)&amp;point_of_sale_id=(?<shop_id>\d+)"u', $data, $match)
						||	preg_match('"EventID\":\s*(?<event_id>\d+).*?PosID\":\s*(?<shop_id>\d+)"su', $data, $match)
						||	preg_match('"event_id[^:]*:[^u]*u0022(?<event_id>\d+).*?point_of_sale_id[^:]*:[^u]*u0022(?<shop_id>\d+)"u', $data, $match)
							# ^^ problem matching \u0022, workaround
						) {
							$presale_setlist['PAYLOGICV2' ] = (int)$match['event_id'];
							$presale_setlist['PAYLOGICPOS'] = (int)$match['shop_id'];
							return true;
						}
						if (preg_match('!event_id"\s+value="(?<event_id>\d+)"!u', $data, $match)) {
							$presale_setlist['PAYLOGICV2' ] = (int)$match['event_id'];
							$presale_setlist['PAYLOGICPOS'] = 0;
							register_warning('presale:warning:check_paylogic_might_not_work_LINE');
							return true;
						}
					}
					register_warning('presale:warning:within_session_LINE', DO_UBB, ['SITE' => $site]);
				}
				return true;
			}
			# NOTE: Tibbaa
			if (preg_match_all('"(?<url>http.*?\btibbaa\.com/order/[a-z\d]+)"iu', $site, $matches)
			||	preg_match_all('"\b(?<url>https?://tibbaa\.com/events/listing/\w+)"iu', $site, $matches)
			||	preg_match_all('!src="(?<url>https?://tibbaa\.com/iframes/show/\w+)!iu', $site, $matches)
			) {
				$seller = 'tibbaa';
				if (isset($matches['url'][1])) {
					register_warning('party:warning:too_many_presales_one_presale_site,could_not_choose_LINE', ['PROVIDER' => 'Tibbaa']);
					$allow_non_seller = true;
				} else {
					$new_site = 'https://tibbaa.com/order/'.$matches['url'][0];
				}
				return false;
			}
			# NOTE: Event-Tickets
			if (preg_match_all('"\bevent-tickets\.be/Event/(?<event_id>\w+)"iu', $site, $matches)) {
				$seller = 'eventtickets';
				if (isset($matches['event_id'][1])) {
					register_warning('party:warning:too_many_presales_one_presale_site,could_not_choose_LINE', ['PROVIDER' => 'Event-tickets']);
					$allow_non_seller = true;
					return false;
				}
				$presale_setlist['EVENTTICKETS'] = $matches['event_id'][0];
				return true;
			}
			# NOTE: TicketMaster / TicketService
			if (preg_match_all('!https://[^/]*?ticket(?:master|service)\.nl[^"]*?\beventId=(?<event_id>\d+)!iu', $site, $matches)
			||	preg_match_all('!href="[^"]*\bticketmaster\.nl/event/(?:[^"]*?/)?(?<event_id>\d+)\b!iu', $site, $matches)
			) {
				$seller = 'ticketmaster';
				if (isset($matches['event_id'][1])) {
					register_warning('party:warning:too_many_presales_one_presale_site,could_not_choose_LINE', ['PROVIDER' => 'Event-tickets']);
					$allow_non_seller = true;
					return false;
				}
				$presale_setlist['TICKETMASTER'] = (int)$matches['event_id'][0];
				return true;
			}
			# NOTE: YourTicketProvider
			# Legacy using storage of only ID:
			if (preg_match('"\byourticketprovider\.nl(?:/events/(\d+)|.*?productid=(\d+))(?:$|\")"u', $site, $match)) {
				$seller = 'yourticketprovider';
				$id = $match[empty($match[1]) ? 2 : 1];
				$presale_setlist['YOURTICKETPROVIDER'] = (int)$id;
				return true;
			}
			# <iframe frameborder="0" height="550" scrolling="yes" src="https://shop.yourticketprovider.nl/cd737ec0-f32c-d3e4-7938-a83fe97b9986" width="100%" class="normalIframe"></iframe>
			if (preg_match_all('!<iframe.*?src="(?<url>https://shop\.yourticketprovider\.nl/[a-f\d]{8}-[a-f\d]{4}-[a-f\d]{4}-[a-f\d]{4}-[a-f\d]{12})"!', $site, $matches)) {
				$seller = 'yourticketprovider';
				if (isset($matches['url'][1])) {
					register_warning('party:warning:too_many_presales_one_presale_site,could_not_choose_LINE', ['PROVIDER' => 'YourTicketProvider']);
					$allow_non_seller = true;
					return false;
				}
				$new_site = $matches['url'][0];
				return false;
			}
			# NOTE: Find YourTicketProvider iframe starting with shop. and check if we can deduce the ticketseller
			# <iframe id="ytp-widget-frame" src="https://shop.thuishaven.nl/12ec030d-8397-7ff5-2a2d-c07da924dda6" width="100%" style="border: none; overflow: auto"></iframe>
			if (preg_match('!iframe[a-z\d\s_+="]+?id="ytp-widget-frame"[a-z\d\s_+="]+?src="(?<shop_url>https?://shop\.[^"]+)"!iu', $site, $match)) {
				[$site /*, $data_url */] = find_actual_ticket_url($match['shop_url'], data: $site, utf8: true);
				#return $search_for_presale($match['shop_url'], $new_site);
				$new_site = $site;
				return false;
			}
			# NOTE: NowOnlineTickets
			if (preg_match('"nowonlinetickets\.nl/.*?(?<event_id>[A-F0-9]{32})"iu', $site, $match)) {
				$seller = 'nowonlinetickets';
				$presale_setlist['NOWONLINETICKETS'] = $match['event_id'];
				return true;
			}
			# NOTE: Chipta
			if (preg_match('!src="(?<url>https://iframeshop\.chipta\.com[^"]*)"!iu', $site, $match)) {
				$seller = 'chipta';
				$new_site = $match['url'];
				return false;
			}
			# NOTE: Eventbrite
			if (preg_match_all('!<iframe[^>]*src="(?<url>https?://(?:www\.)?eventbrite\..*?)"!iu', $site, $matches)) {
				$seller = 'eventbrite';
				if (isset($matches['url'][1])) {
					register_warning('party:warning:too_many_presales_one_presale_site,could_not_choose_LINE', ['PROVIDER' => 'Eventbrite']);
					$allow_non_seller = true;
				} else {
					$new_site = $matches['url'][0];
				}
				return false;
			}
			# NOTE: CM
			# <iframe class="wuksD5" title="Embedded Content" name="htmlComp-iframe" width="100%" height="100%" data-src="" src="https://store.ticketing.cm.com/raveolution20251129"></iframe>
			if (preg_match_all('!<iframe[^>]*src="(?<url>https?://(?:store\.)?(?:ticketing\.)?cm\.com[^"]*)"!iu', $site, $matches)) {
				$seller = 'cm';
				if (isset($matches['url'][1])) {
					register_warning('party:warning:too_many_presales_one_presale_site,could_not_choose_LINE', ['PROVIDER' => 'CM.com']);
					$allow_non_seller = true;
				} else {
					$new_site = $matches['url'][0];
				}
				return false;
			}
			# NOTE: GUTS
			if (preg_match_all('!<iframe[^>]*src="(?<url>https?://(?:(?:app|widget)\.)?guts\.(?:events|tickets)/[a-z\d])$"!iu', $site, $matches)) {
				$seller = 'guts';
				if (isset($matches['url'][1])) {
					register_warning('party:warning:too_many_presales_one_presale_site,could_not_choose_LINE', ['PROVIDER' => 'GUTS']);
					$allow_non_seller = true;
				} else {
					$new_site = $matches['url'][0];
				}
				return false;
			}
			if (preg_match_all('"\beventbrite\.[a-z]{2,}/(?:tickets|checkout)-external\?eid=(?<event_id>\d+)"iu', $site, $matches)
			||	preg_match_all('"\beventbrite\.[a-z]{2,}/e/(?:[\w\-]+-)?(?<event_id>\d+)$"iu', $site, $matches)
			||	preg_match_all('"(?:iframeContainerId|modalTriggerElementId):\h*\'eventbrite[^\']*?-(?<event_id>\d+)\'"iu', $site, $matches)
			) {
				$seller = 'eventbrite';
				if (isset($matches['event_id'][1])) {
					register_warning('party:warning:too_many_presales_one_presale_site,could_not_choose_LINE', ['PROVIDER' => 'Eventbrite']);
					$allow_non_seller = true;
				} else {
					$new_site = 'https://www.eventbrite.nl/e/'.$matches['event_id'][0];
				}
				return false;
			}
			# NOTE: Stager
			# <iframe src="https://so-what.stager.nl/web/tickets/170565?wmode=opaque"
			if (preg_match_all('!<iframe[^>]*?src="(?<url>https://[^/]*?\.stager\.nl/[^"]*)"!u', $site, $matches)
			||	preg_match_all('!<iframe src="(?<url>https://[^/]+\.stager\.nl/web/tickets/\d+)!iu', $site, $matches)
			||	preg_match_all('!data-ticket="(?<url>[^"]+)"!iu', $site, $matches)
			) {
				if (!str_contains($matches['url'][0], 'stager')) {
					mail_log('no stager URL found but did match', get_defined_vars());
				} else {
					$seller = 'stager';
					if (isset($matches['url'][1])) {
						register_warning('party:warning:too_many_presales_one_presale_site,could_not_choose_LINE', ['PROVIDER' => 'Stager']);
						$allow_non_seller = true;
					} else {
						$new_site = $matches['url'][0];
					}
					return false;
				}
			}
			# NOTE: Eventix
			if (preg_match_all('!data-url="(?<url>https://shop\.eventix\.io/[\da-f]+-[\da-f]+-[\da-f]+-[\da-f]+-[\da-f]+)!iu', $site, $matches)
			# <a href="https://eventix.shop/fuf4qrsg">deze link</a>
			||	preg_match_all('!\b(?:src|href)="(?<url>https://eventix\.shop/[a-zA-Z\d]+)"!iu', $site, $matches)
			# id="shop-frame" style="max-width: 600px; margin: 0 auto;" data-url="//shop.eventix.io/76f27d50-e801-11e6-ac6d-d70c9443cd7d
			||	preg_match_all('!data-url="(?<url>//shop\.eventix\.io/[a-f\d\-]+)"!iu', $site, $matches)
			) {
				$seller = 'eventix';
				if (isset($matches['url'][1])) {
					register_warning('party:warning:too_many_presales_one_presale_site,could_not_choose_LINE', ['PROVIDER' => 'Eventix']);
					$allow_non_seller = true;
				} else {
					$new_site = ($matches['url'][0][0] === '/' ? 'https:' : '').$matches['url'][0];
				}
				/** @noinspection RedundantReturnPointInspection */
				return false;
			}
			return false;
		};
		if ($search_for_presale($site, $newsite)) {
			continue;
		}
		if (!$allow_non_seller) {
			if (!empty($newsite)
			&&	$site !== $newsite
			) {
				$site = $newsite;
			} elseif ($data = safe_file_get_contents($data_url ?: $site)) {
				file_put_contents('/tmpdisk/data.html', $data);
				$newsite = null;
				if (str_contains($data, 'shopping-api.paylogic.com')) {
					$seller = 'paylogic';
				} elseif (str_contains($data, 'stager-ticketshop')) {
					$seller = 'stager';
				} elseif (preg_match('!agenda-sidebar__buy-button.*?href="(?<bitly_link>https://bit.ly/[^"]+)"!', $data, $match)) {
					[$newsite] = find_actual_ticket_url($match['bitly_link']);
				}
				if (!$newsite
				&&	$search_for_presale($data, $newsite)
				) {
					continue;
				}
				if (!empty($newsite)) {
					$site = $newsite;
				}
			}
		}
		if (!$site) {
			mail_log('party.commit: site for presale is empty');
			continue;
		}
		$base = base_url($site);
		if (!$allow_non_seller
		&&	isset($skip_urls[$base])
		) {
			register_warning('party:warning:need_direct_link_to_presale_site_LINE', DO_UBB, ['SITE' => $site]);
			continue;
		}
		if (is_bad_presale_site($site)
		# no amsterdamnightlifeticket.com tickets on other events!
		||	str_contains($site, 'amsterdamnightlifeticket.com')
		&&	(	!$all_orgs
		||	!in_array(16805, $all_orgs, true)
		)
		) {
			register_warning('party:warning:presale_site_not_supported_LINE', DO_UBB, ['SITE' => $site]);
			continue;
		}
		$host = parse_url($site, PHP_URL_HOST);
		if (!isset($done_hosts[$host])) {
			$done_hosts[$host] = true;
			$sites[$site] = $site;
		}
	}
}
?></pre><?

print_rr($presale_setlist);
