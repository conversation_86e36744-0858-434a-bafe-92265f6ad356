<?php

function item_set_accept(string|bool $arg): bool {
	require_once '_lock.inc';
	$accept = is_bool($arg) ? $arg : ($arg === 'accept');

	return	require_admin($element = $_REQUEST['sELEMENT'])
		&&	($id = require_idnumber($_REQUEST, 'sID'))
		&&	(	!($lock_type = get_lock_type_for_element($element))
			||	require_last_or_no_lock($lock_type, $id)
			)
		&&	set_accept($element, $id, $accept);
}

function set_accept(string $element, int $id, bool $accept): bool {
	if (false !== actual_set_accept($element, $id, $accept)
	||	HOME_THOMAS
	) {
		require_once '_http_status.inc';
		store_messages_in_cookie();
		see_other(get_element_href($element, $id));
	}
	return false;
}

function actual_set_accept(string $element, int $id, bool $accept): ?bool {
	$extra_setlist = [];

	if ($element === 'party'
	&&	$accept
	) {
		if (false === ($once_accepted = db_single('party_log', '
			SELECT 1
			FROM party_log
			WHERE ACCEPTED
			  AND PARTYID = '.$id)
		)) {
			return false;
		}
		$extra_setlist[] = 'LAST_VALIDATED = '.CURRENTSTAMP;
		if (!$once_accepted) {
			$extra_setlist[] = 'FIRST_VALIDATED = '.CURRENTSTAMP;
			require_once '_needupdates.inc';
			partylist_needs_refresh('new');
		}
	}

	$new_accepted = $accept ? "b'1'" : "b'0'";

	if (!db_insert($element.'_log', "
		INSERT INTO {$element}_log
		SELECT * FROM `$element`
		WHERE {$element}ID = $id
		  AND ACCEPTED != $new_accepted")
	) {
		return false;
	}
	if (!db_affected()) {
		return null;
	}
	if (!db_update($element, "
		UPDATE `$element` SET
			ACCEPTED = $new_accepted,
			MUSERID	 = ".CURRENTUSERID.',
			MSTAMP	 = '.CURRENTSTAMP.
			($extra_setlist ? ', '.implode(', ', $extra_setlist) : '')."
		WHERE {$element}ID = $id
		  AND ACCEPTED != $new_accepted")
	) {
		return false;
	}

	register_notice('item:notice:'.($accept ? 'accepted' : 'unaccepted').'_LINE', DO_UBB, ['ELEMENT' => $element, 'ID' => $id]);
	require_once '_pagechanged.inc';
	page_changed($element, $id);
	require_once '_needupdates.inc';
	flush_needupdates($element);

	switch ($element) {
	case 'party':
		flush_party($id);

		if ($info = db_single_array(['party', 'lineup', 'connect'], "
			SELECT	LOCATIONID,
				BOARDINGID,
				GROUP_CONCAT(DISTINCT ASSOCID),
				GROUP_CONCAT(DISTINCT ARTISTID)
			FROM party
			LEFT JOIN lineup USING (PARTYID)
			LEFT JOIN connect
				   ON MAINTYPE = 'party'
				  AND MAINID = PARTYID
				  AND ASSOCTYPE = 'organization'
			WHERE PARTYID = $id
			GROUP BY PARTYID")
		) {
			[$locationid, $boardingid, $organizationidstr, $artistidstr] = $info;
			if ($locationid) {
				$args[] = 'location';
				$args[] = $locationid;
			}
			if ($boardingid) {
				$args[] = 'boarding';
				$args[] = $boardingid;
			}
			if ($organizationidstr) {
				$args[] = 'organization';
				$args[] = explode(',',$organizationidstr);
			}
			if ($artistidstr) {
				$args[] = 'artist';
				$args[] = explode(',',$artistidstr);
			}
			if (!empty($args)) {
				# This extracts the items in $args to parameters
				require_once '_pagechanged.inc';
				page_changed(...$args);
			}
		}
		break;

	case 'artist':
		if ($update_parties = db_simpler_array('lineup', "
			SELECT PARTYID
			FROM lineup
			WHERE ARTISTID = $id")
		) {
			require_once '_elementchanged.inc';
			element_changed('party', $update_parties);
		}
		break;

	case 'news':
		news_sync_newsads($id, $accept);
		break;
	}
	return true;
}
