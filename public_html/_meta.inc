<?php

function available_meta(?array $args = null): bool {
	[$title, $desc] = get_meta($args);
	return $desc ? true : false;
}

function get_meta(?array $args = null): array {
	$args ??= $_REQUEST;

	$element	= $args['sELEMENT']  ?? '';
	$id			= $args['sID']		 ?? 0;
	$action 	= $args['ACTION']	 ?? '';
	$subaction	= $args['SUBACTION'] ?? '';

	static $__cache;
	if (isset($__cache[$element][$id][$action][$subaction])) {
		return $__cache[$element][$id][$action][$subaction];
	}

	$title = $desc = null;

	switch ($element) {
	case 'video':
		if ($id) {
			require_once '_videometa.inc';
			$meta = new videometa($id);
			if (!$meta->have_videos()) {
				return [false, false];
			}
			if ($title = $meta->get_flat_title($id, true)) {
				if (preg_match('"^(.*?) \[dot\] (.*?) \[dot\] (.*?)$u"', $title, $match)) {
					$title = $match[1].' '.$match[2].(is_number($match[3]) ? ' '.$match[3] : ' ('.$match[3].')');
				}
				$title = preg_replace('"\h{2,}"u',' ', remove_ubb($title, utf8: true));
			}
			if ($title === null) {
				mail_log('no meta video title');
				return [false, false];
			}
			$title = escape_utf8($title);

			if ($videodesc = memcached_single('videoinfo', 'SELECT CONTENT FROM videoinfo WHERE VIDEOID = '.$id)) {
				$first_newline = mb_strpos($videodesc, "\n");
				$first_double_newline = mb_strpos($videodesc, "\n\n");

				$desc = null;

				if ($first_double_newline !== false
				&&	$first_double_newline <= 150
				) {
					$desc = utf8_mytrim(mb_substr($videodesc, 0, $first_double_newline));

				} elseif (
					$first_newline !== false
				&&	$first_newline <= 150
				) {
					$desc = utf8_mytrim(mb_substr($videodesc, 0, $first_newline));

				} else {
					$parts = preg_split('"([\.\?\!]\s)"umis', $videodesc, -1, PREG_SPLIT_DELIM_CAPTURE | PREG_SPLIT_NO_EMPTY);
					$totallen = 0;
					$desc = '';
					$total_parts = count($parts);
					for ($i = 0; $i < $total_parts; $i += 2) {
						$part = $parts[$i];
						$delim = $parts[$i+1] ?? '';
						$len = mb_strlen($part);
						if (!$totallen || ($totallen + $len) <= 150) {
							$desc .= $part.$delim;
						} else {
							break;
						}
						$totallen += $len + strlen($delim);
					}
					$desc = utf8_mytrim($desc);
				}
				$desc = escape_utf8($desc);

			} elseif ($title) {
				$desc = str_replace('%TITLE%', $title, __('metad:video:single', KEEP_EMPTY_KEYWORDS));
			}
		} else {
			switch ($action) {
			case null:
				$desc = __C('metad:video:overview');
				break;

			case 'popular':
				if (!empty($_SERVER['eDATA'])) {
					if (!have_video_contenttype($_SERVER, 'eDATA')) {
						mail_log('eDATA is not a video contenttype', item: get_defined_vars());
						$new_uri = str_replace($_SERVER['eDATA'], '', $_SERVER['REQUEST_URI']);
						header('Location: https://'.$_SERVER['HTTP_HOST'].$new_uri, true, 302);
						exit;
					}
					$title = __C('popular(video)').' '.__('videotypes:'.$_SERVER['eDATA']);
				} else {
					$title = Eelement_plural_name('popular_video');
				}
				static $__urltokey = [
					'pastday'	=> 'past_day',
					'pasttwoweeks'	=> 'past_two_weeks',
					'pastyear'	=> 'past_year',
					'alltime'	=> 'alltime'
				];
				if (isset($__urltokey[$subaction])) {
					$title .= ' '.__('period:'.$__urltokey[$subaction]);
				}
				break;

			case 'recent':
				if ($subaction !== 'all') {
					if (!($video_spec = explain_table('video'))) {
						return [false, false];
					}
					if (!isset($video_spec['CONTENTTYPE']->enum[$subaction])) {
						page_problem(404,'video');
						return [false, false];
					}
				}

				$desc = __('metad:video:overview:recent',[
					'TYPE'	=> $subaction ? __('videotype:'.$subaction) : null
				]);
				if (!$subaction) {
					$title = Eelement_plural_name('recent_video');
				} else {
					$title = __C('videotype:'.$subaction).' '.element_plural_name('recent_video');
				}
				break;
			}
		}
		break;

	case 'artist':
	case 'location':
	case 'organization':
		if (	$_REQUEST['sID']
		&&	(require_once '_bio.inc')
		&&	($bio = get_bio_base($_REQUEST['sELEMENT'], $_REQUEST['sID']))
		&&	($teaser = get_teaser($bio['BODY'], $same))
		) {
			$desc = flat_with_entities($teaser,UBB_UTF8);
		}
		break;
	}
	return $__cache[$element][$id][$action][$subaction] = [$title, $desc];
}
