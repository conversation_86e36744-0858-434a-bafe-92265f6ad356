<?php

declare(strict_types=1);

function password_strong_enough(string $passwd, ?array &$messages = null): bool {
	require_once '__translation.php';

	$len = strlen($passwd);

	if ($len < 9) {
		$messages[] = __('passwdstrength:too_small_LINE');
		return false;
	}
	if ($len > 250) {
		$messages[] = __('passwdstrength:too_large_LINE');
		return false;
	}
	if ($len < 12) {
		$messages[] = __('passwdstrength:advice:use_larger_LINE', ['CNT' => 12]);
	}

	if (!preg_match('"\p{Ll}"', $passwd)) {
		$messages[] = __('passwdstrength:advice:need_alpha_LINE');
	}
	if (!preg_match('"\p{Lu}"', $passwd)) {
		$messages[] = __('passwdstrength:advice:need_upper_LINE');
	}
	if (!preg_match('"\p{N}"', $passwd)) {
		$messages[] = __('passwdstrength:advice:need_digit_LINE');
	}

	# FIXME: Replace code below with execute()
	$spec = [
		['pipe', 'r'],	# STDIN
		['pipe', 'w'],	# STDOUT
		['pipe', 'w'],	# STDERR
	];
	if (!($process = proc_open('/usr/sbin/cracklib-check', $spec, $pipes))) {
		mail_log('cracklib-check failed to execute');
		return true;
	}
	fwrite($pipes[0], $passwd);
	fclose($pipes[0]);
	$stdout = stream_get_contents($pipes[1]);
	$stderr = stream_get_contents($pipes[2]);
	fclose($pipes[1]);
	fclose($pipes[2]);
	if ($rc = proc_close($process)) {
		mail_log(
			"cracklib-check returned with rc $rc", [
				'rc'	 => $rc,
				'stdout' => $stdout,
				'stderr' => $stderr,
		]);
		return true;
	}
	if (!preg_match('": OK$"',$stdout)) {
		if (false !== ($last_colon = strrpos($stdout, ':'))) {
			$error_part = substr($stdout, $last_colon + 2);
			$messages[] = __('passwdstrength:advice:cracklib:'.str_replace(' ', '_', trim($error_part)).'_LINE');
		}
		return true;
	}
	return true;
}
