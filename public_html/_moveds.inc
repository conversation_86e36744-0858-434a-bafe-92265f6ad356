<?php

function redirect_to_moved() {
	$to = get_moveds($_REQUEST['sELEMENT'],$_REQUEST['sID']);
	if (!$to) {
		return;
	}
	list($to_element,$to_id) = $to;
	$have_admin = have_admin();
	if ($have_admin) {
		require_once '_nocache.inc';
		send_no_cache_headers();
	}
	header('Location: https://'.$_SERVER['HTTP_HOST'].get_element_href($to_element,$to_id),true,$have_admin ? 302 : 301);
	exit;
}
function get_moveds($element,$id) {
	static $__moveds = 0;
	if ($__moveds === 0) {
		$__moveds = memcached_simple_hash('moveds','
			SELECT ELEMENT,ID,TO_ELEMENT,TO_ID
			FROM moveds',
			ONE_DAY
		);
	}
	if (!isset($__moveds[$element][$id])) {
		return null;
	}
	$to_element = array_key_first($__moveds[$element][$id]);
	return [$to_element,$__moveds[$element][$id][$to_element]];
}
