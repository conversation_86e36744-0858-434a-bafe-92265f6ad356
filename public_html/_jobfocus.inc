<?php

function get_jobfocus_types($type = null) {
	static $__jobfocustypes = array(
					// id, optval
	'city'		=> true,	// CITYID,RADIUS
	'province'	=> true,	// PROVINCEID
	'country'	=> true,	// COUNTRYID
	);
	return $type ? isset($__jobfocustypes[$type]) : $__jobfocustypes;
}

define('SHOW_REMOVE_LINK',	1);

function job_focus_overview($jobid, $flags = 0) {
	$focuslist = db_rowuse_array('job_focus','
		SELECT *
		FROM job_focus
		WHERE JOBID='.$jobid.'
		ORDER BY TYPE ASC'
	);
	if (!$focuslist) {
		return;
	}
	layout_open_table(TABLE_FULL_WIDTH | TABLE_VTOP);
	foreach ($focuslist as $focus) {
		layout_start_row();
		echo Eelement_name($focus['TYPE']);
		layout_field_value();
		switch ($focus['TYPE']) {
			case 'country':
				if ($country_name = __('country:'.$focus['ID'], RETURN_UTF8)) {
					echo escape_utf8($country_name);
				} else {
					?>???<?
				}
				break;

			case 'province':
				$province_name = get_element_title('province',$focus['ID']);
				echo $province_name ? get_element_link('province',$focus['ID'],$province_name) : '???';
				break;

			case 'city':
				if (!($city_name = memcached_city($focus['ID']))) {
					?>???<?
					break;
				}
				if ($focus['OPTVAL']) {
					echo str_replace(
						['%KM%', '%CITY%'],
						[$focus['OPTVAL'], get_element_link('city', $focus['ID'], $city_name)],
						__('field:within_x_km_from_city', KEEP_EMPTY_KEYWORDS)
					);
				}
				break;

			default:
				?>???<?
				break;
		}
		if ($flags & SHOW_REMOVE_LINK) {
			layout_next_cell(class: 'right');
			?><a href="/job/<?= $jobid
			?>/remfocus/FOCUSID/<?= $focus['FOCUSID']
			?>"><?= __('action:remove') ?></a><?
		}
		layout_stop_row();
	}
	layout_close_table();
}
function job_focus_single_line(int $jobid, bool $shorter = false, ?array $focuslist = null, string $separator = ', ') {
	if (!$focuslist
	&&	!($focuslist = memcached_rowuse_array('job_focus', "
			SELECT *
			FROM job_focus
			WHERE JOBID = $jobid
			ORDER BY TYPE"))
	) {
		return null;
	}
	$first = true;
	$result = '';
	foreach ($focuslist as $focus) {
		if ($first) {
			$first = false;
		} else {
			$result .= $separator;
		}
		switch ($focus['TYPE']) {
			case 'country':
				$country_name = get_element_title('country',$focus['ID']);
				$result .= $country_name ? escape_specials($country_name) : '???';
				break;

			case 'province':
				$province_name = get_element_title('province',$focus['ID']);
				$result .= $province_name ? escape_specials($province_name) : '???';
				break;

			case 'city':
				$city_name = get_element_title('city',$focus['ID']);
				if ($focus['OPTVAL']) {
					$result .= ($shorter ? 'omgeving ' : 'binnen '.$focus['OPTVAL'].' '.__('abbr:kilometer').' van ');
				}
				$result .= $city_name ? escape_utf8($city_name) : '???';
				break;

			default:
				$result .= '???';
				break;
		}
	}
	return $result;
}
function user_in_job_focus($focuslist,$cityid,$countryid,$provid) {
	foreach ($focuslist as $focus) {
		switch ($focus['TYPE']) {
			case 'country':
				if ($focus['ID'] == $countryid) {
					return true;
				}
				break;

			case 'province':
				if ($focus['ID'] == $provid) {
					return true;
				}
				break;

			case 'city':
				if ($focus['OPTVAL'] == 0) {
					if ($focus['ID'] == $cityid) {
						return true;
					}
				} elseif ($cityid) {
					return memcached_single('job_focus_cities','SELECT 1 FROM job_focus_cities WHERE CITYID='.$cityid.' AND JOBID='.$focus['JOBID'],ONE_HOUR);
				}
				break;

			default:
				break;
		}
	}
	return false;
}
