<?php

require_once '_helper.inc';
require_once '_require.inc';

function get_soundcloud_url(string $url, int $presenceid = 0, ?string $track = null, ?int &$track_cnt = null): string {
	# strip /tracks or /sets
	if (preg_match('"^(.*)/(?:tracks|sets)$"i', $url, $match)) {
		$url = $match[1];
	}
	static $__url;
	$phpkey = ($presenceid ?: $url).':'.$track;
	if (!isset($_REQUEST['NOMEMCACHE'])
	&&	isset($__url[$phpkey])
	) {
		$new_url = $__url[$phpkey];
		return $new_url;
	}
	if ($presenceid) {
		$scid =  null;
		if ($track) {
			$user = $presenceid;
			$scid = memcached_single('soundcloud_track','
				SELECT SCID
				FROM soundcloud_track
				WHERE USER = "'.($user = addslashes($user)).'"
				  AND TRACK = "'.($track = addslashes($track)).'"',
				DEFAULT_EXPIRATION,
				$cachekey = 'scid:'.$user.':'.$track
			);
			if ($scid !== false
			&&	$scid !== null
			) {
				$type = 'tracks';
			}
		} elseif (is_number($presenceid)) {
			$info = db_single_array(['soundcloud_id', 'soundcloud_info'],'
				SELECT TYPE, SCID, TRACK_CNT
				FROM soundcloud_id
				LEFT JOIN soundcloud_info ON ID = PRESENCEID
				WHERE SCID != 0
				  AND ID = '.$presenceid
			);
			if ($info) {
				[$type, $scid, $track_cnt] = $info;
			}
		}
		if (!isset($_REQUEST['NOMEMCACHE'])
		&&	$scid !== null
		) {
			$__url[$phpkey] = $new_url = $scid ? 'https://api.soundcloud.com/'.$type.'/'.$scid : ($track ? $url : false);
			return $new_url;
		}
		$old_scid = $scid;
	}
	if (!preg_match('"soundcloud\.com/(([^/]+)(?:/([^/]+))?.*)$"',$url,$match)) {
		return $url;
	}
	if ($new_url = memcached_get($phpkey)) {
		return $__url[$phpkey] = $new_url;
	}
	if (memcached_get('dont_do_soundcloud')) {
		# dropped packets
		return $__url[$phpkey] = $new_url;
	}
	
	require_once '_spider.inc';
	if (ROBOT) {
		# quick result for robot
		return $__url[$phpkey] = $new_url;
	}
	
	$uri = $match[1];
	$type = $match[2];
	$arg = getifset($match, 3);

	if (!$arg || !is_number($arg)) {
		require_once 'defines/soundcloud.inc';
		$get_url = 'https://api.soundcloud.com/resolve.json?url='.urlencode('https://soundcloud.com/'.$uri);
		static $ch = 0;
		if ($ch === 0) {
			$ch = get_soundcloud_channel();
		}
		if (!$ch) {
			$new_url = false;
			error_log('get_soundcloud_channel() failed');
		} else {
			curl_setopt($ch, CURLOPT_URL, $get_url);
			$data = curl_exec($ch);
			$info = curl_getinfo($ch);
			
			if (!$info['http_code']) {
				mail_log('http_code 0, dont_do_soundcloud for '.$get_url);
				memcached_set('dont_do_soundcloud', true, ONE_HOUR);
				return $url;
			}
			
			if ($info['http_code'] === 404
			||	$info['http_code'] === 403
			) {
				if ($info['http_code'] === 404) {
					$scid = 0;
					$type = 'users';
					$store = true;
					$new_url = $url;
					$track_cnt = 0;
				} elseif (preg_match('"/tracks/(\d+)\.json"', $info['url'], $match)) {
					$scid = $match[1];
					$type = 'tracks';
					$store = true;
					$new_url = 'https://soundcloud.com/tracks/'.$scid;
					$track_cnt = null;
				}
			} elseif (
				$data
			&&	($obj = safe_json_decode($data))
			&&	!is_array($obj)
			) {
				if (property_exists($obj, 'id')
				&&	is_number($obj->id)
				) {
					$scid = $obj->id;
					$type = $obj->kind.'s';
					if ($type !== 'tracks') {
						if (!property_exists($obj, 'track_count')) {
							error_log_r($obj, 'no track_count for url '.$get_url.' @ '.$_SERVER['REQUEST_URI']);
						}
						if (property_exists($obj, 'track_count')) {
							$track_cnt = $obj->track_count;
						}
					} else {
						$track_cnt = 1;
					}
					$new_url = 'https://api.soundcloud.com/'.$type.'/'.$scid;
					$store = true;
				} else {
					error_log('bad(2) soundcloud link: '.$url.' @ '.$_SERVER['REQUEST_URI'],0);
					$new_url = false;
				}
			} else {
				error_log('bad(1) soundcloud link: '.$url.' @ '.$_SERVER['REQUEST_URI'],0);
				$new_url = false;
			}
		}
	} else {
		$new_url = 'https://api.soundcloud.com/'.$uri;
	}
	if (isset($store)
	&&	(	!isset($old_scid)
		||	$old_scid !== $scid
		)
	) {
		if ($presenceid) {
			if ($track) {
				if ($scid) {
					db_insupd('soundcloud_track','
					INSERT INTO soundcloud_track SET
						USER	= "'.$user.'",
						TRACK	= "'.$track.'",
						SCID	= '.$scid.',
						CSTAMP	= '.CURRENTSTAMP.',
						MSTAMP	= '.CURRENTSTAMP.'
					ON DUPLICATE KEY UPDATE
						SCID	= '.$scid.',
						MSTAMP	= '.CURRENTSTAMP
					);
				}
			} else {
				db_insupd('soundcloud_id','
				INSERT INTO soundcloud_id SET
					TYPE	= "'.$type.'",
					SCID	= '.$scid.',
					CSTAMP	= '.CURRENTSTAMP.',
					MSTAMP	= '.CURRENTSTAMP.',
					ID	= '.$presenceid.'
				ON DUPLICATE KEY UPDATE
					SCID	= VALUES(SCID),
					MSTAMP	= IF(SCID = VALUES(SCID), MSTAMP, VALUES(MSTAMP))'
				);
				if (isset($track_cnt)) {
					db_insert('soundcloud_info','
					REPLACE INTO soundcloud_info SET
						PRESENCEID	= '.$presenceid.',
						CHECKSTAMP	= '.CURRENTSTAMP.',
						TRACK_CNT	= '.$track_cnt
					);
				}
			}
		}
	}
	if (isset($cachekey)) {
		memcached_delete($cachekey);
	}
	memcached_set($phpkey, $new_url, ONE_HOUR);
	return $__url[$phpkey] = $new_url;
}
