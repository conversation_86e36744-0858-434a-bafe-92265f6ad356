<?php

function _blacklist_add(int $userid, ?string $reason = null, int $offenseid = 0): bool {
	// add current email to blacklist
	if (!($email = db_single('user', 'SELECT EMAIL FROM user WHERE USERID='.$userid))) {
		return false;
	}
	if (!($email = trim($email))) {
		return true;
	}
	$have_already = db_single('blacklist', 'SELECT 1 FROM blacklist WHERE DSTAMP=0 AND EMAIL="'.addslashes($email).'" LIMIT 1');
	if (query_failed()) {
		return false;
	}
	if ($have_already) {
		return true;
	}
	if (!db_insert('blacklist','
		INSERT INTO blacklist SET
			USERID		= '.CURRENTUSERID.',
			CSTAMP		= '.CURRENTSTAMP.',
			EMAIL		= "'.addslashes($email).'",
			DESCRIPTION	= "'.addslashes(trim($reason)).'",
			OFFENSEID	= '.$offenseid)
	) {
		return false;
	}
	register_notice('blacklist:notice:email_added_LINE', ['EMAIL' => $email]);
	return true;
}

function _blacklist_rem(int $userid): bool {
	return	($email = db_single('user','SELECT EMAIL FROM user WHERE USERID='.$userid))
	?	_blacklist_rem_list($email)
	:	true;
}

function _blacklist_rem_list(string $email): bool {
	if (!($email = trim($email))) {
		return true;
	}
	if (db_update('blacklist','
		UPDATE blacklist SET
			DUSERID	= '.(have_user() ? CURRENTUSERID : 0).',
			DSTAMP	= '.CURRENTSTAMP.'
		WHERE DUSERID = 0
		  AND DSTAMP = 0
		  AND EMAIL = "'.addslashes($email).'"')
	) {
		return false;
	}
	register_notice('blacklist:notice:email_removed_LINE', ['EMAIL' => $email]);
	return true;
}
