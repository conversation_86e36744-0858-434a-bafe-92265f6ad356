<?php

if (!empty($_SERVER['eDATA'])) {
	parse_args(urldecode($_SERVER['eDATA']));
} 
if (!empty($_REQUEST['ARGS'])) {
	foreach (explode(';',$_REQUEST['ARGS']) as $arg) {
		$info = explode('=',$arg,2);
		if (!isset($info[1])) continue;
		$_REQUEST[$info[0]] = $info[1];
	}
}
function parse_args($data) {
	$cur = 0;
	$key = null;
	while (false !== ($pos = strpos($data,'/',$cur))) {
		if ($key) {
			$val = substr($data,$cur,$pos-$cur);
			$cur = $pos+1;
			
			$_REQUEST[$key] = $val;
			$key = $val = null;
		} else {
			$key = substr($data,$cur,$pos-$cur);
			$cur = $pos+1;
		}			
	}
	if ($key) {
		$_REQUEST[$key] = substr($data,$cur);
	}
}
