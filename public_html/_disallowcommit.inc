<?php

require_once 'defines/disallow.inc';
require_once '_require.inc';
require_once '_disallowuser.inc';

const DIS_FORCE				= 0x1;
const DIS_DONT_OVERWRITE	= 0x2;

function disallowuser_commit(int $flags = 0) {
	$force = $flags & DIS_FORCE;
	$no_warnings = CLI; # process_tor_nodes uses this
	if (!$force && $no_warnings) {
		$force = true;
	}
	if (!$force && !require_admin('disallowuser')
	||	!($type = require_element($_POST, 'DISALLOW', ['creation', 'login', 'proxy', 'tor', 'harvest']))
	||	!require_something_trim($_POST, 'IPSTR')
	) {
		return false;
	}

	$reason = isset($_POST['REASON']) ? trim($_POST['REASON']) : null;

	[$ipstr, $masksize] = get_netip_and_masksize($_POST['IPSTR']);

	if ($ipstr === '::1'
	||	$ipstr === '127.0.0.1'
	) {
		if (!$no_warnings) {
			register_error('disallowuser:error:incorrect_ip_LINE', ['IPSTR'=>$ipstr]);
		}
		return null;
	}

	$ipbin = @inet_pton($ipstr);
	if (!$ipbin) {
		register_error('disallowuser:error:incorrect_ip_LINE', ['IPSTR'=>$ipstr]);
		return false;
	}
	[$prefix,$iident] = ipbin_to_prefix_and_iident($ipbin);
	$wherep = '
		WHERE MASKSIZE='.$masksize.'
		  AND PREFIX=0x'.$prefix.'
		  AND IIDENT=0x'.$iident;

	$existing = db_single_assoc('disallowuser','
		SELECT DISALLOW,REASON
		FROM disallowuser'.
		$wherep
	);
	if ($existing === false) {
		return false;
	}
	if ($existing) {
		if ($flags & DIS_DONT_OVERWRITE
		||	(	$existing['DISALLOW'] == $type
			&&	(	!$reason
				||	$existing['REASON']
				&&	$existing['REASON'] == $reason
				)
			)
		) {
			if (!$no_warnings) {
				register_warning('disallowuser:warning:already_listed_LINE');
			}
			return null;
		}
		if (!db_insert('disallowuser_log','
			INSERT INTO disallowuser_log
			SELECT	disallowuser.*,
				'.CURRENTUSERID.' AS DUSERID,
				'.CURRENTSTAMP.' AS DSTAMP
			FROM disallowuser'.
			$wherep)
		) {
			return false;
		}
	}
	if (!db_insupd('disallowuser', "
		INSERT INTO disallowuser SET
			DISALLOW = '$type',
			PREFIX	 = 0x$prefix,
			IIDENT	 = 0x$iident,
			MASKSIZE = $masksize,
			CSTAMP	 = ".CURRENTSTAMP.",
			USERID	 = ".CURRENTUSERID.",
			REASON	 = '".(empty($_POST['REASON']) ? '' : addslashes($_POST['REASON']))."'
		ON DUPLICATE KEY UPDATE
			DISALLOW = VALUES(DISALLOW),
			REASON	 = VALUES(REASON),
			USERID	 = VALUES(USERID),
			CSTAMP	 = VALUES(CSTAMP)")
	) {
		return false;
	}
	if (!$no_warnings) {
		register_notice($existing ? 'disallowuser:notice:changed_LINE' : 'disallowuser:notice:added_LINE');
	}
	flush_disallows($ipstr);
	return true;
}

function disallowuser_remove() {
	if (!require_admin('disallowuser')
	||	!require_something_trim($_POST, 'IPSTR')
	||	false === require_number($_POST, 'MASKSIZE')
	) {
		return false;
	}
	$ipbin = inet_pton($ipstr = $_POST['IPSTR']);
	[$prefix, $iident] = ipbin_to_prefix_and_iident($ipbin);
	$wherep = "
		WHERE PREFIX	= 0x$prefix
		  AND IIDENT	= 0x$iident
		  AND MASKSIZE	= {$_POST['MASKSIZE']}";

	if (!db_insert('disallowuser_log','
		INSERT INTO disallowuser_log
		SELECT	disallowuser.*,
			'.CURRENTUSERID.' AS DUSERID,
			'.CURRENTSTAMP.'  AS DSTAMP
		FROM disallowuser'.
		$wherep)
	||	!db_delete('disallowuser', '
		DELETE FROM disallowuser'.$wherep)
	) {
		return false;
	}
	register_notice('disallowuser:notice:removed_LINE');
	flush_disallows($ipstr);
	return true;
}

function show_disallow_form($disallow = null,$ipbin = null) {
	?><div class="block"><?
	echo __C('action:disallow'); ?> <?
	if (!$disallow && !$ipbin) {
		?><input type="text" name="IPSTR" class="ip" required="required" /> <?
	} else {
		?><input type="hidden" name="IPSTR" value="<?= inet_ntop($ipbin) ?>" /><?
		?><input type="hidden" name="MASKSIZE" value="<?= is_array($disallow) ? $disallow['MASKSIZE'] : null ?>" /><?
	}
	?><select name="DISALLOW"><?
	foreach (get_disallow_type() as $id => $type) {
		?><option<?
		if ($disallow && $disallow['ID'] == $id) {
			?> selected="selected"<?
		}
		?> value="<?= $type ?>"><?= __('disallowuser:'.$type); ?></option><?
	}
	?></select>, <?= element_name('reason')
	?>: <input type="text" name="REASON" class="regular" maxlength="255" style="width:20em" <?
	if ($disallow) {
		?> value="<?= escape_specials($disallow['REASON']) ?>"<?
	}
	?>> <?
	?><input type="submit" value="<?= __($disallow ? 'action:change' : 'action:add') ?>" /><?
	if ($disallow) {
		?><input name="REMOVE" onclick="return confirm('<?= __('disallowuser:confirm:removal_LINE') ?>')" type="submit" value="<?= __('action:remove') ?>" class="lmrgn" /><?
	}
	?></div><?
}
