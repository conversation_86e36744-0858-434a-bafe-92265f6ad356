<?php

require_once '_connect.inc';
require_once '_fora.inc';
require_once '_forumlist.inc';
require_once '_message.inc';
require_once '_settings.inc';
require_once '_topic.inc';
require_once '_topiclist.inc';
require_once '_poll.inc';

function preamble(): void {
	switch ($_REQUEST['sELEMENT']) {
	case 'topic':
		if ($topicid = $_REQUEST['sID']) {
			global $__currtopic;
			$__currtopic = memcached_topic($topicid);
			if ($__currtopic) {
				if ($__currtopic['STATUS'] === 'hidden') {
					http_response_code(404);
				}
			}
		}
		if ($_REQUEST['ACTION'] === 'bottom'
		&&	$topicid
		) {
			header('Location: https://'.$_SERVER['HTTP_HOST'].str_replace('/bottom','',$_SERVER['REQUEST_URI']),true,301);
			exit;
		}
		break;
	case 'forum':
		if ($forumid = $_REQUEST['sID']) {
			$GLOBALS['__currforum'] = memcached_forum($forumid) ?: false;
		}
		switch ($_REQUEST['ACTION']) {
		case 'yours':
			if (have_user()) {
				moved_permanently(preg_replace('"/yours(?:/|\.html|)"','/active-history/'.CURRENTUSERID,$_SERVER['REQUEST_URI']),true,301);
				exit;
			}
			break;
		case 'firstpage':
			header('Location: https://'.$_SERVER['HTTP_HOST'].str_replace('/firstpage','',$_SERVER['REQUEST_URI']),true,301);
			break;
		}
		break;
	}
}

function display_header(): void {
	require_once '_feed.inc';
	switch ($_REQUEST['sELEMENT']) {
	case 'topic':
		global $__currtopic;
		if (($topicid = $_REQUEST['sID'])
		&&	($topic = $__currtopic)
		) {
			if ($topic['STATUS'] !== 'hidden') {
				show_feed('topic',FEED_HEADER,$topicid);
			}
			require_once '_topicheader.inc';
			show_topic_stats_header($topic['LSTAMP']);
		}
		break;
	case 'forum':
		global $__currforum;
		if (!$__currforum) {
			if (!$_REQUEST['ACTION']) {
				?><link rel="alternate" type="application/atom+xml" title="Nieuwste Partyflock forumonderwerpen" href="/feed/forum/topics.xml" /><?
				?><link rel="alternate" type="application/atom+xml" title="Nieuwste Partyflock forumberichten" href="/feed/forum/messages.xml" /><?
			}
			break;
		}
		$forumid = $_REQUEST['sID'];
		$name = $__currforum['NAME'];
		?><link rel="alternate" type="application/atom+xml" title="Nieuwste onderwerpen in Partyflock forum: <?= escape_utf8($name);
		?>" href="/feed/forum/<?= $_REQUEST['sID'];
		?>/topics.xml" /><?
		?><link rel="alternate" type="application/atom+xml" title="Nieuwste berichten in Partyflock forum: <?= escape_utf8($name);
		?>" href="/feed/forum/<?= $_REQUEST['sID'];
		?>/messages.xml" /><?
		break;
	}
}
function perform_commit(): ?bool {
	switch ($_REQUEST['ACTION']) {
	default:
	case null:				return null;
	case 'move':			return forum_topic_move();
	case 'rename':			return forum_topic_rename();
	case 'open':			return forum_topic_set_status('open');
	case 'close':			return forum_topic_set_status('closed');
	case 'stick':			return forum_topic_set_sticky(true);
	case 'unstick':			return forum_topic_set_sticky(false);
	case 'hide':			return forum_topic_set_status('hidden');
	case 'commitnew':		require_once '_topicactions.inc';
							return commit_new_topic('topic');
	case 'changetype':		return topic_change_type();
	case 'commit-settings':	return forum_commit_settings();
	case 'connect':			return topic_connect();
	}
}
function display_body(): void {
	switch ($_REQUEST['ACTION']) {
	default:			not_found(); return;
	case null:			display_overview(); return;
	case 'active':		forum_display_active_topics(); return;
	case 'busy':		forum_display_busy_topics(); return;
	case 'news':		forum_display_news(); return;
	case 'firstpage':	$_REQUEST['FIRST_PAGE'] = true;
	case 'single':		display_forum(); return;
	case 'intrecent':	forum_display_recent_interesting(); return;
	case 'showtopic':	forum_display_topic(); return;
	case 'commitnew':
		if (isset($_REQUEST['TOPICID'])) {
			forum_display_topic();
		}
		return;
	case 'form':		forum_display_new_topic_form(); return;
	case 'commit':
	case 'move':
	case 'close':
	case 'open':
	case 'hide':
	case 'stick':
	case 'unstick':
	case 'rename':
	case 'changetype':
	case 'connect':
							forum_display_topic(); return;
	case 'search':			require_once '_search.inc'; flora_show_search(); return;
	case 'searchresult':	require_once '_search.inc'; flora_show_search_results(); return;
	case 'history':			forum_display_history(); return;
	case 'settings':		forum_show_settings(); return;
	case 'commitsettings':	empty($GLOBALS['commitresult']) ? forum_show_settings() : display_overview(); return;
	case 'activehistory':	forum_display_active_history(); return;
	case 'pastweekend':		forum_display_pastweekend(); return;
	}
}
function topic_change_type(): bool {
	require_once '_topictypes.inc';
	if (!require_idnumber($_REQUEST,'TOPICID')
	||	!_topic_adminnable($topicid = $_REQUEST['TOPICID'])
	||	!require_topictype_using_topic($_REQUEST,'TYPE',$topicid)
	) {
		return false;
	}
	if ($type = $_REQUEST['TYPE']) {
		if (!($subj = db_single('topic','SELECT SUBJECT FROM topic WHERE TOPICID='.$topicid))) {
			if ($subj !== false) {
				not_found();
			}
			return false;
		}
		$subj = strip_topictype_subject($type,$subj);
	}
	if (!db_replace('topic_log','
		REPLACE INTO topic_log
		SELECT * FROM topic
		WHERE TOPICID = '.$topicid)
	||	!db_update('topic','
		UPDATE topic SET 
			MUSERID	='.CURRENTUSERID.',
			MSTAMP	='.CURRENTSTAMP.',
			FLAGS	=FLAGS&~'.(TOPIC_FOR_SALE | TOPIC_SEARCHING_FOR).'|'.$type.
			(isset($subj) ? ',SUBJECT="'.addslashes($subj).'"' : null).'
		WHERE TOPICID='.$topicid)
	) {
		return false;
	}
	memcached_delete('topic:'.$topicid);
	_notice('Onderwerptype is aangepast.');
	if (isset($subj)) {
		require_once '_sphinx.inc';
		sphinx_update_element('topic',$topicid);
	}
	return true;
}
function forum_topic_set_status($status): bool {
	require_once '_topicactions.inc';
	if (!require_idnumber($_REQUEST,'TOPICID')
	||	(	$status === 'hidden'
		?	!_topic_adminnable($_REQUEST['TOPICID'])
		:	!_topic_adminnable_sales($_REQUEST['TOPICID'])
		)
	) {
		return false;
	}
	return set_topic_status('topic',$_REQUEST['TOPICID'],$status,true);
}
function forum_topic_set_sticky($sticky): bool {
	if (!($topicid = require_idnumber($_REQUEST,'sID'))
	||	!_topic_adminnable_sales($topicid)
	||	!db_replace('topic_log','
		REPLACE INTO topic_log
		SELECT * FROM topic
		WHERE TOPICID = '.$topicid.'
		  AND STICKY != b'.($sticky ? "'1'" : "'0'"))
	||	!db_update('topic','
		UPDATE topic SET 
			MSTAMP  = '.CURRENTSTAMP.',
			MUSERID = '.CURRENTUSERID.',
			STICKY  = b\''.($sticky ? 1 : 0).'\'
		WHERE TOPICID = '.$topicid.'
		  AND STICKY != b'.($sticky ? "'1'" : "'0'"))
	) {
		return false;
	}
	memcached_delete('topic:'.$topicid);
	register_notice($sticky ? 'topic:notice:made_sticky_LINE' : 'topic:notice:made_unsticky_LINE',DO_UBB, ['ELEMENT' => 'topic', 'TOPICID' => $_REQUEST['sID']]);
	return true;
}
function forum_topic_move(): bool {
	require_once '_topicactions.inc';
	if (!require_idnumber($_REQUEST,'TOPICID')
	||	!require_idnumber($_REQUEST,'FORUMID')
	) {
		return false;
	}
	return move_topic($_REQUEST['TOPICID'],$_REQUEST['FORUMID']);
}
function forum_topic_rename(): bool {
	if (!($topicid = require_idnumber($_REQUEST,'sID'))
	||	!require_something($_REQUEST,'SUBJECT', utf8: true)
	||	!_topic_adminnable($topicid)
	||	!db_insert('topic_log','
		INSERT INTO topic_log
		SELECT * FROM topic
		WHERE TOPICID='.$topicid)
	|| 	!db_update('topic', '
		UPDATE topic SET
			MSTAMP	='.CURRENTSTAMP.',
			MUSERID	='.CURRENTUSERID.',
			SUBJECT ="'.addslashes(trim($_REQUEST['SUBJECT'])).'"
		WHERE TOPICID = '.$topicid)
	) {
		return false;
	}
	memcached_delete('topic:'.$topicid);
	register_notice('topic:notice:renamed_LINE',DO_UBB,array('ELEMENT'=>'topic','TOPICID'=>$topicid));
	require_once '_sphinx.inc';
	sphinx_update_element('topic', $topicid);
	return true;
}
function topic_connect(): bool {
	require_once '_topicactions.inc';
	if (!($topicid = require_idnumber($_REQUEST,'TOPICID'))) {
		return false;
	}
	if (!($topic = db_single_assoc('topic','SELECT FORUMID,USERID,MUSERID FROM topic WHERE TOPICID='.$topicid,DB_USE_MASTER))) {
		if ($topic !== false) {
			not_found();
		}
		return false;
	}
	$forumid = $topic['FORUMID'];
	if ((	CURRENTUSERID !== $topic['USERID']
		||	$topic['MUSERID']
		&&	CURRENTUSERID !== $topic['MUSERID']
		)
	&&	!require_forum_admin($forumid)
	) {
		return false;
	}
	if (isset($_REQUEST['NAME'])) {
		if ($_REQUEST['NAME'] === '') {
			$_REQUEST['PARTYID'] = 0;
		} else {
			if (false === ($options = db_rowuse_array('party','
				SELECT PARTYID, NAME, CANCELLED, SUBTITLE, STAMP
				FROM party
				WHERE STAMP > '.(TODAYSTAMP - ONE_WEEK).'
				  AND (NAME LIKE "%'.addslashes(str_replace(' ','%',$_REQUEST['NAME'])).'%"'.
				(is_number($_REQUEST['NAME']) ? ' OR PARTYID = '.$_REQUEST['NAME'] : '').')
				  AND ACCEPTED
				ORDER BY STAMP'))
			) {
				return false;
			}
			if (!$options) {
				_error('Geen feesten gevonden in afgelopen week en de toekomst, met &quot;'.escape_utf8($_REQUEST['NAME']).'&quot; in de naam!');
				return false;
			}
			if (isset($options[1])) {
				warning('Kan geen keus maken, er zijn meer feesten met die naam!');
				$GLOBALS['__connopts'] = $options;
				return true;
			}
			# $name = $options[0]['NAME'];
			$_REQUEST['PARTYID'] = $options[0]['PARTYID'];
		}
	} elseif (isset($_REQUEST['PARTYID'])) {
		if (!require_idnumber($_REQUEST,'PARTYID')) {
			return false;
		}
		if (!($name = memcached_party($_REQUEST['PARTYID']))) {
			if ($name !== false) {
				_error('Feest met ID '.$_REQUEST['PARTYID'].' is niet opvraagbaar!');
			}
			return false;
		}
	} else {
		return false;
	}
	if (!db_delete('connect','
		DELETE FROM connect
		WHERE MAINTYPE="topic" AND ASSOCTYPE="party" AND MAINID='.$topicid.'
		   OR MAINTYPE="party" AND ASSOCTYPE="topic" AND ASSOCID='.$topicid)
	) {
		return false;
	}
	if (!$_REQUEST['PARTYID']) {
		register_notice('topic:notice:disconnected_from_party_LINE',DO_UBB,array('TOPICID'=>$topicid));
	} else {
		if (!db_insert('connect','
			INSERT INTO connect (MAINTYPE,ASSOCTYPE,MAINID,ASSOCID,CSTAMP,CUSERID)
			VALUES	("topic","party",'.$topicid.','.$_REQUEST['PARTYID'].','.CURRENTSTAMP.','.CURRENTUSERID.'),
					("party","topic",'.$_REQUEST['PARTYID'].','.$topicid.','.CURRENTSTAMP.','.CURRENTUSERID.')')
		) {
			return false;
		}
		register_notice('topic:notice:connected_to_party_LINE',DO_UBB, ['TOPICID' => $topicid, 'PARTYID' => $_REQUEST['PARTYID']]);
		static $__move = [
			46	=> 48,
			4	=> 60
		];
		if (isset($__move[$forumid])
		&&	!move_topic($topicid,$__move[$forumid],false)
		) {
			error_log('FAILED TO MOVE TOPIC '.$topicid);
			return false;
		}
	}
	return true;
}
function forum_commit_settings() {
	require_once '_rights.inc';
	if (!require_user()
	||	!require_post()
	||	!($userid = require_idnumber($_REQUEST,'USERID'))
	||	!require_self_or_admin($_REQUEST['USERID'],'helpdesk')
	) {
		return false;
	}
	global $currentuser;
	foreach ($_POST['RECENTDIFF'] as $forumid => $recent_diff) {
		if (!$currentuser->has_forum_read_access($forumid)
		||	!is_number($recent_diff)
		) {
			continue;
		}
		if ($recent_diff < 1) {
			$recent_diff = ONE_DAY;
		} elseif ($recent_diff > 28) {
			$recent_diff = 28;
		}
		$vals[] =
			'('.$forumid.
			','.$userid.
			','.($recent_diff * ONE_DAY).
			','.(isset($_POST['SHOW_RECENT'][$forumid]) ? 1 : 0).
			','.(isset($_POST['ORDERID'][$forumid]) ? '"'.$_POST['ORDERID'][$forumid].'"' : 0).
			')';
	}
	if (empty($vals)) {
		_error('Geen wijzigingen noodzakelijk!');
		return false;
	}
	if (!db_replace('forumsettings','
		REPLACE INTO forumsettings (FORUMID,USERID,RECENTDIFF,SHOW_RECENT,ORDERID)
		VALUES '.implode(',',$vals))
	) {
		return false;
	}
	register_notice('forum:notice:settings_changed_LINE');
	flush_forum_rights(CURRENTUSERID);
	$currentuser->load_forum_settings();
	return true;
}
function forum_show_settings() {
	if (!require_user()
	||	!require_idnumber($_REQUEST,'USERID')
	||	!require_self_or_admin($_REQUEST['USERID'],'helpdesk')
	) {
		return;
	}
	layout_open_section_header();
	echo Eelement_plural_name('forum_setting');
	layout_close_section_header();

	$forumids = $GLOBALS['currentuser']->forum_ids_read_access();

	if (empty($forumids)) {
		?><p>Jij mag geen enkel forum lezen.</p><?
		return;
	}
	$forumidstr = implode(',',$GLOBALS['currentuser']->forum_ids_read_access());
	$fora = get_fora($forumidstr);
	if ($fora === false) {
		return;
	}
	if (!$fora) {
		?><p>Geen fora gevonden.</p><?
		return;
	}
	if (!($forum_settings = db_rowuse_hash(
		'forumsettings','
		SELECT FORUMID,USERID,SHOW_RECENT,RECENTDIFF,ORDERID
		FROM forumsettings
		WHERE FORUMID IN ('.$forumidstr.')
		  AND USERID IN (1,'.$_REQUEST['USERID'].')
		ORDER BY USERID ASC'
	))) {
		if ($forum_settings !== false) {
			register_error('forum_settings:error:could_not_obtain_LINE');
		}
		return;
	}
	$last_order_id = 0;
	foreach ($forum_settings as $forumid => $forum_setting) {
		if ($forum_setting['ORDERID'] > $last_order_id) {
			$last_order_id = $forum_setting['ORDERID'];
		}
		if (!isset($fora[$forumid])) {
			mail_log("forum with forumid $forumid does not exist for forumsetting of userid ".CURRENTUSERID);
			unset($forum_settings[$forumid]);
		}
	}
	require_once '_flockandforum.inc';
	show_settings_info();

	?><form method="post" onsubmit="return submitForm(this)" action="/forum/commit-settings"><?
	?><input type="hidden" name="USERID" value="<?= $_REQUEST['USERID']; ?>" /><?
	layout_open_box('white');
	layout_open_table('fw hhla default');
	layout_start_header_row();
	layout_header_cell(Eelement_name('forum'));
	layout_header_cell_center(__C('action:show_recent'));
	layout_header_cell_center(Eelement_name('recent_period'));
	layout_stop_header_row();
 	foreach ($fora as $forumid => $name) {
		$forumsetting = $forum_settings[$forumid];
		$showrecent = $forumsetting['SHOW_RECENT'];
		?><input type="hidden" name="ORDERID[<?= $forumid; ?>]" value="<?= $forumsetting['ORDERID']; ?>" /><?
		// FORUM
		layout_start_rrow(0,$showrecent ? 'bold-hilited' : null);
		echo get_element_link('forum',$forumid,$name);

		// SHOW_RECENT
		layout_next_cell(class: 'center');
		show_input(array(
			'onclick'	=> 'cbclickpp(this)',
			'type'		=> 'checkbox',
			'name'		=> 'SHOW_RECENT['.$forumid.']',
			'checked'	=> $showrecent ? true : false,
			'value'		=> 1
		));
		// RECENTDIFF
		layout_next_cell(class: 'center');
		show_input(array(
			'class'		=> 'right three_digits',
			'type'		=> 'number',
			'min'		=> 1,
			'max'		=> 28,
			'name'		=> 'RECENTDIFF['.$forumid.']',
			'value'		=> $forumsetting['RECENTDIFF'] ? $forumsetting['RECENTDIFF'] / ONE_DAY : 2
		));
		layout_stop_row();
	}
	layout_close_table();
	layout_close_box();
	?><div class="block"><?
	?><input type="submit" value="<?= __('action:change'); ?>" /><?
	?></div><?
	?></form><?
}
function forum_display_new_topic_form(): void {
	require_once '_topictypes.inc';
	require_once '_commentobject.inc';

	layout_show_section_header();

	if (!have_user()) {
		no_permission();
		return;
	}
	global $currentuser;
	// pick default forum for messages
	if (($forumid = have_idnumber($_REQUEST,'FORUMID'))
	&&	!$currentuser->has_forum_write_access($forumid)
	) {
		// no rights to do anything in this forum
		return;
	}
	include_js('js/form/poll');
	include_js('js/form/newtopic');

	if (false === ($sale_forumids = db_simpler_array('forum', '
		SELECT FORUMID
		FROM party_db.forum
		WHERE FLAGS & '.FORUMFLAG_SALE
	))) {
		return;
	}

	?><form<?
	?> accept-charset="utf-8"<?
	?> method="post"<?
	?> onsubmit="return submitNewTopic(this)"<?
	?> action="/topic/commitnew"><?

	layout_open_box('white');
	layout_box_header(Eelement_name('new_topic'));

	show_form_secret();
	layout_open_table('fw');

	layout_start_row();
	if (($partyid = have_idnumber($_REQUEST,'PARTYID'))
	&&	($party = memcached_party_and_stamp($partyid))
	&&	$party['ACCEPTED']
	) {
		?><input type="hidden" name="PARTYID" value="<?= $partyid ?>" /><?
		echo Eelement_name('connection');
		layout_field_value();
		echo element_name('event');
		?>: <strong><?= get_element_link('party',$partyid) ?></strong><?
		layout_restart_row();
	}

	echo Eelement_name('topic');
	layout_field_value();
	show_input([
		'name'		=> 'SUBJECT',
		'id'		=> 'subject',
		'type'		=> 'text',
		'maxlength'	=> 128,
		'required'	=> true,
		'autofocus'	=> true
	]);

	$show_sale_notice
	=	$forumid
	&&	($forum = memcached_forum($forumid))
	&&	$forum['FLAGS'] & FORUMFLAG_SALE;


	layout_restart_row();
		echo Eelement_name('forum');
		layout_field_value();
		?><select<?
		?> required="required"<?
		?> name="FORUMID"<?
		?> data-sale-forums="<?= implode(',',$sale_forumids) ?>"<?
		?> onchange="changeForumid(this)"<?
		if ($show_sale_notice) {
			?> data-active-notice="<?= $forumid ?>"<?
		}
		?>><option></option><?
		_forumlist_display_accessible($forumid,$partyid);
		?></select><?
	show_topictype_optionsrow($forumid);
	if (!have_admin()) {
		$pstamp = $forumid
		?	db_single(array('poll','topic'),'
			SELECT MAX(poll.CSTAMP)
			FROM poll
			JOIN topic ON TOPICID=ELEMENTID
			WHERE ELEMENT="topic"
			  AND topic.USERID='.CURRENTUSERID.'
			  AND FORUMID='.$forumid)
		:	db_single('poll','
			SELECT MAX(CSTAMP)
			FROM poll
			WHERE ELEMENT="topic"
			  AND USERID='.CURRENTUSERID
		);
		if ($pstamp && $pstamp > CURRENTSTAMP-3600) {
			$nopoll = true;
		}
	}
	layout_restart_row(isset($nopoll) ? ROW_LIGHT : 0);
	if (!isset($nopoll)) {
		?><label for="polltopicCB"><?= Eelement_name('poll'); ?></label><?
	} else {
		echo Eelement_name('poll');
	}
	layout_field_value();
	if (isset($nopoll)) {
		echo __('topic:info:max_one_poll_per_hour',['MINS' => (int)((CURRENTSTAMP-$pstamp)/60)]);
	} else {
		show_input(array(
			'name'		=> 'POLLTOPIC_C',
			'id'		=> 'polltopicCB',
			'type'		=> 'checkbox',
			'value'		=> 1,
			'onclick'	=> 'doPoll(this)'
		));
	}
	layout_stop_row();
	layout_close_table();
	layout_close_box();

	layout_open_box($show_sale_notice ? 'white' : 'hidden white','salesnotice');
	?><div class="block"><?= __('topic:info:ticket_sale_info_TEXT',DO_UBB|DO_NL2BR) ?></div><?
	layout_close_box();

	comment_display('message');
	?></form><?
}
function forum_display_pastweekend() {
	layout_show_section_header(Eelement_name('past_weekend'));

	overview_menu();

	$topiclist = new _topiclist();

	$topiclist->past_weekend();
	$topiclist->show_author = true;
	$topiclist->show_header = true;
	$topiclist->show_heart = true;
	$topiclist->show_short = true;
	$topiclist->show_last = true;
	$topiclist->no_limit = true;
	$topiclist->not_in_forum(60);
	$topiclist->order_reverse_lstamp();

	if (!$topiclist->query()) {
		return;
	}
	if ($topiclist->have_rows()) {
		$topiclist->display();
	}
}
function display_overview(): void {
	global $currentuser;
	layout_show_section_header();

	overview_menu();

	single_menu();

	require_once '_subscriptionoverview.inc';
	$so = new subscriptionoverview(CURRENTUSERID,'topic');
	$so->display();

	// now query all the fora for their messages!

	// FIXME:	When adding forums, add forumsetting for default user too!
	// 			forumsetting needed???
	// 			Use some sort of ORDERID when this is done using web
	$fora = get_fora();

	if (!$fora) {
		?><p>Geen fora gevonden.</p><?
		return;
	}
	?><table class="fw regular forum"><?

	$topiclist = new _topiclist();
	$topiclist->show_author = true;
	$topiclist->overview	= true;
	$topiclist->show_groups = true;
 	$topiclist->show_last	= true;

	$currentuser->load_forum_rights();
	if (!($forumsettings = $currentuser->forumsettings)) {
		_error('Geen foruminstellingen gevonden!');
		return;
	}
	$lastorderid = 0;
	foreach ($forumsettings as $forumsetting) {
		if ($forumsetting['ORDERID'] > $lastorderid) {
			$lastorderid = $forumsetting['ORDERID'];
		}
	}
	$case = null;
	$max_recentdiff = 0;
	foreach ($fora as $forumid => $name) {
		$forumids[] = $forumid;
		if (!isset($forumsettings[$forumid])) {
			$forumsettings[$forumid] = array(
				'SHOW_RECENT'	=> false,
				'RECENTDIFF'	=> ($recentdiff = 86400),
				'ORDERID'	=> ++$lastorderid
			);
		} else {
			$recentdiff = $forumsettings[$forumid]['RECENTDIFF'];
		}
		if (!$recentdiff) {
			$recentdiff = 86400;
		}
		if ($recentdiff > $max_recentdiff) {
			$max_recentdiff = $recentdiff;
		}
		$case .= ' WHEN '.$forumid.' THEN '.(CACHESTAMP - $recentdiff);
	}
	$case .= ' END';

	$forumlist = memcached_rowuse_hash(
		array('topic','message'),'
		SELECT	forum.FORUMID,SHORT,forum.NAME,
			COUNT(IF(LSTAMP > CASE forum.FORUMID '.$case.',1,NULL)) AS CNT,
			MAX(LSTAMP) AS LAST_POST,
			TO_DAYS(FROM_UNIXTIME('.TODAYSTAMP.')) - TO_DAYS(FROM_UNIXTIME(MAX(LSTAMP))) AS DIFFDAYS
		FROM forum
		JOIN topic USING (FORUMID)
		WHERE LSTAMP>'.(CACHESTAMP-$max_recentdiff).'
		  AND FORUMID IN ('.implode(',',$forumids).')
		GROUP BY FORUMID',
		FIVE_MINUTES
	);
	if ($forumlist === false) {
		return;
	}
	$topiclist->query();
	$orderid = 0;
	number_asort($forumsettings,'ORDERID');
	foreach ($forumsettings as $forumid => $forumsetting) {
		if (!isset($fora[$forumid])) {
			# probably closed forum
			continue;
		}
		if (!isset($forumlist[$forumid])) {
			$forum = memcached_single_assoc(
				'topic', '
				SELECT	MAX(LSTAMP) AS LAST_POST,
					TO_DAYS(FROM_UNIXTIME('.TODAYSTAMP.')) - TO_DAYS(FROM_UNIXTIME(MAX(LSTAMP))) AS DIFFDAYS,
					0 AS CNT
				FROM topic
				WHERE FORUMID='.$forumid,
				TEN_MINUTES
			);
			if (!$forum) {
				$forum['LAST_POST'] = 0;
				$forum['DIFFDAYS']  = 0;
			}
		} else {
			$forum = $forumlist[$forumid];
		}
		$name = $fora[$forumid];
		if (!$currentuser->has_forum_read_access($forumid)) {
			continue;
		}
		/** @noinspection PhpRedundantOptionalArgumentInspection */
		extract($forum, \EXTR_OVERWRITE);
		?><tbody><?
		?><tr class="hdr"><?
		?><th colspan="<?= $topiclist->column_count() ?>" class="hhl"><?
		echo get_element_link('forum',$forumid,$name);
		if ($LAST_POST) {
			ob_start();
			switch ($DIFFDAYS) {
				case 0:	_time_display($LAST_POST);
					break;
				case 1:
				case 2:
					echo get_relative_day_name($LAST_POST);
					break;
				default:
					if (SMALL_SCREEN) {
						_date_display_numeric($LAST_POST);
					} else {
						_dateday_display($LAST_POST);
					}
					break;
			}
			$LAST_PART = ob_get_clean();
		} else {
			$LAST_PART = null;
		}
		?><div class="r nb small nowrap"><?

		if (SMALL_SCREEN) {
			if ($CNT) {
				echo $CNT,' recent, ';
			}
			echo $LAST_PART;
		} else {
			if ($CNT) {
				?><div class="ib right middle nowrap" style="width:5em"><?= $CNT ?> recent</div><?
			}
			?><div class="ib right middle nowrap" style="width:20em"><?= $LAST_PART ?></div><?
		}
		?></div><?
		?></tr><?

		$topiclist->display_forum($forumid);

		?></tbody><?
	}
	?></table><?
}
function overview_menu()  {
	global $currentuser;
	$haveuser = have_user();
	layout_open_menu();
	layout_menuitem(Eelement_name('overview'),'/forum',empty($_REQUEST['ACTION']));
	layout_menuitem(Eelement_name('past_weekend'),'/forum/pastweekend',$_REQUEST['ACTION'] === 'pastweekend');
	layout_menuitem(__C('status:active'),'/forum/active',$_REQUEST['ACTION'] === 'active' && empty($_REQUEST['PAGE']));
	layout_menuitem(__C('status:busy(activity)'),'/forum/busy/month',$_REQUEST['ACTION'] === 'busy' && $_REQUEST['SUBACTION'] === 'month' && getifset($_SERVER,'eDATA') !== 'progressive' && empty($_REQUEST['PAGE']));

	if ($haveuser) {
		$currentuser->load_forum_rights();
		if ($currentuser->forumsettings) {
			foreach ($currentuser->forumsettings as $forumid => $forumsetting) {
				if ($forumsetting['SHOW_RECENT']) {
					$showrecents = true;
					break;
				}
			}
			if (isset($showrecents)) {
				layout_menuitem(__C('field:interesting'),'/forum/intrecent',$_REQUEST['ACTION'] === 'intrecent' && empty($_REQUEST['PAGE']));
			}
		}
	}
	layout_menuitem(Eelement_plural_name('new_topic'),'/forum/news',$_REQUEST['ACTION'] === 'news' && empty($_REQUEST['PAGE']));
	layout_continue_menu();

	if ($haveuser) {
		layout_menuitem(Eelement_plural_name('setting'),'/forum/settings?USERID='.CURRENTUSERID);
	}
	$forumid = have_idnumber($_REQUEST,'FORUMID') ?: 0;
	layout_menuitem(__C('action:search'),'/forum/search'.($forumid ? '?FORUMID='.$forumid : null),$_REQUEST['ACTION'] === 'search');
	layout_close_menu();

	if (!have_user()) {
		return;
	}
	$partic = memcached_single('message','SELECT 1 FROM message WHERE USERID='.CURRENTUSERID.' LIMIT 1',300);
	if ($partic === false) {
		return;
	}
/*	$subcnt	=
		have_admin()
	?	0
	:	$subcnt = memcached_single('subscription','SELECT COUNT(*) FROM subscription WHERE ELEMENT="topic" AND USERID='.CURRENTUSERID,300);
	if ($subcnt === false) return;*/
	if ($partic) {// || $subcnt) {
		layout_open_menu();
		if ($partic) {
			layout_menuitem(
				Eelement_plural_name('active_participation'),
				'/forum/active-history/'.CURRENTUSERID,
					$_REQUEST['ACTION'] === 'activehistory'
				&&	$_REQUEST['subID'] === CURRENTUSERID
			);
			layout_menuitem(
				Eelement_name('participation_history'),
				'/forum/history/'.CURRENTUSERID,
					$_REQUEST['ACTION'] === 'history'
				&&	$_REQUEST['subID'] === CURRENTUSERID
			);
		}
		layout_close_menu();
	}
}

function single_menu($forumid = 0) {
	layout_open_menu();
	if (have_user()) {
		layout_menuitem(Eelement_name('new_topic'),'/topic/form'.($forumid ? '?FORUMID='.$forumid : null));
	}
	if ($forumid) {
		layout_open_menuitem();
		require_once '_subscriptions.inc';
		show_subscription_link('forumnewtopic',$forumid,'action:follow_new_topics','action:unfollow_new_topics');
		layout_close_menuitem();
	}
	layout_close_menu();
}
function topic_menu($topic) {
	static $__topicmenustr = null;
	if ($__topicmenustr) {
		echo str_replace('response_0','response_1',str_replace('follow_0','follow_1',$__topicmenustr));
		return;
	}
	$topicid = $topic->topicid();
	ob_start();
	layout_open_menu();
	layout_menuitem(Eelement_name('overview'),'/forum');
	layout_next_menuitem();
	?><a href="/forum/<?= $topic->forumid() ?>"><b><?= escape_utf8($topic->topicrow['FNAME']) ?></b> <?= Eelement_name('forum') ?></a><?
	layout_menuitem(Eelement_name('permalink'),'/topic/'.$topicid);
	layout_continue_menu();
	layout_menuitem(__C('action:search_in_this_topic'), '/forum/search?TOPICID='.$topic->topicid());
	layout_menuitem(__C('action:search'), '/forum/search?FORUMID='.$topic->forumid());
	if (SHOW_FEED_ICON) {
		if (/*	in_array($topic->topicrow['FORUMID'],memcached_simpler_array('rights_forum','SELECT FORUMID FROM rights_forum WHERE USERID=1 AND TYPE IN ("normal","read")',600))
		&&*/	$topic->topicrow['STATUS'] !== 'hidden'
		) {
			layout_open_menuitem();
			show_feed('topic', FEED_ICON, $topicid);
			layout_close_menuitem();
		}
	}
	layout_close_menu();

	$elemlist = null;
	if ($conns = get_connections('topic', $topicid)) {
		$elemlist = _connect_get_links('topic', $topicid);
	}

	$replyable = true;
	if (have_user()) {
		layout_open_menu();
		if ($forum_admin = $GLOBALS['currentuser']->is_forum_admin($topic->forumid())
		||	(	$replyable
			&&	$topic->status() === 'open'
			&&	$GLOBALS['currentuser']->has_forum_write_access($topic->forumid())
			)
		) {
			$GLOBALS['__topicmayreply'] = true;
			layout_open_menuitem();
			include_js('js/comment');
			?><span class="<?= $replyable ? 'unhideanchor' : 'light' ?>" onclick="openReplyForm()"  id="response_00"><?= __C('action:post_reply') ?></span><?
			layout_close_menuitem();
		}
		layout_menuitem(Eelement_name('new_topic'),'/topic/form?FORUMID='.$topic->forumid());
		require_once '_subscriptions.inc';
		layout_open_menuitem();
		show_subscription_link('topic',$topic->topicid());
		layout_close_menuitem();

		if ($forum_admin) {
			require_once '_connect.inc';
			layout_next_menuitem();
			connect_menuitem();
			layout_close_menuitem();
		}

		if ($elemlist) {
			layout_continue_menu();
			layout_open_menuitem();
			echo __('field:connected_to') ?> <? echo implode(', ', $elemlist);
			layout_close_menuitem();
		}
		layout_close_menu();
	} else {
		layout_open_menu();
		if (!ROBOT
		&&	$replyable
		&&	$topic->status() === 'open'
		) {
			require_once '_layout.inc';
			layout_open_menuitem();
			invite_registration('forum','invite:place_message_LINE');
			layout_close_menuitem();
		}
		if ($elemlist) {
			layout_continue_menu();
			layout_open_menuitem();
			echo __('field:connected_to') ?> <? echo implode(', ', $elemlist);
			layout_close_menuitem();
		}
		layout_close_menu();
	}
	$__topicmenustr = ob_get_flush();
}

function forum_display_topic(): void {
	require_once '_topicstatus.inc';
	if (!($topicid = require_idnumber($_REQUEST,'sID'))) {
		return;
	}
	$topic = new _topic($topicid);
	global $currentuser;
	/** @noinspection NotOptimalIfConditionsInspection */
	if (!($forum_admin = $currentuser->is_forum_admin($forumid = $topic->forumid()))
	&&	$topic->status() === 'hidden'
	) {
		register_error('topic:error:removed_LINE');
		now_gone();
		return;
	}
	// does this user have rights to this forum?
	if (!$currentuser->has_forum_read_access($forumid)) {
		register_error('error:not_enough_rights_to_view_topic_LINE', ['ID' => $topicid]);
		no_permission();
		return;
	}
	if (isset($_REQUEST['PAGE'])) {
		if ($_REQUEST['PAGE'] === 'all') {
			$topic->page = 'all';
		} elseif (!require_idnumber($_REQUEST, 'PAGE')) {
			return;
		} else {
			$topic->page = $_REQUEST['PAGE'];
		}
	}
	if (false === $topic->query()) {
		return;
	}
	$have_rows = $topic->have_rows();

	ob_start();
	require_once '_topictypes.inc';
	?><a class="larger flat" href="<?= get_element_href('topic', $topicid, $topic->subject()) ?>"><?
		?><h1><?
		if ($longprefix = get_topic_longprefix('topic', $topicid, $topic->topicrow['FLAGS'])) {
			echo escape_specials($longprefix) ?>: <?
		}
		echo flat_with_entities($topic->subject(), UBB_UTF8 | UBB_NO_LINKS | UBB_SMILEYS);
		?></h1><?
	?></a><?
	$heading = ob_get_clean();
	ob_start();
	if ($conns = get_connections('topic',$topicid)) {
		$connstrs = [];
		foreach ($conns as $element => $ids) {
			foreach ($ids as $id) {
				if (!may_view_element($element, $id)) {
					continue;
				}
				ob_start();
				echo element_name($element === 'party' ? 'event' : $element) ?>: <b><?= get_element_link($element, $id) ?></b><?
				if ($element === 'party'
				&&	($party = memcached_party_and_stamp($id))
				) {
					?><small>, <? datedaytime_display_link_tzi($party['STAMP_TZI']) ?></small><?
				}
				$connstrs[] = ob_get_clean();
			}
		}
		if ($connstrs) {
			?><table class="vtop dens block"><?
			?><tr><?
			?><td><?= __C('attrib:concerning') ?>:&nbsp;</td><?
			?><td><?= implode('<br />', $connstrs) ?></td><?
			?></tr><?
			?></table><?
		}
	}
	$connpart = ob_get_clean();

	ob_start();
	if (!ROBOT) {
		require_once '_subscriptions.inc';
		$subs = subscription_count();
		$views = $topic->topicrow['VIEWS'];

		if ($subs) {
			echo $subs,' ',element_name('follower',$subs);
		}
		if ($views !== false) {
			if ($subs) {
				?> <?= MIDDLE_DOT_ENTITY ?> <?
			}
			echo $views ?>x <?= __('stats:viewed');
		}
	}
	$stats = ob_get_clean();

	layout_open_section_header();
		?><a href="<?= get_element_href('topic',$topicid) ?>"><?= Eelement_name('topic') ?></a><?
		?> <small class="light">&middot;&nbsp;<?= $topicid ?></small><?
		layout_continue_section_header();

		layout_display_offense_link($topic->topicrow['USERID'],'topic',$topicid,$forum_admin);
		layout_display_contacts('topic',$topicid,$topic->topicrow['USERID'],null,$topic->topicrow['FORUMID']);

	layout_close_section_header();

	topic_menu($topic);

	if (!$have_rows) {
		return;
	}

	require_once '_ticketlist.inc';
	show_connected_tickets();

	layout_open_box('white');
	layout_box_header($heading, $stats);
	if ($connpart) {
		echo $connpart;
	}
	layout_close_box();

#	show_middle_header($heading,$stats);

	if ($forum_admin) {
		require_once '_topicchanges.inc';
		show_topic_changes('topic',$topic->topicrow);

		require_once '_topicactionmenu.inc';
		show_topic_action_menu('topic',$topic->topicrow);
	}
	show_topic_status_box($topic);

	/** @noinspection NotOptimalIfConditionsInspection */
	if (empty($forum_admin)
	&&	have_user()											// registered user
	&&	CURRENTUSERID === $topic->userid()					// topic opener
	&&	(	$topic->topicrow['MUSERID'] === 0 				// wasn't changed
		||	CURRENTUSERID === $topic->topicrow['MUSERID'])	// wasn't closed by an admin.
	) {
		if ($topic->for_sale()) {
			?><div class="l block"><?
			?><form method="get" onsubmit="return submitForm(this)" action="/topic/<?= $topicid ?>/<?= ($open = $topic->status() === 'open') ? 'close' : 'open'; ?>"><?
			?><?= __('topic:info:sale_info_LINE')
			?> <input type="submit" value="<?= __($open ? 'action:close_topic' : 'action:reopen_topic'); ?>" /><?
			?></form><?
			?></div><?
		}
		if ($topic->topicrow['FORUM_FLAGS'] & FORUMFLAG_USER_CONNECT) {
			require_once '_topicactionmenu.inc';
			show_topic_connect_menu($topicid);
		}
	}

	if ($forum_admin) {
		_connect_display_form('topic',$topicid);
	}

	$topic->display();

	topic_menu($topic);

	if (!ROBOT) {
		require_once '_counthit.inc';
		counthit('topic', $topicid);
	}
}
function display_forum(): void {
	if (!($forumid = $_REQUEST['sID'])
	||	!memcached_forum($forumid)
	) {
		not_found();
		return;
	}

	// do we have access to this forum?
	// early return
	// there's a check in _topiclist->query too

	if (!$GLOBALS['currentuser']->has_forum_read_access($_REQUEST['FORUMID'])) {
		return;
	}

	require_once '_forum.inc';

	$topix = memcached_single('forum_stats','SELECT TOPIX FROM forum_stats WHERE FORUMID='.$_REQUEST['FORUMID']);
	$name = memcached_single('forum','SELECT NAME FROM forum WHERE FORUMID='.$_REQUEST['FORUMID']);
	layout_show_section_header();

	overview_menu();

	show_middle_header(get_element_link('forum',$forumid),$topix);

	single_menu($forumid);

	if ($_REQUEST['FORUMID'] === 1) {
		layout_open_box('white');
		?><p>Partyflock geeft leden in dit subforum de mogelijkheid van gedachten te wisselen over recreatief gebruik van genotsmiddelen.<br /><?
		?>We willen met klem benadrukken dat het gebruik van drugs verslavend kan zijn en (gezondheids)risico's met zich meebrengt.<br /><?
		?>Gebruik dus altijd je gezond verstand bij alles wat je hier leest en bespreekt.</p><?
		layout_close_box();

	}

	require_once '_topiclist.inc';

	$topiclist = new _topiclist;

	$topiclist->show_author = true;
	$topiclist->show_last	= true;
	$topiclist->show_header = true;
	$topiclist->show_heart = true;
	$topiclist->show_groups = true;
	$topiclist->in_forum($forumid);
	$topiclist->order_reverse_lstamp();
	if (!$topiclist->query()) {
		return;
	}
	$topiclist->display();
#	$topiclist->display_controls();
}
function forum_display_active_topics() {
	require_once '_topiclist.inc';
	layout_show_section_header(Eelement_plural_name('active_topic'));

	overview_menu();

	$topiclist = new _topiclist;
	$topiclist->show_author = true;
	$topiclist->show_header = true;
	$topiclist->show_heart = true;
	$topiclist->show_short = true;
	$topiclist->show_last = true;
	$topiclist->dont_select_hidden = true;
	$topiclist->order_reverse_lstamp();
#	$topiclist->force_index('LSTAMP');
	//$topiclist->only_past_few_days();
	if (!$topiclist->query()) {
		return;
	}
	if ($topiclist->have_rows()) {
		$topiclist->display();
#		$topiclist->display_controls();
	}
}
function forum_display_busy_topics() {
	require_once '_period.inc';
	if (!($info = get_period_form(
		'/forum/busy',
		$_REQUEST['SUBACTION'] ?? '',
		!empty($_SERVER['eDATA']) && $_SERVER['eDATA'] === 'progressive'))
	) {
		if ($info !== false) {
			not_found();
		}
		return;
	}
	[$form, $period, $progressive] = $info;
	require_once '_topiclist.inc';
	layout_show_section_header(Eelement_plural_name('busy_topic').' '.MIDDLE_DOT_ENTITY.' '.element_name('past_'.$_REQUEST['SUBACTION']));

	overview_menu();

	layout_open_box('white');
	echo $form;
	layout_close_box();

	$topiclist = new _topiclist;
	$topiclist->show_author = true;
	$topiclist->show_header = true;
	$topiclist->show_heart = true;
	$topiclist->show_short = true;
	$topiclist->show_last = true;

	$topiclist->busy($period,$progressive);

	if (!$topiclist->query()) {
		return;
	}
	if ($topiclist->have_rows()) {
		$topiclist->display();
#		$topiclist->display_controls();
	}
}
function forum_display_recent_interesting() {
	require_once '_topiclist.inc';

	layout_show_section_header(Eelement_plural_name('active_interesting_topic'));

	if (!require_user()) {
		return;
	}

	overview_menu();

	$topiclist = new _topiclist;

	foreach ($GLOBALS['currentuser']->forumsettings as $forumid => $setting) {
		if ($setting['SHOW_RECENT']) {
			$showrecents[] = $forumid;
		}
	}
	$topiclist->in_fora_or_subscribed(isset($showrecents) ? $showrecents : null);
	$topiclist->show_author = true;
	$topiclist->show_header = true;
	$topiclist->show_heart = true;
	$topiclist->show_short = true;
	$topiclist->show_last = true;
	$topiclist->order_reverse_lstamp();
	if (!$topiclist->query()) {
		return;
	}
	if ($topiclist->have_rows()) {
		$topiclist->display();
#		$topiclist->display_controls();
	}
}
function forum_display_news() {
	require_once '_topiclist.inc';

	$topiclist = new _topiclist;

	layout_show_section_header(Eelement_plural_name('newest_topic').' '.MIDDLE_DOT_ENTITY.' '.$topiclist->MAX_TOPICS_PER_PAGE);

	overview_menu();

	$topiclist->show_author = true;
	$topiclist->show_header = true;
	$topiclist->show_heart = true;
	$topiclist->show_short = true;
	$topiclist->show_last = true;
	$topiclist->show_cstamp_time = true;
	$topiclist->order_reverse_cstamp();
	$topiclist->dont_select_hidden = true;
	$topiclist->not_in_forum(60);
	if ($_REQUEST['sID']) {
		$topiclist->in_forum($_REQUEST['sID']);
	}
#	$topiclist->force_index('PRIMARY');

	if (!$topiclist->query()) return;

	if ($topiclist->have_rows()) {
		$topiclist->display();
#		$topiclist->display_controls();
	}
}
function forum_display_active_history() {
	if (!require_existing_user($_REQUEST,'subID')) {
		return;
	}
	layout_show_section_header(Eelement_plural_name('active_participation').' '.MIDDLE_DOT_ENTITY.' '.get_element_link('user',$_REQUEST['subID']));

	overview_menu();

	require_once '_topiclist.inc';

	$topiclist = new _topiclist;
	$topiclist->order_reverse_lstamp();
	$topiclist->show_diff_self = false;
	$topiclist->show_diff = true;
	$topiclist->in_which_user_posted(CURRENTUSERID);
	$topiclist->show_author = true;
	$topiclist->show_header = true;
	$topiclist->show_short = true;
	$topiclist->show_groups = false;
	$topiclist->show_last = true;
	if (!$topiclist->query()) {
		return;
	}
	////////////// show's only self messages


#	$topiclist->display_controls();
	$topiclist->display();
#	$topiclist->display_controls();
}
function forum_display_history() {
	require_once '_visibility.inc';
	require_once '_offense_access.inc';
	if (!($userid = require_existing_user($_REQUEST,'subID'))) {
		http_status(404);
		return;
	}
	if (!require_visible_esetting($userid,'STATISTICS',have_admin('helpdesk') || have_offense_admin())) {
		http_status(403);
		return;
	}
	layout_show_section_header(Eelement_name('participation_history').' '.MIDDLE_DOT_ENTITY.' '.get_element_link('user',$_REQUEST['subID']));

	overview_menu();

	require_once '_topiclist.inc';
	$topiclist = new _topiclist;
	if (isset($_REQUEST['SELF'])) {
		$topiclist->order_reverse_lstamp();
		$topiclist->show_diff_self = false;
		$topiclist->show_diff = true;
	} else {
		$topiclist->order_reverse_lastpost();
		$topiclist->show_diff_self = true;
		$topiclist->show_diff = false;
	}
	$topiclist->in_which_user_posted($_REQUEST['subID']);
	$topiclist->show_author = true;
	$topiclist->show_header = true;
	$topiclist->show_short = true;
	$topiclist->show_groups = false;
	$topiclist->show_last = true;
	if (!$topiclist->query()) {
		return;
	}

#	$topiclist->display_controls();
	$topiclist->display();
#	$topiclist->display_controls();
}
function show_single_connect(int $topicid, bool $help = true) {
	global $__connopts;
	?><span class="ib nowrap"><?
	?><form method="get" onsubmit="return submitForm(this)" action="/topic/<?= $topicid ?>/connect"><?
	if ($__connopts) {
		?><select name="PARTYID"><?
		foreach ($__connopts as $party) {
			?><option value="<?= $party['PARTYID'];
			?>"><? _date_display_numeric($party['STAMP']);
			?>: <? echo escape_utf8($party['NAME']);
			if ($party['SUBTITLE']) {
				?> <?= MIDDLE_DOT_ENTITY ?> <?
				echo flat_with_entities($party['SUBTITLE'], UBB_UTF8);
			}
			if ($party['CANCELLED']) {
				?> (<?= __('status:cancelled') ?>)<?
			}
			?></option><?
		}
		?></select><?
	} else {
		show_input([
			'name'			=> 'NAME',
			'type'			=> 'text',
			'required'		=> true,
			'placeholder'	=> $help ? element_name('name_or_party_id') : null
		]);
	}
	?> <input type="submit" value="<?= __('action:connect') ?>" /></form><?
	?></span><?
}
