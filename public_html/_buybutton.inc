<?php

declare(strict_types=1);

require_once '_presale.inc';

function show_link(int $partyid, array $args, ?string $extra_class = null): void {
	require_once '_shoplink.inc';
	require_once '_ticketseller.inc';
	if (!array_key_exists('main_link', $args)) {
		if (!($party = memcached_party_and_stamp($partyid))) {
			return;
		}
		if ($party['STAMP'] + $party['DURATION_SECS'] > CURRENTSTAMP) {
			mail_log('no main_link for presale for future event '.$partyid);
		}
	}
	$main_info = $args['main_info'] ?: null;
	if ((	!isset($main_info)
		||	!is_int($main_info)
		)
		&&	empty($args['main_link'])
	) {
		return;
	}
	?><div class="forcewrap notice"><?
	if ($extra_class) {
		# when class colorless with grayscale filter is passed,<
		# span makes sure the filter does not make the entire div overlap with other html nodes
		?><span class="<?= $extra_class ?>"><?
	}
	if (!empty($args['presaleinfo']['SELLERID'])
	&&	($seller = get_seller($args['presaleinfo']['SELLERID']))
	) {
		echo escape_utf8(mb_strtoupper($seller));
	} else {
		# Legacy naming for sellers we have not fully converted yet
		echo is_number($main_info) ? 'SITE' : $main_info;
	}
	?>: <?
	ob_start();
	if (is_number($main_info)) {
		echo escape_utf8($args['websites'][$main_info]);
	} elseif (preg_match('"/order_ticket/(?<type>[^/]+)/"', $args['main_link'], $match)) {
		if (!($url = get_shop_url($match['type'], $partyid))) {
			mail_log("could not obtain shop url for event $partyid", get_defined_vars(), error_log: 'ERROR');
		} else {
			echo escape_utf8(cleanup_url($url, true));
		}
	} else {
		echo escape_utf8(cleanup_url($args['main_link'], true));
	}
	$href = ob_get_clean();
	?><a class="ns light" href="<?= $href ?>" target="_blank"><?= $href ?></a><?
	if ($extra_class) {
		?></span><?
	}
	?></div><?
}

/**
 * @param array{
 *		partyid:			positive-int,
 *		class?:				string,
 *		href?:				string,
 *		onclick?:			string,
 *		party?:				array,
 *		presaleinfo?:		array,
 *		icons?:				array<string, string>,
 *		main_link?:			string,
 *		main_info?:			string,
 *		websites?:			array<string>,
 *		partner?:			bool,
 *		force_customer?:	bool,
 *		ad?:				bool,
 *		hide_ticketswap?:	bool,
 * } $args
 */
function show_buy_buttons(array $args): void {
	require_once '_custombutton.inc';
	require_once '_memcache.inc';
	require_once '_presale.inc';
	require_once '_shoplink.inc';
	require_once '_ticketswap.inc';
	require_once '_ticketseller.inc';

	$always_show_to_admins = have_admin(['appic', 'party', 'helpdesk']);
	$partyid = $args['partyid'] ?? null;

	if (!$partyid
	||	!($party ??= memcached_party_and_stamp($partyid))
	||	!($in_future = CURRENTSTAMP < $party['STAMP'] + ($party['DURATION_SECS'] ?: 6 * ONE_HOUR))
	&&	!$always_show_to_admins
	) {
		return;
	}
		   $ticketswap_button = null;
			   $ticket_button = null;
	$vakantieveilingen_button = null;

	if (($in_future || $always_show_to_admins)
	&&	empty($args['hide_ticketswap'])
	&&	!empty($args['presaleinfo']['TICKETSWAP'])
	&&	(	# Show TicketSwap always when non-partner
			 empty($args['partner'])
			# Always show ticketswap when sold out
		||	!empty($party['SOLD_OUT'])
			# Show ticketswap when partner is not a customer
		||	in_array($args['partner'],['Free', 'Prospect'], true))
	) {
		$info = get_ticketswap_info_from_cache($partyid, $stale);
		ob_start();
		?><a rel="nofollow" class="middle" target="_blank" href="/order_ticket/ticketswap/<?= $partyid ?>"><?
		?><div class="action-button generic-button ticketswap relative"><?
		if ($in_future) {
			?><div<?
			?> id="ticketswapcnt"<?
			?> class="abs big blueC circle<?
			if (!($available = $info ? $info['AVAILABLE'] : 0)) {
				?> hidden<?
			}
			?>"><?
			if ($available) {
				echo $available;
			}
			?></div><?
			if (isset($_REQUEST['NOMEMCACHE'])
			||	$info === false
			||	$stale
			) {
				include_js('js/ticketswap', onload: /** @lang JavaScript */ "
				addreadyevent(function() {
					Pf_checkTicketSwap($partyid);
				});");
			}
		}
		?><img class="colorless" src="<?= STATIC_HOST ?>/images/ticketswap.png" alt="TicketSwap" /><?
		?></div><?
		?></a><?
		$ticketswap_button = ob_get_clean();
	}
	if (empty($args['onclick'])) {
		if (!$always_show_to_admins
		&&	(	!($party = memcached_party_and_stamp($partyid))
			||	$party['STAMP'] < CURRENTSTAMP
			||	(	$party['PRESALE_STAMP'] > CURRENTSTAMP
				&&	empty($ticketswap_button)
				)
			||	$party['SOLD_OUT']
			||	$party['PRESALE_SOLD_OUT']
			||	$party['CANCELLED'])
		) {
			return;
		}
		$presaleinfo ??= get_presale_info('party', $partyid);
		$locals = prepare_presale_info($presaleinfo, $party);
		$args['locals'] = $locals;
		/** @noinspection PhpRedundantOptionalArgumentInspection */
		extract($locals, \EXTR_OVERWRITE);
		/*$allow_button =
			$main_link
		&&	preg_match('!href="([^"]*)".*?onclick="([^"]*)"!', $main_link, $match);

		$allow_button = true;

		if (!$allow_button) {
			$href = null;
		} else {*/
			[$href, $onclick] = get_generic_presale_link($partyid, $party);
		//}
	}

	$non_customer = false;

	if (empty($args['force_customer'])) {
		if (false === ($partner ??= memcached_single_string('appic_event', '
			SELECT PARTNER
			FROM appic_event
			WHERE PARTYID = '.$party['MAIN_ID']))
		) {
			return;
		}
		if (!$partner
		||	 $partner === 'Prospect'
		) {
			$non_customer = true;
		}
	}

	$is_appic_shop =
		!empty($args['main_info'])
	&&	$args['main_info'] === 'APPIC';

	if (!empty($href)) {
		ob_start();
		# show only tickswap for non-forced ticketswaps
		?><a<?
		if (!$is_appic_shop) {
			?> rel="nofollow"<?
		}
		?> class="middle"<?
		?> target="_blank"<?
		if ($onclick) {
			?> onclick="<?= escape_specials($onclick) ?>"<?
		}
		?> href="<?= escape_specials($is_appic_shop && ROBOT ? get_shop_url('appic', $partyid) : $href) ?>"<?
		?>><?
			?><div class="action-button generic-button buy"><?= __C(
				$party['LIVESTREAM'] === 'only'
			&&	$party['FREE_ENTRANCE']
			?	'action:donate'
			:	'action:buy_tickets')
			?></div><?
		?></a><?
		$ticket_button = ob_get_clean();
	}
	if (!empty($args['icons'])
	&&	!empty($args['icons'][0])
	&&	preg_match('!<a\s+([^>]+)>(<img[^>]*>)!i', $args['icons'][0], $match)
	) {
		ob_start();
		# vakantie veilingen
		[, $inner_anchor, $img] = $match;
		?><a <?= $inner_anchor ?>><?
		?><div class="ib generic-button vv"><?= $img ?></div><?
		?></a><?
		$vakantieveilingen_button = ob_get_clean();
	}
	$custom_button = get_custombutton_and_count('party', $partyid);

	if ($ticket_button
	||	$ticketswap_button
	||	$vakantieveilingen_button
	||	$custom_button
	) {
		?><div class="event-actions<?
		if ($non_customer) {
			?> noncust<?
		}
		if (!$in_future) {
			?> light colorless<?
		}
		if (!empty($args['class'])) {
			?> <? echo $args['class'];
		}
		?>"><?=
			$ticket_button, ' ',
			$ticketswap_button, ' ',
			$vakantieveilingen_button, ' ',
			$custom_button
		?></div><?
	}
	if (!empty($_REQUEST['sELEMENT'])
	&&	$_REQUEST['sELEMENT'] === 'party'
	&&	$_REQUEST['sID'] === $party['PARTYID']
	&&	have_admin(['party', 'helpdesk', 'appic'])
	) {
		require_once '_shoplink.inc';
		require_once '_url.inc';
		ob_start();
		if (!$is_appic_shop
		&&	!empty($args['presaleinfo']['APPIC_GID'])
		) {
			$original_args = $args;
			$args['presaleinfo']['SELLERID'] = get_seller_id('appic');
			$args['presaleinfo']['APPIC'] = $args['presaleinfo']['APPIC_GID'];
			$args['main_link'] = get_shop_url('appic', $partyid, $args['presaleinfo']);
			$args['main_info'] = 'APPIC';
			# Show inactive Appic shop link
			show_link($partyid, $args, 'small colorless');
			$args = $original_args;

		#} elseif (!str_starts_with($args['main_link'], 'http')) {
			# Legacy shops that have their own fields (still) in presaleinfo.
			#$args['main_link'] = get_shop_url(strtolower($args['main_link']), $partyid, $args['presaleinfo']);
		}
		# Show active link (either Appic shop or organizer shop)
		show_link($partyid, $args);

		if ($is_appic_shop
			# Appic shop is currently active, and thus was shown in show_link call above.
			# Display the organizer shop link now, grayed out.
		&&	($presaleinfo ??= get_presale_info('party',$partyid))
		) {
			$presaleinfo['APPIC'] = null;
			if (!$presaleinfo['WEBSITE']
			||	!$presaleinfo['SELLERID']
			) {
				# Doing legacy preparations for non SELLERID/SITE presales
				$args = prepare_presale_info($presaleinfo, $party);
			} else {
				$args['presaleinfo'] = $presaleinfo;
			}
			show_link($partyid, $args, 'small colorless');
		}
		if ($appic_stuff = ob_get_clean()) {
			?><div class="block"><?= $appic_stuff ?></div><?
		}
	}
}
