<?php

function reset_reach($promoid) {
	$needreset = db_single('promo','SELECT 1 FROM promo WHERE (RSTAMP!=0 OR SENT_STAMP!=0) AND PROMOID='.$promoid);
	if ($needreset === false) {
		return false;
	}
	if (!$needreset) {
		return true;
	}
	if (!remove_reach($promoid)
	||	!db_update('promo','
		UPDATE promo SET RSTAMP=0,SENT_STAMP=0,REACH=0
		WHERE PROMOID='.$promoid)
	) {
		return false;
	}
	_notice('Bereik verwijderd.');
	return true;
}
function remove_reach($promoid) {
	return	remove_letters($promoid,'promo_letter_concept')
	&&	remove_letters($promoid,'promo_letter_v2');
}
