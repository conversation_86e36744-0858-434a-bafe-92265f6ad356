<?php

declare(strict_types = 1);

function get_ascii_url(string $url, bool $utf8 = false): string {
	return preg_replace_callback('[^\x20-\x7F]'.($utf8 ? 'u' : ''), static fn($match) => urlencode($match[0]), $url);
}

function is_safe_url(string $url): bool {
	return  $url
		&&  !str_starts_with($url, 'java')
		&&  !str_contains($url, 'script:')
		&&  !str_contains($url, '/logoff')
		&&  !str_contains($url, '/setuser');
}

function parse_utf8_url(string $url): array|false {
	/** @noinspection RequiredAttributes */
	if (preg_match(
		/** @lang PhpRegExp */
			'!^((?<scheme>[^:/?#]+)://)?(?:(?<user>[^:]+):(?<pass>[^@]+)@)?(?<host>[^/?:#]*)(:(?<port>\d+))?'.
		/** @lang PhpRegExp */
			'(?<path>[^?#]*)(\\?(?<query>[^#]*))?(#(?<fragment>.*))?!u',
		$url,
		$match)
	) {
		foreach ($match as $key => $value) {
			if (is_int($key)
			||	!$value
			) {
				unset($match[$key]);
			}
		}
		return $match;
	}
	return false;
}

function get_url_for_display(string $url,bool &$utf8 = false): array {
	$url = strip_white_space($url, $utf8);
	if (str_contains($url, '/')) {
		$parsed = ($utf8 ? parse_utf8_url(...) : parse_url(...))($url);
		$host = $parsed['host'] ?? null;
		$path = $parsed['path'] ?? null;
		if (!$host) {
			$host = $url;
			mail_log('could not understand url for display: '.$url, item: $parsed);
		}
	} else {
		$host = $url;
		$path = null;
	}
	if (str_starts_with($host, 'xn--')) {
		$host = idn_to_utf8($host);
		$utf8 = true;
	}
	$host = preg_replace('"^(?:ftp|www|m(?:obile)?|[a-z]{2})\."i'.($utf8 ? 'u' : ''), '', $host);
	# path[0] is always /
	# Of path[1] exists, we have an actual path
	return [$host, isset($path[1]) ? $path : false];
}

function make_absolute_url(string $url): string {
	if (!$url) {
		return $url;
	}
	if ($url[0] === '/') {
		if (isset($url[1]) && $url[1] === '/') {
			return 'https:'.$url;
		}
		return FULL_HOST.$url;
	}
	# NOTE: We don't know whether the content here will be a valid protocol (https, ftp, etc)
	#		We never checked and something for the future maybe.
	return $url;
}

function cleanup_url(string $url, bool $utf8 = false): string {
	static $__cleaned;

	if (isset($__cleaned[$url])) {
		return $__cleaned[$url];
	}

	require_once '_helper.inc';
	require_once '_flockmod.inc';

	if (!($url = strip_shy($url, utf8: $utf8))
	||	!($url = mytrim($url, utf8: $utf8))
	) {
		return $__cleaned[$url] = '';
	}

	$input_url = $url;

	require_once '_unicode.inc';

	$utf8_mod = $utf8 ? 'u' : '';

	$url = mytrim($url, '.!?#&', utf8: $utf8);

	# substitute HTML entities with the actual characters
#	$url = html_entity_decode($url);

	$url = str_ireplace(
		[	$utf8 ? SOFT_HYPHEN : SOFT_HYPHEN_LATIN1,
			'&8203;',	# zero width space
			'&amp;amp;',
			'&amp;',
			'//play.spotify.com/',
			'[url]',
			'[/url]',
		],
		[	'',
			'',
			'&',
			'&',
			'//open.spotify.com/',
			'',
			'',
		],
		$url
	);
	if (preg_match('"^(?<scheme>https?:/?/?){2,}(?<remainder>.*)$"'.$utf8_mod, $url, $match)) {
		$url = $match['scheme'].$match['remainder'];
	}
	// Note sure if this works properly:
	// if (preg_match('"queue-it\.(?:net|com)/.*(%3A%2F%2F[^&?]*)"', $url, $match)) {
	// 	return cleanup_url('https'.urldecode($match[1]));
	// }
	if ($input_url[0] !== '/'
	&&	preg_match('"^(?<view_source>view-source:)?(?<url>(?:https?://)?(?<fqdn>(?:(?<subdomain>[^/]+?)\.)?(?<domain>[^/]+)\.(?<extension>[a-z]+))(?:/|\?|$).*?)$"i'.$utf8_mod, $url, $match)
	) {
		/** @noinspection PhpRedundantOptionalArgumentInspection */
		extract($match, \EXTR_OVERWRITE);

		# if $view_source is set, $url has been overwritten with the url minus view-source prefix

		# Convert mobile URLs or URLs with country/language subdomain, into standard URLs.
		# For youtube, check only m for mobile domain. This way, music subdomain is kept.
		static $__standardize = [
			# domain		=> want_extension	find_subdomain	want_subdomain
			'chipta'		=> ['com',			null,			'www'],
			'facebook'		=> ['com',			null,			'www'],
			'soundcloud'	=> ['com',			null,			''],
			'twitter' 		=> ['com',			null,			''],
			'youtube'		=> ['com',			'm',			'www'],
		];
		if ( $domain
		&&	($standardize = $__standardize[$domain] ?? null)
		&&	([$want_extension, $find_subdomain, $want_subdomain] = $standardize)
		&&	(	!$find_subdomain
			||	isset($subdomain)
			&&	 $find_subdomain === $subdomain)
		&&	(	$subdomain !== $want_subdomain
			||	$extension !== $want_extension)
		) {
			$url = str_replace($fqdn, ($want_subdomain ? $want_subdomain.'.' : '').$domain.'.'.$want_extension, $url);
		}
	}
	require_once 'defines/languages.inc';
	$shorts = '(?:'.implodekeys('|', [...LANGUAGE_CODES, ...FAKE_LANGUAGE_CODES]).')';

	# facebook remnants
	$url = preg_replace('"%3FFacebookPixelID%3D\d+"i'.$utf8_mod, '', $url) ?? $url;
	# language cleanup
	$url = (preg_replace('"/language/[a-z]{2}\b"i'.$utf8_mod, '', $url) ?? $url);

	if (preg_match('"//(itunes|music)\.apple\.com/(?:[a-z]{2}/)?(artist)/([^/]+)/((?:id)?\d+)"'.$utf8_mod, $url, $match)) {
		$url = "https://$match[1].apple.com/$match[2]/$match[3]/$match[4]";

	} elseif (false !== ($utf8 ? mb_stripos(...) : stripos(...))($url, 'facebook')) {
		if (preg_match('"https?://(?:[a-z.-]+\.)?facebook\.com/events/(\d+)(?:/?\?event_time_id=|/)(\d+)"'.$utf8_mod, $url, $match)) {
			[, $mainid, $instanceid] = $match;
			if ($mainid !== $instanceid) {
				# WARNING::	This is not real safe now is it? It's easy to fill the database with bad ID pairs,
				#		 	by just entering phony Facebook ID in multi-day URLs.
				db_insert('feedevent_multiday', "
				INSERT IGNORE INTO feedevent_multiday SET
					MAINID		= $mainid,
					INSTANCEID	= $instanceid"
				);
			}
		}
		$url = preg_replace('"([a-z]{2,3}-[a-z]{2,3}\.facebook\.com)"'.$utf8_mod, 'www.facebook.com', $url) ?? $url;

		if (preg_match(/** @lang PhpRegExp */ '"^(?<prefix>.*facebook\.\w{2,4}).*?#!(?<hash_path>.*)$"i'.$utf8_mod, $url, $match)) {
			$url = $match['prefix'].$match['hash_path'];
		}
	} elseif (preg_match(/** @lang PhpRegExp */ '"^https?://(?:api|web)\.whatsapp\.com/send/?\?phone=(?:\+|%2B)?(?<phone_number>\d+)"', $url, $match)) {
		$url = "https://wa.me/{$match['phone_number']}";

	}
	if (preg_match(/** @lang PhpRegExp */ '"^(?<prefix>.*)#!(?<hash_path>.*)$"'.$utf8_mod, $url, $match)) {
		$url = $match['prefix'].$match['hash_path'];
		$url = preg_replace('"/{2,}"'.$utf8_mod, '/', $url) ?? $url;
	}
	# Remove 2 lette country code at end of URL.
	# Ticketkantoor has shops with valid 2 chars that should not be stripped.
	#
	# Possible ticketkantoor URLs. It uses two chars at the end to indicate what shop:
	#
	# https://www.ticketkantoor.nl/shop/BH
	# https://www.ticketkantoor.nl/shop/bg
	#
	# Those are valid and the two letters don't indicate the language.
	# Replacement below removed trailing language from URLs.

	if (false !== ($utf8 ? mb_stripos(...) : stripos(...))($url, 'ticketkantoor.nl')) {
		$url = preg_replace("!/$shorts/?$!i$utf8_mod", '', $url) ?? $url;
	}
	# Remove 2 letter country code immediately following the host of the URL.
	# Requires more character after the two letters.
	if (preg_match("!^(?<prefix>https?://[^/]+)/$shorts(?<postfix>/.*|)\$!i$utf8_mod", $url, $match)) {
		$url = $match['prefix'].$match['postfix'];
	}
	# youtube
	$url = preg_replace('"/(?:featured|videos)$"i'.$utf8_mod, '', $url) ?? $url;
	# spotify
	$url = preg_replace('!/intl-[a-z]{2}/!is'.$utf8_mod, '/', $url) ?? $url;
	# remove country/language specified from resident advisor
	$url = preg_replace('"^https?://(?!imgproxy)(?:[a-z]{2}|www)\.ra\.co/"i'.$utf8_mod, 'https://ra.co/', $url) ?? $url;
	# event-tickets
	if (preg_match('"event-tickets\.be.*?(?<remove>/[a-z]{2}/?(?:facebook)?)$"'.$utf8_mod, $url, $match)) {
		$url = ($utf8 ? mb_substr(...) : substr(...))($url, 0, -($utf8 ? mb_strlen(...) : strlen(...))($match['remove']));
	}
	# fourvenues
	$url = preg_replace('"^https://www\.fourvenues\.com/[a-z]{2}/(?:iframe/)?(.+)$"'.$utf8_mod, 'https://www.fourvenues.com/$1', $url) ?? $url;
	# index.(php|html) is default page so remove it
	if (preg_match('"/index\.(?:php|html)$"i'.$utf8_mod, $url)) {
		$url = preg_replace('"/index\.(?:php|html)(\?.*?)?$"i'.$utf8_mod, '/', $url) ?? $url;
	}
	/** @noinspection DuplicatedCode */
	$url = preg_replace('"(?<=[&?])(?:'.
		'(?:f|pn|hc_)?ref(?:_type)?=[^&]*|'.
		'_rdr(?:=[^&]*)?\b|'.
		'sk=(?!group)[a-z]+|'.
		'igsh(?:id)?=[^&]*|'.
		'(?:directed_target_id|source)=[^&]*|'.
		'__(?:xts|tn)__(?:(?:\[|%5B)[a-z]+(?:]|%5D))?=[^&]*|'.
		'(?:hc_location|tn-str)=[^&]*|'.
		'tab=[^&]*|'.
		'[pc]=[^&]*|'.
		'(?:irgwc|clickid|aff_id)=[^&]*|'.
		'cookieCheck=[^&]*|'.
		'_+g=[*&^]*|'.
		# language cleanup:
		'(?:hl|lang(?:uage)?)=[^&]*|'.
		# music.amazon.(co.uk|com)
		'ascsubtag=[a-f\d]*|'.
		'ie=UTF8|'.
		'linkCode=[a-z\d]*|'.
		'tag=[a-z\d-]*|'.
		# bandsintown:
		'affil_code=[^&]*|'.
		'app_id=[a-z\d._=]*|'.
		'came_from=\d+|'.
		# billeto:'
		'vip_token=[a-z]*|'.
		# checkout-ds24:
		'(?:is_in_iframe=1|ds24ref=http[^&]*)|'.
		# chipta:
		'(?:iframe|transparent-background)=1|'.
		# cm:
		'queueToken=[^&]*|'.
		# deezer:
		'app_id=\d*|'.
		'deferredFl=\d*|'.
		# dice:
		'dice_[a-z]+=[^&]*|'.
		# discogs:
		'srsltid=[^&]*|'.
		# docs.google.com (preregistration)
		'(?:edit_requested|usp)=[^&]*|'.
		'(?:[cw]|uiv)=\d|'.
		# entradauno.com:
		'h=[a-f\d]*|'.
		# eventbrite
		'parent=[^&]*|'.
		# eventgoose:
		'(?:queuepasscode|multiSalesChannel)=[^&]*|'.
		# eventim:
		'affiliate=[a-z\d]*|'.
		# eventix:
		'(?:shop_code|original_referer)=[^&]*|'.
		# eventsquare:
		'preview_token=[a-f\d]{8}-[a-f\d]{4}-[a-f\d]{4}-[a-f\d]{4}-[a-f\d]{12}|'.
		# facebook:
		'(?:AnalyticsUID|FacebookPixelID|stateFinalized|(?:fb|g)clid|igshid|mibextid)=[^&]*|'.
		# festicket:
		'page_mode=[^&]*|'.
		# fever:
		'(?:ad_ad|cp_landing(?:_(?:source|term))?)=[^&]*|'.
		'thm=\d*|'.
		# google analytics:
		'(?:lt_)?utm_(?:content|source|term|medium|campaign|referer_info)(?:=[^&]*)?|'.
		'_g[la]=[^&]*|'.
		# instagram (keep img_index, it points to specific image)
		'(?:api|next)=[^&]*|'.
		# invista:
		'eventsubid=[a-zA-Z\d%]*|'.
		'shopid=\d+|'.
		# linkedin
		'(?:originalSubdomain|trk)=[^&]*|'.
		# linktree
		'ltsid=[^&]*|'.
		# mailchimp:
		'mc_[ce]id=[^&]+|'.
		# messenger:
		'messaging_source=[^&]*|'.
		'source_id=\d*|'.
		'recurring_notification=\d*|'.
		# mijnetickets.shop:
		'referrer=[^&]*|'.
		# novotix:
		# agenda=7WE8C-IXNW5W4-65712F0455E70215F15AC34A2A0494B88F45FF79
		'agenda=[A-Z\d]{5}-[A-Z\d]{7}-[A-F\d]{40}(*pla:&|$)|'.
		# embed=1c63a04-516d-49bb-1ee7-1e4353674575e8af044acb8a72a7f2b5745e3220
		'embed=[a-f\d]{7}-[a-f\d]{4}-[a-f\d]{4}-[a-f\d]{4}-[a-f\d]{40}(*pla:&|$)|'.
		# lng=4
		'lng=\d+|'.
		# paradiso
		'(?:error|state)=[^&]*|'.
		# paylogic:
		'(?:token|ssoToken)=[a-zA-Z\d\-_.]{100,}|'.
		'token=[a-zA-Z\d]{5,6}|'.
		'access_id=[a-f\d]{64}|'.
		'lid=[a-z\d]*|'.
		# don't strip flow= from any URL, breaks ticketmatic for example
		'changePassword=[^&]*|'.
		# paypal
		'(?:country|locale)\.x=[^&]*|'.
		# qtickets:
		'(?:viewcart|oqs)=[^&]*|'.
		# queue-it:
		'queueittoken=[^&]*|'.
		'qit[pq]=[a-f\d]{8}-[a-f\d]{4}-[a-f\d]{4}-[a-f\d]{4}-[a-f\d]{12}|'.
		'qitts=\d+|'.
		'(?:qit[ce]|qitrt)=[a-z]+|'.
		'qith=[a-f\d]+|'.
		# seeticket.us
		'afflky=[^&]*|'.
		# skiddle:
		'mcp_token=[^&]*|'.
		# snapchat
		'(?:share_id|sid)=[^&]*|'.
		# somewhere:
		'referer_info=[^&]*|'.
		'affiliate=[^&]*&id=[^&]*|'.
		'frompartner=[^&]*|'.
		'aid=[^&]*|'.
		# spotify:
		'nd=\d*|'.
		'go=\d|'.
		'pt_success=\d+|'.
		'(?:(?:auto)?play|dl_branch|sp_cid)=[^&]*|'.
		'_branch_(?:referrer|match_id)=[^&]*|'.
		'v=A\b|'.
		'locale=[^&]*|'.
		'preview=none|'.
		'(?:dl)?si=[^&]*|'.
		'pi=[a-z\d_-]*|'.
		'context=[^&]*|'.
		# threads:
		'xmt=[^&]*|'.
		# ticketmaster:
		'(?:clickId|ircid)=[^&]*|'.
		'(?:CAMEFROM|CL_ORIGIN|aff|brand|refer(?:er)?)=[^&]*|'.
		# ticketmatic:
		'l=[a-z]{2}|'.
		# ticketpay:
		'session_token=[^&]*|'.
		# ticketstream:
		# Keep the booker parameter, it sometimes enable more ticket options:
		# 'booker=[a-z\d]*|'.
		'fullbooker=yes|'.
		'tsid=[a-z\d]*|'.
		# tickettailor:
		'(?:modal_)?widget=[^&]*|'.
		'userToken=[a-f\d]*|'.
		# tiktok:
		'(?:'.	 'checksum'.
				'|embed_(?:source|creator_card)'.
				'|is_from_webapp'.
				'|is_copy_url'.
				'|referer_url'.
				'|sender_device'.
				'|sec_(?:uid|user_id)'.
				'|share_(?:app_(?:id|name)|author_id|link_id)'.
				'|timestamp'.
				'|u_code'.
				'|user_id'.
				'|ug_[a-z]+'.
				'|_[rtd]'.
		')=[^&]*|'.
		# ticketweb:
		'REFID=[^&]*|'.
		# tivoli vredenburg:
		'_gac=[^&]*|'.
		# tiqs:
		'tag=\d+|'.
		# twitch:
		'tt_[a-z]+=[^&]*|'.
		# weezevent:
		'color_primary=[\da-f]+|'.
		'width_auto=[01]|'.
		'neo=[01]|'.
		# X:
		'ref_src=[^&]*|'.
		# unknown:
		's=\d*|'.
		't=[a-zA-Z\d]*|'.
		'cid=[a-z]{2}-[a-z]{2}|'.
		'came_from=https[^&]+|'.
		# whatsapp:
		'type=phone_number|'.
		'app_absent=[^&]*|'.
		# youtube:
		'(?:disable_polymer|feature|sub_confirmation|view_as|ytsession|app)=[^&]*|'.
		# yurplan:
		'culture=[a-z]{2}|'.
		'from=[a-z]*|'.
		'info=0|'.
		'yp-wr-token=[^&]*|'.
		# ziggodome:
		'pageFrom=[a-z]*'.
	')|'.
	# ticketmatic:
	'#!?/?(?:addtickets)?$'.
	'"i'.$utf8_mod, '', $url) ?? $url;

	$url = preg_replace(
			['"&+"'.$utf8_mod, '"&+#"'.$utf8_mod, '"\?&+"'.$utf8_mod],
		['&',			   '#',				  '?'],
		$url
	) ?? $url;

	echo "before trim: $url\n";
	$url = myrtrim($url, '?&#', utf8: $utf8);
	echo "after trim: $url\n";

	return $__cleaned[$input_url] = $url;
}
