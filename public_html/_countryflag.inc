<?php

declare(strict_types=1);

const CODE_TO_FLAG = [
	'A' => 0x1f1e6,
	'B' => 0x1f1e7,
	'C' => 0x1f1e8,
	'D' => 0x1f1e9,
	'E' => 0x1f1ea,
	'F' => 0x1f1eb,
	'G' => 0x1f1ec,
	'H' => 0x1f1ed,
	'I' => 0x1f1ee,
	'J' => 0x1f1ef,
	'K' => 0x1f1f0,
	'L' => 0x1f1f1,
	'M' => 0x1f1f2,
	'N' => 0x1f1f3,
	'O' => 0x1f1f4,
	'P' => 0x1f1f5,
	'Q' => 0x1f1f6,
	'R' => 0x1f1f7,
	'S' => 0x1f1f8,
	'T' => 0x1f1f9,
	'U' => 0x1f1fa,
	'V' => 0x1f1fb,
	'W' => 0x1f1fc,
	'X' => 0x1f1fd,
	'Y' => 0x1f1fe,
	'Z' => 0x1f1ff
];

const OBSOLETE_FLAG_REPLACEMENT = [
	21 => 10,
	37 => 8
];

function get_country_flag(
	int		$countryid,
	?string $class	= null,
	bool	$link	= false,
	?string	$name	= null,
	?string	$alt	= null,
): string {
	ob_start();
	show_country_flag($countryid, $class, $link, $name, $alt);
	return ob_get_clean();
}

const FLAG_BITMAP	= 1;
const FLAG_SVG		= 2;

function flag_available(
	int $countryid,
	int $type = 0
): int|false {
	static	$__available = memcached_simple_hash(['countryflag', 'countryflagsvg'], /** @lang MariaDB */ '
				SELECT COUNTRYID, '.FLAG_BITMAP.',	0	  FROM countryflag
		UNION	SELECT COUNTRYID, '.FLAG_SVG.',		RATIO FROM countryflagsvg',
		TEN_MINUTES) ?: false;
	if ($__available === false) {
		return false;
	}
	if ($type) {
		/** @noinspection OffsetOperationsInspection */
		return $__available[$countryid][$type] ?? false;
	}
	/** @noinspection OffsetOperationsInspection */
	return $__available[$countryid] ?? false;
}

function get_country_flag_info(int $countryid): ?array {
	require_once '_browser.inc';
	static $__infos = memcached_rowuse_hash(['country', 'countryflag', 'countryflagsvg'], /** @lang MariaDB */ '
		SELECT	COUNTRYID,
				WIDTH'.($x2 = is_high_res() ? '2x >> 1' : '') /* NOSONAR */.",
				HEIGHT$x2,
				RATIO,
				IF(cfs.SIZE < cf.SIZE, 1, 0),
				SHORT,
				NO_EMOJI_FLAG
		FROM countryflag AS cf
		JOIN country USING (COUNTRYID)
		LEFT JOIN countryflagsvg AS cfs USING (COUNTRYID)",
		TEN_MINUTES,
		flags: DB_NON_ASSOC) ?: [];
	/** @noinspection OffsetOperationsInspection */
	return $__infos[$countryid] ?? null;
}

function show_country_flag(
	 int			   $countryid,
	?string			   $class = null,
	 bool  			   $link  = false,
	 string|false|null $name  = null,
	?string			    $alt  = null
): void {
	if (isset(OBSOLETE_FLAG_REPLACEMENT[$countryid])) {
		# not real countries but still in use on user profiles
		# NOTE: propagate changes here in _urltitle.inc too!
		$link = false;
		$countryid = OBSOLETE_FLAG_REPLACEMENT[$countryid];
	}
	if ($countryid <= 0) {
		return;
	}
	if ($link) {
		?><a href="<?= get_element_href('country', $countryid) ?>"><?
	}
	if ($info = get_country_flag_info($countryid)) {
		[	/* $countryid */,
			$width,
			/* $height */,
			$ratio,
			$svg,
			$code,
			$no_emoji_flag
		] = $info;
	} else {
		mail_log('failed to get_country_flag_info', get_defined_vars());
		return;
	}
	require_once '_browser.inc';
	static $__font_loaded = have_unicode_flag_support();
	if (!$__font_loaded
	&&	!$no_emoji_flag
	) {
		$__font_loaded = true;
		include_head('<link rel="preload" crossorigin as="font" type="font/woff2" href="'.STATIC_HOST.'/fonts/twemoji/TwemojiCountryFlags.woff2" />');
		include_style('font_twemoji');
	}
	if (!$no_emoji_flag) {
		if ($code === 'AN') {
			# Nederlandse Antillen has same flag as The Netherlands
			$code = 'NL';
		}
		$name ??= __('country:'.$countryid);
		?><span<?
		?> class="cflg<?
		if ($class) {
			?> <? echo preg_replace('"\blight\b"', 'light8', $class);
		}
		?>"<?
		if ($name) {
			?> title="<?= escape_specials($name) ?>"<?
		}
		?>><?
		foreach ([$code[0], $code[1]] as $flag_letter) {
			if (isset(CODE_TO_FLAG[$flag_letter])) {
				?>&#<?= CODE_TO_FLAG[$flag_letter] ?>;<?
			}
		}
		?></span><?
/*	} elseif (svg_supported()) {
		include_head('<link rel="stylesheet" ef="/flag-icon-css/css/flag-icon.min.css" />');
		?><span class="<?
		if ($class) {
			echo preg_replace('"\blight\b"','light6',$class) ?> <?
		}
		?>flag-icon flag-icon-<?= strtolower($code) ?>"></span><?*/
	} else {
		if ($svg && !svg_supported()) {
			$svg = false;
		}
		if (!$width) {
			// $height = 14;
			$width  = 14 * $ratio;
			$svg = true;
		}
		require_once '_browser.inc';

		?><img<?
#		FIXME: This should be the ubb tag
#		if ($alt) {
#			?> data-ubb="<?= $alt ?>"<?
#		}
		?> width="<?= $width + 2 ?>"<?
		?> class="<?
		if ($class) {
			echo preg_replace('"\blight\b"', 'light6', $class) ?> <?
		}
		?>countryflag" src="/images/countryflag/<?= $countryid, ($svg ? '' : is_high_res()) ?>.<?= $svg ? 'svg' : 'png' ?>"<?

		if ($name !== false) {
			if ($name) {
				$name_escaped = escape_specials($name);
			} else {
				$name_escaped = __('country:'.$countryid);
			}
			?> alt="<?= $alt ?: $name_escaped ?>"<?
			?> title="<?= $name_escaped ?>"<?
		}
		?> /><?
	}
	if ($link) {
		?></a><?
	}
}

function get_country_flag_for_artist(
	int		$artistid,
	int		$countryid,
	?int	$party_countryid = null,
	?string	$class			 = null,
): string {
	ob_start();
	show_country_flag_for_artist($artistid, $countryid, $party_countryid, $class);
	return ob_get_clean();
}

function show_country_flag_for_artist(
	int		$artistid,
	int		$countryid,
	?int	$party_countryid = null,
	?string	$class			 = null,
	bool	$link			 = false,
	bool	$always			 = false,
): void {
	if (!$countryid
	||	!$always
	&&	 $countryid === $party_countryid
	) {
		return;
	}
	if ($countryid === -1) {
		/** @noinspection SuspiciousArrayElementInspection */
		$artist_ids = $check_ids = [$artistid => $artistid];
		while ($check_ids) {
			if (!($member_ids = memcached_same_hash('artistgroup','
				SELECT MEMBERID
				FROM artistgroup
				WHERE ARTISTID IN ('.implode(', ', $check_ids).')'))
			) {
				break;
			}
			$check_ids = array_diff_key($member_ids, $artist_ids);
			$artist_ids += $member_ids;
		}
		$artist_idstr = implodekeys(', ', $artist_ids);

		if ($countryids = memcached_simpler_array(['user', 'artistmember', 'artistgroup'], "
			SELECT DISTINCT(COUNTRYID)
			FROM user
			JOIN artistmember USING (USERID)
			WHERE COUNTRYID
			  AND ID IN ($artist_idstr)
			UNION
			SELECT DISTINCT(COUNTRYID)
			FROM artistgroup
			JOIN artist ON artist.ARTISTID = MEMBERID
			WHERE COUNTRYID
			  AND artistgroup.ARTISTID IN ($artist_idstr)")
		) {
			# More than one country:
			if (isset($countryids[1])) {
				foreach ($countryids as $local_countryid) {
					?> <? show_country_flag($local_countryid, $class, $link);
				}
				return;
			}
			# Only one country:
			if (isset($countryids[0])) {
				if (!$always
				&&	$countryids[0] === $countryid
				) {
					return;
				}
				$countryid = $countryids[0];
			}
 			# ELSE: No country from artistgroup and artistmembers.
 			#		Pass the one that was passed.
		}
	}
	?> <? show_country_flag($countryid, $class, $link);
}
