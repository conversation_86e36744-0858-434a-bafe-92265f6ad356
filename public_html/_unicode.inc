<?php

/** @noinspection SpellCheckingInspection */

declare(strict_types=1);

# Nice site to look up unicode characters:
# https://www.compart.com/en/unicode/
# Regex unicode categories:
# https://www.regular-expressions.info/unicode.html#category

# WARNING: Accolades around unicode codepoint does not work in JavaScript regular expressions
#		   when using sashes (/) as begin and end markings and forgetting the "u" flag.
#		   This does not work:
#				"testPtest".match(/\u{0050}/)
#		   This works:
#				"testPtest".match(/\u0050/u)
#				"testPtest".match("\u{0050}")
#
# NOTE: Unicode General_Category for use with \p{}:
#		https://www.regular-expressions.info/unicode.html#category
#		https://www.unicode.org/versions/Unicode16.0.0/core-spec/chapter-4/#G134153

# for preg_match:
const MATCH_EN_DASH									= '\x{2013}';
const MATCH_EM_DASH									= '\x{2014}';
// const MATCH_ZERO_WIDTH_SPACE						= '\x{200B}';
// const MATCH_NO_BREAK_SPACE						= '\x{00A0}';
const MATCH_SOFT_HYPHEN								= '\x{00AD}';
const MATCH_SINGLE_LOW_9_QUOTATION_MARK				= '\x{0082}';
const MATCH_MULTIPLICATION_SIGN						= '\x{00D7}';
// const MATCH_NONEXISTENT_WIN1251_CHARS			= "\x81\x8d\x8f\x90\x9d";
// const MATCH_ANY_LETTER							= '\p{L}';		# any kind of letter in any kind of language
# NOTE: \p{Z} matches white space characters, but oftten you want to include more,
#		like tab, which is a control character (Cc)
const MATCH_GENERIC_NEWLINE							= '\R';
const MATCH_ANY_SPACE								= '\p{Z}';				# any kind of space or invisible separator
const MATCH_ANY_WHITESPACE							= '\p{White_Space}';	# any kind of white space character
const MATCH_ANY_PUNCTUATION							= '\p{P}';				# any kind of punctuation character
const MATCH_ANY_INVISIBLE_CONTROL					= '\p{C}';				# any kind of invisible control character
const MATCH_ANY_FORMATTING							= '\p{Cf}';				# any kind of invisible formatting control character
// const MATCH_ANY_CONTROL							= '\p{Cc}';				# any kind of control character

		# for example for trim"
const EN_DASH										= "\u{2013}";			# –
// const EN_DASH_ENTITY								= '&ndash;';			# –
const EM_DASH										= "\u{2014}";			# —
const EM_DASH_ENTITY								= '&mdash;';			# —
const NO_BREAK_SPACE								= "\u{00A0}";
const NO_BREAK_SPACE_ENTITY							= '&nbsp;';
const NO_BREAK_SPACE_LATIN1							= "\xA0";
const SOFT_HYPHEN									= "\u{00AD}";			# ­
const SOFT_HYPHEN_ENTITY							= '&shy;';				# ­
const SOFT_HYPHEN_LATIN1							= "\xAD";				# ­
const PLUS_SIGN_ENTITY								= '&plus;';				# +
// const OHGHAM_SPACE_MARK							= "\u{1680}";			#  	// NOSONAR
// const EN_QUAD									= "\u{2000}";			#  	// NOSONAR
// const EM_QUAD									= "\u{2001}";			#  	// NOSONAR
// const EN_SPACE									= "\u{2002}";			#  	// NOSONAR
// const EM_SPACE									= "\u{2003}";			#  	// NOSONAR
// const THREE_PER_EM_SPACE							= "\u{2004}";			#  	// NOSONAR
// const FOUR_PER_EM_SPACE							= "\u{2005}";			#  	// NOSONAR
// const SIX_PER_EM_SPACE							= "\u{2006}";			#  	// NOSONAR
// const FIGURE_SPACE								= "\u{2007}";			#  	// NOSONAR
// const PUNCTUATION_SPACE							= "\u{2008}";			#  	// NOSONAR
// const THIN_SPACE									= "\u{2009}";			#  	// NOSONAR
// const HAIR_SPACE									= "\u{200A}";			#  	// NOSONAR
// const ZERO_WIDTH_SPACE							= "\u{200B}";			# ​	// NOSONAR
// const LINE_SEPARATOR								= "\u{2028}";			#  	// NOSONAR
// const PARAGRAPH_SEPARATOR						= "\u{2029}";			#  	// NOSONAR
// const NARROW_NO_BREAK_SPACE						= "\u{202F}";			#  	// NOSONAR
// const MEDIUM_MATHEMATICAL_SPACE					= "\u{205F}";			# ᠎
// const IDEOGRAPHIC_SPACE							= "\u{3000}";			# 　	// NOSONAR
// const NEXT_LINE									= "\u{0085}";			# 	// NOSONAR
// const MONGOLIAN_VOWEL_SEPARATOR					= "\u{180E}";			# ᠎		// NOSONAR
// const ZERO_WIDTH_NO_BREAK_SPACE					= "\u{FEFF}";			# ﻿	// NOSONAR
// const ZERO_WIDTH_NO_BREAK_SPACE_ENTITY			= '&zwnj;';				# ﻿	// NOSONAR
// const SINGLE_LOW_9_QUOTATION_MARK				= "\u{0082}";			# ‚
const MULTIPLICATION_SIGN							= "\u{00D7}";			# ×
const MULTIPLICATION_SIGN_LATIN1					= "\xD7";				# ×
const MULTIPLICATION_SIGN_ENTITY					= '&times;';			# ×
const LATIN_SMALL_LETTER_E_WITH_ACUTE				= "\u{00E9}";			# é
const LATIN_SMALL_LETTER_E_WITH_ACUTE_LATIN1		= "\xE9";				# é
const LATIN_SMALL_LETTER_E_WITH_GRAVE				= "\u{00E8}";			# è
const LATIN_SMALL_LETTER_E_WITH_GRAVE_LATIN1		= "\xE8";				# è
const LATIN_CAPITAL_LETTER_E_WITH_ACUTE				= "\u{00C9}";			# É
const LATIN_CAPITAL_LETTER_E_WITH_ACUTE_LATIN1		= "\xC9";				# É
const LATIN_CAPITAL_LETTER_E_WITH_GRAVE				= "\u{00C8}";			# È
const LATIN_CAPITAL_LETTER_E_WITH_GRAVE_LATIN1		= "\xC8";				# È
const MIDDLE_DOT									= "\u{00B7}";			# ·
const MIDDLE_DOT_ENTITY								= '&middot;';			# ·
const MIDDLE_DOT_LATIN1								= "\xB7";				# ·
// const CENT_SIGN									= "\u{00A2}";			# ¢
const CENT_SIGN_ENTITY								= '&cent;';				# ¢
// const CENT_SIGN_LATIN1							= "\xA2";				# ¢
const EURO_SIGN										= "\u{20AC}";			# €
const EURO_SIGN_ENTITY								= '&euro;';				# €
const EURO_SIGN_LATIN1								= "\x80";				# €
// const RIGHT_SINGLE_QUOTATION_MARK_LATIN1			= "\x27";				# '
// const RIGHT_SINGLE_QUOTATION_MARK				= "\u{2019}";			# ’
// const ROMAN_NUMERAL_ONE							= "\u{2160}";			# Ⅰ
// const ROMAN_NUMERAL_TWO							= "\u{2161}";			# Ⅱ
// const ROMAN_NUMERAL_THREE						= "\u{2162}";			# Ⅲ
// const ROMAN_NUMERAL_FOUR							= "\u{2163}";			# Ⅳ
// const ROMAN_NUMERAL_FIVE							= "\u{2164}";			# Ⅴ
// const ROMAN_NUMERAL_SIX							= "\u{2165}";			# Ⅵ
// const ROMAN_NUMERAL_SEVEN						= "\u{2166}";			# Ⅶ
// const ROMAN_NUMERAL_EIGHT						= "\u{2167}";			# Ⅷ
// const ROMAN_NUMERAL_NINE							= "\u{2168}";			# Ⅸ
// const ROMAN_NUMERAL_TEN							= "\u{2169}";			# Ⅹ
// const ROMAN_NUMERAL_ELEVEN						= "\u{216A}";			# ⅊
// const ROMAN_NUMERAL_TWELVE						= "\u{216B}";			# ⅋
// const ROMAN_NUMERAL_FIFTY						= "\u{216C}";			# Ⅼ
// const ROMAN_NUMERAL_ONE_HUNDRED					= "\u{216D}";			# Ⅽ
// const ROMAN_NUMERAL_FIVE_HUNDRED					= "\u{216E}";			# Ⅾ
// const ROMAN_NUMERAL_ONE_THOUSAND					= "\u{216F}";			# Ⅿ
const LEFTWARD_ARROW								= "\u{2190}";			# ←
const LEFTWARD_ARROW_ENTITY							= '&larr;';				# ←
const RIGHTWARD_ARROW								= "\u{2192}";			# →
const RIGHTWARD_ARROW_ENTITY						= '&rarr;';				# →
const ERASE_TO_THE_LEFT_ENTITY						= '&#x232B;';			# ⌫
const BROKEN_CIRCLE_WITH_NORTHWEST_ARROW			= '\u{238B}';			# ⎋
const BROKEN_CIRCLE_WITH_NORTHWEST_ARROW_ENTITY		= '&#x238B;';			# ⎋
const BLACK_SQUARE_ENTITY							= '&#x25A0;';			# ■
const BLACK_STAR									= "\u{2605}";			# ★
const BLACK_STAR_LATIN1								= '*';					# ★
const BLACK_STAR_ENTITY								= '&starf;';			# ★
const DAGGER_ENTITY									= '&dagger;';			# †
const BULLET										= "\u{2022}";			# •
// const BULLET_LATIN1								= "\x95";				# •
const BULLET_ENTITY									= '&bull;';				# •
const HORIZONTAL_ELLIPSIS							= "\u{2026}";			# …
const HORIZONTAL_ELLIPSIS_ENTITY					= '&hellip;';			# …
const WATCH_ENTITY									= '&#x231A;';			# ⌚
const BLACK_UP_POINTING_TRIANGLE_ENTITY				= '&#x25B2;';			# ▲
const BLACK_DOWN_POINTING_TRIANGLE_ENTITY			= '&#x25BC;';			# ▼
// const WHITE_CIRCLE								= "\u{25CB}";			# ○
const WHITE_CIRCLE_ENTITY							= '&cir;';				# ○
// const WHITE_SQUARE_WITH_UPPER_LEFT_QUADRANT		= "\u{25F0}";			# ◰
const WHITE_SQUARE_WITH_UPPER_LEFT_QUADRANT_ENTITY	= '&#x25F0;';			# ◰
// const UNIVERSAL_RECYCLING_ENTITY					= '&#x2672;';			# ♲
const BLACK_UNIVERSAL_RECYCLING_ENTITY				= '&#x267B;';			# ♻
const WARNING_SIGN_ENTITY							= '&#x26A0;';			# ⚠
// const WHITE_HEAVY_CHECK_MARK						= "\u{2705}";			# ✅
const WHITE_HEAVY_CHECK_MARK_ENTITY					= '&#x2705;';			# ✅
// const LOWER_RIGHT_PENCIL_ENTITY					= '&#x270E;';			# ✎
const HEAVY_CHECK_MARK								= "\u{2714}";			# ✔
// const HEAVY_CHECK_MARK_ENTITY					= '&#x2714;';			# ✔
// const HEAVY_MULTIPLICATION_X						= "\u{2716}";			# ✖
const HEAVY_MULTIPLICATION_X_ENTITY					= '&#x2716;';			# ✖
// const HEAVY_GREEK_CROSS							= "\u{271A}";			# ✚
// const HEAVY_GREEK_CROSS_ENTITY					= '&#x271A;';			# ✚
const CROSS_MARK_ENTITY								= '&#x274C;';			# ❌
const VECTOR_OR_CROSS_PRODUCT_ENTITY				= '&Cross;';			# ×
const LATIN_SMALL_LETTER_TURNED_K_UTF8				= "\xCA\x9E";			# ʞ
const LATIN_CAPITAL_LETTER_TURNED_K_UTF8			= "\xEA\x9E\xB0";		# 𞞰
const LISU_LETTER_KHA_UTF8							= "\xEA\x93\x98";		# 𖎘
const VARIATION_SELECTOR_16_ENTITY					= '&#xFE0F;';			# ️
const WARNING_SIGN_EMOJI_ENTITY						= WARNING_SIGN_ENTITY.VARIATION_SELECTOR_16_ENTITY;
																			# ⚠️
const JACK_O_LANTERN_ENTITY							= '&#x1F383;';			# 🎃
const ADMISSION_TICKET_ENTITY						= '&#x1F39F;';			# 🎟
const EYES_ENTITY									= '&#x1F440;';			# 👀
// const EYE_ENTITY									= '&#x1F441;';			# 👁
// const CROWN_ENTITY								= '&#x1F451;';			# 👑
// const EYEGLASSES_ENTITY							= '&#x1F453;';			# 👓
const PAPERCLIP_ENTITY								= '&#x1F4CE;';			# 📎
// const EMAIL_SYMBOL_ENTITY						= '&#x1F4E7;';			# 📧
// const INCOMING_ENVELOPE_ENTITY					= '&#x1F4E8;';			# 📨
const CAMERA_ENTITY									= '&#x1F4F7;';			# 📷
const CLOCK_FACE_ONE_OCLOCK_ENTITY					= '&#x1F550;';			# 🕐
// const CLOCK_FACE_TWO_OCLOCK_ENTITY				= '&#x1F551;';			# 🕑
// const CLOCK_FACE_THREE_OCLOCK_ENTITY				= '&#x1F552;';			# 🕒
// const CLOCK_FACE_FOUR_OCLOCK_ENTITY				= '&#x1F553;';			# 🕓
// const CLOCK_FACE_FIVE_OCLOCK_ENTITY				= '&#x1F554;';			# 🕔
// const CLOCK_FACE_SIX_OCLOCK_ENTITY				= '&#x1F555;';			# 🕕
// const CLOCK_FACE_SEVEN_OCLOCK_ENTITY				= '&#x1F556;';			# 🕖
// const CLOCK_FACE_EIGHT_OCLOCK_ENTITY				= '&#x1F557;';			# 🕗
// const CLOCK_FACE_NINE_OCLOCK_ENTITY				= '&#x1F558;';			# 🕘
// const CLOCK_FACE_TEN_OCLOCK_ENTITY				= '&#x1F559;';			# 🕙
// const CLOCK_FACE_ELEVEN_OCLOCK_ENTITY			= '&#x1F55A;';			# 🕚
// const CLOCK_FACE_TWELVE_OCLOCK_ENTITY			= '&#x1F55B;';			# 🕛
// const CLOCK_FACE_ONE_THIRTY_ENTITY				= '&#x1F55C;';			# 🕜
// const CLOCK_FACE_TWO_THIRTY_ENTITY				= '&#x1F55D;';			# 🕝
// const CLOCK_FACE_THREE_THIRTY_ENTITY				= '&#x1F55E;';			# 🕞
// const CLOCK_FACE_FOUR_THIRTY_ENTITY				= '&#x1F55F;';			# 🕟
// const CLOCK_FACE_FIVE_THIRTY_ENTITY				= '&#x1F560;';			# 🕠
// const CLOCK_FACE_SIX_THIRTY_ENTITY				= '&#x1F561;';			# 🕡
// const CLOCK_FACE_SEVEN_THIRTY_ENTITY				= '&#x1F562;';			# 🕢
// const CLOCK_FACE_EIGHT_THIRTY_ENTITY				= '&#x1F563;';			# 🕣
// const CLOCK_FACE_NINE_THIRTY_ENTITY				= '&#x1F564;';			# 🕤
// const CLOCK_FACE_TEN_THIRTY_ENTITY				= '&#x1F565;';			# 🕥
// const CLOCK_FACE_ELEVEN_THIRTY_ENTITY			= '&#x1F566;';			# 🕦
// const CLOCK_FACE_TWELVE_THIRTY_ENTITY			= '&#x1F567;';			# 🕧
// const DARK_SUNGLASSES_ENTITY						= '&#x1F576;';			# 🕶
// const SMILING_FACE_WITH_SUNGLASSES_ENTITY		= '&#x1F60E;';			# 😎
const SMILING_FACE_WITH_HEART_SHAPED_EYES_ENTITY	= '&#x1F60D;';			# 😍
const SEE_NO_EVIL_MONKEY_ENTITY						= '&#x1F648;';			# 🙈
const PEOPLE_HUGGING_ENTITY							= '&#x1FAC2;';			# 🫂
const OBJECT_REPLACEMENT_CHARACTER					= "\u{FFFC}";			# ￼		EF BF BC
const REPLACEMENT_CHARACTER							= "\u{FFFD}";			# �		EF BF BD
const REPLACEMENT_CHARACTER_ENTITY					= '&#xFFFD;';			# �

# ranges: for example as argument to transliterate and remove_characters
const MATHEMATICAL_ALPHANUMERIC_SYMBOLS_RANGE		= '\x{1d400}-\x{1d7ff}';
const IRREGULAR_LETTERS_RANGE						= MATHEMATICAL_ALPHANUMERIC_SYMBOLS_RANGE;

# range of emoji symbols, for example for use with remove_characters:
const EMOJI_RANGES =	// NOSONAR
	'\x{2600}-\x{26FF}'.	# Match Miscellaneous Symbols
	'\x{2700}-\x{27BF}'.	# Match Dingbats
	'\x{1F100}-\x{1F1FF}'.	# Match Enclosed Alphanumeric Supplement
	'\x{1F300}-\x{1F5FF}'.	# Match Miscellaneous Symbols and Pictographs
	'\x{1F600}-\x{1F64F}'.	# Match Emoticons
	'\x{1F680}-\x{1F6FF}'.	# Match Transport And Map Symbols
	'\x{1F900}-\x{1F9FF}';	# Match Supplemental Symbols and Pictographs

function get_symbs(): string {
	$symbs = '∆▬●•▽▼☆†◥€♬►◤◥─❇”“♫⚠◄▚★◆—☠◗◖';
	$symbs .= '🕉';
	$symbs .= '🎭';
	$symbs .= '👑';
	$symbs .= '🌈🎧🥃🚗🧽🦸🧼🚿🩳👙🌼♀︎🏝️♂🏐👒🍩🏖🤾🏽🕶💪🌊🤚🎊🔫💨🏝📸';
	$symbs .= '/\\】【_✯♛╎>❖❗✖✭◢▪️';
	$symbs .= '✯:☾♫━✿▅﹌▶⌛⛄☰';
	$symbs .= '▇▪❌▲|➤♥☼◽◼♪☯✪〰◌═*✰\+▔❀➳ツ☛☚';
	$symbs .= '│☁✔∇♦▄▷▩±⌘❅⌯◘◼︎☞♡➜✊';
	$symbs .= '➽❍▆▻☃✺✿#⭐️';
	$symbs .= '◈═╗╔∿➖∞☢ △▽▸﹏✘❥☮帝✝»«☒|';
	$symbs .= 'Δ▫▴=■❶▄▀✧✞✦》《✮–◎❆❤';
	$symbs .= '▂ ▃ ▅ ▆’‘𐆕ϟ♔✟✌';
	$symbs .= '²◙∎◾️██ □⚡♚⟁☀→® ♫♪♫';
	$symbs .= '► ▬✩✚✱◊›≡✨';
	$symbs .= '⚜▮〓';
	$symbs .= '◐◑✶';
	$symbs .= '♕';
	$symbs .= '˄˅°⚓⦾';
	$symbs .= '▓⍚➥';
	$symbs .= '▬▬◣◥ ';
	$symbs .= '✜💒▦';
	$symbs .= '⚪️🎃';
	$symbs .= '🌀⇻🎵';
	$symbs .= '⚫️🎚🎛';
	$symbs .= '❐';
	$symbs .= '❎➡🔊🔹️';
	$symbs .= '😱🔸🎫🏠🚌';
	$symbs .= '😍😏💃🏻';
	$symbs .= '‼️';
	$symbs .= '🗼🌟🔳';
	$symbs .= '❄️⛷';
	$symbs .= '💀';
	$symbs .= '○◦';
	$symbs .= '🅻🅸🅽🅴🆄🄿';
	$symbs .= '🔥';
	$symbs .= '▤▥▧';
	$symbs .= '🎲';
	$symbs .= '♦️💥🎶';
	$symbs .= '🌳';
	$symbs .= '🌺🐒';
	$symbs .= '⚜️🎟️';
	$symbs .= '�';
	$symbs .= '🏮';
	$symbs .= '🎪';
	$symbs .= '🌶';
	$symbs .= '🐡 🐠🎣👊⏱🎉';
	$symbs .= '📻';
	$symbs .= '😎';
	return $symbs;
}
