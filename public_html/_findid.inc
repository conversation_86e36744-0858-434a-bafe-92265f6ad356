<?php

function find_first_id($table,$stamp) {
	switch ($table) {
	case 'message':
	case 'flockmessage':
		$idname = 'MESSAGEID';
		break;
	case 'topic':
	case 'flocktopic':
		$idname = 'TOPICID';
		break;
	case 'albumelement':
		$idname = 'ALBUMELEMENTID';
		break;
	default:
		# assume _comment table
		$idname = 'COMMENTID';
		break;
	}
	$info = db_rowuse_array($table,'
		SELECT '.$idname.',CSTAMP
		FROM '.$table.'
		JOIN (		SELECT MIN('.$idname.') AS '.$idname.' FROM '.$table.'
			UNION	SELECT MAX('.$idname.') AS '.$idname.' FROM '.$table.')	AS x USING ('.$idname.')
		ORDER BY '.$idname,
		DB_NON_ASSOC
	);
	if (!$info || !isset($info[0])) {
		return false;
	}
	$start = $info[0];
	$stop = $info[1];
	
#	$stamp = CURRENTSTAMP - 365*24*3600;
#	$stamp = 1317732902;
	
	$id = find_in_past($stamp,$start,$stop,$table,$idname);

	return $id;
}
function find_in_past($stamp,$start,$stop,$table,$idname) {
	static $__done;
	
	list($startid,$startstamp) = $start;
	list($stopid,$stopstamp) = $stop;
	
	if ($stamp > $startstamp) {
		if ($stamp < $stopstamp) {
			# stamp is within range
			$pct = ($stamp - $startstamp) / ($stopstamp - $startstamp);
			$chooseid = floor($startid + $pct * ($stopid - $startid));
		} else {
#			error_log('here1 for '.$startid.' - '.$stopid.' and '.$startstamp.' - '.$stopstamp,0);
			# stamp is after stop
#			error_log('WARNING find_in_past stamp is after stop @ '.$_SERVER['REQUEST_URI']);
			return false;
		}
	} else {
#		error_log('here2 for '.$startid.' - '.$stopid.' and '.$startstamp.' - '.$stopstamp,0);
		# stamp is before start
		
		$pct = ($startstamp - $stamp) / ($stopstamp - $startstamp);
		$chooseid = floor($startid - $pct * ($stopid - $startid));
	}
	if (isset($__done[$chooseid])) {
		return $startid;
	}
	$__done[$chooseid] = true;
#	error_log($chooseid);
	$info = db_single_array($table,'SELECT '.$idname.',CSTAMP FROM '.$table.' WHERE '.$idname.'>='.$chooseid.' ORDER BY '.$idname.' ASC LIMIT 1');
		
	if (!$info) {
#		error_log('argh');
		return false;
	}
	if ($info[0] == $startid
	&&	$info[1] == $startstamp
	) {
#		error_log('identical: '.$startid.':'.$startstamp);
		return $startid;
	}
	return find_in_past($stamp,$info,$stop,$table,$idname);
}
