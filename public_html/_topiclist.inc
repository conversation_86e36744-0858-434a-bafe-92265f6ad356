<?php

const MINIMUM_TOPICS_PER_GROUP	= 2;

global $__topiclist_instance;
$__topiclist_instance = null;

class _topiclist {	// NOSONAR
	private array $rowlist				= [];
	private array $forlist				= [];
	private array $topicidlist			= [];
	private array $tmp_row_list			= [];
	private array $queries				= [];
	private int $maxstamp				= 0;
	private int $minstamp				= 0xFFFFFFFF;
	private array $userids				= [];
	private array $wheres				= [];
	private array $joins				= [];
	private array $tables				= [];
	private array $orders				= [];
	private ?array $selfpost			= null;
	private ?array $last_row			= null;
	private ?array $first_row			= null;
	private string $order				= 'topic.LSTAMP';
	private int $total					= 0;
	private ?int $tmp_total				= null;
	private int|string $page			= 0;
	private array $groupcnt				= [];
	public int $colcnt					= 0;
	public bool $overview				= false;
	private ?array $hilite				= null;
	public bool $no_limit				= false;
	public int $MAX_TOPICS_PER_PAGE		= 30;
	public bool $search					= false;
	public bool $show_author			= false;
	public bool $show_last				= false;
	public bool $show_replycnt			= true;
	public bool $show_cstamp			= false;
	public bool $show_cstamp_time		= false;
	public bool $show_diff				= true;
	public bool $show_diff_self			= false;
	public bool $show_heart				= false;
	public bool $show_header			= false;
	public bool $show_groups			= false;
	public bool $show_short				= false;
	public bool $show_inaccessible		= false;
	public bool $show_connects			= false;
	public bool $show_connect_star		= true;
	public bool $dont_select_hidden		= false;
	public ?string $use_key;
	private bool $ignore_sticky			= false;
	private int $opened					= 0;
	private int $userid					= 0;
	private ?string $in_forum;
	private _pagecontrols $controls;
	private array $readable_forumids;
	private array $ignores;
	private int $offset;

	function __construct() {
		$this->MAX_TOPICS_PER_PAGE = setting('TOPICS_PER_PAGE');
	}
	final public function column_count(): int {
		return $this->colcnt;
	}
	final function query(): bool {
		if (SMALL_SCREEN) {
			$this->show_author		= false;
			$this->show_last		= false;
			$this->show_replycnt	= false;
		}
		$this->colcnt = 1;
		if ($this->show_connects) 		{ ++$this->show_connects; }
		if ($this->show_short)			{ ++$this->colcnt; }
		if ($this->show_replycnt)	 	{ ++$this->colcnt; }
		if ($this->show_author) 		{ ++$this->colcnt; }
		if ($this->show_cstamp)			{ ++$this->colcnt; }
		if ($this->show_cstamp_time)	{ ++$this->colcnt; }
		if ($this->show_diff_self)		{ ++$this->colcnt; }
		if ($this->show_last)			{ ++$this->colcnt; }
		if ($this->show_diff)			{ ++$this->colcnt; }

		$selects = ['
			topic.FORUMID, 
			topic.FLAGS,
			STICKY,
			topic.LSTAMP,
			SUBJECT,
			MSGCNT,
			BADMSGCNT,
			topic.TOPICID,
			topic.USERID,
			topic.STATUS,
			topic.CSTAMP AS TOPIC_CSTAMP'
		];
		if ($this->show_diff_self
		&&	$this->userid
		) {
			$selects[] = "(	SELECT LSTAMP
							FROM postedinv3
 							WHERE ELEMENT = 'topic'
 							  AND postedinv3.ID = topic.TOPICID
 							  AND USERID = $this->userid
 							) AS LAST_POST";
		}
		if ($this->show_last) {
			$selects[] = 'topic.LUSERID';
		}
		if ($this->show_short) {
			$selects[] = 'forum.SHORT';
			$this->joins['forum'] = 'LEFT JOIN forum USING (FORUMID)';
			$this->tables[] = 'forum';
		}
		if ($this->show_connect_star
		||	$this->show_groups
		) {
			$selects[] = '(	SELECT GROUP_CONCAT(ASSOCID)
							FROM connect
							WHERE MAINTYPE = "topic"
							  AND MAINID = topic.TOPICID
							  AND ASSOCTYPE = "party"
							) AS CONNECT_PARTYIDS';
			if (!$GLOBALS['currentuser']->any_forum_admin()
			&&	!have_admin('party')
			) {
				$this->show_connect_star = false;
			}
		} else {
			$this->show_connect_star = false;
		}
		if ($this->readable_forumids = $GLOBALS['currentuser']->forum_ids_read_access()) {
			if (!$this->show_inaccessible) {
				# FIXME: don't include this into query, but filter on display routine instead?
				$this->wheres[] = 'FORUMID IN ('.implode(',',$this->readable_forumids).')';
			}
		} else {
			$this->wheres[] = '0';
		}
		if (isset($this->search)) {
			IF ($admin_ids = $GLOBALS['currentuser']->forum_ids_adminnable()) {
				$this->wheres[] = '(topic.STATUS != "hidden" OR topic.FORUMID IN ('.implode(',',$admin_ids).'))';
			} else {
				$this->wheres[] = 'topic.STATUS != "hidden"';
			}
		}
		if ($this->dont_select_hidden) {
			# please note that pages change when topics are marked hidden
			$this->wheres[] = 'topic.STATUS != "hidden"';
		}
		$this->tables[] = 'topic';

		if (have_user()) {
			require_once '_postedin.inc';
			require_once '_ignores.inc';
			$this->ignores = ignores(IGNORE_FORUM_TOPICS);
		}
		if ($this->overview) {
			// do
			// (FORUMID IN (list) AND LSTAMP>now-RECENTDIFF)
			// for every unique RECENTDIFF
			$recent_index = [];
			foreach ($GLOBALS['currentuser']->forumsettings as $forumid => $forum) {
				if ($forum['SHOW_RECENT']) {
					$recent_index[$forum['RECENTDIFF']][] = $forumid;
				}
			}
			if ($recent_index) {
				// now add stickies
				if (false === ($row_list = memcached_rowuse_array($this->tables, $this->queries[] = '
					SELECT		'.implode(',',$selects).'
					FROM topic	'.implode(' ',$this->joins).'
					WHERE		'.implode(' AND ',$this->wheres).'
					  AND STICKY'))
				) {
					return false;
				}
				$this->process_row_list($row_list);
				unset($row_list);
				$extra_wheres = [];
				foreach ($recent_index as $recent_diff => $forum_ids) {
					sort($forum_ids);
					$extra_wheres[] = 'topic.FORUMID IN ('.implode(', ',$forum_ids).') AND topic.LSTAMP>='.(CACHESTAMP - $recent_diff);
				}
				$this->wheres[] = '('.implode(' OR ',$extra_wheres).')';
				if (false === ($row_list = memcached_rowuse_array($this->tables,$this->queries[] = '
					SELECT		'.implode(',',$selects).'
					FROM topic	'.implode(' ',$this->joins).'
					WHERE		'.implode(' AND ',$this->wheres).'
					  AND NOT STICKY'.
					($this->orders ? ' ORDER BY '.implode(',',$this->orders) : '')))
				) {
					return false;
				}
				$this->process_row_list($row_list);
				unset($row_list);
			}
		} else { // regular page
			$this->initial_wheres = $this->wheres;
			if (empty($this->search)
			&&	!$this->no_limit
			) {
				require_once '_pagecontrols.inc';
				$this->controls = new _pagecontrols();
				$this->controls->set_per_page(setting('TOPICS_PER_PAGE'));
				$this->controls->show_prev_next();
				if ($this->search
				||	$this->opened
				) {
					$total = memcached_single_int('topic', $this->queries[] = '
						SELECT COUNT(*)
						FROM topic'.
						($this->wheres ? ' WHERE '.implode(' AND ',$this->wheres) : null)
					);
				} elseif (
					$_REQUEST['ACTION'] !== 'intrecent'
				&&	$this->userid
				&&	!$this->opened
				) {
					$total = memcached_single_int(['topic','postedinv3'], $this->queries[] = '
						SELECT COUNT(*)
						FROM postedinv3
						JOIN topic ON TOPICID = ID
						WHERE ELEMENT = "topic"'.
						($this->wheres ? ' AND '.implode(' AND ',$this->wheres) : '')
					);
				} else {
					$query_total = memcached_single_int(['forum_stats', 'topic'], $this->queries[] = /** @lang MariaDB */ '
						SELECT SUM(TOPIX)
						FROM forum_sttats
						WHERE FORUMID IN (
							SELECT DISTINCT FORUMID
							FROM topic '.
							($this->wheres ? ' WHERE '.implode(' AND ',$this->wheres) : '').')'
					);
					$total = $this->tmp_total ?? $query_total;
					unset($this->tmp_total);
					error_log("DEBUG actual query total: $query_total");
				}
				if ($total === false) {
					return false;
				}
				$this->controls->set_total($total);
				$this->controls->set_order($this->order);
				if (!$this->stats) {
					if ($_REQUEST['ACTION'] !== 'intrecent'
					&&	$this->userid
					&&	!$this->opened
					) {
						$topicids = memcached_simpler_array(
							['topic','postedinv3'],$this->queries[] = '
							SELECT ID
							FROM postedinv3
							JOIN topic ON TOPICID=ID
							WHERE ELEMENT="topic"'.
							($this->wheres ? ' AND '.implode(' AND ',$this->wheres) : null).
							$this->controls->order_and_limit()
						);
						unset($this->joins['postedinv3']);
					} else {
						$topicids = memcached_simpler_array('topic',$this->queries[] = '
							SELECT TOPICID /* '.addslashes($_SERVER['REQUEST_URI']).' */
							FROM topic
							WHERE '.
							(empty($this->in_forum) ? '1' : ' /* also.in_forum */ '.$this->in_forum).
							($this->wheres ? ' AND '.implode(' AND ',$this->wheres) : null).
							$this->controls->order_and_limit()
						);
					}
					if ($topicids === false) {
						$this->wheres[] = '0';
						return false;
					}
					$this->wheres = [$topicids ? 'TOPICID IN ('.implode(',',$topicids).')' : '0'];
				} else {
					$this->controls->include_last(false);
				}
			}
			$this->total = 0;
			$key = $this->use_key ?? null;
			if (false === ($row_list = memcached_read(
				ROW_USE_ARRAY,
				$this->tables,$this->queries[] = '
				SELECT '.implode(',',$selects).'
				FROM topic '.implode(' ',$this->joins).
				($this->wheres ?	' WHERE '.implode(' AND ',$this->wheres) : null).
				(	!isset($this->stats)
				? 	($this->orders ? ' ORDER BY '.implode(',',$this->orders) : null).
					(($this->offset = have_idnumber($_REQUEST,'OFFSET') ?: 0) || isset($this->search) ? ' LIMIT '.$this->offset.','.($this->MAX_TOPICS_PER_PAGE+1) : null)
				:	' GROUP BY TOPICID '.$this->controls->order_and_limit()
				),
				key:	$key,
				flags:	$this->search ? DB_FORCE_SERVER : 0,
				arg:	$this->search ? ['dbsrch', 'dbsrchrep'] : null))
			) {
				return false;
			}
			$this->process_row_list($row_list);
			unset($row_list);

			if ($_REQUEST['sELEMENT'] === 'topic'
			&&	$_REQUEST['ACTION'] === 'single'
			&&	$this->controls->obtain_page() === 'last'
			&&	!$this->ignore_sticky
			) {
				if (false === ($row_list = memcached_read(
					ROW_USE_ARRAY,
					$this->tables,$this->queries[] = '
					SELECT		'.implode(',',$selects).'
					FROM topic	'.implode(' ',$this->joins).'
					WHERE STICKY = b\'1\''.
					($this->topicidlist ? ' AND TOPICID NOT IN ('.implode(',',$this->topicidlist).')' : null).
					($this->initial_wheres ? ' AND '.implode(' AND ',$this->initial_wheres) : null)))
				) {
					$this->wheres[] = '0';
					return false;
				}
				$this->process_row_list($row_list);
			}
		}
		return !$this->total
			||	$this->query_finish();
	}
	private function process_row_list(array $row_list): void {
		if (!$row_list) {
			return;
		}
		foreach ($row_list as $row) {
			if (!empty($row['CONNECT_PARTYIDS'])) {
				$row['CONNECT_PARTYIDS'] = explode(',',$row['CONNECT_PARTYIDS']);
			}
			if (isset($this->ignores[$row['USERID']])
			||	isset($this->tmp_row_list[$row['TOPICID']])
			) {
				continue;
			}
			if (!$this->first_row) {
				$this->first_row = $row;
			}
			if (!$this->last_row) {
				$this->last_row = $row;
			}
			if (is_number($row['TOPICID'])) {
				$this->topicidlist[] = $row['TOPICID'];
			} else {
				continue;
			}
			if (!$row['STICKY']) {
				if ($row['LSTAMP'] < $this->minstamp) {
					$this->minstamp = $row['LSTAMP'];
					$this->last_row = $row;
				} elseif ($row['LSTAMP'] > $this->maxstamp) {
					$this->maxstamp = $row['LSTAMP'];
					$this->first_row = $row;
				}
			}
			if ($this->show_author
			&&	!isset($this->userids[$row['USERID']])
			) {
				if (is_number($row['USERID'])) {
					$this->userids[$row['USERID']] = true;
				} else {
					continue;
				}
			}
			if ($this->show_last
			&	!isset($this->userids[$row['LUSERID']])
			) {
				if (is_number($row['LUSERID'])) {
					$this->userids[$row['LUSERID']] = true;
				} else {
					continue;
				}
			}
			++$this->total;
			if (!isset($this->forumids[$row['FORUMID']])) {
				if (is_number($row['FORUMID'])) {
					$this->forumids[$row['FORUMID']] = true;
				} else {
					continue;
				}
			}
			$this->tmp_row_list[$row['TOPICID']] = $row;
		}
		sort($this->topicidlist);
		if ($this->total > 10000) {
			ob_start();
			echo '_topiclist result with '.$this->total.' results already at '.$_SERVER['REQUEST_URI'];
			echo "\n\n";
			?>_REQUEST:<?
			echo "\n\n";
			print_r($_REQUEST);
			echo "\n\n";
			?>this:<?
			echo "\n\n";
			var_dump($this);
			file_put_contents($file = uniqid('/tmpdisk/large_topic.log_', true), ob_get_clean());
			error_log('large topic log wrtten to '.$file.' on host '.gethostname(),1,'<EMAIL>');
		}
	}
	private function query_finish(): bool {
		static $__forum_flags = [];
		$dofavs = !setting('HIDE_STARS_IN_FORUM');
		$topicidstr = implode(',', $this->topicidlist);
		if ($this->show_groups) {
			$this->party = [];
			if ($topicidstr) {
				if (false === ($parties = memcached_rowuse_hash(
					['topic', 'connect', 'location', 'boarding'], "
					SELECT	ASSOCID AS PARTYID, GROUP_CONCAT(MAINID) AS TOPICIDS,
					       	STAMP, STAMP_TZI, DURATION_SECS, party.NAME,LOCATIONID,
					       	COALESCE(boarding.CITYID, location.CITYID) AS CITYID
					FROM connect
					JOIN party ON PARTYID=ASSOCID
					LEFT JOIN location USING (LOCATIONID)
					LEFT JOIN boarding USING (BOARDINGID)
					WHERE MAINTYPE = 'topic'
					  AND ASSOCTYPE = 'party'
					  AND MAINID IN ($topicidstr)
					GROUP BY ASSOCID",
					FIVE_MINUTES))
				) {
					return false;
				}
				if ($this->party = $parties) {
					foreach ($this->party as $partyid => &$party) {
						foreach (explode(',',$party['TOPICIDS']) as $topicid) {
							$topic = $this->tmp_row_list[$topicid];
							$forumid = $topic['FORUMID'];
							$forum_flags = ($__forum_flags[$forumid] ??= memcached_forum($forumid, 'FLAGS'));
							# there are three parts to every grouped topiclist
							# 1. (0) future events of past events with lstamp within past week
							# 2. (1) unconnected topics
							# 3. (2) past events with no recent messages
							#
							# two are stored in groupcnt, unconnected are group 'apart'
							$stopstamp = $party['STAMP'] + ($party['DURATION_SECS'] ?: 6 * ONE_HOUR);
							$keep_top = $forum_flags & FORUMFLAG_SALE ? 4 * ONE_HOUR : ONE_WEEK;
							$part =	$topic['STATUS'] === 'open'
							&&	(	CURRENTSTAMP < $stopstamp + $keep_top
								||	(	$topic['LSTAMP'] > $stopstamp
									?	$topic['LSTAMP'] > CURRENTSTAMP - ONE_WEEK
									:	$topic['LSTAMP'] > CURRENTSTAMP - ONE_DAY
									)
								)
							?	0
							:	2;

							$this->groupcnt[$forumid][$part][$partyid] = 1 + ($this->groupcnt[$forumid][$part][$partyid] ?? 0);
							$this->tmp_row_list[$topicid]['CONNECT_PART'] = $part;
						}
					}
					unset($party);
				}
				unset($party);
				if (MINIMUM_TOPICS_PER_GROUP > 1
				&&	$this->groupcnt
				) {
					foreach ($this->groupcnt as $forumid => &$parts) {
						foreach ($parts as $part => &$counts) {
							foreach ($counts as $partyid => $cnt) {
								if ($cnt <= 1) {
									unset($counts[$partyid]);
								}
							}
							if (!$counts) {
								unset($parts[$part]);
							}
						}
						if (!$parts) {
							unset($this->groupcnt[$forumid]);
						}
					}
				}
			}
			if (!$this->party) {
				$this->show_groups = false;
			}
		}
		if ($topicidstr
		&&	have_user()
		) {
			$this->selfpost = get_self_posts('topic', $topicidstr);
		}
		if ($this->userids) {
			memcached_prefetch_users($this->userids);
		}
		global $__topiclist_instance;
		$__topiclist_instance = $this;

		if (/*	$_REQUEST['ACTION'] === 'firstpage'
		||*/	$this->overview
		||	$this->show_groups
		) {
			$this->forlist = [];

			if ($this->tmp_row_list) {
				uasort($this->tmp_row_list,topiccmp_full(...));
				foreach ($this->tmp_row_list as $topicid => $row) {

					$forumid = $this->tmp_row_list[$topicid]['FORUMID'];

					$row['BUDDIES'] = 0;

					$this->forlist[$forumid][$topicid] = [...$this->tmp_row_list[$topicid], ...$row];
					unset($this->tmp_row_list[$topicid]);
				}
				mail_log('tmprowlist', get_defined_vars());
				foreach (array_keys($this->tmp_row_list) as $topicid => $topic_list) {
					$this->forlist[$topicid][] = $topic_list;
				}
			}
		} else {
			// move any empty topics from tmprowlist to final list

			foreach ($this->tmp_row_list as $tmp_row) {
				$tmp_row['BUDDIES'] = 0;
				//$tmprow['MSGCNT'] = 0;

				$this->rowlist[$tmp_row['TOPICID']] = $tmp_row;
			}
			if (empty($this->search)) {
/*				if (isset($this->subscriptions)
				&&	$this->subscriptions === false
				) {
					$cmp= topiccmp_lstamp(...);
				} else*/if ($this->show_diff_self) {
					$cmp = topiccmp_max_cstamp(...);
				} else {
					switch ($_REQUEST['ACTION']) {
					case 'active-history':
					case 'pastweekend':
					case 'active':
					case 'intrecent':
						$cmp = topiccmp_lstamp(...);
						break;
					case 'openedtopics':
					case 'news':
						$cmp = topiccmp_cstamp(...);
						break;
					/*case 'busy':
						$local_stats = $this->stats;
						uasort($this->rowlist,static fn(array $a, array $b): int =>
							return $local_stats[$b['TOPICID']] <=> $local_stats[$a['TOPICID']]
						);
						break;*/
					case 'busy':
					default:
						$cmp
						=	!isset($this->controls)
						||	$this->controls->obtain_page() === 'last'
						?	topiccmp_sticky_lstamp(...)
						:	topiccmp_lstamp(...);
						break;
					}
				}
				if ($cmp) {
					uasort($this->rowlist,$cmp);
				}
			}
		}
		if ($dofavs
		&&	!empty($this->party)
		) {
			require_once '_favourite.inc';
			$this->favinfo = new favouriteinfo($this->party);
			if (!$this->favinfo->have_favourites_for_parties()) {
				unset($this->favinfo);
			}
		}
		if ((require_once '_settings.inc')
		&&	setting_isset(SHOW_TOPIC_HEARTS)
		&&	!isset($this->hide_buddies)
		&&	have_buddies()
		) {
			require_once '_hearts.inc';
			$this->hearts = new hearts('message', $topicidstr);
		}
		unset($__topiclist_instance);
		return true;
	}
	function have_rows() {
		return !empty($this->rowlist);
	}
	function display_header() {
		?><tr><?
		?><th class="left"><?= Eelement_name('topic'); ?></th><?
		$this->colcnt = 1;
		if ($this->show_connects) {		?><th class="left"><?= Eelement_name('connection')	?></th><? }
		if ($this->show_short) {		?><th class="left"><?= Eelement_name('forum')		?></th><? ++$this->colcnt; }
		if ($this->show_replycnt) {		?><th class="right">#<?										?></th><? ++$this->colcnt; }
		if ($this->show_author) {		?><th class="right"><?= Eelement_name('opener')		?></th><? ++$this->colcnt; }
		if ($this->show_cstamp) {		?><th class="right"><?= __C('field:opened')			?></th><? ++$this->colcnt; }
		if ($this->show_cstamp_time) {	?><th class="right"><?= __C('field:opened')			?></th><? ++$this->colcnt; }
		if ($this->show_last) {			?><th class="right"><?= __C('field:last')				?></th><? ++$this->colcnt; }
		if ($this->show_diff) {			?><th class="right"><?= Eelement_name('time')		?></th><? ++$this->colcnt; }
		if ($this->show_diff_self) {	?><th class="right"><?= Eelement_name('your_time')	?></th><? ++$this->colcnt; }
		?></tr><?
	}

	function set_list(&$list) {
		$this->rowlist = $list;
	}

	function display_forum($forumid) {
		if (isset($this->forlist[$forumid])) {
			$this->display($this->forlist[$forumid]);
		}
	}
	private function openrow($topic) {
		require_once '_topicrow.inc';
		if ($topic['STATUS'] === 'hidden') {
			$classes[] = 'light';
		}
		if (isset($this->hide_sales[$topic['FORUMID']])) {
			$classes[] = 'tmphidden';
		}
		?><tr class="topicrow<?
		if (isset($classes)) {
			?> <?
			echo implode(' ',$classes);
		}
		?>"><?
		$rowclass = topicrow_get_class($topic,$this->selfpost);//,empty($this->subscriptions) ? null : $this->subscriptions);
		if ($rowclass) {
			?><td class="<?= $rowclass; ?>"><?
		} else {
			?><td><?
		}
	}
	function display($rowlist = null) {
		if (!$rowlist) {
			if ($this->rowlist) {
				$rowlist = $this->rowlist;
			} else if ($this->forlist) {
				$rowlist = current($this->forlist);
			}
		}
		if (empty($rowlist)) {
			if (!isset($this->overview)) {
				?><div class="block"><?= __('topiclist:info:no_visibile_and_accessible_topics_LINE') ?></div><?
			}
			return;
		}
		if (isset($this->controls)) {
			$this->controls->display_and_store();
		} elseif (isset($this->search)) {
			ob_start();
			$have_offset = str_contains($_SERVER['REQUEST_URI'],'OFFSET=');
			if ($this->total > $this->MAX_TOPICS_PER_PAGE) {
				$newoffset = $this->offset + $this->MAX_TOPICS_PER_PAGE;
				?><div class="l"><?
				?><a rel="next nofollow" href="<?= escape_specials(
					$have_offset
				?	preg_replace('"OFFSET=(\d*)"','OFFSET='.$newoffset,$_SERVER['REQUEST_URI'])
				:	$_SERVER['REQUEST_URI'].'&OFFSET='.$newoffset
				)
				?>"><?= __('pagecontrols:next') ?></a><?
				?></div><?
			}
			if ($this->offset) {
				$newoffset = $this->offset-$this->MAX_TOPICS_PER_PAGE;
				if ($newoffset < 0) $newoffset = 0;
				?><div class="r"><?
				?><a rel="prev nofollow" href="<?= escape_specials(
					$have_offset
				?	preg_replace('"OFFSET=(\d*)"','OFFSET='.$newoffset,$_SERVER['REQUEST_URI'])
				:	$_SERVER['REQUEST_URI'].'&OFFSET='.$newoffset
				)
				?>"><?= __C('pagecontrols:previous') ?></a><?
				?></div><?
			}
			$inner = ob_get_clean();
			if ($inner) {
				ob_start();
				?><nav><?
				?><div class="block"><?= $inner
				?><div class="clear"></div><?
				?></div><?
				?></nav><?
				$controls = ob_get_flush();
			} else {
				$controls = null;
			}
		}
		if ($this->show_header) {
			?><table class="fw regular forum"><?
			$this->display_header();
		}
		$listsize = count($rowlist);
		$groupid = -1;
		$partgroup = -1;
		$cnt = 0;
		$topicpagesize = setting('MESSAGES_PER_TOPIC');
		foreach ($rowlist as $row) {
			if (!$this->overview					// not overview
			&&	!isset($_REQUEST['FIRST_PAGE'])				// and not firstpage
			&&	$cnt >= min($this->MAX_TOPICS_PER_PAGE,$listsize)	// and cnt > minimum maxperpage,currentlistsize
			) {
				break;
			}
			$forumid = $row['FORUMID'];
			$topicid = $row['TOPICID'];
			$is_inaccessible =
				(	!$row['MSGCNT']
				||	$row['STATUS'] === 'hidden'
				||	!isset($this->readable_forumids[$forumid])
				)
			&&	!$GLOBALS['currentuser']->is_forum_admin($forumid);

			if (!$this->show_inaccessible
			&&	$is_inaccessible
			) {
				continue;
			}
			$partyid = empty($row['CONNECT_PARTYIDS']) ? 0 : $row['CONNECT_PARTYIDS'][0];
			if (!$row['STICKY']
			&&	(	$this->show_connect_star
				||	$this->show_groups
				||	!$is_inaccessible
				)
			) {
				if (!isset($row['CONNECT_PART'])
				||	!isset($this->groupcnt[$forumid][$part = $row['CONNECT_PART']][$partyid])
				) {
					$part = 1;
				}
				if ($partgroup !== $part
				||	$part !== 1
				&&	$groupid !== $partyid
				) {
					if ($part !== 1) {
						$party = $this->party[$partyid];
						?><tr><th class="white" style="padding:5px 1em" colspan="<?= $this->colcnt ?>"><?
						?><a<?
/*						?> href="/party/<?= $partyid ?>/topics#topics"<?*/
						?> href="<?= get_element_href('party',$partyid) ?>#topics"<?
						?> class="bold"><?=
							escape_utf8($party['NAME'])
						?></a><?
						?><span class="nb"><?
						if (isset($this->favinfo)) {
							$this->favinfo->show_stars($partyid);
						}
						?>, <?
						date_display_link_tzi($party['STAMP_TZI']);
						if ($party['LOCATIONID']) {
							?>, <? echo get_element_link('location',$party['LOCATIONID']);
						}
						if ($party['CITYID']) {
							?>, <? echo get_element_link('city',$party['CITYID']);
						}
						?></span><?
						?></th></tr><?
					} elseif (
						isset($this->groupcnt[$forumid][0])
					) {
						?><tr><th class="white" style="padding:5px 1em" colspan="<?= $this->colcnt ?>"><?= Eelement_plural_name('other(elements)') ?></td></tr><?
					}
				}
				$groupid = $partyid;
				$partgroup = $part;
			}
			$this->openrow($row);
			//only show connect star if user is admin for this forum
			if ($flags = $row['FLAGS']) {
				require_once '_topictypes.inc';
				if ($prefix = get_topic_prefix('topic', $row['TOPICID'], $flags)) {
					echo $prefix; ?>: <?
				}
			}
			if ($row['STATUS'] === 'closed') {
				?><img<?
				?> src="<?= STATIC_HOST; ?>/images/lock<?= is_high_res() ?>.png"<?
				?> class="icon lower colorless"<?
				?> alt="(<?= $alt = __('status:closed') ?>)"<?
				?> title="(<?= $alt ?>)"> <?
			}
			if ($this->show_inaccessible
			&&	$is_inaccessible
			) {
				?><span title="<?= __C('topiclist:topic_started_on', ['DATETIME' => _datetime_get($row['TOPIC_CSTAMP'])]);
				?>"><?= escape_utf8($row['SUBJECT']);
				?></span><?

				if (!$row['MSGCNT']) {
					?> <span class="light">(<?= __('attrib:empty') ?>)</span><?
				} elseif ($row['STATUS'] === 'hidden') {
					?> <span class="light">(<?= __('elementstatus:removed') ?>)</span><?
				} else {
					?> <span class="light">(<?= __('attrib:inaccessible') ?>)</span><?
				}
				$cols = 0;
				if ($this->show_connects)		++$cols;
				if ($this->show_short)			++$cols;
				if ($this->show_replycnt) 		++$cols;
				if ($this->show_author)			++$cols;
				if ($this->show_cstamp)			++$cols;
				if ($this->show_cstamp_time)	++$cols;
				if ($this->show_last)			++$cols;
				if ($this->show_diff)			++$cols;
				if ($this->show_diff_self)		++$cols;
				while ($cols--) {
					?><td></td><?
				}
				continue;
			}
			++$cnt;
			?><a<?
			if (!empty($this->target)) {
				?> target="<?= $this->target ?>"<?
			}
			?> title="<?= __C('topiclist:topic_started_on', ['DATETIME' => _datetime_get($row['TOPIC_CSTAMP'])]) ?>"<?
			?> href="<?
			echo $href =
				$this->hilite
			?	'/topic/'.$topicid.'?HILITE='.urlencode(implode(',', $this->hilite))
			:	get_element_href('topic', $topicid, $row['SUBJECT']);
			?>"><?= flat_with_entities($row['SUBJECT'], UBB_UTF8 | UBB_NO_LINKS | UBB_STATIC_SMILEYS | UBB_DONT_ADD_UTM, 'topic', $topicid);
			?></a><?

			if ($partyid
			&&	$this->show_connect_star
			) {
				static $__bubble = [];
				?> <div class="ib"><?
				if (!($bubble = $__bubble[$partyid] ?? null)) {
					require_once '_bubble.inc';
					require_once '_star.inc';
					$bubble = new bubble(BUBBLE_CLEAN | BUBBLE_CURSOR | CLICK_FOR_QUICK);
					$bubble->catcher(get_star('party'));
					$bubble->content();
					echo get_element_link('party', $partyid);
					$bubble->close();
				}
				$bubble->display();
				$__bubble[$partyid] = $bubble;
				?></div><?
			}

			if (!empty($this->hearts)) {
				$this->hearts->show($topicid);
			}

			if ($groupid === -1
			&&	isset($this->favinfo)
			&&	$row['CONNECT_PARTYIDS']
			) {
				$this->favinfo->show_stars($row['CONNECT_PARTYIDS']);
			}
			if ($fetch
			=	isset($row['MSGNO'])
			?	1
			:	isset($this->selfpost[$topicid])
			) {
				if ($fetch === true) {
					[$row['MSGNO'], $row['MESSAGEID']] = keyval($this->selfpost[$topicid]);
				}
				if ($row['MSGNO']
				&&	$row['MESSAGEID']
				) {
					?> <a<?
					?> href="<?
					if ($row['MSGNO'] === 1) {
						echo $href;
					} elseif ($row['MSGNO'] >= $row['MSGCNT'] - $topicpagesize) {
						echo $href,'#m',$row['MESSAGEID'];
					} else {
						?>/topic/<?= $topicid; ?>/page/<?= ceil($row['MSGNO'] / $topicpagesize); ?>#m<? echo $row['MESSAGEID'];
					}
					// &sim;
					?>"><?= get_user_indicator_icon() ?></a><?
				}
			}
			?> <a class="light" href="<?= $href; ?>#bottom"><?= get_to_end_icon() ?></a></td><?
			if ($this->show_connects) {
				if ($row['CONNECT_PARTYIDS']) {
					?><td></td><?
				} else {
					?><td><input type="text" name="NAME[<?= $topicid ?>]" /></td><?
				}
			}
			if ($this->show_short) {
				?><td><a href="<?= get_element_href('forum',$forumid) ?>"><?= $row['SHORT']; ?></a></td><?
			}
			if ($this->show_replycnt) {
				?><td class="right small"><?
				if (isset($this->stats)) {
					echo $this->stats[$row['TOPICID']];
				} else {
					echo $row['MSGCNT'] - $row['BADMSGCNT'];
				}
				?></td><?
			}
			if ($this->show_author) {
				require_once 'defines/topic.inc';
				?><td class="right small user-cell"><?
				echo	($row['FLAGS'] & TOPIC_IS_ANONYMOUS)
				?	'<span class="light">'.(
						$row['USERID'] === CURRENTUSERID
					||	$GLOBALS['currentuser']->any_forum_admin()
					?	get_element_link('user', $row['USERID'])
					:	__('field:anonymous')
					).'</span>'
				:	get_element_link('user', $row['USERID'], title_attr: true);
				?></td><?
			}
			if ($this->show_cstamp) {
				?><td class="right date-cell"><?
				?><time datetime="<?= gmdate(ISO8601Z,$row['TOPIC_CSTAMP']) ?>"><?
				_datetime_display($row['TOPIC_CSTAMP'], short: true);
				?></time><?
				?></td><?
			}
			if ($this->show_cstamp_time) {
				?><td class="right data-cell"><?
				?><time datetime="<?= gmdate(ISO8601Z,$row['TOPIC_CSTAMP']) ?>" title="<? _datedaytime_display($row['TOPIC_CSTAMP']) ?>"><?
				show_short_diff($row['TOPIC_CSTAMP']);
				?></time><?
				?></td><?
			}
			if ($this->show_last) {
				$last_message_flags = db_single('message', "
					SELECT FLAGS
					FROM message
					WHERE TOPICID = $topicid
					ORDER BY MESSAGEID DESC
					LIMIT 1"
				);
				?><td class="small right user-cell"><?
				require_once 'defines/post.inc';
				echo	(	in_array($last_message_flags, [null, false], true)
						||	($last_message_flags & POST_IS_ANONYMOUS)
						)
					?	'<span class="light">'.(
							$row['LUSERID'] === CURRENTUSERID
						||	$GLOBALS['currentuser']->any_forum_admin()
						?	get_element_link('user', $row['LUSERID'])
						:	__('field:anonymous')
						).'</span>'
					:	get_element_link('user', $row['LUSERID'], title_attr: true);
				?></td><?
			}
			if ($this->show_diff) {
				?><td class="right date-cell"><?
				?><time datetime="<?= gmdate(ISO8601Z,$row['LSTAMP']) ?>" title="<? _datedaytime_display($row['LSTAMP']) ?>"><?
				show_short_diff($row['LSTAMP']);
				?></time><?
				?></td><?
			}
			if ($this->show_diff_self) {
				?><td class="right date-cell"><time datetime="<?= gmdate(ISO8601Z,$row['LAST_POST']) ?>" title="<? _datedaytime_display($row['LAST_POST']) ?>"><?
				show_short_diff($row['LAST_POST']);
				?></time></td><?
			}
			?></tr><?
		}
		if ($this->show_header) {
			?></table><?
		}
		if (isset($this->controls)) {
			$this->controls->display_stored();
		} elseif (!empty($controls)) {
			echo $controls;
		}
	}
	function in_which_user_posted(int $userid): void {
		$this->wheres[] = 'postedinv3.USERID='.$userid;
		$this->userid = $userid;
		$this->joins['postedinv3'] = 'JOIN postedinv3 ON ELEMENT="topic" AND TOPICID=ID';
		$this->tables[] = 'postedinv3';
	}

	function opened_by_user(int $userid): void {
		// FIXME: use postedin
		// nooo! postedin might not be correct! due to unaccept/accept
		// for non-admins ..
		require_once 'defines/topic.inc';
		$this->wheres[] =
			"topic.USERID = $userid".(
				CURRENTUSERID === $userid
			?	''
			:	' AND NOT (topic.FLAGS & '.TOPIC_IS_ANONYMOUS.')'
			);
		$this->opened = $userid;
		$this->userid = $userid;
	}

	function not_in_forum(int|array $arg): void {
		if (is_array($arg)) {
			sort($arg);
		}
		$this->in_forum = is_int($arg)
		?	"topic.FORUMID NOT IN ($arg)"
		:	'topic.FORUMID NOT IN ('.implode(', ',$arg).')';
		$this->wheres[] = $this->in_forum;
	}
	function in_forum(int|array $arg): void {
		if (is_array($arg)) {
			sort($arg);
		}
		$this->in_forum = is_int($arg)
		?	'topic.FORUMID /* in_forum.1 arg:'.addslashes(get_r($arg))." */ IN ($arg)"
		:	'topic.FORUMID /* in_forum.2 arg:'.addslashes(get_r($arg)).' */ IN ('.implode(', ',$arg).')';
		$this->wheres[] = $this->in_forum;
	}
	function in_fora(array $forum_list): void {
		sort($forum_list);
		$this->wheres[] = 'topic.FORUMID IN ('.implode(',',$forum_list).')';
	}
	function in_fora_or_subscribed(array $forum_list) {
		if ($forum_list) {
			sort($forum_list);
			$forum_idstr = implode(',',$forum_list);
		}
		require_once '_subscriptions.inc';
		if ($topic_ids = get_subscriptions('topic')) {
			sort($topic_ids);
			$topic_idstr = implode(',',$topic_ids);
			if ($forum_list) {
				$this->wheres[] = "(	topic.FORUMID IN ($forum_idstr)
									OR	topic.TOPICID IN ($topic_idstr))";

				$local_total = count($topic_ids) + memcached_single_int('forum_stats', "
					SELECT CAST(SUM(TOPIX) AS UNSIGNED)
					FROM party_db.forum_stats
					WHERE FORUMID IN ($forum_idstr)");
			} else {
				$this->wheres[] = "topic.TOPICID IN ($topic_idstr)";

				$local_total = count($topic_ids);
			}
		} elseif ($forum_list) {
			$this->wheres[] = "topic.FORUMID IN ($forum_idstr)";

			$local_total = memcached_single_int('forum_stats', "
				SELECT CAST(SUM(TOPIX) AS UNSIGNED)
				FROM forum_stats
				WHERE FORUMID IN ($forum_idstr)");
		} else {
			$this->wheres[] = '0';
			$local_total = 0;
		}
		error_log('DEBUG in_fora_or_subscribed: local_total: '.$local_total);
	}
	function past_weekend(): void {
		require_once '_party.inc';
		if ($topicids = memcached_simpler_array(['party', 'connect'],'
			SELECT ASSOCID
			FROM party
			JOIN connect
				 ON MAINTYPE = "party"
				AND MAINID = PARTYID
				AND ASSOCTYPE = "topic"
			WHERE STAMP >= '.strtotime('-1 week',TODAYSTAMP).'
			  AND STAMP  < '.strtotime('tomorrow +'.HOUR_DAY_START.' hours',TODAYSTAMP))
		) {
			sort($topicids);
			$this->wheres[] = 'topic.TOPICID IN ('.implode(', ',$topicids).')';
		} else {
			$this->wheres[] = '0';
		}
	}
	final function order_reverse_lstamp(): void {
		$this->order = 'topic.LSTAMP';
	}
	final function order_reverse_lastpost(): void {
		$this->order = 'postedinv3.LSTAMP';
	}
	final public function order_reverse_cstamp(): void {
		$this->order = 'topic.TOPICID';
		$this->orders[] = 'topic.TOPICID DESC';
	}
	final public function connected_to_party(int $partyid): void {
		$this->show_connect_star = false;
		$this->wheres[] = 'ptt.MAINTYPE="party" AND ptt.ASSOCTYPE="topic" AND ptt.MAINID='.$partyid;
		$this->joins['connect'] = 'RIGHT JOIN connect AS ptt ON ptt.ASSOCID=TOPICID';
		$this->tables[] = 'connect';
	}
	final public function hide_sales(): void {
		require_once '_forum.inc';
		$this->hide_sales = get_sale_forums();
	}
	final public function busy(int $period, bool $progressive = false): bool {
		require_once '_findid.inc';
		if (!($first_id = find_first_id('message',TODAYSTAMP - $period))) {
			$this->wheres[] = '0';
			return false;
		}
		if (!$progressive) {
			$stats = db_simple_hash('message', "
				SELECT TOPICID, COUNT(*)
				FROM message
				WHERE MESSAGEID >= $first_id
				GROUP BY TOPICID
				ODER BY COUNT(*) DESC");
		} else {
			$stats = db_simple_hash('message',/** @lang MariaDB*/ '
				SELECT TOPICID, ROUND(SUM( ('.CURRENTSTAMP." - CAST(CSTAMP AS SIGNED)) / $period))
				FROM message
				WHERE MESSAGEID >= $first_id
				GROUP BY TOPICID
				ORDER BY COUNT(*) DESC");
		}
		if ($stats === false) {
			$this->wheres[] = '0';
			return false;
		}
		$this->stats = $stats;
		$this->tmp_total = count($this->stats);
		$this->joins[] = 'JOIN message USING (TOPICID)';
		$this->wheres[] = "MESSAGEID >= $first_id";
		$this->order = 'COUNT(*) DESC';
		$this->ignore_sticky = true;
		return true;
	}
}
function topiccmp_groups($a,$b): int {
	global $__topiclist_instance;
	if (!$__topiclist_instance->show_groups
	||	!isset($a['CONNECT_PART'])
	&&	!isset($b['CONNECT_PART'])
	) {
		return 0;
	}
	$apart = get_part($a);
	$bpart = get_part($b);
	if ($apart !== $bpart) {
		return $apart <=> $bpart;
	}
	if ($apart === 1) {
		return 0;
	}
	$aparty = $__topiclist_instance->party[$a['CONNECT_PARTYIDS'][0]];
	$bparty = $__topiclist_instance->party[$b['CONNECT_PARTYIDS'][0]];
	return	strcasecmp($aparty['NAME'], $bparty['NAME'])
		?:	($aparty['STAMP_TZI'] <=> $bparty['STAMP_TZI']);
}
function get_part($a) {
	if (!isset($a['CONNECT_PART'])) {
		return 1;
	}
	global $__topiclist_instance;
	$apartyid	= $a['CONNECT_PARTYIDS'][0];
	$apart		= $a['CONNECT_PART'];
	if (($__topiclist_instance->groupcnt[$a['FORUMID']][$apart][$apartyid] ?? 0) < MINIMUM_TOPICS_PER_GROUP) {
		return 1;
	}
	return $apart;
}
function topiccmp_sticky(array $a, array $b): int {
	return (int)$b['STICKY'] <=> (bool)$a['STICKY'];
}
function topiccmp_full(array $a, array $b): int {
	return	topiccmp_sticky($a,$b)
	?:		topiccmp_groups($a,$b)
	?:		topiccmp_lstamp($a,$b);
}
function topiccmp_sticky_lstamp(array $a, array $b): int {
	return	topiccmp_sticky($a,$b)
	?:		topiccmp_lstamp($a,$b);
}
function topiccmp_lstamp(array $a, array $b): int {
	return $b['LSTAMP'] <=> $a['LSTAMP'];
}
function topiccmp_max_cstamp(array $a, array $b): int {
	return $b['LAST_POST'] <=> $a['LAST_POST'];
}
function topiccmp_cstamp(array $a, array $b): int {
	return $b['TOPIC_CSTAMP'] <=> $a['TOPIC_CSTAMP'];
}
