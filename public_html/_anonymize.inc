<?php

declare(strict_types=1);

require_once '_db.inc';

# $spam: true	-> find similar
#	 false	-> store anonymised, but don't find similar
#	 null	-> get info
function find_anonymised_matches(int $id, ?bool $spam, bool $multi = true, bool $validate = false): array|false {
	require_once 'defines/directmessage.inc';
	static $__info = [
		'from_userid'		=> 0,
		'make_anonymous'	=> false,
		'remove_these'		=> [],
		'exacts'			=> [],
		'distinct_senders'	=> [],
	];
	if ($spam === null) {
		return $__info;
	}
	if (!($message = db_single_assoc(['directmessage_data', 'directmessage'],"
		SELECT SQL_NO_CACHE FROM_USERID, FLAGS, CSTAMP, BODY
		FROM directmessage_data
		JOIN directmessage USING (MESSAGEID)
		WHERE MESSAGEID = $id"))
	) {
		return false;
	}
	$__info['from_userid'] = $message['FROM_USERID'];
	$and_force_from = $multi ? '' : ' AND FROM_USERID = '.$message['FROM_USERID'];
	if ($spam) {
		$key = 'spamstuff:msgid:'.$id;
		if (!db_getlock($key,FIVE_MINUTES)) {
			return false;
		}
		if (!($__info['exacts'] = memcached_get($key))) {
			$message_length = strlen($message['BODY']);
			$message_crc = crc32($message['BODY']);

			# use regular server, backup is way behind
			# make sure we use master, because slave does not have temporary table
			$flags = DB_FORCE_MASTER;

			# NOTE: no check on actual BODY contents... too slow
			if (!db_create('directmessage_lookup', "
				CREATE TEMPORARY TABLE msgoff (MESSAGEID int unsigned not null default 0 key)
				SELECT DISTINCT MESSAGEID
				FROM directmessage_lookup
				WHERE CRC32 = $message_crc
				  AND LEN = $message_length",
				$flags)
			) {
				return false;
			}
			if ($and_force_from) {
				if (false === ($__info['exacts'] = db_same_hash('directmessage_lookup', "
					SELECT SQL_NO_CACHE MESSAGEID
					FROM msgoff
					JOIN directmessage USING (MESSAGEID)
					WHERE 1 $and_force_from
					  AND READM IN (0, 1)",
					$flags))
				) {
					return false;
				}
			} elseif (false === ($__info['exacts'] = db_same_hash('directmessage_lookup', '
				SELECT SQL_NO_CACHE MESSAGEID
				FROM msgoff',
				$flags))
			) {
				return false;
			}
			memcached_set($key, $__info['exacts'], ONE_DAY);
		}
	}
	if ($__info['make_anonymous']
	=	!empty($_POST['MAKE_ANONYMOUS'])
	&&	!empty($_POST['ANONYMOUS_BODY'])
	) {
		$body = $_POST['ANONYMOUS_BODY'];
		$pre_body = str_replace('%PERSONAL%',"\x1F",$body);

		if (!db_replace('anonymised_directmessage',"
			REPLACE INTO anonymised_directmessage SET
				BODY		= '".addslashes($pre_body)."',
				MESSAGEID	= $id")
		) {
			return false;
		}
	} elseif (!have_post()) {
		# if post, then only accept what was given in offense form, otherwise display info
		if (false === ($body = db_single('anonymised_directmessage',"
			SELECT BODY
			FROM anonymised_directmessage
			where MESSAGEID = $id",
			DB_USE_MASTER
		))) {
			return false;
		}
		$pre_body   = $body;
	}
	if (!empty($pre_body) && $spam) {
		$lower_body = mb_strtolower($pre_body);
		$find_body  = str_replace(["\x1F", "\r\n"], ['%', "\n"], $pre_body);

		if (!($start_messageid = find_messageid($id, $message['CSTAMP'],true))
		||	!( $stop_messageid = find_messageid($id, $message['CSTAMP'],false))
		||	false === ($messages = memcached_simple_hash(['directmessage', 'directmessage_data'], /** @lang MariaDB */ "
			SELECT MESSAGEID, BODY
			FROM directmessage
			JOIN directmessage_data USING (MESSAGEID)
			WHERE REPLACE(BODY, '\r\n', '\n') LIKE '".addslashes($find_body)."'
			  AND MESSAGEID BETWEEN $start_messageid AND $stop_messageid
			 $and_force_from",
			ONE_DAY))
		) {
			return false;
		}
		$remove_these = &$__info['remove_these'];
		if ($messages) {
			set_time_limit(FIVE_MINUTES);
		}
		foreach ($messages as $messageid => $stored_body) {
			similar_text(strtolower($stored_body),$lower_body,$pct);
			if ($pct >= 75) {
				$remove_these[$messageid] = $messageid;
			}
		}
	}
	$remove_these = $__info['remove_these'] + $__info['exacts'];
	$remove_count = count($remove_these);

	if ($validate
	&&	($remove_count > 2000 || $remove_count === 1)
	&&	!have_super_admin()
	) {
		_error($remove_count.' te verwijderen berichten, gaat dit wel goed?');
		return false;
	}
	if ($remove_count <= 1) {
		$__info['distinct_senders'][$__info['from_userid']] = 1;
		return $__info;
	}
	$step = 1000;
	$total = (int)ceil(count($remove_these) / $step);
	$distinct_senders = [];
	for ($i = 0; $i < $total; ++$i) {
		$part = array_slice($remove_these,$i * $step,$step);
		if (false === ($tmp = memcached_simple_hash('directmessage','
			SELECT FROM_USERID,COUNT(*)
			FROM directmessage
			WHERE MESSAGEID IN ('.implode(',',$part).')
			GROUP BY FROM_USERID',
			ONE_DAY))
		) {
			return false;
		}
		foreach ($tmp as $userid => $cnt) {
			increment_or_set($distinct_senders, $userid, $cnt);
		}
	}
	if ($validate
	&&	$multi
	&&	($cnt = count($distinct_senders)) > 10
	&&	!have_super_admin()
	) {
		_error($cnt.' verschillende verzenders, gaat dit wel goed?');
		return false;
	}
	$__info['distinct_senders'] = $distinct_senders;
	return $__info;
}

function find_messageid(int $id, int $stamp, bool $start): int|false {
	$stamp = $stamp + ($start ? -1 : 1) * 2 * ONE_WEEK;
	for ($messageid = $id; ; $messageid += ($start ? -1000 : 1000)) {
		if (false === ($message = db_single_assoc('directmessage','
			SELECT MESSAGEID, CSTAMP
			FROM directmessage
			WHERE MESSAGEID '.($start ? '<' : '>')." = $messageid
			ORDER BY MESSAGEID ".($start ? 'DESC' : 'ASC').'
			LIMIT 1'))
		) {
			return false;
		}
		if (!$message) {
			if ($start) {
				return 1;
			}
			return db_single('directmessage', 'SELECT MAX(MESSAGEID) FROM directmessage');
		}
		if ($start ? $message['CSTAMP'] < $stamp : $message['CSTAMP'] > $stamp) {
			return $message['MESSAGEID'];
		}
	}
	/** @noinspection PhpUnreachableStatementInspection */
	return 0;
}

function show_make_anonymous(int $id): bool {
	if (false === ($body = db_single('anonymised_directmessage', "
		SELECT BODY
		FROM anonymised_directmessage
		WHERE MESSAGEID = $id"))
	) {
		return false;
	}
	if ($body) {
		$anon = true;
		$body = str_replace("\x1F", '%PERSONAL%', $body);
	} else {
		require_once '_directmessage.inc';
		if (false === ($message = db_single_array('directmessage_data','
			SELECT BODY, FLAGS
			FROM directmessage_data
			JOIN directmessage USING (MESSAGEID)
			WHERE MESSAGEID = '.$id
		))) {
			return false;
		}
		if (!$message) {
			register_error('directmessage:error:nonexistent_LINE', ['ID' => $id]);
			return false;
		}
		[$body, /* $flags */] = $message;
	}
	layout_restart_row(NOWRAP);
	?><label for="make-anonymous"><?=  __C('action:make_anonymous') ?></label><?
	layout_field_value();
	show_input([
		'type'		=> 'checkbox',
		'id'		=> 'make-anonymous',
		'name'		=> 'MAKE_ANONYMOUS',
		'onclick'	=> /** @lang JavaScript */ "setdisplay('anonymous-part', this.checked);",
		'checked'	=> isset($anon),
	]);
	include_js('js/anonymise');
	?><div id="anonymous-part"<?
	if (!isset($anon)) {
		?> class="hidden"<?
	}
	?>><?= __('directmessage:info:make_anonymous_LINE') ?><br /><?
	?><form accept-charset="utf-8"><?
	show_textarea([
		'name'		=> 'ANONYMOUS_BODY',
		'id'		=> 'anonymous-body',
		'rows'		=> 10,
		'cols'		=> 70,
		'value'		=> $body,
	],true);
	?><input type="button" value="<?= __('action:check') ?>" onmousedown="hide('anonymize-done')" onclick="check_counts(this, <?= $id ?>);" /> <?
	?><span id="anonymize-done" class="hidden"><?= __('status:done') ?></span><?
	?></form><?
	?></div><?
	return true;
}

function show_anonymous_part(int $id): bool {
	if (false === ($info = find_anonymised_matches($id, true))) {
		return false;
	}
	layout_restart_row();
	echo Eelement_name('amount');

	[$total, $current] = get_anonymize_counts($info);

	layout_field_value();
	?><div id="amount-info"><?= $total === 1 ? 1 : __('directmessage:amount:info', ['TOTAL' => $total, 'USER' => $current]) ?></div><?

	return show_make_anonymous($id);
}

function get_anonymize_counts(array $info): array {
	$from_userid = $info['from_userid'];
	return [	(isset($info['remove_these']) ? count($info['remove_these']) : 0)
        	+	(isset($info['exacts'])		  ? count($info['exacts'])	   : 0),
			$info['distinct_senders'][$from_userid]
	];
}
