<?php

function is_offense_type($type = null) {
	static $__offense_types = [
	#	TYPE				   POINTS/PERM	ELEMENTS
		'copyright_infringement'	=> [ 0 => ['public']],
		'portraitright_infringement'	=> [ 0 => ['public']],
		'discrimination_racism'		=> [ 3 => ['public','private'],
							'p'=> ['user']],
		'double'			=> [ 0 => ['public'],
							'p'=> ['user']],
		'bumping'			=> [ 2 => ['user']],
		'fake'				=> ['p'=> ['user'],
							1 => ['nick']],
		'false_information'		=> [],
#		'flag_weapon'			=> [],
		'ignore_ban'			=> [ 2 => ['user']],
		'inappropriate'			=> [ 1 => ['public','private']],
		'incorrect_connection'		=> [ 0 => ['public']],
#		'middle_finger'			=> [],
		'no_price'			=> [ 0 => ['public']],
		'nonsense'			=> [ 0 => ['public']],
		'offensive_provocative'		=> ['p'=> ['user'],
							2 => ['public']],
		'drugs_dealing'			=> [ 2 => ['public','private']],
		'drugs_incitement'		=> [ 1 => ['public','private']],
		'overloading'			=> [ 2 => ['user']],
		'permanent'			=> ['p'=> ['user']],
#		'photo_album'			=> [],
		'plagiarism'			=> [ 1 => ['public']],
		'price_too_high'		=> [ 1 => ['public']],
		'riot'				=> ['p'=> ['user']],
		'single_question_comment'	=> [ 1 => ['flock']],
#		'soccer'			=> [ 1 => ['public']],
#		'spam'				=> [ 2 => ['private','public'],
#							'p'=> ['user']],
		'spam_other'			=> [ 2 => ['private','public'],
							'p'=> ['user']],
		'spam_ad'			=> [ 2 => ['private','public'],
							'p'=> ['user']],
		'spam_using_other'		=> [ 2 => ['user']],
		'threat'			=> [ 3 => ['private'],
							2 => ['public']],
	];
	return $type ? isset($__offense_types[$type]) : $__offense_types;
}
function offense_types($element = null,$id = 0,$userid = 0) {
	$__offense_types = is_offense_type();
	if (!$element) {
		return $__offense_types;
	}
	switch ($element) {
	case 'valentine':
	case 'directmessage':
		$need = 'private';
		break;
	case 'user':
		$need = 'user';
		break;
	default:
		$need = 'public';
		break;
	}
	$types = [];
	foreach ($__offense_types as $type => $spec) {
		switch ($type) {
		case 'incorrect_connection':
			if ($element == 'albummap'
			||	$element == 'albumelement'
			)	continue 2;
			else	break;
		case 'no_price':
		case 'price_too_high':
			require_once '_forum.inc';
			if ($element == 'party_comment'
			||	str_contains($element,'_comment')
			||	$element == 'message'
/*			&&	db_single(['forum','topic','message'],'
				SELECT forum.FLAGS&'.FORUMFLAG_SALE.'
				FROM message
				JOIN topic USING (TOPICID)
				JOIN forum USING (FORUMID)
				WHERE MESSAGEID='.$id,
				DB_USE_MASTER)*/
			||	$element == 'topic'
/*			&&	db_single(['forum','topic','message'],'
				SELECT forum.FLAGS&'.FORUMFLAG_SALE.'
				FROM topic
				JOIN forum USING (FORUMID)
				WHERE TOPICID='.$id,
				DB_USE_MASTER)*/
			)	break;
			else	continue 2;
		}
		foreach ($spec as $points => $parts) {
			if (!$points
			&&	(	$type == 'double'
				||	$type == 'nonsense'
				)
			) {
				require_once 'defines/offense.inc';
				extract(get_offense_defines(CURRENTSTAMP));
				$lastnonzero = db_single('offense','
					SELECT OFFENSEID
					FROM offense
					WHERE TYPE="'.$type.'"
					  AND ELEMENT="'.$element.'"
					  AND USERID='.$userid.'
					  AND REMOVED = 0
					  AND POINTS
					  AND CSTAMP>'.(CURRENTSTAMP - $OFFENSE_ACTIVE_PERIOD).'
					ORDER BY OFFENSEID DESC
					LIMIT 1',
					DB_USE_MASTER
				);
				if ($lastnonzero === false) {
					return false;
				}
				$zeros = db_single('offense','
					SELECT COUNT(*)
					FROM offense
					WHERE TYPE="'.$type.'"
					  AND ELEMENT="'.$element.'"
					  AND USERID='.$userid.'
					  AND REMOVED = 0
					  AND OFFENSEID>'.($lastnonzero ? $lastnonzero : 0).'
					  AND CSTAMP>'.(CURRENTSTAMP - $OFFENSE_ACTIVE_PERIOD),
					DB_USE_MASTER
				);
				if ($zeros === false) {
					return false;
				}
				if ($zeros >= 4) {
					$points = 1;
				}
			}
			foreach ($parts as $part) {
				if ($part === $element
				||	(	is_scalar($need)
					?	($need == $part)
					:	in_array($part,$need)
					)
				) {
					$types[] = [
						'TYPE'		=> $type,
						'TYPENAME'	=> ($typename = __('offense:type:'.$type)),
						'POINTS'	=> $points,
						'SORTFIELD'	=> $points.'_'.$typename
					];
				}
			}
		}
	}
	require_once '_sort.inc';
	string_sort($types,'SORTFIELD');
	return $types;
}
function show_offense_type_for_prohib_options($selected = null) {
	$types = offense_types('public');
	if (!$types) {
		return;
	}
	$found = false;
	foreach ($types as $type) {
		?><option<?
		if ($selected
		&&	$selected == $type['TYPE']) {
			?> selected="selected"<?
			$found = true;
		}
		?> value="<?= $type['TYPE'] ?>"><?= $type['POINTS'] ?>: <?= $type['TYPENAME'] ?></option><?
	}
	if (!$found) {
		?><option selected="selected" value="<?= $selected ?>">?: <?= __('offense:type:'.$selected) ?></option><?
	}
	return $types;
}
function show_offense_type_options($element,$id = 0,$userid = 0) {
	$types = offense_types($element,$id,$userid);
	if (!$types) {
		return;
	}
	foreach ($types as $type) {
		?><option value="<?= $type['TYPE'] ?>:<?= $type['POINTS'] ?>"><?= $type['POINTS'] ?>: <?= $type['TYPENAME']; ?></option><?
	}
	return $types;
}
function include_offense_form($types,$element,$permbanwarn) {
	?><script>
	function changeOffense(type_points) {
		var parts = type_points.split(':');
		var type = parts[0];
		var points = parts[1];<?
		if ($element != 'user') { //?>
			setdisplay('regonly-row',points != '0'); <?
		}
		switch ($element) {
		case 'topic':
		case 'flocktopic':
		case 'flock': //?>
			setdisplay('altidrow',type == 'double'); <?
			break;
		case 'albummap':
		case 'albumelement': //?>
			var proposable =
					type == 'inappropriate'
				||	type == 'offensive_provocative'
				||	type == 'discrimination_racism'
				||	type == 'threat';
			setdisplay('proposepart',proposable);
			var propobj = getobj('propose',true);
			if (propobj) {
				propobj.checked = proposable;
			} <?
			break;
		case 'user':
			if ($permbanwarn) { //?>
				setdisplay('permbanwarn',
						points == 'p'
				); <?
			} //?>
			setdisplay('refidrow',
					type == 'double'
				||	type == 'permanent'
				||	type == 'riot'
				||	type == 'fake'
				||	type == 'spam'
				||	type == 'spam_other'
				||	type == 'spam_ad'
				|| 	type == 'spam_using_other'
				||	type == 'ignore_ban'
			); <?
			break;
		case 'directmessage': //?>
			//var reach = getobj('reach',true);
			//if (!reach) return;
			//reach.checked = (type == 'spam');
			setdisplay(['reachrow','removerow'],/^(?:spam|drugs)/.test(type));
			//cbclickp(reach);
			<?
			break;
		}
	//?>
	}
	</script><?
}
