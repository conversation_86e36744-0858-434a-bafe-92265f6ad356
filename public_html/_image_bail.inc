<?php

declare(strict_types=1);

/**
 * @param int|bool|http_status|null $errno	int: http status code
 *                                        	bool: true = 200, false = 500
 *                                         	http_status: http_status code
 *                                         	null: 404
 */

function image_bail(int|bool|http_status|null $errno): never {
	require_once '_spider.inc';
	require_once '_servertype.inc';
	require_once '_http_status.inc';
	/** @noinspection SpellCheckingInspection */
	global $dbgid;
	if ($errno instanceof http_status) {
		$errno = $errno->value;
	} elseif ($errno === true) {
		$errno = http_status::OK->value;
	} elseif ($errno === false) {
		$errno = http_status::INTERNAL_SERVER_ERROR->value;
	} elseif ($errno === null) {
		$errno = http_status::NOT_FOUND->value;
	}
	assert(is_int($errno)); # Satisfy EA inspection
	if (!ROBOT && $errno >= http_status::BAD_REQUEST->value) {
		$img = match($errno) {
			http_status::FORBIDDEN->value	=> 'forbidden',
			http_status::NOT_FOUND->value,
			http_status::GONE->value		=> 'notfound',
			default							=> 'failure',
		};
		if (headers_sent($filename, $line)) {
			error_log("$dbgid WARNING image_bail(): $errno already sent in $filename:$line");
		}
		header('Content-Type: image/png');
		header('Cache-Control: no-cache,no-store,must-revalidate,max-age=0');
		$error_image = __DIR__."/../static_html/overlays/$img.png";
		header('Content-Length: '.filesize($error_image));
		readfile($error_image);
	}
	if ($errno >= http_status::INTERNAL_SERVER_ERROR->value) {
		HOME_THOMAS && error_log("$dbgid ERROR image_bail called with status $errno @ {$_SERVER['REQUEST_URI']}");
		header('Retry-After: '.ONE_HOUR, false, $errno);
	}
	global $debug_upload_images;
	($debug_upload_images || HOME_THOMAS) && error_log("$dbgid DEBUG exiting in image_bail with errno $errno @ {$_SERVER['REQUEST_URI']}");
	http_response_code($errno);
	exit;
}
