<?php

require_once '_pagecontrols.inc';
require_once '_subscriptions.inc';
require_once '_topicrow.inc';

class subscriptionoverview {
	private ?_pagecontrols	 $controls	= null;
	private ?string			 $element	= null;
	private ?string			 $q;
	private int				 $pagesize	= 50;
	private array|false|null $subs		= null;
	private bool			 $always_show;

	public function __construct($userid,$element = null) {
		if ($element) {
			$this->always_show = setting_isset(ALWAYS_SHOW_SUBS);
			if (!($info = get_subscription_counts($userid, $element))
			||	!$info[$this->always_show ? 0 : 1]
			) {
				return;
			}
			$this->element = $element;
			$wheres = '
				AND ELEMENT="'.$element.'"'.(
					$this->always_show
				?	null
				:	' AND IF(COALESCE(topic.MSGCNT,flocktopic.MSGCNT,commentsinfo.MSGCNT) > LASTNOSEEN,1,0)');
		} else {
			$total = subscription_total($userid);
			$this->controls = new _pagecontrols;
			$this->controls->set_total($total);
			$this->controls->set_per_page($this->pagesize);
#			$this->controls->set_url('/user/'.$userid.'/subscriptions');
			$this->controls->set_order('LSTAMP');
#			print_rr($total,'total');
			$wheres = null;
			$this->always_show = false;
		}

			$selects = '
				COALESCE(topic.LSTAMP,flocktopic.LSTAMP,commentsinfo.LSTAMP,subscription.CSTAMP) AS LSTAMP,
				COALESCE(topic.LUSERID,flocktopic.LUSERID,commentsinfo.LUSERID) AS LUSERID,
				COALESCE(topic.SUBJECT,flocktopic.SUBJECT,"") AS SUBJECT,
				COALESCE(topic.STICKY, flocktopic.STICKY,0)  AS STICKY,
				topic.STICKY AS TOPIC_STICKY,
				flocktopic.STICKY AS FLOCKTOPIC_STICKY,
				COALESCE(topic.USERID, flocktopic.USERID,0)  AS USERID,
				COALESCE(topic.STATUS, flocktopic.STATUS,"")  AS STATUS,
				flocktopic.FLOCKID,REMOVED,PRIVATE,flock.NAME AS FLOCK_NAME,
				topic.FORUMID,forum.NAME AS FORUM_NAME,
				COALESCE(topic.FLAGS,flocktopic.FLAGS) AS FLAGS,
				COALESCE(topic.MSGCNT,flocktopic.MSGCNT,commentsinfo.MSGCNT) AS MSGCNT,
				postedinv3.MESSAGEID,postedinv3.MSGNO';
			$joins = '
				LEFT JOIN topic ON ELEMENT="topic" AND ID=topic.TOPICID
				LEFT JOIN forum ON forum.FORUMID=topic.FORUMID
				LEFT JOIN flocktopic ON ELEMENT="flocktopic" AND ID=flocktopic.TOPICID
				LEFT JOIN flock ON flock.FLOCKID=flocktopic.FLOCKID
				LEFT JOIN commentsinfo ON ELEMENT=commentsinfo.TYPE AND subscription.ID=commentsinfo.ID';

		$this->subs = memcached_rowuse_array(
			['subscription','topic','flocktopic','flock','commentsinfo'], $this->q = '
			SELECT	ELEMENT,subscription.ID,subscription.CSTAMP,LASTIDSEEN,LASTNOSEEN,'.$selects.'
			FROM subscription
			LEFT JOIN postedinv3 USING (ELEMENT,ID,USERID)'.
			$joins.'
			WHERE subscription.USERID='.$userid.$wheres.
			(!$element ? $this->controls->order_and_limit() : ' ORDER BY LSTAMP DESC'),
			0,
			'subslistv2:'.($element ?: $this->controls->obtain_page()).':'.CURRENTUSERID
		);
	}
	function display() {
		if (!$this->subs) {
			return;
		}
		number_reverse_sort($this->subs,'LSTAMP');
		$havesuper = $haveunseen = false;
		$showelem = !SMALL_SCREEN && !$this->element;
		foreach ($this->subs as $sub) {
			extract($sub);
			if ($FORUMID
			||	$FLOCKID
			) {
				$havesuper = true;
			}
			if ($LASTNOSEEN < $MSGCNT) {
				$haveunseen = true;
			}
			if ($havesuper && $haveunseen) break;
		}
		if ($this->controls) {
			$this->controls->display_and_store();
		}
		include_js('js/subscriptions');
		if ($haveunseen
		&&	($info = get_subscription_counts())
		&&	$info[1]
		) {
			?><input type="hidden" id="totalunseen" value="<?= $info[1] ?>" /><?
		}

		require_once '_ignores.inc';
		$ignores = ignores();

		?><table class="regular fw vttop hha forum" data-confirm="<?= __('subscription:confirm:removal_LINE') ?>"><?
#		layout_open_table(TABLE_REGULAR | TABLE_FULL_WIDTH | TABLE_VTTOP | TABLE_ROW_HILITE_ONMOUSEOVER);
		layout_start_header_row();
		if ($otherside = isset($_REQUEST['OTHERSIDE'])) {
			layout_header_cell_right(__C('field:last'));
			layout_header_cell_right(Eelement_name('date'));
		}
		if ($showelem) {
			?><th style="border-right:0"></th><?
		}
		?><th<?	if ($showelem) { ?> style="border-left:0"<? } ?>><?
		if ($haveunseen) {
			?><span style="float:right;font-weight:normal;margin-top:.1em" class="small light unhideanchor" onclick="<?
			?>if(confirm('<?= __('subscription:confirm:mark_all_seen_LINE') ?>'))catchupSubscriptions(this,'<?= $this->element ?>')"><?= __('action:mark_all_seen') ?></span><?
		}
		echo Eelement_name($this->element ? 'subject' : 'element') ?></th><?
		if (!SMALL_SCREEN) {
			layout_header_cell_right('#');
		}
		if (!$otherside) {
			if (!SMALL_SCREEN) {
				layout_header_cell_right(__C('field:last'));
			}
			layout_header_cell_right(Eelement_name('time'));
		}
		layout_stop_header_row();
		$messages_per_page = setting('MESSAGES_PER_TOPIC');
		$comments_per_page = setting('COMMENTS_PER_PAGE');
		$paged_from = setting('PAGED_COMMENTS_STARTING_FROM');
		static $uniq = 0;
		$bad = false;

		# find identical event names
		foreach ($this->subs as $sub) {
			if ($sub['ELEMENT'] !== 'party') {
				continue;
			}
			$party_name = memcached_party($sub['ID']);
			if (!$party_name) {
				mail_log('subs connected to non-existent party: '.$sub['ID']);
			} else {
				$same_event_names[mb_strtolower($party_name)][$sub['ID']] = $sub['ID'];
			}
		}

		require_once 'defines/topic.inc';

		foreach ($this->subs as $sub) {
			extract($sub);

			$this_paged_from = $paged_from;

			$topic = false;

			switch ($ELEMENT) {
			case 'flocktopic':	$topic = true; if ($showelem) $showelem = 'flock';	$pagesize = $messages_per_page; $msgnoffset = -1; $this_paged_from = 0; break;
			case 'topic':		$topic = true; if ($showelem) $showelem = 'forum';	$pagesize = $messages_per_page;	$msgnoffset = -1; $this_paged_from = 0; break;
			case 'albumelement':	if ($showelem) $showelem = 'album';	$pagesize = $comments_per_page; $msgnoffset = 0; break;
			default:		if ($showelem) $showelem = $ELEMENT;	$pagesize = $comments_per_page; $msgnoffset = 0; break;
			}

			switch ($ELEMENT) {
			case 'topic':
			case 'flocktopic':
				$table = str_replace('topic', 'message', $ELEMENT);
				$last_message = db_single_array($ELEMENT, '
					SELECT MESSAGEID, FLAGS
					FROM '.$table.'
					WHERE TOPICID = '.$ID.'
					ORDER BY MESSAGEID DESC
					LIMIT 1'
				);
				break;

			default:
				$table = $ELEMENT.'_comment';
				$last_message = db_single_array($table, '
					SELECT COMMENTID, FLAGS
					FROM '.$table.'
					WHERE ID = '.$ID.'
					ORDER BY COMMENTID DESC
					LIMIT 1'
				);
				break;
			}

			if (!$last_message) {
				$show_last = false;
			} else {
				[$commentid, $last_message_flags] = $last_message;

				require_once 'defines/post.inc';

				$message_is_anonymous = $last_message_flags & POST_IS_ANONYMOUS;

				require_once '_element_access.inc';

				$show_last =
					!$message_is_anonymous
				||	CURRENTUSERID === $LUSERID
				||	may_change_element($table, $commentid)
				?	$LUSERID
				:	0;
			}

			# old subs are not really 'unseen', we just don't really know
			# newer subs are always unseen.
			$unseen =
				($CSTAMP > 1304200800 || $LASTNOSEEN)
			&&	$LASTNOSEEN < $MSGCNT
			?	$MSGCNT - $LASTNOSEEN
			:	0;
			$hideunseen = $unseen ? 'hideUnseen(this,'.$uniq.','.(!$this->element || $this->always_show ? 'false' : 'true').')' : null;
			switch ($ELEMENT) {
			case 'deaduser':
			case 'user':
				$basehref =
				$pagehref = '/user/'.$ID.'/'.($ELEMENT == 'deaduser' ? 'condolences' : 'comments');

				if (setting('SHOW_USER_GUESTBOOK')) {
					$basehref = get_element_href($ELEMENT,$ID,$SUBJECT ?: false);
				}
				break;
			default:
				$basehref =
					$this_paged_from
				?	(	(require_once '_comment.inc')
					&&	setting('UNCOLLAPSE_COMMENTS') & (1<<constant('CMT_'.strtoupper($ELEMENT)))
					?	(	$ELEMENT == 'promo'
						?	'/'.$ELEMENT.'/'.$ID.'/letter'
						:	get_element_href($ELEMENT,$ID,$SUBJECT ?: false)
						)
					:	'/'.$ELEMENT.'/'.$ID.'/comments'
					)
				:	(	$ELEMENT == 'promo'
					?	'/'.$ELEMENT.'/'.$ID.'/letter'
					:	get_element_href($ELEMENT,$ID,$SUBJECT ?: false)
					);
				$pagehref =
					$this_paged_from
				?	'/'.$ELEMENT.'/'.$ID.'/comments'
				:	'/'.$ELEMENT.'/'.$ID;
				break;
			}
			$cls = topicrow_get_class($sub,$sub['MSGNO'] ? true : false);
			if ($lght
			=	$STATUS == 'hidden'
			||	$REMOVED
			) {
				$cls .= ' unavailable light';
			}
#			if (!$unseen ){
#				$cls .= ' seensub';
#			}

			?><tr<?
			if ($cls) {
				?> class="<?= $cls ?>"<?
			}
			?>><?
			if ($otherside) {
				if ($show_last) {
					?><td class="user-cell right small<?
					if ($message_is_anonymous) {
						?> light<?
					}
					?>"><?= get_element_link('user', $show_last) ?></td><?
				} else {
					?><td></td><?
				}
				?><td class="right" title="<?= _datedaytime_display($LSTAMP) ?>"><? show_short_diff($LSTAMP) ?></td><?
			}

			?><td<?
			if ($showelem) {
				?> class="right" style="border-right:0"<?
			}
			?>><?
			if ($showelem) {
				?><small><?= element_name($showelem) ?>:&nbsp;</small><?
				?></td><td style="border-left:0"><?
			}
			ob_start();
			?>Pf.remSub(this,'<?= $ELEMENT ?>',<?= $ID ?>,<?= $unseen || !$LASTIDSEEN ? 'true' : 'false' ?>)<?
			echo get_remove_char([
				'onclick'	=> ob_get_clean(),
				'class'		=> 'r'.($lght ? '' : ' light')
			]);

			$deleted = false;
			$title = get_element_title_or_log($ELEMENT, $ID, $deleted);
			if ($title === null) {
				# item might have been deleted without log
				$title = $ELEMENT.':'.$ID;
			} else {
				$title = isset(USE_UNICODE[$ELEMENT]) ? escape_utf8($title) : flat_with_entities($title, UBB_NO_LINKS | UBB_STATIC_SMILEYS);
			}

			ob_start();
			$unseenlink = false;

			$imgs = [
				'IMG'		=> [$LASTIDSEEN,$LASTNOSEEN,null]
			];
			if ($MESSAGEID) {
				$imgs[get_user_indicator_icon()] = [$MESSAGEID,$MSGNO,' class="small"'];
			}

			foreach ($imgs as $symbol => $info) {
				[$messageid,$msgno,$cls] = $info;
				if (!$messageid
				&&	$CSTAMP < 1304200800
				) {
					if ($cls || !$MSGCNT) {
						continue;
					}
					$cls = ' class="unseen light"';

				} elseif (
					# no messages:
					!$MSGCNT
					# read last msg:
				||	$msgno == $MSGCNT
				) {
					continue;
				}
				if (!$cls
				&&	!empty($ignores)
				) {
					switch ($ELEMENT) {
					case 'topic':		$itype = IGNORE_FORUM_MESSAGES; break;
					case 'flocktopic':	$itype = IGNORE_FLOCK_MESSAGES; break;
					default:		$itype = IGNORE_COMMENTS; break;
					}
					if (isset($ignores[$itype][$LUSERID])) {
						# FIXME: check if there is someone in between LASTIDSEEN en last ID of ignored user
						continue;
					}
				}
				?> <a<?
				if ($cls) {
					echo $cls;
				} elseif ($unseen) {
					?> class="unseen relative ib seemtext"<?
					?> title="<?= __('subscription:x_new_message(s)',array('CNT'=>$unseen)) ?>"<?
				}
				?> href="<?
				$lastpage = true;


				ob_start();
				if (!$messageid) {
					echo $basehref;
					if ($CSTAMP < 1304200800) {
						?>#bottom<?
					}
				} else {
					$msgno += $msgnoffset;
					if ($msgno <= $this_paged_from
					||	$msgno > $MSGCNT - $pagesize
					) {
						echo $basehref;
					} else {
						echo $pagehref ?>/page/<? echo ceil($msgno / $pagesize);
						$lastpage = false;
					}
					?>#m<? echo $messageid;
				}
				$link = ob_get_flush();
				?>"<?
				$onclick = [];
				if (!$cls) {
					$onclick[] = 'clickSub(this)';
				}
				if ($lastpage) {
					if ($hideunseen) {
						$onclick[] = $hideunseen;
					}
					if ($symbol == 'IMG') {
						?> id="unsa<?= $uniq ?>"<?
					}
				}
				if ($onclick) {
					?> onclick="<?= implode(';', $onclick) ?>"<?
					?> target="_blank"<?
				}
				?>><?
				if ($symbol === 'IMG') {
					?><img class="lower icon mxlh" src="<?= STATIC_HOST ?>/images/sun<?= ($messageid ? '_blue' : null),is_high_res() ?>.png" /><?
					?><div class="ib relative subcnt"><?= $unseen ?></div><?
				} else {
					echo $symbol;
				}
				?></a><?
				if (!$cls) {
					$unseenlink = $link;
				}
			}
			?> <a<?
			if ($hideunseen) {
				?> onclick="<?= $hideunseen ?>"<?
			}
			?> alt="<?= $alt = __('action:to_last_message') ?>"<?
			?> title="<?= $alt ?>"<?
			?> href="<?= $basehref ?>#bottom"><?= get_to_end_icon() ?></a><?
			$misc = ob_get_clean();

			if ($STATUS === 'closed') {
				?><img src="<?= STATIC_HOST; ?>/images/lock<?= is_high_res() ?>.png" class="icon lower" title="(<?= __('status:closed') ?>)" /> <?
			}

			if ($FLAGS
			&&	in_array($ELEMENT, [
					'topic',
					'flocktopic'
				], strict: true)
			) {
				require_once '_topictypes.inc';
				if ($prefix = get_topic_prefix($ELEMENT,$ID,$FLAGS)) {
					echo $prefix; ?>: <?
				}
			}

			?><a<?
			if ($hideunseen) {
				?> onclick="<?= $hideunseen ?>"<?
			}
			?> href="<?= $basehref,($topic ? null : '#page-controls') ?>"><?
			echo $title;
			?></a><?

			if ($ELEMENT === 'party'
			&&	isset($same_event_names[strtolower($title)])
			&&	($party = memcached_party_and_stamp($ID))
			) {
				?> <small class="light">@ <?
				change_timezone('UTC');
				_date_display($party['STAMP_TZI']);
				change_timezone();
				?></small><?
			}

			if ($deleted) {
				?> <i class="warning">(<?= __('elementstatus:removed') ?>)</i><?
			}

			echo $misc;

			if (!SMALL_SCREEN
			&&	($FORUMID || $FLOCKID)
			) {
				if (!($FLOCK_NAME ?: $FORUM_NAME)) {
					mail_log('no FLOCK_NAME nor FORUM_NAME', item: $this->q);
				}
				?> <small>(<?= get_element_link($FLOCKID ? 'flock' : 'forum', $FLOCKID ?: $FORUMID, $FLOCK_NAME ?: $FORUM_NAME ?: __('answer:none')) ?>)</small><?
			}
			if (!SMALL_SCREEN) {
				layout_next_cell(class: 'right msgcnt-cell');
				if ($unseen && $unseenlink) {
					?><span<?
					if ($hideunseen) {
						?> onclick="<?= $hideunseen ?>"<?
					}
					?> class="small light" id="unsb<?= $uniq ?>"><?
					?><a class="unseen" href="<?= $unseenlink ?>"><?= $unseen ?></a> / </span><?
				}
				echo $MSGCNT;
				?></td><?
			}
			if (!$otherside) {
				if ($show_last) {
					if (!SMALL_SCREEN) {
						?><td class="user-cell right small<?
						if ($message_is_anonymous) {
							?> light<?
						}
						?>"><?= get_element_link('user', $LUSERID) ?></td><?
					}
					?><td class="right date-cell" title="<?= _datedaytime_display($LSTAMP) ?>"><? show_short_diff($LSTAMP);
				} else {
					?><td></td><td></td><?
				}
			}
			layout_stop_row();
			++$uniq;
		}
		layout_close_table();
		if ($this->controls) {
			$this->controls->display_stored();
		}
	}
}
