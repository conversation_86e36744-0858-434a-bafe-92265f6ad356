<?php

define('CURRENTSTAMP',time());

require_once '_require.inc';
require_once '_error.inc';
require_once '_nocache.inc';
require_once '_require.inc';

send_no_cache_headers();

function bail(int $errno): never {
	if ($errno !== 200) {
		display_messages_only();
	}
	http_response_code($errno);
	exit;
}
if (!have_valid_origin()) {
	bail(403);
}
if (!require_something_trim($_POST, 'URL', null, null, false, want_ascii: true)) {
	bail(400);
}
if (!require_working_url_or_none($_POST, 'URL', utf8: true, http_code: $http_code = null)) {
	bail($http_code ?: 400);
}
bail(200);
