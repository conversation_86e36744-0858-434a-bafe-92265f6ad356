<?php

function deownerize_tickets(?int $userid = null): bool|null {
	if ($userid) {
		$useridstr = (string)$userid;
	} else {
		if (!($userids = db_simpler_array(['rights', 'rights_forum'], '
			SELECT DISTINCT USERID
			FROM rights
			UNION
			SELECT DISTINCT USERID
			FROM rights_forum
			WHERE TYPE = "admin"'
		))) {
			return $userids;
		}
		$useridstr = implode(',', $userids);
	}
	if (!db_lock_tables(['contact_ticket_log', 'contact_ticket'])) {
		return false;
	}
	$rc = __deownerize($useridstr, !isset($userid));
	db_unlock_tables();
	return $rc;
}

function __deownerize(string $useridstr, bool $exclusive = false): bool {
	return	db_insert('contact_ticket_log','
			INSERT INTO contact_ticket_log
			SELECT * FROM contact_ticket
			WHERE OWNERID'.($exclusive ? ' NOT IN (0, '.$useridstr.')' : ' IN ('.$useridstr.')'))
		&&	db_update('contact_ticket','
			UPDATE contact_ticket SET
				OWNERID	= 0,
				MSTAMP	= '.CURRENTSTAMP.',
				MUSERID	= 0
			WHERE OWNERID'.($exclusive ? ' NOT IN (0, '.$useridstr.')' : ' IN ('.$useridstr.')'));
}
