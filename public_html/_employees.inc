<?php

const EMPLOYER_ELEMENTS = [
	'artist',
	'location',
	'organization',
	'stream',
];

function remove_all_employees(?string $element = null, ?int $id = null, bool $log = true): bool {
	$table = ($element ?: $_REQUEST['sELEMENT']).'member';
	$id ??= $_REQUEST['sID'];

	if ($userids = db_simpler_array($table, 'SELECT USERID FROM '.$table.' WHERE ID = '.$id)) {
		foreach ($userids as $userid) {
			flush_employee($userid);
		}
	}

	return	(	!$log
			||	db_insert("{$table}_log", "
				INSERT INTO `{$table}_log`
				SELECT *, ".CURRENTSTAMP.', '.CURRENTUSERID." FROM `$table`
				WHERE ID = $id")
			)
		&&	db_delete($table, "
			DELETE FROM `$table`
			WHERE ID = $id");
}

function remove_employee(int $userid, ?string $element = null, ?int $id = null): bool {
	$element ??= $_REQUEST['sELEMENT'];
	$id		 ??= $_REQUEST['sID'];
	$table = ($element ?: $_REQUEST['sELEMENT']).'member';

	if (!db_insert("{$table}_log", "
		INSERT INTO `{$table}_log`
		SELECT *, ".CURRENTSTAMP.', '.CURRENTUSERID." FROM `$table`
		WHERE ID = $id
		  AND USERID = $userid")
	||	!db_delete($table, "
		DELETE FROM `$table`
		WHERE ID = $id
		  AND USERID = $userid")
	) {
		return false;
	}
	register_notice('employee:notice:removed_LINE',DO_UBB,['USERID'=>$userid]);
	return true;
}

function activate_employee(int $userid, bool $active, ?string $element = null, ?int $id = null): bool {
	$element ??= $_REQUEST['sELEMENT'];
	$id		 ??= $_REQUEST['sID'];
	$table = $element.'member';

	if (!db_insert($table.'_log','
		INSERT INTO '.$table.'_log
		SELECT *, '.CURRENTSTAMP.', '.CURRENTUSERID.'
		FROM '.$table.'
		WHERE ACTIVE != '.($active ? '1' : '0').'
		  AND ID = '.$id.'
		  AND USERID = '.$userid)
	||	!db_insert($table,'
		UPDATE '.$table.' SET
			MUSERID		='.CURRENTUSERID.',
			MSTAMP		='.CURRENTSTAMP.',
			ACTIVE		='.($active ? '1' : '0').'
		WHERE ACTIVE != '.($active ? '1' : '0').'
		  AND ID = '.$id.'
		  AND USERID = '.$userid)
	) {
		return false;
	}
	flush_employee($userid);
	register_notice($active ? 'employee:notice:activated_LINE' : 'employee:notice:deactivated_LINE', DO_UBB, ['USERID' => $userid]);
	return true;
}

function flush_employee(int $userid): void {
	require_once '_contactstars.inc';
	memcached_delete(ALL_EMPLOYEES_PREFIX.$userid);
}
function update_employee(string $element, int $id, array $info, ?int $userid = null): bool {
	$userid ??= CURRENTUSERID;

	$table = $element.'member';
	if (!db_insert($table.'_log','
		INSERT INTO '.$table.'_log
		SELECT *,'.CURRENTSTAMP.','.$userid.' FROM '.$table.'
		WHERE ID='.$id.'
		  AND USERID='.$userid.'
		  AND VISIBILITY_NAME!='.$info['VISIBILITY_NAME'])
	||	!db_insert($table,'
		UPDATE '.$table.' SET
			MUSERID		='.$userid.',
			MSTAMP		='.CURRENTSTAMP.',
			VISIBILITY_NAME	='.$info['VISIBILITY_NAME'].'
		WHERE ID='.$id.'
		  AND USERID='.$userid.'
		  AND VISIBILITY_NAME!='.$info['VISIBILITY_NAME'])
	) {
		return false;
	}
	flush_employee($userid);
	register_notice(db_affected() === 2 ? 'employee:notice:updated_LINE' : 'employee:notice:added_LINE');
	return true;
}

function commit_employee(int $userid, array $info, ?string $element = null, ?int $id = null): bool {
	$element ??= $_REQUEST['sELEMENT'];
	$id		 ??= $_REQUEST['sID'];
	$table = $element.'member';

	if (!db_insert($table.'_log', '
		INSERT INTO '.$table.'_log
		SELECT *, '.CURRENTSTAMP.', '.CURRENTUSERID.'
		FROM '.$table.'
		WHERE ID = '.$id.'
		  AND USERID = '.$userid)
	||	!db_insert($table, '
		INSERT INTO '.$table.' SET
			CUSERID		='.CURRENTUSERID.',
			CSTAMP		='.CURRENTSTAMP.',
			ID		='.$id.',
			ACTIVE		='.(isset($info['ACTIVE']) ? '1' : '0').',
			USERID		='.$userid.',
			VISIBILITY_NAME	='.$info['VISIBILITY_NAME'].'
		ON DUPLICATE KEY UPDATE
			MUSERID		=VALUES(CUSERID),
			MSTAMP		=VALUES(CSTAMP),
			ACTIVE		=VALUES(ACTIVE),
			VISIBILITY_NAME	=VALUES(VISIBILITY_NAME)'
		)
	) {
		return false;
	}
	flush_employee($userid);
	register_notice(db_affected() === 2 ? 'employee:notice:changed_LINE' : 'employee:notice:added_LINE',DO_UBB,['USERID'=>$userid]);
	return true;
}

function employee_action(): ?bool {
	if (!$_REQUEST['sID']
	||	!have_admin($_REQUEST['sELEMENT'])
	) {
		return true;
	}
	if ($_REQUEST['SUBACTION'] === 'remove') {
		return	require_idnumber($_REQUEST, 'subID')
			&&	remove_employee($_REQUEST['subID']);
	}
	if (!empty($_REQUEST['NICK'])) {
		$nick = trim($_REQUEST['NICK']);
		$_REQUEST['NICK'] = $nick;
		if (!($options = db_rowuse_array(['user_account', $_REQUEST['sELEMENT'].'member','user'], "
			SELECT	USERID, NICK, STATUS, (SELECT EMAIL FROM user WHERE user.USERID=user_account.USERID) AS EMAIL,
					USERID IN (SELECT USERID FROM {$_REQUEST['sELEMENT']}member WHERE ID = {$_REQUEST['sID']}) AS ALREADY
			FROM user_account
			WHERE NICK LIKE '%".addslashes(trim($nick))."%'".
			(is_number($nick) ? ' OR USERID = '.$nick : '').'
			ORDER BY NICK')
		)) {
			if ($options !== false) {
				register_error('employees:error:nick_not_found_LINE', DO_UBB, ['NICK' => $nick]);
			}
			return false;
		}
		if (($option_count = count($options)) > 1) {
			$use_options = [];
			$lower_nick = strtolower($nick);
			$skipped = 0;
			foreach ($options as $option) {
				if (is_number($nick)
				&&	$nick === $option['USERID']
				||	0 === strcasecmp($nick,$option['NICK'])
				) {
					$use_options[] = $option;
				} else {
					similar_text($lower_nick, strtolower($option['NICK']), $pct);
					if ($pct < 80) {
						++$skipped;
					} else {
						$use_options[] = $option;
					}
				}
			}
			if ($skipped === $option_count) {
				register_error('employees:error:all_too_different_LINE', ['CNT' => $option_count]);
				return false;
			}
			register_warning('employees:warning:multiple_choice_LINE', ['CNT' => $option_count, 'SKIPPED' => $skipped]);
			global $__options;
			$__options = $use_options;
			return false;
		}
		$_REQUEST['subID'] = $options[0]['USERID'];
		return true;
	}
	if (!$_REQUEST['subID']) {
		return null;
	}
	if ($_REQUEST['SUBACTION'] !== 'commit') {
		return true;
	}
	require_once '_visibility.inc';
	return	require_visibility($_POST, 'NAME', NOBODY)
		&&	commit_employee($_REQUEST['subID'], $_POST);
}

function display_employee_form(): void {
	if (!require_admin($element = $_REQUEST['sELEMENT'])
	||	!($userid = require_idnumber($_REQUEST, 'subID'))
	||	!($id = require_idnumber($_REQUEST, 'sID'))
	) {
 		return;
	}
	require_once '_visibility.inc';
	layout_section_header(
		__C('employees:form:pageheader', [
			'ELEMENTNAME'	=> element_name($element),
			'ELEMENTSNAME'	=> element_plural_name($element)
		])
	);

	if (false === ($employee = db_single_assoc([$element.'member', 'user'], "
		SELECT m.*, 1 AS MEMBEXISTS, user.NICK, user.EMAIL, user.REALNAME
		FROM {$element}member AS m
		JOIN user USING (USERID)
		WHERE m.ID = $id
		  AND m.USERID = $userid",
		DB_USE_MASTER
	))) {
		return;
	}
	if (!$employee
	&&	!($user = db_single_assoc('user','SELECT NICK, REALNAME, EMAIL FROM user WHERE USERID = '.$userid))
	) {
		if ($user !== false) {
			register_error('user:error:nonexistent_LINE', ['ID' => $userid]);
		}
		return;
	}
	if ($_REQUEST['SUBACTION'] === 'commit') {
		/** @noinspection PhpForeachOverSingleElementArrayLiteralInspection */
		# Visibility, but of not set, don't change
		foreach (['VISIBILITY_NAME'] as $key) {
			if (false === require_number($_POST, $key)) {
				return;
			}
			if (isset($_POST[$key])) {
				$employee[$key] = $_POST[$key];
			}
		}
		/** @noinspection PhpForeachOverSingleElementArrayLiteralInspection */
		# Checkboxes:
		foreach (['ACTIVE'] as $key) {
			$employee[$key] = isset($_POST[$key]);
		}
		if (!($nick = memcached_nick($userid))) {
			register_error('user:error:nonexistent_LINE', ['ID' => $userid]);
			return;
		}
	} elseif (
		!empty($employee['NICK'])
	&&	have_post()
	) {
		register_warning('employee:warning:already_exists_LINE', DO_UBB, ['USERID' => $userid]);
	}
	?><form<?
	?> onsubmit="return submitForm(this)"<?
	?> method="post" action="/<?= $element ?>/<?= $id ?>/employees/<?= $userid ?>/commit"><?

	layout_open_box($element);
	layout_open_table('fw');

	// ELEMENT
	layout_start_row();
	echo Eelement_name($element);
	layout_field_value();
	echo get_element_link($element, $id);

	// USER
	layout_restart_row();
	echo Eelement_name('employee');
	layout_field_value();
	$nick = isset($user) ? $user['NICK'] : ($nick ?? memcached_nick($employee['USERID']));
	echo get_element_link('user', $userid, $nick);

	// SITE
	if ($site = db_single($element, 'SELECT SITE FROM '.$element.' WHERE '.$element.'ID = '.$id)) {
		layout_restart_row();
		echo Eelement_name('site');
		layout_field_value();
		echo escape_specials($site);
	}

	// VISIBILITY
	layout_restart_row();
	echo Eelement_name('visibility');
	layout_field_value();
	_visibility_display_form_part($employee ?: (isset($_REQUEST['INVISI']) ? SELF : EVERYBODY),'NAME',null,null,false,EVERYBODY,NOBODY);

	$info = $employee ?: $user;

	if (!empty($info['REALNAME'])) {
		// REALNAME
		layout_restart_row();
		echo Eelement_name('name');
		layout_field_value();
		echo escape_specials($info['REALNAME']);
	}
	// EMAIL
	layout_restart_row();
	echo Eelement_name('email');
	layout_field_value();
	if (!isset($info['EMAIL'])) {
		mail_log('no EMAIL in employee/user', item: $info);
	}
	echo escape_specials(substr($info['EMAIL'], strpos($info['EMAIL'], '@')));

	layout_restart_row();
	?><label for="active"><?= __C('status:active') ?></label><?
	layout_field_value();
	?><input type="checkbox" name="ACTIVE" value="1"<?
	if (!isset($employee) || $employee['ACTIVE']) {
		?> checked<?
	}
	?> id="active"><?

	layout_stop_row();
	layout_close_table();
	layout_close_box();
	?><div class="block"><?
	?><input type="submit" value="<?= __(!empty($employee['MEMBEXISTS']) ? 'action:change' : 'action:add') ?>" /><?
	?></div><?
	?></form><?
}

function display_employees(): void {
	if (!require_admin($element = $_REQUEST['sELEMENT'])
	||	!($id = require_idnumber($_REQUEST, 'sID'))
	||	false === ($employees = db_rowuse_array([$element.'member', 'user_account', 'user'], "
		SELECT employee.*, STATUS, user.EMAIL, user.REALNAME
		FROM {$element}member AS employee
		JOIN user_account USING (USERID)
		JOIN user USING (USERID)
		WHERE ID = $id
		ORDER BY user_account.NICK",
		DB_USE_MASTER
	))) {
		return;
	}
	layout_section_header(__C('employees:overview:pageheader', ['ELEMENTNAME' => element_name($element), 'ELEMENTSNAME' => element_plural_name($element)]));
	?><div class="block"><?= get_element_link($element,$id) ?></div><?
	layout_open_box($element);
	layout_box_header(__C('employees:header',DO_UBB, ['ELEMENT' => $element, 'ID' => $id]));
	if ($employees) {
		show_employee_table($employees);
	} else {
		?><div class="block"><?= __('employees:no_employees_LINE') ?></div><?
	}
	$site_domain =
		($site = db_single($element, "SELECT SITE FROM `$element` WHERE {$element}ID = $id"))
	&&	preg_match('"^https?://(?:www\.)?([^/]+)/?"', $site, $match)
	?	$match[1]
	:	null;
	layout_close_box();

	?><form<?
	?> onsubmit="return submitForm(this)"<?
	?> method="post"<?
	?> action="/<?= $element ?>/<?= $id ?>/employees"><?
	?><div class="block"><?= Eelement_name('employee') ?> <?
	global $__options;
	if (isset($__options)) {
		require_once '_namedoubles.inc';
		generate_name_doubles($__options, 'NICK', 'user');
		?><select name="subID"><?
		$nick = $_REQUEST['NICK'];
		$number_nick = is_number($nick) ? $nick : 0;

		if ($site_domain) {
			foreach ($__options as $option) {
				if (!strcasecmp($site_domain,  substr($option['EMAIL'], strpos($option['EMAIL'], '@') + 1))) {
					$from_domain[] = $option;
				} else {
					$new_options[] = $option;
				}
			}
			if (isset($from_domain)) {
				$options['@'.$site_domain] = $from_domain;
			}
			if (isset($new_options)) {
				$options[0] = $new_options;
			}
		}
		global $__options;
		if (empty($options)) {
			$options = [$__options];
		}
		foreach ($options as $index => $option_list) {
			if ($index) {
				?><optgroup label="<?= escape_specials($index) ?>"><?
			}
			foreach ($option_list as $option) {
				?><option<?
				if (!isset($selected)
				&&	(	$number_nick
					&&	$number_nick === $option['USERID']
					||	0 === strcasecmp($option['NICK'], $nick)
					)
				) {
					$selected = true;
					?> selected<?
				}
				?> value="<?= $option['USERID'] ?>"><?
				echo escape_specials($option['NICK']);
				$extra = [];
				if (isset($option['DOUBLE'])
				&&	$option['STATUS'] !== 'active'
				&&	(require_once '_status.inc')
				) {
					$extra[] = status_name($option['STATUS']);
				}
				if (!empty($option['ALREADY'])) {
					$extra[] = __('field:already_employee');
				}
				if ($extra) {
					?> (<?= implode(', ', $extra) ?>)<?
				}
				?></option><?
			}
			if ($index) {
				?></optgroup><?
			}
		}
		?></select> <?
		$submit_action = __('action:add');
	} else {
		show_input([
			'type'		=> 'search',
			'id'		=> 'nick',
			'name'		=> 'NICK',
			'autofocus' => 'autofocus',
			'class'		=> 'short',
		]);
		$submit_action = __('action:search');
	}
	show_input([
		'type'		=> 'submit',
		'value'		=> $submit_action,
	]);
	?></div></form><?
}

function show_employees(): void {
	$element = $_REQUEST['sELEMENT'];
	$id		 = $_REQUEST['sID'];
	$table = $element.'member';
	if (!($employees = memcached_rowuse_hash_if_not_admin(
		$element,
		[$table, 'user_account'], "
		SELECT USERID, NICK, STATUS, VISIBILITY_NAME, ACTIVE
		FROM `$table`
		JOIN user_account USING (USERID)
		WHERE ID = $id
		ORDER BY STATUS = 'active' DESC, ACTIVE DESC, NICK"
	))) {
		return;
	}
	require_once '_visibility.inc';
	if ($force_show = $is_admin = have_admin($element)) {
		$counts = [];
		foreach ($employees as $employee) {
			increment_or_set($counts, $employee['ACTIVE'] && $employee['STATUS'] === 'active' ? 1 : 0);
		}
		$hide_self = false;
	} else {
		$force_show = isset($employees[CURRENTUSERID]) && $employees[CURRENTUSERID]['VISIBILITY_NAME'] !== NOBODY;
		$hide_self  = isset($employees[CURRENTUSERID]) && $employees[CURRENTUSERID]['VISIBILITY_NAME'] === NOBODY;
	}
	if ($force_show) {
		require_once '_status.inc';
	}
	$artist = $element === 'artist';
	$current = null;
	foreach ($employees as $employee) {
		/** @noinspection PhpRedundantOptionalArgumentInspection */
		extract($employee, \EXTR_OVERWRITE);
		$mainvisi = _visibility($employee, 'NAME', $is_admin);
		if (!$force_show
		&&	!$mainvisi
		||	!$is_admin
		&&	(	$STATUS !== 'active'
			||	!$ACTIVE
			)
		||	$USERID === CURRENTUSERID
		&&	$hide_self
		) {
			continue;
		}
		$active = $STATUS === 'active' && $ACTIVE;
		if (!isset($opened)) {
			?><div class="block"><b><?= Eelement_plural_name($artist ? 'connected_user' : 'employee') ?></b><?
			if ($artist) {
				?> (<?= __('visible_to:admins') ?>)<?
			}
		}
		if ($current !== $active) {
			if (isset($opened)) {
				?></ul><?
			}
			if ($active) {
				?><ul><?
			} else {
				?><div><?
				?><span<?
				?> class="unhideanchor light block"<?
				?> onclick="swapdisplay('non_active_empl'); changerow(this, 'toggleclass', 'light');"<?
				?>>&darr; <?=
					$counts[0] ?> <?=  __('status:non_active')
				?></span><?
				?></div><?
				?><ul class="hidden" id="non_active_empl"><?
			}
			$opened = true;
		}
		$current = $active;
		?><li<?
		$classes = [];
		if ($mainvisi !== ROW_FULL) {
			$classes[] = 'light';
		}
		if (!$ACTIVE) {
			$classes[] = 'unavailable';
		}
		if ($classes) {
			?> class="<?= implode(' ',$classes) ?>"<?
		}
		?>><?

		$link = get_element_link('user', $USERID, $NICK);
		echo	!$link
		?	escape_specials($NICK)
		:	(	$_REQUEST['sELEMENT'] === 'artist'
			?	$link
			:	str_replace('<a','<a itemprop="employee"',$link)
			);

		if ($force_show
		&&	$STATUS !== 'active'
		) {
			?> (<? show_status_name($STATUS) ?>)<?
		}
		?></li><?
	}
	if (isset($opened)) {
		?></ul><?
		?></div><?
	}
}

function show_employee_table(array $employees): void {
	$element = $_REQUEST['sELEMENT'];
	$id		 = $_REQUEST['sID'];

	require_once '_visibility.inc';
	require_once '_status.inc';

	layout_open_table(TABLE_FULL_WIDTH);
	layout_start_header_row();
	layout_header_cell(Eelement_name('user'));
	layout_header_cell(Eelement_name('status'));
	layout_header_cell(Eelement_name('realname'));
	layout_header_cell(Eelement_name('email'));
	layout_header_cell();
	layout_stop_header_row();

	foreach ($employees as $employee) {
		/** @noinspection PhpRedundantOptionalArgumentInspection */
		extract($employee, \EXTR_OVERWRITE);
		// NICK
		layout_start_rrow($visi = _visibility($employee,'NAME'),$ACTIVE ? null : 'unavailable');
		echo get_element_link('user',$USERID);

		layout_next_cell();
		show_status_name($STATUS);

		// REALNAME
		layout_next_cell(class: !$visi ? 'light' : '');
		if ($REALNAME) {
			echo escape_specials($REALNAME);
		}
		// EMAIL
		layout_next_cell(class: !$visi ? 'light' : '');
		if ($EMAIL) {
			echo escape_specials(substr($employee['EMAIL'] ,strpos($employee['EMAIL'],'@')));
		}
		layout_next_cell();

		// CHANGE | REMOVE, don't show for Partyflock, done automatically
		layout_next_cell(class: 'right nowrap');
		$href = "/$element/$id/employees/$USERID";
		?><a href="<?= $href ?>/form"><?= get_special_char('change') ?></a><?
		?> <?
		with_confirm(
			get_special_char('remove'),
			$href.'/remove',
			str_replace("\r\n",'\\n',__('employee:confirm:removal_TEXT',IS_JAVASCRIPT))
		);
		layout_stop_row();
	}
	layout_close_table();
}
