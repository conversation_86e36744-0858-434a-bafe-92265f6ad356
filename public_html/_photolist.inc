<?php

// FIXME: link artist/xxx/photos/PARTYID/yy should be artist/xxx/photos/GALLERYID/zzz

require_once '_commentsinfo.inc';
require_once '_pagecontrols.inc';
require_once '_settings.inc';

class _photolist {
	// in:
	public int|string|null	$page		= null;

	public bool	$hide_cmts	= false;
	public bool	$popularity	= false;

	private ?array	$photolist;
	private array	$stats;
	private array	$commentsinfos;
	private ?int	$partyid;
	private ?int	$galleryid;
	private ?int	$userid;
	private ?int	$artistid;
	private ?array	$party;

	private array	$wheres		= [];
	private array	$joins 		= [];
	private array	$tables		= ['image'];
	private ?string	$buddies;
	private ?string	$orders;
	private bool	$hidden		= false;
	private string	$loading	= 'lazy';

	public function __construct() {
		if (empty($_REQUEST['PAGE'])) {
			$this->page = 1;
			return;
		}
		if ($_REQUEST['PAGE'] === 'all') {
			$this->page = 'all';
			return;
		}
		if (!require_idnumber($_REQUEST,'PAGE')) {
			$this->page = 1;
			return;
		}
		$this->page = $_REQUEST['PAGE'];
	}

	final public function loading(string $loading): void {
		$this->loading = $loading;
	}

	final public function query(): bool {
		if (isset($this->show_all)) {
			$this->page = 'all';
		}
		if ($this->galleryid) {
			$this->gallery = memcached_gallery($this->galleryid);
		}
		if ($this->hidden) {
			$this->wheres[] = 'image.HIDDEN=1';
		}
		$type = 'THUMB';
		if (!($this->photolist = memcached_rowuse_hash($this->tables, /** @lang MariaDB */ '
			SELECT '.($this->buddies ? 'DISTINCT ' : '')."
					image.IMGID, image.USERID, image.PARTYID, image.HIDDEN, image.GALLERYID,
					{$type}_WIDTH AS WIDTH,
					{$type}_HEIGHT AS HEIGHT,
					GREATEST(ORIGINAL_WIDTH,  DOUBLE_WIDTH,  WIDTH)  AS WIDEST,
					GREATEST(ORIGINAL_HEIGHT, DOUBLE_HEIGHT, HEIGHT) AS HIGHEST
			FROM image ".
			($this->joins ? implode(' ',$this->joins) : '').
			($this->wheres ? ' WHERE '.implode(' AND ',$this->wheres) : '').
			(	isset($this->specialorderstr)
			?	' ORDER BY FIELD(IMGID, '.$this->specialorderstr.')'
			:	' ORDER BY '.($this->orders ?: '').'image.IMGID')))
		) {
			return (bool)$this->photolist;
		}
		if (!empty($this->popularity)
		&&	($local_stats = memcached_simple_hash('image_counter_total','
			SELECT IMGID, COUNTER
			FROM image_counter_total
			WHERE IMGID IN ('.implodekeys(', ', $this->photolist).')'))
		) {
			$this->stats = $local_stats;
			uksort($this->photolist,static fn(int $imgid_a, int $imgid_b): int =>
					(($local_stats[$imgid_b] ?? 0) <=> ($local_stats[$imgid_a] ?? 0))
				?:	($imgid_a <=> $imgid_b)
			);
		}
		$this->commentsinfos = get_commentsinfos('photo', $this->photolist);
		return true;
	}

	/*function of_user_on_party(int $userid, int $partyid): void {
		$this->partyid	= $partyid;
		$this->userid	= $userid;
		$this->wheres[] = 'image.PARTYID='.$partyid;
		$this->joins[]  = 'JOIN user_appearance USING (IMGID)';
		$this->wheres[] = "user_appearance.USERID = $userid";
		$this->tables[] = 'user_appearance';
	}

	function of_artist_on_party(int $artistid, int $partyid): void {
		$this->partyid	= $partyid;
		$this->artistid	= $artistid;
		$this->wheres[] = "image.PARTYID = $partyid";
		$this->joins[]  = 'JOIN artist_appearance USING (IMGID)';
		$this->joins[]  = 'JOIN artist USING (ARTISTID)';
		$this->wheres[] = "artist_appearance.ARTISTID = $artistid";
		require_once '_visibility.inc';
		$this->wheres[] = _where_visible('artist','APPEARANCES', 0, false);
		$this->tables[] = 'artist_appearance';
	}*/
	/*final function of_party(int $partyid) {
		$this->partyid	= $partyid;
		$this->wheres[] = 'image.PARTYID='.$partyid;
	}*/
	final function of_gallery(int $galleryid): void {
		$this->galleryid = $galleryid;
		$this->wheres[] = 'image.GALLERYID='.$galleryid;
	}
	final public function of_user(int $userid): void {
		$this->userid	= $userid;
		$this->wheres[] = 'user_appearance.USERID='.$userid;
		$this->joins[]	= 'JOIN user_appearance USING (IMGID)';
		$this->joins[]  = 'JOIN party ON party.PARTYID=image.PARTYID';
		$this->orders	= 'party.STAMP DESC,';
		$this->tables[] = 'user_appearance';
		$this->tables[] = 'party';
	}
	final public function of_artist(int $artistid): void {
		$this->artistid	= $artistid;
		$this->wheres[] = 'artist_appearance.ARTISTID='.$artistid;
		require_once '_visibility.inc';
		$this->wheres[] = _where_visible('artist','APPEARANCES',0,false);
		$this->joins[]	= 'JOIN artist_appearance USING (IMGID)';
		$this->joins[]  = 'JOIN artist USING (ARTISTID)';
		$this->joins[]  = 'JOIN party ON party.PARTYID=image.PARTYID';
		$this->orders	= 'party.STAMP DESC,';
		$this->tables[] = 'artist_appearance';
		$this->tables[] = 'party';
	}

	private function display_all(): void {
		$this->page = null;
		$work_list = [];
		foreach ($this->photolist as $photo_id => $photo) {
			$work_list[$photo['PARTYID']][$photo_id] = $photo;
		}
		$backup_list = $this->photolist;
		foreach ($work_list as $local_photo_list) {
			$this->photolist = $local_photo_list;
			$this->display();
		}
		$this->photolist = $backup_list;
	}

	final public function display(): void {
		if (!$this->photolist) {
			return;
		}
		if ($this->page === 'all'
		&&	!$this->partyid
		&&	(	$this->userid
			||	$this->artistid)
		) {
			$this->display_all();
			return;
		}
		if (!$this->open_box()) {
			return;
		}
		if (!SMALL_SCREEN
		&&	$_REQUEST['sELEMENT'] === 'gallery'
		) {
			require_once '_slideshow.inc';
			show_slideshow('photo', $this->photolist, $this->commentsinfos);
		}
		$heights = [];
		foreach ($this->photolist as $photo) {
			if ($photo['WIDTH'] > $photo['HEIGHT']) {
				$heights[] = $photo['HEIGHT'];
			}
		}
		sort($heights);
		$median = $heights[count($heights) >> 1] ?? $photo['HEIGHT'];

		if (($use_height = have_idnumber($_REQUEST, 'HEIGHT'))
		&&	$use_height >= 50
		&&	$use_height <= 300
		) {
			$median = $use_height;
		}
		include_js('js/photoload');

		?><div class="<?
		if (!isset($this->align_left)) {
			?>center <?
		}
		?>block rndbrd lz"><?

		$photo_name = element_name('photo');

		# force minimum height of 200px
		if ($median < 200) {
			$median = 200;
		}
		$photo_count = 0;
		foreach ($this->photolist as $photo) {
			/** @noinspection PhpRedundantOptionalArgumentInspection */
			extract($photo, \EXTR_OVERWRITE);

			if ($HIDDEN
			&&	CURRENTUSERID !== $USERID
			&&	!$GLOBALS['currentuser']->is_admin('photo')
			) {
				// HIDDEN AND NO RIGHTS
				continue;
			}

			$src_set = [];
			$x2_src = null;
			$x3_src = null;
			$x1_src = null;
			$x2_src = null;
			$x3_src = null;
			if ($HEIGHT !== $median) {
				if (!SMALL_SCREEN) {
					anyscale($WIDTH, $HEIGHT, 0, $median);
				}
				$size_type_x1 = $WIDTH.'x'.$HEIGHT;
				$x1_src = get_photo_url($IMGID, $size_type_x1);
				$src_set[] = $x1_src.' 1x';
				if ($WIDEST > $WIDTH) {
					$size_type_x2 = (2 * $WIDTH).'x'.(2 * $HEIGHT);
					$x2_src = get_photo_url($IMGID, $size_type_x2);
					$src_set[] = $x2_src.' 2x';
					if ($WIDEST > 2 * $WIDTH) {
						$size_type_x3 = (3 * $WIDTH).'x'.(3 * $HEIGHT);
						$x3_src = get_photo_url($IMGID, $size_type_x3);
						$src_set[] = $x3_src.' 3x';
					}
				}
			} else {
				$size_type = 'thumb';
				$x1_src = get_photo_url($IMGID, $size_type);
			}
			if (SMALL_SCREEN
			&&	(require_once '_resolution.inc')
			&&	($res = get_resolution())
			) {
				[, $w] = $res;
				if ($WIDTH > $w - 30) {
					 # fix width for very wide images on mobile
					scale($WIDTH, $HEIGHT, $w - 30);
				}
			}
			?><div<?
			?> class="<?
			if ($HIDDEN) {
				?>nok <?
			}
			?>photo-box block"<?
			?> style="max-width: <?= $WIDTH + 4 ?>px;"><?
			if (!empty($this->popularity)) {
				if (!empty($this->stats[$IMGID])) {
					show_comment_counter(
						$this->stats[$IMGID],
						title: element_plural_name('view')
					);
				}
			} elseif (
				!ROBOT
			&&	!$this->hide_cmts
			&&	!empty($this->commentsinfos[$IMGID])
			&&	($cnt = $this->commentsinfos[$IMGID][CMTI_OKMSGCNT])
			&&	!$this->commentsinfos[$IMGID][CMTI_HIDEALL]
			) {
				show_comment_counter(
					$cnt,
					link_to_comments('photo', $IMGID),
					element_plural_name('comment')
				);
			}
			?><a<?
			?> id="ph<?= $IMGID ?>"<?
			?> href="<?
			if ($this->userid || $this->artistid) {
				?>/photo/<? echo $IMGID;
				if ($this->userid) {
					?>/user/<? echo $this->userid;
				} else {
					?>/artist/<? echo $this->artistid;
				}
			} else {
				echo get_element_href('photo',$IMGID);
			}
			?>"><?

			# ob_start();

			?><img<?
			?> loading="<?= $this->loading ?>"<?
			if (!ROBOT) {
				?> onload="Pf_photoLoad('photo_load_thumbnail', <?= $IMGID ?>)"<?
			}
			if (aspect_objects()) {
				?> width="<?= $WIDTH ?>" <?
				?> height="<?= $HEIGHT ?>" <?
				?> style="max-width: 100%; width: <?= $WIDTH ?>px; height: auto;" <?
			} else {
				?> style="width: <?= $WIDTH ?>px; height: <?= $HEIGHT ?>px;"<?
			}
			?> src="<?= match(get_rounded_dpr()) {
				default	=> $x1_src,
				1		=> $x1_src,
				2		=> $x2_src ?? $x1_src,
				3		=> $x3_src ?? $x2_src ?? $x1_src,
			} ?>"<?
			if ($src_set) {
				?> srcset="<?= implode(', ', $src_set) ?>"<?
			}
			?> alt="<?
			echo $photo_name, ' ';
			$party = $this->party;
			if ($party['PARTYID']) {
				echo escape_utf8($party['NAME']);
				if ($party['TITLE']) {
					?> <?
					echo escape_specials($party['TITLE']);
				}
				[$y, $m, $d] = _getdate($party['STAMP_TZI'] - $party['AT2400'], 'UTC');
				echo	', ', $d, ' ', _month_name($m), ' ', $y,
						(empty($party['LOCATION_NAME'])	? '' : ', '.escape_utf8($party['LOCATION_NAME'])),
						(empty($party['CITY_NAME'])		? '' : ', '.escape_utf8($party['CITY_NAME']));

			} elseif ($party['TITLE']) {
				echo escape_specials($party['TITLE']);
			}
			?> #<?= $IMGID ?>" /><?

			# show_lazy_fixed(ob_get_clean(), $WIDTH, $HEIGHT);

			?></a><?
			?></div><?

			?><div class="ib block-sep"> </div><?

			if (!(++$photo_count % 10)) {
				show_ad_placeholder();
			}
		}
		?></div><?
		layout_close_box();
	}

	function made_by(int $userid): void {
		$this->wheres[] = 'image.USERID='.$userid;
	}

	function only_hidden(): void {
		$this->hidden = true;
	}

	function only_buddies_and_self(): void {
		require_once '_buddies.inc';
		require_once '_buddy_or_self_appearances.inc';
		if (!$this->galleryid) {
			mail_log('_photolist::only_buddies_and_self() called without galleryid', error_log: 'ERROR');
		}
		if (false === ($buddy_hash = _buddies_full_hash(CURRENTUSERID))
		||	false === ($visible = get_buddy_or_self_appearances($this->galleryid,$buddy_hash))
		) {
			$this->wheres[] = '0';
			return;
		}
		$visible[CURRENTUSERID] = CURRENTUSERID;
		$this->buddies = implodekeys(',',$visible);
		$this->joins[] = 'LEFT JOIN user_appearance AS ua ON ua.IMGID=image.IMGID';
		$this->joins[] = 'LEFT JOIN onphoto ON onphoto.IMGID=image.IMGID';
		$this->wheres[] = '(ua.USERID IN ('.$this->buddies.') OR onphoto.USERID IN ('.$this->buddies.'))';
		$this->tables[] = 'user_appearance';
		$this->tables[] = 'onphoto';

		# NOTE: below was run when galleryid was not set, but contained breaking code
		/*	require_once '_visibility.inc';
			$buddylist = _buddies_full_cached_list(CURRENTUSERID);
			$visible = get_buddy_or_self_appearances($this->galleryid,$buddyhash);
			$visible[CURRENTUSERID] = CURRENTUSERID;
			$this->buddies = implode(',',$visible);
			$this->joins[] = 'LEFT JOIN user_appearance AS ua ON ua.IMGID=image.IMGID';
			$this->joins[] = 'LEFT JOIN onphoto ON onphoto.IMGID=image.IMGID';
			$this->joins[] = 'LEFT JOIN externalsettings AS es ON es.USERID=ua.USERID';
			$this->wheres[] = '(ua.USERID IN ('.$this->buddies.') OR onphoto.USERID IN ('.$this->buddies.'))';
			$this->wheres[] = '(ua.USERID='.CURRENTUSERID.' OR ISNULL(es.USERID) OR VISIBILITY_APPEARANCES<='.BUDDIES.')';
			$this->tables[] = 'user_appearance';
			$this->tables[] = 'onphoto';*/
	}

	function open_box(): bool {
		if (!$this->photolist) {
			return false;
		}
		$size = 0;
		if (have_admin(['photo', 'camerarequest', 'gallery'])) {
			$is_anonymous = [];
			$pusers = [];
			foreach ($this->photolist as $photo) {
				if (!($userid = $photo['USERID'])
				||	isset($pusers[$userid])
				) {
					continue;
				}
				ob_start();
				?><a<?
				?> rel="author"<?
				?> itemprop="author"<?
				?> href="<?= get_element_href('user',$userid, $nick = memcached_nick($userid)) ?>"<?
				if ($is_anonymous[$galleryid = $photo['GALLERYID']] ??= memcached_single('gallery', '
					SELECT ANONYMOUS
					FROM gallery
					WHERE GALLERID = '.$galleryid)
				) {
					?> class="light"<?
				}
				?>><?= escape_specials($nick) ?></a><?
				$pusers[$userid] = ob_get_clean();
				++$size;
			}
		}
		if (!($firstphoto = reset($this->photolist))
		||	!$firstphoto['PARTYID']
		||	!($party = memcached_party_and_stamp($firstphoto['PARTYID']))
		) {
			return false;
		}
		$party['PARTYID'] = $firstphoto['PARTYID'];
		$party['TITLE'] = memcached_single('gallery','SELECT TITLE FROM gallery WHERE GALLERYID='.$firstphoto['GALLERYID']);
		$this->party = $party;

		layout_open_box('white');
		layout_open_box_header(0,$party['PARTYID'] ? 'p'.$party['PARTYID'] : null);

		if ($party['PARTYID']) {
			echo get_element_link('party', $party['PARTYID'], $party['NAME']);
			if ($party['TITLE']) {
				?> <?
				echo escape_utf8($party['TITLE']);
			}
			if (!empty($party['EDITION'])) {
				?> <small class="ib">#<?= $party['EDITION'] ?></small><?
			}
			if (!empty($party['SUBTITLE'])) {
				?> <small class="ib"><?= MIDDLE_DOT_ENTITY ?> <?= escape_utf8($party['SUBTITLE']) ?></small><?
			}
			?> <span class="ib"><?= MIDDLE_DOT_ENTITY ?> <? _dateday_display($party['STAMP']) ?></span><?
			if (!SMALL_SCREEN) {
				if ($party['LOCATIONID']) {
					?> <span class="ib"><?= MIDDLE_DOT_ENTITY ?> <?= get_element_link('location',$party['LOCATIONID']) ?></span><?
				}
				if ($party['CITYID']) {
					?> <span class="ib"><?= MIDDLE_DOT_ENTITY ?> <?= get_element_link('city',$party['CITYID']) ?></span><?
				}
			}
		} elseif ($party['TITLE']) {
			echo escape_specials($party['TITLE']);
		}
		if (!empty($this->popularity)) {
			?> <span class="ib"><?= MIDDLE_DOT_ENTITY ?> <?= element_name('popularity') ?></span><?
		}
		if ($size <= 4
		&&	isset($pusers)
		||	!empty($this->gallery['PHOTOGRAPHER'])
		) {
			layout_continue_box_header();
			show_photographers($pusers ?? null, $this->gallery ?? null);
		}
		layout_close_box_header();
		return true;
	}
}

function photolist_buddy_count(int $galleryid, $buddies = null): int|false {
	if (!$buddies) {
		require_once '_buddies.inc';
		if (!($buddylist = _buddies_full_cached_list(CURRENTUSERID))) {
			return 0;
		}
		$buddies = implode(',', $buddylist);
	}
	return memcached_single(array('image','user_appearance','onphoto'),'
		SELECT COUNT(DISTINCT image.IMGID)
		FROM image
		LEFT JOIN user_appearance USING (IMGID)
		LEFT JOIN onphoto USING (IMGID)
		WHERE GALLERYID = '.$galleryid.'
		  AND (	 user_appearance.USERID IN ('.$buddies.')
			  OR onphoto.USERID IN ('.$buddies.')
	 	)'
	 );
}
