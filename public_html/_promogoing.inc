<?php

function start_promo($promoid) {
	return set_goingpromo($promoid,'BEFORE','pre');
}
function stop_promo($promoid) {
	return set_goingpromo($promoid,'AFTER','post');
}
/*function restart_promo($promoid) {
	return set_goingpromo($promoid,'BEFORE','pre',true);
}*/
function set_goingpromo($promoid,$ctype,$tabletype,$force = false) {
	if (!require_getlock($goinglock = 'promo_'.$ctype.'_check_'.$promoid)) {
		return false;
	}
	if (!$force) {
		$prevcnts = db_single_assoc('promo','SELECT '.$ctype.'GOING AS CERTAIN_CNT,'.$ctype.'GOINGMAYBE AS MAYBE_CNT FROM promo WHERE PROMOID='.$promoid,DB_USE_MASTER);
		if ($prevcnts
		&&	$prevcnts['CERTAIN_CNT'] === null
		) {
			$force = true;
		}
	}
	if ($force) {
		$cnts = really_set_goingpromo($promoid,$ctype,$tabletype);
	}
	db_releaselock($goinglock);
	return !empty($cnts) ? $cnts : $prevcnts;
}
function really_set_goingpromo($promoid,$ctype,$tabletype) {
	$partyids = db_simpler_array('connect','
		SELECT SQL_NO_CACHE ASSOCID
		FROM connect
		WHERE MAINTYPE="promo"
		  AND MAINID='.$promoid.'
		  AND ASSOCTYPE="party"',
		DB_USE_MASTER
	);
	if ($partyids === false) {
		return false;
	}
	if ($partyids) {
		require_once '_promo.inc';
		$v = get_v($promoid);
		$table = $v >= 2 ? 'promo_letter_v2' : 'promo_letter';
		$partyidstr = implode(',',$partyids);
		$cnts = db_single_assoc(
			[$table,'going'],'
			SELECT SQL_NO_CACHE
				COUNT(IF(MAYBE=0,1,NULL)) AS CERTAIN_CNT,
				COUNT(IF(MAYBE=1,1,NULL)) AS MAYBE_CNT
			FROM going
			JOIN '.$table.' USING (USERID)
			WHERE PARTYID IN ('.$partyidstr.')
			  AND PROMOID='.$promoid,
			DB_USE_MASTER
		);
		if ($cnts === false) {
			return false;
		}
		if (!db_insert('going'.$tabletype.'promo','
			INSERT IGNORE INTO going'.$tabletype.'promo (PARTYID,USERID,CSTAMP,PROMOID,MAYBE)
			SELECT PARTYID,USERID,CSTAMP,'.$promoid.',MAYBE
			FROM going
			WHERE PARTYID IN ('.$partyidstr.')',
			DB_USE_MASTER)
		) {
			return false;
		}
	} else {
		$cnts = ['CERTAIN_CNT'=>0,'MAYBE_CNT'=>0];
	}
	if (!db_update('promo','
		UPDATE promo SET
			'.$ctype.'GOING='.$cnts['CERTAIN_CNT'].',
			'.$ctype.'GOINGMAYBE='.$cnts['MAYBE_CNT'].'
		WHERE PROMOID='.$promoid)
	) {
		return false;
	}
	return $cnts;
}
