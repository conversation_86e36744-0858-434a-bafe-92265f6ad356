<?php

# LOCKER === 'token' only for: LOCK_PARTY
# for other locks, when token is allowed, make sure LOCKER is checked (e.g. in _menu LOCK_TICKET doesn't check LOCKER)

const DEBUG_LOCKS = false;

const LOCK_PARTY					= 1;
const LOCK_INVALIDCITYLIST			= 2;
const LOCK_TICKET					= 3;
const LOCK_APPEARANCE				= 4;
const LOCK_PROHIB_PROPOSALS			= 8;
const LOCK_PROMO					= 9;
const LOCK_METAD					= 10;
const LOCK_JOB						= 11;
const LOCK_NEWS						= 12;
const LOCK_COLUMN					= 13;
const LOCK_REVIEW					= 14;
const LOCK_INTERVIEW				= 15;
const LOCK_REPORT					= 17;
const LOCK_PROCLIST_VV				= 20;

const LOCK_PROCLIST_APPIC_CHANGES	= 21;
const LOCK_PROCLIST_NO_LOCATION		= 23;
const LOCK_PROCLIST_MOVEDS			= 24;
const LOCK_PROCLIST_POSTPONED		= 25;
const LOCK_PROCLIST_MAYBE_CANCELLED	= 26;
const LOCK_PROCLIST_MISSING_PRESALES= 27;
const LOCK_PROCLIST_TOP_ARTISTS		= 28;
const LOCK_PROCLIST_TOP_ORGANIZATIONS=29;
const LOCK_PROCLIST_TOP_LOCATIONS	= 30;
const LOCK_ARTIST					= 31;
const LOCK_LOCATION					= 32;
const LOCK_ORGANIZATION				= 33;
const LOCK_PROCLIST_BAD_TIMETABLES	= 34;

const ALLOW_STALE			= 1;
const FRESHEN_LOCK			= 2;

const ALLOW_MULTIPLE_LOCKS = [
	LOCK_TICKET					=> true,
	LOCK_PROCLIST_VV			=> true,
	LOCK_PROCLIST_APPIC_CHANGES	=> true,
];

function __get_lock_time(int $type): int {
	return match($type) {
		LOCK_APPEARANCE					=> ONE_MINUTE,

		# NOTE: processing might take a while. Uses dynamic locking, so lock is removed when window is closed
		LOCK_PROCLIST_APPIC_CHANGES		=> HALF_HOUR,
		LOCK_PROCLIST_NO_LOCATION		=> HALF_HOUR,
		LOCK_PROCLIST_MOVEDS			=> HALF_HOUR,
		LOCK_PROCLIST_POSTPONED			=> HALF_HOUR,
		LOCK_PROCLIST_MAYBE_CANCELLED	=> HALF_HOUR,
		LOCK_PROCLIST_MISSING_PRESALES	=> HALF_HOUR,

		default							=> TEN_MINUTES,
	};
}

function __lock_info(int $type, int $id = 0, array $info = []): array|false {
	static $__lockinfo;
	if ($info !== []) {
		# Set new info
		return $__lockinfo[$type][$id] = $info;
	}
	if (isset($__lockinfo[$type][$id])) {
		# There is lock info available in our static variable
		return $__lockinfo[$type][$id];
	}
	# Fetch lock info from the database
	if (!($info = db_single_array('locks', "
		SELECT SQL_NO_CACHE LOCK_LOCKER, LOCK_LOCKERID, LOCK_EXPIRES
		FROM locks
		WHERE LOCK_TYPE = $type
		  AND LOCK_ID   = $id",
		DB_USE_MASTER))
	) {
		if ($info !== false) {
			# No lock in database
			$info = [null, 0, 0];
		}
	}
	# Store the lock info,
	return $__lockinfo[$type][$id] = $info;
}
function obtain_lock(int $type, int $id = 0): bool {
	$key = 'lock_'.$type.($id ? '_'.$id : '');
	if (!db_getlock($key, 5)) {
		return false;
	}
	[$locker, $lockerid, $expires] =  __obtain_lock($type, $id, $token = have_idnumber($_COOKIE, 'FLOCK_TOKEN_'.$type.'_'.$id));
	db_releaselock($key);

	return	$lockerid
		&&	(	$locker === 'user'
			&&	$lockerid === CURRENTUSERID

			||	$locker === 'token'
			&&	$token
			&&	$token === $lockerid
			);
}

function __obtain_lock(int $type, int $id = 0, $token = 0): array|false {
	if (DEBUG_LOCKS) {
		error_log('__obtain_lock type '.$type.' id '.$id.' @ '.$_SERVER['REQUEST_URI']);
	}

	if (have_user()) {
		$tests = ['user' => CURRENTUSERID];

	} elseif (!$token) {
		return [null, 0, 0];
	} else {
		$tests = ['token' => $token];
	}

	$expires = CURRENTSTAMP + __get_lock_time($type);

	foreach ($tests as $locker => $lockerid) {
		if (!db_insert('locks', $q = "
			INSERT INTO locks SET
				LOCK_EXPIRES	= $expires,
				LOCK_TYPE		= $type,
				LOCK_ID			= $id,
				LOCK_LOCKER		= '$locker',
				LOCK_LOCKERID	= $lockerid
			ON DUPLICATE KEY UPDATE
				CNT				= IF(LOCK_LOCKER = '$locker' AND LOCK_LOCKERID = $lockerid, CNT + 1, IF(LOCK_EXPIRES < UNIX_TIMESTAMP(), 1, CNT)),
				LOCK_LOCKER		= IF(LOCK_LOCKER = '$locker' AND LOCK_LOCKERID = $lockerid OR LOCK_EXPIRES < UNIX_TIMESTAMP(), '$locker', LOCK_LOCKER),
				LOCK_LOCKERID	= IF(LOCK_LOCKER = '$locker' AND LOCK_LOCKERID = $lockerid OR LOCK_EXPIRES < UNIX_TIMESTAMP(), $lockerid, LOCK_LOCKERID),
				LOCK_EXPIRES	= IF(LOCK_LOCKER = '$locker' AND LOCK_LOCKERID = $lockerid OR LOCK_EXPIRES < UNIX_TIMESTAMP(), $expires,  LOCK_EXPIRES)")
		) {
			return [null, 0, 0];
		}

		$locked = db_affected();

		if (DEBUG_LOCKS) {
			error_log_r($q, 'affected: '.$locked);
			error_log("lock type: $type, id: $id, locker: $locker, lockerid: $lockerid, expires: "._datetime_get($expires).', success: '.($locked ? 'yes' : 'no'));
		}

		db_insert('locks_log', "
		INSERT INTO locks_log SET
			ACTION			= 'obtain',
			SUCCESS			= ".($locked ? "b'1'" : "b'0'").",
			STAMP			= UNIX_TIMESTAMP(),
			LOCK_TYPE		= $type,
			LOCK_ID			= $id,
			LOCK_LOCKER		= '$locker',
			LOCK_LOCKERID	= $lockerid,
			LOCK_EXPIRES	= $expires"
		);
		if ($locked) {
			break;
		}
	}
	return __lock_info($type, $id, $locked ? [$locker, $lockerid, $expires] : []);
}

function have_lock(int $type, int $id = 0, int $flags = 0): bool {
	$key = 'lock_'.$type.($id ? '_'.$id : null);
	if ($freshen = $flags & FRESHEN_LOCK) {
		if (!db_getlock($key)) {
			return false;
		}
	}
	[$locker, $lockerid, $expires] = __lock_info($type, $id);

	$token = have_idnumber($_COOKIE, 'FLOCK_TOKEN_'.$type.'_'.$id);

	if (have_user()) {
		$tests = ['user' => CURRENTUSERID];

	} elseif (!$token) {
		return false;
	}
	if ($token) {
		$tests = ['token' => $token];
	}

	$ok = true;

	if ($freshen) {
		foreach ($tests as $have_locker => $have_lockerid) {
			if ($lockerid === $have_lockerid
			&&	$locker   === $have_locker
			) {
				$expires = CURRENTSTAMP + __get_lock_time($type);

				if (!update_expires($type, $id, $locker, $lockerid, $expires)) {
					$ok = false;
					break;
				}
			}
		}
		db_releaselock($key);
	}
	if (!$ok) {
		return false;
	}
	foreach ($tests as $have_locker => $have_lockerid) {
		if ($lockerid
		&&	$lockerid === $have_lockerid
		&&	$locker   === $have_locker
		&&	($flags & ALLOW_STALE ? true : $expires > CURRENTSTAMP)
		) {
			return true;
		}
	}
	return false;
}

function update_expires(
	int		$type,
	int		$id,
	string	$locker,
	int		$lockerid,
	int		$expires,
): bool {
	if (DEBUG_LOCKS) {
		error_log('update_expires type '.$type.' id '.$id.' for '.$locker.':'.$lockerid.' new value: '.($expires - CURRENTSTAMP).' seconds in future');
	}
	if (!create_lock_log('update_expires', $type, $id, $locker, $lockerid)
	||	!db_update('locks', "
		UPDATE locks SET
			LOCK_EXPIRES = $expires
		WHERE LOCK_TYPE		= $type
		  AND LOCK_ID		= $id
		  AND LOCK_LOCKER	= '$locker'
		  AND LOCK_LOCKERID = $lockerid")
	) {
		return false;
	}
	__lock_info($type, $id, [$locker, $lockerid, $expires]);
	return true;
}

function release_lock(
	int		$type,
	int		$id			= 0,
	?string	$locker		= null,
	?int	$lockerid	= null,
): int|false {
	if (DEBUG_LOCKS) {
		error_log("release_lock type $type id $id @ {$_SERVER['REQUEST_URI']}");
	}
	$locker   ??= 'user';
	$lockerid ??= CURRENTUSERID;

	# true: lock released
	# false: lock not release
	# integer: amount of locks left

	if (isset(ALLOW_MULTIPLE_LOCKS[$type])) {
		if (!db_update('locks', "
			UPDATE locks SET
				CNT = IF(CNT > 0, CNT - 1, 0)
			WHERE LOCK_TYPE		= $type
			  AND LOCK_ID		= $id
			  AND LOCK_LOCKER	= '$locker'
			  AND LOCK_LOCKERID = $lockerid")
		||	false === ($cnt = db_single('locks', "
			SELECT CNT
			FROM locks
			WHERE LOCK_TYPE		= $type
			  AND LOCK_ID		= $id
			  AND LOCK_LOCKER	= '$locker'
			  AND LOCK_LOCKERID	= $lockerid"))
		) {
			return false;
		}
		if ($cnt) {
			return $cnt;
		}
	}
	if (!create_lock_log('release', $type, $id, $locker, $lockerid)
	||	!db_delete('locks', "
		DELETE FROM locks
		WHERE LOCK_TYPE		= $type
		  AND LOCK_ID		= $id
		  AND LOCK_LOCKER	= '$locker'
		  AND LOCK_LOCKERID = $lockerid")
	||	!db_affected()
	) {
		return false;
	}
	__lock_info($type, $id, [null, 0, 0]);
	return true;
}

function create_lock_log(
	string	$action,
	int		$type,
	int		$id 	  = 0,
	?string	$locker   = null,
	?int	$lockerid = null,
): bool {

	iF ($action === 'expire') {
		return db_insert('locks_log', "
			INSERT INTO locks_log (ACTION,	 LOCK_TYPE, LOCK_ID, LOCK_LOCKER, LOCK_LOCKERID, LOCK_EXPIRES, STAMP)
			SELECT		 		  '$action', LOCK_TYPE, LOCK_ID, LOCK_LOCKER, LOCK_LOCKERID, LOCK_EXPIRES, UNIX_TIMESTAMP()
			FROM locks
			WHERE LOCK_EXPIRES < $type");
	}

	$locker   ??= 'user';
	$lockerid ??= CURRENTUSERID;

	return db_insert('locks_log', "
		INSERT INTO locks_log (ACTION,	 LOCK_TYPE, LOCK_ID, LOCK_LOCKER, LOCK_LOCKERID, LOCK_EXPIRES, STAMP)
		SELECT		  	     '$action', LOCK_TYPE, LOCK_ID, LOCK_LOCKER, LOCK_LOCKERID, LOCK_EXPIRES, UNIX_TIMESTAMP()
		FROM locks
		WHERE LOCK_TYPE		= $type
		  AND LOCK_ID		= $id
		  AND LOCK_LOCKER	= '$locker'
		  AND LOCK_LOCKERID	= $lockerid");
}

function is_locked(int $type, int $id = 0): bool {
	$key = 'lock_'.$type.($id ? '_'.$id : null);

	[$locker, $lockerid, $expires] = __lock_info($type, $id);

	return	(	$locker === 'token'
			||	$locker === 'user'
			&&	$lockerid
			&&	$lockerid !== CURRENTUSERID
			)
			&&	$expires > CURRENTSTAMP;
}

function require_obtainlock(int $type, int $id = 0): bool {
	if (obtain_lock($type, $id)) {
		return true;
	}
	ob_start();
	display_lockinfo($type, $id);
	_error(ob_get_clean());
	return false;
}

function require_last_or_no_lock(int $type, int $id = 0, bool $force_token = false): bool {
	[$locker, $lockerid, $expires] = __lock_info($type, $id);

	$have_token = have_idnumber($_COOKIE, 'FLOCK_TOKEN_'.$type.'_'.$id);

	if (!$locker
	||	$locker   === 'user'
	&&	$lockerid === CURRENTUSERID

	||	$locker   === 'token'
	&&	$lockerid === $have_token
	) {
		return true;
	}

	if ($expires < CURRENTSTAMP) {
		if (!$force_token) {
			return true;
		}
		if ($type === LOCK_PARTY
		&&	$id
		&&	($may_token = db_single('element_token', 'SELECT TOKEN FROM element_token WHERE ELEMENT = "party" AND ID = '.$id))
		&&	$may_token === $have_token
		) {
			return true;
		}
	}

	ob_start();
	display_lockinfo($type, $id);
	_error(ob_get_clean());
	return false;
}

function require_last_lock_and_freshen(int $type, int $id = 0): bool {
	return require_lock($type, $id, ALLOW_STALE | FRESHEN_LOCK);
}

function require_last_lock(int $type, int $id = 0): bool {
	return require_lock($type, $id, ALLOW_STALE);
}

function require_lock(int $type, int $id = 0, int $flags = 0): bool {
	if (have_lock($type, $id, $flags)) {
		return true;
	}
	ob_start();
	display_lockinfo($type, $id);
	_error(ob_get_clean());
	return false;
}

function get_lockinfo(int $type, int $id = 0): string {
	ob_start();
	display_lockinfo($type,$id);
	return ob_get_clean();
}

function display_lockinfo($type,$id = 0,$hidenick = false): void {
	[$locker, $lockerid, $expires] = __lock_info($type, $id);
	if ($lockerid) {
		$token_lock = $locker === 'token';
		if ($expires >= CURRENTSTAMP) {
			if ($token_lock) {
				echo __C('lock:currently_in_use_by_token');
			} elseif ($hidenick) {
				echo __C('lock:currently_in_use_by_admin');
			} elseif ($lockerid) {
				echo __C('lock:currently_in_use_by_user', DO_UBB, ['USERID' => $lockerid]);
			} else {
				echo __C('lock:currently_in_use');
			}
			?> (<?= __('lock:expires_in', ['TIMELEFT'=> get_locktime_left($expires)]); ?>).<?
		} else {
			if ($token_lock) {
				echo __C('lock:recently_used_by_token');
			} elseif ($hidenick) {
				echo __C('lock:recently_used_by_admin');
			} elseif ($lockerid) {
				echo __C('lock:recently_used_by_user', DO_UBB, ['USERID' => $lockerid]);
			} else {
				echo __C('lock:recently_used');
			}
		}
	} else {
		echo __C('lock:gone'); ?>.<?
	}
}

function locked_ids(int $type, ?int $userid = null): array|false {
	# Returns array of locked IDs held by other users than the $userid ?? CURRENTUSERID
	$userid ??= CURRENTUSERID;
	static $__locked_ids;
	if (isset($__locked_ids[$type][$userid])) {
		return $__locked_ids[$type][$userid];
	}
	if (!($locks = db_simple_hash('locks', "
		SELECT DISTINCT LOCK_LOCKER, LOCK_LOCKERID, LOCK_ID, LOCK_ID
		FROM locks
		WHERE LOCK_TYPE = $type
		  AND LOCK_EXPIRES > UNIX_TIMESTAMP()",
		DB_USE_MASTER))
	) {
		return $__locked_ids[$type][$userid] = $locks;
	}
	$lock_ids = [];
	foreach ($locks as $locker => $locker_ids) {
		foreach ($locker_ids as $locker_id => $ids) {
			if ($locker === 'user'
			&&	$locker_id === $userid
			) {
				continue;
			}
			$lock_ids += $ids;
		}
	}
	if ($lock_ids) {
		asort($lock_ids);
	}
	return $__locked_ids[$type][$userid] = $lock_ids;
}

function get_locktime_left_using_stamp(int $type, int $stamp): string {
	return get_locktime_left($stamp + __get_lock_time($type));
}

function get_locktime_left(int $expires): string {
	$seconds_left = $expires - CURRENTSTAMP;
	if ($seconds_left < 60) {
		$result = __('duration:x_seconds', ['SECONDS' => $seconds_left]);
	} else {
		$minutes_left = $seconds_left / 60;
		$result =
			$seconds_left % 60 === 0
		?	__('duration:x_minutes',  ['MINUTES' => $minutes_left])
		:	__('duration:x.x_minute', ['MINUTE'  => sprintf('%.1f', $minutes_left)]);
	}
	return $result;
}

function unlock_on_unload(): int {
	require_once '_require.inc';
	if (!require_post()
	||	!require_idnumber($_POST, 'LOCK_TYPE')
	||	!require_number  ($_POST, 'LOCK_ID')
	||	!require_number  ($_POST, 'DELAY_RELEASE')
	) {
		return 400;
	}
	global $currentuser;
	$currentuser ??= new _currentuser(TINY_USER | SKIP_SETTINGS);
	if (!require_user()) {
		return 401;
	}
	if ($_POST['DELAY_RELEASE']
	?	!update_expires(
			$_POST['LOCK_TYPE'],
			$_POST['LOCK_ID'],
			'user',
			CURRENTUSERID,
			CURRENTSTAMP + $_POST['DELAY_RELEASE'])
	:	!release_lock(
			$_POST['LOCK_TYPE'],
			$_POST['LOCK_ID'])
	) {
		return 500;
	}
	return 200;
}

function release_lock_on_unload(
	int $type,
	int $id				= 0,
	int $delay_release	= 0,
): void {
	include_js('js/locks');

	# NOTE: Visibilitychange happens then other tab is selected and
	#		page is then 'invisible', so not useful for our situation.
	#		Beforeunload seems the only one usable.
/*	?><script><?
	?>document.addEventListener('visibilitychange', () => {
		if (document.visibilityState !== 'hidden') {
			return;
		}
		Pf_unlockItem(<?= $type ?>, <?= $id ?>, <?= $delay_release ?>);
	});</script><?*/
	?><script><?
	?>document.addEventListener('beforeunload', () => {
		Pf_unlockItem(<?= $type ?>, <?= $id ?>, <?= $delay_release ?>);
	});</script><?

	$lock_time = __get_lock_time($type);

	if (DEBUG_LOCKS) {
		error_log('lock_time: '.$lock_time);
	}
}

function get_lock_type_for_element(?string $element = null): int|false {
	if ('ticket' === ($element ?? $_REQUEST['sELEMENT'])) {
		mail_log('get_lock_type_for_element: element === ticket');
	}
	return match($element ?? $_REQUEST['sELEMENT']) {
		'artist'			=> LOCK_ARTIST,
		'column'			=> LOCK_COLUMN,
		'ticket',
		'contact_ticket'	=> LOCK_TICKET,
		'interview'			=> LOCK_INTERVIEW,
		'job'				=> LOCK_JOB,
		'location'			=> LOCK_LOCATION,
		'news'				=> LOCK_NEWS,
		'organization'		=> LOCK_ORGANIZATION,
		'report'			=> LOCK_REPORT,
		'review'			=> LOCK_REVIEW,
		default				=> false,
	};
}
