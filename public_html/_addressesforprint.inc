<?php

function print_addresses($title,$userlist,$pages = null) {
	require_once '_ubb.inc';
	require_once '_style.inc';
	if (is_array($pages)) {
		error_log('print_addresses: pages not supported yet!');
		_error('MULTI PAGES');
		return;
	}
	?><!DOCTYPE html><?
	?><html><?
	?><head><?
		?><link rel="stylesheet" media="all" href="<? show_stylesheet('main',['THEME'=>'light']) ?>" /><?
		?><title><?= escape_specials($title) ?></title><?
	?></head><?
	?><body style="margin:0;padding:0"><?
	$total = count($userlist);
	foreach ($userlist as /* $userid => */ $user) {
		fix_user_personalia($user);
		extract($user);
		--$total;
		?><div style="<?
			?>page-break-after:<?= $total ? 'always' : 'avoid' ?>;<?
			?>page-break-inside:avoid;<?
			?>page-break-before:avoid;<?
			?>width:210mm;<?
			?>height:250mm;<?
		?>"><?
		?><div style="position:relative"><?
			?><div style="<?
				?>position:absolute;<?
				?>top:5cm;<?
				?>left:2.5cm;<?
			?>"><?
			echo escape_specials(win1252_to_utf8(make_clean_realname($REALNAME ?: $NICK))) ?><br /><?
			echo escape_specials(win1252_to_utf8($ADDRESS)) ?><br /><?
			if (preg_match('"^\s*(\d+)\s*(\w+)\s*$"',$ZIPCODE,$match)) {
 				$ZIPCODE = $match[1].' '.$match[2];
			}
			echo escape_specials(win1252_to_utf8($ZIPCODE)),($CITYID ? ' '.escape_utf8(get_element_title('city',$CITYID)) : null) ?><br /><?
			if ($user['COUNTRYID']
			&&	$user['COUNTRYID'] != 1
			) {
				echo escape_specials(win1252_to_utf8(get_element_title('country',$COUNTRYID))) ?><br /><?
			}
			?></div><?
			if ($pages) {
				?><div style="<?
					?>position:absolute;<?
					?>top:10cm;<?
					?>left:2cm;<?
				?>"><?
				echo make_all_html($pages);
				?></div><?
			}
			?><div style="clear:both"></div><?
		?></div><?
		?></div><?
	}
	?></body></html><?
}
function sticker_addresses($title,$userlist,$pages = null) {
	require_once '_ubb.inc';
	require_once '_style.inc';
	if (is_array($pages)) {
		error_log('print_addresses: pages not supported yet!');
		_error('MULTI PAGES');
		return;
	}
	$foreign = [];
	$domestic = [];
	foreach ($userlist as $user) {
		if ($user['COUNTRYID']
		&&	$user['COUNTRYID'] != 1
		) {
			$foreign[] = $user;
		} else {
			$domestic[] = $user;
		}
	}
	$perpage = 16;
	$cnt = 0;
	$final = [];
	foreach ($foreign as $userid => $user) {
		if (++$cnt >= 14) {
			$cnt = 0;
			$last = reset($domestic);
			$final[$last] = $domestic[$last];
			unset($domestic[$last]);
		}
		$final[$userid] = $user;
	}
	foreach ($domestic as $userid => $user) {
		$final[$userid] = $user;
	}

	?><!DOCTYPE html><?
	?><html><?
	?><head><?
/*		?><link rel="stylesheet" media="all" href="<? show_stylesheet('main',['THEME'=>'light']) ?>" /><?*/
		?><title><?= escape_specials($title) ?></title><?
		echo '<style>
@page {
	margin: 0;
	size: A4
}
BODY {
	color: black;
	background-color: white;
}
TABLE {
	border-collapse: collapse;
}
</style>';
	?></head><?
	?><body style="margin:0;padding:0"><?

	$start_page = function($open = true,$lastpage = false) {
		static $first = true;
		if ($first) {
			$first = false;
		} else {
			?></table><?
			?></div><?
		}
		if (!$open) {
			return;
		}
		?><div style="<?
			?>page-break-after:<?= !$lastpage ? 'always' : 'avoid' ?>;<?
			?>page-break-inside:avoid;<?
			?>page-break-before:avoid;<?
			?>width:205mm;<?
			?>height:289mm;<?
		?>"><?
		?><table style="width:100%;height:100%"><?
	};

	$cellcnt = $perpagecnt = 0;
	$pages = ceil(count($userlist) / $perpage);
	$currentpage = 1;
	foreach ($final as /* $userid => */ $user) {
		fix_user_personalia($user);
		extract($user);

		if (!$perpagecnt || ++$perpagecnt > $perpage) {
			$perpagecnt = 1;
			++$currentpage;
			$start_page(true,$currentpage == $pages);
		}
		if (!$cellcnt) {
			?><tr><?
		}
		?><td style="text-align:center;width:50%;height:<?= 100 / 8 ?>%"><?
		?><div style="display:inline-block;text-align:left;vertical-align:middle"><?
		echo escape_specials(win1252_to_utf8($REALNAME ?: $NICK)) ?><br /><?
		echo escape_specials(win1252_to_utf8($ADDRESS)) ?><br /><?
		echo escape_specials(win1252_to_utf8($ZIPCODE)),($CITYID ? ' '.escape_utf8(get_element_title('city',$CITYID)) : null);
		if ($user['COUNTRYID']
		&&	$user['COUNTRYID'] != 1
		) {
			?><br /><? echo escape_specials(win1252_to_utf8(get_element_title('country',$COUNTRYID)));
		}
		?></div><?
		?></td><?
		if (++$cellcnt >= 2) {
			$cellcnt = 0;
			?></tr><?
		}
	}
	$start_page(false);
	?></body></html><?
}
