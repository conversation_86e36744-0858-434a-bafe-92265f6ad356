<?php

/** @noinspection FileClassnameCaseInspection */

require_once '_unicode.inc';
#require_once '_verify_aspiring.inc';

class box {
	public const int FLUID	= 1;

	private static array	 $boxes = [];
	private static ?self	 $box = null;
	private static int		 $grand_count = 0;

	private ?string 		 $class					= null;
	private ?string			 $id					= null;
	private ?string			 $onclick				= null;
	private array			 $columns 				= [];
	private int				 $total_headers			= 0;
	private float|false|null $prev_part				= 0;
	private float			 $remaining 			= 1;		# remaining part to distribute
	private int				 $column_cnt			= 0;
	private int				 $non_fixed_column_cnt	= 0;	# total columns without supplied part size
	private array			 $styles 				= [];
	private int				 $flags					= 0;
	private string			 $extra_right_header;
	private int				 $count					= 0;

	public function __construct() {
		require_once '_smallscreen.inc';
		ob_start();
	}
	final public function start_column(float|false|null $part = 0): void {
		$this->columns[] = [$this->prev_part, ob_get_clean()];
		$this->total_headers = 0;
		++$this->column_cnt;

		if ($this->prev_part) {
			if (is_float($this->prev_part)
			||	  is_int($this->prev_part)
			) {
				$this->remaining -= $this->prev_part;
			}
		} else {
			++$this->non_fixed_column_cnt;
		}
		$this->prev_part = $part;
		if ($part !== false) {
			ob_start();
		}
	}
	final public function display(): void {
		error_log('DISPLAY BOX: '.$this->count);
		require_once '_browser.inc';
		if ($this->prev_part !== null) {
			$this->start_column(false);
		}
		if ($this->non_fixed_column_cnt) {
			$default_part = $this->remaining / $this->non_fixed_column_cnt;
		}
		if (!$this->columns) {
			# empty box
			return;
		}
		?><div<?
		if ($this->class) {
			?> class="<?= $this->class ?> box"<?
		}
		if ($this->id) {
			?> id="<?= $this->id ?>"<?
		}
		if ($this->onclick) {
			?> onclick="<?= $this->onclick ?>"<?
		}
		if ($this->styles) {
			?> style="<?= implode('; ', $this->styles) ?>;"<?
		}
		?>><?
		$extra = $this->extra_right_header ?? '';
		foreach ($this->columns as &$column) {
			$column[1] = str_replace(BOX_HEADER_PLACEHOLDER, $extra, $column[1]);
			if (!$extra) {
				$column[1] = str_replace('<div class="r"></div>', '', $column[1]);
			}
		}
		unset($column);
		if ($this->column_cnt === 1) {
			[$part, $data] = $this->columns[0];
			?><div class="box-column"><?= $data ?></div><?
		} else {
			foreach ($this->columns as $column) {
				--$this->column_cnt;
				[$part, $data] = $column;
				if (SMALL_SCREEN) {
					?><div class="box-column"><?= $data ?></div><?
					continue;
				}
				if (!$part) {
					$part = $default_part;
				}
				# round half down:
				$part = floor(1000000 * $part) / 10000;
				$float = $part === 'right' ? 'r' : 'l';

				?><div class="<?= $float ?> box-column"<?
				if (!($this->flags & self::FLUID)) {
					?> style="width: <?= $part ?>%;"<?
				}
				?>><?= $data ?></div><?
			}
		}
		?></div><?
	}

	public static function open(
		?string	$class	= null,
		?string	$id		= null,
		int		$flags	= 0,
	): void {
		$box = new self();
		$box->total_headers	= 0;
	 	$box->class		= $class ?: 'white';
		$box->id		= $id;
		$box->count		= self::$grand_count++;
		$box->flags		= $flags;
		self::$box		= $box;
		self::$boxes[]	= $box;
		error_log('OPEN BOX: '.$box->count);
	}

	static function add_style(string $style): void {
		self::$box->styles[] = $style;
	}

	static function increase_total_headers(): void {
		++self::$box->total_headers;
	}

	static function get_total_headers(): int {
		return self::$box->total_headers;
	}

	static function active_box(): bool {
		return (bool)self::$box;
	}

	static function set_part(float $part): void {
		self::$box->prev_part = $part;
	}

	static function onclick(string $onclick): void {
		self::$box->onclick = $onclick;
	}

	static function next_column(float $part = 0): void {
		self::$box->start_column($part);
	}

	static function extra_right_header(string $str): void {
		self::$box->extra_right_header = $str;
	}

	public static function close(): void {
		$box = array_pop(self::$boxes);
		self::$box = end(self::$boxes) ?: null;
		error_log('CLOSE BOX: '.$box->count);
		if (!$box) {
			$traces = debug_backtrace();
			error_log('WARNING no box');
			for ($i = 1; $i <= 2; ++$i) {
				error_log($traces[$i]['file'].', line '.$traces[$i]['line'],0);
			}
		}
		$box->display();
	}
}

function layout_open_box(
	?string	$class	= null,
	?string	$id		= null,
	int		$flags	= 0
): void {
	box::open($class, $id, $flags);
}

function layout_continue_box(float $part = 0): void {
	box::next_column($part);
}

function layout_close_box(
	bool $no_ad = false
): void {
	box::close();
	if (!$no_ad
	&&	function_exists('show_ad_placeholder')
	) {
		show_ad_placeholder();
	}
}

// BOX HEADER
const BOX_HEADER_LIGHT				= 0x2;
const BOX_HEADER_FAN				= 0x4;
const BOX_HEADER_GOING				= 0x8;
const BOX_HEADER_UNACCEPTED_ELEMENT	= 0x20;
const BOX_HEADER_CENTER				= 0x40;
const BOX_HEADER_BOTTOM				= 0x80;
const BOX_HEADER_REPLACEMENT		= 0x100;

function layout_open_box_header(
	int		$flags	= 0,
	?string	$id		= null
): void {
	global $boxh_flags, $boxh_id, $boxh_left;
	$boxh_flags	= $flags;
	$boxh_id	= $id;
	$boxh_left	= false;
	ob_start();
}

function layout_continue_box_header(): void {
	global $boxh_left;
	$boxh_left = ob_get_clean();
	ob_start();
}

function layout_close_box_header(): void {
	global $boxh_last, $boxh_left, $boxh_flags, $boxh_id;
	$boxh_last = ob_get_clean();
	layout_box_header(
		$boxh_left !== false ? $boxh_left : $boxh_last,
		$boxh_left !== false ? $boxh_last : null,
		$boxh_flags,
		$boxh_id
	);
}

const BOX_HEADER_PLACEHOLDER = "\x4\x9\x20\x7";

function layout_box_header(
	string|int|null	$title		= null,
	string|int|null	$more		= null,
	string|int|null	$even_more	= null,
	?string 		$id			= null,
): void {
	$classes = ['noverflow'];

	foreach (['more', 'even_more'] as $key) {
		if (!$$key) {
			continue;
		}
		switch ($$key) {
		case 'warning':
		case 'error':
		case 'notice':
		case 'hh':
		case 'hl':
		case 'required':
		case 'online':
		case 'nomargin':
		case 'ttmrgn':
		case 'ilfake':
			$classes[] = $$key;
			$$key = null;
			break;
		}
	}
	if (is_int($title)) {
		$flags	   = $title;
		$title	   = $more;
		$more	  = $even_more;
		$even_more = null;

	} elseif (is_int($more)) {
		$flags	   = $more;
		$more	   = $even_more;
		$even_more = null;

	} elseif (is_int($even_more)) {
		$flags	   = $even_more;
		$even_more = null;

	} else {
		$flags = 0;
	}
	if ($more
	&&	$more[0] === '#'
	) {
		$id = substr($more, 1);
		$more = null;
	}
	$classes[] = 'header';
	if ($even_more) {
		$classes[] = $even_more;
	}
	if ($flags) {
		if ($flags & BOX_HEADER_FAN)			$classes[] = 'fan';
		if ($flags & BOX_HEADER_GOING)			$classes[] = 'going';
		if ($flags & BOX_HEADER_LIGHT)			$classes[] = 'light';
		if ($flags & BOX_HEADER_UNACCEPTED_ELEMENT)
												$classes[] = 'unaccepted-element';
		if ($flags & BOX_HEADER_CENTER)			$classes[] = 'center';
		if ($flags & BOX_HEADER_BOTTOM)			$classes[] = 'bottom';
		if ($flags & BOX_HEADER_REPLACEMENT)	$replace = true;
	}
	if (!isset($replace)
	&&  box::get_total_headers()
	) {
		$classes[] = 'nf';
	}
	box::increase_total_headers();
	?><div<?
	if ($id) {
	 	?> id="<?= $id ?>"<?
	}
	?> class="<?= implode(' ', $classes) ?>"><?
	if ($title) {
		if (str_starts_with($title, '<h1')
		||	str_starts_with($title, '<h2')
		) {
			echo $title;
		} else {
			?><div class="l bold"><h3><?= $title ?></h3></div><?
		}
	}
	?><div class="r"><?= BOX_HEADER_PLACEHOLDER, $more ?></div><?
	?></div><?
}

function layout_show_section_header(
			$arg1			= null,
			$extra			= null,
			$extrafterid	= null,
	bool	$show_errors	= true,
	int		$to_userid		= 0,
	bool	$no_link		= false,
): void {
	static $__done = false;
	if ($__done) {
		return;
	}
	$__done = true;
	require_once '_elementnames.inc';
	layout_open_section_header();
	$element = $_REQUEST['sELEMENT'];
	$id		 = $_REQUEST['sID'];
	$ACTION  = $_REQUEST['ACTION'];
	$display_element = get_display_element($element);
	$link = null;
	$class = null;
	if (!$no_link) {
		if ($id && $ACTION) {
			$link = get_element_href($element, $id);
			if ($element === 'party'
			&&  $ACTION  === 'single'
			) {
				$class = 'url';
			}
		} elseif ($element !== 'photo') {
			$link = $element;
			if ($element === 'user'
			&&  $ACTION  === 'personalnote;'
			) {
				$link .= "/{$ACTION}";
			}
		}
		if ($link) {
			?><a <?
			if ($class) {
				?> class="<?= $class ?>"<?
			}
			?> href="<?= $link ?>"><?
		}
	}
	if (is_string($arg1)) {
		$title = $arg1;
	} else {
		global $__singular;

		$title = __C(
			$__singular
		?	(	$display_element === 'party'
			?	'element:event'
			:	'element:'.$display_element
			)
		:	(	$display_element === 'party'
			?	'elements:event'
			:	'elements:'.$display_element
			)
#			RETURN_FALSE | (have_admin() ? FORCE_READ : 0)
		);
	}
	echo $title ?: Eelement_name($display_element);
	if (!$no_link
	&&	!empty($link)
	) {
		?></a><?
	}
	if ($id) {
		if (!ROBOT
		&&	$element !== 'error'
		&&	have_admin()
		) {
			?> <small class="light"><?= MIDDLE_DOT_ENTITY ?> <?= $id ?></small><?
		}
		if ($extra) {
			?> <?= MIDDLE_DOT_ENTITY ?> <? echo $extra;
		}
		if ($extrafterid) {
			layout_continue_section_header();
			$continued = true;
			echo $extrafterid;
		}
	} elseif ($extra) {
		?> <?= MIDDLE_DOT_ENTITY ?> <?
		echo $extra;
	}
	$offense_element = $element === 'ticket' ? 'contact_ticket' : $element;
	require_once '_offense_access.inc';
	if ($id
	&&	may_create_offense($offense_element,$id)
	&&	(	$o_userid =
			$offense_element === 'user'
		?	$id
		:	(	$arg1[
				isset($arg1['WITHUSERID'])
			?	'WITHUSERID'
			:	(	isset($arg1['BYUSERID'])
				?	'BYUSERID'
				:	'USERID'
				)
			] 	?? null
			)
		)
	) {
		if (empty($continued)) {
			layout_continue_section_header();
			$continued = true;
		}
		layout_display_offense_link($o_userid,$offense_element,$id);
	}
/*	switch ($_REQUEST['ACTION']) {
	case 'form':
		echo ' '.MIDDLE_DOT_ENTITY.' ',__($id ? 'action:change': 'action:add');
		break;
	}*/
	if ($element === 'camera') {
		$element = 'camerarequest';
	}
	if (/*	have_user()
	&&*/$element !== 'offense'
	&&	(require_once '_contactelements.inc')
	&&	is_contact_element($element)
	&&	$id
	) {
		if (empty($continued)) {
			layout_continue_section_header();
		}
		layout_display_contacts($element, $id, $to_userid ?: (!empty($arg1['USERID']) ? $arg1['USERID'] : 0));
	}
	layout_close_section_header();
}

function layout_section_header(?string $left_part = null, ?string $right_part = null): void {
	layout_open_section_header();
	echo $left_part;
	if ($right_part) {
		layout_continue_section_header();
		echo $right_part;
	}
	layout_close_section_header();
}
function layout_open_section_header(): void {
	global $__left_section,$__right_section;
	$__left_section = null;
	$__right_section = null;
	ob_start();
}
function layout_continue_section_header(): void{
	global $__left_section,$__right_section;
	$__left_section = ob_get_clean() ?: false;
	ob_start();
}
function layout_close_section_header() {
	global $__left_section,$__right_section;
	$remainder = ob_get_clean();

	if (!$__left_section && $__left_section !== false) {
		$__left_section = $remainder;
	} else {
		$__right_section = $remainder;
	}
}

const MENU_SIMPLE				= 1;	# FIXME: almost not used, try to eliminate
const MENU_CLICK_OPEN			= 2;
const MENU_IN_HEADER			= 3;

const MENUITEM_CLOSED			= 0;
const MENUITEM_OPEN				= 1;
const MENUITEM_OPEN_LINK		= 2;
const MENUITEM_OPEN_SELECTED	= 3;

const MENU_ITEMS				= 0;
const MENU_STATE				= 1;
const MENU_TYPE					= 2;
const MENU_CONTINUED			= 3;
const MENU_CLASS				= 4;

function layout_menu(array $menu): void {
	layout_open_menu();
	foreach ($menu as $item) {
		[$name, $uri, $selected] = $item;
		layout_menuitem($name, $uri, $selected);
	}
	layout_close_menu();
}

function layout_open_menu(int $type = 0, ?string $extraclass = null): void {
	global $_layout_menu;
	$separator = match($type) {
		MENU_CLICK_OPEN	=> '<br />',
		default			=> box::active_box() ? ' '.MIDDLE_DOT_ENTITY.' ' : ' ',
	};
	$_layout_menu = [0, MENUITEM_CLOSED, $type, false, $extraclass, $separator];
	if ($type === MENU_SIMPLE) {
		?><nav class="smenu"><div<?
		if ($extraclass) {
			?> class="<?= $extraclass ?>"<?
		}
		?>><?
	} else {
		ob_start();
	}
}

function post_link(
	string	$name,
	string	$link,
	?string	$title = null,
	?string	$extra = null,
): void {
	include_js('js/form/postlink');
	?><span<?
	if ($title) {
		?> title="<?= $title ?>"<?
	}
	?> class="unhideanchor"<?
	?> onclick="post_link(this, '<?= escape_specials($link) ?>'<?= $extra ? ", $extra" : '' ?>);"><?= $name ?></span><?
}

function do_inline(
	string				$name,
	?string				$question,
	string				$method,
	string				$url,
	string				$action,
	?string				$handler = null,
	string|array|null	$arg	 = null
) {
	if (!$arg) {
		$data = null;
	} elseif (is_array($arg)) {
		$data = '';
		$first = true;
		foreach ($arg as $key => $val) {
			if ($first) {
				$first = false;
			} else {
				$data .= '&';
			}
			$data .= rawurlencode($key).'='.rawurlencode($val);
		}
	} else {
		$data = rawurlencode($arg);
	}
	static $handlers;
	$crc = crc32($method."\x08".$question."\x08"."\x08".$url."\x08".$action."\x08".$handler);

	if (!isset($handlers[$crc])) {
		$handlers[$crc] = true;
		?><script>
		function do_inline_<?= $crc ?>(self) {
			<? if ($question) { ?>
				if (!confirm('<?= __($question, IS_JAVASCRIPT) ?>')) {
					return;
				}
			<? } ?>
			let previous_class = self.className;
			let previous_click = self.onclick;
			self.className = 'light wait';
			do_inline('<?= $method ?>', '<?= $url ?>', '<?= $action ?>', <?= $handler ?: 'null' ?>, null, null, self);
			self.className = previous_class;
			self.onclick   = previous_click;
		}
		</script><?
	}
	?><span<?
	if ($data) {
		?> data-data="<?= escape_specials($data) ?>"<?
	}
	?> class="unhideanchor"<?
	?> onclick="do_inline_<?= $crc ?>(this)"><?= __($name); ?></span><?
}

function with_confirm(
	string	$name,
	string	$href,
	?string	$question	= null,
	?array	$data		= null,
	?string	$target		= null,
): void {
	$question ??= __($_REQUEST['sELEMENT'].':confirm:'.$_REQUEST['ACTION'].'_LINE');

	if (!($secret = get_form_secret($href))) {
		return;
	}

	include_js('js/form/confirm');
	?><span<?
	?> class="unhideanchor"<?
	if ($secret) {
		?> data-secret="<?= $secret ?>"<?
	}
	?> onclick="get_confirmation(this,'<?= escape_specials($question) ?>',<?
	if ($href[0] === 'P') {
		echo escape_specials($href);
	} else {
		?>'<?= escape_specials($href) ?>'<?
	}
	?>,<?
	echo $target ? "'".escape_specials($target)."'" : 'null';

	if ($data) {
		?>,{<?
		$first = true;
		foreach ($data as $key => $val) {
			if ($first) {
				$first = false;
			} else {
				?>,<?
			}
			echo escape_specials($key),":'",escape_specials($val),"'";
		}
		?>}<?
	}
	?>)"><?= $name ?></span><?
}

function layout_open_menuitem($separator = null) {
	global $_layout_menu;
	if ($_layout_menu[MENU_ITEMS]) {
		echo $separator ? $separator : $_layout_menu[5];
	}
	++$_layout_menu[MENU_ITEMS];
	$_layout_menu[MENU_STATE] = MENUITEM_OPEN;
}

const BLANK_TARGET	= 0xDEADBEEF;
const DOWNLOAD_LINK	= 0xCAFEBEEF;

function layout_menuitem(
	string					$name,
	string					$href,
	int|bool|string|null	$arg1 = null,
	?string					$arg2 = null,
	?string					$arg3 = null,
): void {
	global $_layout_menu;
	if ($_layout_menu[MENU_STATE] === MENUITEM_CLOSED) {
		layout_open_menuitem();
	} else {
		layout_close_menuitem();
		layout_open_menuitem();
	}
	$extra = '';
	$selected = false;
	if ($arg1 !== null) {
		if (is_bool($arg1)) {
			if ($arg1) {
				$selected = 'selected';
			}

		} elseif ($arg1 === BLANK_TARGET) {
			$extra .= 'target="_blank"';

		} elseif ($arg1 === DOWNLOAD_LINK) {
			$extra .= 'download';

		} elseif (
			is_string($arg1)
		&&	$arg1
		&&	$arg1[0] === ' '
		) {
			$extra .= $arg1;
		}
		if ($arg2) {
			$extra .= $arg2;
			if ($arg3) {
				$extra .= $arg3;
			}
		}
	}
	$classes = [];
	if ($selected) {
		$classes[] = $selected;

		if ($extra) {
			if (preg_match('!(\s+class="(?P<extra_class>[^"]*)")!is', $extra, $match)) {
				$orig_extra = $extra;

				$classes[] = $match['extra_class'];
				$extra = str_replace($match[0], '', $extra);

				mail_log('layout_menuitem extra class', item: get_defined_vars());
			}
		}
	}

	?><a<?
	if ($classes) {
		?> class="<?= implode(' ', $classes) ?>"<?
	}
	if ($extra) {
		echo ' ', $extra;
	}
	?> href="<?= $href ?>"><?= $name ?></a><?
}

function layout_name_menuitem(): void {
	global $_layout_menu;
	if ($_layout_menu[MENU_STATE] === MENUITEM_OPEN_LINK) {
		?>"><?
	}
}
function layout_close_menuitem(): void {
	global $_layout_menu;
	echo match($_layout_menu[MENU_STATE]) {
		MENUITEM_OPEN_SELECTED	=> '</b>',
		MENUITEM_OPEN_LINK		=> '</a>',
		default					=> null,
	};
	$_layout_menu[MENU_STATE] = MENUITEM_CLOSED;
}

/*function layout_next_menuitem($lighter = false) {
	global $_layout_menu;
	if ($_layout_menu[MENU_STATE] !== MENUITEM_CLOSED) {
		layout_close_menuitem();
	}
	layout_open_menuitem($lighter ? '<span class="lighter"> <?= MIDDLE_DOT_ENTITY ?> </span>' : ' '.MIDDLE_DOT_ENTITY.' ');
}*/

function layout_next_menuitem(): void {
	global $_layout_menu;
	if ($_layout_menu[MENU_STATE] !== MENUITEM_CLOSED) {
		layout_close_menuitem();
	}
	layout_open_menuitem();
}

function layout_continue_menu(): void {
	global $_layout_menu;
	if (ob_get_length()) {
		$leftcontent = ob_get_clean();
		?><nav class="smenu"><?
		?><div class="l block"><?= $leftcontent; ?></div><?
		ob_start();
	} else {
		?><nav class="smenu"><?
	}
	?><div class="r block"><?
	$_layout_menu[MENU_CONTINUED] = true;
	$_layout_menu[MENU_ITEMS] = 0;
}

function layout_close_menu(?string $name = null): void {
	global $_layout_menu;
	if ($_layout_menu[MENU_STATE] !== MENUITEM_CLOSED) {
		layout_close_menuitem();
	}
	if ($_layout_menu[MENU_TYPE] === MENU_IN_HEADER) {
		ob_end_flush();
	} elseif ($_layout_menu[MENU_TYPE] === MENU_SIMPLE) {
		?></div></nav><?
	} elseif ($_layout_menu[MENU_TYPE] === MENU_CLICK_OPEN) {
		$menu_data = ob_get_clean();
		require_once '_bubble.inc';
		$bubble = new bubble(CLICK_BUBBLE);
		$bubble->catcher($name);
		$bubble->content();
		?><nav class="smenu"><div style="line-height: 1.5em; margin: .25em .5em;"><?= $menu_data ?></div></nav><?
		$bubble->display();
	} elseif ($_layout_menu[MENU_CONTINUED]) {
		ob_end_flush();
		?></div><div class="clear"></div></nav><?
	} elseif ($content = ob_get_clean()) {
		?><nav class="smenu"><div class="<?
		if ($class = $_layout_menu[MENU_CLASS]) {
			echo $class ?> <?
		}
		?>block"><?= $content ?></div></nav><?
	}
}

# TABLE flags
const TABLE_CLEAN					= 0x1;
const TABLE_BOLD_FIELD				= 0x2;
const TABLE_FULL_WIDTH				= 0x4;
const TABLE_FIFTY_FIFTY				= 0x8;
const TABLE_VTOP					= 0x10;
const TABLE_LIGHT					= 0x20;
const TABLE_ROW_HILITE_ONMOUSEOVER	= 0x40;
const TABLE_REGULAR					= 0x80;
const TABLE_NOMARGIN				= 0x100;
const TABLE_VTTOP					= 0x200;

# CELL & ROW flags, can be combined in single call
const CELL_ALIGN_LEFT				= 0x1;
const CELL_ALIGN_RIGHT				= 0x2;
const CELL_ALIGN_CENTER				= 0x4;
const CELL_LIGHT					= 0x8;
const ROW_HIDDEN					= 0x10;
const CELL_VTOP						= 0x20;
const ROW_LIGHT						= 0x40;
const ROW_SELF						= 0x80;
const ROW_LINE_THROUGH				= 0x100;
const ROW_ADMIN						= 0x200;
const ROW_FULL						= 0x400;
const ROW_HILITE					= 0x800;
const ROW_UNACCEPTED				= 0x1000;
const ROW_GOING						= 0x2000;
const CELL_SPACEY					= 0x4000;
const REQUIRED_FIELD				= 0x8000;
const CELL_RIGHT_SPACE				= 0x10000;
const CELL_LINE_THROUGH				= 0x20000;
const TABLE_SORTABLE				= 0x40000;
const COLUMN_DONT_SORT				= 0x80000;
const COLUMN_IS_SORTED_ASC			= 0x100000;
const ROW_BOLD_HILITED				= 0x200000;
const COLUMN_SORT_NUMERIC			= 0x400000;
const COLUMN_SORT_ALPHA				= 0x800000;
const NOWRAP						= 0x1000000;
const ZEBRA							= 0x2000000;

function layout_set_onclick($arg) {
	# FIXME: almost not used
	global $__row_onclick;
	$__row_onclick = $arg;
}
function layout_open_table(
	int|string	$type			= 0,
	?string		$id				= null,
	?string		$userdef		= null,
	?int		$rows_per_col	= null,
	?int		$max_cols		= null
): void {
	global $__rows_per_col;
	$__rows_per_col = $rows_per_col;
	if ($rows_per_col) {
		global $__max_cols;
		$__max_cols = $max_cols;
		ob_start();
	}
	if ($userdef) {
		$classes = [$userdef];
	}
	if (is_string($type)) {
		if ($type) {
			$types = explode(' ', $type);
			sort($types);
			$classes[] = implode(' ', $types);
		}
	} else {
		if ($type & TABLE_BOLD_FIELD)				$classes[] = 'boldfield';
		if ($type & TABLE_FULL_WIDTH) 				$classes[] = 'fw';
		if ($type & TABLE_FIFTY_FIFTY)				$classes[] = 'fifty';
		if ($type & TABLE_VTOP)						$classes[] = 'vtop';
		if ($type & TABLE_VTTOP)					$classes[] = 'vttop';
		if ($type & TABLE_LIGHT) 					$classes[] = 'light';
		if ($type & TABLE_REGULAR) 					$classes[] = 'regular';
		elseif (!($type & TABLE_CLEAN)) 			$classes[] = 'default';
		if ($type & TABLE_NOMARGIN)					$classes[] = 'nomargin';
		if ($type & NOWRAP) 						$classes[] = 'nowrap';
		if ($type & TABLE_ROW_HILITE_ONMOUSEOVER)	$classes[] = 'hha';
		if ($type & TABLE_SORTABLE)					{ $classes[] = 'sortable'; include_js('js/sorttable'); }
	}
	?><table<?
	if ($id) {
		?> id="<?= $id ?>"<?
	}
	if (!empty($classes)) {
		?> class="<?= implode(' ', $classes) ?>"<?
	}
	?>><?
}
function layout_build_rowclasses(int $flags, string|array|null $userdef = null): array {
	if ($userdef) {
		$rowclasses = is_array($userdef) ? $userdef : [$userdef];
	}
	if ($flags & ROW_LIGHT) 		$rowclasses[] = 'light';
	if ($flags & ROW_SELF)			$rowclasses[] = 'self';
	if ($flags & ROW_LINE_THROUGH)	$rowclasses[] = 'line-through';
	if ($flags & ROW_ADMIN)			$rowclasses[] = 'admin';
	if ($flags & ROW_HIDDEN)		$rowclasses[] = 'hidden';
	if ($flags & ROW_HILITE)		$rowclasses[] = 'hilited';
	if ($flags & ROW_BOLD_HILITED)	$rowclasses[] = 'bold-hilited';
	if ($flags & ROW_UNACCEPTED)	$rowclasses[] = 'unaccepted';
	if ($flags & ROW_GOING)			$rowclasses[] = 'party-attending';
	if ($flags & ZEBRA)				$rowclasses[] = 'zebra';

	return $rowclasses ?? [];
}
function layout_build_cellclasses(int $flags, string|array|null $userdef = null): array {
	if ($userdef) {
		$cellclasses = is_array($userdef) ? $userdef : [$userdef];
	}
	if ($flags & NOWRAP)				$cellclasses[] = 'nowrap';
	if ($flags & CELL_ALIGN_LEFT)		$cellclasses[] = 'left';
	if ($flags & CELL_ALIGN_RIGHT)		$cellclasses[] = 'right';
	if ($flags & CELL_ALIGN_CENTER)		$cellclasses[] = 'center';
	if ($flags & CELL_VTOP)				$cellclasses[] = 'vtop';
	if ($flags & CELL_SPACEY)			$cellclasses[] = 'hpad';
	if ($flags & CELL_RIGHT_SPACE)		$cellclasses[] = 'rpad';
	if ($flags & CELL_LIGHT)			$cellclasses[] = 'light';
	if ($flags & CELL_LINE_THROUGH)		$cellclasses[] = 'unavailable';
	if ($flags & COLUMN_DONT_SORT)		$cellclasses[] = 'sorttable_nosort';
	if ($flags & COLUMN_IS_SORTED_ASC)	$cellclasses[] = 'is_sorted_asc';
	if ($flags & COLUMN_SORT_NUMERIC)	$cellclasses[] = 'sorttable_numeric';
	if ($flags & COLUMN_SORT_ALPHA)		$cellclasses[] = 'sorttable_alpha';
	if ($flags & REQUIRED_FIELD) 		$cellclasses[] = 'required';

	return $cellclasses ?? [];
}

function layout_start_header_row(): void {
	?><tr class="nohl"><?
}
function layout_start_header_cell(int $flags = 0, string|array|null $userdef = null): void {
	if ($flags || $userdef) {
		$cellclasses = layout_build_cellclasses($flags,$userdef);
	}
	?><th<?
	if (!empty($cellclasses)) {
		?> class="<?= implode(' ',$cellclasses) ?>"<?
	}
	?>><?
}
function layout_header_cell(?string $content = null, int $flags = 0, string|array|null $class = null): void {
	if ($flags || $class) {
		$cellclasses = layout_build_cellclasses($flags,$class);
		if ($cellclasses) {
			?><th class="<?= implode(' ',$cellclasses); ?>"><?
		} else {
			?><th><?
		}
	} else {
		?><th><?
	}
	if ($content) {
		echo $content;
	}
	?></th><?
}
function layout_header_cell_right(?string $content = null, int $flags = 0, string|array|null $class = null): void {
	layout_header_cell($content, $flags | CELL_ALIGN_RIGHT, $class);
}
function layout_header_cell_center(?string $content = null, int $flags = 0, string|array|null $class = null): void {
	layout_header_cell($content, $flags | CELL_ALIGN_CENTER, $class);
}
function layout_start_header_cell_right(int $flags = 0, string|array|null $userdef = null): void {
	layout_start_header_cell(CELL_ALIGN_RIGHT | $flags, $userdef);
}
function layout_next_header_cell_right(int $flags = 0, string|array|null $userdef = null): void {
	?></th><?
	layout_start_header_cell(CELL_ALIGN_RIGHT | $flags,$userdef);
}
function layout_next_header_cell_center(int $flags = 0, string|array|null $userdef = null): void {
	?></th><?
	layout_start_header_cell(CELL_ALIGN_CENTER | $flags, $userdef);
}
function layout_next_header_cell(int $flags = 0, string|array|null $userdef = null): void {
	?></th><?
	layout_start_header_cell($flags, $userdef);
}
function layout_stop_header_cell(): void {
	?></th><?
}
function layout_stop_header_row(): void {
	?></tr><?
}
function layout_row(string $field, string|int|float $value): void {
	layout_start_row();
	echo $field;
	layout_field_value();
	echo $value;
	?></td></tr><?
}
// FIXME:	rename  row to start_field_row
//		rename rrow to start_row

function layout_start_reverse_row(int $flags = 0, string|array|null $userdef = null): void {
	$cellclasses = layout_build_cellclasses($flags,$userdef);
	$cellclasses[] = 'right';
	$rowclasses = layout_build_rowclasses($flags,$userdef);
	?><tr<?
	if ($rowclasses) {
		?> class="<?= implode(' ',$rowclasses); ?>"<?
	}
	?>><td class="<?= implode(' ',$cellclasses); ?>"><?
}

const SEPARATOR_NONE		= 0;
const SEPARATOR_DOT			= 1;
const SEPARATOR_PERCENTAGE	= 2;
const SEPARATOR_MULTIPLIER	= 3;
const FIELDONLY				= 4;

function layout_value_field(int|string $separator = SEPARATOR_DOT): void {
	if (is_number($separator)) {
		switch ($separator) {
		case SEPARATOR_DOT:
			?></td><td class="tsep hpad center"><?= MIDDLE_DOT_ENTITY ?></td><td><?
			return;
		case SEPARATOR_PERCENTAGE:
			?></td><td class="tsep rpad center">%</td><td><?
			return;
		case SEPARATOR_MULTIPLIER:
			?></td><td class="tsep hpad center"><?= MULTIPLICATION_SIGN_ENTITY ?></td><td><?
			return;
		case FIELDONLY:
			?></td><td style="width:0"></td><td><?
			return;
		default:
			?></td><td></td><td><?
			return;

		}
	} elseif (is_string($separator)) {
		?></td><td class="tsep hpad center"><?= $separator ?></td><td><?
	} else {
		?></td><td></td><td><?
	}
}

function layout_restart_reverse_row(int $flags = 0, string|array|null $userdef = null): void {
	?></td></tr><?
	layout_start_reverse_row($flags,$userdef);
}

function layout_start_row(int $flags = 0, string|array|null $userdef = null, ?string $id = null, bool $start_cell = true): void {
	$cellclasses = layout_build_cellclasses($flags, in_array($userdef, ['presencerow', 'bold-hilited'], true) ? null : $userdef);
	$cellclasses[] = 'field';
	$rowclasses = layout_build_rowclasses($flags,$userdef);
	?><tr<?
	if ($rowclasses) {
		?> class="<?= implode(' ',$rowclasses) ?>"<?
	}
	if ($id) {
		?> id="<?= $id ?>"<?
	}
	?>><?
	if ($start_cell) {
		?><td class="<?= implode(' ',$cellclasses) ?>"><?
	}
}

function layout_start_spanned_row(int $span = 2, ?string $class = null): void {
	?><tr><td<?
	if ($class) {
		?> class="<?= $class ?>"<?
	}
	?> colspan="<?= $span ?>"><?
}

function layout_restart_spanned_row(int $span = 2, ?string $class = null): void {
	layout_stop_row();
	layout_start_spanned_row($span, $class);
}

function layout_start_spanned_row_right(int $span = 2): void {
	?><tr><td class="right" colspan="<?= $span; ?>"><?
}

function layout_start_rrow_right(int $flags = 0, string|array|null $userdef = null, string|array|null $usercelldef = null): void {
	layout_start_rrow($flags | CELL_ALIGN_RIGHT, $userdef, $usercelldef);
}

function layout_start_rrow_center(int $flags = 0, string|array|null $userdef = null): void {
	layout_start_rrow($flags | CELL_ALIGN_CENTER, $userdef);
}

function layout_start_rrow(int $flags = 0, string|array|null $userdef = null, string|array|null $usercelldef = null, ?string $id = null): void {
	$cellclasses = layout_build_cellclasses($flags,$usercelldef);
	$rowclasses = layout_build_rowclasses($flags,$userdef);
	global $__cell_value,$__row_onclick,$__rowtitle;
	?><tr<?
	if ($id) {
		?> id="<?= $id ?>"<?
	}
	if (!empty($rowclasses)) {
		?> class="<?= implode(' ',$rowclasses); ?>"<?
	}
	if (!empty($__rowtitle)) {
		?> title="<?= $__rowtitle; ?>"<?
	}
	if (!empty($__row_onclick)) {
		?> onclick="<?= $__row_onclick ?>"<?
	}
	?>><td<?
	if (!empty($cellclasses)) {
		?> class="<?= implode(' ',$cellclasses); ?>"<?
	}
	if ($__cell_value) {
		?> data-value="<?= $__cell_value ?>"<?
		unset($GLOBALS['__cell_value']);
	}
	?>><?
}

function layout_field_value(?string $userclass = null, ?string $itemprop = null, ?string $separator = null): void {
	echo $separator;
	?>&nbsp;</td><td<?
	if ($userclass) {
		?> class="<?= $userclass ?>"<?
	}
	if ($itemprop) {
		?> itemprop="<?= $itemprop ?>"<?
	}
	?>><?
}

function layout_field_value_right(?string $userclass = null, ?string $itemprop = null, ?string $separator = ':'): void {
	layout_field_value('right'.($userclass ? ' '.$userclass : ''), $itemprop, $separator);
}

function set_cell_value($value): void {
	$GLOBALS['__cell_value'] = $value;
}

function layout_cell(string $content, int $flags = 0, string|array|null $class = null): void {
	layout_start_cell($flags, $class);
	echo $content;
	layout_stop_cell();
}

function layout_next_cell(int $flags = 0, string|array|null $class = null, ?string $itemprop = null): void {
	?></td><td<?
	if (isset($GLOBALS['__cell_value'])) {
		?> data-value="<?= $GLOBALS['__cell_value'] ?>"<?
		unset($GLOBALS['__cell_value']);
	}
	if ($itemprop) {
		?> itemprop="<?= $itemprop ?>"<?
	}
	if (($flags || $class)
	&&	($cellclasses = layout_build_cellclasses($flags, $class))
	) {
		?> class="<?= implode(' ', $cellclasses) ?>"<?
	}
	?>><?
}

function layout_start_cell(int $flags = 0, string|array|null $class = null): void {
	?><td<?
	if (($flags || $class)
	&&	($cellclasses = layout_build_cellclasses($flags, $class))
	) {
		?> class="<?= implode(' ', $cellclasses) ?>"<?
	}
	if (isset($GLOBALS['__cell_value'])) {
		?> data-value="<?= $GLOBALS['__cell_value'] ?>"<?
		unset($GLOBALS['__cell_value']);
	}
	?>><?
}

function layout_stop_cell(): void {
	?></td><?
}

function layout_start_separator_reverse_row(int $colspan = 2): void {
	?><tr><td colspan="<?= $colspan ?>" class="separator"></td></tr><tr><td class="right"><?
}

function layout_restart_separator_reverse_row(int $colspan = 2): void {
	?></td></tr><tr><td colspan="<?= $colspan ?>" class="separator"></td></tr><tr><td class="right"><?
}

function layout_start_separator_row(int $colspan = 2): void {
	?><tr><td colspan="<?= $colspan ?>" class="separator"></td></tr><tr><td class="field"><?
}

function layout_restart_separator_row(int $colspan = 2): void {
	?></td></tr><?
	layout_start_separator_row($colspan);
}

function layout_start_info_row(int $colspan = 2): void {
	?><tr><td colspan="<?= $colspan ?>" class="inforow"><?
}

function layout_stop_row(bool $stop_cell = true): void {
	if ($stop_cell) {
		echo '</td>';
	}
	echo '</tr>';
}

function layout_close_table(): void {
	?></table><?
	global $__row_onclick, $__rows_per_col;
	if ($__rows_per_col) {
		if ($__rows_per_col === true) {
			ob_end_flush();
		} elseif (preg_match('"^(<table[^>]*>)<tr>(.*)</tr></table>$"',$data = ob_get_clean(),$match)) {
			global $__max_cols;
			[, $open_tag, $inner_data] = $match;
			$parts = explode('</tr><tr>',$inner_data);
			$total = count($parts);
			$cols = (int)ceil($total / $__rows_per_col);
			if ($cols === 1) {
				echo $data;
			} else {
				if ($__max_cols) {
					$cols = min($__max_cols,$cols);
				}
				$__rows_per_col = (int)round($total / $cols);
				$pct =  100 / $cols;

				for ($i = 0; $i < $cols; ++$i) {
					?><div class="ib vtop" style="width: <?= $pct ?>%;"><?
					echo $open_tag;
					for ($j = 0; $j < $__rows_per_col && isset($parts[$ndx = $i * $__rows_per_col + $j]); ++$j) {
						?><tr><?= $parts[$ndx] ?></tr><?
					}
					?></table><?
					?></div><?
				}
			}
		} else {
			echo $data;
		}
		$__rows_per_col = null;
	}
	unset($__row_onclick);
}

class deflist {
	private ?string	$table_class	= null;
	private ?string	$row_class		= null;
	private ?string	$field_class	= null;
	private ?string	$value_class	= null;
	private ?string	$row_id			= null;
	private ?string	$itemprop		= null;
	private int		$groupid		= 0;
	private array	$groupargs		= [];
	private ?array	$caption		= null;

	private ?int	$id				= null;
	private array	$rows			= [];

	final public function __construct(?string $class = null, ?int $id = null) {
		$this->table_class = $class;
		$this->id = $id;
	}

	final public function set_row_class(string|array $class): void {
		if (!$class) {
			return;
		}
		$this->row_class = is_array($class) ? implode(' ', $class) : $class;
	}

	final public function set_field_class(string $class): void {
		$this->field_class = $class;
	}

	final public function set_value_class(string $class): void {
		$this->value_class = $class;
	}

	final public function set_row_id(string $id): void {
		$this->row_id = $id;
	}

	final public function set_itemprop(string $itemprop): void {
		$this->itemprop = $itemprop;
	}

	final public function add_row(?string $field = null, ?string $value = null): void {
		$this->rows[$this->groupid][] = [
			$field,
			$value,
			$this->row_class,
			$this->field_class,
			$this->value_class,
			$this->itemprop,
			$this->row_id
		];
		$this->row_class =
		$this->field_class =
		$this->value_class =
		$this->itemprop =
		$this->row_id = null;
	}
	final public function add_rows($list): void {
		if (!is_array($list)) {
			$i = 0;
			foreach (func_get_args() as $arg) {
				if ($i === 0) {
					$field = $arg;
					$i = 1;
				} else {
					$this->rows[$this->groupid][] = [
						$field,
						$arg,
						$this->row_class,
						$this->field_class,
						$this->value_class,
						$this->itemprop,
						$this->row_id
					];
					$i = 0;
				}
			}
			return;
		}
		foreach ($list as $info) {
			if (!$info) {
				continue;
			}
			[$field, $value] = $info;
			$this->rows[$this->groupid][] = [
				$field,
				$value,
				$this->row_class,
				$this->field_class,
				$this->value_class,
				$this->itemprop,
				$this->row_id
			];
		}
	}
	final public function start_group(?string $argstr = null): void {
		++$this->groupid;
		$this->groupargs[$this->groupid] = $argstr;
	}
	final public function end_group(): void {
		++$this->groupid;
	}
	final public function add_caption(string $content, ?string $argstr = null): void {
		$this->caption = [$content, $argstr];
	}
	final public function display(): void {
		if (!$this->rows) {
			return;
		}
		?><table<?
		if ($this->id) {
			?> id="<?= $this->id ?>"<?
		}
		?> class="<?
		if (!isset($_REQUEST['DYN'])) {
			?>nodyn <?
		}
		if ($this->table_class) {
			echo $this->table_class;
		}
		?>"><?
		if ($this->caption) {
			[$content, $argstr] = $this->caption;
			?><caption<?
			if ($argstr) {
				echo ' ', $argstr;
			}
			?>><?= $content ?></caption><?
		}

		foreach ($this->rows as $group => $rows) {
			if ($rows) {
				?><tbody<?
				if (!empty($this->groupargs[$group])) {
					echo ' ',$this->groupargs[$group];
				}
				?>><?
				foreach ($rows as $row) {
					[$field, $value, $row_class, $field_class, $value_class, $itemprop, $row_id] = $row;
					?><tr<?
					if ($row_class) {
						?> class="<?= $row_class ?>"<?
					}
					if ($row_id) {
						?> id="<?= $row_id ?>"<?
					}
					?>><?
					if ($value === null) {
						if ($field) {
							?><td colspan="2"<?
							if ($field_class) {
								?> class="<?= $field_class ?>"<?
							}
							?>><?= $field ?></td><?
						}
					} else {
						if ($field) {
							?><td class="field<?
							if ($field_class) {
								?> <? echo $field_class;
							}
							?>"<?
							?>><?= $field ?></td><?
						} else {
							?><td></td><?
						}
						?><td<?
						if ($value_class) {
							?> class="<?= $value_class ?>"<?
						}
						if ($itemprop) {
							?> itemprop="<?= $itemprop ?>"<?
						}
						?>><?= $value ?></td><?
					}
					?></tr><?
				}
				?></tbody><?
			}
		}
		?></table><?
	}
	final public function have_rows(): bool {
		return (bool)$this->rows;
	}
}

function get_element_list($element,$list) {
	$size = count($list);
	$result = '';
	foreach ($list as $id => $info) {
		$name = ($arr = is_array($info)) ? $info['NAME'] : $info;
		$result .= get_element_link($element, $id, $name);
		if ($arr) {
			$result .= get_dead($info);
		}

		if (--$size) {
			$result .= ', ';
		}
	}
	return $result;
}

function layout_restart_row(int $flags = 0, ?string $id = null, string|array|null $userdef = null, string|array|null $rowuserdef = null) {
	# FIXME: restart_row as id and userdef swapped compared to start_row
	$cellclasses = layout_build_cellclasses($flags,$userdef);
	$cellclasses[] = 'field';
	$rowclasses = layout_build_rowclasses($flags,$rowuserdef);
	?></td></tr><tr<?
	if ($id) {
		?> id="<?= $id; ?>"<?
	}
	if ($rowclasses) {
		?> class="<?= implode(' ',$rowclasses); ?>"<?
	}
	?>><td class="<?= implode(' ',$cellclasses); ?>"><?
}

function layout_restart_rrow(int $flags = 0, string|array|null $userdef = null, string|array|null $usercelldef = null) {
	if (!$flags && !$userdef && !$usercelldef) {
		?></td></tr><tr><td><?
		return;
	}
	$cellclasses = layout_build_cellclasses($flags,$usercelldef);
	$rowclasses = layout_build_rowclasses($flags,$userdef);
	?></td></tr><tr<?
	if ($rowclasses) {
		?> class="<?= implode(' ',$rowclasses); ?>"<?
	}
	?>><td<?
	if ($cellclasses) {
		?> class="<?= implode(' ',$cellclasses); ?>"<?
	}
	?>><?
}
function layout_display_contacts($element = null,$id = 0,$touserid = 0,$havelock = null,$forumid = 0) {
#	if (!have_user()) {
#		return;
#	}
	require_once '_spider.inc';
	if (ROBOT) {
		return;
	}
	ob_start();
	if (have_user()) {
		if (isset($_REQUEST['ACTION'])
		&&	in_array($_REQUEST['ACTION'], ['single', 'showtopic'])
		) {
			require_once '_commentkeep.inc';
			?> <?
			show_commentkeep_links($element,$id);
		}
		if ($havelock === null
		||	$havelock
		) {
			require_once '_contactoutgoing.inc';
			if ($element === 'user'
			&&	$id !== CURRENTUSERID
			&&	have_outgoing_allowance()
			) {
				?> <a href="/ticket/outgoing-form?TO_USERID=<?= $id ?>"><?
				?><img<?
	/*			?> class="lmrgn icon lower"<?*/
				?> class="icon"<?
				?> src="<?= STATIC_HOST ?>/images/outgoing_mail<?= is_high_res() ?>.png"<?
	/*			?> src="/_help.png"<?*/
				?> alt="<?= __C('contacts:outgoing_to_person'); ?>"<?
				?> title="<?= __C('contacts:outgoing_to_person'); ?>"<?
				?> /></a><?
			} elseif ($element && $id && have_outgoing_allowance($element,$id,$forumid)) {
				?> <a href="/ticket/outgoing-form?ELEMENT=<?= $element ?>;ID=<?
				echo $id;
				if ($touserid) {
					?>;TO_USERID=<? echo $touserid;
				}
				?>"><?
				?><img<?
	/*			?> class="lmrgn icon lower"<?*/
				?> class="icon"<?
				?> src="<?= STATIC_HOST ?>/images/outgoing_mail<?= is_high_res() ?>.png"<?
	/*			?> src="/_help.png"<?*/
				?> alt="<?= __C('contacts:outgoing_about_element') ?>"<?
				?> title="<?= __C('contacts:outgoing_about_element') ?>"<?
				?> /></a><?
			}
			// only head admin for current element may send outgoing ticket!
			if ($id === CURRENTUSERID
			&&	(	$element === 'user'
				||	$element === 'user_text'
				)
			) {
				// no self contacts
				$nocontact = true;
			}
		}
	}
	if (!isset($nocontact)) {
		?> <?
		show_link_to_ticket_form($element,$id);
	}
	echo ob_get_clean();
}

function show_link_to_ticket_form(string $element, int $id, ?string $extra_class = null) {
	?> <a<?
	if ($extra_class) {
		?> class="<?= $extra_class ?>"<?
	}
	if ($element === 'chat') {
		?> target="_blank"<?
	}
	?> href="/ticket/form?ELEMENT=<? echo $element;
	if ($id) {
		?>;ID=<? echo $id;
	}
	if (!have_user()) {
		?>;EMAIL<?
	}
	?>"><img<?
	?> class="icon"<?
	?> src="<?= STATIC_HOST ?>/images/notify<?= is_high_res() ?>.png"<?
	?> alt="<?= $notify = __C('contacts:notify_admin') ?>"<?
	?> title="<?= $notify ?>"<?
	?> /></a><?
}

function layout_display_offense_link(int $userid, string $element, int $id, ?bool $is_admin = null): void {
	if (!have_admin()
	||	(	!$is_admin
		&&	(require_once '_offense_access.inc')
		&&	!may_create_offense($element,$id)
		)
	) {
		return;
	}
	?> <a href="/offense/registerform?USERID=<? echo $userid;
	if ($element) {
		?>;ELEMENT=<? echo $element;
		if ($id) {
			?>;ID=<? echo $id;
		}
	}
	?>"><img<?
	?> class="icon wide"<?
	?> src="<?= STATIC_HOST ?>/images/police<?= is_high_res() ?>.png"<?
	?> alt="<?= $alt = __C('action:reprimand'); ?>"<?
	?> title="<?= $alt ?>"<?
	?> /></a><?
}

// NICE NICKLIST

function layout_display_list(
	string	$element,
	array	$list,
	bool	$invisible_row	= false,
): void {
	$size = count($list);
	$have_admin = have_admin($element);
	$assoc = !isset($list[0]);
	foreach ($list as $arg1 => $arg2) {
		$outer_span = false;
		if (isset($arg2['VISIBLE_TO_ALL'])
		&&	!$arg2['VISIBLE_TO_ALL']
		) {
			if ($outer_span = $have_admin) {
				?><span class="admin"><?
			} else {
				--$size;
				continue;
			}
		}
		if ($assoc) {
			echo get_element_link($element, $arg1, is_array($arg2) ? $arg2['NAME'] : $arg2, class: 'link');
		} elseif (is_scalar($arg2)) {
			echo get_element_link($element, $arg2, class: 'link');
		} else {
			if ($extra_class =
				!$invisible_row
			&&	(	isset($arg2['VISI'])
				&&	$arg2['VISI'] !== ROW_FULL
				||	!empty($arg2['HIDDEN'])
				)
			?	'light'
			:	null
			) {
				?><span class="<?= $extra_class ?>"><?
			}
			echo get_element_link($element, $arg2['ID'], $arg2['NAME'], class: 'link');
			if ($extra_class) {
				?></span><?
			}
		}
		if ($outer_span) {
			?></span><?
		}
		if (--$size > 1) {
			?>, <?
		} elseif ($size === 1) {
			?> &amp; <?
		}
	}
}

function show_table_rowlist(
	 array	$rows,
	 int	$arrsize,
	 string	$method,
	?object	$obj			= null,
	?int	$maxcols		= null,
	 mixed	$arg_to_method	= null,
) {
	# FIXME: WARNING: cell must close the <td first! ... should really do this another way
	# NOTE: Probably use CSS columns instead of this
	if (SMALL_SCREEN) {
		if ($arrsize > 40) {
			$cols = 2;
		} else {
			$cols = 1;
		}
	} elseif ($arrsize > 60) {
		$cols = 4;
	} elseif ($arrsize > 40) {
		$cols = 4;
	} elseif ($arrsize > 20) {
		$cols = 3;
	} elseif ($arrsize > 10) {
		$cols = 2;
	} else {
		$cols = 1;
	}
	if (isset($maxcols)) {
		if ($maxcols > 1) {
			$cols /= $maxcols;
		} else {
			$cols *= $maxcols;
		}
		$cols = max(1, round($cols));
	}
	$rowcnt = ceil($arrsize / $cols);
	if (!isset($rows[0])) {
		reset($rows);
		for ($c = 0; $c < $cols; ++$c) {
			for ($r = 0; $r < $rowcnt; ++$r) {
				$id = key($rows);
				if (!$id) {
					break 2;
				}
				$table[$r][$c] = $id;
				next($rows);
			}
		}
		for ($r = 0; $r < $rowcnt; ++$r) {
			?><tr><?
			for ($c = 0; $c < $cols; ++$c) {
				if (empty($table[$r][$c])) {
					?><td></td><?
					continue;
				}
				$id = $table[$r][$c];
				$name = $rows[$id];
				?><td<?
				if (!$r) {
					?> width="<?= floor(100 / $cols); ?>%"<?
				}
				call_user_func($obj ? [$obj, $method] : $method, [$id, $name], $arg_to_method);
				?></td><?
			}
			?></tr><?
		}
		return;
	}
	for ($rownum = 0; $rownum < $rowcnt; ++$rownum) {
		?><tr><?
		for ($i = 0; $i < $cols; ++$i) {
			if ($rownum + $rowcnt * $i < $arrsize) {
				$row = $rows[$rownum + $rowcnt * $i];
				?><td<?
				if ($rownum === 0) {
					?> width="<?= floor(100 / $cols); ?>%"<?
				}
				call_user_func($obj ? [$obj, $method] : $method, $row, $arg_to_method);
				?></td><?
			} else {
				?><td></td><?
			}
		}
		?></tr><?
	}
}
function get_human_bytes($bytes) {
	ob_start();
	print_human_bytes($bytes);
	return ob_get_clean();
}
function print_human_bytes(int $bytes, $base = 1024): void {
	$tb = $bytes / ($div = $base ** 4);
	$bytes -= floor($tb) * $div;
	$gb = $bytes / ($div = $base ** 3);
	$bytes -= floor($gb) * $div;
	$mb = $bytes / ($div = $base ** 2);
	$bytes -= floor($mb) * $div;
	$kb = $bytes / $base;
	$bytes -= floor($kb) * $base;

	$i = ($base === 1024 ? 'i' : '');

	if (floor($tb) > 0) {
		printf("%.1f T{$i}B", round($tb, 1));
	} elseif (floor($gb)) {
		printf("%.1f G{$i}", round($gb, 1));
	} elseif (floor($mb)) {
		printf("%.1f M{$i}B", round($mb, 1));
	} elseif (floor($kb)) {
		printf("%.1f K{$i}B", round($kb, 1));
	} else {
		echo $bytes,' B';
	}
}
function print_small_big_number(int $number): void {
	$bil = $number / ($div = 1000 ** 3);
	$number -= floor($bil) * $div;
	$mil = $number / ($div = 1000 ** 2);
	$number -= floor($mil) * $div;
	$kil = $number / 1000;
	$number -= floor($kil) * 1000;

	if (floor($bil)) {
		printf('%.1f ', round($bil, 1)); echo __('shortnum:billion');
	} elseif (floor($mil)) {
		printf('%.1f ', round($mil, 1)); echo __('shortnum:million');
	} elseif (floor($kil)) {
		printf('%.1f ', round($kil, 1)); echo __('shortnum:killion');
	} else {
		echo $number;
	}
}

const SHOW_ALL_ALTER = 1;

function layout_display_alteration_note($info,$arg1 = null,$arg2 = null) {
	$page_mstamp = 0;
	if (!empty($_REQUEST['sELEMENT'])
	&&	!empty($_REQUEST['sID'])
	) {
		$page_mstamp = db_single(['pagechanged','elementchanged'], "
			SELECT GREATEST(
				COALESCE(
					(	SELECT MSTAMP
						FROM pagechanged
						WHERE ELEMENT = '{$_REQUEST['sELEMENT']}'
						  AND ID = {$_REQUEST['sID']}
					),	0
				) /* end COALESCE */,
				COALESCE(
					(	SELECT MSTAMP
						FROM elementchanged
						WHERE ELEMENT = '{$_REQUEST['sELEMENT']}'
						  AND ID = {$_REQUEST['sID']}
					),	0
				) /* end COALESCE */
				) /* end GREATEST */"
		);
	}

	$is_admin = have_admin();
	$flags = 0;
	if ($arg1 !== null) {
		if (is_int($arg1)) {
			$flags = $arg1;
		} else {
			$testkeys = null;
			if (is_string($arg1)) {
				$testkeys[] = $arg1;
			}
			if (is_string($arg2)) {
				$testkeys[] = $arg2;
			}
			if ($testkeys) foreach ($testkeys as $testkey) {
				$transchanged = memcached_single_array(['translationtexthistory', 'translationkey'], '
					SELECT MUSERID, MSTAMP
					FROM translationtexthistory
					JOIN translationkey USING (KEYID)
					WHERE KEYNAME = "'.addslashes($testkey).'"
					  AND LANGID = '.CURRENTLANGID.'
					ORDER BY MSTAMP DESC
					LIMIT 1'
				);
				if ($transchanged) {
					[$muserid, $mstamp] = $transchanged;
					if (!$info || $mstamp > $info['MSTAMP']) {
						$info['MSTAMP'] = $mstamp;
						$info['MUSERID'] = $muserid;
						if ($mstamp > $page_mstamp)  {
							$page_mstamp = $mstamp;
						}
					}
				}
			}
		}
	}

	# Content depends on a lot of things, this is not granular enough.
	# Apache now seems to return 304 when Last-Modified is same as If-Modified-Since
	# if ($page_mstamp) {
	# 	header('Last-Modified: '.($hdr = gmdate('D, d M Y H:i:s',$page_mstamp).' GMT'));
	# }

	if (!empty($_REQUEST['sELEMENT'])
	&&	$_REQUEST['sELEMENT'] === 'party'
	&&	($partyid = $_REQUEST['sID'])
	) {
		$lastlineup = db_simple_hash(['lineup', 'partyarea'], "
			(	SELECT MSTAMP,MUSERID
				FROM lineup
				WHERE PARTYID = $partyid
				ORDER BY MSTAMP DESC
				LIMIT 1
			) UNION (
				SELECT MSTAMP,MUSERID
				FROM partyarea
				WHERE PARTYID = $partyid
				ORDER BY MSTAMP DESC
				LIMIT 1
			)
			ORDER BY MSTAMP DESC LIMIT 1"
		);
	}
	if (!$is_admin) {
		$ustamp = ($info['MSTAMP'] ?? 0) ?: ($info['CSTAMP'] ?? 0);
		$datemod = ' itemprop="dateModified"';

		$last_mod = 0;
		$first_mod = 0;

		if (isset($page_mstamp)
		&&	$page_mstamp > $ustamp
		) {
			$ustamp = $page_mstamp;
			$datemod = null;
		}

		if (empty($info['MUSERID'])
#		||	$info['MUSERID'] !== CURRENTUSERID
		) {
			if ($ustamp > $last_mod) {
				$last_mod = $ustamp;
			}
		} elseif (
			!$ustamp
#		||	$info['MUSERID'] === CURRENTUSERID
		) {
			if ($flags & SHOW_ALL_ALTER) {
				if (!empty($info['MSTAMP'])) {
					$notes[] =  str_replace(
						'%DATETIME%',
						'<time itemprop="dateCreated" datetime="'.gmdate(ISO8601Z,$info['MSTAMP']).'">'._datedaytime_get($info['CSTAMP']).'</time>',
						__('alteration:lastchange', KEEP_EMPTY_KEYWORDS)
					);
					if ($info['CSTAMP'] !== $info['MSTAMP']) {
						$notes[] =  str_replace(
							'%DATETIME%',
							'<time itemprop="dateCreated" datetime="'.gmdate(ISO8601Z,$info['CSTAMP']).'">'._datedaytime_get($info['CSTAMP']).'</time>',
							__('alteration:added', KEEP_EMPTY_KEYWORDS)
						);
					}
					if ($info['MSTAMP'] > $last_mod) {
						$last_mod = $info['MSTAMP'];
					}
					if ($info['CSTAMP']) {
						if ($first_mod) {
							$first_mod = min($first_mod,$info['CSTAMP']);
						} else {
							$first_mod = $info['CSTAMP'];
						}
					}
				}

			} elseif (!empty($info['MSTAMP'])) {
				$notes[] =  str_replace(
					'%DATETIME%',
					'<time'.$datemod.' datetime="'.gmdate(ISO8601Z,$info['MSTAMP']).'">'._datedaytime_get($info['MSTAMP']).'</time>',
					__('alteration:lastchange', KEEP_EMPTY_KEYWORDS)
				);
				if ($info['MSTAMP'] > $last_mod) {
					$last_mod = $info['MSTAMP'];
				}

			} elseif (!empty($info['CSTAMP'])) {
				$notes[] =  str_replace(
					'%DATETIME%',
					'<time itemprop="dateCreated" datetime="'.gmdate(ISO8601Z,$info['CSTAMP']).'">'._datedaytime_get($info['CSTAMP']).'</time>',
					__('alteration:added', KEEP_EMPTY_KEYWORDS)
				);
				if ($info['CSTAMP'] > $first_mod) {
					$first_mod = $info['CSTAMP'];
				}
			}

			if (!empty($lastlineup)) {
				[$lmstamp, $lmuserid] = keyval($lastlineup);
				if ($lmstamp > $last_mod) {
					$last_mod = $lmstamp;
				}
				$notes[] = __('alteration:lineup_last_changed_LINE',[
					'DATETIME' => _datedaytime_get($lmstamp)
				]);
			}

			if ($ustamp
			&&	!isset($no_pagemod)
			) {
				if ($ustamp > $last_mod) {
					$last_mod = $ustamp;
				}
				$notes[] = str_replace(
					'%DATETIME%',
					'<time itemprop="dateModified" datetime="'.gmdate(ISO8601Z,$ustamp).'">'._datedaytime_get($ustamp).'</time>',
					__('alteration:page_changed', KEEP_EMPTY_KEYWORDS)
				);
			}
		}
		if ($first_mod) {
			?><meta itemprop="dateCreated" content="<?= gmdate(ISO8601Z, $first_mod) ?>" /><?
		}
		if ($last_mod) {
			?><meta itemprop="dateModified" content="<?= gmdate(ISO8601Z, $last_mod) ?>" /><?
		}
		return false;
	}
	if (!empty($info['CUSERID']) && $info['CUSERID'] !== 1) {
		$userid = $info['CUSERID'];
	} elseif (!empty($info['USERID']) && $info['USERID'] !== 1) {
		$userid = $info['USERID'];
#	} else {
#		$userid = 0;
	}
	if (!empty($userid)) {
		$ids[] = $userid;
	}
	if (!empty($info['MUSERID'])) {
		$ids[] = $info['MUSERID'];
	}
	$contactstars = null;
	if (isset($ids)) {
		require_once '_contactstars.inc';
		$contactstars = new contactstars($ids);
	}
	$note = '';
	$ustamp = ($info['MSTAMP'] ?? null) ?: ($info['CSTAMP'] ?? null);
	$datemod = ' itemprop="dateModified"';
	$datecrt = ' itemprop="dateCreated"';

	if (isset($page_mstamp)
	&&	$page_mstamp > $ustamp
	) {
		$ustamp = $page_mstamp;
		$datemod = null;
	}
	# FIXME: Needs cleaning up.
/*	if (isset($userid)) {
		if (!empty($info['MUSERID'])
		&&	$info['MUSERID'] === $userid
		) {
#			if ($flags & SHOW_ALL_ALTER) {
				$note = str_replace(
					'%DATETIME%',
					'<time itemprop="dateCreated" datetime="'.gmdate(ISO8601Z,$info['CSTAMP']).'">'._datedaytime_get($info['CSTAMP']).'</time>',
					__('alteration:added_by_'.($userid ? 'user' : 'anonymous'), DO_UBB | KEEP_EMPTY_KEYWORDS, ['USERID'	=> $userid])
				);
				if ($info['MSTAMP']
				&&	$info['MSTAMP'] !== $info['CSTAMP']
				) {
					$note .= '<br />'.str_replace(
						'%DATETIME%',
						'<time'.$datemod.' datetime="'.gmdate(ISO8601Z,$info['MSTAMP']).'">'._datedaytime_get($info['MSTAMP']).'</time>',
							$info['MUSERID'] !== $userid
						?	__('alteration:lastchange_by_user', DO_UBB | KEEP_EMPTY_KEYWORDS, ['MUSERID' => $info['MUSERID']])
						:	__('alteration:lastchange', KEEP_EMPTY_KEYWORDS)
					);
				} elseif ($userid) {
			   		$note = str_replace(
						'%DATETIME%',
						'<time'.$datecrt.' datetime="'.gmdate(ISO8601Z,$info['CSTAMP']).'">'._datedaytime_get($info['CSTAMP']).'</time>',
						__('alteration:added_by_user', DO_UBB | KEEP_EMPTY_KEYWORDS, ['USERID' => $userid])
					 );
				} else {
					$note = str_replace(
						'%DATETIME%',
						'<time'.$datecrt.' datetime="'.gmdate(ISO8601Z,$info['CSTAMP']).'">'._datedaytime_get($info['CSTAMP']).'</time>',
						__('alteration:added', DO_UBB | KEEP_EMPTY_KEYWORDS)
					);
				}
#			}
		} elseif (!empty($info['CSTAMP'])) {
			if (!empty($info['MSTAMP']) && $info['MSTAMP'] !== $info['CSTAMP']) {
				$note = str_replace(
					'%DATETIME%',
					'<time'.$datemod.' datetime="'.gmdate(ISO8601Z,$info['MSTAMP']).'">'._datedaytime_get($info['MSTAMP']).'</time>',
					__('alteration:added_and_lastchange_by_system', DO_UBB | KEEP_EMPTY_KEYWORDS, [
						'USERID'	=> $userid,
					])
				);
			} else {
				$note = str_replace(
					'%DATETIME%',
					'<time'.$datecrt.' datetime="'.gmdate(ISO8601Z,$info['CSTAMP']).'">'._datedaytime_get($info['CSTAMP']).'</time>',
					__('alteration:added_by_user', DO_UBB | KEEP_EMPTY_KEYWORDS, [
						'USERID'	=> $userid
					])
				);
			}
		} else {
			$note = __('alteration:added_nodatetime',DO_UBB,array('USERID'=>$userid));
		}
	} elseif (
		!empty($info['MUSERID'])
	||	!empty($info['MSTAMP'])
	) {
		$srcs = ['%DATETIME%'];
		$reps = ['<time'.$datemod.' datetime="'.gmdate(ISO8601Z,$info['MSTAMP']).'">'._datedaytime_get($info['MSTAMP']).'</time>'];

		if (!empty($info['CSTAMP'])) {
			$srcs[] = '%CDATETIME%';
			$reps[] = '<time'.$datecrt.' datetime="'.gmdate(ISO8601Z,$info['CSTAMP']).'">'._datedaytime_get($info['CSTAMP']).'</time>';
		}
		$note =	str_replace(
			$srcs,
			$reps,
			!empty($info['MUSERID'])
			?	__('alteration:lastchange_by_user', DO_UBB | KEEP_EMPTY_KEYWORDS, [
					'MUSERID'	=> $info['MUSERID'],
				])
			:	__('alteration:lastchange', KEEP_EMPTY_KEYWORDS)
		);
	}
	if (!empty($lastlineup)) {
		[$lmstamp, $lmuserid] = keyval($lastlineup);
		$args['USERID'] = $lmuserid;

		$add_note = __('alteration:lineup_last_changed_by_LINE',DO_UBB,[
			'USERID'	=> $lmuserid,
			'DATETIME'	=> _datedaytime_get($lmstamp)
		]);

		if ($note) {
			$note .= '<br />';
		}
		$note .= $add_note;
	}
	if (!isset($no_pagemod)
	&&	$ustamp
	) {
		$note .= '<br />'.str_replace(
			'%DATETIME%',
			'<time itemprop="dateModified" datetime="'.gmdate(ISO8601Z,$ustamp).'">'._datedaytime_get($ustamp).'</time>',
			__('alteration:page_changed', KEEP_EMPTY_KEYWORDS)
		);
	}*/
	if (isset($userid)) {
		if (!empty($info['MUSERID'])
		&&	$info['MUSERID'] === $userid
		) {
			if ($flags & SHOW_ALL_ALTER) {
				$note = str_replace(
					'%DATETIME%',
					'<time itemprop="dateCreated" datetime="'.gmdate(ISO8601Z,$info['CSTAMP']).'">'._datedaytime_get($info['CSTAMP']).'</time>',
					__('alteration:added_by_'.($userid ? 'user' : 'anonymous'), DO_UBB | KEEP_EMPTY_KEYWORDS, [
						'USERID'	=> $userid,
					])
				);
				if ($info['MSTAMP'] && $info['MSTAMP'] !== $info['CSTAMP']) {
					$note .= '<br />'.str_replace(
						'%DATETIME%',
						'<time'.$datemod.' datetime="'.gmdate(ISO8601Z,$info['MSTAMP']).'">'._datedaytime_get($info['MSTAMP']).'</time>',
							$info['MUSERID'] !== $userid
						?	__('alteration:lastchange_by_user', DO_UBB | KEEP_EMPTY_KEYWORDS, [
								'MUSERID'	=> $info['MUSERID']
							])
						:	__('alteration:lastchange', KEEP_EMPTY_KEYWORDS)
					);
				}
			} else {
				$note = str_replace(
					['%DATETIME%','%CDATETIME%'],
					[	'<time'.$datemod.' datetime="'.gmdate(ISO8601Z,$info['MSTAMP']).'">'._datedaytime_get($info['MSTAMP']).'</time>',
						'<time'.$datecrt.' datetime="'.gmdate(ISO8601Z,$info['CSTAMP']).'">'._datedaytime_get($info['CSTAMP']).'</time>',
					],
					__('alteration:added_and_lastchange_by_user', DO_UBB | KEEP_EMPTY_KEYWORDS, [
						'USERID'	=> $userid,
					])
				);
			}
		} elseif (!empty($info['MUSERID'])) {
			$note = str_replace(
				['%DATETIME%','%CDATETIME%'],
				[	!empty($info['MSTAMP']) ? '<time'.$datemod.' datetime="'.gmdate(ISO8601Z,$info['MSTAMP']).'">'._datedaytime_get($info['MSTAMP']).'</time>' : '',
					!empty($info['CSTAMP']) ? '<time'.$datecrt.' datetime="'.gmdate(ISO8601Z,$info['CSTAMP']).'">'._datedaytime_get($info['CSTAMP']).'</time>' : '',
				],
				__('alteration:added_and_other_lastchange', DO_UBB | KEEP_EMPTY_KEYWORDS, [
					'USERID'	=> $userid,
					'MUSERID'	=> $info['MUSERID']
				])
			);
		} elseif (!empty($info['CSTAMP'])) {
			if (!empty($info['MSTAMP']) && $info['MSTAMP'] !== $info['CSTAMP']) {
				$note = str_replace(
					'%DATETIME%',
					'<time'.$datemod.' datetime="'.gmdate(ISO8601Z,$info['MSTAMP']).'">'._datedaytime_get($info['MSTAMP']).'</time>',
					__('alteration:added_and_lastchange_by_system', DO_UBB | KEEP_EMPTY_KEYWORDS, [
						'USERID'	=> $userid,
					])
				);
			} else {
				$note = str_replace(
					'%DATETIME%',
					'<time'.$datecrt.' datetime="'.gmdate(ISO8601Z,$info['CSTAMP']).'">'._datedaytime_get($info['CSTAMP']).'</time>',
					__('alteration:added_by_user', DO_UBB | KEEP_EMPTY_KEYWORDS, [
						'USERID'	=> $userid
					])
				);
			}
		} else {
			$note = __('alteration:added_nodatetime',DO_UBB,array('USERID'=>$userid));
		}
	} elseif (
		!empty($info['MUSERID'])
	||	!empty($info['MSTAMP'])
	) {
		$srcs = ['%DATETIME%'];
		$reps = ['<time'.$datemod.' datetime="'.gmdate(ISO8601Z,$info['MSTAMP']).'">'._datedaytime_get($info['MSTAMP']).'</time>'];

		if (!empty($info['CSTAMP'])) {
			$srcs[] = '%CDATETIME%';
			$reps[] = '<time'.$datecrt.' datetime="'.gmdate(ISO8601Z,$info['CSTAMP']).'">'._datedaytime_get($info['CSTAMP']).'</time>';
		}
		$note =	str_replace(
			$srcs,
			$reps,
			!empty($info['MUSERID'])
			?	__('alteration:lastchange_by_user', DO_UBB | KEEP_EMPTY_KEYWORDS, [
					'MUSERID'	=> $info['MUSERID'],
				])
			:	__('alteration:lastchange', KEEP_EMPTY_KEYWORDS)
		);
	}
	if (!empty($lastlineup)) {
		[$lmstamp, $lmuserid] = keyval($lastlineup);
		$args['USERID'] = $lmuserid;

		$add_note = __('alteration:lineup_last_changed_by_LINE',DO_UBB,[
			'USERID'	=> $lmuserid,
			'DATETIME'	=> _datedaytime_get($lmstamp)
		]);

		if ($note) {
			$note .= '<br />';
		}
		$note .= $add_note;
	}
	if (!isset($no_pagemod)
	&&	$ustamp
	) {
		$note .= '<br />'.str_replace(
			'%DATETIME%',
			'<time itemprop="dateModified" datetime="'.gmdate(ISO8601Z,$ustamp).'">'._datedaytime_get($ustamp).'</time>',
			__('alteration:page_changed', KEEP_EMPTY_KEYWORDS)
		);
	}
	if (!($note = preg_replace('"<br(?:\s*/)?>$"', '', $note))) {	// NOSONAR (string is latin1 not utf8)
		return false;
	}
	?><footer class="note"><?
	if (null === ($new_note = preg_replace_callback(
		'"{SHOWSTARS\s+(\d+)}"',
		static fn(array $match): string => $contactstars ? $contactstars->get_stars($match[1]) : '',
		$note))
	) {
		preg_failure($note);
	} else {
		$note = $new_note;
	}
	echo $note;
	?></footer><?
	return true;
}

function get_action_open_close_for_setting() {
	$args = func_get_args();
	$action = $args[0];
	if (isset($args[2])) {
		[, $hash, $setting] = $args;
	} else {
		$setting = $args[1];
		$hash = $action;
	}
	if (setting($setting)
	&&	$_REQUEST['ACTION'] === 'single'
	) {
		return ['<a href="#'.$hash.'">','</a>'];
	}
	return get_action_open_close($action,$hash);
}

function get_action_open_close($action,$arg = null,$title = null): array {
	if ($arg) {
		if (is_number($arg)) {
			$hash = $action;
		} else {
			$hash = $arg;
			$arg = 0;
		}
	} else {
		$hash = $action;
	}
	if (!$_REQUEST['sID']) {
		return [null, null];
	}
	if ($_REQUEST['ACTION'] !== $action
	||	$arg
	&&	is_number($arg)
	&&	(	!$_REQUEST['subID']
		||	$arg !== $_REQUEST['subID']
		)
	) {
		return ['<a'.($title ? ' title="'.$title.'"' : null).' href="'.($link = '/'.$_REQUEST['sELEMENT'].'/'.$_REQUEST['sID'].'/'.$action.($arg ? '/'.$arg : null).'#'.$hash).'">','</a>',$link];
	} else {
		return ['<a'.($title ? ' title="'.$title.'"' : null).' class="selected" href="#'.$hash.'">','</a>','#'.$hash];
	}
}

/* function show_current_action(?int $elementid = null): void {
 	if ($action = get_current_action($elementid)) {
 		?><!-- (--><?php //= $action ?><!--)--><?//
 	}
 }*/

function get_current_action(?int $elementid = null): string {
	$parts = [];
	if ($base = get_current_base_action($elementid)) {
		$parts[] = $base;
	}
	switch ($_REQUEST['ACTION']) {
	case 'ages':
		if ($age = have_idnumber($_REQUEST,'AGE')) {
			$parts[] = element_name('age').': '.$age;
		}
		break;
	case 'buddycity':
	case 'visitcity':
	case 'fancity':
	case 'maybecity':
		if (($cityid = $_REQUEST['subID'])
		&&	($title = get_element_title('city',$cityid))
		) {
			$parts[] = /*element_name('city').': '.*/escape_utf8($title);
		}
		break;
	case 'photos':
		if (($partyid = have_idnumber($_REQUEST,'PARTYID'))
		&&	($title = get_element_title('party',$partyid))
		) {
			$parts[] = escape_specials($title);
		}
		break;
	}
	if ($page = get_current_page()) {
		$parts[] = $page;
	}
	return implode(', ',$parts);
}
function get_current_page() {
	if (!isset($_REQUEST['PAGE'])) {
		return null;
	}
	switch ($_REQUEST['PAGE']) {
	default:
		if ($page = have_idnumber($_REQUEST,'PAGE')) {
			return element_name('page').' '.$page;
		}
		break;
	case 'last':
		return __('pagecontrols:most_recent');
	case 'all':
		return element_name('all');
	}
	return null;
}
function get_current_base_action(?int $elementid = null): ?string {
	if ($elementid) {
		switch ($_REQUEST['sELEMENT']) {
		case 'user':
			switch ($_REQUEST['ACTION']) {
			case 'archive':		return element_name('agenda_archive');
			case 'comment':
			case 'comments':	return element_name('guestbook');
			case 'text':		return element_name('user_text');
			}
			break;
		case 'artist':
			if ($_REQUEST['ACTION'] === 'archive') {
				return element_name('performance_archive');
			}
			break;
		case 'city':
		case 'country':
		case 'location':
		case 'organization':
		case 'province':
			if ($_REQUEST['ACTION'] === 'archive') {
				return element_name('agenda_archive');
			}
			break;
		}
		if ($_REQUEST['ACTION'] === 'comment'
		||	$_REQUEST['ACTION'] === 'condolence'
		) {
			mail_log('singular '.$_REQUEST['ACTION'].' as ACTION');
		}
		switch ($_REQUEST['ACTION']) {
		case 'single':
		case 'showtopic':
			return null;

		case 'all':
		case 'ages':
		case 'archive':
		case 'artists':
		case 'buddies':
		case 'buddymap':
		case 'buddycity':
		case 'camera':
		case 'cities':
		case 'columns':
		case 'comment':
		case 'comments':
		case 'condolence':
		case 'condolences':
		case 'contenders':
		case 'contests':
		case 'diagram':
		case 'employees':
		case 'everdoubters':
		case 'fans':
		case 'fancity':
		case 'fanmap':
		case 'favourites':
		case 'flocks':
		case 'inactivebuddies':
		case 'info':
		case 'interviews':
		case 'locations':
		case 'meetings':
		case 'maybevisitors':
		case 'maybecity':
		case 'maybemap':
		case 'needupdates':
		case 'news':
		case 'new':
		case 'openedtopics':
		case 'operations':
		case 'organizations':
		case 'photocomments':
		case 'photos':
		case 'presaleinfo':
		case 'prices':
		case 'promos':
		case 'rating':
		case 'ratings':
		case 'reports':
		case 'requestform':
		case 'reviews':
		case 'searchresult':
		case 'secrets':
		case 'settings':
		case 'shoots':
		case 'topics':
		case 'unaccepted':
		case 'users':
		case 'videos':
		case 'videoshoots':
		case 'visitcity':
		case 'visitmap':
		case 'visitors':
		case 'votes':
		case 'weblog':
		case 'sequence':			return __('part:'.$_REQUEST['ACTION']);
		case 'biography':
		case 'information':			return __('element:'.$_REQUEST['ACTION']);
		case 'trackit':				return __('action:follow');
		case 'trackoff':			return __('action:unfollow');
		case 'markread':			return __('status:read');
		case 'combine':				return __('status:combined');
		case 'combinewith':			return __('action:combine');
		case 'accept':				return __('status:accepted');
		case 'remove':				return __('status:removed');
		case 'form':				return __('action:change');
		case 'aliasoverview':		return element_name('alias_overview');

		case 'commentoverview':
			$str = __('part:'.$_REQUEST['ACTION']);
			if ($_REQUEST['SUBACTION']) {
				require_once '_comment.inc';
				if (is_commentable($_REQUEST['SUBACTION'])) {
					$str .= ', '.element_plural_name($_REQUEST['SUBACTION'].'_comment');
				}
			}
			return $str;

		case 'forummessageoverview':
			$str = __('part:'.$_REQUEST['ACTION']);
			if ($forumid = $_REQUEST['subID']) {
				if ($forum = memcached_forum($forumid)) {
					$str .= ', '.escape_utf8($forum['NAME']);
				}
			}
			return $str;

		case 'flockmessageoverview':
			$str = __('part:'.$_REQUEST['ACTION']);
			if ($flockid = $_REQUEST['subID']) {
				if ($name = memcached_flockname($flockid)) {
					$str .= ', '.escape_utf8($name);
				}
			}
			return $str;

		case 'commit':
			return	__(
				$_REQUEST['sID']
			&&	!strncmp($_SERVER['REQUEST_URI'],'/'.$_REQUEST['sELEMENT'].'/commit',1+strlen($_REQUEST['sELEMENT'])+7)
			?	'status:added'
			:	'status:changed'
			);

		}
	} else {
		if ($_REQUEST['subID']
		&&	$_REQUEST['sELEMENT'] === 'poll'
		&&	$_REQUEST['ACTION'] === 'user'
		) {
			return ' '.__('attrib:of_user', DO_UBBFLAT, ['USERID' => $_REQUEST['subID']]);
		}
		switch ($_REQUEST['sELEMENT']) {
		case 'party':
			switch ($_REQUEST['ACTION']) {
			case 'photographers':	return element_plural_name('photographer');
			case 'buddies':			return element_plural_name('buddy');
			case 'favourites':		return element_plural_name('favourite');
			}
			break;
		case 'forum':
		case 'flock':
			switch ($_REQUEST['ACTION']) {
			case 'news':			return element_plural_name('new_topic');
			case 'active':			return element_plural_name('active_topic');
			case 'busy':			return element_plural_name('busy_topic');
			}
			break;
		}
		switch ($_REQUEST['ACTION']) {
		case 'form':
		case 'register':			return __($_REQUEST['sELEMENT'] === 'user' ? 'action:signup' : 'action:add');
		case 'outgoing-form':		return element_name('outgoing_form');
		case 'searchresult':		return element_plural_name('search_result');
		case 'search':				return __('action:search');
		case 'login':				return __('action:login');
		case 'setuser':				return element_name('login_result');
		case 'logoff':				return __('status:logged_out');
		case 'unaccepted':			return element_plural_name('non-validated');
		case 'conversation':		return element_name('conversation');
		case 'conversations':		return element_plural_name('conversation');
		case 'commit':				return $_REQUEST['sELEMENT'] === 'msg' ? __(isset($_POST['PREVIEW']) ? 'element:preview' : 'status:sent') : __('status:changed');
		case 'comments':			return element_plural_name('comment');
		case 'pastweekend':			return element_name('past_weekend');
		case 'active':				return __('status:active');
		case 'intrecent':			return __('status:interesting');
		case 'beach':				return element_plural_name('beach_party');
		case 'news':				return element_name($_REQUEST['ACTION']);
		case 'interestingevents':	return element_plural_name('interesting_event');
		case 'scancodes':			return element_plural_name('ticket');
		case 'winmsg':				return element_plural_name('won_contest');
		case 'personalnote':		return element_name('personalnote');
		case 'options':				return element_plural_name('option');
		case 'top-events':			return element_plural_name('top_event');
		}
	}
	return null;
}

function get_page_title_parts(): array {
	$element = $_REQUEST['sELEMENT'];
	$id		 = $_REQUEST['sID'];

	# ob_start: ENTIRE_TITLE
	ob_start();

	# ob_start: TITLE
/*	ob_start();
			if (SERVER_SANDBOX) {	?>TESTSERVER <?
	}   elseif (SERVER_VIP) {		?>vi<?
	}
	?>partyflock<?
	$prefix = ob_get_clean();*/
	# ob_end: TITLE

	if (function_exists('get_title_parts')
	&&	($info = get_title_parts())
	) {
		[$elem, $title] = $info;
	} else {
		require_once '_meta.inc';
		[$title, $desc] = get_meta();
		$elem = null;
		if (!$title)
		switch ($_REQUEST['ACTION'] === 'single' ? $element : '') {
		case 'contest':
			$title = escape_utf8(get_element_title('contest', $id));
			break;

		case 'organization':
			if (!memcached_organization($id)) {
				break;
			}
			$title = __('metad:organization:title', DO_UBBFLAT, ['ORGANIZATIONID' => $id]);
			break;

		case 'location':
			if (!($location = memcached_location_info($id))) {
				break;
			}
			$title = __('metad:location:title', DO_UBBFLAT, [
				'LOCATIONID'	=> $id,
				'CITYID'		=> $location['CITYID'],
			]);
			break;

		case 'artist':
			if (!($artist = memcached_single_assoc('artist','
				SELECT TYPE, GENDER
				FROM artist
				WHERE TYPE != ""
				  AND ARTISTID = '.$id)
			)) {
				break;
			}
			require_once 'defines/artist.inc';

			$is_group = artist_type_marked_group($artist['TYPE']);

			$types = explode_to_hash(',', $artist['TYPE']);

			unset($types['group']);

			require_once '_artist.inc';
			sort_artist_types($types, $id);

			require_once '_artist_types.inc';
			foreach ($types as &$type) {
				$artist_type = get_artist_type($type, $artist['GENDER'], $is_group);

				$type = html_entity_decode($artist_type);
			}
			unset($type);

			$title = __('metad:artist:title',DO_UBBFLAT,[
				'ARTISTID' 	=> $id,
				'FUNCTION'	=> implode(', ', $types)
			]);
			break;

		case 'party':
			if (!($party = memcached_party_and_stamp($id))) {
				break;
			}
			$name = $party['NAME'];
			$year = _getdate($party['STAMP_TZI'], 'UTC')[0];
			if (str_contains($party['NAME'],	 $year)
			||	str_contains($party['SUBTITLE'], $year)
			) {
				$year = null;
			}
			# FIXME: Once translations are in UTF-8, NAME, SUBTITLE and CITY can be
			#		 supplied as parameters to the translation function.
			$title = str_replace(
				[
					'%NAME%',
					'%SUBTITLE%',
					'%CITY%'
				], [
					escape_utf8($name),
				 	escape_utf8($party['SUBTITLE']),
				 	escape_utf8($party['CITY_NAME'])
				 ],
				 __('metad:party:title', DO_UBBFLAT | KEEP_EMPTY_KEYWORDS, [
					'LINEUP'	=> $party['HAVE_LINEUP'],
					'TIMETABLE'	=> $party['TIMETABLE_RELEASED'],
					'MAP'		=> $party['HAVE_MAP'],
					'YEAR'		=> $year,
			]));
			error_log('TITLE here: '.$title);
			break;
		}
		if (empty($title)) {
			if (!$_REQUEST['sID']
			&&	$_REQUEST['sELEMENT'] === 'user'
			&&	(	($signup = $_REQUEST['ACTION'] === 'form')
				||	$_REQUEST['ACTION'] === 'login'
				)
			) {
				# passwd manager catches nice title
				$title = 'Partyflock '.__($signup ? 'action:signup' : 'action:login');
				error_log('TITLE: '.$title);
				return ["<title>$title</title>", $title, ''];
			}
			# ob_start: TITLE 2
			ob_start();
			if (function_exists('display_title')) {
				display_title();
			}
			if ($element !== 'home' || !ob_get_length()) {
				show_default_title();
			}
			$rest = ob_get_clean();

			# ob_end: TITLE 2
			if (str_contains($rest, ':')) {
				$semis = substr_count($rest, ':');
				$semisperpart = ($semis - 1) / 2;

				$endpartre = '([^:]*?'.str_repeat(':[^:]*?', (int)round($semisperpart)).')';

				if (preg_match('"^\s*(.*)\s*:\s*'.$endpartre.'\s*$"', $rest, $match)) {
					[, $elem, $title] = $match;
				} else {
					# should not happen
					mail_log('title with colon not matched for processing', get_defined_vars());
					[$elem, $title] = explode(':', $rest, 2);
				}
				#$title = mytrim($title);
			} else {
				#$elem = mytrim($rest);
				$elem = $rest;
				$title = null;
			}
		}
	}

	?><title><?
	ob_start();

	global $commitresult;
	if ($commitresult === false) {
		?><?= __('status:failed') ?>! <?
	}

	$page = !$_REQUEST['ACTION'] ? get_current_page() : null;

	if (!is_int($_REQUEST['sID'])) {
		# NOTE: This problem should have been fixed.
		#		(require_idnumber_trim was called with sID as field, and the trim would cause a conversion of an integer value to string)
		mail_log('sID is no integer', [
			'defined_vars'	=> get_defined_vars(),
			'_REQUEST'		=> $_REQUEST,
		]);
		$_REQUEST['sID'] = (int)$_REQUEST['sID'];
	}

	if ($here_action
	=	$_REQUEST['ACTION']
	?	get_current_action($_REQUEST['sID'])
	:	''
	) {
		echo $here_action;
		?> <?= MIDDLE_DOT_ENTITY ?> <?
	}
	if ($title) {
		echo $title;
	}
	if ($elem) {
		if ($title) {
			?> <?= MIDDLE_DOT_ENTITY ?> <?
		}
		echo $elem;
	}
	if ($page) {
		if ($title || $elem) {
			?> <?= MIDDLE_DOT_ENTITY ?> <?
		}
		echo $page;
	}
	echo ob_get_clean() ?: ($title = 'Partyflock');
	?></title><?
	return [ob_get_clean(), $title, $here_action];
}

function show_default_title() {
	require_once '_elementnames.inc';
	global $__singular;
	$element = $_REQUEST['sELEMENT'];
	$display_element = get_display_element($_REQUEST['sELEMENT']);
	if ($__singular) {
		$realname_utf8 = false;
		switch ($singlar_element = ($id = $_REQUEST['sID']) ? $element : null) {
		case 'artist':
			if (!($have = memcached_single_array('artist','
				SELECT TYPE, NAME, IF(NAME != REALNAME, REALNAME, NULL), GENDER
				FROM artist
				WHERE ARTISTID = '.$id,
				TEN_MINUTES))
			) {
				$types = [];
			} else {
				[$types, $name, $realname, $gender] = $have;
				if (($realname ? preg_replace('"[^\p{L}\p{N}]+"u', '', $realname) : null)
				===	($name ?	 preg_replace('"\W+"u',	 		  '', $name)	 : null)
				) {
					# no need to display both if they're the same
					$realname = null;
				}
				$realname_utf8 = true;
				$types = $types ? explode_to_hash(',', $types) : [];
			}
			$result = [];
			if (isset($types['group'])) {
				unset($types['group']);
				# don't show group type for bands:
				$is_group = false;
				foreach ($types as $tmptype) {
					if (artist_type_is_group($tmptype)) {
						$is_group = true;
						break;
					}
				}
				if (!$is_group) {
					# show group at end:
					$types['group'] = 'group';
				}
			}
			foreach ($types as $type) {
				$result[$type] = __('artisttype:'.$type, ['GENDER' => $have ? $gender : 'male']);
			}
			require_once '_artist.inc';
			sort_artist_types($result, $id);

			$title = implode(', ', $result);
			if (in_array('dj', $types, true)
			&&	!empty($name)
			) {
				$title .= ' '.MIDDLE_DOT_ENTITY.' DJ '.escape_utf8($name);
			}
			break;

		case 'region':
		case 'city':
			if ($_REQUEST['ACTION'] === 'single') {
				$title = element_name('party_agenda');
				echo $title.' '.escape_utf8(get_element_title($singlar_element, $id));
				return;
			}
			$title = null;
			break;

		case 'invoice':
			$title = __('element:invoice').' '.$id;
			break;

		default:
			$title = __('element:'.$display_element, RETURN_FALSE | (have_admin() ? FORCE_READ : 0));
			break;
		}
		echo $title ?: escape_specials(element_name($display_element));
		if ($title = get_current_title()) {
			?>: <?
			if ($singlar_element === 'topic') {
				global $__currtopic;
				/** @noinspection NotOptimalIfConditionsInspection */
				if ($__currtopic
				&&	$__currtopic['FLAGS']
				&&	(require_once '_topictypes.inc')
				&&	($longprefix = get_topic_longprefix($element, $id, $__currtopic['FLAGS']))
				) {
					echo escape_specials($longprefix) ?>: <?
				}
			}
			echo isset(USE_UNICODE[$element]) ? escape_utf8($title) : escape_specials($title);
			if (!empty($realname)
			||	(	$element === 'user'
				&&	($info = memcached_single_array(['user','user_account'],'
					SELECT NAME, STATUS
					FROM user
					JOIN user_account USING (USERID,NICK)
					WHERE NAME != ""
					  AND NAME != NICK
/*					  AND NICK NOT LIKE CONCAT("%",NAME,"%")*/
					  AND VISIBILITY_NAME = 0
					  AND USERID = '.$id))
				&&	(	$info[1] !== 'deleted'
					&&	$info[1] !== 'permbanned'
					||	have_admin(['user','helpdesk'])
					)
				&&	preg_match('"^[\w\-]+(?:\s+[\w\-]+)*$"', $realname = $info[0])
				)
			) {
				?> <?= MIDDLE_DOT_ENTITY ?> <?
				if ($realname_utf8) {
					echo escape_utf8(utf8_ucfirst($realname));
				} else {
					echo escape_specials(ucfirst($realname));
				}
			}
		}
		if ($singlar_element === 'translation'
		&&	$_REQUEST['sID']
		&&	($keyname = memcached_single('translationkey','
				SELECT KEYNAME 
				FROM translationkey
				WHERE KEYID = '.$_REQUEST['sID']
		))) {
			?>: <? echo escape_specials($keyname);
		}
	} else {
		$title = __('elements:'.$display_element, RETURN_FALSE | (have_admin() ? FORCE_READ : 0));
		echo $title ?: escape_specials(element_plural_name($display_element));

		if ($_REQUEST['ACTION'] === 'archive') {
			?> <?= MIDDLE_DOT_ENTITY ?> <?
			echo element_name('archive');
			if ($_REQUEST['ssID']) {
				?> <?
				echo _month_name($_REQUEST['ssID']);
			}
			if ($_REQUEST['subID']) {
				?> <?
				echo $_REQUEST['subID'];
			}
		}
	}
}

function show_comment_counter(
	int $counter,
	?string $link = null,
	?string $title = null
) {
	show_bg_overlay(
		text: (string)$counter,
		style: 'left:5px;top:5px',
		class: 'bold ovbg',
		link: $link,
		title: $title
	);
}

function show_overlay(
	string	$text,
	?string	$style	= null,
	?string	$class	= null,
	?string	$link	= null,
	?string	$title	= null,
	bool	$fill_background = false
): void {
	?><div<?
	if ($link) {
		$class .= ' ptr';
		?> onclick="openLink(event,'<?= $link ?>')"<?
	} else {
		$class .= ' novents';
	}
	if ($title) {
		?> title="<?= $title ?>"<?
	}
	?> class="<?= $fill_background ? 'ovbg' : 'ov' ?> z2 abs <?= $class ?> <?= $class ?>"<?
	if ($style) {
		?> style="<?= $style ?>"<?
	}
	?>><?= $text ?></div><?
}

function show_bg_overlay(
	string $text,
	?string $style = null,
	?string $class = null,
	?string $link = null,
	?string $title = null
): void {
	show_overlay($text, $style, $class, $link, $title, fill_background: true);
}

function show_element_menuitems(?array $object = null) {
	require_once '_removable.inc';
	require_once '_combinable.inc';

	$element = $_REQUEST['sELEMENT'];
	$id		 = $_REQUEST['sID'];

	if (isset(COMBINABLE_ELEMENTS[$element])
	&&	have_admin("{$element}_combine")
	) {
		layout_menuitem(__C('action:combine'), "/$element/$id/combinewith");
	}
	if (isset(REMOVABLE_ELEMENTS[$element])
	&&	have_admin(["{$element}_remove", "{element}_remove_unaccepted"])
	) {
		layout_open_menuitem();
		if (empty($object['REMOVED'])) {
			if (!element_always_removable($element)) {
				include_style('bubble');
				include_js('js/bubble');
				include_js('js/checkremovable');
				include_js('js/form/confirm');
				?><div class="ib"><?
				?><span<?
				?> class="unhideanchor light"<?
				?> onmouseover="check_removable(this, '<?= $element ?>', <?= $id ?>);"><?= __C('action:remove') ?></span><?
				?></div><?
			} else {
				with_confirm(
					__C('action:remove'),
					"/$element/$id/remove",
					__($element.':confirm:removal_LINE')
				);
			}
		} else {
			with_confirm(
				__C('action:restore'),
				"/$element/$id/restore",
				__($element.':confirm:restoration_LINE')
			);
		}
		layout_close_menuitem();
	}
}

function show_options(string|int|null $selected, array $options): void {
	foreach ($options as $value => $name) {
		?><option<?
		if ($selected === $value) {
			?> selected<?
		}
		?> value="<?= $value ?>"<?
		?>><?
		if ($name) {
			echo $name;
		}
		?></option><?
	}
}

function show_columns(array $lines, int $columns): void {
	$size = count($lines);
	$percol = ceil($size / $columns);
	$i = 0;
	?><div class="vtop"><?
	foreach ($lines as $line) {
		if (!($i % $percol)) {
			$col = $i / $percol;
			if ($i) {
				?></div> <?
			}
			?><div class="vtop ib"><?
			if ($col !== $columns - 1) {
				?> <?
			}
		}
		echo $line;
		++$i;
	}
	?></div><?
	?><div class="clear"></div><?
	?></div><?
}

function max_file_size_surpassed(string $table, string $field, int $size): bool {
	$spec = explain_table($table);
	if (!$spec
	||	empty($spec['DATA'])
	||	!property_exists($spec['DATA'], 'maxlength')
	) {
		return true;
	}
	$max_size = $spec['DATA']->maxlength;
	if ($size > $max_size) {
		register_error('require:file:error_2_LINE',DO_CONDITIONAL,['MAX_SIZE' => get_human_bytes($max_size)]);
		return true;
	}
	return false;
}

function show_max_file_size_input($arg,string $field, ?int $absolute_maximum = null): void {
	# Force max size 0 if explain_table fails, as we cannot properly throw an exception to stop offering the form.
	#
	# Fields starting from mediumblob or bigger, need not be checked with this, because the upload_max_size is lower than what can be stored
	# in the medium- or largeblob fields.
	$spec = is_array($arg) ? $arg : explain_table($arg);
	if (empty($spec[$field])
	||	!property_exists($spec[$field], 'maxlength')
	) {
		$max_size = 0;
	} else {
		$max_size = $spec[$field]->maxlength;
		if ($absolute_maximum !== null
		&&	$absolute_maximum < $max_size
		) {
			$max_size = $absolute_maximum;
		}
	}
	?><input<?
	?> type="hidden"<?
	?> name="MAX_FILE_SIZE"<?
	?> value="<?= $max_size ?>"<?
	?> /><?
}

function show_gps(array $args = []): void {
	if (isset($args['name'])
	&&	$args['name'] === 'LATITUDE'
	) {
		$max = 90;
	} else {
		$max = 180;
	}
	show_input($args + [
		'class'			=> 'gps',
		'type'			=> 'number',
		'data-valid'	=> 'lnglat',
		'min'			=> -$max,
		'max'			=> $max,
		'step'			=> 'any'
	]);
}

function show_textarea_utf8(array $args): void {
	show_textarea($args, true);
}

function show_textarea(array $args, ?bool $utf8 = false): void {
	if (isset($args['class'])
	&&	str_contains($args['class'], 'growToFit')
	) {
		$rows = 2;
		if (!empty($args['value'])) {
			$body = is_array($args['value']) ? $args['value'][$args['name']] : $args['value'];
			$rows = max(2, substr_count($body, "\n") + 1);
		}
		$args['rows'] = $rows;
	}
	?><textarea<?

	if (isset($args['utf8'])) {
		mail_log('utf8 set in show_textarea', get_defined_vars());
		$utf8 ??= $args['utf8'];
		unset($args['utf8']);
	}

	$body_utf8 = $utf8;

	foreach ($args as $key => $val) {
		switch ($key) {
		case 'spec':
			show_spec(is_array($val) ? $val[$args['name']] : $val, $args);
			continue 2;

		case 'value_escaped':
			$escaped_body = $val;
			break;

		case 'value_utf8':
			$body_utf8 = true;
			# fall through

		case 'value':
			if (is_array($val)) {
				$body = $val[$args['name']] ?? null;
			} else {
				$body = $val;
			}
			continue 2;
		}
		if (is_bool($val)) {
			if (!$val) {
				continue;
			}
			$val = $key;
		}
		?> <?
		if (!$key) {
			mail_log('key is empty', get_defined_vars());
		} elseif ('_' === $key[0]) {
			mail_log('_escaped key indication value is already escaped', get_defined_vars(), error_log: 'WARNING');
			echo substr($key,1) ?>="<?= $val ?>"<?
		} else {
			echo $key, '="',escape_specials($val),'"';
		}
	}
	?>><?
	if (isset($escaped_body)) {
		echo $escaped_body;
	} elseif (isset($body)) {
		echo escape_specials($body, $body_utf8 ?? $utf8 ?? detect_utf8($body));
	} else {
		mail_log('no body', get_defined_vars());
	}
	?></textarea><?
}

function show_input_with_label(
	bool	$checked = false,
	?string	$hilited = null,
	?string	$base	 = null,
	?array	$input	 = null,
	?string	$text	 = null,
#	?string	$id	 = null,
): void {
	?><label<?
	if ($hilited
	||	$base
	) {
		?> class="<?= ($checked ? '' : 'not-'), $hilited, ' ', $base ?>"<?
	}
/*	if ($id) {
		?> id="<?= $id ?>"<?
	}*/
	?>><?
	if ($hilited) {
		if (!isset($input['class'])) {
			$input['class'] = 'upLite';
		} elseif (!str_contains($input['class'], 'upLite')) {
			$input['class'] .= ' upLite';
		}
	}
	$input['checked'] = $checked;
	show_input($input);
	if ($text) {
		echo ' ', $text;
	}
	?></label><?
}

function get_input(array $args): string {
	ob_start();
	show_input($args);
	return ob_get_clean();
}

function show_input(array $args): void {
	if (isset($args['type'])
	&&	$args['type'] === 'email'
	) {
		foreach (['onkeyup', 'onchange'] as $handler) {
			# fix dirty Facebook ?__xts__ and potentially other nasty stuff
			$args[$handler] = isset($args[$handler]) ? 'Pf.cleanEmail(this); '.$args[$handler] : 'Pf.cleanEmail(this);';
		}
	}
	?><input<?
	foreach ($args as $key => $val) {
		if ($val === null
		||	$val === false
		) {
			continue;
		}
		if ($utf8 = str_ends_with($key, '_utf8')) {
			$key = substr($key, 0, -5);
			$utf8 = true;
		}
		switch ($key) {
		case 'checked':
		case 'required':
		case 'disabled':
			if (!$val
			||	is_array($val)
			&&	empty($val[$args['name']])
			) {
				continue 2;
			}
			$val = true;
			break;

		case 'value_escaped':
		case 'value':
			if (is_array($val)
			&&	(	!isset($val[$args['name']])
				||	false === ($val = $val[$args['name']])
			)) {
				continue 2;
			}
			if (!$val
			&&	$val !== ''
			) {
				# $val === 0
				continue 2;
			}
			break;

/*		NOTE: As far as I can tell, js/formv/validation.js uses the value of 'type'
			  as data-valid value if data-valid does note exist, making these additions
			  below, not needed.

		case 'type':
			if (!isset($args['data-valid'])) {
				switch ($val) {
				case 'email':
				case 'number':
				case 'url':
					?> data-valid="<?= $val ?>"<?
					break;
				}
			}
			break;*/

		case 'spec':
			show_spec(is_array($val) ? $val[$args['name']] : $val, $args);
			continue 2;
		}
		if ($val === false) {
			continue;
		}
		?> <?
		if ($val === true) {
			# value could be the same as the key, but using 'true' looks nicer
			echo $key, '="true"';
		} elseif ($key[0] === '_') {
			# value already escaped
			echo substr($key, 1), '="', $val, '"';
		} elseif (str_ends_with($key, '_escaped')) {
			echo substr($key, 0, -8), '="', $val, '"';
		} else {
			echo $key, '="', escape_specials($val, $utf8), '"';
		}
	}
	?> /><?
}

function show_hidden_input(string $name, mixed $value): void {
	if (is_bool($value)) {
		if (!$value) {
			return;
		}
		$value = '1';
	}
	?><input<?
	?> type="hidden"<?
	?> name="<?= $name ?>"<?
	?> value="<?= $value ?>" /><?
}

function show_spec(attrib_spec $spec, array $args): void {
	if ( isset($spec->maxlength)
	&&	!isset($args['maxlength'])
	) {
		?> maxlength="<?= $spec->maxlength ?>"<?
	}
	if (HOME_THOMAS) {
		if (isset($spec->min)
		&&	!isset($args['min'])
		) {
			?> min="<?= $spec->min ?>"<?
		}
		if (isset($spec->max)
		&&	!isset($args['max'])
		) {
			?> max="<?= $spec->max ?>"<?
		}
	}
}

function show_select(
	 string $key,
	 string $table,
	 string $field,
	?string $id			= null,
	?string $selected	= null,
	?string	$prefix		= null,
): void {
	if (!($spec = explain_table($table))) {
		return;
	}
	$prefix ??= $table.':'.strtolower($field).':';
	?><select name="<?= $key ?>"><?
	if (reset($spec[$field]->enum)) {
		# Check the spec, make sure we only show one empty option
		?><option value=""></option><?
	}
	foreach ($spec[$field]->enum as $value) {
		?><option<?
		if ($selected === $value) {
			?> selected<?
		}
		?> value="<?= escape_ascii($value) ?>"><?
		if ($value) {
			echo __($prefix.$value);
		}
		?></option><?
	}
	?></select><?
}

function show_publication(array|int|null $item = null): void {
	$direct =
		!$item
	||	is_array($item)
	&&	$item['CSTAMP'] === $item['PSTAMP'];

	?><select name="DIRECT" onchange="<?
	if (!is_array($item)) {
		?>setdisplay(this.nextSibling, this.selectedIndex);<?
	} else {
		?>setdisplay(this.nextSibling, !this.selectedIndex);<?
		?>setdisplay(this.nextSibling.nextSibling, this.selectedIndex);<?
	}
	?>"><?
		?><option value="1"><?= __('publication:direct') ?></option><?
		?><option value="0"<?
		if (!$direct) {
			?> selected="selected"<?
		}
		?>><?= __('form:select:other_option') ?>&hellip;</option><?
	?></select><?
	if (is_array($item)) {
		?><span class="<?
		if (!$direct) {
			?>hidden <?
		}
		?>lmrgn">&rarr; <? _datedaytime_display($item['CSTAMP']) ?></span><?
	}
	?><span class="<?
	if ($direct) {
		?>hidden <?
	}
	?>lmrgn"><?
	_datetime_display_select_stamp(
		is_array($item)
	?	$item['PSTAMP']
	:	($item ?: TODAYSTAMP)
	);
	?></span><?
}

function expandable_box_header(
	string	$title,
	string	$id,
	?string	$link = null
): void {
	expandable_header($title, $id, $link, true);
}

function expandable_header(
	string	$title,
	string	$id,
	?string	$link				= null,
	bool	$box				= false,
	?string	$after_click_title	= null
): void {
	if (!$link) {
		include_js('js/expand');
	}
	ob_start();
	if ($id) {
		expand_collapse($id);
	}
	$expandy = ob_get_clean();
	?><div<?
	if ($link) {
		?> style="cursor: crosshair !important;"<?
	}
	?> class="ptr uncollapser unhideanchor"<?
	if ($after_click_title) {
		?> data-other-click-title="<?= $after_click_title ?>"<?
		$title = '<span id="expandy-'.$id.'-title">'.$title.'</span>';
	}
	?> onclick="<?
	if ($link) {
		?>openLink(event, '<?= escape_specials($link) ?>');<?
	} else {
		expand_collapse_clicker($id);
	}
	?>"><?
	if ($box) {
		layout_box_header($title, $expandy);
	} else {
		?><div class="r"><?= $expandy ?></div><?
		echo $title;
	}
	?></div><?
}

function expand_collapse_clicker(string $id): void {
	include_js('js/expand');
	?>Pf.collapseClicker(event, this, '<?= $id ?>');<?
}

function expand_collapse(string $part, ?string $class = null): void {
	?><div onclick="<?=
			'event.stopPropagation(event);',
			'let part = getobj(\''.$part.'\', true);',
			'if(!part) return;',
			'swapdisplay(part);',
			'let current_html = this.innerHTML;',
			'this.innerHTML = this.getAttribute(\'data-other\');',
			'this.setAttribute(\'data-other\', current_html);'
	?>"<?
	?> id="expandy-<?= $part ?>"<?
	?> class="<? if ($class) { echo $class, ' '; } ?>ptr"<?
	?> data-other="<?= BLACK_UP_POINTING_TRIANGLE_ENTITY ?>"><?= BLACK_DOWN_POINTING_TRIANGLE_ENTITY ?></div><?
}

function get_remove_char(?array $arg = null): string {
	return get_special_char('remove', $arg);
}

function get_close_char(?array $arg = null): string {
	return get_special_char('close', $arg);
}

function get_special_char(string $type, ?array $arg = null): string {
	require_once '_hosts.inc';

	ob_start();
	require_once '_browser.inc';
	switch ($type) {
	case 'change':
		$type = 'pencil';
		?><span class="spc2 colorhover"><?
		?><img<?
		?> title="<?= $title = __('action:change') ?>"<?
		?> alt="<?= $title /*, LOWER_RIGHT_PENCIL_ENTITY */ ?>"<?
		?> class="icon"<?
		?> src="<?= STATIC_HOST ?>/images/<?= $type,is_high_res() ?>.png" /><?
		?></span><?
		return ob_get_clean();

	case 'close':
		$size = 16;
		if (!empty($arg['class'])) {
			if (str_contains($arg['class'],'large')) {
				$size = 32;
				$classes[] = 'large';
			} elseif (str_contains($arg['class'],'medium')) {
				$size = 24;
				$classes[] = 'medium';
			}
		}
		if (is_high_res()) {
			$size *= 2;
		}
		$icon = 'cchar'.$size;
		$title = 'action:close';
		$high = null;

	case 'remove':
		if (!isset($icon)) {
			$icon = 'karmadown_active';
			$title = 'action:remove';
			$high = is_high_res();
		}
	case 'search':
		if (!isset($icon)) {
			$icon = 'magnifyr';
			$title = 'action:search';
			$high = is_high_res();
		}
	case 'rem':
		if (!isset($icon)) {
			$icon = 'karmadown_active';
			$title = 'action:remove';
			$high = is_high_res();
		}

	case 'add':
		if (!isset($icon)) {
			$icon = 'karmaup_active';
			$title = 'action:add';
			$high = is_high_res();
		}
		$classes[] = 'icon';
		if (isset($arg['class'])) {
			$classes[] = $arg['class'];
		}

		?><span<?
		if (!empty($arg['label'])
		&&	!empty($arg['onclick'])
		) {
			?> onclick="<?= $arg['onclick'] ?>"<?
			unset($arg['onclick']);
		}
		?> class="<?= $type === 'close' || $type === 'remove' ? '' : 'lght ' ?>spc2 colorhover"><?
		?><img<?
		?> title="<?= $title = ($arg['title'] ?? null) ?: __($title) ?>"<?
		?> alt="<?= $title ?>"<?
		?> class="<?= implode(' ',$classes) ?>"<?
		?> src="<?= STATIC_HOST ?>/images/<?= $icon,$high ?>.png"<?
		if ($arg) {
			unset($arg['class'],$arg['title'],$arg['alt']);
			foreach ($arg as $key => $val) {
				if ($key === 'label') {
					continue;
				}
				if ($val === false) {
					continue;
				}
				echo ' ',$key,'="',$val,'"';
			}
		}
		?> /><?
		if (!empty($arg['label'])) {
			?><span><?= $arg['label'] ?></span><?
		}
		?></span><?
		return ob_get_clean();
	}
	?><div<?

	$arg['title'] ??= __('action:'.$type);
	$class = 'spc';

	switch ($type) {
	case 'close':	$char = VECTOR_OR_CROSS_PRODUCT_ENTITY;		$class .= ' cchar'; break;
	case 'remove':	$char = VECTOR_OR_CROSS_PRODUCT_ENTITY;		$class .= ' removeicon'; break;
	case 'change':	$char = BLACK_UNIVERSAL_RECYCLING_ENTITY;	$class .= ' recycleicon'; break;
	case 'add':		$char = PLUS_SIGN_ENTITY;					$class .= ' addicon'; break;
	}
	if ($label = getifset($arg,'label')) {
		unset($arg['label']);
	}
	if (isset($arg['class'])) {
		$arg['class'] .= ' '.$class;
	} else {
		$arg['class'] = $class;
	}
	foreach ($arg as $key => $val) {
		echo ' ',$key,'="',$val,'"';
	}
	?>><div><?= $char ?></div><?
	if ($label) {
		?><span><?= $label ?></span><?
	}
	?></div><?
	return ob_get_clean();
}
function link_to_message($element,$parentelement,$parentid,$message) {
	if (!isset($message['MSGNO'])) {
		return false;
	}
	$comments = null;
	$msgnoffset = 0;
	$collapsed = false;
	switch ($element) {
	case 'contact_ticket_message':
		# FIXME: do linking to ticket
		$pagesize = 0;
		break;

	case 'privatemessage':
	case 'directmessage':
		# FIXME: do linking to conversation and right page
		break;

	case 'message':
	case 'flockmessage':
		$pagesize = setting('MESSAGES_PER_TOPIC');
		$msgnoffset = -1;
		break;
	default:
		if (!$parentelement) {
			require_once '_comment.inc';
			$parentelement = is_comment_table($element);
		}
		if (empty($message['MSGCNT'])) {
			require_once '_commentsinfo.inc';
			if ($commentsinfo = get_commentsinfo($parentelement, $parentid)) {
				$message['MSGCNT'] = $commentsinfo[CMTI_MSGCNT];
			}
		}
		require_once '_settings.inc';
		$pagesize = setting('COMMENTS_PER_PAGE');

		if (!$parentelement) {
			mail_log('parentelement is empty');
		}

		$collapsed = !(
			(	$parentelement === 'user'
			||	$parentelement === 'deaduser'
			)
		&&	setting('SHOW_USER_GUESTBOOK')
		||	setting('UNCOLLAPSE_COMMENTS') & (1 << constant('CMT_'.strtoupper($parentelement)))
		);
		if ($parentelement === 'deaduser') {
			$comments = '/condolences';
			$parentelement = 'user';
		} else {
			$comments = '/comments';
		}
		break;
	}
	if ($message['MSGNO'] !== true) {
		$message['MSGNO'] += $msgnoffset;
	}
	$msgid = $message['MESSAGEID'] ?? $message['COMMENTID'] ?? null;
	if (!$msgid) {
		mail_log('no MESSAGEID or COMMENTID for link_to_message', item: get_defined_vars());
	}
	if (!$message['MSGCNT']) {
		# hope this works without MSGCNT:
		$quotelink = '/'.$parentelement.'/'.$parentid.$comments.'/page/'.(ceil($message['MSGNO'] / $pagesize) ?: 1);
		mail_log('MSGCNT is not set in link_to_message', item: get_defined_vars());
	} else {
		if ($comments
		&&	$message['MSGCNT'] <= setting('PAGED_COMMENTS_STARTING_FROM')
		||	!$pagesize
		||	$message['MSGNO'] > $message['MSGCNT'] - $pagesize
		) {
			$quotelink = !$collapsed ? get_element_href($parentelement,$parentid) : '/'.$parentelement.'/'.$parentid.$comments;
		} else {
			$quotelink = '/'.$parentelement.'/'.$parentid.$comments.'/page/'.(ceil($message['MSGNO'] / $pagesize) ?: 1);
		}
	}
	$quotelink .= '#m'.$msgid;

	if (!empty($_SERVER['eLANG'])) {
		$quotelink = '/'.$_SERVER['eLANG'].$quotelink;
	}
	return $quotelink;
}

function transparent_image(int $width, int $height): string {
	scale($width, $height, 2048, 2048);
	if (!$width) {
		$width = 1;
	}
	if (!$height) {
		$height = 1;
	}

	if (!($data = memcached_get($key = 'trans:'.$width.'x'.$height))) {
		$img = new Imagick;
		$img->newImage($width, $height, 'none', 'png');
		$data = $img->getImageBlob();
		memcached_set($key, $data, ONE_DAY);
	}
	return escape_specials('data:image/png;base64,'.base64_encode($data));
}

function black_pixel() {
	return 'data:image/gif;base64,R0lGODlhAQABAIABAAAAAP///yH+EUNyZWF0ZWQgd2l0aCBHSU1QACwAAAAAAQABAAACAkQBADs=';
}

# NOTE: $option means no HTML, plain text for <option> tag contents
function get_dead(string|array|true $item, ?int $id = null, bool $option = false): string {
	ob_start();
	show_dead($item, $id, $option);
	return ob_get_clean();
}
function show_dead_option(string|array|true $item, ?int $id = null): void {
	show_dead($item, $id, true);
}
function show_dead(string|array|true $item, ?int $id = null, bool $option = false): void {
	if (is_string($item)) {
		if (!$id) {
			return;
		}
		if ($item === 'location') {
			/** @var array $item */
			if (!($item = memcached_location_info($id))) {
				return;
			}
		} elseif ($item === 'organization') {
			/** @noinspection RedundantSuppression, CallableParameterUseCaseInTypeContextInspection */
			if (!($item = db_single_assoc('organization', "
				SELECT DEADSTAMP, FOLLOWUPID
				FROM organization
				WHERE ORGANIZATIONID = $id"))
			) {
				return;
			}
		} else {
			return;
		}
	}
	if ($item === true
	||	empty($item['FOLLOWUPID'])
	&&	(	!empty($item['DEAD'])
		||	!empty($item['DECEASED'])
		||	!empty($item['DEADSTAMP'])
		)
	) {
		?> <?
		if ($option) {
			echo DAGGER_ENTITY;
		} else {
			?><span<?
			if (!empty($item['DEADSTAMP'])
			&&	$item['DEADSTAMP'] !== 1
			) {
				require_once '_date.inc';
				?> title="<?= __C('organizationlist:ceased', ['DATE' => _date_get($item['DEADSTAMP'])]) ?>"<?
			}
			?> class="dead"><?= DAGGER_ENTITY ?></span><?
		}
	} elseif (
		!empty($item['STOPPED'])
	||	!empty($item['FOLLOWUPID'])
	) {
		require_once 'defines/artist.inc';
		?> <?
		if ($option) {
			echo ARTIST_STOP_SIGN;
		} else {
			?><span class="stopped"><?= ARTIST_STOP_SIGN ?></span><?
		}
	}
	if (!empty($item['REMOVED'])
	||	!empty($item['DELETED'])
	) {
		?> <span class="deleted"><?= ERASE_TO_THE_LEFT_ENTITY ?></span><?
	}
}

function show_middle_header(string $title, ?string $extra = null): void {
	// BLACKBOX
	?><div class="hdr"><?
		?><div class="title"><?= $title ?></div><?
		if ($extra) {
			?><div class="info"><?= $extra ?></div><?
			?><div class="clear"></div><?
		}
	?></div><?
}

/** @noinspection PhpUnusedParameterInspection  */
function show_column_list(array $groups, int $max_rows): void {
	/** @noinspection SuspiciousAssignmentsInspection */
	// Just show all in one column
	$max_rows = 100; // NOSONAR
	?><div class="block"><?
	foreach ($groups as $hdr => $items) {
		?><div class="bold"><?= $hdr ?></div><?
		$count = count($items);
		$cols = ceil($count / $max_rows);
		$rows_per_col = $count <= $max_rows ? $count : floor($count / $cols);
		$left = $rows_per_col;
		foreach ($items as $li) {
			if ($left === $rows_per_col) {
				?><div class="ib vtop" style="margin-right: 1em;"><?
				?><ul><?
			}
			?><li><?= $li ?></li><?
			--$left;
			if (!$left) {
				?></ul><?
				?></div><?
				$left = $rows_per_col;
			}
		}
		if ($left !== $rows_per_col) {
			?></ul><?
			?></div><?
		}
	}
	?></div><?
}

function show_gender(string $gender, string $prefix = ' '): void {
	if (!$gender
	||	$gender === '?'
	) {
		return;
	}
	echo $prefix;
	if ($gender === 'multi') {
		?>mix<?
	} else {
		?><span class="<?=
			$gender === 'F'
		||	$gender === 'female'
		?	'female'
		:	'male'
		?>"><?= $gender === 'F'
		||	$gender === 'female'
		? 	'&#9792;'
		:	'&#9794;'
		?></span><?
	}
}
function show_photographers(
	?array				$user_links	= null,
	string|array|null	$extra_name	= null,
	?string				$extra_site	= null,
): void {
	if (null !== ($userid = $user_links['USERID'] ?? null)) {
		$anonymous = !empty($user_links['ANONYMOUS']);
		$user_links =
			$userid
		&&	(	!$anonymous
			||	CURRENTUSERID === $userid
			||	have_admin(['camerarequest', 'photo', 'gallery'])
			)
		?	[$userid =>
				'<a href="'.get_element_href('user',$userid).'">'.
				escape_specials(get_element_title('user',$userid)).
				'</a>'.
				($anonymous ? ' ('.__('field:anonymous').')' : '')]
		:	null;
	}
	if (is_array($extra_name)) {
		$extra_site = $extra_name['PHOTOGRAPHER_SITE']	?? null;
		$extra_name = $extra_name['PHOTOGRAPHER']		?? null;
	}
	/** @noinspection NotOptimalIfConditionsInspection */
	if (1 === ($size = $user_links ? count($user_links) : 0)
	&&	$extra_name
	) {
		$userid = array_key_first($user_links);
		$user_is_extra = !strcasecmp(win1252_to_utf8(get_element_title('user', $userid)), $extra_name);
	} else {
		$user_is_extra = false;
	}
	ob_start();
	if ($user_links) {
		asort($user_links);
		foreach ($user_links as /* $userid => */ $data) {
			echo $data;
			--$size;
			if ($size) {
				?>, <?
			}
		}
		if (!$user_is_extra
		&&	$extra_name
		) {
			?>, <?
		}
	}
	if ($extra_name
	&&	(!$user_is_extra || $extra_site)
	) {
		?> <?
		if ($extra_site) {
			?><a target="_blank" href="<?= escape_specials($extra_site) ?>"><?
		}
		if ($user_is_extra) {
			?><span class="siteimg"><img alt="" src="<?= STATIC_HOST ?>/images/website<?= is_high_res() ?>.png" /></span><?
		} else {
			echo escape_utf8($extra_name);
		}
		if ($extra_site) {
			?></a><?
		}
	}
	$authorstr = ob_get_clean();
	?><small><?
	$photography  = element_name('photography');
	$photographer = element_name('photographer');
	if (!mb_stripos($authorstr, $photography)
	&&	!mb_stripos($authorstr, $photographer)
	) {
		echo $photography; ?>: <?
	}
	echo $authorstr;
	?></small><?
}
function show_views_restart_row(int $min = 0): void {
	show_views_row($min, restart: true);
}
function show_views_row(int $min = 0, bool $restart = false): void {
	$element = $_REQUEST['sELEMENT'];
	$id		 = $_REQUEST['sID'];

	if (ROBOT
	||	(	!have_admin()
		&&	$element === 'party'
		)
	) {
		return;
	}
	if ($element === 'party') {
		$info = memcached_single_array('party_view','
			SELECT SUM(HITS), UNIX_TIMESTAMP(FROM_DAYS(MIN(DAYNUM)))
			FROM party_view
			WHERE PARTYID = '.$id,
			TEN_MINUTES
		);
		if ($party = memcached_party_and_stamp($id)) {
			$appic_views = memcached_single('appic_event','
				SELECT UNIQUE_VIEWS
				FROM appic_event
				WHERE PARTYID = '.$party['MAIN_ID']
			);
		}
	} else {
		$info = memcached_single_array('artist_counter',"
			SELECT SUM(VIEWS), UNIX_TIMESTAMP(FROM_DAYS(MIN(DAYNUM)))
			FROM {$element}_counter
			WHERE {$element}ID = $id",
			TEN_MINUTES
		);
	}
	if (!$info) {
		return;
	}
	[$views, $stamp] = $info;

	if ($views
	&&	$views > $min
	) {
		$restart ? layout_restart_reverse_row() : layout_start_reverse_row();
		echo $views;
		layout_value_field(SEPARATOR_MULTIPLIER);
		echo __('stats:viewed');
		if (!empty($appic_views)) {
			?> <? echo get_partyflock_icon('colorless');
		}
		?> <?
		?><span class="light"><?=
			__('date:since_date', ['DATETIME' => _date_get($stamp)])
		?></span><?
		if (!$restart) {
			layout_stop_row();
		}
	}
	if (!empty($appic_views)) {
		$restart ? layout_restart_reverse_row() : layout_start_reverse_row();
		echo $appic_views;
		layout_value_field(SEPARATOR_MULTIPLIER);
		echo __('stats:scrolled_along');
		?> <?= get_appic_icon('colorless') ?> <span class="light"><?= __('attrib:unique') ?></span><?
		if (!$restart) {
			layout_stop_row();
		}
	}
}
function invite_registration(string $invite, ?string $text_key = null): void {
	if (ROBOT) {
		return;
	}
	if ($text_key) {
		echo __($text_key); ?> <?
	}
	echo __('invite:login_or_register_LINE',DO_UBB,['INVITE' => $invite]);
}
function show_gender_symbol(array $user): void {
	require_once '_visibility.inc';
	?><span class="gendersym<?
	if ($gender =
		(	$user['SEX'] === 'M'
		||	$user['SEX'] === 'F'
		)
	&&	_visibility($user, 'GENDER')
	?	$user['SEX']
	:	false
	) {
		?> <? echo $gender === 'M' ? 'male' : 'female';
	}
	?>"><?
	if ($gender) {
		echo $gender === 'M' ? '&#x2642;' : '&#x2640';
	}
	?></span><?
}
function get_to_end_icon(): string {
	ob_start();
	?><img class="to-end" src="<?= STATIC_HOST ?>/images/to_end_<?= CURRENTTHEME ?>_<?= is_high_res() ? '32' : '16' ?>.png" /><?
	return ob_get_clean();
}
function get_user_indicator_icon(): string {
	ob_start();
	?><img class="to-user" src="<?= STATIC_HOST ?>/images/user_indicator_<?= is_high_res() ? '32' : '16' ?>_<?= CURRENTTHEME ?>.png" /><?
	return ob_get_clean();
}
function load_monospace_font(): void {
	include_style('font_firacode');
	?><link rel="preload" crossorigin as="font" type="font/woff2" href="<?= STATIC_HOST ?>/fonts/firacode/v22/uU9NCBsR6Z2vfE9aq3bh3dSDqFGedA.woff2" /><?
}
function load_fonts(): void {
	require_once '_hosts.inc';
	switch(setting('FONT_TYPE')) {
	case 'Titillium Web':
		include_style('font_titillium');
		# most used:
		?><link rel="preload" crossorigin as="font" type="font/woff2" href="<?= STATIC_HOST ?>/fonts/titilliumweb/v6/NaPDcZTIAOhVxoMyOr9n_E7ffGjEGItzY5abuWI.woff2" /><?
		?><link rel="preload" crossorigin as="font" type="font/woff2" href="<?= STATIC_HOST ?>/fonts/titilliumweb/v6/NaPecZTIAOhVxoMyOr9n_E7fdMPmDaZRbrw.woff2" /><?
		?><link rel="preload" crossorigin as="font" type="font/woff2" href="<?= STATIC_HOST ?>/fonts/titilliumweb/v6/NaPDcZTIAOhVxoMyOr9n_E7ffHjDGItzY5abuWI.woff2" /><?
		break;

	case 'Raleway':
		include_style('font_raleway');
		# most used fonts:
		?><link rel="preload" crossorigin as="font" type="font/woff2" href="<?= STATIC_HOST ?>/fonts/raleway/v12/1Ptrg8zYS_SKggPNwIYqWqZPANqczVs.woff2" /><?
		?><link rel="preload" crossorigin as="font" type="font/woff2" href="<?= STATIC_HOST ?>/fonts/raleway/v12/1Ptug8zYS_SKggPNyC0IT4ttDfA.woff2" /><?
		?><link rel="preload" crossorigin as="font" type="font/woff2" href="<?= STATIC_HOST ?>/fonts/raleway/v12/1Ptrg8zYS_SKggPNwIYqWqhPANqczVsq4A.woff2" /><?
		break;
	}
}

function show_same_facebook_connected(): void {
	$this_item = "
		ELEMENT	= '{$_REQUEST['sELEMENT']}'
		AND ID	= {$_REQUEST['sID']}";

	$other_items = db_rowuse_array('fbid', "
		SELECT ELEMENT, ID, GROUP_CONCAT(TYPE ORDER BY TYPE) AS TYPESTR
		FROM fbid
		WHERE NOT ($this_item)
		  AND ELEMENT NOT IN ('presence', 'user')
		  AND FBID IN (
			SELECT FBID
			FROM fbid
			WHERE $this_item
		)
		GROUP BY ELEMENT, ID"
	);

	if (!$other_items) {
		return;
	}
	?><div class="block"><b><?= get_facebook_icon(),' ',__C('item:info:same_facebook_connected_LINE') ?></b><br /><?
	?><ul><?
	foreach ($other_items as $item) {
		extract($item, EXTR_OVERWRITE);
		?><li><small class="light"><?= element_name($ELEMENT) ?>:</small> <?
		echo get_element_link($ELEMENT, $ID);
		if ($TYPESTR) {
			$types = [];
			foreach (explode(',', $TYPESTR) as $type) {
				if (!$type) {
					mail_log('huh, type is empty', item: get_defined_vars());
					continue;
				}
				$types[] = element_name($type);
			}
			?> <span class="light">(<?= implode(', ', $types) ?>)</span><?
		}
		?></li><?
	}
	?></ul><?
	?></div><?
}

function aspect_style(int $w, int $h): void {
	?> style="padding-top:calc(100%*<?= $h / $w ?>)" <?
}

function show_aspect_embed(
	array|string $src,
	int			 $aspect_w	= 16,
	int			 $aspect_h	= 9,
	int			 $width		= 0,
	int			 $height	= 0,
	?string		 $id		= null,
): void {
	if (is_array($src)) {
		[$lazy, $src] = $src;
	} else {
		$lazy = false;
	}
	if (!$src) {
		return;
	}
	if ($res = get_resolution()) {
		[,,,, $win_width, $win_height] = $res;
	}
	if (!$width) {
		if (SMALL_SCREEN
		&&	(	!$res
			#	^^ default
			||	$win_width < $win_height
			#	^^ landscape
			)
		) {
			$width = 100;
		} else {
			if ($res
			&&	$win_width
			&&	$win_height
			) {
				$max_height_part = 0.8; # percentage

				$use_width =  $win_width;
				$use_height = $win_height;

				$padding = 250; # menu, maybe tower

				$have_width = $win_width - $padding;

				if (!$have_width) {
					$have_width = 1;
				}

				$zero_width = !$have_width;

				scale($use_width, $use_height, $have_width);

				$max_height = (int)round($win_height * $max_height_part);

				scale($use_width, $use_height, 0, $max_height);

				$width = min(90, round(100 * $use_width / $have_width, 1));

				if ($zero_width) {
					mail_log('have_width is zero!', item: [
						'SMALL_SCREEN'	=> SMALL_SCREEN ? 'yes' : 'no',
						'variables'	=> get_defined_vars(),
						'_SERVER'	=> $_SERVER,
					]);
				}
			} else {
				$width = 80;
			}
		}
	}

	if ($lazy) {
		ob_start()
		?><iframe<?
		if ($id) {
			?> id="<?= $id ?>"<?
		}
		?> loading="lazy"<?
		?> allowfullscreen<?
		# YouTube includes this 'allow' parameter. Without it, first embed will fail with "Video not found"
		?> allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"<?
		?> referrerpolicy="strict-origin-when-cross-origin"<?
		?> src="<?= $src ?>"<?
		?>><?
		?></iframe><?
		$lazy_html = ob_get_clean();

		?><div class="lazycontainer centered" style="width: <?= $width ?>%;"><?
		?><div<?
		?> class="lazyload aspect-box"<? aspect_style($aspect_w,$aspect_h) ?><?
		?> data-lazy-html="<?= escape_utf8($lazy_html) ?>"><?
		?></div><?
		?></div><?
	} else {
		?><div class="centered" style="width: <?= $width ?>%;"><?
		?><div class="aspect-box"<? aspect_style($aspect_w, $aspect_h) ?>><?
		if ($src[0] !== '<'
		&&	!str_contains($src, '<iframe')
		) {
			?><iframe<?
			if ($id) {
				?> id="<?= $id ?>"<?
				?> onerror="console.log('trying to reload'); this.src = this.src;" <?
			}
			?> loading="lazy"<?
			?> allowfullscreen<?
			# YouTube includes this 'allow' parameter. Without it, first embed will fail with "Video not found"
			?> allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"<?
			?> src="<?= $src ?>"<?
			?>><?
			?></iframe><?
		} else {
			echo $src;
		}
		?></div><?
		?></div><?
	}
}

function get_mail_icon(bool $incoming = false, string $alt = ''): string {
	require_once '_elementnames.inc';
	require_once '_hosts.inc';
	ob_start();
	?><img<?
	?> class="<?= $incoming ? 'lower icon' : 'mail' ?>"<?
	?> src="<?= STATIC_HOST ?>/images/<?= $incoming ? 'incoming_' : '' ?>mail<?= is_high_res() ?>.png"<?
	?> alt="<?= $alt ?: Eelement_name($incoming ? 'incoming_mail' : 'mail') ?>"<?
	?> /><?
	return ob_get_clean();
}

function show_warning_icon(
	?string	$title	= null,
	?string	$id		= null,
	?string	$class	= null,
	?string	$width	= null,
): void {
	require_once '_browser.inc';
	$show_entity = HOME_OFFICE || iOS || MACOS;
	$classes = empty($class) ? [] : [$class];
	if ($show_entity) {
		$classes[] = 'emoji';
		$classes[] = 'lower';
		?> <span<?
		?> style="cursor: default; font-size: 120%; line-height: 0;"<?
	} else {
		$classes[] = 'vmiddle';
		?> <img<?
	}
	if (empty($title)) {
		$title = element_name('warning');
	}
	?> title="<?= $title ?>"<?
	if (!empty($id)) {
		?> id="<?= $id ?>"<?
	}
	?> class="<?= implode(' ', $classes) ?>"<?

	if (!$show_entity) {
		require_once '_hosts.inc';
		?> src="<?= STATIC_HOST ?>/warning/orange.png"<?
		?> style="width: <?= $width ?? '1em' ?>;" /><?
	} else {
		?>><?= WARNING_SIGN_EMOJI_ENTITY ?></span><?
	}
}

function get_camera_icon(?string $title = null): string {
	require_once '_elementnames.inc';
	require_once '_browser.inc';
	$title ??= element_plural_name('photo');
	ob_start();
	if (iOS >= 9
	||	ANDROID >= 5
	||	MACOS >= 10.11
	) {
		?><span<?
		?> title="<?= $title ?>"<?
		?> class="cam-icon"><?
			?><span><?= CAMERA_ENTITY ?></span><?
		?></span><?
	} else {
		require_once '_hosts.inc';
		?><img<?
		?> class="cam"<?
		?> title="<?= $title ?>"<?
		?> alt="<?= $title ?>"<?
		?> src="<?= STATIC_HOST ?>/images/blackcam<?= is_high_res() ?>.png" /><?
	}
	return ob_get_clean();
}

function get_videocamera_icon(?string $title = null): string {
	require_once '_elementnames.inc';
	require_once '_browser.inc';
	$title ??= element_plural_name('video');
	ob_start();
	if (iOS >= 9
	||	ANDROID >= 5
	||	MACOS >= 10.11
	) {
		?><span<?
		?> class="ib"<?
		?> title="<?= $title ?>"<?
		?> style="height: 1em; width: 1.5em;"><?
			?><span<?
			?> class="ib"<?
			?> style="font-size: 150%; position: absolute; margin-top: -.3em;"<?
			?>>&#x1f3ac;</span><?
		?></span><?
	} else {
		require_once '_hosts.inc';
		?><img<?
		?> class="cam"<?
		?> title="<?= $title ?>"<?
		?> alt="<?= $title ?>"<?
		?> src="<?= STATIC_HOST ?>/images/clapboard<?= is_high_res() ?>.png" /><?
	}
	return ob_get_clean();
}

function get_favicon(): string {
	require_once '_browser.inc';
	return '/favicon'.is_high_res().'.png';
}

function get_partyflock_icon(?string $class = null, string|false|null $title = null): string {
	$title ??= 'Partyflock';
	ob_start();
	?><img <?
	if ($title) {
		?> alt="<?= $title ?>"<?
		?> title="<?= $title ?>"<?
	}
	?> class="presence<?= $class ? ' '.$class : '' ?>"<?
	?> src="<?= get_favicon() ?>" /><?
	return ob_get_clean();
}

function show_favicon_link(): void {
	?><link rel="icon" href="<?= SERVER_SANDBOX ? STATIC_HOST."/{$_SERVER['servertype']}.png" : '/favicon.ico' ?>" type="image/x-icon" sizes="16x16 32x32" /><?
}

function get_ticket_icon(?string $class = null): string {
	require_once '_browser.inc';
	if (MACOS || iOS) {
		return $class ? '<span class="'.$class.'">&#127903;</span>' : '&#127903;';
	}
	require_once '_elementnames.inc';
	require_once '_hosts.inc';
	$title = Eelement_plural_name('ticket');
	ob_start();
	?><img<?
	?> alt="<?= $title ?>"<?
	?> title="<?= $title ?>"<?
	?> class="<?= $class ? $class.' ' : '' ?>presence"<?
	?> src="<?= STATIC_HOST ?>/images/admission_ticket.png" /><?
	return ob_get_clean();
}

function get_clock_icon(): string {
	return '&#128336;';
}

function show_map_icon(): void {
	require_once '_hosts.inc';
	?><img<?
	?> class="middle"<?
	?> width="22"<?
	?> height="22"<?
	?> src="<?= STATIC_HOST ?>/images/minimap<?= is_high_res() ?>.png" /><?
}

function get_appic_partyflock_icon(string|false|null $title = null): string {
	require_once '_hosts.inc';
	$title ??= element_plural_name('visitor');
	ob_start();
	?><img<?
	if ($title) {
		?> alt="<?= $title ?>"<?
		?> title="<?= $title ?>"<?
	}
	?> class="lower"<?
	?> style="width: 24px; height: 16px;"<?
	?> src="<?= STATIC_HOST ?>/presence/apf<?= is_high_res() ?>.png" /><?
	return ob_get_clean();
}

function get_appic_icon(?string $class = null, string|false|null $title = null): string {
	require_once '_hosts.inc';
	require_once '_browser.inc';
	$title ??= 'Appic';
	ob_start();
	?><img<?
	if ($title) {
		?> alt="<?= $title ?>"<?
		?> title="<?= $title ?>"<?
	}
	?> class="presence<?= $class ? ' '.$class : '' ?>"<?
	?> src="<?= STATIC_HOST ?>/presence/appic<?= is_high_res() ?>.png" /><?
	return ob_get_clean();
}

function get_pffb_icon(): string {
	require_once '_hosts.inc';
	$title = element_plural_name('visitor');
	ob_start();
	?><img<?
	?> alt="<?= $title ?>"<?
	?> title="<?= $title ?>"<?
	?> class="lower"<?
	?> style="width: 24px; height: 16px;"<?
	?> src="<?= STATIC_HOST ?>/presence/xb<?= is_high_res() ?>.png" /><?
	return ob_get_clean();
}

function get_ical_feed_link(?string $element = null, ?int $id = null): string {
	$id 	 ??= $_REQUEST['sID'];
	$element ??= $_REQUEST['sELEMENT'];
	ob_start();
	?><a<?
	?> href="<?
		?>webcal://<?= addslashes($_SERVER['HTTP_HOST']) ?>/<?= $element ?>_<?= $id,
		($element === 'user' ? '_'.hash('xxh128', $id.':'.CURRENTUSERID.':s3CR3t').'i'.CURRENTUSERID : '')
	?>.ics"<?
	?> title="<?=
		element_name('ical_feed') ?> <?=
	 	escape_specials(get_element_title($element, $id), utf8: isset(USE_UNICODE[$element]))
	?>">ical</a><?
	return ob_get_clean();
}

/*function get_info_char(): void {
	?><div class="ib relative"><div class="abs" style="top:-1em;font-size:175%">&#8505;</div></div><?
}*/
