<?php

function limit_nick_changes(int $userid, bool &$chatted = false) {
	$nick_stamp = CURRENTSTAMP - ONE_WEEK;
	if (!$userid
	||	have_admin()
	||	!($chatted = db_single('chatinfo','SELECT MAX(LAST_WORDS) FROM chatinfo WHERE USERID='.$userid) > $nick_stamp)
	) {
		return false;
	}
	$nicks = db_rowuse_array(['user_log','user'],'
		SELECT MSTAMP,NICK
		FROM user
		WHERE USERID='.$userid.'
		UNION
		SELECT MSTAMP,NICK
		FROM user_log
		WHERE USERID='.$userid.'
		 AND MSTAMP>'.$nick_stamp
	);
	if (!$nicks) {
		return false;
	}
	number_reverse_sort($nicks,'MSTAMP');
	$changes = 0;
	$prevnick = null;
	foreach ($nicks as $info) {
		$lowernick = preg_replace('"[^a-zA-Z]+"','',strtolower($info['NICK']));
		if (!isset($distincts[$lowernick])) {
			$distincts[$lowernick] = $info;
			$options[$info['NICK']] = $info;
		}
		if ($prevnick != $lowernick) {
			++$changes;
			$prevnick = $lowernick;
		}
	}
	$distinct_cnt = count($distincts);

	if ($distinct_cnt == 1) {
		return false;
	}
	if ($distinct_cnt > 1 && $changes > 2) {
		return true;
	}
	return $options;
}
function check_nick_limit($user) {
	$limit_nick_changes = limit_nick_changes($user['USERID']);
	$ok = false;
	if (!$limit_nick_changes) {
		$ok = true;
	} elseif ($limit_nick_changes === true) {
		if ($user['NICK'] == $_POST['NICK']) {
			$ok  = true;
		}
	} else {
		foreach ($limit_nick_changes as $info) {
			if ($info['NICK'] == $_POST['NICK']) {
				$ok = true;
				break;
			}
		}
	}
	if (!$ok) {
		error_log("WARNING may not change nick {$user['NICK']} to {$_POST['NICK']} for user {$user['USERID']}");
		$_POST['NICK'] = $user['NICK'];
		register_warning('user:warning:nick_limited_not_changed_LINE');
	}
}
function show_nick_limit_options($limit_nick_changes,$user) {
	if ($limit_nick_changes === true) {
		echo escape_specials($user['NICK']);
		?><input type="hidden" name="NICK" value="<?= escape_specials($user['NICK']) ?>" /><?
	} else {
		?><select name="NICK" required="required"><?
		foreach ($limit_nick_changes as $info) {
			?><option<?
			?> value="<?= $nickstr = escape_specials($info['NICK']) ?>"<?
			?>><?= $nickstr ?></option><?
		}
		?></select><?
	}
}
