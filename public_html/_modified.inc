<?php

declare(strict_types=1);

# NOTE: not modified if
#	IF_MODIFIED_SINCE >= $stamp AND IF_NONE_MATCH == $etag
#
#	set $tmp to true if resulting file is not definitive

function not_modified(
	 ?int	 $stamp	 = null,
	 ?string $etag	 = null,
	 bool	 $tmp	 = false,
): bool {
	$stamp_modified = null;
	 $etag_modified = false;
	if ($stamp) {
		header('Last-Modified: '.gmdate('D, d M Y H:i:s', $stamp).' GMT');

		if (isset($_SERVER['HTTP_IF_MODIFIED_SINCE'])) {
			if (false !== ($pos = strpos($if_modified_since = $_SERVER['HTTP_IF_MODIFIED_SINCE'], ';'))) {
				# some browsers put "; length=xxx" after date
				$if_modified_since = trim(substr($if_modified_since, 0, $pos));
			}
			if (false !== ($if_modified_stamp = strtotime($if_modified_since))) {
				assert(is_int($if_modified_stamp)); # Satisfy EA inspection
				# browser supplied stamp is older than what we want
				$stamp_modified = $if_modified_stamp < $stamp;
			} else {
				error_log('DEBU<PERSON> failed to parse HTTP_IF_MODIFIED_SINCE: '.$if_modified_since);
			}
		} else {
			# we have stamp, but browser dit not supply one
			$stamp_modified = true;
		}
	}
	$if_none_match = $_SERVER['HTTP_IF_NONE_MATCH'] ?? null;
	if ($etag) {
		if (str_contains($etag, '-')) {
			mail_log("ERROR etag contains dashes, which is not supported currently: $etag");
		}
			# No If-None-Match supplied, so ETag is always different:
		if (!$if_none_match
			# force-revalidate, so we see this always as etag difference:
		||	 $if_none_match === 'force-revalidate'
		) {
			$etag_modified = true;
		} else {
			if (preg_match('!^"?(?<etag>.*?)"?(?<compression>-[a-z]+)?"?$!', $if_none_match, $match)) {
				if (($compression = $match['compression'] ?? null)
				&&	!in_array($compression, ['-br', '-deflate', '-gzip', '-zstd'])
				) {
					error_log(
						"stripped unknown $compression ".
						"from If-None-Match: $if_none_match ".
						"for {$_SERVER['REMOTE_ADDR']} @ {$_SERVER['REQUEST_URI']}"
					);
				}
				$if_none_match = $match['etag'];
			}
			$etag_modified = ($if_none_match !== $etag);
		}
		if ($tmp) {
			# QUESTION: Is this useful here? Shouldn't the code calling not_modified(),
			#			do or don't add something like _tmp on temporary files.
			#			Why do this here?
			$etag .= '_tmp';
		}
		header('ETag: "'.$etag.'"');

	} elseif ($if_none_match) {
		# No ETag supplied by calling code, but browser sent If-None-Match.
		$etag_modified = true;
	}
	return !$stamp_modified && !$etag_modified;
}

function force_refresh(): bool {
	return	isset($_REQUEST['NOMEMCACHE'])
		&&	(	$_REQUEST['NOMEMCACHE'] === ''
			||	$_REQUEST['NOMEMCACHE']
			)
		||	!empty($_SERVER['HTTP_CACHE_CONTROL'])
		&&	(	str_contains($_SERVER['HTTP_CACHE_CONTROL'], 'max-age=0')
			||	str_contains($_SERVER['HTTP_CACHE_CONTROL'], 'no-cache')
			);
}
