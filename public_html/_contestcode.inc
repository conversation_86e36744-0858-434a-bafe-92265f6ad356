<?php

function confirm_codes(): int {
	if (!($contestid = require_idnumber($_POST,'CONTESTID'))) {
		return 400;
	}
	require_once '_currentuser.inc';
	global $currentuser;
	$currentuser = new _currentuser;
	$confirmed = isset($_POST['UNCONFIRM']) ? 0 : 1;
	if (!require_admin('contest')) {
		return 401;
	}
	if (!db_insert('contestcode_log', '
		INSERT INTO contestcode_log
		SELECT contestcode.*, '.CURRENTSTAMP.'
		FROM contestcode
		LEFT JOIN contestresponse USING (CONTESTID, USERID)
		WHERE CONFIRMED != '.$confirmed.'
		  AND (TOLD IS NULL OR TOLD = 0)
		  AND CONTESTID = '.$contestid)
	||	!db_update('contestcode','
		UPDATE contestcode
		LEFT JOIN contestresponse USING (CONTESTID, USERID)
		SET CONFIRMED = '.$confirmed.'
		WHERE CONFIRMED != '.$confirmed.'
		  AND (TOLD IS NULL OR TOLD = 0)
		  AND CONTESTID = '.$contestid)
	) {
		return 500;
	}
	if (!db_affected()) {
		_error('Winnaars zijn al ingelicht!');
		return 400;
	}
	show_contestcode_menu($contestid);
	return 200;
}

function show_contestcode_menu(int $contestid): void {
	if ('guestlist' !== db_single('contest', 'SELECT TYPE FROM contest WHERE CONTESTID = '.$contestid)) {
		return;
	}
	$codes = db_single_array(['contestcode', 'contestresponse'], '
		SELECT	COUNT(*),
			COUNT(IF((TOLD IS NULL OR TOLD = 0) AND CONFIRMED = 0, 1, NULL)),
			COUNT(IF((TOLD IS NULL OR TOLD = 0) AND CONFIRMED = 1, 1, NULL)),
			COUNT(IF(CONFIRMED = 1, 1, NULL))
		FROM contestcode
		LEFT JOIN contestresponse USING (CONTESTID, USERID)
		WHERE CONTESTID = '.$contestid,
		DB_USE_MASTER
	);
	if (!$codes || !$codes[0]) {
		return;
	}
	list(	$total_codes,
		$unconfirmed_but_changable_cnt,
		$confirmed_but_changable_cnt,
		$confirmed_cnt) = $codes;

	$ajax = isset($_SERVER['AJAX']);
	if (!$ajax) {
		layout_open_menuitem();
		include_js('js/form/contactcontest');
		?><div class="hidden" id="contestthanks" data-template="<?= __('template:contest_codes_thanks:body_TEXT') ?>"></div><?
		?><span id="contestcodesmenu"><?
	} else {
		require_once '_layout.inc';
	}
	if ($total_codes == $confirmed_cnt) {
		echo __('contest:info:all_codes_confirmed_LINE');
		if ($confirmed_but_changable_cnt) {
			?> <?= MIDDLE_DOT_ENTITY ?> <?
			with_confirm(
				__C('action:unconfirm_codes'),
				'Pf.confirmCodes',
				__('contest:confirm:unconfirm_codes_LINE'),
				data: ['CONTESTID'=>$contestid,'UNCONFIRM'=>1]
			);
		}
	} elseif ($unconfirmed_but_changable_cnt) {
		with_confirm(
			__C('action:confirm_codes'),
			'Pf.confirmCodes',
			__('contest:confirm:confirm_codes_LINE'),
			data: ['CONTESTID' => $contestid]
		);
	} else {
		echo __('contest:info:codes_not_confirmed_LINE');
	}
	if (!$ajax) {
		?></span><?
		layout_close_menuitem();
	}
}
