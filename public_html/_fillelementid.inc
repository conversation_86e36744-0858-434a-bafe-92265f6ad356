<?php

declare(strict_types=1);

function show_elementid_input(
	 string	 $element,
	 ?int	 $id	= null,
	 array	 $args	= [],
	 ?string $extra	= null,
): void {
	?><input<?
	if ($bad
	=	$id
	&&	$element === 'user'
	&&	($status = memcached_status($id)) !== 'active'
	&&	!isset($args['allow-bad'])
	) {
		?> data-bad="data-bad"<?
	}
	?> data-element="<?= $element ?>"<?
	?> type="number"<?
	?> class="right id"<?
	?> onchange="immediate_fillElement(this);<?
	if (isset($args['onchange'])) {
		echo $args['onchange'];
		unset($args['onchange']);
	}
	?>"<?
	if (isset($args['on_user'])) {
		?> data-on-user="<?= $args['on_user'] ?>"<?
		unset($args['on_user']);
	}
	?> onkeyup="delayed_fillElement(this);<?
	if (isset($args['onkeyup'])) {
		echo $args['onkeyup'];
		unset($args['onkeyup']);
	}
	?>"<?
	?> min="<?= $args['min'] ?? 0 ?>" <?
	unset($args['min']);

	if ($element) {
		if ($max_id = db_single($element, "SELECT MAX({$element}ID) FROM `$element`")) {
			?>max="<?= $max_id ?>" <?
		}
		if (!isset($args['placeholder'])) {
			$args['placeholder'] = element_name($element).' ID';
		}
	}
	foreach ($args as $key => $val) {
		if (is_bool($val)) {
			if ($val) {
				echo $key ?> <?
			}
			continue;
		}
		echo $key ?>="<?= $val ?>"<?
	}
	?> value="<? if ($id) { echo $id; } ?>"<?

	?>><span class="lmrgn"><?
	if ($id) {
		echo get_element_link($element, $id);
		if ($bad) {
			require_once '_status.inc';
			?> (<?
			/** @noinspection PhpUndefinedVariableInspection */
			echo status_name($status);
			if ($extra) {
				?>, <? echo $extra;
				$extra = null;
			}
			?>)<?
		}
		if ($element === 'party'
		&&	($party = memcached_party_and_stamp($id))
		) {
			$tz = $party['TIMEZONE'] ?: 'UTC';
			change_timezone($tz);
			?><small>, <? _date_display($party[$tz === 'UTC' ? 'STAMP_TZI' : 'STAMP'] + ($party['AT2400'] ? -1 : 0)) ?></small><?
			change_timezone();
		}
	}
	if ($extra) {
		?> (<?= $extra ?>)<?
	}
	?></span><?
	include_js('js/form/fillelementid');
}
