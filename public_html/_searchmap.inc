<?php

function show_search_map(
	string	$element,
	int		$id,
	?array	$item			= null,
	int		$zoom			= 8,
	# NOTE: Don't escape default_search elsewhere, it will be done here
	string	$default_search = '',
	bool	$update_mapsqf	= false,
): void {
	if (!empty($item['LATITUDE'])) {
		$lat = $item['LATITUDE'];
		$lng = $item['LONGITUDE'];
	} else {
		require_once '_countrylatlng.inc';
		[$lat, $lng] =
			!empty($item['CITYID'])
		&&	($city = db_single_array('city', 'SELECT LATITUDE,LONGITUDE FROM city WHERE CITYID = '.$item['CITYID']))
		?	$city
		:	get_country_latlon(1);
	}
	?><div id="searchmap"><?
	?><div<?
	?> style="height:<?= SMALL_SCREEN ? 150 : 400 ?>px; overflow: auto; resize:vertical; position:relative"<?
	?>><?
		?><div<?
		?> id="map"<?
		?> style="<?
			?>position:absolute; <?
			?>resize:none; <?
			?>left: 0; <?
			?>right: 0; <?
			?>top: 0; <?
			?>bottom: 1em;"<?
		?> data-update-mapsqf="<?= $update_mapsqf ? '1' : '0' ?>"<?
		?> data-ini-lat="<?= $lat ?>"<?
		?> data-ini-lon="<?= $lng ?>"<?
		?> data-ini-zoom="<?= $zoom ?>"></div><?
	?></div><?

	?><div style="position:relative;top:-.8em;height:1.5em"><?
	?><input<?
	?> onkeydown="return lookupAddress(this, event);"<?
	?> type="search"<?
	?> autosave="searchmap_address"<?
	?> id="mapsq"<?
	?> value="<?= escape_utf8($default_search) ?>"><?

	?><input<?
	?> id="mapsbutton"<?
	?> class="lmrgn"<?
	?> type="button"<?
	?> onclick="lookupAddress(this.previousSibling, event, true)"<?
	?> value="<?= __('action:search'); ?>"><?
	?></div><?

	# We want searchmap to be loaded before loading Google Maps JavaScript, because Google
	# wants to call the callback defined in searchmap to initialize the map.
	include_js('js/searchmap', JS_SYNC);
	include_google_maps_js($element, $id, 'search', callback: 'initMap');
	include_js('js/hovercheck');
	?></div><?
	?><div class="hidden warning"><?= __('js:error:google_maps_failed_init_LINE') ?></div><?
}
