<?php

require_once '_quote.inc';
require_once '_overview.inc';

class quoteoverview extends overview {
	protected $controls	= null;
	protected $comments	= array();
	function __construct($userid,$selected = null) {
		if (!($this->counts = memcached_simple_hash('quoted','
			SELECT SRCELEM, COUNT(DISTINCT SRCID)
			FROM quoted
			WHERE QOTUSERID = '.$userid.'
			GROUP BY SRCELEM
			WITH ROLLUP'
		))) {
			return;
		}
		ksort($this->counts);

		if ($selected === 'new' || $userid === CURRENTUSERID) {
			require_once '_quote.inc';
			$info = get_new_quotes($userid);
			if (!$info) {
				return;
			}
			[$total, $quoteseen]= $info;
			$this->newquotes = $total;
		}
		if ($selected !== 'new') {
			$total = isset($this->counts[$selected]) ? $this->counts[$selected] : 0;
		}
		require_once '_pagecontrols.inc';
		$this->userid = $userid;
		$this->selected = $selected;
		$this->controls = new _pagecontrols;
		$this->controls->set_total($total);
		$this->controls->set_per_page(50);
		$this->controls->set_url('/user/'.$userid.'/quoted'.($selected ? '/'.$selected : null));
		$this->controls->set_order('quoted.STAMP');

		if ($selected === 'new') {
			$flags = 0;
			if (memcached_get($key = 'newquotes:reload:'.$userid)) {
				$flags = DB_FRESHEN_MEMCACHE;
				memcached_delete($key);
			}
			$key = null;
			$quotes = memcached_multirow_hash(['quoted','quoteseen_element'],
				get_quote_query($userid,$quoteseen).
				$this->controls->order_and_limit(),
				0,$key,$flags
			);
		} else {
			$quotes = memcached_multirow_hash('quoted', '
				SELECT SRCELEM,SRCID
				FROM quoted
				WHERE QOTUSERID = '.$userid.
				($selected ? ' AND SRCELEM = "'.$selected.'"' : '').
				$this->controls->order_and_limit()
			);
		}
		if (!$quotes) {
			return;
		}
		$this->okcnt = 0;

		global $__refresh_key;
		$__refresh_key = 'qo:'.$userid.':'.crc32(igbinary_serialize($quotes));
		if ($stamp = memcached_get($__refresh_key)) {
			memcached_delete($__refresh_key);
			$flags = DB_FRESHEN_MEMCACHE;
		} else {
			$flags = 0;
		}

		foreach ($quotes as $srcelem => $srcids) {
			$key = null;
			$oksrcids = [];
			foreach ($srcids as $srcid) {
				$info = null;
				if (may_view_element($srcelem,$srcid)) {
					++$this->okcnt;
					$oksrcids[] = $srcid;
				}
			}
			if (!$oksrcids) {
				continue;
			}
			$srcids = $oksrcids;
			sort($srcids);

			if ($selected == 'new') {
				$selectstr = ',quoteseen_element.STAMP AS SEEN';
				$joinstr = '
					LEFT JOIN quoteseen_element ON
						quoteseen_element.USERID='.$userid.'
					AND quoteseen_element.ELEMENT='.$srcid.'
					AND quoteseen_element.ID='.($srcelem == 'message' || $srcelem == 'flockmessage' ? 'MESSAGEID' : 'COMMENTID');
			} else {
				$joinstr = $selectstr = null;
			}

			$this->init_karma($srcelem,$srcids);

			switch ($srcelem) {
			case 'flockmessage':
			case 'message';
				$parent = str_replace('message','topic',$srcelem);
				$intopic = true;
				$tmpquoted = memcached_rowuse_array([$srcelem, $parent],'
					SELECT	MESSAGEID, message.CSTAMP, message.MSTAMP, message.USERID, message.MUSERID,
							BODY, TOPICID, ACCEPTED, MSGNO,	message.FLAGS,
							t.FLAGS AS PARENT_FLAGS, FIRST_MESSAGEID, MSGCNT,
							'.($srcelem === 'message' ? 'FORUM' : 'FLOCK').'ID AS GRANDPARENTID'.
							$selectstr.'
					FROM '.$parent.' AS t
					JOIN '.$srcelem.' AS message USING (TOPICID)'.
					$joinstr.'
					WHERE ACCEPTED = 1
					  AND STATUS != "hidden"
					  AND MESSAGEID IN ('.implode(', ', $srcids).')',
					0,
					$key,
					$flags
				);
				if ($tmpquoted === false) {
					break;
				}
				if ($tmpquoted) {
					foreach ($tmpquoted as &$tmpquote) {
						$tmpquote['PARENT'] = $parent;
						$tmpquote['ELEMENT'] = $srcelem;
					}
					unset($tmpquote);
					$this->comments  = array_merge($tmpquoted,$this->comments);
				}
				break;
			default:
				require_once '_comment.inc';
				$parent = is_comment_table($srcelem);
				$tmpquoted = memcached_rowuse_array([$srcelem,$parent],'
					SELECT	COMMENTID,
							comment.CSTAMP, comment.MSTAMP, comment.USERID, comment.MUSERID,
							BODY, comment.ID, ACCEPTED, comment.FLAGS, MSGNO,
							comment.ID AS GRANDPARENTID, MSGCNT '.
							$selectstr.'
					FROM '.$srcelem.' AS comment
					LEFT JOIN commentsinfo ON TYPE = "'.$parent.'" AND commentsinfo.ID = comment.ID'.
					$joinstr.'
					WHERE COMMENTID IN ('.implode(', ', $srcids).')',
					0,
					$key,
					$flags
				);
				if ($tmpquoted === false) {
					break;
				}
				if ($tmpquoted) {
					foreach ($tmpquoted as &$tmpquote) {
						$tmpquote['PARENT'] = $parent;
						$tmpquote['ELEMENT'] = $srcelem;
					}
					unset($tmpquote);
					$this->comments = array_merge($tmpquoted,$this->comments);
				}
			}
		}
		if ($this->comments) {
			number_reverse_sort($this->comments,'CSTAMP');
		}
	}
}
