<?php

require_once '_gallery.inc';
require_once '_photographers.inc';
require_once '_photolist.inc';

function preamble(): void {
	switch ($_REQUEST['ACTION']) { // NOSONAR
	case 'single':
		# NOTE: Galleries don't support pages I think
		if (($galleryid = $_REQUEST['sID'])
		&&	isset($_REQUEST['PAGE'])
		) {
			see_other(get_element_href('gallery', $galleryid));
		}
		break;

	case 'year':
		# NOTE: Legacy redirector that was used by the archive jumper
		$new_uri = preg_replace('"\?subID=(\d+)$"', '/$1', $_SERVER['REQUEST_URI'], -1, $cnt);
		if ($cnt) {
			see_other($new_uri);
		}
	}

	if ($_REQUEST['sID']
	&&	($imgids = memcached_simpler_array('image', "
			SELECT IMGID
			FROM image
			JOIN gallery USING (GALLERYID)
			WHERE HIDDEN = 0
			  AND VISIBLE = 'yes'
			  AND GALLERYID = {$_REQUEST['sID']}
			ORDER BY IMGID",
			TEN_MINUTES)
		)
	&&	($imgids = memcached_simpler_array('image_counter_total','
			SELECT IMGID
			FROM image_counter_total
			WHERE IMGID IN ('.implode(', ', $imgids).')
			ORDER BY COUNTER DESC, RAND()',
			TEN_MINUTES)
		?:	$imgids
		)
	&&	($pop = memcached_single_array(['image','gallery'], "
			SELECT	IMGID, PARTYID, WIDTH, DOUBLE_WIDTH, HEIGHT, DOUBLE_HEIGHT,
					(SELECT 1 FROM user_appearance AS ua WHERE ua.IMGID = image.IMGID LIMIT 1) AS HAVE_APP
			FROM image
			JOIN gallery USING (GALLERYID, PARTYID)
			WHERE HIDDEN = 0
			  AND VISIBLE = 'yes'
			  AND GALLERYID = {$_REQUEST['sID']}
			ORDER BY /*NO_PERSONS DESC,*/
					 HAVE_APP,
					 FIELD(IMGID, ".implode(', ', $imgids).'),
					 IMGID
			LIMIT 1',
			TEN_MINUTES)
		)
	) {
		[$popid, /* $partyid */, $width, $double_width, $height, $double_height] = $pop;
		include_og('og:image',			get_photo_url($popid, 'main', ($double_width ? '@2x' : false)));
		include_og('og:image:width', 	$double_width	?: $width);
		include_og('og:image:height',	$double_height	?: $height);
	}
}

function display_title(): void {
	[$title] = get_gallery_and_title();
	echo $title;
}

function get_gallery_and_title(): array {
	static $__cache = null;
	if ($__cache) {
		return $__cache;
	}

	if (!($galleryid = have_idnumber($_REQUEST,'sID'))
	||	!($gallery = memcached_gallery($galleryid))
	) {
		return $__cache = [element_plural_name('party_photo'), false];
	}
	ob_start();
	$party = $gallery['party'];
	$gallery['NAME'] =
		!empty($party['NAME'])
	?	$party['NAME'].($gallery['TITLE'] ? ' '.$gallery['TITLE'] : '')
	:	$gallery['TITLE'];

	if ($_REQUEST['ACTION'] === 'comments') {
		echo element_plural_name('comment').' '.escape_utf8($gallery['NAME']);
	} elseif ($_REQUEST['ACTION'] === 'popularity') {
		echo element_name('popularity').' '.escape_utf8($gallery['NAME']);
	} else {
		echo str_replace('%NAME%', escape_utf8($gallery['NAME']), __('gallery:title'));
	}
	if (!empty($party['EDITION'])) {
		?> #<? echo $party['EDITION'];
	}
	if (!empty($party['SUBTITLE'])) {
		?> <?= MIDDLE_DOT_ENTITY ?> <? echo escape_utf8($party['SUBTITLE']);
	}
	if (!empty($party['STAMP_TZI'])) {
		[$y, $m, $d] = _getdate($party['STAMP_TZI'] - $party['AT2400'], 'UTC');
		echo	', ', $d, ' ',_month_name($m),' ',$y,
			(empty($party['LOCATION_NAME']) ? '' : ', '.escape_utf8($party['LOCATION_NAME'])),
			(empty($party['CITY_NAME'])	? '' : ', '.escape_utf8($party['CITY_NAME']));
	}
	if (isset($_REQUEST['PAGE'])) {
		if (($page = $_REQUEST['PAGE']) === 'all') {
			?> (<?= __('pagecontrols:all'); ?>)<?
		} elseif (is_number($page)) {
			?> (<?= __('pagecontrols:page',array('PAGE'=>$page)); ?>)<?
		}
	}
	return $__cache = [ob_get_clean(), $gallery];
}

function display_header() {
	if (!$_REQUEST['sID']) {
		require_once '_feed.inc';
		show_feed('gallery');
	} else {
		[$title] = get_gallery_and_title();
		include_meta('description', 'Partyflock'.$title);
	}
}

function perform_commit(): ?bool {
	switch ($_REQUEST['ACTION']) {
	default:
	case null:				return null;
	case 'commit':			return gallery_commit();
	}
}

function display_body(): void {
	switch ($_REQUEST['ACTION']) {
	case null:
	case 'year':		gallery_display_overview();				return;
	case 'commit':
	case 'single':
	case 'hidden':
	case 'buddieself':	gallery_display_single();				return;
	case 'form':		gallery_display_form();					return;
	case 'comments':	gallery_show_comments();				return;
	case 'apptodo':		gallery_display_appearances_todo();		return;
	case 'popularity':	gallery_display_popularity();			return;
	case 'archive':		gallery_display_archive();				return;
	default:			not_found();
	}
}

function require_gallery(): array|false {
	[, $gallery] = get_gallery_and_title();
	if ($gallery) {
		return $gallery;
	}
	if (have_idnumber($_REQUEST, 'sID')) {
		register_error('gallery:error:nonexistent_LINE', ['ID' => $_REQUEST['sID']]);
	}
	return false;
}

function gallery_display_appearances_todo() {
	layout_section_header(__('gallery:pageheader:apptodo'));

	require_once '_photographers.inc';
	$cammers = get_photographers();
	if ($cammers === false) {
		return;
	}
	$appstats = db_rowuse_hash(
		array('onphoto','image'),'
		SELECT	image.USERID,
			COUNT(*) AS TOTAL,
			COUNT(DISTINCT IMGID) AS IMGCNT
		FROM onphoto
		JOIN image USING (IMGID)
		GROUP BY image.USERID
		ORDER BY IMGCNT DESC'
	);
	if ($appstats === false) {
		return;
	}
	if (!$appstats) {
		?><p><?= __('gallery:info:no_appearances_LINE'); ?></p><?
/*		?><p>Er zijn momenteel geen fotoverschijningen om te verwerken.</p><?*/
		return;
	}
	$nonactive_total = 0;
	$nonactive_imgs = 0;
	foreach ($appstats as $userid => $appstat) {
		$userid = $appstat['USERID'];
		if (!isset($cammers[$userid])) {
			$nonactive_total += $appstat['TOTAL'];
			$nonactive_imgs += $appstat['IMGCNT'];
			unset($appstats[$userid]);
		}
	}
	layout_open_box('white');
	layout_open_table(TABLE_FULL_WIDTH | TABLE_SORTABLE);
	layout_start_header_row();
	layout_header_cell(Eelement_name('photographer'));
	layout_header_cell_right(Eelement_plural_name('appearance(photo)'));
	layout_header_cell_right(Eelement_plural_name('photo'));
	layout_stop_header_row();
	foreach ($appstats as $appstat) {
		layout_start_rrow();
		echo get_element_link('user',$appstat['USERID']);
		layout_next_cell(class: 'right');
		echo $appstat['TOTAL'];
		layout_next_cell(class: 'right');
		echo $appstat['IMGCNT'];
		layout_stop_row();
	}
	if ($nonactive_total) {
		layout_start_rrow(ROW_LIGHT);
		echo __('gallery:remaining_appearances_old_photographers');
		/*?>Restant verschijningen oude fotografen<?*/
		layout_next_cell(class: 'right');
		echo $nonactive_total;
		layout_next_cell(class: 'right');
		echo $nonactive_imgs;
		layout_stop_row();
	}
	layout_close_table();
	layout_close_box();
}

function gallery_display_popularity(): void {
	$galleryid = have_idnumber($_REQUEST,'sID');
	if ($galleryid && !($gallery = require_gallery())) {
		return;
	}

	layout_open_menu();
	layout_menuitem(Eelement_name('gallery'), get_element_href('gallery', $galleryid));
	layout_close_menu();

	if (!($photos = db_rowuse_hash('image','
		SELECT IMGID,THUMB_WIDTH AS W,THUMB_HEIGHT AS H,PARTYID,HIDDEN,USERID
		FROM image
		WHERE GALLERYID = '.$galleryid))
	||	false === ($stats = memcached_simple_hash('image_counter_total','
		SELECT IMGID, COUNTER
		FROM image_counter_total
		WHERE IMGID IN ('.implodekeys(', ', $photos).')',
		TEN_MINUTES))
	) {
		return;
	}
	arsort($stats);

	[, $gallery] = get_gallery_and_title();

	$photolist = new _photolist();
	$photolist->of_gallery($galleryid);
	$photolist->popularity = true;
	$photolist->query();

	require_once '_notify.inc';
	notify_register_seen(NOTIFY_GALLERY, $gallery['PSTAMP']);

	?><article itemscope itemtype="https://schema.org/ImageGallery"><?
	?><time pubdate itemprop="datePublished" datetime="<?= gmdate(ISO8601Z, $gallery['PSTAMP']) ?>"></time><?
	$photolist->display();
	?></article><?
}

function gallery_show_comments(): void {
	if (!($galleryid = have_idnumber($_REQUEST,'sID'))
	||	!require_gallery()
	) {
		return;
	}

	layout_show_section_header();

	require_once '_allcomments.inc';
	show_all_comments('gallery', $galleryid);
}

function gallery_display_archive(): void {
	layout_show_section_header(Eelement_plural_name('party_photo').' '.MIDDLE_DOT_ENTITY.' '.element_name('archive'));

	gallery_menu();

	if (!($years = memcached_rowuse_array(['image', 'gallery'],  "
		SELECT	COUNT(*) AS TOTAL_PHOTOS,
				COUNT(DISTINCT gallery.USERID) AS TOTAL_PHOTOGRAPHERS,
				COUNT(IF(HIDDEN, 1, NULL)) AS TOTAL_HIDDEN,
				FROM_UNIXTIME(PSTAMP, '%Y') AS YEAR
		FROM image
		JOIN gallery USING (GALLERYID, PARTYID)
		GROUP BY YEAR
		ORDER BY YEAR DESC",
		3 * ONE_DAY
	))) {
		return;
	}
	layout_open_box();
	?><table class="hhla auto zebra fw ptr"><?
	?><tr class="nohl"><?
	?><th class="right"><?= Eelement_name('year') ?></th><?
	?><th class="right"><?= Eelement_plural_name('photo') ?></th><?
	?><th class="right"><?= __C('status:rejected') ?></th><?
	?><th class="right"><?= Eelement_plural_name('photographer') ?></th><?
	?></tr><?
	foreach ($years as $year) {
		/** @noinspection PhpRedundantOptionalArgumentInspection */
		extract($year, \EXTR_OVERWRITE);

		?><tr onclick="openLink(event, this.firstChild)"><?
		?><td class="right"><a href="/gallery/year/<?= $YEAR ?>"><?= $YEAR ?></td><?
		?><td class="right"><?= $TOTAL_PHOTOS ?></td><?
		?><td class="right"><?= $TOTAL_HIDDEN ?></td><?
		?><td class="right"><?= $TOTAL_PHOTOGRAPHERS ?></td><?
		?></tr><?
	}
	?></table><?
	layout_close_box();
}

function gallery_menu() {
	global $__year;

	layout_open_menu();
	layout_menuitem(Eelement_name('overview'),'/gallery', empty($_REQUEST['ACTION']));
	layout_open_menuitem();
	?><form<?
	?> method="get"<?
	?> action="/gallery/year"<?
	?> class="ib"><?

	?><select onchange="this.form.action += '/' + this.value; submitForm(this.form); this.form.submit()"><?
		?><option value=""><?= Eelement_name('archive') ?></option><?
		for ($year = $__year; $year >= 2001; --$year) {
			?><option<?
			if ($_REQUEST['ACTION'] === 'year'
			&&	$_REQUEST['subID']  === $year
			) {
				?> selected<?
			}
			?> value="<?= $year ?>"><?= $year ?></option><?
		}
	?></select><?
	?></form><?
	layout_close_menuitem();
	layout_menuitem(Eelement_plural_name('newest_comment'), '/gallery/comments');
	layout_close_menu();
}

function gallery_display_overview() {

	require_once '_galleryoverview.inc';
	$galleryoverview = new galleryoverview;

	if ($_REQUEST['ACTION'] === 'year') {
		if (!($year = $_REQUEST['subID'])) {
			not_found();
			return;
		}

		layout_show_section_header(Eelement_plural_name('party_photo').' '.MIDDLE_DOT_ENTITY.' '.$year);

		gallery_menu();

		$galleryoverview->show_year($year);
		$galleryoverview->query();
		$galleryoverview->show_shoots();
		# $galleryoverview->display();

		return;
	}

	layout_show_section_header(Eelement_plural_name('party_photo'));

	gallery_menu();

	if (empty($_REQUEST['PAGE'])
	&&	have_admin(['gallery', 'photographer', 'camerarequest'])
	) {
		$galleryoverview->show_inactive();
		$galleryoverview->query();
		$galleryoverview->display();
	}
	$galleryoverview->show_recent();
	$galleryoverview->query();
	$galleryoverview->display();
	if (have_user()
	&&	($galleryid = $galleryoverview->get_last_galleryid())
	) {
		require_once '_notify.inc';
		notify_register_seen(NOTIFY_GALLERY,db_single('gallery','SELECT PSTAMP FROM gallery WHERE GALLERYID='.$galleryid));
	}
}
/*function gallery_display_year($year) {
	require_once '_galleryoverview.inc';

	$galleryoverview = new galleryoverview();
	$galleryoverview->year = $year;
	if (!$galleryoverview->query()) {
		return;
	}
#	$galleryoverview->show_shoots();
	$galleryoverview->display();
}*/

function gallery_single_menu(array $gallery, int $partyid): void {
	$galleryid = $gallery['GALLERYID'];
	$prefix = '/gallery/'.$galleryid;

	layout_open_menu();
	layout_menuitem(Eelement_name('overview'), '/gallery',!$_REQUEST['ACTION']);
	layout_menuitem(Eelement_name('popularity'), $prefix.'/popularity');

	if ((require_once '_allcomments.inc')
	&&	gallery_has_comments($galleryid)
	) {
		layout_menuitem(Eelement_name('comments_overview'), $prefix.'/comments');
	}

	if (have_user()) {
		require_once '_buddies.inc';
		if ($buddies = _buddies_full_hash(CURRENTUSERID)) {
			require_once '_buddy_or_self_appearances.inc';
			if (get_buddy_or_self_appearances($galleryid, $buddies, true)) {
				layout_menuitem(__C('action:show_your_friends_and_self'),$prefix.'/buddieself',$_REQUEST['ACTION'] === 'buddieself' && empty($_REQUEST['PAGE']));
			}/* else {
				layout_open_menuitem();
				?><span class="light"><?= __C('gallery:info:no_buddies_in_set'); ?></span><?
				layout_close_menuitem();
			}*/
		}
		if (have_admin('photo')
		&&	memcached_single('image','
				SELECT 1
				FROM image
				WHERE HIDDEN = 1
				  AND PARTYID = '.$partyid.'
				LIMIT 1')
		||	have_admin('photographer')
		&&	memcached_single('image','
				SELECT 1
				FROM image
				WHERE HIDDEN = 1
				  AND PARTYID = '.$partyid.'
				  AND USERID = '.CURRENTUSERID.'
				LIMIT 1')
		) {
			layout_menuitem(Eelement_plural_name('denied_image'), $prefix.'/hidden',$_REQUEST['ACTION'] === 'hidden' && empty($_REQUEST['PAGE']));
		}
		if (memcached_single('albumelement','SELECT 1 FROM albumelement WHERE PARTYID='.$partyid.' LIMIT 1')) {
			layout_menuitem(Eelement_plural_name('albumelement'),'/album/party/'.$partyid);
		}
	}
	layout_close_menu();

	if (CURRENTUSERID === $gallery['USERID']
	?	!have_admin(['gallery','photographer'])
	:	!have_admin('gallery')
	) {
		return;
	}

	layout_open_menu();
	layout_menuitem(__C('action:change'), $prefix.'/form');
	layout_close_menu();
}

function gallery_display_single() {
	if (!require_idnumber($_REQUEST,'sID')
	||	!require_gallery()
	) {
		return;
	}

	[, $gallery] = get_gallery_and_title();

	if ($_REQUEST['sID'] !== 6272)
	if (!visible_gallery($gallery)
	&&	!(	CURRENTUSERID === $gallery['USERID']
		||	require_admin(array('gallery','photo','camerarequest'))
		)
	) {
		return;
	}
	$galleryid = $_REQUEST['sID'];
	$partyid = $gallery['PARTYID'];

	layout_show_section_header();

	gallery_single_menu($gallery, $partyid);

	require_once '_ticketlist.inc';
	show_connected_tickets();

	if ($gallery['BODY']) {
		layout_open_box('white');
		?><div class="body block"><?= make_all_html($gallery['BODY'], UBB_UTF8); ?></div><?
		layout_close_box();
	}
	$photolist = new _photolist();
	$photolist->of_gallery($galleryid);

	if (have_user()) {
		if ($_REQUEST['ACTION'] === 'hidden') {
			$photolist->only_hidden();
			if (!$GLOBALS['currentuser']->is_admin('photo')) {
				$photolist->made_by(CURRENTUSERID);
			}
		} elseif ($_REQUEST['ACTION'] === 'buddieself') {
			$photolist->only_buddies_and_self();
		}
	}
	if (!$photolist->query()) {
		return;
	}

	require_once '_voice.inc';
	if (may_speak($gallery)) {
		require_once '_vote.inc';
		vote_display_choices();
	}

	require_once '_notify.inc';
	notify_register_seen(NOTIFY_GALLERY, $gallery['PSTAMP']);
	?><article itemscope itemtype="https://schema.org/ImageGallery"><?
	?><time pubdate itemprop="datePublished" datetime="<?= gmdate(ISO8601Z, $gallery['PSTAMP']) ?>"></time><?
	$photolist->display();
	?></article><?
}

function gallery_display_form() {
	if (!require_idnumber($_REQUEST,'sID')
	||	!require_gallery()
	||	!require_admin(['gallery','photographer'])
	) {
		return;
	}
	$galleryid = $_REQUEST['sID'];
	$gallery = db_single_assoc('gallery','SELECT * FROM gallery WHERE GALLERYID='.$galleryid);
	if ($gallery === false) {
		return;
	}
	if (!$gallery) {
		_error(__('gallery:error:nonexistent_LINE',array('ID'=>$galleryid)));
		return;
	}
	layout_section_header(Eelement_name('gallery').' '.MIDDLE_DOT_ENTITY.' '.__('action:change'));

	$gallery_admin = have_admin('gallery');

	if ($gallery['USERID'] === CURRENTUSERID
	?	!require_admin(['gallery','photographer'])
	:	!require_admin('gallery')
	) {
		return;
	}

	$spec = explain_table('gallery');

	?><form<?
	?> accept-charset="utf-8"<?
	?> onsubmit="return submitForm(this)"<?
	?> method="post"<?
	?> action="/gallery/<?= $galleryid; ?>/commit"><?

	layout_open_box('white');
	layout_box_header(get_element_link('gallery',$galleryid));
	layout_open_table(TABLE_CLEAN);
	layout_start_row();
	# CONNECT USER
		echo Eelement_name('photographer');
		?> <?
		echo element_name('connection');
		layout_field_value();
		if (!$gallery['USERID']) {
			echo __('answer:none');
		} else {
			echo get_element_link('user',$gallery['USERID']);
		}

	if ($gallery_admin) {
		// TITLE
		layout_restart_row();
			echo Eelement_name('title');
			layout_field_value();
			?><input type="text" name="TITLE" maxlength="<?= $spec['TITLE']->maxlength ?>" value="<? echo escape_utf8($gallery['TITLE']); ?>" /><?

		// PSTAMP
		layout_restart_row();
			?><label for="direct"><?= Eelement_name('publication'); ?></label><?
			layout_field_value();
			show_publication($gallery);

		# LOGOPOS
		layout_restart_row();
			echo Eelement_name('logo_position');
			layout_field_value();
			?><select name="LOGOPOS"><?
				foreach (['top_left','top_right','bottom_left','bottom_right'] as $pos) {
					?><option<?
					if ($gallery['LOGOPOS'] === $pos) {
						?> selected="selected"<?
					}
					?> value="<?= $pos ?>"><?= __('position:'.$pos) ?></option><?
				}
			?></select><?
	}

	// VISIBLE
	layout_restart_row();
		?><label for="visible"><?= __C('field:visible'); ?></label><?
		layout_field_value();
		?><select id="visible" name="VISIBLE"><?
			foreach ([
				'yes'		=> __('answer:yes'),
				'visitors'	=> element_plural_name('visitor'),
				'no'		=> __('answer:no'),
				'never'		=> __('date:never'),
			] as $visi => $desc) {
				?><option<?
				if ($visi === $gallery['VISIBLE']) {
					?> selected="selected"<?
				}
				?> value="<?= $visi ?>"><?= $desc ?></option><?
			}
		?></select><?

	if ($gallery_admin) {
		# NOVERVIEW
		layout_restart_row();
			echo Eelement_name('overview');
			layout_field_value();
			?><select name="NOVERVIEW"><?
				?><option value="0"><?= __('answer:yes') ?></option><?
				?><option<?
				if ($gallery['NOVERVIEW'] === 1) {
					?> selected="selected"<?
				}
				?> value="1"><?= element_name('list') ?></option><?
				?><option<?
				if ($gallery['NOVERVIEW'] === 2) {
					?> selected="selected"<?
				}
				?> value="2"><?= __('answer:no') ?></option><?
			?></select><?
		// EXTERN
		layout_restart_row();
			?><label for="extern"><?= __C('camstatus:extern'); ?></label><?
			layout_field_value();
			show_input([
				'type'		=> 'checkbox',
				'id'		=> 'extern',
				'name'		=> 'EXTERN',
				'value'		=> 1,
				'checked'	=> $gallery,
			]);
	}
	// ANONYMOUS
	layout_restart_row();
		?><label for="anonymous"><?= __C('field:anonymous'); ?></label><?
		layout_field_value();
		?><input type="checkbox" id="anonymous" name="ANONYMOUS" value="1"<? if ($gallery['ANONYMOUS']) echo ' checked="checked"'; ?>><?

	// NOPERSONS
	layout_restart_row();
		?><label for="nopersons"><?= __C('field:no_persons'); ?></label><?
		layout_field_value();
		show_input([
			'type'		=> 'checkbox',
			'id'		=> 'nopersons',
			'name'		=> 'NOPERSONS',
			'value'		=> 1,
			'checked'	=> $gallery['NOPERSONS']
		]);

	if ($gallery_admin) {
		// BODY
		layout_restart_row();
			echo Eelement_name('real_message');
			layout_field_value();
			show_textarea([
				'name'		=> 'BODY',
				'class'		=> 'growToFit',
				'rows'		=> 5,
				'cols'		=> 50,
				'spec'		=> $spec,
				'value_utf8'	=> $gallery
			]);
	}

	// PHOTOGRAPHER
	layout_restart_row();
		echo Eelement_name('photographer');
		layout_field_value();
		show_input([
			'type'			=> 'text',
			'name'			=> 'PHOTOGRAPHER',
			'spec'			=> $spec,
			'value_utf8'	=> $gallery,
		]);
	// PHOTOGRAPHER SITE
	layout_restart_row();
		echo Eelement_name('site');
		?> <?
		echo element_name('photographer');
		layout_field_value();
		show_input([
			'type'			=> 'url',
			'name'			=> 'PHOTOGRAPHER_SITE',
			'spec'			=> $spec,
			'value_utf8'	=> $gallery,
		]);
	# TEXT BELOW LOGO
	layout_restart_row();
		echo Eelement_name('text_below_logo');
		layout_field_value();
		show_input([
			'type'			=> 'text',
			'name'			=> 'TEXTOVERLAY',
			'spec'			=> $spec,
			'value_utf8'	=> $gallery,
		]);

	layout_close_table();
	layout_close_box();
	?><div class="block"><input type="submit" value="<?= __('action:change'); ?>" /></div></form><?
}
function gallery_commit(): bool {
	require_once '_postedin.inc';
	require_once '_messageindex.inc';
	require_once '_subscriptions.inc';

	if (!require_idnumber($_REQUEST, 'sID')
	||	!require_anything_trim($_POST, 'PHOTOGRAPHER', utf8: true)
	||	!require_anything_trim($_POST, 'PHOTOGRAPHER_SITE', utf8: true)
	||	!require_anything_trim($_POST, 'TEXTOVERLAY', utf8: true)
	||	!require_enum($_POST, 'VISIBLE', 'gallery')
	||	!require_admin(['gallery', 'photographer'])
	) {
		return false;
	}
	if (false === ($userid = db_single('gallery','SELECT USERID FROM gallery WHERE GALLERYID='.($galleryid = $_REQUEST['sID'])))
	||	 null === $userid
	) {
		if ($userid !== false) {
			register_error('gallery:error:nonexistent_LINE', ['ID' => $galleryid]);
		}
		return false;
	}
	$gallery_admin = have_admin('gallery');

	if ($userid === CURRENTUSERID
	?	!require_admin(['gallery', 'photographer'])
	:	!require_admin('gallery')
	) {
		return false;
	}

	$setlist = [];

	if ($gallery_admin) {
		if (!require_anything_trim($_POST, 'BODY', utf8: true)
		||	!require_element($_POST, 'LOGOPOS', ['top_left', 'top_right', 'bottom_left', 'bottom_right'], strict: true, utf8: true)
		||	!parse_publication($setlist)
		||	!require_anything_trim($_POST, 'TITLE', utf8: true)
		) {
			return false;
		}
		$setlist[] = 'BODY="'.			addslashes($_POST['BODY']).'"';
		$setlist[] = 'LOGOPOS="'.		$_POST['LOGOPOS'].'"';
		$setlist[] = 'NOVERVIEW='.		(have_idnumber($_POST, 'NOVERVIEW') ?: 0);
		$setlist[] = 'EXTERN='.			(isset($_POST['EXTERN']) ? 1 : 0);
		$setlist[] = 'TITLE="'.			addslashes($_POST['TITLE']).'"';
	}
	$setlist[] = 'VISIBLE="'.		$_POST['VISIBLE'].'"';
	$setlist[] = 'ANONYMOUS='.		(isset($_POST['ANONYMOUS']) ? 1 : 0);
	$setlist[] = 'NOPERSONS='.		(isset($_POST['NOPERSONS']) ? 1 : 0);
	$setlist[] = 'PHOTOGRAPHER="'.		addslashes($_POST['PHOTOGRAPHER']).'"';
	$setlist[] = 'PHOTOGRAPHER_SITE="'.	addslashes($_POST['PHOTOGRAPHER_SITE']).'"';
	$setlist[] = 'TEXTOVERLAY="'.		addslashes($_POST['TEXTOVERLAY']).'"';

	if (!db_insert('gallery_log','
		INSERT INTO gallery_log
		SELECT * FROM gallery
		WHERE GALLERYID = '.$galleryid)
	||	!db_update('gallery','
		UPDATE gallery SET
			MUSERID = '.CURRENTUSERID.',
			MSTAMP	= '.CURRENTSTAMP.','.
			implode(',',$setlist).'
		WHERE GALLERYID = '.$galleryid)
	) {
		return false;
	}
	register_notice('gallery:notice:changed_LINE');
	return true;
}
