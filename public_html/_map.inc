<?php

function nl_lonlat_to_xy($lon, $lat): array {
	// callibration points
	// point1: Cadzand
	// point2: Nieuw Statenzijl

	$punt1_x = 6; //44;
	$punt1_y = 262; //510;
	$punt1_lon = 3.41667;
	$punt1_lat = 51.3667;
			
	/*
	$punt2_x = 321; //603;
	$punt2_y = 35; //110;
	$punt2_lon = 7.2000;
	$punt2_lat =  53.2333;
			
	$cali_diff_x = $punt1_x - $punt2_x;
	$cali_diff_y = $punt1_y - $punt2_y;
					
	$cali_diff_lon = $punt1_lon - $punt2_lon;
	$cali_diff_lat = $punt1_lat - $punt2_lat;
				
	$factor_lon = $cali_diff_lon / $cali_diff_x;
	$factor_lat = $cali_diff_lat / $cali_diff_y;

	echo 'factor_long = ',$factor_lon,', factor_lat = ',$factor_lat,'<br />';
	*/
			
	$factor_lon = 0.012010571428571;
	$factor_lat = -0.00822290748898688;
			
	return [(int)((($lon - $punt1_lon) / $factor_lon) + $punt1_x),
		(int)((($lat - $punt1_lat) / $factor_lat) + $punt1_y)];
}
function be_lonlat_to_xy($lon, $lat): array {
	// callibration points
	// point1: De Panne
	// point2: Kinrooi

	$punt1_x = 8; 
	$punt1_y = 89; 
	$punt1_lon = 51.1;
	$punt1_lat = 2.5833333;
/*			
	$punt2_x = 472;
	$punt2_y = 82;
	$punt2_lon = 51.15;
	$punt2_lat = 5.75;
			
	$cali_diff_x = $punt1_x - $punt2_x;
	$cali_diff_y = $punt1_y - $punt2_y;
					
	$cali_diff_lon = $punt1_lon - $punt2_lon;
	$cali_diff_lat = $punt1_lat - $punt2_lat;
				
	$factor_lon = $cali_diff_lon / $cali_diff_x;
	$factor_lat = $cali_diff_lat / $cali_diff_y;

	echo 'factor_long = ',$factor_lon,', factor_lat = ',$factor_lat,'<br />';
*/
	$factor_lon = 0.00010775862068965;
	$factor_lat = -0.45238095714286;

	return [(int)((($lon - $punt1_lon) / $factor_lon) + $punt1_x),
		(int)((($lat - $punt1_lat) / $factor_lat) + $punt1_y)];
}
