<?php

require_once 'defines/youtube.inc';
require_once '_flockmod.inc';

function get_videoinfo_url(string $type, string $externalid): ?string {
	return match($type) {
		'youtube'	=>	'https://www.googleapis.com/youtube/v3/videos'.
						'?part=id,contentDetails,status,snippet,player,recordingDetails'.
						'&maxWidth=10000'.
						'&id='.urlencode($externalid).
						'&key='.YOUTUBE_V3_KEY,
		'vimeo'		=>	'https://vimeo.com/api/v2/video/'.$externalid.'.php',
		default		=>	null,
	};
}

function get_videoinfo_data(string $type, string $externalid, bool $warn = true): string|false {
	static $__data;
	if ( isset($__data[$type][$externalid])) {
		return $__data[$type][$externalid];
	}
	if (!($url = get_videoinfo_url($type, $externalid))
	||	!($data = safe_file_get_contents($url))
	) {
		register_message($warn ? 'warning' : 'error', 'video:error:failed_to_get_information_LINE');
		$data = false;
	}
	return $__data[$type][$externalid] = $data;
}

function fill_videoinfo(
	string  $type,
	int		$videoid,
	string  $externalid,
	?array  $channels	= null,
	?string $data		= null,
): array|false {
	static $__info;
	if (!isset($__info[$type][$externalid])) {
		if ($channels) {
			static $ch = 0;
			if ($ch === 0) {
				if (!($ch = curl_init())) {
					return false;
				}
				require_once __DIR__.'/../config/phpclasses/ipresolve.inc';
				curl_setopt_array($ch, [
					CURLOPT_RETURNTRANSFER	=> true,
					CURLOPT_USERAGENT		=> USER_AGENT_GENERIC_GET,
					CURLOPT_IPRESOLVE		=> RESOLVE_IPV_CURL,
					CURLOPT_VERBOSE			=> true,
				]);
			}
			$url = get_videoinfo_url($type, $externalid);
			curl_setopt($ch, CURLOPT_URL, $url);
			$data = curl_exec($ch);
		} elseif (!$data) {
			$data = get_videoinfo_data($type, $externalid, $videoid);
		}
		if (!$data
		||	!($info = ('fill_videoinfo_'.$type)($data))
		) {
			return false;
		}
		/** @noinspection PhpUnusedLocalVariableInspection */
		$__info[$type][$externalid] = $info;
	} else {
		$info = $__info[$type][$externalid];
	}
	$setlist = [];
	foreach ($info as $key => $val) {
		switch ($key) {
		case 'TITLE':
		case 'LOCATION':
		case 'PSTAMP':
		case 'CONTENT':
		case 'CHANNEL':
		case 'CHANNEL_TYPE':
			$setlist[] = $key.' = "'.addslashes($val).'"';
			break;
		}
	}
	if ($setlist
	&&	!db_replace('videoinfo', '
		REPLACE INTO videoinfo SET
			VIDEOID = '.$videoid.', '.
			implode(', ', $setlist)
	)) {
		return false;
	}
	if (isset($info['CHANNEL_TYPE'])) {
		$type = $info['CHANNEL_TYPE'];
	}
	if (!($channel = $info['CHANNEL'])) {
		$defined_vars = get_defined_vars();
		if (CLI) {
			error_log('video.CHANNEL is empty, defined variables: '.var_get($defined_vars));
		}
		mail_log('videoinfo.CHANNEL is empty', item: $defined_vars);
		return $info;
	}

	if (false === ($channelid = ($channels[$type][$channel] ??=	db_single('videochannel', "
		SELECT CHANNELID
		FROM videochannel
		WHERE TYPE = '$type'
		  AND NAME = '".addslashes($channel)."'")))
	||	$channelid
	&&	!db_update('video', "
		UPDATE video SET CHANNELID = $channelid
		WHERE VIDEOID = $videoid")
	) {
		return false;
	}
	return $info;
}

function fill_videoinfo_vimeo(string $data): array|false {
	$videos = unserialize($data, ['allowed_classes' => false]);
	if (!isset($videos[0])) {
		return false;
	}
	$video = $videos[0];
	if (!is_array($video)) {
		ob_start();
		echo	"could not get vimeo video objects from data:\n\n",
			"data:\n\n",
			$data,"\n\n",
			"unserialized:\n\n";
		var_dump($videos);
		mail_log(ob_get_clean(), item: $_REQUEST);
		return false;
	}
	if (!preg_match('"vimeo\.com/(.*)$"', $video['user_url'], $match)) {
		return false;
	}
	return ['TITLE'			=> $video['title'],
			'CONTENT'		=> $video['description'],
			'PSTAMP'		=> strtotime($video['upload_date']),
			'THUMB_URL'		=> $video['thumbnail_large'],
			'LOCATION'		=> null,
			'DURATION'		=> is_number($video['duration']) ? $video['duration'] : 0,
			'WIDTH'			=> $width  = is_number($video['width' ]) ? $video['width' ] : 0,
			'HEIGHT'		=> $height = is_number($video['height']) ? $video['height'] : 0,
			'ASPECT'		=> $height ? ($width / $height) : 0,
			'CHANNEL_TYPE'	=> 'vimeo',
			'CHANNEL'		=> $match[1]];
}

function fill_videoinfo_youtube(string $data): array|false {
	if (!($obj = safe_json_decode($data))) {
		error_log('fill_videoinfo_youtube: could not decode data');
		return false;
	}
	if (empty($obj->items)) {
		register_error(
			str_contains($data, 'quotaExceeded')
		?	'videoinfo:error:over_quota_LINE'
		:	'videoinfo:error:no_items_found_LINE'
		);
		return false;
	}
	if (!($video = $obj->items[0])) {
		error_log('fill_videoinfo_youtube: item[0] is empty');
		return false;
	}
	$location = '';
	if (property_exists($video, 'recordingDetails')) {
		if (property_exists($video->recordingDetails, 'latitude')) {
			$location =
				(float)$video->recordingDetails->latitude.','.
				(float)$video->recordingDetails->longitude.','.
				(float)$video->recordingDetails->altitude;
		}
	}

	$duration = 0;
	if ($video->contentDetails?->duration ?? null) {
		try {
			$interval = new DateInterval($video->contentDetails->duration);
			$duration = $interval->d * ONE_DAY
						+ $interval->h * ONE_HOUR
						+ $interval->i * 60
						+ $interval->s;
		} catch (Exception $e) {
			mail_log('fill_videoinfo_youtube: could not parse duration: '.$e->getMessage(), $video);
		}
	}

	return ['TITLE'			=> $video->snippet->title,
			'WIDTH'			=> $width  = (int)($video?->player?->embedWidth	 ?? 0),
			'HEIGHT'		=> $height = (int)($video?->player?->embedHeight ?? 0),
			'ASPECT'		=> $height ? ($width / $height) : 0,
			'LOCATION'		=> $location,
			'CONTENT'		=> $video->snippet->description,
			'CHANNEL_TYPE'	=> 'youtube',
			'CHANNEL'		=> $video->snippet->channelTitle,
			'PSTAMP'		=> strtotime($video->snippet->publishedAt),
			'DURATION'		=> $duration,
			'INVISIBLE'		=> youtube_video_non_playable($video)
							|| youtube_video_non_embeddable($video)];
}
