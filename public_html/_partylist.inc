<?php

const SHOW_CANCELLEDS	= false;	# show cancelleds during lockdown
const SHOW_POSTPONEDS	= true;		# show postponsed during lockdown

const EVENT_PROCESSING_GRACE_PERIOD = TEN_MINUTES;

const ADE_ORGANIZATIONID = 3776;

require_once '_going.inc';
require_once '_party.inc';
require_once '_memcache.inc';
require_once '_urltitle.inc';	# get_element_href
require_once '_smallscreen.inc';
require_once 'defines/facebook.inc';
require_once 'defines/party.inc';

class _partylist {
	private const int	ORDER_REVERSE				= 1;
	private const int	ORDER_IGNORE_TIME			= 2;

	private int			$now_and_soon_stamp			= 0;

	public bool			$show_date					= false;
	public bool			$show_date_first			= false;
	public bool			$show_date_headers			= false;
	public bool			$hide_day_prefix			= false;
	public bool			$hide_month_header			= false;
	public bool			$one_table					= false;
	public bool			$show_city_separate			= false;
	public bool			$show_customer				= false;
	public bool			$show_contests				= false;
	public bool			$show_creator				= false;
	public bool			$show_creation_date			= false;
	public bool			$show_filter				= false;
	public bool			$show_modification_date	 	= false;
	public bool			$show_last_modifier			= false;
	public bool			$show_last_validator		= false;
	public bool			$show_last_validation_date	= false;
	public bool			$show_vevent				= false;
	public bool			$show_sort_headers			= false;
	public int			$show_vote_userid			= 0;
	public bool			$show_people				= false;
	public bool			$show_camera_present		= false;
	public bool			$show_camera				= false;
	public int			$hide_buddy					= 0;
	public bool			$show_buddy_hearts			= false;
	public bool			$show_buddies				= false;
	public bool			$show_relation				= false;
	public bool			$show_stars					= false;
	public bool			$show_location				= false;
	public bool			$show_city					= false;
	public bool			$show_lineups				= false;
	public bool			$show_lineups_form			= false;
	public bool			$show_time_offset			= false;
	public bool			$dont_mark_mine				= false;
	public bool			$none_hidden				= false;
	public bool			$nomargin					= false;
	public bool			$include_maybe				= false;
	public bool			$include_all				= false;
	public bool			$only_livestreams			= false;
	public bool			$include_livestreams		= false;
	public bool			$need_geo					= false;
	public bool			$use_ids					= false;
	public bool			$hilite_now					= false;
	/*** @var array|null { float: latitude, float: longitude } */
	public ?array 		$show_distance				= null;
	public bool			$hide_ads					= false;
	public bool			$hide_flag					= false;
	public bool			$hide_country				= false;
	public bool			$hide_separators			= false;
	public bool			$hide_subtitles				= false;
	public bool			$need_lineup_pstamp			= false;
	public bool			$skip_future_lineup			= false;
	public string		$skip;
	public int			$limit						= 0;
	public ?string		$having						= null;
	public ?int			$only_of_interest			= null;		# countryid
	public int			$order_chrono;
	public int			$future_offset;
	public bool	 		$cancelled					= false;

	private ?int		$in_city					= null;
	private	?int		$in_agenda;
	private array		$have_timetable;
	private bool		$past						= false;
	private array 		$date;
	private int			$daystamp					= 0;
	private int			$startstamp					= 0;
	private int			$stopstamp					= 0;

	private bool		$host_or_org				= false;
	private bool		$new						= false;
	private bool		$hide_need_check			= false;

	private ?string		$order						= null;
	private int			$per_page					= 0;
	private bool		$utc						= false;
	private int			$size						= 0;
	private float		$lat;
	private float		$lon;
	private int			$column_count;
	private int			$user_going					= 0;
	private int			$by_org						= 0;
	private array		$partylist					= [];
	private string		$partyidstr;
	private array|bool	$visible					= true;
	private array		$visiblecnt					= [];
	private array		$counts						= [];
	private array		$daycounts					= [];
	private array		$weekday					= [];
	private array		$campresent					= [];
	private array		$day_visible				= [];
	private array		$month_visible				= [];
	private array		$have_images				= [];
	private array		$have_videos				= [];
	private array		$contest_link				= [];
	private array		$allpplcnt					= [];
	private array		$appiccnt_pfid				= [];
	private array		$appiccnt_fbid				= [];
	private array		$allbuddies					= [];
	private array		$allbuddycnt				= [];
	private array		$relations;
	private array		$votes						= [];
	private array		$wheres						= [];
	private array		$tables						= [];
	private array		$joins 						= [];
	private array		$suborder;
	private favouriteinfo
						$favinfo;
	private bool		$need_group					= false;
	private bool		$need_distinct				= false;
	private string		$sort_preference;
	private array		$is_ade;
	private string		$tableclassstr;

	public function __construct() {
		$this->in_agenda = $_REQUEST['sID'] === 'agenda';
	}
	# timezone UTC
	final public function where_start_stop(int $start, int $stop): string {
		return "party.STAMP_TZI BETWEEN $start AND ".($stop - 1);
	}
	final public function list(): array {
		# obtain copy of list
		return $this->partylist ?? [];
	}
	final public function size(): int {
		return $this->size;
	}
	final public function have_rows(): bool {
		return (bool)$this->partylist;
	}
	final public function query(bool $store = false): bool {
		require_once '_buddies.inc';
		require_once '_favourite.inc';
		require_once '_gallery.inc';
		require_once '_geo.inc';
		require_once '_going.inc';
		require_once '_partylistfilter.inc';
		require_once '_partyrelation.inc';
		require_once '_settings.inc';
		require_once '_vote.inc';
		require_once 'defines/appic.inc';

		if (SMALL_SCREEN) {
			$this->hide_subtitles = true;
		}
		$party_admin = have_admin('party');

		$selects = ['
			party.PARTYID,
			party.LOCATIONID,
			party.NAME,
			party.LIVESTREAM, party.LIVESTREAM_ADDRESS,
			SUBTITLE,
			party.EDITION,
			party.DELETED,
			party.ACCEPTED,
			FROM_UNIXTIME(STAMP_TZI, "%H") AS HOUR,
			CANCELLED, POSTPONED, MOVEDID,
			party.USERID, party.MUSERID,
			party.STAMP, STAMP_TZI, ENDSTAMP_TZI, AT2400, NOTIME,
			SOLD_OUT, FREE_ENTRANCE, DOORONLY,
			(SELECT b\'1\' FROM connect WHERE MAINTYPE = "party" AND MAINID = party.PARTYID AND ASSOCTYPE = "party" LIMIT 1) AS HAVE_PARTY_CONNECTS,
			'.PARTY_MAIN_ID
		];

		if ($this->new
		||	$this->show_last_validator
		) {
			$selects[] = '
				LAST_VALIDATED,
				IF (	party.MSTAMP = LAST_VALIDATED,
					party.MUSERID,
					COALESCE((	SELECT MUSERID
							FROM party_log
							WHERE party_log.PARTYID = party.PARTYID
							  AND party_log.MSTAMP = party.LAST_VALIDATED
							LIMIT 1
						),	0)
				) AS LAST_VALIDATOR';
		}

		if ($party_admin) {
			$selects[] = 'PRESALE_STAMP';
		} else {
			$this->wheres[] = ' party.DELETED = 0 ';
		}
		if ($this->host_or_org) {
			$selects[] = 'SUM(IS_HOST) AS IS_HOST, SUM(IS_ORG) AS IS_ORG';
		}
		if ($this->show_creation_date) {
			$selects[] = 'party.CSTAMP';
		}
		if ($this->show_modification_date) {
			$selects[] = 'party.MSTAMP';
		}
		$selects[] = 'DURATION_SECS';

		$show_location  = $this->show_location;
		$show_city	= $this->show_city;
		$need_geo	= $this->need_geo || $this->show_distance || $this->only_of_interest;

		# test rich snippets on all pages, (also, specialday indices want rich snippets!)

		$this->show_vevent = !$this->now_and_soon_stamp;

		if ($show_city
		||	$show_location
		||	$need_geo
		||	$this->show_vevent
		||	$this->now_and_soon_stamp
		||	!$this->hide_flag
		||	$this->show_date
		) {
			$selects[] = 'BOARDINGID,boarding.CITYID AS BOARDING_CITYID';
			$this->joins[] = 'LEFT JOIN boarding USING (BOARDINGID)';
			$this->tables['boarding'] = 'boarding';

			if ($show_location) {
				$selects[] = 'LOCATION_ADDR';
			}
			if ($show_city
			||	$need_geo
			||	$this->show_vevent
			||	$this->now_and_soon_stamp
			|| !$this->hide_flag
			||	$this->show_date
			) {
				$selects[] = 'COALESCE(boarding.CITYID, location.CITYID, party.CITYID) AS SHOW_CITYID';
				$this->joins[] = 'LEFT JOIN location ON location.LOCATIONID = party.LOCATIONID';
				$this->tables['location'] = 'location';
				if ($need_geo
				||	$this->show_vevent
				) {
					if ($this->in_city) {
						if ($city = memcached_city_info($this->in_city)) {
							$city_lat = $city['LATITUDE'];
							$city_lon = $city['LONGITUDE'];
						} else {
							$city_lat = $city_lon = 0;
						}
					}
					if (!isset($city_lat)) {
						$city_lat = 'city.LATITUDE';
						$city_lon = 'city.LONGITUDE';
					}
					$selects[] = '
						COALESCE(boarding.LATITUDE,  location.LATITUDE,  '.$city_lat.') AS LATITUDE,
						COALESCE(boarding.LONGITUDE, location.LONGITUDE, '.$city_lon.') AS LONGITUDE';
				}
			}
		}
		if (!$this->in_city) {
			$selects[] = 'city.CITYID, city.COUNTRYID, city.TIMEZONE, party.CITYID AS PARTY_CITYID';
			$this->joins[] = 'LEFT JOIN city ON city.CITYID = COALESCE(boarding.CITYID, location.CITYID, party.CITYID)';
		} else {
			if (!($city = memcached_city_info($this->in_city))) {
				return false;
			}
			$selects[] = $city['COUNTRYID'].' AS COUNTRYID,"'.$city['TIMEZONE'].'" AS TIMEZONE,'.$this->in_city.' AS CITYID';
		}
		if ($this->skip_future_lineup
		||	$this->need_lineup_pstamp
		||	$this->show_stars
		&& !$party_admin
		&&	have_user()
		) {
			$selects[] = 'LINEUP_PSTAMP';
		}

		if ($this->cancelled) {
			$this->wheres[] = 'CANCELLED = 1';
		} elseif (
			!have_admin()
		&&	!$this->include_all
		) {
			if (!SHOW_CANCELLEDS) {
				$this->wheres[] = 'CANCELLED = 0';
			}
			if (!SHOW_POSTPONEDS) {
				$this->wheres[] = 'POSTPONED = 0';
			}
			$this->wheres[] = 'MOVEDID = 0';
		}
		if ($this->only_livestreams) {
			# show only events that have a live stream
			$this->wheres[] = 'LIVESTREAM IN ("only","+event")';
		} elseif (
			$this->include_livestreams
		||	have_element($_REQUEST, 'sELEMENT', ['organization', 'location', 'user', 'artist'], strict: true)
		) {
			# show both live stream and regular events or combination
		} else {
			# show only events that have regular entrance
			$this->wheres[] = 'LIVESTREAM IN ("","+event")';
		}

		if ($this->in_city) {
			$this->joins['party_city'] = 'JOIN (
				SELECT PARTYID
				FROM party
				WHERE CITYID = '.$this->in_city.'
				UNION
				SELECT PARTYID
				FROM party
				JOIN location USING (LOCATIONID)
				WHERE BOARDINGID = 0
				  AND location.CITYID = '.$this->in_city.'
				UNION
				SELECT PARTYID
				FROM party
				JOIN boarding USING (BOARDINGID)
				WHERE boarding.CITYID = '.$this->in_city.'
			) AS party_city USING (PARTYID)';
		}

		$this->tables['party'] = 'party';

		if (isset($this->tables['going'])) {
			$selects[] = 'MAYBE';

		}
		$selects[] = '
			(	SELECT CSTAMP
				FROM lineupneedupdate
				WHERE lineupneedupdate.PARTYID = party.PARTYID
			) AS LINEUP_NEEDUPDATE,

			(	SELECT b\'1\'
				FROM lineup WHERE lineup.PARTYID = party.PARTYID
				LIMIT 1
			) AS HAS_LINEUP,

			party.PLACEHOLDER';

		if (!($tmplist = memcached_rowuse_hash_if_not_admin(
			'party',
			$this->tables, '
			SELECT '.($this->need_distinct ? 'DISTINCT ' : '').
				implode(', ', $selects).'
			FROM party'.
			($this->joins 		? ' '.implode(' ', $this->joins) : '').
			($this->wheres		? ' WHERE '.implode(' AND ', $this->wheres) : '').
			($this->need_group	? ' GROUP BY PARTYID' : '').
			($this->having		? ' HAVING '.$this->having : '').
			(	$this->order
			?	' ORDER BY '.$this->order.
				' LIMIT '.$this->per_page
			:	(	$this->limit
				?	' ORDER BY STAMP_TZI '.($this->order_chrono & self::ORDER_REVERSE ? 'DESC' : 'ASC').
					' LIMIT '.$this->limit
				:	''
				)
			),
			expires: CACHE_PERIOD_PARTYLIST,
			flags:	($this->utc ? DB_UTC : 0)
				|	(partylist_refresh_needed($this->new ? 'new' : null) ? DB_FRESHEN_MEMCACHE : 0)
		))) {
			return $tmplist !== false;
		}
		if ($store) {
			if (!isset($this->tmp)) {
				$this->tmp = [];
			}
			$this->tmp += $tmplist;
			return true;
		}
		if (isset($this->tmp)) {
			$tmplist += $this->tmp;
			unset($this->tmp);
		}

		$partyids = array_keys($tmplist);
		sort($partyids);
		$this->partyidstr = implode(',', $partyids);

		$this->size = 0;

		if ($this->show_buddies) {
			$this->show_buddy_hearts = true;
		}

		if ($this->show_stars
		&&	have_user()
		) {
			$this->favinfo = new favouriteinfo($partyids);
			if (!$this->favinfo->have_favourites_for_parties()) {
				unset($this->favinfo);
			}
		}
		if ($this->show_buddies
		||	$this->show_buddy_hearts
		) {
			if (!have_user()
			||	$this->show_buddy_hearts
			&&	!setting_isset(SHOW_AGENDA_HEARTS)
			) {
				$this->show_buddies = false;
				$this->show_buddy_hearts = false;
			} elseif ($buddies = _buddies_full_hash(CURRENTUSERID)) {
				if ($this->hide_buddy) {
					unset($buddies[$this->hide_buddy]);
				}
				if ($buddies) {
					memcached_prefetch_users($buddies);

					if ($this->allbuddies = memcached_simple_hash('going','
						SELECT PARTYID, USERID, MAYBE
						FROM going
						WHERE MAYBE IN (0, 1)
						  AND USERID IN ('.implode(',',$buddies).")
						  AND PARTYID IN ($this->partyidstr)")
					) {
						foreach ($this->allbuddies as $partyid => $users) {
							$this->allbuddycnt[$partyid] = count($users);
						}
					}
				}
			}
		}
		if ($this->show_buddies
		&&	SMALL_SCREEN
		) {
			$this->show_buddy_hearts = true;
		}
		$mainidstr = '0';
		foreach ($tmplist as $partyid => $party) {
			$mainidstr .= ','.($party['MAIN_ID'] ?: $partyid);
		}
		if ($this->show_people
		||	$this->only_of_interest
		) {
			if (false === ($local_allpplcnt = memcached_simple_hash('going', "
				SELECT PARTYID, COUNT(*)
				FROM going
				WHERE MAYBE = 0
				  AND PARTYID IN ($this->partyidstr)
				GROUP BY PARTYID",
				ONE_HOUR))
			||	false === ($local_appiccnt_fbid = memcached_simple_hash(['fbid', 'appic_event'], "
				SELECT ID, MAX(VISITORS)
				FROM appic_event
				JOIN fbid USING (FBID)
				WHERE ELEMENT = 'party'
				  AND ID IN ($this->partyidstr)
				GROUP BY ID",
				ONE_HOUR))
			||	false === ($local_appiccnt_pfid = memcached_simple_hash(['fbid', 'appic_event'], "
				SELECT PARTYID, MAX(VISITORS)
				FROM appic_event
				WHERE PARTYID IN ($mainidstr)
				GROUP BY PARTYID",
				ONE_HOUR))
			) {
				return false;
			}
			$this->appiccnt_pfid = $local_appiccnt_pfid;
			$this->appiccnt_fbid = $local_appiccnt_fbid;
			$this->allpplcnt = $local_allpplcnt;
		}

		if ($this->only_of_interest) {
			if (false === ($profile = current_profile())) {
				return false;
			}
			foreach ($tmplist as $partyid => $party) {
				if ($party['STAMP_TZI'] < 1598911200) {
					# keep all events till 1 september 2020, not much to show otherwise due to coronavirus
					continue;
				}
				if ($party['COUNTRYID'] !== $this->only_of_interest
				&&	empty($this->allpplcnt[$partyid])
				&&	empty($this->appiccnt_pfid[$partyid])
				&&	$party['LATITUDE']
				&&	$party['LONGITUDE']
				) {
					# remove foreign events without visitors
					$remove = true;
					if (!empty($profile['positions'])) {
						foreach ($profile['positions'] as [$latitude, $longitude]) {
							if (calculate_distance(
									$latitude,
									$longitude,
									$party['LATITUDE'],
									$party['LONGITUDE']
								) < 100
							) {
								# anything within 100 km of current user should be kept
								$remove = false;
							}
						}
					}
					if ($remove) {
						unset($tmplist[$partyid]);
					}
				}
			}
		}

		foreach ($tmplist as $partyid => $party) {
			$my_party =
				have_user()
			&&	CURRENTUSERID === $party['USERID'];

			if ((	$party_admin
				||	(	$party['ACCEPTED']
					&&	!$party['DELETED']
					)
				||	$my_party
				)
			&&	(	$party_admin
				||	$this->include_all
				||	(	SHOW_CANCELLEDS
					||	!$party['MOVEDID']
					&&	(	!$party['CANCELLED']
						||	$this->cancelled
						)
					)
				||	($goingtype = going_to_party_type($partyid))
				&&	$goingtype === GOING_CERTAIN
				)
			&&	(	$party_admin
				||	!$this->skip_future_lineup
				||	empty($party['LINEUP_PSTAMP'])
				||	$party['LINEUP_PSTAMP'] < CURRENTSTAMP
				)
			&&	(	$this->include_maybe
				||	empty($party['MAYBE'])
				)
			) {
				if ($this->show_filter) {
					static $cities		= [0 => false];
					static $locations	= [0 => false];
					$cityid = $party['SHOW_CITYID'];

					if (!isset($cities[$cityid])) {
						$name = memcached_city($cityid);
						if ($name) {
							$name = preg_replace('"^(?:\'s[\-\s]|\'t\s)"','',$name);
						}
						$cities[$cityid] = $name ?? 'city:'.$cityid;
					}
					$order['CITY'][$cityid] = mb_strtolower($cities[$cityid]);
					$locationid = $party['LOCATIONID'];
					if (!isset($locations[$locationid])) {
						$location = memcached_location_info($locationid);
						$locations[$locationid] = !empty($location['NAME']) ? mb_strtolower($location['NAME']) : 'location:'.$locationid;
					}
					$order['LOCATION'][$locationid] = $locations[$locationid];
					if (isset($this->allpplcnt)
					||	isset($this->allbuddycnt)
					) {
						$order['VISITORS'][$partyid] =
							ltrim(sprintf('%010d%010d',
								$this->get_all_count($party)[0],
								isset($this->allbuddycnt) ? (getifset($this->allbuddycnt,$partyid) ?: 0) : 0
							),'0');
					} elseif (
						isset($this->pplcnt)
					||	isset($this->buddycnt)
					) {
						$order['VISITORS'][$partyid] =
							sprintf('%010d%010d%010d%010d',
								$this->pplcnt[0][$partyid] ?? 0,
								$this->pplcnt[1][$partyid] ?? 0,
								$this->buddycnt[0][$partyid] ?? 0,
								$this->buddycnt[1][$partyid] ?? 0
							);
					}
					$order['NAME'][$partyid] = mb_strtolower($party['NAME'].' #'.$party['EDITION'].' '.$party['SUBTITLE']);
				}
				if ($this->daystamp) {
					$this->partylist[] = $party;
					++$this->size;
				} elseif ($party['DURATION_SECS'] > ONE_DAY) {

					$stamp_tzi = $party['STAMP_TZI'];

					if ($this->startstamp
					&&	$stamp_tzi < $this->startstamp
					) {
						$stamp_tzi = $this->startstamp;
					}

					$stopstamp = $party['ENDSTAMP_TZI'];//strtotime('+1 day',$party['ENDSTAMP_TZI']);

					if ($this->stopstamp) {
						$stopstamp = min($stopstamp,$this->stopstamp);
					}

					$multi_row = $this->show_date_headers || $this->one_table;

					$i = 0;
					$dups = [];
					change_timezone('UTC');
					$orig_party = $party;
					do {
						$newparty = $party;

						$next_stamp = strtotime('+1 day 12:00',$stamp_tzi);

						$newparty['STAMP_TZI'] = $stamp_tzi;
						$newparty['DURATION_SECS'] = $next_stamp - 1 - $stamp_tzi;

						[$year, $month, $day, $hour, $mins] = _getdate($stamp_tzi);

						if (  $newparty['STAMP_TZI'] +   $newparty['DURATION_SECS']
						>	$orig_party['STAMP_TZI'] + $orig_party['DURATION_SECS']
						) {
							# last day!
							$newparty['DURATION_SECS'] -=
								(  $newparty['STAMP_TZI'] +   $newparty['DURATION_SECS']) -
								($orig_party['STAMP_TZI'] + $orig_party['DURATION_SECS']);
						}

						if (!isset($newparty['TIMEZONE'])) {
							$newparty['TIMEZONE'] = 'Europe/Amsterdam';
						}
						change_timezone($newparty['TIMEZONE']);
						$newparty['STAMP'] = mktime($hour, $mins, 0, $month, $day, $year);
						change_timezone();

						$newparty['MULTIDAY'] = ++$i;

						if (!$multi_row) {
							$dups[] = $newparty;
						} else {
							$this->partylist[] = $newparty;
						}
						++$this->size;

						$stamp_tzi = $next_stamp;
					}
					while ($stamp_tzi < $stopstamp);
					change_timezone();

					if (!$multi_row) {
						$party['dups'] = $dups;
						$this->partylist[] = $party;
					}
				} else {
					$this->partylist[] = $party;
					++$this->size;
				}
			} else {
				$this->invisilist[] = $partyid;
			}
		}
		unset($tmplist);

		if ($this->show_relation) {
			if (false === ($local_relations = find_party_relations($this->partyidstr))) {
				return false;
			}
			$this->relations = $local_relations;
		}
		if (isset($order)) {
			foreach ($order as $sort => &$list) {
				if ($sort === 'VISITORS') {
					arsort($list);
				} else {
					natcasesort($list);
				}
				$i = 0;
				$prev = null;
				foreach ($list as /* $id => */ &$cnt) {
					if ($prev === null
					||	$prev !== $cnt
					) {
						++$i;
					}
					$prev = $cnt;
					$cnt = $i;
				}
				unset($cnt);
			}
			unset($list);
			$this->suborder = $order;
		}
		if (!$this->partylist) {
			// no parties? failsafe
			return true;
		}
		if (!$this->new) {
			change_timezone('UTC');
			if (!$this->show_filter) {
				$this->sort_preference = 'NAME';
			} else {
				$stored = get_stored_filters();
				$this->sort_preference = getifset($stored,'SORT') ?: 'NAME';
			}
			usort($this->partylist, [$this, 'compare']);
			change_timezone();
		}
		if ($this->show_camera_present) {
			if (false === ($local_camera_present = memcached_simple_hash(['camerarequest', 'rights'],  "
				SELECT PARTYID, COUNT(rights.USERID)
				FROM camerarequest
				LEFT JOIN rights ON rights.USERID = camerarequest.USERID AND rights.`PORTION` = 'photographer'
				WHERE PARTYID IN ($this->partyidstr)
				  AND STATUS IN ('self', 'extern', 'accepted')
				GROUP BY camerarequest.PARTYID",
				TEN_MINUTES))
			) {
				return false;
			}
			$this->campresent = $local_camera_present;
		}

		if ($this->show_camera) {
			if (false === ($local_have_images = memcached_multirowuse_hash(['gallery', 'image'],'
				SELECT gallery.PARTYID, gallery.USERID, GALLERYID, VISIBLE, ANONYMOUS, PHOTOGRAPHER,
					COUNT(IF(!HIDDEN, 1, NULL)) AS CNT
				FROM gallery
				LEFT JOIN image USING (GALLERYID)
				WHERE gallery.PARTYID IN ('.$this->partyidstr.')
				GROUP BY GALLERYID',
				TEN_MINUTES))
			) {
				return false;
			}
			if ($local_have_images) {
				if (have_admin(['photo', 'gallery', 'camerarequest'])) {
					$this->have_images = $local_have_images;
				} else {
					$visible_have_images = [];
					foreach ($this->have_images as $partyid => $galleries) {
						foreach ($galleries as $gallery) {
							if (visible_gallery($gallery)) {
								$visible_have_images[$partyid][] = $gallery;
							}
						}
					}
					$this->have_images = $visible_have_images;
				}
			}
			if (false === ($local_have_videos = memcached_multirowuse_hash(['connect', 'video'], "
				SELECT MAINID, VIDEOID, STATUS, TITLE, DURATION
				FROM connect
				JOIN video ON VIDEOID = ASSOCID
				WHERE MAINTYPE = 'party'
				  AND MAINID IN ($this->partyidstr)
				  AND ASSOCTYPE = 'video'",
				TEN_MINUTES))
			) {
				return false;
			}
			if ($local_have_videos) {
				if (have_admin('video')) {
					$this->have_videos = $local_have_videos;
				} else {
					$active_have_videos = [];
					foreach ($this->have_videos as $partyid => $videos) {
						foreach ($videos as $video) {
							if ($video['STATUS'] === 'active') {
								$active_have_videos[$partyid][] = $video;
							}
						}
					}
					$this->have_videos = $active_have_videos;
				}
			}
		}
		if (false === ($local_contests = memcached_rowuse_hash(['contest','connect'], "
			SELECT DISTINCT PARTYID, CONTESTID, PSTAMP
			FROM contest
			WHERE CLOSED = 0
			  AND PARTYID
			  AND ACTIVE
			  AND NOT ONLYPROMO
			UNION
			SELECT DISTINCT MAINID, CONTESTID, PSTAMP
			FROM connect
			JOIN contest ON ASSOCID = CONTESTID
			WHERE MAINTYPE = 'party'
			  AND ASSOCTYPE = 'contest'
			  AND CLOSED = 0
			  AND ACTIVE
			  AND NOT ONLYPROMO",
			TEN_MINUTES))
		) {
			return false;
		}
		if ($local_contests) {
			foreach ($local_contests as $partyid => $contest) {
				if ($contest['PSTAMP'] > CURRENTSTAMP) {
					continue;
				}
				$this->contest_link[$partyid] = $contest['CONTESTID'];
			}
		}
		if ($this->show_vote_userid) {
			if (false === ($local_votes = memcached_simple_hash('votenew', "
				SELECT ID, VOTEID
				FROM votenew
				WHERE TYPE = 'party'
				  AND ID IN ($this->partyidstr)
				  AND USERID = $this->show_vote_userid"))
			) {
				return false;
			}
			$this->votes = $local_votes;
		}
		if ($this->show_distance) {
			[$this->lat, $this->lon] = $this->show_distance;
		}
		if (!$this->by_org
		||	 $this->by_org !== ADE_ORGANIZATIONID
		) {
			if (false === ($local_is_ade = memcached_same_hash('connect',  "
				SELECT ASSOCID
				FROM connect
				WHERE MAINTYPE = 'organization'
				  AND MAINID = ".ADE_ORGANIZATIONID."
				  AND ASSOCTYPE = 'party'
				  AND ASSOCID IN ($this->partyidstr)"))
			) {
				return false;
			}
			$this->is_ade = $local_is_ade;
		}
		if ($party_admin) {
			if (false === ($local_have_timetable = memcached_boolean_hash('lineup', "
				SELECT DISTINCT PARTYID
				FROM lineup
				WHERE START_STAMP
				  AND PARTYID IN ($this->partyidstr)"))
			) {
				return false;
			}
			$this->have_timetable = $local_have_timetable;
		}
		return true;
	}

	private function compare(array $a, array $b): int {
		if (isset($this->order_chrono)) {
			if ($this->order_chrono & self::ORDER_REVERSE) {
				$timea = $b;
				$timeb = $a;
			} else {
				$timea = $a;
				$timeb = $b;
			}
			if ($this->order_chrono & self::ORDER_IGNORE_TIME) {
				[$ya, $ma, $da] = _getdate($timea['STAMP_TZI'] - HOUR_DAY_START * ONE_HOUR, 'UTC');
				[$yb, $mb, $db] = _getdate($timeb['STAMP_TZI'] - HOUR_DAY_START * ONE_HOUR, 'UTC');
				$diff = ($ya <=> $yb) ?: ($ma <=> $mb) ?: ($da <=> $db);
			} else {
				$diff = $timea['STAMP_TZI'] <=> $timeb['STAMP_TZI'];
			}
			if ($diff) {
				return $diff;
			}
		}
		if ($diff = $a['ACCEPTED'] <=> $b['ACCEPTED']) {
			return $diff;
		}
		switch ($this->sort_preference) {
		case 'CITY':
			if ($diff = $this->suborder['CITY'][$a['SHOW_CITYID']] <=> $this->suborder['CITY'][$b['SHOW_CITYID']]) {
				return $diff;
			}
			break;
		case 'LOCATION':
			if ($diff = $this->suborder['LOCATION'][$a['LOCATIONID']] <=> $this->suborder['LOCATION'][$b['LOCATIONID']]) {
				return $diff;
			}
			break;
		case 'VISITORS':
			if ($diff = $this->suborder['VISITORS'][$a['PARTYID']] <=> $this->suborder['VISITORS'][$b['PARTYID']]) {
				return $diff;
			}
			break;
		default:
			break;
		}
		return utf8_strcasecmp($a['NAME'], $b['NAME']);
	}

	final public function select_day(int $year, int $month, int $day): void {
		$this->date = [$year, $month, $day];
		$this->wheres[] = $this->where_start_stop(
			$daystamp  = gmmktime(HOUR_DAY_START, 0, 0, $month, $day, $year),
			$stopstamp = $daystamp + ONE_DAY
		);
		$this->daystamp  = $daystamp;
		$this->stopstamp = $stopstamp;
	}

	final public function select_year(int $year): void {
		$start_stamp = gmmktime(HOUR_DAY_START, 0, 0, 1, 1, $year);
		$stop_stamp  = gmmktime(HOUR_DAY_START, 0, 0, 1, 1, $year + 1);
		$this->wheres[] = $this->where_start_stop($start_stamp, $stop_stamp);
		$this->stopstamp = $stop_stamp;
	}

	final public function select_range(array $arr, string $start, string $stop): void {
		$start = _date_getstamp($arr, $start, true) + HOUR_DAY_START * ONE_HOUR;
		$stop  = _date_getstamp($arr, $stop,  true) + HOUR_DAY_START * ONE_HOUR;
		$this->wheres[] = $this->where_start_stop($start, $stop);
		$this->stopstamp = $stop;
	}

	final public function select_from_startstamp(int $startstamp): void {
		$this->wheres[] = 'party.STAMP >= '.$startstamp;
	}

	final public function select_from_stopstamp(int $stopstamp): void {
		$this->wheres[] = 'party.STAMP < '.$stopstamp;
		$this->stopstamp = $stopstamp;
	}

	final public function select_future(?int $offset = null, bool $allow_postponed = false): void {
		# NOTE: Default is to start 2 days in the past,
		#		so users can see the events of past 2 days, for easy access events they might have visited.
		$offset ??= -2;
		$this->future_offset = $offset;
		$this->wheres[] = '(
			party.STAMP_TZI >= '.
			(TODAYSTAMP_TZI + HOUR_DAY_START * ONE_HOUR + $offset * ONE_DAY).
			($allow_postponed ? ' OR POSTPONED' : '').')';
	}

	final public function select_coming_month(): void {
		// select yesterday + 2 + 1 month
		change_timezone('UTC');
		$start = TODAYSTAMP_TZI - 2 * ONE_DAY + HOUR_DAY_START * ONE_HOUR;
		$stop = strtotime('+1 month', $start) - 1;
		change_timezone();
		$this->wheres[] = $this->where_start_stop($start, $stop);
		$this->startstamp = $start;
		$this->stopstamp  = $stop;
	}

	final public function select_coming_week(int $weeks = 1): void {
		// select yesterday + 2 + 1 week
		change_timezone('UTC');
#		$start = TODAYSTAMP_TZI - 2 * ONE_DAY + HOUR_DAY_START * ONE_HOUR;
		$start = TODAYSTAMP_TZI + HOUR_DAY_START * ONE_HOUR;
		$stop = strtotime('+'.$weeks.' week', $start) - 1;
		change_timezone();
		$this->wheres[] = $this->where_start_stop($start, $stop);
		$this->startstamp = $start;
		$this->stopstamp  = $stop;
	}

	final public function select_between(int $start, int $stop): void {
		$this->wheres[] = $this->where_start_stop($start, $stop);
		$this->startstamp = $start;
		$this->stopstamp  = $stop;
	}

	final public function select_past(): void {
		$this->past = true;
		$stop = TODAYSTAMP_TZI + HOUR_DAY_START * ONE_HOUR;
		$this->wheres[] = 'party.STAMP_TZI < '.$stop;
		$this->stopstamp = $stop;
	}

	final public function order_chronologically(): void {
		$this->order_chrono = 0;
	}

	final public function order_chronologically_but_notime(): void {
		$this->order_chrono = self::ORDER_IGNORE_TIME;
	}

	final public function order_reverse_chronologically(): void {
		$this->order_chrono = self::ORDER_REVERSE;
	}

	final public function order_reverse_chronologically_but_notime(): void {
		$this->order_chrono = self::ORDER_REVERSE | self::ORDER_IGNORE_TIME;
	}

	function display_line(array $row, bool $first = false): ?array {
		$maybe = !empty($row['MAYBE']);
		$partyid = $row['PARTYID'];
		$party_admin = have_admin('party');

		if ($maybe
		&&	!(property_exists($this,'force_maybe') && $this->force_maybe)
		&&	(	$this->past
			||	$row['STAMP'] < CURRENTSTAMP
			&&	(require_once '_goingprocess.inc')
			&&	asked_going($partyid, $this->user_going)
			)
		) {
			return null;
		}
		if (!$this->show_filter) {
			$hidden = false;
		} elseif (is_bool($this->visible)) {
			$hidden = !$this->visible;
		} else {
			$hidden = empty($this->visible[$partyid]);
		}
		$tbody_classes = [];

		if ($this->new
		&&	$row['LAST_VALIDATED'] > CURRENTSTAMP - EVENT_PROCESSING_GRACE_PERIOD
		) {
			$tbody_classes[] = 'light';
		}
		if (!$row['ACCEPTED']) {
			$tbody_classes[] = 'unaccepted';
		} elseif (!$this->dont_mark_mine) {
			if (GOING_CERTAIN === ($going_type = going_to_party_type($partyid))) {
				$tbody_classes[] = 'party-attending';
			} elseif (
				$going_type === GOING_MAYBE
			&&	$row['STAMP'] > CURRENTSTAMP
			) {
				$tbody_classes[] = 'party-attending-maybe';
			}
		}
		$is_ad = !empty($GLOBALS['partyad_link']);
		$prefix = null;
		$offset = null;
		$stamp_tzi = $row['STAMP_TZI'];
		$duration = $row['DURATION_SECS'];
		if ($row['AT2400']) {
			--$stamp_tzi;
			++$duration;
		}
		if ( (	!$this->hide_day_prefix
			||	 $this->show_date_headers)
		&&	isset($row['DURATION_SECS'])
		) {
			static $yesterday = false;
			static $day_before_yesterday;
			static $today;
			static $tomorrow;
			static $day_after_tomorrow;
			if ($yesterday === false) {
				if (!defined('TODAYSTAMP_TZI')) {
					define('TODAYSTAMP_TZI', gmmktime(0, 0, 0));
				}
				$today = TODAYSTAMP_TZI + HOUR_DAY_START * ONE_HOUR;
				$yesterday = $today - ONE_DAY;
				$day_before_yesterday = $yesterday - ONE_DAY;
				$tomorrow = $today + ONE_DAY;
				$day_after_tomorrow = $tomorrow + ONE_DAY;
			}
			if (CURRENTSTAMP >= $row['STAMP']
			&&	CURRENTSTAMP < ($row['STAMP'] + ($row['DURATION_SECS'] ?: 4 * ONE_HOUR))
			) {
				$offset	  = 0;
				$make_fix = __('date:today');
				$prefix	  = __('date:now');
				$tbody_classes[] = 'lighter-hilited';
			} elseif ($stamp_tzi < $today) {
				if ($stamp_tzi < $yesterday) {
					if ($stamp_tzi >= $day_before_yesterday) {
						$offset = -2;
						$prefix = __('date:daybeforeyesterday');
					}
				} else {
					$offset = -1;
					$prefix = __('date:yesterday');
				}
			} elseif ($stamp_tzi < $day_after_tomorrow) {
				if ($stamp_tzi < $tomorrow) {
					$offset = 0;
					$prefix = __('date:today');
				} else {
					$offset = 1;
					$prefix = __('date:tomorrow');
				}
			}
		} elseif (
			$this->hilite_now
		&&	CURRENTSTAMP >= $row['STAMP']
		&&	CURRENTSTAMP < $row['STAMP'] + ($row['DURATION_SECS'] ?: 4 * ONE_HOUR)
		) {
			$tbody_classes[] = 'light-hilited';
		}
		$show_vevent
		=	$this->show_vevent
		&&	(	!$row['CANCELLED']
			||	$this->cancelled
			)
		&&	!$row['MOVEDID'];

		if ($this->show_date
		||	$show_vevent
#		&&	ROBOT
		) {
			$startstamp = $stamp_tzi;
			$stopstamp = $stamp_tzi + $duration;
			$start = date('c',$startstamp);
			if ($row['NOTIME']) {
				$start = preg_replace('"^(\d+-\d+-\d+)T\d+:\d+:\d+(.*)$"','$1$2',$start);
			}
			if ($this->show_date) {
				ob_start();
				?><time<?
				?> datetime="<?= $start ?>"><?
				if (isset($row['dups'])) {
					change_timezone('UTC');
					$date_store = [];
					foreach ($row['dups'] as $party) {
						[$y, $m, $d] = _getdate($party['STAMP_TZI']);
						$date_store[$y][$m][$d] = $party;
					}
					$date_parts = [];
					foreach ($date_store as $year => $months) {
						$month_parts = [];
						foreach ($months as $month => $days) {
							$day_parts = [];
							foreach ($days as $day => $party) {
								$day_parts[] = '<a href="/party/day/'.$year.'/'.$month.'/'.$day.'">'.$day.'</a>';
							}
							$month_parts[] = implode(', ',$day_parts).' '.short_month_name($month);
						}
						$date_parts[] = implode(', ',$month_parts).' '.$year;
					}
					change_timezone();

					echo implode(', ',$date_parts);
				} else {
					date_display_link($stamp_tzi,'&nbsp;',false,true,!ROBOT,true);
				}
				?></time><?
				$date_part = ob_get_clean();
			}
		}
		if ($maybe
		&&	!(property_exists($this,'force_maybe') && $this->force_maybe)
		) {
			$tbody_classes[] = 'light5';
		}
		if ($hidden) {
			$tbody_classes[] = 'hidden';
		}
		if ($row['LIVESTREAM']
#		&&	CURRENTSTAMP < $row['STAMP'] + ($row['DURATION_SECS'] ?: 2 * ONE_HOUR)
		) {
			ob_start();
			[,,, $start_hour, $start_minutes] = _getdate($row['STAMP_TZI'],'UTC');
			?> <small class="light"><?
			printf('%02d:%02d', $start_hour, $start_minutes);
			if ($row['DURATION_SECS']) {
				[,,, $stop_hour, $stop_minutes] = _getdate($row['STAMP_TZI'] + $row['DURATION_SECS'],'UTC');
				if ($row['DURATION_SECS'] > ONE_DAY) {
					?> &rarr; <?
				} else {
					?> &ndash; <?
				}
				printf('%02d:%02d', $stop_hour, $stop_minutes);
			}
			?></small><?
			$times = ob_get_clean();
		}
		?><tbody class="hl<?
		if ($is_ad) {
			?> party-spot-header<?
		}
		if ($tbody_classes) {
			?> <? echo implode(' ', $tbody_classes);
		}
		?>"<?
		if ($this->show_filter
		||	$this->use_ids
		) {
			?> id="p<?
			echo $partyid;
			if (!empty($row['MULTIDAY'])) {
				echo '.',$row['MULTIDAY'];
			}
			?>"<?
		}
		if (isset($this->suborder)) {
			?> data-order="<?=
				$this->suborder['CITY']	[$row['SHOW_CITYID']], ',',
				$this->suborder['LOCATION'][$row['LOCATIONID']], ',',
				($this->suborder['VISITORS'][$partyid] ?? '')
			?>"<?
		}
		if ($show_vevent) {
			?> itemprop="<?= match($_REQUEST['sELEMENT'] ?? null) {
				'artist'	=> 'performerIn',
				default		=> 'event',
			} ?>"<?
			?> itemscope itemtype="https://schema.org/Event"<?
		}
		?>><?
		?><tr<?
		ob_start();
		if (SMALL_SCREEN) {
			?> namerow<?
		}
		if ($special_status = (bool)$row['MOVEDID']) {
			?> notice-nb ns<?
		} elseif ($special_status = $row['POSTPONED']) {
			?> warning-nb ns<?
		} elseif ($special_status = $row['CANCELLED']) {
			 ?> error-nb ns<?
		}
		if ($this->hide_need_check
		&&	empty($row['PLACEHOLDER'])
		&&	empty($row['HAS_LINEUP'])
		) {
			?> hilited-light-orange<?
		}
		if (# Only for accepted events, otherwise we're going to double 'light' the row
			$row['ACCEPTED']
		&&	$prefix
		&&	CURRENTSTAMP > $row['STAMP'] + ($row['DURATION_SECS'] ?: 6 * ONE_HOUR)
		&&	!empty($this->future_offset)
		) {
			# finished events
			$row['LIGHT'] = true;
			?> light<?
		}
		if ($data = ob_get_clean()) {
			?> class="<?= $data ?>"<?
		}
		?>><?
		if ($this->show_date_first
		&&	$this->show_date
		&&	$date_part
		) {
			?><td class="right"><?= $date_part ?></td><?
		}
		if ($this->show_customer
		&&	have_admin()
		) {
			require_once '_customer.inc';
			if (!($parents = get_event_customer_parents($partyid))) {
				?><td><?
			} else {
				?><td class="center nowrap nopad"><?
				$icons = [];
				foreach ($parents as $parent) {
					$icons[$parent] = "get_{$parent}_icon"();
				}
				if ($icons) {
					echo implode(' ', $icons);
				}
			}
			?></td><?
		}
		?><td<?
		if (!SMALL_SCREEN
		&&	$this->show_location
		&&	$this->show_city
		) {
			?> style="max-width: <?= $this->show_city_separate ? 40 : 60 ?>%;"<?
		} elseif (SMALL_SCREEN) {
			?> colspan="<?= $this->get_colcnt() ?>"<?
		}
		?>><?

		$show_win =
			isset($this->contest_link[$partyid])
		&&	!$row['CANCELLED']
		&&	!$row['MOVEDID'];

		$show_free =
			$row['FREE_ENTRANCE']
		&&	$row['STAMP'] >= TODAYSTAMP - ONE_DAY
		&&	!$row['CANCELLED']
		&&	!$row['MOVEDID']
		&&	$row['LIVESTREAM'] !== 'only';	# almost always free

		if ($this->now_and_soon_stamp) {
			static $__percoord = null;
			if ($__percoord === null) {
				$__percoord = [];
				foreach ($this->partylist as $p) {
					$__percoord[$p['LATITUDE'].','.$p['LONGITUDE']][$p['PARTYID']] = $p;
				}			}
			$coords = $row['LATITUDE'].','.$row['LONGITUDE'];
			ob_start();
			?>ptr lmrgn" onclick="focusOnParty(<?= crc32(implodekeys(',',$__percoord[$coords])) ?>,<?= $coords ?>)<?
			$rarr = ob_get_clean();

			?><span class="<?
			if (SMALL_SCREEN) {
				?>bold <?
			}
			?>r <?= $rarr ?>"><?
			require_once '_maplink.inc';
			show_map_image('minimap');
			?></span><?
		}

		if ($show_vevent) {
			require_once '_party.inc';
			show_vevent($row);
			?><meta itemprop="startDate" content="<?= $start ?>" /><?

			if (!$row['NOTIME']) {
				if (!$duration) {
					$duration = 6 * ONE_HOUR;
				}
				?><meta itemprop="duration" content="<?= get_iso8601_duration($duration) ?>" /><?
				# endDate is interpreted as inclusive in Google, so make sure it is 1 second less than start + duration
				?><meta itemprop="endDate" content="<?= date('c', $stopstamp - 1) ?>" /><?
			}
		}
		if ($prefix
		&&	!$this->one_table
		&&	!$this->show_date_headers
		) {
			?><span class="small light6"><?= $prefix ?>:</span> <?
		}
		if (!empty($row['IS_HOST'])
		&&	 empty($row['IS_ORG'])
		) {
			?><span class="small light6"><?= __('prefix:as_host(area)') ?>: </span><?
		}

		?><a<?
		if (SMALL_SCREEN || $show_win) {
			?> class="<?
			if (SMALL_SCREEN) {
				?>bold <?
			}
			if ($show_win) {
				?>winlink <?
			}
			?>"<?
		}
		?> href="<?=
			!$is_ad
		?	get_element_href('party',$partyid,$row['NAME'])
		:	$GLOBALS['partyad_link'];
		?>"><?

		if ($show_vevent) {
			?><meta itemprop="url" content="<?= FULL_IMAGES_HOST.get_element_href('party',$partyid,$row['NAME']) ?>" /><?
			?><span itemprop="name"><?
		}

		echo escape_utf8($row['NAME']);

		if ($row['EDITION']) {
			?> <small>#<?= $row['EDITION'] ?></small><?
		}

		if (!$this->hide_subtitles
		&&	$row['SUBTITLE']
		) {
			?> <small><?
			if (!subtitle_no_middot($row['SUBTITLE'])) {
				?>&middot; <?
			}
			if ($small_sub = mb_strlen($row['SUBTITLE']) <= 40) {
				?><span class="nowrap"><?
			}
			echo escape_utf8($row['SUBTITLE']);
			if ($small_sub) {
				?></span><?
			}
			?></small><?
		}
		if ($show_vevent) {
			?></span><?
		}
		?></a><?

		if ($party_admin
		&&	!$row['CANCELLED']
		&&	!$row['POSTPONED']
		&&	$row['STAMP'] < 1603231200				# 2020-10-21
		&&	$row['STAMP'] > 1601503200				# 2020-10-01
		&&	in_array($row['COUNTRYID'], [1, 6], strict: true)	# BE & NL
		) {
			if (empty($row['TIMEZONE'])) {
				change_timezone('Europe/Amsterdam');
			}
			[,,, $hour] = _getdate($row['STAMP'] + $row['DURATION_SECS']);
			?> <span class="bold<?
			if ($hour > ($row['COUNTRYID'] === 1 ? 22 : 23)
			||	$hour <= 8
			) {
				?> error<?
			}
			?>"><?
			_time_display($row['STAMP']);
			if ($row['DURATION_SECS']) {
				?> &ndash; <?
				_time_display($row['STAMP'] + $row['DURATION_SECS']);
			}
			if (empty($row['TIMEZONE'])) {
				change_timezone();
			}
			?></span><?
		}
		if (!$this->only_livestreams
		&&	$row['LIVESTREAM'] === 'only'
		) {
			?> <span class="notice-nb">= <?= element_name('livestream') ?></span><?
		}
		if ($row['SOLD_OUT']) {
			?> <img class="lower colorless icon" alt="<?= $alt = __('status:sold_out') ?>" title="<?= $alt ?>" src="<?= STATIC_HOST ?>/images/lock<?= is_high_res() ?>.png" /><?
		}
		if ($special_status
		&&	!$this->cancelled
		) {
			if ($row['CANCELLED']) {
				?> <small class="bold error">(<?= __('status:cancelled') ?>)</small><?
			}
			if ($row['POSTPONED']) {
				?> <small class="bold warning">(<?= __('status:postponed') ?>)</small><?
			}
			if ($row['MOVEDID']) {
				?> <small class="bold notice">(<a href="<?= get_element_href('party', $row['MOVEDID']) ?>"><?= __('status:moved') ?></a>)</small><?
			}
		}

		if ($party_admin
		&&	$row['STAMP'] > CURRENTSTAMP
		&&	!$row['PLACEHOLDER']
		) {
			$has_lineup = !empty($row['HAS_LINEUP']);
			if (!empty($row['LINEUP_NEEDUPDATE'])
			||	!$has_lineup
			) {
				show_warning_icon(__('status:lineup_incomplete'), 'lineupimgneedupd', 'light');
			}
			if (isset($this->have_timetable[$partyid])) {
				?> <span class="emoji">&#128336;</span><?
			}
		}

		if (!empty($row['COUNTRYID'])
		&&	!$this->hide_flag
		&&	(	!$this->hide_country
			||	 $this->hide_country !== $row['COUNTRYID']
			)
		) {
			$show = true;
			if (!$this->hide_country) {
				static $__current_countryid = false;
				if ($__current_countryid === false) {
					$__current_countryid = ($currentcity = get_current_city()) ? $currentcity['COUNTRYID'] : null;
				}
				if (!$__current_countryid
				||	 $__current_countryid === $row['COUNTRYID']
				) {
					$show = false;
				}
			}
			if ($show) {
				require_once '_countryflag.inc';
				show_country_flag($row['COUNTRYID'],'light lmrgn small');
			}
		}
		if (isset($this->favinfo)
		&&	(	empty($row['LINEUP_PSTAMP'])
			||	$row['LINEUP_PSTAMP'] < CURRENTSTAMP
			)
		) {
			$this->favinfo->show_stars($partyid);
		}
		if (!$this->hide_separators
		&&	(	isset($this->have_images[$partyid])
			||	isset($this->campresent[$partyid])
			||	isset($this->have_videos[$partyid])
			)
		) {
			?><div class="lmrgn ib"><?
			$this->show_photo_cams($partyid);
			$this->show_video_cams($partyid);
			?></div><?
		}
		if ($this->hide_separators
		||	SMALL_SCREEN
		&&	(	$this->show_buddy_hearts
			||	$this->show_buddies)
		&&	(	isset($this->have_images[$partyid])
			||	isset($this->campresent [$partyid])
			||	isset($this->have_videos[$partyid]))
		) {
			$this->show_photo_cams($partyid);
			$this->show_video_cams($partyid);
		}
		if ($show_people_to_small
		=	SMALL_SCREEN
		&&	(	$this->show_people
			||	!empty($this->allbuddycnt)
			)
		) {
			$pplparts = [];
			if (!empty($this->allbuddycnt[$partyid])) {
				ob_start();
				$this->show_buddy_all_count($partyid);
				if ($data = ob_get_clean()) {
					$pplparts[] = $data;
				}
			}
			ob_start();
			$ppl = $this->show_all_count($row);
			if ($data = ob_get_clean()) {
				$pplparts[] = $data;
			}
		}
		if ($show_free) {
			?> <span class="free-entrance small"><?= __('cost:free') ?></span><?
		}
		if ($party_admin) {
			require_once '_presale.inc';
			show_no_presale_indication($row);
			if (!($have_fb = db_single('fbid', 'SELECT FBID FROM fbid WHERE ELEMENT = "party" AND ID = '.$partyid))) {
				require_once 'defines/facebook.inc';
				?> <? echo get_facebook_icon('redhue');
			}
		}
		if ($show_win) {
			?><a<?
			?> class="<? if (!SMALL_SCREEN) { ?>r <? } ?>win lmrgn"<?
			?> href="<?= get_element_href('contest', $this->contest_link[$partyid]) ?>"><?= 	__('partylist:win')
			?></a><?
		}
		if ($this->show_vote_userid) {
			ob_start();
			if (!$row['CANCELLED']
			&&	!$row['MOVEDID']
			&&	($voteid = $this->votes[$partyid] ?? 0)	# ignore voteid 0 (no vote)
			) {
				include_style('votes');
				?><span class="vote text-<?= vote_bar_name($voteid) ?>"><?= vote_name($voteid) ?></span><?
			}
			$votepart = ob_get_clean();
		}
		if (SMALL_SCREEN
		&&	$this->show_date
		&&	!$this->show_date_first
		) {
			?><div class="r lmrgn"><?= $date_part ?></div><?
		}
		if ($this->show_relation
		&&	isset($this->relations[$partyid])
		) {
			require_once '_star.inc';
			require_once '_bubble.inc';
			$rels = [];
			foreach ($this->relations[$partyid] as $relationid) {
				$rels[] = get_element_link('relation',$relationid);
			}
			?> <?
			$bubble = new bubble(BUBBLE_CLEAN | BUBBLE_CURSOR);
			$bubble->catcher(get_crown('relation'));
			$bubble->content(implode('<br />',$rels));
			$bubble->display();
		}
		if (!empty($this->is_ade[$partyid])) {
			?><div class="ADE lmrgn r ns"><?
			?><a href="<?= get_element_href('organization', ADE_ORGANIZATIONID) ?>">ADE</a></div><?
		}
		if (!empty($times)) {
			?><div class="r lmrgn"><?= $times ?></div><?
		}
		?></td><?
		if ($this->hide_need_check) {
			?><td><?
			if (empty($row['PLACEHOLDER'])
			&&	empty($row['HAS_LINEUP'])
			) {
				?><span class="warning"><?= element_name('no_lineup') ?></span><?
			}
			?></td><?
		}
		if (SMALL_SCREEN) {
			?></tr><?
			$this->show_lineup_row($row);
			?><tr class="nsmore"><?
		}
		if ($this->show_distance) {
			$dst = calculate_distance($this->lat,$this->lon,$row['LATITUDE'],$row['LONGITUDE']);
			if ($this->show_sort_headers) {
				set_cell_value(((int)$dst) * 10);
			}
			?><td class="nowrap right" style="width: 5.5em;"><?
			printf('%.1f',$dst) ?> <?= __('abbr:kilometer')
			?></td><?
		}
		if ($this->show_time_offset) {
			ob_start();
			if ($this->now_and_soon_stamp >= $row['STAMP']) {
				$remaining = $row['STAMP'] + ($row['DURATION_SECS'] ?: 4 * ONE_HOUR) - $this->now_and_soon_stamp;
				echo round($remaining / 3600,1);
				echo element_name('h(our)');
				$val = -(10000-$remaining/3600);
			} else {
				if ($row['AT2400']) {
					?>24:00<?
				} else {
					change_timezone('UTC');
					[,,, $h, $m] = _getdate($row['STAMP_TZI']);
					change_timezone();
					printf('%02d:%02d', $h, $m);
				}
				$val = $row['STAMP'] - $this->now_and_soon_stamp;
			}
			$celldata = ob_get_clean();
			if ($this->show_sort_headers) {
				set_cell_value($val);
			}
			?><td class="nowrap center spacey" style="width: 5.5em;"><?= $celldata ?></td><?
		}
		if (!SMALL_SCREEN
		&&	(	$this->show_people
			||	!empty($this->allbuddycnt)
			)
		) {
			if (!empty($this->allbuddycnt)) {
				?><td class="right hpad nowrap"><?
				$this->show_buddy_all_count($partyid);
				?></td><?
			}
			?><td class="right rpad nowrap"><?
			$ppl = $this->show_all_count($row);
			?></td><?
		}

		if (!SMALL_SCREEN
		&&	$this->show_vote_userid
		) {
			layout_start_cell();
			echo $votepart;
			layout_stop_cell();
		}

		$shown_location_microdata = false;

		if ($this->show_location
		||	$this->show_city
		) {
			$cityid = $row['SHOW_CITYID'] ?? 0;
			$citstr = $cityid && $this->show_city ? get_element_link('city', $cityid) : '';

			$locstr = null;

			?><td><?
			if ($this->show_location) {
				if ($show_vevent) {
					$shown_location_microdata = true;
					$this->show_location_microdata($row);
				}
				ob_start();
				echo $this->get_location($row);
				$locstr = ob_get_clean();
			}

			echo $locstr;

			if (SMALL_SCREEN) {
				if (!$this->now_and_soon_stamp) {
					if ($this->show_city
					&&	$citstr
					) {
						if ($locstr) {
							?>, <?
						}
						echo $citstr;
					}
				} elseif ($citstr) {
					?><div class="r nowrap"><?= $citstr ?></div><?
				}
				?></td><?
			} else {
				if ($this->show_city) {
					$city_class = '';
					if ($this->show_city_separate) {
						?></td><?
						if ($first
						&&	$this->now_and_soon_stamp
						) {
							?><td style="width: 15em;"><?
						}
						?><td><?
					} else {
						if ($locstr) {
							?>, <?
						}
						$city_class = ' light7';
					}
					?><span class="nowrap<?= $city_class ?>"><?= $citstr ?></span><?
				}
				?></td><?
			}
		}
		$parts = [];
		if ($this->show_creator) {
			$parts[] = $row['USERID'] ? get_element_link('user', $row['USERID']) : null;
		}
		if ($this->show_last_modifier) {
			$parts[] = $row['MUSERID'] ? get_element_link('user', $row['MUSERID']) : null;
		}
		if ($this->show_last_validator) {
			ob_start();
			if ($row['LAST_VALIDATOR']) {
				?><span style="max-width: 25em;" class="nowrap rpad"><?=
					get_element_link('user', $row['LAST_VALIDATOR'])
				?></span><?
			}
			$parts[] = ob_get_clean() ?: null;
		}
		if ($parts) {
			if (SMALL_SCREEN) {
				$ok_parts = [];
				foreach ($parts as $part) {
					if (!$part) {
						$ok_parts[] = $part;
					}
				}
				if ($this->show_creation_date) {
					$ok_parts[] = _datetime_get($row['CSTAMP'], short: true, hide_year: true);
				}
				if ($this->show_last_validation_date) {
					$ok_parts[] = _datetime_get($row['LAST_VALIDATED'], short: true, hide_year: true);
				}
				echo implode(', ', $ok_parts);

			} else {
				?><td><?= implode('</td><td>', $parts) ?></td><?
				if ($this->show_creation_date) {
					layout_cell(_datetime_get($row['CSTAMP'], short: true, hide_year: true), class: 'right nowrap');
				}
				if ($this->show_last_validation_date) {
					layout_cell(_datetime_get($row['LAST_VALIDATED'], short: true, hide_year: true), class: 'right nowrap');
				}
			}
		}
		if (SMALL_SCREEN) {
			if ($this->show_vote_userid) {
				if (!$votepart) {
					?><td></td><?
				} else {
					?><td class="right nowrap"><?= $votepart ?></td><?
				}
			}
		} elseif (
			 $this->show_date
		&&	!$this->show_date_first
		) {
			?><td class="right"><?= $date_part ?></td><?
		}
		if ($show_people_to_small) {
			if (empty($pplparts)) {
				?><td></td><?
			} else {
				?><td class="right"><?
				echo implode(' <span class="plsep">/</span> ', $pplparts);
				unset($pplparts);
				$icon = $this->get_platforms_icon($row, $ppl);
				if ($icon) {
					echo ' ', $icon;
				}
				?></td><?
			}
		}
		?></tr><?

		if (!$shown_location_microdata
		&&	$show_vevent
		) {
			?><tr class="hidden"><?
			?><td class="hidden"><?
			$this->show_location_microdata($row);
			?></td><?
			?></tr><?
		}
		if (!SMALL_SCREEN) {
			$this->show_lineup_row($row);
		}
		if (!$is_ad
		&&	!$this->hide_ads
		&&	!($this->in_agenda ??= ($_REQUEST['sELEMENT'] ?? null) === 'agenda')
		) {
			show_ad_placeholder_tr($this->get_colcnt());
		}
		?></tbody><?

		return [$offset, $make_fix ?? $prefix];
	}

	function show_lineup_row(array $party): void {
		static $__done;

		if (!$this->show_lineups
		&&	!$this->show_lineups_form
		||	isset($__done[$party['PARTYID']])
		) {
			return;
		}

		$have_user = have_user();

		require_once '_favourite.inc';
		static $__favs = 0;

		$colcnt = $this->get_colcnt();

		$freshen_cache =
			isset($_REQUEST['NOMEMCACHE'])
		||	partylist_refresh_needed();

		if ($cache_key
		=	!$have_user
		||	!(	$__favs === 0
			?	($__favs = $have_user ? get_favourites(CURRENTUSERID, 'artist') : false)
			:	$__favs
			)
		?	PARTY_LINEUP_ROW.$party['PARTYID'].':cols='.$colcnt.':'.(!$this->show_lineups ? 'showlineups' : '')
		:	PARTY_LINEUP_ROW.$party['PARTYID'].':cols='.$colcnt.':'.(!$this->show_lineups ? 'showlineups' : '').CURRENTUSERID
		) {
			if (!$freshen_cache
			&&	(false !== ($party_lineup_row = memcached_get($cache_key)))
			) {
				echo $party_lineup_row;
				return;
			}
			ob_start();
		}

		if ($lineup = get_party_lineup($party['PARTYID'])) {
			$__done[$party['PARTYID']] = true;
			foreach ($lineup as $artistid => &$name) {
				$link = get_element_link('artist', $artistid, $name);
				$name =	!isset($__favs[$artistid])
				?	$link
				:	str_replace('<a ','<a class="win" ',$link);
			}
			unset($name);
			$remainder = null;
			if (count($lineup) > 20) {
				# get fancounts
				static $__fancnts = [];
				if ($todo = array_diff_key($lineup, $__fancnts)) {
					foreach ($todo as $artistid => $name) {
						if (!isset($__fancnts[$artistid])) {
							$__fancnts[$artistid] = get_fan_count('artist', $artistid);
						}
					}
					arsort($__fancnts);
				}
				# find favs
				$favs = [];
				if ($__favs) {
					$favs = array_intersect_key($lineup, $__favs);
				}
				$show_max = 20;
				$choose_from = array_diff_key($lineup, $favs);
				if (($favcnt = count($favs)) < $show_max) {
					uksort($lineup, function(int $a, int $b) use ($__fancnts): int {
						return $__fancnts[$b] - $__fancnts[$a];
					});
					$lineup	= $favs + array_slice($choose_from, 0, $show_max - $favcnt, true);
					$remainder =		 array_slice($choose_from, $show_max - $favcnt, null, true);
				} else {
					$lineup	= array_slice($favs, 0, $show_max, true);
					$remainder = array_slice($favs, $show_max, null, true) + $choose_from;
				}
			}
			unset($name);

			?><tr class="<?
			if (!$this->show_lineups) {
				?>hidden <?
			}
			if (!empty($party['LIGHT'])) {
				?>light <?
			}
			?>lineuprow"><?
			?><td colspan="<?= $this->get_colcnt() ?>"><?

			if (isset($party['MULTIDAY'])
			||	!empty($party['HAVE_PARTY_CONNECTS'])
			&&	single_lineup_multi_days($party)
			) {
				?><span class="notice-nb"><?= __('partylist:spread_over_days') ?></span>: <?
			}
			echo implode(', ', $lineup);
			if ($remainder) {
				?>, <span<?
				?> class="unhideanchor italic"<?
				?> onclick="swapdisplay(this,this.nextSibling)"<?
				?>><?= __('partylist:uncollapse:and_x_remaining_artists',['CNT' => count($remainder)]) ?> &rarr;</span><?
				?><span class="hidden"><?= implode(', ', $remainder) ?></span><?
			}
			if (!empty($party['LINEUP_NEEDUPDATE'])) {
				?> (<?= __('partylist:not_complete') ?>)<?
			}
			?></td></tr><?
		}
		memcached_set($cache_key, ob_get_flush(), ONE_HOUR);
	}

	function show_video_cams(int $partyid): void {
		if (!isset($this->have_videos[$partyid])) {
			return;
		}
		static $__stored = false;
		static $__meta   = false;
		$need = crc32($this->partyidstr);
		if (!$__stored
		||	 $__stored !== $need
		) {
			$__stored = $need;
			$videoids = [];
			require_once '_videometa.inc';
			foreach ($this->have_videos as $local_partyid => $videos) {
				foreach ($videos as $ndx => $video) {
					if ($video['STATUS'] !== 'active') {
						unset($this->have_videos[$local_partyid][$ndx]);
						continue;
					}
					$videoids[] = $video['VIDEOID'];
				}
			}
			$__meta = $videoids ? new videometa(implode(',', $videoids)) : null;
		}
		if (!$__meta) {
			mail_log('no video meta(s) for video(s) in partylist', get_defined_vars());
			return;
		}
		if (!($vidcnt = count($this->have_videos[$partyid]))) {
			return;
		}
		?> <?
		require_once '_bubble.inc';
		$videos_per_title = [];
		foreach ($this->have_videos[$partyid] as &$video) {
			$videoid = $video['VIDEOID'];
			$conns = $__meta->get_connections_for_title($videoid);
			$title = $conns['title'] ?? null;
			if (!empty($conns['type'])) {
				if ($title) {
					$title .= ' '.MIDDLE_DOT_ENTITY.' ';
				}
				$title .= $conns['type'];
			}
			if (!$title) {
				$lower_title = '';
			} elseif (!($new_title = mb_strtolower($title))) {
				$lower_title = $title;
			} else {
				$lower_title = $new_title;
			}
			$video['LOWER_TITLE'] = $lower_title;
			assert(is_string($lower_title));
			$videos_per_title[$lower_title] = 1 + ($videos_per_title[$lower_title] ?? 0);
		}
		unset($video);
		$bubble = new bubble(BUBBLE_CLEAN);
		$bubble->catcher();
		?><a class="light6" target="_blank" href="/party/<?= $partyid ?>/videos#videos"><?
		echo get_videocamera_icon();
		if ($vidcnt > 1) {
			?> <small><?= $vidcnt ?></small><?
		}
		?></a> <?
		$bubble->content();
		foreach ($this->have_videos[$partyid] as $video) {
			$videoid = $video['VIDEOID'];
			$full_title = $__meta->get_flat_title($videoid,true);
			?><a target="_blank" href="<?= get_element_href('video', $videoid, $full_title) ?>"><?
			echo make_all_html($full_title, flags: UBB_UTF8);
			if (($cnt = $videos_per_title[$video['LOWER_TITLE']]) >= 1) {
				?> <? echo $cnt + 1;
			}
			?></a> <small class="light">(<? show_duration($video['DURATION']) ?>)</small><br /><?
		}
		$bubble->display();
		?> <?
	}

	function show_photo_cams(int $partyid): void {
		if (isset($this->have_images[$partyid])) {
			$camcnt = 0;
			ob_start();
			foreach ($this->have_images[$partyid] as $gallery) {
				?><div><?
				?><a target="_blank" href="<?= get_element_href('gallery',$gallery['GALLERYID']) ?>"><?=
					$gallery['CNT']
				?> <?=
					(	$gallery['USERID']
					||	$gallery['PHOTOGRAPHER'])
				&&	!$gallery['ANONYMOUS']
				?	__('attrib:photos_by' ,['NICK' => $gallery['PHOTOGRAPHER'] ?: memcached_nick($gallery['USERID'])])
				:	element_plural_name('photo')
				?></a><?
				?></div><?
				++$camcnt;
			}
			$content = ob_get_clean();

			require_once '_bubble.inc';

			$bubble = new bubble(BUBBLE_CLEAN);
			$bubble->catcher();
			?> <a target="_blank" class="light6" href="<?
				if ($camcnt > 1) {
					echo get_element_href('party',$partyid),'#photos';
				} else {
					echo get_element_href('gallery',$gallery['GALLERYID']);
				}
			?>"><?
			?><img class="cam" alt="<?= element_plural_name('photo') ?>" src="<?= STATIC_HOST ?>/images/cam<?= is_high_res() ?>.png" /> <?
			if ($camcnt > 1) {
				?> <small><?= $camcnt ?></small><?
			}
			?></a> <?
			$bubble->content();
			echo $content;
			$bubble->display();

		} elseif (isset($this->campresent[$partyid])) {
			?> <img class="<?
			if (!$this->campresent[$partyid]) {
				?>light <?
			}
			?>cam"<?
			?> src="<?= STATIC_HOST ?>/images/cam<?= is_high_res() ?>.png"<?
			?> alt="<?= $title = element_plural_name('photo') ?>"<?
			?> title="<?= $title ?>"><?
		}
	}

	function display(): void {
		require_once '_partylistfilter.inc';
		require_once '_layout.inc';
		require_once '_ad.inc';
		require_once '_partyinfo.inc';

		if (!$this->partylist) {
			return;
		}
		if ($this->show_filter) {
			show_partylist_filters_placeholder($this->invisilist ?? []);
			$this->visible = get_partylist_visibilities($this->partyidstr, true);

			db_insert('agendafilter', '
			INSERT INTO agendafilter SET
				STAMP	= UNIX_TIMESTAMP(),
				USERID	= '.CURRENTUSERID.',
				IPBIN	= "'.addslashes(CURRENTIPBIN).'",
				FILTER	= "'.addslashes(json_encode(db_single_assoc('partyfilter', 'SELECT * FROM partyfilter WHERE IDENTID = '.CURRENTIDENTID))).'"'
			);
		}
		if ($this->show_lineups_form) {
			if (have_user()) {
				$who = 'user';
				$who_id = CURRENTUSERID;
			} else {
				$who = 'identity';
				$who_id = CURRENTIDENTID;
			}

			$state = db_single('show_lineups', '
				SELECT STATE
				FROM show_lineups
				WHERE WHO="'.$who.'"
				  AND ID='.$who_id
			);
			if ($state === false) {
				return;
			}

			$this->show_lineups = !$state || $state === 'yes';

			include_js('js/partylist');
			layout_open_box('white');
			?><form<?
			?> onsubmit="return false"<?
			?>><?
			?><label class="<?
			if (!$this->show_lineups) {
				?>not-<?
			}
			?>bold-hilited"><?
			?><input<?
			if ($this->show_lineups) {
				?> checked<?
			}
			?> onclick="do_inline('POST','/showlineups.act','RETURN',null,'YES='+(this.checked ? '1' : '0'));Pf.showLineupRows(this.checked)"<?
			?> type="checkbox"<?
			?> name="LINEUPS"<?
			?> class="upLite"<?
			?>><?
			?> <?= __('action:show_lineups')
			?></label><?
			?></form><?

			layout_close_box();
		}

		static $__ad_partyids;
		if (!isset($__ad_partyids)) {
			if (!$this->hide_ads
			&&	(	!$this->user_going
				||	 $this->user_going !== CURRENTUSERID)
			) {
				$__ad_partyids = obtain_ad(ADPOS_PARTYLIST) ?: false;
			}
		}

		if ($this->one_table) {
			$local_counts = [];
			$total_days = 0;
			$multi_days = 0;
			change_timezone('UTC');
			foreach ($this->partylist as $party) {
				$corrected_stamp = offset_party_stamp($party['STAMP_TZI']);
				[$year, $month, $day] = _getdate($corrected_stamp);
				if (!isset($local_counts[$year][$month][$day])) {
					$local_counts[$year][$month][$day] = 1;
					++$total_days;
				} elseif (++$local_counts[$year][$month][$day] >= 3) {
					++$multi_days;
				}
			}
			change_timezone();
			if ($this->show_date = ($multi_days / $total_days) < 0.5) {
				unset($this->one_table, $this->show_date_headers);
			} else {
				$this->show_date_headers = true;
			}
		}
		$this->tableclassstr = 'partylist fw nocellbrd'.($this->new ? ' zoom-smallish' : /* trying without vtop: */ '' );

		if ($this->nomargin) {
			$this->tableclassstr .= ' nomargin';
		}
		if ($this->show_filter) {
			$this->tableclassstr .= ' sortable';
		}

		if (!($show_date_headers = $this->show_date_headers)) {
			?><table class="<?= $this->tableclassstr ?>"><?
		}

		$done_ad	 = [];
		$displaylist = [];
		$prefixes	 = [];

		foreach ($this->partylist as $party) {
			if (isset($this->skip)) {
				switch ($this->skip) {
				case 'certain':
					if (!$party['MAYBE']) {
						continue 2;
					}
					break;
				case 'maybe':
					if ($party['MAYBE']) {
						continue 2;
					}
					break;
				}
			}
			$partyid = $party['PARTYID'];
			$corrected_stamp = offset_party_stamp($party['STAMP_TZI']);

			// if (have_element($_REQUEST, 'sELEMENT', ['party', 'agenda'], true)
			// &&	!$_REQUEST['ACTION']
			// &&	isset($this->startstamp_tzi)
			// &&	$party['STAMP_TZI'] < $this->startstamp_tzi
			// ) {
			// 	# long events starting at now - 3 days and overlap to now -2
			// 	# solution not optima (still showing empty table)
			// 	continue;
			// }

			# correct for multi day events, use daystamp
			[$year, $month, $day,,,, $weekday] = _getdate($this->daystamp ?? $corrected_stamp);

			if ($show_date_headers) {
				ob_start();
			}
			ob_start();
			$GLOBALS['partyad_link'] = null;

			if ($__ad_partyids
			&&	isset($__ad_partyids[$partyid])
			&&	!isset($done_ad[$partyid])
			) {
				$done_ad[$partyid] = true;
				$hidden =
					$this->show_filter
				&&	(	$this->visible === false
					||	is_array($this->visible)
					&&	empty($this->visible[$partyid]));

				# NOTE: ordering!!
				?><tbody class="sort-ignore<?
				if ($hidden) {
					?> hidden<?
				}
				?>"><?
				?><tr class="party-spot" id="partyl<?= $partyid ?>"><td colspan="<?= $this->get_colcnt() ?>"><?
				display_ad($__ad_partyids[$partyid]);
				?></td></tr><?
				?></tbody><?
			}
			$partyad = ob_get_clean();

			$prefix = $this->display_line($party,$show_date_headers && !isset($displaylist[$year][$month][$day]));

			$GLOBALS['partyad_link'] = null;

			echo $partyad;

			if ($show_date_headers
			&&	($data = ob_get_clean())
			) {
				$displaylist[$year][$month][$day] = ($displaylist[$year][$month][$day] ?? '').$data;

				$this->daycounts[$year][$month][$day] = 1 + (
				$this->daycounts[$year][$month][$day] ?? 0);
				$this->weekday	[$year][$month][$day] = $weekday;

				if ($this->show_filter
				&&	(	$this->visible === true
					||	$this->visible && !empty($this->visible[$party['PARTYID']])
					)
				) {
					$this->month_visible[$year][$month] =
					$this->day_visible	[$year][$month][$day] = true;
				}
				if ($prefix) {
					$prefixes[$year][$month][$day] = $prefix;
					$offset = $prefix[0];
					if ($offset !== null) {
						$this->counts[$offset] =  1 + (
						$this->counts[$offset] ?? 0);
						if (!empty($this->visible[$partyid])) {
							$this->visiblecnt[$offset] =  1 + (
							$this->visiblecnt[$offset] ?? 0);
						}
					}
				}
			}
		}

		if ($show_date_headers) {
			if (isset($this->date)) {
				[$year, $month, $day] = $this->date;
				$this->show_header	  ($year, $month, false);
				$this->show_day_header($year, $month, $day);
				echo $displaylist[$year][$month][$day];
				?></table><?

			} elseif (!empty($displaylist)) {
				if ($one = $this->one_table) {
					?><table class="<?= $this->tableclassstr ?>"><?
				}
				$shown = 0;
				foreach ($displaylist as $year => $monthlist) {
					foreach ($monthlist as $month => $daylist) {
						if (!$one) {
							$this->show_header($year,$month);
						}
						foreach ($daylist as $day => $data) {
							$prefix = $prefixes[$year][$month][$day] ?? null;
							$offset = $prefix ? $prefix[0] : 0;
							$this->show_day_header($year,$month,$day,$prefix,$shown);

							if ($offset < 0
							&&	!$this->past
							&&	!$this->none_hidden
							) {
								$data = str_replace('<tbody class="','<tbody class="hidden offset'.$offset.' ',$data);
							} else {
								# non hidden date
								$shown += $this->daycounts[$year][$month][$day];
							}
							echo $data;
						}
						if (!$one) {
							?></table><?
						}
					}
				}
				if ($one) {
					?></table><?
				}
			} else {
				mail_log('displaylist is empty');
			}
		} else {
			?></table><?
		}
	}

	private function show_header(
		int  $year,
		int	 $month,
		bool $show_month = true,
	): void {
		$hidden = null;
		if (!$this->hide_month_header) {
			$hidden =
				!$this->show_filter
			||	isset($this->month_visible[$year][$month])
			?	null
			:	'hidden ';

/*			if ($show_mfonth) {
				?><h2 class="<?= $hidden ?>party-month" id="<?= $year,'_',$month ?>"><?
				?><a href="/agenda/<?= $year ?>/<?= $month ?>"><?= _month_name($month),' ',$year?></a><?
				?></h2><?
			}*/

			if ($this->show_sort_headers) {
				include_js('js/sorttable');
			}
		}
		?><table class="<?= $hidden,$this->tableclassstr ?>"><?
	}

	private function get_colcnt(): int {
		if (isset($this->column_count)) {
			return $this->column_count;
		}
		$column_count = 1;

		if (SMALL_SCREEN) {
			if ($this->show_city
			&&	$this->show_city_separate
			) {
				++$column_count;
			}
			if ($this->show_vote_userid) {
				++$column_count;
			}
			if ($this->show_people
			||	isset($this->allbuddycnt)
			) {
				++$column_count;
			}
			if ($this->show_distance) {
				++$column_count;
			}
			if ($this->show_time_offset) {
				++$column_count;
			}
			if ($this->show_date) {
				++$column_count;
			}
			if ($this->show_creation_date) {
				++$column_count;
			}
			if ($this->show_customer) {
				++$column_count;
			}
			if ($this->show_last_validation_date) {
				++$column_count;
			}
			return $column_count;
		}

		if ($this->show_people
		||	!empty($this->allbuddycnt)
		) {
			++$column_count;
			if (!empty($this->allbuddycnt)) {
				++$column_count;
			}
		}
		if ($this->show_location) {
			++$column_count;
		}
		if ($this->show_city
		&&	$this->show_city_separate
		) {
			++$column_count;
		}
		if ($this->show_distance) {
			++$column_count;
		}
		if ($this->show_time_offset) {
			++$column_count;
		}
		if ($this->show_creation_date) {
			++$column_count;
		}
		if ($this->show_creator) {
			++$column_count;
		}
		if ($this->show_last_modifier) {
			++$column_count;
		}
		if ($this->show_last_validator) {
			++$column_count;
		}
		if ($this->show_date) {
			++$column_count;
		}
		if ($this->show_customer) {
			++$column_count;
		}
		if ($this->show_last_validation_date) {
			++$column_count;
		}
		return $this->column_count = $column_count;
	}

	private function show_day_header(int $year, int $month, int $day, ?array $prefixinfo = null, bool $shown = false): void {
		$colcnt = $this->get_colcnt();
		$offset = $prefix = null;
		if ($prefixinfo) {
			[$offset, $prefix] = $prefixinfo;
		}
		$hidden_day
		=	$this->show_filter
		&&	!isset($this->day_visible[$year][$month][$day]);

		if (!$this->hide_ads
		&&	!($this->in_agenda ??= ($_REQUEST['sELEMENT'] ?? null) === 'agenda')
		) {
			show_ad_placeholder_tbody($colcnt);
		}

		?><tbody class="party-day"><?
		?><tr<?
		if ($offset) {
			?> id="offset<?= $offset ?>-row"<?
		}
		ob_start();
		if (!$this->past
		&&	!$this->none_hidden
		&&	$offset < 0
		) {
			?> light small<?
		}
		if ($hidden_day) {
			?> hidden<?
		}
		$show_uncollapse =
			!$this->past
		&&	!$this->none_hidden
		&&	isset($this->counts[$offset])
		&&	$offset < 0;

		if ($show_uncollapse) {
			?> unhideanchor<?
		}
		if ($class = ob_get_clean()) {
			?> class="<?= trim($class) ?>"<?
		}
		if ($show_uncollapse) {
			?> onclick="swapDisplayPartyList(this,<?= $offset ?>)"<?
			?> data-other="<?= __('action:hide') ?>"<?
		}
		?>><?

		?><td colspan="<?= $colcnt ?>"><?
		?><h3><?

		ob_start();
		?><time datetime="<? printf('%d%02d%02d',$year,$month,$day) ?>"><?=
			weekday_name($this->weekday[$year][$month][$day]), (SMALL_SCREEN ? '<br />' : ' '), $day, ' ', _month_name($month), ' ', $year
		?></time><?
		$date_str = ob_get_clean();

		if (!$show_uncollapse) {
			?><a href="/agenda/day/<?= $year ?>/<?= $month ?>/<?= $day ?>"><?
		}

		if ($this->only_livestreams) {
			echo element_plural_name('livestream') ?>, <?
			if ($offset !== null
			&&	$prefix
			) {
				echo $prefix; ?>, <?
			}
			echo $date_str;
		} else {
			if (in_array($offset, [0, 1, 2], true)) {
				$header = __('agenda:header:going_out_special_on_date', DO_UBB | KEEP_EMPTY_KEYWORDS, ['OFFSET' => $offset]);
			} else {
				if ($offset !== null
				&&	$prefix
				) {
					echo $prefix; ?>, <?
				}
				$header = __('agenda:header:going_out_on_date', DO_UBB | KEEP_EMPTY_KEYWORDS);
			}
			echo str_replace('%DATE%',$date_str,$header);
		}

		if (!$show_uncollapse) {
			?></a><?
		}
		if ($day_name = get_day_name($year,$month,$day)) {
			?>, <?
			echo $day_name;
		}
		?></h3><?
		if ($show_uncollapse) {
			include_js('js/partylist');
			?><span class="r"><?
			?><span id="offset<?= $offset ?>-cnt"><?
			if (isset($this->visiblecnt)) {
				echo $this->visiblecnt[$offset] ?? 0;
			} else {
				echo $this->counts[$offset];
			}
			?></span> <?
			?><span id="offset<?= $offset ?>-act"><?= __('action:show') ?></span><?
			?></span><?
		}
		?></td></tr><?
		?></tbody><?

		if ($this->show_sort_headers) {
			$show_sort = true;//$this->daycounts[$year][$month][$day] > 2;
			?></table><table class="<?
			echo $this->tableclassstr;
			if ($show_sort) {
				?> sortable<?
			}
			?>"><?
			if ($show_sort) {
				$this->show_sort_header();
			}
		}
	}

	private function show_sort_header(): void {
		static $first = true;
		?><thead><?
		?><tr class="now_and_soon_stamp<?
		if (!$first) {
			?> empty<?
		}
		?>"><?
		?><th<?
		if ($this->sort_preference === 'NAME') {
			?> data-sorted="up"<?
		}
		?>><?
		if ($first) {
			echo Eelement_name('name');
		}
		?></th><?
		if ($this->show_distance) {
			?><th class="right" style="width: 5.5em;"><?= $first ? Eelement_name('distance') : '&nbsp;' ?></th><?
		}
		if ($this->show_time_offset) {
			?><th class="center hpad" style="width: 5.5em;"><?= $first ? Eelement_name('time') : '&nbsp;' ?></th><?
		}
		if (!SMALL_SCREEN) {
			if (!empty($this->buddies)
			||	$this->show_people
			) {
				?><th<?
				?> colspan="<?= empty($this->buddies) ? '2' : '4' ?>"<?
				?> class="hpad center<?
				if ($this->show_sort_headers) {
					?> sorttable_nosort<?
				}
				?>"><?= $first ? Eelement_plural_name('visitor') : '&nbsp;' ?></th><?
			}
		}
		if ($this->show_location) {
			?><th<?
			if ($this->sort_preference === 'LOCATION') {
				?> data-sorted="up"<?
			}
			if ($this->show_sort_headers) {
				?> class="sorttable_nosort"<?
			}
			?>><?= $first ? Eelement_name('location') : '&nbsp;' ?></th><?
		}
		if ($this->show_city) {
			?><th<?
			if ($this->sort_preference === 'CITY') {
				?> data-sorted="up"<?
			}
			if ($this->show_sort_headers) {
				?> class="sorttable_nosort"<?
			}
			?>><?= $first ? Eelement_name('city') : '&nbsp;' ?></th><?
		}
		?></tr><?
		?></thead><?
		$first = false;
	}

	private function show_location_microdata(array $party): void {
		if (ROBOT
		||	SERVER_SANDBOX
		||	HOME_THOMAS
		) {
			require_once '_uploadimage.inc';
			if (false === ($meta_image = memcached_get($cache_key = PARTY_META_IMAGE.$party['PARTYID']))) {
				ob_start();
				$img = uploadimage_get('party', $party['PARTYID']);
				if (have_uploadimage($img)) {
					?><meta itemprop="image" content="<?= FULL_IMAGES_HOST, uploadimage_get_url($img) ?>" /><?
				}
				$meta_image = ob_get_clean();
				memcached_set($cache_key, $meta_image, ONE_HOUR);
			}

			echo $meta_image;

			ob_start();
			if ($party['LIVESTREAM'] === 'only'
			&&	$party['LIVESTREAM_ADDRESS']
			) {
				?><meta itemprop="url" content="<?= escape_utf8($party['LIVESTREAM_ADDRESS']) ?>" /><?
			}
		} else {
			ob_start();
		}
		if ($party['LIVESTREAM'] !== 'only') {
			?><meta itemprop="name" content="<?=
				$party['LOCATIONID']
			?	escape_utf8(get_element_title('location',$party['LOCATIONID']))
			:	element_name(
					$party['BOARDINGID']
				?	'unknown_boat'
				:	'unknown_location'
				)
			?>" /><?
			if ($party['LATITUDE']) {
				?><span itemprop="geo" itemscope itemtype="https://schema.org/GeoCoordinates"><?
				?><meta itemprop="latitude" content="<?= $party['LATITUDE'] ?>" /><?
				?><meta itemprop="longitude" content="<?= $party['LONGITUDE'] ?>" /><?
				?></span><?
			}
			?><span itemprop="address" itemscope itemtype="https://schema.org/PostalAddress"><?
			if (($cityid = $party['SHOW_CITYID'])
			&&	($city = $cityid ? memcached_city_info($cityid) : null)
			) {
				?><meta itemprop="addressLocality" content="<?= escape_utf8($city['NAME']) ?>" /><?
				?><span itemprop="addressCountry" itemscope itemtype="https://schema.org/Country"><?
					?><meta itemprop="name" content="<?= escape_specials(get_element_title('country',$city['COUNTRYID'])) ?>" /><?
					?><meta itemprop="alternateName" content="<?= $city['SHORT'] ?>" /><?
				?></span><?
			}
			?></span><?
		}
		if ($data = ob_get_clean()) {
			?><span<?
			?> class="hidden"<?
			?> itemprop="location"<?
			?> itemscope<?
			?> itemtype="<?
			if ($party['LIVESTREAM'] === 'only') {
				?>https://schema.org/VirtualLocation<?
			} else {
				?>https://schema.org/EventVenue<?
			}
			?>"><?= $data ?></span><?
		}
	}

	private function get_location(array $party): ?string {
		if (!$party['LOCATIONID']) {
			return escape_utf8($party['LOCATION_ADDR']);
		}
		if (!($title = memcached_single_string(['itemname','location'], "
			SELECT COALESCE(
				(	SELECT NAME
					FROM itemname
					WHERE ELEMENT = 'location'
					  AND ID = LOCATIONID
					  AND ENDSTAMP > {$party['STAMP']}
					ORDER BY ENDSTAMP DESC
					LIMIT 1
				),	location.TITLE
			)
			FROM location
			WHERE LOCATIONID = {$party['LOCATIONID']}"))
		) {
			if ($title !== false) {
				mail_log("partylist->get_location for party {$party['PARTYID']} yields empty location title");
			}
			return null;
		}
		return get_element_link('location', $party['LOCATIONID'], $title);
	}

	final public function as_locasorg(int $locationid): void {
		$this->joins[] = '
			JOIN connect AS orgloc
			  ON MAINTYPE = "party"
			 AND MAINID = PARTYID
			 AND ASSOCTYPE = "locasorg"
			 AND ASSOCID = '.$locationid;
	}

	final public function on_location(int $locationid): void {
		$this->on_location = $locationid;
		$this->wheres[] = 'party.LOCATIONID = '.$locationid;
	}
	final public function on_location_including_livestreams(int $locationid): void {
		$this->on_location = $locationid;
		$this->wheres[] = '
			(	party.LOCATIONID='.$locationid.'
			OR	LIVESTREAM="only" AND NOT ISNULL((
							SELECT b\'1\'
							FROM connect
							WHERE	MAINTYPE="party"
							AND	MAINID=PARTYID
							AND	ASSOCTYPE="location"
							AND	ASSOCID='.$locationid.'
							LIMIT 1)
							)
			)';
	}
	final public function boarding_from(int $boardingid): void {
		$this->wheres[] = 'party.BOARDINGID = '.$boardingid;
	}
	final public function only_buddies(): void {
		if (!have_user()) {
			$this->wheres[] = '0';
			return;
		}
		require_once '_buddies.inc';
		$userids = _buddies_full_cached_list(CURRENTUSERID);
		if (!$userids) {
			$this->wheres[] = '0';
			return;
		}
		$this->tables['going'] = 'going';
		$this->joins[] = 'JOIN going ON going.PARTYID=party.PARTYID';
		$this->wheres[] = 'MAYBE=0 AND going.USERID IN ('.implode(',',$userids).')';
		$this->need_distinct = true;
	}
	final public function only_artist(int $artistid): void {
		// need group, because artist might perform in more than one area on the same party
		$this->need_distinct = true;
		$this->wheres[] = 'lineup.ARTISTID='.$artistid;
		$this->joins[] = 'JOIN lineup USING (PARTYID)';
		$this->tables['lineup'] = 'lineup';
		$this->skip_future_lineup = true;
	}
	final public function only_favourites(): void {
		require_once '_favourite.inc';
		if(!have_user()
		||	false === ($favs = get_favourites())
		||	!$favs
		||	empty($favs['artist'])
		&&	empty($favs['location'])
		&&	empty($favs['organization'])
		) {
			$this->wheres[] = '0';
			return;
		}
		$wpart = [];
		if (!empty($favs['artist'])) {
			$this->need_distinct = true;
			$this->tables['lineup'] = 'lineup';
			$this->joins[] = 'LEFT JOIN lineup AS favlineup ON favlineup.PARTYID=party.PARTYID';
			$wpart[] = 'favlineup.ARTISTID IN ('.implodekeys(',',$favs['artist']).')';
		}
		if (!empty($favs['location'])) {
			$wpart[] = 'party.LOCATIONID IN ('.implodekeys(',',$favs['location']).')';
		}
		if (!empty($favs['organization'])) {
			$this->need_distinct = true;
			$this->tables['connect'] = 'connect';
			$this->joins[] = 'LEFT JOIN connect AS favorg ON favorg.MAINTYPE="party" AND favorg.MAINID=party.PARTYID AND favorg.ASSOCTYPE IN ("organization","orgashost")';
			$wpart[] = 'favorg.ASSOCID IN ('.($orgidstr = implodekeys(',',$favs['organization'])).')';

			$this->tables['partyarea'] = 'partyarea';
			$this->joins[] = 'LEFT JOIN partyarea AS favarea ON favarea.PARTYID=party.PARTYID';
			$wpart[] = 'favarea.HOSTEDBYID IN ('.$orgidstr.')';
		}
		$this->wheres[] = '('.implode(' OR ',$wpart).')';
	}
	final public function only_festivals(): void {
		$this->wheres[] = "FESTIVAL = b'1'";
	}
	final public function only_photographers(): void {
		$this->joins[] = '
			JOIN camera 
			  ON camera.PARTYID = party.PARTYID
			 AND camera.STATUS IN ("accepted", "self")';
#		$this->need_distinct = true;
		$this->tables['camera'] = 'camera';
	}

	final public function by_organization(int $organizationid): void {
/*		$this->need_distinct = true;

		$this->tables['connect'] = 'connect';
		$this->joins[] = 'LEFT JOIN connect AS ocp ON ocp.ASSOCID=party.PARTYID AND ocp.ASSOCTYPE="party" AND ocp.MAINTYPE="organization" AND ocp.MAINID='.$organizationid;

		$this->tables['partyarea'] = 'partyarea';
		$this->joins[] = 'LEFT JOIN partyarea AS orgarea ON orgarea.PARTYID=party.PARTYID AND HOSTEDBYID='.$organizationid;
		$this->wheres[] = '(NOT ISNULL(ocp.MAINID) OR NOT ISNULL(orgarea.PARTYID))';*/

		$this->tables['connect'] = 'connect';
		$this->tables['partyarea'] = 'partyarea';

		// if (0 && HOME_THOMAS) {
		// 	$orgids = [$organizationid => $organizationid];
		//
		// 	while ($more_ids = db_same_hash('subof','
		// 		SELECT CONCEPTID
		// 		FROM subof
		// 		WHERE ELEMENT="organization"
		// 		  AND PELEMENT="organization"
		// 		  AND PARENTID IN ('.($orgidstr = implode(',',$orgids)).')
		// 		  AND CONCEPTID NOT IN ('.$orgidstr.')')
		// 	) {
		// 		$orgids += $more_ids;
		// 	}
		//
		// 	$this->joins[] = 'JOIN (
		// 		SELECT PARTYID,0 AS IS_ORG,1 AS IS_HOST
		// 		FROM partyarea
		// 		WHERE HOSTEDBYID IN ('.$orgidstr.')
		//
		// 		UNION
		//
		// 		SELECT ASSOCID AS PARTYID,1 AS IS_ORG,0 AS IS_HOST
		// 		FROM connect
		// 		WHERE MAINTYPE="organization"
		// 		  AND MAINID IN ('.$orgidstr.')
		// 		  AND ASSOCTYPE="party"
		//
		// 		UNION
		//
		// 		SELECT ASSOCID AS PARTYID,0 AS IS_ORG,1 AS IS_HOST
		// 		FROM connect
		// 		WHERE MAINTYPE="orgashost"
		// 		  AND MAINID IN ('.$orgidstr.')
		// 		  AND ASSOCTYPE="party"
		//
		// 	) AS findhostororg USING (PARTYID)';
		// } else {
		$this->joins[] = 'JOIN (
				SELECT PARTYID,0 AS IS_ORG,1 AS IS_HOST
				FROM partyarea
				WHERE HOSTEDBYID='.$organizationid.'
	
				UNION
	
				SELECT ASSOCID AS PARTYID,1 AS IS_ORG,0 AS IS_HOST
				FROM connect
				WHERE MAINTYPE="organization"
				  AND MAINID='.$organizationid.'
				  AND ASSOCTYPE="party"
				  
				UNION
	
				SELECT ASSOCID AS PARTYID,0 AS IS_ORG,1 AS IS_HOST
				FROM connect
				WHERE MAINTYPE="orgashost"
				  AND MAINID='.$organizationid.'
				  AND ASSOCTYPE="party"
				  
			) AS findhostororg USING (PARTYID)';
		// }

		$this->host_or_org = true;
		$this->need_group  = true;
		$this->by_org = $organizationid;
	}
	final public function user_going(int $userid, bool $only_maybe = false): void {
		$this->include_maybe = true;
		$this->user_going = $userid;
		$this->wheres[] = ($only_maybe ? 'MAYBE = 1 AND ' : '').'going.USERID = '.$userid;

		array_unshift($this->joins, 'JOIN going USING (PARTYID)');

		$this->tables['going'] = 'going';

		if (CURRENTUSERID === $userid) {
			$this->dont_mark_mine = true;
		}
	}
	final public function user_maybe_going(int $userid): void {
		$this->user_going($userid, true);
	}
	final public function in_city(int $cityid): void {
		$this->in_city = $cityid;
	}
	final public function within_city_radius(int $cityid, int $radius): void {
		if (!($middlecity = memcached_single_array('city', 'SELECT LATITUDE, LONGITUDE FROM city WHERE CITYID = '.$cityid))) {
			$this->wheres[] = '0';
			return;
		}
		[$latitude, $longitude] = $middlecity;
		if (!($cities = memcached_cities_within_radius($latitude, $longitude, $radius))) {
			$this->wheres[] = '0';
			return;
		}
		$cities[] = $cityid;
		$citylist = implode(', ', $cities);
		$this->joins[] = 'JOIN location AS radiusloc ON radiusloc.LOCATIONID = party.LOCATIONID';
		$this->wheres[] = '(party.CITYID IN ('.$citylist.') OR radiusloc.CITYID IN ('.$citylist.'))';
		$this->tables['location'] = 'location';
	}
	final public function with_name(string $name): void {
		$this->wheres[] = 'party.NAME LIKE "%'.str_replace(' ','%',addslashes($name)).'%"';
	}
	final public function with_styles(array|int $gids): void {
		# why is $gids 'empty'?
		$gid_set = $gids ? '('.(is_array($gids) ? implode(', ', $gids) : $gids).')' : '(0)';
		$this->wheres[] = "
			IFNULL(
				(	SELECT 1
					FROM party_genre
					WHERE party_genre.PARTYID = party.PARTYID
					  AND GID IN $gid_set LIMIT 1
				),
				(	SELECT 1
					FROM party_genre_determined_v2
					WHERE party_genre_determined_v2.PARTYID = party.PARTYID
					  AND GID IN $gid_set
					LIMIT 1
				)
			)";
	}
	final public function in_province(int $provinceid): void {
		if (!($cityids = memcached_simpler_array('city','SELECT CITYID FROM city WHERE PROVINCEID='.$provinceid))) {
			$this->wheres[] = '0';
		} else {
			$this->in_cities(implode(', ', $cityids));
		}
	}

	final public function in_cities(string $cityidstr): void {
		$this->joins[] = "
		JOIN (	SELECT PARTYID FROM party JOIN location USING (LOCATIONID) WHERE location.CITYID IN ($cityidstr)
		UNION	SELECT PARTYID FROM party JOIN boarding USING (BOARDINGID) WHERE boarding.CITYID IN ($cityidstr)
		UNION	SELECT PARTYID FROM party WHERE CITYID IN ($cityidstr)
		) AS x1 USING (PARTYID)";
	}

	final public function in_country(int $countryid): void {
		$this->tables['location'] = 'location';
		$this->tables['city'] = 'city';
		$this->joins[] = 'LEFT JOIN location AS lfc ON lfc.LOCATIONID = party.LOCATIONID';
		$this->joins[] = 'LEFT JOIN city AS cfl ON cfl.CITYID = lfc.CITYID';
		$this->joins[] = 'LEFT JOIN city AS cfc ON cfc.CITYID = party.CITYID';
		$this->wheres[] = "(cfl.COUNTRYID = $countryid OR cfc.COUNTRYID = $countryid)";
	}

	final public function only_party(int $partyid): void {
		$this->wheres[] = 'party.PARTYID = '.$partyid;
	}

	final public function in_year_and_month(int $year, int $month): void {
		change_timezone('UTC');
		$start = offset_day_start(gmmktime(0, 0, 0, month: $month, day: 1, year: $year));
		$stop = strtotime('+1 month', $start);
		change_timezone();
		$this->wheres[] = $this->where_start_stop($start, $stop);
	}

	final public function in_year(int $year): void {
		$start = gmmktime(HOUR_DAY_START, 0, 0, month: 1, day: 1, year: $year);
		$stop  = gmmktime(HOUR_DAY_START, 0, 0, month: 1, day: 1, year: $year + 1);

		$this->wheres[] = $this->where_start_stop($start, $stop);
	}

	final public function in_price_range(int $min, ?int $max = null): void {
		if ($max !== null) {
			if (!$min && !$max) {
				# only free entrance
				$this->wheres[] = 'FREE_ENTRANCE';
				return;
			}
			if ($min > $max) {
				# impossible
				$this->wheres[] = '0';
				return;
			}
		}
		$this->wheres[] = '(
			SELECT MIN(PRICE)
			FROM partyprice
			WHERE partyprice.PARTYID = party.PARTYID
		) '.($max === null ? " >= $min" : " BETWEEN $min AND $max - 1");
	}

	final public function min_minage(int $age): void {
		$this->wheres[] = 'party.MIN_AGE >= '.$age;
	}

	final public function max_minage(int $age): void {
		$this->wheres[] = 'party.MIN_AGE <= '.$age;
	}

	final public function sold_out(bool $sold_out = true): void {
		$this->wheres[] = 'SOLD_OUT = '.($sold_out ? '1' : '0');
	}

	final public function now_and_soon(
		int		$stamp,
		int		$max_time,
		int		$max_distance,
		int		$min_duration,
		float	$latitude,
		float	$longitude
	): void {
		$this->now_and_soon_stamp = $stamp;

		require_once '_geo.inc';
		if (!$latitude) {
			[$latitude, $longitude] = get_geolocation_cache();
			if ($latitude === null) {
				$this->wheres[] = '0';
				return;
			}
		}
		# MAX TIME

		# 1. use two days as max for parties with minimum duration and ongoing
		# 2. always stop at max_time, no party should start later
		# --
		# 3. party must start after 'now_and_soon_stamp'
		# 4. or party must continue to run for at least minduration seconds (stopstamp would be nice)

		$this->wheres[] = '
			STAMP >= '.($this->now_and_soon_stamp - 2 * ONE_DAY).'
		AND	STAMP <  '.($this->now_and_soon_stamp + $max_time).'
		AND (	STAMP >=' .$this->now_and_soon_stamp.'
			OR	STAMP <  '.$this->now_and_soon_stamp.'
			AND	(CAST(STAMP AS SIGNED) + IF(DURATION_SECS, DURATION_SECS, 4*3600)) - '.$this->now_and_soon_stamp.' > '.$min_duration.'
			)
		AND	0 != COALESCE(boarding.LATITUDE,  location.LATITUDE,  city.LATITUDE)
		AND 0 != COALESCE(boarding.LONGITUDE, location.LONGITUDE, city.LONGITUDE)
		';
		# MAX DISTANCE
		$this->wheres[] = distance(
			$latitude,
			$longitude,
			'COALESCE(boarding.LATITUDE,  location.LATITUDE,  city.LATITUDE)',
			'COALESCE(boarding.LONGITUDE, location.LONGITUDE, city.LONGITUDE)'
		).' < '.$max_distance;
	}

	final public function with_location_of_types(array $types): void {
		foreach ($types as $type) {
			$ors[] = 'FIND_IN_SET("'.addslashes($type).'", party.LOCATIONTYPE)';
		}
		$this->wheres[] = '('.implode(' OR ', $ors).')';
	}

	final public function show_buddy_all_count(int $partyid): void {
		if (!($buddycnt = $this->allbuddycnt[$partyid] ?? null)) {
			return;
		}
		require_once '_hearts.inc';
		show_heart_with_bubble($this->allbuddies[$partyid], $buddycnt, $partyid);
	}

	final public function get_all_count(array $party): array {
		$partyid = $party['PARTYID'];
		$pplcnt = 0;
		$platforms = [];
		if ($this->appiccnt_pfid
		&&	($appic_count = $this->appiccnt_pfid[$party['MAIN_ID']] ?? null)
		||	$this->appiccnt_fbid
		&&	($appic_count = $this->appiccnt_fbid[$partyid] ?? null)
		) {
			$pplcnt += $appic_count;
			$platforms['appic'] = 'appic';
		}
		if (isset($this->allpplcnt[$partyid])) {
			$pplcnt += $this->allpplcnt[$partyid];
			$platforms['partyflock'] = 'partyflock';
		}
		return [$pplcnt, $platforms];
	}

	final public function show_all_count(array $party): ?array {
		$ppl = $this->get_all_count($party);
		if (!$ppl[0]) {
			return null;
		}
		[$pplcnt/*, $platforms */] = $ppl;
		echo $pplcnt;
		if ($pplcnt
		&&	!SMALL_SCREEN
		) {
			echo ' ',$this->get_platforms_icon($party, $ppl);
		}
		return $ppl;
	}

	final public function get_platforms_icon(array $party, ?array $ppl): ?string {
		[$pplcnt/*, $platforms */] = $ppl;
		if (!$pplcnt) {
			return null;
		}
		static $__icon = [
			'appic'						=> get_appic_icon(),
			'facebook'					=> get_facebook_icon(),
			'partyflock'				=> get_partyflock_icon(),

			'appic,facebook'			=> 'af',
			'appic,facebook,partyflock'	=> 'apfb',
			'appic,partyflock'			=> get_appic_partyflock_icon(),

			'facebook,partyflock'		=> get_pffb_icon(),
		];
		static $__width = [
			1	=> 16,
			2	=> 24,
			3	=> 32,
		];

		$str = implode(',', $ppl[1]);

		$icon = $__icon[$str];

		if ($icon[0] === '<') {
			return $icon;
		}

		$html = '<img'.
			' alt="'.element_plural_name('visitor').'"'.
			' class="lower"'.
			' style="width:'.$__width[count($ppl[1])].'px;height:16px"'.
			' src="'.STATIC_HOST.'/presence/'.$icon.is_high_res().'.png"'.
			'>';

		return $html;
	}

/*	final public function show_fb_count(int $partyid): void {
		if (!isset($this->fbcnt[$partyid])) {
			return;
		}
		[$fbid, $pplcnt] = keyval($this->fbcnt[$partyid]);
		?><a<?
		?> class="nowrap"<?
		?> href="//facebook.com/<?= $fbid ?>"<?
		?>><?= $pplcnt,' ',get_facebook_icon() ?></a><?
	}*/

	final public function not_cancelled_nor_moved(): void {
		$this->wheres[] = 'MOVEDID = 0 AND CANCELLED = 0';
	}

	final public function only_erotic(): void {
		$this->wheres[] = 'party.EROTIC = b\'1\'';
	}

	final public function without_lineup_and_no_check(): void {
		require_once '_complete.inc';
		$this->joins[] = '
			LEFT JOIN lineupneedupdate ON lineupneedupdate.PARTYID = party.PARTYID
			LEFT JOIN lineup AS lineupcheck ON lineupcheck.PARTYID = party.PARTYID';
		$this->wheres[] = '
			lineupneedupdate.PARTYID IS NULL
		AND	lineupcheck.PARTYID IS NULL
		AND	CANCELLED = 0
		AND	MOVEDID = 0
		AND	(party.STAMP + IF(party.DURATION_SECS, party.DURATION_SECS, 6*3600)) > '.CURRENTSTAMP;

	}

	final public function only_halloween(int $year = 0): void {
		global $__year;

		require_once '_specialday.inc';

		static $parts = null;
		$parts ??= get_halloween_words();

		$halloween = gmmktime(hour: 22, month: 10, day: 31, year: $year ?: $__year);

		$weekend_before = strtotime('last thursday 6 AM', $halloween);
		$weekend_after  = strtotime('next monday   6 AM', $halloween);

		$this->wheres[] = '
				STAMP_TZI BETWEEN '.$weekend_before.' AND '.($weekend_after - 1).'
		AND	(	party.NAME LIKE "%'.($strs = implode ('%" OR party.NAME LIKE "%', $parts)).'%"
			OR	SUBTITLE LIKE  "%'.$strs.'%"
			)';
	}

	final public function only_carnival(int $year = 0): void {
		global $__year;

		require_once '_specialday.inc';

		static $parts = null;
		$parts ??= get_carnival_words();
		$carnival_start = get_carnival_start($year ?: $__year);
		$carnival_start = $carnival_start - 7 * ONE_DAY + HOUR_DAY_START;
		$carnival_end   = $carnival_start + (3 + 7) * ONE_DAY + HOUR_DAY_START;

		$this->wheres[] = '
			STAMP_TZI BETWEEN '.$carnival_start.' AND '.$carnival_end.'
		AND	(	party.NAME LIKE "%'.($strs = implode ('%" OR party.NAME LIKE "%', $parts)).'%"
			OR	SUBTITLE LIKE  "%'.$strs.'%"
			)';
	}

	final public function only_for_current_country(): void {
		if (CURRENTDOMAIN === 'nl') {
			# remove foreign events without visitors
			$this->only_of_interest = 1;
		} elseif ($join = join_only_events_for_current_country()) {
			$this->joins[] = $join;
		}
	}

	final public function master_genre(int $mgid): void {
		$this->joins[] = 'JOIN event_master_genre USING (PARTYID)';
		$this->wheres[] = 'MGID = '.$mgid;
	}

	final public function nighttime(): void {
		$this->wheres[] = 'NIGHTTIME = 1';
	}

	final public function daytime(): void {
		# daytime events are events with at least 6 hours during the hours from 08:00 and 24:00
		$this->wheres[] = 'DAYTIME = 1';
	}

	final public function only_placeholders(): void {
		$this->wheres[] = 'PLACEHOLDER = 1';
	}

/*	final public function order_by_reverse_creation_date(): void {
		$this->order = 'party.CSTAMP DESC';
	}

	final public function per_page(int $per_page): void {
		$this->per_page = $per_page;
	}*/

	final public function only_validated(): void {
		$this->wheres[] = 'party.ACCEPTED = 1';
	}

	final public function new(): void {
		# show_runnning_date_first

		$this->new = true;
		$this->per_page = 200;
		$this->show_date = true;
		$this->order = 'LAST_VALIDATED DESC';
		$this->show_last_validation_date = true;
		# date first is reasonably nice on desktop, not so on mobile
		$this->show_date_first = !SMALL_SCREEN;
		$this->only_validated();
		if (have_admin()) {
			$this->show_customer = true;
			$this->show_last_validator = true;
		}
	}
}
