<?php

function display_body(): void {
	require_once '_countryflag.inc';
	require_once '_language.inc';

	layout_show_section_header();

	if (!($languages = db_rowuse_hash('language','
		SELECT LANGID,NAME,FLAGCOUNTRYID
		FROM language'
	))) {
		return;
	}
	string_asort($languages,'NAME');
	
	layout_open_box('white');
	?><div class="block"><span class="warning">Please note!</span><br /><?
	?>Translations are by no means finished. We're understaffed and lots of projects require our attention, we apologize.<br /><?
	?>If no translation is available, you will see the Dutch language.<br /><?
	?>To come back to this page, click on the flag in the top right corner.</div><?
/*	?><div class="block"><b>Help us!</b><br /><?
	?>Do you regularly use Partyflock and would like to read more of the interface in your own language and would like to help translating?<br /><?
	?>Then please <i><a href="/ticket/form?ELEMENT=development" target="_blank">contact us</a></i> about the possibilities to help out!</div><?*/
	layout_close_box();
	
	if ($stats = memcached_simple_hash('translationtext', '
		SELECT LANGID, COUNT(*)
		FROM translationtext
		WHERE BODY != ""
		GROUP BY LANGID
		ORDER BY COUNT(*) DESC'
	)) {
		$total = max($stats);
	}
	foreach ($languages as $langid => $name) {
		if (!isset($stats[$langid])) {
			$stats[$langid] = 0;
		}
	}
	$maxw = db_single('countryflag','SELECT MAX(WIDTH) FROM countryflag');
	layout_open_box('white');
	layout_box_header(__C('language:header:choose_language'));
	?><table class="default"><?
	foreach ($stats as $langid => $cnt) {
		$language = $languages[$langid];
		?><tr<?
		if ($current = (CURRENTLANGID === $langid)) {
			?> class="bold-hilited"<?
		}
		?>><td><?
		if (!$current) {
			?><a href="/language/<?= $langid; ?>/set"><?
		}
		?><div class="center ib" style="width:<?= $maxw ?>px"><?
		show_country_flag($language['FLAGCOUNTRYID']);
		?></div><?
		?>&nbsp;<?
		echo escape_utf8($language['NAME']);
		if (!$current) {
			?></a><?
		}
		?></td><td class="right"><?
		if ($stats) {
			echo round(100 * getifset($stats, $langid) / $total), '%';
		}
		?></td><?
		?></tr><?
	}
	?></table><?
	layout_close_box();
}
