<?php

declare(strict_types=1);

require_once '_unicode.inc';
require_once 'defines/storage_sizes.inc';

function is_number(string|int|array|null $number): bool {
	# only unsigned numbers are use
	if (is_int($number)) {
		return $number >= 0;
	}
	return $number === '0'
		|| $number
		&& is_string($number)
		&& ctype_digit($number);
}

function get_memory_limit(): int {
	require_once '_require.inc';
	$current = trim(ini_get('memory_limit'));
	if (!is_number($current)) {
		$last = strtolower($current[strlen($current) - 1]);
		$current = (int)substr($current, 0, -1);
		switch ($last) { # NOSONAR
		case 'g': $current *= 1_073_741_824; break;
		case 'm': $current *= 1_048_576; break;
		case 'k': $current *= 1_024; break;
		}
	} else {
		$current = (int)$current;
	}
	return $current;
}

function set_memory_limit(int $limit): void {
	# never decrease memory limit
	if ($limit > get_memory_limit()) {
		ini_set('memory_limit', $limit); // NOSONAR
	}
}

function increase_memory_limit(int $extra): void {
	ini_set('memory_limit', get_memory_limit() + $extra);	// NOSONAR
}

function keyval(array $array): array {
	# only used for single each() call, not for walking arrays
	if (null !== ($key = array_key_first($array))) {
		return [$key, $array[$key]];
	}
	return [false, false];
}

function implodekeys(string $delimiter, array $array): string {
	return implode($delimiter,  array_keys($array));
}

function crc32_file(string $file): int|false {
	if (false === ($crc32b = hash_file('crc32b', $file))) {
		return false;
	}
	return hexdec($crc32b);
}

function clean_utf8(string|array &$arg, ?string $field = null): string|false {
	if ($field) {
		if (!is_array($arg)
		||	!isset($arg[$field])
		||	!is_string($arg[$field])
		) {
			return false;
		}
		return $arg[$field] = clean_charset($arg[$field], 'UTF-8');
	}
	if (!is_string($arg)) {
		return false;
	}
	return $arg = clean_charset($arg, 'UTF-8');
}
function clean_charset(string $text, string $charset): string {
	# Sometimes these latin1 unbreakable space can be found in otherwise UTF-8 text
	$text = str_replace(NO_BREAK_SPACE_LATIN1, ' ', $text);
	$old_sub = ini_get('mbstring.substitute_character');
	ini_set('mbstring.substitute_character', 'none'); // NOSONAR
	$text = mb_convert_encoding($text, $charset, $charset);
	ini_set('mbstring.substitute_character', $old_sub); // NOSONAR
	return str_replace([REPLACEMENT_CHARACTER, OBJECT_REPLACEMENT_CHARACTER], ['', ''], $text);
}

function preg_failure(string $source_text, string|array|null $regex = null): void {
	$error_message = preg_last_error_msg();
	$info = [
		'regex'		=> $regex,
		'source'	=> $source_text,
	];
	if (isset($_SERVER['REQUEST_URI'])
	&&	$_SERVER['REQUEST_URI'] === '/user/commitnew'
	) {
		$len = strlen($source_text);
		$ords = [];
		for ($i = 0; $i < $len; ++$i) {
			$ords[] = dechex(ord($source_text[$i]));
		}
		$info['ords'] = implode(', ', $ords);
	}
	mail_log("preg_replace: $error_message", $info, 'preg_failure');
	if (PHP_SAPI !== 'cli'
	&&	!headers_sent()
	) {
		header('Retry-After: '.ONE_HOUR, true, 503);
	}
}

# certain emoji's fall in the 'unassigned' class: \p{Cn}

function utf8_strcmp(string $str1, string $str2): int|false {
	return get_collator()->compare($str1, $str2);
}
function utf8_strcasecmp(string $str1, string $str2): int|false {
	return utf8_strcmp(mb_strtolower($str1, 'UTF-8'), mb_strtolower($str2, 'UTF-8'));
}
function utf8_str_identical(string $str1, string $str2): bool {
	return 0 === utf8_strcmp($str1, $str2);
}
function utf8_str_identical_ignoring_case(string $str1, string $str2): bool {
	return utf8_str_identical(mb_strtolower($str1, 'UTF-8'), mb_strtolower($str2, 'UTF-8'));
}

function utf8_strncasecmp(string $str1, string $str2, int $length): int|false {
	return	get_collator()->compare(
			mb_strtolower(
				mb_substr($str1, 0, $length, 'UTF-8'),
				'UTF-8'
			),
			mb_strtolower(
				mb_substr($str2, 0, $length, 'UTF-8'),
				'UTF-8'
			)
		);
}

function utf8_mytrim(
	string	$str,
	?string	$also	= null,
	?string	$trim	= null,
): string {
	$trim ??= 'trim';
	if ($also) {
		$also = preg_quote($also, '!');
	}
	$match_characters =
		MATCH_ANY_WHITESPACE.
		# NOTE: We use control for various functions
		#		Like replacing a name with an index encapsulated in 2 control characters.
		#		This number is then later converted back to stored values.
		#		This breaks if the control chars are around the entire string.
		#		This trim function would strip them from front & tail.
		# MATCH_ANY_CONTROL.
		MATCH_ANY_FORMATTING.
		$also.
		'\s\r\f\n\t\v\x0\p{Co}\p{Cs}\p{Cn}';

	if (null !== ($new_str = preg_replace($regex = match($trim) {
			'trim'	=> "!^[$match_characters]+|[$match_characters]+$!u",
			'ltrim'	=> "!^[$match_characters]+!u",
			'rtrim'	=> "![$match_characters]+$!u",
		},
		'',
		$str))
	) {
		/** @var string $new_str */
		$str = $new_str;
	} else {
		preg_failure($str, $regex);
	}
	return $str;
}

function utf8_myltrim(string $str, ?string $also = null): string {
	return utf8_mytrim($str, $also, 'ltrim');
}

function utf8_myrtrim(string $str, ?string $also = null): string {
	return utf8_mytrim($str, $also, 'rtrim');
}

function mytrim(
	string	$str,
	?string	$also = null,
	bool	$utf8 = false,
	?string	$trim = null,
): string {
	$trim ??= 'trim';
	if ($utf8) {
		return ("utf8_my$trim")($str, $also);
	}
	return $trim($str,
		" \n\r\f\t\v\x00".
		NO_BREAK_SPACE_LATIN1.
		SOFT_HYPHEN_LATIN1.
		$also
	);
}

function mytrim_title(string $str, bool $utf8 = false): string {
	# allow \x1F for internal org name replacements
	$chars = ($utf8 ? '\p{L}\p{N}' : '\w\d')."\x1F";
	$regex = '/(?!\$hirak)^[^'.$chars.']+|[^'.$chars.']+$/i'.($utf8 ? 'u' : null);
	$result = preg_replace($regex, '', $str);
	if ($result === null) {
		preg_failure($str, $regex);
		return $str;
	}
	return $result;
}

function myltrim(string $str, ?string $also = null, bool $utf8 = false): string {
	return mytrim($str, $also, $utf8, 'ltrim');
}

function myrtrim(
	 string $str,
	?string $also = null,
	 bool   $utf8 = false,
): string {
	return mytrim($str, $also, $utf8, 'rtrim');
}

function my_ucfirst(string $input, bool $utf8 = false): string {
	if (!$utf8) {
		return ucfirst($input);
	}
	return preg_replace_callback(
		'"^(?<first>.)(?<rest>.*)$"u',
		static fn($match) => mb_strtoupper($match['first']).$match['rest'],
		$input
	);
}

function win1252_to_utf8(string $str): string {
	if (!$str) {
		return $str;
	}
	return mb_convert_encoding($str, 'UTF-8', 'WINDOWS-1252');
}

function remove_characters(string $str, string $ranges): ?string {
	return preg_replace("/[$ranges]+/u", '', $str);
}

function remove_emojis(string $str): string {
	if (null === ($result = remove_characters($str, EMOJI_RANGES))) {
		mail_log('remove_characters returns null', $str, 'remove_characters null');
		$result = '';
	}
	return $result;
}

function transliterate_irregular_letters_to_regular(string $input): ?string {
	return preg_replace_callback('/['.IRREGULAR_LETTERS_RANGE.'\s]+/u', static function (array $match): string {
		# convert non regular letters to letters in windows-1252 charset
		# convert them back to regular letters in UTF-8
		if (false === ($converted_to_win1252 = iconv(
			'UTF-8',
			'windows-1252//TRANSLIT',
			$match[0]))
		||	false === ($converted_back_to_utf8 = iconv(
			'windows-1252',
			'UTF-8',
			$converted_to_win1252))
		) {
			return $match[0];
		}
		return $converted_back_to_utf8;
	}, $input);
}

function utf8_to_win1252(string $str): string {
	return iconv('UTF-8', 'windows-1252//TRANSLIT', remove_emojis($str) ?? '');
}

function utf8_to_ascii(string $str): string {
	return iconv('UTF-8', 'ASCII//TRANSLIT', $str);
}

# let iconv do the heavy lifting, but us below conversion for some
const WIN1252_TO_ASCII_CONVERSION_CHARACTERS = ["\x24\x40\x80\xA5\xB4", "SaeY'"];

function win1252_to_ascii(string $str): string {
	return	iconv(
		'windows-1252',
		'ASCII//TRANSLIT',
		strtr($str, ...WIN1252_TO_ASCII_CONVERSION_CHARACTERS)
	);
}

function utf8_ucfirst(string $str): string {
	$strlen = mb_strlen($str, 'UTF-8');
	$first = mb_substr($str, 0, 1, 'UTF-8');
	$remainder = mb_substr($str, 1, $strlen - 1, 'UTF-8');
	return mb_strtoupper($first, 'UTF-8').$remainder;
}

// from http://www.phperz.com/article/14/1029/31806.html
//	function mb_split_str($str) {
//		preg_match_all('/./u', $str, $arr);
//		return $arr[0];
//	}

// based on http://www.phperz.com/article/14/1029/31806.html, added percent
// based on https://gist.github.com/soderlind/74a06f9408306cfc5de9
function mb_similar_text(string $string_one, string $string_two, ?float &$percent = null): int {
	$array_one = array_unique(mb_split('""u', $string_one));
	$array_two = array_unique(mb_split('""u', $string_two));
	$similarity = count($array_two) - count(array_diff($array_two, $array_one));
	$percent = (float)($similarity * 200) / (strlen($string_one) + strlen($string_two) );
	return $similarity;
}

//	function mb_similar_text(string $str1, string $str2, ?float &$percent = null): int {
//		if (0 === mb_strlen($str1) + mb_strlen($str2)) {
//			$percent = 0.0;
//			return 0;
//		}
//		$pos1 = 0;
//		$pos2 = 0;
//		$max = 0;
//		$length_one = mb_strlen($str1);
//		$length_two = mb_strlen($str2);
//		for ($p = 0; $p < $length_one; ++$p) {
//			for ($q = 0; $q < $length_two; ++$q) {
//				/** @noinspection MissingOrEmptyGroupStatementInspection */
//				/** @noinspection LoopWhichDoesNotLoopInspection */
//				/** @noinspection PhpStatementHasEmptyBodyInspection */
//				for ($l = 0;
//						($p + $l < $length_one)
//					&&	($q + $l < $length_two)
//					&& mb_substr($str1, $p + $l, 1) === mb_substr($str2, $q + $l, 1);
//					++$l
//				) {
//					// nothing to do
//				}
//				if ($l > $max) {
//					$max = $l;
//					$pos1 = $p;
//					$pos2 = $q;
//				}
//			}
//		}
//		if ($similarity = $max) {
//			if ($pos1 && $pos2) {
//				$similarity += mb_similar_text(mb_substr($str1, 0, $pos1), mb_substr($str2, 0, $pos2));
//			}
//			if (($pos1 + $max < $length_one) && ($pos2 + $max < $length_two)) {
//				$similarity += mb_similar_text(
//					mb_substr($str1, $pos1 + $max, $length_one - $pos1 - $max),
//					mb_substr($str2, $pos2 + $max, $length_two - $pos2 - $max)
//				);
//			}
//		}
//		$percent = ($similarity * 200.0) / ($length_one + $length_two);
//		return $similarity;
//	}

const ACCEPT_HEADER_FOR_IMAGE = 'Accept: image/avif,image/jpeg,image/webp,image/apng,image/*,*/*';

function safe_file_get_contents(
	string   $url,
	?array   $params = null,
	?array  &$info   = null,
	?int	&$errno  = null,
	?string &$error  = null,
	bool	 $quiet  = false,
): string|false {
	if (!preg_match('"^[a-zA-Z]+://"u', $url)) {	// NOSONAR
		# not a uri, fall back to regular file_get_contents for file system action
		return file_get_contents($url);
	}
	static $__ch = 0;
	if ($__ch === 0) {
		require_once 'ipresolve.inc';
		require_once 'defines/host_to_local.inc';

		$cookies =
			!$params
		||	!array_key_exists(CURLOPT_COOKIEFILE, $params)
		||	$params[CURLOPT_COOKIEFILE];

		$__ch = curl_init();
		curl_setopt_array($__ch, [
			CURLOPT_ACCEPT_ENCODING		 => '', # Empty string means all supported encodings will be used
			CURLOPT_TRANSFER_ENCODING	 => true,
			# Some sites use empty string as language and return wrong redirect.
			# Also, getting English or Dutch seems preferable:
			CURLOPT_HTTPHEADER			 => ['Accept-Language: en,nl;q=0.9,*;0.5'],
			CURLOPT_RETURNTRANSFER		 => true,
			CURLOPT_FOLLOWLOCATION		 => true,
			CURLOPT_MAXREDIRS			 => 10,
			CURLOPT_CONNECTTIMEOUT		 => 2,
			CURLOPT_TIMEOUT				 => 5,
			CURLOPT_COOKIEFILE			 => $cookies ? '/tmpdisk/partyflock.cookies' : null,
			CURLOPT_COOKIEJAR			 => $cookies ? '/tmpdisk/partyflock.cookies' : null,
			CURLOPT_COOKIESESSION		 => $cookies,
			CURLOPT_USERAGENT			 => USER_AGENT_GENERIC_GET,
			CURLOPT_SSL_CIPHER_LIST		 => 'DEFAULT@SECLEVEL=1',	# More lenient (Thuishaven notably had old SHA1 cert)
			CURLOPT_CONNECT_TO			 => HOST_TO_LOCAL_CONNECT,	# For Partyflock urls
			CURLOPT_DNS_USE_GLOBAL_CACHE => true,
		]);
	}
	if ($params) {
		curl_setopt_array($__ch, $params);
	}
	curl_setopt_array($__ch, [
		CURLOPT_IPRESOLVE	=> str_contains($url,'facebook.com') ? RESOLVE_FB_CURL : RESOLVE_IPV_CURL,
		CURLOPT_URL			=> $url
	]);
	for ($try = 0; $try < 3; ++$try) {
		$data  = curl_exec   ($__ch);
		$info  = curl_getinfo($__ch);
		$errno = curl_errno  ($__ch);
		$error = curl_error  ($__ch);
		if ($data) {
			if ($try && !$quiet) {
				$traces = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 2);
				$trace = $traces[1] ?? null;
				error_log(
					"safe_file_get_contents of $url succeeded on try  ".($try + 1).'. '.
					($trace ? " called from {$trace['file']}:{$trace['function']}:{$trace['line']}" : '').
					(!empty($_SERVER['REQUEST_URI']) ? " @ {$_SERVER['REQUEST_URI']}" : '')
					#"\n\nDATA: ".substr($data, 1, 256)
				);
			}
			break;
		}
		if ($error && !$quiet) {
			$traces = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 2);
			$trace = $traces[1] ?? null;
			error_log(
				"WARNING safe_file_get_contents of $url failed on try ".($try + 1).": $error".
				($trace ? " called from {$trace['file']}:{$trace['function']}:{$trace['line']}" : '').
				(!empty($_SERVER['REQUEST_URI']) ? " @ {$_SERVER['REQUEST_URI']}" : '')
			);
		}
	}
	if (!$data && !$quiet) {
		$traces = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 2);
		$trace = $traces[1] ?? null;
		error_log(
			"WARNING safe_file_get_contents of $url failed ($errno) after ".($try + 1).' tries. '.
			($trace ? " called from {$trace['file']}:{$trace['function']}:{$trace['line']}" : '').
			(empty($_SERVER['REQUEST_URI']) ? '' : ' @ '.$_SERVER['REQUEST_URI'])
		);
	}
	return $data;
}

function safe_fsockopen(
	string	 $host,
	int		 $port	  = -1,
	?int 	&$errno	  = null,
	?string	&$msg	  = null,
	int 	 $timeout = 10,
) {
	if (preg_match('"^(?<protocol>\w+)://(?<host>.*)$"', $host, $match)) { // NOSONAR
		switch ($proto = strtolower($match['protocol'])) {
		case 'http':
			$port = 80;
			break;
		case 'https':
		case 'ssl':
			$port = 443;
			break;
		default:
			$errno = 666;
			$msg = 'safe_fsockopen failed, protocol not understood: '.$proto;
			return false;
		}
		$host = $match['host'];
	}
	static $__records, $__busy;
	if (isset($__busy[$host])) {
		$msg = 'busy';
		# mitigate loops
		return false;
	}
	$__busy[$host] = true;
	if (!isset($__records[$host])) {
		$records = dns_get_record($host, DNS_A | DNS_AAAA | DNS_CNAME | DNS_MX);
		if ($records
		&&	($ip = gethostbyname($host))
		) {
			$found = false;
			foreach ($records as $record) {
				if ($record['type'] === 'A'
				&&	$record['ip'  ] === $ip
				) {
					$found = true;
					break;
				}
			}
			if (!$found) {
				$records[] = [
					'host'	=> $host,
					'ip'	=> $ip,
					'type'	=> 'A',
				];
			}
		}
		$__records[$host] = $records;
	} else {
		$records = $__records[$host];
	}
	if (!$records) {
		$errno = 666;
		$msg = 'safe_fsockopen(host: '.$host.', port: '.$port.') no records found';
		return false;
	}
	require_once 'ipresolve.inc';
	foreach ($records as $record) {
		switch ($record['type']) {
		case 'A':
			if (RESOLVE_IPV
			&&	RESOLVE_IPV !== 4
			) {
				continue 2;
			}
			$ip = $record['ip'];
			break;

		/** @noinspection SpellCheckingInspection */
		case 'AAAA':
			if (RESOLVE_IPV
			&&	RESOLVE_IPV !== 6
			) {
				continue 2;
			}
			$ip = '['.$record['ipv6'].']';
			break;

		case 'CNAME':
			$cnames[] = $record['target'];
			continue 2;

		default:
			continue 2;
		}
		if ($sock = fsockopen(
			($port === 443 ? 'ssl://' : null).$ip,
			$port,
			$errno,
			$msg,
			$timeout
		)) {
			return $sock;
		}
	}
	if (!empty($cnames)) {
		foreach ($cnames as $cname) {
			if ($sock = safe_fsockopen($cname,$port,$errno,$msg,$timeout)) {
				return $sock;
			}
		}
	}
	return false;
}

/** @noinspection FunctionErrorSilencedInspection */
function safe_getimagesize(string $url, array $params = []): array|false {
	static $__size = [];
	return	$__size[$url]
	??=	(	$url[0] === '/'
		?	@getimagesize($url)	// NOSONAR
		:	@getimagesizefromstring(safe_file_get_contents($url, $params + [CURLOPT_CONNECTTIMEOUT => 5])) // NOSONAR
		);
}

// function safe_filesize(string $file): int|false {
// 	set_error_handler(static function() { /* ignore errors */ });
// 	/** @noinspection FunctionErrorSilencedInspection */
// 	$size = @filesize($file); // NOSONAR
// 	restore_error_handler();
// 	if ($size === false) {
// 		mail_log('safe_filesize failed to get filesize', get_defined_vars());
// 	}
// 	return $size;
// }

function safe_json_decode(string $data, ?bool $associative = null): mixed {
	/** @noinspection RedundantSuppression, JsonEncodingApiUsageInspection */
	if (null === ($decoded = json_decode($data, $associative))
		&& ($error = json_last_error())
		&&	$error !== JSON_ERROR_NONE
	) {
		mail_log('safe_json_decode failed to decode a json object, error: '.json_last_error_msg(), $data);
	}
	return $decoded;
}
function safe_json_encode(mixed $data, int $flags = 0, int $depth = 512): string|false {
	/** @noinspection JsonEncodingApiUsageInspection, FunctionErrorSilencedInspection */
	if (false === ($encoded = @json_encode($data, $flags, $depth) /* NOSONAR */ )) {
		mail_log('safe_json_encode failed to encode data, error: '.json_last_error_msg(), $data);
	}
	return $encoded;
}
function safe_json_encode_windows1252(int|string|bool|array|null $data, int $depth = 0): string|false {
	if (is_string($data)) {
		$data = win1252_to_utf8($data);
	} elseif (is_array($data)) {
		foreach ($data as /* $i => */ &$row) {
			$row = safe_json_encode_windows1252($row, $depth + 1);
		}
		unset($row);
	}
	if ($depth) {
		return $data;
	}
	return safe_json_encode($data);
}
function repeatable_safe_shuffle(array &$array, string|int $seed): array|false {
	try {
		$randomizer = new Random\Randomizer(new Random\Engine\PcgOneseq128XslRr64($seed));
		$array = $randomizer->shuffleArray($array);
	}
	/** @noinspection PhpRedundantCatchClauseInspection */
	catch (Random\RandomException $e) {
		mail_log('repeatable_safe_shuffle() failed to shuffle an array: '.$e->getMessage());
		return false;
	}
	return $array;
}
function safe_shuffle(array &$array): array|false {
	try {
		static $__randomizer = new Random\Randomizer();
		$array = $__randomizer->shuffleArray($array);
	}
	/** @noinspection PhpRedundantCatchClauseInspection */
	catch (Random\RandomException $e) {
		mail_log('safe_shuffle() failed to shuffle ab array: '.$e->getMessage());
		return false;
	}
	return $array;
}
function safe_random_int(?int $min = null, ?int $max = null): int|false {
	$min ??= PHP_INT_MIN;
	$max ??= PHP_INT_MAX;
	try {
		$int = random_int($min, $max);
	} catch (Random\RandomException $e) {
		mail_log('safe_random_int() failed to generate a random int: '.$e->getMessage());
		return false;
	}
	return $int;
}

const UTM_PARTYBUSSEN = 'utm_source=partyflock.nl&utm_medium=logo_advertentie';

function add_utm(
	string &$url,
	?string	$element  = null,
	?int	$id		  = null,
	?string	$platform = null,
	bool	$utf8	  = false,
): string {
	if (# Keep specific partybussen utm parameters:
		str_contains($url, UTM_PARTYBUSSEN)
		# URL not understood:
	||	!preg_match('"^(?<base_url>[^#]+?)(?<hash>#.+)?$"'.($utf8 ? 'u' : ''), $url, $match)
	) {
		return $url;
	}
	if (str_contains($url, 'ticketswap.')) {
		# TicketSwap wants these parameters to count or referals
		$source = 'partyflock';
		$medium = 'affiliate';
		$and_utm_campaign = '&utm_campaign=partyflock_affiliate';
		$and_utm_content = '&utm_content=website';
	} else {
		if (!empty($_SERVER['platform'])) {
			mail_log('_SERVER[platform] is set!');
		}
		$source = '2xpr';
		$medium = $_SERVER['platform'] ?? $platform ?? 'partyflock';
		$and_utm_campaign = '';
		$and_utm_content = $element ? '&utm_content='.urlencode($element.($id ? ':'.$id : '')) : '';	// NOSONAR
	}
	return	$url =
			$match['base_url'].
			(!str_contains($match['base_url'], '?') ? '?' : '&').
			"utm_source=$source&utm_medium=$medium".
			$and_utm_content.
			$and_utm_campaign.
			($match['hash'] ?? '');
}

function replace_utm(
	string	$url,
	?string	$element = null,
	?int	$id		 = null,
	bool	$utf8	 = false
): string {
	$utf8_mod = $utf8 ? 'u' : '';
	if (!str_contains($url, '?')) {
		return add_utm($url, $element, $id, utf8: $utf8);
	}
	$url = preg_replace('"utm_source=[^&]*"'.$utf8_mod, 'utm_source=2xpr', $url, -1, $reps);
	if (!$reps) {
		$url .= '&utm_source=2xpr';
	}
	$platform = $_SERVER['platform'] ?? 'partyflock';
	$url = preg_replace('"utm_medium=[^&]*"'.$utf8_mod, 'utm_medium='.$platform, $url, -1, $reps);
	if (!$reps) {
		$url .= '&utm_platform='.$platform;
	}
	$content = $element ? urlencode($id ? "$element:$id" : $element) : '';	// NOSONAR
	$url = preg_replace('"utm_content=[^&]*"'.$utf8_mod, $content ? 'utm_content='.$content : '', $url, -1, $reps);
	if (!$reps && $content) {
		$url .= '&utm_content='.$content;
	}
	return $url;
}

function safe_mkdir(string $path, int $mode = 0777, bool $recursive = false): bool {
	# NOTE: This issue is difficult to reproduce, as any concurrency-related issues are.
	#		It appears when several processes are attempting to create a directory which does not yet exist.
	#		Specifically, when one process is between is_dir() and mkdir() after another process has already managed to create the directory.
	#		See: https://github.com/kalessil/phpinspectionsea/blob/master/docs/probable-bugs.md#mkdir-race-condition
	error_clear_last();
	/** @noinspection FunctionErrorSilencedInspection */
	if (is_dir($path)
	||	@mkdir($path, $mode, $recursive) // NOSONAR
	||	is_dir($path)
	) {
		return true;
	}
	if ($error = error_get_last()) {
		if (str_contains($error['message'], 'File exists')) {
			return true;
		}
		error_log('mkdir("'.$path.'", '.decoct($mode).', '.($recursive ? 'recursive' : 'non-recursive').') failed: '.$error['message']);
	}
	return false;
}

function shuffle_assoc(array $array): array {
	$shuffled_keys = array_keys($array);
	safe_shuffle($shuffled_keys);
	$shuffled = [];
	foreach($shuffled_keys as $key) {
		$shuffled[$key] = $array[$key];
	}
	return $shuffled;
}

function recursive_array_diff(array &$a1, array &$a2): void {
	foreach ($a1 as $k => &$v) {
		if (array_key_exists($k, $a2)) {
			if (is_array($v)) {
				if (is_array($a2[$k])) {
					recursive_array_diff($v,$a2[$k]);
					if (!$v) {
						unset($a1[$k]);
					}
					if (!$a2[$k]) {
						unset($a2[$k]);
					}
				}
			} elseif ($v === $a2[$k]) {
				unset($a2[$k], $a1[$k]);
			}
		}
	}
}
