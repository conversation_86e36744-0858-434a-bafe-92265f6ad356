<?php

# required fields in contest:	party.MIN_AGE,STAMP,NOTIME,DURATION_SECS
#				contest.CLOSE_STAMP,TYPE,PARTYID

function may_participate(
	array	 $contest,
	int		 $userid,
	?string &$errstr	= null,
	bool	 $make_wins = false,
): bool {
	if (!$make_wins
	&&	$contest['CLOSED']
	) {
		$errstr = __('contest:join:error:closed_LINE');
		return false;
	}
	if (!$contest['ACTIVE']) {
		$errstr = __('contest:join:error:inactive_LINE');
		return false;
	}
	if ($contest['PSTAMP'] > CURRENTSTAMP) {
		$errstr = __('contest:join:error:not_published_yet_LINE', ['PSTAMP' => $contest['PSTAMP']]);
		return false;
	}
	if ($contest['ONLYPROMO']
	&&	!db_single(['connect','promo_letter_v2'],'
		SELECT 1
		FROM connect
		JOIN promo_letter_v2 AS l ON ASSOCID=PROMOID
		WHERE MAINTYPE = "contest"
		  AND MAINID = '.$contest['CONTESTID'].'
		  AND ASSOCTYPE = "promo"
		  AND l.USERID = '.$userid.'
		LIMIT 1')
	) {
		if (!query_failed()) {
			$errstr = __('contest:join:not_for_you_LINE');
		}
		return false;
	}
	require_once '_userpersonal.inc';
	static $__user;
	if (!isset($__user[$userid])) {
		$__user[$userid] = db_single_assoc('user','
			SELECT SQL_NO_CACHE REALNAME, ADDRESS, CITYID, ZIPCODE, EMAIL, CITY, BIRTH_YEAR, BIRTH_MONTH, BIRTH_DAY, LDAY, COUNTRYID, STATUS, SEX
			FROM user
			JOIN user_account USING (USERID)
			WHERE USERID = '.$userid
		);
	}
	if (false === ($user = $__user[$userid])) {
		return false;
	}
	if (!$user) {
		$errstr =  __('contest:may_not_participate:you_do_not_exist_LINE');
		return false;
	}
	if ($user['STATUS'] !== 'active') {
		$errstr = __('contest:may_not_participate:account_not_active_LINE');
		return false;
	}
	$flags = UCHK_REALNAME;
	if ($contest['PARTYID']) {
		$flags |= UCHK_BIRTHDATE;
	}
	switch ($contest['TYPE']) {
	case 'cd':
	case 'dvd':
	case 'other':
	case 'vinyl':
	case 'wear':
		$flags |= UCHK_ADDRESS;
		break;
	}
	if ($missing = missing_personal($user, $flags)) {
		$errstr = get_missing_personal_message($missing, $userid);
		return false;
	}

	$use_min_age = $user['SEX'] === 'F' && $contest['MIN_AGE_FEMALE'] ? $contest['MIN_AGE_FEMALE'] : $contest['MIN_AGE'];
	$max_age = $contest['MAX_AGE'];
	if ($use_min_age || $max_age) {
		require_once '_date.inc';
		$age = compute_age(
			$user['BIRTH_YEAR'],
			$user['BIRTH_MONTH'],
			$user['BIRTH_DAY'],
			!empty($contest['STAMP']) ? $contest['STAMP'] : $contest['CLOSE_STAMP'],
			$user['LDAY']
		);
		if ($use_min_age
		&&	$age < $use_min_age
		) {
			$errstr = __('contest:may_not_participate:too_young_LINE');
			return false;
		}
		if ($max_age
		&&	$age > $max_age
		) {
			$errstr = __('contest:may_not_participate:too_old_LINE');
			return false;
		}
	}
	if (!$contest['PARTYID']
	||	 $contest['NOTIME']
	||	!in_array($contest['TYPE'], [
			'guestlist',
			'freeticket',
			'eticket',
			'scancode',
		],	true)
	) {
		return true;
	}
	require_once '_overlap.inc';
	if (false === ($overlaps = db_simpler_array(['party', 'going'], "
		SELECT PARTYID
		FROM party
		JOIN going USING (PARTYID)
		WHERE MAYBE = 0
		  AND going.USERID = $userid
		  AND PARTYID != {$contest['PARTYID']}".
		  and_overlap_in_time($contest['STAMP'], $contest['DURATION_SECS'], JOIN_CONTEST)))
	) {
		return false;
	}
	if (!$overlaps) {
		return true;
	}
	$errstr = __('contest:may_not_participate:overlaps_LINE', DO_UBB, ['PARTYIDS' => implode(',', $overlaps)]);
	return false;
}
