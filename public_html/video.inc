<?php

require_once '_videotype.inc';

function get_title_parts(): ?array {
	if ($_REQUEST['ACTION'] === 'channel') {
		return [
			element_plural_name('videochannel'),
			$_REQUEST['subID'] ? escape_utf8(get_element_title('videochannel', $_REQUEST['subID'])) : ''
		];
	}
	return null;
}

function preamble(): void {
	if (!$_REQUEST['ACTION']) {
		moved_permanently('/video/recent');
	}
	if (isset($_REQUEST['CONTENTTYPE'])
	&&	preg_match('"^(?<path>/video/(?:\w+/)*)\?CONTENTTYPE=(?<content_type>.*)$"',$_SERVER['REQUEST_URI'],$match)
	) {
		see_other("{$match['path']}{$match['content_type']}");
	}
}
function perform_commit(): ?bool {
	return match($_REQUEST['ACTION']) {
		default			=> null,
		'fetchthumbs'	=> video_fetch_thumbs(),
		'inactive'		=> $_REQUEST['SUBACTION'] === 'markallseen' ? video_inactives_mark_all_seen() : null,
		'withouttype',
		'commit'		=> item_commit(),
		'combine'		=> require_once '_combine.inc' && combine_element(),
		'channel'		=> match ($_REQUEST['SUBACTION']) {
								default			=> null,
								'activate'		=> videochannel_set(1),
								'deactivate'	=> videochannel_set(0),
								'enable'		=> videochannel_set(null, 0),
								'disable'		=> videochannel_set(null, 1),
								'disdeactivate'	=> videochannel_set(0, 1),
								'add'			=> videochannel_add(),
							},
	};
}

function display_body(): void {
	switch ($_REQUEST['ACTION']) {
	default:
		not_found();
		return;

	case 'recent':
	case 'popular':

	case 'artist':
	case 'location':
	case 'organization':
	case 'party':
	case 'stream':
		video_display_overview($_REQUEST['ACTION']);
		return;

	case 'single':
	case 'comment':
	case 'comments':
	case 'commit':
	case 'fetchthumbs':
	case 'combine':
		video_display_single();
		return;

	case 'combinewith':
		require_once '_combine.inc';
		show_combine_with();
		return;

	case 'inactive':
		video_display_inactive();
		return;

	case 'register':
	case 'form':
		video_display_form();
		return;

	case 'channel':
		switch ($_REQUEST['SUBACTION']) {
		default:
			not_found();
			return;

		case 'add':
		case 'disable':
		case 'enable':
		case 'activate':
		case 'deactivate':
		case 'disdeactivate':
		case null:
			video_display_channel_overview();
			return;

		case 'popular':
			video_display_popular_channels();
			return;
		}

	case 'withouttype;':
		video_display_without_type();
		return;
	}
}

function video_menu(): array|false|null {
	$action = $_REQUEST['ACTION'];
	layout_open_menu();
	layout_menuitem(Eelement_name('overview'),'/video/recent',$overview = $action === 'recent');
	layout_menuitem(__C('field:popular'),'/video/popular/pasttwoweeks',$action === 'popular' && $_REQUEST['SUBACTION'] === 'pasttwoweeks');//,$action === 'popular' ? 'class="selected"' : null);
	if (have_admin('video')) {
		layout_menuitem(Eelement_plural_name('channel'),'/video/channel',$action === 'channel' && !$action && !$_REQUEST['subID']);
	}
	if ($overview) {
		layout_continue_menu();
		layout_menuitem(__C('action:add'),have_admin('video') ? '/video/form' : '/ticket/form?ELEMENT=video');
	}
	layout_close_menu();
	if (have_admin('video')) {
		return video_inactive_menu();
	}
	return null;
}

function video_inactive_menu(): array|false {
	$base = isset($_REQUEST['base']) ? 1 : 0;

	if (false === ($start_from_ids = db_simple_hash('videodone','
		SELECT BASE, MAX(VIDEOID)
		FROM videodone
		GROUP BY BASE'))
	) {
		return false;
	}
	assert(is_array($start_from_ids));	# Satisfy EA inspection
	if (false === ($total_and_max_inactive = db_single_array('video', "
		SELECT COUNT(*), MAX(VIDEOID)
		FROM video
		WHERE STATUS = 'inactive'"))
	||	false === ($inactive_since_base = db_single_int(['video', 'videodone'], /** @lang MariaDB */ '
		SELECT COUNT(*)
		FROM video
		JOIN videodone USING (VIDEOID)
		WHERE STATUS = "inactive"
		  AND VIDEOID > '.($start_from_ids[1] ?? 0)))
	) {
		return false;
	}
	if (!$total_and_max_inactive) {
		return [0, 0, 0];
	}
	[$all_total, $max_videoid] = $total_and_max_inactive;

	$in_inactives = $_REQUEST['ACTION'] === 'inactive';
	$all = $in_inactives && $_REQUEST['SUBACTION'] === 'all';

	if ($_REQUEST['ACTION'] !== 'channel') {
		layout_open_menu();
		if ($all_total) {
			layout_menuitem(Eelement_name('all_inactive'),'/video/inactive/all', $all);
		}
		if ($inactive_since_base) {
			layout_menuitem(Eelement_name('unseen_inactive'),'/video/inactive?base', $in_inactives && !$all && $base);
		}
		layout_close_menu();
		if ($in_inactives && !$all && ($start_from_ids[$base] ?? 0) < $max_videoid) {
			layout_open_menu();
			layout_menuitem(__C('action:mark_all_seen'),'/video/inactive/markallseen/'.$max_videoid.($base ? '?base' : null));
			layout_close_menu();
		}
	}
	if ($all)  {
		return [0, $all_total, $max_videoid];
	}
	return [$start_from_ids[$base] ?? 0, $base ? $inactive_since_base : $all_total, $max_videoid];
}

function video_display_without_type(): void {
	if (!require_admin('video')) {
		return;
	}
	if ($videoid = db_single('video', "
		SELECT VIDEOID
		FROM video
		WHERE STATUS = 'active'
		  AND CONTENTTYPE = ''
		LIMIT 1"
	)) {
		$_REQUEST['sID'] = $videoid;
	}
	video_display_form();
}

function video_display_overview(?string $element = null): void {
	layout_show_section_header();

	video_menu();

	require_once '_videolist.inc';

	$videolist = new _videolist();

	switch ($_REQUEST['ACTION']) {
	case 'popular':
		$currctype = $_SERVER['eDATA'] ?? '';
		$extra = $_REQUEST['SUBACTION'];
		break;

	case 'recent':
		$currctype = $_REQUEST['SUBACTION'];
		break;

	default:
		if (!require_idnumber($_REQUEST,'subID')) {
			return;
		}
		$currctype = $_REQUEST['SUBACTION'];
		$extra = $_REQUEST['subID'];
		break;
	}

	if ($currctype !== 'all') {
		if (!($video_spec = explain_table('video'))) {
			return;
		}
		if (!isset($video_spec['CONTENTTYPE']->enum[$currctype])) {
			http_response_code(404);
			register_error('video:error:video_type_does_non_exist_LINE',['TYPE' => $currctype]);
			return;
		}
	}

	$videolist->connected_to($element, $_REQUEST['subID']);
	if ($currctype) {
		$videolist->content_type($currctype);
	}
	$max = SMALL_SCREEN ? 22 : 40;
	$videolist->query($max);

	if (!($contenttypes = $videolist->types)
	&&	(	!($spec = explain_table('video'))
		||	!($contenttypes = get_video_content_types($spec)))
	){
		return;
	}

	$vtype = [];
	foreach ($contenttypes as $ctype) {
		if (!$ctype) {
			continue;
		}
		$vtype[$ctype] = __('videotype:'.$ctype);
	}

	# $vtype can be empty array, if all videos in list are not yes processed and have not type

	asort($vtype);

	if (isset($vtype['other'])) {
		$desc = $vtype['other'];
		unset($vtype['other']);
		$vtype['other'] = $desc;
	}

	layout_open_menu();
	layout_open_menuitem();
	?><form<?
	?> class="ib"<?
	?> onsubmit="
		this.className += ' light';
		location.href = this.action + (this.CONTENTTYPE.selectedIndex ? this.CONTENTTYPE.value : '');
		return false;"<?
	?> method="get"<?
	?> action="/video/<?= $element ?>/<? if (isset($extra)) { echo $extra ?>/<? } ?>"><?
	?><select<?
	?> onchange="if (this.form.onsubmit(null)) { this.form.submit(); }"<?
	?> name="CONTENTTYPE"<?
	?>><?
		?><option value=""><?= element_plural_name('all_video') ?> (<?= __('attrib:no_musiconly') ?>)</option><?
		?><option<?
		if ($currctype === 'all') {
			?> selected<?
		}
		?> value="all"><?= element_plural_name('all_video') ?></option><?
	foreach ($vtype as $ctype => $desc) {
		?><option<?
		if ($ctype === $currctype) {
			?> selected<?
		}
		?> value="<?= $ctype ?>"><?= $desc ?></option><?
	}
	?></select><?
	?><noscript><?
	?><input type="submit" value="<?= __('action:show') ?>" /><?
	?></noscript><?
	?></form><?
	layout_close_menuitem();
	layout_close_menu();

	?><article itemscope itemtype="https://schema.org/VideoGallery"><?
	$videolist->show_thumbs();
	$videolist->display();
	?></article><?
}

function video_fetch_thumbs(): bool {
	if (!require_admin('video')
	||	!($videoid = require_idnumber($_REQUEST,'sID'))
	) {
		return false;
	}
	$video = db_single_array('video','SELECT TYPE,EXTERNALID FROM video WHERE VIDEOID='.$videoid);
	if (!$video) {
		if ($video !== false) {
			register_error('video:error:nonexistent_LINE', ['ID' => $videoid]);
		}
		return false;
	}
	require_once '_cachevideothumbs.inc';
	[$type, $externalid] = $video;
	if (cache_video_thumbs($videoid, $type, $externalid, force: true)) {
		register_notice('video:notice:thumbnails_fetched_LINE');
		return true;
	}
	return false;
}

function videochannel_set(int|string|null $active = 'ACTIVE', int|string $disabled = 'DISABLED'): bool {
	if ($active === null) {
		$active = 'ACTIVE';
	}
	if (!require_admin('video')
	||	!($channelid = require_idnumber($_REQUEST, 'subID'))
	||	!db_insert('videochannel_log','
		INSERT INTO videochannel_log
		SELECT * FROM videochannel
		WHERE CHANNELID='.$channelid)
	||	!db_update('videochannel','
		UPDATE videochannel SET
			MUSERID	 = '.CURRENTUSERID.',
			MSTAMP	 = '.CURRENTSTAMP.",
			ACTIVE	 = $active,
			DISABLED = $disabled
		WHERE CHANNELID = $channelid")
	) {
		return false;
	}
	register_notice(
		is_number($active)
	?	(!$active   ? 'video:notice:channel_deactivated_LINE' : 'video:notice:channel_activated_LINE')
	:	(!$disabled ? 'video:notice:channel_enabled_LINE'	  : 'video:notice:channel_disabled_LINE')
	);
	return true;
}

function videochannel_add(): bool {
	if (!require_admin('video')
	||	!require_something_trim($_POST, 'ID')
	||	!($name = require_anything_trim($_POST, 'NAME', utf8: true))
	||	!($type = require_element($_POST, 'TYPE', ['youtube', 'vimeo']))
	||	!($youtube_type = require_element($_POST, 'YOUTUBE_TYPE', ['channel', 'custom', 'handle', 'user'], true))
	||	!db_insert('videochannel',"
		INSERT INTO videochannel SET
			TYPE			= '$type',
			YOUTUBE_TYPE	= ".($type === 'youtube' ? "'$youtube_type'" : 'NULL').',
			ID				= '.($_POST['ID'] ? "'".addslashes($_POST['ID'])."'" : 'NULL').',
			CUSERID			= '.CURRENTUSERID.',
			CSTAMP			= '.CURRENTSTAMP)
	/** @noinspection NotOptimalIfConditionsInspection */
	||	($channelid = db_insert_id())
	&&	$name
	&&	!db_update(['video', 'videoinfo'], "
		UPDATE video
		JOIN videoinfo USING (VIDEOID)
			SET CHANNELID = $channelid
		WHERE CHANNEL = '".addslashes($_POST['NAME'])."'")
	) {
		return false;
	}
	register_notice('video:notice:channel_added_LINE');
	return true;
}

function video_display_popular_channels(): void {
	require_once '_period.inc';
	require_once '_presence.inc';

	if (!require_super_admin()) {
		no_permission();
		return;
	}
	if (!isset($_SERVER['eDATA'])) {
		not_found();
		return;
	}
	$parts = explode('/',$_SERVER['eDATA']);
	if (!($info = get_period_form('/video/channel/popular', $parts[0], !empty($parts[1]) && $parts[1] === 'progressive'))) {
		if ($info !== false) {
			not_found();
		}
		return;
	}
	[$form, $period, $progressive] = $info;
	layout_show_section_header(Eelement_plural_name('popular_videochannel'));
	video_menu();

	layout_open_box('white');
	echo $form;
	layout_close_box();

	if (!($stats = memcached_simple_hash('videoview', /** @lang MariaDB*/ '
		SELECT VIDEOID, '.($progressive ? 'ROUND(SUM( 1 - (('.TODAYSTAMP." - CAST(STAMP AS SIGNED)) / $period)))" : 'COUNT(*)').'
		FROM videoview
		WHERE STAMP>='.(TODAYSTAMP - $period).'
		GROUP BY VIDEOID',
		$period > ONE_DAY ? ONE_DAY : ONE_HOUR))
	) {
		return;
	}
	ksort($stats);

	$videoidstr = implodekeys(',',$stats);

	if (!($infos = memcached_simple_hash('video', "
		SELECT VIDEOID, CHANNELID
		FROM video
		WHERE VIDEOID IN ($videoidstr)"))
	) {
		return;
	}
	$channel_stats = [];
	foreach ($infos as $videoid => $channelid) {
		$channel_stats[$channelid] = ($channel_stats[$channelid] ?? 0) + $stats[$videoid];
	}
	arsort($channel_stats);

	$channelidstr = implodekeys(',',$channel_stats);

	if (!($channelinfo = memcached_rowuse_hash('videochannel', "
		SELECT CHANNELID, NAME, TYPE
		FROM videochannel
		WHERE CHANNELID IN ($channelidstr)"))
	||	false === ($connections = db_multirow_hash('connect', "
		SELECT MAINID, ASSOCTYPE, ASSOCID
		FROM connect
		WHERE MAINTYPE = 'videochannel'
		  AND MAINID IN ($channelidstr)"))
	) {
		return;
	}
	assert(is_array($connections)); # Satisfy EA inspection

	layout_open_box('white');
	layout_open_table('hpadded default');
	?><tr><?
	?><th></th><?
	?><th></th><?
	?><th><?= Eelement_name('videochannel') ?></th><?
	?><th><?= Eelement_plural_name('connection') ?></th><?
	?></tr><?
	foreach ($channel_stats as $channelid => $cnt) {
		if (!$channelid) {
			$NAME = '';
		} else {
			/** @noinspection PhpRedundantOptionalArgumentInspection */
			extract($channelinfo[$channelid], \EXTR_OVERWRITE);
		}
		?><tr><?

	# NUMBER
		?><td class="right"><?= $cnt ?></td><?

	# ICON

		?><td><?
		if ($channelid) {
			show_presence_icon($TYPE);
		}
		?></td><?

	# CHANNEL LINK
		?><td><?
		?><a href="/video/channel/<?= $channelid ?>"><?= $NAME ? escape_utf8($NAME) : element_name('collection') ?></a><?
		?></td><?

	# CONNECTIONS
		?><td><?
		if (isset($connections[$channelid])) {
			$parts = [];
			foreach ($connections[$channelid] as $element => $ids) {
				foreach ($ids as $id) {
					$parts[] = get_element_link(
						in_array($element,['remixer', 'producer'], true) ? 'artist' : $element,
						$id
					);
				}
			}
			echo implode(', ',$parts);
		}
		?></td><?
		?></tr><?
	}
	layout_close_table();
	layout_close_box();
}

function video_display_channel_overview(): void {
	if ($_REQUEST['subID']
	||	preg_match('"/video/channel/0(?:/page/\d+)?"',$_SERVER['REQUEST_URI'])
	) {
		video_display_channel_video_list();
		return;
	}
	if (!require_admin('video')) {
		no_permission();
		return;
	}
	layout_show_section_header(Eelement_plural_name('videochannel'));
	video_menu();
	if ($video_admin = have_admin('video')) {
		?><form<?
		?> accept-charset="utf-8"<?
		?> method="post"<?
		?> action="/video/channel/add"<?
		?> onsubmit="return submitForm(this);"><?

		?><div class="block"><?
		?><select name="TYPE" class="rmrgn" required id="test" onchange="setdisplay(this.form.YOUTUBE_TYPE, this.selectedIndex === 2);"><?
			?><option></option><?
			?><option value="youtube">youtube</option><?
			?><option value="vimeo">vimeo</option><?
		?></select><?
		?><select name="YOUTUBE_TYPE" class="rmrgn hidden"><?
			?><option value="channel">channel</option><?
			?><option value="custom">custom</option><?
			?><option value="handle">handle</option><?
			?><option value="user">user</option><?
		?></select><?
		?><input required name="ID" type="text" class="short" placeholder="<?= element_name('id') ?>" /> <span class="tt"><?
		?></span> <?
		?><input value="<?= __('action:add') ?>" type="submit" /><?
		?></div><?
		?></form><?
	}

	if (false === ($channels = memcached_rowuse_hash(['videochannel', 'video'], '
		SELECT c.CHANNELID,NAME, c.TYPE, ACTIVE, DISABLED, COUNT(video.VIDEOID) AS CNT
		FROM videochannel AS c
		LEFT JOIN video ON video.CHANNELID = c.CHANNELID'.
		($video_admin ? '' : ' AND STATUS = "active"').'
		GROUP BY CHANNELID
		ORDER BY NAME'))
	) {
		return;
	}
	if (!$channels) {
		?><div class="block"><?= __('video:info:no_channels_LINE') ?></div><?
		return;
	}
	if (false === ($connections = db_multirow_hash('connect', "
		SELECT MAINID, ASSOCTYPE, ASSOCID
		FROM connect
		WHERE MAINTYPE = 'videochannel'"))
	) {
		return;
	}
	require_once '_connect.inc';
	if ($video_admin = have_admin('video')) {
		include_js('js/connect');
	}
	require_once '_presence.inc';
	layout_open_box('gallery');
	layout_open_table(TABLE_FULL_WIDTH | TABLE_ROW_HILITE_ONMOUSEOVER | TABLE_VTOP);
	foreach ($channels as $channelid => $channel) {
		if (!$video_admin
		&&	(	 $channel['DISABLED']
			||	!$channel['CNT']
			)
		) {
			continue;
		}
		layout_start_rrow_right($channel['ACTIVE'] ? 0 : ROW_LIGHT,$channel['DISABLED'] ? 'unavailable' : null);
		show_presence_icon($channel['TYPE']);
		layout_next_cell(class: 'right');
		if ($channel['CNT']) {
			echo $channel['CNT'];
		}
		layout_next_cell();
		?><a href="/video/channel/<?= $channelid ?>"><?= escape_utf8($channel['NAME'] ?: '!EMPTY!') ?></a><?
		layout_next_cell();
		if (isset($connections[$channelid])) {
			foreach ($connections[$channelid] as $element => $ids) {
				foreach ($ids as $id) {
					echo element_name($element),': ',get_element_link(in_array($element,['remixer','producer']) ? 'artist' : $element,$id) ?><br /><?
				}
			}
		}
		layout_stop_row();
	}
	layout_close_table();
	layout_close_box();
}

function video_inactives_mark_all_seen(): bool {
	if (!($videoid = $_REQUEST['ssID'])
	||	!have_admin('video')
	) {
		return false;
	}
	$base = isset($_REQUEST['base']) ? '1' : '0';
	if (!db_insert('videodone', "
		INSERT IGNORE INTO videodone SET
			VIDEOID	= $videoid,
			BASE 	= $base,
			USERID	= ".CURRENTUSERID.',
			STAMP	= '.CURRENTSTAMP)
	) {
		return false;
	}
	register_notice('video:info:inactives_marked_seen_LINE');

	if (false !== (/* $msgid = */ store_messages_in_cookie())) {
		header('Location: https://'.$_SERVER['HTTP_HOST'].'/video/inactive'.(isset($_REQUEST['base']) ? '?base' : ''));
		exit;
	}
	return false;
}

function video_display_inactive(): void {
	layout_show_section_header();
	if (!require_admin('video')) {
		return;
	}
	$menu_stats = video_menu();
	[$doneid, $total /*, $maxid */] = $menu_stats ?: [0, 0, 0];

	?><select onchange="location.href = '/video/inactive?base&amp;PAGE=<?= escape_specials($_REQUEST['PAGE'] ?? '') ?>&amp;FILTER=' + this.value;"><?
		?><option></option><?
		$active_filter = $_REQUEST['FILTER'] ?? null;
		foreach ([
			'20+mins',
			'aftermovie',
			'anthem',
			'documentary',
			'endshow',
			'musicvideo',
			'teaser',
			'trailer',
		] as $filter) {
			?><option<?
			if ($active_filter === $filter) {
				?> selected<?
			}
			?> value="<?= str_replace('+', '%2B', escape_specials($filter)) ?>"<?
			?>><?= escape_specials($filter) ?></option><?
		}
	?></select><?
	require_once '_pagecontrols.inc';
	$controls = new _pagecontrols();
	$controls->set_per_page(200);
	$controls->set_total($total);
	$controls->absolute_limit(1_000_000);
	$controls->include_all(true);
#	$controls->include_last(false);
	#$controls->set_order('video.PSTAMP ASC,NAME, videoinfo.TITLE');
	# $base = isset($_REQUEST['base']) ? 1 : 0;
	set_memory_limit(2 * GIGABYTE);
	$controls->set_order('CHANNELID DESC, video.PSTAMP ASC, videoinfo.TITLE ASC');

	if (!($inactives = db_rowuse_hash(['video', 'videoinfo', 'videostats', 'videodone'], "
		SELECT	DISTINCT video.VIDEOID, video.CHANNELID, video.TYPE, DURATION,
				videoinfo.PSTAMP, videoinfo.TITLE, videoinfo.CONTENT,
				viewCount, likeCount, dislikeCount, favoriteCount, commentCount,
				videochannel.TYPE AS CHANNEL_TYPE, YOUTUBE_TYPE, videochannel.ID, videochannel.NAME,
				(SELECT 1 FROM connect WHERE MAINTYPE = 'video' AND MAINID = video.VIDEOID AND ASSOCTYPE = 'party' LIMIT 1) AS HAVE_PARTY
		FROM video
		LEFT JOIN videoinfo USING (VIDEOID)
		LEFT JOIN videostats USING (VIDEOID)
		LEFT JOIN videochannel USING (CHANNELID)
		LEFT JOIN videodone USING (VIDEOID)
		WHERE STATUS = 'inactive'
		  AND videodone.VIDEOID IS NULL
		  AND video.VIDEOID > ".($doneid ?: 0).
		$controls->order_and_limit()))
	) {
		return;
	}

	$controls->sort($inactives);

	# $max_likes = $max_views = $max_comments = 1;

	$videoids = array_keys($inactives);
	sort($videoids);
	$videoidstr = implode(', ', $videoids);
	if (false === ($all_view_stats = memcached_simple_hash('videoview_counter', '
		SELECT VIDEOID, SUM(VIEWS)
		FROM videoview_counter
		WHERE DAYNUM >= TO_DAYS(NOW()) - '.TWO_WEEKS.'
		GROUP BY VIDEOID',
		ONE_HOUR))
	) {
		return;
	}
	# Satisfy EA inspection
	$all_view_stats = (array) $all_view_stats;
/*	if (false === ($youtube_all_view_stats = memcached_simple_hash('videostat', '
		SELECT VIDEOID, viewCount
		FROM videostats
		WHERE VIDEOID IN ('.$videoidstr.')'
	))) {
		return;
	}*/
	if (false === ($all_channel_videos = memcached_simple_hash('video', "
		SELECT VIDEOID, CHANNELID
		FROM video
		WHERE VIDEOID IN ($videoidstr)"
	))) {
		return;
	}
	$channel_ids = array_unique(array_values($all_channel_videos));
	sort($channel_ids);
	if ($channel_ids[0] === 0) {
		array_shift($channel_ids);
	}
	if (false === ($channel_to_videos = memcached_simple_hash('video', '
		SELECT CHANNELID, GROUP_CONCAT(VIDEOID)
		FROM video
		WHERE CHANNELID IN ('.implode(', ', $channel_ids).")
		  AND STATUS = 'active'
		GROUP BY CHANNELID"))
	) {
		return;
	}

	$channelstats = [];
/*	if ($all_channel_videos) {
		foreach ($all_channel_videos as $videoid => $channelid) {
			error_log('increasing channel '.$channelid.' with '.($all_view_status[$videoid] ?? 0));
			incorset($channelstats, $channelid, $all_view_stats[$videoid] ?? 0);
		}
	}
	unset($channelstats[0]);*/
	foreach ($channel_to_videos as $channelid => $videoidstr) {
		foreach (explode(',', $videoidstr) as $videoid) {
			if ($add = $all_view_stats[$videoid] ?? 0) {
				increment_or_set($channelstats, $channelid, $add);
			}
		}
	}

	arsort($channelstats);
	[, $max] = keyval($channelstats);

	foreach ($inactives as $inactive) {
		$pagestats[$channelid = $inactive['CHANNELID']] = getifset($channelstats,$channelid);
	}

	arsort($pagestats);
	[, $local_max] = keyval($pagestats);

	foreach ($pagestats as $channelid => $cnt) {
		$pctstats[$channelid] = $cnt ? [round(100 * $cnt / $local_max),round(100 * $cnt / $max)] : [0,0];
	}
	if ($channelstats) {
		$channel_array = array_values($channelstats);
		$size = count($channel_array);
		# top 50%:
		# $median_50 = $channel_array[floor($size / 2)];
		# top 10%
		$median_10 = $channel_array[(int)floor($size / (100 / 10))];
		# top 5%
		$median_5 = $channel_array[(int)floor($size / (100 / 5))];
		# top 2%
		$median_2 = $channel_array[(int)floor($size / (100 / 2))];
		# top 1%
		$median_1 = $channel_array[(int)floor($size / 100)];
	} else {
		# $median_50 = 0;
		$median_10 = 0;
		$median_5  = 0;
		$median_2  = 0;
		$median_1  = 0;
	}

	$controls->display_and_store();

	$min_duration = null;
	$find = null;

	static $__find = [
		'aftermovie'	=> ['after\s*movie', '\brecap', '𝗔𝗳𝘁𝗲𝗿\s*𝗺𝗼𝘃𝗶𝗲'],
		'endshow'		=> ['ei?nd\s*show'],
		'musicvideo'	=> ['music\s*video', 'prodby', 'prod\.', 'official\s*(?:hardstyle)\s*video'],
	];

	switch ($active_filter) {
	case '20+mins':
		$min_duration = 20 * 60;
		break;

	case 'aftermovie':
		$find = ['after\s*movie', 'official\s*recap', '𝗔𝗳𝘁𝗲𝗿\s*𝗺𝗼𝘃𝗶𝗲'];
		break;

	case 'endshow':
		$find = ['ei?nd\s*show'];
		break;

	case 'musicvideo':
		$find = ['music\s*video', 'prodby', 'prod\.', 'official\s*(?:hardstyle)\s*video'];
		break;

	default:
		if (isset($__find[$active_filter])) {
			$find = $__find[$active_filter];
		} elseif ($active_filter) {
			$find = [$active_filter];
		}
	}
	include_js('js/sorttable');
	layout_open_box('white');
	layout_open_table('fw hha defpad vtop sortable');
	layout_start_header_row();
	layout_header_cell_right(substr(Eelement_name('publication'),0,3),CELL_RIGHT_SPACE);
#	layout_header_cell(null,null,'hpad');
	layout_header_cell(class: 'hpad');
	layout_header_cell_center('G', class: 'hpad');
#	layout_header_cell(Eelement_name('type'),null,'center hpad');
	layout_header_cell(Eelement_name('channel'), class: 'hpad');
#	layout_header_cell(Eelement_plural_name('view'));
	layout_header_cell(Eelement_name('title'));
	layout_header_cell();
	layout_stop_header_row();
	$row_count = 0;
	foreach ($inactives as $inactive) {
		/** @noinspection PhpRedundantOptionalArgumentInspection */
		extract($inactive, \EXTR_OVERWRITE);
		if ($find) {
			$found = false;
			foreach ($find as $regex) {
				if (preg_match("!$regex!iu", $TITLE)) {
					error_log("found $regex in $TITLE");
					$found = true;
					break;
				}
			}
			if (!$found) {
				continue;
			}
		}
		if ($min_duration && $DURATION < $min_duration) {
			continue;
		}
		$content_types = find_vidtype($TITLE, true);
		$row_class = null;

		if ($pctstat = $pctstats[$CHANNELID] ?? null) {
			[/* $local */, $global] = $pctstat;
			if ($global > 5) {
				$row_class = 'notice-nb ns';
			}
		}
		$channel_views = $pagestats[$CHANNELID] ?? 0;

/*		if (!$row_class) {
			if ($viewCount / $max_views > .5
			||	$likeCount / $max_likes > .5
			||	$commentCount / $max_comments > .5
			) {
				$row_class = 'notice-nb ns';
			}
		}*/

		?><tr id="row-<?= $VIDEOID ?>" class="hh<?
		if ($row_count & 1) {
			?> zebra<?
		}
		if (isset($content_types['musiconly'])) {
			?> light<?
		}
		if ($row_class) {
			?> <? echo $row_class;
		}
		if ($HAVE_PARTY) {
			?> notice<?
		}
		?>"><?
		++$row_count;
		?><td class="right rpad" data-value="<?= $PSTAMP ?>"><?

		?><span class="small nowrap"><? _date_display($PSTAMP, short: true, time_span: true) ?></span><?

		// [$page, $player] = get_page_player_and_type($inactive);
		# IMPORTANCE
/*		if ($pctstats) {
			layout_next_cell(class: 'hpad right');
			if ($global) {
				echo $global,'%';
			}
		} else {
			layout_next_cell();
			layout_next_cell();
		}*/
		# DURATION
		layout_next_cell(class: 'right hpad');
		if ($DURATION) {
			echo get_time_duration($DURATION);
		}
		# CHANNEL ICON
		layout_next_cell(class: 'center hpad');
		?><img style="vertical-align: text-bottom;" class="ptr" src="<?= STATIC_HOST ?>/presence/<?= ($CHANNEL_TYPE ?: $TYPE), is_high_res()
		?>.png" width="16" height="16" alt="play" /><?
		?></td><?
		# CHANNEL
		?><td class="hpad nowrap relative" style="overflow: hidden;"><?
/*		?><div class="grdnt v2"></div><?*/
		if ($CHANNELID) {
			?><a target="_blank" class="small" href="<?= get_element_href('videochannel', $CHANNELID) ?>"><?=
				escape_utf8(get_element_title('videochannel', $CHANNELID))
			?></a><?
		}

		if ($channel_views >= $median_10) {
			?> <span class="notice"><?
			if ($channel_views >= $median_1) {
				?>++++<?
			} elseif ($channel_views >= $median_2) {
				?>+++<?
			} elseif ($channel_views >= $median_5) {
				?>++<?
			} else {
				?>+<?
			}
			?></span><?
		}

/*		?><td class="small right"><?
		if (!empty($youtube_all_view_stats[$VIDEOID])) {
			echo $youtube_all_view_stats[$VIDEOID];
		}*/

		# TITLE
		# PROBABLE TYPES
		?></td><td style="min-width: 60%;"<?

		?>><?
		if ($content_types
		&&	(	count($content_types) > 1
			||	!isset($content_types['musiconly'])
			)
		) {
			?><small class="notice"><?= implode(', ',$content_types) ?>: </small><?
		}

		?><a title="<?= escape_utf8(clean_utf8($CONTENT)) 	?>"<?
		?> onclick="changerow(this, 'remove');"<?
		?> target="_blank"<?
		?> href="/video/<?= $VIDEOID ?>/form"><?= escape_utf8($TITLE) ?></a><?

		layout_stop_row();
	}
	layout_close_table();
	layout_close_box();
	$controls->display_stored();
}

function video_display_channel_video_list(): void {
	if (!($channelid = require_number($_REQUEST,'subID'))) {
		return;
	}
	if (!($channel = db_single_array('videochannel', "
		SELECT ACTIVE, DISABLED
		FROM videochannel
		WHERE CHANNELID = $channelid"))
	) {
		not_found();
		return;
	}
	[$active, $disabled] = $channel;

	layout_show_section_header(Eelement_plural_name('videochannel'));

	video_menu();

	if (have_admin('video')) {
		require_once '_connect.inc';

		layout_open_menu();
		layout_menuitem(__C($active		? 'action:deactivate'	: 'action:activate'),	"/video/channel/$channelid/".($active		? 'deactivate'	: 'activate'));
		layout_menuitem(__C($disabled	? 'action:enable'		: 'action:disable'),	"/video/channel/$channelid/".($disabled	? 'enable'		: 'disable'));
		if ($active && !$disabled) {
			layout_menuitem(__C('action:disable').' &amp; '.__C('action:deactivate'), "/video/channel/$channelid/disdeactivate");
		}

		layout_open_menuitem();
		connect_menuitem(null,'videochannel',$channelid);
		layout_close_menuitem();
		layout_close_menu();

		_connect_display_form('videochannel',$channelid);
	}

   	?><article itemscope itemtype="https://schema.org/VideoGallery"><?
	require_once '_videolist.inc';
	$videolist = new _videolist();
	$videolist->force_open_box = true;
	$videolist->channel($channelid);
	$videolist->query(200);
	$videolist->display();
	?></article><?
}

function video_display_single(): void {
	require_once '_commentlist.inc';
	require_once '_connect.inc';
	require_once '_embedvideo.inc';
	require_once '_lastseen.inc';
	require_once '_ticketlist.inc';
	require_once '_ubb_preprocess.inc';
	require_once '_videolist.inc';
	require_once '_videometa.inc';
	require_once '_videosource.inc';
	require_once '_videothumb.inc';

	if (!($videoid = require_idnumber($_REQUEST, 'sID'))) {
		not_found();
		return;
	}
	if (!($video = memcached_single_assoc_if_not_admin('video', 'video', "
		SELECT	video.*,
				videochannel.NAME AS CHANNEL_NAME, videochannel.TYPE AS CHANNEL_TYPE, DISABLED,
				videoinfo.CHANNEL AS VI_CHANNEL, videoinfo.CONTENT, videoinfo.TITLE AS ORIGINAL_TITLE,
				IF (STATUS = 'active', NULL, (SELECT 1 FROM video_log WHERE STATUS = 'active' AND VIDEOID = $videoid LIMIT 1)) AS ONCE_ACTIVE
		FROM video
		LEFT JOIN videochannel USING (CHANNELID)
		LEFT JOIN videoinfo USING (VIDEOID)
		WHERE VIDEOID = $videoid"
	))) {
		if ($video !== false) {
			register_error('video:error:nonexistent_LINE', ['ID' => $videoid]);
			not_found();
		}
		return;
	}

	if (!have_admin('video')) {
		if ($video['STATUS'] !== 'active') {
			register_error('video:error:inactive_LINE', ['ID' => $videoid]);
			not_found();
			return;
		}
		if ($video['DISABLED']) {
			register_error('videochannel:error:disabled_LINE',['ID'=>$videoid]);
			not_found(); return;
		}
	}
	layout_show_section_header();

	$video_admin = have_admin('video');

	include_video_og($videoid);

	video_menu();

	if ($video_admin) {
		layout_open_menu();
		layout_menuitem(__C('action:change'), '/video/'.$videoid.'/form');
		layout_open_menuitem();
		connect_menuitem();
		layout_close_menuitem();
		layout_open_menuitem();
		?><span class="nobutton"><?=
			Eelement_name('status') ?>: <?=
			__('status:'.($video['DISABLED'] ? 'disabled' : $video['STATUS']))
		?></span><?
		layout_close_menuitem();
		layout_continue_menu();
		show_element_menuitems();
		layout_menuitem(__C('action:fetch_thumbs'),'/video/'.$videoid.'/fetchthumbs');
		layout_close_menu();
		_connect_display_form('video',$videoid);
	}

	show_connected_tickets();

	$meta = new videometa($videoid,$video);

	$title = $meta->get_horizontal_title($videoid);

	layout_open_box(($video['STATUS'] === 'active' && !$video['DISABLED'] ? '' : 'light ').'white','video');
	layout_open_box_header();
	?><h1 class="video-title"><?= $title ?></h1><?
	layout_continue_box_header();
	ob_start();
	show_video_source($video);
	$prodstr = ob_get_clean();

	if (($channel_name = $video['CHANNEL_NAME'] ?: (have_admin('video') ? $video['VI_CHANNEL'] : null))
	||	$prodstr
	) {
		?><small><?
		echo $prodstr;
		if ($channel_name) {
			if ($prodstr) {
				?>, <?
			}
			if ($video['CHANNELID']) {
				echo get_element_link('videochannel',$video['CHANNELID']);
			}
		}
		?></small><?
	}
	layout_close_box_header();

	?><div class="centered block"><?=
		embed_video(
			$video,
			max_width: .9,
			aspect: $video['ASPECT'] ?? null,
		)
	?></div><?
	if (have_admin('video')) {
		layout_display_alteration_note($video);
	}
	layout_close_box();

	if ($video['CONTENT']) {
		layout_open_box('white body');
		expandable_header(
			Eelement_name('information').' &hellip;',
			'information',
			null,
			true,
			escape_utf8($video['ORIGINAL_TITLE']),
		);
		?><div class="hidden block" id="information"><?=
			make_all_html(_ubb_preprocess(clean_utf8($video['CONTENT']), UBB_UTF8), UBB_UTF8)
		?></div><?
		layout_close_box();
	}

	# get popular movies same time, same timespan

	$period = 6 * ONE_MONTH;

	if ($otherids = memcached_simpler_array(['video', 'videochannel'], "
		SELECT VIDEOID
		FROM video
		LEFT JOIN videochannel USING (CHANNELID)
		WHERE STATUS = 'active'
		  AND (DISABLED IS NULL OR DISABLED = 0)
		  AND CONTENTTYPE = '{$video['CONTENTTYPE']}'
		  AND PSTAMP BETWEEN ".(TODAYSTAMP - $period).' AND '.TODAYSTAMP)
	) {
		$otheridstr = implode(',',$otherids);
		$progressive = true;
		# This progressive ranking could use an explanation
		if ($stats = memcached_simple_hash('videoview', /** @lang MariaDB */ '
			SELECT VIDEOID, '.(
				$progressive
			?	'ROUND(SUM( IF(STAMP > '.(TODAYSTAMP - SIX_MONTHS).', 100, 1) * -LOG10( POW( (('.TODAYSTAMP." - CAST(STAMP AS SIGNED)) / $period)/1.1 + .1, 10) ) ))"
			:	'COUNT(*)'
			).'
			FROM videoview
			WHERE STAMP > '.(TODAYSTAMP - $period)."
			  AND VIDEOID IN ($otheridstr)
			  AND VIDEOID != $videoid
			GROUP BY VIDEOID
			ORDER BY COUNT(*) DESC",
			ONE_DAY)
		) {
			$seen = get_last_seen('video', $otheridstr);
			$show = SMALL_SCREEN ? 4 : 8;
			$show_list = [];
			foreach ($stats as $local_videoid => /* $cnt = */ $ignored) {
				if (isset($seen[$local_videoid])) {
					continue;
				}
				$show_list[$local_videoid] = $local_videoid;
				if (!--$show) {
					break;
				}
			}
			if ($show_list) {
				$videolist = new _videolist();
				$videolist->videoids($show_list);
				if ($videolist->query()) {
					?><article itemscope itemtype="https://schema.org/VideoGallery"><?
					layout_open_box('white');
					layout_box_header(__C('popular(video)').($video['CONTENTTYPE'] ? ' '.__('videotypes:'.$video['CONTENTTYPE']) : ''));
					?><div class="nowrap" style="overflow-x: auto; overflow-y: hidden;"><?
					$videolist->dont_open_box = true;
					$videolist->show_thumbs();
					layout_close_box();
					?></article><?
				}
			}
		}
	}
	$cmts = new _commentlist();
	$cmts->item($video);
	$cmts->display();

	mark_item();

	if (!ROBOT
	&&	!dont_register()
	&&	!have_admin('video')
	) {
		db_insert('videoview','
		INSERT INTO videoview SET
			VIDEOID	= '.$videoid.',
			STAMP	= '.CURRENTSTAMP.',
			USERID	= '.CURRENTUSERID.',
			IPBIN	= "'.addslashes(CURRENTIPBIN).'",
			IDENTID	= '.CURRENTIDENTID
		);
	}
	counthit('videoview',$videoid);
}

function video_display_form(): void {
	require_once '_connect.inc';
	require_once '_embed.inc';
	require_once '_ubb_preprocess.inc';

	if (!require_admin('video')) {
		return;
	}
	if ($videoid = have_idnumber($_REQUEST,'sID')) {
		if (!($video = db_single_assoc('video', "
			SELECT video.*, vc.NAME, vc.TYPE AS CHANNEL_TYPE, vc.ID, vc.YOUTUBE_TYPE
			FROM video
			LEFT JOIN videochannel AS vc USING (CHANNELID)
			WHERE VIDEOID = $videoid",
			DB_USE_MASTER))
		) {
			if ($video !== false) {
				not_found('video', $videoid);
			}
			return;
		}
		if (false === ($video_info = db_single_assoc('videoinfo', "
			SELECT PSTAMP, TITLE, LOCATION, CONTENT
			FROM videoinfo
			WHERE VIDEOID = $videoid"))
		) {
			return;
		}
		if (HOME_THOMAS
		||	CURRENTUSERID === 2269
		) {
			db_insert('videodone','
			INSERT IGNORE INTO videodone SET
				USERID  = '.CURRENTUSERID.",
				STAMP   = UNIX_TIMESTAMP(),
				BASE	= 0,
				VIDEOID = $videoid"
			);
		}
	} else {
		$video = [];
	}
	assert(is_array($video)); # Satisfy EA inspection
	layout_show_section_header();
	if ($videoid
	&&	false === ($have_log = db_single('video_log', "SELECT 1 FROM video_log WHERE VIDEOID = $videoid LIMIT 1"))
	||	!($spec = explain_table('video'))
	||	!($contenttypes = get_video_content_types($spec))
	) {
		return;
	}
	?><form<?
	?> accept-charset="utf-8"<?
	?> method="post"<?
	?> action="/video/<?
	if ($videoid) {
		echo $videoid ?>/<?
	}
	/** @noinspection SpellCheckingInspection */
	if ($_REQUEST['ACTION'] === 'withouttype') {
		?>withouttype<?
	} else {
		?>commit<?
	}
	?>"<?
	?> onsubmit="return submitForm(this);"><?
	if ($ticketid = have_idnumber($_REQUEST,'TICKETID')) {
		?><input type="hidden" name="TICKETID" value="<?= $ticketid ?>" /><?
	}
	layout_open_box('gallery');
	if (!empty($video_info)) {
		/** @noinspection PhpRedundantOptionalArgumentInspection */
		extract($video_info, \EXTR_OVERWRITE);
		layout_box_header(Eelement_name('source'));
		layout_open_table();
		if ($TITLE) {
			layout_row(Eelement_name('title'), escape_utf8($TITLE));
		}
		if ($video['DURATION']) {
			layout_row(Eelement_name('duration'), get_time_duration($video['DURATION']));
		}
		if ($LOCATION) {
			layout_row(Eelement_name('location'), escape_utf8($LOCATION));
		}
		if ($CONTENT) {
			layout_row(
				Eelement_name('description'),
				'<div style="max-height: 7.2em; overflow-y: scroll; overflow-x: hidden;">'.
				make_all_html(_ubb_preprocess(clean_utf8($CONTENT), true), UBB_UTF8).
				'</div>'
			);
		}
		layout_close_table();
	}
	if ($videoid) {
		$_REQUEST['SHOW_CONNECT_FORM'] = true;
		layout_box_header(Eelement_plural_name('connection'));
		_connect_display_form('video',$videoid);
	}
	layout_box_header(
		Eelement_name('video'),
		get_remove_char([
			'title'		=> __('action:ignore'),
			'onclick'	=> /** @lang JavaScript */ "
				do_inline('POST', '/ignorevideo.act', 'ACT', function(req){
					if (req.status !== 200) {
						showerrors(req);
						return;
					}
					window.close();
				}, 'VIDEOID=$videoid');"
		])
	);
	if ($video) {
		# show small video on form
		[$page, $player] = get_page_player_and_type($video);
		if ($player) {
			?><div class="r lmrgn block"><?
			runembed($player, 560, 349);
			?></div><?
		}
	}
	layout_open_table('');
	layout_start_row();
	?><label for="type"><?= Eelement_name('type') ?></label><?
	layout_field_value();
	$type = $video ? $video['TYPE'] : ($_REQUEST['TYPE'] ?? null);
	?><select name="TYPE" id="type"><?
		?><option<?
		if (!$video || $type === 'youtube') {
			?> selected<?
		}
		?> value="youtube">youtube</option><?
		?><option<?
		if ($type === 'vimeo') {
			?> selected<?
		}
		?> value="vimeo">vimeo</option><?
		if ($type === 'facebook') {
			?><option selected value="facebook">facebook</option><?
		} elseif ($type === 'dailymotion') {
			?><option selected value="dailymotion">dailymotion</option><?
		}
	?></select><?

	layout_restart_row();
	echo Eelement_name('external_ID');
	layout_field_value();
	include_js('js/form/video');
	show_input([
		'required'	=> true,
		'type'		=> 'text',
		'class'		=> 'regular',
		'name'		=> 'EXTERNALID',
		'spec'		=> $spec,
		'pattern'	=> '^[^#]+$',
		'value'		=> $video ?: getifset($_REQUEST,'ID'),
		'onkeyup'	=> /** @lang JavaScript */ 'Pf_parseVideoURL(this)',
	]);

	layout_restart_row();
	echo Eelement_name('title');
	layout_field_value();
	show_input([
		'type'		 => 'text',
		'name'		 => 'TITLE',
		'value_utf8' => $video,
		'spec'		 => $spec,
		'onkeyup'	 => /** @lang JavaScript */ "if (!this.value.is_empty()) { this.form.STATUS = 'active'; }",
	]);

	layout_restart_row();
	echo Eelement_name('version');
	layout_field_value();
	show_input([
		'type'		  => 'text',
		'name'		  => 'VERSION',
		'value_utf8'  => $video,
		'spec'		  => $spec,
	]);

	layout_restart_row();
	echo Eelement_name('mix');
	layout_field_value();
	show_input([
		'type'		  => 'text',
		'name'		  => 'MIX',
		'value_utf8'  => $video,
		'spec'		  => $spec,
	]);

	layout_restart_row();
	?><label for="when"><?= Eelement_name('when') ?></label><?
	layout_field_value();
	show_select('WHEN', 'video', 'WHEN', 'when', $video['WHEN'] ?? null, 'when:');

	layout_restart_row();
	echo Eelement_name('part');
	layout_field_value();
	show_input([
		'class'			=> 'three_digits',
		'type'			=> 'text',
		'name'			=> 'PART',
		'placeholder'	=> 'part/total',
		'value'			=> $video,
		'spec'			=> $spec,
	]);

	layout_restart_row();
	echo Eelement_name('content');
	layout_field_value();
	foreach ($contenttypes as $ctype) {
		if (!$ctype) {
			continue;
		}
		$vtype[$ctype] = __('videotype:'.$ctype);
	}
	asort($vtype);
	if ($video
	&&	!$video['CONTENTTYPE']
	&&	!empty($video_info['TITLE'])
	) {
		$video['CONTENTTYPE'] = find_vidtype($video_info['TITLE']);
	}

	?><select<?
	?> name="CONTENTTYPE"<?
	if ($video
	&&	$video['STATUS'] === 'active'
	) {
		?> required<?
	}
	?> onchange="if (!this.value.is_empty()) { this.form.STATUS = 'active'; }"<?
	?>><?
	?><option></option><?
	foreach ($vtype as $ctype => $desc) {
		?><option<?
		if ($video
		&&	$ctype === $video['CONTENTTYPE']
		) {
			?> selected<?
		} elseif ($ctype === 'studioset') {
			?> disabled<?
		}
		?> value="<?= $ctype ?>"><?= $desc ?></option><?
	}
	?></select><?

	?> <label class="<? if (empty($video['PREVIEW'])) { ?>not-<? } ?>hilited"><?
	show_input([
		'name'		=> 'PREVIEW',
		'class'		=> 'upLite',
		'type'		=> 'checkbox',
		'value'		=> 1,
		'checked'	=> !empty($video['PREVIEW'])
	]);
	?> <?= __('videotype:preview') ?></label><?

	layout_restart_row();
	?><label for="landscape-portrait-duo"><?= Eelement_name('landscape_portrait_duo') ?></label><?
	layout_field_value();
	require_once '_fillelementid.inc';
	show_elementid_input('video', $video['LANDSCAPE_PORTRAIT_DUO'] ?? null, [
		'id'	=> 'landscape-portrait-duo',
		'name'	=> 'LANDSCAPE_PORTRAIT_DUO',
	]);

	layout_restart_row();
	echo Eelement_name('#short');
	layout_field_value();
	show_input_with_label(
		checked: $video
				 ?   $video['SHORT']
				 :   (  $prefilled =
						   !empty($video_info['TITLE'])
						&& false !== mb_stripos($video_info['TITLE'], '#short')
					 ),
		hilited: 'bold-hilited'.(!empty($prefilled) ? ' prefilled' : ''),
		base:	 'cbi',
		input:	 [	'type'	=> 'checkbox',
					'class'	=> 'upLite',
					'name'	=> 'SHORT',
					'value'	=> '1'
		]);

	layout_restart_row();
	echo Eelement_name('status');
	layout_field_value();

	?><select<?
	?> name="STATUS"<?
	?> onchange="this.form.CONTENTTYPE.required = this.value === 'active'"<?
	if (empty($have_log)
	&&	$video
	&&	$video['STATUS'] === 'inactive'
	) {
		$video['STATUS'] = 'active';
		?> class="prefilled notice"<?
	}
	?>><?
	$statuses = $spec['STATUS']->enum;
	foreach ($statuses as $status => &$status_name) {
		$status_name = __("status:$status");
	}
	unset($status_name);
	asort($statuses);
	foreach ($statuses as $status => $status_name) {
		?><option<?
		if ($video
		&&	$status === $video['STATUS']
		) {
			?> selected<?
		}
		?> value="<?= $status ?>"><?= $status_name ?></option><?
	}
	?></select><?

	// PUBLICATION
	layout_restart_row();
	echo Eelement_name('publication');
	layout_field_value();
	?><select name="PUBLICATION" onchange="setdisplay('pstamp', this.value === '2');"><?
		if ($video) {
			?><option value="0"><?= __('action:dont_change') ?>: <?
			_datedaytime_display($video['PSTAMP'] ?? null);
			?></option><?
		}
		if (!$video
		||	empty($video_info['PSTAMP'])
		||	$video['PSTAMP'] !== $video_info['PSTAMP']
		) {
			?><option value="1"><?
			echo __('action:from_feed');
			if (!empty($video_info['PSTAMP'])) {
				?>: <?
				_datedaytime_display($video_info['PSTAMP']);
			}
			?></option><?
		}
		?><option value="2"><?= __('form:select:other_option') ?>&hellip;</option><?
	?></select><?
	?><span class="hidden nowrap" id="pstamp"> <?
	_datetime_display_select_stamp($video ? $video['PSTAMP'] : CURRENTSTAMP);
	?></span><?

	layout_close_table();
	layout_close_box();
	?><div class="block"><input type="submit" value="<?= __($video ? 'action:change' : 'action:add') ?>" /></div><?
	?></form><?
}

function actual_video_commit(): int|false {
	require_once '_cachevideothumbs.inc';
	require_once '_videoinfo.inc';

	$videoid = have_idnumber($_REQUEST, 'sID');

	if (!require_admin('video')
	||	!require_post()
	||	false === require_enum($_POST, 'STATUS', 'video')
	||	false === require_number_element($_POST, 'PUBLICATION', $videoid ? [0, 1, 2] : [1, 2])
	||	false === ($contenttype = require_video_contenttype($_POST, 'CONTENTTYPE'))
	||	!require_anything_trim($_POST, 'TITLE', true, flags: CLEAR_SENSELESS)
	||	!require_anything_trim($_POST, 'VERSION', true, flags: CLEAR_SENSELESS)
	||	!require_anything_trim($_POST, 'MIX', true, flags: CLEAR_SENSELESS)
	||	false === require_enum($_POST, 'WHEN', 'video')
	||	!require_something_trim($_POST, 'EXTERNALID')
	||	false === ($type = require_element($_POST, 'TYPE', ['youtube', 'dailymotion', 'vimeo', 'facebook']))
	||	!require_anything_trim($_POST, 'PART', true)
	) {
		return false;
	}
	$externalid = $_POST['EXTERNALID'];
	if (false === ($existing_videoid = db_single('video', "
		SELECT VIDEOID
		FROM video
		WHERE TYPE = '$type'
		  AND EXTERNALID = '".addslashes($externalid)."'
		  AND VIDEOID != ".($videoid ?: 0)
	))) {
		return false;
	}
	if ($existing_videoid) {
		register_warning('item:warning:already_exists_LINE', DO_UBB, ['ELEMENT' => 'video', 'ID' => $existing_videoid]);
		$_REQUEST['sID'] = $existing_videoid;
		return false;
	}
	if (!($data = get_videoinfo_data($type, $externalid, $videoid))) {
		register_error('videoinfo:error:got_no_data_LINE');
		if (!$videoid) {
			return false;
		}
	}

	if ((!$data || !($info = call_user_func('fill_videoinfo_'.$type, $data)))
	&&	!$videoid
	) {
		return false;
	}
	$url = null;
	if (!empty($info)) {
		if ($type === 'vimeo'
		||	$type === 'dailymotion'
		) {
			$url = $info['THUMB_URL'];
		}
		foreach (['WIDTH', 'HEIGHT', 'ASPECT', 'DURATION'] as $field) {
			if (!empty($info[$field])) {
				$setlist[$field] = $info[$field];
			}
		}
		$pstamp = $info['PSTAMP'];
	}

	$setlist['STATUS']		= ($status = $_POST['STATUS']);
	$setlist['TITLE']		= $_POST['TITLE'];
	$setlist['MIX']			= $_POST['MIX'];
	$setlist['VERSION']		= $_POST['VERSION'];
	$setlist['WHEN']	 	= $_POST['WHEN'];
	$setlist['EXTERNALID']	= ($externalid = $_POST['EXTERNALID']);
	$setlist['TYPE']		= $type;
	$setlist['PREVIEW']		= isset($_POST['PREVIEW']);
	$setlist['CONTENTTYPE']	= $contenttype;
	$setlist['PART']		= $_POST['PART'];
	$setlist['SHORT']		= isset($_POST['SHORT']);

	switch ($_POST['PUBLICATION']) {
	case 1:	$setlist['PSTAMP'] = $pstamp;
		break;
	case 2:	if (!require_date_and_time($_POST)) {
			return false;
		}
		$setlist['PSTAMP'] = _date_getstamp($_POST);
		break;
	}

	[$instr, $valstr, $bincmp] = inserts_values_and_bincmp($setlist);

	if ($videoid) {
		if (!db_insert('video_log','
			INSERT INTO video_log
			SELECT * FROM video
			WHERE NOT '.$bincmp.'
			  AND VIDEOID = '.$videoid)
		) {
			return false;
		}
		if (!db_affected()) {
			register_notice('item:notice:no_change_needed_LINE');
		} else {
			if (!db_update('video', '
				UPDATE video SET
					MUSERID	='.CURRENTUSERID.',
					MSTAMP	='.CURRENTSTAMP.',
					'.$instr.'
				WHERE VIDEOID='.$videoid)
			) {
				return false;
			}
			register_notice($_REQUEST['sELEMENT'].':notice:changed_LINE');
		}

		require_once '_videometa.inc';
		videometa::flush_titles($videoid);

	} else {
		if (!db_insert('video','
			INSERT INTO video SET
				USERID	='.CURRENTUSERID.',
				CSTAMP	='.CURRENTSTAMP.',
				'.$instr,
			DB_DONT_WARN_DUP_ENTRY)
		) {
			if (is_duplicate()) {
				$_REQUEST['sID'] = db_single('video','SELECT VIDEOID FROM video WHERE EXTERNALID="'.addslashes($externalid).'"');
				register_warning('video:warning:already_exists_LINE');
			}
			return false;
		}
		register_notice('video:notice:added_LINE');
		$videoid = db_insert_id();
		$_REQUEST['sID'] = $videoid;
	}
	if (($ticketid = have_idnumber($_POST, 'TICKETID'))
	&&	(require_once '_contactrequire.inc')
	&&	have_ticket_lock($ticketid)
	) {
			db_insert('contact_ticket_log', "
			INSERT INTO contact_ticket_log
			SELECT * FROM contact_ticket
			WHERE TICKETID = $ticketid")
		&&	db_update('contact_ticket', '
			UPDATE contact_ticket SET
				MUSERID	= '.CURRENTUSERID.',
				MSTAMP	= '.CURRENTSTAMP.",
				ELEMENT	= 'video',
				ID		= $videoid
			WHERE TICKETID = $ticketid");
	}
	if ($status === 'active') {
		cache_video_thumbs($videoid, $type, $externalid, $url);
	}
	require_once '_videoinfo.inc';
	fill_videoinfo($type, $videoid, $externalid, data: $data);
	page_changed('video',  $videoid);
	return $videoid;
}

function get_page_player_and_type(array $video): array {
	return match ($video['CHANNEL_TYPE'] ?: $video['TYPE']) {
		'vimeo' => [
			'https://vimeo.com/'.urlencode($video['NAME']),
			empty($video['EXTERNALID']) ? null : 'https://player.vimeo.com/video/'.$video['EXTERNALID'],
		],
		'youtube' => [
			match ($video['YOUTUBE_TYPE']) {
				'channel'	=> 'https://www.youtube.com/channel/'.$video['ID'],
				'custom'	=> 'https://www.youtube.com/c/'.$video['NAME'],
				'user'		=> 'https://www.youtube.com/user/'.$video['NAME'],
				default		=> null,
			},
			empty($video['EXTERNALID']) ? null : 'https://www.youtube.com/embed/'.$video['EXTERNALID']
		],
		'dailymotion' => [
			'https://www.dailymotion.com/'.urlencode($video['NAME']),
			empty($video['EXTERNALID']) ? null : 'https://www.dailymotion.com/embed/video/'.$video['EXTERNALID']
		],
		default => [null, null],
	};
}

function find_vidtype(string $title, bool $all = false): array|string {
	static $__spec;
	$__spec ??= explain_table('video');

	$ctypes = [];
	$titlecmp = preg_replace('"\W+"u', '', $title);

	foreach ($__spec['CONTENTTYPE']->enum as $content_type) {
		if (!$content_type
		||	 $content_type === 'preview'
		) {
			continue;
		}
		$typename = __('videotype:'.$content_type);

		if (false !== mb_stripos($titlecmp, $content_type)
		||	false !== mb_stripos($titlecmp, $typename)
		) {
			if (!$all) {
				return $content_type;
			}
			$ctypes[$content_type] = __('videotype:'.$content_type);
		}
		switch ($content_type) {
		case 'aftermovie':
			if (false !== mb_stripos($titlecmp, 'recap')
			||	false !== mb_stripos($titlecmp, 'this was')
			||	preg_match('"after\s*movie|𝗔𝗳𝘁𝗲𝗿\s*𝗺𝗼𝘃𝗶𝗲"ui', $titlecmp)
			) {
				if (!$all) {
					return $content_type;
				}
				$ctypes[$content_type] = __('videotype:'.$content_type);
			}
			break;

		case 'endshow':
			if (preg_match('"ei?nd\s*show"ui', $titlecmp)) {
				if (!$all) {
					return $content_type;
				}
				$ctypes[$content_type] = __('videotype:'.$content_type);
			}
			break;

		case 'musicvideo':
			if (false !== mb_stripos($titlecmp, 'clip')
			||	false !== mb_stripos($titlecmp, 'prodby')
			||	false !== mb_stripos($title,	'prod.')
			||	false !== mb_stripos($titlecmp, 'officialvideo')
			||	false !== mb_stripos($titlecmp, 'officialhardstylevideo')
			) {
				if (!$all) {
					return $content_type;
				}
				$ctypes[$content_type] = __('videotype:'.$content_type);
			}
			break;
		}
	}
	if (!isset($ctypes['musiconly'])
	&&	!isset($ctypes['musicvideo'])
	&&	preg_match('"\([^)]*?(?:remix|radio\s*edit|\s+mix|available.*)\)"iu', $title)
	) {
		$ctypes['musiconly'] = __('videotype:musiconly');
	}
	if ($all) {
		return $ctypes;
	}
	return false;
}
