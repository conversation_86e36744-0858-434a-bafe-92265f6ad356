<?php

require_once '_genres.inc';

function _genrelist_get_item_styles(
	 string  $element,
	 int	 $id,
	?int	&$cnt		 = null,
	?array	 $sort_by	 = null,
	 bool	 $no_masters = false,
): ?string {
	$key = null;
	switch ($element) {
	case 'user':
		$genres = memcached_rowuse_array(['genre_fan','genre','usergenre'],'
			(	SELECT NAME, GID
				FROM genre_fan
				JOIN genre USING (GID)
				WHERE USERID = '.$id.'
			) UNION (
				SELECT NAME, 0
				FROM usergenre
				WHERE USERID = '.$id.'
			)
			ORDER BY NAME ASC',
			0,
			$key,
			DB_NON_ASSOC
		);
		break;
	default:
		$genres = memcached_rowuse_array([$element.'_genre', 'genre'],'
			SELECT NAME, GID
			FROM '.$element.'_genre
			JOIN genre USING (GID)
			WHERE '.$element.'ID = '.$id.'
			ORDER BY NAME ASC',
			key: $key,
			flags: DB_NON_ASSOC
		);
		break;
	}
	if (!$genres) {
		$cnt = 0;
		return null;
	}
	if ($sort_by) {
		usort($genres, function(array $a, array $b) use ($sort_by): int {
			$aval =	$sort_by[$b[1]] ?? 0;
			$bval =	$sort_by[$a[1]] ?? 0;
			return $aval - $bval;
		});
	}

	$cnt = count($genres);
	$result = [];
	$gids = [];
	foreach ($genres as $info) {
		[$name, $gid] = $info;
		if ($link = $gid) {
			static $__avail = 0;
			if ($__avail === 0) {
				$__avail = get_available_genres('party');
			}
			if (empty($__avail[$gid])) {
				$link = false;
			}
		}
		$gids[] = $gid;
		$result[] = (
			$link
		?	'<a class="link" href="/agenda/genre/'.genre_for_url($name).'">'.escape_utf8($name).'</a>'
		:	escape_utf8($name)
		).(	have_admin()
		&&	isset($sort_by[$gid])
		?	' <small class="light">'.MULTIPLICATION_SIGN_ENTITY.' '.$sort_by[$gid].'</small>'
		:	''
		);
	}

	$str = implode(', ', $result);

	if (!$no_masters
	&&	$gids
	&&	have_admin($element)
	) {
		$master = memcached_single('master_genre','
			SELECT GROUP_CONCAT(DISTINCT NAME ORDER BY NAME SEPARATOR ", ")
			FROM master_genre
			JOIN genre_to_master USING (MGID)
			WHERE GID IN ('.implode(', ', $gids).')'
		);
		if ($master) {
			$str .= '<br /><span class="mastergenre">'.$master.'</span>';
		}
	}

	return $str;
}

function get_genre_link($gid): string {
	$genres = get_genres();
	if (!$genres) {
		return '';
	}
	$name = $genres[$gid];
	return '<a class="link" href="/agenda/genre/'.genre_for_url($name).'">'.escape_utf8($name).'</a>';
}

function show_genre_selection(
	?array $have = null,
	bool $uncollapsed = false,
	bool $disabled = false,
	?array $make_bold = null,
) {
	if (is_string($have)) {
		mail_log('show_genre_selection have argument is a string: '.$have, include_trace: true);
	}

	$genres = get_genres();
	if (!$genres) {
		return;
	}
	$rnd = array_rand($genres, 3);

	include_js('js/form/genres');

	$haves = [];
	if ($have) {
		if (isset($have[0])) {
			foreach ($have as $gid) {
				if (isset($genres[$gid])) {
					$haves[$gid] = $genres[$gid];
				}
			}
		} else {
			foreach ($have as $gid => $true) {
				if (isset($genres[$gid])) {
					$haves[$gid] = $genres[$gid];
				}
			}
		}
		asort($haves);
	}
	ob_start();
	?><input type="hidden" name="GENRESID" disabled value="<?= $uniq = uniqid() ?>"><?
	?><input<?
	?> name="GENRES"<?
	?> data-ampersands="<?

	$ampersands = db_simpler_array('genre','
		SELECT NAME
		FROM genre
		WHERE NAME LIKE "%&%"'
	);

	if ($ampersands) {
		$ampersands[] = 'd&b'; # drum & bass
		foreach ($ampersands as &$ampersand) {
			$ampersand = preg_replace('"\s*&\s*"', '\s*\&\s*', $ampersand);
			$ampersand = str_replace(' ', '\s*', $ampersand);
		}
		unset($ampersand);
		echo escape_utf8('\b('.implode('|', $ampersands).')\b');
	}
	?>"<?
	?> data-convs="<?
	$genre_convs = get_genre_recognitions();
	foreach ($genre_convs as $src => &$dst) {
		$dst = preg_replace('"[\W_]+"', '', $dst);
	}
	unset($dst);
	echo escape_specials(json_encode($genre_convs));

	?>"<?
	?> onchange="Pf.changeGenres(this,event)"<?
	?> onpaste="Pf.checkGenresClip(event,this)"<?
	?> onkeyup="Pf.changeGenresDelayed(this)"<?
	?> onblur="Pf.addSeparator(this,', ')"<?
	?> type="text"<?
	?> autocomplete="off"<?
	?> class="genrein"<?
	?> id="genres_<?= $uniq ?>"<?
	?> placeholder="<?= __('field:for_example') ?>: <?= escape_utf8($genres[$rnd[0]]),', ',escape_utf8($genres[$rnd[1]]).', ',escape_utf8($genres[$rnd[2]]),', &hellip;' ?>"<?
	if ($have) {
		?> value="<?= escape_utf8(implode(', ', $haves)) ?>, "<?
	}
	?>><?

	$input = ob_get_clean();
	ob_start();
	?><input name="GENREALL" type="button" onclick="Pf.clickGenreAll(this)"<?
	if (!$uncollapsed) {
		?> data-chosen="data-chosen"<?
	}
	?> <?= $uncollapsed ? 'data-other' : 'value' ?>="<?= /*__('action:show_all_genres')*/ '&#9660;' ?>"<?
	?> <?= $uncollapsed ? 'value' : 'data-other' ?>="<?= /*__('action:show_chosen_genres')*/ '&#9650;' ?>"><?
	$button = ob_get_clean();


	?><table class="nomargin dens fw"><tr><td><?
	echo $button;
	?></td><td class="fw"><?
	echo $input;
	?></td></tr></table><?

	?><div class="cb-hilite"><?
	foreach ($genres as $gid => $name) {
		$checked = isset($haves[$gid]);
		ob_start();
		?><li<?
		if (!$checked && !$uncollapsed) {
			?> class="hidden"<?
		}
		?>><?
		?><label class="<?
		if ($make_bold
		&&	isset($make_bold[$gid])
		) {
			?>bold <?
		}
		if (!$checked) {
			?>not-<?
		}
		?>bold-hilited"><?
		?><input<?
		if ($checked) {
			?> checked<?
		}
		if ($disabled) {
			?> disabled<?
		}
		?> onclick="Pf.clickGenre(this)"<?
		?> class="upLite"<?
		?> type="checkbox"<?
		?> name="GID[]"<?
		?> value="<?= $gid ?>"<?
		?> data-name="<?= $name ?>"<?
		?>><?
		?> <?= escape_utf8($name)
		?></label><?
		?></li><?
		$lines[] = ob_get_clean();
	}
	?></div><?
	?><ul class="nostyle genrecbs"><?
	echo implode('', $lines);
	?></ul><?
}

function _genrelist_display_checkboxes(array $gidhash = [], bool $showclear = false): void {
	$genres = get_genres();
	if (!$genres) {
		return;
	}
	foreach ($genres as $gid => $name) {
		ob_start();
		?><label class="<?
		if (!($checked = isset($gidhash[$gid]))) {
			?>not-<?
		}
		?>bold-hilited"><input<?
		if ($checked) {
			?> checked="checked"<?
		}
		?> class="upLite" type="checkbox" name="GID[]" value="<?= $gid;
		?>"> <?= escape_utf8($name);
		?></label><br /><?
		$lines[] = ob_get_clean();
	}
	if ($showclear) {
		ob_start();
		?><input type="button" onclick="(function(form){<?
			?>var gids=form['GID[]'];<?
			?>for(var i=0;i<gids.length;++i){<?
				?>gids[i].checked=false;<?
				?>gids[i].onclick(gids[i])<?
			?>}<?
		?>})(this.form)" value="<?= __('action:clear') ?>"><?
		$lines[] = ob_get_clean();
	}
	?><div class="cb-hilite"><?
	show_columns($lines, 4);
	?></div><?
}

function show_user_genre_options(int $userid): bool {
	if ($userid) {
		$selected = db_boolean_hash('genre_fan','SELECT GID,GID FROM genre_fan WHERE USERID='.$userid,DB_USE_MASTER);
		if ($selected === false) {
			return false;
		}
		$usergenres = db_simpler_array('usergenre','SELECT NAME FROM usergenre WHERE USERID='.$userid,DB_USE_MASTER);
		if ($usergenres === false) {
			return false;
		}
	}

	show_genre_selection(isset($selected) ? $selected : null,false);

	require_once '_bubble.inc';
	?><div class="block cb-hilite" style="margin-top:.5em"><?
/*	?><label for="moreopts"><input onclick="if(this.checked){unhide('moreoptions');this.disabled=true}" type="checkbox" value="1" name="MOREOPTS" id="moreopts"> meer opties</label><br /><?*/
	?><label for="useropts" class="<?
	if (empty($usergenres)) {
		?>not-<?
	}
	?>bold-hilited"><?
	?><input<?
	if (!empty($usergenres)) {
		?> checked<?
	}
	?> onclick="setdisplay('usergenres',this.checked)" type="checkbox" value="1" name="USEROPTS" id="useropts" class="upLite"> <?
	$bubble = new bubble(BUBBLE_BLOCK | BUBBLE_30_WIDE | HELP_BUBBLE | CONTENT_AT_BOTTOM);
	$bubble->catcher(__('genrelist:propositions'));
	$bubble->content(__('genrelist:proposition_information_TEXT'));
	$bubble->display();
	?></label><?
	?><span id="usergenres"<?
	if (empty($usergenres)) {
		?> class="hidden"<?
	} else {
		sort($usergenres);
	}
	?>>: <input type="text" name="USERGENRES" value="<? if (!empty($usergenres)) echo escape_utf8(implode(', ',$usergenres)); ?>"><?
	?></span></div><?
	return true;
}

function user_favourite_genres(int $userid): array|null|false {
	return db_same_hash('genre_fan','
		SELECT GID
		FROM genre_fan
		WHERE USERID = '.$userid
	);
}

function _genrelist_commit(?string $element = null, ?int $id = null): bool {
	$element ??= $_REQUEST['sELEMENT'];
	$id ??= $_REQUEST['sID'];
	$idname = strtoupper($element).'ID';
	$changes = 0;
	if ($element == 'user') {
		$table = 'genre_fan';
		if (!empty($_POST['USERGENRES'])) {
			$havegenres = db_boolean_hash('usergenre','
				SELECT LOWER(NAME)
				FROM usergenre
				WHERE USERID='.$id,
				DB_FORCE_MASTER
			);
			if ($havegenres === false) {
				return false;
			}
			$defgenres = memcached_simple_hash(['genre', 'genre_recognize'],'
/*				SELECT LOWER(CONVERT(REPLACE(REPLACE(NAME," ",""),"-","") USING latin1)), GID*/
				SELECT LOWER(REPLACE(REPLACE(NAME,"  "," "), "-", "")), GID
				FROM genre
				UNION
				SELECT LOWER(NAME), GID
				FROM genre_recognize'
			);
			if ($defgenres === false) {
				return false;
			}
			foreach (explode(',', $_POST['USERGENRES']) as $genre) {
				$lgenre = mb_strtolower(preg_replace('"[^\w]+"', '', $genre));
				if (!$lgenre) {
					continue;
				}
				if (isset($defgenres[$lgenre])) {
					$_POST['GID'][] = $defgenres[$lgenre];
					continue;
				}
				$genre = trim($genre);
				$keepgenres[$genre] = true;
				if (!isset($havegenres[$genre])) {
					$inslist[$genre] = '('.$id.', "'.addslashes($genre).'", '.CURRENTSTAMP.')';
				}
			}
		}
		$where_for_usergenre = '
			WHERE USERID='.$id.
			(empty($keepgenres) ? null : ' AND NAME NOT IN (BINARY '.stringsimplodekeys(',', $keepgenres).')');

		if (!db_insert('usergenre_log','
			INSERT INTO usergenre_log
			SELECT *, '.CURRENTSTAMP.'
			FROM usergenre '.
			$where_for_usergenre)
		||	!db_delete('usergenre','
			DELETE FROM usergenre '.
			$where_for_usergenre)
		) {
			return false;
		}
		$changes += db_affected();
		if (isset($inslist)) {
			if (!db_insert('usergenre', '
				INSERT IGNORE INTO usergenre (USERID, NAME, CSTAMP)
				VALUES '.implode(', ', $inslist)
			)) {
				return false;
			}
			$changes += db_affected();
		}

	} else {
		$table = $element.'_genre';
	}
	if (!isset($_POST['GID'])) {
		if (!db_insert($table.'_log','
			INSERT INTO '.$table.'_log
			SELECT *, '.CURRENTSTAMP.'
			FROM '.$table.'
			WHERE '.$idname.' = '.$id)
		||	!db_delete($table,'
			DELETE FROM '.$table.'
			WHERE '.$idname.' = '.$id)
		) {
			return false;
		}
		$changes += db_affected();
		if ($changes) {
			require_once '_profile.inc';
			clear_future_event_genres($element, $id);
			clear_element_profile($element, $id);
		}
		return $changes;
	}
	if (!require_number_array($_POST, 'GID')) {
		return false;
	}
	foreach ($_POST['GID'] as $gid) {
		$vals[$gid] = '('.$id.', '.$gid.', '.CURRENTSTAMP.')';
	}
	$gidstr = isset($vals) ? implodekeys(',',$vals) : '';
	if (!db_insert($table.'_log','
		INSERT INTO '.$table.'_log
		SELECT *,'.CURRENTSTAMP.'
		FROM '.$table.'
		WHERE '.$idname.' = '.$id.
		($gidstr ? ' AND GID NOT IN ('.$gidstr.')' : ''))
	) {
		return false;
	}
	if (!isset($vals)) {
		return $changes;
	}
	if (!db_delete($table,'
		DELETE FROM '.$table.'
		WHERE GID NOT IN ('.$gidstr.')
		  AND '.$idname.'='.$id)
	) {
		return false;
	}
	$changes += db_affected();

	if (!db_insert($table,'
		INSERT IGNORE INTO '.$table.' ('.$idname.', GID, CSTAMP)
		VALUES '.implode(', ', $vals))
	) {
		return false;
	}
	$changes += db_affected();

	if ($changes) {
		require_once '_profile.inc';
		clear_future_event_genres($element, $id);
		clear_element_profile($element, $id);
	}

	return $changes;
}

function clear_future_event_genres(string $element, int $id): void {
	# allow redetermination of event genre if artist or org changes (only future)
	$future_events = [];
	switch ($element) {
	case 'artist':
		$future_events = db_simpler_array(['party','lineup'],'
			SELECT DISTINCT PARTYID
			FROM party
			JOIN lineup USING (PARTYID)
			WHERE ARTISTID='.$id.'
			  AND STAMP>'.CURRENTSTAMP
		);
		break;

	case 'organization':
		$future_events = db_simpler_array(['party','lineup'],'
			SELECT DISTINCT PARTYID
			FROM party
			JOIN connect ON MAINTYPE="party" AND MAINID=PARTYID AND ASSOCTYPE IN ("organization","orgashost")
			WHERE ASSOCID='.$id.'
			  AND STAMP>'.CURRENTSTAMP.'
			UNION
			SELECT DISTINCT PARTYID
			FROM partyarea
			JOIN party USING (PARTYID)
			WHERE HOSTEDBYID='.$id.'
			  AND STAMP>'.CURRENTSTAMP
		);
		break;
	}
	if ($future_events) {
		require_once '_genres.inc';
		foreach ($future_events as $partyid) {
			clear_determined_genres($partyid);
		}
	}
}

function genrelist_display_options(
	string|int $selected = 0,
	?array $genres = null,
	bool $gid_values = false,
): void {
	$all_genres = $genres ?: get_genres();

	$genres = [];
	if (have_user()
	&&	($favourite_genres = user_favourite_genres(CURRENTUSERID))
	) {
		foreach ($all_genres as $gid => $name) {
			$genres[isset($favourite_genres[$gid]) ? 1 : 0][$gid] = $name;
		}
	} else {
		$genres[0] = $all_genres;
	}
	$group_count = count($genres);
	foreach ([1, 0] as $favourite) {
		if (!isset($genres[$favourite])) {
			continue;
		}
		$list = $genres[$favourite];
		if ($group_count > 1) {
			?><optgroup label="<?= element_plural_name($favourite ? 'favourite_genre' : 'other_genre') ?>"><?
		}
		foreach ($list as $gid => $name) {
			if (is_array($name)) {
				[$cnt, $name] = keyval($name);
			} else {
				$cnt = 0;
			}
			?><option<?
			if ($selected === $gid) {
				?> selected<?
			}
			?> value="<?= $gid_values ? $gid : genre_for_url($name) ?>"><?
			echo escape_utf8($name);
			if ($cnt) {
				?> <small>&middot; <? echo $cnt; ?></small><?
			}
			?></option><?
		}
		if ($group_count > 1) {
			?></optgroup><?
		}
	}
}
function show_genre_select(
	?string $element = null,
	int|string $selected = 0,
	?array $genres = null,
	bool $include_favourites = false,
	bool|string $include_empty = false,
	bool $include_all = false,
): void {
	if (!$genres) {
		$genres = get_available_genres($element);
		if ($genres === false) {
			return;
		}
	}
	if ($element === 'agenda'
	&&	$genres
	&&	$include_favourites
	) {
		$favourite_count = 0;
		[$gid, $info] = keyval($genres);
		reset($genres);
		if (is_array($info)) {
			$favourite_genres = user_favourite_genres(CURRENTUSERID);
			foreach (array_intersect_key($genres, $favourite_genres) as $gid => $info) {
				[$cnt, $name] = keyval($info);
				$favourite_count += $cnt;
			}
		}
	}
	if ($include_favourites) {
		if (!have_user()
		||	!user_favourite_genres(CURRENTUSERID)
		) {
			$include_favourites = false;
		} elseif ($include_favourites === true) {
			$include_favourites = 'favourites';
		}
	}
	$action = $_REQUEST['ACTION'];
	$top_part = $action === 'top' ? '/top' : '';
	?><select<?
	?> name="GENRE"<?
	if ($include_empty) {
		?> required<?
	}
	?> onchange="<?
	?>if(this.value === ''){<?
		?>location.href = '/<?= $element, $top_part; ?>/genres';<?
	?>} else {<?
		?>location.href = '/<?= $element, $top_part; ?>/genre/' + this.value<?
	?>}<?
	?>"><?
	if ($include_empty) {
		?><option<?
		if (!$selected) {
			?> selected<?
		}
		?> value=""<?
		?> disabled><?= $include_empty === true ? '' : $include_empty ?></option><?
	}
	if ($include_all) {
		?><option<?
		?> selected<?
		?> value=""><?= element_name('all') ?></option><?
	}
	if ($include_favourites) {
		?><option<?
		if ($selected === $include_favourites) {
			?> selected<?
		}
		?> value="<?= $include_favourites ?>"><?
		echo element_plural_name('all_favourite_genre');
		if (!empty($favourite_count)) {
			?> <?= MIDDLE_DOT_ENTITY ?> <? echo $favourite_count;
		}
		?></option><?
	}

	genrelist_display_options($selected, $genres);

	?></select><?
}

function show_genres_block(array $item): void {
	$element = $_REQUEST['sELEMENT'];
	$id	  = $_REQUEST['sID'];

	if (!($styles = _genrelist_get_item_styles($element, $id))) {
		return;
	}

	?><div class="block"><?
	?><b><?= Eelement_plural_name('genre') ?></b><?
	if (have_admin($element)
	&&	!empty($itemn['COPY_GENRES'])
	) {
		?> (<?= __('genres:info:copy_to_new_event') ?>)<?
	}
	?><br /><?
	echo $styles;
	?></div><?
}
