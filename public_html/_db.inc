<?php

/* @noinspection ForgottenDebugOutputInspection, LongLine */

declare(strict_types=1);

require_once '_globalsettings.inc';
require_once 'defines/cache.inc';
require_once 'defines/db.inc';
require_once '_memcache.inc';
require_once '_duplicate_children.inc';
require_once '_error.inc';
require_once '_helper.inc';
require_once '_querylog.inc';
require_once '_tooslow.inc';
require_once '_log.inc';
require_once '_servertype.inc';

# NOTE: About using addslashes. We should be using real_escape_string of course, but that
#		isn't so easy because of the legacy code using both latin1 and utf-8 strings.
#		If we were to use real_escape_string, be sure to change the charset using
#		mysqli->set_charset(). This would allow for both latin1 and utf-8 but with some
#		overhead if we need to change between character sets often.
#
#		UTF-8 is not susceptible to single quote injections:
#		https://shiflett.org/blog/2006/addslashes-versus-mysql-real-escape-string


function db_display_top_warning(): void {
	if (HOME_OFFICE
	&&	(	NO_ALTER_ATTACHMENT
		||	NO_ALTER_ALBUMELEMENT
		||	NO_ALTER_UPLOADIMAGE
		||	NO_ALTER_USERIMAGE
		||	NO_ALTER_DUMP
		)
	) {
		?><div class="top-message top-warning"><?
		?>There are still tables that may not be altered. Make sure this is still required!<?
		?></div><?
	}
}

if (!db_init()) {
	log_sporadically('db_init() failed @ '.$_SERVER['REQUEST_URI']);
	require_once '_helper.inc';
	page_problem(503, desc: 'database initialization problem');
}

function bit(bool $value): string {
	/** @noinspection ReturnTernaryReplacementInspection */
	return $value ? "b'1'" : "b'0'";
}

function db_quote_wild(string $str): string {
	return strtr($str, [
		'_'		=> '\_',
		'%'		=> '\%',
		'\\'	=> '\\\\',
	]);
}

/*function db_space_to_wild(string $input): string {
	return strstr($input, ' ', '%');
}*/

/*function db_space_to_wild_to_quote(string $input): string {
	return db_space_to_wild(db_quote_wild($input));
}*/

function db_clean_query(string $qstr): ?string {
	return preg_replace(
		[
		'!"[^"]+"!',
		"!'[^']+'!",
		'!\d+!',
		'!,+!',
		'!(\s*SQL_NO_CACHE\s*)!',
		'!\s{2,}!'
	], [
		'',
		'',
		'',
		',',
		' ',
		' ',
	],	$qstr);
}

function db_complete_servers(): void {
	global $hostname, $_db_servers;
	$hostname ??= gethostname();
	foreach ($_db_servers as $server_name => &$server) {
		$server['PASSWD'] = 'FR0Pfrop';
		if (SERVER_SANDBOX) {
			switch ($server_name) {
			case 'dbsphinx':
			case 'dbsphinxrep':
				$server['IP'] = '127.0.0.1';
				break;
			default:
				$server['SOCKET'] = '/db/mysql.sock';
				break;
			}
		} elseif (
			$server['HOST'] === $hostname
		&&	!str_starts_with($server_name, 'dbsphinx')
		) {
			# $server['IP'] = '127.0.0.1';
			$server['SOCKET'] = '/db/mysql.sock';
		} else {
			$server['IP'] = gethostbyname($server['HOST']);
		}
		unset($server['HOST']);
	}
}

function db_init_shm(int $mtime): string|false {
	global $_db_tables, $_db_servers, $_db_default_spec;
	if (!($lines = file(DATABASE_SPEC))) {
		return false;
	}
	$line_num = 0;
	foreach ($lines as $line) {
		++$line_num;
		$line = trim($line);
		if (empty($line)
		||	$line[0] === '#'
		) {
			continue;
		}
		$parts = explode(' ', $line);
		if (empty($parts[1])) {
			mail_log("malformed line in database spec at line $line_num");
			continue;
		}
		[$type, $info] = explode(' ', $line);
		$new_element = null;
		foreach (explode(';', $info) as $pair) {
			$pair = trim($pair, "\t :");
			$sub_info = explode('=', $pair, 2);
			switch (count($sub_info)) {
			case 2:
				[$key, $val] = $sub_info;
				$key = rtrim($key);
				$val = ltrim($val);
				if ($key === 'SLAVES'
				||	$key === 'SPARES'
				) {
					if (!SERVER_SANDBOX) {
						foreach (explode(',', $val) as $slave) {
							$slave = trim($slave);
							if (preg_match('"^(\d+)\s*\*\s*(\w+)$"', $slave, $match)) {
								for ($i = 0; $i < $match[1]; ++$i) {
									$new_element[$key][] = $match[2];
								}
							} else {
								$new_element[$key][] = $slave;
							}
						}
					}
				} elseif ($key === 'PORT') {
					$new_element[$key] = (int)$val;
				} else {
					$new_element[$key] = $val;
				}
				break;

			case 1:
				# No '=' in string, so single string, this is regarded as boolean true
				$new_element[$pair] = true;
				break;

			default:
				mail_log("bad pair '$pair' in db.txt on line $line_num");
			}
		}
		switch ($type) { // NOSONAR
		case 'SERVER':
			if (!isset($new_element['STATUS'])) {
				mail_log('SERVER configuration line has no STATUS, presume offline', $new_element);

			} elseif ($new_element['STATUS'] === 'online') {
				$server_name = $new_element['NAME'];
				unset($new_element['STATUS'], $new_element['NAME']);
				$_db_servers[$server_name] = $new_element;
			}
			break;

		case 'TABLE':
			if (!isset($new_element['MASTER'])) {
				mail_log("TABLE spec has no MASTER field on line $line_num: $line");
				break;
			}
			/** @noinspection NestedAssignmentsUsageInspection */
			$master = $info = $new_element['MASTER'];
			$name   = $new_element['NAME'] ?? null;
			if (isset($new_element['SLAVES'])) {
				$ok_replicas = [];
				$replica_count = 0;
				foreach ($new_element['SLAVES'] as $slave) {
					if (!isset($_db_servers[$slave])) {
						continue;
					}
					$ok_replicas[] = $slave;
					++$replica_count;
				}
				if ($replica_count) {
					$info = [	$master,
								$replica_count === 1 ? $ok_replicas[0] : $ok_replicas,
								$replica_count];
				}
			}
			if (!$name) {
				if (isset($_db_default_spec[$master])) {
					mail_log("TABLE spec with default for $master supplied more than once, second on line $line_num will be ignored: $line");
				} else {
					$_db_default_spec[$master] = $info;
				}
			} elseif (isset($_db_tables[$name])) {
				mail_log("table specification for $name supplied more than once!");
			} else {
				$_db_tables[$name] = $info;
			}
			break;
		}
	}
	if (!$_db_servers) {
		return bad_database_configuration('No SERVERs specified in database specifcation file');
	}
	if (!$_db_tables) {
		return bad_database_configuration('No TABLEs specified in database specification file');
	}
	if (!$_db_default_spec) {
		return bad_database_configuration('No DEFAULT specified in database specification file');
	}
	db_complete_servers();

	$store = igbinary_serialize([
		$_db_servers,
		$_db_tables,
		$_db_default_spec,
	]);

	if (CLI) {
		if (!file_put_contents('/tmp/.__database_spec_'.posix_getuid().'.bin', $store)) {
			return false;
		}
	} elseif ($mtime) {
		// now actually store
		if (!apcu_store(CACHE_DB_DATA, $store)) {
			error_log('ERROR '.CACHE_DB_DATA.': failed to store');
			return false;
		}
		apcu_store(CACHE_DB_LAST_MOD, $mtime);
	}
	return $store;
}

function bad_database_configuration(string $message): false {
	mail_log($message, error_log: 'ERROR');
	return false;
}

function db_init(): bool {
	global $_db_servers, $_db_tables, $_db_default_spec;
	$_db_servers		=[];
	$_db_tables			= [];
	$_db_default_spec	= [];

	if (defined('DB_TIMING')) {
		register_shutdown_function('DB_TIMING_db_exit');
	}
	$modify_time = filemtime(DATABASE_SPEC);
	$ok = null;
	if (CLI) {
		$bin_file_name = '/tmp/.__database_spec_'.posix_getuid().'.bin';
		$bin_time = (int)(is_file($bin_file_name) ? filemtime($bin_file_name) : 0);
		$modify_time = (int)$modify_time;
		if (!(	$spec =
					$bin_time >= $modify_time
				?	file_get_contents($bin_file_name)
				:	db_init_shm(0)
		)) {
			return false;
		}
	}
	if (empty($bin_file_name)
	&&	false !== $modify_time
	&&	(  !($last_modified = isset($_REQUEST['NOMEMCACHE']) ? 0 : apcu_fetch(CACHE_DB_LAST_MOD))
		||	 $last_modified < $modify_time
		)
	) {
		if (!($sem = sem_get(SEMAPHORE_DB))
		||	!sem_acquire($sem)
		) {
			$ok = false;
		} else {
			if (!($last_modified = isset($_REQUEST['NOMEMCACHE']) ? 0 : apcu_fetch(CACHE_DB_LAST_MOD))
			||	  $last_modified < $modify_time
			) {
				$ok = db_init_shm($modify_time);
			}
			sem_release($sem);
		}
	}
	if ($ok === null) {
		if (empty($spec)) {
			$spec = apcu_fetch(CACHE_DB_DATA);
		}
		if ($ok = (bool)$spec) {
			/** @noinspection OnlyWritesOnParameterInspection */
			[	$_db_servers,
				$_db_tables,
				$_db_default_spec
			] = igbinary_unserialize($spec);
		}
	}
	return $ok || $ok === null;

/* 	 	if (DB_PERSISTENT_CONNECTIONS) {
		# doing manual change_user, because somehow mysqli does not, though it says it does:
		# https://www.php.net/manual/en/mysqli.persistconns.php
		# oh, sphinx does not support change_user of course, so we skip them

		register_shutdown_function(function() {
			# make sure we run last:
			register_shutdown_function(function() {
				global $_db_servers;
				foreach ($_db_servers as $server_name => $server) {
					if (empty($GLOBALS[$server_name])
					||	str_starts_with($server_name, 'dbsphinx')
					) {
						continue;
					}
					try {
						$handle = $GLOBALS[$server_name];
						if (!mysqli_change_user($handle, 'party', $server['PASSWD'], null)) {
							mail_log('failed to change user to clean up');
						}
						mysqli_close($handle);
						$handle = null;

					} catch (Exception $exception) {
						error_log('mysqli_change_user failed ('.$exception->getCode().') on '.$server_name.': '.$exception->getMessage());
					}
				}
			});
		});
	}*/
}

function DB_TIMING_db_exit(): void {
	global $_db_servers;
	if (empty($_db_servers)) {
		return;
	}
	global $__stat_list;
	if (!empty($__stat_list)
	&&	($handle = db_get_connection(CURRENT_MASTER_party_db))
	) {
		foreach ($__stat_list as $row) {
			db_query($handle, INSERT, "
			INSERT INTO querylog SET
				QSTR			= '".addslashes($row['QSTR'])."',
				STRIPPED_QSTR	= '".addslashes($row['STRIPPED_QSTR'])."',
				STAMP			= UNIX_TIMESTAMP(),
				TIMETAKEN		= {$row['TIMETAKEN']},
				DBNAME			= '".addslashes($row['DBNAME'])."'",
				CURRENT_MASTER_party_db
			);
		}
	}
}

function db_get_connection(string|array $server_names, bool $show_failure = false): mysqli|false {
	foreach ((is_array($server_names) ? $server_names : [$server_names]) as $server_name) {
		global $$server_name;
		if ($handle = $$server_name ?? db_connect($server_name, $show_failure)) {
			return $handle;
		}
	}
	return false;
}

function get_server(string $server_name): array|false {
	global $_db_servers;
	return $_db_servers[$server_name] ?? false;
}

function db_connect(string $server_name, bool $show_failure = true): mysqli|false {
	static $__bad;
	if (isset($__bad[$server_name])) {
		log_sporadically("db_connect_$server_name=bad", "db_connect $server_name is bad");
		return false;
	}
	if (!($server = get_server($server_name))) {
		# server probably offline (or spec is bad)
		# mail_log('db_connect: failed to find server by name: '.$server_name);
		log_sporadically("db_connect_$server_name=missing", "db_connect $server_name is missing");
		return false;
	}

	$key = "bad_connects_$server_name";
	global $memcache;
	if ($memcache
	&&	($fails = $memcache->get($key))
	&&	$fails > DB_MAX_CONNECTION_FAILURES
	) {
		if (CLI && log_sporadically("bad_server:$server_name", per: TEN_MINUTES)) {
			require_once '_syslog.inc';
			syslog_error(LOG_ERR, "server $server_name bad, too many failed connections");
		}
		return false;
	}
	global $$server_name;
	if ($$server_name) {
		if ($$server_name->thread_id) {
			error_log("NOTICE db_connect(): closing existing handle for $server_name @ {$_SERVER['REQUEST_URI']}");
			mysqli_close($$server_name);
		} else {
			error_log("NOTICE db_connect(): handle for $server_name already closed @ {$_SERVER['REQUEST_URI']}");
		}
		unset($$server_name);
	}
	if (!($handle = mysqli_init())) {
		error_log("ERROR db_connect(): mysqli_init for $server_name failed @ {$_SERVER['REQUEST_URI']}");
		return false;
	}
	foreach ([
		MYSQLI_OPT_CONNECT_TIMEOUT		=> 5,
		MYSQLI_OPT_INT_AND_FLOAT_NATIVE	=> true,
	] as $option => $value) {
		if (!mysqli_options($handle, $option, $value)) {
			error_log("ERROR db_connect(): mysqli_option $option for $server_name failed");
			return false;
		}
	}
	global $$server_name;
	if (db_real_connect($handle, $server_name, $server)) {
		/** @noinspection UselessReturnInspection */
		return $$server_name = $handle;
	}
	unset($$server_name);
	$__bad[$server_name] = true;
	if ($show_failure) {
		static $__shown = [];
		if (!isset($__shown[$server_name])) {
			$__shown[$server_name] = true;
			require_once '__translation.php';
			register_error('db:error:connection_failed', ['NAME' => $server_name]);
		}
	}
	if ($memcache) {
		$memcache->increment($key, 1, 1, DB_EXPIRE_CONNECTION_FAILURES);
	}
	return false;
}

function db_real_connect(
	mysqli $handle,
	string $server_name,
	array  $server,
	int    $flags		= 0,
): mysqli|false {
	global	$__db_errno, $__db_errstr;
	try {
		if (empty($server['IP'])) {
			$server_ip = null;
		} else {
			$server_ip = $server['IP'];
			if (DB_PERSISTENT_CONNECTIONS) {
				$server_ip = 'p:'.$server_ip;
			}
		}
		if (mysqli_real_connect(
			$handle,
			$server_ip,
			$server['USER']		?? 'party',
			$server['PASSWD']		?? null,
			$server['DBNAME']		?? null,
			$server['PORT']			?? null,
			$server['SOCKET']		?? null,
			$flags)
		) {
			global $__db_failed;
			$__db_failed = false;
			# NOTE: Setting charset to binary solves some implicit conversion issues (contact_ticket FROM_NAME and such)
			#if (!mysqli_query($handle, 'SET NAMES binary')
			if (!HOME_OFFICE
			&&	!mysqli_set_charset($handle, 'binary')
			||	($connection = $server['CONNECTION'] ?? null)
			&&	!mysqli_query($handle, $qstr = "SET @@default_master_connection = $connection")
			) {
				$__db_failed = true;
				db_log_message(LOGERROR, DBSET, $server_name, $qstr, $handle, $flags);
				return false;
			}

			if (defined('DB_DEBUG') && DB_DEBUG) {
				# WARNING: this makes mysqli_query throw exceptions when no index/key is used!
				/** @noinspection PhpObjectFieldsAreOnlyWrittenInspection */
				$driver              = new mysqli_driver();
				$driver->report_mode = MYSQLI_REPORT_ALL | MYSQLI_REPORT_ERROR | MYSQLI_REPORT_STRICT;
			}
			return $handle;
		}
	} catch (Exception $exception) {
		error_log('mysqli_real_connect failed: '.$exception->getMessage());
	}
	$__db_errno  = mysqli_connect_errno();
	$__db_errstr = mysqli_connect_error();
	log_sporadically(
		"db_real_connect_$server_name=dead",
		"db_real_connect $server_name is dead, eerrno: $__db_errno, errstr: $__db_errstr"
	);
	return false;
}

function db_slave_get_behind(string $slave, mysqli $handle): int|false {
	if (!($res = db_query($handle, DB_SHOW, 'SHOW SLAVE STATUS', $slave))) {
		error_log("ERROR db_slave_get_behind(): failed to get slave status for $slave: ".mysqli_error($handle));
		return false;
	}
	if (!($row = mysqli_fetch_assoc($res))) {
		error_log("ERROR db_slave_get_behind(): empty slave status row for $slave");
		return false;
	}
	if (is_numeric($behind = $row['Seconds_Behind_Master'])) {
		return (int)$behind;
	}
	return false;
}

function db_slave_ok(string $slave, &$behind = null): mysqli|false {
	if (partyflock_read_only()) {
		return db_get_connection($slave);
	}
	$behind_key = 'slave_behind_'.$slave;
	$status_key = 'slave_status_'.$slave;
	global $memcache;
	if ($memcache) {
		$status = $memcache->get($status_key);
		if ($status
		&&	$behind !== null
		) {
			$behind = $status;
		}
	}
	if (empty($status)) {
		if (!($handle = db_get_connection($slave))) {
			return false;
		}

		# $behind = (ONLY_LAST_DATADB && 0 === strncmp($slave,'dbd',3)) ? 0 : db_slave_get_behind($slave,$handle);
		$behind = db_slave_get_behind($slave, $handle);
		$status = ($behind === false || $behind > SLAVE_MAX_BEHIND ? SLAVE_BAD : SLAVE_OK);

		if ($memcache) {
			if ($status !== SLAVE_OK) {
				$countkey = 'badslavecnt_'.$slave;
				$hits = $memcache->increment($countkey, 1, 1, SLAVE_EXPIRE_BEHIND_FAILURES);
				if ($hits > SLAVE_MAX_BEHIND_FAILURES) {
					if ($behind) {
						$memcache->set($behind_key, $behind, SLAVE_EXPIRE_BEHIND_FAILURES);
					}
					$memcache->set($status_key, SLAVE_BAD, SLAVE_EXPIRE_BEHIND_FAILURES);
					$memcache->delete($countkey);

				} elseif ($hits === SLAVE_MAX_BEHIND_FAILURES) {
					error_log("NOTICE db_slave_ok(): maximum behind failures reached for $slave".($behind ? ", {$behind}s behind" : ', generic failure'));
				}
				$handle = false;
			} else {
				$memcache->set($status_key, SLAVE_OK, SLAVE_EXPIRE_BEHIND_FAILURES);
			}
		} elseif ($status === SLAVE_BAD) {
			$handle = false;
		}
	} elseif ($status === SLAVE_BAD) {
		return false;
	} else {
		$handle = db_get_connection($slave);
	}
	return $handle;
}

/*function fail_these_tables(string|array $tables, ?array $skiptables = null, bool $writeonly = false): bool {
	if ($tables === 'analyzing') {
		return false;
	}
	if (!$skiptables) {
		static $__skiptable = null;
		if (!($__skiptable ??= db_single('analyzing','SELECT TABLENAME FROM analyzing') ?: false)) {
			return false;
		}
		$skiptables = [$__skiptable];
	}
	foreach ($skiptables as $skip) {
		if (is_scalar($tables)
		?	$tables === $skip
		:	(
			is_array($tables)
			?	in_array($skip, $tables)
			:	false
		)
		) {
		 	require_once '_spider.inc';
			if (ROBOT) {
				header('Retry-After: 3600', true, 503);
			}
			return true;
		}
	}
	return false;
}*/

function db_get_master_for_table(string $table): ?string {
	if (!($spec = db_get_spec($table))) {
		return null;
	}
	if (is_string($spec)) {
		return $spec;
	}
	return $spec[0];
}

# tables:	database tables
# qstr:		the query
# type:		SIMPLE_ASSOC, SINGLE_ASSOC, SINGLE_ARRAY, etc, see defines/db.inc
# flags:	DB_FORCE_MASTER, DB_FRESHEN_MEMCACHE,  etc, see defines/db.inc
# arg:		DB_PREFER_SERVER:	servername
#			dataservers:		id

function db_read(
	 string|array|null		 $tables,
	 string					 $qstr,
	 int 					 $type,
	?int					 $flags		= null,
	 int|string|array|null	 $arg		= null,
	?int					 $max_behind = null,
	?array					&$context   = null,
	?int					 $indices	= null,
): mixed {
	// NOTE: Maybe determine tables automatically in the future?
	//	if (HOME_THOMAS) {
	//		if (preg_match_all('!(?:FROM|JOIN|INSERT\s+(DELAYED|IGNORE)\s*INTO|REPLACE|UPDATE)\s+([a-zA-Z\d_]+)!u', $qstr, $matches)) {
	//			error_log_r($matches, 'tables');
	//		}
	//	}

	$flags ??= 0;
	if ($flags & (DB_FORCE_SERVER | DB_PREFER_SERVER)) {
		if (!$arg) {
			# null argument to FORCE_SERVER is just a regular query
			$flags &= ~(DB_FORCE_SERVER | DB_PREFER_SERVER);
			$arg = null;
		} else {
			global $__db_errno;
			# $arg is server name or array of server names
			if (is_array($arg)) {
				$servers = $arg;
				# WARNING: It seems illogical to FORCE or PREFER a server and then get
				#		   a random server from the supplied list. It seems better to
				#		   walk the list in order, so when the first server fails, we
				#		   try the next,
				#
				# Use repeatable shuffle to get repeatable shuffle and same query to same server
				# repeatable_safe_shuffle($servers, crc32($qstr));
			} else {
				$servers = [$arg];
			}
			foreach ($servers as $server) {
				$result =
					($handle = db_get_connection($arg, show_failure: true))
				?	db_read_query($tables, $qstr, $type, $flags, $handle, $server, $context, $indices)
				:	false;
				if (!$__db_errno) {
					if ($handle) {
						global $__db_chosen_server;
						$__db_chosen_server = $server;
						return $result;
					}
					# errno is 0, no handle, server offline
				} elseif (!db_retry_on_replica()) {
					return false;
				}
			}
			return false;
		}
	}
	if (is_scalar($spec = db_get_spec($tables))) {
		if ($handle = db_get_connection($spec, show_failure: true)) {
			return db_read_query($tables, $qstr, $type, $flags, $handle, $spec, $context, $indices);
		}
		return false;
	}
	[$master, $slaves, $slave_count] = $spec;

	if (SERVER_SANDBOX) {
		$slaves = [$master];
	} else {
		if (($max_behind !== null && $max_behind < 0)
		||	($flags & DB_USE_MASTER)
		) {
			$force_fresh = true;
			$max_behind  = 0;
		}
		if ($slave_count === 1) {
			$slaves = [$slaves];
		} else {
			if (is_int($arg)) {
				$use_index = $arg % $slave_count;
			} else {
				if (!$tables) {
					$feed_serialize = $qstr;
				} elseif (is_scalar($tables)) {
					$feed_serialize = [$tables];
				} else {
					$feed_serialize = $tables;
					sort($feed_serialize);
				}
				$use_index = crc32(igbinary_serialize($feed_serialize)) % $slave_count;
			}
			$use_slave = $slaves[$use_index];
			unset($slaves[$use_index]);

			# Using repeatable shuffle to make sure we randomize the slaves the same for the same
			# $use_index, as to optimize query cache and data on disk locality.
			repeatable_safe_shuffle($slaves, $use_index);
			array_unshift($slaves, $use_slave);
		}
	}

	static $__master_status	 = [];
	static $__slave_status   = [];

	static $__slave_handles  = [];
	static $__slave_behinds  = [];
	static $__failed_servers = [];

	$done_slaves = [];
	$slaves[] = $master; # master as last resort
	$result = false;

	foreach ($slaves as $replica) {
		--$slave_count;
		if (isset($done_slaves[$replica])) {
			continue;
		}
		$done_slaves[$replica] = true;
		if (# if master is same as slave, just try to connect, server cannot be 'behind'
			$master === $replica
			# if server is sphinx daemon, just try to connect, server cannot be 'behind'
		||	str_contains($replica, 'sphinx')
		) {
			global $__db_errno;
			if ($handle = db_get_connection($replica, !$slave_count)) {
				$result = db_read_query($tables, $qstr, $type, $flags, $handle, $replica, $context, $indices);
				if (!$__db_errno) {
					break;
				}
			}
			if (!$handle
			||	can_try_next_slave()
			) {
				continue;
			}
			break;
		}
		if (isset($__failed_servers[$replica])) {
			continue;
		}
		if (!isset($__slave_handles[$replica])) {
			if (!isset($__slave_behinds[$replica])) {
				$__slave_behinds[$replica] = 0;
			}
			$handle = db_slave_ok($replica, $__slave_behinds[$replica]);
			$__slave_handles[$replica] = $handle;
		} else {
			$handle = $__slave_handles[$replica];
		}
		if (!$handle) {
			continue;
		}
		if ($max_behind !== null
		&&	$max_behind < $__slave_behinds[$replica]
		) {
			continue;
		}
		$need_status = partyflock_read_only() ? false : db_tables_are_dirty($tables, $replica);
		if (!$need_status
		&&	!isset($force_fresh)
		) {
			$result = db_read_query($tables, $qstr, $type, $flags, $handle, $replica, $context, $indices);
			if (can_try_next_slave()) {
				continue;
			}
			break;
		}
		if (isset($force_fresh)
		&&	!partyflock_read_only()
		) {
			if (!isset($__master_status[$master])) {
				$__master_status[$master] =
					($master_handle = db_get_connection($master, !$slave_count))
				?	db_get_master_status($master_handle, $master) : false;
			}
			$masterstatus = $__master_status[$master];
			if (!$need_status
			||	$masterstatus[0]  >  $need_status[0]
			||	$masterstatus[0] === $need_status[0]
			&&	$masterstatus[1]  >  $need_status[1]
			) {
				$need_status = $masterstatus;
			}
		}

		if ($need_status
		&&	isset($__slave_status[$replica]
		)) {
			[$have_file, $have_pos] = $__slave_status[$replica];
			if ($have_file  >  $need_status[0]
			||	$have_file === $need_status[0]
			&&	$have_pos  >=  $need_status[1]
			) {
				$result = db_read_query($tables, $qstr, $type, $flags, $handle, $replica, $context, $indices);
				if (can_try_next_slave()) {
					continue;
				}
				break;
			}
		}
		if ($need_status !== false) {
			static $__timeout = null;
			$__timeout ??= CLI && preg_match('"/maintenance\b"', $_SERVER['PHP_SELF']) ? 60 : 0;
			$sub_result = db_query(
				$handle,
				SELECT,
				'SELECT MASTER_POS_WAIT("'.$need_status[0].'",'.$need_status[1].','.(
					# NOTE: WAIT_FOR_SLAVE_TIMEOUT too long maybe?
					$__timeout ?: (isset($force_fresh) ? 1 : WAIT_FOR_SLAVE_TIMEOUT)
				).')',
				$replica
			);
			if (!$sub_result) {
				$__failed_servers[$replica] = true;
				continue;
			}
			$wait = mysqli_fetch_row($sub_result);
			mysqli_free_result($sub_result);
			if (!$wait) {
				$__failed_servers[$replica] = true;
				continue;
			}
			if ($wait[0] === null
			||	$wait[0] === -1
			) {
				$__failed_servers[$replica] = true;
				if ( $wait[0] === -1
				&&	($sub_result = db_query($handle, DB_SHOW, 'SHOW FULL PROCESSLIST', $replica))
				) {
					while ($query = mysqli_fetch_assoc($sub_result)) {
						if ($query['Info']
						&&	$query['Time'] >= DB_SLOW_QUERY_FROM
						&&	$query['State'] !== 'Locked'
						&&	$query['State'] !== 'Waiting for table level lock'
						&&	!str_contains($query['Info'], 'MASTER_POS_WAIT')
						#	report same query only once per 10 seconds
						&&	memcached_add('show_slow:'.hash('xxh128', $query['Info']), true, 10)
						) {
							error_log($replica.'.SLOW_SLAVE running: '.$query['Time'].', state: '.$query['State'].', id: '.$query['Id']);
							error_log($replica.'.SLOW_SLAVE query: '.combine_whitespace($query['Info']));
						}
					}
					mysqli_free_result($sub_result);
				}
				continue;
			}
			$__slave_status[$replica] = $need_status;
		}
		$result = db_read_query($tables, $qstr, $type, $flags, $handle, $replica, $context, $indices);
		if (can_try_next_slave()) {
			continue;
		}
		break;
	}
	global $__db_errno;
	if ($__db_errno) {
		require_once '_helper.inc';
		if (!CLI) {
			header('Retry-After: 600', true, 503);
		}
	}
	return $result;
}

function db_get_write_servername(string|array|null $tables): string|false {
	global $_db_tables, $_db_servers;
	if (is_array($tables)) {
		$handles = [];
		foreach ($tables as $table) {
			if (isset($_db_tables[$table])) {
				$table_info = $_db_tables[$table];
				$handles[] = is_scalar($table_info) ? $table_info : $table_info[0];
			} else {
				$handles[] = CURRENT_MASTER_party_db;
			}
		}
		// all write handles must be identical
		$master = null;
		foreach ($handles as $handle) {
			if (!$master) {
				$master = $handle;
			} elseif ($master !== $handle) {
				error_log('ERROR db_get_write_servername() for tables '.implode(', ', $tables).' has insane handle specs: '.implode(', ', $handles));
				return false;
			}
		}
	} elseif (isset($_db_tables[$tables])) {
		$table_info = $_db_tables[$tables];
		$master = is_scalar($table_info) ? $table_info : $table_info[0];
	} else {
		$master = CURRENT_MASTER_party_db;
	}
	if (!isset($_db_servers[$master])) {
		error_log("ERROR db_get_write_servername(): master $master does not exist as a server");
		return false;
	}
	return $master;
}

function db_get_spec(string|array|null $tables): string|array {
	# Find the configured specification of the TABLE.
	# If no $tables are specified, or no TABLE specification was found, then
	# pick the primary of party_db to perform the query.
	#
	# FIXME: Make the setting of the default database to query if no
	#		 specification is found, configurable via the database_spec.txt.

	global $_db_tables, $_db_default_spec;
	if (!$tables) {
		return $_db_default_spec[CURRENT_MASTER_party_db];
	}
	$force = null;
	if (!is_array($tables)) {
		$tables = [$tables];
	}
	$spec = null;
	foreach ($tables as $table) {
		if (!$spec
		&&	isset($_db_tables[$table])
		) {
			$spec = $_db_tables[$table];
			if (!$force || is_scalar($spec)) {
				break;
			}
			if (is_scalar($spec[1])) {
				if (isset($force[$spec[1]])) {
					break;
				}
			} else {
				foreach ($spec[1] as $ndx => $slave) {
					if (!isset($force[$slave])) {
						unset($spec[1][$ndx]);
					}
				}
				if ($spec[1]) {
					break;
				}
			}
			$spec = null;
		}
	}
	if (!$spec) {
		$spec = CURRENT_MASTER_party_db;
	}
	if (is_scalar($spec)
	&& isset($_db_default_spec[$spec])
	) {
		return $_db_default_spec[$spec];
	}
	return $spec;
}

function dbdata_single(string|array|null $table, int $id, string $qstr, int $flags = 0, ?int $maxbehind = null): string|int|float|bool|null {
	return db_read($table, $qstr, SINGLE, $flags, $id, $maxbehind);
}
function db_change_timezone(string $zone, mysqli $handle, string $server_name): bool {
	if (!($tz_res = db_query($handle, DBSET, $qstr = /** @lang MariaDB */ "SET TIME_ZONE = '$zone'", $server_name))) {
		db_log_message(LOGERROR, DBSET, $server_name, $qstr, $handle);
	}
	return $tz_res;
}
function get_mode_for_flags(int $flags): int {
	if ($flags & DB_NON_ASSOC) {
		return MYSQLI_NUM;
	}
	if ($flags & DB_ASSOC_AND_ARRAY) {
		return MYSQLI_BOTH;
	}
	return MYSQLI_ASSOC;
}

function db_read_query(
	 array|string|null	$tables,
	 string				$qstr,
	 int				$type,
	 int				$flags,
	 mysqli				$handle,
	 string				$server_name,
	?array			   &$context		= null,
	?int				$indices		= null,
): mixed {
	# NOTE: db_read_query may return anything, even false.
	# 	use db_errno() or query_failed() for queries that may return boolean false

	if ($flags & DB_UTC) {
		if (!db_change_timezone('UTC', $handle, $server_name)) {
			return false;
		}
		$result = db_read_query($tables, $qstr, $type, $flags & ~DB_UTC, $handle, $server_name, $context, $indices);
		db_change_timezone('SYSTEM', $handle, $server_name);
		return $result;
	}
	/** @noinspection PhpStrictComparisonWithOperandsOfDifferentTypesInspection, PhpConditionAlreadyCheckedInspection, CallableParameterUseCaseInTypeContextInspection */
	if (($db_timing = DB_TIMING && (DB_TIMING === true || DB_TIMING === $server_name))
	||	LOGQUERIES
	||	$context !== null
	) {
		$start_time = microtime(true);
	}

	$res = db_query($handle, $type, $qstr, $server_name, $flags);

	if (isset($start_time)) {
		$end_time = microtime(true);

		if ($context !== null) {
			$context[] = [$res !== false, $server_name, $end_time - $start_time];
		}
	}

	global	$__db_errno,
			$__db_warning_count;

	if ($handle) {
		$__db_errno = mysqli_errno($handle);
		if (!$res
		||	$__db_errno
		) {
			return false;
		}
	} elseif (!$res) {
		return false;
	}
	if ($db_timing) {
		global $__stat_list;
		$__stat_list[] = [
			'DBNAME'		=> $server_name,
			'TIMETAKEN'		=> 1000 * ($end_time - $start_time),
			'QSTR'			=> $qstr,
			'STRIPPED_QSTR' => db_clean_query($qstr),
		];
	}
	if (LOGQUERIES) {
		log_query(
			query_info::LOG_DB,
			query_info::LOG_READ,
			$tables,
			$qstr,
			1000 * ($end_time - $start_time),
			$server_name,
			$handle
		);
	}
	$fields = false;
	if ($res !== true
	&&	($fields = mysqli_fetch_fields($res))
	) {
		foreach ($fields as $field) {
			if (!field_needs_fixing($field)) {
				continue;
			}
			# associative array
			# combine identical names, keeping initial
			# place in associative array is initial encounter, though value is from the last encounter
			// $multiples = [];
			$have_field = [];
			foreach ($fields as $ndx => $inner_field) {
				if ($have_field
				&&	isset($have_field[$inner_field->name])
				) {
					// $multiples[] = $inner_field->name;
					# for now, do old ways, but this does not always work and non unique columns should be fixed
					unset($fields[$ndx]);
					$reorder = true;
				}
				$have_field[$inner_field->name] = $ndx;
			}
			# NOTE: Uncomment below to find queries with identical field names.
			// if ($multiples) {
			//	mail_log('Some selected attributes in query have been mentioned more than once', item: $multiples);
			// }
			if (isset($reorder)) {
				$fields = array_values($fields);
			}
			break;
		}
	}
	try {
		switch ($type) {
		case DBSET:
		case SELECT:
			$result = $res;
			break;

		case SINGLE:
			$mode = MYSQLI_NUM;
			$row = mysqli_fetch_row($res);
			mysqli_free_result($res);
			if (!$row) {
				return $row;
			}
			$fields && db_fix_data_types($fields, $row, $mode);
			$result = $row[0];
			break;

		case SINGLE_ASSOC:
			$mode = $flags & DB_ASSOC_AND_ARRAY ? MYSQLI_BOTH : MYSQLI_ASSOC;
			$result = mysqli_fetch_array($res, $mode);
			mysqli_free_result($res);
			$fields && $result && db_fix_data_types($fields, $result, $mode);
			break;

		case SINGLE_ARRAY:
			$row = mysqli_fetch_row($res);
			mysqli_free_result($res);
			$fields && $row && db_fix_data_types($fields, $row, MYSQLI_NUM);
			$result = $row;
			break;

		case SIMPLE_ARRAY:
			$result = [];
			while ($row = mysqli_fetch_row($res)) {
				$fields && db_fix_data_types($fields, $row, MYSQLI_NUM);
				$result[] = $row[0];
			}
			mysqli_free_result($res);
			break;

		case ROW_USE_ARRAY:
			$mode = get_mode_for_flags($flags);
			$result = [];
			while ($row = mysqli_fetch_array($res, $mode)) {
				$fields && db_fix_data_types($fields, $row, $mode);
				$result[] = $row;
			}
			mysqli_free_result($res);
			break;

		case SAME_HASH:
		case BOOLEAN_HASH:
			$result = [];
			if ($row = mysqli_fetch_row($res)) {
				$total_fields = count($row);
				do {
					$fields && db_fix_data_types($fields, $row, MYSQLI_NUM);
					$result_ptr = &$result;
					foreach ($row as $value) {
						if (!isset($result_ptr[$value])) {
							$result_ptr[$value] = [];
						}
						$result_ptr = &$result_ptr[$value];
					}
					$result_ptr = $type === BOOLEAN_HASH ? true : $row[$total_fields - 1];
				}
				while ($row = mysqli_fetch_row($res));
			}
			mysqli_free_result($res);
			break;

		case SIMPLE_HASH:
			$result = [];
			if ($row = mysqli_fetch_row($res)) {
				$total_fields = count($row);
				$indices ??= $total_fields - 1;
				$lasts = $total_fields - $indices;
				do {
					$fields && db_fix_data_types($fields, $row, MYSQLI_NUM);
					if ($lasts === 1) {
						$end = array_pop($row);
					} else {
						$end = [];
						$lasts_count = $lasts;
						while ($lasts_count) {
							array_unshift($end, array_pop($row));
							--$lasts_count;
						}
					}
					$result_ptr = &$result;
					foreach ($row as $value) {
						if (!isset($result_ptr[$value])) {
							$result_ptr[$value] = [];
						}
						$result_ptr = &$result_ptr[$value];
					}
					$result_ptr = $end;
				}
				while ($row = mysqli_fetch_row($res));
			}
			mysqli_free_result($res);
			break;

		case ARRAY_USE_HASH:
			$result = [];
			while ($row = mysqli_fetch_row($res)) {
				$fields && db_fix_data_types($fields, $row, MYSQLI_NUM);
				$result[$row[0]] = $row;
			}
			mysqli_free_result($res);
			break;

		case ROW_USE_HASH:
			$mode = get_mode_for_flags($flags);
			$result = [];
			while ($row = mysqli_fetch_array($res, $mode)) {
				$fields && db_fix_data_types($fields, $row, $mode);
				# first value determines index for resulting hash
				$key = current($row);
				$result[$key] = $row;
			}
			mysqli_free_result($res);
			break;

		# case MULTIMORESOMEHASH:
		case MULTI_ROW_HASH:
			$result = [];
			if ($row = mysqli_fetch_row($res)) {
				do {
					$fields && db_fix_data_types($fields, $row, MYSQLI_NUM);
					$end = array_pop($row);
					$result_ptr = &$result;
					foreach ($row as $value) {
						if (!isset($result_ptr[$value])) {
							$result_ptr[$value] = [];
						}
						$result_ptr = &$result_ptr[$value];
					}
					$result_ptr[] = $end;
				}
				while ($row = mysqli_fetch_row($res));
			}
			mysqli_free_result($res);
			break;

		case MULTI_ROW_USE_HASH:
			$mode = get_mode_for_flags($flags);
			$result = [];
			while ($row = mysqli_fetch_array($res, $mode)) {
				$fields && db_fix_data_types($fields, $row, $mode);
				# first value is key of hash
				$key = current($row);
				$result[$key][] = $row;
			}
			mysqli_free_result($res);
			break;

		case ROW_USE_MULTI_HASH:
		case MULTI_ROW_USE_INDEX_HASH:
			# NOTE: update this part to same more generic version like SIMPLE_HASH and SAME_HASH,
			#	    indices parameter would nicer than using underscores to indicaes keys for hashing
			$mode = get_mode_for_flags($flags);
			if (!($row = mysqli_fetch_array($res, $mode))) {
				return $row;
			}
			if (!$indices
			||	!preg_match('"/\*\s*indices:\s*(?<indices>\d+)\s*\*/"', $qstr, $match)		// NOSONAR
			||	!($indices = (int)$match['indices'])
			) {
				$indices = 0;
				foreach ($row as $key => $ignored) {
					if ($key[0] !== '_') {
						break;
					}
					$index[$indices++] = $key;
				}
			} else {
				for ($i = 0; $i < $indices; ++$i) {
					$index[$i] = $i;
				}
			}
			if (!$indices) {
				error_log('ERROR db_read_query(): missing indices specification in query: '.$qstr);
				$result = false;
				break;
			}
			$result = [];
			do {
				$fields && db_fix_data_types($fields, $row, $mode);
				# could do in for loop, but this is prolly faster
				$row_copy = $row;
				if ($flags & DB_STORE_INDEX) {
					for ($i = $indices - 1; $i >= 0; --$i) {
						$row_copy[substr($index[$i], 1)] = $row_copy[$index[$i]];
					}
				}
				switch ($indices) {
				case 1:
					unset($row_copy[$index[0]]);
					if ($type === ROW_USE_MULTI_HASH) {
						$result[$row[$index[0]]] = $row_copy;
					} else {
						$result[$row[$index[0]]][] = $row_copy;
					}
					break;
				case 2:
					unset($row_copy[$index[0]],
						  $row_copy[$index[1]]);
					if ($type === ROW_USE_MULTI_HASH) {
						$result[$row[$index[0]]][$row[$index[1]]] = $row_copy;
					} else {
						$result[$row[$index[0]]][$row[$index[1]]][] = $row_copy;
					}
					break;
				case 3:
					unset($row_copy[$index[0]],
						  $row_copy[$index[1]],
						  $row_copy[$index[2]]);
					if ($type === ROW_USE_MULTI_HASH) {
						$result[$row[$index[0]]][$row[$index[1]]][$row[$index[2]]] = $row_copy;
					} else {
						$result[$row[$index[0]]][$row[$index[1]]][$row[$index[2]]][] = $row_copy;
					}
					break;
				default:
					error_log('ERROR db_read_query(): '.db_typename($type).' needs update');
					$result = false;
					break 2;
				}
			}
			while ($row = $flags & DB_NON_ASSOC ? mysqli_fetch_row($res) : mysqli_fetch_assoc($res));
			mysqli_free_result($res);
			break;

		default:
			mysqli_free_result($res);
			error_log("ERROR db_read_query(): unknown read query type: $type for query: $qstr");
			$result = false;
		}
	} catch (Exception $exception) {
		error_log("ERROR: db_read_query(): mysqli on $server_name failed, ".$exception->getCode().': '.$exception->getMessage()."\nERROR: query: $qstr");
		$result = false;
	}
	if ($flags & SPHINX_META) {
		$meta = [];
		$total_res = db_query($handle, SELECT, 'SHOW META', $server_name, $flags);
		if ($total_res) {
			while ($row = mysqli_fetch_row($total_res)) {
				$meta[$row[0]] = $row[1];
			}
			mysqli_free_result($total_res);
		} elseif (log_sporadically('SHOW_META_failure')) {
			mail_log("SHOW META failed on $server_name:\n\n".mysqli_error($handle), get_defined_vars());
		}
		global $__sphinx_meta;
		$__sphinx_meta = $meta;
	}
	# need to get warnings all the way down here because we use USE_RESULT instead of STORE_RESULT for above query
	if ($__db_warning_count
	&&	!($flags & DB_NO_WARNINGS)
	) {
		db_log_message(LOGWARNING, $type, $server_name, $qstr, $handle, $flags);
	}
	return $result;
}

function get_dirty_cache(): array {
	global $__dirtycache, $__dirtytable;
	return [
		'cache'		=> $__dirtycache,
		'tables'	=> $__dirtytable,
	];
}

function db_cache_dirty(string|array|null $tables): bool  {
	if (!$tables) {
		return false;
	}
	global $__dirtycache;
	if (is_scalar($tables)) {
		return isset($__dirtycache[$tables]);
	}
	foreach ($tables as $table) {
		if (isset($__dirtycache[$table])) {
			return true;
		}
	}
	return false;
}

function db_tables_are_dirty(string|array|null $tables, string $server_name): bool|array {
	global $__dirtytable, $__slavestatus;
	if (!isset($__dirtytable)) {
		return false;
	}
	$chosen = false;
	foreach ((is_array($tables) ? $tables : [$tables]) as $table) {
		if (!isset($__dirtytable[$table])) {
			continue;
		}
		# need:
		 [$need_log, $need_pos] = $__dirtytable[$table];
		if (isset($__slavestatus[$server_name])) {
			# check whether we have stats for servername
			# and if they're alright, this server is not dirty
			 [$done_log, $done_pos] = $__slavestatus[$server_name];
			if ($done_log > $need_log
			||	$done_log === $need_log
			&&	$done_pos >= $need_pos
			) {
				# done is further than need
				continue;
			}
		}
		if (!$chosen			# no chosen dirty table
		||	$need_log   > $chosen[0]		# if need is further in the future than current chosen dirty table
		||	$need_log === $chosen[0]
		&&	$need_pos   > $chosen[1]
		) {
			$chosen = $__dirtytable[$table];
		}
	}
	return $chosen;
}

function db_write(
	string|array|null	$tables,
	string				$qstr,
	int					$type,
	int					$flags	= 0,
	string|array|null	$arg	= null,
): bool {
	global $__db_errno;
	if (partyflock_read_only()) {
		$__db_errno = 0;
		return false;
	}
	$primary_server = db_get_write_servername($tables);
	if (defined('DRYRUN') && DRYRUN) {
		if (DRYRUN !== 'quiet') {
			echo $primary_server, ': ', str_replace(',', ', ', $qstr), "\n";
			if (!CLI) {
				?><br /><?
			}
		}
		return true;
	}
	if ($flags & DB_FORCE_SERVER) {
		if (!$arg) {
			mail_log('db_write(): DB_FORCE_SERVER is set but no servers supplied', get_defined_vars());
			return false;
		}
		if (is_array($arg)) {
			$server_names = $arg;
		} else {
			$server_names = [$arg];
		}
	} elseif (!$primary_server) {
		mail_log('db_write(): no primary server for tables: '.($tables ? var_get($tables) : 'null'), get_defined_vars());
		return false;
	} else {
		if ($arg) {
			mail_log('db_write(): no DB_FORCE_SERVER but arg is: '.var_get($arg), get_defined_vars());
		}
		$server_names = [$primary_server];
	}
	$res = false;
	global $_db_servers;
	foreach ($server_names as $server_name) {
		if (isset($_db_servers[$server_name]['READONLY'])
		||	!($handle = db_get_connection($server_name))
		) {
			continue;
		}
		$start_time = 0;
		if (($db_timing = DB_TIMING && (DB_TIMING === true || DB_TIMING === $server_name)) || LOGQUERIES) {
			$start_time = microtime(true);
		}
		if (# Stop when we have a positive result:
			($res = db_query($handle, $type, $qstr, $server_name, $flags))
			# If we may not retry, we should not retry:
		||	!db_retry_on_replica()
		) {
			break;
		}
	}
	if (!$res) {
		if ($__db_errno === MYSQL_CR_CONNECTION_KILLED) {
			mail_log('Connection was killed', [
				'_SERVER'		=> $_SERVER,
				'defined_vars'	=> get_defined_vars()
			]);
		}
		return false;
	}
	if ($start_time) {
		$end_time = microtime(true);
	}
	global $__db_chosen_server;
	$__db_chosen_server = $server_name;
	if ($db_timing) {
		global $__stat_list;
		$__stat_list[] = [
			'DBNAME'		=> $server_name,
			'TIMETAKEN'		=> 1000 * ($end_time - $start_time),
			'QSTR'			=> $qstr,
			'STRIPPED_QSTR'	=> db_clean_query($qstr),
		];
	}
	if (LOGQUERIES) {
		log_query(
			query_info::LOG_DB,
			query_info::LOG_WRITE,
			$tables,
			$qstr,
			1000 * ($end_time - $start_time),
			$server_name,
			$handle ?? null
		);
	}
	global	$__db_insert_id,
			$__db_affected;

	switch ($type) { // NOSONAR
	case INSUPD:
	case INSERT:
	/** @noinspection PhpMissingBreakStatementInspection */
	case REPLACE:
		$__db_insert_id = mysqli_insert_id($handle);
		# fall through
	case UPDATE:
	case DELETE:
	case CREATE:
		$__db_affected = mysqli_affected_rows($handle);
		break;
	}
	if ($tables
	&&	!($flags & DB_KEEP_CLEAN)
		# Sandbox has no replicas
	&&	!SERVER_SANDBOX
		# If the server used to write is not the master server,o
		# it too has no replicas, because it's a replica itself and doesn't propagate
	&&	$server_name === $primary_server
	) {
		$status = db_get_master_status($handle, $server_name) ?: [];
		global $__dirty_table, $__dirty_cache;
		foreach ((is_array($tables) ? $tables : [$tables]) as $table) {
			if ($status) {
				$__dirty_table[$table] = $status;
			}
			$__dirty_cache[$table] = $server_name;
		}
	}
	return true;
}

function db_typename(int $type): string {
	return match($type) {
		INSERT						=> 'INSERT',
		UPDATE						=> 'UPDATE',
		INSUPD						=> 'INSUPD',
		REPLACE						=> 'REPLACE',
		DELETE						=> 'DELETE',
		TRUNCATE					=> 'TRUNCATE',
		CREATE						=> 'CREATE',
		DROP		   				=> 'DROP',
		ALTER						=> 'ALTER',
		DBSET						=> 'SET',

		SELECT						=> 'SELECT',
		SINGLE						=> 'SINGLE',
		SINGLE_ASSOC				=> 'SINGLEASSOC',
		BOOLEAN_HASH				=> 'BOOLEANHASH',
		SIMPLE_ARRAY				=> 'SIMPLEARRAY',
		ARRAY_USE_HASH   			=> 'ARRAYUSEHASH',
		ROW_USE_HASH				=> 'ROWUSEHASH',
		ROW_USE_ARRAY				=> 'ROWUSEARRAY',
		MULTI_ROW_HASH				=> 'MULTIROWHASH',
		MULTI_ROW_USE_HASH			=> 'MULTIROWUSEHASH',
		MULTI_ROW_USE_INDEX_HASH	=> 'MULTIROWUSE_IHASH',
		SIMPLE_HASH					=> 'SIMPLEHASH',
		SAME_HASH					=> 'SAMEHASH',
		ROW_USE_MULTI_HASH			=> 'ROWUSE_MULTIHASH',

		DB_SHOW						=> 'SHOW',

		default						=> '???',
	};
}

function is_duplicate(): int|false {
	global $__db_errno;
	return $__db_errno === MYSQL_ER_DUP_ENTRY;
}

function db_insert_id(): int|false {
	global $__db_insert_id;
	if (defined('DRYRUN') && DRYRUN) {
		return safe_random_int(0, 10000000);
	}
	return $__db_insert_id ?? false;
}

function sphinx_meta(): array {
	global $__sphinx_meta;
	return $__sphinx_meta ?? [];
}

function db_errno(): int {
	global $__db_errno;
	return $__db_errno ?? 0;
}

function db_errstr(): string {
	global $__db_errstr;
	return $__db_errstr ?? '';
}

function query_failed(): bool {
	global $__db_errno;
	return (bool)$__db_errno;
}

function db_retry_on_replica(): bool {
	global $__db_errno;
	return match($__db_errno) {
		# MYSQL_ER_PARSE_ERROR,
		default	=> false,

		MYSQL_ER_BAD_TABLE_ERROR,
		MYSQL_ER_SERVER_SHUTDOWN,
		MYSQL_ER_NORMAL_SHUTDOWN,
		MYSQL_ER_GOT_SIGNAL,
		MYSQL_ER_SHUTDOWN_COMPLETE,
		MYSQL_ER_FORCING_CLOSE,
		MYSQL_ER_IPSOCK_ERROR,
		MYSQL_ER_CRASHED_ON_USAGE,
		MYSQL_ER_QUERY_INTERRUPTED,
		MYSQL_CR_UNKNOWN_ERROR,
		MYSQL_CR_CONNECTION_ERROR,
		MYSQL_CR_CONN_HOST_ERROR,
		MYSQL_CR_IPSOCK_ERROR,
		MYSQL_CR_UNKNOWN_HOST,
		MYSQL_CR_GONE_AWAY,
		MYSQL_CR_VERSION_ERROR,
		MYSQL_CR_OUT_OF_MEMORY,
		MYSQL_CR_WRONG_HOST_INFO,
		MYSQL_CR_TCP_CONNECTION,
		MYSQL_CR_SERVER_HANDSHAKE_ERR,
		MYSQL_CR_SERVER_LOST,
		MYSQL_CR_SERVER_LOST_EXTENDED => true,
	};
}

function db_failed(): bool {
	# useful for queries that might return a boolean, like SELECT CAST_AS_BIT(0)
	global $__db_failed;
	return $__db_failed;
}

function db_chosen_server(): ?string {
	global $__db_chosen_server;
	return $__db_chosen_server ?? null;
}
function db_affected(): int {
	global $__db_affected;
	if (defined('DRYRUN') && DRYRUN) {
		return 1;
	}
	return $__db_affected ?? 0;
}

function db_warnings(): int {
	global $__db_warnings;
	return $__db_warnings ?? 0;
}

function db_tables				(string|array|null $tables, string $qstr, int $flags = 0, string|array|null $arg = null): bool								{ return db_write($tables,$qstr, SELECT, 		 		$flags === 0 ? DB_FORCE_MASTER : $flags, $arg); }	// NOSONAR
function db_select				(string|array|null $tables, string $qstr, int $flags = 0, string|array|null $arg = null): bool|mysqli_result				{ return db_read($tables, $qstr, SELECT, 				$flags, $arg); }	// NOSONAR
function db_single				(string|array|null $tables, string $qstr, int $flags = 0, string|array|null $arg = null): bool|int|float|string|null		{ return db_read($tables, $qstr, SINGLE, 				$flags, $arg); }	// NOSONAR
function db_single_int			(string|array|null $tables, string $qstr, int $flags = 0, string|array|null $arg = null): int|false|null					{ return db_read($tables, $qstr, SINGLE, 				$flags, $arg); }	// NOSONAR
function db_single_bool			(string|array|null $tables, string $qstr, int $flags = 0, string|array|null $arg = null): ?bool								{ return db_read($tables, $qstr, SINGLE, 				$flags, $arg); }	// NOSONAR
function db_single_string		(string|array|null $tables, string $qstr, int $flags = 0, string|array|null $arg = null): string|false|null					{ return db_read($tables, $qstr, SINGLE, 				$flags, $arg); }	// NOSONAR
function db_single_assoc		(string|array|null $tables, string $qstr, int $flags = 0, string|array|null $arg = null): array|false|null 					{ return db_read($tables, $qstr, SINGLE_ASSOC,			$flags, $arg); }	// NOSONAR
function db_single_array		(string|array|null $tables, string $qstr, int $flags = 0, string|array|null $arg = null): array|false|null					{ return db_read($tables, $qstr, SINGLE_ARRAY,			$flags, $arg); }	// NOSONAR
function db_simpler_array		(string|array|null $tables, string $qstr, int $flags = 0, string|array|null $arg = null): array|false 						{ return db_read($tables, $qstr, SIMPLE_ARRAY,			$flags, $arg); }	// NOSONAR
function db_boolean_hash		(string|array|null $tables, string $qstr, int $flags = 0, string|array|null $arg = null): array|false 						{ return db_read($tables, $qstr, BOOLEAN_HASH,			$flags, $arg); }	// NOSONAR
function db_same_hash			(string|array|null $tables, string $qstr, int $flags = 0, string|array|null $arg = null): array|false						{ return db_read($tables, $qstr, SAME_HASH,			$flags, $arg); }	// NOSONAR
function db_simple_hash			(string|array|null $tables, string $qstr, int $flags = 0, string|array|null $arg = null, ?int $indices = null): array|false	{ return db_read($tables, $qstr, SIMPLE_HASH, 			$flags, $arg, indices: $indices); }	// NOSONAR
function db_rowuse_hash			(string|array|null $tables, string $qstr, int $flags = 0, string|array|null $arg = null): array|false						{ return db_read($tables, $qstr, ROW_USE_HASH,			$flags, $arg); }	// NOSONAR
function db_multirow_hash		(string|array|null $tables, string $qstr, int $flags = 0, string|array|null $arg = null): array|false						{ return db_read($tables, $qstr, MULTI_ROW_HASH,		$flags, $arg); }	// NOSONAR
function db_multirowuse_hash	(string|array|null $tables, string $qstr, int $flags = 0, string|array|null $arg = null): array|false						{ return db_read($tables, $qstr, MULTI_ROW_USE_HASH,	$flags, $arg); }	// NOSONAR
#function db_multirowuse_ihash	(string|array|null $tables, string $qstr, int $flags = 0, string|array|null $arg = null): array|false						{ return db_read($tables, $qstr, MULTI_ROW_USE_INDEX_HASH,	$flags, $arg); }	// NOSONAR
function db_rowuse_array		(string|array|null $tables, string $qstr, int $flags = 0, string|array|null $arg = null): array|false						{ return db_read($tables, $qstr, ROW_USE_ARRAY,		$flags, $arg); }	// NOSONAR

function db_insert	(string|array|null $tables, string $qstr, int $flags = 0, string|array|null $arg = null): bool { return db_write($tables, $qstr, INSERT,	$flags, $arg); }	// NOSONAR
function db_update	(string|array|null $tables, string $qstr, int $flags = 0, string|array|null $arg = null): bool { return db_write($tables, $qstr, UPDATE,	$flags, $arg); }	// NOSONAR
function db_insupd	(string|array|null $tables, string $qstr, int $flags = 0, string|array|null $arg = null): bool { return db_write($tables, $qstr, INSUPD,	$flags, $arg); }	// NOSONAR
function db_replace	(string|array|null $tables, string $qstr, int $flags = 0, string|array|null $arg = null): bool { return db_write($tables, $qstr, REPLACE,	$flags, $arg); }	// NOSONAR
function db_delete	(string|array|null $tables, string $qstr, int $flags = 0, string|array|null $arg = null): bool { return db_write($tables, $qstr, DELETE,	$flags, $arg); }	// NOSONAR
function db_truncate(string|array|null $tables, string $qstr, int $flags = 0, string|array|null $arg = null): bool { return db_write($tables, $qstr, TRUNCATE,	$flags, $arg); }	// NOSONAR
function db_create	(string|array|null $tables, string $qstr, int $flags = 0, string|array|null $arg = null): bool { return db_write($tables, $qstr, CREATE,	$flags, $arg); }	// NOSONAR
function db_drop	(string|array|null $tables, string $qstr, int $flags = 0, string|array|null $arg = null): bool { return db_write($tables, $qstr, DROP,		$flags, $arg); }	// NOSONAR
function db_alter	(string|array|null $tables, string $qstr, int $flags = 0, string|array|null $arg = null): bool { return db_write($tables, $qstr, ALTER,	$flags, $arg); }	// NOSONAR

function db_getlock(string $name, int $timeout = 1): ?bool {
	# returns:	true for succes
	#			false for failure
	#			null for timeout

	$server_name = CURRENT_MASTER_party_db;

	if (defined('DRYRUN') && DRYRUN) {
		echo $server_name ?>: SELECT SQL_NO_CACHE GET_LOCK('<?= $name ?>', <?= $timeout ?>);<? echo "\n";
		return true;
	}
	if (!($handle = db_get_connection($server_name))) {
		return false;
	}
	$qstr = 'SELECT SQL_NO_CACHE GET_LOCK("'.addslashes($name).'", '.$timeout.')';

	/** @noinspection PhpStrictComparisonWithOperandsOfDifferentTypesInspection */
	if (($db_timing = DB_TIMING && (DB_TIMING === true || DB_TIMING === $server_name)) || LOGQUERIES) {
		$start_time = microtime(true);
	}

	$res = db_query($handle, SELECT, $qstr, $server_name);

	/** @noinspection DuplicatedCode */
	if (isset($start_time)) {
		$end_time = microtime(true);
	}

	if ($db_timing) {
		global $__statlist;
		$__statlist[] = [
			'DBNAME'		=> $server_name,
			'TIMETAKEN'	  	=> 1000 * ($end_time - $start_time),
			'QSTR'			=> $qstr,
			'STRIPPED_QSTR' => db_clean_query($qstr),
		];
	}
	if (LOGQUERIES) {
		log_query(
			query_info::LOG_DB,
			query_info::LOG_READ,
			null,
			$qstr,
			1000 * ($end_time - $start_time),
			$server_name,
			$handle
		);
	}
	if (!$res
	||	!($row = mysqli_fetch_row($res))
	||	!isset($row[0])
	) {
		return false;
	}
	if ($row[0]) {
		return true;
	}
	return null;
}

function db_releaselock(string $name): bool {
	$server_name = CURRENT_MASTER_party_db;
	if (defined('DRYRUN') && DRYRUN) {
		echo $server_name ?>: SELECT SQL_NO_CACHE RELEASE_LOCK('<?= $name ?>')<? echo "\n";
		return true;
	}
	if (!($handle = db_get_connection($server_name))) {
		return false;
	}
	$qstr = 'SELECT SQL_NO_CACHE RELEASE_LOCK("'.addslashes($name).'")';

	/** @noinspection PhpStrictComparisonWithOperandsOfDifferentTypesInspection */
	if (($db_timing = DB_TIMING && (DB_TIMING === true || DB_TIMING === $server_name)) || LOGQUERIES) {
		$start_time = microtime(true);
	}

	$res = db_query($handle, SELECT, $qstr, $server_name, 0, $reconnected);

	/** @noinspection DuplicatedCode */
	if (isset($start_time)) {
		$end_time = microtime(true);
	}

	if ($db_timing) {
		global $__statlist;
		$__statlist[] = [
			'DBNAME'		=> $server_name,
			'TIMETAKEN'	 	=> 1000 * ($end_time - $start_time),
			'QSTR'			=> $qstr,
			'STRIPPED_QSTR'	=> db_clean_query($qstr),
		];
	}

	if (LOGQUERIES) {
		log_query(
			query_info::LOG_DB,
			query_info::LOG_READ,
			null,
			$qstr,
			1000 * ($end_time - $start_time),
			$server_name,
			$handle
		);
	}

	if (!$res) {
		return false;
	}
	if ($reconnected) {
		# all locks have been released
		$unlocked = true;
	} else {
		if (!($row = mysqli_fetch_row($res))) {
			return false;
		}
		$unlocked = (bool)$row[0];
	}
	return $unlocked;
}

const DBLOCK_READ	= ' READ';
const DBLOCK_WRITE	= ' WRITE';

function db_lock_tables(
	string|array|null $tables,
	string			  $lock_type = DBLOCK_WRITE,
	int		  		  $flags	 = 0,
	?string			  $arg		 = null
): bool {
	if (partyflock_read_only()) {
		global $__db_errno;
		$__db_errno = 0;
		return false;
	}
	if (!is_array($tables)) {
		$tables = [$tables];
	}
	$table_specs = [];
	foreach ($tables as $table) {
		$table_specs[] =
		(	false !== ($dot_pos = strpos($table, '.'))
		?	substr($table, 0, $dot_pos).'.`'.substr($table, $dot_pos + 1).'`'
		:	"`$table`"
		).	" $lock_type";
	}
	return db_tables($tables, 'LOCK TABLES '.implode(', ', $table_specs), $flags, $arg);
}

function db_unlock_tables(
	string|array|null $tables = null,
	int				  $flags  = 0,
	?string			  $arg	  = null,
): bool {
	/* '.($tables === null ? 'NULL' : (is_string($tables) ? $tables : implode(', ', $tables))).' */
	return db_tables($tables, 'UNLOCK TABLES', $flags, $arg);
}

function db_table_info(string $table, int $flags = 0, string|array|null $arg = null): array|false|null {
	if (false === ($dot = strpos($table, '.'))) {
		return false;
	}
	$database	= substr($table, 0, $dot);
	$table		= substr($table, $dot + 1);

	if (!($info = db_single_assoc($table, "CHECKSUM TABLE $database.`$table`", $flags, $arg))) {
		return false;
	}
	$checksum = $info['Checksum'];
	if (!($info = db_single_assoc($table, "
		SELECT TABLE_ROWS,DATA_LENGTH
		FROM information_schema.TABLES
		WHERE TABLE_SCHEMA = '$database'
		  AND TABLE_NAME   = '$table'",
		$flags,
		$arg))
	) {
		return $info;
	}
	return [$checksum, $info['TABLE_ROWS'], $info['DATA_LENGTH']];
}

# expects db_table_info arrays as parameter one and two
function db_identical_tables(
	string|array|null $info_one,
	string|array|null $info_two,
	int				  $flags = 0,
	string|array|null $arg	 = null,
): bool {
	if (is_string($info_one)) {
		$info_one = db_table_info($info_one, $flags, $arg);
		$info_two = db_table_info($info_two, $flags, $arg);
	}
	if (!$info_one || !$info_two) {
		return false;
	}
	return	$info_one[0] === $info_two[0]
		&&	$info_one[1] === $info_two[1]
		&&	$info_one[2] === $info_two[2];
}

function db_log_message(
	int		$severity,
	int		$qtype,
	string	$server_name,
	string	$qstr,
	?mysqli	$handle   = null,
	int		$db_flags = 0,
): bool {
	global $__db_errno, $__db_errstr;

	if (can_try_next_slave()
	&&	!memcached_add('dberrno:'.$server_name.':'.$__db_errno, true, 10)
	) {
		return false;
	}
	$flags = LOG_DB_PROBLEM | LOG_URI | LOG_BACKTRACE;
	$qstr = shorten_query_for_log($qstr);
	$len = strlen($qstr);
	if ($len > 1024 * 10) {
		$qstr = substr($qstr, 0, 10 * KILOBYTE).'...';
	}
	if ($len < 4 * MEGABYTE) {
		$flags |= LOG_BACKTRACE;
	}
	if ($handle
	&&	($srv = get_server_name_from_handle($handle))
	) {
		$server_name = $srv;
	}
	$qstr = str_replace("\t", '	', $qstr);

	$flags |= $severity;

	switch ($severity) { // NOSONAR
	case LOGERROR:
		log_message($flags, $qstr, $server_name);

		if ($__db_errno === MYSQL_ER_TRUNCATED_WRONG_VALUE_FOR_FIELD) {
			mail_log('MYSQL_ER_TRUNCATED_WRONG_VALUE_FOR_FIELD: '.$__db_errstr, item: ['_POST' => $_POST]);
		}

		if (($db_flags & DB_USE_SYSLOG)
		||	function_exists('syslog_is_open')
		&&	syslog_is_open()
		) {
			syslog_error(LOG_ERR, "query failed: $qstr\n".db_errstr());
		}

		$errstr = $qtype < SELECT
		?	(defined('CURRENTLANGUAGE') && CURRENTLANGUAGE === 'nl' ? 'Database schrijfoperatie mislukt' : 'Database write operation failed').' ('.$server_name.':'.db_typename($qtype).')'
		:	(defined('CURRENTLANGUAGE') && CURRENTLANGUAGE === 'nl' ? 'Database leesoperatie mislukt'    : 'Database read operation failed'). ' ('.$server_name.':'.db_typename($qtype).')';
		_error($errstr);
		if ($__db_errno
		&&	$__db_errno !== MYSQL_CR_GONE_AWAY				# this is amendable
		&&	$__db_errno !== MYSQL_CR_COMMANDS_OUT_OF_SYNC	# make sure we don't keep warning about this in a never ending loop
		&&	!SERVER_SANDBOX
		&&	!HOME_THOMAS
		&&	!($flags & DB_NO_FLOCKBOT)
		&&	!CLI
		&&	!have_admin('development')
		) {
			require_once '_date.inc';
			require_once '_helper.inc';
			# errno 0 usually is reconnect warning

			[$file, $line, $function] = get_interesting_caller();

			$flock_msg =
				"<u>database query failed!</u>\n". //NOSONAR
				'<b>date</b>: '._datetime_get(CURRENTSTAMP)."\n".
				'<b>uri</b>: '.($_SERVER['REQUEST_URI'] ?? '?')."\n".
				"<b>server</b>: $server_name\n".
				"<b>query</b>: $qstr\n".
				"<b>errno</b>: $__db_errno\n".
				($__db_errstr ? "<b>errstr</b>: $__db_errstr\n" : '').
				"<b>called from file</b>: $file\n".
				"<b>called from func</b>: $function\n".
				"<b>called from line</b>: $line";

			require_once '_flockbot.inc';
			flockbot_notify(FLOCK_CHANNEL_IMPORTANT, $flock_msg);
		}
		break;

	case LOGWARNING:
		try {
			global $__db_handle_for_warnings;
			if ($warning = mysqli_get_warnings($__db_handle_for_warnings)) {
				do {
					if ((	$warning->errno !== MYSQL_ER_DUP_ENTRY
						||	!($db_flags & DB_DONT_WARN_DUP_ENTRY)
						)
					&&	(	$warning->errno !== MYSQL_ER_WARN_DEPRECATED_SYNTAX
						||	!str_contains($qstr, 'DELAYED')
						)
					) {
						$warnings[] = $warning;
					}
				} while ($warning->next());
			}
		} catch (Exception $exception) {
			error_log('ERROR db_log_message(): mysqli_get_warnings failed: '.$exception->getMessage());
		}
		if (isset($warnings)) {
			log_message($flags, $qstr, $server_name);
			mail_log('warnings from database', $warnings);
			foreach ($warnings as $warning) {
				log_message($flags, $warning->sqlstate . ': ' . $warning->message, $server_name);
 			}
		}
		break;
	}
	return true;
}

function db_slave_behinds(string|array $tables, int $flags = 0): array|false|null {
	if (SERVER_SANDBOX) {
		return [0];
	}
	if ($flags & DB_FORCE_SERVER) {
		$test_replicas = is_array($tables) ? $tables : [$tables];
	} else {
		$test_replicas = [];
		if (!is_array($tables)) {
			$tables = [$tables];
		}
		foreach ($tables as $table) {
			if (is_scalar($spec = db_get_spec($table))) {
				continue;
			}
			[$master, $slaves, $slavecnt] = $spec;
			if ($slavecnt === 1) {
				$slaves = [$slaves];
			}
			foreach ($slaves as $slave) {
				if ($slave !== $master) {
					$test_replicas[$slave] = $slave;
				}
			}
		}
		if (!$test_replicas) {
			return null;
		}
	}
	$behinds = [];
	foreach ($test_replicas as $slave) {
		if (!($handle = db_get_connection($slave))) {
			error_log('WARNING db_slave_behinds(): no connection for slave_behinds for '.$slave);
			# ignore failed connections, server probabkly offline
			continue;
			//return false;
		}
		if (!($res = db_query($handle, DB_SHOW, 'SHOW SLAVE STATUS', $slave))) {
			error_log("ERROR db_slave_behinds(): failed to get slave status for $slave: ".mysqli_error($handle));
			return false;
		}
		if (!($row = mysqli_fetch_assoc($res))) {
			error_log("ERROR db_slave_behinds(): empty slave status row for $slave");
			return false;
		}
		if (!is_int($behind = $row['Seconds_Behind_Master'])) {
			continue;
		}
		if (!($flags & DB_OK_SLAVES_ONLY)
		||	$behind <= SLAVE_MAX_BEHIND
		) {
			$behinds[] = $behind;
		}
	}
	return $behinds;
}

function can_try_next_slave(): bool {
	global $__db_errno;
	return	match($__db_errno) {
		MYSQL_ER_STORAGE_ENGINE,
		MYSQL_CR_CONN_HOST_ERROR,
		MYSQL_ER_SERVER_SHUTDOWN,
		MYSQL_ER_CRASHED_ON_USAGE,
		MYSQL_CR_GONE_AWAY,
		MYSQL_CR_SERVER_LOST,
		MYSQL_CR_SERVER_LOST_EXTENDED	=> true,
		default							=> false,
	};
}

function explain_table(string $table, int $flags = 0, string|array|null $arg = null): array|false {
	static $__explained;
	if (isset($__explained[$table])) {
		return $__explained[$table];
	}
	$table = str_replace('.', '`.`', $table);

	if (!($res = db_read($table, 'EXPLAIN `'.$table.'`', SELECT, $flags, $arg))) {
		return $__explained[$table] = false;
	}
	$specs = [];
	while ($attrib = mysqli_fetch_row($res)) {
		$new_spec = new attrib_spec();

		$key = $attrib[0];
		$new_spec->type   = $attrib[1];
		$new_spec->null   = $attrib[2] === 'YES';
		$new_spec->key	  = $attrib[3];
		$new_spec->extra  = $attrib[5];

		if (str_contains($new_spec->type, 'int(')) {
			$new_spec->default = (int)$attrib[4];
		} elseif ($new_spec->type === 'bit(1)') {
			$new_spec->default = $attrib[4] === "b'1'";
		} else {
			$new_spec->default = $attrib[4];
		}
		if (preg_match('"^(?:var)?char\((?<size>\d+)\)"', $new_spec->type, $match)) {
			$new_spec->maxlength = (int)$match['size'];
		} elseif (preg_match('"^(?<base_type>(?:medium|small|tiny|big)?int)\(\d+\)(?<unsigned>\s*unsigned)?"', $new_spec->type, $match)) { // NOSONAR
			$unsigned = !empty($match['unsigned']);
			switch ($match['base_type']) { // NOSONAR
			case 'tinyint':		$new_spec->min = $unsigned ? 0 :				 -128; $new_spec->max = $unsigned ?					  255  : 127; break;
			case 'smallint':	$new_spec->min = $unsigned ? 0 :			   -32768; $new_spec->max = $unsigned ?					65535  : 32767; break;
			case 'mediumint':	$new_spec->min = $unsigned ? 0 :			 -8388608; $new_spec->max = $unsigned ?				 16777215  : 8388607; break;
			case 'int':			$new_spec->min = $unsigned ? 0 :		  -2147483648; $new_spec->max = $unsigned ?			   4294967295  : 2147483647; break;
			case 'bigint':		$new_spec->min = $unsigned ? 0 : -9223372036854775808; $new_spec->max = $unsigned ? '18446744073709551615' : 9223372036854775807; break;
			}
		} elseif (preg_match("!^(?<base_type>enum|set)\('(?<parts>.*?)'\)$!", $new_spec->type, $match)) {
			$parts = explode("','", $match['parts']);
			$store = $match['base_type'];
			foreach ($parts as $part) {
				$new_spec->$store[$part] = $part;
			}
		} elseif (preg_match('"^(?<size>tiny|medium|long)?(?<base_type>text|blob)(?<compressed>\s+/\*M?!100301\s+COMPRESSED\*/)?$"', $new_spec->type, $match)) {	// NOSONAR
			# Sometimes there's a strange M in front of the !100301 COMPRESSED.
			# Also strange: often the entire !100301 COMPRESSED is missing from the type.
			$new_spec->maxlength = match($match['size'] ?? '') {
				'tiny'		=> 255,
				''			=> 65_535,
				'medium'	=> 16_777_216,
				'long'		=> 4_294_967_296,
			};
		}

		$specs[$key] = $new_spec;
	}
	mysqli_free_result($res);

	!$specs && register_error('db:error:explain_table_no_results_LINE', ['TABLE' => $table]);

	return $__explained[$table] = $specs;
}

function field_needs_fixing(stdClass $field): bool {
	return match ($field->type) {
		MYSQLI_TYPE_BIT			=> $field->length <= 64,	# Convert bit(1) to bool and bit(n) to int.
		MYSQLI_TYPE_VAR_STRING	=> $field->length === 1,	# Convert varchar(1) to	bool.
		MYSQLI_TYPE_NEWDECIMAL	=> true,
		default					=> false,
	};
}

function fix_field(stdClass $field, mixed &$value): void {
	if ($value === null) {
		# NULL values are not converted, because they would be converted to false of 0
		return;
	}
	switch ($field->type) { # NOSONAR
	case MYSQLI_TYPE_BIT:
		if ($field->length === 1) {
			$value = (bool)$value;
		} elseif ($field->length <= 64) {
			$value = (int)$value;
		}
		break;

	case MYSQLI_TYPE_VAR_STRING:
		if ($field->length === 1) {
			# error_log("NOTICE converting varchar(1) to bool, \$field->name = $field->name, \$value = $value");
			if (($true = $field->name === "b'1'")
			||			 $field->name === "b'0'"
			) {
				$value = $true;
			} else {
		 		$value = ($value !== "\000");
			}
		}
		break;

	case MYSQLI_TYPE_NEWDECIMAL:
		$value = (float)$value;
		break;
	}
}

function db_fix_data_types(array $fields, array &$row, int $mode): void {
	# Since MYSQLI_OPT_INT_AND_FLOAT_NATIVE make sures ints and floats are used,
	# this function only 'fixes' bit(1) to bool
	if ($mode === MYSQLI_BOTH) {
		while (
			false !== ($numm = key($row)) && false !== next($row)
		&&	false !== ($asoc = key($row)) && false !== next($row)
		) {
			fix_field($fields[$numm], $row[$numm]);
			$row[$asoc] = $row[$numm];
		}
	} else {
		$i = 0;
		foreach ($row as &$value) {
			if (isset($fields[$i])) {
				# gracefully ignore non unique indices
				fix_field($fields[$i], $value);
			}
			++$i;
		}
	}
}

/**
 * @param string|array|null $tables			Specific tables used in the write query, so this function can determine which replicas to check.
 * @param int|null          $max_sleep		Don't wait longer than this.
 * @param float|null        &$slept			Filled with the time it took to wait.
 * @param array|null        &$ok_replicas	Contains all replicas that properly synchronized to the master.
 * @param array|null        &$bad_replicas	Contains all replicas that failed to synchronize to the master.
 * @param bool              $accept_failed_connections
 *                                          Accept failed_connections. The ok_replicas and bad_replicas are still properly filled,
 *                                          but when $accept_failed_connections is true, failed connections don't make the function return false.
 *
 * @return bool		If all replicas are caught up, this function returns true
 *                  If $accept_failed_connections is true, this function returns true even if some replicas failed to connect.
 *					If all replicas failed, this function al ways return false, even if $accept_failed_connections is true.
 *
 */

function waitforslaves(
	string|array|null $tables   				= null,
	?int			  $max_sleep 				= null,
	?float			 &$slept					= null,
	# $ok_replicas fill be filled with the replicas that properly synchronized
	# to the master.
	?array			 &$ok_replicas				= null,
	?array			 &$bad_replicas				= null,
	bool			 $accept_failed_connections	= true,
): bool {
	$max_sleep ??= -1;

	$start = microtime(true);

	# $flags = DB_OK_SLAVES_ONLY | DB_FORCE_SERVER;

	static $__slaves = null;
	if ($__slaves === null) {
		global $_db_tables, $_db_default_spec;
		foreach (($_db_tables + $_db_default_spec) as /* $table => */ $spec) {
			if (!is_array($spec)) {
				continue;
			}
			$master = $spec[0];
			foreach ((is_scalar($spec[1]) ? [$spec[1]] : $spec[1]) as $slave) {
				if ($slave === $master) {
					continue;
				}
				$__slaves[$master][$slave] = $slave;
			}
		}
	}
	if (!$tables) {
		$check_slaves = $__slaves;
	} else {
		$check_slaves = [];
		foreach ((is_array($tables) ? $tables : [$tables]) as $table) {
			if (!($spec = db_get_spec($table))) {
				continue;
			}
			$master = $spec[0];
			if (!isset($check_slaves[$master])) {
				if (!isset($__slaves[$master])) {
					# master $master has no slaves
					continue;
				}
				$check_slaves[$master] = $__slaves[$master];
			}
		}
	}
	$ok = true;
	$ok_replicas = [];
	$bad_replicas = [];
	foreach ($check_slaves as $master => $slavelist) {
		if (str_contains($master, 'sphinx')) {
			# sphinx daemon cannot be 'behind'
			continue;
		}
		if (!($masterhandle = db_get_connection($master))) {
			error_log("ERROR waitforslaves(): could not get connection to master $master");
			if (!$accept_failed_connections) {
				$ok = false;
			}
			continue;
		}
		if (!($status = db_get_master_status($masterhandle, $master))) {
			error_log("ERROR waitforslaves(): could not get master $master status");
			$ok = false;
			continue;
		}
		$bad_replicas = [];
		$ok_replicas = [];
		foreach ($slavelist as $slave) {
			if (!($slave_handle = db_get_connection($slave))) {
				error_log("ERROR waitforslaves(): could not get connection to replica $slave");
				$bad_replicas[] = $slave;
				if (!$accept_failed_connections) {
					$ok = false;
				}
				continue;
			}
			if (!($sres = db_query($slave_handle, SELECT, "
				SELECT MASTER_POS_WAIT(
					'$status[0]',
					$status[1],
					$max_sleep
				)", $slave))
			) {
				error_log('WARNING waitforslaves(): MASTER_POS_WAIT could not execute');
				$bad_replicas[] = $slave;
				$ok = false;
				continue;
			}
			$wait = mysqli_fetch_row($sres);
			mysqli_free_result($sres);
			if (!$wait
			||	$wait[0] === null
			||	$wait[0] === -1
			) {
				error_log("ERROR waitforslaves(): failed to wait for master on replica $slave. wait == ".var_get($wait));
				$bad_replicas[] = $slave;
				$ok = false;
				continue;
			}
			$ok_replicas[] = $slave;
		}
	}
	$slept = microtime(true) - $start;
	# Return false if $accept_failed_connections is true, but no replicas were OK at all
	if ($ok
	&&	$accept_failed_connections
	&&	!$ok_replicas
	) {
		return false;
	}
	return $ok;
}

function db_get_master_status(mysqli $handle, string $server_name): array|false {
	if (!($sres = db_query($handle, DB_SHOW, 'SHOW MASTER STATUS', $server_name))) {
		return false;
	}
	$status = mysqli_fetch_row($sres);
	mysqli_free_result($sres);
	if (!$status) {
		error_log("ERROR db_get_master_status(): failed to fetch $server_name master status");
		return false;
	}
	return $status;
}

function db_query(
	mysqli &$handle,
	int		$type,
	string  $qstr,
	string  $server_name,
	int		$flags		  = 0,
	?bool   &$reconnected = null
): bool|mysqli_result {
	$reconnected = false;
	$result_type = $flags & DB_STORE_RESULT ? MYSQLI_STORE_RESULT : MYSQLI_USE_RESULT;
	$res = false;

	try {
		global $__db_failed;
		$tries = 2;
		while ($tries--) {
			$__db_failed = false;
			try {
				$res = mysqli_query($handle, $qstr, $result_type);
			} catch (Exception $exception) {
				error_log('mysqli_query failed: '.$exception->getMessage());
				$res = false;
			}
			global $__db_errno, $__db_errstr;

			$__db_errno  = mysqli_errno($handle);
			$__db_errstr = mysqli_error($handle);

			if ($res) {
				global $__db_warning_count;
				if ($__db_warning_count = str_contains($server_name, 'sphinx') ? 0 : mysqli_warning_count($handle)) {
					global $__db_handle_for_warnings;
					$__db_handle_for_warnings = $handle;
				}
				break;
			}
			if (($flags & DB_IGNORE_TRUNCATED)
			&&	in_array($__db_errno, [
					MYSQL_ER_TRUNCATED_WRONG_VALUE_FOR_FIELD,
					MYSQL_ER_TRUNCATED_WRONG_VALUE,
					MYSQL_WARN_DATA_TRUNCATED,
				], true)
			) {
				return false;
			}
			db_log_message(LOGERROR, $type, $server_name, $qstr, $handle, $flags);

			if (in_array($__db_errno, [
					false,
					MYSQL_CR_SERVER_LOST,
					MYSQL_CR_GONE_AWAY,
				], true)
			) {
				$reconnected = true;
				$new_handle  = db_connect($server_name);

				error_log(
					'NOTICE reconnection (@ '.(time() - CURRENTSTAMP).' seconds => '.($new_handle ? 'OK!' : 'FAILED!').") due to errno $__db_errno to lost server $server_name".
					(empty($_SERVER['REQUEST_URI']) ? null : ' @ '.$_SERVER['REQUEST_URI'])
				);
				if (!$new_handle) {
					$handle = null;
					break;
				}
				$handle = $new_handle;
				continue;
			}
			$__db_failed = true;
			break;
		}
	} catch (Exception $exception) {
		error_log("ERROR db_query() on $server_name failed, ".$exception->getCode().': '.$exception->getMessage());
		$res = false;
	}
	return $res;
}

function get_server_name_from_handle(mysqli $handle): ?string {
	global $_db_servers;
	foreach ($_db_servers as $server_name => $ignored /* $server */) {
		global $$server_name;
		if ($handle === $$server_name) {
			return $server_name;
		}
	}
	return null;
}

function get_full_set_from_enum_spec(string $table, string $enum_field): string|false {
	if (!($spec = explain_table($table))) {
		return false;
	}
	if (empty($spec[$enum_field])) {
		error_log("ERROR get_full_set_from_enum_spec($table, $enum_field): field does not exist");
		return false;
	}
	if (!preg_match('"^enum(\(.*\))$"', $spec[$enum_field]->type ?? '', $match)) {
		error_log("ERROR get_full_set_from_enum_spec($table, $enum_field): unexpected type: ".get_r(getifset($spec[$enum_field], 'type')));
		return false;
	}
	return $match[1];
}

function db_write_in_parts(
	callable	 $write_function,
	string|array $tables,
	string		 $qstr,
	int		 	 $part_size = 100,
	?array		 $values	= null,
): bool {
	global $__db_affected;
	$total_affected = 0;
	if ($values) {
		$slices = ceil(count($values) / $part_size);
		for ($i = 0; $i < $slices; ++$i) {
			$slice = array_slice($values, $i * $part_size, $part_size);
			if (!$write_function($tables, $qstr.' VALUES '.implode(', ', $slice))) {
				return false;
			}
			$total_affected += db_affected();
		}
		$__db_affected = $total_affected;
		return true;
	}
	# use this function split huge DELETE query in parts;, with
	# some waiting in between queries, so we don't block the server too long
	while (
		($ok = $write_function($tables, $qstr.' LIMIT '.$part_size))
	&&	($affected = db_affected())
	) {
		$total_affected += $affected;
		if ($affected < $part_size) {
			break;
		}
		# Don't fail on waitforslaves, we want to write all changes to master
		# to make this function seem more like a single action.
		waitforslaves($tables);
	}
	$__db_affected = $total_affected;
	return $ok;
}

// function db_insert_in_parts(string|array $tables, string $qstr, array $values, int $part_size = 100): bool {
// 	return db_write_in_parts('db_insert', $tables, $qstr, $part_size, $values);
// }

function db_replace_in_parts(string|array $tables, string $qstr, array $values, int $part_size = 100): bool {
	return db_write_in_parts('db_replace', $tables, $qstr, $part_size, $values);
}

function db_delete_in_parts(string|array $tables, string $qstr, int $part_size = 100): bool {
	return db_write_in_parts('db_delete', $tables, $qstr, $part_size);
}

function db_update_in_parts(string|array $tables, string $qstr, int $part_size = 100): bool {
	return db_write_in_parts('db_update', $tables, $qstr, $part_size);
}

function shorten_query_for_log(string $qstr): string {
	# NOTE: Only replace binary fields that can be overly long and have nasty chars in them.
	#		Previously used: (?:BODY|(?:POST)?DATA(?:2x)?|IPBIN|JSON|OUTPUT|QSTR|TEXT|NOTE)
	return preg_replace_callback(
			'!(?<field>(?:(?:POST)?DATA(?:2x)?|IPBIN)\s*=\s*)"(?<data>[^"\\\\]*(?:\\\\.[^"\\\\]*)*)"!u',
			static fn (array $match): string => $match['field'].'"<'.strlen($match['data']).' bytes of data>"',
			$qstr
	) ?: $qstr;
}

// function db_table_update_time(string $database, string $table): int|false|null {
// 	return db_single_int($table, "
// 		SELECT UNIX_TIMESTAMP(UPDATE_TIME)
// 		FROM information_schema.TABLES
// 		WHERE TABLE_SCHEMA = '$database'
// 		  AND TABLE_NAME   = '$table'"
// 	);
// }

// function db_configuration_problem(string $error_for_mail): false {
// 	register_error('db_configuration_problem');
// 	mail_log($error_for_mail);
// 	return false;
// }
