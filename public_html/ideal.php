<?php

define('CURRENTSTAMP',time());
define('DAYNUMOFFSET',693961);

require_once '_exit_if_readonly.inc';
require_once '_currentuser.inc';
require_once '_hosts.inc';

function set_redirect($redirect = null) {
	static $__redir = null;
	if ($redirect !== null) {
		$__redir = $redirect;
	} else {
		return $__redir;
	}
}

function bail(int $errno, ?string $url = null) {
	global $transaction;

	if ($errno !== 200) {
		if (!have_messages()) {
			register_error('error:generic_LINE');
		}
	}
	store_messages_in_cookie();
	if ($redir = $url ?: ($transaction && $transaction->do_redirect() ? set_redirect() : null)) {
		header('Location: '.($redir[0] === '/' ? 'https://'.$_SERVER['HTTP_HOST'].$redir : $redir));
	} else {
		http_response_code($errno);
	}
	exit;
}

require_once '_nocache.inc';
send_no_cache_headers();

set_redirect('/error');

require_once '_require.inc';
if (!($action = require_element($_SERVER, 'eACTION', ['start', 'update', 'return']))
) {
	bail(400);
}

if ($idealid = have_idnumber($_SERVER,'eIDEALID')) {
	if (!($psp = db_single('ideal_transaction','SELECT PSP FROM ideal_transaction WHERE IDEALID = '.$idealid, DB_FORCE_SERVER))) {
		bail($psp === false ? 500 : 404);
	}
} elseif (!($psp = require_element($_POST, 'PSP', ['mollie']))) {
	error_log($_SERVER['REMOTE_ADDR'].' WARNING no eIDEALID and no PSP form input');
	bail(400);
}

require_once '_'.$psp.'.inc';

switch ($psp) {
case 'mollie':
	$transaction = new mollie_transaction;
	break;
}

$transaction->action($action);

switch ($action) {
case 'start':
	$transaction->validate_form();

	$transaction->create();

	$transaction->start();

	bail(200);

case 'return':
	$transaction->load();

	$transaction->validate_return();

	$transaction->update();

	$transaction->status_notification();

	bail(200);

case 'update':
	$transaction->load();

	$transaction->validate_update();

	$transaction->update();
	break;
}
