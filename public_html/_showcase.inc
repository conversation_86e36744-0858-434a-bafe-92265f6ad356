<?php

declare(strict_types=1);

function show_presence_showcase(
	?string	$element	 = null,
	?int 	$id			 = null,
	?string	$color		 = null,
	bool	$hide_videos = false,
): void {
	$element ??= $_REQUEST['sELEMENT'];
	$id		 ??= $_REQUEST['sID'];
	have_presence($element, $id, $presence_infos);
	$parts = [];
	$h = 400;
	$or_part = '0';

	if ($element === 'organization'
	&&	($partyidstr = memcached_single('connect', "
		SELECT GROUP_CONCAT(ASSOCID)
		FROM connect
		WHERE MAINTYPE	= 'organization'
		  AND MAINID	= $id
		  AND ASSOCTYPE = 'party'"))
	) {
		$or_part = "MAINTYPE = 'party' AND MAINID IN ($partyidstr) ";
	}
	if ($hide_videos) {
		$videoids = null;
	} else {
		$videoids = memcached_simpler_array(['video', 'connect', 'videochannel'], "
			SELECT EXTERNALID
			FROM video
			JOIN connect ON ASSOCID = VIDEOID
			LEFT JOIN videochannel USING (CHANNELID)
			WHERE video.TYPE = 'youtube'
			  AND (		MAINTYPE IN (".($element === 'artist' ? "'artist', 'remixer', 'producer'" : "'$element'").")
				  AND	MAINID = $id
				  OR	$or_part)
			  AND ASSOCTYPE = 'video'
			  AND CONTENTTYPE != 'music'
			  AND STATUS = 'active'
			  AND (videochannel.CHANNELID IS NULL OR DISABLED = 0)
			ORDER BY PSTAMP DESC
			LIMIT 20"
		);
	}
	if (isset($presence_infos['soundcloud'])
	&&	($info = reset($presence_infos['soundcloud']))
	&&	!$info['OFFLINE']
	&&	($soundcloud_url = $info['SITE'])
	&&	preg_match('"soundcloud\.com/[^/]+(?:/[^/]+)?$"', $info['SITE'])
	) {
		ob_start();
		?><div class="center" data-partyflock-embed="soundcloud"><?
		?><iframe<?
		if (!LITE) {
			?> class="light8"<?
		}
		/*if (!cookie_consented()) {
			# no allow-same-origin, so no cookies allowed
			?> sandbox="allow-scripts"<?
		}*/
		?> loading="lazy"<?
		?> width="100%"<?
		?> height="<?= $h ?>"<?
		/*?>%LIMITWIDTH%<?*/
		?> src="https://w.soundcloud.com/player/<?
		?>?url=<?= urlencode($soundcloud_url)
		?>&amp;show_artwork=<?= SMALL_SCREEN ? 'false' : 'true'
		?>&amp;sharing=false<?
		?>&amp;buying=false<?
		?>&amp;liking=false<?
		?>&amp;show_bpm=false<?
		?>&amp;show_playcount=false<?
		if ($element === 'artist'
		&&	in_array($id, [7981, 73600], strict: true)
		) {
			?>&amp;show_reposts=true<?
		}
		?>"></iframe><?
		?></div><?
		$part = ob_get_clean();
		ob_start();
		show_presence_icon('soundcloud');
		$parts[] = [ob_get_clean().' '.Eelement_name('recent_music'), null, $part];
	}

	if (isset($presence_infos['mixcloud'])
	&&	($info = reset($presence_infos['mixcloud']))
	&&	!$info['OFFLINE']
	&&	preg_match('"mixcloud\.com/(?P<user>[^/]+)/$"', $info['SITE'], $match)
	) {
		ob_start();
		?><div class="center" data-partyflock-embed="mixcloud"><?
		?><iframe<?
		if (!LITE) {
			?> class="light8"<?
		}
		/*if (!cookie_consented()) {
			# no allow-same-origin, so no cookies allowed
			?> sandbox="allow-scripts"<?
		}*/
		?> loading="lazy"<?
		?> width="100%"<?
		?> height="180"<?
		/*?>%LIMITWIDTH%<?*/
		?> src="https://www.mixcloud.com/widget/iframe/?<?
			?>feed=<?= urlencode('https://www.mixcloud.com/'.$match['user'].'/')
			?>&embed_type=widget_standard<?
			?>&hide_cover=1<?
			?>&hide_tracklist=1<?
			?>&light=<?= CURRENTTHEME === 'light' ? '1' : '0'
		?>"></iframe><?
		?></div><?
		$part = ob_get_clean();
		ob_start();
		show_presence_icon('mixcloud');
		$parts[] = [ob_get_clean().' '.Eelement_name('recent_music'), null, $part];
	}

	if (isset($presence_infos['spotify'])) {
		$spotifies = [];
	 	foreach ($presence_infos['spotify'] as $site => $info) {
	 		if (!$info['OFFLINE']
			&&	preg_match('"spotify\.com/(?<type>[a-z]+)/"', $site, $match)
			) {
	 			$spotifies[$match['type']][] = $info;
			}
	 	}
	 	# non-embeddable:
	 	unset($spotifies['user']);
		if ($spotifies) {
			foreach ($spotifies as /* $type => */ $spotify_infos) {
				foreach ($spotify_infos as $spotify) {
					if (preg_match('"spotify\.com/(\w+)/(.*)(?:\?|\b)"i', $spotify['SITE'], $match)
					&&	(	$match[1] !== 'user'
						||	str_contains($spotify['SITE'], 'playlist')
						#	^^ can only show playlist, no encompassing user profile
						)
					) {
						[, $type, $spotify_id] = $match;
						if (preg_match('"/(playlist)/(.*)$"', $spotify_id, $match)) {
							# may happen: https://open.spotify.com/user/32tpuql007du2ef36c7llx3ac/playlist/2eR42q8qoct7pkupKsbwNj
							[, $type, $spotify_id] = $match;
						}
						ob_start();
						?><iframe<?
						?> loading="lazy"<?
						?> style="width: 100%; height: <?= $h ?>px;"<?
						?> src="https://embed.spotify.com/?uri=spotify:<?= $type ?>:<?= urlencode($spotify_id) ?>"<?
						?>></iframe><?
						$part = ob_get_clean();
						ob_start();
						show_presence_icon('spotify');
						$parts[] = [ob_get_clean().' '.Eelement_name('music'), null, $part];
					}
				}
			}
		}
	}

	$chrome_workaround = !HOME_THOMAS && CHROME && !SMALL_SCREEN;

	$all_videos_link = "<a href=\"/video/$element/$id\">".element_name('overview_of_all_videos').'</a>';

	if (isset($presence_infos['youtube'])
	&& ($info = reset($presence_infos['youtube']))
	&&	!$info['OFFLINE']
	&&	($youtube_info = db_single_array('youtube_info', "
		SELECT TYPE, CHANNEL, UID
		FROM youtube_info
		WHERE OK
		  AND PRESENCEID = {$info['PRESENCEID']}"))
	) {
		$videoids = null;
		[$type, $channel, $uplist] = $youtube_info;

		if(	!$uplist
		&&	str_starts_with($channel, 'UC')
		) {
			$uplist = $channel;
		}
		# somehow YoutuBe wants UU instead of UC
		$uplist[1] = 'U';

		ob_start();
		ob_start();
		if ($uplist) {
			?>https://www.youtube.com/embed/videoseries?<?
			?>list=<? echo escape_specials($uplist);
		} else {
			?>https://www.youtube.com/embed?<?
			?>listType=<?=  $type === 'user' ? 'user_uploads' : 'playlist'
			?>&amp;list=<?
			echo escape_specials(
					str_starts_with($uplist, 'UC')
				?	substr($channel, 2)
				:	$channel);
		}
		if ($chrome_workaround) {
			?>&amp;enablejs=1<?
		}
		show_aspect_embed(ob_get_clean(), id: 'showcase-video');
		$part = ob_get_clean();

		ob_start();
		show_presence_icon('youtube');
		if ($chrome_workaround) {
			require_once '_video_workaround.inc';
			show_chrome_video_workaround('showcase-video');
		}
		$parts[] = [ob_get_clean().' '.Eelement_plural_name('recent_video'), $all_videos_link, $part];
	}
	if ($videoids) {
		# Reload to work around chrome bug:
		# https://issuetracker.google.com/issues/249707272
		# When bug is fixed, remove code below and remove enablejsapi=1 from iframe src.
		# Strange thing is, reload seems to not be triggered. Maybe connecting the player to the
		# iframe this way, also is a workaround.
		$total = count($videoids);
		$params = [];
		if ($chrome_workaround) {
			$params[] = 'enablejsapi=1';
		}
		if ($total > 1) {
			$params[] = 'showinfo=1';
			$params[] = 'playlist='.implode(',', $videoids);
		}
		ob_start();
		show_aspect_embed(
			'https://www.youtube.com/embed/'.escape_ascii($videoids[0]).'?'.implode('&amp;', $params),
			id: 'showcase-video'
		);
		$part = ob_get_clean();
		ob_start();
		show_presence_icon('youtube');
		if ($chrome_workaround) {
			require_once '_video_workaround.inc';
			show_chrome_video_workaround('showcase-video');
		}
		$parts[] = [ob_get_clean().' '.Eelement_name('recent_video', $total), $all_videos_link, $part];
	}
	if ($parts) {
		$parts_count = $total_parts = count($parts);
		layout_open_box($color ?: $element);
		foreach ($parts as $info) {
			[$header, $header_right, $part] = $info;
			layout_box_header("<h2>$header</h2>", $header_right);
			echo str_replace(
				'%LIMITWIDTH%',
				$total_parts > 1 ? '' : ' style="max-width:'.(16 * $h / 9).'px"',
				$part
			);
			if (--$parts_count) {
				layout_continue_box();
			}
		}
		layout_close_box();
	}
}
