<?php

function _joblist_columns() {
	return 'JOBID,FUNCTION,COMPANY,job.RELATIONID,job.INVOICENR,STARTSTAMP,STOPSTAMP,ACTIVE,SENTSTAMP,job.REQUEST,COSTSENT,job.DISCOUNT,PRICEID,job.REMOVED';
}
function _joblist_display_rowlist($rowlist,$showdates = false,$showinfocus = false) {
	$jobadmin = have_admin('job');
	$hideremoved = $_REQUEST['sELEMENT'] != 'relation' || !$jobadmin;
	if (!$jobadmin
	&&	$showinfocus
	) {
		foreach ($rowlist as $job) {
			$jobids[] = $job['JOBID'];
		}
		$foci = memcached_multirowuse_hash(
			'job_focus','
			SELECT JOBID,TYPE,ID,OPTVAL
			FROM job_focus
			WHERE JOBID IN ('.implode(',',$jobids).')'
		);
		if ($foci
		&&	(	have_user()
			&&	!$jobadmin
			)
		) {
			require_once '_jobfocus.inc';
			$city = get_current_city();
			if ($city) {
				$cityid = $city['CITYID'];
				$countryid = $city['COUNTRYID'];
				$provid = $city['PROVINCEID'];
			} else {
				$cityid = $countryid = $provid = 0;
			}
		}
	}
	$doinvoice = array_key_exists('INVOICENR',$rowlist[0]);
	layout_start_header_row();
	layout_start_header_cell();
	if ($doinvoice) {
		layout_next_header_cell();
	}
	echo Eelement_name('function');
	if (!$jobadmin) {
		layout_next_header_cell();
		echo Eelement_name('location');
	}
	layout_next_header_cell();
	echo Eelement_name('company');
	if ($jobadmin) {
		if (isset($rowlist[0]['RELATIONID'])) {
			layout_next_header_cell();
			echo Eelement_name('relation');
		}
	}
	if ($jobadmin || $showdates) {
		layout_next_header_cell_right(CELL_RIGHT_SPACE);
		echo __C('field:start');
		layout_next_header_cell_right();
		echo __C('field:duration_char');
	}
	layout_stop_header_cell();
	layout_stop_header_row();
	foreach ($rowlist as $job) {
		if ($hideremoved
		&&	!empty($job['REMOVED'])
		) {
			continue;
		}
		if ($jobadmin) {
			$jobclasses = jobrow_get_admin_classes($job);
		} elseif (
			$showinfocus
		&&	isset($cityid)
		&&	isset($foci[$job['JOBID']])
		&&	user_in_job_focus($foci[$job['JOBID']],$cityid,$countryid,$provid)
		) {
			$jobclasses = 'infocus';
		} else {
			$jobclasses = null;
		}
		layout_start_rrow($doinvoice ? CELL_ALIGN_RIGHT | CELL_LIGHT : 0, $jobclasses);
		if ($doinvoice) {
			if ($job['INVOICENR']) {
				?><a href="<?= get_element_href('job',$job['JOBID'],$job['FUNCTION']); ?>"><?= $job['INVOICENR'];
				?></a>&nbsp;&middot;&nbsp;<?
			}
			layout_next_cell();
		}
		# FUNCTION
		echo get_element_link('job', $job['JOBID'], $job['FUNCTION']);
		if (!$jobadmin) {
			layout_next_cell();
			if (isset($foci[$job['JOBID']])) {
				echo job_focus_single_line($job['JOBID'],false,$foci[$job['JOBID']],'<br />');
			}
		}		
		layout_next_cell();
		# COMPANY
		$len = strlen($job['COMPANY']);
		if ($len > 40) {
			?><span title="<?= escape_specials($job['COMPANY']) ?>"><?= escape_specials(substr($job['COMPANY'],0,27)) ?>&hellip;</span><?
		} else {
			echo escape_specials($job['COMPANY']);
		}
		if ($jobadmin
		&&	isset($job['RELATIONID'])
		) {
			layout_next_cell();
			echo get_element_link('relation', $job['RELATIONID']);
		}
		// STARTSTAMP
		layout_next_cell(class: 'rpad right');
		if ($jobadmin
		&&	!$job['ACTIVE']
		&&	(	!empty($job['REQUEST'])
			||	isset($job['RECHECK'])
			)
		&&	$job['STARTSTAMP'] < CURRENTSTAMP+30*3600
		) {
			if ($job['STARTSTAMP'] <= TODAYSTAMP) {
				?><span class="error"><?
				if ($job['STARTSTAMP'] < TODAYSTAMP) {
					$days = floor((TODAYSTAMP - $job['STARTSTAMP'])/ONE_DAY);
					echo __('date:days_past',array('DAYS'=>$days));
/*					if ($days > 1) {
						?> dagen gelden<?
					} else {
						?> dag geleden<?
					}*/
				} else {
					echo __('date:today');
				}
				?></span><?
			} else {
				?><span class="selected warning"><?= __('date:tomorrow'); ?>!</span><?
			}
		} elseif ($showdates) {
			_date_display($job['STARTSTAMP'],'&nbsp;');
		}
		if ($showdates) {
			layout_next_cell(class: 'right');
			echo round(($job['STOPSTAMP'] - $job['STARTSTAMP']) / 24 / 3600 / 7);
		}
		layout_stop_row();
	}
}
function jobrow_get_admin_classes($job) {
	if ($job['ACTIVE']) {
		$classes[] = 'active';
	} elseif (!$job['COSTSENT']) {
		$classes[] = 'light';
	}
	if (CURRENTSTAMP >= $job['STARTSTAMP']
	&&	CURRENTSTAMP <  $job['STOPSTAMP']
	) {
		$classes[] = 'running';
	}
	return isset($classes) ? $classes : null;
} 
