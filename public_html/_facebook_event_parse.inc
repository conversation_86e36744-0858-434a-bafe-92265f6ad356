<?php

declare(strict_types=1);

function __debug(string $str): void {
	if (!function_exists('debug')) {
		return;
	}
	debug('event: '.$str);
}

require_once '_find_ticket_uri.inc';
require_once '_facebook_parse.inc';
require_once '_url.inc';

function parse_facebook_event(string $dom_str, int $partyid = 0): array|false {
	$doc = new DOMDocument();
	$dom_str = prepare_dom_str($dom_str);
	# file_put_contents($output_file = '/tmpdisk/event_dom_'.CURRENTUSERID.'_.'.$partyid.'_'.CURRENTSTAMP.'.html', $dom_str);
	__debug(
		'start parsing DOM for user '.CURRENTUSERID.', '.memcached_nick(CURRENTUSERID).
		' for '.($partyid ? 'event '.$partyid.', '.get_element_title('party', $partyid) : 'new event')
		# ', file: '.$output_file
	);
	set_libxml_error_handler();
	$doc->loadHTML($dom_str, LIBXML_COMPACT);
	restore_error_handler();
	$xpath = new DOMXpath($doc);
	$xml = $doc->saveXML();
	return parse_new_facebook_event($doc, $xpath, $xml, $partyid);
}

function parse_new_facebook_event(
	DOMDocument	$doc,
	DOMXPath	$xpath,
	string		$xml,
	int			$partyid,
): array|false {
	__debug('parse new facebook');

	$event = [
		'version'	=> 1
	];

	if (preg_match('"events/(\d+)/\?active_tab=(?:going|about|discussion)"ui', $xml, $match)) {
		$fbid = (int)$match[1];

		__debug('found FBID '.$fbid);

		if (preg_match('"\bhttps://www\.facebook\.com/events/(\d+)/\?event_time_id='.$fbid.'\b"uis', $xml, $match)
		||	preg_match('"\bhttps://www\.facebook\.com/events/(\d+)/'.$fbid.'\b"uis', $xml, $match)
		) {
			$event['event_time_id'] = $fbid;
			$fbid = (int)$match[1];

			__debug('found FBID for multi '.$fbid);
		}
		$event['fbid'] = $fbid;
	} else {
		_error('could not find FBID');
		return false;
	}

	$imgs = $xpath->query('//img[@data-imgperflogname="profileCoverPhoto"]');
	if ($imgs?->length) {
		foreach ($imgs as $img) {
			$event['image_src'] = $img->getAttribute('src');
		}
	}

	$details = null;
	$event_type = null;
	$headers = $xpath->query('//h2|//h1');

	if ($headers?->length) {
		[$current_year] = _getdate();

		foreach ($headers as $header) {
			$header_value = $header->textContent;

			if (in_array($header_value, [
				'Aanbevolen evenementen',
				'Delen in groepen',
				'Evenementen',
				'Maak kennis met de organisatoren',
				'Met vrienden gaan',
				'Populair bij vrienden',
			], strict: true)) {
				continue;
			}

			__debug('encountered '.$header->nodeName.' '.$header_value);

			if ($header->nodeName === 'h1') {
				if (!empty($event['fbtitle'])) {
					continue;
				}
				$event['fbtitle'] = $header_value;
				__debug('found title: '.$header_value);

				$event_type = $header->parentNode->nextSibling;

				$header_value = $header->parentNode->parentNode->firstChild->textContent;
			}

			$tz = null;
			if (preg_match('"\b(?<tz>UTC[+\-]\d+|CES?T)\b"u', $header_value, $tz_match)) {
				__debug('found timezone: '.$tz_match['tz']);
				$tz = $tz_match['tz'];
			}

			if (preg_match(
				'"(?<start_day>\d+)\s+(?<start_monthname>[a-z]+)\.?\s+(?<start_year>\d{4})?\s*om\s+(?<start_hour>\d+):(?<start_mins>\d+)[\s\-]+'.
				'(?<end_day>\d+)\s+(?<end_monthname>[a-z]+)\.?\s+(?<end_year>\d{4})?\s*om\s+(?<end_hour>\d+):(?<end_mins>\d+)"iu',
				$header_value,$date_match
			)) {
				__debug('found date (1): '.$date_match[0]);

				$event['start_date'] = [
					'year'  => $start_year = (int)$date_match['start_year'] ?: $current_year,
					'month' => $start_month = (int)get_month_from_string($date_match['start_monthname']),
					'day'   => $start_day = (int)$date_match['start_day'],
					'hour'  => $start_hour = (int)$date_match['start_hour'],
					'mins'  => $start_mins = (int)$date_match['start_mins'],
				];

				if ($tz) {
					$event['start_date']['tz'] = $tz;
				}

				$start_stamp = mktime(
					$start_hour,
					$start_mins,
					0,
					$start_month,
					$start_day,
					$start_year
				);

				$end_stamp = mktime(
					hour: (int)$date_match['end_hour'],
					minute: (int)$date_match['end_mins'],
					second: 0,
					month: (int)get_month_from_string($date_match['end_monthname']),
					day: (int)$date_match['end_day'],
					year: (int)$date_match['end_year'] ?: $date_match['start_year'] ?: $current_year,
				);

				$event += split_duration($end_stamp - $start_stamp);
				continue;
			}
			if (preg_match(
					'"(?<start_day_name>\w+)\s*(?<start_day>\d+)\s*(?<start_monthname>\w+)\s*(?<start_year>\d{4})?\s*(?:van|om)\s*'.
					 '(?<start_hour>\d+):(?<start_mins>\d+)(?:[\s*\-]+(?<end_hour>\d+):(?<end_mins>\d+))?"iu',
				$header_value,$date_match
			)) {
				__debug('found date (2): '.$date_match[0]);

				if ($date_match['start_mins'] >= 55) {
					++$date_match['start_hour'];
					$date_match['start_mins'] = 0;
				}
				$event['start_date'] = [
					'year'  => (int)$date_match['start_year'] ?: $current_year,
					'month' => (int)get_month_from_string($date_match['start_monthname']),
					'day'   => (int)$date_match['start_day'],
					'hour'  => (int)$date_match['start_hour'],
					'mins'  => (int)$date_match['start_mins'],
				];
				if ($tz) {
					$event['start_date']['tz'] = $tz;
				}
				if (isset($date_match['end_hour'])) {
					$event += get_duration_from_match($date_match);
				}
				continue;
			}
			if (preg_match(
					'"(?<start_day_name>'.implode('|',get_day_names()).')\s*van\s*'.
					'(?<start_hour>\d+):(?<start_mins>\d+)[\s*-]+(?<end_hour>\d+):(?<end_mins>\d+)"iu',
					$header_value,
					$date_match
			)) {
				__debug('found date (3): '.$date_match[0]);

				$weekday = get_weekday_for_day_name($date_match['start_day_name']);

				$day_name = daynum_to_day_name($weekday);

				$date = strtotime("next $day_name");

				# FIXME: this is not exact, we're getting date info in current timezone, but the event might be in another

				[$start_year, $start_month, $start_day] = _getdate($date);

				$event['start_date'] = [
					'year'  => $start_year,
					'month' => $start_month,
					'day'   => $start_day,
					'hour'  => (int)$date_match['start_hour'],
					'mins'  => (int)$date_match['start_mins'],
				];

				if ($tz) {
					$event['start_date']['tz'] = $tz;
				}

				$event += get_duration_from_match($date_match);

				continue;
			}
			if (preg_match(
				'"(?<start_day_name>gisteren|vandaag|morgen|'.implode('|', get_day_names()).')'.
				'\s+om\s+(?<start_hour>\d+):(?<start_mins>\d+)"iu',
				$header_value,
				$date_match
			)) {
				__debug('found date: '.$date_match[0]);

				$day_name = match(mb_strtolower($date_match['start_day_name'])) {
					'gisteren'	=> 'yesterday',
					'vandaag'	=> 'today',
					'morgen'	=> 'tomorrow',
					default		=> daynum_to_day_name(get_weekday_for_day_name($date_match['start_day_name']))
				};

				$start_stamp = strtotime("$day_name {$date_match['start_hour']}:{$date_match['start_mins']}");

				[$start_year, $start_month, $start_day, $start_hour, $start_mins]  = _getdate($start_stamp);

				$event['start_date'] = [
					'year'  => $start_year,
					'month' => $start_month,
					'day'   => $start_day,
					'hour'  => $start_hour,
					'mins'  => $start_mins,
				];

				continue;
			}

			if (preg_match(
				'"(?<start_day_name>vandaag|morgen|gisteren)'.
				'\s+van\s+(?<start_hour>\d+):(?<start_mins>\d+)'.
				'\s+-\s+(?<end_hour>\d+):(?<end_mins>\d+)"iu',
				$header_value,
				$date_match
			)) {
				__debug('found date: '.$date_match[0]);

				$day_name = match(mb_strtolower($date_match['start_day_name'])) {
					'gisteren'	=> 'yesterday',
					'vandaag'	=> 'today',
					'morgen'	=> 'tomorrow',
				};

				$start_stamp = strtotime($day_name.' '.$date_match['start_hour'].':'.$date_match['start_mins']);

				[$start_year, $start_month, $start_day, $start_hour, $start_mins] = _getdate($start_stamp);

				$event['start_date'] = [
					'year'  => $start_year,
					'month' => $start_month,
					'day'   => $start_day,
					'hour'  => $start_hour,
					'mins'  => $start_mins,
				];

				if ($tz) {
					$event['start_date']['tz'] = $tz;
				}
				$event += get_duration_from_match($date_match);

				continue;
			}

			if ($header_value === 'Details') {
				__debug('found Details section');
				# grab container
				$details = $header->parentNode->parentNode->parentNode->parentNode;
				$row = $details->firstChild;
				$duration = 0;
				do {
					/** @noinspection BypassedUrlValidationInspection */
					if (preg_match('"Duur: (?<spec>.*)\.?"iu', $row->textContent, $duration_match)) {
						__debug('found duration: '.$duration_match['spec']);
						if (preg_match_all('"(?<value>\d+)\s*(?<unit>uur|uren|minuten|mins?)"u', $duration_match['spec'], $spec_parts)) {
							foreach ($spec_parts['value'] as $ndx => $value) {
								$unit = $spec_parts['unit'][$ndx];
								__debug("found $value for unit $unit");

								switch ($unit) {
								case 'uur':
								case 'uren':
									$duration += ONE_HOUR * $value;
									break;
								case 'min':
								case 'mins':
								case 'minuten':
									$duration += ONE_MINUTE * $value;
									break;
								}
							}
						}
					}
				}
				while ($row = $row->nextSibling);

				if ($duration) {
					$event += split_duration($duration);
				}

				// last is the description

				$desc_part = $details->lastChild;
				$desc_text = $desc_part->textContent;

				if (str_contains($desc_text, 'Meer weergeven')) {
					_error('Klik omschrijving open!');
					return false;
				}

				$anchors = $xpath->query('.//a', $desc_part);
				foreach ($anchors as $anchor) {
					$href = $anchor->getAttribute('href');
					if (str_contains($anchor->textContent, '...')
					&&	(	preg_match('"l\.facebook\.com/l\.php\?u=(.*?)%3Ffbclid"u', $href, $match)
						||	preg_match('"^(.*?)\?fbclid="u', $href, $match)
						)
					) {
						$anchor->textContent = urldecode($match[1]);
					}
				}

				$event['fbdescription'] = $desc_text;

				$desc_xml = $doc->saveXML($desc_part);

				if (preg_match_all('!href="(https://www\.facebook\.com/.*?)"!ui', $desc_xml, $matches)) {
					$links = [];
					foreach ($matches[1] as $link) {
						# skip hashtags and event links, only keep org/loc/artist links
						if (str_starts_with($link, 'https://www.facebook.com/hashtag/')
						||	str_starts_with($link, 'https://www.facebook.com/events/')
						) {
							continue;
						}
						$link = preg_replace('"\?(.*)$"u', '', $link);
						$links[] = $link;
					}
					if ($links) {
						$event['description_links'] = $links;
					}
				}

				# get only immediate descendants, because divs may appear inside description blocks
				$desc_blocks = $xpath->query('./div',$desc_part->firstChild->firstChild);
				if (!$desc_blocks->length) {
					continue;
				}
				$desc_texts = [];
				foreach ($desc_blocks as $desc_block) {
					$text = $desc_block->textContent;

					switch (strtolower($text)) {
					case 'minder weergeven':
						# skip all remainder
						break 2;
					case 'dans':
					case 'feest':
					case 'kindvriendelijk':
					case 'meer weergeven':
					case 'muziek':
						continue 2;
					}
					$text = preg_replace('!Minder weergeven$!ui', '', $text);
					# replace non-breaking space with regular space making matching easier:
					$text = str_replace("\xC2\xA0", ' ', $text);
					# remove spaces before newlines making matching easier:
					# removing multiple space to make matching easier:
					$text = utf8_mytrim(
							preg_replace(
								['"\h+\n"u', '"\h+$"u', '"\s{2,}"u'],
								["\n", '', ' '],
							$text));
					if (!$text) {
						# use low ascii DLE to to postprocessing on empty lines
						$text = chr(0x10);
					}

					$desc_texts[] = $text;
				}

				$event['fbdescription'] =
					str_replace(
						"\n\n\x10",
						"\n",
						implode("\n\n", $desc_texts)
					);

				continue;
			}
			if ($header_value === 'Tickets') {
				__debug('found Tickets section');
				$link_part = $header->parentNode->parentNode->parentNode->nextSibling;
				__debug('link_part: '.$link_part->textContent);
				$anchors = $xpath->query('.//a', $link_part);
				if ($anchors?->length) {
					[$event['ticketlink']] = find_actual_ticket_url(cleanup_presale_url($anchors[0]->getAttribute('href')), utf8: true);
					__debug('ticket link: '.$event['ticketlink']);
				}
				continue;
			}
			if ($header_value === 'Gasten') {
				__debug('found Gasten section');
				/** @noinspection OneTimeUseVariablesInspection */
				$content_part = $header->parentNode->parentNode->parentNode->nextSibling;
				$going_str    = $content_part->textContent;
				$going_str    = str_replace("\xC2\xA0",'',$going_str);	# non breaking space
				__debug('going: '.$going_str);
				if (preg_match('"(?<going>[\w.,]*)(?:GEGAAN|GAAT)(?<interested>[\w.,]*)GE\xc3\x8fNTERESSEERD"u',$going_str,$going_match)) {
					$visitors = parse_fb_count($going_match['going']);
					$interested = parse_fb_count($going_match['interested']);
					__debug('detected visitors ('.$visitors.') and interested ('.$interested.')');
					if ($visitors !== false
					&&	$interested !== false
					) {
						db_insert('facebook_guests_log','
						INSERT INTO facebook_guests_log
						SELECT * FROM facebook_guests
						WHERE FBID='.$event['fbid']
						)
						&& db_insert('facebook_guests', '
						INSERT INTO facebook_guests SET
							FBID		='.$event['fbid'].',
							GOING		='.$visitors.',
							INTERESTED	='.$interested.',
							GI_STAMP	='.CURRENTSTAMP.'
						ON DUPLICATE KEY UPDATE
							GOING		=VALUES(GOING),
							INTERESTED	=VALUES(INTERESTED),
							GI_STAMP	=VALUES(GI_STAMP)'
						);
					} else {
						error_log('WARNING could not detect visitors ('.$match['going'].') and interested ('.$match['interested'].') in '.$going_str.' for event '.$event['fbid']);
					}
				}
			}
		}
	}


	if (empty($event['fbtitle'])) {
		_error('Geen titel gevonden!');
		return false;
	}

	$locationid = 0;
	$location_name = null;
	$organizationids = [];

	if ($details) {
		foreach ($xpath->query('.//i', $details) as $i) {
			if (!$i->getAttribute('data-visualcompletion')) {
				continue;
			}

			$content = $i->parentNode->parentNode->lastChild;

			__debug('got row: '.$content->nodeValue);

			# first try to match on content, because that's easier

			if (!isset($event['organization'])
			&&	str_contains($content->textContent, 'Evenement van')
			) {
				if (!($organizer_links = $xpath->query('.//a', $content))
				||	!$organizer_links->length) {
					break;
				}
				foreach ($organizer_links as $organizer_link) {
					$organizer_href = $organizer_link->getAttribute('href');
					$organizer_name = $organizer_link->textContent;

					$organization = ['name' => $organizer_name];

					__debug('found organizer name: '.$organizer_name);
					__debug('found organizer link: '.$organizer_href);

					if ($organization_fbids = get_fbids_for_href($organizer_href)) {
						__debug('found organizer fbids: '.implode(', ', $organization_fbids));
						$organization['fbids'] = $organization_fbids;
						if ($elements = get_elements('organization', $organization_fbids)) {
							$organization['elements'] = $elements;
							foreach ($elements as $element) {
								$organizationids[$organizationid = $element['id']] = $organizationid;
								__debug('matched partyflock organization '.$organizationid.': '.get_element_title('organization', $organizationid));
							}
						}
					}

					$event['organizations'][] = $organization;
				}

			} elseif (!isset($event['location'])) {
				$location_link = null;
				$deeper = $content;
				while ($deeper = $deeper->firstChild) {
					$location_link = $deeper;
				}
				# location appear as single anchor on a row, so must not have sibling:
				if ( $location_link?->textContent !== 'Tickets'
				&&	!$location_link->nextSibling
				&&	!$location_link->firstChild
				&&	!str_starts_with($location_link->textContent, 'Duur:')
				&&	!str_contains($location_link->textContent, ' gereageerd')
				&&	in_array($location_link->nodeName, ['a', '#text'], strict: true)

				) {
					$location_name = $location_link->textContent;
					__debug('found location name: '.$location_name);

					if (preg_match('"(?<address>[^,]+), (?<zipcode>\d+\s*\w{2}|\d+) (?<city>[^,]+), (?<country>\w+)"iu', $location_name, $match)) {
						$location_name = $match['address'];
					}

					$location = ['name' => $location_name];

					if ($location_link->nodeName === 'a'
					&&	($location_href = $location_link->getAttribute('href'))
					||	$location_link->parentNode->nodeName === 'a'
					&&	($location_href = $location_link->parentNode->getAttribute('href'))
					) {
						__debug('found location href: '.$location_href);

						$location_fbids = get_fbids_for_href($location_href);

						if ($location_fbids) {
							__debug('found location fbids: '.implode(', ', $location_fbids));
							$location['fbids'] = $location_fbids;
							if ($elements = get_elements('location', $location_fbids)) {
								$location['elements'] = $elements;
								foreach ($elements as $element) {
									$locationid = $element['id'];
									__debug('matched partyflock location '.$locationid.': '.get_element_title('location', $locationid));
								}
							}
						}
					}
					$event['location'] = $location;
				}
			}
		}
	}
	if (preg_match('"(?:^|\h+)ADE(?:\h+|$)"u',$event['fbtitle'])) {
		__debug('ADE match');
		$organizationids[ORGANIZATIONID_ADE] = ORGANIZATIONID_ADE;
		$event['organizations'][] = [
			'name'		=> 'Amsterdam Dance Event',
			'id'		=> ORGANIZATIONID_ADE,
			'fbids'		=> [$organization_fbid = 116433981841],
			'elements'	=> get_elements('organization', $organization_fbid)
		];
	}
	[$name, $subtitle, $edition] = utf8_parse_event_title($event['fbtitle'],[
		'partyid'			=> $partyid,
		'locationid'		=> $locationid,
		'location'			=> $location_name,
		'organizationids'	=> $organizationids,
	]);

	__debug('parse_event_title.result: name = '.$name.', subtitle = '.$subtitle.', edition = '.$edition);

	if ($edition) {
		$event['edition'] = (int)$edition;
	}
	$event['name'] = $name;
	if ($subtitle) {
		$event['subtitle'] = $subtitle;
	}

	$event['clean_title'] = get_clean_title_from_facebook($event['fbtitle'], $organizationids);

	$livestream =
		$event_type
	&&	in_array($event_type->textContent, [
			'Facebook Live',
			'Online evenement',
		], strict: true)
	||	preg_match('"live[\-\h]*stream"ui', $event['fbtitle']);


	if ($livestream) {
		$event['livestream'] = true;
		$event['livestream_address'] = 'https://www.facebook.com/events/'.$fbid;
		$event['free_entrance'] = true;
		$event['min_age'] = 0;
		__debug('livestream detected');
	}

	# fix events that start at x:59 to (x+1):00 and substract 1 minute from duration if it has a duration

	if (!empty($event['start_date']['hour'])
	&&	!empty($event['start_date']['mins'])
	&&	$event['start_date']['mins'] === 59
	) {
		++$event['start_date']['hour'];
		$event['start_date']['mins'] = 0;

		if (!empty($event['duration_mins'])
		&&	$event['duration_mins'] & 1
		) {
			$event['duration_mins'] &= ~1;
			$event['duration_secs'] = 0;
		}
	}

	if (!$partyid
	&&	!empty($event['fbid'])
	&&	!empty($event['start_date'])
	) {
		# find exact same event:

		$time_correction = '';
		if ($event['start_date']['hour'] === 24
		&&	$event['start_date']['mins'] === 0
		) {
			# 24:00 is not really a valid time
			$event['start_date']['hour'] = 23;
			$event['start_date']['mins'] = 59;
			$time_correction = '60';
		}

		if ($parties = db_single('party', "
			SELECT GROUP_CONCAT(PARTYID)
			FROM fbid
			JOIN party ON PARTYID = ID
			WHERE ELEMENT = 'party'
			  AND FBID = {$event['fbid']}
			  AND STAMP BETWEEN $time_correction + UNIX_TIMESTAMP('".(
			  $start_date =
						$event['start_date']['year'].
					'-'.$event['start_date']['month'].
					'-'.$event['start_date']['day'].
					' '.$event['start_date']['hour'].
					':'.$event['start_date']['mins'])."') - 4 * ".ONE_HOUR." AND UNIX_TIMESTAMP('$start_date') + 4 * ".ONE_HOUR.'
			ORDER BY PARTYID'
		)) {
			$event['seems'] = $parties;
			header('X-PF-SEEMS: '.$parties);
			$msg = __('party:notice:event_seems_to_have_been_processed_multiple_times_TEXT', DO_UBBFLAT, ['PARTYIDSTR' => $parties]);
			$msg = str_replace("\r\n", '\\r\\n', $msg);
			header('X-PF-SEEMS-NOTIFY: '.$msg);
			if (!str_contains($parties, ',')) {
				$seems_partyid = (int)$parties;
				$seems_name = get_element_title('party', $seems_partyid);
				$event['seems_name'] = $seems_name;
				header('X-PF-SEEMS-NAME: '.$seems_name);
				$header_seems = __('party:notice:event_seems_to_be_processed_already_TEXT', DO_UBBFLAT, ['PARTYID' => $seems_partyid]);
				$header_seems = str_replace("\r\n", '\\r\\n', $header_seems);
				header('X-PF-SEEMS-NAME-ASK: '.$header_seems);
			}
		} elseif ($parties === false) {
			mail_log('bad date?', item: $event);
		}
		if ($time_correction) {
			$event['start_date']['hour'] = 24;
			$event['start_date']['mins'] = 0;
		}
	}
	return $event;
}

function same_facebook_event(int $partyid, array &$obj, bool $utf8 = false): bool {
	$debug = HOME_THOMAS; //CURRENTUSERID == 2269;

	if (!$partyid
	||	!($prev_json = db_single('facebook_event','SELECT EVENTOBJ FROM facebook_event WHERE PARTYID='.$partyid))
	) {
		$debug && __debug('no prev');
		return false;
	}
	$prev = safe_json_decode($prev_json, true);
#	$curr = safe_json_decode(json_encode($obj));
	$curr = $obj;

	# only compare existence
	$prev_img = isset($prev['image_src']);
	$curr_img = isset($curr['image_src']);
	if ($prev_img !== $curr_img) {
		$debug && __debug('curr_img != prev_img');
		return false;
	}
	unset(
	# ignore image
		$prev['image_src'],
		$curr['image_src'],

	# unused:
		$prev['fbowners'],
		$curr['fbowners'],

	# these are generated and not stable
		$prev['name'],
		$curr['name'],
		$prev['clean_title'],
		$curr['clean_title'],

	# description_links are only used to prioritize artists when adding line-ups and are not that important and not very stable
		$prev['description_links'],
		$curr['description_links'],
	);

	foreach (['prev','curr'] as $elem) {
		# fix some properties

		if (isset($$elem['location']['fbid'])) {
			$$elem['location'] = $$elem['location']['fbid'];
		}
		if (isset($$elem['location'])
		&&	is_number($$elem['location'])
		) {
			$is_location = db_single('fbid','SELECT 1 FROM fbid WHERE ELEMENT="location" AND FBID='.$$elem['location']);
			if (!$is_location) {
				unset($$elem['location']);
			}
		}

		if (isset($$elem['organizations'])) {
			$uniques = [];
			foreach ($$elem['organizations'] as $organization) {
				$id = $organization['fbid'] ?? $organization['name'];
				$uniques[$id] = $id;
			}
			$$elem['organizations'] = $uniques;
		}
		$$elem['livestream'] = !empty($$elem['livestream']);
		$$elem['ticketlink'] = isset($$elem['ticketlink']) ? call_user_func($utf8 ? 'utf8_myrtrim' : 'rtrim', cleanup_presale_url($$elem['ticketlink']), '/') : null;

		if (isset($$elem['fbdescription'])) {
			# compare fbdescription without ubb tags
			require_once '_ubb.inc';
			$$elem['fbdescription'] = utf8_mytrim(plain_text($$elem['fbdescription'],UBB_UTF8));
		}
	}

	$prev_desc = $prev['fbdescription'] ?? null;
	$curr_desc = $curr['fbdescription'] ?? null;

#	file_put_contents('/tmpdisk/prev_desc',$prev_desc);
#	file_put_contents('/tmpdisk/curr_desc',$curr_desc);

	if ($prev_desc === $curr_desc) {
		$obj['fbdescription'] = $prev_desc;
	}

	$prev_orgs = !empty($prev['organizations']) ? count($prev['organizations']) : 0;
	$curr_orgs = !empty($curr['organizations']) ? count($curr['organizations']) : 0;

	if ($curr_orgs < $prev_orgs) {
		# ignore orgs
		unset($prev['organizations'],
			  $curr['organizations']);
	} else {
		if ($curr_orgs > $prev_orgs) {
			# remove unconnectable orgs from the comparison list
			if ($curr_orgs) {
				$keep_orgs = [];
				foreach ($curr['organizations'] as $fbid => $org) {
					if (empty($org['elements'])) {
						continue;
					}
					$keep_orgs[$fbid] = $org;
				}
				ksort($keep_orgs);
				$curr['organizations'] = $keep_orgs;
			}
			if ($prev_orgs) {
				$keep_orgs = [];
				foreach ($prev['organizations'] as $fbid => $org) {
					if (empty($org['elements'])) {
						continue;
					}
					$keep_orgs[$fbid] = $org;
				}
				ksort($keep_orgs);
				$prev['organizations'] = $keep_orgs;
			}

			$prev_orgs = !empty($prev['organizations']) ? count($prev['organizations']) : 0;
			$curr_orgs = !empty($curr['organizations']) ? count($curr['organizations']) : 0;

			if ($curr_orgs > $prev_orgs) {
				if ($debug) {
					__debug('curr_orgs != prev_orgs');
					__debug('curr_orgs: '.var_get($curr['organizations']));
					__debug('prev_orgs: '.var_get($prev['organizations']));
				}
				return false;
			}
		}
		# ignore connected elements
		if ($curr_orgs) {
			foreach ($curr['organizations'] as &$org) {
				if (is_array($org)) {
					unset($org['elements']);
				}
			}
			unset($org);
		}
		if ($prev_orgs) {
			foreach ($prev['organizations'] as &$org) {
				if (is_array($org)) {
					unset($org['elements']);
				}
			}
			unset($org);
		}
	}

	require_once '_find_ticket_uri.inc';
	# remove fbclid from ticketlink

	# try to do as little lookups as possible
	$prev_ticketlink = $prev['ticketlink'];
	$curr_ticketlink = $curr['ticketlink'];

	if ($prev_ticketlink !== $curr_ticketlink) {
		if (!$prev_ticketlink || !$curr_ticketlink) {
			if ($debug) {
				__debug('WARNING one of the ticket links is empty', get_defined_vars());
			}
		} elseif (false !== ($utf8 ? 'mb_strpos' : 'strpos')($prev_ticketlink,'facebook')) {
			# prev has internal facebooklink, so look it up a
			[$prev_ticketlink] = ($utf8 ? 'utf8_myrtrim' : 'rtrim')(find_actual_ticket_url(cleanup_url($prev['ticketlink'], $utf8), utf8: $utf8), '/');
			if ($prev_ticketlink === $curr_ticketlink) {
				$debug && __debug('identical ticketlink after prev lookup');
				# actually identical
				unset($prev['ticketlink'],$curr['ticketlink']);
			} elseif ($debug) {
				__debug('parsed prev, but still not same?');
				__debug('prev: '.$prev_ticketlink);
				__debug('curr: '.$curr_ticketlink);
			}
		} elseif (false !== call_user_func($utf8 ? 'mb_strpos' : 'strpos', $curr_ticketlink, 'facebook')) {
			if ($debug) {
				__debug('WARNING huh, curr_ticketlink with facebook @ '.$curr['fbid']);
			}
		} else {
			[$prev_ticketlink] = find_actual_ticket_url(cleanup_url($prev['ticketlink'], $utf8), utf8: $utf8);
			$prev_ticketlink = ($utf8 ? 'utf8_myrtrim' : 'rtrim')($prev_ticketlink, '/');
			if ($prev_ticketlink === $curr_ticketlink) {
				if ($debug) {
					__debug('identical ticketlink after prev lookup');
				}
				# actually identical
				unset($prev['ticketlink'], $curr['ticketlink']);
			}
		}
	}

	# 2021: facebook does not supply TZ information anymore:
	# 2022: facebook supports it again?
#	unset($prev['start_date']['tz']);
#	unset($curr['start_date']['tz']);

	$prev_cmp = $prev;
	$curr_cmp = $curr;

	recursive_array_diff($prev_cmp, $curr_cmp);

	if ($prev_cmp || $curr_cmp) {
		if ($debug) {
			__debug('prev: '.var_get($prev_cmp));
			__debug('curr: '.var_get($curr_cmp));
		}
		return false;
	}

	header('X-PF-FB-SAME: true');
	return true;
}

function parse_fb_count(string $str): int|false {
	# strip erroneous bit from icon
	$str = str_replace('friend_friends', '', $str);

	if (is_number($str)) {
		return (int)$str;
	}

	if (preg_match('"(\d+)(?:[,.](\d+))?[dk]"iu', $str, $match)) {
		return 1000 * (empty($match[2]) ? $match[1] : $match[1].'.'.$match[2]);
	}
	return false;
}

function get_fbids_for_href(string $url): ?array {
	if (!preg_match('"^(?:https://(?:www\.)?facebook\.com)?/(.*?)/?$"u', $url, $match)) {
		return null;
	}
	[, $link_name] = $match;

	/** @var array<int> $fbids */
	$fbids = [];

	if (preg_match('"^profile\.php\?id=(?<fbid>\d{6,})"u', $link_name, $match)
	||	preg_match('"^[^/]+-(?<fbid>\d{6,})"u', $link_name, $match)
	||	preg_match('"^pages/[^/]*?/(?<fbid>\d{6,})"u', $link_name, $match)
	) {
		$fbids[$fbid = (int)$match['fbid']] = $fbid;
	}
	/** @var int $fbid */
	if ($fbid = db_single('facebook_link', '
		SELECT FBID
		FROM facebook_link
		WHERE LINK = "'.addslashes($link_name).'"
		ORDER BY TYPE = "page" DESC
		LIMIT 1')
	) {
		$fbids[$fbid] = $fbid;
	}

	if (!$fbids) {
		return [];
	}

	$fbid_str = implode(', ', $fbids);

	if ($identicals = db_same_hash('fbid_identical', '
		SELECT DISTINCT PAGEID
		FROM fbid_identical
		WHERE FBID IN ('.$fbid_str.')')
	) {
		$fbids += $identicals;
	}
	if ($identicals = db_same_hash('fbid_identical', '
		SELECT DISTINCT FBID
		FROM fbid_identical
		WHERE PAGEID IN ('.$fbid_str.')')
	) {
		$fbids += $identicals;
	}
	return $fbids;
}

function split_duration(int $duration): array {
	$hours = (int)($duration / ONE_HOUR);
	$duration -= ONE_HOUR * $hours;
	$mins = (int)($duration / 60);
	$duration -= 60 * $mins;
	$secs = $duration;

	if ($mins === 59) {
		# round to an hour
		++$hours;
		$mins = 0;
	}

	return [
		'duration_hours'	=> $hours,
		'duration_mins'		=> $mins,
		'duration_secs'		=> $secs
	];
}

function get_duration_from_match(array $date_match): array {
	$start_time = $date_match['start_hour'] * ONE_HOUR + $date_match['start_mins'] * ONE_MINUTE;
	$end_time =
	(	$date_match['end_hour'] < $date_match['start_hour']
	?	$date_match['end_hour'] + 24
	:	$date_match['end_hour']
	) * 3600 + $date_match['end_mins'] * 60;

	$duration = $start_time > $end_time ? $start_time - $end_time : $end_time - $start_time;

	__debug('duration determined: '.$duration);

	return split_duration($duration);
}
