<?php

global $page_problem;

define('CURRENTSTAMP',	 time());
const CACHESTAMP = CURRENTSTAMP - CURRENTSTAMP % 300;
define('TODAYSTAMP',	 mktime(0, 0, 0));
define('TODAYSTAMP_TZI', gmmktime(0, 0, 0));

require_once '_offline.inc';
online_check();

foreach (['sELEMENT', 'ACTION', 'SUBACTION', 'PAGE'] as $field) {
	if (!isset($_REQUEST[$field])
	||	!is_scalar($_REQUEST[$field])
	) {
		$_REQUEST[$field] = null;
	}
}

foreach (['sID', 'subID', 'ssID', 'sssID'] as $field) {
	$_REQUEST[$field] =
		empty($_REQUEST[$field])
	||	!ctype_digit($_REQUEST[$field])
	?	0
	:	(int)$_REQUEST[$field];
}

require_once '_helper.inc';

if (!isset($_REQUEST['SECTION'])) {
	$_REQUEST['SECTION'] = 'home';
} elseif ($_REQUEST['SECTION'] === 'error') {
	if (isset($_SERVER['REDIRECT_URL'])
	&&	preg_match('/\.(?:avif|bmp|gif|jpe?g|png|webp)$/i', $_SERVER['REDIRECT_URL'])
	) {
		require_once '_image_bail.inc';
		image_bail(404);
	}
} elseif (
	$_REQUEST['SECTION'] !== 'short'
&&	(	empty($_REQUEST['sELEMENT'])
	||	$_REQUEST['SECTION'] !== $_REQUEST['sELEMENT']
	)
) {
	robot_action('noindex');

	# mail_log('when end up here', get_defined_vars());

	if (isset($_REQUEST['sELEMENT'])
	&&	$_REQUEST['sELEMENT'] === 'flock'
	) {
		$_REQUEST['sELEMENT'] = $_REQUEST['SECTION'];
		mail_log('sELEMENT ('.$_REQUEST['sELEMENT'].') set to SECTION ('.$_REQUEST['SECTION'].')');
	}
}

require_once '_tooslow.inc';
require_once '_http_status.inc';
require_once '_short.inc';
short_conversion();

require_once '_legacyredirect.inc';
legacy_redirect();

if (!empty($_SERVER['REDIRECT_STATUS'])
&&	$_SERVER['REDIRECT_STATUS'] === '429'
) {
	http_status(429);
	exit;
}

/*if ($bgproc = !empty($_POST['ALLOWBG'])) {
	$bgproc	=
		!empty($_POST['URL'])
	||	!empty($_POST['URL2'])
	||	!empty($_POST['DATA'])
	||	!empty($_POST['DATA2'])
		# NOTE: What does this PRESALE_WEBSITE do thet required background processing?
	||	!empty($_POST['PRESALE_WEBSITE']);

	if (!$bgproc
	&&	!empty($_FILES)
	) {
		foreach ($_FILES as $name => $info) {
			if (is_array($info['size'])) {
				foreach ($info['size'] as $size) {
					if ($size) {
						$bgproc = true;
						break;
					}
				}
			} elseif ($info['size']) {
				$bgproc = true;
				break;
			}
		}
	}
	if ($bgproc) {
		register_shutdown_function(static function(): void {
			if (!($fastcgi_finish = function_exists('fastcgi_finish_request'))
			&&	!empty($GLOBALS['do_background'])
			) {
				header('Connection: close');
			}
			$status = ob_get_status(true);
			$cnt = count($status);
			error_log("closing $cnt output buffers @ shutdown of {$_SERVER['REQUEST_URI']}");
			ob_start();
			var_dump($status);
			error_log(ob_get_clean());
			while ($cnt--) {
				ob_end_flush();
			}
			flush();
			if ($fastcgi_finish) {
				fastcgi_finish_request();
			}
			set_time_limit(3600);
		});
	}
}*/

function close_connection_for_background(): void {
	if (!($fastcgi_finish = function_exists('fastcgi_finish_request'))) {
		header('Connection: close');
	}
	$status = ob_get_status(true);
	$cnt = count($status);
	while ($cnt--) {
		ob_end_flush();
	}
	flush();
	if ($fastcgi_finish) {
		fastcgi_finish_request();
	}
	set_time_limit(ONE_HOUR);
}

require_once '_error.inc';

preload_messages();

require_once '_log.inc';
require_once '_browser.inc';
require_once '_domain.inc';

header('Vary: Cookie');

require_once '_require.inc';
require_once '_servertype.inc';

if (HOME_THOMAS) {
	require_once '_error_handler.inc';
	set_my_error_handler();
}

require_once '_globalsettings.inc';
deny_robots_if_partyflock_under_load();

require_once '_combinable.inc';
redirect_to_combination();

if (preg_match('"^/label/(?P<labelid>\d+)\b"', $_SERVER['REQUEST_URI'], $match)
&&	($organizationid = db_single('label_to_organization','
		SELECT ORGANIZATIONID
		FROM label_to_organization
		WHERE LABELID = '.$match['labelid']))
) {
	moved_permanently(get_element_href('organization', $organizationid));
}


require_once '_date.inc';

# special daynum value, ranging from 1900 and up, first daynum is 1st of january 1900
const DAYNUMOFFSET = 693961;
define('CURRENTDAYNUM', to_days());
const OFFSETDAYNUM = CURRENTDAYNUM - 693961;

require_once '_argparse.inc';

require_once '_cookie.inc';
require_once '_db.inc';
require_once '_memcache.inc';
require_once '_urltitle.inc';

require_once '_hosts.inc';
require_once '_flockmod.inc';
require_once '_getcurrentaddr.inc';
getcurrentaddr();

require_once '_identity.inc';
require_once '__translation.php';
require_once '_identity.inc';
require_once '_currentuser.inc';
require_once '_pingdom.inc';
require_once '_server_range_detect.inc';
require_once 'defines/disallow.inc';

if (DISALLOW_ROBOTS
&&	!is_pingdom_ip()
&&	!str_contains(USER_AGENT, 'facebookexternalhit')
&&	!str_contains(USER_AGENT, 'archive.org_bot')
&&	!str_contains(USER_AGENT, 'GoogleBot')
&&	!str_contains(USER_AGENT, 'Bingbot')
&&	!is_allowed_robot(IPV6, CURRENTIPNUM, CURRENTIPSTR)
&&	(	is_server_ip()
	||	is_spider())
) {
	require_once '_simple_error.inc';
	show_simple_error_page('warning:NO_BOT_title', 'warning:NO_BOT_body');
	http_response_code(http_status::PAYMENT_REQUIRED->value);
	exit;
}

$currentuser = new _currentuser();

if (SERVER_VIP
&&	!HOME_THOMAS
&&	!FLOCK_NET
&&	!have_admin('development')
&&	CURRENTUSERID !== 1175083
||	CURRENTDOMAIN !== 'nl'
) {
	# NOTE: bounce to non-VIP
	#require_once '_memcache.inc';
	#require_once '_url.inc';

	/*error_log(
		"TO non-VIP: REQUEST_URI: ".$_SERVER['REQUEST_URI']."\n".
		"TO non-VIP: REMOTE_ADDR: ".$_SERVER['REMOTE_ADDR']."\n".
		"TO non-VIP: REMOTE_HOST: ".memcached_hostname_from_ipbin(CURRENTIPBIN)."\n".
		"TO non-VIP: USER_AGENT:  ".($_SERVER['HTTP_USER_AGENT'] ?? '')
	);*/

	see_other('https://partyflock.nl'.$_SERVER['REQUEST_URI']);
}

if (SERVER_VIP
||	$_REQUEST['ACTION'] === 'searchresult'
) {
	robot_action('noindex, nofollow');
}

if ($platform
=	!USER_AGENT
?	null
:	(	str_contains(USER_AGENT, 'iOSPartyflockApp')
	||	str_contains(USER_AGENT, 'CPU iPhone OS')
	&&	!str_contains(USER_AGENT, 'Safari/')
	?	'ios'
	:	(	str_contains(USER_AGENT, 'AndroidPartyFlockApp')
		?	'android'
		:	null
		)
	)
) {
	db_insert('app_request', "
	INSERT INTO app_request SET
		PLATFORM='$platform',
		STAMP	=".CURRENTSTAMP.',
		USERID	='.CURRENTUSERID.',
		IDENTID	='.CURRENTIDENTID.",
		IPBIN	='".addslashes(CURRENTIPBIN)."',
		URI		='".addslashes($_SERVER['REQUEST_URI'])."'"
	);
}

if (empty($_SERVER['HTTP_HOST'])
||	in_array($_SERVER['HTTP_HOST'], ['localhost', '127.0.0.1', '::1'], true)
) {
	# no host was sent, or localhost
	http_response_code(400);
	exit;
}

# Validate HTTP_HOST
if (SERVER_VIP) {
	if ($_SERVER['HTTP_HOST'] !== 'vip.partyflock.nl') {
		header("Location: https://vip.partyflock.nl{$_SERVER['REQUEST_URI']}");
		exit;
	}
} elseif (SERVER_PRODUCTION) {
	if ($_SERVER['HTTP_HOST'] !== 'partyflock.nl') {
		header("Location: https://partyflock.nl{$_SERVER['REQUEST_URI']}");
		exit;
	}
} elseif (!SERVER_SANDBOX) {
	mail_log('Not VIP, REGULAR nor SANDBOX, what is it?');
	http_response_code(400);
	exit;
}

require_once '_sections.inc';
if (isset($_SERVER['eELEMENT'])) {
	require_once $_SERVER['eELEMENT'].'.inc';
	$section = new section();
}

if (empty($_SERVER['SCRIPT_URL'])) {
	# probably for a *.txt file or something else that does not exist
	$_SERVER['SCRIPT_URL'] = null;
}

if ($_REQUEST['sELEMENT'] === 'doc') {
	header('Location: https://'.$_SERVER['HTTP_HOST'].'/advertising');
	exit;
}
if (!isset($_SERVER['REMOTE_ADDR'])) {
	$_SERVER['REMOTE_ADDR'] = '127.0.0.1';
}

if (($item_hashed
=	isset($_GET['NEWSADHASH'])
||	isset($_GET['ADHASH'])
||	isset($_GET['ITEMHASH'])
||	isset($_GET['PROMOHASH'])
)
||	SERVER_SANDBOX
#||	isset(DONT_INDEX_SECTIONS[$_REQUEST['sELEMENT']])
) {
	robot_action('noindex');
}

switch ($_REQUEST['ACTION']) {
case 'commit':
	if ($_REQUEST['sID']
	&&	!have_post()
	) {
		header('Location: https://'.$_SERVER['HTTP_HOST'].get_element_href($_REQUEST['sELEMENT'], $_REQUEST['sID']),true,301);
		exit;
	}
	break;

case 'comment':
	switch ($_REQUEST['SUBACTION']) {
	case 'commit':
	case 'deny':
	case 'accept':
	case 'change':
	case 'new':
	case null:
		if ($_REQUEST['sID']) {
			header('Location: https://'.$_SERVER['HTTP_HOST'].get_element_href($_REQUEST['sELEMENT'],$_REQUEST['sID']),true,301);
			exit;
		} else {
			http_response_code(404);

			$_REQUEST['SECTION'] = 'error';
			$_REQUEST['sELEMENT'] = 'error';
			$_SERVER['REDIRECT_STATUS'] = 404;
		}
		break;
	}
	break;
}

require_once '_cookie_consent.inc';
require_once '_colors.inc';

//const NICE_GALLERY = SERVER_SANDBOX || HOME_OFFICE || SERVER_VIP;

require_once '_resolution.inc';
$res = get_resolution();

require_once '_smallscreen.inc';

header('Cache-Control: must-revalidate,'.(have_user() || $item_hashed ? 'private' : 'public'));

header('Strict-Transport-Security: max-age=31536000; includeSubDomains');

# we may not be framed
header("Content-Security-Policy: frame-ancestors 'none';");

$sectionid = get_sectionid($_REQUEST['SECTION']);

if (!$_REQUEST['ACTION']
&&	!empty($_SERVER['eDATA'])
) {
	$_REQUEST['ACTION'] = $_SERVER['eDATA'];
}

check_urltitle();

if (!$sectionid) {
	http_status(404);

	$_REQUEST['SECTION'] = 'error';
	$_REQUEST['sELEMENT'] = 'error';
	$_SERVER['REDIRECT_STATUS'] = 404;
}

if (!ROBOT
&&	!isset($_SERVER['REDIRECT_STATUS'])
&&	(require_once '_dosdetect.inc')
&&	have_DOS()
) {
	exit_with_DOS_page();
}

# Cache-control: max-age=0 => F5
# Cache-control: no-cache => CTRL+F5
# FIXME: generize dirty refresh detection for all users.

require_once '_ad.inc';

show_ad_placeholder(true);

if (isset($_REQUEST['NEXT'], $_REQUEST['THEME'])
&&	($_REQUEST['NEXT'] = (int)$_REQUEST['NEXT'])
&&	$_REQUEST['NEXT'] === 2
) {
	set_flag_flock_cookie(FLOCK_FLAG_DARK_SITE, $_REQUEST['THEME'] === 'dark');
	set_flag_flock_cookie(FLOCK_FLAG_THEME_SET);
}

# FIXME: these are not always necessary!
require_once '_url.inc';
require_once '_smiley.inc';
require_once '_ubb.inc';
require_once '_going.inc';
require_once '_profileimage.inc';
require_once '_lock.inc';
require_once '_sort.inc';
require_once '_layout.inc';
require_once '_style.inc';
require_once '_pagechanged.inc';

set_global_dates();
global $__day, $__month, $__year;

if (!$_REQUEST['SECTION']) {
	header('Location: '.FULL_HOST.'/', true, 301);
	exit;
}

if (!isset($section)) {
	switch ($_REQUEST['SECTION']) {
	case 'event':
		require_once '_stable_slug.inc';
		if (!slug_shot()) {
			not_found();
			$_REQUEST['SECTION'] =
			$_REQUEST['sELEMENT'] = 'error';
		}
		break;

	case 'party':
		if (!$_REQUEST['sID']
		||	$_REQUEST['ACTION'] !== 'single'
		) {
			break;
		}
		require_once '_stable_slug.inc';
		slug_jump($_REQUEST['sID']);
		break;
	}
#	if (HOME_THOMAS) {
#		require_once "{$_REQUEST['SECTION']}_new.inc";
#	} else {
		require_once "{$_REQUEST['SECTION']}.inc";
#	}
}

$__singular =
	$_REQUEST['sID']
||	$_REQUEST['ACTION'] === 'form';

if (have_admin($_REQUEST['sELEMENT'])
&&	$_REQUEST['sID']
&&	(	$_REQUEST['sELEMENT'] === 'photo'
	?	(	$_REQUEST['ACTION'] === 'hide'
		||	$_REQUEST['ACTION'] === 'unhide'
		)
	:	(	$_REQUEST['ACTION'] === 'accept'
		||	$_REQUEST['ACTION'] === 'unaccept'
		)
	)
) {
	require_once '_quote.inc';
	flush_new_quotes_element($_REQUEST['sELEMENT'], $_REQUEST['sID']);
}

if (!empty($_REQUEST['ELEMENT'])
&&	$_REQUEST['ELEMENT'] === 'on'
&&	($newuri = preg_replace('"[?&;]ELEMENT=on(?:$|[&;])"', '', $_SERVER['REQUEST_URI']))
&&	$newuri !== $_SERVER['REQUEST_URI']
) {
	header('Location: https://'.$_SERVER['HTTP_HOST'].$newuri);
	exit;
}

header('Content-Type: text/html; charset=windows-1252');

if (function_exists('preamble')) {
	preamble();
}

if (!empty($_REQUEST['PAGE'])
&&	$_REQUEST['ACTION'] === 'single'
) {
	check_page_comments_action();
}

if ($_REQUEST['sID']
&&	$_REQUEST['ACTION'] === 'videos'
) {
	require_once '_nocache.inc';
	send_no_cache_headers();
	header('Location: https://'.$_SERVER['HTTP_HOST'].'/video/'.$_REQUEST['sELEMENT'].'/'.$_REQUEST['sID'],true,303);
	exit;
}
require_once '_menu.inc';
require_once '_nocache.inc';

if (!partyflock_read_only()) {
	if (isset($section)) {
		$section->perform_commit();

	} elseif (function_exists('perform_commit')) {
		$commitresult = perform_commit();

		if ($_SERVER['REQUEST_METHOD'] === 'POST'
		&&	isset(USE_URLTITLE[$_REQUEST['sELEMENT']])
		) {
			# refresh titles
			# NOTE: why?
			get_titles(false, true);
		}
	}
}

$html_prefix = [
	'og'			=> 'https://ogp.me/ns#',
	'fb'			=> 'https://ogp.me/ns/fb#',
	'partyflock'	=> 'https://ogp.me/ns/fb/partyflock#',
];

require_once 'defines/facebook.inc';

include_og('fb:app_id', FACEBOOK_APPID);

include_og('og:locale', CURRENTLOCALE);
include_og('og:locale:alternate', 'en_US');
include_og('og:locale:alternate', 'nl_NL');

if (isset($_REQUEST['choosescreen'])) {
	require_once '_choosescreen.inc';
	show_screen_chooser();
	exit;
}

require_once '_lastseen.inc';
mark_item();

# Determining whether we should show overlay, quite high up.
# We must be sure that the get_special_overlay() can initialize global $__whatover used to augment
# the logo on certain days.
require_once '_specialoverlay.inc';
$overlay = get_special_overlay();

require_once '_counthit.inc';
require_once '_meta.inc';

# OB_START: head
ob_start();
	?><link rel="dns-prefetch" href="https://photo.partyflock.nl/" /><?
	?><link rel="dns-prefetch" href="https://static.partyflock.nl/" /><?

	# include_js('js/main', JS_PRELOAD);
	# Firefox warns preload is not effective on js/main, as it is not used within a few seconds:
	include_js('js/main');

	?><meta charset="windows-1252" /><?
/*	?><meta http-equiv="content-type" content="text/html;charset=windows-1252" /><?*/

	if ($_REQUEST['sELEMENT'] === 'home') {
		?><meta name="wot-verification" content="0e7fec564324abddcf88" /><?
	}

	if (USER_AGENT
	&&	str_contains(USER_AGENT, 'Google')
	) {
		# <NAME_EMAIL>
		?><meta name="google-site-verification" content="T2iL7g7tHxhe2NBEL43YBBioy_a30pxsxhe4kcfmIss" /><?
		# <EMAIL>:
		?><meta name="google-site-verification" content="yFMYA2gc5nnNueAezW83DroAXkwioOT6x7AfqB7lE2g" /><?
		?><meta name="google-site-verification" content="WY_yZVEprogFYBG9fDALwjOFkeTtp6eWcgMpO30RnTI" /><?
	}
	include_style('main');
	if (have_admin()) {
		include_style('admin');
	}
	load_fonts();

	show_favicon_link();

	?><link rel="apple-touch-icon" href="<?= STATIC_HOST ?>/apple-touch-icon.png" /><?
	?><link rel="apple-touch-icon" sizes="120x120" href="<?= STATIC_HOST ?>/apple-touch-icon-120x120.png" /><?
	?><link rel="apple-touch-icon" sizes="152x152" href="<?= STATIC_HOST ?>/apple-touch-icon-152x152.png" /><?
	?><link rel="apple-touch-icon" sizes="167x167" href="<?= STATIC_HOST ?>/apple-touch-icon-167x167.png" /><?
	?><link rel="apple-touch-icon" sizes="180x180" href="<?= STATIC_HOST ?>/apple-touch-icon-180x180.png" /><?

	?><link rel="search" type="application/opensearchdescription+xml" href="/opensearch.xml" title="<?= __('searchspec:action') ?>" /><?

	# OB_START: section_header
	ob_start();
	if (function_exists('display_header')) {
		display_header();
	}
	$section_header = ob_get_clean();
	# OB_END: section_header

	if ($_REQUEST['sID']
	&&	$_REQUEST['ACTION'] === 'single'
	) {
		require_once '_feed.inc';
		show_feed($_REQUEST['sELEMENT'], FEED_HEADER);
	}
	if ($_REQUEST['sELEMENT'] === 'home') {
		$search_json = [
			'@context'		=> 'https://schema.org',
			'@type'			=> 'WebSite',
			'url'			=> 'https://partyflock.'.CURRENTDOMAIN.'/',
			'potentialAction'	=> [
				'@type'		=> 'SearchAction',
				'target'	=> 'https://partyflock.'.CURRENTDOMAIN.'/search?TERMS={TERMS}',
				'query-input'	=> 'required name=TERMS',
			],
		];
		echo '<script type="application/ld+json">',safe_json_encode($search_json, JSON_UNESCAPED_SLASHES),'</script>';
	}

$head = ob_get_clean();
# OB_END: head

foreach(partyflock_all_statuses() as $status => $message) {
}

if ($read_only = partyflock_read_only()) {
	header('Retry-After: 3600', true, 503);
	if (!ROBOT) {
		?><div class="top-warning top-message"><?
		require_once '_star.inc';
		echo get_star(alt: '!', color: 'red') ?> <?
		if (CURRENTLANGID === 1) {
			?><b>Momenteel alleen leesmogelijkheid! Groot onderhoud aan de database.</b> Excuses voor het ongemak.<?
		} else {
			?><b>Currently Partyflock can only be read! Major maintenance of the database.</b> Sorry for the inconvenience.<?
		}
		if (is_string($read_only)) {
			?><br /><?
			echo nlnl2cmbr(
				 str_replace(['\n\n', '\n'], ["\n\n", "\n"], escape_utf8($read_only))
			);
		}
		?></div><?
	}
} elseif (
	!ROBOT
&&	have_user()
) {
	if (have_relation()) {
		require_once '_invoicemailonly.inc';
		show_invoicemailonly_form();
	}

	require_once '_findby.inc';
	show_findby_form();
}

$is_home = $_REQUEST['sELEMENT'] === 'home';

if (!$page_problem) {
	$body = get_contents();
}
if ($page_problem) {
	# OB_START: body with problem
	ob_start();
	show_page_problem();
	$body = ob_get_clean();
	# OB_END: body with problem
}

determine_banners();

if ($is_home) {
	if (!function_exists('fill_home_banners')) {
		# could happen for 404 in home section
	} else {
		fill_home_banners($body);
	}
}

global $__left_section, $__right_section;

$menu_data = get_menu(SMALL_SCREEN);



if (SMALL_SCREEN) {
	include_style('smallscreen');
}

if (SMALL_SCREEN) {
	global $mobilead, $subbar;

	$premenu = '';

	# OB_START: body
	ob_start();

	?><div class="clear" style="margin-top: .4em;"></div><?

	if ($mobilead = obtain_ad(ADPOS_SMALL_SCREEN)) {
		?><div class="center block"><?
		display_ad($mobilead);
		?></div><?
	}

	if ($__left_section || $__right_section) {
		?><div class="sh relative"><?
			?><div class="bold"><?= $__left_section ?: '&nbsp;' ?></div><?
			if ($__right_section) {
				?><div class="abs shrp"><?= $__right_section ?></div><?
			}
		?></div><?
	}
	global $subbar;
	if ($subbar) {
		display_ad($subbar);
		$subbar = null;
	}

	display_messages();

	?><main><?= $body ?></main><?

	if (!ROBOT && !have_post()) {
		?><hr class="slim" style="width: 20%; margin-left: 40%; margin-top: 1.8em;"><?
		?><div id="bottom" class="center clear" style="padding: 1em;"><?
		?><a onclick="return confirm('<?= __('confirm:jump_to_regular_LINE',IS_JAVASCRIPT) ?>');"<?
		?> href="/smallscreen.act?no"><?=
			__C('action:view_this_page_on_regular_Partyflock')
		?></a><?
		?></div><?
	} else {
		?><div id="bottom"></div><?
	}
} else {
	global $ad, $ad2, $subbar, $towerad, $towerad2, $towerad3;

	# OB_START: pre_menu
	ob_start();

	include_style('largescreen');

	require_once '_resolution.inc';

	include_style('largescreen');

	?><div class="sitewrap"><?

	?><div id="toprow"<?
	if (isset($ad['BG'])) {
		?> class="toprow"<?
		$head .= '<style>.toprow { background-color: #'.sprintf('%06X', $ad['BG']).'; }</style>';
	}
	?>><?

	$classes = [];
	global $__month, $__day, $__hour, $__whatover, $__year, $__halloween;
	if ($__halloween
		=	!ROBOT
		&&	(	isset($_GET['HALLOWEEN'])
			||	$__month === 10
			&&	$__day	 === 31
			)
	) {
		?><img<?
		?> class="novents abs" src="<?= STATIC_HOST ?>/images/moonbats<?= is_high_res() ?>.png"<?
		?> style="width: 116px; height: 110px; top: -14px; left: 4px;" /><?
		$classes[] = 'halloween';
	}

	?><div class="nmark"><?
		?><div class="relative"><?
		global $__month, $__day, $__hour, $__whatover, $__year, $__halloween;
		if (!ROBOT && $__whatover) {
			show_logo_overlay();
		}
		?><a href="/"><?
		?><img src="<?= STATIC_HOST ?>/images/logo_<?= CURRENTTHEME ?>_v2<?= is_high_res()
		?>.png" width="90" height="90" class="<?
		if ($__halloween) {
			?>bshdb <?
		}
		?>relative z1 vbottom" alt="Partyflock" title="Partyflock" /><?
		?></a><?
		?></div><?
	?></div><?

	?><div class="nmore"><?
		?><div id="maininfo"<?
		if ($classes) {
			?> class="<?= implode(' ', $classes) ?>"<?
		}
		?>><?
		global $ad, $ad2;
		if ($ad || $ad2) {
			?><div class="cntnt"><?
		}

		main_display_info();

		if ($ad || $ad2) {
			?></div><?
		}
		?></div><?
	?></div><?
	?></div><? # END #toprow

	function show_top_ad(array &$ad): void {
		# set line-height to 90px to force middle alignment
		?><div class="ib vtop dovents" style="max-width: 49%; height: 90px; line-height: 90px;"><?
		display_ad($ad, loading: 'eager');
		?></div><?
	}

	if ($ad || $ad2) {
		$h = 90;

		?><div class="abs center nowrap fw novents" style="top: <?= (102 - $h) / 2 ?>px;" id="topfrot"><?
		if ($ad && $ad2) {
			$sep = 10;
			show_top_ad($ad);
			?><div class="ib vtop" style="max-width: 2%; width: <?= $sep ?>px;"></div><?
			show_top_ad($ad2);
		} else {
			?><div class="ib dovents"><?
			if ($ad) {
				# if/then needed because first param is refernce
				display_ad($ad, loading: 'eager');
			} else {
				display_ad($ad2, loading: 'eager');
			}
			?></div><?
		}
		?></div><?
	}

	// SECTION HEADER

	?><div id="barrow"><?
	?><div class="lbr"><?
	if (!have_user()) {
		?><div class="sh">&nbsp;</div><?
	} else {
		?><div class="sh bold"><?
			?><div class="mwrp"><?
				?><div class="grdnt"></div><?
				?><div id="mebox"><?= get_me(2) ?></div><?
				?>&nbsp;<?
			?></div><?
		?></div><?
	}
	?></div><?

	?><div class="rbr"><?
	?><div class="sh relative"><?
		?><div class="bold"><?= $__left_section ?: '&nbsp;' ?></div><?

		?><div class="abs shrp"><?
		require_once '_supersearch.inc';
		show_super_search_form_inline() ?> <?=
			get_language_switch(), ' ',
			get_theme_switch(), ' ',
			$__right_section
		?></div><?

	?></div><?
	?></div><?

	?></div><? # END #barrow

	// end of SECTION HEADER

	# relatively small screen, display billboard in its own row
	#
	# required non home: 160 + space + 970 + space + 170 = 1300 + 2*space
	# required home: 160 + space + 970 + space + 336 = 1466 + 2*space

	global $subbar;
	if ($subbar) {
		?><div id="widesubbar"><?
		$w = $res[1] ?? 0;
		if (!$w
		||	(	$is_home
			?	$w < 1500
			:	$w < 1350
			)
		) {
			display_ad($subbar, loading: 'eager');
		}
		?></div><?
	}

	?><div id="mainrow"><?

		?><div id="menucol"><?

		$premenu = ob_get_clean();
		# OB_END: PRE MENU

		# OB_START: BODY
		ob_start();
		global $towerad, $towerad2, $towerad3;
		if ($towerad) {
			?><div class="block"><?
			display_ad($towerad);
			?></div><?
		}
		if ($towerad2) {
			?><div class="block"><?
			display_ad($towerad2);
			?></div><?
		}
		if ($towerad3) {
			?><div class="block"><?
			display_ad($towerad3);
			?></div><?
		}

		?></div><? # END #menucol

		?><main id="contentcol"><?

		if ($subbar
		&&	empty($subbar['done'])
		) {
			?><div id="narrowsubbar"><?
			display_ad($subbar, loading: 'eager');
			?></div><?
		}

		display_messages();

		echo $body;

		?></main><? # END #contentcol

		if (show_right_towers()) {
			?><aside id="twrcol"><?
			foreach ([
				'rightower'		=> 'eager',
				'rightower2'	=> 'eager',
				'rightower3'	=> 'lazy',
				'rightower4'	=> 'lazy',
				'rightower5'	=> 'lazy',
				'rightower6'	=> 'lazy',
			] as $tower => $loading) {
				if (!$$tower) {
					continue;
				}
				display_ad($$tower, 'block', loading: $loading);
			}
			?></aside><?
		}

	?></div><? # END #mainrow

	?></div><? # END sitewrap

	if (!have_post()
	&&	probably_small_screen()
	) {
		?><div class="center clear" style="padding: 1em;"><?
		?><a href="/smallscreen.act?yes"><?= __C('action:view_this_page_on_mobile_Partyflock') ?></a><?
		?></div><?
	}

	?><div id="bottom"></div><?
}

?><footer id="footer"><?
?></footer><?

if ($overlayad) {
	display_ad($overlayad, loading: 'eager');
}

$body = ob_get_clean();
# OB_END: post menu

ob_start(); # all_head

?><meta name="viewport" content="width=device-width, initial-scale=1" id="vp" /><?

echo $head;

$body = preg_replace_callback('!<img(?<properties>[^>]*)>!', static fn(array $match): string =>
		str_contains($match['properties'], 'loading=')
	?	$match[0]
	:	'<img loading="lazy"'.$match['properties'].'>',
	$body);

$have_form =
		in_content('<form')
	||	isset($GLOBALS['__pffboverlay']);

if ($have_form
||	in_content('<select')
||	in_content('<input')
||	in_content('<button')
) {
	include_style('form');
}

if (!have_og('og:image')) {
	include_og('og:image', STATIC_HOST.'/logos/partyflock_logo_and_text_darkbg_3400px.png');
	include_og('og:image:width',  '3400');
	include_og('og:image:height', '3400');
}

if (in_content('hilited')) {
	$body = preg_replace_callback('!class="([^"]*\bhilited\b.*?)"!', static fn(array $match): string =>
			str_contains($match[1], 'basehl')
		?	$match[0]
		:	'class="basehl '.$match[1].'"',
	$body);
}

if (!include_js('js/hiliting', JS_INCLUDED)
&&	(	in_content('cbclick')
	||	in_content('radioclick')
	)
) {
	include_js('js/hiliting');
}

$body = preg_replace_callback(
	'!'.
	'(?<open_table><table\s*)'.
	'(?<table_attributes>[^>]*)'.
	'(?<table_contents>(>(?:<caption.*?</caption>\s*)?(?:<tbody[^>]*>\s*)?<tr[^>]*>\s*<td\s+class="field">))'.
	'!s',
	static function(array $table_match): string {
		if (str_contains($table_match['table_attributes'], 'nodyn')) {
			return $table_match[0];
		}
		$updated_table_part = preg_replace('!\bclass="!', 'class="dyntab ', $table_match['table_attributes'], -1, $count);
		if (!$count) {
			$updated_table_part .= ' class="dyntab"';
		}
		return $table_match['open_table'].$updated_table_part.$table_match['table_contents'];
	}, $body
);

if ($body && $have_form) {
	if (in_content('<textarea')) {
		# NOTE: kind of obsolete, remove in due time
		if (!CHROME
		&&	!SAFARI
		&&	!(FIREFOX && BROWSER_VERSION >= 4)
		) {
			include_js('js/textarea-resizer');
		}
		include_js('js/form/textarea');
		load_monospace_font();
	} elseif (preg_match('"\btt\b|<tt"', $body)) {
		load_monospace_font();
	}
	if (in_content('growToFit')) {
		include_js('js/form/growtofit');
		$body = preg_replace_callback('"<textarea([^>]*?)>(.*?)</textarea>"is', static function (array $match): string {
			[$full, $attribs, $content] = $match;
			if (!str_contains($attribs, 'growToFit')) {
				return $full;
			}
			if (!$content
			||	$content[strlen($content) - 1] !== "\n"
			) {
				$content .= "\n";
			}
			return "<textarea$attribs>$content</textarea>";
		}, $body);
	}
	if (in_content('upLite')) {
		include_js('js/form/uplite');
	}

/*	if (iOS && WEBKIT) {
		include_js('js/form/nozoom');
		if (in_content('<label')) {
			// labels on iOS only work when there's an onclick handler, even if it's empty
			$body = preg_replace_callback('"<label([^>]*)>"', static function(array $match): string {
				if ($match[1]
				&&	str_contains($match[1], 'onclick')
				) {
					return $match[0];
				}
				return '<label onclick'.$match[1].'>';
			},$body);
		}
	}*/

	ini_set('pcre.backtrack_limit', 10*1024*1024); # NOSONAR

	$body = preg_replace_callback('"<form.*?</form>"ms', static function(array $form_match): string {
		$body = $form_match[0];
		if (preg_match('!class="[\w\s]*\bfield\b!', $body)) {
			$body = preg_replace_callback('!<td class="([\w\s]*\bfield\b[\w\s]*)">(.*?)</td><td[^>]*>(.*?)</td>!ms', function(array $field_match): string {
				if (!str_contains($field_match[1], 'nowrap')
				&&	preg_match('"(?:<br|<textarea)\b"', $field_match[3])
				) {
					return preg_replace('"\bfield\b"', 'wrap field', $field_match[0]);
				}
				return $field_match[0];
			}, $body);
		}
		return $body;
	}, $body);

	include_js('js/form/validation');
}

echo $section_header;

/*# tracking for adbrothers.nl, run only once per day per ip:cookie combinatioen
$key = CURRENTIPSTR.':'.(defined('CURRENTIDENTID') ? CURRENTIDENTID : 0);
# reset cache at 5:00 UTC every day
$expires = TODAYSTAMP_TZI + ONE_DAY;

if (memcached_add($key, true, $expires)) {
	?><script async src="https://keypush.net/tracking/pf/viewtag.js"></script><?
}*/

robot_action();

if (!isset($og_type)) {
	if ($_REQUEST['sID']) {
		$og_type = match ($_REQUEST['sELEMENT']) {
			'album',
			'city',
			'country',
			'video'			=> $_REQUEST['sELEMENT'],
			'party'			=> 'partyflock:event',
			'albumelement',
			'artist',
			'label',
			'location',
			'music',
			'organization'	=> 'partyflock:'.$_REQUEST['sELEMENT'],
			default			=> 'article',
		};
	} else {
		$og_type = $_SERVER['REQUEST_URI'] === '/' ? 'website' : 'article';
	}
}

include_og('og:type', $og_type);

?><meta property="twitter:card" content="summary" /><?
?><meta property="twitter:site" content="@Partyflock" /><?
if ($_REQUEST['sID']) {
	switch ($_REQUEST['sELEMENT']) {
	case 'news':
	case 'review':
	case 'interview':
	case 'column':
		if ($pstamp = memcached_single($_REQUEST['sELEMENT'], "
			SELECT PSTAMP
			FROM `{$_REQUEST['sELEMENT']}`
			WHERE {$_REQUEST['sELEMENT']}ID = {$_REQUEST['sID']}")
		) {
			?><meta property="article:publisher" content="<?= FULL_HOST ?>/" /><?
			?><meta property="article:published_time" content="<?= gmdate(ISO8601Z, $pstamp) ?>" /><?
		}
		break;
	}
}

$all_head = ob_get_clean();

require_once '_pixel.inc';

ob_start(); # all_body

?><body id="top"<?

if (have_admin()) {
	#$body_classes[] = 'forcescroll';
	$body_classes[] = 'isadmin';
}
if (SMALL_SCREEN) {
	?> data-small-screen="true"<?
	$body_classes[] = 'smallscreen';
}
if (iOS) {
	$body_classes[] = 'iOS';
}
if (SERVER_SANDBOX || SERVER_VIP) {
	$body_classes[] = 'development';
	if (SERVER_SANDBOX) {
		$body_classes[] = 'data-sandbox';
	}
}

$body_classes[] = have_user() ? 'haveuser' : 'haveguest';

if (show_right_towers()) {
	$body_classes[] = 'towertest';
}
if ($overlay && $overlay !== true) {
	$body_classes[] = 'noverflow';
}
if ($_REQUEST['sELEMENT']) {
	?> data-element="<?= $_REQUEST['sELEMENT'] ?>"<?
}
if ($_REQUEST['sID']) {
	?> data-id="<?= $_REQUEST['sID'] ?>"<?
}
if (HOME_THOMAS) {
	?> data-home-thomas<?
}
if (!empty($body_classes)) {
	?> class="<?= implode(' ', $body_classes) ?>"<?
}
?>><?

if ((	SERVER_SANDBOX
	||	SERVER_VIP
	||	CURRENTUSERID === 2269
	)
&&	$_REQUEST['sELEMENT']
&&	is_string($_REQUEST['sELEMENT'])
&&	isset(USE_URLTITLE[$_REQUEST['sELEMENT']])
&&	$_REQUEST['sID']
&&	false !== ($current_title = get_utf8_element_title($_REQUEST['sELEMENT'], $_REQUEST['sID']))
&&	null  !==  $current_title
) {
	$current_url_title = myurlencode($current_title, true);
		$new_url_title = myurlencode($current_title, true, true);
	if ($current_url_title !== $new_url_title) {
		?><pre><?
			?>OLD: <?= escape_specials($current_url_title), "\n"
			?>NEW: <?= escape_specials($new_url_title)
		?></pre><?
	}
}

if (HOME_THOMAS
&&	($bad_files = [
	...glob('/home/<USER>/*/*.inc[a-z]', GLOB_NOSORT),
	...glob('/home/<USER>/*/*.php[a-z]', GLOB_NOSORT),
	...glob('/home/<USER>/*/*/*.inc[a-z]', GLOB_NOSORT),
	...glob('/home/<USER>/*/*/*.php[a-z]', GLOB_NOSORT),
	...glob('/home/<USER>/*/*/*/*.inc[a-z]', GLOB_NOSORT),
	...glob('/home/<USER>/*/*/*/*.php[a-z]', GLOB_NOSORT)])
) {
	?><div<?
	?> style="<?
		?>position: fixed;<?
		?>z-index: 10000000000000;<?
		?>width: 100%;<?
		?>height: 100%;<?
		?>background-color: rgba(0,0,0,.8);<?
		?>padding: <?= $padding ?? 1 ?>em;<?
		?>cursor: pointer;<?
	?>"><?
	?><div style="font-size: 150%;"><?
	?><h1 style="color red;">probably bad files</h1><?
	?><pre><ul><?
	foreach ($bad_files as $file) {
		?><li style="color: orange;"><?= escape_specials($file) ?></li><?
	}
	?></ul><?
	?><div>please run: <span style="color: green;">rembad</span></div><?
	?></pre><?
	?></div><?
	?></div><?
}

require_once '_process_appic_changes.inc';
show_active_item_overlay();

show_cookie_consent();

db_display_top_warning();

if ($overlay
&&	$overlay !== true
) {
	echo $overlay;
}

?><div id="fb-root"></div><?

/*if (NICE_GALLERY) {
	if (!empty($GLOBALS['photo'])) {
		echo $GLOBALS['photo'];
	}
}*/

# OB_START: menu
ob_start();
display_menu($menu_data);
$menu = ob_get_clean();
# OB_END: menu

if (!empty($GLOBALS['__lazy_load'])) {
	require_once '_lazyload.inc';
	include_js('js/lazyload');
	$body = process_lazies($body);
}

echo $premenu;

if (!SMALL_SCREEN && !empty($no_bar) && have_user()) {
	?><div class="sh mpty bold relative"><?
		?><div class="mwrp"><?
		?><div class="grdnt"></div><?
		?><div id="mebox"><?= get_me(2) ?></div><?
		?>&nbsp;<?
		?></div><?
	?></div><?
}

echo $menu, $body;

if (!empty($GLOBALS['__pffboverlay'])) {
	show_pf_fb_overlay();
}

if (class_exists('bubble')) {
	bubble::show_bottom_content();
}
if (!empty($GLOBALS['bottom'])) {
	echo $GLOBALS['bottom'];
	# remove if not used
	mail_log('GLOBALS[bottom] is set', item: $GLOBALS['bottom']);
}

require_once '_resolution.inc';
update_resolution();

include_busy_img(true);

$all_body = ob_get_clean();
# OB_END: all body

replace_pixel_status($all_body);

?><!DOCTYPE html><?

?><html lang="<?= CURRENTLANGUAGE ?>"><?

?><head<?
?> prefix="<?
	$cnt = count($html_prefix);
	foreach ($html_prefix as $ns => $url) {
		echo $ns,': ',$url;
		if (--$cnt) {
			echo ' ';
		}
	}
?>"><?

if (!ROBOT
&&	!HOME_OFFICE
&&	!SERVER_SANDBOX
&&	!SERVER_VIP
#	Add sentry script on 1% of the requests:
&&	!safe_random_int(0, 100)
) {
	?><script src="https://js.sentry-cdn.com/66887c10131cffa82f4d0a1ba37a6100.min.js" crossorigin="anonymous"></script><?
}

# don't leak title information if page is not found, not accessible or there is a server problem

if ($page_problem) {
	[$http_status,,, $desc] = $page_problem;
	if ($desc) {
		mail_log('page_problem', ['desc' => $desc]);
	}
	$title = __('http_status:'.$http_status);
	$entire_title = '<title>Partyflock - '.$title.'</title>';
	$use_title = $title;
} else {
	[$entire_title, $title] = get_page_title_parts();
	$use_title = preg_replace('"^<title>(.*?)</title>$"', '$1', $entire_title);
}

echo $entire_title;

include_og('og:site_name', 'Partyflock');
include_og('og:title', $use_title ?: escape_specials(get_current_title(), isset(USE_UNICODE[$_REQUEST['sELEMENT']])));

if ($_REQUEST['sELEMENT']
&&	$_REQUEST['sID']
&&	$_REQUEST['ACTION'] === 'single'
) {
	$canonical = 'https://partyflock.'.CURRENTDOMAIN.get_element_href($_REQUEST['sELEMENT'],$_REQUEST['sID']);
	if ($_REQUEST['sELEMENT'] === 'user'
	&&	($nick = memcached_nick($_REQUEST['sID']))
	) {
		$html_prefix['profile'] = 'https://ogp.me/ns/profile#';
		$og_type = 'profile';
		include_og('profile:username',escape_specials($nick));
	}
} else {
	$canonical = 'https://partyflock.'.CURRENTDOMAIN.$_SERVER['REQUEST_URI'];
}

include_og('og:url', $canonical);
?><link rel="canonical" hreflang="nl" href="<?= $canonical ?>" /><?
/*?><link rel="alternate" hreflang="nl-be" href="https://partyflock.be<?= $_SERVER['REQUEST_URI'] ?>" /><?*/

echo $all_head;

$head_pixels = get_head_pixels();

fill_ad_placeholders($all_body);

# make sure sections have set a more specific meta description at this point.
if (!have_meta('description')) {
	[,$meta_desc] = get_meta();
	if ($meta_desc) {
		include_meta('description', $meta_desc);
	} elseif (
		!$_REQUEST['ACTION']
	&&  !$_REQUEST['sID']
	&&  ($sectionid = get_sectionid($_REQUEST['sELEMENT']))
	&&  ($meta_desc = memcached_single('metad','SELECT DESCRIPTION FROM metad WHERE SECTIONID='.$sectionid))
	) {
		$key = 'metad:'.$_REQUEST['sELEMENT'].':overview';
		$desc2 = __($key, RETURN_FALSE);
		include_meta('description', $meta_desc = $desc2 === $key ? escape_specials($meta_desc) : $desc2);

	} else {
		# default description
		include_meta('description', __('metad:home:overview', DO_UBB));
	}
}

include_meta();
include_og();
include_head();
include_style(true);
include_js(true);
echo $head_pixels;

?></head><?

show_non_head_pixels();

echo $all_body;

if (!ROBOT
&&	LOGQUERIES
&&	$_REQUEST['sELEMENT'] !== 'rss'
) {
	display_querylog();
}

?></body></html><?

require_once '_pagehit.inc';
count_pagehit();

# try to find new robots once in a while:

if (!ROBOT
&&	$_SERVER['REMOTE_ADDR'] === '2001:4860:7:61f::ff'
&&	memcached_add('google.agent.detect.'.$_SERVER['REMOTE_ADDR'], ONE_HOUR)
) {
	mail_log(
		'unrecognized google user agent: '.USER_AGENT."\n".
		'userid: '.CURRENTUSERID."\n".
		'identid: '.CURRENTIDENTID,
		['_SERVER' => $_SERVER, '_COOKIE' => $_COOKIE]
	);
}

if ($_REQUEST['sELEMENT'] === 'user'
&&	in_array($_REQUEST['ACTION'], [
	'login',
	'setuser',
])) {
	db_insupd('login_hits', "
	INSERT INTO login_hits SET
		ACTION	= '".addslashes($_REQUEST['ACTION'])."',
		IPBIN	= '".addslashes(CURRENTIPBIN)."',
		HITS	= 1
	ON DUPLICATE KEY UPDATE HITS = HITS + 1");
}

/*function main_show_whos_online(): void {
	$onlines = memcached_rowuse_array('online',"
		SELECT TYPE, CNT, STAMP
		FROM online",
		30
	);
	if (!$onlines) {
		return;
	}
	foreach ($onlines as $info) {
		if ($info['STAMP'] < CURRENTSTAMP - 180) {
			continue;
		}
		$cnts[$info['TYPE']] = $info['CNT'];
	}
	if (!isset($cnts)) {
		return;
	}
	$whoslist[] =
		!empty($cnts['members'])
	?	'<a href="/user/showonline'.(!have_user() ? '/suitable' : null).'">'.$cnts['members'].' '.element_name('member',$cnts['members']).'</a>'
	:	__('banneraction:no_members');
	if (have_user()
	&&	have_admin()
	&&	have_buddies()
	) {
		if ($onlineids = onlineids()) {
			require_once '_buddies.inc';
			if (($buddies = _buddies_full_hash(CURRENTUSERID))
			&&	($diff = array_intersect_key($buddies,$onlineids))
			) {
				$buddycnt = count($diff);
				$whoslist[] = '<a href="/user/showonline?NOMENU;ONLYBUDDIES">'.$buddycnt.' '.element_name('buddy',$buddycnt).'</a>';
			}
		}
	}
	if (isset($cnts['guests'])) {
		$whoslist[] = $cnts['guests'] ? $cnts['guests'].' '.element_name('guest',$cnts['guests']) : __('banneraction:no_guests');
	}
	if (!empty($cnts['mobiles'])
	&&	have_super_admin()
	) {
		$whoslist[] = $cnts['mobiles'].' '.element_name('smallscreen(online)',$cnts['mobiles']);
	}
	if (isset($whoslist)) {
		?><br /><?
		echo implode(', ',$whoslist),' ',__('status:online');
	}
}*/

function main_display_info(): void {
	if (ROBOT) {
		return;
	}
	$argsep = str_contains($_SERVER['REQUEST_URI'],'?') ? ';' : '?';
	?><nav><?
	if (!have_post()
	&&	(	have_admin()
		||	SERVER_SANDBOX
		)
	&&	!isset($_REQUEST['NOMEMCACHE'])
	) {
		?><a href="<?= $_SERVER['REQUEST_URI'],$argsep ?>NOMEMCACHE"><?= __('banneraction:without_cache') ?></a><?
		?> <?= MIDDLE_DOT_ENTITY ?> <?
	}
	if (!SMALL_SCREEN
	&&	!have_post()
	) {
		# FIXME: replace smallscreen arg if exists
		?><a href="/smallscreen.act?yes"><?= element_name('mobile_site') ?></a> <?= MIDDLE_DOT_ENTITY ?> <?
	}
	?><a href="/contact?OVERVIEW"><?= __('section:contact') ?></a><?
	?></nav><?
}

function get_language_switch(): ?string {
	if (!($countryid = memcached_single('language','SELECT FLAGCOUNTRYID FROM language WHERE LANGID='.CURRENTLANGID, TEN_MINUTES))) {
		return null;
	}
	require_once '_countryflag.inc';
	ob_start();

	$flagstr = get_country_flag($countryid);

	?><a class="seemtext ib<?
	if ($_REQUEST['sELEMENT'] === 'language'
	&&	empty($_REQUEST['sID'])
	) {
		?> light<?
	}
	?>"<?
	if (!str_contains($flagstr, '<img')) {
		?> style="height: 20px;"<?
	}
	?> href="/language"><?= $flagstr ?></a><?
	return ob_get_clean();
}

function get_theme_switch(): string {
	ob_start();
	?><span class="ib icon"><?
	$icons = ['sun','moon'];
	$titles = [__('action:go_light'),__('action:go_dark')];
	$active = LITE ? 1 : 0;
	$inactive = LITE ? 0 : 1;

	?><img<?
	?> id="tsw"<?
	?> onclick="switchTheme(this)"<?
	?> data-title="<?= $titles[$inactive] ?>"<?
	?> title="<?= $titles[$active] ?>"<?
	?> data-other="<?= STATIC_HOST ?>/images/<?= $icons[$inactive],is_high_res() ?>.png"<?
	?> data-theme="<?= CURRENTTHEME ?>"<?
	?> class="abs icon ptr"<?
	?> src="<?= STATIC_HOST ?>/images/<?= $icons[$active],is_high_res() ?>.png"<?
	?>><?
	?></span><?
	return ob_get_clean();
}

function check_page_comments_action(): void {
	require_once '_comment.inc';
	if (preg_match('"^(.*?/('.implodekeys('|', commentables()).')/\d+)(/(?:PAGE|page)/.*)$"', $_SERVER['REQUEST_URI'], $match)) {
		$newuri = $match[1].'/comments'.$match[3];
		if (($ref = $_SERVER['HTTP_REFERER'] ?? null)
		&&	!str_contains($ref, 'www.google.')
		) {
			mail_log('have_page but single (redirected to '.$newuri.')', get_defined_vars());
		}
		require_once '_nocache.inc';
		send_no_cache_headers();
		header('Location: https://'.$_SERVER['HTTP_HOST'].$newuri, true, 301);
		exit;
	}
	switch($_REQUEST['sELEMENT']) {
	case 'compo':
	case 'flock':
	case 'flocktopic':
	case 'forum':
	case 'gallery':
		break;
	default:
		mail_log('have_page but single (NOT redirected)', get_defined_vars());
	}
}

function get_contents(): string {
	ob_start();

	require_once '_profile.inc';

	global $section,$only_if_ok_header;

	if (isset($section)) {
		$section->display_body();
	} else {
		$status = 0;

/*		if ($disallow_info = agenda_disallowed($_REQUEST['sELEMENT'], $_REQUEST['sID'])) {
			[$disallow, $type, $expires] = $disallow_info;
			$after = $expires - CURRENTSTAMP;
			$status = 429;
			header('Retry-After: '.$after);

			$page_explain = '<div class="body block">'.__(
				$disallow === DISALLOW_TOR
			?	'error:no_tor_LINE'
			:	(	$disallow === DISALLOW_HARVEST
				?	'error:no_harvest_LINE'
				:	'error:no_proxy_LINE'
				),
				DO_NL2BR | DO_COMBINE_2BR | DO_UBB
			).'</div><div>'.__('disallowuser:error:is_incorrect_LINE',DO_UBB | DO_NL2BR).'</div>';

			$only_if_ok_header = null;
			page_problem($status,null,0,$page_explain ?? null);
		} else {*/
			if (preg_match('"^/[a-z]+(?<!language)/0\b"',$_SERVER['REQUEST_URI'],$match)) {
				# language langid=0 exists, so languange/0/set must function
				page_problem(404);
			} elseif (!function_exists('display_body')) {
				mail_log('display_body does not exist');
			} else {
				display_body();
			}
//		}
	}
	return ob_get_clean();
}

function determine_banners(): void {
	global	$ad, $ad2,
			$towerad, $towerad2, $towerad3,
			$miniad, $miniad_agenda, $miniad_delayed,
			$boxad, $boxad_small, $boxad_late, $boxad_random, $boxad_three, $boxad_four,
			$rightower, $rightower2, $rightower3, $rightower4, $rightower5, $rightower6,
			$overlayad,
			$subbar;

	/** @noinspection NestedAssignmentsUsageInspection */
	$ad =
	$ad2 =
	$towerad =
	$towerad2 =
	$towerad3 =
	$miniad =
	$miniad_agenda =
	$miniad_delayed =
	$boxad =
	$boxad_small =
	$boxad_late =
	$rightower =
	$rightower2 =
	$rightower3 =
	$rightower4 =
	$rightower5 =
	$subbar =
	$overlayad = null;

	if (no_banners()) {
		return;
	}
	$element = $_REQUEST['sELEMENT'];

	if (!SMALL_SCREEN) {
		$ad_spec = [
			'ad'				=> [ADPOS_TOPBOTTOM,	0],
			'towerad'			=> [ADPOS_LEFTMENU,		0],
			'towerad2'			=> [ADPOS_RIGHT_TOWER,	 .2],
			'towerad3'			=> [ADPOS_RIGHT_TOWER,	 .5],
			'miniad'			=> [ADPOS_MENUMINI,		0],
			'miniad_agenda'		=> [ADPOS_MENUMINI,		0],
			'miniad_delayed'	=> [ADPOS_MENUMINI,		 .1],
		];
		if (show_double_leaderboards()) {
			$ad_spec['ad2'] = [ADPOS_TOPBOTTOM, .1];
		}
	}
	if ($element !== 'contact'
	&&	$element !== 'ticket'
	) {
		$ad_spec['subbar'] = [ADPOS_SUBBAR, SMALL_SCREEN ? .1 : 0];
	}
	$ad_spec['overlayad'] = [ADPOS_OVERLAY, 0];

	if (show_right_towers()) {
		$ad_spec = [
			...$ad_spec,
			'rightower'		=> [ADPOS_RIGHT_TOWER, 0],
			'rightower2'	=> [ADPOS_RIGHT_TOWER, 0],
			'rightower3'	=> [ADPOS_RIGHT_TOWER, 0],
			'rightower4'	=> [ADPOS_RIGHT_TOWER,  .1],
			'rightower5'	=> [ADPOS_RIGHT_TOWER,  .2],
			'rightower6'	=> [ADPOS_RIGHT_TOWER,  .5]
		];
	}

	$done_position = [];
	$ads = [];
	$ad_spec = shuffle_assoc($ad_spec);

	foreach ($ad_spec as $ad_name => [$position, $min_missing_mult]) {
		if (isset($done_position[$position])) {
			continue;
		}
		if (!($tmp_ad = obtain_ad($position, min_missing: $min_missing_mult * 500, min_missing_mult: $min_missing_mult))) {
			$done_position[$position] = true;
			continue;
		}
		if ($ad_name === 'miniad'
		&&	$tmp_ad['TAKEOVER']
		) {
			# repeat mini-ad three times during takeover
			unset(	$ad_spec['mini_agenda'],
					$ad_spec['mini_delayed']);
			$ads[$position][] = $tmp_ad;
			$ads[$position][] = $tmp_ad;
		}
		$ads[$position][] = $tmp_ad;
	}

	mt_srand();
	foreach ($ads as /* $position => */ &$adlist) {
		safe_shuffle($adlist);
	}
	unset($adlist);

	foreach ($ad_spec as $ad_name => [$position/*, $min_missing_mult */]) {
		if (empty($ads[$position])) {
			continue;
		}
		$tmp_ad = array_pop($ads[$position]);
		$$ad_name = $tmp_ad;
	}
	if (SMALL_SCREEN) {
		if ($subbar
		&&	(	empty($subbar['TYPE'])
			||	$subbar['TYPE'] !== 'upload'
			)
		) {
			$subbar = null;
		}
	} elseif ($subbar) {
		# subbar moving only works on largescreen

		include_js('js/mover');
	}
}

function include_busy_img($show = null): void {
	static $__show = false;
	if ($show === null) {
		$__show = true;
		return;
	}
	if ($show && $__show) {
		?><div id="bb"><?
		?><img<?
		?> id="busyimg"<?
		?> src="<?= STATIC_HOST ?>/loader/clock_<?= CURRENTTHEME, is_high_res() ?>.png"<?
		?> class="hidden" /><?
		?></div><?
	}
}

function in_content(array|string $find): bool {
	foreach ([
		'premenu',
		'body',
	] as $key) {
		if (empty($GLOBALS[$key])) {
			continue;
		}
		if (str_contains($GLOBALS[$key], $find)) {
			return true;
		}
	}
	return false;
}
