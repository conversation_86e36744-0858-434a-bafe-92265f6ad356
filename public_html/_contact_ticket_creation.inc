<?php

require_once '_ubb_preprocess.inc';
require_once '_attachments.inc';

function contact_commit_message($direction,$withuserid,$withemail = null) {
	if (false === ($ticketid = db_single('contact_ticket_message', "
		SELECT TICKETID
		FROM contact_ticket_message
		WHERE DIRECTION	  = '$direction'
		  AND USERID_FROM = ".CURRENTUSERID."
		  AND TICKETID	  = {$_REQUEST['sID']}
		  AND FORMSTAMP	  = {$_POST['FORMSTAMP']}
		LIMIT 1",
		DB_USE_MASTER
	))) {
		return 0;
	}
	if ($ticketid) {
		register_warning('contact_ticket:warning:already_sent_LINE', ['TICKETID' => $ticketid]);
		return 0;
	}
	if (!db_insert('contact_ticket_message','
		INSERT INTO contact_ticket_message SET
			TEMPLATE		= "'.(empty($_SERVER['ticket_TEMPLATE']) ? null : addslashes($_SERVER['ticket_TEMPLATE'])).'",
			BODY			= "'.addslashes($_POST['MSGBODY']).'",
			ISMAIL			= b\''.($withemail ? 1 : 0).'\',
			BODYUTF8		= "",
			DIRECTION		= "'.$direction.'",
			USERID_FROM		= '.($userid_from = CURRENTUSERID === 1 ? 0 : CURRENTUSERID).',
			CSTAMP			= '.CURRENTSTAMP.',
			TICKETID		= '.$_REQUEST['sID'].',
			ANSWERDATAID	= '.($direction === 'touser' ? have_idnumber($_POST,'ANSWERDATAID') : 0))
	) {
		return 0;
	}
	return db_insert_id();
}

function create_message_and_attachments(int $ticketid, string $mailstr, int $ctmsgid, ?string $email = null, ?string $quotestr = null, string $cc = '') {
	$ticket = db_single_assoc('contact_ticket','
		SELECT WITHUSERID,ELEMENT,ID
		FROM contact_ticket
		WHERE TICKETID='.$ticketid,
		DB_FORCE_MASTER
	);

	$atts = get_attachments();

	set_memory_limit(256 * MEGABYTE);

	if ($mailstr || $quotestr) {
		$mailstr = plain_text($mailstr);

		if ($ticket) {
			$links = [];
			if ($ticket['WITHUSERID']) {
				$links['contact_ticket'][$ticketid] = $ticketid;
			}
			if (($element = $ticket['ELEMENT'])
			&&	($id = $ticket['ID'])
			) {
				$links[$element][$id] = $id;
			}
			$connects = db_same_hash('connect','
				SELECT ASSOCTYPE, ASSOCID
				FROM connect
				WHERE MAINTYPE="contact_ticket"
				  AND MAINID='.$ticketid
			);
			if ($connects) {
				foreach ($connects as $element => $ids) {
					foreach ($ids as $id) {
						$links[$element][$id] = $id;
					}
				}
			}
			if ($links) {
				$refs = [];
				foreach ($links as $element => $ids) {
					switch ($element) {
					case 'camerarequest':
						$element = 'camera';
						break;
					case 'deaduser':
					case 'helpdesk':
						$element = 'user';
						break;
					case 'contact_ticket':
						$withuserids = db_simple_hash('contact_ticket','
							SELECT TICKETID,WITHUSERID
							FROM contact_ticket
							WHERE TICKETID IN ('.implode(',', $ids).')'
						);
						$element = 'ticket';
						break;
					}
					foreach ($ids as $id) {
						if ($element === 'ticket'
						&&	($withuserids[$id] ?? null) !== CURRENTUSERID
						) {
							# only withuserid can view tickets
							continue;
						}
						$href = get_element_href($element, $id);
						$refs[] =
							$href
						?	'https://partyflock.'.CURRENTDOMAIN.$href
						:	'https://partyflock.'.CURRENTDOMAIN.'/'.$element.':'.$id;
					}
				}
				if ($refs) {
					$mailstr .= "\r\n\r\nLinks:\r\n".implode("\r\n", $refs);

/*					ob_start();
					?>added links to mail in ticket <?= $ticketid ?>:<? echo "\n\n";
					echo implode("\n", $refs);
					error_log(ob_get_clean(), 1, '<EMAIL>');*/
				}
			}
		}
		if (str_contains($mailstr, 'http')) {
			$mailstr = preg_replace('"https?://(?:vip\.)?partyflock\.(\w)"', 'https://partyflock.$1', $mailstr);
			$mailstr = preg_replace('"https?://(\w+)\.partyflock\.nl"', 'https://$1.partyflock.nl', $mailstr);
		}

		if ($quotestr) {
			$mailstr .= "\r\n\r\n".$quotestr;
		}

		$mailstr = win1252_to_utf8($mailstr);
		$mailstr = html_entity_decode($mailstr, ENT_HTML5, 'UTF-8');
	}

	$headers = ['MIME-Version' => '1.0'];

	if ($cc) {
		$headers['CC'] = $cc;
	}

	if (!$atts) {
		return $mailstr ? [
			quoted_printable_encode($mailstr),$headers + [
				'Content-Transfer-Encoding'	=> 'quoted-printable',
				'Content-Type'			=> 'text/plain; charset=UTF-8',
			]
		] : [null,[]];
	}
	if ($mailstr) {
		$boundary = 'muhahaha'.uniqid();
		$msg =
			'--'.$boundary."\r\n".
			"Content-Transfer-Encoding: quoted-printable\r\n".
			"Content-Type: text/plain; charset=UTF-8\r\n\r\n".
			quoted_printable_encode($mailstr)."\r\n\r\n";
	}
	require_once '_mimetype.inc';
	$done = false;
	foreach ($atts as $att) {
		if (!$done && (empty($att['tmp_name']) && empty($att['tmp_data']))) {
			error_log_r(debug_backtrace(),'backtrace');
			error_log_r($_FILES,'_FILES for ctmsgid:'.$ctmsgid);
			error_log_r($_REQUEST,'_REQUEST');
			$have = require_file($att);
			error_log_r($have,'require_file_result');
			$done = true;
		}
		$data = isset($att['tmp_data']) ? $att['tmp_data'] : file_get_contents($att['tmp_name']);
		if (!$data) continue;
		$mimetype = isset($att['tmp_data']) ? $att['type'] : mimetype_file($att['tmp_name'], $att['name'], 'application/octet-stream');
		$crc = crc32($data);
		$len = strlen($data);

		$att['name'] = html_entity_decode($att['name'], ENT_HTML5, 'UTF-8');
		if ('Windows-1252' === mb_detect_encoding($att['name'], 'Windows-1252, UTF-8')) {
			$att['name'] = win1252_to_utf8($att['name']);
		}

		if (!db_insert('contact_ticket_attachment_meta','
			INSERT INTO contact_ticket_attachment_meta SET
				MIMETYPE	="'.addslashes($mimetype).'",
				SIZE		='.$len.',
				CRC		='.$crc)
		||	!($dataid = db_insert_id())
		||	!db_insert('contact_ticket_attachment','
			INSERT INTO contact_ticket_attachment SET
				DATAID		='.$dataid.',
				CTMSGID		='.$ctmsgid.',
				FILENAME	="'.addslashes($att['name']).'",
				CSTAMP		='.CURRENTSTAMP)
		||	!db_insert('contact_ticket_attachment_data', '
			INSERT INTO data_db.contact_ticket_attachment_data SET
				DATA	="'.addslashes($data).'",
				DATAID	='.$dataid)
		) {
			register_warning('contact:warning:attachment_not_stored');
		}
		if (!$mailstr) {
			continue;
		}
		$msg .=
			"--$boundary\r\n".
			"Content-Type: $mimetype; name=\"".($fname = str_replace('"','\\"',$att['name']))."\"\r\n".
			"Content-Transfer-Encoding: base64\r\n".
			"Content-Disposition: attachment; filename=\"".$fname."\"\r\n\r\n".
			wordwrap(base64_encode($data),72,"\r\n",true)."\r\n\r\n";
	}
	if ($mailstr) {
		$msg .= '--'.$boundary."--";
		return [
			$msg,$headers + [
				'Content-Type'	=> 'multipart/mixed; boundary="'.$boundary.'"',
		]];
	}
	return [null,[]];
}

function combine_mail_headers($args) {
	$res = [];
	foreach ($args as $key => $val) {
		$res[] = $key.': '.$val;
	}
	return $res;
}
