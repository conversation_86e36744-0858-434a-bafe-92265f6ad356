<?php

const CLOSE_CONTEST_DAYS			= 4;
const CLOSE_CONTEST_DAYS_CONNECTED	= 5;	# for orgs adding their own contests

const MAX_SCANCODES_RELEASE_HOUR	= 48;

function preamble(): void {
	$new_uri = str_replace('/contender', '/contestant', $_SERVER['REQUEST_URI'], $reps);
	if ($reps) {
		moved_permanently($new_uri);
	}
}

function display_title(): void {
	switch ($_REQUEST['ACTION']) {
	case null:
		echo __('contest:page_title');
		break;

	case 'organization':
		echo element_plural_name('contest');
		if ($_REQUEST['subID']) {
			?> <?= MIDDLE_DOT_ENTITY ?> <?
			echo escape_utf8(get_element_title('organization', $_REQUEST['subID']));
			?> <?= MIDDLE_DOT_ENTITY ?> <?
			if ($_REQUEST['SUBACTION'] === 'all') {
				echo element_name('all');
			} else {
				echo __('status:open');
			}
		}
		break;
	}
}

function display_header(): void {
	require_once '_feed.inc';
	show_feed('contest', FEED_HEADER);

	if (($contestid = $_REQUEST['sID'])
	&&	($contest = memcached_contest($contestid))
	&&	(	have_admin('contest')
		||	$contest['PSTAMP'] <= CURRENTSTAMP
		&&	$contest['ACTIVE']
		)
	) {
		require_once '_contest.inc';
		extract($contest);
		ob_start();
		switch ($TYPE) {
		case 'guestlist':
		case 'eticket':
		case 'freeticket':
		case 'scancode':
			if ($contest['PARTYID']) {
				require_once '_appic.inc';
				if ($party = memcached_party_and_stamp($contest['PARTYID'])) {
					$party['NAME_FOR_APPIC'] = null;
				}
				[$appic_name, $year, $city] = get_appic_titles($party);
			}

			echo __C('feed:contest:win_free_entrance_TITLE', DO_UBBFLAT, [
				'NAME'		=> $contest['NAME'],
				'PARTYID'	=> $PARTYID ?? 0,
				'YEAR'		=> $year ?? 0,
				'CITY'		=> $city ?? null,
			]);
			break;
		default:
			echo __C('feed:contest:win_something_TITLE', DO_UBBFLAT, ['NAME' => $CONTEST_NAME]);
			break;
		}
		echo ": ";

		if ($VISIBLE_GROUPS) {
			if ($VISIBLE_GROUPS != 1) {
				echo $VISIBLE_GROUPS, MULTIPLICATION_SIGN_ENTITY, ' ', $AMOUNT;
			} else {
				echo $AMOUNT, MULTIPLICATION_SIGN_ENTITY;
			}
			?> <?
		}
		echo contest_get_type($AMOUNT,$TYPE);

		$data = ob_get_clean();

		include_meta('description', $data);
	}
}

function perform_commit(): ?bool {
	switch ($_REQUEST['ACTION']) {
	default:
	case null:				return null;
	case 'join':			return contest_join();
	case 'leave':			return contest_leave();
	case 'markread':		return contest_mark_read();
	case 'commit':			return contest_commit();
	case 'make-winners':		return contest_make_winners();
	case 'msgwins':			return contest_message_winners();
	case 'allatonce':		return contest_set_closed(1) && contest_make_winners();
	case 'open':			return contest_set_closed(0);
	case 'close':			return contest_set_closed(1);
	case 'activate':		return contest_set_active(1);
	case 'deactivate':		return contest_set_active(0);
	case 'blacklistadd':	return contest_blacklist_add();
	case 'blacklistremove':	return contest_blacklist_remove();
	case 'choosewins':		return contest_choose_wins();
	case 'remove-winners':	return contest_remove_winners();
	case 'remove':			require_once '_remove.inc'; return remove_element();
	case 'combine':			require_once '_combine.inc'; return combine_element();
	case 'movechance':		return contest_move_chance();
	case 'fix':				return fix_contest_code();
	}
}

function display_body(): void {
	switch ($_REQUEST['ACTION']) {
	default:		not_found(); return;
	case 'remove':
	case null:
	case 'beach':
	case 'outdoor':		contest_display_overview(); return;
	case 'closed':		contest_display_closed(); return;
	case 'unfinished':	contest_display_unfinished(); return;
	case 'needclosure':	contest_display_need_closure(); return;
	case 'list':		contest_display_list(); return;
	case 'new':		contest_display_overview(true); return;
	case 'contestants':
	case 'single':
	case 'comments':
	case 'comment':
	case 'join':
	case 'leave':
	case 'msgwins':
	case 'open':
	case 'close':
	case 'combine':
	case 'activate':
	case 'deactivate':
	case 'choosewins':
	case 'remove-winners':
	case 'movechance':
	case 'fix':				contest_display_single(); return;
	case 'commit':			empty($GLOBALS['commitresult']) ? contest_display_form() : contest_display_single(); return;
	case 'organization':	contest_display_organization(); return;
	case 'form':			contest_display_form(); return;
	case 'make-winners':
	case 'show-winners':
	case 'allatonce':
	case 'blacklistadd':	contest_display_winners(); return;
	case 'top':				contest_display_top(); return;
	case 'recent':			contest_display_recent(); return;
	case 'winmsg':
	case 'markread':		contest_display_winmsg(); return;
	case 'participations':	contest_display_participations(); return;
	case 'blacklistremove':
	case 'blacklist':		contest_display_blacklist(); return;
	case 'blacklistarchive':contest_display_blacklist_archive(); return;
	case 'choosewinsform':	contest_display_choosewinsform(); return;
	case 'search':			contest_display_search(); return;
	case 'searchresult':	contest_display_search_result(); return;
	case 'combinewith':		require_once '_combine.inc'; show_combine_with(); return;
#	case 'connected':		return contest_display_connected();
	case 'scancodes':		contest_display_scancodes(); return;
	}
}

function contest_move_chance(): void {
	if (!require_user()
	||	!($contestid = require_idnumber($_REQUEST,'sID'))
	) {
		return;
	}
	$unused = db_simple_hash(['contestresponse', 'contest', 'contest_extra_chance', 'user', 'targetsmsent', 'targetsms'],'
		SELECT DISTINCT contest_extra_chance.MESSAGEID, contestresponse.CONTESTID
		FROM contestresponse
		JOIN contest USING (CONTESTID)
		JOIN contest_extra_chance USING (CONTESTID)
		JOIN targetsmsent ON targetsmsent.REPLYTO = contest_extra_chance.MESSAGEID
		JOIN targetsms ON targetsms.MESSAGEID = targetsmsent.REPLYTO
		JOIN user ON SENDTO=CLEANPHONE AND user.USERID = contestresponse.USERID
		LEFT JOIN contestresponse AS winner ON winner.WINSTATUS = "winner" AND winner.CONTESTID = contestresponse.CONTESTID
		LEFT JOIN party ON party.PARTYID = contest.PARTYID
		WHERE contestresponse.USERID = '.CURRENTUSERID.'
		  AND (		party.PARTYID IS NULL
		 	OR	party.STAMP < '.CURRENTSTAMP.'
		)
		  AND MOVEDTO = 0
		  AND RECEIVED != 0
		  AND STATUS = 0
		  AND CLOSED
		  AND (contest.TOLD = 0 OR ISNULL(winner.CONTESTID))'
	);
	if ($unused === false) {
		return;
	}
	if (!$unused) {
		register_warning('contest:warning:no_unused_extra_chance_LINE');
		return;
	}
	foreach ($unused as $messageid => $movedfrom) {
		$setlist[] = '('.$messageid.', '.$contestid.', '.$movedfrom.')';
		$wherelist[] = 'MESSAGEID = '.$messageid.' AND CONTESTID = '.$movedfrom;
	}
	if (!db_insert('contest_extra_chance','
		INSERT IGNORE INTO contest_extra_chance (MESSAGEID, CONTESTID, MOVEDFROM)
		VALUES '.implode(',', $setlist))
	||	!db_update('contest_extra_chance','
		UPDATE contest_extra_chance SET MOVEDTO = '.$contestid.'
		WHERE '.implode(' OR ', $wherelist))
	) {
		return;
	}
}

function contest_display_connected() {
	$elems = am_employee();
	if (!$elems) {
		no_permission();
		return;
	}

	layout_open_menu();
	layout_menuitem(Eelement_name('overview'),'/contest/connected',$_REQUEST['ACTION'] === 'connected');
	layout_close_menu();

	layout_open_menu();
	layout_menuitem(__C('action:add'),'/contest/form');
	layout_close_menu();

	require_once '_contestlist.inc';
	$contestlist = new _contestlist;
	$contestlist->connected_to_me();
	$contestlist->no_pages = true;
	$contestlist->order_newest_publication_first();
	$contestlist->query();
	$contestlist->display();
}

function contest_set_active(int $setting): void {
	if (!require_admin('contest')
	||	!($contestid = require_idnumber($_REQUEST, 'sID'))
	||	!db_insert('contest_log', '
		INSERT INTO contest_log
		SELECT * FROM contest
		WHERE CONTESTID = '.$contestid.'
		  AND ACTIVE != '.$setting)
	||	!db_update('contest', '
		UPDATE contest SET
			MSTAMP	= '.CURRENTSTAMP.',
			MUSERID	= '.CURRENTUSERID.',
			ACTIVE	= '.$setting.'
		WHERE CONTESTID = '.$contestid.'
		  AND ACTIVE != '.$setting)
	) {
		return;
	}
	register_notice($setting ? 'contest:notice:activated_LINE' : 'contest:notice:deactivated_LINE');
	if ($setting
	&&	($pstamp = db_single('contest','SELECT PSTAMP FROM contest WHERE CONTESTID = '.$contestid))
	) {
		require_once '_notify.inc';
		notify_register_have(NOTIFY_CONTEST, $pstamp);
	}
}

function contest_mark_read(): void {
	if (!require_idnumber($_REQUEST, 'sID')
	||	!require_user()
	||	!db_update('contestwinmessage','
		UPDATE contestwinmessage SET READM = '.CURRENTSTAMP.'
		WHERE READM = 0
		  AND USERID = '.CURRENTUSERID.'
		  AND CONTESTID = '.$_REQUEST['sID'])
	) {
		return;
	}
}

function contest_blacklist_add(): void {
	if (!require_admin('contest')
	||	!($contestid = require_idnumber($_REQUEST, 'sID'))
	||	!($userids = require_number_array($_POST, 'USERIDS') ? $_POST['USERIDS'] : false)
	) {
		return;
	}
	require_once '_contestblacklist.inc';
	$blacklisted = blacklist_contest_winners($contestid, $userids);
	if ($blacklisted !== false) {
		register_notice('contest:notice:blacklisted_LINE', ['CNT' => $blacklisted]);
	}
}

function contest_blacklist_remove(): void {
	if (!require_admin('contest')) {
		return;
	}
	if (empty($_POST['BLACKLIST'])) {
		return;
	}
	$cnt = 0;
	foreach ($_POST['BLACKLIST'] as $blacklist) {
		[$contestid, $userid] = explode(',', $blacklist);
		if (!is_number($contestid)
		||	!is_number($userid)
		) {
			register_error('contest:error:invalid_value_for_blacklist_LINE', ['VALUE' => $blacklist]);
			continue;
		}
		$remlist[] = '(CONTESTID = '.$contestid.' AND USERID = '.$userid.')';
		++$cnt;
	}
	if (isset($remlist)
	&&	!db_delete('contestblacklist','
		DELETE FROM contestblacklist 
		WHERE '.implode(' OR ', $remlist))
	) {
		return;
	}
	register_notice('contest:notice:delisted_LINE', ['CNT' => $cnt]);
}

function contest_join() {
	require_once '_contestparticipation.inc';
	if (!require_idnumber($_REQUEST,'sID')
	||	!require_user()
	||	!require_referer()
	) {
		return;
	}
	// FIXME: check realname gebeuren hier ook
	//		en check ook blacklist gebeuren en overlap

	// check whether contest is closed

	$contestid = $_REQUEST['sID'];
	$contest = db_single_assoc(
		['contest','party'],'
		SELECT	CONTESTID,TYPE,CLOSED,ACTIVE,contest.PSTAMP,PARTYID,CLOSE_STAMP,ONLYPROMO,ADDGOING,
				party.MIN_AGE, party.MIN_AGE_FEMALE, party.MAX_AGE,STAMP,NOTIME,DURATION_SECS
		FROM contest
		LEFT JOIN party USING (PARTYID)
		WHERE CONTESTID = '.$contestid
	);
	if ($contest === false) {
		return;
	}
	if (!$contest) {
		register_error('contest:error:nonexistent_LINE',['ID'=>$contestid]);
		return;
	}
	if (!may_participate($contest, CURRENTUSERID, $errstr)) {
		if ($errstr) {
			_error($errstr);
		}
		return;
	}
	require_once '_contestjoin.inc';
	join_contest($contest, CURRENTUSERID);
}
function contest_leave() {
	if (!require_idnumber($_REQUEST,'sID')
	||	!require_user()
	) {
		return;
	}

	// check whether contest is closed

	$closed = db_single('contest','SELECT CLOSED FROM contest WHERE CONTESTID = '.$_REQUEST['sID']);
	if (query_failed()) {
		return;
	}
	if ($closed) {
		_error('Je kunt je deelname niet meer intrekken aangezien de actie al gesloten is!');
		return;
	}
	if (!db_insert('contestresponse_log', '
		INSERT INTO contestresponse_log
		SELECT *, '.CURRENTSTAMP.' FROM contestresponse
		WHERE USERID = '.CURRENTUSERID.'
		  AND CONTESTID = '.$_REQUEST['sID'])
	||	!db_delete('contestresponse', '
		DELETE FROM contestresponse
		WHERE USERID = '.CURRENTUSERID.'
		  AND CONTESTID = '.$_REQUEST['sID'])
	) {
	}
	memcached_delete('contestcounts:'.CURRENTUSERID);
}
function contest_set_closed(int $setting): bool {
	if (!($contestid = require_idnumber($_REQUEST, 'sID'))
	||	!require_admin('contest')
	||	!db_replace('contest_log', '
		INSERT INTO contest_log
		SELECT * FROM contest
		WHERE CONTESTID = '.$contestid.'
		  AND CLOSED != '.$setting)
	||	!db_update('contest','
		UPDATE contest SET
			CLOSED	= '.$setting.',
			MSTAMP	= '.CURRENTSTAMP.',
			MUSERID	= '.CURRENTUSERID.'
		WHERE CONTESTID	= '.$contestid.'
		  AND CLOSED != '.$setting)
	) {
		return false;
	}
	return true;
}
function contest_display_blacklist() {
	if (!require_admin('contest')) {
		return;
	}
	layout_section_header(Eelement_name('blacklist'));

	$action = $_REQUEST['ACTION'];
	layout_open_menu();
	layout_menuitem(Eelement_name('blacklist'), '/contest/blacklist',		$action === 'blacklist');
	layout_menuitem(Eelement_name('archive'),   '/contest/blacklistarchive', $action === 'blacklistarchive');
	layout_close_menu();

	$blacklisted = db_rowuse_array(['contestblacklist', 'user_account'], '
		SELECT	contestblacklist.USERID,
			contestblacklist.STAMP,
			STOPSTAMP,
			contestblacklist.CONTESTID,
			NICK
		FROM contestblacklist
		JOIN user_account USING (USERID)
		WHERE contestblacklist.STAMP <= '.CURRENTSTAMP.'
		  AND contestblacklist.STOPSTAMP > '.CURRENTSTAMP.'
		ORDER BY NICK'
	);
	if ($blacklisted === false) {
		return;
	}
	if (!$blacklisted) {
		?><div class="block"><?= __('contest:info:nobody_on_the_blacklist_LINE') ?></div><?
		return;
	}
	string_sort($blacklisted, 'NICK');

	?><form<?
	?> accept-charset="utf-8"<?
	?> onsubmit="return submitForm(this)"<?
	?> method="post"<?
	?> action="/contest/blacklistremove"><?

	layout_open_box('contest');
	layout_open_table(TABLE_FULL_WIDTH);
	layout_start_header_row();
	layout_start_header_cell();
	layout_next_header_cell();
	echo Eelement_name('user');
	layout_next_header_cell_right();
	?>Ban start<?
	layout_next_header_cell_right();
	?>Ban eind<?
	layout_next_header_cell_right();
	echo Eelement_name('contest');
	layout_stop_header_cell();
	layout_stop_header_row();
	$actable = 0;
	foreach ($blacklisted as $blacklist) {
		extract($blacklist);
		if (!$USERID) {
			continue;
		}

		layout_start_rrow_center();
		if ($CONTESTID) {
			++$actable;
			?><input type="checkbox" name="BLACKLIST[]" value="<?= $CONTESTID ?>,<?= $USERID ?>" /><?
		}
		layout_next_cell();
		echo get_element_link('user',$USERID);
		layout_next_cell(class: 'right');
		?><small><?
		if ($STAMP) {
			?><small><?
			_dateday_display($STAMP, short: true);
			?></small><?
		}
		layout_next_cell(class: 'right');
		?><small><?
		echo _dateday_display($STOPSTAMP, short: true);
		?></small><?
		layout_next_cell(class: 'right');
		if ($CONTESTID) {
			echo get_element_link('contest',$CONTESTID);
		}
		layout_stop_row();
	}
	layout_close_table();
	layout_close_box();
	if ($actable) {
		?><div class="block"><input type="submit" value="<?= __('action:remove') ?>" /></div></form><?
	}
}

function contest_display_blacklist_archive() {
	if (!require_admin('contest')) {
		no_permission();
		return;
	}
	$history = db_rowuse_array(['contestblacklist','contest'],'
		SELECT	contestblacklist.USERID,
			contestblacklist.STAMP,
			STOPSTAMP,
			contestblacklist.CONTESTID,
			NICK
		FROM contestblacklist
		JOIN user_account USING (USERID)
		WHERE contestblacklist.STOPSTAMP<='.CURRENTSTAMP.'
		ORDER BY STOPSTAMP DESC'
	);
	if (!$history) {
		return;
	}
	layout_open_box('contest');
	layout_box_header(Eelement_name('archive'));

	?><table class="fw default"><?
	?><tr><?
	?><th><?= Eelement_name('user');
	?><th><?= Eelement_name('ban') ?> <?= __('field:start') ?></th><?
	?><th><?= Eelement_name('ban') ?> <?= __('field:stop') ?></th><?
	?><th><?= Eelement_name('contest') ?></th><?
	?></tr><?

	foreach ($history as $blacklist) {
		extract($blacklist);
		?><tr><?
		?><td><?= get_element_link('user',$USERID) ?></td><?
		?><td class="nowrap small right"><? _dateday_display($STAMP, short: true) ?></td><?
		?><td class="nowrap small right"><? _dateday_display($STOPSTAMP, short: true) ?></td><?
		?><td><?=  get_element_link('contest',$CONTESTID) ?></td><?
		?></tr><?
	}
	?></table><?

	layout_close_box();
}
function contest_display_top() {
	layout_show_section_header();

	$top = have_admin('contest') && have_idnumber($_REQUEST,'subID') ? $_REQUEST['subID'] : 50;

	main_menu();

	$winners = memcached_simple_hash('contestresponse','
		SELECT USERID,COUNT(*)
		FROM contestresponse
		WHERE WINSTATUS="winner"
		  AND TOLD=1
		GROUP BY USERID
		ORDER BY COUNT(*) DESC
		LIMIT 0,'.$top
	);
	if ($winners === false) {
		return;
	}
	if (!$winners) {
		?><p><?= __('contest:info:no_winners_LINE'); ?></p><?
		return;
	}
	arsort($winners);
/*	?><p>Dit zijn de geluksvogels van Partyflock. Zij hebben het meeste gewonnen met acties!</p><?*/
	?><p><?= __('contest:info:winners_list_information_TEXT',DO_NL2BR); ?></p><?

	layout_open_box('white');
	layout_open_table(TABLE_FULL_WIDTH | TABLE_ROW_HILITE_ONMOUSEOVER);
	layout_start_header_row();
	layout_header_cell(Eelement_name('user'));
	layout_header_cell_right(__C('field:won'));
	layout_header_cell_right(__C('field:joined'));
	layout_stop_header_row();
	foreach ($winners as $userid => $cnt) {
		layout_start_rrow();
		echo get_element_link('user',$userid);
		layout_next_cell(class: 'right');
		echo $cnt;
		layout_next_cell(class: 'right');
		echo memcached_single('contestresponse','SELECT COUNT(*) FROM contestresponse WHERE USERID='.$userid);
		layout_stop_row();
	}
	layout_close_table();
	layout_close_box();
}
function contest_display_recent(): void {
	layout_show_section_header();

	$top = have_admin('contest') && have_idnumber($_REQUEST,'subID') ? $_REQUEST['subID'] : 50;

	main_menu();

	$winners = memcached_rowuse_array('contestresponse','
		SELECT contestresponse.USERID, CONTESTID, CLOSE_STAMP
		FROM contestresponse
		JOIN contest USING (CONTESTID,TOLD)
		WHERE WINSTATUS = "winner"
		  AND TOLD = 1
 		  AND ONLYPROMO = b\'0\'
		GROUP BY contestresponse.USERID
		ORER BY CLOSE_STAMP DESC, CONTESTID DESC
		LIMIT 0,'.$top,
		DEFAULT_EXPIRATION,null,DB_NON_ASSOC
	);
	if ($winners === false) {
		return;
	}
	if (!$winners) {
		?><div class="block"><?= __('contest:info:no_recent_winners_LINE'); ?></div><?
		return;
	}
	?><div class="block"><?= __('contest:info:recent_winners_information_TEXT',DO_NL2BR); ?></div><?

	layout_open_box('white');
	layout_open_table(TABLE_FULL_WIDTH | TABLE_ROW_HILITE_ONMOUSEOVER);
	layout_start_header_row();
	layout_header_cell(Eelement_name('user'));
	layout_header_cell_right(__C('field:last'));
	layout_header_cell_right(__C('field:won'));
	layout_header_cell_right(__C('field:joined'));
	layout_stop_header_row();
	foreach ($winners as $contest) {
		[$USERID, $CONTESTID, $CLOSE_STAMP] = $contest;
		layout_start_rrow();
		echo get_element_link('user', $USERID);
		layout_next_cell(class: 'right');
		_date_display($CLOSE_STAMP);
		layout_next_cell(class: 'right');
		echo get_element_link('contest', $CONTESTID);
		layout_next_cell(class: 'right');
		echo memcached_single('contestresponse','SELECT COUNT(*) FROM contestresponse WHERE USERID='.$USERID);
		layout_stop_row();
	}
	layout_close_table();
	layout_close_box();
}

function contest_message_winners(): void {
	require_once '_contact.inc';
	require_once '_mail.inc';
	if (!require_admin('contest')
	||	!require_idnumber($_REQUEST,'sID')
	||	!require_getlock($lockname = 'msgwins:'.($contestid = $_REQUEST['sID']),1)
	) {
		return;
	}
	while (true) {
		$contest = db_single_assoc(['contest', 'party'],'
			SELECT ACTIVE, PARTYID, MOVEDID, CANCELLED
			FROM contest
			LEFT JOIN party USING (PARTYID)
			WHERE CONTESTID = '.$contestid
		);
		if ($contest === false) {
			break;
		}
		if (!$contest) {
			not_found();
			break;
		}
		if (!$contest['ACTIVE']) {
			register_error('contest:error:not_active_LINE');
			break;
		}
		if ($contest['MOVEDID']) {
			register_error('party:error:is_moved_LINE',DO_UBB,array('PARTYID'=>$contest['PARTYID'],'MOVEDID'=>$contest['MOVEDID']));
			break;
		}
		if ($contest['CANCELLED']) {
			register_error('party:error:is_cancelled_LINE',DO_UBB,array('PARTYID'=>$contest['PARTYID']));
			break;
		}

		$winners = db_rowuse_array(['contestresponse', 'user'], '
			SELECT contestresponse.USERID, NICK, EMAIL
			FROM contestresponse
			LEFT JOIN user USING (USERID)
			WHERE CONTESTID = '.$contestid.'
			  AND WINSTATUS = "winner"
			  AND TOLD = 0',
			DB_USE_MASTER
		);
		if ($winners === false) {
			break;
		}
		if ($winners) {
			foreach ($winners as $winner) {
				if (db_insert('contestwinmessage', '
					INSERT IGNORE INTO contestwinmessage SET
						CONTESTID	= '.$contestid.',
						USERID		= '.$winner['USERID'].',
						CUSERID		= '.CURRENTUSERID.',
						CSTAMP		= '.CURRENTSTAMP)
				&&	db_update('contestresponse', '
					UPDATE contestresponse SET
						TOLD=1
					WHERE CONTESTID = '.$contestid.'
					  AND USERID = '.$winner['USERID'])
				) {
					if (SERVER_SANDBOX) {
						$warninglist[] = 'Bericht niet verzonden naar '.get_element_link('user',$winner['USERID'],$winner['NICK']);
					} else {
						$noticelist[] =	'Bericht verzonden naar '.get_element_link('user',$winner['USERID'],$winner['NICK']);
						if (!SERVER_SANDBOX) {
							mail_contest_winner($winner['EMAIL'],$winner['NICK'],$winner['USERID'],$contestid);
						}
					}
				}
				memcached_delete('contestcounts:'.$winner['USERID']);
			}
			db_update('contest', 'UPDATE contest SET TOLD = 1 WHERE TOLD = 0 AND CONTESTID = '.$contestid);
			if (isset($noticelist)) {
				_notice(implode('<br />', $noticelist));
			}
			if (isset($warninglist)) {
				warning(implode('<br />', $warninglist));
			}
		} else {
			warning('Niemand ingelicht!');
		}
		if (isset($_REQUEST['TICKETID'])) {
			contact_set_status();
		}
		break;
	}
	db_releaselock($lockname);
}

function contest_display_participations() {
	if (!($userid = require_idnumber($_REQUEST, 'subID'))) {
		return;
	}
	layout_show_section_header();

	require_once '_contestlist.inc';
	$contestlist = new _contestlist;
	$contestlist->participated_by($userid);
	$contestlist->order_by_close_stamp_reverse();
	$contestlist->show_close_diff = false;
	$contestlist->query();
	$contestlist->display();
}

function contest_display_winmsg() {
	layout_show_section_header();

	if (($contestid = $_REQUEST['sID'])
	&&	$_REQUEST['ACTION'] !== 'markread'
	) {
		if (!require_admin('contest')) {
			return;
		}
		$contest = db_single_assoc(['contest', 'user', 'city', 'contest', 'party'], '
			SELECT	CONTESTID, AMOUNT, GROUPS, VISIBLE_GROUPS, TYPE, contest.NAME, CLOSED, contest.PARTYID, WINMSG, STDWINMSG, GLCLOSEHOUR, GLCLOSEMINS, CLOSE_STAMP, SCANCODES_RELEASE,
				REALNAME, ADDRESS, ZIPCODE, CITY, user.CITYID, NICK, EMAIL,
				city.NAME AS CITY_NAME,
				party.NAME AS PARTY_NAME, party.STAMP, party.STAMP_TZI, NOTIME, AT2400
			FROM contest
			LEFT JOIN user ON user.USERID = '.CURRENTUSERID.'
			LEFT JOIN city ON city.CITYID = user.CITYID
			LEFT JOIN party ON party.PARTYID = contest.PARTYID
			WHERE CONTESTID = '.$contestid
		);
		if ($contest === false) {
			return;
		}
#		$contest['UNIQ'] = db_single('contestcode','SELECT UNIQ FROM contestcode WHERE CONTESTID = '.$_REQUEST['sID'].' LIMIT 1') ? create_password(8,'ABCDEFGHJKLMNPQRSTUVWXYZ3456789',false) : null;
		$contest['CODE'] = '%RANDOMCODE%';
		$contest['CONFIRMED'] = db_single('contestcode', 'SELECT 1 FROM contestcode WHERE CONTESTID = '.$contestid.' AND CONFIRMED = 1');
		$contestwins[] = $contest;
		$singlecontest = true;
		$showall = false;
	} else {
		if (!require_user()) {
			return;
		}
		$userid =
			have_admin(['contest', 'helpdesk'])
		&&	$_REQUEST['subID']
		?	$_REQUEST['subID']
		:	CURRENTUSERID;

		$showall = ($_REQUEST['SUBACTION'] === 'all');

		$contestwins = db_rowuse_array(['contestwinmessage', 'user', 'city', 'contest', 'party'],'
			SELECT 	contestwinmessage.CONTESTID,contestwinmessage.CSTAMP AS MSGSTAMP, CLOSE_STAMP, SCANCODES_RELEASE,
				REALNAME, ADDRESS, ZIPCODE, CITY, user.CITYID, NICK, EMAIL,
				city.NAME AS CITY_NAME,
				AMOUNT, GROUPS, VISIBLE_GROUPS, TYPE, contest.NAME, CLOSED, contest.PARTYID, WINMSG, STDWINMSG, GLCLOSEHOUR, GLCLOSEMINS,
				CODE, CONFIRMED
			FROM contestwinmessage
			LEFT JOIN contestresponse USING (CONTESTID, USERID)
			LEFT JOIN contestcode USING (CONTESTID, USERID)
			LEFT JOIN user USING (USERID)
			LEFT JOIN city USING (CITYID)
			LEFT JOIN contest USING (CONTESTID)
			LEFT JOIN party USING (PARTYID)
			WHERE contestwinmessage.USERID = '.$userid.($showall ? '' : ' AND READM = 0').'
			ORDER BY contestwinmessage.CSTAMP DESC'
		);
		if ($contestwins === false) {
			return;
		}
	}
	if (!$contestwins) {
		?><div class="block"><?= __('contest:info:no_new_won_contests_LINE') ?></div><?
		return;
	}
	if ($showall) {
		layout_open_box('white');
		?><div class="block"><span class="warning"><?= __C('warningheader') ?></span><br /><?
		echo __('contest:info:all_won_contests_TEXT',DO_NL2BR | DO_UBB);
		?></div><?
		layout_close_box();
	} else {
		?><div class="block"><?= __('contest:info:you_have_won_LINE') ?></div><?
	}
	foreach ($contestwins as &$contest) {
		if ($contest['PARTYID']) {
			$contest['party'] = $party = memcached_party_and_stamp($contest['PARTYID']);
			if (!$contest['party']) {
				return false;
			}
		} else {
			$contest['party'] = [];
		}

		if (!($hide_personal = ($contest['party'] ? (CURRENTSTAMP > $party['STAMP'] + 5 * ONE_DAY) : (CURRENTSTAMP > $contest['CLOSE_STAMP'] + 14 * ONE_DAY)))) {
			$nonhidden = true;
		}
	}
	unset($contest);
	if (!isset($nonhidden)) {
		?><div class="block"><?= __('contest:info:personals_hidden_LINE') ?></div><?
	}
	foreach ($contestwins as $contest) {
		extract($party = $contest['party']);

		layout_open_box('contest');
		layout_open_box_header();
		$args = [
			'PARTYID'	=> $contest['PARTYID'],
			'AMOUNT'	=> $contest['AMOUNT'],
			'NAME'		=> $contest['NAME']
		];

		switch ($contest['TYPE']) {
		case 'guestlist':
			echo __('contest:header:won_guestlist_for', DO_UBB, $args);
			if ($party) {
				?> <small>(<? datedaytime_display_link($contest['party']) ?>)</small><?
			}
			break;
		case 'freeticket':
			echo __('contest:header:won_freeticket_for', DO_UBB, $args);
			if ($party) {
				?> <small>(<? datedaytime_display_link($contest['party']) ?>)</small><?
			}
			break;

		case 'eticket':
		case 'scancode':
			echo __('contest:header:won_eticket_for', DO_UBB, $args);
			if ($party) {
				?> <small>(<? datedaytime_display_link($contest['party']) ?>)</small><?
			}
			break;

		case 'cd':
		case 'dvd':
		case 'discount':
		case 'vinyl':
		case 'wear':
			// FIXME: fill this in
		default:
			echo escape_utf8($contest['NAME']);
			break;
		}
		layout_continue_box_header();
		?><a href="<?= get_element_href('contest', $contest['CONTESTID']) ?>"><?= __('action:see_contest') ?></a><?
		layout_close_box_header();

		show_contest_uploadimage($contest['CONTESTID'], $contest['PARTYID']);

		if ($hide_personal = (
				$party
			?	(CURRENTSTAMP > $party['STAMP'] + 5 * ONE_DAY)
			:	(CURRENTSTAMP > $contest['CLOSE_STAMP'] + 14 * ONE_DAY)
			)
		) {
			# hide 5 days after party or 14 days after closing of contest
			$contest['EMAIL']	= __('<EMAIL>');
			$contest['REALNAME']	= Eelement_name('your_realname');
			$contest['ADDRESS']	= Eelement_name('your_address');
			$contest['ZIPCODE']	= Eelement_name('your_zipcode');
			$contest['CITYID']	= 0;
			$contest['CITY']	= Eelement_name('your_city');
		} else {
			$contest['REALNAME'] = make_clean_realname($contest['REALNAME']);
		}
		if ($contest['STDWINMSG']) {
			switch ($contest['TYPE']) {
			case 'guestlist':
				$endtime = $contest['GLCLOSEHOUR'] !== 24 ? sprintf('%02d:%02d',$contest['GLCLOSEHOUR'],$contest['GLCLOSEMINS']) : null;
#				$contest['AMOUNT'] = 3;
#				$contest['CODE'] = null;
#				$endtime = '20:30';
				?><div class="block"><?
				echo __('contest:info:guestlist_winmsg_TEXT',DO_UBB,[
					'GROUPS'	=> $contest['GROUPS'],
					'AMOUNT'	=> $contest['AMOUNT'],
					'X'		=> $contest['AMOUNT']-1,
					'CONFIRMED'	=> $contest['CONFIRMED'] ?: 0,
					'ENDTIME'	=> $endtime,
					'UNIQ'		=> $contest['CODE'],
					'REALNAME'	=> $contest['REALNAME'],
				]);
				?></div><?

				?><hr class="slim" style="margin:1em 0"><?

				$list = new deflist('deflist vtop');
				$list->add_row(Eelement_name('contest'), get_element_link('contest', $contest['CONTESTID']));
				if ($contest['PARTYID']) {
					$list->add_rows(
						Eelement_name('party'),		get_element_link('party', $contest['PARTYID']),
						Eelement_name('date'),		_dateday_get($party['STAMP'], null, ', '),
						Eelement_name('time'),		_time_get($party['STAMP'])
					);
				}
				if ($endtime) {
					$list->add_row(__C('field:guestlist_closes'), $endtime);
				}
				$list->add_rows(
					Eelement_name('list'),	(
						$contest['CONFIRMED']
					?	'<b>'.$contest['CODE'].'</b>'
					:	'<b>'.escape_specials($contest['REALNAME']).'</b>'.(
								$contest['CODE'] ? ', '.escape_specials($contest['CODE'])
							: null)
					).($contest['AMOUNT']-1 ? ' +'.($contest['AMOUNT']-1) : null)
				);
				$list->display();

				break;

			case 'freeticket':
				?><div class="block brmrgn"><?= __('contest:info:won_freetickets_TEXT',DO_NL2BR) ?></div><?
				?><div class="block brmrgn"><div class="lmrgn easy-quote"><tt><?
					   echo escape_specials($contest['REALNAME']);
				?><br /><? echo escape_specials($contest['ADDRESS']);
				?><br /><? echo escape_specials($contest['ZIPCODE']);
				?><br /><? echo escape_utf8($contest['CITYID'] ? $contest['CITY_NAME'] : $contest['CITY'])
				?></tt></div></div><?
				?><div class="block"><?= __('contest:info:incomplete_address_TEXT',DO_NL2BR) ?></div><?
				break;

			case 'eticket':
			case 'scancode':
				# NOTE: when offering archive of won tickets, don't display email address anymore

				if ($contest['TYPE'] === 'eticket') {
					$args = [
						'EMAIL' 	=> $contest['EMAIL'],
					];
				} else {
					change_timezone($party['TIMEZONE'] ?: 'UTC');
					$args = [
						'RELEASE'	=> $contest['SCANCODES_RELEASE'],
						'RELEASE_DATE'	=> _datetime_get($party['STAMP'] - ONE_HOUR * $contest['SCANCODES_RELEASE']),
					];
					change_timezone();
				}

				?><div class="body block brmrgn"><?= __('contest:info:won_'.$contest['TYPE'].'s_TEXT', DO_NL2BR | DO_UBB, $args) ?></div><?
				if ($contest['GLCLOSEHOUR'] !== 24) {
					?><div class="body block"><?
					echo __('contest:info:eticket_entry_till_TEXT', DO_NL2BR, [
						'ENDTIME' => sprintf('%02d:%02d', $contest['GLCLOSEHOUR'], $contest['GLCLOSEMINS'])
					]);
					?></div><?
				}
				break;

			case 'cd':
			case 'dvd':
			case 'discount':
			case 'vinyl':
			case 'wear':
				// FIXME: fill this in
			default:
				?><div class="body block brmrgn"><?= __('contest:info:won_generic_LINE') ?></div><?
				?><div class="block brmrgn"><div class="lmrgn easy-quote"><tt><?= escape_specials($contest['REALNAME']);
				?><br /><?= escape_specials($contest['ADDRESS']);
				?><br /><?= escape_specials($contest['ZIPCODE']);
				?><br /><?= escape_utf8($contest['CITYID'] ? $contest['CITY_NAME'] : $contest['CITY']);
				?></tt></div></div><?
				?><div class="block brmrgn"><div class="lmrgn easy-quote"><tt><?= _make_illegible($contest['EMAIL']) ?></tt></div></div><?
				?><div class="block"><?= __('contest:info:incomplete_address_generic_TEXT',DO_NL2BR) ?></div><?
				break;
			}
		}
		if (!$contest['STDWINMSG']
		||	$contest['STDWINMSG'] === 2
		&&	!empty($contest['WINMSG'])
		) {
			if ($contest['STDWINMSG']) {
				?><hr class="slim" style="margin:1em 0"><?
			}
			?><div class="body block"><?
			echo make_all_html(
				str_replace('#REALNAME#',	win1252_to_utf8($contest['REALNAME']),
				str_replace('#ADDRESS#',	win1252_to_utf8($contest['ADDRESS']),
				str_replace('#ZIPCODE#',	win1252_to_utf8($contest['ZIPCODE']),
				str_replace('#CITY#',		$contest['CITYID'] ? $contest['CITY_NAME'] : $contest['CITY'],
				str_replace('#EMAIL#',		win1252_to_utf8($contest['EMAIL']),
					$contest['WINMSG']
				))))),
				UBB_UTF8,
				'contest',
				$contest['CONTESTID']
			);
			?></div><?
		}
		layout_close_box();
		if (!$showall) {
			if (!isset($singlecontest)) {
				?><div class="funcs"><a href="/contest/<?= $contest['CONTESTID'];
				?>/markread"><?= __C('action:mark_read') ?></a></div><?
			}
			if ($contest['TYPE'] !== 'scancode') {
				?><div class="body block"><?
				echo __('contest:info:info_has_been_submitted_TEXT', DO_NL2BR | DO_UBB, [
					'CONTESTID' => $contest['CONTESTID']
				]);
				?></div><?
			}
		}
		if ($hide_personal && isset($nonhidden)) {
			?><div class="block"><?= __('contest:info:personals_hidden_LINE') ?></div><?
		}
	}
}

function contest_display_winners(): void {
	layout_show_section_header(null,element_plural_name('winner'));

	if (!require_admin('contest')
	||	!($contestid = require_idnumber($_REQUEST,'sID'))
	) {
		return;
	}

	$contest = db_single_assoc(['contest','party'],'
		SELECT	contest.PARTYID,CONTACT,GROUPS,VISIBLE_GROUPS,AMOUNT,TYPE,contest.TICKETID,MOVEDID,EXTERNAL,
			party.NAME,party.STAMP
		FROM contest
		LEFT JOIN party USING (PARTYID)
		WHERE CONTESTID = '.$contestid
	);
	if ($contest === false) {
		return;
	}
	if (!$contest) {
		not_found();
		return;
	}
	require_once '_contestwinners.inc';
	$extra_chance_users = get_contest_sms_counts($contestid);

	$winners = db_rowuse_array(['contestresponse', 'contestcode', 'user', 'city'],'
		SELECT	contestresponse.USERID, TOLD,
			user.NICK, REALNAME, user.ADDRESS, user.ZIPCODE, user.CITYID, user.CITY, user.EMAIL, user.PHONE,
			IF(CONFIRMED, CODE, NULL) AS CODE,
			city.NAME
		FROM contestresponse
		LEFT JOIN contestcode USING (CONTESTID, USERID)
		LEFT JOIN user USING (USERID)
		LEFT JOIN city USING (CITYID)
		WHERE CONTESTID = '.$contestid.'
		  AND WINSTATUS = "winner"'
	);
	if ($winners === false) {
		return;
	}
	if (!$winners) {
		?><p>Geen winnaars gevonden.</p><?
		return;
	}
	$need_names = false;
	$untold = 0;
	foreach ($winners as $winner) {
		if ($contest['TYPE'] !== 'scancode'
		&&	(	$contest['TYPE'] !== 'guestlist'
			||	!$winner['CODE']
			)
		) {
			$need_names = true;
		}
		if (!$winner['TOLD']) {
			++$untold;
		}
	}

	string_sort($winners,'REALNAME');

	$blacklisted = db_boolean_hash('contestblacklist', 'SELECT USERID FROM contestblacklist WHERE CONTESTID = '.$contestid);
	if ($blacklisted === false) {
		return;
	}
	?><form<?
	?> onsubmit="return submitForm(this)"<?
	?> method="post"<?
	?> action="/contest/<?= $contestid ?>/blacklistadd"><?

	layout_open_box('contest'.(!have_super_admin() ? ' noselect' : null));

	$uniq = 'onlynames_'.uniqid();

	ob_start();
	expand_collapse($uniq);
	$clicker = ob_get_clean();

	?><div class="ptr" onclick="<? expand_collapse_clicker($uniq)  ?>"><?
	layout_box_header(Eelement_plural_name('address'),$clicker);
	?></div><?
	?><div class="hidden" id="<?= $uniq ?>"><?

	foreach ($winners as $winner) {
		extract($winner);

		$already_listed = isset($blacklisted[$USERID]);

		?><fieldset<?
		if ($already_listed) {
			?> class="light"<?
		}
		?> style="margin-bottom:1em"><?
		?><legend><?
			?><label class="basehl not-bold-hilited-red"><?
			show_input([
				'class'		=> 'upLite',
				'type'		=> 'checkbox',
				'name'		=> 'USERIDS[]',
				'value'		=> $USERID,
				'disabled'	=> $already_listed,
				'checked'	=> $already_listed,
			]);
			?> <b><?
			echo ($clean_real = escape_specials(make_clean_realname($REALNAME)));
			if ($CODE) {
				?>, <?
				echo $CODE;
			}
			?></b><?
			?></label><?

			if ($sms = getifset($extra_chance_users,$USERID)) {
				?> <?= MIDDLE_DOT_ENTITY ?> <span class="notice"><?= $sms ?></span><?
			}
		?></legend><?
		?><div><?
		?><i><?= get_element_link('user',$USERID) ?></i><br /><?
		?><i><? print_email_link($EMAIL,$clean_real,PRINT_EMAIL_IMAGE_BEFORE | PRINT_EMAIL_NO_NAME | PRINT_EMAIL_SHOW_EMAIL | PRINT_EMAIL_TICKET,'contest',$contestid,$USERID) ?></i><br /><?
		echo $clean_real ?><br /><?
		echo escape_specials($ADDRESS); ?><br /><?
		echo escape_specials($ZIPCODE); ?> <? echo escape_specials($NAME);
		if ($CITY) {
			?> (of <? echo escape_specials($CITY); ?>)<?
		}
		?></div><?
		?></fieldset><?
	}
	?></div><?

	$uniq = 'onlynames_'.uniqid();

	ob_start();
	expand_collapse($uniq);
	$clicker = ob_get_clean();

	?><div class="ptr" onclick="<? expand_collapse_clicker($uniq)  ?>"><?
	layout_box_header(Eelement_plural_name('only_name').(empty($winners[0]['CODE']) ? null : ' en unieke codes'),$clicker);
	?></div><?
	?><div class="hidden" id="<?= $uniq ?>"><?
	?><p>Voor vermelding op gastenlijst.</p><?
	?><ul style="margin-bottom:1em"><?
	foreach ($winners as $winner) {
		?><li><?
		echo escape_specials(make_clean_realname($winner['REALNAME']));
		if (!empty($winner['CODE'])
		&&	$contest['TYPE'] === 'guestlist'
		) {
			?>, <? echo $winner['CODE'];
		}
		?></li><?
	}
	?></ul><?
	?></div><?

	$uniq = 'onlyemails_'.uniqid();

	ob_start();
	expand_collapse($uniq);
	$clicker = ob_get_clean();

	?><div class="ptr" onclick="<? expand_collapse_clicker($uniq)  ?>"><?
	layout_box_header(Eelement_plural_name('only_email'), $clicker);
	?></div><?
	?><div class="hidden" id="<?= $uniq ?>"><?
	?><p>Voor tickets die per email verstuurd moeten worden.</p><?
	?><ul><?
	foreach ($winners as $winner) {
		extract($winner);
		$email = _make_illegible($EMAIL);
		$clean_real = escape_specials(make_clean_realname($REALNAME));

		?><li><? print_email_link($EMAIL,$clean_real,PRINT_EMAIL_HIDE_SEARCH | PRINT_EMAIL_NO_POST_LINK | PRINT_EMAIL_NO_NAME | PRINT_EMAIL_SHOW_EMAIL | PRINT_EMAIL_TICKET,'contest',$contestid,$USERID) ?></li><?
	}
	?></ul><?
	?></div><?

	# voornaan, achternaam, email adres

	$uniq = 'onlynames_'.uniqid();

	ob_start();
	expand_collapse($uniq);
	$clicker = ob_get_clean();

	?><div class="ptr" onclick="<? expand_collapse_clicker($uniq)  ?>"><?
	layout_box_header(Eelement_name('first,surname,email'), $clicker);
	?></div><?
	?><div class="hidden" id="<?= $uniq ?>"><?
	?><p>Voor onder andere Paylogic.</p><?

	foreach ($winners as $winner) {
		extract($winner);

		[$first,$sur] = explode(' ',$REALNAME,2);

		?><li>&quot;<?
		echo str_replace('"','""',$first);
		?>&quot;,&quot;<?
		echo str_replace('"','""',$sur);
		?>&quot;,&quot;<?
		echo escape_utf8($EMAIL);
		?>&quot;<?
		?></li><?
	}

	?></div><?

	layout_close_box();

	?><div class="r block"><input type="submit" value="<?= __('action:add_to_blacklist') ?>" /></div></form><?

	layout_open_menu();
	if ($need_names) {
		$have_winners = count($winners);

		if ($have_winners > $contest['GROUPS']) {
			?><div class="error block"><?
			?>ER ZIJN TE VEEL WINNAARS<?
			?></div><?
		} else {
			layout_menuitem(
				__C('action:make_contact_ticket_with_winners'),
				!$contest['TICKETID']
			?	'/ticket/outgoing-form?USETEMPLATE=contestresult;CONTESTID='.$contestid
			:	'/ticket/'.$contest['TICKETID'].'?USETEMPLATE=contestresult;CONTESTID='.$contestid
			);
		}
	} elseif (
		!$contest['EXTERNAL']
	&&	!$contest['MOVEDID']
	&&	$untold
	) {
		layout_open_menuitem();
		with_confirm(
			__C('action:notify_winners'),
			'/contest/'.$contestid.'/msgwins',
			__C('contest:confirm:notify_winners_LINE')
		);
		if ($contest['TYPE'] !== 'scancode') {
			?> (<?= __('contest:info:codes_already_confirmed') ?>)<?
		}
		layout_close_menuitem();
	}
	layout_close_menu();

/*	layout_open_box('contest');
	layout_open_box_header();
	?>Telefoon en email<?
	layout_close_box_header();
	?><p>In principe nooit uitgeven.</p><?
	layout_open_table(TABLE_FULL_WIDTH);
	foreach ($winners as $winner) {
		layout_start_rrow();
		echo get_element_link('user',$winner['USERID'],$winner['NICK']);
		layout_next_cell();
		echo escape_specials($winner['PHONE']);
		layout_next_cell();
		$mailto = _make_illegible('mailto:');
		$email = _make_illegible($winner['EMAIL']);
		?><a href="<? echo $mailto,$email; ?>"><?= $email; ?></a><?
		layout_stop_row();
	}
	layout_close_table();
	layout_close_box();*/
}

function contest_display_scancodes(): void {
	require_once '_scancodes.inc';
	show_future_scancodes();
}

function contest_make_winners(): bool {
	require_once '_contestparticipation.inc';
	if (!($contestid = require_idnumber($_REQUEST,'sID'))
	||	!require_admin('contest')
	||	!require_getlock($winslock = 'contestwins:'.$contestid, 5)
	) {
		return false;
	}
	$rc = actually_contest_make_winners($contestid);
	db_releaselock($winslock);
	return $rc;
}

function actually_contest_make_winners(int $contestid): bool {
	# FIXME: choose unique realnames
	if (!($contest = db_single_assoc(['contest', 'party'],'
		SELECT	CONTESTID, TYPE, GROUPS, VISIBLE_GROUPS, CLOSE_STAMP, ONLYPROMO, CLOSED, ACTIVE, contest.PSTAMP, CHANCECALC,
				STAMP, DURATION_SECS, NOTIME, MOVEDID, POSTPONED, CANCELLED, ACCEPTED, PARTYID, party.MIN_AGE, party.MIN_AGE_FEMALE, MAX_AGE, SCANCODES_TYPE, AMOUNT
		FROM contest
		LEFT JOIN party USING (PARTYID)
		WHERE CONTESTID  ='.$contestid))
	) {
		if ($contest !== false) {
			register_error('contest:error:nonexistent_LINE', ['ID' => $contestid]);
		}
		return false;
	}
	if (!($to_give = $contest['GROUPS'])) {
		register_error('contest:error:nothing_to_give_LINE');
		return true;
	}
	if (false === ($winners = db_single('contestresponse', '
		SELECT COUNT(*)
		FROM contestresponse
		WHERE WINSTATUS = "winner"
		  AND CONTESTID = '.$contestid))
	) {
		return false;
	}
	$to_give -= $winners;
	if ($to_give <= 0) {
		# _error('Niets te vergeven!');
		register_error('contest:error:nothing_to_give_away_LINE');
		return true;

	}
	require_once '_contestchance.inc';
	require_once '_contestwinners.inc';
	if (false === ($extra_chance_users = get_contest_sms_counts($contestid))
	||	false === ($blacklisted = ignore_for_contest($contest))
	) {
		return false;
	}
	if ($blacklisted) {
		# remove sms senders from blacklist, they may always go
		$blacklisted = array_diff_key($blacklisted, $extra_chance_users);
	}
	if (false === ($options = db_rowuse_hash(['contestresponse', 'user_account', 'user'], "
		SELECT USERID, EMAIL, REALNAME, PHONE, ADDRESS, CITYID
		FROM contestresponse
		JOIN user_account USING (USERID)
		JOIN user USING (USERID)
		WHERE CONTESTID = $contestid
		  AND WINSTATUS NOT IN ('invalid', 'winner')
		  AND STATUS = 'active'
		ORDER BY contestresponse.CSTAMP"))
	) {
		return false;
	}

	# All options, including blacklisted
	$all_options = $options;

	# Remove blacklisted users from regular options
	$options = array_diff_key($options, $blacklisted);

	# Keep only one user per phone,name,email combination
	$ok_options = [];
	$done_emails = [];
	foreach ($options as $option) {
		extract($option);
		if ($PHONE) {
			$option['PHONE'] = $PHONE = preg_replace('"[^\d]+"', '', $PHONE);

			if (strlen($PHONE) >= 8) {
				if (!isset($donephone[$PHONE])) {
					$donephone[$PHONE] = $option;
				} else {
					continue;
				}
			}
		}
		if ($REALNAME) {
			$option['REANAME'] = $REALNAME = strtolower(preg_replace('"[\W_]+"', '', $REALNAME));

			if (!isset($donename[$REALNAME])) {
				$donename[$REALNAME] = $option;
			} else {
				continue;
			}
		}
		$option['EMAIL'] = $EMAIL = strtolower($EMAIL);

		if (!isset($done_emails[$EMAIL])) {
			$done_email[$EMAIL] = $option;
		} else {
			continue;
		}
		$ok_options[$USERID] = $option;
	}
	$options = array_keys($ok_options);
	$option_size = count($options);

	# Blacklisted users that we in the initial list of contestants,
	# and which we may allow to have a chance if we have tickets
	# left.
	$blacklisted = array_intersect_key($blacklisted, $all_options);

	if ($blacklisted
	&&	$to_give > $option_size
	) {
		$empty_places = $to_give - $option_size;
		while ($blacklisted && $empty_places--) {
			$blacklisted_userid = array_rand($blacklisted);
			$options[] = $blacklisted_userid;
			unset($blacklisted[$blacklisted_userid]);
		}
		$option_size = count($options);
	}
	if (!$options) {
		register_error('contest:warning:no_contestant_at_all_LINE');
		return false;
	}

	if ($option_size < $to_give) {
		$left = $to_give - $option_size;
		register_warning('contest:warning:not_enough_contestants_few_left_LINE', ['LEFT' => $left]);
/*		warning('Er waren niet genoeg mededingers om alles te verloten. '.
			(	$left > 1
			?	'Er zijn nog '.$left.' eenheden onverloot!'
			:	'Er is nog 1 eenheid onverloot!'
			)
		);*/
		$to_give = $option_size;
	}

	if (false === ($alreadywon = db_simple_hash(['contest', 'contestresponse'],'
		SELECT contestresponse.USERID, MAX(CLOSE_STAMP)
		FROM contestresponse
		JOIN contest USING (CONTESTID)
		WHERE contestresponse.USERID IN ('.implode(', ', $options).')
		  AND CLOSE_STAMP >= '.(TODAYSTAMP - 60 * ONE_DAY)))
	) {
		return false;
	}

	require_once '_contestchance.inc';

	foreach ($extra_chance_users as $userid => $cnt) {
		if (!in_array($userid, $options, true)) {
			# invalid or already won
			continue;
		}
		$extra = get_extra_chance_mult($contest, $cnt);
		for ($i = 0; $i < $extra; ++$i) {
			$options[] = $userid;
			++$option_size;
		}
	}

	$useridstr = implode(',', $options);

	# no events or only interested
	if (false === ($only_interest = db_simple_hash(['going', 'party', 'user_account'], "
		SELECT going.USERID, COUNT(IF(going.MAYBE = 1, 1, NULL)) = COUNT(*) AS ALL_MAYBE
		FROM going
		JOIN party USING (PARTYID)
		WHERE going.USERID IN ($useridstr)
		  AND party.STAMP > ".TODAYSTAMP.'
		GROUP BY going.USERID
		HAVING ALL_MAYBE = 1'))
	||	false === ($many_contests = db_rowuse_hash(['going','party'], "
		SELECT contestresponse.USERID,COUNT(*)
		FROM contestresponse
		JOIN contest USING (CONTESTID)
		JOIN party USING (PARTYID)
		WHERE contestresponse.USERID IN ($useridstr)
		  AND party.STAMP > ".TODAYSTAMP.'
		GROUP BY contestresponse.USERID
		HAVING COUNT(*) > 10'))
	) {
		return false;
	}

	# split options in first_group and regular options

	if ($extra_chance_users) {
		$first_group = $second_group = [];
		foreach ($options as $userid) {
			if (!may_participate($contest, $userid, $errstr, true)) {
				continue;
			}
			if (isset($extra_chance_users[$userid])) {
				$first_group[] = $userid;
			} else {
				$second_group[] = $userid;
			}
		}
		$groups = ['first_group', 'second_group'];
	} else {
		$groups = ['first_group'];
		$first_group = $options;
		$second_group = [];
	}

	$maxiter = 500;
	$winners = null;

	for ($i = 0; $i < $to_give; ++$i) {
		$iter = 0;
		do {
			# first try to pick from first_group, then second_group

			foreach ($groups as $optionlist) {
				if (!$$optionlist) {
					continue;
				}
				$indexval = safe_random_int(0, count($$optionlist) - 1);

				$userid = $$optionlist[$indexval];

				break;
			}
			if (!isset($extra_chance_users[$userid])) {
				if (isset($only_interest[$userid])
				||	isset($many_contests[$userid])
				) {
					# only 1 percent chance
					if (mt_rand(0, 99)) {
						continue;
					}
				}
			}
			if (isset($extra_chance_users[$userid])
			||	!isset($alreadywon[$userid])
			||	$alreadywon[$userid] < TODAYSTAMP - ONE_MONTH
			||	mt_rand(0, 1)
			) {
				# full chance for people who have not won or have not won recently
				# or people who sent paid sms
				break;
			}
		}
		while (++$iter < $maxiter);

		$winners[] = $userid;

		# Remove all $userids from $$optionlist
		# There may be more instances of $userid in the list, due to increased chance
		foreach ($$optionlist as $ndx => $tmp_userid) {
			if ($tmp_userid === $userid) {
				unset($$optionlist[$ndx]);
			}
		}

		$$optionlist = array_values($$optionlist);

		if (!$first_group && !$second_group) {
			# no options left
			break;
		}
	}
	foreach ($winners as $userid) {
		if ($contest['TYPE'] === 'scancode'
		&&	$contest['SCANCODES_TYPE']
		) {
			if (!db_update('contest_scancode', "
				UPDATE contest_scancode SET USERID = $userid
				WHERE CONTESTID = $contestid
				  AND USERID = 0
				LIMIT {$contest['AMOUNT']}")
			) {
				return false;
			}
			if ($contest['AMOUNT'] !== db_affected()) {
				register_error('scancodes:error:failed_to_choose_scancode_for_winner_LINE', DO_UBB, ['USERID' => $userid]);
				return false;
			}
		}

		foreach ([true, false] as $confirmed) {
			if (!db_update('contestcode', "
				UPDATE contestcode SET USERID = $userid
				WHERE CONTESTID = $contestid
				  AND NOT USERID
				  AND ".($confirmed ? 'CONFIRMED' : 'NOT CONFIRMED').'
				LIMIT 1')
			) {
				return false;
			}
			if (db_affected()) {
				break;
			}
		}
		if (!db_affected()) {
			register_error('contest:error:failed_to_choose_unique_code_for_winner_LINE', ['USERID' => $userid]);
			return false;
		}
		if (!db_update('contestresponse', "
			UPDATE contestresponse SET
				WINSTATUS = 'winner'
			WHERE CONTESTID = $contestid
			  AND USERID = $userid")
		) {
			return false;
		}
	}
	return true;
}

function contest_choose_wins(): void {
	if (!require_admin('contest')
	||	!($contestid = require_idnumber($_REQUEST, 'sID'))
	||	!require_number_array($_POST, 'WINNER')
	||	!db_update('contestresponse','
		UPDATE contestresponse SET
			WINSTATUS = IF(USERID IN ('.implode(',', $_POST['WINNER']).'), "winner", "loser")
		WHERE CONTESTID = '.$contestid)
	) {
		return;
	}
	register_notice('contest:notice:winners_chosen_LINE');
}

function contest_remove_winners(): void {
	if (!require_admin('contest')
	||	!($contestid = require_idnumber($_REQUEST, 'sID'))
	||	!db_update('contestcode','
		UPDATE contestcode
		JOIN contestresponse USING (CONTESTID, USERID)
		SET USERID = 0
		WHERE NOT TOLD
		  AND CONTESTID = '.$contestid)
	||	!db_update('contest_scancode','
		UPDATE contest_scancode
		JOIN contestresponse USING (CONTESTID, USERID)
		SET USERID = 0
		WHERE NOT TOLD
		  AND CONTESTID = '.$contestid)
	||	!db_update('contestresponse','
		UPDATE contestresponse SET
			WINSTATUS = "loser"
		WHERE WINSTATUS = "winner"
		  AND NOT TOLD
		  AND CONTESTID = '.$contestid)
	) {
		return;
	}
	$cnt = db_affected();
	if (!$cnt) {
		register_warning('contest:warning:no_winners_removed_LINE');
	} else {
		register_notice('contest:notice:winners_removed_LINE', ['CNT' => $cnt]);
	}
}

function contest_display_choosewinsform() {
	if (!require_admin('contest')
	||	!($contestid = require_idnumber($_REQUEST, 'sID'))
	) {
		return;
	}
	layout_section_header(__('action:choose_winners'));

	if (!($responses = db_rowuse_array(['contestresponse', 'user_account'], "
		SELECT contestresponse.USERID, WINSTATUS, NICK
		FROM contestresponse
		JOIN user_account USING (USERID)
		WHERE CONTESTID = $contestid
		ORDER BY NICK ASC"))
	) {
		if ($responses !== false) {
			_error('Niemand staat voor deze actie ingeschreven!');
		}
		return;
	}
	?><form<?
	?> onsubmit="return submitForm(this)"<?
	?> method="post"<?
	?> action="/contest/<?= $_REQUEST['sID'] ?>/choosewins"><?

	layout_open_box('contest');
	foreach ($responses as $response) {
		?><input<?
		if ($response['WINSTATUS'] === 'winner') {
			?> checked<?
		}
		?> type="checkbox" value="<?= $response['USERID'] ?>" name="WINNER[]" /> <?=
			get_element_link('user', $response['USERID'], $response['NICK'])
		?><br /><?
	}
	layout_close_box();
	?><div class="block"><input type="submit" value="<?= __('action:send') ?>" /></div><?
	?></form><?
}

function main_menu(?array $contest = null): void {
	$contest_admin = have_admin('contest');
	$action = $_REQUEST['ACTION'];
	layout_open_menu();
	layout_menuitem(Eelement_name('overview'),				'/contest',			!$action && $_REQUEST['PAGE'] !== 'all');
	layout_menuitem(__C('attrib:new')			,			'/contest/new',		$action === 'new');
	layout_menuitem(Eelement_name('entire_list'),			'/contest/list',	$action === 'list');
	layout_menuitem(Eelement_plural_name('beach_party'),	'/contest/beach',	$action === 'beach');
	layout_menuitem(Eelement_plural_name('outdoor_event'),	'/contest/outdoor',	$action === 'outdoor');
	layout_menuitem(Eelement_name('archive'),				'/contest/closed',	$action === 'closed');
	require_once '_contestwins.inc';
	if ($wins = get_won_contest_count(CURRENTUSERID)) {
		layout_menuitem(Eelement_plural_name('won_contest'), '/contest/winmsg/'.CURRENTUSERID.'/all');
	}
	if (SHOW_FEED_ICON) {
		layout_continue_menu();
		layout_open_menuitem();
		show_feed('contest', FEED_ICON);
		layout_close_menuitem();
	}
	layout_close_menu();

	if ($contest_admin) {
		layout_open_menu();
		layout_menuitem(__C('contest:action:unfinished'),	 '/contest/unfinished', $action === 'unfinished');
		layout_menuitem(__C('action:lucky_bastards'),		 '/contest/top',		$action === 'top');
		layout_menuitem(__C('action:recent_lucky_bastards'), '/contest/recent',		$action === 'recent');
		layout_continue_menu();
		layout_menuitem(__C('action:blacklist'),			 '/contest/blacklist',	$action === 'blacklist');
		layout_menuitem(__C('action:search'),				 '/contest/search',		$action === 'search');
		layout_close_menu();
	}

	if (!$contest_admin
	||	!$contest
	||	$contest['ONLYPROMO']
	&&	!have_admin('promo')
	) {
		return;
	}
	$basehref = '/contest/'.$contest['CONTESTID'];
	layout_open_menu();
	layout_menuitem(__C('action:change'), $basehref.'/form');
	if ($contest['ACTIVE']) {
		layout_menuitem(__C('action:deactivate'), $basehref.'/deactivate');
	} else {
		layout_menuitem(__C('action:activate'), $basehref.'/activate');
	}
	if (!$contest['CANCELLED']
	&&	!$contest['MOVEDID']
	&&	!$contest['CLOSED']
	) {
		layout_menuitem(__C('contest:action:allatonce'), $basehref.'/allatonce');
	}
	if ($contest['CLOSED']) {
		layout_menuitem(__C('action:reopen'), $basehref.'/open');
	} else {
		layout_menuitem(__C('action:close'), $basehref.'/close');
	}

	layout_menuitem(__C('contest:action:show_gratulations'), $basehref.'/winmsg');

	require_once '_connect.inc';
	layout_open_menuitem();
	connect_menuitem();
	layout_close_menuitem();

	layout_continue_menu();
	show_element_menuitems();
	layout_close_menu();

	if ($contest['TICKETID']
	||	$contest['CONTACT']
	) {
		$done = db_single('contact_ticket_message', '
			SELECT 1
			FROM contact_ticket_message
			WHERE TEMPLATE = "creation"
			  AND TICKETID = '.$contest['TICKETID']
		);
		layout_open_menu();
		if ($done) {
			?><span class="light"><?
		}
		layout_menuitem(
			__C('action:make_confirmation'),
			$contest['TICKETID']
		?	'/ticket/'.$contest['TICKETID'].'?USETEMPLATE=creation;ELEMENT=contest;ID='.$contest['CONTESTID']
		:	'/ticket/outgoing-form?USETEMPLATE=creation;ELEMENT=contest;ID='.$contest['CONTESTID'].';'.(
					is_number($contest['CONTACT'])
				?	'TO_USERID='.$contest['CONTACT']
				:	'TO='.urlencode($contest['CONTACT'])
			)
		);
		if ($done) {
			?></span><?
		}
		layout_close_menu();
	}

	if ($contest['CANCELLED']) {
		return;
	}
	if ($contest['CLOSED']) {
		if (!($info = db_single_assoc('contestresponse','
			SELECT	COUNT(if(TOLD = 0, 1, NULL)) AS UNTOLD_CNT,
					COUNT(if(WINSTATUS = "winner", 1, NULL)) AS WINNER_CNT
			FROM contestresponse
			WHERE CONTESTID = '.$_REQUEST['sID'].'
			  AND WINSTATUS = "winner"'))
		) {
			return;
		}
		layout_open_menu();
		if ($contest['EXTERNAL']) {
			layout_menuitem(__C('action:choose_winners'), $basehref.'/choosewinsform');
		} else {
			if (false !== ($multi_chance = db_simple_hash(['contest_extra_chance','targetsmsent','targetsmsent', 'user', 'user_account'], "
				SELECT CLEANPHONE, COUNT(DISTINCT user.USERID)
				FROM contest_extra_chance
				JOIN targetsmsent ON targetsmsent.REPLYTO = contest_extra_chance.MESSAGEID
				JOIN targetsms ON targetsms.MESSAGEID = targetsmsent.REPLYTO
				JOIN user ON SENDTO = CLEANPHONE
				JOIN user_account
				  ON user_account.USERID = user.USERID
				JOIN contestresponse
				  ON contestresponse.USERID = user.USERID
				 AND contestresponse.CONTESTID = contest_extra_chance.CONTESTID
				WHERE contest_extra_chance.CONTESTID = {$contest['CONTESTID']}
				  AND RECEIVED != 0
				  AND targetsmsent.STATUS = 0
				  AND user_account.STATUS = 'active'
				GROUP BY CLEANPHONE
				HAVING COUNT(DISTINCT user.USERID) > 1"))
			&&	$info
			&&	$info['WINNER_CNT'] < $contest['GROUPS']
			) {
				layout_menuitem(__C('action:determine_winners'), $basehref.'/make-winners');
			}
		}
		if ($info['WINNER_CNT']) {
			layout_menuitem(__C('action:show_winners'), $basehref.'/show-winners');
		}
		if ($contest['MOVEDID']) {
			layout_open_menuitem();
			?><span class="unavailable help" title="<?= __('contest:info:moved_need_target_LINE') ?>"><?= __C('action:notify_winners') ?></span><?
			layout_close_menuitem();
		} else {
			if (!$contest['EXTERNAL']
			&&	$info['UNTOLD_CNT']
			) {
				layout_open_menuitem();
				with_confirm(__C('action:notify_winners'), $basehref.'/msgwins', __C('contest:confirm:notify_winners_LINE'));
				layout_close_menuitem();
			}
		}
		if (!$contest['EXTERNAL']
		&&	$info['UNTOLD_CNT']
		) {
			layout_continue_menu();
			layout_menuitem(__C('action:remove_winners'), $basehref.'/remove-winners');
		}
		layout_close_menu();
	}
}

function contest_display_form(): void {
	require_once '_contest.inc';
	require_once '_partyoptions.inc';
	require_once '_scancodes.inc';

	$contest_admin = have_admin('contest');

	if (!($elems = am_employee())
	&&	!require_admin('contest')
	) {
		return;
	}

	layout_show_section_header();

	$contest = null;
	$partyid = null;

	if ($contestid = have_idnumber($_REQUEST,'sID')) {
		if (!($contest = db_single_assoc('contest', 'SELECT * FROM contest WHERE CONTESTID = '.$contestid))) {
			if ($contest !== false) {
				register_error('contest:error:nonexistent_LINE', ['ID' => $contestid]);
			}
			return;
		}
	} elseif ($contest_admin) {
		if ($ticketid = have_idnumber($_REQUEST, 'TICKETID')) {
			require_once '_contact_ticket_first_message.inc';
			if (!($info = contact_display_first_ticket_message($ticketid))) {
				return;
			}
			[$ticket, $parties] = $info;

		} else {
			$partyid = have_idnumber($_REQUEST, 'PARTYID');
		}
	} elseif (!($parties = eligble_parties($elems))) {
		register_warning('contest:warning:no_eligble_parties_future_LINE');
		return;
	}
	$spec = explain_table('contest');
	include_js('js/form/contest');

	if (!$contestid) {
		$scancodestr = '';
	} elseif (false === ($scancodestr = db_single('contest_scancode', "
		SELECT GROUP_CONCAT(SCANCODE SEPARATOR '\n')
		FROM contest_scancode
		WHERE CONTESTID = $contestid
		ORDER BY SCANCODE"))
	) {
		return;
	}

	?><form<?
	?> accept-charset="utf-8"<?
	?> onsubmit="return Pf.submitContestForm(this, <?= $contestid ?: 0 ?>)"<?
	?> method="post"<?
	?> action="/contest<? if ($contestid) echo '/', $contestid ?>/commit"><?

	?><input type="hidden" name="FORMSTAMP" value="<?= CURRENTSTAMP ?>" /><?

	$non_entrance = $contest && !in_array($contest['TYPE'], ['guestlist', 'freeticket', 'eticket', 'scancode'], strict: true);

	$party_stamp = $partyid ? db_single('party', 'SELECT STAMP FROM party WHERE PARTYID = '.$partyid) : null;

	layout_open_box('contest');
	layout_open_table('fw');
	layout_start_row();
		echo Eelement_name('event');
		layout_field_value('fw');
		?><select<?
		?> name="PARTYID"<?
		?> class="fw"<?
		?> onchange="Pf.changeContestParty(this);"<?
		if (!$non_entrance) {
			?> required<?
		}
		?>><?
		?><option></option><?
		$info =	$contest_admin
		?	show_party_options(
				selected: empty($partyid)
				?	(	isset($parties)
					?	$parties
					:	(	$contestid
						?	$contest['PARTYID'] : 0)
					)
				:	$partyid,
				future:	  true,
				disabled: false,
				labels:   isset($parties) ? party_source_labels() : null
			)
		:	show_party_options(
				selected:  0,
				future:	true,
				disabled:  false,
				labels:	null,
				parties:   $parties
			);
		?></select><?

	$selected = $info ? $info[1] : false;

	layout_restart_row($selected ? 0 : ROW_HIDDEN,'addGoingRow');
		$addgoing = $contest ? $contest['ADDGOING'] : true;//$selected;
		layout_next_cell();
		?><label class="<? if (!$addgoing) echo 'not-' ?>hilited"><?
		show_input([
			'type'		=> 'checkbox',
			'class'		=> 'upLite',
			'name'		=> 'ADDGOING',
			'value'		=> 1,
			'checked'	=> $addgoing
		]);
		?> <?
		echo __('contest:info:contestant_becomes_doubter_too');
		?></label><?

	layout_restart_row();
		$name_is = ($contest && $contest['PARTYID'] || $partyid) ? 'addition' : 'title';

		?><span<?= $name_is !== 'title'	? ' class="hidden"' : '' ?> id="name-is-title"><?=	Eelement_name('title')		 ?></span><?
		?><span<?= $name_is !== 'addition' ? ' class="hidden"' : '' ?> id="name-is-addition"><?= Eelement_name('title_addition') ?></span><?

		layout_field_value();
		show_input([
			'type'		=> 'text',
			'name'		=> 'NAME',
			'onchange'	=> 'Pf.changeContestName(this)',
			'value'		=> $contest,
			'spec'		=> $spec,
		]);

	layout_restart_row();
		?><label for="direct"><?= Eelement_name('publication') ?></label><?
		layout_field_value();
		show_publication($contest);

	layout_restart_row();
		echo Eelement_name('closing_date');
		layout_field_value();
		if (!$contestid
		||	$party_stamp
		) {
			?><label for="close_before" class="bold-hilited"><?
			?><input class="upLite" id="close_before" type="radio" value="days" name="CLOSE_TYPE" checked="checked" /><?
			?></label> <?
			show_input([
				'onkeydown'	=> "getobj('close_before').click()",
				'onkeyup'	=> "Pf.updateCloseStampOrDays(this)",
				'class'		=> 'right three_digits',
				'type'		=> 'text',
				'name'		=> 'CLOSEDAYS',
				'value'		=> !empty($contest['CLOSE_STAMP'])
						&& !empty($party_stamp)
						?  floor(($party_stamp - $contest['CLOSE_STAMP']) / ONE_DAY)
						:  ($contest_admin ? CLOSE_CONTEST_DAYS : CLOSE_CONTEST_DAYS_CONNECTED)
			]);
			?> <?
			echo element_plural_name('weekday_before_event');
			?></span><br /><?
			?><label for="close_stamp" class="not-bold-hilited"><?
			show_input([
				'class'		=> 'upLite',
				'id'		=> 'close_stamp',
				'type'		=> 'radio',
				'onchange'	=> "Pf.updateCloseStampOrDays(this)",
				'value'		=> 'stamp',
				'name'		=> 'CLOSE_TYPE',
			]);
			?></label> <?
			$do_onchange = true;
		} else {
			?><input type="hidden" name="CLOSE_TYPE" value="stamp" /><?
		}

		[$year, $month, $day] = _getdate($close_stamp = $contest['CLOSE_STAMP'] ?? ($party_stamp ? strtotime('-4 weekdays 8:00', $party_stamp) : CURRENTSTAMP));

		_datetime_display_select_stamp(
			stamp:				 $close_stamp,
			prefix:				 'CLOSE_',
			relative_start_year: $year,
			relative_end_year:   $year + 3,
			no_time:			 false,
			select_hour:		 18,
			onchange: isset($do_onchange)
			?	[	'DAY'	=> "getobj('close_stamp').click();Pf.updateCloseStampOrDays(this)",
					'MONTH'	=> "getobj('close_stamp').click();Pf.updateCloseStampOrDays(this)",
					'YEAR'	=> "getobj('close_stamp').click();Pf.updateCloseStampOrDays(this)",
					'HOUR'	=> "getobj('close_stamp').click();Pf.updateCloseStampOrDays(this)",
					'MINS'	=> "getobj('close_stamp').click();Pf.updateCloseStampOrDays(this)"
				]
			:	null
		);

	layout_restart_row();
		echo Eelement_name('type');
		layout_field_value();
		?><select required="required" name="TYPE" onchange="Pf.changeContestType(this)"><?
			?><option value=""></option><?
			foreach ([
				'guestlist',
				'freeticket',
				'eticket',
				'scancode',
				'cd',
				'dvd',
				'vinyl',
				'discount',
				'wear',
				'other',
			] as $type) {
				?><option<?
				?> value="<?= $type; ?>"<?
				if ($contestid
				&&	$contest['TYPE'] === $type
				) {
					?> selected<?
				} elseif ($type === 'freeticket') {
					?> disabled<?
				}
				?>><?= contest_get_type(2, $type); ?></option><?
			}
		?></select><?

	layout_restart_row($contestid && $contest['TYPE'] === 'scancode' ? 0 : ROW_HIDDEN, 'scancode-type-row');
		echo Eelement_name('scancode_type');
		layout_field_value();

		$selected_scancodes_type = $contestid ? $contest['SCANCODES_TYPE'] : ($partyid ? get_scancodes_type($partyid) : null);

		?><select name="SCANCODES_TYPE"><?
		foreach ([
			'',
			'EAN-13',
			'CODE 39',
			'CODE 128',
			'QR',
		] as $scancode_type) {
			?><option<?
			if ($selected_scancodes_type
			&&	$selected_scancodes_type === $scancode_type
			) {
				?> selected<?
			}
			?> value="<?= $scancode_type ?>"><?= $scancode_type ?></option><?
		}
		?></select><?

	layout_restart_row($contestid && $contest['TYPE'] === 'scancode' ? 0 : ROW_HIDDEN, 'scancode-release-row');
		echo Eelement_name('release');
		layout_field_value();
		$release_hours = array_merge(range(1, 24), [32, MAX_SCANCODES_RELEASE_HOUR]);
		$selected_hour = $contest ? $contest['SCANCODES_RELEASE'] : 24;
		$party = !empty($contest['PARTYID']) ? memcached_party_and_stamp($contest['PARTYID']) : null;
		?><select name="SCANCODES_RELEASE" class="right" onchange="Pf.updateScancodeReleaseDate(this.form)"><?
		foreach ($release_hours as $release_hour) {
			?><option<?
			if ($release_hour === $selected_hour) {
				?> selected<?
			}
			?> value="<?= $release_hour ?>"><?= $release_hour ?></option><?
		}
		?></select><?
		?> <?
		echo __('scancodes:info:hours_before_start_event', ['HOURS' => 2]);
		?>: <b><?
		?><span id="scancode-release-date"><?
		if ($party) {
			_datedaytime_display($party['STAMP'] - $selected_hour * ONE_HOUR);
		}
		?></span></b><?

	layout_restart_row($contestid && $contest['TYPE'] === 'scancode' ? 0 : ROW_HIDDEN, 'scancodes-row');
		echo Eelement_plural_name('scancode');
		layout_field_value();
		$upper_scancode_str = strtoupper(element_name('scancode'));
		show_textarea([
			'class'				=> 'growToFit tt',
			'data-max'			=> 10,
			'name'				=> 'SCANCODES',
			'value'				=> $scancodestr,
			'placeholder'		=> "$upper_scancode_str\n$upper_scancode_str\n$upper_scancode_str\n",
		]);

		?><label class="basehl cbi"><?
		show_input([
			'type'		=> 'checkbox',
			'name'		=> 'SCANCODES_REPLACE',
			'value'		=> '1',
			'onclick'	=> /** @lang JavaScript */ "setclass(this.parentNode, this.checked, 'bold hilited-orange');",
		]);
		?> <?= __('action:replace');
		?></label><?

	$entrance_included = !empty($contest['ENTRANCE_INCLUDED']);
	layout_restart_row($selected && $non_entrance ? 0 : ROW_HIDDEN, 'entranceIncludedRow');
		layout_next_cell();
		?><label class="<? if (!$entrance_included) echo 'not-' ?>hilited"><?
		show_input([
			'type'		=> 'checkbox',
			'class'		=> 'upLite',
			'name'		=> 'ENTRANCE_INCLUDED',
			'value'		=> '1',
			'checked'	=> $entrance_included
		]);
		?> <?
		echo __('contest:info:entrance_included');
		?></label><?

	layout_restart_row(!$contestid || in_array($contest['TYPE'], ['guestlist', 'eticket', 'scancode'], strict: true) ? 0 : ROW_HIDDEN, 'glistcloserow');
		echo __C('field:guestlist_closes');
		layout_field_value();
		if ($contestid) {
			_time_display_select('GLCLOSE', $contest['GLCLOSEHOUR'], $contest['GLCLOSEMINS'], $contest['GLCLOSEHOUR'] === 24);
		} else {
			_time_display_select('GLCLOSE', no_time: true);
		}

	layout_restart_row();
		echo Eelement_name('amount');
		layout_field_value();
		show_input([
			'onkeyup'		=> /** @lang JavaScript */ "setattr(this.form.VISIBLE_GROUPS, 'max', this.form.VISIBLE_GROUPS.value = this.value);",
			'class'			=> 'center',
			'style'			=> 'width: 7.5em;',
			'type'			=> 'number',
			'required'		=> true,
			'min'			=> 1,
			'name'			=> 'GROUPS',
			'value'			=> $contest,
			'placeholder'	=> element_plural_name('winner'),
		]);
		echo MULTIPLICATION_SIGN_ENTITY ?>  <?
		show_input([
			'class'			=> 'center',
			'style'			=> 'width: 7.5em;',
			'type'			=> 'number',
			'required'		=> true,
			'min'			=> 1,
			'name'			=> 'AMOUNT',
			'value'			=> $contest,
			'placeholder'	=> element_plural_name('unit'),
		]);
	layout_restart_row();
		echo __C('field:visible');
		layout_field_value();
		show_input(array(
			'class'			=> 'center',
			'style'			=> 'width: 7.5em;',
			'type'			=> 'number',
			'min'			=> 0,
			'max'			=> $contestid ? ($contest['GROUPS'] ?: 1) : 1,
			'name'			=> 'VISIBLE_GROUPS',
			'value'			=> $contest,
			'data-warn'		=> __('contest:question:visible_less_than_actual_LINE'),
			'placeholder'	=> element_plural_name('winner'),
		));

	layout_restart_row();
	if (isset($ticket)) {
		echo Eelement_name('contact_ticket');
		layout_field_value();
		?><input type="hidden" name="TICKETID" value="<?= $ticket['TICKETID'] ?>" /><?
		?><a href="/ticket/<?= $ticket['TICKETID'] ?>">#<?= $ticket['TICKETID'] ?></a> <?
		if ($ticket['ISMAIL']) {
			?> met <a href="<?= _make_illegible('mailto:'.$ticket['FROM_EMAIL']) ?>"><?= escape_specials($ticket['FROM_NAME']) ?></a><?
		} else {
			?> met <?= get_element_link('user',$ticket['USERID_FROM']);
		}
	} else {
		if ($contest_admin) {
			echo Eelement_name('contact_ticket');
			layout_field_value();
			show_input([
				'type'		=> 'number',
				'name'			=> 'TICKETID',
				'class'			=> 'id',
				'value'			=> $contest['TICKETID'] ?? null,
				'spec'			=> $spec,
				'placeholder'	=> element_name('contact_ticket').' ID'
			]);
			require_once '_viavia.inc';
			show_viavia_form_rows($contest);
			layout_restart_row();
		}
		echo Eelement_name('contact_person');
		layout_field_value();
		if ($contest_admin) {
			show_input([
				'type'			=> 'text',
				'name'			=> 'CONTACT',
				'value_utf8'	=> $contest,
				'spec'			=> $spec,
				'placeholder'	=> element_name('email_or_userid')
			]);
		} else {
			?><input type="hidden" name="CONTEST" value="<?= CURRENTUSERID ?>" /><?
			echo get_element_link('user',CURRENTUSERID);
		}
	}
	layout_restart_row();
		?>Extra info (exposure)<?
		layout_field_value();
		show_textarea([
			'cols'		=> 60,
			'rows'		=> 10,
			'class'		=> 'growToFit',
			'name'		=> 'BODY',
			'value_utf8'	=> $contest,
			'spec'		=> $spec
		]);
	layout_restart_row();
		?>Bericht voor winnaars<?
		layout_field_value();
		if ($contest_admin) {
			?><select<?
			?> onchange="setdisplay('winmsg', this.value == '2' || this.value == '0')"<?
			?> name="STDWINMSG"><?
				?><option<? if (!$contestid || $contest['STDWINMSG'] == 1) echo ' selected="selected"'; ?> value="1">standaard</option><?
				?><option<? if ($contestid && $contest['STDWINMSG'] == 2) echo ' selected="selected"'; ?> value="2">standaard + toevoeging</option><?
				?><option<? if ($contestid && !$contest['STDWINMSG']) echo ' selected="selected"'; ?> value="0">compleet anders</option><?
			?></select><?
			?><div<?
			if (!$contestid
			||	$contest['STDWINMSG'] == 1
			) {
				?> class="hidden"<?
			}
			?> id="winmsg"><?
			?><div><?
			show_textarea([
				'name'			=> 'WINMSG',
				'class'			=> 'growToFit',
				'cols'			=> 60,
				'rows'			=> 10,
				'value_utf8'	=> $contest['WINMSG'] ?? '',
				'spec'			=> $spec
			]);
			?></div><?
			?>De volgende elementen zullen vervangen worden voor elke afzonderlijke winnaar:<br /><?
			?><tt>#REALNAME#, #ADDRESS#, #ZIPCODE#, #CITY#, #EMAIL#</tt><?
			?></div><?
			?></div><?
		} else {
			?><input type="hidden" name="STDWINMSG" value="1" /><?
			?>standaard<?
		}
	layout_restart_row();
		?><label for="active"><?= __C('status:active')?></label><?
		layout_field_value();
		?><input<?
		if (!$contestid
		||	$contest['ACTIVE']
		) {
			?> checked<?
		}
		?> type="checkbox" value="1" name="ACTIVE" id="active" /><?
	if ($contest_admin) {
		layout_restart_row();
			?><label for="external">Extern</label><?
			layout_field_value();
			?><input type="checkbox" value="1" name="EXTERNAL" id="external"<?
			if (!empty($contest['EXTERNAL'])) {
				?> checked<?
			}
			?> /><?
	}
	if (have_admin('promo')) {
		layout_restart_row();
		?><label for="onlypromo"><?= __C('attrib:only_for_promo') ?></label><?
		layout_field_value();
		?><input type="checkbox" value="1" name="ONLYPROMO" id="onlypromo"<?
		if (!empty($contest['ONLYPROMO'])) {
			?> checked<?
		}
		?> /><?
	}
	layout_restart_row();
		echo __C('header:admin_comments');
		layout_field_value();
		show_textarea([
			'name'			=> 'ADMINTEXT',
			'cols'			=> 60,
			'rows'			=> 5,
			'class'			=> 'growToFit',
			'value_utf8'	=> $contest,
			'spec'			=> $spec
		]);
	layout_stop_row();
	layout_close_table();
	layout_close_box();
	?><div class="block"><input type="submit" value="<?= __($contestid ? 'action:change' : 'action:add'); ?>" /></div><?
	?></form><?
}

function contest_display_single() {
	require_once '_contest.inc';
	if (!require_idnumber($_REQUEST,'sID')) {
		return;
	}
	$contestid = $_REQUEST['sID'];
	if (false === ($contest = db_single_assoc(['contest','party','location','city','user_account','contact_ticket'],'
		SELECT	contest.*,
				party.PARTYID AS PARTY_PARTYID, party.NAME AS PARTY_NAME, party.MIN_AGE, party.MIN_AGE_FEMALE, MAX_AGE, party.LOCATIONID,
				party.STAMP, party.DURATION_SECS, CANCELLED, MOVEDID, POSTPONED, NOTIME, AT2400, party.ACCEPTED,
				location.CITYID,
				contact_ticket.STATUS AS TICKET_STATUS,
				TIMEZONE	
		FROM contest
		LEFT JOIN party ON party.PARTYID = contest.PARTYID
		LEFT JOIN location ON location.LOCATIONID = party.LOCATIONID
		LEFT JOIN boarding ON boarding.BOARDINGID = party.BOARDINGID
		LEFT JOIN city ON city.CITYID = COALESCE(boarding.CITYID, location.CITYID, party.CITYID)
		LEFT JOIN contact_ticket ON contact_ticket.TICKETID = contest.TICKETID
		WHERE contest.CONTESTID = '.$contestid))
	) {
		return;
	}
	if (!$contest) {
		not_found();
		return;
	}
	if (!($contest_admin = have_admin('contest'))) {
		if (!$contest['ACTIVE']) {
			register_warning('contest:join:error:inactive_LINE');
			return;
		}
		if ($contest['PSTAMP'] > CURRENTSTAMP) {
			register_warning('contest:join:error:not_published_yet_LINE');
			return;
		}
	}
	if ( $contest['PARTYID']
	&&	!$contest['PARTY_PARTYID']
	) {
		warning('Actie is gekoppeld aan niet bestaand feest!');
		$contest['PARTYID'] = 0;
	}
	require_once '_element_access.inc';
	if (!may_view_element('contest', $contestid, $contest)) {
		return;
	}
	layout_show_section_header();

	main_menu($contest);

	require_once '_ticketlist.inc';
	show_connected_tickets();

	if ($contest_admin) {
		require_once '_connect.inc';
		_connect_display_form('contest', $contestid);
	}
	if ($contest['MOVEDID']) {
		?><h3 class="warning"><?= __('party:error:is_moved_LINE', DO_UBB, ['PARTYID' => $contest['PARTYID'], 'MOVEDID' => $contest['MOVEDID']]) ?></h3><?
	} elseif ($contest['CANCELLED']) {
		?><h3 class="error"><?= __('party:error:is_cancelled_LINE', DO_UBB, ['PARTYID' => $contest['PARTYID']]) ?></h3><?
	}
	if ($contest['ADMINTEXT']
	&&	$contest_admin
	) {
		layout_open_box('white');
		?><div class="block"><span class="warning"><?= __C('header:admin_comments') ?></span><br /><?
		echo make_all_html($contest['ADMINTEXT'], UBB_UTF8);
		?></div><?
		layout_close_box();
	}
	$non_entrance = !in_array($contest['TYPE'], ['guestlist', 'freeticket', 'eticket', 'scancode'], strict: true);

	?><article itemscope itemtype="https://schema.org/Article"><?
	require_once '_drag.inc';
	open_drag();
	layout_open_box(!$contest['ACTIVE'] ? 'unaccepted contest' : 'contest');
	layout_open_box_header();
		?><h1 itemprop="name"><?

		if ($non_entrance) {
			if ($contest['NAME']) {
				echo escape_utf8($contest['NAME']);
			}
			if ($contest['PARTYID']) {
				?> (<? echo escape_utf8($contest['PARTY_NAME']) ?>)<?
			}
		} else {
			if ($contest['PARTYID']) {
				echo escape_utf8($contest['PARTY_NAME']);
			}
			if ($contest['NAME']) {
				?> <?
				echo escape_utf8($contest['NAME']);
			}
		}
		?> <?= element_name('contest');
		?></h1><?
	layout_close_box_header();
	?><time pubdate datetime="<?= gmdate(ISO8601Z,$contest['PSTAMP']) ?>"></time><?

	show_contest_uploadimage($contestid,$contest['PARTYID']);

	if ($contest['ONLYPROMO']
	&&	have_admin(['contest','promo'])
	) {
		?><div class="warning block">(<?= __('attrib:only_for_promo') ?>)</div><?
	}

	$list = new deflist('deflist vtop');

	$party_field = null;
	if ($contest['PARTYID']) {
		$party_field = Eelement_name('party');
		$party_value = '<b>'.get_element_link('party',$contest['PARTYID'],$contest['PARTY_NAME']).'</b>';
	}
	if ($contest['NAME']) {
		if (!$non_entrance
		&&	$party_field
		) {
			$list->add_row($party_field, $party_value);
			$party_field = null;
		}
		$list->add_row(Eelement_name('description'), escape_utf8($contest['NAME']));

		if ($party_field) {
			$list->add_row($party_field, $party_value);
			$list->add_row(null,element_name('entrance').'  &rarr; '.__($contest['ENTRANCE_INCLUDED'] ? 'status:included' : 'status:not_included'));
		}
	} elseif ($party_field) {
		$list->add_row($party_field,$party_value);
	}

	if ($contest['STAMP']) {
		$list->add_row(Eelement_name('date'), datedaytime_get_link($contest));
	}
	if ($contest['PARTYID']) {
		if (false === ($orgs = memcached_simple_hash(['connect','organization'],'
			SELECT ASSOCID AS ORGANIZATIONID,NAME
			FROM connect
			JOIN organization ON ASSOCID=ORGANIZATIONID
			WHERE MAINTYPE="party"
			  AND ASSOCTYPE="organization"
			  AND MAINID='.$contest['PARTYID']))
		) {
			return;
		}
		if ($orgs) {
			$list->add_row(Eelement_name('organization'), get_element_list('organization',$orgs));
		}
	}
	if ($contest['LOCATIONID']) {
		ob_start();
		echo get_element_link('location',$contest['LOCATIONID']);
		if ($contest['CITYID']) {
			?>, <? echo get_element_link('city',$contest['CITYID']);
		}
		$list->add_row(Eelement_name('location'), ob_get_clean());
	}

	$list->add_row(Eelement_name('type'), contest_get_type($contest['AMOUNT'],$contest['TYPE']));

	if ($contest_admin
	&&	$contest['TYPE'] === 'guestlist'
	) {
		if (false === ($codesinfo = db_single_array('contestcode', 'SELECT COUNT(*), COUNT(IF(CONFIRMED, 1, NULL)) FROM contestcode WHERE CONTESTID = '.$contestid))) {
			return;
		}
		$note = element_plural_name('realname');
		if ($codesinfo) {
			[$total, $confirmed] = $codesinfo;
			if ($confirmed) {
				$note = element_plural_name('unique_code');
				if ($confirmed < $contest['GROUPS']) {
					$note .= '<br /><span class="warning">'.__('contest:warning:not_all_amount_confirmed_codes_LINE').'</span>';
				}
			}
		}
		$list->set_row_class('light');
		$list->add_row(Eelement_name('list_notation'),'<b>'.$note.'</b>');
	}

	iF ($contest['TYPE'] === 'scancode') {
		$list->add_row(Eelement_name('release'), __('scancodes:info:hours_before_start_event', ['HOURS' => $contest['SCANCODES_RELEASE']]));
	}

	if ($contest['VISIBLE_GROUPS']) {
		ob_start();
		if ($contest['VISIBLE_GROUPS'] < $contest['GROUPS']) {
			echo __('attrib:at_least') ?> <?
		}
		echo $contest['VISIBLE_GROUPS'];
		if ($contest['PARTYID']
		||	$contest['AMOUNT'] > 1
		) {
			echo MULTIPLICATION_SIGN_ENTITY ?><? echo $contest['AMOUNT'];
		}
		$list->add_row(Eelement_name('amount'), ob_get_clean());
	}

	if ($contest['CLOSE_STAMP']) {
		[$y, $m, $d, $hour, $mins] = _getdate($contest['CLOSE_STAMP']);
		$list->add_row(Eelement_name('closing_date'), _date_getnice($hour || $mins ? PRINTNICE_DATE_DAY_TIME : PRINTNICE_DATE_DAY,$contest['CLOSE_STAMP']));
	}

#	$list->add_row(Eelement_name('duration'), CURRENTTAMP - $contest['CSTAMP']) / ONE_DAY);
	require_once '_duration.inc';
	show_duration_rows($list, ['STARTSTAMP' => $contest['CSTAMP'], 'STOPSTAMP' => $contest['CLOSE_STAMP']], DURATION_HIDE_START | DURATION_HIDE_STOP);

	if ($contest_admin
	&&	$contest['CONTACT']
	) {
		$list->set_row_class('light');
		$list->add_row(Eelement_name('contact_person'),
			is_number($contest['CONTACT'])
		?	get_element_link('user',$contest['CONTACT'])
		:	_make_illegible($contest['CONTACT'])
		);
	}
	if ($age_value = $contest['MIN_AGE']) {
		ob_start();
		require_once '_bubble.inc';
		$bubble = new bubble(HELP_BUBBLE);
		$bubble->catcher(Eelement_name('minimum_age'));
		$bubble->content(__('contest:info:age_warning_LINE'));
		$bubble->display();
		$bubble_str = ob_get_clean();

		if ($contest['MIN_AGE_FEMALE']) {
			$age_value .= ' ('.element_plural_name('woman').' '.$contest['MIN_AGE_FEMALE'].')';
		}
		$list->add_row($bubble_str, $age_value);
	}
	if ((	($glist = $contest['TYPE'] === 'guestlist')
		||	in_array($contest['TYPE'], ['eticket', 'scancode'], strict: true)
		)
	&&	$contest['GLCLOSEHOUR'] != 24
	) {
		$list->add_row(
			__C($glist ? 'field:guestlist_closes' : 'field:eticket_closes'),
			'<b>'.sprintf('%02d:%02d',$contest['GLCLOSEHOUR'],$contest['GLCLOSEMINS']).'</b>'
		);
	}
	if ($contest_admin) {
		require_once '_viavia.inc';
		show_viavia_rows($contest, $list, class: 'light');
	}

	$list->display();

	require_once '_contestwinners.inc';
	show_contest_winners($contest);

	if ($contest['BODY']) {
		?><hr class="slim"><?
		?><div class="body block"><?= make_all_html($contest['BODY'], UBB_UTF8, 'contest', $contestid); ?></div><?
	}

	layout_display_alteration_note($contest, true);
	layout_close_box();

	close_drag();

	if (!$contest['CLOSED']
	&&	!$contest['CANCELLED']
	&&	!$contest['MOVEDID']
	&&	!$contest['POSTPONED']
	&&	!$contest['EXTERNAL']
	) {
		?><div id="joinpart"><?

		if (!have_user()) {
			if (!ROBOT) {
				?><div class="block"><?
				invite_registration('contest','invite:join_contest_LINE');
				?></div><?
			}
/*			?><div class="block"><?= __('contest:info:only_for_registered_LINE',DO_UBB) ?></div><?*/
		} else {
/*			if (false === ($blacklisted = memcached_rowuse_hash(['contestblacklist', 'contest', 'party'],'
				SELECT contestblacklist.USERID, contestblacklist.CONTESTID, STOPSTAMP, contest.NAME, PARTYID, party.NAME AS PARTY_NAME
				FROM contestblacklist
				JOIN contest USING (CONTESTID)
				LEFT JOIN party USING (PARTYID)
				WHERE contestblacklist.STAMP <= '.TODAYSTAMP.'
				  AND contestblacklist.STOPSTAMP > '.TODAYSTAMP))*/
			if (false === ($joined = db_single('contestresponse', '
				SELECT 1 FROM contestresponse
				WHERE CONTESTID = '.$_REQUEST['sID'].'
				  AND USERID = '.CURRENTUSERID))
			) {
				return false;
			}

/*			if (isset($blacklisted[CURRENTUSERID])
			&&	$contest['STAMP'] < $blacklisted[CURRENTUSERID]['STOPSTAMP']
			&&	!$joined
			) {
				?><div class="block"><?= __('contest:info:blacklisted_TEXT',DO_UBB|DO_NL2BR,array(
					'PARTYID'	=> $blacklisted[CURRENTUSERID]['PARTYID'],
					'CONTESTID'	=> $blacklisted[CURRENTUSERID]['CONTESTID']
	;			));
				?></div><?
			} else {*/

				require_once '_contestparticipation.inc';
				if ($joined) {
					?><nav class="smenu"><?
					?><div class="block"><?
					?><b><?= __('contest:you_participate_LINE') ?></b> <?
					?><i><a href="/contest/<?= $contestid ?>/leave"><?= __C('contest:i_dont_want_to_participate') ?></a>&hellip;</i><?
					?></div><?
					?></nav><?

					if (CURRENTDOMAIN === 'nl'
					&&	CURRENTLANGUAGE === 'nl'
					&&	$GLOBALS['currentuser']->row['COUNTRYID'] === 1
					) {
						require_once '_contestchance.inc';
						$sent_cnt =
						$have_all_sms = null;
						$total_chance = get_win_chance(
							$contest,
							CURRENTUSERID,
							add_one: false,
							get_sent_cnt: $sent_cnt,
							get_have_all_sms: $have_all_sms
						);

/*						layout_open_box('white');
						?><div class="body block"><?= __('contest:info:increase_chance_TEXT', DO_NL2BR | DO_UBB, [
							'SMSOVERRULE'	=> $have_all_sms ? 1 : 0,
							'X'				=> $sent_cnt,
							'CONTESTID'		=> $contestid,
							'WINPCT'		=> round($total_chance, 1).'%',
							'PHONE'			=> db_single('user', 'SELECT PHONE FROM user WHERE CLEANPHONE != 0 AND USERID = '.CURRENTUSERID),
						]
						) ?></div><?
						layout_close_box();*/

						if ($unused = db_single(['contestresponse', 'contest', 'contest_extra_chance', 'targetsmsent', 'targetsms', 'user', 'party'],'
							SELECT COUNT(DISTINCT contest_extra_chance.MESSAGEID)
							FROM contestresponse
							JOIN contest USING (CONTESTID)
							JOIN contest_extra_chance USING (CONTESTID)
							JOIN targetsmsent ON targetsmsent.REPLYTO = contest_extra_chance.MESSAGEID
							JOIN targetsms ON targetsms.MESSAGEID = targetsmsent.REPLYTO
							JOIN user
								 ON SENDTO = CLEANPHONE
								AND user.USERID = contestresponse.USERID
							LEFT JOIN party USING (PARTYID)
							LEFT JOIN contestresponse AS winner
								 ON winner.WINSTATUS = "winner"
								AND winner.CONTESTID = contestresponse.CONTESTID
							WHERE contestresponse.USERID = '.CURRENTUSERID.'
							  AND (	party.PARTYID IS NULL
							 	OR	party.STAMP < '.CURRENTSTAMP.'
								  )
							  AND MOVEDTO = 0
							  AND RECEIVED != 0
							  AND STATUS = 0
							  AND CLOSED
							  AND (contest.TOLD = 0 OR winner.CONTESTID IS NULL)')
						) {
							layout_open_box('white');
							?><div class="body block"><?
							echo __('contest:info:unused_extra_chance_TEXT', DO_UBB | DO_NL2BR, [
								'X'		=> $unused,
								'URL'	=> "/contest/$contestid/movechance",
							]);
							?></div><?
							layout_close_box();
							?><div class="block smenu notice ns"><?
							?><a href="/contest/<?= $contestid ?>/movechance"><?= __C('action:move_chance_here',['X'=>$unused]) ?></a><?
							?></div><?
						}
						$failed = db_rowuse_array('targetsmsent','
							SELECT STATUS,COUNT(*) AS CNT,MAX(STAMP) AS LSTAMP
							FROM targetsmsent
							JOIN targetsms ON REPLYTO=targetsms.MESSAGEID
							JOIN user
							WHERE SENDTO=CLEANPHONE
							  AND STATUS!=0
							  AND USERID='.CURRENTUSERID.'
							  AND STAMP>'.(TODAYSTAMP - ONE_WEEK).'
							GROUP BY STATUS'
						);
						if ($failed) {
							# see https://www.targetsms.nl/statuscodes
							layout_open_box('white');
							?><div class="body block"><?= __('contest:info:failed_smsent_TEXT',DO_UBB|DO_NL2BR) ?></div><?
							?><div class="body block"><?
							foreach ($failed as $info) {
								extract($info);
								echo $CNT, MULTIPLICATION_SIGN_ENTITY ?> <?= $STATUS
								?>, <?= __('targetsmstatus:'.$STATUS)
								?>, <?= __('field:last') ?>: <? _datedaytime_display($LSTAMP) ?><br /><?
							}
							?></div><?
							layout_close_box();
						}
					}

					layout_open_box('white');
					?><div class="body block"><?= __('contest:info:via_site_LINE',DO_UBB) ?></div><?
					?><div class="body block"><?
					echo __(in_array($contest['TYPE'], [
						'guestlist',
						'freeticket',
						'eticket',
						'scancode',
					], true)
					?	'contest:info:free_entrance_info_TEXT'
					:	'contest:info:verify_address_TEXT',
					DO_NL2BR|DO_UBB|DO_CONDITIONAL);
					?></div><?
					layout_close_box();

				} elseif (may_participate($contest, CURRENTUSERID, $errstr)) {
					?><nav class="smenu"><?
					?><div class="block"><?
					?><a href="/contest/<?= $contestid ?>/join#joinpart"><?= __('contest:i_want_to_participate_LINE')
					?></a><?
					?></div><?
					?></nav><?

				} else {
					?><div class="block"><?
					echo __('contest:you_cannot_participate_LINE');
					if ($errstr) {
						?><br /><?
						echo $errstr;
					}
					?></div><?
				}
//			}
		}
		?></div><?

	} elseif (!$contest['EXTERNAL']) {
		if (have_user()) {
			?><div class="notice block"><?= __('contest:info:you_cannot_participate_anymore_LINE') ?></div><?
		}
	}
	if (!$contest['EXTERNAL']) {
		if (false === ($usercnt = memcached_single_assoc('contestresponse','
			SELECT	COUNT(*) AS TOTAL_CNT,
					COUNT(IF(TOLD = 1, 1, NULL)) AS TOLD_CNT
			FROM contestresponse
			WHERE CONTESTID = '.$_REQUEST['sID']))
		) {
			return;
		}
		if ($usercnt['TOTAL_CNT']) {
			if ($_REQUEST['ACTION'] === 'contestants'
			||	$usercnt['TOTAL_CNT'] <= 200
			) {
				require_once '_userlist.inc';
				$userlist = new _userlist;
				$userlist->show_camera = true;
				$userlist->show_heart = true;
				$userlist->hilite_online = false;
				$userlist->contesting_in($_REQUEST['sID']);
				if (!$userlist->query()) {
					return;
				}
				if ($contest['CLOSED']
				&&	$usercnt['TOLD_CNT']
				&&	(	have_admin('contest')
					||	$contest['VISIBLE_GROUPS']
					)
				) {
					?><div class="block"><?= __('contest:info:winners_are_green_LINE') ?></div><?
				} else {
					$userlist->hide_winners = true;
				}
				layout_open_box('contest');
				layout_box_header($usercnt['TOTAL_CNT'].' '.element_name('contestant', $usercnt['TOTAL_CNT']));
				$userlist->display();
				layout_close_box();
			} else {

				layout_open_box('contest');
				layout_box_header(
					'<a href="/contest/'.$_REQUEST['sID'].'/contestants">'.
					$usercnt['TOTAL_CNT'].' '.element_name('contestant', $usercnt['TOTAL_CNT']).
					'</a>'
				);
				layout_close_box();
			}
		}
	}
	require_once '_commentlist.inc';
	$cmts = new _commentlist();
	$cmts->item($contest);
	$cmts->display();
	?></article><?
}

function contest_display_closed() {
	require_once '_contestlist.inc';
	layout_show_section_header(Eelement_plural_name('closed_contest'));

	main_menu();

	$contestlist = new _contestlist();
	$contestlist->only_closed();
	$contestlist->order_by_close_stamp();
	$contestlist->reverse_page = true;
	if (!$contestlist->query()) {
		return;
	}
	if ($contestlist->have_contests()) {
		$contestlist->display();
	} else {
		?><p><?= __('contest:info:no_closed_contests_LINE'); ?></p><?
	}
}
function contest_display_search() {
	layout_show_section_header();

	main_menu();

	?><form onsubmit="return submitForm(this)" method="get" action="/contest/searchresult"><?
	layout_open_box('white');
	layout_open_table(TABLE_CLEAN);

	layout_start_row();
	echo Eelement_name('name');
	layout_field_value();
	?><input id="sname" type="search" autosave="contestname" name="NAME" autofocus="autofocus" /><?

	layout_restart_row();
	echo Eelement_name('type');
	layout_field_value();
	?><label class="bold-hilited"><input checked type="radio" class="upLite" name="PARTY" value="1" /> <?= element_name('party') ?></label><br /><?
	?><label class="not-bold-hilited"><input type="radio" class="upLite" name="PARTY" value="0" /> <?= __('contesttypes:other') ?></label><?

	layout_restart_row(0,null,null,'not-hilited');
	?><label for="closed"><?= __C('action:closed') ?></label><?
	layout_field_value();
	?><input id="closed" type="checkbox" class="upLite" name="CLOSED" value="1" /><?

	layout_stop_row();
	layout_close_table();
	layout_close_box();
	?><div class="block"><input type="submit" value="<?= __('action:search') ?>" /></div><?
	?></form><?
}
function contest_display_search_result() {
	if (!require_something_trim($_REQUEST,'NAME')
	||	false === require_number($_REQUEST,'PARTY')
	) {
		return;
	}
	require_once '_contestlist.inc';
	layout_show_section_header();

	$contestlist = new _contestlist;
	if ($_REQUEST['PARTY']) {
		$contestlist->with_party_name($_REQUEST['NAME']);
	} else {
		$contestlist->with_other_name($_REQUEST['NAME']);
	}
	if (isset($_REQUEST['CLOSED'])) {
		$contestlist->only_closed(false);
	}
	if (!$contestlist->query()) {
		return;
	}
	if ($contestlist->have_contests()) {
		$contestlist->display();
	} else {
		?><div class="block"><? __('search:info:nothing_found_LINE') ?></div><?
	}
}
function contest_display_unfinished(): void {
	if (!require_admin('contest')) {
		return;
	}
	require_once '_contestlist.inc';
	layout_show_section_header(__('contest:pageheader:unfinished_HEADER'));

	main_menu();

	require_once '_contestlist.inc';
	$contestlist = new _contestlist;
	$contestlist->only_untold();
	$contestlist->order_by_close_stamp();
	$contestlist->show_organization = true;
	$contestlist->reverse_page = true;
	if (!$contestlist->query()) {
		return;
	}
	if ($contestlist->have_contests()) {
		$contestlist->display();
	} else {
		?><p><?= __('contest:no_unfinished_contests_LINE'); ?></p><?
	}
}
function contest_display_need_closure(): void {
	if (!require_admin('contest')) {
		return;
	}
	require_once '_contestlist.inc';
	layout_show_section_header();

	main_menu();

	require_once '_contestlist.inc';
	$contestlist = new _contestlist();
	$contestlist->important_all();
	$contestlist->order_by_close_stamp();
	$contestlist->no_pages = true;
	if ($contestlist->query()) {
		$contestlist->display(__C('action:close'));
	}
}

function contest_display_overview(bool $new = false): void {
	require_once '_contestlist.inc';
	layout_show_section_header();

	main_menu();

	$location_type = have_location_type($_REQUEST, 'ACTION');

	$new_since = 0;

	if (!$new) {
		if (!$location_type
		&&	have_admin('contest')
		) {
			layout_open_menu();
			layout_menuitem(__C('action:add'),'/contest/form');
			layout_close_menu();
			$contestlist = new _contestlist;
			$contestlist->show_to_be_published();
			$contestlist->show_participants = false;
			$contestlist->show_date = false;
			$contestlist->only_open();
			$contestlist->no_pages = true;
			$contestlist->all_contests();
			if ($contestlist->query()
			&&	$contestlist->have_contests()
			) {
				$contestlist->display();
			}
		}
	} else {
		if (have_user()) {
			require_once '_notify.inc';
			$new_since = memcached_single('notifyinfo','
				SELECT MANUAL_STAMP
				FROM notifyinfo
				WHERE USERID = '.CURRENTUSERID.'
				  AND TYPE='.NOTIFY_CONTEST,
				  HALF_HOUR
			);
			if (!$new_since) {
				if ($new_since === false) {
					return;
				}
				see_other('/contest');
			}
		}
		ob_start();
	}

	$max_pstamp = 0;
	$total = 0;
	if (false === ($profile = current_profile())) {
		return;
	}
	assert(is_array($profile)); # Satisfy EA inspection
	if (!$new_since
	&&	!$location_type
	&&	!isset($_REQUEST['PAGE'])
	) {
		$contestlist = new _contestlist();
		if (empty($profile['erotic'])) {
			$contestlist->hide_erotic();
		}
		$contestlist->show_city = false;
		$contestlist->show_date = false;
		$contestlist->show_header = true;
		$contestlist->only_open();
		$contestlist->other_contests();
		$contestlist->no_pages = true;
		$contestlist->order_chronologically();
		$contestlist->show_date = false;
		if ($contestlist->query()
		&&	$contestlist->have_contests()
		) {
			SMALL_SCREEN ? $contestlist->display() : $contestlist->display_flyers();

			$max_pstamp = $contestlist->max_pstamp;

			$total += $contestlist->have_contests();
		}
	}
	/** @noinspection SuspiciousAssignmentsInspection */
	$contestlist = new _contestlist();

	if (empty($profile['erotic'])) {
		$contestlist->hide_erotic();
	}

	if ($new_since) {
		$contestlist->newest_first();
		$contestlist->show_cstamp = true;
	} else {
		$contestlist->order_chronologically();
		$contestlist->show_date_headers = true;
		$contestlist->show_date = false;
		$contestlist->party_contests();
		$contestlist->only_for_current_country();
	}
	$contestlist->only_open();
	if ($location_type) {
		$contestlist->only_location_type(get_types_for_locality($location_type));
	}
	if ($contestlist->query()
	&&	$contestlist->have_contests()
	) {
		$title = get_title_for_location_type($location_type);

		$contestlist->display_controls();
		$contestlist->display_flyers($title);
		$contestlist->display(true);

		$max_pstamp = max($max_pstamp, $contestlist->max_pstamp);

		$total += $contestlist->have_contests();
	}
	if ($max_pstamp
	&&	have_user()
	) {
		require_once '_notify.inc';
		notify_register_seen(NOTIFY_CONTEST, $max_pstamp);
	}
	if ($new) {
		$data = ob_get_clean();

		if (have_user()) {
			layout_open_box('white');
			?><div class="block"><?= __('contest:info:new_contests_since_last_visit_TEXT',DO_NL2BR,[
				'CNT'	=> $total,
				'DATE'	=> _datedaytime_get($new_since),
			]);
			?></div><?
			layout_close_box();
		}

		echo $data;
	}
}
function contest_display_list() {
	require_once '_contestlist.inc';
	layout_show_section_header();

	main_menu();

	if (have_admin('contest')) {
		layout_open_menu();
		layout_menuitem(__C('action:add'),'/contest/form');
		layout_close_menu();
	}

	$location_type = have_location_type($_REQUEST, 'ACTION');

	$contestlist = new _contestlist();
	$contestlist->show_city = false;
	$contestlist->show_date = false;
	$contestlist->show_header = true;
	$contestlist->include_all = true;
	$contestlist->only_open();
	$contestlist->other_contests();
	$contestlist->no_pages = true;
	$contestlist->order_chronologically();
	$contestlist->only_for_current_country();

	$max_pstamp = 0;

	if ($contestlist->query()
	&&	$contestlist->have_contests()
	) {
		$contestlist->display();

		$max_pstamp = $contestlist->max_pstamp;
	}

	$contestlist = new _contestlist();
	$contestlist->include_all = true;
	$contestlist->only_open();
	$contestlist->party_contests();
	if ($location_type) {
		$contestlist->only_location_type(get_types_for_locality($location_type));
	}
	$contestlist->order_chronologically();
	$contestlist->only_for_current_country();
	$contestlist->show_date_headers = true;
	   	$contestlist->show_date = false;
	if ($contestlist->query()
	&&	$contestlist->have_contests()
	) {
		$title = get_title_for_location_type($location_type);

		$contestlist->display($title ?: true);

		$max_pstamp = max($max_pstamp, $contestlist->max_pstamp);
	}
	if ($max_pstamp
	&&	have_user()
	) {
		require_once '_notify.inc';
		notify_register_seen(NOTIFY_CONTEST, $max_pstamp);
	}
}
function contest_display_organization() {
	if (!require_idnumber($_REQUEST,'subID')) {
		return;
	}
	require_once '_contestlist.inc';
	layout_show_section_header();

	main_menu();

	$orgid = $_REQUEST['subID'];

	$contestlist = new _contestlist;
	if ($_REQUEST['SUBACTION'] != 'all') {
		$contestlist->only_open();
	}
	$contestlist->of_organization($orgid);
	$contestlist->no_pages = true;
	$contestlist->order_reverse_chronologically();
#	$contestlist->show_date_headers = true;
	$contestlist->show_date = true;
	$contestlist->show_type = true;

	if ($contestlist->query()
	&&	$contestlist->have_contests()
	) {
		$contestlist->display(get_element_link('organization',$orgid));
	}
}

function contest_commit(): bool {
	require_once '_ticket.inc';
	require_once '_ubb_preprocess.inc';
	require_once '_scancodes.inc';

	if (!require_admin('contest')
	||	!require_anything_trim($_POST, 'BODY', utf8: true)
	||	!require_anything_trim($_POST, 'NAME', utf8: true)
	||	!require_anything_trim($_POST, 'WINMSG', utf8: true)
	||	!require_anything_trim($_POST, 'ADMINTEXT', utf8: true)
	||	!require_element($_POST, 'TYPE', ['cd', 'discount', 'dvd', 'eticket', 'freeticket', 'guestlist', 'other', 'vinyl', 'wear', 'tokens', 'scancode'],strict:true)
	||	!require_element($_POST, 'CLOSE_TYPE', ['stamp', 'days'],strict:true)
	||	!require_number_or_empty($_POST, 'PARTYID')
	||	!require_idnumber($_POST, 'AMOUNT')
	||	!require_idnumber($_POST, 'GROUPS')
	||	!require_idnumber($_POST, 'VISIBLE_GROUPS')
	||	false === require_number($_POST, 'STDWINMSG')
	||	false === require_number($_POST, 'GLCLOSEHOUR')
	||	false === require_number($_POST, 'GLCLOSEMINS')
	||	false === require_number($_POST, 'STDWINMSG')
	||	!optional_number($_POST, 'TICKETID')
	||	$_POST['TYPE'] === 'scancode'
	&&	false === ($scancodes = verify_scancodes())
	) {
		return false;
	}
	if (isset($_POST['TICKETID'])) {
		$setlist[] = 'TICKETID='.$_POST['TICKETID'];
	}

	require_once '_namefix.inc';

	$contestid = have_idnumber($_REQUEST, 'sID');

	$setlist[] = 'GLCLOSEHOUR	='.$_POST['GLCLOSEHOUR'];
	$setlist[] = 'GLCLOSEMINS	='.$_POST['GLCLOSEMINS'];
	$setlist[] = 'PARTYID		='.($partyid = $_POST['PARTYID'] ?: 0);
	$setlist[] = 'BODY		="'.addslashes($body = get_fixed_utf8_name(_ubb_preprocess($_POST['BODY'], utf8: true))).'"';
	$setlist[] = 'NAME		="'.addslashes(get_fixed_utf8_name($_POST['NAME'])).'"';
	$setlist[] = 'TYPE		="'.addslashes($_POST['TYPE']).'"';
	$setlist[] = 'WINMSG		="'.addslashes($_POST['WINMSG']).'"';
	$setlist[] = 'STDWINMSG		=b\''.decbin($_POST['STDWINMSG']).'\'';
	$setlist[] = 'AMOUNT		='.$_POST['AMOUNT'];
	$setlist[] = 'GROUPS		='.$_POST['GROUPS'];
	$setlist[] = 'VISIBLE_GROUPS	='.$_POST['VISIBLE_GROUPS'];
	$setlist[] = 'ACTIVE		=b'.(isset($_POST['ACTIVE']) ? "'1'" : "'0'");
	$setlist[] = 'EXTERNAL		=b'.(isset($_POST['EXTERNAL']) ? "'1'" : "'0'");
	$setlist[] = 'ADMINTEXT		="'.addslashes(_ubb_preprocess($_POST['ADMINTEXT'], utf8: true)).'"';
	$setlist[] = 'ADDGOING		='.(isset($_POST['ADDGOING']) ? "b'1'" : "b'0'");
	$setlist[] = 'ENTRANCE_INCLUDED	='.(isset($_POST['ENTRANCE_INCLUDED']) ? "b'1'" : "b'0'");
	$setlist[] = 'SCANCODES_TYPE	="'.$_POST['SCANCODES_TYPE'].'"';
	$setlist[] = 'SCANCODES_RELEASE	='.($_POST['SCANCODES_TYPE']  ? min($_POST['SCANCODES_RELEASE'], MAX_SCANCODES_RELEASE_HOUR) : 0);

	require_once '_viavia.inc';
	add_viavia_to_setlist($setlist);

	if (isset($_POST['CONTACT'])) {
		$setlist[] = 'CONTACT="'.addslashes(utf8_mytrim(utf8_strip_shy($_POST['CONTACT']))).'"';
	}
	if (have_admin('promo')) {
		$setlist[] = 'ONLYPROMO=b'.(isset($_POST['ONLYPROMO']) ? "'1'" : "'0'");
	}
	switch ($_POST['CLOSE_TYPE']) {
	case 'stamp':
		if (!require_date($_POST,'CLOSE_')) {
			return false;
		}
		$close_stamp = _date_getstamp($_POST,'CLOSE_');
		break;

	case 'days':
		if (!require_idnumber($_POST,'CLOSEDAYS')) {
			return false;
		}
		if ($partyid) {
			$party_stamp = db_single('party', 'SELECT STAMP FROM party WHERE PARTYID = '.$partyid);
			if ($party_stamp === false) {
				return false;
			}
			if ($party_stamp === null) {
				register_error('party:error:nonexistent_LINE', ['ID' => $partyid]);
				return false;
			}
			$close_stamp = strtotime($q = '-'.$_POST['CLOSEDAYS'].' weekdays 8:00', $party_stamp);
		} else {
			$close_stamp = TODAYSTAMP + 5 * ONE_DAY;
			register_warning('contest:warning:no_close_date_TEXT', DO_NL2BR, ['DAYS' => 5]);
		}
		break;
	default:
		$close_stamp = 0;
		break;
	}

	$setlist[] = 'CLOSE_STAMP = '.$close_stamp;
	if (!empty($_POST['DIRECT'])) {
		$setlist[] = 'PSTAMP = '.($contestid ? 'CSTAMP' : CURRENTSTAMP);
		$pstamp = $contestid ? db_single('contest', 'SELECT PSTAMP FROM contest WHERE CONTESTID = '.$contestid) : CURRENTSTAMP;

	} elseif (!require_date_and_time($_POST)) {
		return false;

	} else {
		$setlist[] = 'PSTAMP = '.($pstamp = _date_getstamp($_POST));
	}
	if ($contestid) {
		if (!db_insert('contest_log','
			INSERT INTO contest_log
			SELECT * FROM contest
			WHERE NOT '.binary_equal($setlist).'
			  AND CONTESTID = '.$contestid)
		) {
			return false;
		}
		if (!db_affected()) {
			register_notice('item:notice:no_change_needed_LINE');
		} else {
			if (!db_update('contest', '
				UPDATE contest SET
					MSTAMP	= '.CURRENTSTAMP.',
					MUSERID	= '.CURRENTUSERID.',
					'.implode(', ', $setlist).'
				WHERE CONTESTID = '.$contestid)
			) {
				return false;
			}
			$status = 'changed';
		}
	} else {
		if (!require_idnumber($_POST, 'FORMSTAMP')) {
			return false;
		}
		if (false === ($exists = db_single('contest','
			SELECT CONTESTID
			FROM contest
			WHERE PARTYID = '.($_POST['PARTYID'] ?: 0).'
			  AND CLOSED = 0
			  AND FORMSTAMP = '.$_POST['FORMSTAMP']))
		) {
			return false;
		}
		if ($exists) {
			register_warning('contest:warning:already_exists_LINE');
			if (false !== ($msgid = store_messages_in_cookie())) {
				see_other(get_element_href('contest', $exists));
			}
			$_REQUEST['sID'] = $exists;
			return true;
		}
		if (!db_insert('contest','
			INSERT INTO contest SET
				FORMSTAMP	='.$_POST['FORMSTAMP'].',
				USERID		='.CURRENTUSERID.',
				CSTAMP		='.CURRENTSTAMP.',
				'.implode(',',$setlist))
		) {
			return false;
		}
		$_REQUEST['sID'] = $contestid = db_insert_id();
		ticket_update('contest',$contestid);
		$status = 'added';
	}

	if (isset($scancodes)) {
		commit_scancodes($contestid, $scancodes, $affected);
	}

	if (!empty($status)) {
		switch ($status) {
		case 'changed':
		case 'added':
			register_notice($_REQUEST['sELEMENT'].':notice:'.$status.'_LINE');
			break;
		}
	}

	if ($_POST['GROUPS']) {
		# generate codes, even for non-guestlist contests, so we can easily change the type later
		$have = db_single('contestcode', 'SELECT COUNT(*) FROM contestcode WHERE CONTESTID = '.$contestid, DB_USE_MASTER);
		if ($have !== false) {
			$diff = $_POST['GROUPS'] - $have;
			if ($diff) {
				if ($diff < 0) {
					if (!db_delete('contestcode','
						DELETE FROM contestcode
						WHERE CONTESTID = '.$contestid.'
						  AND USERID = 0
						LIMIT '.(-$diff))
					) {
						return false;
					}
					if (db_affected() != -$diff) {
						register_error('contest:error:contestcodes_have_already_been_issued_and_cannot_be_recalled_LINE');
					}
				} else {
					for ($i = 0; $i < $diff; ++$i) {
						do {
							$code = create_password(8,'ABCDEFGHJKLMNPQRSTUVWXYZ3456789',false);
						}
						while (isset($inslist[$code]));

						$inslist[$code] = '('.$contestid.',0,"'.addslashes($code).'")';
					}
					if (!db_insert('contestcode','
						INSERT INTO contestcode (CONTESTID,USERID,CODE)
						VALUES '.implode(',',$inslist))
					) {
						return false;
					}
				}
			}
		}
	} elseif (!db_delete('contestcode', "
		DELETE FROM contestcode
		WHERE CONTESTID = $contestid
		  AND USERID = 0")
	||	false === ($rc = db_single('contestcode', "
		SELECT COUNT(*)
		FROM contestcode
		WHERE USERID
		  AND CONTESTID = $contestid", DB_USE_MASTER)
	)) {
		return false;
	} elseif ($rc) {
		register_error('contest:error:contestcodes_have_already_been_issued_and_cannot_be_recalled_LINE');
		return false;
	}
	taint_include_cache('contest', $contestid,$body);
	require_once '_sphinx.inc';
	sphinx_update_element('contest', $contestid);
	if (isset($_POST['ACTIVE'])
	&&	$pstamp
	) {
		require_once '_notify.inc';
		notify_register_have(NOTIFY_CONTEST, $pstamp);
	}
	page_changed('contest', $contestid);
	return true;
}

function show_contest_uploadimage(int $contestid, int $partyid, int $flags = 0): void {
	require_once '_uploadimage.inc';
	ob_start();
	uploadimage_show_with_fallback('contest', $contestid, flags: UPIMG_SHOW_HISTORY);
	$img = ob_get_clean();

	ob_start();
	require_once '_connect.inc';
	_connect_display('contest',$contestid);
	$connect = ob_get_clean();

	if ($img || $connect) {
		?><div class="right-float"><?
		echo $img,$connect;
		?></div><?
	}
}

function eligble_parties(array $elements): array {
	$stamp = TODAYSTAMP + ONE_DAY * (CLOSE_CONTEST_DAYS_CONNECTED + 1);
	foreach ($elements as $element => $ids) {
		switch ($element) {
		case 'organization':
			$parts[] = '
			SELECT DISTINCT PARTYID
			FROM party
			JOIN connect
			  ON MAINTYPE = "organization"
			  AND MAINID IN ('.implode(', ', $ids).')
			  AND ASSOCTYPE = "party"
			  AND ASSOCID = PARTYID
			WHERE STAMP > '.$stamp;
			break;

		case 'location':
			$parts[] = '
			SELECT DISTINCT PARTYID
			FROM party
			WHERE LOCATIONID IN ('.implode(', ', $ids).')
			  AND STAMP > '.$stamp;
			break;
		}
	}
	$partyids = db_simpler_array(['party', 'connect'],
		implode(' UNION ', $parts)
	);
	return $partyids;
}

function fix_contest_code(): bool {
	if (!($contestid = require_idnumber($_REQUEST, 'CONTESTID'))) {
		return false;
	}
	if (!($contest = db_single_assoc('contest', "
		SELECT *
		FROM contest
		WHERE CONTESTID = $contestid",
		DB_FORCE_MASTER
	))) {
		if ($contest === false) {
			not_found();
		}
		return false;
	}
	if (false === ($have = db_single('contestcode', "
		SELECT COUNT(*)
		FROM `contestcode`
		WHERE CONTESTID = $contestid",
		DB_USE_MASTER
	))) {
		return false;
	}
	if ($have !== false) {
		if ($diff = $contest['GROUPS'] - $have) {
			if ($diff < 0) {
				if (!db_delete('contestcode', "
					DELETE FROM contestcode
					WHERE CONTESTID = $contestid
					  AND USERID = 0
					LIMIT -$diff"
				)) {
					return false;
				}
				if (db_affected() !== -$diff) {
					register_error('contest:error:contestcodes_have_already_been_issued_and_cannot_be_recalled_LINE');
				}
			} else {
				for ($i = 0; $i < $diff; ++$i) {
					do {
						$code = create_password(8, 'ABCDEFGHJKLMNPQRSTUVWXYZ3456789', false);
					}
					while (isset($inslist[$code]));

					$inslist[$code] = '('.$contestid.', 0, "'.addslashes($code).'")';
				}
				if (!db_insert('contestcode','
					INSERT INTO contestcode (CONTESTID, USERID, CODE)
					VALUES '.implode(', ', $inslist))
				) {
					return false;
				}
			}
		}
	}
	return true;
}

function get_types_for_locality(string $location_type): string|array {
	return	$location_type === 'beach'
		?	['beach', 'outdoor']
		:	$location_type;
}

function get_title_for_location_type(string $location_type): string {
	if (!$location_type) {
		return '';
	}
	if ($location_type === 'beach') {
		return Eelement_plural_name('beach_party');
	}
	return __C('title:agenda:'.$location_type);
}
