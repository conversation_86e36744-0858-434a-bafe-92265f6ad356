<?php

use Jet<PERSON><PERSON>s\PhpStorm\ExpectedValues;

const NEED_UPDATE_ELEMENTS = [
	'artist',
	'location',
	'organization',
	'party',
];
# Required views or connections for 'need_update' items to be included in show_needupdates
# Less views and less connects? Ignore
const SHOW_NEED_UPDATE_AT_LEAST_VIEWS	 = 400;
const SHOW_NEED_UPDATE_AT_LEAST_CONNECTS =  10;
const NEED_UPDATE_VERSION				 = 'v5';
const NEED_UPDATE_PREFIX				 = NEED_UPDATE_VERSION.':need_updates:';
const NEED_UPDATE_PREFIX_CONNECTED		 = NEED_UPDATE_VERSION.':need_updates,connected:';
const NEED_UPDATE_PREFIX_INFO			 = NEED_UPDATE_VERSION.':need_updates,info:';
const NEED_UPDATE_PREFIX_COUNTS			 = NEED_UPDATE_VERSION.':need_updates,counts:';
const NEED_UPDATE_PREFIX_COUNTS_ASPIRING = NEED_UPDATE_VERSION.':need_updates,counts_aspiring:';
# To force need-updates to not use the cache, add NOMEMCACHE=need-updates to the query string of th request uri.
const DONT_CACHE_NEED_UPDATES			 = false;

function get_needupdate_count(string $element): array|false {
	if (is_aspiring_content_admin()) {
		$key = NEED_UPDATE_PREFIX_COUNTS_ASPIRING.$element;
	} else {
		$key = NEED_UPDATE_PREFIX_COUNTS.$element;
	}
	if (!DONT_CACHE_NEED_UPDATES
	&&	(	!isset($_REQUEST['NOMEMCACHE'])
		||	$_REQUEST['NOMEMCACHE'] !== 'need-updates'
		)
	&&	($counts = memcached_get($key))
	) {
		return $counts;
	}
	if (false === ($need_updates = get_needupdates($element))) {
		return false;
	}
	[$show_need_updates, $all_need_updates, $aspiring_need_updates] = $need_updates;
	$counts = [
			$show_need_updates	? count($show_need_updates)		: 0,
			 $all_need_updates	? count($all_need_updates)		: 0,
		$aspiring_need_updates	? count($aspiring_need_updates)	: 0,
	];
	memcached_set($key, $counts, FIVE_MINUTES);
	return $counts;
}

function get_needupdate_agenda_counters(?array $locked_ids = null): array|false {
	if ($locked_ids === null
	&&	false === ($locked_ids = locked_ids(LOCK_PARTY))
	) {
		return false;
	}
	return	memcached_single_assoc(['party', 'contact_ticket'], /** @lang MariaDB */ '
			SELECT	COUNT(DISTINCT PARTYID) AS all_count,
					COUNT(DISTINCT IF(USERID = 0 '/* was: CURRENTUSERID*/.',		  		  PARTYID, NULL)) AS mine_count,
					COUNT(DISTINCT IF(USERID	 IN ('.get_aspiring_content_admins_list().'), PARTYID, NULL)) AS aspiring_count,
					COUNT(DISTINCT IF(USERID NOT IN ('.get_aspiring_content_admins_list().'), PARTYID, NULL)) AS show_count
			FROM (
				SELECT DISTINCT PARTYID, party.USERID
				FROM party
				LEFT JOIN contact_ticket ON ELEMENT = "party" AND ID = PARTYID
				WHERE (ACCEPTED = 0 AND NOT EVENT_PSTAMP)
				  AND (contact_ticket.TICKETID IS NULL OR contact_ticket.STATUS = "open") '.
				($locked_ids ? /** @lang MariaDB */ ' AND PARTYID NOT IN ('.implode(',', $locked_ids).')' : '').'
			) AS parties',
			HALF_HOUR,
			NEED_UPDATE_PREFIX_COUNTS.'party',
		);
}

function show_needupdate_counters(string $element): void {
	if ($element === 'party') {
		if (!($unaccepteds = get_needupdate_agenda_counters())) {
			return;
		}
		/** @noinspection PhpRedundantOptionalArgumentInspection */
		extract($unaccepteds, \EXTR_OVERWRITE);
		if (have_admin('party_accept')) {
			echo $show_count ? '<b>' : '<small class="light">';
			?> <?= MIDDLE_DOT_ENTITY ?> <?
			?><a href="/agenda/needupdate"><?= $show_count ?: '&#9733;' ?></a><?
			echo $show_count ? '</b>' : '</small>';

		} elseif (is_aspiring_content_admin()) {
			?><small> <?= MIDDLE_DOT_ENTITY ?> <?
			?><b><a href="/agenda/needupdate/aspiring"><?= $aspiring_count ?: '&#9733;' ?></a></b></small><?
		}
		return;
	}

	[$show_count, /* $all_count */, $aspiring_count] = get_needupdate_count($element);

	if ($aspiring_count
	&&	is_aspiring_content_admin()
	) {
		?> <?= MIDDLE_DOT_ENTITY ?> <b><a href="/<?= $element ?>/needupdate/aspiring"><?= $aspiring_count ?></a></b><?
	} elseif ($show_count) {
		?> <?= MIDDLE_DOT_ENTITY ?> <b><a href="/<?= $element ?>/needupdate"><?= $show_count ?></a></b><?
	} else {
		?> <small class="light">&middot; <a href="/<?= $element ?>/needupdate">&#9733;</a></small><?
	}
}

function flush_needupdates(?string $element = null): void {
	# Call this function only when an already existing item changes its NEEDUPDATE.
	# It's not needed for new items, as those start without views and are not counted anyway
	$element ??= $_REQUEST['sELEMENT'];
	error_log("flushing needupdate for {$element}s");
	memcached_delete(
		NEED_UPDATE_VERSION.':needupdates:'.$element,
		NEED_UPDATE_VERSION.':needupdates:info:'.$element,
		NEED_UPDATE_VERSION.':need_update:counts:'.$element,
		NEED_UPDATE_VERSION.':need_update:counts:'.$element.':aspiring',
	);
}

function needupdate_flush_upon_removal(string $element, int $id): bool {
	if (!in_array($element, NEED_UPDATE_ELEMENTS)) {
		return false;
	}
	if (!($was_in_need = db_single($element, "SELECT NEEDUPDATE FROM $element WHERE {$element}ID = $id"))
	&&	db_failed()
	) {
		return true;
	}
	return (bool)$was_in_need;
}

function get_needupdates(
	#[ExpectedValues(['artist', 'location', 'organization'])]
	string $element
): array|false {
	$cache_key = NEED_UPDATE_PREFIX_INFO.$element;
	if (!DONT_CACHE_NEED_UPDATES
	&&	(	!isset($_REQUEST['NOMEMCACHE'])
		||	$_REQUEST['NOMEMCACHE'] !== 'need_updates'
		)
	&&	($need_updates_info = memcached_get($cache_key))
	) {
		return $need_updates_info;
	}
	switch ($element) {
	case 'artist':
		$base_query = '
			SELECT	ARTISTID AS ID, NAME, CSTAMP, USERID, NEEDUPDATE, ACCEPTED
			FROM party_db.artist';

		if (false === ($need_updates = memcached_rowuse_hash(['artist', 'lineup'], /** @lang MariaDB */ "
			(		$base_query WHERE ACCEPTED = 0
			UNION	$base_query WHERE NEEDUPDATE = 1
			)",
			ONE_DAY,
			NEED_UPDATE_PREFIX.'artist'))
		) {
			return false;
		}
		$idstr = implodekeys(',', $need_updates);
		if (false === ($connected = memcached_simple_hash('lineup', "
			SELECT ARTISTID, COUNT(DISTINCT PARTYID)
			FROM lineup
			WHERE ARTISTID IN ($idstr)",
			ONE_DAY,
			NEED_UPDATE_PREFIX_CONNECTED.'artist'))
		) {
			return false;
		}
		break;

	case 'organization':
		if (false === ($need_updates = memcached_rowuse_hash(['organization', 'connect', 'partyarea'], '
			SELECT ORGANIZATIONID AS ID, NAME, organization.CSTAMP, USERID, NEEDUPDATE, ACCEPTED
			FROM organization
			WHERE ACCEPTED = 0
			   OR NEEDUPDATE',
			ONE_DAY,
			NEED_UPDATE_PREFIX.'organization'))
		) {
			return false;
		}
		$idstr = implodekeys(',', $need_updates);
		if (false === ($connected = memcached_simple_hash(['connect','partyarea'], "
			SELECT ID, COUNT(DISTINCT OTHER_ID)
			FROM (
				SELECT DISTINCT MAINID AS ID, ASSOCID AS OTHER_ID
				FROM connect
				WHERE MAINTYPE IN ('organization', 'orgashost')
				  AND MAINID IN ($idstr)
				UNION DISTINCT
				SELECT DISTINCT HOSTEDBYID, PARTYID AS OTHER_ID
				FROM partyarea
				WHERE HOSTEDBYID IN ($idstr)
			) AS distinct_organization_combination",
			ONE_DAY,
			NEED_UPDATE_PREFIX_CONNECTED.'organization'))
		) {
			return false;
		}
		break;

	case 'location':
		if (false === ($need_updates = memcached_rowuse_hash(['location', 'party'], '
			SELECT LOCATIONID AS ID, TITLE, NAME, CSTAMP, USERID, NEEDUPDATE, ACCEPTED
			FROM location
			WHERE ACCEPTED = 0
			   OR NEEDUPDATE
			GROUP BY LOCATIONID',
			ONE_DAY,
			NEED_UPDATE_PREFIX.'location'))
		) {
			return false;
		}
		$idstr = implodekeys(',', $need_updates);
		if (false === ($connected = memcached_simple_hash('party', "
			SELECT LOCATIONID, COUNT(*)
			FROM party
			WHERE LOCATIONID IN ($idstr)
			GROUP BY LOCATIONID",
			ONE_DAY,
			NEED_UPDATE_PREFIX_CONNECTED.'location'))
		) {
			return false;
		}
		break;

	default:
		return false;
	}
	if (!$need_updates) {
		$need_updates_info = [$need_updates, [], $need_updates, [], [], []];
		memcached_set($cache_key, $need_updates_info, TEN_MINUTES);
		return $need_updates_info;
	}
	if (false === ($tickets = db_multirowuse_hash('contact_ticket', "
		SELECT DISTINCT ID, STATUS, LSTAMP, TICKETID, TRACKIT, OUTGOING
		FROM contact_ticket
		WHERE ELEMENT = '$element'
		  AND ID IN ($idstr)
		ORDER BY STATUS = 'open', LSTAMP DESC"))
	) {
		return false;
	}
	assert(is_array($tickets)); # Satisfy EA inspection
	$gets = [];
	foreach ($need_updates as $id => /* $info = */ $ignored) {
		$gets[$id] = "$element:$id|needupdates:sum(views):".NEED_UPDATE_VERSION;
	}
	if (false === ($cached_views = memcached_get($gets))) {
		return false;
	}
	foreach ($gets as $id => $get_key) {
		if (isset($cached_views[$get_key])) {
			$need_updates[$id]['VIEWS'] = $cached_views[$get_key];
			continue;
		}
		if (false === ($need_updates[$id]['VIEWS'] = memcached_single_int("{$element}_counter", "
			SELECT CAST(SUM(VIEWS) AS UNSIGNED)
			FROM {$element}_counter
			WHERE {$element}ID = $id
			GROUP BY {$element}ID",
			ONE_WEEK,
			key: $get_key,
			flags: DB_DONT_FRESHEN))
		) {
			return false;
		}
	}
	assert(is_array($connected)); # Satisfy EA inspection
	$userids = [];
	foreach ($need_updates as &$info) {
		$userid = $info['USERID'];
		if (!isset($userids[$userid])) {
			$userids[$userid] = $userid;
		}
		$info['CONNECTED'] = $connected[$info['ID']] ?? 0;
	}
	unset($info);
	$all_needupdates = [];
	$show_needupdates = [];
	$aspiring_needupdates = [];
	$current_aspiring = is_aspiring_content_admin();

/*	uksort($need_updates, static fn(int $a_id, int $b_id): int =>
	isset($__aspiring[$need_updates[$a_id]['USERID']])
	<=> isset($__aspiring[$need_updates[$b_id]['USERID']])
	?:	$need_updates[$a_id]['ACCEPTED'] 	<=> $need_updates[$b_id]['ACCEPTED']
	?:  $need_updates[$a_id]['NEEDUPDATE']	<=> $need_updates[$b_id]['NEEDUPDATE']
	?:	$need_updates[$b_id]['CONNECTED']	<=> $need_updates[$a_id]['CONNECTED']
	?:	$need_updates[$b_id]['VIEWS']		<=> $need_updates[$a_id]['VIEWS']
	?:	$need_updates[$b_id]['ID']			<=> $need_updates[$a_id]['ID']
	);*/

	uasort($need_updates, static fn(array $a, array $b): int =>
			isset(ASPIRING_CONTENT_ADMINS[$a['USERID']])
		<=> isset(ASPIRING_CONTENT_ADMINS[$b['USERID']])
		?:	$a['ACCEPTED'] 	 <=> $b['ACCEPTED']
		?:  $a['NEEDUPDATE'] <=> $b['NEEDUPDATE']
		?:	$b['CONNECTED']	 <=> $a['CONNECTED']
		?:	$b['VIEWS']		 <=> $a['VIEWS']
		?:	$b['ID']		 <=> $a['ID']
	);

	foreach ($need_updates as $id => $needupdate) {
		$element_by_aspiring = is_aspiring_content_admin($needupdate['USERID']);
		if ($current_aspiring) {
			if ($element_by_aspiring) {
				$aspiring_needupdates[$id] = $needupdate;
			}
			continue;
		}
		if ($element_by_aspiring) {
			$aspiring_needupdates[$id] = $needupdate;
			continue;
			# always add non-artist need_updates to show (org, loc), because they are short and doable
		}
		$id = $needupdate['ID'];

		if (!empty($tickets[$id])
		||	 $needupdate['CONNECTED'] >= SHOW_NEED_UPDATE_AT_LEAST_CONNECTS
		||	 $needupdate['VIEWS']	  >= SHOW_NEED_UPDATE_AT_LEAST_VIEWS
		||	!$needupdate['ACCEPTED']
		) {
			$show_needupdates[$id] = $needupdate;
		}
		$all_needupdates[$id] = $needupdate;
	}
	$need_updates_info = [
		$show_needupdates,
		$all_needupdates,
		$aspiring_needupdates,
		$userids,
		$tickets,
	];
	memcached_set($cache_key, $need_updates_info, ONE_DAY);
	return $need_updates_info;
}

function show_menu(int $show_count, int $all_count, int $aspiring_count): void {
	layout_open_menu();
	if (have_admin('party_accept')) {
		$element	 = $_REQUEST['sELEMENT'];
		$sub_action  = $_REQUEST['SUBACTION'];
		layout_menuitem(
			__C('status:useful').($show_count ? " <small>= $show_count</small>" : ''),
			"/$element/needupdate",
			!$sub_action
		);
		if ($element !== 'agenda') {
			layout_menuitem(
				Eelement_name('all').($all_count ? " <small>= $all_count</small>" : ''),
				"/$element/needupdate/all",
				$sub_action === 'all'
			);
		}
		layout_menuitem(
			Eelement_name('aspirant').($all_count ? " <small>= $aspiring_count</small>" : ''),
			"/$element/needupdate/aspiring",
			$sub_action === 'aspiring'
		);
	}
	layout_close_menu();
}

function show_needupdates(): void {
	layout_show_section_header();

	$element = $_REQUEST['sELEMENT'];

	/** @noinspection NotOptimalIfConditionsInspection */
	if (!is_aspiring_content_admin(CURRENTUSERID)
	&&	!require_admin($element)
	||	false === ($need_updates = get_needupdates($element))
	) {
		return;
	}
	[	$show_needupdates,
		$all_needupdates,
		$aspiring_needupdates,
		$userids,
		$views,
		$tickets
	] = $need_updates;

	$aspiring = $_REQUEST['SUBACTION'] === 'aspiring';
	$all	  = ($aspiring ? ($_SERVER['eDATA'] ?? false) : $_REQUEST['SUBACTION']) === 'all';

	show_menu(
		count($show_needupdates),
		count($all_needupdates),
		count($aspiring_needupdates),
	);
	$also_needupdates = match (true) {
		$all		=> $all_needupdates,
		$aspiring	=> $aspiring_needupdates,
		default		=> $show_needupdates,
	};
	if ($all) {
		$needupdates = $all_needupdates;
	} elseif ($aspiring) {
		$needupdates = $aspiring_needupdates;
	} else {
		$needupdates = $show_needupdates;
	}
	if ($also_needupdates !== $needupdates) {
		error_log('ERROR neeedupdates !== also_needupdates');
		error_log_r($needupdates, 'DEBUG needupdates');
		error_log_r($also_needupdates, 'DEBUG also_needupdates');
	}
	if ($also_needupdates !== $needupdates) {
		error_log('ERROR neeedupdates !== also_needupdates');
		error_log_r($needupdates, 'DEBUG needupdates');
		error_log_r($also_needupdates, 'DEBUG also_needupdates');
	}
	if (!$needupdates) {
		?><div class="block"><?= __($element.':info:no_unaccepted_LINE') ?></div><?
		return;
	}
	require_once '_contactstars.inc';
	$contactstars = new contactstars($userids);
	$needupdate = null;
	$accepted	= null;

	foreach ($needupdates as $id => $item) {
		if ($needupdate !== $item['NEEDUPDATE']
		||	$accepted   !== $item['ACCEPTED']
		) {
			if ($needupdate !== null) {
				layout_close_table();
				layout_close_box();
			}
			$parts = [];
			if (!$item['ACCEPTED']) {
				$parts[] = Eelement_plural_name('unaccepted_'.$element);
			}
			if ($item['NEEDUPDATE']) {
				$parts[] = __C('info:need_update');
			}
			layout_open_box('white');
			layout_box_header(implode(' '.MIDDLE_DOT_ENTITY.' ', $parts));

			layout_open_table(TABLE_FULL_WIDTH | TABLE_SORTABLE);
			layout_start_header_row();
			layout_header_cell_right(SMALL_SCREEN ? '+' : __C('field:added'));
			layout_header_cell(Eelement_name('name'));
			if (!SMALL_SCREEN) {
				layout_header_cell(__C('field:by'));
				layout_header_cell_right('&#128279;');
			}
			layout_header_cell_right(SMALL_SCREEN ? '&#128065;' : Eelement_plural_name('view'));
			layout_header_cell(get_mail_icon(incoming: true));
			layout_stop_header_row();

			$needupdate = $item['NEEDUPDATE'];
			$accepted	= $item['ACCEPTED'];
		}

		set_cell_value($item['CSTAMP']);
		layout_start_rrow(CELL_ALIGN_RIGHT, 'nowrap'.($item['CONNECTED'] ? ' connected' : ''));
		_date_display_numeric($item['CSTAMP']);

		?></td><?
		?><td class="forcewrap"><?
		?><b><?= get_element_link($element, $item['ID']) ?></b><?

		if (!SMALL_SCREEN) {
			layout_next_cell(class: 'nowrap');
			if ($item['USERID']) {
				echo get_element_link('user', $item['USERID']);
				$contactstars->show_stars($item['USERID']);
			}

			set_cell_value($item['CONNECTED']);
			layout_next_cell(class: 'right connect-status');
			if ($item['CONNECTED']) {
				echo $item['CONNECTED'], MULTIPLICATION_SIGN_ENTITY ?> <?
			}
		}

		layout_next_cell(class: 'right');
		echo $views[$id] ?? '';

		layout_next_cell(class: 'right');
		if (isset($tickets[$item['ID']])) {
			$open_count = 0;
			$closed_count = 0;
			foreach ($tickets[$item['ID']] as $ticket) {
				if ($ticket['STATUS'] === 'open') {
					++$open_count;
				} else {
					++$closed_count;
				}
			}
			foreach ([
				['open', 	$open_count,	''],
				['closed',	$closed_count,	'light']
			] as [$status, $count, $class]) {
				if (!$count) {
					continue;
				}
				echo $count, MULTIPLICATION_SIGN_ENTITY ?> <?
				?><a<?
				if ($ticket['STATUS'] !== 'open') {
					?> class="light"<?
				}
				?> href="<?= get_element_href($element, $ticket['ID']) ?>"><?=
					get_mail_icon(alt: escape_utf8(get_element_title($element, $ticket['ID'])))
				?></a><?
			}
		}
		layout_stop_row();
	}
	if ($needupdate !== null) {
		layout_close_table();
		layout_close_box();
	}
}

function show_agenda_needupdates(): void {
	require_once '_countryflag.inc';
	require_once '_star.inc';

	layout_section_header(Eelement_plural_name('party').' '.MIDDLE_DOT_ENTITY.' '.__('attrib:unapproved'));

	if (false === ($locked_ids = locked_ids(LOCK_PARTY))
	||	!($unaccepteds = get_needupdate_agenda_counters($locked_ids))
	) {
		return;
	}

	/** @noinspection PhpRedundantOptionalArgumentInspection */
	extract($unaccepteds, \EXTR_OVERWRITE);

	show_menu(
		show_count:		$show_count,
		all_count:		$all_count,
		aspiring_count: $aspiring_count,
	);

	$aspiring = $_REQUEST['SUBACTION'] === 'aspiring';
	$ADE	  = $_REQUEST['SUBACTION'] === 'ADE';

	if (!($unaccepteds = memcached_rowuse_hash(['party', 'user_account', 'location', 'boarding', 'city', 'connect'], '
		SELECT	PARTYID, party.NAME, party.CSTAMP, LOCATION_ADDR,
				party.USERID, party.STAMP_TZI, NICK, AT2400,
				party.LOCATIONID, location.NAME AS LOCATION_NAME,
				GROUP_CONCAT(connect_org.ASSOCID) AS ORGIDS,
				city.CITYID, city.COUNTRYID,
				country.SHORTA3,
				IF(salescontactme.PARTYID IS NOT NULL, b\'1\', b\'0\') AS POTENTIAL_CUSTOMER
		FROM party
		LEFT JOIN user_account USING (USERID)
		LEFT JOIN location ON location.LOCATIONID = party.LOCATIONID
		LEFT JOIN boarding ON boarding.BOARDINGID = party.BOARDINGID
		LEFT JOIN city ON city.CITYID = COALESCE(boarding.CITYID, location.CITYID, party.CITYID)
		LEFT JOIN country ON country.COUNTRYID = city.COUNTRYID
		LEFT JOIN salescontactme USING (PARTYID)
		LEFT JOIN contact_ticket ON ELEMENT = "party" AND ID = PARTYID
		LEFT JOIN connect AS connect_org
			 ON connect_org.MAINTYPE = "party"
			AND connect_org.MAINID = PARTYID
			AND connect_org.ASSOCTYPE = "organization"
		LEFT JOIN connect AS connect_ADE
			 ON connect_ADE.MAINTYPE = "party"
			AND connect_ADE.MAINID = PARTYID
			AND connect_ADE.ASSOCTYPE = "organization"
			AND connect_ADE.ASSOCID = 3776
		WHERE NOT party.ACCEPTED
		  AND party.USERID '.($aspiring ? 'IN' : 'NOT IN').' ('.get_aspiring_content_admins_list().')'.
		  ($ADE ? ' AND connect_ADE.MAINTYPE IS NOT NULL ' : '').
		  /*(false  && $locked_ids ? ' AND PARTYID NOT IN ('.implode(', ', $locked_ids).')' : '')*/ '
		GROUP BY PARTYID
		ORDER BY party.USERID IN ('.get_aspiring_content_admins_list().') ASC,
			 ABS(UNIX_TIMESTAMP() - CAST(party.STAMP AS SIGNED)) ASC',
		TEN_MINUTES,
		NEED_UPDATE_VERSION.':needupdates:party'
	))) {
		if ($unaccepteds === false) {
			return;
		}
		goto no_unapproveds;
	}
	require_once '_invoice.inc';
	# make sure relation has many paid invoices relative to notpaid and irrecoverable
	require_once '_okrelations.inc';
	$ok_relations = get_ok_relations();
	$partyidstr = implodekeys(', ', $unaccepteds);

	if (false === ($tickets = db_multirowuse_hash('contact_ticket', "
		SELECT DISTINCT ID, STATUS, LSTAMP, TICKETID, CLOSETYPE, TRACKIT
		FROM contact_ticket
		WHERE ELEMENT = 'party'
		  AND ID IN ($partyidstr)
		  AND (	STATUS IN ('open', 'pending')
			 OR	STATUS = 'closed' AND TRACKIT
		  	)
		ORDER BY STATUS = 'open', LSTAMP DESC"))
	||	false === ($ok_users = $ok_relations
		?	db_same_hash('relationmember', '
				SELECT USERID
				FROM relationmember
				WHERE RELATIONID IN ('.implodekeys(', ', $ok_relations).')'
		) :	[])
	||	false === ($ok_organizations = $ok_users
		?	db_same_hash('organizationmember', '
				SELECT ID
				FROM organizationmember
				WHERE USERID IN ('.implode(', ', $ok_users).')'
		) :	[])
	) {
		return;
	}
	assert(
		is_array($ok_organizations)
	&&	is_array($ok_users)
	&&	is_array($tickets)); # Satisfy EA inspection

	$all_orgs = [];
	foreach ($orgs as /* $partyid => */ $organizationids) {
		foreach ($organizationids as $orgid) {
			$all_orgs[$orgid] = $orgid;
		}
	}
	sort($all_orgs);

	if (false === ($locs = db_simple_hash('party', "
		SELECT PARTYID,LOCATIONID
		FROM party
		WHERE PARTYID IN ($partyidstr)
		  AND LOCATIONID"))
	) {
		return;
	}
	$all_locs = [];
	foreach ($locs as /* $partyid => */ $local_locationid) {
		$all_locs[$local_locationid] = $local_locationid;
	}
	sort($all_locs);

	if (!$all_orgs) {
		$org_goings = [];
	} else {
		foreach ($all_orgs as $organizationid) {
			if (false === ($org_goings[$organizationid] = memcached_single_int(['going', 'party', 'connect'], "
				SELECT COUNT(*)
				FROM connect
				JOIN going ON PARTYID = ASSOCID
				WHERE MAINTYPE = 'organization'
				  AND MAINID = $organizationid
				  AND ASSOCTYPE = 'party'
				  AND going.CSTAMP > ".(TODAYSTAMP - ONE_YEAR),
				ONE_WEEK))
			) {
				return;
			}
		}
	}

	if (!$all_locs) {
		$loc_goings = [];
	} else {
		foreach ($all_locs as $locationid) {
			if (false === ($loc_goings[$locationid] = memcached_single_int(['going', 'party'], "
				SELECT COUNT(*)
				FROM party
				JOIN going USING (PARTYID)
				WHERE LOCATIONID = $locationid
				  AND going.CSTAMP > ".(TODAYSTAMP - ONE_YEAR),
				ONE_DAY
			))) {
				return;
			}
		}
	}

	arsort($loc_goings);
	arsort($org_goings);

	$show_parties = [];
	$ADE_parties = [];
	$aspiring_parties = [];
	$all_count = 0;

	foreach ($unaccepteds as $partyid => &$party) {
		++$all_count;
		$organizationids = $party['ORGIDS'] = $party['ORGIDS'] ? explode(',', $party['ORGIDS']) : [];
		$is_ADE = $ADE && $organizationids && in_array(3776, $organizationids, true);
		$aspiring = is_aspiring_content_admin($party['USERID']);
		$store = $is_ADE ? 'ADE_parties' : ($aspiring ? 'aspiring_parties' : 'show_parties');
		$count = $is_ADE ? 'ADE_count'   : ($aspiring ? 'aspiring_count'   : 'show_count');
		$userids[$userid = $party['USERID']] = $userid;
		$r = false;
		if (isset($ok_users[$userid])) {
			$r = true;
		} elseif ($party['ORGIDS']) {
			foreach ($party['ORGIDS'] as $orgid) {
				if (isset($ok_organizations[$orgid])) {
					$r = true;
					break;
				}
			}
		}
		$pending = false;
		if (isset($tickets[$partyid])) {
			foreach ($tickets[$partyid] as $ticket) {
				if ($ticket['STATUS'] === 'pending') {
					$pending = true;
					break;
				}
			}
		}
		if ($pending) {
			$r = false;
		}
		$$store[$pending][$r][$partyid] = $party;
		++$$count;
	}
	unset($party);

	if (!($parties = match($_REQUEST['SUBACTION']) {
		'aspiring'	=> $aspiring_parties,
		'ADE'		=> $ADE_parties,
		null		=> $show_parties})
	) {
		goto no_unapproveds;
	}
	$show_row = static function(
		array		 $unaccepted,
		array		 $tickets,
		contactstars $contactstars
	) use ($org_goings, $loc_goings): void {
		$pending_count = 0;
		$open_count = 0;
		$closed_count = 0;
		$visible_count = 0;
		$userid = $unaccepted['USERID'];
		$partyid = $unaccepted['PARTYID'];
		set_cell_value($unaccepted['STAMP_TZI']);
		$row_class = '';
		unset($ticket);
		if ($have_tickets = isset($tickets[$partyid])) {
			foreach ($tickets[$partyid] as $ticket) {
				switch ($ticket['STATUS']) {
				case 'open':
					++$open_count;
					break;
				case 'closed':
					if ($ticket['TRACKIT']) {
						++$closed_count;
					}
					break;
				default:
					++$pending_count;
					break;
				}
			}
			if ($open_count || $closed_count) {
				$row_class = $closed_count ? 'deny ' : 'accept';
			}
		}
		layout_start_rrow(0, $row_class, 'rpad right nowrap');
		?><small><?
		date_display_link_tzi($unaccepted['STAMP_TZI'] - ($unaccepted['AT2400'] ? 1 : 0),' ',false,false,true);
		?></small><?

		layout_next_cell();
		?><span class="<?=
			!isset($ticket)
		||	$ticket['STATUS'] !== 'pending'
		?	'bold'
		:	'light'
		?>"><?=
			get_element_link('party', $unaccepted['PARTYID'], $unaccepted['NAME'])
		?></span><?

		if ($unaccepted['POTENTIAL_CUSTOMER']) {
			?> <span class="win"><span class="blink"><?=
				RIGHTWARD_ARROW_ENTITY
				?></span> <?=
				element_name('potential_customer')
			?></span><?
		}

		set_cell_value($unaccepted['SHORTA3']);
		layout_next_cell();
		if ($unaccepted['COUNTRYID']) {
			show_country_flag($unaccepted['COUNTRYID']);
		}

		layout_next_cell();
		if ($unaccepted['LOCATION_NAME']) {
			echo get_element_link('location',$unaccepted['LOCATIONID'],$unaccepted['LOCATION_NAME']);

		} elseif ($unaccepted['LOCATION_ADDR']) {
			?><span class="light"><?= escape_utf8($unaccepted['LOCATION_ADDR']) ?></span><?
		}

		layout_next_cell();
		if ($unaccepted['ORGIDS']) {
			$orgs = [];
			foreach ($unaccepted['ORGIDS'] as $organization_id) {
				$orgs[] = get_element_link('organization',$organization_id);
				$visible_count = max($visible_count, getifset($org_goings,$organization_id) ?: 0);
			}
			?><small><?= implode(', ', $orgs) ?></small><?
		}

		?></td><?
		?><td width="52" data-value="<?= $contactstars->value($userid) ?>" class="nowrap"><?
		if ($contactstars->have_star($userid)) {
			?><small><?
			$contactstars->show_stars($userid,false);
			?></small><?
		}

		set_cell_value($have_tickets ? ($open_count * 10 + $closed_count * 5 + $pending_count * 2) : 0);
		layout_next_cell(class: 'rpad right');
		if ($have_tickets) {
			?><small><?
			foreach ($tickets[$partyid] as $ticket) {
				$incoming =
					$ticket['STATUS'] === 'open'
				||	($ticket['STATUS'] === 'closed' && $ticket['TRACKIT']);

				?><a href="/ticket/<?= $ticket['TICKETID'] ?>"><?=
					get_mail_icon($incoming, alt: '#'.$ticket['TICKETID'])
				?></a><?
			}
			?></small><?
		}

		set_cell_value($unaccepted['CSTAMP']);
		layout_next_cell(class: 'right nowrap');
		?><small><?
		_date_display($unaccepted['CSTAMP'], short: true);
		?></small><?

		layout_next_cell(class: 'right nowrap lpad');
		if ($unaccepted['LOCATIONID']) {
			$visible_count = max($visible_count, $loc_goings[$unaccepted['LOCATIONID']] ?? 0);
		}
		if ($visible_count) {
			echo $visible_count;
		}
		layout_stop_row();
	};

	$contactstars = new contactstars($userids);

	ksort($parties);

	foreach ($parties as $pending => $partiess) {
		foreach ($partiess as $r => $partiesss) {
			layout_open_box('party');
			layout_box_header(
				$pending
			?	__('header:pending')
			:	__('party:header:unapproved_parties').
					($r ? ' '.MIDDLE_DOT_ENTITY.' '.element_plural_name('relation') : '')
			,	!$pending && $r ? get_crown() : ''
			);
			layout_open_table(TABLE_FULL_WIDTH | TABLE_SORTABLE | TABLE_VTOP);
			?><thead data-sortable="header" class="bold"><?
			layout_start_header_row();
			layout_header_cell_right(Eelement_name('date'),								COLUMN_SORT_NUMERIC,'nowrap rpad');
			layout_header_cell		(Eelement_name('name'),								COLUMN_SORT_ALPHA);
			layout_header_cell		(null,														COLUMN_SORT_ALPHA);
			layout_header_cell		(Eelement_name('location'),							COLUMN_SORT_ALPHA);
			layout_header_cell		('<small>'.Eelement_name('organization').'<small>',	COLUMN_SORT_ALPHA);
			layout_header_cell		('<small>&#128119;</small>',								COLUMN_SORT_NUMERIC,'threestars');
			layout_header_cell_right('<small>'.get_mail_icon().'</small>',						COLUMN_SORT_NUMERIC | CELL_RIGHT_SPACE);
			layout_header_cell_right('<small>'.__C('field:added').'</small>',				COLUMN_SORT_NUMERIC);
			layout_header_cell_right('&#128994',												COLUMN_SORT_NUMERIC);
			layout_stop_header_row();
			?></thead><?
			?><tbody><?
			foreach ($partiesss as /* $partyid => */ $party) {
				$show_row($party, $tickets, $contactstars);
			}
			?></tbody><?
			layout_close_table();
			layout_close_box();
		}
	}
	return;

	no_unapproveds:
	?><div class="block"><?= __('party:info:no_unaccepted_LINE') ?></div><?
}

function show_new_profile_info(string $element, int $id): void {
	/** @noinspection NotOptimalIfConditionsInspection */
	if (false === ($connections = match($element) {
		'artist'		=> memcached_single_int('lineup',  "SELECT COUNT(*) FROM lineup  WHERE ARTISTID   = $id"),
		'location'		=> memcached_single_int('party',   "SELECT COUNT(*) FROM party   WHERE LOCATIONID = $id"),
		'organization'	=> memcached_single_int('connect', "SELECT COUNT(*) FROM connect WHERE MAINID	  = $id AND MAINTYPE = 'organization' AND ASSOCTYPE = 'party'")})
	||	false === ($views = memcached_single_int(($table = "{$element}_counter"), "
																		SELECT CAST(SUM(VIEWS) AS UNSIGNED) FROM `$table` WHERE {$element}ID = $id", ONE_DAY, flags: DB_DONT_FRESHEN))
	) {
		return;
	}
	?><span class="warning"> [<?
	echo element_name('new_profile');
	if ($connections) {
		?> <?= MULTIPLICATION_SIGN_ENTITY ?> <? echo $connections;
	}
	if ($views) {
		?>, <?= $views, MULTIPLICATION_SIGN_ENTITY ?> <? echo __('stats:viewed');
	}
	?>]</span><?
}

function show_needupdate_form_part(?array $item): void {
	layout_next_cell();
	show_input_with_label(
		!$item || $item['NEEDUPDATE'],
		'hilited',
		input:  ['type'	 => 'checkbox',
				 'name'	 => 'NEEDUPDATE',
				 'value' => '1'],
		text:	__('item:fill_later'),
	);
	layout_restart_row();
}

function show_needupdate_mark(array $item): void {
	if (!HOME_THOMAS
	&&	CURRENTUSERID !== 2269
	) {
		return;
	}
	$element = $_REQUEST['sELEMENT'];
	$id		 = $_REQUEST['sID'];

	include_style('admin');

	$class = $item['NEEDUPDATE'] ? 'in-need' : 'no-need';

	#$complement = HEAVY_GREEK_CROSS;
	$complement = __('action:complement');

	?> <span class="needupdate <?= $class ?>" onclick="
	const done = Boolean(this.className.match(/in-need/));
	do_inline('POST', '/set_needupdate.act', 'ACT', function(req) {
		if (req.ok) {
			event.target.lastChild.innerHTML = event.target.lastChild.innerHTML === '?' ? '!' : '?';
			event.target.className = 'needupdate ' + (done ? 'no-need' : 'in-need');
		}
	}, 'ELEMENT=<?= $element ?>&amp;ID=<?= $id ?>&amp;DONE=' + (done ? '1' : '0'));
	"><?= $complement ?><span><?= $item['NEEDUPDATE'] ? '!' : '?' ?></span></span><?
}

function act_needupdate(): int {
	if (!($element = require_element($_POST, 'ELEMENT', ['artist', 'location', 'organization'], strict: true))
	||	!($id	   = require_idnumber($_POST, 'ID'))
	) {
		return 400;
	}
	global $currentuser;
	$currentuser ??= new _currentuser(TINY_USER | SKIP_SETTINGS);
	if (!require_admin($element)) {
		return 403;
	}
	$new_value = !empty($_POST['DONE']) ? '0' : '1';
	if (!db_insert($element.'_log', "
		INSERT INTO {$element}_log
		SELECT * FROM $element
		WHERE NEEDUPDATE != $new_value
		  AND {$element}ID = $id")
	|| !db_update($element, "
		UPDATE $element SET
			MSTAMP		= ".CURRENTSTAMP.",
			MUSERID		= 2269,
			NEEDUPDATE	= $new_value
		WHERE NEEDUPDATE != $new_value
		  AND {$element}ID = $id")
	) {
		return 500;
	}
	return 200;
}
