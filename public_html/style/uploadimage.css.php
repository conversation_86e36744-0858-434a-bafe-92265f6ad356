<?php

require_once '_styles.inc';
require_once '../_browser.inc';

$style = new styles('style/uploadimage.css.php');
/** @noinspection PhpRedundantOptionalArgumentInspection */
extract(styles::$styles, \EXTR_OVERWRITE);

require_once '../_uploadimage.inc';

?>
.draghover {
	outline: 2px dotted #00FF00;
}
.crop-over {
	user-select: none;
	background-color: rgba(0,0,0,.8);
	width: 100%;
	height: 100%;
	left: 0;
	top: 0;
	position: fixed;
	z-index: 10000;
}
.crop-over .container {
	display: flex;
	justify-content: center;
	width: 100%;
	height: 100%;
}
.crop-over .container .the-uimg {
	display: block;
	max-height: 90%;
	max-width: 90%;
	align-self: center;
}
.uimg-container {
	text-align: center;
	background-color: <?= $LITE ? '#00000019' : '#FFFFFF19' ?>;
}
.uimg-selector > DIV {
	margin: .4em .2em .2em .2em;
}
.uimg-selector > DIV:not(.uimg-generated) {
	cursor: n-resize;
}
.uimg-selector .uimg-generated {
	opacity: .4;
	cursor: crosshair;
}
.uimg-selector .uimg-generated.uimg-generated-chosen {
	opacity: 1;
	cursor: default;
}
.uimg-selector .uimg-generated.uimg-generated-chosen .the-uimg {
	outline: 2px solid green;
	outline-offset: 1px;
}
.uimg .spc2,
.uimg .spc2 IMG {
	display: inline-block;
	vertical-align: 0;
}
.crop-button {
	position: absolute;
	top: 1.9em;
	left: .3em;
	margin-top: -1.5em;
	cursor: pointer;
	color: #FFFFFFCC;
	text-shadow: -1px 0 #0000004C, 0 1px #0000004C, 1px 0 #0000004C, 0 -1px #0000004C;
}
.crop-button:hover {
	color: #00FF00CC;
	text-shadow: -1px 0 #000000CC,0 1px #000000CC, 1px 0 #000000CC, 0 -1px #000000CC;
}
.crop-button * {
	font-size: 300%;
	vertical-align: top;
}
.uimg-crop {
	z-index: 20;
	visibility: hidden;
	position: absolute;
	background-color: rgba(255,255,255,.3);
	overflow: auto;
	resize: both;
	border: 1px solid green;
}
.upload-previews {
	margin-bottom: .5em;
}
.upload-previews .uimg-block {
	display: inline-block;
	position: relative;
	vertical-align: bottom;
	max-height: 300px;
	margin: .2em;
}
/*.uploadimageform .box::after {
	content: '';
	display: inherit;
	clear: inherit;
}*/
.uimg-block::after {
	content: ' ';
}
.uimg-block {
	max-width: 100%;
	object-fit: cover;
}
.uimg-block > IMG,
.uimg-block > DIV {
	max-height: 300px;
	max-width: 100%;
	border-radius: 2px;
}
.uimg-block > IMG {
	display: block;
}
.uimg-block .uimg-pdf {
	width: 200px;
	line-height: 200px;
	text-align: center;
	background-color: <?= $LITE ? '#00000019' : '#FFFFFF19' ?>;
	font-weight:bold;
	text-shadow: 1px 1px 1px <?= $LITE ? 'white' : 'black' ?>;
}
.uimg-block INPUT[name="COPYRIGHT[]"] {
	width: calc(100% - 1em - 2em);
	position: absolute;
	right: 0;
	bottom: 0;
	margin: 0 .2em .2em 0;
}
.uimg .date {
	margin: .5e;
	font-size: 80%;
	opacity: .8;
}
