<?php

declare(strict_types=1);

require_once '_styles.inc';
require_once '../_hosts.inc';
require_once '../_browser.inc';
require_once '../_smallscreen.inc';

$style = new styles('style/admin.css.php');
/** @noinspection PhpRedundantOptionalArgumentInspection */
extract(styles::$styles, \EXTR_OVERWRITE);

require_once '_colors.inc';

?>
.priority,
.priority A {
	color: <?= $LITE ? '#070' : '#0C0' ?>;
	font-weight: bold;
}
.vidinf {
	text-shadow: 1px 1px 1px black;
	margin: .5em;
}
.vidinf.done {
	background-color: rgba(255, 0, 0, .6);
}
.vidinf.inact {
	background-color: rgba(255, 128, 0, .6);
}
.fblue,
.fblue A {
	color: #<?= $LITE ? '3C5A99' : '738ec9' ?>;
}
#keeplockcheckbox {
	text-align: right;
	float: right;
}
.tags,
.check-for-info,
.check-for-info A {
	color: #F0F;
	font-weight: bold;
}
.check-for-info A:hover {
	filter: brightness(1.5);
}
.check-for-info A {
	text-decoration: solid #FF00FF66 underline;
}
.tags .tag-field-name {
	opacity: .4;
}
.checkup,
.neeupdate {
	cursor: pointer;
	color: <?= $LITE ? '#555' : '#CCC' ?>;
}
.box-header .checkup,
.needupdate {
	margin-left: 1em;
}
.needupdate.in-need {
	font-weight: bold;
	color: <?= $LITE ? '#E67E00' : '#FF8C00' ?>;
}
.checkup.checked,
.checkup.now-checked {
	color: <?= $LITE ? '#13A413' : '#0D0' ?>;
}
.checkup.now-checked {
	font-weight: bold;
	cursor: default;
}
.rights LABEL {
	display: inline-block;
	width: 100%;
	cursor: pointer;
}
#queries .query {
	border: 1px solid <?= $LITE ? '#CCC' : '#333' ?>;
	padding: .2em;
	margin: 0;
	border-collapse: collapse;
}
#queries .server {
	color: #FF0;
	font-weight: bold;
}
