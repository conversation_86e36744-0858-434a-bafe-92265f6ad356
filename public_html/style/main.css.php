<?php

declare(strict_types=1);

# z-indices:
#
#	bgov:		 300000000
#	main-nav:		  1000
#	connectbox-back:   501
#	connectbox:		   500
#	closebutton:		40
#	top-notice:
#	top-warning:
#	top-notice:
##	cookie-consent:		26
#	topr:				25
#	maininfo:hover		21
#	socialbox:			15
#	ads:				10

require_once '_styles.inc';
require_once '../_hosts.inc';
require_once '../_browser.inc';
require_once '../_smallscreen.inc';
require_once '../defines/videothumb.inc';

$style = new styles('style/main.css.php');
/** @noinspection PhpRedundantOptionalArgumentInspection */
extract(styles::$styles, \EXTR_OVERWRITE);

require_once '_colors.inc';

?>
:root {
	--line-height: 1.2;
}
<?

if (!$LITE) { ?>
HTML {
	-webkit-tap-highlight-color: #FFFF0066;
}
<? } ?>

HTML {
	<? styles::vendor_prefixed('text-size-adjust:none') ?>;
}
<? if (SMALL_SCREEN) { ?>
	@media (min-device-width:641px) {
		HTML {
			<? styles::vendor_prefixed('text-size-adjust:auto') ?>
		}
	} /* endmedia */
<? } ?>

<? if (!ANDROID && $CONTRAST < 1) { ?>
	BODY {
		opacity: <?= $CONTRAST ?>;
	}
<? } ?>

BODY,
.bg {
	font-weight: 300;<?
	if (WEBKIT) {
		# NOTE: Antialiased darkens the resulting font by 30%, but not the bold version,
		#		which leads to a nice minimally darker white font on dark site, with clear bolds.
		#		On the light site, a more pronounces black has much better contrast than the 30% darkened one.

		?>-webkit-font-smoothing: <?= $LITE ? 'subpixel-antialiased' : 'antialiased' ?>;<?
	}
	if (FIREFOX) {
		?>-moz-osx-font-smoothing: grayscale;<?
	} ?>
	background-color: <?= $LITE ? '#FFF' : '#000'  ?>;
}
BODY,
#keeplockfloat {
	line-height: 1.5;
}
#keeplockfloat {
	padding: 2px;
	float: right;
}
.rounded .profile-image {
	border-radius: 50%;
}
UL {
	padding: 0 1.2em;
	/* text-indent: <?= FIREFOX ? '-.6em' : '-.9em' ?>; */
	text-indent: -1em;
	list-style-position: outside;
	list-style-type: none;
}
UL LI:before {
	color: <?= styles::FLOCK_YELLOW ?>;
	content: "\2022\2002";
}
.party-day,
.party-day A,
.party-day A:visited {
	color: <?= $LITE ? styles::FLOCK_YELLOW_DARK : styles::FLOCK_YELLOW ?>;
}
.murp > DIV {
	display: inline-block;
}
.murp.high {
	background-color: red;
}
.murp.mid {
	background-color: green;
}
.murp.low {
	background-color: yellow;
}
.murp.sky {
	background-color: white;
}
TABLE {
	border-collapse: collapse;
}
/*.nobpad {
	padding-bottom: 0;
}*/
TABLE.hhla {
	/* nicer hiliting */
	border-collapse: collapse !important;
}
.socialbox {
	margin: -2.7em -2em;
	padding: 2em;
	z-index: 15;
}
.activities SPAN {
	display: inline-block;
	background-color: rgba(<?= $LITE ? styles::RGB_BLACK : styles::RGB_WHITE ?>,.1);
	border-radius: 3px;
	padding: 0 .5em;
}
.socialbox > .bg {
	position: relative;
	width: <?= SMALL_SCREEN ? 250 : 300 ?>px;
	height: 100px;
	line-height: 100px;
	text-align: center;
	<? styles::vendor_prefixed('box-shadow:'.make_shadow(0,0,2,2).'rgba(127,127,127,.5)') ?>
}
.socialboxv2 {
	margin: 0 0 0 1em;
	z-index: 15;
}
.socialboxv2 > .bg {
	position: relative;
	vertical-align: middle;
	padding:2em;
	<? styles::vendor_prefixed('box-shadow:'.make_shadow(0,0,2,2).'rgba(127,127,127,.5)') ?>
}
.socialboxv2 > .bg > DIV {
	padding: .3em;
}
.buttons {
	margin-top: -1em;
}
.buttons > DIV {
	line-height: normal;
	display: inline-block;
	margin-bottom: <?= (100 - 62) / 2 ?>px !important;
}
.buttons > DIV,
.buttons > DIV > *,
.fb-like > SPAN {
	vertical-align: bottom !important;
}
.altered-status-container {
	overflow: hidden;
	position: relative;
}
.altered-status-container .original-content {
	<? styles::vendor_prefixed('filter:grayscale(1)') ?>;
	<? styles::vendor_prefixed('transform: translateZ(0)') ?>;
}
.event-over {
	z-index: 10;
	width:100%;
	right: 0;
	top: 12.5%;
	text-align:center;
	transform: rotate(-25deg);
	opacity: .7;
	pointer-events: none;
	font-weight: bold;
}
.cancelled-over.event-over {
	color: rgba(255, 0, 0, .7) !important;
}
.deleted-over.event-over {
	color: rgba(255, 255, 255, .5) !important;
	text-shadow: 0 0 5px black;
}
.deleted-container .original-content {
	opacity: .3;
}
.postponed-over.event-over {
	color: rgba(0, 255, 0, .6) !important;
}

.locked-item,
.item-done,
.colorless {
	<? styles::vendor_prefixed('filter:grayscale(1)') ?>;
	<? styles::vendor_prefixed('transform: translateZ(0)') ?>;
}
.colorfull-hover:hover {
	<? styles::vendor_prefixed('filter:grayscale(0)') ?>;
	<? styles::vendor_prefixed('transform: translateZ(0)') ?>;
}
.forcecolors {
	<? styles::vendor_prefixed('filter:grayscale(0)') ?> !important;
}
.lesscolor7 {
	<? styles::vendor_prefixed('filter:grayscale(.7)') ?>;
	<? styles::vendor_prefixed('transform: translateZ(0)') ?>;
}
.colorhover IMG {
	<? styles::vendor_prefixed('filter:grayscale(1)') ?>;
}
.colorhover:hover IMG {
	<? styles::vendor_prefixed('filter:grayscale(0)') ?>;
}
.redhue {
	<? styles::vendor_prefixed('filter:hue-rotate(120deg)') ?>;
}
.bshd.redhue {
	<? styles::vendor_prefixed('filter: drop-shadow(.5px .5px .5px #000) hue-rotate(120deg)') ?>;
	<? styles::vendor_prefixed('transform: translateZ(0)') ?>;
}
.orangehue {
	<? styles::vendor_prefixed('filter:hue-rotate(180deg)') ?>;
}
.greenhue {
	<? styles::vendor_prefixed('filter:hue-rotate(270deg)') ?>;
}
/*.red-to-greenhue {
	<? styles::vendor_prefixed('filter:hue-rotate(100deg)') ?>;
}*/
.colorless.appiclogo {
	filter: contrast(.1);
}
.ibg {
	background-color: <?= $LITE ? '#000' : '#FFF' ?>;
}
.sh {
 	margin-bottom: .4em;
	background-color: <?= inverted_color(0xE6E6E6) ?>;
}
.shrp,
.sh .bold,
.sh {
	height: 40px;
	line-height: 40px;
	padding: 0;
}
.sh FORM {
	display: inline-block;
	vertical-align: middle;
}
.mwrp {
	margin: 0 1em 0 0 !important;
}
#mebox {
	padding: 0 .4em;
	margin-left: 5px;
}
.smallscreen .sh {
	padding: 0 .5em;
}
.shrp {
	margin: 0;
}
.shrp .icon {
	vertical-align: middle;
}
.smallscreen .icon {
	margin: 0 .3em;
}
.shrp {
	right: .4em;
	top: 0;
}
HR.slim {
	opacity: .6;
	margin: .8em 0;
	height: 1px;
	border: 0;
	background-color: <?= $LITE ? '#0000004D' : '#FFFFFF4D' ?>;
}

.lineup .timeless {
	opacity: .7;
	background-color: rgba(255,128,0,.2);
}
.lineup.alter {
	font-size: 90%;
}
.lineup .overlap {
	background-color: <?= $LITE ? '#FFCFCF' : '#701515' ?>;
}
.lineup .overlap-inner {
	background-color: <?= $LITE ? '#FFDFDF' : '#600505' ?> !important;
}
.lineup SELECT {
	min-width: 2em;
}
.lineupsep {
	display: inline-block;
	border-top: 1px solid <?= $LITE ? '#0000001A' : '#FFFFFF1A' ?>;
	padding: .2em 0;
	margin-top: .2em;
}
BODY,
LEGEND {
	color: <?= $LITE ? '#000' : '#FFF' ?>;
}
PRE {
	white-space: pre-wrap;
}
FIGURE {
	margin: 0;
}
BODY {
	padding: 0;
	margin: 0;
	font-family: <?= $FONT_TYPE ?>, sans-serif;<?
	if ($SCALE !== 1) {
		?>font-size: <?= $SCALE,'em' ?>;<?
	}
	/*styles::vendor_prefixed('font-smoothing: antialiased') ?>;<?*/
	if (FIREFOX) {
		?>-moz-osx-font-smoothing: grayscale;<?
	}
	?>
}
* {
	box-sizing: border-box;
}
.smiley {
	left: 0;
	bottom: -1px;
}
.smallspacer {
	height: .5em;
}

P,
DL,
.block {
	display: flow-root;
	margin: 0 0 .8em;
}
.block-sep {
	display: inline-block;
	width: .8em;
}
.sbot .l,
.sbot .r {
	margin: 0 .25em .2em .25em;
}
.brmrgn {
	margin-bottom: 1em;
}

UL,
OL,
#subbar > DIV {
	margin-top: 0;
	margin-bottom: .4em;
}
#subbar > IMG {
	vertical-align: middle;
}
#subbar {
	margin-bottom: .5em;
	background-color: rgba(<?= $LITE ? '0,0,0,.025' : '255,255,255,.1' ?>);
}
.heartsender {
	border-top: 1px solid #AAA;
}
.lck {
	position: absolute;
	bottom: -2px;
	left: 50%;
	margin-left: 38px;
}
.z0 {
	z-index: 0;
}
.z1 {
	z-index: 1;
}
.z2 {
	z-index: 2;
}
.ratings {
	padding: 1em;
}
.ratings .date,
.small {
	font-size: 80%;
}
.zoom-smallish {
	zoom: .9;
}
.larger {
	font-size: 120%;
}
.small.pad {
	margin: .1em;
}
TABLE.vtop .middle,
.ratings .date,
.visible-photo,
.hrt.middle,
.middle,
.middle *,
.vmiddle {
	vertical-align: middle;
}
.ratings .txt {
	position: relative;
}

IFRAME,
IMG {
	border: 0;
}

IFRAME.seamless {
	background-color: transparent;
	overflow: hidden;
}

/* hide counting pixels */
IMG[width="1"] {
	display: none;
}
.ADE {
	font-weight: 700;
}
.ADE,
.ADE A {
	color: <?= $LITE ? '#AA0' :'#FF0' ?> !important;
}
.ADE.darker,
.ADE.darker A {
	color: <?= $LITE ? '#DD0' :'#BB0' ?>;
}
.ADE.darker .abbr-hook {
	filter: brightness(50%);
}
.ADE.darker .abbr {
	filter: brightness(75%);
}
.time-error {
	background-color: #FF800066 !important;
}
.votes TD {
	padding: .1em .5em !important;
}
A.compo-chosen:hover,
.win,
DL.boldterm DT,
TABLE.boldfield TD.field,
TABLE.section TD.left,
TABLE.regular TH,
.blackbox,
.bold,
.box TR.active,
.compo-invisible,
.connect-status,
.day-header,
.error,
.mfav,
.notice,
.pay-status,
.party-day,
.permbanned,
.selected,
.selected-page,
.sold-out,
.sold-out-old,
.ubbheader,
.warning,
.tor,
.party-info .date,
.quote .info A,
.partymonth,
.lineup TH,
LI.area .name,
TR.connected TD.connect-status,
.inforow,
A.compo-chosen,
.spoiler,
.bold-hilited,
.double-hilited,
.bold-hilited-red,
.bold-hilited-yellow,
.bold-hilited-green,
.bold-hilited-cyan,
.bold-hilited-orange,
.double-hilited-green,
.invalid,
.ratings TH,
.partyprice .strip,
.nws,
.product_details_mini,
.isonlast,
.fbchoice .abs TABLE,
.vidinf,
.takeover,
B {
	font-weight: 700;
}

.hdr .title H1,
.nb,
.nb * {
	font-weight: 300 !important;
}

A {
	color: <?= $LITE ? '#000000CC' : '#FFFFFFE6' ?>;
}
.unhideanchor:not(A):not(.error):not(.warning):not(.notice) {
	color: <?= $LITE ? '#000000CC' : '#FFFFFFE6' ?> !important;
}
A:visited {
	color: <?= $LITE ? '#00000099' : '#FFFFFFB3' ?>;
}

.unhideanchor:not(A):not(.error):not(.warning):not(.notice):hover,
.unhideanchor:not(A):not(.error):not(.warning):not(.notice):active,
A:active,
A:hover	{
	text-decoration: underline;
	color: <?= $LITE ? '#000' : '#FFF' ?>;
}

A:active,
.unhideanchor:not(A):not(.error):not(.warning):not(.notice):active {
	text-shadow: 0 0 5px <?= $LITE ? '#0000000A' : '#FFFFFF33' ?>;
}
A {
	text-decoration: none;
}
.unhideanchor:not(A):not(.error):not(.warning):not(.notice).extra,
A.extra {
	text-decoration: none;
	border-bottom: 1px solid <?= $LITE ? '#00000033' : '#FFFFFF33' ?>;
}
.unhideanchor.extra:not(A):not(.error):not(.warning):not(.notice):hover,
A.extra:hover {
	border-bottom: 1px solid <?= $LITE ? '#000' : '#FFF' ?>;
}
.action-button A,
.seemtext,
.seemtext A,
.seemtext:hover,
.seemtext A:hover {
	text-decoration: none !important;
}
.jumpPrev,
.jumpNext {
	top: 50%;
	height: 10em;
	padding-top: 4.5em;
	margin-top: -5em;
	<? if (SMALL_SCREEN) { ?>
		margin-left:-.4em;
		margin-right: -.4em;
	<? } ?>
}
.unavailable,
.unavailable TD {
	text-decoration: line-through !important;
}
.unavailable:active,
.unavailable:hover {
	text-decoration: underline line-through;
}
.buddies {
	color: <?= $LITE ? '#AA6655' : '#FF9999' ?> !important;
}
.sold-out {
	color: <?= diff_color(0x990000) ?>;
}
.appic,
.appic A {
	color: <?= $LITE ? '#802e2e' : '#FFACAC' ?> !important;
}
.takeover,
.win A,
.win {
	color: <?= $LITE ? '#A80' : diff_color(0xAAAA44) ?>;
}
.win .orange {
	color: <?= $LITE ? '#000' : '#FFAA1A' ?>;
}
.winlink A,
.winlink,
A.winlink:visited {
	color: #<?= !$LITE ? 'FFA' : '660' ?>;
}
A.win:visited {
	color: <?= diff_color(0x999933) ?>;
}
A.win:hover {
	color: <?= diff_color(0xBBBB55) ?>;
}
.underline {
	text-decoration: underline !important;
}

SPAN.search-hilite {
	background-color: #FF0;
	background-color: rgba(255,255,0,.5);
	outline: 2px solid rgba(255,255,0,.5);
	text-shadow: 1px 1px <?= $LITE ? '#FFFFFFCC' : '#000000CC' ?>;
}
.fallback,
.dims {
	border: 1px solid #FFFFFF4D;
	font-size: 80%;
	white-space: nowrap;
	left: .3em;
	top: .3em;
	padding: 2px 7px !important;
}
.fallback {
	top: unset;
	bottom: .3em;
}
.inline,
.ubb.smenu {
	display: inline;
}
.ubb.smenu A {
	font-style: normal;
	white-space: nowrap;
	border: 1px solid #FFFFFF1A;
}
.smenu A.prty {
	font-weight: 600;
	color: <?= $LITE ? '#966400' : '#FFC800' ?>;
	border: 1px solid <?= $LITE? '#0000001A' : '#FFFFFF33' ?>;
}
TABLE.ubb,
TABLE.nobut,
.no-bottom,
UL.ubb,
OL.ubb {
	margin-bottom: 0;
}
.mw100 {
	max-width: 100%;
}
.mw90 {
	max-width: 90%;
}
.ubb.l {
	margin: 0 .5em .5em 0;
}
.ubb.r {
	margin: 0 0 .5em .5em;
}
.connectpart {
	margin: .3em 0;
	padding: .2em .5em;
	background-color: rgba(<?= $LITE ? styles::RGB_BLACK : styles::RGB_WHITE ?>,.05);
}
.connectpart:hover {
	background-color: <?= $LITE ? '#0000001A' : '#FFFFFF1A' ?>;
}
.left-noimg,
IMG.ubb.left {
	float: left;
	margin: .25em .5em .25em 0;
}
.right-noimg,
IMG.ubb.right {
	float: right;
	margin: .25em 0 .25em .5em;
}
.clear,
.nf.header {
	clear: both;
}
.lclr {
	clear: left;
}
.rclr {
	clear: right;
}
TABLE {
	font-family: inherit;
	font-size: inherit;
	font-weight: inherit;
	margin-bottom: .8em;
}
.defptr {
	cursor: default;
	width: 20px;
	height: 17px;
}
.wait {
	cursor: wait;
}
TH.sortable,
TD.sortable {
	cursor: s-resize !important;
}
TD.sortable:hover {
	<? styles::vendor_prefixed('box-shadow:'.make_shadow(0,0,10).($LITE ? '#00000080' : '#FFFFFFB3')) ?>;
}
H3 {
	font-size: 100%;
}
.textarea-handle {
	height: 4px;
	background-color: <?= inverted_color(0xDDDDDD) ?>;
	color: <?= inverted_color(0xDDDDDD) ?>;
	cursor: s-resize;
}
.bgov {
	position: fixed;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	z-index: 300000000;
	background-color: #000000CC;
}
.bgov.slideshow {
	background-color: #000000E6;
}
.party-attending {
	background-color: #00FF0024 !important;
}
.party-attending-maybe {
	background-color: rgba(0,255,0,.06) !important;
}
.zebra {
	background-color: <?= $LITE ? '#FFFFFF80' : '#00000080' ?>;
}

.auto.zebra TR:nth-child(odd),
.white .zebra,
.gallery .zebra,
.music .zebra,
.panther {
	background-color: <?= $LITE ? 'rgba(0,0,0,.05)' : 'rgba(255,255,255,.06)' ?>;
}

TABLE.transparent .zebra {
	background-color: <?= $LITE ? 'rgba(0,0,0,.05)' : 'rgba(255,255,255,.1)' ?>;
}

.partylist,
TABLE.regular {
	background-color: <?= $LITE ? '#F6F6F6' : '#101010' ?>;
}
.partylist TIME {
	max-width: 12em;
	overflow: hidden;
	white-space: nowrap;
}
.default TD.tsep {
	width: 2em;
}
.deflist > TBODY > TR > TD,
FORM TABLE.dyntab > TBODY > TR > TD.field {
	vertical-align: top;
}
/*.deflist > TBODY > TR > TD {
	padding: .3em .6em;
}*/

.darken {
	filter: brightness(.6);
}

<?
$result = null;
foreach ([
	2	=> ['.lighter'],
	3	=> null,
	4	=> ['.ratings .date','.self','.admin','.light','.langs IMG'],
	5	=> null,
	6	=> null,
	7	=> null,
	8	=> null,
] as $opacity => $classes) {
	if (!$classes) {
		$classes = ['.light'.$opacity];
	}
	foreach ($classes as $class) {
		$result[$opacity][] = $class;
	}
}
foreach ($result as $opacity => $classes) {
	echo implode(',', $classes), '{
		opacity: .', $opacity, ';
	}';
}
?>
.regular > TBODY > TR > TD,
.regular > * > TR > TH {
	border: 1px solid <?= $LITE ? 'rgba(0,0,0,.05)' : 'rgba(255,255,255,.05)' ?>;
}
.fTH {
	background-color: <?= inverted_color(0xEEDDDD) ?> !important;
}
.regular > * > TR > TH {
	background-color: <?= inverted_color(0xEEDDDD) ?>;
	text-align: left;
}
.regular > TBODY > TR:first-child > TH {
	background-color: <?= $LITE ? '#FFF' : '#000' ?>;
}
.hdr {
	background-color: <?= $LITE ? 'rgba(0,0,0,.05)' : '#FFFFFF1A' ?>;
	padding: .4em .5em;
	margin-bottom: .5em;
}
.flat,
.hdr,
.hdr A,
.hdr A:visited {
	color: <?= $LITE ? '#000' : '#FFF' ?>;
}
.regular > *  > TR > TH {
	border-top: 1px solid <?= $LITE ? '#0000001A' : '#FFFFFF1A' ?>;
}
.regular > * > TR.hdr > TH {
	color: <?= $LITE ? '#000' : '#FFF' ?>;
}
.partylist > * > TR > TH,
.partylist > * > TR > TD,
.regular > * > TR > TH,
.regular > * > TR > TD {
	padding: .4em .4em;
}
.nf {
	margin-top: 0 !important;
}
.nf.elsewhere {
	margin-top: 2em !important;
}
.corners {
	color: <?= $LITE ? '#0000001A' : '#FFFFFF33' ?>;
}
.corners * {
	position: absolute;
	z-index: 2;
}
.tl, .tr {
	top: -2px;
}
.tl, .bl {
	left: -1px;
}
.tr, .br {
	right: -1px;
}
.bl, .br {
	bottom: -2px;
}
.box,
.box-column,
#homec,
.mwrp,
.relative {
	position: relative;
}
.ratings TD:last-child {
	position: relative;
}
.novents {
	pointer-events: none;
}
.dovents {
	pointer-events: auto;
}
.gallery-preview TD {
	width: 20%;
	text-align: center;
	padding: 0;
}
.appearances-line {
	margin: .8em 0 0 0;
	text-align: center;
}
.photos TD {
	vertical-align: bottom;
	padding: 0;
}
.infos TD {
	padding: .2em 0 .4em;
}
.selected-element {
	border: 2px dashed green !important;
}
.remove-element {
	border: 2px dashed red;
	opacity: .3;
}
.remove-element IMG {
	opacity: .3;
}
.unaccepted-element {
	outline: 2px dashed <?= diff_color(0xCC0000) ?>;
	opacity: .5;
}
.yellow-element {
	outline: 2px dashed <?= diff_color(0xCC9900) ?>;
}
.orange-element {
	outline: 2px dashed <?= diff_color(0xFFAA66) ?>;
}
.visible-photo {
	border: 1px solid <?= $LITE ? '#000' : '#555' ?>;
}
.box {
	border-radius: 1px;
	margin-bottom: .8em;
}
.box-column {
	padding: .8em .8em .8em .8em;		/* negative padding does not work */
}
.box-column > DIV.block:last-child,
.box-column > DIV:last-child > TABLE:last-child,
.box-column > FORM:last-child > TABLE:last-child,
.box-column > FORM:last-child > DIV:last-child,
.box-column > TABLE:last-child,
.box-column > NAV:last-child {
	margin-bottom: 0; /* workaround for all those blocks in columns with padding */
}
.showquote {
	float: right;
	font-size: 80%;
	clear: both;
	margin-bottom: .8em;
}
TD.news.box,
TD.report.box,
TD.review.box,
TD.interview.box,
TD.column.box {
	padding: .8em .8em .8em .8em;
}
.box.nopad .box-column,
.nopad {
	padding: 0 !important;
}
.note {
	opacity: .4;
	margin: 0;
	padding: .2em .8em .2em .8em;
	clear: both;
	font-size: 80%;
}

.box .note {
	background-color: <?= $LITE ? 'white' : 'black' ?>;
	margin: 0 -1em -1em -1em;
}

.box .clear .note {
	margin: 0 -.5em 0 0;
}

.box.boarding,
.box.country,
.box.city,
.box.contact-tounknown,
.box.grey,
.box.force.grey {
	background-color: <?= inverted_color(0xF0F0F0) ?>;
}
.box.gallery,
.box.contact-touser,
.box.music,
.box.camera,
.box.white {
	background-color: <?= $LITE ? '#FAFAFA' : '#1A1A1A' ?>;
}
.box.white.new-event {
	background-color: <?= $LITE ? '#FFC' : '#221' ?>;
}
.box.translucent {
	background-color: transparent;
}
.box.contact-failure,
.box.offense,
.box.prohib,
.box.interview,
.box.red {
	background-color: <?= diff_color(0xEEE4E4) ?>;
}
.box.interview-recent,
.late,
.broken-image {
	background-color: #FF000033;
}
.box.poll,
.box.stream,
.box.column,
.box.flock,
.box.contact-action,
.box.promo,
.box.green {
	background-color: <?= diff_color(0xE4EEE4) ?>;
}
.box.column-recent,
.box TR.infocus {
	background-color: <?= diff_color(0xDDFFDD) ?>;
}
<? if (!$LITE) { ?>
.box.lnews,
.box.lighterblue {
	background-color: <?= diff_color(0xF4F4FE) ?>;
}
.box.news,
<? } ?>
.box.party,
.box.label,
.box.contact-toadminfromadmin,
.box.settings,
.box.review,
.box.blue {
	background-color: <?= $LITE ? '#E4E4EE' : '#1C1C30' ?>;
}
.box.review-recent {
	background-color: <?= diff_color(0xDDDDFF) ?>;
}
<? if ($LITE) { ?>
.box.lnews,
.box.lighteryellow	 {
	background-color: <?= diff_color(0xFEFEF4) ?>;
}
.box.news,
<? } ?>
.box.crew,
.box.holiday,
.box.help,
.box.guide,
.box.policy,
.box.faq,
.box.report,
.box.report-recent,
.box.contact,
.box.contact-toadmin,
.box.contact-forwardmsg,
.box.artist,
.box.job,
.box.yellow {
	background-color: <?= diff_color(0xEEEEE4) ?>;
}
.box.lightyellow {
	background-color: <?= diff_color(0xEEEEC4) ?>;
}
.box.darkyellow {
	background-color: <?= diff_color(0xBBBBB4) ?>;
}
.box.contact-forwardedmsg,
.box.relation,
.box.location,
.box.cartoon,
.box.purple {
	background-color: <?= diff_color(0xEEE4EE) ?>;
}
.box.organization,
.box.contest,
.box.banner,
.box.adx,
.box.cyan {
	background-color: <?= diff_color(0xE4EEEE) ?>;
}
.box.orange {
	background-color: <?= diff_color(0xFFF0DD) ?>;
}
.box.meeting,
.box.violet {
	background-color: <?= diff_color(0xE6DDF5) ?>;
}
.box.pink {
	background-color: <?= diff_color(0xFFE3F2) ?>;
}
.event-actions {
	vertical-align:middle;
}
.action-button {
	border: 0 !important;
	border-radius: 3px;
	height:50px;
	line-height: 50px;
	padding: 0 .5em;
	font-weight: bold;
	font-size: 100%;
	display: inline-block;
	text-shadow: 1px 1px rgba(<?= $LITE ? styles::RGB_WHITE : styles::RGB_BLACK ?>, .8);
	cursor: pointer;
	user-select: none;
}
.fb-login-button {
	font-size: 150%;
	height: 50px !important;
	background-color: #3B5998 !important;
	padding-left: 48px !important;
	background-image: url(<?= STATIC_HOST ?>/images/facebook_white.png);
	background-repeat: no-repeat;
	<? if ($LITE) { ?>
		color: #FFF !important;
		text-shadow: 1px 1px #000000CC;
	<? } ?>
}
.generic-button {
	transition: background-color .15s;
	height: 40px;
	line-height: 40px;
	border-radius: 3px;
	padding: 0 2em;
	text-shadow: none;
	background-color: rgba(255, 200, 0, .8) !important;
	border: 1px solid <?= $LITE ? '#FFFFFF1A' : '#0000001A' ?> !important;
	color: black !important;
}
.generic-button.ticketswap {
	padding: 0 1em;
}
.generic-button.ticketswap IMG {
	vertical-align: -7.5px;
	height: 26px;
}
.generic-button.ticketswap,
.generic-button.ticketswap:hover {
	background-color: #00BFF0CC !important;
}
.fw .generic-button.buy {
	text-align: center;
	padding: 0;
	width: 100%;
	max-width: 10em;
}
.generic-button.buy {
	background-color: #FFC800CC !important;
}
.partner-button {
	transition: background-color .15s;
	border: 1px solid #FFFFFF1A;
	border-radius: 2px;
	display: inline-block;
	min-width: 10em;
	padding: .5em;
	background-repeat: no-repeat;
	background-size: contain;
	background-position: center;
	text-align: center;
	cursor: pointer;
	margin-bottom: .5em;
}
.partner-button.bookingcom {
	background-color: #003680;
}
.partner-button.bookingcom:hover {
	background-color: #0044AA !important;
}
.partner-button.livestream {
	background-color: #0046FF;
	<? if ($LITE) { ?>
		color: #FFF;
	<? } ?>
}
.partner-button.livestream:hover {
	background-color: #004690 !important;
}
.partner-button.partybussen {
	background-color: #333;
}
.partner-button.partybussen:hover {
	background-color: #444 !important;
}
.partner-button.winticket:hover {
	background-color: #BBBB44;
}
.partner-button.winticket:hover .win {
	color: #FFF !important;
}
.partner-button.winticket {
	background-color: #99990A;
}
.partner-button.appicwin {
	background-color: #C50046;
}
.appicwin .win {
	color: #DA999C;
}
.partner-button.winticket .win {
	color: #FF0;
}
.partner-button.bookingcom > IMG {
	width: 7em;
	display: inline-block;
	vertical-align: middle;
}
.partner-button.partybussen > IMG {
	width: 8em;
	vertical-align: -2px;
}
.noncust .generic-button.buy {
	padding: 0 .6em;
	<? styles::vendor_prefixed('filter:grayscale(1)') ?>;
	<? styles::vendor_prefixed('transform: translateZ(0)') ?>;
	border: 1px solid <?= $LITE? '#96969680' : '#FAFAFA80' ?> !important;
}
.generic-button.buy:hover {
	background-color: #FFC800 !important;
}
.generic-button.join {
	margin: .2em 0;
	background-color: #00FA00CC !important;
}
.generic-button.join:hover {
	background-color: #00FA00FF !important;
}
.generic-button.vv {
	width: 40px;
	padding: 0 8px;
	background-color: #4489FF !important;
	border: 1px solid #5599FF;
	opacity: .9;
}
.generic-button.vv:hover {
	background-color: #4489FF !important;
	opacity: 1;
}

<?  ?>

.unhideanchor:not(A):not(.error):not(.warning):not(.notice),
.ptr {
	cursor: pointer !important;
}
.uncollapser > .header:hover {
	outline: 1px solid <?= $LITE ? '#00000033' : '#FFFFFF33' ?>;
	background-color:  <?= $LITE ? '#00000033' : '#FFFFFF33' ?>;
}
.progress {
	cursor: progress !important;
}
.box DIV.header {
	margin: -.8em -.8em .8em -.8em;
}
.box.potential-customer DIV.header {
	background-color: <?= $LITE ? '#DD0' : '#550' ?>;
}
.box DIV.header,
.box DIV.header.user-is-online .online {
	padding: .4em .8em;
}
.box DIV.header.user-is-online {
	padding: 0;
}
.bspan {
	margin: 0 .5em;
}
.box::after {
	content: '';
	display: block;
	clear: both;
}
.box DIV.header::after,
.bodyrow::after {
	visibility: hidden;
	display: block;
	font-size: 0;
	content: " ";
	clear: both;
	height: 0;
}
.box DIV.header.ubbheader::after {
	clear: none;
	content: none;
}
.box.empty DIV.header {
	margin-bottom: 0;
}
.box DIV.header.bottom {
	margin: 0 -.4em 0 -.4em;
}
H1,
H2,
H3 {
	font-size: 100%;
	margin: 0;
	display: inline;
}
.boarding DIV.header,
.country DIV.header,
.city DIV.header,
.grey DIV.header {
	background-color: <?= diff_color(0xCCCCCC) ?>;
}
TR.nowsoon.empty TH {
	padding: 0;
}
.nsbub {
	max-height: 100%;
	overflow-y: auto;
	overflow-x: hidden;
}
.music DIV.header,
.white DIV.header,
.camera DIV.header,
.gallery DIV.header {
	background-color: <?= diff_color(0xEAEAEA) ?>;
}
.contact-failure DIV.header,
.offense DIV.header,
.prohib DIV.header,
.interview DIV.header,
.red DIV.header {
	background-color: <?= diff_color(0xCCC2C2) ?>;
}
.stream DIV.header,
.poll DIV.header,
.column DIV.header,
.flock DIV.header,
.promo DIV.header,
.green DIV.header {
	background-color: <?= diff_color(0xC2CCC2) ?>;
}
<? if (!$LITE) { ?>
.news DIV.header,
<? } ?>
.settings DIV.header,
.label DIV.header,
.party DIV.header,
.review DIV.header,
.blue DIV.header {
	background-color: <?= $LITE ? '#C9C9DC' : '#2C2C46' ?>;
}
<? if ($LITE) { ?>
.news DIV.header,
<? } ?>
.contact DIV.header,
.crew DIV.header,
.holiday DIV.header,
.help DIV.header,
.guide DIV.header,
.policy DIV.header,
.faq DIV.header,
.artist DIV.header,
.report DIV.header,
.job DIV.header,
.yellow DIV.header {
	background-color: <?= diff_color(0xCCCCC2) ?>;
}
.contact-toadmin-forward DIV.header,
.relation DIV.header,
.box.location DIV.header,
.cartoon DIV.header,
.purple DIV.header {
	background-color: <?= diff_color(0xCCC2CC) ?>;
}
.contest DIV.header,
.organization DIV.header,
.banner DIV.header,
.adx DIV.header,
.cyan DIV.header {
	background-color: <?= diff_color(0xC2CCCC) ?>;
}
.orange DIV.header {
	background-color: <?= diff_color(0xFFC47F) ?>;
}
.meeting DIV.header,
.violet DIV.header {
	background-color: <?= diff_color(0xC4BCD0) ?>;
}
.pink DIV.header {
	background-color: <?= diff_color(0xDCC4D1) ?>;
}
.dayh {
	margin-bottom: .3em;
}
.box DIV.header.smaller {
	font-size: 100% !important;
}
/*.uh {
	line-height: 1.5em
}*/
.box.location .header.fan {
	background-color: <?= diff_color(0xCCAACC) ?>;
}
.artist .header.fan {
	background-color: <?= diff_color(0xCCCCAA) ?>;
}
.stream .header.fan {
	background-color: <?= diff_color(0xAACCAA) ?>;
}
.organization .header.fan {
	background-color: <?= diff_color(0xAACCCC) ?>;
}
.box .header.going {
	background-color: <?= diff_color(0xAACCAA, 2) ?>;
}
.box .header.going.maybe {
	background-color: <?= diff_color(0xB5C5B5, 2) ?>;
}
.box .controls.header {
	width: 100%;
	margin: 0 0 .4em;
	padding: 2px;
}
.justify {
	text-align: justify;
}
.funcs {
	text-align: right;
}
.funcs {
	background-color: <?= $LITE ? 'rgba(0,0,0,.01)' : 'rgba(255,255,255,.02)' ?>;
}
DIV.funcs {
	width: 100%;
	margin: -.5em 0 .4em;
	padding: .2em .4em;
}
.funcs {
	background-color: <?= $LITE ? 'rgba(0,0,0,.03)' : 'rgba(255,255,255,.06)' ?>;
}

A.status-inactive,
A.status-permbanned,
A.status-banned,
.status-deleted {
	text-decoration: line-through !important;
}
A.status-inactive:hover,
A.status-permbanned:hover,
A.status-banned:hover,
.status-deleted:hover {
	text-decoration: line-through underline !important;
}
<?

$oldLITE = $LITE;

foreach ([false, !$LITE ? null : true] as $prefixed) {
	if ($prefixed === null) {
		continue;
	}
	$LITE = !$prefixed;
	$prefix = $prefixed ? '.for-dark ' : null;

	?>
	<?= $prefix ?>A.status-permbanned			{ color: <?= diff_color(0x880000) ?> !important; }
	<?= $prefix ?>A.status-permbanned:visited	{ color: <?= diff_color(0x992222) ?> !important; }
	<?= $prefix ?>A.status-permbanned:hover		{ color: <?= diff_color(0x880000) ?> !important; }
	<?= $prefix ?>A.status-banned				{ color: <?= diff_color(0x550000) ?> !important; }
	<?= $prefix ?>A.status-banned:visited		{ color: <?= diff_color(0x772222) ?> !important; }
	<?= $prefix ?>A.status-banned:hover			{ color: <?= diff_color(0x550000) ?> !important; }
	<?= $prefix ?>.status-deceased				{ color: <?= inverted_color(0xAAAAAA) ?> !important; }
	<?= $prefix ?>.status-deceased:hover		{ color: <?= inverted_color(0xAAAAAA) ?> !important; }
	<?= $prefix ?>.status-deleted				{ color: <?= diff_color(0x005500) ?> !important; }
	<?= $prefix ?>.status-deleted:visited		{ color: <?= diff_color(0x227722) ?> !important; }
	<?= $prefix ?>.status-deleted:hover			{ color: <?= diff_color(0x005500) ?> !important; }
<? }

$LITE = $oldLITE;

?>

.accept,
.user-won-contest {	background-color: <?= $LITE ? '#EFE' : '#060' ?>; }
.user-hilite-self {	background-color: <?= $LITE ? '#EEF' : '#006' ?>; }

.feedicon { width: 14px; height: 14px; }

.heart {
	color: <?= $LITE ? '#955' : '#F88' ?>;
	font-size: 80%;
	font-weight: bold;
	white-space: nowrap;
}
.heart IMG {
	transform: scale(.8);
	vertical-align: middle;
}
.shrt {
	width: 12px;
	height: 12px;
	margin-bottom: -1px;
}
IMG.mail {
	width: 16px;
	height: 12px;
}
.at,
.wb {
	margin: -1px 0 0 -2px;
	font-size: 10px;
	text-decoration: none;
}
.no-sync {
	<? $outline = $LITE ? '#A44' : '#A00' ?>
	text-shadow: -1px 0 <?= $outline ?>, 0 1px <?= $outline ?>, 1px 0 <?= $outline ?>, 0 -1px <?= $outline ?>;
}
.oglo,
.at {
	<? $outline = $LITE ? '#999' : '#555' ?>
	color: #EEE;
	text-shadow: -1px 0 <?= $outline ?>, 0 1px <?= $outline ?>, 1px 0 <?= $outline ?>, 0 -1px <?= $outline ?>;
}
.unseen.seemtext {
	border-radius: 2px;
	padding: .1em .5em;
	border: 1px solid <?= $LITE ? '#00000033' : '#FFFFFF33' ?>;
	background: <?= $LITE ? '#FFFFFF4D' : '#0000004D' ?>;
	transition: background-color .15s;
}
.unseen.seemtext:hover {
	background-color: <?= $LITE ? '#0000001A' : '#FFFFFF1A' ?>;
}

.subcnt {
	font-size: 80%;
	margin-left: .5em;
}

.online {
	<?	$LITE
	?	styles::linear_gradient('to right', '#FFFFC8E6', '#FFFFC800', '#FFC')
	:	styles::linear_gradient('to right', '#646400E6', '#64640000', '#994')
	?>
}

.online,
.nmsg.onln .header,
.nmsg.special .header,
.nmsg.mine .header,
.nmsg.as-admin .header {
	text-shadow: <?= $LITE
	? '#FFFFFFCC .1em .1em .1em'
	: '#000000CC .1em .1em .1em'
	?>;
}
.contest-joined				{ background-color: <?= diff_color (0xEEFFEE, 0, 0x20, 1.5) ?>; }
.contest-unfinished			{ background-color: <?= diff_color (0xFFEEEE, 0, 0x20) ?>; }
.contest-unfinished-joined	{ background-color: <?= diff_color (0xFFFFEE, 0, 0x20) ?>; }
.contest-nfnshd				{ background-color: <?= diff_color (0xF0EDEC, 0, 0x20) ?>; }
.contest-nfnshd-joined		{ background-color: <?= diff_color (0xFFFAEA, 0, 0x20) ?>; }
.l {
	float: left !important;
}
.easy-quote {
	overflow: hidden;
	display: block;
	border: 1px dotted <?= border_color(inverted_color(0x555555)) ?>;
	margin-left: 1em;
	margin-right: 1em;
	padding: .5em;
}
.quote.ib,
.easy-quote.ib {
	margin-bottom: .8em;
}
.test {
	border: 1px solid #FFFFFF26;
	border-left: 1px solid #FFFFFF12;
	border-top:  1px solid #FFFFFF12;
	border-radius: 2px;
}
.quote {
	display: inline-block;
	overflow: hidden;
	background-color: <?= $LITE ? '#FFFFFFB3' : '#000000B3' ?>;
	border: 1px solid <?= $LITE ? '#00000026' : '#FFFFFF26' ?>;
	border-left: 1px solid <?= $LITE ? '#00000012' : '#FFFFFF12' ?>;
	border-top:  1px solid <?= $LITE ? '#00000012' : '#FFFFFF12' ?>;
	border-radius: 2px;
	padding: .3em;
	margin-block-start: 0;
	margin-block-end: 0;
	margin-inline-start: 1em;
	margin-inline-end: 1em;
}
.quote,
.quote A,
.quote A:visited {
	color: <?= $LITE ? '#000000B3' : '#FFFFFFB3' ?>;
}
.quote.mine { <?
	styles::vendor_prefixed('box-shadow:'.
		make_shadow( 5,5,16,$LITE ? -7 : -6).($color_part = $LITE ? '#009600CC' : '#00C800CC').','.
		make_shadow(-5,5,16,$LITE ? -7 : -6).$color_part
	) ?>
}
.quote .info {
	background-color: <?= $LITE ? '#FFF' : '#000' ?>;
	border-bottom: 1px solid <?= inverted_color(0xDDDDDD) ?>;
	margin: 0 0 .5em;
}
.quote {
	padding: .5em;
}
.white.box .quote {
	background-color: <?= inverted_color(0xF4F4F4) ?>;
}

.center>TABLE,
.centered,
.centered>*,
#subbar > DIV > DIV {
	margin-left: auto;
	margin-right: auto;
}
#subbar {
	margin-bottom: .5em;
}
.rightaligned,
.right>TABLE {
	margin-left: auto;
}
.topic-sticky {				background-color: <?= diff_color(0xEEEEFF, 0, 0x25, 1.5) ?>; }
.topic-owner {				background-color: <?= diff_color(0xE0FFE0, 0, 0x25) ?>; }
.topic-owner-sticky {		background-color: <?= diff_color(0xE5E5FF, 0, 0x25, 1.5) ?>; }
.topic-poster {				background-color: <?= diff_color(0xEEFFEE, 0, 0x25) ?>; }
.topic-poster-sticky {		background-color: <?= diff_color(0xF0FAFA, 0, 0x25, 1.2) ?>; }
.forum TH.white {
	background-color: <?= inverted_color(0xFFFFFF) ?>;
}
.pay-status.irrecoverable,
.topic-hidden,
.topic-closed {
	color: red;
}
.topic-double {
	color: #FF8000;
}
.first-menu,
.page-controls {
	padding: 0;
	margin: 0 0 .5em;
	width: 100%;
}
.page-controls .pages {
	<? if (SMALL_SCREEN) { ?>
		line-height: 25px;
	<? } else { ?>
		/* line-height: 35px; */
	<? } ?>
}
.page-controls A,
.page-controls .choose {
	min-width: <?= SMALL_SCREEN ? 30 : 40 ?>px;
	background-color: <?= $LITE ? '#00000014' : '#FFFFFF26' ?>;
	display: inline-block;
	border-radius: 2px;
	height: calc(var(--line-height) * 18.6666px);
	/*line-height: 28px;*/
	padding: 0 10px;
	transition: background-color .15s;
}
.page-controls A:hover {
	background-color: <?= $LITE ? '#00000029' : '#FFFFFF40' ?>;
	text-decoration: none;
}
.page-controls .selected-page {
	color: <?= $LITE ? styles::FLOCK_YELLOW_DARK : styles::FLOCK_YELLOW ?>;
	text-decoration: none;
}
.fyel {
	color: <?= $LITE ? styles::FLOCK_YELLOW_DARK : styles::FLOCK_YELLOW ?> !important;
}
.page-controls .previous {
	width: 10%;
}
.page-controls .next {
	width: 10%;
	text-align: right;
}
.page-controls .pages {
	text-align: center;
}
.first-menu .selected-first,
.selected-page {
	text-decoration: underline;
}
.selected-page {
	background-color: <?= $LITE ? '#0000001A' : '#FFFFFF1A' ?>;
}
.age-bar,
.styles-bar {
	margin: 0;
	height: 1em;
	background-color: #0D0;
	border: 1px solid #0A0;
}

.buddycnt {
	color: <?= diff_color(0xCC5555) ?> !important;
}
.cancelled,
.error-nb,
.error-nb A  {
	color: <?= diff_color(0x800000) ?> !important;
}
FORM .required {
	color: <?= diff_color(0x008000) ?> !important;
	font-weight: bold;
}
.warror {
	color: <?= $LITE ? '#000' : '#FFAA1A' ?>;
}
.incfee,
TR.active A,
.postponed,
.notice-nb,
.notice-nb A,
.notice-nb A:visited {
	color: <?= $LITE ? '#070' : '#7F7' ?> !important;
}
.less-notice {
	color: <?= $LITE ? '#7F7' : '#0A0' ?> !important;
}
.erobg {
	padding: .25em .5em;
	border-radius: 2px;
	background-color: <?= $LITE ? '#FF646433' : '#FF64644D' ?>;
}
.tor {
	color: <?= diff_color(0x999900) ?> !important;
}
.tor A,
.tor A:visited {
	color: <?= diff_color(0x777700) ?> !important;
	font-style: italic;
}
.ns,
.ns * {
	font-style: normal !important;
}
.italic {
	font-style: italic;
}
.stopped,
.dead {
	font-weight: bold;
	color: <?= $LITE ? '#A70' : '#B80' ?>;
}
.photo-hidden,
.photo-hidden:active,
.connect-status {
	color: red;
}
.photo-hidden:hover {
		<? styles::vendor_prefixed('filter: brightness('.($LITE ? '70' : '130').'%)') ?>;
}
.photo-hidden:visited {
	color: <?= $LITE ? '#F00' : '#E00' ?>;
}
.party-day {
	position: sticky;
	top: <?= SMALL_SCREEN ? '2.3em' : '0' /* leave space for menu at top */ ?>;
	z-index: 5;
}
.party-day TD {
	background-color: #<?= $LITE ? 'EEE' : '222' ?>;
	padding: 1em 1em !important;
	overflow: hidden;
	font-size: 125%;
}
.lineup {
	background-color: <?= $LITE ? '#FFFFFF33' : '#00000033' ?>;
}
.lineup .times {
	white-space: nowrap;
}
PRE,
TEXTAREA,
.mono,
.tt {
	font-family: "Fira Code", "Courier New", monospace;
	font-size: 90%;
	line-height: 1.2;
}
.lineup .tt,
.lineup .times .weekday,
.lineup .times .dst-indicator {
	font-family: monospace;
}
.tabular,
.connectselect,
.lineup .times,
.importance,
.fixed-width-numbers {
	font-variant-numeric: tabular-nums;
	font-family: "Titillium Web", sans-serif;
}
.needsave .lineup {
	background-color: #FF80001A;
}
.lineupcont.ubb {
	margin-bottom: -.5em; <? /* lineup.il has .5 bottom margin, but strip it for the entire block, need it for multi-row lineups */ ?>
}
.ubb .lineup {
	background: transparent;
	margin-bottom: 0;
}
.lineup.form,
.lineup {
	background: inherit;
}
.lineup > TBODY > TR > TD,
.lineup > * > TR > TH {
	padding: .2em;
	background: <?= $LITE ? '#FFFFFF4D' : '#0000004D' ?>;
}
.lineup > * > TR > TH {
	padding: .2em .8em;
	background-color: <?= $LITE ? '#0000001A' : '#FFFFFF1A' ?>;
}
.lineup > TBODY > TR  {
	border-top:    1px solid <?= $LITE ? '#0000001D' : '#FFFFFF0D' ?>;
	border-bottom: 1px solid <?= $LITE ? '#0000001D' : '#FFFFFF0D' ?>;
}
.lineup .empty {
	background-color: <?= $LITE ? '#0000000D' : '#FFFFFF0D' ?>;
	height: .5em;
}
.lineup TD {
	margin: -.2em;
	padding: .2em .4em;
}
.lineup TD DIV:not(.findresults) {
	margin: 0 -.2em;
	padding: .2em .8em;
}
.lineup TD DIV.active-performance:last-child {
	margin-bottom: -.2em;
}
.lineup TD DIV.active-performance:first-child {
	margin-top: -.2em;
}
.lineup TH {
	background-color: <?= diff_color(0xD2D2DD) ?>;
}
UL.nostyle,
UL.nobullets,
UL.bulled,
UL.lineup {
	list-style-type: none;
	list-style-image: none;
}
UL.nostyle,
UL.nobullets,
UL.lineup {
	padding: 0;
	text-indent: 0;
}
UL.nostyle LI:before,
UL.nobullets LI:before,
UL.lineup LI:before {
	content: none;
	list-style-type: none;
	list-style-image: none;
}
DL.itempad > DD,
OL.itempad > LI,
UL.itempad > LI {
	margin-bottom: 1em;
}
DL.itempad > DD:last-child,
OL.itempad > LI:last-child,
UL.itempad > LI:last-child {
	margin-bottom: inherit;
}
UL.lineup {
	padding: 2px 2em 2px 0;
}
LI.area .name {
	text-decoration: underline;
}
LI.area.next {
	padding-top: .5em;
}
.party-info {
	background-color: <?= $LITE ? '#FFFFFF80' : '#00000080' ?>;
	padding: .8em 1em;
}
.party-info .maybe {
	background-color: <?= inverted_color(0xE3E3ED) ?> !important;
}
.forum {
	background-color: <?= inverted_color(0xF0F0F0) ?>;
}
.forum > TBODY > TR > TD:first-child,
.forum > * > TR > TH:first-child {
	border-left: 0 !important;
}
.forum > TBODY > TR > TD:last-child,
.forum > * > TR > TH:last-child {
	border-right: 0 !important;
}

TABLE.forum TD {
	vertical-align: top;
}

.forum .flock-cell,
.forum .user-cell,
.forum .msgcnt-cell,
.forum .date-cell {
	font-size: 80%;
	max-width: 9em;
	overflow: hidden;
	<?= styles::FORCE_WRAP ?>;
	white-space: nowrap;
}
.forum .flock-cell {
	max-width: 12em;
}
.forum .msgcnt-cell {
	max-width: inherit;
}
TBODY.unaccepted,
DIV.unaccepted,
TR.unaccepted TD,
TD.unaccepted,
.unaccepted-in-search {
	background-color: <?= $LITE ? '#DDD' : '#333' ?> !important;
	opacity: .6;
}
.favourite-artist,
.favourite-artist TD,
.connected,
.proc-customer {
	background-color: #00FF001A;
}
.proc-prio {
	background-color: #FFFF000D;
}
TR.unconnected,
.deny {
	background-color: <?= diff_color(0xFFEEEE) ?>;
}
TR.connected TD.connect-status {
	color: green;
}
.right-float TABLE {
	clear: both;
	padding: 0;
}
.uimg-cp {
	font-size: 80%;
	position: absolute;
	bottom: .2em;
	right: .2em;
}
.uimg-no-person {
	user-select: none;
	cursor: pointer;
	position: absolute;
	font-size: 150%;
	top: .2em;
	right: .4em;
	opacity: .3;
}
.uimg-no-person.isset {
	opacity: 1;
}
.uimg-cp.wide {
	width: calc(100% - 1em - 2em);
}
.acts {
	margin: .4em 0;
}
.ticket-old				{ color: <?= diff_color(0x800000) ?>;
}
.ticket-older			{ color: <?= diff_color(0xA00000) ?>;
}
.ticket-oldest			{ color: <?= diff_color(0xC00000) ?>;
}
.ticket-elder			{ color: <?= diff_color(0xE00000) ?>;
}
.ticket-ancient			{ color: <?= diff_color(0xFF0000) ?>;
}
.ticket-locked-by-me	{ background-color: <?= inverted_color(0xF8F8EA) ?>;
}
<? if (SMALL_SCREEN) { ?>
.ticket-subject {
	opacity: .6;
	background-color: <?= !$LITE ? '#FFFFFF0D' : '#0000000D' ?>;
}
<? } ?>
.msg-cnt,
.msg-type			{ width:2em;
}
.wd30,
.ticket-subject		{ width:30%;
}
.default TR,
.default.brdgrp TBODY,
.default TBODY.brdgrp {
	border-bottom: 1px solid <?= $LITE ? '#00000010' : '#FFFFFF10' ?>;
}
.default.brdgrp TBODY TR,
.default.brdgrp TBODY TD,
.default TBODY.brdgrp TR,
.default TBODY.brdgrp TD {
	border: 0;
}
.default TH.date {
	padding: .2em .4em;
	background-color: <?= $LITE ? '#0000001A' : '#FFFFFF1A' ?>;
}
.default TH,
.default TD {
	padding: .2em .4em;
}
.inforow		{ padding-top: 1em;
}
.separator		{ border: 0 !important; height: .7em;
}
.default:not(.allborders) > TBODY > TR:last-child { border-bottom: 0 !important;
}
.default TD.admin	{ background-color: <?= inverted_color(0xFFFFFF) ?>;
}
.default.bordered TD	{ border: 1px solid #0000000D; }

.smallscreen .deflist {
	width: 100%;
}
.deflist > TBODY > TR.self,
.deflist > TBODY > TR.admin,
.deflist > TBODY > TR.light {
	opacity: .5;
}
.fw.deflist > TBODY > TR > TD:last-child,
.smallscreen .deflist > TBODY > TR > TD:last-child {
	width: 100%;
}
.deflist > TBODY > TR > TD {
	background-color: <?= $LITE ? '#00000006' : '#FFFFFF0D' ?>;
}
.deflist > TBODY > TR > TD.field {
	background-color: <?= $LITE ? '#0000000D' : '#FFFFFF1A' ?>;
}
.deflist > TBODY > TR.clean TD {
	background-color: transparent;
}
.fw.deflist > TBODY > TR > TD:last-child {
	width: 100%;
}
.deflist {
	border-collapse: collapse;
}
.deflist > TBODY > TR > TD {
	border: 2px solid transparent;
	padding: .2em .6em;
	border-radius: 2px;
}
.dens,
.dens > TBODY > TR > TD	{ padding: 0;
}
.fw,
.fifty {
	width: 100%;
}
.fh {
	height: 100%;
}
.bordered TD,
.bordered TH {
	border: 1px solid <?= $LITE ? '#00000012' : '#FFFFFF12' ?>;
}
.padded TD {
	padding: .1em;
}
.defpad TD,
.defpad TH {
	padding: .2em .4em;
}
.vbottom,
.vbottom TD {
	vertical-align: bottom;
}
.vttop,
.vttop TD,
IMG.sub {
	vertical-align: text-top;
}
TABLE.userimagetable TD,
.infos TD,
.vtop,
.vtop > TBODY > TR > TD,
.vtop > * > TR > TH {
	vertical-align: top;
}
.fifty > TBODY > TR > TD {
	width: 50%;
}
.permbanned,
.banned {
	color: <?= diff_color(0xAA0000) ?>;
}
TD.infavor,
.free-entrance,
.ticket-open,
.ticket-open:visited {		color: <?= diff_color(0x008800) ?>;
}
.ticket-delayed,
.ticket-delayed:visited {	color: <?= $LITE ? '#960' : '#D80' ?>;
}

.cost-free {
	color: <?= diff_color(0x005500) ?>;
}

TD.against,
.ticket-closed,
.ticket-closed:visited	{ color: <?= diff_color(0x880000) ?>; }
.ticket-pending,
.ticket-pending:visited,
.ticket-pending A		{ color: <?= diff_color(0x888800) ?>; }
.pending,
.pending A				{ color: <?= diff_color(0xAAAA00) ?>; }
.dark.pending,
.dark.pending A			{ color: <?= diff_color(0x666600) ?>; }
.compo-closed			{ color: <?= diff_color(0x0000AA) ?>; }
.compo-open				{ color: <?= diff_color(0x00AA00) ?>; }
IMG.compo-chosen		{ border: 5px dashed <?= diff_color(0x00BB00) ?>; }
A.compo-chosen			{ color: <?= diff_color(0x00AA00) ?>; }
A.compo-chosen:visited	{ color: <?= diff_color(0x006600) ?>; }
.compo-disabled-chosen	{ color: <?= diff_color(0x00AA00) ?>; text-decoration: line-through; }
SPAN.help {
	border-bottom: 1px dashed <?= inverted_color(0x999999) ?>;
}
.helpc,
SPAN.help,
.help-cursor {
	cursor: help !important;
}
.spoiler {
	outline: 1px dotted #FF8080CC;
	cursor: help;
	color: red;
}
SPAN.currentusernick {
	border-bottom: 1px dashed <?= inverted_color(0x55AA55) ?>;
}
.invisible,
.hlay {
	visibility: hidden;
}
.hidden,
.tmphidden TR.tmphidden,
.tmphidden TD.tmphidden,
.tmphidden TH.tmphidden {
	display: none !important;
}
.unhidelink {
	border-bottom: 1px dotted <?= inverted_color(0x9999FF) ?>;
	cursor: pointer;
}
.overline {
	text-decoration: overline;
}
TR.bottom {
	height: 0;
}
#chatusers,
#chatlines {
	border: 1px solid <?= inverted_color(0xAAAAAA) ?>;
}
#chatlines {
	padding-right: 1px;
}
#flarp,
#floerp {
	margin: .2em;
}
.backptr {
	left: -10px;
	top: -4px;
}
.backptr:hover {
	<? styles::vendor_prefixed('filter: drop-shadow(0px 0px 2px #00000080)') ?>;
	<? styles::vendor_prefixed('transform: translateZ(0)') ?>;
}
.connectbox {
	position: absolute;
	z-index: 500;
	width: 60em;
	height: 400px;
	border: 1px solid <?= $LITE ? '#0000004D' : '#FFFFFF4D' ?>;
	border-radius: 2px;
}
.connectbox-back {
	z-index: 501;
}
.connectboxbody {
	padding: 1em;
	vertical-align: middle;
	overflow-y: hidden;
}
.connectboxbody.inline-body {
	padding: 0;
	overflow-y: scroll;
}
DL.criteria {
	margin: 0 1em .5em;
}
.box TR.running {
	background-color: <?= diff_color(0xFFFFDD) ?>;
}
.pay-status.notpaid {
	color: #FF8000;
}
.pay-status.paid {
	color: green;
}
TABLE.photo-comment {
	margin: 0;
	border-bottom: 1px dotted <?= inverted_color(0x777777) ?>;
}
TABLE.photo-comment TD {
	padding: .5em 0;
}
TABLE.photo-comment TD.photo {
	padding: .5em 1em .5em 0;
}
.no-access		{ background-color: <?= diff_color(0xFF5555) ?>; }
.read-access	{ background-color: <?= diff_color(0xFFA050) ?>; }
.normal-access	{ background-color: <?= diff_color(0xEEEEEE) ?>; }

<? /* used by tagger! */ ?>
.bottom-dashed {
	border-bottom: 1px dashed grey;
}
.lighter-hilited {
	background-color: rgba(<?= $LITE ? '255,255,190,.3' : '255,255,0,.15' ?>);
}
.active-performance {
	font-weight: bold;
	background-color: rgba(<?= $LITE ? '255,255,100,.3' : '255,255,255,.1' ?>);
}
.active-performance.favourite-artist {
	background-color: rgba(<?= $LITE ? '0,255,0,.3' : '200,255,200,.2' ?>);
}

.ahili,
.light-hilited,
.hhl,
.hhla > TBODY > TR:not(.nohl) {
	transition: background-color .15s ease-in;
}
.ahili,
.light-hilited,
.hhl:hover,
.hhla > TBODY > TR:not(.nohl):not(.hilited-red):not(.hilited-green):hover {
	background-color: rgba(<?= $LITE ? '255, 255, 190' : '81, 81, 20' ?>, .7);
}
.bold-hilited {
	border-radius: 2px;
	background-color: rgba(<?= $LITE ? '255,255,150,.8' : '180,180,0,.5' ?>) !important;
}
.hilited-artist,
.hilited {
	background-color: rgba(<?= $LITE ? '255,255,200,.8' : '180,180,0,.4' ?>) !important;
}
.immediate {
	transition-property: none !important;
}
.hha > TBODY > TR:not(.nohl),
.hh {
	transition: background .15s ease-in;
}
.hha > TBODY > TR:not(.nohl):not(.hilited-red):hover,
.hh:hover {
	background-color: rgba(<?= $LITE ? '255, 255, 190' : '81, 81, 20' ?>, .9) !important;
}
.hha > TBODY > TR.zebra:not(.nohl):not(.hilited-red):hover {
	background-color: rgba(<?= $LITE ? '255,255,80' : '110,110,20' ?>, .9) !important;
}
<? require_once '../defines/zoomover.inc' ?>
.zoomover {
	transition: transform .07s ease-in;
}
.zoomover:hover {
	<? styles::vendor_prefixed('transform:scale('.get_corrected_zoomover_scale().')') ?>
}
.superzoom {
	transition: transform .5s ease-in, opacity .5s ease-in;
}
.superzoom.now {
	<? styles::vendor_prefixed('transform:scale(200) !important') ?>;
	opacity: 0;
}
.hla > TBODY > TR:not(.nohl):not(.hilited-red):hover,
.hl:hover,
.lbg {
	background-color: rgba(<?= styles::RGB_WHITE, $LITE ? ', .6' : ', .15' ?>) !important;
}
.hla > TBODY > TR:not(.nohl),
.lbg,
.hl {
	transition: background-color .15s ease-in;
}
.smallscreen .partylist .hl:hover {
	background-color: rgba(<?= !$LITE ? styles::RGB_WHITE : styles::RGB_BLACK ?>, .07) !important;
}
.partylist TBODY {
	border: 1px solid rgba(<?= $LITE ? styles::RGB_BLACK : styles::RGB_WHITE ?>, .05);
	border-left: 0;
	border-right: 0;
}
.light-hilited.hl:hover {
	background-color: rgba(255, 255, 230, <?= $LITE ? .8 : .3 ?>);
}
.double-hilited {
	background-color: #<?= $LITE ? 'FF5' : '660' ?> !important;
}
.bold-hilited-red,
.hilited-loud-cancelled {
	background-color: <?= diff_color(0xFFAAAA) ?> !important;
}
.slowlite {
	transition: background-color .2s ease-in;
}
.admin-access,
.bold-hilited-green,
.hilited-loud-green,
.hilited-loud-postponed {
	background-color: rgba(0, 255, 0, .5);
}
.hilited-green {
	background-color: <?= diff_color(0xDDFFDD) ?> !important;
}
.bold-hilited-orange,
.hilited-orange {
	background-color: rgba(255, 128, 0, .6) !important;
}
.deleted {
	color: #F00 !important;
}
.hilited-loud-deleted,
.hilited-loud-purple {
	background-color: <?= diff_color(0xFFAAFF) ?> !important;
}
.hilited-purple {
	background-color: rgba(255,0,255,.1) !important;
}
.hilited-red {
	background-color: <?= diff_color(0xFFDDDD) ?> !important;
}
.bold-hilited-yellow,
.hilited-yellow {
	background-color: rgba(255, 255, 0, .5) !important;
}
.hilited-white {
	background-color: rgba(<?= $LITE ? '0,0,0,.6' : '255,255,255,.3' ?>) !important;
}
<? /* used by tagger!! */  ?>
<? /* .hilited-purple, */ ?>
.hilited-location {
	background-color: <?= $LITE ? '#FCF' : '#606' ?>;
}
<? /* .hilited-cyan, */ ?>
.bold-hilited-cyan,
.hilited-organization {
	background-color: <?= $LITE ? '#CFF' : '#066' ?>;
}
.hilited-light-orange {
	background-color: rgba(255,150,0,<?= $LITE ? .1 : .2 ?>);
}
<? /* .hilited-blue, */ ?>
.hilited-party {
	background-color: <?= $LITE ? '#CCF' : '#006' ?>;
}
<? /* .hilited-grey,
.hilited-city {
	background-color: <?= dcolor(0xEEEEEE) ?>
} */ ?>
<? /*********/ ?>
.double-hilited-green {
	background-color: <?= diff_color(0xDDEEAA) ?> !important;
}
.invalid {
	color: <?= diff_color(0xAA0000) ?>;
}
.invalid A {
	color: <?= diff_color(0x990000) ?>;
}
.invalid A:visited {
	color: <?= diff_color(0xBB0000) ?>;
}
.server-ok {
	color: #008000;
}
.server-not-ok {
	color: #FF0000;
	font-weight: bold;
}
.server-warn {
	color: #EE8000;
}
TABLE.split {
	border: 0;
	width: 100%;
}
TABLE.split > TBODY > TR > TD {
	border: 0;
	padding: 0;
	background-color: transparent;
}
.tt {
	white-space: pre;
}
.tt.bordered {
	border: 1px solid #808080;
	padding: .25em;
}
UL.nostyle {
	list-style-image: none;
}
.nostyle,
.bulled,
TABLE.split {
	padding: 0;
	margin: 0;
}
.mfav:hover {
	color: <?= $LITE ? '#000' : '#FFF' ?>;
	text-decoration: none;
}
.mfav.mfav-artist,
.mfav.mfav-artist:visited {
	color: <?= diff_color(0xAAAA00) ?>;
}
.mfav.mfav-organization,
.mfav.mfav-organization:visited {
	color: <?= diff_color(0x00AAAA) ?>;
}
.mfav.mfav-location,
.mfav.mfav-location:visited {
	color: <?= diff_color(0xAA00AA) ?>;
}
.lazyload > .loading {
	border: 1px solid rgba(<?= $LITE ? styles::RGB_BLACK : styles::RGB_WHITE ?>,.1);
	background-color: rgba(<?= $LITE ? styles::RGB_BLACK : styles::RGB_WHITE ?>,.03);
	height: 100%;
}
.photo-box.nok {
	outline: 2px dashed #F00;
	opacity: .5;
}
.photo-box.nok IMG {
	<? styles::vendor_prefixed('filter:blur(2px)') ?>;
}
.photo-box.hid {
	margin: <?= FIREFOX ? '1px 1px 3px 1px' : '1px' ?>;
	border: 2px dashed <?= diff_color(0x0000CC) ?>;
	opacity: .5;
}
.photo-box {
	padding: 2px;
	display: inline-block;
	position: relative;
}
.photo-box IMG {
	display: block;
}
.rarr {
	width: 59px;
	height: 51px;
	margin-left: -64px;
	font-size: 300%;
}
.darr {
	margin-left: 10px;
	width: 51px;
	height: 59px;
	font-size: 300%;
}
.rvbg.negmrgn {
	margin-right: -10px;
}
.rvbg,
.ovbg,
.dims,
.fallback {
	color: #FFF;
	background-color: rgba(<?= styles::RGB_BLACK ?>, .7);
	border-radius: 4px;
	z-index: 2;
	padding: 2px 10px;
}
#multifb {
	z-index: 3;
}
.subinfo {
	padding: 0;
	overflow: hidden;
	background-color: rgba(<?= $LITE ? styles::RGB_BLACK : styles::RGB_WHITE ?>, .05);
	border-top: 8px solid transparent;
	border-bottom: 5px solid transparent;
}
.vidgrp .subinfo {
	width: <?= VIDEOTHUMB_WIDTH ?>px;
}
.subinfo .tworows {
	height: 4em;
	display: inline-block;
}
.vidgrp .subinfo .rows {
	width: 100%;
	max-height: 5em;
	display: inline-block;
}
.seen {
	color: <?= $LITE ? '#CACACA' : '#444' ?>;
	right: .5em;
	bottom: .5em;
}
.seenlight {
	opacity: .6;
}
<? if (!$LITE) { ?>
.darkinv {
	color: #000;
	background-color: rgba(<?= styles::RGB_WHITE ?>, .7);
}
<? } ?>
.bannerbg {
	padding: .2em;
	border-radius: 2px;
	margin: -.5em -.5em .4em -.5em;
	background-color: rgba(<?= $LITE ? styles::RGB_BLACK : styles::RGB_WHITE ?>, .1);
}
.ov,
.ovbg,
.dims,
.fallback {
	position: absolute;
}
.rndbrd IMG,
.rndbrd .loading {
	border-radius: 2.5px;
}
.rv A,
.rvbg A,
<? if ($LITE) { ?>
	.rvbg A:hover,
<? } ?>
.ov A,
.ovbg A,
.hlbg,
.hlbg A {
	color: #FFF !important;
}
.hlbg {
	background-color: rgba(<?= $LITE ? styles::RGB_BLACK.', .1' : styles::RGB_WHITE.', .05' ?>);
	text-shadow: 1px 1px rgba(0,0,0,.8);
}
.ov { <?
	$outline = '000'; $inline = 'FFF';
	?>color: #<?= $inline ?>;<?
	?>text-shadow: -1px 0 #<?= $outline ?>,0 1px #<?= $outline ?>,1px 0 #<?= $outline ?>,0 -1px #<?= $outline ?>;
}
TH {
	text-align: left;
}
.left {
	text-align: left !important;
}
#bottom,
.center {
 	text-align: center !important;
}
#bottom {
	clear: both;
	margin-bottom: 1em;
}

.note,
.right {
	text-align: right !important;
}

.frotc IMG[width="0"] {
	display: none;
}
.blackbox {
	background-color: <?= $LITE ? '#444' : '#EEE' ?>;
	border-radius: 0 0 2px 2px;
	color: <?= $LITE ? '#FFF' : '#000' ?>;
	margin: 0;
	padding: .2em;
	text-align: center;
	text-shadow: 1px 1px #<?= $LITE ? '000' : 'FFF' ?>;
}
.udv.blackbox {
	font-size: 75%;
}
.hdr .title {
	font-size: 120%;
	display: inline-block;
}
.hdr .info {
	float: right;
	padding-top: .2em;
	font-size: 1em;
}

.udv.blackbox {
	<? $multiplier = 1.25 ?>
	margin: -<?= .4 * $multiplier ?>em -<?= .4 * $multiplier ?>em <?= .2 * $multiplier ?>em -<?= .4 * $multiplier ?>em;
}
.blackbox A,
.blackbox A:visited,
.blackbox A:hover {
	color: <?= $LITE ? '#FFF' : '#333' ?>;
}
.line-through,
.line-through TD,
.line-through A,
.line-through A:visited,
.line-through A:active {
	text-decoration: line-through !important;
}
.line-through A:hover {
	text-decoration: line-through underline !important;
}
.hr {
	width: 100%;
	display: block;
	border-bottom: 1px solid <?= inverted_color(0x888888) ?>;
	margin: .3em 0 .5em;
}
HR.ubb {
	margin: .8em 0 -.4em 0;
}
.progresscontainer {
	width: 100%;
	border: 1px solid <?= $LITE ? '#030' : '#0B0' ?>;
}
.progressbar {
	width: 0;
	height: 2em;
	background-color: <?= $LITE ? '#050' : '#090' ?>;
}
.right-float {
	margin-left: 1em;
}
.right-float,
.r {
	float: right !important;
}
.threestars {
	width: 40px;
}
.cookie-consent,
.top-message {
	font-size: .9em;
	text-align: center;
	padding: .3em;
	color: white;
	text-shadow: 1px 1px 1px black;
	font-weight: 600;
}
.top-win {
	border-bottom: 1px solid <?= $LITE ? '#FF8' : '#770' ?>;
	background-color: <?= $LITE ? '#FF5' : '#990' ?>;
}
.top-error {
	border-bottom: 1px solid <?= $LITE ? '#F88' : '#900' ?>;
	background-color: <?= $LITE ? '#F55' : '#B00' ?>;
}
.top-warning {
	border-bottom: 1px solid <?= $LITE ? '#D90' : '#830' ?>;
	background-color: <?= $LITE ? '#FB0' : '#A50' ?>;
}
.top-notice {
	border-bottom: 1px solid <?= $LITE ? '#2E2' : '#060' ?>;
	background-color: <?= $LITE ? '#5F5' : '#080' ?>;
}
.cookie-consent,
.top-blue {
	border-bottom: 1px solid <?= $LITE ? '#88F' : '#116' ?>;
	background-color: <?= $LITE ? '#55F' : '#229' ?>;
}
.cookie-consent {
	text-align: left;
	padding: 1em;
}
UL.meetings LI {
	border-bottom: 1px solid <?= inverted_color(0xBBBBBB) ?>;
	margin-bottom: 1em;
}
.photo-opt {
	border: 1px solid <?= inverted_color(0xCCCCCC) ?>;
	padding: .1em 1em;
	cursor: pointer;
}
.male {
	color: #41AAC1;
}
.female {
	color: #CC7979;
}
.photo-opt {
	color: inherit;
}
.photo-opt.male {
	background-color: <?= diff_color(0xDDEEFF) ?>;
}
.photo-opt.female {
	background-color: <?= diff_color(0xFFDDEE) ?>;
}
.ubbheader {
	margin: 0 !important;
}
.inln.header.ubbheader {
	margin: 0 0 5px 0 !important;
}
.ngmrgn { margin-bottom: <?= -1 + .4 ?>em !important;
}
TABLE.votes,
.nomargin { margin-bottom: 0 !important;
}
.ratings .date,
TABLE.rpadded > TBODY > TR > TD,
.hpad,
.rpad { padding-right: .4em !important;
}
.lrpad { padding-right: 1em !important;
}
.srpad { padding-right: .2em;
}
.hpad,
.lpad { padding-left: .4em !important;
}
.headsep > TD,
.tpad { padding-top: .4em !important;
}
.bpad { padding-bottom: .4em !important;
}
.llpad { padding-left: 1em !important;
}
.rrpad { padding-right: 1em !important;
}
.lmrgn { margin-left: .4em !important;
}
.bmrgn { margin-bottom: .4em !important;
}
.rmrgn { margin-right: .4em !important;
}
.rmrgn2 { margin-right: .8em;
}
.tmrgn { margin-top: .4em !important;
}
.ttmrgn { margin-top: 3em !important;
}
.srmrgn { margin-right: .2em;
}
.slmrgn { margin-left: .2em;
}
.shpad > TBODY > TR > TD,
.shpad > THEAD > TR > TH {
	padding-left: 2em;
	padding-right: 2em;
}
.default .presencerow > TD,
.presencerow A {
	padding: .2em;
}
/*.deflist .presencerow {
	padding: inherit .2em;
}*/
.presencerow A:first-child {
	padding-left: 0;
}
.presencerow A:last-child {
	padding-right: 0;
}
.topicrow > TD:first-child,
.body,
.forcewrap {
	<?= styles::FORCE_WRAP ?>;
	white-space: normal !important;
}
.field,
.nowrap,
.ov,
.ovm *,
.ratings .date,
.smallest,
.u-cell {
	white-space: nowrap;
}
TABLE.dowrap .field,
.dowrap,
.field.wrap {
	white-space: normal;
}
.cmbr {
	height: .5em;
	width: 1px;
}

.emoji {
	font-family: "Apple Color Emoji", "Noto Color Emoji", "Segoe UI Emoji", emoji;
}
.cflg {
	font-family: "Apple Color Emoji", "Twemoji Country Flags", emoji;
	font-size: 150%;
	line-height: 50%;
	vertical-align: -25%;
}
.countryflag {
	height: 14px;
	border-radius: 1px;
	vertical-align: -1px;
	border: 1px solid black;
}
.menublock .countryflag {
	border: 0;
	vertical-align: top;
}
.r > .langlink {
	vertical-align:middle
	<? if (SMALL_SCREEN) { ?>;margin-left:1em <? } ?>;
}
.siteimg {
	display: inline-block;
	position: relative;
	width: 22px;
	height: 1em;
	top: <?= SMALL_SCREEN ? '-4px' : '0' ?>;
}
.siteimg > IMG {
	<? if (!$LITE) { ?>
		opacity: .9;
	<? } ?>
	width: 22px;
	height: 22px;
	position: absolute;
}
IMG.sub,
.icon,
.hrt,
.cam,
.presence {
	width: 16px;
	height: 16px;
}

.cam-icon {
	display: inline-block;
	height: 1em;
	width: 1.5em;
}
.cam-icon > SPAN {
	font-size: 150%;
	position: absolute;
	margin-top: -.2em;
}

.lower,
.cam,
.hrt,
.presence {
	vertical-align: -2px;
}
.smallscreen .presence {
	margin-right: .5em;
}
.lowert {
	vertical-align: .1em;
}
.wide.icon {
	width: 20px;
}
.iconw {
	width: 16px;
}
.presence {
	display: inline-block;
}
.videothumb {
	width: <?= VIDEOTHUMB_WIDTH ?>px;
	height: <?= VIDEOTHUMB_WIDTH * 9 / 16 ?>px;
	border-radius: 2px;
	<? if (FIREFOX) { ?>
		-moz-background-size: <?= VIDEOTHUMB_BG_WIDTH ?>px;
	<? } ?>
	background-size: <?= VIDEOTHUMB_BG_WIDTH  ?>px;
	display: inline-block;
}
.viddate {
	position: absolute;
	top: -3px;
	right: -2em;
	font-size: 70%;
	text-shadow: 1px 1px 1px black;
	opacity: .7;
}
.fixed {
	position: fixed;
}
.c-over,
.abs,
.xie {
	position: absolute;
}
.xie {
	width: .1px;
}
<? if (!SMALL_SCREEN) { ?>
.tracklist {
	display: inline-block;
	width: 50%;
}
<? } ?>
.tracklist LI {
	padding: .1em 0;
	border-bottom: 1px solid rgba(<?= $LITE ? styles::RGB_BLACK : styles::RGB_WHITE ?>,.1);
}
.unlabelled {
	margin: 0 .3em .5em !important;
}
.labelled {
	margin: 0 .3em 1.4em !important;
}
.labelled .label {
	z-index: 2;
	width: 100%;
	bottom: -1.2em;
}
.lz {
	line-height: 0;
}
.lz * {
	line-height: normal;
}
.globox {
	border-radius: 1.5px;
	background-color: <?= $LITE ? '#EEE' : '#222' ?>;
}
.glo,
.hlbox:hover {
	<? styles::vendor_prefixed('box-shadow:'.make_shadow(0,0,10).'rgba('.($LITE ? styles::RGB_BLACK : styles::RGB_WHITE).','.($LITE ? .5 : .7 ).')') ?>
}
.hlbox {
	transition: filter .30s;
}
.hlbox:hover {
	<? styles::vendor_prefixed('box-shadow: inherit') ?>;
	filter: brightness(<?= $LITE ? '110' : '130' ?>%);
}
.closebutton {
	width: 30px;
	height: 30px;
	cursor: pointer;
	position: absolute;
	top: -16px;
	right: -16px;
	z-index: 40;
}
.ratings TH {
	padding: .5em 0;
}
.ratings .txt {
	<?= styles::FORCE_WRAP ?>
}
.bulled > LI:before {
	content: "\2022\2002";
}
.il {
	display: inline-block;
	margin-bottom: 0 !important;
}
.lineup.il {
	margin-bottom: .5em !important;
}
.ilfake {
	margin-bottom: -1.4em !important;
}
.as-block {
	display: block !important;
}
.inln {
	display: inline;
}
.ib,
.smil,
.subj .title,
.video-title,
.video-title DIV {
	display: inline-block;
}
.it {
	display: inline-table;
	margin: 0;
}
.large.icon {
	width: 32px;
	height: 32px;
}
.c-over {
	right: -1px;
	padding:5px;
}
.c-over.contact-toadmin {
	background-color: <?= diff_color(0xFAFAF4) ?>;
}
.c-over.contact-touser {
	background-color: <?= inverted_color(0xFAFAFA) ?>;
}
.secretpart {
	<? styles::vendor_prefixed('mask-image: linear-gradient(to bottom, rgba(0,0,0,1) 90%, rgba(0,0,0,0))') ?>;
	max-height: 20em;
	overflow-y: auto;
	padding: 0 1em 1em 0;
	margin-top:-.2em;
}
.mxlh {
	max-height: 1.2em;
}
.to-user,
.to-end {
	width: 15px;
	height: 16px;
	vertical-align: middle;
	opacity: <?= $LITE ? '.3' : '.7' ?>;
	transition: opacity .15s;
}
.to-user:hover,
.to-end:hover {
	opacity: <?= $LITE ? .6 : 1?>;
}
.to-user {
	width: 16px;
	vertical-align: -3px;
	margin: 0 4px;
}
.oneline {
	height: 1.2em;
	overflow: hidden;
}
.noverflow {
	overflow: hidden !important;
}
.ovisi,
.ovisi .box-column {
	overflow: visible !important;
}
.inside {
	width: 60px;
	height: 55px;
}
.insidehzw,
.insidehz {
	width: 68px;
}
.insidehz {
	height: 24px;
}
.pubinfo {
	<? if (!SMALL_SCREEN) { ?>
		float: left;
		padding-left: 2em;
	<? } else { ?>
		text-align: center;
	<? } ?>
	opacity: .6;
}
.minp:before {
	content: "";
	width: 10em;
	display: block;
	overflow: hidden;
}
.tdir {
	height: 32px;
	width: 32px;
	margin-top: -22px;
	margin-left: 5px;
}
.tcom {
	height: 1em;
	width: 1em;
}
.nopac,
.langs .selected IMG {
	opacity: 1 !important;
}
.smallest {
	width: 1px;
}
.star {
	vertical-align: -1px;
	width: 14px;
	height: 14px;
}
.bshd {
	filter: drop-shadow(.5px .5px .5px #000);
	transform: translateZ(0);
}
.bshdb {
	filter: drop-shadow(0 0 1px #000);
	transform: translateZ(0);
}
.star {
	filter: drop-shadow(1px 1px 1px #0000007F);
	transform: translateZ(0);
}
.extrapad > TBODY > TR:last-child > TD {
	padding-bottom: .5em;
}
.hpadded > TBODY > TR > TD,
.hpadded > * > TR > TH,
.nocellbrd > TBODY > TR > TD:first-child,
.nocellbrd > TBODY > TR > TD:last-child {
	padding-left: .6em;
	padding-right: .6em;
}
.hpadded.between > TBODY > TR > TD:first-child {
	padding-left: inherit;
}
.hpadded.between > TBODY > TR > TD:last-child {
	padding-right: inherit;
}
.elpad > TBODY > TR > TD:not(:first-child) {
	padding-left: 1em;
}
.nocellbrd > TBODY > TR > TD {
	border-left: 0 !important;
	border-right: 0 !important;
}
.visible,
.hovisi:hover .invisible {
	visibility: visible;
}
.livisi:hover .light {
	opacity: 1;
}
.hlay {
	display: block !important;
	position: absolute;
}
<?
ob_start();
# FIXME: Keep these o single line! with_prefix function can then easily prefix them.
#		 Though, it seems with_prefix is only used for dyntab, we can in the future kjust do away with with_prefix.
?>> TBODY > TR {					position: relative; }
> TBODY,
> TBODY > TR,
> TBODY > TR > TD {					display: block;	width: 100%; }
> TBODY > TR > TD {					margin-bottom: .4em; }
> TBODY > TR > TD:empty {			margin-top: -.5em; }
> TBODY > TR > TD.field	{			font-weight: bold; background-color: <?= !$LITE ? '#FFFFFF0C' : '#0000000C' ?>;	padding: .2em .4em; }
.deflist > TBODY > TR > TD.field {	background-color: inherit; padding: inherit; }
> TBODY > TR > TD:first-child	{	font-weight: bold; margin-bottom: 0; }
> TBODY > TR > TD.right {			text-align: inherit !important; padding: 0 !important;  width: auto; }
#userform TBODY > TR > TD.right	{	position: absolute;	right: 0; top: 0; }<?

$base_table = explode("\n", ob_get_clean());

$with_prefix = static function(string $prefix) use ($base_table): void {
	foreach ($base_table as $line) {
		if (!mytrim($line)) {
			continue;
		}
		if (preg_match('"^(#\w+)\s+(.*)$"',$line,$match)) {
			echo $match[1], ' ', $prefix, ' ', $match[2];
		} else {
			echo $prefix, $line;
		}
	}
};

?>
@media all and (max-width: 1700px) {
	.col,
	.box-column {
		width: 100% !important;
	}
	.shrink {
		height: auto !important;
		padding: 0 !important;
	}
	.agenda .col,
	.agenda .box-column {
		width: 100% !important;
	}
	.shid1000 {
		display: none;
	}
} /* endmedia */

@media all and (max-width: 500px) {
	.hide-on-small {
		display: none;
	}
	.dyntab {
		display: block;
	}
	<? $with_prefix('.dyntab'); ?>
} /* endmedia */

@media print {
	HTML {
		font-size: 12pt;
		transform: scale(.7);
	}
} /* endmedia */

FIELDSET {
	background-color: <?= $LITE ? '#FFFFFF4C' : '#0000004C' ?>;
	border: 1px solid <?= $LITE ? '#00000019' : '#FFFFFF19' ?>;
	border-left: 0;
 	border-right: 0;
 	padding: .4em;
}
.partyprice.needstrip {
	background-color: #FFA50033;
}
.partyprice .strip {
	display: block;
	margin: -.4em -.4em .2em -.4em;
	padding: .2em .8em;
	background-color: rgba(<?= $LITE ? styles::RGB_WHITE : styles::RGB_BLACK ?>,.5);
}
.party-box {
	margin: .8em 0 .8em 0;
}
.party-box LEGEND {
	font-size: 75%;
}
.party-box TABLE {
	margin: 0 .4em .4em .4em;
}
.indent {
	text-indent: 1em;
}

/********************************************** close char, remove char, recycle char *********************/

.spc > DIV {
	color: transparent;
}
.spc > SPAN {
	margin-left: .5em;
}
.rtop {
	position: absolute;
	right: -1em;
	top: -1em;
	z-index: 40;
}
.large.rtop {
	right: -16px;
	top: -16px;
}
.medium.rtop {
	right: -12px;
	top: -12px;
}

/************************************************************************************************************/


.smil {
	height: 1px;
	position: relative;
}
.middle .smil {
	vertical-align: baseline;
}
.pollrow .wrapper {
	position: relative;
}
.pollrow .bar {
	margin: 0;
	background-color: rgba(255, 0, 0 ,<?= $LITE ? .2 : .4 ?>);
	border: 1px solid rgba(255, 0, 0, .2);
	position: absolute;
	left: 0;
	bottom: 1px;
	top: 1px;
	height: auto;
}
.pollrow .ans {
	width: 100%;
}
.pollrow .desc {
	padding: .2em .5em;
	position: relative;
}
.nws {
	background-color: rgba(<?= $LITE ? styles::RGB_WHITE : styles::RGB_BLACK ?>,.5);
	border-radius:2px;
	padding: .2em;
	margin-bottom: .2em;
}
.nwsitm {
	clear: both;
	margin: 0 1em .4em 0;
}
.maplnk {
	margin-top: -4px;
	width: 48px;
	height: 48px;
}
.mini.maplnk {
	margin-bottom: -4px;
	width: 22px;
	height: 22px;
}
.mapcircle {
	width: 1em;
	height: 1em;
	border-radius: 50%;
	border: 1px solid black;
}
.third {
	width: 33%;
	margin-left: .15%;
	margin-right: .15%;
}
.equalize > DIV {
	display: inline-block;
	vertical-align: top;
}
.faketable,
.spc.rtop > DIV {
	display: block;
}
.faketable > *,
.faketable > * > *,
.faketable > * > * > * {
	display: inline;
}
.spc2 IMG {
	cursor: pointer;
	vertical-align: -4px;
}
.spc2.lght IMG {
	opacity: <?= $LITE ? .7 : .6 ?>;
}
.spc2:hover IMG {
	opacity: 1;
}
.spc2:hover {
	text-decoration: none !important;
}
.spc2 > SPAN {
	margin-left: .5em;
}
.checkp {
	outline: .5em solid rgba(255,127,0,.4);
}
.bol_pml_price,
.product_details_mini {
	font-size:<?= $SCALE * 12 ?>pt !important;
}
.bol_pml_box {
	border: 1px solid <?= $LITE ? 'rgba(0,0,0,.1)' : 'rgba(255,255,255,.2)' ?> !important;
	border-radius: 2px;
}
.srchdl {
	line-height: 30px;
}
.srchdl DT:first-child {
	margin-top: 0;
}
.spotl .info {
	padding: .5em 1em;
	margin-bottom:.5em;
	border-radius: 4px;
	border: 1px solid rgba(0,128,0,.4);
	background-color: <?= $LITE ? '#F7F7F7' : '#111' ?>;
}
#verlay TABLE {
	margin: 0;
	width: 100%;
	height: 100%;
}
#verlay TD {
	text-align: center;
	vertical-align: middle;
}
.saved {
	vertical-align: 0;
	background-size: 100% auto;
	background-image: url(<?= STATIC_HOST ?>/images/saved.png);
}
.notyet.saved {
  	background-image: url(<?= STATIC_HOST ?>/images/save_grey.png);
}
.isonlast {
	font-size: 80%;
	white-space: nowrap;
	margin: -.4em -.4em .3em -.4em;
	padding: .2em .5em;
	background-color: rgba(<?= $LITE ? styles::RGB_BLACK : styles::RGB_WHITE ?>,.4);
}
.isonlast,
.isonlast A {
	color: #<?= $LITE ? 'FFF' : '000' ?>;
}
.fadable {
	transition: opacity .5s ease-in-out;
}
.fadable.faded {
	opacity: 0;
}
.fbchoice {
	vertical-align: top;
	display: inline-block;
	text-align: center;
	min-width: 200px;
	margin: .5em;
}
.fbchoice .imgb,
.fbchoice .abs {
	padding: .5em;
}
.fbchoice .abs TABLE {
	text-shadow: 1px 1px 1px <?= $LITE ? 'white' : 'black' ?>;
	background-color: rgba(<?= $LITE ? '255,255,255,.7' : '0,0,0,.6' ?>);
}
#bb.active {
	position: fixed;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(<?= $LITE ? '255,255,255,.5' : '0,0,0,.5' ?>);
	z-index: 100000;
}
#busyimg {
	width: 64px;
	height: 64px;
	position: fixed;
	left: 50%;
	top: 50%;
	margin: -32px 0 0 -32px;

}
#adrime_pushdown_div {
	text-align: center;
	display: inline-block;
	margin: 0 auto;
}
.gendersym {
	display: inline-block;
	text-align: center;
	width: 1.5em;
}
<? if (iOS) { ?>
.gendersym {
	/* weird display bug */
	vertical-align: .6em;
}
<? } ?>
.imggroup .image,
.search .image {
	display: inline-block;
	width: 200px;
	height: 8em;
	background-repeat: no-repeat;
	background-size: cover;
	background-position: center;
}
.object-cover {
	object-fit: cover;
}
.noselect {
	-webkit-touch-callout: none;
	<? styles::vendor_prefixed('user-select: none') ?>;
}
.all-select {
	<? styles::vendor_prefixed('user-select: all') ?>;
}
@media (min-resolution: 2dppx) {
	.hdr .title H1,
	.nb,
	.nb * {
		font-weight: 300 !important;
	}
	.saved {
		background-image: url(<?= STATIC_HOST ?>/images/<EMAIL>);
	}
	.notyet.saved {
  		background-image: url(<?= STATIC_HOST ?>/images/<EMAIL>);
	}
} /* endmedia */
.smenu {
	/*line-height: 36px;*/
}
.smenu A {
	user-select: none;
	white-space: nowrap;
}
.smenu.dens {
	/*line-height: 24px;*/
	line-height: .8;
}

.not-allowed,
.unavailable.seembutton {
	cursor: not-allowed;
}
.seembutton,
.smenu A,
.smenu .unhideanchor:not(A):not(.error):not(.warning):not(.notice),
.smenu .nobutton {
	margin-bottom: 2px;
	border-radius: 2px;
	line-height: 36px;
	border: 1px solid transparent;
	padding: 5px 10px;
	background-color: rgba(<?= $LITE ? '0,0,0,.05' : '255,255,255,.15' ?>);
	transition: border 0.15s;
	text-decoration: none;
}
.smenu .ib {
	margin: 0;
	padding: 0;
	line-height: 1.8em;
}
.smenu .nobutton {
	background: none;
}
.smenu.genrebuttons A {
	font-size: 80%;
	padding: 2.5px 5px;
	color: <?= $LITE ? '#555' : '#CCC' ?>;
}
.smenu.genrebuttons A:hover {
	color: inherit;
}
.syncbutton {
	line-height: inherit;
}
/*.seembutton {
	border: 1px solid <?= $LITE ? '#E2E2E2' : '#393939' ?>;
}*/

.seembutton:hover,
.smenu A:hover,
.smenu .unhideanchor:not(A):not(.error):not(.warning):not(.notice):hover {
	cursor: pointer;
/*	background-color: rgba(<?= $LITE ? '245,245,245' : '42,42,42' ?>,.8);*/
	background-color: rgba(<?= $LITE ? '50,50,0,.2' : '255,255,255,.3' ?>);
	transition: background-color 0.15s;
	text-decoration: none;
}
.seembutton.green {
	border: 1px solid <?= $LITE ? '#AAE2AA' : '#003900' ?>;
	color: <?= $LITE ? '#006600' : '#AAFFAA' ?>;
}
.seembutton.green {
	background-color: rgba(<?= $LITE ? '150,245,150' : '0,42,0' ?>,.8);
}
.seembutton.green:hover {
	background-color: rgba(<?= $LITE ? '100,245,100' : '0,80,0' ?>,.8);
}
/*
Removing this overflow hidden setting,  fixes clear: both used in msg section
.smenu {
	overflow: hidden;
}*/
.smenu .block {
	margin-bottom: .4em;
}
/*.redCircle {
	display: inline-block;
	width: 18px;
	height: 18px;
	font-size: 14px;
	background: #d1273a;
	border-radius: 9px;
	text-align: center;
	font-weight: bold;
}*/
.greenC {
	background: #27d127;
}
.fbfillC {
	background: #27B724;
}
.fboptsC {
	background: #FFD600;
}
.blueC {
	background: #3737d1;
}
#ticketswapcnt {
	right: -.5rlh;
	top: -.5rlh;
}
.big.circle,
.bigger.circle {
	width: 26px;
	height: 26px;
	line-height: 26px;
	font-size: 20px;
	border-radius: 13px;
}
.big.circle {
	font-size: 15px;
	line-height: 25px;
}
.circle {
	color: white;
	text-shadow: 0 0 1px black;
	display: inline-block;
	width: 20px;
	height: 20px;
	line-height: 20px;
	vertical-align: 0;
	font-size: 14px;
	border-radius: 10px;
	text-align: center;
	font-weight: bold;
}
.lineuprow {
	font-size: 80%;
	opacity: .7;
	color: rgb(<?= $LITE ? '220,170,80' : '125,100,0' ?>);
}
.light.lineuprow {
	opacity: .4;
}
.lineuprow A {
	color: rgb(<?= $LITE ? '150,100,0' : '255,204,0' ?>);
}
.lineuprow TD {
	padding-top: 0 !important;
	padding-left: 1em !important;
	padding-right: 1em !important;
}
.year-ago {
	opacity: .8;
}
.two-year-ago {
	opacity: .65;
}
.three-year-plus-ago {
	opacity: .5;
}
.fpart {
	padding-top: 1em;
}
.fpart .sellim {
	margin-right: .5em !important;
}
.fpart .inpspc {
	margin-left: .5em !important;
	margin-right: .5em !important;
}
.generic .nongeneric {
	display: none;
}
.aspect-box {
	height: 0;
	position: relative;
	overflow: hidden;
}
.aspect-box > * {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	/* Limit height of content (iframe, img, etc) to 95% of the viewport */
	max-height: 95vh;
}
#lineup-form.floating {
	top: <?= SMALL_SCREEN ? '2.2em' : '0' ?>;
	position: sticky;
	z-index:20;
}
#lineup-form.floating .party.box {
	padding: .5em;
	margin: 0;
}
#lineup-form.floating::after,
#lineup-form.floating::before {
	display: block;
	content: " ";
	height: 1em;
	width: 100%;
	background-color: rgba(0,0,0,.5);
}
#lineup-error .block {
	margin: .2em 0 0 0;
}
.locked-item {
	cursor: not-allowed !important;
	opacity: .3;
}
.item-done {
	opacity: .3;
}
/*.warning-less {
	color: <?= $LITE ? 'rgb(135,68,0)' : '#FFCE6A' ?>;
}*/

TBODY.party-spot-header TR,
.party-spot {
	background-color: rgba(<?= $LITE ? '224,235,224' : '25,45,25' ?>,.8);
}
TR.party-spot TD {
	padding: 1em 2em !important;
}
.party-spot .party-desc {
	padding-left: 2em;
}
.smallscreen .party-spot .uimg {
	margin: 0 -2em;
}
.uimg {
	margin-bottom: .5em;
}
.party-spot .uimg-container {
	min-height: inherit;
}
.party-spot H2 {
	font-size: 125%;
}
.party-spot {
	font-size: 100%;
}
.default.prices TD {
	padding: 0;
}
.tag {
	padding: .2em .5em;
	background-color: rgba(<?= $LITE ? styles::RGB_BLACK : styles::RGB_WHITE ?>,.1);
}
.taglist LI {
	margin: .2em 0;
}

.body .title A {
	font-weight: bold;
	color: <?= $LITE ? 'rgba(138,91,0,.7)' : 'rgba(255,231,185,.7)' ?>;
}

.force-link A,
.force-link .unhideanchor,
.body .link,
.body .emailink,
.body .itemlink {
	color: <?= $LITE ? 'rgb(138,91,0)' : 'rgb(255,231,185)' ?>;
	border-bottom: 1px solid <?= $LITE ? 'rgba(138,91,0,.2)' : 'rgba(255,231,185,.2)' ?>;
}

/* lines below images don't look nice, so remove them: */
.body .link:has(IMG) {
	color: inherit;
	border-bottom: inherit;
}

.force-link A:visited,
.force-link .unhideanchor:visited,
.body .link:visited,
.body .emailink:visited,
.body .itemlink:visited {
	color: <?= $LITE ? 'rgb(128,71,0)' : 'rgb(255,245,220)' ?>;
	border-bottom: 1em solid <?= $LITE ? 'rgba(128,71,0,.3)' : 'rgba(255,245,220,.3)' ?>;
}

.force-link A:hover,
.force-link A:active,
.force-link .unhideanchor:hover,
.force-link .unhideanchor:active,
.body .link:hover,
.body .link:active,
.body .emailink:hover,
.body .emailink:active,
.body .itemlink:hover,
.body .itemlink:active {
	filter: brightness(1.5);
	text-decoration: none;
}

.body SPAN.link {
	color: inherit;
}

<?
$base = $LITE ? styles::RGB_BLACK : styles::RGB_WHITE;

$mult = $LITE ? 1 : 2;
?>

/*.forcescroll:f:-webkit-scrollbar {
	overflow:visible;
	height: 18px;
	width: 18px
}
.forcescroll::-webkit-scrollbar-button {
	height: 0;
	width: 0;
}
.forcescroll::-webkit-scrollbar-track {
	background-color: rgba(<?= $base ?>,<?= .02 * $mult ?>);
	border: 0
}
.forcescroll::-webkit-scrollbar-track:horizontal {
	border: 0;
}
.forcescroll::-webkit-scrollbar-track:hover {
	background-color: rgba(<?= $base ?>,<?= .05 * $mult ?>);
}
.forcescroll::-webkit-scrollbar-track:active {
	background-color: rgba(<?= $base ?>,<?= .05 * $mult ?>);
}
.forcescroll::-webkit-scrollbar-thumb {
	background-clip: padding-box;
	background-color: rgba(<?= $base ?>,<?= .2 * $mult ?>);
	border: 2px solid transparent;
	min-height: 30px
}
.forcescroll::-webkit-scrollbar-thumb:hover {
	background-color: rgba(<?= $base ?>,<?= .3 * $mult ?>);
}

.forcescroll::-webkit-scrollbar-thumb:active {
	background-color: rgba(<?= $base ?>,<?= .4 * $mult ?>);
}
.forcescroll::-webkit-scrollbar-corner {
	background: transparent;
}

.forcescroll::-webkit-scrollbar-track-piece,
.forcescroll::-webkit-scrollbar-corner {
	background-color: #<?= $LITE ? 'F5F5F5' : '101010' ?>;
	border: 0;
}
.forcescroll::-webkit-scrollbar-track-piece {
	border-width: 0 0 0 3px;
}

.forcescroll::-webkit-scrollbar-track-piece:horizontal {
	border-width: 3px 0 0;
}*/

A.onepage {
	color: rgb(0,255,0) !important;
	font-weight: bold;
}
.partnerchoice {
	/* fixes opacity obscuring crop onover button */
	display: inline-block;
}
.partnerchoice SELECT {
	background: transparent;
}
.partnerchoice.no {
	opacity: .5;
}
.partnerchoice.no SELECT {
	border: 1px solid #445;
}
.advice-header {
	padding: .5em;
	font-weight: bold;
	background-color: #<?= $LITE ? 'C9C9DC' : '223' ?>;
}
.advice-box {
	cursor: pointer;
	padding: .5em .6em;
	margin: .5em;
	width: 33.33333%;
	background-color: #<?= $LITE ? 'EEF' : '223' ?>;
	border-radius: 5px;
}
.advice-box .uimg {
	margin-bottom: 0;
}
.advice-box .the-uimg {
	border-radius: 5px;
}
.smallscreen .advice-box {
	width: auto;
	min-height:calc(100px + 1em);
}

.body .center .block {
	margin-left: auto;
	margin-right: auto;
}
.simple-box {
	padding: .5em 1em;
	background-color: <?= $LITE ? '#F8F8F8' : '#111' ?>;
}
.simple-box .header {
	font-weight: bold;
}
.groupmemberinfo {
/*	display: inline-block;*/
	font-size: 80%;
	opacity: .4;
/*	margin-left: 2em;
	white-space: nowrap;*/
}
.genres {
	color: <?= $LITE ? '#00F' : '#88F' ?>;
}
.master-genre {
	color: <?= $LITE ? '#F8F': '#F5F' ?>;
	<? if ($LITE) { ?>
	font-weight: 500;
	<? } ?>
}

.xe-xdebug {
	color: #000;
}

.partylist .great-thing > .block {
	margin: 0;
	padding: 1em 0;
	border: 1px solid rgba(<?= $LITE ? styles::RGB_BLACK : styles::RGB_WHITE ?>,.05);
	border-left: 0;
	border-right: 0;
}
.partylist .great-span {
	padding: 0;
}
.marked {
	font-weight: 700;
	padding: .3em;
	margin: -.3em .2em;
	background-color: rgba(255, 255, 255, .1);
}
#cmts {
	display: block;
	margin-bottom: .5em;
}
.uimg .date {
	font-size: 80%;
	opacity: .6;
}
.broken-link {
	text-decoration: underline;
	text-decoration-color: rgba(255,0,0,.4) !important;
	text-underline-offset: 3px;
	opacity: .8;
}

.box.bordered {
	border: 1px solid rgba(<?= $LITE ? '0,0,0' : '255,255,255' ?>,.2);
}

.virtualtour.zoomover {
	margin: .5em;
}

/* NOTE: Place these at the end, so they are chosen above higher up rules */

A.error,
A.warning,
A.notice,
.error A,
.warning A,
.notice A {
	text-underline-offset: 3px;
}
.notice A:hover,
.warning A:hover,
.error A:hover {
	filter: brightness(1.5);
}
.error SELECT,
.error {
	color: #F00;
}
A.error,
.error A {
	color: #F00;
	text-decoration: solid #FF000066 underline;
}
A.error:visited,
.error A:visited {
	color: #FF0000CC !important;
}
.error A:hover {
	color: #F00 !important;
	text-decoration: solid #FF0000B3 underline;
}
.warning {
	color: #FF8000;
}
A.warning,
.warning A {
	color: #FF8000;
	text-decoration: solid #FF800066 underline;
}
A.warning:visited,
.warning A:visited {
	color: <?= $LITE ? '#FFA050' : '#DD7000' ?>;
}
A.warning:hover,
.warning A:hover {
	text-decoration: solid #FF8000 underline;
}
.notice {
	color: <?= $LITE ? '#00B400' : '#0F0' ?>;
}
A.notice,
.notice A {
	color: <?= $LITE ? '#0C0' : '#0F0' ?>;
	text-decoration: solid <?= $LITE ? '#00CC0066' : '#00FF0066' ?> underline;
}
A.notice:hover,
.notice A:hover {
	text-decoration: solid #0F0 underline;
}
.warning-nb,
.warning-nb A {
	color: <?= diff_color(0x874400) ?> !important;
}
.blink {
  animation: blinker 1s linear infinite;
}
@keyframes blinker {
	50% {
		opacity: 0;
	}
}
.genre-description {
	font-size: 80%;
	opacity: .7;
	padding: .5em;
	background-color: <?= $LITE ? '#EEE' : '#222' ?>;
}
.menu-bullet {
	opacity: .5;
	padding: 0 .5em;
}
