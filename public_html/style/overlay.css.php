<?php

require_once '_styles.inc';
#require_once '../_hosts.inc';
#require_once '../_browser.inc';
#require_once '../_smallscreen.inc';

$style = new styles('style/overlay.css.php');
/** @noinspection PhpRedundantOptionalArgumentInspection */
extract(styles::$styles, \EXTR_OVERWRITE);

?>
.overlay {
	position: fixed;
	text-align: center;
	width: 100%;
	height: 100%;
	display: table;
	z-index: 25
}
.overlay > DIV {
	display: table-cell;
	vertical-align: middle;
	height: 100%
}
.overlay > DIV > DIV {
	display: inline-block;
	position: relative;
	text-align: left;
	background-color: #<?= $LITE ? 'FFE' : '003' ?>;
	padding: 2em;
	border-radius: 1em;
	border: 2px solid #<?= $LITE ? '994' : '66A' ?>;
}
.overlay INPUT[type="submit"].abs {
	position: absolute;
	bottom: 2em;
	right: 2em
}
.overlay .hilited {
	border-radius: 5px;
	background-color: rgba(<?= $LITE ? '190,255,190, .8' : '0,255,0, .5' ?>) !important;
}
