<?php

declare(strict_types=1);

require_once '_styles.inc';
require_once '../_hosts.inc';
require_once '../_browser.inc';
require_once '../_smallscreen.inc';
#require_once '../_cookie.inc';

$style = new styles('style/buddyrequest.css.php');
/** @noinspection PhpRedundantOptionalArgumentInspection */
extract(styles::$styles, \EXTR_OVERWRITE);

?>.bddyrqst {
	border-radius: 2px;
	position: relative;
	display: inline-block;
	text-align: center;
	min-width: 250px;
	margin: 5px;
	padding: 5px;
	border: 1px solid rgba(<?= $LITE ? '0,0,0' : '255,255,255' ?>, .2);
}
.bddyrqst .img {
	margin: 3px 0
}
