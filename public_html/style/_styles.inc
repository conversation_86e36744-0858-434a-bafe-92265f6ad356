<?php

declare(strict_types=1);

define('CURRENTSTAMP',	time());

require_once __DIR__.'/../_servertype.inc';
require_once __DIR__.'/../defines/cache.inc';

class styles {
	public const string FLOCK_YELLOW		= '#ECBA2C';
	public const string FLOCK_YELLOW_DARK	= '#BB9433';
	public const string RGB_WHITE			= '255,255,255';
	public const string RGB_BLACK			= '0,0,0';
	public const string FORCE_WRAP			= 'overflow-wrap: anywhere;';
	public const array SCALES = [
		'xx-small'	=> 0.7,
		'x-small'	=> 0.8,
		'small'		=> 0.9,
		'browser'	=> 1,
		'medium'	=> 1,
		'large'		=> 1.1,
		'x-large'	=> 1.2,
		'xx-large'	=> 1.3,
	];

	public static array $styles;

	private bool	$started	= false;
	private string	$key;

	public function __construct(string $file_name) {
		require_once __DIR__.'/../_modified.inc';
		require_once __DIR__.'/../_hashes.inc';
		require_once __DIR__.'/../_nocache.inc';
		require_once __DIR__.'/../_style.inc';

		if (isset($_SERVER['eID'])) {
			$_SERVER['eID'] = (int)$_SERVER['eID'];
		}
		$_SERVER['REQUEST_URI'] =
			!isset($_SERVER['REQUEST_URI'])
		?	''
		:	rtrim($_SERVER['REQUEST_URI'], '?');

		if (isset($_REQUEST['SWAPTHEME'])) {
			require_once __DIR__.'/../_memcache.inc';
			require_once __DIR__.'/../_hosts.inc';
			require_once __DIR__.'/_styles.inc';

			send_no_cache_headers();

			$style_settings =
					!empty($_SERVER['eID'])
				?   self::get_stylespec($_SERVER['eID'])
				:   DEFAULT_STYLES;
			$style_settings['THEME'] = ($style_settings['THEME'] === 'dark') ? 'light' : 'dark';
			unset($style_settings['ID']);

			if (!($id = get_id_for_style_settings($style_settings))) {
				error_log('WARNING could not id from get_stylespec()');
				http_response_code(503);
				exit;
			}
			see_other(
				rtrim(
					preg_replace(
						'"_(\d+)_"',
						"_{$id}_",
						str_replace(
							'SWAPTHEME',
							'',
							$_SERVER['REQUEST_URI'] ?? ''
						)
					),
					'?'
				)
			);
		}
		require_once __DIR__.'/../_browser.inc';
		require_once __DIR__.'/../_memcache.inc';
		require_once __DIR__.'/../_smallscreen.inc';
		require_once __DIR__.'/../_hosts.inc';

		header('Cache-Control: public,must-revalidate,max-age=600');
		#header('Cache-Control: public,must-revalidate,max-age=0');
		header('Content-Type: text/css; charset=us-ascii');
		header('Vary: User-Agent');

		static::$styles = self::parse_style();

		$this->key = hash(
			HASH_ALGO_FOR_FILES,
			'style'.
			':'.HASHES_VERSION.
			':'.get_hash($file_name).
			':'.get_hash('style/_styles.inc').
			':'.implodehash(',', '=', static::$styles).
			':'.(iOS ? 'iOS' : '').
			':'.(ANDROID ? 'Android' : '').
			':'.BROWSER_NAME.
			':'.BROWSER_VERSION.
			':'.SERVER_TYPE.
			':'.(SMALL_SCREEN ? 'smallscreen' : 'desktop'));

		$last_modified = max(
			filemtime(__DIR__.'/_styles.inc'),
			filemtime($_SERVER['SCRIPT_FILENAME'])
		);

		if (not_modified($last_modified, $this->key)) {
			http_response_code(304);
			exit;
		}

		if ($data = memcached_get($this->key)) {
			echo $data;
			exit;
		}

		require_once __DIR__.'/_coloradjust.inc';

		switch (static::$styles['FONT_TYPE']) {
		default:
		case 'browser':
			static::$styles['FONT_TYPE'] = '';
			break;

		case "'Titillium Web'":
			static::$styles['FONT_TYPE'] = "'".static::$styles['FONT_TYPE']."'";
			# fall through
		case 'Raleway':
		case 'Arial':
		case 'Tahoma':
		case 'Verdana':
			static::$styles['FONT_TYPE'] .= ',sans-serif';
			break;
		}
		if (!empty(static::$styles['FORLITE'])) {
			$LITE = static::$styles['THEME'] !== 'light';
			static::$styles['LITE']	 = $LITE;
			static::$styles['THEME'] = $LITE ? 'light' : 'dark';
			unset(static::$styles['THEME']);
		} else {
			static::$styles['LITE'] = static::$styles['THEME'] === 'light';
		}
		static::$styles['SCALE']	= static::SCALES[static::$styles['FONT_SIZE']];
		static::$styles['CONTRAST']	= static::$styles['THEME_CONTRAST'] / 100;
		static::$styles['SOFT']		= static::$styles['THEME_SOFT'];
		unset(static::$styles['THEME_CONTRAST'], static::$styles['THEME_SOFT']);

		static::$styles['VENDOR_PREFIX'] = FIREFOX ? '-moz-' : (WEBKIT ? '-webkit-' : '');

		$this->started = true;
		ob_start();
	}

	private static function get_stylespec(int $id): ?array {
		return memcached_single_assoc(
			'stylespec',
			'SELECT * FROM stylespec WHERE ID = '.$id,
			DB_FORCE_MASTER
		) ?: null;
	}

	private static function parse_style(): array {
		if (!empty($_SERVER['eARGS'])) {
			error_log('WARNING styles::parse_style() called with eARGS = '.$_SERVER['eARGS']);
			$settings = DEFAULT_STYLES;
			$parts = explode(',', $_SERVER['eARGS']);
			$i = 0;
			foreach ($settings as /* $key => */ &$default_setting) {
				if (!isset($parts[$i])) {
					break;
				}
				if ($parts[$i] !== ''
				&&	preg_match('~^[a-zA-Z\d\s_-]+$~', $parts[$i])
				) {
					$default_setting = str_replace('_', ' ', $parts[$i]);
				}
				++$i;
			}
			unset($default_setting);
		} elseif (!empty($_SERVER['eID'])) {
			$settings = self::get_stylespec($_SERVER['eID']);
		} else {
			$settings = DEFAULT_STYLES;
		}
		return $settings;
	}

	final public function get(): array {
		$local_styles = static::$styles;
		if (!empty(static::$styles['FORLITE'])) {
			$local_styles['THEME'] =  $local_styles['THEME'] === 'dark' ? 'light' : 'dark';
			$local_styles['LITE']  = !$local_styles['LITE'];
		}
		return $local_styles;
	}

	public function __destruct() {
		if (!isset($this->started)) {
			return;
		}
		$data = ob_get_clean();
		$media = str_contains($data, '@');
		if (!$media) {
			$data = self::optimize($data);
		}
		if (!empty(static::$styles['FORLITE'])) {
			$new_parts = [];
			if (preg_match_all('"([^{]*){([^}]*)}"', $data, $matches)) {
				foreach ($matches[0] as $i => $ignored) {
					$new_selectors = [];
					foreach (explode(',',$matches[1][$i]) as $selector) {
						$new_selectors[] = '.for-lite '.$selector;
					}
					$new_parts[] = implode(',', $new_selectors).'{'.$matches[2][$i].'}';
				}
			}
			$data = implode('', $new_parts);
		}
		if ($media) {
			$media_parts = [];
			$i = 0;
			$data = preg_replace_callback(
				'"@(?<header>[^{]*){(?<block>[^}]*)}\s*/\*+\s*end(?:media|_at)\s*\*+/"',
				static function(array $match) use (&$media_parts, &$i): string {
					$media_parts[$rep = "mediaholder{\x1E$i\x1F}"] = $match;
					++$i;
					return $rep;
				},
				$data
			);

			$data = self::optimize($data);

			if ($media_parts) {
				foreach ($media_parts as $rep => $part) {
					$data = str_replace(
						$rep,
						'@'.utf8_mytrim($part['header']).'{'.
							self::optimize(utf8_mytrim($part['block'])).
						'}',
						$data);
				}
			}
		}

		echo $data;

		memcached_set($this->key, $data, TEN_MINUTES);
	}

	private static function optimize(string $data): string {
		/** @noinspection RegExpSuspiciousBackref */
		$data = preg_replace([
		/* 1 */ '"\n+"',
		/* 2 */ '"/\*(.*?)\*/"',
		/* 3 */ '"([a-z\-]):\s*(.*?)(?:\s*(!important))?(;)?(\n)?"m',
		/* 4 */ '"([;{},])\s+"',
		/* 5 */ '"\s+([;{},])"',
		/* 6 */ '"\s*([<>])\s*"',
				# font-weight: bold -> font-weight:700
		/* 7 */ '"(?<=:)\s*bold\b"',
		/* 8 */ '";\s*}"',
				#  margin: 5px 5px 5px 5px -> padding:5px
		/* 9 */ '"((?:margin|padding)\s*:\s*(?<only>[\d.-]+[a-z%]+))\s+\k<only>\s+\k<only>\s+\k<only>\b"',
				#  margin: 2px 4px 2px 4px -> margin:2px 4px
		/* a */ '"((?:margin|padding):\s*(?<first>[\d.-]+[a-z%]+)\s+(?<second>[\d.-]+[a-z%]+))\s+\k<first>\s+\k<second>\b"',
				#  margin: 3px 5px 9px 5px -> padding:3px 5px 9px
		/* b */ '"((?:margin|padding):\s*[\d.-]+[a-z%]+\s+(?<mid>[\d.-]+[a-z%]+)\s+[\d.-]+[a-z%]+)\s+\k<mid>\b"',
				# @media (max-width: 600px) -> @media(max-width: 600px)
		/* c */ '"\s+(\(.*?\))\s+"s',
				# 0.5em -> .5em
		/* d */ '"\b0\."',
		/* e */ '"#([a-f0-9])\1([a-f0-9])\2([a-f0-9])\3\b"i'
		],[
		/* 1 */ '',
		/* 2 */ '',
		/* 3 */ '$1:$2$3$4$5',
		/* 4 */ '$1',
		/* 5 */ '$1',
		/* 6 */ '$1',
		/* 7 */ '700',
		/* 8 */ '}',
		/* 9 */ '$1',
		/* a */ '$1',
		/* b */ '$1',
		/* c */ '$1',
		/* d */ '.',
		/* e */ '#$1$2$3',
		],$data);
		if (str_contains($data,'rgb(')) {
			$data = preg_replace_callback(
				'"rgb\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)"',
				static function(array $match): string {
					$r = dechex((int)$match[1]);
					$g = dechex((int)$match[2]);
					$b = dechex((int)$match[3]);
					if (!isset($r[1])
					&&	!isset($g[1])
					&&	!isset($b[1])
					) {
						return "#$r$g$b";
					}
					return '#'.	str_pad($r, 2, '0', STR_PAD_LEFT).
								str_pad($g, 2, '0', STR_PAD_LEFT).
								str_pad($b, 2, '0', STR_PAD_LEFT);
				},
				$data
			);
		}
		if (!preg_match_all('"([^{]*){([^}]*)}"',$data,$matches)) {
			return $data;
		}
		$specs = [];
		foreach ($matches[1] as $ndx => $vars) {
			if (!($specstr = $matches[2][$ndx])) {
				continue;
			}
			$specs[$specstr] = [...$specs[$specstr] ?? [], ...explode(',', $vars)];
		}
		$new_spec = [];
		foreach ($specs as $spec => $var_list) {
			foreach ($var_list as $vars) {
				foreach (explode(',', $vars) as $var) {
					$new_spec[$spec][str_contains($var, '-child')][] = $var;
				}
			}
		}
		$new_data = '';
		foreach ($new_spec as $spec => $children) {
			foreach ($children as /* $child => */ $vars) {
				$new_data .= implode(',', $vars).'{'.$spec.'}';
			}
		}
		return $new_data;
	}

	public static function vendor_prefixed(string $arg): void {
		if (!$arg) {
			return;
		}
		$prefix = static::$styles['VENDOR_PREFIX'];
		if ($arg[0] === '@') {
			if ($prefix) {
				# @at_rule with vendor prefix
				echo '@', $prefix, substr($arg, 1);
			}
			# @at_rule as-is
			echo $arg;
			return;
		}
		if ($prefix) {
			# property with vendor prefix
			echo $prefix, $arg ?>;<?
		}
		# property as-is
		echo $arg;
	}
	public static function transition(string $arg): void {
		if ($prefix = static::$styles['VENDOR_PREFIX']) {
			echo $prefix, 'transition:', $prefix, $arg, ';';
		}
		echo 'transition:', $arg, ';';
		if (WEBKIT && str_starts_with($arg,'filter')) {
			echo 'transition:', $prefix, $arg, ';';
		}
	}

	public static function linear_gradient(
		string	$spec,
		string	$from,
		string	$to,
		?string	$fallback = null,
	): void {
		if ($fallback) {
			echo 'background:', $fallback ?>;<?
		}
		if (WEBKIT) {
			?>background:-webkit-linear-gradient(<?= $spec ?>,<?= $from ?>,<?= $to ?>);<?
		} elseif (FIREFOX) {
			?>background:-moz-linear-gradient(<?= $spec ?>,<?= $from ?>,<?= $to ?>);<?
		}
		?>background:linear-gradient(<?= $spec ?>,<?= $from ?>,<?= $to ?>)<?
	}
}

function make_shadow(): string {
	global $LITE;
	ob_start();
	$multiplier = 1;
	if (!$LITE) {
		$multiplier = 1.2;
		foreach (func_get_args() as $var) {
			if ($var < 0) {
				$multiplier = 2;
				break;
			}
		}
	}
	foreach (func_get_args() as $var) {
		echo $var ? (($LITE ? 1 : $multiplier) * (FIREFOX ? ($var / 2) : $var)).'px ' : '0 ';
	}
	return ob_get_clean();
}

function make_rgb(
	string	$rgb,
	float	$a,
	?string	$bg = null,
): string {
	if ($bg === null) {
		global $LITE;
		$bgs = $LITE ? [255,255,255] : [0,0,0];
	} else {
		$bgs = explode(',',$bg);
	}
	$rgbs = explode(',',$rgb);

	return
		round( ($a * $rgbs[0] + (1 - $a) * $bgs[0])).','.
		round( ($a * $rgbs[1] + (1 - $a) * $bgs[1])).','.
		round( ($a * $rgbs[2] + (1 - $a) * $bgs[2]));
}
