<?php

require_once '_styles.inc';
require_once '../_hosts.inc';
require_once '../_browser.inc';
require_once '../_smallscreen.inc';
#require_once '../_cookie.inc';

$style = new styles('style/form.css.php');
/** @noinspection PhpRedundantOptionalArgumentInspection */
extract(styles::$styles, \EXTR_OVERWRITE);

$from = $LITE ? '#FEFEFE' : '#2A2A2A';
  $to = $LITE ? '#EEEEEE' : '#111111';

?>FORM {
	margin: 0;
	padding: 0;
}
FORM .box-column TABLE.fw,
FORM .box-column TABLE.dyntab {
	border-collapse: separate;
}
FORM .box-column TABLE.fw,
FORM .box-column TABLE.dyntab {
	border-collapse: collapse;
}
FORM TABLE > TBODY > TR > TD.field {
	padding: .2em .4em;
	background-color: rgba(<?= $LITE ? '0,0,0,.04' : '255,255,255,.1' ?>);
	border-radius: 2px;
}
FORM TABLE > TBODY > TR > TD.field + TD,
FORM TABLE > TBODY > TR > TD.header {
	padding: 0 .5em 0 .5em;
}
FORM TABLE > TBODY > TR > TD.header {
	padding-bottom: .5em;
	font-weight: bold;
}

FORM .contact-touser TABLE > TBODY > TR > TD.field,
FORM .white TABLE > TBODY > TR > TD.field {
	background-color: rgba(<?= $LITE ? '0,0,0,.025' : '255,255,255,.05' ?>);
}
TEXTAREA,
SELECT,
INPUT,
BUTTON {
	font-family: inherit;
	font-size: inherit;
}
TEXTAREA {
	/* font-family: system-ui; */
	font-family: "Fira Code", "Courier New", monospace;
	/* font-size: 14px; */
}
@media (min-resolution: 2dppx) {
	TEXTAREA,
	SELECT,
	INPUT {
		font-weight: 300;
	}
} /* endmedia */

SELECT.nobg {
	background-image: none
}
SELECT.fw {
	max-width: 100%
}
TEXTAREA.growToFit {
	overflow-y: hidden
<?/*	border-bottom-left-radius: 10px*/?>
}
TEXTAREA,
INPUT[type="email"],
INPUT[type="file"],
INPUT[type="float"],
INPUT[type="number"],
INPUT[type="password"],
INPUT[type="search"],
INPUT[type="tel"],
INPUT[type="text"],
INPUT[type="url"] {
	width: 100%;
}
INPUT[type="file"] {
	width: auto;
}

<? if (true) { ?>
	SELECT:focus,
	TEXTAREA:focus,
	INPUT[type="email"]:focus,
	INPUT[type="float"]:focus,
	INPUT[type="number"]:focus,
	INPUT[type="password"]:focus,
	INPUT[type="search"]:focus,
	INPUT[type="tel"]:focus,
	INPUT[type="text"]:focus,
	INPUT[type="url"]:focus {
		outline: 2px solid #7B8AFC;
/*		<? styles::vendor_prefixed('outline-radius: 2px') ?>;*/
	}
<? } ?>

<? if (WEBKIT) { ?>
INPUT[type="search"]::-webkit-search-results-decoration {
	display: none;
}
<? }  ?>

TEXTAREA::placeholder,
INPUT::placeholder {
	/*font-weight: normal;*/
	color: <?= $LITE ? '#AAA' : '#777' ?>;
}

TEXTAREA {
	display: block;
	resize: vertical;
	max-width: 100%;
	width: 100%;
}
<? if (CHROME
&&	($linux = false !== stripos(USER_AGENT,'linux'))
&&	$FONT_TYPE
&&	USER_AGENT
) { ?>
SELECT,
INPUT {
	font-family:<?= $FONT_TYPE ?>
}
<? } ?>
TEXTAREA,
INPUT[type="text"],
INPUT[type="search"],
INPUT[type="tel"],
INPUT[type="url"],
INPUT[type="file"],
INPUT[type="email"],
INPUT[type="number"],
INPUT[type="float"],
INPUT[type="password"],
BUTTON,
INPUT[type="button"],
INPUT[type="submit"],
SELECT {
	background-color: <?= $LITE ? '#FFFFFF80' : '#0000004D' ?>;
	color: <?= $LITE ? '#000' : '#FFF' ?>;
	margin: 2px 0;
	height: 1.6rlh;
	padding: 0 5px;
	border-radius: 2px;
	border: 1px solid #<?= $LITE ? 'BBB' : '555' ?>;
	transition: border 0.15s;
}
INPUT[readonly][type="text"],
INPUT[readonly][type="password"] {
	border: 1px dashed #<?= $LITE ? 'BBB' : '555' ?>;
}

<? if (SAFARI) { ?>
	INPUT[type="button"],
	BUTTON,
	INPUT[type="submit"],
	INPUT[type="search"] {
		-webkit-appearance: none;
	}
<? } ?>
<? if (SAFARI || FIREFOX) { ?>
	SELECT {
		<? styles::vendor_prefixed('appearance: none') ?>;
		padding-right: 20px;
		background-size: 16px;
		background-repeat: no-repeat;
		background-position: right 3px center;
		background-image: url(<?= STATIC_HOST ?>/images/arrow-down-<?= $LITE ? 'light' : 'dark' ?>.png);
	}
	SELECT[multiple] {
		background: none
	}
	SELECT[multiple] OPTION[selected] {
		background-color: green;
	}
<? } ?>
TEXTAREA,
SELECT[multiple] {
	height: auto;
}
TEXTAREA:focus,
SELECT:focus,
INPUT[type="text"]:focus,
INPUT[type="search"]:focus,
INPUT[type="tel"]:focus,
INPUT[type="url"]:focus,
INPUT[type="file"]:focus,
INPUT[type="email"]:focus,
INPUT[type="number"]:focus,
INPUT[type="float"]:focus,
INPUT[type="password"]:focus {
	background-color: #<?= $LITE ? 'FFF' : '000' ?>;
}
SELECT OPTION:checked {
	font-weight: bold;
	background-color: <?= $LITE ? '#0F0' : '#080' ?>;
}
TEXTAREA {
	padding: .5em;
}
TEXTAREA:hover,
INPUT[type="text"]:hover,
INPUT[type="search"]:hover,
INPUT[type="tel"]:hover,
INPUT[type="url"]:hover,
/*INPUT[type="file"]:hover,*/
INPUT[type="email"]:hover,
INPUT[type="number"]:hover,
INPUT[type="float"]:hover,
INPUT[type="password"]:hover,
SELECT:hover {
	border: 1px solid #<?= $LITE ? '777' : '777' ?>
}
INPUT[type="file"] {
	border: inherit !important;
	background-color: transparent !important;
}
BUTTON,
INPUT[type="button"],
INPUT[type="submit"] {
	padding: 0 10px;
	background-color: <?= $LITE ? '#EEE' : '#292929' ?>;
	border: 1px solid rgba(<?= $LITE ? '0,0,0' : '255,255,255' ?>,.2);
	transition: border 0.15s;
}
INPUT.blue {
	<? styles::linear_gradient('to top',$from,$LITE ? '#D2D2FF' : '#52529F') ?>
}
BUTTON:hover,
INPUT[type="button"]:hover,
INPUT[type="submit"]:hover {
	cursor: pointer;
	background-color: <?= $LITE ? '#DDD' : '#3A3A3A' ?>;
	transition: background-color 0.15s;
}
BUTTON.unhideanchor,
INPUT[type="button"].unhideanchor,
INPUT[type="submit"].unhideanchor {
	border: 0;
	margin: 0;
	padding: 0;
	background: transparent
}
OPTION,
OPTGROUP {
	color: #<?= $LITE ? '000' : 'FFF' ?>;
	background-color: <?= inverted_color(0xF7F7F7) ?>
}

OPTGROUP:disabled,
OPTGROUP:disabled OPTION,
OPTION:disabled {
	color: #<?= $LITE ? 'BBB' : '444' ?>
}
OPTGROUP.hidden OPTION {
	display: none
}
SELECT:disabled {
	opacity: .4;
}
OPTION.fd:disabled {
	color: inherit
}
<? if (WEBKIT) { ?>
INPUT[type="number"]::-webkit-outer-spin-button,
INPUT[type="number"]::-webkit-inner-spin-button {
	-webkit-appearance: none;
	margin-left: 4px
}
<? } elseif (FIREFOX) { ?>
INPUT[type="number"] {
	-moz-appearance: textfield
}
<? } ?>
<? if (WEBKIT && !$LITE) { ?>
INPUT:-webkit-autofill {
	-webkit-box-shadow: 0 0 0px 1000px #004 inset !important;
	-webkit-text-fill-color: #FFF !important;
	<? /* this is required to force safari to apply above styles: */ ?>
	background-clip: content-box;
}
<? } ?>
INPUT:invalid,
.refill,
.refill SELECT {
	border: 1px solid rgba(255,0,0,.5) !important;
	color: #<?= $LITE ? 'A00' : 'F88' ?>;
	caret-color: #<?= $LITE ? '000' : 'FFF' ?>;
}
DIV.refill {
	border: inherit !important;
	outline: 2px solid rgba(255,0,0,.5) !important;
}

INPUT[type="search"].supersearch {
	border: 1px solid rgb(0,<?= $LITE ? 160 : 150 ?>,0) !important;
}
INPUT[type="search"].supersearch:focus {
	outline: 2px solid rgb(0,<?= $LITE ? 200 : 200  ?>,0);
}

.rethink {
	border: 1px solid green !important;
}
.refill:focus {
	border: 1px solid red !important;
	outline-color: red;
}
.pollhome INPUT {
	margin: 0 .4em 0 0
}
.clean,
.clean:hover,
.clean:focus {
	border: 0 !important;
	margin: 0 !important;
	padding: 0 !important;
	outline: none !important
}
/*INPUT[type="checkbox"],
INPUT[type="radio"] {
	vertical-align: middle;
	width: 1.1em;
	height: 1.1em;
	margin: 2px 0;
}*/
INPUT.two_digits	{ max-width: 4em }
SELECT.three_digits,
INPUT.three_digits	{ max-width: 5em }
INPUT.time		{ min-width: 4em; max-width: 5em }
INPUT.five_digits,
INPUT.centsprice	{ max-width: 8em }
INPUT.centsprice	{ text-align: right }
INPUT.ip,
INPUT.id,
INPUT.gps,
INPUT[type="tel"]	{ max-width: 10em }
INPUT.price_euro	{ max-width: 7em }
INPUT[type="password"],
INPUT.email,
INPUT.credential	{ max-width: 24em }
INPUT.regular,
INPUT[type="search"]	{ max-width: 30em }
INPUT.fw[type="search"]	{ max-width: 100% }
INPUT.short		{ max-width: 15em }

<? /* non dynamic tables should not be enlarged */ ?>
INPUT[type="file"].autow,
FORM .nodyn TD.field + TD,
FORM .nodyn > TBODY > TR > TD.field + TD,
.r INPUT {
	width: auto
}
.genrein {
	color: rgba(<?= $LITE ? '0,0,0' : '255,255,255' ?>, .8) !important;
	width: 100% !important
}
INPUT:invalid,
SELECT:invalid,
TEXTAREA:invalid {
	box-shadow: none;
}
INPUT:required:invalid,
TEXTAREA:required:invalid,
SELECT:required:invalid,
SELECT.fillme {
	border: 1px solid rgba(<?= $LITE ? '0,150,0' : '0,255,0' ?>,.5) !important;
}
INPUT[name="AREAID"]:invalid:focus {
	outline: red auto 5px !important;
}
SELECT.right {
	direction: rtl
}
.updated INPUT,
.updated SELECT,
.updated TEXTAREA {
	background-color: rgba(0,255,0,<?= $LITE ? .1 : .5 ?>) !important;
}
.basehl {
	border-radius: 2px;
	min-height: 28px;
	padding: 5px 8px 5px 8px;
	margin-bottom: .2em;
}
LABEL {
	user-select: none
}
LABEL.basehl.cbi,
.partyprice .basehl {
	display: inline-block;
	padding: 2px 6px 2px 6px;
}
.cbi.nicer {
	text-wrap: nowrap;
	width: 100%;
}
.nice-boxes .description,
.cbi .description {
	vertical-align: middle;
	display: inline-block;
	text-wrap: wrap;
	overflow-wrap: anywhere;
	margin-left: .5em;
	padding-right: 1em;
}
/* itempad and nobullets used in ubb */
UL.genre-checkboxes {
	<? styles::vendor_prefixed('column-width: 10em') ?>
}
.nice-boxes LABEL {
	display: block;
	white-space: nowrap;
	<? # By adding a bit of padding (not 0), Firefox does display checkboxes in requested amount of columns ?>
	padding: .1em;
	margin-bottom: 0;
}
.nice-boxes INPUT {
	vertical-align: middle;
}
.not-added.urlupload::placeholder {
	color: rgb(<?= $LITE ? '200,100,0' : '255,150,0' ?>) !important;
}
.yes-added.urlupload::placeholder {
	color: rgb(<?= $LITE ? '0,150,0' : '0,255,0' ?>) !important;
}
.prefilled {
	background-color: rgba(0,255,0,.1);
}
.optfilled {
	background-color: rgba(255,255,0,.2);
	color: #FFD600;
	font-weight: bold;
}
.prefilled INPUT,
.prefilled TEXTAREA {
	background-color: #030;
}
.optfilled INPUT,
.optfilled TEXTAREA {
	background-color: #440;
}
.optfilled SELECT {
	background-color: #330;
}
.optfilled TD + TD > SPAN {
	padding: 0 5px;
}
/*.samefilled INPUT,
.samefilled TEXTAREA {
	opacity: .4;
	background-color: #030;
}*/
.prefilled SELECT {
	background-color: #020;
}
.newfromfb .prefilled,
.genres .prefilled {
	color: #0F0;
}
.newprice INPUT[name^='PRICE_AMOUNT'] {
	background-color: green !important;
}
.newprice INPUT[name^='PRICE_AMOUNT']::placeholder {
	color: white !important;
}
.warning SELECT {
	color: orange;
	font-weight: bold;
}
FORM .lineup SELECT.type {
	min-width: 3em
}

.filter-header TD {
	padding-bottom: .5em;
}
.connectboxbody TD.field + TD,
FORM TD.field + TD {
	width: 100%
}
