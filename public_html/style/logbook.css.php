<?php

require_once '_styles.inc';
require_once '../_browser.inc';

$style = new styles('style/logbook.css.php');
/** @noinspection PhpRedundantOptionalArgumentInspection */
extract(styles::$styles, \EXTR_OVERWRITE);

?>
.logbook {
	border: 1px solid rgba(<?= $LITE ? '0,0,0,.2' : '255,255,255,.3' ?>);
}
.logbook .content {
	max-height: 40em;
	overflow-y: auto;
	margin: .4em;
}
.logbody,
.logopts {
	margin: 0 2em
}
.logfield {
	font-weight: bold;
}
.logelement {
	font-weight: 300;
}
.logaddate {
	font-style: italic;
	font-size: 80%;
/*	float: right;*/
}
.loguser {
	margin: 0 1em;
	font-weight: 700;
}
.loguser A {
	color: <?= $LITE ? 'rgb(150,150,0)' : 'yellow' ?>;
}
.newlog {
	background-color: rgba(0,150,0,.2) !important;
}
