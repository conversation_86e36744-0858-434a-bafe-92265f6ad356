<?php

require_once '_styles.inc';
require_once '../_hosts.inc';
require_once '../_browser.inc';
require_once '../_smallscreen.inc';

$style = new styles('style/appic_requests.css.php');

extract($style->get());

?>
.appic-changes TABLE CAPTION {
	text-align: left;
}
.appic-changes .action-header {
	font-weight: bold;
	font-size: 110%;
}
.appic-changes .item-removed {
	font-weight: bold;
	font-size: 120%;
	color: #A61;
}
.appic-changes .event-name {
	font-weight: bold;
	font-size: 120%;
}
.appic-changes .event-name A,
.appic-changes .event-name .subtitle {
	color: #<?= $LITE ? '0A0' : '3F3' ?>;
}
.appic-changes .request {
	padding: 2px;
}
.appic-changes .requests {
	margin-bottom: 1em;
}
.appic-changes .action-act {
	width: 16px;
	cursor: pointer;
}
.appic-changes .action-accept {
	color: green;
}
.appic-changes .action-decline {
	color: red;
}
.appic-changes .action-exists {
	color: yellow;
}
.appic-changes .action-accept,
.appic-changes .action-decline {
	width: 1em;
	text-align: center;
	user-select: none;
}
.appic-changes .active-item {
	font-size: 150%;
	background-color: #<?= $LITE ? '5A5' : '050' ?>;
	position: fixed;
	padding: .2em .5em;
	margin: .5em;
	top: 0;
	right: 0;
	border: 1px solid yellow;
	border-radius: 3px;
	z-index: 100;
}
.appic-changes #element-name {
	font-size: 50%;
	vertical-align: .25em;
}
.appic-changes #item-link {
	font-weight: 700;
}
.appic-changes .active-item .spc2 IMG {
	vertical-align: baseline;
}
