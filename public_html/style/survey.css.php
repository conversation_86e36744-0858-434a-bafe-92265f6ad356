<?php

require_once '_styles.inc';
require_once '../_smallscreen.inc';
require_once '../_hosts.inc';

$style = new styles('style/survey.css.php');
/** @noinspection PhpRedundantOptionalArgumentInspection */
extract(styles::$styles, \EXTR_OVERWRITE);

?>.matrixOpt {
	background-color:#<?= $LITE ? 'AAA' : '7f7f7f' ?>;
	background-color:rgba(<?= $LITE ? '0,0,0' : '255,255,255' ?>,.2);
	height: 1.2em
}
.matrixOpt INPUT {
	margin: 0 !important;
	vertical-align: .1em
}
DIV.matrixOpt.bar {
	border: 0
}
SELECT {
	color: black !important;
}
SELECT.bar {
	height: auto !important;
	margin: 2px 0;
	background: none
}
