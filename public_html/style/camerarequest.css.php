<?php

declare(strict_types=1);

require_once '_styles.inc';

$style = new styles('style/camerarequest.css.php');
/** @noinspection PhpRedundantOptionalArgumentInspection */
extract(styles::$styles, \EXTR_OVERWRITE);

?>
.cam-status {
	padding: .3em;
	margin: -.3em;
}

.cam-bg-msg					{ background-color: <?= $LITE ? '#CCC' : '#333' ?>; padding: .5em 1em .5em 1em; }
.cam-bg-take_over,
.cam-bg-accepted			{ background-color: <?= $LITE ? '#DFD' : '#060' ?>; }
.cam-bg-organization_request { background-color: <?= $LITE ? '#EFE' : '#030' ?>; }
.cam-bg-not_chosen			{ background-color: <?= $LITE ? '#FEE' : '#630' ?>; }
.cam-bg-no_response			{ background-color: <?= $LITE ? '#FED' : '#620' ?>; }
.cam-bg-not_written TD		{ opacity: .4; }
.cam-bg-not_written 		{ background-color: rgba(255,128,0,<?= $LITE ? .2 : .3 ?>); color: rgba(<?= $LITE ? '0,0,0,' : '255,255,255,' ?>,.5); }
.cam-bg-chosen,
.cam-bg-written,
.cam-bg-combined			{ background-color: <?= $LITE ? 'rgba(0, 0, 0, .1)' : 'rgba(255, 255, 255, .1)' ?>; }
.cam-bg-written-not_chosen	{ background-color: <?= $LITE ? '#FFE0C0' : '#643' ?>; }
.cam-bg-denied				{ background-color: <?= $LITE ? '#FDD' : '#600' ?>; }
.cam-bg-cancelled			{ background-color: <?= $LITE? 'rgba(255, 165, 0, .5)' : 'rgba(255, 165, 0, ,5)' ?>; }
.cam-bg-failed_organization,
.cam-bg-failed_photographer	{ background-color: <?= $LITE ? '#FFEFEF' : '#644' ?>; }
.cam-bg-self				{ background-color: <?= $LITE ? '#FAFFEE' : '#460' ?>; }
.cam-bg-extern				{ background-color: rgba(0, 255, 255, .2); }

.cam-status-transferred:visited,
.cam-status-transferred,
.cam-status-take_over:visited,
.cam-status-take_over,
.cam-status-accepted:visited,
.cam-status-accepted				{ color: <?= diff_color(0x00AA00) ?>; }
.cam-status-organization_request:visited,
.cam-status-organization_request	{ color: <?= $LITE ? '#080' : '#0A0' ?>; }
.cam-status-denied:visited,
.cam-status-denied					{ color: <?= diff_color(0xAA0000) ?>; }
.cam-status-self:visited,
.cam-status-self 					{ color: <?= diff_color(0xAAAA00) ?>; }
.cam-status-failed_organization:visited,
.cam-status-failed_orgnization,
.cam-status-failed_photographer:visited,
.cam-status-failed_photographer 	{ color: <?= diff_color(0xAA8080) ?>; }
.cam-status-written:visited,
.cam-status-written					{ color: <?= diff_color(0xFFFFFF) ?>; }
.cam-status-not_written:visited,
.cam-status-not_written				{ color: <?= $LITE ? 'rgba(0, 0, 0, .5)' : 'rgba(255, 255, 255, .5)' ?>; }
.cam-status-extern:visited,
.cam-status-extern					{ color: <?= $LITE ? '#0AA' : '#0FF' ?>; }
.cam-status-not_chosen:visited,
.cam-status-not_chosen				{ color: <?= $LITE ? '#A30' : '#F70' ?>; }

.intevents > TBODY > TR > TD {
	padding: .5em;
}
