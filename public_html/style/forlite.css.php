<?php

require_once '_styles.inc';
require_once '../_hosts.inc';
require_once '../_browser.inc';
require_once '../_smallscreen.inc';

$style = new styles('style/forlite.css.php');
/** @noinspection PhpRedundantOptionalArgumentInspection */
extract(styles::$styles, \EXTR_OVERWRITE);

?>.for-lite {
	color: #000
}
.for-lite .bg {
	background-color: #FFF
}
.for-lite A {
	color: rgba(0,0,0, .8);
}
.for-lite A:visited {
	color: rgba(0,0,0, .9);
}
.for-lite .hilited {
	background-color: #FFC !important
}
