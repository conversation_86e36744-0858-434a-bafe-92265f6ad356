<?php

require_once '_styles.inc';
require_once '../_browser.inc';

$style = new styles('style/votes.css.php');
/** @noinspection PhpRedundantOptionalArgumentInspection */
extract(styles::$styles, \EXTR_OVERWRITE);

?>.vote.text-ultra-green,
.vote.text-ultra-red,
.vote.text-super-green {
	font-weight: bold
}
<? include '_text_colors.inc'; ?>
.bar {
	margin: 0;
	height: 1.2em
}
.bar.ultra-red {
	background-color: red;
	border: 1px solid #<?= !$LITE ? 'FF3432' : 'CD0000' ?>
}
.bar.super-red {
	background-color: #FF1D00;
	border: 1px solid #<?= !$LITE ? 'FF4F32' : 'CD0000' ?>
}
.bar.red {
	background-color: #F40;
	border: 1px solid #<?= !$LITE ? 'FF7632' : 'CD1200' ?>
}
.bar.orange-red {
	background-color: #FF6900;
	border: 1px solid #<?= !$LITE ? 'FF9B32' : 'CD3700' ?>
}
.bar.orange {
	background-color: #FF8000;
	border: 1px solid #<?= !$LITE ? 'FFB232' : 'CD4E00' ?>
}
.bar.orange-green {
	background-color: #C89C00;
	border: 1px solid #<?= !$LITE ? 'FACE32' : '966A00' ?>
}
.bar.green {
	background-color: #77C400;
	border: 1px solid #<?= !$LITE ? 'A9F632' : '459200' ?>
}
.bar.super-green {
	background-color: #29EA00;
	border: 1px solid #<?= !$LITE ? '5BFF32' : '00B800' ?>
}
.bar.ultra-green {
	background-color: #0F0;
	border: 1px solid #<?= !$LITE ? '32FF32' : '00CD00' ?>
}
.vote {
	white-space: nowrap
}
.vote-options SPAN {
	border: 1px solid rgba(<?= $LITE ? '0,0,0' : '255,255,255' ?>,.1);
	white-space: nowrap;
	margin-bottom: 2px;
	border-radius: 2px;
	line-height: 36px;
	padding: 5px 10px;
	background-color: <?= $LITE ? '#F2F2F2' : '#191919' ?>;
/*	transition: border 0.15s;
	transition: background-color 0.15s; 
	text-decoration: none;
	cursor: pointer;
}

.vote-options SPAN:hover {
	<? styles::vendor_prefixed('filter: brightness('.($LITE ? '110' : '150').'%)') ?>;
}
.bar.bold {
	color: black;
	border: transparent;
}
