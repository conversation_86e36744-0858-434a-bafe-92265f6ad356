<?php

require_once '_styles.inc';
require_once '../_browser.inc';

$style = new styles('style/karma.css.php');
/** @noinspection PhpRedundantOptionalArgumentInspection */
extract(styles::$styles, \EXTR_OVERWRITE);

?>
.karma.inactive {
	margin-right: .5em;
}

.karma.active IMG {
	cursor: pointer !important
}
.karma IMG {
	width: 16px;
	height: 16px;
	margin: 0 .3em;
	vertical-align: -2px
}
.karma IMG.inactive {
	opacity: .6
}
@media (min-resolution: 2dppx) {
	.karma * {
		font-weight: 300;
	}
} /* endmedia */
