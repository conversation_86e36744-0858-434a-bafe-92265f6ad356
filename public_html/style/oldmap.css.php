<?php

require_once '_styles.inc';
require_once '../_browser.inc';

$style = new styles('style/oldmap.css.php');
/** @noinspection PhpRedundantOptionalArgumentInspection */
extract(styles::$styles, \EXTR_OVERWRITE);

?>.mapclickinfo,
.overcitytext {
	position: absolute;
	background-color: <?= diff_color(0xFFFFEE); ?>;
	border: 1px solid <?= diff_color(0x555544); ?>;
	padding: .2em;
	width: auto;
	z-index: 2
}
.overcitytext A,
.overcitytext A:visited,
.overcitytext A:active,
.overcitytext A:hover {
	color: <?= diff_color(0x222211); ?>
}
.mousemark {
	position: absolute;
	left: 0;
	top: 0
}
