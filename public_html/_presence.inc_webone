<?php

declare(strict_types=1);

const ELEMENTS_WITH_PRESENCES = [
	'artist'		=> 'artist',
	'location'		=> 'location',
	'organization'	=> 'organization',
	'party'			=> 'party',
	'stream'		=> 'stream',
	'user'			=> 'user',
];

const PRFLG_THEMED	= 1;
const PRFLG_2x		= 2;
const PRFLG_OFFLINE	= 4;
const PRFLG_V2		= 8;

function get_presence_flags(string $type): int|false {
	return ($presence_spec = get_all_presences($type))[0] ?? false;
}

function get_all_presences(?string $type = null): ?array {
	static $__presence_specs = [
		# numbers
		'101barz'	=> [PRFLG_OFFLINE,				'(\b101barz\.bnn\.nl/page/profiel/[\da-f]+|[\w\d_\-]+\.(?<!www\.)101barz\.nl)'],
		'123video'	=> [PRFLG_OFFLINE,				'\b[\w\d_\-]+\.(?<!www\.)123video\.nl\b'],
		'123website'	=> [PRFLG_OFFLINE,				'\b123website\.nl/[\w\d\-\+\._]+'],
		'2day'		=> [PRFLG_OFFLINE,				'\b2day\.nl/[\w\d\-\+\._]+'],
		'2night'	=> [PRFLG_OFFLINE,				'\b2night\.nl/[\w\d\-\+\._]+'],
		'3voor12'	=> [PRFLG_OFFLINE|PRFLG_THEMED,			'\b3voor12\.vpro\.nl/artiesten/artiest/\d+'],
		'4us'		=> [PRFLG_OFFLINE,				'\b4us\.vg/member\.php\?u=\d+'],
		'500px'		=> [PRFLG_THEMED,				'\b500px\.com/[\w\d\-\+\._]+'],

		# A
		'about.me'	=> [0,						'\babout\.me/[\w\d\-\+\._]+'],
		'agendainfo'	=> [0,						'\bagendainfo\.nl/user/(\d+)/'],
		'almere4you'	=> [PRFLG_OFFLINE,				'\b[\w\d_\-]+\.(?<!www\.)almere4you\.nl\b'],
		'alphen'	=> [PRFLG_OFFLINE,				'\balphen\.nu/\?p=profiel&pid=\d+'],
		'altparty'	=> [PRFLG_OFFLINE,				'\baltparty\.net/profile/[\w\d\-\+\._]+'],
		'andrelon'	=> [PRFLG_OFFLINE,				'\bactie\.andrelon\.nl/#/gallerij/\d+'],
		'apeldoorner'	=> [PRFLG_OFFLINE,				'\bapeldoorner\.com/[\w\d\-\+\._]+'],
		'appstore'	=> [PRFLG_2x,					'\bitunes\.apple\.com/app/[\w\d\-\+_\.]+/id\d+'],
		'artistdata_sonicbids'
				=> [PRFLG_OFFLINE,				'\bartistdata\.sonicbids\.com/[\w\d\-\+\._]+'],
		'asn-online'	=> [PRFLG_OFFLINE,				'\basn\-online\.nl/community/profiles/view/\d+'],
		'atlastfm'	=> [PRFLG_OFFLINE,				'\batlast\.fm/[\w\d\+\-\._]+'],
		'audiogenic'	=> [PRFLG_OFFLINE|PRFLG_THEMED,			'\baudiogenic\.fr/\d+/artist\-[\w\d\+\-\._]+'],
		'audiomack'	=> [0,						'\baudiomack.com/artist/.+'],
		'autoblog'	=> [0,						'\bautoblog\.nl/gebruikers/[\w\d\-\+\._]+'],
		'awdio'		=> [PRFLG_OFFLINE,				'\bawdio\.com/[\w\d\-\+\._]+'],
		'audiojelly'	=> [PRFLG_OFFLINE,				'\baudiojelly\.com/artists/[\w\d\-\+\.]+/\d+'],

		# B
		'babes-dudes'	=> [PRFLG_OFFLINE,				'\bbabes\-dudes\.nl/[\w\d\+\-\._;]+'],
		'babybytes'	=> [0,						'\bbabybytes\.nl/vip/[\w\d\+\-\._]+'],
		'badoo'		=> [PRFLG_OFFLINE,				'\bbadoo\.com/[\w\d\-\+\._]+'],
		'back2noize'	=> [PRFLG_OFFLINE,				'\bback2noize\.com/memberlist\.php\?mode=viewprofile&u=\d+'],
		'bambuser'	=> [PRFLG_OFFLINE,				'\bbambuser\.com/[\w\d\-\+\._]+'],
		'bandcamp'	=> [PRFLG_2x,						'\b(?:[\w\d_\-]+\.(?<!www\.)bandcamp\.com|bandcamp\.com/[\w\d]+)\b'],
		'baszdrome'	=> [PRFLG_OFFLINE,				'\bbaszdrome\.com/info/djdetails\.php\?djid=\d+'],
		'battlefieldheroes'
				=> [0,						'\bbattlefieldheroes\.com/[a-z]{2,3}/player/\d+'],
		'beatport'	=> [PRFLG_2x,						'\bbeatport\.com/[\w\d\-\+\._]+'],
		'beatportal'	=> [PRFLG_OFFLINE,				'\bbeatportal\.com/artists/[\w\d\-\+\._]+'],
		'beatsdigital'	=> [PRFLG_OFFLINE,				'\bbeatsdigital\.com/label/[\w\d\-\+\._]+/\d+'],
		'beatstars'	=> [PRFLG_2x,					'(?:\bbeatstars\.com/[\w\d\-\+\._]+|[\w\d_\-]+\.(?<!www\.)beatstars.com/?$)'],
		'bebo'		=> [PRFLG_OFFLINE,				'\bbebo\.com/[\w\d\-\+\._]+'],
		'behance'	=> [PRFLG_2x,					'\bbehance\.net/[\w\d\-\+\._]+'],
		'betribes'	=> [PRFLG_OFFLINE,				'\bbetribes\.com/[\w\d\-\+\._]+'],
		'bimeiden'	=> [PRFLG_OFFLINE,				'\bbimeiden\.nl/index\.php\?action=profile;u=\d+'],
		'blackbunny'	=> [PRFLG_OFFLINE,				'\bblackbunny\.nl/[\w\d\-\+\._]+'],
		'blip.fm'	=> [PRFLG_OFFLINE,				'\bblip\.fm/[\w\d\-\+\._]+'],
		'blog-video'	=> [PRFLG_OFFLINE|PRFLG_2x,			'\b[\w\d_\-]+\.(?<!www\.)blog\-video\.tv\b'],
		'blogg'		=> [PRFLG_OFFLINE,				'\b[\w\d_\-]+\.(?<!www\.)blogg\.se\b'],
		'blogger'	=> [PRFLG_2x,					'\b(?:[\w\d\-\+\._]+\.(?<!www\.)(?:blogspot|blogger)\.|blogger\.com/profile/\d+)'],
		'blogsport'	=> [PRFLG_OFFLINE,				'\b[\w\d_\-]+\.(?<!www\.)blogsport\.de\b'],
		'bokt'		=> [0,						'\bbokt\.nl/forums/memberlist\.php\?mode=viewprofile&u=\d+'],
		'bol'		=> [0,						'\bbol\.com/\w{2,3}/2ndhand/2ndhand_seller\.html\?sellerId=\d+|\bbol\.com/.+'],
		'bolo'		=> [PRFLG_OFFLINE,				'\bbolo\.nl/artiest/[\w\d\-\+\._]+'],
		'boomr'		=> [PRFLG_OFFLINE|PRFLG_THEMED,			'\bboomr\.nl/[\w\d\-\+\._]+'],
		'bramosia'	=> [PRFLG_OFFLINE,				'\bbramosia\.nl/profiel/\d+'],
		'break'		=> [PRFLG_OFFLINE,				'\bbreak\.com/user/[\w\d\-\+\._]+'],
		'bricklink'	=> [PRFLG_OFFLINE,				'\bbricklink\.com/store\.asp\?p=[\w\d\-\+\._]+'],
		'brightkite'	=> [PRFLG_OFFLINE,				'\bbrightkite\.com/people/[\w\d\-\+\._]+'],
		'brommerforum'	=> [PRFLG_OFFLINE,				'\bbrommerforum\.nl/user/\d+'],
		'bulletstar'	=> [PRFLG_OFFLINE,				'\bbulletstar\.(?:net|de)/\?p=profile&g=[\w\d\-\+\._]+'],
		'buurmeisje'	=> [PRFLG_OFFLINE,				'\bbm2011\.nl/profiel/\d+'],
		'burn-studios'	=> [PRFLG_OFFLINE,				'\bburn\-studios\.com/profile/[\w\d\-\+\._]+'],
		'buzznet'	=> [PRFLG_OFFLINE,				'\b[\w\d_\-]+\.(?<!www\.)buzznet\.com/user/'],

		# C
		'cdbaby'	=> [PRFLG_OFFLINE,				'\bcdbaby\.com/cd/[\w\d\-\+\._]+'],
		'chatgirl'	=> [PRFLG_OFFLINE,				'\b[\w\d_\-]+\.(?<!www\.)chatgirl\.nl\b'],
		'chathoek'	=> [PRFLG_OFFLINE,				'\bchathoek\.nl/\d+\-'],
		'chatnu'	=> [PRFLG_OFFLINE,				'\bchatnu\.nl/users/profile/\d+'],
		'chattijd'	=> [PRFLG_OFFLINE,				'\bprofielen\.chattijd\.nl/\?profiel=[\w\d\-\+\._]+'],
		'civicclubholland'
				=> [PRFLG_OFFLINE,				'\bcivicclubholland\.nl/forum/memberlist\.php\?mode=viewprofile&u=\d+'],
		'clubcharts'	=> [PRFLG_OFFLINE,				'\bclubcharts\.nl/memberlist\.php\?mode=viewprofile&u=\d+'],
		'come2me'	=> [PRFLG_OFFLINE,				'\b[\w\d_\-]+\.(?<!www\.)come2me\.nl\b'],
		'corebay'	=> [PRFLG_OFFLINE,				'\bcorebay\.com/index\.php\?showuser=\d+'],
		'coretime'	=> [PRFLG_OFFLINE,				'\bcoretime\.fm/member/\d+'],
		'couchsurfing'	=> [PRFLG_OFFLINE,				'\bcouchsurfing\.org/people/[\w\d\-\+\._]+'],
		'cu2'		=> [PRFLG_OFFLINE,				'\bcu2\.nl/[\w\d\-\+\._]+'],

		# D
		'd2jsp'		=> [0,						'\bd2jsp\.org/user\.php\?i=\d+'],
		'dailybooth'	=> [PRFLG_OFFLINE,				'\bdailybooth\.com/[\w\d\-\+\._]+'],
		'dailymotion'	=> [PRFLG_2x,					'\bdailymotion\.com/[\w\d\-\+\._]+'],
		'dance.nl'	=> [PRFLG_OFFLINE,				'\bdance\.nl/members/[\w\d\-\+\._]+\.html'],
		'dance-tunes'	=> [PRFLG_OFFLINE,				'\b(?:dance\-tunes\.com/#?home/(?:artist|label)/\d+|dancetunesradio\.com/tag/[\w\d\-\+\._]+)'],
		'dancegids'	=> [PRFLG_OFFLINE,				'\bdancegids\.nl/(?:profiles/my\-profile/\d+|[\w\d\-\+\._]+)'],
		'danceproducer'	=> [PRFLG_OFFLINE,				'\bdanceproducer\.nl/profile/[\w\d\-\+\._]+'],
		'darkthrone'	=> [PRFLG_OFFLINE,				'\bdarkthrone\.com/viewprofile(?:\.dt\?.*?\bid=|/index/)\d+'],
		'decksharks'	=> [PRFLG_OFFLINE,				'\bdecksharks\.com/[\w\d\-_\+\.]+/'],
		'delicious'	=> [PRFLG_OFFLINE,				'\bdelicious\.com/[\w\d\-\+\._]+'],
		'demodrop'	=> [PRFLG_OFFLINE,				'\bdemodrop\.\w{2,3}\b'],
		'deviantart'	=> [0,						'\b[\w\d_\-\.\+]+\.(?<!www\.)deviantart\.'],
		'dg2'		=> [PRFLG_OFFLINE,				'\bdg2\.nl/.+'],
		'dhost'		=> [PRFLG_OFFLINE,				'\bdhost\.info/[\w\d\-\+\._]+'],
		'digg'		=> [PRFLG_OFFLINE,				'\bdigg\.com/[\w\d\-\+\._]+'],
		'digitalracing'	=> [PRFLG_OFFLINE,				'\bforum\.digitalracing\.nl/member\.php\?u=\d+'],
		'discogs'	=> [PRFLG_2x,					'\bdiscogs\.com/[\w\d\-\+\._]+'],
		'discord'	=> [0,						'\bdiscord(?:app)?\.com/invite/.+'],
		'disqus'	=> [0,						'\bdisqus\.com/[\w\d\-\+\._]+'],
		'dj-mixes'	=> [PRFLG_OFFLINE,				'\bdj\-mixes\.com/[\w\d\-\+\._]+'],
		'djbooth'	=> [0,						'\b(?:djbooth\.net/index/artists/info/|djbooth.net/artists/)[\w\d\-\+\._]+'],
		'djdownload'	=> [PRFLG_OFFLINE,				'\bdjdownload\.com/(?:labels|artist)/[\w\d\-\+\._]+/\d+'],
		'djfez'		=> [PRFLG_OFFLINE,				'\bdjfez\.com/djs/[\w\d\-\+\._]+'],
		'djguide'	=> [0,						'\bdjguide\.nl/(?:djinfo\.p\?djid|artistinfo\.p\?id|profielinfo\.p\?iduserprofile|locatiedetail\.p\?idlocation|organiserevents\.p\?id)=\d+'],
		'djlist'	=> [PRFLG_OFFLINE,				'\bthedjlist\.com/(?:djs|members|labels)/[\w\d\-\+\._]+'],
		'djresource'	=> [PRFLG_OFFLINE,				'\bdjresource\.eu/Profile/profileid/\d+'],
		'djscene'	=> [PRFLG_OFFLINE,				'\bdjscene\.nl/industry/Artiesten/item\?artisttype=\d+&artistid=\d+'],
		'djtunes'	=> [PRFLG_OFFLINE,				'\bdjtunes\.com/(?:#/)?[\w\d\-\+\._]+'],
		'drumandbass'	=> [0,						'\bdrumandbass\.nl/forum/index\.php\?action=profile[&;]u=\d+'],
		'dutchdancescene'
				=> [PRFLG_OFFLINE,				'\bdutchdancescene\.com/profile/[\w\d\-\+\._]+'],
		'dutchheaven'	=> [PRFLG_OFFLINE,				'\bdutchheaven\.nl/modules\.php\?name=Model_Details&op=userinfo&uname=[\w\d\-\+\._]+'],

		# E
		'ea_fc'		=> [PRFLG_OFFLINE,				'\bea\.com/nl/voetbal/mgd/player-profile/\d+'],
		'ebay'		=> [0,						'\b(?:shop\.ebay\.[a-z]{2,4}/[\w\d\-\+\._]+/m\.html|myworld\.(?:benl\.)?ebay\.\w{2,4}/[\w\d\-\+\._]+)'],
		'edmcommunity'	=> [PRFLG_OFFLINE,				'\bedmcommunity\.com/memberlist\.php\?mode=viewprofile&u=\d+\b'],
		'eigenwijsprodukties'
				=> [PRFLG_OFFLINE, 				'\beigenwijsprodukties\.nl/artiesten/[\w\d\-\+\._]+/'],
		'electrobel'	=> [0,						'\belectrobel\.org/profile\.ebel/\d+'],
		'esl'		=> [PRFLG_OFFLINE,				'\besl\.eu/eu/cs/4on4/mr15/ladder/player/\d+/'],
		'esnips'	=> [PRFLG_OFFLINE,				'\besnips\.com/user/[\w\d\-\+\._]+'],
		'examiner'	=> [PRFLG_OFFLINE,				'\bexaminer\.com/[\w\d\-\+\._]+'],

		# F
		'f1rejects'	=> [PRFLG_OFFLINE,				'\bf1rejects\.com/forum/memberlist\.php\?mode=viewprofile&u=\d+'],
		'facebookpage'	=> [PRFLG_2x,					'\bfacebook\.[a-z]{2,3}/(?:pages|pg)/.+'],
		'facebook'	=> [PRFLG_2x,					'\bfacebook\.[a-z]{2,3}/.+'],
		'factsonacts'	=> [PRFLG_OFFLINE,				'\bfactsonacts\.nl/catalog/profile/fetchone/[^/]+/\?k=\d+'],
		'fear.fm'	=> [PRFLG_OFFLINE,				'\bfear\.fm/(?:user|artist|radio/program)/[^/]+/\d+\b'],
		'feest.je'	=> [PRFLG_OFFLINE,				'\bfeest\.je/[\w\d\-\+\._]+'],
		'festuc'	=> [PRFLG_OFFLINE,				'\bfestuc\.com/dj\-[\w\d\-\+\._]+'],
		'fetlife'	=> [0,						'\bfetlife\.com/users/\d+'],
		'flavors'	=> [PRFLG_OFFLINE,				'\bflavors\.me/[\w\d\-\+\._]+'],
		'flickr'	=> [0,						'\bflickr\.com/(?:photos|people|groups)/[\w\d\-\+\._]+'],
		'flowd'		 => [PRFLG_OFFLINE,				'\bflowd\.com/(?:hot/)?[\w\d\-\+\._]+'],
		'fmylife'	=> [PRFLG_THEMED,				'\bfmylife\.com/user/\d+'],
		'formspring'	=> [PRFLG_OFFLINE | PRFLG_THEMED,		'\bformspring\.me/[\w\d\-\+\._]+'],
		'formule323club'=> [PRFLG_OFFLINE,				'\bformule323club\.nl/forum/memberlist\.php\?mode=viewprofile&u=\d+'],
		'forumer'	=> [PRFLG_OFFLINE,				'\b[\w\d_\-]+\.(?<!www\.)forumer\.com/index\.php\?showuser=\d+'],
		'fotografentop'	=> [PRFLG_OFFLINE,				'\bfotografentop\.nl/info/\d+(?::|\.html)'],
		'fotolog'	=> [PRFLG_OFFLINE,				'\bfotolog\.com/[\w\d\-\+\._]'],
		'fourartists'	=> [PRFLG_OFFLINE,				'\bfourartists\.com/[\w]{2,3}/kuenstler/\w/[\w\d\-\+\._]+\.html\b'],
		'foursquare'	=> [PRFLG_OFFLINE,				'\bfoursquare\.com/(?:user/)?[\w\d\-\+\._]+'],
		'fr12'		=> [0,						'\bfr12\.nl/(?:profiel/\d+|user/[\w\d\-\+_]_)'],
		'friend.ly'	=> [PRFLG_OFFLINE,				'\bfriend\.ly/profile\?(?:ref=[\w_]+&)?id=\d+'],
		'friendster'	=> [PRFLG_OFFLINE,				'\bprofiles\.friendster\.com/\d+'],
		'ftd'		=> [PRFLG_OFFLINE,				'\bforum\.ftd\.nu/member\.php\?u=\d+'],
		'fundalize'	=> [PRFLG_OFFLINE,				'\bfundalize\.com/profiel/[\w\d\-\+\._]+'],
		'funkybabes'	=> [PRFLG_OFFLINE,				'\bfunkybabes\.nl/[\w\d\-\+\._]+'],
		'fusionbv'	=> [PRFLG_OFFLINE,				'\bfusionbv\.com/community/memberlist\.php\?mode=viewprofile&u=\d+'],

		# G
		'gabber.fm'	=> [PRFLG_OFFLINE | PRFLG_2x,			'\bgabber\.fm/user\.php\?id\.\d+'],
		'gabbertube'	=> [PRFLG_OFFLINE,				'\bgabbertube\.com/profile/\d+\-[\w\d\-\+\._]+'],
		'gamesmeter'	=> [PRFLG_OFFLINE,				'\bgamesmeter\.nl/user/\d+'],
		'gamespot'	=> [0,						'\bgamespot\.com/profile/[\w\d\-\+\._]+/'],
		'gametrailers'	=> [PRFLG_OFFLINE,				'\bgametrailers\.com/users/[\w\d\-\+\._]+'],
		'gay'		=> [PRFLG_OFFLINE,				'\bgay\.nl/[\w\d\-\+\._]+'],
		'gaydar'	=> [PRFLG_OFFLINE,				'\bgaydar\.nl/[\w\d\-\+\._]+'],
		'gertlily'	=> [PRFLG_OFFLINE,				'\bgertlily\.co\.cc/home/<USER>'],
		'getglue'	=> [PRFLG_OFFLINE,				'\bgetglue\.com/[\w\d\-\+\._]+'],
		'getsnipper'	=> [PRFLG_OFFLINE,				'\bgetsnipper\.com/\w{2}/channel/[\w\d\-]+/'],
		'gigatools'	=> [PRFLG_OFFLINE,				'\bgigatools\.com/user/[\w\d\-\+\._]+'],
		'globalhardcore'=> [PRFLG_OFFLINE,				'\bglobalhardcore\.net/forum/memberlist\.php\?mode=viewprofile&u=\d+'],
		'globalhardtrance'
				=> [PRFLG_OFFLINE,				'\bglobalhardtrance\.com/forum/memberlist\.php\?mode=viewprofile&u=\d+'],
		'goatrance'	=> [0,						'\bgoatrance\.de/goabase/member/profile/[\w\d]+'],
		'goodreads'	=> [0,						'\bgoodreads\.com/user/show/\d+'],
		'google'	=> [PRFLG_2x,					'\b(?:google\.com/profiles/[\w\d\-\+\._]+|maps\.google\.com/maps/place\?cid=\d+|profiles\.google\.\w{2,4}/[\w\d\-\+\._]+)'],
		'googleplus'	=> [PRFLG_OFFLINE | PRFLG_2x,			'\b(?:plus\.google\.\w{2,3}/(?:(?:u/0/)?\d{10,}|\+[\w\d\-\+\._]+|u/.+)|gplus\.to/[\w\d\-\+\._]+)\b'],
		'googlesites'	=> [PRFLG_OFFLINE,				'\bsites\.google\.\w{2,3}/site/[\w\d\-\+\._]+/'],
		'gowalla'	=> [PRFLG_OFFLINE,				'\bgowalla\.com/spots/\d+'],
		'gravatar'	=> [0,						'\bgravatar\.com/[\w\d\-\+\._]+'],
		'grimepedia'	=> [PRFLG_OFFLINE | PRFLG_THEMED,		'\bgrimepedia\.co\.uk/wiki/[\w\d\-\+\._]+'],
		'guestzone'	=> [PRFLG_OFFLINE,				'\bguestzone\.nl/(member_info\.php\?member_id=|party_agenda\.php\?(?:organiser|(?:venuelink=\d+&)?venue)=)\d+'],

		# H
		'hardcoredates'	=> [PRFLG_OFFLINE,				'\bhardcoredates\.de/component/option,com_comprofiler/task,userProfile/user,\d+'],
		'harderstate'	=> [0,						'\bharderstate\.com/member/[\w\d\-\+\._]+'],
		'hardstyle'	=> [0,						'\bhardstyle\.com/[\w\d\-\+\._]+'],
		'hardtraxx'	=> [0,						'\bhardtraxx\.nl/forum/member/[\w\d\-\+\._]+'],
		'hardware.info'	=> [0,						'\bhardware\.info/profiel/\d+'],
		'h4h'		=> [PRFLG_OFFLINE,				'\bh4h\.fm/(?:[\w\d\-\+\._]+|user/[\w\d\-\+\._\*]+).html$'],
		'hearthis.at'	=> [PRFLG_THEMED,				'\bhearthis\.at/.+'],
		'hi5'		=> [0,						'\bhi5\.com/[\w\d\-\+\._]+'],
		'highdeas'	=> [0,						'\bhighdeas\.com/users/[\w\d\-\+\._]+'],
		'hiphopinjesmoel'=>[PRFLG_OFFLINE,				'\bhiphopinjesmoel\.com/members/\d+'],
		'homo.nl'	=> [PRFLG_OFFLINE | PRFLG_2x,			'\b[\w\d_\-]+\.(?<!www\.)homo\.nl\b'],
		'hotfrog'	=> [0,						'\bhotfrog\.\w{2,4}/Companies/[\w\d\-\+\._]+'],
		'house-mixes'	=> [0,						'\bhouse\-mixes\.com/(?:artists|profile)/[\w\d\-\+\._]+'],
		'hyves'		=> [PRFLG_OFFLINE,				'(?:[\w\d_\-]+\.(?<!www\.)hyves\.(?:nl|net)|www\.hyves\.nl/(?:hyves|spot|agenda)/\d+/.+)'],

		# I
		'ibiza-voice'	=> [PRFLG_OFFLINE,				'\bibiza-voice\.com/music/artist/[\w\d\-\+\._]+'],
		'icq'		=> [0,						'\bicq\.com/people/\d+'],
		'ididid'	=> [PRFLG_OFFLINE,				'\bididid\.eu/groups/\d+'],
		'idj'		=> [PRFLG_THEMED,				'\bidj\.cz/[\w\d\-\+\._]+'],
		'iens'		=> [PRFLG_OFFLINE,				'\biens\.nl/restaurant/\d+'],
		'ilike'		=> [PRFLG_OFFLINE,				'\bilike\.com/(?:user|artist)/[\w\d\-\+\._]+'],
		'imageshack'	=> [0,						'\bimageshack\.com/user/.+'],
		'imdb'			=> [0,						'\bimdb\.com/name/nm\d+'],
		'imikimi'	=> [PRFLG_OFFLINE,				'\bimikimi\.com/[\w\d\-\+\._]+'],
		'imvu'		=> [PRFLG_OFFLINE,				'\bavatars\.imvu\.com/[\w\d\-\+\._]+'],
		'insidegamer'	=> [PRFLG_OFFLINE,				'\binsidegamer\.nl/members/\d+'],
		'instagram'	=> [PRFLG_2x | PRFLG_V2,			'\binstagram\.com/(?:p/)?[\w\d\-\+\._]+'],
		'inthemix'	=> [PRFLG_OFFLINE,				'\binthemix\.com\.au/(?:allabout/artist/\d+|people/[\w\d\-\+\._]+)'],
		'is-music'	=> [PRFLG_OFFLINE,				'\b[\w\d\-]+\.(?<!www\.)is-music\.net\b'],
		'itsmyurls'	=> [PRFLG_OFFLINE,				'\bitsmyurls\.com/[\w\d\-\+\._]+'],
		'itunes'	=> [PRFLG_THEMED | PRFLG_2x,			'\b(?:itunes\.com/[\w\d\-\+\._]+|(?:itunes|music)\.apple\.com/(?:(?:\w{2,4}/)?(?:(?:podcast|artist|curator)/[\w\d\-\+\._%]+|profile/id\-\d+)|.*?viewPodcast\?id=\d+))\b'],
		'iwiw'		=> [PRFLG_OFFLINE | PRFLG_THEMED,		'\biwiw\.hu/pages/user/userdata\.jsp\?userID=\d+'],

		# J
		'jamendo'	=> [0,						'\bjamendo\.com/(?:\w{2,4}/)?(?:user|artist)/[\w\d\-\+\._]+/'],
		'jappy'		=> [PRFLG_OFFLINE,				'\bjappy\.de/user/[\w\d\-\+\._]+'],
		'juno'		=> [0,						'\bjuno\.co\.uk/artists/[\w\d\-\+\._]+'],
		'junodownload'	=> [0,						'\bjunodownload\.com/(?:artists|labels|products)/[\w\d\-\+\._]+'],

		# K
		'kamasutrabeurs'=> [0,						'\bkamasutrabeurs\.nl/leden/.+/'],
		'kat.ph'	=> [PRFLG_OFFLINE,				'\bkat\.ph/user/[\w\d\-\+\._]+'],
		'kickasstorrents'=>[PRFLG_OFFLINE,				'\bkickasstorrents\.com/user/[\w\d\-\+\._]+/'],
		'king'		=> [PRFLG_OFFLINE,				'\bking\.com/community/profile\.jsp\?username=[\w\d\-\+\._]+'],
		'kvk_hallo'	=> [PRFLG_OFFLINE,				'\bhallo\.kvk\.nl/hallo/ondernemers/[a-z]+/default\.aspx\b'],
		'komi'			=> [PRFLG_2x,				'\b[a-z]+.komi\.io/'],

		# L
		'labelsbase'	=> [0,						'\blabelsbase\.net/.+'],
		'labyrinthbookings'
				=> [PRFLG_OFFLINE,				'\blabyrinthbookings\.com/agency/artists/[\w\d\-]+'],
		'lastfm'	=> [0,						'\blast(?:fm\.\w{2,4}|\.fm)/(?:listen/)?(?:user|music|venue|group|event)/[\w\d\-\+\._%]+'],
		'legaldownload.net'
				=> [PRFLG_OFFLINE,				'\blegaldownload\.net/[\w\d\-\+\._]+'],
		'lethal-zone'	=> [PRFLG_OFFLINE,				'\blethal\-zone\.eu/memberlist\.php\?mode=viewprofile&u=\d+'],
		'letsmix'	=> [PRFLG_OFFLINE,				'\bletsmix\.com/[\w\d\-\+\._]+'],
		'link2party'	=> [PRFLG_OFFLINE,				'\blink2party\.nl/(?:\w+/)?[\w\d\-\+\._]+'],
		'linkedin'	=> [0,						'\b(?:linkedin\.com/[\w\d\-\+\._]+|linkd\.in/[\w\d]+)'],
		'linkin.bio'	=> [PRFLG_2x,			'\blinkin\.bio/[a-z\d]+'],
		'linktree'	=> [0,						'\blinktr.ee/[\w\d\-\+\._]+'],
		'listen2myradio'=> [PRFLG_OFFLINE,				'\b[\w\d\-\+\._]+\.listen2myradio\.com\b'],
		'live'		=> [PRFLG_OFFLINE,				'\b(?:[\w\d\-\+\._]+\.profile\.live\.com|profile\.live\.com/cid-[a-f\d]+)\b'],
		'livedoor'	=> [PRFLG_OFFLINE,				'\bblog\.livedoor\.jp/[\w\d\-\+\._]+'],
		'livejournal'	=> [0,						'\b[\w\d\-\+\._]+\.livejournal\.com\b'],
		'livemocha'	=> [PRFLG_OFFLINE,				'\blivemocha\.com/profiles/view/\d+'],
		'livenation'	=> [PRFLG_OFFLINE,				'\b(?:livenation\.com/[\w\d\-]+/venue/\d+|vipnation\.livenation\.com/venue/[\w\d\-]+)'],
		'livesets'	=> [PRFLG_OFFLINE,				'\blivesets\.com/forum/user/\d+'],
		'livestream'	=> [PRFLG_OFFLINE,				'\blivestream\.com/[\w\d\-\+\._]+'],
		'lockerz'	=> [PRFLG_OFFLINE,				'\blockerz\.com/gallery/\d+'],
		'lololyrics'	=> [0,						'\blololyrics\.com/user/[\w\d\-\+\._]+'],
		'lookbook'	=> [0,						'\blookbook\.nu/[\w\d\-\+\._]+'],
		'lookilike'	=> [PRFLG_OFFLINE,				'\blookilike\.com/models/overview/profile/[\w\d\-\+\._]+'],
		'looptijden'	=> [PRFLG_OFFLINE,				'\blooptijden\.nl/lopers/[\w\d\-\+\._]+'],
		'lovegirlz'	=> [PRFLG_OFFLINE,				'\blovegirlz\.nl/(?:[\w\d\-\+\._]|%\d+)+'],
		'lsdb'		=> [0,						'\blsdb\.\w{2,4}/(?:user/[a-z\d]+|artists/view/\d+)'],

		# M
		'marktplaats'	=> [PRFLG_OFFLINE,				'\bverkopers\.marktplaats\.nl/\d+'],
		'maxmodels'	=> [PRFLG_OFFLINE,				'\bmaxmodels\.nl/models/fashion\-men/[\w\d\-\+\._]+/\d+/\d+'],
		'meetme'	=> [PRFLG_OFFLINE | PRFLG_THEMED | PRFLG_2x,	'\bmeetme\.com/member/\d+'],
		'megavideo'	=> [PRFLG_OFFLINE,				'\bmegavideo\.com/[\w\d\-\+\._\?\=]+'],
		'meinvz'	=> [PRFLG_OFFLINE,				'\bmeinvz\.net/Profile/[\w\d\-\+\._]+'],
		'memories4you'	=> [PRFLG_OFFLINE,				'\bmemories4you\.nl/index\.php\?option=com_comprofiler&task=userProfile&user=\d+'],
		'messenger'	=> [PRFLG_2x,					'//(?:m\.me/.+|(?:www\.)?messenger\.com/t/[^/&]+/?)$'],
		'metacafe'	=> [PRFLG_OFFLINE,				'\bmetacafe\.com/channels/[\w\d\-\+\._]+/'],
 	 	'metal-archives'=> [0,						'\bmetal\-archives\.com/band\.php\?id=\d+'],
		'mijnalbum'	=> [0,						'\b[\w\d\-]+\.(?<!www\.)mijnalbums?\.nl\b'],
		'ministryofsound'=>[PRFLG_OFFLINE | PRFLG_THEMED,		'\bministryofsound\.com(?:\.au)?/music/artist/dj\-[\w\d\-\+\._]+'],
		'minus'		=> [PRFLG_OFFLINE,				'\b[\w\d\-]+\.(?<!www\.)minus\.com\b'],
		'mix.dj'	=> [PRFLG_OFFLINE | PRFLG_2x | PRFLG_THEMED,	'\bmix\.dj/[\w\d\-\+\._]+'],
		'mixcrate'	=> [PRFLG_OFFLINE,				'\bmixcrate\.com/[\w\d\-\+_]+'],
		'mixcloud'	=> [PRFLG_2x,					'\bmixcloud\.com/[\w\d\-\+\._%]+'],
		'mixify'	=> [PRFLG_OFFLINE | PRFLG_2x,			'\bmixify\.com/[\w\d\-\+\._]+'],
		'mixlr'		=> [PRFLG_OFFLINE | PRFLG_2x,			'\bmixlr\.com/[\w\d\-\+\._]+'],
		'modellennet'	=> [0,						'\bmodellennet\.be/profile_det\.php\?type=model\&pid=\d+'],
		'modelmayhem'	=> [PRFLG_THEMED,				'\bmodelmayhem\.com/[\w\d\-\+\._]+'],
		'motorstek'	=> [PRFLG_OFFLINE,				'\b(?:[\w\d\-]+\.(?<!www\.)motorstek\.nl|motorstek\.nl/gebruiker/[\w\d\-]+)\b'],
		'moviemeter'	=> [0,						'\bmoviemeter\.nl/user/\d+'],
		'msnsexdate'	=> [PRFLG_OFFLINE,				'\bmsnsexdate\.com/profiel/[\w\d\-\+\._]+\.html\b'],
		'multiply'	=> [PRFLG_OFFLINE,				'\b[\w\d\-]+\.(?<!www\.)multiply\.com\b'],
		'music2dance'	=> [0,						'\bmusic2dance\.com/index\.php\?m=profiel&id=\d+'],
		'musicfrom'	=> [PRFLG_OFFLINE,				'\bmusicfrom\.nl/artiesten/\d+'],
		'musicmeter'	=> [0,						'\bmusicmeter\.nl/user/\d+'],
		'myanimelist'	=> [0,						'\bmyanimelist\.net/profile/[\w\d\-\+\._]+'],
		'mydjspace'	=> [PRFLG_OFFLINE,				'\bmydjspace\.com/[\w\d\-\+\._]+'],
		'myebook'	=> [PRFLG_OFFLINE | PRFLG_THEMED,		'\bmyebook\.com/[\w\d\-\+\._]+'],
		'mygamercard'	=> [PRFLG_OFFLINE,				'\bprofile\.mygamercard\.net/[\w\d\-\+\._%]+'],
		'mygb'		=> [PRFLG_OFFLINE,				'\b[\w\d\-]+\.(?<!www\.)mygb\.nl\b'],
		'myname'	=> [PRFLG_OFFLINE,				'\bmyname\.is/[\w\d\-\+\._]+'],
		'mypodcast'	=> [PRFLG_OFFLINE,				'\b[\w\d\-]+\.(?<!www\.)mypodcast\.com\b'],
		'myspace'	=> [PRFLG_THEMED| PRFLG_2x,			'\bmyspace\.\w{2,4}/[\w\d\-\+\._]+'],
		'myvideo'	=> [PRFLG_OFFLINE,				'\bmyvideo\.\w{2,4}/channel/[\w\d\-\+\._]+'],

		# N
		'netlog'	=> [PRFLG_OFFLINE | PRFLG_THEMED,		'\bnetlog\.\w+/[\w\d\-\+\._]+'],
		'new-bambu'	=> [PRFLG_OFFLINE,				'\bnew\-bambu\.com/start\.php/profil/[\w\d\-\+\._]+'],
		'newunderground'=> [PRFLG_OFFLINE,				'\bnewunderground\.nl/deejay/[\w\d\-\+\._]+/\d+'],
		'nike'		=> [PRFLG_OFFLINE,				'\bmy\.nike\.com/[\w\d\-\+\._\s]+'],
		'ning.danceproducer'
				=> [PRFLG_OFFLINE,				'\bdanceproducer\.ning\.com/profile/[\w\d\-\+\._]+'],
		'nogeno'	=> [PRFLG_OFFLINE | PRFLG_THEMED,		'\bnogeno\.com/[\w\d\-\+\._]+'],
		'noisetrade'	=> [PRFLG_OFFLINE,				'\bnoisetrade\.com/[\w\d\-\+\._]+'],
		'noxa'		=> [0,						'\bnoxa\.net/[\w\d\-\+\._]+'],
 		'nujij'		=> [PRFLG_OFFLINE,				'\bnujij\.nl/[\w\d\-\+\._]+\.\d+\.lynkx'],

		# O
		'obamiconme'	=> [PRFLG_OFFLINE,				'\bobamiconme\.pastemagazine\.com/profiles/[\w\d\-\+\._]+'],
		'official.fm'	=> [PRFLG_OFFLINE | PRFLG_THEMED,		'\b[\w\d\-]+\.(?<!www\.)official\.fm\b'],
		'officialpokerrankings'
				=> [PRFLG_OFFLINE,				'\bofficialpokerrankings\.com/pokerstars/[\w\d\-\+\._]+'],
		'oinovosom'	=> [PRFLG_OFFLINE,				'\boinovosom.com.br/[\w\d\-\+\._]+'],
		'oldschoolgabbers'
				=> [PRFLG_OFFLINE | PRFLG_THEMED,		'\boldschoolgabbers\.nl/user\.php\?id\.\d+'],
		'onesheet'	=> [PRFLG_OFFLINE | PRFLG_2x,			'\bonesheet\.com/[\w\d\-\+\._]+'],
		'ongekendtalent'=> [PRFLG_OFFLINE,				'\bongekendtalent\.nl/band/[\w\d\-\+\._]+'],
		'onlyfriends'	=> [PRFLG_OFFLINE,				'\bonlyfriends\.be/forum/memberlist\.php\?mode=viewprofile&u=\d+'],
		'orkut'		=> [PRFLG_OFFLINE,				'\borkut\.com(?:\.br)?/(?:Main#)?Profile\?uid=\d+'],
		'ourstage'	=> [0,						'\bourstage\.com/epk/[\w\d\-\+\._]+'],
		'outlar'	=> [PRFLG_OFFLINE | PRFLG_2x,			'\boutlar\.com/artist\.php\?id=\d+'],

		# P
		'panoramio'	=> [PRFLG_OFFLINE,				'\bpanoramio\.com/user/[\w\d\-\+\._]+'],
		'party-babe'	=> [PRFLG_OFFLINE,				'\bparty\-babe\.be/\w+/fotograaf/\w+'],
		'partydb'	=> [PRFLG_OFFLINE,				'\bpartydb\.nl/user/[\w\d\-\+\._]+\.html'],
		'partyflock'	=> [0,						'\bpartyflock\.nl/(?:artist|dj|user)/\d+'],
		'partyfreaker'	=> [0,						'\bpartyfreaker\.nl/profile/[\w\d\-\+\._]+'],
		'partygood'	=> [PRFLG_OFFLINE | PRFLG_THEMED,		'\bpartygood\.nl/index\.php/members/mijn\-profiel/userprofile/[\w\d\-\+\._]+'],
		'partyguide.be'	=> [PRFLG_OFFLINE,				'\bpartyguide\.be/[a-z]+/members/\d+/'],
		'partypeeps2000'=> [PRFLG_OFFLINE,				'\b(?:partypeeps2000|pp2g)\.(?:com|nl)/(?:members/[\w\d\-\+\._]+(?:\.aspx)?|Artist/\d+)'],
		'partyscene'	=> [PRFLG_OFFLINE,				'\bpartyscene\.nl/(?:pages\.php\?page=1063&uid=\d+|community/profiel\?uid=\d+)'],
		'patreon'	=> [0,						'\bpatreon\.com/.+'],
		'pfsquad'	=> [PRFLG_OFFLINE | PRFLG_THEMED | PRFLG_2x,	'\bpfsquad\.nu/gebruiker_cp\.html\?profile=\d+'],
		'photobucket'	=> [PRFLG_OFFLINE,				'\bphotobucket\.com/(?:home/[\w\d\-\+\._]+|albums/[\w\d]+/[\w\d\-\+\._]+)'],
		'photoshop'	=> [PRFLG_OFFLINE,				'\bphotoshop\.com/users/[\d\w\-\+\._]+/profile'],
		'picasa'	=> [PRFLG_OFFLINE,				'\bpicasa(?:web\.google)?\.\w{2,4}/[\w\d\-\+\._]+'],
		'picturepush'	=> [0,						'\b[\w\d\-\.]+\.(?<!www\.)picturepush\.com\b'],
		'picturetrail'	=> [PRFLG_OFFLINE,				'\bpicturetrail\.com/[\w\d\-\+\._]+'],
		'piczo'		=> [PRFLG_OFFLINE,				'\b[\w\d\-\.]+\.(?<!www\.)piczo\.com\b'],
		'pingplace'	=> [PRFLG_OFFLINE,				'\bpingplace\.nl/[\w\d\-\+\._]+'],
		'pinterest'	=> [PRFLG_2x,					'\bpinterest\.com/[\w\d\-\+\._]+'],
		'pkr'		=> [PRFLG_OFFLINE,				'\bpkr\.com/\w{2,4}/community/players/\d+'],
		'play.fm'	=> [PRFLG_OFFLINE,				'\bplay\.fm/(?:artist|festival)/[\w\d\-\+\._]+'],
		'playfire'	=> [PRFLG_OFFLINE,				'\bplayfire\.com/[\w\d\-\+\._]+'],
		'plurlife'	=> [PRFLG_OFFLINE,				'\bplurlife\.com/[\w\d\-\+\._]+'],
		'podomatic'	=> [0,						'\b(?:[\w\d\-]+\.(?<!www\.)podomatic\.com|podomatic\.com/[\w\d\-\+\._]+)'],
		'polyvore'	=> [PRFLG_OFFLINE,				'\bpolyvore\.com/cgi/profile\?id=\d+'],
		'posterous'	=> [PRFLG_OFFLINE,				'\b[\w\d\-]+\.(?<!www\.)posterous\.com\b'],
		'promodj'	=> [0,						'\b(?:[\w\d\-]+\.(?<!www\.)(?:pdj|promodj)\.(?:ru|com)|promodj\.com/[\w\d]+)\b'],
		'purevolume'	=> [PRFLG_OFFLINE,				'\bpurevolume\.com/[\w\d\-\+\._]+'],

		# Q
		'q-dance'	=> [PRFLG_OFFLINE,				'\bq\-dance\.(?:com\.au|\w{2,4})/q/user_profile/[\w\d\-\+\._]+'],
		'qik'		=> [PRFLG_OFFLINE,				'\bqik\.com/[\w\d\-\+\._]+'],

		# R
		'race.sk'	=> [PRFLG_OFFLINE,				'\brace\.sk/forum/memberlist\.php\?mode=viewprofile&u=\d+'],
		'racesimulations'=>[PRFLG_OFFLINE,				'\bracesimulations\.com/account/userinfo/\d+\.html'],
		'rapidshare'	=> [PRFLG_OFFLINE,				'\brapidshare\.com/users/[\w\d\-\+\._]+'],
		'rave.ca'	=> [PRFLG_2x,					'\brave\.ca/[a-z]{2}/profiles_info/\d+'],
		'rcrotterdam'	=> [PRFLG_OFFLINE,				'\brcrotterdam\.nl/member\.php/\d+\-'],
		'rdio'		=> [PRFLG_OFFLINE | PRFLG_2x,			'\brdio\.com/artist/[\w\d\-\+\._]+'],
		'reislogger'	=> [0,						'\b[\w\d\-]+\.(?<!www\.)reislogger\.nl\b'],
		'residentadvisor'
				=> [PRFLG_THEMED | PRFLG_2x,				'\bra\.co/(?:dj|profile|clubs?|events?|promoters?|labels?)/[\w\d\-\+\._]+'],
		'reverbnation'	=> [0,						'\breverbnation\.com/[\w\d\-\+\._]+'],
		'robotdj'	=> [PRFLG_OFFLINE | PRFLG_THEMED,		'\brobotdj\.net/?\?dj=.+'],
		'rolldabeats'	=> [0,						'\brolldabeats\.com/artist/[\w\d\-\+\._]+'],
		'rotterdam4you'	=> [PRFLG_OFFLINE | PRFLG_OFFLINE,		'\b[\w\d\-]+\.(?<!www\.)rotterdam4you\.nl\b'],
		'runkeeper'	=> [0,						'\brunkeeper\.com/user/[\w\d\-\+\._]+'],

		# S
		'samurai.fm'	=> [PRFLG_OFFLINE | PRFLG_2x,			'\bsamurai\.fm/[\w\d\-\+\._]+'],
		'schuelervz'	=> [PRFLG_OFFLINE,				'\bschuelervz\.net/Profile/[\w\d\-\+\._]+'],
		'sdc'		=> [0,						'\bwww\.sdc\.com/react/#/profile\?idUser=\d+'],
		'seeyoudance'	=> [PRFLG_OFFLINE,				'\bseeyoudance\.nl/[\w\d\-\+\._]+'],
		'sevenload'	=> [PRFLG_OFFLINE,				'\bsevenload\.com/users/[\w\d\-\+\._]+'],
		# NOTE: Only entry here, no icon and no database update done:
		# 'shopify'		=> [0,						'\b(?:my)?shopify.com'],
		'silaa7-mocro'	=> [PRFLG_OFFLINE,				'\bsilaa7-mocro\.nl/home\.php\?p=profiel/profiel&naam=[\w\d\-\+\._]+'],
		'sitemodel'	=> [PRFLG_OFFLINE,				'\bsitemodel\.net/(?:view_profile\.php\?member_id=)?\d+'],
		'skoften'	=> [0,						'\bskoften\.net/skoft/\d+'],
		'skyrock'	=> [PRFLG_OFFLINE,				'\b[\w\d\-]+\.(?<!www\.)(?:skyrock|skyblog)\.com\b'],
		'slideshare'	=> [0,						'\bslideshare\.net/[\w\d\-\+\._]+'],
		'snapchat'	=> [PRFLG_2x,					'\bsnapchat\.com/add/.+'],
		'solo.to'	=> [PRFLG_2x,					'\bsolo\.to/.+'],
		'solomio'	=> [PRFLG_OFFLINE,				'\b(solomio\.nl/p\d+_|[\w\d\-]+\.(?<!www\.)solomio\.nl)'],
		'songkick'	=> [PRFLG_2x,					'\bsongkick\.com/(?:users/[\w\d\-\+\._]+|artists/\d+)'],
		'soundarea'	=> [PRFLG_OFFLINE,				'\bforum\.soundarea\.org/index\.php\?/user/\d+'],
		'soundclick'	=> [PRFLG_THEMED,				'\bsoundclick\.com/[\w\d\-\+\._]+'],
		'soundcloud'	=> [PRFLG_2x,					'\bsoundcloud\.com/(?!you/tracks)[\w\d\-\+\._]+'],
		'spaces'	=> [PRFLG_OFFLINE,				'\b[\w\d\-]+\.(?<!www\.)spaces\.live\.'],
		'speedcore.ca'	=> [PRFLG_OFFLINE,				'\bspeedcore\.ca/component/comprofiler/userprofile/[\w\d\-\+\._]+'],
		'spoilertv'	=> [PRFLG_OFFLINE,				'\bspoilertv\.co\.uk/forum/memberlist\.php\?mode=viewprofile&u=\d+'],
		'spotify'	=> [PRFLG_V2 | PRFLG_2x,			'\b(?:open|play)\.spotify\.com/(?:user|artist|playlist)/[\w\d\-\+\._]+'],
		'spreadshirt'	=> [PRFLG_OFFLINE,				'\b[\w\d\-_]+\.(?<!www\.)spreadshirt\.nl'],
		'starchat'	=> [PRFLG_OFFLINE,				'\bstarchat\.nl/members/profile/[\w\d\-\+\._]+'],
		'steamcommunity'=> [0,						'\bsteamcommunity\.com/(?:id|profiles)/[\w\d\-\+\._]+'],
		'studivz'	=> [PRFLG_OFFLINE,				'\bstudivz\.net/Profile/[\w\d\-\+\._]+'],
		'stumbleupon'	=> [PRFLG_OFFLINE,				'\b(?:stumbleupon\.com/stumbler/[\w\d\-\+\._]+|[\w\d\-_]+\.(?<!www\.)stumbleupon\.com)'],
		'sugababes'	=> [PRFLG_OFFLINE,				'\bsugababes\.nl/[\w\d\-\+\._;\*]+'],
		'superdudes'	=> [PRFLG_OFFLINE,				'\bsuperdudes\.nl/[\w\d\-\+\._;\*]+'],
		'sxc'		=> [PRFLG_OFFLINE,				'\bsxc\.hu//?profile/[\w\d\-\+\._]+'],

		# T
		'tagged'	=> [0,						'\btagged\.com/(?!profile.html$)[\w\d\-\+\._]+'],
		'techno4ever.fm'=> [PRFLG_OFFLINE,				'\btechno4ever\.fm/profil/\d+'],
		'telegram'	=> [PRFLG_2x,					'//(?:t\.me/.+|.+\.t\.me/?)$'],
		'theharderforum'=> [PRFLG_OFFLINE | PRFLG_THEMED,		'\btheharderforum\.com/index\.php\?/user/\d+'],
		'thehardest'	=> [PRFLG_OFFLINE,				'\bthehardest\.nl/(?:download\.php\?list\.\d+|user\.php\?id\.\d+)\b'],
		'themapark'	=> [PRFLG_OFFLINE,				'\bthemepark\.nl/ubb/ubbthreads\.php/users/\d+'],
		'thepiratebay'	=> [PRFLG_THEMED,				'\bthepiratebay\.\w{2,3}(?:/user/[\w\d\-\+\._]+|search\.php\?q=user:.+)'],
		'threads'	=> [PRFLG_THEMED,				'\bthreads\.net/@\w{2,}'],
		'tiktok'	=> [0,						'\btiktok.com/\@.+'],
		'tilllate'	=> [PRFLG_OFFLINE,				'\btilllate\.com/[a-z]{2,4}/(?:member|location|artist|venue)/[\w\d\-\+\._]+'],
		'tmf'		=> [PRFLG_OFFLINE,				'\b(?:[\w\d\-_]+\.(?<!www\.)tmf\.nl|tmf\.nl/[\w\d\-\+\._]+)'],
		'topdj'		=> [PRFLG_OFFLINE,				'\b[\w\d\-_]+\.topdj\.ua\b'],
		'townster'	=> [PRFLG_OFFLINE,				'\btownster\.de/\w+/ort/[\w\-]+'],
		'trackitdown'	=> [PRFLG_OFFLINE,				'\btrackitdown\.net/(?:(?:artist|recordlabel)/\d+|profile/[\w\d\-\+\._]+)'],
		'trance.fm'	=> [PRFLG_OFFLINE | PRFLG_THEMED,		'\btrance\.fm/(?:#/)?people/\d+'],
		'transmissionfm'=> [PRFLG_OFFLINE,				'\btransmissionfm\.com/profile/[\w\d\-\+\._]+/'],
		'travbuddy'	=> [PRFLG_OFFLINE,				'\btravbuddy\.com/[\w\d\-\+\._]+'],
		'trueachievements'
				=> [0,						'\btrueachievements\.com/gamer/[\w\+\d_\.]+'],
		'tsapper'	=> [PRFLG_OFFLINE,				'\btsapper\.nl/profielkijk\.php\?userId=\d+'],
		'tuenti'	=> [PRFLG_OFFLINE,				'\bplaces\.tuenti\.com/[\w\d\-_\.\+]+'],
		'tumblr'	=> [0,						'\b[\w\d\-_]+\.(?<!www\.)tumblr\.com\b'],
		'tunecore'	=> [PRFLG_OFFLINE,				'\btunecore\.com/music/[\w\d\-\+\._]+'],
		'turkishplace'	=> [PRFLG_OFFLINE,				'\bturkishplace\.nl/(?:profiel/[\w\d\-\+\._]+/|[\w\d\-\+\._]+)'],
		'tweakers'	=> [0,						'\btweakers\.net/gallery/[\w\d\-\+\._]+'],
		'twitch'	=> [0,						'\btwitch.tv/[\w\d\-\+\._]+'],
		'twitpic'	=> [PRFLG_OFFLINE,				'\btwitpic\.com/photos/[\w\d\-\+\._]+'],
		'twitter'	=> [PRFLG_2x,					'\btwitter\.com/[\w\d\-\+\._]+'],
		'twoo'		=> [PRFLG_OFFLINE,				'\btwoo\.com/\d+'],

		# U
		'uplay'		=> [PRFLG_OFFLINE,				'\buplay\.\w{2,4}\.ubi\.com/profile/[\w\d\-\+\._]+'],
		'ustream'	=> [PRFLG_OFFLINE,				'\bustream\.tv/(?:channel|user)/[\w\d\-\+\._]+'],

		# V
		'vampirefreaks'	=> [PRFLG_OFFLINE,				'\bvampirefreaks\.com/[\w\d\-\+\._]+'],
		'vgjot'		=> [PRFLG_OFFLINE,				'\bvgjot\.nl/index\.php/[a-z]+\-[a-z\-]+\.html\b'],
		'vi.be'		=> [PRFLG_THEMED,				'\bvi\.be/platform/[\w\d\-\+\._]+'],
		'vibedeck'	=> [PRFLG_OFFLINE,				'\bvibedeck\.com/[\w\d\-]+'],
		'viberate'	=> [0,						'\bviberate\.com/artist/.+'],
		'vimeo'		=> [0,						'\bvimeo\.com/(?:user\d+|[\w\d\-\+\._]+)'],
		'virtualdj'	=> [0,						'\bvirtualdj\.com/user/[\w\d\-\+\._]+'],
		'virtualnights'	=> [PRFLG_OFFLINE,				'\bvirtualnights\.com/[\w\d\-\+\._]+'],
		'vk'		=> [0,						'\bvk\.com/[\w\d\-\+\._]+'],
		'vkontakte'	=> [PRFLG_OFFLINE,				'\bvkontakte\.ru/(?:id\d+|[\w\.]+)'],
		'voetbalmodel'	=> [PRFLG_OFFLINE | PRFLG_THEMED,		'\bvoetbalmodel\.nl/\d+/\d+/[\w\d\-\+\._]+\.html'],
		'voetbalzone'	=> [0,						'\bvoetbalzone\.nl/profiel\.asp\?uid=\d+'],
		'vwforum'	=> [PRFLG_OFFLINE,				'\bvwforum\.nl/member/[\w\d\-\+\._]+'],

		# W
		'waarbenjij'	=> [0,						'\b[\w\d\-_]+\.(?<!www\.)waarbenjij\.nu/profiel'],
		'warp'		=> [PRFLG_OFFLINE | PRFLG_2x,			'\bwarp\.net/records/[\w\d\-\+\._]+'],
		'web-log'	=> [PRFLG_OFFLINE,				'\b[\w\d\-_]+\.(?<!www\.)web\-log\.nl\b'],
		'webklik'	=> [PRFLG_OFFLINE,				'\b[\w\d\-_]+\.(?<!www\.)webklik\.nl\b'],
		'webnode'	=> [PRFLG_2x,					'\b[\w\d\-_]+\.(?<!www\.)webnode\.(?:com|nl)\b'],
		'weebly'	=> [0,						'\b[\w\d\-_]+\.(?<!www\.)weebly\.com\b'],
		'weheartit'	=> [PRFLG_OFFLINE,				'\bweheartit\.com/[\w\d\-\+\._]+'],
		'wer-kennt-wen'	=> [PRFLG_OFFLINE,				'\bwer-kennt-wen\.de/neues/[\w\d\-\+\._]+'],
		'wetransfer'	=> [0,						'\b[\w\d\-_]+.(?<!www\.)wetransfer\.com'],
		'whatsapp'	=> [PRFLG_2x,					'\b(?:wa\.me/\d+|(?:chat|www)\.whatsapp\.com/(?:channel/)?[a-zA-Z\d]{22,})'],
		'wieowie'	=> [PRFLG_OFFLINE,				'\bwieowie\.nl/[\w\d\-\+\._]+'],
		'wikipedia'	=> [0,						'\bwikipedia\.org/wiki/[\w\d\-\+\._%]+'],
		'wix'		=> [0,						'\bwix\.com/[\w\d\-\+\._]+'],
		'wordpress'	=> [PRFLG_OFFLINE,				'\b[\w\d\-_]+\.(?<!www\.)wordpress\.com\b'],
		'worldtv'	=> [PRFLG_OFFLINE,				'\bworldtv\.com/[\w\d\-\+\._]+'],

		# X
		'x'			=> [PRFLG_2x,						'\bx\.com/[a-z\d\+._\-]+'],
		'xfire'		=> [PRFLG_OFFLINE,				'\bxfire\.com/profile/[\w\d\-\+\._]+'],
		'xing'		=> [0,						'\bxing\.com/profile/[\w\d\-\+\._]+'],
		'xseno'		=> [PRFLG_OFFLINE,				'\bxseno\.nl/[\w\d\-\+\._%\*;]+/?'],
		'xstreamist'	=> [PRFLG_OFFLINE | PRFLG_THEMED,		'\bxstreamist\.com/members/\d+'],

		# Y
		'yahoo'		=> [PRFLG_OFFLINE,				'\b(?:profile\.yahoo\.com/[A-Z\d]{10,}|music\.yahoo\.com/[\w\d\-\+\._]+/)'],
		'yalwa'		=> [PRFLG_OFFLINE,				'\byalwa\.nl/ID_\d+/'],
		'yearbook'	=> [PRFLG_OFFLINE,				'\byearbook\.com/ask/[\w\d\-\+\._]+'],
		'youtube'	=> [PRFLG_2x,					'\b(?<!music\.)youtube\.\w+/@?(?!v/|watch\?)[\w\d\-\+\._]+'],
		'youtube.music' => [0,						'\bmusic\.youtube\.com/channel/[a-zA-Z\d_-]+'],

		# Z
		'zero-inch'	=> [PRFLG_OFFLINE,				'\b(?:[\w\d\-_]+\.(?<!www\.)zero\-inch\.com|zero\-inch\.com/artist/[\w\d\-\+\._]+)\b'],
		'zideo'		=> [PRFLG_OFFLINE | PRFLG_2x,			'\bzideo\.nl/partner/[\w\d]+/'],
		'zippyshare'	=> [PRFLG_OFFLINE,				'\bzippyshare\.com/[\w\d\-\+\._]+'],
		'zoom'		=> [0,						'\bforum\.zoom\.nl/profile/\d+\-'],
	];
	return $type ? ($__presence_specs[$type] ?? null) : $__presence_specs;
}

function supported_presence(?string	$site = null, bool $utf8 = false): array|string|false {
	static $__presence_specs = get_all_presences();
	if (!$site) {
		return $__presence_specs;
	}
	foreach ($__presence_specs as $type => [$flags, $regex]) {
		if (($flags & PRFLG_OFFLINE)
		||	!preg_match("\x01$regex\x01i".($utf8 ? 'u' : ''), $site, $match)
		) {
			continue;
		}
		return $type;
	}
	return false;
}

function store_presence(
	?array $skip   = null,
	?int   $userid = null,
	bool   $utf8   = false,
): bool|null {
	if (!isset($_POST['PRESENCE'])) {
		return false;
	}
	$element = $_REQUEST['sELEMENT'];
	$id		 = $_REQUEST['sID'];

	if (false === ($existing = db_simple_hash('presence', "
		SELECT SITE, PRESENCEID
		FROM presence
		WHERE ELEMENT = '$element'
		  AND ID = $id"
	))) {
		return false;
	}

	$userid ??= CURRENTUSERID;

	require_once '_site.inc';
	$presences = [];

	$start_time = microtime(true);

	foreach (preg_split('"[\r\n]+"'.($utf8 ? 'u' : ''), $_POST['PRESENCE']) as $line) {
		if (!($line = mytrim($line, utf8: $utf8))) {
			continue;
		}
		if ((	str_contains($line, ' ')
			||	str_contains($line, "\t")
			)
		&&	preg_match('"(?:\s*(https?.*?)\s*)+"', $line)
		) {
			foreach (explode(' ', $line) as $site) {
				if (!($site = mytrim($site, utf8: true))) {
					continue;
				}
				if (preg_match('"[\x00-\x1F\x7F-\xFF]"', $site)) {
					$new_site = preg_replace_callback(
						'"[\x00-\x1F\x7F-\xFF]"',
						static fn($match): string => '%'.strtoupper(dechex(ord($match[0]))),
						$text
					);
					mail_log("converted presence site\n\n$site\n\n$new_site\n\n");
					$site = $new_site;
				}
				$presences[$site] = true;
			}
		} else {
			$presences[$line] = true;
		}
	}
	$ok_presences = [];
	foreach ($presences as $site => $true) {
		if (preg_match('"^https?://(?:www\.)?facebook\.com/events/(?P<eventid>\d+)(?:/?\?event_time_id=|/)(\d+)"i', $site, $match)) {
			$ok_presences[$extra_site = "https://www.facebook.com/events/{$match['eventid']}/"] = $extra_site;
		} elseif (preg_match('"^https?://(?:www|music)\.youtube\.com/(?!channel)"i', $site, $match)) {
			require_once 'defines/youtube.inc';
			if (false === ($new_site = get_youtube_channel_url_from_any_url($site))) {
				return false;
			}
			print_rr($site.' => '.$new_site);
			$site = $new_site;
		}
		$ok_presences[$site] = $site;
	}

	$presences = $ok_presences;
	$webdriver = null;

	$show_infos = true; # SERVER_SANDBOX || SERVER_VIP;

	$show_infos && $infos[] = "$element:$id store_presence(1) taking ".(microtime(true) - $start_time);

	$presence = [];

	print_rr($presences, 'presences at start');

	foreach ($presences as $site) {
		print_rr($site, 'site start');
		$show_infos && $infos[] = "$element:$id store_presence(2) taking ".(microtime(true) - $start_time)." starting with site $site";

		if (!($site = cleanup_presence_url($site, utf8: $utf8))) {
			continue;
		}
		print_rr($site, 'cleaned url');
		if ($skip && isset($skip[base_url($site)])) {
			$presence[$site] = 0;
			continue;
		}

		$show_infos && $infos[] = "$element:$id store_presence(2.1) taking ".(microtime(true) - $start_time);

		# Store empty string as type for unsupported URLs, but do continue, don't remove the site
		$type = supported_presence($site) ?: '';

		$show_infos && $infos[] = "$element:$id store_presence(2.2) taking ".(microtime(true) - $start_time);

		if ($element === 'party') {
			if ($type === 'facebook'
			&&	preg_match('"/events/(\d+)"', $site, $match)
			) {
				$fbids[$fbid = $match[1]] = "('$element', $id, $fbid, 'event')";
			}
		} elseif (in_array($type, ['facebook', 'facebookpage'])) {
			if (str_contains($site, '/events/')) {
				register_warning('presence:warning:no_event_presence_on_non_event_LINE');
				continue;
			}
			$user_fbid = 0;
			$user_site = null;

			if (preg_match('"\bfacebook\.com/profile\.php\?id=(?P<fbid>\d+)"', $site, $match)) {
				$user_fbid = (int)$match['fbid'];
				$fbids[$user_fbid] = "('$element', $id, $user_fbid, 'user')";
				$user_site = "https://www.facebook.com/$user_fbid";
				$presence[$user_site] = 'facebook';
			}

			if ($webdriver !== false) {
				require_once '_fb.inc';
				$use_site = $user_site ?? $site;

				$show_infos && $infos[] = "$element:$id store_presence(a) taking ".(microtime(true) - $start_time);

				$fbid = get_fbid_from_link($use_site, $get_type, $webdriver);

				$show_infos && $infos[] = "$element:$id store_presence(b) taking ".(microtime(true) - $start_time);

				if ($fbid
				&&	$fbid !== $user_fbid
				) {
					$fbids[$fbid] = "('$element', $id, $fbid, '".addslashes($get_type)."')";
					$site = "https://www.facebook.com/$fbid";
					$presence[$site] = $get_type === 'page' ? 'facebookpage' : 'facebook';

					if ($user_fbid) {
						if ($get_type === 'page') {
							if (!db_insert('fbid_identical', '
								INSERT IGNORE INTO fbid_identical SET
									STAMP	= '.CURRENTSTAMP.",
									PAGEID	= $fbid,
									FBID	= $user_fbid")
							) {
								return false;
							}
						}
						if (!empty($user_fbid)) {
							unset($fbids[$user_fbid]);
						}
						if (!empty($user_site)) {
							unset($presence[$user_site]);
						}
					}
				}
			}
		}

		$show_infos && $infos[] = "$element:$id store_presence(2.3) taking ".(microtime(true) - $start_time);

		$presence[$site] = $type;
		$have_presence[$type] = $type;
	}

	print_rr($presence);

	if (HOME_THOMAS) {
		exit;
	}

	$show_infos && $infos[] = "$element:$id store_presence(3) taking ".(microtime(true) - $start_time);

	if (empty($have_presence['facebook'])
	&&	empty($have_presence['facebookpage'])
	) {
		if (!db_insert('fbid_log', '
			INSERT INTO fbid_log
			SELECT *, '.CURRENTSTAMP."
			FROM fbid
			WHERE ELEMENT = '$element'
			  AND ID = $id")
		||	!db_delete('fbid', "
			DELETE FROM fbid
			WHERE ELEMENT = '$element'
			  AND ID = $id")
		) {
			return false;
		}
	} elseif (!empty($fbids)) {
		if (!db_insert('fbid','
			REPLACE DELAYED INTO fbid (ELEMENT, ID, FBID, TYPE)
			VALUES '.implode(', ', $fbids))
		) {
			return false;
		}
	}
	foreach ($presence as $site => &$supported) {
		if (!$supported) {
			register_warning(
				$supported === 0
			?	'party:warning:site_not_party_specific_LINE'
			:	'presence:warning:not_yet_supported_LINE', ['SITE' => $site]
			);
			if ($supported === 0) {
				unset($presence[$site]);
			}
		}
	}
	unset($supported);

	$show_infos && $infos[] = "$element:$id store_presence(4) taking ".(microtime(true) - $start_time);

	$new = array_diff_key($presence, $existing);
	$rem = array_diff_key($existing, $presence);

	if ($rem) {
		require_once '_site.inc';
		$remfb = [];
		foreach ($rem as $site => $presenceid) {
			if (str_contains($site, 'facebook.')) {
				$remfb[base_url($site)] =
					preg_match('"facebook\.com/events/(\d+)"', $site, $match)
				||	preg_match('"facebook\.com/(\d+)"', $site, $match)
				||	preg_match('"facebook\.com/.*?\-(\d+)$"', $site, $match)
				?	[$presenceid, (int)$match[1]]
				:	[$presenceid, null];
			}
		}

		if ($new) {
			foreach ($new as $site => $type) {
				unset($remfb[base_url($site)]);
			}
		}
		if (!empty($remfb)) {
			$remfbids = [];
			$rempresences = [];
			foreach ($remfb as $site => [$site, $fbid]) {
				if ($fbid) {
					$remfbids[] = $fbid;
				}
				$rempresences[] = $presenceid;
			}
			if ($remfbids) {
				$remfbidstr = implode(', ', $remfbids);
				require_once '_favourite.inc';
				if (is_favourable($element)) {
					if (false === ($userids = db_simpler_array(['fbid', 'fblikeids'], "
						SELECT DISTINCT USERID
						FROM fbid
						JOIN fblikeids USING (FBID)
						WHERE FBID IN ($remfbidstr)
						  AND ELEMENT = '$element'
						  AND ID = $id"
					))) {
						return false;
					}
					foreach ($userids as $inner_userid) {
						unmark_favourite($element, $id, $inner_userid, true);
					}
				}
				if (!db_insert('fbid_log', '
					INSERT INTO fbid_log
					SELECT *, '.CURRENTSTAMP. "
					FROM fbid
					WHERE FBID IN ($remfbidstr)
					  AND ELEMENT = '$element'
					  AND ID = $id")
				||	!db_delete('fbid', "
					DELETE FROM fbid
					WHERE FBID IN ($remfbidstr)
					  AND ELEMENT = '$element'
					  AND ID = $id")
				) {
					return false;
				}
			}
		}
		$rem_idstr = implode(', ', $rem);
		if (!db_insert('urlcheck', "
			INSERT INTO urlcheck_log
			SELECT * FROM urlcheck
			WHERE ELEMENT = 'presence'
			  AND ID IN ($rem_idstr)")
		||	!db_delete('urlcheck', "
			DELETE FROM urlcheck
			WHERE ELEMENT = 'presence'
			  AND ID IN ($rem_idstr)")
		||	!db_insert('fbid_log', '
			INSERT INTO fbid_log
			SELECT *, '.CURRENTSTAMP."
			FROM fbid
			WHERE ELEMENT = 'presence'
			  AND ID IN ($rem_idstr)")
		||	!db_delete('fbid', "
			DELETE FROM fbid
			WHERE ELEMENT = 'presence'
			  AND ID IN ($rem_idstr)")
		||	!db_insert('presence_log','
			INSERT INTO presence_log
			SELECT presence.*, '.CURRENTSTAMP.", $userid FROM presence
			WHERE PRESENCEID IN ($rem_idstr)")
		||	!db_delete('presence', "
			DELETE FROM presence
			WHERE PRESENCEID IN ($rem_idstr)")
		||	!db_delete('youtube_info', "
			DELETE FROM youtube_info
			WHERE PRESENCEID IN ($rem_idstr)")
		) {
			return false;
		}
	}

	$show_infos && $infos[] = "$element:$id store_presence(5) taking ".(microtime(true) - $start_time);

	if ($new) {
		$setlist = [];
		print_rr($new, 'new');
		foreach ($new as $site => $type) {
			$setlist[] = '("'.$element.'", '.$id.', "'.addslashes($site).'", '.$userid.', '.CURRENTSTAMP.', '.($type ? 1 : 0).',"'.addslashes($type).'")';
		}
		foreach ($setlist as $setstr) {
			if (!db_insert('presence', '
				INSERT INTO presence (ELEMENT, ID, SITE, CUSERID, CSTAMP, SUPPORTED, TYPE)
				VALUES '.$setstr)
			||	!db_insert('urlcheck','
				INSERT DELAYED INTO urlcheck SET
					ELEMENT	= "presence",
					ID		= '.db_insert_id())
			) {
				return false;
			}
		}
	}

	$show_infos && $infos[] = "$element:$id store_presence took ".($taken_time = microtime(true) - $start_time);

	if ($taken_time > 2) {
		error_log('store_presence took '.$taken_time.' seconds');
	}

	$show_infos && $taken_time > 2 && error_log(implode("\n", $infos));

	if ($rem || $new) {
		memcached_delete('presences:'.$element.':'.$id);
		return true;
	}
	return null;
}

function add_presence(string $element, int $id, string $type, string $uri): bool {
	if (!db_getlock($key = "add_presence $element:$id:$type:$uri")) {
		return false;
	}
	if ($type === 'facebook'
	&&	$element === 'party'
	) {
		if (!preg_match('"/events/(?<event_id>\d+)"', $uri, $match)) {
			error_log('add_presence: facebook event uri not understood: '.$uri);
			return false;
		}
		if (!db_insert('fbid','
			INSERT DELAYED IGNORE INTO fbid SET
				ELEMENT	= "party",
				ID		= '.$id.',
				FBID	= '.$match['event_id'].',
				TYPE	= "event"')
		) {
			return false;
		}
		if (!db_affected()) {
			error_log("already have presence for party $id and fbid {$match['event_id']}");
			return true;
		}
	}
	if (!db_insert('presence', '
		INSERT INTO presence SET
			ELEMENT	= "'.$element.'",
			ID		= '.$id.',
			CSTAMP	= '.CURRENTSTAMP.',
			CUSERID	= '.CURRENTUSERID.',
			SITE	= "'.addslashes($uri).'",
			TYPE	= "'.$type.'"')
	||	!($presenceid = db_insert_id())
	||	!db_insert('urlcheck','
		INSERT DELAYED INTO urlcheck SET
			ELEMENT	= "presence",
			ID		= '.$presenceid)
	) {
		return false;
	}
	db_releaselock($key);
	memcached_delete("presences:$element:$id");
	return $presenceid;
}

# remove_presence(element, id)
# remove_presence([presenceid, ...])
# remove_presence(presenceid)
function remove_presences(string|array $element, ?int $id = null): bool {
	$wherep =
		$id === null
	?	(	is_array($element)
		?	'	presence.PRESENCEID IN ('.implode(',', $element).')'
		:	'	presence.PRESENCEID='.$element
		)

	:	'	presence.ELEMENT="'.$element.'"
		AND	presence.ID='.$id;

	if (!db_insert('urlcheck','
		INSERT INTO urlcheck_log
		SELECT urlcheck.*
		FROM urlcheck
		JOIN presence ON PRESENCEID = urlcheck.ID
		WHERE urlcheck.ELEMENT = "presence"
		  AND '.$wherep)
	||	!db_delete('urlcheck','
		DELETE urlcheck
		FROM urlcheck
		JOIN presence ON PRESENCEID = urlcheck.ID
		WHERE urlcheck.ELEMENT = "presence"
		  AND '.$wherep)
	||	!db_delete('fbid','
		DELETE fbid
		FROM fbid
		JOIN presence ON PRESENCEID=fbid.ID
		WHERE fbid.ELEMENT="presence"
		  AND '.$wherep)
	||	!db_insert('presence_log','
		INSERT INTO presence_log
		SELECT presence.*,'.CURRENTSTAMP.','.CURRENTUSERID.'
		FROM presence
		WHERE '.$wherep)
	||	!db_delete('presence','
		DELETE FROM presence
		WHERE '.$wherep)
	) {
		return false;
	}
	if (!is_array($element)) {
		memcached_delete('presences:'.$element.':'.$id);
	}
	return true;
}

function presence_last_modified(): array {
	static $__lastmodified = -1;
	return	$__lastmodified === -1
	?	(		($presences = have_presence())
		?	(	($lastmod = db_single_assoc('presence', 'SELECT CUSERID, CSTAMP FROM presence WHERE PRESENCEID='.$presences['MAXID']))
			?	[$lastmod['CUSERID'], $lastmod['CSTAMP']]
			:	[null, null]
			)
		:	[0, 0]
		)
	:	$__lastmodified;
}

function show_presence(bool $me = false, bool $lines = false): int {
	if (!($presences = have_presence(infos: $infos))) {
		return 0;
	}

	unset($presences['MAXID']);
	# Keys contain the single word identifier of the presences
	ksort($presences);

	$is_party = $_REQUEST['sELEMENT'] === 'party';

	$cnt = 0;

	if (isset($presences['spotify'])) {
		# showing presecenses, if we have both user/artist and playlist type links, keep only user/artist to display in icons
		static $__chosen_spotify = null;

		if ($__chosen_spotify !== null) {
			$presences['spotify'] = $__chosen_spotify;

		} elseif (($count = count($presences['spotify'])) === 1) {
			$__chosen_spotify = $presences['spotify'];

		} else {
			$spotifies = [];
			foreach ($presences['spotify'] as $site => $icon) {
				if ($type = get_type_from_spotify_link($site)) {
					$spotifies[$type][$site] = $icon;
				}
			}
			unset($spotifies['playlist']);
			if (!--$count) {
				$__chosen_spotify = [$site => $icon];
			} else {
				uksort($spotifies, function (string $type_a, string $type_b): int {
					static $__worths = [
						'artist'   => 0, # most preferred
						'user'	   => 1,
						'playlist' => 2,
						'track'	   => 3, # least preferred
					];
					return $__worths[$type_a] - $__worths[$type_b];
				});

				[$type, $sites_and_icons] = keyval($spotifies);

				$__chosen_spotify = $sites_and_icons;
			}

			$presences['spotify'] = $__chosen_spotify;
		}
	}

	foreach ($presences as $type => $sites) {
		if (is_bool($sites)) {
			mail_log('sites for type '.$type.' is bool', item: get_defined_vars());
			continue;
		}
		foreach ($sites as $site => $icon) {
			if ($lines) {
				?><div><?
			}
			if ($me) {
				$icon = str_replace('rel="', 'rel="me ', $icon);
			}

			if ($is_party
			&&	$type === 'facebook'
			&&	preg_match('"events/(\d+)"', $site, $match)
			) {
				mail_log('where does this strange print happen in show_presence?');

				$icon = str_replace('</a>', ob_get_clean().'</a>', $icon);
			}

/*			if ($multi_spotify
			&& 	($type = get_type_from_spotify_link($site))
			) {
				$short = match($type) {
					'artist'	=> 'a',
					'user'		=> 'u',
					'playlist'	=> 'pl',
					default		=> '',
				};
				$icon = str_replace('<a ', '<a class="seemtext" ', $icon);
				$icon = str_replace('</a>', '<sub class="abs">'.$short.'</sub></a>', $icon);
#				echo $type;
			}*/

			echo $icon;

			if ($lines) {
				?></div><?
			}
			++$cnt;
		}
	}

	if (!ROBOT) {
		foreach ($infos as $type => $more) {
			foreach ($more as $site => $info) {
				$views_presenceid[] = $info['PRESENCEID'];
			}
		}

		db_insupd('presence_view', '
		INSERT INTO presence_view (PRESENCEID, LAST_VIEW, LAST_CHECK)
		SELECT ID, UNIX_TIMESTAMP(), LAST_CHECK
		FROM urlcheck
		WHERE urlcheck.ELEMENT = "presence"
		  AND urlcheck.ID IN ('.implode(', ',$views_presenceid).')
		ON DUPLICATE KEY UPDATE LAST_VIEW = VALUES(LAST_VIEW)'
		);
	}

	return $cnt;
}

function get_presences(string $element, int $id): array {
	static $__presences;
	return	$__presences[$element][$id]
	??=		$__presences[$element][$id] = memcached_rowuse_hash('presence', "
			SELECT PRESENCEID, TYPE, SITE
			FROM presence
			WHERE ELEMENT = '$element'
			  AND ID = $id",
			key: 'presences:'.$element.':'.$id,
			flags: DB_NON_ASSOC
	)	?: [];
}

function have_presence(
	?string $element	  = null,
	?int	$id	  		  = null,
	array  &$infos		  = null,
	bool	$include_bad  = false,
	bool	$show_bubbles = true,
): array {
	$element ??= $_REQUEST['sELEMENT'];
	$id		 ??= $_REQUEST['sID'];

	$show_bubbles = $show_bubbles && have_admin($element);

	static $__presence;
	static $__infos;
	if (isset($__presence[$element][$id])) {
		$infos = $__infos[$element][$id] ?? [];
		return $__presence[$element][$id];
	}
	if (!($sites = get_presences($element, $id))) {
		return $__presence[$element][$id] = [];
	}

	$__presence[$element][$id] = [];

	require_once '_urlcheck.inc';
	require_once '_jumper.inc';

	$show_offline =
		$include_bad
	||	(	$element === 'user'
		?	(	CURRENTUSERID === $id
			||	have_admin('helpdesk')
			)
		:	have_admin($element)
		);

	$maxid = 0;
	$done = [];
	$supported_presences = supported_presence();

	foreach ($sites as $presenceid => [, $TYPE, $SITE]) {
		if (!$TYPE
		||	isset($done[$SITE])
		) {
			# sporadicly, people enter same address twice
			continue;
		}
		$done[$SITE] = true;

		if ($TYPE === 'spotify') {
			$spotifies[] = $SITE;
		}

		# FIXME: Both YouTube and Instagram cannot be checked for status currently using simple get pages.
		#		 Instagram recognizes us as a scraper and YouTube shows a cookie message.
		#		 Using youtube_info state, youtube can be done but there is no consistent single 'urlchecker'.
		if ($TYPE === 'youtube') {
			$offline = (bool)memcached_single_int('youtube_info', '
				SELECT BADCNT
				FROM youtube_info
				WHERE PRESENCEID = '.$presenceid
			);
		} else {
			$status = get_url_status('presence', $presenceid);
			$offline = url_is_bad($status);
		}
		if ($offline && !$show_offline) {
			continue;
		}
		$offline_service = false;
		if (HOME_THOMAS) {
			if (!$TYPE) {
				mail_log('TYPE not set in show_presence_row', get_defined_vars(), error_log: 'WARNING');
			}
			$presence_spec = $supported_presences[$TYPE];
			$type = $TYPE;
			$traverse_presences = [$type => $presence_spec];
		} else {
			$traverse_presences = $supported_presences;
		}
		foreach ($traverse_presences as $type => [$flags]) {
			if ($type !== $TYPE) {
				continue;
			}
			if (!$offline) {
				$offline_service =
				$offline = (bool)($flags & PRFLG_OFFLINE);
			}
			if ($offline && !$show_offline) {
				continue;
			}
			ob_start();
			?><a<?
			if ($offline) {
				?> class="light"<?
			}
			?> target="_blank"<?
			?> title="<?= escape_specials($type) ?>: <?= escape_specials($SITE) ?>"<?
			?> href="<?= jump_to('presence', $presenceid) ?>"<?
			?> rel="nofollow"><?
			if ($show_bubble = $offline && $show_bubbles) {
				require_once '_bubble.inc';
				$bubble = new bubble(BUBBLE_CLEAN);
				$bubble->catcher();
			}
			show_presence_icon($type, $flags);
			if ($show_bubble) {
				$bubble->content();
				if (empty($offline_service)) {
					if (!$status) {
						error_log("no status for presence $presenceid: $SITE");
					} else {
						show_url_bubble($status, $SITE, 'presence', $presenceid);
					}
				} else {
					?><div><?= __('presence:info:service_is_offline_LINE') ?></div><?
				}
				$bubble->display();
			}
			?></a><?
			$__presence[$element][$id][$type][$SITE] = $iconstr = ob_get_clean();
			$infos[$type][$SITE] = [
				'OFFLINE'		=> $offline,
				'PRESENCEID'	=> $presenceid,
				'SITE'			=> $SITE,
				'ICON'			=> $iconstr
			];
			if ($presenceid > $maxid) {
				$maxid = $presenceid;
			}
			break;
		}
	}
	if ($maxid) {
		$__presence[$element][$id]['MAXID'] = $maxid;
		$__infos[$element][$id] = $infos ?? [];
	}
	return $__presence[$element][$id];
}

function show_presence_form_row(
	?array  $info  = null,
	?string $class = null,
	?int	$srcid = null,
): bool {
	$element = $_REQUEST['sELEMENT'];
	if ($id = ($srcid ??= $_REQUEST['sID'])) {
		if (false === ($sites = db_simpler_array('presence', "
			SELECT SITE
			FROM presence
			WHERE ELEMENT = '$element'
			  AND ID = $id
			ORDER BY SITE"
		))) {
			return false;
		}
	}
	layout_restart_row(rowuserdef: $class);
	$event = false;
	switch ($element) {
	case 'user':
		$is_user = true;
		require_once '_bubble.inc';
		$bubble = new bubble(BUBBLE_BLOCK | HELP_BUBBLE);
		$bubble->catcher_and_title(Eelement_plural_name('social_link'));
		$bubble->content(__('presence:info:bubble_TEXT', DO_NL2BR));
		$bubble->display();
		break;

	case 'party':
		$event = true;
		?><div><?
		echo Eelement_plural_name('source_link');
		?> <div class="ib colorless"><?=
		get_facebook_icon() ?> <?
		show_presence_icon('instagram'); ?> <?
		show_presence_icon('residentadvisor');
		?></div><?
		?></div><?
		break;

	default:
		echo Eelement_plural_name('social_link');
		break;
	}
	layout_field_value();

	if (!empty($sites)) {
		$__bare_hosts = [];
/*		NOTE: Overly complex and doesn't seem to do what it should, order sites on main domain.
			  Just use regular sorting for now.

		usort($sites, static function (string $url_one, string $url_two) use (&$__bare_hosts): int {
			foreach (['url_one', 'url_two'] as $url_var) {
				if (isset($__bare_hosts[$$url_var])) {
					continue;
				}
				$bare_host = parse_url($$url_var, PHP_URL_HOST);
				if (preg_match('"^(?:(?<subdomain>[a-z\d_\-\.]+)\.)?(?<domain>[^.]+)\.(?<toplevel>[a-z]{2,})$"i', $bare_host, $match)) {
					$bare_host = $match['domain'].'-'.$match['subdomain'].'.'.$match['toplevel'];
				}
				$__bare_hosts[$$url_var] = $bare_host;
			}
			return $__bare_hosts[$url_one] <=> $__bare_hosts[$url_two];
		});*/
		$content = escape_ascii(implode("\n", $sites))."\n";
	} else {
		$content = null;
	}

	$show_check_dups = ($element !== 'user');

	include_js('js/form/presence');

	show_textarea([
		'placeholder'	=> empty($event) ? __('presence:info:link_to_profile_here') : null,
		'onkeyup'		=> $show_check_dups ? /** @lang MariaDB */ "Pf.checkPresenceDups(this, '$element', $id);" : null,
		'ondrop'		=> /** @lang MariaDB */ 'Pf.dropPresence(event, this);',
		'class'			=> 'growToFit',
		'name'			=> 'PRESENCE',
		'cols'	 		=> 80,
		'rows'			=> empty($sites) ? 2 : max(2, count($sites) + 1),
		'value_escaped'	=> $content,
	]);

	if ($show_check_dups) {
		?><div id="fbtopfstore"></div><?
	}

	if (isset($is_user)) {
		layout_next_cell(class: 'right');
		_visibility_display_form_part($info, 'PRESENCE');
	}
	return true;
}

function get_presence_icon(string $service, ?string $class = null): string {
	require_once '_presence.inc';
	ob_start();
	show_presence_icon($service, class: $class);
	return ob_get_clean();
}

function show_presence_icon(
	string	$type,
	?int	$flags	= null,
	?string	$class	= null,
): void {
	require_once '_layout.inc';

	$flags ??= get_presence_flags($type);

	if ($type === 'twitter') {
		$type = 'x';
		$flags = PRFLG_THEMED;
	}

	$x2_url =
		$type === 'partyflock'
	?	get_favicon()
	:	(SERVER_SANDBOX ? '/static' : STATIC_HOST).'/presence/'.$type.
		($flags & PRFLG_THEMED ? '_'.CURRENTTHEME : '').
		($flags & PRFLG_V2 ? '_v2' : '').
		($flags & PRFLG_2x ? '@2x' : '').
		(webp_supported() ? '.webp' : '.png');

	$x1_url = str_replace('@2x', '', $x2_url);

	?><img<?
	?> loading="eager"<?
	?> alt="<?= escape_specials($type) ?>"<?
	?> class="<? if ($class) { echo $class; ?> <? } ?>presence"<?
	?> src="<?= is_high_res() ? $x2_url : $x1_url ?>"<?
	if ($x1_url !== $x2_url) {
		?> srcset="<?= $x1_url ?> 1x, <?= $x2_url ?> 2x"<?
	}
	?>><?
}

function move_site_to_presence(
	string|array|null	$site_fields	= null,
	bool		  		$utf8			= false
): void {
	foreach (is_array($site_fields) ? $site_fields : ['SITE'] as $site_field) {
		if (empty($_POST[$site_field])
		||	!supported_presence($_POST[$site_field])
		) {
			continue;
		}

		$_POST['PRESENCE'] =
			!empty($_POST['PRESENCE'])
		?	$_POST['PRESENCE']."\n".$_POST[$site_field]
		:	$_POST[$site_field];

		register_warning('presence:warning:site_moved_to_presence_LINE', [$site_field => $_POST[$site_field]]);
		$_POST[$site_field] = '';
	}
}

function show_presence_row(deflist $list, bool $me = false): bool {
	if (!have_presence()) {
		return false;
	}
	ob_start();
	$cnt = show_presence($me);
	$list->set_row_class('presencerow');
	$list->add_row(Eelement_name('link', $cnt), ob_get_clean());
	return true;
}

function link_to_facebook_events(string $element, int $id): void {
	$presences = have_presence($element, $id);

	if (!isset($presences['facebook'])
	&&	!isset($presences['facebookpage'])
	) {
		return;
	}
	$site = key($presences[isset($presences['facebookpage']) ? 'facebookpage' : 'facebook']);

	if (preg_match('"facebook\.com/((?:pages|pg)/(.*)/(\d+).*)(?:\?.*)?$"', $site, $match)) {
		$path = $match[1].'?id='.$match[3].'&amp;sk=events';

	} elseif (preg_match('"facebook\.com/(?:.*\-(\d+))$"', $site, $match)) {
		$path = $match[1].'?sk=events';

	} elseif (preg_match('"facebook\.com/profile\.php\?(.*?)id=(\d+)(.*?)$"', $site, $match)) {
		$path = $match[2].'?v=events';

	} elseif (preg_match('"facebook\.com/(.*)(?:\?.*)?$"', $site, $match)) {
		$path = rtrim($match[1],'/').'/events';

	} else {
		return;
	}
	?> <a<?
	?> target="_blank"<?
	?> class="light colorless colorfull-hover"<?
	?> href="https://www.facebook.com/<?= $path ?>"><?
	show_presence_icon('facebook');
	?></a><?
}

function link_to_instagram(string $element, int $id): void {
	$presences = have_presence($element, $id);

	if (!isset($presences['instagram'])) {
		return;
	}
	$site = key($presences['instagram']);

	?> <a target="_blank" class="light colorless colorfull-hover" href="<?= $site ?>"><?
	show_presence_icon('instagram');
	?></a><?
}

function cleanup_presence_url(string $site, bool $utf8 = false): string {
	require_once '_flockmod.inc';
	require_once '_helper.inc';
	require_once '_url.inc';
	require_once '_site.inc';

	$utf8_mod = $utf8 ? 'u' : '';

	$site = str_replace(['[url]', '[/url]'], '', $site);
	$site = myrtrim($site, '#?&', $utf8);

	if (str_contains($site, 'twitter.nl')) {
		$site = preg_replace('"\b(twitter\.nl)\b"'.$utf8_mod, 'twitter.com', $site);
	}

	# remove any service named in front of url

	if (preg_match('"^[a-z_-]+\s*:\s*(?<site>http.*?)\s*$"', $site, $match)) {
		$site = $match['site'];
	}

	if (!HOME_THOMAS) {
		$site = cleanup_url($site, $utf8);
	}

	if (str_contains($site, 'facebook')) {
		if (preg_match('"https?://(?:www\.)?facebook\.com/events/(\d+)(?:/?\?event_time_id=|/)(\d+)"'.$utf8_mod, $site, $match)) {
			[,$mainid, $instanceid] = $match;
			if ($mainid !== $instanceid) {
				db_insert('feedevent_multiday','
				INSERT DELAYED IGNORE INTO feedevent_multiday SET
					MAINID		= '.$mainid.',
					INSTANCEID	= '.$instanceid
				);
			}
			# recognize repeating event the easy way for now
			$site = 'https://www.facebook.com/events/'.$instanceid;
		}
		if (preg_match('"^(.*?facebook\.com/events/\d+)/?(?:.*?)$"'.$utf8_mod, $site, $match)) {
			$site = $match[1];
		}
		if (preg_match('"^(?P<prefix>.*facebook.*)/(?:events|timeline|info)[/\-](?P<fbid>\d+)?(?P<path>.*)$"i'.$utf8_mod, $site, $match)) {
			if (!$match['fbid']) {
				$site = $match['prefix'].$match['path'];
			}
		}
		$site = str_ireplace('facebook.nl', 'facebook.com', $site);
		$site = preg_replace('"\bm\.facebook\.com\b"i'.$utf8_mod, 'www.facebook.com', $site);

		# don't point to about pages
		$site = preg_replace('"/about/?$"'.$utf8_mod, '', $site);
	}

	if (preg_match(
		'"\b(?P<service>'.
			'insta(?:gram)?|'.
			'telegram|'.
			'snap(?:chat)?|'.
			'soundcloud|'.
			'tiktok|'.
			'what\'?sapp'.
	 	')[\s:;\*]+(?P<account>.+?)\s*$"i'.$utf8_mod, $site, $match)
	) {
		$match['account'] = myrtrim($match['account'], '/', $utf8);
		switch (strtolower($match['service'])) {
		case 'snap':
		case 'snapchat':
			$site = 'https://www.snapchat.com/add/'.$match['account'];
			break;

		case 'insta':
		case 'instagram':
			$site = 'https://www.instagram.com/'.ltrim($match['account'], '@');
			break;

		case 'soundcloud':
			$site = 'https://soundcloud.com/'.$match['account'];
			break;

		case 'telegram':
			$site = 'https://t.me/'.$match['account'];
			break;

		case 'tiktok':
			$site = 'https://www.tiktok.com/'.($match['account'][0] === '@' ? '' : '@').$match['account'];
 			break;

		case 'whatsapp':
			if (!preg_match('"^\h*\+(?<phone>[\s\d\h\-\(\)]+)$"', $match['account'], $new_match)) {
				register_error('presence:error:whatsapp_needs_full_phone_with_country_code_LINE');
				return $site;
			}
			$site = 'https://wa.me/'.preg_replace('"[^\d]+"', '', $new_match['phone']);
			break;
		}
	}

	if (preg_match('"(?P<user>[\w\d]+)@twitter"i'.$utf8_mod, $site, $match)) {
		$site = 'https://twitter.com/'.$match['user'];

	} elseif (preg_match('"partyflock\.nl/(?!user/)(?P<path>.*)$"'.$utf8_mod, $site, $match)) {
		require_once '_search_via_url.inc';
		if ($tmp_userid = find_nick($match['path'])) {
			$site = FULL_HOST.get_element_href('user', $tmp_userid);
		}

	} elseif (preg_match('"//(itunes|music)\.apple\.com/(?:\w+/)?(artist)/([^/]+)/((?:id)?(?:\d+))"'.$utf8_mod, $site, $match)) {
		$site = 'https://'.$match[1].'.apple.com/'.$match[2].'/'.$match[3].'/'.$match[4];
	}
	if (str_contains($site, 'facebook')) {
		$site = preg_replace('"([a-z]{2,3}-[a-z]{2,3}\.facebook\.com)"'.$utf8_mod, 'www.facebook.com', $site);

		if (preg_match('"^(?P<prefix>.*facebook\.\w{2,4}).*?#!(?P<hash_path>.*)$"i'.$utf8_mod, $site, $match)) {
			$site = $match['prefix'].$match['hash_path'];
		}
		$site = preg_replace('"/info$"'.$utf8_mod, '', $site);

	} elseif (preg_match('"^(?P<prefix>.*)#!(.*)$"'.$utf8_mod, $site, $match)) {
		$site = $match['prefix'].$match['hash_path'];
		$site = preg_replace('"/{2,}"'.$utf8_mod, '/', $site);
	}

	if (preg_match('"^https?://(?:api|web)\.whatsapp\.com/send/?\?phone=(?:\+|%2B)?(?P<phone_number>\d+)"', $site, $match)) {
		$site = 'https://wa.me/'.$match['phone_number'];
	}

	return make_proper_site($site);
}

function show_presence_search(?array $item = null): void {
	if (!$item
	||	(	!have_admin($_REQUEST['sELEMENT'])
		&&	!is_aspiring_content_admin()
		)
	) {
		return;
	}

	?><div class="block"><?
	echo __C('action:search_on') ?> <?

	if (!isset(USE_UNICODE[$_REQUEST['sELEMENT']])) {
		$item['NAME'] = win1252_to_utf8($item['NAME']);
	}
	$item['NAME'] = $item['NAME'] ?: ($item['TITLE'] ?? '');
	$enc = urlencode($item['NAME']);
	$raw_enc = rawurlencode($item['NAME']);
	$types = !empty($item['TYPE']) ? explode_to_hash(',', $item['TYPE']) : null;

	$presences = have_presence(infos: $infos);
	# Prefixing 'dj' often yields empty page on instagram
	$type_prefix = isset($types['rapper']) ? 'rapper+' : ''; //(isset($types['dj']) ? 'dj+' : '');

	?> <a<?
	?> class="colorhover lpad"<?
	?> target="_blank"<?
	?> href="https://www.google.com/search?q=<?= $type_prefix ?>&quot;<?= $enc ?>&quot;"<?
	?>><? show_presence_icon('google') ?></a><?

	if (!isset($presences['facebookpage'])
	||	!($site = key($presences['facebookpage']))
	||	$infos['facebookpage'][$site]['OFFLINE']
	) {
		?> <a<?
		?> class="colorhover lpad"<?
		?> target="_blank" href="https://www.facebook.com/search/top/?q=<?= $enc ?>"><?= get_facebook_icon() ?></a><?
	}

	if (!isset($presences['soundcloud'])
	||	!($site = key($presences['soundcloud']))
	||	$infos['soundcloud'][$site]['OFFLINE']
	) {
		?> <a<?
		?> class="colorhover lpad"<?
		?> target="_blank" href="https://soundcloud.com/search/people?q=<?= $enc ?>"><? show_presence_icon('soundcloud') ?></a><?
	}

	if (!isset($presences['instagram'])
	||	!($site = key($presences['instagram']))
	||	$infos['instagram'][$site]['OFFLINE']
	) {
		if ($type_prefix) {
			?> <a<?
			?> class="colorhover lpad"<?
			?> target="_blank"<?
			?> href="https://www.google.com/search?q=<?= $type_prefix ?>&quot;<?= $enc ?>&quot;+site%3Ainstagram.com"><? show_presence_icon('instagram') ?></a><?
		}
		?> <a<?
		?> class="colorhover lpad"<?
		?> target="_blank"<?
		?> href="https://www.google.com/search?q=&quot;<?= $enc ?>&quot;+site%3Ainstagram.com"><? show_presence_icon('instagram') ?></a><?
	}

	if (!isset($presences['youtube'])
	||	!($site = key($presences['youtube']))
	||	$infos['youtube'][$site]['OFFLINE']
	) {
		?> <a<?
		?> class="colorhover lpad"<?
		?> target="_blank"<?
		?> href="https://www.youtube.com/results?search_query=<?= $enc ?>&amp;sp=EgIQAg%253D%253D"><? show_presence_icon('youtube') ?></a><?
	}

	if (!isset($presences['spotify'])
	||	!($site = key($presences['spotify']))
	||	$infos['spotify'][$site]['OFFLINE']
	) {
		?> <a<?
		?> class="colorhover lpad"<?
		?> target="_blank"<?
		?> href="https://open.spotify.com/search/<?= $raw_enc,  match($_REQUEST['sELEMENT']) {
			'artist'		=> '/artists',
			'organization'	=> '/users',
			default			=> '',
		};
		?>"><? show_presence_icon('spotify') ?></a><?
	}

	if (!isset($presences['tiktok'])
	||	!($site = key($presences['tiktok']))
	||	$infos['tiktok'][$site]['OFFLINE']
	) {
		?> <a<?
		?> class="colorhover lpad"<?
		?> target="_blank"<?
		?> href="https://www.tiktok.com/search/user?q=<?= $raw_enc ?>"><? show_presence_icon('tiktok') ?></a><?
	}

	if (!isset($presences['residentadvisor'])
	||	!($site = key($presences['residentadvisor']))
	# NOTE: resident always resturns 403, we're detect as a bad bot I presume
	# ||	$infos['residentadvisor'][$site]['OFFLINE']
	) {
		?> <a<?
		?> class="colorhover lpad"<?
		?> target="_blank"<?
		?> href="https://www.google.com/search?q=&quot;<?= $enc ?>&quot;+site:ra.co"><? show_presence_icon('residentadvisor') ?></a><?
	}

	if (!isset($presences['discogs'])
	||	!($site = key($presences['discogs']))
	||	$infos['discogs'][$site]['OFFLINE']
	) {
		?> <a<?
		?> class="colorhover lpad"<?
		?> target="_blank"<?
		?> href="https://www.discogs.com/search/?q=&quot;<?= $enc ?>&quot;&type=<?=
			match($_REQUEST['sELEMENT']) {
				'artist'		=> 'artist',
				'organizattion'	=> 'label',
				default			=> 'all'
			};
		?>"><? show_presence_icon('discogs') ?></a><?
	}
	?></div><?
}

function get_type_from_spotify_link(string $site): ?string {
	return	preg_match('"spotify\.com/(?P<type>\w+)/"', $site, $match)
	?	$match['type']
	:	null;
}
