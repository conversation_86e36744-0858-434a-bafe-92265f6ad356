<?php

declare(strict_types=1);

const CLI			= (PHP_SAPI === 'cli');
# Other domains, like partyflock.be, have SPF records that deny sending mail from partyflock.be.
# Lots of different domains complicates stuff.
const MAIL_DOMAIN	= 'partyflock.nl';

require_once __DIR__.'/_servertype.inc';

if (!CLI && !HOME_OFFICE) {
	header("X-Originating-Server: {$_SERVER['SERVER_ADDR']}");
}

const FLOCK_OFFLINE		= '/offline';
const FLOCK_READ_ONLY	= '/read-only';
# Make sure mails cannot be sent
const FLOCK_NO_MAIL		= '/nomail';
# Drastic measure if servers are severely overloaded (for example due to multiple broken servers)
const FLOCK_LOADED		= '/loaded';
# Updating indicates that updates are taking place that could impact performance and availability
const FLOCK_UPDATING	= '/updating';

const FLOCK_STATUSES = [
	FLOCK_OFFLINE,
	FLOCK_READ_ONLY,
	FLOCK_NO_MAIL,
	FLOCK_LOADED,
	FLOCK_UPDATING,
];

function partyflock_all_statuses(): array {
	// static $__statuses;
	// if (isset($__statuses)) {
	// 	return $__statuses;
	// }
	$__statuses = [];
	foreach (FLOCK_STATUSES as $status) {
		if (!($message = partyflock_status($status))
		||	!is_string($message)
		) {
			continue;
		}
		$__statuses[$status] = $message;
	}
	return $__statuses;
}

function partyflock_status(string $status): string|bool {
	static $__statuses = [];
	/** @noinspection NestedTernaryOperatorInspection */
	return $__statuses[$status] ??=
		is_file($status)
	?	(	filesize($status) # NOSONAR
		&&	is_string($get_comment = file_get_contents($status))
		?	$get_comment
		:	true
		)
	:	false;
}

function deny_robots_if_partyflock_under_load(): void {
	if (!partyflock_status(FLOCK_LOADED)) {
		return;
	}
	require_once '_spider.inc';
	if (ROBOT) {
		header('Retry-After: 3600', true, 429);
		exit;
	}
}

function partyflock_offline(): string|bool {
	return partyflock_status(FLOCK_OFFLINE);
}

function partyflock_read_only(): string|bool {
	return partyflock_status(FLOCK_READ_ONLY);
}

function partyflock_no_mail(): string|bool {
	  $no_mail_comment = partyflock_status(FLOCK_NO_MAIL);
	$read_only_comment = partyflock_status(FLOCK_READ_ONLY);
	if (  $no_mail_comment
	||	$read_only_comment
	) {

		if ($get_comment_no_mail) {
			$message = $get_comment_no_mail;
		} else {
			$message = $get_comment_read_only;
		}
	}
}

function partyflock_may_mail(): bool {
	return !partyflock_status(FLOCK_NO_MAIL);
}

function require_mailing(): bool {
	if (!partyflock_may_mail()) {
		register_warning('general:warning:no_mailing_LINE');
		http_response_code(503);
		return false;
	}
	return true;
}

function require_writable_partyflock(): bool {
	if (partyflock_read_only()) {
		register_error('partyflock_is_read_only_need_write');
		return false;
	}
	return true;
}

function pf_mail(
	string $to,
	string $subject,
	string $message,
	#[SensitiveParameter] array|string $headers = [],
	string $params = ''
): bool {
	if (partyflock_no_mail()) {
		register_warning('general:warning:no_mailing_LINE');
		http_response_code(503);
		return false;
	}
	if (defined('DRYRUN')
	&&	DRYRUN
	) {
		return true;
	}
	return mail($to, $subject, $message, $headers, $params);
}
