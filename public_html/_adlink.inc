<?php

function show_adlink(array $item): void {
	layout_open_menuitem();
	show_input([
		'onclick'	=> 'this.select()',
		'type'		=> 'text',
		'class'		=> 'tt clean right',
		'value'		=> ($url = get_hashlink($item)),
		'style'		=> 'width:'.(strlen($url) / 1.5).'em',
	]);
	layout_close_menuitem();
}

function verify_hash_link(array $item,string $element = null, ?int $id = null): int|false {
	$element ??= $_REQUEST['sELEMENT'];
	$id ??= $_REQUEST['sID'];
	$arg_hash = have_idnumber($_REQUEST, $element === 'invoice' ? 'UNIQID' : $element.'HASH');
	if (!$arg_hash) {
		return false;
	}
	robot_action('noindex');
	header('Cache-Control: no-store,no-cache,must-revalidate,max-age=0');
	if (ROBOT) {
		return false;
	}
	return	$arg_hash === (
			$element == 'invoice'
		?	$item['UNIQID']
		:	get_hashcrc($item,$id)
		);
}

function get_hashcrc(array $item, int $id): int {
	return crc32($item['RELATIONID'].':'.$id.':'.$item['STARTSTAMP'].':'.$item['STOPSTAMP']);
}

function get_hashlink(array $item, ?string $element = null, int $userid = 0): string {
	$element ??= $_REQUEST['sELEMENT'];
	$upper = strtoupper($element);
	if ($element == 'invoice') {
		return 'https://partyflock.'.CURRENTDOMAIN.'/invoice/'.$item['INVOICENR'].'?UNIQID='.$item['UNIQID'];
	}
	if (!isset($item['RELATIONID'])) {
		require_once '_itemlist.inc';
		return 'https://partyflock.'.CURRENTDOMAIN.'/'.$element.'/'.$item[$upper.'ID'].'?ITEMHASH='._item_hash($item,null,$element);
	}
	$id = $item[$element === 'invoice' ? 'INVOICENR' : $upper.'ID'];
	$path = '/';

	if ($element === 'promo') {
		$path .= $element.'/'.$id;
		$no_id = true;
	} elseif (!empty($item['PARTYID'])) {
		$party = memcached_party_and_stamp($item['PARTYID']);
		if (!$party) {
			return 'about:blank';
		}
		list($y,$m,$d) = _getdate($party['STAMP_TZI'],'UTC');
		$path .= 'party/day/'.$y.'/'.$m.'/'.$d;
	} elseif (
		isset($item['POSITION'])
	&&	in_array($item['POSITION'], [ADPOS_RIGHT_TOWER, ADPOS_SUBBAR], true)
	) {
		$path .= 'news';
	}
	$url = 'https://partyflock.'.CURRENTDOMAIN.$path.'?'.(isset($no_id) ? null : $upper.'ID='.$id.';').$upper.'HASH='.get_hashcrc($item,$id);
	if ($upper === 'NEWSAD') {
		$url .= '#nid'.$id;
	} elseif (!empty($item['PARTYID'])) {
		$url .= '#partyl'.$item['PARTYID'];
	}
	return $url;
}

function and_where_adhash(string $element, string $relationidname = 'RELATIONID'): ?string {
	if (have_admin($element)) {
		return null;
	}
	robot_action('noindex');

	return ROBOT
	?	' AND 0 '
	:	' AND (
				ACTIVE=1
			AND STARTSTAMP<'.CURRENTSTAMP.'
			OR  CRC32(
				CONCAT('.$relationidname.',":",'.$element.'ID,":",STARTSTAMP,":",STOPSTAMP)
				) = '.(have_idnumber($_REQUEST, strtoupper($element).'HASH') ?: 'NULL').'
	) ';
}
