<?php

function max_new_topics(string $element): bool {
	$per_hour_key = $element."postmax:".CURRENTIPSTR;
	$per_hour = ($new_user = $GLOBALS['currentuser']->row['CSTAMP'] > CURRENTSTAMP - 2 * ONE_DAY) ? 5 : 15;
	$new_cnt = memcached_increment($per_hour_key, 1, 1, ONE_HOUR);
	if ($new_cnt < $per_hour) {
		return false;
	}
	require_once '_flockbot.inc';
	flockbot_notify(
		FLOCK_CHANNEL_NOTIFICATIONS_FORUM,
		$element.'s limited to '.$per_hour.' for new user '.CURRENTUSERID.' and ip '.CURRENTIPSTR,
		FLOCKBOT_ASIS,
		get_element_href('user',CURRENTUSERID)
	);
	register_error(
		$new_user ? 'topic:error:new_user_max_per_hour' : 'topic:error:user_max_per_hour',
		['PER_HOUR' => $per_hour]
	);
	return true;
}

function max_new_posts(string $element): bool {
	if ($GLOBALS['currentuser']->row['CSTAMP'] < CURRENTSTAMP - 1 * ONE_DAY) {
		return false;
	}
	if (memcached_add('forumpostmax:5:'.CURRENTIPSTR, true, FIVE_MINUTES)) {
		return false;
	}
	if (memcached_add('forumpostmax:warned:'.CURRENTIPSTR, true, QUARTER)) {
		require_once '_flockbot.inc';
		flockbot_notify(
			FLOCK_CHANNEL_NOTIFICATIONS_FORUM,
			$element.'s limited to once per 5 minutes for user '.CURRENTUSERID.' and '.CURRENTIPSTR,
			FLOCKBOT_ASIS,
			get_element_href('user', CURRENTUSERID)
		);
	}
	register_error('topic:error:new_user_1_per_5_mins');
	return true;
}
