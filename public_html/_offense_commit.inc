<?php

function offense_commit() {
	require_once '_offense_cleanup_element.inc';
	require_once '_offense_points.inc';
	require_once '_offense_ban.inc';
	if (!CLI
	&&	(!require_admin())
	||	!require_idnumber($_POST,'USERID')
	) {
		return;
	}
	$_REQUEST['subID'] = $_POST['USERID'];
	if (!require_something($_POST,'TYPE_POINTS')
	||	!require_something($_POST,'ELEMENT')
	||	!require_anything_trim($_POST,'EXTRA')
	||	!($id = require_idnumber($_POST,'ID'))
	) {
		return;
	}
	list($type,$points) = explode(':',$_POST['TYPE_POINTS']);
	$element = $_POST['ELEMENT'];
/*	if ($element == 'directmessage'
	&&	!strncmp($type,'spam',4)
	) {
		if (CURRENTUSERID != 2269) {
			_error('t0mm1e laten doen!');
			return;
		}
	}*/
	$offenderid = $_POST['USERID'];
	$setlist[] = 'ID='.$id;
	require_once '_offense_access.inc';
	if (!CLI
	&&	!may_create_offense($element,$id)
	) {
		register_error('error:not_enough_rights_for_operation_LINE');
		return;
	}
	if ($element == 'albummap') {
		if (false === require_number($_POST,'INCLUSIVE')) {
			return false;
		}
	}
	if ($element == 'profile_image') {
		if (!require_array($_POST,'WHICH')) {
			return false;
		}
	}
	$status = db_single('user_account','SELECT STATUS FROM user_account WHERE USERID='.$offenderid,DB_USE_MASTER);
	if ($status === false) {
		return;
	}
	if (!$status) {
		register_error('user:error:nonexistent_LINE',array('ID'=>$offenderid));
		return;
	}
	$altid = have_idnumber($_POST,'ALTID');
	require_once 'defines/offense.inc';
	require_once '_element_lastchange.inc';
	extract(get_offense_defines(CURRENTSTAMP));
	$element_lastchange = element_lastchange($element,$id,$offenderid);
	$existing = db_single(
		array('offense','offense_reference_element'),'
		SELECT OFFENSEID
		FROM offense
		LEFT JOIN offense_reference_element AS ref USING (OFFENSEID)
		WHERE USERID='.$offenderid.'
/* 		  AND BY_USERID='.CURRENTUSERID.'*/
		  AND TYPE="'.addslashes($type).'"
		  AND ELEMENT="'.addslashes($element).'"
		  AND REMOVED=0
		  AND CSTAMP>='.$element_lastchange.'
		  AND CSTAMP>='.(CURRENTSTAMP - $OFFENSE_ACTIVE_PERIOD).'
		  AND offense.ID='.$id.'
		  AND (NOT '.$altid.' AND ISNULL(ref.ID) OR '.$altid.'=ref.ID)',
		DB_USE_MASTER
	);
	if ($existing === false) {
		return;
	}
	if ($existing) {
		register_warning('offense:warning:already_exists_LINE');
		$_REQUEST['sID'] = $existing;
		return;
	}
	$regonly =
		$element != 'user'
	&&	$points
	&&	$points !== 'p'
	&&	isset($_POST['REGISTERONLY']);
	$setlist[] = 'BY_USERID='.	CURRENTUSERID;
	$setlist[] = 'TYPE="'.		addslashes($type).'"';
	$setlist[] = 'ELEMENT="'.	addslashes($element).'"';
	$setlist[] = 'CSTAMP='.		CURRENTSTAMP;
	$setlist[] = 'EXTRA="'.		addslashes($_POST['EXTRA']).'"';
	$setlist[] = 'POINTS='.		($points === 'p' ? 'NULL' : $points);
	$setlist[] = 'REGONLY='.	($regonly ? 1 : 0);

	$realoffenderid = $offenderid;
	$offenders = array();
	$remove = null;

	$multi = isset($_POST['REACH']);

	if ($element == 'directmessage') {
		$remove = isset($_POST['CLEANUP']) && ($_POST['CLEANUP'] == 'remove');
		require_once '_anonymize.inc';
		$info = find_anonymised_matches($id,!strncmp($type,'spam',4) || !strncmp($type,'drugs',5),$multi,$remove);
		if (!$info) {
			return;
		}
		if ($multi) {
			foreach (['distinct_senders','distinct_delayed'] as $key) {
				if (!empty($info[$key])) {
					$offenders += $info[$key];
				}
			}
			$sortedoffenders = array($realoffenderid => $offenders[$realoffenderid]);
			foreach ($offenders as $offenderid => $cnt) {
				if ($offenderid == $realoffenderid) {
					continue;
				}
				$sortedoffenders[$offenderid] = $cnt;
			}
			$offenders = $sortedoffenders;
		} else {
			$offenders = [$offenderid=>true];
		}
	} else {
		$offenders = [$offenderid=>true];
	}
	$perform_propose =
			!empty($_POST['PROPOSE'])
/*		&&	(	$type == 'inappropriate'
			||	$type == 'offensive_provocative'
			||	$type == 'thread'
			||	$type == 'discrimination_racism'
			)*/;

	$multi = $multi && count($offenders) > 1;
	$baseid = null;

	foreach ($offenders as $offenderid => $cnt) {
/*		if ($cnt <= 1) {
			# skip single message?
			continue;
		}*/
		$copylist = $setlist;
		$copylist[] = 'USERID='.$offenderid;

		if (!db_insert('offense','INSERT INTO offense SET '.implode(',',$copylist))) {
			return;
		}
		$arg = array('USERID'=>$offenderid);
		register_notice('offense:notice:decreed_LINE',DO_UBB,$multi ? $arg : array('USERID'=>0));

		$offenseid = db_insert_id();

		if ($altid) {
			db_insert('offense_reference_element','
				INSERT INTO offense_reference_element SET
					OFFENSEID	='.$offenseid.',
					ID		='.$altid
			);
		}
		if ($offenderid == $realoffenderid) {
			$baseid = $offenseid;
		} elseif ($baseid !== null) {
			db_insert('offense_reference_offense','
				INSERT INTO offense_reference_offense SET
					BASEID		='.$baseid.',
					OFFENSEID	='.$offenseid
			);
		}
		$pts = get_offense_points($offenderid);
		if ($pts === false) {
			return;
		}
		if ($offenderid == $realoffenderid) {
			$_REQUEST['sID'] = $offenseid;
		}
		require_once '_offense_log_element.inc';
		if (!offense_log_element($element,$id,$offenseid)) {
			error_log('fail or empty for offense_log_element of '.$element.' with id '.$id.' for offense '.$offenseid,0);
		}
		register_notice('offense:notice:points_overview_LINE',$pts);// + ($multi ? $arg : array()));
		if (!$regonly
		&&	$points
		) {
			create_banpoints($offenderid,$offenseid);
		}
		$counts[$offenderid] = $offenseid;
		if (!$multi) {
			break;
		}
	}
	$offenderid = $realoffenderid;
	require_once '_prohibit.inc';
	switch ($element) {
	case 'albumelement':
		if (($albumelement = prohibit_albumelement($id,$type,$offenseid,$perform_propose))) {
			$albumelements = array($albumelement);
		}
		break;
	case 'albummap':
		$albumelements = prohibit_albummap($id,$type,$offenseid,$perform_propose);
		break;
	case 'profile_image':
		if ($perform_propose) {
			foreach ($_POST['WHICH'] as $which) {
				$large = $which == 'large' ? 1 : 0;
				$prohibids[$which] = prohibit_userimage($id,$large,$type,$offenseid);
			}
		}
		break;
	case 'nick':
		if ($perform_propose) {
			prohibit_nick($id,$type,$offenseid);
		}
		break;
	}
	if (!empty($albumelements)) {
		foreach ($albumelements as $albumelement) {
			$ids = null;
			# FIXME: don't insert same data more than once, check CRC and LEN
			foreach (array('THMB','DATA') as $which) {
				$storeid = $albumelement[$which.'STOREID'];
				$dataid = $albumelement[$which.'ID'];
				if (!($data = dbdata_single('albums',$storeid,'SELECT DATA FROM albums.u'.$storeid.' WHERE DATAID='.$dataid))
				||	!db_insert('image_reference_data','
					INSERT INTO image_reference_data SET DATA="'.addslashes($data).'"')
				||	!($ids[$which] = db_insert_id())
				||	!db_insert('image_reference_meta','
					INSERT INTO image_reference_meta SET
						DATAID	='.$ids[$which].',
						CRC	='.$albumelement[$which.'CRC'].',
						LEN	='.$albumelement[$which.'LEN'].',
						WIDTH	='.$albumelement[$which.'WIDTH'].',
						HEIGHT	='.$albumelement[$which.'HEIGHT'].',
						FILETYPE="'.$albumelement[$which.'FILETYPE'].'",
						CSTAMP	='.CURRENTSTAMP.',
						CUSERID	='.CURRENTUSERID.',
						OFFENSEID='.$offenseid)
				) {
					continue;
				}
			}
			if (isset($albumelement['PROHIBID'])) {
				if (!db_insert('prohibited_image','
					INSERT INTO prohibited_image SET
						PROHIBID='.$albumelement['PROHIBID'].',
						DATAID	='.$ids['DATA'].',
						THMBID	='.$ids['THMB'])
				) {
				}
			}
			if (!db_insert('albumelement_reference','
				INSERT INTO albumelement_reference SET
					ALBUMELEMENTID	='.$albumelement['ALBUMELEMENTID'].',
					THMBID		='.$ids['THMB'].',
					DATAID		='.$ids['DATA'].',
					OFFENSEID	='.$offenseid
				)
			) {
			}
		}
	} elseif ($element == 'profile_image') {
		$ids = null;
		# FIXME: don't insert same data more than once, check CRC and LEN
		foreach ($_POST['WHICH'] as $which) {
			$large = $which == 'large' ? 1 : 0;
			if (($userimage = db_single_assoc('userimage','
				SELECT DATAID,WIDTH,HEIGHT,TYPE,LEN
				FROM userimage
				JOIN userimagedatameta USING (DATAID)
				WHERE LARGE='.$large.'
				  AND USERID='.$id))
			&&	($data = db_single('userimagedata','SELECT SQL_NO_CACHE DATA FROM data_db.userimagedata WHERE DATAID='.$userimage['DATAID'],DB_USE_MASTER))
			) {
				if (!db_insert('image_reference_data','
					INSERT INTO image_reference_data SET DATA="'.addslashes($data).'"')
				||	!($dataid = db_insert_id())
				||	!db_insert('image_reference_meta','
					INSERT INTO image_reference_meta SET
						DATAID	='.$dataid.',
						CRC	='.crc32($data).',
						LEN	='.$userimage['LEN'].',
						WIDTH	='.$userimage['WIDTH'].',
						HEIGHT	='.$userimage['HEIGHT'].',
						FILETYPE="'.$userimage['TYPE'].'",
						CSTAMP	='.CURRENTSTAMP.',
						CUSERID	='.CURRENTUSERID.',
						OFFENSEID='.$offenseid)
				) {
					continue;
				}
				if (!empty($prohibids[$which])) {
					if (!db_insert('prohibited_image','
						INSERT INTO prohibited_image SET
							PROHIBID='.$prohibids[$which].',
							THMBID	='.$dataid.',
							DATAID	='.$dataid)
					) {
					}
				}
				if (!db_insert('userimage_reference','
					INSERT INTO userimage_reference SET
						USERID		='.$id.',
						DATAID		='.$dataid.',
						LARGE		='.$large.',
						OFFENSEID	='.$offenseid)
				) {
				}
			}
		}
	}
	if ($chatban = have_idnumber($_POST,'CHATBAN')) {
		if (db_insert('chatban','INSERT INTO chatban SET
				USERID='.$_REQUEST['USERID'].',
				CSTAMP='.CURRENTSTAMP.',
				CUSERID='.CURRENTUSERID.',
				EXPIRES='.(CURRENTSTAMP + $chatban).',
				OFFENSEID='.$offenseid)
		) {
			register_notice('offense:notice:chatban_initiated_LINE',array('DATETIME'=>_datedaytime_get(CURRENTSTAMP+$chatban)));
		}
	}
	// null remove for default

	switch ($type) {
	default:
		offense_cleanup_element($offenderid,$element,$id,$counts,$remove === null ? false : $remove,$type);
		break;
	case 'spam':
	case 'spam_other':
	case 'spam_ad':
	case 'drugs_dealing':
	case 'drugs_incitement':
		offense_cleanup_element($offenderid,$element,$id,$counts,$remove === null ? true : $remove);
		break;
	case 'offensive_provoking':
	case 'discrimination_racism':
	case 'threat':
	case 'nonsense':
	case 'no_price':
	case 'price_too_high':
		offense_cleanup_element($offenderid,$element,$id,$counts,$remove === null ? ($element != 'directmessage') : $remove);
		break;
	case 'incorrect_connection':
		switch ($element) {
		case 'albumelement':
			if (!db_update(
				'albumelement','
				UPDATE albumelement SET
					PARTYID	=0,
					MUSERID	='.CURRENTUSERID.',
					MSTAMP	='.CURRENTSTAMP.'
				WHERE ALBUMELEMENTID='.$id)
			) {
				return;
			}
			require_once '_album.inc';
			flush_albumelement($id);
			break;
		case 'albummap':
			if (isset($_POST['INCLUSIVE'])
			&&	($albumid = db_single('albummap','SELECT ALBUMID FROM albummap WHERE MAPID='.$id))
			) {
				require_once '_albummapdisconnect.inc';
				if (!disconnect_albummap_contents($albumid,$id)) {
					return;
				}
			}
			if (!db_update(
				'albummap','
				UPDATE albummap SET
					PARTYID	=0,
					MUSERID	='.CURRENTUSERID.',
					MSTAMP	='.CURRENTSTAMP.'
				WHERE MAPID='.$id)
			) {
				return;
			}
			break;
		}
	}
}
