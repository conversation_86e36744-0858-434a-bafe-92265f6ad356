<?php

require_once '_flockmod.inc';
require_once '_form.inc';
require_once '_http_status.inc';
require_once 'defines/timing.inc';

/*function is_valid_utf8(string $text): bool {
	return preg_match('%^(?:
		  [\x09\x0A\x0D\x20-\x7E]			# ASCII
		| [\xC2-\xDF][\x80-\xBF]			# non-overlong 2-byte
		| \xE0[\xA0-\xBF][\x80-\xBF]		# excluding overlongs
		| [\xE1-\xEC\xEE\xEF][\x80-\xBF]{2}	# straight 3-byte
		| \xED[\x80-\x9F][\x80-\xBF]		# excluding surrogates
		| \xF0[\x90-\xBF][\x80-\xBF]{2}		# planes 1-3
		| [\xF1-\xF3][\x80-\xBF]{3}			# planes 4-15
		| \xF4[\x80-\x8F][\x80-\xBF]{2}		# plane 16
		)*$%x', $text);
}*/

function strip_uri_args(string $url): string {
	if (!str_contains($url, '?')) {
		return $url;
	}
	if (null === ($new_url = preg_replace('"\?.*$"', '', $url))) {
		preg_failure($url);
	} else {
		$url = $new_url;
	}
	return $url;
}

function get_control_chars(): string {
	static $__control_chars = "\x1\x2\x3\x4\x5\x6\x7\x8\xc\xe\xf\x10\x11\x12\x13\x14\x15\x16\x17\x18\x19\x1a\x1b\x1c\x1d\x1e\x1f";
	return $__control_chars;
}

function remove_control_chars(?string $text, bool $utf8 = false): ?string {
	if ($text) {
		if (null === ($new_text = preg_replace("'[".get_control_chars()."]+'".($utf8 ? 'u' : ''), '', $text))) {
			preg_failure($text);
		} else {
			$text = $new_text;
		}
		return $text;
	}
	return null;
}

const NONEXISTENT_WIN1251_CHARS = "\x81\x8d\x8f\x90\x9d";

function remove_nonexistent_win1252_chars(string $text): string {
	if (null === ($new_text = preg_replace("'[".NONEXISTENT_WIN1251_CHARS."]+'", '', $text))) {
		preg_failure($text);
	} else {
		$text = $new_text;
	}
	return $text;
}

function utf8ord(string $char): int {
	[, $ord] = unpack('N', mb_convert_encoding($char, 'UCS-4BE', 'UTF-8'));
	return $ord;
}

function fix_facebook_description(array &$array, string $field): string {
	require_once '_unicode.inc';

	if (null === ($new_text = preg_replace(
		[	'!'.MATCH_ANY_FORMATTING.'!u',
			"!\n{3,}!um" # downsize 3+ newlines to 2
		],[ '',
			"\n\n"],
		str_replace("\r", '', $array[$field])))
	) {
		preg_failure($array[$field]);
	} else {
		$array[$field] = $new_text;
	}
	return  $array[$field];
}

function is_ip_address(string $ipstr, $allow_local = true): bool {
	static $__is_ip_address = [];
	return $__is_ip_address[$ipstr] ??= (false !== filter_var(
		$ipstr,
		FILTER_VALIDATE_IP,
		FILTER_FLAG_NO_RES_RANGE | ($allow_local ? 0 : FILTER_FLAG_NO_PRIV_RANGE)
	));
}

function ipbin_is_ipv4(string $ipbin): bool {
	return strlen($ipbin) === 4;
}

function ipbin_to_ipnum(string $ipbin): int|false {
	# returns false for ipv6
	return ip2long(inet_ntop($ipbin));
}

const IP_PAD = 1;

function ipbin_to_prefix_and_iident(string $ipbin, int $flags = 0): array {
	static $__stored;
	if (isset($__stored[$ipbin][$flags])) {
		return $__stored[$ipbin][$flags];
	}
	switch ($len = strlen($ipbin)) {
	case 4:
		# Force array, because it cannot fail
		$parts = (array)unpack('N1', $ipbin);
		$prefix = 'FFFF';
		$iident = sprintf($flags & IP_PAD ? '%08X' : '%X', $parts[1]);
		break;

	case 16:
		# Force array, because it cannot fail
		$parts = (array)unpack('N4',$ipbin);
		$ip6 = sprintf('%08X%08X%08X%08X', $parts[1], $parts[2], $parts[3], $parts[4]);
		$prefix = substr($ip6,  0, 16);
		$iident = substr($ip6, 16, 16);
		if (!($flags & IP_PAD)) {
			$prefix = ltrim($prefix, '0') ?: 0;
			$iident = ltrim($iident, '0') ?: 0;
		}
		break;

	default:
		mail_log('weird IPBIN length: '.$len);
		$iident = '';
		$prefix = '';
		break;
	}
	return $__stored[$ipbin][$flags] = [$prefix, $iident];
}

function get_netip_and_masksize(string $ipstr, bool $p2p = false): array {
	if (preg_match('"^(.*?)(:*[:.])$"', $ipstr, $match)) {
		$net = true;
		if ($match[2] === '.') {
			$parts = [];
			foreach (explode('.', $ipstr) as $part) {
				if (!isset($part[0])) {
					continue;
				}
				$parts[] = $part;
			}
			$mask_size = 0;
			for ($i = 0; $i < 4; ++$i) {
				if (!isset($parts[$i])) {
					$parts[$i] = '0';
				} else {
					$mask_size += 8;
				}
			}
			$ipstr = implode('.', $parts);
		} else {
			$ipstr = $match[1].'::';
			$parts = explode(':', $ipstr);
			$mask_size = 0;
			foreach ($parts as $part) {
				if (!isset($part[0])) {
					break;
				}
				$mask_size += 8;
			}
		}
	} elseif (str_contains($ipstr, ':')) {
		if ($p2p) {
			$mask_size = 128;
			$net = false;
		} else {
			$mask_size = 64;
			$ipbin = (string)inet_pton($ipstr);
			$parts = (array)unpack('N4', $ipbin);
			$ip6 = sprintf('%08X%08X0000000000000000', $parts[1] ?? 0, $parts[2] ?? 0);
			$ipstr = inet_ntop(hex2bin($ip6));
			$net = true;
		}
	} else {
		$mask_size = 32;
		$net = false;
	}
	return [$ipstr, $mask_size, $net];
}

function prefix_and_iident_to_ipbin(string $prefix, string $iident): string {
	if ($prefix === 'FFFF') {
		# ipv4
		return hex2bin(str_pad($iident, 8, '0', STR_PAD_LEFT));
	}
	# ipv6
	$prefix = str_pad($prefix, 16, '0', STR_PAD_LEFT);
	$iident = str_pad($iident, 16, '0', STR_PAD_LEFT);

	return hex2bin($prefix.$iident);
}

function prefix_and_iident_to_ipstr(string $prefix, string $iident): string {
	return inet_ntop(prefix_and_iident_to_ipbin($prefix, $iident));
}

function print_rr_set_force(?bool $set = null, bool $reset = false): ?bool {
	static $__force = null;
	if ($reset) {
		return $__force = null;
	}
	if ($set !== null) {
		return $__force = $set;
	}
	return $__force;
}

function print_rr_hide_location(?bool $set = null, bool $reset = false): ?bool {
	static $__hide_location = null;
	if ($reset) {
		return $__hide_location = null;
	}
	if ($set !== null) {
		return $__hide_location = $set;
	}
	return $__hide_location;
}

function reset_print_rr(): void {
	print_rr_set_force(reset: true);
	print_rr_hide_location(reset: true);
}

function print_rr(
	mixed   $elem,
	?string $name		   = null,
	bool	$utf8		   = false,
	bool	$force		   = false,
	bool	$hide_location = false,
	?string	$data		   = null,
): void {
	require_once '_require.inc';
	require_once '_spider.inc';

	if ($data) {
		mail_log('$data is set in print_rr', get_defined_vars());
	}
	$force		   = $force			|| print_rr_set_force();
	$hide_location = $hide_location || print_rr_hide_location();

	/** @noinspection UsingInclusionOnceReturnValueInspection */
	if (!$force
	&&  (	# hide from robots
			(require_once '_spider.inc')
		&&	ROBOT
		||  # always show on VIP and sandbox
			(require_once '_servertype.inc')
		&&	!SERVER_VIP
		&&	!SERVER_SANDBOX
		# always show when run from cli
		&&	!CLI
		# always show to Thomas
		&&	!HOME_THOMAS
		# always show to development users
		&&	!have_admin('development')
	)) {
		return;
	}
	[$file, $line, $function] = get_interesting_caller();
	if ($to_error_log
	=	!empty($_SERVER['REQUEST_URI'])
	&&	preg_match('"\.(?:json|js|png|gif|jpe?g|webp)$"i', $_SERVER['REQUEST_URI'])
	) {
		ob_start();
	}
	if ($to_error_log || CLI) {
		if (!$hide_location) {
			echo 'called from ', $file, ($function ? ' => '.$function : ''), ' on line ', $line, "\n";
		}
	 	if ($name) {
			echo $name ?>:<?
			/*<?= gettype($elem) ?> = <?*/
		}
		ob_start();
		var_dump($elem);
		echo compact_var_dump(ob_get_clean());
	} else {
		?><pre><?
		if (!$hide_location) {
			?><span class="small light7"><?
			?>called from <span class="notice"><?= escape_utf8($file)	  ?></span><?
				?> &rarr; <span class="notice"><?= escape_utf8($function) ?></span><?
			   ?> on line <span class="notice"><?= escape_utf8($line)	  ?></span><?
			?></span><br /><?
		}
		if ($name) {
			echo escape_specials($name, $utf8);
		}
		?>:<?= escape_specials(gettype($elem)) ?> = <?

		if (is_array ($elem)
		||	is_object($elem)
		) {
			$nullify = static function(mixed $arg) use (&$nullify, $utf8): array {
				$res = [];
				foreach ((is_object($arg) ? get_object_vars($arg) : $arg) as $key => $val) {
					$check_variables = ['key'];
					if (is_array($val)
					||	is_object($val)
					) {
					 	$val = $nullify($val);
					} else {
						$check_variables[] = 'val';
					}
					foreach ($check_variables as $check_variable) {
						if (is_int($$check_variable)) {
							$$check_variable = '<span style="color: cyan; font-weight:bold;">'.$$check_variable.'</span>';
						} elseif (is_float($$check_variable)) {
							$$check_variable = '<span style="color: #66F; font-weight:bold;">'.$$check_variable.'</span>';
						} elseif ($$check_variable === '') {
							$$check_variable = '<span style="color: gray; font-weight:bold;">&laquo;&raquo;</span>';
						} elseif ($$check_variable === null) {
							$$check_variable = '<span style="color: orange; font-weight:bold;">null</span>';
						} elseif ($$check_variable === true || $$check_variable === "\x1") {
							$$check_variable = '<span style="color: #0F0; font-weight:bold;">true</span>';
						} elseif ($$check_variable === false || $check_variable === "\x0") {
							# FIXME: comparison to "\x0" does not work
							$$check_variable = '<span style="color: #F00; font-weight:bold;">false</span>';
						} elseif (is_string($$check_variable)) {
							$$check_variable =
								'<span style="color: gray; font-weight:bold;">&laquo;</span>'.
								escape_specials($$check_variable, $utf8).
								'<span style="color: gray; font-weight:bold;">&raquo;</span>';
						} else {
							$$check_variable = escape_specials($$check_variable, $utf8);
						}
					}
					$res[$key] = $val;
				}
				return $res;
			};
			$elem = $nullify($elem);
		}
		if (!$data) {
			ob_start();
			print_r($elem);
			$data = compact_var_dump(ob_get_clean());
		}
		echo $data;
		?></pre><?
	}
}

function get_r(mixed $item, ?string $name = null): string {
	ob_start();
	if ($name) {
		echo $name, ':';
	}
	print_r($item);
	return ob_get_clean();
}

function var_get(mixed $var): string {
	ob_start();
	var_dump($var);
	$var_dump_message = ob_get_clean();
	# strip first line, contains file + line of var_dump call above
	if ($new_var_dump_message = preg_replace('"^([^\n]+\n)"', '', $var_dump_message)) {
		$var_dump_message = $new_var_dump_message;
	}
	return compact_var_dump(utf8_myrtrim($var_dump_message));
}

function get_interesting_caller(): array {
	foreach (debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 4) as $i => $trace) {
		if (!$i) {
			continue;
		}
		$file	  = $trace['file']	   ?? null;
		$line	  = $trace['line']	   ?? null;
		$function = $trace['function'] ?? null;

		if ($function !== 'get_interesting_caller'
		&&	$function !== 'db_log_message'
#		&&	$function !== 'print_rr'
		) {
			break;
		}
	}
	return [$file ?? null, $line ?? null, $function ?? null];
}

function compact_var_dump(string $text): string {
	if (null !== ($compact_text = preg_replace(
		[
			"'=>(?:\x1B\[0m)?\n\s*'",
			# RegExp below manages to match colorized output from var_dump,
			# to remove var_dump location in _helper.inc, since it's not useful information:
			'!\n?[^/]*(?:(?:/mnt/largepart)?(?:/home)?/party|/mnt/share/sandbox_?[a-z]+)/public_html/_helper\.inc[^:]*:\D*\d+[^:]*:[^\n]*\n*!u',
			# Replace tabs by spaces:
			'",(?!\h)"u', '"\t"u'
		],[
			'=> ',
			'',
			', ', '    '
		], $text))
	) {
		$text = $compact_text;
	} else {
		preg_failure($text);
	}
	return $text;
}

function error_log_r(mixed $item, string|int|null $name = null, bool $hide_source = false): string {
	[$file, $line, $function] = get_interesting_caller();
	$output = '';
	if (!$hide_source
	&&	(	$file
		||	$function
		||	$line
		)
	) {
		$output .= "$file, @ $line\n";
	}
	if ($name) {
		$output .= "$name: ";
	}
	ob_start();
	var_dump($item);
	error_log($output .= compact_var_dump(ob_get_clean()));
	return $output;
}

function escape_null(string $message): string {
	return str_replace(chr(0), "\\00", $message);
}

/** @noinspection PhpUnused */
function apcu_increment(string $key, int $step = 1, int $initial = 1, int $ttl = 60): int|false {
	if (false === apcu_fetch($key)) {
		if (false === apcu_store($key, $initial, $ttl)) {
			return false;
		}
		return $initial;
	}
	return apcu_inc($key, $step, ttl: $ttl);
}
function apcu_fetch_int(string $key): int|false {
	return apcu_fetch($key);
}
/*function shm_increment(string $key, int $step = 1, int $initial = 1, int $expire = 60): int|false {
	# incrementing re-sets the expire further in time

	# 10 Kilobyte, enough for quite a few counters
	if (!($shm = shm_attach(0xCAFEDBDB, size: 10240))) {
		return false;
	}

	$id = crc32($key);

	static $__number_to_keys;

	$__number_to_keys[$id]	[$key] = true;
	$__number_to_keys[$id + 1][$key] = true;

	if (count($__number_to_keys[$id]	) > 1
	||	count($__number_to_keys[$id + 1]) > 1
	) {
		mail_log('hash overflow', get_defined_vars());
		# poor mans limitation, once every 60 seconds if a hit happens on right moment:
		return !(CURRENTSTAMP % $expire);
	}

	$cnt = shm_get_var($shm, $id);
	$stored_expire = shm_get_var($shm, $id + 1);
	if (CURRENTSTAMP >= $stored_expire) {
		$cnt = false;
	}
	$new_value = $cnt === false ? $initial : ($cnt + $step);

	if (shm_put_var($shm, $id, $new_value)
	&&	shm_put_var($shm, $id + 1, CURRENTSTAMP + $expire)
	) {
		return $new_value;
	}
	return false;
}*/

function get_plain_stack(?string $message = null): string {
	ob_start();
	xdebug_print_function_stack($message ?: 'user triggered', XDEBUG_STACK_NO_DESC);
	return ob_get_clean();
}

function get_stack(): string {
	return print_stack(get: true);
}

function print_stack(?string $message = null, bool $get = false): ?string  {
	ini_set('html_errors', true);	# NOSONAR
	ob_start();
	echo '<style>.xe-debug { color #000; }</style>';
	xdebug_print_function_stack($message ?: 'user triggered');
	$stack = ob_get_clean();
	ini_set('html_errors', false); // NOSONAR
	if ($get) {
		return $stack;
	}
	echo $stack;
	return null;
}

/** @noinspection SensitiveParameterInspection */
function mail_log(
	?string	$message	= null,
	mixed	$item		= null,
	?string	$subject	= null,
	?string	$code		= null,
	int		$per		= ONE_MINUTE,
	?string	$error_log	= null,
	?bool	$to_page	= false,
): void {
	require_once '_date.inc';
	$nobody_is_watching = !CLI || isset($_SERVER['CRON']);
	$hash = hash('xxh128', $subject."\x1F".$message);
	static $__done;
	if (isset($__done[$hash])) {
		return;
	}
	$__done[$hash] = true;
	if ($nobody_is_watching) {
		# Don't sent identical messages more than once per minute
		$key = 'mail_log:'.$hash;
		if (!log_sporadically($key, per: $per)) {
			return;
		}
	}
	if ($nobody_is_watching) {
		$mail_message = '<html lang="en"><body><pre>'.escape_utf8($message);
	} else {
		$mail_message = $message;
	}
	if ($item) {
		$mail_message .= escape_utf8(var_get($item));
	}
	if ($code) {
		$infos['CODE'] = $code;
	}
	if (!empty($GLOBALS['argv'])) {
		$infos['COMMAND'] = escape_utf8(implode(' ', $GLOBALS['argv']));
	}
	require_once '_smallscreen.inc';
	if (isset($_SERVER['HTTP_HOST'], $_SERVER['REQUEST_URI'])) {
		# Combining both host and uri into single url makes link in mail clickable
		$infos['REQUEST_URL'] = "https://{$_SERVER['HTTP_HOST']}{$_SERVER['REQUEST_URI']}";
	}
	if (!empty($_SERVER['HTTP_REFERER'])) {
		$infos['HTTP_REFERER'] = $_SERVER['HTTP_REFERER'];
	}
	if (!empty($_SERVER['REMOTE_ADDR'])) {
		$infos['REMOTE_ADDR'] = ($ipstr = $_SERVER['REMOTE_ADDR']);
		$ipbin = inet_pton($ipstr);
		if ($remote_hostname = memcached_try_hostname_from_ipbin($ipbin, $ipstr)) {
			$infos['REMOTE_HOSTNAME'] = $remote_hostname;
		}
	}
	if (!empty($_SERVER['HTTP_USER_AGENT'])) {
		$infos['USER_AGENT'] = $_SERVER['HTTP_USER_AGENT'];
	}
	require_once '_spider.inc';
	require_once '_domain.inc';
	require_once '_currentuser.inc';
	$userid = _currentuser::get_userid();
	$infos['ROBOT']			= ROBOT ? 'yes' : 'no';
	$infos['CURRENTUSER']	= $userid ? "userid: $userid, nick: ".win1252_to_utf8(memcached_nick($userid) ?: '') : 'not set';
	$infos['CURRENTDOMAIN']	= CURRENTDOMAIN;
	$infos['CURRENTIPSTR']	= CURRENTIPSTR;
	$infos['CURRENTIDENTID']= CURRENTIDENTID;
	$infos['SMALL_SCREEN']	= (defined('SMALL_SCREEN') ? (SMALL_SCREEN ? 'yes' : 'no') : 'not set');
	$infos['HOSTNAME']		= gethostname();
	$infos['DATETIME']		= _datetime_get_numeric(time());
	$infos['COOKIE']		= escape_utf8(var_get($_COOKIE));

	$mail_message .= "\n\n";

	foreach ($infos as $key => $value) {
		# longest possible key 15 chars: REMOTE_HOSTNAME
		$mail_message .= $key.': '.str_repeat(' ', 15 - strlen($key)).$value."\n";
	}

	if (HOME_THOMAS) {
		$error_log ??= 'DEBUG';
	}

	if ($nobody_is_watching) {
		$mail_message .= '</pre>'.get_stack();
	} else {
		ob_start();
		xdebug_print_function_stack();
		$mail_message .= ob_get_clean();
	}
	if ($nobody_is_watching) {
		if ('partyflock.nl' === ($hostname = gethostname())) {
			$hostname = 'preparty';
		}
		$mail_message .= '</body></html>';
		$subject ??= 'log message';
		$headers = "From: $hostname: $subject <<EMAIL>>\r\n".
				   "MIME-Version: 1.0\r\n".
				   "Content-type:text/html; charset=UTF-8\r\n";
		$caller ??= debug_backtrace(limit: 1)[0];

		if (!mail(	'watcher <<EMAIL>>',
					"$hostname: $subject, ".basename($caller['file']).":{$caller['line']}",
					# preferred max line length: 78, absolute max: 998
					wordwrap($mail_message, width: 150, cut_long_words: true),
					$headers)
		) {
			error_log_r([
				'subject'		=> $subject,
				'mail_message'	=> $mail_message,
			], 'FAILURE TO mail_log');
		}
	} else {
		echo $mail_message;
	}
	if ($error_log) {
		$caller ??= debug_backtrace(limit: 1)[0];
		error_log($error_log.' '.$message.', '.basename($caller['file']).":{$caller['line']} @ {$_SERVER['REQUEST_URI']}");
	}
	if ($to_page) {
		_error($mail_message);
	}
}

function mail_log_register_generic(
	?string	$message = null,
	mixed	$item	 = null,
	?string	$subject = null,
): void {
	mail_log($message, $item, $subject, register_generic_error());
}
/** @noinspection SensitiveParameterInspection */
function implodehash(string $pair_separator, string $key_value_separator, array $hash): string {
	$pairs = [];
	foreach ($hash as $key => $val) {
		$pairs[] = $key.$key_value_separator.$val;
	}
	return implode($pair_separator,$pairs);
}

/** @noinspection PhpUnusedParameterInspection */
function add_quote(string|int &$val, string|int $key, string $quote): void { // NOSONAR
	$val = $quote.$val.$quote;
}

/** @noinspection PhpUnusedParameterInspection */
function add_slashes_quote(string|int &$val, string|int $key, string $quote): void { // NOSONAR
	$val = $quote.addslashes($val).$quote;
}

function stringsimplode_noslash(?string $sep, array $array, string $quote = '"'): string {
	array_walk($array, add_quote(...), $quote);
	return implode($sep, $array);
}

function stringsimplode(?string $separator, array $array, string $quote = '"'): string {
	array_walk($array, add_slashes_quote(...), $quote);
	return implode($separator, $array);
}

function stringsimplodekeys(?string $separator, array $array, string $quote = '"'): string {
	$str = '';
	foreach ($array as $key => $ignored) {
		if ($str) {
			$str .= $separator;
		}
		$str .= $quote.addslashes($key).$quote;
	}
	return $str;
}

function anyscale(
	int|float	&$width,
	int|float	&$height,
	int			$new_width,
	int			$new_height = 0,
): bool {
	if (!$width || !$height) {
		return false;
	}
	if ($set_new_width
	=	$new_width
	&&	$new_width !== $width
	) {
		$height = $new_width * $height / $width;
		$width  = $new_width;
	}
	if ($new_height
	&&	$new_height !== ($set_new_width ? (int)round($height) : $height)
	) {
		$width  = $new_height * $width / $height;
		$height = $new_height;
	}
	$width  = (int)round($width);
	$height = (int)round($height);
	return true;
}

function scale(
	int|float	&$width,
	int|float	&$height,
	int			$max_width,
	int			$max_height	= 0,
	bool		$make_integer	= true
): void {
	if ($max_width
	&&	$width > $max_width
	) {
		$height = $max_width * $height / $width;
		$width  = $max_width;
	}
	if ($max_height
	&&	$height > $max_height
	) {
		$width  = $max_height * $width / $height;
		$height = $max_height;
	}
	if ($make_integer) {
		$width  = (int)round($width);
		$height = (int)round($height);
	}
}

function scale_max_pixels(
	int|float	&$width,
	int|float	&$height,
	int			$max_pixels,
	int 		$max_width 		= 0,
	int			$max_height		= 0,
	bool		$make_integer	= true,
): void {
	$have_pixels = $width * $height;
	if ($have_pixels > $max_pixels) {
		$multiplier = 1 / sqrt($have_pixels / $max_pixels);
		$width  *= $multiplier;
		$height *= $multiplier;
	}
	if ($max_width
	&&	$width > $max_width
	) {
		$new_width = $max_width;
		$height = $new_width * $height / $width;
		$width  = $new_width;
	}
	if ($max_height
	&&	$height > $max_height
	) {
		$new_height = $max_height;
		$width  = $new_height * $width / $height;
		$height = $new_height;
	}
	if ($make_integer) {
		$width  = (int)round($width);
		$height = (int)round($height);
	}
}

function scale_fill(
	int			$width,
	int			$height,
	int|float	&$new_width,
	int|float	&$new_height,
	bool		$make_integer = true
): void {
	if ($new_width && $new_height) {
		return;
	}
	if (!$new_width && !$new_height) {
		$new_width  = $width;
		$new_height = $height;
		return;
	}
	if (!$new_width) {
		$new_width = $width * $new_height / $height;
	}
	if (!$new_height) {
		$new_height = $height * $new_width / $width;
	}
	if ($make_integer) {
		$new_width  = (int)round($new_width);
		$new_height = (int)round($new_height);
	}
}

function _make_illegible(string $txt): string {
	$res = '';
	$length = strlen($txt);
	for ($cnt = 0; $cnt < $length; ++$cnt) {
		$res .= '&#'.ord($txt[$cnt]).';';
	}
	return $res;
}

function show_email_img(?string $link = null, ?string $title = null, ?string $class = null): void {
	require_once '_hosts.inc';

	if ($link) {
		?><a<?
		if ($link[0] === '/') {
			?> target="_blank"<?
		}
		?> href="<?= $link ?>"><?
	}
	?><img<?
	?> class="mail<?
	if ($class) {
		?> <? echo $class;
	}
	?>"<?
	?> src="<?= STATIC_HOST ?>/images/mail<?= is_high_res() ?>.png"<?
	if ($title) {
		?> alt="<?= $title ?>"<?
		?> title="<?= $title ?>"<?
	}
	?>><?
	if ($link) {
		?></a><?
	}
}

const PRINT_EMAIL_BAD			= 0b1;
const PRINT_EMAIL_IMAGE_BEFORE	= 0b10;
const PRINT_EMAIL_IMAGE_AFTER	= 0b100;
const PRINT_EMAIL_TICKET		= 0b1000;
const PRINT_EMAIL_NO_NAME		= 0b10000;
const PRINT_EMAIL_SHOW_EMAIL 	= 0b100000;
const PRINT_EMAIL_HIDE_SEARCH 	= 0b1000000;
const PRINT_EMAIL_NO_POST_LINK	= 0b10000000;

function print_email_link(
	#[SensitiveParameter]
	string				$email,
	string|array|null	$name 	 = null,
	int					$flags	 = 0,
	?string				$element = null,
	?int				$id		 = null,
	?int				$userid  = null,
): void {
	if (!$email && !$name) {
		return;
	}
	if ($email) {
		$email = strtolower($email);
		$mailto  = _make_illegible('mailto:');
		$illmail = _make_illegible($email);
	}
	if (is_array($name)) {
		[$display_name, $email_name] = $name;
	} else {
		$email_name   = $name;
		$display_name = $name;
	}
	# FIXME: Why should UTF-8 always be false for the display name?
	$utf8 = false;
	if (isset($display_name[1])
	&&	$display_name[0] === '='
	&&	$display_name[1] === '?'
	) {
		require_once '_mimeheader.inc';
		$display_name = decode_quoted_printable_header($display_name, $made_utf8);
		$utf8 = $made_utf8;
	}
	require_once '_require.inc';

	$ticket_link =
		have_admin($element)
	&&	($flags & PRINT_EMAIL_TICKET);

	$post_link =
		$ticket_link
	&&	!($flags & PRINT_EMAIL_NO_POST_LINK);

	$internal_email = contains_partyflock_email($email);

	$link = $email
	?	(	$ticket_link
		?	'/ticket/outgoing-form'.(
				$post_link
			?	''
			:	 '?TO_EMAIL='.urlencode($email).
				($name	  ? ';TO_NAME='.urlencode($name)  : '').
				($element ? ';ELEMENT='.$element : '').
				($id	  ? ';ID='.$id		 : '').
				($userid  ? ';TO_USERID='.$userid: '')
			)
		:	$mailto.($email_name ? escape_specials($email_name).' &lt;' : null).$illmail.($email_name ? '&gt;' : null)
		)
	:	null;

	$title = $email ? ' &rarr; '.$illmail : null;

	if ($email
	&& $flags & PRINT_EMAIL_IMAGE_BEFORE
	) {
		show_email_img(null, $title);
		?> <?
	}
	if (!($flags & PRINT_EMAIL_BAD)
	&&	($info = db_single_array(['realemailcheck', 'contact_undeliverable', 'contact_undeliverable_ignore'], "
			SELECT CIMID, STAMP, FAILREASON
			FROM realemailcheck
			LEFT JOIN contact_undeliverable ON EMAIL = FAILMAIL
			LEFT JOIN contact_undeliverable_ignore USING (CIMID)
			WHERE EMAIL = '".addslashes($email)."'
			  AND CONTENT NOT LIKE '%No action is required on your part%'
			  AND FAILS
			  AND contact_undeliverable_ignore.CIMID IS NULL
			ORDER BY CIMID DESC
			LIMIT 1"))
	) {
		[$cimid, $stamp, $reason] = $info;
		if ($cimid !== null) {
			$flags |= PRINT_EMAIL_BAD;
		}
	}
	if ($flags & PRINT_EMAIL_BAD) {
		?><span class="unavailable"><?
	}
	if ($email
	&&	!$internal_email
	) {
		if ($post_link) {
			ob_start();
		} else {
			?><a<?
			if ($link[0] === '/') {
				?> target="_blank"<?
			}
			?> title="<?= $title ?>"<?
			 ?> href="<?= $link ?>"><?
		}
	}

	if (!$display_name
	||	$flags & PRINT_EMAIL_NO_NAME
	) {
		echo $illmail;
	} else {
		echo escape_specials($display_name, $utf8);
	}
	if ($email
	&&	(	$display_name
		&&	$display_name !== $email
		&&	!($flags & PRINT_EMAIL_NO_NAME)
		)
	&& $flags & PRINT_EMAIL_SHOW_EMAIL
	) {
		echo ' <span class="nb">, '.$illmail.'</span>';
	}

	if ($email
	&&	!$internal_email
	) {
		if (!$post_link) {
			?></a><?
		} else {
			post_link(ob_get_clean(),$link,$title,
				"'target',	'_blank',".
				"'TO_EMAIL',	'$illmail'".
				($email_name	? ",'TO_NAME','".escape_specials($email_name)."'" : null).
				($element		? ",'ELEMENT','$element'" : '').
				($id			? ",'ID','$id'" : '').
				($userid		? ",'TO_USERID',$userid" : '')
			);
		}
	}
	if ($flags & PRINT_EMAIL_BAD) {
		?></span><?
		if (!empty($cimid)) {
			?><a class="hlp" title="<? _datetime_display($stamp); echo "\n\n",escape_specials($reason) ?>" href="/contact/undeliverable/<?= $cimid ?>"><sup>?</sup></a><?
		}
	}
	if ($email
	&& $flags & PRINT_EMAIL_IMAGE_AFTER
	) {
		?> <?
		show_email_img(null,$title);
	}
	if (!($flags & PRINT_EMAIL_HIDE_SEARCH)
	&&	have_admin()
	) {
		search_ticket_link($email);
	}
}

function online_ids(): array {
	require_once '_memcache.inc';
	static $__onlines;
	if (is_array($__onlines ??= memcached_get('__onlinez'))) {
		return $__onlines;
	}
	if (!db_getlock('__onlinez', 2)) {
		return $__onlines = [];
	}
	# retry once, because someone might have gottend the online info before us
	if (is_array($__onlines ??= memcached_get('__onlinez'))) {
		mail_log('second try onlinez yield result');
		return $__onlines;
	}
	memcached_set(
		'__onlinez',
		$__onlines = db_boolean_hash('user_data', '
			SELECT USERID
			FROM user_data
			WHERE LAST_USED > '.(CURRENTSTAMP - 300)
		) ?: [],
		5 * ONE_MINUTE
	);
	db_releaselock('__onlinez');
	return $__onlines;
}

function is_online(int|array $userid, ?array $user = null): bool {
	$online_ids = online_ids();
	if (!is_array($userid)) {
		if (!$user) {
			if (!$userid) {
				# very old directmessages where sent by userid 0 in case of admin
				return false;
			}
			$user = memcached_user($userid);
		}
	} elseif (!isset($userid['VISIBILITY_ONLINE'])) {
		$user = memcached_user($userid = $userid['USERID']);
	} else {
		$user   = $userid;
		$userid = $user['USERID'];
	}
	require_once '_visibility.inc';
	return	$user
		&&	_visibility($user, 'ONLINE', have_admin())
		&&	isset($online_ids[$userid]);
}

const JS_EXTERNAL		= 0x2;
const JS_SYNC			= 0x4;
const JS_INCLUDED		= 0x10;
const JS_STATIC_HOST	= 0x40;
const JS_PRELOAD		= 0x80;
const JS_DEFER			= 0x100;

function include_js(
	string|true $file,
	?int		$flags  = null,
	?string 	$base	= null,
	?string 	$arg	= null,
	?string		$onload = null,
): bool {
	$flags ??= 0;
	static $__js, $__late;
	if ($file === true) {
		if ($__late) {
			foreach ($__late as $now_file => [$now_flags, $now_onload, $now_base]) {
				include_js_immediate($now_file, $now_flags, $now_base, onload: $now_onload);
			}
		}
		return false;
	}
	if (isset($__js[$file])) {
		return true;
	}
	if ($flags & JS_INCLUDED) {
		return false;
	}
	if ($file === 'js/choosecity') {
		include_js('js/form/locationtype');
	}
	if (!isset($_SERVER['AJAX'])) {
		if ($flags & JS_PRELOAD) {
			include_head('<link rel="preload" as="script" type="text/javascript" href="'.get_hashed_js($file, $flags, $base, $arg).'" />');
		}
		$__late[$file] = $base ? [$flags, $onload, $base] : [$flags, $onload, null];
	}
	$__js[$file] = [0, $onload, null];
	return true;
}

function include_js_immediate(
	string	$file,
	int		$flags	= 0,
	?string $base	= null,
	?string $arg	= null,
	?string $onload	= null,
): void {
	require_once '_spider.inc';
	require_once '_servertype.inc';

	switch ($base ?: $file) {
	case 'js/feedevent':
	case 'js/geo':
	case 'js/hiliting':
	case 'js/hovercheck':
	case 'js/infomessage':					# needed because ready event might otherwise fire before showInfoMessage is available
	case 'js/iptable':						# needed by js/lazyload readyevent
	case 'js/nowsoon':						# getNowSoon needed by addreadyevent
	case 'js/main':
	case 'js/markerclusterer_compiled':
	case 'js/photoload':
	case 'js/textarea-resizer':
		$flags |= JS_SYNC;
		break;
	}
	?><script<?
	if (!($flags & JS_SYNC)) {
		?> async<?
	}
	if ($flags & JS_DEFER) {
		?> defer<?
	}
	if ($onload) {
		?> onload="<?= $onload ?>"<?
	}
	?> src="<?= get_hashed_js($file, $flags, $base, $arg) ?>"></script><?
}
function include_google_maps_js(
	string  $element,
	int	 	$id			= 0,
	string  $type		= '',
	?string	$callback	= null,
	bool	$sensor		= false,
): void {
	require_once '_browser.inc';
	require_once '_spider.inc';
	require_once 'defines/maps.inc';
	if (!ROBOT) {
		db_insert('gmapshitv2',"
		INSERT INTO gmapshitv2 SET
			ELEMENT		= '$element',
			TYPE		= '$type',
			ID			= $id,
/*			BROWSERID   = '.(get_browserid() ?: 0).',*/
			ROBOT		= 0,
			IDENTID		= ".CURRENTIDENTID.",
			IPBIN		= '".addslashes(CURRENTIPBIN)."',
			USERID		= ".CURRENTUSERID.',
			STAMP		= '.CURRENTSTAMP
		);
	}
	include_js(
		'https://maps.googleapis.com/maps/api/js'.
			'?language='.strtolower(CURRENTLANGUAGE).
			'&key='.urlencode(GOOGLE_MAPS_API_KEY).
			($callback	? "&callback=$callback" : '').
			($sensor	? '&sensor=true'		: ''),
		JS_EXTERNAL
	);
}

function get_hashed_js(
	string	$file,
	int 	$flags = 0,
	?string $base  = null,
	?string $arg   = null,
): string {
	require_once '_hashes.inc';
	require_once '_hosts.inc';
	require_once '_spider.inc';
	require_once '_servertype.inc';
	$js_is_php = match ($base ?: $file) {
		'js/main',
		'js/commission',
		'js/contact',
		'js/quote',
		'js/form/albummapforelement',
		'js/form/growtofit',
		'js/form/upload',
		'js/form/validation'	=> true,
		default					=> false,
	};
	ob_start();
	if ($flags & JS_EXTERNAL) {
		echo escape_specials($file);
	} else {
		if ($flags & JS_STATIC_HOST) {
			echo STATIC_HOST;
		}
		?>/<?= $file ?>_<?
		require_once '_hashes.inc';
		echo get_hash(
			($base ?: $file).
			($js_is_php ? '.js.php' : '.js')
		);
		?>.js<?
	}
	if ($arg) {
		?>?<? echo $arg;

	}
	return ob_get_clean();
}

const STYLE_FOR_LITE	= 0b10;

function include_style(
	string|true $file,
	?string		$media = null,
	int			$flags = 0
): void {
	require_once '_hosts.inc';
	if (defined('LITE')
	&&	LITE
	&&	(	$file === 'forlite'
		||	$flags & STYLE_FOR_LITE
		)
	) {
		return;
	}
	static $__styles;

	$media ??= 'all';

	if ($file === true) {
		if ($__styles) {
			foreach ($__styles as $medias) {
				foreach ($medias as $now_media => $full_file) {
					?><link<?
					?> rel="stylesheet"<?
					?> media="<?= $now_media ?: 'all' ?>"<?
					?> href="<?= $full_file ?>" /><?

					?><link<?
					?> rel="preload"<?
					?> as="style"<?
					?> href="<?= $full_file ?>" /><?
				}
			}
		}
		return;
	}
	if (isset($__styles[$file][$media])) {
		return;
	}
	require_once '_style.inc';
	ob_start();
	show_stylesheet(
		$file,
		overrule: $flags & STYLE_FOR_LITE ? ['FORLITE' => true] : []
	);
	$__styles[$file][$media] = ob_get_clean();
}

# FIXME: This function is used only once and does not seem very UTF-8 friendly.
function make_search_regexp(string $subject, string $terms): array {
	if (false === ($terms_list = preg_split('"(\s+)"', $terms, -1, str_contains($terms, '"') ? PREG_SPLIT_DELIM_CAPTURE : 0))) {
		# Let query result nothing:
		return ['0'];
	}
	$result = [];
	$ingroup = false;
	foreach ($terms_list as $term) {
		if (!$ingroup) {
			$term = mytrim($term);
			if (!$term) {
				continue;
			}
			if ($term[0] === '-') {
				$neg = ' NOT';
				$term = substr($term, 1);
			} else  {
				$neg = null;
			}
			if ($term[0] === '"') {
				$ingroup = true;
				$grouped_term = substr($term, 1);
				continue;
			}
		} elseif ($term[strlen($term)-1] === '"') {
			$ingroup = false;
			$term = $grouped_term.substr($term,0,-1);
		} else {
			if (!mytrim($term)) {
				// for people typing too many spaces
				$grouped_term .= ' ';
			} else {
				$grouped_term .= $term;
			}
			continue;
		}
		$result[] = $subject.$neg.' LIKE "%'.addslashes(db_quote_wild($term)).'%"';
	}
	return $result;
}

# FIX ME: unuse
function prolly_latin1(string $str): bool {
	return 'UTF-8' !== mb_detect_encoding($str, 'windows-1252, UTF-8');
}

function escape_utf8_to_win1252(string $str): string {
	return escape_specials(utf8_to_win1252($str));
}

function escape_utf8(string $str, bool $quiet = false): string {
	return escape_specials($str, true, $quiet);
}

function escape_specials(?string $str, bool $utf8 = false, bool $quiet = false): ?string {
	if (!$str) {
		return $str;
	}
	$result = htmlentities($str, ENT_QUOTES | ENT_SUBSTITUTE | ENT_HTML401, $utf8 ? 'UTF-8' : 'windows-1252');
	if (!$quiet
	&&	(	str_contains($result, REPLACEMENT_CHARACTER)
		||	str_contains($result, REPLACEMENT_CHARACTER_ENTITY))
	) {
		mail_log(
			'htmlspecialchars: bad code unit(s) converting '.
			($utf8 ? 'utf8' : 'win1252').' string special characters', $str
		);
	}
	if ($utf8) {
		return mb_encode_numericentity($result, [0x80, 0xffffff, 0, 0xffffff], 'UTF-8');
	}
	return $result;
}

function escape_ascii(?string $str): ?string {
	# input is valid ascii, don't care about encoding as we don't go above ascii values
	return escape_specials($str);
}

function user_exists(int $userid): bool {
	static $__user_exists;
	if (isset($__user_exists[$userid])) {
		return $__user_exists[$userid];
	}
	global $memcache;
	if ($memcache) {
		return $__user_exists[$userid] = (bool)memcached_user($userid);
	}
	return $__user_exists[$userid] = (bool)db_single('user_account', 'SELECT USERID FROM user_account WHERE USERID = '.$userid);
}

function int_explode(
	string	$separator,
	string	$string,
	int		$limit = PHP_INT_MAX
): array {
	return array_map(\intval(...), explode($separator, $string, $limit));
}

function explode_to_hash(
	string 	$separator,
	string	$string,
	int		$limit	  = PHP_INT_MAX,
	?string	$set_type = null,
): array {
	if (!$string) {
		return [];
	}
	foreach (explode($separator, $string, $limit) as $value) {
		if ($set_type) {
			settype($value, $set_type);
		}
		$result[$value] = $value;
	}
	return $result ?? [];
}

function bad_arguments(?string $element = null, ?int $id = null): void {
	page_problem(400, $element, $id);
}
// function no_authorization(?string $element = null, ?int $id = null): void {
// 	page_problem(401, $element, $id);
// }
function no_permission(?string $element = null, ?int $id = null): void {
	page_problem(403, $element, $id);
}
function now_gone(?string $element = null, ?int $id = null): void {
	page_problem(410, $element, $id);
}
// function precondition_failed(?string $element = null, ?int $id = null): void {
// 	page_problem(412, $element, $id);
// }
function not_found(?string $element = null, ?int $id = null): void {
	if ($element
	&&	$id
	&&	empty($GLOBALS['__error'])
	) {
		register_nonexistent($element, $id);
	}
	page_problem(404, $element, $id);
}

function conflicted(?string $element = null, ?int $id = null): void {
	page_problem(409, $element, $id);
}

function not_available_yet(?string $element = null, ?int $id = null): void {
	conflicted($element, $id);
}

function server_failure(?string $element = null, ?int $id = null): void {
	page_problem(503, $element, $id);
}

function page_problem(
	int	 	$status,
	?string $element = null,
	?int	$id	 	 = null,
	?string $desc	=  null
): void {
	$element ??= $_REQUEST['sELEMENT'];
	$id		 ??= $_REQUEST['sID'];

	static $__done;
	if (isset($__done[$element][$id])) {
		return;
	}
	$__done[$element][$id] = true;

	$GLOBALS['page_problem'] =
		$element
	&&	$id
	&&	(	!(require_once '_urltitle.inc')
		||	!isset(USE_URLTITLE[$element])
		||	!get_element_title($element, $id)
		)
	?	[$status, $element, $id, $desc]
	:	[$status, null,	 null, $desc];

	http_response_code($status);
}

function have_page_problem(): bool {
	return !empty($GLOBALS['page_problem']);
}

function nlnl2cmbr(string $text, bool $use_xhtml = true): string {
	$br = $use_xhtml ? '<br />' : '<br>';
	if (null === ($new_text = preg_replace_callback(
		"'\r\n\r\n|\\\\r\\\\n\\\\r\\\\n|\n\n|\\\\n\\\\n|\\n'",
		fn (array $match): string => $match[0] === '\n' ? $br : '<div class="cmbr"> </div>',
		$text))
	) {
		preg_failure($text);
	} else {
		assert(is_string($new_text)); # Satisfy EA inspection
		$text = $new_text;
	}
	return nl2br($text, use_xhtml: $use_xhtml);
}

function separate_thousands(?int $number = null, ?int $decimals = null): string {
	if (!$number
	||	!($info = get_locale_info())
	) {
		return (string)$number;
	}
	$info['thousands_sep'] = ' ';
	$decimals ??= 0;
	return str_replace(' ','&thinsp;',
		number_format(
			$number, $decimals,
			$info['decimal_point'],
			$info['thousands_sep']
		)
	);
}

function show_distance_to_item(array $item, ?string $prefix = null): void {
	if (empty($item['LATITUDE'])) {
		if (empty($item['LAT'])) {
			return;
		}
		$item['LATITUDE']  = $item['LAT'];
		$item['LONGITUDE'] = $item['LON'];
	}
	require_once '_geo.inc';
	if (!($geo = get_geolocation_cache())) {
		return;
	}
	[$latitude, $longitude] = $geo;

	echo $prefix;

	$distance = calculate_distance($item['LATITUDE'], $item['LONGITUDE'], $latitude, $longitude);

	?><small class="light nowrap">(<?
	if ($distance >= 100) {
		echo round($distance);
		?> <?
		echo __('abbr:kilometer');
	} elseif ($distance >= 10) {
		echo round($distance, 1);
		?> <?
		echo __('abbr:kilometer');
	} else {
		echo round(1000 * $distance);
		?> <?
		echo __('abbr:meter');
	}
	?>)</small><?
}

function calculate_distance(
	float $lat1,
	float $lon1,
	float $lat2,
	float $lon2
): float {
	if ($lat1 === $lat2
	&&	$lon1 === $lon2
	) {
		return 0.0;
	}
	$lat1 = deg2rad($lat1);
	$lat2 = deg2rad($lat2);
	return 6371 * acos(
			sin($lat1)
		*	sin($lat2)
		+	cos($lat1)
		*	cos($lat2)
		*	cos( deg2rad($lon1 - $lon2) )
		);
}

function distance(
	string|int|float $lat1,
	string|int|float $lon1,
	string|int|float $lat2,
	string|int|float $lon2,
): string {
	# get distance in km
	# https://www.meridianworlddata.com/Distance-Calculation.asp
	return "
		COALESCE(
			6371 * ACOS(
				SIN(RADIANS($lat1))
				*	SIN(RADIANS($lat2))
				+	COS(RADIANS($lat1))
				*	COS(RADIANS($lat2))
				*	COS(RADIANS($lon1 - $lon2))
			), 0
		)";
}

function http_status(int $status, ?int $length = null): void {
	if ($status === 500) {
		# Google doesn't downgrade ranking as fast with 500
		$status = 503;
	}
	if ($length !== null) {
		header('Content-Length: '.$length);
	}
	http_response_code($status);
}

function have_meta(string $field): ?string {
	return include_meta($field);
}

function include_meta(?string $name = null, ?string $content = null, bool $include_og_description = true): ?string {
	static $__metas = [];
	if (!$name) {
		foreach ($__metas as $stored_name => $stored_content) {
			?><meta name="<?= $stored_name ?>" content="<?= $stored_content ?>" /><?
		}
		$__metas = [];
		return null;
	}
	if (!$content) {
		# test whether field is already set
		return $__metas[$name] ?? null;
	}
	if (isset($__metas[$name])) {
		#mail_log("duplicate meta setting: '$name' = '$content', stored: '$__metas[$name]'");
		error_log("duplicate meta setting: '$name' = '$content', stored: '$__metas[$name]'");
		return null;
	}
	if ($include_og_description
	&& $name === 'description'
	) {
		include_og('og:description', $content);
	}
	$__metas[$name] = $content;
	return null;
}

function have_og(string $field): ?string {
	return include_og($field);
}

function include_og(?string $property = null, ?string $content = null): ?bool {
	static $__ogs = [], $__have;
	if (!$property) {
		foreach ($__ogs as [$stored_property, $stored_content]) {
			?><meta property="<?= $stored_property ?>" content="<?= $stored_content ?>" /><?
		}
		$__ogs = [];
		return null;
	}
	if (!$content) {
		# no content to store, then test if the property is stored
		return isset($__have[$property]);
	}
	if (isset($__have[$property])
	&&	!str_starts_with($property, 'og:image')
	&&	!str_starts_with($property, 'twitter:image')
	&&	!str_starts_with($property, 'og:locale:alternate')
	) {
		mail_log("duplicate og setting: '$property' = '$content'", get_defined_vars());
		return null;
	}
	switch ($property) {
	case 'og:title':
		include_og('twitter:title', $content);
		break;
	case 'og:image':
		include_og('twitter:image', $content);
		break;
	case 'og:description':
		include_og('twitter:description', $content);
		include_meta('description', $content, false);
		break;
	}
	$__ogs[] = [$property, $content];
	$__have[$property] = true;
	return null;
}

function robot_action(?string $arg = null): void {
	static $__actions;
	if ($arg) {
		$__actions[$arg] = $arg;
		return;
	}
	if (empty($__actions)) {
		return;
	}
	$actions_str = implodekeys(',', $__actions);
	if (headers_sent()) {
		mail_log('headers already sent');
	}
	header('X-Robots-Tag: '.$actions_str);
	include_head('<meta name="robots" content="'.$actions_str.'" />');
}

function include_head(?string $head = null): void {
	static $__heads = '';
	if ($head) {
		$__heads .= $head;
		return;
	}
	echo $__heads;
}

function show_simple_diff(int $stamp, ?int $reference = null): void {
	if (($diff = ($reference ?? CURRENTSTAMP) - $stamp) < ONE_DAY) {
		echo __('date:today');
		return;
	}
	if ($diff < 2 * ONE_DAY) {
		echo __('date:yesterday');
		return;
	}
	if (($diff_days = round($diff / ONE_DAY, 1)) < 31) {
		echo number_format($diff_days, (float)(int)$diff_days === $diff_days ? 0 : 1), ' ', __('date:days_ago');
		return;
	}
	if (($diff_months = round($diff / ONE_MONTH, 1)) <= 12) {
		echo number_format($diff_months, (float)(int)$diff_months === $diff_months ? 0 : 1), ' ', __('date:months_ago');
		return;
	}
	$diff_years = round($diff / ONE_YEAR, 1);
	echo number_format($diff_years, (float)(int)$diff_years === $diff_years ? 0 : 1), ' ', __('date:years_ago');
}

function show_short_diff(int $stamp): void {
	assert(is_int(TODAYSTAMP)); # Satisfy type checker
	if ($stamp >= TODAYSTAMP) {
		_time_display($stamp);
		return;
	}
	global $__year;
	[$y, $m, $d] = _getdate($stamp);
	if ($__year !== $y) {
		printf('%04d-%02d-%02d', $y, $m, $d);
		if (!SMALL_SCREEN) {
			?>&nbsp;<?
			_time_display($stamp);
		}
		return;
	}
	// message is before 00:00 today morning
	$days = ceil((TODAYSTAMP - $stamp) / ONE_DAY);
	if ($days > 365.25) {
		$years = floor($days / ONE_YEAR_DAYS);
		$days -= ceil($years * ONE_YEAR_DAYS);
		echo $years, __('diff:y(ear)');
	}
	echo $days,__('diff:d(ay)'),'&nbsp;';
	_time_display($stamp);
}

function get_hidden_orgs(?int $id = null): array|int|bool {
	static $__hidden_orgs = null;
	if (!($__hidden_orgs ??= memcached_simple_hash('hidden_orgs', 'SELECT ID, DSTID FROM party_db.hidden_orgs'))) {
		return false;
	}
	if ($id !== null) {
		return $__hidden_orgs[$id] ?? false;
	}
	return $__hidden_orgs;
}

function get_duration(array|int|float $open_time): string {
	if (is_array($open_time)) {
		$poll      = $open_time;
		$open_time = $poll['CLOSESTAMP'] ? $poll['OPENDURATION'] : CURRENTSTAMP - $poll['OPENSTAMP'] + $poll['OPENDURATION'];
	}
	if ($open_time < ONE_MINUTE) {
		// seconds
		return __('duration:x_seconds', ['SECONDS' => round($open_time)]);
	}
	if ($open_time < ONE_HOUR) {
		// minutes
		$exact		= $open_time % ONE_MINUTE === 0;
		$open_time /= ONE_MINUTE;
		/** @noinspection ReturnTernaryReplacementInspection */
		return $exact
			?	__('duration:x_minutes',  ['MINUTES' => round($open_time)])
			:	__('duration:x.x_minute', ['MINUTE'  => sprintf('%.1f', $open_time)]);

	}
	if ($open_time < ONE_DAY) {
		// hours
		$exact		= $open_time % ONE_HOUR === 0;
		$open_time /= ONE_HOUR;
		/** @noinspection ReturnTernaryReplacementInspection */
		return $exact
			?	__('duration:x_hours',  ['HOURS' => round($open_time)])
			:	__('duration:x.x_hour', ['HOUR'  => sprintf('%.1f', $open_time)]);
	}
	// days
	$exact		= $open_time % ONE_DAY === 0;
	$open_time /= ONE_DAY;
	/** @noinspection ReturnTernaryReplacementInspection */
	return $exact
		?	__('duration:x_days',  ['DAYS' => round($open_time)])
		:	__('duration:x.x_day', ['DAY'  => sprintf('%.1f', $open_time)]);
}

function getifset(
	stdClass|array|attrib_spec|null	$arr	= null,
	string|int|null					$key	= null,
	string|int|null					$key2	= null,
): mixed {
	if (!$arr) {
		return null;
	}
	if (is_object($arr)) {
		if (property_exists($arr, $key)) {
			if ($key2) {
				return $arr->$key?->$key2 ?? null;
			}
			return $arr->$key;
		}
		return null;
	}
	if (isset($arr[$key])) {
		if ($key2 !== null) {
			return $arr[$key][$key2] ?? null;
		}
		return $arr[$key];
	}
	return null;
}

function increment_or_set(array &$array, string|int $key, int|float $add = 1): void {
	if (isset($array[$key])) {
		$array[$key] += $add;
	} else {
		$array[$key] = $add;
	}
}

function link_to_comments(string $element, int $id): string {
	require_once '_comment.inc';
	$uncollapsed = setting('UNCOLLAPSE_COMMENTS') & (1 << constant('CMT_'.strtoupper($element)));
	$is_promo = ($element === 'promo');
	return	(	$uncollapsed
			?	($is_promo ? "/promo/$id/letter" : get_element_href($element, $id))
			:	(isset($_REQUEST['ABSPATH']) ? 'https://'.$_SERVER['HTTP_HOST'] : '')."/$element/$id/comments"
			)
			.(setting('COMMENT_PAGE_ORDER') === 'chrono' ? '#cmts' : '#bottom');
}

function get_current_email(): string|false|null {
	return memcached_single('user', 'SELECT EMAIL FROM user WHERE USERID = '.CURRENTUSERID);
}

function fix_user_personalia(array &$user): void {
	if (!empty($user['ZIPCODE'])
	&&	!empty($user['COUNTRYID'])
	) {
		switch ($user['COUNTRYID']) {
		case 1:	if (preg_match('"^\s*(\d{4})\s*([a-zA-Z]{2})\s+"', $user['ZIPCODE'], $match)) {
				$user['ZIPCODE'] = $match[1].' '.strtoupper($match[2]);
			}
			break;
		case 6: if (preg_match('"^\s*(\d{4})"', $user['ZIPCODE'], $match)) {
				$user['ZIPCODE'] = $match[1];
			}
			break;
		}
	}
	foreach (['ADDRESS', 'REALNAME', 'NAME'] as $key) {
		if (empty($user[$key])) {
			continue;
		}
		$parts = [];
		foreach (preg_split('"\s+"', $user[$key]) as $part) {
			$parts[] =
				preg_match('"^(der?|van|in|v\.?d\.?)$"i',$part,$match)
			?	strtolower($match[1])
			:	ucfirst($part);
		}
		$user[$key] = ucfirst(implode(' ',$parts));
	}
}

function strip_white_space(string $text, bool $utf8 = false): string {
	require_once '_unicode.inc';
	# Actually doesn't just strip just white space. It also strips any invsible separator
	return preg_replace('!'.MATCH_ANY_SPACE.'+!'.($utf8 ? 'u' : ''), '', $text) ?? $text;
}

function utf8_strip_shy(string $text): string {
	return strip_shy($text, true);
}

function strip_shy(string $text, bool $utf8 = false): string {
	require_once '_unicode.inc';
	return str_replace($utf8 ? SOFT_HYPHEN : SOFT_HYPHEN_LATIN1, '', $text);
}

function combine_whitespace(string $text): string {
	if (null === ($new_text = preg_replace('"\s+"', ' ', $text))) {
		preg_failure($text);
	} else {
		$text = $new_text;
	}
	return $text;
}

/*function find_id(
	string $table,
	string $idname,
	string $key,
	int	$val,
	string $force_server = null
): int|null|false {
	# find an ID using an index and an other field, both numerically ordered low -> hi
	# inclusive! and NO holes support yet

#	error_log('find: '.$val);
	$db_flags = $force_server ? DB_FORCE_SERVER : 0;
	$db_arg   = $force_server;

	$minmax = db_single_array($table, '
		SELECT MIN('.$idname.'), MAX('.$idname.')
		FROM '.$table,
		$db_flags,
		$db_arg
	);
	if (!$minmax) {
		return $minmax === false ? false : null;
	}
	[$min, $max] = $minmax;

	$find_internal = function(int $min, int $max) use ($table, $idname, $key, $val, $db_flags, $db_arg, &$find_internal): int|false {
		$diff = ($max - $min) >> 1;
		if (!$diff) {
			return db_single($table, '
				SELECT '.$idname.'
				FROM '.$table.'
				WHERE '.$idname.' <= '.$max.' AND '.$key.'<='.$val.'
				ORDER BY '.$idname.'
				DESC LIMIT 1',
				$db_flags,
				$db_arg
			);
		}
		$pivot = $min + $diff;

#		error_log('min '.$min.', max '.$max.', diff '.$diff.', pivot '.$pivot,0);

		$minmaxval = db_simpler_array($table, '
			SELECT '.$key.'
			FROM '.$table.'
			WHERE '.$idname.' IN ('.$min.', '.$pivot.', '.$max.')
			ORDER BY '.$idname,
			$db_flags,
			$db_arg
		);
		if (!$minmaxval) {
			return false;
		}
		[$minval, $pivotval, $maxval] = $minmaxval;
		return	$val <= $pivotval
		?	$find_internal($min, $pivot)
		:	$find_internal($pivot, $max);
	};
	return $find_internal($min, $max);
}*/

function make_clean_realname(string $real_name, bool $utf8 = false): string {
	$name_parts = [];
	foreach (preg_split('"[\s\-\']"'.($utf8 ? 'u' : ''), $real_name) as $name_part) {
		$name_parts[] = match ($name_part) {
			'van',
			'der',
			'den',
			'v/d',
			'de',
			'in',
			'het',
			'd\'',
			'l\''	=> ($utf8 ? 'mb_strtolower' : 'strtolower')($name_part),
			default	=> my_ucfirst($name_part, $utf8),
		};
	}
	return implode(' ', $name_parts);
}

function beautify_text(string $text): string {
	if (null === ($new_text = preg_replace(
		['"([!.?])[!.?]+\s*"u', '"\n\s*[\n\s*]+"u'],
		['$1 ', "\n\n"],
		$text))
	) {
		preg_failure($text);
	} else {
		$text = $new_text;
	}
	return $text;
}

function minify_js(string $data): string {
	return $data;

	# NOTE: JSMinPlus does not support newer javascript constructs

/*	require_once '_memcache.inc';
	$minified_key = 'js:v2:'.hash('xxh128', $data);

	if (!isset($_REQUEST['NOMEMCACHE'])
	&&	($minified = memcached_get($minified_key))
	) {
		return $minified;
	}
	require_once 'JSMinPlus.php';
	if (!($minified = JSMinPlus::minify($data))) {
		$minified = $data;
	}
	memcached_set($minified_key, $minified, TEN_MINUTES);
	return $minified;*/
}

function search_ticket_link(string|int $arg): void {
	if (is_string($arg)
	&&	contains_partyflock_email($arg)
	) {
		return;
	}
	if (!memcached_single('contact_ticket_message',
		is_int($arg)
	?	'	SELECT 1
			FROM contact_ticket
			WHERE WITHUSERID = '.$arg.'
			LIMIT 1'
	:	'	SELECT (
				SELECT 1
				FROM contact_ticket_mail
				WHERE FROM_EMAIL = "'.addslashes($arg).'"
				LIMIT 1
			) OR (	SELECT 1
				FROM contact_ticket_mail
				WHERE TO_EMAIL = "'.addslashes($arg).'"
				LIMIT 1
			)')
	) {
		return;
	}
	?> <?
	if (is_int($arg)) {
		?><a target="_blank" href="/contact/search?TYPE=withuser&amp;SRCH=<?= $arg ?>"><?=
		get_special_char('search')
		?></a><?
	} else {
		post_link(
			get_special_char('search'),
			'/contact/search',
			extra: "'target','_blank','TYPE','withuser','SRCH','".escape_specials($arg)."'"
		);
	}
}

function subtitle_no_middot(string $subtitle): bool {
	return (bool)preg_match('"^#?(?:\d+|[IVX]+|\+.*)$"u', $subtitle);
}

function detect_utf8_bom(string $text): bool {
	return	isset($text[0], $text[1], $text[2])
	&&	$text[0] === "\xEF"
	&&	$text[1] === "\xBB"
	&&	$text[2] === "\xBF";
}

function detect_utf8(array|string|null $arg = null): bool {
	if (!$arg) {
		return false;
	}
	if (!is_string($arg)) {
		if (!isset($arg['BODY'])) {
			return false;
		}
		$arg = &$arg['BODY'];
	}
	return detect_utf8_bom($arg);
}

function rem_utf8_bom(array|string &$arg): void {
	if (!is_string($arg)) {
		if (!isset($arg['BODY'])) {
			return;
		}
		$arg = &$arg['BODY'];
	}
	if (detect_utf8_bom($arg)) {
		$arg = substr($arg, 3);
	}
}

//function add_utf8_bom(array|string &$arg): void {
//	if (!is_string($arg)) {
//		if (!isset($arg['BODY'])) {
//			return;
//		}
//		$arg = &$arg['BODY'];
//	}
//	if (!detect_utf8_bom($arg)) {
//		$arg = "\xEF\xBB\xBF".$arg;
//	}
//}

function clean_title(string $title, bool $utf8 = false): string {
	return str_replace([' ','-'], '', ($utf8 ? 'mb_strtolower' : 'strtolower')($title));
}

function clean_zip(string $zip, bool $utf8 = false): string {
	if (preg_match('"^\h*(\d{4})\h*([a-zA-Z]{2})\h*$"'.($utf8 ? 'u' : ''), $zip, $match)) {
		return $match[1].' '.($utf8 ? mb_strtoupper($match[2]) : strtoupper($match[2]));
	}
	return $zip;
}

function invisible_profile(int $userid, ?string $allow_admin = null): bool {
	require_once '_require.inc';
	require_once '_visibility.inc';
	if (!($user = memcached_user($userid))) {
		return true;
	}
	if (have_admin(['user', 'helpdesk'])
	||	$allow_admin
	&&	have_admin($allow_admin)
	) {
		return false;
	}
	if (in_array($user['STATUS'], ['deleted', 'permbanned', 'unactive'], true)) {
		return true;
	}
	return !is_visible($userid, $user['VISIBILITY_PROFILE'] ?? EVERYBODY);
}

function utf8_parse_event_title(string $title, ?array $args = null): array {
	return parse_event_title(transliterate_irregular_letters_to_regular($title), $args, utf8: true);
}

function RE_OFFICIAL_EVENT(?array $orgids = null): string {
	$re_orgs = null;
	if ($orgids) {
		foreach ($orgids as $orgid => &$name) {
			$name = str_replace(' ', '\h*', preg_quote(get_element_title('organization',$orgid), '"'));
		}
		unset($name);
		$re_orgs = '\h*(?:(?:(?:'.implode('|',$orgids).')[\h&]*)+|'."\x1F\d+\x1F".')?';
	}
	return '(?:officieel|official|offizielle)'.$re_orgs.'(?:\h*(?:event|evenement))?';
}

const RE_AND_MANY_MORE		= 'and\h*many\h*more';
const RE_TITLE_BRACKETS		= '(?:[\[\]\(\)\*\h\|\-\/\!]+|\h+[^\w\d]\h+)';
const RE_TITLE_BRACKETS_MAY	= '(?:[\[\]\(\)\*\h\|\-\/\!]+|\h+[^\w\d]\h+)?';	# closing brackets are often not required, and MAY be there

/**
 * @param array{
 *        partyid: int,
 *        locationid: int,
 *        location: string,
 *        organizationids: array<int>
 * }|null $args
 */
function parse_event_title(string $title, ?array $args = null, bool $utf8 = false): array {
	/** @noinspection PhpRedundantOptionalArgumentInspection */
	$args && extract($args, \EXTR_OVERWRITE);

	$edition = 0;

	$name = mytrim($title, utf8: $utf8);

	$subtitle = '';

	$params = $utf8 ? 'ui' : 'i';

	$strip_chars = '!.,*%&^@-:;|/';

	$item_separator = "\x1F";

	# NOTE:	Allow exact organizations or location name in title, where otherwise things might get stripped.
	#		A location named WAS., with a full stop at the end, would get events there that are named WAS., with the full stop too,
	#		to lose it full stop. Code below makes sure the full stop is kept as it is exactly the same as the full location name.
	$keeps = [];
	if (!empty($organizationids)) {
		$keeps[] = ['organization', $organizationids];
	}
	if (!empty($locationid)) {
		$keeps[] = ['location', [$locationid => null]];
	}
	if ($keeps) {
		# allow exact organization and location matches (keep uppercase & special chars)
		require_once '_urltitle.inc';
		foreach ($keeps as [$element, $ids_to_name]) {
			foreach ($ids_to_name as $id => $name) {
				$name = get_element_title($element, $id);
				$repcnt = 0;
				if (null === ($new_name = preg_replace(
					'"'.preg_quote($name,'"').'"'.$params,
					$item_separator.$element.':'.$id.$item_separator,
					$name,
					-1,
					$repcnt))
				) {
					preg_failure($name);
				} elseif ($repcnt) {
					$replacements[$element][$id] = $name;
					$name = $new_name;
				}
			}
		}
	}

	$name = get_clean_title_from_facebook($name, $args);

	require_once '_date.inc';

	$month_names = implode('|', get_month_names());
	$day_names   = implode('|', get_day_names());

	$anystrips = [
		'\b(?:'.$day_names.')\h*(?:'.$month_names.')\h*\d+\b',
	];

	$stripped = true;
	while ($stripped) {
		$stripped = false;
		foreach ($anystrips as /* $ndx => */ $strip) {
			if (preg_match('"^(.*?)'.$strip.'(.*?)$"'.$params, $name, $match)) {
				$new_name = $match[1].$match[2];
				$new_name = mytrim($new_name, $strip_chars, $utf8);
				if (!$new_name) {
					continue;
				}
				$name = $new_name;
				$stripped = true;
			}
		}
	}

	$trailstrips = [
		'\bw/\h*.*',
		'(?:'.RE_TITLE_BRACKETS.'(?:free(?:\h*entrance)?|gratis(?:\h*entree)?)'.RE_TITLE_BRACKETS_MAY.')+',
		RE_TITLE_BRACKETS.'(?<!goes to )\bADE(?:\h*special)?'.RE_TITLE_BRACKETS_MAY,
		'(\d+\h*(?:th|st|nd)?\h*(?:ye?a?rs?)?\h*anniversary)\h*',
		'(\d+\h*jarig\h*bestaan)\h*',
		'\h*every\h*(?:monday|tuesday|wednesday|thursday|friday|saturday|sunday)\h*',
		'\h*(?:elke|iedere)\h*(?:maandag|dinsdag|woensdag|donderdag|vrijdag|zaterdag|zondag)\h*',
		$gl = '(?:'.RE_TITLE_BRACKETS.'(?:aanwezig\h*(?:=|is)\h*(?:gastenlijst|guestlist))'.RE_TITLE_BRACKETS_MAY.')+',
		'(?:\d+[\h\-]*(?:'.$day_names.')?\h*(?:'.$month_names.'))\h*20[12]\d',
		'\h+\d+\-\d+\-20[12]\d',
		'(?<!\hof|goodbye|happy|hello|welcome(?:\h*to)?|year)\h+20[12]\d',
		'\h+\(\d+:\d+\h*(?:\-|t/?m)\h*\d+:\d+\)',
		# NOTE: Shouldn't the \d+ group below be non-capturing?
		'\b(\d+)\h*(?:st|th|nd)\h*of\h*(?:'.$month_names.')\h*',
		RE_TITLE_BRACKETS.'(?:20[12]\d[\h\-]*)?(?:'.$day_names.')?\h*(?:\d+\h*(?:'.$month_names.'))'.RE_TITLE_BRACKETS_MAY,
		'\h+\d{1,2}[\.\-]\d{1,2}[\.\-]20[12]\d',
		'\h+'.RE_TITLE_BRACKETS.'(?:cancell?ed|geann?uleerd)'.RE_TITLE_BRACKETS_MAY,
		'\h+dag\h*\d+',
		'\h+pres(?:ents?:?)?[:.\h]+(.*)',
		'\h+x\h+(?:(?:\w+)\h*(?:editie|edition))',
		'\h+xl+\h*',
	];
	$leadstrips = [
		$gl,
		'\h*[xl]\h+',
	];
	$anystrips = [
		RE_TITLE_BRACKETS.'\h*(?:'.$month_names.')\h+\d+\h*'.RE_TITLE_BRACKETS_MAY,
	];

	if (!empty($locationid)
	&&	($location_info = db_single_assoc(['location', 'city'], "
		SELECT location.NAME, TITLE, ORDERNAME, city.NAME AS CITY_NAME
		FROM location
		LEFT JOIN city USING (CITYID)
		WHERE LOCATIONID = $locationid"))
	&&	!empty($location_info['CITY_NAME'])
	) {
		# remove trailing city
		$city_name = $location_info['CITY_NAME'];
		if (!$utf8) {
			$city_name = utf8_to_win1252($city_name);
		}
		$city_name     = preg_quote($city_name, '"');
		$trailstrips[] = '\h+(?:at|in|@)?\h+'.$city_name;
	}

	if (!empty($location_info)) {
		foreach (['TITLE','NAME','ORDERNAME'] as $key) {
			$loc_name = $location_info[$key];
			if (!$utf8) {
				$loc_name = utf8_to_win1252($loc_name);
			}
			$loc_parts[] = preg_quote($loc_name,'"');
		}
	}
	if (!empty($location)) {
		$loc_parts[] = preg_quote($location,'"');
	}
	if (!empty($loc_parts)) {
		$trailstrips[] = '\h+(?:at|in|[x\-\|l/]+|[^\w\d]|\x{00D7})\h+(?:'.implode('|',$loc_parts).')';
	}
	$stripped = true;
	while ($stripped) {
		$stripped = false;
		foreach ($trailstrips as /* $ndx => */ $strip) {
			if (preg_match('"^(.*?)'.$strip.'$"'.$params,$name,$match)) {
				if (!($new_name = mytrim($match[1], $strip_chars, $utf8))) {
					continue;
				}
				$name = $new_name;
				if (!empty($match[2])) {
					$match[2] = mytrim($match[2],$strip_chars,$utf8);
					if (!empty($match[2])) {
						$subtitle .= ' '.$match[2];
						$subtitle = mytrim($subtitle, $strip_chars, $utf8);
					}

				}
				$stripped = true;
			}
		}
		foreach ($anystrips as /* $ndx => */ $strip) {
			if (preg_match('"^(.*?)'.$strip.'(.*?)$"'.$params,$name,$match)) {
				if (!($new_name = mytrim("$match[1] | $match[2]", $strip_chars, $utf8))) {
					continue;
				}
				$name = $new_name;
				$stripped = true;
			}
		}
		foreach ($leadstrips as $strip) {
			if (preg_match('"^'.$strip.RE_TITLE_BRACKETS.'(.*?)$"'.$params,$name,$match)) {
				if (!($new_name = mytrim($match[1], $strip_chars, $utf8))) {
					continue;
				}
				$name = $new_name;
				$stripped = true;
			}
		}
	}

	if (!$subtitle
	&&	(	preg_match('"^(.+)[:;]\h+(.+)$"'.$params, $name, $match)
		||	preg_match('"^(.+)\h+[(\[*\"\']+(.*?)[])*\"\']+$"'.$params, $name, $match)
		||	preg_match('"^(.+)(?:\h*[*:|(\[]+\h*|\h+[il\-]\h+)(.+(?:edition|editie|special))[\-*:|)\]]*$"'.$params, $name, $match)
		||	preg_match('"^(.+)\h*//+(.*?)$"'.$params, $name, $match)
		||	preg_match('"^(.+)\h+((?:x-?|christ)mas\h+(?:special|edition))$"'.$params, $name, $match)
		)
	) {
		[, $name, $subtitle] = $match;
		$subtitle = mytrim_title($subtitle, $utf8);
	}

	$name = mytrim($name, $strip_chars, $utf8);

	foreach (['name', 'subtitle'] as $part) {
		if ($$part
		&&	preg_match('"(?<!\his|meta|phase|sweet)\h+(?:part\h*)?(?<number>\d+|[mdclxvi]+)\h*$"i'.$params, $$part, $match)
		) {
			switch (($utf8 ? 'mb_strtolower' : 'strtolower')($match['number'])) {
			case 'i':
				if (preg_match('"&\s*[iI]$"'.($utf8 ? 'u' : ''), $$part)) {
					# & I, the I then doesn't mean the edition
					continue 2;
				}
				break;

			case 'mix':
			case 'x':
			case 'xl':
			case 'xxl':
			case 'xxxl':
			case 'c':
			case 'm':
			case 'mc':
			case 'd':
				# roman numeral too, but most often it means something else :)
				continue 2;
			}
			if (is_number($match[1])
			&&	(	$match[1][0] === '0'
				||	$match[1] >= 50
				)
			) {
				# not an edition when number starts with 0
				continue;
			}

			if (preg_match('"(?:str\.?|straat|laan|strasse)\h*'.preg_quote($match[0],'"').'$"i'.$params,$$part)) {
				continue;
			}
			$$part = str_replace($match[0],'',$$part);
			static $__nums = [
				'one'	=> 1,
				'two'	=> 2,
				'three'	=> 3,
				'four'	=> 4,
				'five'	=> 5,
				'six'	=> 6,
			];
			if (is_number($match[1])) {
				$edition = $match[1];
			} elseif (isset($__nums[$match[1]])) {
				$edition = $__nums[$match[1]];
			} elseif ($num = convert_roman_to_decimal($match[1], $utf8)) {
				$edition = $num;
			}
		}
	}

	if ($name === 'REC') {
		$name = 'REC.';
	}
	if (preg_match('"^(.*)?\h+#(\d+)$"'.$params,$name,$match)) {
		[, $name, $edition] = $match;
	}
	if (!empty($replacements)) {
		foreach ($replacements as $element => $ids_to_name) {
			foreach ($ids_to_name as $id => $replacement) {
				$name	  = str_replace($item_separator.$element.':'.$id.$item_separator, $replacement, $name);
				$subtitle = str_replace($item_separator.$element.':'.$id.$item_separator, $replacement, $subtitle);
			}
		}
	}
	return [$name, $subtitle, $edition];
}

function get_clean_title_from_facebook(string $name, ?array $args = null): string {
	require_once '_urltitle.inc';

	$trail_strips = [
		'(?:'.RE_TITLE_BRACKETS.'(?:(?:bijna\h*)?uitverkocht|(?:almost\h*)|sold\h*out)'.RE_TITLE_BRACKETS_MAY.')+',
		'(?:'.RE_TITLE_BRACKETS.RE_AND_MANY_MORE.RE_TITLE_BRACKETS_MAY.')+',
		'(?:'.RE_TITLE_BRACKETS.'(?:(?:limited|last)\h*tickets|tickets\h*now\h*on\h*sale)'.RE_TITLE_BRACKETS_MAY.')+',
		'(?:'.RE_TITLE_BRACKETS.'(?:de\h*afsluiting\h*van\h*het\h*(?:festival\h*)?seizoen!?)'.RE_TITLE_BRACKETS_MAY.')+',
		'(?:'.RE_TITLE_BRACKETS.RE_OFFICIAL_EVENT($args['organizationids'] ?? null).RE_TITLE_BRACKETS_MAY.')+',
	];
	$params = 'ui';
	$stripped = true;
	while ($stripped) {
		$stripped = false;
		foreach ($trail_strips as $strip) {
			if (preg_match('"^(.*?)[\h\-]*'.$strip.'$"'.$params, $name, $match)
			&&	($new_name = mytrim_title($match[1], true))
			) {
				$name = $new_name;
			}
		}
	}
	return $name;
}

const WILD_LEFT			= 1;
const WILD_RIGHT		= 2;
const WILD_BOTH			= 3;
const SEARCH_BINARY		= 4;
const LIMIT_TO_VALUE	= 8;
const NAME_UTF8			= 16;

function where_search_title(string $field, string $value, int $flags = 0): string {
	$utf8 = $flags & NAME_UTF8;

	if (!$utf8) {
		$value = win1252_to_utf8($value);
	}

	$search_value = utf8_mytrim($value);

	if (null === ($new_search_value = preg_replace('"[^\p{L}\d]+"u', '%', $search_value))) {
		preg_failure($search_value);
	} else {
		$search_value = $new_search_value;
	}

	if (preg_match('"^%+$"u', $search_value)) {
		# never match anything
		return '0';
	}

	$search_field = !str_contains($field, 'NAME_FOR_SEARCH') ? 'REGEXP_REPLACE('.$field.',"[^[:alnum:]]","")' : $field;

	if (!$utf8) {
		$search_value = utf8_to_win1252($search_value);
	}

	$prefix  = $flags & WILD_LEFT  ? '%' : null;
	$postfix = $flags & WILD_RIGHT ? '%' : null;

	return	$search_field.' LIKE '.($flags & SEARCH_BINARY ? 'BINARY ' : null).'"'.$prefix.addslashes($search_value).$postfix.'"'.
		(	$flags & LIMIT_TO_VALUE
		?	' AND LENGTH('.$field.') <= '.(strlen($search_value) * 1.5)
		:	null
		);
}

function convert_roman_to_decimal(string $roman, bool $utf8 = false): int {
	# WARNING: not checked if this works for complex roman numerals
	static $__romans = [
		'M'		=> 1000,
		'CM'	=> 900,
		'D'		=> 500,
		'CD'	=> 400,
		'C'		=> 100,
		'XC'	=> 90,
		'L'		=> 50,
		'XL'	=> 40,
		'X'		=> 10,
		'IX'	=> 9,
		'V'		=> 5,
		'IV'	=> 4,
		'I'		=> 1,
	];
	$result = 0;
	$roman = ($utf8 ? 'mb_strtoupper' : 'strtoupper')($roman);
	foreach ($__romans as $letter => $value) {
		while (str_starts_with($roman, $letter)) {
			$result += $value;
			$roman = ($utf8 ? 'mb_substr' : 'substr')($roman, strlen($letter));
		}
	}
	return $result;
}

function convert_array_to_win1252(array &$array, ?array $keep_utf8 = null, ?array $make_win1252 = null): void {
	$to_win1252 = static function(array &$array, int $depth = 0) use (&$to_win1252, $keep_utf8, $make_win1252) {
		foreach ($array as $key => &$val) {
			if (is_array($val)) {
				$to_win1252($val, $depth + 1);
			} elseif (
				is_string($val)
			&&	(	$depth
				||	$keep_utf8
				&&	!in_array($key, $keep_utf8, true)
				||	$make_win1252
				&&	in_array($key, $make_win1252, true)
				)
			) {
				$val = utf8_to_win1252($val);
			}
		}
		unset($val);
	};
	$to_win1252($array);
}

function log_sporadically(
	string	$uniq,
	string	$text	= '',
	int		$per	= ONE_MINUTE,
): bool {
	require_once '_memcache.inc';
	$key = 'log_sporadically:'.$uniq;
	global $memcache;
	if ($memcache) {
		if (memcached_add($key, true, $per)) {
			if ($text) {
				error_log($text);
			}
			return true;
		}
		return false;
	}

	# key did not exist, memcache failure, try acpu
	if (apcu_add($key, true, $per)) {
		if ($text) {
			error_log($text);
		}
		return true;
	}
	return false;
}

const ORGANIZATIONID_PARTYFLOCK	= 1961;
const ORGANIZATIONID_APPIC		= 10547;
const ORGANIZATIONID_ADE		= 3776;

function contains_partyflock_domain(string $url, bool $utf8 = false): bool {
	return preg_match('"partyflock\.(?:be|nl)"'.($utf8 ? 'u' : ''), $url);
}

function contains_partyflock_email(#[SensitiveParameter] string $email): bool {
	return (bool)preg_match('"@partyflock\.[a-z]+$"i', $email);
}

function array_sort_by_array(array $array_to_sort, array $array_as_ranking): array {
	return array_merge(array_flip($array_as_ranking), $array_to_sort);
}

function curl_get_response_headers(CurlHandle $ch, string|false &$data): array|false {
	if ($data === false) {
		return false;
	}
	$header_size = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
	$header_data = substr($data, 0, $header_size);
	$data = substr($data, $header_size);
	$headers = [];
	foreach (explode("\r\n", mytrim($header_data)) as $header_line) {
		$parts = explode(':', $header_line, 2);
		$headers[$parts[0]] = isset($parts[1]) ? mytrim($parts[1]) : null;
	}
	return $headers;
}

function item_commit(): bool {
	# Use this match with explicit calls, because IntelliJ cannot handle de cal function with string.
	if (!(/* $id = */ match($element = $_REQUEST['sELEMENT']) {
		'artist'		=> actual_artist_commit(),
		'location'		=> actual_location_commit(),
		'organization'	=> actual_organization_commit(),
		'party'			=> actual_party_commit(),
		'video'			=> actual_video_commit(),
		default 		=> /* $id = call_user_func */("actual_{$element}_commit")(),})	# NOSONAR
	) {
		return false;
	}

	#error_log_r(get_dirty_cache(), 'item_commit');

	return true;

	#### FIX CACHING (CLEAR):

	#require_once '_error.inc';
	#store_messages_in_cookie();
	#see_other(get_element_href($element, $id));
}

function add_or_set(array &$main_array, int|string $index, array $extra_array): void {
	if (isset($main_array[$index])) {
		$main_array[$index] += $extra_array;
	} else {
		$main_array[$index] = $extra_array;
	}
}

function unlink_at_end(string $file_to_delete): void {
	static $__files_to_delete = [];
	if (isset($__files_to_delete[$file_to_delete])) {
		error_log("DEBUG unlink_at_end: $file_to_delete already registered");
		return;
	}
	$__files_to_delete[$file_to_delete] = true;
	static $__registered = false;
	if ($__registered) {
		return;
	}
	$__registered = true;
	register_shutdown_function(static function(string $file_to_delete): void {
		register_shutdown_function(static function(string $file_to_delete): void {
			unlink($file_to_delete);
		}, $file_to_delete);
	}, $file_to_delete);
}
