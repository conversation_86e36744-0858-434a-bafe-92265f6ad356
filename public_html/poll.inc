<?php

require_once '_poll.inc';
require_once '_pollget.inc';
require_once '_wrap.inc';

function perform_commit() {
	switch ($_REQUEST['ACTION']) {
	case 'commitnew':		return poll_commit_new();
	case 'commit':			return poll_commit();
	case 'open':			return poll_open();
	case 'close':			return poll_close();
	case 'remove':			return poll_set_status(POLLSTATUS_REMOVED);
	case 'undelete':
	case 'accept':			return poll_set_status(POLLSTATUS_ACCEPTED);
	case 'unaccept':		return poll_set_status(POLLSTATUS_UNACCEPTED);
	case 'flushvotes':		return poll_flush_votes();
	case 'removefromprofile':	return poll_remove_from_profile();
#	case 'realremove':		require_once '_remove.inc'; return remove_element();
	}
}
function display_body() {
	switch ($_REQUEST['ACTION']) {
	case null:
	default:		return poll_display_overview();
	case 'single':
	case 'commitnew':
	case 'commit':
	case 'comments':
	case 'comment':
	case 'open':
	case 'close':
	case 'openwarning':
	case 'removefromprofile':
	case 'unaccept':
	case 'accept':
	case 'flushvotes':
	case 'undelete':	return poll_display_single();
	case 'newform':		return poll_display_new_form();
	case 'form':		return poll_display_form();
	}
}
function poll_remove_from_profile() {
	if (!require_idnumber($_REQUEST,'sID')) {
		return;
	}
	_poll_remove_from_profile($_REQUEST['sID']);
}
function poll_set_status($val) {
	if ($val == POLLSTATUS_REMOVED) {
		if (!require_post()
		||	!require_admin('poll')
		) {
			return;
		}
	}
	if (!require_idnumber($_REQUEST,'sID')) {
		return;
	}
	$pollid = $_REQUEST['sID'];
	$poll = db_single_assoc('poll','SELECT ELEMENT,ELEMENTID,USERID,CSTAMP,STATUS FROM poll WHERE POLLID='.$pollid,DB_USE_MASTER);
	if ($poll === false) {
		return;
	}
	if (!$poll) {
		not_found(); return;
	}
	if ($poll['STATUS'] == $val) {
		register_warning('poll:warning:nothing_changed_LINE');
		return;
	}
	unset($poll['STATUS']);
	if (!may_alter_poll($poll,true)) {
		return;
	}
	if (!db_insert('poll_log','
		INSERT INTO poll_log
		SELECT * FROM poll
		WHERE POLLID='.$pollid.'
		  AND STATUS!='.$val)
	) {
		return;
	}
	if (!db_affected()) {
		register_warning('poll:warning:nothing_changed_LINE');
		return;
	}
	if (!db_update('poll','
		UPDATE poll SET
			MUSERID		='.CURRENTUSERID.',
			MSTAMP		='.CURRENTSTAMP.',
			STATUS		='.$val.',
			OPENSTAMP	=IF(STATUS='.POLLSTATUS_ACCEPTED.' AND '.$val.'!='.POLLSTATUS_ACCEPTED.',
						OPENSTAMP,
						IF ('.$val.'='.POLLSTATUS_ACCEPTED.',
							/* OPEN: */ IF (OPENSTAMP=0,'.CURRENTSTAMP.',OPENSTAMP),
							OPENSTAMP)),
			CLOSESTAMP	=IF(STATUS='.POLLSTATUS_ACCEPTED.' AND '.$val.'!='.POLLSTATUS_ACCEPTED.',
						/* CLOSE: */ IF(OPENSTAMP=0 OR CLOSESTAMP!=0,CLOSESTAMP,IF(CLOSESTAMP=0,IF(OPENSTAMP=0,0,'.CURRENTSTAMP.'),CLOSESTAMP)),
						IF ('.$val.'='.POLLSTATUS_ACCEPTED.',
							/* OPEN: */ 0,
							OPENSTAMP)),
			OPENDURATION	=IF(STATUS='.POLLSTATUS_ACCEPTED.' AND '.$val.'!='.POLLSTATUS_ACCEPTED.',
						/* CLOSE: */ IF(OPENSTAMP=0 OR CLOSESTAMP!=0,OPENDURATION,OPENDURATION+('.CURRENTSTAMP.'-OPENSTAMP)),
						IF ('.$val.'='.POLLSTATUS_ACCEPTED.',
							/* OPEN: */ OPENDURATION,
							OPENDURATION))
		WHERE POLLID='.$pollid.'
		  AND STATUS!='.$val)
	) {
		return;
	}
	switch ($val) {
	case POLLSTATUS_ACCEPTED:	$key = 'poll:notice:accepted_LINE'; break;
	case POLLSTATUS_UNACCEPTED:	$key = 'poll:notice:rejected_LINE'; break;
	case POLLSTATUS_REMOVED:	$key = 'poll:notice:removed_LINE'; break;
	}
	register_notice($key,DO_UBB,array('POLLID'=>$pollid));
	flush_poll($pollid);
}
function poll_open() {
	if (!($pollid = require_idnumber($_REQUEST,'sID'))) {
		return false;
	}
	$poll = get_poll($pollid);
	if (!$poll) {
		return false;
	}
	if (!may_alter_poll($poll,true)) {
		return false;
	}
	if ($poll['OPENSTAMP'] && !$poll['CLOSESTAMP']) {
		warning('Poll is al open!');
		return false;
	}
	if (!db_insert('poll_log','
		INSERT INTO poll_log
		SELECT * FROM poll
		WHERE (OPENSTAMP=0 OR CLOSESTAMP!=0)
		  AND POLLID='.$pollid)
	||	!db_update('poll','
		UPDATE poll SET
			MSTAMP		='.CURRENTSTAMP.',
			MUSERID		='.CURRENTUSERID.',
			CLOSESTAMP	=0,
			OPENSTAMP	='.CURRENTSTAMP.'
		WHERE (OPENSTAMP=0 OR CLOSESTAMP!=0)
		  AND POLLID='.$pollid)
	) {
		return false;
	}
	unhide_closed_home_polls();
	register_notice('poll:notice:opened_LINE');
	flush_poll($pollid);
	return true;
}
function unhide_closed_home_polls() {
	if (!db_single('poll','SELECT 1 FROM poll WHERE ELEMENT="home" AND POLLID='.$_REQUEST['sID'])) {
		return;
	}
	$openpolls = db_simpler_array('poll','SELECT POLLID FROM poll WHERE CLOSESTAMP=0 AND OPENSTAMP!=0 AND ELEMENT="home"');
	if (!$openpolls) {
		return;
	}
	!db_delete('hidepoll','
	DELETE FROM hidepoll
	WHERE POLLID IN ('.implode(',',$openpolls).')'
	);
}
function poll_close() {
	if (!($pollid = require_idnumber($_REQUEST,'sID'))) {
		return false;
	}
	$poll = get_poll($pollid);
	if (!$poll) {
		return false;
	}
	if (!may_alter_poll($poll,true)) {
		return false;
	}
	if (!$poll['OPENSTAMP']
	||	$poll['CLOSESTAMP']
	) {
		register_warning('poll:warning:already_closed_LINE',DO_UBB,['POLLID'=>$pollid]);
		return false;
	}
	if (!db_insert('poll_log','
		INSERT INTO poll_log
		SELECT * FROM poll
		WHERE OPENSTAMP!=0
		  AND CLOSESTAMP=0
		  AND POLLID='.$pollid)
	||	!db_update('poll','
		UPDATE poll SET
			MSTAMP='.CURRENTSTAMP.',
			MUSERID='.CURRENTUSERID.',
			OPENDURATION=OPENDURATION+('.CURRENTSTAMP.'-OPENSTAMP),
			CLOSESTAMP='.CURRENTSTAMP.'
		WHERE OPENSTAMP!=0
		  AND CLOSESTAMP=0
		  AND POLLID='.$pollid)
	) {
		return;
	}
	unhide_closed_home_polls();
	register_notice('poll:notice:closed_LINE',DO_UBB,['POLLID'=>$pollid]);
	flush_poll($pollid);
}
function poll_flush_votes() {
	if (!require_post()
	||	!require_idnumber($_REQUEST,'sID')
	) {
		return false;
	}
	$pollid = $_REQUEST['sID'];
	$poll = db_single_assoc('poll','SELECT ELEMENT,ELEMENTID,USERID,CSTAMP FROM poll WHERE POLLID='.$pollid);
	if ($poll === false) {
		return false;
	}
	if (!$poll) {
		not_found(); return;
	}
	if (!may_alter_poll($poll,true)) {
		return false;
	}
	$answerids = db_simpler_array('poll_answer', 'SELECT ANSWERID FROM poll_answer WHERE POLLID='.$pollid);
	if ($answerids === false){
	 	return false;
	}
	if (!db_insert('pollflush','
		INSERT INTO pollflush SET
			POLLID	='.$pollid.',
			USERID	='.CURRENTUSERID.',
			FSTAMP	='.CURRENTSTAMP)
	) {
		return false;
	}
	if ($answerids) {
		$dones = db_simpler_array('poll_done','SELECT USERID FROM poll_done WHERE POLLID='.$pollid);
		if ($dones) {
			foreach ($dones as $userid) {
				memcached_delete('polldone:'.$pollid.':'.$userid);
			}
		}
		$answeridstr = implode(',',$answerids);
		if (!db_delete('poll_answer_counter','
			DELETE FROM poll_answer_counter
			WHERE ANSWERID IN ('.$answeridstr.')')
		||	!db_insert('poll_filled_log','
			INSERT INTO poll_filled_log
			SELECT *,'.CURRENTSTAMP.' FROM poll_filled
			WHERE ANSWERID IN ('.$answeridstr.')')
		||	!db_delete('poll_filled','
			DELETE FROM poll_filled
			WHERE ANSWERID IN ('.$answeridstr.')')
		) {
			return false;
		}
	}
	if (!db_delete('poll_counter','
		DELETE FROM poll_counter
		WHERE POLLID='.$pollid)
	||	!db_insert('poll_done_log','
		INSERT INTO poll_done_log
		SELECT *,'.CURRENTSTAMP.' FROM poll_done
		WHERE POLLID='.$pollid)
	||	!db_delete('poll_done','
		DELETE FROM poll_done
		WHERE POLLID='.$pollid)
	||	!db_insert('poll_log','
		INSERT INTO poll_log
		SELECT * FROM poll
		WHERE POLLID='.$pollid)
	||	!db_update('poll','
		UPDATE poll SET
			OPENSTAMP	=IF(OPENSTAMP!=0 AND CLOSESTAMP=0,'.CURRENTSTAMP.',0),
			CLOSESTAMP	=0,
			OPENDURATION	=0
		WHERE POLLID='.$pollid)
	) {
		return false;
	}
	register_notice('poll:notice:votes_flushed_LINE');
	flush_poll($pollid);
	return true;
}
function poll_display_overview() {
	require_once '_pagecontrols.inc';

	layout_show_section_header();

	poll_menu();

	$poll_admin = have_admin('poll');
	$news_admin = have_admin('news');
	$promo_admin = have_admin('promo');
	$announcement_admin = have_admin('announcement');

	if (!isset($_REQUEST['PAGE'])) {
		?><form<?
		?> accept-charset="utf-8"<?
		?> onsubmit="return submitForm(this)"<?
		?> method="get"<?
		?> action="/poll/searchresult"<?
		?>><?
		?><div class="block"><?= __C('search:for_question'); ?>: <input type="search" autosave="pollname" name="TERM" id="term"<?
		if (!empty($_REQUEST['TERM'])) {
			?> value="<?= escape_utf8($_REQUEST['TERM']); ?>"<?
		}
		?> autofocus="autofocus"> <?
		?> <select name="ELEMENT"><?
		?><option value="all"><?= element_name('all') ?></option><?
		if (isset($_REQUEST['ELEMENT'])) {
			$selected = $_REQUEST['ELEMENT'];
		} else {
			switch ($_REQUEST['ACTION']) {
			case null:	$selected = 'home'; break;
			case 'all':	$selected = null; break;
			default:	$selected = $_REQUEST['ACTION']; break;
			}
		}
		foreach (get_pollelementnames() as $element => $name) {
			?><option<?
			if ($selected == $element) {
				?> selected="selected"<?
			}
			?> value="<?= $element; ?>"><?= $name; ?></option><?
		}
		?></select> <?
		?><input type="submit" value="<?= __('action:search') ?>" /></div><?
	}
	switch ($_REQUEST['ACTION']) {
	case 'personal':
		if (!have_user()) {
			return;
		}
		$total = get_userpoll_count();
		if (!$total) {
			?><p>Je hebt nog geen eigen polls.</p><?
			return;
		}
		break;
	case 'user':
		if ($subID = $_REQUEST['subID']) {
			if (!require_visible_esetting($subID,'STATISTICS',have_admin('helpdesk','poll'))) {
				http_status(403);
				return;
			}
			$total = db_single('poll','SELECT COUNT(*) FROM poll WHERE USERID='.$subID);
			if (!$total) {
				?><p>Er zijn geen polls gevonden van <?= get_element_link('user',$subID); ?>.</p><?
				return;
			}
			break;
		}
	case 'topic':
	case 'flocktopic':
		$total = db_single('poll','SELECT COUNT(*) FROM poll WHERE ELEMENT="'.$_REQUEST['ACTION'].'"');
		if (!$total) {
			switch ($_REQUEST['ACTION']) {
				case 'topic':
					?><p>Er zijn op dit moment geen polls in forumberichten.</p><?
					break;
				case 'flocktopic':
					?><p>Er zijn op dit moment geen polls in flockberichten.</p><?
					break;
				case 'user':
					?><p>Er zijn op dit moment geen leden met polls op hun profiel.</p><?
					break;
			}
			return;
		}
		break;
	case 'news':
	case 'promo':
	case 'announcement':
		$total = db_single('poll','SELECT COUNT(*) FROM poll WHERE ELEMENT="'.$_REQUEST['ACTION'].'"');
		if (!$total) {
			?><p>Er zijn op dit moment geen polls.</p><?
			return;
		}
		break;
	case 'all':
		$total = db_single('poll','SELECT COUNT(*) FROM poll');
		if (!$total) {
			?><p>Er zijn totaal geen polls op dit moment.</p><?
			return;
		}
		break;
	case 'searchresult':
		$total = false;
		if (empty($_REQUEST['TERM'])) {
			_error('Je moet wel een term opgeven!');
			return;
		}
		if (!empty($_REQUEST['ELEMENT'])
		&&	$_REQUEST['ELEMENT'] != 'all'
		&&	!require_pollelement($_REQUEST,'ELEMENT')
		) {
			return;
		}
		break;
	default:
		$_REQUEST['ACTION'] = 'home';
		$total = db_single('poll','SELECT COUNT(*) FROM poll WHERE ELEMENT="home"');
		if (!$total) {
			?><p>Er zijn op dit moment geen polls.</p><?
			return;
		}
		break;
	}
	if ($total > 100) {
		$controls = new _pagecontrols;
		$controls->set_per_page(100);
		$controls->include_all(false);
		$controls->show_prev_next();
		$controls->set_total($total);
		$controls->set_order('poll.POLLID');
	}
	switch ($_REQUEST['ACTION']) {
	case 'personal':
		if (isset($controls)) {
			$controls->set_url('/poll/personal');
		}
		$polls = memcached_rowuse_array(array('poll','poll_counter'),'
			SELECT QUESTION,FLAGS,ELEMENT,ELEMENTID,poll.POLLID,OPENSTAMP,CSTAMP,CLOSESTAMP,OPENDURATION,CNT,STATUS,USERID
			FROM poll
			LEFT JOIN poll_counter USING (POLLID)
			WHERE USERID='.CURRENTUSERID.
			(isset($controls) ? $controls->order_and_limit() : null)
		);
		break;
	case 'user':
		if ($subID) {
			if (isset($controls)) {
				$controls->set_url('/poll/user/'.$subID);
			}
			$polls = memcached_rowuse_array(array('poll','poll_counter'),'
				SELECT QUESTION,FLAGS,ELEMENT,ELEMENTID,poll.POLLID,OPENSTAMP,CSTAMP,CLOSESTAMP,OPENDURATION,CNT,STATUS,USERID
				FROM poll
				LEFT JOIN poll_counter USING (POLLID)
				WHERE USERID='.$subID.
				(isset($controls) ? $controls->order_and_limit() : null)
			);
			break;
		}
	case 'topic':
	case 'flocktopic':
	case 'news':
	case 'promo':
	case 'announcement':
		if (isset($controls)) {
			$controls->set_url('/poll/'.$_REQUEST['ACTION']);
		}
		$polls = memcached_rowuse_array(array('poll','poll_counter'),$qstr = '
			SELECT QUESTION,FLAGS,ELEMENT,ELEMENTID,poll.POLLID,OPENSTAMP,CSTAMP,CLOSESTAMP,OPENDURATION,CNT,STATUS,USERID
			FROM poll LEFT JOIN poll_counter USING (POLLID)
			WHERE ELEMENT="'.$_REQUEST['ACTION'].'"'.
			(isset($controls) ? $controls->order_and_limit() : null)
		);
		break;
	case 'all':
		if (isset($controls)) {
			$controls->set_url('/poll/all');
		}
		$polls = memcached_rowuse_array(array('poll','poll_counter'),'
			SELECT QUESTION,FLAGS,ELEMENT,ELEMENTID,poll.POLLID,OPENSTAMP,CSTAMP,CLOSESTAMP,OPENDURATION,CNT,STATUS,USERID
			FROM poll FORCE INDEX (PRIMARY)
			LEFT JOIN poll_counter USING (POLLID)'.
			(isset($controls) ? $controls->order_and_limit() : null)
		);
		break;
	case 'searchresult':
		$polls = memcached_rowuse_array(['poll', 'poll_counter'],'
			SELECT QUESTION,FLAGS,ELEMENT,ELEMENTID,poll.POLLID,OPENSTAMP,CSTAMP,CLOSESTAMP,OPENDURATION,CNT,STATUS,USERID
			FROM poll LEFT JOIN poll_counter USING (POLLID)
			WHERE '.(	empty($_REQUEST['ELEMENT']) || $_REQUEST['ELEMENT'] == 'all'
				?	null
				:	'ELEMENT="'.$_REQUEST['ELEMENT'].'" AND'
			).' QUESTION LIKE "%'.str_replace(' ','%',addslashes(db_quote_wild($_REQUEST['TERM']))).'%"
			ORDER BY poll.POLLID DESC
			LIMIT 101'
		);
		if ($polls === false) {
			return;
		}
		$resultsize = $polls ? count($polls) : 0;

		?><p><?= Eelement_name('search_term') ?>: <b>&quot;<?= escape_utf8($_REQUEST['TERM']); ?>&quot;</b> levert <?

		if (!$resultsize) {
			?>geen resultaten op.<?
			return;
		} elseif ($resultsize > 100) {
			?>meer dan 100 resultaten op. De recentste 100 worden getoond<?
		} elseif ($resultsize > 1) {
			echo $resultsize; ?> resultaten op<?
		} else {
			echo $resultsize; ?> resultaat op<?
		}
		?>:</p><?
		break;
	case 'home':
		if (isset($controls)) {
			$controls->set_url('/poll');
		}
		$polls = memcached_rowuse_array(array('poll','poll_counter'),'
			SELECT QUESTION,FLAGS,ELEMENT,ELEMENTID,poll.POLLID,OPENSTAMP,CSTAMP,CLOSESTAMP,OPENDURATION,CNT,STATUS,USERID
			FROM poll LEFT JOIN poll_counter USING (POLLID)
			WHERE ELEMENT="home"'.
			(isset($controls) ? $controls->order_and_limit() : null)
		);
		break;
	}
	ob_start();
	if (isset($controls)) {
		$controls->display_and_store();
		number_reverse_sort($polls,'POLLID');
#		if ($controls->is_last()) {
#			$polls = array_reverse($polls);
#		}
	} elseif ($_REQUEST['ACTION'] != 'searchresult') {
		$polls = array_reverse($polls);
	}
	$frontpagepoll = ($_REQUEST['ACTION'] == 'home');
	$nonpersonal = ($_REQUEST['ACTION'] != 'personal');
	layout_open_box('poll');
	layout_open_table(TABLE_FULL_WIDTH | TABLE_SORTABLE | TABLE_VTOP);
	layout_start_header_row();
	layout_header_cell(Eelement_name('question'));
	if ($showlocation
	=	!empty($subID)
	||	$_REQUEST['ACTION'] == 'all'
	||	$_REQUEST['ACTION'] == 'personal'
	||	$_REQUEST['ACTION'] == 'searchresult'
	) {
		layout_header_cell_right(Eelement_name('location'));
	} elseif (
		$showexactlocation
	=	$_REQUEST['ACTION'] != 'home'
	&&	$_REQUEST['ACTION'] != 'user'
	) {
		ob_start();
		switch ($_REQUEST['ACTION']) {
		case 'topic':		echo Eelement_name('forum'); break;
		case 'flocktopic':	echo Eelement_name('flock'); break;
		case 'news':		echo Eelement_name('news'); break;
		case 'promo':		echo Eelement_name('promo'); break;
		case 'announcement':	echo Eelement_name('announcement'); break;
		}
		layout_header_cell_right(ob_get_clean());
	}
	layout_header_cell_right(Eelement_plural_name('vote'),CELL_RIGHT_SPACE);
	if ($showopener
	=	$nonpersonal
	&&	!$frontpagepoll
	&&	!empty($subID)
	) {
		layout_header_cell(Eelement_name('opener'));
	}
	layout_header_cell_right(__C($frontpagepoll ? 'field:opened' : 'field:created'),CELL_RIGHT_SPACE);
	layout_header_cell_right(Eelement_name('duration'));
	layout_stop_header_cell();
	layout_stop_header_row();
	// get accessinfo
	if (!$frontpagepoll
	&&	(	!empty($subID)
		||	$_REQUEST['ACTION'] != 'user'
		)
	) {
		$flocktopic_admin = have_admin('flocktopic');
		foreach ($polls as $poll) {
			switch ($poll['ELEMENT']) {
			case 'topic':		$topics[] = $poll['ELEMENTID'];	break;
			case 'flocktopic':	$flocktopics[] = $poll['ELEMENTID']; break;
			case 'news':		$news[] = $poll['ELEMENTID']; break;
			case 'promo':		$promos[] = $poll['ELEMENTID'];	break;
			case 'announcement':	$announcements[] = $poll['ELEMENTID']; break;
			}
		}
		if (isset($topics)) {
			sort($topics);
			$topics = memcached_rowuse_hash('topic','
				SELECT TOPICID,USERID,STATUS,FORUMID
				FROM topic
				WHERE TOPICID IN ('.implode(',',$topics).')'
			);
			if ($topics) {
				foreach ($topics as $topic) {
					if (!isset($forum[$topic['FORUMID']])) {
						$forum[$topic['FORUMID']] = $topic['FORUMID'];
					}
				}
				$forum = memcached_simple_hash('forum','
					SELECT FORUMID,SHORT
					FROM forum
					WHERE FORUMID IN ('.implode(',',$forum).')'
				);
			}
		}
		if (isset($flocktopics)) {
			sort($flocktopics);
			$oldflocktopics = $flocktopics;
			$flocktopics = memcached_rowuse_hash(['flocktopic', 'flock'],'
				SELECT	TOPICID, ft.USERID, SUBJECT, ft.FLOCKID, STATUS,
						PRIVATE,flock.ACCEPTED,REMOVED
				FROM flocktopic AS ft
				JOIN flock USING (FLOCKID)
				WHERE TOPICID IN ('.implode(', ', $flocktopics).')'
			);
		}
		if (isset($news)) {
			sort($news);
			$news = memcached_rowuse_hash('news','SELECT NEWSID,USERID,ACCEPTED,PSTAMP FROM news WHERE NEWSID IN ('.implode(',',$news).')');
		}
		if (isset($promos)) {
			sort($promos);
			$promos = memcached_rowuse_hash('promo','SELECT PROMOID,RELATIONID,USERID,ACTIVE,STARTSTAMP FROM promo WHERE PROMOID IN ('.implode(',',$promos).')');
		}
		if (isset($announcements)) {
			sort($announcements);
			$announcements = memcached_rowuse_hash('announcement','SELECT ANNOUNCEMENTID,CUSERID,ACTIVE,STARTSTAMP FROM announcement WHERE ANNOUNCEMENTID IN ('.implode(',',$announcements).')');
		}
	}
	$frontpoll_admin = have_admin('frontpoll');
	ob_start();
	foreach ($polls as $poll) {
		if ($poll['ELEMENT'] == 'home'
		?	(	$poll['STATUS'] != POLLSTATUS_ACCEPTED
			||	!$poll['OPENSTAMP']
			)
			&&	!$frontpoll_admin
		:	(	$poll['STATUS'] != POLLSTATUS_ACCEPTED
			&&	CURRENTUSERID != $poll['USERID']
			&&	!$poll_admin
			)
		) {
			continue;
		}
		if (isset($news)
		&&	!$news_admin
		||	isset($flocktopics)
		&&	!$flocktopic_admin
		||	isset($topics)
		||	isset($promos)
		&&	!$promo_admin
		||	isset($announcements)
		&&	!$announcement_admin
		) {
			require_once '_element_access.inc';
			$infos = null;
			switch ($poll['ELEMENT']) {
			case 'news':		$infos = $news; break;
			case 'flocktopic':	$infos = $flocktopics; break;
			case 'topic':		$infos = $topics; break;
			case 'promo':		$infos = $promos; break;
			case 'announcement':	$infos = $announcements; break;
			}
			$info = isset($infos[$poll['ELEMENTID']]) ? $infos[$poll['ELEMENTID']] : null;
			if (!may_view_element($poll['ELEMENT'],$poll['ELEMENTID'],$info)) {
				continue;
			}
		}
		// QUESTION
		layout_start_rrow(
			$poll['STATUS'] == POLLSTATUS_ACCEPTED
		?	0
		:	(	$poll['STATUS'] == POLLSTATUS_UNACCEPTED
			?	ROW_LINE_THROUGH
			:	ROW_LIGHT
			)
		);
		?><a<?
		if (!$poll['OPENSTAMP']) {
			?> class="light"<?
		}
		?> href="<?= get_element_href('poll',$poll['POLLID'],$poll['QUESTION']);
		?>"><?= replace_shy(escape_utf8($poll['QUESTION'])); ?></a><?
		// LOCATION
		if ($showlocation) {
			layout_next_cell(class: 'right small');
			switch ($poll['ELEMENT']) {
				case 'topic':
					?><a href="<?= get_element_href('topic',$poll['ELEMENTID']); ?>"><?= ucfirst(element_name('topic')); ?></a><?
					break;
				case 'flocktopic':
					$subject = memcached_single('flocktopic','SELECT SUBJECT FROM flocktopic WHERE TOPICID='.$poll['ELEMENTID']);
					?><a href="<?= get_element_href('flocktopic',$poll['ELEMENTID'],$subject); ?>"><?= ucfirst(element_name('flocktopic')); ?></a><?
					break;
				case 'user':
					?><a href="<?= get_element_href('user',$poll['ELEMENTID']); ?>">Profiel</a><?
					break;
				case 'news':
					?><a href="<?= get_element_href('news',$poll['ELEMENTID']); ?>"><?= ucfirst(element_name('news')); ?></a><?
					break;
				case 'promo':
				case 'announcement':
					?><a href="<?= get_element_href($poll['ELEMENT'],$poll['ELEMENTID']); ?>"><?= ucfirst(element_name($poll['ELEMENT'])); ?></a><?
					break;
				case 'home':
					echo ucfirst(element_name('home'));
					break;
			}
		} elseif ($showexactlocation) {
			layout_next_cell(class: 'right small');
			switch ($_REQUEST['ACTION']) {
			case 'flocktopic':
				if (!isset($flocktopics[$poll['ELEMENTID']])) {
					log_warning('flocktopic with id '.$poll['ELEMENTID'].' not set');
				} else {
					$flocktopic = $flocktopics[$poll['ELEMENTID']];
					echo get_element_link('flock',$flocktopic['FLOCKID']);
				}
				break;
			case 'topic':
				if (isset($topics[$poll['ELEMENTID']])
				&&	isset($forum[$topic['FORUMID']])
				) {
					$topic = $topics[$poll['ELEMENTID']];
					$short = $forum[$topic['FORUMID']];
					?><a href="/forum/<?= $topic['FORUMID']; ?>"><?= escape_specials($short); ?></a><?
				}
				break;
			default:
				echo get_element_link($poll['ELEMENT'], $poll['ELEMENTID'], $poll['FLAGS'] & POLLFLAG_TOPIC_IS_QUESTION ? 'idem' : null);
				break;
			}
		}
		// VOTES
		layout_next_cell(class: 'rpad right');
		echo $poll['CNT'];
		if ($showopener) {
			// OPENER
			layout_next_cell(class: 'small rpad');
			if ($poll['ELEMENT'] != 'home') {
				echo get_element_link('user',$poll['USERID']);
			}
		}
		if ($frontpagepoll) {
			// OPENSTAMP
			set_cell_value($poll['OPENSTAMP']);
			layout_next_cell(class: 'rpad right');
			if ($poll['OPENSTAMP']) {
				_date_display_numeric($poll['OPENSTAMP'],'&nbsp;');
			}
		} else {
			// CSTAMP
			set_cell_value($poll['CSTAMP']);
			layout_next_cell(class: 'rpad right');
			_date_display_numeric($poll['CSTAMP'],'&nbsp;');
		}
		// OPENDURATION
		$opentime = $poll['OPENSTAMP'] ? ($poll['CLOSESTAMP'] ? $poll['OPENDURATION'] : CURRENTSTAMP - $poll['OPENSTAMP'] + $poll['OPENDURATION']) : 0;
		set_cell_value($opentime);
		layout_next_cell(class: 'right nowrap'.($poll['CLOSESTAMP'] ? ' light' : ''));
		if ($opentime) {
			echo get_duration($opentime);
		}
		layout_stop_row();
	}
	$tablecontent = ob_get_length();
	ob_end_flush();
	layout_close_table();
	layout_close_box();
	$boxcontent = ob_get_clean();
	if ($tablecontent) {
		echo $boxcontent;
		if (isset($controls)) {
			$controls->display_stored();
		}
	} else {
		?><p>Geen polls gevonden.</p><?
	}
}
function poll_menu() {
	$poll_admin = have_admin('poll');
	$news_admin = have_admin('news');
	$promo_admin = have_admin('promo');
	$announcement_admin = have_admin('announcement');
	layout_open_menu();
	layout_menuitem('Voorpagina','/poll',!$_REQUEST['ACTION']);
	layout_menuitem('Alles','/poll/all',$_REQUEST['ACTION'] == 'all');
	if (have_user()
	&&	(require_once '_pollget.inc')
	&&	get_userpoll_count()
	) {
		layout_menuitem('Jouw polls','/poll/personal',$_REQUEST['ACTION'] == 'personal');
	}
	layout_menuitem(ucfirst(element_name('topic')),'/poll/topic',$_REQUEST['ACTION'] == 'topic');
	layout_menuitem(ucfirst(element_name('flocktopic')),'/poll/flocktopic',$_REQUEST['ACTION'] == 'flocktopic');
	layout_menuitem(ucfirst(element_name('news')),'/poll/news',$_REQUEST['ACTION'] == 'news');
	layout_menuitem(ucfirst(element_name('promo')),'/poll/promo',$_REQUEST['ACTION'] == 'promo');
	layout_menuitem(ucfirst(element_name('announcement')),'/poll/announcement',$_REQUEST['ACTION'] == 'announcement');
	layout_menuitem('Profiel','/poll/user',!$_REQUEST['subID'] && $_REQUEST['ACTION'] == 'user');
	if (!$_REQUEST['ACTION']
	&&	have_admin('frontpoll')
	) {
		layout_close_menu();
		layout_open_menu();
		layout_menuitem(__C('action:add'),'/poll/newform/home');
	}
	if (have_user()
	&&	(	$_REQUEST['ACTION'] == 'personal'
		||	$_REQUEST['ACTION'] == 'profile'
		)
	) {
		layout_close_menu();
		layout_open_menu();
		layout_menuitem(__C('action:add_poll_to_profile'),'/poll/newform/user?ELEMENTID='.CURRENTUSERID);
	}
	layout_close_menu();
}
function poll_display_single() {
	if (!require_idnumber($_REQUEST,'sID')) {
		return;
	}
	if ($_REQUEST['ACTION'] == 'openwarning') {
		?><p><span class="warning">Je moet de poll eerst sluiten voordat je hem kunt aanpassen!</span></p><?
	}
	$pollid = $_REQUEST['sID'];
	$poll = get_poll($pollid);
	if ($poll === false) {
		return;
	}
	if (!$poll) {
		not_found(); return;
	}
	$polladmin = have_admin('poll');
	if (!$polladmin
	&&	$poll['ELEMENT'] == 'user'
	&&	($userstatus = memcached_status($poll['USERID']))
	&&	$userstatus == 'deleted'
	) {
		register_error('poll:error:user_is_deleted_LINE',array('ID'=>$pollid));
		now_gone(); return;
	}
	if (!$polladmin
	&&	$poll['STATUS'] != POLLSTATUS_ACCEPTED
	) {
		register_error('poll:error:rejected_LINE',['ID'=>$pollid]);
		no_permission();
		return;
	}
	layout_show_section_header($poll);

	switch (isset($_REQUEST['GENDER']) ? $_REQUEST['GENDER'] : null) {
	case 'M':
	case 'F':
		$total = memcached_single(array('poll_answer','poll_filled','user'),'
			SELECT COUNT(DISTINCT poll_filled.USERID)
			FROM poll_answer
				LEFT JOIN poll_filled ON poll_filled.ANSWERID = poll_answer.ANSWERID
				LEFT JOIN user ON user.USERID=poll_filled.USERID
			WHERE user.SEX="'.$_REQUEST['GENDER'].'"
			  AND poll_answer.POLLID='.$_REQUEST['sID']);
		break;
	default:
		$total = memcached_single(array('poll_answer','poll_filled'),'
			SELECT COUNT(DISTINCT USERID)
			FROM poll_answer
				LEFT JOIN poll_filled USING (ANSWERID)
			WHERE POLLID='.$_REQUEST['sID']
		);
	}
	$polladmin = have_admin('poll');
	if (!$polladmin) {
		switch ($poll['ELEMENT']) {
		case 'promo':
		case 'news':
		case 'topic':
		case 'flocktopic':
		case 'announcement':
			require_once '_element.inc';
			if (!may_view_element($poll['ELEMENT'],$poll['ELEMENTID'])) {
				_error('Je mag het element behorende bij poll met ID '.$_REQUEST['sID'].' niet zien!');
				return;
			}
			break;
		}
	}
	$frontpolladmin = have_admin('frontpoll');
	if (!$frontpolladmin) {
		if (!$poll['OPENSTAMP']) {
			_error('Je mag deze voorpaginapoll nog niet bekijken!');
			return;
		}
	}
	poll_menu();

	_poll_menu_single($poll);
	_poll_display_result($poll['POLLID'],true);

	if ($poll['ELEMENT'] == 'home'
	||	$poll['ELEMENT'] == 'user'
	) {
		require_once '_commentlist.inc';
		$cmts = new _commentlist;
		$cmts->item($poll);
		$cmts->display();
	}
}
function poll_display_new_form() {
	if (!require_something_trim($_REQUEST, 'SUBACTION')
	||	(	$_REQUEST['SUBACTION'] == 'home'
		?	!require_admin('frontpoll')
		:	!require_idnumber($_REQUEST,'ELEMENTID')
		)
	) {
		return;
	}
	layout_show_section_header(null,element_name('form'));

	layout_open_menu();
	layout_menuitem(Eelement_name('overview'),'/poll',!$_REQUEST['ACTION']);
	layout_close_menu();

	?><div class="block"><?
	switch ($_REQUEST['SUBACTION']) {
	case 'user':	?>Poll op profiel van <? break;
	case 'news':	?>Poll bij nieuwsbericht: <? break;
	case 'promo':	?>Poll bij promobericht: <? break;
	case 'home':	?>Poll op <?
			$link = '<a href="/home">voorpagina</a>';
			break;
	}
	echo isset($link) ? $link : get_element_link($_REQUEST['SUBACTION'],$_REQUEST['ELEMENTID']);
	?></div><?

	include_js('js/form/poll');
	?><form<?
	?> accept-charset="utf-8"<?
	?> onsubmit="return checkPollPart(this) &amp;&amp; submitForm(this)"<?
	?> method="post"<?
	?> action="/poll/commitnew/<?= $_REQUEST['SUBACTION']; ?>?ELEMENTID=<?= (($_REQUEST['SUBACTION'] != 'home') ? $_REQUEST['ELEMENTID'] : 0); ?>"<?
	?>><?
	_poll_display_form_part($_REQUEST['SUBACTION']);
	?><div class="block"><input type="submit" value="<?= __('action:add'); ?>" /></div></form><?
}
function poll_display_form() {
	if (!require_idnumber($_REQUEST,'sID')
	) {
		return;
	}
	$poll = get_poll($_REQUEST['sID']);
	if (!$poll) {
		_error('Poll met ID '.$_REQUEST['sID'].' is niet opvraagbaar!');
		return;
	}
	layout_open_section_header();
	?>Poll formulier<?
	layout_close_section_header();

	layout_open_menu();
	layout_menuitem(Eelement_name('overview'),'/poll',!$_REQUEST['ACTION']);
	layout_close_menu();

	?><form<?
	?> accept-charset="utf-8"<?
	?> onsubmit="return submitForm(this);"<?
	?> method="post"<?
	?> action="/poll/<?= $poll['POLLID']; ?>/commit"<?
	?>><?
	?><input type="hidden" name="ELEMENT" value="<?= $poll['ELEMENT']; ?>" /><?
	?><input type="hidden" name="ELEMENTID" value="<?= $poll['ELEMENTID']; ?>" /><?
	_poll_display_form_part($poll['ELEMENT'], $poll['ELEMENTID'], $poll['POLLID']);
	?><div class="block"><input type="submit" value="<?= __('action:change'); ?>" /></div></form><?
}
function poll_commit_new() {
	if (!require_user()
	||	!require_pollelement($_REQUEST,'SUBACTION')
	||	false === require_number($_REQUEST,'ELEMENTID')
	) {
		return;
	}
	$_REQUEST['sID'] = commit_poll($_REQUEST['SUBACTION'],$_REQUEST['ELEMENTID']);
	if ($_REQUEST['sID']
	&&	$_REQUEST['SUBACTION'] == 'user'
	) {
		if (!$_REQUEST['sID']) {
			error_log('sID is not a number: '.$_REQUEST['sID'],0);
		}
		if (!db_insert('userpoll_log','
			INSERT INTO userpoll_log
			SELECT * FROM userpoll
			WHERE USERID='.CURRENTUSERID)
		||	!db_insupd('userpoll','
			INSERT INTO userpoll SET
				USERID	='.CURRENTUSERID.',
				MUSERID	='.CURRENTUSERID.',
				MSTAMP	='.CURRENTSTAMP.',
				POLLID	='.$_REQUEST['sID'].'
			ON DUPLICATE KEY UPDATE
				MSTAMP	=VALUES(MSTAMP),
				MUSERID	=VALUES(MUSERID),
				POLLID	=VALUES(POLLID)')
		) {
			return false;
		}
		register_notice('poll:notice:added_to_profile_LINE');
		//Poll is aan het profiel toegevoegd.');
	}
}
function poll_commit() {
	if (!require_idnumber($_REQUEST,'sID')
	||	!require_element($_REQUEST,'ELEMENT',['home','user','flocktopic','topic','news','promo','announcement'])
	||	(	$_REQUEST['ELEMENT'] == 'home'
		?	false === require_number($_REQUEST,'ELEMENTID')
		:	!require_idnumber($_REQUEST,'ELEMENTID')
		)
	) {
		return;
	}
	$_REQUEST['sID'] = commit_poll($_REQUEST['ELEMENT'],$_REQUEST['ELEMENTID'],$_REQUEST['sID']);
}
