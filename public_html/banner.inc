<?php

require_once '_banner.inc';

function perform_commit(): ?bool {
	if (!have_admin('banner')) {
		return false;
	}
	switch ($_REQUEST['ACTION']) {
	case 'remove':
		require_once '_object.inc';
		return set_field('REMOVED', true, 'removed');

	case 'restore':
		require_once '_object.inc';
		return set_field('REMOVED', false, 'restored');

	case 'commit':
		return banner_commit();
	}
	return null;
}

function display_body(): void {
	if (!require_admin('banner')) {
		return;
	}
	switch ($_REQUEST['ACTION']) {
	case 'single':
	case 'commit':
	case 'remove':
	case 'restore':
		banner_display_single();
		return;
	case 'form':
		banner_display_form();
		return;
	case null:
	default:
		banner_display_overview();
	}
}

function banner_menu(): void {
	layout_open_menu();
	layout_menuitem(Eelement_name('overview'), '/banner', !$_REQUEST['ACTION']);
	layout_close_menu();
}

function banner_single_menu(?array $banner = null): void {
	layout_open_menu();
	if ($banner) {
		if ($_REQUEST['ACTION'] !== 'form') {
			layout_menuitem(__C('action:change'), "/banner/{$banner['BANNERID']}/form");
		}
		layout_menuitem(__C('action:add_ad'), "/ad/form?BANNERID={$banner['BANNERID']}");
	} else {
		layout_menuitem(__C('action:add'), '/banner/form');
	}
	if ($banner) {
		layout_continue_menu();
		show_element_menuitems($banner);
	}
	layout_close_menu();
}

function banner_display_overview(): void {
	layout_show_section_header();

	banner_menu();

	if (!($banners_total = db_single('banner', 'SELECT COUNT(*) FROM party_db.banner'))) {
		return;
	}
	require_once '_pagecontrols.inc';
	$controls = new _pagecontrols();
	$controls->set_total($banners_total);
	$controls->set_per_page(50);
	$controls->show_prev_next(true);
	$controls->set_url('/banner');
	$controls->set_order('BANNERID');

	if (!($banners = db_rowuse_hash(['banner', 'bannermultiparty', 'party'], '
		SELECT	BANNERID, RELATIONID, TYPE, banner.NAME, banner.CSTAMP, SIZETYPE, 0 + REMOVED AS REMOVED,
				GROUP_CONCAT(party.NAME) AS PARTY_NAMES
		FROM banner
		LEFT JOIN bannermultiparty USING (BANNERID)
		LEFT JOIN party USING (PARTYID)
		GROUP BY BANNERID
		'.$controls->order_and_limit()))
	) {
		if ($banners !== false) {
			?><div class="block"><?= __('banner:info:no_banners_LINE') ?></div><?
		}
		return;
	}
	banner_single_menu();

	$controls->display_and_store();

	layout_open_box('banner');
	layout_open_table(TABLE_FULL_WIDTH | TABLE_SORTABLE);
	layout_start_header_row();
	layout_header_cell(__C('field:added'), class: 'right');
	layout_header_cell(Eelement_name('description'));
	layout_header_cell(Eelement_name('relation'), class: 'small');
	layout_header_cell(Eelement_name('size(banner)'), class: 'small');
	layout_header_cell(Eelement_name('type'), class: 'small');
	layout_header_cell();
	layout_stop_header_row();

	foreach ($banners as $bannerid => $banner) {
		set_cell_value($banner['CSTAMP']);
		layout_start_rrow_right(NOWRAP,$banner['REMOVED'] ? 'unavailable' : null);
		_date_display($banner['CSTAMP'], short: true, time_span: true);
		layout_next_cell();
		?><a href="/banner/<?= $bannerid
		?>"><?= escape_utf8($banner['NAME'] ?: $banner['PARTY_NAMES'].' multi-party')
		?></a><?

		layout_next_cell(class: 'small');
		if ($banner['RELATIONID']) {
			echo get_element_link('relation',$banner['RELATIONID']);
		}
		layout_next_cell(class: 'small');
		echo banner_size_name($banner['SIZETYPE']);

		layout_next_cell(class: 'small');
		echo $banner['TYPE'];

		layout_next_cell(class: 'small');
		if ($banner['TYPE'] === 'upload') {
			echo memcached_single('bannerupload', '
				SELECT FILETYPE
				FROM bannerupload
				WHERE BANNERID = '.$bannerid
			);
		}
		layout_stop_row();
	}
	layout_close_table();
	layout_close_box();

	$controls->display_stored();
}

function banner_display_single(): void {
	if (!require_idnumber($_REQUEST,'sID')) {
		return;
	}
	layout_show_section_header();

	$bannerid = $_REQUEST['sID'];
	$banner = db_single_assoc('banner','SELECT * FROM banner WHERE BANNERID='.$bannerid);
	if ($banner === false) {
		return;
	}
	if (!$banner) {
		_error(__('banner:error:nonexistent_LINE', ['ID' => $bannerid]));
		return;
 	}
	$banner['BANNERID'] = $bannerid;
	banner_menu();
	banner_single_menu($banner);

	require_once '_ticketlist.inc';
	show_connected_tickets();

	$tower = $banner['WIDTH'] <= 160 && $banner['WIDTH'] < $banner['HEIGHT'];

	?><article itemscope itemtype="https://schema.org/Article"><?

	if ($banner['REMOVED']) {
		?><div class="light"><?
	}
	if ($tower) {
		?><table class="vtop fw dens"><tr><td><?
	} else {
		display_banner_innerbox($banner);
	}

	layout_open_box('banner');
	layout_box_header(Eelement_name('information'));

	$list = new deflist('deflist vtop');
	if ($banner['RELATIONID']) {
		$list->add_row(Eelement_name('relation'), get_element_link('relation',$banner['RELATIONID']));
	}
	$list->add_rows(
		Eelement_name('description'),	escape_utf8($banner['NAME']),
		Eelement_name('type'),		escape_specials($banner['TYPE']),
		Eelement_name('size(banner)'),	banner_size_name($banner['SIZETYPE'])
	);
	if ($banner['SIZETYPE'] !== BANNER_SIZE_TEXTLINK
	&&	$banner['SIZETYPE'] !== BANNER_SIZE_INLINE
	&&	$banner['TYPE']	 !== 'multiparty'
	) {
		$list->add_row(Eelement_name('dimension'), $banner['WIDTH'].' '.MULTIPLICATION_SIGN_ENTITY.' '.$banner['HEIGHT']);
	}
	if ($banner['BG']) {
		$bg = sprintf('%06X',$banner['BG']);
		$list->add_row(Eelement_name('background'),'<span style="background-color: #'.$bg.'; text-shadow: 1px 1px 0 '.(LITE ? 'white' : 'black').';">'.$bg.'</span>');
	}
	if ($banner['ADBOX']) {
		$list->add_row(Eelement_name('ad_block'),__('answer:yes'));
	}
	# NOTE: Legacy:
	if ($banner['FLASHONLY']) {
		$list->add_row(__C('attrib:flash_only'),__('answer:yes'));
	}
	# NOTE: Legacy:
	if ($banner['ONLY_NON_SECURE']) {
		$list->add_row(__C('attrib:non_secure_only'),
			__('answer:yes').
			($banner['ONLY_NON_SECURE'] & 2 ? ' + Firefox &lt; 23 '.__('status:secure') : null)
		);
	}
	# NOTE: Legacy:
	if ($banner['SKIP_LINUX']) {
		$list->add_row(__C('attrib:skip_linux'),__('answer:yes'));
	}
	# NOTE: Legacy:
	if ($bannerid <= 4919
	&&	!$banner['CLICKTAG']
	) {
		$list->add_row(__C('attrib:clicktag'),__('answer:no'));
	}
	# NOTE: Legacy:
	if (($fallbackid = $banner['FALLBACKID'])
	&&	($fallback_name = db_single_string('banner','SELECT NAME FROM banner WHERE BANNERID='.$fallbackid))
	) {
		$list->add_row(Eelement_name('fallback'),get_element_link('banner', $fallbackid, $fallback_name));
	}
	$list->display();

	layout_continue_box();

	$list = new deflist('deflist vtop'.($banner['TYPE'] === 'multiparty' ? null : ' fw'));

	switch ($banner['TYPE']) {
	case 'multiparty':
		if (!($partyids = db_rowuse_hash('bannermultiparty','
			SELECT PARTYID, WHAT
			FROM bannermultiparty
			WHERE BANNERID = '.$bannerid))
		) {
			if ($partyids === false) {
				return;
			}
			break;
		}
		change_timezone('UTC');
		foreach ($partyids as $partyid => $info) {
			/** @noinspection PhpRedundantOptionalArgumentInspection */
			extract($info, \EXTR_OVERWRITE);
			$party = memcached_party_and_stamp($partyid);
			if (!$party) {
				continue;
			}
			$list->add_row(
				Eelement_name('party'),
				get_element_link('party',$partyid).'</td>'.
				'<td class="right">'._date_get($party['STAMP_TZI']).'</td>'.
				'<td>'.get_element_link('location',$party['LOCATIONID']).'</td>'.
				'<td>'.($WHAT === 'front' ? element_name('frontside') : ($WHAT === 'back' ? element_name('backside') : element_plural_name('both_side')))
			);
		}
		change_timezone();
		break;

	case 'upload':
		if (!($upload = db_single_assoc('bannerupload', '
			SELECT FILETYPE, SIZE, SIZE2x
			FROM bannerupload
			WHERE BANNERID = '.$bannerid))
		) {
			if ($upload === false) {
				return;
			}
			register_error('banner:error:missing_upload_info_LINE');
			mail_log("banner $bannerid is missing bannerupload info");
			break;
		}
		$missing_x1	= !$upload['SIZE'];
		$missing_x2 = !$upload['SIZE2x'];
		if ($missing_x1 || $missing_x2) {
			(	$missing_x1
			?	register_error(...)
			:	register_warning(...)
			)('banner:error:missing_parts_LINE', ['1x' => $upload['SIZE'], '2x' => $upload['SIZE2x']]);
		}
		assert(is_array($upload)); # Satisfy EA inspection
		foreach ([null => null, '2x' => ' x2'] as $x2 => $xx2) {
			if ($upload['SIZE'.$x2]) {
				ob_start();
				print_human_bytes($upload['SIZE'.$x2]);
				$list->add_row(Eelement_name('size').$xx2, ob_get_clean());
			}
		}
		$list->add_row(Eelement_name('filetype'), $upload['FILETYPE']);
		break;

	case 'script':
		if (!($script = db_single_assoc('bannerscript','
			SELECT SCRIPTURL, ANCHORURL, IMAGEURL
			FROM bannerscript
			WHERE BANNERID = '.$bannerid))
		) {
			if ($script === false) {
				return;
			}
			break;
		}
		foreach ([
			'SCRIPTURL'	=> 'script_url',
			'ANCHORURL'	=> 'anchor_url',
			'IMAGEURL'	=> 'image_url',
		] as $name => $descr) {
			if (!$script[$name]) {
				continue;
			}
			$url = str_replace('#UNIQUE#', '1', $display_url = $script[$name]);
			/** @noinspection DisconnectedForeachInstructionInspection */
			$list->set_value_class('forcewrap');
			$list->add_row(Eelement_name($descr), '<a href="'.escape_specials($url).'">'.escape_specials($display_url).'</a>');
		}
		break;

	case 'link':
		if (!($link = db_single_assoc('bannerlink', '
			SELECT URL, OBJTYPE
			FROM bannerlink
			WHERE BANNERID = '.$bannerid))
		) {
			if ($link === false) {
				return;
			}
			break;
		}
		$list->set_value_class('forcewrap');
		$list->add_rows(
			Eelement_name('url'),		'<a href="'.($escaped_url = escape_specials($link['URL'])).'">'.$escaped_url.'</a>',
			Eelement_name('type'),		__('bannerlink:'.$link['OBJTYPE']),
		);
		break;

	case 'iframe':
		if (!($iframe = db_single_assoc('banneriframe','
			SELECT URL
			FROM banneriframe
			WHERE BANNERID = '.$bannerid))
		) {
			if ($iframe === false) {
				return;
			}
			break;
		}
		$list->set_value_class('forcewrap');
		$list->add_row(Eelement_name('url'), '<a href="'.($escaped_url = escape_specials($iframe['URL'])).'">'.$escaped_url.'</a>');
		break;

	case 'html':
		if (!($html = db_single_assoc('bannerhtml', '
			SELECT BODY, SCRIPT_URL
			FROM bannerhtml
			WHERE BANNERID = '.$bannerid))
		) {
			if ($html === false) {
				return;
			}
			break;
		}
		$list->add_row('HTML',
				nl2br(
				str_replace(' ','&nbsp; ',
				str_replace("\t",'&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; ',
					replace_shy(escape_utf8(preg_replace('"([/.#&=-]+)"u', "\$1\xAD", $html['BODY'])), utf8: true)
				)))
		);
		if ($html['SCRIPT_URL']) {
			$list->add_row(Eelement_name('script_url'), replace_shy(escape_ascii(preg_replace('"([/.#&=-]+)"', "\$1\xAD", $html['SCRIPT_URL']))));
		}
		break;
	case 'textlinks':
		if (!($textlinks = db_single('bannertextlinks', '
			SELECT TEXTLINKS
			FROM bannertextlinks
			WHERE BANNERID = '.$bannerid))
		) {
			if ($textlinks === false) {
				return;
			}
			break;
		}
		$list->add_row(Eelement_plural_name('textlink'), nl2br(escape_specials($textlinks)));
		break;

	case 'ubb':
		if (!($body = db_single('bannerubb', '
			SELECT BODY
			FROM bannerubb
			WHERE BANNERID = '.$bannerid))
		) {
			if ($body === false) {
				return;
			}
			break;
		}
		$list->add_row(Eelement_name('text'), nl2br(replace_shy(escape_specials($body))));
		break;
	}
	if ($list->have_rows()) {
		layout_box_header(__('banner:header:'.$banner['TYPE'].'_information'));
		$list->display();
	}
	layout_display_alteration_note($banner);
	layout_close_box();

	if ($tower) {
		?></td><td style="width: <?= $banner['WIDTH'] ?>px;"><?
		display_banner_innerbox($banner)
		?></td></tr></table><?
	}
	if ($banner['REMOVED']) {
		?></div><?
	}
	if ($ads = db_rowuse_hash(['ad', 'adinfo'], "
		SELECT ADID,STARTSTAMP,STOPSTAMP,IMPRESSIONS,IMPRESSIONSDONE,ACTIVE,INVOICENR,REMOVED
		FROM ad
		JOIN adinfo USING (ADID)
		WHERE BANNERID = $bannerid
		ORDER BY STARTSTAMP")
	) {
		require_once '_adrow.inc';
		layout_open_box('banner');
		layout_box_header(Eelement_plural_name('ad'));
		layout_open_table(TABLE_FULL_WIDTH);
		layout_start_header_row();
		layout_start_header_cell();
			echo Eelement_name('start');
		layout_next_header_cell();
			echo Eelement_name('last_day');
		layout_next_header_cell_right();
			echo Eelement_name('duration');
		layout_next_header_cell_right();
			echo Eelement_plural_name('impression');
		layout_stop_header_cell();
		layout_stop_header_row();
		foreach ($ads as $ad) {
			$adclasses = adrow_get_classes($ad);
			layout_start_rrow(0,$adclasses);
			?><a href="/ad/<?= $ad['ADID'] ?>"><? _dateday_display($ad['STARTSTAMP'],null,false,true); ?></a><?

			layout_next_cell();
			?><a href="/ad/<?= $ad['ADID'] ?>"><? _dateday_display($ad['STOPSTAMP']-1,null,false,true); ?></a><?

			layout_next_cell(class: 'right');
			$duration = $ad['STOPSTAMP'] - $ad['STARTSTAMP'];
			if ($duration > ONE_DAY) {
				echo __('duration:x_days',['DAYS'=>round($duration / ONE_DAY,1)]);
			} else {
				echo __('duration:x_hours',['HOURS'=>round($duration / ONE_HOUR, 1)]);
			}

			layout_next_cell(class: 'right');
			echo separate_thousands($ad['IMPRESSIONS']);
			layout_stop_row();
		}
		layout_close_table();
		layout_close_box();
	}

	?></article><?
}
function banner_display_form(): void {
	require_once '_ticket.inc';

	layout_show_section_header(null,element_name('form'));

	banner_menu();

	if ($bannerid = have_idnumber($_REQUEST,'sID')) {
		if (false === ($banner = db_single_assoc('banner','SELECT * FROM banner WHERE BANNERID='.$bannerid))) {
			return;
		}
		if (!$banner) {
			register_error('banner:error:nonexistent_LINE',['ID' => $bannerid]);
			return;
		}
		if ($banner['TYPE'] !== 'upload') {
			register_error('banner:error:obsolete_type_cannot_be_changed_LINE', ['TYPE' => $banner['TYPE']]);
			return;
		}
	} else {
		$banner = null;
	}

	banner_single_menu($banner);

	$bg = $banner && $banner['BG'] !== null ? sprintf('%06X',$banner['BG']) : null;

	if ($banner) {
		if ($tower = $banner['WIDTH'] < $banner['HEIGHT']) {
			?><table class="vtop fw dens"><tr><td><?
		} else {
			display_banner_innerbox($banner);
		}
	}
	include_js('js/form/banner');
	?><form<?
	?> accept-charset="utf-8"<?
	?> enctype="multipart/form-data"<?
	?> onsubmit="return submitForm(this);"<?
	?> method="post"<?
	?> action="/banner/<? if ($bannerid) { echo $bannerid ?>/<? } ?>commit"><?

	ticket_passthrough();

	layout_open_box('banner');
	layout_open_table('fw');
	layout_start_row();
		?><label for="relationid"><?= Eelement_name('relation') ?></label><?
		layout_field_value();
		require_once '_relationlist.inc';
		?><select id="relationid" name="RELATIONID"><option value="0"></option><?
		relationlist_show_options($banner ? $banner['RELATIONID'] : null);
		?></select><?
	layout_restart_row();
		?><label for="descr"><?= Eelement_name('description') ?></label><?
		layout_field_value();
		?><input type="text" name="NAME" value="<? if ($banner) { echo escape_utf8($banner['NAME']); } ?>" id="descr" /><?
	layout_restart_row();
		echo Eelement_name('size(banner)');
		layout_field_value();
		?><select required="required" id="size" name="SIZETYPE" onchange="<?
			?>setdisplay('maxwidthrow', parseInt(this.value) === <?= BANNER_SIZE_FRONTFLYER ?>);<?
		?>"><?
		banner_size_options($banner ? $banner['SIZETYPE'] : 0);
		?></select><?
	layout_restart_row(!$banner || $banner['SIZETYPE'] !== BANNER_SIZE_FRONTFLYER ? ROW_HIDDEN : 0, 'maxwidthrow');
		echo Eelement_name('maximum_width');
		layout_field_value();
		$widths[336] = 336;
		if ($banner) {
			$widths[$banner['WIDTH']] = $banner['WIDTH'];
		}
		sort($widths);
		?><select name="MAX_WIDTH"><?
		foreach ($widths as $mw) {
			?><option<?
			if ($banner
			?	$mw === $banner['WIDTH']
			:	$mw === 336
			) {
				?> selected<?
			}
			?> value="<?= $mw ?>"><?= $mw ?></option><?
		}
		?></select><?

/*	layout_restart_row();
		?><label for="type"><?= Eelement_name('type') ?></label><?
		layout_field_value();
		if ($banner) {
			?><input type="hidden" name="ORGTYPE" value="<?= $banner['TYPE'] ?>" /><?
		}
		?><select required="required" name="TYPE" id="type" onchange="typeChange(this)"><option value=""></option><?
		?><option<? if (!$bannerid || $banner['TYPE'] === 'upload')		{ ?> selected<? } ?> value="upload"><?= __('bannertype:upload'); ?></option><?
		?><option<? if ($bannerid && $banner['TYPE'] === 'iframe')		{ ?> selected<? } ?> value="iframe"><?= __('bannertype:iframe'); ?></option><?
		?><option<? if ($bannerid && $banner['TYPE'] === 'script')		{ ?> selected<? } ?> value="script"><?= __('bannertype:script'); ?></option><?
		?><option<? if ($bannerid && $banner['TYPE'] === 'link')		{ ?> selected<? } ?> value="link"><?= __('bannertype:link'); ?></option><?
		?><option<? if ($bannerid && $banner['TYPE'] === 'html')		{ ?> selected<? } ?> value="html"><?= __('bannertype:html'); ?></option><?
		?><option<? if ($bannerid && $banner['TYPE'] === 'textlinks')	{ ?> selected<? } else { ?> disabled<? } ?> value="textlinks"><?= __('bannertype:textlinks'); ?></option><?
		?><option<? if ($bannerid && $banner['TYPE'] === 'ubb')			{ ?> selected<? } ?> value="ubb"><?= __('bannertype:ubb'); ?></option><?
		?><option<? if ($bannerid && $banner['TYPE'] === 'floormovie')	{ ?> selected<? } ?> value="floormovie"><?= __('bannertype:floormovie'); ?></option><?
		?><option<? if ($bannerid && $banner['TYPE'] === 'multiparty')	{ ?> selected<? } ?> value="multiparty"><?= __('bannertype:multiparty'); ?></option><?
		?></select><?*/

	layout_restart_row();
		echo Eelement_name('background');
		layout_field_value();
		show_input([
			'name'			=> 'BG',
			'type'			=>'text',
			'pattern'		=> '^[\da-fA-F]{6}|$',
			'placeholder'	=> 'RRGGBB',
			'style'			=> /** @lang CSS */ ($bg ? 'background-color:#'.$bg : '').'; text-shadow: 1px 1px 0 '.(LITE ? 'white': 'black').';',
			'onkeyup'		=> /** @lang JavaScript */ 'changeBG(this);',
			'class'			=> 'id center',
			'value'			=> $bg
		]);

	layout_stop_row();
	layout_close_table();
	layout_close_box();

	// UPLOAD
	?><div id="uploadpart"<?
/*	if ($hidden = $bannerid && $banner['TYPE'] !== 'upload') {
		?> class="hidden"<?
	}*/
	?>><?
	layout_open_box('banner');
	layout_open_table('fw');
	layout_start_row();
		echo Eelement_name('image');
		layout_field_value();
		require_once 'defines/image_processing.inc';
		?><input<?
		?> type="file"<?
		?> name="UPLOAD_FILE"<?
		?> accept="<?= get_image_processing_accepted_mime_types() ?>,video/mp4,video/mpg,video/mpeg"<?
		?> onchange="Pf_bannerUploadChangeFile(this);"<?
		?> /><?

	layout_restart_row(id: 'upload-to-webp-row', rowuserdef: 'hidden');
		?><label for="upload_to_webp"><?= __C('action:convert_to_webp') ?></label><?
		layout_field_value();
		show_input([
			'id'		=> 'upload_to_webp',
			'type'		=> 'checkbox',
			'name'		=> 'UPLOAD_TO_WEBP',
			'value'		=> 1,
			'checked'	=> true,
		]);

	layout_restart_row(id: 'upload-to-mp4-row', rowuserdef: 'hidden');
		?><label for="upload_to_mp4"><?= __C('action:convert_to_mp4') ?></label><?
		layout_field_value();
		show_input([
			'id'		=> 'upload_to_mp4',
			'type'		=> 'checkbox',
			'name'		=> 'UPLOAD_TO_MP4',
			'value'		=> 1,
			'checked'	=> false
		]);

	layout_stop_row();
	layout_close_table();
	layout_close_box();
	?></div><?

	/*if ($banner) {
		$parties = db_rowuse_hash('bannermultiparty','SELECT PARTYID,WHAT FROM bannermultiparty WHERE BANNERID='.$bannerid);
		if ($parties === false) {
			return;
		}
	}
	?><div id="multipartypart"<?
	if ($hidden = !$bannerid || $banner['TYPE'] !== 'multiparty') {
		?> class="hidden"<?
	}
	?>><?
	require_once '_fillelementid.inc';
	layout_open_box('banner');
	layout_open_table('hpadded fw');

	?><tr><?
	?><th></th><?
	?><th><?= Eelement_name('event') ?></th><?
	?><th><?= Eelement_plural_name('flyer') ?></th><?
	?><th><?= Eelement_name('frontside') ?> &rarr; <?= element_name('backside') ?></th><?
	?><th>&rarr; <?= __C('pagecontrols:next') ?></th><?
	?></tr><?

	$parties[0] = false;

	?><tbody id="<?= $uniq = uniqid('prntrow', true) ?>"><?
	$i = 0;
	foreach ($parties as $partyid => $party) {
		if ($party) {
			extract($party, \EXTR_OVERWRITE);
		}
		?><tr><td class="field"><?= Eelement_name('party') ?></td><td class="nowrap" style="width: auto;"><?
		show_elementid_input(
			'party',
			$partyid, [
			'name'		=> 'PARTYID[]',
			'onkeyup'	=> "
							Pf_changePartyID(this, '$uniq');
							(function(self) {
								let row = Pf.getParentNode(self, 'TR');
								setdisplay(row.lastChild, !self.value.is_empty());
							})(this);"
		]);
		?></td><td<?
		if (!$partyid) {
			?> class="hidden"<?
		}
		?>><?
		?><select name="WHAT[<?= $i ?>]"><?
			foreach ([
				'front'		=> element_name('flyer_front'),
				'back'		=> element_name('flyer_back'),
				'both'		=> element_plural_name('both_side'),
			] as $opt => $desc) {
				?><option<?
				if ($party && $WHAT === $opt) {
					?> selected="selected"<?
				}
				?> value="<?= $opt ?>"><?= $desc ?></option><?
			}
		?></select><?
		?></td><?
		?></tr><?
		++$i;
	}
	?></tbody><?
	layout_close_table();
	layout_close_box();
	?></div><?*/

	/*if ($bannerid && $banner['TYPE'] === 'iframe') {
		$iframe = db_single_assoc('banneriframe','
			SELECT URL
			FROM banneriframe
			WHERE BANNERID='.$bannerid
		);
		if ($iframe === false) {
			return;
		}
	}
	?><div id="iframepart"<?
	if ($hidden = !$bannerid || $banner['TYPE'] !== 'iframe') {
		?> class="hidden"<?
	}
	?>><?
	layout_open_box('banner');
	layout_open_table('fw');
	layout_start_row();
		echo Eelement_name('url');
		layout_field_value();
		show_input([
			'type'			=> 'url',
			'data-valid'	=> 'working-url',
			'name'			=> 'IFRAME_URL',
			'value'			=> empty($iframe['URL']) ? null : $iframe['URL'],
			'maxlength'		=> 255,
			'required'		=> !$hidden]);
	layout_stop_row();
	layout_close_table();
	layout_close_box();
	?></div><?*/

	/*if ($banner) {
		$html = db_single_assoc('bannerhtml', '
			SELECT BODY, SCRIPT_URL
			FROM bannerhtml
			WHERE BANNERID = '.$bannerid
		);
		if ($html === false) {
			return;
		}
	}
	?><div id="htmlpart"<?
	if (!$bannerid || $banner['TYPE'] !== 'html') {
		?> class="hidden"<?
	}
	?>><?
	layout_open_box('banner');
	layout_open_table('');
	layout_start_row();
		echo Eelement_name('html');
		layout_field_value();
		show_textarea([
			'name'		=> 'HTML_BODY',
			'cols'		=> 80,
			'rows'		=> 15,
			'class'		=> 'growToFit',
			'value_utf8'	=> !empty($html['BODY']) ? $html['BODY'] : '',
		]);

	layout_restart_row();
		echo Eelement_name('script_url');
		layout_field_value();
		show_input([
			'type'		=> 'url',
			'data-valid'	=> 'working-url',
			'name'		=> 'SCRIPT_URL',
			'value'		=> isset($html) ? $html['SCRIPT_URL'] : null,
			'maxlength'	=> 255,
		]);

	layout_restart_spanned_row();
		?><hr class="slim"><?

	layout_restart_row();
		layout_next_cell();
		?><dl class="boldterm"><?
			?><dt>#RAWCOUNTCLICK#</dt><?
			?><dd>wordt vervangen door juiste link om clickthrough te tellen<?

			?><dt>#COUNTCLICK#</dt><?
			?><dd>wordt vervangen door juiste link URLENCODED om clickthrough te tellen<?

			?><dt>#CURRENTNICKURLENC#</dt><?
			?><dd>wordt vervangen door huidige nickname geschikt voor in een URL</dd><?

			?><dt>#CURRENTLANGUAGE#</dt><?
			?><dd>wordt vervangen door de huidige taal (nl, en, de, etc)</dd><?

			?><dt>#CURRENTNICK#</dt><?
			?><dd>wordt vervangen door huidige nickname<br /><?

			?><dt>#CURRENTTHEME#</dt><?
			?><dd>wordt vervangen door huidige thema (dark of light)</dd><?

			?><dt>#CURRENTURL#</dt><?
			?><dd>wordt vervangen door huidige URL</dd><?

			?><dt>#2x#</dt><?
			?><dd>wordt vervangen door @2x indien van toepassing</dd><?

			?><dt>#UNIQUE#</dt><?
			?><dd>wordt vervangen door een uniek nummer</dd><?

			?><dt>#SCRIPT#</dt><?
			?><dd>wordt vervangen door script tag met opgegeven URL</dd><?

			?><dt>{IFTHEMELIGHT}&lt;light&gt;{ELSE}&lt;dark&gt{ENDIF}</dt><?
			?><dd>laat &lt;light&gt; zien wanneer de light site actief is, &lt;dark&gt; in het andere geval.</dd><?

			?><dt>{IF LANGUAGE=nl}&lt;Nederlands&gt;{ELSE}&lt;English&gt{ENDIF}</dt><?
			?><dd>laat &lt;Nederlands&gt; zien wanneer de taal op Nederlands staat, &lt;English&gt; in het andere geval.</dd><?

			?><dt>{IF SECURE}yes{ELSE}no{ENDIF}</dt><?
			?><dd>laat 'yes' zien op secure server, 'no' op reguliere</dd><?

			?><dt>{IF HAVEUSER}yes{ELSE}no{ENDIF}</dt><?
			?><dd>laat 'yes' zien als persoon geregistreerd lid is, anders 'no'</dd><?
		?></dl><?
	layout_stop_row();
	layout_close_table();
	layout_close_box();
	?></div><?*/

	/*if ($banner) {
		$link = db_single_assoc('bannerlink', '
			SELECT URL, OBJTYPE
			FROM bannerlink
			WHERE BANNERID = '.$bannerid
		);
		if ($link === false) {
			return;
		}
	}
	?><div id="linkpart"<?
	if ($hidden = !$bannerid || $banner['TYPE'] !== 'link') {
		?> class="hidden"<?
	}
	?>><?
	layout_open_box('banner');
	layout_open_table('fw');
	layout_start_row();
		echo Eelement_name('url');
		layout_field_value();
		?><input type="url" data-valid="working-url" name="LINK_URL" maxlength="255" value="<? if (isset($link)) echo escape_specials($link['URL']); ?>" /><?
	layout_restart_row();
		echo Eelement_name('type');
		layout_field_value();
		?><select name="LINK_TYPE"><?
			?><option<? if (isset($link) && $link['OBJTYPE'] === 'image') echo ' selected'; ?> value="image"><?= __('bannerlink:image'); ?></option><?
			?><option<? if (isset($link) && $link['OBJTYPE'] === 'flash') echo ' selected'; ?> value="flash"><?= __('bannerlink:flash'); ?></option><?
		?></select><?
	layout_stop_row();
	layout_close_table();
	layout_close_box();
	?></div><?*/

	/*if ($banner) {
		$script = db_single_assoc('bannerscript','
			SELECT SCRIPTURL, ANCHORURL, IMAGEURL
			FROM bannerscript
			WHERE BANNERID = '.$bannerid
		);
		if ($script === false) {
			return;
		}
	}
	?><div id="scriptpart"<?
	if (!$bannerid || $banner['TYPE'] !== 'script') {
		?> class="hidden"<?
	}
	?>><?
	layout_open_box('banner');
	?><p>Vul eventueel <b><tt>#UNIQUE#</tt></b> in op de plaats waar een uniek getal moet komen te staan!</p><?
	layout_open_table('fw');
	layout_start_row();
		echo Eelement_name('script_url');
		layout_field_value();
		?><input type="url" data-valid="url" name="SCRIPT_SCRIPTURL" maxlength="255" value="<? if (isset($script)) echo escape_specials($script['SCRIPTURL']); ?>" /><?
	layout_restart_row();
		echo Eelement_name('anchor_url');
		layout_field_value();
		?><input type="url" data-valid="url" name="SCRIPT_ANCHORURL" maxlength="255" value="<? if (isset($script)) echo escape_specials($script['ANCHORURL']); ?>" /><?
	layout_restart_row();
		echo Eelement_name('image_url');
		layout_field_value();
		?><input type="url" data-valid="url" name="SCRIPT_IMAGEURL" maxlength="255" value="<? if (isset($script)) echo escape_specials($script['IMAGEURL']); ?>" /><?
	layout_stop_row();
	layout_close_table();
	layout_close_box();
	?></div><?*/

	/*if ($bannerid && $banner['TYPE'] === 'textlinks') {
		$textlinks = db_single('bannertextlinks','SELECT TEXTLINKS FROM bannertextlinks WHERE BANNERID='.$bannerid);
		if ($textlinks === false) {
			return;
		}
	}
	?><div id="textlinkspart"<?
	if (!$bannerid || $banner['TYPE'] !== 'textlinks') {
		?> class="hidden"<?
	}
	?>><?
	layout_open_box('banner');
	layout_open_table('fw vtop');
	layout_start_row();
		echo Eelement_plural_name('textlink');
		layout_field_value('fw');
		?><textarea name="TEXTLINKS" cols="70" rows="20"><?
		if (isset($textlinks)) {
			echo escape_specials($textlinks);
		}
		?></textarea><?
	layout_restart_row();
		echo Eelement_name('information');
		layout_field_value();
		?><div class="block"><?
		?>Tekstlinks beslaan twee regels. Op de eerste regel staat de naam van de link, op de tweede regelijk het adres zelf.<br /><?
		?>Bijvoorbeeld:</div><div class="block"><?
		?><tt><?
		?>Bezoek de bekendste nieuws site van Nederland<br /><?
		?>https://www.nu.nl/<br /><?
		?><br /><?
		?>Klik hier voor alle informatie over feesten!<br /><?
		?>https://partyflock.<?= CURRENTDOMAIN ?>/<br /><?
		?></tt><?
		?></div><?
	layout_stop_row();
	layout_close_table();
	layout_close_box();
	?></div><?*/

	/*if ($bannerid && $banner['TYPE'] === 'ubb') {
		$ubbbody = db_single('bannerubb','SELECT BODY FROM bannerubb WHERE BANNERID='.$bannerid);
		if ($ubbbody=== false) {
			return;
		}
	}
	?><div id="ubbpart"<?
	if (!$bannerid || $banner['TYPE'] !== 'ubb') {
		?> class="hidden"<?
	}
	?>><?
	layout_open_box('banner');
	layout_open_table(TABLE_VTOP | TABLE_FULL_WIDTH);
	layout_start_row();
		echo Eelement_name('text');
		layout_field_value();
		?><div><textarea name="UBB_BODY" rows="5" cols="80" class="fw growToFit"><?
		if (!empty($ubbbody)) {
			echo escape_specials($ubbbody);
		}
		?></textarea></div><?
		?><div class="light"><?
		?>Voeg #COUNTCLICK# aan een element toe om de klik te tellen, bijvoorbeeld: [party:=200321%COUNTCLICK%]<?
		?></div><?
	layout_stop_row();
	layout_close_table();
	layout_close_box();
	?></div><?*/

	?><div class="block"><?
		?><input type="submit" value="<?= __($banner ? 'action:change' : 'action:add') ?>" name="COMMIT" /><?
	?></div><?
	?></form><?

	if ($banner && $tower) {
		?></td><td><? display_banner_innerbox($banner) ?></td></tr></table><?
	}
}

function banner_commit(): bool {
	require_once '_site.inc';
	require_once '_ticket.inc';

	if (!optional_number($_POST, 'RELATIONID')
	||	!require_anything_trim($_POST, 'NAME', utf8: true)
	#||	!require_element($_POST, 'TYPE', ['upload', 'iframe', 'script', 'link', 'html', 'textlinks', 'ubb', 'floormovie', 'multiparty'], strict: true)
	||	!require_idnumber($_POST, 'SIZETYPE')
	||	!optional_number($_POST, 'TICKETID')
	||	!require_anything_trim($_POST, 'BG')
	||	!empty($_POST['BG'])
	&&	!require_regex($_POST, 'BG','"^[a-fA-F\d]{6}$"')
	) {
		return false;
	}
/*	switch ($_POST['TYPE']) {
	case 'link':
		if (!require_anything_trim($_POST, 'LINK_URL', want_ascii: true)
		||	!require_element($_POST, 'LINK_TYPE', ['flash', 'image'], strict: true)
		) {
			return false;
		}
		[$width,$height] = banner_get_width_height($_POST['SIZETYPE']);
		$setlist[] = 'WIDTH='.$width;
		$setlist[] = 'HEIGHT='.$height;
		$setlist[] = 'SIZETYPE='.$_POST['SIZETYPE'];
		$extra_setlist['URL']			= 'URL="'.addslashes(strip_shy($_POST['LINK_URL'])).'"';
		$extra_setlist['OBJTYPE']		= 'OBJTYPE="'.$_POST['LINK_TYPE'].'"';
		break;

	case 'iframe':
		if (!require_anything_trim($_POST, 'IFRAME_URL', want_ascii: true)
		||	!require_banner_size($_POST, 'SIZETYPE')
		) {
			return false;
		}
		if (false === ($banner = $_REQUEST['sID'] ? db_single_assoc('banner', 'SELECT WIDTH, HEIGHT, SIZETYPE FROM banner WHERE BANNERID = '.$_REQUEST['sID']) : null)) {
			return false;
		}
		# only update WIDTH and HEIGHT if not set or sizetype differs
		if (!$banner['WIDTH']
		||	$banner['SIZETYPE'] !== $_POST['SIZETYPE']
		) {
			[$width, $height] = banner_get_width_height($_POST['SIZETYPE']);
			$setlist[] = 'WIDTH = '.$width;
			$setlist[] = 'HEIGHT = '.$height;
			$setlist[] = 'SIZETYPE = '.$_POST['SIZETYPE'];
		}
		$extra_setlist['URL'] = 'URL = "'.addslashes(strip_shy($_POST['IFRAME_URL'])).'"';
		break;

	case 'floormovie':
		$setlist[] = 'SIZETYPE='.BANNER_SIZE_INLINE;
		break;

	case 'upload':
*/		if (!empty($_FILES['UPLOAD_FILE'])
		&&	!empty($_FILES['UPLOAD_FILE']['name'])
		) {
			if (!require_file('UPLOAD_FILE')) {
				return false;
			}

			$tmp = $_FILES['UPLOAD_FILE']['tmp_name'];
			$swiffy = false;
			$video = false;

			require_once '_uploadimage.inc';

			switch ($_FILES['UPLOAD_FILE']['type']) {
			case 'video/mp4':
			case 'video/mpg':
			case 'video/mpeg':
				$video = true;
				[/* $rc */, $stdout /*, $stderr */] = execute('ffprobe -v error -show_entries stream=width,height -of default=noprint_wrappers=1 '.$tmp);
				if (!preg_match('"width=(?<width>\d+)\s*height=(?<height>\d+)"u', $stdout, $match)) {
					register_error('video:error:dimensions_of_video_not_found_LINE');
					return false;
				}
				$info = [(int)$match['width'], (int)$match['height']];
				break;
			default:
				if (!($info = uploadimage_file_info($tmp))) {
					register_error('banner:error:unable_to_determine_dimensions_LINE');
					return false;
				}
				if (isset($_POST['UPLOAD_TO_MP4'])) {
					$video = true;
					if ($info[2] === IMAGETYPE_GIF) {
						# NOTE: ffmpeg 5 cuts off the end of the gif.
						#		Setting the duration explicitly seems to fix this.
						[/* $rc */, $stdout /*, $stderr */] = execute('ffprobe -v error -show_entries stream=duration -of default=noprint_wrappers=1 '.$tmp);
						if (!preg_match('"duration=(?<duration>\d+(?:\.\d+)?)"u', $stdout, $match)) {
							register_error('video:error:dduration_of_video_not_found_LINE');
							return false;
						}
						$info['duration'] = (float)$match['duration'];
					}
				}
			}
			switch ($_POST['SIZETYPE']) {
			case BANNER_SIZE_FRONTFLYER:
				if (!$video) {
					if (!($mw = require_number_element($_POST, 'MAX_WIDTH', [200, 250, 300, 336]))) {
						return false;
					}
					[$width, $height] = $info;
					if ($width > $mw) {
						require_once '_execute.inc';
						scale($width, $height, $mw * 2, 0);

						$generate_sizes[true] = [$width, $height];

						[$width, $height] = $info;
						scale($width, $height, $mw, 0);
					}
					$generate_sizes[false] = [$width, $height];
					break;
				}
			default:
				if (!have_banner_size($_POST, 'SIZETYPE')) {
					[$width, $height] = $info;
					$banner = [
						'WIDTH'	=> $width,
						'HEIGHT'=> $height,
					];
					$_POST['SIZETYPE'] = get_banner_size($banner);
				} else {
					[$ww, $wh] = banner_get_width_height($_POST['SIZETYPE']);
					if ($swiffy) {
						$width = $ww;
						$height = $wh;
					} else {
						[$width, $height] = $info;
						if ($ww < $info[0]
						||	$wh < $info[1]
						) {
							[$width, $height] = $info;
							scale($width, $height, 2*$ww, 2*$wh);
							if ($video
							&&	$width & 1
							) {
								# ffmpeg needs width divisible by 2
								$width &= ~1;
							}
							$generate_sizes[true] = [$width, $height];

							[$width, $height] = $info;
							scale($width, $height, $ww, $wh);
							if ($video
							&&	$width & 1
							) {
								# ffmpeg needs width divisible by 2
								$width &= ~1;
							}
						}
					}
				}
				$generate_sizes[false] = [$width, $height];
				break;
			}
			$setlist[] = 'SIZETYPE = '.$_POST['SIZETYPE'];
			$setlist[] = 'WIDTH = '.$width;
			$setlist[] = 'HEIGHT = '.$height;

			if ($video) {
				require_once '_execute.inc';
				foreach ($generate_sizes as $x2 => [$width, $height]) {
					# NOTE: See https://trac.ffmpeg.org/wiki/Encode/H.264
					#		Good compression and no visible difference to the source:
					#		-crf 30
					#		Required to make the video play on iOS devices:
					#		See https://www.reddit.com/r/handbrake/comments/b8yzd3/i_cant_understand_what_encoder_level_is/
					#		-pix_fmt yuv420p
					#		-level 4.0
					#		Guide says to not use this setting:
					#		-profile:v baseline
					#		For streaming:
					#		-tune zerolatency
					#		-movflags faststart+empty_moov
					#		For regular videos not converted from GIF:
					#		-vf scale=$width:$height
					#		For GIFs converted to MP4, to make sure end-delay works and is not cut short
					#		-filter_complex 'color=c=white:s={$width}x$height:d=$duration [base]; [base][0:v] overlay'

					if (isset($info['duration'])) {
						$filter = "-filter_complex 'color=c=white:s={$width}x$height:d={$info['duration']} [base]; [base][0:v] overlay";
					} else {
						$filter = "-vf scale=$width:$height";
					}

					[/* $rc */, $stdout, /* $stderr */] = execute(<<<COMMAND
						ffmpeg \
							-i $tmp \
							-f mp4 \
							$filter \
							-c:v libx264 \
							-crf 30 \
							-tune zerolatency \
							-level 4.0 \
							-loglevel warning \
							-preset veryslow \
							-profile:v baseline \
							-pix_fmt yuv420p \
							-an \
							-movflags faststart+empty_moov \
							-
						COMMAND
					);
					if (!$stdout) {
						return false;
					}
					$blobs[$x2] = $stdout;
				}
				$filetype = 'mp4';
/*			} elseif ($swiffy) {
				$blobs = [
					false	=> $data,
					true	=> null
				];*/
			} elseif (
				!isset($generate_sizes)
			/*||	(	isset($filetype)
				&&	$filetype === 'swf')*/
			) {
				$blobs = [
					false	=> file_get_contents($tmp),
					true	=> null
				];
			} else {
				require_once '_imageprocessing.inc';
				if (!($info = process_images($generate_sizes, [
					'src'				=> $tmp,
					'info'				=> $info,
					'to_webp'			=> !empty($_POST['UPLOAD_TO_WEBP']),
					'webp_quality'		=> 75]))
				) {
					return false;
				}
				[$blobs, $filetype] = $info;
				if (!isset($blobs[true])) {
					$blobs[true] = null;
				}
				# keep original if it is smaller:
				foreach ($blobs as $x2 => &$data) {
					if (empty($data)) {
						continue;
					}
					if ($generate_sizes[$x2][0] === $info[0]
					&&	$generate_sizes[$x2][1] === $info[1]
					) {
						$origdata = file_get_contents($tmp);
						if (strlen($origdata) < strlen($data)) {
							$data = $origdata;
						}
					}
				}
				unset($data);
			}
			foreach ($blobs as $x2 => $data) {
				$x2 = $x2 ? '2x' : '';
				$extra_setlist['SIZE'.$x2]	= "SIZE$x2 	= ".(empty($data) ? 0 : strlen($data));
				$extra_setlist['CRC32'.$x2]	= "CRC32$x2	= ".(empty($data) ? 0 : crc32($data));
				$extra_setlist['DATA'.$x2]	= "DATA$x2	= '".(empty($data) ? '' : addslashes($data))."'";
			}
			$extra_setlist['FILETYPE'] = "FILETYPE = '$filetype'";
			if (empty($_POST['NAME'])) {
				$_POST['NAME'] = $_FILES['UPLOAD_FILE']['name'];
			}
		} else {
			if (!require_banner_size($_POST, 'SIZETYPE')) {
				return false;
			}
			$setlist[] = 'SIZETYPE = '.$_POST['SIZETYPE'];
		}
/*		break;

	case 'script':
		if (!require_working_url_or_none($_POST, 'SCRIPT_SCRIPTURL')
		||	!require_working_url_or_none($_POST, 'SCRIPT_ANCHORURL')
		||	!require_working_url_or_none($_POST, 'SCRIPT_IMAGEURL')
		) {
			return false;
		}
		if ($_POST['SCRIPT_IMAGEURL']
		&&	($info = @getimagesize($_POST['SCRIPT_IMAGEURL']))
		) {
			if (!have_banner_size($_POST, 'SIZETYPE')) {
				$banner = [
					'WIDTH'	=> $info[0],
					'HEIGHT'=> $info[1]
				];
				$_POST['SIZETYPE'] = get_banner_size($banner);
			}
			$setlist[] = 'SIZETYPE='.$_POST['SIZETYPE'];
			$setlist[] = 'WIDTH='.$info[0];
			$setlist[] = 'HEIGHT='.$info[1];

		} elseif (!require_banner_size($_POST, 'SIZETYPE')) {
			return false;
		} else {
			[$width, $height] = banner_get_width_height($_POST['SIZETYPE']);
			$setlist[] = 'WIDTH='.$width;
			$setlist[] = 'HEIGHT='.$height;
			$setlist[] = 'SIZETYPE='.$_POST['SIZETYPE'];
		}
		#$extra_setlist['SCRIPTURL'] = 'SCRIPTURL="'.addslashes(make_proper_site($_POST['SCRIPT_SCRIPTURL'],KEEP_ORIGINAL)).'"';
		#$extra_setlist['ANCHORURL'] = 'ANCHORURL="'.addslashes(make_proper_site($_POST['SCRIPT_ANCHORURL'],KEEP_ORIGINAL)).'"';
		#$extra_setlist['IMAGEURL']  = 'IMAGEURL="'.addslashes(make_proper_site($_POST['SCRIPT_IMAGEURL'],KEEP_ORIGINAL)).'"';
		$extra_setlist['SCRIPTURL'] = 'SCRIPTURL="'.addslashes($_POST['SCRIPT_SCRIPTURL']).'"';
		$extra_setlist['ANCHORURL'] = 'ANCHORURL="'.addslashes($_POST['SCRIPT_ANCHORURL']).'"';
		$extra_setlist['IMAGEURL']  = 'IMAGEURL="'.addslashes($_POST['SCRIPT_IMAGEURL']).'"';
		break;

	case 'html':
		if (!require_anything_trim($_POST, 'HTML_BODY', utf8: true)
		||	!require_anything_trim($_POST, 'SCRIPT_URL', want_ascii: true)
		) {
			return false;
		}
		[$width, $height] = banner_get_width_height($_POST['SIZETYPE'],$_POST['HTML_BODY']);
		$setlist[] = 'WIDTH='.$width;
		$setlist[] = 'HEIGHT='.$height;
		$setlist[] = 'SIZETYPE="'.$_POST['SIZETYPE'].'"';
		$extra_setlist['BODY']		= 'BODY="'.		addslashes(strip_shy($_POST['HTML_BODY'])).'"';
		$extra_setlist['SCRIPT_URL']	= 'SCRIPT_URL="'.	addslashes(strip_shy($_POST['SCRIPT_URL'])).'"';
		break;

	case 'textlinks':
		if (!require_anything_trim($_POST, 'TEXTLINKS')) {
			return false ;
		}
		[$width, $height] = banner_get_width_height($_POST['SIZETYPE']);
		$setlist[] = 'WIDTH='.$width;
		$setlist[] = 'HEIGHT='.$height;
		$setlist[] = 'SIZETYPE="'.$_POST['SIZETYPE'].'"';
		$extra_setlist['TEXTLINKS'] = 'TEXTLINKS="'.addslashes($_POST['TEXTLINKS']).'"';
		break;
	case 'ubb':
		if (!require_anything_trim($_POST, 'UBB_BODY')) {
			return false;
		}
		require_once '_ubb_preprocess.inc';
		[$width,$height] = banner_get_width_height($_POST['SIZETYPE']);
		$setlist[] = 'WIDTH='.$width;
		$setlist[] = 'HEIGHT='.$height;
		$setlist[] = 'SIZETYPE="'.$_POST['SIZETYPE'].'"';
		$extra_setlist['BODY'] = 'BODY="'.addslashes(_ubb_preprocess($_POST['UBB_BODY'])).'"';
		break;
	case 'multiparty':
		$setlist[] = 'SIZETYPE='.BANNER_SIZE_FRONTFLYER;
		if (!require_array($_POST, 'PARTYID')) {
			return false;
		}
		foreach ($_POST['PARTYID'] as $i => $partyid) {
			if (!is_number($partyid)) {
				continue;
			}
			$what = isset($_POST['WHAT'][$i]) && have_element($_POST['WHAT'],$i,['front','back','both']) ? $_POST['WHAT'][$i] : 'front';
			$partysetlist['('.$partyid.',"'.$what.'")'] = '(%BANNERID%,'.$partyid.','.CURRENTUSERID.','.CURRENTSTAMP.',"'.$what.'")';
		}
		break;
	}
*/
	$_POST['NAME'] = preg_replace('"(?P<sky>sky)(?P<craper>craper)"ui', '$1s$2', $_POST['NAME']);

	$setlist[] = 'BG='.(empty($_POST['BG']) && '' === $_POST['BG'] ? 'NULL' : '0x'.$_POST['BG']);
	$setlist[] = 'NAME="'.addslashes($_POST['NAME']).'"';
	# $setlist[] = 'TYPE="'.addslashes($_POST['TYPE']).'"';
	$setlist[] = 'TYPE="upload"';
	$setlist[] = 'RELATIONID = '.$_POST['RELATIONID'];
	if (isset($_POST['TICKETID'])) {
		$setlist[] = 'TICKETID='.$_POST['TICKETID'];
	}
	if ($bannerid = have_idnumber($_REQUEST,'sID')) {
		if (!db_insert('banner_log','
			INSERT INTO banner_log
			SELECT * FROM banner
			WHERE BANNERID='.$bannerid)
		) {
			return false;
		}
		$setlist[] = 'MSTAMP='.CURRENTSTAMP;
		$setlist[] = 'MUSERID='.CURRENTUSERID;
		if (!db_update('banner','
			UPDATE banner SET '.implode(',',$setlist).'
			WHERE BANNERID='.$bannerid)
		) {
			return false;
		}
		register_notice('banner:notice:changed_LINE');
	} else {
		$setlist[] = 'CSTAMP='.CURRENTSTAMP;
		$setlist[] = 'CUSERID='.CURRENTUSERID;
		if (!db_insert('banner','INSERT INTO banner SET '.implode(',',$setlist))) {
			return false;
		}
		$_REQUEST['sID'] = $bannerid = db_insert_id();

		register_notice('banner:notice:added_LINE');

		ticket_update('banner',$bannerid);
	}
/*	if (isset($partysetlist)) {
		if (!db_insert('bannermultiparty_log','
			INSERT INTO bannermultiparty_log
			SELECT *,'.CURRENTUSERID.','.CURRENTSTAMP.' FROM bannermultiparty
			WHERE BANNERID='.$bannerid.'
			  AND (PARTYID,WHAT) NOT IN ('.implodekeys(',',$partysetlist).')')
		||	!db_delete('bannermultiparty','
			DELETE FROM bannermultiparty
			WHERE BANNERID='.$bannerid.'
			  AND (PARTYID,WHAT) NOT IN ('.implodekeys(',',$partysetlist).')')
		||	!db_insert('bannertmultiparty','
			INSERT INTO bannermultiparty (BANNERID, PARTYID, MUSERID, MSTAMP, WHAT)
			VALUES '.str_replace('%BANNERID%', $bannerid, implode(', ', $partysetlist)).'
			ON DUPLICATE KEY UPDATE
				WHAT	= VALUES(WHAT),
				MUSERID	= IF(WHAT = VALUES(WHAT), MUSERID,  VALUES(MUSERID)),
				MSTAMP	= IF(WHAT = VALUES(WHAT), MSTAMP,  VALUES(MSTAMP))')
		) {
			return false;
		}
	}*/
	if (isset($extra_setlist)) {
		if (false === ($affected = db_single_int('banner'.$_POST['TYPE'],'SELECT 1 FROM banner'.$_POST['TYPE'].' WHERE BANNERID='.$bannerid,DB_USE_MASTER))) {
			return false;
		}
		if ($affected !== null) {
			if (!db_insert('banner'.$_POST['TYPE'].'_log','
				INSERT INTO banner'.$_POST['TYPE'].'_log
				SELECT * FROM banner'.$_POST['TYPE'].'
				WHERE NOT '.binary_equal($extra_setlist).'
				  AND BANNERID='.$bannerid)
			) {
				return false;
			}
			$affected = db_affected();
			$extra_setlist['MUSERID'] = 'MUSERID = '.CURRENTUSERID;
			$extra_setlist['MSTAMP']  = 'MSTAMP  = '.CURRENTSTAMP;
		}
		if ($affected || $affected === null) {
			if (!db_insupd('banner'.$_POST['TYPE'],'
				INSERT INTO banner'.$_POST['TYPE'].' SET
					BANNERID='.$bannerid.','.
					implode(',',$extra_setlist).'
				ON DUPLICATE KEY UPDATE '.value_parts($extra_setlist))
			) {
				return false;
			}
		}
	}
	if (isset($_POST['ORGTYPE'])) {
		if (!require_element($_POST, 'ORGTYPE', ['upload', 'iframe', 'script', 'link', 'html', 'textlinks', 'ubb', 'floormovie', 'multiparty'], true)) {
			return false;
		}
		if (($original_type = $_POST['ORGTYPE']) !== $_POST['TYPE']) {
			if ($original_type === 'multiparty') {
				$extra_fieldstr = ', '.CURRENTUSERID.', '.CURRENTSTAMP;
			} else {
				$extra_fieldstr = '';
			}
			if (!db_insert("banner{$original_type}_log", "
				INSERT INTO banner{$original_type}_log
				SELECT * $extra_fieldstr
				FROM banner$original_type
				WHERE BANNERID = $bannerid")
			||	!db_delete("banner$original_type", "
				DELETE FROM banner$original_type
				WHERE BANNERID = $bannerid")
			) {
				return false;
			}
		}
	}
	return true;
}
