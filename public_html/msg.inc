<?php

require_once '_directcontentlist.inc';
require_once '_directmessage.inc';
require_once '_promo.inc';

define('MSG_MARK_AMOUNT',10);

function preamble() {
	if (preg_match('"/conversationinfo/(\d+)"',$_SERVER['REQUEST_URI'],$match)) {
		header('Location: https://'.$_SERVER['HTTP_HOST'].'/msg/conversation/'.$match[1],true,301);
		exit;
	}
}
function msg_subsys_offline() {
	if (!file_exists('/msgoffline')) {
		return false;
	}
	$mtime = @filemtime('/msgoffline');
	if (!$mtime) {
		return false;
	}
	if (!empty($_POST)) {
		if ($mtime > CURRENTSTAMP - 120) {
			return false;
		}
		_error('Tijdelijk buiten gebruik voor onderhoud!');
	} else {
		warning('Tijdelijk buiten gebruik voor onderhoud!');
		if ($data = file_get_contents('/msgoffline')) {
			warning($data);
		}
	}
	return true;
}
function display_header() {
	if (have_user()
	&&	(	!$_REQUEST['ACTION']
		||	$_REQUEST['ACTION'] == 'incoming'
		)
	) {
		require_once '_feed.inc';
		show_feed('msg',FEED_HEADER,CURRENTUSERID);
/*		?><link rel="alternate" type="application/atom+xml" title="Partyflock ongelezen priv&eacute;berichten voor <?= escape_specials($GLOBALS['currentuser']->row['NICK']);
		?>" href="/feed/msg/<?= CURRENTUSERID;
		?>.xml" /><? */
	}
}
function display_title() {
	switch ($_REQUEST['ACTION']) {
	case 'incoming':
		$incoming = true;
	case 'outgoing':
		echo element_plural_name('directmessage');
		if ($userid = $_REQUEST['subID']) {
			echo isset($incoming) ? ' &larr; ' : ' &rarr; ';
			echo escape_specials(memcached_nick($userid));
		}
		break;
	case 'form':
		echo element_name('directmessage');
		if ($userid = $_REQUEST['subID'] ?: have_idnumber($_REQUEST,'USERID')) {
			?> &rarr; <? echo escape_specials(memcached_nick($userid));
		}
		break;
	default:
		if (!($userid = have_idnumber($_REQUEST,'USERID'))) {
			echo element_plural_name('directmessage');
			unset($_REQUEST['USERID']);
			return;
		}
		echo element_name('directmessage',have_idnumber($_REQUEST,'AMOUNT'));
		if ($nick = memcached_nick($userid)) {
			?> &larr; <?
			echo escape_specials($nick);
		}
	}
}
function perform_commit() {
	switch ($_REQUEST['ACTION']) {
	case 'commit':				return msg_perform_commit();
	case 'markread':			return msg_mark_read();
	case 'markpromoread':		return promo_letter_alter(PROMO_SET_READ);
	case 'markpromoseen':		return promo_letter_alter(PROMO_SET_SEEN);
	case 'keeppromoread':		return promo_letter_alter(PROMO_SET_KEEP | PROMO_SET_READ);
	case 'keeppromoseen':		return promo_letter_alter(PROMO_SET_KEEP | PROMO_SET_SEEN);
	case 'removereadpromo':		return promo_letter_alter(PROMO_SET_REMOVE | PROMO_SET_READ);
	case 'removeseenpromo':		return promo_letter_alter(PROMO_SET_REMOVE | PROMO_SET_SEEN);
	case 'answercommit':		return msg_answer_commit();
	case 'delete':				return msg_delete_single();
	case 'remallread':			return msg_remove_all_read();
	case 'remalloutg':			return msg_remove_all_outgoing();
	case 'markallread':			return msg_mark_all_read();
	case 'markunreadoutgoing':	return msg_mark_unread_outgoing();
	case 'fix':
		if ($_REQUEST['subID']) {
			require_once '_fixconversations.inc';
			fix_all_conversations($_REQUEST['subID']);
		}
		break;
	}
}
function display_body() {
	if (msg_subsys_offline()) {
		return;
	}
	switch ($_REQUEST['ACTION']) {
	case 'commit':
		if (isset($_POST['PREVIEW'])) {
			msg_display_preview();
		//} elseif (empty($GLOBALS['commitresult'])) {
		//	msg_display_form();
		} elseif (!msg_display_incoming()) {
			msg_display_overview();
		}
		break;

	default:
	case 'keeppromo':
	case 'removepromo':
	case 'markpromoread':
	case 'delete':
	case 'conversations':
	case 'remallread':
	case 'remalloutg':
	case 'markallread':
	case 'markunreadoutgoing':	return msg_display_overview();
	case 'single':
		if ($_REQUEST['sID']
		&&	empty($_SERVER['eDATA'])
		) {
			msg_display_single();
			break;
		}

	case null:
			empty($_POST)
		?	msg_display_overview()
		:	(	empty($GLOBALS['commitresult'])
			?	msg_display_overview()
			:	msg_display_overview()
			);
		return;

	case 'answerform':
		msg_display_answerform();
		return;

	case 'answercommit':
			isset($_POST['REMOVE'])
		?	msg_display_overview()
		:	msg_display_answerform();
		return;

	case 'conversation':		return msg_display_conversation();
	case 'outgoing':		return msg_display_outgoing();
	case 'incoming':		return msg_display_incoming();
	case 'form':			return msg_display_form();
	case 'markread':
		if (!msg_display_incoming()) {
			msg_display_overview();
		}
		break;

	case 'remconv':			return msg_remove_conversation();
#	case 'conversationinfo':	return msg_display_conversation_info();
	case 'specialops':		return msg_display_specialops();
	}
}

function mark_msgs($qstr) {
	$qstr .= ' LIMIT '.MSG_MARK_AMOUNT;
	while (($res = db_update('directmessage',$qstr))) {
		if (db_affected() != MSG_MARK_AMOUNT) {
			break;
		}
	}
	return $res;
}
function msg_display_preview() {
	if (!($nick = require_nick($_POST,'TO_USERID'))) {
		return;
	}
	require_once '_ubb_preprocess.inc';
	require_once '_commentobject.inc';
	layout_open_section_header();
	echo Eelement_name('directmessage') ?> &rarr; <? echo get_element_link('user',$_POST['TO_USERID'],$nick);
	layout_close_section_header();

	if ($reply_to = have_idnumber($_REQUEST,'REPLY_TO')) {
		$_POST['REPLY_TO'] = $reply_to;
		$msg = _directmessage_query($reply_to);
		if ($msg) {
			// set MESSAGEID to 0 to disallow 'removal' link
			$msg['NOCHANGE'] = true;
#			_directmessage_display($msg);
			comment_display('directmessage',null,$msg);
		}
	}
	$row = [
		'USERID'	=> CURRENTUSERID,
		'CSTAMP'	=> CURRENTSTAMP,
		'BODY'		=> _ubb_preprocess($_POST['BODY'] ?? '', true),
		'FLAGS'		=> 0,
#		'NICK'		=> $GLOBALS['currentuser']->row['NICK'],
#		'STATUS'	=> $GLOBALS['currentuser']->row['STATUS'],
		'NOCHANGE'	=> true,
		'MESSAGEID'	=> 0
	];
	comment_display('directmessage',null,$row,CMTFLG_PREVIEW);
	_directmessage_display_form($_POST);
}
function msg_display_conversation_info(): void {
	if (!require_user()
	||	!($userid = require_idnumber($_REQUEST,'subID'))
	||	!($user = memcached_user($userid))
	||	!($color = memcached_color($userid))
	) {
		return;
	}
	layout_show_section_header(Eelement_name('conversation_information'));
	layout_open_box($color);
	layout_open_box_header();
	echo get_element_link('user', $userid, $user['NICK']);
	layout_close_box_header();
	?><div class="block"><a href="/msg/conversation/<?= $userid ?>"><?= __C('action:view_conversation') ?></a></div><?
	layout_close_box();
}
function msg_answer_commit() {
	if (!require_user()) {
		return;
	}
	if (isset($_POST['REMOVE'])) {
		if (!db_insert('answeringmachine_log','
			INSERT INTO answeringmachine_log
			SELECT * FROM answeringmachine
			WHERE USERID='.CURRENTUSERID)
		||	!db_delete('answeringmachine','
			DELETE FROM answeringmachine
			WHERE USERID='.CURRENTUSERID)
		) {
			return;
		}
		register_notice('answeringmachine:removed_and_deactivated_LINE');
		return;
	}
	if (!require_anything_trim($_POST,'BODY')) {
		return;
	}
	require_once '_ubb_preprocess.inc';
	if (!db_insert('answeringmachine_log','
		INSERT INTO answeringmachine_log
		SELECT * FROM answeringmachine
		WHERE USERID='.CURRENTUSERID)
	||	!db_insupd('answeringmachine','
		INSERT INTO answeringmachine SET
			USERID	='.CURRENTUSERID.',
			BODY	="'.addslashes($body = _ubb_preprocess($_POST['BODY'], utf8: true)).'",
			ACTIVE	='.(isset($_POST['ACTIVE']) ? '1' : '0').',
			PHOTO	='.(isset($_POST['PHOTO']) ? '1' : '0').',
			MSTAMP	='.CURRENTSTAMP.',
			MUSERID	='.CURRENTUSERID.',
			ACCEPTED=1
		ON DUPLICATE KEY UPDATE
			BODY	=VALUES(BODY),
			ACTIVE	=VALUES(ACTIVE),
			PHOTO	=VALUES(PHOTO),
			MSTAMP	=VALUES(MSTAMP),
			MUSERID	=VALUES(MUSERID),
			ACCEPTED=VALUES(ACCEPTED)')
	) {
		return;
	}
	taint_include_cache('answeringmachine',CURRENTUSERID,$body);
	register_notice(
		isset($_POST['ACTIVE'])
	?	'answeringmachine:notice:changed_and_activated_LINE'
	:	'answeringmachine:notice:changed_and_deactivated_LINE'
	);
}

function msg_display_answerform() {
	require_once '_answeringmachine.inc';

	if (!require_user()
	||	!($color = memcached_color(CURRENTUSERID))
	) {
		return;
	}

	layout_show_section_header(Eelement_name('answeringmachine'));

	$spec = explain_table('answeringmachine');

	$answer = answeringmachine_show(CURRENTUSERID);
	if ($answer === false) {
		return;
	}

	layout_open_box('white');
	?><div class="block"><?= __('answeringmachine:info:basic_information_TEXT', DO_NL2BR) ?></div><?
	?><form<?
	?> accept-charset="utf-8"<?
	?> onsubmit="return submitForm(this)"<?
	?> method="post"<?
	?> action="/msg/answercommit"><?

	?><div class="block"><?
	?><textarea cols="80" rows="15" name="BODY" maxlength="<?= $spec['BODY']->maxlength ?>"><?
	if ($answer) {
		echo escape_utf8($answer['BODY']);
	}
	?></textarea></div><?
	?><div class="block"><?
	?><label for="active"><input id="active" type="checkbox" value="1" name="ACTIVE"<?
	if (!$answer
	||	$answer['ACTIVE']
	) {
		?> checked<?
	}
	?>> <?= __C('action:turn_on_answering_machine') ?></label><br /><label for="photo"><input<?
	if (!$answer
	||	$answer['PHOTO']
	) {
		?> checked<?
	}
	?> type="checkbox" id="photo" value="1" name="PHOTO"> <?= __C('attrib:include_profile_image') ?></label><?
	?></div><?
	layout_close_box();

	?><div class="right"><?
	?><span style="float:left;"><input type="submit" value="<?= __('action:change'); ?>" name="CHANGE" /></span><?
	?><input type="submit" value="<?= __('action:remove'); ?>" name="REMOVE" /><?
	?></div><?

	?></form><?
}
function msg_delete_single() {
	if (!require_user()
	||	!require_post()
	||	!($messageid = require_idnumber($_REQUEST,'sID'))
	||	!require_referer('/msg')
	) {
		return;
	}
	$message = db_single_assoc('directmessage','
		SELECT FROM_USERID,TO_USERID
		FROM directmessage
		WHERE MESSAGEID='.$messageid,
		DB_USE_MASTER
	);
	if ($message === false) {
		return;
	}
	if (!$message) {
		register_error('directmessge:error:nonexistent_LINE',array('ID'=>$messageid));
		return;
	}
	if (CURRENTUSERID == $message['TO_USERID']) {
		if (!db_update('directmessage','
			UPDATE directmessage SET
				READM		=1,
				TO_DELETED	=1
			WHERE (READM=0 OR TO_DELETED=0)
			  AND MESSAGEID='.$messageid.'
			  AND TO_USERID='.CURRENTUSERID)
		) {
			return;
		}
		_currentuser::flush_dmsgunread(CURRENTUSERID);
	} elseif (CURRENTUSERID == $message['FROM_USERID']) {
		if (!db_update('directmessage','
			UPDATE directmessage SET
				FROM_DELETED=1
			WHERE MESSAGEID='.$messageid.'
			  AND FROM_DELETED=0
			  AND FROM_USERID='.CURRENTUSERID)
		) {
			return;
		}
	} else {
		# not receiver nor sender
		return;
	}
	require_once '_commentkeep.inc';
	if (kept_comment('directmessage',$messageid)) {
		keep_comment('directmessage',$messageid,'unkeep');
	}
	log_special_op('remove_single',$messageid);
	register_notice('msg:notice:removed_LINE');
	require_once '_fixconversations.inc';
	fix_single_conversation(CURRENTUSERID,$message[$message['FROM_USERID'] == CURRENTUSERID ? 'TO_USERID' : 'FROM_USERID']);
}
function msg_mark_all_read() {
	require_once 'defines/directmessage.inc';
	if (!require_user()
	||	!require_post()
	||	!require_referer('/msg/specialops')
	||	!require_valid_origin_or_secret('markallread:'.CURRENTUSERID)
	) {
		return;
	}
	$reads = db_simpler_array('directmessage','
		SELECT MESSAGEID
		FROM directmessage
		WHERE READM=0
		  AND TO_USERID='.CURRENTUSERID
	);
	if ($reads === false) {
		return;
	}
	if ($reads) {
		if (!db_update('directmessage','
			UPDATE directmessage SET READM=1,COMPRESSED=COMPRESSED|'.DMSG_READ.'
			WHERE MESSAGEID IN ('.implode(',',$reads).')
			  AND READM=0
			  AND TO_USERID='.CURRENTUSERID)
		) {
			return;
		}
	}
	log_special_op('mark_all_incoming_read');
	_currentuser::flush_dmsgunread(CURRENTUSERID);
	$GLOBALS['currentuser']->unread_messages(0);
	_notice('Alle inkomende berichten zijn als gelezen gemarkeerd.');
}
function msg_remove_all_read() {
	require_once '_fixconversations.inc';
	if (!require_user()
	||	!require_post()
	||	!require_valid_origin_or_secret('remallread:'.CURRENTUSERID)
	||	!require_referer('/msg/specialops')
	||	!require_getlock($lock = 'remallread_'.CURRENTUSERID)
	) {
		return;
	}
	foreach (['TO','FROM'] as $dir) {
		$deletes = db_simpler_array('directmessage','
			SELECT MESSAGEID
			FROM directmessage
			WHERE READM=1
			  AND '.$dir.'_DELETED=0
			  AND '.$dir.'_USERID='.CURRENTUSERID
		);
		if (!$deletes) {
			continue;
		}
		if (!db_update('directmessage','
			UPDATE directmessage SET '.$dir.'_DELETED=1
			WHERE MESSAGEID IN ('.implode(',',$deletes).')
			  AND '.$dir.'_DELETED=0
			  AND '.$dir.'_USERID='.CURRENTUSERID)
		) {
			continue;
		}
	}
	if (!fix_all_conversations(CURRENTUSERID)) {
		return;
	}
	log_special_op('remove_all_read');
	db_releaselock($lock);
	_currentuser::flush_dmsgunread(CURRENTUSERID);
	_notice('Alle gelezen berichten zijn verwijderd.');
}
function msg_remove_all_outgoing() {
	require_once '_fixconversations.inc';
	if (!require_user()
	||	!require_post()
	||	!require_valid_origin_or_secret('remalloutg:'.CURRENTUSERID)
	||	!require_referer('/msg/specialops')
	||	!require_getlock($lock = 'remalloutg_'.CURRENTUSERID)
	) {
		return;
	}
	$deletes = db_simpler_array('directmessage','
		SELECT MESSAGEID
		FROM directmessage
		WHERE READM=0
		  AND FROM_DELETED=0
		  AND FROM_USERID='.CURRENTUSERID
	);
	if ($deletes === false) {
		return;
	}
	if ($deletes) {
		if (!db_update('directmessage','
			UPDATE directmessage SET FROM_DELETED=1
			WHERE MESSAGEID IN ('.implode(',',$deletes).')
			  AND READM=0
			  AND FROM_DELETED=0
			  AND FROM_USERID='.CURRENTUSERID)
		) {
			return;
		}
	}
	if (!fix_all_conversations(CURRENTUSERID)) {
		return;
	}
	log_special_op('remove_all_outgoing');
	db_releaselock($lock);
	_notice('Alle ongelezen uitgaande berichten zijn verwijderd.<br />Het kan zijn dat de link &quot;Ongelezen uitgaand&quot; niet direct verdwijnt, dit kan even duren omdat de database wordt bijgewerkt.');
}
function msg_mark_unread_outgoing() {
	if (!require_user()
	||	!require_post()
	||	!require_valid_origin_or_secret('markunreadoutgoing:'.CURRENTUSERID)
	||	!db_insupd('unreadstartstamp','
		INSERT INTO unreadstartstamp SET
			USERID	='.CURRENTUSERID.',
			STAMP	='.CURRENTSTAMP.'
		ON DUPLICATE KEY UPDATE STAMP=VALUES(STAMP)')
	) {
		return;
	}
	log_special_op('mark_unread_outgoing');
	_notice('Alle ongelezen uitgaande berichten tot '._date_get(CURRENTSTAMP).' worden niet meer getoond.');
}
function msg_remove_conversation() {
	if (!require_user()
	||	!require_post()
	||	!($nick = require_nick($_REQUEST,'USERID'))
	||	!require_valid_origin_or_secret('remconv:'.CURRENTUSERID.':'.$_REQUEST['USERID'])
	) {
		return;
	}
	layout_open_section_header();
	?>Conversatie met <?= get_element_link('user',$_REQUEST['USERID'],$nick); ?> verwijderen<?
	layout_close_section_header();


	if (!require_post()
	||	!require_referer_regex('/msg/conversation/'.$_REQUEST['USERID'].'\b')
	||	!require_getlock($lockname = 'removeconv_'.CURRENTUSERID.'_'.$_REQUEST['USERID'])
	) {
		return;
	}
	foreach ([
		['=1',		'TO','FROM'],
		[' IN (0,1)',	'FROM','TO'],
	] as $info) {
		[$readm,$dir1,$dir2] = $info;
		$msgs = db_simpler_array('directmessage','
			SELECT MESSAGEID
			FROM directmessage
			WHERE READM '.$readm.'
			  AND '.$dir1.'_DELETED=0
			  AND '.$dir1.'_USERID='.CURRENTUSERID.'
			  AND '.$dir2.'_USERID='.$_REQUEST['USERID']
		);
		if (!$msgs) {
			continue;
		}
		if (!db_update('directmessage','
			UPDATE directmessage SET '.$dir1.'_DELETED=1
			WHERE MESSAGEID IN ('.implode(',',$msgs).')
			  AND READM '.$readm.'
			  AND '.$dir1.'_DELETED=0
			  AND '.$dir1.'_USERID='.CURRENTUSERID.'
			  AND '.$dir2.'_USERID='.$_REQUEST['USERID'])
		) {
			continue;
		}
	}
	log_special_op('remove_conversation',$_REQUEST['USERID']);
	$haveleft = db_single('directmessage','
		SELECT 1
		FROM directmessage
		WHERE READM=0
		  AND TO_DELETED=0
		  AND FROM_USERID='.$_REQUEST['USERID'].'
		  AND TO_USERID='.CURRENTUSERID.'
		LIMIT 1'
	);
	if ($haveleft !== false
	&&	!$haveleft
	) {
		if (!db_delete('conversation_v2','
			DELETE FROM conversation_v2
			WHERE I_USERID='.CURRENTUSERID.'
			  AND WITH_USERID='.$_REQUEST['USERID'])
		) {
			return;
		}
	}
	?><p>Alle gelezen berichten in je conversatie met <?= get_element_link('user',$_REQUEST['USERID'],$nick); ?> zijn verwijderd!</p><?
	db_releaselock($lockname);
}
function msg_mark_read() {
	if (!require_user()
	||	!require_idnumber($_REQUEST,'sID')
	) {
		return;
	}
	directmessage_mark_read($_REQUEST['sID'],CURRENTUSERID);
}
function msg_menu($userid,$part = 0) {
	$inconversation = $_REQUEST['ACTION'] == 'conversation';
	if (!$part || $part == 1) {
		layout_open_menu();
		layout_menuitem(Eelement_name('overview'),isset($_REQUEST['ALL']) ? '/msg/conversations' : '/msg');
		layout_menuitem(Eelement_name('conversation'),'/msg/conversation/'.$userid,$inconversation);
		layout_close_menu();
	}
	if (!$part || $part == 2) {
		$maysend = may_send_directmessage($userid,CURRENTUSERID);

		if (!$maysend && !$inconversation) {
			return;
		}
		layout_open_menu();
		if ($maysend) {
			layout_menuitem(__C('action:write_message_back'),'/msg/form/'.$userid);//.'#inputbody');
		}
		if ($inconversation) {
			layout_continue_menu();
			layout_open_menuitem();
			with_confirm(
				__C('action:remove_conversation'),
				'/msg/remconv?USERID='.$_REQUEST['subID'],
				__('msg:confirm:conversation_removal_LINE')
			);
			layout_close_menuitem();
		}
		layout_close_menu();
	}
}
function msg_display_conversation() {
	if (!require_user()
	||	!require_idnumber($_REQUEST,'subID')
	) {
		return;
	}
	if (!($userid = $_REQUEST['subID'])) {
		$nick = element_name('employee');
	} elseif (!($nick = require_nick($_REQUEST,'subID'))) {
		return;
	}
	require_once '_directcontentlist.inc';
/*	layout_open_section_header();
	echo Eelement_name('conversation');
	if ($nick) {
		?> <?= MIDDLE_DOT_ENTITY ?> <? echo
	get_element_link('user',$_REQUEST['subID'],$nick); }
	layout_close_section_header();*/
	layout_show_section_header(Eelement_name('conversation'));//Eelement_plural_name('real_message'));

	// display complete conversation

	msg_menu($userid,1);

	layout_open_box('white');
	layout_box_header(get_element_link('user',$userid),is_online($userid) ? 'online' : null);
/*	require_once '_profileimage.inc';
	if ($img = _profileimage_get($userid)) {
		?><div class="r block"><?= $img ?></div><?
	}*/
	$info = memcached_single_assoc('directmessage','
		SELECT	COUNT(IF(FROM_USERID='.CURRENTUSERID.',1,NULL)) AS FROM_ME,
			COUNT(*) AS TOTAL,
			MAX(IF(FROM_USERID='.CURRENTUSERID.',CSTAMP,NULL)) AS LAST_SENT,
			MAX(IF(TO_USERID='.CURRENTUSERID.',CSTAMP,NULL)) AS LAST_RECEIVED
		FROM directmessage
		WHERE READM IN (0,1) AND FROM_DELETED=0 AND FROM_USERID='.CURRENTUSERID.	 ' AND TO_USERID='.$_REQUEST['subID'].'
		   OR READM IN (0,1) AND   TO_DELETED=0 AND FROM_USERID='.$_REQUEST['subID'].' AND TO_USERID='.CURRENTUSERID,
		TEN_MINUTES
	);
	if ($info) {
		layout_open_table();
		//SENT
		layout_start_row();
		echo __C('status:sent');
		layout_field_value_right(null,null,' &rarr;');
		?><b><?= $info['FROM_ME'] ?></b><?
		layout_next_cell();
		if ($info['LAST_SENT']) {
			?>, <?= __('field:last') ?>&nbsp;<?
			layout_next_cell(class: 'right');
			_datedaytime_display($info['LAST_SENT']);
		}

		// RECEIVED
		layout_restart_row();
		echo __C('attrib:received');
		layout_field_value_right(null,null,' &larr;');
		?><b><?= $info['TOTAL'] - $info['FROM_ME'] ?></b><?
		layout_next_cell();
		if ($info['LAST_RECEIVED']) {
			?>, <?= __('field:last') ?>&nbsp;<?
			layout_next_cell(class: 'right');
			_datedaytime_display($info['LAST_RECEIVED']);
		}
		layout_stop_row();

		layout_close_table();
	}
	layout_close_box();

	msg_menu($userid,2);

	$directcontentlist = new _directcontentlist;

	if ($directcontentlist->query_conversation($_REQUEST['subID'])) {
		$directcontentlist->display();
	}
}
function msg_display_overview() {
	if (!require_user()) {
		return;
	}

	require_once '_directmessagelist.inc';
	require_once '_promolist.inc';
	layout_show_section_header(Eelement_plural_name('directmessage'));

	$haveoutg =
		!setting('HIDE_UNREAD_OUTGOING')
	&&	false !== ($startfrom = memcached_single('unreadstartstamp','SELECT STAMP FROM unreadstartstamp WHERE USERID='.CURRENTUSERID,300))
	?	memcached_single('directmessage','
			SELECT 1
			FROM directmessage
			WHERE READM=0
			  AND FROM_DELETED=0
			  AND FROM_USERID='.CURRENTUSERID.
			($startfrom ? ' AND CSTAMP>'.$startfrom : null).'
			LIMIT 1',
			TEN_MINUTES
		)
	:	false;
	$haveconv = memcached_single('conversation_v2','SELECT 1 FROM conversation_v2 WHERE I_USERID='.CURRENTUSERID.' LIMIT 1');
	if ($haveconv === false) {
		return;
	}

	layout_open_menu();
	layout_menuitem(__C('msg:header:unread_incoming'),'/msg',!$_REQUEST['ACTION']);
	global $currentuser;
	if ($amount = $currentuser->unread_messages()) {
		?> (<?= $amount ?>)<?
	}
	if ($haveoutg) {
		layout_menuitem(__C('msg:header:unread_outgoing'),'/msg/showoutgoing',$haveoutg = $_REQUEST['ACTION'] == 'showoutgoing');
	}
	if ($haveconv) {
		layout_menuitem(Eelement_plural_name('conversation'),'/msg/conversations',$haveconv = $_REQUEST['ACTION'] == 'conversations');
	}
	$haveinactive = _promolist_display_inactive();

	$show_promos = true;

	layout_continue_menu();
	layout_next_menuitem();
	require_once '_feed.inc';
	if (SHOW_FEED_ICON) {
		show_feed('msg',FEED_ICON,CURRENTUSERID);
	}
	layout_close_menuitem();

	layout_close_menu();
	layout_open_menu();
	layout_continue_menu();
 	layout_menuitem(Eelement_plural_name('email_notification'),'/user/'.CURRENTUSERID.'/settings#notifications');
 	layout_menuitem(Eelement_plural_name('special_operation'),'/msg/specialops',$_REQUEST['ACTION'] == 'specialops');

	$ainfo = db_single_assoc('answeringmachine','
		SELECT ACCEPTED, ACTIVE
		FROM answeringmachine
		WHERE USERID = '.CURRENTUSERID
	);
	if ($ainfo !== false) {
		layout_open_menuitem();
		?><a href="/msg/answerform"><?= Eelement_name('answeringmachine'); ?></a> <?
		if ($ainfo) {
			if (!$ainfo['ACCEPTED']) {
				?><span class="invalid"> = <?= __('field:unaccepted'); ?></span><?
			} elseif ($ainfo['ACTIVE']) {
				?> = <?= __('status:on') ?><?
			} else {
				?><span class="light"> = <?= __('status:off') ?></span><?
			}
		} else {
			?><span class="light"> = <?= __('status:off') ?></span><?
		}
	}
	layout_close_menu();

	include_js('js/quote');
	include_js('js/form/growtofit');

	if ($show_promos
	&&	($manyunread = memcached_single('directmessage','
			SELECT COUNT(DISTINCT FROM_USERID)
			FROM directmessage
			WHERE READM=0
			  AND TO_USERID='.CURRENTUSERID,
			HALF_HOUR,
			'unreadgroup_'.CURRENTUSERID.'_'.$GLOBALS['currentuser']->unread_messages())
			> 5
		)
	) {
		_promolist_display_active();
	}

	if (!$haveconv && !$haveoutg) {
		layout_open_box('white');
		layout_box_header(__C('msg:header:unread_incoming'));

		$directmessagelist = new _directmessagelist;
		$amount = $directmessagelist->query_incoming() ? $directmessagelist->amount() : 0;
		if ($amount) {
			$directmessagelist->display();
		}
		layout_close_box();
	}

	if ($haveoutg) {
		layout_open_box('white');
		layout_box_header(__C('msg:header:unread_outgoing'));
		$directmessagelist = new _directmessagelist;
		$directmessagelist->start_from($startfrom);
		if ($directmessagelist->query_outgoing()) {
			$directmessagelist->display();
		}
		layout_close_box();
	}

	if ($haveconv) {

		foreach ([1,0] as $active) {
			require_once '_userlist.inc';
			$userlist = new _userlist;
			$userlist->have_conversation();
			$active ? $userlist->only_active() : $userlist->only_nonactive();
			$userlist->show_invisible = true;
			$userlist->show_camera = true;
			$userlist->show_heart = true;
			$userlist->specialurl = '/msg/conversation/';
			$userlist->query();
			if ($userlist->count()) {
				$counts[$active] = [
					$userlist,
					$userlist->count(),
					$active ? 'active_user' : 'non_active_user'
				];
			}
		}
		if (!empty($counts)) {
			include_js('js/conversations');
			layout_open_box('white','conversations');
			layout_box_header(Eelement_plural_name('conversation'));

			$i = 0;
			foreach ($counts as $active => $info) {
				[$data,$cnt,$usertype] = $info;
				?><div class="block"><?
				?><span id="convclk<?= $active ?>"<?
					?> onclick="Pf.clickConversationType(this)"<?
					?> data-actives="<?= $active ? 1 : 0 ?>"<?
					?> class="<?= $i ? 'ptr' : 'selected' ?>"><?= Eelement_name($usertype,$cnt) ?></span> (<?= $cnt ?>)<?
				?></div><?
				++$i;
			}
			layout_close_box();

			$i = 0;
			foreach ($counts as $active => $info) {
				[$userlist,$cnt,$usertype] = $info;
				layout_open_box(($i ? 'hidden ' : null).'white',($active ? 'active' : 'inactive').'_conversations');
				layout_box_header(Eelement_plural_name('conversation').' '.MIDDLE_DOT_ENTITY.' '.element_name($usertype,$cnt));
				$userlist->display();
				layout_close_box();
				++$i;
			}
		}
	}
	if ($show_promos) {
		if (!$manyunread) {
			_promolist_display_active();
		}
#		_promolist_display_kept();
	}
}
function msg_display_specialops() {
	if (!require_user()) {
		return;
	}
	layout_section_header(Eelement_plural_name('special_operation'));

	?><p>Omdat veel mensen niet weten hoe ze met de toenemende informatie stroom in deze snelle wereld om moeten gaan, staan hieronder <?
	?>voor hen een aantal handige functies!</p><?
	?><dl><?
	// HIDE UNREAD OUTGOING
	?><dt><i><?
	with_confirm(
		__C('action:clear_unread_outgoing'),
		'/msg/markunreadoutgoing',
		__('msg:confirm:clear_unread_outgoing_LINE')
	);
	?></i></dt><?
	?><dd>Hiermee leeg je als het ware je 'Ongelezen uitgaand' lijst. Als je nu wat verstuurt, zie je dit weer wel <?
	?>in de lijst verschijnen, in tegenstelling tot de optie hieronder, die er voor zorgt dat je de link en lijst nooit ziet.</dd><?

	// MARK ALL INCOMING READ
	?><dt><b><?
	with_confirm(
		__C('action:mark_all_incoming_messages_as_read'),
		'/msg/markallread',
		__('msg:confirm:mark_all_read_LINE'),
	);
	?></b><dt><?
	?><dd>Als je echt niet meer weet wat je met al die berichten aan moet, heb je je plicht als sociaal wezen zo verschrikkelijk <?
	?>verzaakt. Dan kun je met deze operatie alle berichten die nu nog als ongelezen staan, allemaal als gelezen markeren.</dd><?

	// REMOVE ALL READ MESSAGES
	?><dt><b><?
	with_confirm(
		__C('action:remove_all_read_messages'),
		'/msg/remallread',
		__('msg:confirm:remove_all_read_LINE')
	);
	?></b></dt><?
	?><dd>Met deze operatie verwijder je in een keer alle gelezen berichten. Indien je nog ongelezen berichten van iemand hebt, dan <?
	?> blijven die staan, de rest van jullie conversatie wordt verwijderd.</dd><?

	// REMOVE ALL UNREAD OUTGOING
	?><dt><b><?
	with_confirm(
		__C('action:remove_all_unread_outgoing_messages'),
		'/msg/remalloutg',
		__('msg:confirm:remove_all_unread_outgoing_LINE')
	);
	?></b><dt><?
	?><dd>Verwijdert alle ongelezen uitgaande berichten uit jouw lijst. Houd er rekening mee dat de berichten wel gewoon verzonden zijn, <?
	?>dus nog wel gelezen kunnen worden door de geadresseerde.</dd><?

	?></dl><?
}
function msg_display_incoming() {
	if (!require_user()
	||	!require_nick($_REQUEST,'subID')
	||	!optional_number($_REQUEST,'AMOUNT')
	) {
		return false;
	}
	require_once '_directcontentlist.inc';
	$directcontentlist = new _directcontentlist;
	if (!$directcontentlist->query_to(CURRENTUSERID,$_REQUEST['subID'])) {
		return false;
	}
	layout_show_section_header(Eelement_plural_name('incoming_directmessage'), no_link: true);

	msg_menu($_REQUEST['subID']);

	$directcontentlist->display();

	include_js('js/earliermsg');
	include_js('js/quote');
	include_js('js/form/growtofit');

	?><div id="insertbefore"><input type="hidden" id="firstmessageid" value="<?= $directcontentlist->get_first_messageid(); ?>" /></div><?

 	layout_open_menu(0, 'clear');
	layout_open_menuitem();
	?><span<?
	?> data-no-more="<?= __('msg:info:there_are_no_more_messages_LINE') ?>"<?
	?> id="addmsg"<?
	?> class="unhideanchor"<?
	?> onclick="importEarlierMessage(this)"><?= __C('action:see_previous_message_in_conversation') ?></span><?
	layout_close_menuitem();
	layout_menuitem(Eelement_name('conversation'),'/msg/conversation/'.$_REQUEST['subID']);
	layout_close_menu();
	return true;
}
function msg_display_single() {
	if (!require_user()
	||	!require_idnumber($_REQUEST,'sID')
	) {
		return false;
	}
	require_once '_commentobject.inc';
	layout_show_section_header(Eelement_name('directmessage'));

	$msg = _directmessage_query($_REQUEST['sID']);
	if ($msg === false) {
		return;
	}
	if (!$msg) {
		_error('Bericht met ID '.$_REQUEST['sID'].' bestaat niet of is niet aan jou geadresseerd!');
		return;
	}
	msg_menu($msg[CURRENTUSERID == $msg['TO_USERID'] ? 'USERID' : 'TO_USERID']);

#	_directmessage_display($msg);
	comment_display('directmessage',null,$msg);
}
function msg_display_outgoing() {
	if (!require_user()
	||	!($nick = require_nick($_REQUEST,'subID'))
	) {
		return;
	}
	require_once '_directcontentlist.inc';
	layout_open_section_header();
	echo Eelement_plural_name('outgoing_directmessage') ?> &rarr; <? echo get_element_link('user',$_REQUEST['subID'],$nick);
	layout_close_section_header();

	msg_menu($_REQUEST['subID']);

	$directcontentlist = new _directcontentlist;
	if (!$directcontentlist->query_to($_REQUEST['subID'],CURRENTUSERID)
	||	!$directcontentlist->numrows()
	) {
		return;
	}
	$directcontentlist->display();

	include_js('js/earliermsg');
	include_js('js/quote');
	include_js('js/form/growtofit');

	?><div id="insertbefore"><input type="hidden" id="firstmessageid" value="<?= $directcontentlist->get_first_messageid(); ?>" /></div><?

 	layout_open_menu(0, 'clear');
	layout_open_menuitem();
	?><span id="addmsg" class="unhideanchor" onclick="importEarlierMessage(this)"><?= __C('action:see_previous_message_in_conversation') ?></span><?
	layout_close_menuitem();
	layout_menuitem(Eelement_name('conversation'),'/msg/conversation/'.$_REQUEST['subID']);
	layout_close_menu();
}
function msg_display_form() {
	require_once '_answeringmachine.inc';
	require_once '_commentobject.inc';
	if (!($touserid = $_REQUEST['subID'] ?: require_idnumber($_REQUEST,'USERID'))) {
		return;
	}
	if (!memcached_nick($touserid)) {
		return;
	}
	layout_show_section_header(Eelement_name('directmessage').' &rarr; '.get_element_link('user',$touserid));

	if (!may_send_directmessage($touserid, CURRENTUSERID, SHOW_REACH_MESSAGE | SHOW_ERROR_LINES)) {
		http_response_code(403);
		return;
	}
	answeringmachine_show($touserid);
	$msg = ($reply_to = have_idnumber($_REQUEST,'REPLY_TO')) ? _directmessage_query($reply_to) : null;
	_directmessage_display_form(array(
		'TO_USERID'		=> $touserid,
		'REPLY_TO'		=> $reply_to,
		'FORMSTAMP'		=> CURRENTSTAMP,
		'BODY'			=> !empty($_REQUEST['B64BODY']) ? base64_decode($_REQUEST['B64BODY']) : null
	));
}
function msg_perform_commit(): bool {
	if (!require_user()
	||	!($to_userid = require_idnumber	($_POST,'TO_USERID'))
	||	!require_number				($_POST,'FORMSTAMP')
	||	!require_idnumber			($_POST,'LAST_MESSAGEID')
	||	!require_something_trim		($_POST, 'BODY', null, null, utf8: true)
	) {
		return false;
	}
	if (isset($_POST['PREVIEW'])) {
		return true;
	}
	if (!may_send_directmessage($to_userid, CURRENTUSERID, SHOW_ERROR_LINES | SHOW_REACH_MESSAGE)) {
		return false;
	}
#	$bare = strtolower(preg_replace("'([\s\n\t\.\-_,!\$#@%&\*\|]+)'s",'',$_POST['BODY']));
#	if (false === ($views = db_single('user_counter_total','SELECT COUNTER FROM user_data_counter WHERE USERID='.CURRENTUSERID,DB_USE_MASTER))) {
#		return false;
#	}
	if ($id = _directmessage_commit(CURRENTUSERID, nocheck: true)) {
		register_notice('msg:notice:sent_LINE', DO_UBB, ['USERID' => $to_userid]);
		return true;;
	}
	return false;
}

function log_special_op(string $action, int $dstuserid = 0): void {
	db_insert('specialop','
	INSERT INTO specialop SET
		USERID	='.CURRENTUSERID.',
		ID		='.$dstuserid.',
		ACTION	="'.addslashes($action).'",
		STAMP	='.CURRENTSTAMP
	);
}
