<?php

declare(strict_types=1);

/** @noinspection SpellCheckingInspection */
function getcurrentaddr(): ?string {
	if (defined('CURRENTIPSTR')) {
		return CURRENTIPSTR;
	}
	if ($ipstr = $_SERVER['REMOTE_ADDR'] ?? null) {
		$ipv6  = str_contains($ipstr, ':');
		$ipnum = $ipv6 ? 0 : ip2long($ipstr);
		$ipbin = inet_pton($ipstr);
	} else {
		$ipv6  = null;
		$ipnum = null;
		$ipbin = null;
	}
	define('IPV6',			$ipv6);
	define('CURRENTIPNUM',	$ipnum);
	define('CURRENTIPBIN',	$ipbin);
	define('CURRENTIPSTR',	$ipstr);
	return CURRENTIPSTR;
}
