<?php

require_once 'defines/dating.inc';

function get_dating_type($find_flag) {
	if (!$find_flag) {
		return false;
	}
	static $__flag_to_type = [
		DTNG_SEX	=> 'sex',
		DTNG_ROMANCE	=> 'romance',
		DTNG_FLIRT	=> 'flirt',
		DTNG_FRIENDSHIP	=> 'friendship',
		DTNG_WEEKEND	=> 'weekend',
		DTNG_CHAT	=> 'chat',
	];
	return getifset($__flag_to_type,$find_flag);
}
function get_dating_info($type = null) {
	static $__type_to_info = [
		'sex'		=> DTNG_SEX,
		'romance'	=> DTNG_ROMANCE,
		'flirt'		=> DTNG_FLIRT,
		'friendship'	=> DTNG_FRIENDSHIP,
		'weekend'	=> DTNG_WEEKEND,
		'chat'		=> DTNG_CHAT,
	];
	return $type ? getifset($__type_to_info,$type) : $__type_to_info;
}
function get_badge_width() {
	static $__width = null;
	if ($__width === null) {
		$max = 0;
		foreach (get_dating_info() as $type => $null) {
			$typelen = strlen($type);
			$max = $max ? max($max,$typelen) : $typelen;
		}
		$__width = $max.'em';
	}
	return $__width;
}
function available_dating($user) {
	global $currentuser;
	# may $currentuser view $user ?

	if (have_super_admin()) {
		return $user['DATING'];
	}

	if ($user['USERID'] == CURRENTUSERID) {
		return $GLOBALS['currentuser']->row['DATING'];
	}

	$selected = $user['DATING'];

	if ($selected & DTNG_SHOW_TO_ALL) {
		return $selected;
	}


#	if (!($user['DATING'] & DTNG_SHOW_DISJUNCTS_TO_OTHERS)
#	||	$currentuser->row['DATING'] & DTNG_HIDE_DISJUNCTS_FROM_ME
#	) {
		# filter disjuncts
		$selected = $user['DATING'] & $currentuser->row['DATING'] & DTNG_TYPES;
#	}

	if (
#		(	!($user['DATING'] & DTNG_SHOW_INCOMPATIBLES_TO_OTHERS)
#		||	$currentuser->row['DATING'] & DTNG_HIDE_INCOMPATIBLES_FROM_ME
#		)
		$selected & DTNG_BODILY
	) {
		if (!$user['SEX']	  || !$currentuser->row['SEX']
		||	!$user['SEXPREF'] || !$currentuser->row['SEXPREF']
		) {
			$selected &= ~DTNG_BODILY;
		} else {
			static $__iscompat = null;
			if ($__iscompat === null) {
				$__iscompat = get_compat_hash();
			}
			if (!isset($__iscompat[$user['SEX']][$user['SEXPREF']][$currentuser->row['SEX']][$currentuser->row['SEXPREF']])) {
				$selected &= ~DTNG_BODILY;
			}
		}
	}
	return $selected;
}
function get_compat_hash() {
	static $__iscompat = [
		'M'	=> [
			'hetero' => [
				'F'	=> [
					'hetero'	=> true,
					'bi'		=> true,
				],
			],
			'bi'	=> [
				'M'	=> [
					'bi'		=> true,
					'homo'		=> true,
				],
				'F'	=> [
					'hetero'	=> true,
					'bi'		=> true,
				],
			],
			'homo'	=> [
				'M'	=> [
					'bi'		=> true,
					'homo'		=> true,
				]
			],
		],
		'F'	=> [
			'hetero' => [
				'M'	=> [
					'hetero'	=> true,
					'bi'		=> true,
				],
			],
			'bi'	=> [
				'M'	=> [
					'hetero'	=> true,
					'bi'		=> true,
				],
				'F'	=> [
					'bi'		=> true,
					'homo'		=> true,
				],
			],
			'homo'	=> [
				'F'	=> [
					'bi'		=> true,
					'homo'		=> true,
				],
			]
		]
	];
	return $__iscompat;
}
function show_dating_badges($user) {
	$userid = $user['USERID'];
	$allow_msg = have_user() && CURRENTUSERID != $userid;
	$helpdesk_admin = false;//have_admin('helpdesk');
	$selected = available_dating($user);
	$all_dating = $user['DATING'];

	if (!$selected && !$helpdesk_admin) {
		return;
	}
	foreach (get_dating_info() as $type => $flag) {
		$allow = $selected & $flag;
		$show = $helpdesk_admin && ($all_dating & $flag);
		if ($allow || $show) {
			?> <?
			if ($allow && $allow_msg) {
				?><a href="/msg/form/<?= $userid ?>/<?= $type ?>" class="choice"><? show_dating_badge($type) ?></a><?
			} else {
				if (!$allow) {
					?><span class="light"><?
				}
 				show_dating_badge($type);
 				if (!$allow) {
 					?></span><?
 				}
			}
		}
	}
}
function show_dating_squares($user,$only = null) {
	$userid = $user['USERID'];
	$selected = isset($user['DATING']) ? available_dating($user) : (($user = memcached_user($userid)) ? available_dating($user) : 0);
	if ($only) {
		$selected &= $only;
	}
	$allow_msg = CURRENTUSERID != $userid;

	foreach (get_dating_info() as $type => $flag) {
		if (($selected & $flag)
		&&	$allow_msg
		) {
			?> <?
			if ($allow_msg && have_user()) {
				?><a title="<?= __('dating:'.$type) ?>" href="/msg/form/<?= $userid ?>/<?= $type ?>" class="choice"><? show_dating_square($type) ?></a><?
			} else {
				show_dating_square($type);
			}
		}
	}
}
function show_dating_multi_select($selected = 0,$visible = 0,$do_requires = false) {
	include_js('js/form/dating');
	require_once 'defines/dating.inc';
	$info = get_dating_info();

	if (!$visible) {
		# move sex to last position
		unset($info['sex']);
		$info['sex'] = DTNG_SEX;
	}
	?><div class="dating"><?
	foreach ($info as $type => $flag) {
		if ($visible && !($visible & $flag)) {
			continue;
		}
		$checked = $selected & $flag;
		?><label class="center ib <?= $checked ? 'active' : 'inactive' ?>"><?
		ob_start();
		show_input([
			'type'		=> 'checkbox',
			'name'		=> 'DATING[]',
			'value'		=> get_dating_info($type),
			'checked'	=> $checked,
			'onclick'	=> 'Pf.clickDatingType(this,'.($do_requires ? 'true' : 'false').')',
		]);
		show_dating_badge($type,ob_get_clean());
		?></label> <?
	}
	?></div><?
}
function show_dating_badge($type,$checkbox = null) {
	include_style('dating');
	?><div class="<?= $type ?> badge" style="width:<?= get_badge_width() ?>"><?
	echo $checkbox;
	echo __('dating:'.$type);
	?></div><?
}
function show_dating_square($type) {
	include_style('dating');
	?><div class="<?= $type ?> mini badge" style="width:1em;height:1em;border-radius:.3em">&nbsp;</div><?
}
function allow_dating($userid) {
	static $__dating;
	return	isset($__dating[$userid])
	?	$__dating[$userid]
	:	($__dating[$userid] = memcached_single('user','SELECT DATING FROM user WHERE USERID='.$userid, TEN_MINUTES, 'dating:'.$userid));
}
function show_selected_dating_badge($userid) {
	if (!isset($_POST['DATING'])
	&&	empty($_REQUEST['SUBACTION'])
	) {
		return;
	}
	$allowed = allow_dating($userid);
	if (!$allowed) {
		return;
	}
	$flag = get_dating_info($type = getifset($_POST,'DATING') ?: $_REQUEST['SUBACTION']);
	if (!$flag) {
		return;
	}

	include_js('js/form/dating');
	?><div class="block"><?
	?><label class="center ib active"><?
	ob_start();
	show_input([
		'type'		=> 'checkbox',
		'name'		=> 'DATING',
		'value'		=> $type,
		'checked'	=> true,
		'onclick'	=> 'Pf.clickDatingType(this,false)',
	]);
	show_dating_badge($type,ob_get_clean());
	?></label><?
	?></div><?
}
function get_dating_where($need,$suitable_only = false) {
	global $currentuser;

	$bodneed = $need & DTNG_BODILY;
#	$need_show_all = true;
	$need_show_all = false;

	if ($bodneed) {
		$__iscompat = get_compat_hash();
		if (!have_user()
		||	!isset($__iscompat[$currentuser->row['SEX']][$currentuser->row['SEXPREF']])
		) {
		 	$bodwherep = ' AND DATING&'.DTNG_SHOW_TO_ALL;
		 	$need_show_all = true;
		} else {
			foreach ($__iscompat[$currentuser->row['SEX']][$currentuser->row['SEXPREF']] as $sex => $sexprefs) {
				$okother[] = 'SEX="'.$sex.'" AND SEXPREF IN ('.stringsimplodekeys(',',$sexprefs).')';
			}
			$bodwherep = ' AND ('.implode(' OR ',$okother).')';
		}
	}
	for ($i = 1; $i <= DTNG_TYPES; ++$i) {
		$bodily = $i & DTNG_BODILY;
		if ($i & $need) {
			$flag = $need_show_all ? ($i | DTNG_SHOW_TO_ALL) : $i;
			$nums[$bodily ? true : false][] = $flag;
			$allnums[] = $flag;
		}
	}
	if (empty($nums)) {
		return '0';
	}
	$wheres = [];
	foreach ($nums as $bodily => $actuals) {
		$wheres[] = 'DATING IN ('.implode(',',$actuals).')'.($bodneed ? $bodwherep : null);
	}
	$wherep = 'DATING&'.$need.' AND DATING IN ('.implode(',',$allnums).') AND ('.($wheres ? implode(' OR ',$wheres) : 0).')';
#	$wherep = 'DATING&'.$need.' AND ('.($wheres ? implode(' OR ',$wheres) : 0).')';
	return $wherep;
}
function show_dating_choice() {
	if (!have_user()
	||	CURRENTUSERID >= 1323134
	) {
		return;
	}
	global $currentuser;
	$user = $currentuser->row;

#	$user['DATING'] = DTNG_UNSPECIFIED;

	if (isset($_POST['DATINGASK'])) {
		store_dating();
		return;
	}
	if (!isset($user['DATING'])) {
		error_log_r($currentuser);
		return;
	}
	if ($user['DATING'] != DTNG_UNSPECIFIED
	||	memcached_get($skey = 'asked_dating:'.CURRENTUSERID)
	) {
		return;
	}
	memcached_set($skey, true, TEN_MINUTES);

	include_style('forlite');
	include_style('overlay');
	include_style('form',null,STYLE_FOR_LITE);
	?><form<?
	?> method="post"<?
	?> action="/"<?
	?> onsubmit="if(!validateElements(this.elements)){return false}this.className='light';return !do_inline(<?
	?>'POST',<?
	?>'/storedating.act',<?
	?>'ACT',<?
	?>function(req,self){<?
		?>self.parentNode.removeChild(self);<?
		?>if(req.status!=200){<?
			?>showerrors(req,ERR_TO_ALERT);<?
		?>}<?
	?>},<?
	?>build_poststr(this)<?
	?>,null,this)"><?
	?><input type="hidden" name="DATINGASK" value="1" /><?

	?><div class="bgov"><?
	?><div class="overlay"><?
	?><div><?
	?><div style="max-width:60em"><?

	?><div class="block"><?
	if (CURRENTLANGID == 1) {
		?>Je hebt aangegeven in je profiel dat je 'beschikbaar' bent.<br /><?
		?>Recentelijke hebben we die functie uitgebreid. Je kunt nu uit de volgende beschikbaarheden kiezen:<?
	} else {
		?>You have indicated in your profile that you are 'available'.<br /><?
		?>Recently whe have extended that option. You can now choose from the following availability settings:<?
	}
	?></div><?

	?><div class="block"><?
	show_dating_multi_select(0,0,!$user['SEXPREF'] || !$user['SEX']);
	?></div><?


	?><div class="block"><?
	if (CURRENTLANGID == 1) {
		?>Klik op een knop om deze in te schakelen, hoe meer mensen het aan geven, hoe eenvoudiger je gevonden kunt worden of iemand kunt vinden!<?
	} else {
		?>Click a button to turn it on, the more people use it, the easier you can be found or you can find someone!<?
	}
	?></div><?

	?><div class="block"><?
	show_input([
		'type'		=> 'submit',
		'class'		=> 'r',
		'value'		=> __('action:store')
	]);
	if (!$user['SEXPREF'] || !$user['SEX']) {
		?><div class="l"><?
		if (!$user['SEX']) {
			?><select name="GENDER" class="hidden"><?
				?><option disabled="disabled" selected="selected" value=""><?= element_name('your_gender') ?></option><?
				?><option value="M"><?= __('gender:male') ?></option><?
				?><option value="F"><?= __('gender:female') ?></option><?
			?></select><?
		}
		if (!$user['SEXPREF']) {
			?><select name="SEXPREF" class="hidden"><?
				?><option disabled="disabled" selected="selected" value=""><?= element_name('your_(sexual)orientation') ?></option><?
				?><option value="hetero"><?= __('sexpref:straight') ?></option><?
				?><option value="homo"><?= __('sexpref:gay') ?></option><?
				?><option value="bi"><?= __('sexpref:bisexual') ?></option><?
			?></select><?
		}
		?></div><?
	}
	?></div><?


	?></div><?
	?></div><?
	?></div><?
	?></div><?

	?></form><?
}
function store_dating() {
	$dating = 0;
	if (have_number_array($_POST,'DATING')) {
		foreach ($_POST['DATING'] as $flg) {
			$dating |= $flg;
		}
	}
	if ($dating) {
		$dating |= DTNG_UNSPECIFIED;
	}
	if (!db_insert('user_log','
		INSERT INTO user_log
		SELECT * FROM user
		WHERE USERID='.CURRENTUSERID)
	||	!db_update('user','
		UPDATE user SET
			MUSERID	='.CURRENTUSERID.',
			MSTAMP	='.CURRENTSTAMP.',
			DATING	='.$dating.',
			SEX	='.(!empty($_POST['GENDER']) ? '"'.addslashes($_POST['GENDER']).'"' : 'SEX').',
			SEXPREF	='.(!empty($_POST['SEXPREF']) ? '"'.addslashes($_POST['SEXPREF']).'"' : 'SEXPREF').'
		WHERE USERID='.CURRENTUSERID)
	) {
		return false;
	}
	flush_user(CURRENTUSERID);
	require_once '_elementchanged.inc';
	element_changed('user',CURRENTUSERID);
	global $currentuser;
	$currentuser->row['DATING'] = $dating;
	if ($sex = getifset($_POST,'GENDER')) {
		$currentuser->row['SEX'] = $sex;
	}
	if ($sexpref = getifset($_POST,'SEXPREF')) {
		$currentuser->row['SEXPREF'] = $sexpref;
	}
	return true;
}
function show_dating_visibility($dating) {
	$all = $dating & DTNG_SHOW_TO_ALL;
	?><select name="DATING[]" class="fw" data-lbg="data-lbg"><?
	?><option value="<?= DTNG_SHOW_TO_ALL ?>"><?= element_name('everyone') ?></option><?
	?><option<?
	if (!$all) {
		?> selected="selected"<?
	}
	?> value="0"><?= __('attrib:suitable') ?></option><?
	?></select><?
}
function get_dating_numbers($flags) {
	$numbers = [];
	for ($i = 2; $i <= DTNG_SHOW_TO_ALL; $i<<=1) {
		if ($flags & $i) {
			$numbers[] = $i;
		}
	}
	return $numbers;
}
