<?php

require_once 'defines/topic.inc';

function show_topictype_optionsrow($chosenforumid) {
	require_once '_forum.inc';
	$saleforumids = get_sale_forums();
	if (!$saleforumids) {
		return;
	}
	foreach ($saleforumids as $forumid => $true) {
		layout_restart_row($forumid == $chosenforumid ? 0 : ROW_HIDDEN,'types-'.$forumid);
		echo Eelement_name('type');
		layout_field_value();
		?><select name="TYPE[<?= $forumid; ?>]"><?
		show_topictype_options(TOPIC_FOR_SALE);
		?></select><?
	}
}
function show_topictype_options($flags = 0) {
	?><option value="0"></option><?
	?><option<? if ($flags & TOPIC_FOR_SALE) echo ' selected="selected"'; ?> value="<?= TOPIC_FOR_SALE; ?>"><?= __C('topictype:for_sale'); ?></option><?
	?><option<? if ($flags & TOPIC_SEARCHING_FOR) echo ' selected="selected"'; ?> value="<?= TOPIC_SEARCHING_FOR; ?>"><?= __C('topictype:searching_for'); ?></option><?
}
function strip_topictype_subject($type,$subject) {
	if (!$type) {
		return $subject;
	}
	if ($type == TOPIC_FOR_SALE) {
		$strip = '"^[\[\(]?(ge?z(ocht)?|te\s?koop|tk[ag]?)[\)\]:\.\s]*"i';
	} else {// ($type == TOPIC_SEARCHING_FOR)
		$strip = '"^[\[\(]?(ge?z(ocht)?|te\s?koop|tk[ag]?)[\)\]:\.\s]*"i';
	}
	return preg_replace($strip,'',$subject);
}
function require_topictype_using_forum($arr,$name,$forumid) {
	if (false === require_number($arr,$name)) {
		return false;
	}
	require_once '_forum.inc';
	return	get_sale_forums($forumid)
	&&	(	!($type = $arr[$name])
		||	$type == TOPIC_FOR_SALE
		||	$type == TOPIC_SEARCHING_FOR
		);
}
function require_topictype_using_topic($arr,$name,$topicid) {
	if (false === require_number($arr,$name)) {
		return false;
	}
	if ($type = ($arr[$name] == 0)) {
		return true;
	}
	return	($forumid = require_topic_forumid($topicid))
	&&	require_topictype_using_forum($arr,$name,$forumid);
}
function get_topic_prefix($element,$id,$flags) {
	if (!$flags) {
		return null;
	}
	return	get_topic_poll_prefix($element,$id,$flags)
	?:	(	$flags & TOPIC_FOR_SALE
		?	__('topicprefix:for_sale')
		:	(	$flags & TOPIC_SEARCHING_FOR
			?	__('topicprefix:searching_for')
			:	null
			)
		);
}
function get_topic_longprefix($element,$id,$flags) {
	if (!$flags) {
		return null;
	}
	return	get_topic_poll_prefix($element,$id,$flags)
	?:	(	$flags & TOPIC_FOR_SALE
		?	__C('topicprefix_long:for_sale')
		:	(	$flags & TOPIC_SEARCHING_FOR
			?	__C('topicprefix_long:searching_for')
			:	null
			)
		);
}
function get_topic_poll_prefix($element,$id,$flags) {
	require_once 'defines/topic.inc';
	require_once '_pollstatus.inc';
	if ($flags & TOPIC_IS_POLL
	&&	memcached_single('poll','SELECT STATUS='.POLLSTATUS_ACCEPTED.' FROM poll WHERE ELEMENT="'.$element.'" AND ELEMENTID='.$id)
	) {
		return Eelement_name('poll');
	}
	return null;
}
