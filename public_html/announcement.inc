<?php

require_once '_announcement.inc';
require_once '_announcementlist.inc';
require_once '_ubb.inc';
require_once '_ubb_preprocess.inc';

function perform_commit() {
	switch ($_REQUEST['ACTION']) {
	case 'markread':	announcement_mark_read(); return;
	case 'commit':		announcement_commit(); return;
	case 'activate':	announcement_set_active(1); return;
	case 'deactivate':	announcement_set_active(0); return;
	case 'makeletters':	announcement_make_letters(); return;
	case 'resend':		announcement_resend(); return;
	}
}
function display_body() {
	switch ($_REQUEST['ACTION']) {
	default:
	case null:		announcement_display_overview(); return;
	case 'markread':
	case 'letters':		announcement_display_letters(); return;
	case 'letter':		announcement_display_letter(); return;
	case 'activate':
	case 'deactivate':
	case 'commit':
	case 'single':
	case 'makeletters':
	case 'resend':		announcement_display_single(); return;
	case 'form':		announcement_display_form(); return;
	}
}
function announcement_display_letters() {
	layout_show_section_header();

	if (!announcementlist_show_actives()) {
		?><p>Er zijn op dit moment geen mededelingen.</p><?
	}
}
function announcement_display_letter() {
	if (!require_idnumber($_REQUEST,'sID')) {
		return;
	}
	$announcementid = $_REQUEST['sID'];
	$announcement = db_single_assoc(
		array('announcement','announcement_letter'),'
		SELECT FLAGS,SHOWTITLE,TITLE,BODY
		FROM announcement AS a
		LEFT JOIN announcement_letter AS al ON a.ANNOUNCEMENTID=al.ANNOUNCEMENTID AND al.USERID='.CURRENTUSERID.'
		WHERE a.ANNOUNCEMENTID='.$announcementid
	);
	if ($announcement === false) {
		return;
	}
	if (!$announcement) {
		_error(__('announcement:error:nonexistent_LINE',array('ID'=>$announcementid)));
		return;
	}
	if ($announcement['FLAGS'] === null) {
		_error(__('announcement:error:not_for_you_LINE'));
		return;
	}
	layout_show_section_header();
	require_once '_announcement.inc';
	$announcement['ANNOUNCEMENTID'] = $announcementid;
	show_announcement($announcement,!($announcement['FLAGS'] & ANNOUNCE_READ));
}
function announcement_set_active($setting) {
	if (!require_idnumber($_REQUEST,'sID')
	||	!require_admin('announcement')
	||	!db_insert('announcement_log','
		INSERT INTO announcement_log
		SELECT * FROM announcement
		WHERE ANNOUNCEMENTID='.$_REQUEST['sID'].'
		  AND ACTIVE='.($setting ? 0 : 1))
	||	!db_update('announcement','
		UPDATE announcement SET
			ACTIVE	='.$setting.',
			MSTAMP	='.CURRENTSTAMP.',
			MUSERID	='.CURRENTUSERID.'
		WHERE ANNOUNCEMENTID='.$_REQUEST['sID'].'
		  AND ACTIVE='.($setting ? 0 : 1))
	) {
		return;
	}
	_notice($setting ? 'Aankondiging is geactiveerd.' : 'Aankondiging is gedeactiveerd.');
}
function announcement_display_overview() {
	if (!require_admin('announcement')) {
		return;
	}
	layout_show_section_header();

	layout_open_menu();
	layout_menuitem(__C('action:add'),'/announcement/form');
	layout_close_menu();

	$announcements = db_rowuse_array(
		'announcement','
		SELECT STARTSTAMP,STOPSTAMP,TITLE,ANNOUNCEMENTID,ACTIVE
		FROM announcement
		ORDER BY STARTSTAMP DESC'
	);
	if ($announcements === false) {
		return;
	}
	if (!$announcements) {
		?><p>Geen aankondigingen gevonden.</p><?
		return;
	}
	layout_open_box('white');
	layout_open_table(TABLE_FULL_WIDTH);
	layout_start_header_row();
	layout_header_cell(Eelement_name('title'));
	layout_header_cell_right(__C('field:start'));
	layout_header_cell_right(Eelement_name('last_day'));
	layout_stop_header_row();
	foreach ($announcements as $announcement) {
		unset($rowclasses);
		if ($announcement['ACTIVE']) {
			$rowclasses[] = 'active';
		}
		if (CURRENTSTAMP >= $announcement['STARTSTAMP']
		&&	CURRENTSTAMP < $announcement['STOPSTAMP']
		) {
			$rowclasses[] = 'running';
		}
		if (isset($rowclasses)) {
			layout_start_rrow(0,$rowclasses);
		} else {
			layout_start_rrow();
		}
		?><a href="/announcement/<?= $announcement['ANNOUNCEMENTID'];
		?>"><?= $announcement['TITLE'] ? escape_utf8($announcement['TITLE']) : '#'.$announcement['ANNOUNCEMENTID'];
		?></a><?
		layout_next_cell(class: 'right');
		_dateday_display($announcement['STARTSTAMP'],null,false,true);
		layout_next_cell(class: 'right');
		_dateday_display($announcement['STOPSTAMP']-1,null,false,true);
		layout_stop_row();
	}
	layout_close_table();
	layout_close_box();
}
function announcement_display_single() {
	if (!require_admin('announcement')
	||	!($announcementid = require_idnumber($_REQUEST,'sID'))
	) {
		return;
	}
	layout_show_section_header();

	$announcement = db_single_assoc('announcement','SELECT * FROM announcement WHERE ANNOUNCEMENTID='.$announcementid);
	if ($announcement === false) {
		return;
	}
	if (!$announcement) {
		_error(__('announcement:error:nonexistent_LINE',array('ID'=>$announcementid))	);
		return;
	}
	$stats = db_single_assoc(
		'announcement_letter','
		SELECT	COUNT(*) AS TOTAL_CNT,
			COUNT(IF(FLAGS&'.ANNOUNCE_HAVE.',1,NULL)) AS HAVE_CNT,
			COUNT(IF(FLAGS&'.ANNOUNCE_SEEN.',1,NULL)) AS SEEN_CNT,
			COUNT(IF(FLAGS&'.ANNOUNCE_READ.',1,NULL)) AS READ_CNT
		FROM announcement_letter
		WHERE ANNOUNCEMENTID='.$announcementid
	);
	if ($stats === false) {
		return;
	}
	$rights = db_boolean_hash('rights','SELECT `PORTION` FROM announcement_rights WHERE ANNOUNCEMENTID='.$announcementid);
	if ($rights === false) {
		return;
	}
	if ($rights) {
		foreach ($rights as $right => &$desc) {
			$desc = preg_match('"_(?:comment|rating)$"',$right) ? Eelement_plural_name($right) : __C('right:'.$right);
		}
		unset($desc);
		asort($rights);
	}

	layout_open_menu();
	layout_menuitem(Eelement_name('overview'),'/announcement/');
	layout_close_menu();

	layout_open_menu();
	layout_menuitem(__C('action:change'),'/announcement/'.$announcementid.'/form');
	if ($announcement['ACTIVE']) {
		layout_menuitem(__C('action:deactivate'),'/announcement/'.$announcementid.'/deactivate');
	} else {
		layout_menuitem(__C('action:activate'),'/announcement/'.$announcementid.'/activate');
		layout_menuitem(__C('action:add_poll'),'/poll/newform/announcement?ELEMENTID='.$announcementid);
	}
	layout_continue_menu();
	if ($stats['TOTAL_CNT']) {
		layout_menuitem(__C('action:resend'),'/announcement/'.$announcementid.'/resend');
	}
	if ($announcement['RECEIVERS']) {
		layout_menuitem(__C('action:determine_receivers'),'/announcement/'.$announcementid.'/makeletters');
	}
	layout_close_menu();

	?><article itemscope itemtype="https://schema.org/Article"><?
	layout_open_box('white');
	layout_box_header(Eelement_name('information'));
	layout_open_table(TABLE_VTTOP);
	layout_start_row();
	echo Eelement_name('title');
	layout_field_value();
	echo escape_utf8($announcement['TITLE']);
	layout_restart_row();
	echo __C('field:start');
	layout_field_value();
	_dateday_display($announcement['STARTSTAMP']);
	layout_restart_row();
	echo __C('field:stop');
	layout_field_value();
	_dateday_display($announcement['STOPSTAMP']);
	layout_restart_row();
	echo __C('field:show_title');
	layout_field_value();
	echo __($announcement['SHOWTITLE'] ? 'answer:yes' : 'answer:no');

	layout_restart_row();
	echo Eelement_plural_name('receiver');
	layout_field_value();
	$first = true;
	foreach (explode(',',$announcement['RECEIVERS']) as $receiver) {
		if ($first) {
			$first = false;
		} else {
			?>, <?
		}
		switch ($receiver) {
		case 'crew':			echo element_plural_name('employee'); break;
		case 'relations':		echo element_plural_name('relation'); break;
		case 'artistmembers':		echo element_plural_name('artist'); break;
		case 'location_employees':	echo element_plural_name('location_employee'); break;
		case 'organization_employees':	echo element_plural_name('organization_employee'); break;
		}
	}
	if ($rights) {
		layout_restart_row();
		echo Eelement_name('right',count($rights));
		layout_field_value();
		foreach ($rights as $right => $desc) {
			echo $desc ?><br /><?
		}
	}


	layout_close_table();

	layout_continue_box();
	layout_box_header(Eelement_plural_name('statistic'));

	if ($stats) {
		layout_open_table(TABLE_FULL_WIDTH);
		// TOTAL_CNT
		layout_start_reverse_row();
		echo $stats['TOTAL_CNT'];
		layout_value_field();
		echo element_name('receiver',$stats['TOTAL_CNT']);

		// HAVE_CNT
		layout_restart_reverse_row();
		echo $stats['HAVE_CNT'];
		layout_value_field();
		?>gepasseerd<?

		// SEEN_CNT
		layout_restart_reverse_row();
		echo $stats['SEEN_CNT'];
		layout_value_field();
		?>gezien<?

		// READ_CNT
		layout_restart_reverse_row();
		echo $stats['READ_CNT'];
		layout_value_field();
		?>gelezen<?

		layout_stop_row();
		layout_close_table();
	}

	layout_display_alteration_note($announcement);

	layout_close_box();

	layout_open_box('grey');
	if ($announcement['SHOWTITLE']) {
		layout_open_box_header();
		echo escape_utf8($announcement['TITLE']);
		layout_close_box_header();
	}
	?><div class="block"><?= make_all_html($announcement['BODY']); ?></div><?
	layout_close_box();
	?></article><?
}
function announcement_display_form() {
	if (!require_admin('announcement')) {
		return;
	}
	layout_show_section_header(Eelement_name('announcement_form'));

	$rights = db_boolean_hash('right','SELECT `PORTION` FROM rights');
	if ($rights === false) {
		return;
	}
	foreach ($rights as $right => &$desc) {
		$desc = preg_match('"_(?:comment|rating)$"',$right) ? Eelement_plural_name($right) : __C('right:'.$right);
	}
	unset($desc);
	asort($rights);

	if ($announcementid = $_REQUEST['sID']) {
		$announcement = db_single_assoc('announcement','SELECT * FROM announcement WHERE ANNOUNCEMENTID='.$_REQUEST['sID']);
		if ($announcement === false) {
			return;
		}
		if (!$announcement) {
			_error(__('announcement:error:nonexistent_LINE',array('ID'=>$_REQUEST['sID'])));
			return;
		}
		$have_rights = db_boolean_hash('rights','SELECT `PORTION` FROM announcement_rights WHERE ANNOUNCEMENTID='.$announcementid);
		if ($have_rights === false) {
			return;
		}
	}

	?><form<?
	?> accept-charset="utf-8"<?
	?> method="post"<?
	?> action="/announcement/<?
	if (isset($announcement)) {
		echo $_REQUEST['sID']; ?>/<?
	}
	?>commit"<?
	?> onsubmit="return submitForm(this)"><?

	layout_open_box('white');
	layout_open_table(TABLE_CLEAN | TABLE_VTOP);
	// TITLE
	layout_start_row();
	echo Eelement_name('title');
	layout_field_value();
	?><input type="text" name="TITLE" value="<?
	if (!empty($announcement['TITLE'])) {
		echo escape_utf8($announcement['TITLE']);
	}
	?>"><?
	// STARTSTAMP
	layout_restart_row();
	echo __C('field:start');
	layout_field_value();
	_date_display_select_stamp(isset($announcement) ? $announcement['STARTSTAMP'] : CURRENTSTAMP, 'START', 0, +1);

	// STOPSTAMP
	layout_restart_row();
	echo Eelement_name('last_day');
	layout_field_value();
	_date_display_select_stamp(isset($announcement) ? $announcement['STOPSTAMP'] - 1 : CURRENTSTAMP, 'STOP', 0, +1);

	// SHOWTITLE
	layout_restart_row();
	?><label for="showtitle"><?= __C('action:show_title') ?></label><?
	layout_field_value();
	?><input type="checkbox" id="showtitle" name="SHOWTITLE" value="1"<?
	if (!empty($announcement['SHOWTITLE'])) {
		?> checked="checked"<?
	}
	?>><?
	// BODY
	layout_restart_row();
	echo Eelement_name('real_message');
	layout_field_value();
	?><textarea name="BODY" rows="20" cols="80"><?
	if (!empty($announcement['BODY'])) {
		echo escape_utf8($announcement['BODY']);
	}
	?></textarea><?

	layout_restart_row();
	echo Eelement_plural_name('receiver');
	layout_field_value();
	$crew = $relations = $artistmembers = $location_empl = $organization_empl = null;
	if (isset($announcement)) {
		foreach (explode(',',$announcement['RECEIVERS']) as $receiver) {
			switch ($receiver) {
			case 'crew':
				$crew = ' checked="checked"';
				break;
			case 'relations':
				$relations = ' checked="checked"';
				break;
			case 'artistmembers':
				$artistmembers = ' checked="checked"';
				break;
			case 'location_employees':
				$location_empl = ' checked="checked"';
				break;
			case 'organization_employees':
				$organization_empl = ' checked="checked"';
				break;
			}
		}
	}
	?><label><input<?= $relations	  ?> type="checkbox" value="relations" name="RECEIVERS[]" /> <?= element_plural_name('relation'); ?></label><br /><?
	?><label><input<?= $crew	  ?> onclick="setdisplay('rightsrow',this.checked)" type="checkbox" value="crew" name="RECEIVERS[]" /> <?= element_plural_name('flock_employee'); ?></label><br /><?
	?><label><input<?= $location_empl ?> type="checkbox" value="location_employees" name="RECEIVERS[]" /> <?= element_plural_name('location_employee'); ?></label><br /><?
	?><label><input<?= $organization_empl ?> type="checkbox" value="organization_employees" name="RECEIVERS[]" /> <?= element_plural_name('organization_employee'); ?></label><br /><?
	?><label><input<?= $artistmembers ?> type="checkbox" value="artistmembers" name="RECEIVERS[]" /> <?= element_plural_name('artist'); ?></label><?
	layout_restart_row($crew ? 0 : ROW_HIDDEN,'rightsrow');
	echo Eelement_plural_name('right');
	layout_field_value();
	$rights['forum'] = __C('right:forum');
	?><select name="PORTION[]" multiple="multiple"><?
	foreach ($rights as $right => $desc) {
		?><option<?
		if (isset($have_rights[$right])) {
			?> selected="selected"<?
		}
		?> value="<?= $right ?>"><?= $desc ?></option><?
	}
	?></select><?

	layout_stop_row();
	layout_close_table();
	layout_close_box();
	?><div class="block"><input type="submit" value="<?= __(isset($announcement) ? 'action:change' : 'action:add'); ?>" /></div><?
	?></form><?
}
function announcement_commit() {
	if (!require_admin('announcement')
	||	!require_anything_trim($_POST, 'BODY', utf8: true)
	||	!require_something_trim($_POST, 'TITLE', null, null, utf8: true)
	||	!require_date($_POST, 'START')
	||	!require_date($_POST, 'STOP')
	) {
		return;
	}
	$startstamp = _date_getstamp($_POST, 'START');
	$stopstamp  = _date_getstamp($_POST, 'STOP');
	if ($stopstamp) {
		$stopstamp = strtotime('+1 day', $stopstamp);
	}
	if ($stopstamp < $startstamp) {
		_error('Stopdatum ligt voor startdatum!');
		return;
	}
	if ($stopstamp === $startstamp) {
		_error('Stopdatum is indentiek aan startdatum!');
		return;
	}
	$setlist[] = 'TITLE="'.addslashes($_POST['TITLE']).'"';
	$setlist[] = 'BODY="'.addslashes($body = _ubb_preprocess($_POST['BODY'], utf8: true)).'"';
	$setlist[] = 'SHOWTITLE='.(isset($_POST['SHOWTITLE']) ? 1 : 0);
	$setlist[] = 'STARTSTAMP='.$startstamp;
	$setlist[] = 'STOPSTAMP='.$stopstamp;
	$setlist[] = 'RECEIVERS="'.(isset($_POST['RECEIVERS']) ? addslashes(implode(',',$_POST['RECEIVERS'])) : null).'"';
	if ($announcementid = $_REQUEST['sID']) {
		if (!db_insert('announcement_log','
			INSERT INTO announcement_log
			SELECT * FROM announcement
			WHERE ANNOUNCEMENTID='.$announcementid)
		||	!db_update('announcement','
			UPDATE announcement SET
				MUSERID	='.CURRENTUSERID.',
				MSTAMP	='.CURRENTSTAMP.','.
				implode(',',$setlist).'
			WHERE ANNOUNCEMENTID='.$announcementid)
		) {
			return;
		}
		register_notice('announcement:notice:changed_LINE');
	} else {
		if (!db_insert('announcement','
			INSERT INTO announcement SET
				CUSERID	='.CURRENTUSERID.',
				CSTAMP	='.CURRENTSTAMP.','.
				implode(',',$setlist))
		) {
			return;
		}
		$_REQUEST['sID'] = $announcementid = $newid = db_insert_id();
		register_notice('announcement:notice:added_LINE');
	}
	taint_include_cache('announcement',$announcementid,$body);
	$removep = null;
	if (have_array($_POST,'PORTION')) {
		foreach ($_POST['PORTION'] as $portion) {
			$vals[] = '('.$announcementid.',"'.addslashes($portion).'",'.CURRENTUSERID.','.CURRENTSTAMP.')';
		}
		if (!db_insert('announcement_rights','
			INSERT IGNORE INTO announcement_rights (ANNOUNCEMENTID,`PORTION`,CUSERID,CSTAMP)
			VALUES '.implode(',',$vals))
		) {
			return;
		}
		$removep = ' AND `PORTION` NOT IN ('.stringsimplode(',',$_POST['PORTION']).')';
	}
	if (!isset($newid)) {
		if (!db_insert('announcement_rights_log','
			INSERT INTO announcement_rights_log
			SELECT *,'.CURRENTUSERID.','.CURRENTSTAMP.' FROM announcement_rights
			WHERE ANNOUNCEMENTID='.$announcementid.$removep)
		||	!db_delete('announcement_rights','
			DELETE FROM announcement_rights
			WHERE ANNOUNCEMENTID='.$announcementid.$removep)
		) {
			return;
		}
	}
}

function announcement_make_letters() {
	if (!($announcementid = require_idnumber($_REQUEST,'sID'))) {
		return;
	}
	$receivers = db_single('announcement','SELECT RECEIVERS FROM announcement WHERE ANNOUNCEMENTID='.$announcementid);
	if ($receivers === false) {
		return;
	}
	if (!$receivers) {
		register_error('announcement:error:invalid_receivers_LINE',array('RECEIVERS'=>$receivers));
		return;
	}
	$rights = db_simpler_array('announcement_rights','SELECT `PORTION` FROM announcement_rights WHERE ANNOUNCEMENTID='.$announcementid);
	if ($rights === false) {
		return;
	}
	$forum = $rights && in_array('forum',$rights);
	$userids = [];
	foreach (explode(',',$receivers) as $receiver) {
		switch ($receiver) {
		case 'crew':
			if (!$rights) {
				require_once '_crew.inc';
				$add_userids= get_crewlist();
				if ($add_userids === false) {
					return;
				}
				$userids += $add_userids;
			} else {
				$userids = db_same_hash('rights','SELECT DISTINCT USERID FROM rights WHERE `PORTION` IN ('.stringsimplode(',',$rights).')');
				if ($userids === false) return;
				if ($forum) {
					$moderators = get_forum_admins();
					$userids += $moderators;
				}
			}
			break;
		case 'relations':
			$add_userids = db_same_hash(
				array('user_account','relationmember'),'
				SELECT DISTINCT USERID
				FROM relationmember
				JOIN user_account USING (USERID)
				WHERE STATUS="active"'
			);
			if ($add_userids === false) {
				return;
			}
			$userids += $add_userids;
			break;
		case 'artistmembers':
			warning('Laat dit even controleren!');
			$add_userids = db_same_hash(
				array('user_account','artistmember'),'
				SELECT DISTINCT USERID
				FROM artistmember
				JOIN user_account USING (USERID)
				WHERE STATUS="active"
				  AND ACTIVE=1'
			);
			if ($add_userids === false) {
				return;
			}
			$userids += $add_userids;
			break;
		case 'location_employees':
			$add_userids = db_same_hash(
				array('user_account','locationmember'),'
				SELECT DISTINCT USERID
				FROM locationmember
				JOIN user_account USING (USERID)
				WHERE STATUS="active"
				  AND ACTIVE=1'
			);
			if ($add_userids === false) {
				return;
			}
			$userids += $add_userids;
			break;
		case 'organization_employees':
			$add_userids = db_same_hash(
				array('user_account','organizationmember'),'
				SELECT DISTINCT USERID
				FROM organizationmember
				JOIN user_account USING (USERID)
				WHERE STATUS="active"
				  AND ACTIVE=1'
			);
			if ($add_userids === false) {
				return;
			}
			$userids += $add_userids;
			break;
		default:
			register_error('announcement:error:unknown_receiver_LINE', ['RECEIVER' => $receiver]);
		}
	}
	if (!$userids) {
		register_warning('announcement:warning:no_receivers_LINE');
		return;
	}
	foreach ($userids as $userid => $x) {
		$vals[] = '('.$announcementid.','.$userid.')';
	}
	if (!db_delete('announcement_letter','
		DELETE FROM announcement_letter
		WHERE USERID NOT IN ('.implodekeys(',',$userids).')
		  AND ANNOUNCEMENTID='.$announcementid)
	||	!db_insert('announcement_letter','
		INSERT IGNORE INTO announcement_letter (ANNOUNCEMENTID,USERID)
		VALUES '.implode(',',$vals))
	) {
		return;
	}
	register_notice('announcement:notice:determined_receivers_LINE');
}

function announcement_resend(): void {
	if (!($announcementid = require_idnumber($_REQUEST,'sID'))) {
		return;
	}
	if (!db_update('annoucement_letter','
		UPDATE announcement_letter
			SET FLAGS = 0
		WHERE ANNOUNCEMENTID = '.$announcementid)
	) {
		return;
	}
	register_notice('announcement:notice:resent_LINE');
}
