<?php

require_once '_employees.inc';
require_once '_gallery.inc';
require_once '_locationpart.inc';
require_once '_partylist.inc';
require_once '_ticket.inc';
require_once '_ratinglist.inc';
require_once '_vote.inc';

function get_location_info(): array|false|null {
	static $__location;
	if (isset($__location)) {
		return $__location;
	}
	if (!($locationid = $_REQUEST['sID'])) {
		return $__location = [];
	}
	if ($locationid === 20480) {
		moved_permanently(get_element_href('organization', 10935));
	}
	if (false === ($__location = memcached_single_assoc(['location','city'], '
		SELECT	location.NAME, city.NAME AS CITY_NAME, TITLE, COUNTRYID, REGIONID, EROTIC,
				IF(location.LATITUDE, location.LATITUDE,  city.LATITUDE) as LAT,
				IF(location.LATITUDE, location.LONGITUDE, city.LONGITUDE) as LON
		FROM location
		LEFT JOIN city USING (CITYID)
		WHERE LOCATIONID='.$locationid,
		TEN_MINUTES))
	) {
		return false;
	}
	return $__location;
}
function display_title(): void {
	if (have_idnumber($_REQUEST,'sID')
	&&	($loc = get_location_info($_REQUEST['sID']))
	) {
		echo element_name('location') ?>: <?
		echo escape_utf8($loc['TITLE']);
		if ($loc['CITY_NAME']) {
			?>, <?
			echo escape_utf8($loc['CITY_NAME']);
		}
		# show_current_action($_REQUEST['sID']);
	} else {
		echo element_plural_name('location');
		# show_current_action();
	}
}
function display_header() {
	if ($location = get_location_info()) {
		require_once '_geotag.inc';
		show_geotag($location);
	}
	require_once '_feed.inc';
	show_feed('agenda',FEED_HEADER);
}
function perform_commit(): ?bool {
	switch ($_REQUEST['ACTION']) {
	default:
	case null:											return null;
	case 'commit':										return item_commit();
	case 'accept':
	case 'unaccept':	require_once '_accept.inc'; 	return item_set_accept($_REQUEST['ACTION']);
	case 'remove':		require_once '_remove.inc';		return remove_element();
	case 'employees':									return employee_action();
	case 'combine':		require_once '_combine.inc';	return combine_element();
	case 'names':		require_once '_itemnames.inc';	return item_commit_names();
	}
}
function display_body() {
	switch ($_REQUEST['ACTION']) {
	default:
		require_once '_search_via_url.inc';
		search_via_url();
		not_found();
		return;

	case 'searchresult':
	case 'remove':
	case null:
		location_display_overview();
		return;

	case 'register':
	case 'form':
		location_display_form();
		return;

	case 'archive':
	case 'orchive':
	case 'commit':
	case 'photos':
	case 'news':
	case 'camera':
	case 'comments':
	case 'comment':
	case 'ratings':
	case 'rating':
	case 'rate':
	case 'votes':
	case 'combine':
	case 'description':
#		robot_action('noindex');
	case 'single':
		location_display_single();
		return;

	case 'fans':
		if (ROBOT) {
			not_found(); return;
		}
		location_display_single();
		return;

	case 'bioform':		require_once '_bio.inc'; 		 show_bio_form(); return;
	case 'combinewith':	require_once '_combine.inc';	 show_combine_with(); return;
	case 'names':		require_once '_itemnames.inc';	 item_display_names_overview(); return;
	case 'needupdate':	require_once '_needupdates.inc'; show_needupdates(); return;
	case 'employees':
			!$GLOBALS['commitresult']
		&&	$_REQUEST['SUBACTION'] !== 'form'
		?	display_employees()
		:	display_employee_form();
		return;
	}
}
function main_menu($location = null) {
	if (!$_REQUEST['sID']
	&&	in_array($_REQUEST['ACTION'],[null,'boarding','virtualtour'])
	) {
		layout_open_menu();
		layout_menuitem(Eelement_name('overview'),'/location',!$_REQUEST['ACTION']);
		layout_menuitem(Eelement_plural_name('boarding'),'/boarding');
		layout_menuitem(Eelement_plural_name('virtualtour'),'/virtualtour');
		if (have_user() && $_REQUEST['ACTION'] !== 'register') {
			layout_continue_menu();
			layout_menuitem(__C('action:add_location'),'/location/register');
		}
		layout_close_menu();
	}

	$is_admin = have_admin('location');

	layout_open_menu();
	if ($location) {
		$locationid = $location['LOCATIONID'];
		$locationhref = '/location/'.$locationid;
		if ($_REQUEST['ACTION'] !== 'form'
		&&	(	$is_admin
			||	(	!$location['ACCEPTED']
				&&	$location['USERID'] === CURRENTUSERID
				)
			)
		) {
			layout_menuitem(__C('action:change'),$locationhref.'/form');
			layout_menuitem(__C('action:change_information'),$locationhref.'/bioform');
		} elseif (am_employee('location',$locationid)) {
			layout_menuitem(__C('action:change_information'),$locationhref.'/bioform');
		}
		if ($is_admin) {
			if (!$location['ACCEPTED']) {
				if ($location['ORDERNAME']
				&&	$location['NAME']
				) {
					layout_menuitem(__C('action:accept'),$locationhref.'/accept');
				} else {
					layout_open_menuitem();
					?><span title="Behoeft aanvulling" class="unavailable help"><?= __C('action:accept') ?></span><?
					layout_close_menuitem();
				}
			} else {
				layout_menuitem(__C('action:reject'),$locationhref.'/unaccept');
			}
			layout_menuitem(Eelement_plural_name('name'),$locationhref.'/names');
			layout_menuitem(Eelement_plural_name('employee'),$locationhref.'/employees');
			layout_close_menuitem();
			layout_continue_menu();
			show_element_menuitems();
		}
	}
	layout_close_menu();
	if ($location
	&&	$is_admin
	) {
		require_once '_element_to_ticket_menu.inc';
		layout_open_menu();
		show_element_to_ticket_menu('location',$locationid,$location);
		layout_close_menu();
	}
}

function location_display_overview() {
	require_once '_locationlist.inc';
	layout_show_section_header();

	main_menu();

	$name = require_something_trim_clean_or_none($_REQUEST, 'NAME', utf8: true);

	?><form<?
	?> accept-charset="utf-8"<?
	?> onsubmit="return submitForm(this);"<?
	?> method="get"<?
	?> action="/location/searchresult"><?

	?><div class="block"><input placeholder="<?= __('action:search_for_location') ?>" type="search" autosave="locationname" name="NAME" id="locationname" autofocus="autofocus" required="required"<?
	if ($name) {
		?> value="<?= escape_utf8($name) ?>"<?
	}
	?>> <?
	?><input type="submit" value="<?= __('action:search') ?>" /></div></form><?

	if ($_REQUEST['ACTION'] === 'searchresult') {
		if ($name) {
			?><div class="block bold"><?= Eelement_name('search_term') ?>: &quot;<?= escape_utf8($name) ?>&quot;</div><?
			layout_open_box('location');
			layout_box_header(Eelement_plural_name('search_result'));
			$locationlist = new _locationlist;
			$locationlist->show_city = true;
			$locationlist->show_address = true;
			$locationlist->simple = true;
			$locationlist->name_or_address_like($name);
			if ($locationlist->query()) {
				if (!$locationlist->is_empty()) {
					$locationlist->display();
				} else {
					?><div class="block"><? __('search:info:nothing_found_LINE') ?></div><?
				}
			}
			layout_close_box();
			unset($locationlist);
		}
	} elseif (
		!$_REQUEST['ACTION']
	||	 $_REQUEST['ACTION'] === 'remove'
	) {
		if (have_user()) {
			require_once '_favourite.inc';
			show_favourites();
		}
	} else {
		not_found();
		return;
	}
}

function location_display_form() {
	if (!require_user()) {
		return;
	}
	require_once '_citylist.inc';
	require_once '_locationoptions.inc';
	require_once '_locationtype.inc';
	require_once '_presence.inc';

	$locationid = $_REQUEST['sID'];

#	layout_section_header(Eelement_name('location').' '.MIDDLE_DOT_ENTITY.' '.__(($locationid = $_REQUEST['sID']) ? 'action:change' : 'action:add'));
	layout_show_section_header(null,__(!empty($_REQUEST['sID']) ? 'action:change' : 'action:add'));

	main_menu();

	$location_admin = have_admin('location');

	if ($locationid) {
		$location = db_single_assoc('location','
			SELECT	location.*,
				(SELECT GROUP_CONCAT(PARENTID) FROM subof WHERE ELEMENT="location" AND CONCEPTID=LOCATIONID) AS PARENTIDS
			FROM location
			WHERE LOCATIONID='.$locationid
		);
		if ($location === false) {
			return;
		}
		if (!$location) {
			not_found(); return;
		}

		// must be admin,
		// or the location is unaccepted and currentuser
		//   was the one who entered it in the first place

		if (!$location_admin
		&&	($location['ACCEPTED'] || $location['USERID'] != CURRENTUSERID)
		) {
			return;
		}
	} else {
		$location = null;
	}
	$showupdatecheckbox =
		isset($location)
	&&	db_single('party','SELECT 1 FROM party WHERE LOCATIONID='.$locationid.' LIMIT 1',DB_USE_MASTER)
	&&	$location['LOCATIONTYPE']
	&&	!str_contains($location['LOCATIONTYPE'],',');

	include_js('js/form/location');
	?><form<?
	?> enctype="multipart/form-data"<?
	?> accept-charset="utf-8"<?
	?> id="elementform"<?
	?> autocomplete="off"<?
	?> onsubmit="return submitLocationForm(this,<?= $locationid ?>)"<?
	?> method="post"<?
	?> action="/location<?
	if ($locationid) {
		?>/<? echo $locationid;
	}
	?>/commit"><?
	?><input type="hidden" name="enc" value="&#129392;" /><?

	require_once '_ticket.inc';
	ticket_passthrough();

	require_once '_process_appic_changes.inc';
	passthrough_update_from_appic();

	require_once '_inquirymail.inc';
	show_inquiry_mail_question();

	$spec = explain_table('location');

	require_once '_uploadimage.inc';
	show_uploadimage_for_element_form('location',$locationid);

	require_once '_presence.inc';
	show_presence_search($location);

	layout_open_box('location');

	$boat = isset($location) && $location['LOCATIONTYPE'] === 'boat';

	layout_open_table('fw');
	layout_start_row();

	if ($location_admin) {
		require_once '_needupdates.inc';
		show_needupdate_form_part($location);
	}

	echo Eelement_name('title');
	layout_field_value();
	?><input<?
	?> placeholder="<?= __('location:info:title_field') ?>"<?
	?> id="locationtitle" required type="text" maxlength="<?= $spec['TITLE']->maxlength ?>" name="TITLE" value="<? if (isset($location)) echo escape_utf8($location['TITLE']); ?>" autofocus="autofocus"><?

	layout_restart_row();
		echo Eelement_name('name');
		layout_field_value();
		?><input placeholder="<?= __('location:info:name_field') ?>" type="text" maxlength="<?= $spec['NAME']->maxlength ?>" name="NAME" value="<? if (isset($location)) echo escape_utf8($location['NAME']); ?>" /><?

	layout_restart_row();
		echo Eelement_name('order_name');
		layout_field_value();
		?><input placeholder="<?= __('location:info:order_name_field') ?>" type="text" maxlength="<?= $spec['ORDERNAME']->maxlength ?>" name="ORDERNAME" id="locationname" value="<? if (isset($location)) echo escape_utf8($location['ORDERNAME']); ?>" /><?

	require_once '_alternate_name.inc';
	show_alternate_name_form('location', $locationid);

	layout_restart_row();
		echo Eelement_name('type');
		layout_field_value();
		show_locationtype_checkboxes(
			!empty($location['LOCATIONTYPE']) ? explode(',', $location['LOCATIONTYPE']) : null,
			onclick: 'changeLocType(this,'.($showupdatecheckbox ? 'true' : 'false').')'
		);

	layout_restart_row();
	?><label for="erotic"><?= __C('attrib:erotic') ?></label><?
	layout_field_value();
	?><label class="cbi <?
	if (empty($location['EROTIC'])) {
		?>not-<?
	}
	?>hilited"><?
	show_input([
		'type'		=> 'checkbox',
		'id'		=> 'erotic',
		'class'		=> 'upLite',
		'name'		=> 'EROTIC',
		'value'		=> 1,
		'checked'	=> !empty($location['EROTIC'])
	]);
	?> <?= __('erotic:info:mainly_erotic_events')
	?></label><?

	if ($showupdatecheckbox) {
		layout_restart_row(ROW_HIDDEN,'update-parties-row');
		echo Eelement_name('update');
		layout_field_value();
		?><label class="cbi not-bold-hilited"><?
		?><input data-current="<?= escape_specials($location['LOCATIONTYPE']) ?>" class="upLite" type="checkbox" name="UPDATEPARTIES" value="1" /> <?
		echo __('location:update_parties');
		?></label><?
	}
	layout_restart_row();
		echo Eelement_name('site');
		layout_field_value();
		?><input type="url" data-valid="url" maxlength="<?= $spec['SITE']->maxlength ?>" name="SITE" value="<? if (isset($location)) echo escape_utf8($location['SITE']); ?>" /><?
	layout_restart_row();
		echo Eelement_name('email');
		layout_field_value();
		show_input([
			'type'		=> 'email',
			'data-valid'	=> 'working-email',
			'spec'		=> $spec,
			'name'		=> 'EMAIL',
			'value'		=> $location,
		]);
	if (!show_presence_form_row()) {
		return;
	}
	layout_restart_row();
		echo Eelement_name('address');
		layout_field_value();
		?><input onchange="update_mapsqf(this.form)" type="text" name="ADDRESS" value="<? if (isset($location)) { echo escape_utf8($location['ADDRESS']); } ?>" /><?
		if (($allow_empty_addr = !empty($location['ALLOW_EMPTY_ADDR']))
		||	have_admin('location')
		) {
			?><br /><label><input onclick="clickAllowEmpty(this)"<?
			if ($allow_empty_addr) {
				?> checked<?
			}
			?> type="checkbox" name="ALLOW_EMPTY_ADDR" value="1"> <?= __('action:allow_empty_address') ?></label><?
		}
		?><br /><?
		?><label><?
		?><input<?
		if (!empty($location['ACCEPTED'])) {
			?> checked<?
		}
		?> onclick="if (this.checked) { this.form.ADDRESS.className = ''; }" type="checkbox" name="OVERLAP" value="1"> <?= __('action:allow_overlap')
		?></label><?

	layout_restart_row();
		echo Eelement_name('zipcode');
		layout_field_value();
		?><input class="id" onchange="update_mapsqf(this.form)" maxlength="<?= $spec['ZIPCODE']->maxlength ?>"  type="text" name="ZIPCODE" value="<? if (isset($location)) echo escape_utf8($location['ZIPCODE']); ?>" /><?

	layout_restart_row();
		echo Eelement_name('pobox');
		layout_field_value();
		?><input class="id" type="number" name="POBOX" value="<? if (!empty($location['POBOX'])) echo escape_specials($location['POBOX']); ?>" /><?

	layout_restart_row();
		echo Eelement_name('pobox_zipcode');
		layout_field_value();
		?><input class="id" type="text" name="POBOX_ZIPCODE" maxlength="<?= $spec['POBOX_ZIPCODE']->maxlength ?>" value="<? if (isset($location)) echo escape_utf8($location['POBOX_ZIPCODE']); ?>" /><?

	layout_restart_row();
		echo Eelement_name('country'); ?> &amp; <? echo Eelement_name('city');
		layout_field_value();
		display_dynamic_city_pair(
			cityid: 	$location['CITYID'] ?? 0,
			countryid:	$location['COUNTRYID'] ?? 0,
			flags:		UPDATE_MAP | CITY_REQUIRED | COUNTRY_REQUIRED,
		);

	layout_restart_row();
		echo Eelement_name('phone');
		layout_field_value();
		show_input([
			'type'			=> 'tel',
			'data-valid'	=> 'intl-tel',
			'maxlength'		=> $spec['PHONE']->maxlength,
			'name'			=> 'PHONE',
			'value_utf8'	=> $location['PHONE'] ?? null,
		]);

	layout_restart_row();
		echo Eelement_name('map');
		layout_field_value();
		require_once '_searchmap.inc';
		$address_parts = [];
		if (!empty($location['ADDRESS'])) {
			$address_parts[] = $location['ADDRESS'];
		}
		if (!empty($location['ZIPCODE'])) {
			$address_parts[] = $location['ZIPCODE'];
		}
		if (!empty($location['CITYID'])) {
			if ($city = memcached_city_info($location['CITYID'])) {
				$address_parts[] = $city['NAME'];
				if ($city['COUNTRYID']) {
					$address_parts[] = win1252_to_utf8(get_element_title('country', $city['COUNTRYID']));
				}
			}
		}
		show_search_map('location',$locationid,$location,13,implode(', ',$address_parts));
	layout_restart_row();
		echo Eelement_name('latitude');
		layout_field_value();
		show_gps(array(
			'required'	=> !$boat,
			'name'		=> 'LATITUDE',
			'value'		=> $location['LATITUDE'] ?? null
		));
	layout_restart_row();
		echo Eelement_name('longitude');
		layout_field_value();
		show_gps(array(
			'required'	=> !$boat,
			'name'		=> 'LONGITUDE',
			'value'		=> $location['LONGITUDE'] ?? null
		));
	require_once '_kvk.inc';
		show_kvk_form_row($location);
	layout_restart_row();
		echo Eelement_name('capacity');
		layout_field_value();
		show_input([
			'class'		=> 'right id',
			'type'		=> 'number',
			'name'		=> 'CAPACITY',
			'value'		=> $location && $location['CAPACITY'] ? $location['CAPACITY'] : null,
		]);
	layout_restart_row();
		echo Eelement_name('minimum_age');
		layout_field_value();
		require_once '_minimum_age.inc';
		show_minimum_age_input($location);

	layout_restart_row();
		?><label for="dead"><?= __C('status:closed') ?></label><?
		layout_field_value();
		?><input type="checkbox" id="dead" name="DEAD" value="1"<?
		if (!empty($location['DEAD'])) {
			?> checked="checked"<?
		}
		?>><?
	layout_stop_row();

	require_once '_followup.inc';
	show_followup_and_parent_form_rows('location', $locationid, $location);

	layout_start_row();
		echo Eelement_plural_name('genre');
	layout_field_value();
		$genres =
		require_once '_genrelist.inc';
		show_genre_selection(
			$location ? (db_boolean_hash('location_genre', 'SELECT GID FROM location_genre WHERE LOCATIONID = '.$locationid) ?: null) : null
		);
	layout_stop_row();

	if (have_admin('location')) {
		layout_start_row();
			echo __C('header:admin_comments');
			layout_field_value();
			?><textarea class="growToFit" name="ADMIN_TEXT" cols="50" rows="5"><?
			if (isset($location)) {
				echo escape_utf8($location['ADMIN_TEXT']);
			}
			?></textarea><?
		layout_restart_row();
			echo __C('header:admin_party_comments');
			layout_field_value();
			?><textarea class="growToFit" name="ADMIN_PARTY_TEXT" cols="50" rows="5"><?
			if (isset($location)) {
				echo escape_utf8($location['ADMIN_PARTY_TEXT']);
			}
			?></textarea><?

		if (have_admin('hide_stuff')) {
			require_once '_status_overlay.inc';
			show_status_checkboxes($location);
		}

		layout_stop_row();
	}
	require_once '_cameracontacts.inc';
	show_camreq_form_part($locationid ? $location : null);
	layout_close_table();

	layout_close_box();
	?><div class="block"><?
	?><input type="submit" value="<?= __(isset($location) ? 'action:change' : 'action:add') ?>" /><?
	?></div><?
	?></form><?
}

function location_display_single(): void {
	require_once '_locationpart.inc';
	require_once '_uploadimage.inc';
	require_once '_presence.inc';

	if (!($locationid = require_idnumber($_REQUEST,'sID'))) {
		return;
	}
	if (!($location = memcached_single_assoc_if_not_admin(
		'location',
		['location','city','contact_ticket'], '
		SELECT	location.*,
				city.NAME AS CITYNAME,
				city.COUNTRYID,country.SHORT,REGIONID,
				contact_ticket.STATUS AS TICKET_STATUS,
				(	SELECT GROUP_CONCAT(PARENTID)
					FROM subof
					WHERE ELEMENT = "location"
					  AND CONCEPTID = LOCATIONID
				) AS PARENTIDS
		FROM location
		LEFT JOIN city USING (CITYID)
		LEFT JOIN country USING (COUNTRYID)
		LEFT JOIN contact_ticket ON contact_ticket.TICKETID = location.TICKETID
		WHERE location.LOCATIONID = '.$locationid,
		TEN_MINUTES))
	) {
		$location !== false && not_found();
		return;
	}

	$location_admin = have_admin('location');

	if (!available_meta()) {
		include_meta('description', __('metad:location:single', DO_UBBFLAT, ['LOCATIONID' => $locationid]));
	}

	if (!$location['ACCEPTED']
	&&	$location['USERID'] != CURRENTUSERID
	&&	!$location_admin
	) {
		register_error('location:error:not_approved_yet_LINE', ['ID' => $locationid]);
		return;
	}

	layout_show_section_header($location);

	main_menu($location);

	#show_check_mark_for_aspiring();

	if (have_user()) {
		require_once '_ticketlist.inc';
		show_connected_tickets();

		if (have_admin('logbook')) {
			require_once '_logbook.inc';
			show_logbook();
		}
		require_once '_favourite.inc';
		show_marking_menu();
	}

	$mapping = 	!empty($location['LONGITUDE'])
			&&	!empty($location['LATITUDE'])
			&&	!preg_match('"^0*(?:\.0*)?$"', $location['LONGITUDE'])
			&&	!preg_match('"^0*(?:\.0*)?$"', $location['LATITUDE'])
			||	$location['ZIPCODE']
			||	$location['ADDRESS'];

	if (false === ($as_org = memcached_single_array(['connect', 'party'], '
		SELECT	COUNT(*),
				COUNT(IF(ACCEPTED = 1, 1, NULL)),
				COUNT(IF(STAMP >= '.($pivot = strtotime('-2 days', TODAYSTAMP)).', 1, NULL)),
				COUNT(IF(STAMP >= '.($pivot = strtotime('-2 days', TODAYSTAMP)).' AND ACCEPTED = 1,1, NULL))
		FROM connect
		JOIN party ON PARTYID = ASSOCID
		WHERE MAINTYPE = "locasorg"
		  AND MAINID  ='.$locationid.'
		  AND ASSOCTYPE = "party"'))
	) {
		if ($as_org !== false) {
			register_generic_error();
		}
		return;
	}

	[$as_org_total,$as_org_total_ok,$as_org_future,$as_org_future_ok] = $as_org;

	if ($party_admin = have_admin('party')) {
		$may_org_future = $as_org_future;
		$as_org_total_ok = $as_org_total;
		$as_org_future_ok = $as_org_future;
	}

	// SHOOTCNT
	if (!($shootinfo = db_single_assoc(['image','connect','gallery'],'
		SELECT	COUNT(DISTINCT party.PARTYID) AS SHOOT_CNT,
				COUNT(DISTINCT IF(image.HIDDEN=0,IMGID,NULL)) AS PHOTO_CNT
		FROM party
		JOIN image USING (PARTYID)
		JOIN gallery USING (GALLERYID)
		WHERE VISIBLE="yes"
		  AND LOCATIONID='.$locationid))
	) {
		$shootinfo !== false && register_generic_error();
		return;
	}
	// PARTYCNT
	if (!($cnts = memcached_single_assoc(['party','connect'], '
		SELECT	COUNT(IF(STAMP>='.$pivot.',1,NULL)) AS FUTURE_CNT,
				COUNT(IF(STAMP>='.$pivot.' AND DELETED = 0 AND ACCEPTED = 1 AND CANCELLED = 0 AND MOVEDID = 0,1,NULL)) AS FUTURE_OK_CNT,
				COUNT(*) AS TOTAL_CNT,
				COUNT(IF(DELETED = 0 AND ACCEPTED=1 AND CANCELLED=0 AND MOVEDID=0,1,NULL)) AS TOTAL_OK_CNT
		FROM party  
		WHERE LOCATIONID='.$locationid.'
		OR	LIVESTREAM="only" AND NOT ISNULL((
					SELECT 1
					FROM connect
					WHERE	MAINTYPE="party"
						AND	MAINID=PARTYID
						AND	ASSOCTYPE="location"
						AND	ASSOCID='.$locationid.'
					LIMIT 1)
			)',
		TEN_MINUTES, null,
		partylist_refresh_needed() ? DB_FRESHEN_MEMCACHE : 0))
	) {
		$cnts !== false && register_generic_error();
		return;
	}
	if (!$party_admin) {
		$may_future = $cnts['FUTURE_CNT'];
		$cnts['TOTAL_CNT']  = $cnts['TOTAL_OK_CNT'];
		$cnts['FUTURE_CNT'] = $cnts['FUTURE_OK_CNT'];
	}
	$cnts['PAST_CNT'] = $cnts['TOTAL_CNT'] - $cnts['FUTURE_CNT'];

	require_once '_favourite.inc';
	$fancnt = get_fan_count('location',$locationid);

	$am_employee = am_employee('location',$locationid);
	require_once '_news.inc';
	[$total_news,$teaser_news] = get_news_counts();
	// CAMERAREQUESTS
	if ($am_employee
	||	have_admin('camerarequest')
	) {
		$caminfo = db_single(array('camerarequest','party'),'
			SELECT 1
			FROM party
				LEFT JOIN camerarequest ON camerarequest.PARTYID=party.PARTYID
				LEFT JOIN contact_ticket ON ELEMENT="camerarequest" AND ID=party.PARTYID
			WHERE LOCATIONID='.$locationid.'
			  AND (NOT ISNULL(camerarequest.PARTYID) OR NOT ISNULL(ID))
			LIMIT 1'
		);
	} else {
		$caminfo = false;
	}

	require_once '_admin_texts.inc';
	show_admin_texts('location', $locationid, $location);

	require_once '_star.inc';
	$is_fav = check_favourite();

	require_once '_status_overlay.inc';
	open_status_overlay($location);

	?><article itemscope itemtype="https://schema.org/EventVenue"><?

	?><meta itemprop="url" content="<?= FULL_HOST, get_element_href('location',$locationid) ?>" /><?

	if ($location['CAPACITY']) {
		?><meta itemprop="maximumAttendeeCapacity" content="<?= $location['CAPACITY'] ?>" /><?
	}

	require_once '_drag.inc';
	open_drag();

	layout_open_box($location['ACCEPTED'] ? 'location' : 'unaccepted location');
	layout_open_box_header($is_fav ? BOX_HEADER_FAN : 0,'box-header');
	?><h1 itemprop="name"><?
	echo escape_utf8($location['TITLE']);
	?></h1><?

	ob_start();
	show_dead($location);
	$dead = ob_get_flush();

	echo get_header_star();

	require_once '_maplink.inc';
	?> <?
	show_map_link($location, 16, 'minimap');
	show_distance_to_item($location, ' ');

	require_once '_virtualtour.inc';
	if ($tourbutton = get_virtualtour_button('location',$locationid)) {
		?> <?
		?><div class="ib insidehzw"><?
		?><div class="abs middle" style="margin-top:-20px;"><?
		echo $tourbutton;
		?></div><?
		?></div><?
	}

	if ($location_admin) {
		if ($location['NEEDUPDATE']) {
			require_once '_needupdates.inc';
			?> <?
			show_new_profile_info('location', $locationid);
		}
		require_once '_needupdates.inc';
		show_needupdate_mark($location);
		require_once '_checkup.inc';
		show_checkup_mark($location);
	}

	// RIGHT HEADER
	if ($total_news
	||	$caminfo
	||	$shootinfo['SHOOT_CNT']
#	||	$tourbutton
	||	$fancnt
	) {
		layout_continue_box_header();
		layout_open_menu(MENU_IN_HEADER);
		// CAMERAREQUESTS
		if ($caminfo) {
			layout_open_menuitem();
			[$linkopen,$linkclose] = get_action_open_close('camera');
			echo $linkopen,element_plural_name('camerarequest'),$linkclose;
			layout_close_menuitem();
		}
		// PHOTOS
		if ($shootinfo['SHOOT_CNT']) {
			layout_open_menuitem();
			[$linkopen,$linkclose] = get_action_open_close('photos');
			echo $linkopen,element_plural_name('photo'),$linkclose;
			layout_close_menuitem();
		}
		// NEWS
		if ($total_news) {
			layout_open_menuitem();
			if ($total_news == $teaser_news) {
				$linkopen = '<a href="#news">';
				$linkclose = '</a>';
			} else {
				[$linkopen,$linkclose] = get_action_open_close('news');
			}
			echo $linkopen,element_name('news'),$linkclose;
			layout_close_menuitem();
		}
/*		if ($tourbutton) {
			layout_next_menuitem();
			?><div class="ib" style="width:60px"><?
			?><span class="abs" style="margin-top:-53px"><?= $tourbutton ?></span><?
			?></div><?
			layout_close_menuitem();
		}*/
		# FANS
		if ($fancnt) {
			layout_open_menuitem();
			[$fanlinkopen,$fanlinkclose] = get_action_open_close('fans');
			echo $fanlinkopen,$fancnt,' ',element_name('fan',$fancnt),$fanlinkclose;
			layout_close_menuitem();
		}
		layout_close_menu();
	}
	layout_close_box_header();

	if ($location
	&&	$location_admin
	) {
		require_once '_presence.inc';
		show_presence_search($location);
	}

	require_once '_appic.inc';
	show_appic_link();

	if ($location['LATITUDE']) {
		?><span itemprop="geo" itemscope itemtype="https://schema.org/GeoCoordinates"><?
			?><meta itemprop="latitude" content="<?= $location['LATITUDE'] ?>" /><?
			?><meta itemprop="longitude" content="<?= $location['LONGITUDE'] ?>" /><?
		?></span><?
	}

	$tours = memcached_rowuse_array(['virtualtour','virtualroom'],'
		SELECT TOURID,AS_IMAGE,COUNT(*) AS ROOMS,CSTAMP
		FROM virtualtour
		JOIN virtualroom USING (TOURID)
		WHERE ELEMENT="location"
		  AND ID='.$locationid.'
		  AND VISIBLE=1
		GROUP BY TOURID
		ORDER BY TOURID DESC',
		HALF_HOUR
	);
/*	if ($tours) {
		foreach ($tours as $tour) {
			if ($tour['AS_IMAGE']) {
				break;
			}
		}
		if ($tour['AS_IMAGE']) {
			$tourbutton = get_virtualtour_image();
			extract($tour);
			?><div class="relative centered r block" style="max-width:100%;width:480px"><?
				?><a class="middle centered" href="<?= get_element_href('virtualtour',$TOURID,'#tour') ?>"><?

					?><div class="vtnewv2 abs z2" style="top:5px;right:5px"><?= $tourbutton ?></div><?
					global $head;
					ob_start();
					if (!have_og('og:image')) {
						include_og('og:image',VT_HOST.'vt/'.$TOURID.'/'.mt_rand(1,$ROOMS).'/t_small.jpg');
					}
					$head .= ob_get_clean();
					?><img itemprop="image" class="zoomover fw mw100" src="<?= VT_HOST ?>/vt/<?= $TOURID ?>/<?= mt_rand(1,$ROOMS) ?>/t_regular<?= is_high_res() ?>.jpg" /><?
					?><div class="center fw abs z2" style="font-size:150%;bottom:10px"><?
						?><div class="ib rvbg"><?
							?><div><?= escape_utf8($location['NAME']) ?></div><?
							?><div style="font-size:50%"><?= element_name('virtualtour') ?></div><?
						?></div><?
					?></div><?
				?></a><?
			?></div><?
		} else {
			$tour = null;
		}
	}
	*/

	if ($img = uploadimage_get('location', $locationid)) {
		uploadimage_show_from_img(
			$img,
			UPIMG_SCHEMA
			| UPIMG_SHOW_HISTORY
			| UPIMG_LINK_ORIGINAL
			| (SMALL_SCREEN ? UPIMG_MAX_WIDTH : UPIMG_R)
		);
	}

	require_once 'defines/processlist.inc';
	PROCLIST_DO_PRIORITIES && show_proclist_priority();

	if (/*	$isinfoadmin
	&&*/	$location['EROTIC']
	) {
		?><div class="block"><span class="erobg"><?= __('erotic:info:mainly_erotic_events') ?></span></div><?
	}


#	$is_boat = $location['LOCATIONTYPE'] == 'boat';

	if ($location['DEAD']
	&&	!$location['FOLLOWUPID']
	) {
		?><div class="block"><?= __('location:info:ceased_to_exist_LINE',['DATE'=>'']
//			array('DATE'=>$organization['DEADSTAMP'] > 1 ? _date_get($organization['DEADSTAMP']) : 0)
		); ?></div><?
	}

	require_once '_followup.inc';
	show_successors('location', $locationid, $location['FOLLOWUPID']);
	show_parents('location', $locationid, 'location', $location['PARENTIDS']);

	$list = new deflist('deflist vtop');
	// ADDRESS
	if ($location['ADDRESS']
	||	$location['ZIPCODE']
	||	$location['CITYNAME']
	) {
		ob_start();
		?><span itemprop="address" itemscope itemtype="https://schema.org/PostalAddress"><?
		if ($location['ADDRESS']) {
			?><span itemprop="streetAddress"><?= escape_utf8($location['ADDRESS']); ?></span><br /><?
		}
		if ($location['ZIPCODE']) {
			?><span itemprop="postalCode"><?= escape_utf8($location['ZIPCODE']); ?></span><br /><?
		}
		if ($location['CITYID']) {
			?><span itemprop="addressLocality"><?
			echo get_element_link('city', $location['CITYID'], $location['CITYNAME']);
			?></span><?
			if ($location['REGIONID']) {
				?><br /><?
				?><span itemprop="addressRegion"><?= get_element_link('region', $location['REGIONID']) ?></span><?
			}

			require_once '_countryflag.inc';
			?><br /><?
			?><span itemprop="addressCountry" itemscope itemtype="https://schema.org/Country"><?
			?><span itemprop="name"><?= get_element_link('country', $location['COUNTRYID']) ?></span><?
			?><meta itemprop="alternateName" content="<?= $location['SHORT'] ?>" /><?
			?></span> <?
			show_country_flag($location['COUNTRYID'],'light',false,false);

/*			$currentcity = get_current_city();
			if ($currentcity
			&&	$currentcity['COUNTRYID'] != $location['COUNTRYID']
			) {
				require_once '_countryflag.inc';
				?><br /><?
				?><span itemprop="addressCountry"><?= get_element_link('country',$location['COUNTRYID']) ?></span> <?
				show_country_flag($location['COUNTRYID'],'light',false,false);
			}*/
		}
		$list->add_row(Eelement_name('address'), ob_get_clean());
	}
	if ($location_admin
	&&	(require_once '_addresscheck.inc')
	&&	($same_address = find_at_same_address('location', $location['ADDRESS'], $location['CITYID'], $location, 0, true))
	) {
		$sames = [];
		foreach ($same_address as $sameid => $same) {
			ob_start();
			echo get_element_link('location', $sameid);
			show_dead('location', $sameid);
			if ($same['LSTAMP']) {
				?><span class="small">, <span class="nowrap"><?= __('time:till') ?> <?
					printf('%04d-%02d-%02d', ..._getdate($same['LSTAMP'], 'UTC'));
				?></span></span><?
			}
			$sames[] = ob_get_clean();
		}
		$list->set_row_class('light');
		$list->add_row(
			Eelement_name('address_identical'),
			'<ul><li>'.(!isset($sames[1]) ? $sames[0] : implode('</li><li>', $sames)).'</li></ul>'
		);
#		show_same_addresses($list, $
	}
	// TYPE
	if ($location_admin
	&&	$location['LOCATIONTYPE']
	) {
		ob_start();
		$types = explode(',',$location['LOCATIONTYPE']);
		foreach ($types as &$type) {
			$type = __('locationtype:'.$type);
		}
		unset($type);
		sort($types);
		echo implode(', ',$types);

		$list->add_row(Eelement_name('type'),ob_get_clean());
	}
	// PHONE
	require_once '_phone.inc';
	add_phone_to_list($list, $location, $dead);

	if ($location['KVKNUM']) {
		require_once '_kvk.inc';
		show_kvk_row($list, $location);
	}
	// SITE, EMAIL
	require_once '_site.inc';
	show_site_and_email_rows($list,$location);

	show_presence_row($list);

	if ($location['CAPACITY']) {
		$list->add_row(Eelement_name('capacity'),$location['CAPACITY']);
	}

	require_once '_minimum_age.inc';
	show_minimum_age($list, $location);

	if ($boardings = memcached_simple_hash(array('boarding','party','location'),'
		SELECT DISTINCT BOARDINGID,boarding.CITYID
		FROM boarding
		JOIN party USING (BOARDINGID)
		JOIN location USING (LOCATIONID)
		WHERE LOCATIONID='.$locationid.'
		ORDER BY boarding.ADDRESS,boarding.NAME',
		TEN_MINUTES
	)) {
		ob_start();
		?><ul class="nostyle"><?
		foreach ($boardings as $boardingid => $cityid) {
			?><li><?
			echo get_element_link('boarding',$boardingid);
			?>, <?
			echo get_element_link('city',$cityid);
			?></li><?
		}
		?></ul><?
		$list->add_row(Eelement_name('boarding',count($boardings)),ob_get_clean());
	}

	if (have_admin('party')) {
		require_once '_facebook.inc';
		add_facebook_rows($list);
	}
	if (have_admin('video')) {
		require_once '_youtube.inc';
		add_youtube_rows($list);
	}

	$list->display();

	require_once '_genrelist.inc';
	show_genres_block($location);

	show_predecessors('location',$locationid);

	show_children('location',$locationid,'location');
	show_children('location',$locationid,'organization');

	require_once '_itemnames.inc';
	show_item_renames('location', $locationid);

	require_once '_alternate_name.inc';
	show_alternate_names('location', $locationid, $location['NAME']);

	require_once '_employees.inc';
	show_employees();

	if (have_admin('relation')) {
		show_same_facebook_connected();

		require_once '_relationlist.inc';
		show_relations();
	}

	require_once '_bio.inc';
	show_bio();

	require_once '_affiliates.inc';
	show_booking_com('location', $locationid, $location);

	layout_display_alteration_note($location, true);
	layout_close_box();

	if (may_speak($location)) {
		vote_display_choices();
	}
	require_once '_ratinglist.inc';
	$ratinglist = new _ratinglist;
	$ratinglist->item($location);
	$ratinglist->show_form();

	if (have_admin(['location', 'camerarequest'])) {
		require_once '_cameracontacts.inc';
		show_cameracontacts($location);
	}

	drag_store();

#	require_once '_agenda_source.inc';
#	show_agenda_source();

	layout_open_box('location agenda', 'agenda');
	// AGENDA
	layout_open_box_header();
	?><h2><?
	$future = $cnts['FUTURE_CNT'];

	if (have_user()) {
		echo Eelement_name('agenda');
	} else {
		echo Eelement_name('party_agenda');
		?> <?
		echo escape_utf8($location['NAME']);
	}
	?></h2><?

	$parts = [];
	if (!ROBOT) {
		$parts[] = get_ical_feed_link();
		if (SHOW_FEED_ICON) {
			$parts[] = get_feed('agenda',FEED_ICON);
		}
	}
	if ($am_employee || have_admin() || have_relation()) {
		ob_start();
		?><a href="/party/register?LOCATIONID=<?= $locationid ?>"><?= __('action:add_party'); ?></a><?
		$parts[] = ob_get_clean();
	}
	if ($cnts['PAST_CNT']) {
		[$linkopen, $linkclose] = get_action_open_close('archive', null, element_name('agenda_archive').' '.escape_utf8($location['NAME']));
		$parts[] = $linkopen.element_name('archive').$linkclose;
	}
	ob_start();
	if (have_admin('party')) {
		require_once '_processlist.inc';
		processlist_show_closer('location',$locationid);
	}
	$closer = ob_get_clean();

	if ($parts) {
		layout_continue_box_header();
		?><nav><?= implode(' '.MIDDLE_DOT_ENTITY.' ',$parts),$closer	?></nav><?
	}
	layout_close_box_header();

	if ($future || $as_org_future_ok || !empty($may_org_future) || !empty($may_future)) {
		require_once '_partylist.inc';
		$partylist = new _partylist;

		$partylist->show_people = !SMALL_SCREEN;
		$partylist->show_lineups =
		$partylist->show_contests =
		$partylist->show_date =
		$partylist->show_camera =
		$partylist->show_stars =
		$partylist->show_buddy_hearts =
		$partylist->hide_separators =
		$partylist->one_table =
		$partylist->hide_month_header =	true;
		$partylist->order_chronologically();
		$partylist->select_future(null,true);

		if ($as_org_future_ok) {
			$asorglist = clone $partylist;
		}
	}
	if ($future || !empty($may_future)) {
		$partylist->on_location_including_livestreams($locationid);
		$partylist->hide_flag = true;
		if ($_REQUEST['ACTION'] === 'single') {
			$partylist->show_vevent = true;
		}
		if ($partylist->query()) {
			$partylist->display();
		}
	}
	if (empty($partylist) || !$partylist->size()) {
		require_once '_lastparty.inc';
		show_last_party();
	}

	if ($_REQUEST['ACTION'] !== 'archive'
	&&	$cnts['PAST_CNT']
	) {
		[$linkopen,$linkclose] = get_action_open_close('archive',null,element_name('agenda_archive').' '.escape_utf8($location['NAME']));
		?><div class="light6 block" style="margin-top:1em"><i><?
		echo $linkopen,' ',__('action:show_archive'),', ',$cnts['PAST_CNT'],' ',element_name('event',$cnts['PAST_CNT']),$linkclose;
		?></i></div><?
	}

	if ($as_org_total_ok) {
		[$linkopen,$linkclose] = get_action_open_close('orchive',null,element_name('agenda_elsewhere_archive').' '.escape_utf8($location['NAME']));

		layout_box_header(
			Eelement_name('elsewhere'),
			$linkopen.element_name('archive').$linkclose,
			'elsewhere'
		);

		if ($as_org_future_ok) {
			$asorglist->as_locasorg($locationid);
			$asorglist->show_location = true;

			$asorglist->query();
			$asorglist->display();
		}
		if (!$as_org_future_ok || !$asorglist->size()) {
			require_once '_lastparty.inc';
			show_last_party('locasorg');
		}
	}

	layout_close_box();
	layout_open_box('location');

	// STATISTICS
	layout_box_header(Eelement_plural_name('statistic'));

	layout_open_table('fw vtop default', max_cols: SMALL_SCREEN ? 1 : 3);

	layout_start_reverse_row(!$cnts['TOTAL_CNT'] ? ROW_LIGHT : 0);
		$linkopen = $cnts['TOTAL_CNT'] ? (!$future ? get_action_open_close('archive')[0] : '<a href="#agenda">') : null;
		$linkclose = $linkopen ? '</a>' : null;
		echo $cnts['TOTAL_CNT'] ? $linkopen.$cnts['TOTAL_CNT'].$linkclose : __('counter:none');
		layout_value_field();
		echo $linkopen,element_name('party',$cnts['TOTAL_CNT']),$linkclose;
	layout_restart_reverse_row(!$future ? ROW_LIGHT : 0);
		$linkopen = $future ? '<a href="#agenda">' : null;
		$linkclose = $linkopen ? '</a>' : null;
		echo $future ? $linkopen.$future.$linkclose : __('counter:none');
		layout_value_field();
		echo $linkopen,__('when:in_the_future'),$linkclose;
	if ($cnts['PAST_CNT']) {
		layout_restart_reverse_row();
		// ARCHIVE
		[$linkopen,$linkclose] = get_action_open_close('archive');
		echo $linkopen,$cnts['PAST_CNT'],$linkclose;
		layout_value_field();
		echo $linkopen,__('when:in_the_past'),$linkclose;
	}
	if ($as_org_total_ok) {
		layout_restart_reverse_row();
		// ORCHIVE
		[$linkopen,$linkclose] = get_action_open_close('orchive');
		echo $linkopen,$as_org_total_ok,$linkclose;
		layout_value_field();
		echo $linkopen,element_name('party_elsewhere',$as_org_total_ok),$linkclose;
	}
	show_views_restart_row();
	if ($total_news) {
		// NEWS
		layout_restart_reverse_row();
		if ($total_news != $teaser_news) {
			[$linkopen,$linkclose] = get_action_open_close('news');
		} else {
			$linkopen = '<a href="#news">';
			$linkclose = '</a>';
		}
		echo $linkopen,$total_news,$linkclose;
		layout_value_field();
		echo $linkopen,element_name('news_message',$total_news),$linkclose;
	}
	if ($tours) {
		layout_restart_reverse_row();
		$tourcnt = count($tours);
		if ($tourcnt == 1) {
#			$linkopen = '<a onclick="unhide(\'vts\');return false">';
			$linkopen = '<a href="'.get_element_href('virtualtour',$tours[0]['TOURID']).'">';
			$linkclose = '</a>';
			echo $linkopen,$tourcnt,$linkclose;
			layout_value_field();
			echo $linkopen,element_name('virtualtour',$tourcnt),$linkclose;
		} else {
			require_once '_bubble.inc';
			$bubble = new bubble(CLICK_KEEP_BUBBLE | HELP_CATCHER);
			ob_start();
			foreach ($tours as $tour) {
				?><div class="right"><?
				?><a href="<?= get_element_href('virtualtour',$tour['TOURID']) ?>"><? _date_display($tour['CSTAMP']) ?> &rarr;</a><?
				?></div><?
			}
			$bubble->content(ob_get_clean());
			$bubble->catcher($tourcnt);
			$bubble->display();
			layout_value_field();
			$bubble->catcher(element_name('virtualtour',$tourcnt));
			$bubble->display();
#			echo $linkopen,element_name('virtualtour',$tourcnt),$linkclose;
		}
	}
	if ($shootinfo['SHOOT_CNT']) {
		// PHOTOS
		[$linkopen,$linkclose] = get_action_open_close('photos');
		layout_restart_reverse_row();
		echo $linkopen,$shootinfo['SHOOT_CNT'],$linkclose;
		layout_value_field();
		echo $linkopen,element_name('photoshoot',$shootinfo['SHOOT_CNT']),$linkclose;

		layout_restart_reverse_row();
		echo $linkopen,$shootinfo['PHOTO_CNT'],$linkclose;
		layout_value_field();
		echo $linkopen,element_name('photo',$shootinfo['PHOTO_CNT']),$linkclose;
	}
	require_once '_videototal.inc';
	show_video_statistics_row();

	require_once '_background_queries.inc';
	$visitorcnts = get_bgquery(BGQRY_VISITORS,'location',$locationid);
	if ($visitorcnts) {
		[$total,$uniq] = keyval($visitorcnts);
		if ($total) {
			layout_restart_reverse_row();
			echo $total;
			layout_value_field();
			echo element_name('visitor',$total);

			[$linkopen,$linkclose] = get_action_open_close('visitmap');
			?> <?= MIDDLE_DOT_ENTITY ?> <?
			echo $linkopen,element_name('origin'),$linkclose;

			if ($uniq) {
				layout_restart_reverse_row();
				echo $uniq;
				layout_value_field();
				echo element_name('unique_visitor',$uniq);
			}
/*			layout_restart_reverse_row();
			echo round($total / $cnts['TOTAL_OK_CNT']);
			layout_value_field();
			echo element_name('average');*/
/*			$median = memcached_single(
				array('party','going','location','user_account'),'
				SELECT COUNT(*)
				FROM user_account
				JOIN going ON going.USERID=user_account.USERID AND STATUS!="permbanned"
				JOIN party USING (PARTYID)
				WHERE ACCEPTED=1
				  AND CANCELLED=0
				  AND MOVEDID=0
				  AND LOCATIONID='.$locationid.'
				GROUP BY PARTYID
				LIMIT '.floor($cnts['TOTAL_OK_CNT'] / 2).',1'
			);
			print_rr($median);*/
		}
	}
	if ($fancnt) {
		[$linkopen,$linkclose] = get_action_open_close('fans');
		layout_restart_reverse_row();
		echo $linkopen,$fancnt,$linkclose;
		layout_value_field(get_partyflock_icon());
		echo $linkopen,element_name('fan',$fancnt),$linkclose;
/*		list($linkopen,$linkclose) = get_action_open_close('fanmap');
		?> <?= MIDDLE_DOT_ENTITY ?> <?
		echo $linkopen,element_name('origin'),$linkclose;*/
	}

	require_once '_fblikes.inc';
	show_fb_likes();

	require_once '_album.inc';
	album_show_stats_row();
	require_once '_commentlist.inc';
	_commentlist::show_stats_row();
	_ratinglist::show_stats_row();
	vote_show_row();
	layout_stop_row();
	layout_close_table();
	layout_close_box();

	close_drag();

	if ($_REQUEST['ACTION']) {
		require_once '_fans.inc';
		switch ($_REQUEST['ACTION']) {
		case 'camera':
			if ($caminfo) {
				require_once '_camerarequests.inc';
				show_camerarequests('location',$locationid);
			} else {
				http_response_code(404);
			}
			break;
		case 'news':
			if ($total_news) {
				require_once '_news.inc';
				show_news_teasers();
			} else {
				http_response_code(404);
			}
			break;
		case 'archive':
			if ($cnts['PAST_CNT']) {
				show_location_archive($location);
			} else {
				http_response_code(404);
			}
			break;
		case 'orchive':
			if ($as_org_total_ok) {
				show_location_archive($location,$as_org_total_ok);
			} else {
				http_response_code(404);
			}
			break;
		case 'photos':
			if ($shootinfo['SHOOT_CNT']) {
				require_once '_galleryoverview.inc';
				$galleryoverview = new galleryoverview;
				$galleryoverview->show_location($locationid);
				$galleryoverview->query();
				$galleryoverview->show_thumbs();
				$galleryoverview->show_shoots();
			} else {
				http_response_code(404);
			}
			break;
		case 'fans':
			if ($fancnt) {
				show_fans('location',$locationid,$location);
			} else {
				http_response_code(404);
			}
			break;

/*		case 'fanmap':
		case 'fancity':
			if ($fancnt) {
				require_once '_usermap.inc';
				show_usermap_overview('location',$locationid);
			}
			break;

		case 'visitmap':
			require_once '_usermap.inc';
			show_usermap_overview('location',$locationid,VISITOR_MAP);//,isset($tmpmaybe));
			break;*/

		case 'votes':
			vote_show_box();
			break;

		default:
			$showcmts = true;
			break;
		}
	} else {
		$showcmts = true;
	}
	if (isset($showcmts)) {
		require_once '_videosandphotos.inc';
		show_videos_and_photos('location',$locationid);

		require_once '_showcase.inc';
		show_presence_showcase();

		show_news_teasers();
		$ratinglist->display();
		$cmts = new _commentlist;
		$cmts->item($location);
		$cmts->display();
	}
	?></article><?

	close_status_overlay();

	counthit('location',$locationid);
}

function actual_location_commit(): int|false {
	require_once '_presence.inc';
	require_once '_elementchanged.inc';
	require_once '_inquirymail.inc';
	require_once '_followup.inc';
	require_once '_phone.inc';
	require_once '_minimum_age.inc';

	if (!require_user()
	||	!optional_number($_REQUEST,'LOCATIONID')
	||	!require_anything_trim($_POST,'CAMREQ_NAME')
	||	!optional_number($_POST,'CAMREQ_USERID')
	||	!require_something_trim($_POST, 'TITLE', null, null, utf8: true)
	||	!require_anything_trim($_POST, 'ADDRESS', utf8: true)
	||	!require_anything_trim($_POST, 'ZIPCODE', utf8: true)
	||	!require_anything_trim($_POST, 'POBOX_ZIPCODE', utf8: true)
	||	!require_anything_trim($_POST, 'PHONE', utf8: true)
	||	!require_number_or_empty($_POST, 'KVKNUM', utf8: true)
	||	false === require_number($_POST, 'POBOX')
	||	false === require_number($_POST, 'CITYID')
	||	false === require_number($_POST, 'CAPACITY')
	||	!require_anything_trim($_POST,'SITE', utf8: true)
	||	!require_minimum_age_or_none($_POST, 'MIN_AGE')
	||	!require_number_or_empty($_POST, 'FOLLOWUPID')
	||	!require_number_array($_POST, 'PARENTID', allow_empty: true)
	||	(	$_REQUEST['sID']
		?	(	!require_anything_trim($_POST, 'CAMREQ_EMAIL', utf8: true)
			||	!require_anything_trim($_POST, 'EMAIL', utf8: true)
			)
		:	(	!optional_working_email($_POST, 'CAMREQ_EMAIL', utf8: true)
			||	!optional_working_email($_POST, 'EMAIL', utf8: true)
			)
		)
	||	!require_array($_POST,'LOCATIONTYPE')
	) {
		return false;
	}
	$loctype = isset($_POST['LOCATIONTYPE']) && is_array($_POST['LOCATIONTYPE']) ? implode(',',$_POST['LOCATIONTYPE']) : null;
	if ($loctype === 'boat'
	?	(	!require_anything_trim($_POST, 'LATITUDE')
		||	!require_anything_trim($_POST, 'LONGITUDE')
		||	!require_anything_trim($_POST, 'ADDRESS', utf8: true)
		)
	:	(	!require_lng_or_lat($_POST, 'LONGITUDE')
		||	!require_lng_or_lat($_POST, 'LATITUDE')
		||	(	!isset($_POST['ALLOW_EMPTY_ADDR'])
			&&	!require_something_trim($_POST, 'ADDRESS', null, null, utf8: true)
			)
		)
	) {
		return false;
	}
	if ($location_admin = have_admin('location')) {
		if (!require_something_trim($_POST, 'NAME', null, null, utf8: true)
		||	!require_something_trim($_POST, 'ORDERNAME', null, null, utf8: true)
		) {
			return false;
		}
	}
	if ($locationid = $_REQUEST['sID']) {
		if (!($oldlocation = db_single_assoc('location', 'SELECT * FROM location WHERE LOCATIONID='.$locationid, DB_USE_MASTER))) {
			if ($oldlocation === false) {
				return false;
			}
			not_found();
			return false;
		}
		if (!$oldlocation['ACCEPTED']
		&&	$oldlocation['USERID'] !== CURRENTUSERID
		&&	!require_admin('location')
		) {
			return false;
		}
	}
	if (($followupid = $_POST['FOLLOWUPID'] ?: 0)
	&&	(	empty($oldlocation)
		||	$oldlocation['FOLLOWUPID'] != $followupid
		)
	) {
		if (false === ($conflictid = db_single('location','SELECT LOCATIONID FROM location WHERE FOLLOWUPID = '.$followupid, DB_USE_MASTER))) {
			return false;
		}
		if ($conflictid) {
			register_warning('location:error:already_has_predecessor_LINE', DO_UBB, [
				'PREDECESSORID'	=> $conflictid,
				'LOCATIONID'	=> $followupid
			]);
		}
		if ($locationid
		&&	$followupid
		&&	!isset($_POST['OVERLAP'])
		) {
			if (false === ($first_of_follow = db_single_assoc('party','
				SELECT PARTYID,NAME,STAMP
				FROM party
				WHERE LOCATIONID='.$followupid.'
				  AND CANCELLED=0
				  AND MOVEDID=0
				ORDER BY STAMP
				LIMIT 1'))
			||	false === ($last_of_current = db_single_assoc('party','
				SELECT PARTYID,NAME,STAMP
				FROM party
				WHERE LOCATIONID='.$locationid.'
				  AND CANCELLED=0
				  AND MOVEDID=0
				ORDER BY STAMP DESC
				LIMIT 1'))
			) {
				return false;
			}
			if ($first_of_follow
			&&	$last_of_current
			&&	$first_of_follow['STAMP'] < $last_of_current['STAMP']
			) {
				register_error('location:error:successor_overlaps_current_LINE', DO_UBB, [
					'SUCCESSORID'	=> $followupid,
					'LOCATIONID'	=> $locationid
				]);
				return false;
			}
		}

	}
	move_site_to_presence();

	require_once '_site.inc';
	require_once '_namefix.inc';
	require_once '_ubb_preprocess.inc';

	$setlist[] = 'TITLE="'.	addslashes(get_fixed_name($_POST['TITLE'], true)).'"';

	if ($location_admin) {
		$setlist[] = 'NAME="'.		addslashes(get_fixed_name($_POST['NAME'], true)).'"';
		$setlist[] = 'ORDERNAME="'.	addslashes(get_fixed_name($_POST['ORDERNAME'], true)).'"';
	}
	$setlist[] = 'NEEDUPDATE='.		(isset($_POST['NEEDUPDATE']) ? "b'1'" : "b'0'");
	$setlist[] = 'MIN_AGE='.		get_minimum_age_for_setlist($_POST, 'MIN_AGE');
	$setlist[] = 'KVKNUM="'.		addslashes($_POST['KVKNUM'] ?: '').'"';
	$setlist[] = 'CAMREQ_EMAIL="'.	addslashes($_POST['CAMREQ_EMAIL']).'"';
	$setlist[] = 'CAMREQ_NAME="'.	($_POST['CAMREQ_EMAIL'] ? addslashes($_POST['CAMREQ_NAME']) : null).'"';
	$setlist[] = 'CAMREQ_USERID='.	$_POST['CAMREQ_USERID'];
	$setlist[] = 'EMAIL="'.			addslashes($_POST['EMAIL']).'"';
	$setlist[] = 'ADDRESS="'.		addslashes($_POST['ADDRESS']).'"';
	$setlist[] = 'ZIPCODE="'.		addslashes(clean_zip($_POST['ZIPCODE'], utf8: true)).'"';
	$setlist[] = 'POBOX_ZIPCODE="'.	addslashes(clean_zip($_POST['POBOX_ZIPCODE'], utf8: true)).'"';
	$setlist[] = 'PHONE="'.			addslashes(clean_phone($_POST['PHONE'])).'"';
	$setlist[] = 'SITE="'.			($_POST['SITE'] ? addslashes(make_proper_site($_POST['SITE'],KEEP_MAIN_DOMAIN)) : null).'"';
 	#$setlist[] = 'SITE="'.			($_POST['SITE'] ? addslashes($_POST['SITE']) : '').'"';
	$setlist[] = 'POBOX='.			$_POST['POBOX'];
	$setlist[] = 'CITYID='.			$_POST['CITYID'];
	$setlist[] = 'CAPACITY='.		$_POST['CAPACITY'];
	$setlist[] = 'FOLLOWUPID='.		$followupid;
	$setlist[] = 'DEAD='.			(isset($_POST['DEAD']) || $locationid && $oldlocation['FOLLOWUPID'] ? "b'1'" : "b'0'");
	$setlist[] = 'ALLOW_EMPTY_ADDR='.(isset($_POST['ALLOW_EMPTY_ADDR']) ? "b'1'" : "b'0'");
	$setlist[] = 'EROTIC='.			(isset($_POST['EROTIC']) ? "b'1'" : "b'0'");
	$setlist[] = 'LOCATIONTYPE="'.	addslashes($loctype).'"';

	if (have_admin('location')) {
		$setlist[] = 'ADMIN_TEXT 		= "'.addslashes(_ubb_preprocess($_POST['ADMIN_TEXT'],		utf8: true)).'"';
		$setlist[] = 'ADMIN_PARTY_TEXT	= "'.addslashes(_ubb_preprocess($_POST['ADMIN_PARTY_TEXT'], utf8: true)).'"';

		if (have_admin('hide_stuff')) {
			require_once '_status_overlay.inc';
			add_status_to_setlist($setlist);
		}
	}
	if (!isset($_POST['OVERLAP'])) {
		require_once '_addresscheck.inc';
		$result = address_exists('location', $_POST['ADDRESS'], $_POST['CITYID'], $locationid, $followupid);
		if ($result || $result === false) {
			return false;
		}
	}
	$setlist[] = 'LONGITUDE='.round((float)$_POST['LONGITUDE'], 12);
	$setlist[] = 'LATITUDE='. round((float)$_POST['LATITUDE'], 12);

	if ($locationid) {
		if (!db_insert('location_log','
			INSERT INTO location_log
			SELECT * FROM location
			WHERE NOT '.binary_equal($setlist).'
			  AND LOCATIONID='.$locationid)
		) {
			return false;
		}
		if (db_affected()) {
			reset_site();
			if (!db_update('location','
				UPDATE location SET
					MUSERID	='.CURRENTUSERID.',
					MSTAMP	='.CURRENTSTAMP.',
					'.implode(',',$setlist).'
				WHERE LOCATIONID = '.$locationid)
			) {
				return false;
			}
		}
		if (isset($_POST['UPDATEPARTIES'])) {
			$parties = db_simpler_array('party','
				SELECT PARTYID
				FROM party
				WHERE LOCATIONID='.$_REQUEST['sID'].'
				  AND LOCATIONTYPE!="'.$loctype.'"'
			);
			$upds = 0;
			if ($parties) foreach ($parties as $partyid) {
				if (!db_insert('party_log','
					INSERT INTO party_log
					SELECT * FROM party
					WHERE PARTYID='.$partyid)
				||	!db_update('party','
					UPDATE party SET
						MUSERID		='.CURRENTUSERID.',
						MSTAMP		='.CURRENTSTAMP.',
						LOCATIONTYPE	="'.$loctype.'"
					WHERE PARTYID='.$partyid)
				) {
					return false;
				}
				++$upds;
			}
			if ($upds) {
				register_notice('location:notice:parties_updated_LINE');
			}
		}
		if ($location_admin
		&&	isset($_POST['NEEDUPDATE']) !== $oldlocation['NEEDUPDATE']
		) {
			require_once '_needupdates.inc';
			flush_needupdates();
		}
		register_notice('location:notice:changed_LINE');
	} else {
		if (!db_insert('location','
			INSERT INTO location SET
				ACCEPTED='.($location_admin ? 1 : 0).',
				CSTAMP	='.CURRENTSTAMP.',
				USERID	='.CURRENTUSERID.',
				'.implode(',',$setlist))
		) {
			return false;
		}
		$_REQUEST['sID'] = $locationid = db_insert_id();
		ticket_update('location', $locationid);
		init_site();
		register_notice('location:notice:added_LINE');
	}
	if ($location_admin) {
		require_once '_alternate_name.inc';
		commit_alternate_names('location', $locationid);
	}
	require_once '_uploadimage.inc';
	uploadimage_actual_receive('location', $locationid);
	require_once '_process_appic_changes.inc';
	cleanup_update_from_appic($locationid);
	require_once '_genrelist.inc';
	_genrelist_commit();
	process_parents('location', $locationid);
	store_presence();
	element_changed();
	process_inquiry_allowance();
	page_changed('location', $locationid);
	return $locationid;
}
