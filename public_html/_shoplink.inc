<?php

declare(strict_types=1);

function get_shop_url(
	string	$type,
	int		$partyid,
	?array	$presaleinfo = null,
	bool	$add_utm	 = true,
): ?string {
	if (!($url = actual_get_shop_url($type, $partyid, $presaleinfo))) {
		return null;
	}
	if ($add_utm) {
		return add_utm($url, 'party', $partyid, utf8: true);
	}
	return $url;
}

const NEED_NO_PRESALEINFO = [
	'partybussen'	=> true,
];

function actual_get_shop_url(string $type, int $partyid, ?array $presaleinfo = null): ?string {
	require_once '__translation.php';
	if (!isset(NEED_NO_PRESALEINFO[$type])
	&&	$presaleinfo === null
	&&	(require_once '_presale.inc')
	&&	!($presaleinfo = get_presale_info('party', $partyid))
	) {
		mail_log('empty presaleinfo in actual_get_shop_url');
		return null;
	}
	switch ($type) {
	case 'appic':
		return "https://appic.shop/redirect/{$presaleinfo['APPIC']}?source=partyflock";

	case 'ticketswap':
		# EXAMPLE: https://www.ticketswap.com/event/billie-eilish/595410b6-aa96-4d78-be99-db7c75ca4f66
		#							  01234567 8901 2345 6789 012345678901
		#									 1		  2		3

		if (!array_key_exists('TICKETSWAP', $presaleinfo)) {
			mail_log('no ticketswap in presaleinfo?', $presaleinfo);
			$presaleinfo['TICKETSWAP'] = null;
		}
		if (!$presaleinfo['TICKETSWAP']) {
			return 'https://www.ticketswap.com/';
		}
		return $presaleinfo['TICKETSWAP'];

	case 'ibizadiscoticket':
		if ($id = $presaleinfo['IBIZADISCOTICKET']) {
			return 'https://www.ibizadiscoticket.com/?c=events&a=details&id='.$id.'&partner=115';
		}
		return 'https://www.ibizadiscoticket.com/?partner=115';

	case 'ikbenaanwezig':
		if ($name = $presaleinfo['IKBENAANWEZIG']) {
			return 'https://shop.ikbenaanwezig.nl/tickets/event/'.$name;
		}
		return 'https://shop.ikbenaanwezig.nl/';

	case 'nowonlinetickets':
		if ($id = $presaleinfo['NOWONLINETICKETS']) {
			return 'https://www.nowonlinetickets.nl/Shop/EventDetails/'.$id;
		}
		return 'https://www.nowonlinetickets.nl/Shop/';

	case 'yourticketprovider':
		if ($id = $presaleinfo['YOURTICKETPROVIDER']) {
			return "https://www.yourticketprovider.nl/events/$id-partyflock";
		}
		return 'https://www.yourticketprovider.nl/';

	case 'partybussen':
		if ($other = db_single_array('partybussen','SELECT OTHERID,PATH FROM partybussen WHERE PARTYID='.$partyid)) {
			[$otherid, $path] = $other;
			if ($path) {
				$path .= '/';
			}
			return "https://www.partybussen.nl/$path?".UTM_PARTYBUSSEN."&utm_term=partybussen&utm_campaign=$otherid";
		}
		return 'https://www.partybussen.nl/';

	case 'ticketmaster':
		return"https://www.ticketmaster.nl/event/{$presaleinfo['TICKETMASTER']}?CAMEFROM=PARTYFLOCK";

	case 'seedance':
		if ($seedance = $presaleinfo['SEEDANCE']) {
			if (preg_match('"^(.+?)_(.+?)_(.+)$"',$seedance,$matches)) {
				[, $sh, $v, $p] = $matches;
				return "https://webshop.seetickets.nl/shows/showtickets.aspx?sh=$sh&v=$v&p=$p&referrer=PF&ns_campaign=PF&ns_mchannel=web&ns_source=PF&ns_linkname=$sh&ns_fee=0&view=dance";
			}
			return "https://webshop.seetickets.nl/Shows/Show.aspx?PARTYFLOCK=1&sh=$seedance&referrer=PF&ns_campaign=PF&ns_mchannel=web&ns_source=PF&ns_linkname=$seedance&ns_fee=0&view=dance";
		}
		return 'https://webshop.seetickets.nl/?PARTYFLOCK=1&referrer=PF&ns_campaign=PF&ns_mchannel=web&ns_source=PF&view=dance';

	case 'ticketscript':
	case 'ticketscriptv2':
		$shop = 'TXYXQR3P';
		if (!empty($presaleinfo['TICKETSCRIPTV2'])
		&&	!empty($presaleinfo['TICKETSCRIPTSHOP'])
		&&	db_single('ticketscriptbad','SELECT 1 FROM ticketscriptbad WHERE EID='.$presaleinfo['TICKETSCRIPTV2'])
		) {
			$shop = $presaleinfo['TICKETSCRIPTSHOP'];
		}
		if ($presaleinfo['TICKETSCRIPTV2']) {
			return 'https://shop.ticketscript.com/channel/web2/get-dates/rid/'.$shop.'/eid/'.$presaleinfo['TICKETSCRIPTV2'].'/language/'.CURRENTLANGUAGE;
		}
		if ($presaleinfo['TICKETSCRIPT'] > 1) {
			return 'https://www.ticketscript.com/shop_plus.php?view=order&org_id=2346&lang='.CURRENTLANGUAGE.'&tid='.$presaleinfo['TICKETSCRIPT'];
		}
		if (ROBOT) {
			return null;
		}
		return 'https://www.ticketscript.com/?lang='.CURRENTLANGUAGE;

	case 'fnac':
		if ($presaleinfo['FNAC']
		&&	($party = memcached_party_and_stamp($partyid))
		) {
			[$year, $month, $day, $hour, $minutes] = _getdate($party['STAMP']);
			/** @noinspection SpellCheckingInspection */
			return	'https://be.fnacspectacles.com/processreservation/disponibilite.do'.
					"?code={$presaleinfo['FNAC']}".
					'&seanceChoisie='.sprintf('%04d%02d%02d_%02d%02d',$year, $month, $day, $hour, $minutes).
					'&typeDisponibilite=0'.
					'&_lang='.CURRENTLANGUAGE;
		}
		return 'https://be.fnacspectacles.com/?_lang='.CURRENTLANGUAGE;

	/** @noinspection SpellCheckingInspection */
	case 'eventtickets':
	case 'event-tickets':
		/** @noinspection SpellCheckingInspection */
		return "https://www.event-tickets.be/Event/{$presaleinfo['EVENTTICKETS']}/".CURRENTLANGUAGE.'/PARTYFLOCK';

	case 'paylogic':
		if ($presaleinfo['PAYLOGICV2']) {
			return	"https://queue.paylogic.com/{$presaleinfo['PAYLOGICV2']}".
					($presaleinfo['PAYLOGICPOS'] ? "/{$presaleinfo['PAYLOGICPOS']}/" : '');
		}
		if ($presaleinfo['PAYLOGIC']) {
			return "https://v1.paylogic.nl/frontoffice/?command=paymentMenu&merchantModuleID={$presaleinfo['PAYLOGIC']}&portal=lz73kC0hRFw";
		}
		return 'https://www.paylogictickets.nl/?portal=lz73kC0hRFw';

	case 'site':
		if ($presaleinfo['WEBSITE']) {
			require_once '_presale.inc';
			$sites = explode("\n", $presaleinfo['WEBSITE']);
			$i = have_number($_REQUEST, 'I') ? $_REQUEST['I'] : 0;
			$site = mytrim($sites[isset($sites[$i]) ? $i : 0], utf8: true);

			if (str_contains($site, 'ticketscript')) {
				$new_site = preg_replace('"/language/(\w+)"u','/language/'.CURRENTLANGUAGE,$site,-1,$cnt);
				$site = $cnt ? $new_site : myrtrim($site,'/',  true).'/language/'.CURRENTLANGUAGE;
				$site = preg_replace('"^(https?://)(ticketscript\.com/channel/web.*)$"u','\\1shop.\\2',$site);

			} elseif (str_contains($site, 'eventbrite')) {
				foreach (['aff','ref'] as $key) {
					if (str_contains($site, $key.'=')) {
						$site = preg_replace('"([?&])'.$key.'=\w+"u','$1'.$key.'=partyflock',$site);
					} else {
						$site .= (!str_contains($site, '?') ? '?' : '&').$key.'=partyflock';
					}
				}
				if (!str_contains($site, '#')) {
					$site .= '#tickets';
				}

			} elseif (str_contains($site, 'eventim')) {
				$site .= (!str_contains($site,'?') ? '?' : '&').'referer_info=partyflock';
			}
			require_once '_affiliates.inc';
			return make_affiliate_url($site, 'party', $partyid, [
				'DONT_REPLACE_UTM' => true,	# adding utm in get_shop_url
			], true);
		}
		break;

	case 'eventim':
		/** @noinspection SpellCheckingInspection */
		return	'https://secure.eventim.nl/tickets.html'.
				'?affiliate=P29'.
				'&referer_info=partyflock'.
				'&fun=evdetail'.
				'&doc=evdetailb'.
				'&key='.$presaleinfo['EVENTIMSERIESID'].'$'.$presaleinfo['EVENTIM'];
	}
	return null;
}
