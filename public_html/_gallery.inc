<?php

function visible_gallery(array $gallery): bool {
	static $__visi;
	if (isset($__visi[$galleryid = $gallery['GALLERYID']])) {
		return $__visi[$galleryid];
	}
	if (is_number($gallery['VISIBLE'])) {
		$visi = $gallery['VISIBLE'] ? true : false;
	} else {
		switch ($gallery['VISIBLE']) {
		case 'never':
		case 'no':
			$visi = false;
			break;

		case 'yes':
			$visi = true;
			break;

		case 'visitors':
			require_once '_going.inc';
			$visi =	have_user()
				&&	$gallery['PARTYID']
				&&	going_to_party($gallery['PARTYID']);
			break;
		}
	}
	return $__visi[$galleryid] = $visi;
}
