<?php

declare(strict_types=1);

$_SERVER['appic'] = true;

const NEED_KEY = [
	'future'		=> true,
	'event'			=> true,
	'deleted'		=> true,
	'combined'		=> true,
	'organizations'	=> true,
	'organization'	=> true,
	'artists'		=> true,
	'artist'		=> true,
	'locations'		=> true,
	'location'		=> true,
];

define('CURRENTSTAMP',	time());
define('TODAYSTAMP',	mktime(0,0,0));

const CURRENTUSERID		= 1;
const CURRENTIDENTID	= 0;
const SMALL_SCREEN		= null;
const ROBOT				= false;
const CURRENTTHEME		= 'dark';

require_once '_exit_if_offline.inc';
require_once '_helper.inc';
require_once '_db.inc';
require_once '_require.inc';
require_once '_date.inc';
require_once '_urltitle.inc';
require_once '_urlcheck.inc';
require_once '_hosts.inc';
require_once '_ubb.inc';
require_once '_organizationstuff.inc';
require_once '_presale.inc';
require_once '_uploadimage.inc';
require_once '_servertype.inc';
require_once '_appic.inc';
require_once '_livestream.inc';
require_once 'defines/facebook.inc';
require_once 'defines/appic.inc';

if (!empty($_SERVER['eFB'])) {
	mail_log('appic feed eFB still used');
}

function event_videos(int|string $id): string {
	return "
		  STATUS	= 'active'
	  AND TYPE		= 'youtube'
	  AND MAINTYPE	= 'party'
	  AND MAINID	= $id
	  AND ASSOCTYPE	='video'";
}

define('UPDATE_STAMPS',	'
	party.CSTAMP AS EVENT_CREATED,
	party.MSTAMP AS EVENT_BASE_MODIFIED,
	GREATEST(
		party.CSTAMP,
		party.MSTAMP,
		COALESCE((
			SELECT MAX(MSTAMP)
			FROM promo_video
			WHERE STARTSTAMP <= UNIX_TIMESTAMP()
			  AND STOPSTAMP > UNIX_TIMESTAMP()
			  AND (		ELEMENT = "location"
					AND ID = party.LOCATIONID
					 OR ELEMENT = "organization"
					AND ID IN (
						SELECT ASSOCID
						FROM connect
						WHERE MAINTYPE  = "party"
						  AND MAINID    = party.PARTYID
						  AND ASSOCTYPE = "organization"
					)
			  )
		),0),
		COALESCE((SELECT CSTAMP FROM facebook_info AS fi WHERE fi.PARTYID=party.PARTYID),0),
		COALESCE((SELECT MSTAMP FROM appic_event_info AS aei WHERE aei.MAINID='.PARTY_MAIN_ID_INC.'),0),
		COALESCE((SELECT MAX(MSTAMP) FROM lineup WHERE lineup.PARTYID=party.PARTYID),0),
		COALESCE((SELECT MAX(MSTAMP) FROM presaleinfo WHERE presaleinfo.PARTYID=party.PARTYID),0),
		COALESCE((SELECT MAX(CSTAMP) FROM uploadimage_generated WHERE TYPE="party" AND ID=party.PARTYID),0),
		COALESCE((SELECT MAX(CSTAMP) FROM uploadimage JOIN uploadimage_link USING (UPIMGID) WHERE TYPE IN ("party", "lineup") AND ID=party.PARTYID),0),
		COALESCE((SELECT MSTAMP FROM elementchanged WHERE ELEMENT="party" AND ID=party.PARTYID),0),
		COALESCE((SELECT GREATEST(MAX(PSTAMP),MAX(MSTAMP)) FROM video JOIN connect ON VIDEOID=ASSOCID WHERE '.event_videos('party.PARTYID').'),0)
	) AS EVENT_MODIFIED,
	COALESCE((SELECT MAX(CSTAMP) FROM uploadimage_generated WHERE TYPE="party" AND ID=party.PARTYID),0) AS GENERATED_UPDATE,
	COALESCE((SELECT MAX(CSTAMP) FROM uploadimage JOIN uploadimage_link USING (UPIMGID) WHERE TYPE="party" AND ID=party.PARTYID),0) AS FLYER_UPDATE,
	COALESCE((SELECT MAX(MSTAMP) FROM lineup WHERE lineup.PARTYID=party.PARTYID),0) AS LINEUP_MODIFIED,
	COALESCE((SELECT GREATEST(MAX(PSTAMP),MAX(MSTAMP)) FROM video JOIN connect ON VIDEOID=ASSOCID WHERE '.event_videos('party.PARTYID').'),0) AS VIDEOS_MODIFIED
');

function get_modified(string $element): string {
	return " GREATEST(
		$element.CSTAMP,
		$element.MSTAMP,
		COALESCE((SELECT MSTAMP FROM elementchanged WHERE ELEMENT = '$element' AND ID = $element.{$element}ID), 0),
		COALESCE((SELECT MAX(CSTAMP) FROM uploadimage JOIN uploadimage_link USING (UPIMGID) WHERE TYPE = '$element' AND ID = $element.{$element}ID), 0)
	) AS MODIFIED ";
}

const NEW_DUAL_LANDSCAPES	= true;
const SYNC_DELAY			= TEN_MINUTES;

$_SERVER['eVERSION'] = empty($_SERVER['eVERSION']) ? 0 : (int)$_SERVER['eVERSION'];

$obj = main();
$obj['generated'] = date(ISO8601Z);

header('Content-Type: application/json');
header('Cache-Control: must-revalidate,max-age=0,no-store,no-cache');

echo safe_json_encode($obj);

function error(int $status, string $errstr): array {
	http_response_code($status);
	return ['error' => $errstr];
}

function main(): array {
	if (isset($_SERVER['eID'])) {
		$_SERVER['eID'] = (int)$_SERVER['eID'];
	}

	if ((	empty($_SERVER['eELEMENT'])
		||	isset(NEED_KEY[$_SERVER['eELEMENT']])
		)
	&&	!may_access()
	) {
		return error(403, 'no access');
	}

	if (have_post()) {
		if (isset($_SERVER['eELEMENT'])) {
			return error(400, 'eELEMENT not expenot allowed in POST');
		}
		if (!($data = file_get_contents('php://input'))) {
			mail_log('update_from_appic missing data: '.$data);
			return error(400,'missing data');
		}
		if (!($input = safe_json_decode($data, true))) {
			mail_log('update_from_appic bad json: '.$data);
			return error(400,'could not decode json data');
		}
		$action =
			isset($input['action'])
		?	$input['action'] = mytrim($input['action'], '/')
		:	null;

		$pf_id = 0;
		if (!have_idnumber($input, 'pf_id')
		&&	have_idnumber($input, 'appic_id')
		&&	($element = have_something($input, 'element'))
		&&	($pf_element = APPIC_ELEMENT_TO_PARTYFLOCK[$element] ?? null)
		&&	false === ($pf_id = db_single('appic_event',"
				SELECT ID
				FROM appic_element
				WHERE ELEMENT = '$pf_element'
				  AND APPICID = {$input['appic_id']}"
		))) {
			return error(500, "failed to find partyflock id for appic id {$input['appic_id']} and element $element");
		}
		if (empty($pf_id)) {
			$pf_id = 0;
		}
		if (empty($input['data']['user_data'])
		||	!preg_match('"^(?P<name>.*?)\s*\((?P<email>.*?)\)$"', $input['data']['user_data'], $sender)
		) {
			$sender = ['name' => '', 'email' => ''];
		}
		if (!db_insert('update_from_appic','
			INSERT INTO update_from_appic SET
				SENDER_NAME			= "'.addslashes($sender['name']).'",
				SENDER_EMAIL		= "'.addslashes($sender['email']).'",
				STATUS				= "'.($action === 'syncdone' ? 'noted' : 'open').'",
				ACTION				= "'.addslashes($action).'",
				PF_ID				= '.$pf_id.',
				APPIC_ID			= '.(have_number($input,'appic_id') ?: 0).',
				ELEMENT				= "'.addslashes($input['element'] ?? $input['type'] ?? '').'",
				CALLBACK_URL		= "'.addslashes($input['callback_url'] ?? '').'",
				CHANGE_REQUEST_ID	= '.(have_number($input,'change_request_id') ?: 0).',
				STAMP				= '.CURRENTSTAMP.',
				JSON				= "'.addslashes($data).'"')
		) {
			return error(500,'failed to register update');
		}
	} else {
		if (!isset($_SERVER['eELEMENT'])) {
			return error(400,'missing element');
		}
		return match($_SERVER['eELEMENT']) {
			'future'		=> create_future(),
			'deleted'		=> create_deleted(),
			'combined'		=> create_combined(),
			'organizations',
			'artists',
			'locations'		=> create_item_feed(substr($_SERVER['eELEMENT'], 0, -1)),
			'event',
			'organization',
			'artist',
			'location'		=> create_object(),
			'gallery',
			'photos'		=> create_photo_feed($_SERVER['eELEMENT']),
			'galleries'		=> create_gallery_feed(),
			'genres'		=> create_genres(),
			default			=> error(404,'unknown element'),
		};
	}
	return [];
}

function may_access(): string|true {
	global $currentuser;
	if (HOME_OFFICE
	||	FLOCK_NET
	||	(	!empty($_COOKIE['FLOCK_SESSION'])
		&&	(require_once '_currentuser.inc')
		&&	($currentuser ??= new _currentuser(TINY_USER | SKIP_SETTINGS))
		&&	have_admin('development')
		#	^ double define CURRENTUSERID, but keep the 1 userid for further queries
		)
	) {
		return true;
	}
	$api_key = get_header('X-API-KEY');

	/** @noinspection ReturnTernaryReplacementInspection */
	return	$api_key === SERVER_SANDBOX
		?	'vaVTeQwGsDyVR6BLpKgm5D0EEt'
		:	'OhiTfHuNlQ7bIpJFDwN1dLe7U2';
}

function get_boolean_header(string $name): bool {
	if (!($header = get_header($name))) {
		return false;
	}
	return match (strtolower($header)) {
		'yes', 'true', '1'	=> true,
		default				=> false,
	};
}
function get_header(string $name): ?string {
	static $__headers = [];
	if ($__headers === []) {
		foreach (apache_request_headers() + $_GET as $key => $val) {
			$__headers[strtoupper($key)] = $val;
		}
	}
	return $__headers[$name] ?? null;
}

function is_hidden_org(int $organizationid): bool {
	static $__hidden = db_boolean_hash('hidden_orgs','SELECT ID FROM hidden_orgs');
	/** @noinspection OffsetOperationsInspection */
	return $__hidden === false
		|| isset($__hidden[$organizationid]);
}

function create_item_feed(string $element): array {
	$since = (int)get_header('X-SINCE');

	if (!$since) {
		$items = db_simpler_array($element,'
			SELECT '.$element.'ID
			FROM '.$element.'
			WHERE ACCEPTED=1'
		);
	} else {
		$qs[] ='SELECT '.$element.'ID AS ID
			FROM '.$element.'
			WHERE ACCEPTED = 1
			  AND CSTAMP >= '.$since;

		$qs[] ='SELECT '.$element.'ID AS ID
			FROM '.$element.'
			WHERE ACCEPTED = 1
			  AND MSTAMP >= '.$since;

		$qs[] ='SELECT ID
			FROM elementchanged
			JOIN '.$element.' ON '.$element.'ID = ID
			WHERE ACCEPTED = 1
			  AND ELEMENT = "'.$element.'"
			  AND elementchanged.MSTAMP >= '.$since;

		$qs[] ='SELECT ID
			FROM pagechanged
			JOIN '.$element.' ON '.$element.'ID = ID
			WHERE ELEMENT = "'.$element.'"
			  AND pagechanged.MSTAMP >= '.$since;

		$qs[] ='SELECT ID
			FROM uploadimage
			JOIN uploadimage_link USING (UPIMGID)
			JOIN '.$element.' ON '.$element.'ID=ID
			WHERE ACCEPTED = 1
			  AND uploadimage_link.TYPE = "'.$element.'"
			  AND uploadimage.CSTAMP >= '.$since;

		$items = db_simpler_array(
			[$element, 'elementchanged', 'uploadimage', 'uploadimage_link'],
			implode(' UNION ',$qs)
		);
	}

	$result = [];

	if ($items) {
		$is_org = $element === 'organization';

		foreach ($items as $itemid) {
			if ($is_org && is_hidden_org($itemid)) {
				continue;
			}
			$result[] = $itemid;
		}
	}
	return ['changed' => $result];
}

function get_old_names(string $element, int $id): string {
	return "(
		SELECT GROUP_CONCAT(CONCAT(ON_PROFILE, ':', ENDSTAMP, ':', NAME) SEPARATOR '\xF')
		FROM itemname
		WHERE ELEMENT = '$element'
		  AND ID = $id
	)	AS OLDNAMES ";
}

function add_old_names(array &$item, string $OLDNAMES): void {
	if (!$OLDNAMES) {
		return;
	}
	foreach (explode(chr(0xF), $OLDNAMES) as $info) {
		[$on_profile, $endstamp, $name] = explode(':', $info, 3);
		$item['old_names'][] = [
			'name'			=> $name,
			'endstamp'		=> (int)$endstamp,
			'on_profile'	=> (bool)$on_profile,
		];
	}
}

function create_genres(): array {
	if (!($all_genres = db_rowuse_array(['genre', 'master_genre', 'genre_to_master'], '
		SELECT GID AS gid, genre.NAME AS genre_name, MGID AS mgid, master_genre.NAME AS master_genre_name
		FROM genre
		LEFT JOIN genre_to_master USING (GID)
		LEFT JOIN master_genre USING (MGID)
		ORDER BY GENRE_NAME'))
	) {
		if ($all_genres === false) {
			return error(500, 'failed to query genres');
		}
		return [];
	}

	$genres = [];
	$master_genres = [];
	$genre_to_master = [];

	foreach ($all_genres as $genre) {
		/** @noinspection PhpRedundantOptionalArgumentInspection */
		extract($genre, \EXTR_OVERWRITE);
		$genres[$gid] = $genre_name;
		$master_genres[$mgid] = $master_genre_name;
		if ($mgid) {
			$genre_to_master[$gid] = $mgid;
		}
	}
	return ['genres'			=> $genres,
			'master_genres'		=> $master_genres,
			'genre_to_master'	=> $genre_to_master];
}

/** @noinspection PhpUnused */
function create_artist(int $artistid): array {
	if (!($ARTIST = db_single_assoc(['artist', 'country', 'fbid', 'artist_genre', 'genre', 'alternate_name', 'artistalias', 'artistgroup'], "
		SELECT	ARTISTID , artist.NAME, SITE, EMAIL, REALNAME, REALNAME_FULL, BIRTH_YEAR, BIRTH_MONTH, BIRTH_DAY, BIRTH_HIDDEN, artist.CSTAMP,
				DECEASED, DECEASED_YEAR, DECEASED_MONTH, DECEASED_DAY,
				STOPPED, STOPYEAR, STOPMONTH, STOPDAY,
				GENDER, TYPE,
				FOLLOWUPID, GENERIC,
				(SELECT GROUP_CONCAT(CONCAT(genre.GID, ':', genre.NAME)) FROM artist_genre JOIN genre USING (GID) WHERE artist_genre.ARTISTID=artist.ARTISTID) AS GENRES,
				(SELECT GROUP_CONCAT(FBID) FROM fbid WHERE ELEMENT = 'artist' AND ID = $artistid) AS FBIDSTR,
				country.COUNTRYID,  country.SHORT,  country.SHORTA3,
				live_country.COUNTRYID AS LIVE_COUNTRYID,  live_country.SHORT AS LIVE_SHORT, live_country.SHORTA3 AS LIVE_SHORTA3,
				(SELECT GROUP_CONCAT(CONCAT(HIDDEN, ':', ALSOID)) FROM artistalias WHERE ARTISTID = $artistid) AS ALIASES,
				(SELECT GROUP_CONCAT(CONCAT(ACTIVE, ':', HIDDEN, ':', MEMBERID)) FROM artistgroup WHERE ARTISTID = $artistid) AS MEMBERS,
				(SELECT GROUP_CONCAT(alternate_name.NAME SEPARATOR \"\x1F\") FROM alternate_name WHERE ELEMENT = 'artist' AND ID = $artistid) AS RECOGNITIONS,
				".get_old_names('artist',$artistid).',
				'.get_modified('artist')."
			FROM artist
		LEFT JOIN country ON country.COUNTRYID = artist.COUNTRYID
		LEFT JOIN country AS live_country ON live_country.COUNTRYID = artist.LIVE_COUNTRYID
		WHERE ACCEPTED
		  AND ARTISTID = $artistid"))
	) {
		if ($ARTIST === false) {
			return error(500,"failed to query artist with id $artistid");
		}
		return error(404, "artist with id $artistid does not exist");
	}

	/** @noinspection PhpRedundantOptionalArgumentInspection */
	extract($ARTIST, \EXTR_OVERWRITE);

	$artist = [
		'id'			=> $ARTISTID,
		'name'			=> $NAME,
		'last_update'	=> (int)$MODIFIED,
		'generic'		=> $GENERIC,
		'created'		=> $CSTAMP,
	];

	if ($FBIDSTR) {
		[$primary_fbid, $fb_ids, $error] = get_facebook_ids($FBIDSTR, 'artist', $artistid);
		if ($error) {
			return $error;
		}
		$artist += [
			'fb_ids'		=> $fb_ids,
			'primary_fb_id'	=> $primary_fbid,
		];
	}

	if ($REALNAME) {
		$artist['realname'] = $REALNAME;
	}
	if ($REALNAME_FULL) {
		$artist += [
			'realname_internal' => $REALNAME_FULL,
		];
	}

	if ($SITE
	&&	!url_is_bad('artist',$ARTISTID)
	) {
		$artist['site'] = win1252_to_utf8($SITE);
	}
	if ($GENDER) {
		$artist['gender'] = $GENDER;
	}
	if ($TYPE) {
		$artist['type'] = explode(',',$TYPE);
	}

	if ($COUNTRYID) {
		$artist['country'] = [
			'id'		=> $COUNTRYID,
			'alpha2'	=> $SHORT,
			'alpha3'	=> $SHORTA3,
		];
	}
	if ($LIVE_COUNTRYID) {
		$artist['live_country'] = [
			'id'		=> $LIVE_COUNTRYID,
			'alpha2'	=> $LIVE_SHORT,
			'alpha3'	=> $LIVE_SHORTA3,
		];
	}

	if ($BIRTH_YEAR) {
		$artist['birth_year'] = $BIRTH_YEAR;
	}
	if ($BIRTH_MONTH) {
		$artist['birth_month'] = $BIRTH_MONTH;
	}
	if ($BIRTH_DAY) {
		$artist['birth_day'] = $BIRTH_DAY;
	}
	if ($BIRTH_HIDDEN) {
		$artist['birth_hidden'] = true;
	}

	if ($DECEASED) {
		$artist['deceased'] = true;
		if ($DECEASED_YEAR) {
			$artist['deceased_year'] = $DECEASED_YEAR;
		}
		if ($DECEASED_MONTH) {
			$artist['deceased_month'] = $DECEASED_MONTH;
		}
		if ($DECEASED_DAY) {
			$artist['deceased_day'] = $DECEASED_DAY;
		}
	}
	if ($STOPPED) {
		$artist['stopped'] = true;
		if ($STOPYEAR) {
			$artist['stop_year'] = $STOPYEAR;
		}
		if ($STOPMONTH) {
			$artist['stop_month'] = $STOPMONTH;
		}
		if ($STOPDAY) {
			$artist['stop_day'] = $STOPDAY;
		}
	}
	if ($FOLLOWUPID) {
		$artist['followup_id'] = $FOLLOWUPID;
	}
	if ($GENRES) {
		add_genres($GENRES, $artist);
	}
	if ($EMAIL) {
		$artist += [
			'email'		=> win1252_to_utf8($EMAIL),
			'email_hidden'	=> true,
		];
	}
	if ($images = get_images('artist', $artistid)) {
		$artist['images'] = $images;
	}
	$presence = get_presences_for_appic('artist', [$artistid]);
	if (!empty($presence[$artistid])) {
		$artist['presences'] = $presence[$artistid];
	}

	if ($ALIASES) {
		$aliases = [];
		foreach (explode(',', $ALIASES) as $info) {
			[$hidden, $alsoid] = explode(':', $info);
			$aliases[] = [
				'id'		=> (int)$alsoid,
				'hidden'	=> (bool)$hidden,
			];
		}
		$artist['aliases'] = $aliases;
	}
	if ($MEMBERS) {
		$members = [];
		foreach (explode(',', $MEMBERS) as $info) {
			[$active, $hidden, $memberid] = explode(':', $info);
			$members[] = [
				'id'		=> (int)$memberid,
				'active'	=> (bool)$active,
				'hidden'	=> (bool)$hidden,
			];
		}
		$artist['members'] = $members;
	}
	if ($RECOGNITIONS) {
		$artist['recognitions'] = explode(chr(0xF), $RECOGNITIONS);
	}
	if (false === ($bioinfo = db_simple_hash('bio', "
		SELECT SHORTCODE, BODY
		FROM bio
		JOIN language USING (LANGID)
		WHERE ELEMENT = 'artist'
		  AND ID = $artistid"))
	) {
		return error(500,'query failed to obtain artist bio');
	}
	if ($bioinfo) {
		foreach ($bioinfo as $lang => $text) {
			$artist['biography'][$lang] = plain_text($text,UBB_UTF8);
		}
	}

	if ($OLDNAMES) {
		add_old_names($artist, $OLDNAMES);
	}

	return $artist;
}
function create_organization(int $organizationid): ?array {
	if (is_hidden_org($organizationid)) {
		return null;
	}
	$ORG = db_single_assoc(null,'
		SELECT	ORGANIZATIONID, organization.NAME, EMAIL, ADDRESS, ZIPCODE, PHONE, ADDRESS_HIDDEN, FOUNDED, SITE, organization.CSTAMP,
				'.select_genres('organization').',
				GROUP_CONCAT(fbid.FBID) AS FBIDSTR,
				organization.CITYID, city.NAME AS CITY_NAME, city.LATITUDE, city.LONGITUDE,
				country.COUNTRYID, SHORT, SHORTA3,
				'.get_old_names('organization',$organizationid).',
				'.get_modified('organization').',
				DEADSTAMP, FOLLOWUPID,
				ACTIVITIES,
				EROTIC,
				WHOLE,
				GROUP_CONCAT(subof.PARENTID) AS PARENTIDS,
				GROUP_CONCAT(API_ENUM) AS API_ENUMS,
				(SELECT GROUP_CONCAT(NAME SEPARATOR "'."\xF".'") FROM alternate_name WHERE ELEMENT="organization" AND ID='.$organizationid.') AS RECOGNITIONS
		FROM organization
		LEFT JOIN city ON city.CITYID=organization.CITYID
		LEFT JOIN country ON country.COUNTRYID=COALESCE(city.COUNTRYID, organization.COUNTRYID)
		LEFT JOIN subof ON subof.ELEMENT="organization" AND CONCEPTID=ORGANIZATIONID
		LEFT JOIN fbid ON fbid.ELEMENT="organization" AND fbid.ID=organization.ORGANIZATIONID
		LEFT JOIN fbid_category ON fbid_category.FBID=fbid.FBID AND fbid_category.SUB=0
		LEFT JOIN fbcategory USING (CATEGORYID)
		WHERE organization.ACCEPTED=1
		  AND organization.ORGANIZATIONID='.$organizationid.'
		GROUP BY organization.ORGANIZATIONID'
	);

	if ($ORG === false) {
		return error(500,'failed to query organization:'.$organizationid);
	}
	if (!$ORG) {
		return null;
	}

	/** @noinspection PhpRedundantOptionalArgumentInspection */
	extract($ORG, \EXTR_OVERWRITE);

	$org = [
		'id'			=> $ORGANIZATIONID,
		'name'			=> $NAME,
		'created'		=> $CSTAMP,
		'last_update'	=> (int)$MODIFIED,
		'discontinued'	=> $DEADSTAMP,
		'umbrella'		=> $WHOLE,
		'erotic'		=> $EROTIC,
	];

	if ($FBIDSTR) {
		[$primary_fbid, $fb_ids, $error] = get_facebook_ids($FBIDSTR, 'organization', $organizationid);
		if ($error) {
			return $error;
		}
		$org += [
			'fb_ids'	=> $fb_ids,
			'primary_fb_id'	=> $primary_fbid,
		];
	}

	if ($RECOGNITIONS) {
		$org['recognitions'] = explode(chr(0xF), $RECOGNITIONS);
	}
	if ($API_ENUMS) {
		$org['api_enums'] = explode(',', $API_ENUMS);
	}
	if ($PARENTIDS) {
		foreach (explode(',', $PARENTIDS) as $parentid) {
			$org['parentids'][] = (int)$parentid;
		}
	}
	if ($GENRES) {
		add_genres($GENRES, $org);
	}
	if ($SITE
	&&	!url_is_bad('organization',$ORGANIZATIONID)
	) {
		$org['site'] = win1252_to_utf8($SITE);
	}

	$presences = get_presences_for_appic('organization', $organizationid);

	if (!empty($presences[$organizationid])) {
		$org['presences'] = $presences[$organizationid];
	}
	if ($FOUNDED) {
		$org['founded'] = $FOUNDED;
	}
	if ($EMAIL) {
		$org += [
			'email'		=> win1252_to_utf8($EMAIL),
			'email_hidden'	=> true,
		];
	}
	if ($PHONE) {
		$org += [
			'phone'			=> win1252_to_utf8($PHONE),
			'phone_hidden'	=> true,
		];
	}
	if ($ADDRESS) {
		$org += [
			'address'		=> win1252_to_utf8($ADDRESS),
			'zipcode'		=> win1252_to_utf8($ZIPCODE),
			'address_hidden'=> $ADDRESS_HIDDEN,
		];
	}

	$country = $COUNTRYID ? [
		'id'		=> $COUNTRYID,
		'alpha2'	=> $SHORT,
		'alpha3'	=> $SHORTA3,
	] : null;

	if ($CITYID) {
		$org['city'] = [
			'id'		=> $CITYID,
			'name'		=> $CITY_NAME,
			'latitude'	=> $LATITUDE,
			'longitude'	=> $LONGITUDE,
			'country'	=> $country,
		];
	}
	if ($country) {
		$org['country'] = $country;
	}

	if ($images = get_images('organization', $organizationid)) {
		$org['images'] = $images;
	}

	if ($FOLLOWUPID) {
		$org['followup_id'] = $FOLLOWUPID;
	}

	if ($ACTIVITIES) {
		$org['activities'] = explode(',',$ACTIVITIES);
	}

	if ($OLDNAMES) {
		add_old_names($org, $OLDNAMES);
	}

	return $org;
}

/** @noinspection PhpUnused */
function create_location(int $locationid): ?array {
	$LOCATION = db_single_assoc(null,'
		SELECT	location.NAME, location.TITLE, location.ORDERNAME, location.CSTAMP, PHONE, EMAIL, EROTIC, SITE,
				(SELECT GROUP_CONCAT(FBID) FROM fbid WHERE ELEMENT="location" AND ID='.$locationid.') AS FBIDSTR,
				(SELECT GROUP_CONCAT(PARENTID) FROM subof WHERE ELEMENT="location" AND CONCEPTID='.$locationid.') AS PARENTIDS,
				ADDRESS, ZIPCODE, location.LONGITUDE, location.LATITUDE,
				location.CITYID, city.NAME AS CITY_NAME, city.LATITUDE AS CITY_LATITUDE, city.LONGITUDE AS CITY_LONGITUDE,
				country.COUNTRYID, SHORT, SHORTA3,
				'.get_old_names('location', $locationid).',
				'.get_modified('location').',
				DEAD, FOLLOWUPID,
				LOCATIONTYPE,
				CAPACITY,
				(SELECT GROUP_CONCAT(NAME SEPARATOR "'."\xF".'") FROM alternate_name WHERE ELEMENT="location" AND ID='.$locationid.') AS RECOGNITIONS
		FROM location
		LEFT JOIN city ON city.CITYID=location.CITYID
		LEFT JOIN country ON country.COUNTRYID=city.COUNTRYID
		WHERE location.ACCEPTED=1
		  AND location.LOCATIONID='.$locationid
	);

	if ($LOCATION === false) {
		return error(500,'failed to query location:'.$locationid);
	}
	if (!$LOCATION) {
		return null;
	}

	/** @noinspection PhpRedundantOptionalArgumentInspection */
	extract($LOCATION, \EXTR_OVERWRITE);

	if ($locationid === 20796
	||	$locationid === 24303
	) {
		$MODIFIED = max($MODIFIED, 1688728755);
	}

	$location = [
		'id'			=> $locationid,
		'name'			=> $NAME,
		'title'			=> $TITLE,
		'ordername'		=> $ORDERNAME,
		'created'		=> $CSTAMP,
		'last_update'	=> $MODIFIED,
		'closed'		=> $DEAD,
		'erotic'		=> $EROTIC,
		'latitude'		=> $LATITUDE,
		'longitude'		=> $LONGITUDE,
	];

	if ($FBIDSTR) {
		[$primary_fbid, $fb_ids, $error] = get_facebook_ids($FBIDSTR, 'location', $locationid);
		if ($error) {
			return $error;
		}
		$location += [
			'fb_ids'		=> $fb_ids,
			'primary_fb_id'	=> $primary_fbid,
		];
	}

	if ($PARENTIDS) {
		foreach (explode(',',$PARENTIDS) as $parentid) {
			$location['parentids'][] = (int)$parentid;
		}
	}

	if ($RECOGNITIONS) {
		$location['recognitions'] = explode(chr(0xF), $RECOGNITIONS);
	}

	if ($SITE
	&&	!url_is_bad('location',$locationid)
	) {
		$location['site'] = win1252_to_utf8($SITE);
	}

	$presences = get_presences_for_appic('location', $locationid);

	if (!empty($presences[$locationid])) {
		$location['presences'] = $presences[$locationid];
	}
	if ($EMAIL) {
		$location += [
			'email'		=> win1252_to_utf8($EMAIL),
			'email_hidden'	=> true,
		];
	}
	if ($PHONE) {
		$location += [
			'phone'		=> win1252_to_utf8($PHONE),
			'phone_hidden'	=> true,
		];
	}
	if ($ADDRESS) {
		$location += [
			'address'	=> $ADDRESS,
			'zipcode'	=> $ZIPCODE,
		];
	}


	if ($CITYID) {
		$location['city'] = [
			'id'		=> $CITYID,
			'name'		=> $CITY_NAME,
			'latitude'	=> $LATITUDE,
			'longitude'	=> $LONGITUDE,
		];
		if ($COUNTRYID) {
			$location['city']['country'] = [
				'id'		=> $COUNTRYID,
				'alpha2'	=> $SHORT,
				'alpha3'	=> $SHORTA3,
			];
		}
	}

	if ($images = get_images('location', $locationid)) {
		if ($locationid === 20796
		||	$locationid === 24303
		) {
			$images[0]['mimetype'] = 'image/jpeg';
			$images[0]['url'] = 'https://home.partyflock.nl/thomas/20230707_atelier.jpg';
		}
		$location['images'] = $images;
	}

	if ($FOLLOWUPID) {
		$location['followup_id'] = $FOLLOWUPID;
	}

	if ($CAPACITY) {
		$location['capacity'] = $CAPACITY;
	}

	if ($OLDNAMES) {
		add_old_names($location, $OLDNAMES);
	}

	return $location;
}
function create_future() {
	$since = get_header('X-SINCE');
	if (!is_number($since)) {
		return error(400, 'X-SINCE must be a unix time stamp');
	}
	$since = (int)$since;

	$q = '	SELECT PARTYID, '.UPDATE_STAMPS.', (
				SELECT GROUP_CONCAT(DISTINCT FBID)
				FROM fbid
				LEFT JOIN feedevent ON FEEDID = FBID
				WHERE ELEMENT= " party"
				  AND ID = party.PARTYID
			) AS FBIDSTR,'.
			PARTY_MAIN_ID.'
		FROM party
		WHERE ACCEPTED = 1
		  AND STAMP > '.CURRENTSTAMP.'
		HAVING EVENT_MODIFIED >= '.($since - SYNC_DELAY).'
		   AND EVENT_MODIFIED < '.(CURRENTSTAMP - SYNC_DELAY) // wait ten minutes after change to make sure event is in consistent state
		;

	$events = db_rowuse_hash('party', $q);

	if ($events === false) {
		return error(500,'failed to query future events');
	}
	$okevents = [];
	foreach ($events as $event) {
		$okevent = [
			'main_id'		=> (int)($event['MAIN_ID'] ?: $event['PARTYID']),
			'id'			=> $event['PARTYID'],
			'created'		=> (int)$event['EVENT_CREATED'],
			'last_update'	=> (int)$event['EVENT_MODIFIED'],
			'lineup_update'	=> $event['LINEUP_MODIFIED'] ? (int)$event['LINEUP_MODIFIED'] : null,
			'videos_update'	=> $event['VIDEOS_MODIFIED'] ? (int)$event['VIDEOS_MODIFIED'] : null,
		];

		if ($event['FBIDSTR']) {
			[$primary_fbid, $fb_ids, $error] = get_facebook_ids($event['FBIDSTR'], 'party', $event['PARTYID']);
			if ($error) {
				return $error;
			}
			$okevent += [
				'fb_ids'		=> $fb_ids,
				'primary_fb_id'	=> $primary_fbid,
			];
		}

		$okevents[] = $okevent;
	}

	$inslist = [];
	foreach ($okevents as $okevent) {
		$inslist[] = '('.
			CURRENTSTAMP.','.
			$since.','.
			$okevent['last_update'].','.
			$okevent['id'].','.
			(HOME_OFFICE ? 1 : 0).
		')';
	}
	if (!$inslist) {
		# create empty placeholder to indicate no results for this query
		$inslist[] = '('.
			CURRENTSTAMP.','.
			$since.','.
			'0,'.
			'0,'.
			(HOME_OFFICE ? 1 : 0).
		')';
	}
	db_insert('appic_future_log','
	INSERT INTO appic_future_log (STAMP, SINCE, LAST_UPDATE, PARTYID, HOME)
	VALUES '.implode(',',$inslist)
	);

	return ['events' => $okevents];
}
function create_deleted(): array {
	$since = get_header('X-SINCE');
	if (!is_number($since)) {
		$since = 0;
	}

	$deleted_field = $_SERVER['eVERSION'] === 1 ? 'deleted' : 'deleted_at';

	if (false === ($deleted = db_rowuse_array('deleted',"
		SELECT	ELEMENT AS element,
				ID		AS id,
				DSTAMP	AS $deleted_field
		FROM deleted
		WHERE ELEMENT IN ('party', 'location', 'organization', 'artist')
		  AND DSTAMP >= $since"
	))) {
		return error(500,'failed to query deleted items');
	}

	foreach ($deleted as &$deletion) {
		$deletion['soft'] = false;
	}
	unset($deletion);

	$soft_deleted = db_rowuse_array(['artist', 'location', 'organization', 'party'], "
		SELECT	'organization' AS ELEMENT, ORGANIZATIONID AS ID, MSTAMP,
			(	SELECT GROUP_CONCAT(CONCAT(IF(DELETED, '1', '0'), ':', MSTAMP) ORDER BY MSTAMP DESC)
				FROM party_db.organization_log log
				WHERE log.ORGANIZATIONID = item.ORGANIZATIONID
			) AS HISTORY
		FROM party_db.organization item
		WHERE DELETED
		UNION
		SELECT 'artist' AS ELEMENT, ARTISTID AS ID, MSTAMP,
			(	SELECT GROUP_CONCAT(CONCAT(IF(DELETED, '1', '0'), ':', MSTAMP) ORDER BY MSTAMP DESC)
				FROM party_db.artist_log log
				WHERE log.ARTISTID = item.ARTISTID
			) AS HISTORY
		FROM party_db.artist item
		WHERE DELETED
		UNION
		SELECT 'party' AS ELEMENT, PARTYID AS ID, MSTAMP,
			(	SELECT GROUP_CONCAT(CONCAT(IF(DELETED, '1', '0'), ':', MSTAMP) ORDER BY MSTAMP DESC)
				FROM party_db.party_log log
				WHERE log.PARTYID = party.PARTYID
			) AS HISTORY
		FROM party_db.party
		WHERE DELETED
		UNION
		SELECT 'location' AS ELEMENT, LOCATIONID AS ID, MSTAMP,
			(	SELECT GROUP_CONCAT(CONCAT(IF(DELETED, '1', '0'), ':', MSTAMP) ORDER BY MSTAMP DESC)
				FROM party_db.location_log log
				WHERE log.LOCATIONID = item.LOCATIONID
			) AS HISTORY
		FROM party_db.location item
		WHERE DELETED"
	);

	if ($soft_deleted === false) {
		return error(500, 'failed to query soft-deleted items');
	}

	# make sure we pick the date the item was actually marked deleted:

	foreach ($soft_deleted as $soft_deletion) {
		/** @noinspection PhpRedundantOptionalArgumentInspection */
		extract($soft_deletion, \EXTR_OVERWRITE);
		$history = explode(',', $HISTORY);
		$current_mstamp = $MSTAMP;
		foreach ($history as $pair) {
			[$is_deleted, $mstamp] = explode(':', $pair);
			if (!$is_deleted) {
				break;
			}
			$current_mstamp = $mstamp;
		}
		$deleted[] = [
			'soft'			=> true,
			'element'		=> $ELEMENT,
			'id'			=> $ID,
			$deleted_field	=> $current_mstamp
		];
	}
	if ($_SERVER['eVERSION'] === 1) {
		return $deleted;
	}
	if ($_SERVER['eVERSION'] === 2) {
		return ['combined' => $deleted];
	}
	return ['deleted' => $deleted];
}

function create_combined(): array {
	$since = get_header('X-SINCE');
	if (!is_number($since)) {
		$since = 0;
	}
	if (!($combined = db_rowuse_array('combined','
		SELECT	ELEMENT AS element,
				OLDID AS id,
				NEWID AS destination_id,
				STAMP AS '.($_SERVER['eVERSION'] === 1 ? 'combined' : 'combined_at').'
		FROM combined
		WHERE ELEMENT IN ("party", "location", "organization", "artist")
		  AND STAMP >= '.$since
	))) {
		if ($combined === false) {
			return error(500,'failed to query combined items');
		}
		return [];
	}
	if ($_SERVER['eVERSION'] === 1) {
		return $combined;
	}
	return ['combined' => $combined];
}

function create_object() {
	if (!isset($_SERVER['eELEMENT'])) {
		return error(400,'missing element');
	}
	if (!isset($_SERVER['eID'])) {
		return error(400,'missing id');
	}
	$_SERVER['eID'] = (int)$_SERVER['eID'];

	switch ($_SERVER['eELEMENT']) {
	case 'event':
		if (empty($_SERVER['eFB'])) {
			$event = create_event($_SERVER['eID']);
			if (isset($event['error'])) {
				return $event;
			}
			$events = [$event];

			if (!empty($event['day_ids'])) {
				$events_with_timetables = 0;
				foreach ($event['day_ids'] as $day_id) {
					if ($day_id === $_SERVER['eID']) {
						# have already
						continue;
					}
					$event = create_event($day_id);
					if (isset($event['error'])) {
						return $event;
					}
					$events[] = $event;
					if ($event['timetable_released']) {
						++$events_with_timetables;
					}
				}
				$total_events = count($events);

				if ($total_events > 1
				&&	$events_with_timetables !== $total_events
				&&	$events_with_timetables / $total_events >= .5
				) {
					# Partyflock determines 'timetable_released' on a per day basis.
					# Appic wants a global for the entire multi-day event and picks the one on day 1.
					# For now, if more than 50% of the days have a timetable, then all days should have timetable_released set to true
					foreach ($events as &$event) {
						$event['timetable_released'] = true;
					}
					unset($event);
				}

			}
			return ['events' => $events];
		}
		if (!($partyids = db_same_hash('fbid','SELECT ID, FBID FROM fbid WHERE ELEMENT="party" AND FBID='.$_SERVER['eID']))) {
			if ($partyids === false) {
				return error(500,'failed to query partyids for facebook id:'.$_SERVER['eID']);
			}
			return ['events'=>[]];
		}
		$events = [];
		$checkonly = isset($_REQUEST['checkonly']);
		foreach ($partyids as $partyid => $fbids) {
			if ($checkonly) {
				$events[] = (object)[
					'id'		=> $partyid,
					'fb_ids'	=> array_values($fbids),
				];
			} else {
				$event = create_event($partyid);
				if (isset($event['error'])) {
					return $event;
				}
				$events[] = $event;
			}
		}
		return ['events' => $events];

	/** @noinspection PhpMissingBreakStatementInspection */
	case 'organization':
		if (is_hidden_org($_SERVER['eID'])) {
			return error(404,$_SERVER['eELEMENT'].' '.$_SERVER['eID'].' does not exist');
		}
		# fall through
	case 'artist':
	case 'location':
		return [$_SERVER['eELEMENT'] => call_user_func('create_'.$_SERVER['eELEMENT'],$_SERVER['eID'])];

	default:
		return error(400,'element '.$_SERVER['eELEMENT'].' not supported');
	}
}
function create_event($id) {
	require_once '_advice.inc';
	require_once '_bio.inc';
	require_once '_catch_presales.inc';
	require_once '_date.inc';
	require_once '_flockmod.inc';
	require_once '_genres.inc';
	require_once '_party_statistics.inc';
	require_once '_partydescription.inc';
	require_once '_presale.inc';
	require_once '_promo_video.inc';
	require_once '_shoplink.inc';
	require_once '_specialday.inc';
	require_once '_videoaftermovie.inc';
	require_once '_videothumb.inc';
	require_once 'defines/party.inc';
	require_once 'defines/youtube.inc';

	if (!($event = db_single_assoc(null, /** @lang MariaDB */ '
		SELECT	PARTYID, party.USERID, party.CSTAMP, SEPARATE, LIVESTREAM, LIVESTREAM_ADDRESS,
				party.NAME, party.NAME_FOR_APPIC, party.STAMP, STAMP_TZI, DURATION_SECS, party.SITE, party.MSTAMP, party.MIN_AGE, party.MIN_AGE_FEMALE, MAX_AGE, party.EROTIC, party.ACCEPTED, CANCELLED, POSTPONED, MOVEDID, SUBTITLE, EDITION, NOTIME,
				PREREG_URL, SOLD_OUT, PRESALE_SOLD_OUT, AT2400, NIGHTTIME, DAYTIME,
				TIMETABLE_PSTAMP, LINEUP_PSTAMP, FLYER_PSTAMP, EVENT_PSTAMP, FESTIVAL, CONCERT, PREPARTY, AFTERPARTY,
				party.LOCATIONTYPE,'.UPDATE_STAMPS.',
				GROUP_CONCAT(DISTINCT fbid.FBID) AS FBIDSTR,
				TIMEZONE,
				(SELECT 1 FROM lineup WHERE lineup.PARTYID=party.PARTYID LIMIT 1) AS HAVE_LINEUP,
				
					(SELECT 1 FROM lineupneedupdate WHERE lineupneedupdate.PARTYID = party.PARTYID)
				OR	LINEUP_PSTAMP > UNIX_TIMESTAMP() AS INCOMPLETE_LINEUP,
	
				/* have times: */
				(SELECT 1 FROM lineup WHERE lineup.PARTYID=party.PARTYID AND START_STAMP LIMIT 1)
				/* may be published: */
				AND
				IF (
					(SELECT COUNT(DISTINCT ARTISTID) FROM lineup WHERE lineup.PARTYID=party.PARTYID)
					<= 6
					,
					/* all non entire area types have time (MC can perform entire area) */
					ISNULL(
						(SELECT 1 FROM lineup WHERE lineup.PARTYID=party.PARTYID AND TYPE NOT IN ("mc","vj","lj","show") AND (ISNULL(START_STAMP) OR NOT START_STAMP) LIMIT 1)
					)
					,
				/* allow 10% non timed performances */
					(SELECT COUNT(*) FROM lineup WHERE lineup.PARTYID=party.PARTYID AND TYPE NOT IN ("mc","vj","lj","show") AND (ISNULL(START_STAMP) OR NOT START_STAMP))
					/
					(SELECT COUNT(*) FROM lineup WHERE lineup.PARTYID=party.PARTYID AND TYPE NOT IN ("mc","vj","lj","show") AND START_STAMP)
					< .1
				)
				AS TIMETABLE_RELEASED,
			
	/*			(SELECT COUNT(*) FROM lineup WHERE lineup.PARTYID=party.PARTYID AND TYPE NOT IN ("mc","vj","lj","show") AND (ISNULL(START_STAMP) OR NOT START_STAMP))
				AS NOTIMES,
				(SELECT COUNT(*) FROM lineup WHERE lineup.PARTYID=party.PARTYID AND TYPE NOT IN ("mc","vj","lj","show") AND START_STAMP)
				AS WITH_TIMES,
				(SELECT COUNT(*) FROM lineup WHERE lineup.PARTYID=party.PARTYID AND TYPE NOT IN ("mc","vj","lj","show") AND (ISNULL(START_STAMP) OR NOT START_STAMP))
				/
				(SELECT COUNT(*) FROM lineup WHERE lineup.PARTYID=party.PARTYID AND TYPE NOT IN ("mc","vj","lj","show") AND START_STAMP)
				AS PCT_TIMED,*/
					
				(SELECT MSTAMP FROM pagechanged WHERE ELEMENT="party" AND ID=PARTYID) AS PAGE_MSTAMP,
				
				'.select_genres('party').',
				
				party.LOCATIONID, location.NAME AS LOCATION_NAME, location.ZIPCODE AS LOCATION_ZIPCODE, location.ADDRESS AS LOCATION_ADDRESS,
					COALESCE(boarding.LATITUDE, location.LATITUDE, city.LATITUDE)  AS LATITUDE,
					COALESCE(boarding.LONGITUDE, location.LONGITUDE, city.LONGITUDE) AS LONGITUDE,
				GROUP_CONCAT(DISTINCT lfbid.FBID) AS LOCATION_FBIDSTR,
				
				boarding.BOARDINGID, boarding.NAME AS BOARDING_NAME, boarding.ZIPCODE AS BOARDING_ZIPCODE, boarding.ADDRESS AS BOARDING_ADDRESS,
				
				city.CITYID, city.NAME AS CITY_NAME, city.COUNTRYID,
	
				country.SHORT, country.SHORTA3,
				
				(	SELECT GROUP_CONCAT(ASSOCID)
					FROM connect
					WHERE MAINTYPE="party"
					  AND MAINID=PARTYID
					  AND ASSOCTYPE="party"
				) AS PARTY_CONNECTS,
	
				'.PARTY_MAIN_ID.',
				
				facebook_info.NAME AS FBTITLE,
				(SELECT NAME FROM feedevent WHERE FEEDID=fbid.FBID ORDER BY MSTAMP DESC LIMIT 1) AS FBTITLE_FROM_FEEDEVENT,
				
				DOOR_CLOSE_HOUR, DOOR_CLOSE_MINS,
				
				RESTRICTION,
				
				DRESSCODE, DRESSCODE_MANDATORY, LOCKERS, INVITEONLY, DOORONLY, FREE_ENTRANCE, PRESALE_STAMP, PRESALE_STAMP_NOTIME,
				
				(	SELECT GROUP_CONCAT('.PARTY_MAIN_ID_INC.')
					FROM connect
					JOIN party ON PARTYID=ASSOCID
					WHERE MAINTYPE="party"
					  AND MAINID='.$id.'
					  AND ASSOCTYPE="party"
					  AND (		NAME LIKE "%after%"
						OR  SUBTITLE LIKE "%after%"
					)
				) AS AFTERPARTIES,
	
				(	SELECT GROUP_CONCAT('.PARTY_MAIN_ID_INC.')
					FROM connect
					JOIN party ON PARTYID = ASSOCID
					WHERE MAINTYPE="party"
					  AND MAINID='.$id.'
					  AND ASSOCTYPE="party"
					  AND (		NAME LIKE "%preparty%"
						OR	NAME LIKE "%pre-party%"
						OR	NAME LIKE "%pre party%"
						OR  SUBTITLE LIKE "%preparty%"
						OR  SUBTITLE LIKE "%pre-party%"
						OR  SUBTITLE LIKE "%pre party%"
					)
				) AS PREPARTIES,
				
				(	SELECT GROUP_CONCAT(NAME)
					FROM event_master_genre emg
					JOIN master_genre USING (MGID)
					WHERE emg.PARTYID = party.PARTYID
				) AS MASTER_GENRES,
	
				(	SELECT GROUP_CONCAT(NAME SEPARATOR "'."\xF".'")
					FROM alternate_name
					WHERE ELEMENT = "party"
					  AND ID = '.$id.'
				) AS RECOGNITIONS,
					
				'.party_has_statistics($id).' AS HAS_STATISTICS

		FROM party
		LEFT JOIN fbid
			 ON fbid.ELEMENT="party"
			AND fbid.ID = PARTYID
		LEFT JOIN location ON location.LOCATIONID = party.LOCATIONID
		LEFT JOIN fbid AS lfbid
			 ON lfbid.ELEMENT = "location"
			AND lfbid.ID = location.LOCATIONID
		LEFT JOIN boarding ON boarding.BOARDINGID = party.BOARDINGID
		LEFT JOIN city ON city.CITYID = COALESCE(boarding.CITYID, location.CITYID, party.CITYID)
		LEFT JOIN country ON country.COUNTRYID = city.COUNTRYID
		LEFT JOIN facebook_info USING (PARTYID)
		WHERE PARTYID='.$id.'
		  AND party.ACCEPTED = 1
		GROUP BY PARTYID'
	))) {
		if ($event === false) {
			return error(500, 'failed to query event '.$id);
		}
		return error(404, 'event '.$id.' does not exist');
	}

	/** @noinspection PhpRedundantOptionalArgumentInspection */
	extract($event, \EXTR_OVERWRITE);

	$host_ids = db_simpler_array(['partyarea','connect'], "
		SELECT DISTINCT ORGID
		FROM (	(
			SELECT HOSTEDBYID AS ORGID, AREAID
			FROM partyarea
			WHERE partyarea.PARTYID = $id
			  AND HOSTEDBYID
		) UNION (
			SELECT ASSOCID AS ORGID, 1000000 AS AREAID
			FROM connect
			WHERE MAINTYPE = 'party'
			  AND MAINID = $id
			  AND ASSOCTYPE = 'orgashost'
		)
		) AS hosts"
	);

	if ($host_ids === false) {
		return error(500,'failed to query hosts of event:'.$id);
	}

	static $__date_initialized = false;
	if (!$__date_initialized) {
		$__date_initialized = true;
		set_global_dates();
	}

	[$start_year, $start_month, $start_day, $start_hour, $start_mins] = _getdate($STAMP_TZI, 'UTC');

	$start_date = $NOTIME
	?	sprintf('%04d-%02d-%02d',		$start_year, $start_month, $start_day)
	:	sprintf('%04d-%02d-%02d %02d:%02d', $start_year, $start_month, $start_day, $start_hour, $start_mins);

	$days = [];
	if ($multi_day = !$SEPARATE && $PARTY_CONNECTS) {
		if (false === ($days_infos = db_simple_hash('party', /** @lang MariaDB */ "
			SELECT PARTYID, CONCAT(STAMP, CHAR(0xF), SUBTITLE)
			FROM party
			WHERE NAME = '".addslashes($NAME)."'
			  AND LOCATIONID = $LOCATIONID
			  AND PARTYID IN ($PARTY_CONNECTS)
			  AND NOT SEPARATE
			ORDER BY STAMP"))
		) {
			return error(500,'failed to query for connected parties of event:'.$id);
		}
		if (!$days_infos) {
			$multi_day = [];
		} else {
			$days[(int)$id] = (int)$STAMP;
			asort($days);

			# divide events into multi-day stampgroups;

			$stampgroups = [];
			$prev_stamp = 0;
			$grp = 0;
			foreach ($days_infos as $partyid => $info) {
				[$stamp] = explode(chr(0xF), $info);
				$days[(int)$partyid] = (int)$stamp;

				if (!$prev_stamp) {
					$prev_stamp = $stamp;
				} elseif ($stamp > $prev_stamp + 2 * ONE_DAY) {
					++$grp;
					$prev_stamp = 0;
				} else {
					$prev_stamp = $stamp;
				}
				$stampgroups[$grp][$partyid] = $stamp;
			}

			# pick the group with current id

			foreach ($stampgroups as $stampevents) {
				if (isset($stampevents[$id])) {
					$days = $stampevents;
					break;
				}
			}

			asort($days);

			$multi_day = count($days) > 1 ? array_keys($days) : false;
			if ($multi_day) {
				# all days must be more than 20 hours apart

				foreach ($multi_day as $tmp_partyid) {
					if ($party_tmp = memcached_party_and_stamp($tmp_partyid)) {
						$stamps[$tmp_partyid] = [$party_tmp['STAMP'], $party_tmp['STAMP'] + $party_tmp['DURATION_SECS']];
					}
				}
				$separate = false;
				$prev_minstamp = null;
				$prev_maxstamp = null;
				foreach ($stamps as [$minstamp,$maxstamp]) {
					if ($prev_minstamp
					&&	abs($minstamp - $prev_maxstamp) > 48 * ONE_HOUR
					) {
						$separate = true;
						break;
					}
					$prev_minstamp = $minstamp;
					$prev_maxstamp = $maxstamp;
				}

				if ($separate) {
					$SEPARATE = true;
					$multi_day = [];
				}
			}
		}
	}
	if ($SEPARATE) {
		$event['MAIN_ID'] = $id;
	}
	if (false === ($stats = get_determined_genres($id))) {
		return error(500, 'failed to get determined genres for event:'.$id);
	}
	assert(is_array($stats)); # Satisfy EA inspection
	$determined = [];
	if (!empty($stats['for_display'])) {
		if (false === ($genre_names = memcached_simple_hash('genre','
			SELECT GID, NAME
			FROM genre
			WHERE GID IN ('.implodekeys(', ', $stats['for_display']).')'))
		) {
			return error(500, 'coud not obtain genre names');
		}
		assert(is_array($genre_names)); # Satisfy EA inspection
		foreach ($stats['for_display'] as $gid => $cnt) {
			$determined[$genre_names[$gid]] = (int)$cnt;
		}
	}
	if ($GENRES) {
		$genres = [];
		foreach (explode(',', $GENRES) as $genre_info) {
			[$gid, $name] = explode(':', $genre_info);
			$genres[$name] = $stats['per_gid'][(int)$gid] ?? 0;
		}
	}
	if ($DURATION_SECS) {
		if ($TIMEZONE) {
			change_timezone($TIMEZONE);
			$end_stamp = $STAMP + $DURATION_SECS;
		} else {
			change_timezone('UTC');
			# dirty, must be fixed
			$end_stamp = $STAMP_TZI + $DURATION_SECS;
		}
		if ($DURATION_SECS === ONE_DAY) {
			# otherwise sync of time-table does not work
			--$end_stamp;
		}

		[$end_y, $end_m, $end_d, $end_hour, $end_mins] = _getdate($end_stamp);
		$end_date = sprintf('%04d-%02d-%02d %02d:%02d', $end_y, $end_m, $end_d, $end_hour, $end_mins);
		change_timezone();
	}

	$do_bios	  = get_boolean_header('X-BIOS');
	$do_presences = get_boolean_header('X-PRESENCES');

	$stuff = get_organization_stuff($id);

	$all_orgs = [];
	if ($stuff) {
		[,, $all_orgs,, $wholes] = $stuff;
	}

	foreach ($all_orgs as $organizationid => $ignored) {
		if (is_hidden_org($organizationid)) {
			unset($all_orgs[$organizationid]);
		}
	}

	[$appic_title] = get_appic_titles($event, $all_orgs);

	if ($multi_day) {
		# Appic sets entire event to CANCELLED if it's main partyid is cancelled
		$have_non_cancelled = db_single('party', '
			SELECT 1
			FROM party
			WHERE CANCELLED = 0
			  AND PARTYID IN ('.implode(',', $multi_day).')
			LIMIT 1'
		);
		$cancelled = !$have_non_cancelled;
	} else {
		$cancelled = $CANCELLED;
	}

	if ($cancelled) {
		$appic_title = '(cancelled) '.$appic_title;
	}

	if ($POSTPONED) {
		$appic_title = '(postponed) '.$appic_title;
	}

	$event_map = uploadimage_get('eventmap', $id, size: 'tiny');

	$livestream_is_embeddable = null;

	if (($LIVESTREAM_EMBEDDABLE_ADDRESS = get_embeddable_livestream($LIVESTREAM_ADDRESS,$externalid))
	&&	str_contains($LIVESTREAM_EMBEDDABLE_ADDRESS,'youtube')
	) {
		$key = 'embeddable_cache:'.$externalid;
		if (!($data = memcached_get($key))) {
			$data = safe_file_get_contents(
				'https://www.googleapis.com/youtube/v3/videos'.
				'?part=id,contentDetails,status'.
				'&id='.$externalid.
				'&key='.YOUTUBE_V3_KEY
			);
			memcached_set($key,$data ?: null,ONE_WEEK);
		}

		if ($data) {
			$obj = safe_json_decode($data);

			$livestream_is_embeddable = !youtube_video_non_embeddable($obj);
		}
	}
	if ($_SERVER['eVERSION'] >= 2) {
		$livestream = $event['LIVESTREAM'] ?? null;
	} else {
		$livestream = $event['LIVESTREAM'] === 'only';
	}
	$obj = [
		'main_id'				=> $event['MAIN_ID'] ?: $id,
		'id'					=> $id,
		'url_path'				=> get_element_href('party',$id),
		'is_verified'			=> $ACCEPTED,
		'created'				=> $event['EVENT_CREATED'],
		'last_update'			=> $event['EVENT_MODIFIED'],
		'lineup_update'			=> $event['LINEUP_MODIFIED'] ?: null,
		'generated_flyer_update'=> $event['GENERATED_UPDATE'],
		'flyer_update'			=> $event['FLYER_UPDATE'],
		'multi_day'				=> $multi_day,
		'day_ids'				=> $multi_day ?: [],
		'timezone'				=> $TIMEZONE,
		'preregistration_url'	=> $PREREG_URL ? 'https://partyflock.nl/jumpto/prereg/'.$id.'?appic' : null,
		'cancelled'				=> $cancelled,
		'postponed'				=> $POSTPONED,
		'start'					=> $start_date,
		'end'					=> $DURATION_SECS ? $end_date : null,
		'at2400'				=> $AT2400,
		'duration_seconds'		=> !$NOTIME && $DURATION_SECS ? $DURATION_SECS : null,
		'sold_out'				=> $SOLD_OUT,
		'presale_sold_out'		=> $PRESALE_SOLD_OUT,
		'name'					=> $appic_title,
		'livestream'			=> $livestream,
		'livestream_address'	=> $LIVESTREAM_ADDRESS ?: null,
		'master_genres'			=> $MASTER_GENRES ? explode(',', $MASTER_GENRES) : [],
		'edition'				=> $EDITION ?: null,
		'lineup_complete'		=> $HAVE_LINEUP && !$INCOMPLETE_LINEUP,
		'timetable_released'	=> $TIMETABLE_RELEASED && !$INCOMPLETE_LINEUP && $HAVE_LINEUP,
		'genres_determined'		=> $determined,
		'erotic'				=> $EROTIC,
		'festival'				=> $FESTIVAL,
		'concert'				=> $CONCERT,
		'is_preparty'			=> $PREPARTY,
		'is_afterparty'			=> $AFTERPARTY,
		'nighttime'				=> $NIGHTTIME,
		'daytime'				=> $DAYTIME,
		'location_type'			=> explode(',', $LOCATIONTYPE),
		'min_age'				=> $MIN_AGE,
		'min_age_female'		=> $MIN_AGE_FEMALE	 ?: null,
		'max_age'				=> $MAX_AGE			 ?: null,
		'event_publication'		=> $EVENT_PSTAMP	 ?: null,
		'lineup_publication'	=> $LINEUP_PSTAMP	 ?: null,
		'timetable_publication'	=> $TIMETABLE_PSTAMP ?: null,
		'flyer_publication'		=> $FLYER_PSTAMP	 ?: null,
		'event_map'				=> have_uploadimage($event_map) ? FULL_IMAGES_HOST.uploadimage_get_url($event_map, UPIMG_GET_ORIGINAL | UPIMG_DEFINITIVE) : null,
	];

	if ($HAS_STATISTICS) {
		$obj['statistics_for_customer'] = get_party_statistics_link($event);
	}

	if ($FBIDSTR) {
		[$primary_event_fbid, $fb_ids, $error] = get_facebook_ids($FBIDSTR, 'party', $id);
		if ($error) {
			return $error;
		}
		$obj += [
			'fb_ids'		=> $fb_ids,
			'primary_fb_id'		=> $primary_event_fbid,
		];
	}

	if ($LIVESTREAM_ADDRESS) {
		$obj += [
			'livestream_embeddable'		=> $LIVESTREAM_EMBEDDABLE_ADDRESS ?: null,
			'livestream_is_embeddable'	=> $livestream_is_embeddable,
		];
	}

	if ($AFTERPARTIES) {
		foreach (explode(',', $AFTERPARTIES) as $afterpartyid) {
			$obj['afterparties'][] = (int)$afterpartyid;
		}
	}

	if ($PREPARTIES) {
		foreach (explode(',', $PREPARTIES) as $prepartyid) {
			$obj['preparties'][] = (int)$prepartyid;
		}
	}
	if ($picklist = get_event_advice($event, true)) {
		if (false === ($main_ids = db_simpler_array('party','
			SELECT DISTINCT '.PARTY_MAIN_ID.'
			FROM party
			WHERE PARTYID IN ('.implode(',',$picklist).')'
		))) {
			return error(500,'failed to get main_ids for advice');
		}
		if ($main_ids) {
			$obj['similar'] = $main_ids;
		}
	}

	if ($host_ids) {
		$obj['host_ids']	= $host_ids;
	}

	if ($DOOR_CLOSE_HOUR) {
		$obj['door_closes']	= sprintf('%02d:%02d',$DOOR_CLOSE_HOUR,$DOOR_CLOSE_MINS);
	}
	if ($RESTRICTION) {
		$obj['restrictions']	= explode(',',$RESTRICTION);
	}
	if ($DRESSCODE) {
		$obj += [
			'dresscode'				=> $DRESSCODE,
			'dresscode_mandatory'	=> $DRESSCODE_MANDATORY,
		];
	}

	if ($LOCKERS) {
		$obj['lockers'] = true;
	}
	if ($INVITEONLY) {
		$obj['inviteonly'] = true;
	}
	if ($DOORONLY) {
		$obj['dooronly'] = true;
	}
	if ($FREE_ENTRANCE) {
		$obj['free_entrance'] = true;
	}
	if ($PRESALE_STAMP) {
		$TIMEZONE && change_timezone($TIMEZONE);
		[$year, $month, $day, $hour, $mins] = _getdate($PRESALE_STAMP);
		$TIMEZONE && change_timezone();

		$obj['presale_start'] =
			$PRESALE_STAMP_NOTIME
		?	sprintf('%04d-%02d-%02d', 			$year, $month, $day)
		:	sprintf('%04d-%02d-%02d %02d:%02d',	$year, $month, $day, $hour, $mins);
	}

	$stuff = get_organization_stuff($id);

	if ($stuff) {
		[,, $all_orgs] = $stuff;
		if ($all_orgs) {
			$clean_name = static fn(string $input): string => str_replace(' ','',strtolower($input));
			$party_NAME = $clean_name($NAME);
			$orgs = [];

			foreach ($all_orgs as $organizationid) {
				if (null === ($org = create_organization($organizationid))) {
					continue;
				}
				if (!$org || http_response_code() !== 200) {
					return error(500, "failed to create_organization $organizationid");
				}

				$org_NAME = $clean_name($org['name']);

				$pct = 0;

				similar_text($party_NAME,$org_NAME,$pct);

				$org['event_match'] = round($pct);

				$orgs[] = $org;
			}
			if ($orgs) {
				$obj['organizations'] = $orgs;
			}
		}
	}

	$obj['ticketswap_url'] = 'https://partyflock.nl/order_ticket/ticketswap/'.$id.'?appic';

	[$ticket_url] = get_generic_presale_link($id, $event, 'appic');

	if (!$ticket_url
	&&	$PREREG_URL
	) {
		$ticket_url = 'https://partyflock.nl/jumpto/prereg/'.$id.'?appic';
	}

	if ($ticket_url === false) {
		error(500,'failed to get presale info');
	}

	$obj['ticket_url'] = $ticket_url;

	$location = null;
	if ($LOCATIONID
	&&	!$BOARDINGID
	) {
		$location = [
			'id'		=> $LOCATIONID,
			'name'		=> $LOCATION_NAME,
		];
		if (0+$LATITUDE) {
			$location['latitude'  ]= 0+$LATITUDE;
			$location['longitude'] = 0+$LONGITUDE;
		}
		if ($LOCATION_ADDRESS) {
			$location['address'] = $LOCATION_ADDRESS;
		}
		if ($LOCATION_ZIPCODE) {
			$location['zipcode'] = $LOCATION_ZIPCODE;
		}
		if ($LOCATION_FBIDSTR) {
			[$primary_fbid, $fb_ids, $error] = get_facebook_ids($LOCATION_FBIDSTR, 'location', $LOCATIONID);
			if ($error) {
				return $error;
			}
			$location += [
				'primary_fb_id'	=> $primary_fbid,
				'fb_ids'	=> $fb_ids,
			];
		}
		if (!($presence = get_presences_for_appic('location', $LOCATIONID))) {
			return error(500,'query failed to obtain organization presences');
		}
		if (!empty($presence[$LOCATIONID])) {
			$location['presences'] = $presence[$LOCATIONID];
		}
		if ($images = get_images('location', $LOCATIONID)) {
			$location['images'] = $images;
		}

	} elseif ($BOARDINGID) {
		$location = [
			'name'		=> $LOCATION_NAME ?: $BOARDING_NAME,
			'latitude'	=> $LATITUDE,
			'longitude'	=> $LONGITUDE,
		];
		if ($BOARDING_ADDRESS) {
			$location['address'] = $BOARDING_ADDRESS;
		}
		if ($BOARDING_ZIPCODE) {
			$location['zipcode'] = $BOARDING_ZIPCODE;
		}
	}

	if ($CITYID) {
		$city = [
			'id'		=> $CITYID,
			'name'		=> $CITY_NAME,
			'latitude'	=> $LATITUDE,
			'longitude'	=> $LONGITUDE,
		];
		if ($COUNTRYID) {
			$country = [
				'id'		=> $COUNTRYID,
				'alpha2'	=> $SHORT,
				'alpha3'	=> $SHORTA3,
			];

			$city['country'] = $country;
		}
		$location['city'] = $city;
	}

	$obj['location'] = $location;

	if ($GENRES) {
		$obj['genres'] = $genres;
	}
	if ($SITE
	&&	!url_is_bad('party',$event['PARTYID'])
	) {
		$obj['site'] = win1252_to_utf8($SITE);
	}

	if ($info = db_single('appic_event_info','
		SELECT BODY
		FROM appic_event_info
		WHERE MAINID = '.$MAIN_ID
	)) {
		$info = catch_presales($info, $id, for_appic: true);

		$obj['description'] = [
			'full'		=> plain_text($info,						UBB_UTF8),
			'teaser'	=> plain_text(get_teaser($info),	UBB_UTF8),
		];
	} elseif (
		$info !== false
	&&	($info = get_party_description($id, for_appic: true))
	) {
		[$teaser, $desc, $same] = $info;

		$replace_element = static fn (array $match) => $match['name'];

		$item_res = [
			'!\[(?<element>artist|location|organization|party)=(?<id>\d+) name="(?<name>.*?)"\]!u',
			'!\[(?<element>artist|location|organization|party) id=(?<id>\d+)](?<name>.*?)\[/$1\]!u'
		];

		$full = $desc;

		foreach ($item_res as $item_re) {
			$full = preg_replace_callback($item_re, $replace_element, $full);
		}

		$obj['description'] = [
			'full'	=> escape_utf8(plain_text($full, UBB_UTF8))
		];

		if (!$same) {
			$teaser = preg_replace_callback($item_re, $replace_element, $teaser);
			$obj['description']['teaser'] = escape_utf8(plain_text($teaser, UBB_UTF8));
		}
	}

	if ($images = get_images('party', $id)) {
		$obj['images'] = $images;
	}
	$movies = get_promo_videos($id) ?: [];

	if (false === ($extra_movies = db_rowuse_hash(['connect', 'video'],'
		SELECT VIDEOID, TITLE, TYPE, EXTERNALID, PSTAMP, CONTENTTYPE
		FROM connect
		JOIN video ON ASSOCID = VIDEOID
		WHERE '.event_videos($id)
	))) {
		return error(500,'failed to query movies');
	}

	$have_aftermovie = false;
	if ($extra_movies) {
		foreach ($extra_movies as $movie) {
			if ($movie['CONTENTTYPE'] === 'aftermovie') {
				$have_aftermovie = true;
				break;
			}
		}

		$movies += $extra_movies;
	}

	if (!$have_aftermovie
	&&	!empty($all_orgs)
		# get aftermovie?
		# NOTE: pick 4 years for aftermovie during 2 years after corona lockdowns, so we have mor
	&&	($videoids = get_aftermovie($event, $all_orgs, 2 * ONE_YEAR))
	&&	($videos = memcached_rowuse_hash('video','
		SELECT VIDEOID, TITLE, TYPE, EXTERNALID, PSTAMP, CONTENTTYPE, 1 AS PREVIOUS
		FROM video
		WHERE TYPE IN ("youtube", "facebook")
		  AND VIDEOID IN ('.implode(', ', $videoids).')'))
	) {
		$movies += $videos;
	}
	if ($movies) {
		$ok_movies = [];
		foreach ($movies as $movie) {
			if (empty($movie['VIDEOID'])) {
				error_log_r($movies, 'empty VIDEOID');
				continue;
			}
			if (!($meta = get_videothumb_meta($movie['VIDEOID']))) {
				mail_log('failed to get videothumb meta for video '.$movie['VIDEOID']);
				continue;
			}

			$ok_movie = [
				'id'		=> $movie['VIDEOID'],
				'promo'		=> $movie['PROMO'] ?? false,
				'url'		=> ($movie['TYPE'] === 'youtube' ? 'https://www.youtube.com/v/' : 'https://www.facebook.com/watch/?v=').$movie['EXTERNALID'],
#				'url'		=> ($movie['TYPE'] === 'youtube' ? 'https://www.youtube.com/embed/' : 'https://www.facebook.com/watch/?v=').$movie['EXTERNALID'],
				'source'	=> $movie['TYPE'],
				'type'		=> $movie['CONTENTTYPE'],
				'pstamp'	=> $movie['PSTAMP'],
				'thumbnail'	=> FULL_IMAGES_HOST."/images/video/{$movie['VIDEOID']}/{$meta['ID']}.jpg",
			];
			if ($movie['TITLE']) {
				$ok_movie['title'] = win1252_to_utf8($movie['TITLE']);
			}
			if (!empty($movie['PREVIOUS'])) {
				$ok_movie['previous_edition'] = true;
			}
			$ok_movies[] = $ok_movie;
		}
		if ($ok_movies) {
			$obj['movies'] = $ok_movies;
		}
	}

	if ($RECOGNITIONS) {
		$obj['recognitions'] = explode(chr(0xF), $RECOGNITIONS);
	}

	if (false === ($area_info = db_rowuse_hash('partyarea','SELECT AREAID, partyarea.* FROM partyarea WHERE PARTYID='.$id))) {
		return error(500,'failed to query areas');
	}
	if ($LINEUP_PSTAMP < CURRENTSTAMP) {
		if (false === ($lineups = db_rowuse_array(['lineup', 'artist', 'fbid', 'partyarea'], '
			SELECT	AREAID, LINEUPID,ARTISTID, lineup.TYPE, START_STAMP, START_STAMP_UTC, DURATION,DST, END_DST, SITE, LIVESTREAM_ADDRESS,
					artist.NAME,
					GROUP_CONCAT(FBID) AS FBIDSTR
			FROM lineup
			JOIN artist USING (ARTISTID)
			LEFT JOIN fbid ON ELEMENT = "artist" AND ID = ARTISTID
			LEFT JOIN partyarea USING (PARTYID, AREAID)
			WHERE PARTYID = '.$id.'
			  AND artist.ACCEPTED = 1
			GROUP BY AREAID, START_STAMP, ARTISTID'
		))) {
			return error(500,'failed to query lineup');
		}
		$areas = [];
		if ($lineups) {
			$single_lineup = single_lineup_multi_days($id);

			static $lineup_images = [];
			static $bios = [];
			static $artist_images = [];
			static $presences = [];

			foreach ($lineups as $lineup) {
				if (!isset($lineup_images[$lineup['LINEUPID']])
				&&	false === ($lineup_images[$lineup['LINEUPID']] = get_images('lineup', $lineup['LINEUPID']) ?: [])
				) {
					return error(500,'query failed to obtain artist image');
				}
				if (!isset($artist_images[$lineup['ARTISTID']])
				&&	false === ($artist_images[$lineup['ARTISTID']] = get_images('artist', $lineup['ARTISTID']) ?: [])
				) {
					return error(500,'query failed to obtain artist image');
				}
				if ($do_bios
				&&	!isset($bios[$lineup['ARTISTID']])
				&&	false === ($bios[$lineup['ARTISTID']] = db_simple_hash('bio',"
						SELECT SHORTCODE, BODY
						FROM bio
						JOIN language USING (LANGID)
						WHERE ELEMENT = 'artist'
						  AND ID = {$lineup['ARTISTID']}"
					) ?: [])
				) {
					return error(500,'query failed to obtain artist bio');
				}
				if ($do_presences
				&&	!isset($presences[$lineup['ARTISTID']])
				&&	!($presences[$lineup['ARTISTID']] = get_presences_for_appic('artist', $lineup['ARTISTID']) ?: [])
				) {
					return error(500, 'query failed to obtain artist presences');
				}
			}

			foreach ($lineups as $lineup) {
				/** @noinspection PhpRedundantOptionalArgumentInspection */
				extract($lineup, \EXTR_OVERWRITE);

				$entry = [
					'id'		=> $LINEUPID,
					'partyid'	=> $PARTYID,
					'type'		=> $TYPE,
				];

				if ($lineup['LIVESTREAM_ADDRESS']) {
					$entry['livestream_address'] = $lineup['LIVESTREAM_ADDRESS'];
					require_once '_livestream.inc';
					$entry['livestream_embeddable'] = get_embeddable_livestream($lineup['LIVESTREAM_ADDRESS']);
				}

				$artist = [
					'id'	=> $ARTISTID,
					'name'	=> $NAME,
					'type'	=> $TYPE,
				];

				if (!empty($lineup_images[$LINEUPID])) {
					$entry['images'] = $lineup_images[$LINEUPID];
				}
				if (!empty($artist_images[$ARTISTID])) {
					$artist['images'] = $artist_images[$ARTISTID];
				}
				if (!empty($bios[$ARTISTID])) {
					foreach ($bios[$ARTISTID] as $lang => $text) {
						$artist['biography'][$lang] = plain_text($text,UBB_UTF8);
					}
				}
				if (!empty($presences[$ARTISTID])) {
					$artist['presences'] = $presences[$ARTISTID];
				}

				if ($FBIDSTR) {
					[$primary_fbid, $fb_ids, $error] = get_facebook_ids($FBIDSTR, 'artist', $ARTISTID);
					if ($error) {
						return $error;
					}
					$artist += [
						'primary_fb_id'	=> $primary_fbid,
						'fb_ids'		=> $fb_ids,
					];
				}

				if ($START_STAMP_UTC
//				&&	$TIMETABLE_PSTAMP < CURRENTSTAMP
				) {
					[,,, $hour, $mins] = _getdate($START_STAMP_UTC,'UTC');
					$entry['time_start'] = $hour * 100 + $mins;
					if ($DURATION) {
						[,,,$hour, $mins] = _getdate($START_STAMP_UTC + $DURATION,'UTC');
						$entry['time_end'] = $hour * 100 + $mins;
					}
				}

				if ($START_STAMP) {
					$entry['start_stamp']	= $START_STAMP;
					$entry['duration']		= $DURATION;
				}
				if ($START_STAMP_UTC) {
					$entry['start_stamp_utc'] = $START_STAMP_UTC;
				}

				if (!isset($areas[$lineup['AREAID']])) {
					$current_area = getifset($area_info, $lineup['AREAID']);

					$area = [
						'unknownday'	=> (bool)$single_lineup,
						'order_id'		=> $lineup['AREAID']
					];

					if ($current_area) {
						if ($current_area['NAME_FOR_APPIC']) {
							$current_area['NAME'] = $current_area['NAME_FOR_APPIC'];
						}

						$area['id'] = $current_area['PARTYAREAID'];

						if ($current_area['HOSTEDBYID']) {
							$area['host_id'] = $current_area['HOSTEDBYID'];
						}
						if ($current_area['HOSTEDBY']) {
							$area['hostedby'] = $current_area['HOSTEDBY'];
						}
						if (empty($current_area['NAME'])) {
							if (!empty($current_area['HOSTEDBYID'])) {
								$current_area['NAME'] = get_element_title('organization',$current_area['HOSTEDBYID']);
							} elseif (!empty($current_area['HOSTEDBY'])) {
								$current_area['NAME'] = $current_area['HOSTEDBY'];
							}
						}
						foreach (['NAME', 'GENRES'] as $key) {
							if (!empty($current_area[$key])) {
								$area[$key === 'GENRES' ? 'extra' : strtolower($key)] = $current_area[$key];
							}
						}
					}
					$areas[$lineup['AREAID']] = $area;
				}
				$entry['artist'] = $artist;

				$areas[$lineup['AREAID']]['performances'][] = $entry;
			}
			$obj['areas'] = array_values($areas);
		}
	}

	$tags = get_special_day_event_tags($event);

	# summer start: friday before or on 21 June
	# summer end: monday on or after 23 September
	/** @noinspection InsufficientTypesControlInspection */
	/** @noinspection NotOptimalIfConditionsInspection */
	if ($FESTIVAL
	&&	false !== ($summer_start = strtotime($start_year.'-06-21 friday previous week',	$STAMP))
	&&	false !== ($summer_end   = strtotime($start_year.'-09-23 monday next week',		$STAMP))
	&&	$STAMP >= $summer_start
	&&	$STAMP  < $summer_end
	) {
		$tags[] = 'summer_festival';
	}
	if ($wholes
	&& in_array(3776, $wholes, true)
	) {
		$tags[] = 'ADE';
	}
	if ($tags) {
		$obj['tags'] = $tags;
	}
	return $obj;
}

function get_presences_for_appic(string $element, int|array $arg): array {
	require_once '_presence.inc';
	$presences = [];
	foreach (is_array($arg) ? $arg : [$arg] as $id) {
		$infos = null;
		if (!have_presence($element, $id, $infos)
		||	!$infos
		) {
			continue;
		}
		if (isset($infos['facebookpage'])) {
			# prefer facebookpage if not offline
			foreach ($infos['facebookpage'] as $site => $info) {
				if ($info['OFFLINE']) {
					continue;
				}
				$infos['facebook'] = [$site => $info];
				break;
			}
			unset($infos['facebookpage']);
		}
		foreach ($infos as $type => $sites) {
			foreach ($sites as $site => $info) {
				if ($info['OFFLINE']) {
					continue;
				}
				$presences[$id][$type] = $site;
			}
		}
	}
	return $presences;
}

function get_images(string $element, int $id, int $flags = 0): ?array {
	require_once '_partyimage.inc';

	$image = uploadimage_get($element, $id, size: 'original', flags: $flags);

	$flags = UPIMG_GET_ORIGINAL | UPIMG_DEFINITIVE;

	if (!$image
	&&	$element === 'party'
	&&	!($image = uploadimage_get('party2', $id, size: 'original', flags: $flags))
	&&	($party = memcached_party_and_stamp($id))
	) {
		[,, $alternative_image] = get_front_and_back_flyer($party);
		if ($alternative_image) {
			$image = uploadimage_get(
				$alternative_image['TYPE'],
				$alternative_image['ID'],
				'original',
				$flags
			);
		}
	}
	assert(is_array($image)); # Satisfy EA inspection
	if (!have_uploadimage($image)) {
		return null;
	}
	if (false === ($images = empty($image['IMGS']) ? $image : $image['IMGS'])) {
		return error(500, 'query failed to obtain images');
	}
	if (!$images) {
		return $images;
	}
	$result = [];
	foreach ($images as $image) {
		/** @noinspection PhpRedundantOptionalArgumentInspection */
		extract($image, \EXTR_OVERWRITE);
		if (!empty($image['GENERATED'])
		&&	 empty($image['CHOSEN'])
		) {
			continue;
		}
		# Appic can't handle large images:
		$original_WIDTH  = $WIDTH;
		$original_HEIGHT = $HEIGHT;
		# scale($WIDTH, $HEIGHT, 2052);
		scale_max_pixels($WIDTH, $HEIGHT, 20_000_000);

		$img = [
			'mimetype'			=> 'image/'.($FILETYPE === 'jpg' ? 'jpeg' : $FILETYPE),
			'orientation'		=> $ORIENTATION,
			'original_width'	=> $original_WIDTH,
			'original_height'	=> $original_HEIGHT,
			'width'				=> $WIDTH,
			'height'			=> $HEIGHT,
			'aspect_crop'		=> false,
			'url'				=> FULL_IMAGES_HOST.uploadimage_get_url($image, $flags),

			'generated'			=> empty($image['GENERATED'])		? null : $image['GENERATED'],
			'generated_src'		=> empty($image['GENERATED_SRC'])	? null : $image['GENERATED_SRC'],
		];

		$result[] = $img;

		if (empty($image['GENERATED'])) {
			$o[$ORIENTATION] = $img;
		}
	}
	if ($element !== 'party') {
		return $result;
	}
	$pick = $o['landscape'] ?? $o['square'] ?? $o['portrait'] ?? null;
	$want_aspect = 1.8;

	if ($pick) {
		$aspect = $pick['width'] / $pick['height'];

		$diff = $aspect - $want_aspect;

		$diff_pct = 100 - (100 * $aspect / $want_aspect);

		if ($diff < 0
		&&	$diff_pct > 5
		) {
			# aspect smaller

			$new_height = floor($pick['width'] / $want_aspect);

			$pick['aspect_crop'] = true;

			$pick['url'] = str_replace(
				$pick['width'].'x'.$pick['height'],
				$pick['width'].'x'.($pick['height'] = $new_height),
				$pick['url']
			);

			$pick['generated'] = 'smartcrop';
			$pick['generated_src'] = $pick['orientation'];

			if ($pick['orientation'] === 'landscape') {
				if (!NEW_DUAL_LANDSCAPES) {
					# replace the original landscape version with this one
					foreach ($result as &$img) {
						if ($img['orientation'] === 'landscape') {
							$img = $pick;
							break;
						}
					}
					unset($img);
				} else {
					# add cropped
					$result[] = $pick;
				}
			} else {
				$pick['orientation'] = 'landscape';
				$result[] = $pick;
			}
		}
	}
	return $result;
}

function create_photo_feed($element) {
	if (empty($_SERVER['eID'])) {
		return error(400,'missing id');
	}
	require_once '_date.inc';

	# getting update_time using information_schema is faster than SHOW TABLE STATUS LIKE
	if (!($statuses = db_simple_hash('information_schema',"
		SELECT table_name, update_time
		FROM information_schema.TABLES
		WHERE TABLE_SCHEMA = 'party_db'
		  AND TABLE_NAME IN ('image', 'artist_appearance')"))
	) {
		return error(503, 'failed to get information_schema.TABLE');
	}

	$key = "appicphotos:v2:$element:".($_SERVER['eFB'] ? '' : 'pf:')."{$_SERVER['eID']}.{$statuses['artist_appearance']}.{$statuses['image']}";

	if ($result = isset($_REQUEST['NOMEMCACHE']) ? null : memcached_get($key)) {
		return $result;
	}

	if ($element === 'gallery') {
		$galleryidstr = $_SERVER['eID'];
		$topcropped = db_single_int('gallery', "
			SELECT TOPCROPPED
			FROM gallery
			WHERE GALLERYID IN ($galleryidstr)"
		);
	} elseif ($_SERVER['eFB']) {
		$topcropped = db_single_int(['gallery', 'party', 'fbid'], "
			SELECT MIN(TOPCROPPED)
			FROM gallery
			JOIN party USING (PARTYID)
			JOIN fbid ON ELEMENT = 'party' AND ID = PARTYID
			WHERE FBID = {$_SERVER['eID']}"
		);
	} else {
		$partyidstr = $_SERVER['eID'];
		$topcropped = db_single_int('gallery', "
			SELECT MIN(TOPCROPPED)
			FROM gallery
			WHERE PARTYID IN ($partyidstr)"
		);
	}
	if (!$topcropped) {
		if ($topcropped === false) {
			return error(503, 'failed to determine cropping state');
		}
		return error(http_status::ENHANCE_YOUR_CALM->value, 'photos not cropped yet');
	}
	if ($element === 'gallery') {
		$images = memcached_rowuse_hash(['image', 'topcrop', 'party'], /** @lang MariaDB */ '
			SELECT IMGID, '.PARTY_MAIN_ID.", topcrop.ID, image.WIDTH, image.HEIGHT, image.GALLERYID
			FROM image
			JOIN party USING (PARTYID)
			LEFT JOIN topcrop ON SRC = 'photo' AND topcrop.ID = IMGID
			WHERE HIDDEN = 0
			  AND GALLERYID IN ($galleryidstr)"
		);
	} elseif ($_SERVER['eFB']) {
		$images = memcached_rowuse_hash(['image', 'fbid', 'topcrop', 'party'], /** @lang MariaDB */ '
			SELECT IMGID, '.PARTY_MAIN_ID.", topcrop.ID, image.WIDTH, image.HEIGHT, image.GALLERYID
			FROM image
			JOIN party USING (PARTYID)
			JOIN fbid ON ELEMENT = 'party' AND fbid.ID = PARTYID
			LEFT JOIN topcrop ON SRC='photo' AND topcrop.ID = IMGID
			WHERE HIDDEN = 0
			  AND FBID = {$_SERVER['eID']}"
		);
	} else {
		/** @noinspection PhpUndefinedVariableInspection */
		$images = memcached_rowuse_hash(['image', 'party', 'topcrop'], /** @lang MariaDB */ '
			SELECT IMGID, '.PARTY_MAIN_ID.", topcrop.ID, image.WIDTH, image.HEIGHT, image.GALLERYID
			FROM image
			JOIN party USING (PARTYID)
			LEFT JOIN topcrop ON SRC = 'photo' AND topcrop.ID = IMGID
			WHERE HIDDEN = 0
			  AND (".PARTY_MAIN_ID_INC." IN ($partyidstr) OR PARTYID IN ($partyidstr))"
		);
	}
	if (!$images) {
		if ($images === false) {
			return error(503,'failed to query photos');
		}
		return [];
	}
	foreach ($images as /* $imgid => */ $image) {
		if (!$image['ID']
		&&	$image['WIDTH'] !== $image['HEIGHT']
		) {
			# gallery is probably still being processed.
			# just return empty array until it's done
			return [];
		}
	}
	$photos = [];
	foreach ($images as $imgid => $image) {
		$uri = get_photo_url($imgid,'main',true);
		$thumb_uri = str_replace(
			[	'/main/',
				'@2x',
			],
			[	'/400x400/',
				'',
			],
			$uri
		);
		$photos[] = [
			'id'		=> (int)$imgid,
			'main_id'	=> (int)$image['MAIN_ID'],
			'galleryid'	=> $image['GALLERYID'],
			'thumbnail'	=> $thumb_uri,
			'photo'		=> $uri,
		];
	}

	$result = [
		'photos'	=> $photos,
	];

	memcached_set($key, $result, ONE_HOUR);

	return $result;
}
function create_gallery_feed(): array {
	$since = get_header('X-SINCE');

	if (!is_number($since)) {
		$since = 0;
	}
	if (!($galleries = db_rowuse_hash(['gallery', 'party'], /** @lang MariaDB */ '
		SELECT GALLERYID, PSTAMP,' .PARTY_MAIN_ID.'
		FROM gallery
		JOIN party USING (PARTYID)
		WHERE (	  TOPCROPPED
			  AND VISIBLE = "yes"
			   OR GALLERYID = 6669
			  )
		  AND PSTAMP <= '.CURRENTSTAMP."
		  AND PSTAMP > $since
		ORDER BY PSTAMP DESC"))
	) {
		if ($galleries === false) {
			return error(503, 'failed to get galleries');
		}
		return [];
	}
	$result = [];
	foreach ($galleries as $galleryid => $gallery) {
		$result[] = [
			'galleryid'	=> $galleryid,
			'main_id'	=> $gallery['MAIN_ID'],
			'published'	=> $gallery['PSTAMP'],
		];
	}
	return ['galleries' => $result];
}

function select_genres(string $element): string {
	return '(
		SELECT GROUP_CONCAT(CONCAT(genre.GID," :", genre.NAME))
		FROM '.$element.'_genre
		JOIN genre USING (GID)
		WHERE '.$element.'_genre.'.$element.'ID = '.$element.'.'.$element.'ID
	) AS GENRES';
}

function add_genres(string $genrestr, array &$item): void {
	foreach (explode(',', $genrestr) as $genre_info) {
		[/* $gid */, $name] = explode(':', $genre_info);
		$item['genres'][] = get_genre_for_appic($name);
	}
}

function get_genre_for_appic(string $genre): string {
	return match($genre) {
		default		=> $genre,
		'acid techno'	=> 'acidtechno',
		'blues rock'	=> 'bluesrock',
		'deep house'	=> 'deephouse',
		'electro swing'	=> 'electroswing',
		'hard dance'	=> 'harddance',
		'hard techno'	=> 'hardtechno',
		'hard trance'	=> 'hardtrance',
		'tech house'	=> 'techhouse',
	};
}
