<?php

function show_map_link($item,$zoom = 13,$type = 'maps') {
	require_once '_google_maps.inc';

/*	?><a target="_blank" href="https://maps.google.com/?q=<?
	$parts = array(
		$_REQUEST['sELEMENT'] == 'city' ? $item['NAME'] : win1252_to_utf8($item['NAME'])
	);
	if (!empty($item['ZIPCODE'])) {
		$parts[] = win1252_to_utf8($item['ZIPCODE']);
	}
	if (!empty($item['ADDRESS'])) {
		$parts[] = $item['ADDRESS'];
	}
	if (!empty($item['CITYNAME'])) {
		$parts[] = $item['CITYNAME'];
	}
	if (!empty($item['COUNTRYID'])) {
		$parts[] = win1252_to_utf8(get_element_title('country',$item['COUNTRYID']));
	}
	echo urlencode(implode(', ',$parts));

	if (!empty($item['LONGITUDE'])
	&&	!empty($item['LATITUDE'])
	) {
		$lat = rtrim($item['LATITUDE'],'0');
		$lon = rtrim($item['LONGITUDE'],'0');

		echo	urlencode(' @'),
			$lat,',',$lon;
	}

	?>&amp;z=<?= $zoom,maptypeurlarg()
	?>&amp;hl=<?= CURRENTLANGUAGE ?>"><?
	show_map_image($type);
	?></a><?*/

	$latitude  = (string)$item['LATITUDE'];
	$longitude = (string)$item['LONGITUDE'];

	$latlon = rtrim($latitude, '0').','.rtrim($longitude, '0');

	?><a target="_blank" href="https://www.google.com/maps/place/<?= $latlon ?>/@<?= $latlon ?>,<?= $zoom ?>z?hl=<?= CURRENTLANGUAGE ?>"><?
	show_map_image($type);
	?></a><?
}
function show_map_image($type): void {
	$class = '';
	switch ($type) {
	case 'minimap':
		$file = 'minimap';
		$class .= 'mini ';
		if (LITE) {
			$class .= 'bshd ';
		}
		break;
	default:
		$file = $type;
		break;
	case 'header':
		$file = 'maps';
		break;
	}
	require_once '_browser.inc';
	?><img<?
	?> class="<?= $class ?>maplnk"<?
	?> alt="<?= __('maps:on_the_map') ?>"<?
	?> src="<?= STATIC_HOST ?>/images/<?= $file, is_high_res() ?>.png"><?

	if ($type === 'header') {
		?><div class="ib" style="width:48px"></div><?
	}
}
