<?php

require_once '_ad.inc';
require_once '_banner.inc';
require_once '_countries.inc';
require_once '_distribution.inc';
require_once '_ticket.inc';

function banner_needs_url(string|array $arg): bool {
	return in_array(
		is_array($arg) ? $arg['TYPE'] : $arg,
		['upload', 'link', 'floormovie'],
		true
	);
}

function preamble(): void {
	require_once '_info.inc';
	info_redirect_legacy();
}

function perform_commit(): ?bool {
	switch ($_REQUEST['ACTION']) {
	case 'commit':					return ad_commit();
	case 'activate':				return ad_set_active(1);
	case 'deactivate':				return ad_set_active(0);
	case 'remove':					$remove = true;
	case 'restore':					require_once '_object.inc'; return set_field('REMOVED', $remove ?? false, 'restored');
	case 'calculate-distribution':	return ad_calculate_distribution();
	case 'remove-distribution':		require_once '_distribution.inc'; return remove_distribution('ad', $_REQUEST['sID'], true);
	default:						return null;
	}
}

function display_body(): void {
	switch ($_REQUEST['ACTION']) {
	default:			not_found('ad'); return;
	case 'archive':
	case null:			ad_display_overview(); return;
	case 'form':		ad_display_form(); return;
	case 'commit':
	case 'single':
	case 'activate':
	case 'deactivate':
	case 'calculate-distribution':
	case 'remove-distribution':
	case 'remove':
	case 'restore':		ad_display_single(); return;
	case 'comments':
	case 'comment':
	case 'simple':		ad_display_simple(); return;
	case 'status':		ad_display_status(); return;
	case 'reserved':	require_once '_distribution_reserved.inc'; show_distribution_reserved('ad'); return;
	case 'cpcs':		ad_display_cpcs(); return;
	}
}
function ad_display_cpcs(): void {
	if (!require_admin('ad')) {
		no_permission();
		return;
	}
	$adids = db_rowuse_hash(['ad', 'adinfo', 'banner'],'
		SELECT	ADID, POSITION, IMPRESSIONSDONE, IF(EXPLICIT, PRICE, NULL) AS PRICE, STARTSTAMP, STOPSTAMP,
				ADPRICEID,
				SIZETYPE
		FROM ad
		LEFT JOIN adinfo USING (ADID)
		LEFT JOIN banner USING (BANNERID)
		WHERE STOPSTAMP < '.TODAYSTAMP.'
		  AND STARTSTAMP >= '.(TODAYSTAMP - 6 * ONE_MONTH).'
		  AND ACTIVE
		  AND IMPRESSIONSDONE > 1000
		  AND NOT ad.REMOVED'
	);
	$table = 'adclick';
	$element = 'AD';
	if (false === ($clickinfos = db_rowuse_hash($table, "
		SELECT	{$element}ID,
				COUNT(*) AS CLICKS,
				COUNT(DISTINCT IF(USERID NOT IN (0, 1), USERID, NULL)) AS USERID_CLICKS,
				COUNT(DISTINCT IF(USERID IN (0, 1) AND IDENTID, IDENTID, NULL)) AS IDENTID_CLICKS,
				COUNT(DISTINCT IF(USERID IN (0, 1) AND NOT IDENTID, IPBIN, NULL)) AS IPBIN_CLICKS,
				MIN(STAMP) AS FIRST_CLICK
		FROM $table
		WHERE {$element}ID IN (".implodekeys(', ', $adids).")
		GROUP BY {$element}ID"))
	) {
		return;
	}
	foreach ($adids as $adid => $ad) {
		$posses[$ad['POSITION']][$adid] = $ad;
	}
	include_js('js/sorttable');

	require_once '_price.inc';

	foreach ($posses as $pos => $ads) {
		layout_open_box('white');
		layout_box_header(__C('adpos:'.$pos));
		?><table class="fw sortable"><?
		?><tr><?
		?><th>ad</th><?
		?><th class="right hpad">ctr</th><?
		?><th class="right hpad">cpc</th><?
		?><th class="right hpad">uctr</th><?
		?><th class="right hpad">ucpc</th><?
		?></tr><?

		$total_cpc = 0;
		$total_ucpc = 0;

		$total_ctr = 0;
		$total_uctr = 0;

		$all_ctrs = [];
		$all_uctrs = [];
		$all_cpcs = [];
		$all_ucpcs = [];

		$cnt = 0;

		foreach ($ads as $adid => $ad) {
			if (!$ad['IMPRESSIONSDONE']) {
				continue;
			}

			$baseprice = $cpday = 0;

			if ($ad['ADPRICEID']
			&&	(	$ad['ADPRICEID'] < 5000
				||	$ad['SIZETYPE']
				)
			) {
				if (!($adprice = db_single_array('adpricesize',
						$ad['ADPRICEID'] < 5000
					? "	SELECT BASEPRICE, CPDAY
						FROM adpricesize
						WHERE ID = {$ad['ADPRICEID']}"
					: "	SELECT BASEPRICE, 0
						FROM adpricesize
						WHERE ADPRICEID = {$ad['ADPRICEID']}
						  AND SIZETYPE IN (0, {$ad['SIZETYPE']})
						  AND ADPOS IN (0, {$ad['POSITION']})
						ORDER BY SIZETYPE DESC, ADPOS DESC
						LIMIT 1"
				))) {
					if ($adprice !== false) {
						register_error('price:error:could_not_be_determined_LINE');
					}
					continue;
				}
				[$baseprice, $cpday] = $adprice;
			}

			if (!$baseprice
			&&	!$cpday
			&&	!$ad['PRICE']
			) {
				continue;
			}

			if ($ad
			&& !$ad['PRICE']
			&&	$ad['PRICE'] !== null
			) {
				# free
				continue;
			}

			$partyad = $ad['POSITION'] === ADPOS_PARTYLIST;

			$price = $ad['PRICE'];

			$realprice = 0;

			if (!$price) {
				if ($partyad) {
					$a = _getdate($ad['STARTSTAMP']);
					$b = _getdate($ad['STOPSTAMP']);
					if ($a[7] !== $b[7]) {
						continue;
					}
					$duration = $ad['STOPSTAMP'] - $ad['STARTSTAMP'];
					$days = ceil($duration / ONE_DAY);
					$realprice = $cpday * $days;
				} else {
					$realprice = $baseprice * $ad['IMPRESSIONSDONE'] / 1000;
				}
			}

			if (!$realprice) {
				continue;
			}

			?><tr><?
			?><td><?
			echo get_element_link('ad',$ad['ADID']);
			?></td><?
			?><td class="tt right hpad" style="color: #FF0;"><?
			# CTR
			$clicks = $clickinfos[$adid]['CLICKS'] ?? 0;
			$ctr = 100 * $clicks / $ad['IMPRESSIONSDONE'];
			printf('%.02f',$ctr);
			?></td><?
			?><td class="tt right hpad" style="color: #FF0;><?

			$cpc = $realprice / ($clicks ?: 1);

			echo get_price($cpc,'EUR',true);

			?></td><?
			?><td class="tt right hpad" style="color:#0F0"><?
			# UCTR
			$unique =
				($clickinfos[$adid]['USERID_CLICKS'] ?? 0)
			+	($clickinfos[$adid]['IDENTID_CLICKS'] ?? 0)
			+	($clickinfos[$adid]['IPBIN_CLICKS'] ?? 0);
			$uctr = 100 * $clicks / $ad['IMPRESSIONSDONE'];
			printf('%.02f',$uctr);
			?></td><?
			?><td class="tt right hpad" style="color:#0F0"><?

			$ucpc = $realprice / ($clicks ?: 0);

			echo get_price($ucpc,'EUR',true);

			?></td><?
			?></tr><?

			$total_ctr += $ctr;
			$total_uctr += $uctr;

			$total_cpc += $cpc;
			$total_ucpc += $ucpc;

			$all_ctrs[] = $ctr;
			$all_uctrs[] = $ctr;

			$all_cpcs[] = $cpc;
			$all_ucpcs[] = $ucpc;

			++$cnt;
		}

		sort($all_cpcs);
		sort($all_ucpcs);
		sort($all_ctrs);
		sort($all_uctrs);

		$median_ctr = $all_ctrs[$cnt >> 1];
		$median_uctr = $all_uctrs[$cnt >> 1];
		$median_cpc = $all_cpcs[$cnt >> 1];
		$median_ucpc = $all_ucpcs[$cnt >> 1];

		$avg_ctr = $total_ctr / $cnt;
		$avg_uctr = $total_uctr / $cnt;
		$avg_cpc= $total_cpc / $cnt;
		$avg_ucpc = $total_ucpc / $cnt;

		?><tr class="bold"><?
		?><td>average</td><?
		?><td class="right hpad" style="color:#FF0"><?
		printf('%.02f',$avg_ctr);
		?></td><?
		?><td class="right hpad" style="color:#FF0"><?
		echo get_price($avg_cpc,'EUR',true);
		?></td><?
		?><td class="right hpad" style="color:#0F0"><?
		printf('%.02f',$avg_uctr);
		?></td><?
		?><td class="right hpad" style="color:#0F0"><?
		echo get_price($avg_ucpc,'EUR',true);
		?></td><?
		?></tr><?

		?><tr class="bold"><?
		?><td>median</td><?
		?><td class="right hpad" style="color:#FF0"><?
		printf('%.02f',$median_ctr);
		?></td><?
		?><td class="right hpad" style="color:#FF0"><?
		echo get_price($median_cpc,'EUR',true);
		?></td><?
		?><td class="right hpad" style="color:#0F0"><?
		printf('%.02f',$median_uctr);
		?></td><?
		?><td class="right hpad" style="color:#0F0"><?
		echo get_price($median_ucpc,'EUR',true);
		?></td><?
		?></tr><?

		?><tr><?
		?><th>ad</th><?
		?><th class="right hpad">ctr</th><?
		?><th class="right hpad">cpc</th><?
		?><th class="right hpad">uctr</th><?
		?><th class="right hpad">ucpc</th><?
		?></tr><?


		?></table><?

		layout_close_box();
	}
}
function ad_calculate_distribution(): bool {
	if (!require_admin('ad')) {
		no_permission('ad');
		return false;
	}
	if (!($adid = $_REQUEST['sID'])) {
		not_found('ad', $adid);
		return false;
	}
	if (!($ad = db_single_assoc(['ad', 'banner'], '
		SELECT IMPRESSIONS, POSITION, IMPRESSIONSDONE, STARTSTAMP, STOPSTAMP, HEIGHT, DOMAIN
		FROM ad
		JOIN banner USING (BANNERID)
		WHERE ADID = '.$adid))
	) {
		$ad !== false && not_found('ad', $adid);
		return false;
	}
	if (/*false === ($citylimits =
			!empty($_POST['CITYLIMIT'])
			?	db_simple_hash('adforcityspec','SELECT CITYID, RADIUS FROM adforcityspec WHERE ADID ='.$adid) : null)
	||	*/false === ($section_spec =
			(	!empty($_POST['FLAGS'])
			&&	 ($_POST['FLAGS'] & ADFLG_SECTION_LIMIT))
			?	db_same_hash('adforsection', 'SELECT SECTIONID FROM adforsection WHERE ADID = '.$adid) : null)
	) {
		return false;
	}
	require_once '_distribution.inc';
	/** @var array $ad */
	if (!($distribution = calculate_distribution(
		$ad['POSITION'] === ADPOS_SMALL_SCREEN ? ADTYPE_SMALL_SCREEN : ADTYPE_REGULAR,
		$adid,
		$ad['DOMAIN'],
		$ad['STARTSTAMP'],
		$ad['STOPSTAMP'],
		$ad['IMPRESSIONS'],
		$ad['IMPRESSIONSDONE'],
		$maximp,
		$ad['POSITION'],
		section_spec: $section_spec))
	) {
		if ($maximp) {
			register_error('ad:error:too_little_room_LINE', DO_UBB,  ['ADID' => $adid, 'MAX_IMPRESSIONS' => floor($maximp)]);
		}
		return false;
	}
	if (!store_distribution($adid, $distribution)) {
		return false;
	}
	register_notice('distribution:notice:recalculated_LINE');
	return true;
}
function ad_menu(bool $ad_admin = true) {
	layout_open_menu();
	if ($ad_admin
	||	have_relation()
	) {
		$action = $_REQUEST['ACTION'];
		layout_menuitem(Eelement_name('overview'),'/ad',empty($action));
		if ($ad_admin) {
			layout_menuitem(Eelement_name('archive'),	'/ad/archive',	$action === 'archive');
			layout_menuitem(Eelement_name('status'),	'/ad/status',	$action === 'status');
			layout_menuitem(__C('status:reserved'),		'/ad/reserved',	$action === 'reserved');
			layout_menuitem(Eelement_plural_name('banner'),	'/banner');
		}
		layout_continue_menu();
	}
	require_once '_info.inc';
	show_info_menu();
#	doc_promo_and_ad_menupart();
	layout_close_menu();
}
function ad_single_menu(?array $ad = null): void {
	layout_open_menu();
	if ($ad) {
		$adid = $ad['ADID'];
		$adhref = '/ad/'.$adid;
		$active = $ad['ACTIVE'];
		layout_menuitem(__C('action:change'),$adhref.'/form');
		layout_next_menuitem();
		if (!$active
		&&	(	 $ad['REMOVED']
			||	!$ad['URL'] && banner_needs_url($ad)
			)
		) {
			?><span class="unavailable"><?=  __C('action:activate') ?></span><?

		} elseif ($ad['STOPSTAMP'] < CURRENTSTAMP - FREQCAP_KEEP_PERIOD + 2 * ONE_DAY) {
			# freqcaps/adimps archived
			?><span class="unavailable"><?=  __C('action:activate') ?></span><?

		} elseif (
			!$active
		&&	!$ad['KEEPTOP']
		&&	!$ad['IMPRESSIONS']
		) {
			?><span class="unavailable"><?=  __C('action:activate') ?></span><?

		} elseif (
			!$active
		&&	!$ad['KEEPTOP']
		&&	!ad_distribution_alright($adid)
		) {
			?><span class="unavailable"><?=  __C('action:activate') ?></span><?

		} else {
			?><span class="<?= $active ? 'notice' : 'warning' ?>"><?=  __C($active ? 'status:active' : 'status:inactive') ?> &rarr; </span><?
			?><a<?
			?> href="/ad/<?= $adid ?>/<?= $active ? 'deactivate' : 'activate' ?>"><?=
				 __C($active ? 'action:deactivate' : 'action:activate');
			 ?></a><?
		}
		layout_close_menuitem();
		layout_continue_menu();

		require_once '_adlink.inc';
		show_adlink($ad);

		require_once '_distribution.inc';
		show_distribution_menuitems($ad);
		show_element_menuitems($ad);
	} else {
		layout_menuitem(__C('action:add'),'/ad/form');
		layout_menuitem(__C('action:add_banner'),'/banner/form');
		layout_continue_menu();
		layout_open_menuitem();

	}
	layout_close_menuitem();
	layout_close_menu();
}
function ad_display_status() {
	layout_show_section_header();

	if (!require_admin('ad')) {
		return;
	}
	ad_menu(true);

	?><div id="adstatus" element="ad"></div><?
	include_js('js/adstatus');
	include_style('votes');
}

function ad_display_overview() {
	require_once '_adlist.inc';
	require_once '_profilefilter.inc';
	layout_show_section_header();

	if (!have_admin('ad')) {
		if (!require_relation()) {
			return;
		}
		ad_menu(false);
		if (!($adlist = memcached_rowuse_array(['ad', 'adinfo', 'relation', 'relationmember', 'banner', 'party', 'distribution_ad'],'
			SELECT	ad.ADID, STARTSTAMP, STOPSTAMP, ad.IMPRESSIONS, IMPRESSIONSDONE, ACTIVE, ad.POSITION, ad.PARTYID, ad.REMOVED, TAKEOVER,
					banner.BANNERID, banner.NAME AS BANNER_NAME,
					adinfo.RELATIONID, relation.NAME AS RELATION_NAME,
					GROUP_CONCAT(party.NAME) AS PARTY_NAMES,
					NOT KEEPTOP AND (SELECT 1 FROM distribution_ad da WHERE da.ADID = ad.ADID LIMIT 1) IS NULL AS MISSING_DISTRIBUTION,
					'.profile_filters_for_query('ad',true).'
			FROM ad
			JOIN adinfo ON adinfo.ADID = ad.ADID
			LEFT JOIN banner ON banner.BANNERID = ad.BANNERID
			LEFT JOIN bannermultiparty AS bmp ON bmp.BANNERID = ad.BANNERID
			LEFT JOIN party ON party.PARTYID = bmp.PARTYID
			LEFT JOIN distribution_ad AS d
				 ON d.ADID = ad.ADID
				AND HOUR = HOUR(NOW())
				AND DAYNUM = TO_DAYS(NOW())
			JOIN relation ON relation.RELATIONID = adinfo.RELATIONID
			JOIN relationmember ON relationmember.RELATIONID = adinfo.RELATIONID
			WHERE relationmember.USERID = '.CURRENTUSERID.'
			GROUP BY ad.ADID
			ORDER BY STOPSTAMP DESC'))
		) {
			if ($adlist !== false) {
				?><div class="block"><?= __('element:info:no_items_found_LINE', ['ELEMENT' => 'ad']) ?></div><?
			}
			return;
		}
		layout_open_box('adx');
		layout_open_table('default fw vtop');
		_adlist_display_rowlist($adlist);
		layout_close_table();
		layout_close_box();
		return;
	}

	ad_menu();
	ad_single_menu();

	if ($_REQUEST['ACTION'] === 'archive') {
		if (!($adlist = db_rowuse_array(['ad', 'adinfo', 'relation', 'banner', 'bannermultiparty', 'distribution_ad'],'
			SELECT	ad.ADID, STARTSTAMP, STOPSTAMP, ad.IMPRESSIONS, IMPRESSIONSDONE, ACTIVE, ad.POSITION, ad.PARTYID, ad.REMOVED, TAKEOVER,
					banner.BANNERID, banner.NAME AS BANNER_NAME,
					adinfo.RELATIONID, relation.NAME AS RELATION_NAME,
					GROUP_CONCAT(party.NAME) AS PARTY_NAMES,
					NOT KEEPTOP AND (SELECT 1 FROM distribution_ad da WHERE da.ADID = ad.ADID LIMIT 1) IS NULL AS MISSING_DISTRIBUTION
			FROM ad
			JOIN adinfo ON adinfo.ADID = ad.ADID
			LEFT JOIN banner ON banner.BANNERID = ad.BANNERID
			LEFT JOIN bannermultiparty AS bmp ON bmp.BANNERID = ad.BANNERID
			LEFT JOIN party ON party.PARTYID = bmp.PARTYID
			LEFT JOIN relation ON relation.RELATIONID = adinfo.RELATIONID
			WHERE STOPSTAMP <= '.TODAYSTAMP.'
			GROUP BY ad.ADID
			ORDER BY STOPSTAMP DESC, RELATION_NAME ASC'))
		) {
			if ($adlist !== false) {
				?><div class="block"><?= __('ad:info:no_active_ads_LINE') ?></div><?
			}
			return;
		}
		layout_open_box('adx');
		layout_open_table('default fw vtop');
		_adlist_display_rowlist($adlist);
		layout_close_table();
		layout_close_box();
		return;
	}
	if (false === ($pending = db_rowuse_array(['ad', 'adinfo', 'relation', 'banner', 'bannermultiparty', 'party', 'distribution_ad'],'
		SELECT	ad.ADID, STARTSTAMP, STOPSTAMP, IMPRESSIONS, IMPRESSIONSDONE, ACTIVE, ad.POSITION, ad.PARTYID, ad.REMOVED, TAKEOVER,
				banner.BANNERID, banner.NAME AS BANNER_NAME,
				adinfo.RELATIONID, relation.NAME AS RELATION_NAME,
				GROUP_CONCAT(party.NAME) AS PARTY_NAMES,
				NOT KEEPTOP AND (SELECT 1 FROM distribution_ad da WHERE da.ADID = ad.ADID LIMIT 1) IS NULL AS MISSING_DISTRIBUTION
		FROM ad
		JOIN adinfo ON adinfo.ADID = ad.ADID
		LEFT JOIN banner ON banner.BANNERID = ad.BANNERID
		LEFT JOIN bannermultiparty AS bmp ON bmp.BANNERID = ad.BANNERID
		LEFT JOIN party ON party.PARTYID = bmp.PARTYID
		LEFT JOIN relation ON relation.RELATIONID = adinfo.RELATIONID
		WHERE STARTSTAMP > '.CURRENTSTAMP.'
		  AND ad.REMOVED = 0
		  AND ( ACTIVE = 0
		  	OR (	 banner.BANNERID IS NULL
				 AND ad.PARTYID=0
		  	   )
			  )
		GROUP BY ad.ADID
		ORDER BY RELATION_NAME, STARTSTAMP'))
	) {
		return;
	}
	if ($pending) {
		layout_open_box('adx');
		expandable_box_header(__C('status:incomplete').' '.MIDDLE_DOT_ENTITY.' '.count($pending), 'incomplete');
		layout_open_table('hidden default fw vtop', id: 'incomplete');
		_adlist_display_rowlist($pending);
		layout_close_table();
		layout_close_box();
	}
	if (false === ($future = db_rowuse_array(['ad', 'adinfo', 'relation', 'banner', 'bannermultiparty', 'party', 'distribution_ad'],'
		SELECT	ad.ADID, STARTSTAMP, STOPSTAMP, IMPRESSIONS, IMPRESSIONSDONE, ACTIVE, ad.POSITION, ad.PARTYID, ad.REMOVED, TAKEOVER,
				banner.BANNERID, banner.NAME AS BANNER_NAME,
				adinfo.RELATIONID, relation.NAME AS RELATION_NAME,
				GROUP_CONCAT(party.NAME) AS PARTY_NAMES,
				NOT KEEPTOP AND (SELECT 1 FROM distribution_ad da WHERE da.ADID = ad.ADID LIMIT 1) IS NULL AS MISSING_DISTRIBUTION
		FROM ad
		JOIN adinfo ON adinfo.ADID = ad.ADID
		LEFT JOIN banner ON banner.BANNERID = ad.BANNERID
		LEFT JOIN bannermultiparty AS bmp ON bmp.BANNERID = ad.BANNERID
		LEFT JOIN party ON party.PARTYID = bmp.PARTYID
		LEFT JOIN relation ON relation.RELATIONID = adinfo.RELATIONID
		WHERE STARTSTAMP >= '.CURRENTSTAMP.'
		  AND ACTIVE = 1
		GROUP BY ad.ADID
		ORDER BY RELATION_NAME, STARTSTAMP'))
	) {
		return;
	}
	if ($future) {
		layout_open_box('adx');
		expandable_box_header(__C('status:scheduled').' '.MIDDLE_DOT_ENTITY.' '.count($future), 'future');
		layout_open_table('hidden default fw vtop', id: 'future');
		_adlist_display_rowlist($future);
		layout_close_table();
		layout_close_box();
	}
	if (!($running = db_rowuse_array(['ad','adinfo','relation','banner', 'bannermultiparty', 'party', 'distribution_ad'],'
		SELECT	ad.ADID, STARTSTAMP, STOPSTAMP, ad.IMPRESSIONS, IMPRESSIONSDONE, ACTIVE, ad.POSITION, ad.PARTYID, ad.REMOVED, TAKEOVER,
				banner.BANNERID, banner.NAME AS BANNER_NAME,
				adinfo.RELATIONID, relation.NAME AS RELATION_NAME,
				d.IMPRESSIONS AS D_IMPRESSIONS, THISHOUR,
				GROUP_CONCAT(party.NAME) AS PARTY_NAMES,
				NOT KEEPTOP AND (SELECT 1 FROM distribution_ad da WHERE da.ADID = ad.ADID LIMIT 1) IS NULL AS MISSING_DISTRIBUTION,
				'.profile_filters_for_query('ad', true).'
		FROM ad
		JOIN adinfo ON adinfo.ADID = ad.ADID
		LEFT JOIN banner ON banner.BANNERID = ad.BANNERID
		LEFT JOIN bannermultiparty AS bmp ON bmp.BANNERID = ad.BANNERID
		LEFT JOIN party ON party.PARTYID = bmp.PARTYID
		LEFT JOIN relation ON relation.RELATIONID = adinfo.RELATIONID
		LEFT JOIN distribution_ad AS d
			 ON d.ADID = ad.ADID
			AND DAYNUM = TO_DAYS(NOW())
			AND HOUR = HOUR(NOW())
		WHERE STARTSTAMP <= '.CURRENTSTAMP.'
		  AND STOPSTAMP > '.(CURRENTSTAMP - ADLATE_CONTINUE).'
		  AND (STOPSTAMP > '.CURRENTSTAMP.' OR IMPRESSIONSDONE < ad.IMPRESSIONS AND KEEPGOING = 1)
		  AND (ACTIVE = 1 OR INACTIVE_INVISIBLE = 0)
		GROUP BY ad.ADID
		ORDER BY RELATION_NAME, STARTSTAMP'))
	) {
		if ($running !== false) {
			?><div class="block"><?= __('ad:info:no_running_ads_LINE') ?></div><?
		}
		return;
	}
	ob_start();
	layout_open_table('default fw vtop');
	$running && _adlist_display_rowlist($running, total_missing: $total_missing);
	layout_close_table();
	$data = ob_get_clean();

	$todayimps = memcached_single(['ad', 'distribution_ad'], '
		SELECT CAST(SUM(THISHOUR) AS UNSIGNED INT)
		FROM distribution_ad
		JOIN ad USING (ADID)
		WHERE REMOVED = 0
		  AND DAYNUM = '.CURRENTDAYNUM,
		ONE_HOUR
	);

	layout_open_box('adx');
	layout_box_header(
		__C('status:active'),
		element_name('total_shortage').': <b>'.(isset($total_missing) ? separate_thousands($total_missing) : '?').'</b>, '.
		element_name('impression_today', $todayimps).': <b>'.separate_thousands($todayimps).'</b>'
	);
	echo $data;
	layout_close_box();
}

function ad_display_single(): void {
	if (!($adid = require_idnumber($_REQUEST,'sID'))) {
		return;
	}

	require_once '_profilefilter.inc';

	layout_show_section_header();

	global $__hour;
	if (!($ad = db_single_assoc(['ad', 'adinfo', 'banner', 'distribution_ad', 'relation', 'invoice'], '
		SELECT	ad.*,
				adinfo.*,
				ad.BANNERID, banner.TYPE, SIZETYPE, WIDTH, HEIGHT, CLICKTAG,
				adinfo.RELATIONID, banner.NAME AS BANNER_NAME, relation.NAME, banner.BG, ADBOX,
				invoice.STATUS,
				'.profile_filters_for_query('ad', true).'
		FROM ad
		JOIN adinfo USING (ADID)
		LEFT JOIN banner ON banner.BANNERID = ad.BANNERID
		LEFT JOIN relation ON relation.RELATIONID = adinfo.RELATIONID
		LEFT JOIN invoice ON invoice.INVOICENR = adinfo.INVOICENR
		LEFT JOIN distribution_ad AS d
			 ON d.ADID = ad.ADID
			AND HOUR = '.$__hour.'
			AND DAYNUM = '.CURRENTDAYNUM.'
		WHERE ad.ADID = '.$adid))
	) {
		$ad !== false && not_found('ad', $adid);
		return;
	}
	/** @var array $ad */
	if (!($ad_admin = have_admin('ad'))
	&&	!have_relation($ad['RELATIONID'])
	&&	!require_admin('ad')
	) {
		no_permission('ad');
		return;
	}

	$freecaps = false;

	if ($ad['ADPRICEID']
	&&	(	$ad['ADPRICEID'] < 5000
		||	$ad['SIZETYPE']
		)
	) {
		if (!($adprice = get_adprice($ad))) {
			if ($adprice !== false) {
				register_error('price:error:could_not_be_determined_LINE');
			}
			unset($adprice);
		} else {
			[$base_price, $freecaps, $cost_per_day] = $adprice;
			# starting 2016-01-27, all caps free
			if ($ad['CSTAMP'] > 1453849200) {
				$freecaps = true;
			}
		}
	}

	if (!$freecaps
	&&	$ad['FQPRICEID']
	&&	(	$ad['FREQCAP']
		||	$ad['FREQCAP_DAY']
		)
	&&	false === ($freqcap = db_single_assoc('adpricefreqcap',"
		SELECT *
		FROM adpricefreqcap
		WHERE FQPRICEID = {$ad['FQPRICEID']}
		  AND FREQCAP IN (0, {$ad['FREQCAP']})"))
	) {
		register_error('price:error:freqcap_could_not_be_determined_LINE');
		unset($freqcap, $base_price);
	}

	ad_menu($ad_admin);
	if ($ad_admin) {
		ad_single_menu($ad);
	}

	require_once '_ticketlist.inc';
	show_connected_tickets();

	$top =	!$ad['WIDTH']
		||	 $ad['WIDTH'] > $ad['HEIGHT']
		||	 $ad['SIZETYPE'] === BANNER_SIZE_OVERLAY;

	$partyad = $ad['POSITION'] === ADPOS_PARTYLIST;

	?><article itemscope itemtype="https://scherma.org/Article"><?
	if ($ad['REMOVED']) {
		?><div class="light"><?
	}
	if ($top) {
		display_banner_innerbox($ad);
	} else {
		?><table class="vtop fw dens nomrgn"><tr><td><?
	}

	layout_open_box('adx');
	layout_box_header(Eelement_name('information').(!$ad['ACTIVE'] ? ' <span class="warning">('.__('status:inactive').')</span>' : null));

	if (have_admin('pixel')) {
		require_once '_pixel.inc';
		show_pixel_status();
	}

	$list = new deflist('deflist vtop');
	if ($ad['NAME']) {
		$list->add_row(Eelement_name('relation'), get_element_link('relation',$ad['RELATIONID'],$ad['NAME']));
	}
	if ($ad['BANNER_NAME']) {
		$list->add_row(Eelement_name('banner'), '<a href="/banner/'.$ad['BANNERID'].'">'.escape_utf8($ad['BANNER_NAME']).'</a>');
	}
	if ($ad['PARTYID']) {
		$party = memcached_party_and_stamp($ad['PARTYID']);
		change_timezone('UTC');
		ob_start();
		echo get_element_link('party',$ad['PARTYID']);
		if ($party) {
			?><small><?
			if ($party['EDITION']) {
				?> #<? echo $party['EDITION'];
			}
			if ($party['SUBTITLE']) {
				?> <?= MIDDLE_DOT_ENTITY ?> <? echo escape_utf8($party['SUBTITLE']);
			}
			?><br /><? _datedaytime_display($party['STAMP_TZI']);
			?><small><?
		}
		$list->add_row(Eelement_name('party'), ob_get_clean());
		change_timezone();
	}
	if ($invoicenr = $ad['INVOICENR']) {
		$list->add_row( Eelement_name('invoice_number'),
			$ad['STATUS'] !== null
		?	'<a href="/invoice/'.$invoicenr.'">'.$invoicenr.'</a>'
		:	$invoicenr
		);
	}

	require_once '_domain.inc';
	show_domain_in_list($list, $ad);

	/** @var array $ad */
	$list->add_row(Eelement_name('position'), ad_position_name($ad['POSITION']));

	if ($ad['SUBPOSITION']) {
		$list->add_row(Eelement_name('subposition'), ad_subposition_name($ad['SUBPOSITION']));
	}

	// IMPRESSIONS || KEEPTOP
	if ($ad['KEEPTOP']) {
		$list->add_row(__C('action:show'), __('answer:always').($ad['TAKEOVER'] ? ', <span class="takeover">take-over</span>' : ''));
	} elseif ($ad['IMPRESSIONS']) {
		$list->add_row(Eelement_plural_name('impression'), separate_thousands($ad['IMPRESSIONS']));
	}
	if ($ad_admin && $ad['PCTEXTRA']) {
		$list->add_row(Eelement_name('extra'), $ad['PCTEXTRA'].'%');
	}
	if ($ad['URL']
	||	banner_needs_url($ad)
	) {
		ob_start();
		if ($ad['URL']) {
			require_once '_affiliates.inc';
			if ($ad['DISCOUNT'] === 100
			&&	($aff_url = make_affiliate_url($ad['URL'],'ad',$adid)) !== $ad['URL']
			) {
				?><a target="_blank" href="<?= $aff_url ?>" class="forcewrap"><?= addslashes($aff_url) ?></a><?
				$list->add_row(Eelement_name('affiliate_click'),ob_get_clean());
				ob_start();
			}
			$url = get_ad_url($ad, $ad['URL']);

			?><a target="_blank" href="<?= $url ?>" class="forcewrap"><?
			echo addslashes($ad['URL']);
			?></a><?
		} else {
			$list->set_row_class('warning');
			echo __('status:missing');
		}
		$list->add_row(Eelement_name('click'), ob_get_clean());
	} elseif ($ad['TYPE'] === 'multiparty') {
		$partyids = db_simpler_array('bannermultiparty','
			SELECT PARTYID
			FROM bannermultiparty
			WHERE BANNERID='.$ad['BANNERID']
		);
		ob_start();
		change_timezone('UTC');
		foreach ($partyids as $partyid) {
			$party = memcached_party_and_stamp($partyid);
			echo get_element_link('party',$partyid),', ',_date_get($party['STAMP_TZI']),', ',get_element_link('location',$party['LOCATIONID']) ?><br /><?
		}
		change_timezone();
		$list->add_row(Eelement_name('click'), ob_get_clean());
	}

	if ($ad['COUNTIMG']) {
		$list->set_value_class('forcewrap');
		$list->add_row(Eelement_name('counter_image'), escape_specials($ad['COUNTIMG']));
	}

	if ($ad['COUNTHTML']) {
		$list->set_value_class('forcewrap');
		$list->add_row(Eelement_name('tracker_html'), escape_specials($ad['COUNTHTML']));
	}
	require_once '_duration.inc';
	show_duration_rows($list, $ad);

	if ($ad['FREQCAP']) {
		$list->add_row(Eelement_name('frequency_cap').' ('.element_name('total').')', $ad['FREQCAP']);
	}
	if ($ad['FREQCAP_DAY']) {
		$list->add_row(Eelement_name('frequency_cap').' ('.element_name('day').')', $ad['FREQCAP_DAY']);
	}
	if ($ad['DELAY']) {
		$list->add_row(Eelement_name('minimum_interim'), $ad['DELAY'].' '.element_name('second', $ad['DELAY']));
	}
	if ($ad['DELAY_POSITION']) {
		$list->add_row(Eelement_name('minimum_interim_position'), $ad['DELAY'].' '.element_name('second', $ad['DELAY_POSITION']));
	}
	if ($ad_admin
	&&	$ad['CSTAMP'] < 1329225726
	) {
		$list->set_row_class('light');
		$list->add_row(Eelement_plural_name('free_impression'),$ad['FREE'] ? ($ad['FREE']-1).MULTIPLICATION_SIGN_ENTITY : __('answer:no'));
	}
	if (!$partyad) {
		# NOTE: Legacy:
		$list->add_row( __C('field:isolated'), __($ad['FLAGS'] & ADFLG_ISOLATED ? 'answer:yes' : 'answer:no'));

		// INTRUSIVE
		$list->add_row(Eelement_name('layer_ad'), __($ad['FLAGS'] & ADFLG_INTRUSIVE ? 'answer:yes' : 'answer:no'));

		if ($ad['CSTAMP'] < AD_END_OF_LARGE_AND_SMALL) {
			if ($ad['FLAGS'] & ADFLG_SMALL_SCREENS_ONLY) {
				$visi = element_plural_name('small_screen');
			} elseif ($ad['FLAGS'] & ADFLG_SMALL_SCREENS) {
				$visi = element_plural_name('large_screen').' + '.element_plural_name('small_screen');
			} else {
				$visi = element_plural_name('large_screen');
			}
			$list->add_row(Eelement_name('visibility'),$visi);
		}
	}
	if ($ad['ONLYNONCUSTOMERS']) {
		$list->add_row(Eelement_plural_name('customer'), __('ad:info:only_non_customers'));
	}
	show_profilefilter_rows($list, $ad);

	require_once '_price.inc';
	if ($ad['EXPLICIT']) {
		if ($ad['COMMISSION']) {
			$list->add_row(Eelement_name('commission'), $ad['COMMISSION'].'%');
		}
		if (!$ad['KEEPTOP']
		&&	($base_price =
				$ad['PRICE'] && ($ad['IMPRESSIONS'] || $ad['IMPRESSIONSDONE'])
			?	$ad['PRICE'] * 1000 * 100 / ($ad['IMPRESSIONS'] ?: $ad['IMPRESSIONSDONE'])
			:	0)
		) {
			$list->add_row(Eelement_name('CPM'), get_price($base_price));
		}
		$real_price = $ad['PRICE'] * 100;
	} elseif ($partyad) {
		if (!empty($cost_per_day)) {
			$list->add_row('CPDay', get_price($cost_per_day));

			$a = _getdate($ad['STARTSTAMP']);
			$b = _getdate($ad['STOPSTAMP']);
			$duration = $ad['STOPSTAMP'] - $ad['STARTSTAMP'];
			if ($a[7] !== $b[7]) {
				register_notice('ad:notice:check_price_due_to_dst_change_LINE');
				if ($a[7]) {
					# summer -> winter, clock one hour back, sub 1 hour to duration
					$duration -= ONE_HOUR;
				} else {
					# winter -> summer, clock on hour forward, add 1 hour
					$duration += ONE_HOUR;
				}
			}
			$days = ceil($duration / ONE_DAY);
			$real_price = $cost_per_day * $days;
		}
	} elseif (isset($base_price)) {
		$list->add_row(Eelement_name('CPM'), get_price($base_price));

		if (!empty($freqcap)
		&&	($ad['FREQCAP'] || $ad['FREQCAP_DAY'])
		) {
			if ($freqcap['EXTRAPRICE']) {
				# old freqcap prices
				$base_price += $freqcap['EXTRAPRICE'];
				$yes_extra = true;
			} else {
				$max_multiplier = null;
				foreach ([null,'_DAY'] as $type) {
					if (!$ad['FREQCAP'.$type]
					||	 $ad['FREQCAP'.$type] >= $freqcap['NO_MULT'.$type]
					) {
						continue;
					}
					$per_cap_point = ($freqcap['MAX_MULT'.$type] - 1) / ($freqcap['NO_MULT'.$type] - 1);
					$parts = $ad['FREQCAP'.$type] - 1;
					$multiplier = $freqcap['MAX_MULT'.$type] - ($parts * $per_cap_point);
					if ($multiplier > $max_multiplier) {
						$max_multiplier = $multiplier;
					}
				}
				if ($max_multiplier !== null) {
					$base_price *= $max_multiplier;
					$yes_extra = true;
				}
			}
			if (isset($yes_extra)) {
				$list->add_row(Eelement_name('CPM').' ('.__('attrib:with_cap').')', get_price($base_price));
			}
		}
		$real_price = isset($base_price) ? $base_price * $ad['IMPRESSIONS']/1000 : 0;
	}
	if (!empty($real_price)) {
		$list->add_row(Eelement_name('price'), get_price($real_price));
	}
	require_once '_voucher.inc';
	show_voucher_row($list,$ad);

	if ($ad['DISCOUNT']) {
		# DISCOUNT
		$list->add_row(Eelement_name('discount'),$ad['DISCOUNT'].'%');

		# PRICE AFTER DISCOUNT
		$real_price = (100 - $ad['DISCOUNT']) * $real_price / 100;
		$list->add_row(Eelement_name('discount_price'),get_price($real_price));
	}
	if ($ad['EXPLICIT']
	&&	$ad['COMMISSION']
	&&	$real_price
	) {
		# INCOME
		$real_price = (100 - $ad['COMMISSION']) * $real_price / 100;
		$list->add_row(Eelement_name('income'),get_price($real_price));
	}
	if ($ad['FLAGS'] & ADFLG_NEVER_TO_ADMINS) {
		$list->add_row(Eelement_plural_name('employee'),__('date:never'));
	}
	// CMTS
	if (!$ad['CMTS']) {
		$list->add_row(Eelement_plural_name('comment'), __('answer:no'));
	} else {
		$cmtcnt = memcached_single('ad_comment','SELECT COUNT(*) FROM ad_comment WHERE ACCEPTED=1 AND ID='.$ad['ADID']);
		if ($cmtcnt) {
			ob_start();
			?><a href="<?= link_to_comments('ad',$ad['ADID']) ?>"><?= Eelement_plural_name('comment') ?></a><?
			$field = ob_get_clean();
			ob_start();
			?><a href="<?= link_to_comments('ad',$ad['ADID']) ?>"><?= $cmtcnt ?></a><?
			$list->add_row($field,ob_get_clean());
		} else {
			$list->add_row(Eelement_plural_name('comment'), __('status:allowed'));
		}
	}
	if ($ad['REGONLY']
	||	$ad['MINAGE']
	||	$ad['GENDERONLY']
	) {
		if ($ad['REGONLY']) {
			$restr[] = __C('attrib:members_only');
		}
		if ($ad['GENDERONLY']) {
			$restr[] = __C('attrib:gender_only',['GENDER'=>$ad['GENDERONLY']]);
		}
		if ($ad['MINAGE']) {
			$restr[] = __C('attrib:only_of_age_plus',['MINAGE'=>$ad['MINAGE']]);
		}
		$list->add_row(Eelement_name('restriction'), implode('<br />',$restr));
	}
	if ($ad['EROTICONLY']) {
		$list->add_row(Eelement_name('restriction'), __('attrib:erotic_only'));
	}
	// CITIES
	if ($ad['CITYLIMIT']) {
		ob_start();
		if ($cities = db_rowuse_array(['city', 'adforcityspec'], '
			SELECT adforcityspec.CITYID,RADIUS,NAME
			FROM adforcityspec
			JOIN city USING (CITYID)
			WHERE ADID = '.$ad['ADID'])
		) {
			foreach ($cities as $city) {
				echo get_element_link('city',$city['CITYID'],$city['NAME']);
				if ($city['RADIUS']) {
					?> (binnen <?= $city['RADIUS']; ?> <?= __('abbr:kilometer') ?>)<?
				}
				?><br /><?
			}
		}
		if ($ad['CITYEXACT']) {
			?> (<?= __('search:exact') ?>)<?
		}
		$list->add_row(Eelement_name('city_group'), ob_get_clean());
	}
	// CITIES
	if ($ad['FLAGS'] & ADFLG_SECTION_LIMIT) {
		if ($sections = db_same_hash('adforsection','
			SELECT SECTIONID, REGEX
			FROM adforsection
			WHERE ADID = '.$adid
		)) {
			ob_start();
			foreach ($sections as $sectionid => $regexes) {
				foreach ($regexes as $regex) {
					if (!($name = get_sectionname($sectionid))) {
						register_warning('ad:warning:invalid_sectionid_LINE', ['SECTIONID' => $sectionid]);
						continue;
					}
					?><dt><?= element_name('section') ?>: <b><?= __('section:'.$name) ?></b></dt><?
					if ($regex) {
						?><dd class="mono"><?= escape_specials($regex) ?></dd><?
						if (preg_match('"^(?<element>party|event):(?<id>\d+)$"i', $regex, $match)) {
							$element = match($match['element']) {
								'event'	=> 'party',
								default	=> $match['element'],
							};
							?><dd><?= get_element_link($element, (int)$match['id']) ?></dd><?
						}
					} elseif ($name) {
						echo element_name('section') ?>: <? echo __('section:'.$name);
					}
				}
			}
			if ($data = ob_get_clean()) {
				$list->add_row(Eelement_plural_name('section'), '<dl class="no-bottom">'.$data.'</dl>');
			}
		}
	}

	$list->display();

	if ($ad_admin
	&&	!empty($ad['ADMIN_TEXT'])
	) {
		?><div class="block"><b><?= Eelement_name('comment') ?></b><br /><?= make_all_html($ad['ADMIN_TEXT'], UBB_UTF8); ?></div><?
	}
	layout_continue_box();
	layout_open_box_header();
	echo Eelement_plural_name('statistic');
	if (!$ad['KEEPTOP']) {
		layout_continue_box_header();
		if (have_distribution_days()) {
			show_distribution_link();
		} else {
			?><span class="error"><?= element_name('no_distribution') ?></span><?
		}
	}
	layout_close_box_header();
	layout_open_table(TABLE_FULL_WIDTH);
	show_distribution_rows('ad', $ad);

	require_once '_clickinfo.inc';
	show_clickinfo('ad', $ad);

	if ($hitsdone = $ad['IMPRESSIONSDONE']) {
		if ($ad_admin
		&&	!empty($base_price)
		&&	$ad['FREE']
		) {
			layout_restart_reverse_row(ROW_LIGHT);
			echo get_price($base_price * $hitsdone / $ad['ACTUALHITS']);
			layout_value_field();
			echo element_name('effective_CPM');
		}
	}
	if ($ad['TYPE'] === 'floormovie') {
		show_clickinfo('ad', $ad, 'expansion', 'expand');
	}
	layout_stop_row();
	layout_close_table();

	if (!$ad['KEEPTOP']) {
		show_distribution_days();
	}
	if ($ad['ACTIVE']
	&&	$ad['STOPSTAMP'] < CURRENTSTAMP
	) {
		require_once '_paystatus.inc';
		show_paystatus($ad);
	}
	if ($ad_admin) {
		ob_start();
		layout_display_alteration_note($ad);
		if ($alteration_note = ob_get_clean()) {
			layout_box_footer($alteration_note);
		}
	}
	layout_close_box();
	if (!$top && $ad['BANNERID']) {
		?></td><td><?
		display_banner_innerbox($ad);
		?></td></tr></table><?
	}
	?></article><?
	if ($ad['REMOVED']) {
		?></div><?
	}
}

function ad_display_simple() {
	layout_show_section_header();

	layout_open_menu();
	layout_menuitem(Eelement_name('overview'),'/ad/running');
	layout_close_menu();

	if (!($adid = require_idnumber($_REQUEST,'sID'))) {
		return;
	}
	$ad = memcached_single_assoc(['ad', 'adinfo', 'banner', 'relation'],'
		SELECT	ADID, ad.BANNERID, ACTIVE, STARTSTAMP, STOPSTAMP, ADBOX, ad.POSITION,SIZETYPE, ad.REMOVED, adinfo.URL,
				banner.TYPE, WIDTH, HEIGHT, CLICKTAG,
				adinfo.RELATIONID, relation.NAME AS RELATION_NAME
		FROM ad
		JOIN adinfo USING (ADID)
		JOIN banner USING (BANNERID)
		LEFT JOIN relation ON relation.RELATIONID = adinfo.RELATIONID
		WHERE ADID = '.$adid
	);
	if (!$ad) {
		return;
	}
	if (!have_admin('ad')
	&&	!have_relation($ad['RELATIONID'])
	) {
		if (!$ad['ACTIVE']) {
			register_error('ad:error:not_active_LINE', DO_UBB, ['ADID' => $adid]);
			return;
		}
		if ($ad['STARTSTAMP'] > CURRENTSTAMP) {
			register_error('ad:error:starts_in_the_future_LINE', DO_UBB, ['ADID' => $adid]);
			return;
		}
	}
	layout_open_box('white');
	?><p class="center"><?
	$ad['FREE'] = true;
	display_ad($ad);
	?></p><?
	layout_close_box('white');

#	require_once '_commentlist.inc';
#	$cmts = new _commentlist;
#	$cmts->item($ad);
#	$cmts->display();
}

function ad_display_form() {
	if (!require_admin('ad')) {
		no_permission();
		return;
	}

	require_once '_bannerlist.inc';

	layout_show_section_header(null,element_name('form'));

	ad_menu();

	if ($adid = $_REQUEST['sID']) {
		if (!($ad = db_single_assoc(['ad', 'adinfo', 'banner', 'relation', 'distribution_ad'],'
			SELECT	ad.*, adinfo.*,
					banner.TYPE, relation.DEFAULT_COMMISSION, ADBOX, CLICKTAG,
					WIDTH, HEIGHT, SIZETYPE, BG,
					NOT KEEPTOP AND (SELECT 1 FROM distribution_ad da WHERE da.ADID = ad.ADID LIMIT 1) IS NULL AS MISSING_DISTRIBUTION
			FROM ad
			JOIN adinfo USING (ADID)
			LEFT JOIN banner ON ad.BANNERID = banner.BANNERID
			LEFT JOIN relation ON relation.RELATIONID = adinfo.RELATIONID
			WHERE ad.ADID = '.$adid,
			DB_USE_MASTER))
		) {
			$ad !== false && not_found('ad', $adid);
			return;
		}
	} else {
		$ad = [];
	}
	if (!($ad_spec	   = explain_table('ad'))
	||	!($adinfo_spec = explain_table('adinfo'))
	) {
		return;
	}
	$partyad = $ad && $ad['POSITION'] === ADPOS_PARTYLIST;

	if ($ad) {
		if (false === ($adcities = db_rowuse_array(['city', 'adforcityspec'],'
			SELECT CITYID, RADIUS, NAME
			FROM adforcityspec
			JOIN city USING (CITYID)
			WHERE ADID = '.$ad['ADID']))
		|| false === ($adsections = db_multirow_hash('adforsection','
			SELECT SECTIONID, REGEX
			FROM adforsection
			WHERE ADID = '.$adid))
		) {
			return;
		}
		$sections = [];
		foreach ($adsections as $sectionid => $regexs) {
			foreach ($regexs as $regex) {
				$sections[] = escape_specials($regex ?: '/'.get_sectionname($sectionid));
			}
		}
		asort($sections);
	} else {
		$adcities = null;
	}

	require_once '_profilefilter.inc';

	ob_start();
	$rc = profilefilter_show_form($ad);
	$profilefilter_form = ob_get_clean();
	if (!$rc) {
		return false;
	}

	include_js('js/form/ad');

	?><form<?
	?> accept-charset="utf-8"<?
	?> onsubmit="return submitAdForm(this, <?= ADPOS_OVERLAY ?>, <?= ADPOS_PARTYLIST ?>)"<?
	?> method="post"<?
	?> action="/ad<? if ($adid) { ?>/<? echo $adid; } ?>/commit"><?

	ticket_passthrough();

	$selected_bannerid = 0;

	if (!empty($ad['BANNERID'])) {
		$selected_bannerid = $ad['BANNERID'];
		$banner = $ad;
	} elseif ($bannerid = have_idnumber($_REQUEST,'BANNERID')) {
		if (!($banner = db_single_assoc('banner','
			SELECT BANNERID, TYPE, WIDTH, HEIGHT, SIZETYPE, DEFAULT_COMMISSION, BG, ADBOX, RELATIONID, CLICKTAG
			FROM banner
			LEFT JOIN relation USING (RELATIONID)
			WHERE BANNERID = '.$bannerid))
		) {
			$banner === false ? page_problem(503) : not_found('banner', $bannerid);
			return;
		}
		$selected_bannerid = $bannerid;
	} else {
		$banner = null;
	}

	$top =	$banner
	&&	(	!$banner['WIDTH']
		||	$banner['WIDTH'] > $banner['HEIGHT']
		);

	?><div id="bannerboxtop"><?
	if ($top) {
		display_banner_innerbox($banner);
	}
	?></div><?
	?><table class="dens fw vtop nomrgn"><tr><td><?

	layout_open_box('adx');
	layout_box_header(Eelement_name('ad'));

	layout_open_table('fw');

	# FIXME: this does not check if ad fits
	# NOTE: unsure what I meant, let's try making active the default
	$active_checked = !$ad || ($ad['ACTIVE'] && !$ad['MISSING_DISTRIBUTION']);
	layout_start_row();
		?><label for="active"><?= __C('status:active') ?></label><?
		layout_field_value();
		show_input_with_label(
			checked:  $active_checked,
			hilited:  'bold-hilited-green',
			input:	 ['id'		=> 'active',
					  'type'	=> 'checkbox',
					  'name'	=> 'ACTIVE',
					  'onclick' => 'this.form.URL.required = this.checked;',
					  'disabled'=> $ad && $ad['MISSING_DISTRIBUTION']]);

	$inactive_invisible_checked = $ad['INACTIVE_INVISIBLE'] ?? false;
	layout_start_row();
		?><label for="inactive-invisible"><?= __C('status:inactive'),' ',__('status:invisible') ?></label><?
		layout_field_value();
		show_input_with_label(
			checked:  $inactive_invisible_checked,
			hilited:  'bold-hilited-orange',
			input:	 ['id'	 => 'inactive-invisible',
					  'type' => 'checkbox',
					  'name' => 'INACTIVE_INVISIBLE']);

	layout_restart_row();
	if (!empty($ad['INVOICENR'])) {
		echo Eelement_name('invoice_number');
			layout_field_value();
			show_input([
				'type'		=> 'number',
				'class'		=> 'id right',
				'min'		=> 0,
				'name'		=> 'INVOICENR',
				'value'		=> $ad['INVOICENR'] ?? null,
			]);
		layout_restart_row();
	}

/*		echo Eelement_name('domain');
		layout_field_value();
		show_domain_form_part($ad);

	layout_restart_row();*/
		echo Eelement_name('relation');
		layout_field_value();
		require_once '_discounts.inc';
		discount_relation_select($ad['RELATIONID'] ?? $banner['RELATIONID'] ?? 0);

	layout_restart_row();
		echo Eelement_name('position');
		layout_field_value();
		?><select name="POSITION" required onchange="Pf_changeAdPosition(this)"><?
		?><option></option><?
		ad_position_options($ad, $banner);
		?></select><?

	layout_restart_row($partyad ? ROW_HIDDEN : 0, 'bannerrow');
		echo Eelement_name('banner');
		layout_field_value();
		include_js('js/commission');
		?><select name="BANNERID" onchange="Pf_changeBannerBox(this);Pf_changeBanner(this)"<?
		if (!$partyad) {
			?> required<?
		}
		?>><?
		?><option></option><?
		bannerlist_show_options($selected_bannerid);
		?></select><?

	layout_restart_row($partyad ? 0 : ROW_HIDDEN,'partyrow');
		echo Eelement_name('party');
		layout_field_value();
		require_once '_fillelementid.inc';
		show_elementid_input(
			'party',
			$ad['PARTYID'] ?? null, [
				'name'	   => 'PARTYID',
				'required' => $ad && $ad['PARTYID'] ? true : false
		]);
	layout_stop_row();

	require_once '_duration.inc';
	show_last_day_duration_form_part($ad, [
		'time'				=> true,
		'stopstamp'			=> $ad ? null : strtotime('+1 day', TODAYSTAMP),
		'hardstop'			=> !$ad || !$ad['KEEPGOING'],
		'hardstop_hidden'	=> $partyad,
	]);

	?><tbody id="nopartypart"<?
	if ($partyad) {
		?> class="hidden"<?
	}
	?>><?

	layout_start_row();
		?><label for="keeptop"><?= __C('action:show_always') ?></label><?
		layout_field_value();
		show_input_with_label(
			checked:  $ad && $ad['KEEPTOP'],
			hilited: 'bold-hilited-green',
			input:   ['type'	=> 'checkbox',
	 				  'name'	=> 'KEEPTOP',
					  'id'		=> 'keeptop',
					  'onclick'	=> "setdisplay('impressionsrow', !this.checked, true); ".
								   "setdisplay('takeoverrow', this.checked);"]);

	layout_restart_row($ad && $ad['KEEPTOP'] ? 0 : ROW_HIDDEN, id: 'takeoverrow');
		?><label for="takeover" class="takeover">Take-over</label><?
		layout_field_value();
		show_input_with_label(
			checked: $ad && $ad['TAKEOVER'],
			hilited: 'bold-hilited-yellow',
			input:	['type'		=> 'checkbox',
					 'name'		=> 'TAKEOVER',
					 'id'		=> 'takeover']);

	require_once '_budget.inc';
	show_impressions_and_budget_form($ad);

	if ($ad && in_array($ad['TYPE'], ['iframe', 'script'])) {
		layout_restart_row();
			echo Eelement_plural_name('click');
			layout_field_value();
			?><input type="number" class="id"  min="0" data-valid="number" class="three_digits" name="CLICKS" value="<?= $ad['CLICKS'] ?>" /> (opgegeven door adverteerder)<?
	}

	layout_restart_row();
		echo Eelement_name('click');
		layout_field_value();
		show_input([
			'type'		 => 'url',
			'data-valid' => 'working-url',
			'required'	 => $active_checked && (empty($ad['POSITION']) || $ad['POSITION'] !== ADPOS_PARTYLIST),
			'name'		 => 'URL',
			'value'		 => $ad,
			'spec'		 => $adinfo_spec,
			'onkeyup'	 => /** @lang JavaScript */ 'Pf_changeURL(this)',
		]);

	layout_restart_row();
		?><label for="add_utm"><?= __C('action:add') ?> UTM</label><?
		layout_field_value();
		show_input_with_label(
			checked: $ad['URL_ADD_DEFAULT_UTM'] ?? true,
			hilited: 'bold-hilited-green',
			input:  ['type'		=> 'checkbox',
					 'name'		=> 'URL_ADD_DEFAULT_UTM',
					 'id'		=> 'add_utm']);


	/*if (!empty($ad['COUNTIMG'])
	||	!empty($ad['COUNTHTML'])
	) {
		$uniqmsg = '#UNIQUE# in teller afbeelding of html wordt vervangen door uniek getal';

		layout_restart_row();
			echo Eelement_name('counter_image');
			layout_field_value();
			show_input([
				'type'			=> 'url',
				'data-valid'	=> 'working-url',
				'name'			=> 'COUNTIMG',
				'value'			=> $ad,
				'spec'			=> $adinfo_spec,
			]);

		layout_restart_row();
			echo Eelement_name('tracker_html');
			layout_field_value();
			show_textarea([
				'class'		=> 'growToFit',
				'name'		=> 'COUNTHTML',
				'value'		=> $ad,
				'spec'		=> $adinfo_spec,
			]);

		layout_restart_row();
			layout_next_cell();
			?>&uarr; <? echo escape_specials($uniqmsg);

	} else {
		?><input type="hidden" name="COUNTIMG" value="" /><?
		?><input type="hidden" name="COUNTHTML" value="" /><?
	}*/
	# NOTE: Not used anymore:
/*	layout_restart_row();
		?><label for="isolated"><?= __C('field:isolated') ?></label><?
		layout_field_value();
		?><label><input id="isolated" type="checkbox" name="FLAGS[]" value="<?= ADFLG_ISOLATED ?>"<?
		if ($ad && ($ad['FLAGS'] & ADFLG_ISOLATED)) {
			?> checked="checked"<?
		}
		?>> <?
		echo __('ad:info:isolated_info_LINE');
		?></label><?*/
	# NOTE: Not used anymore:
	/*if ($ad && ($ad['FLAGS'] & ADFLG_INTRUSIVE)) {
		layout_restart_row();
			?><label for="intrusive"><?= Eelement_name('layer_ad') ?></label><?
			layout_field_value();
			?><label><input id="intrusive" type="checkbox" name="FLAGS[]" value="<?= ADFLG_INTRUSIVE ?>"<?
			if ($ad && ($ad['FLAGS'] & ADFLG_INTRUSIVE)) {
				?> checked="checked"<?
			}
			?>> <?= __('ad:info:intrusive_info_LINE') ?></label><?
	}*/
	# NOTE: Not used anymore:
/*	layout_restart_row();
		echo Eelement_name('visibility');
		layout_field_value();
		?><select name="FLAGS[]"><?
		?><option<?
		if (!$ad
		||	!($ad['FLAGS'] & ADFLG_SMALL_SCREENS_ONLY)
		||	  $ad['FLAGS'] & ADFLG_SMALL_SCREENS
		) {
			?> selected="selected"<?
		}
		?> value="<?= ADFLG_SMALL_SCREENS ?>"><?= element_plural_name('large_screen') ?> + <?= element_plural_name('small_screen') ?></option><?

		if ($ad
		&&	!($ad['FLAGS'] & ADFLG_SMALL_SCREENS_ONLY)
		&&	!($ad['FLAGS'] & ADFLG_SMALL_SCREENS)
		) {
			?> selected="selected"<?
		}
		?> value="0"><?= element_plural_name('large_screen') ?></option><?
		?><option<?
		if ($ad
		&&	$ad['FLAGS'] & ADFLG_SMALL_SCREENS_ONLY
		) {
			?> selected="selected"<?
		}
		?> value="<?= ADFLG_SMALL_SCREENS | ADFLG_SMALL_SCREENS_ONLY ?>"><?= element_plural_name('small_screen') ?></option><?
		?></select><?*/

	# NOTE: Not used anymore:
	/*if (!empty($ad['NOCLICKS'])) {
		layout_restart_row();
			?><label for="noclicks"><?= Eelement_plural_name('click_not_countable') ?></label><?
			layout_field_value();
			?><input<?
			if ($ad && $ad['NOCLICKS']) {
				?> checked<?
			}
			?> type="checkbox" id="noclicks" name="NOCLICKS" value="1" /><?
	}*/

	# NOTE: Not used anymore:
	/*if ($ad
	&&	($ad['FLAGS'] & ADFLG_NEVER_TO_ADMINS)
	) {
		layout_restart_row();
			echo Eelement_plural_name('employee');
			layout_field_value();
			?><select name="FLAGS[]"><?
			show_options($ad && ($ad['FLAGS'] & ADFLG_NEVER_TO_ADMINS) ? ADFLG_NEVER_TO_ADMINS : 0, [
				0			=> __('answer:yes'),
				ADFLG_NEVER_TO_ADMINS	=> __('date:never')
			]);
			?></select><?
	}*/
	if ($ad
	&&	$ad['CSTAMP'] < 1329225726
	) {
		layout_restart_row(id: 'free_impressions_row');
		?><label for="free"><?= Eelement_plural_name('free_impression') ?></label><?
		layout_field_value();
		show_input([
			'class'		=> 'right three_digits',
			'type'		=> 'number',
			'data-valid'	=> 'float',
			'step'		=> 'any',
			'id'		=> 'free',
			'name'		=> 'FREE',
			'value'		=> isset($ad['FREE']) ? ($ad['FREE'] ? $ad['FREE'] - 1 : 0) : 0,
		]);
		echo MULTIPLICATION_SIGN_ENTITY ?> <?
	}

	layout_restart_row();
		echo Eelement_name('frequency_cap') ?> (<?= element_name('total') ?>)<?
		layout_field_value();
		show_input([
			'class'		=> 'right three_digits',
			'type'		=> 'number',
			'min'		=> 0,
			'max'		=> 255,
			'name'		=> 'FREQCAP',
			'value'		=> !empty($ad['FREQCAP']) ? $ad['FREQCAP'] : null,
		]);
		ob_start();
		?><span class="lpad"><?= __('attrib:max_number', ['MAX' => '255']) ?></span><?
		$max_info = ob_get_flush();

	layout_restart_row();
		echo Eelement_name('frequency_cap'); ?> (<?= element_name('day') ?>)<?
		layout_field_value();
		show_input([
			'class'		=> 'right three_digits',
			'type'		=> 'number',
			'min'		=> 0,
			'max'		=> 255,
			'name'		=> 'FREQCAP_DAY',
			'value'		=> !empty($ad['FREQCAP_DAY']) ? $ad['FREQCAP_DAY'] : null,
		]);
		echo $max_info;

	layout_restart_row();
		echo Eelement_name('minimum_interim');
		layout_field_value();
		show_input([
			'type'		=> 'number',
			'class'		=> 'right three_digits',
			'min'		=> 0,
			'max'		=> TEN_MINUTES,
			'name'		=> 'DELAY',
			'value'		=> !empty($ad['DELAY']) ? $ad['DELAY'] : null
		]);
		ob_start();
		?><span class="lpad"><?= element_plural_name('second') ?></span><?
		$seconds = ob_get_flush();

	layout_restart_row();
		echo Eelement_name('minimum_interim_position');
		layout_field_value();
		show_input([
			'type'		=> 'number',
			'class'		=> 'right three_digits',
			'min'		=> 0,
			'max'		=> TEN_MINUTES,
			'name'		=> 'DELAY_POSITION',
			'value'		=> $ad['DELAY_POSITION'] ?? null,
		]);
		echo $seconds;

	layout_stop_row();
	?></tbody><?

	echo $profilefilter_form;

	/*if (!empty($ad['REGONLY'])
	||	!empty($ad['GENDERONLY'])
	||	!empty($ad['MINAGE'])
	||	!empty($ad['EROTICONLY'])
	||	!empty($ad['CITYLIMIT'])
	) {
		?><tbody id="legacy-filters"><?
		?><tr><td colspan="2"><hr class="slim"></td></tr><?
		layout_start_row();
			layout_field_value();
			echo Eelement_plural_name('filter') ?> (<?= __('attrib:members_only') ?>)<?

		# These are all obsolete:

		layout_restart_row($ad && $ad['REGONLY'] ? ROW_BOLD_HILITED : 0);
			?><label for="regonly"><?= __C('attrib:members_only') ?></label><?
			layout_field_value();
			?><input<?
			if ($ad
			&&	$ad['MINAGE'] === 18
			) {
				?> disabled="disabled"<?
			}
			?> type="checkbox" onclick="cbclickpp(this)" id="regonly" name="REGONLY" value="1"<? if ($ad['REGONLY']) echo ' checked="checked"'; ?>><?

		layout_restart_row();//$ad['GENDERONLY'] ? ROW_BOLD_HILITED : 0);
			echo __C('attrib:gender_only',DO_CONDITIONAL);
			layout_field_value();
			?><select name="GENDERONLY"><?
				?><option></option><?
				?><option<?
				if ($ad['GENDERONLY'] === 'F') {
					?> selected<?
				}
				?> value="F"><?= element_plural_name('woman') ?></option><?
				?><option<?
				if ($ad['GENDERONLY'] === 'M') {
					?> selected<?
				}
				?> value="M"><?= element_plural_name('man') ?></option><?
			?></select><?

		layout_restart_row($ad && $ad['MINAGE'] === 18 ? ROW_BOLD_HILITED : 0);
			?><label for="minage"><?= __C('attrib:only_of_age_plus',['MINAGE'=>18]) ?></label><?
			layout_field_value();
			?><input onclick="cbclickpp(this);o=getobj('regonly');if (o){o.disabled = this.checked;o.checked = this.checked;cbclickpp(o)}"<?
			?> type="checkbox" id="minage" name="MINAGE" value="18"<? if ($ad && $ad['MINAGE'] === 18) { ?> checked<? } ?> /><?

		layout_restart_row(($checked = $ad && $ad['EROTICONLY']) ? ROW_BOLD_HILITED : 0);
			?><label for="eroticonly"><?= __C('attrib:erotic_only') ?></label><?
			layout_field_value();
			show_input([
				'type'		=> 'checkbox',
				'id'		=> 'eroticonly',
				'name'		=> 'EROTICONLY',
				'value'		=> 1,
				'checked'	=> $checked,
				'onclick'	=> 'cbclickpp(this)',
			]);

		layout_restart_row($ad && $ad['CITYLIMIT'] ? ROW_BOLD_HILITED : 0);
			?><label for="citylimit"><?= Eelement_name('city_group') ?></label><?
			layout_field_value();
			?><input onclick="cbclickpp(this);setdisplay(['cities','cityexactrow'],this.checked)" type="checkbox" name="CITYLIMIT" id="citylimit" value="1"<?
			if ($ad['CITYLIMIT']) {
				?> checked="checked"<?
			}
			?>><?

		layout_restart_row(!$ad || !$ad['CITYLIMIT'] ? ROW_HIDDEN : 0,'cities');
			echo Eelement_plural_name('city');
			layout_field_value();
			?><div id="citieslist"><?
			if ($adcities) {
				foreach ($adcities as $city) {
					?><span id="city_<?= $city['CITYID'] ?>"><?
					?><input type="hidden" name="CITY[<?= $city['CITYID'] ?>]" value="<?= $city['RADIUS'] ?>" /><?
					echo get_element_link('city',$city['CITYID'],$city['NAME']);
					if ($city['RADIUS']) {
						?> (binnen <?= $city['RADIUS'] ?> <?= __('abbr:kilometer') ?>)<?
					}
					?> <?= MIDDLE_DOT_ENTITY ?> <?=
					get_remove_char(['onclick' => "Pf.changeNode(this.parentNode, 'remove')"])
					?><br /><?
					?></span><?
				}
			}
			?></div><?
			include_js('js/choosecity');
			?><select onchange="setdisplay('add', this.selectedIndex); changeCountry(this, 0);" id="countryid"><?
			?><option value="0"></option><?
			if ($countries = get_non_empty_countries()) {
				foreach ($countries as $countryid => $name) {
					?><option value="<?= $countryid ?>"><?= escape_specials($name)	?></option><?
				}
			}
			?></select><br /> <?

			require_once 'include.php';

			$_REQUEST['INCLUDE'] = 'cities';
			$_REQUEST['COUNTRYID'] =
			$_REQUEST['SELECTED'] =
			$_REQUEST['FLAGS'] = 0;
			include_something([
				'INCLUDE'	=> 'cities',
				'COUNTRYID'	=> 0,
				'SELECTED'	=> 0,
				'FLAGS'		=> 0
			]);
			?> <input type="number" data-valid="number" min="0" id="radius" class="three_digits" /> <?= __('abbr:kilometer');
			?> <input onclick="Pf_filterAddCity('CITY')" type="button" name="ADD" value="<?= __('action:add'); ?>" id="add" class="hidden" /><?

		layout_restart_row(!$ad || !$ad['CITYLIMIT'] ? ROW_HIDDEN : 0,'cityexactrow');
			layout_next_cell();
			?><label class="<?
			if (!$ad['CITYEXACT']) {
				?>not-<?
			}
			?>hilited"><?
			show_input([
				'type'		=> 'checkbox',
				'class'		=> 'upLite',
				'name'		=> 'CITYEXACT',
				'value'		=> 1,
				'checked'	=> $ad['CITYEXACT'],
			]);
			?> <?= __('search:exact') ?></label><?

		layout_stop_row();
		?></tbody><?
	}*/
	?><tbody id="other-filters"><?
	?><tr><td colspan="2"><hr class="slim"></td></tr><?
	?><tr class="filter-header"><td></td><?
	?><td class="header"><?= Eelement_plural_name('filter') ?> (<?= element_plural_name('other(elements)') ?>)<?
	?><td></tr><?
	?><tr><td colspan="2"><hr class="slim"></td></tr><?

	layout_start_row();
		?><label for="sections"><?= Eelement_plural_name('section') ?></label><?
		layout_field_value();
		show_textarea([
			'name'		=> 'SECTIONS',
			'id'		=> 'sections',
			'class'		=> 'growToFit mono',
			'cols'		=> 60,
			'rows'		=> empty($sections) ? 1 : count($sections) + 1,
			'value'		=> empty($sections) ? null : implode("\n", $sections)."\n"
		]);
	layout_stop_row();

	?></tbody><?

	?><tr><td colspan="2"><hr class="slim small light" /><?

	if (!empty($ad['CMTS'])) {
		layout_restart_row($ad['CMTS'] ? ROW_BOLD_HILITED : 0);
			?><label for="cmts"><?= Eelement_plural_name('comment') ?></label><?
			layout_field_value();
			?><input<?
			?> checked="checked"<?
			?> type="checkbox" onclick="cbclickpp(this)" value="1" id="cmts" name="CMTS"><?
	}
	layout_stop_row();
	?></tbody><?

	if (!empty($ad['VOUCHERID'])) {
		require_once '_voucher.inc';
		show_voucher_form_row($ad);
	}

	show_discount_form_row($ad);

	$show_price = $ad ? $ad['EXPLICIT'] : ($banner ? $banner['DEFAULT_COMMISSION'] : false);

	layout_start_row();
		?><label for="explicit"><?= Eelement_name('explicit_price') ?></label><?
		layout_field_value();
		?><input onclick="Pf_changeExplicitPrice(this);" name="EXPLICIT" value="1" id="explicit" type="checkbox"<?
		if ($show_price) {
			?> checked<?
		}
		?> /><?

	layout_restart_row($show_price ? 0 : ROW_HIDDEN, 'commissionrow');
		echo Eelement_name('commission');
		layout_field_value();
		show_input([
			'class'		=> 'right rpad three_digits',
			'type'		=> 'number',
			'min'		=> 0,
			'max'		=> 100,
			'name'		=> 'COMMISSION',
			'value'		=> $ad['COMMISSION'] ?? null,
		]);
		?> %<?

	layout_restart_row($show_price ? 0 : ROW_HIDDEN, 'pricerow');
		echo Eelement_name('price');
		layout_field_value();
		show_input([
			'required'	=> $show_price,
			'class'		=> 'right rpad three_digits',
			'type'		=> 'number',
			'min'		=> 25,
			'name'		=> 'PRICE',
			'value'		=> $ad['PRICE'] ?? null,
		]);
		?> &euro; (voor aftrek commissie)<?

	layout_restart_row();
		echo  __C('header:admin_comments');
		layout_field_value();
		show_textarea([
			'name'			=> 'ADMIN_TEXT',
			'rows'			=> 5,
			'class'			=> 'growToFit',
			'cols'			=> 60,
			'value_utf8'	=> $ad['ADMIN_TEXT'] ?? '',
		]);

	layout_stop_row();

	layout_close_table();
	layout_close_box();

	?><div class="block"><input type="submit" value="<?= __($ad ? 'action:change' : 'action:add'); ?>" /></div><?

	?></td><td><?

	?><div id="bannerboxright"><?
	if ($banner && !$top) {
		display_banner_innerbox($banner);
	}
	?></div><?

	?></td></tr><?
	?></table><?

	?></form><?
}

function get_adprice(array $ad): array|false {
	return db_single_array('adpricesize',
			$ad['ADPRICEID'] < 5000
		? "	SELECT BASEPRICE, FREECAPS, CPDAY
			FROM adpricesize
			WHERE ID = {$ad['ADPRICEID']}"
		: "	SELECT BASEPRICE, 0, 0
			FROM adpricesize
			WHERE ADPRICEID = {$ad['ADPRICEID']}
			  AND SIZETYPE IN (0, {$ad['SIZETYPE']})
			  AND ADPOS IN (0, {$ad['POSITION']})
			ORDER BY SIZETYPE DESC, ADPOS DESC
			LIMIT 1");
}

function ad_set_active(int $active): bool {
	if (!require_admin('ad')
	||	!($adid = require_idnumber($_REQUEST, 'sID'))
	) {
		return false;
	}
	if ($active && !ad_distribution_alright($adid)) {
		register_warning('distribution:error:missing_LINE');
		return false;
	}
	$set_active = $active ? '1' : '0';
	if (!db_insert('ad_log', "
		INSERT INTO ad_log
		SELECT * FROM ad
		WHERE ACTIVE != $set_active
		  AND (ACTIVE OR KEEPTOP OR IMPRESSIONS)
		  AND ADID = $adid")
	||	!db_update('ad', "
		UPDATE ad SET
			ACTIVE	= $set_active,
			MUSERID	= ".CURRENTUSERID.',
			MSTAMP	= '.CURRENTSTAMP."
		WHERE ADID = $adid
		  AND (ACTIVE OR KEEPTOP OR IMPRESSIONS)
		  AND ACTIVE != $set_active")
	) {
		return false;
	}
	require_once '_ad.inc';
	flush_running_ads();
	if ($position = db_single_int('ad', "SELECT POSITION FROM ad WHERE ADID = $adid")) {
		flush_adlist($position);
	}
	if (!db_affected()) {
		register_warning($active ? 'ad:error:not_activated_LINE' : 'ad:error:not_deactivated_LINE');
		return false;
	}
	register_notice($active ? 'ad:notice:activated_LINE' : 'ad:notice:deactivated_LINE');
	return true;
}

function ad_commit(): bool {
	if (!require_admin('ad')
	||	false === require_number($_POST, 'FREQCAP')
	||	false === require_number($_POST, 'FREQCAP_DAY')
	||	false === require_number($_POST, 'DELAY')
	||	false === require_number($_POST, 'DELAY_POSITION')
	||	!($pos = require_ad_position($_POST, 'POSITION'))
	||	(	($partyad = ($pos === ADPOS_PARTYLIST))
		?	!require_idnumber($_POST, 'PARTYID')
		:	!require_idnumber($_POST, 'BANNERID')
		)
	||	!require_working_url_or_none($_POST, 'URL')
	||	!optional_number($_POST, 'INVOICENR')
	||	false === require_element($_POST, 'WHAT', ['impressions', 'budget'], strict: true)
	# ||	false === require_element($_POST, 'GENDERONLY', ['', 'F', 'M'], strict: true, default: '')
	||	false === require_element($_POST, 'DOMAIN',[ '', 'nl', 'be'], strict: true, default: '')
	||	false === require_number($_POST, 'WHATVAL')
	||	!require_anything_trim($_POST, 'ADMIN_TEXT', utf8: true)
	# ||	!require_anything_trim($_POST, 'COUNTIMG')
	||	!optional_number($_POST, 'COMMISSION')
	||	!optional_number($_POST, 'PRICE')
	||	!optional_number($_POST, 'TICKETID')
	||	!optional_number($_POST, 'RELATIONID')
	||	!optional_number_array($_POST, 'FLAGS')
	||	false === optional_float($_POST, 'FREE')
	||	!require_anything_trim($_POST, 'SECTIONS')
	||	!optional_number($_POST, 'DISCOUNT')
	) {
		return false;
	}
	if (!empty($_POST['CITY'])) {
		if (!require_hash($_POST,  'CITY', HASH_NUMBER, HASH_NUMBER)) {
			return false;
		}
/*		if (!require_array($_POST, 'CITY')) {
			return false;
		}
		foreach ($_POST['CITY'] as $cityid => $radius) {
			if (!is_number($cityid)
			||	!empty($radius)
			&&	!is_number($radius)
			) {
				register_error('
				error('Incorrecte steden!');
				return false;
			}
		}*/
	}
	$flags = 0;
	$oldad = null;
	if ($adid = $_REQUEST['sID']) {
		if (!($oldad = db_single_assoc(['ad', 'adinfo', 'banner'],'
			SELECT ad.*, adinfo.*, banner.SIZETYPE
			FROM ad
			JOIN adinfo USING (ADID)
			LEFT JOIN banner USING (BANNERID)
			WHERE ADID='.$adid,
			DB_USE_MASTER))
		) {
			if ($oldad !== false) {
				not_found('ad', $adid);
			}
			return false;
		}
		# Clear the flags that are supported, and will be passed by form:
		$flags = ($oldad['FLAGS'] & ~ADFLG_SUPPORTED);
	}
	if ($banner = !$partyad) {
		if (!($banner = db_single_assoc('banner','SELECT TYPE, SIZETYPE FROM banner WHERE BANNERID = '.$_POST['BANNERID']))) {
			if ($banner !== false) {
				not_found('banner', $_POST['BANNERID']);
			}
			return false;
		}
		if ($banner['TYPE'] === 'iframe'
		||	$banner['TYPE'] === 'script'
		) {
			if (have_number($_POST, 'CLICKS')) {
				$setlist[] = 'CLICKS='.$_POST['CLICKS'];
			}
		} elseif ($_POST['POSITION'] === ADPOS_SMALL_SCREEN) {
			if (false === require_element($banner, 'TYPE', ['upload', 'link', 'html'], strict: true)) {
				return false;
			}
		}
		if ($pos === ADPOS_OVERLAY
		&&	$banner['TYPE'] === 'upload'
		&&	$banner['SIZETYPE'] !== BANNER_SIZE_OVERLAY
		) {
			if (!($upload = db_single_assoc('bannerupload', '
				SELECT FILETYPE, SIZE, SIZE2x
				FROM bannerupload
				WHERE BANNERID = '.$_POST['BANNERID']))
			) {
				if ($upload !== false) {
					not_found('banner', $_POST['BANNERID']);
				}
				return false;
			}
			$missing_x1  = !$upload['SIZE'];
			$missing_x2  = !$upload['SIZE2x'];
			if ($missing_x1 || $missing_x2) {
				(!$missing_x1 ? register_error(...) : register_warning(...))('banner:error:missing_parts_LINE', ['1x' => $missing_x1, '2x' => $missing_x2]);
			}

		}
	}
	if (isset($_POST['EXPLICIT'])
	&&	!require_idnumber($_POST, 'PRICE')
	) {
		return false;
	}
	$infosetlist[] = "INACTIVE_INVISIBLE = b'".(isset($_POST['INACTIVE_INVISIBLE']) ? 1 : 0)."'";
	$infosetlist[] = "INACTIVE_INVISIBLE = b'".(isset($_POST['INACTIVE_INVISIBLE']) ? 1 : 0)."'";
	# $infosetlist[] = "NOCLICKS = b'".(isset($_POST['NOCLICKS']) ? 1 : 0)."'";
	$infosetlist[] = "EXPLICIT = b'".(isset($_POST['EXPLICIT']) ? 1 : 0)."'";
	$infosetlist[] = 'PRICE = '.$_POST['PRICE'];
	$infosetlist[] = 'RELATIONID = '.$_POST['RELATIONID'];
	if (isset($_POST['COMMISSION'])) {
		$infosetlist[] = 'COMMISSION = '.$_POST['COMMISSION'];
	}
	require_once '_duration.inc';
	if (!($info = process_duration_form_parts())) {
		return false;
	}
	[$startstamp, $stopstamp] = $info;

	$setlist[] = 'STARTSTAMP='.$startstamp;
	$setlist[] = 'STOPSTAMP='. $stopstamp;

	if (!empty($oldad)
	&&	$oldad['STOPSTAMP'] !== $stopstamp
	&&	$oldad['STOPSTAMP'] < CURRENTSTAMP - FREQCAP_KEEP_PERIOD + 2 * ONE_DAY
	) {
		register_error('ad:error:end_time_cannot_be_changed_LINE');
		return false;
	}
	$sizetype = $banner ? $banner['SIZETYPE'] : 0;

	$new = !$adid;

	if ((
		#	new:
			$new
		#	existing but changed position or size:
		||	$oldad['SIZETYPE'] !== $sizetype
		||	$oldad['POSITION'] !== $_POST['POSITION']
		#	budget calculation, need adprice too:
		||	$_POST['WHAT'] === 'budget'
		)
	&&	$sizetype
	&&	$_POST['POSITION']
	) {
		if (false === ($adprice = db_single_assoc('adpricesize', "
			SELECT ID, BASEPRICE, CPDAY
			FROM adpricesize
			WHERE SIZETYPE IN (0, $sizetype)
			  AND ADPOS IN (0, {$_POST['POSITION']})
			  AND ADPRICEID <= ".CURRENTSTAMP."
			  AND ACTIVE = 1
			ORDER BY
				SIZETYPE = $sizetype DESC,
				ADPOS = {$_POST['POSITION']} DESC,
				ADPRICEID DESC
			LIMIT 1"
		))) {
			return false;
		}
		$infosetlist[] = 'ADPRICEID='.($adprice ? $adprice['ID'] : 0);
	}

	$keeptop = isset($_POST['KEEPTOP']) || $partyad ? true : false;
	$setlist[] = 'KEEPTOP = b\''.($keeptop ? '1' : '0').'\'';

	if ($keeptop) {
		$_POST['IMPRESSIONS'] = 0;
	} else switch ($_POST['WHAT']) {
	case 'impressions':
		$_POST['IMPRESSIONS'] = $_POST['WHATVAL'];
		break;

	case 'budget':
		# cpm for regular banners, cpday for agenda spotlight
		$cpm =	!empty($adprice)
		?	$adprice['BASEPRICE']
		:	db_single('adpricesize','
			SELECT BASEPRICE
			FROM adpricesize
			WHERE ADPRICEID = '.$oldad['ADPRICEID']);

		if (!$cpm) {
			if ($cpm === false) {
				return false;
			}
			break;
		}

		$_POST['IMPRESSIONS'] =
			$_POST['DISCOUNT'] === 100
		?	1000 * $_POST['WHATVAL'] / $cpm
		:	1000 * $_POST['WHATVAL'] / ($cpm * (100 - $_POST['DISCOUNT']) / 100);
	}

	$infosetlist[] = 'DISCOUNT='.	($_POST['DISCOUNT'] ?: 0);
	$infosetlist[] = 'INVOICENR='.	(empty($_POST['INVOICENR']) ? 'NULL' : $_POST['INVOICENR']);
	$infosetlist[] = 'ADMIN_TEXT="'.addslashes($_POST['ADMIN_TEXT']).'"';
	$infosetlist[] = 'CMTS='.	(isset($_POST['CMTS']) ? 1 : 0);

	$setlist[] = 'TAKEOVER=b\''.	($takeover = (!$keeptop || !isset($_POST['TAKEOVER']) ? 0 : 1)).'\'';
	$setlist[] = 'KEEPGOING='.	(isset($_POST['HARDSTOP']) ? "b'0'" : "b'1'");
	$setlist[] = 'FREQCAP='.	($takeover && $_POST['POSITION'] !== ADPOS_OVERLAY ? 0 : $_POST['FREQCAP']);
	$setlist[] = 'FREQCAP_DAY='.	($takeover && $_POST['POSITION'] !== ADPOS_OVERLAY ? 0 : $_POST['FREQCAP_DAY']);
	$setlist[] = 'DELAY='.		min($_POST['DELAY'],		  ONE_HOUR);
	$setlist[] = 'DELAY_POSITION='.	min($_POST['DELAY_POSITION'], ONE_HOUR);
	$setlist[] = 'BANNERID='.	($partyad ? 0 : ($_POST['BANNERID'] ?: 0));
	$setlist[] = 'PARTYID='.	($partyad ? ($_POST['PARTYID'] ?: 0) : 0);
	/*$setlist[] = 'REGONLY='.	((	isset($_POST['MINAGE'])
					||	!empty($_POST['GENDERONLY'])
					||	isset($_POST['REGONLY'])
					) ? 1 : 0);*/

	# $setlist[] = 'MINAGE='.		(isset($_POST['MINAGE']) ? 18 : 0);
	# $setlist[] = 'GENDERONLY="'.	$_POST['GENDERONLY'].'"';
	# $setlist[] = 'EROTICONLY='.	(isset($_POST['EROTICONLY']) ? "b'1'" : "b'0'");
	$setlist[] = 'FREE="'.		($_POST['FREE'] ? $_POST['FREE']+1 : 0).'"';
	$setlist[] = 'POSITION='.	$_POST['POSITION'];
	# $setlist[] = 'CITYLIMIT='.	(isset($_POST['CITYLIMIT']) ? "b'1'" : "b'0'");
	# $setlist[] = 'CITYEXACT='.	(isset($_POST['CITYEXACT']) ? "b'1'" : "b'0'");
	$setlist[] = 'DOMAIN="'.	$_POST['DOMAIN'].'"';
	$setlist[] = 'OTHER_DOMAIN_FORCED_FILTERS='.(isset($_POST['OTHER_DOMAIN_FORCED_FILTERS']) ? "b'1'" : "b'0'");

	profilefilter_post_item($setlist);

	# foreach (['URL', 'COUNTIMG'] as $key) {
	$key = 'URL';
		$url = strip_shy($_POST[$key]);
		$url = str_replace('&amp;', '&', $url);
		# strip Partyflock utm from links, as we add these ourselves later
		if (str_contains($url, 'utm_source=2xpr')) {
			$url = preg_replace(
				[	'"(?:([\?&])utm_source=2xpr)"',
					'"(?:([\?&])utm_medium=partyflock)"',
					'"(?:([\?&])utm_content=[^&]*)"',
					'"[\?&]+$"',
					'"&{2,}"',
					'"\?&+"'
			],	[
					'$1',
					'$1',
					'$1',
					'',
					'&',
					'?',
			],	$url);
		}
		$_POST[$key] = $url;
	#}

	# $infosetlist[] = 'COUNTIMG="'.	addslashes($_POST['COUNTIMG']).'"';
	# $infosetlist[] = 'COUNTHTML="'.	addslashes(strip_shy($_POST['COUNTHTML'])).'"';

	$infosetlist[] = 'URL="'.addslashes($_POST['URL']).'"';
	$infosetlist[] = 'URL_ADD_DEFAULT_UTM='.(isset($_POST['URL_ADD_DEFAULT_UTM']) ? 1 : 0);

	if (empty($_POST['URL'])
	&&	$banner
	&&	banner_needs_url($banner)
	) {
		register_warning('ad:error:banner_needs_url_LINE');
		return false;
	}
	if (isset($_POST['TICKETID'])) {
		$infosetlist[] = 'TICKETID = '.($_POST['TICKETID'] ?? '0');
	}
	if (isset($_POST['VOUCHER'])) {
		require_once '_voucher.inc';
		if (false === ($voucherid = optional_voucherid($oldad))) {
			return false;
		}
		$infosetlist[] = 'VOUCHERID='.$voucherid;
	}
	$height = have_idnumber($_POST, 'HEIGHT') ?: ($banner ? ($banner['HEIGHT'] ?: 0) : 0);

	if ($new) {
		$infosetlist[] = 'CSTAMP  = '.CURRENTSTAMP;
		$infosetlist[] = 'CUSERID = '.CURRENTUSERID;
		if (!db_insert('adinfo','
			INSERT INTO adinfo
			SET '.implode(', ', $infosetlist))
		) {
			return false;
		}
		$_REQUEST['sID'] = $adid = db_insert_id();
	}

	$sections = [];
	foreach (explode("\n", $_POST['SECTIONS']) as $line) {
		if (!($line = mytrim($line))) {
			 continue;
		}
		if (str_contains($line, ' ')
		||	str_contains($line, "\t")
		) {
			register_error('ad:error:unknown_section_specifier_LINE', ['LINE' => $line]);
			continue;
		}
		if (in_array($line, ['^/home'. '/home', 'home', '^/', '/', ''], strict: true)) {
			$sectionid = __SECTIONS['home'];
			$sections["($sectionid, '')"] = "($adid, $sectionid, UNIX_TIMESTAMP(). '')";
			continue;
		}
		if (preg_match('"^(?<element>party|event)\s*:\s*(?<id>\d+)$"', $line, $match)) {
			$element = match($match['element']) {
				'event'	=> 'party',
				default	=> $match['element'],
			};
			$sectionid = __SECTIONS[$element];
			$regex = $element.':'.$match['id'];
			$sections["($sectionid, '$regex')"] = "($adid, $sectionid, UNIX_TIMESTAMP(), '$regex')";
			continue;
		}
		if (preg_match('"^/?(?<section>\w+)(?<rest_regex>.*)$"', $line, $match)) {
			$regex = $match['rest_regex'] ? $match[0] : '';

			if (!($sectionid = get_sectionid($match['section']))) {
				register_error('ad:warning:unknown_section_LINE', ['SECTION' => $match['section']]);
			} else {
				$regex = addslashes($regex);
				$sections["($sectionid, '$regex')" ] = "($adid, $sectionid, UNIX_TIMESTAMP(), '$regex')";
				$sectionspec[$sectionid] = $sectionid;
			}
			continue;
		}
		register_error('ad:error:unknown_section_specifier_LINE', ['LINE' => $line]);
	}

	$sections_affected = 0;
	if ($adid) {
		if (!db_insert('adforsection_log','
			INSERT INTO adforsection_log
			SELECT *, '.CURRENTSTAMP.'
			FROM adforsection
			WHERE ADID = '.$adid.
			($extrawhere = $sections  ? ' AND (SECTIONID, REGEX) NOT IN ('.implodekeys(',', $sections).')' : ''))
		||	!db_delete('adforsection', "
			DELETE FROM adforsection
			WHERE ADID = $adid
			$extrawhere")
		) {
			return false;
		}
		$sections_affected += db_affected();
	}
	if ($sections) {
		if (!db_insert('adforsection', '
			INSERT IGNORE INTO adforsection (ADID, SECTIONID, CSTAMP, REGEX)
			VALUES '.implode(', ', $sections))
		) {
			return false;
		}
		$sections_affected += db_affected();
		$flags |= ADFLG_SECTION_LIMIT;
	}
	$cities = [];
	if (!empty($_POST['CITY'])) {
		foreach ($_POST['CITY'] as $cityid => $radius) {
			if (!$radius) {
				$radius = '0';
			}
			if (!is_number($cityid) || !is_number($radius)) {
				register_error('require:misc:bad_value',array('NAME'=>'CITY'));
				return false;
			}
			$cities["($cityid, $radius)"] = "($adid, $cityid, $radius, ".CURRENTSTAMP.')';
		}
	}
	$cities_affected = 0;
	if (!$new) {
		if (!db_insert('adforcityspec_log','
			INSERT INTO adforcityspec_log
			SELECT *,'.CURRENTSTAMP.'
			FROM adforcityspec
			WHERE ADID = '.$adid.
			($extrawhere = $cities ? ' AND (CITYID, RADIUS) NOT IN ('.implodekeys(', ', $cities).')' : ''))
		||	!db_delete('adforcityspec','
			DELETE FROM adforcityspec
			WHERE ADID = '.$adid.
			$extrawhere)
		) {
			return false;
		}
		$cities_affected += db_affected();
	}
	if ($cities) {
		if (!db_insert('adforcityspec','
			INSERT IGNORE INTO adforcityspec (ADID, CITYID, RADIUS, CSTAMP)
			VALUES '.implode(', ', $cities))
		) {
			return false;
		}
		$cities_affected += db_affected();
	}
	if (isset($_POST['FLAGS'])) {
		foreach ($_POST['FLAGS'] as $flag) {
			$flags |= $flag;
		}
	}
	$setlist[] = "FLAGS=b'".sprintf('%b',$flags)."'";
	if (CURRENTSTAMP < $stopstamp
	&&	(	!isset($oldad)
		||	$cities_affected
		||	$sections_affected
		||	$oldad['STOPSTAMP']	  !== $stopstamp
		||	$oldad['STARTSTAMP']  !== $startstamp
		||	$oldad['IMPRESSIONS'] !== $_POST['IMPRESSIONS']
		||	$oldad['KEEPTOP']	  !== $keeptop
		||	$oldad['DOMAIN']	  !== $_POST['DOMAIN']
		||	$oldad['CITYLIMIT']	  !== isset($_POST['CITYLIMIT']))
	) {
		if ($keeptop) {
			if ($adid) {
				remove_distribution('ad',$adid);
			}
			register_notice('distribution:notice:unnecessary_LINE');
		} else {
			$distribution = calculate_distribution(
				$_POST['POSITION'] == ADPOS_SMALL_SCREEN ? ADTYPE_SMALL_SCREEN : ADTYPE_REGULAR,
				$_REQUEST['sID'] ?: 0,
				$_POST['DOMAIN'],
				$startstamp,
				$stopstamp,
				$_POST['IMPRESSIONS'],
				isset($oldad) ? $oldad['IMPRESSIONSDONE'] : 0,
				$maximp,
				$_POST['POSITION'],
				$_POST['CITY'] ?? null,
				$sectionspec ?? false,
				// need_user: isset($_POST['MINAGE']) || isset($_POST['REGONLY'])
			);
			if (!$distribution) {
				# Make the ad inactive if it does not fit and there is no distribution.
				unset($_POST['ACTIVE']);
				if ($maximp === false) {
					register_error('ad:error:distribution_LINE');
				} else {
					register_error('ad:error:not_feasible_LINE' , ['MAXIMPRESSIONS' => (int)$maximp]);
				}
			} elseif (!$new) {
				register_notice('distribution:notice:recalculated_LINE');
			}
		}
	}
	$setlist[] = "ACTIVE=b'".(isset($_POST['ACTIVE']) ? 1 : 0)."'";
	$setlist[] = 'IMPRESSIONS='.$_POST['IMPRESSIONS'];
	if (!$new) {
		$infosetlist[] = 'MSTAMP  = '.CURRENTSTAMP;
		$infosetlist[] = 'MUSERID = '.CURRENTUSERID;
		$setlist[] = 'MSTAMP  = '.CURRENTSTAMP;
		$setlist[] = 'MUSERID = '.CURRENTUSERID;
		if (!db_insert('adinfo_log','
			INSERT INTO adinfo_log
			SELECT * FROM adinfo
			WHERE ADID = '.$adid)
		||	!db_update('adinfo','
			UPDATE adinfo
			SET '.implode(', ', $infosetlist).'
			WHERE ADID = '.$adid)
		||	!db_insert('ad_log','
			INSERT INTO ad_log
			SELECT * FROM ad
			WHERE ADID = '.$adid)
		||	!db_update('ad','
			UPDATE ad
			SET '.implode(', ', $setlist).'
			WHERE ADID = '.$adid)
		) {
			return false;
		}
		register_notice('ad:notice:changed_LINE');
		flush_running_ads();
	} else {
		$setlist[] = 'MSTAMP  = '.CURRENTSTAMP;
		$setlist[] = 'MUSERID = '.CURRENTUSERID;

		if (!db_insert('ad', "INSERT INTO ad SET ADID = $adid, ".implode(', ', $setlist))) {
			return false;
		}
		register_notice('ad:notice:added_LINE');
		ticket_update('ad', $adid);
	}
	db_create('adimp', "
		CREATE TABLE IF NOT EXISTS `adimp_ad_$adid` (
			`IDENTID` int(10) unsigned NOT NULL DEFAULT 0,
			`USERID` mediumint(8) unsigned NOT NULL DEFAULT 0,
			`IPNUM` int(10) unsigned NOT NULL DEFAULT 0,
			`STAMP` int(10) unsigned NOT NULL DEFAULT 0,
			`SERVED` bit(1) NOT NULL DEFAULT b'0',
			`FREE` bit(1) NOT NULL DEFAULT b'0',
			`UNIQ` bigint(20) unsigned NOT NULL DEFAULT 0,
			`TRY` bit(1) NOT NULL DEFAULT b'0',
			`INCID` int(10) unsigned NOT NULL AUTO_INCREMENT,
			`IMMEDIATE` bit(1) NOT NULL DEFAULT b'0',
			`EXPANDED` bit(1) NOT NULL DEFAULT b'0',
			`CLICKED` bit(1) NOT NULL DEFAULT b'0',
			`IPBIN` varbinary(16) NOT NULL DEFAULT '',
			`HARDMATCH` bit(1) NOT NULL DEFAULT b'0',
			PRIMARY KEY (`INCID`),
			KEY `ELEMENT` (`ELEMENT`,`ID`)
		) ENGINE=MyISAM DEFAULT CHARSET=ascii PAGE_CHECKSUM=1");

	if (!empty($distribution)) {
		store_distribution($adid, $distribution);
	}
	require_once '_profilefilter.inc';
	store_profilefilter();
	require_once '_ad.inc';
	flush_adlist($_POST['POSITION']);
	memcached_delete('min_ad_delay');
	return true;
}
