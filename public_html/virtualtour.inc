<?php

function display_body(): void {
	switch ($_REQUEST['ACTION'] ?? null) {
	default:			not_found(); return;
	case null:			virtualtour_display_overview(); return;
	case 'comments':
	case 'comment':
	case 'single':		virtualtour_display_single(); return;
	case 'all':			virtualtour_display_all(); return;
	}
}
function virtualtour_menu() {
	layout_open_menu();
	layout_menuitem(Eelement_name('overview'),	'/virtualtour',		!$_REQUEST['ACTION']);
	layout_menuitem(Eelement_name('all'),		'/virtualtour/all',  $_REQUEST['ACTION'] === 'all');
	layout_continue_menu();
	layout_menuitem(__('virtualtour:request_LINE'), '/ticket/form?ELEMENT=virtualtour');
	layout_close_menu();
}

function get_width_and_height(): array {
	static $__width_and_height;
	if (isset($__width_and_height)) {
		return $__width_and_height;
	}
	require_once '_resolution.inc';
	if ($resolution = get_resolution()) {
		[, $width, $height] = $resolution;
		if ($width > 1280) {
			$width = 1280;
		}
	}
	$width ??= SMALL_SCREEN ? 425 : 1280;
	$height = $width * 9 / 16;
	return [$width, $height];
}

function virtualtour_display_overview() {
	layout_show_section_header();

	virtualtour_menu();

	if (!($tours = memcached_rowuse_hash(['virtualtour', 'virtualroom'], '
		SELECT TOURID, ELEMENT, ID, CSTAMP, (
			SELECT GROUP_CONCAT(CONCAT(ROOMID, "'.chr(0x1E).'", NAME) SEPARATOR "'.chr(0x1F).'")
			FROM virtualroom AS vr
			WHERE vr.TOURID=vt.TOURID
		) AS ROOMINFO
		FROM virtualtour AS vt
		WHERE VISIBLE = 1
		ORDER BY CSTAMP DESC
		LIMIT 32'
	))) {
		if ($tours === false) {
			page_problem(500);
		}
		return;
	}
	if (false === ($comment_count = memcached_simple_hash('virtualtour_comment','
		SELECT ID, COUNT(*)
		FROM virtualtour_comment
		WHERE ACCEPTED = 1
		  AND ID IN ('.implodekeys(', ', $tours).')
		GROUP BY ID'))
	) {
		return;
	}

	[$width, $height] = get_width_and_height();

	layout_open_box('white');
	?><div class="center"><?
	$cnt = SMALL_SCREEN ? 6 : 9;
	foreach ($tours as &$tour) {
		extract($tour);

		$roominfos = $tour['ROOMINFO'] = explode(chr(0x1F), $ROOMINFO);

		[$roomid, $roomname] = explode(chr(0x1E), $roominfos[array_rand($roominfos)]);

		?><div class="l block" style="width:<?= SMALL_SCREEN ? 50 : 33 ?>%"><?
			?><div  class="relative ib"><?
				if ($cmtcnt = $comment_count[$TOURID] ?? null) {
					show_comment_counter($cmtcnt);
				}
				?><div class="virtualtour zoomover" ><?
					?><a class="middle" href="<?= get_element_href('virtualtour', $TOURID) ?>"><?
						?><img<?
						if (aspect_objects()) {
							?> width="<?= $width ?>"<?
							?> height="<?= $height ?>"<?
							?> style="<?
								?>max-width: 100%;<?
								?>width: <?= $width ?>px;<?
								?>height: auto<?
							?>"<?
						}
						?> class="fw"<?
						?> alt="<?= element_name('virtualtour') ?>: <?= escape_specials(get_element_title($ELEMENT, $ID), utf8: isset(USE_UNICODE[$ELEMENT])) ?>"<?
						?> src="<?= VT_HOST ?>/vt/<?= $TOURID ?>/<?= $roomid ?>/t_regular<?= is_high_res() ?>.jpg" /><?
					?></a><?
				?></div><?
				?><div class="oneline"><?= get_element_link($ELEMENT, $ID) ?></div><?
				?><div class="oneline small"><?= _date_get($CSTAMP) ?></div><?
			?></div><?
		?></div><?

		if (!--$cnt) {
			break;
		}
	}
	unset($tour);
	?></div><?
	layout_close_box();

	layout_open_box('white');
	show_virtualtour_list($tours);
	layout_close_box();
}

function virtualtour_display_all() {
	layout_show_section_header();

	$tours = memcached_rowuse_hash(
		array('virtualtour','virtualroom'),'
		SELECT TOURID,ELEMENT,ID,CSTAMP/*,(
			SELECT GROUP_CONCAT(CONCAT(ROOMID,"'.chr(0x1E).'",NAME) SEPARATOR "'.chr(0x1F).'")
			FROM virtualroom AS vr
			WHERE vr.TOURID=vt.TOURID
		) AS ROOMINFO*/
		FROM virtualtour AS vt
		WHERE VISIBLE=1
		ORDER BY TOURID DESC'
	);

	layout_open_box('white');
	show_virtualtour_list($tours);
	layout_close_box();
}

function show_virtualtour_list($tours) {
	$hits = memcached_simple_hash('virtualtour_counter','
		SELECT VIRTUALTOURID, SUM(VIEWS)
		FROM virtualtour_counter
		WHERE VIRTUALTOURID IN ('.implodekeys(', ', $tours).')
		GROUP BY VIRTUALTOURID'
	);

 	layout_open_table(TABLE_FULL_WIDTH | TABLE_VTOP | TABLE_ROW_HILITE_ONMOUSEOVER);
	layout_start_header_row();
	layout_header_cell(Eelement_name('location').' &amp; '.element_plural_name('room(tour)'));
	layout_header_cell_right(Eelement_plural_name('view'));
	layout_header_cell_right(__C('field:added'));
	layout_stop_header_row();
	foreach ($tours as $tour) {
		extract($tour);

		layout_start_rrow(0, null, 'rpad');
		echo get_element_link('virtualtour', $TOURID);
		if ($ELEMENT === 'location'
		&&	($location = memcached_location_info($ID))
		&&	$location['CITYID']
		) {
			?><small class="light">, <?= escape_utf8(get_element_title('city', $location['CITYID'])) ?></small><?
		}

		layout_next_cell(class: 'right');
		echo $hits[$TOURID] ?? 0;

		layout_next_cell(class: 'right nowrap lpad');
		_date_printnice(PRINTNICE_DATE_DAY | (SMALL_SCREEN ? PRINTNICE_SHORT : 0), $CSTAMP);

		layout_stop_row();
	}
	layout_close_table();
}

function virtualtour_display_single(): void {
	if (!($tourid = $_REQUEST['sID'])) {
		not_found();
		return;
	}
	$tour = memcached_single_assoc('virtualtour','
		SELECT TOURID, ELEMENT, ID, CSTAMP, CREATORID, VISIBLE
		FROM virtualtour
		WHERE TOURID = '.$tourid
	);
	if ($tour === false) {
		return;
	}
	if (!$tour
	||	!$tour['VISIBLE']
	&	!have_admin('virtualtour')
	) {
		register_error('virtualtour:error:nonexistent_LINE', DO_UBB, ['ID' => $tourid]);
		return;
	}

	layout_show_section_header();

	virtualtour_menu();

	require_once '_ticketlist.inc';
	show_connected_tickets();

	if ($tour['ELEMENT'] === 'location'
	&&	($location = memcached_location_info($tour['ID']))
	&&	$location['CITYID']
	&&	($citylink = get_element_link('city', $location['CITYID']))
	) {
	}
	layout_open_box('white','tour');
	layout_box_header(
		'<h1>'.get_element_link($tour['ELEMENT'],$tour['ID']).'</h1> <small>'.MIDDLE_DOT_ENTITY.' '.(!empty($citylink) ? $citylink.' '.MIDDLE_DOT_ENTITY.' ' : null)._date_get($tour['CSTAMP']).'</small>'.
		(!$tour['VISIBLE'] ? ' ('.__('status:invisible').')' : null),
		'<small>'.element_name('photographer').': '.get_element_link('user',$tour['CREATORID']).'</small>'
	);

	include_js('components/krpano');

	[$width, $height] = get_width_and_height();

	?><div class="centered block" id="vt" style="height:<?= $height ?>px"></div><?

	echo '<script>',
		"embedpano({",
			(isset($_REQUEST['HTML5']) ? "html5:'always'," : null),
			"target:'vt',",
			"width:'100%',",
			"height:'100%',",
			"allowfullscreen:'true',",
			"xml:'/virtualtour/$tourid.xml'+(location.hash.match(/\\broom(\\d+)\\b/)?'?ROOMID='+RegExp.\$1:'')",
		"})",
	'</script>';

	layout_close_box();

	require_once '_resizy.inc';
	start_resizy('vt',RSZ_DOMREADY);


	require_once '_commentlist.inc';
	$comment_list = new _commentlist;
	$comment_list->item($tour);
	$comment_list->display();

	require_once '_hitlog.inc';
	log_hit();

	require_once '_counthit.inc';
	counthit('virtualtour', $tourid);
}
