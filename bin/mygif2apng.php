#!/usr/bin/php
<?php

declare(strict_types=1);

require_once __DIR__.'/../public_html/_exit_if_readonly.inc';
require_once __DIR__.'/../public_html/_run_and_exit.inc';

run_and_exit(syslog: true, syslog_quiet: true);

function main(): bool {
	syslog(LOG_INFO, 'converting GIF to APNG');
	if (!($data = file_get_contents('php://stdin'))) {
		syslog_last_error(LOG_ERR, 'failed to read anything from stdin');
		return false;
	}
	# NOTE: gif2apng cannot handle paths, only files in current directory
	if (!chdir('/tmpdisk')) {
		syslog_last_error(LOG_ERR, 'failed to change directory to /tmpdisk');
		return false;
	}
	$tmp_file_name = uniqid('__pf_gif2apng_', true);
	syslog(LOG_DEBUG, 'write '.strlen($data).' bytes to temporary file: '.$tmp_file_name);
	if (!file_put_contents($tmp_file_name, $data)) {
		syslog_last_error(LOG_ERR, "failed to write to temporary file $tmp_file_name");
		return false;
	}
	$png_tmp_name = "$tmp_file_name.png";
	register_shutdown_function(static function() use ($tmp_file_name, $png_tmp_name): void {
		unlink($tmp_file_name);
		unlink($png_tmp_name);
	});
	# On 2024-09-01, the following compression options were available, 7z was removed so it seems:
	# -z0 zlib
	# -z2 zopfli
	# Will try zopfli for now.
	syslog(LOG_DEBUG, $command = "gif2apng -z2 $tmp_file_name $png_tmp_name 1>/dev/null");
	if (false === system($command, $result_code)) {
		syslog_last_error(LOG_ERR, 'failed to convert GIF to APNG');
		return false;
	}
	if (!filesize($png_tmp_name)) {
		syslog_error(LOG_ERR, 'empty APNG file');
		return false;
	}
	readfile($png_tmp_name);
	return !$result_code;
}
