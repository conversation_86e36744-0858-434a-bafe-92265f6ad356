#!/usr/bin/php
<?php

define('CURRENTSTAMP',time());
define('RESETLINE',	"\n\x1B[A\x1B[K");

require_once '../public_html/_offline.inc';
require_once '../public_html/_db.inc';
require_once '../public_html/_error.inc';
require_once '../public_html/_require.inc';

ob_implicit_flush(true);
$rc = main();
exit(show_messages_cli() || !$rc ? 1 : 0);

function main() {
	require_once '../public_html/_password.inc';

/*	$receivers = db_rowuse_array(array('user','relationmember','user_account','relation'),'
		SELECT rm.RELATIONID,rm.USERID,EMAIL
		FROM relationmember AS rm
		JOIN relation USING (RELATIONID)
		JOIN user USING (USERID)
		JOIN user_account USING (USERID)
		WHERE REMOVED = 0
		ORDER BY STATUS="active" DESC,RELATIONID DESC'
	);
	if ($receivers === false) {
		return false;
	}
	if ($receivers) {
		$okreceivers = array();
		foreach ($receivers as $receiver) {
			if (!isset($emails[$receiver['EMAIL']])) {
				$emails[$receiver['EMAIL']] = $receiver['EMAIL'];
				$okreceivers[] = $receiver;
			}
		}
		$receivers = $okreceivers;
	}*/
	$receivers = array();
	$and_not_have_already = $receivers ? ' AND CONTACTEMAIL NOT IN ('.stringsimplode(',',$emails).')' : null;

	$more = db_rowuse_array('relation',$qstr = '
		SELECT DISTINCT RELATIONID,CONTACTEMAIL
		FROM relation
		WHERE REMOVED = 0
/*	  AND (CONTACTEMAIL LIKE "%@partyflock.nl" OR CONTACTEMAIL="<EMAIL>" OR CONTACTEMAIL="<EMAIL>")*/
		  AND CONTACTEMAIL!=""'.
		$and_not_have_already
	);
	if ($more) {
		$receivers += $more;
	}
	foreach ($receivers as $receiver) {
		$voucher = create_password(12,null,false);
		if (!db_insert('discountvoucher','
			INSERT INTO discountvoucher SET
				TYPE		="newyear2012",
				VOUCHER		="'.addslashes($voucher).'",
				EXPIRES		=1328050799,
				TO_EMAIL	="'.addslashes($receiver['CONTACTEMAIL']).'",
				TO_RELATIONID	='.$receiver['RELATIONID'].',
				DISCOUNT	='.mt_rand(2,6))
		) {
			return false;
		}
	}
	return true;
}
