#!/usr/bin/php
<?php

declare(strict_types=1);

$path = dirname($_SERVER['SCRIPT_FILENAME']).'/../';

require_once __DIR__.'/../public_html/_run_and_exit.inc';
require_once __DIR__.'/../public_html/defines/facebook.inc';

run_and_exit();

function main(): bool {
	global $argv;
	if (empty($argv[1])) {
		error_log('need token');
		return false;
	}
	echo '     appsecret_proof=',appsecret_proof($argv[1],FACEBOOK_LIVE_APPID),"\n";
	echo 'test appsecret_proof=',appsecret_proof($argv[1],FACEBOOK_TEST_APPID),"\n";
	return true;
}
