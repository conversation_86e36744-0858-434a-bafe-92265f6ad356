#!/usr/bin/php
<?php

define('CURRENTSTAMP',	time());
define('CURRENTUSERID',	0);
#define('DRYRUN',true);

$path = dirname($_SERVER['SCRIPT_FILENAME']).'/../';

require_once $path.'public_html/_offline.inc';
require_once $path.'public_html/_db.inc';
require_once $path.'public_html/_error.inc';
require_once $path.'public_html/_urltitle.inc';

ob_implicit_flush(true);
$rc = main();
exit(show_messages_cli() || !$rc ? 1 : 0);

function main() {
	$data = file_get_contents('/root/shadowlands.png');
	
	db_insert('photo_overlay','
			REPLACE INTO photo_overlay SET
			DESCR="Shadowlands logo",
			OVERLAYID=10,
			CSTAMP	=UNIX_TIMESTAMP(),
			DATA	="'.addslashes($data).'"'
		);
}
