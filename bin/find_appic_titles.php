#!/usr/bin/php
<?php

define('CURRENTSTAMP',time());

if (PHP_SAPI != 'cli') {
	exit;
}

require_once '_db.inc';

$parties = db_simple_hash('party','
	SELECT PARTYID,facebook_info.NAME
	FROM party
	JOIN facebook_info USING (PARTYID)
	WHERE STAMP>'.CURRENTSTAMP
);

foreach ($parties as $partyid => $fbtitle) {
	$party = memcached_party_and_stamp($partyid);
	$orgids = $party['ORGIDS'] ? explode_to_hash(',',$party['ORGIDS']) : [];
	if ($orgids) {
		foreach ($orgids as $orgid => &$name) {
			$name = db_single('organization','SELECT NAME FROM organization WHERE ORGANIZATIONID='.$orgid);
		}
		unset($name);
	}
	$appic_title = get_clean_title_from_facebook($fbtitle,['organizationids'=>$orgids]);
	if (false !== stripos($appic_title,'offici')) {
		error_log($partyid.': '.$fbtitle);
	}
}

