#!/usr/bin/perl

# Combine this command with 'grep', to find occurrences and then run 'joe' to edit the files.
# This allows some inspection using 'joe'. Otherwise 'sed' could be used without this command to just replace.
#
# grep "I WANT TO FIND AND EDIT THIS" -c -l | /home/<USER>/bin/pup | sh"

my $cmd;
if (defined $ARGV[0]) {
	unshift(@ARGV);
	$cmd = join(' ', @ARGV);
} else {
	$cmd = 'joe';
}

while (<STDIN>) {
	chomp;
	# Remove known escape sequences
	s/\x1b\[[0-9;]*[mGKHF]//g;
	if (/~$/) {
		next;
	}
	print "$cmd $_\n";
}
