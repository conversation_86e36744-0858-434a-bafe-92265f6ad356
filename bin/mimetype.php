#!/usr/bin/php -d display_errors=on
<?php

ini_set('display_errors','on');

define('CURRENTSTAMP',time());

$path = dirname($_SERVER['SCRIPT_FILENAME']).'/../';

require_once $path.'public_html/_mimetype.inc';
require_once $path.'public_html/_error.inc';

ob_implicit_flush(true);
$rc = main();
exit(show_messages_cli() || !$rc ? 1 : 0);

function main() {
	global $argv;
	$filenames = null;
	$raw = false;
	$stop = count($argv);
	for ($i = 1; $i < $stop; ++$i) {
		switch ($argv[$i]) {
		case '--raw':
			$raw = true;
			break;
		default:
			$filenames[] = $argv[$i];
			break;
		}
	}
	if (empty($filenames)) {
		error_log('usage: mimetype [--raw] filenames',0);
		return false;
	}
	$rc = true;
	foreach ($filenames as $filename) {
		echo $filename,($raw ? "\x1F" : ': ');
		if (!file_exists($filename)) {
			echo $raw ? "not found\n" : "ERROR\x1FNOT FOUND\x1F\n";
			$rc = false;
			continue;
		}
		$mimetype = mimetype_file($filename,$filename);
		if ($raw) {
			if ($mimetype) {
				list($type,$charset) = mimetype_and_charset($mimetype);
				echo "OK\x1F$type\x1F$charset\n";
			} else {
				echo "ERROR\x1FUNKNOWN\x1F\n";
			}
		} else {
			echo $mimetype,"\n";
		}
	}
	return $rc;
}
