#!/usr/bin/php
<?php

declare(strict_types=1);

require_once __DIR__.'/../public_html/_error.inc';
require_once __DIR__.'/../public_html/_memcache.inc';
require_once __DIR__.'/../public_html/_run_and_exit.inc';
require_once __DIR__.'/../public_html/defines/storage_sizes.inc';

run_and_exit();

function main(): bool {
	global $argv;

	if (!empty($argv[1])
	&&	$argv[1] === 'flush'
	) {
		for ($level = 0; $level < 128; ++$level) {
			$delete[] = 'free_watcher:'.$level;
		}
		memcached_delete($delete);
		return true;
	}

	return check_free();
}

function check_free(): bool {
	if (!($free = shell_exec('free -b'))) {
		return false;
	}
	foreach (explode("\n", $free) as $line) {
		if (!preg_match('"^Swap:\s+(?P<total>\d+)\s+(?P<used>\d+)"', $line, $match)) { //NOSONAR
			continue;
		}
		$used = (int)$match['used'];

		if ($used < 128 * MEGABYTE) {
			continue;
		}
		$level = (int)($used / (128 * MEGABYTE));

		if (!memcached_add($level_key = 'free_watcher:'.$level, true, ONE_HOUR)) {
			# already done
			continue;
		}
		memcached_set($level_key, true, ONE_HOUR);

		echo	'swap is growing on ', gethostname(), ': ',(int) ($used / MEGABYTE),"MB\n\n",
				"VmRSS:\n\n",
				shell_exec('for file in /proc/*/status ; do awk \'/VmRSS|Name/{printf $2 " " $3}END{ print ""}\' $file; done | sort -k 2 -n -r  | egrep "(k|M)B"'),
				"\n\nps aux:\n\n",
				shell_exec('ps aux'),
				"\n\nserver-status:\n\n",
				shell_exec('curl --insecure https://localhost/server-status | html2text  -width 300');
	}
	return true;
}
