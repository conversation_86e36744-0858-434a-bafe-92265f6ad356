<?php

/** @noinspection DuplicatedCode */
declare(strict_types=1);

define('CURRENTSTAMP', time());

require_once __DIR__.'/../public_html/_exit_if_offline.inc';
require_once __DIR__.'/../public_html/_image_bail.inc';
require_once __DIR__.'/../public_html/_browser.inc';
require_once __DIR__.'/../public_html/_hosts.inc';
require_once __DIR__.'/../public_html/_urltitle.inc';
require_once __DIR__.'/../public_html/_helper.inc';
require_once __DIR__.'/../public_html/_require.inc';
require_once __DIR__.'/../public_html/_memcache.inc';
require_once __DIR__.'/../public_html/defines/cache_path.inc';

deny_robots_if_partyflock_under_load();

function image_bail_and_cleanup(int $errno): never {
	global $__fp, $__cache_name;
	if (!empty($__cache_name)
	&&	!empty($__fp)
	&&	!filesize($__cache_name)
	) {
		unlink($__cache_name);
	}
	image_bail($errno);
}

if (!isset($_SERVER['eIMGID'], $_SERVER['eTYPE'])) {
	error_log('missing eIMGID or eTYPE');
	image_bail(404);
}

$photoid = (int)$_SERVER['eIMGID'];
$type	 =		$_SERVER['eTYPE'];

if (!in_array($type, ['thumb', 'main', 'original'], true)
&&	!preg_match('"^(?<width>\d+)x(?<height>\d+)$"', $type, $match)
) {
	error_log('invalid type: '.$type);
	image_bail(404);
}
$filetype = $_SERVER['eFILETYPE'];
if (empty($_SERVER['eFILETYPE'])
||	!($mimetype = match ($filetype) {
		'webp'	=> 'webp',
		'avif'	=> 'avif',
		'jpg'	=> 'jpeg',
		default	=> null})
) {
	error_log('invalid file type: '.$filetype);
	image_bail(503);
}

if (!($photo = db_single_assoc(['image', 'party', 'location', 'user'],'
	SELECT	HIDDEN, image.USERID, party.STAMP, LATITUDE, LONGITUDE, EXTERN, LOGOPOS, OVERLAYID, image.PARTYID, COALESCE(party.NAME, gallery.TITLE) AS NAME, GALLERYID,
			THUMB_WIDTH, THUMB_HEIGHT,
			WIDTH, HEIGHT,
			DOUBLE_WIDTH, DOUBLE_HEIGHT,
			ORIGINAL_WIDTH, ORIGINAL_HEIGHT,
			IF(ANONYMOUS, "", TEXTOVERLAY) AS TEXTOVERLAY
	FROM image
	LEFT JOIN party USING (PARTYID)
	LEFT JOIN gallery USING (GALLERYID)
	LEFT JOIN location USING (LOCATIONID)
	LEFT JOIN user ON image.USERID = user.USERID
	WHERE IMGID = '.$photoid
))) {
	error_log('photo not found: '.$photoid);
	image_bail($photo === false ? 503 : 404);
}

if (# test certain overlay
	HOME_THOMAS
&&	have_idnumber($_REQUEST, 'OVERLAYID')
) {
	$photo['OVERLAYID'] = $_REQUEST['OVERLAYID'];
}

$partyid = $photo['PARTYID'];

$x2         = !empty($_SERVER['e2x']) || isset($_REQUEST['2x']);
$multiplier = $x2 ? 2 : 1;

if ($type === 'front'	&& ($long_edge = 165)
||	$type === 'medium'	&& ($long_edge = 500)
) {
	$aspect = $photo['WIDTH'] / $photo['HEIGHT'];
	if ($aspect >= 1) {
		$width = $long_edge;
		if ($x2) {
			$width *= 2;
		}
		$height = round($width / $aspect);
	} else {
		$height = $long_edge;
		if ($x2) {
			$height *= 2;
		}
		$width = round($height * $aspect);
	}
	moved_permanently(str_replace(["/$type/", '@2x'], ["/{$width}x$height/", ''], $_SERVER['REQUEST_URI']));
}

# these have no custom made 2x, make them from larger images
$no2xsmall =	(	$partyid !== 199317
				&&	$photoid <= 701980
				||	!$photo['DOUBLE_WIDTH']
				);

if ($no2xsmall && $x2) {
	# have no x2, redirect to non x2
	moved_permanently(str_replace('@2x', '', $_SERVER['REQUEST_URI']));
}

$__mstamp = false;

if (!file_exists($thumb_src = '/mnt/gluster/photos_v2/'.($partyid ?: 'gallery/'.$photo['GALLERYID']).'/thumb/'.$photoid.'.jpg')
||	false === ($__mstamp = filemtime($thumb_src))
) {
	# This can go very fast if lots of robots are getting photos, so limit to 1 per 10 seconds
	if (memcached_add('photo_not_found_on_gluster', true, 10)) {
		mail_log("photo thumb not found for {$_SERVER['REQUEST_URI']}\n\ntried $thumb_src");
	}
	error_log("photo thumb not found for {$_SERVER['REQUEST_URI']}, tried $thumb_src");
	image_bail(503);
}

header('Cache-Control: public,must-revalidate');

$etag = '2011091301';

require_once __DIR__.'/../public_html/_modified.inc';

if ($photo['HIDDEN']
||	$type === 'original'
) {
	require_once __DIR__.'/../public_html/_currentuser.inc';
	$currentuser = new _currentuser(TINY_USER | SKIP_SETTINGS);
	if ($photo['HIDDEN']
	&&	(	CURRENTUSERID <= 1
		||	(	CURRENTUSERID !== $photo['USERID']
			&&	!$currentuser->is_admin(['appearance', 'photo'])
			)
		)
	||	$type === 'original'
	&&	!$currentuser->is_admin('photo')
	) {
		error_log('hidden photo: '.$photoid);
		image_bail(403);
	}
	if ($type === 'original') {
		$_REQUEST['REALLY_NO_LOGO'] = true;
	}
}

if (isset($_SERVER['eTITLE'])
&& ($have = urlencode($_SERVER['eTITLE'])) !== ($want = get_urltitle('photo', $photoid))
) {
	moved_permanently(PHOTO_HOST."/$photoid/$type/$want{$_SERVER['e2x']}.$filetype");
}

$etag .= get_etag($photo);

if (!isset($_REQUEST['NOMEMCACHE'])
&&	!force_refresh()
&&	not_modified($__mstamp, $etag)
) {
	error_log('not modified');
	image_bail(304);
}
$load_type = null;
if ($got_dims = isset($match)) {
	$want_width  = (int)$match['width'];
	$want_height = (int)$match['height'];
	if ($x2) {
		moved_permanently(get_photo_url($photoid, ($want_width * 2).'x'.($want_height * 2)));
	}
	foreach (['thumb', 'main'] as $chk) {
		# base sizes:
		$sizes[$chk] = [
			false,
			$width  = $photo[$chk === 'main' ? 'WIDTH'  : strtoupper($chk.'_WIDTH')],
			$height = $photo[$chk === 'main' ? 'HEIGHT' : strtoupper($chk.'_HEIGHT')],
			$chk
		];
		# x2 sizes:
		if ($chk === 'main'
		?	$photo['DOUBLE_WIDTH']
		:	!$no2xsmall
		) {
			$sizes[$chk.'2x'] = ['@2x', $width * 2, $height * 2, $chk];
		}
	}
	asort($sizes);
	foreach ($sizes as $tmp_load_type => $dims) {
		[$is2x, $width, $height, $base_type] = $dims;
		if ($width  === $want_width
		&&	$height === $want_height
		&&	(	!$is2x
			||	!$no2xsmall
			)
		) {
			moved_permanently(get_photo_url($photoid, $base_type, $is2x));
		}
		# break on first image that is large enough
		if ($width  >= $want_width
		&&	$height >= $want_height
		) {
			$load_type = $tmp_load_type;
			break;
		}
	}
	$load_type ??= array_key_last($sizes);
	$cache_type   = $match[0];
	$image_width  = $want_width;
	$image_height = $want_height;
} else {
	switch ($type) {
	case 'thumb':
		$load_type    = $x2 && !$no2xsmall ? 'thumb2x' : 'thumb';
		$image_width  = $photo['THUMB_WIDTH' ] * $multiplier;
		$image_height = $photo['THUMB_HEIGHT'] * $multiplier;
		$cache_type   = $image_width.'x'.$image_height;
		break;
	case 'main':
		$load_type	  = $x2 && !$no2xsmall ? 'main2x' : 'main';
		$image_width  = $photo['WIDTH' ] * $multiplier;
		$image_height = $photo['HEIGHT'] * $multiplier;
		$cache_type	  = $image_width.'x'.$image_height;
		break;
	case 'original':
		$image_width  = $photo['ORIGINAL_WIDTH' ];
		$image_height = $photo['ORIGINAL_HEIGHT'];
		$load_type    = $type;
		$cache_type   = $type;
		break;
	default:
		image_bail(503);
	}
}

if (!(file_exists($photo_src = '/mnt/gluster/photos_v2/'.($partyid ?: 'gallery/'.$photo['GALLERYID'])."/$load_type/$photoid.$filetype"))) {
	#if ($filetype !== 'jpg'
	#&&	file_exists($photo_src = '/mnt/gluster/photos_v2/'.($partyid ?: 'gallery/'.$photo['GALLERYID'])."/$load_type/$photoid.jpg")
	#) {
	#} else {
		mail_log($error = "photo $load_type not found for {$_SERVER['REQUEST_URI']}\n\ntried: $photo_src");
		image_bail(503);
	#}
}

header('Content-Type: image/'.$mimetype);

function get_overlay(array $photo): Imagick|false|null {
	# Partyflock v1: 2
	# Partyflock v2: 6
	# Appic: 7

	if (empty($photo['OVERLAYID'])) {
		return null;
	}
	if (!($data = db_single('photo_overlay', 'SELECT DATA FROM photo_overlay WHERE OVERLAYID = '.$photo['OVERLAYID']))) {
		return false;
	}
	try {
		$image = new Imagick();
		$image->readImageBlob($data);
	} catch (Exception $e) {
		mail_log($e->getMessage().' @ '.$_SERVER['REQUEST_URI']);
		return false;
	}
	return $image;
}
if (isset($_REQUEST['REALLY_NO_LOGO'])
||	$cache_type === 'thumb'
||	$cache_type === 'original'
||	(	isset($_REQUEST['NO_LOGO'])
	&&	check_admin())
) {
	header('Content-Length: '.filesize($photo_src));
	header('X-Sendfile: '.$photo_src);
	exit;
}
if (false === ($overlay = get_overlay($photo))) {
	image_bail_and_cleanup(500);
}
$add_overlay =
	$overlay
&&	$image_width * $image_height > 450 * 300
&&	$image_height > 250;

$lock_ok = try_cache($photoid, $photo['GALLERYID'], $cache_type, $photo);

if ($add_overlay
||	$got_dims
) {
	$data = null;
	try {
		if (!($image = new Imagick($photo_src))) {
			mail_log('failed to create Imagick object');
			image_bail_and_cleanup(503);
		}
		$image_width  = $image->getImageWidth();
		$image_height = $image->getImageHeight();
		if ($got_dims) {
			require_once __DIR__.'/../public_html/_smartcrop.inc';
			if (should_smart_crop(
				$image_width,
				$image_height,
				$want_width,
				$want_height,
			)) {
				if (!($top_crop = db_single_assoc('topcrop', '
					SELECT * FROM topcrop
					WHERE SRC = "photo"
					  AND ID = '.$photoid.'
					  AND ASPECT = '.($want_width / $want_height)))
				) {
					require_once __DIR__.'/../public_html/_smartcrop.inc';
					if ($top_crop = determine_smart_crop_file($photo_src, $want_width, $want_height)) {
						$top_crop = store_crop('photo', $photoid, $top_crop, $image_width, $image_height);
					} else {
						mail_log('could not find top crop for '.$_SERVER['REQUEST_URI']);
						header('Retry-After: '.ONE_DAY);
						image_bail_and_cleanup(503);
					}
				}

				$size = [$image_width, $image_height];

				$new_width  = (int)min($size[0], round($size[0] * $top_crop['WIDTH' ] / 1000000000));
				$new_height = (int)min($size[1], round($size[1] * $top_crop['HEIGHT'] / 1000000000));

				if ($new_width !== $new_height) {
					# safe minimum, not sure whether x and y are imperfect too
					# this happens for quick processing of 'tiny' format
					$new_height = min($new_width, $new_height);
					$new_width  = $new_height;
				}

				$crop = (object)[
					'x'			=> (int)min($size[0] - 1, round($size[0] * $top_crop['X'] / 1000000000)),
					'y'			=> (int)min($size[1] - 1, round($size[1] * $top_crop['Y'] / 1000000000)),
					'width'		=> $new_width,
					'height'	=> $new_height,
				];

				$image->cropImage($crop->width, $crop->height, $crop->x, $crop->y);

				if ($crop->width  !== $want_width
				||	$crop->height !== $want_height
				) {
					$image->resizeImage ($want_width, $want_height, Imagick::FILTER_LANCZOS, 1);
					$image->setImagePage($want_width, $want_height, 0, 0);

					$image_width  = $want_width;
					$image_height = $want_height;
				}
			} elseif (
				$want_width  !== $image_width
			||	$want_height !== $image_height
			) {
				$image->resizeImage ($want_width, $want_height, Imagick::FILTER_LANCZOS, 1);
				$image->setImagePage($want_width, $want_height, 0, 0);

				$image_width  = $want_width;
				$image_height = $want_height;
			}
		}

		if ($add_overlay) {
			$space_w = .0065 * max($image_width, $image_height);	# 0.65%
			$space_h = .01   * min($image_height, $image_width);	# 1%

			# offset for logo due to TEXTOVERLAY
			$offset_y = 0;

			$overlay_width  = $overlay ? $overlay->getImageWidth()  : 0;
			$overlay_height = $overlay ? $overlay->getImageHeight() : 0;

			if ($photo['TEXTOVERLAY']) {
				# font_size 2.5% of smallest side
				$font_size = .025 * min($image_height, $image_width);

				$draw = new ImagickDraw();

				$pos_x = $space_w;// * 1.8;
				$pos_y = $space_h;

				static $__gravity = [
					'top_left'		=> Imagick::GRAVITY_NORTHWEST,
					'top_right'		=> Imagick::GRAVITY_NORTHEAST,
					'bottom_left'	=> Imagick::GRAVITY_SOUTHWEST,
					'bottom_right'	=> Imagick::GRAVITY_SOUTHEAST,
				];
				$draw->setGravity($__gravity[$photo['LOGOPOS']]);

				$offset_y = $font_size * 1.4;

				if ($photo['LOGOPOS'] === 'bottom_left'
				||	$photo['LOGOPOS'] === 'bottom_right'
				) {
					$offset_y = -$offset_y;
				}

				$name = $photo['TEXTOVERLAY'];

				$draw->setFont(__DIR__.'/../static_html/components/EurostileLTStd-Bold.otf');
				$draw->setFontSize($font_size);
				$draw->setStrokeAntialias(true);
				$draw->setTextAntialias(true);

				$clone = clone $draw;
				$black_pixel = new ImagickPixel('black');

				$draw->setFillColor($black_pixel);
				$draw->setStrokeColor($black_pixel);
				$draw->setStrokeWidth(1);
				$draw->setStrokeOpacity(.4);
				$draw->setFillOpacity(.4);
				$image->annotateImage($draw, $pos_x, $pos_y, 0, $name);

				$draw = $clone;
				$white_pixel = new ImagickPixel('white');

				$draw->setFillColor($white_pixel);
				$draw->setStrokeWidth(0);
				$image->annotateImage($draw, $pos_x, $pos_y, 0, $name);
			}

			if (Imagick::COLORSPACE_GRAY === $image->getImageColorspace()) {
				$image->setImageColorspace(Imagick::COLORSPACE_SRGB);
			}

			if ($overlay) {
				$overlay_pixels = $overlay_width * $overlay_height;
				  $image_pixels =	$image_width *	 $image_height;

				# must fit at most 100 times in image

				if (($image_pixels / $overlay_pixels) < 100) {
					$new_overlay_pixels = $image_pixels / 100;

					scale_max_pixels($overlay_width, $overlay_height, (int)$new_overlay_pixels);

					$overlay->resizeImage($overlay_width, $overlay_height, Imagick::FILTER_LANCZOS, 1);
				}

				switch ($photo['LOGOPOS']) {
				case 'top_left':
					$pos_x = $space_w;
					$pos_y = $space_h;
					break;
				case 'top_right':
					$pos_x = $image_width - $space_w - $overlay_width;
					$pos_y = $space_h;
					break;
				case 'bottom_left':
					$pos_x = $space_w;
					$pos_y = $image_height - $space_h - $overlay_height;
					break;
				case 'bottom_right':
					$pos_x = $image_width  - $space_w - $overlay_width;
					$pos_y = $image_height - $space_h - $overlay_height;
					break;
				}
				$image->compositeImage(
					$overlay,
					Imagick::COMPOSITE_DISSOLVE,
					(int)$pos_x,
					(int)($pos_y + $offset_y)
				);
			}
		}
		$image->setImageCompressionQuality(93);
		$image->setInterlaceScheme(Imagick::INTERLACE_LINE);
		if ($photoid < 623583) {
			$image->setSamplingFactors([2,2]);
		}
		$image->setImageFormat($filetype);
#		$image->compositeImage(
#			$overlay,
#			imagick::COMPOSITE_DISSOLVE,
#			0,
#			0
#		);
		$data = $image->getImageBlob();
	} catch (Exception $e) {
		mail_log($e->getMessage());
	}
	if (!$data) {
		image_bail(503);
	}
	header('Content-Length: '.strlen($data));
	echo $data;

} else {
	# no overlay
	# no change in size
	# just pass through original
	header('Content-Length: '.filesize($photo_src));
	readfile($photo_src);
	exit;
}

fastcgi_finish_request();

if (!empty($lock_ok)) {
	write_cache( $data, $photo);
}

function try_cache(int $photoid, int $galleryid, string $cache_type, array $photo): bool {
	$cache_path = get_cache_path("/imgcache/$galleryid");
	if (!safe_mkdir($cache_path, 0777, true)) {
		return false;
	}
	global $__mstamp, $__fp, $__cache_name;
	$__cache_name = get_cache_name($photoid, $galleryid, $cache_type, $photo);
	if (!($__fp = dio_open($__cache_name, O_RDWR | O_CREAT, 0644))) {
		chmod($__cache_name, 0644);
	}
	if (!($__fp = dio_open($__cache_name, O_RDWR | O_CREAT, 0644))) {
		error_log($error_message = 'failed to dio_open: '.$__cache_name);
		mail_log($error_message);
		return false;
	}
	if (-1 === ($rc = dio_fcntl($__fp, F_SETLK, [
		'start'		=> 0,
		'length'	=> 0,
		'whence'	=> SEEK_SET,
		'type'		=> F_WRLCK,
	]))) {
		# -1 can indicate failed to lock or general failure
		# warn if we get lots of failures
#		if (memcached_increment($key = 'photo.php:write_lock', 1, 1, ONE_MINUTE) > 10) {
#			memcached_delete($key);
#			mail_log('failed to write lock: '.$__cache_name);
#		}
        error_log('WARNING '.($error = 'dio_fcntl returned '.var_get($rc).', failed to obtain a write lock on '.$__cache_name));
        mail_log($error);
		return false;
	}
	if (isset($_REQUEST['NOMEMCACHE'])
	||	force_refresh()
	||	($stat = dio_stat($__fp))
	&&	(	!$stat['size']
		||	 $stat['mtime'] < $__mstamp
		)
	) {
		return true;
	}
	header('X-Cache-Name: '.$__cache_name);
	header('X-Cache-Stamp: '.$stat['mtime']);
	header('X-Cache: disc');
	header('Content-Length: '.$stat['size']);
	header('X-Sendfile: '.$__cache_name);
	exit;
}

function write_cache($data, $photo): void {
	if (!$data) {
		return;
	}

	global $__fp, $__cache_name;
	dio_write($__fp, $data);
	dio_close($__fp);

	$latitude  = $photo['LATITUDE'];
	$longitude = $photo['LONGITUDE'];
	$stamp	   = $photo['STAMP'];

	if (!$latitude
	&&	!$longitude
	) {
		return;
	}
	/** @noinspection SpellCheckingInspection */
	system('exiftool -q'.	// NOSONAR
		' -overwrite_original'.
		' -exif:gpslatitude='.$latitude.
		' -exif:gpslongitude='.$longitude.
		' -exif:datetimeoriginal="'.gmdate('Y:m:d H:i:s',$stamp).'"'.
		' '.$__cache_name.
		' </dev/null >/dev/null 2>/dev/null &'
	);
}
/** @noinspection SpellCheckingInspection */
function get_cache_name(
	int		$photoid,
	int		$galleryid,
	string	$cache_type,
	array	$photo
): string {
	$file = get_cache_path("/imgcache/$galleryid/{$photoid}_$cache_type");
	if ($photo['EXTERN']) {
		$file .= '_ext';
	}
	if ($photo['LOGOPOS'] !== 'bottom_right') {
		$file .= '_'.$photo['LOGOPOS'];
	}
	if ($photo['TEXTOVERLAY']) {
		$file .= '_'.crc32($photo['TEXTOVERLAY']);
	}
	$file .= '_ov'.$photo['OVERLAYID'];
	if ($_SERVER['eFILETYPE'] !== 'jpg') {
		$file .= '.'.$_SERVER['eFILETYPE'];
	}
	return $file;
}
/** @noinspection SpellCheckingInspection */
function get_etag(array $photo): string {
	$etag = '';
	if ($photo['EXTERN']) {
		$etag .= '_ext';
	}
	if ($photo['LOGOPOS'] !== 'bottom_right') {
		$etag .= '_'.$photo['LOGOPOS'];
	}
	if ($photo['TEXTOVERLAY']) {
		$etag .= '_'.crc32($photo['TEXTOVERLAY']);
	}
	if ($photo['OVERLAYID']) {
		$etag .= '_'.$photo['OVERLAYID'];
	}
	if ($_SERVER['eFILETYPE'] !== 'jpg') {
		$etag .= '.'.$_SERVER['eFILETYPE'];
	}
	return $etag;
}

function check_admin(): bool {
	global $currentuser;
	if (!$currentuser) {
		require_once __DIR__.'/../public_html/_currentuser.inc';
		$currentuser = new _currentuser(TINY_USER | SKIP_SETTINGS);
	}
	return have_admin();
}
