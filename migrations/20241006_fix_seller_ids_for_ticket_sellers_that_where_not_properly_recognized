update presaleinfo set sellerid = 39 where website like '%eventix';
create table eventix_parties select partyid from presaleinfo where sellerid=39;
alter table eventix_parties add primary key (partyid);

update presaleinfo set sellerid = 107 where website like '%howler';
create table howler_parties select partyid from presaleinfo where sellerid=107;
alter table howler_parties add primary key (partyid);

update presaleinfo set sellerid = 13 where website like '%event-tickets%';
create table eventtickets_parties select partyid from presaleinfo where sellerid=13;
alter table eventtickets_parties add primary key (partyid);

update presaleinfo set sellerid = 35 where website like '%cm.com%' or website like '%cmtickets%';
create table cm_parties select partyid from presaleinfo where sellerid=35;
alter table cm_parties add primary key (partyid);

update presaleinfo set sellerid = 45 where website like '%ra.co%' or website like '%residentadvisor%';
create table residentadvisor_parties select partyid from presaleinfo where sellerid=45;
alter table residentadvisor_parties add primary key (partyid);

on ultraparty: (party_db master)
mariadb-dump party_db eventix_parties > eventix_parties.dump.sql
mariadb-dump party_db eventtickets_parties > eventtickets_parties.dump.sql
mariadb-dump party_db cm_parties > cm_parties.dump.sql
mariadb-dump party_db residentadvisor_parties > residentadvisor_parties.dump.sql
mariadb-dump party_db howler_parties > howler_parties.dump.sql

transfer dumps to black

on black: (counter_db master)
mariadb < eventix_parties.dump.sql
mariadb < eventtickets_parties.dump.sql
mariadb < cm_parties.dump.sql
mariadb < residentadvisor_parties.dump.sql
mariadb < howler_parties.dump.sql

update tickethit join eventix_parties using (partyid) set sellerid = 39;
update tickethit join eventtickets_parties using (partyid) set sellerid = 13;
update tickethit join cm_parties using (partyid) set sellerid = 35;
update tickethit join residentadvisor_parties using (partyid) set sellerid = 45;
update tickethit join howler_parties using (partyid) set sellerid = 107;

drop table eventix_parties;
drop table eventtickets_parties;
drop table cm_parties;
drop table residentadvisor_parties;
drop table howler_parties;
