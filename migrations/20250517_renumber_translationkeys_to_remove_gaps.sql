set @cnt = 0;
create table translation_mapping select keyname, keyid, @cnt := @cnt + 1 as new_keyid from translationkey;

insert into translation_mapping select '', translationtext.keyid, @cnt := @cnt + 1 as new_keyid from translationtext left join translationkey using (keyid) where translationkey.keyid is null;

insert ignore into translation_mapping select '', translationtexthistory.keyid, @cnt := @cnt + 1 as new_keyid from translationtexthistory left join translationkey using (keyid) where translationkey.keyid is null;

alter table translation_mapping add primary key (keyid, new_keyid);

create table new_translationchanged like translationchanged;
insert into new_translationchanged select langid, new_keyid, mstamp from translationchanged join translation_mapping using (keyid);
rename table translationchanged to BACKUP_20250517_before_renumber_translationchanged, new_translationchanged to translationchanged;

create table new_translationchecked like translationchecked;
insert into new_translationchecked select langid, new_keyid, checkstamp from translationchecked join translation_mapping using (keyid);
rename table translationchecked to BACKUP_20250517_before_renumber_translationchecked, new_translationchecked to translationchecked;

create table new_translationkey like translationkey;
insert into new_translationkey select translationkey.keyname, new_keyid from translationkey join translation_mapping using (keyid);
rename table translationkey to BACKUP_20250517_before_renumber_translationkey, new_translationkey to translationkey;

create table new_translationmapspec like translationmapspec;
insert into new_translationmapspec select argument, value, new_keyid from translationmapspec join translation_mapping using (keyid);
rename table translationmapspec to BACKUP_20250517_before_renumber_translationmapspec, new_translationmapspec to translationmapspec;

create table new_translationtexthistory like translationtexthistory;
insert into new_translationtexthistory select new_keyid, langid, body, mstamp, muserid from translationtexthistory join translation_mapping using (keyid);
rename table translationtexthistory to BACKUP_20250517_before_renumber_translationtexthistory, new_translationtexthistory to translationtexthistory;

create table new_translationtext like translationtext;
insert into new_translationtext select new_keyid, langid, body from translationtext join translation_mapping using (keyid);
rename table translationtext to BACKUP_20250517_before_renumber_translationtext, new_translationtext to translationtext;

create table new_translationuse like translationuse;
insert into new_translationuse select distinct contextid, new_keyid from translationuse join translation_mapping using (keyid);
rename table translationuse to BACKUP_20250517_before_renumber_translationuse, new_translationuse to translationuse;

rename table translation_mapping to BACKUP_20250517_before_renumber_translation_renumber_mapping;
