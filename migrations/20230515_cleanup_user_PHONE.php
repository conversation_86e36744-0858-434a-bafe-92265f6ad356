#!/usr/bin/php
<?php

define('CURRENTSTAMP',	time());
# define('DRYRUN',	true);

const RESETLINE	= "\n\x1B[A\x1B[K";

require_once '../public_html/_exit_if_readonly.inc';
require_once '../public_html/_db.inc';
require_once '../public_html/_error.inc';
require_once '../public_html/_require.inc';
require_once '../public_html/_phone.inc';

ob_implicit_flush(true);
$rc = main();
exit(show_messages_cli() || !$rc ? 1 : 0);

function main(): bool {
	$last_userid = db_single('user', 'SELECT MAX(USERID) FROM user');
	if (!$last_userid) {
		return $last_userid !== false;
	}
	$userid = 1;
	$step = 1000;
	while ($users = db_rowuse_array('user', '
		SELECT USERID, PHONE
		FROM user
		WHERE PHONE != ""
		  AND USERID BETWEEN '.$userid.' AND '.($userid + $step - 1))
	) {
		$clean = [];
		foreach ($users as $user) {
			echo RESETLINE, "processing user ", $user['USERID'], "... ";

			$source_phone = $user['PHONE'];

			$clean_phone = preg_replace('"[^\d\s\-+]+"', '', $user['PHONE']);
			
			$update_user = ($clean_phone !== $user['PHONE']);
			
			$user['PHONE'] = $clean_phone;

			if (!require_phone_or_empty($user, 'PHONE')) {
				echo "remove phone, clean: ", $clean_phone,' source: ', $source_phone, "\n";
				$clean[] = $user['USERID'];
			
			} elseif ($update_user) {
				$phone_for_sms = clean_phone_for_sms($clean_phone);
				$clean_phone = mytrim($clean_phone);

				echo "updating phone, source: ", $source_phone, ', clean: ', $clean_phone, ", sms: ", $phone_for_sms, "\n";
				
				if (!db_update('user', '
					UPDATE user SET
						PHONE		= "'.addslashes($clean_phone).'",
						CLEANPHONE	= '.$phone_for_sms.'
					WHERE USERID = '.$user['USERID'])
				) {
					return false;
				}
			}
			
		}
		if ($clean) {
			$cleanidstr = implode(', ', $clean);
	
			if (!db_insert('user_log', '
				INSERT INTO user_log
				SELECT * FROM user
				WHERE USERID IN ('.$cleanidstr.')')
			||	!db_update('user', '
				UPDATE user SET
					MUSERID		= 0,
					MSTAMP		= '.CURRENTSTAMP.',
					PHONE		= "",
					CLEANPHONE	= 0
				WHERE USERID IN ('.$cleanidstr.')')
			) {
				return false;
			}
		}
		$userid += $step;
	}
	return $users !== false;
}

