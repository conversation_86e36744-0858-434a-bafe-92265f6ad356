#!/usr/bin/php
<?php

declare(strict_types=1);

require_once __DIR__.'/../public_html/_exit_if_readonly.inc';
require_once __DIR__.'/../public_html/_run_and_exit.inc';
require_once __DIR__.'/../public_html/_db.inc';
require_once __DIR__.'/../public_html/_servertype.inc';

# NOTE: Execute this on webstore

const PFCACHE_UPLOAD_IMAGES = SERVER_SANDBOX ? '/tmpdisk/uploadimage' : '/mnt/largepart/pfcachev2/uploadimage';
const DRY_RUN = true;

run_and_exit(syslog: true);

function main(): bool {
	if (!($dir = opendir($path = PFCACHE_UPLOAD_IMAGES))) {
		syslog_last_error(LOG_ERR, "failed to opendir $path");
		return false;
	}
	while (false !== ($upimgid = readdir($dir))) {
		if (!$upimgid
		||	 $upimgid[0] === '.'
		) {
			continue;
		}
		if (!($upimgdir = opendir($upimgid_path = $path.'/'.$upimgid))) {
			syslog_last_error(LOG_ERR, "failed to opendir $upimgid");
			return false;
		}
		while (false !== ($item = readdir($upimgdir))) {
			if (!$item
			||	 $item[0] === '.'
			) {
				continue;
			}
			if (preg_match('"\.[a-z]+$"', $item)) {
				syslog(LOG_DEBUG, "skipping $upimgid_path/$item");
				continue;
			}
			if (!($filetype = db_single('uploadimage', "SELECT FILETYPE FROM uploadimage WHERE UPIMGID = $upimgid"))) {
				if ($filetype !== false) {
					syslog_error(LOG_ERR, "failed to fetch filetype of uploadimage $upimgid");
				}
				return false;
			}
			if (!in_array($item, ['letterbox', 'pillarbox', 'manualcrop', 'smartcrop'], true)) {
				syslog(LOG_INFO, "link $upimgid_path/$item $upimgid_path/$item.$filetype");
				if (!DRY_RUN
				&&	!link("$upimgid_path/$item", "$upimgid_path/$item.$filetype")
				) {
					syslog_last_error(LOG_ERR, "link $upimgid_path/$item $upimgid_path/$item.$filetype failed");
				 	return false;
				}
				continue;
			}
			if (!($subdir = opendir($subdir_path = $upimgid_path.'/'.$item))) {
				syslog_last_error(LOG_ERR, "failed to opendir $subdir_path");
				return false;
			}
			while (false !== ($sub_item = readdir($subdir))) {
				if (!$sub_item
				||	 $sub_item[0] === '.'
				) {
					continue;
				}
				syslog(LOG_INFO, "link $subdir_path/$sub_item $upimgid_path/{$item}_$sub_item");
				if (!DRY_RUN
				&&	!link("$subdir_path/$sub_item", "$upimgid_path/{$item}_$sub_item")
				) {
					syslog_last_error(LOG_ERR, "link $subdir_path/$sub_item $upimgid_path/{$item}_$sub_item failed");
				 	return false;
				}
			}
			closedir($subdir);
		}
		closedir($upimgdir);
	}
	closedir($dir);
	return true;
}
